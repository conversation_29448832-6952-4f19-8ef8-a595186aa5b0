#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Nadzor
#XTXT: Type name for spaces in browser tab page title
space=Prostor
#_____________________________________
#XFLD: Spaces label in
spaces=Prostori
#XFLD: Manage plan button text
manageQuotaButtonText=Upravljaj planom
#XBUT: Manage resources button
manageResourcesButton=Upravljaj resursima
#XFLD: Create space button tooltip
createSpace=Stvaranje prostora
#XFLD: Create
create=Stvori
#XFLD: Deploy
deploy=Uvedi
#XFLD: Page
page=Stranica
#XFLD: Cancel
cancel=Odustani
#XFLD: Update
update=Ažuriraj
#XFLD: Save
save=Spremi
#XFLD: OK
ok=OK
#XFLD: days
days=Dani
#XFLD: Space tile edit button label
edit=Uredi
#XFLD: Auto Assign all objects to space
autoAssign=Automatski dodijeli
#XFLD: Space tile open monitoring button label
openMonitoring=Nadzor
#XFLD: Delete
delete=Izbriši
#XFLD: Copy Space
copy=Kopiraj
#XFLD: Close
close=Zatvori
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Aktivno
#XFLD: Space status locked
lockedLabel=Zaključano
#XFLD: Space status critical
criticalLabel=Kritično
#XFLD: Space status cold
coldLabel=Hladno
#XFLD: Space status deleted
deletedLabel=Izbrisano
#XFLD: Space status unknown
unknownLabel=Nepoznato
#XFLD: Space status ok
okLabel=U dobrom stanju
#XFLD: Database user expired
expired=Isteklo
#XFLD: deployed
deployed=Uvedeno
#XFLD: not deployed
notDeployed=Nije uvedeno
#XFLD: changes to deploy
changesToDeploy=Promjene za uvođenje
#XFLD: pending
pending=Uvođenje
#XFLD: designtime error
designtimeError=Pogreška vremena dizajna
#XFLD: runtime error
runtimeError=Pogreška vremena izvođenja
#XFLD: Space created by label
createdBy=Stvorio
#XFLD: Space created on label
createdOn=Stvoreno
#XFLD: Space deployed on label
deployedOn=Uvedeno
#XFLD: Space ID label
spaceID=ID prostora
#XFLD: Priority label
priority=Prioritet
#XFLD: Space Priority label
spacePriority=Prioritet prostora
#XFLD: Space Configuration label
spaceConfiguration=Konfiguracija prostora
#XFLD: Not available
notAvailable=Nije dostupno
#XFLD: WorkloadType default
default=Zadano
#XFLD: WorkloadType custom
custom=Prilagođeno
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Pristup jezeru podataka
#XFLD: Translation label
translationLabel=Prijevod
#XFLD: Source language label
sourceLanguageLabel=Izvorni jezik
#XFLD: Translation CheckBox label
translationCheckBox=Omogući prijevod
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Uvedite prostor za pristup pojedinostima korisnika.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Uvedite prostor za otvaranje Explorera baze podataka.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Ne možete se koristiti ovim prostorom za pristupanje jezeru podataka jer ga već upotrebljava drugi prostor.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Upotrijebite ovaj prostor za pristup jezeru podataka.
#XFLD: Space Priority minimum label extension
low=Nisko
#XFLD: Space Priority maximum label extension
high=Visoko
#XFLD: Space name label
spaceName=Naziv prostora
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Uvedi objekte
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Kopiranje {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(nije odabrano)
#XTXT Human readable text for language code "af"
af=afrikaans
#XTXT Human readable text for language code "ar"
ar=arapski
#XTXT Human readable text for language code "bg"
bg=bugarski
#XTXT Human readable text for language code "ca"
ca=katalonski
#XTXT Human readable text for language code "zh"
zh=pojednostavljeni kineski
#XTXT Human readable text for language code "zf"
zf=kineski
#XTXT Human readable text for language code "hr"
hr=hrvatski
#XTXT Human readable text for language code "cs"
cs=češki
#XTXT Human readable text for language code "cy"
cy=velški
#XTXT Human readable text for language code "da"
da=danski
#XTXT Human readable text for language code "nl"
nl=nizozemski
#XTXT Human readable text for language code "en-UK"
en-UK=engleski (Ujedinjena Kraljevina)
#XTXT Human readable text for language code "en"
en=engleski (Sjedinjene Države)
#XTXT Human readable text for language code "et"
et=estonski
#XTXT Human readable text for language code "fa"
fa=perzijski
#XTXT Human readable text for language code "fi"
fi=finski
#XTXT Human readable text for language code "fr-CA"
fr-CA=francuski (Kanada)
#XTXT Human readable text for language code "fr"
fr=francuski
#XTXT Human readable text for language code "de"
de=njemački
#XTXT Human readable text for language code "el"
el=grčki
#XTXT Human readable text for language code "he"
he=hebrejski
#XTXT Human readable text for language code "hi"
hi=hindski
#XTXT Human readable text for language code "hu"
hu=mađarski
#XTXT Human readable text for language code "is"
is=islandski
#XTXT Human readable text for language code "id"
id=indonezijski
#XTXT Human readable text for language code "it"
it=talijanski
#XTXT Human readable text for language code "ja"
ja=japanski
#XTXT Human readable text for language code "kk"
kk=kazaški
#XTXT Human readable text for language code "ko"
ko=korejski
#XTXT Human readable text for language code "lv"
lv=latvijski
#XTXT Human readable text for language code "lt"
lt=litavski
#XTXT Human readable text for language code "ms"
ms=bahasa melayu
#XTXT Human readable text for language code "no"
no=norveški
#XTXT Human readable text for language code "pl"
pl=poljski
#XTXT Human readable text for language code "pt"
pt=portugalski (Brazil)
#XTXT Human readable text for language code "pt-PT"
pt-PT=portugalski (Portugal)
#XTXT Human readable text for language code "ro"
ro=rumunjski
#XTXT Human readable text for language code "ru"
ru=ruski
#XTXT Human readable text for language code "sr"
sr=srpski
#XTXT Human readable text for language code "sh"
sh=srpsko-hrvatski
#XTXT Human readable text for language code "sk"
sk=slovački
#XTXT Human readable text for language code "sl"
sl=slovenski
#XTXT Human readable text for language code "es"
es=španjolski
#XTXT Human readable text for language code "es-MX"
es-MX=španjolski (Meksiko)
#XTXT Human readable text for language code "sv"
sv=švedski
#XTXT Human readable text for language code "th"
th=tajski
#XTXT Human readable text for language code "tr"
tr=turski
#XTXT Human readable text for language code "uk"
uk=ukrajinski
#XTXT Human readable text for language code "vi"
vi=vijetnamski
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Izbriši prostore
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Želite li zaista premjestiti prostor "{0}" u koš za smeće?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Želite li zaista premjestiti ovoliko odabranih prostora: {0} u koš za smeće?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Želite li zaista izbrisati prostor "{0}"? Ovu radnju nije moguće poništiti.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Želite li zaista izbrisati ovoliko odabranih prostora: {0}? Ovu radnju nije moguće poništiti. Sljedeći sadržaj bit će {1} izbrisan:
#XTXT: permanently
permanently=trajno
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Sljedeći sadržaj bit će {0} izbrisan i ne može se obnoviti.
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Upišite {0} da potvrdite brisanje.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Provjerite pravopis i pokušajte ponovo.
#XTXT: All Spaces
allSpaces=Svi prostori
#XTXT: All data
allData=Svi objekti i podaci sadržani u prostoru
#XTXT: All connections
allConnections=Sve veze definirane u prostoru
#XFLD: Space tile selection box tooltip
clickToSelect=Pritisnite za odabir
#XTXT: All database users
allDatabaseUsers=Svi objekti i podaci sadržani u bilo kojoj shemi Open SQL povezanoj s prostorom
#XFLD: remove members button tooltip
deleteUsers=Ukloni članove
#XTXT: Space long description text
description=Opis (maksimalno 4000 znakova)
#XFLD: Add Members button tooltip
addUsers=Dodaj članove
#XFLD: Add Users button tooltip
addUsersTooltip=Dodavanje korisnika
#XFLD: Edit Users button tooltip
editUsersTooltip=Uređivanje korisnika
#XFLD: Remove Users button tooltip
removeUsersTooltip=Uklanjanje korisnika
#XFLD: Searchfield placeholder
filter=Pretraži
#XCOL: Users table-view column health
health=Stanje
#XCOL: Users table-view column access
access=Pristup
#XFLD: No user found nodatatext
noDataText=Korisnik nije pronađen
#XTIT: Members dialog title
selectUserDialogTitle=Dodavanje članova
#XTIT: User dialog title
addUserDialogTitle=Dodavanje korisnika
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Brisanje veza
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Brisanje veze
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Želite li izbrisati odabrane veze? Bit će trajno uklonjene.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Odabir veza
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Dijeljenje veze
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Dijeljene veze
#XFLD: Add remote source button tooltip
addRemoteConnections=Dodavanje veza
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Uklanjanje veza
#XFLD: Share remote source button tooltip
shareConnections=Dijeljenje veza
#XFLD: Tile-layout tooltip
tileLayout=Raspored pločica
#XFLD: Table-layout tooltip
tableLayout=Raspored tablica
#XMSG: Success message after creating space
createSpaceSuccessMessage=Prostor stvoren
#XMSG: Success message after copying space
copySpaceSuccessMessage=Kopiranje prostora "{0}" u prostor "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Uvođenje prostora pokrenuto
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Ažuriranje Apache Sparka pokrenuto
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Ažuriranje Apache Sparka nije uspjelo
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Pojedinosti prostora ažurirane
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Prostor privremeno otključan
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Prostor izbrisan
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Prostori izbrisani
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Prostor vraćen
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Prostori vraćeni
#YMSE: Error while updating settings
updateSettingsFailureMessage=Postavke prostora nije moguće ažurirati.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Jezero podataka već je dodijeljeno drugom prostoru. Samo jedan prostor istovremeno može pristupiti jezeru podataka.
#YMSE: Error while updating data lake option
virtualTablesExists=Ne možete poništiti dodjelu jezera podataka ovom prostoru jer još postoje zavisnosti o virtualnim tablicama*. Izbrišite virtualne tablice kako biste poništili dodjelu jezera podataka ovom prostoru.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Prostor nije moguće otključati.
#YMSE: Error while creating space
createSpaceError=Prostor nije moguće stvoriti.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Prostor s nazivom {0} već postoji.
#YMSE: Error while deleting a single space
deleteSpaceError=Prostor nije moguće izbrisati.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Vaš prostor “{0}” više ne radi ispravno. Pokušajte ga ponovo izbrisati. Ako ne proradi, obratite se administratoru radi brisanja prostora ili otvorite ticket.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Podatke prostora u datotekama nije moguće izbrisati.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Korisnike nije moguće ukloniti.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Sheme nije moguće ukloniti.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Veze nije moguće ukloniti.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Podatke prostora nije moguće izbrisati.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Prostore nije moguće izbrisati.
#YMSE: Error while restoring a single space
restoreSpaceError=Prostor nije moguće vratiti.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Prostore nije moguće vratiti.
#YMSE: Error while creating users
createUsersError=Korisnike nije moguće dodati.
#YMSE: Error while removing users
removeUsersError=Ne možemo ukloniti korisnike.
#YMSE: Error while removing user
removeUserError=Nije moguće ukloniti korisnika.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Nije moguće dodati korisnika u odabranu ulogu u opsegu. \n\nNe možete dodati ni sebe u ulogu u opsegu. Možete zamoliti svog administratora da vas doda.
#YMSE: Error assigning user to the space
userAssignError=Ne možemo dodijeliti korisnika prostoru. \n\n Korisnik je već dodijeljen maksimalnom dopuštenom broju (100) prostora za sve uloge u opsegu.
#YMSE: Error assigning users to the space
usersAssignError=Ne možemo dodijeliti korisnika prostoru. \n\n Korisnik je već dodijeljen maksimalnom dopuštenom broju (100) prostora za sve uloge u opsegu.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Ne možemo dohvatiti korisnike. Pokušajte ponovo poslije.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Ne možemo dohvatiti uloge koje se nalaze u opsegu.
#YMSE: Error while fetching members
fetchUserError=Nije moguće dohvatiti članove. Pokušajte ponovo poslije.
#YMSE: Error while loading run-time database
loadRuntimeError=Ne možemo učitati informacije iz baze podataka vremena izvođenja.
#YMSE: Error while loading spaces
loadSpacesError=Nažalost, došlo je do problema pri pokušaju dohvaćanja vaših prostora.
#YMSE: Error while loading haas resources
loadStorageError=Nažalost, došlo je od problema pri pokušaju dohvaćanja podataka pohrane.
#YMSE: Error no data could be loaded
loadDataError=Nažalost, došlo je do problema pri pokušaju dohvaćanja vaših podataka.
#XFLD: Click to refresh storage data
clickToRefresh=Kliknite ovdje kako biste pokušali ponovo.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Brisanje prostora
#XCOL: Spaces table-view column name
name=Naziv
#XCOL: Spaces table-view deployment status
deploymentStatus=Status uvođenja
#XFLD: Disk label in space details
storageLabel=Disk (GB)
#XFLD: In-Memory label in space details
ramLabel=Memorija (GB)
#XFLD: Memory label on space card
memory=Memorija za pohranu
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Prostor za pohranu
#XFLD: Storage Type label in space details
storageTypeLabel=Tip pohrane
#XFLD: Enable Space Quota
enableSpaceQuota=Omogućivanje kvote prostora
#XFLD: No Space Quota
noSpaceQuota=Nema kvote prostora
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA baza podataka (disk i In-Memory)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA datoteke jezera podataka
#XFLD: Available scoped roles label
availableRoles=Dostupne uloge u opsegu
#XFLD: Selected scoped roles label
selectedRoles=Odabrane uloge u opsegu
#XCOL: Spaces table-view column models
models=Modeli
#XCOL: Spaces table-view column users
users=Korisnici
#XCOL: Spaces table-view column connections
connections=Veze
#XFLD: Section header overview in space detail
overview=Pregled
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Aplikacije
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Dodjela zadatka
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU-ovi
#XFLD: Memory label in Apache Spark section
memoryLabel=Memorija (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Konfiguracija prostora
#XFLD: Space Source label
sparkApplicationLabel=Aplikacija
#XFLD: Cluster Size label
clusterSizeLabel=Veličina klastera
#XFLD: Driver label
driverLabel=Pokretač
#XFLD: Executor label
executorLabel=Izvršitelj
#XFLD: max label
maxLabel=Maksimalno upotrijebljeno
#XFLD: TrF Default label
trFDefaultLabel=Zadan tok transformacije
#XFLD: Merge Default label
mergeDefaultLabel=Zadano spajanje
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimiziraj zadano
#XFLD: Deployment Default label
deploymentDefaultLabel=Uvođenje lokalne tablice (datoteka)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Tip objekta
#XFLD: Task activity label
taskActivityLabel=Aktivnost
#XFLD: Task Application ID label
taskApplicationIDLabel=Zadana aplikacija
#XFLD: Section header in space detail
generalSettings=Opće postavke
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Sustav je trenutačno zaključao ovaj prostor.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Promjene u ovom odjeljku bit će uvedene odmah.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Uzmite u obzir da promjena tih vrijednosti može uzrokovati probleme s izvedbom.
#XFLD: Button text to unlock the space again
unlockSpace=Otključaj prostor
#XFLD: Info text for audit log formatted message
auditLogText=Omogućite zapisnike revizije kako bi se zapisivale radnje čitanja ili promjene (pravilnici o reviziji). Administratori tada mogu analizirati tko je izveo koju radnju u kojem trenutku.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Zapisnici revizije mogu trošiti veliki iznos pohrane na disku u vašem zakupcu. Ako omogućite pravilnik o reviziji (radnje čitanja ili promjene), trebate redovito nadzirati iskorištenost pohrane na disku (preko Iskorištena pohrana na disku u Nadzoru sustava) kako biste izbjegli prekide rada zbog punog diska, što može dovesti do poremećaja usluge. Ako onemogućite pravilnik o reviziji, svi unosi njegova zapisnika revizije bit će izbrisani. Ako želite zadržati te unose zapisnika revizije, razmislite o tome da ih izvezete prije nego što onemogućite taj pravilnik o reviziji. 
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Pokaži pomoć
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Ovaj prostor prekoračuje svoj prostor za pohranu i zaključat će se za {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=h
#XMSG: Unit for remaining time until space is locked again
minutes=min
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Revizija
#XFLD: Subsection header in space detail for auditing
auditing=Postavke revizije prostora
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kritični prostori: iskorištena je pohrana veća od 90 %.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Prostori u dobrom stanju: iskorištena je pohrana između 6 % i 90 %.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Plavi prostori: iskorištena je pohrana 5 % ili manja.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kritični prostori: iskorištena je pohrana veća od 90 %.
#XFLD: Green space tooltip
okSpaceCountTooltip=Prostori u dobrom stanju: iskorištena je pohrana između 6 % i 90 %.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Zaključani prostori: blokirano zbog nedovoljne memorije.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Zaključani prostori
#YMSE: Error while deleting remote source
deleteRemoteError=Veze nije moguće ukloniti.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=ID prostora poslije nije moguće promijeniti.\nValjani su znakovi A - Z, 0 - 9, i _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Unesite naziv prostora.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Unesite poslovni naziv.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Unesite ID prostora.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Nevaljani znakovi. Upotrijebite samo A - Z, 0 - 9 i _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ID prostora već postoji.
#XFLD: Space searchfield placeholder
search=Pretraži
#XMSG: Success message after creating users
createUsersSuccess=Korisnici dodani
#XMSG: Success message after creating user
createUserSuccess=Korisnik dodan
#XMSG: Success message after updating users
updateUsersSuccess=Korisnici ažurirani ({0})
#XMSG: Success message after updating user
updateUserSuccess=Korisnik ažuriran
#XMSG: Success message after removing users
removeUsersSuccess=Korisnici uklonjeni ({0})
#XMSG: Success message after removing user
removeUserSuccess=Korisnik uklonjen
#XFLD: Schema name
schemaName=Naziv sheme
#XFLD: used of total
ofTemplate={0} od {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Dodijeljeni disk ({0} od {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Dodijeljena memorija ({0} od {1})
#XFLD: Storage ratio on space
accelearationRAM=Ubrzanje memorije
#XFLD: No Storage Consumption
noStorageConsumptionText=Kvota pohrane nije dodijeljena.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disk iskorišten za pohranu ({0} od {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Memorija iskorištena za pohranu ({0} od {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} od {1} diska iskorišteno za pohranu
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} od {1} memorije iskorišteno
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} od {1} diska dodijeljeno
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} od {1} memorije dodijeljeno
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Podaci prostora: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Ostali podaci: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Razmotrite proširenje plana ili se obratite SAP podršci.
#XCOL: Space table-view column used Disk
usedStorage=Disk iskorišten za pohranu
#XCOL: Space monitor column used Memory
usedRAM=Memorija iskorištena za pohranu
#XCOL: Space monitor column Schema
tableSchema=Shema
#XCOL: Space monitor column Storage Type
tableStorageType=Tip pohrane
#XCOL: Space monitor column Table Type
tableType=Tip tablice
#XCOL: Space monitor column Record Count
tableRecordCount=Broj zapisa
#XFLD: Assigned Disk
assignedStorage=Disk dodijeljen za pohranu
#XFLD: Assigned Memory
assignedRAM=Memorija dodijeljena za pohranu
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Iskorištena pohrana
#XFLD: space status
spaceStatus=Status prostora
#XFLD: space type
spaceType=Tip prostora
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Most SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Proizvod dobavljača podataka
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Ne možete izbrisati prostor {0} jer ima tip prostora {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Ne možete izbrisati odabrane prostore ({0}). Prostori sa sljedećim tipovima prostora ne mogu se izbrisati: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Nadzor
#XFLD: Tooltip for edit space button
editSpace=Uređivanje prostora
#XMSG: Deletion warning in messagebox
deleteConfirmation=Želite li zaista izbrisati ovaj prostor?
#XFLD: Tooltip for delete space button
deleteSpace=Brisanje prostora
#XFLD: storage
storage=Disk za pohranu
#XFLD: username
userName=Korisničko ime
#XFLD: port
port=Priključak
#XFLD: hostname
hostName=Naziv glavnog računala
#XFLD: password
password=Lozinka
#XBUT: Request new password button
requestPassword=Zatraži novu lozinku
#YEXP: Usage explanation in time data section
timeDataSectionHint=Stvorite vremenske tablice i dimenzije za upotrebu u modelima i pričama.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Želite li da podaci u vašem prostoru budu dostupni za potrošnju drugim alatima i aplikacijama? Ako je tako, stvorite jednog ili više korisnika koji mogu pristupiti podacima u vašem prostoru i odaberite želite li da svi budući podaci prostora budu dostupni za potrošnju prema zadanim postavkama.
#XTIT: Create schema popup title
createSchemaDialogTitle=Stvaranje sheme Open SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Stvaranje vremenskih tablica i dimenzija
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Uređivanje vremenskih tablica i dimenzija
#XTIT: Time Data token title
timeDataTokenTitle=Vremenski podaci
#XTIT: Time Data token title
timeDataUpdateViews=Ažuriranje prikaza vremenskih podataka
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Stvaranje u tijeku...
#XFLD: Time Data token creation error label
timeDataCreationError=Stvaranje nije uspjelo. Pokušajte ponovo.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Postavke vremenske tablice
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Tablice preračunavanja
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Vremenske dimenzije
#XFLD: Time Data dialog time range label
timeRangeHint=Definirajte vremenski raspon.
#XFLD: Time Data dialog time data table label
timeDataHint=Imenujte tablicu.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Imenujte dimenziju.
#XFLD: Time Data Time range description label
timerangeLabel=Vremenski raspon
#XFLD: Time Data dialog from year label
fromYearLabel=Od godine
#XFLD: Time Data dialog to year label
toYearLabel=Do godine
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Tip kalendara
#XFLD: Time Data dialog granularity label
granularityLabel=Granularnost
#XFLD: Time Data dialog technical name label
technicalNameLabel=Tehnički naziv
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Tablica preračunavanja za tromjesečja
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Tablica preračunavanja za mjesece
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Tablica preračunavanja za dane
#XFLD: Time Data dialog year label
yearLabel=Dimenzija godine
#XFLD: Time Data dialog quarter label
quarterLabel=Dimenzija tromjesečja
#XFLD: Time Data dialog month label
monthLabel=Dimenzija mjeseca
#XFLD: Time Data dialog day label
dayLabel=Dimenzija dana
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregorijanski
#XFLD: Time Data dialog time granularity day label
day=Dan
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Dosegnuta je maksimalna dužina od 1000 znakova.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Maksimalni je vremenski raspon 150 godina.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=“Od godine” treba biti prije “Do godine”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Od godine" treba biti 1900. ili više.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=“Do godine” treba biti poslije “Od godine”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=“Do godine” mora biti niže od tekuće godine plus 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Povećanje "Od godine" može dovesti do gubitka podataka
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Smanjenje "Do godine" može dovesti do gubitka podataka
#XMSG: Time Data creation validation error message
timeDataValidationError=Čini se da su neka polja nevaljana. Provjerite obvezna polja kako biste nastavili.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Želite li zaista izbrisati podatke?
#XMSG: Time Data creation success message
createTimeDataSuccess=Vremenski podaci stvoreni
#XMSG: Time Data update success message
updateTimeDataSuccess=Vremenski podaci ažurirani
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Vremenski podaci izbrisani
#XMSG: Time Data creation error message
createTimeDataError=Došlo je do problema pri stvaranju vremenskih podataka.
#XMSG: Time Data update error message
updateTimeDataError=Došlo je do problema pri pokušaju ažuriranja vremenskih podataka.
#XMSG: Time Data creation error message
deleteTimeDataError=Došlo je do problema pri pokušaju brisanja vremenskih podataka.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Vremenske podatke nije moguće učitati.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Upozorenje
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Ne možemo izbrisati vaše vremenske podatke jer se upotrebljavaju u drugim modelima.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Ne možemo izbrisati vaše vremenske podatke jer se upotrebljavaju u drugom modelu.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Ovo je polje obvezno i ne smije ostati prazno.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Otvaranje u Exploreru baze podataka
#YMSE: Dimension Year
dimensionYearView=Dimenzija "Godina"
#YMSE: Dimension Year
dimensionQuarterView=Dimenzija "Tromjesečje"
#YMSE: Dimension Year
dimensionMonthView=Dimenzija "Mjesec"
#YMSE: Dimension Year
dimensionDayView=Dimenzija "Dan"
#XFLD: Time Data deletion object title
timeDataUsedIn=(upotrebljava se u {0} modela)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(upotrebljava se u 1 modelu)
#XFLD: Time Data deletion table column provider
provider=Dobavljač
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Zavisnosti
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Stvaranje korisnika za shemu prostora
#XFLD: Create schema button
createSchemaButton=Stvori shemu Open SQL
#XFLD: Generate TimeData button
generateTimeDataButton=Stvori vremenske tablice i dimenzije
#XFLD: Show dependencies button
showDependenciesButton=Pokaži zavisnosti
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Za izvođenje ove operacije vaš korisnik mora biti član prostora.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Stvori korisnika sheme prostora
#YMSE: API Schema users load error
loadSchemaUsersError=Popis korisnika nije moguće učitati.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Pojedinosti korisnika sheme prostora
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Želite li zaista izbrisati odabranog korisnika?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Korisnik izbrisan.
#YMSE: API Schema user deletion error
userDeleteError=Korisnika nije moguće izbrisati.
#XFLD: User deleted
userDeleted=Korisnik je izbrisan.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Upozorenje
#XMSG: Remove user popup text
removeUserConfirmation=Želite li zaista ukloniti korisnika? Korisnik i njegove dodijeljene uloge u opsegu uklonit će se iz prostora.
#XMSG: Remove users popup text
removeUsersConfirmation=Želite li zaista ukloniti korisnike? Korisnici i njihove dodijeljene uloge u opsegu uklonit će se iz prostora.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Ukloni
#YMSE: No data text for available roles
noDataAvailableRoles=Prostor se ne dodaje nijednoj ulozi u opsegu. \n Da biste mogli dodavati korisnike u prostor, prvo ga morate dodati jednoj ili više uloga u opsegu.
#YMSE: No data text for selected roles
noDataSelectedRoles=Nema odabranih uloga u opsegu
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Pojedinosti konfiguracije sheme Open SQL
#XFLD: Label for Read Audit Log
auditLogRead=Omogući zapisnik revizije za operacije čitanja
#XFLD: Label for Change Audit Log
auditLogChange=Omogući zapisnik revizije za operacije promjene
#XFLD: Label Audit Log Retention
auditLogRetention=Zadrži zapisnike
#XFLD: Label Audit Log Retention Unit
retentionUnit=dana
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Unesite cijeli broj između {0} i {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Troši podatke sheme prostora
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Prestanak potrošnje podataka sheme prostora
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Ova shema Open SQL može trošiti podatke vaše sheme prostora. Ako prestanete s trošenjem, modeli zasnovani na podacima te sheme prostora možda neće više raditi.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Prestani trošiti
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Ovaj se prostor upotrebljava za pristup jezeru podataka
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Jezero podataka omogućeno
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Ograničenje memorije dosegnuto
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Ograničenje pohrane dosegnuto
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Minimalno ograničenje pohrane dosegnuto
#XFLD: Space ram tag
ramLimitReachedLabel=Ograničenje memorije dosegnuto
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Minimalno ograničenje memorije dosegnuto
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Dosegnuli ste ograničenje dodijeljene pohrane prostora od {0}. Dodijelite više pohrane prostoru.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Ograničenje pohrane sustava dosegnuto
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Dosegnuli ste ograničenje pohrane sustava od {0}. Trenutačno ne možete dodijeliti više pohrane prostoru.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Brisanjem ove sheme Open SQL trajno će se izbrisati svi pohranjeni objekti i održavane asocijacije u toj shemi. Nastaviti?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Shema izbrisana
#YMSE: Error while deleting schema.
schemaDeleteError=Shemu nije moguće izbrisati.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Shema ažurirana
#YMSE: Error while updating schema.
schemaUpdateError=Shemu nije moguće ažurirati.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Naveli smo lozinku za ovu shemu. Ako ste zaboravili ili izgubili svoju lozinku, možete zatražiti novu. Ne zaboravite kopirati ili spremiti novu lozinku.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Kopirajte svoju lozinku. Trebat će vam radi postavljanja veze s ovom shemom. Ako ste zaboravili svoju lozinku, možete otvoriti ovaj dijalog i ponovo je postaviti.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Nemoguće je mijenjati naziv nakon stvaranja sheme.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Prostor
#XFLD: HDI Container section header
HDIContainers=Spremnici HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Dodavanje spremnika HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Uklanjanje spremnika HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Omogući pristup
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Nisu dodani spremnici HDI.
#YMSE: No data text for Timedata section
noDataTimedata=Nisu stvorene vremenske tablice ni dimenzije.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Nije moguće učitati vremenske tablice ni dimenzije jer baza podataka vremena izvođenja nije dostupna.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Nije moguće učitati spremnike HDI jer baza podataka vremena izvođenja nije dostupna.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Ne može se doći do spremnika HDI. Pokušajte ponovo poslije.
#XFLD Table column header for HDI Container names
HDIContainerName=Naziv spremnika HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Omogućivanje pristupa
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Možete omogućiti SAP SQL Data Warehousing u svom zakupcu SAP Datasphera radi razmjene podataka između vaših spremnika HDI i vaših prostora SAP Datasphere bez potrebe za kretanjem podataka.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Kako biste to učinili, otvorite ticket za podršku klikom na gumb u nastavku.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Kad se ticket obradi, morate izraditi jedan ili više novih spremnika HDI u bazi podataka vremena izvođenja SAP Datasphere. Zatim će se gumb Omogući pristup zamijeniti gumbom + u odjeljku spremnika HDI za sve vaše prostore SAP Datasphere i moći ćete dodati spremnike prostoru.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Trebate više informacija? Idite na %%0. Za detaljne informacije o tome što treba uključiti u ticket, pogledajte %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP bilješka 3057059
#XBUT: Open Ticket Button Text
openTicket=Otvori ticket
#XBUT: Add Button Text
add=Dodaj
#XBUT: Next Button Text
next=Sljedeće
#XBUT: Edit Button Text
editUsers=Uredi
#XBUT: create user Button Text
createUser=Stvori
#XBUT: Update user Button Text
updateUser=Odaberi
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Dodavanje nedodijeljenih spremnika HDI
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Nismo pronašli nedodijeljene spremnike. \n Spremnik koji tražite možda je već dodijeljen prostoru.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Dodijeljene spremnike HDI nije moguće učitati.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Spremnike HDI nije moguće učitati.
#XMSG: Success message
succeededToAddHDIContainer=Spremnik HDI dodan
#XMSG: Success message
succeededToAddHDIContainerPlural=Spremnici HDI dodani
#XMSG: Success message
succeededToDeleteHDIContainer=Spremnik HDI uklonjen
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Spremnici HDI uklonjeni
#XFLD: Time data section sub headline
timeDataSection=Vremenske tablice i dimenzije
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Čitanje
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Promjena
#XFLD: Remote sources section sub headline
allconnections=Dodjela veza
#XFLD: Remote sources section sub headline
localconnections=Lokalne veze
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Dodjela članova
#XFLD: User assignment section sub headline
userAssignment=Dodjela korisnika
#XFLD: User section Access dropdown Member
member=Član
#XFLD: User assignment section column name
user=Korisničko ime
#XTXT: Selected role count
selectedRoleToolbarText=Odabrano: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Veze
#XTIT: Space detail section data access title
detailsSectionDataAccess=Pristup shemi
#XTIT: Space detail section time data title
detailsSectionGenerateData=Vremenski podaci
#XTIT: Space detail section members title
detailsSectionUsers=Članovi
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Korisnici
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Pohrana puna
#XTIT: Storage distribution
storageDistributionPopoverTitle=Iskorištena pohrana na disku
#XTXT: Out of Storage popover text
insufficientStorageText=Za stvaranje novog prostora smanjite dodijeljenu pohranu drugog prostora ili izbrišite prostor koji više ne trebate. Možete povećati ukupnu pohranu sustava pozivom upravljanja planom.
#XMSG: Space id length warning
spaceIdLengthWarning=Maksimum od {0} znakova prekoračen.
#XMSG: Space name length warning
spaceNameLengthWarning=Maksimum od {0} znakova prekoračen.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Nemojte upotrebljavati prefiks {0} kako biste izbjegli moguće sukobe.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Sheme Open SQL nije moguće učitati.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Shemu Open SQL nije moguće stvoriti.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Nije moguće učitati sve udaljene veze.
#YMSE: Error while loading space details
loadSpaceDetailsError=Pojedinosti prostora nije moguće učitati.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Prostor nije moguće uvesti.
#YMSE: Error while copying space details
copySpaceDetailsError=Prostor nije moguće kopirati.
#YMSE: Error while loading storage data
loadStorageDataError=Podatke pohrane nije moguće učitati.
#YMSE: Error while loading all users
loadAllUsersError=Nije moguće učitati sve korisnike.
#YMSE: Failed to reset password
resetPasswordError=Lozinku nije moguće ponovo postaviti.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Nova je lozinka postavljena za shemu
#YMSE: DP Agent-name too long
DBAgentNameError=Naziv agenta DP predug.
#YMSE: Schema-name not valid.
schemaNameError=Naziv sheme nije valjan.
#YMSE: User name not valid.
UserNameError=Korisničko ime nije valjano.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Potrošnja po tipu pohrane
#XTIT: Consumption by Schema
consumptionSchemaText=Potrošnja po shemi
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Cjelokupna potrošnja tablice po shemi
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Cjelokupna potrošnja po tipu tablice
#XTIT: Tables
tableDetailsText=Pojedinosti tablice
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Potrošnja pohrane tablice
#XFLD: Table Type label
tableTypeLabel=Tip tablice
#XFLD: Schema label
schemaLabel=Shema
#XFLD: reset table tooltip
resetTable=Ponovno postavljanje tablice
#XFLD: In-Memory label in space monitor
inMemoryLabel=Memorija
#XFLD: Disk label in space monitor
diskLabel=Disk
#XFLD: Yes
yesLabel=Da
#XFLD: No
noLabel=Ne
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Želite li da podaci u ovom prostoru budu dostupni za potrošnju prema zadanim postavkama?
#XFLD: Business Name
businessNameLabel=Poslovni naziv
#XFLD: Refresh
refresh=Osvježi
#XMSG: No filter results title
noFilterResultsTitle=Čini se da vaše postavke filtra ne pokazuju podatke.
#XMSG: No filter results message
noFilterResultsMsg=Pokušajte suziti postavke filtra, a ako i dalje ne vidite podatke, stvorite tablice u sastavljaču podataka. Nakon što tablice potroše pohranu, moći ćete ih tamo nadzirati.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Baza podataka vremena izvođenja nije dostupna.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Budući da baza podataka vremena izvođenja nije dostupna, neke su značajke onemogućene i na ovoj stranici ne možemo prikazati nikakve informacije.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Korisnika sheme prostora nije moguće stvoriti.
#YMSE: Error User name already exists
userAlreadyExistsError=Korisničko ime već postoji.
#YMSE: Error Authentication failed
authenticationFailedError=Provjera autentičnosti nije uspjela.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Korisnik je zaključan zbog previše neuspjelih pokušaja prijave. Zatražite novu lozinku kako biste otključali korisnika.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Nova je lozinka postavljena, a korisnik otključan
#XMSG: user is locked message
userLockedMessage=Korisnik zaključan.
#XCOL: Users table-view column Role
spaceRole=Uloga
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Uloga u opsegu
#XCOL: Users table-view column Space Admin
spaceAdmin=Administrator prostora
#XFLD: User section dropdown value Viewer
viewer=Preglednik
#XFLD: User section dropdown value Modeler
modeler=Modelar
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Integrator podataka
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Administrator prostora
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Uloga prostora ažurirana je
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Uloga prostora nije uspješno ažurirana.
#XFLD:
databaseUserNameSuffix=Sufiks imena korisnika baze podataka
#XTXT: Space Schema password text
spaceSchemaPasswordText=Kako biste postavili vezu do ove sheme, kopirajte svoju lozinku. Ako ste zaboravili lozinku, uvijek možete zatražiti novu.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=SAP Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Kako biste postavili pristup preko ovog korisnika, omogućite potrošnju i kopirajte vjerodajnice. U slučaju da možete kopirati samo vjerodajnice bez lozinke, pripazite da lozinku dodate poslije.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Omogući potrošnju na platformi SAP Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Vjerodajnice za servis koji daje korisnik:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Vjerodajnice za servis koji daje korisnik (bez lozinke):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Kopiraj vjerodajnice bez lozinke
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Kopiraj potpune vjerodajnice
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Kopiraj lozinku
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Vjerodajnice kopirane u međuspremnik
#XMSG: Password copied to clipboard
passwordCopiedMessage=Lozinka kopirana u međuspremnik
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Stvori korisnika baze podataka
#XMSG: Database Users section title
databaseUsers=Korisnici baze podataka
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Pojedinosti korisnika baze podataka
#XFLD: database user read audit log
databaseUserAuditLogRead=Omogući zapisnike revizije za operacije čitanja i zadrži zapisnike
#XFLD: database user change audit log
databaseUserAuditLogChange=Omogući zapisnike revizije za operacije promjene i zadrži zapisnike
#XMSG: Cloud Platform Access
cloudPlatformAccess=Pristup platformi SAP Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Postavite pristup na svoj spremnik HANA Deployment Infrastructure preko ovog korisnika baze podataka. Za povezivanje sa spremnikom HDI, modeliranje SQL mora biti omogućeno.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Omogući potrošnju HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Želite li drugim aplikacijama ili alatima omogućiti potrošnju podataka u vašem prostoru?
#XFLD: Enable Consumption
enableConsumption=Omogući potrošnju SQL
#XFLD: Enable Modeling
enableModeling=Omogući modeliranje SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Učitavanje podataka
#XMSG: Privileges for Data Consumption
privilegesConsumption=Potrošnja podataka za vanjske alate
#XFLD: SQL Modeling
sqlModeling=Modeliranje SQL
#XFLD: SQL Consumption
sqlConsumption=Potrošnja SQL
#XFLD: enabled
enabled=Omogućeno
#XFLD: disabled
disabled=Onemogućeno
#XFLD: Edit Privileges
editPrivileges=Uredi ovlasti
#XFLD: Open Database Explorer
openDBX=Otvori Explorer baze podataka
#XFLD: create database user hint
databaseCreateHint=Imajte na umu da korisničko ime više nećete moći promijeniti nakon spremanja.
#XFLD: Internal Schema Name
internalSchemaName=Interni naziv sheme
#YMSE: Failed to load database users
loadDatabaseUserError=Učitavanje korisnika baze podataka nije uspjelo
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Brisanje korisnika baze podataka nije uspjelo
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Korisnik baze podataka izbrisan
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Korisnici baze podataka izbrisani
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Korisnik baze podataka stvoren
#YMSE: Failed to create database user
createDatabaseUserError=Stvaranje korisnika baze podataka nije uspjelo
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Korisnik baze podataka ažuriran
#YMSE: Failed to update database user
updateDatabaseUserError=Ažuriranje korisnika baze podataka nije uspjelo
#XFLD: HDI Consumption
hdiConsumption=Potrošnja HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Pristup bazi podataka
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Omogućite potrošnju podataka svog prostora prema zadanim postavkama. Modeli u sastavljačima automatski će dopustiti potrošnju podataka.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Zadana potrošnja podataka prostora:
#XFLD: Database User Name
databaseUserName=Ime korisnika baze podataka
#XMSG: Database User creation validation error message
databaseUserValidationError=Čini se da su neka polja nevaljana. Provjerite obvezna polja kako biste nastavili.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Nije moguće omogućiti učitavanje podataka jer je ovaj korisnik migriran.
#XBUT: Remove Button Text
remove=Ukloni
#XBUT: Remove Spaces Button Text
removeSpaces=Ukloni prostore
#XBUT: Remove Objects Button Text
removeObjects=Ukloni objekte
#XMSG: No members have been added yet.
noMembersAssigned=Još nisu dodani članovi.
#XMSG: No users have been added yet.
noUsersAssigned=Još nisu dodani korisnici.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Nisu stvoreni korisnici baze podataka ili vaš filtar ne pokazuje podatke.
#XMSG: Please enter a user name.
noDatabaseUsername=Unesite korisničko ime.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Korisničko ime predugo. Unesite kraće.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Nisu omogućene ovlasti i ovaj korisnik baze podataka imat će ograničenu funkcionalnost. Želite li ipak nastaviti?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Kako bi se omogućili zapisnici revizije za operacije promjene, također je potrebno omogućiti učitavanje podataka. Želite li to učiniti?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Kako bi se omogućili zapisnici revizije za operacije čitanja, također je potrebno omogućiti učitavanje podataka. Želite li to učiniti?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Kako bi se omogućila potrošnja HDI-ja, također je potrebno omogućiti učitavanje podataka i potrošnju podataka. Želite li to učiniti?
#XMSG:
databaseUserPasswordText=Kako biste postavili vezu do ovog korisnika baze podataka, kopirajte svoju lozinku. Ako ste zaboravili lozinku, uvijek možete zatražiti novu.
#XTIT: Space detail section members title
detailsSectionMembers=Članovi
#XMSG: New password set
newPasswordSet=Nova lozinka postavljena
#XFLD: Data Ingestion
dataIngestion=Učitavanje podataka
#XFLD: Data Consumption
dataConsumption=Potrošnja podataka
#XFLD: Privileges
privileges=Ovlasti
#XFLD: Enable Data ingestion
enableDataIngestion=Omogući učitavanje podataka
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Vodite zapisnik o operacjiama čitanja i promjene za učitavanje podataka.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Učinite podatke svog prostora dostupnima u svojim spremnicima HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Omogući potrošnju podataka
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Dopustite drugim aplikacijama ili alatima potrošnju podataka vašeg prostora.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Kako biste postavili pristup preko ovog korisnika, kopirajte vjerodajnice za svoj servis koji daje korisnik. U slučaju da možete kopirati samo vjerodajnice bez lozinke, pripazite da lozinku dodate poslije.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Kapacitet vremena izvođenja toka podataka ({0}:{1} h od {2} h)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Nije moguće učitati kapacitet vremena izvođenja toka podataka
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Korisnik može odobriti potrošnju podataka drugim korisnicima.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Omogući potrošnju podataka s mogućnošću odobrenja
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Kako biste omogućili potrošnju podataka s mogućnošću odobrenja, potrebno je omogućiti potrošnju podataka. Želite li omogućiti oboje?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Omogući Automated Predictive Library (APL) i Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Korisnik može upotrijebiti funkcije strojnog učenja ugrađene u SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Pravilnik o lozinkama
#XMSG: Password Policy
passwordPolicyHint=Ovdje omogućite ili onemogućite konfigurirani pravilnik o lozinkama.
#XFLD: Enable Password Policy
enablePasswordPolicy=Omogući pravilnik o lozinkama
#XMSG: Read Access to the Space Schema
readAccessTitle=Pristup za čitanje sheme prostora
#XMSG: read access hint
readAccessHint=Dopustite korisniku baze podataka da poveže vanjske alate sa shemom prostora i čita prikaze koji su izloženi za potrošnju.
#XFLD: Space Schema
spaceSchema=Shema prostora
#XFLD: Enable Read Access (SQL)
enableReadAccess=Omogući pristup za čitanje (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Dopustite korisniku da odobrava pristup za čitanje drugim korisnicima.
#XFLD: With Grant Option
withGrantOption=S mogućnošću odobrenja
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Učinite podatke svog prostora dostupnima u svojim spremnicima HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Omogući potrošnju HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Pristup za pisanje korisnikovoj shemi Open SQL
#XMSG: write access hint
writeAccessHint=Dopustite korisniku baze podataka da poveže vanjske alate s korisnikovom shemom Open SQL radi stvaranja entiteta podataka i učitavanja podataka za upotrebu u prostoru.
#XFLD: Open SQL Schema
openSQLSchema=Shema Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Omogući pristup za pisanje (SQL, DDL i DML)
#XMSG: audit hint
auditHint=Zabilježite operacije čitanja i mijenjanja u shemi Open SQL.
#XMSG: data consumption hint
dataConsumptionHint=Izložite sve nove prikaze u prostoru za potrošnju prema zadanim postavkama. Modelari mogu zamijeniti ovu postavku za pojedinačne prikaze prekidačem "Izloži za potrošnju" u bočnom panelu izlaza prikaza. Također možete izabrati formate u kojima se prikazi izlažu.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Izloži za potrošnju prema zadanim postavkama
#XMSG: database users hint consumption hint
databaseUsersHint2New=Stvorite korisnike baze podataka za povezivanje vanjskih alata s SAP Datasphere. Postavite ovlasti kako biste dopustili korisnicima čitanje podataka prostora te stvaranje entiteta podataka (DDL) i učitavanje podataka (DML) za upotrebu u prostoru.
#XFLD: Read
read=Čitanje
#XFLD: Read (HDI)
readHDI=Čitanje (HDI)
#XFLD: Write
write=Pisanje
#XMSG: HDI Containers Hint
HDIContainersHint2=Omogućite pristup svojim spremnicima SAP HANA Deployment Infrastructure (HDI) u vašem prostoru. Modelari mogu upotrebljavati artefakte HDI kao izvore za prikaze, a klijenti HDI mogu pristupati podacima vašeg prostora.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Otvorite dijalog informacija
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Korisnik baze podataka je zaključan. Za otključavanje otvorite dijalog
#XFLD: Table
table=Tablica
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Veza partnera
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Konfiguracija veze partnera
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Definirajte vlastitu pločicu veze partnera dodavanjem svog URL-a i ikone za iFrame. Ta je konfiguracija dostupna samo za ovog zakupca.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Naziv pločice
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL za iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Porijeklo poruke o objavi iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ikona
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Nije moguće pronaći konfiguracije veze partnera.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Konfiguracije partnerske veze ne mogu se prikazati kad je baza podataka vremena izvođenja nedostupna.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Stvaranje konfiguracije veze partnera
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Prenesi ikonu
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Odaberite (maksimalna veličina 200 KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Primjer pločice partnera
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Pregledaj
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Konfiguracija veze partnera uspješno je stvorena.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Došlo je do pogreške pri brisanju konfiguracija veze partnera.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Konfiguracija veze partnera uspješno je izbrisana.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Došlo je do pogreške pri dohvaćanju konfiguracija veze partnera.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Datoteku nije moguće prenijeti jer prekoračuje maksimalnu veličinu od 200 KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Stvori konfiguraciju veze partnera
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Izbriši konfiguraciju veze partnera.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Pločicu partnera nije moguće stvoriti.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Pločicu partnera nije moguće izbrisati.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Ponovno postavljanje postavki konektora za oblak u SAP HANA za kupca nije uspjelo
#XFLD: Workload Class
workloadClass=Klasa radnog opterećenja
#XFLD: Workload Management
workloadManagement=Upravljanje radnim opterećenjem
#XFLD: Priority
workloadClassPriority=Prioritet
#XMSG:
workloadManagementPriorityHint=Možete odrediti prioritet ovog prostora pri slanju upita u bazu podataka. Unesite vrijednost od 1 (najmanji prioritet) do 8 (najveći prioritet). U situaciji u kojoj se prostori natječu za dostupne niti, oni s većim prioritetima izvode se prije prostora s manjim prioritetima.
#XMSG:
workloadClassPriorityHint=Možete navesti prioritet prostora od 0 (najmanji prioritet) do 8 (najveći). Naredbe prostora s visokim prioritetom izvršavaju se prije naredbi drugih prostora s niskim prioritetom. Zadani je prioritet 5. Vrijednost prioriteta 9 rezervirana je za operacije sustava i stoga nije dostupna za prostor.
#XFLD: Statement Limits
workloadclassStatementLimits=Ograničenja naredbi
#XFLD: Workload Configuration
workloadConfiguration=Konfiguracija radnog opterećenja
#XMSG:
workloadClassStatementLimitsHint=Možete navesti maksimalan broj (ili postotak) niti i GB-ova memorije koje naredbe koje se izvode istovremeno u prostoru mogu potrošiti. Možete unijeti vrijednost ili postotak između 0 (bez ograničenja) i ukupne memorije i niti dostupnih u zakupcu. \n\n Ako navedete ograničenje niti, imajte na umu da to može dovesti do slabije izvedbe. \n\n Ako navedete ograničenje memorije, naredbe koje dosegnu ograničenje memorije neće se izvesti.
#XMSG:
workloadClassStatementLimitsDescription=Zadana konfiguracija osigurava široka ograničenja resursa, dok sprječava da pojedinačni prostor preoptereti sustav.
#XMSG:
workloadClassStatementLimitCustomDescription=Možete postaviti maksimum ukupnih ograničenja niti i memorije, koje naredbe koje se istovremeno izvode u prostoru mogu iskoristiti.
#XMSG:
totalStatementThreadLimitHelpText=Postavljanje ograničenja niti prenisko može utjecati na izvedbu naredbi, dok pretjerano visoke vrijednosti ili 0 mogu omogućiti prostoru da potroši sve dostupne niti sustava.
#XMSG:
totalStatementMemoryLimitHelpText=Postavljanje ograničenja memorije prenisko može uzrokovati probleme s nedostatkom memorije, dok pretjerano visoke vrijednosti ili 0 mogu omogućiti prostoru iskorištenje cijele dostupne memorije sustava.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Unesite postotak između 1 % i 70 % (ili ekvivalentni broj) ukupnog broja niti dostupnih u vašem zakupcu. Postavljanje ograničenja niti prenisko može utjecati na izvedbu naredbi, dok previsoke vrijednosti mogu utjecati na izvedbu naredbi u drugim prostorima.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Unesite postotak između 1 % i {0} % (ili ekvivalentni broj) ukupnog broja niti dostupnih u vašem zakupcu. Postavljanje ograničenja niti prenisko može utjecati na izvedbu naredbi, dok previsoke vrijednosti mogu utjecati na izvedbu naredbi u drugim prostorima.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Unesite vrijednost ili postotak između 0 (bez ograničenja) i ukupne količine memorije dostupne u vašem zakupcu. Postavljanje ograničenja memorije prenisko može utjecati na izvedbu naredbi, dok previsoke vrijednosti mogu utjecati na izvedbu naredbi u drugim prostorima.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Ukupno ograničenje niti naredbi
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Niti
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Ukupno ograničenje memorije naredbi
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Učitavanje informacija stranke za SAP HANA nije uspjelo.
#XMSG:
minimumLimitReached=Dosegnuto je minimalno ograničenje.
#XMSG:
maximumLimitReached=Dosegnuto je maksimalno ograničenje.
#XMSG: Name Taken for Technical Name
technical-name-taken=Veza s tehničkim nazivom koji ste unijeli već postoji. Unesite drugi naziv.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Tehnički naziv koji ste unijeli prekoračuje 40 znakova. Unesite naziv s manje znakova.
#XMSG: Technical name field empty
technical-name-field-empty=Unesite tehnički naziv.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Za naziv možete upotrijebiti samo slova (a-z), brojeve (0-9) i donje crte (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Naziv koji ste unijeli ne može započeti ili završiti donjom crtom (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Omogući ograničenja naredbi
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Postavke
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Da biste stvorili ili uredili veze, otvorite aplikaciju Veze na bočnoj navigaciji ili kliknite ovdje:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Idi na Veze
#XFLD: Not deployed label on space tile
notDeployedLabel=Prostor još nije uveden.
#XFLD: Not deployed additional text on space tile
notDeployedText=Uvedite prostor.
#XFLD: Corrupt space label on space tile
corruptSpace=Došlo je do problema.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Pokušajte ponovo uvesti ili se obratite podršci
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Podaci zapisnika revizije
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Administrativni podaci
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Drugi podaci
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Podaci u prostoru
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Želite li zaista otključati prostor?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Želite li zaista zaključati prostor?
#XFLD: Lock
lock=Zaključaj
#XFLD: Unlock
unlock=Otključaj
#XFLD: Locking
locking=Zaključavanje
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Prostor zaključan
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Prostor otključan
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Prostori zaključani
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Prostori otključani
#YMSE: Error while locking a space
lockSpaceError=Nije moguće zaključati prostor.
#YMSE: Error while unlocking a space
unlockSpaceError=Nije moguće otključati prostor.
#XTIT: popup title Warning
confirmationWarningTitle=Upozorenje
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Prostor je ručno zaključan.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Sustav je zaključao prostor jer zapisnici revizije troše veliku količinu GB-a diska.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Sustav je zaključao prostor jer prekoračuje svoje alokacije pohrane u memoriji ili na disku.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Želite li zaista otključati odabrani prostor?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Želite li zaista zaključati odabrani prostor?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Uređivač uloga u opsegu
#XTIT: ECN Management title
ecnManagementTitle=Upravljanje prostorom i čvorom elastičnog izračuna
#XFLD: ECNs
ecns=Čvorovi elastičnih izračuna
#XFLD: ECN phase Ready
ecnReady=Spremno
#XFLD: ECN phase Running
ecnRunning=Izvodi se
#XFLD: ECN phase Initial
ecnInitial=Nije spremno
#XFLD: ECN phase Starting
ecnStarting=Pokretanje
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Pokretanje nije uspjelo
#XFLD: ECN phase Stopping
ecnStopping=Zaustavljanje
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Zaustavljanje nije uspjelo
#XBTN: Assign Button
assign=Dodaj prostore
#XBTN: Start Header-Button
start=Započni
#XBTN: Update Header-Button
repair=Ažuriraj
#XBTN: Stop Header-Button
stop=Zaustavi
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 h preostalo
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=Broj preostalih blok-sati: {0}
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} preostali blok-sat
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Stvaranje čvora elastičnog izračuna
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Uređivanje čvora elastičnog izračuna
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Brisanje čvora elastičnog izračuna
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Dodavanje prostora
#XFLD: ECN ID
ECNIDLabel=Čvora elastičnog izračuna
#XTXT: Selected toolbar text
selectedToolbarText=Odabrano: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Čvorovi elastičnih izračuna
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Broj objekata
#XTIT: Object assignment - Dialog header text
selectObjects=Odaberite prostore i objekte koje želite dodijeliti svom čvoru elastičnog izračuna
#XTIT: Object assignment - Table header title: Objects
objects=Objekti
#XTIT: Object assignment - Table header: Type
type=Tip
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Imajte na umu da će brisanje korisnika baze podataka dovesti do brisanja svih generiranih unosa zapisnika revizije. Ako želite zadržati zapisnike revizije, razmislite o njihovom izvozu prije nego izbrišete korisnika baze podataka.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Imajte na umu da će poništavanje dodjele spremnika HDI u prostoru dovesti do brisanja svih generiranih unosa zapisnika revizije. Ako želite zadržati zapisnike revizije, razmislite o njihovom izvozu prije nego što poništite dodjelu spremnika HDI.
#XTXT: All audit logs
allAuditLogs=Svi unosi zapisnika revizije generirani za prostor
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Imajte na umu da će onemogućavanje pravilnika o reviziji (operacija čitanja ili promjene) dovesti do brisanja svih njenih unosa zapisnika revizije. Ako želite zadržati unose zapisnika revizije, razmislite o njihovu izvozu prije nego što onemogućite taj pravilnik o reviziji.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Prostori ili objekti još nisu dodijeljeni
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Da biste započeli raditi sa svojim čvorom elastičnog izračuna, dodijelite mu prostor ili objekte.
#XTIT: No Spaces Illustration title
noSpacesTitle=Prostor još nije stvoren
#XTIT: No Spaces Illustration description
noSpacesDescription=Za početak prikupljanja podataka stvorite prostor.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Koš za smeće je prazan
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Ne možete vratiti izbrisane prostore otamo.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Kad se uvede prostor, sljedeći korisnici baze podataka bit će {0} izbrisani i ne mogu se vratiti:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Brisanje korisnika baze podataka
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID već psotoji.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Upotrijebite samo mala slova a - z i brojeve 0 - 9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID mora imati najmanje ovoliko znakova: {0}.
#XMSG: ecn id length warning
ecnIdLengthWarning=Maksimum od {0} znakova prekoračen.
#XFLD: open System Monitor
systemMonitor=Nadzor sustava
#XFLD: open ECN schedule dialog menu entry
schedule=Raspored
#XFLD: open create ECN schedule dialog
createSchedule=Stvori raspored
#XFLD: open change ECN schedule dialog
changeSchedule=Uredi raspored
#XFLD: open delete ECN schedule dialog
deleteSchedule=Izbriši raspored
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Dodijeli mi raspored
#XFLD: open pause ECN schedule dialog
pauseSchedule=Pauziraj raspored
#XFLD: open resume ECN schedule dialog
resumeSchedule=Nastavi s rasporedom
#XFLD: View Logs
viewLogs=Prikaži zapisnike
#XFLD: Compute Blocks
computeBlocks=Računski blokovi
#XFLD: Memory label in ECN creation dialog
ecnMemory=Memorija (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Pohrana (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Broj CPU
#XFLD: ECN updated by label
changedBy=Promijenio
#XFLD: ECN updated on label
changedOn=Promijenjeno
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Čvor elastičnog izračuna stvoren
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Nije moguće stvoriti čvor elastičnog izračuna
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Čvor elastičnog izračuna ažuriram
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Nije moguće ažurirati čvor elastičnog izračuna
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Čvor elastičnog izračuna izbrisan
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Nije moguće izbrisati čvor elastičnog izračuna
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Pokretanje čvora elastičnog izračuna
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Zaustavljanje čvora elastičnog izračuna
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Nije moguće pokrenuti čvor elastičnog izračuna
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Nije moguće zaustaviti čvor elastičnog izračuna
#XBUT: Add Object button for an ECN
assignObjects=Dodaj objekte
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Automatski dodaj sve objekte
#XFLD: object type label to be assigned
objectTypeLabel=Tip (semantička upotreba)
#XFLD: assigned object type label
assignedObjectTypeLabel=Tip
#XFLD: technical name label
TechnicalNameLabel=Tehnički naziv
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Odaberite objekte koje želite dodati čvoru elastičnog izračuna
#XTIT: Add objects dialog title
assignObjectsTitle=Dodjela objekata
#XFLD: object label with object count
objectLabel=Objekt
#XMSG: No objects available to add message.
noObjectsToAssign=Nema dostupnih objekata za dodjelu.
#XMSG: No objects assigned message.
noAssignedObjects=Objekti nisu dodijeljeni.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Upozorenje
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Izbriši
#XMSG: Remove objects popup text
removeObjectsConfirmation=Želite li zaista ukloniti odabrane objekte?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Želite li zaista ukloniti odabrane prostore?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Ukloni prostore
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Izloženi objekti uklonjeni su
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Izloženi objekti dodijeljeni su
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Svi izloženi objekti
#XFLD: Spaces tab label
spacesTabLabel=Prostori
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Izloženi objekti
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Prostori su uklonjeni
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Prostor je uklonjen
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Prostore nije moguće dodijeliti ili ukloniti.
#YMSE: Error while removing objects
removeObjectsError=Ne možemo dodijeliti ili ukloniti objekte.
#YMSE: Error while removing object
removeObjectError=Ne možemo dodijeliti ili ukloniti objekt.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Prethodno odabrani broj više ne vrijedi. Odaberite valjan broj.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Odaberite valjanu klasu izvedbe.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Prethodno odabrana klasa izvedbe "{0}" trenutačno nije valjana. Odaberite valjanu klasu izvedbe.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Želite li zaista izbrisati čvor elastičnog izračuna?
#XFLD: tooltip for ? button
help=Pomoć
#XFLD: ECN edit button label
editECN=Konfiguriraj
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Model odnosa između entiteta
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Lokalna tablica
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Udaljena tablica
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analitički model
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Lanac zadataka
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Tok podataka
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Tok replikacije
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Tok transformacije
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Inteligentno pretraživanje
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Spremište
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Pretraživanje za poduzeća
#XFLD: Technical type label for View
DWC_VIEW=Prikaz
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Podatkovni proizvod
#XFLD: Technical type label for Data Access Control
DWC_DAC=Kontrola pristupa podacima
#XFLD: Technical type label for Folder
DWC_FOLDER=Mapa
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Poslovni entitet
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Varijanta poslovnog entiteta
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Scenarij odgovornosti
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Model činjenica
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektiva
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Model potrošnje
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Udaljena veza
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Varijanta modela činjenica
#XMSG: Schedule created alert message
createScheduleSuccess=Raspored stvoren
#XMSG: Schedule updated alert message
updateScheduleSuccess=Raspored ažuriran
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Raspored izbrisan
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Dodijeljen vam je raspored
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Pauziranje 1 rasporeda
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Nastavak 1 rasporeda
#XFLD: Segmented button label
availableSpacesButton=Dostupno
#XFLD: Segmented button label
selectedSpacesButton=Odabrano
#XFLD: Visit website button text
visitWebsite=Posjetite web-mjesto
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Prethodno odabran izvorni jezik uklonit će se.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Omogući
#XFLD: ECN performance class label
performanceClassLabel=Klasa izvedbe
#XTXT performance class memory text
memoryText=Memorija
#XTXT performance class compute text
computeText=Računalstvo 
#XTXT performance class high-compute text
highComputeText=Računalstvo visokih performansi
#XBUT: Recycle Bin Button Text
recycleBin=Koš za smeće
#XBUT: Restore Button Text
restore=Vrati
#XMSG: Warning message for new Workload Management UI
priorityWarning=Ovo je područje samo za čitanje. Možete promijeniti prioritet prostora u području Sustav / Konfiguracija / Upravljanje radnim opterećenjem.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Ovo je područje samo za čitanje. Možete promijeniti konfiguraciju radnog opterećenja prostora u području Sustav / Konfiguracija / Upravljanje radnim opterećenjem.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=vCPU-ovi Apache Sparka
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Memorija Apache Sparka (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Učitavanje podatkovnog proizvoda
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Nema dostupnih podataka jer se prostor trenutačno uvodi
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Nema dostupnih podataka jer se prostor trenutačno učitava
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Uredi mapiranja instanci
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
