#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=監控
#XTXT: Type name for spaces in browser tab page title
space=空間
#_____________________________________
#XFLD: Spaces label in
spaces=空間
#XFLD: Manage plan button text
manageQuotaButtonText=管理計劃
#XBUT: Manage resources button
manageResourcesButton=管理資源
#XFLD: Create space button tooltip
createSpace=建立空間
#XFLD: Create
create=建立
#XFLD: Deploy
deploy=部署
#XFLD: Page
page=頁面
#XFLD: Cancel
cancel=取消
#XFLD: Update
update=更新
#XFLD: Save
save=儲存
#XFLD: OK
ok=確定
#XFLD: days
days=天數
#XFLD: Space tile edit button label
edit=編輯
#XFLD: Auto Assign all objects to space
autoAssign=自動指派
#XFLD: Space tile open monitoring button label
openMonitoring=監控
#XFLD: Delete
delete=刪除
#XFLD: Copy Space
copy=複製
#XFLD: Close
close=關閉
#XCOL: Space table-view column status
status=狀態
#XFLD: Space status active
activeLabel=啟用中
#XFLD: Space status locked
lockedLabel=已鎖住
#XFLD: Space status critical
criticalLabel=關鍵
#XFLD: Space status cold
coldLabel=不常用
#XFLD: Space status deleted
deletedLabel=已刪除
#XFLD: Space status unknown
unknownLabel=未知
#XFLD: Space status ok
okLabel=健康
#XFLD: Database user expired
expired=已到期
#XFLD: deployed
deployed=已部署
#XFLD: not deployed
notDeployed=未部署
#XFLD: changes to deploy
changesToDeploy=對部署更改
#XFLD: pending
pending=部署中
#XFLD: designtime error
designtimeError=設計時期錯誤
#XFLD: runtime error
runtimeError=執行時期錯誤
#XFLD: Space created by label
createdBy=建立者
#XFLD: Space created on label
createdOn=建立日期
#XFLD: Space deployed on label
deployedOn=部署日期
#XFLD: Space ID label
spaceID=空間 ID
#XFLD: Priority label
priority=優先順序
#XFLD: Space Priority label
spacePriority=空間優先順序
#XFLD: Space Configuration label
spaceConfiguration=空間組態
#XFLD: Not available
notAvailable=無
#XFLD: WorkloadType default
default=預設
#XFLD: WorkloadType custom
custom=自訂
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=資料湖存取
#XFLD: Translation label
translationLabel=翻譯
#XFLD: Source language label
sourceLanguageLabel=來源語言
#XFLD: Translation CheckBox label
translationCheckBox=啟用翻譯
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=部署空間以存取使用者明細。
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=部署空間以開啟資料庫總管。
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=由於另一空間已使用資料，因此您無法使用此空間來存取該資料湖。
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=使用此空間存取資料湖。
#XFLD: Space Priority minimum label extension
low=低
#XFLD: Space Priority maximum label extension
high=高
#XFLD: Space name label
spaceName=空間名稱
#XFLD: Enable deploy objects checkbox
enableDeployObjects=部署物件
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=複製 {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(未選擇)
#XTXT Human readable text for language code "af"
af=南非荷蘭文
#XTXT Human readable text for language code "ar"
ar=阿拉伯文
#XTXT Human readable text for language code "bg"
bg=保加利亞文
#XTXT Human readable text for language code "ca"
ca=加泰羅尼亞文
#XTXT Human readable text for language code "zh"
zh=簡體中文
#XTXT Human readable text for language code "zf"
zf=中文
#XTXT Human readable text for language code "hr"
hr=克羅埃西亞文
#XTXT Human readable text for language code "cs"
cs=捷克文
#XTXT Human readable text for language code "cy"
cy=威爾斯文
#XTXT Human readable text for language code "da"
da=丹麥文
#XTXT Human readable text for language code "nl"
nl=荷蘭文
#XTXT Human readable text for language code "en-UK"
en-UK=英文 (英國)
#XTXT Human readable text for language code "en"
en=英文 (美國)
#XTXT Human readable text for language code "et"
et=愛沙尼亞文
#XTXT Human readable text for language code "fa"
fa=波斯文
#XTXT Human readable text for language code "fi"
fi=芬蘭文
#XTXT Human readable text for language code "fr-CA"
fr-CA=法文 (加拿大)
#XTXT Human readable text for language code "fr"
fr=法文
#XTXT Human readable text for language code "de"
de=德文
#XTXT Human readable text for language code "el"
el=希臘文
#XTXT Human readable text for language code "he"
he=希伯來文
#XTXT Human readable text for language code "hi"
hi=印度文
#XTXT Human readable text for language code "hu"
hu=匈牙利文
#XTXT Human readable text for language code "is"
is=冰島文
#XTXT Human readable text for language code "id"
id=印尼文
#XTXT Human readable text for language code "it"
it=義大利文
#XTXT Human readable text for language code "ja"
ja=日文
#XTXT Human readable text for language code "kk"
kk=哈薩克文
#XTXT Human readable text for language code "ko"
ko=韓文
#XTXT Human readable text for language code "lv"
lv=拉脫維亞文
#XTXT Human readable text for language code "lt"
lt=立陶宛文
#XTXT Human readable text for language code "ms"
ms=馬來文
#XTXT Human readable text for language code "no"
no=挪威文
#XTXT Human readable text for language code "pl"
pl=波蘭文
#XTXT Human readable text for language code "pt"
pt=葡萄牙文 (巴西)
#XTXT Human readable text for language code "pt-PT"
pt-PT=葡萄牙文 (葡萄牙)
#XTXT Human readable text for language code "ro"
ro=羅馬尼亞文
#XTXT Human readable text for language code "ru"
ru=俄文
#XTXT Human readable text for language code "sr"
sr=塞爾維亞文
#XTXT Human readable text for language code "sh"
sh=塞爾維亞克羅埃西亞文
#XTXT Human readable text for language code "sk"
sk=斯洛伐克文
#XTXT Human readable text for language code "sl"
sl=斯洛維尼亞文
#XTXT Human readable text for language code "es"
es=西班牙文
#XTXT Human readable text for language code "es-MX"
es-MX=西班牙文 (墨西哥)
#XTXT Human readable text for language code "sv"
sv=瑞典文
#XTXT Human readable text for language code "th"
th=泰文
#XTXT Human readable text for language code "tr"
tr=土耳其文
#XTXT Human readable text for language code "uk"
uk=烏克蘭文
#XTXT Human readable text for language code "vi"
vi=越南文
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=刪除空間
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=您確定要將空間「{0}」移至資源回收桶嗎？
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=您確定要將所選 {0} 個空間移至資源回收桶嗎？
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=您確定要刪除空間「{0}」嗎？此動作無法復原。
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=您確定要刪除 {0} 個所選連線嗎？此動作無法復原。下列內容將{1}刪除：
#XTXT: permanently
permanently=永久
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=下列內容將{0}刪除，且無法復原：
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=請輸入 {0} 確認刪除。
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=請檢查拼字並重試。
#XTXT: All Spaces
allSpaces=所有空間
#XTXT: All data
allData=空間中包含的所有物件和資料
#XTXT: All connections
allConnections=空間中定義的所有連線
#XFLD: Space tile selection box tooltip
clickToSelect=按一下以選擇
#XTXT: All database users
allDatabaseUsers=與空間相關的開放式 SQL 綱要中包含的所有物件和資料
#XFLD: remove members button tooltip
deleteUsers=移除成員
#XTXT: Space long description text
description=說明 (最多 4000 個字元)
#XFLD: Add Members button tooltip
addUsers=新增成員
#XFLD: Add Users button tooltip
addUsersTooltip=新增使用者
#XFLD: Edit Users button tooltip
editUsersTooltip=編輯使用者
#XFLD: Remove Users button tooltip
removeUsersTooltip=移除使用者
#XFLD: Searchfield placeholder
filter=搜尋
#XCOL: Users table-view column health
health=健康
#XCOL: Users table-view column access
access=存取
#XFLD: No user found nodatatext
noDataText=找不到使用者
#XTIT: Members dialog title
selectUserDialogTitle=新增成員
#XTIT: User dialog title
addUserDialogTitle=新增使用者
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=刪除連線
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=刪除連線
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=您確定要刪除所選連線？所選連線將永久移除。
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=選擇連線
#XTIT: Share connection dialog title
connectionSharingDialogTitle=共用連線
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=共用的連線
#XFLD: Add remote source button tooltip
addRemoteConnections=新增連線
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=移除連線
#XFLD: Share remote source button tooltip
shareConnections=共用連線
#XFLD: Tile-layout tooltip
tileLayout=功能磚配置
#XFLD: Table-layout tooltip
tableLayout=表格配置
#XMSG: Success message after creating space
createSpaceSuccessMessage=已建立空間。
#XMSG: Success message after copying space
copySpaceSuccessMessage=將空間 "{0}" 複製到空間 "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=已開始空間部署
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Apache Spark 更新已開始
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=無法更新 Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=已更新空間明細
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=已暫時解除鎖定空間
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=已刪除空間
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=已刪除空間
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=已還原空間
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=已還原空間
#YMSE: Error while updating settings
updateSettingsFailureMessage=無法更新空間設定。
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=已將資料湖指派給另一空間。一次僅一個空間可存取該資料湖。
#YMSE: Error while updating data lake option
virtualTablesExists=由於仍有虛擬表格相關性，因此您無法取消指派此空間的資料湖。請刪除虛擬表格以取消指派此空間的資料湖。
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=無法將空間解除鎖定。
#YMSE: Error while creating space
createSpaceError=無法建立空間。
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=名稱為 {0} 的空間已存在。
#YMSE: Error while deleting a single space
deleteSpaceError=無法刪除空間。
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=您的空間「{0}」無法正常運作，請嘗試再將刪除一次。若依然無法運作，可請求管理員刪除空間或建立記錄單。
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=無法刪除檔案中的空間資料。
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=無法移除使用者。
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=無法移除綱要。
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=無法移除連線。
#YMSE: Error while deleting a single space data
deleteSpaceDataError=無法刪除空間資料。
#YMSE: Error while deleting multiple spaces
deleteSpacesError=無法刪除空間。
#YMSE: Error while restoring a single space
restoreSpaceError=無法還原空間。
#YMSE: Error while restoring multiple spaces
restoreSpacesError=無法還原空間。
#YMSE: Error while creating users
createUsersError=無法新增使用者。
#YMSE: Error while removing users
removeUsersError=無法移除使用者。
#YMSE: Error while removing user
removeUserError=無法移除使用者。
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=無法將使用者新增至所選範圍角色。\n\n您無法將自己新增至範圍角色。您可詢問管理員將您新增至範圍角色。
#YMSE: Error assigning user to the space
userAssignError=無法將使用者指派給空間。\n\n已在範圍角色之間將使用者指派給最大允許數量 (100) 的空間。
#YMSE: Error assigning users to the space
usersAssignError=無法將使用者指派給空間。\n\n已在範圍角色之間將使用者指派給最大允許數量 (100) 的空間。
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=無法檢索使用者，請稍後再試。
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=無法檢索範圍角色。
#YMSE: Error while fetching members
fetchUserError=無法取得成員，請稍後再試。
#YMSE: Error while loading run-time database
loadRuntimeError=無法從執行時期資料庫載入資訊。
#YMSE: Error while loading spaces
loadSpacesError=很抱歉，嘗試檢索您的空間時發生問題。
#YMSE: Error while loading haas resources
loadStorageError=很抱歉，嘗試檢索儲存資料時發生問題。
#YMSE: Error no data could be loaded
loadDataError=很抱歉，嘗試檢索您的資料時發生問題。
#XFLD: Click to refresh storage data
clickToRefresh=按一下此處再試一次。
#XTIT: Delete space popup title
deleteSpacePopupTitle=刪除空間
#XCOL: Spaces table-view column name
name=名稱
#XCOL: Spaces table-view deployment status
deploymentStatus=部署狀態
#XFLD: Disk label in space details
storageLabel=硬碟 (GB)
#XFLD: In-Memory label in space details
ramLabel=記憶體 (GB)
#XFLD: Memory label on space card
memory=用於儲存的記憶體
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=空間儲存
#XFLD: Storage Type label in space details
storageTypeLabel=儲存類型
#XFLD: Enable Space Quota
enableSpaceQuota=啟用空間配額
#XFLD: No Space Quota
noSpaceQuota=無空間配額
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA 資料庫 (硬碟和記憶體內)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA 資料湖檔案
#XFLD: Available scoped roles label
availableRoles=可用範圍角色
#XFLD: Selected scoped roles label
selectedRoles=所選範圍角色
#XCOL: Spaces table-view column models
models=模型
#XCOL: Spaces table-view column users
users=使用者
#XCOL: Spaces table-view column connections
connections=連線
#XFLD: Section header overview in space detail
overview=概觀
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=應用程式
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=工作細項指派
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=記憶體 (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=空間組態
#XFLD: Space Source label
sparkApplicationLabel=應用程式
#XFLD: Cluster Size label
clusterSizeLabel=叢集大小
#XFLD: Driver label
driverLabel=驅動程式
#XFLD: Executor label
executorLabel=執行器
#XFLD: max label
maxLabel=最大使用
#XFLD: TrF Default label
trFDefaultLabel=轉換流程預設
#XFLD: Merge Default label
mergeDefaultLabel=合併預設
#XFLD: Optimize Default label
optimizeDefaultLabel=最佳化預設
#XFLD: Deployment Default label
deploymentDefaultLabel=本端表格 (檔案) 部署
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU/{1} GB
#XFLD: Object type label
taskObjectTypeLabel=物件類型
#XFLD: Task activity label
taskActivityLabel=作業
#XFLD: Task Application ID label
taskApplicationIDLabel=預設應用程式
#XFLD: Section header in space detail
generalSettings=一般設定
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=系統目前已鎖住此空間。
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=將立即部署此區段中的更改。
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=請注意，更改這些值可能造成效能問題。
#XFLD: Button text to unlock the space again
unlockSpace=解除鎖定空間
#XFLD: Info text for audit log formatted message
auditLogText=啟用稽核日誌以記錄讀取或更改動作 (稽核政策)。管理員即可分析在特定時間點執行特定動作的人員。
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=稽核日誌可能使用租用戶中大量硬碟儲存。若啟用稽核政策 (讀取或更改動作)，則應定期監控硬碟儲存使用 (透過「系統監控器」中的「使用的硬碟儲存」卡片) 避免完全硬碟中斷，這可能造成服務中斷。若停用稽核政策，則將刪除所有其稽核日誌輸入項。若要保留稽核日誌輸入項，則考慮匯出再停用稽核政策。
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=顯示輔助說明
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=此空間超過其空間儲存，且將在 {0} {1}內鎖住。
#XMSG: Unit for remaining time until space is locked again
hours=小時
#XMSG: Unit for remaining time until space is locked again
minutes=分鐘
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=稽核
#XFLD: Subsection header in space detail for auditing
auditing=空間稽核設定
#XFLD: Hot space tooltip
hotSpaceCountTooltip=關鍵空間：使用的儲存大於 90%。
#XFLD: Green space tooltip
greenSpaceCountTooltip=健康空間：使用的儲存介於 6% 到 90% 之間。
#XFLD: Cold space tooltip
coldSpaceCountTooltip=不常存取的空間：使用的儲存小於或等於 5%。
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=關鍵空間：使用的儲存大於 90%。
#XFLD: Green space tooltip
okSpaceCountTooltip=健康空間：使用的儲存介於 6% 到 90% 之間。
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=鎖住的空間：因記憶體不足而凍結。
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=鎖住的空間
#YMSE: Error while deleting remote source
deleteRemoteError=無法移除連線。
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=無法於稍後更改空間 ID。\n有效的字元為 A-Z、0-9 和 _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=請輸入空間名稱。
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=請輸入業務名稱。
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=請輸入空間 ID。
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=字元無效。請僅使用 A-Z、0-9 和 _。
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=空間 ID 已存在。
#XFLD: Space searchfield placeholder
search=搜尋
#XMSG: Success message after creating users
createUsersSuccess=已新增使用者
#XMSG: Success message after creating user
createUserSuccess=已新增使用者
#XMSG: Success message after updating users
updateUsersSuccess=已更新 {0} 個使用者
#XMSG: Success message after updating user
updateUserSuccess=已更新使用者
#XMSG: Success message after removing users
removeUsersSuccess=已移除 {0} 個使用者
#XMSG: Success message after removing user
removeUserSuccess=已移除使用者
#XFLD: Schema name
schemaName=綱要名稱
#XFLD: used of total
ofTemplate={0}/{1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=指派的硬碟 ({0}/{1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=指派的記憶體 ({0}/{1})
#XFLD: Storage ratio on space
accelearationRAM=記憶體加速
#XFLD: No Storage Consumption
noStorageConsumptionText=未指派儲存配額。
#XFLD: Used disk label in space overview
usedStorageTemplate=用於儲存的硬碟 ({0}/{1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=用於儲存的記憶體 ({0}/{1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0}/{1} 硬碟已用於儲存
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0}/{1} 記憶體已使用
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0}/{1} 硬碟已指派
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0}/{1} 記憶體已指派
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=空間資料：{0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=其他資料：{0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=請考慮擴展計劃，或者聯絡 SAP 支援。
#XCOL: Space table-view column used Disk
usedStorage=用於儲存的硬碟
#XCOL: Space monitor column used Memory
usedRAM=用於儲存的記憶體
#XCOL: Space monitor column Schema
tableSchema=綱要
#XCOL: Space monitor column Storage Type
tableStorageType=儲存類型
#XCOL: Space monitor column Table Type
tableType=表格類型
#XCOL: Space monitor column Record Count
tableRecordCount=記錄計數
#XFLD: Assigned Disk
assignedStorage=已指派用於儲存的硬碟
#XFLD: Assigned Memory
assignedRAM=已指派用於儲存的記憶體
#XCOL: Space table-view column storage utilization
tableStorageUtilization=使用的儲存
#XFLD: space status
spaceStatus=空間狀態
#XFLD: space type
spaceType=空間類型
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW 橋接
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=資料提供者產品
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=由於空間 {0} 類型為 {1}，因此無法刪除。
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=您無法刪除 {0} 個所選空間。無法刪除具有下列空間類型的空間：{1}。
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=監控
#XFLD: Tooltip for edit space button
editSpace=編輯空間
#XMSG: Deletion warning in messagebox
deleteConfirmation=您確定要刪除此空間嗎?
#XFLD: Tooltip for delete space button
deleteSpace=刪除空間
#XFLD: storage
storage=用於儲存的硬碟
#XFLD: username
userName=使用者名稱
#XFLD: port
port=連接埠
#XFLD: hostname
hostName=主機名稱
#XFLD: password
password=密碼
#XBUT: Request new password button
requestPassword=請求新密碼
#YEXP: Usage explanation in time data section
timeDataSectionHint=建立要在模型和故事中使用的時間表格和維度。
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=您要讓其他工具或應用程式可使用空間中的資料嗎？若是，請建立可在空間中存取資料的一或多個使用者，並選擇按預設是否可使用所有未來空間資料。
#XTIT: Create schema popup title
createSchemaDialogTitle=建立開放式 SQL 綱要
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=建立時間表格和維度
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=編輯時間表格和維度
#XTIT: Time Data token title
timeDataTokenTitle=時間資料
#XTIT: Time Data token title
timeDataUpdateViews=更新時間資料檢視
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=建立進行中...
#XFLD: Time Data token creation error label
timeDataCreationError=建立失敗，請再試一次。
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=時間表格設定
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=換算表格
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=時間維度
#XFLD: Time Data dialog time range label
timeRangeHint=定義時間範圍。
#XFLD: Time Data dialog time data table label
timeDataHint=指定表格名稱。
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=指定維度名稱。
#XFLD: Time Data Time range description label
timerangeLabel=時間範圍
#XFLD: Time Data dialog from year label
fromYearLabel=開始年度
#XFLD: Time Data dialog to year label
toYearLabel=結束年度
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=行事曆類型
#XFLD: Time Data dialog granularity label
granularityLabel=精細度
#XFLD: Time Data dialog technical name label
technicalNameLabel=技術名稱
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=季度的換算表格
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=月份的換算表格
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=天數的換算表格
#XFLD: Time Data dialog year label
yearLabel=年度維度
#XFLD: Time Data dialog quarter label
quarterLabel=季度維度
#XFLD: Time Data dialog month label
monthLabel=月份維度
#XFLD: Time Data dialog day label
dayLabel=天數維度
#XFLD: Time Data dialog gregorian calendar type label
gregorian=西曆
#XFLD: Time Data dialog time granularity day label
day=天
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=已達到 1000 個字元的長度上限。
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=最大時間範圍為 150 年。
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=「開始年度」應早於「結束年度」。
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=「開始年度」必須是 1900 或更晚。
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=「結束年度」應晚於「開始年度」。
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=「結束年度」必須低於目前年度加 100 年
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=增加「開始年度」可能會導致資料遺失
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=減少「結束年度」可能會導致資料遺失
#XMSG: Time Data creation validation error message
timeDataValidationError=部份欄位無效，請檢查必要欄位以繼續。
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=您確定要刪除資料嗎?
#XMSG: Time Data creation success message
createTimeDataSuccess=已建立時間資料
#XMSG: Time Data update success message
updateTimeDataSuccess=已更新時間資料
#XMSG: Time Data delete success message
deleteTimeDataSuccess=已刪除時間資料
#XMSG: Time Data creation error message
createTimeDataError=嘗試建立時間資料時發生問題。
#XMSG: Time Data update error message
updateTimeDataError=嘗試更新時間資料時發生問題。
#XMSG: Time Data creation error message
deleteTimeDataError=嘗試刪除時間資料時發生問題。
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=無法載入時間資料。
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=警告
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=由於其他模型已使用您的時間資料，因此無法將其刪除。
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=由於另一模型已使用您的時間資料，因此無法將其刪除。
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=此為必要欄位且不可空白。
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=在資料庫總管中開啟
#YMSE: Dimension Year
dimensionYearView=維度「年度」
#YMSE: Dimension Year
dimensionQuarterView=維度「季度」
#YMSE: Dimension Year
dimensionMonthView=維度「月」
#YMSE: Dimension Year
dimensionDayView=維度「天」
#XFLD: Time Data deletion object title
timeDataUsedIn=(已在 {0} 個模型中使用)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(已在 1 個模型中使用)
#XFLD: Time Data deletion table column provider
provider=提供者
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=相關性
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=建立空間綱要的使用者
#XFLD: Create schema button
createSchemaButton=建立開放式 SQL 綱要
#XFLD: Generate TimeData button
generateTimeDataButton=建立時間表格和維度
#XFLD: Show dependencies button
showDependenciesButton=顯示相關性
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=若要執行此作業，您的使用者必須是空間成員。
#XFLD: Create space schema user button
createSpaceSchemaUserButton=建立空間綱要使用者
#YMSE: API Schema users load error
loadSchemaUsersError=無法載入使用者清單。
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=空間綱要使用者明細
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=您確定要刪除所選使用者嗎？
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=已刪除使用者。
#YMSE: API Schema user deletion error
userDeleteError=無法刪除使用者。
#XFLD: User deleted
userDeleted=已刪除使用者。
#XTIT: Remove user popup title
removeUserConfirmationTitle=警告
#XMSG: Remove user popup text
removeUserConfirmation=您確定要移除使用者嗎？該使用者和其指派的範圍角色將從空間移除。
#XMSG: Remove users popup text
removeUsersConfirmation=您確定要移除使用者嗎？該使用者和其指派的範圍角色將從空間移除。
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=移除
#YMSE: No data text for available roles
noDataAvailableRoles=未將空間新增至範圍角色。\n若要將使用新增至空間，則必須先新增至一或多個範圍角色。
#YMSE: No data text for selected roles
noDataSelectedRoles=沒有所選範圍角色
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=開放式 SQL 綱要組態明細
#XFLD: Label for Read Audit Log
auditLogRead=啟用讀取作業的稽核日誌
#XFLD: Label for Change Audit Log
auditLogChange=啟用更改作業的稽核日誌
#XFLD: Label Audit Log Retention
auditLogRetention=保留日誌：
#XFLD: Label Audit Log Retention Unit
retentionUnit=天
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=請輸入介於 {0} 到 {1} 之間的整數
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=使用空間綱要資料
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=停止使用空間綱要資料
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=此開放式 SQL 綱要可能會使用您空間綱要的資料。若您停止使用，則以空間綱要資料為依據的模型可能再也無法使用。
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=停止使用
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=此空間用來存取資料湖。
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=已啟用資料湖
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=已達到記憶體限制
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=已達到儲存限制
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=已達到最小儲存限制
#XFLD: Space ram tag
ramLimitReachedLabel=已達到記憶體限制
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=已達到最小記憶體限制
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=您已達到指派空間的 {0} 儲存限制。請將更多儲存指派給該空間。
#XFLD: System storage tag
systemStorageLimitReachedLabel=已達到系統儲存限制
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=您已達到 {0} 的系統儲存限制，現在無法將更多儲存指派給該空間。
#XMSG: Schema deletion warning
deleteSchemaConfirmation=刪除此開放式 SQL 綱要也會永久刪除所有綱要中儲存的物件及維護的關聯。是否繼續？
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=已刪除綱要
#YMSE: Error while deleting schema.
schemaDeleteError=無法刪除綱要。
#XMSG: Success message after update a schema
schemaUpdateSuccess=已更新綱要
#YMSE: Error while updating schema.
schemaUpdateError=無法更新綱要。
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=我們已提供此綱要的密碼。若您忘記或遺失密碼，可請求新密碼。請記得複製或儲存新密碼。
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=請複製您的密碼。設定此綱要的連線需要此密碼。若忘記密碼，可開啟此對話將其重設。
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=建立綱要後，便無法更改此名稱。
#XFLD: Open SQL Schemas section sub headline
schemasSQL=開放式 SQL
#XFLD: Space schema section sub headline
schemasSpace=空間
#XFLD: HDI Container section header
HDIContainers=HDI 容器
#XTXT: Add HDI Containers button tooltip
addHDIContainers=新增 HDI 容器
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=移除 HDI 容器
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=啟用存取
#YMSE: No data text for HDI Containers table
noDataHDIContainers=未新增 HDI 容器。
#YMSE: No data text for Timedata section
noDataTimedata=未建立時間表格和維度。
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=由於執行時期資料庫無法使用，因此無法載入表格和維度。
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=由於執行時期資料庫無法使用，因此無法載入 HDI 容器。
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=無法取得 HDI 容器，請稍後再試。
#XFLD Table column header for HDI Container names
HDIContainerName=HDI 容器名稱
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=啟用存取
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=您可在您的 SAP Datasphere 租用戶啟用 SAP SQL Data Warehousing，不需要移動資料即可交換 HDI 容器和 SAP Datasphere 空間之間的資料。
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=按一下下方按鈕建立支援記錄單以執行此動作。
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=記錄單處理後，必須在 SAP Datasphere 執行時期資料庫中建立一或多個新 HDI 容器。接著，在所有 SAP Datasphere 空間的 HDI 容器區段中，「啟用存取」按鈕將由 + 按鈕替代，即可將容器新增至空間。
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=需要更多資訊？請移至 %%0。如需關於要包含在記錄單的詳細資訊，請參閱 %%1。
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP 註記 3057059
#XBUT: Open Ticket Button Text
openTicket=建立記錄單
#XBUT: Add Button Text
add=新增
#XBUT: Next Button Text
next=下一步
#XBUT: Edit Button Text
editUsers=編輯
#XBUT: create user Button Text
createUser=建立
#XBUT: Update user Button Text
updateUser=選擇
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=新增未指派的 HDI 容器
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=找不到未指派的容器。\n您正在尋找的容器可能已指派給空間。
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=無法載入指派的 HDI 容器。
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=無法載入 HDI 容器。
#XMSG: Success message
succeededToAddHDIContainer=已新增 HDI 容器
#XMSG: Success message
succeededToAddHDIContainerPlural=已新增 HDI 容器
#XMSG: Success message
succeededToDeleteHDIContainer=已移除 HDI 容器
#XMSG: Success message
succeededToDeleteHDIContainerPlural=已移除 HDI 容器
#XFLD: Time data section sub headline
timeDataSection=時間表格和維度
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=讀取
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=更改
#XFLD: Remote sources section sub headline
allconnections=連線指派
#XFLD: Remote sources section sub headline
localconnections=本端連線
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=成員指派
#XFLD: User assignment section sub headline
userAssignment=使用者指派
#XFLD: User section Access dropdown Member
member=成員
#XFLD: User assignment section column name
user=使用者名稱
#XTXT: Selected role count
selectedRoleToolbarText=已選擇：{0}
#XTIT: Space detail section connections title
detailsSectionConnections=連線
#XTIT: Space detail section data access title
detailsSectionDataAccess=綱要存取
#XTIT: Space detail section time data title
detailsSectionGenerateData=時間資料
#XTIT: Space detail section members title
detailsSectionUsers=成員
#XTIT: Space detail section Users title
detailsSectionUsersTitle=使用者
#XTIT: Out of Storage
insufficientStoragePopoverTitle=儲存不足
#XTIT: Storage distribution
storageDistributionPopoverTitle=使用的硬碟儲存
#XTXT: Out of Storage popover text
insufficientStorageText=若要建立新空間，請減少其他空間的指派儲存，或刪除不再需要的空間。您可透過呼叫「管理計劃」來增加系統總儲存。
#XMSG: Space id length warning
spaceIdLengthWarning=已超過 {0} 個字元上限。
#XMSG: Space name length warning
spaceNameLengthWarning=已超過 {0} 個字元上限。
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=請勿使用 {0} 前置字元，以避免可能的衝突。
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=無法載入開放式 SQL 綱要。
#YMSE: Error while creating open SQL schema
createLocalSchemaError=無法建立開放式 SQL 綱要。
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=無法載入所有遠端連線。
#YMSE: Error while loading space details
loadSpaceDetailsError=無法載入空間明細。
#YMSE: Error while deploying space details
deploySpaceDetailsError=無法部署空間。
#YMSE: Error while copying space details
copySpaceDetailsError=無法複製空間。
#YMSE: Error while loading storage data
loadStorageDataError=無法載入儲存資料。
#YMSE: Error while loading all users
loadAllUsersError=無法載入所有使用者。
#YMSE: Failed to reset password
resetPasswordError=無法重設密碼。
#XMSG: Success message after updating space
resetPasswordSuccessMessage=已設定綱要的新密碼
#YMSE: DP Agent-name too long
DBAgentNameError=資料提供代理程式名稱過長。
#YMSE: Schema-name not valid.
schemaNameError=綱要名稱無效。
#YMSE: User name not valid.
UserNameError=使用者名稱無效。
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=按儲存類型的使用
#XTIT: Consumption by Schema
consumptionSchemaText=按綱要的使用
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=按綱要的整體表格使用
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=按表格類型的整體使用
#XTIT: Tables
tableDetailsText=表格明細
#XTIT: Table Storage Consumption
tableStorageConsumptionText=表格儲存使用
#XFLD: Table Type label
tableTypeLabel=表格類型
#XFLD: Schema label
schemaLabel=綱要
#XFLD: reset table tooltip
resetTable=重設表格
#XFLD: In-Memory label in space monitor
inMemoryLabel=記憶體
#XFLD: Disk label in space monitor
diskLabel=硬碟
#XFLD: Yes
yesLabel=是
#XFLD: No
noLabel=否
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=您要讓此空間中的資料按預設可供使用嗎？
#XFLD: Business Name
businessNameLabel=業務名稱
#XFLD: Refresh
refresh=重新整理
#XMSG: No filter results title
noFilterResultsTitle=您的篩選設定未顯示資料。
#XMSG: No filter results message
noFilterResultsMsg=請嘗試改善篩選設定，若仍看不到資料，請在資料模型建立器中建立表格。這些表格使用儲存後，您便可在此處進行監控。
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=執行時期資料庫無法使用。
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=由於執行時期資料庫無法使用，因此特定功能已停用，且無法在此頁面上顯示資訊。
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=無法建立空間綱要使用者。
#YMSE: Error User name already exists
userAlreadyExistsError=使用者名稱已存在。
#YMSE: Error Authentication failed
authenticationFailedError=驗證失敗。
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=由於多次登入失敗，因此已鎖定該使用者。請求新密碼以將使用者解除鎖定。
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=已設定新密碼並解除鎖定使用者
#XMSG: user is locked message
userLockedMessage=使用者已鎖定。
#XCOL: Users table-view column Role
spaceRole=角色
#XCOL: Users table-view column Scoped Role
spaceScopedRole=範圍角色
#XCOL: Users table-view column Space Admin
spaceAdmin=空間管理員
#XFLD: User section dropdown value Viewer
viewer=檢視器
#XFLD: User section dropdown value Modeler
modeler=模型器
#XFLD: User section dropdown value Data Integrator
dataIntegrator=資料整合器
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=空間管理員
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=已更新空間角色
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=未成功更新空間角色。
#XFLD:
databaseUserNameSuffix=資料庫使用名稱後置字元
#XTXT: Space Schema password text
spaceSchemaPasswordText=請複製密碼以建立此綱要的連線。若忘記密碼，一律可請求新密碼。
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=為了透過此使用者建立存取，請啟用使用和複製資格證明。若您只要複製資格證明而不需密碼，請確保稍後會新增密碼。
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=啟用 Cloud Platform 中的使用
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=使用者提供的服務資格證明：
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=使用者提供的服務資格證明 (無密碼)：
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=複製資格證明 (無密碼)
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=複製完整資料證明
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=複製密碼
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=已將資格證明複製到剪貼簿
#XMSG: Password copied to clipboard
passwordCopiedMessage=已將密碼複製到剪貼簿
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=建立資料庫使用者
#XMSG: Database Users section title
databaseUsers=資料庫使用者
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=資料庫使用者明細
#XFLD: database user read audit log
databaseUserAuditLogRead=啟用讀取作業和保留日誌的稽核日誌：
#XFLD: database user change audit log
databaseUserAuditLogChange=啟用更改作業和保留日誌的稽核日誌：
#XMSG: Cloud Platform Access
cloudPlatformAccess=Cloud Platform 存取
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=透過此資料庫使用者建立 HANA 部署基礎架構 (HDI) 的存取。若要連線至 HDI 容器，必須開啟 SQL 模型化
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=啟用 HDI 使用
#XFLD: Enable Consumption hint
enableConsumptionHint=您要讓其他工作或應用程式使用空間中的資料嗎？
#XFLD: Enable Consumption
enableConsumption=啟用 SQL 使用
#XFLD: Enable Modeling
enableModeling=啟用 SQL 模型化
#XMSG: Privileges for Data Modeling
privilegesModeling=資料擷取
#XMSG: Privileges for Data Consumption
privilegesConsumption=外部工具的資料使用
#XFLD: SQL Modeling
sqlModeling=SQL 模型化
#XFLD: SQL Consumption
sqlConsumption=SQL 使用
#XFLD: enabled
enabled=已啟用
#XFLD: disabled
disabled=已停用
#XFLD: Edit Privileges
editPrivileges=編輯權限
#XFLD: Open Database Explorer
openDBX=開啟資料庫總管
#XFLD: create database user hint
databaseCreateHint=請注意，儲存後將無法再更改使用者名稱。
#XFLD: Internal Schema Name
internalSchemaName=內部綱要名稱
#YMSE: Failed to load database users
loadDatabaseUserError=無法載入資料庫使用者
#YMSE: Failed to delete database user
deleteDatabaseUsersError=無法刪除資料庫使用者
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=已刪除資料庫使用者
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=已刪除資料庫使用者
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=已建立資料庫使用者
#YMSE: Failed to create database user
createDatabaseUserError=無法建立資料庫使用者
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=已更新資料庫使用者
#YMSE: Failed to update database user
updateDatabaseUserError=無法更新資料庫使用者
#XFLD: HDI Consumption
hdiConsumption=HDI 使用
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=資料庫存取
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=讓您的空間資料按預設可供使用。建立器中的模型將自動允許資料可供使用。
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=空間資料預設使用：
#XFLD: Database User Name
databaseUserName=資料庫使用名稱
#XMSG: Database User creation validation error message
databaseUserValidationError=部份欄位無效，請檢查必要欄位以繼續。
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=由於此使用者已移轉，因此無法啟用資料擷取。
#XBUT: Remove Button Text
remove=移除
#XBUT: Remove Spaces Button Text
removeSpaces=移除空間
#XBUT: Remove Objects Button Text
removeObjects=移除物件
#XMSG: No members have been added yet.
noMembersAssigned=尚未新增成員。
#XMSG: No users have been added yet.
noUsersAssigned=尚未新增使用者。
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=未建立資料庫使用者，或篩選未顯示資料。
#XMSG: Please enter a user name.
noDatabaseUsername=請輸入使用者名稱。
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=使用者名稱過長，請使用較短的名稱。
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=未啟用權限，此資料庫使用者將只具有限功能。您仍要繼續嗎？
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=若要啟用更改作業的稽核日誌，則必須也啟用資料擷取。您要執行此動作嗎？
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=若要啟用讀取作業的稽核日誌，則必須也啟用資料擷取。您要執行此動作嗎？
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=若要啟用 HDI 使用，則必須也啟用資料擷取和資料使用。您要執行此動作嗎？
#XMSG:
databaseUserPasswordText=請複製密碼以建立此資料庫使用者的連線。若忘記密碼，一律可請求新密碼。
#XTIT: Space detail section members title
detailsSectionMembers=成員
#XMSG: New password set
newPasswordSet=已設定新密碼
#XFLD: Data Ingestion
dataIngestion=資料擷取
#XFLD: Data Consumption
dataConsumption=資料使用
#XFLD: Privileges
privileges=權限
#XFLD: Enable Data ingestion
enableDataIngestion=啟用資料擷取
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=日誌記錄資料擷取的讀取和更改作業。
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=讓您的空間資料可在 HDI 容器中使用。
#XFLD: Enable Data consumption
enableDataConsumption=啟用資料使用
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=允許其他應用程式或工具使用您的空間資料。
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=為了透過此資料庫使用者建立存取，請將資格證明複製到使用者提供的服務。若只要複製資格證明而不需密碼，請確保稍後會新增密碼。
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=資料流程執行時期容量 ({0}：{1} 小時/{2} 小時)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=無法載入資料流程執行時期容量
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=使用者可將資料使用權限授予給其他使用者。
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=啟用含授予選項的資料使用
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=若要啟用含授予選項的資料使用，則必須啟用資料使用。您要啟用兩者嗎？
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=啟用自動化預測程式庫 (APL) 和預測分析程式庫 (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=使用者可使用 SAP HANA Cloud 嵌入式機器學習功能。
#XFLD: Password Policy
passwordPolicy=密碼政策
#XMSG: Password Policy
passwordPolicyHint=在此啟用或停用組態的密碼政策。
#XFLD: Enable Password Policy
enablePasswordPolicy=啟用密碼政策
#XMSG: Read Access to the Space Schema
readAccessTitle=讀取存取空間綱要
#XMSG: read access hint
readAccessHint=允許資料庫使用者將外部工具連線至空間綱要，以及讀取公開使用的檢視。
#XFLD: Space Schema
spaceSchema=空間綱要
#XFLD: Enable Read Access (SQL)
enableReadAccess=啟用讀取存取 (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=允許使用者將讀取存取授予給其他使用者。
#XFLD: With Grant Option
withGrantOption=含授予選項
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=讓您的空間資料可在 HDI 容器中使用。
#XFLD: Enable HDI Consumption
enableHDIConsumption=啟用 HDI 使用
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=寫入存取至使用者的開放式 SQL 綱要
#XMSG: write access hint
writeAccessHint=允許資料庫使用者將外部工具連線至使用者的開放式 SQL 綱要，以建立資料實體和擷取資料用於空間中。
#XFLD: Open SQL Schema
openSQLSchema=開放式 SQL 綱要
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=啟用寫入存取 (SQL、DDL 以及 DML)
#XMSG: audit hint
auditHint=日誌記錄開放式 SQL 綱要中的讀取和更改作業。
#XMSG: data consumption hint
dataConsumptionHint=按預設在空間中公開所有新檢視以供使用。模型器可透過檢視輸出側邊面板的「公開使用」，覆寫個別檢視的此設定。您也可選擇公開檢視的格式。
#XFLD: Expose for Consumption by Default
exposeDataConsumption=按預設公開使用
#XMSG: database users hint consumption hint
databaseUsersHint2New=建立資料庫使用者以將外部工具連線至 SAP Datasphere。設定權限允許使用者讀取空間資料，並可建立資料實體 (DDL) 及擷取資料 (DML) 以在空間中使用。
#XFLD: Read
read=讀取
#XFLD: Read (HDI)
readHDI=讀取 (HDI)
#XFLD: Write
write=寫入
#XMSG: HDI Containers Hint
HDIContainersHint2=啟用存取空間中的 SAP HANA Deployment Infrastructure (HDI) 容器。模型器可將 HDI Artifact 作為檢視來源使用，而 HDI 用戶端可存取空間資料。
#XMSG: Open info dialog
openDatabaseUserInfoDialog=開啟資訊對話
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=資料庫使用者已鎖定。請開啟對話以解除鎖定
#XFLD: Table
table=表格
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=夥伴連線
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=夥伴連線組態
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=新增 iFrame URL 和圖示來定義您自有的夥伴連線功能磚。此組態僅適用於此租用戶。
#XFLD: Table Name Field
partnerConnectionConfigurationName=功能磚名稱
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=iFrame Post Message 來源
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=圖示
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=找不到夥伴連線組態。
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=執行時期資料庫無法使用時，無法顯示夥伴連線組態。
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=建立夥伴連線組態
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=上傳圖示
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=選擇 (大小上限 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=夥伴功能磚範例
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=瀏覽
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=已成功建立夥伴連線組態。
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=刪除夥伴連線組態時發生錯誤。
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=已成功刪除夥伴連線組態。
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=檢索夥伴連線設定時發生錯誤。
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=由於檔案超過 200KB 的大小上限，因此無法上傳。
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=建立夥伴連線組態
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=刪除夥伴連線組態。
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=無法建立夥伴功能磚。
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=無法刪除夥伴功能磚。
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=重設客戶 SAP HANA Cloud 連接器設定失敗
#XFLD: Workload Class
workloadClass=工作負荷類別
#XFLD: Workload Management
workloadManagement=工作負荷管理
#XFLD: Priority
workloadClassPriority=優先順序
#XMSG:
workloadManagementPriorityHint=查詢資料庫時，您可指定此空間的優先順序。輸入從 1 (最低優先順序) 至 8 (最高優先順序) 的值。在空間競爭可用執行緒的情況下，具有較高優先順序的空間，將在較低優先順序前先執行。
#XMSG:
workloadClassPriorityHint=您可指定從 0 (最低) 到 8 (最高) 的空間優先順序。將於具有較低優先順序的其他空間陳述式之前，先執行具有高優先順序的空間陳述式。預設優先順序為 5。由於值 9 保留給系統作業，因此無法用於空間。
#XFLD: Statement Limits
workloadclassStatementLimits=陳述式限制
#XFLD: Workload Configuration
workloadConfiguration=工作負荷組態
#XMSG:
workloadClassStatementLimitsHint=您可指定同時在空間中執行陳述式的執行緒和記憶體 GB 上限 (或百分比)。您可輸入介於 0 (無限制) 和租用戶中可用的總計記憶體和執行緒之間的值或百分比。\n\n若指定執行緒限制，請注意效能可能較低。\n\n若指定記憶體限制，達到記憶體限制的陳述式將不會執行。
#XMSG:
workloadClassStatementLimitsDescription=預設組態提供大量資源限制，防止單一空間與系統重疊。
#XMSG:
workloadClassStatementLimitCustomDescription=您可設定目前在空間中執行的陳述式，可使用的最大總計執行緒和記憶體限制。
#XMSG:
totalStatementThreadLimitHelpText=將執行緒限制設定過低可能影響陳述式效能，而過高值或 0 可允許空間使用所有可用的系統執行緒。
#XMSG:
totalStatementMemoryLimitHelpText=將記憶體限制設定過低可能造成記憶體不足問題，而過高值或 0 可允許空間使用所有可用的系統記憶體。
#XMSG:
totalStatementThreadLimitHelpTextRestricted=請輸入您租用戶中可用執行緒總數 1% 和 70% 之間的百分比 (或同等數字)。將執行緒限制設定過低可能影響陳述式效能，而過高值可能影響其他空間中的陳述式效能。
#XMSG:
totalStatementThreadLimitHelpTextDynamic=請輸入您租用戶中可用執行緒總數 1% 和 {0}% 之間的百分比 (或同等數字)。將執行緒限制設定過低可能影響陳述式效能，而過高值可能影響其他空間中的陳述式效能。
#XMSG:
totalStatementMemoryLimitHelpTextNew=請輸入值或 0 (無限制) 到租用戶中可用的記憶體總數之間的百分比。將記憶體限制設定過低可能影響陳述式效能，而過高值可能影響其他空間中的陳述式效能。
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=總計陳述式執行緒限制
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=執行緒
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=總計陳述式記憶體限制
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=無法載入客戶 SAP HANA 資訊。
#XMSG:
minimumLimitReached=已達到限制下限。
#XMSG:
maximumLimitReached=已達到限制上限。
#XMSG: Name Taken for Technical Name
technical-name-taken=具有您輸入技術名稱的連線已存在。請輸入其他名稱。
#XMSG: Name Too long for Technical Name
technical-name-too-long=您輸入的技術名稱超過 40 個字元。請輸入較少字元的名稱。
#XMSG: Technical name field empty
technical-name-field-empty=請輸入技術名稱。
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=您僅可針對名稱使用字母 (a-z)、數字 (0-9) 和底線 (_)。
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=您輸入的名稱開頭或結尾不能是底線 (_)。
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=啟用陳述式限制
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=設定
#XMSG: Connections tool hint in Space details section
connectionsToolHint=若要建立或編輯連線，請開啟側邊瀏覽的「連線」應用程式，或按一下此處：
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=移至連線
#XFLD: Not deployed label on space tile
notDeployedLabel=尚未部署空間。
#XFLD: Not deployed additional text on space tile
notDeployedText=請部署空間。
#XFLD: Corrupt space label on space tile
corruptSpace=發生錯誤。
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=請嘗試重新部署或聯絡支援
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=稽核日誌資料
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=管理資料
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=其他資料
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=空間中的資料
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=您確定要解除鎖定空間嗎？
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=您確定要鎖定空間嗎？
#XFLD: Lock
lock=鎖定
#XFLD: Unlock
unlock=解除鎖定
#XFLD: Locking
locking=鎖定
#XMSG: Success message after locking space
lockSpaceSuccessMsg=空間已鎖定
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=空間已解除鎖定
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=空間已鎖定
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=空間已解除鎖定
#YMSE: Error while locking a space
lockSpaceError=無法鎖定空間。
#YMSE: Error while unlocking a space
unlockSpaceError=無法解除鎖定空間。
#XTIT: popup title Warning
confirmationWarningTitle=警告
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=已手動鎖定空間。
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=由於稽核日誌使用大量磁碟 GB，因此系統已鎖定空間。
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=由於空間超過記憶體或硬碟儲存的分配，因此系統已將其鎖定。
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=您確定要解除鎖定所選空間嗎？
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=您確定要鎖定所選空間嗎？
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=範圍角色編輯器
#XTIT: ECN Management title
ecnManagementTitle=空間和彈性計算節點管理
#XFLD: ECNs
ecns=彈性計算節點
#XFLD: ECN phase Ready
ecnReady=就緒
#XFLD: ECN phase Running
ecnRunning=正在執行中
#XFLD: ECN phase Initial
ecnInitial=未就緒
#XFLD: ECN phase Starting
ecnStarting=正在開始
#XFLD: ECN phase Starting Failed
ecnStartingFailed=開始失敗
#XFLD: ECN phase Stopping
ecnStopping=正在停止
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=停止失敗
#XBTN: Assign Button
assign=指派空間
#XBTN: Start Header-Button
start=開始
#XBTN: Update Header-Button
repair=更新
#XBTN: Stop Header-Button
stop=停止
#XFLD: ECN hours remaining
ecnHoursRemaining=剩餘 1000 小時
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=剩餘 {0} 個區塊時數
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=剩餘 {0} 個區塊時數
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=建立彈性計算節點
#XTIT: ECN edit dialog title
ecnEditDialogTitle=編輯彈性計算節點
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=刪除彈性計算節點
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=指派空間
#XFLD: ECN ID
ECNIDLabel=彈性計算節點
#XTXT: Selected toolbar text
selectedToolbarText=已選擇：{0}
#XTIT: Elastic Compute Nodes
ECNslong=彈性計算節點
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=物件數量
#XTIT: Object assignment - Dialog header text
selectObjects=選擇您要指派給彈性計算節點的空間和物件：
#XTIT: Object assignment - Table header title: Objects
objects=物件
#XTIT: Object assignment - Table header: Type
type=類型
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=請注意，刪除資料庫使用者將刪除所有產生的稽核日誌輸入項。若要保留稽核日誌，請考慮匯出再刪除資料庫使用者。
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=請注意，取消指派空間的 HDI 容器將刪除所有產生的稽核日誌輸入項。若要保留稽核日誌，請考慮匯出再取消指派 HDI 容器。
#XTXT: All audit logs
allAuditLogs=針對空間產生的所有稽核日誌輸入項
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=請注意，停用稽核政策 (讀取或更改作業) 將刪除所有其稽核日誌輸入項。若要保留稽核日誌輸入項，請考慮匯出再停用稽核政策。
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=尚未指派空間或物件
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=將空間或物件指派給彈性計算節點，才能開始使用。
#XTIT: No Spaces Illustration title
noSpacesTitle=尚未建立空間
#XTIT: No Spaces Illustration description
noSpacesDescription=若要開始取得資料，請先建立空間。
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=資源回收桶為空
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=您可以在此復原已刪除的空間。
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=部署空間後，下列資料庫使用者將{0}刪除且無法復原：
#XTIT: Delete database users
deleteDatabaseUsersTitle=刪除資料庫使用者
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID 已存在。
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=請只使用小寫字元 a - z 和數字 0 - 9
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID 必須至少為 {0} 個字元長。
#XMSG: ecn id length warning
ecnIdLengthWarning=已超過 {0} 個字元上限。
#XFLD: open System Monitor
systemMonitor=系統監控器
#XFLD: open ECN schedule dialog menu entry
schedule=排程
#XFLD: open create ECN schedule dialog
createSchedule=建立排程
#XFLD: open change ECN schedule dialog
changeSchedule=編輯排程
#XFLD: open delete ECN schedule dialog
deleteSchedule=刪除排程
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=將排程指派給我
#XFLD: open pause ECN schedule dialog
pauseSchedule=暫停排程
#XFLD: open resume ECN schedule dialog
resumeSchedule=繼續排程
#XFLD: View Logs
viewLogs=檢視日誌
#XFLD: Compute Blocks
computeBlocks=計算區塊
#XFLD: Memory label in ECN creation dialog
ecnMemory=記憶體 (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=儲存 (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=CPU 數量
#XFLD: ECN updated by label
changedBy=更改者
#XFLD: ECN updated on label
changedOn=更改日期
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=已建立彈性計算節點
#YMSE: Error while creating a Elastic Compute Node
createEcnError=無法建立彈性計算節點
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=已更新彈性計算節點
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=無法更新彈性計算節點
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=已刪除彈性計算節點
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=無法刪除彈性計算節點
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=開始彈性計算節點
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=停止彈性計算節點
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=無法開始彈性計算節點
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=無法停止彈性計算節點
#XBUT: Add Object button for an ECN
assignObjects=新增物件
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=自動新增所有物件
#XFLD: object type label to be assigned
objectTypeLabel=類型 (語意使用)
#XFLD: assigned object type label
assignedObjectTypeLabel=類型
#XFLD: technical name label
TechnicalNameLabel=技術名稱
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=選擇您要新增至彈性計算節點的物件。
#XTIT: Add objects dialog title
assignObjectsTitle=指派物件
#XFLD: object label with object count
objectLabel=物件
#XMSG: No objects available to add message.
noObjectsToAssign=沒有可指派的物件。
#XMSG: No objects assigned message.
noAssignedObjects=未指派物件。
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=警告
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=刪除
#XMSG: Remove objects popup text
removeObjectsConfirmation=您確定要移除所選物件嗎？
#XMSG: Remove spaces popup text
removeSpacesConfirmation=您確定要移除所選空間嗎？
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=移除空間
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=已移除公開的物件
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=已指派公開的物件
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=所有公開的物件
#XFLD: Spaces tab label
spacesTabLabel=空間
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=公開的物件
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=已移除空間
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=已移除空間
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=無法指派或移除空間。
#YMSE: Error while removing objects
removeObjectsError=無法指派或移除物件。
#YMSE: Error while removing object
removeObjectError=無法指派或移除物件。
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=先前選擇的數字不再有效。請選擇有效數字。
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=請選擇有效的效能類別。
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=先前選擇的效能類別 "{0}" 目前無效。請選擇有效的效能類別。
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=您確定要刪除彈性計算節點嗎？
#XFLD: tooltip for ? button
help=輔助說明
#XFLD: ECN edit button label
editECN=組態
#XFLD: Technical type label for ERModel
DWC_ERMODEL=實體 - 關係模型
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=本端表格
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=遠端表格
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=分析模型
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=工作細項鏈
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=資料流程
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=複製流程
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=轉換流程
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=智慧查詢
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=儲藏庫
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=企業搜尋
#XFLD: Technical type label for View
DWC_VIEW=檢視
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=資料產品
#XFLD: Technical type label for Data Access Control
DWC_DAC=資料存取控制
#XFLD: Technical type label for Folder
DWC_FOLDER=資料夾
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=業務實體
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=業務實體變式
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=責任情境
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=事實資料模型
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=層面
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=使用模型
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=遠端連線
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=事實資料模型變式
#XMSG: Schedule created alert message
createScheduleSuccess=已建立排程
#XMSG: Schedule updated alert message
updateScheduleSuccess=已更新排程
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=已刪除排程
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=已將排程指派給您
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=正在暫停 1 個排程
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=正在繼續 1 個排程
#XFLD: Segmented button label
availableSpacesButton=可用
#XFLD: Segmented button label
selectedSpacesButton=已選擇
#XFLD: Visit website button text
visitWebsite=造訪網站
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=將移除先前選擇的來源語言。
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=啟用
#XFLD: ECN performance class label
performanceClassLabel=效能類別
#XTXT performance class memory text
memoryText=記憶體
#XTXT performance class compute text
computeText=計算
#XTXT performance class high-compute text
highComputeText=高計算
#XBUT: Recycle Bin Button Text
recycleBin=資源回收筒
#XBUT: Restore Button Text
restore=還原
#XMSG: Warning message for new Workload Management UI
priorityWarning=此區域為唯讀。您可在系統/組態/工作負荷管理區域中，更改空間優先順序。
#XMSG: Warning message for new Workload Management UI
workloadWarning=此區域為唯讀。您可在系統/組態/工作負荷管理區域中，更改空間工作負荷組態。
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPU
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark 記憶體 (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=資料產品擷取
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=由於空間目前正在部署中，因此資料無法使用
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=由於空間目前正在載入中，因此資料無法使用
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=編輯事例對映
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
