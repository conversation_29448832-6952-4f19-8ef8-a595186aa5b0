#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Мониторинг
#XTXT: Type name for spaces in browser tab page title
space=Пространство
#_____________________________________
#XFLD: Spaces label in
spaces=Пространства
#XFLD: Manage plan button text
manageQuotaButtonText=Управление планом
#XBUT: Manage resources button
manageResourcesButton=Управление ресурсами
#XFLD: Create space button tooltip
createSpace=Создать пространство
#XFLD: Create
create=Создать
#XFLD: Deploy
deploy=Развернуть
#XFLD: Page
page=Страница
#XFLD: Cancel
cancel=Отменить
#XFLD: Update
update=Обновить
#XFLD: Save
save=Сохранить
#XFLD: OK
ok=ОК
#XFLD: days
days=дн.
#XFLD: Space tile edit button label
edit=Редактировать
#XFLD: Auto Assign all objects to space
autoAssign=Автоматическое присвоение
#XFLD: Space tile open monitoring button label
openMonitoring=Монитор
#XFLD: Delete
delete=Удалить
#XFLD: Copy Space
copy=Скопировать
#XFLD: Close
close=Закрыть
#XCOL: Space table-view column status
status=Статус
#XFLD: Space status active
activeLabel=Активно
#XFLD: Space status locked
lockedLabel=Блокировано
#XFLD: Space status critical
criticalLabel=Критично
#XFLD: Space status cold
coldLabel=Холодный
#XFLD: Space status deleted
deletedLabel=Удалено
#XFLD: Space status unknown
unknownLabel=Неизвестно
#XFLD: Space status ok
okLabel=ОК
#XFLD: Database user expired
expired=Истекло
#XFLD: deployed
deployed=Развернуто
#XFLD: not deployed
notDeployed=Не развернуто
#XFLD: changes to deploy
changesToDeploy=Изменения для развертывания
#XFLD: pending
pending=Развертывание
#XFLD: designtime error
designtimeError=Ошибка времени дизайна
#XFLD: runtime error
runtimeError=Ошибка выполнения
#XFLD: Space created by label
createdBy=Создал
#XFLD: Space created on label
createdOn=Дата создания
#XFLD: Space deployed on label
deployedOn=Дата развертывания
#XFLD: Space ID label
spaceID=Ид. пространства
#XFLD: Priority label
priority=Приоритет
#XFLD: Space Priority label
spacePriority=Приоритет пространства
#XFLD: Space Configuration label
spaceConfiguration=Конфигурация пространства
#XFLD: Not available
notAvailable=Недоступно
#XFLD: WorkloadType default
default=По умолчанию
#XFLD: WorkloadType custom
custom=Пользоват.
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Доступ к озеру данных
#XFLD: Translation label
translationLabel=Перевод
#XFLD: Source language label
sourceLanguageLabel=Исходный язык
#XFLD: Translation CheckBox label
translationCheckBox=Активировать перевод
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Разверните пространство для доступа к сведениям о пользователе.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Разверните пространство, чтобы открыть проводник базы данных.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Это пространство нельзя использовать для доступа к озеру данных, так как его уже использует другое пространство.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Используйте это пространство для доступа к озеру данных.
#XFLD: Space Priority minimum label extension
low=Высокий
#XFLD: Space Priority maximum label extension
high=Низкий
#XFLD: Space name label
spaceName=Имя пространства
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Развернуть объекты
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Скопировать {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(не выбрано)
#XTXT Human readable text for language code "af"
af=Африкаанс
#XTXT Human readable text for language code "ar"
ar=Арабский
#XTXT Human readable text for language code "bg"
bg=Болгарский
#XTXT Human readable text for language code "ca"
ca=Каталанский
#XTXT Human readable text for language code "zh"
zh=Китайский упрощенный
#XTXT Human readable text for language code "zf"
zf=Китайский
#XTXT Human readable text for language code "hr"
hr=Хорватский
#XTXT Human readable text for language code "cs"
cs=Чешский
#XTXT Human readable text for language code "cy"
cy=Валлийский
#XTXT Human readable text for language code "da"
da=Датский
#XTXT Human readable text for language code "nl"
nl=Нидерландский
#XTXT Human readable text for language code "en-UK"
en-UK=Английский (Великобритания)
#XTXT Human readable text for language code "en"
en=Английский (США)
#XTXT Human readable text for language code "et"
et=Эстонский
#XTXT Human readable text for language code "fa"
fa=Персидский
#XTXT Human readable text for language code "fi"
fi=Финский
#XTXT Human readable text for language code "fr-CA"
fr-CA=Французский (Канада)
#XTXT Human readable text for language code "fr"
fr=Французский
#XTXT Human readable text for language code "de"
de=Немецкий
#XTXT Human readable text for language code "el"
el=Греческий
#XTXT Human readable text for language code "he"
he=Иврит
#XTXT Human readable text for language code "hi"
hi=Хинди
#XTXT Human readable text for language code "hu"
hu=Венгерский
#XTXT Human readable text for language code "is"
is=Исландский
#XTXT Human readable text for language code "id"
id=Индонезийский
#XTXT Human readable text for language code "it"
it=Итальянский
#XTXT Human readable text for language code "ja"
ja=Японский
#XTXT Human readable text for language code "kk"
kk=Казахский
#XTXT Human readable text for language code "ko"
ko=Корейский
#XTXT Human readable text for language code "lv"
lv=Латышский
#XTXT Human readable text for language code "lt"
lt=Литовский
#XTXT Human readable text for language code "ms"
ms=Малайский
#XTXT Human readable text for language code "no"
no=Норвежский
#XTXT Human readable text for language code "pl"
pl=Польский
#XTXT Human readable text for language code "pt"
pt=Португальский (Бразилия)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Португальский (Португалия)
#XTXT Human readable text for language code "ro"
ro=Румынский
#XTXT Human readable text for language code "ru"
ru=Русский
#XTXT Human readable text for language code "sr"
sr=Сербский
#XTXT Human readable text for language code "sh"
sh=Сербохорватский
#XTXT Human readable text for language code "sk"
sk=Словацкий
#XTXT Human readable text for language code "sl"
sl=Словенский
#XTXT Human readable text for language code "es"
es=Испанский
#XTXT Human readable text for language code "es-MX"
es-MX=Испанский (Мексика)
#XTXT Human readable text for language code "sv"
sv=Шведский
#XTXT Human readable text for language code "th"
th=Тайский
#XTXT Human readable text for language code "tr"
tr=Турецкий
#XTXT Human readable text for language code "uk"
uk=Украинский
#XTXT Human readable text for language code "vi"
vi=Вьетнамский
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Удалить пространства
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Действительно переместить пространство "{0}" в корзину?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Действительно переместить выбранные пространства ({0}) в корзину?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Действительно удалить пространство "{0}"? Эта операция необратима.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Действительно удалить выбранные пространства ({0})? Эта операция необратима. Следующий контент будет {1} удален:
#XTXT: permanently
permanently=безвозвратно
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Следующий контент будет {0} удален без возможности восстановления:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Введите {0}, чтобы подтвердить удаление.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Проверьте правописание и повторите попытку.
#XTXT: All Spaces
allSpaces=Все пространства
#XTXT: All data
allData=Все объекты и данные в пространстве
#XTXT: All connections
allConnections=Все соединения, определенные в пространстве
#XFLD: Space tile selection box tooltip
clickToSelect=Щелкните для выбора
#XTXT: All database users
allDatabaseUsers=Все объекты и данные во всех схемах Open SQL, связанных с пространством
#XFLD: remove members button tooltip
deleteUsers=Удалить участников
#XTXT: Space long description text
description=Описание (максимум 4000 символов)
#XFLD: Add Members button tooltip
addUsers=Добавить участников
#XFLD: Add Users button tooltip
addUsersTooltip=Добавить пользователей
#XFLD: Edit Users button tooltip
editUsersTooltip=Редактировать пользователей
#XFLD: Remove Users button tooltip
removeUsersTooltip=Удалить пользователей
#XFLD: Searchfield placeholder
filter=Поиск
#XCOL: Users table-view column health
health=Состояние
#XCOL: Users table-view column access
access=Доступ
#XFLD: No user found nodatatext
noDataText=Пользователь не найден
#XTIT: Members dialog title
selectUserDialogTitle=Добавить участников
#XTIT: User dialog title
addUserDialogTitle=Добавить пользователей
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Удалить соединения
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Удалить соединение
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Действительно удалить выбранные соединения? Они будут удалены безвозвратно.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Выбрать соединения
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Открыть доступ к соединению
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Соединения с открытым доступом
#XFLD: Add remote source button tooltip
addRemoteConnections=Добавить соединения
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Удалить соединения
#XFLD: Share remote source button tooltip
shareConnections=Открыть доступ к соединениям
#XFLD: Tile-layout tooltip
tileLayout=Формат плитки
#XFLD: Table-layout tooltip
tableLayout=Формат таблицы
#XMSG: Success message after creating space
createSpaceSuccessMessage=Пространство создано
#XMSG: Success message after copying space
copySpaceSuccessMessage=Копирование пространства "{0}" в пространство "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Развертывание пространства начато
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Обновление Apache Spark начато
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Не удалось обновить Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Сведения о пространстве обновлены
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Пространство временно разблокировано
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Пространство удалено
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Пространства удалены
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Пространство восстановлено
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Пространства восстановлены
#YMSE: Error while updating settings
updateSettingsFailureMessage=Не удалось обновить настройки пространства.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Озеро данных уже присвоено другому пространству. К озеру данных может иметь доступ только одно пространство.
#YMSE: Error while updating data lake option
virtualTablesExists=Нельзя отменить присвоение озера данных этому пространству, так как еще есть зависимости от виртуальных таблиц*. Удалите виртуальные таблицы, чтобы отменить присвоение озера данных этому пространству.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Не удалость разблокировать пространство.
#YMSE: Error while creating space
createSpaceError=Не удалось создать пространство.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Пространство с именем {0} уже есть.
#YMSE: Error while deleting a single space
deleteSpaceError=Не получилось удалить пространство.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Ваше пространство "{0}" работает неправильно. Попробуйте удалить его снова. Если проблема не решится, обратитесь к администратору с просьбой удалить ваше пространство или создайте сервисный запрос.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Не получилось удалить данные пространства в файлах.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Не получилось удалить пользователей.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Не получилось удалить схемы.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Не получилось удалить соединения.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Не получилось удалить данные пространства.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Не получилось удалить пространства.
#YMSE: Error while restoring a single space
restoreSpaceError=Не получилось восстановить пространство.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Не получилось восстановить пространства.
#YMSE: Error while creating users
createUsersError=Не удалось добавить пользователей.
#YMSE: Error while removing users
removeUsersError=Не получилось удалить пользователей.
#YMSE: Error while removing user
removeUserError=Не получилось удалить пользователя.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Не удалось добавить пользователя в выбранную роль в объеме. \n\n Вы не можете добавить себя в роль в объеме. Вы можете попросить администратора добавить вас в роль в объеме.
#YMSE: Error assigning user to the space
userAssignError=Не удалось присвоить пользователя пространству. \n\n Пользователь уже присвоен максимально разрешенному числу пространств (100) по всем ролям в объеме.
#YMSE: Error assigning users to the space
usersAssignError=Не удалось присвоить пользователей пространству. \n\n Пользователь уже присвоен максимально разрешенному числу пространств (100) по всем ролям в объеме.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Не удалось вызвать пользователей. Повторите попытку позднее.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Не удалось вызвать роли в объеме.
#YMSE: Error while fetching members
fetchUserError=Не удалось вызвать участников. Повторите попытку позднее.
#YMSE: Error while loading run-time database
loadRuntimeError=Не удалось загрузить информацию из динамической базы данных.
#YMSE: Error while loading spaces
loadSpacesError=Возникла проблема при вызове пространств.
#YMSE: Error while loading haas resources
loadStorageError=Возникла проблема при вызове данных памяти.
#YMSE: Error no data could be loaded
loadDataError=Возникла проблема при вызове данных.
#XFLD: Click to refresh storage data
clickToRefresh=Щелкните здесь, чтобы повторить попытку.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Удалить пространство
#XCOL: Spaces table-view column name
name=Имя
#XCOL: Spaces table-view deployment status
deploymentStatus=Статус развертывания
#XFLD: Disk label in space details
storageLabel=Диск (ГБ)
#XFLD: In-Memory label in space details
ramLabel=Память (ГБ)
#XFLD: Memory label on space card
memory=Память для данных
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Память пространства
#XFLD: Storage Type label in space details
storageTypeLabel=Тип памяти
#XFLD: Enable Space Quota
enableSpaceQuota=Активировать квоту пространства
#XFLD: No Space Quota
noSpaceQuota=Нет квоты пространства
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=База данных SAP HANA (диск и in-memory)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Файлы озера данных SAP HANA 
#XFLD: Available scoped roles label
availableRoles=Доступные роли в объеме
#XFLD: Selected scoped roles label
selectedRoles=Выбранные роли в объеме
#XCOL: Spaces table-view column models
models=Модели
#XCOL: Spaces table-view column users
users=Пользователи
#XCOL: Spaces table-view column connections
connections=Соединения
#XFLD: Section header overview in space detail
overview=Обзор
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Приложения
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Присвоение задач 
#XFLD: vCPU label in Apache Spark section
vCPULabel=Виртуальные ЦП
#XFLD: Memory label in Apache Spark section
memoryLabel=Память (ГБ)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Конфигурация пространства
#XFLD: Space Source label
sparkApplicationLabel=Приложение
#XFLD: Cluster Size label
clusterSizeLabel=Размер кластера
#XFLD: Driver label
driverLabel=Драйвер
#XFLD: Executor label
executorLabel=Исполнитель
#XFLD: max label
maxLabel=Макс. использовано
#XFLD: TrF Default label
trFDefaultLabel=Значение по умолчанию потока преобразования
#XFLD: Merge Default label
mergeDefaultLabel=Значение по умолчанию объединения
#XFLD: Optimize Default label
optimizeDefaultLabel=Значение по умолчанию оптимизации
#XFLD: Deployment Default label
deploymentDefaultLabel=Развертывание локальной таблицы (файловой)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} ЦП / {1} ГБ
#XFLD: Object type label
taskObjectTypeLabel=Тип объекта
#XFLD: Task activity label
taskActivityLabel=Операция
#XFLD: Task Application ID label
taskApplicationIDLabel=Приложение по умолчанию
#XFLD: Section header in space detail
generalSettings=Общие настройки
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Это пространство сейчас блокировано системой.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Изменения в этом разделе будут развернуты немедленно.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Обратите внимание, что изменение этих значений может снизить производительность.
#XFLD: Button text to unlock the space again
unlockSpace=Разблокировать пространство
#XFLD: Info text for audit log formatted message
auditLogText=Активируйте журналы аудита для записи операций чтения или изменения (политики аудита). Администраторы затем могут анализировать, кто выполнял какую операцию в какой момент времени.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Журналы аудита могут занимать большой объем места на диске в арендаторе. Если вы активируете политику аудита (операции чтения или изменения), то вам следует регулярно отслеживать использование места на диске (на карте "Использование места на диске" в "Мониторе системы") во избежание отключений из-за переполненного диска, которые могут привести к нарушениям работы сервиса. Если вы деактивируете политику аудита, все ее записи журналов аудита будут удалены. Если вы хотите сохранить записи журналов аудита, экспортируйте их до деактивации политики аудита. 
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Показать справку
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Это пространство превышает выделенную память и будет блокировано через {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=ч
#XMSG: Unit for remaining time until space is locked again
minutes=мин
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Аудит
#XFLD: Subsection header in space detail for auditing
auditing=Настройки аудита пространств
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Критичные пространства: использовано более 90 % памяти.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Пространства со статусом "ОК": использовано от 6 до 90 % памяти.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Холодные пространства: использовано не более 5 % памяти.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Критичные пространства: использовано более 90 % памяти.
#XFLD: Green space tooltip
okSpaceCountTooltip=Пространства со статусом "ОК": использовано от 6 до 90 % памяти.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Пространства со статусом "Блокировано": блокировано из‑за недостаточного объема памяти.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Блокированные пространства
#YMSE: Error while deleting remote source
deleteRemoteError=Не получилось удалить соединения.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=В дальнейшем ид. пространства нельзя будет изменить.\nРазрешены символы A - Z, 0 - 9 и _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Введите имя пространства.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Введите бизнес-имя.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Введите ид. пространства.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Неразрешенные символы. Используйте только A - Z, 0 - 9 и _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Ид. пространства уже есть.
#XFLD: Space searchfield placeholder
search=Поиск
#XMSG: Success message after creating users
createUsersSuccess=Пользователи добавлены
#XMSG: Success message after creating user
createUserSuccess=Пользователь добавлен
#XMSG: Success message after updating users
updateUsersSuccess=Пользователи обновлены ({0})
#XMSG: Success message after updating user
updateUserSuccess=Пользователь обновлен
#XMSG: Success message after removing users
removeUsersSuccess=Пользователи удалены ({0})
#XMSG: Success message after removing user
removeUserSuccess=Пользователь удален
#XFLD: Schema name
schemaName=Имя схемы
#XFLD: used of total
ofTemplate={0} из {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Присвоение диска ({0} из {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Присвоение памяти ({0} из {1})
#XFLD: Storage ratio on space
accelearationRAM=Ускорение памяти
#XFLD: No Storage Consumption
noStorageConsumptionText=Квота хранилища не присвоена.
#XFLD: Used disk label in space overview
usedStorageTemplate=Использование диска для данных ({0} из {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Использование памяти для данных ({0} из {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate=Использовано диска: {0} из {1}
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate=Использовано памяти: {0} из {1}
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} из {1} на диске присвоено
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate=Присвоено памяти: {0} из {1}
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Данные пространства: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Другие данные: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Рекомендуется расширить план или обратиться в службу поддержки SAP.
#XCOL: Space table-view column used Disk
usedStorage=Использование диска для данных
#XCOL: Space monitor column used Memory
usedRAM=Использование памяти для данных
#XCOL: Space monitor column Schema
tableSchema=Схема
#XCOL: Space monitor column Storage Type
tableStorageType=Тип памяти
#XCOL: Space monitor column Table Type
tableType=Тип таблицы
#XCOL: Space monitor column Record Count
tableRecordCount=Число записей
#XFLD: Assigned Disk
assignedStorage=Присвоение диска для данных
#XFLD: Assigned Memory
assignedRAM=Присвоение памяти для данных
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Использование памяти
#XFLD: space status
spaceStatus=Статус пространства
#XFLD: space type
spaceType=Тип пространства
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Мост для SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Продукт провайдера данных
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Нельзя удалить пространство {0}, т. к. оно относится к типу {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Нельзя удалить выбранные пространства {0}. Пространства следующего типа невозможно удалить: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Монитор
#XFLD: Tooltip for edit space button
editSpace=Редактировать пространство
#XMSG: Deletion warning in messagebox
deleteConfirmation=Действительно удалить это пространство?
#XFLD: Tooltip for delete space button
deleteSpace=Удалить пространство
#XFLD: storage
storage=Диск для данных
#XFLD: username
userName=Имя пользователя
#XFLD: port
port=Порт
#XFLD: hostname
hostName=Имя хоста
#XFLD: password
password=Пароль
#XBUT: Request new password button
requestPassword=Запросить новый пароль
#YEXP: Usage explanation in time data section
timeDataSectionHint=Создайте таблицы и измерения времени для использования в ваших моделях и журналах.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Хотите использовать данные из вашего пространства в других инструментах и приложениях? Создайте одного или нескольких пользователей с доступом к данным в вашем пространстве и укажите, требуется ли разрешать использование всех будущих данных пространства по умолчанию.
#XTIT: Create schema popup title
createSchemaDialogTitle=Создать схему Open SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Создание таблиц и измерений времени
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Редактирование таблиц и измерений времени
#XTIT: Time Data token title
timeDataTokenTitle=Данные времени
#XTIT: Time Data token title
timeDataUpdateViews=Обновить ракурсы данных времени
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Создание...
#XFLD: Time Data token creation error label
timeDataCreationError=Ошибка при создании. Повторите попытку.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Настройки таблицы времени
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Таблицы перевода
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Измерения времени
#XFLD: Time Data dialog time range label
timeRangeHint=Определите период времени.
#XFLD: Time Data dialog time data table label
timeDataHint=Укажите имя таблицы.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Укажите имя измерения.
#XFLD: Time Data Time range description label
timerangeLabel=Период времени
#XFLD: Time Data dialog from year label
fromYearLabel=С года
#XFLD: Time Data dialog to year label
toYearLabel=По год
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Тип календаря
#XFLD: Time Data dialog granularity label
granularityLabel=Гранулярность
#XFLD: Time Data dialog technical name label
technicalNameLabel=Техническое имя
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Таблица переводов на кварталы
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Таблица переводов на месяцы
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Таблица переводов на дни
#XFLD: Time Data dialog year label
yearLabel=Измерение (годы)
#XFLD: Time Data dialog quarter label
quarterLabel=Измерение (кварталы)
#XFLD: Time Data dialog month label
monthLabel=Измерение (месяцы)
#XFLD: Time Data dialog day label
dayLabel=Измерение (дни)
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Григорианский
#XFLD: Time Data dialog time granularity day label
day=день
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Достигнута максимальная длина в 1000 символов.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Макс. период времени = 150 лет.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=Значение поля "С года" должно быть меньше значения поля "По год"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=Значение "С года" должно быть 1900 или больше.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=Значение поля "По год" должно быть больше значения поля "С года"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=Значение "По год" должно быть меньше текущего года плюс 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Увеличение значения поля "С года" может привести к потере данных
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Снижение значения поля "По год" может привести к потере данных
#XMSG: Time Data creation validation error message
timeDataValidationError=Некоторые поля недействительны. Проверьте требуемые поля, чтобы продолжить.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Действительно удалить данные?
#XMSG: Time Data creation success message
createTimeDataSuccess=Данные времени созданы
#XMSG: Time Data update success message
updateTimeDataSuccess=Данные времени обновлены
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Данные времени удалены
#XMSG: Time Data creation error message
createTimeDataError=Ошибка при создании данных времени.
#XMSG: Time Data update error message
updateTimeDataError=Ошибка при обновлении данных времени.
#XMSG: Time Data creation error message
deleteTimeDataError=Ошибка при удалении данных времени.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Не удалось загрузить данные времени.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Предупреждение
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Не получилось удалить ваши данные времени, так как они используются в других моделях.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Не получилось удалить ваши данные времени, так как они используются в другой модели.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Это поле является обязательным.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Открыть в обозревателе баз данных
#YMSE: Dimension Year
dimensionYearView=Измерение "Год"
#YMSE: Dimension Year
dimensionQuarterView=Измерение "Квартал"
#YMSE: Dimension Year
dimensionMonthView=Измерение "Месяц"
#YMSE: Dimension Year
dimensionDayView=Измерение "День"
#XFLD: Time Data deletion object title
timeDataUsedIn=(используется в моделях: {0})
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(используется в 1 модели)
#XFLD: Time Data deletion table column provider
provider=Провайдер
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Зависимости
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Создать пользователя для схемы пространства
#XFLD: Create schema button
createSchemaButton=Создать схему Open SQL
#XFLD: Generate TimeData button
generateTimeDataButton=Создание таблиц и измерений времени
#XFLD: Show dependencies button
showDependenciesButton=Показать зависимости
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Чтобы выполнить эту операцию, пользователь должен быть участником пространства
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Создать пользователя схемы пространства
#YMSE: API Schema users load error
loadSchemaUsersError=Не удалось загрузить список пользователей.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Сведения о пользователе схемы пространства
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Действительно удалить выбранного пользователя?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Пользователь удален.
#YMSE: API Schema user deletion error
userDeleteError=Не получилось удалить пользователя.
#XFLD: User deleted
userDeleted=Пользователь удален.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Предупреждение
#XMSG: Remove user popup text
removeUserConfirmation=Действительно удалить пользователя? Пользователь и его присвоенные роли в объеме будут удалены из пространства.
#XMSG: Remove users popup text
removeUsersConfirmation=Действительно удалить пользователей? Пользователи и их присвоенные роли в объеме будут удалены из пространства.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Удалить
#YMSE: No data text for available roles
noDataAvailableRoles=Пространство не добавлено ни в одну роль в объеме. \n Чтобы можно было добавить пользователей в пространство, его сначала необходимо добавить в одну или несколько ролей в объеме.
#YMSE: No data text for selected roles
noDataSelectedRoles=Нет выбранных ролей в объеме
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Сведения о конфигурации схемы Open SQL
#XFLD: Label for Read Audit Log
auditLogRead=Активировать журнал аудита для операций чтения
#XFLD: Label for Change Audit Log
auditLogChange=Активировать журнал аудита для операций изменения
#XFLD: Label Audit Log Retention
auditLogRetention=Оставить журналы на
#XFLD: Label Audit Log Retention Unit
retentionUnit=дн.
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Введите целое число от {0} до {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Использовать данные схемы пространства
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Остановить использование данных схемы пространства
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Схема Open SQL может использовать данные вашей схемы пространства. Если остановить использование, модели на основе данных этой схемы пространства могут прекратить работу.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Остановить использование
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Это пространство используется для доступа к озеру данных
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Озеро данных активировано
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Достигнут лимит оперативной памяти
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Достигнут лимит памяти
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Достигнут минимальный лимит памяти
#XFLD: Space ram tag
ramLimitReachedLabel=Достигнут лимит оперативной памяти
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Достигнут минимальный лимит оперативной памяти
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Вы достигли лимита присвоенной памяти пространства в {0}. Присвойте пространству больше памяти.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Достигнут лимит памяти системы
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Вы достигли лимита памяти системы в {0}. Невозможно присвоить пространству больше памяти.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=При удалении этой схемы Open SQL также будут безвозвратно удалены все сохраненные объекты и указанные в схеме связи. Продолжить?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Схема удалена
#YMSE: Error while deleting schema.
schemaDeleteError=Не получилось удалить схему.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Схема обновлена
#YMSE: Error while updating schema.
schemaUpdateError=Не удалось обновить схему.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Пароль для этой схемы уже задан. Если вы забыли или потеряли пароль, запросите новый. Не забудьте скопировать или сохранить новый пароль.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Скопируйте пароль. Вам нужно настроить соединение с этой схемой. Если вы забыли пароль, откройте данное диалоговое окно, чтобы его сбросить.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Это имя нельзя изменить после создания схемы.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Пространство
#XFLD: HDI Container section header
HDIContainers=Контейнеры HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Добавить контейнеры HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Удалить контейнеры HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Активировать доступ
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Контейнеры HDI добавлены.
#YMSE: No data text for Timedata section
noDataTimedata=Таблицы и измерения времени не созданы.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Невозможно загрузить таблицы и измерения, так как база данных времени выполнения недоступна.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Невозможно загрузить контейнеры HDI, так как база данных времени выполнения недоступна.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Не удалось вызвать контейнеры HDI. Повторите попытку позднее.
#XFLD Table column header for HDI Container names
HDIContainerName=Имя контейнера HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Активировать доступ
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Вы можете активировать SAP SQL Data Warehousing в вашем арендаторе SAP Datasphere, чтобы обмениваться данными между контейнерами HDI и пространствами SAP Datasphere без перемещения данных.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Для этого создайте сервисный запрос поддержки нажатием на кнопку ниже.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=После обработки сервисного запроса необходимо создать один или несколько новых контейнеров HDI в базе данных среды выполнения SAP Datasphere. Тогда кнопка "Активировать доступ" изменится на кнопку "+" в разделе "Контейнеры HDI" для всех ваших пространств SAP Datasphere, и вы сможете добавить контейнеры в пространство.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Нужно больше информации? Перейдите в %%0. Для получения подробных сведений о том, что включать в запрос, см. %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=Справка SAP
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP-нота 3057059
#XBUT: Open Ticket Button Text
openTicket=Создать сервисный запрос
#XBUT: Add Button Text
add=Добавить
#XBUT: Next Button Text
next=Дальше
#XBUT: Edit Button Text
editUsers=Редактировать
#XBUT: create user Button Text
createUser=Создать
#XBUT: Update user Button Text
updateUser=Выбрать
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Добавить неприсвоенные контейнеры HDI
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Неприсвоенные контейнеры не найдены. \n Возможно, контейнер, который вы ищете, уже присвоен пространству.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Не удалось загрузить присвоенные контейнеры HDI.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Не удалось загрузить контейнеры HDI.
#XMSG: Success message
succeededToAddHDIContainer=Контейнер HDI добавлен
#XMSG: Success message
succeededToAddHDIContainerPlural=Контейнеры HDI добавлены
#XMSG: Success message
succeededToDeleteHDIContainer=Контейнер HDI удален
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Контейнеры HDI удалены
#XFLD: Time data section sub headline
timeDataSection=Таблицы и измерения времени
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Чтение
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Изменение
#XFLD: Remote sources section sub headline
allconnections=Присвоение соединения
#XFLD: Remote sources section sub headline
localconnections=Локальные соединения
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Присвоение элементов
#XFLD: User assignment section sub headline
userAssignment=Присвоение пользователей
#XFLD: User section Access dropdown Member
member=Элемент
#XFLD: User assignment section column name
user=Имя пользователя
#XTXT: Selected role count
selectedRoleToolbarText=Выбрано: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Соединения
#XTIT: Space detail section data access title
detailsSectionDataAccess=Доступ к схеме
#XTIT: Space detail section time data title
detailsSectionGenerateData=Данные времени
#XTIT: Space detail section members title
detailsSectionUsers=Участники
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Пользователи
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Недостаточно памяти
#XTIT: Storage distribution
storageDistributionPopoverTitle=Использование места на диске
#XTXT: Out of Storage popover text
insufficientStorageText=Чтобы создать новое пространство, уменьшите присвоенную память другого пространства или удалите ненужное пространство. Вы можете увеличить общую память системы в разделе "Управление планом".
#XMSG: Space id length warning
spaceIdLengthWarning=Превышен максимум, составляющий {0} симв.
#XMSG: Space name length warning
spaceNameLengthWarning=Превышен максимум, составляющий {0} симв.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Не используйте префикс {0} во избежание возможных конфликтов.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Не удалось загрузить схемы Open SQL.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Не удалось создать схему Open SQL.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Не удалось загрузить все дистанционные соединения.
#YMSE: Error while loading space details
loadSpaceDetailsError=Не удалось загрузить сведения о пространстве.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Не удалось развернуть пространство.
#YMSE: Error while copying space details
copySpaceDetailsError=Не удалось скопировать пространство.
#YMSE: Error while loading storage data
loadStorageDataError=Не удалось загрузить данные памяти.
#YMSE: Error while loading all users
loadAllUsersError=Не удалось загрузить всех пользователей.
#YMSE: Failed to reset password
resetPasswordError=Не удалось сбросить пароль.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Новый пароль установлен для схемы
#YMSE: DP Agent-name too long
DBAgentNameError=Имя агента DP слишком длинное.
#YMSE: Schema-name not valid.
schemaNameError=Имя схемы недействительно.
#YMSE: User name not valid.
UserNameError=Имя пользователя недействительно.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Использование по типу памяти
#XTIT: Consumption by Schema
consumptionSchemaText=Использование по схеме
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Общее использование таблиц по схеме
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Общее использование по типу таблицы
#XTIT: Tables
tableDetailsText=Сведения о таблице
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Использование табличного хранилища
#XFLD: Table Type label
tableTypeLabel=Тип таблицы
#XFLD: Schema label
schemaLabel=Схема
#XFLD: reset table tooltip
resetTable=Сбросить таблицу
#XFLD: In-Memory label in space monitor
inMemoryLabel=Память
#XFLD: Disk label in space monitor
diskLabel=Диск
#XFLD: Yes
yesLabel=Да
#XFLD: No
noLabel=Нет
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Хотите разрешить использование данных в этом пространстве по умолчанию?
#XFLD: Business Name
businessNameLabel=Бизнес-имя
#XFLD: Refresh
refresh=Обновить
#XMSG: No filter results title
noFilterResultsTitle=Кажется, из-за ваших настроек фильтрации не отображаются никакие данные.
#XMSG: No filter results message
noFilterResultsMsg=Попробуйте уточнить критерии фильтрации. Если данные не будут отображаться и тогда, создайте несколько таблиц в Построителе данных. Когда они начнут использовать память, вы сможете отслеживать их здесь.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Динамическая база данных недоступна.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Поскольку динамическая база данных недоступна, некоторые функции отключены, и невозможно отобразить информацию на этой странице.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Не удалось создать пользователя схемы пространства.
#YMSE: Error User name already exists
userAlreadyExistsError=Имя пользователя уже существует.
#YMSE: Error Authentication failed
authenticationFailedError=Ошибка аутентификации.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Пользователь заблокирован: слишком много неудачных попыток входа. Запросите новый пароль, чтобы разблокировать пользователя.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Новый пароль установлен, пользователь разблокирован
#XMSG: user is locked message
userLockedMessage=Пользователь заблокирован.
#XCOL: Users table-view column Role
spaceRole=Роль
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Роль в объеме
#XCOL: Users table-view column Space Admin
spaceAdmin=Администратор пространства
#XFLD: User section dropdown value Viewer
viewer=Пользователь с правами на просмотр
#XFLD: User section dropdown value Modeler
modeler=Разработчик моделей
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Интегратор данных
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Администратор пространства
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Роль пространства обновлена
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Не удалось обновить роль пространства.
#XFLD:
databaseUserNameSuffix=Суффикс имени пользователя базы данных
#XTXT: Space Schema password text
spaceSchemaPasswordText=Чтобы настроить соединение с этой схемой, скопируйте свой пароль. Если вы забыли пароль, всегда можно запросить новый.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Чтобы настроить доступ под этим пользователем, активируйте потребление и скопируйте регистрационные данные. Если вы можете скопировать только регистрационные данные без пароля, не забудьте добавить пароль позднее.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Активировать потребление в Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Регистрационные данные для предоставленного пользователем сервиса:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Регистрационные данные для предоставленного пользователем сервиса (без пароля):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Скопировать регистрационные данные без пароля
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Скопировать все регистрационные данные
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Скопировать пароль
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Регистрационные данные скопированы в буфер обмена
#XMSG: Password copied to clipboard
passwordCopiedMessage=Пароль скопирован в буфер обмена
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Создать пользователя базы данных
#XMSG: Database Users section title
databaseUsers=Пользователи базы данных
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Сведения о пользователе базы данных
#XFLD: database user read audit log
databaseUserAuditLogRead=Активировать журналы аудита для операций чтения и оставить журналы на
#XFLD: database user change audit log
databaseUserAuditLogChange=Активировать журналы аудита для операций изменения и оставить журналы на
#XMSG: Cloud Platform Access
cloudPlatformAccess=Доступ к Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Настройте доступ к своему контейнеру SAP HANA Deployment Infrastructure (HDI) под этим пользователем базы данных. Чтобы установить соединение со вашим контейнером HDI, следует включить моделирование SQL.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Активировать потребление HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Хотите ли вы сделать данные в вашем пространстве доступными для потребления в других инструментах и приложениях?
#XFLD: Enable Consumption
enableConsumption=Активировать использование SQL
#XFLD: Enable Modeling
enableModeling=Активировать моделирование SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Прием данных
#XMSG: Privileges for Data Consumption
privilegesConsumption=Потребление данных для внешних инструментов
#XFLD: SQL Modeling
sqlModeling=Моделирование SQL
#XFLD: SQL Consumption
sqlConsumption=Потребление SQL
#XFLD: enabled
enabled=Активировано
#XFLD: disabled
disabled=Деактивировано
#XFLD: Edit Privileges
editPrivileges=Редактировать привилегии
#XFLD: Open Database Explorer
openDBX=Открыть проводник базы данных
#XFLD: create database user hint
databaseCreateHint=Обратите внимание, что после сохранения невозможно изменить имя пользователя.
#XFLD: Internal Schema Name
internalSchemaName=Имя внутренней схемы
#YMSE: Failed to load database users
loadDatabaseUserError=Не удалось загрузить пользователей базы данных
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Не получилось удалить пользователей базы данных
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Пользователь базы данных удален
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Пользователи базы данных удалены
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Пользователь базы данных создан
#YMSE: Failed to create database user
createDatabaseUserError=Не удалось создать пользователя базы данных
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Пользователь базы данных обновлен
#YMSE: Failed to update database user
updateDatabaseUserError=Не удалось обновить пользователя базы данных
#XFLD: HDI Consumption
hdiConsumption=Потребление HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Доступ к базе данных
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Разрешите использование данных своего пространства по умолчанию. Модели в построителях будут автоматически разрешать потребление данных.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Потребление данных пространства по умолчанию:
#XFLD: Database User Name
databaseUserName=Имя пользователя базы данных
#XMSG: Database User creation validation error message
databaseUserValidationError=Некоторые поля недействительны. Проверьте требуемые поля, чтобы продолжить.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Невозможно активировать прием данных, поскольку пользователь был перенесен.
#XBUT: Remove Button Text
remove=Удалить
#XBUT: Remove Spaces Button Text
removeSpaces=Удалить пространства
#XBUT: Remove Objects Button Text
removeObjects=Удалить объекты
#XMSG: No members have been added yet.
noMembersAssigned=Участники еще не добавлены.
#XMSG: No users have been added yet.
noUsersAssigned=Пользователи еще не добавлены.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Пользователи базы данных не созданы, или установленный фильтр не показывает данные.
#XMSG: Please enter a user name.
noDatabaseUsername=Введите имя пользователя.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Имя пользователя слишком длинное, используйте более короткое.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Нет активированных привилегий, а для пользователя БД ограничена функциональность. Продолжить?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Чтобы активировать журналы аудита для операций изменения, нужно также активировать прием данных. Выполнить?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Чтобы активировать журналы аудита для операций считывания, нужно также активировать прием данных. Выполнить?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Чтобы активировать потребление HDI, нужно также активировать прием и потребление данных. Выполнить?
#XMSG:
databaseUserPasswordText=Чтобы настроить соединение с этим пользователем БД, скопируйте свой пароль. Если вы забыли пароль, всегда можно запросить новый.
#XTIT: Space detail section members title
detailsSectionMembers=Участники
#XMSG: New password set
newPasswordSet=Новый пароль установлен
#XFLD: Data Ingestion
dataIngestion=Прием данных
#XFLD: Data Consumption
dataConsumption=Потребление данных
#XFLD: Privileges
privileges=Привилегии
#XFLD: Enable Data ingestion
enableDataIngestion=Активировать прием данных
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Загрузите операции чтения и изменения для приема данных.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Предоставьте доступ к своему пространству в своих контейнерах HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Активировать потребление данных
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Разрешите другим приложениям или инструментам использовать данные своего пространства.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Чтобы настроить доступ под этим пользователем БД, скопируйте регистрационные данные в свой предоставленный пользователем сервис. Если вы можете скопировать только регистрационные данные без пароля, не забудьте добавить пароль позднее.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Пропускная способность времени выполнения потока данных ({0}:{1} ч из {2} ч)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Не удалось загрузить пропускную способность времени выполнения потока данных
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Пользователь может предоставлять разрешение на потребление данных другим пользователям.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Активировать потребление данных с опцией предоставления
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Чтобы активировать потребление данных с опцией предоставления, необходимо активировать потребление данных. Активировать обе опции?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Активировать Automated Predictive Library (APL) и Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Пользователь может применять встроенные функции машинного обучения SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Политика паролей
#XMSG: Password Policy
passwordPolicyHint=Здесь вы можете активировать или деактивировать настроенную политику паролей.
#XFLD: Enable Password Policy
enablePasswordPolicy=Активировать политику паролей
#XMSG: Read Access to the Space Schema
readAccessTitle=Доступ для чтения к схеме пространства
#XMSG: read access hint
readAccessHint=Разрешить пользователю базы данных подключать внешние инструменты к схеме пространства и считывать ракурсы, экспонированные для потребления.
#XFLD: Space Schema
spaceSchema=Схема пространства
#XFLD: Enable Read Access (SQL)
enableReadAccess=Активировать доступ для чтения (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Разрешить пользователю предоставлять доступ для чтения другим пользователям.
#XFLD: With Grant Option
withGrantOption=С опцией предоставления
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Предоставьте доступ к своему пространству в своих контейнерах HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Активировать потребление HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Доступ для записи к схеме Open SQL пользователя
#XMSG: write access hint
writeAccessHint=Разрешить пользователю базы данных подключать внешние инструменты к схеме Open SQL пользователя, чтобы создавать сущности данных и получать данные для использования в пространстве.
#XFLD: Open SQL Schema
openSQLSchema=Схема Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Активировать доступ для записи (SQL, DDL и DML)
#XMSG: audit hint
auditHint=Записывать операции чтения и изменения в схеме Open SQL
#XMSG: data consumption hint
dataConsumptionHint=Экспонировать для потребления все новые ракурсы в пространстве по умолчанию. Разработчик моделей может переопределить эту настройку для отдельных ракурсов через переключатель "Экспонировать для потребления" на боковой панели вывода ракурса. Вы также можете выбрать форматы экспонирования ракурсов.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Экспонировать для потребления по умолчанию
#XMSG: database users hint consumption hint
databaseUsersHint2New=Создайте пользователей базы данных для подключения внешних инструментов к SAP Datasphere. Настройте полномочия, чтобы разрешить пользователям чтение данных пространства, создание сущностей данных (DDL) и получение данных (DML) для использования в пространстве.
#XFLD: Read
read=Чтение
#XFLD: Read (HDI)
readHDI=Чтение (HDI)
#XFLD: Write
write=Запись
#XMSG: HDI Containers Hint
HDIContainersHint2=Активируйте доступ к контейнерам SAP HANA Deployment Infrastructure (HDI) в вашем пространстве. Разработчик моделей может использовать артефакты HDI как источники для ракурсов, и клиенты HDI могут обращаться к данным вашего пространства.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Открыть окно информации
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Пользователь базы данных блокирован. Чтобы разблокировать его, откройте диалоговое окно.
#XFLD: Table
table=Таблица
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Соединение с партнером
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Конфигурация соединения с партнером
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Определите собственную плитку соединения с партнером, добавив iFrame URL и значок. Это соединение доступно только для данного арендатора.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Имя плитки
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Происхождение размещенного сообщения iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Значок
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Конфигурация(и) соединения с партнером не найдены(ы).
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Конфигурации соединения с партнером невозможно отобразить, когда база данных среды выполнения недоступна.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Создать конфигурацию соединения с партнером
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Загрузить значок
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Выбрать (максимальный размер 200 КБ)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Пример плитки партнера
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Обзор
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Конфигурация соединения с партнером успешно создана.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=При удалении конфигурации(й) соединения с партнером обнаружена ошибка.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Конфигурация соединения с партнером успешно удалена.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=При поиске конфигураций соединения с партнером обнаружена ошибка.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Не удалось загрузить файл, так как он превышает максимальный размер 200 КБ.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Создать конфигурацию соединения с партнером
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Удалите конфигурацию соединения с партнером.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Не удалось создать плитку партнера.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Не получилось удалить плитку партнера.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Сброс пользовательских настроек коннектора SAP HANA Cloud Connector выполнен с ошибкой
#XFLD: Workload Class
workloadClass=Класс рабочей нагрузки
#XFLD: Workload Management
workloadManagement=Управление рабочей нагрузкой
#XFLD: Priority
workloadClassPriority=Приоритет
#XMSG:
workloadManagementPriorityHint=Можно задать приоритет этого пространства при запросе в базу данных. Введите значение от 1 (самый низкий) до 8 (самый высокий). В случае пространств, конкурирующих за доступные треды, имеющие более высокий приоритет будут выполнены первыми.
#XMSG:
workloadClassPriorityHint=Можно указать приоритет пространства от 0 (самый низкий) до 8 (самый высокий). Инструкции пространства с высоким приоритетом выполняются раньше инструкций других пространств с более низким приоритетом. Приоритет по умолчанию = 5. Значение 9 зарезервировано для системных операций и недоступно для пространства.
#XFLD: Statement Limits
workloadclassStatementLimits=Лимиты инструкций
#XFLD: Workload Configuration
workloadConfiguration=Конфигурация рабочей нагрузки
#XMSG:
workloadClassStatementLimitsHint=Вы можете указать максимальное число (или процент) тредов и ГБ памяти, доступных инструкциям, выполняемым параллельно в пространстве. Введите любое значение или процент от 0 (нет лимита) до общего размера памяти и тредов, доступных в арендаторе. \n\n При указании лимита треда учитывайте возможное снижение производительности. \n\n При указании лимита памяти инструкции не будут выполняться в случае достижения этого лимита.
#XMSG:
workloadClassStatementLimitsDescription=Конфигурация по умолчанию предоставляет широкие лимиты ресурсов, чтобы отдельные пространства не перегружали систему.
#XMSG:
workloadClassStatementLimitCustomDescription=Можно установить максимальные совокупные лимиты тредов и памяти, которые инструкции могут использовать при одновременном выполнении в пространстве.
#XMSG:
totalStatementThreadLimitHelpText=Если задан слишком низкий лимит тредов, возможны проблемы с производительностью инструкций, а при слишком высоких значениях и 0 пространство может использовать все доступные системные треды.
#XMSG:
totalStatementMemoryLimitHelpText=Если задан слишком низкий лимит памяти, возможны проблемы нехватки памяти, а при слишком высоких значениях и 0 пространство может использовать всю доступную память системы.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Введите процент от 1 до 70 % (или эквивалент) от общего числа тредов, доступных в вашем арендаторе. Установка слишком низкого лимита тредов может снизить производительность инструкций, а слишком высокий лимит может снизить производительность инструкций в других пространствах.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Введите процент от 1 до {0} % (или эквивалент) от общего числа тредов, доступных в вашем арендаторе. Установка слишком низкого лимита тредов может снизить производительность инструкций, а слишком высокий лимит может снизить производительность инструкций в других пространствах.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Введите значение или процент от 0 (без ограничения) до общего объема памяти, доступного в вашем арендаторе. Установка слишком низкого лимита памяти может снизить производительность инструкций, а слишком высокий лимит может снизить производительность инструкций в других пространствах.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Общий лимит треда инструкций
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=ГБ
#XFLD, 80: Segmented button label
workloadclassThreads=Треды
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Общий лимит памяти инструкций
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Не удалось загрузить информацию SAP HANA клиента.
#XMSG:
minimumLimitReached=Достигнут минимальный лимит.
#XMSG:
maximumLimitReached=Достигнут максимальный лимит.
#XMSG: Name Taken for Technical Name
technical-name-taken=Соединение с введенным техническим именем уже есть. Введите другое имя.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Введенное техническое имя длиннее 40 символов. Введите более короткое имя.
#XMSG: Technical name field empty
technical-name-field-empty=Введите техническое имя.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=В имени можно использовать только буквы (a-z), цифры (0-9) и подчеркивание (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Имя не может начинаться или заканчиваться подчеркиванием (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Активировать лимиты инструкций
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Настройки
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Чтобы создать или редактировать соединения, откройте приложение "Соединения" на боковой панели навигации или щелкните здесь:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Перейти к соединениям
#XFLD: Not deployed label on space tile
notDeployedLabel=Пространство еще не развернуто.
#XFLD: Not deployed additional text on space tile
notDeployedText=Разверните пространство.
#XFLD: Corrupt space label on space tile
corruptSpace=Что-то пошло не так.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Попробуйте развернуть повторно или обратиться в службу поддержки
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Данные журнала аудита
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Административные данные
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Прочие данные
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Данные в пространствах
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Действительно разблокировать пространство?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Действительно блокировать пространство?
#XFLD: Lock
lock=Блокировать
#XFLD: Unlock
unlock=Разблокировать
#XFLD: Locking
locking=Блокировка
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Пространство блокировано
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Пространство разблокировано
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Пространства блокированы
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Пространства разблокированы
#YMSE: Error while locking a space
lockSpaceError=Невозможно блокировать пространство.
#YMSE: Error while unlocking a space
unlockSpaceError=Невозможно разблокировать пространство.
#XTIT: popup title Warning
confirmationWarningTitle=Предупреждение
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Пространство блокировано вручную.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Пространство блокировано системой, так как журналы аудита занимают много ГБ на диске.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Пространство блокировано системой, так как оно превышает выделенную память или место на диске.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Действительно разблокировать выбранные пространства?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Действительно блокировать выбранные пространства?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Редактор ролей в объеме
#XTIT: ECN Management title
ecnManagementTitle=Управление пространством и эластичными узлами вычислений
#XFLD: ECNs
ecns=Эластичные узлы вычислений
#XFLD: ECN phase Ready
ecnReady=Готово
#XFLD: ECN phase Running
ecnRunning=Выполняется
#XFLD: ECN phase Initial
ecnInitial=Не готово
#XFLD: ECN phase Starting
ecnStarting=Запускается
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Не удалось запустить
#XFLD: ECN phase Stopping
ecnStopping=Останавливается
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Не удалось остановить
#XBTN: Assign Button
assign=Присвоить пространства
#XBTN: Start Header-Button
start=Запустить
#XBTN: Update Header-Button
repair=Обновить
#XBTN: Stop Header-Button
stop=Остановить
#XFLD: ECN hours remaining
ecnHoursRemaining=Осталось 1000 часов
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=Осталось блокочасов: {0}
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=Остался {0} блокочас
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Создать эластичный узел вычислений
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Редактировать эластичный узел вычислений
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Удалить эластичный узел вычислений
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Присвоить пространства
#XFLD: ECN ID
ECNIDLabel=Эластичный узел вычислений
#XTXT: Selected toolbar text
selectedToolbarText=Выбрано: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Эластичные узлы вычислений
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Число объектов
#XTIT: Object assignment - Dialog header text
selectObjects=Выберите пространства и объекты для присвоения эластичному узлу вычислений:
#XTIT: Object assignment - Table header title: Objects
objects=Объекты
#XTIT: Object assignment - Table header: Type
type=Тип
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Обратите внимание, что удаление пользователя базы данных приведет к удалению всех сгенерированных записей журналов аудита. Чтобы сохранить журналы аудита, экспортируйте их перед удалением пользователя базы данных.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Обратите внимание, что отмена присвоения контейнера HDI пространству приведет к удалению всех сгенерированных записей журналов аудита. Чтобы сохранить журналы аудита, экспортируйте их перед отменой присвоения контейнера HDI.
#XTXT: All audit logs
allAuditLogs=Все записи журналов аудита, сгенерированные для пространства
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Обратите внимание, что деактивация политик аудита (операций чтения или изменения) приведет к удалению всех записей журналов аудита. Чтобы сохранить записи журналов аудита, экспортируйте их перед деактивацией политики аудита.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Пространства или объекты еще не присвоены
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Чтобы начать работу с эластичным узлом вычислений, присвойте ему пространство или объекты.
#XTIT: No Spaces Illustration title
noSpacesTitle=Пространство еще не создано
#XTIT: No Spaces Illustration description
noSpacesDescription=Чтобы начать сбор данных, создайте пространство.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Корзина пуста
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Отсюда можно восстановить удаленные пространства.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=После развертывания пространства следующие пользователи базы данных будут удалены {0} без возможности восстановления:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Удалить пользователей базы данных
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=Ид. уже существует.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Используйте только строчные буквы a-z и цифры 0-9.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=Ид. должен быть не короче {0} симв.
#XMSG: ecn id length warning
ecnIdLengthWarning=Превышен максимум, составляющий {0} симв.
#XFLD: open System Monitor
systemMonitor=Монитор системы
#XFLD: open ECN schedule dialog menu entry
schedule=Планирование
#XFLD: open create ECN schedule dialog
createSchedule=Создать планирование
#XFLD: open change ECN schedule dialog
changeSchedule=Редактировать планирование
#XFLD: open delete ECN schedule dialog
deleteSchedule=Удалить планирование
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Присвоить планирование мне
#XFLD: open pause ECN schedule dialog
pauseSchedule=Приостановить планирование
#XFLD: open resume ECN schedule dialog
resumeSchedule=Возобновить планирование
#XFLD: View Logs
viewLogs=Просмотреть журналы
#XFLD: Compute Blocks
computeBlocks=Блоки вычислений
#XFLD: Memory label in ECN creation dialog
ecnMemory=Память (ГБ)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Хранилище (ГБ)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Число ЦП
#XFLD: ECN updated by label
changedBy=Изменил
#XFLD: ECN updated on label
changedOn=Дата изменения
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Эластичный узел вычислений создан
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Не удалось создать эластичный узел вычислений
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Эластичный узел вычислений обновлен
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Не удалось обновить эластичный узел вычислений
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Эластичный узел вычислений удален
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Не получилось удалить эластичный узел вычислений
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Запуск эластичного узла вычислений
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Остановка эластичного узла вычислений
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Не удалось запустить эластичный узел вычислений
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Не удалось остановить эластичный узел вычислений
#XBUT: Add Object button for an ECN
assignObjects=Добавить объекты
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Добавить все объекты автоматически
#XFLD: object type label to be assigned
objectTypeLabel=Тип (семантическое использование)
#XFLD: assigned object type label
assignedObjectTypeLabel=Тип
#XFLD: technical name label
TechnicalNameLabel=Техническое имя
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Выберите объекты для добавления в эластичный узел вычислений
#XTIT: Add objects dialog title
assignObjectsTitle=Присвоить объекты
#XFLD: object label with object count
objectLabel=Объект
#XMSG: No objects available to add message.
noObjectsToAssign=Нет доступных объектов для присвоения.
#XMSG: No objects assigned message.
noAssignedObjects=Нет присвоенных объектов.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Предупреждение
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Удалить
#XMSG: Remove objects popup text
removeObjectsConfirmation=Действительно удалить выбранные объекты?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Действительно удалить выбранные пространства?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Удалить пространства
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Экспонированные объекты удалены
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Экспонированные объекты присвоены
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Все экспонированные объекты
#XFLD: Spaces tab label
spacesTabLabel=Пространства
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Экспонированные объекты
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Пространства удалены
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Пространство удалено
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Не удалось присвоить или удалить пространства.
#YMSE: Error while removing objects
removeObjectsError=Не удалось присвоить или удалить объекты.
#YMSE: Error while removing object
removeObjectError=Не удалось присвоить или удалить объект.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Ранее выбранный номер больше не действителен. Выберите действительный номер.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Выберите действительный класс производительности.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Ранее выбранный класс производительности "{0}" сейчас недействителен. Выберите действительный класс производительности.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Действительно удалить эластичный узел вычислений?
#XFLD: tooltip for ? button
help=Справка
#XFLD: ECN edit button label
editECN=Настроить
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Модель связей сущностей
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Локальная таблица
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Дистанционная таблица
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Аналитическая модель
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Цепочка задач
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Поток данных
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Поток тиражирования
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Поток преобразования
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Интеллектуальный поиск
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Репозитарий
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Корпоративный поиск
#XFLD: Technical type label for View
DWC_VIEW=Ракурс
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Продукт данных
#XFLD: Technical type label for Data Access Control
DWC_DAC=Контроль доступа к данным
#XFLD: Technical type label for Folder
DWC_FOLDER=Папка
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Бизнес-сущность
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Вариант бизнес-сущности
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Сценарий ответственности
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Модель фактов
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Перспектива
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Модель потребления
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Дистанционное соединение
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Вариант модели фактов
#XMSG: Schedule created alert message
createScheduleSuccess=Планирование создано
#XMSG: Schedule updated alert message
updateScheduleSuccess=Планирование обновлено
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Планирование удалено
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Планирование присвоено вам
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Приостанавливаем 1 планирование
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Возобновляем 1 планирование
#XFLD: Segmented button label
availableSpacesButton=Доступно
#XFLD: Segmented button label
selectedSpacesButton=Выбрано
#XFLD: Visit website button text
visitWebsite=Посетить веб-сайт
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Выбранный ранее исходный язык будет удален.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Активировать
#XFLD: ECN performance class label
performanceClassLabel=Класс производительности
#XTXT performance class memory text
memoryText=Память
#XTXT performance class compute text
computeText=Вычисления
#XTXT performance class high-compute text
highComputeText=Высокопроизводительные вычисления
#XBUT: Recycle Bin Button Text
recycleBin=Корзина
#XBUT: Restore Button Text
restore=Восстановить
#XMSG: Warning message for new Workload Management UI
priorityWarning=Эта область только для чтения. Приоритет пространства можно изменить в области "Система / Конфигурация / Управление рабочей нагрузкой".
#XMSG: Warning message for new Workload Management UI
workloadWarning=Эта область только для чтения. Конфигурацию рабочей нагрузки пространства можно изменить в области "Система / Конфигурация / Управление рабочей нагрузкой".
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=ВЦП Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Память Apache Spark (ГБ)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Прием продукта данных
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Данные недоступны, так как пространство в настоящее время развертывается
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Данные недоступны, так как пространство в настоящее время загружается
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Редактировать сопоставления инстанций
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 ГБ
