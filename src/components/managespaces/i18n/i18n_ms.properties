#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Pemantauan
#XTXT: Type name for spaces in browser tab page title
space=Ruang
#_____________________________________
#XFLD: Spaces label in
spaces=Ruang
#XFLD: Manage plan button text
manageQuotaButtonText=Uruskan Rancangan
#XBUT: Manage resources button
manageResourcesButton=Uruskan Sumber
#XFLD: Create space button tooltip
createSpace=Cipta Ruang
#XFLD: Create
create=Cipta
#XFLD: Deploy
deploy=Atur Duduk
#XFLD: Page
page=Halaman
#XFLD: Cancel
cancel=Batalkan
#XFLD: Update
update=Kemas Kini
#XFLD: Save
save=Simpan
#XFLD: OK
ok=OK
#XFLD: days
days=Hari
#XFLD: Space tile edit button label
edit=Edit
#XFLD: Auto Assign all objects to space
autoAssign=Umpukkan Secara Automatik
#XFLD: Space tile open monitoring button label
openMonitoring=Pemantau
#XFLD: Delete
delete=Padam
#XFLD: Copy Space
copy=Salin
#XFLD: Close
close=Tutup
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Aktif
#XFLD: Space status locked
lockedLabel=Dikunci
#XFLD: Space status critical
criticalLabel=Kritikal
#XFLD: Space status cold
coldLabel=Sejuk
#XFLD: Space status deleted
deletedLabel=Dipadam
#XFLD: Space status unknown
unknownLabel=Tidak diketahui
#XFLD: Space status ok
okLabel=Sihat
#XFLD: Database user expired
expired=Telah Tamat Tempoh
#XFLD: deployed
deployed=Diatur Duduk
#XFLD: not deployed
notDeployed=Tidak Diatur Duduk
#XFLD: changes to deploy
changesToDeploy=Perubahan untuk Atur Duduk
#XFLD: pending
pending=Diatur Duduk
#XFLD: designtime error
designtimeError=Ralat Masa Rekaan
#XFLD: runtime error
runtimeError=Ralat Masa Jalanan
#XFLD: Space created by label
createdBy=Dicipta oleh
#XFLD: Space created on label
createdOn=Dicipta pada
#XFLD: Space deployed on label
deployedOn=Diatur Duduk pada
#XFLD: Space ID label
spaceID=ID Ruang
#XFLD: Priority label
priority=Keutamaan
#XFLD: Space Priority label
spacePriority=Keutamaan Ruang
#XFLD: Space Configuration label
spaceConfiguration=Konfigurasi Ruang
#XFLD: Not available
notAvailable=Tidak Tersedia
#XFLD: WorkloadType default
default=Lalai
#XFLD: WorkloadType custom
custom=Khas
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Capaian Data Lake
#XFLD: Translation label
translationLabel=Terjemahan
#XFLD: Source language label
sourceLanguageLabel=Bahasa Sumber
#XFLD: Translation CheckBox label
translationCheckBox=Dayakan Terjemahan
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Atur duduk ruang untuk capai butiran pengguna.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Atur duduk ruang untuk buka Explorer Pangkalan Data.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Anda tidak boleh menggunakan ruang ini untuk mencapai Data Lake kerana ia telah digunakan oleh ruang lain.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Gunakan ruang ini untuk mencapai data lake.
#XFLD: Space Priority minimum label extension
low=Rendah
#XFLD: Space Priority maximum label extension
high=Tinggi
#XFLD: Space name label
spaceName=Nama Ruang
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Atur Duduk Objek
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Salin {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Tidak Dipilih)
#XTXT Human readable text for language code "af"
af=Afrikaan
#XTXT Human readable text for language code "ar"
ar=Arab
#XTXT Human readable text for language code "bg"
bg=Bulgaria
#XTXT Human readable text for language code "ca"
ca=Catalonia
#XTXT Human readable text for language code "zh"
zh=Cina Ringkas
#XTXT Human readable text for language code "zf"
zf=Cina
#XTXT Human readable text for language code "hr"
hr=Croatia
#XTXT Human readable text for language code "cs"
cs=Czech
#XTXT Human readable text for language code "cy"
cy=Wales
#XTXT Human readable text for language code "da"
da=Denmark
#XTXT Human readable text for language code "nl"
nl=Belanda
#XTXT Human readable text for language code "en-UK"
en-UK=English (United Kingdom)
#XTXT Human readable text for language code "en"
en=Bahasa Inggeris (Amerika Syarikat)
#XTXT Human readable text for language code "et"
et=Estonia
#XTXT Human readable text for language code "fa"
fa=Parsi
#XTXT Human readable text for language code "fi"
fi=Finland
#XTXT Human readable text for language code "fr-CA"
fr-CA=Perancis (Kanada)
#XTXT Human readable text for language code "fr"
fr=Perancis
#XTXT Human readable text for language code "de"
de=Jerman
#XTXT Human readable text for language code "el"
el=Greek
#XTXT Human readable text for language code "he"
he=Ibrani
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Hungary
#XTXT Human readable text for language code "is"
is=Iceland
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Itali
#XTXT Human readable text for language code "ja"
ja=Jepun
#XTXT Human readable text for language code "kk"
kk=Kazakhstan
#XTXT Human readable text for language code "ko"
ko=Korea
#XTXT Human readable text for language code "lv"
lv=Latvia
#XTXT Human readable text for language code "lt"
lt=Lithuania
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norway
#XTXT Human readable text for language code "pl"
pl=Poland
#XTXT Human readable text for language code "pt"
pt=Portugis (Brasil)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugis (Portugal)
#XTXT Human readable text for language code "ro"
ro=Romania
#XTXT Human readable text for language code "ru"
ru=Rusia
#XTXT Human readable text for language code "sr"
sr=Serbia
#XTXT Human readable text for language code "sh"
sh=Serbo-Croatia
#XTXT Human readable text for language code "sk"
sk=Slovak
#XTXT Human readable text for language code "sl"
sl=Slovenia
#XTXT Human readable text for language code "es"
es=Sepanyol
#XTXT Human readable text for language code "es-MX"
es-MX=Sepanyol (Mexico)
#XTXT Human readable text for language code "sv"
sv=Sweden
#XTXT Human readable text for language code "th"
th=Thai
#XTXT Human readable text for language code "tr"
tr=Turki
#XTXT Human readable text for language code "uk"
uk=Ukraine
#XTXT Human readable text for language code "vi"
vi=Vietnam
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Padam Ruang
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Adakah anda pasti ingin menggerakkan ruang "{0}" ke tong kitar semula?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Adakah anda pasti ingin menggerakkan ruang {0} yang dipilih ke tong kitar semula?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Adakah anda pasti ingin memadam ruang "{0}"? Tindakan ini tidak boleh dibatalkan.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Adakah anda pasti ingin padam {0} ruang yang dipilih? Tindakan ini tidak boleh dibatalkan. Kandungan berikut akan dipadamkan {1}:
#XTXT: permanently
permanently=terus
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Kandungan berikut akan terpadam {0} dan tidak boleh dipulihkan semula:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Taip {0} untuk sahkan pemadaman.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Semak ejaan anda dan cuba semula.
#XTXT: All Spaces
allSpaces=Semua Ruang
#XTXT: All data
allData=Semua objek dan data terkandung dalam ruang
#XTXT: All connections
allConnections=Semua sambungan tertakrif dalam ruang
#XFLD: Space tile selection box tooltip
clickToSelect=Klik untuk Pilih
#XTXT: All database users
allDatabaseUsers=Semua objek dan data terkandung dalam apa-apa skema SQL Terbuka berkaitan dengan ruang
#XFLD: remove members button tooltip
deleteUsers=Keluarkan Ahli
#XTXT: Space long description text
description=Perihalan (Maksimum 4000 Aksara)
#XFLD: Add Members button tooltip
addUsers=Tambah Ahli
#XFLD: Add Users button tooltip
addUsersTooltip=Tambah Pengguna
#XFLD: Edit Users button tooltip
editUsersTooltip=Edit Pengguna
#XFLD: Remove Users button tooltip
removeUsersTooltip=Keluarkan Pengguna
#XFLD: Searchfield placeholder
filter=Cari
#XCOL: Users table-view column health
health=Kesihatan
#XCOL: Users table-view column access
access=Capaian
#XFLD: No user found nodatatext
noDataText=Tiada Pengguna Ditemui
#XTIT: Members dialog title
selectUserDialogTitle=Tambah Ahli
#XTIT: User dialog title
addUserDialogTitle=Tambah Pengguna
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Padam Sambungan
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Padam Sambungan
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Adakah anda pasti ingin padam sambungan dipilih? Ia akan dikeluarkan terus.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Pilih Sambungan
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Kongsi Sambungan
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Sambungan Dikongsi
#XFLD: Add remote source button tooltip
addRemoteConnections=Tambah Sambungan
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Keluarkan Sambungan
#XFLD: Share remote source button tooltip
shareConnections=Kongsi Sambungan
#XFLD: Tile-layout tooltip
tileLayout=Tataletak Jubin
#XFLD: Table-layout tooltip
tableLayout=Tataletak Jadual
#XMSG: Success message after creating space
createSpaceSuccessMessage=Ruang dicipta
#XMSG: Success message after copying space
copySpaceSuccessMessage=Menyalin ruang "{0}" kepada ruang "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Pengerahan ruang telah bermula
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Kemas kini Apache Spark telah bermula
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Kemas kini Apache Spark tidak berjaya
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Butiran ruang dikemas kini
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Ruang dibuka sementara
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Ruang dipadam
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Ruang dipadam
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Ruang dipulihkan
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Ruang dipulihkan
#YMSE: Error while updating settings
updateSettingsFailureMessage=Tetapan ruang tidak dapat dikemas kini.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Data lake telah diumpukkan ke ruang lain. Hanya satu ruang boleh mencapai data lake pada satu masa.
#YMSE: Error while updating data lake option
virtualTablesExists=Anda tidak boleh batalkan umpukan data lake daripada ruang ini kerana masih terdapat kebersandaran pada jadual maya*. Padam jadual maya ini untuk batalkan umpukan data lake daripada ruang ini.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Ruang tidak dapat dibuka.
#YMSE: Error while creating space
createSpaceError=Ruang tidak dapat dicipta.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Ruang dengan nama {0} telah wujud.
#YMSE: Error while deleting a single space
deleteSpaceError=Ruang tidak dapat dipadam.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Ruang “{0}” tidak berfungsi dengan betul. Cuba padamnya semula. Jika ia tidak berjaya, minta pentadbir padam ruang anda atau buka tiket.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Data ruang dalam Fail tidak dapat dipadam.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Pengguna tidak dapat dikeluarkan.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Skema tidak dapat dikeluarkan.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Sambungan tidak dapat dikeluarkan.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Data ruang tidak dapat dipadam.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Ruang tidak dapat dipadam.
#YMSE: Error while restoring a single space
restoreSpaceError=Ruang tidak dapat dipulihkan.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Ruang tidak dapat dipulihkan.
#YMSE: Error while creating users
createUsersError=Pengguna tidak dapat ditambah.
#YMSE: Error while removing users
removeUsersError=Pengguna tidak dapat dikeluarkan.
#YMSE: Error while removing user
removeUserError=tidak dapat mengeluarkan pengguna.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Kami tidak dapat menambah pengguna pada fungsi berskop yang dipilih. \n\n Anda tidak boleh menambah diri sendiri pada fungsi berskop. Anda cuba minta pentadbir anda untuk menambah anda pada fungsi berskop.
#YMSE: Error assigning user to the space
userAssignError=Kami tidak dapat mengumpukkan pengguna kepada ruang. \n\n Pengguna telah diumpukkan kepada nombor (100) maksimum yang dibenarkan bagi ruang merentasi fungsi berskop.
#YMSE: Error assigning users to the space
usersAssignError=Kami tidak dapat mengumpukkan pengguna kepada ruang. \n\n Pengguna telah diumpukkan kepada nombor (100) maksimum yang dibenarkan bagi ruang merentasi fungsi berskop.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Cuba dapatkan semula pengguna sebentar lagi.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Cuba dapatkan semula fungsi diskop.
#YMSE: Error while fetching members
fetchUserError=Ahli tidak boleh diperoleh. Cuba sekali lagi.
#YMSE: Error while loading run-time database
loadRuntimeError=Kami tidak dapat memuatkan maklumat daripada pangkalan data masa jalanan.
#YMSE: Error while loading spaces
loadSpacesError=Maaf, sesuatu telah berlaku ketika cuba mendapatkan ruang anda.
#YMSE: Error while loading haas resources
loadStorageError=Maaf, sesuatu telah berlaku ketika cuba mendapatkan data storan.
#YMSE: Error no data could be loaded
loadDataError=Maaf, sesuatu telah berlaku ketika cuba mendapatkan data anda.
#XFLD: Click to refresh storage data
clickToRefresh=Klik di sini untuk cuba semula.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Padam Ruang
#XCOL: Spaces table-view column name
name=Nama
#XCOL: Spaces table-view deployment status
deploymentStatus=Status Pengerahan
#XFLD: Disk label in space details
storageLabel=Cakera (GB)
#XFLD: In-Memory label in space details
ramLabel=Ingatan (GB)
#XFLD: Memory label on space card
memory=Ingatan untuk Storan
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Storan Ruang
#XFLD: Storage Type label in space details
storageTypeLabel=Jenis Storan
#XFLD: Enable Space Quota
enableSpaceQuota=Dayakan Kuota Ruang
#XFLD: No Space Quota
noSpaceQuota=Tiada Kuota Ruang
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Pangkalan Data SAP HANA (Cakera dan In-Memory)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Fail Data Lake SAP HANA
#XFLD: Available scoped roles label
availableRoles=Fungsi Berskop Tersedia
#XFLD: Selected scoped roles label
selectedRoles=Fungsi Berskop Terpilih
#XCOL: Spaces table-view column models
models=Model
#XCOL: Spaces table-view column users
users=Pengguna
#XCOL: Spaces table-view column connections
connections=Sambungan
#XFLD: Section header overview in space detail
overview=Paparan Keseluruhan
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Aplikasi
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Umpukan Tugas
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU
#XFLD: Memory label in Apache Spark section
memoryLabel=Ingatan (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Konfigurasi Ruang
#XFLD: Space Source label
sparkApplicationLabel=Aplikasi
#XFLD: Cluster Size label
clusterSizeLabel=Saiz Kluster
#XFLD: Driver label
driverLabel=Pemacu
#XFLD: Executor label
executorLabel=Pelaksana
#XFLD: max label
maxLabel=Maksimum Digunakan
#XFLD: TrF Default label
trFDefaultLabel=Lalai Aliran Perubahan
#XFLD: Merge Default label
mergeDefaultLabel=Gabungkan Lalai
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimumkan Lalai
#XFLD: Deployment Default label
deploymentDefaultLabel=Pengerahan Jadual Tempatan (Fail)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Jenis Objek
#XFLD: Task activity label
taskActivityLabel=Aktiviti
#XFLD: Task Application ID label
taskApplicationIDLabel=Aplikasi Lalai
#XFLD: Section header in space detail
generalSettings=Tetapan Am
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Ruang ini kini dikunci oleh sistem.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Perubahan dalam bahagian ini akan diatur duduk dengan segera.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Ambil perhatian bahawa penukaran nilai ini boleh menyebabkan isu prestasi.
#XFLD: Button text to unlock the space again
unlockSpace=Buka Ruang
#XFLD: Info text for audit log formatted message
auditLogText=Dayakan log audit untuk merekodkan bacaan atau tindakan perubahan (polisi audit). Pentadbir kemudiannya boleh menganalisis individu yang melaksanakan tindakan mengikut titik masa.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Log audit boleh menggunakan amaun yang besar bagi storan cakera dalam penyewa anda. Jika anda mendayakan polisi audit (bacaan atau tindakan perubahan), anda perlu sentiasa memantau penggunaan storan cakera (melalui kad Storan Cakera Digunakan dalam Pemantau Sistem) untuk mengelakkan gangguan cakera penuh, yang boleh menyebabkan gangguan perkhidmatan. Jika anda menyahdayakan polisi audit, semua entri log auditnya akan dipadam. Jika anda ingin menyimpan entri log audit, pertimbangkan untuk mengeksportnya sebelum anda menyahdayakan polisi audit.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Tunjukkan Bantuan
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Ruang ini melebihi storan ruang dan akan dikunci dalam {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=jam
#XMSG: Unit for remaining time until space is locked again
minutes=minit
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Pengauditan
#XFLD: Subsection header in space detail for auditing
auditing=Tetapan Audit Ruang
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Ruang kritikal: Storan yang digunakan melebihi 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Ruang yang sihat: Storan yang digunakan antara 6% hingga 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Ruang tidak aktif: Storan yang digunakan ialah 5% atau kurang.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Ruang kritikal: Storan yang digunakan melebihi 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Ruang yang sihat: Storan yang digunakan antara 6% hingga 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Ruang dikunci: Disekat kerana ingatan tidak mencukupi.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Ruang Dikunci
#YMSE: Error while deleting remote source
deleteRemoteError=Sambungan tidak dapat dikeluarkan.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=ID ruang tidak boleh diubah lagi.\nAksara sah A - Z, 0 - 9, dan _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Masukkan nama ruang.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Masukkan nama perniagaan.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Masukkan ID ruang.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Aksara tidak sah. Gunakan A - Z, 0 - 9, dan _ sahaja.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ID ruang telah wujud.
#XFLD: Space searchfield placeholder
search=Cari
#XMSG: Success message after creating users
createUsersSuccess=Pengguna ditambah
#XMSG: Success message after creating user
createUserSuccess=Pengguna ditambah
#XMSG: Success message after updating users
updateUsersSuccess={0} Pengguna dikemas kini
#XMSG: Success message after updating user
updateUserSuccess=Pengguna dikemas kini
#XMSG: Success message after removing users
removeUsersSuccess={0} Pengguna dikeluarkan
#XMSG: Success message after removing user
removeUserSuccess=Pengguna dikeluarkan
#XFLD: Schema name
schemaName=Nama Skema
#XFLD: used of total
ofTemplate={0} daripada {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Cakera Diumpukkan ({0} daripada {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Ingatan Diumpukkan ({0} daripada {1})
#XFLD: Storage ratio on space
accelearationRAM=Pecutan Ingatan
#XFLD: No Storage Consumption
noStorageConsumptionText=Tiada kuota storan diumpukkan.
#XFLD: Used disk label in space overview
usedStorageTemplate=Cakera Digunakan untuk Storan ({0} daripada {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Ingatan Digunakan untuk Storan ({0} daripada {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} daripada {1} Cakera Digunakan untuk Storan
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} daripada {1} Ingatan Digunakan
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} daripada {1} Cakera Diumpukkan
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} daripada {1} Ingatan Diumpukkan
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Data Ruang: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Data Lain: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Pertimbangkan untuk lanjutkan rancangan anda, atau hubungi Sokongan SAP.
#XCOL: Space table-view column used Disk
usedStorage=Cakera Digunakan untuk Storan
#XCOL: Space monitor column used Memory
usedRAM=Ingatan Digunakan untuk Storan
#XCOL: Space monitor column Schema
tableSchema=Skema
#XCOL: Space monitor column Storage Type
tableStorageType=Jenis Storan
#XCOL: Space monitor column Table Type
tableType=Jenis Jadual
#XCOL: Space monitor column Record Count
tableRecordCount=Kiraan Rekod
#XFLD: Assigned Disk
assignedStorage=Cakera Diumpukkan untuk Storan
#XFLD: Assigned Memory
assignedRAM=Ingatan Diumpukkan untuk Storan
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Storan Digunakan
#XFLD: space status
spaceStatus=Status Ruang
#XFLD: space type
spaceType=Jenis Ruang
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW bridge
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Produk Pembekal Data
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Anda tidak boleh memadam ruang {0} kerana jenis ruangnya ialah {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Anda tidak boleh memadam ruang {0} yang dipilih. Ruang dengan jenis ruang berikut tidak boleh dipadam: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Pemantau
#XFLD: Tooltip for edit space button
editSpace=Edit Ruang
#XMSG: Deletion warning in messagebox
deleteConfirmation=Adakah anda pasti ingin padam ruang ini?
#XFLD: Tooltip for delete space button
deleteSpace=Padam Ruang
#XFLD: storage
storage=Cakera untuk Storan
#XFLD: username
userName=Nama Pengguna
#XFLD: port
port=Port
#XFLD: hostname
hostName=Nama Hos
#XFLD: password
password=Kata Laluan
#XBUT: Request new password button
requestPassword=Minta Kata Laluan Baharu
#YEXP: Usage explanation in time data section
timeDataSectionHint=Cipta jadual masa dan dimensi untuk digunakan dalam model dan laporan anda.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Adakah anda ingin data dalam ruang guna habis oleh alat atau aplikasi lain? Jika demikian, cipta satu atau beberapa pengguna yang dapat capai data dalam ruang anda dan pilih sama ada anda ingin semua data ruang seterusnya guna habis secara lalai atau tidak.
#XTIT: Create schema popup title
createSchemaDialogTitle=Cipta Skema SQL Terbuka
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Cipta Jadual Masa dan Dimensi
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Edit Jadual Masa dan Dimensi
#XTIT: Time Data token title
timeDataTokenTitle=Data Masa
#XTIT: Time Data token title
timeDataUpdateViews=Kemas Kini Paparan Data Masa
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Ciptaan sedang dijalankan...
#XFLD: Time Data token creation error label
timeDataCreationError=Ciptaan gagal. Cuba sekali lagi.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Tetapan Jadual Masa
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Jadual Pertukaran
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Dimensi Masa
#XFLD: Time Data dialog time range label
timeRangeHint=Takrifkan julat masa.
#XFLD: Time Data dialog time data table label
timeDataHint=Berikan nama untuk jadual anda.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Berikan nama untuk dimensi anda.
#XFLD: Time Data Time range description label
timerangeLabel=Julat Masa
#XFLD: Time Data dialog from year label
fromYearLabel=Dari Tahun
#XFLD: Time Data dialog to year label
toYearLabel=Hingga Tahun
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Jenis Kalendar
#XFLD: Time Data dialog granularity label
granularityLabel=Granulariti
#XFLD: Time Data dialog technical name label
technicalNameLabel=Nama Teknikal
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Jadual Pertukaran untuk Suku Tahun
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Jadual Pertukaran untuk Bulan
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Jadual Pertukaran untuk Hari
#XFLD: Time Data dialog year label
yearLabel=Dimensi Tahun
#XFLD: Time Data dialog quarter label
quarterLabel=Dimensi Suku Tahun
#XFLD: Time Data dialog month label
monthLabel=Dimensi Bulan
#XFLD: Time Data dialog day label
dayLabel=Dimensi Hari
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregory
#XFLD: Time Data dialog time granularity day label
day=Hari
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Panjang maksimum 1000 aksara dicapai.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Julat masa maksimum ialah 150 tahun.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=“Dari Tahun” perlu lebih rendah dari “Hingga Tahun”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Dari Tahun" mestilah 1900 atau lebih tinggi.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=“Hingga Tahun” perlu lebih tinggi dari “Dari Tahun”
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=“Hingga Tahun” mestilah lebih rendah dari 100 tambahan tahun semasa
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Meningkatkan "Dari Tahun" boleh menyebabkan kehilangan data
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Merendahkan "Hingga Tahun" boleh menyebabkan kehilangan data
#XMSG: Time Data creation validation error message
timeDataValidationError=Beberapa medan tidak sah. Semak medan diperlukan untuk teruskannya.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Adakah anda pasti ingin padam data?
#XMSG: Time Data creation success message
createTimeDataSuccess=Data masa dicipta
#XMSG: Time Data update success message
updateTimeDataSuccess=Data masa dikemas kini
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Data masa dipadam
#XMSG: Time Data creation error message
createTimeDataError=Sesuatu telah berlaku ketika cuba mencipta data masa.
#XMSG: Time Data update error message
updateTimeDataError=Sesuatu telah berlaku ketika cuba mengemas kini data masa.
#XMSG: Time Data creation error message
deleteTimeDataError=Sesuatu telah berlaku ketika cuba memadam data masa.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Data masa tidak dapat dimuatkan.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Amaran
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Data Masa tidak dapat dipadam kerana ia sedang digunakan dalam model lain.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Data Masa tidak dapat dipadam kerana ia sedang digunakan dalam model lain.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Medan ini diperlukan dan tidak boleh dibiarkan kosong.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Buka dalam Explorer Pangkalan Data
#YMSE: Dimension Year
dimensionYearView=Dimensi "Tahun"
#YMSE: Dimension Year
dimensionQuarterView=Dimensi "Suku Tahun"
#YMSE: Dimension Year
dimensionMonthView=Dimensi "Bulan"
#YMSE: Dimension Year
dimensionDayView=Dimensi "Hari"
#XFLD: Time Data deletion object title
timeDataUsedIn=(digunakan dalam {0} model)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(digunakan dalam 1 model)
#XFLD: Time Data deletion table column provider
provider=Pembekal
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Kebersandaran
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Cipta Pengguna untuk Skema Ruang
#XFLD: Create schema button
createSchemaButton=Cipta Skema SQL Terbuka
#XFLD: Generate TimeData button
generateTimeDataButton=Cipta Jadual Masa dan Dimensi
#XFLD: Show dependencies button
showDependenciesButton=Tunjukkan Kebersandaran
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Untuk melakukan operasi ini, pengguna anda mestilah ahli ruang.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Cipta Pengguna Skema Ruang
#YMSE: API Schema users load error
loadSchemaUsersError=Senarai pengguna tidak dapat dimuatkan.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Butiran Pengguna Skema Ruang
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Adakah anda pasti ingin padam pengguna dipilih?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Pengguna dipadam.
#YMSE: API Schema user deletion error
userDeleteError=Pengguna tidak dapat dipadam.
#XFLD: User deleted
userDeleted=Pengguna telah dipadam.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Amaran
#XMSG: Remove user popup text
removeUserConfirmation=Adakah anda pasti ingin keluarkan pengguna? Pengguna dan fungsi berskop yang diumpukkan akan dikeluarkan daripada ruang.
#XMSG: Remove users popup text
removeUsersConfirmation=Adakah anda pasti ingin keluarkan pengguna? Pengguna dan fungsi berskop yang diumpukkan akan dikeluarkan daripada ruang.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Keluarkan
#YMSE: No data text for available roles
noDataAvailableRoles=Ruang tidak ditambahkan pada mana-mana fungsi berskop. \n Untuk membolehkan penambahan pengguna pada ruang, ia mesti ditambahkan dahulu pada satu atau lebih fungsi berskop.
#YMSE: No data text for selected roles
noDataSelectedRoles=Tiada fungsi berskop terpilih
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Butiran Konfigurasi Skema SQL Terbuka
#XFLD: Label for Read Audit Log
auditLogRead=Dayakan Log Audit untuk Baca Operasi
#XFLD: Label for Change Audit Log
auditLogChange=Dayakan Log Audit untuk Ubah Operasi
#XFLD: Label Audit Log Retention
auditLogRetention=Simpan Log untuk
#XFLD: Label Audit Log Retention Unit
retentionUnit=Hari
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Masukkan keseluruhan nombor antara {0} hingga {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Gunakan Data Skema Ruang
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Hentikan Penggunaan Data Skema Ruang
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Skema SQL Terbuka mungkin menggunakan data skema ruang anda. Jika anda hentikan penggunaannya, model berasaskan data skema ruang mungkin tidak berfungsi lagi.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Hentikan Penggunaan
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Ruang ini digunakan untuk mencapai data lake
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Data Lake Didayakan
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Had Ingatan Dicapai
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Had Storan Dicapai
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Had Storan Minimum Dicapai
#XFLD: Space ram tag
ramLimitReachedLabel=Had Ingatan Dicapai
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Had Ingatan Minimum Dicapai
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Anda telah mencapai had storan ruang yang diumpukkan {0}. Umpukkan lebih banyak storan untuk ruang.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Had Storan Sistem Dicapai
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Anda telah mencapai had storan sistem {0}. Apa-apa storan untuk ruang kini tidak boleh diumpukkan.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Memadam skema SQL terbuka ini juga akan memadam terus semua objek yang disimpan dan perkaitan yang diselenggarakan dalam skema. Teruskan?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Skema dipadam
#YMSE: Error while deleting schema.
schemaDeleteError=Skema tidak dapat dipadam.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Skema dikemas kini
#YMSE: Error while updating schema.
schemaUpdateError=Skema tidak dapat dikemas kini.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Kami telah memberikan kata laluan untuk skema ini. Jika anda terlupa kata laluan atau kehilangannya, anda boleh meminta yang baharu. Ingat untuk salin atau simpan kata laluan baharu.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Salin kata laluan anda. Anda akan memerlukannya untuk membuat sambungan ke skema ini. Jika terlupa kata laluan, anda boleh membuka dialog ini untuk menetapkannya semula.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Nama ini tidak boleh diubah selepas skema dicipta.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=SGL Terbuka
#XFLD: Space schema section sub headline
schemasSpace=Ruang
#XFLD: HDI Container section header
HDIContainers=Bekas HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Tambah Bekas HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Keluarkan Bekas HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Dayakan Capaian
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Tiada bekas HDI yang ditambah.
#YMSE: No data text for Timedata section
noDataTimedata=Tiada jadual masa dan dimensi dicipta.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Tidak boleh memuatkan jadual dan dimensi masa kerana pangkalan data masa jalanan tidak tersedia.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Tidak berjaya memuatkan Bekas HDI kerana pangkalan data masa jalanan tidak tersedia.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Bekas HDI tidak dapat diperoleh. Cuba sekali lagi.
#XFLD Table column header for HDI Container names
HDIContainerName=Nama Bekas HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Dayakan Capaian
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Anda boleh mendayakan SAP SQL Data Warehousing pada penyewa SAP Datasphere anda untuk menukar data antara bekas HDI anda dan ruang SAP Datasphere anda tanpa memerlukan pergerakan data.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Untuk melakukannya, buka tiket sokongan dengan mengklik butang di bawah.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Setelah anda proses tiket anda, anda mesti bina satu atau lebih bekas HDI baharu dalam pangkalan data masa jalanan SAP Datasphere. Kemudian, anda gantikan butang Dayakan Capaian dengan butang + dalam bahagian Bekas HDI untuk semua ruang SAP Datasphere anda, dan anda boleh tambahkan bekas anda kepada ruang.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Perlukan maklumat lanjut? Pergi ke %%0. Untuk maklumat terperinci tentang perkara yang perlu dimasukkan dalam tiket, lihat %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=Bantuan SAP
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Nota SAP 3057059
#XBUT: Open Ticket Button Text
openTicket=Buka Tiket
#XBUT: Add Button Text
add=Tambah
#XBUT: Next Button Text
next=Seterusnya
#XBUT: Edit Button Text
editUsers=Edit
#XBUT: create user Button Text
createUser=Cipta
#XBUT: Update user Button Text
updateUser=Pilih
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Tambah Bekas HDI yang tidak diumpukkan
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Kami tidak menemui bekas yang tidak diumpukkan. \n Bekas yang anda cari mungkin telah diumpukkan ke ruang.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Bekas HDI yang diumpukkan tidak dapat dimuatkan.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Bekas HDI tidak dapat dimuatkan.
#XMSG: Success message
succeededToAddHDIContainer=Bekas HDI ditambah
#XMSG: Success message
succeededToAddHDIContainerPlural=Bekas HDI ditambah
#XMSG: Success message
succeededToDeleteHDIContainer=Bekas HDI dikeluarkan
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Bekas HDI dikeluarkan
#XFLD: Time data section sub headline
timeDataSection=Jadual Masa dan Dimensi
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Baca
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Ubah
#XFLD: Remote sources section sub headline
allconnections=Umpukan Sambungan
#XFLD: Remote sources section sub headline
localconnections=Sambungan Setempat
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=Penyambung Terbuka SAP
#XFLD: User section sub headline
memberassignment=Umpukan Ahli
#XFLD: User assignment section sub headline
userAssignment=Umpukan Pengguna
#XFLD: User section Access dropdown Member
member=Ahli
#XFLD: User assignment section column name
user=Nama Pengguna
#XTXT: Selected role count
selectedRoleToolbarText=Terpilih: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Sambungan
#XTIT: Space detail section data access title
detailsSectionDataAccess=Capaian Skema
#XTIT: Space detail section time data title
detailsSectionGenerateData=Data Masa
#XTIT: Space detail section members title
detailsSectionUsers=Ahli
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Pengguna
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Kehabisan storan
#XTIT: Storage distribution
storageDistributionPopoverTitle=Storan Cakera Digunakan
#XTXT: Out of Storage popover text
insufficientStorageText=Untuk mencipta ruang baharu, kurangkan storan ruang lain yang diumpukkan atau padamkan ruang yang tidak diperlukan lagi. Anda boleh meningkatkan jumlah storan sistem anda dengan memanggil Uruskan Pelan.
#XMSG: Space id length warning
spaceIdLengthWarning=Melebihi maksimum {0} aksara.
#XMSG: Space name length warning
spaceNameLengthWarning=Melebihi maksimum {0} aksara.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Jangan guna awalan {0} untuk elak kemungkinan konflik.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Skema SQL terbuka tidak dapat dimuatkan.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Skema SQL terbuka tidak dapat dicipta.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Tidak dapat muatkan semua sambungan jauh.
#YMSE: Error while loading space details
loadSpaceDetailsError=Cuba muatkan butiran ruang semula.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Ruang tidak dapat diatur duduk.
#YMSE: Error while copying space details
copySpaceDetailsError=Ruang tidak dapat disalin.
#YMSE: Error while loading storage data
loadStorageDataError=Data storan tidak dapat dimuatkan.
#YMSE: Error while loading all users
loadAllUsersError=Tidak dapat muatkan semua pengguna.
#YMSE: Failed to reset password
resetPasswordError=Kata laluan tidak dapat ditetapkan semula.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Kata laluan baharu ditetapkan untuk skema
#YMSE: DP Agent-name too long
DBAgentNameError=Nama agen DP terlalu panjang.
#YMSE: Schema-name not valid.
schemaNameError=Nama skema tidak sah.
#YMSE: User name not valid.
UserNameError=Nama pengguna tidak sah.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Penggunaan mengikut Jenis Storan
#XTIT: Consumption by Schema
consumptionSchemaText=Penggunaan mengikut Skema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Keseluruhan Penggunaan Jadual mengikut Skema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Keseluruhan Penggunaan mengikut Jenis Skema
#XTIT: Tables
tableDetailsText=Butiran Jadual
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Penggunaan Storan Jadual
#XFLD: Table Type label
tableTypeLabel=Jenis Jadual
#XFLD: Schema label
schemaLabel=Skema
#XFLD: reset table tooltip
resetTable=Tetapkan Semula Jadual
#XFLD: In-Memory label in space monitor
inMemoryLabel=Ingatan
#XFLD: Disk label in space monitor
diskLabel=Cakera
#XFLD: Yes
yesLabel=Ya
#XFLD: No
noLabel=Tidak
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Adakah anda ingin data dalam ruang ini guna habis mengikut lalai?
#XFLD: Business Name
businessNameLabel=Nama Perniagaan
#XFLD: Refresh
refresh=Segar Semula
#XMSG: No filter results title
noFilterResultsTitle=Tetapan penapis anda tidak menunjukkan apa-apa data.
#XMSG: No filter results message
noFilterResultsMsg=Cuba perhalusi tetapan penapis anda dan jika data masih tidak ditunjukkan, cipta beberapa jadual dalam Pembina Data. Setelah storan digunakan, anda boleh memantaunya di sini.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Pangkalan data masa jalanan tidak tersedia.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Oleh sebab pangkalan data masa jalanan tidak tersedia, sesetengah ciri dinyahdayakan dan kami tidak boleh memaparkan apa-apa maklumat pada halaman ini.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Pengguna skema ruang tidak dapat dicipta.
#YMSE: Error User name already exists
userAlreadyExistsError=Nama pengguna telah wujud.
#YMSE: Error Authentication failed
authenticationFailedError=Pengesahan gagal.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Pengguna dikunci disebabkan terlalu banyak log masuk gagal. Minta kata laluan baharu untuk buka kunci pengguna.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Kata laluan baharu ditetapkan dan pengguna dibuka
#XMSG: user is locked message
userLockedMessage=Pengguna dikunci.
#XCOL: Users table-view column Role
spaceRole=Fungsi
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Fungsi Berskop
#XCOL: Users table-view column Space Admin
spaceAdmin=Pentadbir Ruang
#XFLD: User section dropdown value Viewer
viewer=Pemapar
#XFLD: User section dropdown value Modeler
modeler=Modeler
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Penyepadu Data
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Pentadbir Ruang
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Fungsi ruang dikemas kini
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Fungsi Ruang tidak berjaya dikemas kini.
#XFLD:
databaseUserNameSuffix=Akhiran Nama Pengguna Pangkalan Data
#XTXT: Space Schema password text
spaceSchemaPasswordText=Untuk menyediakan sambungan ke skema ini, salin kata laluan anda. Sekiranya anda terlupa kata laluan, anda boleh meminta yang baharu.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Platform Cloud
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Untuk menyediakan capaian melalui pengguna ini, dayakan penggunaan dan salin kelayakan. Sekiranya anda hanya boleh menyalin kelayakan tanpa kata laluan, pastikan anda menambah kata laluan kemudian.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Dayakan Penggunaan dalam Platform Cloud
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Kelayakan untuk Perkhidmatan Disediakan Pengguna:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Kelayakan untuk Perkhidmatan Disediakan Pengguna (Tanpa Kata Laluan):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Salin Kelayakan Tanpa Kata Laluan
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Salin Kelayakan Penuh
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Salin Kata Laluan
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Kelayakan disalin ke papan klip
#XMSG: Password copied to clipboard
passwordCopiedMessage=Kata laluan disalin ke papan klip
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Cipta Pengguna Pangkalan Data
#XMSG: Database Users section title
databaseUsers=Pengguna Pangkalan Data
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Butiran Pengguna Pangkalan Data
#XFLD: database user read audit log
databaseUserAuditLogRead=Dayakan Log Audit bagi Baca Operasi dan Simpan Log untuk
#XFLD: database user change audit log
databaseUserAuditLogChange=Dayakan Log Audit bagi Ubah Operasi dan Simpan Log untuk
#XMSG: Cloud Platform Access
cloudPlatformAccess=Capaian Platform Cloud
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Sediakan capaian ke bekas HANA Deployment Infrastructure (HDI) melalui pengguna pangkalan data ini. Untuk menyambung ke bekas HDI anda, pemodelan SQL mestilah diaktifkan
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Dayakan Penggunaan HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Adakah anda ingin data dalam ruang anda guna habis oleh aplikasi atau peralatan lain?
#XFLD: Enable Consumption
enableConsumption=Dayakan Penggunaan SQL
#XFLD: Enable Modeling
enableModeling=Dayakan Pemodelan SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Pengingesan Data
#XMSG: Privileges for Data Consumption
privilegesConsumption=Penggunaan Data untuk Alat Luaran
#XFLD: SQL Modeling
sqlModeling=Pemodelan SQL
#XFLD: SQL Consumption
sqlConsumption=Penggunaan SQL
#XFLD: enabled
enabled=Didayakan
#XFLD: disabled
disabled=Dinyahdayakan
#XFLD: Edit Privileges
editPrivileges=Edit Keistimewaan
#XFLD: Open Database Explorer
openDBX=Buka Explorer Pangkalan Data
#XFLD: create database user hint
databaseCreateHint=Ambil perhatian bahawa ia tidak boleh mengubah nama anda lagi selepas disimpan.
#XFLD: Internal Schema Name
internalSchemaName=Nama Skema Dalaman
#YMSE: Failed to load database users
loadDatabaseUserError=Gagal untuk memuat pengguna pangkalan data
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Gagal untuk memadam pengguna pangkalan data
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Pengguna pangkalan data dipadam
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Pengguna pangkalan data dipadam
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Pengguna pangkalan data dicipta
#YMSE: Failed to create database user
createDatabaseUserError=Gagal untuk mencipta pengguna pangkalan data
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Pengguna pangkalan data dikemas kini
#YMSE: Failed to update database user
updateDatabaseUserError=Gagal untuk mengemas kini pengguna pangkalan data
#XFLD: HDI Consumption
hdiConsumption=Penggunaan HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Capaian Pangkalan Data
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Jadikan data ruang anda boleh digunakan dengan lalai. Model dalam pembina akan membenarkan data boleh digunakan secara automatik.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Penggunaan Lalai Data Ruang:
#XFLD: Database User Name
databaseUserName=Nama Pengguna Pangkalan Data
#XMSG: Database User creation validation error message
databaseUserValidationError=Beberapa medan tidak sah. Semak medan diperlukan untuk teruskannya.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Pengingesan data tidak boleh didayakan sejak pengguna ini dihijrahkan.
#XBUT: Remove Button Text
remove=Keluarkan
#XBUT: Remove Spaces Button Text
removeSpaces=Keluarkan Ruang
#XBUT: Remove Objects Button Text
removeObjects=Keluarkan Objek
#XMSG: No members have been added yet.
noMembersAssigned=Tiada ahli telah ditambah lagi.
#XMSG: No users have been added yet.
noUsersAssigned=Tiada pengguna telah ditambah lagi.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Tiada pengguna pangkalan data telah dicipta, atau penapis anda tidak menunjukkan apa-apa data.
#XMSG: Please enter a user name.
noDatabaseUsername=Masukkan nama pengguna.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Nama pengguna terlalu panjang. Gunakan nama yang pendek.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Tiada keistimewaan telah didayakan dan pengguna pangkalan data ini akan mempunyai kefungsian yang terhad. Adakah anda ingin teruskan?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Untuk dayakan log audit bagi mengubah operasi, pengingesan data perlu didayakan juga. Adakah anda ingin melakukannya?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Untuk dayakan log audit bagi membaca operasi, pengingesan data perlu didayakan juga. Adakah anda ingin melakukannya?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Untuk dayakan penggunaan HDI, pengingesan data dan penggunaan data perlu didayakan juga. Adakah anda ingin melakukannya?
#XMSG:
databaseUserPasswordText=Untuk menyediakan sambungan ke pengguna pangkalan data ini, salin kata laluan anda. Sekiranya anda terlupa kata laluan, anda boleh meminta yang baharu.
#XTIT: Space detail section members title
detailsSectionMembers=Ahli
#XMSG: New password set
newPasswordSet=Kata laluan baharu ditetapkan
#XFLD: Data Ingestion
dataIngestion=Pengingesan Data
#XFLD: Data Consumption
dataConsumption=Penggunaan Data
#XFLD: Privileges
privileges=Keistimewaan
#XFLD: Enable Data ingestion
enableDataIngestion=Dayakan Pengingesan Data
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Log masuk operasi baca dan ubah untuk pengingesan data.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Jadikan ruang anda tersedia dalam bekas HDI anda.
#XFLD: Enable Data consumption
enableDataConsumption=Dayakan Penggunaan Data
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Benarkan aplikasi atau alat lain menggunakan data ruang anda.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Untuk menyediakan capaian melalui pengguna pangkalan data ini, salin kelayakan untuk perkhidmatan disediakan pengguna. Sekiranya anda hanya boleh menyalin kelayakan tanpa kata laluan, pastikan anda menambah kata laluan kemudian.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Kapasiti Masa Jalanan Aliran Data ({0}:{1} jam dari {2} jam)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Tidak berjaya memuat data kapasiti masa jalanan aliran data
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Pengguna boleh memperuntukkan penggunaan data kepada pengguna lain.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Mendayakan Penggunaan Data dengan Pilihan Peruntukan
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Untuk mendayakan penggunaan data dengan data pilihan peruntukan, penggunaan perlu didayakan. Adakah anda ingin mendayakan kedua-duanya?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Dayakan Automated Predictive Library (APL) dan Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Anda boleh gunakan fungsi machine learning terbenam SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Polisi Kata Laluan
#XMSG: Password Policy
passwordPolicyHint=Dayakan atau nyahdayakan polisi kata laluan dikonfigurasi di sini.
#XFLD: Enable Password Policy
enablePasswordPolicy=Dayakan Polisi Kata Laluan
#XMSG: Read Access to the Space Schema
readAccessTitle=Capaian Bacaan ke Skema Ruang
#XMSG: read access hint
readAccessHint=Benarkan pengguna pangkalan data untuk bersambung dengan alat luaran ke skema ruang dan baca paparan yang didedahkan bagi penggunaan.
#XFLD: Space Schema
spaceSchema=Skema Ruang
#XFLD: Enable Read Access (SQL)
enableReadAccess=Dayakan Capaian Bacaan (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Benarkan pengguna untuk memberi capaian bacaan kepada pengguna lain.
#XFLD: With Grant Option
withGrantOption=Dengan Pilihan Pemberian
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Jadikan ruang anda tersedia dalam bekas HDI anda.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Dayakan Penggunaan HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Capaian Tulis ke Skema SQL Terbuka Pengguna
#XMSG: write access hint
writeAccessHint=Benarkan pengguna pangkalan data untuk bersambung dengan alat luaran ke skema SQL Terbuka pengguna bagi mencipta entiti data dan inges data untuk digunakan dalam ruang ini.
#XFLD: Open SQL Schema
openSQLSchema=Skema SQL Terbuka
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Dayakan Capaian Tulis (SQL, DDL, & DML)
#XMSG: audit hint
auditHint=Log masuk ke bacaan dan ubah operasi dalam skema SQL terbuka.
#XMSG: data consumption hint
dataConsumptionHint=Dedahkan semua paparan baharu dalam ruang mengikut lalai penggunaan. Modeler boleh membatalkan tetapan ini bagi paparan individu melalui penukaran “Dedahkan untuk Penggunaan” dalam panel sisi output paparan. Anda juga boleh memilih format paparan yang didedahkan.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Dedahkan untuk Penggunaan mengikut Lalai
#XMSG: database users hint consumption hint
databaseUsersHint2New=Cipta pengguna pangkalan data untuk bersambung dengan alat luaran ke SAP Datasphere. Tetapkan keistimewaan untuk membenarkan pengguna membaca data ruang dan mencipta entiti data (DDL) dan inges data (DML) untuk kegunaan dalam ruang.
#XFLD: Read
read=Baca
#XFLD: Read (HDI)
readHDI=Baca (HDI)
#XFLD: Write
write=Tulis
#XMSG: HDI Containers Hint
HDIContainersHint2=Dayakan capaian ke bekas SAP HANA Deployment Infrastructure (HDI) anda dalam ruang anda. Modeler boleh menggunakan artifak HDI sebagai sumber untuk paparan dan pelanggan HDI boleh mencapai data ruang anda.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Buka dialog maklumat
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Pengguna pangkalan data dikunci. Buka dialog untuk membukanya
#XFLD: Table
table=Jadual
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Sambungan Pekongsi
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Konfigurasi Sambungan Pekongsi
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Takrifkan jubin sambungan pekongsi anda sendiri dengan menambah ikon dan URL iFrame. Konfigurasi hanya tersedia untuk penyewa ini.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Nama Jubin
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Asal Mesej Pos iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ikon
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Tiada konfigurasi sambungan pekongsi boleh ditemui.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Konfigurasi sambungan pekongsi hanya boleh dipaparkan apabila pangkalan data masa jalanan telah tersedia.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Cipta Konfigurasi Sambungan Pekongsi
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Muat Naik Ikon
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Pilih (saiz maksimum daripada 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Contoh Jubin Pekongsi
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Layar
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Konfigurasi Sambungan Pekongsi berjaya dicipta.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Ralat berlaku semasa memadam konfigurasi Sambungan Pekongsi.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Konfigurasi Sambungan Pekongsi berjaya dipadam.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Ralat berlaku semasa mendapatkan Konfigurasi Sambungan Pekongsi.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Fail tidak boleh dimuat naik kerana ia melebihi saiz maksimum 200KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Cipta Konfigurasi Sambungan Pekongsi
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Padam Konfigurasi Sambungan Pekongsi.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Jubin Pekongsi tidak boleh dicipta.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Jubin Pekongsi tidak boleh dipadam.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Penetapan Semula Tetapan Penyambung SAP HANA Cloud Pelanggan tidak berjaya
#XFLD: Workload Class
workloadClass=Kelas Muatan Kerja
#XFLD: Workload Management
workloadManagement=Pengurusan Beban Kerja
#XFLD: Priority
workloadClassPriority=Keutamaan
#XMSG:
workloadManagementPriorityHint=Anda boleh tentukan keutamaan ruang ini apabila menanyakan pangkalan data. Masukkan nilai dari 1 (keutamaan terendah) hingga 8 (keutamaan tertinggi). Dalam keadaan yang ruang bersaing untuk jaluran yang tersedia, anda jalankan nilai yang mempunyai keutamaan yang lebih tinggi sebelum ruang dengan keutamaan yang lebih rendah.
#XMSG:
workloadClassPriorityHint=Anda boleh menentukan keutamaan ruang dari 0 (paling rendah) hingga 8 (paling tinggi). Penyata ruang dengan keutamaan tinggi dilaksanakan sebelum penyata ruang lain dengan keutamaan yang lebih rendah. Keutamaan lalai ialah 5. Disebabkan nilai 9 dikhaskan untuk operasi sistem, ia tidak tersedia untuk ruang.
#XFLD: Statement Limits
workloadclassStatementLimits=Had Penyata
#XFLD: Workload Configuration
workloadConfiguration=Konfigurasi Beban Kerja
#XMSG:
workloadClassStatementLimitsHint=Anda boleh menentukan nombor maksimum (atau peratusan) bagi jaluran dan GB ingatan yang boleh digunakan oleh penyataan yang dijalankan secara serentak dalam ruang. Anda boleh memasukkan mana-mana nilai atau peratusan antara 0 (tiada had) dan jumlah ingatan dan jaluran yang tersedia di penyewa. \n\n Jika anda menentukan had jaluran, pastikan bahawa ia boleh merendahkan prestasi. \n\n Jika anda menentukan had ingatan, penyata yang boleh mencapai had ingatan tidak akan dijalankan.
#XMSG:
workloadClassStatementLimitsDescription=Konfigurasi lalai menyediakan had sumber yang banyak, sambil menghalang mana-mana ruang tunggal daripada melebihi beban sistem.
#XMSG:
workloadClassStatementLimitCustomDescription=Anda boleh tetapkan jumlah jaluran dan had ingatan maksimum yang pernyataan berjalan secara serentak dalam ruang boleh gunakan.
#XMSG:
totalStatementThreadLimitHelpText=Menetapkan had jaluran terlalu rendah boleh menjejaskan prestasi pernyataan, manakala nilai yang terlalu tinggi atau 0 membolehkan ruang menggunakan semua jaluran sistem yang tersedia.
#XMSG:
totalStatementMemoryLimitHelpText=Menetapkan had ingatan terlalu rendah boleh menyebabkan masalah ingatan penuh, manakala nilai yang terlalu tinggi atau 0 membolehkan ruang menggunakan semua ingatan sistem yang tersedia.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Masukkan peratusan antara 1% dan 70% (atau nombor yang setara) bagi jumlah bilangan jaluran yang tersedia dalam penyewa anda. Menetapkan had jaluran terlalu rendah boleh menjejaskan prestasi pernyataan, manakala nilai yang terlalu tinggi boleh menjejaskan prestasi pernyataan dalam ruang lain.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Masukkan peratusan antara 1% dan {0}% (atau nombor yang setara) bagi jumlah bilangan jaluran yang tersedia dalam penyewa anda. Menetapkan had jaluran terlalu rendah boleh menjejaskan prestasi pernyataan, manakala nilai yang terlalu tinggi boleh menjejaskan prestasi pernyataan dalam ruang lain.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Masukkan nilai atau peratusan antara 0 (tiada had) dan jumlah amaun ingatan yang tersedia dalam penyewa anda. Menetapkan had ingatan terlalu rendah boleh menjejaskan prestasi pernyataan, manakala nilai yang terlalu tinggi boleh menjejaskan prestasi pernyataan dalam ruang lain.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Jumlah Had Jaluran Penyata
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Jaluran
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Jumlah Had Ingatan Penyata
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Tidak berjaya memuatkan Maklumat SAP HANA pelanggan.
#XMSG:
minimumLimitReached=Had minimum dicapai.
#XMSG:
maximumLimitReached=Had maksimum dicapai.
#XMSG: Name Taken for Technical Name
technical-name-taken=Sambungan dengan nama teknikal yang anda masukkan telah wujud. Masukkan nama lain.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Nama teknikal yang anda masukkan melebihi 40 aksara. Masukkan nama dengan aksara yang kurang.
#XMSG: Technical name field empty
technical-name-field-empty=Masukkan nama teknikal.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Anda hanya boleh menggunakan huruf (a-z), nombor (0-9) dan garis bawah (_) untuk nama.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Nama yang anda masukkan tidak boleh bermula atau berakhir dengan garis bawah (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Dayakan Had Penyata
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Tetapan
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Untuk mencipta atau mengedit sambungan, buka aplikasi Sambungan daripada navigasi sisi atau klik di sini:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Pergi ke Sambungan
#XFLD: Not deployed label on space tile
notDeployedLabel=Ruang belum diatur duduk lagi.
#XFLD: Not deployed additional text on space tile
notDeployedText=Atur duduk ruang.
#XFLD: Corrupt space label on space tile
corruptSpace=Sesuatu telah berlaku.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Cuba atur duduk semula atau hubungi sokongan
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Data Log Audit
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Data Pentadbiran
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Data Lain
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Data dalam Ruang
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Adakah anda pasti ingin buka kunci ruang?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Adakah anda pasti ingin kunci ruang?
#XFLD: Lock
lock=Kunci
#XFLD: Unlock
unlock=Buka Kunci
#XFLD: Locking
locking=Mengunci
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Anda telah kunci ruang
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Anda telah buka kunci ruang
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Anda telah kunci ruang
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Anda telah buka kunci ruang
#YMSE: Error while locking a space
lockSpaceError=Cuba kunci ruang semula.
#YMSE: Error while unlocking a space
unlockSpaceError=Cuba buka kunci ruang semula.
#XTIT: popup title Warning
confirmationWarningTitle=Amaran
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Anda telah mengunci ruang secara manual.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Sistem telah mengunci ruang kerana log audit menggunakan kuantiti GB cakera yang besar.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Sistem telah mengunci ruang kerana ia melebihi peruntukan ingatan atau storan cakera.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Adakah anda pasti ingin buka kunci ruang yang anda pilih?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Adakah anda pasti ingin kunci ruang yang anda pilih?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor Fungsi Berskop
#XTIT: ECN Management title
ecnManagementTitle=Pengurusan Ruang dan Nod Kira Elastik
#XFLD: ECNs
ecns=Nod Kira Elastik
#XFLD: ECN phase Ready
ecnReady=Sedia
#XFLD: ECN phase Running
ecnRunning=Sedang Berjalan
#XFLD: ECN phase Initial
ecnInitial=Tidak Tersedia
#XFLD: ECN phase Starting
ecnStarting=Bermula
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Cuba Mulakan Sekali Lagi
#XFLD: ECN phase Stopping
ecnStopping=Pemberhentian
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Cuba Hentikan Sekali Lagi
#XBTN: Assign Button
assign=Umpukkan Ruang
#XBTN: Start Header-Button
start=Mula
#XBTN: Update Header-Button
repair=Kemas Kini
#XBTN: Stop Header-Button
stop=Henti
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 baki jam
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} Baki Jam Blok
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} Baki Jam Blok
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Cipta Nod Kira Elastik
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Edit Nod Kira Elastik
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Padam Nod Kira Elastik
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Umpukkan Ruang
#XFLD: ECN ID
ECNIDLabel=Nod Kira Elastik
#XTXT: Selected toolbar text
selectedToolbarText=Terpilih: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Nod Kira Elastik
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Bilangan Objek
#XTIT: Object assignment - Dialog header text
selectObjects=Pilih ruang dan kerja yang anda ingin umpukkan kepada nod kira elastik anda:
#XTIT: Object assignment - Table header title: Objects
objects=Objek
#XTIT: Object assignment - Table header: Type
type=Jenis
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Ambil perhatian bahawa memadam pengguna pangkalan data akan menyebabkan pemadaman semua penjanaan entri log audit. Jika anda ingin menyimpan log audit, pertimbangkan untuk mengeksportnya sebelum anda memadam pengguna pangkalan data.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Ambil maklum bahawa membatalkan umpukan bekas HDI daripada ruang akan menyebabkan pemadaman semua penjanaan entri log audit. Jika anda ingin menyimpan entri log audit, pertimbangkan untuk mengeksportnya sebelum anda membatalkan umpukan bekas HDI.
#XTXT: All audit logs
allAuditLogs=Semua penjanaan entri log audit untuk ruang
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Ambil maklum bahawa melumpuhkan dasar audit (baca atau ubah operasi) akan menyebabkan pemadaman semua entri log auditnya. Jika anda ingin menyimpan entri log audit, pertimbangkan untuk mengeksportnya sebelum anda menyahdayakan dasar audit.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Tiada umpukan ruang atau objek lagi
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Untuk mula bekerja dengan nod kira elastik anda, umpukkan ruang atau objek kepadanya.
#XTIT: No Spaces Illustration title
noSpacesTitle=Tiada ruang dicipta lagi
#XTIT: No Spaces Illustration description
noSpacesDescription=Untuk mulakan data perolehan, cipta ruang.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Tong kitar semula kosong
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Anda boleh memulihkan ruang anda padam dari sini.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Selepas anda atur duduk ruang, pengguna pangkalan data berikut akan terpadam {0} dan tidak boleh dipulihkan semula:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Padam Pengguna Pangkalan Data
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID telah wujud.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Gunakan aksara huruf kecil a - z dan nombor 0 - 9 sahaja
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID perlu sekurang-kurangnya {0} panjang aksara.
#XMSG: ecn id length warning
ecnIdLengthWarning=Melebihi maksimum {0} aksara.
#XFLD: open System Monitor
systemMonitor=Pemantauan Sistem
#XFLD: open ECN schedule dialog menu entry
schedule=Jadual
#XFLD: open create ECN schedule dialog
createSchedule=Cipta Jadual
#XFLD: open change ECN schedule dialog
changeSchedule=Edit Jadual
#XFLD: open delete ECN schedule dialog
deleteSchedule=Padam Jadual
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Umpukkan Jadual kepada Saya
#XFLD: open pause ECN schedule dialog
pauseSchedule=Hentikan Seketika Jadual
#XFLD: open resume ECN schedule dialog
resumeSchedule=Sambung Semula Jadual
#XFLD: View Logs
viewLogs=Paparkan Log
#XFLD: Compute Blocks
computeBlocks=Sekatan Pengiraan
#XFLD: Memory label in ECN creation dialog
ecnMemory=Ingatan (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Storan (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Bilangan CPU
#XFLD: ECN updated by label
changedBy=Diubah oleh
#XFLD: ECN updated on label
changedOn=Diubah pada
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Nod kira elastik dicipta
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Cipta nod kira elastik semula
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Nod kira elastik dikemas kini
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Tidak boleh kemas kini nod kira elastik
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Nod kira elastik dipadam
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Padam nod kira elastik semula
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Memulakan nod kira elastik
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Menghentikan nod kira elastik
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Mulakan nod kira elastik semula
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Hentikan nod kira elastik semula
#XBUT: Add Object button for an ECN
assignObjects=Tambah Objek
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Umpukkan Semua Objek Secara Automatik
#XFLD: object type label to be assigned
objectTypeLabel=Jenis (Penggunaan Semantik)
#XFLD: assigned object type label
assignedObjectTypeLabel=Jenis
#XFLD: technical name label
TechnicalNameLabel=Nama Teknikal
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Pilih objek yang anda ingin tambahkan kepada nod kira elastik
#XTIT: Add objects dialog title
assignObjectsTitle=Umpukkan Objek bagi
#XFLD: object label with object count
objectLabel=Objek
#XMSG: No objects available to add message.
noObjectsToAssign=Tiada objek tersedia untuk anda umpukkan.
#XMSG: No objects assigned message.
noAssignedObjects=Tiada objek diumpukkan.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Amaran
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Padam
#XMSG: Remove objects popup text
removeObjectsConfirmation=Adakah anda pasti ingin mengeluarkan objek yang anda pilih?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Adakah anda pasti ingin mengeluarkan ruang yang anda pilih?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Keluarkan Ruang
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Objek terdedah telah dikeluarkan
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Objek terdedah telah diumpukkan
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Semua Objek Didedahkan
#XFLD: Spaces tab label
spacesTabLabel=Ruang
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Objek Didedahkan
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Anda telah mengeluarkan ruang
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Anda telah mengeluarkan ruang
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Cuba umpukkan atau keluarkan ruang semula.
#YMSE: Error while removing objects
removeObjectsError=Kami tidak dapat mengumpukkan atau mengeluarkan objek.
#YMSE: Error while removing object
removeObjectError=Kami tidak dapat mengumpukkan atau mengeluarkan objek.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Nombor yang anda telah pilih sebelumnya tidak lagi sah. Pilih nombor yang sah.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Pilih kelas prestasi yang sah.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Kelas prestasi "{0}" yang dipilih sebelumnya tidak sah pada masa ini. Pilih kelas prestasi yang sah.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Adakah anda pasti ingin memadam nod kira elastik?
#XFLD: tooltip for ? button
help=Bantuan
#XFLD: ECN edit button label
editECN=Konfigurasi
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Entiti - Model Hubungan
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Jadual Tempatan
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Jadual Jauh
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Model Analisis
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Rantaian Tugas
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Aliran Data
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Aliran Replikasi
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Aliran Perubahan
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Carian Pintar
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repositori
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Carian Syarikat
#XFLD: Technical type label for View
DWC_VIEW=Papar
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Produk Data
#XFLD: Technical type label for Data Access Control
DWC_DAC=Kawalan Capaian Data
#XFLD: Technical type label for Folder
DWC_FOLDER=Folder
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Entiti Perniagaan
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Varian Entiti Perniagaan
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Senario Tanggungjawab
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Model Fakta
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektif
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Model Penggunaan
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Sambungan Jauh
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Varian Model Fakta
#XMSG: Schedule created alert message
createScheduleSuccess=Jadual dicipta
#XMSG: Schedule updated alert message
updateScheduleSuccess=Jadual dikemas kini
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Jadual dipadam
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Jadual diumpukkan kepada anda
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Menghentikan seketika 1 jadual
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Menyambung semula 1 jadual
#XFLD: Segmented button label
availableSpacesButton=Tersedia
#XFLD: Segmented button label
selectedSpacesButton=Terpilih
#XFLD: Visit website button text
visitWebsite=Layari Laman Web
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Bahasa sumber yang sebelumnya dipilih akan dikeluarkan.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Dayakan
#XFLD: ECN performance class label
performanceClassLabel=Kelas Prestasi
#XTXT performance class memory text
memoryText=Ingatan
#XTXT performance class compute text
computeText=Pengiraan
#XTXT performance class high-compute text
highComputeText=Pengiraan Tinggi
#XBUT: Recycle Bin Button Text
recycleBin=Tong Kitar Semula
#XBUT: Restore Button Text
restore=Memulihkan
#XMSG: Warning message for new Workload Management UI
priorityWarning=Bahagian ini hanya baca sahaja. Anda boleh ubah keutamaan ruang dalam bahagian Sistem / Konfigurasi / Pengurusan Beban Kerja.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Bahagian ini hanya baca sahaja. Anda boleh ubah konfigurasi beban kerja ruang dalam bahagian Sistem / Konfigurasi / Pengurusan Beban Kerja.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=vCPU Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Ingatan Spark Apache (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Pengingesan Produk Data
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Tiada data tersedia kerana ruang sedang diatur duduk
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Tiada data tersedia kerana ruang sedang dimuatkan
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Edit Pemetaan Tika
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
