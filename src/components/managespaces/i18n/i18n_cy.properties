#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Monitro
#XTXT: Type name for spaces in browser tab page title
space=Gofod
#_____________________________________
#XFLD: Spaces label in
spaces=Gofodau
#XFLD: Manage plan button text
manageQuotaButtonText=Rheoli Cynllun
#XBUT: Manage resources button
manageResourcesButton=Rheoli Adnoddau
#XFLD: Create space button tooltip
createSpace=Creu Gofod
#XFLD: Create
create=Creu
#XFLD: Deploy
deploy=Gosod
#XFLD: Page
page=Tudalen
#XFLD: Cancel
cancel=Canslo
#XFLD: Update
update=Diweddaru
#XFLD: Save
save=Cadw
#XFLD: OK
ok=Iawn
#XFLD: days
days=Diwrnod
#XFLD: Space tile edit button label
edit=Golygu
#XFLD: Auto Assign all objects to space
autoAssign=Neilltuo’n Awtomatig
#XFLD: Space tile open monitoring button label
openMonitoring=Monitro
#XFLD: Delete
delete=Dileu
#XFLD: Copy Space
copy=Copïo
#XFLD: Close
close=Cau
#XCOL: Space table-view column status
status=Statws
#XFLD: Space status active
activeLabel=Gweithredol
#XFLD: Space status locked
lockedLabel=Wedi Cloi
#XFLD: Space status critical
criticalLabel=Hollbwysig
#XFLD: Space status cold
coldLabel=Oer
#XFLD: Space status deleted
deletedLabel=Wedi Dileu
#XFLD: Space status unknown
unknownLabel=Anhysbys
#XFLD: Space status ok
okLabel=Iawn
#XFLD: Database user expired
expired=Wedi Dod i Ben
#XFLD: deployed
deployed=Wedi'i Osod
#XFLD: not deployed
notDeployed=Heb ei Osod
#XFLD: changes to deploy
changesToDeploy=Newidiadau i'w Gosod
#XFLD: pending
pending=Wrthi'n Gosod
#XFLD: designtime error
designtimeError=Gwall amser dylunio
#XFLD: runtime error
runtimeError=Gwall amser rhedeg
#XFLD: Space created by label
createdBy=Wedi'i Greu Gan
#XFLD: Space created on label
createdOn=Wedi'i Greu Ar
#XFLD: Space deployed on label
deployedOn=Wedi'i Osod Ar
#XFLD: Space ID label
spaceID=ID Gofod
#XFLD: Priority label
priority=Blaenoriaeth
#XFLD: Space Priority label
spacePriority=Blaenoriaeth Gofod
#XFLD: Space Configuration label
spaceConfiguration=Ffurfweddu Gofod
#XFLD: Not available
notAvailable=Ddim Ar Gael
#XFLD: WorkloadType default
default=Diofyn
#XFLD: WorkloadType custom
custom=Personol
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Mynediad Data Lake
#XFLD: Translation label
translationLabel=Cyfieithiad
#XFLD: Source language label
sourceLanguageLabel=Iaith Ffynhonnell
#XFLD: Translation CheckBox label
translationCheckBox=Rhoi Cyfieithu Ar Waith
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Gosod gofod i gael mynediad at fanylion defnyddiwr.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Gosod gofod i agor yr Archwilydd Cronfa Ddata.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Does dim modd i chi ddefnyddio'r gofod hwn i gael mynediad i'r Llyn Data oherwydd ei fod eisoes yn cael ei ddefnyddio gan ofod arall.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Defnyddio'r gofod hwn i gael mynediad at y llyn data.
#XFLD: Space Priority minimum label extension
low=Isel
#XFLD: Space Priority maximum label extension
high=Uchel
#XFLD: Space name label
spaceName=Enw Gofod
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Gosod Gwrthrychau
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Copïo {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Heb Ddewis)
#XTXT Human readable text for language code "af"
af=Affricaneg
#XTXT Human readable text for language code "ar"
ar=Arabeg
#XTXT Human readable text for language code "bg"
bg=Bwlgareg
#XTXT Human readable text for language code "ca"
ca=Catalaneg
#XTXT Human readable text for language code "zh"
zh=Tsieinëeg Syml
#XTXT Human readable text for language code "zf"
zf=Tsieineëg
#XTXT Human readable text for language code "hr"
hr=Croateg
#XTXT Human readable text for language code "cs"
cs=Tsieceg
#XTXT Human readable text for language code "cy"
cy=Cymraeg
#XTXT Human readable text for language code "da"
da=Daneg
#XTXT Human readable text for language code "nl"
nl=Iseldireg
#XTXT Human readable text for language code "en-UK"
en-UK=Saesneg (Y Deyrnas Unedig)
#XTXT Human readable text for language code "en"
en=Saesneg (Yr Unol Daleithiau)
#XTXT Human readable text for language code "et"
et=Estoneg
#XTXT Human readable text for language code "fa"
fa=Perseg
#XTXT Human readable text for language code "fi"
fi=Ffinneg
#XTXT Human readable text for language code "fr-CA"
fr-CA=Ffrangeg (Canada)
#XTXT Human readable text for language code "fr"
fr=Ffrangeg
#XTXT Human readable text for language code "de"
de=Almaeneg
#XTXT Human readable text for language code "el"
el=Groegaidd
#XTXT Human readable text for language code "he"
he=Hebraeg
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Hwngareg
#XTXT Human readable text for language code "is"
is=Islandeg
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Eidaleg
#XTXT Human readable text for language code "ja"
ja=Japaneg
#XTXT Human readable text for language code "kk"
kk=Kazakh
#XTXT Human readable text for language code "ko"
ko=Corëeg
#XTXT Human readable text for language code "lv"
lv=Latifeg
#XTXT Human readable text for language code "lt"
lt=Lithwaneg
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Norwyeg
#XTXT Human readable text for language code "pl"
pl=Pwyleg
#XTXT Human readable text for language code "pt"
pt=Portiwgaleg (Brasil)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portiwgaleg (Portiwgal)
#XTXT Human readable text for language code "ro"
ro=Rwmaneg
#XTXT Human readable text for language code "ru"
ru=Rwseg
#XTXT Human readable text for language code "sr"
sr=Serbeg
#XTXT Human readable text for language code "sh"
sh=Serbo-Croateg
#XTXT Human readable text for language code "sk"
sk=Slofaceg
#XTXT Human readable text for language code "sl"
sl=Slofeneg
#XTXT Human readable text for language code "es"
es=Sbaeneg
#XTXT Human readable text for language code "es-MX"
es-MX=Sbaeneg (Mecsico)
#XTXT Human readable text for language code "sv"
sv=Swedeg
#XTXT Human readable text for language code "th"
th=Tai
#XTXT Human readable text for language code "tr"
tr=Tyrceg
#XTXT Human readable text for language code "uk"
uk=Wcreineg
#XTXT Human readable text for language code "vi"
vi=Fietnameg
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Dileu Gofodau
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Ydych chi''n siŵr eich bod chi am symud gofod "{0}" i''r bin ailgylchu?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Ydych chi''n siŵr eich bod chi am symud {0} o''r gofodau a ddewiswyd i''r bin ailgylchu?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Ydych chi''n siŵr eich bod chi am ddileu gofod ''{0}''? Fedrwch chi ddim dadwneud hyn.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Ydych chi''n siŵr eich bod am ddileu''r {0} gofod a ddewiswyd? Fedrwch chi ddim dadwneud hyn. Bydd y cynnwys canlynol yn cael ei ddileu {1}:
#XTXT: permanently
permanently=am byth
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Bydd y cynnwys canynol yn cael ei ddileu {0} ac ni fydd modd ei adfer:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Teipiwch {0} i gadarnhau''r dileu.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Gwiriwch eich sillafu a rhowch gynnig arall arni.
#XTXT: All Spaces
allSpaces=Pob Gofod
#XTXT: All data
allData=Yr holl wrthychau a data sydd wedi'u cynnwys yn y gofod
#XTXT: All connections
allConnections=Pob cysylltiad sydd wedi'i ddiffinio yn y gofod
#XFLD: Space tile selection box tooltip
clickToSelect=Cliciwch i Ddewis
#XTXT: All database users
allDatabaseUsers=Yr holl wrthrychau a data sydd wedi'u cynnwys mewn unrhyw sgema Open SQL sy'n gysylltiedig â'r gofod
#XFLD: remove members button tooltip
deleteUsers=Tynnu Aelodau
#XTXT: Space long description text
description=Disgrifiad (Uchafswm o 4000 Nod)
#XFLD: Add Members button tooltip
addUsers=Ychwanegu Aelodau
#XFLD: Add Users button tooltip
addUsersTooltip=Ychwanegu Defnyddwyr
#XFLD: Edit Users button tooltip
editUsersTooltip=Golygu Defnyddwyr
#XFLD: Remove Users button tooltip
removeUsersTooltip=Tynnu Defnyddwyr
#XFLD: Searchfield placeholder
filter=Chwilio
#XCOL: Users table-view column health
health=Iechyd
#XCOL: Users table-view column access
access=Mynediad
#XFLD: No user found nodatatext
noDataText=Heb Ddod o Hyd i Ddefnyddiwr
#XTIT: Members dialog title
selectUserDialogTitle=Ychwanegu Aelodau
#XTIT: User dialog title
addUserDialogTitle=Ychwanegu Defnyddwyr
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Dileu Cysylltiadau
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Dileu Cysylltiad
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Ydych chi'n siŵr eich bod am ddileu'r cysylltiadau a ddewiswyd? Byddant yn cael eu tynnu am byth.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Dewis Cysylltiadau
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Rhannu Cysylltiad
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Cysylltiadau a Rennir
#XFLD: Add remote source button tooltip
addRemoteConnections=Ychwanegu Cysylltiadau
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Tynnu Cysylltiadau
#XFLD: Share remote source button tooltip
shareConnections=Rhannu Cysylltiadau
#XFLD: Tile-layout tooltip
tileLayout=Cynllun Teils
#XFLD: Table-layout tooltip
tableLayout=Cynllun Tabl
#XMSG: Success message after creating space
createSpaceSuccessMessage=Gofod wedi'i greu
#XMSG: Success message after copying space
copySpaceSuccessMessage=Wrthi''n copïo gofod "{0}" i ofod "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Wedi dechrau gosod gofod
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Diweddariad Apache Spark wedi dechrau
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Wedi'u methu diweddaru Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Wedi diweddaru manylion gofod
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Gofod wedi'i ddatgloi dros dro
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Gofod wedi'i ddileu
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Gofodau wedi'u dileu
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Gofod wedi'i adfer
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Gofodau wedi'u hadfer
#YMSE: Error while updating settings
updateSettingsFailureMessage=Dim modd diweddaru gosodiadau'r gofod.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Mae'r llyn data eisoes wedi'i neilltuo i ofod arall. Dim ond un lle sy'n gallu cyrchu'r llyn data ar y tro.
#YMSE: Error while updating data lake option
virtualTablesExists=Ni allwch ddynodi'r llyn data o'r gofod hwn oherwydd mae yna ddibyniaethau o hyd i dablau rhithwir*. Dylech ddileu'r tablau rhithwir i ail-ddynodi'r llyn data o'r gofod hwn.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Doedd dim modd datgloi'r gofod.
#YMSE: Error while creating space
createSpaceError=Doedd dim modd creu'r gofod.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Mae gofod â''r enw {0} yn bodoli''n barod.
#YMSE: Error while deleting a single space
deleteSpaceError=Doedd dim modd dileu'r gofod.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Dydy eich gofod "{0}" ddim yn gweithio''n iawn bellach. Ceisiwch ei ddileu eto. Os nad yw''n gweithio, gofynnwch i''ch gweinyddwr ddileu''r gofod neu agor tocyn.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Doedd dim modd dileu data'r gofod yn Ffeiliau.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Doedd dim modd tynnu'r defnyddwyr.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Doedd dim modd tynnu'r sgemâu.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Doedd dim modd dileu'r cysylltiadau.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Doedd dim modd dileu data'r gofod.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Doedd dim modd dileu'r gofodau.
#YMSE: Error while restoring a single space
restoreSpaceError=Doedd dim modd adfer y gofod.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Doedd dim modd adfer y gofodau.
#YMSE: Error while creating users
createUsersError=Doedd dim modd ychwanegu'r defnyddwyr.
#YMSE: Error while removing users
removeUsersError=Doedd dim modd tynnu'r defnyddwyr.
#YMSE: Error while removing user
removeUserError=dim modd tynnu'r defnyddiwr.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Doedd dim modd ychwanegu’r defnyddiwr at y rôl mewn cwmpas sydd dan sylw. \n\n Does dim modd i chi ychwanegu eich hun at rôl mewn cwmpas. Gallwch ofyn i'ch gweinyddwr eich ychwanegu at rôl mewn cwmpas.
#YMSE: Error assigning user to the space
userAssignError=Doedd dim modd neilltuo'r defnyddiwr i'r gofod. \n\n Mae'r defnyddiwr wedi'i neilltuo'n barod i'r nifer fwyaf o ofodau a ganiateir (100) ar draws rolau wedi'u cwmpasu.
#YMSE: Error assigning users to the space
usersAssignError=Doedd dim modd neilltuo'r defnyddwyr i'r gofod. \n\n Mae'r defnyddiwr wedi'i neilltuo'n barod i'r nifer fwyaf o ofodau a ganiateir (100) ar draws rolau wedi'u cwmpasu.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Doedd dim modd adfer y defnyddwyr. Rhowch gynnig arall arni rywbryd eto.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Doedd dim modd adfer y rolau wedi'u cwmpasu.
#YMSE: Error while fetching members
fetchUserError=Dim modd cyrchu'r aelodau. Rhowch gynnig arall arni rywbryd eto.
#YMSE: Error while loading run-time database
loadRuntimeError=Doedd dim modd i ni lwytho gwybodaeth o’r gronfa ddata amser rhedeg.
#YMSE: Error while loading spaces
loadSpacesError=Yn anffodus, aeth rhywbeth o'i le wrth geisio cyrchu eich gofodau.
#YMSE: Error while loading haas resources
loadStorageError=Yn anffodus, aeth rhywbeth o'i le wrth geisio cyrchu'r data storio.
#YMSE: Error no data could be loaded
loadDataError=Yn anffodus, aeth rhywbeth o'i le wrth geisio cyrchu eich data.
#XFLD: Click to refresh storage data
clickToRefresh=Cliciwch yma i roi cynnig arall arni.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Dileu Gofod
#XCOL: Spaces table-view column name
name=Enw
#XCOL: Spaces table-view deployment status
deploymentStatus=Statws Gosod
#XFLD: Disk label in space details
storageLabel=Disg (GB)
#XFLD: In-Memory label in space details
ramLabel=Cof (GB)
#XFLD: Memory label on space card
memory=Cof ar gyfer Storio
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Lle Storio mewn Gofod
#XFLD: Storage Type label in space details
storageTypeLabel=Math o Storfa
#XFLD: Enable Space Quota
enableSpaceQuota=Galluogi Cwota Gofodau
#XFLD: No Space Quota
noSpaceQuota=Dim Cwota Bwlch
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Cronfa Ddata SAP HANA (Disg ac ar Gof)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Ffeiliau Llyn Data, SAP HANA
#XFLD: Available scoped roles label
availableRoles=Rolau wedi'u Cwmpasu Ar Gael
#XFLD: Selected scoped roles label
selectedRoles=Rolau wedi'u Cwmpasu Dan Sylw
#XCOL: Spaces table-view column models
models=Modelau
#XCOL: Spaces table-view column users
users=Defnyddwyr
#XCOL: Spaces table-view column connections
connections=Cysylltiadau
#XFLD: Section header overview in space detail
overview=Trosolwg
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Rhaglenni
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Aseiniad Tasg
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPUs
#XFLD: Memory label in Apache Spark section
memoryLabel=Cof (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Ffurfweddu Gofod
#XFLD: Space Source label
sparkApplicationLabel=Rhaglen
#XFLD: Cluster Size label
clusterSizeLabel=Maint y Clwstwr
#XFLD: Driver label
driverLabel=Gyrrwr
#XFLD: Executor label
executorLabel=Gweithredwr
#XFLD: max label
maxLabel=Uchafswm. Wedi'i ddefnyddio
#XFLD: TrF Default label
trFDefaultLabel=Llif Trawsnewid Diofyn
#XFLD: Merge Default label
mergeDefaultLabel=Cyfuno'n Ddiofyn
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimeiddio Diofyn
#XFLD: Deployment Default label
deploymentDefaultLabel=Gosod Tabl Lleol (Ffeil)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Math o Wrthrych
#XFLD: Task activity label
taskActivityLabel=Gweithgaredd
#XFLD: Task Application ID label
taskApplicationIDLabel=Rhaglen Ddiofyn
#XFLD: Section header in space detail
generalSettings=Gosodiadau Cyffredinol
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Mae'r gofod wedi'i gloi gan y system ar hyn o bryd.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Bydd newidiadau i’r adran hon yn cael eu gosod yn syth.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Cofiwch fod newid y gwerthoedd hyn yn gallu effeithio ar berfformiad.
#XFLD: Button text to unlock the space again
unlockSpace=Datgloi'r Gofod
#XFLD: Info text for audit log formatted message
auditLogText=Galluogi logiau archwilio i gofnodi, darllen neu newid gweithredoedd (polisïau archwilio). Yna, gall gweinyddwyr ddadansoddi pwy wnaeth pa weithred ar adeg benodol.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Gall logiau archwilio gymryd llawer iawn o le stori ar ddisg yn eich tenant. Os ydych chi'n galluogi polisi archwilio (darllen neu newid gweithredoedd), dylech chi fynd ati'n rheolaidd i fonitro faint o le stori sydd ar y ddisg (drwy'r cerdyn Disk Storage Used yn y System Monitor) er mwyn atal y disg rhag fynd yn llawn, a all arwain at amhariadau yn y gwasanaeth. Os byddwch chi'n analluogi polisi archwilio, bydd yr holl logiau archwilio yn cael eu dileu. Os ydych chi eisiau cadw cofnodion y logiau archwilio, dylech chi ystyried eu hallgludo cyn analluogi'r polisi archwilio.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Dangos Cymorth
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Mae''r gofod hwn wedi defnyddio mwy na''i le storio a bydd wedi''i gloi mewn {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=oriau
#XMSG: Unit for remaining time until space is locked again
minutes=munudau
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Archwilio
#XFLD: Subsection header in space detail for auditing
auditing=Gosodiadau Archwilio Gofodau
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Gofodau critigol: Defnydd o le storio'n fwy na 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Gofodau iawn: Defnydd o le storio rhwng 6% a 90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Gofodau oer: Defnydd o le storio'n 5% neu lai.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Gofodau critigol: Defnydd o le storio'n fwy na 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=Gofodau iawn: Defnydd o le storio rhwng 6% a 90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Gofodau wedi cloi: Wedi rhwystro oherwydd diffyg cof.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Gofodau Wedi Cloi
#YMSE: Error while deleting remote source
deleteRemoteError=Doedd dim modd dileu'r cysylltiadau.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Dim modd newid ID y Gofod nes ymlaen.\nDyma'r nodau dilys A - Z, 0 - 9, a _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Rhowch enw gofod.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Rhowch enw busnes.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Rhowch ID gofod.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Nodau annilys. Defnyddiwch  A - Z, 0 - 9, a _ yn unig.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ID gofod yn bodoli'n barod.
#XFLD: Space searchfield placeholder
search=Chwilio
#XMSG: Success message after creating users
createUsersSuccess=Defnyddwyr wedi'u hychwanegu
#XMSG: Success message after creating user
createUserSuccess=Defnyddiwr wedi'i ychwanegu
#XMSG: Success message after updating users
updateUsersSuccess={0} o ddefnyddwyr wedi''u diweddaru
#XMSG: Success message after updating user
updateUserSuccess=Defnyddiwr wedi'i ddiweddaru
#XMSG: Success message after removing users
removeUsersSuccess={0} o ddefnyddwyr wedi''u tynnu
#XMSG: Success message after removing user
removeUserSuccess=Defnyddiwr wedi'i dynnu
#XFLD: Schema name
schemaName=Enw'r Sgema
#XFLD: used of total
ofTemplate={0} o {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Disg Wedi''i Neilltuo ({0} o {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Cof Wedi''i Neilltuo ({0} o {1})
#XFLD: Storage ratio on space
accelearationRAM=Cyflymiad Cof
#XFLD: No Storage Consumption
noStorageConsumptionText=Does dim cwota storio wedi'i neilltuo.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disg wedi''i defnyddio ar gyfer Storio ({0} o {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Cof wedi''i ddefnyddio ar gyfer Storio ({0} o {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} o {1} o''r Ddisg wedi''i Defnyddio ar gyfer Storio
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} o {1} o''r Cof Wedi''i Ddefnyddio
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} o {1} o''r Disg Wedi''i Neilltuo
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} of {1} o''r Cof Wedi''i Neilltuo
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Data Gofod: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Data Arall: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Ystyriwch ehangu eich cynllun, neu cysylltwch â SAP Support.
#XCOL: Space table-view column used Disk
usedStorage=Disg wedi'i defnyddio ar gyfer Storio
#XCOL: Space monitor column used Memory
usedRAM=Cof wedi'i ddefnyddio ar gyfer Storio
#XCOL: Space monitor column Schema
tableSchema=Sgema
#XCOL: Space monitor column Storage Type
tableStorageType=Math o Storfa
#XCOL: Space monitor column Table Type
tableType=Math o Dabl
#XCOL: Space monitor column Record Count
tableRecordCount=Cyfrif Cofnodion
#XFLD: Assigned Disk
assignedStorage=Disg wedi'i neilltuo ar gyfer Storio
#XFLD: Assigned Memory
assignedRAM=Cof wedi'i neilltuo ar gyfer Storio
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Lle Storio wedi'i Ddefnyddio
#XFLD: space status
spaceStatus=Statws Gofod
#XFLD: space type
spaceType=Math o Ofod
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Pont SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Cynnyrch Darparwr Data
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Does dim modd i chi ddileu gofod {0} gan ei fod yn fath o ofod {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Does dim modd i chi ddileu’r {0} gofod a ddewiswyd. Does dim modd dileu gofodau â’r math canlynol o ofod: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Monitro
#XFLD: Tooltip for edit space button
editSpace=Golygu Gofod
#XMSG: Deletion warning in messagebox
deleteConfirmation=Ydych chi'n siŵr eich bod am ddileu'r gofod hwn?
#XFLD: Tooltip for delete space button
deleteSpace=Dileu Gofod
#XFLD: storage
storage=Disg ar gyfer Storio
#XFLD: username
userName=Enw defnyddiwr
#XFLD: port
port=Porth
#XFLD: hostname
hostName=Enw Lletywr
#XFLD: password
password=Cyfrinair
#XBUT: Request new password button
requestPassword=Gofyn am Gyfrinair Newydd
#YEXP: Usage explanation in time data section
timeDataSectionHint=Gallwch greu amserlenni a dimensiynau i'w defnyddio yn eich modelau a'ch straeon.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Ydych chi am i'r data yn eich gofod allu cael ei ddefnyddio gan offer neu apiau eraill? Os hynny, dylech greu un neu fwy o ddefnyddwyr y gall gael mynediad at y data yn eich gofod a dewis a ydych am i'r holl ddata yn y gofod yn y dyfodol allu cael ei ddefnyddio'n ddiofyn.
#XTIT: Create schema popup title
createSchemaDialogTitle=Creu Sgema SQL Agored
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Creu Amserlenni a Dimensiynau
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Golygu Amserlenni a Dimensiynau
#XTIT: Time Data token title
timeDataTokenTitle=Data Amser
#XTIT: Time Data token title
timeDataUpdateViews=Diweddaru Gweddau Data Amser
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Wrthi'n creu...
#XFLD: Time Data token creation error label
timeDataCreationError=Wedi methu creu. Rhowch gynnig arall arni.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Gosodiadau Amserlen
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Tablau Cyfieithu
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Dimensiynau Amser
#XFLD: Time Data dialog time range label
timeRangeHint=Diffiniwch yr ystod amser.
#XFLD: Time Data dialog time data table label
timeDataHint=Rhowch enw i'ch tabl.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Rhowch enw i'ch dimensiynau.
#XFLD: Time Data Time range description label
timerangeLabel=Ystod Amser
#XFLD: Time Data dialog from year label
fromYearLabel=O Flwyddyn
#XFLD: Time Data dialog to year label
toYearLabel=I Flwyddyn
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Math o Galendr
#XFLD: Time Data dialog granularity label
granularityLabel=Manylder
#XFLD: Time Data dialog technical name label
technicalNameLabel=Enw Technegol
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Tabl Cyfieithu ar gyfer Chwarteri
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Tabl Cyfieithu ar gyfer Misoedd
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Tabl Cyfieithu ar gyfer Diwrnodau
#XFLD: Time Data dialog year label
yearLabel=Dimensiwn Blwyddyn
#XFLD: Time Data dialog quarter label
quarterLabel=Dimensiwn Chwarter
#XFLD: Time Data dialog month label
monthLabel=Dimensiwn Mis
#XFLD: Time Data dialog day label
dayLabel=Dimensiwn Diwrnod
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoraidd
#XFLD: Time Data dialog time granularity day label
day=Diwrnod
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Wedi cyrraedd uchafswm hyd sef 1000 nod.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Yr ystod amser uchaf yw 150 o flynyddoedd.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=Dylai "O Flwyddyn" fod yn llai na "I Flwyddyn"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=Rhaid i "O Flwyddyn" fod yn 1900 neu'n uwch.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=Dylai "I Flwyddyn" fod yn uwch na "O Flwyddyn"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=Rhaid i “I Flwyddyn” fod yn is na'r flwyddyn gyfredol a 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Gallai cynyddu'r "O Flwyddyn" arwain at golli data
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Gallai ostwng yr "I Flwyddyn" arwain at golli data
#XMSG: Time Data creation validation error message
timeDataValidationError=Mae rhai meysydd yn annilys. Gwiriwch y meysydd gofynnol er mwyn bwrw ymlaen.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Ydych chi'n siŵr eich bod am ddileu'r data?
#XMSG: Time Data creation success message
createTimeDataSuccess=Data amser wedi'i greu
#XMSG: Time Data update success message
updateTimeDataSuccess=Data amser wedi'i ddiweddaru
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Data amser wedi'i ddileu
#XMSG: Time Data creation error message
createTimeDataError=Aeth rhywbeth o'i le wrth geisio creu data amser.
#XMSG: Time Data update error message
updateTimeDataError=Aeth rhywbeth o'i le wrth geisio diweddaru data amser.
#XMSG: Time Data creation error message
deleteTimeDataError=Aeth rhywbeth o'i le wrth geisio dileu data amser.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Dim modd llwytho data amser.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Rhybudd
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Doedd dim modd i ni ddileu eich Data Amser am ei fod yn cael ei ddefnyddio mewn modelau eraill.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Doedd dim modd i ni ddileu eich Data Amser am ei fod yn cael ei ddefnyddio mewn model arall.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Mae'r maes hwn yn ofynnol a does dim modd ei adael yn wag.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Agor yn Archwilydd Cronfa Ddata
#YMSE: Dimension Year
dimensionYearView=Dimensiwn "Blwyddyn"
#YMSE: Dimension Year
dimensionQuarterView=Dimensiwn "Chwarter"
#YMSE: Dimension Year
dimensionMonthView=Dimensiwn "Mis"
#YMSE: Dimension Year
dimensionDayView=Dimensiwn "Diwrnod"
#XFLD: Time Data deletion object title
timeDataUsedIn=(defnyddir mewn {0} model)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(defnyddir mewn 1 model)
#XFLD: Time Data deletion table column provider
provider=Darparwr
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Dibyniaethau
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Creu Defnyddiwr ar gyfer Sgema Gofod
#XFLD: Create schema button
createSchemaButton=Creu Sgema SQL Agored
#XFLD: Generate TimeData button
generateTimeDataButton=Creu Amserlenni a Dimensiynau
#XFLD: Show dependencies button
showDependenciesButton=Dangos Dibyniaethau
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Rhaid i chi fod yn aelod o'r gofod er mwyn cyflawni'r weithred hon.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Creu Defnyddiwr Sgema Gofod
#YMSE: API Schema users load error
loadSchemaUsersError=Dim modd llwytho rhestr o ddefnyddwyr.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Manylion Defnyddiwr Sgema Gofod
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Ydych chi'n siŵr eich bod am ddileu'r defnyddiwr a ddewiswyd?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Defnyddiwr wedi'i ddileu.
#YMSE: API Schema user deletion error
userDeleteError=Doedd dim modd dileu'r defnyddiwr.
#XFLD: User deleted
userDeleted=Mae’r defnyddiwr wedi cael ei ddileu.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Rhybudd
#XMSG: Remove user popup text
removeUserConfirmation=Ydych chi wir eisiau dileu'r defnyddiwr? Bydd y defnyddiwr a'i rolau wedi'u cwmpasu sydd wedi'u neilltuo yn cael eu tynnu o'r gofod.
#XMSG: Remove users popup text
removeUsersConfirmation=Ydych chi wir eisiau dileu'r defnyddwyr? Bydd y defnyddwyr a'u rolau wedi'u cwmpasu sydd wedi'u neilltuo yn cael eu tynnu o'r gofod.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Tynnu
#YMSE: No data text for available roles
noDataAvailableRoles=Nid yw'r gofod wedi'i ychwanegu at unrhyw rôl wedi'i chwmpasu. \n I fod yn gallu ychwanegu defnyddwyr at y gofod, rhaid iddo fod wedi'i ychwanegu at un neu fwy o rolau wedi'u cwmpasu.
#YMSE: No data text for selected roles
noDataSelectedRoles=Dim rolau wedi'u cwmpasu wedi'u dewis
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Manylion Ffurfweddu Sgema SQL Agored
#XFLD: Label for Read Audit Log
auditLogRead=Galluogi Log Archwilio ar gyfer Gweithredoedd Darllen
#XFLD: Label for Change Audit Log
auditLogChange=Galluogi Log Archwilio ar gyfer Gweithredoedd Newid
#XFLD: Label Audit Log Retention
auditLogRetention=Cadw Logiau am
#XFLD: Label Audit Log Retention Unit
retentionUnit=Diwrnod
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Rhowch gyfanrif rhwng {0} a {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Defnyddio Data Sgema Gofod
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Stopio Defnyddio Data Sgema Gofod
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Efallai bydd y sgema SQL Agored yn defnyddio data eich sgema gofod. Os byddwch yn stopio ei ddefnyddio, mae'n bosib na fydd modelau sydd wedi'u selio ar y sgemâu gofod yn gweithio.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Stopio Defnyddio
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Mae'r gofod hwn yn cael ei ddefnyddio i gael mynediad at y llyn data
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Llyn Data Ar Waith
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Wedi Cyrraedd Terfyn y Cof
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Wedi Cyrraedd Terfyn Storio
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Wedi Cyrraedd Isafswm Terfyn Storio
#XFLD: Space ram tag
ramLimitReachedLabel=Wedi Cyrraedd Terfyn y Cof
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Wedi Cyrraedd Isafswm Terfyn y Cof
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Rydych wedi defnyddio''r terfyn storio gofod a neilltuwyd sef {0}. Dylech neilltuo rhagor o le storio i''r gofod.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Wedi Cyrraedd Terfyn Storio'r System
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Rydych wedi cyrraedd terfyn storio''r system sef {0}. Ni allwch neilltuo mwy o le storio i''r gofod nawr.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Bydd dileu'r sgema SQL agored hon hefyd yn dileu'r holl wrthrychau sydd wedi'u storio a'r cysylltiadau a gynhelir yn y sgema yn barhaol. Bwrw ymlaen?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Sgema wedi'i dileu
#YMSE: Error while deleting schema.
schemaDeleteError=Doedd dim modd dileu'r sgema.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Sgema wedi'i diweddaru
#YMSE: Error while updating schema.
schemaUpdateError=Doedd dim modd diweddaru'r sgema.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Rydym wedi darparu cyfrinair ar gyfer y sgema hwn. Os ydych wedi anghofio neu golli eich cyfrinair, gallwch ofyn am un newydd. Cofiwch gopïo neu gadw'r cyfrinair newydd.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Dylech gopïo eich cyfrinair. Bydd ei angen arnoch er mwyn sefydlu cysylltiad â'r sgema hon. Os ydych wedi anghofio eich cyfrinair, gallwch agor y ddeialog hon i'w hailosod.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Dim modd newid yr enw ar ôl i'r sgema ei chreu.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Agor SQL
#XFLD: Space schema section sub headline
schemasSpace=Gofod
#XFLD: HDI Container section header
HDIContainers=Cynwysyddion HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Ychwanegu Cynwysyddion HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Tynnu Cynwysyddion HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Galluogi Mynediad
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Does dim cynwysyddion HDI wedi'u hychwanegu.
#YMSE: No data text for Timedata section
noDataTimedata=Does dim tablau amser a dimensiynau wedi'u creu.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Wedi methu llwytho amserlenni a dimensiynau gan nad yw'r gronfa ddata amser rhedeg ar gael.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Wedi methu llwytho Cynwysyddion HDI gan nad yw'r gronfa ddata amser rhedeg ar gael.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Doedd dim modd cael gafael ar y Cynwysyddion HDI. Rhowch gynnig arall arni rywbryd eto.
#XFLD Table column header for HDI Container names
HDIContainerName=Enw Cynhwysydd HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Galluogi Mynediad
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Gallwch chi alluogi SAP SQL Data Warehousing ar eich tenant SAP Datasphere i gyfnewid data rhwng eich cynwysyddion HDI a’ch gofodau SAP Datasphere heb orfod symud data.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=I wneud hyn, agorwch docyn cymorth drwy glicio’r botwm isod.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Ar ôl i’ch tocyn gael ei brosesu, rhaid i chi adeiladu un neu ragor o gynwysyddion HDI newydd yng nghronfa dda amser rhedeg SAP Datasphere. Yna, bydd y botwm Galluogi Mynediad yn cael ei newid am y botwm + yn yr adran Cynwysyddion HDI ar gyfer pob un o’ch gofodau SAP Datasphere, a gallwch chi ychwanegu eich cynwysyddion i ofod.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Angen rhagor o wybodaeth? Ewch i %%0. I gael gwybodaeth fanwl am beth i'w gynnwys yn eich tocyn, gweler %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=Nodyn SAP 3057059'
#XBUT: Open Ticket Button Text
openTicket=Agor Tocyn
#XBUT: Add Button Text
add=Ychwanegu
#XBUT: Next Button Text
next=Nesaf
#XBUT: Edit Button Text
editUsers=Golygu
#XBUT: create user Button Text
createUser=Creu
#XBUT: Update user Button Text
updateUser=Dewis
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Ychwanegu Cynwysyddion HDI sydd Heb eu Neilltuo
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Heb ddod o hyd i gynwysyddion heb ei neilltuo. \n Efallai bod y cynhwysydd rydych yn chwilio amdano wedi'i neilltuo i ofod yn barod.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Dim modd llwytho cynwysyddion HDI wedi'u neilltuo.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Dim modd llwytho cynwysyddion HDI.
#XMSG: Success message
succeededToAddHDIContainer=Cynhwysydd HDI wedi'i ychwanegu
#XMSG: Success message
succeededToAddHDIContainerPlural=Cynwysyddion HDI wedi'u hychwanegu
#XMSG: Success message
succeededToDeleteHDIContainer=Cynhwysydd HDI wedi'i dynnu
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Cynwysyddion HDI wedi'u tynnu
#XFLD: Time data section sub headline
timeDataSection=Amserlenni a Dimensiynau
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Darllen
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Newid
#XFLD: Remote sources section sub headline
allconnections=Neilltuo Cysylltiad
#XFLD: Remote sources section sub headline
localconnections=Cysylltiadau Lleol
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=Cysylltwyr Agored SAP
#XFLD: User section sub headline
memberassignment=Neilltuo Aelod
#XFLD: User assignment section sub headline
userAssignment=Neilltuo Defnyddiwr
#XFLD: User section Access dropdown Member
member=Aelod
#XFLD: User assignment section column name
user=Enw defnyddiwr
#XTXT: Selected role count
selectedRoleToolbarText=Wedi Dewis: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Cysylltiadau
#XTIT: Space detail section data access title
detailsSectionDataAccess=Mynediad Sgema
#XTIT: Space detail section time data title
detailsSectionGenerateData=Data Amser
#XTIT: Space detail section members title
detailsSectionUsers=Aelodau
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Defnyddwyr
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Wedi Rhedeg Allan o Le Storio
#XTIT: Storage distribution
storageDistributionPopoverTitle=Lle Storio ar Ddisg wedi'i Ddefnyddio
#XTXT: Out of Storage popover text
insufficientStorageText=I greu gofod newydd, dylech leihau'r lle storio sydd wedi'i neilltuo i ofod arall neu ddileu gofod nad oes ei angen arnoch. Gallwch gynyddu cyfanswm lle storio eich system drwy alw Rheoli Cynllun.
#XMSG: Space id length warning
spaceIdLengthWarning=Wedi pasio''r uchafswm o {0} nod.
#XMSG: Space name length warning
spaceNameLengthWarning=Wedi pasio''r uchafswm o {0} nod.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Peidiwch â defnyddio''r rhagddodiad {0} er mwyn osgoi gwrthdaro posib.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Dim modd llwytho sgemâu SQL Agored.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Dim modd creu sgemâu SQL Agored.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Dim modd llwytho pob cysylltiad pell.
#YMSE: Error while loading space details
loadSpaceDetailsError=Doedd dim modd llwytho manylion y gofod.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Doedd dim modd gosod y gofod.
#YMSE: Error while copying space details
copySpaceDetailsError=Doedd dim modd copïo'r gofod.
#YMSE: Error while loading storage data
loadStorageDataError=Dim modd llwytho'r data storio.
#YMSE: Error while loading all users
loadAllUsersError=Dim modd llwytho pob defnyddiwr.
#YMSE: Failed to reset password
resetPasswordError=Dim modd ailosod y cyfrinair.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Cyfrinair newydd wedi'i osod yn llwyddiannus ar gyfer y sgema
#YMSE: DP Agent-name too long
DBAgentNameError=Mae enw'r asiant DP yn rhy hir.
#YMSE: Schema-name not valid.
schemaNameError=Mae enw'r sgema yn annilys.
#YMSE: User name not valid.
UserNameError=Mae'r enw defnyddiwr yn annilys.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Defnydd yn ôl Math Storio
#XTIT: Consumption by Schema
consumptionSchemaText=Defnydd yn ôl Sgema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Defnydd Tabl Cyffredinol yn ôl Sgema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Defnydd Cyffredinol yn ôl Math o Dabl
#XTIT: Tables
tableDetailsText=Manylion Tabl
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Defnydd Storio Tabl
#XFLD: Table Type label
tableTypeLabel=Math o Dabl
#XFLD: Schema label
schemaLabel=Sgema
#XFLD: reset table tooltip
resetTable=Ailosod Tabl
#XFLD: In-Memory label in space monitor
inMemoryLabel=Cof
#XFLD: Disk label in space monitor
diskLabel=Disg
#XFLD: Yes
yesLabel=Iawn
#XFLD: No
noLabel=Na
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Ydych chi am i'r data yn y gofod hwn allu cael ei ddefnyddio'n ddiofyn?
#XFLD: Business Name
businessNameLabel=Enw Busnes
#XFLD: Refresh
refresh=Adnewyddu
#XMSG: No filter results title
noFilterResultsTitle=Mae'n ymddangos nad yw eich gosodiadau hidlo yn dangos unrhyw ddata.
#XMSG: No filter results message
noFilterResultsMsg=Ceisiwch fireinio gosodiadau eich hidlydd. Os na welwch unrhyw ddata o hyd, ewch ati i greu tablau yn y Lluniwr Data. Pan fyddant yn defnyddio storfa, byddwch yn gallu eu monitro yma.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Dydy'r gronfa ddata amser rhedeg ddim ar gael.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Gan nad yw'r gronfa ddata amser rhedeg ar gael, mae rhai nodweddion wedi'u hanalluogi ac ni allwn ddangos unrhyw wybodaeth ar y dudalen hon.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Dim modd creu defnyddiwr sgema gofod.
#YMSE: Error User name already exists
userAlreadyExistsError=Mae'r enw defnyddiwr yn bodoli'n barod.
#YMSE: Error Authentication failed
authenticationFailedError=Dilysu wedi methu.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Mae'r defnyddiwr wedi'i gloi o ganlyniad i fethu mewngofnodi gormod o weithiau. Gofynnwch am gyfrinair newydd i ddatgloi'r defnyddiwr.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Cyfrinair newydd wedi'i osod a'r defnyddiwr wedi'i ddatgloi.
#XMSG: user is locked message
userLockedMessage=Defnyddiwr wedi'i gloi.
#XCOL: Users table-view column Role
spaceRole=Rôl
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Rolau wedi'u Cwmpasu
#XCOL: Users table-view column Space Admin
spaceAdmin=Gweinyddwr Gofod
#XFLD: User section dropdown value Viewer
viewer=Dangosydd
#XFLD: User section dropdown value Modeler
modeler=Modelydd
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Intregreiddiwr Data
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Gweinyddwr Gofod
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Rôl y gofod wedi'i diweddaru
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Rôl Gofod heb ei diweddaru'n llwyddiannus.
#XFLD:
databaseUserNameSuffix=Ôl-ddodiad Enw Defnyddiwr Set Ddata
#XTXT: Space Schema password text
spaceSchemaPasswordText=I sefydlu cysylltiad i'r sgema hwn, copïwch eich cyfrinair. Os byddwch yn anghofio eich cyfrinair, gallwch wneud cais am un newydd.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Platfform Cwmwl
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=I sefydlu mynediad drwy'r defnyddiwr yma, dylech alluogi defnyddio a chopïo'r manylion adnabod. Os byddwch yn gallu copïo'r manylion adnabod heb gyfrinair yn unig, gwnewch yn siŵr eich bod yn ychwanegu'r cyfrinair yn nes ymlaen.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Galluogi Platfform Defnyddio mewn Cwmwl
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Manylion Adnabod ar gyfer Gwasanaeth a Roddwyd gan Ddefnyddiwr:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Manylion Adnabod ar gyfer Gwasanaeth a Roddwyd gan Ddefnyddiwr (Heb Gyfrinair):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Copïo Manylion Adnabod Heb Gyfrinair
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Copïo'r Manylion Adnabod yn Llawn
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Copïo'r Cyfrinair
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Manylion adnabod wedi'u copïo i'r clipfwrdd
#XMSG: Password copied to clipboard
passwordCopiedMessage=Cyfrinair wedi'i gopïo i'r clipfwrdd
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Creu Defnyddiwr Cronfa Ddata
#XMSG: Database Users section title
databaseUsers=Defnyddwyr Cronfa Ddata
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Manylion Defnyddiwr Cronfa Ddata
#XFLD: database user read audit log
databaseUserAuditLogRead=Galluogi Logiau Golygu ar gyfer Gweithrediadau Darllen a Chadw Logiau ar gyfer
#XFLD: database user change audit log
databaseUserAuditLogChange=Galluogi Logiau Golygu ar gyfer Gweithrediadau Newid a Chadw Logiau ar gyfer
#XMSG: Cloud Platform Access
cloudPlatformAccess=Mynediad Platfform Cwmwl
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Sefydlu mynediad at eich cynhwysydd HANA Deployment Infrastructure (HDI) drwy'r defnyddiwr cronfa ddata hwn. I gysylltu â'ch cynhwysydd HDI, rhaid i fodelu SQL fod ar waith
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Galluogi Defnyddio HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Ydych chi am i'r data yn eich gofod allu cael ei ddefnyddio gan offer neu apiau eraill?
#XFLD: Enable Consumption
enableConsumption=Galluogi Defnyddio SQL
#XFLD: Enable Modeling
enableModeling=Galluogi Modelu SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Amlyncu Data
#XMSG: Privileges for Data Consumption
privilegesConsumption=Defnyddio Data ar gyfer Offer Allanol
#XFLD: SQL Modeling
sqlModeling=Modelu SQL
#XFLD: SQL Consumption
sqlConsumption=Defnyddio SQL
#XFLD: enabled
enabled=Wedi galluogi
#XFLD: disabled
disabled=Wedi analluogi
#XFLD: Edit Privileges
editPrivileges=Golygu Caniatâd
#XFLD: Open Database Explorer
openDBX=Agor Archwilydd Cronfa Ddata
#XFLD: create database user hint
databaseCreateHint=Cofiwch na fydd modd i chi newid eich enw defnyddiwr ar ôl cadw.
#XFLD: Internal Schema Name
internalSchemaName=Enw Sgema Mewnol
#YMSE: Failed to load database users
loadDatabaseUserError=Wedi methu llwytho defnyddwyr cronfa ddata
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Wedi methu dileu defnyddwyr cronfa ddata
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Wedi dileu defnyddiwr cronfa ddata
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Wedi dileu defnyddwyr cronfa ddata
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Wedi creu defnyddiwr cronfa ddata
#YMSE: Failed to create database user
createDatabaseUserError=Wedi methu creu defnyddiwr cronfa ddata
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Wedi diweddaru defnyddiwr cronfa ddata
#YMSE: Failed to update database user
updateDatabaseUserError=Wedi methu diweddaru defnyddiwr cronfa ddata
#XFLD: HDI Consumption
hdiConsumption=Defnyddio HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Mynediad Cronfa Ddata
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Gwnewch ddata eich gofod ar gael i'w ddefnyddio yn ddiofyn. Bydd y modelau yn y llunwyr yn caniatáu i'r data fod ar gael i'w ddefnyddio yn awtomatig.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Defnydd Diofyn o Ddata Diofyn:
#XFLD: Database User Name
databaseUserName=Enw Defnyddiwr Set Ddata
#XMSG: Database User creation validation error message
databaseUserValidationError=Mae rhai meysydd yn annilys. Gwiriwch y meysydd gofynnol er mwyn bwrw ymlaen.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Dim modd rhoi amlyncu data ar gael am fod y defnyddiwr hwn wedi'i symud.
#XBUT: Remove Button Text
remove=Tynnu
#XBUT: Remove Spaces Button Text
removeSpaces=Tynnu Gofodau
#XBUT: Remove Objects Button Text
removeObjects=Tynnu Gwrthrychau
#XMSG: No members have been added yet.
noMembersAssigned=Does dim aelodau wedi'u hychwanegu ar hyn o bryd.
#XMSG: No users have been added yet.
noUsersAssigned=Does dim defnyddwyr wedi'u hychwanegu ar hyn o bryd.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Does dim defnyddwyr cronfa ddata wedi'u creu, neu nid yw eich hidlydd yn dangos unrhyw ddata.
#XMSG: Please enter a user name.
noDatabaseUsername=Rhowch enw defnyddiwr.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Mae'r enw defnyddiwr yn rhy hir. Defnyddiwch un byrrach.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Does dim caniatâd wedi'i alluogi, a bydd gan y defnyddiwr cronfa ddata hwn swyddogaethau cyfyngedig. Bwrw ymlaen?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=I alluogi logiau archwilio ar gyfer gweithredoedd newid, rhaid galluogi defnyddio data hefyd. Ydych chi am wneud hyn?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=I alluogi logiau archwilio ar gyfer gweithredoedd darllen, rhaid galluogi defnyddio data hefyd. Ydych chi am wneud hyn?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=I alluogi defnyddio HDI, rhaid i defnyddio data a defnydd data gael eu galluogi hefyd. Ydych chi am wneud hyn?
#XMSG:
databaseUserPasswordText=I sefydlu cysylltiad i'r defnyddiwr cronfa ddata yma, dylech gopïo eich cyfrinair. Os byddwch yn anghofio eich cyfrinair, gallwch wneud cais am un newydd.
#XTIT: Space detail section members title
detailsSectionMembers=Aelodau
#XMSG: New password set
newPasswordSet=Cyfrinair newydd wedi'i osod
#XFLD: Data Ingestion
dataIngestion=Amlyncu Data
#XFLD: Data Consumption
dataConsumption=Defnyddio Data
#XFLD: Privileges
privileges=Hawliau
#XFLD: Enable Data ingestion
enableDataIngestion=Galluogi Amlyncu Data
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Logio'r gweithrediadau darllen a newid ar gyfer amlyncu data.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Gwnewch ddata eich gofod ar gael yn eich cynwysyddion HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Galluogi Defnyddio Data
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Caniatáu i apiau neu offer eraill ddefnyddio data eich gofod.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=I sefydlu mynediad drwy'r defnyddiwr cronfa ddata yma, dylech gopïo'r manylion adnabod i'ch gwasanaeth a roddwyd gan ddefnyddiwr. Os byddwch yn gallu copïo'r manylion adnabod heb gyfrinair yn unig, gwnewch yn siŵr eich bod yn ychwanegu'r cyfrinair yn nes ymlaen.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Capasiti Amser Rhedeg Llif Data ({0}:{1} awr o {2} awr)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Doedd dim modd llwytho'r capasiti amser rhedeg llif data
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Gall defnyddiwr roi defnydd data i ddefnyddwyr eraill.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Galluogi Defnyddio Data gyda Dewis Grant
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=I alluogi defnyddio data gydag opsiwn grant, mae angen galluogi defnyddio data. Ydych chi am roi'r ddau ar waith?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Rhoi'r Llyfrgell Ddarogan Awtomatig (APL) a'r Llyfrgell Ddadansoddi Darogan (PAL) ar waith
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Gall y defnyddiwr ddefnyddio swyddogaethau dysgu peiriant planedig SAP Hana Cloud.
#XFLD: Password Policy
passwordPolicy=Polisi cyfrinair
#XMSG: Password Policy
passwordPolicyHint=Galluogi neu analluogi'r polisi cyfrinair wedi'i ffurfweddu yma.
#XFLD: Enable Password Policy
enablePasswordPolicy=Rhoi Polisi Cyfrinair ar Waith
#XMSG: Read Access to the Space Schema
readAccessTitle=Mynediad Darllen ar gyfer Sgema’r Gofod
#XMSG: read access hint
readAccessHint=Caniatáu i’r defnyddiwr cronfa ddata gysylltu offer allanol i sgema’r gofod a darllen gweddau sydd ar gael i’w defnyddio.
#XFLD: Space Schema
spaceSchema=Sgema’r Gofod
#XFLD: Enable Read Access (SQL)
enableReadAccess=Caniatáu Mynediad Darllen (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Caniatáu i’r defnyddiwr roi caniatâd darllen i ddefnyddwyr eraill.
#XFLD: With Grant Option
withGrantOption=Dewis Rhoi Caniatâd
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Gwnewch ddata eich gofod ar gael yn eich cynwysyddion HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Galluogi Defnyddio HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Mynediad Ysgrifennu i Sgema SQL Agored y Defnyddiwr
#XMSG: write access hint
writeAccessHint=Caniatáu i ddefnyddiwr y gronfa ddata gysylltu offer allanol â sgema SQL Agored y defnyddiwr i greu endidau data ac i amlyncu data i'w defnyddio yn y gofod.
#XFLD: Open SQL Schema
openSQLSchema=Sgema SQL Agored
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Galluogi Caniatâd Ysgrifennu (SQL, DDL, a DML)
#XMSG: audit hint
auditHint=Logio’r gweithrediadau darllen a newid yn y sgema SQL Agored
#XMSG: data consumption hint
dataConsumptionHint=Datgelu’r holl weddau newydd yn y gofod yn ddiofyn i'w defnyddio. Gall cymedrolwyr ddiystyru'r gosodiad hwn ar gyfer gweddau unigol trwy ddefnyddio’r switsh “Ar Gael i’w Ddefnyddio” ym mhanel ochr allbwn y wedd. Gallwch hefyd ddewis y fformatau y mae gweddau yn agored iddynt.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Ar Gael i’w Defnyddio yn Ddiofyn
#XMSG: database users hint consumption hint
databaseUsersHint2New=Creu defnyddwyr cronfa ddata i gysylltu offer allanol i SAP Datasphere. Gosodwch freintiau i ganiatáu i ddefnyddwyr ddarllen data’r gofod ac i greu endidau data (DDL) ac amlyncu data (DML) i’w defnyddio yn y gofod hwn.
#XFLD: Read
read=Darllen
#XFLD: Read (HDI)
readHDI=Darllen (HDI)
#XFLD: Write
write=Ysgrifennu
#XMSG: HDI Containers Hint
HDIContainersHint2=Galluogi mynediad i'ch cynwysyddion Seilwaith Defnyddio SAP HANA (HDI) yn eich gofod. Gall cymedrolwyr ddefnyddio arteffactau HDI fel ffynonellau ar gyfer gweddau, a gall cleientiaid HDI gael mynediad i ddata eich gofod.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Agor y ddeialog gwybodaeth
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Mae’r defnyddiwr cronfa ddata wedi’i gloi. Agorwch y ddeialog i’w ddatgloi
#XFLD: Table
table=Tabl
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Cysylltiad Partner
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Ffurfweddiad Cysylltiad Partner
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Diffiniwch eich teilsen cysylltiad partner eich hun trwy ychwanegu eich URL a'ch eicon iFrame. Mae'r ffurfweddiad hwn ar gael i'r tenant hwn yn unig.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Enw'r Deilsen
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Tarddiad Neges y Post iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Eicon
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Methu dod o hyd i ffurfweddiad(au) cysylltiad partner.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Does dim modd dangos ffurfweddiadau cysylltiad partner pan nad yw'r gronfa ddata amser rhedeg ar gael.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Creu Ffurfweddiad Cysylltiad Partner
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Llwytho Eicon i Fyny
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Dewis (uchafswm maint: 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Enghraifft o Deilsen Bartner
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Pori
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Mae Ffurfweddiad Cysylltiad Partner wedi'i greu'n llwyddiannus.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Roedd gwall wrth ddileu ffurfweddiad(au) Cysylltiad Partner.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Mae Ffurfweddiad Cysylltiad Partner wedi'i ddileu'n llwyddiannus.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Roedd gwall wrth gyrchu Ffurfweddiadau Cysylltiad Partner.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Doedd dim modd llwytho'r ffeil i fyny am ei fod yn fwy na'r uchafswm maint sef 200KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Creu Ffurfweddiad Cysylltiad Partner
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Dileu Ffurfweddiad Cysylltiad Partner
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Doedd dim modd creu Teilsen Bartner.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Doedd dim modd dileu Teilsen Bartner.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Ailosod Gosodiadau Cysylltydd SAP HANA Cloud Cwsmer wedi methu
#XFLD: Workload Class
workloadClass=Dosbarth Llwyth Gwaith
#XFLD: Workload Management
workloadManagement=Rheoli Llwyth Gwaith
#XFLD: Priority
workloadClassPriority=Blaenoriaeth
#XMSG:
workloadManagementPriorityHint=Gallwch nodi blaenoriaeth y gofod hwn wrth holi’r gronfa ddata. Rhowch werth o 1 (blaenoriaeth isaf) i 8 (blaenoriaeth uchaf). Mewn sefyllfaoedd pan fo gofodau’n cystadlu am y llinynnau sydd ar gael, bydd y rheini â blaenoriaethau uwch yn rhedeg cyn gofodau â blaenoriaethau is.
#XMSG:
workloadClassPriorityHint=Gallwch nodi blaenoriaeth y gofod o 0 (isaf) i 8 (uchaf). Gweithredir datganiadau gofod â blaenoriaeth uchel cyn datganiadau gofodau eraill sydd â blaenoriaeth is. Y flaenoriaeth ddiofyn yw 5. Gan fod gwerth 9 wedi'i gadw i weithrediadau system, nid yw ar gael ar gyfer gofod.
#XFLD: Statement Limits
workloadclassStatementLimits=Cyfyngiadau Datganiad
#XFLD: Workload Configuration
workloadConfiguration=Ffurfweddu Llwyth Gwaith
#XMSG:
workloadClassStatementLimitsHint=Gallwch chi nodi'r nifer (neu'r ganran) uchaf o edafedd a GBs o gof y gall datganiadau sy'n cydredeg yn y gofod eu defnyddio. Gallwch nodi unrhyw werth neu ganran rhwng 0 (dim terfyn) a chyfanswm y cof a'r edafedd sydd ar gael yn y tenant. \n\n Os ydych chi'n nodi terfyn edefyn, byddwch yn ymwybodol y gall ostwng perfformiad. \n\n Os nodwch derfyn cof, ni chaiff y datganiadau sy'n cyrraedd y terfyn cof eu rhedeg.
#XMSG:
workloadClassStatementLimitsDescription=Mae’r ffurfweddiad diofyn yn darparu terfynau adnoddau hael, tra’n atal unrhyw ofod unigol rhag gorlwytho’r system.
#XMSG:
workloadClassStatementLimitCustomDescription=Gallwch chi osod terfynau cof a llinyn uchaf y mae datganiadau sy’n rhedeg yr un pryd yn y gofod yn gallu eu defnyddio.
#XMSG:
totalStatementThreadLimitHelpText=Mae gosod y terfyn llinyn yn rhy isel yn gallu effeithio ar berfformiad datganiad, tra bod gwerthoedd rhy uchel neu 0 yn rhoi lle i’r gofod ddefnyddio holl linynnau sydd ar gael yn y system.
#XMSG:
totalStatementMemoryLimitHelpText=Mae gosod y terfyn cof yn rhy isel yn gallu achosi problemau diffyg cof, tra bod gwerthoedd rhy uchel neu 0 yn rhoi lle i’r gofod ddefnyddio holl linynnau sydd ar gael yn y system.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Rhowch ganran rhwng 1% a 70% (neu'r nifer cyfatebol) o gyfanswm nifer y llinynnau sydd ar gael yn eich tenant. Gall gosod terfyn y llinyn yn rhy isel effeithio ar berfformiad datganiadau, tra gall gwerthoedd rhy uchel effeithio ar berfformiad datganiadau mewn mannau eraill.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Rhowch ganran rhwng 1% a {0}% (neu''r nifer cyfatebol) o gyfanswm nifer y llinynnau sydd ar gael yn eich tenant. Gall gosod terfyn y llinyn yn rhy isel effeithio ar berfformiad datganiadau, tra gall gwerthoedd rhy uchel effeithio ar berfformiad datganiadau mewn mannau eraill.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Rhowch werth neu ganran rhwng 0 (dim terfyn) a faint o gof sydd ar gael yn eich tenant. Gall gosod terfyn y cof yn rhy isel effeithio ar berfformiad datganiadau, tra gall gwerthoedd rhy uchel effeithio ar berfformiad datganiadau mewn mannau eraill.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Cyfanswm Terfyn Edefyn Datganiad
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Edeifion
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Cyfanswm Terfyn Cof Datganiad
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Wedi methu llwytho Gwybodaeth SAP HANA cwsmer.
#XMSG:
minimumLimitReached=Wedi cyrraedd y terfyn isaf.
#XMSG:
maximumLimitReached=Wedi cyrraedd y terfyn uchaf.
#XMSG: Name Taken for Technical Name
technical-name-taken=Mae cysylltiad â’r enw technegol rydych chi wedi’i roi yn bodoli’n barod. Rhowch enw arall.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Mae’r enw technegol rydych chi wedi’i roi yn cynnwys mwy na 40 nod. Rhowch enw sydd â llai o nodau.
#XMSG: Technical name field empty
technical-name-field-empty=Rhowch enw technegol.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Dim ond llythrennau (a-z), rhifau (0-9) a thanlinellau (_) y gallwch eu defnyddio ar gyfer yr enw.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Ni all yr enw rydych chi’n ei roi ddechrau na gorffen gyda thanlinell (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Galluogi Terfynau Datganiad
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Gosodiadau
#XMSG: Connections tool hint in Space details section
connectionsToolHint=I greu neu olygu cysylltiadau, agorwch yr ap Cysylltiadau o'r bar llywio ochr neu cliciwch yma:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Mynd i Cysylltiadau
#XFLD: Not deployed label on space tile
notDeployedLabel=Gofod heb ei osod eto.
#XFLD: Not deployed additional text on space tile
notDeployedText=Gosodwch y gofod.
#XFLD: Corrupt space label on space tile
corruptSpace=Aeth rhywbeth o'i le.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Rhowch gynnig ar ailosod neu cysylltwch â chymorth
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Data Log Archwilio
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Data Gweinyddol
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Data Arall
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Data mewn Gofodau
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Ydych chi wir am ddatgloi'r gofod?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Ydych chi wir am gloi'r gofod?
#XFLD: Lock
lock=Cloi
#XFLD: Unlock
unlock=Datgloi
#XFLD: Locking
locking=Yn cloi
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Gofod wedi'i gloi
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Gofod wedi'i ddatgloi
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Gofodau wedi'u cloi
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Gofodau wedi'u datgloi
#YMSE: Error while locking a space
lockSpaceError=Does dim modd cloi'r gofod.
#YMSE: Error while unlocking a space
unlockSpaceError=Does dim modd datgloi'r gofod.
#XTIT: popup title Warning
confirmationWarningTitle=Rhybudd
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Mae'r gofod wedi'i gloi gan rywun.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Mae'r gofod wedi'i gloi gan y system oherwydd bod logiau archwilio'n defnyddio llawer iawn o GB ar y ddisg.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Mae'r gofod wedi'i gloi gan y system oherwydd ei fod yn defnyddio mwy o le storio ar ddisg neu gof na'r hyn sydd wedi'i neilltuo.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Ydych chi wir am ddatgloi'r gofodau sydd wedi’u dewis?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Ydych chi wir am gloi'r gofodau sydd wedi’u dewis?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Golygydd Rôl wedi'i Chwmpasu
#XTIT: ECN Management title
ecnManagementTitle=Rheoli Cygnau Cyfrifyddu Elastig a Gofod
#XFLD: ECNs
ecns=Cygnau Cyfrifyddu Elastig
#XFLD: ECN phase Ready
ecnReady=Parod
#XFLD: ECN phase Running
ecnRunning=Yn Rhedeg
#XFLD: ECN phase Initial
ecnInitial=Ddim yn Barod
#XFLD: ECN phase Starting
ecnStarting=Yn Dechrau
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Cychwyn wedi methu
#XFLD: ECN phase Stopping
ecnStopping=Wrthi'n Stopio
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Stopio wedi methu
#XBTN: Assign Button
assign=Neilltuo Gofodau
#XBTN: Start Header-Button
start=Dechrau
#XBTN: Update Header-Button
repair=Diweddaru
#XBTN: Stop Header-Button
stop=Stop
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 ar ôl
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} Oriau-bloc sy''n Weddill
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} Awr-bloc sy''n Weddill
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Creu Cwgn Cyfrifyddu Elastig
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Golygu Cwgn Cyfrifyddu Elastig
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Dileu Cwgn Cyfrifyddu Elastig
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Neilltuo Gofodau
#XFLD: ECN ID
ECNIDLabel=Cwgn Cyfrifyddu Elastig
#XTXT: Selected toolbar text
selectedToolbarText=Wedi Dewis: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Cygnau Cyfrifyddu Elastig
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Nifer y Gwrthrychau
#XTIT: Object assignment - Dialog header text
selectObjects=Dewiswch y bylchau a'r gwrthrychau rydych am eu neilltuo i'ch cwgn cyfrifyddu elastig:
#XTIT: Object assignment - Table header title: Objects
objects=Gwrthrychau
#XTIT: Object assignment - Table header: Type
type=Math
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Byddwch yn ymwybodol y bydd dileu defnyddiwr cronfa ddata yn arwain at ddileu pob cofnod log archwilio a gynhyrchir. Os ydych chi am gadw'r logiau archwilio, ystyriwch eu hallgludo cyn i chi ddileu defnyddiwr y gronfa ddata.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Byddwch yn ymwybodol y bydd dadneilltuo cynhwysydd HDI o'r gofod yn arwain at ddileu'r holl gofnodion log archwilio a gynhyrchir. Os ydych chi am gadw'r logiau archwilio, ystyriwch eu hallforio cyn i chi ddadneilltuo'r cynhwysydd HDI.
#XTXT: All audit logs
allAuditLogs=Pob cofnod log archwilio a gynhyrchir ar gyfer y gofod
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Byddwch yn ymwybodol y bydd analluogi polisi archwilio (gweithredoedd darllen neu newid) yn arwain at ddileu ei holl gofnodion log archwilio. Os ydych am gadw'r cofnodion log archwilio, ystyriwch eu hallgludo cyn i chi analluogi'r polisi archwilio.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Does dim gofod na gwrthrychau wedi'u neilltuo eto
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=I ddechrau gweithio gyda'ch cwgn cyfrifyddu elastig, neilltuwch ofod neu wrthrychau ar ei gyfer.
#XTIT: No Spaces Illustration title
noSpacesTitle=Dim gofod wedi'i greu eto
#XTIT: No Spaces Illustration description
noSpacesDescription=I ddechrau caffael data, ewch ati i greu gofod.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Mae'r bin ailgylchu yn wag
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Gallwch chi adfer eich gofodau wedi'u dileu o'r fan hon.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Ar ôl i’r gofod gael ei osod, bydd y defnyddwyr cronfa ddata canlynol yn cael eu dileu {0} ac ni fydd modd eu hadfer:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Dileu Defnyddwyr Cronfa Ddata
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=Mae’r ID eisoes yn bodoli
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Defnyddiwch y llythrennau bach a - z a’r rhifau 0 -9 yn unig
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=Rhaid i’r ID fod yn o leiaf {0} nod o hyd
#XMSG: ecn id length warning
ecnIdLengthWarning=Wedi pasio''r uchafswm o {0} nod.
#XFLD: open System Monitor
systemMonitor=Monitro System
#XFLD: open ECN schedule dialog menu entry
schedule=Amserlen
#XFLD: open create ECN schedule dialog
createSchedule=Creu Amserlen
#XFLD: open change ECN schedule dialog
changeSchedule=Golygu Amserlen
#XFLD: open delete ECN schedule dialog
deleteSchedule=Dileu Amserlen
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Neilltuo Amserlen i Mi
#XFLD: open pause ECN schedule dialog
pauseSchedule=Rhewi Amserlen
#XFLD: open resume ECN schedule dialog
resumeSchedule=Ailgychwyn Amserlen
#XFLD: View Logs
viewLogs=Gweld Logiau
#XFLD: Compute Blocks
computeBlocks=Blociau Cyfrifo
#XFLD: Memory label in ECN creation dialog
ecnMemory=Cof (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Lle storio (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Nifer y CPU
#XFLD: ECN updated by label
changedBy=Wedi'i Newid Gan
#XFLD: ECN updated on label
changedOn=Wedi'i Newid Ar
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Wedi creu cwgn cyfrifyddu elastig
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Doedd dim modd creu'r cwgn cyfrifyddu elastig
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Wedi diweddaru cwgn cyfrifyddu elastig
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Doedd dim modd diweddaru'r cwgn cyfrifyddu elastig
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Wedi dileu cwgn cyfrifyddu elastig
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Doedd dim modd dileu'r cwgn cyfrifyddu elastig
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Dechrau cwgn cyfrifyddu elastig
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Rhoi stop ar y cwgn cyfrifyddu elastig
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Doedd dim modd dechrau'r cwgn cyfrifyddu elastig
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Doedd dim modd rhoi stop ar y cwgn cyfrifyddu elastig
#XBUT: Add Object button for an ECN
assignObjects=Ychwanegu Gwrthrychau
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Ychwanegu Pob Gwrthrych yn Awtomatig
#XFLD: object type label to be assigned
objectTypeLabel=Math (Defnydd Semantig)
#XFLD: assigned object type label
assignedObjectTypeLabel=Math
#XFLD: technical name label
TechnicalNameLabel=Enw Technegol
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Dewiswch y gwrthrychau rydych am eu hychwanegu i'r cwgn cyfrifyddu elastig
#XTIT: Add objects dialog title
assignObjectsTitle=Neilltuo Gwrthrychau
#XFLD: object label with object count
objectLabel=Gwrthrych
#XMSG: No objects available to add message.
noObjectsToAssign=Dim gwrthrychau ar gael i'w neilltuo.
#XMSG: No objects assigned message.
noAssignedObjects=Dim gwrthrychau wedi'u neilltuo.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Rhybudd
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Dileu
#XMSG: Remove objects popup text
removeObjectsConfirmation=Ydych chi wir am dynnu'r gwrthrychau dan sylw?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Ydych chi wir am dynnu'r gofodau dan sylw?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Tynnu Gofodau
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Mae gwrthrychau amlwg wedi'u tynnu
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Mae gwrthrychau amlwg wedi'u neilltuo
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Holl Wrthrychau Amlwg
#XFLD: Spaces tab label
spacesTabLabel=Gofodau
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Gwrthrychau Amlwg
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Mae'r gofodau wedi'u tynnu
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Mae gofod wedi'i dynnu
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Doedd dim modd neilltuo na thynnu gofodau.
#YMSE: Error while removing objects
removeObjectsError=Doedd dim modd neilltuo neu dynnu'r gwrthrychau.
#YMSE: Error while removing object
removeObjectError=Doedd dim modd neilltuo neu dynnu'r gwrthrych,
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Dydy’r rhif a gafodd ei ddewis yn flaenorol ddim yn ddilys mwyach. Dewiswch rif dilys.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Dewiswch ddosbarth perfformiad dilys.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Mae''r dosbarth perfformiad "{0}" a ddewiswyd yn flaenorol yn annilys ar hyn o bryd. Dewiswch y dosbarth perfformiad dilys.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Ydych chi'n siŵr eich bod am ddileu'r cwgn cyfrifyddu elastig?
#XFLD: tooltip for ? button
help=Help
#XFLD: ECN edit button label
editECN=Ffurfweddu
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Endid - Model Perthynas
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Tabl Lleol
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Tabl Pell
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Model Dadansoddol
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Cadwyn Tasgau
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Llif Data
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Llif Dyblygu
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Llif Trawsnewid
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Chwilio Clyfar
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Ystorfa
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Chwilio Menter
#XFLD: Technical type label for View
DWC_VIEW=Gwedd
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Cynnyrch Data
#XFLD: Technical type label for Data Access Control
DWC_DAC=Rheolydd Mynediad Data
#XFLD: Technical type label for Folder
DWC_FOLDER=Ffolder
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Endid Busnes
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Amrywiad Endid Busnes
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Senario Cyfrifoldeb
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Model Ffeithiau
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Persbectif
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Model Defnydd
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Cysylltiad Pell
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Amrywiant Model Ffeithiau
#XMSG: Schedule created alert message
createScheduleSuccess=Amserlen wedi'i chreu
#XMSG: Schedule updated alert message
updateScheduleSuccess=Amserlen wedi'i diweddaru
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Amserlen wedi'i dileu
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Amserlen wedi'i neilltuo i chi
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Rhewi 1 amserlen
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Ailgychwyn 1 amserlen
#XFLD: Segmented button label
availableSpacesButton=Ar Gael
#XFLD: Segmented button label
selectedSpacesButton=Wedi Dewis
#XFLD: Visit website button text
visitWebsite=Ewch i'r Wefan
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Bydd yr iaith ffynhonnell a ddewiswyd yn flaenorol yn cael ei dynnu.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Galluogi
#XFLD: ECN performance class label
performanceClassLabel=Dosbarth Perfformiad
#XTXT performance class memory text
memoryText=Cof
#XTXT performance class compute text
computeText=Cyfrifo
#XTXT performance class high-compute text
highComputeText=Cyfrifo Uchel
#XBUT: Recycle Bin Button Text
recycleBin=Bin Ailgylchu
#XBUT: Restore Button Text
restore=Adfer
#XMSG: Warning message for new Workload Management UI
priorityWarning=Mae'r ardal hon mewn gosodiad darllen yn unig. Gallwch newid y flaenoriaeth gofod yn yr ardal System / Ffurfweddu / Rheoli Llwyth Gwaith.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Mae'r ardal hon mewn gosodiad darllen yn unig. Gallwch newid y gofod ffurfweddu llwyth gwaith yn yr ardal System / Ffurfweddu / Rheoli Llwyth Gwaith.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPUs
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark Memory (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Amlyncu Cynnyrch Data
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Dim data ar gael oherwydd bod y tabl wrthi'n cael ei roi ar waith
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Dim data ar gael oherwydd bod y tabl wrthi'n cael ei lwytho
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Golygu Mapiau Enghraifft
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
