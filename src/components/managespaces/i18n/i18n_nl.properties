#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Bewaking
#XTXT: Type name for spaces in browser tab page title
space=Ruimte
#_____________________________________
#XFLD: Spaces label in
spaces=Ruimten
#XFLD: Manage plan button text
manageQuotaButtonText=Plan beheren
#XBUT: Manage resources button
manageResourcesButton=Resources beheren
#XFLD: Create space button tooltip
createSpace=Ruimte maken
#XFLD: Create
create=Maken
#XFLD: Deploy
deploy=Implementeren
#XFLD: Page
page=Pagina
#XFLD: Cancel
cancel=Annuleren
#XFLD: Update
update=Bijwerken
#XFLD: Save
save=Opslaan
#XFLD: OK
ok=OK
#XFLD: days
days=Dagen
#XFLD: Space tile edit button label
edit=Bewerken
#XFLD: Auto Assign all objects to space
autoAssign=Automatisch toewijzen
#XFLD: Space tile open monitoring button label
openMonitoring=Bewaken
#XFLD: Delete
delete=Verwijderen
#XFLD: Copy Space
copy=Kopiëren
#XFLD: Close
close=Sluiten
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Actief
#XFLD: Space status locked
lockedLabel=Geblokkeerd
#XFLD: Space status critical
criticalLabel=Kritiek
#XFLD: Space status cold
coldLabel=Koud
#XFLD: Space status deleted
deletedLabel=Verwijderd
#XFLD: Space status unknown
unknownLabel=Onbekend
#XFLD: Space status ok
okLabel=OK
#XFLD: Database user expired
expired=Verlopen
#XFLD: deployed
deployed=Geïmplementeerd
#XFLD: not deployed
notDeployed=Niet geïmplementeerd
#XFLD: changes to deploy
changesToDeploy=Te implementeren wijzigingen
#XFLD: pending
pending=Bezig met implementeren
#XFLD: designtime error
designtimeError=Ontwerptijdfout
#XFLD: runtime error
runtimeError=Runtimefout
#XFLD: Space created by label
createdBy=Gemaakt door
#XFLD: Space created on label
createdOn=Gemaakt op
#XFLD: Space deployed on label
deployedOn=Geïmplementeerd op
#XFLD: Space ID label
spaceID=Ruimte-ID
#XFLD: Priority label
priority=Prioriteit
#XFLD: Space Priority label
spacePriority=Prioriteit van ruimte
#XFLD: Space Configuration label
spaceConfiguration=Ruimteconfiguratie
#XFLD: Not available
notAvailable=Niet beschikbaar
#XFLD: WorkloadType default
default=Standaard
#XFLD: WorkloadType custom
custom=Aangepast
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Toegang tot data lake
#XFLD: Translation label
translationLabel=Vertaling
#XFLD: Source language label
sourceLanguageLabel=Brontaal
#XFLD: Translation CheckBox label
translationCheckBox=Vertaling inschakelen
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Ruimte implementeren om gebruikersdetails te openen.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Ruimte implementeren om Database Explorer te openen.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled="U kunt deze ruimte niet gebruiken voor toegang tot het data lake omdat deze al door een andere ruimte wordt gebruikt."
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Gebruik deze ruimte om het data lake te openen.
#XFLD: Space Priority minimum label extension
low=Laag
#XFLD: Space Priority maximum label extension
high=Hoog
#XFLD: Space name label
spaceName=Naam ruimte
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Objecten implementeren
#XTIT: Copy spaces dialog title
copySpaceDialogTitle={0} kopiëren
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(Niet geselecteerd)
#XTXT Human readable text for language code "af"
af=Afrikaans
#XTXT Human readable text for language code "ar"
ar=Arabisch
#XTXT Human readable text for language code "bg"
bg=Bulgaars
#XTXT Human readable text for language code "ca"
ca=Catalaans
#XTXT Human readable text for language code "zh"
zh=Chinees (vereenvoudigd)
#XTXT Human readable text for language code "zf"
zf=Chinees
#XTXT Human readable text for language code "hr"
hr=Kroatisch
#XTXT Human readable text for language code "cs"
cs=Tsjechisch
#XTXT Human readable text for language code "cy"
cy=Welsh
#XTXT Human readable text for language code "da"
da=Deens
#XTXT Human readable text for language code "nl"
nl=Nederlands
#XTXT Human readable text for language code "en-UK"
en-UK=Engels (Verenigd Koninkrijk)
#XTXT Human readable text for language code "en"
en=Engels (Verenigde Staten)
#XTXT Human readable text for language code "et"
et=Ests
#XTXT Human readable text for language code "fa"
fa=Perzisch
#XTXT Human readable text for language code "fi"
fi=Fins
#XTXT Human readable text for language code "fr-CA"
fr-CA=Frans (Canada)
#XTXT Human readable text for language code "fr"
fr=Frans
#XTXT Human readable text for language code "de"
de=Duits
#XTXT Human readable text for language code "el"
el=Grieks
#XTXT Human readable text for language code "he"
he=Hebreeuws
#XTXT Human readable text for language code "hi"
hi=Hindi
#XTXT Human readable text for language code "hu"
hu=Hongaars
#XTXT Human readable text for language code "is"
is=Ijslands
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=Italiaans
#XTXT Human readable text for language code "ja"
ja=Japans
#XTXT Human readable text for language code "kk"
kk=Kazaks
#XTXT Human readable text for language code "ko"
ko=Koreaans
#XTXT Human readable text for language code "lv"
lv=Lets
#XTXT Human readable text for language code "lt"
lt=Litouws
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=Noors
#XTXT Human readable text for language code "pl"
pl=Pools
#XTXT Human readable text for language code "pt"
pt=Portugees (Brazilië)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugees (Portugal)
#XTXT Human readable text for language code "ro"
ro=Roemeens
#XTXT Human readable text for language code "ru"
ru=Russisch
#XTXT Human readable text for language code "sr"
sr=Servisch
#XTXT Human readable text for language code "sh"
sh=Servo-Kroatisch
#XTXT Human readable text for language code "sk"
sk=Slowaaks
#XTXT Human readable text for language code "sl"
sl=Sloveens
#XTXT Human readable text for language code "es"
es=Spaans
#XTXT Human readable text for language code "es-MX"
es-MX=Spaans (Mexico)
#XTXT Human readable text for language code "sv"
sv=Zweeds
#XTXT Human readable text for language code "th"
th=Thai
#XTXT Human readable text for language code "tr"
tr=Turks
#XTXT Human readable text for language code "uk"
uk=Oekraïens
#XTXT Human readable text for language code "vi"
vi=Vietnamees
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Ruimten verwijderen
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Weet u zeker dat u de ruimte "{0}" wilt verplaatsen naar de prullenbak?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Weet u zeker dat u de {0} geselecteerde ruimten wilt verplaatsen naar de prullenbak?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Weet u zeker dat u ruimte "{0}" wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Weet u zeker dat u de {0} geselecteerde ruimten wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt. De volgende content wordt {1} verwijderd:
#XTXT: permanently
permanently=permanent
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=De volgende content wordt {0} verwijderd en kan niet meer worden hersteld:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Typ {0} om de verwijdering te bevestigen.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Controleer uw spelling en probeer het opnieuw.
#XTXT: All Spaces
allSpaces=Alle ruimten
#XTXT: All data
allData=Alle objecten en gegevens in de ruimte
#XTXT: All connections
allConnections=Alle in de ruimte gedefinieerde verbindingen
#XFLD: Space tile selection box tooltip
clickToSelect=Klikken om te selecteren
#XTXT: All database users
allDatabaseUsers=Alle objecten en gegevens in een willekeurig Open SQL-schema die aan de ruimte zijn gekoppeld
#XFLD: remove members button tooltip
deleteUsers=Leden verwijderen
#XTXT: Space long description text
description=Omschrijving (max. 4000 tekens)
#XFLD: Add Members button tooltip
addUsers=Leden toevoegen
#XFLD: Add Users button tooltip
addUsersTooltip=Gebruikers toevoegen
#XFLD: Edit Users button tooltip
editUsersTooltip=Gebruikers bewerken
#XFLD: Remove Users button tooltip
removeUsersTooltip=Gebruikers verwijderen
#XFLD: Searchfield placeholder
filter=Zoeken
#XCOL: Users table-view column health
health=Status
#XCOL: Users table-view column access
access=Toegang
#XFLD: No user found nodatatext
noDataText=Geen gebruiker gevonden
#XTIT: Members dialog title
selectUserDialogTitle=Leden toevoegen
#XTIT: User dialog title
addUserDialogTitle=Gebruikers toevoegen
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Verbindingen verwijderen
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Verbinding verwijderen
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Weet u zeker dat u de geselecteerde verbindingen wilt verwijderen? De verbindingen worden permanent verwijderd.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Verbindingen selecteren
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Verbinding delen
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Gedeelde verbindingen
#XFLD: Add remote source button tooltip
addRemoteConnections=Verbindingen toevoegen
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Verbindingen verwijderen
#XFLD: Share remote source button tooltip
shareConnections=Verbindingen delen
#XFLD: Tile-layout tooltip
tileLayout=Tegellay-out
#XFLD: Table-layout tooltip
tableLayout=Tabellay-out
#XMSG: Success message after creating space
createSpaceSuccessMessage=Ruimte gemaakt
#XMSG: Success message after copying space
copySpaceSuccessMessage=Ruimte "{0}" naar ruimte "{1}" kopiëren
#XMSG: Success message after deploying space
deploymentSuccessMessage=Ruimte-implementatie is gestart.
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Apache Spark-update is gestart
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Kon Apache Spark niet actualiseren
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Ruimtedetails geactualiseerd
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Ruimte is tijdelijk gedeblokkeerd.
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Ruimte verwijderd
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Ruimten verwijderd
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Ruimte is hersteld
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Ruimten zijn hersteld
#YMSE: Error while updating settings
updateSettingsFailureMessage=De ruimte-instellingen zijn niet bijgewerkt.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Het data lake is al aan een andere ruimte toegewezen. Er kan slechts een ruimte tegelijk toegang tot het data lake hebben.
#YMSE: Error while updating data lake option
virtualTablesExists=U kunt de toewijzing van deze ruimte aan het data lake niet ongedaan maken omdat er nog afhankelijkheden van virtuele tabellen* zijn. Verwijder de virtuele tabellen om de toewijzing van deze ruimte aan het data lake ongedaan te maken.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=De ruimte is niet gedeblokkeerd.
#YMSE: Error while creating space
createSpaceError=De ruimte is niet gemaakt.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Er bestaat al een ruimte met de naam {0}.
#YMSE: Error while deleting a single space
deleteSpaceError=De ruimte is niet verwijderd.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Uw ruimte “{0}” werkt niet meer naar behoren. Probeer de ruimte opnieuw te verwijderen. Vraag uw beheerder om uw ruimte te verwijderen of maak een ticket aan als het probleem blijft bestaan.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=De ruimtegegevens onder Bestanden zijn niet verwijderd.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=De gebruikers zijn niet verwijderd.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=De schema's zijn niet verwijderd.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=De verbindingen zijn niet verwijderd.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=De ruimtegegevens zijn niet verwijderd.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=De ruimten zijn niet verwijderd.
#YMSE: Error while restoring a single space
restoreSpaceError=De ruimte is niet hersteld.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=De ruimte kon niet worden hersteld.
#YMSE: Error while creating users
createUsersError=De gebruikers zijn niet toegevoegd.
#YMSE: Error while removing users
removeUsersError=We kunnen de gebruikers niet verwijderen.
#YMSE: Error while removing user
removeUserError=gebruiker verwijderen niet mogelijk.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=De gebruiker kan niet aan de geselecteerde scoperol worden toegevoegd. \n\n U kunt uzelf niet aan een scoperol toevoegen. Vraag eventueel uw beheerder om u aan een scoperol toe te voegen.
#YMSE: Error assigning user to the space
userAssignError=De gebruiker kan niet aan de ruimte worden toegewezen. \n\n De gebruiker is al aan het maximum toegestane aantal (100) ruimtes voor alle rollen in scope toegewezen.
#YMSE: Error assigning users to the space
usersAssignError=De gebruikers kunnen niet aan de ruimte worden toegewezen. \n\n De gebruiker is al aan het maximum toegestane aantal (100) ruimtes voor alle rollen in scope toegewezen.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=We kunnen de gebruikers niet ophalen. Probeer het later opnieuw.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=We kunnen de rollen in scope niet ophalen.
#YMSE: Error while fetching members
fetchUserError=De leden kunnen niet worden opgehaald. Probeer het later opnieuw.
#YMSE: Error while loading run-time database
loadRuntimeError=Er is geen informatie geladen uit de runtimedatabase.
#YMSE: Error while loading spaces
loadSpacesError=Er is een fout opgetreden tijdens het ophalen van uw ruimten.
#YMSE: Error while loading haas resources
loadStorageError=Er is een fout opgetreden tijdens het ophalen van de geheugengegevens.
#YMSE: Error no data could be loaded
loadDataError=Er is een fout opgetreden tijdens het ophalen van uw gegevens.
#XFLD: Click to refresh storage data
clickToRefresh=Klik hier om het opnieuw te proberen.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Ruimte verwijderen
#XCOL: Spaces table-view column name
name=Naam
#XCOL: Spaces table-view deployment status
deploymentStatus=Implementatiestatus
#XFLD: Disk label in space details
storageLabel=Schijf (GB)
#XFLD: In-Memory label in space details
ramLabel=Geheugen (GB)
#XFLD: Memory label on space card
memory=Geheugen voor opslag
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Ruimteopslag
#XFLD: Storage Type label in space details
storageTypeLabel=Geheugentype
#XFLD: Enable Space Quota
enableSpaceQuota=Ruimtequota inschakelen
#XFLD: No Space Quota
noSpaceQuota=Geen ruimtequota
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=SAP HANA Database (schijf en in-memory)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=SAP HANA Data Lake-bestanden
#XFLD: Available scoped roles label
availableRoles=Beschikbare rollen in scope
#XFLD: Selected scoped roles label
selectedRoles=Geselecteerde rollen in scope
#XCOL: Spaces table-view column models
models=Modellen
#XCOL: Spaces table-view column users
users=Gebruikers
#XCOL: Spaces table-view column connections
connections=Verbindingen
#XFLD: Section header overview in space detail
overview=Overzicht
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Applicaties
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Taaktoewijzing
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPU's
#XFLD: Memory label in Apache Spark section
memoryLabel=Geheugen (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Ruimteconfiguratie
#XFLD: Space Source label
sparkApplicationLabel=Applicatie
#XFLD: Cluster Size label
clusterSizeLabel=Clustergrootte
#XFLD: Driver label
driverLabel=Chauffeur
#XFLD: Executor label
executorLabel=Executor
#XFLD: max label
maxLabel=Max. gebruikt
#XFLD: TrF Default label
trFDefaultLabel=Standaardtransformatiestroom
#XFLD: Merge Default label
mergeDefaultLabel=Standaard samenvoegen
#XFLD: Optimize Default label
optimizeDefaultLabel=Standaard optimaliseren
#XFLD: Deployment Default label
deploymentDefaultLabel=Implementatie lokale tabel (bestand)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Objecttype
#XFLD: Task activity label
taskActivityLabel=Activiteit
#XFLD: Task Application ID label
taskApplicationIDLabel=Standaardapplicatie
#XFLD: Section header in space detail
generalSettings=Algemene instellingen
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Deze ruimte wordt momenteel geblokkeerd door het systeem.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Wijzigingen in deze sectie worden onmiddellijk geïmplementeerd.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Als u deze waarden wijzigt, kunnen er prestatieproblemen optreden.
#XFLD: Button text to unlock the space again
unlockSpace=Ruimte deblokkeren
#XFLD: Info text for audit log formatted message
auditLogText=Schakel auditlogs in om acties (auditbeleid) te registreren, lezen of wijzigen. Beheerders kunnen dan analyseren wie welke acties wanneer heeft uitgevoerd.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Auditlogs kunnen een grote hoeveelheid schijfruimte gebruiken in uw tenant. Als u een auditbeleid (acties lezen of wijzigen) inschakelt, moet u regelmatig het gebruik van opslagruimte bewaken (via de kaart Gebruikte schijfruimte in de Systeemmonitor) om volledig gebruik van de schijfruimte te voorkomen, waardoor de service mogelijk kan worden onderbroken. Als u een auditbeleid uitschakelt, worden alle auditloggegevens verwijderd. Als u de auditloggegevens wilt bewaren, kunt u deze exporteren voordat u het auditbeleid uitschakelt.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Help weergeven
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Deze ruimte overschrijdt de opslagruimte en wordt geblokkeerd in {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=uur
#XMSG: Unit for remaining time until space is locked again
minutes=minuten
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Auditing
#XFLD: Subsection header in space detail for auditing
auditing=Auditinstellingen voor ruimten
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Ruimten met status "kritiek": gebruikt geheugen is meer dan 90%
#XFLD: Green space tooltip
greenSpaceCountTooltip=Ruimten met status "OK": gebruikt geheugen ligt tussen 6% en 90%
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Ruimten met status "koud": gebruikt geheugen is 5% of minder
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Ruimten met status "kritiek": gebruikt geheugen is meer dan 90%
#XFLD: Green space tooltip
okSpaceCountTooltip=Ruimten met status "OK": gebruikt geheugen ligt tussen 6% en 90%
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Ruimten met status "geblokkeerd": geblokkeerd vanwege onvoldoende geheugen.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Geblokkeerde ruimten
#YMSE: Error while deleting remote source
deleteRemoteError=De verbindingen zijn niet verwijderd.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=Ruimte-ID kan later niet meer worden gewijzigd.\nGeldige tekens: A - Z, 0 - 9 en _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Voer naam voor ruimte in.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Voer een objectnaam in.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Voer ruimte-ID in.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Ongeldige tekens. Gebruik alleen A - Z, 0 - 9 en _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=Ruimte-ID bestaat al.
#XFLD: Space searchfield placeholder
search=Zoeken
#XMSG: Success message after creating users
createUsersSuccess=Gebruikers toegevoegd
#XMSG: Success message after creating user
createUserSuccess=Gebruiker toegevoegd
#XMSG: Success message after updating users
updateUsersSuccess={0} gebruikers bijgewerkt
#XMSG: Success message after updating user
updateUserSuccess=Gebruiker bijgewerkt
#XMSG: Success message after removing users
removeUsersSuccess={0} gebruikers verwijderd
#XMSG: Success message after removing user
removeUserSuccess=Gebruiker verwijderd
#XFLD: Schema name
schemaName=Naam schema
#XFLD: used of total
ofTemplate={0} van {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Toegewezen schijfruimte ({0} van {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Toegewezen geheugen ({0} van {1})
#XFLD: Storage ratio on space
accelearationRAM=Geheugenversnelling
#XFLD: No Storage Consumption
noStorageConsumptionText=Geen opslagquota toegewezen.
#XFLD: Used disk label in space overview
usedStorageTemplate=Schijfruimte gebruikte voor opslag ({0} van {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Geheugen gebruikt voor opslag ({0} van {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} van {1} schijfruimte
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} van {1} geheugen gebruikt
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} van {1} schijfruimte toegewezen
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} van {1} geheugen toegewezen
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Ruimtegegevens: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Andere gegevens: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Overweeg om uw plan uit te breiden of neem contact op met SAP Support.
#XCOL: Space table-view column used Disk
usedStorage=Schijfruimte gebruikt voor opslag
#XCOL: Space monitor column used Memory
usedRAM=Geheugen gebruikt voor opslag
#XCOL: Space monitor column Schema
tableSchema=Schema
#XCOL: Space monitor column Storage Type
tableStorageType=Geheugentype
#XCOL: Space monitor column Table Type
tableType=Tabeltype
#XCOL: Space monitor column Record Count
tableRecordCount=Aantal records
#XFLD: Assigned Disk
assignedStorage=Schijfruimte toegewezen aan opslag
#XFLD: Assigned Memory
assignedRAM=Geheugen toegewezen voor opslag
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Gebruikt geheugen
#XFLD: space status
spaceStatus=Status ruimte
#XFLD: space type
spaceType=Type ruimte
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=SAP BW-bridge
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Data Provider-product
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=U kunt ruimte {0} niet verwijderen omdat type ruimte {1} is.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=U kunt de {0} geselecteerde ruimten niet verwijderen. Ruimten met de volgende typen ruimte kunnen niet worden verwijderd: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Bewaken
#XFLD: Tooltip for edit space button
editSpace=Ruimte bewerken
#XMSG: Deletion warning in messagebox
deleteConfirmation=Weet u zeker dat u deze ruimte wilt verwijderen?
#XFLD: Tooltip for delete space button
deleteSpace=Ruimte verwijderen
#XFLD: storage
storage=Schijfruimte voor opslag
#XFLD: username
userName=Gebruikersnaam
#XFLD: port
port=Poort
#XFLD: hostname
hostName=Hostnaam
#XFLD: password
password=Wachtwoord
#XBUT: Request new password button
requestPassword=Nieuw wachtwoord aanvragen
#YEXP: Usage explanation in time data section
timeDataSectionHint=Maak tijdtabellen en -dimensies om te gebruiken in uw modellen en scenario's.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Wilt u dat de gegevens in uw ruimte in andere tools of apps kunnen worden gebruikt? Als ja, maakt u een of meerdere gebruikers die toegang hebben tot de gegevens in uw ruimte en geeft u aan of u wilt dat alle toekomstige ruimtegegevens standaard kunnen worden gebruikt.
#XTIT: Create schema popup title
createSchemaDialogTitle=Open SQL-schema maken
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Tijdtabellen en -dimensies maken
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Tijdtabellen en -dimensies bewerken
#XTIT: Time Data token title
timeDataTokenTitle=Tijdgegevens
#XTIT: Time Data token title
timeDataUpdateViews=Tijdgegevensviews bijwerken
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Bezig met aanmaken...
#XFLD: Time Data token creation error label
timeDataCreationError=Aanmaken is mislukt. Probeer het opnieuw.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Instellingen tijdtabel
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Vertaaltabellen
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Tijdsdimensies
#XFLD: Time Data dialog time range label
timeRangeHint=Definieer de periode.
#XFLD: Time Data dialog time data table label
timeDataHint=Geef uw tabel een naam.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Geef uw dimensies een naam.
#XFLD: Time Data Time range description label
timerangeLabel=Periode
#XFLD: Time Data dialog from year label
fromYearLabel=Van jaar
#XFLD: Time Data dialog to year label
toYearLabel=Tot jaar
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Kalendertype
#XFLD: Time Data dialog granularity label
granularityLabel=Granulariteit
#XFLD: Time Data dialog technical name label
technicalNameLabel=Technische naam
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Vertaaltabel voor kwartalen
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Vertaaltabel voor maanden
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Vertaaltabel voor dagen
#XFLD: Time Data dialog year label
yearLabel=Dimensie Jaar
#XFLD: Time Data dialog quarter label
quarterLabel=Dimensie Kwartaal
#XFLD: Time Data dialog month label
monthLabel=Dimensie Maand
#XFLD: Time Data dialog day label
dayLabel=Dimensie Dag
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriaans
#XFLD: Time Data dialog time granularity day label
day=Dag
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Max.lengte van 1000 tekens bereikt.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=De maximumperiode is 150 jaar.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower="Van jaar" moet eerder zijn dan "Tot jaar"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher="Van jaar" moet 1900 of later zijn
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher="Tot jaar" moet later zijn dan "Van jaar"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower="Tot jaar" moet eerder dan het huidige jaar plus 100 zijn
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Als het "Van Jaar" op een later kalenderjaar wordt ingesteld, gaan er mogelijk gegevens verloren
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Als het "Tot Jaar" op een eerder kalenderjaar wordt ingesteld, gaan er mogelijk gegevens verloren
#XMSG: Time Data creation validation error message
timeDataValidationError=Sommige velden zijn ongeldig. Controleer de verplichte velden om door te gaan.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Weet u zeker dat u de gegevens wilt verwijderen?
#XMSG: Time Data creation success message
createTimeDataSuccess=Tijdgegevens zijn gemaakt.
#XMSG: Time Data update success message
updateTimeDataSuccess=Tijdgegevens zijn bijgewerkt.
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Tijdgegevens zijn verwijderd.
#XMSG: Time Data creation error message
createTimeDataError=Er is een fout opgetreden tijdens het maken van tijdgegevens.
#XMSG: Time Data update error message
updateTimeDataError=Er is een fout opgetreden tijdens het bijwerken van tijdgegevens.
#XMSG: Time Data creation error message
deleteTimeDataError=Er is een fout opgetreden tijdens het verwijderen van tijdgegevens.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Tijdgegevens zijn niet geladen.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Waarschuwing
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Uw tijdgegevens zijn niet geladen: de gegevens worden in andere modellen gebruikt.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Uw tijdgegevens zijn niet geladen: de gegevens worden in een ander model gebruikt.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Dit veld is verplicht en mag niet leeg worden gelaten.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Openen in Database Explorer
#YMSE: Dimension Year
dimensionYearView=Dimensie "Jaar"
#YMSE: Dimension Year
dimensionQuarterView=Dimensie "Kwartaal"
#YMSE: Dimension Year
dimensionMonthView=Dimensie "Maand"
#YMSE: Dimension Year
dimensionDayView=Dimensie "Dag"
#XFLD: Time Data deletion object title
timeDataUsedIn=(gebruikt in {0} modellen)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(gebruikt in 1 model)
#XFLD: Time Data deletion table column provider
provider=Provider
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Afhankelijkheden
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Gebruiker voor ruimteschema maken
#XFLD: Create schema button
createSchemaButton=Open SQL-schema maken
#XFLD: Generate TimeData button
generateTimeDataButton=Tijdtabellen en -dimensies maken
#XFLD: Show dependencies button
showDependenciesButton=Afhankelijkheden weergeven
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Uw gebruiker moet lid zijn van de ruimte om deze bewerking te kunnen uitvoeren.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Gebruiker ruimteschema maken
#YMSE: API Schema users load error
loadSchemaUsersError=Lijst met gebruikers is niet geladen.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Details van gebruiker ruimteschema
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Weet u zeker dat u de geselecteerde gebruiker wilt verwijderen?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Gebruiker is verwijderd.
#YMSE: API Schema user deletion error
userDeleteError=De gebruiker is niet verwijderd.
#XFLD: User deleted
userDeleted=De gebruiker is verwijderd.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Waarschuwing
#XMSG: Remove user popup text
removeUserConfirmation=Wilt u de gebruiker echt verwijderen? De gebruiker en de toegewezen rollen in scope worden uit de ruimte verwijderd.
#XMSG: Remove users popup text
removeUsersConfirmation=Wilt u de gebruikers echt verwijderen? De gebruikers en hun toegewezen rollen in scope worden uit de ruimte verwijderd.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Verwijderen
#YMSE: No data text for available roles
noDataAvailableRoles=De ruimte wordt niet toegevoegd aan een rol in scope. \n Om gebruikers aan de ruimte toe te voegen, moeten deze eerst aan een of meerdere rollen in scope worden toegevoegd.
#YMSE: No data text for selected roles
noDataSelectedRoles=Geen geselecteerde rollen in scope
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Configuratiedetails Open SQL-schema
#XFLD: Label for Read Audit Log
auditLogRead=Auditlog voor leesbewerkingen inschakelen
#XFLD: Label for Change Audit Log
auditLogChange=Auditlog voor wijzigingsbewerkingen inschakelen
#XFLD: Label Audit Log Retention
auditLogRetention=Logs bewaren tot
#XFLD: Label Audit Log Retention Unit
retentionUnit=dagen
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Voer een geheel getal in tussen {0} en {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Gegevens ruimteschema gebruiken
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Gegevens ruimteschema niet meer gebruiken
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Dit Open SQL-schema gebruikt mogelijk gegevens uit uw ruimteschema. Als u geen gegevens meer gebruikt, werken modellen die zijn gebaseerd op de gegevens uit het ruimteschema, mogelijk niet meer.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Niet meer gebruiken
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Deze ruimte wordt gebruikt voor toegang tot het data lake
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Data lake ingeschakeld
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Geheugenlimiet bereikt
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Geheugenlimiet bereikt
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Minimale geheugenlimiet bereikt
#XFLD: Space ram tag
ramLimitReachedLabel=Geheugenlimiet bereikt
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Minimale geheugenlimiet bereikt
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=U hebt de limiet van {0} voor het toegewezen ruimtegeheugen bereikt. Wijs meer geheugen toe aan de ruimte.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Systeemgeheugenlimiet bereikt
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=U hebt de systeemgeheugenlimiet van {0} bereikt. U kunt momenteel niet meer geheugen toewijzen aan de ruimte.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Als u dit Open SQL-schema verwijdert, worden ook alle opgeslagen objecten en onderhouden koppelingen in het schema permanent verwijderd. Doorgaan?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Schema is verwijderd.
#YMSE: Error while deleting schema.
schemaDeleteError=Het schema is niet verwijderd.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Schema is bijgewerkt.
#YMSE: Error while updating schema.
schemaUpdateError=Het schema is niet bijgewerkt.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=We hebben een wachtwoord opgegeven voor dit schema. Als u het wachtwoord bent vergeten of bent kwijtgeraakt, kunt u een nieuw wachtwoord aanvragen. Denk eraan om het nieuwe wachtwoord te kopiëren of op te slaan.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Kopieer uw wachtwoord. U hebt het wachtwoord nodig om een verbinding met dit schema tot stand te brengen. Als u het wachtwoord bent vergeten, opent u dit dialoogvenster om het opnieuw in te stellen.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Naam wijzigen niet meer mogel. na aanmaak schema.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Ruimte
#XFLD: HDI Container section header
HDIContainers=HDI-containers
#XTXT: Add HDI Containers button tooltip
addHDIContainers=HDI-containers toevoegen
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=HDI-containers verwijderen
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Toegang inschakelen
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Er zijn geen HDI-containers toegevoegd.
#YMSE: No data text for Timedata section
noDataTimedata=Er zijn geen tijdtabellen en -dimensies gemaakt.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Tijdtabellen en dimensies laden niet mogelijk omdat de runtimedatabase niet beschikbaar is.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=HDI-containers laden niet mogelijk omdat de runtimedatabase niet beschikbaar is.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=De HDI-containers zijn niet opgehaald. Probeer het later opnieuw.
#XFLD Table column header for HDI Container names
HDIContainerName=Naam HDI-container
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Toegang inschakelen
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=U kunt SAP SQL Data Warehousing inschakelen op uw SAP Datasphere-tenant om gegevens tussen uw HDI-containers en uw SAP Datasphere-ruimten uit te wisselen zonder dat daarbij gegevens moeten worden verplaatst.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Maak hiervoor een supportticket aan door op onderstaande knop te klikken.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Als uw ticket is verwerkt, moet u een of meer nieuwe HDI-containers bouwen in de runtimedatabase van SAP Datasphere. De knop "Toegang inschakelen" wordt vervolgens vervangen door de +-knop in de sectie HDI-containers voor al uw SAP Datasphere-ruimten, en u kunt uw containers aan een ruimte toevoegen.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Meer informatie nodig? Ga naar %%0. Zie %%1 voor meer details over wat u moet opnemen in het ticket.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP Note 3057059
#XBUT: Open Ticket Button Text
openTicket=Ticket aanmaken
#XBUT: Add Button Text
add=Toevoegen
#XBUT: Next Button Text
next=Volgende
#XBUT: Edit Button Text
editUsers=Bewerken
#XBUT: create user Button Text
createUser=Maken
#XBUT: Update user Button Text
updateUser=Selecteren
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Niet-toegewezen HDI-containers toevoegen
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Geen niet-toegewezen containers gevonden. \nDe gezochte container is mogelijk al toegewezen aan een ruimte.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Toegewezen HDI-containers zijn niet geladen.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=HDI-containers zijn niet geladen.
#XMSG: Success message
succeededToAddHDIContainer=HDI-container is toegevoegd.
#XMSG: Success message
succeededToAddHDIContainerPlural=HDI-containers zijn toegevoegd.
#XMSG: Success message
succeededToDeleteHDIContainer=HDI-container is verwijderd.
#XMSG: Success message
succeededToDeleteHDIContainerPlural=HDI-containers zijn verwijderd.
#XFLD: Time data section sub headline
timeDataSection=Tijdtabellen en -dimensies
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Lezen
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Wijzigen
#XFLD: Remote sources section sub headline
allconnections=Verbindingstoewijzing
#XFLD: Remote sources section sub headline
localconnections=Lokale verbindingen
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=Lidtoewijzing
#XFLD: User assignment section sub headline
userAssignment=Gebruikerstoewijzing
#XFLD: User section Access dropdown Member
member=Lid
#XFLD: User assignment section column name
user=Gebruikersnaam
#XTXT: Selected role count
selectedRoleToolbarText=Geselecteerd: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Verbindingen
#XTIT: Space detail section data access title
detailsSectionDataAccess=Schematoegang
#XTIT: Space detail section time data title
detailsSectionGenerateData=Tijdgegevens
#XTIT: Space detail section members title
detailsSectionUsers=Leden
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Gebruikers
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Onvoldoende geheugen
#XTIT: Storage distribution
storageDistributionPopoverTitle=Opslag schijfruimte gebruikt
#XTXT: Out of Storage popover text
insufficientStorageText=Als u een nieuwe ruimte wilt maken, verkleint u het toegewezen geheugen van een andere ruimte of verwijdert u een ruimte die niet meer hoeft te worden gebruikt. Ga naar Plan beheren om uw totale systeemgeheugen te vergroten.
#XMSG: Space id length warning
spaceIdLengthWarning=Maximum van {0} tekens overschreden.
#XMSG: Space name length warning
spaceNameLengthWarning=Maximum van {0} tekens overschreden.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Gebruik de {0}-prefix niet om mogelijke conflicten te voorkomen.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Open SQL-schema's zijn niet geladen.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Open SQL-schema is niet gemaakt.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Niet alle remote verbindingen zijn geladen.
#YMSE: Error while loading space details
loadSpaceDetailsError=Ruimtedetails zijn niet geladen.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Ruimte kon niet worden geïmplementeerd.
#YMSE: Error while copying space details
copySpaceDetailsError=Ruimte kon niet worden gekopieerd.
#YMSE: Error while loading storage data
loadStorageDataError=Geheugengegevens zijn niet geladen.
#YMSE: Error while loading all users
loadAllUsersError=Niet alle gebruikers zijn geladen.
#YMSE: Failed to reset password
resetPasswordError=Wachtwoord is niet opnieuw ingesteld.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Nieuw wachtwoord ingesteld voor schema.
#YMSE: DP Agent-name too long
DBAgentNameError=De naam van het middel voor datalevering is te lang.
#YMSE: Schema-name not valid.
schemaNameError=De naam van het schema is ongeldig.
#YMSE: User name not valid.
UserNameError=De gebruikersnaam is ongeldig.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Gebruik op geheugentype
#XTIT: Consumption by Schema
consumptionSchemaText=Gebruik op schema
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Totale tabelgebruik op schema
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Totale gebruik op tabeltype
#XTIT: Tables
tableDetailsText=Tabeldetails
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Gebruik tabelgeheugen
#XFLD: Table Type label
tableTypeLabel=Tabeltype
#XFLD: Schema label
schemaLabel=Schema
#XFLD: reset table tooltip
resetTable=Tabel opnieuw instellen
#XFLD: In-Memory label in space monitor
inMemoryLabel=Geheugen
#XFLD: Disk label in space monitor
diskLabel=Schijf
#XFLD: Yes
yesLabel=Ja
#XFLD: No
noLabel=Nee
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Wilt u dat de gegevens in deze ruimte standaard worden gebruikt?
#XFLD: Business Name
businessNameLabel=Objectnaam
#XFLD: Refresh
refresh=Vernieuwen
#XMSG: No filter results title
noFilterResultsTitle=Er worden geen gegevens weergegeven met de huidige filterinstellingen.
#XMSG: No filter results message
noFilterResultsMsg=Verfijn uw filterinstellingen. Als u daarna nog steeds geen gegevens ziet, maakt u een paar tabellen in de Data Builder. Als er geheugen in deze tabellen wordt gebruikt, kunt u de gegevens bewaken.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=De runtimedatabase is niet beschikbaar.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Omdat de runtimedatabase niet beschikbaar is, zijn bepaalde functies uitgeschakeld en kunnen we op deze pagina geen informatie weergeven.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Gebruiker ruimteschema is niet gemaakt.
#YMSE: Error User name already exists
userAlreadyExistsError=Gebruikersnaam bestaat al.
#YMSE: Error Authentication failed
authenticationFailedError=Verificatie is mislukt.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Te veel mislukte aanmeldpogingen: de gebruiker is geblokkeerd. Vraag een nieuw wachtwoord aan om de gebruiker te deblokkeren.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Nieuw wachtwoord ingesteld en gebruiker gedeblokkeerd.
#XMSG: user is locked message
userLockedMessage=Gebruiker is geblokkeerd.
#XCOL: Users table-view column Role
spaceRole=Rol
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Rol in scope
#XCOL: Users table-view column Space Admin
spaceAdmin=Space Administrator
#XFLD: User section dropdown value Viewer
viewer=Viewer
#XFLD: User section dropdown value Modeler
modeler=Modeler
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Data Integrator
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Space Administrator
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Rol in ruimte is bijgewerkt
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Rol in ruimte is niet bijgewerkt.
#XFLD:
databaseUserNameSuffix=Suffix databasegebruikersnaam
#XTXT: Space Schema password text
spaceSchemaPasswordText=U kunt een verbinding met dit schema tot stand brengen door uw wachtwoord te kopiëren. Als u het wachtwoord bent vergeten, kunt u altijd een nieuw wachtwoord aanvragen.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=SAP Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Om toegang via deze gebruiker in te stellen, schakelt u gebruik in en kopieert u de aanmeldgegevens. Als u de aanmeldgegevens alleen zonder wachtwoord kunt kopiëren, voegt u het wachtwoord later toe.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Gebruik in SAP Cloud Platform inschakelen
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Aanmeldgegevens voor door gebruiker geleverde service:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Aanmeldgegevens voor door gebruiker geleverde service (zonder wachtwoord):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Aanmeldgegevens zonder wachtwoord kopiëren
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Volledige aanmeldgegevens kopiëren
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Wachtwoord kopiëren
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Aanmeldgegevens gekopieerd naar klembord
#XMSG: Password copied to clipboard
passwordCopiedMessage=Wachtwoord gekopieerd naar klembord
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Databasegebruiker maken
#XMSG: Database Users section title
databaseUsers=Databasegebruikers
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Details databasegebruikers
#XFLD: database user read audit log
databaseUserAuditLogRead=Auditlogs inschakelen voor leesbewerkingen en logs bewaren gedurende
#XFLD: database user change audit log
databaseUserAuditLogChange=Auditlogs inschakelen voor wijzigingsbewerkingen en logs bewaren gedurende
#XMSG: Cloud Platform Access
cloudPlatformAccess=Toegang tot SAP Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Configureer de toegang tot uw HANA Deployment Infrastructure-container (HDI-container) via deze databasegebruiker. U kunt alleen verbinding maken met uw HDI-container als SQL-modellering is ingeschakeld.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=HDI-gebruik inschakelen
#XFLD: Enable Consumption hint
enableConsumptionHint=Wilt u dat de gegevens in uw ruimte door andere tools of apps kunnen worden gebruikt?
#XFLD: Enable Consumption
enableConsumption=SQL-gebruik inschakelen
#XFLD: Enable Modeling
enableModeling=SQL-modellering inschakelen
#XMSG: Privileges for Data Modeling
privilegesModeling=Gegevensopname
#XMSG: Privileges for Data Consumption
privilegesConsumption=Gegevensgebruik voor externe tools
#XFLD: SQL Modeling
sqlModeling=SQL-modellering
#XFLD: SQL Consumption
sqlConsumption=SQL-gebruik
#XFLD: enabled
enabled=Ingeschakeld
#XFLD: disabled
disabled=Uitgeschakeld
#XFLD: Edit Privileges
editPrivileges=Rechten bewerken
#XFLD: Open Database Explorer
openDBX=Database Explorer openen
#XFLD: create database user hint
databaseCreateHint=Houd er rekening mee dat de gebruikersnaam niet meer kan worden gewijzigd nadat deze is opgeslagen.
#XFLD: Internal Schema Name
internalSchemaName=Naam intern schema
#YMSE: Failed to load database users
loadDatabaseUserError=Databasegebruikers zijn niet geladen
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Databasegebruikers kunnen niet worden verwijderd
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Databasegebruiker verwijderd
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Databasegebruikers verwijderd
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Databasegebruiker gemaakt
#YMSE: Failed to create database user
createDatabaseUserError=Databasegebruiker kan niet worden gemaakt
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Databasegebruiker geactualiseerd
#YMSE: Failed to update database user
updateDatabaseUserError=Databasegebruiker kan niet worden geactualiseerd
#XFLD: HDI Consumption
hdiConsumption=HDI-gebruik
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Databasetoegang
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Maak de gegevens in deze ruimte standaard geschikt voor gebruik. De modellen in de builders staan automatisch toe dat de gegevens kunnen worden gebruikt.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Standaardgebruik van gegevens in ruimte:
#XFLD: Database User Name
databaseUserName=Databasegebruikersnaam
#XMSG: Database User creation validation error message
databaseUserValidationError=Sommige velden zijn ongeldig. Controleer de verplichte velden om door te gaan.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Gegevensopname kan niet worden ingeschakeld, omdat deze gebruiker is gemigreerd.
#XBUT: Remove Button Text
remove=Verwijderen
#XBUT: Remove Spaces Button Text
removeSpaces=Ruimten verwijderen
#XBUT: Remove Objects Button Text
removeObjects=Objecten verwijderen
#XMSG: No members have been added yet.
noMembersAssigned=Er zijn nog geen leden toegevoegd.
#XMSG: No users have been added yet.
noUsersAssigned=Er zijn nog geen gebruikers toegevoegd.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Er zijn geen databasegebruikers gemaakt of uw filter geeft geen gegevens weer.
#XMSG: Please enter a user name.
noDatabaseUsername=Voer een gebruikersnaam in.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=De gebruikersnaam is te lang. Gebruik een kortere naam.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Er zijn geen bevoegdheden ingeschakeld en deze databasegebruiker zal over beperkte funtionaliteit beschikken. Doorgaan?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Als u auditlogs wilt inschakelen voor wijzigingsbewerkingen moet gegevensopname ook worden ingeschakeld. Wilt u dit doen?
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Als u auditlogs wilt inschakelen voor leesbewerkingen moet gegevensopname ook worden ingeschakeld. Wilt u dit doen?
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Als u HDI-gebruik wilt inschakelen moet gegevensopname en gegevensgebruik ook worden ingeschakeld. Wilt u dit doen?
#XMSG:
databaseUserPasswordText=U kunt een verbinding met deze databasegebruiker tot stand brengen door uw wachtwoord te kopiëren. Als u het wachtwoord bent vergeten, kunt u altijd een nieuw wachtwoord aanvragen.
#XTIT: Space detail section members title
detailsSectionMembers=Leden
#XMSG: New password set
newPasswordSet=Nieuw wachtwoord ingesteld
#XFLD: Data Ingestion
dataIngestion=Gegevensopname
#XFLD: Data Consumption
dataConsumption=Gegevensgebruik
#XFLD: Privileges
privileges=Rechten
#XFLD: Enable Data ingestion
enableDataIngestion=Gegevensopname inschakelen
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Log de lees- en wijzigingsbewerkingen voor gegevensopname.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Maak de gegevens in uw ruimte beschikbaar in uw HDI-containers.
#XFLD: Enable Data consumption
enableDataConsumption=Gegevensgebruik inschakelen
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Sta toe dat andere apps of tools gebruikmaken van de gegevens in uw ruimte.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Om toegang via deze databasegebruiker in te stellen, kopieert u de aanmeldgegevens naar de door uw gebruiker geleverde service. Als u de aanmeldgegevens alleen zonder wachtwoord kunt kopiëren, voegt u het wachtwoord later toe.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Runtimecapaciteit gegevensstroom ({0}:{1} uur van {2} uur)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Runtimecapaciteit van gegevensstroom is niet geladen
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Gebruiker kan gegevensverbruik aan andere gebruikers toekennen.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Gegevensverbruik met toekenningsoptie inschakelen
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Om gegevensverbruik met toekenningsoptie in te schakelen, moet gegevensverbruik worden ingeschakeld. Wilt u beide opties inschakelen?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Automated Predictive Library (APL) en Predictive Analysis Library (PAL) inschakelen
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Gebruiker kan in SAP HANA Cloud ingesloten functies voor automatisch leren gebruiken.
#XFLD: Password Policy
passwordPolicy=Wachtwoordrichtlijn
#XMSG: Password Policy
passwordPolicyHint=Schakel de geconfigureerde wachtwoordrichtlijn hier in of uit.
#XFLD: Enable Password Policy
enablePasswordPolicy=Wachtwoordrichtlijn inschakelen
#XMSG: Read Access to the Space Schema
readAccessTitle=Leestoegang tot ruimteschema
#XMSG: read access hint
readAccessHint=Sta de databasegebruiker toe om externe tools te verbinden met het ruimteschema en views te lezen die zijn weergegeven voor gebruik.
#XFLD: Space Schema
spaceSchema=Ruimteschema
#XFLD: Enable Read Access (SQL)
enableReadAccess=Leestoegang inschakelen (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Sta de gebruiker toe om leestoegang toe te kennen aan andere gebruikers.
#XFLD: With Grant Option
withGrantOption=Met toekenningsoptie
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Maak uw ruimtegegevens beschikbaar in uw HDI-containers.
#XFLD: Enable HDI Consumption
enableHDIConsumption=HDI-gebruik inschakelen
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Schrijftoegang tot Open SQL-schema van gebruiker
#XMSG: write access hint
writeAccessHint=Sta de databasegebruiker toe om externe tools te verbinden met het Open SQL-schema van de gebruiker om gegevensentiteiten te maken en gegevens op te nemen voor gebruik in de ruimte.
#XFLD: Open SQL Schema
openSQLSchema=Open SQL-schema
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Schrijftoegang inschakelen (SQL, DDL, & DML)
#XMSG: audit hint
auditHint=Log de lees- en wijzigingsbewerkingen in het Open SQL-schema.
#XMSG: data consumption hint
dataConsumptionHint=Geef alle nieuwe views in de ruimte standaard voor gebruik weer. Modelers kunnen deze instelling overschrijven voor individuele views via de schakeloptie "Weergeven voor gebruik" in het zijpaneel van de viewuitvoer. U kunt ook de opmaak kiezen waarin de views worden weergegeven.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Standaard weergeven voor gebruik
#XMSG: database users hint consumption hint
databaseUsersHint2New=Maak databasegebruikers om externe tools te verbinden met SAP Datasphere. Stel bevoegdheden in om gebruikers toe te staan om ruimtegegevens te lezen en gegevensentiteiten (DDL) te maken en gegevens (DML) op te nemen voor gebruik in de ruimte.
#XFLD: Read
read=Lezen
#XFLD: Read (HDI)
readHDI=Lezen (HDI)
#XFLD: Write
write=Schrijven
#XMSG: HDI Containers Hint
HDIContainersHint2=Toegang inschakelen tot uw containers van SAP HANA Deployment Infrastructure (HDI) in uw ruimte. Modelers kunnen HDI-artefacten gebruiken als bronnen voor views en HDI-clients hebben toegang tot uw ruimtegegevens.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Open het informatiedialoogvenster
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=De databasegebruiker is geblokkeerd. Open het dialoogvenster om deze te deblokkeren.
#XFLD: Table
table=Tabel
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Partnerverbinding
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Partnerverbindingsconfiguratie
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Definieer uw eigen partnerverbindingstegel door uw iFrame-URL en -pictogram toe te voegen. Deze configuratie is alleen beschikbaar voor deze tenant.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Tegelnaam
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=iFrame-URL
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Herkomst iFrame-berichtplaatsing
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Pictogram
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Er is/zijn geen partnerverbindingsconfiguratie(s) gevonden.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Partnerverbindingsconfiguraties kunnen niet worden weergegeven als de runtimedatabase niet beschikbaar is.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Partnerverbindingsconfiguratie maken
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Pictogram uploaden
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Selecteren (maximale grootte 200 kB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Voorbeeld partnertegel
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.voorbeeld.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.voorbeeld.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Bladeren
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Partnerverbindingsconfiguratie is gemaakt.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Er is een fout opgetreden bij het verwijderen van de partnerverbindingsconfiguratie(s).
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Partnerverbindingsconfiguratie is verwijderd.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Er is een fout opgetreden bij het ophalen van de partnerverbindingsconfiguraties.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Het bestand is niet geüpload: het bestand overschrijdt de maximale grootte van 200 kB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Partnerverbindingsconfiguratie maken
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Partnerverbindingsconfiguratie verwijderen
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Partnertegel kon niet worden gemaakt.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Partnertegel kon niet worden verwijderd.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Het opnieuw instellen van SAP HANA-cloudconnectorinstellingen is mislukt
#XFLD: Workload Class
workloadClass=Werkbelastingklasse
#XFLD: Workload Management
workloadManagement=Werkbelastingsbeheer
#XFLD: Priority
workloadClassPriority=Prioriteit
#XMSG:
workloadManagementPriorityHint=U kunt de prioritering van deze ruimte bij het uitvoeren van query's in de database opgeven. Voer een waarde in tussen 1 (laagste prioriteit) en 8 (hoogste prioriteit). In een situatie waarin meerdere ruimten kunnen worden gebruikt voor beschikbare threads, worden de ruimten met hogere prioriteit uitgevoerd vóór ruimten met lagere prioriteit.
#XMSG:
workloadClassPriorityHint=U kunt een prioriteit van de ruimte opgeven van 0 (laagste prioriteit) tot 8 (hoogste prioriteit). Statements van een ruimte met een hoge prioriteit worden uitgevoerd vóór statements van andere ruimten met een lagere prioriteit. De standaardprioriteit is 5. Omdat de waarde 9 is gereserveerd voor systeembewerkingen, is deze waarde niet beschikbaar voor een ruimte.
#XFLD: Statement Limits
workloadclassStatementLimits=Statementlimieten
#XFLD: Workload Configuration
workloadConfiguration=Werkbelastingsconfiguratie
#XMSG:
workloadClassStatementLimitsHint=U kunt het maximumaantal (of percentage) threads en GB's geheugen opgeven die statements die gelijktijdig in de ruimte worden uitgevoerd, kunnen gebruiken. U kunt een waarde of percentage opgeven tussen 0 (geen limiet) en het totale geheugen en het totaalaantal threads die in de tenant beschikbaar zijn. \n\n Als u een threadlimiet opgeeft, kan dit prestatieverlies veroorzaken. \n\n Als u een geheugenlimiet opgeeft, worden de statements die de geheugenlimiet bereiken, niet uitgevoerd.
#XMSG:
workloadClassStatementLimitsDescription=De standaardconfiguratie biedt ruime resourcelimieten, maar zorgt er tevens voor dat een enkele ruimte het systeem niet kan overbelasten.
#XMSG:
workloadClassStatementLimitCustomDescription=U kunt een maximum aantal threads en geheugenlimieten instellen voor statements die gelijktijdig in de ruimte worden uitgevoerd.
#XMSG:
totalStatementThreadLimitHelpText=Als u de threadlimiet te laag instelt, kan dit gevolgen hebben voor de statementprestaties. Veel te hoge waarden of waarde 0 zorgen er mogelijk voor dat de ruimte alle beschikbare systeemthreads gebruikt.
#XMSG:
totalStatementMemoryLimitHelpText=Als u de geheugenlimiet te laag instelt, kan dit leiden tot fouten vanwege onvoldoende geheugen. Veel te hoge waarden of waarde 0 zorgen er mogelijk voor dat de ruimte het gehele beschikbare systeemgeheugen gebruikt.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Voer een percentage tussen 1% en 70% (of een gelijkwaardig aantal) van het totaalaantal in uw tenant beschikbare threads in. Als u de threadlimiet te laag instelt, kan dit gevolgen hebben voor de statementprestaties, terwijl veel te hoge waarden mogelijk gevolgen heeft voor statementprestaties in andere ruimten.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Voer een percentage tussen 1% en {0}% (of een gelijkwaardig aantal) van het totaalaantal in uw tenant beschikbare threads in. Als u de threadlimiet te laag instelt, kan dit gevolgen hebben voor de statementprestaties, terwijl veel te hoge waarden mogelijk gevolgen heeft voor statementprestaties in andere ruimten.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Voer een waarde of percentage tussen 0 (geen limiet) en de totale hoeveelheid in uw tenant beschikbare geheugen in. Als u de geheugenlimiet te laag instelt, kan dit gevolgen hebben voor de statementprestaties, terwijl veel te hoge waarden mogelijk gevolgen hebben voor statementprestaties in andere ruimten.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Totale threadlimiet voor statement
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Threads
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Totale geheugenlimiet voor statement
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Er is geen klantinfo over SAP HANA geladen
#XMSG:
minimumLimitReached=Minimale limiet bereikt.
#XMSG:
maximumLimitReached=Maximale limiet bereikt.
#XMSG: Name Taken for Technical Name
technical-name-taken=Er bestaat al een verbinding met de technische naam die u hebt ingevoerd. Voer een andere naam in.
#XMSG: Name Too long for Technical Name
technical-name-too-long=De technische naam die u hebt ingevoerd, overschrijdt de limiet van 40 tekens. Voer een naam met minder tekens in.
#XMSG: Technical name field empty
technical-name-field-empty=Voer een technische naam in.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=U kunt alleen letters (a-z), cijfers (0-9) en onderstrepingstekens (_) voor de naam gebruiken.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=De naam die u invoert, mag niet beginnen of eindigen met een onderstrepingsteken (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Statementlimieten inschakelen
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Instellingen
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Voor het maken of bewerken van verbindingen, moet u de app Verbindingen openen of hier klikken:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Naar Verbindingen
#XFLD: Not deployed label on space tile
notDeployedLabel=Ruimte is nog niet geïmplementeerd.
#XFLD: Not deployed additional text on space tile
notDeployedText=Implementeer de ruimte.
#XFLD: Corrupt space label on space tile
corruptSpace=Er is iets mis gegaan.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Probeer opnieuw te implementeren of neem contact op met ondersteuning
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Auditloggegevens
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Beheergegevens
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Andere gegevens
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Gegevens in ruimten
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Wilt u de ruimte echt deblokkeren?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Wilt u de ruimte echt blokkeren?
#XFLD: Lock
lock=Blokkeren
#XFLD: Unlock
unlock=Deblokkeren
#XFLD: Locking
locking=Blokkering
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Ruimte geblokkeerd
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Ruimte gedeblokkeerd
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Ruimten geblokkeerd
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Ruimten gedeblokkeerd
#YMSE: Error while locking a space
lockSpaceError=De ruimte kan niet worden geblokkeerd.
#YMSE: Error while unlocking a space
unlockSpaceError=De ruimte kan niet worden gedeblokkeerd.
#XTIT: popup title Warning
confirmationWarningTitle=Waarschuwing
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=De ruimte is handmatig geblokkeerd.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=De ruimte is door het systeem geblokkeerd omdat auditlogs een grote hoeveelheid GB's van vaste schijf gebruiken.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=De ruimte is geblokkeerd door het systeem omdat het de toewijzingen van geheugen of schijfruimte overschrijdt.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Wilt u de geselecteerde ruimten echt deblokkeren?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Wilt u de geselecteerde ruimten echt blokkeren?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor van rollen in scope
#XTIT: ECN Management title
ecnManagementTitle=Beheer van ruimte en knooppunten elastische berekening
#XFLD: ECNs
ecns=Knooppunten elastische berekening
#XFLD: ECN phase Ready
ecnReady=Gereed
#XFLD: ECN phase Running
ecnRunning=Wordt uitgevoerd
#XFLD: ECN phase Initial
ecnInitial=Niet gereed
#XFLD: ECN phase Starting
ecnStarting=Wordt gestart
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Starten is mislukt
#XFLD: ECN phase Stopping
ecnStopping=Wordt gestopt
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Stoppen is mislukt
#XBTN: Assign Button
assign=Ruimten toewijzen
#XBTN: Start Header-Button
start=Starten
#XBTN: Update Header-Button
repair=Bijwerken
#XBTN: Stop Header-Button
stop=Stoppen
#XFLD: ECN hours remaining
ecnHoursRemaining=1000 uur resterend
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining={0} blokuren resterend
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining={0} blokuur resterend
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Knooppunt elastische berekening maken
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Knooppunt elastische berekening bewerken
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Knooppunt elastische berekening verwijderen
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Ruimten toewijzen
#XFLD: ECN ID
ECNIDLabel=Knooppunt elastische berekening
#XTXT: Selected toolbar text
selectedToolbarText=Geselecteerd: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Knooppunten elastische berekening
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Aantal objecten
#XTIT: Object assignment - Dialog header text
selectObjects=Selecteer de ruimten en objecten die u aan uw knooppunt elastische berekening wilt toewijzen:
#XTIT: Object assignment - Table header title: Objects
objects=Objecten
#XTIT: Object assignment - Table header: Type
type=Type
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Let op: het verwijderen van een databasegebruiker resulteert in de verwijdering van alle gegenereerde auditloggegevens. Als u de auditlogs wilt behouden, moet u deze exporteren voordat u de databasegebruiker verwijdert.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Let op: het ongedaan maken van een toewijzing van een HDI-container van de ruimte resulteert in de verwijdering van alle gegenereerde auditloggegevens. Als u de auditlogs wilt behouden, moet u deze exporteren voordat u de toewijzing van de HDI-container ongedaan maakt.
#XTXT: All audit logs
allAuditLogs=Alle voor de ruimte gegenereerde auditloggegevens
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Let op: het uitschakelen van een auditrichtlijn (lees- of wijzigingsbewerkingen) resulteert in de verwijdering van alle auditloggegevens. Als u de auditloggegevens wilt behouden, moet u deze exporteren voordat u de auditrichtlijn uitschakelt.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Er zijn nog geen ruimten of objecten toegewezen
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Wijs een ruimte of objecten aan uw knooppunt elastische berekening toe om ermee aan de slag te kunnen gaan.
#XTIT: No Spaces Illustration title
noSpacesTitle=Er is nog geen ruimte gemaakt
#XTIT: No Spaces Illustration description
noSpacesDescription=Maak een ruimte om aan de slag te gaan met het verwerven van gegevens.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=De prullenbak is leeg.
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Hier kunt u uw verwijderde ruimten terugvinden.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Zodra de ruimte is geïmplementeerd, worden de volgende databasegebruikers {0} verwijderd en kunnen deze niet meer worden hersteld:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Databasegebruikers verwijderen
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID bestaat al.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Gebruik alleen kleine letters a - x en nummer 0 - 9.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID moet minimaal {0} tekens lang zijn.
#XMSG: ecn id length warning
ecnIdLengthWarning=Maximum van {0} tekens overschreden.
#XFLD: open System Monitor
systemMonitor=Systeemmonitor
#XFLD: open ECN schedule dialog menu entry
schedule=Plannen
#XFLD: open create ECN schedule dialog
createSchedule=Planning maken
#XFLD: open change ECN schedule dialog
changeSchedule=Planning bewerken
#XFLD: open delete ECN schedule dialog
deleteSchedule=Planning verwijderen
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Planning aan mij toewijzen
#XFLD: open pause ECN schedule dialog
pauseSchedule=Planning pauzeren
#XFLD: open resume ECN schedule dialog
resumeSchedule=Planning hervatten
#XFLD: View Logs
viewLogs=Logs weergeven
#XFLD: Compute Blocks
computeBlocks=Berekeningsblokken
#XFLD: Memory label in ECN creation dialog
ecnMemory=Geheugen (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Opslag (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Aantal CPU's
#XFLD: ECN updated by label
changedBy=Gewijzigd door
#XFLD: ECN updated on label
changedOn=Gewijzigd op
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Knooppunt elastische berekening gemaakt
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Knooppunt elastische berekening kon niet worden gemaakt
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Knooppunt elastische berekening bijgewerkt
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Knooppunt elastische berekening kon niet worden bijgewerkt
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Knooppunt elastische berekening verwijderd
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Knooppunt elastische berekening kon niet worden verwijderd
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Knooppunt elastische berekening gestart
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Knooppunt elastische berekening gestopt
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Knooppunt elastische berekening kon niet starten
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Knooppunt elastische berekening kon niet stoppen
#XBUT: Add Object button for an ECN
assignObjects=Objecten toevoegen
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Alle objecten automatisch toevoegen
#XFLD: object type label to be assigned
objectTypeLabel=Type (semantisch gebruik)
#XFLD: assigned object type label
assignedObjectTypeLabel=Type
#XFLD: technical name label
TechnicalNameLabel=Technische naam
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Selecteer de objecten die u wilt toevoegen aan het knooppunt van de elastische berekening
#XTIT: Add objects dialog title
assignObjectsTitle=Objecten toewijzen van
#XFLD: object label with object count
objectLabel=Object
#XMSG: No objects available to add message.
noObjectsToAssign=Er zijn geen toe te wijzen objecten.
#XMSG: No objects assigned message.
noAssignedObjects=Er zijn geen objecten toegewezen.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Waarschuwing
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Verwijderen
#XMSG: Remove objects popup text
removeObjectsConfirmation=Weet u zeker dat u de geselecteerde objecten wilt verwijderen?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Weet u zeker dat u de geselecteerde ruimten wilt verwijderen?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Ruimten verwijderen
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Weergegeven objecten zijn verwijderd.
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Weergegeven objecten zijn toegewezen.
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Alle weergegeven objecten
#XFLD: Spaces tab label
spacesTabLabel=Ruimten
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Weergegeven objecten
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Ruimten zijn verwijderd
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Ruimte is verwijderd
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Ruimten kunnen niet worden toegewezen of verwijderd.
#YMSE: Error while removing objects
removeObjectsError=Objecten toewijzen of verwijderen niet mogelijk.
#YMSE: Error while removing object
removeObjectError=Object toewijzen of verwijderen niet mogelijk.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Het eerder geselecteerde nummer is niet meer geldig. Selecteer een geldig nummer.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Selecteer een geldige performanceklasse.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=De eerder geselecteerde prestatieklasse "{0}" is momenteel niet geldig. Selecteer geldige prestatieklasse.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Weet u zeker dat u het knooppunt elastische berekening wilt verwijderen?
#XFLD: tooltip for ? button
help=Help
#XFLD: ECN edit button label
editECN=Configureren
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Entiteit - relatiemodel
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Lokale tabel
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Remote tabel
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analytisch model
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Taakketen
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Gegevensstroom
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Replicatiestroom
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Transformatiestroom
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Intelligente zoekactie
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Repository
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Zoekactie voor onderneming
#XFLD: Technical type label for View
DWC_VIEW=View
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Gegevensproduct
#XFLD: Technical type label for Data Access Control
DWC_DAC=Gegevenstoegangscontrole
#XFLD: Technical type label for Folder
DWC_FOLDER=Map
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Businessentiteit
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Businessentiteitvariant
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Verantwoordelijkheidsscenario
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Feitenmodel
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspectief
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Gebruiksmodel
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Externe verbinding
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Variant feitenmodel
#XMSG: Schedule created alert message
createScheduleSuccess=Planning gemaakt
#XMSG: Schedule updated alert message
updateScheduleSuccess=Planning bijgewerkt
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Planning verwijderd
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Planning is aan u toegewezen
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Pauzeren van 1 planning
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Hervatten van 1 planning
#XFLD: Segmented button label
availableSpacesButton=Beschikbaar
#XFLD: Segmented button label
selectedSpacesButton=Geselecteerd
#XFLD: Visit website button text
visitWebsite=Website bezoeken
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=De voorheen geselecteerde brontaal wordt verwijderd.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Inschakelen
#XFLD: ECN performance class label
performanceClassLabel=Performanceklasse
#XTXT performance class memory text
memoryText=Geheugen
#XTXT performance class compute text
computeText=Berekening
#XTXT performance class high-compute text
highComputeText=High-Compute
#XBUT: Recycle Bin Button Text
recycleBin=Prullenbak
#XBUT: Restore Button Text
restore=Herstellen
#XMSG: Warning message for new Workload Management UI
priorityWarning=Dit gebied is alleen-lezen. U kunt de ruimteprioriteit wijzigen in het gebied Systeem / Configuratie / Werkbelasting.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Dit gebied is alleen-lezen. U kunt de werkbelastingprioriteit wijzigen in het gebied Systeem / Configuratie / Werkbelasting.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark-vCPU's
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark-geheugen (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Gegevensproductopname
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Geen gegevens beschikbaar omdat ruimte momenteel wordt geïmplementeerd
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Geen gegevens beschikbaar omdat ruimte momenteel wordt geladen
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Instancetoewijzingen bewerken
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
