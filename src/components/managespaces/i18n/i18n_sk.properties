#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=Monitorovanie
#XTXT: Type name for spaces in browser tab page title
space=Priestor
#_____________________________________
#XFLD: Spaces label in
spaces=Priestory
#XFLD: Manage plan button text
manageQuotaButtonText=Spravovať plán
#XBUT: Manage resources button
manageResourcesButton=Správa zdrojov
#XFLD: Create space button tooltip
createSpace=Vytvoriť priestor
#XFLD: Create
create=Vytvoriť
#XFLD: Deploy
deploy=Nasadiť
#XFLD: Page
page=Strana
#XFLD: Cancel
cancel=Zrušiť
#XFLD: Update
update=Aktualizovať
#XFLD: Save
save=Uložiť
#XFLD: OK
ok=OK
#XFLD: days
days=Dni
#XFLD: Space tile edit button label
edit=Upraviť
#XFLD: Auto Assign all objects to space
autoAssign=Automatické priradenie
#XFLD: Space tile open monitoring button label
openMonitoring=Monitorovať
#XFLD: Delete
delete=Odstrániť
#XFLD: Copy Space
copy=Kopírovať
#XFLD: Close
close=Zavrieť
#XCOL: Space table-view column status
status=Status
#XFLD: Space status active
activeLabel=Aktívne
#XFLD: Space status locked
lockedLabel=Blokované
#XFLD: Space status critical
criticalLabel=Kritické
#XFLD: Space status cold
coldLabel=Studené
#XFLD: Space status deleted
deletedLabel=Odstránené
#XFLD: Space status unknown
unknownLabel=Neznáme
#XFLD: Space status ok
okLabel=OK
#XFLD: Database user expired
expired=Exspirované
#XFLD: deployed
deployed=Nasadené
#XFLD: not deployed
notDeployed=Nenasadené
#XFLD: changes to deploy
changesToDeploy=Zmeny na nasadenie
#XFLD: pending
pending=Nasadzuje sa
#XFLD: designtime error
designtimeError=Chyba času návrhu
#XFLD: runtime error
runtimeError=Chyba doby chodu
#XFLD: Space created by label
createdBy=Vytvoril
#XFLD: Space created on label
createdOn=Vytvorené dňa
#XFLD: Space deployed on label
deployedOn=Nasadené dňa
#XFLD: Space ID label
spaceID=ID priestoru
#XFLD: Priority label
priority=Priorita
#XFLD: Space Priority label
spacePriority=Priorita priestoru
#XFLD: Space Configuration label
spaceConfiguration=Konfigurácia priestoru
#XFLD: Not available
notAvailable=Nedostupné
#XFLD: WorkloadType default
default=Predvolené
#XFLD: WorkloadType custom
custom=Vlastné
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=Prístup do dátového jazera
#XFLD: Translation label
translationLabel=Preklad
#XFLD: Source language label
sourceLanguageLabel=Zdrojový jazyk
#XFLD: Translation CheckBox label
translationCheckBox=Aktivovať preklad
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=Nasaďte priestor na získanie prístupu k detailom používateľa.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=Nasaďte priestor na otvorenie prieskumníka databáz.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=Tento priestor nemôžete používať na prístup k dátovému jazeru, pretože sa ho už používa iný priestor.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=Pomocou tohto priestoru získate prístup k dátovému jazeru.
#XFLD: Space Priority minimum label extension
low=Nízke
#XFLD: Space Priority maximum label extension
high=Vysoké
#XFLD: Space name label
spaceName=Názov priestoru
#XFLD: Enable deploy objects checkbox
enableDeployObjects=Nasadiť objekty
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=Kopírovať {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(nevybrané)
#XTXT Human readable text for language code "af"
af=afrikánčina
#XTXT Human readable text for language code "ar"
ar=arabčina
#XTXT Human readable text for language code "bg"
bg=bulharčina
#XTXT Human readable text for language code "ca"
ca=katalánčina
#XTXT Human readable text for language code "zh"
zh=čínština (zjednodušená)
#XTXT Human readable text for language code "zf"
zf=čínština
#XTXT Human readable text for language code "hr"
hr=chorvátčina
#XTXT Human readable text for language code "cs"
cs=čeština
#XTXT Human readable text for language code "cy"
cy=waleština
#XTXT Human readable text for language code "da"
da=dánčina
#XTXT Human readable text for language code "nl"
nl=holandčina
#XTXT Human readable text for language code "en-UK"
en-UK=Angličtina (Spojené kráľovstvo)
#XTXT Human readable text for language code "en"
en=Angličtina (Spojené štáty americké)
#XTXT Human readable text for language code "et"
et=Estónčina
#XTXT Human readable text for language code "fa"
fa=Perzština
#XTXT Human readable text for language code "fi"
fi=fínčina
#XTXT Human readable text for language code "fr-CA"
fr-CA=Francúzština (Kanada)
#XTXT Human readable text for language code "fr"
fr=francúzština
#XTXT Human readable text for language code "de"
de=Nemčina
#XTXT Human readable text for language code "el"
el=Gréčtina
#XTXT Human readable text for language code "he"
he=Hebrejčina
#XTXT Human readable text for language code "hi"
hi=hindčina
#XTXT Human readable text for language code "hu"
hu=maďarčina
#XTXT Human readable text for language code "is"
is=Islandčina
#XTXT Human readable text for language code "id"
id=Bahasa Indonesia
#XTXT Human readable text for language code "it"
it=taliančina
#XTXT Human readable text for language code "ja"
ja=Japončina
#XTXT Human readable text for language code "kk"
kk=Kazaština
#XTXT Human readable text for language code "ko"
ko=Kórejčina
#XTXT Human readable text for language code "lv"
lv=Lotyština
#XTXT Human readable text for language code "lt"
lt=litovčina
#XTXT Human readable text for language code "ms"
ms=Bahasa Melayu
#XTXT Human readable text for language code "no"
no=nórčina
#XTXT Human readable text for language code "pl"
pl=poľština
#XTXT Human readable text for language code "pt"
pt=Portugalčina (Brazília)
#XTXT Human readable text for language code "pt-PT"
pt-PT=Portugalčina (Portugalsko)
#XTXT Human readable text for language code "ro"
ro=rumunčina
#XTXT Human readable text for language code "ru"
ru=ruština
#XTXT Human readable text for language code "sr"
sr=srbčina
#XTXT Human readable text for language code "sh"
sh=Srbochorvátčina
#XTXT Human readable text for language code "sk"
sk=slovenčina
#XTXT Human readable text for language code "sl"
sl=slovinčina
#XTXT Human readable text for language code "es"
es=španielčina
#XTXT Human readable text for language code "es-MX"
es-MX=Španielčina (Mexiko)
#XTXT Human readable text for language code "sv"
sv=švédčina
#XTXT Human readable text for language code "th"
th=Thajčina
#XTXT Human readable text for language code "tr"
tr=turečtina
#XTXT Human readable text for language code "uk"
uk=ukrajinčina
#XTXT Human readable text for language code "vi"
vi=vietnamčina
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=Odstrániť priestory
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=Naozaj chcete presunúť priestor "{0}" do koša?
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=Naozaj chcete presunúť {0} vybraných priestorov do koša?
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=Naozaj chcete odstrániť priestor „{0}“? Túto akciu nie je možné vrátiť späť.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=Naozaj chcete odstrániť vybrané priestory {0}? Túto akciu nie je možné vrátiť späť. Nasledujúci obsah sa {1} odstráni:
#XTXT: permanently
permanently=trvalo
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=Nasledujúci obsah bude {0} odstránený a nie je možné ho obnoviť:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=Zadajte {0} na potvrdenie odstránenia.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=Skontrolujte pravopis a skúste to znova.
#XTXT: All Spaces
allSpaces=Všetky priestory
#XTXT: All data
allData=Všetky objekty a údaje obsiahnuté v priestore
#XTXT: All connections
allConnections=Všetky pripojenia definované v priestore
#XFLD: Space tile selection box tooltip
clickToSelect=Kliknutím vyberte
#XTXT: All database users
allDatabaseUsers=Všetky objekty a údaje obsiahnuté v akejkoľvek schéme Open SQL priradenej k priestoru
#XFLD: remove members button tooltip
deleteUsers=Odstrániť členov
#XTXT: Space long description text
description=Popis (maximálne 4 000 znakov)
#XFLD: Add Members button tooltip
addUsers=Pridať členov
#XFLD: Add Users button tooltip
addUsersTooltip=Pridať používateľov
#XFLD: Edit Users button tooltip
editUsersTooltip=Upraviť používateľov
#XFLD: Remove Users button tooltip
removeUsersTooltip=Odstrániť používateľov
#XFLD: Searchfield placeholder
filter=Hľadať
#XCOL: Users table-view column health
health=Stav
#XCOL: Users table-view column access
access=Prístup
#XFLD: No user found nodatatext
noDataText=Nenájdený žiadny používateľ
#XTIT: Members dialog title
selectUserDialogTitle=Pridať členov
#XTIT: User dialog title
addUserDialogTitle=Pridať používateľov
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=Odstrániť pripojenia
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=Odstrániť pripojenie
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=Naozaj chcete odstrániť vybrané pripojenia? Budú trvalo odstránené.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=Vybrať pripojenia
#XTIT: Share connection dialog title
connectionSharingDialogTitle=Zdieľať pripojenie
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=Zdieľané pripojenia
#XFLD: Add remote source button tooltip
addRemoteConnections=Pridať pripojenia
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=Odstrániť pripojenia
#XFLD: Share remote source button tooltip
shareConnections=Zdieľať pripojenia
#XFLD: Tile-layout tooltip
tileLayout=Rozloženie dlaždíc
#XFLD: Table-layout tooltip
tableLayout=Rozloženie tabuľky
#XMSG: Success message after creating space
createSpaceSuccessMessage=Priestor vytvorený
#XMSG: Success message after copying space
copySpaceSuccessMessage=Kopírovanie priestoru "{0}" do priestoru "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=Nasadenie priestoru bolo spustené
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=Aktualizácia Apache Spark bola spustená
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=Aktualizácia Apache Spark zlyhala
#XMSG: Success message after updating space
updateSettingsSuccessMessage=Detaily priestoru aktualizované
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=Priestor dočasne odblokovaný
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=Priestor odstránený
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=Priestory odstránené
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=Priestor obnovený
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=Priestory obnovené
#YMSE: Error while updating settings
updateSettingsFailureMessage=Nastavenia priestoru nebolo možné aktualizovať.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=Dátové jazero je už priradené k inému priestoru. K dátovému jazeru môže získať prístup vždy len jeden priestor.
#YMSE: Error while updating data lake option
virtualTablesExists=Nemôžete zrušiť priradenie dátového jazera k tomuto priestoru, pretože ešte existujú závislosti k virtuálnym tabuľkám*. Ak chcete zrušiť priradenie dátového jazera k tomuto priestoru, odstráňte virtuálne tabuľky.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=Nastavenia priestoru nebolo možné odblokovať.
#YMSE: Error while creating space
createSpaceError=Nastavenia priestoru nebolo možné vytvoriť.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=Už existuje priestor s názvom {0}.
#YMSE: Error while deleting a single space
deleteSpaceError=Priestor nebolo možné odstrániť.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=Váš priestor „{0}“ už nefunguje správne. Skúste ho znova odstrániť. Ak stále nefunguje, požiadajte správcu o odstránenie priestoru a otvorte tiket.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=Údaje priestoru v súboroch nebolo možné odstrániť.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=Používateľov nebolo možné odstrániť.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=Schémy nebolo možné odstrániť.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=Pripojenia nebolo možné odstrániť.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=Údaje priestoru nebolo možné odstrániť.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=Priestory nebolo možné odstrániť.
#YMSE: Error while restoring a single space
restoreSpaceError=Priestor nebolo možné obnoviť.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=Priestory nebolo možné obnoviť.
#YMSE: Error while creating users
createUsersError=Používateľov nebolo možné pridať.
#YMSE: Error while removing users
removeUsersError=Nepodarilo sa nám odstrániť používateľov.
#YMSE: Error while removing user
removeUserError=Nepodarilo sa odstrániť používateľa.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=Nepodarilo sa nám pridať používateľa do vybratej roly s rozsahom. \n\n Do roly s rozsahom sa nemôžete pridať. Môžete požiadať správcu, aby vás pridal do roly s rozsahom.
#YMSE: Error assigning user to the space
userAssignError=Používateľa sa nám nepodarilo priradiť k priestoru. \n\n Používateľ je už priradený k maximálnemu povolenému počtu (100) priestorov v rámci rolí v rozsahu.
#YMSE: Error assigning users to the space
usersAssignError=Používateľov sa nám nepodarilo priradiť k priestoru. \n\n Používateľ je už priradený k maximálnemu povolenému počtu (100) priestorov v rámci rolí v rozsahu.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=Nepodarilo sa nám načítať používateľov. Skúste to neskôr znova.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=Nepodarilo sa nám načítať roly s rozsahom.
#YMSE: Error while fetching members
fetchUserError=Členov nebolo možné načítať. Skúste to neskôr znovu.
#YMSE: Error while loading run-time database
loadRuntimeError=Nepodarilo sa načítať informácie z databázy doby chodu.
#YMSE: Error while loading spaces
loadSpacesError=Ľutujeme, počas pokusu o načítanie vašich priestorov sa vyskytla chyba.
#YMSE: Error while loading haas resources
loadStorageError=Ľutujeme, počas pokusu o načítanie údajov ukladacieho priestoru sa vyskytla chyba.
#YMSE: Error no data could be loaded
loadDataError=Ľutujeme, počas pokusu o načítanie vašich údajov sa vyskytla chyba.
#XFLD: Click to refresh storage data
clickToRefresh=Kliknite sem a skúste to znova.
#XTIT: Delete space popup title
deleteSpacePopupTitle=Odstrániť priestor
#XCOL: Spaces table-view column name
name=Názov
#XCOL: Spaces table-view deployment status
deploymentStatus=Status nasadenia
#XFLD: Disk label in space details
storageLabel=Disk (GB)
#XFLD: In-Memory label in space details
ramLabel=Pamäť (GB)
#XFLD: Memory label on space card
memory=Pamäť na ukladanie
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=Úložisko priestoru
#XFLD: Storage Type label in space details
storageTypeLabel=Typ ukladacieho priestoru
#XFLD: Enable Space Quota
enableSpaceQuota=Aktivovať kvótu priestoru
#XFLD: No Space Quota
noSpaceQuota=Žiadna kvóta priestoru
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=Databáza SAP HANA (disk a pamäť)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=Súbory SAP HANA Data Lake
#XFLD: Available scoped roles label
availableRoles=Dostupné roly s rozsahom
#XFLD: Selected scoped roles label
selectedRoles=Vybrané roly s rozsahom
#XCOL: Spaces table-view column models
models=Modely
#XCOL: Spaces table-view column users
users=Používatelia
#XCOL: Spaces table-view column connections
connections=Pripojenia
#XFLD: Section header overview in space detail
overview=Prehľad
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=Aplikácie
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=Priradenie úlohy
#XFLD: vCPU label in Apache Spark section
vCPULabel=vCPUs
#XFLD: Memory label in Apache Spark section
memoryLabel=Pamäť (GB)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=Konfigurácia priestoru
#XFLD: Space Source label
sparkApplicationLabel=Aplikácia
#XFLD: Cluster Size label
clusterSizeLabel=Veľkosť klastra
#XFLD: Driver label
driverLabel=Driver
#XFLD: Executor label
executorLabel=Vykonávateľ
#XFLD: max label
maxLabel=Max.použité
#XFLD: TrF Default label
trFDefaultLabel=Predvolený transformačný tok
#XFLD: Merge Default label
mergeDefaultLabel=Zlúčiť predvolené
#XFLD: Optimize Default label
optimizeDefaultLabel=Optimalizácia predvolenej hodnoty
#XFLD: Deployment Default label
deploymentDefaultLabel=Nasadenie lokálnej tabuľky (súboru).
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} CPU / {1} GB
#XFLD: Object type label
taskObjectTypeLabel=Typ objektu
#XFLD: Task activity label
taskActivityLabel=Aktivita
#XFLD: Task Application ID label
taskApplicationIDLabel=Predvolená aplikácia
#XFLD: Section header in space detail
generalSettings=Všeobecné nastavenia
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=Tento priestor je momentálne blokovaný systémom.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=Zmeny tejto sekcie budú okamžite nasadené.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=Upozorňujeme, že zmena hodnôt môže spôsobiť problémy s výkonom.
#XFLD: Button text to unlock the space again
unlockSpace=Odblokovať priestor
#XFLD: Info text for audit log formatted message
auditLogText=Aktivujte protokoly auditu na zaznamenávanie akcií čítania alebo zmeny (zásady auditu). Správcovia potom môžu analyzovať, kto vykonal akú akciu v ktorom čase.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=Protokoly auditu môžu spotrebovať veľké množstvo diskového priestoru vášho klienta. Ak povolíte politiku auditu (akcie čítania alebo zmeny), mali by ste pravidelne monitorovať využitie diskového priestoru (prostredníctvom karty Disk Storage Used v nástroji System Monitor), aby ste sa vyhli úplným výpadkom disku, čo môže viesť k prerušeniu služby. Ak zakážete politiku auditu, všetky jej záznamy denníka auditu sa odstránia. Ak si chcete ponechať záznamy protokolu auditu, zvážte ich exportovanie skôr, ako zakážete politiku auditu.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=Zobraziť nápovedu
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=Tento priestor prekračuje svoje úložisko priestoru a bude blokovaný o {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=hod.
#XMSG: Unit for remaining time until space is locked again
minutes=min.
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=Audítovanie
#XFLD: Subsection header in space detail for auditing
auditing=Nastavenia auditu priestoru
#XFLD: Hot space tooltip
hotSpaceCountTooltip=Kritické priestory: Je použitých viac ako 90% ukladacieho priestoru.
#XFLD: Green space tooltip
greenSpaceCountTooltip=Zdravé priestory: Je použitých od 6 % do 90 % ukladacieho priestoru
#XFLD: Cold space tooltip
coldSpaceCountTooltip=Studené priestory: Je použitých maximálne 5 % ukladacieho priestoru.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=Kritické priestory: Je použitých viac ako 90% ukladacieho priestoru.
#XFLD: Green space tooltip
okSpaceCountTooltip=Zdravé priestory: Je použitých od 6 % do 90 % ukladacieho priestoru
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=Blokované priestory: Blokované z dôvodu nedostatočnej pamäte.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=Blokované priestory
#YMSE: Error while deleting remote source
deleteRemoteError=Pripojenia nebolo možné odstrániť.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=ID priestoru sa neskôr nemôže zmeniť.\nPlatné znaky sú A - Z, 0 - 9, a _
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=Zadajte názov priestoru.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=Zadajte podnikový názov.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=Zadajte ID priesotru.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=Neplatné znaky. Použite len A - Z, 0 - 9, a _.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=ID priestoru už existuje.
#XFLD: Space searchfield placeholder
search=Hľadať
#XMSG: Success message after creating users
createUsersSuccess=Používatelia pridaní
#XMSG: Success message after creating user
createUserSuccess=Používateľ pridaný
#XMSG: Success message after updating users
updateUsersSuccess={0} používateľov aktualizovaných
#XMSG: Success message after updating user
updateUserSuccess=Používateľ aktualizovaný
#XMSG: Success message after removing users
removeUsersSuccess={0} používateľov odstránených
#XMSG: Success message after removing user
removeUserSuccess=Používateľ odstránený
#XFLD: Schema name
schemaName=Názov schémy
#XFLD: used of total
ofTemplate={0} z {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=Priradený disk ({0} z {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=Priradená pamäť ({0} z {1})
#XFLD: Storage ratio on space
accelearationRAM=Zrýchlenie pamäte
#XFLD: No Storage Consumption
noStorageConsumptionText=Nepriradená žiadna kvóta priestoru.
#XFLD: Used disk label in space overview
usedStorageTemplate=Disk používaný na ukladanie ({0} z {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=Pamäť používaná na ukladanie ({0} z {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} z {1} Disk používaný na ukladanie
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate=Použité {0} z {1} pamäte
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate=Priradené {0} z {1} disku
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate=Priradené {0} z {1} pamäte
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=Údaje priestoru: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=Ostatné údaje: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=Zvážte rozšírenie svojho plánu alebo kontaktujte podporu SAP.
#XCOL: Space table-view column used Disk
usedStorage=Disk používaný na ukladanie
#XCOL: Space monitor column used Memory
usedRAM=Pamäť používaná na ukladanie
#XCOL: Space monitor column Schema
tableSchema=Schéma
#XCOL: Space monitor column Storage Type
tableStorageType=Typ ukladacieho priestoru
#XCOL: Space monitor column Table Type
tableType=Typ tabuľky
#XCOL: Space monitor column Record Count
tableRecordCount=Počet záznamov
#XFLD: Assigned Disk
assignedStorage=Disk priradený na ukladanie
#XFLD: Assigned Memory
assignedRAM=Pamäť priradená na ukladanie
#XCOL: Space table-view column storage utilization
tableStorageUtilization=Použitý ukladací priestor
#XFLD: space status
spaceStatus=Status priestoru
#XFLD: space type
spaceType=Typ priestoru
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=Premostenie SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=Produkt poskytovateľa údajov
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=Priestor {0} nemôžete zmeniť, pretože jeho typ je {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=Nemôžete odstrániť vybrané priestory ({0}). Priestory s nasledujúcimi typmi priestorov nemožno odstrániť: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=Monitorovať
#XFLD: Tooltip for edit space button
editSpace=Upraviť priestor
#XMSG: Deletion warning in messagebox
deleteConfirmation=Naozaj chcete odstrániť tento priestor?
#XFLD: Tooltip for delete space button
deleteSpace=Odstrániť priestor
#XFLD: storage
storage=Disk na ukladanie
#XFLD: username
userName=Meno používateľa
#XFLD: port
port=Port
#XFLD: hostname
hostName=Názov hostiteľa
#XFLD: password
password=Heslo
#XBUT: Request new password button
requestPassword=Požadovať nové heslo
#YEXP: Usage explanation in time data section
timeDataSectionHint=Vytvorte časové tabuľky a dimenzie, ktoré použijete vo svojich modeloch a príbehoch.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=Chcete, aby iné nástroje a aplikácie používali údaje vo vašich priestoroch? V takom prípade vytvorte jedného alebo viacerých používateľov, ktorí budú mať prístup k údajom vo vašom priestore, a vyberte, či chcete, aby boli všetky budúce údaje priestoru predvolene možné použiť.
#XTIT: Create schema popup title
createSchemaDialogTitle=Vytvoriť otvorenú schému SQL
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=Vytvoriť časové tabuľky a dimenzie
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=Upraviť časové tabuľky a dimenzie
#XTIT: Time Data token title
timeDataTokenTitle=Časové údaje
#XTIT: Time Data token title
timeDataUpdateViews=Aktualizovať zobrazenia časových údajov
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=Prebieha vytvorenie...
#XFLD: Time Data token creation error label
timeDataCreationError=Vytvorenie zlyhalo. Skúste to znovu.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=Nastavenia časovej tabuľky
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=Prevodové tabuľky
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=Časové dimenzie
#XFLD: Time Data dialog time range label
timeRangeHint=Definujte časové rozpätie.
#XFLD: Time Data dialog time data table label
timeDataHint=Zadajte názov tabuľky.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=Zadajte názvy dimenzií.
#XFLD: Time Data Time range description label
timerangeLabel=Časové rozpätie
#XFLD: Time Data dialog from year label
fromYearLabel=Od roku
#XFLD: Time Data dialog to year label
toYearLabel=Do roku
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=Typ kalendára
#XFLD: Time Data dialog granularity label
granularityLabel=Granularita
#XFLD: Time Data dialog technical name label
technicalNameLabel=Technický názov
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=Prevodové tabuľky pre štvrťroky
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=Prevodové tabuľky pre mesiace
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=Prevodové tabuľky pre dni
#XFLD: Time Data dialog year label
yearLabel=Dimenzia roku
#XFLD: Time Data dialog quarter label
quarterLabel=Dimenzia štvrťroku
#XFLD: Time Data dialog month label
monthLabel=Dimenzia mesiaca
#XFLD: Time Data dialog day label
dayLabel=Dimenzia dňa
#XFLD: Time Data dialog gregorian calendar type label
gregorian=Gregoriánsky
#XFLD: Time Data dialog time granularity day label
day=Deň
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=Dosiahnutá maximálna dĺžka 1 000 znakov.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=Maximálne časové rozpätie je 150 rokov.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=„Od roku“ by malo byť skoršie ako „Do roku“
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=„Od roku“ musí mať hodnotu 1900 alebo vyššiu.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=„Do roku“ by malo byť neskoršie ako „Od roku“.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=„Do roku“ musí byť skoršie ako aktuálny rok plus 100.
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=Zvýšenie hodnoty „Od roku“ môže viesť k strate údajov
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=Zníženie hodnoty „Do roku“ môže viesť k strate údajov
#XMSG: Time Data creation validation error message
timeDataValidationError=Zdá sa, že niektoré polia sú neplatné. Ak chcete pokračovať, skontrolujte povinné polia.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=Naozaj chcete odstrániť údaje?
#XMSG: Time Data creation success message
createTimeDataSuccess=Časové údaje vytvorené
#XMSG: Time Data update success message
updateTimeDataSuccess=Časové údaje aktualizované
#XMSG: Time Data delete success message
deleteTimeDataSuccess=Časové údaje odstránené
#XMSG: Time Data creation error message
createTimeDataError=Počas pokusu o vytvorenie časových údajov sa vyskytla chyba.
#XMSG: Time Data update error message
updateTimeDataError=Počas pokusu o aktualizáciu časových údajov sa vyskytla chyba.
#XMSG: Time Data creation error message
deleteTimeDataError=Počas pokusu o odstránenie časových údajov sa vyskytla chyba.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=Časové údaje nebolo možné načítať.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=Upozornenie
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=Vaše časové údaje nebolo možné odstrániť, pretože sú použité v iných modeloch.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=Vaše časové údaje nebolo možné odstrániť, pretože sú použité v inom modeli.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=Toto pole je povinné a nemôže byť prázdne.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=Otvoriť v prieskumníkovi databáz
#YMSE: Dimension Year
dimensionYearView=Dimenzia „Rok“
#YMSE: Dimension Year
dimensionQuarterView=Dimenzia „Štvrťrok“
#YMSE: Dimension Year
dimensionMonthView=Dimenzia „Mesiac“
#YMSE: Dimension Year
dimensionDayView=Dimenzia „Deň“
#XFLD: Time Data deletion object title
timeDataUsedIn=(použité v {0} modeloch)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(použité v 1 modeli)
#XFLD: Time Data deletion table column provider
provider=Poskytovateľ
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=Závislosti
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=Vytvoriť používateľa pre schému priestoru
#XFLD: Create schema button
createSchemaButton=Vytvoriť otvorenú schému SQL
#XFLD: Generate TimeData button
generateTimeDataButton=Vytvoriť časové tabuľky a dimenzie
#XFLD: Show dependencies button
showDependenciesButton=Zobraziť závislosti
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=Ak chcete vykonať túto operáciu, váš používateľ musí byť členom priestoru.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=Vytvoriť používateľa schémy priestoru
#YMSE: API Schema users load error
loadSchemaUsersError=Zoznam používateľov nebolo možné načítať.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=Detaily používateľa schémy priestoru
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=Naozaj chcete odstrániť vybraného používateľa?
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=Používateľ odstránený.
#YMSE: API Schema user deletion error
userDeleteError=Používateľa nebolo možné odstrániť.
#XFLD: User deleted
userDeleted=Používateľ bol odstránený.
#XTIT: Remove user popup title
removeUserConfirmationTitle=Upozornenie
#XMSG: Remove user popup text
removeUserConfirmation=Naozaj chcete používateľa odstrániť? Používateľ a jeho priradené roly s rozsahom budú z priestoru odstránené.
#XMSG: Remove users popup text
removeUsersConfirmation=Naozaj chcete používateľov odstrániť? Používatelia a im priradené roly s rozsahom budú z priestoru odstránené.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=Odstrániť
#YMSE: No data text for available roles
noDataAvailableRoles=Priestor sa nepridáva k žiadnej role s rozsahom. \n Aby bolo možné do priestoru pridať používateľov, musí byť najprv pridaný do jednej alebo viacerých rolí s rozsahom.
#YMSE: No data text for selected roles
noDataSelectedRoles=Žiadne vybrané roly s rozsahom
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=Otvoriť detaily konfigurácie schémy SQL
#XFLD: Label for Read Audit Log
auditLogRead=Aktivovať protokol auditu pre operácie čítania
#XFLD: Label for Change Audit Log
auditLogChange=Aktivovať protokol auditu pre operácie zmeny
#XFLD: Label Audit Log Retention
auditLogRetention=Zachovať protokoly
#XFLD: Label Audit Log Retention Unit
retentionUnit=Dni
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=Zadajte celé číslo medzi {0} a {1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=Spotrebovať údaje schémy priestoru
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=Zastaviť spotrebu údajov schémy priestoru
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=Táto schéma Open SQL môže spotrebovávať údaje vašej schémy priestoru. Ak zastavíte spotrebu, modely založené na údajoch schémy priestoru už nemusia fungovať.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=Zastaviť spotrebu
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=Tento priestor sa používa na prístup k dátovému jazeru
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=Aktivované dátové jazero
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=Dosiahnutý limit pamäte
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=Dosiahnutý limit ukladacieho priestoru
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=Dosiahnutý limit minimálneho ukladacieho priestoru
#XFLD: Space ram tag
ramLimitReachedLabel=Dosiahnutý limit pamäte
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=Dosiahnutý limit minimálnej pamäte
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=Dosiahli ste limit priradeného ukladacieho priestoru pre priestor {0}. Priestoru je potrebné priradiť viac ukladacieho priestoru.
#XFLD: System storage tag
systemStorageLimitReachedLabel=Dosiahnutý limit systémového ukladacieho priestoru
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=Dosiahli ste limit systémového ukladacieho priestoru {0}. Teraz nemôžete priestoru priradiť ďalší ukladací priestor.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=Odstránením tejto schémy Open SQL sa trvalo odstránia aj všetky uložené objekty a spravované asociácie v schéme. Chcete pokračovať?
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=Schéma odstránená
#YMSE: Error while deleting schema.
schemaDeleteError=Schému nebolo možné odstrániť.
#XMSG: Success message after update a schema
schemaUpdateSuccess=Schéma aktualizovaná
#YMSE: Error while updating schema.
schemaUpdateError=Schému nebolo možné aktualizovať.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=Poskytli sme heslo pre túto schému. Ak ste ho zabudli alebo stratili, môžete požiadať o nové. Nezabudnite nové heslo skopírovať alebo uložiť.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=Svoje heslo skopírujte. Budete musieť nastaviť pripojenie k tejto schéme. Ak ste svoje heslo zabudli, môžete otvoriť tento dialóg a resetovať ho.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=Tento názov sa po vytvorení schémy nemôže zmeniť.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=Open SQL
#XFLD: Space schema section sub headline
schemasSpace=Priestor
#XFLD: HDI Container section header
HDIContainers=Kontajnery HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=Pridať kontajnery HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=Odstrániť kontajnery HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=Aktivovať prístup
#YMSE: No data text for HDI Containers table
noDataHDIContainers=Neboli pridané žiadne kontajnery HDI.
#YMSE: No data text for Timedata section
noDataTimedata=Neboli vytvorené žiadne tabuľky a dimenzie.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=Nie je možné načítať časové tabuľky a dimenzie, pretože databáza doby chodu nie je dostupná.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=Nie je možné načítať kontajnery HDI, pretože databáza doby chodu nie je k dispozícii.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=Kontajnery HDI nebolo možné získať. Skúste to znovu neskôr.
#XFLD Table column header for HDI Container names
HDIContainerName=Názov kontajnera HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=Aktivovať prístup
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=Vo svojom nájomcovi SAP Datasphere možnosť aktivovať SAP SQL Data Warehousing na výmenu údajov medzi vašimi kontajnermi HDI a vašimi priestormi SAP Datasphere bez toho, aby ste museli údaje presúvať.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=Na vykonanie tejto akcie kliknite na tlačidlo nižšie a otvorte hlásenie podpory.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=Po spracovaní vášho hlásenia musíte vytvoriť jeden alebo viacero nových kontajnerov HDI v databáze v dobe chodu SAP Datasphere. Následne sa tlačidlo Umožniť prístup nahradí tlačidlom + v časti Kontajnery HDI pre všetky vaše priestory v SAP Datasphere a vy tak môžete do príslušného priestoru pridať svoje kontajnery.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=Potrebujete viac informácií? Prejdite na %%0. Podrobné informácie o tom, čo zahrnúť do hlásenia, nájdete v %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=SAP Help
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=SAP Note 3057059
#XBUT: Open Ticket Button Text
openTicket=Otvoriť tiket
#XBUT: Add Button Text
add=Pridať
#XBUT: Next Button Text
next=Ďalej
#XBUT: Edit Button Text
editUsers=Upraviť
#XBUT: create user Button Text
createUser=Vytvoriť
#XBUT: Update user Button Text
updateUser=Vybrať
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=Pridať nepriradené kontajnery HDI
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=Nenašli sme žiadne nepriradené kontajnery. \n Kontajner, ktorý hľadáte, už môže byť priradený k priestoru.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=Priradené kontajnery HDI nebolo možné načítať.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=Kontajnery HDI nebolo možné načítať.
#XMSG: Success message
succeededToAddHDIContainer=Kontajner HDI pridaný
#XMSG: Success message
succeededToAddHDIContainerPlural=Kontajnery HDI pridané
#XMSG: Success message
succeededToDeleteHDIContainer=Kontajner HDI odstránený
#XMSG: Success message
succeededToDeleteHDIContainerPlural=Kontajnery HDI odstránené
#XFLD: Time data section sub headline
timeDataSection=Časové tabuľky a dimenzie
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=Čítať
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=Zmeniť
#XFLD: Remote sources section sub headline
allconnections=Priradenie pripojenia
#XFLD: Remote sources section sub headline
localconnections=Lokálne pripojenia
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=Konektory SAP Open Connector
#XFLD: User section sub headline
memberassignment=Priradenie člena
#XFLD: User assignment section sub headline
userAssignment=Priradenie používateľa
#XFLD: User section Access dropdown Member
member=Člen
#XFLD: User assignment section column name
user=Meno používateľa
#XTXT: Selected role count
selectedRoleToolbarText=Vybrané: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=Pripojenia
#XTIT: Space detail section data access title
detailsSectionDataAccess=Prístup k schéme
#XTIT: Space detail section time data title
detailsSectionGenerateData=Časové údaje
#XTIT: Space detail section members title
detailsSectionUsers=Členovia
#XTIT: Space detail section Users title
detailsSectionUsersTitle=Používatelia
#XTIT: Out of Storage
insufficientStoragePopoverTitle=Nedostatok ukladacieho priestoru
#XTIT: Storage distribution
storageDistributionPopoverTitle=Využité úložisko disku
#XTXT: Out of Storage popover text
insufficientStorageText=Ak chcete vytvoriť nový priestor, znížte priradený ukladací priestor iného priestoru alebo odstráňte priestor, ktorý už nepotrebujete. Môžete zväčšiť celkový ukladací priestor systému vyvolaním položky Spravovať plán.
#XMSG: Space id length warning
spaceIdLengthWarning=Prekročený maximálny počet {0} znakov.
#XMSG: Space name length warning
spaceNameLengthWarning=Prekročený maximálny počet {0} znakov.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=Nepoužívajte prefix {0}, aby ste sa vyhli možným konfliktom.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=Schémy Open SQL nebolo možné načítať.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=Schémy Open SQL nebolo možné vytvoriť.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=Nebolo možné načítať všetky vzdialené pripojenia.
#YMSE: Error while loading space details
loadSpaceDetailsError=Detaily priestoru nebolo možné načítať.
#YMSE: Error while deploying space details
deploySpaceDetailsError=Priestor nebolo možné nasadiť.
#YMSE: Error while copying space details
copySpaceDetailsError=Priestor nebolo možné skopírovať.
#YMSE: Error while loading storage data
loadStorageDataError=Údaje ukladacieho priestoru nebolo možné načítať.
#YMSE: Error while loading all users
loadAllUsersError=Nebolo možné načítať všetkých používateľov.
#YMSE: Failed to reset password
resetPasswordError=Heslo nebolo možné resetovať.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=Nové heslo úspešne nastavené pre schému
#YMSE: DP Agent-name too long
DBAgentNameError=Názov agenta poskytovania údajov je príliš dlhý.
#YMSE: Schema-name not valid.
schemaNameError=Názov schémy je neplatný.
#YMSE: User name not valid.
UserNameError=Meno používateľa je neplatné.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=Spotreba podľa typu ukladacieho priestoru
#XTIT: Consumption by Schema
consumptionSchemaText=Spotreba podľa schémy
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=Celková spotreba tabuliek podľa schémy
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=Celková spotreba podľa typu tabuľky
#XTIT: Tables
tableDetailsText=Detaily tabuľky
#XTIT: Table Storage Consumption
tableStorageConsumptionText=Spotreba ukladacieho priestoru tabuliek
#XFLD: Table Type label
tableTypeLabel=Typ tabuľky
#XFLD: Schema label
schemaLabel=Schéma
#XFLD: reset table tooltip
resetTable=Resetovať tabuľku
#XFLD: In-Memory label in space monitor
inMemoryLabel=Pamäť
#XFLD: Disk label in space monitor
diskLabel=Disk
#XFLD: Yes
yesLabel=Áno
#XFLD: No
noLabel=Nie
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=Chcete, aby bolo predvolene možné spotrebovať údaje v tomto priestore?
#XFLD: Business Name
businessNameLabel=Podnikový názov
#XFLD: Refresh
refresh=Obnoviť
#XMSG: No filter results title
noFilterResultsTitle=Zdá sa, že vaše nastavenia filtra nezobrazujú žiadne údaje.
#XMSG: No filter results message
noFilterResultsMsg=Skúste spresniť svoje nastavenia filtra. Ak ani potom stále nevidíte žiadne údaje, vytvorte zopár tabuliek v zostavovači údajov. Keď budú využívať ukladací priestor, budete ich tu môcť monitorovať.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=Databáza v dobe chodu nie je dostupná.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=Databáza v dobe chodu nie je dostupná, určité funkcie boli deaktivované a na tejto stránke nie je možné zobraziť žiadne informácie.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=Používateľ schémy priestorov nebolo možné vytvoriť.
#YMSE: Error User name already exists
userAlreadyExistsError=Meno používateľa už existuje.
#YMSE: Error Authentication failed
authenticationFailedError=Overenie zlyhalo.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=Používateľ je blokovaný z dôvodu príliš veľkého počtu neúspešných prihlásení. Požiadajte o nové heslo, aby ste odblokovali používateľa.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=Nové heslo nastavené a používateľ odblokovaný
#XMSG: user is locked message
userLockedMessage=Používateľ je blokovaný.
#XCOL: Users table-view column Role
spaceRole=Rola
#XCOL: Users table-view column Scoped Role
spaceScopedRole=Roly s rozsahom
#XCOL: Users table-view column Space Admin
spaceAdmin=Správca priestorov
#XFLD: User section dropdown value Viewer
viewer=Zobrazovač
#XFLD: User section dropdown value Modeler
modeler=Modeler
#XFLD: User section dropdown value Data Integrator
dataIntegrator=Integrátor údajov
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=Správca priestorov
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=Rola priestoru bola aktualizovaná
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=Rolu priestoru nebolo možné aktualizovať.
#XFLD:
databaseUserNameSuffix=Sufix mena používateľa databázy
#XTXT: Space Schema password text
spaceSchemaPasswordText=Ak chcete nastaviť pripojenie k tejto schéme, skopírujte svoje heslo. Ak ste svoje heslo zabudli, môžete požiadať o nové.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=Cloud Platform
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=Ak chcete nastaviť prístup prostredníctvom tohto používateľa, aktivujte spotrebu a skopírujte poverenia. Ak môžete poverenia skopírovať len bez hesla, nezabudnite heslo neskôr pridať.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=Aktivovať spotrebu na Cloud Platform
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=Poverenia pre službu zadanú používateľom:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=Poverenia pre službu zadanú používateľom (bez hesla):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=Kopírovať poverenia bez hesla
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=Kopírovať celé poverenia
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=Kopírovať heslo
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=Poverenia skopírované do schránky
#XMSG: Password copied to clipboard
passwordCopiedMessage=Heslo skopírované do schránky
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=Vytvoriť používateľa databázy
#XMSG: Database Users section title
databaseUsers=Používatelia databázy
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=Detaily používateľa databázy
#XFLD: database user read audit log
databaseUserAuditLogRead=Aktivovať protokoly auditu pre operácie čítania a zachovať protokoly
#XFLD: database user change audit log
databaseUserAuditLogChange=Aktivovať protokoly auditu pre operácie zmeny a zachovať protokoly
#XMSG: Cloud Platform Access
cloudPlatformAccess=Prístup ku Cloud Platform
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=Nastavte prístup ku kontajneru HANA Deployment Infrastructure (HDI) prostredníctvom tohto používateľa databázy. Ak chcete pripojiť svoj kontajner HDI, musí byť zapnuté modelovanie SQL.
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=Aktivovať spotrebu HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=Chcete, aby sa údaje vo vašom priestore mohli použiť v iných nástrojoch alebo aplikáciách?
#XFLD: Enable Consumption
enableConsumption=Aktivovať spotrebu SQL
#XFLD: Enable Modeling
enableModeling=Aktivovať modelovanie SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=Prijatie údajov
#XMSG: Privileges for Data Consumption
privilegesConsumption=Spotreba údajov pre externé nástroje
#XFLD: SQL Modeling
sqlModeling=Modelovanie SQL
#XFLD: SQL Consumption
sqlConsumption=Spotreba SQL
#XFLD: enabled
enabled=Aktivované
#XFLD: disabled
disabled=Deaktivované
#XFLD: Edit Privileges
editPrivileges=Upraviť oprávnenia
#XFLD: Open Database Explorer
openDBX=Otvoriť prieskumníka databáz
#XFLD: create database user hint
databaseCreateHint=Pamätajte, že po uložení už nebudete môcť zmeniť meno používateľa.
#XFLD: Internal Schema Name
internalSchemaName=Interný názov schémy
#YMSE: Failed to load database users
loadDatabaseUserError=Používateľov databázy sa nepodarilo načítať
#YMSE: Failed to delete database user
deleteDatabaseUsersError=Používateľov databázy sa nepodarilo odstrániť
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=Používateľ databázy odstránený
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=Používatelia databázy odstránení
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=Používateľ databázy vytvorený
#YMSE: Failed to create database user
createDatabaseUserError=Používateľa databázy sa nepodarilo vytvoriť
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=Používateľ databázy aktualizovaný
#YMSE: Failed to update database user
updateDatabaseUserError=Používateľa databázy sa nepodarilo aktualizovať
#XFLD: HDI Consumption
hdiConsumption=Spotreba HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=Prístup k databáze
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=Nastavte, aby bolo predvolene možné spotrebovať údaje priestoru. Modely v zostavovačoch automaticky povolia spotrebu údajov.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=Predvolená spotreba údajov priestoru:
#XFLD: Database User Name
databaseUserName=Meno používateľa databázy
#XMSG: Database User creation validation error message
databaseUserValidationError=Zdá sa, že niektoré polia sú neplatné. Ak chcete pokračovať, skontrolujte povinné polia.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=Príjem údajov nie je možné aktivovať, pretože používateľ migroval.
#XBUT: Remove Button Text
remove=Odstrániť
#XBUT: Remove Spaces Button Text
removeSpaces=Odstrániť priestory
#XBUT: Remove Objects Button Text
removeObjects=Odstrániť objekty
#XMSG: No members have been added yet.
noMembersAssigned=Ešte neboli pridaní žiadni členovia.
#XMSG: No users have been added yet.
noUsersAssigned=Ešte neboli pridaní žiadni používatelia.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=Neboli vytvorení žiadni používatelia databázy alebo váš filter nezobrazuje žiadne údaje.
#XMSG: Please enter a user name.
noDatabaseUsername=Zadajte meno používateľa.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=Meno používateľa je príliš dlhé. Použite kratšie meno.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=Neboli aktivované žiadne oprávnenia a tento používateľ databázy bude mať obmedzenú funkčnosť. Ešte chcete pokračovať?
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=Ak chcete aktivovať protokoly auditu pre operácie zmeny, musí byť aktivovaný aj príjem údajov.
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=Ak chcete aktivovať protokoly auditu pre operácie čítania, musí byť aktivovaný aj príjem údajov.
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=Ak chcete aktivovať spotrebu HDI, musí byť aktivovaný aj príjem údajov a spotreba údajov. Chcete to vykonať?
#XMSG:
databaseUserPasswordText=Ak chcete nastaviť pripojenie k tomuto používateľovi databázy, skopírujte svoje heslo. Ak ste svoje heslo zabudli, môžete kedykoľvek požiadať o nové.
#XTIT: Space detail section members title
detailsSectionMembers=Členovia
#XMSG: New password set
newPasswordSet=Nastavené nové heslo
#XFLD: Data Ingestion
dataIngestion=Prijatie údajov
#XFLD: Data Consumption
dataConsumption=Spotreba údajov
#XFLD: Privileges
privileges=Oprávnenia
#XFLD: Enable Data ingestion
enableDataIngestion=Aktivovať príjem údajov
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=Zaprotokolujte operácie čítania a zmeny pre príjem údajov.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=Sprístupnite údaje priestoru v kontajneroch HDI.
#XFLD: Enable Data consumption
enableDataConsumption=Aktivovať spotrebu údajov
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=Povoľte iným aplikáciám alebo nástrojom spotrebovať vaše údaje priestoru.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=Ak chcete nastaviť prístup prostredníctvom tohto používateľa databázy, skopírujte poverenia do služby zadanej používateľom. Ak môžete poverenia skopírovať len bez hesla, nezabudnite heslo neskôr pridať.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=Kapacita doby chodu toku údajov ({0}:{1} hodín z {2} hodín)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=Nebolo možné načítať kapacitu doby chodu toku údajov
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=Používateľ môže povoliť spotrebu údajov iným používateľom.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=Aktivovať spotrebu údajov pomocou možnosti udelenia
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=Ak chcete aktivovať spotrebu údajov s možnosťou udelenia, musí byť aktivovaná spotreba. Chcete aktivovať obe možnosti?
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=Aktivovať Automated Predictive Library (APL) a Predictive Analysis Library (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=Používateľ môže použiť funkcie strojového učenia vnoreného do SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=Politika hesiel
#XMSG: Password Policy
passwordPolicyHint=Tu aktivujete alebo deaktivujete konfigurovanú politiku hesiel.
#XFLD: Enable Password Policy
enablePasswordPolicy=Aktivovať politiku hesiel
#XMSG: Read Access to the Space Schema
readAccessTitle=Čítací prístup k schéme priestorov
#XMSG: read access hint
readAccessHint=Povoliť databázovému používateľovi pripojiť externé nástroje k schéme priestorov a čítať zobrazenia, ktoré sú k dispozícii na spotrebu.
#XFLD: Space Schema
spaceSchema=Schéma priestorov
#XFLD: Enable Read Access (SQL)
enableReadAccess=Povoliť čítací prístup (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=Povoliť používateľovi prideliť čítací prístup iným používateľom.
#XFLD: With Grant Option
withGrantOption=S možnosťou pridelenia
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=Sprístupnite údaje priestoru v kontajneroch HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=Aktivovať spotrebu HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=Zápisový prístup do schémy Open SQL používateľa
#XMSG: write access hint
writeAccessHint=Povoliť databázovému používateľovi pripojiť externé nástroje k schéme Open SQL používateľa na vytvorenie entít údajov a prijatie údajov, ktoré sa použijú v priestore.
#XFLD: Open SQL Schema
openSQLSchema=Schéma Open SQL
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=Povoliť zápisový prístup (SQL, DDL & DML)
#XMSG: audit hint
auditHint=Zaprotokolovať operácie čítania a zmeny v schéme Open SQL.
#XMSG: data consumption hint
dataConsumptionHint=Predvolene môžete všetky nové zobrazenia sprístupniť v priestore na účely spotreby. Modeleri môžu toto nastavenie prepísať pre jednotlivé zobrazenie pomocou prepínača „Sprístupniť pre spotrebu“ v bočnom paneli výstupu zobrazenia. Môžete tiež vybrať formáty, v ktorých sa zobrazenia sprístupnia.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=Predvolene sprístupniť pre spotrebu
#XMSG: database users hint consumption hint
databaseUsersHint2New=Vytvorte databázových používateľov na prepojenie externých nástrojov s SAP Datasphere. Nastavte oprávnenia, aby ste povolili používateľom načítať údaje priestoru a vytvoriť entity údajov (DDL) a prijať údaje (DML) na použitie v priestore.
#XFLD: Read
read=Čítať
#XFLD: Read (HDI)
readHDI=Čítať (HDI)
#XFLD: Write
write=Písať
#XMSG: HDI Containers Hint
HDIContainersHint2=Povoľte prístup ku kontajnerom SAP HANA Deployment Infrastructure (HDI) vo vašom priestore. Modeleri môžu použiť artefakty HDI ako zdroje pre zobrazenia a klienti HDI majú prístup k údajom priestoru.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=Otvoriť informačný dialóg
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=Používateľ databázy je blokovaný. Ak ho chcete odblokovať,odtvorte dialógové okno
#XFLD: Table
table=Tabuľka
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=Pripojenie partnera
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=Konfigurácia pripojenia partnera
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=Definujte vlastnú dlaždicu pripojenia partnera pripojením URL a ikony iFrame. Táto konfigurácia je dostupná len pre tohto nájomcu.
#XFLD: Table Name Field
partnerConnectionConfigurationName=Názov dlaždice
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=URL iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=Pôvod následnej správy iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=Ikona
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=Nenašla sa žiadna konfigurácia pripojenia partnera.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=Konfigurácie pripojenia partnera sa nedajú zobraziť, keď nie je k dispozícii databáza doby chodu.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=Vytvoriť konfiguráciu pripojenia partnera
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=Odovzdať ikonu
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=Vybrať (maximálna veľkosť 200KB)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=Príklad dlaždice partnera
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=Prehľadávať
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=Konfigurácia pripojenia partnera bola úspešne vytvorená.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=Počas odstraňovania konfigurácií pripojenia partnera sa vyskytla chyba.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=Konfigurácia pripojenia partnera bola úspešne odstránená.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=Počas načítania konfigurácií pripojenia partnera sa vyskytla chyba.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=Súbor nebolo možné odovzdať, pretože prekračuje maximálnu veľkosť 200KB.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=Vytvoriť konfiguráciu pripojenia partnera
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=Odstrániť konfiguráciu pripojenia partnera.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=Dlaždicu partnera nebolo možné vytvoriť.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=Dlaždicu partnera nebolo možné odstrániť.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=Reset nastavení cloudového konektora SAP HANA zákazníka zlyhal
#XFLD: Workload Class
workloadClass=Trieda pracovného zaťaženia
#XFLD: Workload Management
workloadManagement=Správa pracovného zaťaženia
#XFLD: Priority
workloadClassPriority=Priorita
#XMSG:
workloadManagementPriorityHint=Pri vytvorení dotazu na databázu môžete určiť prioritu tohto priestoru. Zadajte hodnotu od 1 (najnižšia priorita) do 8 (najvyššia priorita). V situácii, keď priestory súťažia o dostupné vlákna, tie s vyššími prioritami sa spúšťajú pred priestormi s nižšími prioritami.
#XMSG:
workloadClassPriorityHint=Môžete zadať prioritu priestoru od 0 (najnižšia) po 8 (najvyššia). Príkazy priestoru s vysokou prioritou sa vykonajú pred príkazmi ostatných priestorov s nižšou prioritou. Predvolená priorita je 5. Hodnota 5 je rezervovaná pre systémové operácie, nie je dostupná pre priestor.
#XFLD: Statement Limits
workloadclassStatementLimits=Limity príkazov
#XFLD: Workload Configuration
workloadConfiguration=Konfigurácia pracovného zaťaženia
#XMSG:
workloadClassStatementLimitsHint=Môžete zadať maximálny počet (alebo percento) vlákien a GB pamäte, ktorú spotrebúvajú súbežne spustené príkazy v priestore. Môžete zadať hodnotu alebo percento od 0 (žiadny limit) do dostupnej celkovej pamäte a vlákien v nájomcovi. \n\n Ak zadáte limit vlákien, pamätajte, že môže dôjsť k zníženiu výkonu. \n\n Ak zadáte limit pamäte, príkazy, ktoré dosiahnu limit pamäte, sa nevykonajú.
#XMSG:
workloadClassStatementLimitsDescription=Predvolená konfigurácia poskytuje veľkorysé limity zdrojov a zároveň zabraňuje preťaženiu systému akéhokoľvek jednotlivého priestoru.
#XMSG:
workloadClassStatementLimitCustomDescription=Môžete nastaviť maximálne limity celkového počtu vlákien a pamäte, ktoré môžu spotrebovať príkazy spustené súbežne v priestore.
#XMSG:
totalStatementThreadLimitHelpText=Nastavenie príliš nízkeho limitu vlákna môže ovplyvniť výkon príkazu, zatiaľ čo príliš vysoké hodnoty alebo 0 umožňujú, aby priestor spotreboval všetky dostupné vlákna systému.
#XMSG:
totalStatementMemoryLimitHelpText=Nastavenie príliš nízkeho limitu pamäte môže spôsobiť problémy s nedostatkom pamäte, zatiaľ čo príliš vysoké hodnoty alebo 0 umožňujú, aby priestor spotreboval všetku dostupnú systémovú pamäť.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=Zadajte percento medzi 1% a 70% (alebo ekvivalentné číslo) z celkového počtu vlákien dostupných vo vašom kliente. Nastavenie príliš nízkeho limitu vlákien môže ovplyvniť výkon príkazu, zatiaľ čo nadmerne vysoké hodnoty môžu ovplyvniť výkon príkazu v iných priestoroch.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=Zadajte percento medzi 1% a {0}% (alebo ekvivalentné číslo) z celkového počtu vlákien dostupných vo vašom kliente. Nastavenie príliš nízkeho limitu vlákien môže ovplyvniť výkon príkazu, zatiaľ čo nadmerne vysoké hodnoty môžu ovplyvniť výkon príkazu v iných priestoroch.
#XMSG:
totalStatementMemoryLimitHelpTextNew=Zadajte hodnotu alebo percento medzi 1% a 70% (žiadny limitu) z celkového počtu vlákien dostupných vo vašom kliente. Nastavenie príliš nízkeho limitu vlákien môže ovplyvniť výkon príkazu, zatiaľ čo nadmerne vysoké hodnoty môžu ovplyvniť výkon príkazu v iných priestoroch.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=Limit celkového počtu vlákien príkazu
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=GB
#XFLD, 80: Segmented button label
workloadclassThreads=Vlákna
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=Limit celkovej pamäte príkazov
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=Informácie o SAP HANA zákazníka sa nepodarilo načítať.
#XMSG:
minimumLimitReached=Dosiahnutý minimálny limit.
#XMSG:
maximumLimitReached=Dosiahnutý maximálny limit.
#XMSG: Name Taken for Technical Name
technical-name-taken=Pripojenie so zadaným technickým názvom už existuje. Zadajte iný názov.
#XMSG: Name Too long for Technical Name
technical-name-too-long=Zadaný technický názov prekračuje 40 znakov. Zadajte názov s menším počtom znakov.
#XMSG: Technical name field empty
technical-name-field-empty=Zadajte technický názov.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=Pre názov môžete zadať len písmená (a-z), čísla (0-9) a znaky podčiarknutia (_).
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=Zadaná názov nemôže začínať ani končiť znakom podčiarknutia (_).
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=Aktivovať limity príkazu
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=Nastavenia
#XMSG: Connections tool hint in Space details section
connectionsToolHint=Ak chcete vytvoriť alebo upraviť pripojenia, otvorte aplikáciu Pripojenia z bočnej navigácie alebo kliknite sem:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=Prejsť na pripojenia
#XFLD: Not deployed label on space tile
notDeployedLabel=Priestor ešte nebol nasadený.
#XFLD: Not deployed additional text on space tile
notDeployedText=Nasaďte priestor.
#XFLD: Corrupt space label on space tile
corruptSpace=Vyskytla sa chyba.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=Skúste opätovné nasadenie alebo kontaktujte podporu
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=Údaje protokolu auditu
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=Administratívne dáta
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=Ostatné dáta
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=Údaje v priestore
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=Naozaj chcete odblokovať priestor?
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=Naozaj chcete zablokovať priestor?
#XFLD: Lock
lock=Blokovať
#XFLD: Unlock
unlock=Odblokovať
#XFLD: Locking
locking=Blokovanie
#XMSG: Success message after locking space
lockSpaceSuccessMsg=Priestor blokovaný
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=Priestor odblokovaný
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=Priestory blokované
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=Priestory odblokované
#YMSE: Error while locking a space
lockSpaceError=Priestor nie je možné zablokovať.
#YMSE: Error while unlocking a space
unlockSpaceError=Priestor nie je možné odblokovať.
#XTIT: popup title Warning
confirmationWarningTitle=Upozornenie
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=Priestor bol manuálne blokovaný.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=Systém zablokoval priestor, pretože protokoly auditu spotrebúvajú veľké množstvo GB na disku.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=Systém zablokoval priestor, pretože prekračuje pridelenia pamäte alebo úložiska na disku.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=Naozaj chcete odblokovať vybrané priestory?
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=Naozaj chcete blokovať vybrané priestory?
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=Editor rolí s rozsahom
#XTIT: ECN Management title
ecnManagementTitle=Správa priestoru a elastických výpočtových uzlov
#XFLD: ECNs
ecns=Elastické výpočtové uzly
#XFLD: ECN phase Ready
ecnReady=Pripravené
#XFLD: ECN phase Running
ecnRunning=Spustené
#XFLD: ECN phase Initial
ecnInitial=Nepripravené
#XFLD: ECN phase Starting
ecnStarting=Spúšťa sa
#XFLD: ECN phase Starting Failed
ecnStartingFailed=Spustenie zlyhalo
#XFLD: ECN phase Stopping
ecnStopping=Zastavuje sa
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=Zastavenie zlyhalo
#XBTN: Assign Button
assign=Priradiť priestory
#XBTN: Start Header-Button
start=Spustiť
#XBTN: Update Header-Button
repair=Aktualizovať
#XBTN: Stop Header-Button
stop=Zastaviť
#XFLD: ECN hours remaining
ecnHoursRemaining=Zostáva 1000 hodín
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=Zostáva {0} blokových hodín
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=Zostáva {0} bloková hodina
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=Vytvoriť elastický výpočtový uzol
#XTIT: ECN edit dialog title
ecnEditDialogTitle=Upraviť elastický výpočtový uzol
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=Odstrániť elastický výpočtový uzol
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=Priradiť priestory
#XFLD: ECN ID
ECNIDLabel=Elastický výpočtový uzol
#XTXT: Selected toolbar text
selectedToolbarText=Vybrané: {0}
#XTIT: Elastic Compute Nodes
ECNslong=Elastické výpočtové uzly
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=Počet objektov
#XTIT: Object assignment - Dialog header text
selectObjects=Vyberte priestory a objekty, ktoré chcete priradiť k svojmu elastickému výpočtovému uzlu:
#XTIT: Object assignment - Table header title: Objects
objects=Objekty
#XTIT: Object assignment - Table header: Type
type=Typ
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=Uvedomte si, že odstránenie používateľa databázy bude mať za následok odstránenie všetkých vygenerovaných záznamov protokolu auditu. Ak chcete ponechať protokoly auditu, zvážte ich exportovanie pred odstránením používateľa databázy.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=Uvedomte si, že zrušenie priradenia kontajnera HDI z priestoru bude mať za následok odstránenie všetkých vygenerovaných záznamov protokolu auditu. Ak chcete ponechať protokoly auditu, zvážte ich exportovanie skôr, ako zrušíte priradenie kontajnera HDI.
#XTXT: All audit logs
allAuditLogs=Všetky položky protokolu auditu vygenerované pre priestor
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=Uvedomte si, že zakázanie smernice auditu (operácie čítania alebo zmeny) bude mať za následok odstránenie všetkých záznamov protokolu auditu. Ak chcete ponechať záznamy protokolu auditu, zvážte ich exportovanie skôr, ako zakážete smernicu auditu.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=Žiadne priestory alebo objekty nie sú ešte priradené
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=Aby ste mohli začať pracovať s vašim elastickým výpočtovým uzlom, priraďte mu priestor alebo objekty.
#XTIT: No Spaces Illustration title
noSpacesTitle=Zatiaľ nie je vytvorený žiadny priestor
#XTIT: No Spaces Illustration description
noSpacesDescription=Pre začatie získavania údajov, vytvorte priestor.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=Kôš je prázdny
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=Tu môžete obnoviť odstránené priestory.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=Po nasadení priestoru budú nasledujúci používatelia databázy {0} odstránení a nebude možné ich obnoviť:
#XTIT: Delete database users
deleteDatabaseUsersTitle=Odstrániť používateľov databázy
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=ID už existuje.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=Používajte iba malé písmená a - z a čísla 0 - 9.
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=ID musí pozostávať minimálne z {0} znakov.
#XMSG: ecn id length warning
ecnIdLengthWarning=Prekročený maximálny počet {0} znakov.
#XFLD: open System Monitor
systemMonitor=Monitor systémov
#XFLD: open ECN schedule dialog menu entry
schedule=Plán
#XFLD: open create ECN schedule dialog
createSchedule=Vytvoriť plán
#XFLD: open change ECN schedule dialog
changeSchedule=Upraviť plán
#XFLD: open delete ECN schedule dialog
deleteSchedule=Odstrániť plán
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=Priradiť plán mne
#XFLD: open pause ECN schedule dialog
pauseSchedule=Pozastaviť plán
#XFLD: open resume ECN schedule dialog
resumeSchedule=Obnoviť plán
#XFLD: View Logs
viewLogs=Upraviť protokoly
#XFLD: Compute Blocks
computeBlocks=Bloky výpočtu
#XFLD: Memory label in ECN creation dialog
ecnMemory=Pamäť (GB)
#XFLD: Storage label in ECN creation dialog
ecnStorage=Úložisko (GB)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=Počet CPU
#XFLD: ECN updated by label
changedBy=Zmenil
#XFLD: ECN updated on label
changedOn=Zmenené dňa
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=Bol vytvorený elastický výpočtový uzol
#YMSE: Error while creating a Elastic Compute Node
createEcnError=Elastický výpočtový uzol sa nepodarilo vytvoriť
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=Elastický výpočtový uzol bol aktualizovaný
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=Elastický výpočtový uzol sa nepodarilo aktualizovať
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=Elastický výpočtový uzol bol odstránený
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=Elastický výpočtový uzol sa nepodarilo odstrániť
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=Spustenie elastického výpočtového uzla
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=Zastavenie elastického výpočtového uzla
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=Elastický výpočtový uzol sa nepodarilo spustiť
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=Elastický výpočtový uzol sa nepodarilo zastaviť
#XBUT: Add Object button for an ECN
assignObjects=Pridať objekty
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=Automaticky pridať všetky objekty
#XFLD: object type label to be assigned
objectTypeLabel=Typ (sémantické použitie)
#XFLD: assigned object type label
assignedObjectTypeLabel=Typ
#XFLD: technical name label
TechnicalNameLabel=Technický názov
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=Vyberte objekty, ktoré chcete pridať k svojmu elastickému výpočtovému uzlu.
#XTIT: Add objects dialog title
assignObjectsTitle=Priradiť objekty
#XFLD: object label with object count
objectLabel=Objekt
#XMSG: No objects available to add message.
noObjectsToAssign=Nie sú k dispozícii žiadne objekty na priradenie.
#XMSG: No objects assigned message.
noAssignedObjects=Nepriradené žiadne objekty.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=Upozornenie
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=Odstrániť
#XMSG: Remove objects popup text
removeObjectsConfirmation=Naozaj chcete odstrániť vybraté objekty?
#XMSG: Remove spaces popup text
removeSpacesConfirmation=Naozaj chcete odstrániť vybrané priestory?
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=Odstrániť priestory
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=Vystavené objekty boli odstránené
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=Vystavené objekty boli priradené
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=Všetky vystavené objekty
#XFLD: Spaces tab label
spacesTabLabel=Priestory
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=Vystavené objekty
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=Priestory boli odstránené
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=Priestor bol odstránený
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=Priestory sa nepodarilo priradiť alebo odstrániť.
#YMSE: Error while removing objects
removeObjectsError=Nepodarilo sa nám priradiť ani odstrániť objekty.
#YMSE: Error while removing object
removeObjectError=Nepodarilo sa nám priradiť ani odstrániť objekt.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=Predtým vybraté číslo už nie je platné. Vyberte platné číslo.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=Vyberte platnú triedu výkonu.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=Predtým zvolená výkonnostná trieda "{0}" momentálne nie je platná. Vyberte platnú výkonnostnú triedu.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=Naozaj chcete odstrániť elastický výpočtový uzol?
#XFLD: tooltip for ? button
help=Nápoveda
#XFLD: ECN edit button label
editECN=Konfigurovať
#XFLD: Technical type label for ERModel
DWC_ERMODEL=Entita - model vzťahov
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=Lokálna tabuľka
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=Vzdialená tabuľka
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=Analytický model
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=Reťazec úloh
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=Tok údajov
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=Replikačný tok
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=Transformačný tok
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=Inteligentné vyhľadávanie
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=Úložisko
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=Komplexné hľadanie
#XFLD: Technical type label for View
DWC_VIEW=Zobraziť
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=Údajový produkt
#XFLD: Technical type label for Data Access Control
DWC_DAC=Riadenie prístupu k údajom
#XFLD: Technical type label for Folder
DWC_FOLDER=Priečinok
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=Obchodná entita
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=Variant obchodnej entity
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=Scenár zodpovednosti
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=Model faktov
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=Perspektíva
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=Model spotreby
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=Vzdialené pripojenie
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=Variant modelu faktov
#XMSG: Schedule created alert message
createScheduleSuccess=Plán vytvorený
#XMSG: Schedule updated alert message
updateScheduleSuccess=Plán aktualizovaný
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Plán odstránený
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=Vám priradený plán
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=Pozastavenie 1 plánu
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=Obnovenie 1 plánu
#XFLD: Segmented button label
availableSpacesButton=K dispozícii
#XFLD: Segmented button label
selectedSpacesButton=Vybrané
#XFLD: Visit website button text
visitWebsite=Navštíviť webovú stránku
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=Predtým vybraný zdrojový jazyk sa odstráni.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=Aktivovať
#XFLD: ECN performance class label
performanceClassLabel=Trieda výkonu
#XTXT performance class memory text
memoryText=Pamäť
#XTXT performance class compute text
computeText=Vypočítať
#XTXT performance class high-compute text
highComputeText=High-Compute
#XBUT: Recycle Bin Button Text
recycleBin=Kôš
#XBUT: Restore Button Text
restore=Obnoviť
#XMSG: Warning message for new Workload Management UI
priorityWarning=Táto oblasť je len na čítanie. Prioritu priestoru môžete zmeniť v oblasti Systém / Konfigurácia / Správa pracovného zaťaženia.
#XMSG: Warning message for new Workload Management UI
workloadWarning=Táto oblasť je len na čítanie. Konfiguráciu pracovného zaťaženia priestoru môžete zmeniť v oblasti Systém / Konfigurácia / Správa pracovného zaťaženia.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=Apache Spark vCPUs
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=Apache Spark Memory (GB)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=Prijatie údajov produktu
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=Nie sú k dispozícii žiadne údaje, pretože priestor sa momentálne nasadzuje
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=Nie sú k dispozícii žiadne údaje, pretože priestor sa momentálne zavádza
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=Upraviť priradenia inštancií
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
