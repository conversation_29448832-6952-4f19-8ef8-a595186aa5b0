#DO NOT REMOVE________________________
#XFLD: Breadcrumb for space monitoring page
spaceMonitorBreadcrumb=المراقبة
#XTXT: Type name for spaces in browser tab page title
space=المساحة
#_____________________________________
#XFLD: Spaces label in
spaces=المساحات
#XFLD: Manage plan button text
manageQuotaButtonText=إدارة الخطة
#XBUT: Manage resources button
manageResourcesButton=إدارة الموارد
#XFLD: Create space button tooltip
createSpace=إنشاء مساحة
#XFLD: Create
create=إنشاء
#XFLD: Deploy
deploy=نشر
#XFLD: Page
page=الصفحة
#XFLD: Cancel
cancel=إلغاء
#XFLD: Update
update=تحديث
#XFLD: Save
save=حفظ
#XFLD: OK
ok=موافق
#XFLD: days
days=أيام
#XFLD: Space tile edit button label
edit=تحرير
#XFLD: Auto Assign all objects to space
autoAssign=تعيين تلقائي
#XFLD: Space tile open monitoring button label
openMonitoring=مراقبة
#XFLD: Delete
delete=حذف
#XFLD: Copy Space
copy=نسخ
#XFLD: Close
close=إغلاق
#XCOL: Space table-view column status
status=الحالة
#XFLD: Space status active
activeLabel=نشط
#XFLD: Space status locked
lockedLabel=مؤمَّن
#XFLD: Space status critical
criticalLabel=حرج
#XFLD: Space status cold
coldLabel=ضعيف
#XFLD: Space status deleted
deletedLabel=محذوف
#XFLD: Space status unknown
unknownLabel=غير معروف
#XFLD: Space status ok
okLabel=صحيح
#XFLD: Database user expired
expired=منتهي الصلاحية
#XFLD: deployed
deployed=منشور
#XFLD: not deployed
notDeployed=غير منشور
#XFLD: changes to deploy
changesToDeploy=التغييرات للنشر
#XFLD: pending
pending=النشر
#XFLD: designtime error
designtimeError=خطأ في وقت التصميم
#XFLD: runtime error
runtimeError=خطأ في وقت التشغيل
#XFLD: Space created by label
createdBy=المنشئ
#XFLD: Space created on label
createdOn=تاريخ الإنشاء
#XFLD: Space deployed on label
deployedOn=تاريخ النشر
#XFLD: Space ID label
spaceID=معرف المساحة
#XFLD: Priority label
priority=الأفضلية
#XFLD: Space Priority label
spacePriority=أفضلية المساحة
#XFLD: Space Configuration label
spaceConfiguration=تكوين المساحة
#XFLD: Not available
notAvailable=غير متوفر
#XFLD: WorkloadType default
default=افتراضي
#XFLD: WorkloadType custom
custom=مخصص
#XFLD: Data Lake option label; refers to component SAP HANA Cloud, Data Lake
dataLakeAccess=الوصول إلى مستودع البيانات
#XFLD: Translation label
translationLabel=الترجمة
#XFLD: Source language label
sourceLanguageLabel=اللغة المصدر
#XFLD: Translation CheckBox label
translationCheckBox=تمكين الترجمة
#XTXT: Popover hint that space has to be deployed to show user details
openDatabaseUserHintPopoverText=نشر المساحة للوصول إلى تفاصيل المستخدم.
#XTXT: Popover hint that space has to be deployed to open database explorer
openDatabaseExplorerHintPopoverText=نشر المساحة لفتح مستكشف قاعدة البيانات.
#XTXT: Text for when the Data Lake check box is disabled
dataLakeOptionDisabled=لا يمكنك استخدام هذه المساحة للوصول إلى مستودع البيانات لأنه مستخدم بالفعل بواسطة مساحة أخرى.
#XFLD: Data Lake check box text; refers to component SAP HANA Cloud, Data Lake
dataLakeCheckBox=استخدم هذه المساحة للوصول إلى مستودع البيانات.
#XFLD: Space Priority minimum label extension
low=منخفض
#XFLD: Space Priority maximum label extension
high=مرتفع
#XFLD: Space name label
spaceName=اسم المساحة
#XFLD: Enable deploy objects checkbox
enableDeployObjects=نشر الكائنات
#XTIT: Copy spaces dialog title
copySpaceDialogTitle=نسخ {0}
#XTXT Human readable text for unselected source language
unSelectedSourceLanguage=(غير محدد)
#XTXT Human readable text for language code "af"
af=الأفريقانية
#XTXT Human readable text for language code "ar"
ar=العربية
#XTXT Human readable text for language code "bg"
bg=البلغارية
#XTXT Human readable text for language code "ca"
ca=الكتالانية
#XTXT Human readable text for language code "zh"
zh=الصينية المبسّطة
#XTXT Human readable text for language code "zf"
zf=الصينية
#XTXT Human readable text for language code "hr"
hr=الكرواتية
#XTXT Human readable text for language code "cs"
cs=التشيكية
#XTXT Human readable text for language code "cy"
cy=الويلزية
#XTXT Human readable text for language code "da"
da=الدانمركية
#XTXT Human readable text for language code "nl"
nl=الهولندية
#XTXT Human readable text for language code "en-UK"
en-UK=الإنجليزية (المملكة المتحدة)
#XTXT Human readable text for language code "en"
en=الإنجليزية (الولايات المتحدة)
#XTXT Human readable text for language code "et"
et=الإستونية
#XTXT Human readable text for language code "fa"
fa=الفارسية
#XTXT Human readable text for language code "fi"
fi=الفنلندية
#XTXT Human readable text for language code "fr-CA"
fr-CA=الفرنسية (كندا)
#XTXT Human readable text for language code "fr"
fr=الفرنسية
#XTXT Human readable text for language code "de"
de=الألمانية
#XTXT Human readable text for language code "el"
el=اليونانية
#XTXT Human readable text for language code "he"
he=العبرية
#XTXT Human readable text for language code "hi"
hi=الهندية
#XTXT Human readable text for language code "hu"
hu=الهنغارية
#XTXT Human readable text for language code "is"
is=الأيسلندية
#XTXT Human readable text for language code "id"
id=الأندونيسية
#XTXT Human readable text for language code "it"
it=الإيطالية
#XTXT Human readable text for language code "ja"
ja=اليابانية
#XTXT Human readable text for language code "kk"
kk=الكازاخستانية
#XTXT Human readable text for language code "ko"
ko=الكورية
#XTXT Human readable text for language code "lv"
lv=اللاتفية
#XTXT Human readable text for language code "lt"
lt=الليتوانية
#XTXT Human readable text for language code "ms"
ms=الملايوية
#XTXT Human readable text for language code "no"
no=النرويجية
#XTXT Human readable text for language code "pl"
pl=البولندية
#XTXT Human readable text for language code "pt"
pt=البرتغالية (البرازيل)
#XTXT Human readable text for language code "pt-PT"
pt-PT=البرتغالية (البرتغال)
#XTXT Human readable text for language code "ro"
ro=الرومانية
#XTXT Human readable text for language code "ru"
ru=الروسية
#XTXT Human readable text for language code "sr"
sr=الصربية
#XTXT Human readable text for language code "sh"
sh=الصربية الكرواتية
#XTXT Human readable text for language code "sk"
sk=السلوفاكية
#XTXT Human readable text for language code "sl"
sl=السلوفانية
#XTXT Human readable text for language code "es"
es=الإسبانية
#XTXT Human readable text for language code "es-MX"
es-MX=الإسبانية (المكسيك)
#XTXT Human readable text for language code "sv"
sv=السويدية
#XTXT Human readable text for language code "th"
th=التايلاندية
#XTXT Human readable text for language code "tr"
tr=التركية
#XTXT Human readable text for language code "uk"
uk=الأوكرانية
#XTXT Human readable text for language code "vi"
vi=الفيتنامية
#XTIT: Delete spaces confirmation title
deleteConfirmationHeaderText=حذف المساحات
#XTXT: Logically delete space delete confirmation text with space name
logicallyDeleteConfirmationBodyTextWithSpaceName=هل تريد نقل المساحة "{0}" إلى سلة المحذوفات؟
#XTXT: Logically dpace delete confirmation text for multiple spaces
logicallyDeleteConfirmationBodyTextMultiple=هل تريد نقل المساحات المحددة "{0}" إلى سلة المحذوفات؟
#XTXT: Delete space delete confirmation text with space name
deleteConfirmationBodyTextWithSpaceName=هل تريد بالتأكيد حذف المساحة "{0}"؟ لا يمكن التراجع عن هذا الإجراء.
#XTXT: Delete space delete confirmation text for multiple spaces
deleteConfirmationBodyTextMultiple=هل تريد بالتأكيد حذف {0} من المساحات المحددة؟ لا يمكن التراجع عن هذا الإجراء. سيتم حذف المحتوى التالي {1}:
#XTXT: permanently
permanently=نهائيًا
#XTXT: {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDetail=سيتم حذف المحتوى التالي {0} ولا يمكن استرداده:
#XTXT: Hint Delete space confirmation text
deleteConfirmationHint=يرجى كتابة {0} لتأكيد الحذف.
#XTXT: Delete space confirmation error check spelling
deleteConfirmationErrorCheckSpelling=يرجى التحقق من التهجئة ثم إعادة المحاولة.
#XTXT: All Spaces
allSpaces=كل المساحات
#XTXT: All data
allData=كافة الكائنات والبيانات المضمنة في المساحة
#XTXT: All connections
allConnections=كل الاتصالات المحددة في المساحة
#XFLD: Space tile selection box tooltip
clickToSelect=انقر للتحديد
#XTXT: All database users
allDatabaseUsers=جميع الكائنات والبيانات المضمنة في أي مخطط SQL مفتوح مرتبط بالمساحة
#XFLD: remove members button tooltip
deleteUsers=إزالة الأعضاء
#XTXT: Space long description text
description=الوصف (4000 حرف كحد أقصى)
#XFLD: Add Members button tooltip
addUsers=إضافة أعضاء
#XFLD: Add Users button tooltip
addUsersTooltip=إضافة مستخدمين
#XFLD: Edit Users button tooltip
editUsersTooltip=تحرير المستخدمين
#XFLD: Remove Users button tooltip
removeUsersTooltip=إزالة بيانات المستخدمين
#XFLD: Searchfield placeholder
filter=بحث
#XCOL: Users table-view column health
health=الحالة
#XCOL: Users table-view column access
access=الوصول
#XFLD: No user found nodatatext
noDataText=لم يتم العثور على أي مستخدم
#XTIT: Members dialog title
selectUserDialogTitle=إضافة أعضاء
#XTIT: User dialog title
addUserDialogTitle=إضافة مستخدمين
#XTIT: Delete connections confirmation title
deleteConnectionConfirmationHeaderText=حذف الاتصالات
#XTIT: Delete single connection confirmation title
deleteConnectionConfirmationHeaderTextSingle=حذف الاتصال
#XTXT: Delete connection delete confirmation text
deleteConnectionConfirmationBodyText=هل تريد بالتأكيد حذف الاتصالات المحددة؟ سيتم حذفها نهائيًا.
#XTIT: Remote source dialog title
selectRemoteConnectionDialogTitle=تحديد الاتصالات
#XTIT: Share connection dialog title
connectionSharingDialogTitle=مشاركة الاتصال
#XTIT: Connections shared from other spaces table title
sharedConnectionsFromOtherSpaces=الاتصالات المشتركة
#XFLD: Add remote source button tooltip
addRemoteConnections=إضافة اتصالات
#XFLD: Delete remote source button tooltip
deleteRemoteConnections=إزالة الاتصالات
#XFLD: Share remote source button tooltip
shareConnections=مشاركة الاتصالات
#XFLD: Tile-layout tooltip
tileLayout=مخطط الإطار
#XFLD: Table-layout tooltip
tableLayout=مخطط الجدول
#XMSG: Success message after creating space
createSpaceSuccessMessage=تم إنشاء المساحة
#XMSG: Success message after copying space
copySpaceSuccessMessage=جارٍ نسخ المساحة "{0}" إلى المساحة "{1}"
#XMSG: Success message after deploying space
deploymentSuccessMessage=تم البدء في نشر المساحة
#XMSG: Success message after Apache Spark update started
sparkUpdateStartedSuccessMessage=بدأ تحديث Apache Spark
#YMSE: Error while starting Apache Spark update
sparkUpdateStartedErrorMessage=فشل تحديث Apache Spark
#XMSG: Success message after updating space
updateSettingsSuccessMessage=تم تحديث تفاصيل المساحة
#XMSG: Success message after unlocking space
unlockSpaceSuccessMessage=تم إلغاء تأمين المساحة مؤقتًا
#XMSG: Success message after deleting a single space
deleteSpaceSuccessMessage=تم حذف المساحة
#XMSG: Success message after deleting multiple spaces
deleteSpacesSuccessMessage=تم حذف المساحات
#XMSG: Success message after restoring a single space
restoreSpaceSuccessMessage=تم استعادة المساحة
#XMSG: Success message after restoring multiple spaces
restoreSpacesSuccessMessage=تم استعادة المساحات
#YMSE: Error while updating settings
updateSettingsFailureMessage=تعذر تحديث إعدادات المساحة.
#YMSE: Error while updating data lake option
dataLakeEnabledInOtherSpace=تم تعيين مستودع البيانات بالفعل إلى مساحة أخرى. يمكن لمساحة واحدة فقط الوصول إلى مستودع البيانات في المرة الواحدة.
#YMSE: Error while updating data lake option
virtualTablesExists=لا يمكنك إلغاء تعيين مستودع البيانات من هذه المساحة لأنه لا تزال هناك تبعيات للجداول الافتراضية*. يرجى حذف الجداول الافتراضية لإلغاء تعيين مستودع البيانات من هذه المساحة.
#YMSE: Error while unlocking space
unlockSpaceFailureMessage=تعذر إلغاء تأمين المساحة.
#YMSE: Error while creating space
createSpaceError=تعذر إنشاء المساحة.
#YMSE: Error while creating duplicate space
createDuplicateSpaceError=توجد مساحة بالاسم {0} بالفعل.
#YMSE: Error while deleting a single space
deleteSpaceError=تعذر حذف المساحة.
#YMSE: Error details while deleting a single space
deleteSpaceDetailedError=لم تعد المساحة "{0}" تعمل بصورة صحيحة. يرجى محاولة حذفها مرة أخرى. إذا ظلت متوقفة عن العمل، فاطلب من المسؤول لديك حذف مساحتك أو فتح بطاقة.
#YMSE: Error while deleting a single space in files
deleteSpaceInFilesError=تعذر حذف بيانات المساحة في 'الملفات'.
#YMSE: Error while deleting a single space users
deleteSpaceUsersError=تعذرت إزالة المستخدمين.
#YMSE: Error while deleting a single space schema
deleteSpaceSchemasError=تعذرت إزالة المخططات.
#YMSE: Error while deleting a single space connections
deleteSpaceConnectionsError=تعذرت إزالة الاتصالات.
#YMSE: Error while deleting a single space data
deleteSpaceDataError=تعذر حذف بيانات المساحة.
#YMSE: Error while deleting multiple spaces
deleteSpacesError=تعذر حذف المساحات.
#YMSE: Error while restoring a single space
restoreSpaceError=تعذر استعادة المساحة.
#YMSE: Error while restoring multiple spaces
restoreSpacesError=تعذر استعادة المساحات.
#YMSE: Error while creating users
createUsersError=تعذرت إضافة المستخدمين.
#YMSE: Error while removing users
removeUsersError=تعذر علينا إزالة بيانات المستخدمين.
#YMSE: Error while removing user
removeUserError=تعذر إزالة بيانات المستخدم.
#YMSE: Error assigning roles to a user
roleAssignUnAssignError=تعذرت إضافة المستخدم إلى الدور المحدد المجال. \n\n لا يمكنك إضافة نفسك إلى دور محدد المجال. يمكنك مطالبة المسؤول لديك بإضافتك إلى دور محدد المجال.
#YMSE: Error assigning user to the space
userAssignError=تعذر تعيين المستخدم إلى المساحة. \n\n تم بالفعل تعيين المستخدم إلى الحد الأقصى المسموح به للعدد (100) من المسافات عبر الأدوار المحددة.
#YMSE: Error assigning users to the space
usersAssignError=تعذر تعيين المستخدمين إلى المساحة. \n\n تم بالفعل تعيين المستخدم إلى الحد الأقصى المسموح به للعدد (100) من المسافات عبر الأدوار المحددة.
#YMSE: Error while fetching users
fetchUserWithScopedRolesError=تعذر علينا استرجاع المستخدمين. يُرجى المحاولة مرة أخرى لاحقًا.
#YMSE: Error while fetching scoped roles
fetchScopedRolesError=تعذر علينا استرجاع الأدوار ذات المجال.
#YMSE: Error while fetching members
fetchUserError=تعذر استدعاء الأعضاء. الرجاء المحاولة مرة أخرى لاحقًا.
#YMSE: Error while loading run-time database
loadRuntimeError=تعذر تحميل المعلومات من قاعدة بيانات وقت التشغيل.
#YMSE: Error while loading spaces
loadSpacesError=عذرًا، حدث خطأ ما عند محاولة استرجاع مساحاتك.
#YMSE: Error while loading haas resources
loadStorageError=عذرًا، حدث خطأ ما عند محاولة استرجاع بيانات التخزين.
#YMSE: Error no data could be loaded
loadDataError=عذرًا، حدث خطأ ما عند محاولة استرجاع بياناتك.
#XFLD: Click to refresh storage data
clickToRefresh=انقر هنا لإعادة المحاولة.
#XTIT: Delete space popup title
deleteSpacePopupTitle=حذف المساحة
#XCOL: Spaces table-view column name
name=الاسم
#XCOL: Spaces table-view deployment status
deploymentStatus=حالة النشر
#XFLD: Disk label in space details
storageLabel=القرص (جيجابايت)
#XFLD: In-Memory label in space details
ramLabel=الذاكرة (جيجا بايت)
#XFLD: Memory label on space card
memory=ذاكرة التخزين
#XFLD: Storage Assignment label in space details
storrageAssignmentLabel=المساحة التخزينية
#XFLD: Storage Type label in space details
storageTypeLabel=نوع التخزين
#XFLD: Enable Space Quota
enableSpaceQuota=تمكين حصة المساحة
#XFLD: No Space Quota
noSpaceQuota=دون حصة المساحة
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
diskAndInMemoryText=قاعدة بيانات SAP HANA (القرص والذاكرة الضمنية)
#XFLD: SAP HANA DB Disk and In-Memory label of radio button
dataLakeFilesText=ملفات مستودع بيانات SAP HANA
#XFLD: Available scoped roles label
availableRoles=الأدوار ذات النطاق المتوفرة
#XFLD: Selected scoped roles label
selectedRoles=الأدوار ذات النطاق المحددة
#XCOL: Spaces table-view column models
models=النماذج
#XCOL: Spaces table-view column users
users=المستخدمون
#XCOL: Spaces table-view column connections
connections=الاتصالات
#XFLD: Section header overview in space detail
overview=نظرة عامة
#XFLD: Section header Apache Spark in space detail
sparkManagementSection=Apache Spark
#XFLD: Section header Apache Spark in space detail
sparkManagementSubSection=التطبيقات
#XFLD: Section header Apache Spark in space detail
taskAssignmentSubSection=تعيين المهام
#XFLD: vCPU label in Apache Spark section
vCPULabel=وحدات المعالجة المركزية الافتراضية
#XFLD: Memory label in Apache Spark section
memoryLabel=الذاكرة (جيجا بايت)
#XFLD: space configuration label in Apache Spark section
spaceConfigurationLabel=تكوين المساحة
#XFLD: Space Source label
sparkApplicationLabel=التطبيق
#XFLD: Cluster Size label
clusterSizeLabel=حجم التجمع
#XFLD: Driver label
driverLabel=العامل
#XFLD: Executor label
executorLabel=المنفِّذ
#XFLD: max label
maxLabel=الحد الأقصى المستخدم
#XFLD: TrF Default label
trFDefaultLabel=الإعداد الافتراضي لتدفق التحويل
#XFLD: Merge Default label
mergeDefaultLabel=الإعداد الافتراضي للدمج
#XFLD: Optimize Default label
optimizeDefaultLabel=تحسين الإعداد الافتراضي
#XFLD: Deployment Default label
deploymentDefaultLabel=نشر الجدول المحلي (الملف)
#XFLD: max spark source label
driverExecutorMaxUsedSparkSource={0} وحدة المعالجة المركزية / {1} جيجا بايت
#XFLD: Object type label
taskObjectTypeLabel=نوع الكائن
#XFLD: Task activity label
taskActivityLabel=النشاط
#XFLD: Task Application ID label
taskApplicationIDLabel=التطبيق الافتراضي
#XFLD: Section header in space detail
generalSettings=إعدادات عامة
#XMSG: Message strip message when a space is locked
spaceLockedMessageStrip=هذه المساحة مؤمَّنة حاليًا بواسطة النظام.
#XMSG: Message strip message deploy warning
deployWarningMessageStrip=سيتم نشر التغييرات في هذا القسم على الفور.
#XMSG: Message strip message modify space configuration warning
modifySpaceConfigurationMessageStrip=اعلم أن تغيير هذه القيم قد يتسبب في حدوث مشكلات في الأداء.
#XFLD: Button text to unlock the space again
unlockSpace=إلغاء تأمين المساحة
#XFLD: Info text for audit log formatted message
auditLogText=يمكنك تمكين سجلات التدقيق لتسجيل إجراء القراءة أو التغيير (سياسات التدقيق). يمكن للمسؤولين بعد ذلك تحليل بيانات الشخص الذي نفَّذ الإجراء ووقت التنفيذ.
#XMSG: Message strip  audit warning
auditLogWarningMessageStrip=يمكن أن تستهلك سجلات التدقيق مساحة كبيرة من التخزين على القرص في وحدتك المستضافة. في حالة تمكين سياسة تدقيق (إجراء القراءة أو التغيير)، يجب مراقبة استخدام مساحة التخزين على القرص بانتظام (من خلال بطاقة "مساحة التخزين على القرص المستخدمة" في "مراقبة النظام") لتجنب استخدام مساحة القرص بأكملها؛ مما قد يؤدي إلى تعطل الخدمة. أما في حالة تعطيل سياسة التدقيق، فسيتم حذف جميع إدخالات سجل التدقيق الخاصة بها. وإذا كنت تريد الاحتفاظ بإدخالات سجل التدقيق، فعليك مراعاة تصديرها قبل تعطيل سياسة التدقيق.
#XFLD: Message strip link text to open in app help to guide user what to do to unlock his space permanently
unlockSpaceHelp=إظهار المساعدة
#XMSG: Message strip message when a space will be locked again
spaceTemporarilyUnlockedMessageStrip=تتجاوز هذه المساحة مساحتها التخزينية وسيتم تأمينها في {0} {1}.
#XMSG: Unit for remaining time until space is locked again
hours=ساعات
#XMSG: Unit for remaining time until space is locked again
minutes=دقائق
#XFLD: Section header in space detail for auditing
detailsSectionAuditing=التدقيق
#XFLD: Subsection header in space detail for auditing
auditing=إعدادات تدقيق المساحة
#XFLD: Hot space tooltip
hotSpaceCountTooltip=المساحات الحرجة: التخزين المستخدم أكبر من 90%.
#XFLD: Green space tooltip
greenSpaceCountTooltip=المساحات الصحيحة: التخزين المستخدم بين 6% و90%.
#XFLD: Cold space tooltip
coldSpaceCountTooltip=المساحات المستخدمة نادرًا: التخزين المستخدم هو 5% أو أقل.
#XFLD: Critical space tooltip
criticalSpaceCountTooltip=المساحات الحرجة: التخزين المستخدم أكبر من 90%.
#XFLD: Green space tooltip
okSpaceCountTooltip=المساحات الصحيحة: التخزين المستخدم بين 6% و90%.
#XFLD: Locked space tooltip
lockedSpaceCountTooltipNew=المساحات المؤمَّنة: تم الإيقاف لأن مساحة الذاكرة غير كافية.
#XFLD: Locked space tooltip
lockedSpaceCountTooltip=المساحات المؤمَّنة
#YMSE: Error while deleting remote source
deleteRemoteError=تعذرت إزالة الاتصالات.
#XFLD: Space ID input tooltip
spaceIDInfoTooltip=معرف المساحة لا يمكن تغييره لاحقًا.\nالحروف الصالحة هي A - Z و0 - 9 و_
#YMSE: Error when space name is empty
spaceNameEmptyValidationError=أدخل اسم المساحة.
#YMSE: Error when ECN Business name is empty
ecnNameEmptyValidationError=أدخل اسمًا تجاريًا.
#YMSE: Error when space id is empty
spaceIDInputEmptyValidationError=أدخل معرف المساحة.
#YMSE: Error when space id contains invalid characters
spaceIDInputInValidError=الحروف غير صالحة. يرجى استخدام A - Z و0 - 9 و_ فقط.
#YMSE: Error when space id exists while creation
spaceIDInputDuplicateError=معرف المساحة موجود بالفعل.
#XFLD: Space searchfield placeholder
search=بحث
#XMSG: Success message after creating users
createUsersSuccess=تمت إضافة المستخدم
#XMSG: Success message after creating user
createUserSuccess=تمت إضافة المستخدم
#XMSG: Success message after updating users
updateUsersSuccess=تم تحديث {0} من المستخدمين
#XMSG: Success message after updating user
updateUserSuccess=تم تحديث المستخدم
#XMSG: Success message after removing users
removeUsersSuccess=تمت إزالة {0} من المستخدمين
#XMSG: Success message after removing user
removeUserSuccess=تمت إزالة المستخدم
#XFLD: Schema name
schemaName=اسم المخطط
#XFLD: used of total
ofTemplate={0} من {1}
#XFLD: Assigned disk label in space overview
assignedStorageTemplate=القرص المعيَّن ({0} من {1})
#XFLD: Assigned Memory label in space overview
assignedRAMTemplate=الذاكرة المعيَّنة ({0} من {1})
#XFLD: Storage ratio on space
accelearationRAM=تسريع الذاكرة
#XFLD: No Storage Consumption
noStorageConsumptionText=لم يتم تعيين حصة التخزين.
#XFLD: Used disk label in space overview
usedStorageTemplate=القرص المستخدم للتخزين ({0} من {1})
#XFLD: Used Memory label in space overview
usedRAMTemplate=الذاكرة المستخدمة للتخزين ({0} من {1})
#XFLD: Used Disk tooltip
usedStorageTooltipTemplate={0} من {1} من القرص المستخدم للتخزين
#XFLD: Used Memory tooltip
usedRAMTooltipTemplate={0} من {1} من الذاكرة المستخدمة
#XFLD: Assigned Disk tooltip
assignedStorageTooltipTemplate={0} من قرص {1} معيَّن
#XFLD: Assigned Memory tooltip
assignedRAMTooltipTemplate={0} من {1} من الذاكرة المعيَّنة
#XFLD: Space related used storage tooltip
spaceRelatedUsedStorageTooltipTemplate=بيانات المساحة: {0}
#XFLD: Not space related used storage tooltip
otherDataUsedStorageTooltipTemplate=بيانات أخرى: {0}
#XFLD: Storage used exceeded popover and tooltip
storageUsedExceededText=قم بمراعاة تمديد خطتك، أو اتصل بدعم SAP.
#XCOL: Space table-view column used Disk
usedStorage=القرص المستخدم للتخزين
#XCOL: Space monitor column used Memory
usedRAM=الذاكرة المستخدمة للتخزين
#XCOL: Space monitor column Schema
tableSchema=المخطط
#XCOL: Space monitor column Storage Type
tableStorageType=نوع التخزين
#XCOL: Space monitor column Table Type
tableType=نوع الجدول
#XCOL: Space monitor column Record Count
tableRecordCount=عدد السجلات
#XFLD: Assigned Disk
assignedStorage=القرص المُعيَّن للتخزين
#XFLD: Assigned Memory
assignedRAM=الذاكرة المُعيَّنة للتخزين
#XCOL: Space table-view column storage utilization
tableStorageUtilization=التخزين المستخدم
#XFLD: space status
spaceStatus=حالة المساحة
#XFLD: space type
spaceType=نوع المساحة
#XFLD: space type label for space with space type "SAP BW bridge" ("SAP BW bridge" is a product name, do not translate it)
abapbridgeSpaceType=وحدة توصيل SAP BW
#XFLD: space type label for space with space type "Data Marketplace"
marketplaceSpaceType=منتج مزود البيانات
#XMSG: Error message for when selected space cannot be deleted due to the space type
deleteTypedSpaceError=لا يمكنك حذف المساحة {0} حيث إن نوع المساحة الخاصة بها هو {1}.
#XMSG: Error message for when selected spaces cannot be deleted due to the space type
deleteTypedSpacesError=لا يمكن حذف {0} من المساحات المحددة، حيث إن المساحات بأنواع المساحات التالية لا يمكن حذفها: {1}.
#XFLD: space type label for space without space type
defaultSpaceType=SAP Datasphere
#XFLD: Tooltip for monitor space button
monitorSpace=مراقبة
#XFLD: Tooltip for edit space button
editSpace=تحرير المساحة
#XMSG: Deletion warning in messagebox
deleteConfirmation=هل تريد بالفعل حذف هذه المساحة؟
#XFLD: Tooltip for delete space button
deleteSpace=حذف المساحة
#XFLD: storage
storage=قرص التخزين
#XFLD: username
userName=اسم المستخدم
#XFLD: port
port=المنفذ
#XFLD: hostname
hostName=اسم المضيف
#XFLD: password
password=كلمة المرور
#XBUT: Request new password button
requestPassword=طلب كلمة مرور جديدة
#YEXP: Usage explanation in time data section
timeDataSectionHint=قم بإنشاء الجداول والأبعاد الزمنية لاستخدامها في النماذج والقصص لديك.
#YEXP: Usage explanation in Space schema section
spaceSchemaHint=هل تريد أن تكون البيانات في مساحتك قابلة للاستهلاك من جانب الأدوات أو التطبيقات الأخرى؟ إذا كان الأمر كذلك، فقم بإنشاء مستخدم واحد أو عدة مستخدمين يمكن لهم الوصول إلى البيانات في مساحتك ثم قم بتحديد ما إذا كنت تريد أن تكون كل بيانات المساحة المستقبلية قابلة للاستهلاك بشكل افتراضي.
#XTIT: Create schema popup title
createSchemaDialogTitle=إنشاء مخطط SQL مفتوح
#XTIT: Generate Time Data dialog title
generateTimeDataDialogTitle=إنشاء جداول وأبعاد زمنية
#XTIT: Edit Time Data dialog title
editTimeDataDialogTitle=تحرير الجداول والأبعاد الزمنية
#XTIT: Time Data token title
timeDataTokenTitle=البيانات الزمنية
#XTIT: Time Data token title
timeDataUpdateViews=تحديث طرق عروض البيانات الزمنية
#XFLD: Time Data token creation in progress label
timeDataCreationInProgress=الإنشاء قيد التنفيذ...
#XFLD: Time Data token creation error label
timeDataCreationError=فشل الإنشاء. يرجى المحاولة مرة أخرى.
#XTIT: Time Data dialog time settings title
timeDataDialogTimeSettingsTitle=إعدادات الجدول الزمني
#XTIT: Time Data dialog time data table title
timeDataDialogTranslationTablesTitle=جداول التحويل
#XTIT: Time Data dialog dimensions title
timeDataDialogTimeViewsTitle=الأبعاد الزمنية
#XFLD: Time Data dialog time range label
timeRangeHint=قم بتحديد النطاق الزمني.
#XFLD: Time Data dialog time data table label
timeDataHint=أدخل اسمًا لجدولك.
#XFLD: Time Data dialog time data dimensions label
timeDimensionsHint=أدخل اسمًا للأبعاد لديك.
#XFLD: Time Data Time range description label
timerangeLabel=النطاق الزمني
#XFLD: Time Data dialog from year label
fromYearLabel=سنة البدء
#XFLD: Time Data dialog to year label
toYearLabel=سنة الانتهاء
#XFLD: Time Data dialog calendar type label
calendarTypeLabel=نوع التقويم
#XFLD: Time Data dialog granularity label
granularityLabel=مستوى التفصيل
#XFLD: Time Data dialog technical name label
technicalNameLabel=الاسم التقني
#XFLD: Time Data dialog quarters text table lable
quarterTextsTableLabel=جدول التحويل لأرباع السنة
#XFLD: Time Data dialog months text table lable
monthsTextsTableLabel=جدول التحويل للشهور
#XFLD: Time Data dialog days text table lable
daysTextsTableLabel=جدول التحويل للأيام
#XFLD: Time Data dialog year label
yearLabel=بُعد السنة
#XFLD: Time Data dialog quarter label
quarterLabel=بُعد ربع السنة
#XFLD: Time Data dialog month label
monthLabel=بُعد الشهر
#XFLD: Time Data dialog day label
dayLabel=بُعد اليوم
#XFLD: Time Data dialog gregorian calendar type label
gregorian=ميلادي
#XFLD: Time Data dialog time granularity day label
day=اليوم
#XFLD: Time Data dialog business name maxlength reached
timeRangeValueStateTextMaxLengthBusinessName=تم الوصول إلى الحد الأقصى للطول المكون من 1000 حرف.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextMaxRangeExceeded=الحد الأقصى للنطاق الزمني هو 150 سنة.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeLower=يجب أن تكون "سنة البدء" أقل من "سنة الانتهاء"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextFromYearMustBeHigher=يجب أن تكون "سنة البدء" 1900 أو أكبر.
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeHigher=يجب أن تكون "سنة الانتهاء" أكبر من القيمة "سنة البدء"
#XFLD: Time Data dialog time range validation value state text
timeRangeValueStateTextToYearMustBeLower=يجب أن تكون "سنة الانتهاء" أقل من السنة الحالية زائد 100
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningFromYear=قد يترتب على زيادة "سنة البدء" فقدان البيانات
#XFLD: Time Data dialog time range warning value state text
timeRangeValueStateTextDataLossWarningToYear=قد يترتب على خفض "سنة البدء" فقدان البيانات
#XMSG: Time Data creation validation error message
timeDataValidationError=يبدو أن بعض الحقول ليست صالحة. يرجى التحقق من الحقول المطلوبة للمتابعة.
#XMSG: Time Data deletion popup text
deleteTimeDataConfirmation=هل أنت متأكد من رغبتك في حذف البيانات؟
#XMSG: Time Data creation success message
createTimeDataSuccess=تم إنشاء بيانات الوقت
#XMSG: Time Data update success message
updateTimeDataSuccess=تم تحديث بيانات الوقت
#XMSG: Time Data delete success message
deleteTimeDataSuccess=تم حذف بيانات الوقت
#XMSG: Time Data creation error message
createTimeDataError=حدث خطأ ما أثناء محاولة إنشاء البيانات الزمنية.
#XMSG: Time Data update error message
updateTimeDataError=حدث خطأ ما أثناء محاولة تحديث البيانات الزمنية.
#XMSG: Time Data creation error message
deleteTimeDataError=حدث خطأ ما أثناء محاولة حذف البيانات الزمنية.
#XMSG: Error message when time data could not be retrieved
retrievingTimeDataError=تعذر تحميل البيانات الزمنية.
#XTIT: Time Data deletion popup title
deleteTimeDataConfirmationTitle=تحذير
#XTXT: Time Data deletion dependency popup text
deleteTimeDataDependencyText=تعذر حذف بيانات الوقت لديك نظرًا لاستخدامها في نماذج أخرى.
#XTXT: Time Data deletion dependency popup text singular
deleteTimeDataDependencyTextSingular=تعذر حذف بيانات الوقت لديك نظرًا لاستخدامها في نموذج آخر.
#XMSG: Validation error when required time data form field is empty
timeDataRequiredField=هذا الحقل مطلوب ولا يمكن تركه فارغًا.
#XFLD: Tooltip for Open in Database Explorer button
openInDBX=فتح في مستكشف قاعدة البيانات
#YMSE: Dimension Year
dimensionYearView=البُعد "سنة"
#YMSE: Dimension Year
dimensionQuarterView=البُعد "ربع سنة"
#YMSE: Dimension Year
dimensionMonthView=البُعد "شهر"
#YMSE: Dimension Year
dimensionDayView=البُعد "يوم"
#XFLD: Time Data deletion object title
timeDataUsedIn=(مستخدَم في {0} من النماذج)
#XFLD: Time Data deletion object title singular
timeDataUsedInSingular=(مستخدَم في نموذج واحد)
#XFLD: Time Data deletion table column provider
provider=المزوِّد
#XTIT: Time Data dependency dialog title
timeDataDependencyDialogTitle=التبعيات
#XTIT: Create schema popup title
createSpaceSchemaUserDialogTitle=إنشاء مستخدم لمخطط المساحة
#XFLD: Create schema button
createSchemaButton=إنشاء مخطط SQL مفتوح
#XFLD: Generate TimeData button
generateTimeDataButton=إنشاء جداول وأبعاد زمنية
#XFLD: Show dependencies button
showDependenciesButton=إظهار التبعيات
#XFLD: tooltip for disabled buttons when user is not member of space
mustBeMemberOfSpaceForOperation=لإجراء هذه العملية، يجب أن يكون المستخدم الخاص بك عضوًا في المساحة.
#XFLD: Create space schema user button
createSpaceSchemaUserButton=إنشاء مستخدم مخطط المساحة
#YMSE: API Schema users load error
loadSchemaUsersError=تعذر تحميل قائمة المستخدمين.
#XTIT: API Schema-config popup title
schemaUserDetailsDialogTitle=تفاصيل مستخدم مخطط المساحة
#XMSG: API Schema user deletion popup text
deleteSpaceSchemaUserConfirmation=هل أنت متأكد من رغبتك في حذف المستخدم المحدد؟
#XMSG: API Schema user deletion confirmation text
userDeleteSuccess=تم حذف المستخدم.
#YMSE: API Schema user deletion error
userDeleteError=تعذر حذف المستخدم.
#XFLD: User deleted
userDeleted=تم حذف المستخدم.
#XTIT: Remove user popup title
removeUserConfirmationTitle=تحذير
#XMSG: Remove user popup text
removeUserConfirmation=هل تريد بالتأكيد إزالة المستخدم؟ ستتم إزالة المستخدم والأدوار المعيَّنة له ذات النطاق من المساحة.
#XMSG: Remove users popup text
removeUsersConfirmation=هل تريد بالتأكيد إزالة المستخدمين؟ ستتم إزالة المستخدمين والأدوار المعيَّنة لهم ذات النطاق من المساحة.
#XBUT: Confirmation button for remove user dialog
removeUserConfirmationBtn=إزالة
#YMSE: No data text for available roles
noDataAvailableRoles=لم تتم إضافة المساحة إلى أي دور محدد النطاق. \n لتتمكن من إضافة مستخدمين إلى المساحة، يجب إضافتها أولًا إلى دور واحد أو أكثر من الأدوار محددة النطاق.
#YMSE: No data text for selected roles
noDataSelectedRoles=لم يتم تحديد الأدوار ذات النطاق
#XTIT: Schema-config popup title
schemaDetailsDialogTitle=تفاصيل تكوين مخطط SQL المفتوح
#XFLD: Label for Read Audit Log
auditLogRead=تمكين سجل التدقيق لعمليات القراءة
#XFLD: Label for Change Audit Log
auditLogChange=تمكين سجل التدقيق لعمليات التغيير
#XFLD: Label Audit Log Retention
auditLogRetention=إبقاء السجلات من أجل
#XFLD: Label Audit Log Retention Unit
retentionUnit=أيام
#YMSE: Error when log retention time is set incorrectly
incorrectRetentionTime=أدخل عددًا صحيحًا بين {0} و{1}
#XFLD: Label for Cross Space Schema Access
spaceSchemaAccess=استهلاك بيانات مخطط المساحة
#XTIT: Confirm deactivation of space schema consumption dialog title
disableSpaceSchemaConsumptionDialogTitle=إيقاف استهلاك بيانات مخطط المساحة
#XTXT: Confirm deactivation of space schema consumption dialog text
disableSpaceSchemaConsumptionDialogText=مخطط SQL المفتوح هذا يستهلك بيانات مخطط المساحة لديك. إذا كنت تريد إيقاف الاستهلاك، فقد لن تعمل النماذج القائمة على بيانات مخطط المساحة بعد الآن.
#XBUT: Confirmation button for the stop consuming space schema data dialog
stopConsuming=إيقاف الاستهلاك
#XTXT: Tooltip for data lake icon
dataLakeIconTooltip=هذه المساحة مستخدمة للوصول إلى مستودع البيانات
#XFLD: Tooltip for data lake table entry
dataLakeTableEntryTooltip=تم تمكين مستودع البيانات
#XFLD: used In-Memory linit reached tag
usedRamLimitReachedLabel=تم بلوغ حد الذاكرة
#XFLD: Space storage Limit reached tag
storageLimitReachedLabel=تم بلوغ حد التخزين
#XFLD: Minimal Storage Limit Reached tag
minStorageLimitReachedLabel=تم بلوغ الحد الأدنى للتخزين
#XFLD: Space ram tag
ramLimitReachedLabel=تم بلوغ حد الذاكرة
#XFLD: Minimal In-Memory Limit Reached tag
minRamLimitReachedLabel=تم بلوغ الحد الأدنى للذاكرة
#XFLD: Popover text after click on space storage limit warning
storageLimitReachedText=لقد بلغت حد تخزين المساحة المعيَّنة المكون من {0}. يرجى تعيين المزيد من المراحل إلى المساحة.
#XFLD: System storage tag
systemStorageLimitReachedLabel=تم بلوغ حد تخزين النظام
#XMSG: Popover text after click on storage limit warning
systemStorageLimitReachedText=لقد بلغت حد تخزين النظام المكون من {0}. لا يمكنك تعيين أي تخزين إضافي إلى المساحة الآن.
#XMSG: Schema deletion warning
deleteSchemaConfirmation=حذف مخطط SQL المفتوح هذا سيترتب عليه حذف كل الكائنات المخزَّنة والارتباطات المُعالجة في المخطط نهائيًا. هل تريد المتابعة؟
#XMSG: Success message after deleting a schema
schemaDeleteSuccess=تم حذف المخطط
#YMSE: Error while deleting schema.
schemaDeleteError=تعذر حذف المخطط.
#XMSG: Success message after update a schema
schemaUpdateSuccess=تم تحديث المخطط
#YMSE: Error while updating schema.
schemaUpdateError=تعذر تحديث المخطط.
#XMSG: Hint when no password is shown in the dialog.
newPasswordHint=قمنا بتقديم كلمة مرور لهذا المخطط. إذا نسيت كلمة المرور أو فقدتها، فيمكنك طلب كلمة مرور جديدة. تذكر لنسخ كلمة المرور الجديدة أو حفظها.
#XMSG: Hint when showing the password in the dialog.
savePasswordHint=يرجى نسخ كلمة المرور. ستحتاجها من أجل إعداد اتصال بهذا المخطط. إذا نسيت كلمة المرور، فيمكنك فتح مربع الحوار هذا لإعادة تعيينها.
#XFLD,50: Hint on entering schema name in creation dialog
schemaNameHint=لا يمكن تغيير هذا الاسم بعد إنشاء المخطط.
#XFLD: Open SQL Schemas section sub headline
schemasSQL=SQL المفتوح
#XFLD: Space schema section sub headline
schemasSpace=المساحة
#XFLD: HDI Container section header
HDIContainers=حاويات HDI
#XTXT: Add HDI Containers button tooltip
addHDIContainers=إضافة حاويات HDI
#XTXT: Remove HDI Containers button tooltip
removeHDIContainers=إزالة حاويات HDI
#XFLD: Enable HDI Containers feature button
enableHDIContainersButton=تمكين الوصول
#YMSE: No data text for HDI Containers table
noDataHDIContainers=لم تتم إضافة أي من حاويات HDI.
#YMSE: No data text for Timedata section
noDataTimedata=لم يتم إنشاء أي أبعاد أو جداول زمنية.
#YMSE: No data text during runtime not available for Timedata section
runtimeNotAvailableNoDataTimedata=لا يمكن تحميل الأبعاد والجداول الزمنية نظرًا لعدم توفر قاعدة بيانات وقت التشغيل.
#YMSE: No data text during runtime not available for HDI section
runtimeNotAvailableNoDataHDI=لا يمكن تحميل حاويات HDI نظرًا لعدم توفر قاعدة بيانات وقت التشغيل.
#YMSE: Error while fetching HDI Containers
fetchHDIContainersError=تعذر الحصول على حاويات HDI. الرجاء المحاولة مرة أخرى لاحقًا.
#XFLD Table column header for HDI Container names
HDIContainerName=اسم حاوية HDI
#XFLD: Enable HDI Containers feature dialog title
enableHDIContainersDialogTitle=تمكين الوصول
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText1New=يمكنك تمكين SAP SQL Data Warehousing على وحدة SAP Datasphere المستضافة الخاصة بك لتبادل البيانات بين حاويات HDI ومساحات SAP Datasphere spaces دون الحاجة إلى نقل البيانات.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText2=لإجراء ذلك، افتح بطاقة دعم بالنقر فوق الزر أدناه.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText3New=بمجرد معالجة بطاقتك، يجب عليك إنشاء حاوية واحدة أو أكثر من حاويات HDI الجديدة في قاعدة بيانات وقت تشغيل SAP Datasphere. وبعد ذلك، يتم استبدال الزر تمكين الوصول(Enable Access) بالزر + في قسم حاويات HDI لجميع مساحات SAP Datasphere الخاصة بك، ويمكنك إضافة حاوياتك إلى مساحة.
#XFLD: Enable HDI Containers feature dialog text content
enableHDIContainersDialogText41=هل تحتاج إلى مزيد من المعلومات؟ انتقل إلى %%0. للحصول على معلومات تفصيلية حول ما يجب تضمينه في البطاقة، راجع %%1.
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkText=مساعدة SAP
#XFLD: Enable HDI Containers feature dialog text contents
enableHDIContainersDialogLinkTextSapNote3057059=ملاحظة SAP رقم 3057059
#XBUT: Open Ticket Button Text
openTicket=فتح بطاقة
#XBUT: Add Button Text
add=إضافة
#XBUT: Next Button Text
next=التالي
#XBUT: Edit Button Text
editUsers=تحرير
#XBUT: create user Button Text
createUser=إنشاء
#XBUT: Update user Button Text
updateUser=تحديد
#XTIT: Add HDI Containers Dialog Title
addHDIContainersDialogTitle=إضافة حاويات HDI مُلغى تعيينها
#XFLD: No data text for add HDI container dialog
addHDIContainersDialogNoDataText=تعذر علينا العثور على أي حاويات مُلغى تعيينها. \n الحاوية التي تبحث عنها من المحتمل أن يكون تم تعيينها إلى إحدى المساحات.
#YMSE: Error while loading assigned HDI Containers
loadAssignedHDIContainersError=تعذر تحميل حاويات HDI المعيَّنة.
#YMSE: Error while loading assigned HDI Containers
loadUnassignedHDIContainersError=تعذر تحميل حاويات HDI.
#XMSG: Success message
succeededToAddHDIContainer=تمت إضافة حاوية HDI
#XMSG: Success message
succeededToAddHDIContainerPlural=تمت إضافة حاويات HDI
#XMSG: Success message
succeededToDeleteHDIContainer=تمت إزالة حاوية HDI
#XMSG: Success message
succeededToDeleteHDIContainerPlural=تمت إزالة حاويات HDI
#XFLD: Time data section sub headline
timeDataSection=الجداول والأبعاد الزمنية
#XFLD: Schemas list entry read audit log enabled indicator
readIndicator=قراءة
#XFLD: Schemas list entry change audit log enabled indicator
changeIndicator=تغيير
#XFLD: Remote sources section sub headline
allconnections=تعيين الاتصال
#XFLD: Remote sources section sub headline
localconnections=الاتصالات المحلية
#XFLD: Remote sources section sub headline for open Connectors
openConnectors=SAP Open Connectors
#XFLD: User section sub headline
memberassignment=تعيين العضو
#XFLD: User assignment section sub headline
userAssignment=تعيين المستخدم
#XFLD: User section Access dropdown Member
member=العضو
#XFLD: User assignment section column name
user=اسم المستخدم
#XTXT: Selected role count
selectedRoleToolbarText=المحدد: {0}
#XTIT: Space detail section connections title
detailsSectionConnections=الاتصالات
#XTIT: Space detail section data access title
detailsSectionDataAccess=الوصول إلى المخطط
#XTIT: Space detail section time data title
detailsSectionGenerateData=البيانات الزمنية
#XTIT: Space detail section members title
detailsSectionUsers=الأعضاء
#XTIT: Space detail section Users title
detailsSectionUsersTitle=المستخدمون
#XTIT: Out of Storage
insufficientStoragePopoverTitle=نفاد مساحة التخزين
#XTIT: Storage distribution
storageDistributionPopoverTitle=تخزين القرص المستخدَم
#XTXT: Out of Storage popover text
insufficientStorageText=لإنشاء مساحة جديدة، يرجى تقليل التخزين المعيَّن لمساحة أخرى أو حذف مساحة لم تعد بحاجة إليها. يمكنك زيادة إجمالي تخزين النظام من خلال استدعاء 'إدارة الخطة'.
#XMSG: Space id length warning
spaceIdLengthWarning=تم تجاوز الحد الأقصى المكون من {0} من الحروف.
#XMSG: Space name length warning
spaceNameLengthWarning=تم تجاوز الحد الأقصى المكون من {0} من الحروف.
#XMSG: Space id reserved prefix warning
spaceIdnWarningReservedPrefix=يُرجى عدم استخدام البادئة {0} تجنبًا لحدوث أية تعارضات ممكنة.
#YMSE: Error while loading open SQL schemas
loadLocalSchemasError=تعذر تحميل مخططات SQL المفتوحة.
#YMSE: Error while creating open SQL schema
createLocalSchemaError=تعذر إنشاء مخطط SQL المفتوح.
#YMSE: Error while loading all remote connections
loadRemoteConnectionsError=تعذر تحميل كل الاتصالات البعيدة.
#YMSE: Error while loading space details
loadSpaceDetailsError=تعذر تحميل تفاصيل المساحة.
#YMSE: Error while deploying space details
deploySpaceDetailsError=تعذر نشر المساحة.
#YMSE: Error while copying space details
copySpaceDetailsError=تعذر نسخ المساحة.
#YMSE: Error while loading storage data
loadStorageDataError=تعذر تحميل بيانات التخزين.
#YMSE: Error while loading all users
loadAllUsersError=تعذر تحميل كل المستخدمين.
#YMSE: Failed to reset password
resetPasswordError=تعذرت إعادة تعيين كلمة المرور.
#XMSG: Success message after updating space
resetPasswordSuccessMessage=تم تعيين كلمة مرور جديدة للمخطط
#YMSE: DP Agent-name too long
DBAgentNameError=اسم وكيل توفير البيانات طويل للغاية.
#YMSE: Schema-name not valid.
schemaNameError=اسم المخطط غير صالح.
#YMSE: User name not valid.
UserNameError=اسم المستخدم غير صالح.
#XTIT: Consumption by Storage Type
consumptionStorageTypeText=الاستهلاك حسب نوع التخزين
#XTIT: Consumption by Schema
consumptionSchemaText=الاستهلاك حسب المخطط
#XTIT: Overall Consumption by schema
overallConsumptionSchemaText=استهلاك الجدول الكلي حسب المخطط
#XTIT: Overall Consumption by Table Type
overallConsumptionTableTypeText=الاستهلاك الكلي حسب نوع الجدول
#XTIT: Tables
tableDetailsText=تفاصيل الجدول
#XTIT: Table Storage Consumption
tableStorageConsumptionText=استهلاك تخزين الجدول
#XFLD: Table Type label
tableTypeLabel=نوع الجدول
#XFLD: Schema label
schemaLabel=المخطط
#XFLD: reset table tooltip
resetTable=إعادة تعيين الجدول
#XFLD: In-Memory label in space monitor
inMemoryLabel=الذاكرة
#XFLD: Disk label in space monitor
diskLabel=القرص
#XFLD: Yes
yesLabel=نعم
#XFLD: No
noLabel=لا
#XFLD: switch label: Want the data in this space to be consumable by default?
allowConsumptionByDefault=هل تريد أن تكون البيانات في هذه المساحة قابلة للاستهلاك بشكل افتراضي؟
#XFLD: Business Name
businessNameLabel=الاسم التجاري
#XFLD: Refresh
refresh=تحديث
#XMSG: No filter results title
noFilterResultsTitle=يبدو أن إعدادات التصفية لديك لا تُظهِر أي بيانات.
#XMSG: No filter results message
noFilterResultsMsg=حاول تنقيح إعدادات التصفية لديك وإذا كنت لا تزال لا ترى أي بيانات بعد ذلك؛ قم بإنشاء بعض الجداول في أداة إنشاء البيانات. بمجرد استهلاكها للتخزين، ستتمكن من مراقبتها هنا.
#XMSG: Space monitoring Hana state red title
spaceMonitorHanaRedTitle=قاعدة بيانات وقت التشغيل غير متوفرة.
#XMSG: Space monitoring Hana state red message
spaceMonitorHanaRedMsg=نظرًا لعدم توفر قاعدة بيانات وقت التشغيل، تم تعطيل بعض الميزات ولا يمكننا عرض أي معلومات على هذه الصفحة.
#YMSE: Error while creating space schema user
createSpaceSchemaUserError=تعذر إنشاء مخطط المساحة.
#YMSE: Error User name already exists
userAlreadyExistsError=اسم المستخدم موجود بالفعل.
#YMSE: Error Authentication failed
authenticationFailedError=فشلت المصادقة.
#XMSG: Message strip message when a schema user is locked
userLockedMessageStrip=تم تأمين المستخدم بسبب فشل عدد كبير جدًا من عمليات تسجيل الدخول. يرجى طلب كلمة مرور جديدة لإلغاء تأمين المستخدم.
#XMSG: Success message after reset and unlock schema user
resetUnlockPasswordSuccessMessage=تم تعيين كلمة مرور جديدة وتم إلغاء تأمين المستخدم
#XMSG: user is locked message
userLockedMessage=تم تأمين المستخدم.
#XCOL: Users table-view column Role
spaceRole=الدور
#XCOL: Users table-view column Scoped Role
spaceScopedRole=الدور ذو النطاق
#XCOL: Users table-view column Space Admin
spaceAdmin=مسؤول المساحة
#XFLD: User section dropdown value Viewer
viewer=العارض
#XFLD: User section dropdown value Modeler
modeler=أداة إعداد النماذج
#XFLD: User section dropdown value Data Integrator
dataIntegrator=أداة دمج البيانات
#XFLD: User section dropdown value Space Administrator
spaceAdministrator=مسؤول المساحة
#XMSG: Success message -  Space Role updated successfully.
updateUserSpaceRoleSuccess=تم تحديث دور المساحة
#XMSG: Error message - Failed to update Space Role.
updateUserSpaceRoleError=لم يتم تحديث دور المساحة بنجاح.
#XFLD:
databaseUserNameSuffix=لاحقة اسم مستخدم قاعدة البيانات
#XTXT: Space Schema password text
spaceSchemaPasswordText=لإعداد اتصال بهذا المخطط، يرجى نسخ كلمة المرور الخاصة بك. وفي حالة نسيان كلمة المرور، يمكنك دائمًا طلب كلمة مرور جديدة.
#XFLD: Cloud Platform label in space schema dialog
cloudPlatformLabel=النظام الأساسي السحابي
#XTXT: Cloud Platform text in space schema dialog
cloudPlatformText=لإعداد الوصول من خلال هذا المستخدم، قم بتمكين الاستهلاك ثم نسخ بيانات الاعتماد. في حالة قدرتك فقط على نسخ بيانات الاعتماد دون كلمة مرور، تأكد من إضافة كلمة المرور لاحقًا.
#XFLD: enable consumption in CloudPlatform label in space schema dialog
enableConsumptionInCloudPlatformLabel=تمكين الاستهلاك في النظام الأساسي السحابي
#XFLD: Credentials for User-Provided Service label in space schema dialog
cloudPlatformCredentialsLabel=بيانات اعتماد الخدمة المقدمة بواسطة المستخدم:
#XFLD: Credentials for User-Provided Service (Without Password) label in space schema dialog
cloudPlatformCredentialsWithoutPasswordLabel=بيانات اعتماد الخدمة المقدمة بواسطة المستخدم (دون كلمة مرور):
#XFLD: Copy Credentials label in space schema dialog
copyCredentialsLabel=نسخ بيانات الاعتماد دون كلمة مرور
#XFLD: Copy Credentials label in space schema dialog
copyFullCredentialsLabel=نسخ بيانات الاعتماد بالكامل
#XFLD: Copy Password label in space schema dialog
copyPasswordLabel=نسخ كلمة المرور
#XMSG: Credentials copied to clipboard
credentialsCopiedMessage=تم نسخ بيانات الاعتماد إلى الحافظة
#XMSG: Password copied to clipboard
passwordCopiedMessage=تم نسخ كلمة المرور إلى الحافظة
#XMSG: create database user dialog title
createDatabaseUserDialogTitle=إنشاء مستخدم قاعدة البيانات
#XMSG: Database Users section title
databaseUsers=مستخدمو قاعدة البيانات
#XMSG: Database User Details dialog title
databaseUserDetailsDialogTitle=تفاصيل مستخدم قاعدة البيانات
#XFLD: database user read audit log
databaseUserAuditLogRead=تمكين سجلات التدقيق لعمليات القراءة وإبقاء السجلات من أجل
#XFLD: database user change audit log
databaseUserAuditLogChange=تمكين سجلات التدقيق لعمليات التغييرات وإبقاء السجلات من أجل
#XMSG: Cloud Platform Access
cloudPlatformAccess=الوصول إلى النظام الأساسي السحابي
#XFLD: Enable Consumption in Cloud Platform hint
enableConsumptionCloudPlatformHint=قم بإعداد الوصول إلى حاوية HANA Deployment Infrastructure (HDI) لديك من خلال مستخدم قاعدة البيانات هذا. للاتصال بحاوية HDI لديك، يجب تشغيل إعداد نماذج SQL
#XFLD: Enable HDI Consumption
enableConsumptionCloudPlatform=تمكين استهلاك HDI
#XFLD: Enable Consumption hint
enableConsumptionHint=هل تريد أن تكون البيانات في المساحة لديك قابلة للاستهلاك من جانب أدوات أو تطبيقات أخرى بشكل افتراضي؟
#XFLD: Enable Consumption
enableConsumption=تمكين استهلاك SQL
#XFLD: Enable Modeling
enableModeling=تمكين إعداد نماذج SQL
#XMSG: Privileges for Data Modeling
privilegesModeling=استيعاب البيانات
#XMSG: Privileges for Data Consumption
privilegesConsumption=استهلاك البيانات للأدوات الخارجية
#XFLD: SQL Modeling
sqlModeling=إعداد نماذج SQL
#XFLD: SQL Consumption
sqlConsumption=استهلاك SQL
#XFLD: enabled
enabled=ممكّن
#XFLD: disabled
disabled=معطل
#XFLD: Edit Privileges
editPrivileges=تحرير الامتيازات
#XFLD: Open Database Explorer
openDBX=فتح مستكشف قاعدة البيانات
#XFLD: create database user hint
databaseCreateHint=يرجى ملاحظة أنه لن يكون من الممكن تغيير اسم المستخدم مرة أخرى بعد الحفظ.
#XFLD: Internal Schema Name
internalSchemaName=اسم المخطط الداخلي
#YMSE: Failed to load database users
loadDatabaseUserError=فشل تحميل مستخدمي قاعدة البيانات
#YMSE: Failed to delete database user
deleteDatabaseUsersError=فشل حذف مستخدمي قاعدة البيانات
#XMSG: Success message after deleting database user
deleteDatabaseUserSuccess=تم حذف مستخدم قاعدة البيانات
#XMSG: Success message after deleting database users
deleteDatabaseUsersSuccess=تم حذف مستخدمي قاعدة البيانات
#XMSG: Success message after creating database user
createDatabaseUserSuccessMessage=تم إنشاء مستخدم قاعدة البيانات
#YMSE: Failed to create database user
createDatabaseUserError=فشل إنشاء مستخدم قاعدة البيانات
#XMSG: Success message after updating database user
updateDatabaseUserSuccessMessage=تم تحديث مستخدم قاعدة البيانات
#YMSE: Failed to update database user
updateDatabaseUserError=فشل تحديث مستخدم قاعدة البيانات
#XFLD: HDI Consumption
hdiConsumption=استهلاك HDI
#XTIT: Space detail section Database Access title
detailsSectionDatabaseAccess=الوصول إلى البيانات
#XTXT: Database users consumption hint
databaseUsersConsumptionHint=تأكد من أن بيانات المساحة قابلة للاستهلاك بشكل افتراضي. النماذج في أدوات الإنشاء تسمح تلقائيًا بأن تكون البيانات قابلة للاستهلاك.
#XFLD: Default Consumption of Space Data:
defaultConsumptionOfSpaceData=الاستهلاك الافتراضي لبيانات المساحة:
#XFLD: Database User Name
databaseUserName=اسم مستخدم قاعدة البيانات
#XMSG: Database User creation validation error message
databaseUserValidationError=يبدو أن بعض الحقول ليست صالحة. يرجى التحقق من الحقول المطلوبة للمتابعة.
#XMSG: SQL modeling can’t be enabled since this user has been migrated.
migratedDatabaseUserHint=لا يمكن تمكين استيعاب البيانات نظرًا لترحيل هذا المستخدم.
#XBUT: Remove Button Text
remove=إزالة
#XBUT: Remove Spaces Button Text
removeSpaces=إزالة المساحات
#XBUT: Remove Objects Button Text
removeObjects=إزالة الكائنات
#XMSG: No members have been added yet.
noMembersAssigned=لم تتم إضافة أي أعضاء حتى الآن.
#XMSG: No users have been added yet.
noUsersAssigned=لم تتم إضافة أي مستخدمين حتى الآن.
#XMSG: No database users have been created or your filter is not showing any data.
noDatabaseUsers=لم يتم إنشاء أي مستخدمين لقاعدة البيانات، أو إن عامل التصفية لديك لم يظهر أي بيانات.
#XMSG: Please enter a user name.
noDatabaseUsername=يُرجى إدخال اسم مستخدم.
#XMSG: The user name is too long. Please use a shorter one.
databaseUsernameTooLong=اسم المستخدم طويل للغاية. يرجى استخدام اسم أقصر.
#XMSG: No privileges have been enabled and this database user will have limited functionality. Do you still want to continue?
noPrivilegesConfirmationMessage=لم يتم تمكين أي امتيازات ومستخدم قاعدة البيانات هذا سيكون لديه وظيفة محدودة. هل لا تزال تريد الاستخدام؟
#XMSG: To enable audit logs for change operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogChangeMessage=لتمكين سجلات التدقيق لعمليات التغيير، يجب تمكين استيعاب البيانات أيضًا. هل تريد إجراء هذا الأمر؟
#XMSG: To enable audit logs for read operations data ingestion needs to be enabled. Do you want to enable both?
enableAuditLogReadMessage=لتمكين سجلات التدقيق لعمليات القراءة، يجب تمكين استيعاب البيانات أيضًا. هل تريد إجراء هذا الأمر؟
#XMSG: To enable HDI consumption data ingestion and data consumption needs to be enabled. Do you want to enable all?
enableHDIConsumptionMessage=لتمكين استهلاك HDI، يجب تمكين استيعاب البيانات واستهلاك البيانات أيضًا. هل تريد إجراء هذا الأمر؟
#XMSG:
databaseUserPasswordText=لإعداد اتصال بمستخدم قاعدة البيانات هذا، يرجى نسخ كلمة المرور الخاصة بك. وفي حالة نسيان كلمة المرور، يمكنك دائمًا طلب كلمة مرور جديدة.
#XTIT: Space detail section members title
detailsSectionMembers=الأعضاء
#XMSG: New password set
newPasswordSet=تم تعيين كلمة مرور جديدة
#XFLD: Data Ingestion
dataIngestion=استيعاب البيانات
#XFLD: Data Consumption
dataConsumption=استهلاك البيانات
#XFLD: Privileges
privileges=الامتيازات
#XFLD: Enable Data ingestion
enableDataIngestion=تمكين استيعاب البيانات
#XMSG: Log the read and change operations for data ingestion.
databaseUserAuditHint=قم بتسجيل عمليات القراءة والتغيير لاستيعاب البيانات.
#XMSG: Make your space data available in your HDI containers.
databaseUserHDIHint=اجعل بيانات المساحة لديك متاحة في حاويات HDI.
#XFLD: Enable Data consumption
enableDataConsumption=تمكين استهلاك البيانات
#XMSG: Allow other apps or tools to consume your space data.
enableDataConsumptionHint=اسمح للتطبيقات أو الأدوات الأخرى باستهلاك بيانات المساحة.
#XMSG: To set up access via this schema user copy the credentials to your user-provided service. In case you can only copy the credentials without a password make sure to add the password later.
databaseUserHDIConsumptionHint=لإعداد الوصول من خلال مستخدم قاعدة البيانات هذا، قم بنسخ بيانات الاعتماد إلى الخدمة المقدمة بواسطة المستخدم. في حالة قدرتك فقط على نسخ بيانات الاعتماد دون كلمة مرور، تأكد من إضافة كلمة المرور لاحقًا.
#XFLD: Data Flow Runtime Capacity label in space overview
dataFlowRuntimeCapacity=قدرة وقت التشغيل لتدفق البيانات ({0}:{1} ساعة/ساعات من {2} من الساعات)
#XFLD: Could not load Data Flow Runtime Capacity
dataFlowRuntimeCapacityError=تعذر تحميل قدرة وقت التشغيل لتدفق البيانات
#XMSG: User can grant data consumption to other users.
databaseUserConsumptionWithGrantOptionHint=يمكن للمستخدم منح استهلاك البيانات إلى المستخدمين الآخرين.
#XFLD: Enable data consumption with grant option
enableConsumptionWithGrantOption=تمكين استهلاك البيانات مع خيار المَنح
#XMSG: To enable data consumption with grant option data consumption needs to be enabled. Do you want to enable both?
enableConsumptionWithGrantMessage=لتمكين استهلاك البيانات مع خيار المَنح، يجب تمكين استهلاك البيانات. هل تريد تمكينهما معًا؟
#XFLD: Enable Automated Predictive Library (APL) and Predictive Analysis Library (PAL)
allowUsingScriptServerFunctionality=تمكين المكتبة التنبؤية المؤتمتة (APL) ومكتبة التحليلات التنبؤية (PAL)
#XMSG: User can use SAP HANA Cloud embedded machine learning functions.
allowUsingScriptServerFunctionalityHint=يمكن للمستخدم استخدام وظائف تدريب الآلة المضمَّنة في SAP HANA Cloud.
#XFLD: Password Policy
passwordPolicy=سياسة كلمة المرور
#XMSG: Password Policy
passwordPolicyHint=قم بتمكين أو تعطيل سياسة كلمة المرور المكوَّنة هنا.
#XFLD: Enable Password Policy
enablePasswordPolicy=تمكين سياسة كلمة المرور
#XMSG: Read Access to the Space Schema
readAccessTitle=صلاحية قراءة مخطط المساحة
#XMSG: read access hint
readAccessHint=اسمح لمستخدم قاعدة البيانات بتوصيل الأدوات الخارجية بمخطط المساحة وطرق عرض القراءة المُعرَّضة للاستهلاك.
#XFLD: Space Schema
spaceSchema=مخطط المساحة
#XFLD: Enable Read Access (SQL)
enableReadAccess=تمكين صلاحية القراءة (SQL)
#XMSG: Allow the user to grant read access to other users.
withGrantOptionHint=اسمح للمستخدم بمنح صلاحية القراءة إلى المستخدمين الآخرين.
#XFLD: With Grant Option
withGrantOption=مع خيار المَنح
#XMSG: Make your space data available in your HDI containers.
enableHDIConsumptionHint=اجعل بيانات المساحة لديك متاحة في حاويات HDI.
#XFLD: Enable HDI Consumption
enableHDIConsumption=تمكين استهلاك HDI
#XMSG: Write Access to the User’s Open SQL Schema
writeAccessTitle=صلاحية كتابة مخطط SQL المفتوح للمستخدم
#XMSG: write access hint
writeAccessHint=اسمح لمستخدم قاعدة البيانات بتوصيل الأدوات الخارجية بمخطط SQL المفتوح للمستخدم من أجل إنشاء كيانات بيانات واستيعاب البيانات للاستخدام في المساحة.
#XFLD: Open SQL Schema
openSQLSchema=مخطط SQL المفتوح
#XFLD: Enable Write Access (SQL DDL & DML)
enableWriteAccess=تمكين صلاحية الكتابة (SQL وDDL وDML)
#XMSG: audit hint
auditHint=قم بتسجيل عمليات القراءة والتغيير في مخطط SQL المفتوح.
#XMSG: data consumption hint
dataConsumptionHint=قم بتعريض كل طرق العرض الجديدة في المساحة للاستهلاك بشكل افتراضي. يمكن لأدوات إعداد النماذج تجاوز هذا الإعداد لطرق العرض الفردية عبر مفتاح "تعريض للاستهلاك" في اللوحة الجانبية لمخرجات طريقة العرض. كما يمكنك اختيار التنسيقات التي يتم بها تعريض طرق العرض.
#XFLD: Expose for Consumption by Default
exposeDataConsumption=تعريض للاستهلاك بشكل افتراضي
#XMSG: database users hint consumption hint
databaseUsersHint2New=قم بإنشاء مستخدمي قاعدة البيانات لتوصيل الأدوات الخارجية إلى SAP Datasphere. قم بتعيين الامتيازات للسماح للمستخدمين بقراءة بيانات المساحة وبإنشاء كيانات البيانات (DDL) واستيعاب البيانات (DML) من أجل الاستخدام داخل المساحة.
#XFLD: Read
read=قراءة
#XFLD: Read (HDI)
readHDI=قراءة (HDI)
#XFLD: Write
write=كتابة
#XMSG: HDI Containers Hint
HDIContainersHint2=قم بتمكين الوصول إلى حاويات SAP HANA Deployment Infrastructure (HDI) في المساحة لديك. يمكن لأدوات إعداد النماذج استخدام نماذج HDI كمصادر لطرق العرض ويمكن لعملاء HDI الوصول إلى بيانات المساحة لديك.
#XMSG: Open info dialog
openDatabaseUserInfoDialog=افتح مربع حوار المعلومات
#XMSG: Database user is locked. Open info dialog to unlock
openLockedDatabaseUserInfoDialog=تم تأمين مستخدم قاعدة البيانات. افتح مربع الحوار لإلغاء التأمين
#XFLD: Table
table=الجدول
#XFLD: Space section partner configuration title
partnerConnectionConfigurationSectionTitle=اتصال الشريك
#XFLD: Space section partner configuration subtitle
partnerConnectionConfigurationSectionSubTitle=تكوين اتصال الشريك
#XFLD: Space section partner configuration hint
partnerConnectionConfigurationSectionHint=تحديد إطار اتصال الشريك الخاص بك بإضافة الأيقونة والرابط iFrame. يتوفر هذا التكوين فقط لهذه الوحدة المستضافة.
#XFLD: Table Name Field
partnerConnectionConfigurationName=اسم الإطار
#XFLD: Table iFrame URL field
partnerConnectionConfigurationiFrameURL=رابط iFrame
#XFLD: Table iFrame Post Message Origin field
partnerConnectionConfigurationiFramePostMessageOrigin=أصل رسالة نشر iFrame
#XFLD: Table icon/image name
partnerConnectionConfigurationIcon=الأيقونة
#XMSG: Table no result found
partnerConnectionConfigurationNotfound=لا يمكن العثور على تكوين (تكوينات) لاتصال شريك.
#XMSG: Partner Connection Confgurations are not available
partnerConnectionConfigurationNotAvailable=لا يمكن عرض تكوينات اتصال الشريك عندما تكون قاعدة بيانات وقت التشغيل غير متوفرة.
#XFLD: Dialog Create Partner Connection Configuration
partnerConnectionConfigurationDialogTitleAdd=إنشاء تكوين اتصال الشريك
#XFLD: Dialog Create Partner Connection Configuration Upload Icon
partnerConnectionConfigurationButtonUloadPartnerIcon=تحميل الأيقونة
#XFLD: Dialog Create Partner Connection Configuration Select
partnerConnectionConfigurationSelectIcon=تحديد (الحد الأقصى للحجم 200 كيلو بايت)
#XFLD: Dialog Create Partner Connection Configuration Enter a name
partnerConnectionConfigurationNamePlaceholder=نموذج إطار الشريك
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL
partnerConnectionConfigurationiFrameURLPlaceholder=http://www.example.com
#XFLD: Dialog Create Partner Connection Configuration Enter a iFrame URL Check
partnerConnectionConfigurationiFramePostMessageOriginPlaceholder=.example.com
#XFLD: Dialog Create Partner Connection Configuration Browse
partnerConnectionConfigurationBrowseButton=استعراض
#XFLD: Create Partner Connection Configuration Msg
partnerConnectionConfigurationUploadMsg=تم إنشاء تكوين اتصال الشريك بنجاح.
#XMSG: Error while deleting Partner Connection configuration(s)
partnerConnectionConfigurationDeleteError=حدث خطأ أثناء حذف تكوين (تكوينات) اتصال الشريك.
#XMSG: Partner Connection Configuration has been successfully deleted
partnerConnectionConfigurationDeleteSuccess=تم حذف تكوين اتصال الشريك بنجاح.
#XMSG: Error while retrieving Partner Connection Configurations
partnerConnectionConfigurationLoadError=حدث خطأ أثناء استرجاع تكوينات اتصال الشريك.
#XMSG: Error fileSizeLimitExceeded
partnerConnectionConfigurationfileSizeLimitExceeded=تعذر تحميل الملف لأنه يتجاوز الحد الأقصى للحجم المقدر بـ 200 كيلو بايت.
#XBUT: Create Partner Connection Configuration
partnerConnectionConfigurationButtonCreate=إنشاء تكوين اتصال الشريك
#XBUT: Delete Partner Connection Configuration
partnerConnectionConfigurationButtonDelete=حذف تكوين اتصال الشريك.
#XMSG: Partner Tile Creation Failure
upsertPartnerTileFailed=تعذر إنشاء إطار الشريك.
#XMSG: Partner Tile Deletion Failure
deletePartnerTileFailed=تعذر حذف إطار الشريك.
#XMSG: HANA cloud connector settings reset failure
resetHANASCCSettingsFailed=فشلت إعادة تعيين إعدادات الرابط السحابي لنظام SAP HANA للعميل
#XFLD: Workload Class
workloadClass=صنف حمل العمل
#XFLD: Workload Management
workloadManagement=إدارة حمل العمل
#XFLD: Priority
workloadClassPriority=الأفضلية
#XMSG:
workloadManagementPriorityHint=يمكنك تحديد أفضلية هذه المساحة عند الاستعلام عن قاعدة البيانات. أدخل قيمة من 1 (أقل أفضلية) إلى 8 (أعلى أفضلية). في الموقف الذي تتنافس فيه المساحات على سلاسل الرسائل المتوفرة، يتم تشغيل تلك ذات الأفضلية الأعلى قبل المساحات ذات الأفضلية الأقل.
#XMSG:
workloadClassPriorityHint=يمكنك تحديد أفضلية المساحة من 0 (أقل أفضلية) إلى 8 (أعلى أفضلية). يتم تنفيذ عبارات المساحة ذات الأفضلية العالية قبل عبارات المساحات الأخرى ذات الأفضلية الأقل، علمًا أن الأفضلية الافتراضية هي 5، وبما أن القيمة 9 محجوزة لعمليات النظام، فلا تتوفر لإحدى المساحات.
#XFLD: Statement Limits
workloadclassStatementLimits=حدود العبارة
#XFLD: Workload Configuration
workloadConfiguration=تكوين حمل العمل
#XMSG:
workloadClassStatementLimitsHint=يمكنك تحديد الحد الأقصى للعدد (أو النسبة المئوية) لوحدات العمليات ووحدات الجيجا بايت للذاكرة التي يمكن استهلاكها بواسطة العبارات التي يجري تنفيذها بشكل متزامن في المساحة. يمكنك إدخال أي قيمة أو نسبة مئوية بين 0 (بدون حد) وإجمالي الذاكرة ووحدات العمليات المتوفرة في الوحدة المستضافة. \n\n إذا حددتَ حدًا لوحدة عملية، فاعلم أنه يمكن أن يؤدي إلى خفض معدل الأداء. \n\n وإذا حددت حدًا لذاكرة، فلن يتم تنفيذ العبارات التي تصل إلى حد الذاكرة.
#XMSG:
workloadClassStatementLimitsDescription=يوفر التكوين الافتراضي حدودًا كبيرة للموارد، مع منع أي مساحة فردية من التحميل الزائد على النظام.
#XMSG:
workloadClassStatementLimitCustomDescription=يمكنك تعيين الحد الأقصى لإجمالي حدود الذاكرة وسلاسل الرسائل التي يمكن أن تستهلكها العبارات التي يتم تشغيلها بشكل متزامن في المساحة.
#XMSG:
totalStatementThreadLimitHelpText=قد يؤثر تعيين حد سلسلة الرسائل المنخفض جدًا على أداء العبارة، بينما قد تسمح القيم العالية جدًا أو 0 للمساحة باستهلاك جميع سلاسل الرسائل المتوفرة للنظام.
#XMSG:
totalStatementMemoryLimitHelpText=قد يؤدي تعيين حد الذاكرة المنخفض جدًا إلى حدوث مشكلات بسبب نفاد الذاكرة، بينما قد تسمح القيم العالية جدًا أو 0 للمساحة باستهلاك كل ذاكرة النظام المتوفرة.
#XMSG:
totalStatementThreadLimitHelpTextRestricted=أدخل نسبة مئوية تتراوح بين 1٪ و70٪ (أو الرقم المكافئ) من إجمالي عدد سلاسل الرسائل المتوفرة في الوحدة المستضافة الخاصة بك. قد يؤثر تعيين حد مؤشر سلسلة الرسائل منخفضًا جدًا على أداء العبارة، بينما قد تؤثر القيم المرتفعة بشكل مفرط على أداء العبارات في المساحات الأخرى.
#XMSG:
totalStatementThreadLimitHelpTextDynamic=أدخل نسبة مئوية بين 1% و{0}% (أو الرقم المكافئ) من إجمالي عدد وحدات العمليات المتوفرة في وحدتك المستضافة. قد يؤثر تعيين حد سلسلة الرسائل المنخفض جدًا على أداء العبارة، بينما قد تؤثر القيم المرتفعة على أداء العبارات في المساحات الأخرى.
#XMSG:
totalStatementMemoryLimitHelpTextNew=أدخل قيمة أو نسبة مئوية بين 0 (دون حد) وإجمالي حجم الذاكرة المتوفرة في الوحدة المستضافة الخاصة بك. قد يؤثر تعيين حد الذاكرة على منخفض جدًا على أداء البيان، بينما قد تؤثر القيم المرتفعة جدًا على أداء العبارات في المساحات الأخرى.
#XFLD: Total Statement Thread Limit
totalStatementThreadLimit=إجمالي حد وحدات عملية العبارات
#XFLD: Segmented button label
workloadclassPercentage=%
#XFLD: Segmented button label
workloadclassGigaByte=جيجا بايت
#XFLD, 80: Segmented button label
workloadclassThreads=سلاسل
#XFLD: Total Statement Memory Limit
totalStatementMemoryLimit=إجمالي حد ذاكرة العبارة
#YMSE: Error while loading customer hana info
loadCustomerHanaInfoError=فشل تحميل معلومات SAP HANA الخاصة بالعميل.
#XMSG:
minimumLimitReached=تم الوصول إلى الحد الأدنى.
#XMSG:
maximumLimitReached=تم الوصول إلى الأقصى.
#XMSG: Name Taken for Technical Name
technical-name-taken=يوجد بالفعل اتصال بالاسم التقني الذي أدخلته. يُرجى إدخال اسم آخر.
#XMSG: Name Too long for Technical Name
technical-name-too-long=يتجاوز الاسم التقني الذي أدخلته 40 حرفًا. برجاء إدخال اسم بحروف أقل.
#XMSG: Technical name field empty
technical-name-field-empty=يُرجى إدخال اسم تقني.
#XMSG: Generic error when user enters invalid characters for a field
field-invalid-characters=لا يمكنك استخدام سوى الحروف (a-z) والأرقام (0-9) والشرطات السفلية (_) للاسم.
#XMSG: Generic error when user enters invalid initial characters for a field
field-underscore-trim=لا يمكن أن يبدأ الاسم الذي تقوم بإدخاله بشرطة سفلية (_) أو ينتهي بها.
#XFLD: Checkbox label for Enable Statement Limits
enableStatementLimits=تمكين حدود العبارة
#XFLD: Connection SubSection Settings Title in space details
connectionSettings=الإعدادات
#XMSG: Connections tool hint in Space details section
connectionsToolHint=لإنشاء اتصالات أو فتحها، افتح تطبيق الاتصالات من التنقل الجانبي أو انقر هنا:
#XBUT: Connection list Open Connection Tool Button
button-connectiontool=انتقال إلى الاتصالات
#XFLD: Not deployed label on space tile
notDeployedLabel=لم يتم نشر المساحة حتى الآن.
#XFLD: Not deployed additional text on space tile
notDeployedText=يُرجى نشر المساحة.
#XFLD: Corrupt space label on space tile
corruptSpace=حدث خطأ ما.
#XFLD: Corrupt space additional text on space tile
corruptSpaceHint=حاول إعادة النشر أو الاتصال بالدعم
#XFLD: ResourceCategory Audit logs
ResourceCategoryAudit=بيانات سجل التدقيق
#XFLD: ResourceCategory DWC global
ResourceCategoryDWCGlobal=البيانات الإدارية
#XFLD: ResourceCategory Outside of DWC
ResourceCategoryOutsideDWC=بيانات أخرى
#XFLD: ResourceCategory Spacerelated
ResourceCategorySpace=البيانات في المساحات
#XMSG: Unlock Space confirmation text
unlockSpaceConfirmation=هل تريد إلغاء تأمين المساحة بالتأكيد؟
#XMSG: Lock Space confirmation text
lockSpaceConfirmation=هل تريد تأمين المساحة بالتأكيد؟
#XFLD: Lock
lock=تأمين
#XFLD: Unlock
unlock=إلغاء تأمين
#XFLD: Locking
locking=جارٍ تأمين
#XMSG: Success message after locking space
lockSpaceSuccessMsg=تم تأمين المساحة
#XMSG: Success message after unlocking space
unlockSpaceSuccessMsg=تم إلغاء تأمين المساحة
#XMSG: Success message after locking spaces
lockSpacesSuccessMsg=تم تأمين المساحات
#XMSG: Success message after unlocking spaces
unlockSpacesSuccessMsg=تم إلغاء تأمين المساحات
#YMSE: Error while locking a space
lockSpaceError=لا يمكن تأمين المساحة.
#YMSE: Error while unlocking a space
unlockSpaceError=لا يمكن إلغاء تأمين المساحة.
#XTIT: popup title Warning
confirmationWarningTitle=تحذير
#XMSG: Message strip message when a space was locked manually
manualLockedMessage=تم تأمين المساحة يدويًا.
#XMSG: Message strip message when a space was locked due to the amount of generated audit logs
auditLogLockedMessage=تم تأمين المساحة بواسطة النظام لأن سجلات التدقيق تستهلك كمية كبيرة من الجيجابايت من القرص.
#XMSG: Message strip message when a space was locked because the assigned quota was exceeded.
quotaLockedMessage=تم تأمين المساحة بواسطة النظام لأنها تتجاوز تخصيصاته للذاكرة أو تخزين القرص.
#XMSG: Unlock Spaces confirmation text
unlockSpacesConfirmation=هل تريد إلغاء تأمين المساحات المحددة بالتأكيد؟
#XMSG: Lock Spaces confirmation text
lockSpacesConfirmation=هل تريد تأمين المساحات المحددة بالتأكيد؟
#XTIT: Scoped role editor dialog title
scopedRoleEditorDialogTitle=محرر الأدوار ذو النطاق
#XTIT: ECN Management title
ecnManagementTitle=إدارة عُقد الحوسبة المرنة والمساحة
#XFLD: ECNs
ecns=عُقد الاحتساب المرنة
#XFLD: ECN phase Ready
ecnReady=جاهز
#XFLD: ECN phase Running
ecnRunning=قيد التشغيل
#XFLD: ECN phase Initial
ecnInitial=غير جاهز
#XFLD: ECN phase Starting
ecnStarting=بدء
#XFLD: ECN phase Starting Failed
ecnStartingFailed=فشل البدء
#XFLD: ECN phase Stopping
ecnStopping=جارٍ الإيقاف
#XFLD: ECN phase Stopping Failed
ecnStoppingFailed=فشل الإيقاف
#XBTN: Assign Button
assign=تعيين المساحات
#XBTN: Start Header-Button
start=بدء
#XBTN: Update Header-Button
repair=تحديث
#XBTN: Stop Header-Button
stop=إيقاف
#XFLD: ECN hours remaining
ecnHoursRemaining=يتبقى 1000 ساعة
#XFLD: ECN block-hours remaining
ecnBlockHoursRemaining=يتبقى {0} من ساعات مجموعة الاحتساب
#XFLD: ECN block-hour remaining
ecnBlockHourRemaining=يتبقى {0} ساعة مجموعة احتساب
#XTIT: ECN creation dialog title
ecnCreationDialogTitle=إنشاء عقدة الاحتساب المرنة
#XTIT: ECN edit dialog title
ecnEditDialogTitle=تحرير عقدة الاحتساب المرنة
#XTIT: ECN deletion dialog title
ecnDeletionDialogTitle=حذف عقدة الاحتساب المرنة
#XTIT: ECN Space assignment dialog title
ecnSpaceAssignmentDialogTitle=تعيين المساحات
#XFLD: ECN ID
ECNIDLabel=عقدة الاحتساب المرنة
#XTXT: Selected toolbar text
selectedToolbarText=المحدد: {0}
#XTIT: Elastic Compute Nodes
ECNslong=عُقد الاحتساب المرنة
#XTIT: Object assignment - Table header: Objects available
objectsAvailable=عدد الكائنات
#XTIT: Object assignment - Dialog header text
selectObjects=حدد المساحات والكائنات التي تريد تعيينها إلى عقدة الاحتساب المرنة لديك:
#XTIT: Object assignment - Table header title: Objects
objects=الكائنات
#XTIT: Object assignment - Table header: Type
type=النوع
#XMSG: Message strip delete database user audit warning
deleteDatabaseUserWarningMessageStrip=اعلم أن حذف مستخدم قاعدة البيانات سيؤدي إلى حذف جميع إدخالات سجل التدقيق المنشأة. إذا كنت تريد الاحتفاظ بسجلات التدقيق، فعليك مراعاة تصديرها قبل حذف مستخدم قاعدة البيانات.
#XMSG: Message strip delete HDI container audit logs impact
deleteHDIContainerWarningMessageStrip=اعلم أن إلغاء تعيين حاوية HDI من المساحة سيؤدي إلى حذف جميع إدخالات سجل التدقيق المنشأة. إذا كنت تريد الاحتفاظ بسجلات التدقيق، فعليك مراعاة تصديرها قبل إلغاء تعيين حاوية HDI.
#XTXT: All audit logs
allAuditLogs=تم إنشاء كل إدخالات سجل التدقيق للمساحة
#XMSG: Message strip disable audit warning
disableAuditWarningMessageStrip=اعلم أن تعطيل سياسة التدقيق (عمليات القراءة أو التغيير) سيؤدي إلى حذف جميع إدخالات سجل التدقيق الخاصة بها. إذا كنت تريد الاحتفاظ بإدخالات سجل التدقيق، فعليك مراعاة تصديرها قبل تعطيل سياسة التدقيق.
#XTIT: No Spaces assigned to ECN Illustration title
noSpacesECNTitle=لم يتم تعيين أي مساحات أو كائنات حتى الآن
#XTIT: No Spaces assigned to ECN Illustration description
noSpacesECNDescription=لبدء العمل مع عقدة الحوسبة المرنة، قم بتعيين مساحة أو كائنات لها.
#XTIT: No Spaces Illustration title
noSpacesTitle=لم يتم إنشاء أي مساحة حتى الآن
#XTIT: No Spaces Illustration description
noSpacesDescription=لبدء الحصول على البيانات، قم بإنشاء مساحة.
#XTIT: No Spaces Illustration title
noDeletedSpacesTitle=سلة المحذوفات فارغة
#XTIT: No Spaces Illustration description
noDeletedSpacesDescription=يمكنك استعادة المساحات المحذوفة من هنا.
#XTXT:  {0} is always filled with value “permanently”. This value is translated in prior string (string ID=permanently).
deleteDatabaseUsers=بمجرد نشر المساحة، سيتم حذف مستخدمي قاعدة البيانات التالية {0} ولا يمكن استردادهم:
#XTIT: Delete database users
deleteDatabaseUsersTitle=حذف مستخدمي قاعدة البيانات
#YMSE: Error if ecn id already exists
ecnIDInputDuplicateError=المعرف موجود بالفعل.
#YMSE: Error if ecn id contains invalid characters
ecnIDInputInvalidError=يرجى استخدام الحروف الصغيرة من a إلى z والأرقام من 0 إلى 9 فقط
#YMSE: Error if ecn id is too short
ecnIDInputTooShortError=يجب أن يتكون المعرف من {0} أحرف على الأقل.
#XMSG: ecn id length warning
ecnIdLengthWarning=تم تجاوز الحد الأقصى المكون من {0} من الحروف.
#XFLD: open System Monitor
systemMonitor=مراقبة النظام
#XFLD: open ECN schedule dialog menu entry
schedule=الجدول الزمني
#XFLD: open create ECN schedule dialog
createSchedule=إنشاء جدول زمني
#XFLD: open change ECN schedule dialog
changeSchedule=تحرير الجدول الزمني
#XFLD: open delete ECN schedule dialog
deleteSchedule=حذف الجدول الزمني
#XFLD: open self assign ECN schedule dialog
selfAssignSchedule=تعيين الجدول لي
#XFLD: open pause ECN schedule dialog
pauseSchedule=إيقاف مؤقت للجدول
#XFLD: open resume ECN schedule dialog
resumeSchedule=استئناف الجدول
#XFLD: View Logs
viewLogs=عرض السجلات
#XFLD: Compute Blocks
computeBlocks=مجموعات الاحتساب
#XFLD: Memory label in ECN creation dialog
ecnMemory=الذاكرة (جيجا بايت)
#XFLD: Storage label in ECN creation dialog
ecnStorage=التخزين (جيجا بايت)
#XFLD: Number of CPU label in ECN creation dialog
ecnCPUCount=رقم وحدة المعالجة المركزية
#XFLD: ECN updated by label
changedBy=تم التغيير بواسطة
#XFLD: ECN updated on label
changedOn=تاريخ التغيير
#XMSG: Success message after creating a Elastic Compute Node
createEcnSuccessMessage=تم إنشاء عقدة الاحتساب المرنة
#YMSE: Error while creating a Elastic Compute Node
createEcnError=تعذر إنشاء عقدة الاحتساب المرنة
#XMSG: Success message after updating a Elastic Compute Node
updateEcnSuccessMessage=تم تحديث عقدة الاحتساب المرنة
#XMSG: Error while updating a Elastic Compute Node
udpateEcnError=تعذر تحديث عقدة الاحتساب المرنة
#XMSG: Success message after deleting a Elastic Compute Node
deleteEcnSuccessMessage=تم حذف عقدة الاحتساب المرنة
#YMSE: Error while deleting a Elastic Compute Node
deleteEcnError=تعذر حذف عقدة الاحتساب المرنة
#XMSG: Success message after starting a Elastic Compute Node
startingEcnSuccessMessage=جارٍ بدء عقدة الاحتساب المرنة
#XMSG: Success message after stopping a Elastic Compute Node
stoppingEcnSuccessMessage=إيقاف عقدة الاحتساب المرنة
#YMSE: Error while starting a Elastic Compute Node
startingEcnError=تعذر بدء عقدة الاحتساب المرنة
#YMSE: Error while stopping a Elastic Compute Node
stoppingEcnError=تعذر إيقاف عقدة الاحتساب المرنة
#XBUT: Add Object button for an ECN
assignObjects=إضافة الكائنات
#XFLD: Enable auto object assignment label
enableAutoObjAssignment=إضافة جميع الكائنات تلقائيًا
#XFLD: object type label to be assigned
objectTypeLabel=النوع (استخدام دلالي)
#XFLD: assigned object type label
assignedObjectTypeLabel=النوع
#XFLD: technical name label
TechnicalNameLabel=الاسم التقني
#XFLD: Object selecttion ECN label
objectSelectionECNLabel=حدد الكائنات التي تريد إضافتها إلى عقدة الاحتساب المرنة
#XTIT: Add objects dialog title
assignObjectsTitle=تعيين كائنات
#XFLD: object label with object count
objectLabel=الكائن
#XMSG: No objects available to add message.
noObjectsToAssign=لا تتوفر أي كائنات للتعيين.
#XMSG: No objects assigned message.
noAssignedObjects=لم يتم تعيين أي كائنات.
#XTIT: Remove objects popup title
removeObjectsOrSpacesConfirmationTitle=تحذير
#XTIT: Delete ECN popup button and title
deleteECNConfirmationButtonAndTitle=حذف
#XMSG: Remove objects popup text
removeObjectsConfirmation=هل تريد بالفعل إزالة الكائنات المحددة؟
#XMSG: Remove spaces popup text
removeSpacesConfirmation=هل تريد بالتأكيد إزالة المساحات المحددة؟
#XBUT: Confirmation button for objects dialog
removeObjectsOrSpacesConfirmationBtn=إزالة المساحات
#XMSG: Success message after removing objects
removeObjectsSuccessMessage=تمت إزالة الكائنات المعروضة
#XMSG: Success message after assigning objects
assignObjectsSuccessMessage=تم تعيين الكائنات المعروضة
#XFLD: auto object assignment label for space tile
autoObjAssignmentLabel=كل الكائنات المعروضة
#XFLD: Spaces tab label
spacesTabLabel=المساحات
#XFLD: Exposed objects tab label
consumptionObjectsTabLabel=الكائنات المعروضة
#XMSG: Success message after unassigning spaces
spacesUnassignedSuccessMessage=تمت إزالة المساحات
#XMSG: Success message after unassigning space
spaceUnassignedSuccessMessage=تمت إزالة المساحة
#YMSE: Error while assigning / unassigning spaces
spacesAssignUnassignError=تعذر تعيين المساحات أو إزالتها.
#YMSE: Error while removing objects
removeObjectsError=تعذر علينا تعيين الكائنات أو إزالتها.
#YMSE: Error while removing object
removeObjectError=تعذر علينا تعيين الكائن أو إزالته.
#YMSE: Error when saving the ecn with invalid value
invalidSizingPlanError=الرقم الذي تم تحديده مسبقًا لم يعد صالحًا. يرجى تحديد رقم صالح.
#YMSE: Error when saving the ecn with invalid value
invalidPerformanceClassError=يرجى تحديد صف أداء صالح.
#YMSE: Error when saving the ecn with invalid value
performanceClassNotAvailableError=صنف الأداء "{0}" المحدد مسبقًا غير صالح حاليًا. يُرجى تحديد صنف أداء صالح.
#XTXT: Delete ECN confirmation text
deleteConfirmationTxtECN=هل تريد بالتأكيد حذف عقدة الاحتساب المرنة؟
#XFLD: tooltip for ? button
help=مساعدة
#XFLD: ECN edit button label
editECN=تكوين
#XFLD: Technical type label for ERModel
DWC_ERMODEL=الكيان - نموذج العلاقة
#XFLD: Technical type label for Local Table
DWC_LOCAL_TABLE=الجدول المحلي
#XFLD: Technical type label for Remote Table
DWC_REMOTE_TABLE=الجدول البعيد
#XFLD: Technical type label for Analytic Model
DWC_ANALYTIC_MODEL=النموذج التحليلي
#XFLD: Technical type label for Task Chain
DWC_TASKCHAIN=سلسلة المهام
#XFLD: Technical type label for Data Flow
DWC_DATAFLOW=تدفق البيانات
#XFLD: Technical type label for Replication Flow
DWC_REPLICATIONFLOW=تدفق النسخ المتماثل
#XFLD: Technical type label for Transformation Flow
DWC_TRANSFORMATIONFLOW=تدفق التحويل
#XFLD: Technical type label for Intelligent Lookup
DWC_IDT=البحث الذكي
#XFLD: Technical type label for Repository
DWC_REPOSITORY_PACKAGE=المستودع
#XFLD: Technical type label for Enterprise Search
DWC_ESMODEL=البحث داخل المؤسسة
#XFLD: Technical type label for View
DWC_VIEW=العرض
#XFLD: Technical type label for Data Product
DWC_DATAPRODUCT=البيانات - المنتج
#XFLD: Technical type label for Data Access Control
DWC_DAC=التحكم في الوصول إلى البيانات
#XFLD: Technical type label for Folder
DWC_FOLDER=المجلد
#XFLD: Technical type label for Data Entity
DWC_BUSINESS_ENTITY=كيان الأعمال
#XFLD: Technical type label for Business Entity Variant
DWC_BUSINESS_ENTITY_VARIANT=متغير كيان الأعمال
#XFLD: Technical type label for Responsibility Scenario
DWC_AUTH_SCENARIO=سيناريو المسؤولية
#XFLD: Technical type label for Fact Model
DWC_FACT_MODEL=نموذج الحقائق
#XFLD: Technical type label for Transformation Flow
DWC_PERSPECTIVE=المنظور
#XFLD: Technical type label for Consumption Model
DWC_CONSUMPTION_MODEL=نموذج الاستهلاك
#XFLD: Technical type label for Remote Connection
DWC_REMOTECONNECTION=الاتصال عن بُعد
#XFLD: Technical type label for Fact Model Variant
DWC_FACT_MODEL_VARIANT=متغير نموذج الحقائق
#XMSG: Schedule created alert message
createScheduleSuccess=تم إنشاء الجدول الزمني
#XMSG: Schedule updated alert message
updateScheduleSuccess=تم تحديث الجدول الزمني
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=تم حذف الجدول الزمني
#XMSG: Schedule takeover success message
ownerChangeScheduleSuccess=تم تعيين الجدول لك
#XMSG: Message for starting pausing of schedule
pauseScheduleStarted=جارٍ الإيقاف المؤقت لجدول زمني واحد
#XMSG: Message for starting resuming of schedule
resumeScheduleStarted=جارٍ استئناف جدول زمني واحد
#XFLD: Segmented button label
availableSpacesButton=متوفر
#XFLD: Segmented button label
selectedSpacesButton=محدد
#XFLD: Visit website button text
visitWebsite=زيارة موقع الويب
#XMSG: Warning message on unselecting source language
sourceLanguageRemovalWarning=ستتم إزالة لغة المصدر المحددة مسبقًا.
#XBUT: enable translation confirmation button
enableTranslationConfirmationBtn=تمكين
#XFLD: ECN performance class label
performanceClassLabel=صف الأداء
#XTXT performance class memory text
memoryText=الذاكرة
#XTXT performance class compute text
computeText=حوسبة
#XTXT performance class high-compute text
highComputeText=حوسبة مرتفعة
#XBUT: Recycle Bin Button Text
recycleBin=سلة المحذوفات
#XBUT: Restore Button Text
restore=استعادة
#XMSG: Warning message for new Workload Management UI
priorityWarning=هذه المنطقة للقراءة فقط. يمكنك تغيير أفضلية المساحة في منطقة إدارة النظام / التكوين / حمل العمل.
#XMSG: Warning message for new Workload Management UI
workloadWarning=هذه المنطقة للقراءة فقط. يمكنك تغيير تكوين حِمل عمل المساحة في منطقة إدارة النظام / التكوين / حمل العمل.
#XCOL: Space table-view column: Apache Spark vCPUs
sparkManagementVCPUs=وحدات المعالجة المركزية الافتراضية لـ Apache Spark
#XCOL: Space table-view column: Apache Spark Memory (GB)
sparkManagementMemory=ذاكرة Apache Spark (جيجا بايت)
#XCOL: Space type (from Space Capability) for Data Ingestion Spaces
ingestionSpaceType=استيعاب منتج البيانات
#XFLD: Deployment in progress nodatatext for spark table
sparkTableDeploymentInProgress=لا تتوفر بيانات لأن المساحة قيد النشر حاليًا
#XFLD: Runtime data being loaded nodatatext for spark table
sparkTableDeploymentLoading=لا تتوفر أي بيانات نظرًا لأن المساحة قيد التحميل حاليًا
#XCOL: Space type (from Space Capability) for BW Private Cloud Edition
BWPCESpaceType=SAP BW
#XBUT: Go to Instance Mappings Button
navToMappingsButton=تحرير عمليات ربط النُسخ
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB
