/** @format */

import { State } from "@sap/dwc-circuit-breaker";
import {
  EcnPhaseSnapshotText,
  IECNInstance,
  IECNMetadataSpaceAsgmtStrtg,
  IECNMetadataSpaceObject,
  IEcnElasticityReadinessResponse,
} from "../../../../shared/ecn/ecnMetadataTypes";
import { PerformanceClass } from "../../../../shared/provisioning/ftc/types";
import { ResourceConsumption, SpaceStatus } from "../../../../shared/spaces/types";
import { BaseControllerClass } from "../../basecomponent/controller/BaseController.controller";
import { SpacesType } from "../../reuse/utility/Constants";
import { isRuntimeAvailable } from "../../reuse/utility/Runtime";
import { ObjectStatus } from "../../reuse/utility/Types";
import { ShellContainer } from "../../shell/utility/Container";
import { UISpaceCapabilities } from "../../shell/utility/UISpaceCapabilities";
import { User } from "../../shell/utility/User";
import { ManageSpacesClass } from "../controller/overview/ManageSpaces.controller";
import { SpacesSidePanelClass } from "../controller/overview/SpacesSidePanel.controller";
import { Space } from "../model/Space";
import { SpacesModel } from "../model/SpacesModel";
import { NOT_AVAILABLE, NOT_INITIALIZED } from "../utility/Constants";
import { ECNStatus, SpacesModelDataType } from "../utility/Enums";
import { getPercent, showLoadErrorMessage } from "../utility/ManageSpacesUtility";
import { SpaceDetailsFormatter } from "./SpaceDetailsFormatter";
import { SpacesFormatter } from "./SpacesFormatter";
export class SpacesOverviewFormatter extends SpacesFormatter {
  public static hasNoSpaceQuota(this: ManageSpacesClass, ramAssigned: string, storageAssigned: string) {
    const ram = parseInt(ramAssigned, 10);
    const disk = parseInt(storageAssigned, 10);
    return ram === 0 && disk === 0;
  }

  public static shouldStorageUtilizationBarBeVisible(
    this: ManageSpacesClass,
    assignedValue: number,
    otherAssignedValue: number
  ) {
    return !SpacesOverviewFormatter.hasNoSpaceQuota.apply(this, [assignedValue, otherAssignedValue]);
  }

  public static shouldSpaceTileContentBeVisible(this: ManageSpacesClass, controlId: string, deployment_status: string) {
    const deploymentStatus: number = parseInt(deployment_status, 10);
    const isDeployed = SpacesOverviewFormatter.isDeployed.apply(this, [deployment_status]);
    switch (controlId) {
      case "deployedContent":
        return !isNaN(deploymentStatus) && isDeployed;
      case "notDeployedContent":
        return !isNaN(deploymentStatus) && !isDeployed;
      case "brokenContent":
        return isNaN(deploymentStatus) && !isDeployed;
      default:
        return false;
    }
  }

  public static getTagText(this: ManageSpacesClass, spaceType: string, spaceId: string) {
    return SpaceDetailsFormatter.formatSpaceTypeText.apply(this, [spaceType as SpacesType, spaceId]);
  }

  public static getSpaceObjectStatusText(this: ManageSpacesClass, status: SpaceStatus): string {
    switch (status) {
      case SpaceStatus.Ok:
      case SpaceStatus.Cold:
      case SpaceStatus.Critical:
      case SpaceStatus.Locked:
      case SpaceStatus.Deleted:
        return this.getText(`${status}Label`);
      default:
        return this.getText("unknownLabel");
    }
  }

  // todo rename
  public static getSpaceHealthColor(health = "unknown", hanaState: State): string {
    return !health || !isRuntimeAvailable() ? "unknown" : health;
  }

  // todo rename
  public static formatPercentage(this: BaseControllerClass, value: number, hanaState: State): number {
    return !isRuntimeAvailable() ? 0 : value;
  }

  public static getSpacePriorityIcon(priority: number): string | undefined {
    if (priority > 0 && priority < 3) {
      return "sap-icon://expand-group";
    } else if (priority > 3) {
      return "sap-icon://collapse-group";
    } else if (priority === 3) {
      return "sap-icon://less";
    }
  }

  public static getSpacePriorityIconTooltip(this: ManageSpacesClass, priority: number): string | undefined {
    if (priority > 0) {
      return this.getText("spacesTablePriorityIconTooltip", [String(priority)]);
    }
  }

  public static formatSpacePriority(priority: number = 0): string | undefined {
    return !priority ? NOT_AVAILABLE : priority.toString();
  }

  public static getStorageLabel(
    this: ManageSpacesClass,
    message: string,
    usedValue = -1,
    assignedValue = -1,
    hanaState: State = State.Green
  ): string {
    if (!isRuntimeAvailable() || (usedValue === -1 && assignedValue === -1)) {
      return this.getText(message, [NOT_AVAILABLE, NOT_AVAILABLE]);
    }
    const formattedAssigned: string = SpacesOverviewFormatter.getFormattedValue(assignedValue, hanaState, 1);
    const formattedUsed: string = SpacesOverviewFormatter.getFormattedValue(usedValue, hanaState, 2);
    return this.getText(message, [formattedUsed, formattedAssigned]);
  }

  public static getAssignedValue(
    this: ManageSpacesClass,
    assignedValue: number,
    otherAssignedValue: number,
    hanaState: State = State.Green,
    maxFractionDigits = 1
  ): string {
    if (SpacesOverviewFormatter.hasNoSpaceQuota.apply(this, [assignedValue, otherAssignedValue])) {
      return this.getText("noSpaceQuota");
    } else {
      return SpacesOverviewFormatter.getFormattedValue.apply(this, [assignedValue, hanaState, maxFractionDigits]);
    }
  }

  public static getFormattedValue(usedValue: number, hanaState: State, maxFractionDigits = 1): string {
    if (!isRuntimeAvailable()) {
      return NOT_AVAILABLE;
    }
    const formattedValue = SpacesOverviewFormatter.formatValue(usedValue, maxFractionDigits);
    return usedValue > -1 ? formattedValue : NOT_INITIALIZED;
  }

  public static getHdlfSpaceFormattedValue(
    this: ManageSpacesClass,
    usedValue: number,
    hanaState: State,
    maxFractionDigits: number = 1
  ): string {
    if (!isRuntimeAvailable()) {
      return NOT_AVAILABLE;
    }
    let formattedValue = NOT_INITIALIZED;
    if (usedValue > -1) {
      if (usedValue >= 1000 * 1000 * 1000) {
        formattedValue = SpacesOverviewFormatter.formatValue(usedValue, maxFractionDigits);
      } else {
        formattedValue = this.getText("dataLakeStorageUsed");
      }
    }
    return formattedValue;
  }

  public static formatValue(usedValue: number, maxFractionDigits: number = 2) {
    return sap.ui.core.format.FileSizeFormat.getInstance({
      binaryFilesize: false,
      maxFractionDigits,
    }).format(usedValue);
  }

  // todo rename
  public static formatSpaceCount(this: BaseControllerClass, value: number, hanaState: State): any {
    if (!isRuntimeAvailable()) {
      return NOT_AVAILABLE;
    }
    if (value >= 0) {
      return value;
    }
    return NOT_INITIALIZED;
  }

  public static getStorageProgressIndicatorLabel(
    this: ManageSpacesClass,
    usedValue: number,
    assignedValue: number,
    hanaState: State
  ): string {
    const i18nKey = "ofTemplate";
    if (!isRuntimeAvailable()) {
      return this.getText(i18nKey, [
        NOT_AVAILABLE,
        Number(assignedValue) ? SpacesFormatter.getFormattedStorageLabel(assignedValue) : NOT_AVAILABLE,
      ]);
    }
    if (Number(assignedValue) === 0) {
      return this.getText(i18nKey, [SpacesFormatter.getFormattedStorageLabel(usedValue), NOT_INITIALIZED]);
    } else {
      return usedValue && assignedValue
        ? this.getText(i18nKey, [
            SpacesFormatter.getFormattedStorageLabel(usedValue),
            SpacesFormatter.getFormattedStorageLabel(assignedValue),
          ])
        : "";
    }
  }

  public static getStorageProgressIndicatorState(this: BaseControllerClass, percent: number): string {
    return percent >= 100 ? sap.ui.core.ValueState.Warning : sap.ui.core.ValueState.None;
  }

  public static isLoadErrorMessageStripVisible(
    this: ManageSpacesClass,
    spaceResourcesLoadedWithError: boolean,
    spacesLoadedWithError: boolean,
    resourcesLoadedWithError: boolean
  ) {
    if (spaceResourcesLoadedWithError || spacesLoadedWithError || resourcesLoadedWithError) {
      const type: SpacesModelDataType = SpacesOverviewFormatter.getLoadErrorTypeByError(
        spaceResourcesLoadedWithError,
        spacesLoadedWithError,
        resourcesLoadedWithError
      );
      showLoadErrorMessage(
        this.byId("loadErrorMessageStrip") as sap.m.MessageStrip,
        this.getText(SpacesOverviewFormatter.getLoadErrorMessageByType(type)),
        (this.getView().getModel("spaces") as SpacesModel).getRetryFunctionByType(
          type,
          this.getView().getModel().getProperty("/currentSpaceIds")
        )
      );
      return true;
    }
    return false;
  }

  private static getLoadErrorMessageByType(type: SpacesModelDataType) {
    switch (type) {
      case SpacesModelDataType.SPACES:
        return "loadSpacesError";
      case SpacesModelDataType.SPACE_RUNTIMEDATA:
        return "loadRuntimeError";
      case SpacesModelDataType.SPACE_RESOURCES:
        return "loadStorageError";
      default:
        return "";
    }
  }

  public static isSpacesOverviewBusy(
    this: ManageSpacesClass,
    spacesDeleted: boolean,
    totalResourcesLoaded: boolean,
    spaceNamesLoaded: boolean,
    spaceCreated: boolean,
    spacesRestored: boolean
  ) {
    return (
      spacesDeleted === false ||
      totalResourcesLoaded === false ||
      spaceNamesLoaded === false ||
      spaceCreated === false ||
      spacesRestored === false
    );
  }

  private static getLoadErrorTypeByError(
    spaceResourcesLoadedWithError: boolean,
    spacesLoadedWithError: boolean,
    resourcesLoadedWithError: boolean
  ) {
    if (spacesLoadedWithError) {
      return SpacesModelDataType.SPACES;
    } else if (resourcesLoadedWithError) {
      return SpacesModelDataType.SPACE_RUNTIMEDATA;
    } else if (spaceResourcesLoadedWithError) {
      return SpacesModelDataType.SPACE_RESOURCES;
    }
  }

  public static isVisible(this: ManageSpacesClass, controlId: string, privileges: any, hanaState: State, space: Space) {
    const { DWC_DUMMY_SPACE_PERMISSIONS, DWCO_SPACES_LOGICAL_DELETE, INFRA_DWC_TWO_TENANT_MODE } = sap.ui
      .getCore()
      .getModel("featureflags")
      .getData();
    const { DWC_SPACES, USER, SYSTEMINFO } = privileges;
    switch (controlId) {
      case "taskschedulerVBox":
      case "ecnsTable":
        return Boolean(SYSTEMINFO?.read) && Boolean(DWC_SPACES?.assign);
      case "createSpaceHeaderButton":
        return SpacesOverviewFormatter.hasSpaceCreatePrivilege(privileges);
      case "manageSpacesActionChecker":
        return !isRuntimeAvailable();
      case "spaceTileStorageVBox":
      case "spaceTileMemoryVBox":
        return (
          (!DWC_DUMMY_SPACE_PERMISSIONS || (DWC_DUMMY_SPACE_PERMISSIONS && Boolean(DWC_SPACES?.read))) &&
          !Boolean(space?.capabilities?.includes("hdlfStorage"))
        );
      case "hdlfStorageVBox":
      case "sparkVBox":
        return Boolean(space?.capabilities?.includes("hdlfStorage"));
      case "usersColumn":
        return (!DWC_DUMMY_SPACE_PERMISSIONS && Boolean(USER?.read)) || DWC_DUMMY_SPACE_PERMISSIONS;
      case "deletedSpacesListItem":
        return Boolean(DWC_SPACES?.assign) && DWCO_SPACES_LOGICAL_DELETE && INFRA_DWC_TWO_TENANT_MODE;
      default:
        return false;
    }
  }

  public static shouldCreateSpaceBeEnabled(
    this: ManageSpacesClass,
    spacesLoaded: boolean = false,
    resourcesLoaded: boolean = false,
    hanaState: State
  ): boolean {
    const model = this.getView().getModel();
    const hasLoadingErrors =
      model.getProperty("/totalResourcesLoadedWithError") || model.getProperty("/spacesLoadedWithError");
    return spacesLoaded && resourcesLoaded && isRuntimeAvailable() && !hasLoadingErrors;
  }

  public static isDeleteBtnEnabled(this: ManageSpacesClass, hanaState: State, selection: Space[]): boolean {
    const sps = ShellContainer.get().getPrivilegeService();
    const isAllowedToDelete = selection.every((i) => sps.hasPrivilegeInScope(i.name, "DWC_SPACES", "delete"));
    return selection.length > 0 && isAllowedToDelete && isRuntimeAvailable();
  }

  public static hasSpaceDeletePrivilege(privileges: any): boolean {
    const { DWC_SPACES, TEAM } = privileges;
    const featureflagModel: sap.ui.model.Model = sap.ui.getCore().getModel("featureflags");
    const DWC_DUMMY_SPACE_PERMISSIONS = featureflagModel.getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    return Boolean(
      (!DWC_DUMMY_SPACE_PERMISSIONS && DWC_SPACES?.delete && TEAM?.delete) ||
        (DWC_DUMMY_SPACE_PERMISSIONS && DWC_SPACES?.delete)
    );
  }

  public static isLockBtnEnabled(this: ManageSpacesClass, hanaState: State, selection: Space[]): boolean {
    const sps = ShellContainer.get().getPrivilegeService();

    if (selection.length > 0 && isRuntimeAvailable()) {
      if (!selection.every((i) => sps.hasPrivilegeInScope(i.name, "DWC_SPACES", "update", true))) {
        return false;
      }
      const spaceCapabilities = UISpaceCapabilities.get();
      if (!selection.every((i) => !spaceCapabilities.hasCapability(i.name, "hdlfStorage"))) {
        return false;
      }
      const allUnknown = selection.every((i) => i.runtimeData.status === SpaceStatus.Unknown);
      if (allUnknown) {
        return false;
      }
      const hasLockedSpaces = selection.some((i) => i.runtimeData.status === SpaceStatus.Locked);
      const hasActiveSpaces = selection.some((i) => i.runtimeData.status !== SpaceStatus.Locked);
      const onlyActiveSpaces = selection.every((i) => i.runtimeData.status !== SpaceStatus.Locked);
      return onlyActiveSpaces || (hasLockedSpaces && hasActiveSpaces);
    } else {
      return false;
    }
  }

  public static isUnlockBtnEnabled(this: ManageSpacesClass, hanaState: State, selection: Space[]): boolean {
    const sps = ShellContainer.get().getPrivilegeService();
    if (selection.length > 0 && isRuntimeAvailable()) {
      if (!selection.every((i) => sps.hasPrivilegeInScope(i.name, "DWC_SPACES", "update", true))) {
        return false;
      }

      const spaceCapabilities = UISpaceCapabilities.get();
      if (!selection.every((i) => !spaceCapabilities.hasCapability(i.name, "hdlfStorage"))) {
        return false;
      }
      const hasLockedSpaces = selection.some((i) => i.runtimeData.status === SpaceStatus.Locked);
      const hasActiveSpaces = selection.some((i) => i.runtimeData.status !== SpaceStatus.Locked);
      const onlyLockedSpaces = selection.every((i) => i.runtimeData.status === SpaceStatus.Locked);
      return onlyLockedSpaces || (hasLockedSpaces && hasActiveSpaces);
    } else {
      return false;
    }
  }

  public static isMonitorBtnEnabled(this: ManageSpacesClass, selection: Space[]): boolean {
    return selection.length === 1 && !UISpaceCapabilities.get().hasCapability(selection[0].name, "hdlfStorage");
  }

  public static isComputeBlockStepInputEnabled(
    this: ManageSpacesClass,
    selectedPerformanceClass: PerformanceClass,
    readinessPerformanceClass: PerformanceClass
  ): boolean {
    return selectedPerformanceClass === readinessPerformanceClass;
  }

  public static isCreateECNBtnEnabled(
    this: ManageSpacesClass,
    privileges: any,
    hanaState: State,
    isReady = false
  ): boolean {
    const { DWC_SPACES, SYSTEMINFO } = privileges;
    return Boolean(SYSTEMINFO?.update && DWC_SPACES?.assign && isReady) && isRuntimeAvailable();
  }

  public static isViewLogsBtnEnabled(this: ManageSpacesClass, privileges: any, hanaState: State): boolean {
    const { DWC_SPACES, SYSTEMINFO } = privileges;
    return Boolean(SYSTEMINFO?.read && DWC_SPACES?.assign) && isRuntimeAvailable();
  }

  public static isConfigureECNBtnEnabled(
    this: ManageSpacesClass,
    phase: EcnPhaseSnapshotText,
    isReady: boolean,
    privileges: any
  ): boolean {
    const { DWC_SPACES, SYSTEMINFO } = privileges;
    return (
      Boolean(SYSTEMINFO?.update && DWC_SPACES?.assign) &&
      !(phase === EcnPhaseSnapshotText.ACTIVE || phase === EcnPhaseSnapshotText.STOPPING) &&
      Boolean(isReady)
    );
  }

  public static getRemainingECNHoursText(
    this: ManageSpacesClass,
    elasticityReadiness: IEcnElasticityReadinessResponse
  ): string {
    return this.getText(
      elasticityReadiness?.remainingComputeBlockHours > 1 ? "ecnBlockHoursRemaining" : "ecnBlockHourRemaining",
      [elasticityReadiness?.remainingComputeBlockHours?.toString() || NOT_AVAILABLE]
    );
  }

  public static hasSpaceReadPrivilege(privileges: any): boolean {
    return Boolean(privileges.DWC_SPACES?.read);
  }

  public static hasSpaceCreatePrivilege(privileges: any): boolean {
    const { DWC_SPACES, TEAM, USER } = privileges;
    const featureflagModel: sap.ui.model.Model = sap.ui.getCore().getModel("featureflags");
    const DWC_DUMMY_SPACE_PERMISSIONS = featureflagModel.getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    return Boolean(
      (!DWC_DUMMY_SPACE_PERMISSIONS &&
        DWC_SPACES?.create &&
        DWC_SPACES?.assign &&
        TEAM?.read &&
        TEAM?.create &&
        TEAM?.update) ||
        (DWC_DUMMY_SPACE_PERMISSIONS && DWC_SPACES?.create && DWC_SPACES?.assign && USER?.read)
    );
  }

  public static formatECNStatus(this: SpacesSidePanelClass, ecn: IECNInstance): string {
    if (!ecn?.isEnable) {
      return this.getText("ecnRunning");
    }
    // ECN phase
    const { phase, status } = ecn?.runDetails;
    switch (phase) {
      case EcnPhaseSnapshotText.NOT_READY:
        return this.getText("ecnInitial");
      case EcnPhaseSnapshotText.ACTIVE:
        return this.getText("ecnRunning");
      case EcnPhaseSnapshotText.INACTIVE:
      case EcnPhaseSnapshotText.READY:
        return this.getText("ecnReady");
      case EcnPhaseSnapshotText.STARTING:
        if (status === ECNStatus.RED) {
          return this.getText("ecnStartingFailed");
        }
        return this.getText("ecnStarting");
      case EcnPhaseSnapshotText.STOPPING:
        if (status === ECNStatus.RED) {
          return this.getText("ecnStoppingFailed");
        }
        return this.getText("ecnStopping");
      default:
        return this.getText("ecnInitial");
    }
  }

  public static formatECNStatusState(this: SpacesSidePanelClass, ecn: IECNInstance): sap.ui.core.ValueState {
    if (!ecn?.isEnable) {
      return sap.ui.core.ValueState.Error;
    }
    // ECN status
    const { phase, status } = ecn?.runDetails;
    switch (status) {
      case ECNStatus.YELLOW:
        if (phase === EcnPhaseSnapshotText.STARTING || phase === EcnPhaseSnapshotText.STOPPING) {
          return sap.ui.core.ValueState.Information;
        }
        return sap.ui.core.ValueState.Warning;
      case ECNStatus.GREEN:
        return sap.ui.core.ValueState.Success;
      case ECNStatus.RED:
        return sap.ui.core.ValueState.Error;
      default:
        return sap.ui.core.ValueState.None;
    }
  }

  public static hasECNSchedule(this: SpacesSidePanelClass, ecn: IECNInstance): boolean {
    return ecn?.schedule ? true : false;
  }

  public static canCreateECNSchedule(this: SpacesSidePanelClass, ecn: IECNInstance, privileges: any): boolean {
    const { SYSTEMINFO } = privileges;
    return ecn?.schedule && Boolean(SYSTEMINFO?.update) ? false : true;
  }

  public static canChangeECNSchedule(this: SpacesSidePanelClass, ecn: IECNInstance, privileges: any): boolean {
    const { SYSTEMINFO } = privileges;
    return ecn?.schedule && Boolean(SYSTEMINFO?.update) ? true : false;
  }

  public static canPauseECNSchedule(this: SpacesSidePanelClass, ecn: IECNInstance, privileges: any): boolean {
    const { SYSTEMINFO } = privileges;
    return ecn?.schedule?.activationStatus === "ENABLED" && Boolean(SYSTEMINFO?.update);
  }

  public static canResumeECNSchedule(this: SpacesSidePanelClass, ecn: IECNInstance, privileges: any): boolean {
    const { SYSTEMINFO } = privileges;
    return ecn?.schedule?.activationStatus === "DISABLED" && Boolean(SYSTEMINFO?.update);
  }

  public static canChangeOwnerECNSchedule(this: SpacesSidePanelClass, ecn: IECNInstance, privileges: any): boolean {
    const { SYSTEMINFO } = privileges;
    if (ecn?.schedule && Boolean(SYSTEMINFO?.update)) {
      return ecn?.schedule?.owner !== User.getInstance().getUserName();
    } else {
      return false;
    }
  }

  public static formatResourceConsumption(this: SpacesSidePanelClass, resource: ResourceConsumption): string {
    return `${getPercent(resource?.used, resource?.assigned).toString()} %`;
  }

  public static formatSelectionToolbar(this: BaseControllerClass, selection: any[]): string {
    return this.getText("selectedRoleToolbarText", [selection?.length?.toString() || "0"]);
  }

  public static isEditButtonEnabled(selection: Space[]): boolean {
    if (selection?.length === 1) {
      return SpacesOverviewFormatter.isTileEditButtonEnabled(selection[0].name);
    }
    return false;
  }

  public static isTileEditButtonEnabled(spaceName: string): boolean {
    const { DWC_DUMMY_SPACE_PERMISSIONS } = sap.ui.getCore().getModel("featureflags").getData();
    if (DWC_DUMMY_SPACE_PERMISSIONS) {
      const privilegeService = ShellContainer.get().getPrivilegeService();
      return (
        privilegeService.hasPrivilegeInScope(spaceName, "DWC_SPACES", "read") ||
        privilegeService.hasPrivilegeInScope(spaceName, "DWC_SPACES", "update")
      );
    }
    return true;
  }

  public static isTileEditButtonVisible(lifecycle_status: string = "ACTIVE"): boolean {
    const { DWCO_SPACES_LOGICAL_DELETE, INFRA_DWC_TWO_TENANT_MODE } = sap.ui
      .getCore()
      .getModel("featureflags")
      .getData();
    const isLogicallyDeleted =
      DWCO_SPACES_LOGICAL_DELETE && INFRA_DWC_TWO_TENANT_MODE && lifecycle_status === "DELETED";
    return !isLogicallyDeleted;
  }

  public static isUserCountVisible(spaceName: any): boolean {
    const { DWC_DUMMY_SPACE_PERMISSIONS } = sap.ui.getCore().getModel("featureflags").getData();
    const privilegeService = ShellContainer.get().getPrivilegeService();
    if (DWC_DUMMY_SPACE_PERMISSIONS) {
      return privilegeService.hasPrivilegeInScope(spaceName, "DWC_SPACES", "read");
    }
    return privilegeService.hasPrivilege("USER", "read");
  }

  public static formatObjectsToAssignCount(this: ManageSpacesClass, totalAvailableObjectCount: number): string {
    return `${this.getText("objectLabel")} (${totalAvailableObjectCount})`;
  }

  public static getAssigmentStrategy(
    this: ManageSpacesClass,
    asgmtStrtg: IECNMetadataSpaceAsgmtStrtg,
    assignedObjectsCount: number
  ): string {
    if (asgmtStrtg === IECNMetadataSpaceAsgmtStrtg.automatic) {
      return this.getText("autoObjAssignmentLabel");
    } else {
      return assignedObjectsCount?.toString();
    }
  }

  public static getSpacesTitleCount(
    this: BaseControllerClass,
    showDeleted: boolean,
    spacesCount: number,
    deletedSpacesCount: number,
    hanaState: State
  ) {
    const formattedValue = SpacesOverviewFormatter.formatSpaceCount.apply(this, [
      showDeleted ? deletedSpacesCount : spacesCount,
      hanaState,
    ]);
    return showDeleted
      ? `${this.getText("recycleBin")} (${formattedValue})`
      : `${this.getText("spaces")} (${formattedValue})`;
  }

  public static isAssignObjectsBtnEnabled(
    this: ManageSpacesClass,
    selectedSpaces: Space[],
    currentECNActive: boolean
  ): boolean {
    return !currentECNActive && selectedSpaces?.length === 1;
  }

  public static isRemoveObjectsBtnEnabled(
    this: ManageSpacesClass,
    selectedObjects: IECNMetadataSpaceObject[],
    currentECNActive: boolean
  ): boolean {
    if (!currentECNActive) {
      return Boolean(selectedObjects?.length);
    }
    return false;
  }

  public static isRemoveSpacesBtnEnabled(
    this: ManageSpacesClass,
    selectedSpaces: Space[],
    currentECNActive: boolean
  ): boolean {
    if (!currentECNActive) {
      return Boolean(selectedSpaces?.length);
    }
    return false;
  }

  public static getObjectAssignemntTableMode(asgmtStrtg: IECNMetadataSpaceAsgmtStrtg): string {
    return asgmtStrtg === IECNMetadataSpaceAsgmtStrtg.automatic ? "None" : "MultiSelect";
  }

  public static getTechnicalTypeLabel(this: ManageSpacesClass, type: string) {
    return this.getText(type);
  }

  public static getTypeWithSemanticUsageLabel(
    this: ManageSpacesClass,
    technicalTypeDescription: string,
    businessTypeDescription: string
  ) {
    return businessTypeDescription
      ? `${this.getText(technicalTypeDescription)} (${businessTypeDescription})`
      : this.getText(technicalTypeDescription);
  }

  public static privilegeCheckForECNActions(
    this: ManageSpacesClass,
    controlId: string,
    currentECN: IECNInstance,
    privileges: any
  ): boolean {
    const { SYSTEMINFO, DWC_SPACES } = privileges;
    const { phase, status, isRunning } = currentECN?.runDetails;
    switch (controlId) {
      case "assignButton":
      case "deleteButton":
        return (
          Boolean(SYSTEMINFO?.update && DWC_SPACES?.assign) &&
          !isRunning &&
          !(phase === EcnPhaseSnapshotText.ACTIVE || phase === EcnPhaseSnapshotText.STOPPING)
        );
      case "startButton":
        return (
          Boolean(SYSTEMINFO?.read && SYSTEMINFO?.update) &&
          !isRunning &&
          Boolean(phase) &&
          (phase === EcnPhaseSnapshotText.READY ||
            (phase === EcnPhaseSnapshotText.STARTING && (status === ECNStatus.RED || status === ECNStatus.YELLOW)) ||
            !(
              phase === EcnPhaseSnapshotText.ACTIVE ||
              phase === EcnPhaseSnapshotText.STOPPING ||
              phase === EcnPhaseSnapshotText.NOT_READY ||
              phase === EcnPhaseSnapshotText.STARTING
            ))
        );
      case "updateButton":
        return (
          Boolean(SYSTEMINFO?.read && SYSTEMINFO?.update) &&
          !isRunning &&
          (phase === EcnPhaseSnapshotText.ACTIVE ||
            (phase === EcnPhaseSnapshotText.STOPPING && (status === ECNStatus.RED || status === ECNStatus.YELLOW)))
        );
      case "stopButton":
        return (
          Boolean(SYSTEMINFO?.read && SYSTEMINFO?.update) &&
          ((!isRunning &&
            (phase === EcnPhaseSnapshotText.ACTIVE ||
              (phase === EcnPhaseSnapshotText.STOPPING &&
                (status === ECNStatus.RED || status === ECNStatus.YELLOW)))) ||
            !currentECN?.isEnable)
        );
      default:
        return false;
    }
  }

  public static updateTabIconContentHeight(this: ManageSpacesClass, spaces: Space[], isTileLayout: boolean): boolean {
    return !Boolean(spaces?.length && isTileLayout);
  }

  public static getFormattedPerformanceClass(this: ManageSpacesClass, performanceClass: PerformanceClass): string {
    return this.getText(`${performanceClass}Text`);
  }

  public static getSpaceCardDeployStatusText(
    this: ManageSpacesClass,
    deploymentStatus: string,
    deploymentDate: string
  ): string {
    if (
      SpacesFormatter.processObjectStatus.apply(this, [deploymentStatus]) === ObjectStatus.deployed &&
      deploymentDate
    ) {
      return `${this.getText("deployedOn")} ${SpacesFormatter.formatDeployDateTime.apply(this, [deploymentDate])}`;
    }
    return SpacesFormatter.getDeployStatusText.apply(this, [deploymentStatus]);
  }
}
