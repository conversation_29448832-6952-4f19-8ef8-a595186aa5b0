/** @format */
/* eslint-disable camelcase */

import { State } from "@sap/dwc-circuit-breaker";
import { IScopedRole } from "../../../../shared/api/ScopedUsers";
import { MAX_LENGTH_SPACENAME } from "../../../../shared/spaces/constants";
import { getSparkMemoryAmount, IDatabaseUser, SpaceStatus } from "../../../../shared/spaces/types";
import { BaseControllerClass } from "../../basecomponent/controller/BaseController.controller";
import { SpacesType } from "../../reuse/utility/Constants";
import { isRuntimeAvailable } from "../../reuse/utility/Runtime";
import { ObjectStatus } from "../../reuse/utility/Types";
import { ShellContainer } from "../../shell/utility/Container";
import { UISpaceCapabilities } from "../../shell/utility/UISpaceCapabilities";
import { SpaceDetailsClass } from "../controller/details/SpaceDetails.controller";
import { Space } from "../model/Space";
import { byteToGb, MAX_LENGTH_SPACE_LONG_DESCRIPTION, SpaceTypeIconPath } from "../utility/Constants";
import { LockReason } from "../utility/Enums";
import { SpacesFormatter } from "./SpacesFormatter";
import { SpacesOverviewFormatter } from "./SpacesOverviewFormatter";

export class SpaceDetailsFormatter extends SpacesFormatter {
  public static getSpacesHeaderOverflowPriority(
    this: SpaceDetailsClass,
    privileges: any
  ): sap.m.OverflowToolbarPriority {
    const { DWC_DUMMY_SPACE_PERMISSIONS } = sap.ui.getCore().getModel("featureflags").getData();
    return DWC_DUMMY_SPACE_PERMISSIONS && !privileges?.DWC_SPACES?.delete
      ? sap.m.OverflowToolbarPriority.Disappear
      : sap.m.OverflowToolbarPriority.AlwaysOverflow;
  }

  public static isSpaceCopyButtonVisible(isSpaceCopyAllowed: boolean, privileges: any, hanaState: State = State.Green) {
    return (
      Boolean(isSpaceCopyAllowed && SpacesOverviewFormatter.hasSpaceCreatePrivilege(privileges)) && isRuntimeAvailable()
    );
  }

  public static shouldBeVisible(
    this: SpaceDetailsClass,
    controlId: string,
    space: Space,
    privileges: any,
    hanaState: State = State.Green,
    viewModelData: any
  ) {
    const { DWC_SPACES, USER, DWC_REMOTECONNECTION, DWC_DATABUILDER, SCOPEROLEUSERASSIGN, PROFILE } = privileges;
    const view: sap.ui.core.mvc.View = this.getView();
    const spaceDetailsModel: sap.ui.model.json.JSONModel = view.getModel("spaceDetails") as sap.ui.model.json.JSONModel;
    const customerHanaInfoModel: sap.ui.model.json.JSONModel = view.getModel(
      "customerHanaInfo"
    ) as sap.ui.model.json.JSONModel;
    const dataLakeEnabled = !!customerHanaInfoModel?.getProperty("/dataLakeEnabled");
    const spaceData: Space = spaceDetailsModel.getData();
    const { DWC_DUMMY_SPACE_PERMISSIONS, DWCO_LARGE_SYSTEMS_SPARK_DEFAULT, DWCO_WORKLOAD_MANAGEMENT_GROUPS } = sap.ui
      .getCore()
      .getModel("featureflags")
      .getData();
    const isLocked = spaceData?.runtimeData?.status === SpaceStatus.Locked;
    const privService = ShellContainer.get().getPrivilegeService();
    switch (controlId) {
      case "loadErrorMessageStrip":
        return isRuntimeAvailable() && Object.values(viewModelData?.errors).some((error) => !!error);
      case "saveButton":
      case "deployButton":
      case "lockUnlockButton":
        return (
          privService.hasPrivilegeInScope(space.id, "DWC_SPACES", "update", true) &&
          privService.hasPrivilegeInScope(space.id, "DWC_SPACEFILE", "update", true)
        );
      case "deleteButton":
        return Boolean(
          (!DWC_DUMMY_SPACE_PERMISSIONS || (DWC_DUMMY_SPACE_PERMISSIONS && SCOPEROLEUSERASSIGN?.assign)) &&
            DWC_SPACES?.delete
        );
      case "monitorButton":
      case "spaceDetailsOverviewSection":
      case "generalSpaceDetails":
        return Boolean(DWC_SPACES?.read);
      case "spaceDetailsWorkloadClassSection":
        return Boolean(
          !DWCO_WORKLOAD_MANAGEMENT_GROUPS && DWC_SPACES?.read && !space.capabilities?.includes("hdlfStorage")
        );
      case "storageAssignmentHBox":
      case "translationVBox":
        return Boolean(DWC_SPACES?.read && !space.capabilities?.includes("hdlfStorage"));
      case "spaceLongDescription":
        return Boolean(DWC_SPACES?.read);
      case "dataLakeOptionVBox":
        return dataLakeEnabled && Boolean(DWC_SPACES?.read && !space.capabilities?.includes("hdlfStorage"));
      case "workloadPriorityVBox":
        return DWC_DUMMY_SPACE_PERMISSIONS ? true : Boolean(DWC_SPACES?.read);
      case "spaceDetailsTaskAssignmentId":
        return Boolean(DWCO_LARGE_SYSTEMS_SPARK_DEFAULT);
      case "spaceDetailsUserSection":
        return Boolean(
          (!DWC_DUMMY_SPACE_PERMISSIONS && USER?.read) ||
            (DWC_DUMMY_SPACE_PERMISSIONS && (PROFILE?.read || SCOPEROLEUSERASSIGN?.assign))
        );
      case "spaceDetailsRemoteConnectionsSection":
        return Boolean(DWC_REMOTECONNECTION?.read && !space.capabilities?.includes("hdlfStorage"));
      case "spaceDetailsLocalSchemaSection":
        return Boolean(
          (DWC_DUMMY_SPACE_PERMISSIONS ? true : DWC_REMOTECONNECTION?.read) &&
            !space.capabilities?.includes("hdlfStorage")
        );
      case "HDIContainersSectionView":
        return Boolean(DWC_REMOTECONNECTION?.read);
      case "spaceDetailsDataSection":
        return Boolean(
          ((!DWC_DUMMY_SPACE_PERMISSIONS && DWC_DATABUILDER?.read && DWC_SPACES?.read) ||
            (DWC_DUMMY_SPACE_PERMISSIONS && DWC_DATABUILDER?.read)) &&
            !space.capabilities?.includes("hdlfStorage")
        );
      case "spaceDetailsAuditingSection":
        return Boolean(
          (DWC_DUMMY_SPACE_PERMISSIONS ? true : DWC_SPACES?.update) && !space.capabilities?.includes("hdlfStorage")
        );
      case "spaceDetailsSparkManagementSection":
        return Boolean(space.capabilities?.includes("hdlfStorage"));
      case "dataLakeHeaderIcon":
        return spaceData?.runtimeData?.dataLakeEnabled && dataLakeEnabled;
      case "accelerationVBox":
        return Boolean((this.byId("enableStorageQuotaCheckBox") as sap.m.CheckBox)?.getSelected());
      case "storageLimitTag":
        const runtimeData = space?.runtimeData;
        const disk = runtimeData?.disk;
        const memory = runtimeData?.memory;
        return disk?.used > disk?.assigned && disk?.assigned > 0 && memory?.assigned > 0;
      case "timeDataDeployMessageStrip":
        if (DWC_DUMMY_SPACE_PERMISSIONS) {
          return Boolean(DWC_DATABUILDER?.update && DWC_SPACES?.update) && isRuntimeAvailable();
        } else {
          return isRuntimeAvailable();
        }
      case "detailDatabaseUserButton":
        return DWC_DUMMY_SPACE_PERMISSIONS ? true : Boolean(DWC_REMOTECONNECTION?.read);
      case "allowConsumptionCheckbox":
        return DWC_DUMMY_SPACE_PERMISSIONS ? true : Boolean(DWC_REMOTECONNECTION?.read);
      case "databaseUserDeletionMessageStrip":
        return DWC_DUMMY_SPACE_PERMISSIONS ? Boolean(DWC_SPACES?.update) && !!spaceData.databaseUsers.length : true;
      case "createDatabaseUserButton":
      case "editDatabaseUserButton":
        if (DWC_DUMMY_SPACE_PERMISSIONS) {
          return Boolean(DWC_SPACES?.update);
        } else {
          return Boolean(DWC_SPACES?.update && DWC_REMOTECONNECTION?.update);
        }
      case "deleteDatabaseUserButton":
        return Boolean(
          (!DWC_DUMMY_SPACE_PERMISSIONS && DWC_SPACES?.update && DWC_REMOTECONNECTION?.delete) ||
            (DWC_DUMMY_SPACE_PERMISSIONS && DWC_SPACES?.update)
        );
      case "disableAuditMessageStrip":
        return DWC_DUMMY_SPACE_PERMISSIONS ? !isLocked && Boolean(DWC_SPACES?.update) : true;
      default:
        return false;
    }
  }

  public static shouldBeEnabled(
    this: SpaceDetailsClass,
    controlId: string,
    space: Space,
    privileges: any,
    hanaState: State = State.Green
  ) {
    const { DWC_SPACES, DWC_REMOTECONNECTION } = privileges;
    const spaceDetailsModel = this.getView().getModel("spaceDetails") as sap.ui.model.json.JSONModel;
    const spaceData: Space = spaceDetailsModel.getData();
    const { DWC_DUMMY_SPACE_PERMISSIONS, DWCO_WORKLOAD_MANAGEMENT_UI } = sap.ui
      .getCore()
      .getModel("featureflags")
      .getData();
    const isLocked = spaceData?.runtimeData?.status === SpaceStatus.Locked;
    /* Please note: the next variable uses "normal" privilege checks for spaces
    that override DWC_SPACES.update via SpaceCapabilities. If might need with
    an additional check like:
    UISpaceCapabilities.get().hasCapability(space.id, "bdc");
    */

    // Disable saveButton if a tenant upgrade is in progress or there are no unsaved changes
    this.byId("saveButton")?.setEnabled(isRuntimeAvailable() && this.getView().getModel("spaceDetails")["isDirty"]());

    const DWC_SPACES_update = ShellContainer.get()
      .getPrivilegeService()
      .hasPrivilegeInScope(space.id, "DWC_SPACES", "update", true);
    switch (controlId) {
      case "detailDatabaseUserButton":
        if (DWC_DUMMY_SPACE_PERMISSIONS) {
          return true;
        } else {
          return !isLocked && DWC_REMOTECONNECTION?.read;
        }
      case "deployButton":
        if (DWC_DUMMY_SPACE_PERMISSIONS) {
          return !isLocked && isRuntimeAvailable();
        } else {
          return Boolean(DWC_SPACES?.update) && space.runtimeData.status !== SpaceStatus.Locked && isRuntimeAvailable();
        }
      case "deleteButton":
        if (DWC_DUMMY_SPACE_PERMISSIONS) {
          return isRuntimeAvailable();
        } else {
          return isRuntimeAvailable() && Boolean(DWC_SPACES?.delete);
        }
      case "spaceName":
      case "spaceLongDescription":
      case "readAuditLogCheckBox":
      case "changeAuditLogCheckBox":
        return !isLocked && DWC_SPACES_update;
      case "diskStepInput":
      case "ramStepInput":
        return (
          Boolean((this.byId("enableStorageQuotaCheckBox") as sap.m.CheckBox)?.getSelected()) &&
          !isLocked &&
          Boolean(DWC_SPACES?.assign)
        );
      case "spacePrioritySlider":
      case "workloadTypeSelect":
        if (DWCO_WORKLOAD_MANAGEMENT_UI) {
          return false;
        } else {
          return !isLocked && Boolean(DWC_SPACES?.assign);
        }
      case "enableStorageQuotaCheckBox":
      case "dataLakeCheckBox":
        return !isLocked && Boolean(DWC_SPACES?.assign);
      case "changeAuditLogRetentionInput":
        return !isLocked && space?.dppChange?.isAuditPolicyActive && DWC_SPACES_update;
      case "readAuditLogRetentionInput":
        return !isLocked && space?.dppRead?.isAuditPolicyActive && DWC_SPACES_update;
      case "sourceLanguageSelect":
        return Boolean(DWC_SPACES?.update);
      case "createDatabaseUserButton":
        if (DWC_DUMMY_SPACE_PERMISSIONS) {
          return !isLocked;
        } else {
          return !isLocked && Boolean(DWC_REMOTECONNECTION?.update);
        }
      case "allowConsumptionCheckbox":
        return !isLocked && Boolean(DWC_SPACES?.update);
      case "monitorButton":
        return !UISpaceCapabilities.get().hasCapability(space.id, "hdlfStorage");
      case "updateSparkManagementBtn":
      case "memoryStepInput":
        return (
          Boolean(DWC_SPACES?.assign) &&
          SpacesFormatter.processObjectStatus.apply(this, [spaceDetailsModel.getProperty("/objectStatus")]) !==
            ObjectStatus.pending
        );
      default:
        return false;
    }
  }

  public static getNoDataTextForSparkSourcesTable(this: SpaceDetailsClass, space: Space): string {
    return Boolean(SpacesFormatter.processObjectStatus.apply(this, [space?.objectStatus]) === ObjectStatus.pending)
      ? this.getText("sparkTableDeploymentInProgress")
      : this.getText("sparkTableDeploymentLoading");
  }

  public static formatStatusValidToTimestamp(this: SpaceDetailsClass, statusValidToUtc: string): string | undefined {
    if (statusValidToUtc) {
      const localStatusValidToTime: Date = new Date(statusValidToUtc);
      const currentTime: Date = new Date();
      let remainingHours: number = (localStatusValidToTime.getTime() - currentTime.getTime()) / 3600000;
      remainingHours = Math.max(remainingHours, 0);

      let timeUnit: string;
      let remainingTime: string;
      if (remainingHours >= 1) {
        remainingTime = Math.round(remainingHours).toString();
        timeUnit = this.getText("hours");
      } else {
        remainingTime = Math.round(remainingHours * 60).toString();
        timeUnit = this.getText("minutes");
      }

      return this.getText("spaceTemporarilyUnlockedMessageStrip", [remainingTime, timeUnit]);
    }
  }

  public static formatSpaceID(this: SpaceDetailsClass, spaceID: string): string {
    const spaceIdInput: sap.m.Input = this.byId("spaceID") as sap.m.Input;
    return spaceID || spaceIdInput?.getValue();
  }

  public static getSpaceTypeIconPath(spaceType: SpacesType): string {
    switch (spaceType) {
      case SpacesType.Marketplace:
        return SpaceTypeIconPath.MARKETPLACE;
      case SpacesType.AbapBridge:
        return SpaceTypeIconPath.ABAPBRIDGE;
      default:
        return SpaceTypeIconPath.DEFAULT;
    }
  }

  public static formatSpaceTypeText(this: BaseControllerClass, spaceType: SpacesType, spaceId: string): string {
    spaceType = Space.getSpaceType(spaceId, spaceType);
    if (spaceType) {
      return this.getText(`${spaceType}SpaceType`);
    }
    return this.getText("notAvailable");
  }

  public static formatBytesToGB(bytes: number): number {
    return bytes !== undefined ? bytes / byteToGb : -1;
  }

  public static getFormatterLanguageLabel(this: BaseControllerClass, languageLabel: string): string {
    return this.getText(languageLabel);
  }

  public static getFormatterClusterSize(this: BaseControllerClass, property: string): string {
    return this.getText(property);
  }

  public static getFormatterSparkSourceCPUAndMemoryLabel(
    this: BaseControllerClass,
    value: string,
    spark_configurations: any
  ): string {
    let cpu: number;
    let memory: string;
    switch (value) {
      case "driverConfiguration": {
        cpu = spark_configurations["spark.driver.cores"];
        memory = spark_configurations["spark.driver.memory"];
        break;
      }
      case "executorConfiguration": {
        cpu = spark_configurations["spark.executor.cores"];
        memory = spark_configurations["spark.executor.memory"];
        break;
      }
      case "maxUsedConfiguration": {
        cpu = spark_configurations["spark.cores.max"];
        memory = spark_configurations["spark.memory.max"];
        break;
      }
    }
    return this.getText("driverExecutorMaxUsedSparkSource", [cpu.toString(), getSparkMemoryAmount(memory)]);
  }

  public static getRoleString(this: BaseControllerClass, roles: string): string {
    return roles === "-1" ? this.getText("userDeleted") : roles;
  }

  public static getLockedSpaceReasonText(this: BaseControllerClass, lockReason: LockReason): string {
    switch (lockReason) {
      case LockReason.Manual:
        return this.getText("manualLockedMessage");
      case LockReason.AuditLog:
        return this.getText("auditLogLockedMessage");
      case LockReason.Quota:
        return this.getText("quotaLockedMessage");
      default:
        return this.getText("spaceLockedMessageStrip");
    }
  }

  public static formatSelectedRolesText(this: BaseControllerClass, selectedRoles: IScopedRole[]): string {
    return this.getText("selectedRoleToolbarText", [selectedRoles?.length?.toString() || "0"]);
  }

  public static isSelectedRoleOverflowToolbarVisible(this: BaseControllerClass, selectedRoles: IScopedRole[]): boolean {
    return Boolean(selectedRoles?.length);
  }

  public static getApprovalIconPath(value: boolean = false): string {
    return `sap-icon://${value ? "accept" : "decline"}`;
  }

  public static getApprovalIconColorName(value: boolean): string {
    return value ? sap.ui.core.IconColor.Positive : sap.ui.core.IconColor.Negative;
  }

  public static getIconTooltipText(this: SpaceDetailsClass, value: boolean): string {
    return this.getText(value ? "enabled" : "disabled");
  }

  public static getDBUserInfoIconPath(username: string, dbUsersRuntime: IDatabaseUser[]): string {
    const user = dbUsersRuntime?.find((user) => user.username === username);
    return `sap-icon://sac/${user?.isLocked ? "public" : "info"}`;
  }

  public static getDatabaseUserInfoDialogTooltipText(
    this: SpaceDetailsClass,
    username: string,
    dbUsersRuntime: IDatabaseUser[]
  ): string {
    const user = dbUsersRuntime?.find((user) => user.username === username);
    return this.getText(user?.isLocked ? "openLockedDatabaseUserInfoDialog" : "openDatabaseUserInfoDialog");
  }

  public static getDBUserHighlightState(
    this: SpaceDetailsClass,
    username: string,
    dbUsersRuntime: IDatabaseUser[],
    isNew: boolean
  ): string {
    const user = dbUsersRuntime?.find((user) => user.username === username);
    if (user?.isLocked) {
      return sap.ui.core.MessageType.Error;
    } else if (isNew) {
      return sap.ui.core.MessageType.Information;
    } else {
      return sap.ui.core.MessageType.None;
    }
  }

  public static getDBUserObjectStatusState(
    this: SpaceDetailsClass,
    username: string,
    dbUsersRuntime: IDatabaseUser[],
    hanaState: State = State.Red
  ): string {
    const user = dbUsersRuntime?.find((user) => user.username === username);
    if (user?.isLocked || !isRuntimeAvailable()) {
      return sap.ui.core.MessageType.Error;
    } else if (user) {
      return sap.ui.core.MessageType.Success;
    } else {
      return sap.ui.core.MessageType.None;
    }
  }

  public static getDBUserStatusText(
    this: SpaceDetailsClass,
    username: string,
    dbUsersRuntime: IDatabaseUser[],
    hanaState: State = State.Red
  ): string {
    const user = dbUsersRuntime?.find((user) => user.username === username);
    if (!isRuntimeAvailable()) {
      return this.getText("notAvailable");
    } else if (!user) {
      return this.getText("notDeployed");
    } else if (user?.isLocked) {
      return this.getText("lockedLabel");
    } else {
      return this.getText("activeLabel");
    }
  }

  public static getSpaceDescriptionValueState(this: SpaceDetailsClass, value: string): sap.ui.core.ValueState {
    if (!value || value.length === 0) {
      return sap.ui.core.ValueState.Error;
    } else if (value.length < MAX_LENGTH_SPACENAME) {
      return sap.ui.core.ValueState.None;
    } else {
      return sap.ui.core.ValueState.Warning;
    }
  }

  public static getSpaceDescriptionValueStateText(this: SpaceDetailsClass, value: string): string {
    if (!value || value.length === 0) {
      return this.getText("spaceNameEmptyValidationError");
    } else if (value.length < MAX_LENGTH_SPACENAME) {
      return "";
    } else {
      return this.getText("spaceNameLengthWarning", [MAX_LENGTH_SPACENAME.toString()]);
    }
  }

  public static getAssignedRamValueState(
    this: SpaceDetailsClass,
    assignedRam: number,
    minAssignableRamBytes: number,
    maxAssignableRamBytes: number,
    isSpaceQuotaEnabled: boolean
  ): sap.ui.core.ValueState {
    if (!isSpaceQuotaEnabled) {
      return sap.ui.core.ValueState.None;
    }
    if (isNaN(assignedRam)) {
      return sap.ui.core.ValueState.Error;
    }
    if (assignedRam === minAssignableRamBytes || assignedRam === maxAssignableRamBytes) {
      return sap.ui.core.ValueState.Warning;
    } else {
      return sap.ui.core.ValueState.None;
    }
  }

  public static getAssignedRamValueStateText(
    this: SpaceDetailsClass,
    assignedRam: number,
    minAssignableRamBytes: number,
    maxAssignableRamBytes: number,
    isSpaceQuotaEnabled: boolean
  ): string {
    if (isNaN(assignedRam) || !isSpaceQuotaEnabled) {
      return "";
    }
    if (assignedRam === minAssignableRamBytes) {
      return this.getText("minRamLimitReachedLabel");
    } else if (assignedRam === maxAssignableRamBytes) {
      return this.getText("ramLimitReachedLabel");
    } else {
      return "";
    }
  }

  public static getMaxSpaceNameLength(this: SpaceDetailsClass): number {
    return MAX_LENGTH_SPACENAME;
  }

  public static getLongDescriptionMaxLength(this: SpaceDetailsClass): number {
    return MAX_LENGTH_SPACE_LONG_DESCRIPTION;
  }

  public static getNoDataText(this: SpaceDetailsClass, hanaState: State = State.Green) {
    return isRuntimeAvailable() ? this.getText("noDataTimedata") : this.getText("runtimeNotAvailableNoDataTimedata");
  }
  public static getHDINoDataText(this: SpaceDetailsClass, hanaState: State = State.Red) {
    return isRuntimeAvailable() ? this.getText("noDataHDIContainers") : this.getText("runtimeNotAvailableNoDataHDI");
  }

  public static getLoadErrorMessageStripText(this: SpaceDetailsClass, errors: any = {}): string {
    return this.getText("loadDataError");
  }

  public static getSparkManagementSectionTitle(this: SpaceDetailsClass, featureFlags: object): string {
    const { DWCO_LARGE_SYSTEMS_SPARK_DEFAULT } = sap.ui.getCore().getModel("featureflags").getData();
    return DWCO_LARGE_SYSTEMS_SPARK_DEFAULT
      ? this.getText("sparkManagementSection")
      : this.getText("workloadManagement");
  }

  public static getSparkManagementSubsectionTitle(this: SpaceDetailsClass, featureFlags: object): string {
    const { DWCO_LARGE_SYSTEMS_SPARK_DEFAULT } = sap.ui.getCore().getModel("featureflags").getData();
    return DWCO_LARGE_SYSTEMS_SPARK_DEFAULT
      ? this.getText("sparkManagementSubSection")
      : this.getText("sparkManagementSection");
  }

  public static getRelativeEditUrlForRoleId(roleId: string): string {
    return `#/roles&/r/${roleId?.substring(roleId.lastIndexOf(":") + 1)}`;
  }
}
