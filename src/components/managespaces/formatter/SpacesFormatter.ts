/** @format */

import { WorkloadClassUnit } from "@sap/deepsea-types";
import { State } from "@sap/dwc-circuit-breaker/dist/types";
import { WorkloadType } from "../../../../shared/api/SpaceAPI";
import { SpaceStatus, StorageType } from "../../../../shared/spaces/types";
import { BaseControllerClass } from "../../basecomponent/controller/BaseController.controller";
import { objectStatusIconFormatter } from "../../ermodeler/js/utility/sharedFunctions";
import { IActionAvailability } from "../../reuse/control/actionchecker/utility/Constants";
import { ActionState, SpacesType, ToolName } from "../../reuse/utility/Constants";
import { Format } from "../../reuse/utility/Format";
import { isRuntimeAvailable } from "../../reuse/utility/Runtime";
import { ObjectStatus } from "../../reuse/utility/Types";
import { getActionAvailabilityObject } from "../../reuse/utility/UIHelper";
import { SpaceDetailsClass } from "../controller/details/SpaceDetails.controller";
import { DatabaseUserDetailsDialogClass } from "../controller/details/databaseusers/DatabaseUserDetailsDialog.controller";
import { ManageSpacesClass } from "../controller/overview/ManageSpaces.controller";
import { Space } from "../model/Space";
import {
  NOT_INITIALIZED,
  SpaceHealth,
  SpaceIconPath,
  maxAuditLogRetentionTimeInDays,
  minAuditLogRetentionTimeInDays,
} from "../utility/Constants";
import { ResourceCategory } from "../utility/Enums";
import * as TimeData from "../utility/TimeData";
export class SpacesFormatter {
  public static shouldBeVisible(
    this: SpaceDetailsClass,
    controlId: string,
    space: Space,
    privileges: any,
    hanaState: State = State.Green,
    viewModelData: any
  ) {
    const { DWC_SPACES, DWC_REMOTECONNECTION, TEAM, USER, SCOPEROLEUSERASSIGN, SYSTEMINFO } = privileges;
    const spaceDetailsModel = this.getView().getModel("spaceDetails") as sap.ui.model.json.JSONModel;
    const spaceData: Space = spaceDetailsModel.getData();
    const featureflagModel: sap.ui.model.Model = sap.ui.getCore().getModel("featureflags");
    const DWC_DUMMY_SPACE_PERMISSIONS: boolean = featureflagModel.getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    const DWCO_WORKLOAD_MANAGEMENT_UI: boolean = featureflagModel.getProperty("/DWCO_WORKLOAD_MANAGEMENT_UI");
    const DWCO_SPACES_HDI_MAPPING: boolean = featureflagModel.getProperty("/DWCO_SPACES_HDI_MAPPING");
    const HDIenabled: boolean = spaceDetailsModel.getProperty("/HDIContainers/enabled");
    switch (controlId) {
      case "addHDIContainersButton":
      case "removeHDIContainersButton":
        return HDIenabled && Boolean(DWC_SPACES?.update && DWC_REMOTECONNECTION?.update);
      case "enableHDIContainersButton":
        return !DWCO_SPACES_HDI_MAPPING && !HDIenabled && Boolean(DWC_REMOTECONNECTION?.read);
      case "navToMappingsButton":
        return Boolean(DWCO_SPACES_HDI_MAPPING && SYSTEMINFO?.update && DWC_SPACES?.assign);
      case "editUsersBtn":
      case "usersTableDeleteBtn":
        return Boolean((DWC_SPACES?.read && SCOPEROLEUSERASSIGN?.assign) || USER?.read);
      case "addUsersButton":
        return Boolean(
          (!DWC_DUMMY_SPACE_PERMISSIONS && USER?.read && TEAM?.read) ||
            (DWC_DUMMY_SPACE_PERMISSIONS && DWC_SPACES?.read && (SCOPEROLEUSERASSIGN?.assign || USER?.read))
        );
      case "usersTableDeleteButton":
        return Boolean(TEAM?.update);
      case "statementLimitsVBox":
        return DWC_DUMMY_SPACE_PERMISSIONS ? true : Boolean(DWC_SPACES?.read);
      case "statementLimits":
        return spaceData?.workloadType === WorkloadType.CUSTOM;
      case "workloadTypeDescription":
        if (DWCO_WORKLOAD_MANAGEMENT_UI) {
          return false;
        } else {
          return Boolean(DWC_SPACES?.assign || (spaceData?.workloadType === WorkloadType.DEFAULT && DWC_SPACES?.read));
        }
      case "modifySpaceConfigurationMessageStrip":
      case "totalStatementThreadLimitHelpText":
      case "totalStatementMemoryLimitHelpText":
        if (DWCO_WORKLOAD_MANAGEMENT_UI) {
          return false;
        } else {
          return Boolean(
            (!DWC_DUMMY_SPACE_PERMISSIONS && DWC_SPACES?.assign) ||
              (DWC_DUMMY_SPACE_PERMISSIONS && (DWC_SPACES?.assign || (DWC_SPACES?.create && DWC_SPACES?.update)))
          );
        }
      case "HDIContainerDeletionMessageStrip":
        if (DWC_DUMMY_SPACE_PERMISSIONS) {
          return HDIenabled;
        } else {
          return true;
        }
      default:
        return false;
    }
  }

  public static canResetPassword(this: DatabaseUserDetailsDialogClass, data: any) {
    const DWC_DUMMY_SPACE_PERMISSIONS = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    const privileges = sap.ui.getCore().getModel("privilege").getData();

    if (DWC_DUMMY_SPACE_PERMISSIONS) {
      return !data.isSpaceLocked && (privileges?.DWC_REMOTECONNECTION?.update || privileges?.DWC_SPACES?.update);
    } else {
      return !data.isSpaceLocked && privileges?.DWC_REMOTECONNECTION?.update;
    }
  }

  public static shouldBeEnabled(
    this: SpaceDetailsClass,
    controlId: string,
    space: Space,
    privileges: any,
    hanaState: State = State.Green
  ) {
    const { DWC_SPACES, DWC_REMOTECONNECTION, DWC_DATABUILDER } = privileges;
    const featureflagModel: sap.ui.model.Model = sap.ui.getCore().getModel("featureflags");
    const spaceDetailsModel = this.getView().getModel("spaceDetails") as sap.ui.model.json.JSONModel;
    const spaceData: Space = spaceDetailsModel.getData();
    const DWC_DUMMY_SPACE_PERMISSIONS: boolean = featureflagModel.getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    const DWCO_WORKLOAD_MANAGEMENT_UI: boolean = featureflagModel.getProperty("/DWCO_WORKLOAD_MANAGEMENT_UI");
    const HDIenabled: boolean = spaceDetailsModel.getProperty("/HDIContainers/enabled");
    const isLocked = spaceData?.runtimeData?.status === SpaceStatus.Locked;

    switch (controlId) {
      case "addHDIContainersButton":
        return !isLocked && HDIenabled && isRuntimeAvailable();
      case "enableHDIContainersButton":
        if (DWC_DUMMY_SPACE_PERMISSIONS) {
          return !HDIenabled;
        } else {
          return !HDIenabled && Boolean(DWC_REMOTECONNECTION?.update);
        }
      case "addUsersButton":
        if (DWC_DUMMY_SPACE_PERMISSIONS) {
          return !isLocked;
        } else {
          return !isLocked && Boolean(DWC_SPACES?.update);
        }
      case "generateTimeDataButton":
        if (DWC_DUMMY_SPACE_PERMISSIONS) {
          return (
            SpacesFormatter.isDeployed.apply(this, [spaceDetailsModel.getProperty("/objectStatus")]) &&
            isRuntimeAvailable() &&
            !isLocked
          );
        } else {
          if (!spaceData.userIsMemberOfSpace || isLocked) {
            return false;
          }
          return (
            SpacesFormatter.isDeployed.apply(this, [spaceDetailsModel.getProperty("/objectStatus")]) &&
            isRuntimeAvailable()
          );
        }
      case "refreshListButton":
      case "deleteButton":
      case "refreshButton":
      case "editButton":
        if (DWC_DUMMY_SPACE_PERMISSIONS) {
          return !isLocked && isRuntimeAvailable();
        } else {
          return (
            spaceData.userIsMemberOfSpace &&
            !isLocked &&
            isRuntimeAvailable() &&
            Boolean(DWC_SPACES?.read && DWC_DATABUILDER?.delete)
          );
        }

      case "enableTotalStatementThreadLimitCheckBox":
      case "enableTotalStatementMemoryLimitCheckBox":
        if (DWC_DUMMY_SPACE_PERMISSIONS) {
          return !isLocked && (DWC_SPACES?.assign || (DWC_SPACES?.create && DWC_SPACES?.update));
        } else {
          return !isLocked && Boolean(DWC_SPACES?.assign);
        }
      case "totalStatementThreadLimitSlider":
      case "totalStatementThreadLimitInput":
      case "totalStatementThreadLimitSegmentedButton":
        if (DWCO_WORKLOAD_MANAGEMENT_UI) {
          return false;
        } else {
          return (
            !isLocked &&
            spaceData?.workloadClass?.totalStatementThreadLimit?.value !== null &&
            Boolean(DWC_SPACES?.assign)
          );
        }
      case "totalStatementMemoryLimitSlider":
      case "totalStatementMemoryLimitInput":
      case "totalStatementMemoryLimitSegmentedButton":
        if (DWCO_WORKLOAD_MANAGEMENT_UI) {
          return false;
        } else {
          return (
            !isLocked &&
            spaceData?.workloadClass?.totalStatementMemoryLimit?.value !== null &&
            Boolean(DWC_SPACES?.assign)
          );
        }
      case "openConnectionToolButton":
        if (DWC_DUMMY_SPACE_PERMISSIONS) {
          return true;
        } else {
          return spaceData.userIsMemberOfSpace;
        }
      default:
        return false;
    }
  }

  public static getWorkloadSectionDescription(this: SpaceDetailsClass, workloadType: WorkloadType): string {
    return workloadType === WorkloadType.DEFAULT
      ? this.getText("workloadClassStatementLimitsDescription")
      : this.getText("workloadClassStatementLimitCustomDescription");
  }

  public static getTotalStatementThreadLimitHelpText(this: SpaceDetailsClass, maxThreadLimitPercent: string): string {
    return this.getText("totalStatementThreadLimitHelpTextDynamic", [maxThreadLimitPercent]);
  }

  public static getThreadLimitMaxValue(this: SpaceDetailsClass, unit: WorkloadClassUnit): number {
    const customerHanaInfo = this.getView()?.getModel("customerHanaInfo")?.getData();
    switch (unit) {
      case WorkloadClassUnit.COUNTER:
        return customerHanaInfo?.maxThreadLimit;
      case WorkloadClassUnit.PERCENT:
        return customerHanaInfo?.maxThreadLimitPercent;
      default:
        return 100;
    }
  }

  public static processObjectStatus(
    currentDeployStatus: string,
    modificationDate: number,
    deploymentDate: number
  ): ObjectStatus {
    const objectStatus: ObjectStatus = parseInt(currentDeployStatus, 10) as ObjectStatus;
    switch (objectStatus) {
      case ObjectStatus.deployed:
      case ObjectStatus.notDeployed:
      case ObjectStatus.hasNoObjectStatus:
      case ObjectStatus.changesToDeploy:
        return modificationDate > deploymentDate ? ObjectStatus.changesToDeploy : objectStatus;
      case ObjectStatus.pending:
      case ObjectStatus.designTimeError:
      case ObjectStatus.runTimeError:
        return objectStatus;
      default:
        return ObjectStatus.hasNoObjectStatus;
    }
  }

  public static getDeployStatusText(
    this: SpaceDetailsClass,
    objectStatus: number = ObjectStatus.hasNoObjectStatus,
    modificationDate: number,
    deploymentDate: number
  ): string {
    switch (SpacesFormatter.processObjectStatus.apply(this, [objectStatus, modificationDate, deploymentDate])) {
      case ObjectStatus.notDeployed:
        return this.getText("notDeployed");
      case ObjectStatus.deployed:
        return this.getText("deployed");
      case ObjectStatus.changesToDeploy:
        return this.getText("changesToDeploy");
      case ObjectStatus.pending:
        return this.getText("pending");
      case ObjectStatus.designTimeError:
        return this.getText("designtimeError");
      case ObjectStatus.runTimeError:
        return this.getText("runtimeError");
      default:
        return this.getText("notAvailable");
    }
  }

  public static getDeployStatusIconColor(objectStatus: ObjectStatus): string {
    return Format.objectStatusIconColorFormatter(objectStatus, false);
  }

  public static getDeployStatusIconPath(objectStatus: ObjectStatus): string {
    return objectStatusIconFormatter(objectStatus, false);
  }

  public static formatDeployDateTime(this: SpaceDetailsClass, value: string): string {
    return value ? this.formatDateTime(value) : this.getText("notAvailable");
  }

  public static getStorageLabel(
    this: BaseControllerClass,
    message: string,
    usedValue: number,
    assignedValue: number,
    hanaState: State = State.Green
  ): string {
    if (Number(assignedValue) === 0) {
      if (!isRuntimeAvailable()) {
        return `${this.getText(message)} -`;
      }
      return `${this.getText(message)} ${SpacesFormatter.getFormattedStorageLabel(usedValue)}`;
    } else {
      if (!isRuntimeAvailable()) {
        return this.getText(message, ["-", SpacesFormatter.getFormattedStorageLabel(assignedValue)]);
      }
      return this.getText(message, [
        SpacesFormatter.getFormattedStorageLabel(usedValue),
        SpacesFormatter.getFormattedStorageLabel(assignedValue),
      ]);
    }
  }

  public static getFormattedStorageLabel(value: number) {
    let formattedValue = NOT_INITIALIZED;
    if (value > -1) {
      formattedValue = sap.ui.core.format.FileSizeFormat.getInstance({
        binaryFilesize: false,
        maxFractionDigits: 2,
      }).format(value);
    }
    return formattedValue;
  }

  public static formatStatusText(
    this: BaseControllerClass,
    isLocked: boolean,
    passwordChangeNeeded: boolean,
    isDeployed: boolean
  ): string {
    let statusText = this.getText("activeLabel");
    if (isLocked) {
      statusText = this.getText("lockedLabel");
    } else if (passwordChangeNeeded) {
      statusText = this.getText("expired");
    } else if (isDeployed !== undefined || isDeployed === false) {
      statusText = this.getText("notDeployed");
    }
    return statusText;
  }

  public static formatStatusState(isLocked: boolean, passwordChangeNeeded: boolean, isDeployed: boolean): string {
    let statusState = sap.ui.core.ValueState.Success;
    if (isLocked) {
      statusState = sap.ui.core.ValueState.Error;
    } else if (passwordChangeNeeded) {
      statusState = sap.ui.core.ValueState.None;
    } else if (isDeployed !== undefined || isDeployed === false) {
      statusState = sap.ui.core.ValueState.None;
    }
    return statusState;
  }

  public static getCalendarTypeText(this: BaseControllerClass, calType: string): string {
    return this.getText(calType);
  }

  public static getGranularityText(this: BaseControllerClass, granularity: string): string {
    return this.getText(granularity);
  }

  public static getPanelTitle(this: BaseControllerClass, name: string): string {
    if (!name?.length) {
      return null;
    }
    const object: any = (this.getView().getModel() as sap.ui.model.json.JSONModel)
      ?.getData()
      .find((item) => item.name === name);
    if (object?.files) {
      const amountOfFiles = object.files.length;
      const objectName = TimeData.DEFAULT_VALUES[object.name]?.technical || object.name;
      if (amountOfFiles === 1) {
        return `"${objectName}" ${this.getText("timeDataUsedInSingular")}`;
      } else {
        return `"${objectName}" ${this.getText("timeDataUsedIn", [object.files.length])}`;
      }
    }
    return null;
  }

  public static getDialogTitle(this: BaseControllerClass, dependencies: any): string {
    const data: any = dependencies?.[0];
    const hasSingleDependency: boolean = data?.files?.length === 1;
    if (data?.dialogTitle) {
      return hasSingleDependency ? data?.dialogTitle.singular : data?.dialogTitle.plural;
    } else {
      return hasSingleDependency
        ? this.getText("deleteTimeDataDependencyTextSingular")
        : this.getText("deleteTimeDataDependencyText");
    }
  }

  public static getButtonTooltip(this: BaseControllerClass, userIsMemberOfSpace: boolean) {
    const featureflagModel: sap.ui.model.Model = sap.ui.getCore().getModel("featureflags");
    const DWC_DUMMY_SPACE_PERMISSIONS: boolean = featureflagModel.getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    if (!userIsMemberOfSpace && !DWC_DUMMY_SPACE_PERMISSIONS) {
      return this.getText("mustBeMemberOfSpaceForOperation");
    } else {
      return "";
    }
  }

  public static shouldTimeDataBeActive(
    this: BaseControllerClass,
    userIsMemberOfSpace: boolean = false,
    objectStatus: string,
    DataHANA: State
  ): boolean {
    const spaceData: Space =
      (this.getView().getModel("spaceDetails") as sap.ui.model.json.JSONModel)?.getData() || ({} as Space);

    const featureflagModel: sap.ui.model.Model = sap.ui.getCore().getModel("featureflags");
    const DWC_DUMMY_SPACE_PERMISSIONS = Boolean(featureflagModel.getProperty("/DWC_DUMMY_SPACE_PERMISSIONS"));
    return (
      (userIsMemberOfSpace || DWC_DUMMY_SPACE_PERMISSIONS) &&
      spaceData.runtimeData?.status !== SpaceStatus.Locked &&
      SpacesFormatter.isDeployed.apply(this, [objectStatus]) &&
      SpacesFormatter.hasRuntimeData.apply(this) &&
      isRuntimeAvailable() &&
      !spaceData.capabilities?.includes("hdlfStorage")
    );
  }

  public static hasRuntimeData(this: BaseControllerClass): boolean {
    const spaceData: Space = (this.getView().getModel("spaceDetails") as sap.ui.model.json.JSONModel).getData();
    const runtimeData = spaceData.runtimeData;
    return runtimeData?.disk.assigned > -1 && runtimeData?.memory.assigned > -1;
  }

  public static isDeployed(this: BaseControllerClass, objectStatus: string) {
    const status: ObjectStatus = parseInt(objectStatus, 10) as ObjectStatus;
    return (
      !isNaN(status) &&
      [
        ObjectStatus.deployed,
        ObjectStatus.pending,
        ObjectStatus.changesToDeploy,
        ObjectStatus.designTimeError,
        ObjectStatus.runTimeError,
      ].includes(status)
    );
  }

  public static getFormattedDescription(
    this: BaseControllerClass,
    status: string,
    calendarType: string,
    granularity: string,
    rangeFrom: string,
    rangeTo: string
  ): string {
    if (status === "COMPLETED") {
      return `${this.getText("calendarTypeLabel")}: ${this.getText(calendarType)}, ${this.getText(
        "granularityLabel"
      )}: ${this.getText(granularity)}, ${this.getText("timerangeLabel")}: ${rangeFrom}-${rangeTo}`;
    } else if (status === "ERROR") {
      return this.getText("timeDataCreationError");
    } else if (status === "IN_PROCESS") {
      return this.getText("timeDataCreationInProgress");
    }
  }

  public static getDeleteSpaceActionAvailability(spaceType: SpacesType): ActionState {
    const deleteSpaceActionObject: IActionAvailability = getActionAvailabilityObject(
      `/${ToolName.ManageSpaces}/spacedetails/deleteSpace`
    );
    return deleteSpaceActionObject?.spaceType?.[spaceType];
  }

  public static getSpaceObjectStatusIconPath(status: SpaceStatus, health: SpaceHealth): string {
    switch (health) {
      case SpaceHealth.Green:
        return SpaceIconPath.GREEN;
      case SpaceHealth.Cold:
        return SpaceIconPath.COLD;
      case SpaceHealth.Hot:
        return SpaceIconPath.HOT;
      case SpaceHealth.Locked:
        return SpaceIconPath.LOCKED;
      default:
        return SpaceIconPath.UNKNOWN;
    }
  }

  public static getResourceDonutSegmentLabel(this: ManageSpacesClass, text: string, bytesUsed: string): string {
    const label = SpacesFormatter.getResourceDonutSegmentTextByCategory.apply(this, [text]);
    const filesize = sap.ui.core.format.FileSizeFormat.getInstance({
      binaryFilesize: false,
      maxFractionDigits: 2,
    }).format(bytesUsed);
    return `${label} (${filesize})`;
  }

  public static getResourceDonutSegmentTextByCategory(this: BaseControllerClass, category: ResourceCategory): string {
    switch (category) {
      case ResourceCategory.HANA_AUDIT_LOGS:
        return this.getText("ResourceCategoryAudit");
      case ResourceCategory.DWC_GLOBAL:
        return this.getText("ResourceCategoryDWCGlobal");
      case ResourceCategory.HANA_OTHER:
        return this.getText("ResourceCategoryOutsideDWC");
      case ResourceCategory.DWC_SPACES:
        return this.getText("ResourceCategorySpace");
      default:
        return this.getText("unknown");
    }
  }

  public static getStatusIcon(status: SpaceStatus) {
    switch (status) {
      case SpaceStatus.Locked:
        return SpaceIconPath.LOCKED;
      case SpaceStatus.Cold:
        return SpaceIconPath.COLD;
      case SpaceStatus.Critical:
        return SpaceIconPath.HOT;
      case SpaceStatus.Ok:
        return SpaceIconPath.GREEN;
      default:
        return SpaceIconPath.UNKNOWN;
    }
  }

  public static getStatusIconTooltip(this: ManageSpacesClass, status: SpaceStatus) {
    if (status) {
      return this.getText(`${status}Label`);
    }
    return "";
  }

  public static getSpaceObjectStatusState(status: SpaceStatus): string {
    switch (status) {
      case SpaceStatus.Ok:
        return sap.ui.core.ValueState.Success;
      case SpaceStatus.Cold:
        return sap.ui.core.ValueState.Information;
      case SpaceStatus.Critical:
        return sap.ui.core.ValueState.Warning;
      case SpaceStatus.Locked:
        return sap.ui.core.ValueState.Error;
      default:
        return sap.ui.core.ValueState.None;
    }
  }

  public static hasSpaceLockingPrivilege(updatePrivilege: any): boolean {
    return !!updatePrivilege;
  }

  public static getAuditInputValueState(this: BaseControllerClass, value: string) {
    const valid = SpacesFormatter.isValidAuditLogRetentionTime(parseFloat(value));
    return valid ? sap.ui.core.ValueState.None : sap.ui.core.ValueState.Error;
  }

  public static getAuditInputValueStateText(this: BaseControllerClass, value: string) {
    const valid = SpacesFormatter.isValidAuditLogRetentionTime(parseFloat(value));
    return !valid
      ? this.getText("incorrectRetentionTime", [
          minAuditLogRetentionTimeInDays.toString(),
          maxAuditLogRetentionTimeInDays.toString(),
        ])
      : "";
  }

  public static getSelectSourceLanguageValueState(this: BaseControllerClass, sourceLanguage: string) {
    return sourceLanguage === "unSelectedSourceLanguage" ? sap.ui.core.ValueState.Warning : sap.ui.core.ValueState.None;
  }

  public static getSelectSourceLanguageValueStateText(this: BaseControllerClass, sourceLanguage: string) {
    return sourceLanguage === "unSelectedSourceLanguage" ? this.getText("sourceLanguageRemovalWarning") : "";
  }

  public static isValidAuditLogRetentionTime(inputValue: number): boolean {
    return (
      Number.isInteger(inputValue) &&
      inputValue >= minAuditLogRetentionTimeInDays &&
      inputValue <= maxAuditLogRetentionTimeInDays
    );
  }

  public static getSpaceMonitoringTableTypeLabel(this: BaseControllerClass, tableType: StorageType): string {
    return tableType === StorageType.InMemory ? this.getText("inMemoryLabel") : this.getText("diskLabel");
  }

  public static formatSpaceStorageText(this: SpaceDetailsClass, capabilities: string[]): string {
    return capabilities?.includes("hdlfStorage")
      ? this.getText("dataLakeFilesText")
      : this.getText("diskAndInMemoryText");
  }

  public static isTenantEnabledForSpark(this: SpaceDetailsClass, compute: number): boolean {
    return compute > 0;
  }
}
