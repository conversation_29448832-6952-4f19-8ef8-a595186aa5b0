<mvc:View xmlns:core="sap.ui.core"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:ac="sap.cdw.components.reuse.control.actionchecker"
  xmlns:cd="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1"
  xmlns="sap.m" id="userDetails" controllerName="sap.cdw.components.managespaces.controller.details.users.UserDetails">
  <ac:ActionChecker
    id="userDetailsActionChecker"
    actionControlIds="addUsersButton,addUsersBtn"
    spaceType="{spaceDetails>/spaceType}"
    hiddenMode="true"
  >
  </ac:ActionChecker>
  <VBox class="sapUiTinyMarginBottom">
    <Table id="usersTable" growing="true" growingThreshold="10" items="{spaceDetails>/members/assignedUsers}" mode="MultiSelect" alternateRowColors="true"
      selectionChange="changeDeleteUserButtonState" noDataText="{i18n>noMembersAssigned}" visible="{= ${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} === false}">
      <headerToolbar>
        <Toolbar>
          <ToolbarSpacer />
          <Button id="addUsersButton" text="{i18n>add}" tooltip="{i18n>addUsers}" press="onOpenUserDialog"
            enabled="{ parts: [{value:'addUsersButton'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spacesFormatter.shouldBeEnabled'}"
            visible="{ parts: [{value:'addUsersButton'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spacesFormatter.shouldBeVisible'}"
            cd:actionId="managespaces/spacedetails/addSpaceMember"/>
          <Button tooltip="{i18n>deleteUsers}" text="{i18n>remove}" press="onDeleteUsers" id="usersTableDeleteButton" enabled="false"
            visible="{ parts: [{value:'usersTableDeleteButton'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spacesFormatter.shouldBeVisible'}" />
          <SearchField id="filterUsersInput" placeholder="{i18n>search}" liveChange="onFilterUsers" search="onSearchUsers" width="15rem" />
        </Toolbar>
      </headerToolbar>
      <columns>
        <Column minScreenWidth="Tablet" demandPopin="true" visible="true">
          <Text text="{i18n>member}" />
        </Column>
        <Column minScreenWidth="Tablet" demandPopin="true" visible="true">
          <Text text="{i18n>spaceRole}" />
        </Column>
         <Column width="80px" minScreenWidth="Tablet" hAlign="Center" demandPopin="true" visible="true">
          <Text text="{i18n>activeLabel}" />
        </Column>
      </columns>
      <items>
        <ColumnListItem highlight="{= ${spaceDetails>isNew} === true ? 'Information' : 'None' }">
          <cells>
            <ObjectIdentifier title="{spaceDetails>displayName} ({spaceDetails>name})" visible="true" />
            <Text visible="true" text="{ path:'spaceDetails>roles', formatter: '.spaceDetailsFormatter.getRoleString'}"/>
            <core:Icon src="{= ${spaceDetails>isNew} === true ? 'sap-icon://decline' : 'sap-icon://accept'}" color="{= ${spaceDetails>isNew} === true ? 'Negative' : 'Positive'}" tooltip="{= ${spaceDetails>isNew} === true ? ${i18n>disabled} : ${i18n>enabled}}"/>
          </cells>
        </ColumnListItem>
      </items>
    </Table>

    <Table id="usersTableNew" growing="true" growingThreshold="10" items="{spaceDetails>/users/assignedUsers}" mode="MultiSelect" alternateRowColors="true"
      selectionChange="changeDeleteUserButtonState" noDataText="{i18n>noUsersAssigned}" visible="{= ${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} === true}">
      <headerToolbar>
        <Toolbar>
          <ToolbarSpacer />
          <Button id="addUsersBtn" text="{i18n>add}" tooltip="{i18n>addUsersTooltip}" press="onOpenUserDialog"
            enabled="{ parts: [{value:'addUsersButton'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spacesFormatter.shouldBeEnabled'}"
            visible="{ parts: [{value:'addUsersButton'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spacesFormatter.shouldBeVisible'}"
            cd:actionId="managespaces/spacedetails/addSpaceMember"/>
          <Button tooltip="{i18n>editUsersTooltip}" text="{i18n>editUsers}" press="onPressEditUsers" id="editUsersBtn" enabled="false"
            visible="{ parts: [{value:'editUsersBtn'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spacesFormatter.shouldBeVisible'}"/>
          <Button tooltip="{i18n>removeUsersTooltip}" text="{i18n>remove}" press="onPressRemoveUsers" id="usersTableDeleteBtn" enabled="false"
            visible="{ parts: [{value:'usersTableDeleteBtn'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spacesFormatter.shouldBeVisible'}"/>
          <SearchField id="filterUsersIp" placeholder="{i18n>search}" liveChange="onFilterUsers" search="onSearchUsers" width="15rem" />
        </Toolbar>
      </headerToolbar>
      <columns>
        <Column minScreenWidth="Tablet" demandPopin="true" visible="true">
          <Text text="{i18n>user}" />
        </Column>
        <Column minScreenWidth="Tablet" demandPopin="true" visible="true">
          <Text text="{i18n>spaceScopedRole}" />
        </Column>
        <Column minScreenWidth="Tablet" hAlign="Center" demandPopin="true" visible="true">
          <Text text="{i18n>spaceAdmin}" />
        </Column>
      </columns>
      <items>
        <ColumnListItem>
          <cells>
            <ObjectIdentifier title="{spaceDetails>displayName} ({spaceDetails>userName})" visible="true" />
            <FlexBox
              items="{path: 'spaceDetails>roles', templateShareable:false}"
              wrap="Wrap">
              <CustomListItem>
                <Link class="sapUiTinyMarginEnd" text="{= ${spaceDetails>name} || ${spaceDetails>id}}" href="{ path:'spaceDetails>id', formatter: '.spaceDetailsFormatter.getRelativeEditUrlForRoleId'}" press="navigateToRoleEditor(${spaceDetails>id})"/>
              </CustomListItem>
            </FlexBox>
            <core:Icon src="sap-icon://accept" tooltip="{i18n>spaceAdminPrivileges}" visible="{= ${spaceDetails>isScopeAdmin} === true}" color="Positive"/>
          </cells>
        </ColumnListItem>
      </items>
    </Table>
  </VBox>
</mvc:View>
