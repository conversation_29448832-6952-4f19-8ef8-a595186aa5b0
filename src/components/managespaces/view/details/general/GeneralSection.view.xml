<mvc:View
  xmlns:core="sap.ui.core"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns="sap.m"
  xmlns:ac="sap.cdw.components.reuse.control.actionchecker"
  xmlns:dos="sap.cdw.components.reuse.control.dwcobjectstatus"
  xmlns:cd="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1"
  xmlns:mc="sap.suite.ui.microchart"
  id="generalSettings"
  controllerName="sap.cdw.components.managespaces.controller.details.general.GeneralSection"
>
  <ac:ActionChecker
    id="generalSettingsActionChecker"
    actionControlIds="diskStepInput,ramStepInput,enableStorageQuotaCheckBox"
    spaceType="{spaceDetails>/spaceType}"
    hanaState="{circuitbreaker>/DataHANA}"
    hanaProvisioningState="{circuitbreaker>/DataHANAProvisioningState}"
    hiddenMode="true"
  >
  </ac:ActionChecker>
  <VBox class="sapUiTinyMarginTop sapUiTinyMarginBottom">
    <HBox
      id="lockedMessageStripHBox"
      visible="{= ${spaceDetails>/runtimeData/status} === 'locked' }"
      class="sapUiSmallMarginBottom"
    >
      <VBox width="100%">
        <MessageStrip
          id="spaceLockedMessageStrip"
          text="{= ${path:'spaceDetails>/runtimeData/lockReason', formatter:'.spaceDetailsFormatter.getLockedSpaceReasonText'} }"
          type="Error"
          showIcon="true"
          customIcon="sap-icon://locked"
          showCloseButton="false"/>
      </VBox>
    </HBox>

    <HBox
      id="temporarilyUnlockedMessageStripHBox"
      visible="{=
                  ${spaceDetails>/runtimeData/status} !== 'locked' &amp;&amp; ${spaceDetails>/runtimeData/statusValidToUtc} !== undefined &amp;&amp; ${spaceDetails>/runtimeData/statusValidToUtc} !== null &amp;&amp; ${spaceDetails>/runtimeData/statusValidToUtc} !== '' &amp;&amp; (${spaceDetails>/runtimeData/disk/used} &gt; ${spaceDetails>/runtimeData/disk/assigned} || ${spaceDetails>/runtimeData/memory/used} &gt; ${spaceDetails>/runtimeData/memory/assigned}) &amp;&amp; ${spaceDetails>/runtimeData/disk/assigned} !== 0
                }"
    >
      <MessageStrip
        id="spaceTemporarilyUnlockedMessageStrip"
        text="{=
                    ${path:'spaceDetails>/runtimeData/statusValidToUtc', formatter:'.spaceDetailsFormatter.formatStatusValidToTimestamp'}
                  }"
        type="Warning"
        showIcon="true"
        showCloseButton="false"
        class="sapUiTinyMarginBottom"
      >
        <link>
        <Link
          id="unlockSpaceHelpLink"
          text="{i18n>unlockSpaceHelp}"
          press="toggleInAppHelp"
        />
        </link>
      </MessageStrip>
    </HBox>

    <HBox
      visible="{ parts: [{value:'generalSpaceDetails'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spaceDetailsFormatter.shouldBeVisible'}"
    >
      <VBox>
        <VBox
          id="spaceIdVBox"
          class="sapUiMediumMarginEnd"
          width="250px"
        >
          <Label
            id="spaceIdLabel"
            text="{i18n>spaceID}:"
          />
          <Input
            id="spaceID"
            value="{spaceDetails>/name}"
            class="spaceIDInput"
            enabled="false"
          />
        </VBox>
        <VBox
          id="creatorTextVBox"
          class="sapUiMediumMarginEnd sapUiSmallMarginTop"
        >
          <Label
            id="creatorTextLabel"
            text="{i18n>createdBy}:"
          />
          <Text
            id="creatorText"
            text="{= ${spaceDetails>/creatorDisplayName} ? ${spaceDetails>/creatorDisplayName} + ' (' + ${spaceDetails>/creator} + ')': ${spaceDetails>/creator}}"
          />
        </VBox>
      </VBox>
      <VBox>
        <VBox
          id="spaceNameVBox"
          class="sapUiMediumMarginEnd"
          width="250px"
        >
          <Label
            id="spaceNameLabel"
            text="{i18n>spaceName}:"
          />
          <Input
            id="spaceName"
            showSuggestion="false"
            valueLiveUpdate="true"
            maxLength="{ parts: [{path:'spaceDetails>/description'}], formatter: '.spaceDetailsFormatter.getMaxSpaceNameLength'}"
            valueState="{ parts: [{path:'spaceDetails>/description'}], formatter: '.spaceDetailsFormatter.getSpaceDescriptionValueState'}"
            valueStateText="{ parts: [{path:'spaceDetails>/description'}], formatter: '.spaceDetailsFormatter.getSpaceDescriptionValueStateText'}"
            value="{spaceDetails>/description}"
            class="sapUiSmallMarginEnd"
            enabled="{ parts: [{value:'spaceName'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spaceDetailsFormatter.shouldBeEnabled'}"
          />
        </VBox>
        <VBox
          id="creatorDateVBox"
          class="sapUiMediumMarginEnd sapUiSmallMarginTop"
        >
          <Label
            id="creatorDateLabel"
            text="{i18n>createdOn}:"
          />
          <Text
            id="creationDateText"
            text="{path:'spaceDetails>/creationDate', formatter:'.formatDateTime'}"
          />
        </VBox>
      </VBox>
      <VBox>
        <VBox
          id="statusSelectVBox"
          class="sapUiMediumMarginEnd"
        >
          <Label
            id="statusSelectLabel"
            text="{i18n>spaceStatus}:"
          />
          <Select
            enabled="false"
            id="statusSelect"
            selectedKey="{spaceDetails>/runtimeData/status}"
          >
            <items>
              <core:ListItem
                key="unknown"
                text="{i18n>unknownLabel}"
              />
              <core:ListItem
                key="active"
                text="{i18n>activeLabel}"
              />
              <core:ListItem
                key="cold"
                text="{i18n>activeLabel}"
              />
              <core:ListItem
                key="ok"
                text="{i18n>activeLabel}"
              />
              <core:ListItem
                key="critical"
                text="{i18n>activeLabel}"
              />
              <core:ListItem
                key="locked"
                text="{i18n>lockedLabel}"
              />
            </items>
          </Select>
        </VBox>
        <VBox id="deployStatusHBox" class="sapUiMediumMarginEnd sapUiSmallMarginTop">
          <Label
            id="deployStatusLabel"
            text="{i18n>deploymentStatus}:"
          />
          <HBox>
            <dos:DWCObjectStatus id="deployStatusText" class="sapUiTinyMarginEnd" statusType ="{spaceDetails>/objectStatus}" />
          </HBox>
        </VBox>
      </VBox>
      <VBox>
        <VBox
          id="spaceTypeVBox"
          class="sapUiMediumMarginEnd sapUiTinyMarginBottom"
        >
          <Label
            id="spaceTypeLabel"
            text="{i18n>spaceType}:"
          />
          <HBox class="sapUiTinyMarginTop">
            <core:Icon
              id="spaceTypeIcon"
              class="sapUiTinyMarginEnd"
              src="{parts:[{path:'spaceDetails>/spaceType'}], formatter:'.spaceDetailsFormatter.getSpaceTypeIconPath'}"
              color="Neutral"
            />
            <Text
              id="spaceTypeText"
              class="sapUiTinyMarginBottom"
              text="{parts:[{path: 'spaceDetails>/spaceType'}, {path: 'spaceDetails>/id'}], formatter:'.spaceDetailsFormatter.formatSpaceTypeText'}"
            />
          </HBox>
        </VBox>
        <VBox id="deployDateVBox" class="sapUiMediumMarginEnd sapUiTinyMarginTop">
          <Label
            id="deployDateLabel"
            text="{i18n>deployedOn}:"
          />
          <Text
            id="deployDateText"
            text="{path:'spaceDetails>/deploymentDate', formatter:'.spaceDetailsFormatter.formatDeployDateTime'}"
          />
        </VBox>
      </VBox>
    </HBox>
    <Label
      id="longDescriptionLabel"
      class="sapUiSmallMarginTop"
      text="{i18n>description}:"
      visible="{ parts: [{value:'spaceLongDescription'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spaceDetailsFormatter.shouldBeVisible'}"
    />
    <TextArea
      id="spaceLongDescription"
      maxLength="{ parts: [{path:'spaceDetails>/longDescription'}], formatter: '.spaceDetailsFormatter.getLongDescriptionMaxLength'}"
      width="100%"
      height="100px"
      wrapping="None"
      value="{spaceDetails>/longDescription}"
      valueLiveUpdate="true"
      visible="{ parts: [{value:'spaceLongDescription'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spaceDetailsFormatter.shouldBeVisible'}"
      enabled="{ parts: [{value:'spaceLongDescription'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spaceDetailsFormatter.shouldBeEnabled'}"
    />
    <VBox
      id="translationVBox"
      class="sapUiSmallMarginTop"
      visible="{ parts: [{value:'translationVBox'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spaceDetailsFormatter.shouldBeVisible'}"
    >
      <HBox alignItems="Center">
        <Label
          id="translationLabel"
          text="{i18n>translationLabel}:"
        />
        <Button
          id="translationLabelButton"
          icon="sap-icon://sys-help"
          type="Transparent"
          press="toggleInAppHelp"
          tooltip="{i18n>help}"
        />
      </HBox>
      <HBox
        id="sourceLanguageSelectionHBox"
        class="sapUiTinyMarginBegin"
        alignItems="Center"
      >
        <Label
          id="sourceLanguageLabel"
          class="sapUiSmallMarginEnd"
          text="{i18n>sourceLanguageLabel}:"
        />
        <Select
          id="sourceLanguageSelect"
          showSecondaryValues="true"
          forceSelection="false"
          change="sourceLanguageChange"
          selectedKey="{spaceDetails>/sourceLanguage}"
          items="{ path: 'spaceDetails>/supportedLanguages'}"
          enabled="{ parts: [{value:'sourceLanguageSelect'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spaceDetailsFormatter.shouldBeEnabled'}"
          valueState="{path: 'spaceDetails>/sourceLanguage', formatter: '.spaceDetailsFormatter.getSelectSourceLanguageValueState'}"
          valueStateText="{path: 'spaceDetails>/sourceLanguage', formatter: '.spaceDetailsFormatter.getSelectSourceLanguageValueStateText'}"
        >
          <core:ListItem
            key="{spaceDetails>}"
            text="{path: 'spaceDetails>', formatter: '.spaceDetailsFormatter.getFormatterLanguageLabel'}"
            additionalText="{= ${spaceDetails>} !== 'unSelectedSourceLanguage' ? ${spaceDetails>} : '' }"
          />
        </Select>
      </HBox>
    </VBox>
    <VBox
      id="spaceStorageVBox"
      class="sapUiSmallMarginTop"
    >
      <Label
        id="capabilitesLabel"
        text="{i18n>tableStorageType}:"
      />
      <HBox class="sapUiTinyMarginTop">
        <Text
          id="spaceStorageText"
          text="{path:'spaceDetails>/capabilities', formatter:'.spaceDetailsFormatter.formatSpaceStorageText'}"
        />
      </HBox>
    </VBox>
    <HBox
      id="storageAssignmentHBox"
      class="sapUiSmallMarginTop"
      visible="{ parts: [{value:'storageAssignmentHBox'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spaceDetailsFormatter.shouldBeVisible'}"
    >
      <VBox>
        <HBox>
          <VBox
            id="storageAssignmentVBox"
            class="sapUiMediumMarginEnd"
            justifyContent="Center"
          >
            <Label
              id="storrageAssignmentLabel"
              text="{i18n>storrageAssignmentLabel}:"
            />
            <CheckBox
              id="enableStorageQuotaCheckBox"
              select="toggleStorageAssignmentCheckBox"
              text="{i18n>enableSpaceQuota}"
              selected="{= ${spaceDetails>/isSpaceQuotaEnabled} === true }"
              cd:actionId="managespaces/spacedetails/changeSpaceQuota"
              enabled="{ parts: [{value:'enableStorageQuotaCheckBox'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spaceDetailsFormatter.shouldBeEnabled'}"
            ></CheckBox>
            <VBox visible="{= ${spaceDetails>/isSpaceQuotaEnabled} === true }">
              <HBox
                justifyContent="SpaceBetween"
                class="sapUiTinyMarginBegin"
                alignItems="Stretch"
              >
                <Label
                  id="diskLabel"
                  text="{i18n>storageLabel}:"
                  class="sapUiTinyMarginTop sapUiTinyMarginEnd"
                />
                <StepInput
                  id="diskStepInput"
                  displayValuePrecision="1"
                  step="1"
                  change="onChangeStorage"
                  width="128px"
                  value="{path:'spaceDetails>/assignedStorage', formatter:'.spaceDetailsFormatter.formatBytesToGB'}"
                  min="0.1"
                  cd:actionId="managespaces/spacedetails/changeSpaceQuota"
                  enabled="{ parts: [{value:'diskStepInput'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spaceDetailsFormatter.shouldBeEnabled'}"
                />
              </HBox>
              <HBox
                justifyContent="SpaceBetween"
                class="sapUiTinyMarginBegin"
                alignItems="Stretch"
              >
                <Label
                  id="ramLabel"
                  text="{i18n>ramLabel}:"
                  class="sapUiTinyMarginTop sapUiTinyMarginEnd"
                />
                <StepInput
                  id="ramStepInput"
                  cd:actionId="managespaces/spacedetails/changeSpaceQuota"
                  width="128px"
                  displayValuePrecision="1"
                  step="1"
                  change="onChangeRam"
                  value="{path:'spaceDetails>/assignedRam', formatter:'.spaceDetailsFormatter.formatBytesToGB'}"
                  min="0"
                  max="{path:'spaceDetails>/maxAssignableRamBytes', formatter:'.spaceDetailsFormatter.formatBytesToGB'}"
                  valueState="{ parts: [{path:'spaceDetails>/assignedRam'},{path:'spaceDetails>/minAssignableRamBytes'},{path:'spaceDetails>/maxAssignableRamBytes'},{path:'spaceDetails>/isSpaceQuotaEnabled'}], formatter: '.spaceDetailsFormatter.getAssignedRamValueState'}"
                  valueStateText="{ parts:
                  [{path:'spaceDetails>/assignedRam'},{path:'spaceDetails>/minAssignableRamBytes'},{path:'spaceDetails>/maxAssignableRamBytes'},{path:'spaceDetails>/isSpaceQuotaEnabled'}],
                  formatter: '.spaceDetailsFormatter.getAssignedRamValueStateText'}"
                  enabled="{ parts: [{value:'ramStepInput'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spaceDetailsFormatter.shouldBeEnabled'}"
                />
              </HBox>
            </VBox>
          </VBox>
          <VBox
            class="sapUiSmallMarginTop"
            id="accelerationVBox"
            justifyContent="Center"
            visible="{ parts: [{value:'accelerationVBox'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spaceDetailsFormatter.shouldBeVisible'}"
          >
            <Label
              id="accelerationText"
              text="{i18n>accelearationRAM}"
            />
            <mc:HarveyBallMicroChart
              id="accellerationChart"
              class="sapUiTinyMarginTop"
              width="100%"
              size="S"
              total="100"
              totalScale=""
              showTotal="false"
            >
              <mc:HarveyBallMicroChartItem
                fraction="{spaceDetails>/ramAccellerationInPercent}"
                color="Neutral"
                fractionScale="%"
              />
            </mc:HarveyBallMicroChart>
          </VBox>
        </HBox>
      </VBox>
    </HBox>

    <VBox
      id="dataLakeOptionVBox"
      class="sapUiSmallMarginTop"
      visible="{ parts: [{value:'dataLakeOptionVBox'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spaceDetailsFormatter.shouldBeVisible'}"
    >
      <HBox alignItems="Center">
        <Label
          id="dataLakeLabel"
          text="{i18n>dataLakeAccess}:"
        />
        <Button
          id="dataLakeHelpButton"
          icon="sap-icon://sys-help"
          type="Transparent"
          press="toggleInAppHelp"
          tooltip="{i18n>help}"
        />
      </HBox>
      <CheckBox
        id="dataLakeCheckBox"
        cd:actionId="managespaces/spacedetails/dataLakeAccess"
        selected="{spaceDetails>/enableDataLake}"
        select="onDataLakeOptionSelect"
        text="{i18n>dataLakeCheckBox}"
        enabled="{ parts: [{value:'dataLakeCheckBox'}, {path: 'spaceDetails>/'}, {path: 'privilege>/'}], formatter: '.spaceDetailsFormatter.shouldBeEnabled'}"
      />
    </VBox>
  </VBox>
</mvc:View>
