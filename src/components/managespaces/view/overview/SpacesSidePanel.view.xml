<mvc:View
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:core="sap.ui.core"
  xmlns="sap.m"
  xmlns:mc="sap.suite.ui.microchart"
  xmlns:f="sap.f"
  controllerName="sap.cdw.components.managespaces.controller.overview.SpacesSidePanel">
  <f:DynamicPage
    busyIndicatorDelay="0"
    busy="{= ${/allLoaded} === false}"
    class="sapUiNoContentPadding">
    <f:content class="noBorder">
        <Page id="spacesSidePanelPage" showHeader="false" showFooter="false" enableScrolling="false">
          <content>
            <VBox id="taskschedulerVBox" class="sapUiTinyMargin" visible="{ parts: [{value:'taskschedulerVBox'}, {path: 'privilege>/'}], formatter: '.spacesOverviewFormatter.isVisible'}">
              <mvc:XMLView id="ecnSchedulingAuth" viewName="sap.cdw.components.taskscheduler.view.TaskScheduleAuth"/>
              <mvc:XMLView id="ecnSchedulingDialog" viewName="sap.cdw.components.taskscheduler.view.TaskSchedule"/>
            </VBox>
            <Table
            id="spacesTable"
            width="auto"
            busy="{= ${/spacesLoaded} === false}"
            busyIndicatorDelay="0"
            mode="SingleSelectMaster"
            selectionChange="onAllSpacesListItemPress"
            class="sapUiTinyMarginTop sapUiTinyMarginBeginEnd sapUiMediumMarginBottom"
            >
            <headerToolbar>
              <OverflowToolbar class="noBorder">
                <Label text="{i18n>spaces}" design="Bold"/>
                <ToolbarSpacer />
              </OverflowToolbar>
            </headerToolbar>
            <columns>
              <Column/>
              <Column hAlign="End"/>
            </columns>
            <items>
              <ColumnListItem
                type="Navigation"
                id="allSpacesListItem"
              >
                <cells>
                  <Text text="{i18n>allSpaces}" />
                  <Text id="allSpacesCount" text="{path: '/spaceCount', formatter: '.spacesOverviewFormatter.formatSpaceCount'}" />
                </cells>
              </ColumnListItem>
              <ColumnListItem
                type="Navigation"
                id="deletedSpacesListItem"
                visible="{ parts: [{value:'deletedSpacesListItem'}, {path: 'privilege>/'}], formatter: '.spacesOverviewFormatter.isVisible'}"
              >
                <cells>
                  <Text text="{i18n>recycleBin}" />
                  <Text id="recycleBinSpacesCount" text="{path: '/spaceCountDeleted', formatter: '.spacesOverviewFormatter.formatSpaceCount'}" />
                </cells>
              </ColumnListItem>
            </items>
            </Table>
            <Table
            id="ecnsTable"
            width="auto"
            items="{ecn>/ecns}"
            busy="{= ${/ECNsLoaded} === false}"
            busyIndicatorDelay="0"
            mode="SingleSelectMaster"
            selectionChange="onECNListItemPress"
            class="sapUiTinyMargin"
            visible="{ parts: [{value:'ecnsTable'}, {path: 'privilege>/'}], formatter: '.spacesOverviewFormatter.isVisible'}"
            >
            <headerToolbar>
              <OverflowToolbar class="noBorder">
                <VBox>
                  <Label text="{i18n>ECNslong}" design="Bold"/>
                  <Label id="blockHoursLabel" text="{ path: 'ecn>/elasticityReadiness', formatter: '.spacesOverviewFormatter.getRemainingECNHoursText'}" visible="{= ${privilege>/SYSTEMINFO/read} === true &amp;&amp; ${privilege>/DWC_SPACES/assign} === true}"/>
                </VBox>
                <ToolbarSpacer />
                <HBox class="sapUiSmallMarginTop">
                  <Button
                    id="createECNSidePanelButton"
                    text="{i18n>create}"
                    press="onCreateECNButtonPress"
                    busy="{= ${/ECNCreated} === false}"
                    enabled="{parts:[{path: 'privilege>/'},{path: 'circuitbreaker>/DataHANA'},{path: 'ecn>/elasticityReadiness/isReady'}], formatter: '.spacesOverviewFormatter.isCreateECNBtnEnabled'}"
                    busyIndicatorDelay="0"/>
                  <Button
                    id="viewLogsButton"
                    text="{i18n>viewLogs}"
                    enabled="{parts:[{path: 'privilege>/'},{path: 'circuitbreaker>/DataHANA'}], formatter: '.spacesOverviewFormatter.isViewLogsBtnEnabled'}"
                    press="onViewLogsPress"/>
                  <Button
                    busyIndicatorDelay="0"
                    class="sapUiTinyMarginEnd"
                    id="refreshSidePanelButton"
                    icon="sap-icon://refresh"
                    type="Transparent"
                    busy="{= ${/ECNsLoaded} === false}"
                    enabled="{= ${privilege>/SYSTEMINFO/read} === true &amp;&amp; ${privilege>/DWC_SPACES/assign} === true}"
                    press="onRefreshButtonPress"/>
                </HBox>
              </OverflowToolbar>
            </headerToolbar>
            <columns>
              <Column />
            </columns>
            <items>
              <ColumnListItem
                id="availableECNS"
                type="Navigation"
                selected="{ecn>selected}"
              >
                <cells>
                    <HBox
                      width="100%"
                      alignItems="Start"
                      justifyContent="SpaceBetween"
                    >
                      <VBox class="sapUiSmallMarginBegin sapUiTinyMarginTop">
                        <Text id="ecnDisplayText" text="{= ${spaces>/objectNameDisplay} === 'technicalName' ? ${ecn>ecnId} : ${ecn>name}}"/>
                        <ObjectStatus
                          class="sapUiTinyMarginBottom"
                          state="{ path: 'ecn>', formatter: '.spacesOverviewFormatter.formatECNStatusState'}"
                          text="{ path: 'ecn>', formatter: '.spacesOverviewFormatter.formatECNStatus'}"
                        />
                        <!-- <Label
                          class="sapUiTinyMarginTop"
                          text="{ecn>schedule}"
                        /> -->
                      </VBox>
                      <!-- <VBox class="sapUiTinyMarginTop">
                        <Label text="{i18n>storage} {path: 'ecn>disk', formatter: '.spacesOverviewFormatter.formatResourceConsumption'}" />
                        <Label
                          class="sapUiTinyMarginTop"
                          text="{i18n>memory} {path: 'ecn>ram', formatter: '.spacesOverviewFormatter.formatResourceConsumption'}"
                        />
                      </VBox> -->
                      <VBox class="sapUiTinyMarginTop">
                        <HBox>
                          <ObjectStatus
                          class="sapUiTinyMarginEnd"
                            visible="{path: 'ecn>', formatter: '.spacesOverviewFormatter.hasECNSchedule'}"
                            icon="sap-icon://appointment"
                          />
                          <ObjectStatus id="addedSpacesCount"
                            text="{= ${ecn>spaces}.length}"
                            icon="sap-icon://sac/data-space-management"
                          />
                          <!-- <ObjectStatus
                            class="sapUiTinyMarginBegin"
                            text="{ecn>numberOfObjects}"
                            icon="sap-icon://documents"
                          /> -->
                        </HBox>
                      </VBox>
                    </HBox>
                </cells>
              </ColumnListItem>
            </items>
            </Table>
          </content>
        </Page>
    </f:content>
  </f:DynamicPage>
</mvc:View>
