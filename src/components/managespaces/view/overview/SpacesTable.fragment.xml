<core:FragmentDefinition xmlns:core="sap.ui.core"
  xmlns="sap.m"
  xmlns:ux="sap.uxap"
  xmlns:f="sap.ui.layout.form"
  xmlns:l="sap.ui.layout"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:c="sap.ui.core"
  xmlns:t="sap.ui.table"
  xmlns:dos="sap.cdw.components.reuse.control.dwcobjectstatus"
  xmlns:mc="sap.suite.ui.microchart">
  <t:Table
    id="spacesTable"
    width="100%"
    rowHeight="30px"
    rows="{spaces>/spaces/table}"
    alternateRowColors="true"
    visibleRowCountMode="Auto"
    selectionMode="Multi"
    selectionBehavior="RowSelector"
    cellClick=".triggerEditSpace"
    rowSelectionChange=".triggerSelectSpace"
    enableSelectAll="true"
    busy="{= ${/spacesLoaded} === false || ${/spaceResourcesLoaded} === false}"
    busyIndicatorDelay="0"
  >
    <t:columns>
      <t:Column
        id="nameColumn"
        sortProperty="name"
        autoResizable="true"
        width="200px"
      >
        <Text text="{i18n>spaceID}" />
        <t:template>
          <Text
            id="nameCell"
            text="{spaces>name}"
          />
        </t:template>
      </t:Column>
      <t:Column
        id="descriptionColumn"
        sortProperty="description"
        width="250px"
      >
        <Text text="{i18n>name}" />
        <t:template>
          <Text
            id="descriptionCell"
            text="{spaces>description}"
            wrapping="false"
          />
        </t:template>
      </t:Column>
      <t:Column
        id="statusColumn"
        sortProperty="runtimeData/status"
        hAlign="Center"
        resizable="false"
        width="70px"
      >
        <Text text="{i18n>status}" />
        <t:template>
          <ObjectStatus
            id="statusCell"
            tooltip="{path:'spaces>runtimeData/status', formatter:'.spacesOverviewFormatter.getStatusIconTooltip'}"
            icon="{path:'spaces>runtimeData/status', formatter:'.spacesOverviewFormatter.getStatusIcon'}"
            state="{path:'spaces>runtimeData/status', formatter:'.spacesOverviewFormatter.getSpaceObjectStatusState'}"
          />
        </t:template>
      </t:Column>
      <t:Column
        id="dataLakeColumn"
        sortProperty="runtimeData/dataLakeEnabled"
        hAlign="Center"
        width="100px"
      >
        <HBox>
          <core:Icon
            id="dataLakeTableIcon"
            src="sap-icon://sac/data"
            class="sapUiTinyMarginEnd"
          />
          <Text text="{i18n>dataLakeAccess}" />
        </HBox>
        <t:template>
          <core:Icon
            visible="{spaces>runtimeData/dataLakeEnabled}"
            id="datalakeEnabledIcon"
            src="sap-icon://accept"
            tooltip="{i18n>dataLakeTableEntryTooltip}"
            color="Positive"
          />
        </t:template>
      </t:Column>
      <t:Column
        id="deploymentStatusColumn"
        sortProperty="deployment_status"
        width="160px"
        autoResizable="true"
      >
        <Text text="{i18n>deploymentStatus}" />
        <t:template>
          <HBox>
            <dos:DWCObjectStatus id="deployStatusText" class="sapUiTinyMarginEnd" statusType ="{spaces>deployment_status}" />
          </HBox>
        </t:template>
      </t:Column>
      <t:Column
        id="deploymentDateColumn"
        sortProperty="deployment_date"
        autoResizable="true"
        width="160px"
      >
        <Text text="{i18n>deployedOn}" />
        <t:template>
          <Text
            id="deployDateText"
            text="{path:'spaces>deployment_date', formatter:'.spacesOverviewFormatter.formatDeployDateTime'}"
          />
        </t:template>
      </t:Column>
      <t:Column
        id="utilizedColumn"
        visible="{= ${/spaceResourcesLoaded} !== false}"
        sortProperty="runtimeData/disk/percent/used"
        resizable="false"
        width="150px"
      >
        <Text text="{i18n>tableStorageUtilization}" />
        <t:template>
          <VBox width="100%">
            <ProgressIndicator
              id="utilizedCell"
              busy="{= ${/spaceResourcesLoaded} === false}"
              busyIndicatorDelay="0"
              visible="{= ${parts:[{path: 'spaces>runtimeData/memory/assigned'},{path:'spaces>runtimeData/disk/assigned'}], formatter:'.spacesOverviewFormatter.shouldStorageUtilizationBarBeVisible'} === true}"
              class="spacesProgressIndicator"
              width="100%"
              height="0.75rem"
              percentValue="{spaces>runtimeData/disk/percent/used}"
              displayValue="{parts:[{path:'spaces>runtimeData/disk/percent/used'}, {path:'circuitbreaker>/DataHANA'}], formatter:'.spacesOverviewFormatter.formatPercentage'}%"
              showValue="true"
              displayAnimation="false"
              state="{path:'spaces>runtimeData/disk/percent/used', formatter:'.spacesOverviewFormatter.getStorageProgressIndicatorState'}"
            />
            <Text
              id="noQuotaText"
              text="{i18n>noSpaceQuota}"
              visible="{= ${parts:[{path: 'spaces>runtimeData/memory/assigned'},{path:'spaces>runtimeData/disk/assigned'}], formatter:'.spacesOverviewFormatter.hasNoSpaceQuota'} === true}"
            />
          </VBox>
        </t:template>
      </t:Column>
      <t:Column
        id="usedStorageColumn"
        visible="{= ${/spaceResourcesLoaded} !== false}"
        sortProperty="runtimeData/disk/used"
        autoResizable="true"
        width="100px"
        hAlign="Right"
      >
        <Text text="{i18n>usedStorage}" />
        <t:template>
          <Text
            id="usedStorageCell"
            text="{parts:[{path:'spaces>runtimeData/disk/used'},{path:'circuitbreaker>/DataHANA'},{value: 2}], formatter:'.spacesOverviewFormatter.getFormattedValue'}"
          />
        </t:template>
      </t:Column>
      <t:Column
        id="assignedStorageColumn"
        sortProperty="runtimeData/disk/assigned"
        autoResizable="true"
        width="130px"
        hAlign="Right"
      >
        <Text text="{i18n>assignedStorage}" />
        <t:template>
          <Text
            id="assignedStorageCell"
            text="{parts:[{path: 'spaces>runtimeData/disk/assigned'},{path: 'spaces>runtimeData/memory/assigned'},{path:'circuitbreaker>/DataHANA'}], formatter:'.spacesOverviewFormatter.getAssignedValue'}"
          />
        </t:template>
      </t:Column>
      <t:Column
        id="priorityColumn"
        sortProperty="runtimeData/priority"
        width="80px"
      >
        <Text text="{i18n>priority}" />
        <t:template>
          <HBox>
            <core:Icon
              useIconTooltip="false"
              src="{path:'spaces>runtimeData/priority', formatter:'.spacesOverviewFormatter.formatSpacePriorityIcon'}"
            />
            <Label
              class="sapUiTinyMarginBegin"
              id="priorityCell"
              text="{path:'spaces>runtimeData/priority', formatter:'.spacesOverviewFormatter.formatSpacePriority'}"
            />
          </HBox>
        </t:template>
      </t:Column>
      <t:Column
        id="usedRamColumn"
        visible="{= ${/spaceResourcesLoaded} !== false}"
        sortProperty="runtimeData/memory/used"
        autoResizable="true"
        width="100px"
        hAlign="Right"
      >
        <Text text="{i18n>usedRAM}" />
        <t:template>
          <Text
            id="usedRamCell"
            text="{parts:[{path:'spaces>runtimeData/memory/used'},{path:'circuitbreaker>/DataHANA'},{value: 2}], formatter:'.spacesOverviewFormatter.getFormattedValue'}"
          />
        </t:template>
      </t:Column>
      <t:Column
        id="assignedRamColumn"
        sortProperty="runtimeData/memory/assigned"
        autoResizable="true"
        width="130px"
        hAlign="Right"
      >
        <Text text="{i18n>assignedRAM}" />
        <t:template>
          <Text
            id="assignedRamCell"
            text="{parts:[{path: 'spaces>runtimeData/memory/assigned'},{path: 'spaces>runtimeData/disk/assigned'},{path:'circuitbreaker>/DataHANA'}], formatter:'.spacesOverviewFormatter.getAssignedValue'}"
          />
        </t:template>
      </t:Column>
      <t:Column
        id="usersColumn"
        sortProperty="{= ${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} === true ? 'spaceUserCount' : 'user_count'}"
        hAlign="Center"
        width="80px"
        visible="{parts:[{value: 'usersColumn'},{path: 'privilege>/'},{path:'circuitbreaker>/DataHANA'}], formatter: '.spacesOverviewFormatter.isVisible'}"
      >
        <HBox>
          <core:Icon
            src="sap-icon://group"
            class="sapUiTinyMarginEnd"
          />
          <Text text="{i18n>users}" />
        </HBox>
        <t:template>
          <Label
            class="sapUiTinyMarginBegin"
            id="usersCell"
            text="{= ${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} === true ? ${spaces>spaceUserCount} : ${spaces>user_count}}"
            visible="{path:'spaces>name', formatter:'.spacesOverviewFormatter.isUserCountVisible'}"
          />
        </t:template>
      </t:Column>
      <t:Column
        id="connectionsColumn"
        sortProperty="connection_count"
        hAlign="Center"
        width="120px"
      >
        <HBox>
          <core:Icon
            src="sap-icon://sac/connections"
            class="sapUiTinyMarginEnd"
          />
          <Text text="{i18n>connections}" />
        </HBox>
        <t:template>
          <Label
            class="sapUiTinyMarginBegin"
            id="connectionsCell"
            text="{spaces>connection_count}"
          />
        </t:template>
      </t:Column>
      <t:Column
        id="modelsColumn"
        sortProperty="model_count"
        hAlign="Center"
        width="90px"
      >
        <HBox>
          <core:Icon
            src="sap-icon://documents"
            class="sapUiTinyMarginEnd"
          />
          <Text text="{i18n>models}" />
        </HBox>
        <t:template>
          <Label
            class="sapUiTinyMarginBegin"
            text="{spaces>model_count}"
          />
        </t:template>
      </t:Column>
      <t:Column
        id="storageTypeColumn"
        sortProperty="capabilities"
        autoResizable="true"
        width="300px"
        hAlign="Left">
        <Text text="{i18n>tableStorageType}" />
        <t:template>
          <Text
            id="storageTypeCell"
            text="{path:'spaces>capabilities', formatter:'.spacesOverviewFormatter.formatSpaceStorageText'}"
          />
        </t:template>
      </t:Column>
      <t:Column
        id="usedHdlfStorageColumn"
        visible="{= ${/spaceResourcesLoaded} !== false}"
        sortProperty="runtimeData/hdlfStorage/used"
        autoResizable="true"
        width="100px"
        hAlign="Right">
        <Text text="{i18n>dataLakeFilesText}" />
        <t:template>
          <Text
            id="usedHdlfStorageCell"
            text="{parts:[{path:'spaces>runtimeData/hdlfStorage/used'},{path:'circuitbreaker>/DataHANA'},{value: 2}], formatter:'.spacesOverviewFormatter.getHdlfSpaceFormattedValue'}"
          />
        </t:template>
      </t:Column>
      <t:Column
        id="sparkVCPUsColumn"
        visible="{= ${/spaceResourcesLoaded} !== false}"
        sortProperty="runtimeData/spark/vCPUs"
        autoResizable="true"
        width="100px"
        hAlign="Right">
        <Text text="{i18n>sparkManagementVCPUs}" />
        <t:template>
          <Text
            id="sparkVCPUsCell"
            text="{spaces>runtimeData/spark/vCPUs}"
          />
        </t:template>
      </t:Column>

      <t:Column
      id="sparkMemoryColumn"
      visible="{= ${/spaceResourcesLoaded} !== false}"
      sortProperty="runtimeData/spark/memory"
      autoResizable="true"
      width="100px"
      hAlign="Right">
      <Text text="{i18n>sparkManagementMemory}" />
      <t:template>
        <Text
          id="sparkMemoryCell"
          text="{spaces>runtimeData/spark/memory}"
        />
      </t:template>
    </t:Column>

    </t:columns>
  </t:Table>
</core:FragmentDefinition>
