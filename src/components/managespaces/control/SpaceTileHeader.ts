/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { publishUi5 } from "../../reuse/utility/UIHelper";

export class SpaceTileHeaderClass extends sap.f.cards.BaseHeader {
  private spaceIdentifier: sap.m.ObjectIdentifier;
  private checkBox: sap.m.CheckBox;
  private container: sap.m.HBox;
  private static readonly SELECTED_CLASS: string = "spaceCardSelected";

  static get renderer() {
    return sap.ui.core.Renderer.extend("sap.cdw.components.managespaces.control.SpaceTileHeaderRenderer", {
      render: (rm, oControl) => {
        rm.write("<div");
        rm.writeControlData(oControl);
        rm.writeClasses();
        rm.write(">");
        rm.renderControl(oControl.container);
        rm.write("</div>");
      },
    });
  }

  public init(...args): void {
    sap.f.cards.BaseHeader.prototype.init.apply(this, args);
    this.createUI();
  }

  private createUI(): void {
    this.addStyleClass("sapFCardHeader");
    this.addStyleClass("spaceCardHeader");
    this.spaceIdentifier = new sap.m.ObjectIdentifier(this.getId() + "-identifier");
    const bundleName = require("../i18n/i18n.properties");
    const resourceModel = new sap.ui.model.resource.ResourceModel({ bundleName });
    const tooltip = resourceModel.getResourceBundle().getText("clickToSelect");
    this.checkBox = new sap.m.CheckBox(this.getId() + "-checkbox", { tooltip });
    this.checkBox.attachSelect(this.onPressCheckbox.bind(this));
    this.checkBox.addAriaLabelledBy(this.spaceIdentifier);
    this.checkBox.addAriaDescribedBy(this.spaceIdentifier);
    this.container = new sap.m.HBox({
      width: "100%",
      justifyContent: sap.m.FlexJustifyContent.SpaceBetween,
      alignItems: sap.m.FlexAlignItems.Start,
      items: [this.spaceIdentifier, this.checkBox],
    });
  }

  private onPressCheckbox(): void {
    this.setSelected(!this.getSelected());
    (this as any).fireOnSelect({ spaceId: this.getSpaceId(), selected: this.getSelected() });
  }

  public exit() {
    this.spaceIdentifier.destroy();
    this.checkBox.destroy();
    this.container.destroy();
  }

  public getSpaceId(): string {
    return this.getProperty("spaceId");
  }

  public setSpaceId(value: string) {
    this.setProperty("spaceId", value);
    this.spaceIdentifier.setTitle(value);
  }

  public getSpaceDescription(): string {
    return this.getProperty("spaceDescription");
  }

  public setSpaceDescription(value: string) {
    this.setProperty("spaceDescription", value);
    this.spaceIdentifier.setText(value);
  }

  public getSelected(): boolean {
    return this.getProperty("selected").toString() === "true";
  }

  public setSelected(selected = false) {
    this.setProperty("selected", selected);
    (this.getParent() as sap.f.Card).toggleStyleClass(SpaceTileHeaderClass.SELECTED_CLASS, selected);
    this.checkBox.setSelected(selected);
  }

  private _getTitle() {
    return { getId: () => "" };
  }

  public static get metadata() {
    return {
      interfaces: ["sap.f.cards.IHeader"],
      properties: {
        spaceId: {
          type: "string",
          defaultValue: "spaceId",
        },
        spaceDescription: {
          type: "string",
          defaultValue: "spaceDescription",
        },
        selected: {
          type: "boolean",
          defaultValue: "false",
        },
      },
      events: {
        onSelect: {
          parameters: {
            value: {
              type: "object",
            },
          },
        },
      },
    };
  }
}

export const SpaceTileHeader: typeof SpaceTileHeaderClass = publishUi5(
  SpaceTileHeaderClass,
  "sap.cdw.components.managespaces.control.SpaceTileHeader"
);
