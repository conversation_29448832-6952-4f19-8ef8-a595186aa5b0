/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { MAX_LENGTH_SPACENAME } from "../../../../../shared/spaces/constants";
import { MessageHandler, ObjectType } from "../../../reuse/utility/MessageHandler";
import { publishUi5 } from "../../../reuse/utility/UIHelper";
import { SpacesFormatter } from "../../formatter/SpacesFormatter";
import { MAX_LENGTH_SPACEID } from "../../utility/Constants";
import { DialogController } from "../../utility/DialogController";

export interface ICreateSpaceViewData {
  spaceNames: string[];
  copyingSpaceId?: string;
  copyingSpaceLabel?: string;
}
export interface ICreateSpaceResult {
  spaceName: string;
  spaceID: string;
  deployContent?: boolean;
  spaceStorage?: string;
}

class CreateSpaceDialogClass extends DialogController<ICreateSpaceViewData, ICreateSpaceResult> {
  private view: sap.ui.core.mvc.View;
  private spaceNames: string[];
  private spaceIDInput: sap.m.Input;
  private spaceNameInput: sap.m.Input;
  private deployObjectsCheckBox: sap.m.CheckBox;
  private spaceStorageRadioButtonGroup: sap.m.RadioButtonGroup;
  public spacesFormatter: SpacesFormatter = SpacesFormatter;
  private model: sap.ui.model.json.JSONModel;

  public onInit(): void {
    this.setupViews();
    this.setupModels();
    if (this.model.getProperty("/isCopySpace")) {
      this.spaceNameInput.setValue(`${this.getViewData().copyingSpaceLabel}_Copy`);
      this.spaceIDInput.setValue(`${this.getViewData().copyingSpaceId}_COPY`);
      this.validate();
    }
  }

  getResult() {
    return {
      spaceName: this.spaceNameInput.getValue(),
      spaceID: this.spaceIDInput.getValue(),
      deployContent: this.deployObjectsCheckBox.getSelected(),
      spaceStorage: this.spaceStorageRadioButtonGroup.getSelectedIndex() === 0 ? "" : "hdlfStorage",
    };
  }

  public onSubmit(): void {
    this.validate().then(() => {
      const okButton = this.byId("ok") as sap.m.Button;
      okButton.firePress();
    });
  }

  public async onSpaceNameChange(event: sap.ui.base.Event): Promise<void> {
    const newName: string = event.getParameter("value");
    if (newName.length <= MAX_LENGTH_SPACENAME) {
      this.setInputState(this.spaceNameInput, sap.ui.core.ValueState.None, "");
      this.spaceNameInput.setValue(newName);
    } else {
      this.setInputState(
        this.spaceNameInput,
        sap.ui.core.ValueState.Error,
        this.getText("spaceNameLengthWarning", [MAX_LENGTH_SPACENAME.toString()])
      );
      this.spaceNameInput.setValue(newName.slice(0, MAX_LENGTH_SPACENAME));
    }
    this.setIDInput(newName.toUpperCase(), true);
  }

  public onSpaceIDChange(event: sap.ui.base.Event): void {
    const newValue: string = (event.getParameter("value") as string).toUpperCase();
    this.setIDInput(newValue, false);
  }

  public onSpaceStorageChange(event: sap.ui.base.Event): void {
    const selectedIndex = event.getParameters().selectedIndex;
    this.spaceStorageRadioButtonGroup.setSelectedIndex(selectedIndex);
  }

  public onAfterRendering(): void {
    const cancelButton = this.getView().getParent()["getEndButton"]() as sap.m.Button;
    const originalFirePress = cancelButton.firePress;
    const messageBundle = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../../../reuse/utility/i18n/i18n.properties"),
    }).getResourceBundle();
    const that = this;
    cancelButton.firePress = function (this: sap.m.Button, mParameters?: any): sap.m.Button {
      if (that?.isDirty?.()) {
        MessageHandler.confirm(
          messageBundle.getText("isDirtyConfirmationMessage"),
          messageBundle.getText("isDirtyConfirmationTitle"),
          null,
          null,
          null,
          (action: sap.m.MessageBox.Action) => {
            if (action === sap.m.MessageBox.Action.OK) {
              originalFirePress.call(this, mParameters);
            }
          }
        );
      } else {
        originalFirePress.call(this, mParameters);
      }

      return this;
    };
  }

  public isDirty() {
    return this.spaceIDInput.getValue().length > 0 || this.spaceNameInput.getValue().length > 0;
  }

  private async checkSpaceId(
    spaceId: string
  ): Promise<{ state: sap.ui.core.ValueState; text: string; derivationResults: any }> {
    return MessageHandler.validateName(
      ObjectType.Space,
      spaceId,
      this.spaceNames,
      {
        messageBundleId: "sap.cdw.components.managespaces.i18n.i18n",
        messageKeys: {
          nameValidator_DuplicateString: { messageKey: "spaceIDInputDuplicateError" },
          nameValidator_InvalidCharacter: { messageKey: "spaceIDInputInValidError" },
          nameValidator_NameTooShort: { messageKey: "spaceIDInputEmptyValidationError" },
          nameValidator_NameTooLong: { messageKey: "spaceIdLengthWarning" },
          nameValidator_ReservedPrefix: { messageKey: "spaceIdnWarningReservedPrefix" },
        },
      },
      { computeDerivedName: true }
    );
  }

  validate(): Promise<void> {
    return new Promise((resolve, reject) => {
      (async () => {
        const { state, text } = await this.checkSpaceId(this.spaceIDInput.getValue().toUpperCase());
        const spaceName = this.spaceNameInput.getValue();
        let valid = state === sap.ui.core.ValueState.None || state === sap.ui.core.ValueState.Warning;
        this.setInputState(this.spaceIDInput, state, text);
        if (!spaceName) {
          this.setInputState(
            this.spaceNameInput,
            sap.ui.core.ValueState.Error,
            this.getText("spaceNameEmptyValidationError")
          );
          valid = false;
        } else if (spaceName.length > MAX_LENGTH_SPACENAME) {
          this.setInputState(
            this.spaceNameInput,
            sap.ui.core.ValueState.Error,
            this.getText("spaceNameLengthWarning", [MAX_LENGTH_SPACENAME.toString()])
          );
          valid = false;
        } else {
          this.setInputState(this.spaceNameInput, sap.ui.core.ValueState.None, "");
        }
        valid ? resolve() : reject({ suppress: true });
      })();
    });
  }

  private setInputState(input: sap.m.Input, state: sap.ui.core.ValueState, stateText: string): void {
    input.setValueState(state);
    input.setValueStateText(stateText);
    if (state === sap.ui.core.ValueState.None) {
      input.closeValueStateMessage();
    } else {
      input.openValueStateMessage();
    }
  }

  private setupViews(): void {
    this.view = this.getView();
    this.spaceIDInput = this.view.byId("spaceIDInput") as sap.m.Input;
    this.spaceNameInput = this.view.byId("spaceNameInput") as sap.m.Input;
    this.deployObjectsCheckBox = this.view.byId("deployObjectsCheckBox") as sap.m.CheckBox;
    this.spaceStorageRadioButtonGroup = this.view.byId("spaceStorageRadioButtonGroup") as sap.m.RadioButtonGroup;
  }

  private setupModels(): void {
    this.model = new sap.ui.model.json.JSONModel({
      isCopySpace: Boolean(this.getViewData().copyingSpaceId),
    });
    this.spaceNames = this.getViewData().spaceNames;
    this.view.setModel(this.model);
  }

  private async setIDInput(newValue: string, useDerivedName: boolean): Promise<void> {
    let { state, text, derivationResults } = await this.checkSpaceId(newValue);
    if (useDerivedName && state !== sap.ui.core.ValueState.Warning) {
      state = sap.ui.core.ValueState.None;
    }
    this.spaceIDInput.setValue(useDerivedName ? derivationResults?.derivedName : newValue);
    if (newValue.length > MAX_LENGTH_SPACEID) {
      this.spaceIDInput.setValue(derivationResults?.derivedName);
      state = sap.ui.core.ValueState.Error;
    }
    this.setInputState(this.spaceIDInput, state, text);
  }
}

export const CreateSpaceDialog = publishUi5(
  CreateSpaceDialogClass,
  "sap.cdw.components.managespaces.controller.dialog.CreateSpaceDialog"
);
