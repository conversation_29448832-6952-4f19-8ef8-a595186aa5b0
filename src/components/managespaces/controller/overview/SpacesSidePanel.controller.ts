/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { showDialogExt } from "@sap/dwc-uihelper";
import { ShellNavigationService } from "@sap/orca-shell";
import {
  EcnPhaseSnapshotText,
  IECN,
  IECNInstance,
  IECNMetadataSpace,
  IECNMetadataSpaceAsgmtStrtg,
  IECNMetadataSpaceObject,
  IECNSizingPlan,
} from "../../../../../shared/ecn/ecnMetadataTypes";
import {
  BaseController,
  BaseControllerClass,
  smartExtend,
} from "../../../basecomponent/controller/BaseController.controller";
import { Views } from "../../../monitoring/utility/Enums";
import { notificationsEventChannel, notificationsFetched } from "../../../notifications/utility/Types";
import { MessageHandler } from "../../../reuse/utility/MessageHandler";
import { ShellContainer } from "../../../shell/utility/Container";
import { setHelpScreenId } from "../../../shell/utility/WebAssistantHelper";
import { Activity, ApplicationId, SpaceRequirement } from "../../../tasklog/utility/Constants";
import {
  ITTaskScheduleController,
  ITaskScheduleRequest,
} from "../../../taskscheduler/controller/TaskSchedule.controller";
import { getTaskScheduer, recordAction } from "../../../taskscheduler/utility/ScheduleUtil";
import { SpacesOverviewFormatter } from "../../formatter/SpacesOverviewFormatter";
import { ECNModel } from "../../model/ECNModel";
import { NO_ECN, SCHEDULE_ECN_KEYWORDS } from "../../utility/Constants";
import { ECNActions, SpaceOverviewView, SpacesModelDataType } from "../../utility/Enums";
import { forbidden } from "../../utility/ManageSpacesUtility";
import { NavigationInfo } from "../../utility/Types";
import { IAssignObjectsDialogResult, IAssignObjectsDialogViewData } from "../dialog/AssignObjectsDialog.controller";
import { ICreateECNDialogViewData } from "../dialog/CreateECNDialog.controller";
import {
  ISpaceAssignmentDialogResult,
  ISpaceAssignmentDialogViewData,
} from "../dialog/SpaceAssignmentDialog.controller";
import { ManageSpacesClass } from "./ManageSpaces.controller";

export class SpacesSidePanelClass extends BaseControllerClass {
  public static NAME = "SpacesSidePanel";
  public readonly spacesOverviewFormatter: SpacesOverviewFormatter = SpacesOverviewFormatter;
  private view: sap.ui.core.mvc.View;
  private model: sap.ui.model.json.JSONModel;
  private ecnModel: ECNModel;
  private router: sap.m.routing.Router;
  private eventBus: sap.ui.core.EventBus;
  private ecnId: string;
  private name: string;
  private sizingPlan: IECNSizingPlan;
  private changedAt: Date;
  private changedBy: string;
  private DWCO_SPACES_LOGICAL_DELETE: boolean;
  private DWCO_REUSABLE_TASK_SCHEDULING_UI: boolean;
  private DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION: boolean;

  public onInit(): void {
    super.onInit();
    this.view = this.getView();
    this.setupListeners();
    this.setupModels();
    this.setupRouter();
  }

  private setupListeners(): void {
    this.eventBus = this.getOwnerComponent().getEventBus();
    this.eventBus.subscribe(
      ManageSpacesClass.NAME,
      "onServiceCallStarted",
      (channel: string, event: string, details: any) => {
        this.onServiceCallStarted.apply(this, [details]);
      },
      this
    );
    this.eventBus.subscribe(
      ManageSpacesClass.NAME,
      "onServiceCallCompleted",
      (channel: string, event: string, details: any) => {
        this.onServiceCallCompleted.apply(this, [details]);
      },
      this
    );
    this.eventBus.subscribe(
      ManageSpacesClass.NAME,
      "press",
      (channel: string, event: string, details: any) => {
        this.onECNAction.apply(this, [details]);
      },
      this
    );
  }

  private async onECNAction(details: any): Promise<void> {
    const key: ECNActions = details?.key;
    const ecnId = this.model.getProperty("/currentECNID");
    switch (key) {
      case ECNActions.START:
        await this.ecnModel.startECN(ecnId);
        break;
      case ECNActions.STOP:
        await this.ecnModel.stopECN(ecnId);
        break;
      case ECNActions.STATUS:
        // TODO status display
        break;
      case ECNActions.EDIT:
        this.openEditECNDialog();
        break;
      case ECNActions.ASSIGN:
        this.openSpaceAssignmentDialog();
        break;
      case ECNActions.ASSIGN_OBJECTS:
        const { selectedSpaces } = details;
        this.openAssignObjectsDialog(selectedSpaces[0].name, selectedSpaces[0].asgmtStrtg);
        break;
      case ECNActions.REMOVE_OBJECTS:
        this.eventBus.publish(SpacesSidePanelClass.NAME, "removeObjects", {
          ecnId: this.model.getProperty("/currentECNID"),
          ecnBusinessName: this.name,
          ecnModel: this.ecnModel,
          autoAssignedSpaceIds: this.ecnModel
            .getProperty("/currentECN")
            .spaces.filter((space) => space.asgmtStrtg === IECNMetadataSpaceAsgmtStrtg.automatic)
            .map((space) => space.spaceId),
        });
        break;
      case ECNActions.REMOVE_SPACES:
        const currentECNID = this.model.getProperty("/currentECNID");
        const spacesToUnassign = details.selectedSpaces.map((spaceId) => ({
          spaceId,
          isSelected: false,
          objects:
            this.ecnModel
              .getProperty("/currentECN")
              .spaces.filter(
                (space) => space.spaceId === spaceId && space.asgmtStrtg === IECNMetadataSpaceAsgmtStrtg.manual
              )[0]
              ?.objects.map((object) => ({ ...object, isEnable: false })) || [],
        }));
        await this.ecnModel.assignToECN(
          currentECNID,
          spacesToUnassign,
          undefined,
          SpacesModelDataType.SPACE_REMOVAL_ECN
        );
        break;
      case ECNActions.DELETE:
        this.openDeleteECNDialog();
        break;
      case ECNActions.CREATE_SCHEDULE:
      case ECNActions.EDIT_SCHEDULE:
      case ECNActions.DELETE_SCHEDULE:
      case ECNActions.CHANGE_OWNER_SCHEDULE:
      case ECNActions.PAUSE_SCHEDULE:
      case ECNActions.RESUME_SCHEDULE:
        this.handleSchedule(key);
        break;
      case ECNActions.VIEW_LOGS:
        const ecnID = this.model.getProperty("/currentECNID");
        this.navigateToSystemMonitor(ecnID);
        break;
      default:
        forbidden(key);
    }
  }

  public async onSchedulingNotification(channel: string, eventName: string, data: any): Promise<void> {
    if (channel === notificationsEventChannel && eventName === notificationsFetched && data?.length) {
      const title: string = data[0].title?.toLowerCase();
      const hasKeyword = (text: string, word: string): boolean => {
        const regex = new RegExp(word, "i");
        return regex.test(text);
      };
      if (
        title &&
        hasKeyword(title, SCHEDULE_ECN_KEYWORDS.mandatory) &&
        SCHEDULE_ECN_KEYWORDS.optional.some((word) => hasKeyword(title, word))
      ) {
        this.setSchedulingMenuBusy(false);
        this.fetchData();
      }
    }
  }

  public setSchedulingMenuBusy(isBusy: boolean): void {
    if (isBusy) {
      sap.ui
        .getCore()
        .getEventBus()
        .subscribe(notificationsEventChannel, notificationsFetched, this.onSchedulingNotification.bind(this));
    } else {
      sap.ui
        .getCore()
        .getEventBus()
        .unsubscribe(notificationsEventChannel, notificationsFetched, this.onSchedulingNotification.bind(this));
    }
    (
      sap.ui
        .getCore()
        .byId("shellMainContent---managespacesComponent---manageSpaces--title--scheduleMenu") as sap.m.MenuButton
    )?.setBusy(isBusy);
  }

  private setupRouter(): void {
    this.router = sap.ui.core.UIComponent.getRouterFor(this) as sap.m.routing.Router;
    this.router.getRoute("overview").attachPatternMatched((event) => {
      const privilegeService = ShellContainer.get().getPrivilegeService();
      if (
        privilegeService.hasGlobalPrivilege("SYSTEMINFO", "read") &&
        privilegeService.hasGlobalPrivilege("DWC_SPACES", "assign")
      ) {
        if (!this.ecnModel.getProperty("/elasticityReadiness")) {
          this.ecnModel.fetchECNReadiness();
        }
        this.onOverviewRouteMatched.apply(this, [event]);
      } else {
        const { view } = event.getParameter("arguments")?.["?query"] || {};
        this.eventBus.publish(SpacesSidePanelClass.NAME, "openView", {
          navigationInfo: {
            view: view || SpaceOverviewView.TILE,
            ecn: "",
            spaceIds: [],
            isActive: false,
            ecnBusinessName: "",
          } as NavigationInfo,
          ECNData: {},
          elasticityReadiness: {},
        });
      }
    });
  }

  private async onOverviewRouteMatched(event: sap.ui.base.Event): Promise<void> {
    let { view, ecn, showDeleted } = event.getParameter("arguments")?.["?query"] || {};
    view = view || SpaceOverviewView.TILE;
    ecn = ecn || NO_ECN;
    this.model.setProperty("/currentECNID", ecn);
    this.model.setProperty("/currentView", view);
    this.model.setProperty("/showDeleted", showDeleted);
    const ecns: IECNInstance[] = await this.ecnModel.fetchData();
    if (this.ecnId === ecn) {
      this.ecnModel.fetchECNReadiness();
    }
    if (ecn === NO_ECN) {
      this.handleNonECNContext(view, showDeleted);
      return;
    }
    const ecnData: IECNInstance = ecns?.find((e) => e.ecnId === ecn);
    if (!ecnData) {
      this.router.navTo("overview", { query: { view, ecn: NO_ECN } });
      return;
    }

    await this.handleECNContext(view, ecn, ecnData);
  }

  private handleNonECNContext(view: string, showDeleted?: string) {
    setHelpScreenId(""); // reset screen Id back to managespaces component
    // pre-select the correct space list item only if not ecn context
    const spacesTableItemToSelect =
      showDeleted === "true"
        ? this.view.byId("deletedSpacesListItem")?.getId()
        : this.view.byId("allSpacesListItem")?.getId();
    this.getSpacesTable()?.setSelectedItemById(spacesTableItemToSelect, true);
    this.eventBus.publish(SpacesSidePanelClass.NAME, "openView", {
      navigationInfo: {
        view,
        ecn: NO_ECN,
        showDeleted,
      },
      ECNData: this.ecnModel.getProperty("/currentECN"),
      elasticityReadiness: this.ecnModel.getProperty("/elasticityReadiness"),
    });
  }

  private async handleECNContext(view: string, ecn: string, ecnData: IECNInstance): Promise<void> {
    this.ecnId = ecnData.ecnId;
    this.name = ecnData.name;
    this.sizingPlan = ecnData.sizingPlan;
    this.changedAt = ecnData.changedAt;
    this.changedBy = ecnData.changedBy;
    const ecnSpaceMetaData: IECNMetadataSpace[] = ecnData.spaces;
    const spaceIds: string[] = ecnSpaceMetaData.map((space) => space.spaceId);
    this.model.setProperty("/currentSpaceIds", spaceIds);
    setHelpScreenId("ECNDetails"); // set ECN help screen Id
    await this.ecnModel.fetchECN(ecn);
    this.model.setProperty("/currentECN", this.ecnModel.getProperty("/currentECN"));
    await this.updateAssignedObjectsFromRepo(ecnSpaceMetaData);
    ecnData.selected = true;
    this.ecnModel.refresh();
    this.eventBus.publish(SpacesSidePanelClass.NAME, "openView", {
      navigationInfo: {
        view,
        ecn,
        spaceIds,
        isActive:
          ecnData?.runDetails?.phase === EcnPhaseSnapshotText.ACTIVE ||
          ecnData?.runDetails?.phase === EcnPhaseSnapshotText.STOPPING,
        ecnBusinessName: this.name,
      } as NavigationInfo,
      ECNData: this.ecnModel.getProperty("/currentECN"),
      elasticityReadiness: this.ecnModel.getProperty("/elasticityReadiness"),
    });
  }

  private setupModels(): void {
    this.model = new sap.ui.model.json.JSONModel({
      currentView: SpaceOverviewView.TILE,
      currentECNID: NO_ECN,
      currentSpaceIds: [],
      spacesLoaded: undefined,
    });
    this.view.setModel(this.model);
    this.ecnModel = new ECNModel(
      { ecns: [] as IECNInstance[] },
      this.onServiceCallStarted.bind(this),
      this.onServiceCallCompleted.bind(this)
    );
    this.view.setModel(this.ecnModel, "ecn");
    this.view.setModel(sap.ui.getCore().getModel("featureflags"), "featureflags");
    const { DWCO_SPACES_LOGICAL_DELETE, INFRA_DWC_TWO_TENANT_MODE } = this.view.getModel("featureflags").getData();
    this.DWCO_SPACES_LOGICAL_DELETE = DWCO_SPACES_LOGICAL_DELETE && INFRA_DWC_TWO_TENANT_MODE;
    this.DWCO_REUSABLE_TASK_SCHEDULING_UI = this.view
      .getModel("featureflags")
      .getData().DWCO_REUSABLE_TASK_SCHEDULING_UI;
    this.DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION = this.view
      .getModel("featureflags")
      .getData().DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION;
  }

  private onServiceCallStarted(event: sap.ui.base.Event): void {
    const { type }: { type: SpacesModelDataType } = event.getParameters();
    this.model.setProperty(`/${type}WithError`, false);
    this.model.setProperty(`/${type}`, false);
  }

  private onServiceCallCompleted(event: sap.ui.base.Event): void {
    const { type, error, details }: { type: SpacesModelDataType; error: any; details: any } = event.getParameters();
    this.model.setProperty(`/${type}WithError`, !!error);
    this.model.setProperty(`/${type}`, true);
    if (!error) {
      this.handleServiceCallSuccess(type, details);
    } else {
      this.handleServiceCallError(type, error, details);
    }
  }

  private async handleServiceCallSuccess(type: SpacesModelDataType, details: any) {
    switch (type) {
      case SpacesModelDataType.SPACES:
        if (details?.updateSpaceCount) {
          this.model.setProperty(!details.logical ? "/spaceCount" : "/spaceCountDeleted", details.spacesCount);
        }
        break;
      case SpacesModelDataType.SPACES_COUNT:
        this.model.setProperty("/spaceCount", details.spacesCount);
        this.model.setProperty("/spaceCountDeleted", details.deletedSpacesCount);
        break;
      case SpacesModelDataType.CREATE_ECN:
        MessageHandler.success(this.getText("createEcnSuccessMessage"));
        break;
      case SpacesModelDataType.UPDATE_ECN:
        MessageHandler.success(this.getText("updateEcnSuccessMessage"));
        break;
      case SpacesModelDataType.DELETE_ECN:
        MessageHandler.success(this.getText("deleteEcnSuccessMessage"));
        break;
      case SpacesModelDataType.START_ECN:
        MessageHandler.success(this.getText("startingEcnSuccessMessage", [this.name]));
        await this.ecnModel.fetchData();
        break;
      case SpacesModelDataType.STOP_ECN:
        MessageHandler.success(this.getText("stoppingEcnSuccessMessage", [this.name]));
        await this.ecnModel.fetchData();
        break;
      case SpacesModelDataType.OBJECT_REMOVAL_ECN:
        await this.fetchData("consumptionObjects", false);
        MessageHandler.success(this.getText("removeObjectsSuccessMessage"));
        break;
      case SpacesModelDataType.ASSIGNMENT_ECN:
        await this.fetchData("spaces", false);
        break;
      case SpacesModelDataType.SPACE_REMOVAL_ECN:
        await this.fetchData("spaces", false);
        MessageHandler.success(
          details.multipleSpaces
            ? this.getText("spacesUnassignedSuccessMessage")
            : this.getText("spaceUnassignedSuccessMessage")
        );
        break;
      case SpacesModelDataType.OBJECT_ASSIGNMENT_ECN:
        await this.fetchData("spaces", false);
        this.eventBus.publish(SpacesSidePanelClass.NAME, "objectsAssigned", null);
        MessageHandler.success(this.getText("assignObjectsSuccessMessage"));
        break;
      default:
        break;
    }
  }

  private handleServiceCallError(type: SpacesModelDataType, error: any, details: any) {
    switch (type) {
      case SpacesModelDataType.CREATE_ECN:
        MessageHandler.exception({
          message: this.getText("createEcnError"),
          exception: error,
        });
        break;
      case SpacesModelDataType.UPDATE_ECN:
        MessageHandler.exception({
          message: this.getText("udpateEcnError"),
          exception: error,
        });
        break;
      case SpacesModelDataType.DELETE_ECN:
        MessageHandler.exception({
          message: this.getText("deleteEcnError"),
          exception: error,
        });
        break;
      case SpacesModelDataType.START_ECN:
        MessageHandler.exception({
          message: this.getText("startingEcnError"),
          exception: error,
        });
        break;
      case SpacesModelDataType.STOP_ECN:
        MessageHandler.exception({
          message: this.getText("stoppingEcnError"),
          exception: error,
        });
        break;
      case SpacesModelDataType.ASSIGNMENT_ECN:
      case SpacesModelDataType.SPACE_REMOVAL_ECN:
        MessageHandler.exception({
          message: this.getText("spacesAssignUnassignError"),
          exception: error,
        });
        break;
      case SpacesModelDataType.OBJECT_REMOVAL_ECN:
        MessageHandler.exception({
          message:
            this.view.getModel("spaces").getProperty("/selectedObjects").length > 1
              ? this.getText("removeObjectsError")
              : this.getText("removeObjectError"),
          exception: error,
        });
        break;
      case SpacesModelDataType.OBJECT_ASSIGNMENT_ECN:
        MessageHandler.exception({
          message: details.multipleObjects ? this.getText("removeObjectsError") : this.getText("removeObjectError"),
          exception: error,
        });
        break;
      default:
        break;
    }
  }

  private deleteECNConfirmationDialog(): Promise<void> {
    return new Promise((resolve, reject) => {
      const id = this.getView().createId("-deleteECNDialog");
      const confirmDeleteDialog = new sap.m.Dialog({
        id,
        title: this.getText("deleteECNConfirmationButtonAndTitle"),
        state: sap.ui.core.ValueState.Warning,
        content: new sap.m.Text({
          text: this.getText("deleteConfirmationTxtECN"),
        }),
        type: sap.m.DialogType.Message,
        beginButton: new sap.m.Button({
          id: `${id}--deleteECNDialogConfirmationBtn`,
          text: this.getText("deleteECNConfirmationButtonAndTitle"),
          press: () => {
            confirmDeleteDialog.close();
            resolve();
          },
        }),
        endButton: new sap.m.Button({
          text: this.getText("cancel"),
          press: () => {
            confirmDeleteDialog.close();
            reject();
          },
        }),
        afterClose: () => confirmDeleteDialog.destroy(),
      });
      confirmDeleteDialog.open();
    });
  }

  private onAllSpacesListItemPress(event: sap.ui.base.Event): void {
    const spacesSection = event.getSource() as sap.m.ListBase;
    const items = spacesSection.getItems();
    const selectedItem = event.getParameter("listItem") as sap.m.StandardListItem;
    const selectedIndex = items.indexOf(selectedItem);
    this.getECNsTable()?.removeSelections(true);
    this.model.setProperty("/currentECNID", NO_ECN);
    setHelpScreenId("");
    if (selectedIndex === 0) {
      this.openAllSpaces();
    } else if (selectedIndex === 1) {
      this.openRecycleBin();
    }
  }

  private openAllSpaces() {
    this.navigateToSpaces();
  }

  private openRecycleBin() {
    this.navigateToSpaces(true);
  }

  private onECNListItemPress(event: sap.ui.base.Event): void {
    this.getSpacesTable()?.removeSelections(true);
    this.eventBus.publish(SpacesSidePanelClass.NAME, "clearSelectedObjects");
    this.model.setProperty(
      "/currentECNID",
      event.getSource()["getSelectedItem"]().getBindingContext("ecn").getProperty("ecnId")
    );
    this.setSchedulingMenuBusy(false);
    this.navigateToECN(true);
  }

  private navigateToSpaces(showDeleted: boolean = false): void {
    this.router.navTo("overview", {
      query: {
        view: this.model.getProperty("/currentView"),
        ...(this.DWCO_SPACES_LOGICAL_DELETE ? { showDeleted } : {}),
      },
    });
  }

  private navigateToECN(isECNContext: boolean): void {
    this.router.navTo("overview", {
      query: {
        view: this.model.getProperty("/currentView"),
        ...(isECNContext && { ecn: this.model.getProperty("/currentECNID") }),
      },
    });
  }

  private onCreateECNButtonPress(): void {
    this.openCreateECNDialog();
  }

  private async openCreateECNDialog(): Promise<void> {
    showDialogExt<ICreateECNDialogViewData, IECN>({
      parentView: this.getView(),
      viewName: require("../../../managespaces/view/dialog/CreateECNDialog.view.xml"),
      title: this.getText("ecnCreationDialogTitle"),
      okButtonText: this.getText("create"),
      cancelButtonText: this.getText("cancel"),
      viewData: {
        existingECNIDs: (this.ecnModel.getProperty("/ecns") as IECNInstance[]).map((ecn) => ecn.ecnId),
        availablePlans: await this.ecnModel.getElasticComputeBlocks(),
        elasticityReadiness: await this.ecnModel.fetchECNReadiness(),
      },
    })
      .then(async (ecn) => {
        await this.ecnModel.createECN(ecn);
        this.ecnModel.fetchData();
      })
      .catch(() => {
        /* dialog cancellation */
      });
  }

  private async openEditECNDialog(): Promise<void> {
    showDialogExt<ICreateECNDialogViewData, IECN>({
      parentView: this.getView(),
      viewName: require("../../../managespaces/view/dialog/CreateECNDialog.view.xml"),
      title: this.getText("ecnEditDialogTitle"),
      okButtonText: this.getText("save"),
      cancelButtonText: this.getText("cancel"),
      viewData: {
        existingECNIDs: (this.ecnModel.getProperty("/ecns") as IECNInstance[]).map((ecn) => ecn.ecnId),
        availablePlans: await this.ecnModel.getElasticComputeBlocks(),
        elasticityReadiness: await this.ecnModel.fetchECNReadiness(),
        currentECNDetails: {
          ecnId: this.ecnId,
          name: this.name,
          sizingPlan: this.sizingPlan,
          changedAt: this.changedAt,
          changedBy: this.changedBy,
        },
      },
    })
      .then(async (ecn) => {
        await this.ecnModel.updateECN(ecn);
        this.ecnModel.fetchData().then((ecnData: IECNInstance[]) => {
          const currentECNData = ecnData.find((item) => item.ecnId === ecn.ecnId);
          this.name = currentECNData.name;
          this.sizingPlan = currentECNData.sizingPlan;
          this.changedAt = currentECNData.changedAt;
          this.changedBy = currentECNData.changedBy;
          this.publishOpenViewEvent(currentECNData);
        });
      })
      .catch(() => {
        /* dialog cancellation */
      });
  }

  private publishOpenViewEvent(ecnData: IECNInstance, tab = "spaces"): void {
    this.eventBus.publish(SpacesSidePanelClass.NAME, "openView", {
      navigationInfo: {
        view: this.model.getProperty("/currentView") || SpaceOverviewView.TILE,
        ecn: ecnData.ecnId,
        spaceIds: ecnData?.spaces?.map((space) => space.spaceId),
        isActive:
          ecnData?.runDetails?.phase === EcnPhaseSnapshotText.ACTIVE ||
          ecnData?.runDetails?.phase === EcnPhaseSnapshotText.STOPPING,
        ecnBusinessName: ecnData?.name,
      } as NavigationInfo,
      ECNData: this.ecnModel.getProperty("/currentECN"),
      elasticityReadiness: this.ecnModel.getProperty("/elasticityReadiness"),
      tab,
    });
  }

  private openDeleteECNDialog(): void {
    this.deleteECNConfirmationDialog()
      .then(async () => {
        // TODO add busy indicator on deletion
        await this.ecnModel.deleteECN(this.model.getProperty("/currentECNID"));
        this.ecnModel.fetchData();
        this.openAllSpaces();
      })
      .catch(() => {
        /** */
      });
  }

  private async openSpaceAssignmentDialog(): Promise<void> {
    const alreadyAssignedSpaces = (this.ecnModel.getProperty("/ecns") as IECNInstance[])
      .map((ecn) => ecn.spaces?.map((space) => space.spaceId))
      .flat(1);
    const availableSpace = await this.ecnModel.fetchSpacesData(0, "", alreadyAssignedSpaces);
    showDialogExt<ISpaceAssignmentDialogViewData, ISpaceAssignmentDialogResult[]>({
      parentView: this.getView(),
      viewName: require("../../../managespaces/view/dialog/SpaceAssignmentDialog.view.xml"),
      title: this.getText("ecnSpaceAssignmentDialogTitle"),
      okButtonText: this.getText("assign"),
      cancelButtonText: this.getText("cancel"),
      // TODO separate logic of spaceretrieval from open page to allow separate loaders
      viewData: {
        ecnModel: this.ecnModel,
        alreadyAssignedSpaces,
        spaces: availableSpace,
      },
    })
      .then(async (result) => {
        const currentECNID = this.model.getProperty("/currentECNID");
        await this.ecnModel.assignToECN(currentECNID, result);
      })
      .catch(() => {
        /* dialog cancellation */
      });
  }

  private async openAssignObjectsDialog(
    selectedSpaceId: string,
    previousAsgmtStrtg: IECNMetadataSpaceAsgmtStrtg
  ): Promise<void> {
    const assignedObjectsNames = this.ecnModel.getProperty("/assignedObjects").map((obj) => obj.objectName);
    const objectsAssignedToCurrentSpace = this.ecnModel
      .getProperty("/currentECN")
      .spaces.filter((space) => space.spaceId === selectedSpaceId);
    const alreadyAssignedObjectToSpaceCount = objectsAssignedToCurrentSpace[0].objects?.length;
    const availableObjects = await this.ecnModel.fetchObjectsToAssign(selectedSpaceId);
    showDialogExt<IAssignObjectsDialogViewData, IAssignObjectsDialogResult>({
      parentView: this.getView(),
      viewName: require("../../../managespaces/view/dialog/AssignObjectsDialog.view.xml"),
      okButtonText: this.getText("assignObjects"),
      title: `${this.getText("assignObjectsTitle")} ${selectedSpaceId}`,
      cancelButtonText: this.getText("cancel"),
      viewData: {
        availableObjects: availableObjects.value.filter(({ name }) => !assignedObjectsNames.includes(name)),
        assignedObjectsNames,
        selectedSpaceId,
        asgmtStrtg: previousAsgmtStrtg,
        ecnModel: this.ecnModel,
        totalAvailableObjectCount: availableObjects["@odata.count"]
          ? availableObjects["@odata.count"] - alreadyAssignedObjectToSpaceCount
          : 0,
      },
    })
      .then(async (result) => {
        const updatedAsgmtStrtg: IECNMetadataSpaceAsgmtStrtg = result.asgmtStrtg;
        if (
          (previousAsgmtStrtg === IECNMetadataSpaceAsgmtStrtg.automatic &&
            updatedAsgmtStrtg === IECNMetadataSpaceAsgmtStrtg.automatic) ||
          (previousAsgmtStrtg === IECNMetadataSpaceAsgmtStrtg.manual &&
            updatedAsgmtStrtg === IECNMetadataSpaceAsgmtStrtg.manual &&
            result.objects.length === 0)
        ) {
          return;
        } else {
          let objects: IECNMetadataSpaceObject[] = result.objects;
          if (updatedAsgmtStrtg === IECNMetadataSpaceAsgmtStrtg.automatic) {
            objects = this.ecnModel
              .getProperty("/currentECN")
              .spaces.filter((space) => space.spaceId === selectedSpaceId)[0]
              .objects.map((obj) => ({
                ...obj,
                isEnable: false,
              }));
          }
          const currentECNID = this.model.getProperty("/currentECNID");
          const spacesToAssignUnassign = { spaceId: selectedSpaceId, isSelected: true, objects };
          await this.ecnModel.assignToECN(
            currentECNID,
            [spacesToAssignUnassign],
            updatedAsgmtStrtg,
            SpacesModelDataType.OBJECT_ASSIGNMENT_ECN
          );
        }
      })
      .catch(() => {
        /* dialog cancellation */
      });
  }

  private async fetchData(tab = "spaces", skipFetchAutoAssignedObjects = true) {
    const ecns: IECNInstance[] = await this.ecnModel.fetchData();
    if (ecns) {
      const currentECNID = this.model.getProperty("/currentECNID");
      const ecnData: IECNInstance = ecns?.find((e) => e.ecnId === currentECNID);
      if (ecnData) {
        const ecnSpaceMetaData: IECNMetadataSpace[] = ecnData?.spaces;
        const spaceIds: string[] = ecnSpaceMetaData?.map((space) => space.spaceId);
        this.model.setProperty("/currentSpaceIds", spaceIds);
        await this.ecnModel.fetchECN(currentECNID);
        if (!skipFetchAutoAssignedObjects && ecnSpaceMetaData) {
          await this.updateAssignedObjectsFromRepo(ecnSpaceMetaData);
        }
        this.publishOpenViewEvent(ecnData, tab);
      }
    }
  }

  private async updateAssignedObjectsFromRepo(ecnSpaceMetaData: IECNMetadataSpace[]) {
    const spacesWithAutoAssignStrategy = ecnSpaceMetaData
      ?.filter((space) => space.asgmtStrtg === IECNMetadataSpaceAsgmtStrtg.automatic)
      .map((space) => space.spaceId);
    if (spacesWithAutoAssignStrategy?.length) {
      const autoAssignedObjects = await this.ecnModel.fetchObjectsToAssign("", 0, "", spacesWithAutoAssignStrategy);
      const ecnWithAutoAssignedObjects = this.ecnModel.getProperty("/currentECN")?.spaces?.map((space) => {
        if (space.asgmtStrtg === IECNMetadataSpaceAsgmtStrtg.automatic) {
          const autoAssignedObjectsForSpace = autoAssignedObjects?.value?.filter(
            (object) => object.space_name === space.spaceId
          );
          space.objects = autoAssignedObjectsForSpace.map((obj) => ({
            objectName: obj.name,
            objectType: obj.technical_type,
            schemaName: obj.space_name,
            isEnable: true,
          }));
        }
        return space;
      });
      this.model.setProperty("/currentECN", {
        ...this.ecnModel.getProperty("/currentECN"),
        spaces: ecnWithAutoAssignedObjects,
      });
    }
  }

  async initScheduleDialog() {
    if (!this["newScheduleDialog"]) {
      const customURLParams_ECN = {
        SCHEDULE: "ecn/schedules",
        SCHEDULE_BY_ID: "ecn/schedules/:scheduleid",
        OWNER_CHANGE: "ecn/schedules/:scheduleid/ownerchange",
        PAUSE_SCHEDULE: "/ecn/schedules/:scheduleid/pause",
        RESUME_SCHEDULE: "/ecn/schedules/:scheduleid/resume",
      };
      this["newScheduleDialog"] = await getTaskScheduer("ecnScheduler", customURLParams_ECN);
    }
  }

  private async handleSchedule(action: ECNActions): Promise<void> {
    let scheduleDialog;
    if (this.DWCO_REUSABLE_TASK_SCHEDULING_UI && this.DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("ecnSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const ecn = this.model.getProperty("/currentECN") as IECNInstance;
    const taskScheduleRequest: ITaskScheduleRequest = {
      objectId: ecn.ecnId,
      applicationId: ApplicationId.ELASTIC_COMPUTE_NODE,
      activity: Activity.GENERATE_START_CHAIN,
      activationStatus: "ENABLED",
      description: "",
    };

    switch (action) {
      case ECNActions.CREATE_SCHEDULE:
        if (this.DWCO_REUSABLE_TASK_SCHEDULING_UI && this.DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION) {
          recordAction(`createTaskSchedule: ${ApplicationId.ELASTIC_COMPUTE_NODE}`, "taskSchedule", "onCreate");
        }
        scheduleDialog.createTaskSchedule(
          taskScheduleRequest,
          SpaceRequirement.NO_SPACE_REQUIRED,
          ApplicationId.ELASTIC_COMPUTE_NODE,
          () => {
            if (this.DWCO_REUSABLE_TASK_SCHEDULING_UI && this.DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION) {
              recordAction(`saveTaskSchedule: ${ApplicationId.ELASTIC_COMPUTE_NODE}`, "taskSchedule", "save");
            }
            MessageHandler.success(this.getText("createScheduleSuccess"));
            this.fetchData();
          },
          () => {},
          () => {},
          true
        );
        break;
      case ECNActions.EDIT_SCHEDULE:
        if (this.DWCO_REUSABLE_TASK_SCHEDULING_UI && this.DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION) {
          recordAction(`editTaskSchedule: ${ApplicationId.ELASTIC_COMPUTE_NODE}`, "taskSchedule", "onEdit");
        }
        scheduleDialog.changeTaskSchedule(
          taskScheduleRequest,
          SpaceRequirement.NO_SPACE_REQUIRED,
          ApplicationId.ELASTIC_COMPUTE_NODE,
          () => {
            if (this.DWCO_REUSABLE_TASK_SCHEDULING_UI && this.DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION) {
              recordAction(`saveTaskSchedule: ${ApplicationId.ELASTIC_COMPUTE_NODE}`, "taskSchedule", "save");
            }
            MessageHandler.success(this.getText("updateScheduleSuccess"));
            this.fetchData();
          },
          () => {},
          () => {}
        );
        break;
      case ECNActions.DELETE_SCHEDULE:
        scheduleDialog.deleteSchedule(
          taskScheduleRequest,
          SpaceRequirement.NO_SPACE_REQUIRED,
          ApplicationId.ELASTIC_COMPUTE_NODE,
          () => {
            MessageHandler.success(this.getText("deleteScheduleSuccess"));
            this.fetchData();
          },
          () => {},
          () => {}
        );
        break;
      case ECNActions.CHANGE_OWNER_SCHEDULE:
        scheduleDialog.changeOwner(
          SpaceRequirement.NO_SPACE_REQUIRED,
          ApplicationId.ELASTIC_COMPUTE_NODE,
          ecn.ecnId,
          () => {
            MessageHandler.success(this.getText("ownerChangeScheduleSuccess"));
            this.fetchData();
          }
        );
        break;
      case ECNActions.PAUSE_SCHEDULE:
        scheduleDialog.pauseSchedule(
          SpaceRequirement.NO_SPACE_REQUIRED,
          ApplicationId.ELASTIC_COMPUTE_NODE,
          ecn.ecnId,
          () => {
            this.setSchedulingMenuBusy(true);
            MessageHandler.success(this.getText("pauseScheduleStarted"));
          }
        );
        break;
      case ECNActions.RESUME_SCHEDULE:
        scheduleDialog.resumeSchedule(
          SpaceRequirement.NO_SPACE_REQUIRED,
          ApplicationId.ELASTIC_COMPUTE_NODE,
          ecn.ecnId,
          () => {
            this.setSchedulingMenuBusy(true);
            MessageHandler.success(this.getText("resumeScheduleStarted"));
          }
        );
        break;
      default:
        break;
    }
  }

  private async onRefreshButtonPress() {
    // TODO refresh spaces as well
    await this.fetchData("spaces", false);
    this.ecnModel.fetchECNReadiness(true);
  }

  private onManageResourcesButtonPress(): void {
    // TODO link manage resources to FTC
  }

  private onViewLogsPress(): void {
    this.navigateToSystemMonitor();
  }

  private navigateToSystemMonitor(ecnID = ""): void {
    const viewId: Views = Views.TASKLOGS;
    const url = `monitoring&/m/tool?view=${viewId}&oDataView=monitoring/TASKS/TASK_LOGS_MEMORY&orderBy=START_TIME desc&filter={"SPACE_ID":{"operator":"eq","operand":"$$ecn$$"} ,"ACTIVITY":[{"operator":"eq","operand":"GENERATE_START_CHAIN"},{"operator":"eq","operand":"GENERATE_STOP_CHAIN"},{"operator":"eq","operand":"RUN_CHAIN_TECHNICAL"}] ${
      ecnID === ""
        ? "}"
        : `,"OBJECT_ID":[{"operator":"eq","operand":"${ecnID}"},{"operator":"StartsWith","operand":"${ecnID}_"}]}`
    }`;
    ShellNavigationService.toExternal({
      target: {
        shellHash: url,
      },
    });
  }

  getSpacesTable(): sap.m.Table {
    return this.view.byId("spacesTable") as sap.m.Table;
  }

  getECNsTable(): sap.m.Table {
    return this.view.byId("ecnsTable") as sap.m.Table;
  }
}

export const SpacesSidePanel = smartExtend(
  BaseController,
  "sap.cdw.components.managespaces.controller.SpacesSidePanel",
  SpacesSidePanelClass
);
sap.ui.define(
  "sap/cdw/components/managespaces/controller/overview/SpacesSidePanel.controller",
  [],
  () => SpacesSidePanel
);
