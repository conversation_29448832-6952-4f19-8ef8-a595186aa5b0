/** @format */

import { ESOrderType, IESSearchOptions } from "@sap/enterprise-search-objects";

export const ECN_ID_PREFIX = "ds";
export const NO_ECN = "";
export const NOT_AVAILABLE = "-";
export const NOT_INITIALIZED = "---";

export const byteToGb = Math.pow(10, 9);
export const minSpaceSizeInByte: number = 1 * byteToGb;
export const defaultSpaceStorageSizeInByte: number = 2 * byteToGb;
export const defaultSpaceRamSizeInByte: number = 1 * byteToGb;
export const MAX_LENGTH_SPACE_LONG_DESCRIPTION = 4000;
export const MAX_LENGTH_ECN_BUSINESSNAME = 30;
export const MAX_LENGTH_ECN_TECHNICALNAME = 9;
export const MAX_LENGTH_SPACEID = 20;
export const defaultPriority = 5;
export const minAuditLogRetentionTimeInDays = 7;
export const maxAuditLogRetentionTimeInDays = 10000;
export const defaultAuditLogRetentionTimeInDays = 7;
export const SpaceManagementChannel = "SpaceManagementChannel";
export const TOTAL_STATEMENT_MEMORY_LIMIT_DEFAULT = 80;
export const TOTAL_STATEMENT_THREAD_LIMIT_DEFAULT = 70;

export enum SpaceEvent {
  UPDATE = "Update",
  CREATE = "Create",
  DELETE = "Delete",
  OPEN_DETAIL = "Open_Detail",
  OPEN_MONITORING = "Open Monitoring",
  RELOAD_DATABASE_USERS = "Reload_Database_Users",
  DETAILS_LOADED = "DETAILS_LOADED",
  USERS_MODIFIED = "USERS_MODIFIED",
  MODIFIED = "MODIFIED",
  RELOAD_PARTNER_CONNECTION_CONFIGURATION = "RELOAD_PARTNER_CONNECTION_CONFIGURATION",
  BACK_TO_ADD_USERS = "BACK_TO_ADD_USERS",
}

export enum SpaceHealth {
  Green = "green",
  Hot = "hot",
  Cold = "cold",
  Locked = "locked",
  Unknown = "unknown",
}

export enum WorkloadClassStatementLimitUnit {
  Counter = "Counter",
  Gigabyte = "Gigabyte",
  Percent = "Percent",
}

export const SpaceIconPath: Readonly<{
  COLD: string;
  HOT: string;
  GREEN: string;
  HIBERNATED: string;
  LOCKED: string;
  UNKNOWN: string;
}> = Object.freeze({
  COLD: "sap-icon://sac/temperature-cold",
  HOT: "sap-icon://sac/temperature-hot",
  GREEN: "sap-icon://sac/completed",
  HIBERNATED: "sap-icon://sac/moon",
  LOCKED: "sap-icon://locked",
  UNKNOWN: "sap-icon://sys-help-2",
});

export enum SpaceTypeIconPath {
  MARKETPLACE = "sap-icon://cart-full",
  ABAPBRIDGE = "sap-icon://sac/linked-model",
  DEFAULT = "sap-icon://sac/models",
}

const SPACESMODEL: any = {
  spaces: [],
  selection: [],
  spaceCounts: {
    hot: NOT_INITIALIZED,
    green: NOT_INITIALIZED,
    cold: NOT_INITIALIZED,
    suspend: NOT_INITIALIZED,
    locked: NOT_INITIALIZED,
  },
  total: {
    spaces: NOT_INITIALIZED,
    storage: {
      percent: {
        used: 0,
        assigned: 0,
      },
      total: 0,
      used: 0,
      assigned: 0,
    },
    ram: {},
  },
  storage: {
    details: {},
    total: {},
  },
};

const SEARCH_OPTIONS: IESSearchOptions = {
  $top: 100000,
  $skip: 0,
  scope: "SEARCH_SPACE",
  $count: true,
  whyfound: false,
  $orderby: [
    {
      key: "Search.score()",
      order: ESOrderType.Descending,
    },
    {
      key: "name",
      order: ESOrderType.Ascending,
    },
  ],
  $select: [],
  customQueryParameters: new Map([["inSpaceManagement", "true"]]),
};

export const DEFAULTDATA = {
  SPACESMODEL,
  SEARCH_OPTIONS,
};

export const SUPPORTED_LANGUAGES = [
  "af",
  "ar",
  "bg",
  "ca",
  "zh",
  "zf",
  "hr",
  "cs",
  "cy",
  "da",
  "nl",
  "en-UK",
  "en",
  "et",
  "fa",
  "fi",
  "fr-CA",
  "fr",
  "de",
  "el",
  "he",
  "hi",
  "hu",
  "is",
  "id",
  "it",
  "ja",
  "kk",
  "ko",
  "lv",
  "lt",
  "ms",
  "no",
  "pl",
  "pt",
  "pt-PT",
  "ro",
  "ru",
  "sr",
  "sh",
  "sk",
  "sl",
  "es",
  "es-MX",
  "sv",
  "th",
  "tr",
  "uk",
  "vi",
].sort((a, b) => a.localeCompare(b));

export const sparkSourcesConfig = [
  {
    identifier: {
      index: "001",
      label: "Micro",
    },
    calculated: {
      "spark.cores.max": 6,
      "spark.memory.max": "24g",
    },
    configuration: {
      spark_configurations: {
        "spark.driver.cores": 2,
        "spark.driver.memory": "8g",
        "spark.executor.cores": 2,
        "spark.executor.memory": "8g",
      },
    },
    preference: {
      ltf: true,
      merge: false,
      optimize: false,
      transformation: false,
    },
  },
  {
    identifier: {
      index: "100",
      label: "Small",
    },
    calculated: {
      "spark.cores.max": 42,
      "spark.memory.max": "168g",
    },
    configuration: {
      spark_configurations: {
        "spark.driver.cores": 2,
        "spark.driver.memory": "8g",
        "spark.executor.cores": 4,
        "spark.executor.memory": "16g",
      },
    },
    preference: {
      ltf: false,
      merge: false,
      optimize: false,
      transformation: false,
    },
  },
  {
    identifier: {
      index: "200",
      label: "Small",
    },
    calculated: {
      "spark.cores.max": 48,
      "spark.memory.max": "192g",
    },
    configuration: {
      spark_configurations: {
        "spark.driver.cores": 8,
        "spark.driver.memory": "32g",
        "spark.executor.cores": 4,
        "spark.executor.memory": "16g",
      },
    },
    preference: {
      ltf: false,
      merge: false,
      optimize: false,
      transformation: false,
    },
  },
  {
    identifier: {
      index: "300",
      label: "Medium",
    },
    calculated: {
      "spark.cores.max": 164,
      "spark.memory.max": "656g",
    },
    configuration: {
      spark_configurations: {
        "spark.driver.cores": 4,
        "spark.driver.memory": "16g",
        "spark.executor.cores": 4,
        "spark.executor.memory": "16g",
      },
    },
    preference: {
      ltf: false,
      merge: true,
      optimize: true,
      transformation: false,
    },
  },
  {
    identifier: {
      index: "400",
      label: "Medium",
    },
    calculated: {
      "spark.cores.max": 168,
      "spark.memory.max": "672g",
    },
    configuration: {
      spark_configurations: {
        "spark.driver.cores": 8,
        "spark.driver.memory": "32g",
        "spark.executor.cores": 4,
        "spark.executor.memory": "16g",
      },
    },
    preference: {
      ltf: false,
      merge: false,
      optimize: false,
      transformation: true,
    },
  },
  {
    identifier: {
      index: "500",
      label: "Large",
    },
    calculated: {
      "spark.cores.max": 408,
      "spark.memory.max": "1632g",
    },
    configuration: {
      spark_configurations: {
        "spark.driver.cores": 8,
        "spark.driver.memory": "32g",
        "spark.executor.cores": 4,
        "spark.executor.memory": "16g",
      },
    },
    preference: {
      ltf: false,
      merge: false,
      optimize: false,
      transformation: false,
    },
  },
];

export const SCHEDULE_ECN_KEYWORDS = {
  mandatory: "schedule",
  optional: ["pause", "resume", "owner"],
};
