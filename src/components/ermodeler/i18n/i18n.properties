#XTIT: property details header with ellipsis
@propertyDetailsHeader={0}…
#XTOL
@editColums=Edit Columns
#XTOL
@editMeasures=Edit Measures
#XTOL
@editAssociations=Edit Associations
@showInDiagram=Show in Diagram
@editMappings=Edit Mappings
#XTOL
cdcColumn=Delta Capture Column
#XBUT:To delete association
@deleteAssociation=Delete Association
#XTIT: select target dialog title
@selectAssociation=Select Association Target
#XTIT: dialog title of Select Text Association Target
@selectTextAssociation=Select Text Association Target
#XFLD: Label to represent target type
@targetType=Target Type
#XBUT:To add data access control
@createViewDataAccessControl=Create Data Access Control Restriction
#XBUT:To delete data access control
@deleteViewDataAccessControl=Delete Data Access Control Restriction
#XTIT: select data access control
@selectDataAccessControl=Select Data Access Control
#XBUT:To select object
@addObjectBtn=Select
#XCOL
@createdBy=Created By
#XCOL
@associationType=Type
#XCOL
@association=Association
#XCOL
@hierarchyAssociation=Hierarchy Association
#XCOL
@hierarchyWithDirectoryAssociation=Hierarchy with Directory Association
#XCOL
@textAssociation=Text Association
#XCOL
@targetEntity=Target Entity
#XCOL
@mapping=Mappings
#XMSG
@associationsNoDataText=No Associations
#XTEXT
@noObjectsFound=No Objects Found
#XTOL
@columnCount=Number of Columns
#XTOL
@validateModel=Validation messages
@undo=Undo
@redo=Redo
@deleteNode=Delete Selected Symbol
@zoomToFit=Zoom to Fit
@expandAll=Expand All
@collapseAll=Collapse All
@autoLayout=Auto Layout
@createEntity=Create Table
@createView=Create View
@sqlType=Language
@sql=SQL (Standard Query)
@tableFunction=SQLScript (Table Function)
#XTOL
@setValues=Apply to All Local Tables
#XMSG
@setValuesSuccess=Values set to all local tables.
#XTOL
@createAssociation=Create Association
@createViewFromSelection=Create View from Selection...
#XTOL
@openImpactLineage=Impact and Lineage Analysis
@editCSNAnnotations=Edit Custom CSN Annotations
@welcomeText=Drag and drop your data from the left panel over to this canvas. You can also create new tables or views by using the tools provided.
@txtNoData=It looks like you haven’t added any data yet.
@cannotDeleteAssocOfEntity=Cannot delete the association because its source is read-only.
@name=Technical Name
@aggregationName=Name:
@label=Business Name
@title=Create View
@openViewBuilder=Open in View Editor
@description=Description
@definition=Definition
@key=Key
@dataType=Data Type
@baseType=Base Type
@length=Length
@precision=Precision
@scale=Scale
@srid=SRID
@remoteSource=Connection
@remoteProperties=Remote Table Properties
@remoteTable=Remote Table
@localTable=Local Table
@remote=Remote
@localSchema=Local Schema
@sourceObjectLabel=Source Object (Open SQL Schema/HDI Container)
@remoteCache=Use Cache
@dataAccessFrom=Data Access
@tableDataAccessRemote=Remote
@tableDataAccessReplicated=Replicated
#XFLD: Label for Non-Availability of Remote table replication
@tableDataAccessNotAvailable=Not Available
@deploymentStatus=Deployment Status
@deploymentDate=Deployed On
#XFLD: Object status (repo: "#objectStatus")
@objectStatus=Status
#XFLD: Object status, model has been changed but changes are not yet deployed (repo: "#objectStatus")
@statusChangesToDeploy=Changes to Deploy
#XFLD: Object status, deployed model has run-time errors, example: data type of used source has been changed (repo: "#objectStatus")
@statusRunTimeError=Runtime Error
#XFLD: Object status, deployed model is OK current/changed model is not consistent, example: error in expression of calc. column, after changing the model (repo: "#objectStatus")
@statusDesignTimeError=Design Time Error
#XFLD: Object status, model has never been deployed up to now / no deployment date exists (repo: "#objectStatus")
@statusNew=Not Deployed
#XFLD: Object status, currently some repository objects like "dataflow" to not provide an object status (repo: "#objectStatus")
@hasNoObjectStatus=Object Type w/o Status
#XFLD: Object status, model has  been deployed / deployment date exists (repo: "#objectStatus")
@statusActive=Deployed
@statusRevised=Local Updates
@statusFailed=Failed
@statusPending=Deploying...
@revert=Revert to Deployed Version
@revertDeployedVersion=Revert to Deployed Version
#XMSG: Delta Capture column error
@cdcColDelErrorTxt=You cannot delete the column "{0}" which is used for delta capture.
#XMSG: Delta Capture column error
@partitionColDelErrorTxt=You cannot delete the column "{0}" which is used for partitioning.
#XMSG MessageBox text when it is failed to restore deployed version since backend cannot not find it
@revertDeployedVersionNotFound=Unable to restore deployed version as it cannot be found.
@msgRevertDeployedVersion1=Reverting to the deployed version will undo any local changes. This action cannot be undone.
@msgRevertDeployedVersion2=Do you want to continue?
@attributes=Attributes
@columns=Columns
@addAttribute=Add Column
@deleteColumns=Delete Columns
@moveAttributesUp=Move Columns Up
@moveAttributesDown=Move Columns Down
@filterKeyAttributes=Filter Key Columns
@filterAttributes=Filter Attributes by Name or Description
@editInViewBuilder=Open in View Editor
@editInTableEditor=Open in Table Editor
@SuggestAssociationTooltip=Add Related Entities
@selectEntities=Add Related Entities for {0}
@searchEntities=Search
@select=Select
@addElement=Add a Column
@addElementBelow=Add a Column Below
@moveElementUp=Move Column Up
@moveElementDown=Move Column Down
@importObjects=Import Objects
@waitingToImport=Ready to Import
@entityRenamed=Renamed
@txtSearchColumns=Search Columns
@general=General
#XTIT
@ordHeader=Open Resource Discovery (ORD) Settings
#XTIT
@ordConnection=Connection
#XTIT
@deltaCapture=Delta Capture
#XTIT
@ordId=ORD Id
#XTIT
@generatedAmountColumn=Generated Amount Column
#XTIT
@currencyProperties=Currency Properties
#XTIT
@conversionProperties=Conversion Properties
#XTIT
@advancedProperties=Advanced Properties
@importCompleted=Import Completed
@noObjectsImported=Objects not imported.
@askPickFromRepo=Go and pick the entities from the Repository tab.
@uploadFile=Upload your file to the server
@contextsHeaderText=Contexts ({0})
@simpleTypesHeaderText=Simple Types ({0})
@tablesHeaderText=Tables ({0})
@viewsHeaderText=Views ({0})
@deleteEntities=Delete Tables
@filterEntities=Filter by Name or Description
@deleteViews=Delete Views
@findInDiagram=Find in Diagram
@file=File
@next=Next
@browse=Browse
@info=Info
@cancel=Cancel
@addedItems=Items Added
@confirm=Confirm
@override=Overwrite
@askForFilter=More than {0} items. Please enter a filter string to reduce the results.
@importConfirmOverrideText=Your selection depends on a number of non-selected objects that are already present in the repository. Do you want to re-import these non-selected objects and overwrite the repository versions? Some data may be lost due to structure changes.
@importCsnFile=Import CSN File
@selectObjectsToImport=Select Objects to Import
@csnFileTypeMissmatch=Only csn files are supported.
@csnFileInvalidFormat=The csn file does not have the valid csn format.
@csnFileEmpty=The csn file is empty.
@import=Import and Deploy
@addRepositoryObjects=Add Repository Objects
@type=Type
#XTOL Tooltip for object link
@openInNewTab=Open in New Tab
#XCOL: Type and Semantic usage
@typeAndSemantic=Type (Semantic Usage)
@remoteHost=Remote Host
@from=From
@to=To
@minCardinality=Minimum Cardinality
@maxCardinality=Maximum Cardinality
@join=Join
@mappings=Mappings
@managed=Managed
@unmanaged=Unmanaged
@txtType=Semantic Usage
@txtPackage=Package
@txtSpaceName=Space Name
@txtContextName=Context Name
@txtLanguageField=Language Field
@txtFields=Columns
@txtLessThan=<
@txtSemanticType=Semantic Type
@txtReleaseState=Release State
#XMIT
@selectSourceAssociations=Copy Associations from Source
#XMIT
@searchAssociations=Search in Available Associations
#XMIT
@outputAttributes=View Attributes
#XMIT
@associationSource=Association Source Entity
#XMIT
@associationTarget=Association Target Entity
#XMIT
@create=Create
#XMIT
@txtAddTextAssociationOnColumn=Add Text Association
#XMIT
@txtRemoveTextAssociationOnColumn=Remove Text Association
#XMIT
@txtAddTextAssociation=Text Association
#XMIT
@txtAddPushedAssociation=Copy from Source...
#XMIT
@txtAddAssociation=Association
#XMIT To show adding Hierarchy Association dialog
@txtAddHierarchyAssociation=Hierarchy Association
@txtMeasureUnitColumn=Unit Column
@txtMeasures=Measures
@txtAggregation=Aggregation
@txtSemanticDataType=Semantic Data Type
#XTIT Title for old feature of Label Column
@txtLabelColumn=Label Column
#XTIT Title for new feature of Label Association
@txtLabelAssociation=Text / Association
@rename=Change Business Name
@renameTechnical=Change Technical Name
@changeName=Change Name
#XBUT Save button for rename dialog
@renameSave=Rename
#XFLD SQL Consumption for Story Builder or other analytical clients
@txtAllowConsumption=Expose for Consumption
#XFLD
@txtDimensionType=Dimension Type:
#XFLD
@txtDimensionTypeSettings=Settings
#XFLD
@txtDimensionTypeStandard=Standard
#XFLD
@txtDimensionTypeFiscalTime=Fiscal Time
#XFLD Link to create Analytic Model
@txtCreateAnalyticModel=Create Analytic Model
#XFLD Use OLAP plan as SQL DB Hint
@txtUseOLAPDBHint=Run in Analytical Mode
#XTOL Tooltip for Help icon
@help=Help
@txtRestrictAccess=Restrict Access
@updatingDataAccessControl=Updating Data Access Control

@businessDateFrom=Business Start Date
@businessDateTo=Business End Date
@unitOfMeasure=Unit of Measure
@currencyCode=Currency Code
@date=Date
@geoLongitude=Geolocation - Longitude
@geoLatitude=Geolocation - Latitude
@geoCartoId=Geolocation - CartoId
@geoNormalizedName=Geolocation - Normalized Name
@hierarchyDialog=Hierarchy Dialog
@amountWithCurrency=Amount with Currency
@quantityWithUnit=Quantity with Unit

#XMSG
VAL_STATE_UNIT_COLUMN_EMPTY_UNIT=Select an attribute with semantic type "Unit of Measure".
#XMSG
VAL_STATE_UNIT_COLUMN_EMPTY_CURRENCY=Select an attribute with semantic type "Currency Code".

@hierarchyName=Technical Name
@hierarchyDescription=Business Name
@pcHierarchyParentColumn=Parent Column
@pcHierarchyChildColumn=Child Column
@hierarchyParent=Parent
@hierarchyChild=Child
@Levels=Levels
@noHierarchy=No hierarchies have been created yet. To create one, please press the Add button.
@addPcHierarchy=Parent Child Hierarchy
@addLbHierarchy=Level Based Hierarchy
@addExHierarchy=External Hierarchy
@addLevel=Add Level
@removeLevel=Remove Level
@deleteHierarchy=Delete Hierarchy
@externalHierarchy=External Hierarchy
@exHierarchyName=External Hierarchy Name
@changeAggregation=Change Aggregation
@changeToAttribute=Change to Attribute
@changeToMeasure=Change to Measure
@more=More
@setAsKey=Set as Key
@removeAsKey=Remove as Key
@keyColumn=Edit Compound Key
@addParentChild=Add Parent-Child
@SUM=SUM
@MAX=MAX
@MIN=MIN
@COUNT=COUNT
@NONE=NONE
@Sum=Sum
@Count=Count
@CountExclNull=Count Excluding Null
@CountExclNullAndZero=Count Excluding Null And Zero
@Minimum=Minimum
@Maximum=Maximum
@Average=Average
@AverageExclNull=Average Excluding Null
@AverageExclNullAndZero=Average Excluding Null And Zero
@First=First
@Last=Last
@StandardDeviation=Standard Deviation
@setHidden=Set Hidden
@setVisible=Set Visible
@isVisible=Show
@hideInStory=Hide in Story
@showInStory=Show in Story
@hiddenInStory=Hidden in Story
@noDataForFilter=No columns match the filter you have set.
@noAttributesEditMode=No attributes have been created yet. To see something here, please press the Add button.
@noMeasuresEditMode=No measures have been created yet. To see something here, please drag some numeric non-key attributes to the measure.
@noColumnsEditMode=No columns have been created yet. To add columns, please press the Add button.
@txtMeasureNotPossible=This column does not have a numeric data type. It cannot be converted as a measure.
@txtOnlyForFacts=This is only possible for entities of type Fact.
@businessDefinition=Business Definition
@entDefinition=Enter Definition...
@entDescription=Enter Description...
@purpose=Purpose
@entPurpose=Enter Purpose...
@businessContactPers=Business Contact Person
@entBusCntctPers=Enter Business Contact Person...
@responsibleTeam=Responsible Team
@entResponsibleTeam=Enter Responsible Team...
@tags=Tags
@entTags=Enter Tags...
@flipSourceTarget=Flip Source and Target
@selectRemoteSource=Select Connection
@addRemoteObjects=Import Objects from Connection
@ok=OK
#XMSG
@connectionError=Connection Error
#XFLD
@searchIn=Search in:
#XFLD: placeholder for search field. For example: Search in folder1/subFolder2/subFolder3
@searchInForSearchField=Search in {0}
#XMSG: Text shown temporarily on tree nodes that are currently loading until real child nodes are available
@loading=Loading...
@available=Available
@alreadyAdded=Already present in the diagram
@selection=Selection
@noSourceSubFolder=Tables and Views
@location=Location
@modelNameUnset=Untitled
@objects=Objects
@remoteFromSelection=Remove from Selection
@status=Status
#XMSG
@exceedLimit=You can’t import more than {0} objects at a time. Please deselect at least {1} objects.
@deployObjects=Importing {0} object(s)...
@deployObjectsStatus={0} object(s) has/have been imported, and {1} object(s) could not be imported.
@getCsn=Getting CSN definition...
@getCsnFail=Couldn’t get CSN definition.
@saving=Saving...
@savingFail=Couldn’t save.
@deploying=Deploying...
@deployingFail=Couldn’t deploy.
@retry=Retry
@done=Done
@alreadyInRepository=Already in the repository.
@alreadyInModel=Already in the current model.
@alreadyUsed="{0}" already used.
@dataProperties=Data Properties
@businessProperties=Business Properties
@previewData=Preview Data
@businessName=Business Name
@filterWarning=Showing 100 of {0} containers. To search across all source containers, please enter a filter string.
@businessHeader=Business Purpose
@filterRemoteTable=Filter
@createRemoteFilter=Create Filter
@csvRF=Select a CSV file to import its structure to the selected target connection.
#XTIT Title for Input Parameter Section
@InputParameterTitle=Input Parameters ({0})
#XTIT Title for Input Parameter Section
@inputParametersTitle=Input Parameters
#XMSG Label Name for parameters name
@parameterNameLabel=Name
#XMSG Label Name for parameters data type
@parameterDataTypeLabel=Data Type
#XMSG Label Name for parameters default value
@parameterDefaultValueLabel=Default Value
#XMSG No Data Text
NO_DATA=No Data
#XMSG Label for Value
@inputParametersValue=Value
detailsText=Enter values to generate your data
txtClose=Close
#XGRP:Associations panel header
@associationsList=Associations
@viewDataAccessControlsList=Data Access Controls
@businessTechnicalName=Technical Name
@entBusinessName=Enter Business Name...
@entBusinessTechnicalName=Enter Technical Name...
@hidden=Hidden
@yes=Yes
@no=No
@none=None
@tableServices=Table Services
@txtDropAttributeMeasure=Drop the attribute here to change it into a measure.
#XMSG Column can not be converted to be a measure
@txtMeasureDropNotPossible=The selection contains either a column which does not have a numeric data type or a key. It cannot be converted into a measure.
@close=Close
@search=Search
@deleteMeasures=Delete Measures
#XMSG
@txtColumnsHasAIChange=AI generated changes. Click "Edit" to review.
#XMSG
@txtEntityHasAIChangeMsg=The AI-generated semantics may contain inaccuracies. Use the "Review" button to understand changes.
#XMSG
@txtAIStatusPopoverDesc=Content on this page, such as the semantic usage, keys, and column semantic types has been generated by artificial intelligence (AI) technologies.\n\nThe AI-generated content may contain inaccuracies due to using multiple information sources. Verify results before use.
#XBUT
@closeText=Close
#XMSG
@titAIStatusPopover=Created with AI
#XFLD
@review=Review
#XTIT
@reviewTitle=Review GenAI Changes
#XBUT
@totip_aistatus=AI Status
#XBUT
@revertChanges=Revert Changes
#XLST
@all=All
@fact=Analytical Dataset
#XLST
@factDeprecated=Analytical Dataset (deprecated)
@sqlfact=Fact
@dimension=Dimension
@dataSet=Relational Dataset
@hierarchy=Hierarchy
#XLST
@hierarchyWithDirectory=Hierarchy with Directory
#XBUT
@hierarchyWithDirectorySettings=Hierarchy with Directory Settings
#XLST: Semantic Usage is Text
@text=Text
@dataAccessControl=Data Access Control
@attribute=Attribute
@measure=Measure
@parameter=Parameter
@analyticParameter=Analytical Parameter
@parameters=Parameters
@defaultAggregation=Default Aggregation
@noAttributes=There are currently no attributes available. To add some, please press the Edit button in the Data Properties panel.
@noColumns=There are currently no columns available. To add some, please press the Edit button.
@attributesWithAmount=Attributes ({0})
@attributesWithAmountAndSelection=Attributes ({0}/{1})
@measuresWithAmount=Measures ({0})
@measuresWithAmountAndSelection=Measures ({0}/{1})
@columnsWithAmount=Columns ({0})
@columnsWithAmountAndSelection=Columns ({0}/{1})
@txtSourceFact=Source Analytical Dataset
enterTechnicallName=Please enter a technical name.
@saveRemoteObject=Import Table
@msgTableImported="{0}"."{1}"’ is imported and deployed.
#XMSG The CSN import is cancelled by the user.
VAL_SAVE_CANCELLED=Import canceled by the user.
@waitModelUpdate=The model is being updated. Please wait and try again later.
#XTEXT: Not Null Text
@notNull=Not Null
#XTEXT: Default value Text
@default=Default Value
#XTEXT: Text to select date
@dateSelect=Select Date
#XTEXT: Text to select time
@timeSelect=Select Time
#XTEXT: Text to select date and time
@dateTimeSelect=Select Date and Time
#XTEXT: Text to select Current Date
@currentDate=Current Date
#XTEXT: Text to select Current UTC Date
@currentUtcDate=Current UTC Date
#XTEXT: Text to select Time
@currentTime=Current Time
#XTEXT: Text to select Current UTC time
@currentUtcTime=Current UTC Time
#XTEXT: Text to select current timestamp
@currentTimestamp=Current Time Stamp
#XTEXT: Text to select current UTC timestamp
@currentUtcTimestamp=Current UTC Time Stamp
#XTEXT: Text for true dropdown of boolean datatype
@true=TRUE
#XTEXT: Text for false dropdown of boolean datatype
@false=FALSE
#XTEXT: Text for true dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
@param_true=true
#XTEXT: Text for false dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
@param_false=false
#XFLD: Placeholder for string input type
stringplaceholdertext=Enter a string
#XFLD: Placeholder for integer input type
intplaceholdertext=Enter a number
#XFLD: Placeholder for decimal input type
decplaceholdertext=Enter a decimal value
#XFLD: Placeholder for date format
@dateFormat=DD/MM/YYYY
#XFLD: Placeholder for time format
@timeFormat=hh:mm:ss
#XFLD: Placeholder for date and time format
@dateTimeFormat=DD/MM/YYYY, hh:mm:ss

#XFLD: Placeholder for date format Filter
@dateFormatFilter=YYYY-MM-DD
#XFLD: Placeholder for time format Filter
@timeFormatFilter=HH:mm:ss
#XFLD: Placeholder for date and time format Filter
@dateTimeFormatFilter=YYYY-MM-DD, HH:mm:ss

#XMSG
invalid_format=Please enter "{0}" format

#XTEXT: Text for cardinality dropdown
txtExactOne=Exactly One
txtOne=One
txtMany=Many

#XFLD: Label for cardinality
txtCardinality=Cardinality
txtCardinalityTo=To

#XMSG
txtCardinalityDescription=Every row in {0} joins to {1} in {2}
txtDescMany=Zero or multiple rows
txtDescExactone=Only one row
txtDescOne=Zero rows or one row

#XTEXT
txtMore=( {0} more...)

## BW Services
#XBUT Header Text for the Services Section
bws_header=Table Services
#XFLD NSE Switch
bws_nse=Store Table Data in Memory

txtColumnNameAsc=Column Name Ascending
txtColumnNameDesc=Column Name Descending
txtSortDisabled=Sorting Disabled
txtSortDescending=Sort Descending
txtTo=to
txtSortAscending=Sort Ascending
txtNoSort=Initial Order
#XTOL: Sort by severity (top down), example: Sort dependent object list by object status (run time error -> design time error -> ...)
txtSortSeverity=Sort by Severity of Status
view=View
selectUserImpersonation=Enter another user identifier to view the data with that user’s permissions and see which records will be visible to them. Select a space user from the list or directly enter the user identifier.
selectUserImpersonationNoUsersList=Enter another user identifier to view the data with that user’s permissions and see which records will be visible to them.
userImpersonationCrossSpaceWarning=This view has one or more sources shared from other spaces, which may have data access controls applied. As "View as User" can only simulate permissions applied to a user in the current space, you may not see exactly the same records as the user you are impersonating.
viewAsUser=View as User
usersSpace=Space {0} Users
helpUserImpersonation=You can choose someone from the list or enter any user id.
displayNameAndIdpFormat={0} ({1})
cancel=Cancel
ok=OK
reset=Reset
help=Help

#XTOL
showElementDetails=Show details

onlyAlphanumericCharAllowed=Please note that the maximum length is 30 characters and that only the following characters can be used: a-z, A-Z, 0-9, _. It cannot start with _.

#XMSG
PROCESSINGS=Processing...

## AVAILABLE CHECKS
#XMSG
AC_ONLY_IF_PRIVILEGE=No privileges to edit.
#XMSG
AC_NOT_IF_ELEMENT_OF_HIERARCHY_QUERY_ENTITY=Not allowed in entities of graphical view or association view.
#XMSG
AC_NOT_IF_QUERY_ENTITY=Not allowed in entities of graphical view or association view.
#XMSG
AC_NOT_IF_HIERARCHY_QUERY_ENTITY=Not allowed in entities of graphical view.
#XMSG
AC_NOT_IF_ELEMENT_OF_ASSOCIATION=Not possible for association.
#XMSG
AC_NOT_IF_ELEMENT_OF_REMOTE_TABLE=Remote table columns aren’t editable.
#XMSG
VAL_ENTER_VALID_DATE_GEN=Invalid Date Format
#XMSG
AC_NOT_IF_ELEMENT_OF_LOCAL_SCHEMA=Not for attributes of table from local schema.
#XMSG
AC_NOT_IF_ELEMENT_OF_VIEW=Not for elements of views.
#XMSG
AC_ONLY_IF_ELEMENT_IS_CALCULATED=Not for regular columns.
#XMSG
AC_ONLY_IF_ELEMENT_NOT_FOR_GRAPHICAL_VIEW=Not for graphical view.
#XMSG
AC_ONLY_IF_ELEMENT_NOT_FOR_SQL_VIEW=Not for sql view.
#XMSG
AC_ONLY_IF_IS_NEW=Technical names cannot be modified once an object is saved.
#XMSG
AC_NOT_IF_IS_VIEW=Not for views.
#XMSG
AC_ONLY_IF_IS_VIEW=Only for views.
#XMSG
AC_NOT_IF_IS_REMOTE=Not for remote tables.
#XMSG
AC_ONLY_IF_ELEMENT_HAS_MEASURE_DATATYPE=Only for data types that are compatible with measures.
#XMSG
AC_NOT_IF_ELEMENT_IS_KEY=Not for keys.
#XMSG
AC_NOT_IF_ELEMENT_HAS_FORBIDDEN_DATA_TYPE=Not available for this data type.
#XMSG
AC_NOT_IF_ELEMENT_HAS_SEMANTIC_TYPE_TEXT=Not available for elements with semantic type "text".
#XMSG
AC_ONLY_IF_DATA_TYPE_SUPPORTED_AS_KEY=Only available if data type can be used as key.
#MSG
AC_ONLY_IF_DATA_TYPE_CHANGE_SUPPORTED=Not available for geo data types if table has data.

#XMSG
allowConsumptionInfo=Makes the object available for consumption in SAP Analytics Cloud (Analytical Datasets only, along with their dependencies) and other clients (all types).
#XMSG Info help text for unionAll switch
unionAllInfo=Two types are available: <strong>Union All</strong> and <strong>Union</strong>. <ul><li><strong>Union All</strong> is the default union and the fastest to create. It combines two or more SELECT statements or queries and includes duplicate rows.</li> <li><strong>Union</strong> combines two or more SELECT statements or queries. It takes longer to create it because it removes duplicate rows.</li></ul>
#XMSG
TARGET_NOT_EXIST=Target is not available in the repository. Please delete the association or import the target.
FILTER_RESTRICT_CREATE=Remove the replicated data before creating a filter.
FILTER_RESTRICT_UPDATE=Remove the replicated data before editing a filter.
FILTER_NOT_SUPPORTED_ABAP=Filtering is not supported for ABAP connections.
FILTER_NO_FILTER_COLUMN=The table contains no filterable columns.
FILTER_NOT_SUPPORTED_CDI=Filtering is not supported for Cloud Data Integration connections.
FILTER_NOT_SUPPORTED_SDA=Filtering is not supported for SDA based connections.
#MSG
VAL_ASSOCIATION_NOT_UNIQUE=Association name is not unique.
VAL_ASSOCIATION_EMPTY=Association name is empty.

#XMSG
viewPersistenceInfo=Data is quickly accessible and available locally when the view is persisted.

#XMSG
infoViewWithIP=An object with input parameters cannot be selected as target.

#XMSG
viewPersistenceSchedule=This action will start the data persistence of the view "{0}"

#XMSG
oldDeployedViewSchedulePersistence=This action will start the data persistence of previously deployed version of the view "{0}"



##Function categories
FUNCTION_CATEGORY_ALL=All Functions
FUNCTION_CATEGORY_STRING=String
FUNCTION_CATEGORY_NUMERIC=Numeric
FUNCTION_CATEGORY_DATETIME=Datetime
FUNCTION_CATEGORY_MISCELLANEOUS=Miscellaneous
FUNCTION_CATEGORY_AGGREGATE=Aggregate
FUNCTION_CATEGORY_SECURITY=Security
FUNCTION_CATEGORY_FULLTEXT=Fulltext
FUNCTION_CATEGORY_WINDOW=Window
FUNCTION_CATEGORY_SERIES_DATA=Series Data
FUNCTION_CATEGORY_DATA_TYPE_CONVERSION=Data Type Conversion

#XMSG: MessageToast for preview failure
cannotPreview=The data preview is not possible because the table definition contains errors.




#XFLD: View Monitor title
viewPersMonitorLnk=Views Monitor
#XTIT: View Persistence schedule confirmation header
confirmScheduleHeader=Confirm Schedule
#XMSG: success message for starting persistence
startPersistenceSuccess=We’re persisting the view "{0}".
#XMSG: success message for stopping persistence
stopPersistenceSuccess=We’re removing persisted data for view "{0}".
#XMSG: error message during start persistence of a view (backend error)
ERROR_SCHEDULING_PERSISTENCE=An error occurred while scheduling the data persistence for the view "{0}".
#XMSG: error message during stop persistence of a view (backend error)
STOP_PERSISTENCE_ERROR=An error occurred while stopping the data persistence for the view "{0}".
#XBUT: Tooltip for load new peristence
loadNewPersistence=Re-Start Data Persistence
#XMSG: error message for reading data from backend
txtReadBackendError=An error occurred while reading data persistence information from the back end.
#XFLD: View Persistency access
persistencydataAccess=Data Access:
#XFLD: View Persistency access Partial
partiallyPersisted=Partially Persisted
#XFLD: View Persistency access without ":"
dataAccess=Data Access
#XFLD: Activity label
activity=Activity
#XFLD: The value of View Persistency access when the view is not persisted
dataAccessVirtual=Virtual
#XFLD: View Persistency status
persistencyStatus=Status
#XFLD: Last Persistency update
lastPersistedTimeLbl=Updated at
#XFLD: View Pesistence status persisted
persistencyPanelTitle=Data Persistence
#XBUT: Menu Button tooltip for view persistancy actions
ViewPersistencyMenu=Data Persistence
#XBUT: Menu Button tooltip for view persistancy actions
ViewPersistencyMenuNew=Data Persistence
#XBUT: Menu Button tooltip for schedule view persistancy actions
ScheduleViewPersistenceMenu=Schedule View Persistency
#XBUT: Menu Button tooltip for schedule view persistancy actions
ScheduleViewPersistenceMenuNew=Schedule Data Persistence
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabel=Load New Snapshot
#XFLD: Label for Remove persisted data menuitem
#XFLD: Label for Load new snapshot menuitem
loadNewSnapShotLabelNew = Start Data Persistence
removePersistedDataLabel=Remove Persisted Data
#XMSG: Warning message for removing the persisted data
RemovePersistency_Confirm_Msg=Do you want to delete the persisted data and switch to virtual access of the view "{0}"?
#XFLD: Label for Refresh Persistency info
txtRefresh=Refresh
#XFLD: Label for Refresh Persistency info
refreshPersistencyText=Getting Data Persistence Information
#XFLD: Label for No Data Error
NoDataError=Error
#XFLD: Label for Start View Analyzer
startViewAnalyzerLabel=Start View Analyzer


#XFLD: placeholder for empty cell
emptyCell=---
#XFLD: text for values shown in Persistence Status
txtLoading=Loading
#XFLD: text for values shown in Persistence Status
txtAvailable=Available
#XFLD: text for values shown in Persistence Status
txtError=Error

#XFLD: Label for menu item to Add Filter
@defineFilterOnColumn=Add Story Filter
#XFLD: Label for menu item to Edit Filter
@editFilterOnColumn=Edit Story Filter
#XFLD: Label for menu item to Remove Filter
@clearFilterOnColumn=Remove Story Filter
#XFLD: Label for Filter Default Value
@filterDefaultValue=Default Value
#XFLD: Label for Filter Mandatory
@filterMandatory=Mandatory
#XFLD: Label for Filter Default Value Hign
@filterDefaultValueHigh=Default Value High
#XFLD: Label for Filter Multiple Entries
@filterMultipleSelections=Multiple Entries
#XFLD: Label for Filter Hidden
@filterHidden=Hide Filter
#XFLD: Label for Filter Selection Type
@filterSelectionType=Type
#XFLD: Label for Filter
@filter=Story Filter
#XFLD: Label for Filter From Default value
@filterDefaultValueFrom=From
#XFLD: Label for Filter To Default value
@filterDefaultValueTo=To
#XTOL Tooltip for Clear icon
@clearFilter=Clear
#XBUT: Text for home button
txtHome=Home
#XFLD: ready label
lblReady=Ready
#XLBL: Delta capture label
lblDelta=Delta Capture
#XLBL: Delta capture label table name
lblDeltaTableName=Delta Capture Table
#XLBL: Delta capture label
deltaOutboundLbl=Delta Outbound
#XLBL: Delta Outbound table name
lblDeltaOutboundTableName=Delta Outbound Table
#XFLD: success label
lblSuccess=Success
#XFLD: failure label
lblFailed=Failed
#XBUT: Text for Add selection button
btnAddSelection=Add Selection
#XFLD: fetching status label
lblFetchingDetail=Fetching details
#XMSG Place holder text for tree filter control
filterPlaceHolder=Type text to filter top-level objects
#XMSG Place holder text for server search control
serverSearchPlaceholder=Type and press Enter to search

# begin replace-source mapping popover
replaceSourceText=Map the columns from the old source to the new source. You do not have to map all the old source columns, but any that are not mapped will be removed from the view and might generate errors or warnings.
#XTIT: Title of confirm popover "replace-source mapping"
warning=Warning
#XMSG: Message on the confirm popover "replace-source mapping" -> NOT all columns of old source have been mapped. These columns will be removed and may lead to validation error messages
confirmReplaceIncompleteMappingText=Old source columns remain unmapped and will not be replaced ({0}). Their absence may cause validation errors.
# end replace-source mapping popover

# begin dependent objects list
dependentObjectList=Dependent Objects
#XFLD: Single question mark to be used as "unknown" counter of section labels or panel header: "Dependent Objects (?)". As soon as data has been loaded, "?" gets replaced by the number of objects.
@questionMark=?
#XMSG: Informing the user about why we cannot display the dependent object lsit for this entity/source/repo-object (technical reasons)
dependentObjectListCannotDisplayMissingRepoId=The dependent object list cannot be displayed because you are not assigned to the source’s space.
#XFLD: Label for segmented button (all/errors only)
show=Show:
#XBUT
errorsOnly=Errors Only
#XBUT
refresh=Refresh
#XMSG
dependentObjectsWoPermission=There are other dependent objects that you do not have permission to view ({0}).
#XMSG
dependentObjectsIsCrossSpace=The object "{1}" has been shared from the space "{0}".\nTo see all dependent objects, navigate to the original object and have a look at it’s dependent objects from there.
#XCOL
subDependencies=Sub-Dependencies
#XCOL
dependencyType=Dependency Type
#XCOL
shared=Shared
#XCOL
changedBy=Changed By
#XCOL
changedOn=Changed On
#XTOL: Tooltip of dependent object list, perspective(s) of consumtion model (see business builder)
fileTypeConsumptionModel=Consumption Model (Perspectives)
#XTOL: Tooltip of dependent object list, perspective (consumtion model, see business builder)
fileTypePerspective=Perspective (Consumption Model, Business Builder)
#XFLD: Type/Kind of dependency (see Deepsea, DependencyKind), currently we only use "Source" and "Association"
dependencyTypeSource=Source
#XFLD: Type/Kind of dependency Target
dependencyTypeTarget=Target
#XFLD
dependencyTypeAssociation=Association
#XFLD
dependencyTypeHierarchyAsso=Hierarchy Association
#XFLD
dependencyTypeUsage=Usage
#XFLD
dependencyTypeValueHelpEntity=Parameter Value Help Source
# end dependent objects list

#begin input parameters and variables
#XMSG
txtParameter=Input Parameter
#XMSG
txtParameters=Input Parameters
#XMSG
txtAnalyticParameters=Analytical Parameters
#XMSG
editParameter=Edit Parameter
#XFLD
txtParametersTitle=Input Parameters ({0})
#XBUT
txtOk=OK
#XBUT
txtCancel=Cancel
#XBUT
txtAdd=Add
#XFLD
txtReplace=Replace
#XFLD
txtType=Type
#XFLD
txtDescription=Description
#XFLD
txtParameterType=Parameter Type
#XFLD
txtValueHelpColumn=Value Help Column
#XFLD
txtDefaultValue=Default Value
#XFLD
txtSemanticType=Semantic Type
#XFLD
txtDataType=Data Type
#XFLD
txtAttribute=Attribute
#XMSG
txt_amount_with_currency_code=Amount with Currency Code
#XMSG
txt_quantity_with_unit_of_measure=Quantity with Unit Of Measure
#XFLD
txtToValue=To Value
#XFLD
txt_create_Parameter=Create Parameter
#XMSG
txtParametersOnlyInFact=Parameter can only be created in analytical dataset views or fact views.
txtInfo=Information

#XMSG
txtPersistedViewDrop=If you create and deploy a parameter on a persisted view, the persistency is removed and cannot be set again as it does not support input parameters.
#XMSG
txtPersistedViewDropNew=A persisted view can support only one single input parameter for which a default value is defined.
txtWarning=Warning
#XMSG
txtParametersDescription=Create input parameters in the object to be used in calculated columns or inner filters.
#XMSG
txtAnalyticParametersDescription=Create analytical parameters in the analytical measures to be used in restricted measures
#XMSG
txtEmptyName=Parameter name cannot be empty. Resetting value to old name: {0}...
#XFLD
txtId=ID
#XFLD
select_source=Select Source
#XMSG
showParameters=Show Parameters
#XMSG
@noParameters=There are currently no parameters available. To add some, please press the Edit button in the Data Properties panel.
@cannotCreateParameters=Cannot create input parameters as this object is being used as the association target in another object.
#XMSG
associtedDataEntites=Associated Data Entities ({0})
#XMSG
associationColumns=Columns ({0})
#XMSG
txtselectcolumnfromthecurrentviewassociation=Select a column from the current view or associated data entities
#XMSG
txtselectvalue=Select a value
#XFLD
txtselect=Select
#XFLD
txtinputparameter=Input Parameters ({0})
#XFLD
txtAnalyticParameter=Analytical Parameters ({0})
#XFLD
txtLength=Length
#XFLD
txtPrecision=Precision
#XFLD
txtScale=Scale
#XFLD
txtSrid=SRID

#end input parameters and variables
#XTIT
ip_enterValuesToGenerateData=Enter values to generate your data.
#XTIT
ip_inputParameters=Input Parameters
#XFLD
ip_name=Name
#XFLD
ip_value=Value
#XMSG No value entered in Input parameter pop up
ip_no_value_entered=Please enter a value to preview data.

#XMSG text for table control in remote browse when empty
msgRemoteNoDataText=No Data
#XMSG Search message for table control in remote browse
msgRemoteSearchText=Please search for the required dataset in "{0}"

#XFLD: Object type is a context
txtTypeContext=Context
#XFLD: Object type is a simple type
txtTypeSimpleType=Simple Type
#XFLD: Object type is a DAC
txtTypeDAC=Data Access Control
#XMSG
msgChooseParametername=Choose a parameter name
#XMSG
msgtechnicalforparametermustbeunique=The technical name for parameter must be unique

#XFLD: Analytical measure header
txtAnalyticalMeasures=Analytical Measures ({0})
#XTIT
titleAnalyticalMeasures=Analytical Measures
#XFLD: Analytical measures menu item formula
txtFormula=Formula
#XFLD: Restricted Measure menu item
txtRestrictedMeasure=Restricted Measure
txtFormula1=Formula
#XFLD: Exception Aggregation menu item
txtExceptionAggr=Exception Aggregation
#XFLD: Exception Aggregation detail screen text labels
txtSourceMeasureAggrType=Aggregation Type
txtExceptionAggrType=Exception Aggregation Type
txtExceptionAggrAttributes=Exception Aggregation Attributes

txtAddAnalyticMeasureElement=Add Analytical Measures
txtSourceMeasure=Source Measure
#XMIT Menu in Analytical Measure More Menus
txtMoveToTop=Move to Top
#XMIT Menu in Analytical Measure More Menus
txtMoveUp=Move Up
#XMIT Menu in Analytical Measure More Menus
txtMoveDown=Move Down
#XMIT Menu in Analytical Measure More Menus
txtMoveToBottom=Move to Bottom
#XMIT Menu in Analytical Measure More Menus
txtDelete=Delete
txtAddRow=Add Filter
#XMSG
VAL_PARAM_EMPTY_NM=The name of the parameter is empty.
#XMSG
VAL_PARAM_NAME_NOT_UNIQUE=Parameter name is not unique.
#XFLD: Count Distinct menu item
txtCountDistinct=Count Distinct
txtReferenceAttributes=Attributes
txtAnalyticMeasure=Analytical Measure
emptyCountReferenceAttributes=Please select at least one reference attribute.
#XMSG: Error state messages for Exception aggregation input fields
emptyExcAggrAttributes=Please select at least one aggregation attribute.
#XMSG
emptySourceMeasure=Please select or enter a valid source measure.
#XMSG
analyticSourceNotMeasure=The selected measure can’t be found.
@COLUMNS_LABEL=Columns ({0})


#XLBL: Label for partition availability
partitions=Partitions
#XMSG: Message to enter correct Precision, Scale and Length value
@msgPrecision=Enter a value between 1 and 38.
@msgScale=Enter a value between 0 and Precision value.
@msgLength=Enter a value between 1 and 5000.

#XBUT: Drop down menu button to create a schedule
createScheduleLabel=Create Schedule
#XBUT: Drop down menu button to edit a schedule
changeScheduleLabel=Edit Schedule
#XBUT: Drop down menu button to delete a schedule
deleteScheduleLabel=Delete Schedule
#XMSG: Description to create scheduling persistency
createSchedulingPersistencyDescription=Create schedule for Persistency
#XMSG: Description to edit scheduling persistency
editSchedulingPersistencyDescription=Edit schedule for Persistency
#XMSG: Description to delete scheduling persistency
deleteSchedulingPersistencyDescription=Delete schedule for Persistency
#XFLD:Key column dialog title
@keyDefinition=Compound Key
#XMSG:Key column dialog description
@keycolumndialogDesc=Define a compound key by ordering key columns and adding a representative key.
@editCompoundKey=Edit Compound Key
@removeBtn=Remove
@addBtn=Add
@addRepresentativeKey=Add as Representative Key
@removeRepresentativeKey=Remove as Representative Key
@compoundKey=Compound Key:
@keyColumns=Key Column(s)
@moveDown=Move Down
@moveUp=Move Up
@representativeKey=Representative Key
@addKeyColumnHere=Add a key column here.
@defineRepresentativeKeyDefine=Define a representative key to create a compound key.
@parentChildHierarchy=Parent-Child Hierarchy:
@hierarchyParentList=Parent:
@hierarchyChildList=Child:
@CompoundKey=Compound Key
@keyColumnsTitle=Key Column(s)
@selectValue=Insert Values for {1}
@insert=Insert
@selectedValues=Selected Values
@BETWEEN_OPERATOR_ONLY_SELECT_TWO=Only two values can be selected for operator BETWEEN
ValueHelpServiceFailed=Value help request failed with error : \r\n
@cannotFetchValue=Insert a column name and an operator in the Expression field to open the Insert Values dialog.
@noValueHelp=No Value Help
@currentViewValueHelp=Predefined Value from the Current View
@findSourceForValueHelp=Predefined Value from an Object
@txtIsValueHelp=Default Value Help
@txtObjectSelection=Object
@txtColumnSelection=Column
#XTEXT Header text for Partitions Section
partition_header=Partitions ({0})
#XTEXT Header text for Partitions Section for LTF Tables
partition_headerLtf=Partition Columns ({0})
fileStorageText=File
@txtStorageType=Storage
@txtStorageTypeTable=Storage
disk=Disk
inMemory=In-Memory
requiredDeltaCaptureLabel=Delta Capture (Required for File Storage)
@txtDeltaCaptSettingsNoChange=The delta capture setting cannot be changed after deployment.
#XBUT Create Partition button  to define partitions
@createPartitions=Define Partitions
#XBUT Create Partition button  to define partitions for LTF Tables
@createPartitionsLtf=Define Partition Columns
@txt_partition_column=Partitioning Column
@noPartitionsDescription=Defining Partitions can improve performance.

#XTIT: Compatibility contracts - Select Successor Object
selectSuccessor=Select Successor Object
#XTIT: Annotations Dialog title
dialogTitle=Edit Custom CSN Annotations
#XBUT
OK=OK
#XBUT
Cancel=Cancel
#XBUT
Close=Close
#XBUT: Previous Annotation
previousAnnotation=Previous Annotation
#XBUT: Next Annotation
nextAnnotation=Next Annotation
#XMSG: No custom annotation
noAnnotation=No custom annotation
#XMSG: One custome annotation
oneAnnotation=One custom annotation
#XMSG: custom annotations plural
nAnnotations={0} custom annotations
#XMSG: {i} "of" {nAnnotations}
iofn={0} of {1}
#XBUT: Validate CSN expression
validateExpression=Validate CSN expression
#XFLD: CSN Annotations Window Title
CSNAnnotations=Edit Custom CSN Annotations
#XMSG: Validating CSN expression...
validating=Validating CSN expression
#XMSG: Valid CSN
ValidCSN=The CSN expression is valid.
#XMSG: Invalid CSN
invalidCSN=The input is invalid input.
#XMSG: Invalid empty CSN
emptyCSN=An expression cannot be empty!
#XMSG
invalidRoot=It’s not allowed to change the root name "{0}"
#XMSG
invalidCollection=Added collections "{0}" ignored
#XMSG
invalidObject=Added objects "{0}" ignored
#XMSG
invalidAttribute=Changes to attributes "{0}" are ignored
#XMSG
missingCollection=Collections "{0}" are restored
#XMSG
missingObject=Objects "{0}" are restored
# XFLD select button text of object selection dialog
btn_select=Add Object
# XFLD title of object selection dialog
dialog_title=Add Object
# XFLD Plural Label
valueHelp_selectedValues=Selected Values
# XFLD Singular Label
valueHelp_selectedValue=Selected Value
partitionedColumnsTextLabel=Partition Columns
deleteAllText=Delete all records
deleteAllPermanent=Delete All Records
deleteAllPermanentSubText= This action cannot be undone. All records will be physically deleted. You may need to check the dependent objects to ensure that flows processing this table in delta mode are not impacted.
deleteLogical=Delete All Records (Mark as "Deleted")
deleteLogicalSubText=All records will take the change type "D" but still can be processed by SAP Datasphere apps until they are permanently deleted.
deleteHouseKeepingLTF=Delete previous versions (vacuum), which are older than the specified number of days.
deleteHouseKeeping=Delete all records marked for deletion which are older than the specified number of days.
deleteHouseKeepingSubText=Based on the defined settings, all fully processed records with change type "D" will be permanently deleted.
deleteRecordsRetentionTime=Delete all fully-processed records with Change Type "Deleted", which are older than:
deleteLogicalLTFSubText=Records will not be physically deleted, but marked as deleted and filtered out when accessing the active records of the local table.
deleteNoActivedataTextLTF=The table contains no active data. Previous versions can be deleted.
deleteHouseKeepingLTFSubText=Records from the last 7 days cannot be deleted. In addition, only records that have been fully processed can be deleted.
deleteTableDataDialogTitleNew=Delete
dataDeletionTextForNonDelta=You are about to delete all records. This action cannot be undone. Do you want to continue?
dataDeletionTextForLTF=You are about to mark all records for deletion. Records will not be physically deleted (truncate), but marked as deleted and filtered out when accessing the active records of the local table. Do you want to continue?
deleteAllDescription=You may need to check the dependent objects to ensure that flows processing this table in delta mode are not impacted.
cancelButton=Cancel
defaultValueText= Enter a value with a maximum of {0} characters.
#XFLD
DATAVALIDATIONSTATUS=Data Validation Status
#XFLD
VALIDATEDON=Validated On
#XTIT
@importOverriteReleasestate=Objects that are already present in the repository can only be selected for re-import if their release states are consistent with the objects they will be replacing.
#XMSG: Message for Settings used for data persistence
partiallyPersistedHeader=Settings used for last data persistence run:
#XMSG: Message for input parameter name
inputParameterLabel=Input Parameter
#XMSG: Message for input parameter value
inputParameterValueLabel=Value
#XMSG: Message for persisted data
inputParameterPersistedLabel=Persisted At
#XTIT: Count for View in ViewMonitor Toolbar
@sourceObject=Source Object
#XMSG: Message for adjusting link routing
@msgLinkRoutingAdjusted=Links optimized
#XFLD Dialog Heading
apacheSparkHeading=Apache Spark Application Settings
#XFLD Dialog Heading
deletionType=Deletion Type
#XFLD Radio Button: Use Space Default
useSpaceDefault=Use Space Default
#XFLD Radio Button: Define New Setting
defineNewSetting=Define new setting for this task
#XFLD Label: Application
applicationLabel=Application:
#XFLD Button: Close
closeButton=Close
