/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { ObjectKind } from "@sap/deepsea-types";
import { isNull } from "lodash";
import { getArtefactSharesForTarget, valueHelpService } from "../../../services/metadata";
import { BuilderConfig, IWorkbenchController } from "../../abstractbuilder/api";
import {
  isHarmonizationObjectSelection,
  isModelingParameterLineageEnabled,
} from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import {
  AbstractController,
  AbstractControllerClass,
} from "../../abstractbuilder/controller/AbstractController.controller";
import { AbstractDiagramEditorClass } from "../../abstractbuilder/controller/AbstractDiagramEditor.controller";
import { getWorkbenchController } from "../../abstractbuilder/utility/BuilderUtils";
import { getCurrentOrCrossSpaceObjectByName } from "../../abstractbuilder/utility/RepositoryUtils";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import {
  AnalyticsType,
  getAnalyticsType,
  getDataCategoryName,
  getFileType,
  getFileTypeAndIcon,
  getFullDataCategoryName,
  getFullTypeNameFromTypes,
  getTypeAndIcon,
} from "../../businesscatalogs/utility/BusinessCatalogsUtility";
import { SupportedFeaturesService } from "../../commonmodel/api/SupportedFeaturesService";
import { CsnAnnotations } from "../../commonmodel/csn/csnAnnotations";
import {
  findOrCreateAndAttachDACFromCSN,
  getEntityNameFromCSN,
  updateViewFromDataAccessControlCSN,
} from "../../commonmodel/csn/csnUtils";
import {
  CDSDataType,
  CardinalityValues,
  DataCategory,
  DimensionType,
  SRID_VALUES,
} from "../../commonmodel/model/types/cds.types";
import {
  FilterSelectionTypes,
  getListOfConsumedInputParametersInAggr,
  getListOfConsumedInputParametersInCC,
  getListOfConsumedInputParametersInFilter,
  isORDTable,
} from "../../commonmodel/utility/CommonUtils";
import { getReleaseStateFromValue } from "../../commonmodel/utility/CompatibilityContractUtils";
import { HierarchyValidation } from "../../commonmodel/utility/HierarchyUtils";
import {
  addHierarchyAssociationEnableFormatter,
  getHWDMessageIds,
  openHierarchyWithDirectoryDialog,
} from "../../commonmodel/utility/HierarchyWithDirectoryUtils";
import { updateAnnotationByAssociation } from "../../commonui/utility/AssociationUtils";
import { NameUsage } from "../../commonui/utility/NameInputValidator";
import { ImpactAndLineageHighlighter } from "../../csnquerybuilder/js/diagram/ImpactAndLineageHighlighter";
import { getUnpushedAssociations, updateUnresolvedAssociations } from "../../csnquerybuilder/utility/AssociationHelper";
import { AbstractDataBuilderEditorComponentClass } from "../../databuilder/AbstractDataBuilderEditorComponent";
import { DataBuilderWorkbench } from "../../databuilder/controller/DataBuilderWorkbench.controller";
import { Editor } from "../../databuilder/utility/Constants";
import {
  dataCategoryIconFormatter,
  getDatabuilderWorkbench,
  getIsEditorSupportVersions,
  getLinkText,
  openDatabuilderValidationsPopoverBy,
} from "../../databuilder/utility/DatabuilderHelper";
import {
  aggregationValidationCount,
  aggregationValidationType,
  aggregationValidationTypeCustomStyle,
  areAllTrue,
  collectionHeaderTextBusyFormatter,
  collectionHeaderTextFormatter,
  isEmpty,
  isFilled,
  toggleLabel,
} from "../../ermodeler/js/utility/commonFormatter";
import { getSpaceCSN } from "../../managespaces/model/SpaceAPIProxy";
import { openExplorerSelectorDialogExt } from "../../reuse/control/explorerselectordialog/openExplorerSelectorDialogExt";
import { revertDataCategoryAIChange } from "../../reuse/utility/DeltaUtil";
import { Format } from "../../reuse/utility/Format";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { NamingHelper } from "../../reuse/utility/NamingHelper";
import { getBusinessName } from "../../reuse/utility/RepoHelper";
import { ShellContainer } from "../../shell/utility/Container";
import { Repo } from "../../shell/utility/Repo";
import { DWCFeature, EventType } from "../../shell/utility/ShellUsageCollectionService";
import { DataBuilderAction } from "../../shell/utility/UsageActions";
import { TokenListItem } from "../../tokenListItem/control/TokenListItem";
import { ObjectNameDisplay } from "../../userSettings/utility/Constants";
import { SidepanelMode } from "../js/statics/const/EntityPropertiesSidepanel";
import { QualifiedClassNames, entityTypeModelData } from "../js/statics/const/er.model";
import * as commonUtils from "../js/utility/CommonUtils";
import { getAllObjectsOfSpace, getMappings } from "../js/utility/DependencyUtil";
import {
  SORT_DEP_OBJ_BUTTON_DATA,
  dependencyTypeTextFormatter,
  getObjectStatusSeverity,
  objectStatusIconFormatter,
  objectStatusTextFormatter,
  objectStatusTooltipFormatter,
} from "../js/utility/sharedFunctions";

export enum UsageActions {
  SORT_COLUMNS = "sortColumns",
  ADD_FORMULA_ELEMENT = "addFormulaElement",
  ADD_RESTRICTED_MEASURE = "addRestrictedMeasure",
  ADD_EXCEPTION_AGGR_ELEMENT = "addExceptionAggrElement",
  ADD_COUNT_DISTINCT = "addCountDistinct",
}
export enum Pages {
  ListPage = "ListPage",
  DetailsPage = "associationDetailsPage",
  DACLPage = "DaclDetailsPage",
  FilterColumnPage = "filterColumnDetailsPage",
  FormulaDetailsPage = "formulaDetailsPage",
  RestrictedMeasureDetailsPage = "restrictedMeasureDetailsPage",
  ExceptionAggregationDetailsPage = "exceptionAggregationDetailsPage",
  CountDistinctDetailsPage = "countDistinctDetailsPage",
}

export enum TableTypes {
  LOCALTABLE = "Local Table",
  REMOTETABLE = "Remote Table",
  RELATIONALDATASET = "Relational Dataset",
  ANALYTICALDATASET = "Analytical Dataset",
  FACT = "Fact",
  ALL = "all",
}

// __DP__ Analitical Measure views
export class AnalyticMeasureHelper {
  static oAMParams = {
    // Formula
    Formula: {
      fAMcreateNew: sap.cdw.querybuilder.NodeImpl.createNewFormulaElement,
      sAMpropertyName: "/selectedAnalyticMeasure",
      sAMviewId: "formulaDetailsContainer",
      sAMDetailsPage: Pages.FormulaDetailsPage,
      sAMusageAction: UsageActions.ADD_FORMULA_ELEMENT,
    },
    // RestrictedMeasure
    RestrictedMeasure: {
      fAMcreateNew: sap.cdw.querybuilder.NodeImpl.createRestrictedMeasure,
      sAMpropertyName: "/selectedAnalyticMeasure",
      sAMviewId: "restrictedMeasureDetailsContainer",
      sAMDetailsPage: Pages.RestrictedMeasureDetailsPage,
      sAMusageAction: UsageActions.ADD_RESTRICTED_MEASURE,
    },
    ExceptionAggregation: {
      fAMcreateNew: sap.cdw.querybuilder.NodeImpl.createNewExceptionAggregationElement,
      sAMpropertyName: "/selectedAnalyticMeasure",
      sAMviewId: "exceptionAggregationContainer",
      sAMDetailsPage: Pages.ExceptionAggregationDetailsPage,
      sAMusageAction: UsageActions.ADD_EXCEPTION_AGGR_ELEMENT,
    },
    // CountDistinct
    CountDistinct: {
      fAMcreateNew: sap.cdw.querybuilder.NodeImpl.createCountDistinct,
      sAMpropertyName: "/selectedAnalyticMeasure",
      sAMviewId: "countDistinctDetailsContainer",
      sAMDetailsPage: Pages.CountDistinctDetailsPage,
      sAMusageAction: UsageActions.ADD_COUNT_DISTINCT,
    },
  };

  public static getAMParams(sAMkind: string): any {
    return AnalyticMeasureHelper.oAMParams[sAMkind];
  }
  public static getAMViewId(sAMkind: string): string {
    return AnalyticMeasureHelper.oAMParams[sAMkind]?.sAMviewId;
  }
}
export class CommonPropertiesClass extends AbstractControllerClass {
  static smallWidth: sap.ui.core.CSSSize = "460px";
  public oResourceCommonModel: sap.ui.model.resource.ResourceModel;
  public oResourceModel: sap.ui.model.resource.ResourceModel;
  public highlightCounter: number;
  public getCurrentNode: any;
  public isEditable: boolean;
  public isCanModifyDefaultWidth: boolean = false;
  public formatters = {
    toggleLabel: toggleLabel,
  };
  protected dacInfoPopover: sap.m.Popover;
  protected associationtInfoPopover: sap.m.Popover;
  protected viewFilterInfoPopover: sap.m.Popover;
  protected subscriptionsDone: boolean; // make sure we only subscribe once per event
  protected modelObjectForDependentObjects: any;
  private resizeHandlerAttached: boolean;
  private databuilderWorkbench: DataBuilderWorkbench;
  private diagramEditor: sap.galilei.ui.editor.DiagramEditor;
  private fragmentIdToControlId = {};

  public setDefaultPropertiesWidth(browserWidth?: number): void {
    if (this.isCanModifyDefaultWidth) {
      const minWidth = 460;
      const maxWidth = 520;
      const nWidth = browserWidth ? browserWidth : window.innerWidth;
      const nPercentage = 34;
      let propertiesDefaultWidth: number = nWidth ? (nWidth * nPercentage) / 100 : minWidth;
      if (propertiesDefaultWidth < minWidth) {
        propertiesDefaultWidth = minWidth;
      }
      if (propertiesDefaultWidth > maxWidth) {
        propertiesDefaultWidth = maxWidth;
      }
      BuilderConfig.PROPERTIES_DEFAULT_WIDTH = propertiesDefaultWidth + "px";
      // hook view resize events
      if (!this.resizeHandlerAttached) {
        sap.ui.Device.resize.attachHandler(function (oEvent) {
          CommonProperties.prototype.setDefaultPropertiesWidth(oEvent.width);
        });
        this.resizeHandlerAttached = true;
      }
    }
  }

  public onDefaultInit(): void {
    const commonBundleName = require("../../commonmodel/i18n/i18n.properties");
    this.oResourceCommonModel = new sap.ui.model.resource.ResourceModel({
      bundleName: commonBundleName,
    });
    this.getView().setModel(this.oResourceCommonModel, "i18n_commonmodel"); // For other components to reuse common model resources

    const bundleName = require("../i18n/i18n.properties");
    this.oResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
      enhanceWith: [this.oResourceCommonModel.getResourceBundle()],
    });
    this.getView().setModel(this.oResourceModel, "i18n");
    this.getView().setModel(this.oResourceModel, "i18n_erd"); // For other components to reuse ER Modeler resources

    const vbBundleName = require("../../csnquerybuilder/i18n/i18n.properties");
    const resourceVBModel = new sap.ui.model.resource.ResourceModel({
      bundleName: vbBundleName,
    });
    this.getView().setModel(resourceVBModel, "i18n_vb");

    const model = this.getView().getModel();
    if (!model) {
      // add an empty default model
      this.getView().setModel(new sap.ui.model.json.JSONModel({}));
    }
    super.onInit();
  }

  public onInit(): void {
    this.onDefaultInit();
    super.onInit();
    sap.ui
      .getCore()
      .getEventBus()
      .subscribe("HIERARCHY_VALIDATION", "UPDATE_VALIDATION", this.updateHierarchyValidation.bind(this));
  }

  public getDiagramEditor(): sap.galilei.ui.editor.DiagramEditor {
    if (!this.diagramEditor) {
      const controller = this.getDatabuilderWorkbench()
        .getActiveEditor(true)
        .view()
        ?.getController() as AbstractDiagramEditorClass;
      this.diagramEditor = controller?.getDiagramEditor && controller?.getDiagramEditor();
    }
    return this.diagramEditor;
  }

  public getCDSDataModel(object: object): object {
    return {
      dataTypes: sap.cdw.commonmodel.ObjectImpl.getPossibleDataTypes(object),
      sridValues: SRID_VALUES,
    };
  }

  protected getGalileiModel() {
    return this?.getView()?.getModel("galileiModel") as sap.galilei.ui5.GalileiModel;
  }

  private operationNodeNameChange(event: IEvent<sap.m.Input, {}>) {
    const nameValidator = NamingHelper.getNameInputValidator();
    const node = this.getView().getModel("header").getProperty("/");
    const input = event.getSource();
    const siblings = node.container.nodes;
    const commonBundle = this.oResourceCommonModel.getResourceBundle();
    const emptyMsg = "VAL_NODE_NO_NAME";
    nameValidator.onTechnicalNameChanged(
      node,
      input,
      "name",
      {
        duplicatedTechnicalName: commonBundle.getText("VAL_TABLE_DUPLICATED_ELT"),
        emptyTechnicalName: commonBundle.getText(emptyMsg),
      },
      siblings,
      NameUsage.intermediatenode
    );
  }

  private operationNodeNameSubmit(event: IEvent<sap.m.Input, {}>) {
    const nameValidator = NamingHelper.getNameInputValidator();
    const node = this.getView().getModel("header").getProperty("/");
    const input = event.getSource();
    const siblings = node.container.nodes;
    const commonBundle = this.oResourceCommonModel.getResourceBundle();
    const emptyMsg = "VAL_NODE_NO_NAME";
    nameValidator.onTechnicalNameSubmit(
      node,
      input,
      "name",
      {
        duplicatedTechnicalName: commonBundle.getText("VAL_TABLE_DUPLICATED_ELT"),
        emptyTechnicalName: commonBundle.getText(emptyMsg),
      },
      siblings,
      /* rollbackIfEmpty */ true,
      NameUsage.intermediatenode
    );
  }

  protected getParentChildModel(hierarchy) {
    const parentElements = hierarchy?.parentElement?.toArray();
    const childElements = hierarchy?.childElement?.toArray();
    const parentElementCount = hierarchy?.parentElement?.length ? hierarchy?.parentElement?.length : 1;
    const childElementCount = hierarchy?.childElement?.length ? hierarchy?.childElement?.length : 1;
    const parentChildCount = parentElementCount > childElementCount ? parentElementCount : childElementCount;
    const parentChildModel = [];
    for (let count = 0; count < parentChildCount; count++) {
      parentChildModel[count] = {};
      parentChildModel[count].parent = parentElements && parentElements[count];
      parentChildModel[count].child = childElements && childElements[count];
    }
    return parentChildModel;
  }

  public localizeMessage(sModelName: string, sMessageId: string, aParams?: string[]): string {
    try {
      if (!aParams) {
        return this.getView()?.getModel(sModelName)?.getProperty(sMessageId);
      }
      return (this.getView()?.getModel(sModelName) as sap.ui.model.resource.ResourceModel)
        ?.getResourceBundle()
        ?.getText(sMessageId, aParams);
    } catch (e) {
      return sMessageId;
    }
  }

  public cardinalityFormatter(modelName, key, text) {
    const localizedText = this.getI18nText(modelName, text);
    if (key !== "") {
      return localizedText + " (" + key + ")";
    }
    return "";
  }

  public cardinalityDescFormatter(modelName, tbl1, cardinality, tbl2) {
    let cardinalityText, cardinalityDesc;
    if (cardinality === CardinalityValues.MANY) {
      cardinalityText = this.getI18nText(modelName, "txtDescMany");
    } else if (cardinality === CardinalityValues.EXACTONE) {
      cardinalityText = this.getI18nText(modelName, "txtDescExactone");
    } else if (cardinality === CardinalityValues.ONE) {
      cardinalityText = this.getI18nText(modelName, "txtDescOne");
    } else if (cardinality === CardinalityValues.NONE) {
      cardinalityText = "";
      cardinalityDesc = "";
    }
    if (cardinalityText !== "") {
      cardinalityDesc = this.getI18nText("i18n_erd", "txtCardinalityDescription", [tbl1, cardinalityText, tbl2]);
    }
    return cardinalityDesc;
  }

  public createModel(object: object): sap.galilei.ui5.GalileiModel {
    const oModel: sap.galilei.ui5.GalileiModel = new sap.galilei.ui5.GalileiModel(object);
    oModel.setProperty("/isReadOnly", true);
    oModel.setProperty("/cdsDataModel", this.getCDSDataModel(object));
    oModel.setSizeLimit(500);
    return oModel;
  }

  public createGalileiModel(galileiObject) {
    const model = this.createModel(galileiObject);

    this.getView().setModel(model, "galileiModel");
    return model;
  }

  public createUiStateModel(galileiModel) {
    const uiStateModel =
      this.getView().getModel("UiState") ||
      new sap.ui.model.json.JSONModel({
        editable: this.getEditableStates(this.getHasPrivileges(), /* isRemote*/ false, /* isView*/ false),
      });
    this.getView().setModel(uiStateModel, "UiState");
    return uiStateModel;
  }

  public ordEnabledFormatter(hasPrivileges: boolean) {
    const oDataBuilderWorkbenchController = (
      sap.ui.getCore().byId("shellMainContent---databuilderComponent---databuilderWorkbench") as sap.ui.core.mvc.View
    ).getController() as IWorkbenchController;
    const isCustomerDPTenantAndSAPSpace = oDataBuilderWorkbenchController.workbenchModel.getProperty(
      "/isCustomerDPTenantAndSAPSpace"
    );
    return hasPrivileges && !isCustomerDPTenantAndSAPSpace;
  }

  /**
   * @param forceRefresh use it to force to call formatter even though it is never used
   */
  public ordEnabledSetValuesFormatter(hasPrivileges: boolean, oModel, forceRefresh) {
    if (!this.ordEnabledFormatter(hasPrivileges)) {
      return false;
    }
    const hasDiff = oModel?.tables
      ?.toArray()
      .some((table) => isORDTable(table) && (table.connection !== oModel.connection || table.ordId !== oModel.ordId));
    return !!hasDiff;
  }

  // ORD Panel can be only displayed on model or local table
  public ordVisibleFormatter(object) {
    const isORDInERMEnabled = SupportedFeaturesService.getInstance().isORDInERMEnabled();
    const result = !!(object && isORDInERMEnabled && (isORDTable(object) || object.isModel));
    return result;
  }

  /**
   * When ORD panel expand, we load connection list if not
   * @param event
   */
  public async onOrdPanelExpand(event: sap.ui.base.Event) {
    const toExpand = event?.getParameters()?.expand;
    if (toExpand) {
      const panel = event?.getSource();
      await this.loadConnectionData(panel);
    }
  }

  public async loadConnectionData(panel) {
    const workbenchController = getWorkbenchController();
    try {
      if (!workbenchController.connectionModel) {
        panel.setBusy(true);
        let iniData = await Repo.getRemoteConnectionList(this.getSpaceName(), ["name", "typeId"], {
          typeId: ["SAPARIBA_EVENT_INGESTION", "SAPCONCUR_EVENT_INGESTION", "SAPFIELDGLASS_EVENT_INGESTION"],
        });
        iniData = iniData?.sort((a, b) => (a.name < b.name ? -1 : 1)) || [];
        const connectionData = [{ name: "", key: "" }].concat(
          iniData.map((item) => ({ name: item.name, key: item.name }))
        );
        workbenchController.connectionModel = new sap.ui.model.json.JSONModel({ data: connectionData });
      }
      this.getView().setModel(workbenchController.connectionModel, "ordConnections");
      panel.setBusy(false);
    } catch (err) {
      workbenchController.connectionModel = new sap.ui.model.json.JSONModel({ data: [{ name: err?.responseText }] });
      this.getView().setModel(workbenchController.connectionModel, "ordConnections");
      panel.setBusy(false);
    }
  }

  public setHeaderModel(object?: any) {
    const modelObject = object || {};
    const oModel: sap.galilei.ui5.GalileiModel = new sap.galilei.ui5.GalileiModel(modelObject);
    this.isEditable = modelObject.isReadOnlyObject !== true && this.getHasPrivileges();
    oModel.setProperty("/editable", this.isEditable);
    const unresolvedAssociation = modelObject.csn && modelObject.csn.type === "cds.Association";
    if (
      (modelObject &&
        modelObject.classDefinition &&
        (modelObject.classDefinition.name === "Association" ||
          modelObject.classDefinition.name === "ViewDataAccessControl" ||
          modelObject.classDefinition.name === "Element")) ||
      unresolvedAssociation
    ) {
      if (unresolvedAssociation && modelObject.source) {
        oModel.setProperty("/backText", modelObject.source.displayName);
      } else if (modelObject.qualifiedClassName === QualifiedClassNames.ASSOCIATION && modelObject.source) {
        oModel.setProperty("/backText", modelObject.source.displayName);
      } else if (modelObject.classDefinition.name === "Element") {
        oModel.setProperty("/backText", modelObject.container.displayName);
      } else {
        const viewModel: sap.galilei.ui5.GalileiModel = this.getView().getModel(
          "galileiModel"
        ) as sap.galilei.ui5.GalileiModel;
        oModel.setProperty("/backText", viewModel && viewModel.getData() && viewModel.getData().displayName);
      }
    }

    const ordPanel = this.byId("ordPanel") as sap.m.Panel;
    if (ordPanel) {
      const workbenchController = getWorkbenchController();
      if (!workbenchController.connectionModel) {
        ordPanel.setExpanded(false);
      } else if (this.getView().getModel("ordConnections") !== workbenchController.connectionModel) {
        this.getView().setModel(workbenchController.connectionModel, "ordConnections");
        ordPanel.setExpanded(false);
      }
    }

    // Prepare dependent object data
    // eslint-disable-next-line no-underscore-dangle
    this._resetDepObjPanel();
    if (!this.subscriptionsDone) {
      sap.ui
        .getCore()
        .getEventBus()
        .subscribe("DEPENDENT_OBJECT_LIST", "REFRESH", this.onRefreshDepObjListEvent.bind(this));
      sap.ui
        .getCore()
        .getEventBus()
        .subscribe("userPreferences", "change", this.onUserPreferencesChangedEvent.bind(this));
      // subscribe to 'object open' (usually triggered by validation message description -> object 'pointerdown')
      sap.ui
        .getCore()
        .getEventBus()
        .subscribe("openObject", "selectElement", this.onOpenObjectSelectElement.bind(this));
      this.subscriptionsDone = true;
    }
    // this.prepareDependentObjectListData(modelObject, oModel, false); // only load on demand, see DW101-802
    this.modelObjectForDependentObjects = modelObject; // ToDo, try to get rid of property modelObjectForDependentObjects

    this.getView().setModel(oModel, "header");
    return oModel;
  }

  public propertiesPanelHeaderFormatter(listItems) {
    const resourceBundle = this.oResourceModel.getResourceBundle();
    let tableHeaderText;
    if (listItems?.length) {
      tableHeaderText = resourceBundle.getText("@COLUMNS_LABEL", [listItems.length.toString()]);
    } else {
      tableHeaderText = resourceBundle.getText("@COLUMNS_LABEL", ["0"]);
    }
    return tableHeaderText;
  }

  public async prepareDependentObjectListData(
    modelObject: any,
    oModel: any,
    forceRefresh?: boolean,
    isRefresh?: boolean
  ): Promise<any> {
    if (
      (modelObject && modelObject.classDefinition && modelObject.classDefinition.name === "Output") ||
      (modelObject && modelObject.classDefinition && modelObject.classDefinition.name === "Entity") ||
      (modelObject && modelObject.classDefinition && modelObject.classDefinition.name === "Model") || // ER modeler
      (modelObject && modelObject.classDefinition && modelObject.classDefinition.name === "View") || // ER modeler
      (modelObject && modelObject.classDefinition && modelObject.classDefinition.name === "Table")
    ) {
      return new Promise<any>((resolve, reject) => {
        const dependentObjectPanel = this.getView().byId("dependentObjectPanel") as sap.m.Panel;
        const dependentObjectSection = this.getView().byId("dependentObjectSection") as sap.uxap.ObjectPageSection;
        const dependentObjectView = dependentObjectSection || dependentObjectPanel;
        if (dependentObjectView) {
          if (!oModel.getProperty("/dependentObjects/items") || forceRefresh) {
            const editorId = this.getEditorComponent()?.getId();
            if (editorId !== Editor.TABLEEDITOR) {
              dependentObjectView.setBusy(true);
            }
            this.getEditorComponent()
              ?.getDependentObjectListData(modelObject, 2)
              .then((dependencies) => {
                if (editorId !== Editor.TABLEEDITOR || (isRefresh && editorId === Editor.TABLEEDITOR)) {
                  oModel.setProperty("/dependentObjects", dependencies);
                }
                // eslint-disable-next-line no-underscore-dangle
                this._resetSearchDepObj();
                // eslint-disable-next-line no-underscore-dangle
                this._resetFilterDepObj();
                // eslint-disable-next-line no-underscore-dangle
                this._resetSortDepObj();
                dependentObjectView.setVisible(true);
                if (editorId !== Editor.TABLEEDITOR) {
                  dependentObjectView.setBusy(false);
                }
                resolve(dependencies);
              });
          }
        }
      });
    }
  }

  public setObjectModel(object: object): void {
    const oModel: sap.galilei.ui5.GalileiModel = this.createModel(object);
    this.hideColumns(oModel, {
      newName: true,
      isRemoved: true,
    });
    this.getView().setModel(oModel);
    this.setHeaderModel(object);

    const galileiModel = this.createGalileiModel(object);
    this.createUiStateModel(galileiModel);
  }

  public areAllTrue(...args: any) {
    return areAllTrue(...args);
  }

  public isEmpty(args: any) {
    return isEmpty(args);
  }

  public isFilled(args: any) {
    return isFilled(args);
  }

  public hideColumns(model: sap.galilei.ui5.GalileiModel, oColumnsFilter): void {
    model.setProperty("/columnsFilter", oColumnsFilter);
  }

  // This function will be overridden in sap.ui.define
  public defaultOnSortTable(event: sap.ui.base.Event): any {
    //
  }

  // On change of Semantic Usage
  public onChangeType(event: IEvent<sap.m.Select, { selectedItem: sap.ui.core.Item }>): void {
    //
  }

  public onCloseSuccessorSelect() {
    this["stateControl"]?.afterSelectSuccessor();
  }
  public onSuccessorSelected(evt) {
    const successor = evt?.getParameter("selection")?.[0]?.technicalName;
    if (successor) {
      this["stateControl"]?.afterSelectSuccessor(successor);
      const view = this.getGalileiModel()?.getData();
      if (view) {
        view.successorObject = successor;
      }
    }
  }
  public selectSuccessor() {
    // Get current view name
    const view = this.getGalileiModel()?.getData();
    const currentViewTechnicalName = view?.technicalName || view?.name;
    // Open Select dialog, limited to "DWC_VIEW"s with following additional filters:
    // - Exclude current view
    // - Only Released views (ES: release_state === RELEASED)
    sap.ui.require(
      ["sap/esh/search/ui/sinaNexTS/sina/SimpleCondition", "sap/esh/search/ui/sinaNexTS/sina/ComparisonOperator"],
      (SimpleConditionModule, ComparisonOperatorModule) => {
        openExplorerSelectorDialogExt({
          styleClass: "translationDashboardSearchContainer",
          spaceName: this.getSpaceName(),
          title: this.getText("selectSuccessor"),
          selectButtonText: this.getText("@addObjectBtn"),
          onSelect: this.onSuccessorSelected.bind(this),
          onClose: this.onCloseSuccessorSelect.bind(this),
          displayBasket: false,
          getSupportedTechnicalTypes: async () => ["DWC_VIEW"],
          enableMultiSelection: false,
          enableFolder: true,
          configure: (configuration) => {
            configuration.searchOnStart = true;
            configuration.resultViewTypes = ["searchResultTable"];
            const querySuffix = configuration.sinaConfiguration.querySuffix;

            const SimpleCondition = SimpleConditionModule.SimpleCondition;
            const ComparisonOperator = ComparisonOperatorModule.ComparisonOperator;

            // Exclude current View
            querySuffix.addCondition(
              new SimpleCondition({
                operator: ComparisonOperator.Ne,
                attribute: "name",
                value: currentViewTechnicalName,
              })
            );

            // RELEASE condition
            querySuffix.addCondition(
              new SimpleCondition({
                operator: ComparisonOperator.Eq,
                attribute: "release_state",
                value: "RELEASED",
              })
            );
          },
          customRowEnablement: (item: any) => {
            const attribute = item.attributesMap.technical_type;
            return attribute?.value === "DWC_VIEW";
          },
        });
      }
    );
  }
  // On successor select
  public onSuccessorSelect(event): void {
    this["stateControl"] = event?.mParameters?.stateControl;
    if (
      this["stateControl"] &&
      this["stateControl"].getProperty("isSuccesssorEnabled") === true &&
      this["stateControl"].getReadOnly() !== true
    ) {
      this["stateControl"].beforeSelectSuccessor();
      this.selectSuccessor();
    }
  }

  public onToggleFullScreen(event: any) {
    sap.ui.getCore().getEventBus().publish("propertyPanel", "toggleFullScreen", {
      // put "normal" width here if it is not the default
    });
  }

  public onSortTable(event: sap.ui.base.Event): object {
    // Record usage
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: UsageActions.SORT_COLUMNS,
      feature: DWCFeature.DATA_BUILDER,
      eventtype: EventType.CLICK,
    });

    return this.defaultOnSortTable(event);
  }

  public setupParentChildHierarchyUI() {
    const model = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const featureFlagsModel = sap.ui.getCore().getModel("featureflags");

    const isHierarchyModel = model.getProperty("/isHierarchy");
    this.plugOrUnplugUIFragment(
      "pch",
      require("./ParentChildHierarchy.fragment.xml"),
      "entityPropertiesForm",
      isHierarchyModel
    );
  }

  public setupHierarchyWithDirectoryUI() {
    const model = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const isHierarchyWithDirectory = model.getProperty("/isHierarchyWithDirectory");
    const container = model.getData()?.resource?.model;
    const isTableEditor = container?.table === model.getData();
    this.plugOrUnplugUIFragment(
      "hwd",
      require("./HierarchyWithDirectory.fragment.xml"),
      isTableEditor ? "entityPropertiesFormTable" : "entityPropertiesForm",
      isHierarchyWithDirectory
    );
  }

  public async setStorageTypeUI() {
    const model = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const space = super.getSpaceName();
    return (await getSpaceCSN({ query: { space, spaceDefinitionUiProperties: true } })).data;
  }

  onOpenHierarchyWithDirectorySettings(event) {
    const model = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const entity = model.getData();
    openHierarchyWithDirectoryDialog(entity);
  }

  public getHWDValidations(validations: any[]): any {
    const msgIds = getHWDMessageIds();
    return validations?.filter((v) => msgIds?.includes(v?.message?.id));
  }

  public formatterHWDValidationsCount(validations: any[], model?: any): number {
    return this.getHWDValidations(validations)?.length || 0;
  }

  public formatterHWDValidationsStatusType(validations: any): sap.m.ButtonType {
    const databuilderWbcontroller = this.getDatabuilderWorkbench();
    const validationButton = this.byId("hwd--validationPopoverButton") as sap.m.Button;
    return databuilderWbcontroller
      .getActiveEditor()
      .getValidationStatusType(this.getHWDValidations(validations), validationButton);
  }

  public onHWDValidationPopoverButtonPress(event: IEvent<sap.m.Button, {}>) {
    // const databuilderWbcontroller = this.getDatabuilderWorkbench();
    const msgIds = getHWDMessageIds();
    openDatabuilderValidationsPopoverBy(this.getGalileiModel().getData(), event.getSource(), undefined, msgIds);
  }

  public setupCompoundKeyUI() {
    const model = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const featureFlagsModel = sap.ui.getCore().getModel("featureflags");
    const isHierarchyModel = model.getProperty("/isHierarchy");
    this.plugOrUnplugUIFragment("ck", require("./CompoundKeys.fragment.xml"), "entityPropertiesForm", isHierarchyModel);
  }

  private plugOrUnplugUIFragment(id, fragmentId, containerId, condition, position = 0, tryCount = 0) {
    const controlId = this.createId(id);
    const container = this.byId(containerId) as sap.m.Page;
    if (!container && tryCount < 5) {
      // UI Not ready - wait
      setTimeout(() => {
        this.plugOrUnplugUIFragment(id, fragmentId, containerId, condition, position, tryCount + 1);
      }, 500);
    } else if (!container) {
      // something wrong
      if (console.error) {
        console.error("Cannot find side panel UI to plug fragment - skipped!");
      }
      return;
    }
    container?.setBusyIndicatorDelay(0);
    if (condition && container) {
      // plug UI
      if (container) {
        container.setBusy(true);
        setTimeout(() => {
          try {
            if (!sap.ui.getCore().byId(this.fragmentIdToControlId[controlId])) {
              const fragment = sap.ui.xmlfragment(controlId, fragmentId, this);
              this.fragmentIdToControlId[controlId] = fragment.getId();
              let index: number = container.getContent()?.findIndex((o) => o?.getId()?.endsWith("semanticUsageVbox"));
              const isTableEditor = containerId === "entityPropertiesFormTable";
              if (isTableEditor) {
                index = container.getContent()?.findIndex((o) => o?.getId()?.endsWith("vBoxDataSetType"));
                // Change the width of the fragment to 50% in table editor
                fragment.setWidth("50%");
              }
              index = (index === undefined ? 0 : index) + 1 + (isTableEditor ? 1 : 0);
              container.insertContent(fragment, index || position);
            }
          } finally {
            container.setBusy(false);
          }
        }, 500);
      } else {
        container.setBusy(false);
      }
      // } else if (container) {
      // "undo" does not trigger onChangeType, so do not destroy the control once created, just rely on its "visible" data binding
      // container.setBusy(true);
      // // unplug UI
      // const control = sap.ui.getCore().byId(this.fragmentIdToControlId[controlId]);
      // if (control) {
      //   setTimeout(() => {
      //     try {
      //       control.destroy();
      //       delete this.fragmentIdToControlId[controlId];
      //     } finally {
      //       container.setBusy(false);
      //     }
      //   }, 500);
      // } else {
      //   container.setBusy(false);
      // }
    }
  }

  public onElementSelectionChange(event, model?: sap.galilei.ui5.GalileiModel) {
    const oModel = model || this.getView().getModel();
    const selectedItems = event.getParameter("items");
    const selectedElements =
      selectedItems && selectedItems.map((item) => oModel.getProperty(item.getBindingContextPath()));
    this.highlightSymbolByNodeElement(selectedElements, model);
  }

  public onElementSelect(event: sap.ui.base.Event, model?: sap.galilei.ui5.GalileiModel): void {
    const oModel = model || this.getView().getModel();
    const source = event.getSource() as any;
    const oSelectedItem = source?.getSelectedItems();
    let oSelectedElement;
    if (Array.isArray(oSelectedItem)) {
      oSelectedElement = oSelectedItem.map((item) => oModel.getProperty(item.getBindingContextPath()));
    } else {
      oSelectedElement = [oModel.getProperty(oSelectedItem.getBindingContextPath())];
    }
    this.highlightSymbolByNodeElement(oSelectedElement, model);
    if (isModelingParameterLineageEnabled()) {
      // deselect all parameters
      const parametersList = this.getView().byId("parametersList") as sap.m.List;
      parametersList?.removeSelections();
    }
  }

  public highlightSymbolByNodeElement(
    oSelectedElements: sap.cdw.querybuilder.Element[],
    model?: sap.galilei.ui5.GalileiModel
  ): void {
    if (oSelectedElements.length > 0) {
      const oModel = model || this.getView().getModel();
      const oNode = this.getCurrentNode instanceof Function ? this.getCurrentNode() : oModel.getProperty("/");
      const highlighterResult = ImpactAndLineageHighlighter.getInstance().highlightImpactAndLineage({
        elements: oSelectedElements,
      });
      highlighterResult.nodesToHighlight.then((nodes) => {
        if (oNode.classDefinition.name === "Join") {
          // in case of join then we need to add input itself
          nodes.push(oSelectedElements[0].container);
        } else if (oNode.classDefinition.name === "Union" && oSelectedElements[0].container !== oNode) {
          // in case of union then we need to add input itself
          nodes.push(oSelectedElements[0].container);
        }
        sap.cdw.querybuilder.DiagramImpl.highlightNodes(nodes, oNode, /* bIncludeSuccessorLinks */ true);
      });
    } else {
      sap.cdw.querybuilder.DiagramImpl.highlightNodes();
    }
  }

  public onParameterSelectionChange(event: sap.ui.base.Event): void {
    if (!isModelingParameterLineageEnabled()) {
      return;
    }
    let keepSelection;
    if (this.getEditorComponent()?.getId() === Editor.VIEWBUILDER) {
      // if needed, add more editors to highlight source-node belonging to selected parameter (output)
      const oModel = (this.getView().getModel("header") ?? this.getView().getModel()) as sap.galilei.ui5.GalileiModel;
      const oNode = this.getCurrentNode instanceof Function ? this.getCurrentNode() : oModel.getProperty("/");
      if (oNode.classDefinition.name === "Output") {
        // for Output node, highlight all source nodes with a mapped output parameter
        const selectedItems = (event.getSource() as sap.m.List).getSelectedItems();
        const selectedParameters =
          selectedItems && selectedItems.map((item) => oModel.getProperty(item["getBindingContextPath"]()));
        this.highlightSymbolByNodeParameter(selectedParameters, oModel);
        keepSelection = true;
      }
    }
    if (!keepSelection) {
      // deselect all selected parameters
      (event.getSource() as sap.m.List)?.removeSelections();
    }
    // deselect all columns
    const attributeList = this.getView().byId("entityBusinessPropertiesElementsTokenList") as sap.m.List;
    attributeList?.removeSelections();
  }

  public highlightSymbolByNodeParameter(
    oSelectedParameters: sap.cdw.commonmodel.Parameter[],
    model?: sap.galilei.ui5.GalileiModel
  ): void {
    if (oSelectedParameters?.length > 0) {
      // const oModel = model || this.getView().getModel();
      // const oNode = this.getCurrentNode instanceof Function ? this.getCurrentNode() : oModel.getProperty("/");
      const refEntityNodesToHighlight = oSelectedParameters
        .filter((param: any) => typeof param.sourceParameter !== "undefined")
        .map((refParam: any) => refParam.sourceParameter.container);
      const allNodesToHighlight = refEntityNodesToHighlight;
      // usage in expressions (filter, calculated column, aggregation, script
      for (const parameter of oSelectedParameters) {
        (parameter.rootContainer as any).nodes.forEach((node) => {
          // check if node already in the highlighting-list
          if (!allNodesToHighlight.find((highlightingNode) => highlightingNode.objectId === node.objectId)) {
            // check expressions/scripts for parameter usage
            if (node.classDefinition.name === "Filter") {
              const foundParams = getListOfConsumedInputParametersInFilter(node, [parameter]);
              if (foundParams?.length > 0) {
                allNodesToHighlight.push(node);
              }
            } else if (node.classDefinition.name === "CalculatedElements") {
              const foundParams = getListOfConsumedInputParametersInCC(node, [parameter]);
              if (foundParams?.length > 0) {
                allNodesToHighlight.push(node);
              }
            } else if (node.classDefinition.name === "AggregatedElements") {
              const foundParams = getListOfConsumedInputParametersInAggr(node, [parameter]);
              if (foundParams?.length > 0) {
                allNodesToHighlight.push(node);
              }
            } else if (node.classDefinition.name === "Entity") {
              // SQL view script
              // we need to analyze the mapping !!!
              const foundParams = node?.parameters;
              if (foundParams?.length > 0) {
                const mappedParameters = foundParams.filter((param) => param?.targetParameter?.name === parameter.name);
                if (mappedParameters?.length > 0) {
                  allNodesToHighlight.push(node);
                }
              }
            }
          }
        });
      }
      // highlight the nodes
      if (allNodesToHighlight.length > 0) {
        sap.cdw.querybuilder.DiagramImpl.highlightNodes(
          allNodesToHighlight,
          /* oNodeToExclude */ undefined,
          /* bIncludeSuccessorLinks */ false
        );
        return;
      }
    }
    // remove highlighting (all nodes)
    sap.cdw.querybuilder.DiagramImpl.highlightNodes();
  }

  public newNameIsVisible(oColumnsFilter, selectedCount): boolean {
    return (!oColumnsFilter || !oColumnsFilter.newName) && (!selectedCount || selectedCount === 1);
  }

  public renameIsVisible(oColumnsFilter, selectedCount): boolean {
    return this.newNameIsVisible(oColumnsFilter, selectedCount);
  }

  public renameIsEnabled(oColumnsFilter, selectedCount, isEditable): boolean {
    return isEditable && (!oColumnsFilter || !oColumnsFilter.newName) && (!selectedCount || selectedCount === 1);
  }

  public columnIsModified(isRemoved, name, newName): boolean {
    if (isRemoved || (newName && newName !== name)) {
      return true;
    }
    return false;
  }

  public removeIsVisible(oColumnsFilter): boolean {
    return !oColumnsFilter || !oColumnsFilter.isRemoved;
  }

  public elementNameFormatter(oElement): string {
    return oElement && oElement.newName;
  }

  public elementIsNotRemoved(bIsRemoved): boolean {
    return bIsRemoved !== undefined && bIsRemoved !== true;
  }

  public elementValidationStatusFormatter(oElement) {
    if (oElement && oElement.validationStatus) {
      return true;
    }
    return false;
  }

  public objectNameFormatter(oObject, sDisplayName): string {
    // method needs refactoring:
    // logic should be shifted to galilei model so that we do not need the formatter anymore
    if (
      oObject &&
      oObject.classDefinition &&
      (oObject.classDefinition.name === "Output" ||
        oObject.classDefinition.name === "Table" ||
        oObject.classDefinition.name === "View" ||
        oObject.classDefinition.name === "Entity" ||
        oObject.classDefinition.name === "Element" ||
        oObject.classDefinition.name === "ViewDataAccessControl")
    ) {
      return sDisplayName;
    } else if (oObject && oObject.classDefinition && oObject.classDefinition.name === "Association") {
      // special for associations
      if (oObject.displayName) {
        return oObject.displayName;
      } else if (oObject.source && oObject.target) {
        const computedDisplayName = this.getI18nText("i18n", "@associationTo", [
          oObject.source.displayName,
          oObject.target.displayName,
        ]);
        oObject.displayName = computedDisplayName;
        return computedDisplayName;
      }
    }
    return oObject ? oObject.name : "";
  }

  public classNameFormatter(oObject, sClassName): string {
    if (oObject && oObject.isRemote) {
      return "RemoteTable";
    } else if (oObject && (oObject.isView || sap.cdw.commonmodel.ObjectImpl.isView(oObject))) {
      return "View";
    } else if (sClassName === "Element") {
      return oObject.isAnalyticMeasure ? "AnalyticMeasure" : oObject.container?.classDefinition?.name;
    } else {
      return sClassName;
    }
  }

  public onOpenObjectSelectElement(channelId: string, eventId, data) {
    // let actionButtonId;
    if (data.object) {
      if (
        data.object.classDefinition.name === "ViewDataAccessControl" &&
        data.object.container.classDefinition.name === "Output"
      ) {
        const dacId = document.querySelectorAll("[id$=viewDataAccessControlsList]")[0].id; // automatic Id generation of view hierarchoes seems to be broken, get the Id the hard way
        const table: sap.m.Table = this.byId(dacId) as sap.m.Table;
        if (table) {
          table.focus(); // make sure, no item has the focus
          /* if (table.getMode() !== sap.m.ListMode.None && table.getMode() !== sap.m.ListMode.Delete) {
            table.removeSelections();  // deselect all items
          } */
          for (const item of table.getItems()) {
            // find the menu (action) button
            if (item.getBindingContext("galileiModel").getObject().objectId === data.object.objectId) {
              const actionButtons = item["getActionButtons"]();
              if (actionButtons?.length > 0) {
                actionButtons[0].firePress();
              }
              // for (const actionButton of item.getActionButtons()) {
              // if (actionButton.getId().includes("editButton")) {  // generic Id, not really optimal
              // actionButton.firePress();
              // break; // press first button (workaround, because button has a generic Id)
              // }
              // }
            }
          }
        }
      }
    }
  }

  // Data Access Control methods
  public viewDataAccessControlFactory(id, context) {
    const tokenListItem = new TokenListItem({
      icon: "sap-icon://permission",
      title: "{galileiModel>displayName}",
      showDescription: false,
      hideActionButtonsOnMouseOut: true,
      actionButtons: this.getDataAccessControlsTokenListItemActionButtons(id),
    });
    tokenListItem.addCustomData(
      new sap.ui.core.CustomData({
        key: "cy",
        value: `viewDataAccessControlListItem-${context.oModel.getProperty(context.sPath).displayName}`,
        writeToDom: true,
      })
    );
    return tokenListItem;
  }

  private getDataAccessControlsTokenListItemActionButtons(itemId: string): sap.m.Button[] {
    const dacPrivileges = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DAC");
    if (!dacPrivileges.read) {
      return [];
    }

    const editButton = new sap.m.Button({
      id: `${itemId}--editMappings`,
      icon: "sap-icon://slim-arrow-right",
      tooltip: "{i18n>@editMappings}",
      type: sap.m.ButtonType.Transparent,
    });
    editButton.attachPress(this.onDACLEdit.bind(this));

    const infoButton = new sap.m.Button({
      icon: "sap-icon://sac/info",
      type: sap.m.ButtonType.Transparent,
    });
    infoButton.attachPress(this.openDacInfoPopover.bind(this));

    return [infoButton, editButton];
  }

  public onViewDataAccessControlSelect(event: sap.ui.base.Event) {
    const oModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const oSelectedItem = event.getParameter("listItem");
    const list = oSelectedItem.getList();
    oModel.setProperty("/viewDataAccessControlSelectedCount", list.getSelectedItems().length);
  }

  public onCancelDataAccessControl() {
    const dialog = sap.ui.getCore().byId("dataAccessControlDialog") as sap.m.Dialog;
    if (dialog) {
      dialog.close();
    }
  }

  public processSelectedDataAccessControl(rootModel, object) {
    let oVDAC;
    let oView = rootModel?.getData();
    const oResource = oView?.resource;
    const oModel = oResource && oResource.model;
    const oOutput = oModel && oModel.output;
    const dacName = object && object.name;
    const dacCSN = object.csn && object.csn.definitions && object.csn.definitions[dacName];
    oView = oOutput || oView;
    if (dacName && dacCSN && oView) {
      oResource.applyUndoableAction(() => {
        oVDAC = findOrCreateAndAttachDACFromCSN(oView, dacName, dacCSN);
      });
    }
    return oVDAC;
  }

  public onCreateViewDataAccessControl(oEvent) {
    const table: sap.m.Table = sap.ui.getCore().byId("targetDataAccessControl") as sap.m.Table;
    const selectedIndex = table["getSelectedIndex"]();
    let aContexts;
    if (selectedIndex !== -1) {
      aContexts = table["getContextByIndex"](selectedIndex);
    }
    let oSelObject;
    if (aContexts) {
      oSelObject = aContexts.getObject().object;
    }
    const rootModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const oVDAC = this.processSelectedDataAccessControl(rootModel, oSelObject);

    rootModel.setProperty("/daclSelected", true);
    rootModel.setProperty("/selectedDacl", oVDAC);
    oEvent.getSource().getParent().destroy();
    this.switchPage(Pages.DACLPage);
    this.setHeaderModel(oVDAC);
    this.setDACLModel(oVDAC);
    // Record usage
    const activeEditor = this.getView().getModel("workbenchEnv").getProperty("/activeEditor");
    ShellContainer.get()
      .getUsageCollectionService()
      .recordAction({
        action: "attachdac",
        feature: DWCFeature.DATA_BUILDER,
        eventtype: EventType.CLICK,
        options: [
          {
            param: "target",
            value: activeEditor,
          },
        ],
      });
  }

  public async updateSelectedViewDataAccessControl(oSelectedElement) {
    // Check if selected data access control is already loaded
    if (oSelectedElement && oSelectedElement.dataAccessControl === undefined) {
      const spaceName = super.getSpaceName();
      const dacName = oSelectedElement && oSelectedElement.name;
      const oResource = oSelectedElement && oSelectedElement.resource;
      if (oResource && spaceName && dacName) {
        const dacCsn = await Repo.getDataAccessControlDetails(spaceName, dacName, ["csn"]);
        const dacCsnDefinition = dacCsn && dacCsn.csn && dacCsn.csn.definitions && dacCsn.csn.definitions[dacName];
        const updatingDataAccessControl = this.getI18nText("i18n", "@updatingDataAccessControl");
        oResource.applyUndoableAction(
          function () {
            updateViewFromDataAccessControlCSN(oSelectedElement, dacCsnDefinition);
          },
          updatingDataAccessControl,
          true
        ); // Protect from Undo!
      }
    }
  }

  public async getDataAccessControls() {
    const spaceName = super.getSpaceName();
    if (spaceName) {
      const objectList = await Repo.getDataAccessControlList(spaceName, [
        "name",
        "kind",
        "creation_date",
        "owner",
        "csn",
        "@EndUserText.label",
      ]);
      const list = objectList.filter((obj) => obj.kind === "sap.dwc.dac");
      const objects = list.map((e) => {
        const { type, icon } = getTypeAndIcon(e);
        return {
          label: e["@EndUserText.label"] || e.name,
          technicalName: e.name,
          type: getDataCategoryName(type),
          icon,
          createdBy: e.creator,
          object: e,
          kind: e.kind,
        };
      });
      return objects;
    } else {
      return [];
    }
  }

  public async onPressCreateViewDataAccessControl() {
    const isHarmonizationObjectSelectionFF = isHarmonizationObjectSelection();
    if (isHarmonizationObjectSelectionFF) {
      await this.openDACObjectSelectionDialog();
    } else {
      let dialog = sap.ui.getCore().byId("dataAccessControlDialog") as sap.m.Dialog;
      if (!dialog) {
        const dacNS = require("../view/SelectDataAccessControl.fragment.xml");
        dialog = sap.ui.xmlfragment(dacNS, this) as sap.m.Dialog;
        dialog.setEscapeHandler(this.onCancelDataAccessControl.bind(this));
      }
      this.getView().addDependent(dialog);
      const i18nMod = this.getView().getModel("i18n");
      const oModel = new sap.ui.model.json.JSONModel({
        objects: {},
        loadingText: "Loading Data Access Controls",
        targetType: "All",
      });
      dialog.setModel(oModel, "oModel");
      dialog.setModel(i18nMod, "i18n");
      dialog.getModel("oModel").setProperty("/isDacOkEnabled", false);
      dialog.open();
      dialog.setBusy(true);
      this.getDataAccessControls().then(function (objects) {
        (dialog.getModel("oModel") as sap.ui.model.json.JSONModel).getData().objects = objects;
        (dialog.getModel("oModel") as sap.ui.model.json.JSONModel).updateBindings(true);
        dialog.setBusy(false);
      });
    }
  }

  public async openDACObjectSelectionDialog() {
    const self = this;
    const oFeatures = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();
    this["objectDialog"] = await openExplorerSelectorDialogExt({
      styleClass: "repository-package-editor-object-selection",
      title: this.localizeMessage("i18n_erd", "@selectDataAccessControl"),
      spaceName: this.spaceName,
      includeSharedObjects: true,
      onSelect: this.onObjectSelectDAC.bind(this),
      customRowEnablement: self.customRowEnablement.bind(self),
      enableMultiSelection: false,
      displayBasket: false,
      enableFolder: true,
      selectButtonText: this.localizeMessage("i18n_erd", "@addObjectBtn"),
      getSupportedTechnicalTypes: async () => ["DWC_DAC"],
      configure: (configuration) => {
        configuration.searchOnStart = true;
        configuration.resultViewTypes = ["searchResultTable"];
      },
      onClose: () => {
        self.byId("splitApp")?.setBusy(false);
      },
    });
  }

  public onObjectSelectDAC(evt) {
    const selectedObjArr = evt.getParameter("selection");
    if (selectedObjArr.length > 0) {
      const selectedObj = selectedObjArr[0];
      if (selectedObj?.isFolder === true) {
        return;
      }
      Repo.getDataAccessControlDetails(this.spaceName, selectedObj.technicalName, [
        "name",
        "kind",
        "creation_date",
        "owner",
        "csn",
        "@EndUserText.label",
      ]).then((e) => {
        selectedObj.object = e;
        const rootModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
        const oVDAC = this.processSelectedDataAccessControl(rootModel, selectedObj.object);

        rootModel.setProperty("/daclSelected", true);
        rootModel.setProperty("/selectedDacl", oVDAC);
        this.switchPage(Pages.DACLPage);
        this.setHeaderModel(oVDAC);
        this.setDACLModel(oVDAC);
        // Record usage
        const activeEditor = this.getView().getModel("workbenchEnv").getProperty("/activeEditor");
        ShellContainer.get()
          .getUsageCollectionService()
          .recordAction({
            action: "attachdac",
            feature: DWCFeature.DATA_BUILDER,
            eventtype: EventType.CLICK,
            options: [
              {
                param: "target",
                value: activeEditor,
              },
            ],
          });
      });
    }
  }

  public onSelectDataAccessControl(event: sap.ui.base.Event) {
    const dacTable = event.getSource() as sap.ui.table.Table;
    const rowSelectedIndex = dacTable.getSelectedIndices();
    const dialog = sap.ui.getCore().byId("dataAccessControlDialog") as sap.m.Dialog;
    (dialog.getModel("oModel") as sap.ui.model.json.JSONModel).setProperty(
      "/isDacOkEnabled",
      rowSelectedIndex.length === 1
    );
  }

  public onPressDeleteViewDataAccessControl() {
    const model: sap.galilei.ui5.GalileiModel = this.getView().getModel("galileiModel") as sap.galilei.ui5.GalileiModel;
    const oOutputNode = model.getProperty("/");
    const table: sap.m.Table = this.byId("viewDataAccessControlsList") as sap.m.Table;
    const oSelectedItems = table.getSelectedItems();
    const oResource = oOutputNode.resource;
    oResource.applyUndoableAction(function () {
      for (let columnIndex = oSelectedItems.length - 1; columnIndex >= 0; columnIndex--) {
        const oSelectedItem = oSelectedItems[columnIndex];
        const bindingPath: string = oSelectedItem && oSelectedItem.getBindingContext("galileiModel").getPath();
        const oSelectedElement = bindingPath && model.getProperty(bindingPath);
        if (oSelectedElement.classDefinition && oSelectedElement.classDefinition.name === "ViewDataAccessControl") {
          oSelectedElement.deleteObject();
        }
      }
    });

    oOutputNode.container.validate();
    model.refresh(true);
    // Record usage
    const activeEditor = this.getView().getModel("workbenchEnv").getProperty("/activeEditor");
    ShellContainer.get()
      .getUsageCollectionService()
      .recordAction({
        action: "detachdac",
        feature: DWCFeature.DATA_BUILDER,
        eventtype: EventType.CLICK,
        options: [
          {
            param: "target",
            value: activeEditor,
          },
        ],
      });
  }

  // begin of --- Dependent objects
  /**
   * shared spaces info, based on 'shared_with_space_name' (array)
   */
  public async sharedWithSpaceIconFormatter(spaces) {
    if (spaces?.length > 0) {
      return "sap-icon://share-2";
    } else {
      return "sap-icon://none";
    }
  }
  /**
   * shared spaces info, based on 'shared_with_space_name' (array)
   */
  public async sharedWithSpaceTooltipFormatter(spaces) {
    if (spaces?.length > 0) {
      return spaces.join(",");
    } else {
      return "";
    }
  }
  /**
   * dependency link text, if it is cross space, add space name.
   */
  public dependencyLinkTextFormatter(item) {
    let text = item.displayName;
    if (item.isCrossSpace) {
      text = getLinkText(item);
    }
    return text;
  }
  /**
   * dependency type text ('Source', 'Association')
   */
  public async dependencyTypeTextFormatter(dependencyType, wrapWithBrackets, dependentObjectType) {
    const isHierarchy = this.getView().getModel("header")?.getProperty("/isHierarchy");
    return dependencyTypeTextFormatter(dependencyType, wrapWithBrackets, dependentObjectType, isHierarchy);
  }
  /**
   * Object status icon, based on '#objectType'
   */
  public async objectStatusIconFormatter(objectStatus, isNew: false) {
    return objectStatusIconFormatter(objectStatus, isNew);
  }
  /**
   * Object status icon color, based on '#objectType'
   */
  public async objectStatusIconColorFormatter(objectStatus, isNew: false) {
    return Format.objectStatusIconColorFormatter(objectStatus, isNew);
  }
  /**
   * Object status text, based on '#objectType'
   */
  public async objectStatusTextFormatter(objectStatus, isNew: false) {
    return objectStatusTextFormatter(objectStatus, isNew);
  }
  /**
   * Object status tooltip, based on '#objectType'
   */
  public async objectStatusTooltipFormatter(objectStatus, isNew: false) {
    return objectStatusTooltipFormatter(objectStatus, isNew);
  }
  /**
   * Object type icon, based on '#isViewEntity' and '@DataWarehouse.remote.connection'
   */
  public async objectDataCategoryIconFormatter(entity) {
    return dataCategoryIconFormatter(entity);
  }
  /**
   * Object type tooltip, based on 'kind' and 'type'
   */
  public async objectDataCategoryTooltipFormatter(entity) {
    let tooltip = "";
    if (entity.kind === "sap.dwc.businessEntityVariant" || entity.technical_type === "DWC_PERSPECTIVE") {
      // Business Builder, perspective
      tooltip = this.localizeMessage("i18n_erd", "fileTypePerspective");
    } else {
      const fileType = getFileType(entity);
      const analyticsType = getAnalyticsType(entity);
      tooltip = getFullTypeNameFromTypes(fileType, analyticsType);
    }
    return tooltip;
  }
  /**
   * Filter the dependent objects list
   */
  public async onFilterButtonSelectionChange() {
    // eslint-disable-next-line no-underscore-dangle
    this._updateTableFilterDepObj();
  }
  /**
   * Search the dependent objects list
   */
  public async onSearchDepObj() {
    // eslint-disable-next-line no-underscore-dangle
    this._updateTableFilterDepObj();
  }
  /**
   * Get the next sort icon
   *   sort options:
   *     1: Ascending (displayName) --- default
   *     2: Descending by severity (objectStatus)
   *     3: Descending (displayName)
   *   return:
   *     1->2, 2->3, 3->1
   */
  public getSortButtonDataDepObj(mode: "current" | "next" | "default") {
    let oResult = SORT_DEP_OBJ_BUTTON_DATA.ascending; // default
    if ((this.byId("sortListButton") as sap.m.Button).getIcon() === SORT_DEP_OBJ_BUTTON_DATA.ascending.icon) {
      oResult = mode === "next" ? SORT_DEP_OBJ_BUTTON_DATA.descending : SORT_DEP_OBJ_BUTTON_DATA.ascending;
    } else if ((this.byId("sortListButton") as sap.m.Button).getIcon() === SORT_DEP_OBJ_BUTTON_DATA.descending.icon) {
      oResult = mode === "next" ? SORT_DEP_OBJ_BUTTON_DATA.highestSeverityOnTop : SORT_DEP_OBJ_BUTTON_DATA.descending;
    } else if (
      (this.byId("sortListButton") as sap.m.Button).getIcon() === SORT_DEP_OBJ_BUTTON_DATA.highestSeverityOnTop.icon
    ) {
      oResult = mode === "next" ? SORT_DEP_OBJ_BUTTON_DATA.ascending : SORT_DEP_OBJ_BUTTON_DATA.highestSeverityOnTop;
    }
    return oResult;
  }
  /**
   * Sort the dependent objects list
   */
  public async onNextSortDepObj() {
    const sortButtonData = this.getSortButtonDataDepObj("next");
    (this.byId("sortListButton") as sap.m.Button).setIcon(sortButtonData.icon);
    (this.byId("sortListButton") as sap.m.Button).setTooltip(
      this.localizeMessage("i18n_erd", sortButtonData.tooltipResourceId)
    );
    // eslint-disable-next-line no-underscore-dangle
    this._updateTableSorterDepObj();
  }
  /**
   * Update display name (technical name <-> business name)
   */
  public onUserPreferencesChangedEvent(_channel, _eventName, oPreferenceChanges) {
    if (!oPreferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY) {
      return;
    }
    const oModel = this.getView().getModel("header");
    if (oModel && oModel?.getProperty("/dependentObjects/items")) {
      const depObjListItems = oModel.getProperty("/dependentObjects/items");
      for (const item of depObjListItems) {
        if (oPreferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY.newValue === ObjectNameDisplay.technicalName) {
          item.displayName = item.name;
        } else {
          item.displayName = item.label || item.name;
        }
      }
      oModel.refresh(true);
    }
  }
  /**
   * Refresh the dependent objects list (event)
   */
  public onRefreshDepObjListEvent(_channel, _eventName) {
    // Refresh dependent object list
    this.onRefreshDepObj();
  }
  /**
   * Refresh the dependent objects list
   */
  public async onRefreshDepObj() {
    const editorId = this.getEditorComponent()?.getId();
    const dependentObjectSection = this.getView().byId("dependentObjectSection") as sap.uxap.ObjectPageSection;
    if (editorId === Editor.TABLEEDITOR) {
      dependentObjectSection.setBusy(true);
    }
    const currentObject = this.modelObjectForDependentObjects;
    if (
      this.getEditorComponent()?.getId() === Editor.TABLEEDITOR &&
      currentObject.isDeltaTable &&
      currentObject.deltaTableName !== undefined
    ) {
      this.prepareDependentObjectListData(currentObject, this.getView().getModel("header"), true).then((result) => {
        const deltaObject = {
          classDefinition: {
            name: "Table",
          },
          name: currentObject.deltaTableName,
          label: currentObject.deltaTableName,
          qualifiedName: currentObject.deltaTableName,
          "#objectStatus": currentObject["#objectStatus"],
        };
        this.prepareDependentObjectListData(deltaObject, this.getView().getModel("header"), true).then((res1) => {
          const items = res1?.items?.filter((item) => item.name !== currentObject.name);
          const finalDependencies = {
            inaccessibleDependencies: result?.inaccessibleDependencies?.concat(res1.inaccessibleDependencies),
            items: result?.items?.concat(items),
            numberOfItemsWithoutPermissionToView:
              result.numberOfItemsWithoutPermissionToView + res1.numberOfItemsWithoutPermissionToView,
          };
          this.getView().getModel("header").setProperty("/dependentObjects", finalDependencies);
          if (editorId === Editor.TABLEEDITOR) {
            dependentObjectSection.setBusy(false);
          }
        });
      });
    } else {
      this.prepareDependentObjectListData(currentObject, this.getView().getModel("header"), true, true);
      if (editorId === Editor.TABLEEDITOR) {
        dependentObjectSection.setBusy(false);
      }
    }
  }
  /**
   * Expand/collapse dependent objects panel
   */
  public async onDependentObjectPanelExpand(event) {
    if (event.getParameter("expand")) {
      this.onRefreshDepObj();
    }
  }
  /**
   * Reset filter (dependent object list)
   */
  public async _resetFilterDepObj() {
    const filterTabBar = this.byId("filterButtons") as sap.m.SegmentedButton;
    if (filterTabBar) {
      filterTabBar.setSelectedKey("all");
      let oBinding;
      if (this.byId("dependentObjectList").getBinding("items")) {
        // sap.m.Table
        oBinding = this.byId("dependentObjectList").getBinding("items") as sap.ui.model.ListBinding;
      } else {
        // sap.ui.table
        oBinding = this.byId("dependentObjectList").getBinding("rows") as sap.ui.model.ListBinding;
      }
      oBinding.filter([]);
      // eslint-disable-next-line no-underscore-dangle
      this._updateTableFilterDepObj();
    }
  }

  protected getDatabuilderWorkbench(): DataBuilderWorkbench {
    if (this.databuilderWorkbench) {
      return this.databuilderWorkbench;
    }
    this.databuilderWorkbench = getDatabuilderWorkbench(this.getView());
    return this.databuilderWorkbench;
  }

  protected getEditorComponent(): AbstractDataBuilderEditorComponentClass {
    const workbench = this.getDatabuilderWorkbench();
    return workbench?.getActiveEditor();
  }

  /**
   * Reset/ collapse the panel (dependent object list)
   */
  private _resetDepObjPanel() {
    const oModel = this.getGalileiModel();
    if (
      oModel &&
      typeof oModel.getProperty === "function" &&
      typeof oModel.getProperty("/dependentObjects") !== "undefined"
    ) {
      oModel.setProperty("/dependentObjects", undefined);
    }
    const dependentObjectPanel = this.byId("dependentObjectPanel") as sap.m.Panel;
    if (dependentObjectPanel) {
      dependentObjectPanel.setExpanded(false);
    }
  }
  /**
   * Reset the search (dependent object list)
   */
  private _resetSearchDepObj() {
    (this.byId("searchField") as sap.m.SearchField)["clear"]();
  }
  /**
   * Reset sorting (dependent object list)
   */
  public async _resetSortDepObj() {
    if (this.byId("sortListButton")) {
      const sortButtonData = this.getSortButtonDataDepObj("default");
      (this.byId("sortListButton") as sap.m.Button).setIcon(sortButtonData.icon);
      (this.byId("sortListButton") as sap.m.Button).setTooltip(
        this.localizeMessage("i18n_erd", sortButtonData.tooltipResourceId)
      );
      let oBinding;
      if (this.byId("dependentObjectList").getBinding("items")) {
        // sap.m.Table
        oBinding = this.byId("dependentObjectList").getBinding("items") as sap.ui.model.ListBinding;
      } else {
        // sap.ui.table
        oBinding = this.byId("dependentObjectList").getBinding("rows") as sap.ui.model.ListBinding;
      }
      oBinding.sort([]);
      // eslint-disable-next-line no-underscore-dangle
      this._updateTableSorterDepObj();
    }
  }
  // end of --- Dependent objects
  /**
   * Update depependent object list filter (all/errors only and search term)
   */
  private _updateTableFilterDepObj() {
    const Filter = sap.ui.model.Filter;
    const FilterOperator = sap.ui.model.FilterOperator;
    const aFilters = [];
    let combinedFilter;
    // filter buttons all/only errors
    if (this.byId("filterButtons")) {
      const objectStatusFilter = new Filter({
        path: "#objectStatus",
        test: (oValue) => {
          const sFilterMode = (this.byId("filterButtons") as sap.m.SegmentedButton).getSelectedKey();
          switch (sFilterMode) {
            case "all":
              return true;
            case "errorsOnly":
              return Format.isObjectStatusError(oValue);
          }
        },
      });
      aFilters.push(objectStatusFilter);
    }
    // search
    const aSearchFilter = [];
    const query = (this.getView().byId("searchField") as sap.m.SearchField).getValue();
    if (query && query.length > 0) {
      aSearchFilter.push(new Filter("displayName", FilterOperator.Contains, query));
      aSearchFilter.push(new Filter("name", FilterOperator.Contains, query));
      aFilters.push(
        new Filter({
          filters: aSearchFilter,
          and: false,
        })
      );
    }
    // combined filter
    if (aFilters.length > 1) {
      combinedFilter = new Filter({
        filters: aFilters,
        and: true,
      });
    } else {
      combinedFilter = aFilters;
    }
    let oBinding;
    if (this.byId("dependentObjectList").getBinding("items")) {
      // sap.m.Table
      oBinding = this.byId("dependentObjectList").getBinding("items") as sap.ui.model.ListBinding;
    } else {
      // sap.ui.table
      oBinding = this.byId("dependentObjectList").getBinding("rows") as sap.ui.model.ListBinding;
    }
    oBinding.filter([]);
    oBinding.filter([combinedFilter]);
  }
  /**
   * Update depependent object list item-sorting
   */
  private _updateTableSorterDepObj() {
    const oBinding = this.byId("dependentObjectList").getBinding("items") as sap.ui.model.ListBinding;
    const sortButtonData = this.getSortButtonDataDepObj("current");

    let descending;
    let path;
    if (sortButtonData.type === SORT_DEP_OBJ_BUTTON_DATA.descending.type) {
      path = "name";
      descending = true;
      oBinding.sort([new sap.ui.model.Sorter(path, descending)]);
    } else if (sortButtonData.type === SORT_DEP_OBJ_BUTTON_DATA.ascending.type) {
      path = "name";
      descending = false;
      oBinding.sort([new sap.ui.model.Sorter(path, descending)]);
    } else if (sortButtonData.type === SORT_DEP_OBJ_BUTTON_DATA.highestSeverityOnTop.type) {
      // custom sorter, by severity of '#objectStatus'
      path = "#objectStatus";
      const sorter = new sap.ui.model.Sorter(path, null);
      sorter["fnCompare"] = function (status1, status2) {
        // custom sort function, needed to sort by '#objectStatus' severity
        return getObjectStatusSeverity(status2) - getObjectStatusSeverity(status1);
      };
      oBinding.sort(sorter);
    }
  }
  // end of --- Dependent objects

  // Association methods
  public associationBusinessNameFormatter(oAssociation) {
    if (oAssociation.label) {
      return oAssociation.label;
    }
    if (oAssociation?.editable && oAssociation.source && oAssociation.target) {
      const sourceName = oAssociation.source?.label || oAssociation.source?.displayName || oAssociation.source?.name;
      const targetName = oAssociation.target?.label || oAssociation.target?.displayName || oAssociation.target?.name;
      oAssociation.label = this.getI18nText("i18n", "@associationTo", [sourceName, targetName]);
      return oAssociation.label;
    }
    if (oAssociation?.editable) {
      return oAssociation.name;
    } else {
      return "";
    }
  }

  public onTargetRowSelectionChange(oEvent: sap.ui.base.Event): void {
    commonUtils.onTargetRowSelectionChange(oEvent);
  }

  public sourceAssociationsFormatter(sourceAssociations) {
    const subTitle = this.getI18nText("i18n", "@availableSourceAssociations");
    if (sourceAssociations?.length) {
      return subTitle + " (" + String(sourceAssociations?.length) + ")";
    }

    return subTitle;
  }

  public onSourceAssociationSearch() {
    const Filter = sap.ui.model.Filter;
    const FilterOperator = sap.ui.model.FilterOperator;
    const aFilters = [];
    const aSearchFilter = [];
    const searchVal = (sap.ui.getCore().byId("sourceSearchField") as sap.m.SearchField).getValue();
    if (searchVal && searchVal.length > 0) {
      aSearchFilter.push(new Filter("viewSourceAttributes", FilterOperator.Contains, searchVal));
      aSearchFilter.push(new Filter("sourceName", FilterOperator.Contains, searchVal));
      aSearchFilter.push(new Filter("targetName", FilterOperator.Contains, searchVal));
      aFilters.push(
        new Filter({
          filters: aSearchFilter,
          and: false,
        })
      );
    }
    const table = sap.ui.getCore().byId("sourceAssociationsControl");
    if (table) {
      const oBinding = table.getBinding("rows") as sap.ui.model.ListBinding;
      if (oBinding) {
        oBinding.filter(aFilters);
      }
    }
  }

  public destroyCopyFromSourceAssociationDialog() {
    const dialog = sap.ui.getCore().byId("copyFromSourceAssociationsDialog") as sap.m.Dialog;
    dialog?.destroy();
  }

  public onCreateCopyFromSourceAssociation() {
    const model = this.getOutputModel();
    const output = model?.output;
    const spaceName = this.getSpaceName();
    if (output) {
      const self = this;
      const oTable: any = sap.ui.getCore().byId("sourceAssociationsControl") as any;
      const selectedIndices = oTable?.getSelectedIndices().reverse();
      const indicesNumber = selectedIndices.length;
      if (!indicesNumber) {
        return self.destroyCopyFromSourceAssociationDialog();
      }
      oTable.setBusy(true);
      output.resource.applyUndoableAction(function () {
        for (let i = 0; i < indicesNumber; i++) {
          const index = selectedIndices[i];
          const unresolvedAssociation = oTable.getContextByIndex(index).getObject();
          // Support both shared and local object
          const targetName = unresolvedAssociation.csn.target;
          getCurrentOrCrossSpaceObjectByName(spaceName, targetName, true).then((targetEntity) => {
            if (targetEntity) {
              const association = sap.cdw.querybuilder.ModelImpl.pushAssociation(
                unresolvedAssociation,
                targetEntity,
                output,
                model
              );
              updateAnnotationByAssociation(association);
            }
            if (i === indicesNumber - 1) {
              oTable.setBusy(false);
              self.destroyCopyFromSourceAssociationDialog();
            }
          });
        }
      });
    }
  }

  public onCancelCopyFromSourceAssociation() {
    this.destroyCopyFromSourceAssociationDialog();
  }

  public getOutputModel() {
    const galileiModel: any = this.getView().getModel("galileiModel");
    return galileiModel?.oData?.resource?.model;
  }
  public getSourceAssociationsToCopy() {
    const model = this.getOutputModel();
    return model ? getUnpushedAssociations(model) : [];
  }

  public openCopyFromSourceAssociationsDialog() {
    //
    let dialog = sap.ui.getCore().byId("copyFromSourceAssociationsDialog") as sap.m.Dialog;
    if (!dialog) {
      const dacNS = require("../view/SelectSourceAssociations.fragment.xml");
      dialog = sap.ui.xmlfragment(dacNS, this) as sap.m.Dialog;
      dialog.setEscapeHandler(this.onCancelCopyFromSourceAssociation.bind(this));
    }
    this.getView().addDependent(dialog);
    const i18nMod = this.getView().getModel("i18n");
    const oModel = new sap.ui.model.json.JSONModel({
      viewSourceAssociations: {},
      loadingText: "Looking for source associations to copy",
    });
    dialog.setModel(oModel, "oModel");
    dialog.setModel(i18nMod, "i18n");
    dialog.open();
    dialog.setBusy(true);
    const model = this.getOutputModel();
    updateUnresolvedAssociations(model).then(() => {
      (dialog.getModel("oModel") as sap.ui.model.json.JSONModel).getData().viewSourceAssociations =
        this.getSourceAssociationsToCopy();
      (dialog.getModel("oModel") as sap.ui.model.json.JSONModel).updateBindings(true);
      dialog.setBusy(false);
    });
  }

  public createEditKeyDialog(viewName: string, dialogId: string): sap.m.Dialog {
    const i18nModel = this.getView().getModel("i18n_erd");
    const model = this.getGalileiModel();
    const featureFlagsModel = this.getView().getModel("featureFlags") as sap.ui.model.json.JSONModel;
    const galileiModel = model.getData();
    const representativeKeyElement = galileiModel?.representativeKey;
    for (const element of galileiModel.orderedElements) {
      if (element.isKey && representativeKeyElement !== element) {
        const isExist = galileiModel?.compoundKeySequence?.contains(element);
        if (!isExist) {
          galileiModel.compoundKeySequence.push(element);
        }
      }
    }

    const notKeyColumns = galileiModel?.compoundKeySequence?.toArray()?.filter((keyColumn) => !keyColumn?.isKey);
    for (const notKeyColumn of notKeyColumns) {
      const notKeyElement = galileiModel?.compoundKeySequence
        ?.toArray()
        ?.filter((keyColumn) => keyColumn === notKeyColumn);
      if (notKeyElement?.length >= 1) {
        const elementIndex = galileiModel?.compoundKeySequence.indexOf(notKeyElement[0]);
        galileiModel?.compoundKeySequence?.removeAt(elementIndex);
      }
      if (galileiModel?.representativeKey && galileiModel?.representativeKey === notKeyColumn) {
        galileiModel.representativeKey = undefined;
      }
    }
    const representativeKey = galileiModel?.representativeKey ? [galileiModel?.representativeKey] : [];

    const keyModel = new sap.ui.model.json.JSONModel({
      representativeKey: representativeKey,
    });
    const view = sap.ui.view({
      type: sap.ui.core.mvc.ViewType.XML,
      viewName: viewName,
      id: dialogId + "View",
    });

    view.setModel(i18nModel, "i18n_erd");
    view.setModel(keyModel, "keyModel");
    view.setModel(model, "galileiModel");
    view.setModel(featureFlagsModel, "featureFlags");
    const keyDialog = new sap.m.Dialog({
      id: dialogId,
      title: i18nModel.getProperty("@CompoundKey"),
      type: "Standard",
      content: [view],
      contentWidth: "25%",
      contentHeight: "75%",
      endButton: new sap.m.Button({
        id: dialogId + "-closeBtn",
        text: i18nModel.getProperty("@close"),
        press: function () {
          keyDialog.close();
        },
        type: sap.m.ButtonType.Emphasized,
      }),
      afterClose: function () {
        view.byId("addRepoKeyBtn")?.destroy();
        view.byId("removeRepoKeyBtn")?.destroy();
        view.byId("representativelist")?.destroy();
        view.byId("repoKeyBtn")?.destroy();
        view.byId("moveUp")?.destroy();
        view.byId("moveUp")?.destroy();
        view.byId("keyColumnList")?.destroy();
        view.byId("keyBtn")?.destroy();
        keyDialog.destroy();
        view.destroy();
      },
    });
    return keyDialog;
  }
  public onPressEditCompoundKey() {
    const keyDialog = this.createEditKeyDialog(
      require("../view/KeyColumnEdit.view.xml"),
      this.createId("keyColumnEditDialog")
    );
    keyDialog.open();
  }
  public onPressCreateAssociation(event?, element?, targetType?) {
    if (event && event.getParameter && !targetType) {
      const item = event.getParameter("item");
      if (item?.getKey() === "AddTextAssociation") {
        targetType = DataCategory.TEXT;
      } else if (item?.getKey() === "AddHierarchyAssociation") {
        targetType = DataCategory.HIERARCHY;
      } else if (item?.getKey() === "AddHierarchyWithDirectoryAssociation") {
        targetType = DataCategory.HIERARCHY_WITH_DIRECTORY;
      } else if (item?.getKey() === "AddPushedAssociation") {
        return this.openCopyFromSourceAssociationsDialog();
      }
    }
    const self = this;
    const spaceName = super.getSpaceName();
    const i18nMod = this.getView().getModel("i18n");
    const oDialogModel = new sap.ui.model.json.JSONModel({
      title: this.associationDialogTitleFormatter(targetType),
      objects: {},
      possibleTargetTypes: this.getPossibleSemanticTypes(),
      loadingText: "Loading Objects",
      targetType: targetType || TableTypes.ALL,
      isOkEnabled: false,
      isSelectEnabled: [DataCategory.TEXT, DataCategory.HIERARCHY, DataCategory.HIERARCHY_WITH_DIRECTORY].includes(
        targetType
      )
        ? false
        : true,
      selectedElement: element,
    });
    const isHarmonizationObjectSelectionFF = isHarmonizationObjectSelection();
    if (!isHarmonizationObjectSelectionFF) {
      commonUtils.openSelectTargetDialog(oDialogModel, i18nMod, self, targetType);
    } else {
      const rootModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
      const oModel = rootModel.getData().resource.model;
      sap.ui.require(
        [
          "sap/esh/search/ui/sinaNexTS/sina/SimpleCondition",
          "sap/esh/search/ui/sinaNexTS/sina/ComparisonOperator",
          "sap/esh/search/ui/sinaNexTS/sina/ComplexCondition",
          "sap/esh/search/ui/sinaNexTS/sina/LogicalOperator",
        ],
        async (SimpleConditionModule, ComparisonOperator, ComplexConditionModule, LogicalOperator) => {
          const oFeatures = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();
          const parameterCondition = new SimpleConditionModule.SimpleCondition({
            operator: ComparisonOperator.ComparisonOperator.Ne,
            attribute: "has_parameters",
            value: "true",
          });

          const notCondition = new ComplexConditionModule.ComplexCondition({
            operator: LogicalOperator.LogicalOperator.Not,
          });

          const rowCondition2 = new ComplexConditionModule.ComplexCondition({
            operator: LogicalOperator.LogicalOperator.Row,
          });
          const deltaCondition2 = new ComplexConditionModule.ComplexCondition({
            operator: LogicalOperator.LogicalOperator.And,
          });
          deltaCondition2.addCondition(
            new SimpleConditionModule.SimpleCondition({
              attribute: "tag_name",
              operator: ComparisonOperator.ComparisonOperator.Eq,
              value: CsnAnnotations.DataWarehouse.delta,
            })
          );
          deltaCondition2.addCondition(
            new SimpleConditionModule.SimpleCondition({
              attribute: "tag_value",
              operator: ComparisonOperator.ComparisonOperator.Search,
              value: '*"type":{"#":"UPSERT"}*',
            })
          );

          rowCondition2.addCondition(deltaCondition2);
          notCondition.addCondition(rowCondition2);

          const orCondition = new ComplexConditionModule.ComplexCondition({
            operator: LogicalOperator.LogicalOperator.Or,
          });

          (targetType ? ["DWC_" + targetType] : self.getPossibleTechnicalTypes())?.forEach?.((type) => {
            orCondition.addCondition(
              new SimpleConditionModule.SimpleCondition({
                attribute: "business_type",
                operator: ComparisonOperator.ComparisonOperator.Eq,
                value: type,
              })
            );
          });

          const obj = {
            supportedTypes: self.getPossibleTechnicalTypes(),
            spaceName: spaceName,
            conditions: [notCondition, orCondition],
          };
          this["objectDialog"] = await openExplorerSelectorDialogExt({
            styleClass: "repository-package-editor-object-selection",
            title: self.associationDialogTitleFormatter(targetType),
            spaceName: obj.spaceName,
            includeSharedObjects: true,
            onSelect: this.onObjectSelect.bind(this),
            customRowEnablement: self.customRowEnablement.bind(self),
            enableMultiSelection: false,
            displayBasket: false,
            enableFolder: true,
            postDataRead: this.postDataRead.bind(this),
            selectButtonText: this.localizeMessage("i18n_erd", "@addObjectBtn"),
            getSupportedTechnicalTypes: async () => ["DWC_VIEW", "DWC_LOCAL_TABLE", "DWC_REMOTE_TABLE", "DWC_IDT"],
            configure: (configuration) => {
              configuration.searchOnStart = true;
              configuration.resultViewTypes = ["searchResultTable"];

              const querySuffix = configuration.sinaConfiguration.querySuffix;
              if (obj.conditions) {
                obj.conditions.forEach((condition: any) => {
                  querySuffix.addCondition(condition);
                });
              }
            },
            onClose: () => {
              self.byId("splitApp")?.setBusy(false);
            },
          });

          this["objectDialog"].setModel(oDialogModel, "oDialogModel");
        }
      );
    }
  }

  public customRowEnablement(currentRow) {
    const technicalType = currentRow?.attributesMap?.technical_type?.value;
    if (technicalType === "DWC_FOLDER") {
      return false;
    } else {
      return true;
    }
  }

  public async postDataRead(list) {
    if (list.length > 0) {
    }
  }

  public async onObjectSelect(evt) {
    const selectedObjArr = evt.getParameter("selection");
    if (selectedObjArr.length > 0) {
      const dialogData = this["objectDialog"]?.getModel("oDialogModel")?.getData();
      const selectedElement = dialogData?.selectedElement;
      const targetType = dialogData?.targetType;
      let selectedObj = selectedObjArr[0];
      if (selectedObj?.isFolder === true) {
        return;
      }
      const rootModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
      const oModel = rootModel.getData().resource.model;

      if (SupportedFeaturesService.getInstance().isSkylineERModelerEnabled() && oModel?.isSkylineERModel) {
        const sourceEntity = rootModel["oData"];
        const targetEntityName = selectedObj.technicalName;
        oModel.loadAndAddAssociation(
          sourceEntity,
          this.spaceName,
          selectedObj?.space,
          targetEntityName,
          selectedElement,
          (association) => {
            association.proposeDefaultMappings(targetType, selectedElement);
            rootModel.setProperty("/selectedAssociation", association);
            this.switchPage(Pages.DetailsPage);
          }
        );
        return;
      }

      const details = [
        "csn",
        "name",
        "kind",
        "#isViewEntity",
        "creation_date",
        "owner",
        "creator_user_name",
        "deployment_date",
        "#deploymentExecutionStatus",
        "releaseStateValue",
        "releaseDate",
        "deprecationDate",
        "decommissioningDate",
        "#writeAccess",
        "#creatorBusinessName",
        "#ownerBusinessName",
        "#hasParameters",
        "@Analytics.dbViewType",
        "#isToolingHidden",
      ];
      if (selectedObj?.space === this.spaceName) {
        const rootModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
        const oModel = rootModel.getData().resource.model;

        Repo.getModelDetails(this.spaceName, selectedObj.technicalName, details).then((e) => {
          const type = getFullDataCategoryName(e);
          const icon = getFileTypeAndIcon(e).icon;
          const analyticsType = getAnalyticsType(e);
          const selectedObjWithDetails = {
            displayName: e.name,
            kind: e.kind,
            name: e.name,
            displayLabel: e["@EndUserText.label"] || e.name,
            label: e["@EndUserText.label"] || e.name,
            id: e.name,
            folderOrder: e.name,
            hasChildren: false,
            icon,
            type,
            analyticsType,
            definitions: e.csn?.definitions,
            creator: e["#creatorBusinessName"] || e["#ownerBusinessName"] || e.creator,
            creation_date: e.creation_date,
            owner: e.owner, // Modifier
            modification_date: e.modification_date,
            deployment_date: e.deployment_date,
            dbViewType: undefined,
            "#deploymentExecutionStatus": e["#deploymentExecutionStatus"],
            "#objectStatus": e["#objectStatus"],
            releaseState: getReleaseStateFromValue(e.releaseStateValue),
            // "initialState": getReleaseStateFromValue(e.releaseStateValue),
            "#writeAccess": e["#writeAccess"],
            "#isViewEntity": e["#isViewEntity"],
            "#isToolingHidden": e["#isToolingHidden"],
          };
          const dbViewTypeCsn = e["@Analytics.dbViewType"];
          if (dbViewTypeCsn) {
            selectedObjWithDetails.dbViewType = dbViewTypeCsn;
          }

          const targetFound = sap.cdw.ermodeler.ModelImpl.checkSymbolExists(selectedObjWithDetails, oModel, {
            "object.technicalName": selectedObjWithDetails.name,
          });
          const oAssociation = sap.cdw.ermodeler.ModelImpl.processAssociations(
            rootModel,
            targetFound,
            selectedObjWithDetails,
            { isUnresolved: false, name: {} },
            targetType,
            selectedElement
          );
          rootModel.setProperty("/selectedAssociation", oAssociation);
          this.switchPage(Pages.DetailsPage);
        });
      } else if (selectedObj?.space) {
        const loadSharedObjects = await getArtefactSharesForTarget(this.spaceName, details, [
          `name:${selectedObj.technicalName}`,
        ]);
        if (loadSharedObjects?.results?.length > 0) {
          let e;
          loadSharedObjects.results.forEach((obj) => {
            if (obj.spaceName === selectedObj.space) {
              e = obj;
            }
          });
          if (!e) {
            e = loadSharedObjects.results[0];
          }
          if (e.content && !e.csn) {
            e.csn = e.content;
            delete e.content;
          }
          const isCrossSpace = true;
          const type = getFullDataCategoryName(e);
          const icon = getFileTypeAndIcon(e).icon;
          const analyticsType = getAnalyticsType(e);
          const entityName = e.csn && e.csn.definitions && getEntityNameFromCSN(e.csn.definitions);
          const spaceDisplayName = e.spaceName;
          const displayName = e.name;
          selectedObj = {
            displayName: e.name + (e.spaceName !== undefined ? " (" + spaceDisplayName + ")" : ""),
            spaceName: e.spaceName,
            name: e.name,
            kind: e.kind,
            displayLabel: e.name + (e.spaceName !== undefined ? " (" + spaceDisplayName + ")" : ""),
            label: displayName + (e.spaceName !== undefined ? " (" + spaceDisplayName + ")" : ""), // this is link text
            id: e.name,
            folderOrder: e.name,
            hasChildren: false,
            type: type,
            icon,
            analyticsType,
            definitions: (e.csn && e.csn.definitions) as ICsnDefinitions,
            creator:
              (e.properties && (e.properties["#creatorBusinessName"] || e.properties["#ownerBusinessName"])) ||
              e.creator,
            creation_date: e.creationDate,
            owner: e.owner, // Modifier
            modification_date: e.modificationDate,
            deployment_date: e.deploymentDate,
            dbViewType: undefined,
            isCrossSpace: isCrossSpace,
            "#deploymentExecutionStatus": e.properties && e.properties["#deploymentExecutionStatus"],
            "#objectStatus": e.properties && e.properties["#objectStatus"],
            releaseState: getReleaseStateFromValue(e.properties?.releaseStateValue),
            // initialState: getReleaseStateFromValue(e.properties?.releaseStateValue),
            "#writeAccess": e.properties && e.properties["#writeAccess"],
            spaceId: e.spaceId,
            "#isViewEntity": e.properties && e.properties["#isViewEntity"],
            qualifiedName: e.spaceName + "." + entityName,
            ...{ "#isToolingHidden": e["#isToolingHidden"] },
          };
          selectedObj["#repositoryPackage"] = e.properties["#repositoryPackage"];
          if (entityName !== undefined && selectedObj.definitions[entityName]) {
            const businessName = selectedObj.definitions[entityName]["@EndUserText.label"];
            if (businessName) {
              selectedObj.displayLabel = businessName + (e.spaceName !== undefined ? " (" + e.spaceName + ")" : "");
            }
            selectedObj.dbViewType =
              selectedObj.definitions[entityName] && selectedObj.definitions[entityName]["@Analytics.dbViewType"];
          }
          if (e.spaceName !== undefined && entityName !== undefined) {
            selectedObj.definitions[selectedObj.qualifiedName] = {
              ...selectedObj.definitions[entityName],
              "@cds.persistence.exists": true,
              "@DataWarehouse.space.name": e.spaceName,
              "@DataWarehouse.space.schema": e.spaceSchemaName,
              "@DataWarehouse.space.businessName": e.spaceBusinessName,
            };
            delete selectedObj.definitions[entityName];

            selectedObj.definitions[e.spaceName] = {
              kind: "context",
              "@DataWarehouse.space.name": e.spaceName,
              "@DataWarehouse.space.schema": e.spaceSchemaName,
              "@EndUserText.label": e.spaceBusinessName,
            };

            // Remove useless space section from backend
            for (const name in selectedObj.definitions) {
              const def = selectedObj.definitions[name];
              if (def?.kind === ObjectKind.context && name !== e.spaceName) {
                delete selectedObj.definitions[name];
              }
            }

            const targetFound = sap.cdw.ermodeler.ModelImpl.checkSymbolExists(selectedObj, oModel);
            const oAssociation = sap.cdw.ermodeler.ModelImpl.processAssociations(
              rootModel,
              targetFound,
              selectedObj,
              { isUnresolved: false, name: {} },
              targetType,
              selectedElement
            );
            rootModel.setProperty("/selectedAssociation", oAssociation);
            this.switchPage(Pages.DetailsPage);
          }
        }
      }
    }
  }

  public onAssociationSelect(event: sap.ui.base.Event) {
    const oModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const oSelectedItem = event.getParameter("listItem");
    const list = oSelectedItem.getList();
    oModel.setProperty("/associationSelectedCount", list.getSelectedItems().length);
  }

  public async getObjects() {
    const spaceName = super.getSpaceName();
    if (spaceName) {
      const list = await getAllObjectsOfSpace(spaceName, false);
      const allObjects = list.filter(
        (obj) => obj.kind === "entity" && obj.analyticsType !== AnalyticsType.Cube && obj["#isToolingHidden"] != "true"
      );
      return commonUtils.getObjectsMap(allObjects);
    } else {
      return [];
    }
  }

  public getPossibleSemanticTypes() {
    const targetTypesArr = entityTypeModelData.datasetTypesWithAll;
    return targetTypesArr;
  }

  public getPossibleTechnicalTypes() {
    const targetTypesArr = entityTypeModelData.technicalTypes;
    return targetTypesArr;
  }

  public switchPage(toPage: string): void {
    const navCon = this.byId("navCon") as sap.m.NavContainer;
    const model: sap.galilei.ui5.GalileiModel = this.getView().getModel("galileiModel") as sap.galilei.ui5.GalileiModel;
    // const table = this.byId("associationList") as sap.m.List;
    const oAssociation = model.getProperty("/selectedAssociation");
    let directionTo = true;
    if (toPage === Pages.ListPage) {
      directionTo = false;
      this.setHeaderModel(model.getData());
    } else {
      // Association Properties page
      if (toPage === Pages.DetailsPage && oAssociation) {
        this.setHeaderModel(oAssociation);
        this.setAssociationModel(oAssociation);
      }
    }
    if (toPage) {
      if (directionTo) {
        navCon.to(this.byId(toPage) as sap.m.Page, "slide");
      } else {
        navCon.back(this.byId(toPage) as sap.m.Page, "slide");
      }
    } else {
      navCon.back();
    }
  }

  public onBackToFields() {
    this.switchPage(Pages.ListPage);
  }

  public onAssociationEdit(oEvent) {
    const model: sap.galilei.ui5.GalileiModel = this.getView().getModel("galileiModel") as sap.galilei.ui5.GalileiModel;
    const bindingPath: string = (oEvent.getSource() as sap.m.Button).getBindingContext("galileiModel").getPath();
    const oSelectedElement = bindingPath && model.getProperty(bindingPath);
    const table = this.byId("associationsList") as sap.m.Table;
    const aItems = table.getItems();
    let oCurrentElement;
    for (const item of aItems) {
      oCurrentElement = model.getProperty(item.getBindingContext("galileiModel").getPath());
      if (oSelectedElement === oCurrentElement) {
        table.setSelectedItem(item, true);
        item.focus();
      }
    }
    model.setProperty("/associationSelected", true);
    if (oSelectedElement) {
      model.setProperty("/selectedAssociation", oSelectedElement);
      this.switchPage(Pages.DetailsPage);
    } else {
      model.setProperty("/selectedAssociation", "");
    }
  }

  public onDACLEdit(oEvent) {
    const self = this;
    const model: sap.galilei.ui5.GalileiModel = this.getView().getModel("galileiModel") as sap.galilei.ui5.GalileiModel;
    const bindingPath: string = (oEvent.getSource() as sap.m.Button).getBindingContext("galileiModel").getPath();
    const oSelectedElement = bindingPath && model.getProperty(bindingPath);
    const table = this.byId("viewDataAccessControlsList") as sap.m.Table;
    const aItems = table.getItems();
    let oCurrentElement;
    const nLength = aItems ? aItems.length : 0;
    for (let i = 0; i < nLength; i++) {
      oCurrentElement = model.getProperty(aItems[i].getBindingContext("galileiModel").getPath());
      if (oSelectedElement === oCurrentElement) {
        table.setSelectedItem(aItems[i], true);
        aItems[i].focus();
      }
    }
    model.setProperty("/daclSelected", true);
    if (oSelectedElement) {
      model.setProperty("/selectedDacl", oSelectedElement);
      this.getView().setBusy(true);
      this.updateSelectedViewDataAccessControl(oSelectedElement)
        .then(() => {
          self.switchPage(Pages.DACLPage);
          self.setHeaderModel(oSelectedElement);
          self.setDACLModel(oSelectedElement);
        })
        .catch(() => {
          const message = this.localizeMessage(
            "i18n_commonmodel",
            "VAL_VERSIONING_RESTORE_DAC_TARGET_NOT_EXIST_FOR_MSGBOX",
            [oSelectedElement.name]
          );
          sap.m.MessageBox.error(message, {
            initialFocus: sap.m.MessageBox.Action.CLOSE,
          });
        })
        .finally(() => {
          self.getView().setBusy(false);
        });
    } else {
      model.setProperty("/selectedDacl", "");
    }
  }

  public onDataAccessControlSearch() {
    const Filter = sap.ui.model.Filter;
    const FilterOperator = sap.ui.model.FilterOperator;
    const aFilters = [];
    const aSearchFilter = [];
    const searchVal = (sap.ui.getCore().byId("dacSearchField") as sap.m.SearchField).getValue();
    if (searchVal && searchVal.length > 0) {
      aSearchFilter.push(new Filter("technicalName", FilterOperator.Contains, searchVal));
      aSearchFilter.push(new Filter("label", FilterOperator.Contains, searchVal));
      aFilters.push(
        new Filter({
          filters: aSearchFilter,
          and: false,
        })
      );
    }
    const table = sap.ui.getCore().byId("targetDataAccessControl");
    if (table) {
      const oBinding = table.getBinding("rows") as sap.ui.model.ListBinding;
      if (oBinding) {
        oBinding.filter(aFilters);
      }
    }
  }

  public setDACLModel(viewDataAccessControl): void {
    const joinPropertiesView = (this.getView() as sap.ui.core.mvc.XMLView).byId(
      "daclJoin--daclPropertiesDialogView"
    ) as sap.ui.core.mvc.XMLView;
    (joinPropertiesView?.getController() as any)?.setAssociationModel(viewDataAccessControl); // TODO: interface missing
  }

  public setFilterColumnModel(oObject): void {
    const filterColumnView = (this.getView() as sap.ui.core.mvc.XMLView).byId(
      "filterColumnDetailsContainer"
    ) as sap.ui.core.mvc.XMLView;
    (filterColumnView?.getController() as any)?.setObjectModel(oObject);
  }

  // __DP__ Set Analytical Measure view model
  public setAnalyticMeasureModel(oObject: any, viewId: string): void {
    const filterColumnView = (this.getView() as sap.ui.core.mvc.XMLView).byId(viewId) as sap.ui.core.mvc.XMLView;
    (filterColumnView?.getController() as any)?.setObjectModel(oObject);
  }

  public setAssociationModel(oObject): void {
    const joinPropertiesView = (this.getView() as sap.ui.core.mvc.XMLView).byId(
      "joinPropertiesDialogView"
    ) as sap.ui.core.mvc.XMLView;
    const targetExists = oObject.target;
    if (!targetExists && oObject.csn && oObject.csn.target) {
      oObject.target = {
        displayName: oObject.csn.target,
      };
    }
    (joinPropertiesView?.getController() as any)?.setAssociationModel(oObject);
  }

  public onPressDeleteAssociation() {
    const model: sap.galilei.ui5.GalileiModel = this.getView().getModel("galileiModel") as sap.galilei.ui5.GalileiModel;
    const oOutputNode = model.getProperty("/");
    const table = this.byId("associationsList") as any;
    const self = this;
    let oSelectedItems,
      isTable = false;
    // View builders and ER Modeler - Associations is a list control
    if (typeof table.getSelectedItems === "function") {
      oSelectedItems = table.getSelectedItems();
    }
    // Table Editor - Associations is a table
    else if (typeof table.getSelectedIndices === "function") {
      oSelectedItems = table.getSelectedIndices();
      isTable = true;
    }
    const oResource = oOutputNode.resource;
    oResource.applyUndoableAction(function () {
      const elementNames: string[] = [];
      const hierarchiesAndTexts: string[] = [];
      const analyticModelLifecycleHandlingIsActive = sap.ui
        .getCore()
        .getModel("featureflags")
        ?.getProperty("/DWCO_MODELING_AM_LIFECYCLE_HANDLING_STATUS");
      for (let columnIndex = oSelectedItems.length - 1; columnIndex >= 0; columnIndex--) {
        let bindingPath, oSelectedElement;
        const oSelectedItem = oSelectedItems[columnIndex];
        if (!isTable) {
          bindingPath = oSelectedItem && oSelectedItem.getBindingContext("galileiModel").getPath();
          oSelectedElement = bindingPath && model.getProperty(bindingPath);
        } else {
          oSelectedElement = table.getContextByIndex(oSelectedItem)?.getObject();
        }
        const elementIsHierarchy = oSelectedElement.isHierarchy || oSelectedElement.isHierarchyWithDirectory;
        if (oSelectedElement.classDefinition && oSelectedElement.classDefinition.name === "Association") {
          const selectedElementName = oSelectedElement.name;
          if (
            analyticModelLifecycleHandlingIsActive &&
            selectedElementName &&
            (elementIsHierarchy || oSelectedElement.isText)
          ) {
            hierarchiesAndTexts.push(selectedElementName);
          }
          oSelectedElement.deleteObject();
          if (selectedElementName) {
            elementNames.push(selectedElementName);
          }
        } else {
          if (oSelectedElement && oSelectedElement.csn && oSelectedElement.source) {
            const aUnresolvedAssociations = oSelectedElement.source.resource.model.unresolvedAssociations;
            const index = aUnresolvedAssociations.findIndex(
              (obj) => obj.source?.name === oOutputNode?.name && obj.name === oSelectedElement.name
            );
            const aUnresolved = [...aUnresolvedAssociations];
            aUnresolved.splice(index, 1);
            oSelectedElement.source.resource.model.unresolvedAssociations = aUnresolved;
            if (oSelectedElement.name) {
              elementNames.push(oSelectedElement.name);
              if (analyticModelLifecycleHandlingIsActive && (elementIsHierarchy || oSelectedElement.isText)) {
                hierarchiesAndTexts.push(oSelectedElement.name);
              }
            }
          }
        }
      }
      if (oOutputNode.classDefinition?.name === "Output") {
        const diagramEditor = self.getDiagramEditor();
        const outputSymbol = oOutputNode.relatedSymbols?.get(0);
        if (diagramEditor && outputSymbol) {
          diagramEditor.drawSymbol(outputSymbol);
        }
      }
      if (oOutputNode?.getRepositoryCSN?.() && elementNames.length > 0) {
        oOutputNode?.loadExternalImpact?.({ onlyElements: elementNames, force: true }).then?.(() => {
          if (analyticModelLifecycleHandlingIsActive && hierarchiesAndTexts.length) {
            oOutputNode?.loadDependantAnalyticModels?.().then?.(() => {
              hierarchiesAndTexts.forEach((association) => {
                sap.cdw.commonmodel.Validation.addAssociationImpactForAnalyticModel(oOutputNode, association);
              });
            });
          }
          sap.cdw.commonmodel.Validation.validateAssociationDependencies(oOutputNode);
        });
        oOutputNode.loadDependantOnHierarchySourceDACs().then(() => {
          sap.cdw.commonmodel.Validation.validateDacHierarchyWithDirectoryAssociationsSource(oOutputNode);
        });
      }
    });
    if (isTable) {
      table.clearSelection();
    }
    oResource.model.validate();
    model.refresh(true);
  }

  public onSelectDialogSearch(oEvent?, isTypeSelect?) {
    commonUtils.onSelectDialogSearch(oEvent, isTypeSelect);
  }

  public onCreateOK(oEvent) {
    const table: sap.m.Table = sap.ui.getCore().byId("targetTable") as sap.m.Table;
    const selectedIndex = table["getSelectedIndex"]();
    let aContexts;
    if (selectedIndex !== -1) {
      aContexts = table["getContextByIndex"](selectedIndex);
    }
    let oSelObject;
    if (aContexts) {
      oSelObject = aContexts.getObject().object;
    }
    const rootModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const oModel = rootModel.getData().resource?.model;
    const targetFound = sap.cdw.ermodeler.ModelImpl.checkSymbolExists(oSelObject, oModel);
    const dialog = oEvent.getSource().getParent();
    const dialogData = dialog?.getModel("oModel")?.getData();
    const selectedElement = dialogData?.selectedElement;
    const targetType = dialogData?.targetType;
    if (!oSelObject.isCrossSpace) {
      dialog?.setBusy(true);
      Repo.getModelDetails(this.spaceName, oSelObject.name, ["csn"]).then((details) => {
        if (details.csn && details.csn.definitions) {
          oSelObject.definitions = details.csn.definitions;
        }
        dialog?.setBusy(false);
        const oAssociation = sap.cdw.ermodeler.ModelImpl.processAssociations(
          rootModel,
          targetFound,
          oSelObject,
          { isUnresolved: false, name: {} },
          targetType,
          selectedElement
        );
        rootModel.setProperty("/selectedAssociation", oAssociation);
        dialog?.destroy();
        this.switchPage(Pages.DetailsPage);
      });
    } else {
      const oAssociation = sap.cdw.ermodeler.ModelImpl.processAssociations(
        rootModel,
        targetFound,
        oSelObject,
        { isUnresolved: false, name: {} },
        targetType,
        selectedElement
      );
      rootModel.setProperty("/selectedAssociation", oAssociation);
      dialog?.destroy();
      this.switchPage(Pages.DetailsPage);
    }
  }

  private getNamesControlsCommon() {
    const oAssociation = this.getView().getModel("header").getProperty("/");
    const businessInput = this.getView().byId("associationBName") as sap.m.Input;
    const technicalInput = this.getView().byId("associationTName") as sap.m.Input;
    return {
      isTableColumn: true,
      object: oAssociation,
      businessInput: businessInput,
      technicalInput: technicalInput,
      technicalNameProperty: "name",
    };
  }

  public onAssociationBusinessNameChange(oEvent) {
    const nameValidator = NamingHelper.getNameInputValidator();
    const controls = this.getNamesControlsCommon();
    const siblings = this.getColumnSiblings(controls.object.source); // controls.object.container.associations?.toArray();
    const emptyMsg = this.getI18nText("i18n", "VAL_ASSOCIATION_EMPTY");
    const value = oEvent.getParameter("newValue");
    const notUniqueMsg = this.getI18nText("i18n", "VAL_ASSOCIATION_NOT_UNIQUE", [value]);

    if (oEvent.sId === "liveChange") {
      nameValidator.onLabelChanged(
        controls.businessInput,
        controls.technicalInput,
        controls.object,
        controls.technicalNameProperty,
        {
          duplicatedLabel: notUniqueMsg,
          emptyLabel: emptyMsg,
        },
        siblings,
        NameUsage.association
      );
    } else if (oEvent.sId === "change") {
      nameValidator.onLabelSubmit(
        controls.object,
        controls.businessInput,
        controls.technicalInput,
        controls.technicalNameProperty,
        {
          duplicatedLabel: notUniqueMsg,
          emptyLabel: emptyMsg,
        },
        siblings,
        NameUsage.association
      );
    }
  }

  public async onAssociationTechNameChange(oEvent) {
    const nameValidator = NamingHelper.getNameInputValidator();
    const controls = this.getNamesControlsCommon();
    const siblings = this.getColumnSiblings(controls?.object?.source); // controls.object.container.associations?.toArray();
    const emptyMsg = this.getI18nText("i18n", "VAL_ASSOCIATION_EMPTY");
    const value = oEvent.getParameter("newValue");
    const notUniqueMsg = this.getI18nText("i18n", "VAL_ASSOCIATION_NOT_UNIQUE", [value]);

    if (oEvent.sId === "liveChange") {
      nameValidator.onTechnicalNameChanged(
        controls.object,
        controls.technicalInput,
        controls.technicalNameProperty,
        {
          duplicatedTechnicalName: notUniqueMsg,
          emptyTechnicalName: emptyMsg,
        },
        siblings,
        /* usage */ NameUsage.association
      );
    } else if (oEvent.sId === "change") {
      const oldName = controls.object.name;
      nameValidator.onTechnicalNameSubmit(
        controls.object,
        controls.technicalInput,
        controls.technicalNameProperty,
        {
          duplicatedTechnicalName: notUniqueMsg,
          emptyTechnicalName: emptyMsg,
        },
        siblings,
        true,
        /* usage */ NameUsage.association
      );
      // Trigger association and source entity validation?
      const oOutputNode = this.getView?.()?.getModel?.("galileiModel")?.getProperty?.("/");
      if (oldName && oOutputNode?.getRepositoryCSN?.()) {
        await oOutputNode?.loadExternalImpact?.({ onlyElements: [oldName], force: true });
        if (controls?.object?.isHierarchy || controls?.object?.isHierarchyWithDirectory || controls?.object?.isText) {
          await oOutputNode?.loadDependantAnalyticModels?.();
          sap.cdw.commonmodel.Validation.addAssociationImpactForAnalyticModel(oOutputNode, oldName);
        }
      }
      if (controls?.object?.container?.isSkylineERModel) {
        controls.object.source?.validate();
      } else {
        sap.cdw.commonmodel.Validation.validateEntity(controls?.object?.source);
        sap.cdw.commonmodel.Validation.validateAssociation(controls?.object);
      }
    }
  }

  public onCancel(oEvent) {
    commonUtils.onCancel();
  }

  public getI18nText(oModel, text: string, params?: string[]) {
    let sText;
    if (
      this.getView() &&
      this.getView().getModel(oModel) &&
      (this.getView().getModel(oModel) as sap.ui.model.resource.ResourceModel).getResourceBundle()
    ) {
      sText = (this.getView().getModel("i18n") as sap.ui.model.resource.ResourceModel)
        .getResourceBundle()
        .getText(text, params);
    }
    return sText;
  }

  public collectionHeaderTextFormatter(titleText: string, collection: any) {
    return collectionHeaderTextFormatter(titleText, collection);
  }

  public formatterShowDependentObjectPanel = function (isVersioningReadOnlyMode: boolean, editor: string): boolean {
    if (isVersioningReadOnlyMode && getIsEditorSupportVersions(editor)) {
      return false;
    }
    return true;
  };

  public collectionHeaderTextBusyFormatter(
    titleText: string,
    collection: any,
    numberOfItemsWithoutPermissionToView: number,
    deltaReadOnly?: boolean
  ) {
    return collectionHeaderTextBusyFormatter(
      titleText,
      collection,
      numberOfItemsWithoutPermissionToView,
      deltaReadOnly
    );
  }

  public openDacInfoPopover(event: IEvent<sap.m.Button, { id: string }>): void {
    const sourceControl = event.getSource();

    if (!this.dacInfoPopover) {
      require("./DacInfoPopover.fragment.xml");
      this.dacInfoPopover = sap.ui.xmlfragment(
        this.createId("DacInfoPopover"),
        "sap.cdw.components.ermodeler.properties.DacInfoPopover",
        this
      ) as sap.m.Popover;
      this.getView().addDependent(this.dacInfoPopover);
    }
    const bindingContext = sourceControl.getBindingContext("galileiModel");
    this.dacInfoPopover.bindElement(`galileiModel>${bindingContext.getPath()}`);

    const oSelectedElement = bindingContext.getObject();
    if (oSelectedElement) {
      sourceControl.setBusy(true);
      this.updateSelectedViewDataAccessControl(oSelectedElement)
        .then(() => {
          this.dacInfoPopover.openBy(sourceControl, false);
        })
        .catch(() => {
          const message = this.localizeMessage(
            "i18n_commonmodel",
            "VAL_VERSIONING_RESTORE_DAC_TARGET_NOT_EXIST_FOR_MSGBOX",
            [oSelectedElement.name]
          );
          sap.m.MessageBox.error(message, {
            initialFocus: sap.m.MessageBox.Action.CLOSE,
          });
        })
        .finally(() => {
          sourceControl.setBusy(false);
        });
    }
  }

  public async openAssociationInfoPopover(event: IEvent<sap.m.Button, { id: string }>): Promise<void> {
    const sourceControl = event.getSource();

    if (!this.associationtInfoPopover) {
      require("./AssociationInfoPopover.fragment.xml");
      this.associationtInfoPopover = sap.ui.xmlfragment(
        this.createId("AssociationInfoPopover"),
        "sap.cdw.components.ermodeler.properties.AssociationInfoPopover",
        this
      ) as sap.m.Popover;
      this.getView().addDependent(this.associationtInfoPopover);
    }
    const bindingContext = sourceControl.getBindingContext("galileiModel");
    this.associationtInfoPopover.bindElement(`galileiModel>${bindingContext.getPath()}`);
    this.associationtInfoPopover.openBy(sourceControl, false);
    const association = bindingContext.getObject();
    if (association?.source || association?.target) {
      this.associationtInfoPopover.setBusy(true);
      const setLinkText = async (object) => {
        if (object) {
          const linkText = await getLinkText(object);
          const oResource = association.resource;
          if (oResource) {
            oResource.applyUndoableAction(
              function () {
                object.linkText = linkText;
              },
              "Set link text of source or target for popover",
              true
            ); // Protect from Undo!
          } else {
            object.linkText = linkText; // Association is not resolved
          }
        }
      };
      await setLinkText(association.source); // Association.source now supports shared object
      await setLinkText(association.target?.classDefinition ? association.target : association.unresolvedTarget);

      this.associationtInfoPopover.getModel("galileiModel").refresh(true);
      this.associationtInfoPopover.setBusy(false);
    }
    if (association?.target && association.target.name && !association.target.label) {
      this.associationtInfoPopover.setBusy(true);
      getBusinessName(this.spaceName, association.target.name)
        .then((label) => {
          // No additional service call
          const oResource = association.resource;
          if (association.target.label !== label) {
            if (oResource) {
              oResource.applyUndoableAction(
                function () {
                  // Set businessName
                  association.target.label = label;
                },
                "Set businessName for popover",
                true
              ); // Protect from Undo!
            } else {
              // Set businessName
              association.target.label = label; // Association is not resolved
            }
          }

          // Correct displayName if association.target is not galilei object, otherwise it is computed property
          if (!association.target.classDefinition) {
            const entity = this.getView().getModel().getData();
            const model = this.getGalileiModel().getData();
            const objectNameDisplay =
              association.rootContainer?.objectNameDisplay ||
              entity.rootContainer?.objectNameDisplay ||
              entity.resource?.model?.rootContainer?.objectNameDisplay ||
              model?.rootContainer?.objectNameDisplay;
            switch (objectNameDisplay) {
              case ObjectNameDisplay.businessName:
                association.target.displayName = label; // No need to protect from undo since it is not a galilei object
                break;
              default:
                association.target.displayName = association.target.name;
            }
          }
        })
        .finally(() => {
          this.associationtInfoPopover.getModel("galileiModel").refresh(true);
          this.associationtInfoPopover.setBusy(false);
        });
    }
  }

  public async onToggleClick(event) {
    const currentMode = event.getSource().getIcon().split("//")[1];
    const rootModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
    const oModel = rootModel.getData().resource.model;
    const source = event.getSource();
    const bindingContext = source.getBindingContext("galileiModel");
    const object = bindingContext.getObject();
    if (currentMode === "show") {
      let targetFound;
      if (object && object.csn && object.csn.target) {
        const targetObj = {
          name: object.csn.target,
        };
        targetFound = sap.cdw.ermodeler.ModelImpl.checkSymbolExists(targetObj, oModel);
        const objects = await Repo.getModelList(this.spaceName, [
          "name",
          "kind",
          "creation_date",
          "owner",
          "deployment_date",
          "#deploymentExecutionStatus",
          "#objectStatus",
          "releaseStateValue",
          "releaseDate",
          "deprecationDate",
          "decommissioningDate",
          "#writeAccess",
        ]); // { kind: ["entity", "type"] } breaks the CRUD so that need to remove to let it use cache
        let foundObject = objects.filter((obj) => obj.name === targetObj.name);
        if (foundObject.length === 0) {
          const allObjects = await getAllObjectsOfSpace(
            this.spaceName,
            true,
            object.entityName ? [`name:${object.entityName}`] : []
          ); // Optimize shares call to add name filter
          foundObject = allObjects.filter((obj) => {
            const definitions = (obj && obj.content && obj.content.definitions) || obj.definitions;
            if (getEntityNameFromCSN(definitions) === targetObj.name) {
              return obj;
            }
          });
        }
        const selObject = foundObject.length > 0 ? foundObject[0] : undefined;
        if (selObject) {
          if (SupportedFeaturesService.getInstance().isSkylineERModelerEnabled()) {
            const view = this.getView();
            view?.setBusy(true);
            if (!selObject.csn) {
              selObject.csn = { definitions: selObject["definitions"] || selObject["content"]?.definitions };
            }
            object.setResolved(selObject, () => view?.setBusy(false));
          } else {
            const oAssociation = sap.cdw.ermodeler.ModelImpl.processAssociations(rootModel, targetFound, selObject, {
              isUnresolved: true,
              name: object.name,
            });
            rootModel.setProperty("/selectedAssociation", oAssociation);
            this.switchPage(Pages.DetailsPage);
          }
        } else {
          const errorInfoText = this.getI18nText("i18n", "TARGET_NOT_EXIST");
          sap.m.MessageToast.show(errorInfoText);
        }
      }
    }
  }

  public associationFactory(id, context) {
    const newErModel = SupportedFeaturesService.getInstance().isSkylineERModelerEnabled();
    const className = newErModel
      ? context.getProperty("target/qualifiedClassName")
      : context.getProperty("qualifiedClassName");
    let isUnresolved = false;
    const oModelName = context.oModel.getData().resource.model.qualifiedClassName;
    let showToggle = false;

    // let toggleIcon;
    if (className === undefined) {
      isUnresolved = true;
      const csn = context.getProperty("csn");
      const source = context.getProperty("source");
      const sourceName = source.displayName;
      const associationObj = getMappings(csn);
      const targetName = csn.target;
      const associationName = associationObj.name;
      // get path
      const path = context.getPath();
      const val = path.split("/");
      let index;
      if (val.length > 2) {
        index = parseInt(val[2], 10);
      }
      // update target in galileiModel
      const oModel = this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel;
      const allAssociations = oModel.getData().allAssociations;
      const association = allAssociations?.[index];
      if (oModel && association) {
        if (association.crossSpaceName) {
          // Unresolved association and target is shared object
          association.unresolvedTarget = {
            name: association.entityName,
            isCrossSpace: !!association.crossSpaceName,
            crossSpaceName: !!association.crossSpaceName ? association.crossSpaceName : undefined,
            crossSpaceEntityName: !!association.crossSpaceName ? association.entityName : undefined,
            label: association.entityBusinessName,
            displayName: NamingHelper.getDisplayName(association.entityName, association.entityBusinessName),
          };
        } else if (!association.target?.name) {
          association.unresolvedTarget = {
            name: targetName,
            displayName: targetName,
          };
        }
        if (csn["@EndUserText.label"]) {
          association.displayName = csn["@EndUserText.label"];
          association.label = csn["@EndUserText.label"];
        } else {
          const computedDisplayName = this.getI18nText("i18n", "@associationTo", [sourceName, targetName]);
          association.displayName = computedDisplayName;
        }
        association.name = associationName;
      }
    }
    if (oModelName === QualifiedClassNames.ER_MODELER_MODEL && isUnresolved) {
      showToggle = true;
    }
    const editButton = new sap.m.Button({
      icon: "sap-icon://slim-arrow-right",
      tooltip: "{i18n>@editAssociations}",
      type: sap.m.ButtonType.Transparent,
      visible: !isUnresolved,
    });
    editButton.attachPress(this.onAssociationEdit.bind(this));
    const infoButton = new sap.m.Button({
      icon: "sap-icon://sac/info",
      type: sap.m.ButtonType.Transparent,
    });
    infoButton.attachPress(this.openAssociationInfoPopover.bind(this));
    const toggleButton = new sap.m.Button({
      icon: "sap-icon://show",
      tooltip: "{i18n>@showInDiagram}",
      type: sap.m.ButtonType.Transparent,
      visible: showToggle,
    });
    toggleButton.attachPress(this.onToggleClick.bind(this));

    const tokenlistItem = new TokenListItem({
      rightIcon: {
        parts: [
          { path: "galileiModel>isText" },
          { path: "galileiModel>isHierarchy" },
          { path: "galileiModel>isHierarchyWithDirectory" },
        ],
        formatter: function (isText, isHierarchy, isHierarchyWithDirectory) {
          if (isText) {
            return "sap-icon://text";
          } else if (isHierarchy || isHierarchyWithDirectory) {
            return "sap-icon://sac/hierarchy";
          }
        },
      },
      showRightIcon: {
        parts: [
          { path: "galileiModel>isText" },
          { path: "galileiModel>isHierarchy" },
          { path: "galileiModel>isHierarchyWithDirectory" },
          { path: "galileiModel>/" },
        ],
        formatter: function (isText: boolean, isHierarchy: boolean, isHierarchyWithDirectory: boolean, object) {
          return isText === true || ((isHierarchy === true || isHierarchyWithDirectory === true) && object.isDimension);
        },
      },
      icon: "sap-icon://sac/association",
      title: {
        parts: [
          { path: "galileiModel>/" },
          { path: "galileiModel>displayName" },
          { path: "galileiModel>label" },
          { path: "galileiModel>name" },
        ],
        formatter: function (oObject, displayName, label, name) {
          switch (oObject.rootContainer && oObject.rootContainer.objectNameDisplay) {
            case ObjectNameDisplay.businessName:
              return displayName || label || name;
            case ObjectNameDisplay.technicalName:
              return name;
          }
        },
      },
      showDescription: false,
      hideActionButtonsOnMouseOut: true,
      actionButtons: [infoButton, toggleButton, editButton],
    });
    return tokenlistItem;
  }

  public deleteViewDataAccessControlFormatter(oObject, selectedCount, canCreateOrUpdateModel: boolean): boolean {
    if (!canCreateOrUpdateModel) {
      return false;
    }
    if (oObject && oObject.isReadOnlyObject) {
      return false;
    }
    const dacPrivileges = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DAC");
    if (!dacPrivileges.read) {
      return false;
    }
    if (oObject && selectedCount > 0) {
      return true;
    } else {
      return false;
    }
  }

  public addViewDataAccessControlFormatter(oObject, canCreateOrUpdateModel: boolean, parameters: any): boolean {
    if (!canCreateOrUpdateModel) {
      return false;
    }
    if (oObject && oObject.isReadOnlyObject) {
      return false;
    }
    const dacPrivileges = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DAC");
    if (!dacPrivileges.read) {
      return false;
    }
    if (oObject && oObject.classDefinition && oObject.classDefinition.name === "View") {
      return true;
    } else if (oObject && oObject.rootContainer && oObject.rootContainer.output) {
      const count = oObject.rootContainer.output.elementCount;
      if (count > 0) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  public associationDialogTitleFormatter(targetType): string {
    let title = this.getI18nText("i18n", "@selectAssociation");
    switch (targetType) {
      case DataCategory.TEXT:
        title = this.getI18nText("i18n", "@selectTextAssociation");
    }
    return title;
  }

  public addHierarchyAssociationEnableFormatter(allAssociations, isDimension, usage, hierarchies?) {
    return addHierarchyAssociationEnableFormatter(allAssociations, isDimension, usage, hierarchies);
  }

  public addAssociationFormatter(oObject, canCreateOrUpdateModel: boolean, parameters?): boolean {
    if (!canCreateOrUpdateModel) {
      return false;
    }
    if (oObject && oObject.isReadOnlyObject) {
      return false;
    }
    if (
      oObject &&
      oObject.classDefinition &&
      (oObject.classDefinition.name === "Table" || oObject.classDefinition.name === "View")
    ) {
      return true;
    } else if (oObject && oObject.rootContainer && oObject.rootContainer.output) {
      const count = oObject.rootContainer.output.elementCount;
      if (count > 0) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  public deleteAssociationFormatter(oObject, selectedCount, canCreateOrUpdateModel: boolean, parameters?): boolean {
    if (!canCreateOrUpdateModel) {
      return false;
    }
    if (oObject && oObject.isReadOnlyObject) {
      return false;
    }
    if (oObject && selectedCount > 0) {
      return true;
    } else {
      return false;
    }
  }

  public showBreadCrumbsFormatter(oObject) {
    const unresolvedAssociation = oObject && oObject.csn && oObject.csn.type === "cds.Association";
    if (
      (oObject &&
        oObject.classDefinition &&
        (oObject.classDefinition.name === "Association" ||
          oObject.classDefinition.name === "ViewDataAccessControl" ||
          oObject.classDefinition.name === "Element")) ||
      unresolvedAssociation
    ) {
      return true;
    } else {
      return false;
    }
  }

  public aggregationValidationCount(aValidations): string {
    return aggregationValidationCount(aValidations);
  }

  public aggregationValidationType(aValidations): string {
    return aggregationValidationType(aValidations);
  }

  public aggregationValidationTypeCustomStyle(aValidations): string {
    return aggregationValidationTypeCustomStyle(aValidations);
  }

  /**
   * Data type formatter
   * @param {*} sDataType the data type literal
   */
  public dataTypeFormatter(dataType: string): string {
    const nIndex = dataType && dataType.indexOf(".");
    if (dataType && nIndex >= 0) {
      return dataType.substring(nIndex + 1);
    }
    return dataType;
  }

  // This function will be overridden in sap.ui.define
  public onSearch(event: sap.ui.base.Event, table: sap.m.Table): any {
    //
  }

  /**
   * Sets the default width.
   */
  public getWidth(): sap.ui.core.CSSSize {
    return CommonPropertiesClass.smallWidth;
  }

  public onSearchField(event: sap.ui.base.Event) {
    const table: sap.m.Table = this.byId("tableElements") as sap.m.Table;
    this.onSearch(event, table);
  }

  public objectsHeaderTextFormatter(textKey: string, aObjects: any[], filteredObjectsCount?: number) {
    const resourceBundle = this.oResourceModel.getResourceBundle();
    const amount = filteredObjectsCount !== undefined ? filteredObjectsCount : aObjects ? aObjects.length : 0;
    return resourceBundle.getText(textKey, [amount + ""]);
  }

  public contextsHeaderTextFormatter(aObjects: any[], filteredContextsCount?: number) {
    return this.objectsHeaderTextFormatter("@contextsHeaderText", aObjects, filteredContextsCount);
  }

  public simpleTypesHeaderTextFormatter(aObjects: any[], filteredSimpleTypesCount?: number) {
    return this.objectsHeaderTextFormatter("@simpleTypesHeaderText", aObjects, filteredSimpleTypesCount);
  }

  public tablesHeaderTextFormatter(aObjects: any[], filteredTablesCount?: number) {
    return this.objectsHeaderTextFormatter("@tablesHeaderText", this.getUnHiddenTables(aObjects), filteredTablesCount);
  }

  public viewsHeaderTextFormatter(aObjects: any[], filteredViewsCount?: number) {
    return this.objectsHeaderTextFormatter("@viewsHeaderText", aObjects, filteredViewsCount);
  }

  public amountOfColumnsFormatter(aElements: any[]): number {
    return aElements ? aElements.length : 0;
  }

  public amountOfHierarchiesFormatter(allHierarchies: any[]): number {
    return allHierarchies && allHierarchies.length > 0 ? allHierarchies.length : undefined;
  }

  public showAIStatusFormatter(hasAIChange: boolean, object: any): boolean {
    return !!(
      SupportedFeaturesService.getInstance().isGenAISemanticEnabled() &&
      object?.classDefinition?.name === "Output" &&
      hasAIChange
    );
  }

  public isFullScreenFormatter(mode: SidepanelMode): boolean {
    return mode === SidepanelMode.fullScreen;
  }

  public fullScreenIconFormatter(mode: SidepanelMode) {
    // return "sap-icon://" + (mode === SidepanelMode.fullScreen) ? "exit-full-screen" : "full-screen";
    return sap.ui.core.IconPool.getIconURI(mode === SidepanelMode.fullScreen ? "exit-full-screen" : "full-screen");
  }

  public propertyPanelHeaderTextFormatter(sDisplayName: string, oObject: any) {
    let sName = "";
    if (oObject && oObject.isRemote) {
      sName = this.localizeMessage("i18n_erd", "@remoteTable");
    } else if (oObject && (oObject.isView || sap.cdw.commonmodel.ObjectImpl.isView(oObject))) {
      sName = this.localizeMessage("i18n_vb", "modelNameView");
    } else if (sDisplayName === "Table" || sDisplayName === "Entity") {
      sName = this.localizeMessage("i18n_erd", "@localTable");
    } else if (sDisplayName) {
      sName = sDisplayName;
    }
    return sName + " " + this.localizeMessage("i18n_prop", "txtProperties");
  }

  public onChangeStateCDCColumns(entity, state) {
    if (state && entity) {
      entity.isDeltaTable = true;
      entity.deltaTable = { type: "UPSERT" };
      entity.createElement({
        name: "Change_Type",
        label: "Change Type",
        isCDCColumn: true,
        dataType: CDSDataType.STRING,
        length: 1,
        default: "I",
        isNotNull: true,
        isKey: false,
      });
      entity.createElement({
        name: "Change_Date",
        label: "Change Date",
        isCDCColumn: true,
        dataType: CDSDataType.TIMESTAMP,
        default: "CURRENT_UTCTIMESTAMP",
        isNotNull: true,
        isKey: false,
      });
    } else {
      entity.isDeltaTable = false;
      entity.deltaTable = undefined;
    }
  }

  public onChangeOfOutboundColumns(entity, state) {
    if (entity && state) {
      entity.isDeltaOutboundOn = true;
    } else {
      entity.isDeltaOutboundOn = false;
    }
  }

  public selectTableItemFromElement(table: sap.m.Table, model: sap.galilei.ui5.GalileiModel, oElement): void {
    const aItems = table.getItems();
    let oCurrentElement;
    for (const item of aItems) {
      oCurrentElement = model.getProperty(item.getBindingContext().getPath());
      if (oElement === oCurrentElement) {
        table.setSelectedItem(item, true);
        item.focus();
      }
    }
  }

  public onCDCColumnsDelete(selectedElements) {
    const errorList = [];
    selectedElements.forEach((element, index) => {
      if (element?.isCDCColumn) {
        errorList.push({
          column: element.name,
          error: this.localizeMessage("i18n_erd", "@cdcColDelErrorTxt", [element.name]),
        });
      } else if (element?.isPartitionedColumn) {
        errorList.push({
          column: element.name,
          error: this.localizeMessage("i18n_erd", "@partitionColDelErrorTxt", [element.name]),
        });
      }
    });
    const length = errorList.length;
    if (length > 0) {
      selectedElements = selectedElements.filter((el) => el.isCDCColumn !== true && el.isPartitionedColumn !== true);
      let errorMsg = "";
      errorList.forEach((err, index) => {
        if (length > 1) {
          errorMsg += index + 1 + ". ";
        }
        errorMsg += err.error + "\n\n";
      });
      sap.m.MessageBox.error(errorMsg, {
        id: "deltacapturecolDelErrorDialog",
      });
    }
    return selectedElements;
  }

  public onDropElement(event: sap.ui.base.Event): any {
    const oModel: sap.galilei.ui5.GalileiModel = this.getView().getModel() as sap.galilei.ui5.GalileiModel;
    const oDraggedItem = event.getParameter("draggedControl");
    const oDroppedItem = event.getParameter("droppedControl");
    const oDraggedElement = oDraggedItem && oModel.getProperty(oDraggedItem.getBindingContextPath());
    const oDroppedElement = oDroppedItem && oModel.getProperty(oDroppedItem.getBindingContextPath());

    if (
      oDraggedElement &&
      oDroppedElement &&
      oDraggedElement.container === oDroppedElement.container &&
      oDraggedElement.container
    ) {
      sap.cdw.querybuilder.NodeImpl.changeElementIndexOrder(
        oDraggedElement.container,
        oDraggedElement,
        oDraggedElement.indexOrder,
        oDroppedElement.indexOrder
      );

      if (oDraggedItem && oDraggedItem.getTable) {
        // drag & drop inside same table -> select the dragged item
        const oTable = oDraggedItem.getTable();
        setTimeout(() => {
          this.selectTableItemFromElement(oTable, oModel, oDraggedElement);
        }, 200);
      }
    }
    oModel.refresh(false);
    return oDraggedElement;
  }

  public tableTitleFormatter(sName, aElements): string {
    const count = aElements ? aElements.length : 0;
    return sName + " (" + count + ")";
  }

  public linkObjectSpaceNameFormatter(crossSpaceName?: string): string {
    return crossSpaceName || this.getSpaceName();
  }

  public async tableBTitleFormatter(sName, aElements, tableB?): Promise<string> {
    await this.tableBLabelFormatter(undefined, tableB); // change label to compute new displayName
    const count = aElements ? aElements.length : 0;
    return (tableB.alias || tableB.displayName || sName) + " (" + count + ")"; // alias in GV
  }

  public async tableBLabelFormatter(label, tableB): Promise<string> {
    if (tableB) {
      let label = tableB.label;
      if (tableB.classDefinition.name === "Association" && tableB.name) {
        label = await getBusinessName(this.getSpaceName(), tableB.name); // Association is not solved really, need get business name, but actually no additional service call
        const oResource = tableB.resource;
        if (oResource && tableB.label !== label) {
          oResource.applyUndoableAction(
            function () {
              // Set businessName
              tableB.label = label;
            },
            "Set businessName for tableB",
            true
          ); // Protect from Undo!
        }
      }
      return label;
    }
    return undefined;
  }

  public onDefineFilterPressed(event: IEvent<sap.m.MenuItem, {}>) {
    const objectId = event.getSource().data("objectId");
    const element = (this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel)
      .getData()
      .resource.getObject(objectId);
    if (element && (element.filterSelectionType === "" || typeof element.filterSelectionType === "undefined")) {
      element.filterSelectionType = FilterSelectionTypes.Single;
    }
    this.switchPage(Pages.FilterColumnPage);
    this.setFilterColumnModel(element);
    this.setHeaderModel(element);
  }

  public onViewFilterPressed(event: IEvent<sap.m.MenuItem, {}>) {
    const sourceControl = event.getSource();

    if (!this.viewFilterInfoPopover) {
      require("./ViewFilterInfoPopover.fragment.xml");
      this.viewFilterInfoPopover = sap.ui.xmlfragment(
        this.createId("ViewFilterInfoPopover"),
        "sap.cdw.components.ermodeler.properties.ViewFilterInfoPopover",
        this
      ) as sap.m.Popover;
      this.getView().addDependent(this.viewFilterInfoPopover);
    }
    const bindingContext = sourceControl.getBindingContext("galileiModel");
    this.viewFilterInfoPopover.bindElement(`galileiModel>${bindingContext.getPath()}`);
    this.viewFilterInfoPopover.openBy(sourceControl, false);
  }

  public visibleFilterAttributes(filterSelectionType, isAllowConsumption?): boolean {
    return (
      filterSelectionType !== "" &&
      this.isViewFilterEnabled() &&
      (typeof isAllowConsumption === "undefined" || isAllowConsumption === true)
    );
  }

  public isViewFilterEnabled(object?): boolean {
    return !object || (object && object.isAllowConsumption);
  }

  /**
   * Triggered when header validation button is clicked
   */
  public onShowValidations(oEvent): void {
    const oControl = oEvent.getParameter("oSource") || oEvent.getSource();
    const object = this.getView().getModel("header").getProperty("/");
    openDatabuilderValidationsPopoverBy(object, oControl, this.getI18nResources());
  }

  public getI18nResources(): any {
    const oResourceModel = this.getView().getModel("i18n");
    const aResourceModels = [];
    aResourceModels.push({
      model: oResourceModel,
      name: "i18n",
    });
    aResourceModels.push({
      model: this.getView().getModel("i18n_erd"),
      name: "i18n_erd",
    });
    const workbenchController = getWorkbenchController();
    if (workbenchController?.isSecondaryEditorActive && workbenchController.secondaryEditor) {
      // I'm used as secondary editor then extend side panel i18n models from editor extensions
      const extensions = workbenchController.secondaryEditor.getEditorExtensions();
      const i18nModels = extensions?.i18nSidePanelExtensions;
      i18nModels?.forEach((ext) => {
        aResourceModels.push(ext);
      });
    }
    return aResourceModels;
  }

  public async linkTextFormatter(object) {
    return await getLinkText(object);
  }

  public showDacTextFormatter(currentObject) {
    return !!(currentObject?.classDefinition?.name === "Output");
  }

  public showDacLinkFormatter(currentObject) {
    return !this.showDacTextFormatter(currentObject);
  }

  public showSourceTextFormatter(currentObject, leftObject) {
    return NamingHelper.showLeftTextFormatter(currentObject, leftObject, true);
  }

  public showSourceLinkFormatter(currentObject, leftObject) {
    return NamingHelper.showLeftLinkFormatter(currentObject, leftObject);
  }

  public showTargetLinkFormatter(currentObject, rightObject) {
    return NamingHelper.showRightLinkFormatter(currentObject, rightObject);
  }

  public showTargetTextFormatter(currentObject, rightObject) {
    return NamingHelper.showRightTextFormatter(currentObject, rightObject);
  }

  public sortIconFormatter(sortDisabled, sortAscending) {
    if (sortDisabled === undefined && sortAscending === undefined) {
      return "sap-icon://sort";
    }
    if (sortDisabled) {
      return "sap-icon://sort";
    } else if (sortAscending) {
      return "sap-icon://sort-ascending";
    }
    return "sap-icon://sort-descending";
  }

  public sortTooltipFormatter(sortDisabled, sortAscending) {
    if (sortDisabled === undefined && sortAscending === undefined) {
      return this.localizeMessage("i18n_erd", "txtNoSort");
    }
    if (sortDisabled) {
      return this.localizeMessage("i18n_erd", "txtNoSort");
    } else if (sortAscending) {
      return this.localizeMessage("i18n_erd", "txtSortAscending");
    }
    return this.localizeMessage("i18n_erd", "txtSortDescending");
  }

  // __DP__ Analitical Measure views
  public onAddAnalyticMeasure(amId: string): void {
    const oAMParams = AnalyticMeasureHelper.getAMParams(amId);
    if (!oAMParams) {
      MessageHandler.warning(
        "The feature " + amId + " is Under construction, please come back later!",
        "Under Construction",
        5000
      );
      return;
    }
    let oAnalyticMeasure;

    const model: sap.galilei.ui5.GalileiModel = this.getView().getModel("galileiModel") as sap.galilei.ui5.GalileiModel;
    const table = this.byId("analyticalMeasuresList") as sap.m.List;
    const oSelectedItem = table && table.getSelectedItem();
    const bindingPath: string =
      oSelectedItem && oSelectedItem.getBindingContext() && oSelectedItem.getBindingContext().getPath();
    const oSelectedElement = bindingPath && model.getProperty(bindingPath);
    const nIndexOrder = oSelectedElement && oSelectedElement.indexOrder >= 0 ? oSelectedElement.indexOrder : undefined;
    const oOutput = model.getProperty("/");
    const oResource = oOutput.resource;

    oResource.applyUndoableAction(function () {
      oAnalyticMeasure = oAMParams.fAMcreateNew(oOutput);
      if (nIndexOrder !== undefined) {
        sap.cdw.querybuilder.NodeImpl.changeElementIndexOrder(
          oOutput,
          oAnalyticMeasure,
          oAnalyticMeasure.indexOrder,
          nIndexOrder
        );
      }
    });
    model.setProperty(oAMParams.sAMpropertyName, oAnalyticMeasure);
    this.switchPage(oAMParams.sAMDetailsPage);
    this.setHeaderModel(oAnalyticMeasure);
    this.setAnalyticMeasureModel(oAnalyticMeasure, oAMParams.sAMviewId);

    // Record usage
    ShellContainer.get().getUsageCollectionService().recordAction({
      action: oAMParams.sAMusageAction,
      feature: DWCFeature.DATA_BUILDER,
      eventtype: EventType.CLICK,
    });
  }

  public onChangeChildElement(event: IEvent<sap.m.Select, { selectedItem: sap.ui.core.Item }>) {
    const entity = (this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel).getData();
    const model = entity.resource.model;
    const selectedItem = event.getParameter("selectedItem");

    const originalChildElement = model.resource.getObject(selectedItem.data("objectId"));
    const sItemId = event.getSource().getId();
    const sItemIndex = sItemId.charAt(sItemId.length - 1);
    const hierarchy = entity.hierarchies?.get(0);
    model.resource.applyUndoableAction(function () {
      const childEle = hierarchy?.childElement?.get(Number(sItemIndex));
      childEle && hierarchy?.childElement?.removeAt(Number(sItemIndex));
      hierarchy?.childElement?.insert(Number(sItemIndex), originalChildElement);
    }, "Change Hierarchy Child Element");
    const parentChildModel = this.getParentChildModel(hierarchy);
    HierarchyValidation.validateHierarchyModel(parentChildModel);
    this.getView().getModel("hierarchyUiModel").setProperty("/parentElements", parentChildModel);
    this.getView().getModel("hierarchyUiModel").setProperty("/childElements", parentChildModel);
  }

  public onChangeParentElement(event: IEvent<sap.m.Select, { selectedItem: sap.ui.core.Item }>) {
    const entity = (this.getView().getModel("galileiModel") as sap.ui.model.json.JSONModel).getData();
    const model = entity.resource.model;
    const selectedItem = event.getParameter("selectedItem");
    const originalParentElement = model.resource.getObject(selectedItem.data("objectId"));
    const sItemId = event.getSource().getId();
    const sItemIndex = sItemId.charAt(sItemId.length - 1);
    const hierarchy = entity.hierarchies?.get(0);
    model.resource.applyUndoableAction(function () {
      const parentEle = hierarchy?.parentElement?.get(Number(sItemIndex));
      parentEle && hierarchy?.parentElement?.removeAt(Number(sItemIndex));
      hierarchy?.parentElement?.insert(Number(sItemIndex), originalParentElement);
    }, "Change Hierarchy Parent Element");

    const parentChildModel = this.getParentChildModel(hierarchy);
    HierarchyValidation.validateHierarchyModel(parentChildModel);
    this.getView().getModel("hierarchyUiModel").setProperty("/parentElements", parentChildModel);
    this.getView().getModel("hierarchyUiModel").setProperty("/childElements", parentChildModel);
  }

  public hideColumnVisible(dataCategory): boolean {
    if (
      dataCategory === DataCategory.FACT ||
      dataCategory === DataCategory.SQLFACT ||
      dataCategory === DataCategory.DIMENSION
    ) {
      return true;
    } else {
      return false;
    }
  }

  public hideColumnVisibleForAD(dataCategory): boolean {
    // "Show/Hide in Story" is only available for ADS but not Fact
    // Details in: https://jira.tools.sap/browse/DW00-6729
    if (dataCategory === DataCategory.FACT || dataCategory === DataCategory.DIMENSION) {
      return true;
    } else {
      return false;
    }
  }

  public hideColumnAnalyticVisible(dataCategory): boolean {
    return false;
  }

  public hideColumnHiddenIcon(isVisible): boolean {
    return isVisible === false;
  }

  // TODO: need to change this based on which node we are viewing info popover
  public showColumnIfHidden(isVisible): boolean {
    return true;
  }

  public hideColumnVisibleText(oldText, newText): boolean {
    return newText;
  }

  public hideColumnVisibleTextMultiple(isVisible, oldText1, oldText2, newText1, newText2): any {
    return toggleLabel(isVisible, newText1, newText2);
  }

  protected getColumnSiblings(nodeOrEntity) {
    let siblings = [];
    if (nodeOrEntity?.orderedElements) {
      siblings = nodeOrEntity.orderedElements.concat(nodeOrEntity.allAssociations);
    }
    return siblings;
  }

  /* ER IMPL FIXME WHY ANALATYC MEASURES?
  export function getColumnSiblings(object) {
    let siblings = [];
    if (object?.orderedElements) {
      siblings = object.orderedElements.concat(object.allAssociations);
    }
    if (object.resource?.model?.output?.analyticMeasureElements?.length) {
      siblings = [...siblings, ...object.resource.model.output.analyticMeasureElements.toArray()];
    }

    return siblings;
  }
  */

  protected getEditableStates(hasPrivileges: boolean, isRemote: boolean, isView: boolean) {
    return {
      hasPrivileges: hasPrivileges,
      name: hasPrivileges && !isRemote,
      businessProps: hasPrivileges,
      localTableProps: hasPrivileges && !isRemote && !isView,
    };
  }

  protected disableGalileiModels() {
    const model = this.getView().getModel() as sap.galilei.ui5.GalileiModel;
    const galileiModel = this.getView().getModel("galileiModel") as sap.galilei.ui5.GalileiModel;
    const headerModel = this.getView().getModel("header") as sap.galilei.ui5.GalileiModel;

    model?.setData({});
    galileiModel?.setData({});
    headerModel?.setData({});
  }

  protected getHasPrivileges(): boolean {
    // Check the feature flag
    let hasPrivileges: boolean;
    const workbenchEnvModel = this.getView().getModel("workbenchEnv");
    if (workbenchEnvModel) {
      hasPrivileges = workbenchEnvModel.getProperty("/canCreateOrUpdateModel");
    } else {
      const privileges = (sap.ui.getCore().getModel("privilege") as sap.ui.model.json.JSONModel).getData()
        .DWC_DATABUILDER;
      hasPrivileges = privileges.create || privileges.update;
    }
    return hasPrivileges;
  }

  public getUnHiddenTables(tables: sap.cdw.commonmodel.Table[]): sap.cdw.commonmodel.Table[] {
    return tables?.filter((t) => !t.isToolingHidden);
  }
  private updateHierarchyValidation() {
    const hierarchyModel = this.getView().getModel("hierarchyUiModel");
    const parentChildModel = hierarchyModel && hierarchyModel.getProperty("/parentElements");
    if (parentChildModel) {
      HierarchyValidation.validateHierarchyModel(parentChildModel);
      this.getView().getModel("hierarchyUiModel").setProperty("/parentElements", parentChildModel);
      this.getView().getModel("hierarchyUiModel").setProperty("/childElements", parentChildModel);
    }
  }

  protected async parameterValueHelpStart(oEvent) {
    let model = oEvent.getSource()?.getBindingContext("viewData")
      ? oEvent.getSource()?.getBindingContext("viewData")
      : oEvent.getSource().getBindingContext("paramModel");
    let source = oEvent.getSource();
    const bPreview = oEvent.getSource()?.getBindingContext("viewData")
      ? "Preview"
      : oEvent.getSource().getBindingContext("paramModel")
      ? "Mapping"
      : "SetValue";
    let bindingContextObject = model?.getObject();
    if (!bindingContextObject) {
      model = this["defaultDialog"];
      bindingContextObject = this["defaultDialog"].getModel().getData();
    }
    let space = this.getSpaceName();
    const dataTypeOfColumnToFetch = bindingContextObject?.primitiveDataType;
    const entity = bindingContextObject?.valueHelpDefinition?.entityName;
    const entityNameSplitArray = entity?.split(".");
    let finalEntityName = entity;
    if (
      entityNameSplitArray[0] !== "SAP" &&
      entityNameSplitArray[1] &&
      !(entityNameSplitArray.length === 3 && entityNameSplitArray[0] === "Remote") &&
      entityNameSplitArray[0] !== space
    ) {
      // Check if the entity is saved in current space first, if not, then it is a shared object
      const csnObject = SupportedFeaturesService.getInstance().isDotSupportEnabledFF()
        ? await Repo.getModelDetails(space, entity, ["name"])
        : { name: entity };
      if (!csnObject?.name) {
        space = entityNameSplitArray[0];
        // to handle shared space objects with dots, ex- time dimension tables
        finalEntityName = entityNameSplitArray.slice(1, entityNameSplitArray.length + 1).join(".");
      }
    }
    const element = bindingContextObject?.valueHelpDefinition?.elementName;
    if (element) {
      sap.ui.require(
        ["sap/ui/table/library", "sap/skyline/tools/valuehelp/ValueHelpDialog"],
        async function (sapUiTableLibrary, ValueHelpDialog) {
          const dialog = new ValueHelpDialog({
            title: "Value Help Dialog",
            selectionMode: sapUiTableLibrary.SelectionMode.Single,
            useAuxiliaryColumn: false,
            config: {
              columnName: element,
              mode: "Single",
              maxValues: 1,
              operator: "=",
              csn: {
                something: "",
              },
              parameterDialog: this,
            },
            onSubmitSelection: (event: sap.ui.base.Event) => {
              dialog.close();
              const selectedValues = event.getParameter("values");
              switch (bPreview) {
                case "Preview":
                  bindingContextObject.defaultForDataPreview = selectedValues[0]?.value;
                  source.fireChangeEvent();
                  break;
                case "Mapping":
                  bindingContextObject.value = selectedValues[0]?.value;
                  break;
                case "SetValue":
                  bindingContextObject.value = selectedValues[0]?.value;
                  model.inputValue = selectedValues[0]?.value;
                  break;
              }
              model.getModel()?.refresh(true);
              // parameterDialog.getView().byId("valueHelpInputParam").setValue(bindingContextObject.defaultForDataPreview);
            },
            valuesGetter: async (dialog, searchValue) => {
              const config = dialog.getConfig();
              dialog.selectionMode = config.mode;
              dialog.setContentHeight("70%");
              dialog.setContentWidth("50%");
              let filter = [];
              if (searchValue !== "") {
                let filterOption;
                if (dataTypeOfColumnToFetch === "cds.String") {
                  filterOption = "icp";
                } else {
                  filterOption = "eq";
                }
                filter = [
                  {
                    fieldName: element,
                    conditions: [
                      {
                        sign: "I",
                        option: filterOption,
                        low: searchValue,
                      },
                    ],
                  },
                ];
              }
              const result = await valueHelpService(
                [element],
                filter,
                undefined,
                space,
                finalEntityName,
                /* distinct*/ true
              );
              let values = [];
              for (const item of result.value) {
                let updatedVal = item[element];
                if (isNull(updatedVal)) {
                  updatedVal = "NULL";
                } else if (updatedVal?.toString().trim() === "" || updatedVal === undefined) {
                  updatedVal = "<empty>";
                }
                const obj = { key: updatedVal, value: item[element] };
                values.push(obj);
              }
              return values;
            },
          } as any);
          dialog.open();
        }
      );
    }
  }
}

export const CommonProperties = smartExtend(
  AbstractController,
  "sap.cdw.components.ermodeler.properties.CommonProperties",
  CommonPropertiesClass
);

sap.ui.define(
  "sap/cdw/components/ermodeler/properties/CommonProperties.controller",
  ["sap/ui/model/Filter", "sap/m/MessageToast"],
  function (Filter, MessageToast) {
    CommonProperties.prototype.setDefaultPropertiesWidth();

    // We need to define those functions as a prototype, because they have UI5 dependencies
    CommonProperties.prototype.defaultOnSortTable = function (event: sap.ui.base.Event): object {
      const model: sap.galilei.ui5.GalileiModel = this.getView().getModel();
      const oButton = event.getSource() as sap.m.Button;
      const tableId: string = oButton.data("tableId");
      const oTable = this.getView().byId(tableId);
      const oBinding = oTable.getBinding("items");
      const aSorters = oBinding.aSorters;
      const sortDisabledProperty = `/sortDisabled_${tableId}`;
      const sortAscendingProperty = `/sortAscending_${tableId}`;
      let mode: string;

      if (aSorters && aSorters.length > 0 && aSorters[0].bDescending) {
        oBinding.sort([new sap.ui.model.Sorter("displayName", false)]);
        mode = this.localizeMessage("i18n_erd", "txtColumnNameAsc");
        model.setProperty(sortDisabledProperty, false);
        model.setProperty(sortAscendingProperty, true);
      } else if (aSorters && aSorters.length > 0 && !aSorters[0].bDescending) {
        mode = this.localizeMessage("i18n_erd", "txtSortDisabled");
        oBinding.sort(); // new sap.ui.model.Sorter("newName", false, /**group */undefined)
        oTable.getModel().refresh(true);
        model.setProperty(sortDisabledProperty, true);
        model.setProperty(sortAscendingProperty, false);
      } else {
        mode = this.localizeMessage("i18n_erd", "txtColumnNameDesc");
        oBinding.sort([new sap.ui.model.Sorter("displayName", true)]);
        model.setProperty(sortDisabledProperty, false);
        model.setProperty(sortAscendingProperty, false);
      }
      MessageToast.show(mode);
      return {
        tableId: tableId,
        mode: mode,
      };
    };

    CommonProperties.prototype["onChangeTypeCheck"] = function (
      event: IEvent<sap.m.Select, { selectedItem: sap.ui.core.Item }>
    ): void {
      const newValue = (event.getParameter("selectedItem")?.getKey?.() ||
        event.getSource?.()?.getSelectedKey?.()) as DataCategory;
      const entity = this.getGalileiModel().getData();
      const galileiModel = this.getGalileiModel();
      // ADS -> Fact, we should popup a dialog to let user know that ADS to Fact will remove story filter information
      // and show/hide in story information
      const oldValue = entity.dataCategory;
      if (oldValue === DataCategory.FACT && newValue === DataCategory.SQLFACT) {
        const promise = commonUtils.blockedConfirmDialog(
          this.localizeMessage("i18n_commonmodel", "confirmADSToFact"),
          "adsToFactConfirmDialog"
        );
        const self = this;
        promise.then((message) => {
          if (message === "cancel") {
            const selectControl = event.getSource();
            // set semantic type selected item back to ADS and do not proceed coming steps
            selectControl.setSelectedKey(DataCategory.FACT);
          } else {
            self.onChangeType(event);
          }
        });
      } else {
        if (
          SupportedFeaturesService.getInstance().isFiscalTimeDimensionEnabled() &&
          oldValue === DataCategory.DIMENSION
        ) {
          // clear dimensionType and fiscalTimeSettings when change from Dimension to others
          galileiModel.setProperty("/dimensionType", DimensionType.STANDARD);
          this.fiscalTimeSettingsDialog?.clearSelection();
          galileiModel.setProperty("/fiscalTimeSettingsStart", undefined);
          galileiModel.setProperty("/fiscalTimeSettingsEnd", undefined);
        }
        this.onChangeType(event);
      }
    };

    CommonProperties.prototype["onRevertDataCategoryAIChange"] = function (
      event: IEvent<sap.m.Select, { selectedItem: sap.ui.core.Item }>
    ): void {
      const entity = this.getGalileiModel().getData();
      const isView = entity.classDefinition.name === "Output";
      if (isView) {
        // Record usage
        ShellContainer.get().getUsageCollectionService().recordAction({
          action: DataBuilderAction.RevertGenAISemanticOnView,
          feature: DWCFeature.DATA_BUILDER,
          eventtype: EventType.CLICK,
        });
      }
      revertDataCategoryAIChange(entity);
    };

    CommonProperties.prototype.onChangeType = function (
      event: IEvent<sap.m.Select, { selectedItem: sap.ui.core.Item }>
    ): void {
      const dataCategory = (event.getParameter("selectedItem")?.getKey?.() ||
        event.getSource?.()?.getSelectedKey?.()) as DataCategory;
      const entity = this.getGalileiModel().getData();
      const model = entity.rootContainer;
      const galileiModel = this.getGalileiModel();
      model?.resource?.applyUndoableAction(function () {
        entity.isDataCategoryChanged = true;
        if (typeof entity.setDataCategory === "function") {
          if (SupportedFeaturesService.getInstance().isFiscalTimeDimensionEnabled()) {
            const prevValue = entity.dataCategory;
            if (prevValue === DataCategory.DIMENSION) {
              this.fiscalTimeSettingsDialog = sap.ui.getCore().byId("ftDialog");
              // clear dimensionType and fiscalTimeSettings when change from Dimension to others
              galileiModel.setProperty("/dimensionType", DimensionType.STANDARD);
              this.fiscalTimeSettingsDialog?.clearSelection();
              galileiModel.setProperty("/fiscalTimeSettingsStart", undefined);
              galileiModel.setProperty("/fiscalTimeSettingsEnd", undefined);
            }
          }
          if (dataCategory === DataCategory.FACT || dataCategory === DataCategory.SQLFACT) {
            // Clean Text Associations if switch from dimension to ADS/Fact
            const oldValue = entity.dataCategory;
            if (oldValue === DataCategory.DIMENSION) {
              const nsLocal = sap.cdw.commonmodel;
              nsLocal.ObjectImpl.removeTextAssociationFromElement(entity);
            }
          }

          entity.setDataCategory(dataCategory);
          if (dataCategory === DataCategory.HIERARCHY) {
            const hierarchy = entity.hierarchies?.get(0);
            if (entity.dimensionElements && entity.dimensionElements.length) {
              const childElement = entity.dimensionElements.find((e) => e.isKey === true);
              let parentElement;
              if (childElement) {
                parentElement = entity.dimensionElements.find(
                  (e) => e.name !== childElement.name && e.dataType === childElement.dataType
                );
              }
              const firstElement = entity.dimensionElements[0];
              if (hierarchy) {
                const parentEle = parentElement || firstElement;
                parentEle && hierarchy.parentElement.push(parentEle);
                const childEle = childElement || firstElement;
                childEle && hierarchy.childElement.push(childEle);
              } else {
                if (!hierarchy.parentElement.get(0)) {
                  hierarchy.parentElement.push(parentElement || firstElement);
                }
                if (!hierarchy.childElement.get(0)) {
                  hierarchy.childElement.push(childElement || firstElement);
                }
              }
            }
          }
        }

        if (
          model?.qualifiedClassName === "sap.cdw.ermodeler.Model" &&
          model.associations.toArray().some((a) => a?.target === entity)
        ) {
          // On ER modeler, it may change the target entity's data catalog, so need to update annotations on source entity and validate the related associations
          model.associations.toArray().forEach((a) => {
            if (a?.source && a.target === entity) {
              // Update annotation
              updateAnnotationByAssociation(a);
            }
          });
        }
      }, "Add Hierarchy Parent and Child Element");
      if (dataCategory === DataCategory.HIERARCHY) {
        const hierarchy = entity.hierarchies?.get(0);
        const parentChildModel = this.getParentChildModel(hierarchy);
        HierarchyValidation.validateHierarchyModel(parentChildModel);
        this.getView().getModel("hierarchyUiModel")?.setProperty("/parentElements", parentChildModel);
        this.getView().getModel("hierarchyUiModel")?.setProperty("/childElements", parentChildModel);
        if (parentChildModel?.length <= 1) {
          this.getView().getModel("hierarchyUiModel")?.setProperty("/listItemDltMode", undefined);
        } else {
          this.getView().getModel("hierarchyUiModel")?.setProperty("/listItemDltMode", "Delete");
        }
      }
      this.setupParentChildHierarchyUI();
      this.setupCompoundKeyUI();
      this.setupHierarchyWithDirectoryUI();

      this.getDatabuilderWorkbench()?.getToolbarModel().setProperty("/dataCategory", dataCategory);
    };
    CommonProperties.prototype.onSearch = function (event: sap.ui.base.Event, table: sap.m.Table) {
      // add filter for search
      const aFilters = [];
      const query: string = (event.getSource() as sap.m.SearchField).getValue();
      if (query && query.length > 0) {
        const filter = new Filter("displayName", sap.ui.model.FilterOperator.Contains, query);
        aFilters.push(filter);
      }

      // update list binding
      const oBinding: sap.galilei.ui5.GalileiListBinding = table.getBinding(
        "items"
      ) as unknown as sap.galilei.ui5.GalileiListBinding;
      oBinding.filter(aFilters, "Application");
    };
  }
);

sap.ui.model.SimpleType.extend("UpperCaseAlphaNumeric", {
  // Converts from model to UI representation (here from string to boolean)
  formatValue: function (sValue, sInternalType) {
    return sValue;
  },

  // Converts from UI to model representation
  parseValue: function (sValue, sInteralType) {
    return sValue && sValue.replace ? sValue.replace(/[^\w]/gi, "").toUpperCase() : "";
  },

  // Checks whether the UI value is valid - doesn"t make sense for boolean type
  validateValue: function (bValue) {
    //
  },
});
