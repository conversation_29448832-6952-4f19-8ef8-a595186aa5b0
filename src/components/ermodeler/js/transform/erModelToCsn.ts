/**
 * eslint-disable id-blacklist
 *
 * @format
 */

/** Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved. */
import { SupportedFeaturesService } from "../../../commonmodel/api/SupportedFeaturesService";
import { CsnPropertyMappings } from "../../../commonmodel/csn/csnProperyMappings";
import { isORDTable } from "../../../commonmodel/utility/CommonUtils";
import { GModelHandler } from "../../../commonmodel/utility/GModelHandler";

/**
 * CSN generator for ER model
 */
sap.galilei.namespace("sap.cdw.ermodeler", function (nsLocal) {
  /**
   * @class
   * ErModelToCsn implements the conversion from UI model to CSN
   */
  nsLocal.ErModelToCsn = sap.galilei.core.defineClass({
    fullClassName: "sap.cdw.ermodeler.ErModelToCsn",

    parent: sap.cdw.commonmodel.ModelToCsn,

    fields: {
      UNIQUE_CSN_RESOURCE_ID: nsLocal.ModelImpl.UNIQUE_ERD_RESOURCE_ID,

      // Use the default common model to CSN conversion mappings.
      CSN_PROP_MAPPING: CsnPropertyMappings,
    },

    properties: {
      CSN_CREATOR: {
        get: function () {
          return nsLocal.ErModelToCsn.ERMODELER_CSN_CREATOR;
        },
      },

      CSN_KIND: {
        get: function () {
          return nsLocal.ErModelToCsn.ERMODELER_CSN_KIND;
        },
      },
    },

    statics: {
      ERMODELER_CSN_CREATOR: "ER Modeler",

      ERMODELER_CSN_KIND: "sap.dwc.ermodel",

      ERMODELER_DIAGRAM_KIND: "sap.dwc.erdiagram",

      /**
       * Gets an instance of ErModelToCsn.
       */
      getInstance: function () {
        if (!nsLocal.ErModelToCsn.instance) {
          nsLocal.ErModelToCsn.instance = new nsLocal.ErModelToCsn();
        }
        return nsLocal.ErModelToCsn.instance;
      },

      /**
       * Promise based generate CSN function.
       * Gets the query builder CSN object and convert expressions.
       * @param {string} sName? The name of model.
       * @param {Model} oModel? The model.
       * @param {Object} oOptions? The options.
       *     rootObject: The root object o use.
       *     creator: The creator.
       *     documentKind: Document kind
       *     serializeModel: Add serialized Gaiilei model in CSN.
       *     modelKind: Galilei model kind
       */
      getCSN: async function (sName, oModel, oOptions): Promise<any> {
        const isORDInERMEnabled = SupportedFeaturesService.getInstance().isORDInERMEnabled();
        const oInstance = nsLocal.ErModelToCsn.getInstance();

        oModel = oModel || oInstance.getModel.call(oInstance, oModel);
        oOptions = oOptions || {};
        oOptions.deploymentHints = isORDInERMEnabled;
        return Promise.resolve(oInstance.getValue(sName, oModel, oOptions));
      },
    },

    methods: {
      /**
       * Serializes galilei model.
       * @param {*} oModel
       */
      serializeModel: function (oModel) {
        const oWriter = new sap.galilei.model.JSONWriter();

        return oWriter.save(oModel.resource, {
          includeVolatileObjects: false,
          isSaveDefaultValue: false,
        });
      },

      /**
       * Adds the objects dependencies section in the CSN. It can be overridden.
       * @param oCsnDocument
       * @param model
       */
      addObjects: function (oCsnDocument: any, model: sap.cdw.commonmodel.Model, name: string, options: any = {}) {
        if (oCsnDocument && model) {
          // Define diagram
          const oRootObject = options.rootObject;
          const bSerializeModel = options.serializeModel !== undefined ? options.serializeModel : !oRootObject;

          // Save Galilei model in objects
          if (bSerializeModel) {
            GModelHandler.writeERGalileiModel(oCsnDocument, name, this.serializeModel(model), model as any);
          }
        }
      },

      /**
       * Adds extensions in the CSN. It can be overridden.
       * @param oCsnDocument
       * @param model
       */
      addExtensions: function (oCsnDocument: any, model: sap.cdw.commonmodel.Model, name: string, options: any = {}) {
        this.parent.addExtensions.call(this, oCsnDocument, model, name, options);

        if (oCsnDocument && model) {
          // Adds entities
          for (const entity of model.entities.toArray()) {
            if ((entity as any).revertLastVersion) {
              oCsnDocument.extensions = oCsnDocument.extensions || {};
              const oCsnExtension = {
                revertLastVersion: true,
              };
              oCsnDocument.extensions[entity.qualifiedName] = oCsnExtension;
            }
          }
        }
      },

      /**
       * Adds the objects' ORD info into deploymentHints section in the CSN.
       * @param oCsnDeploymentHints
       * @param entities
       * @param oOptions
       */
      addOrdEntities: function (
        oCsnDeploymentHints: any,
        entities: sap.galilei.model.BaseCollection<sap.cdw.commonmodel.Entity> | sap.cdw.commonmodel.Entity[],
        oOptions?
      ) {
        const isORDInERMEnabled = SupportedFeaturesService.getInstance().isORDInERMEnabled();
        if (isORDInERMEnabled && oCsnDeploymentHints && entities) {
          entities = entities instanceof Array ? entities : entities.toArray();
          for (const entity of entities) {
            // Only add local table to csn deploymentHints
            if (isORDTable(entity)) {
              this.addOrdEntity(oCsnDeploymentHints, entity, oOptions);
            }
          }
        }
      },

      /**
       * Adds ord info of one local table in the CSN deploymentHints.
       * @param oCsnDeploymentHints
       * @param table
       * @param oOptions
       */
      addOrdEntity: function (oCsnDeploymentHints: any, table, oOptions?) {
        if (oCsnDeploymentHints && table && table.connection !== "" && table.ordId !== "") {
          const oCsnEntity = {
            connection: table.connection,
            ord: {
              eventResources: [
                {
                  ordId: table.ordId,
                  apiProtocol: "asyncapi-v2", // fix values
                  url: "", // Mo: not required for the version.
                  supportedUseCases: [], // "mass-extract" for CDI & Co; here just empty
                  selectors: [
                    {
                      type: "asyncapi-v2", // fix values
                      path: "", // Mo: not required for the version.
                    },
                  ],
                },
              ],
            },
          };

          const sQualifiedName = table.qualifiedName;
          oCsnDeploymentHints[sQualifiedName] = oCsnEntity;
        }
      },
    },
  });
});
