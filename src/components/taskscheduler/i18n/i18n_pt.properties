#XMSG: Message to ask user to authorize
authorizeWarn=Dê sua autorização para executarmos as tarefas recorrentes que você programou.
#XFLD: Authorize field value
authorise=Autorizar
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=Programação para "{0}"
#XMSG: Ok message
ok=OK
#XMSG: Schedule
schedule=Programação
#XMSG: Cancel message
cancel=Cancelar
#XMSG: Close message
close=Fechar
#XFLD: lable to set recurrence
recurrenceLabel=Recorrência
#XFLD: label to set the recurrence value
everyLabel=A cada
#XFLD: Start date field
startDateLabel=Data de início
#XFLD: Start date field
endDateLabel=Data de término
#XFLD: TimeZone field
timeZoneLabel=Fuso horário
#XMSG: validation text
recurrenceValidationText1=Informe o valor inteiro 1 ou mais alto
#XMSG: validation text
recurrenceValidationText2=Informe uma data de início
#XMSG: validation text
recurrenceValidationText3=Informe uma data de início
#XMSG: validation text
noObjectSelectedText=Selecione uma tabela remota para programar.
#XMSG: error text
errorLabel=Erro
#XMSG: Schedule created alert message
createScheduleSuccess=Programação criada
#XMSG: Error in creating schedule
errorCreateSchedule=Não foi possível criar a programação neste momento. Tente novamente. \N {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=Programação atualizada
#XMSG: Error in updating schedule
errorUpdateSchedule=Não foi possível atualizar a programação neste momento. Tente novamente. \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=Tem certeza que deseja excluir a programação?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Programação excluída.
#XMSG: Schedule deletion error message
errorSeleteSchedule=Não foi possível excluir a programação neste momento. Tente novamente. \n
#XFLD: Authorise service title
authServiceLabel=Autorizar serviço
#XMSG: Redirecting message
redirectingText=Redirecionando para autenticação.
#XMSG: Redirection success message
redirectSuccess=Você concedeu com sucesso a autorização para nós executarmos as tarefas recorrentes que você programou.
#XMSG: Start time label
startTimeFormatLabel=Hora de início (formato de {0} horas)
#XMSG: Start date and end date range validation
dateRangeValidationText=A data de início precisa ser anterior à data de término.
#XMSG: Delete schedule error
errorDeleteSchedule=Erro ao excluir a programação \n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=Hora(s)
#XFLD: Plural Recurrence text for Day
dayPluraltext=Dia(s)
#XFLD: Plural Recurrence text for Month
monthPluralText=Mês(es)
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=Horas
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=Dias
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=Meses
#XFLD: Recurrence text for Hour
hourText=Uma vez por hora
#XFLD: Recurrence text for Day
daytext=Uma vez por dia
#XFLD: Recurrence text for Month
monthText=Uma vez por mês
#XMSG: Start date cannot be in the past
startDateValidationText=A data de início não pode ser no passado
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=Insira um valor entre 1 e 23.
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=Insira um valor entre 1 e 31.
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=Insira um valor entre 1 e 12.
#XMSG: Invalid date text
invalidDateText=Data inválida
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=Recorrência
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=Data de início
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=Data de término
#XMSG: Schedule popover end date label
naText=ND
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=Fuso horário
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=Proprietário
#XMSG: text for not available
naText=ND
#XMSG: String Every
everyString=A cada
#XMSG: on label
onLabel=Em
#XMSG: on label
onLabelMonth=No dia do mês
#XFLD: Label for Sunday
sunLabel=Dom
#XFLD: Label for Monday
monLabel=Seg
#XFLD: Label for Tuesday
tueLabel=Ter
#XFLD: Label for Wednesday
wedLabel=Qua
#XFLD: Label for Thursday
thuLabel=Qui
#XFLD: Label for Friday
friLabel=Sex
#XFLD: Label for Saturday
satLabel=Sáb
#XFLD: Recurrence text for Week
weekText=Semana
#XFLD: Create Schedule Dialog Title
createDialogTitle=Criar programação
#XFLD: Edit Dialog title
editDialogTitle=Editar programação
#XFLD: Selected Object Title
selectedObjectText=Selecionada
#XMSG: Table Text
tableText=Tabela
#XMSG: View Text
viewText=Visão
#XMSG: Data flow text
dataFlowText=Fluxo de dados
#XMSG: Select a day of week error message
weekRecErrorText=Selecione pelo menos um dia da semana
#XFLD: Label for Owner
ownerLabel=Proprietário
#XFLD: Button label for takeover schedule
takeoverLabel=Modificar proprietário
#XFLD: Created By label
createdbyLabel=Criado por
#XFLD: Changed By label
changedbyLabel=Última modificação por
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=A autenticação de usuário é necessária para executar as tarefas programadas em SAP Datasphere.\n Ao escolher executar essa tarefa, a tarefa programada terá você como proprietário ao ser executada.\n Antes de chegar a hora da execução da tarefa programada, verifique se você deu seu consentimento para executá-la em seu nome.
#XMSG: Schedule takeover success message
ownerChangeSuccessText=Transferência de programação bem-sucedida
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=Falha ao transferir programação.
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=Falha ao transferir a programação devido a um erro na atualização da programação.
#XFLD: Text for confirm
confirm=Confirmar
#XMSG: Create schedule error message
createScheduleError=Falha ao criar programação.
#XMSG: Schedule update failed message
updateScheduleError=Falha ao atualizar programação.
#XMSG: Schedule delete error message
deleteScheduleError=Falha ao excluir programação.
#XFLD: Label for Frequency
frequencyLabel=Frequência
#XFLD: Label for Frequency
frequencyLabelNew=Configurações
#XFLD: Label for repeat
repeatLabel=Repetir
#XFLD: Label for repeat
repeatLabelNeww=Frequência:
#XFLD: Labe for text at
atLabel=Às
#XFLD: Label for starting
startingLabel=Iniciando
#XFLD: Label for Start
startLabel=Início
#XFLD: Label for Ending
validityLabel=Validade
#XFLD: Label for end
endLabel=Término
#XMSG: Message for no end date
noEndDateMsg=A recorrência ocorrerá indefinidamente.
#XFLD: Assign label
assignLabel=Atribuir a mim
#XFLD: Overview label
overviewLabel=Visão geral
#XFLD: Next runs field
nextRunsLabel=Execuções seguintes:
#XFLD: Next runs field
nextRunsLabelForPopOver=Execuções seguintes
#XFLD: Expired field
taskPopoverExpiredLabel=Expirado
#XFLD: Expired Message
taskPopoverExpiredMessage=A programação expirou. Para estendê-la, edite a programação e altere a data de término.
#XFLD: label for Day-of-month
dayOfmonthLabel=Dia do mês
#XFLD: label for Minute
minuteLabel=Minuto
#XFLD: label for hour
hourLabel=Hora
#XFLD: Last Day of month selection
lastDayText=Último dia do mês
#XFLD: Hourly unit description label
hourUnitLabel=Hora do dia
#XFLD: Daily recurrence unit description label
dayUnitLabel=Dia(s) por mês
#XFLD: Monthly receurrence unit description label
monthUnitLabel=Meses do ano
#XFLD: label for off
offLabel=Desativado
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=Ocorre a cada {0} hora(s) iniciando {1} em {2} minutos.
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=Ocorre a cada {0} dia(s) iniciando {1} às {2}.
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=Ocorre a cada {0} mês(es) iniciando {1}, {2} às {3}.
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=Ocorre a cada semana, {0} às {1}.
#XMSG: Schedule expired message
scheduleExpiredLabel=A programação expirou.
#XFLD: Create Schedule Dialog Title
createScheduleTitle=Criar programação para "{0}"
#XFLD: Edit Dialog title
createScheduleTitleNew={0}: Criar programação
#XFLD: Edit Dialog title
editScheduleTitle=Editar programação para "{0}"
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}: Editar programação
#XFLD: Edit Schedule Button
editSchedule=Editar programação
#XFLD: Full Label for Sunday
sunFullLabel=Domingo
#XFLD: Full Label for Monday
monFullLabel=Segunda-feira
#XFLD: Full Label for Tuesday
tueFullLabel=Terça-feira
#XFLD: Full Label for Wednesday
wedFullLabel=Quarta-feira
#XFLD: Full Label for Thursday
thuFullLabel=Quinta-feira
#XFLD: Full Label for Friday
friFullLabel=Sexta-feira
#XFLD: Full Label for Saturday
satFullLabel=Sábado
#XFLD: Weekly text
weeklyText=Uma vez por semana
#XFLD: Set start day text
startEndDateText=Definir data de início e término
#XMSG: Schedule runs indefinitely message
validityOffText=A programação será executada indefinidamente.
#XFLD: Summary label
summaryLabel=Síntese
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=Executa a cada {0} hora(s). A execução do ciclo é reinicializada todos os dias à 00:00 (UTC).
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=Executa a cada {0} dia(s) às {1} (UTC). A execução do ciclo é reinicializada no primeiro dia de cada mês.
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=Executa a cada {0} mês(es), {1} às {2} (UTC). A execução do ciclo é reinicializada no primeiro dia de cada ano.
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=Executa a cada semana, {0} às {1} (UTC).
#XFLD: Future runs label with time zone info
futureRunsLabel=Futuras execuções
#XFLD: Start & End Date label
startEndDateLabel=Definir data de início e término
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=A primeira execução pode não ser realizada porque ela será iniciada em menos de 10 minutos.
#XFLD: Label for text at UTC
atUTCLabel=Às (UTC)
#XMSG: Authorize expired text
authExpiredText=A autorização para executar suas tarefas recorrentes expirou. Você precisa renovar a autorização para permitir que a SAP execute a programação em seu nome, se quiser continuar a programar tarefas.
#XMSG: Authorization expiring soon text
authExpiringSoonText=A autorização para executar suas tarefas recorrentes expirará em breve. Renove a autorização para permitir que a SAP continue a executar a programação em seu nome.
#XMSG: Enter as Label text
enterAsText=Inserir como
#XMSG: Enter as Label text
enterAsTextNew=Tipo de configuração
#XMSG: standard schedule label text
simpleScheduleText=Programação simples
#XMSG: Cron expression label text
cronScheduleText=Expressão cron
#XMSG: Time Range label text
timeRangeLabelText=Período
#XMSG: Time Range label text
timeRangeLabelTextNew=Intervalo de datas
#XMSG: Ownership label
ownershipLabelText=Propriedade
#XMSG: Show runs in the selected timezone label
showRunsInText=Mostrar execuções em
#XMSG: Text for schedule pause
pauseScheduleLabel=Pausar programação:
#XMSG: Show Warning for schedule pause
schedulePausedWarning=Esta programação está pausada. Nenhuma execução futura disponível.
#XMSG: Cycle run text for hourly
hourlyCycleResetText=O ciclo de execução é reinicializado à 00:00 de cada dia.
#XMSG: Cycle run text for Daily
dailyCycleResetText=O ciclo de execução é reinicializado no primeiro dia de cada mês.
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=O ciclo de execução é reinicializado em 1º de janeiro de cada ano.
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=O ciclo de execução é reinicializado no domingo de cada semana.
#XMSG: Day label
dayLabel=Dia (mês)
#XMSG: Monthly label
monthlyLabel=Mês
#XMSG: Weekly label
weeklyLabel=Dia (semana)
#XMSG: Time in UTC label
utcTimeLabel=Hora atual (UTC)

#XMSG: January
monthLabel1=Janeiro
#XMSG: February
monthLabel2=Fevereiro
#XMSG: March
monthLabel3=Março
#XMSG: April
monthLabel4=Abril
#XMSG: May
monthLabel5=Maio
#XMSG: June
monthLabel6=Junho
#XMSG: July
monthLabel7=Julho
#XMSG: August
monthLabel8=Agosto
#XMSG: September
monthLabel9=Setembro
#XMSG: October
monthLabel10=Outubro
#XMSG: November
monthLabel11=Novembro
#XMSG: December
monthLabel12=Dezembro

weekdayLabel0=Domingo
weekdayLabel1=Segunda-feira
weekdayLabel2=Terça-feira
weekdayLabel3=Quarta-feira
weekdayLabel4=Quinta-feira
weekdayLabel5=Sexta-feira
weekdayLabel6=Sábado

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=A cada {0} minuto de {1} a {2}.
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=A cada {0} minuto.
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=A cada {0} minuto iniciando em {1}.
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=No minuto {0}.

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=após cada {0} hora de {1} a {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=após cada {0} hora
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=após cada {0} hora iniciando em {1}
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=após hora {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=a cada {0} dia do mês de {1} até {2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=a cada {0} dia do mês
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=a cada {0} dia do mês iniciando em {1}
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=no dia do mês {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=a cada {0} mês de {1} até {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=a cada {0} mês
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=a cada {0} mês iniciando em {1}
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=em {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=a cada {0} dia da semana de {1} até {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=a cada {0} dia da semana
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=a cada {0} dia da semana iniciando em {1}
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=em {0}
#XMSG: And text
andText=e
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=Insira um valor ou combinação de valores suportados:
#XMSG: default information about cron string
defaultCronStringInfoText=Valores suportados para expressão cron: <strong>números * , / </strong>.
#XMSG: Parameterized info about cron string
cronStringinfoText=Valores suportados para o campo selecionado: <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=Saiba mais
#XMSG: Hide details text
hideDetailsText=Ocultar detalhes
#XMSG: Label for starting at UTC Text
startingText=Iniciando em (UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=Copiar para clipboard
#XMSG: cron copied to clipboard
cronCopiedText=Expressão cron copiada para clipboard
#XMSG: error message for invalid cron copy
invalidCronCopyText=Não é possível copiar expressão cron inválida
#XMSG: No preview label
noPreviewText=Não é possível exibir uma visualização porque há erros ou valores ausentes. Revise a programação.
#XMSG: Label for create button
createText=Criar
#XMSG: Label for Save button
saveText=Salvar
#XMSG: Label for text leave
leaveText=Sair
#XMSG: warn message when user click cancel button
cancelWarnText=Suas alterações serão perdidas quando você sair.
#XMSG: Expired schedule warning
expiredTaskWarnText=Esta programação expirou. Selecione uma nova data de término para continuar a replicação.
#XMSG: Expired schedule message
expiredScheduleMessageText=Esta programação expirou. Selecione uma nova data de término para continuar a executar tarefas recorrentes.
#XMSG: Expired schedule message
expiredTaskWarnTextNew=Esta programação expirou. Para estendê-la, altere a data de término.
#XMSG: Expired schedule Text
expiredScheduleText=Expirado
#XMSG: No future runs generated
noFutureRunsText=Nenhuma execução seguinte disponível.
#XMSG: Text for Local time
localTimeText=Hora local
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=Não há nenhuma execução dentro do período de datas selecionado.
#XMSG: Warning text for expired end date
endDateExpiredText=A data de término está no passado.
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=Especifique uma data de início.
#XMSG: UTC info label
utcInfoLabelText=As programações são criadas na Hora Universal Coordenada (UTC). A hora UTC atual é {0}.
#XMSG: UTC info label
utcInfoLabelTextNew=Hora Universal Coordenada (UTC). A hora UTC atual é {0}.
#XMSG: Invalid cron paste error message
invalidCronPasteText=Não é possível colar a expressão cron porque ela é inválida. Tente novamente com uma expressão válida.
#XMSG: Weekly schedule error text
invalidWeeklyRecText=Selecione, pelo menos, um dia.
#XFLD: No End date text
noEndDateText=Nenhuma data de término
#XFLD: Help Button
openHelpButtonText=Mostrar ajuda
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=A data excede o número máximo de dias para o mês fornecido
#XMSG: Redundancy error in cron string
cronRedundantErrorText=Esse padrão tem expressões redundantes. Remova os padrões: * ou / da lista separada por vírgulas.
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=Insira um valor entre 0 e 59.
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=Insira um valor acima de 10.
#XMSG: Error message for missing start time
startTimeErrorText=Selecione um horário.
#XMSG: Error message handling for error code
invalidScheduleBodyError=O corpo da solicitação contém dados inválidos.
#XMSG: Error message handling for error code
scheduleNotFoundError=Não foi possível encontrar a programação.
#XMSG: Error message handling for error code
actionForbiddenError=Você não tem a permissão obrigatória.
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=Você está tentando criar uma programação que já existe.
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=Redefinir a cada dia
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=Redefinir a cada mês
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=Redefinir a cada ano
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=Redefinir a cada semana
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=Redefinir a cada hora
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=Redefinir a cada dia:
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=Redefinir a cada mês:
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=Redefinir a cada ano:
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=Redefinir a cada semana:
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=Redefinir a cada hora:
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=Às {0} a cada {1} dia(s)
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=Autorize-nos a executar as cadeias de tarefas, bem como as tarefas recorrentes que você programou.
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=Uma execução de programação carregará um instantâneo. O modo de replicação mudará de replicação em tempo real para replicação em lote e os dados não serão mais atualizados em tempo real.
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=Tem certeza de que deseja excluir as programações selecionadas?
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn={0} dos {1} objetos selecionados têm programações. \nTem certeza de que deseja excluir as programações?

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=Deseja assumir a propriedade das programações para os objetos selecionados?
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=Programações estão definidas para {0} dos {1} objetos selecionados. Deseja assumir a propriedade dessas programações?

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=Deseja pausar as programações dos objetos selecionados?
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn={0} dos {1} objetos selecionados têm programações em execução. Deseja pausá-las?

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=Deseja retomar as programações dos objetos selecionados?
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn={0} dos {1} objetos selecionados têm programações pausadas. Deseja retomá-las?

#XTXT: Warning text
warningText=Aviso
#XTXT: Text for schedule pause
pauseScheduleText=Pausar programação
#XTXT: Text for resume schedule
resumeScheduleText=Retomar programação
#XTXT: Text for assigning schedule
assignScheduleText=Atribuir programação a mim

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=A autorização para executar suas tarefas recorrentes expirará em {0}. Para continuar executando suas tarefas programadas, precisamos do seu consentimento novamente.

#XMSG: Reauthorize text
reauthorize=Reautorizar

#XTXT: Duration text
duration=Duração
#XTXT: Time frame label text
timeFrame=Período
#XTXT: Hours Label text
hours=Hora(s)
#XTXT: Minutes Label text
minutes=Minuto(s)
#XTXT: Minutes Label text
minutesNew=Minutos

#XTXT: Minute Recurrence type text
byMinute=Minutos
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=Insira um valor entre 0 e 59 ou defina uma frequência maior que ou igual a {0} minutos
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=Insira um valor entre 0 e 59.
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=A duração de {0} minutos é mais longa do que a frequência programada de {1} minutos.
#XTXT: Selected time zone At
timeZoneAt=Às
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=Iniciando às
