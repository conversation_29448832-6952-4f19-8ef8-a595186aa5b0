#XMSG: Message to ask user to authorize
authorizeWarn=Autorisez-nous à exécuter les tâches récurrentes que vous avez planifiées.
#XFLD: Authorize field value
authorise=Autoriser
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=Planification pour "{0}"
#XMSG: Ok message
ok=OK
#XMSG: Schedule
schedule=Planifier
#XMSG: Cancel message
cancel=Annuler
#XMSG: Close message
close=Fermer
#XFLD: lable to set recurrence
recurrenceLabel=Récurrence
#XFLD: label to set the recurrence value
everyLabel=Tous les/Toutes les
#XFLD: Start date field
startDateLabel=Date de début
#XFLD: Start date field
endDateLabel=Date de fin
#XFLD: TimeZone field
timeZoneLabel=Fuseau horaire
#XMSG: validation text
recurrenceValidationText1=Saisissez un nombre entier supérieur ou égal à 1.
#XMSG: validation text
recurrenceValidationText2=Saisissez une date de début.
#XMSG: validation text
recurrenceValidationText3=Saisissez une date de début.
#XMSG: validation text
noObjectSelectedText=Sélectionnez une table distante à planifier.
#XMSG: error text
errorLabel=Erreur
#XMSG: Schedule created alert message
createScheduleSuccess=Planification créée
#XMSG: Error in creating schedule
errorCreateSchedule=Impossible de créer la planification pour le moment. Veuillez réessayer. \n {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=Planification mise à jour
#XMSG: Error in updating schedule
errorUpdateSchedule=Impossible de mettre à jour la planification pour le moment. Veuillez réessayer. \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=Souhaitez-vous vraiment supprimer la planification?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Planification supprimée.
#XMSG: Schedule deletion error message
errorSeleteSchedule=Impossible de supprimer la planification pour le moment. Veuillez réessayer.
#XFLD: Authorise service title
authServiceLabel=Autoriser le service
#XMSG: Redirecting message
redirectingText=Redirection à des fins d'authentification.
#XMSG: Redirection success message
redirectSuccess=Vous nous avez autorisés à exécuter les tâches récurrentes que vous avez planifiées.
#XMSG: Start time label
startTimeFormatLabel=Heure de début (format {0} heures)
#XMSG: Start date and end date range validation
dateRangeValidationText=La date de début doit être inférieure (antérieure) à la date de fin.
#XMSG: Delete schedule error
errorDeleteSchedule=Erreur lors de la suppression de la planification \n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=Heure(s)
#XFLD: Plural Recurrence text for Day
dayPluraltext=Jour(s)
#XFLD: Plural Recurrence text for Month
monthPluralText=Mois
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=Heures
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=Jours
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=Mois
#XFLD: Recurrence text for Hour
hourText=Horaire
#XFLD: Recurrence text for Day
daytext=Quotidien
#XFLD: Recurrence text for Month
monthText=Mensuel
#XMSG: Start date cannot be in the past
startDateValidationText=La date de début ne peut pas se situer dans le passé.
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=Saisissez une valeur comprise entre 1 et 23.
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=Saisissez une valeur comprise entre 1 et 31.
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=Saisissez une valeur comprise entre 1 et 12.
#XMSG: Invalid date text
invalidDateText=Date non valide
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=Récurrence
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=Date de début
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=Date de fin
#XMSG: Schedule popover end date label
naText=S/O
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=Fuseau horaire
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=Propriétaire
#XMSG: text for not available
naText=Non applicable
#XMSG: String Every
everyString=Tous les/Toutes les
#XMSG: on label
onLabel=le
#XMSG: on label
onLabelMonth=Jour du mois
#XFLD: Label for Sunday
sunLabel=dim.
#XFLD: Label for Monday
monLabel=lun.
#XFLD: Label for Tuesday
tueLabel=mar.
#XFLD: Label for Wednesday
wedLabel=mer.
#XFLD: Label for Thursday
thuLabel=jeu.
#XFLD: Label for Friday
friLabel=ven.
#XFLD: Label for Saturday
satLabel=sam.
#XFLD: Recurrence text for Week
weekText=Semaine
#XFLD: Create Schedule Dialog Title
createDialogTitle=Créer une planification
#XFLD: Edit Dialog title
editDialogTitle=Modifier la planification
#XFLD: Selected Object Title
selectedObjectText=Sélectionné(es)
#XMSG: Table Text
tableText=Table
#XMSG: View Text
viewText=Vue
#XMSG: Data flow text
dataFlowText=Flux de données
#XMSG: Select a day of week error message
weekRecErrorText=Sélectionnez au moins un jour de la semaine.
#XFLD: Label for Owner
ownerLabel=Propriétaire
#XFLD: Button label for takeover schedule
takeoverLabel=Modifier le propriétaire
#XFLD: Created By label
createdbyLabel=Auteur de la création
#XFLD: Changed By label
changedbyLabel=Dernière modification par
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=L'authentification de l'utilisateur est requise pour exécuter des tâches planifiées dans SAP Datasphere.\n Si vous décidez d'exécuter cette tâche, cette tâche planifiée sera exécutée avec vous en tant que propriétaire.\n Avant l'exécution d'une tâche planifiée, assurez-vous que vous avez consenti à ce que ces tâches soient exécutées en votre nom.
#XMSG: Schedule takeover success message
ownerChangeSuccessText=Reprise de la planification réussie.
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=Échec de la reprise de la planification.
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=Échec de la reprise de la planification en raison d'une erreur lors de la mise à jour de la planification.
#XFLD: Text for confirm
confirm=Confirmer
#XMSG: Create schedule error message
createScheduleError=Échec de la création de la planification.
#XMSG: Schedule update failed message
updateScheduleError=Échec de la mise à jour de la planification.
#XMSG: Schedule delete error message
deleteScheduleError=Échec de la suppression de la planification.
#XFLD: Label for Frequency
frequencyLabel=Fréquence
#XFLD: Label for Frequency
frequencyLabelNew=Paramètres
#XFLD: Label for repeat
repeatLabel=Répéter
#XFLD: Label for repeat
repeatLabelNeww=Fréquence :
#XFLD: Labe for text at
atLabel=À
#XFLD: Label for starting
startingLabel=Lancement
#XFLD: Label for Start
startLabel=Début
#XFLD: Label for Ending
validityLabel=Validité
#XFLD: Label for end
endLabel=Fin
#XMSG: Message for no end date
noEndDateMsg=La récurrence aura lieu indéfiniment.
#XFLD: Assign label
assignLabel=Me l'affecter
#XFLD: Overview label
overviewLabel=Synthèse
#XFLD: Next runs field
nextRunsLabel=Prochaines exécutions:
#XFLD: Next runs field
nextRunsLabelForPopOver=Prochaines exécutions
#XFLD: Expired field
taskPopoverExpiredLabel=Expiré(e)
#XFLD: Expired Message
taskPopoverExpiredMessage=La planification a expiré. Pour la prolonger, modifiez la planification et modifiez la date de fin.
#XFLD: label for Day-of-month
dayOfmonthLabel=Jour du mois
#XFLD: label for Minute
minuteLabel=Minute
#XFLD: label for hour
hourLabel=Heure
#XFLD: Last Day of month selection
lastDayText=Dernier jour du mois
#XFLD: Hourly unit description label
hourUnitLabel=Heure du jour
#XFLD: Daily recurrence unit description label
dayUnitLabel=Jour(s) par mois
#XFLD: Monthly receurrence unit description label
monthUnitLabel=Mois de l'année
#XFLD: label for off
offLabel=Désactivé
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=A lieu toutes les {0} heures, à partir du {1} à {2} minutes.
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=A lieu tous les {0} jours, à partir du {1} à {2}.
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=A lieu tous les {0} mois, à partir du {1} le {2} à {3}.
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=A lieu toutes les semaines, le {0} à {1}.
#XMSG: Schedule expired message
scheduleExpiredLabel=La planification a expiré.
#XFLD: Create Schedule Dialog Title
createScheduleTitle=Créer une planification pour "{0}"
#XFLD: Edit Dialog title
createScheduleTitleNew={0} : Créer une planification
#XFLD: Edit Dialog title
editScheduleTitle=Modifier la planification pour "{0}"
#XFLD: Edit Dialog title
editScheduleTitleNeww={0} : Modifier la planification
#XFLD: Edit Schedule Button
editSchedule=Modifier la planification
#XFLD: Full Label for Sunday
sunFullLabel=dimanche
#XFLD: Full Label for Monday
monFullLabel=lundi
#XFLD: Full Label for Tuesday
tueFullLabel=mardi
#XFLD: Full Label for Wednesday
wedFullLabel=mercredi
#XFLD: Full Label for Thursday
thuFullLabel=jeudi
#XFLD: Full Label for Friday
friFullLabel=vendredi
#XFLD: Full Label for Saturday
satFullLabel=samedi
#XFLD: Weekly text
weeklyText=Hebdomadaire
#XFLD: Set start day text
startEndDateText=Définir les dates de début et de fin
#XMSG: Schedule runs indefinitely message
validityOffText=La planification aura lieu indéfiniment.
#XFLD: Summary label
summaryLabel=Synthèse
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=Exécution toutes les {0} heures. ''Exécuter le cycle'' est réinitialisé tous les jours à 0:00 (UTC).
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=Exécution tous les {0} jours à {1} (UTC). ''Exécuter le cycle'' est réinitialisé le premier jour de chaque mois.
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=Exécution tous les {0} mois le {1} à {2} (UTC). ''Exécuter le cycle'' est réinitialisé le premier jour de chaque année.
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=Exécution toutes des semaines, le {0} à {1} (UTC).
#XFLD: Future runs label with time zone info
futureRunsLabel=Exécutions futures
#XFLD: Start & End Date label
startEndDateLabel=Définir les dates de début et de fin
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=Il se peut que la première exécution ne se lance pas car elle doit avoir lieu dans moins de 10 minutes.
#XFLD: Label for text at UTC
atUTCLabel=à (UTC)
#XMSG: Authorize expired text
authExpiredText=L'autorisation d'exécuter vos tâches récurrentes a expiré. Vous devez renouveler l'autorisation pour permettre à SAP d'exécuter la planification en votre nom si vous voulez continuer à planifier des tâches.
#XMSG: Authorization expiring soon text
authExpiringSoonText=L'autorisation d'exécuter vos tâches récurrentes va bientôt expirer. Renouvelez l'autorisation pour permettre à SAP de poursuivre l'exécution de la planification en votre nom.
#XMSG: Enter as Label text
enterAsText=Saisir en tant que
#XMSG: Enter as Label text
enterAsTextNew=Type de paramètre
#XMSG: standard schedule label text
simpleScheduleText=Planification simple
#XMSG: Cron expression label text
cronScheduleText=Expression cron
#XMSG: Time Range label text
timeRangeLabelText=Intervalle de temps
#XMSG: Time Range label text
timeRangeLabelTextNew=Plage de dates
#XMSG: Ownership label
ownershipLabelText=Périmètre
#XMSG: Show runs in the selected timezone label
showRunsInText=Afficher les exécutions en
#XMSG: Text for schedule pause
pauseScheduleLabel=Suspendre la planification :
#XMSG: Show Warning for schedule pause
schedulePausedWarning=La planification est suspendue. Aucune autre exécution n'est disponible.
#XMSG: Cycle run text for hourly
hourlyCycleResetText='Exécuter le cycle' est réinitialisé tous les jours à 00:00.
#XMSG: Cycle run text for Daily
dailyCycleResetText='Exécuter le cycle' est réinitialisé le premier jour de chaque mois.
#XMSG: Cycle run text for Monthly
monthlyCycleResetText='Exécuter le cycle' est réinitialisé le 1er janvier de chaque année.
#XMSG: Cycle run text for Weekly
weeklyCycleResetText='Exécuter le cycle' est réinitialisé tous les dimanches.
#XMSG: Day label
dayLabel=Jour (mois)
#XMSG: Monthly label
monthlyLabel=Mois
#XMSG: Weekly label
weeklyLabel=Jour (semaine)
#XMSG: Time in UTC label
utcTimeLabel=Heure actuelle (UTC)

#XMSG: January
monthLabel1=Janvier
#XMSG: February
monthLabel2=Février
#XMSG: March
monthLabel3=Mars
#XMSG: April
monthLabel4=Avril
#XMSG: May
monthLabel5=Mai
#XMSG: June
monthLabel6=Juin
#XMSG: July
monthLabel7=Juillet
#XMSG: August
monthLabel8=Août
#XMSG: September
monthLabel9=Septembre
#XMSG: October
monthLabel10=Octobre
#XMSG: November
monthLabel11=Novembre
#XMSG: December
monthLabel12=Décembre

weekdayLabel0=dimanche
weekdayLabel1=lundi
weekdayLabel2=mardi
weekdayLabel3=mercredi
weekdayLabel4=jeudi
weekdayLabel5=vendredi
weekdayLabel6=samedi

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=Toutes les {0} minutes de {1} à {2}.
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=Toutes les {0} minutes.
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=Toutes les {0} minutes, en commençant à {1}.
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=À la minute {0}.

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=après chaque {0} heure de {1} à {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=après chaque {0} heure
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=après chaque {0} heure, en commençant à {1}
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=après l''heure {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=chaque {0} jour du mois de {1} à {2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=chaque {0} jour du mois
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=chaque {0} jour du mois, en commençant le {1}
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=le {0} du mois

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=tous les {0} mois, du {1} au {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=tous les {0} mois
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=tous les {0} mois, en commençant le {1}
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=en {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=chaque {0} jour de la semaine de {1} à {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=chaque {0} jour de la semaine
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=chaque {0} jour de la semaine, en commençant le {1}
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=le {0}
#XMSG: And text
andText=et
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=Saisissez une valeur prise en charge ou une combinaisons de valeurs :
#XMSG: default information about cron string
defaultCronStringInfoText=Valeurs prises en charge pour l'expression cron : <strong>nombres * , - /</strong>
#XMSG: Parameterized info about cron string
cronStringinfoText=Valeurs prises en charge pour la zone sélectionnée : <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=En savoir plus
#XMSG: Hide details text
hideDetailsText=Masquer les détails
#XMSG: Label for starting at UTC Text
startingText=Début à (UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=Copier dans le presse-papiers
#XMSG: cron copied to clipboard
cronCopiedText=Expression cron copiée dans le presse-papiers.
#XMSG: error message for invalid cron copy
invalidCronCopyText=Impossible de copier l'expression cron non valide.
#XMSG: No preview label
noPreviewText=Impossible d'afficher un aperçu. Des erreurs subsistent ou des valeurs sont manquantes. Veuillez réviser la planification.
#XMSG: Label for create button
createText=Créer
#XMSG: Label for Save button
saveText=Enregistrer
#XMSG: Label for text leave
leaveText=Quitter
#XMSG: warn message when user click cancel button
cancelWarnText=Vos modifications seront perdues si vous quittez maintenant.
#XMSG: Expired schedule warning
expiredTaskWarnText=Cette planification a expiré. Veuillez sélectionner une nouvelle date de fin pour continuer avec la réplication.
#XMSG: Expired schedule message
expiredScheduleMessageText=Cette planification a expiré. Veuillez sélectionner une nouvelle date de fin pour exécuter les tâches récurrentes.
#XMSG: Expired schedule message
expiredTaskWarnTextNew=Cette planification a expiré. Pour la prolonger, modifiez la date de fin.
#XMSG: Expired schedule Text
expiredScheduleText=Expiré(e)
#XMSG: No future runs generated
noFutureRunsText=Aucune exécution suivante n'est disponible.
#XMSG: Text for Local time
localTimeText=Heure locale
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=Aucune exécution dans la plage de dates sélectionnée
#XMSG: Warning text for expired end date
endDateExpiredText=La date de fin se situe dans le passé.
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=Indiquez une date de début.
#XMSG: UTC info label
utcInfoLabelText=Les planifications sont créées en temps universel coordonné (UTC). L''heure UTC actuelle est {0}.
#XMSG: UTC info label
utcInfoLabelTextNew=Temps universel coordonné (UTC). L''heure UTC actuelle est {0}.
#XMSG: Invalid cron paste error message
invalidCronPasteText=Nous ne pouvons pas coller l'expression cron car elle n'est pas valide. Essayez à nouveau avec une expression valide.
#XMSG: Weekly schedule error text
invalidWeeklyRecText=Sélectionnez au moins un jour.
#XFLD: No End date text
noEndDateText=Aucune date de fin
#XFLD: Help Button
openHelpButtonText=Afficher l'aide
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=La date dépasse le nombre maximal de jours pour le mois donné.
#XMSG: Redundancy error in cron string
cronRedundantErrorText=Ce modèle comporte des expressions redondantes. Retirez les modèles : * ou / de la liste séparée par des virgules.
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=Saisissez une valeur comprise entre 0 et 59.
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=Saisissez une valeur supérieure à 10.
#XMSG: Error message for missing start time
startTimeErrorText=Sélectionnez une heure.
#XMSG: Error message handling for error code
invalidScheduleBodyError=Le corps de la requête contient des données non valides.
#XMSG: Error message handling for error code
scheduleNotFoundError=La planification est introuvable.
#XMSG: Error message handling for error code
actionForbiddenError=Vous ne disposez pas de l'autorisation requise.
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=Vous tentez de créer une planification qui existe déjà.
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=Réinitialiser tous les jours
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=Réinitialiser tous les mois
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=Réinitialiser tous les ans
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=Réinitialiser toutes les semaines
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=Réinitialiser toutes les heures
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=Réinitialiser tous les jours :
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=Réinitialiser tous les mois :
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=Réinitialiser tous les ans :
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=Réinitialiser toutes les semaines :
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=Réinitialiser toutes les heures :
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=À {0} tous les {1} jour(s)
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=Autorisez-nous à exécuter les chaînes de tâches ainsi que les tâches récurrentes que vous avez planifiées.
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=L'exécution d'une planification chargera un nouvel instantané. Le mode de réplication passera ainsi de la réplication en temps réel à la réplication par lots et les données ne seront plus mises à jour en temps réel.
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=Voulez-vous vraiment supprimer les planifications sélectionnées?
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn={0} des {1} objets sélectionnés comporte(nt) des planifications. \nVoulez-vous vraiment supprimer les planifications?

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=Voulez-vous devenir le propriétaire des planifications des objets sélectionnés?
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=Des planifications sont définies pour {0} des {1} objets sélectionnés. Voulez-vous devenir le propriétaire de ces planifications?

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=Voulez-vous suspendre les planifications des objets sélectionnés?
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn={0} des {1} objets sélectionnés comporte(nt) des planifications en cours d''exécution. Voulez-vous les suspendre?

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=Voulez-vous reprendre les planifications des objets sélectionnés?
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn={0} des {1} objets sélectionnés comporte(nt) des planifications suspendues. Voulez-vous les reprendre?

#XTXT: Warning text
warningText=Avertissement
#XTXT: Text for schedule pause
pauseScheduleText=Suspendre la planification
#XTXT: Text for resume schedule
resumeScheduleText=Reprendre la planification
#XTXT: Text for assigning schedule
assignScheduleText=M'affecter la planification

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=L''autorisation pour exécuter vos tâches récurrentes expirera le {0}. Pour poursuivre l''exécution de vos tâches planifiées, nous aurons de nouveau besoin de votre consentement.

#XMSG: Reauthorize text
reauthorize=Autoriser à nouveau

#XTXT: Duration text
duration=Durée
#XTXT: Time frame label text
timeFrame=Période
#XTXT: Hours Label text
hours=Heure(s)
#XTXT: Minutes Label text
minutes=Minute(s)
#XTXT: Minutes Label text
minutesNew=Minutes

#XTXT: Minute Recurrence type text
byMinute=Minutes
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=Saisissez une valeur entre 0 et 59, ou définissez une fréquence supérieure ou égale à {0} minutes.
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=Saisissez une valeur entre 0 et 59.
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=La durée de {0} minutes est supérieure à la fréquence planifiée de {1} minutes.
#XTXT: Selected time zone At
timeZoneAt=À
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=Heure de début
