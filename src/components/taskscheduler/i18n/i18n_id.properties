#XMSG: Message to ask user to authorize
authorizeWarn=<PERSON>torisasikan kami untuk mengeksekusi tugas berulang yang telah Anda jadwalkan.
#XFLD: Authorize field value
authorise=Otorisasikan
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=<PERSON><PERSON><PERSON><PERSON> untuk "{0}"
#XMSG: Ok message
ok=OKE
#XMSG: Schedule
schedule=Jadwal
#XMSG: Cancel message
cancel=Batalkan
#XMSG: Close message
close=Tutup
#XFLD: lable to set recurrence
recurrenceLabel=Pengulangan
#XFLD: label to set the recurrence value
everyLabel=Setiap
#XFLD: Start date field
startDateLabel=Tanggal Mulai
#XFLD: Start date field
endDateLabel=Tanggal Berakhir
#XFLD: TimeZone field
timeZoneLabel=Zona Waktu
#XMSG: validation text
recurrenceValidationText1=Masukkan nilai bilangan bulat senilai 1 atau lebih besar
#XMSG: validation text
recurrenceValidationText2=Masukkan tanggal mulai
#XMSG: validation text
recurrenceValidationText3=Masukkan tanggal mulai
#XMSG: validation text
noObjectSelectedText=Pilih tabel jarak jauh untuk dijadwalkan.
#XMSG: error text
errorLabel=Kesalahan
#XMSG: Schedule created alert message
createScheduleSuccess=Jadwal dibuat
#XMSG: Error in creating schedule
errorCreateSchedule=Tidak dapat membuat jadwal saat ini. Silakan coba lagi. \N {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=Jadwal diperbarui
#XMSG: Error in updating schedule
errorUpdateSchedule=Tidak dapat memperbarui jadwal saat ini. Silakan coba lagi. \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=Apakah Anda yakin ingin menghapus permanen jadwal ini?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Jadwal dihapus permanen.
#XMSG: Schedule deletion error message
errorSeleteSchedule=Tidak dapat menghapus permanen jadwal saat ini. Silakan coba lagi.
#XFLD: Authorise service title
authServiceLabel=Otorisasikan Layanan
#XMSG: Redirecting message
redirectingText=Mengalihkan untuk autentikasi.
#XMSG: Redirection success message
redirectSuccess=Anda telah berhasil memberikan otoritas kepada kami untuk mengeksekusi tugas berulang yang telah Anda jadwalkan.
#XMSG: Start time label
startTimeFormatLabel=Waktu Mulai (Format {0} Jam)
#XMSG: Start date and end date range validation
dateRangeValidationText=Tanggal mulai harus lebih kecil (lebih awal) daripada tanggal berakhir.
#XMSG: Delete schedule error
errorDeleteSchedule=Kesalahan saat menghapus jadwal \n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=Jam
#XFLD: Plural Recurrence text for Day
dayPluraltext=Hari
#XFLD: Plural Recurrence text for Month
monthPluralText=Bulan
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=Jam
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=Hari
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=Bulan
#XFLD: Recurrence text for Hour
hourText=Per jam
#XFLD: Recurrence text for Day
daytext=Harian
#XFLD: Recurrence text for Month
monthText=Bulanan
#XMSG: Start date cannot be in the past
startDateValidationText=Tanggal mulai tidak boleh di masa lampau
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=Masukkan nilai antara 1 dan 23.
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=Masukkan nilai antara 1 dan 31.
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=Masukkan nilai antara 1 dan 12.
#XMSG: Invalid date text
invalidDateText=Tanggal tidak valid
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=Pengulangan
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=Tanggal Mulai
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=Tanggal Berakhir
#XMSG: Schedule popover end date label
naText=Tidak Tersedia
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=Zona Waktu
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=Pemilik
#XMSG: text for not available
naText=Tidak Ada
#XMSG: String Every
everyString=Setiap
#XMSG: on label
onLabel=Pada
#XMSG: on label
onLabelMonth=Pada Hari dalam Bulan
#XFLD: Label for Sunday
sunLabel=Min
#XFLD: Label for Monday
monLabel=Sen
#XFLD: Label for Tuesday
tueLabel=Sel
#XFLD: Label for Wednesday
wedLabel=Rab
#XFLD: Label for Thursday
thuLabel=Kam
#XFLD: Label for Friday
friLabel=Jum
#XFLD: Label for Saturday
satLabel=Sab
#XFLD: Recurrence text for Week
weekText=Minggu
#XFLD: Create Schedule Dialog Title
createDialogTitle=Buat Jadwal
#XFLD: Edit Dialog title
editDialogTitle=Edit Jadwal
#XFLD: Selected Object Title
selectedObjectText=Dipilih
#XMSG: Table Text
tableText=Tabel
#XMSG: View Text
viewText=Tampilan
#XMSG: Data flow text
dataFlowText=Aliran Data
#XMSG: Select a day of week error message
weekRecErrorText=Pilih setidaknya satu hari dalam seminggu
#XFLD: Label for Owner
ownerLabel=Pemilik
#XFLD: Button label for takeover schedule
takeoverLabel=Ubah Pemilik
#XFLD: Created By label
createdbyLabel=Dibuat Oleh
#XFLD: Changed By label
changedbyLabel=Terakhir Dimodifikasi Oleh
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=Autentikasi pengguna diperlukan untuk mengeksekusi tugas terjadwal di SAP Datasphere.\n Dengan memilih menjalankan tugas ini, tugas terjadwal ini akan dieksekusi dengan Anda sebagai pemiliknya.\n Sebelum tugas terjadwal akan dieksekusi, pastikan Anda telah memberikan persetujuan untuk mengeksekusi tugas atas nama Anda.
#XMSG: Schedule takeover success message
ownerChangeSuccessText=Pengambilalihan jadwal berhasil.
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=Pengambilalihan jadwal gagal.
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=Pengambilalihan jadwal gagal karena kesalahan saat memperbarui jadwal.
#XFLD: Text for confirm
confirm=Konfirmasi
#XMSG: Create schedule error message
createScheduleError=Pembuatan jadwal gagal.
#XMSG: Schedule update failed message
updateScheduleError=Pembaruan jadwal gagal.
#XMSG: Schedule delete error message
deleteScheduleError=Penghapusan jadwal gagal.
#XFLD: Label for Frequency
frequencyLabel=Frekuensi
#XFLD: Label for Frequency
frequencyLabelNew=Pengaturan
#XFLD: Label for repeat
repeatLabel=Ulangi
#XFLD: Label for repeat
repeatLabelNeww=Frekuensi:
#XFLD: Labe for text at
atLabel=Pada
#XFLD: Label for starting
startingLabel=Memulai
#XFLD: Label for Start
startLabel=Mulai
#XFLD: Label for Ending
validityLabel=Validitas
#XFLD: Label for end
endLabel=Berakhir
#XMSG: Message for no end date
noEndDateMsg=Pengulangan akan terjadi tanpa batas waktu.
#XFLD: Assign label
assignLabel=Tetapkan ke Saya
#XFLD: Overview label
overviewLabel=Gambaran Umum
#XFLD: Next runs field
nextRunsLabel=Eksekusi Selanjutnya:
#XFLD: Next runs field
nextRunsLabelForPopOver=Eksekusi Selanjutnya
#XFLD: Expired field
taskPopoverExpiredLabel=Kedaluwarsa
#XFLD: Expired Message
taskPopoverExpiredMessage=Jadwal telah kedaluwarsa. Untuk memperpanjangnya, edit jadwal dan ubah tanggal berakhirnya.
#XFLD: label for Day-of-month
dayOfmonthLabel=Hari dalam Bulan
#XFLD: label for Minute
minuteLabel=Menit
#XFLD: label for hour
hourLabel=Jam
#XFLD: Last Day of month selection
lastDayText=Hari Terakhir dalam Bulan
#XFLD: Hourly unit description label
hourUnitLabel=Jam dalam Sehari
#XFLD: Daily recurrence unit description label
dayUnitLabel=Hari(-Hari) per Bulan
#XFLD: Monthly receurrence unit description label
monthUnitLabel=Bulan dalam Setahun
#XFLD: label for off
offLabel=Nonaktif
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=Terjadi setiap {0} jam mulai {1} pada {2} menit.
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=Terjadi setiap {0} hari mulai {1} pukul {2}.
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=Terjadi setiap {0} bulan mulai {1} pada tanggal {2} pukul {3}.
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=Terjadi setiap minggu pada tanggal {0} pukul {1}.
#XMSG: Schedule expired message
scheduleExpiredLabel=Jadwal telah kedaluwarsa.
#XFLD: Create Schedule Dialog Title
createScheduleTitle=Buat Jadwal untuk “{0}”
#XFLD: Edit Dialog title
createScheduleTitleNew={0}: Buat Jadwal
#XFLD: Edit Dialog title
editScheduleTitle=Edit Jadwal untuk “{0}”
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}: Edit Jadwal
#XFLD: Edit Schedule Button
editSchedule=Edit Jadwal
#XFLD: Full Label for Sunday
sunFullLabel=Minggu
#XFLD: Full Label for Monday
monFullLabel=Senin
#XFLD: Full Label for Tuesday
tueFullLabel=Selasa
#XFLD: Full Label for Wednesday
wedFullLabel=Rabu
#XFLD: Full Label for Thursday
thuFullLabel=Kamis
#XFLD: Full Label for Friday
friFullLabel=Jumat
#XFLD: Full Label for Saturday
satFullLabel=Sabtu
#XFLD: Weekly text
weeklyText=Mingguan
#XFLD: Set start day text
startEndDateText=Tetapkan Tanggal Mulai dan Berakhir
#XMSG: Schedule runs indefinitely message
validityOffText=Jadwal akan dieksekusi tanpa batas waktu.
#XFLD: Summary label
summaryLabel=Ringkasan
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=Dieksekusi setiap {0} Jam. Siklus eksekusi diatur ulang setiap hari pada pukul 00:00 (UTC).
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=Dieksekusi setiap {0} Hari pada pukul {1} (UTC). Siklus eksekusi diatur ulang pada hari pertama setiap bulan.
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=Dieksekusi setiap {0} Bulan pada tanggal {1} pukul {2} (UTC). Siklus eksekusi diatur ulang pada hari pertama setiap tahun.
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=Dieksekusi setiap minggu pada tanggal {0} pukul {1} (UTC).
#XFLD: Future runs label with time zone info
futureRunsLabel=Eksekusi Mendatang
#XFLD: Start & End Date label
startEndDateLabel=Tetapkan Tanggal Mulai dan Berakhir
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=Eksekusi pertama mungkin tidak dilaksanakan karena akan dimulai kurang dari 10 menit lagi.
#XFLD: Label for text at UTC
atUTCLabel=Pada pukul (UTC)
#XMSG: Authorize expired text
authExpiredText=Otorisasi untuk mengeksekusi tugas berulang Anda telah kedaluwarsa. Anda perlu memperbarui otorisasi agar SAP dapat mengeksekusi jadwal atas nama Anda jika Anda ingin terus menjadwalkan tugas.
#XMSG: Authorization expiring soon text
authExpiringSoonText=Otorisasi untuk mengeksekusi tugas berulang Anda akan segera kedaluwarsa. Perbarui otorisasi agar SAP dapat terus mengeksekusi jadwal atas nama Anda.
#XMSG: Enter as Label text
enterAsText=Masuk sebagai
#XMSG: Enter as Label text
enterAsTextNew=Tipe Pengaturan
#XMSG: standard schedule label text
simpleScheduleText=Jadwal Sederhana
#XMSG: Cron expression label text
cronScheduleText=Ekspresi Cron
#XMSG: Time Range label text
timeRangeLabelText=Rentang Waktu
#XMSG: Time Range label text
timeRangeLabelTextNew=Rentang Tanggal
#XMSG: Ownership label
ownershipLabelText=Kepemilikan
#XMSG: Show runs in the selected timezone label
showRunsInText=Tampilkan Eksekusi dalam
#XMSG: Text for schedule pause
pauseScheduleLabel=Jadwal Jeda:
#XMSG: Show Warning for schedule pause
schedulePausedWarning=Jadwal ini dijeda. Tidak ada eksekusi mendatang yang tersedia.
#XMSG: Cycle run text for hourly
hourlyCycleResetText=Siklus eksekusi diatur ulang setiap hari pada pukul 00:00.
#XMSG: Cycle run text for Daily
dailyCycleResetText=Siklus eksekusi diatur ulang pada hari pertama setiap bulan.
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=Siklus eksekusi diatur ulang setiap tahun pada 1 Januari.
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=Siklus eksekusi diatur ulang setiap minggu pada hari Minggu.
#XMSG: Day label
dayLabel=Hari (Bulan)
#XMSG: Monthly label
monthlyLabel=Bulan
#XMSG: Weekly label
weeklyLabel=Hari (Minggu)
#XMSG: Time in UTC label
utcTimeLabel=Waktu Saat Ini (UTC)

#XMSG: January
monthLabel1=Januari
#XMSG: February
monthLabel2=Februari
#XMSG: March
monthLabel3=Maret
#XMSG: April
monthLabel4=April
#XMSG: May
monthLabel5=Mei
#XMSG: June
monthLabel6=Juni
#XMSG: July
monthLabel7=Juli
#XMSG: August
monthLabel8=Agustus
#XMSG: September
monthLabel9=September
#XMSG: October
monthLabel10=Oktober
#XMSG: November
monthLabel11=November
#XMSG: December
monthLabel12=Desember

weekdayLabel0=Minggu
weekdayLabel1=Senin
weekdayLabel2=Selasa
weekdayLabel3=Rabu
weekdayLabel4=Kamis
weekdayLabel5=Jumat
weekdayLabel6=Sabtu

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=Pada setiap {0} menit dari {1} hingga {2}.
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=Pada setiap {0} menit.
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=Pada setiap {0} menit dimulai pada {1}.
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=Pada menit {0}.

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=lewat setiap {0} jam dari {1} hingga {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=lewat setiap {0} jam
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=lewat setiap {0} jam dimulai pada {1}
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=jam lalu {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=pada setiap {0} hari dari bulan sejak {1} melewati {2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=pada setiap {0} hari dari bulan
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=pada setiap {0} hari dari bulan dimulai pada {1}
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=pada hari dalam bulan {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=dalam setiap {0} bulan dari {1} melewati {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=dalam setiap {0} bulan
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=dalam setiap {0} bulan dimulai pada {1}
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=dalam {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=pada setiap {0} hari dari minggu sejak {1} melewati {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=pada setiap {0} hari dari minggu
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=pada setiap {0} hari dari minggu dimulai pada {1}
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=pada {0}
#XMSG: And text
andText=dan
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=Masukkan nilai yang didukung atau kombinasi nilai:
#XMSG: default information about cron string
defaultCronStringInfoText=Nilai yang didukung untuk ekspresi cron: <strong>angka * , - / </strong>
#XMSG: Parameterized info about cron string
cronStringinfoText=Nilai yang didukung untuk bidang terpilih: <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=Pelajari Selengkapnya
#XMSG: Hide details text
hideDetailsText=Sembunyikan rincian
#XMSG: Label for starting at UTC Text
startingText=Dimulai pada (UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=Salin ke Clipboard
#XMSG: cron copied to clipboard
cronCopiedText=Ekspresi cron disalin ke clipboard
#XMSG: error message for invalid cron copy
invalidCronCopyText=Tidak dapat menyalin ekspresi cron yang tidak valid
#XMSG: No preview label
noPreviewText=Kami tidak dapat menampilkan pratinjau karena terdapat kesalahan atau nilai yang hilang. Silakan tinjau jadwal.
#XMSG: Label for create button
createText=Buat
#XMSG: Label for Save button
saveText=Simpan
#XMSG: Label for text leave
leaveText=Keluar
#XMSG: warn message when user click cancel button
cancelWarnText=Perubahan akan hilang jika Anda meninggalkan halaman ini.
#XMSG: Expired schedule warning
expiredTaskWarnText=Jadwal ini telah kedaluwarsa. Silakan pilih tanggal berakhir yang baru untuk melanjutkan replikasi.
#XMSG: Expired schedule message
expiredScheduleMessageText=Jadwal ini telah kedaluwarsa. Silakan pilih tanggal berakhir yang baru untuk terus mengeksekusi tugas berulang.
#XMSG: Expired schedule message
expiredTaskWarnTextNew=Jadwal telah kedaluwarsa. Untuk memperpanjangnya, ubah tanggal berakhirnya.
#XMSG: Expired schedule Text
expiredScheduleText=Kedaluwarsa
#XMSG: No future runs generated
noFutureRunsText=Tidak tersedia eksekusi selanjutnya.
#XMSG: Text for Local time
localTimeText=Waktu Setempat
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=Tidak ada eksekusi dalam rentang tanggal terpilih.
#XMSG: Warning text for expired end date
endDateExpiredText=Tanggal berakhir berada di waktu lampau.
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=Tetapkan tanggal mulai.
#XMSG: UTC info label
utcInfoLabelText=Jadwal dibuat dalam Waktu Universal Terkoordinasi (UTC). Waktu UTC saat ini adalah {0}.
#XMSG: UTC info label
utcInfoLabelTextNew=Waktu Universal Terkoordinasi (UTC). Waktu UTC saat ini adalah {0}.
#XMSG: Invalid cron paste error message
invalidCronPasteText=Kami tidak dapat menempelkan ekspresi cron karena tidak valid. Silakan coba lagi dengan ekspresi yang valid.
#XMSG: Weekly schedule error text
invalidWeeklyRecText=Silakan pilih sedikitnya satu hari.
#XFLD: No End date text
noEndDateText=Tidak Ada Tanggal Berakhir
#XFLD: Help Button
openHelpButtonText=Tampilkan Bantuan
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=Tanggal melebihi jumlah hari maks untuk bulan yang diberikan
#XMSG: Redundancy error in cron string
cronRedundantErrorText=Pola ini memiliki ekspresi yang berlebihan. Harap hapus pola: * atau / dari daftar yang dipisahkan koma.
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=Masukkan nilai antara 0 hingga 59.
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=Masukkan nilai di atas 10.
#XMSG: Error message for missing start time
startTimeErrorText=Pilih Waktu.
#XMSG: Error message handling for error code
invalidScheduleBodyError=Isi permintaan mengandung data yang tidak valid.
#XMSG: Error message handling for error code
scheduleNotFoundError=Jadwal tidak dapat ditemukan.
#XMSG: Error message handling for error code
actionForbiddenError=Anda tidak memiliki izin yang diperlukan.
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=Anda sedang mencoba membuat jadwal yang sudah ada.
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=Atur Ulang Setiap Hari
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=Atur Ulang Setiap Bulan
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=Atur Ulang Setiap Tahun
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=Atur Ulang Setiap Minggu
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=Atur Ulang Setiap Jam
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=Atur Ulang Setiap Hari:
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=Atur Ulang Setiap Bulan:
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=Atur Ulang Setiap Tahun:
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=Atur Ulang Setiap Minggu:
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=Atur Ulang Setiap Jam:
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=Pada pukul {0} setiap {1} hari
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=Otorisasikan kami untuk mengeksekusi rantai tugas serta tugas berulang yang telah Anda jadwalkan.
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=Eksekusi jadwal akan memuat snapshot. Dengan demikian, mode replikasi akan beralih dari replikasi waktu nyata menjadi replikasi massal, dan data sudah tidak akan bisa diperbarui lagi dalam waktu nyata.
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=Apakah Anda yakin ingin menghapus permanen jadwal yang dipilih?
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn={0} dari {1} objek terpilih telah memiliki jadwal. \nApakah Anda yakin ingin menghapus permanen jadwal?

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=Apakah Anda ingin memiliki jadwal untuk objek terpilih?
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=Jadwal ditentukan untuk {0} dari {1} objek terpilih. Apakah Anda ingin memiliki jadwal ini?

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=Apakah Anda ingin menjeda jadwal dari objek terpilih?
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn={0} dari {1} objek terpilih memiliki jadwal yang sedang dieksekusi. Apakah Anda ingin menjedanya?

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=Apakah Anda ingin melanjutkan jadwal dari objek terpilih?
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn={0} dari {1} objek terpilih memiliki jadwal yang dijeda. Apakah Anda ingin melanjutkannya?

#XTXT: Warning text
warningText=Peringatan
#XTXT: Text for schedule pause
pauseScheduleText=Jeda Jadwal
#XTXT: Text for resume schedule
resumeScheduleText=Lanjutkan Jadwal
#XTXT: Text for assigning schedule
assignScheduleText=Tetapkan Jadwal untuk Saya

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=Otorisasi untuk mengeksekusi tugas berulang Anda akan kedaluwarsa pada tanggal {0}. Untuk terus mengeksekusi tugas terjadwal Anda, kami memerlukan persetujuan Anda lagi.

#XMSG: Reauthorize text
reauthorize=Otorisasikan Ulang

#XTXT: Duration text
duration=Durasi
#XTXT: Time frame label text
timeFrame=Kerangka Waktu
#XTXT: Hours Label text
hours=Jam
#XTXT: Minutes Label text
minutes=Menit
#XTXT: Minutes Label text
minutesNew=Menit

#XTXT: Minute Recurrence type text
byMinute=Menit
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=Masukkan nilai antara 0 dan 59, atau atur frekuensi menjadi lebih dari atau sama dengan {0} menit
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=Masukkan nilai antara 0 dan 59.
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=Durasi selama {0} menit lebih lama dari frekuensi yang dijadwalkan yaitu selama {1} menit.
#XTXT: Selected time zone At
timeZoneAt=Pada
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=Dimulai pada
