#XMSG: Message to ask user to authorize
authorizeWarn=Autorícenos para ejecutar las tareas repetitivas que ha programado.
#XFLD: Authorize field value
authorise=Autorizar
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=Programar para "{0}"
#XMSG: Ok message
ok=OK
#XMSG: Schedule
schedule=Programar
#XMSG: Cancel message
cancel=Cancelar
#XMSG: Close message
close=Cerrar
#XFLD: lable to set recurrence
recurrenceLabel=Periodicidad
#XFLD: label to set the recurrence value
everyLabel=Cada
#XFLD: Start date field
startDateLabel=Fecha de inicio
#XFLD: Start date field
endDateLabel=Fecha de fin
#XFLD: TimeZone field
timeZoneLabel=Huso horario
#XMSG: validation text
recurrenceValidationText1=Indique un valor entero de 1 o superior
#XMSG: validation text
recurrenceValidationText2=Introduzca una fecha de inicio
#XMSG: validation text
recurrenceValidationText3=Introduzca una fecha de inicio
#XMSG: validation text
noObjectSelectedText=Seleccione una tabla remota para programar.
#XMSG: error text
errorLabel=Error
#XMSG: Schedule created alert message
createScheduleSuccess=Se ha creado la programación
#XMSG: Error in creating schedule
errorCreateSchedule=No se ha podido crear la programación en este momento. Vuelva a intentarlo. \N {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=Se ha actualizado la programación
#XMSG: Error in updating schedule
errorUpdateSchedule=No se ha podido actualizar la programación en este momento. Vuelva a intentarlo. \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=¿Seguro que desea eliminar la programación?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Se ha eliminado la programación.
#XMSG: Schedule deletion error message
errorSeleteSchedule=No se ha podido eliminar la programación. Vuelva a intentarlo.
#XFLD: Authorise service title
authServiceLabel=Autorizar servicio
#XMSG: Redirecting message
redirectingText=Redireccionando para autenticación.
#XMSG: Redirection success message
redirectSuccess=Nos ha autorizado correctamente para ejecutar las tareas repetitivas que ha programado.
#XMSG: Start time label
startTimeFormatLabel=Hora de inicio (formato de {0} horas)
#XMSG: Start date and end date range validation
dateRangeValidationText=La fecha de inicio debe ser anterior a la fecha de fin.
#XMSG: Delete schedule error
errorDeleteSchedule=Error al eliminar la programación \n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=Hora(s)
#XFLD: Plural Recurrence text for Day
dayPluraltext=Día(s)
#XFLD: Plural Recurrence text for Month
monthPluralText=Mes(es)
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=Horas
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=Días
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=Meses
#XFLD: Recurrence text for Hour
hourText=Cada hora
#XFLD: Recurrence text for Day
daytext=Diaria
#XFLD: Recurrence text for Month
monthText=Mensual
#XMSG: Start date cannot be in the past
startDateValidationText=La fecha de inicio no puede estar en el pasado
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=Introduzca un valor comprendido entre 1 y 23.
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=Introduzca un valor comprendido entre 1 y 31.
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=Introduzca un valor comprendido entre 1 y 12.
#XMSG: Invalid date text
invalidDateText=Fecha no válida
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=Periodicidad
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=Fecha de inicio
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=Fecha de fin
#XMSG: Schedule popover end date label
naText=N/D
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=Huso horario
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=Propietario
#XMSG: text for not available
naText=N/D
#XMSG: String Every
everyString=Cada
#XMSG: on label
onLabel=El
#XMSG: on label
onLabelMonth=Un día del mes
#XFLD: Label for Sunday
sunLabel=Dom.
#XFLD: Label for Monday
monLabel=Lun.
#XFLD: Label for Tuesday
tueLabel=Mar.
#XFLD: Label for Wednesday
wedLabel=Mié.
#XFLD: Label for Thursday
thuLabel=Jue.
#XFLD: Label for Friday
friLabel=Vie.
#XFLD: Label for Saturday
satLabel=Sáb.
#XFLD: Recurrence text for Week
weekText=Semana
#XFLD: Create Schedule Dialog Title
createDialogTitle=Crear programación
#XFLD: Edit Dialog title
editDialogTitle=Editar programación
#XFLD: Selected Object Title
selectedObjectText=Seleccionado
#XMSG: Table Text
tableText=Tabla
#XMSG: View Text
viewText=Vista
#XMSG: Data flow text
dataFlowText=Flujo de datos
#XMSG: Select a day of week error message
weekRecErrorText=Seleccione al menos un día de la semana
#XFLD: Label for Owner
ownerLabel=Propietario
#XFLD: Button label for takeover schedule
takeoverLabel=Modificar propietario
#XFLD: Created By label
createdbyLabel=Creado por
#XFLD: Changed By label
changedbyLabel=Autor de la última modificación
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=Se requiere la autenticación de usuario para ejecutar las tareas programadas en SAP Datasphere.\n Al elegir ejecutar esta tarea programada, se ejecutará con usted como propietario.\n Antes de que venza la ejecución de una tarea programada, asegúrese de haber dado su consentimiento para ejecutar tareas en su nombre.
#XMSG: Schedule takeover success message
ownerChangeSuccessText=Transferencia de programación correcta.
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=Error en la transferencia de programación.
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=La transferencia de la programación no se ha realizado porque se ha producido un error durante su actualización.
#XFLD: Text for confirm
confirm=Confirmar
#XMSG: Create schedule error message
createScheduleError=Error al crear la programación.
#XMSG: Schedule update failed message
updateScheduleError=Error al actualizar la programación.
#XMSG: Schedule delete error message
deleteScheduleError=Error al eliminar la programación.
#XFLD: Label for Frequency
frequencyLabel=Frecuencia
#XFLD: Label for Frequency
frequencyLabelNew=Opciones
#XFLD: Label for repeat
repeatLabel=Repetir
#XFLD: Label for repeat
repeatLabelNeww=Frecuencia:
#XFLD: Labe for text at
atLabel=A las
#XFLD: Label for starting
startingLabel=Desde
#XFLD: Label for Start
startLabel=Inicio
#XFLD: Label for Ending
validityLabel=Validez
#XFLD: Label for end
endLabel=Fin
#XMSG: Message for no end date
noEndDateMsg=La periodicidad se producirá indefinidamente.
#XFLD: Assign label
assignLabel=Asignar a mí
#XFLD: Overview label
overviewLabel=Resumen
#XFLD: Next runs field
nextRunsLabel=Próximas ejecuciones:
#XFLD: Next runs field
nextRunsLabelForPopOver=Próximas ejecuciones
#XFLD: Expired field
taskPopoverExpiredLabel=Vencido
#XFLD: Expired Message
taskPopoverExpiredMessage=La programación ha vencido. Para ampliarla, edítela y modifique la fecha de fin.
#XFLD: label for Day-of-month
dayOfmonthLabel=Día del mes
#XFLD: label for Minute
minuteLabel=Minuto
#XFLD: label for hour
hourLabel=Hora
#XFLD: Last Day of month selection
lastDayText=Último día del mes
#XFLD: Hourly unit description label
hourUnitLabel=Hora del día
#XFLD: Daily recurrence unit description label
dayUnitLabel=Día(s) por mes
#XFLD: Monthly receurrence unit description label
monthUnitLabel=Meses del año
#XFLD: label for off
offLabel=Desactivado
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=Se produce cada {0} hora(s) desde el {1} a los {2} minutos.
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=Se produce cada {0} día(s) desde el {1} a las {2}.
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=Se produce cada {0} mes(es) desde el {1}, el día {2} a las {3}.
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=Se produce cada semana el {0} a las {1}.
#XMSG: Schedule expired message
scheduleExpiredLabel=La programación ha vencido.
#XFLD: Create Schedule Dialog Title
createScheduleTitle=Crear programación para "{0}"
#XFLD: Edit Dialog title
createScheduleTitleNew={0}: crear programación
#XFLD: Edit Dialog title
editScheduleTitle=Editar programación para "{0}"
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}: editar programación
#XFLD: Edit Schedule Button
editSchedule=Editar programa
#XFLD: Full Label for Sunday
sunFullLabel=Domingo
#XFLD: Full Label for Monday
monFullLabel=Lunes
#XFLD: Full Label for Tuesday
tueFullLabel=Martes
#XFLD: Full Label for Wednesday
wedFullLabel=Miércoles
#XFLD: Full Label for Thursday
thuFullLabel=Jueves
#XFLD: Full Label for Friday
friFullLabel=Viernes
#XFLD: Full Label for Saturday
satFullLabel=Sábado
#XFLD: Weekly text
weeklyText=Semanal
#XFLD: Set start day text
startEndDateText=Definir fecha de inicio y de fin
#XMSG: Schedule runs indefinitely message
validityOffText=La programación se ejecutará indefinidamente.
#XFLD: Summary label
summaryLabel=Resumen
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=Se ejecuta cada {0} hora(s). El ciclo de ejecución se restablece todos los días a las 0:00 (UTC).
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=Se ejecuta cada {0} día(s) a las {1} (UTC). El ciclo de ejecución se restablece el primer día de cada mes.
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=Se ejecuta cada {0} mes(es) el {1} a las {2} (UTC). El ciclo de ejecución se restablece el primer día de cada año.
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=Se ejecuta cada semana el {0} a las {1} (UTC).
#XFLD: Future runs label with time zone info
futureRunsLabel=Ejecuciones futuras
#XFLD: Start & End Date label
startEndDateLabel=Definir fecha de inicio y de fin
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=Es posible que la primera ejecución no se ejecute ya que está a punto de comenzar en menos de 10 minutos.
#XFLD: Label for text at UTC
atUTCLabel=A las (UTC)
#XMSG: Authorize expired text
authExpiredText=La autorización para ejecutar sus tareas repetitivas ha caducado. Debe renovarla para que SAP ejecute la programación en su nombre si desea seguir programando tareas.
#XMSG: Authorization expiring soon text
authExpiringSoonText=La autorización para ejecutar sus tareas repetitivas caducará pronto. Renuévela para que SAP siga ejecutando la programación en su nombre.
#XMSG: Enter as Label text
enterAsText=Acceder como
#XMSG: Enter as Label text
enterAsTextNew=Tipo de opción
#XMSG: standard schedule label text
simpleScheduleText=Programación simple
#XMSG: Cron expression label text
cronScheduleText=Expresión Cron
#XMSG: Time Range label text
timeRangeLabelText=Intervalo de tiempo
#XMSG: Time Range label text
timeRangeLabelTextNew=Intervalo de fechas
#XMSG: Ownership label
ownershipLabelText=Propiedad
#XMSG: Show runs in the selected timezone label
showRunsInText=Mostrar ejecuciones en
#XMSG: Text for schedule pause
pauseScheduleLabel=Interrumpir programación:
#XMSG: Show Warning for schedule pause
schedulePausedWarning=Se ha interrumpido esta programación. No hay ejecuciones futuras disponibles.
#XMSG: Cycle run text for hourly
hourlyCycleResetText=El ciclo de ejecución se restablece a las 00:00 cada día.
#XMSG: Cycle run text for Daily
dailyCycleResetText=El ciclo de ejecución se restablece el primer día de cada mes.
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=El ciclo de ejecución se restablece cada 1 de enero.
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=El ciclo de ejecución se restablece cada domingo.
#XMSG: Day label
dayLabel=Día (mes)
#XMSG: Monthly label
monthlyLabel=Mes
#XMSG: Weekly label
weeklyLabel=Día (semana)
#XMSG: Time in UTC label
utcTimeLabel=Hora actual (UTC)

#XMSG: January
monthLabel1=Enero
#XMSG: February
monthLabel2=Febrero
#XMSG: March
monthLabel3=Marzo
#XMSG: April
monthLabel4=Abril
#XMSG: May
monthLabel5=Mayo
#XMSG: June
monthLabel6=Junio
#XMSG: July
monthLabel7=Julio
#XMSG: August
monthLabel8=Agosto
#XMSG: September
monthLabel9=Septiembre
#XMSG: October
monthLabel10=Octubre
#XMSG: November
monthLabel11=Noviembre
#XMSG: December
monthLabel12=Diciembre

weekdayLabel0=Domingo
weekdayLabel1=Lunes
weekdayLabel2=Martes
weekdayLabel3=Miércoles
weekdayLabel4=Jueves
weekdayLabel5=Viernes
weekdayLabel6=Sábado

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=Cada {0} minutos de {1} a {2}.
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=Cada {0} minutos.
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=Cada {0} minutos a partir de las {1}.
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=Cada {0} minutos.

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=después de cada {0} horas de {1} a {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=después de cada {0} horas
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=después de cada {0} horas a partir de las {1}
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=después de cada {0} horas

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=cada día {0} del mes de {1} a través de {2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=cada día {0} del mes
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=cada día {0} del mes a partir de {1}
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=el día del mes {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=cada mes de {0} de {1} a través de {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=cada mes {0}
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=cada mes {0} a partir de {1}
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=el {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=cada día {0} de la semana de {1} a través de {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=cada día {0} de la semana
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=cada día {0} de la semana a partir de {1}
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=el {0}
#XMSG: And text
andText=y
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=Introduzca un valor admitido o una combinaciones de valores:
#XMSG: default information about cron string
defaultCronStringInfoText=Valores admitidos para la expresión cron: <strong>números * , / </strong>
#XMSG: Parameterized info about cron string
cronStringinfoText=Valores admitidos para el campo seleccionado: <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=Más información
#XMSG: Hide details text
hideDetailsText=Ocultar detalles
#XMSG: Label for starting at UTC Text
startingText=A partir de las (UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=Copiar en el portapapeles
#XMSG: cron copied to clipboard
cronCopiedText=Expresión cron copiada en el portapapeles
#XMSG: error message for invalid cron copy
invalidCronCopyText=No se puede copiar una expresión cron no válida
#XMSG: No preview label
noPreviewText=No podemos realizar una vista previa porque hay errores o faltan valores. Revise la programación.
#XMSG: Label for create button
createText=Crear
#XMSG: Label for Save button
saveText=Guardar
#XMSG: Label for text leave
leaveText=Salir
#XMSG: warn message when user click cancel button
cancelWarnText=Las modificaciones se perderán cuando salga de esta página.
#XMSG: Expired schedule warning
expiredTaskWarnText=Esta programación ha caducado. Seleccione una fecha de fin nueva para seguir con la replicación.
#XMSG: Expired schedule message
expiredScheduleMessageText=Esta programación ha caducado. Seleccione una fecha de fin nueva para seguir ejecutando las tareas repetitivas.
#XMSG: Expired schedule message
expiredTaskWarnTextNew=Esta programación ha vencido. Para ampliarla, modifique la fecha de fin.
#XMSG: Expired schedule Text
expiredScheduleText=Vencido
#XMSG: No future runs generated
noFutureRunsText=No hay ninguna ejecución siguiente disponible.
#XMSG: Text for Local time
localTimeText=Hora local
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=No hay ejecuciones en el intervalo de fechas seleccionado.
#XMSG: Warning text for expired end date
endDateExpiredText=La fecha de fin está en el pasado.
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=Especifique una fecha de inicio.
#XMSG: UTC info label
utcInfoLabelText=Las programaciones se crean en Tiempo Universal Coordinado (UTC). La hora UTC actual es {0}.
#XMSG: UTC info label
utcInfoLabelTextNew=Tiempo Universal Coordinado (UTC). La hora UTC actual es {0}.
#XMSG: Invalid cron paste error message
invalidCronPasteText=No se puede pegar la expresión cron porque no es válida. Vuelva a intentarlo con una expresión válida.
#XMSG: Weekly schedule error text
invalidWeeklyRecText=Seleccione como mínimo un día.
#XFLD: No End date text
noEndDateText=Sin fecha de fin
#XFLD: Help Button
openHelpButtonText=Mostrar ayuda
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=La fecha supera el número máximo de días para el mes indicado
#XMSG: Redundancy error in cron string
cronRedundantErrorText=Este patrón tiene expresiones redundantes. Quite los patrones: * o / de la lista separada por comas.
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=Introduzca un valor comprendido entre 0 y 59.
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=Introduzca un valor superior a 10.
#XMSG: Error message for missing start time
startTimeErrorText=Seleccione una hora.
#XMSG: Error message handling for error code
invalidScheduleBodyError=El cuerpo de la solicitud contiene datos no válidos.
#XMSG: Error message handling for error code
scheduleNotFoundError=La programación no existe.
#XMSG: Error message handling for error code
actionForbiddenError=No tiene el permiso necesario.
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=Está intentando crear una programación que ya existe.
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=Restablecer cada día
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=Restablecer cada mes
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=Restablecer cada año
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=Restablecer cada semana
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=Restablecer cada hora
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=Restablecer cada día:
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=Restablecer cada mes:
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=Restablecer cada año:
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=Restablecer cada semana:
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=Restablecer cada hora:
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=A las {0} cada {1} día
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=Autorícenos para ejecutar cadenas de tareas y tareas repetitivas que ha programado.
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=Una ejecución programada cargará una instantánea. El modo de replicación cambiará de en tiempo real a en lote y los datos ya no se actualizarán en tiempo real.
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=¿Seguro que desea eliminar las planificaciones seleccionadas?
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn={0} de los {1} objetos seleccionados tienen programaciones. \\u00BFSeguro que desea eliminar las programaciones?

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=¿Desea asumir la propiedad de las programaciones de los objetos seleccionados?
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=Se han definido programaciones para {0} de los {1} objetos seleccionados. ¿Desea asumir la propiedad de estas programaciones?

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=¿Desea interrumpir las programaciones de los objetos seleccionados?
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn={0} de los {1} objetos seleccionados tienen programaciones en curso. ¿Desea interrumpirlas?

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=¿Desea reanudar las programaciones de los objetos seleccionados?
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn={0} de los {1} objetos seleccionados tienen programaciones interrumpidas. ¿Desea reanudarlas?

#XTXT: Warning text
warningText=Advertencia
#XTXT: Text for schedule pause
pauseScheduleText=Interrumpir programación
#XTXT: Text for resume schedule
resumeScheduleText=Reanudar programación
#XTXT: Text for assigning schedule
assignScheduleText=Asignarme la programación

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=La autorización para ejecutar sus tareas periódicas vencerá el {0}. Para seguir ejecutando las tareas programadas, necesitamos de nuevo su consentimiento.

#XMSG: Reauthorize text
reauthorize=Volver a autorizar

#XTXT: Duration text
duration=Duración
#XTXT: Time frame label text
timeFrame=Período de tiempo
#XTXT: Hours Label text
hours=hora(s)
#XTXT: Minutes Label text
minutes=minuto(s)
#XTXT: Minutes Label text
minutesNew=minutos

#XTXT: Minute Recurrence type text
byMinute=minutos
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=Introduzca un valor comprendido entre 0 y 59 o fije la frecuencia en {0} minutos o más.
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=Introduzca un valor comprendido entre 0 y 59.
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=La duración de {0} minutos es mayor que la frecuencia programada de {1} minutos.
#XTXT: Selected time zone At
timeZoneAt=A las
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=Comienza a las
