#XMSG: Message to ask user to authorize
authorizeWarn=Rhowch awdurdod i ni redeg tasgau rheolaidd rydych wedi'u hamserlennu.
#XFLD: Authorize field value
authorise=Awdurdodi
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=Amserlennu ar gyfer "{0}"
#XMSG: Ok message
ok=Iawn
#XMSG: Schedule
schedule=Amserlen
#XMSG: Cancel message
cancel=Canslo
#XMSG: Close message
close=Cau
#XFLD: lable to set recurrence
recurrenceLabel=Ailddigwydd
#XFLD: label to set the recurrence value
everyLabel=Pob
#XFLD: Start date field
startDateLabel=Dyddiad Cychwyn
#XFLD: Start date field
endDateLabel=Dyddiad Gorffen
#XFLD: TimeZone field
timeZoneLabel=Cylchfa amser
#XMSG: validation text
recurrenceValidationText1=Rhowch werth cyfanrif o 1 neu fwy
#XMSG: validation text
recurrenceValidationText2=Rhowch ddyddiad cychwyn
#XMSG: validation text
recurrenceValidationText3=Rhowch ddyddiad cychwyn
#XMSG: validation text
noObjectSelectedText=Dewiswch dabl pell i'w amserlennu.
#XMSG: error text
errorLabel=Gwall
#XMSG: Schedule created alert message
createScheduleSuccess=Amserlen wedi'i chreu
#XMSG: Error in creating schedule
errorCreateSchedule=Doedd dim modd creu''r amserlen ar hyn o bryd. Rhowch gynnig arall arni. \N {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=Amserlen wedi'i diweddaru
#XMSG: Error in updating schedule
errorUpdateSchedule=Doedd dim modd diweddaru''r amserlen ar hyn o bryd. Rhowch gynnig arall arni. \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=Ydych chi'n siŵr eich bod am ddileu'r amserlen?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Amserlen wedi'i dileu.
#XMSG: Schedule deletion error message
errorSeleteSchedule=Doedd dim modd dileu'r amserlen ar hyn o bryd. Rhowch gynnig arall arni.
#XFLD: Authorise service title
authServiceLabel=Awdurdodi Gwasanaeth
#XMSG: Redirecting message
redirectingText=Ailgyfeirio ar gyfer awdurdodi.
#XMSG: Redirection success message
redirectSuccess=Rydych wedi llwyddo i'n hawdurdodi ni i redeg tasgau rheolaidd rydych wedi'u hamserlennu.
#XMSG: Start time label
startTimeFormatLabel=Amser Cychwyn (Fformat {0}-Awr)
#XMSG: Start date and end date range validation
dateRangeValidationText=Rhaid i'r dyddiad cychwyn fod yn is (cyn) y dyddiad gorffen.
#XMSG: Delete schedule error
errorDeleteSchedule=Gwall wrth ddileu''r amserlen \n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=Awr/Oriau
#XFLD: Plural Recurrence text for Day
dayPluraltext=Diwrnod(au)
#XFLD: Plural Recurrence text for Month
monthPluralText=Mis(oedd)
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=Oriau
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=Diwrnod
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=Mis
#XFLD: Recurrence text for Hour
hourText=Fesul Awr
#XFLD: Recurrence text for Day
daytext=Fesul Diwrnod
#XFLD: Recurrence text for Month
monthText=Fesul Mis
#XMSG: Start date cannot be in the past
startDateValidationText=Ni all y dyddiad cychwyn fod yn y gorffennol.
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=Rhowch werth rhwng 1 a 23.
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=Rhowch werth rhwng 1 a 31.
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=Rhowch werth rhwng 1 a 12.
#XMSG: Invalid date text
invalidDateText=Dyddiad annilys
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=Ailddigwydd
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=Dyddiad Cychwyn
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=Dyddiad Gorffen
#XMSG: Schedule popover end date label
naText=Ddim ar gael
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=Cylchfa amser
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=Perchennog
#XMSG: text for not available
naText=AMH
#XMSG: String Every
everyString=Pob
#XMSG: on label
onLabel=Ymlaen
#XMSG: on label
onLabelMonth=Ar Ddiwrnod o'r Mis
#XFLD: Label for Sunday
sunLabel=Sul
#XFLD: Label for Monday
monLabel=Llun
#XFLD: Label for Tuesday
tueLabel=Maw
#XFLD: Label for Wednesday
wedLabel=Mer
#XFLD: Label for Thursday
thuLabel=Iau
#XFLD: Label for Friday
friLabel=Gwen
#XFLD: Label for Saturday
satLabel=Sad
#XFLD: Recurrence text for Week
weekText=Wythnos
#XFLD: Create Schedule Dialog Title
createDialogTitle=Creu Amserlen
#XFLD: Edit Dialog title
editDialogTitle=Golygu Amserlen
#XFLD: Selected Object Title
selectedObjectText=Wedi Dewis
#XMSG: Table Text
tableText=Tabl
#XMSG: View Text
viewText=Gwedd
#XMSG: Data flow text
dataFlowText=Llif Data
#XMSG: Select a day of week error message
weekRecErrorText=Dewiswch o leiaf un diwrnod yr wythnos
#XFLD: Label for Owner
ownerLabel=Perchennog
#XFLD: Button label for takeover schedule
takeoverLabel=Newid Perchennog
#XFLD: Created By label
createdbyLabel=Wedi'i Greu Gan
#XFLD: Changed By label
changedbyLabel=Addaswyd Ddiwethaf gan
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=Mae angen dilysu defnyddwyr i redeg tasgau sydd wedi'u hamserlennu yn SAP Datasphere.\n Trwy ddewis rhedeg y dasg hon, bydd y dasg hon sydd wedi'i hamserlennu'n cael ei rhedeg gyda chi fel y perchennog.\n Cyn bod tasg wedi'i hamserlennu i fod i gael ei rhedeg, gwnewch yn siŵr eich bod wedi rhoi eich caniatâd i redeg tasgau ar eich rhan.
#XMSG: Schedule takeover success message
ownerChangeSuccessText=Wedi llwyddo i gymryd yr amserlen hon.
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=Wedi methu â chymryd yr amserlen hon.
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=Wedi methu â chymryd yr amserlen hon oherwydd bod gwall wrth ddiweddaru'r tabl.
#XFLD: Text for confirm
confirm=Cadarnhau
#XMSG: Create schedule error message
createScheduleError=Wedi methu creu'r amserlen.
#XMSG: Schedule update failed message
updateScheduleError=Wedi methu diweddaru'r amserlen.
#XMSG: Schedule delete error message
deleteScheduleError=Wedi methu dileu'r amserlen.
#XFLD: Label for Frequency
frequencyLabel=Amlder
#XFLD: Label for Frequency
frequencyLabelNew=Gosodiadau
#XFLD: Label for repeat
repeatLabel=Ailadrodd
#XFLD: Label for repeat
repeatLabelNeww=Amlder:
#XFLD: Labe for text at
atLabel=Am
#XFLD: Label for starting
startingLabel=Yn Dechrau
#XFLD: Label for Start
startLabel=Dechrau
#XFLD: Label for Ending
validityLabel=Dilysrwydd
#XFLD: Label for end
endLabel=Gorffen
#XMSG: Message for no end date
noEndDateMsg=Bydd yr ailadrodd yn digwydd am gyfnod amhenodol
#XFLD: Assign label
assignLabel=Neilltuo i Mi
#XFLD: Overview label
overviewLabel=Trosolwg
#XFLD: Next runs field
nextRunsLabel=Rhediadau Nesaf:
#XFLD: Next runs field
nextRunsLabelForPopOver=Rhediad Nesaf
#XFLD: Expired field
taskPopoverExpiredLabel=Wedi Dod i Ben
#XFLD: Expired Message
taskPopoverExpiredMessage=Mae'r amserlen wedi dod i ben. I'w ymestyn, ewch ati i olygu'r amserlen a newid y dyddiad gorffen.
#XFLD: label for Day-of-month
dayOfmonthLabel=Diwrnod o'r Mis
#XFLD: label for Minute
minuteLabel=Munud
#XFLD: label for hour
hourLabel=Awr
#XFLD: Last Day of month selection
lastDayText=Diwrnod Olaf y Mis
#XFLD: Hourly unit description label
hourUnitLabel=Awr o'r Dydd
#XFLD: Daily recurrence unit description label
dayUnitLabel=Diwrnod(au) fesul Mis
#XFLD: Monthly receurrence unit description label
monthUnitLabel=Misoedd y Flwyddyn
#XFLD: label for off
offLabel=I Ffwrdd
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=Mae''n digwydd bob {0} awr yn dechrau {1} ar {2} munud.
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=Mae''n digwydd bob {0} diwrnod yn dechrau {1} am {2}.
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=Mae''n digwydd bob {0} mis yn dechrau {1} ar {2} am {3}.
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=Mae''n digwydd bob wythnos ar {0} am {1}.
#XMSG: Schedule expired message
scheduleExpiredLabel=Mae'r amserlen wedi dod i ben.
#XFLD: Create Schedule Dialog Title
createScheduleTitle=Creu Amserlen ar gyfer "{0}"
#XFLD: Edit Dialog title
createScheduleTitleNew={0}: Creu Amserlen
#XFLD: Edit Dialog title
editScheduleTitle=Golygu Amserlen ar gyfer "{0}"
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}: Golygu Amserlen
#XFLD: Edit Schedule Button
editSchedule=Golygu Amserlen
#XFLD: Full Label for Sunday
sunFullLabel=Dydd Sul
#XFLD: Full Label for Monday
monFullLabel=Dydd Llun
#XFLD: Full Label for Tuesday
tueFullLabel=Dydd Mawrth
#XFLD: Full Label for Wednesday
wedFullLabel=Dydd Mercher
#XFLD: Full Label for Thursday
thuFullLabel=Dydd Iau
#XFLD: Full Label for Friday
friFullLabel=Dydd Gwener
#XFLD: Full Label for Saturday
satFullLabel=Dydd Sadwrn
#XFLD: Weekly text
weeklyText=Fesul Wythnos
#XFLD: Set start day text
startEndDateText=Gosod Dyddiad Dechrau a Gorffen
#XMSG: Schedule runs indefinitely message
validityOffText=Bydd yr amserlen yn rhedeg am gyfnod amhenodol.
#XFLD: Summary label
summaryLabel=Crynodeb
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=Yn rhedeg pob {0} Awr. Mae’r gylched redeg yn cael ei hailosod pob dydd am 0:00 (UTC).
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=Yn rhedeg pob {0} Diwrnod am {1} (UTC). Mae’r gylched redeg yn cael ei hailosod ar ddiwrnod cyntaf pobl mis.
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=Yn rhedeg pob {0} Mis ar {1} am {2} (UTC). Mae’r gylched redeg yn cael ei hailosod ar ddiwrnod cyntaf pob blwyddyn.
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=Yn rhedeg pob wythnos ar {0} am {1} (UTC).
#XFLD: Future runs label with time zone info
futureRunsLabel=Rhedeg yn y Dyfodol:
#XFLD: Start & End Date label
startEndDateLabel=Gosod Dyddiad Dechrau a Gorffen
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=Efallai na fydd y rhediad cyntaf yn gweithredu gan ei fod ar fin dechrau mewn llai na 10 munud.
#XFLD: Label for text at UTC
atUTCLabel=Am (UTC)
#XMSG: Authorize expired text
authExpiredText=Mae awdurdodiad i redeg eich tasgau cylchol wedi dod i ben. Mae angen i chi adnewyddu'r awdurdodiad i ganiatáu i SAP redeg yr amserlen ar eich rhan os ydych chi am barhau i amserlennu tasgau.
#XMSG: Authorization expiring soon text
authExpiringSoonText=Bydd yr awdurdodiad i redeg eich tasgau cylchol yn dod i ben yn fuan. Adnewyddwch yr awdurdodiad i ganiatáu i SAP barhau i redeg yr amserlen ar eich rhan.
#XMSG: Enter as Label text
enterAsText=Rhoi Fel
#XMSG: Enter as Label text
enterAsTextNew=Math o Osodiad
#XMSG: standard schedule label text
simpleScheduleText=Amserlen Syml
#XMSG: Cron expression label text
cronScheduleText=Mynegiant Cron
#XMSG: Time Range label text
timeRangeLabelText=Ystod Amser
#XMSG: Time Range label text
timeRangeLabelTextNew=Ystod Dyddiadau
#XMSG: Ownership label
ownershipLabelText=Perchnogaeth
#XMSG: Show runs in the selected timezone label
showRunsInText=Dangos Rhediadau Yn
#XMSG: Text for schedule pause
pauseScheduleLabel=Rhewi Amserlen:
#XMSG: Show Warning for schedule pause
schedulePausedWarning=Mae'r amserlen wedi rhewi. Does dim rhediadau yn y dyfodol ar gael.
#XMSG: Cycle run text for hourly
hourlyCycleResetText=Mae’r gylched redeg yn cael ei hailosod am 00:00 bob dydd.
#XMSG: Cycle run text for Daily
dailyCycleResetText=Mae’r gylched redeg yn cael ei hailosod ar ddiwrnod cyntaf pob mis.
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=Mae’r gylched redeg yn cael ei hailosod bob blwyddyn ar 1 Ionawr.
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=Mae’r gylched redeg yn cael ei hailosod bob wythnos ar ddydd Sul.
#XMSG: Day label
dayLabel=Diwrnod (Mis)
#XMSG: Monthly label
monthlyLabel=Mis
#XMSG: Weekly label
weeklyLabel=Diwrnod (Wythnos)
#XMSG: Time in UTC label
utcTimeLabel=Amser Presennol (UTC)

#XMSG: January
monthLabel1=Ionawr
#XMSG: February
monthLabel2=Chwefror
#XMSG: March
monthLabel3=Mawrth
#XMSG: April
monthLabel4=Ebrill
#XMSG: May
monthLabel5=Mai
#XMSG: June
monthLabel6=Mehefin
#XMSG: July
monthLabel7=Gorffennaf
#XMSG: August
monthLabel8=Awst
#XMSG: September
monthLabel9=Medi
#XMSG: October
monthLabel10=Hydref
#XMSG: November
monthLabel11=Tachwedd
#XMSG: December
monthLabel12=Rhagfyr

weekdayLabel0=Dydd Sul
weekdayLabel1=Dydd Llun
weekdayLabel2=Dydd Mawrth
weekdayLabel3=Dydd Mercher
weekdayLabel4=Dydd Iau
weekdayLabel5=Dydd Gwener
weekdayLabel6=Dydd Sadwrn

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=Bob {0} munud o {1} tan {2}.
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=Bob {0} munud.
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=Bob {0} munud yn cychwyn am {1}.
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=Bob {0} munud.

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=ar ôl bob {0} awr o {1} tan {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=ar ôl bob {0} awr
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=ar ôl bob {0} awr yn cychwyn {1}
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=ar ôl awr {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=bob {0} diwrnod o''r mis o {1} tan {2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=bob {0} diwrnod o''r mis
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=bob {0} diwrnod o''r mis yn dechrau {1}
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=ar ddiwrnod o''r mis {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=bob {0} mis o {1} tan {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=bob {0} mis
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=bob {0} mis yn dechrau {1}
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=yn {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=bob {0} diwrnod o''r wythnos o {1} tan {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=bob {0} diwrnod o''r wythnos
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=bob {0} diwrnod o''r wythnos yn dechrau {1}
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=ar {0}
#XMSG: And text
andText=a
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=Rhowch werth neu gyfuniad o werthoedd y mae modd delio â nhw:
#XMSG: default information about cron string
defaultCronStringInfoText=Gwerthoedd y mae modd delio â nhw ar gyfer mynegiant cron: <strong>rhifau* , - /</strong>.
#XMSG: Parameterized info about cron string
cronStringinfoText=Gwerthoed dy mae modd delio â nhw ar gyfer y maes dan sylw: <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=Dysgu Mwy
#XMSG: Hide details text
hideDetailsText=Cuddio manylion
#XMSG: Label for starting at UTC Text
startingText=Dechrau am (UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=Copïo i’r Clipfwrdd
#XMSG: cron copied to clipboard
cronCopiedText=Wedi copïo'r mynegiant i'r clipfwrdd
#XMSG: error message for invalid cron copy
invalidCronCopyText=Dim modd copïo mynegiant cron annilys
#XMSG: No preview label
noPreviewText=Ni allwn arddangos rhagolwg gan fod gwallau neu werthoedd ar goll. Adolygwch yr amserlen.
#XMSG: Label for create button
createText=Creu
#XMSG: Label for Save button
saveText=Cadw
#XMSG: Label for text leave
leaveText=Gadael
#XMSG: warn message when user click cancel button
cancelWarnText=Bydd eich newidiadau'n cael eu colli pan fyddwch yn gadael.
#XMSG: Expired schedule warning
expiredTaskWarnText=Mae'r amserlen hon wedi dod i ben. Dewiswch ddyddiad gorffen newydd i barhau â'r dyblygu.
#XMSG: Expired schedule message
expiredScheduleMessageText=Mae'r amserlen hon wedi dod i ben. Dewiswch ddyddiad gorffen newydd i barhau â'r dyblygu.
#XMSG: Expired schedule message
expiredTaskWarnTextNew=Mae'r amserlen hwn wedi dod i ben. I'w ymestyn, ewch ati i newid y dyddiad gorffen.
#XMSG: Expired schedule Text
expiredScheduleText=Wedi Dod i Ben
#XMSG: No future runs generated
noFutureRunsText=Does dim rhediad nesaf ar gael.
#XMSG: Text for Local time
localTimeText=Amser Lleol
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=Does dim rediadau o fewn yr ystod dyddiadau a ddewiswyd.
#XMSG: Warning text for expired end date
endDateExpiredText=Mae'r dyddiad gorffen yn y gorffennol.
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=Rhowch ddyddiad cychwyn.
#XMSG: UTC info label
utcInfoLabelText=Mae amserlenni’n cael eu creu yn Coordinated Universal Time (UTC). Yr amser UTC ar hyn o bryd yw {0}.
#XMSG: UTC info label
utcInfoLabelTextNew=Coordinated Universal Time (UTC). Yr amser UTC ar hyn o bryd yw {0}.
#XMSG: Invalid cron paste error message
invalidCronPasteText=Does dim modd i ni ludo’r mynegiant cron gan ei fod yn annilys. Rhowch gynnig arall arni gyda mynegiant dilys.
#XMSG: Weekly schedule error text
invalidWeeklyRecText=Dewiswch o leiaf un dyddiad.
#XFLD: No End date text
noEndDateText=Dim Dyddiad Gorffen
#XFLD: Help Button
openHelpButtonText=Dangos Cymorth
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=Mae’r dyddiad yn uwch na nifer y dyddiau yn y mis dan sylw.
#XMSG: Redundancy error in cron string
cronRedundantErrorText=Mae gan y patrwm hwn ymadroddion diangen. Tynnwch batrymau: * neu / oddi ar y rhestr sydd wedi'i gwahanu gan goma.
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=Rhowch werth rhwng 0 a 59.
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=Rhowch werth sy'n fwy na 10.
#XMSG: Error message for missing start time
startTimeErrorText=Dewiswch Amser.
#XMSG: Error message handling for error code
invalidScheduleBodyError=Mae data annilys yng nghorff y cais.
#XMSG: Error message handling for error code
scheduleNotFoundError=Methu dod o hyd i'r amserlen.
#XMSG: Error message handling for error code
actionForbiddenError=Does gennych chi ddim y caniatâd angenrheidiol.
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=Rydych chi’n ceisio creu amserlen sydd eisoes yn bodoli.
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=Ailosod bob diwrnod
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=Ailosod bob mis
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=Ailosod bob blwyddyn
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=Ailosod bob wythnos
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=Ailosod bob awr
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=Ailosod Bob Diwrnod:
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=Ailosod Mob Mis:
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=Ailosod Bob Blwyddyn:
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=Ailosod Bob Wythnos:
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=Ailosod Bob Awr:
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=Am {0} ar bob {1} diwrnod
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=Ein hawdurdodi ni i redeg cadwyni tasgau yn ogystal â thasgau rheolaidd rydych wedi'u hamserlennu.
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=Bydd rhediad wedi’i drefnu yn llwytho ciplun. Bydd y modd dyblygu yn newid o ddyblygu amser real i ddyblygu swp, ac ni fydd data’n cael ei ddiweddaru mewn amser real.
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=Ydych chi'n siŵr eich bod am ddileu'r amserlenni a ddewiswyd?
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn=Mae gan {0} o''r {1} gwrthrych dan sylw amserlenni. \nYdych chi''n siŵr eich bod am ddileu''r amserlenni?

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=Ydych chi am fod yn berchen ar yr amserlenni ar gyfer y gwrthrychau dan sylw?
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=Mae amserlenni yn cael eu diffinio ar gyfer {0} o''r {1} gwrthrych dan sylw. Ydych chi am fod yn berchen ar yr amserlenni hyn?

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=Ydych chi am rewi amserlenni'r gwrthrychau dan sylw?
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn=Mae gan {0} o''r {1} gwrthrych dan sylw amserlenni ar waith. Ydych chi am eu rhewi?

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=Ydych chi am ailgychwyn amserlenni'r gwrthrychau dan sylw?
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn=Mae gan {0} o''r {1} gwrthrych dan sylw amserlenni wedi''u rhewi. Ydych chi am eu hailgychwyn?

#XTXT: Warning text
warningText=Rhybudd
#XTXT: Text for schedule pause
pauseScheduleText=Rhewi Amserlen
#XTXT: Text for resume schedule
resumeScheduleText=Ailgychwyn Amserlen
#XTXT: Text for assigning schedule
assignScheduleText=Neilltuo Amserlen i Mi

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=Bydd yr awdurdodiad i redeg eich tasgau cylchol yn dod i ben ar {0}. I barhau i redeg eich tasgau sydd wedi''u hamserlennu, mae angen i ni gael eich caniatâd eto.

#XMSG: Reauthorize text
reauthorize=Ailawdurdodi

#XTXT: Duration text
duration=Hyd
#XTXT: Time frame label text
timeFrame=Cyfnod Amser
#XTXT: Hours Label text
hours=Awr/Oriau
#XTXT: Minutes Label text
minutes=Munud
#XTXT: Minutes Label text
minutesNew=Munud

#XTXT: Minute Recurrence type text
byMinute=Munud
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=Rhowch werth rhwng 0 a 59, neu gosodwch amlder i fwy na {0} munud neu''n cyfateb i hynny
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=Rhowch werth rhwng 0 a 59.
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=Mae hyd o {0} munud yn hirach na''r amlder wedi''i drefnu o {1} munud.
#XTXT: Selected time zone At
timeZoneAt=Am
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=Dechrau am
