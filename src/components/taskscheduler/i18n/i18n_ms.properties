#XMSG: Message to ask user to authorize
authorizeWarn=Benarkan kami untuk jalankan tugas berulang yang anda telah jadualkan.
#XFLD: Authorize field value
authorise=Benarkan
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=Jadual untuk "{0}"
#XMSG: Ok message
ok=OK
#XMSG: Schedule
schedule=Jadual
#XMSG: Cancel message
cancel=Batalkan
#XMSG: Close message
close=Tutup
#XFLD: lable to set recurrence
recurrenceLabel=Perulangan
#XFLD: label to set the recurrence value
everyLabel=Setiap
#XFLD: Start date field
startDateLabel=<PERSON>rikh Mula
#XFLD: Start date field
endDateLabel=Tarikh Akhir
#XFLD: TimeZone field
timeZoneLabel=Zon Waktu
#XMSG: validation text
recurrenceValidationText1=Masukkan 1 atau lebih tinggi bagi nilai integer
#XMSG: validation text
recurrenceValidationText2=Masukkan tarikh mula
#XMSG: validation text
recurrenceValidationText3=Masukkan tarikh mula
#XMSG: validation text
noObjectSelectedText=Pilih jadual jauh untuk jadual.
#XMSG: error text
errorLabel=Ralat
#XMSG: Schedule created alert message
createScheduleSuccess=Jadual dicipta
#XMSG: Error in creating schedule
errorCreateSchedule=Tidak boleh cipta jadual sekarang. Cuba lagi. \N {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=Jadual dikemas kini
#XMSG: Error in updating schedule
errorUpdateSchedule=Tidak boleh kemas kini jadual sekarang. Cuba lagi. \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=Adakah anda pasti ingin padam jadual?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Jadual dipadam.
#XMSG: Schedule deletion error message
errorSeleteSchedule=Tidak boleh memadam jadual sekarang. Cuba lagi.
#XFLD: Authorise service title
authServiceLabel=Benarkan Perkhidmatan
#XMSG: Redirecting message
redirectingText=Penghalaan semula sah kuasa.
#XMSG: Redirection success message
redirectSuccess=Anda berjaya benarkan kami untuk jalankan tugas berulang yang anda telah jadualkan.
#XMSG: Start time label
startTimeFormatLabel=Masa Mula (Format {0} Jam)
#XMSG: Start date and end date range validation
dateRangeValidationText=Tarikh mula mestilah lebih rendah (lebih awal) daripada tarikh akhir.
#XMSG: Delete schedule error
errorDeleteSchedule=Ralat semasa memadam jadual \n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=Jam
#XFLD: Plural Recurrence text for Day
dayPluraltext=Hari
#XFLD: Plural Recurrence text for Month
monthPluralText=Bulan
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=Jam
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=Hari
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=Bulan
#XFLD: Recurrence text for Hour
hourText=Mengikut Jam
#XFLD: Recurrence text for Day
daytext=Harian
#XFLD: Recurrence text for Month
monthText=Bulanan
#XMSG: Start date cannot be in the past
startDateValidationText=Tarikh mula tidak boleh pada masa lalu
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=Masukkan nilai antara 1 hingga 23.
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=Masukkan nilai antara 1 hingga 31.
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=Masukkan nilai antara 1 hingga 12.
#XMSG: Invalid date text
invalidDateText=Tarikh tidak sah
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=Perulangan
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=Tarikh Mula
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=Tarikh Akhir
#XMSG: Schedule popover end date label
naText=NA
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=Zon Waktu
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=Pemilik
#XMSG: text for not available
naText=Tidak Tersedia
#XMSG: String Every
everyString=Setiap
#XMSG: on label
onLabel=Pada
#XMSG: on label
onLabelMonth=Pada Hari dalam Bulan
#XFLD: Label for Sunday
sunLabel=Ahd
#XFLD: Label for Monday
monLabel=Isn
#XFLD: Label for Tuesday
tueLabel=Sel
#XFLD: Label for Wednesday
wedLabel=Rab
#XFLD: Label for Thursday
thuLabel=Kha
#XFLD: Label for Friday
friLabel=Jum
#XFLD: Label for Saturday
satLabel=Sab
#XFLD: Recurrence text for Week
weekText=Minggu
#XFLD: Create Schedule Dialog Title
createDialogTitle=Cipta Jadual
#XFLD: Edit Dialog title
editDialogTitle=Edit Jadual
#XFLD: Selected Object Title
selectedObjectText=Terpilih
#XMSG: Table Text
tableText=Jadual
#XMSG: View Text
viewText=Paparan
#XMSG: Data flow text
dataFlowText=Aliran Data
#XMSG: Select a day of week error message
weekRecErrorText=Pilih sekurang-kurangnya sehari dalam seminggu
#XFLD: Label for Owner
ownerLabel=Pemilik
#XFLD: Button label for takeover schedule
takeoverLabel=Ubah Pemilik
#XFLD: Created By label
createdbyLabel=Dicipta oleh
#XFLD: Changed By label
changedbyLabel=Diubah Suai Terakhir oleh
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=Pengesahan pengguna diperlukan untuk menjalankan tugas berjadual dalam SAP Datasphere.\n Dengan memilih untuk menjalankan tugas ini, tugas berjadual ini akan dijalankan dengan anda sebagai pemilik.\n Sebelum tugasan dijadualkan akan dijalankan, pastikan anda telah memberikan persetujuan anda untuk menjalankan tugas bagi pihak anda.
#XMSG: Schedule takeover success message
ownerChangeSuccessText=Pengambilalihan jadual berjaya.
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=Pengambilalihan jadual tidak berjaya.
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=Pengambilalihan jadual tidak berjaya kerana ralat dalam jadual pengemaskinian.
#XFLD: Text for confirm
confirm=Sahkan
#XMSG: Create schedule error message
createScheduleError=Penciptaan jadual tidak berjaya.
#XMSG: Schedule update failed message
updateScheduleError=Pengemaskinian jadual tidak berjaya.
#XMSG: Schedule delete error message
deleteScheduleError=Pemadaman jadual tidak berjaya.
#XFLD: Label for Frequency
frequencyLabel=Kekerapan
#XFLD: Label for Frequency
frequencyLabelNew=Tetapan
#XFLD: Label for repeat
repeatLabel=Ulang
#XFLD: Label for repeat
repeatLabelNeww=Kekerapan:
#XFLD: Labe for text at
atLabel=Pada
#XFLD: Label for starting
startingLabel=Bermula
#XFLD: Label for Start
startLabel=Mula
#XFLD: Label for Ending
validityLabel=Kesahan
#XFLD: Label for end
endLabel=Tamat
#XMSG: Message for no end date
noEndDateMsg=Pengulangan akan berlaku selama-lamanya.
#XFLD: Assign label
assignLabel=Umpukkan kepada Saya
#XFLD: Overview label
overviewLabel=Paparan keseluruhan
#XFLD: Next runs field
nextRunsLabel=Jalanan Seterusnya:
#XFLD: Next runs field
nextRunsLabelForPopOver=Jalanan Seterusnya
#XFLD: Expired field
taskPopoverExpiredLabel=Tamat Tempoh
#XFLD: Expired Message
taskPopoverExpiredMessage=Jadual telah tamat tempoh. Untuk melanjutkannya, edit jadual dan ubah tarikh akhir.
#XFLD: label for Day-of-month
dayOfmonthLabel=Hari bagi Bulan
#XFLD: label for Minute
minuteLabel=Minit
#XFLD: label for hour
hourLabel=Jam
#XFLD: Last Day of month selection
lastDayText=Hari Terakhir bagi Bulan
#XFLD: Hourly unit description label
hourUnitLabel=Jam Bagi Bulan
#XFLD: Daily recurrence unit description label
dayUnitLabel=Hari setiap Bulan
#XFLD: Monthly receurrence unit description label
monthUnitLabel=Bulan bagi Tahun
#XFLD: label for off
offLabel=Nyahaktifkan
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=Berlaku setiap {0} jam bermula pada {1} pada {2} minit.
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=Berlaku setiap {0} hari bermula pada {1} pada {2}.
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=Berlaku setiap {0} bulan bermula pada {1} pada {2} pada {3}.
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=Berlaku setiap minggu pada {0} pada {1}.
#XMSG: Schedule expired message
scheduleExpiredLabel=Jadual telah tamat tempoh.
#XFLD: Create Schedule Dialog Title
createScheduleTitle=Cipta Jadual untuk "{0}"
#XFLD: Edit Dialog title
createScheduleTitleNew={0}: Cipta Jadual
#XFLD: Edit Dialog title
editScheduleTitle=Edit Jadual untuk "{0}"
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}: Edit Jadual
#XFLD: Edit Schedule Button
editSchedule=Edit Jadual
#XFLD: Full Label for Sunday
sunFullLabel=Ahad
#XFLD: Full Label for Monday
monFullLabel=Isnin
#XFLD: Full Label for Tuesday
tueFullLabel=Selasa
#XFLD: Full Label for Wednesday
wedFullLabel=Rabu
#XFLD: Full Label for Thursday
thuFullLabel=Khamis
#XFLD: Full Label for Friday
friFullLabel=Jumaat
#XFLD: Full Label for Saturday
satFullLabel=Sabtu
#XFLD: Weekly text
weeklyText=Mingguan
#XFLD: Set start day text
startEndDateText=Tetapkan Tarikh Mula dan Akhir
#XMSG: Schedule runs indefinitely message
validityOffText=Jadual akan berjalan selama-lamanya.
#XFLD: Summary label
summaryLabel=Ringkasan
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=Berjalan setiap {0} Jam. Kitaran jalanan ditetapkan semula setiap hari 0:00 (UTC).
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=Berjalan setiap {0} Hari pada {1} (UTC). Kitaran jalanan ditetapkan semula pada hari pertama setiap bulan.
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=Berjalan setiap {0} Bulan di {1} pada {2} (UTC). Kitaran jalanan ditetapkan semula pada hari pertama setiap tahun.
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=Berjalan setiap minggu di {0} pada {1} (UTC).
#XFLD: Future runs label with time zone info
futureRunsLabel=Jalanan Akan Datang
#XFLD: Start & End Date label
startEndDateLabel=Tetapkan Tarikh Mula dan Akhir
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=Jalanan pertama mungkin tidak dilaksanakan kerana ia akan bermula kurang daripada 10 minit.
#XFLD: Label for text at UTC
atUTCLabel=Di (UTC)
#XMSG: Authorize expired text
authExpiredText=Sah kuasa untuk menjalankan tugas berulang anda telah tamat tempoh. Anda perlu memperbaharui sah kuasa bagi membolehkan SAP menjalankan jadual bagi pihak anda jika ingin meneruskan tugas penjadualan.
#XMSG: Authorization expiring soon text
authExpiringSoonText=Sah kuasa untuk menjalankan tugas berulang anda akan tamat tempoh tidak lama lagi. Perbaharui sah kuasa bagi membolehkan SAP terus menjalankan jadual anda bagi pihak anda.
#XMSG: Enter as Label text
enterAsText=Masukkan sebagai
#XMSG: Enter as Label text
enterAsTextNew=Jenis Tetapan
#XMSG: standard schedule label text
simpleScheduleText=Jadual Ringkas
#XMSG: Cron expression label text
cronScheduleText=Ungkapan Cron
#XMSG: Time Range label text
timeRangeLabelText=Julat Masa
#XMSG: Time Range label text
timeRangeLabelTextNew=Julat Tarikh
#XMSG: Ownership label
ownershipLabelText=Pemilikan
#XMSG: Show runs in the selected timezone label
showRunsInText=Tunjukkan Jalanan dalam
#XMSG: Text for schedule pause
pauseScheduleLabel=Hentikan Seketika Jadual:
#XMSG: Show Warning for schedule pause
schedulePausedWarning=Jadual ini terhenti. Tiada jalanan masa hadapan yang tersedia.
#XMSG: Cycle run text for hourly
hourlyCycleResetText=Kitaran jalanan ditetapkan semula pada 00:00 setiap hari.
#XMSG: Cycle run text for Daily
dailyCycleResetText=Kitaran jalanan ditetapkan semula pada hari pertama setiap bulan.
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=Kitaran jalanan ditetapkan semula pada 1 Jan.
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=Kitaran jalanan ditetapkan semula pada setiap minggu pada hari Ahad.
#XMSG: Day label
dayLabel=Hari (Bulan)
#XMSG: Monthly label
monthlyLabel=Bulan
#XMSG: Weekly label
weeklyLabel=Hari (Minggu)
#XMSG: Time in UTC label
utcTimeLabel=Masa Semasa (UTC)

#XMSG: January
monthLabel1=Januari
#XMSG: February
monthLabel2=Februari
#XMSG: March
monthLabel3=Mac
#XMSG: April
monthLabel4=April
#XMSG: May
monthLabel5=Mei
#XMSG: June
monthLabel6=Jun
#XMSG: July
monthLabel7=Julai
#XMSG: August
monthLabel8=Ogos
#XMSG: September
monthLabel9=September
#XMSG: October
monthLabel10=Oktober
#XMSG: November
monthLabel11=November
#XMSG: December
monthLabel12=Disember

weekdayLabel0=Ahad
weekdayLabel1=Isnin
weekdayLabel2=Selasa
weekdayLabel3=Rabu
weekdayLabel4=Khamis
weekdayLabel5=Jumaat
weekdayLabel6=Sabtu

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=Pada setiap {0} minit dari {1} hingga {2}.
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=Pada setiap {0} minit.
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=Pada setiap {0} minit bermula dari {1}.
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=Pada minit {0}.

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=selepas setiap {0} jam dari {1} hingga {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=selepas setiap {0} jam
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=selepas setiap {0} jam bermula dari {1}
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=selepas jam {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=pada setiap {0} hari setiap bulan dari {1} sepanjang {2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=pada setiap {0} hari setiap bulan
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=pada setiap {0} hari setiap bulan bermula dari {1}
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=pada hari dalam bulan {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=dalam setiap {0} bulan dari {1} sepanjang {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=dalam setiap {0} bulan
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=dalam setiap {0} bulan bermula dari {1}
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=dalam {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=pada setiap {0} hari setiap minggu dari {1} sepanjang {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=pada setiap {0} hari setiap minggu
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=pada setiap {0} hari setiap minggu bermula dari {1}
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=pada {0}
#XMSG: And text
andText=dan
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=Masukkan nilai yang disokong atau gabungan nilai:
#XMSG: default information about cron string
defaultCronStringInfoText=Nilai yang disokong untuk ungkapan cron: <strong>nombor * , / </strong>
#XMSG: Parameterized info about cron string
cronStringinfoText=Nilai yang disokong untuk medan yang dipilih: <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=Ketahui Selanjutnya
#XMSG: Hide details text
hideDetailsText=Sembunyikan butiran
#XMSG: Label for starting at UTC Text
startingText=Bermula dari (UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=Salin ke Papan Klip
#XMSG: cron copied to clipboard
cronCopiedText=Ungkapan cron disalin ke papan klip
#XMSG: error message for invalid cron copy
invalidCronCopyText=Tidak boleh salin ungkapan cron tidak sah
#XMSG: No preview label
noPreviewText=Kami tidak boleh memaparkan pratonton kerana terdapat ralat atau nilai yang tiada. Semak jadual.
#XMSG: Label for create button
createText=Cipta
#XMSG: Label for Save button
saveText=Simpan
#XMSG: Label for text leave
leaveText=Tinggalkan
#XMSG: warn message when user click cancel button
cancelWarnText=Perubahan anda akan hilang apabila anda meninggalkan halaman ini.
#XMSG: Expired schedule warning
expiredTaskWarnText=Jadual ini telah tamat tempoh. Pilih tarikh akhir baharu untuk teruskan replikasi.
#XMSG: Expired schedule message
expiredScheduleMessageText=Jadual ini telah tamat tempoh. Pilih tarikh akhir baharu untuk teruskan menjalankan tugas berulang.
#XMSG: Expired schedule message
expiredTaskWarnTextNew=Jadual ini telah tamat tempoh. Untuk melanjutkannya, ubah tarikh akhir.
#XMSG: Expired schedule Text
expiredScheduleText=Tamat Tempoh
#XMSG: No future runs generated
noFutureRunsText=Jalanan seterusnya tidak tersedia.
#XMSG: Text for Local time
localTimeText=Masa Tempatan
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=Tiada jalanan dalam julat masa yang dipilih.
#XMSG: Warning text for expired end date
endDateExpiredText=Tarikh akhir telah berlalu.
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=Tentukan tarikh mula.
#XMSG: UTC info label
utcInfoLabelText=Jadual dicipta dalam Waktu Sejagat Diselaras (UTC). Masa UTC semasa ialah {0}.
#XMSG: UTC info label
utcInfoLabelTextNew=Waktu Sejagat Diselaras (UTC). Masa UTC semasa ialah {0}.
#XMSG: Invalid cron paste error message
invalidCronPasteText=Kami tidak boleh tampal ungkapan cron kerana ia tidak sah, Cuba lagi dengan ungkapan yang sah.
#XMSG: Weekly schedule error text
invalidWeeklyRecText=Pilih sekurang-kurangnya satu hari.
#XFLD: No End date text
noEndDateText=Tiada Tarikh Tamat
#XFLD: Help Button
openHelpButtonText=Tunjukkan Bantuan
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=Tarikh melebihi bilangan maksimum hari untuk bulan yang diberi
#XMSG: Redundancy error in cron string
cronRedundantErrorText=Corak ini mempunyai ungkapan berulang. Keluarkan corak: * atau / daripada senarai dipisahkan koma.
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=Masukkan nilai antara 0 hingga 59.
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=Masukkan nilai di atas 10.
#XMSG: Error message for missing start time
startTimeErrorText=Pilih Masa.
#XMSG: Error message handling for error code
invalidScheduleBodyError=Kandungan permintaan mengandungi data tidak sah.
#XMSG: Error message handling for error code
scheduleNotFoundError=Jadual tidak ditemui.
#XMSG: Error message handling for error code
actionForbiddenError=Anda memerlukan kebenaran yang diperlukan.
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=Anda cuba mencipta jadual yang telah wujud.
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=Tetapkan semula Setiap Hari
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=Tetapkan semula Setiap Bulan
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=Tetapkan semula Setiap Tahun
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=Tetapkan semula Setiap Minggu
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=Tetapkan semula Setiap Jam
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=Tetapkan semula Setiap Hari:
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=Tetapkan semula Setiap Bulan:
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=Tetapkan semula Setiap Tahun:
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=Tetapkan semula Setiap Minggu:
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=Tetapkan semula Setiap Jam:
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=Pada {0} pada setiap {1} hari
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=Benarkan kami untuk jalankan rantaian tugas serta tugas berulang yang anda telah jadualkan.
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=Jalanan jadual akan memuatkan snapshot. Mod replikasi akan bertukar daripada replikasi masa nyata kepada replikasi kelompok dan data tidak lagi akan dikemas kini dalam masa nyata.
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=Adakah anda pasti ingin padam jadual yang anda pilih?
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn={0} daripada {1} objek yang anda pilih mempunyai jadual.\nAnda pasti ingin padam jadual?

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=Adakah anda ingin mengambil pemilikan jadual bagi objek yang anda pilih?
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=Anda telah takrifkan jadual untuk {0} daripada {1} objek yang anda pilih. Anda ingin mengambil pemilikan bagi jadual ini?

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=Adakah anda ingin hentikan seketika jadual bagi objek yang anda pilih?
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn={0} daripada {1} objek yang anda pilih mempunyai jadual berjalan. Anda ingin hentikannya seketika?

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=Adakah anda ingin sambungkan semula jadual bagi objek yang anda pilih?
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn={0} daripada {1} objek yang anda pilih mempunyai jadual yang terhenti seketika. Anda ingin sambungkannya semula?

#XTXT: Warning text
warningText=Amaran
#XTXT: Text for schedule pause
pauseScheduleText=Hentikan Seketika Jadual
#XTXT: Text for resume schedule
resumeScheduleText=Sambung Semula Jadual
#XTXT: Text for assigning schedule
assignScheduleText=Umpukkan Jadual kepada Saya

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=Sah kuasa untuk menjalankan tugas berulang anda akan tamat tempoh pada {0}. Untuk meneruskan jalanan tugas yang dijadualkan, kami memerlukan persetujuan anda semula.

#XMSG: Reauthorize text
reauthorize=Benarkan semula

#XTXT: Duration text
duration=Jangka masa
#XTXT: Time frame label text
timeFrame=Tempoh Masa
#XTXT: Hours Label text
hours=Jam
#XTXT: Minutes Label text
minutes=Minit
#XTXT: Minutes Label text
minutesNew=Minit

#XTXT: Minute Recurrence type text
byMinute=Minit
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=Masukkan nilai antara 0 dan 59, atau tetapkan kekerapan kepada lebih daripada atau sama dengan {0} minit
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=Masukkan nilai antara 0 dan 59.
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=Jangka masa {0} minit lebih lama daripada kekerapan berjadual {1} minit.
#XTXT: Selected time zone At
timeZoneAt=Pada
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=Bermula pada
