#XMSG: Message to ask user to authorize
authorizeWarn=Autoritzeu-nos per executar les tasques periòdiques que heu programat.
#XFLD: Authorize field value
authorise=Autoritzar
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=Programar per a "{0}"
#XMSG: Ok message
ok=D'acord
#XMSG: Schedule
schedule=Programar
#XMSG: Cancel message
cancel=Cancel·lar
#XMSG: Close message
close=Tancar
#XFLD: lable to set recurrence
recurrenceLabel=Repetició
#XFLD: label to set the recurrence value
everyLabel=Cada
#XFLD: Start date field
startDateLabel=Data d'inici
#XFLD: Start date field
endDateLabel=Data de fi
#XFLD: TimeZone field
timeZoneLabel=Fus horari
#XMSG: validation text
recurrenceValidationText1=Introduïu un valor enter d’1 o superior
#XMSG: validation text
recurrenceValidationText2=Introduïu una data d’inici
#XMSG: validation text
recurrenceValidationText3=Introduïu una data d’inici
#XMSG: validation text
noObjectSelectedText=Seleccioneu una taula remota per programar.
#XMSG: error text
errorLabel=Error
#XMSG: Schedule created alert message
createScheduleSuccess=Programació creada
#XMSG: Error in creating schedule
errorCreateSchedule=Ara no s''ha pogut crear la programació. Torneu a provar-ho. \n {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=Programació actualitzada
#XMSG: Error in updating schedule
errorUpdateSchedule=Ara no s’ha pogut actualitzar la programació. Torneu a provar-ho. \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=Segur que voleu suprimir la programació?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Programació suprimida.
#XMSG: Schedule deletion error message
errorSeleteSchedule=Ara no s’ha pogut suprimir la programació. Torneu a provar-ho.
#XFLD: Authorise service title
authServiceLabel=Autoritzar servei
#XMSG: Redirecting message
redirectingText=Redireccionant per a autenticació.
#XMSG: Redirection success message
redirectSuccess=Ens heu autoritzat per executar les tasques periòdiques que heu programat.
#XMSG: Start time label
startTimeFormatLabel=Hora d’inici (format de {0} hores)
#XMSG: Start date and end date range validation
dateRangeValidationText=La data d’inici ha de ser anterior a la data de fi.
#XMSG: Delete schedule error
errorDeleteSchedule=Error en suprimir programa\n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=Hora/es
#XFLD: Plural Recurrence text for Day
dayPluraltext=Dia/es
#XFLD: Plural Recurrence text for Month
monthPluralText=Mes/os
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=Hores
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=Dies
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=Mesos
#XFLD: Recurrence text for Hour
hourText=Per hora
#XFLD: Recurrence text for Day
daytext=Per dia
#XFLD: Recurrence text for Month
monthText=Mensual
#XMSG: Start date cannot be in the past
startDateValidationText=La data d’inici no pot ser en el passat
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=Indiqueu un valor comprès entre 1 i 23.
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=Indiqueu un valor comprès entre 1 i 31.
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=Indiqueu un valor comprès entre 1 i 12.
#XMSG: Invalid date text
invalidDateText=Data no vàlida
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=Repetició
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=Data d’inici
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=Data de fi
#XMSG: Schedule popover end date label
naText=N/D
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=Fus horari
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=Propietari
#XMSG: text for not available
naText=N/D
#XMSG: String Every
everyString=Cada
#XMSG: on label
onLabel=Activat
#XMSG: on label
onLabelMonth=El dia del mes
#XFLD: Label for Sunday
sunLabel=Dg.
#XFLD: Label for Monday
monLabel=Dl.
#XFLD: Label for Tuesday
tueLabel=Dt.
#XFLD: Label for Wednesday
wedLabel=Dc.
#XFLD: Label for Thursday
thuLabel=Dj.
#XFLD: Label for Friday
friLabel=Dv.
#XFLD: Label for Saturday
satLabel=Ds.
#XFLD: Recurrence text for Week
weekText=Setmana
#XFLD: Create Schedule Dialog Title
createDialogTitle=Crear programa
#XFLD: Edit Dialog title
editDialogTitle=Editar programació
#XFLD: Selected Object Title
selectedObjectText=Seleccionat
#XMSG: Table Text
tableText=Taula
#XMSG: View Text
viewText=Vista
#XMSG: Data flow text
dataFlowText=Flux de dades
#XMSG: Select a day of week error message
weekRecErrorText=Seleccioneu almenys un dia de la setmana
#XFLD: Label for Owner
ownerLabel=Propietari
#XFLD: Button label for takeover schedule
takeoverLabel=Modificar propietari
#XFLD: Created By label
createdbyLabel=Autor
#XFLD: Changed By label
changedbyLabel=Autor de l'última modificació
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=L'autenticació d'usuari és necessària per executar les tasques programades a SAP Datasphere.\n Si trieu executar aquesta tasca, aquesta tasca programada s'executarà i en sereu el propietari.\n Abans d'executar una tasca programada, assegureu-vos que heu donat el vostre consentiment per executar tasques en nom vostre.
#XMSG: Schedule takeover success message
ownerChangeSuccessText=Acceptació de la programació correcta.
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=Acceptació de la programació errònia.
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=L'acceptació de la programació ha fallat degut a un error en la l'actualització de la programació.
#XFLD: Text for confirm
confirm=Confirmar
#XMSG: Create schedule error message
createScheduleError=Error en crear programació.
#XMSG: Schedule update failed message
updateScheduleError=Error en actualitzar programació.
#XMSG: Schedule delete error message
deleteScheduleError=Error en suprimir programació.
#XFLD: Label for Frequency
frequencyLabel=Freqüència
#XFLD: Label for Frequency
frequencyLabelNew=Opcions
#XFLD: Label for repeat
repeatLabel=Repetir
#XFLD: Label for repeat
repeatLabelNeww=Freqüència:
#XFLD: Labe for text at
atLabel=A les
#XFLD: Label for starting
startingLabel=Inicial
#XFLD: Label for Start
startLabel=Inici
#XFLD: Label for Ending
validityLabel=Validesa
#XFLD: Label for end
endLabel=Fi
#XMSG: Message for no end date
noEndDateMsg=La recurrència es produirà indefinidament.
#XFLD: Assign label
assignLabel=Assignar-me a mi
#XFLD: Overview label
overviewLabel=Resum
#XFLD: Next runs field
nextRunsLabel=Pròximes execucions:
#XFLD: Next runs field
nextRunsLabelForPopOver=Pròximes execucions
#XFLD: Expired field
taskPopoverExpiredLabel=Vençut
#XFLD: Expired Message
taskPopoverExpiredMessage=La programació ha vençut. Per ampliar-la, editeu-la i modifiqueu la data de fi.
#XFLD: label for Day-of-month
dayOfmonthLabel=Dia del mes
#XFLD: label for Minute
minuteLabel=Minut
#XFLD: label for hour
hourLabel=Hora
#XFLD: Last Day of month selection
lastDayText=Últim dia del mes
#XFLD: Hourly unit description label
hourUnitLabel=Hora del dia
#XFLD: Daily recurrence unit description label
dayUnitLabel=Dia/es al mes
#XFLD: Monthly receurrence unit description label
monthUnitLabel=Mesos de l’any
#XFLD: label for off
offLabel=Desactivat
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=Té lloc cada {0} hora/es a partir de {1} a les {2} minuts.
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=Té lloc cada {0} dia/es a partir de {1} a les {2}.
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=Té lloc cada {0} mes(os) a partir de {1} el {2} a les {3}.
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=Té lloc cada setmana el {0} a les {1}.
#XMSG: Schedule expired message
scheduleExpiredLabel=El programa ha vençut.
#XFLD: Create Schedule Dialog Title
createScheduleTitle=Crear programa per a "{0}"
#XFLD: Edit Dialog title
createScheduleTitleNew={0}: Crear programació
#XFLD: Edit Dialog title
editScheduleTitle=Editar programa per a "{0}"
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}: Editar programació
#XFLD: Edit Schedule Button
editSchedule=Editar programació
#XFLD: Full Label for Sunday
sunFullLabel=Diumenge
#XFLD: Full Label for Monday
monFullLabel=Dilluns
#XFLD: Full Label for Tuesday
tueFullLabel=Dimarts
#XFLD: Full Label for Wednesday
wedFullLabel=Dimecres
#XFLD: Full Label for Thursday
thuFullLabel=Dijous
#XFLD: Full Label for Friday
friFullLabel=Divendres
#XFLD: Full Label for Saturday
satFullLabel=Dissabte
#XFLD: Weekly text
weeklyText=Setmanal
#XFLD: Set start day text
startEndDateText=Fixar data d’inici i de fi
#XMSG: Schedule runs indefinitely message
validityOffText=La programació s’executarà de manera indefinida.
#XFLD: Summary label
summaryLabel=Resum
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=S’executa cada {0} hora/es. El cicle d’execució es reinicialitza cada dia a les 0:00 (UTC).
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=S’executa cada {0} dia/es a les {1} (UTC). El cicle d’execució es reinicialitza el primer dia de cada mes.
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=S’executa cada {0} mes/os el {1} a les {2} (UTC). El cicle d’execució es reinicialitza el primer dia de cada any.
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=S’executa cada setmana el {0} a les {1} (UTC).
#XFLD: Future runs label with time zone info
futureRunsLabel=Execucions futures
#XFLD: Start & End Date label
startEndDateLabel=Fixar data d’inici i de fi
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=És possible que la primera execució no es pugui fer, atès que començarà en menys de 10 minuts.
#XFLD: Label for text at UTC
atUTCLabel=A les (UTC)
#XMSG: Authorize expired text
authExpiredText=L'autorització per executar les tasques recurrents ha vençut. Heu de renovar-la per permetre que SAP executi el programa en el vostre nom si voleu continuar programant tasques.
#XMSG: Authorization expiring soon text
authExpiringSoonText=L'autorització per executar les vostres tasques recurrents vencerà aviat. Renoveu l'autorització per permetre que SAP continuï executant el programa en el vostre nom.
#XMSG: Enter as Label text
enterAsText=Entrar com a
#XMSG: Enter as Label text
enterAsTextNew=Tipus d'opció
#XMSG: standard schedule label text
simpleScheduleText=Programa simple
#XMSG: Cron expression label text
cronScheduleText=Expressió cron
#XMSG: Time Range label text
timeRangeLabelText=Interval de temps
#XMSG: Time Range label text
timeRangeLabelTextNew=Interval de dates
#XMSG: Ownership label
ownershipLabelText=Propietat
#XMSG: Show runs in the selected timezone label
showRunsInText=Mostrar execucions en
#XMSG: Text for schedule pause
pauseScheduleLabel=Pausar programació:
#XMSG: Show Warning for schedule pause
schedulePausedWarning=Aquesta programació està en pausa. No hi ha futures execucions disponibles.
#XMSG: Cycle run text for hourly
hourlyCycleResetText=El cicle d'execució es restableix a les 00:00 cada dia.
#XMSG: Cycle run text for Daily
dailyCycleResetText=El cicle d'execució es restableix el primer dia de cada mes.
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=El cicle d'execució es restableix dia 1 de gener de cada any.
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=El cicle d'execució es restableix cada diumenge.
#XMSG: Day label
dayLabel=Dia (mes)
#XMSG: Monthly label
monthlyLabel=Mes
#XMSG: Weekly label
weeklyLabel=Dia (setmana)
#XMSG: Time in UTC label
utcTimeLabel=Hora actual (UTC)

#XMSG: January
monthLabel1=Gener
#XMSG: February
monthLabel2=Febrer
#XMSG: March
monthLabel3=Març
#XMSG: April
monthLabel4=Abril
#XMSG: May
monthLabel5=Maig
#XMSG: June
monthLabel6=Juny
#XMSG: July
monthLabel7=Juliol
#XMSG: August
monthLabel8=Agost
#XMSG: September
monthLabel9=Setembre
#XMSG: October
monthLabel10=Octubre
#XMSG: November
monthLabel11=Novembre
#XMSG: December
monthLabel12=Desembre

weekdayLabel0=Diumenge
weekdayLabel1=Dilluns
weekdayLabel2=Dimarts
weekdayLabel3=Dimecres
weekdayLabel4=Dijous
weekdayLabel5=Divendres
weekdayLabel6=Dissabte

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=Cada {0} minut de {1} a {2}.
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=Cada {0} minut.
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=Cada {0} minut a partir de {1}.
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=En el minut {0}.

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=després de cada {0} hora de {1} a {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=després de cada {0} hora
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=després de cada {0} hora a partir de {1}
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=després de les {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=cada {0} dia del mes des de {1} fins a {2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=cada {0} dia del mes
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=cada {0} dia del mes des de {1}
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=el dia del mes {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=a cada {0} mes de {1} a {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=a cada {0} mes
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=a cada {0} mes des de {1}
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=a {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=cada {0} dia de la setmana des de {1} fins a {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=cada {0} dia de la setmana
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=cada {0} dia de la setmana des de {1}
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=el {0}
#XMSG: And text
andText=i
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=Introduïu un valor permès o una combinació de valors:
#XMSG: default information about cron string
defaultCronStringInfoText=Valors permesos per a l'expressió cron: <strong>números * , / </strong>
#XMSG: Parameterized info about cron string
cronStringinfoText=Valors permesos per al camp seleccionat: <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=Més informació
#XMSG: Hide details text
hideDetailsText=Ocultar detalls
#XMSG: Label for starting at UTC Text
startingText=A partir de les (UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=Copiar al porta-retalls
#XMSG: cron copied to clipboard
cronCopiedText=Expressió cron copiada al porta-retalls
#XMSG: error message for invalid cron copy
invalidCronCopyText=No es pot copiar expressió cron no vàlida
#XMSG: No preview label
noPreviewText=No es pot visualitzar una vista prèvia perquè hi ha erros o falten valors. Reviseu el programa.
#XMSG: Label for create button
createText=Crear
#XMSG: Label for Save button
saveText=Desar
#XMSG: Label for text leave
leaveText=Sortir
#XMSG: warn message when user click cancel button
cancelWarnText=Les modificacions es perdran quan sortiu.
#XMSG: Expired schedule warning
expiredTaskWarnText=El programa ha vençut. Seleccioneu una nova data de fi per continuar amb la replicació.
#XMSG: Expired schedule message
expiredScheduleMessageText=El programa ha vençut. Seleccioneu una nova data de fi per continuar executant les tasques actuals.
#XMSG: Expired schedule message
expiredTaskWarnTextNew=La programació ha vençut. Per ampliar-la, modifiqueu la data de fi.
#XMSG: Expired schedule Text
expiredScheduleText=Vençut
#XMSG: No future runs generated
noFutureRunsText=Cap execució següent disponible.
#XMSG: Text for Local time
localTimeText=Hora local
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=No hi ha execucions dins de l'interval de dates seleccionat.
#XMSG: Warning text for expired end date
endDateExpiredText=La data de fi està en el passat.
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=Especifiqueu una data d'inici
#XMSG: UTC info label
utcInfoLabelText=Els programes es creen en temps universal coordinat (UTC). L’hora UTC actual és {0}.
#XMSG: UTC info label
utcInfoLabelTextNew=Temps universal coordinat (UTC). L’hora UTC actual és {0}.
#XMSG: Invalid cron paste error message
invalidCronPasteText=No es pot enganxar l'expressió cron perquè no és vàlida. Torneu a provar-ho amb una expressió vàlida.
#XMSG: Weekly schedule error text
invalidWeeklyRecText=Seleccioneu almenys un dia.
#XFLD: No End date text
noEndDateText=Cap data final
#XFLD: Help Button
openHelpButtonText=Mostrar ajuda
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=La data supera el nombre màxim de dies del mes determinat
#XMSG: Redundancy error in cron string
cronRedundantErrorText=Aquest patró té expressions redundants. Elimineu els patrons: * o / de la llista separada per comes.
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=Indiqueu un valor comprès entre 0 i 59.
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=Introduïu un valor superior a 10.
#XMSG: Error message for missing start time
startTimeErrorText=Seleccioneu una hora.
#XMSG: Error message handling for error code
invalidScheduleBodyError=El cos de la sol·licitud conté dades no vàlides.
#XMSG: Error message handling for error code
scheduleNotFoundError=No s'ha trobat el programa.
#XMSG: Error message handling for error code
actionForbiddenError=No teniu el permís necessari.
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=Esteu intentant crear un programa que ja existeix.
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=Restablir cada dia
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=Restablir cada mes
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=Restablir cada any
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=Restablir cada setmana
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=Restablir cada hora
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=Restablir cada dia:
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=Restablir cada mes:
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=Restablir cada any:
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=Restablir cada setmana:
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=Restablir cada hora:
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=A les {0} cada {1}
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=Autoritzeu-nos per executar cadenes de tasques, així com tasques periòdiques que hàgiu programat.
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=Una execució de planificació carregarà una captura de pantalla. El mode de replicació canviarà de temps real a en lots i es deixaran d'actualitzar dades en temps real.
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=Segur que voleu suprimir la planificació seleccionada?
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn={0} dels {1} objectes seleccionats tenen planificacions. \nSegur que voleu eliminar les planificacions?

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=Voleu atribuir-vos la propietat de les planificacions per als objectes seleccionats?
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=Les planificacions estan definides per a {0} dels {1} objectes seleccionats. Voleu atribuir-vos la propietat d''aquestes planificacions?

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=Voleu pausar les planificacions per als objectes seleccionats?
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn={0} dels {1} objectes seleccionats tenen planificacions en curs. Voleu posar-les en pausa?

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=Voleu reprendre les planificacions dels objectes seleccionats?
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn={0} dels {1} objectes seleccionats tenen planificacions en pausa. Voleu reprendre-les?

#XTXT: Warning text
warningText=Advertència
#XTXT: Text for schedule pause
pauseScheduleText=Pausar programa
#XTXT: Text for resume schedule
resumeScheduleText=Reprendre programa
#XTXT: Text for assigning schedule
assignScheduleText=Assignar-me programa

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=L''autorització per executar les tasques recurrents vencerà el {0}. Per continuar executant les tasques recurrents, cal que torneu a donar el vostre consentiment.

#XMSG: Reauthorize text
reauthorize=Tornar a autoritzar

#XTXT: Duration text
duration=Durada
#XTXT: Time frame label text
timeFrame=Període
#XTXT: Hours Label text
hours=Hora/es
#XTXT: Minutes Label text
minutes=Minut(s)
#XTXT: Minutes Label text
minutesNew=Minuts

#XTXT: Minute Recurrence type text
byMinute=Minuts
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=Introduïu un valor entre 0 i 59 o definiu la freqüència a {0} minuts o més.
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=Introduïu un valor entre 0 i 59.
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=La durada de {0} minuts es superior a la freqüència programada de {1} minuts.
#XTXT: Selected time zone At
timeZoneAt=A les
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=Comença a les
