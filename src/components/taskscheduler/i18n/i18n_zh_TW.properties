#XMSG: Message to ask user to authorize
authorizeWarn=授權我們執行您所排程的週期性工作細項。
#XFLD: Authorize field value
authorise=授權
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=排程「{0}」
#XMSG: Ok message
ok=確定
#XMSG: Schedule
schedule=排程
#XMSG: Cancel message
cancel=取消
#XMSG: Close message
close=關閉
#XFLD: lable to set recurrence
recurrenceLabel=週期
#XFLD: label to set the recurrence value
everyLabel=每
#XFLD: Start date field
startDateLabel=開始日期
#XFLD: Start date field
endDateLabel=結束日期
#XFLD: TimeZone field
timeZoneLabel=時區
#XMSG: validation text
recurrenceValidationText1=輸入大於或等於 1 的整數
#XMSG: validation text
recurrenceValidationText2=輸入開始日期
#XMSG: validation text
recurrenceValidationText3=輸入開始日期
#XMSG: validation text
noObjectSelectedText=選擇要排程的遠端表格
#XMSG: error text
errorLabel=錯誤
#XMSG: Schedule created alert message
createScheduleSuccess=已建立排程
#XMSG: Error in creating schedule
errorCreateSchedule=目前無法建立排程，請再試一次。\N{0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=已更新排程
#XMSG: Error in updating schedule
errorUpdateSchedule=目前無法更新排程，請再試一次。\n{0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=您確定要刪除排程嗎?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=已刪除排程。
#XMSG: Schedule deletion error message
errorSeleteSchedule=目前無法刪除排程，請再試一次。
#XFLD: Authorise service title
authServiceLabel=授權服務
#XMSG: Redirecting message
redirectingText=正在重新導向以進行驗證。
#XMSG: Redirection success message
redirectSuccess=您已成功授權我們執行您所排程的週期性工作細項。
#XMSG: Start time label
startTimeFormatLabel=開始時間 ({0} 小時制)
#XMSG: Start date and end date range validation
dateRangeValidationText=開始日期必須早於結束日期。
#XMSG: Delete schedule error
errorDeleteSchedule=刪除排程時發生錯誤\n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=小時
#XFLD: Plural Recurrence text for Day
dayPluraltext=天
#XFLD: Plural Recurrence text for Month
monthPluralText=月
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=小時
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=天
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=個月
#XFLD: Recurrence text for Hour
hourText=每小時
#XFLD: Recurrence text for Day
daytext=每日
#XFLD: Recurrence text for Month
monthText=每月
#XMSG: Start date cannot be in the past
startDateValidationText=開始日期不可在過去
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=請輸入介於 1 到 23 之間的值。
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=請輸入介於 1 到 31 之間的值。
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=請輸入介於 1 到 12 之間的值。
#XMSG: Invalid date text
invalidDateText=日期無效
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=週期
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=開始日期
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=結束日期
#XMSG: Schedule popover end date label
naText=無
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=時區
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=所有人
#XMSG: text for not available
naText=NA
#XMSG: String Every
everyString=每
#XMSG: on label
onLabel=開啟
#XMSG: on label
onLabelMonth=於每月日期
#XFLD: Label for Sunday
sunLabel=日
#XFLD: Label for Monday
monLabel=一
#XFLD: Label for Tuesday
tueLabel=二
#XFLD: Label for Wednesday
wedLabel=三
#XFLD: Label for Thursday
thuLabel=四
#XFLD: Label for Friday
friLabel=五
#XFLD: Label for Saturday
satLabel=六
#XFLD: Recurrence text for Week
weekText=週
#XFLD: Create Schedule Dialog Title
createDialogTitle=建立排程
#XFLD: Edit Dialog title
editDialogTitle=編輯排程
#XFLD: Selected Object Title
selectedObjectText=已選擇
#XMSG: Table Text
tableText=表格
#XMSG: View Text
viewText=檢視
#XMSG: Data flow text
dataFlowText=資料流程
#XMSG: Select a day of week error message
weekRecErrorText=請選擇該週的至少一天。
#XFLD: Label for Owner
ownerLabel=所有人
#XFLD: Button label for takeover schedule
takeoverLabel=更改所有人
#XFLD: Created By label
createdbyLabel=建立者
#XFLD: Changed By label
changedbyLabel=上次修改者
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=需要使用者授權才能在 SAP Datasphere 中執行排程的工作細項。\n 選擇執行此工作細項，此排程的工作細項將以您為所有人來執行。\n在排程的工作細項到期無法執行前，確保您提供同意以代替您執行工作細項。
#XMSG: Schedule takeover success message
ownerChangeSuccessText=排程接管成功。
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=排程接管失敗。
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=由於更新排程時發生錯誤，因此排程接管失敗。
#XFLD: Text for confirm
confirm=確認
#XMSG: Create schedule error message
createScheduleError=排程建立失敗。
#XMSG: Schedule update failed message
updateScheduleError=排程更新失敗。
#XMSG: Schedule delete error message
deleteScheduleError=排程刪除失敗。
#XFLD: Label for Frequency
frequencyLabel=頻率
#XFLD: Label for Frequency
frequencyLabelNew=設定
#XFLD: Label for repeat
repeatLabel=重複
#XFLD: Label for repeat
repeatLabelNeww=頻率：
#XFLD: Labe for text at
atLabel=時間
#XFLD: Label for starting
startingLabel=正在開始
#XFLD: Label for Start
startLabel=開始
#XFLD: Label for Ending
validityLabel=有效性
#XFLD: Label for end
endLabel=結束
#XMSG: Message for no end date
noEndDateMsg=週期將無限期發生
#XFLD: Assign label
assignLabel=指派給我
#XFLD: Overview label
overviewLabel=概觀
#XFLD: Next runs field
nextRunsLabel=下一次執行：
#XFLD: Next runs field
nextRunsLabelForPopOver=下一次執行
#XFLD: Expired field
taskPopoverExpiredLabel=已到期
#XFLD: Expired Message
taskPopoverExpiredMessage=排程已到期。若要延長，請編輯排程並更改結束日期。
#XFLD: label for Day-of-month
dayOfmonthLabel=每月天次
#XFLD: label for Minute
minuteLabel=分鐘
#XFLD: label for hour
hourLabel=小時
#XFLD: Last Day of month selection
lastDayText=每月最後一天
#XFLD: Hourly unit description label
hourUnitLabel=每天時間
#XFLD: Daily recurrence unit description label
dayUnitLabel=每月天數
#XFLD: Monthly receurrence unit description label
monthUnitLabel=每年月數
#XFLD: label for off
offLabel=關閉
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=從 {1} 開始，每 {0} 小時 {2} 分鐘發生。
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=從 {1} 開始，每 {0} 天於 {2} 發生。
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=從 {1} 開始，每 {0} 個月的 {2} {3} 發生。
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=每週於 {0} {1} 發生。
#XMSG: Schedule expired message
scheduleExpiredLabel=排程已到期。
#XFLD: Create Schedule Dialog Title
createScheduleTitle=建立「{0}」的排程
#XFLD: Edit Dialog title
createScheduleTitleNew={0}：建立排程
#XFLD: Edit Dialog title
editScheduleTitle=編輯「{0}」的排程
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}：編輯排程
#XFLD: Edit Schedule Button
editSchedule=編輯排程
#XFLD: Full Label for Sunday
sunFullLabel=星期日
#XFLD: Full Label for Monday
monFullLabel=星期一
#XFLD: Full Label for Tuesday
tueFullLabel=星期二
#XFLD: Full Label for Wednesday
wedFullLabel=星期三
#XFLD: Full Label for Thursday
thuFullLabel=星期四
#XFLD: Full Label for Friday
friFullLabel=星期五
#XFLD: Full Label for Saturday
satFullLabel=星期六
#XFLD: Weekly text
weeklyText=每週
#XFLD: Set start day text
startEndDateText=設定開始和結束日期
#XMSG: Schedule runs indefinitely message
validityOffText=排程將無限期執行。
#XFLD: Summary label
summaryLabel=摘要
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=每 {0} 小時執行。每天 0:00 (UTC) 將重設執行循環。
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=每 {0} 天的 {1} (UTC) 執行。每個月第一天將重設執行循環。
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg={1} 每 {0} 個月的 {2} (UTC) 執行。每年第一天將重設執行循環。
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg={0} 每週的 {1} (UTC) 執行。
#XFLD: Future runs label with time zone info
futureRunsLabel=未來執行
#XFLD: Start & End Date label
startEndDateLabel=設定開始和結束日期
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=由於第一次執行將於小於 10 分鐘開始，因此可能不會執行。
#XFLD: Label for text at UTC
atUTCLabel=時間 (UTC)
#XMSG: Authorize expired text
authExpiredText=執行週期性工作細項的權限已到期。若要繼續排程工作細項，您必須更新權限以允許 SAP 代替您執行排程。
#XMSG: Authorization expiring soon text
authExpiringSoonText=執行週期性工作細項的權限即將到期。請更新權限以允許 SAP 繼續代替您執行排程。
#XMSG: Enter as Label text
enterAsText=輸入方式
#XMSG: Enter as Label text
enterAsTextNew=設定類型
#XMSG: standard schedule label text
simpleScheduleText=簡單排程
#XMSG: Cron expression label text
cronScheduleText=Cron 運算式
#XMSG: Time Range label text
timeRangeLabelText=時間範圍
#XMSG: Time Range label text
timeRangeLabelTextNew=日期範圍
#XMSG: Ownership label
ownershipLabelText=所有權
#XMSG: Show runs in the selected timezone label
showRunsInText=顯示執行時區
#XMSG: Text for schedule pause
pauseScheduleLabel=暫停排程：
#XMSG: Show Warning for schedule pause
schedulePausedWarning=此排程已暫停，不需要進一步執行。
#XMSG: Cycle run text for hourly
hourlyCycleResetText=每天 00:00 重設執行循環。
#XMSG: Cycle run text for Daily
dailyCycleResetText=每個月第一天重設執行循環。
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=每年 1 月 1 日重設執行循環。
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=每週星期日重設執行循環。
#XMSG: Day label
dayLabel=天 (月)
#XMSG: Monthly label
monthlyLabel=月
#XMSG: Weekly label
weeklyLabel=天 (週)
#XMSG: Time in UTC label
utcTimeLabel=目前時間 (UTC)

#XMSG: January
monthLabel1=一月
#XMSG: February
monthLabel2=二月
#XMSG: March
monthLabel3=三月
#XMSG: April
monthLabel4=四月
#XMSG: May
monthLabel5=五月
#XMSG: June
monthLabel6=六月
#XMSG: July
monthLabel7=七月
#XMSG: August
monthLabel8=八月
#XMSG: September
monthLabel9=九月
#XMSG: October
monthLabel10=十月
#XMSG: November
monthLabel11=十一月
#XMSG: December
monthLabel12=十二月

weekdayLabel0=星期日
weekdayLabel1=星期一
weekdayLabel2=星期二
weekdayLabel3=星期三
weekdayLabel4=星期四
weekdayLabel5=星期五
weekdayLabel6=星期六

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=每 {0} 分鐘，從 {1} 到 {2}。
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=每 {0} 分鐘。
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=每 {0} 分鐘，從 {1} 開始。
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=第 {0} 分鐘。

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=過去每 {0} 小時，從 {1} 到 {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=過去每 {0} 小時
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=過去每 {0} 小時，從 {1} 開始
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=過去小時 {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=每月每 {0} 天，從 {1} 到 {2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=每月每 {0} 天
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=每月每 {0} 天，從 {1} 開始
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=每月第 {0} 天

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=每 {0} 個月，從 {1} 到 {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=每 {0} 個月
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=每 {0} 個月，從 {1} 開始
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=於 {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=每週每 {0} 天，從 {1} 到 {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=每週每 {0} 天
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=每週每 {0} 天，從 {1} 開始
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=於 {0}
#XMSG: And text
andText=和
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=請輸入支援的值或值組合：
#XMSG: default information about cron string
defaultCronStringInfoText=Cron 運算式支援的值：<strong>數字 * , - /</strong>
#XMSG: Parameterized info about cron string
cronStringinfoText=所選欄位支援的值：<strong>{0}</strong>
#XMSG: Show details text
showDetailsText=瞭解更多
#XMSG: Hide details text
hideDetailsText=隱藏明細
#XMSG: Label for starting at UTC Text
startingText=開始時間 (UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=複製至剪貼簿
#XMSG: cron copied to clipboard
cronCopiedText=已將 Cron 運算式複製到剪貼簿
#XMSG: error message for invalid cron copy
invalidCronCopyText=無法複製無效的 Cron 運算式
#XMSG: No preview label
noPreviewText=由於有錯誤或缺少值，因此無法顯示預覽。請審查排程。
#XMSG: Label for create button
createText=建立
#XMSG: Label for Save button
saveText=儲存
#XMSG: Label for text leave
leaveText=離開
#XMSG: warn message when user click cancel button
cancelWarnText=離開時，您的更改內容將遺失。
#XMSG: Expired schedule warning
expiredTaskWarnText=此排程已到期。請選擇新的結束日期以繼續複製。
#XMSG: Expired schedule message
expiredScheduleMessageText=此排程已到期。請選擇新的結束日期以繼續執行週期性工作細項。
#XMSG: Expired schedule message
expiredTaskWarnTextNew=此排程已到期。若要延長，請更改結束日期。
#XMSG: Expired schedule Text
expiredScheduleText=已到期
#XMSG: No future runs generated
noFutureRunsText=沒有下一次執行。
#XMSG: Text for Local time
localTimeText=本地時間
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=所選日期範圍內沒有執行。
#XMSG: Warning text for expired end date
endDateExpiredText=結束日期為過去日期。
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=請指定開始日期。
#XMSG: UTC info label
utcInfoLabelText=排程以格林威治標準時間 (UTC) 建立。目前 UTC 時間為 {0}。
#XMSG: UTC info label
utcInfoLabelTextNew=格林威治標準時間 (UTC)。目前 UTC 時間為 {0}。
#XMSG: Invalid cron paste error message
invalidCronPasteText=由於 Cron 運算式無效，因此無法貼上。請使用有效運算式再試一次。
#XMSG: Weekly schedule error text
invalidWeeklyRecText=請至少選擇一天。
#XFLD: No End date text
noEndDateText=無結束日期
#XFLD: Help Button
openHelpButtonText=顯示輔助說明
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=日期超過指定月份的天數上限。
#XMSG: Redundancy error in cron string
cronRedundantErrorText=此模式有多餘的運算式。請從逗號分隔清單移除：* 或 /。
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=請輸入介於 0 到 59 之間的值。
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=輸入大於 10 的值。
#XMSG: Error message for missing start time
startTimeErrorText=請選擇時間。
#XMSG: Error message handling for error code
invalidScheduleBodyError=請求主體包含無效資料。
#XMSG: Error message handling for error code
scheduleNotFoundError=找不到排程。
#XMSG: Error message handling for error code
actionForbiddenError=您未具備必要權限。
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=您正嘗試建立已存在的排程。
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=每天重設
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=每個月重設
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=每年重設
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=每週重設
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=每小時重設
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=每天重設：
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=每個月重設：
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=每年重設：
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=每週重設：
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=每小時重設：
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=每 {1} 天的 {0}
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=授權我們執行您所排程的工作細項鏈及週期性工作細項。
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=排程執行將載入新概要。複製模式將從即時複製切換為批次複製，且資料不再即時更新。
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=您確定要刪除所選排程嗎？
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn={0} 個所選物件 (共 {1} 個) 已有排程。\n您確定要刪除排程嗎？

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=您要取得所選物件的排程所有權嗎？
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=已定義 {0} 個所選物件 (共 {1} 個) 的排程。您要取得這些排程的所有權嗎？

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=您要暫停所選物件的排程嗎？
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn={0} 個所選物件 (共 {1} 個) 已執行排程。您要暫停這些排程嗎？

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=您要繼續所選物件的排程嗎？
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn={0} 個所選物件 (共 {1} 個) 已暫停排程。您要繼續這些排程嗎？

#XTXT: Warning text
warningText=警告
#XTXT: Text for schedule pause
pauseScheduleText=暫停排程
#XTXT: Text for resume schedule
resumeScheduleText=繼續排程
#XTXT: Text for assigning schedule
assignScheduleText=將排程指派給我

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=執行週期性工作細項的權限將於 {0} 到期。若要繼續執行排程的工作細項，則需要您再次同意。

#XMSG: Reauthorize text
reauthorize=重新授權

#XTXT: Duration text
duration=持續期
#XTXT: Time frame label text
timeFrame=期間
#XTXT: Hours Label text
hours=小時
#XTXT: Minutes Label text
minutes=分鐘
#XTXT: Minutes Label text
minutesNew=分鐘

#XTXT: Minute Recurrence type text
byMinute=分鐘
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=輸入介於 0 到 59 之間的值，或將頻率設定為大於或等於 {0} 分鐘。
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=輸入 0 到 59 之間的值。
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1={0} 分鐘持續期超過 {1} 分鐘排程頻率。
#XTXT: Selected time zone At
timeZoneAt=時間
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=開始時間
