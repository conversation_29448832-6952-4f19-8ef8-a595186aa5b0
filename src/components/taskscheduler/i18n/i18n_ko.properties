#XMSG: Message to ask user to authorize
authorizeWarn=예약된 반복 태스크를 실행할 권한을 부여하십시오.
#XFLD: Authorize field value
authorise=권한 부여
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle="{0}"의 일정
#XMSG: Ok message
ok=확인
#XMSG: Schedule
schedule=일정
#XMSG: Cancel message
cancel=취소
#XMSG: Close message
close=닫기
#XFLD: lable to set recurrence
recurrenceLabel=반복
#XFLD: label to set the recurrence value
everyLabel=매
#XFLD: Start date field
startDateLabel=시작일
#XFLD: Start date field
endDateLabel=종료일
#XFLD: TimeZone field
timeZoneLabel=시간대
#XMSG: validation text
recurrenceValidationText1=1 이상의 정수 값을 입력하십시오.
#XMSG: validation text
recurrenceValidationText2=시작일을 입력하십시오.
#XMSG: validation text
recurrenceValidationText3=시작일을 입력하십시오.
#XMSG: validation text
noObjectSelectedText=일정 계획할 원격 테이블을 선택하십시오.
#XMSG: error text
errorLabel=오류
#XMSG: Schedule created alert message
createScheduleSuccess=일정이 생성되었습니다.
#XMSG: Error in creating schedule
errorCreateSchedule=지금은 일정을 생성할 수 없습니다. 다시 시도하십시오. \n {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=일정이 업데이트되었습니다.
#XMSG: Error in updating schedule
errorUpdateSchedule=지금은 일정을 업데이트할 수 없습니다. 다시 시도하십시오. \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=일정을 삭제하시겠습니까?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=일정이 삭제되었습니다.
#XMSG: Schedule deletion error message
errorSeleteSchedule=지금은 일정을 삭제할 수 없습니다. 다시 시도하십시오.
#XFLD: Authorise service title
authServiceLabel=서비스에 권한 부여
#XMSG: Redirecting message
redirectingText=인증을 위해 리디렉션 중입니다.
#XMSG: Redirection success message
redirectSuccess=예약된 반복 태스크를 실행할 권한을 부여했습니다.
#XMSG: Start time label
startTimeFormatLabel=시작 시간({0}시간 형식)
#XMSG: Start date and end date range validation
dateRangeValidationText=시작일은 종료일보다 빨라야 합니다.
#XMSG: Delete schedule error
errorDeleteSchedule=일정을 삭제하는 동안 오류가 발생했습니다. \n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=시간
#XFLD: Plural Recurrence text for Day
dayPluraltext=일
#XFLD: Plural Recurrence text for Month
monthPluralText=개월
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=시간
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=일
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=개월
#XFLD: Recurrence text for Hour
hourText=시간별
#XFLD: Recurrence text for Day
daytext=일별
#XFLD: Recurrence text for Month
monthText=월별
#XMSG: Start date cannot be in the past
startDateValidationText=시작일은 과거 일자가 아니어야 합니다.
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=1 ~ 23 사이의 값을 입력하십시오.
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=1 ~ 31 사이의 값을 입력하십시오.
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=1 ~ 12 사이의 값을 입력하십시오.
#XMSG: Invalid date text
invalidDateText=일자가 잘못되었습니다.
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=반복
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=시작일
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=종료일
#XMSG: Schedule popover end date label
naText=N/A
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=시간대
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=소유자
#XMSG: text for not available
naText=N/A
#XMSG: String Every
everyString=매
#XMSG: on label
onLabel=일자/요일
#XMSG: on label
onLabelMonth=일자
#XFLD: Label for Sunday
sunLabel=일
#XFLD: Label for Monday
monLabel=월
#XFLD: Label for Tuesday
tueLabel=화
#XFLD: Label for Wednesday
wedLabel=수
#XFLD: Label for Thursday
thuLabel=목
#XFLD: Label for Friday
friLabel=금
#XFLD: Label for Saturday
satLabel=토
#XFLD: Recurrence text for Week
weekText=주
#XFLD: Create Schedule Dialog Title
createDialogTitle=일정 생성
#XFLD: Edit Dialog title
editDialogTitle=일정 편집
#XFLD: Selected Object Title
selectedObjectText=선택됨
#XMSG: Table Text
tableText=테이블
#XMSG: View Text
viewText=뷰
#XMSG: Data flow text
dataFlowText=데이터 흐름
#XMSG: Select a day of week error message
weekRecErrorText=요일을 하나 이상 선택하십시오.
#XFLD: Label for Owner
ownerLabel=소유자
#XFLD: Button label for takeover schedule
takeoverLabel=소유자 변경
#XFLD: Created By label
createdbyLabel=생성자
#XFLD: Changed By label
changedbyLabel=최종 수정자
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=SAP Datasphere에서 예약된 태스크를 실행하려면 사용자 인증이 필요합니다.\n 이 태스크를 실행하도록 선택하면 이 예약된 태스크가 귀하를 소유자로 하여 실행됩니다.\n 예약된 태스크를 실행하기 전에 다음을 제공했는지 확인하십시오.
#XMSG: Schedule takeover success message
ownerChangeSuccessText=일정을 가져왔습니다.
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=일정을 가져오지 못했습니다.
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=일정 업데이트에 오류가 발생하여 일정을 가져오지 못했습니다.
#XFLD: Text for confirm
confirm=확인
#XMSG: Create schedule error message
createScheduleError=일정을 생성하지 못했습니다.
#XMSG: Schedule update failed message
updateScheduleError=일정을 업데이트하지 못했습니다.
#XMSG: Schedule delete error message
deleteScheduleError=일정을 삭제하지 못했습니다.
#XFLD: Label for Frequency
frequencyLabel=주기
#XFLD: Label for Frequency
frequencyLabelNew=설정
#XFLD: Label for repeat
repeatLabel=반복
#XFLD: Label for repeat
repeatLabelNeww=주기:
#XFLD: Labe for text at
atLabel=시간
#XFLD: Label for starting
startingLabel=시작
#XFLD: Label for Start
startLabel=시작
#XFLD: Label for Ending
validityLabel=유효 기간
#XFLD: Label for end
endLabel=종료
#XMSG: Message for no end date
noEndDateMsg=반복은 무기한으로 발생합니다.
#XFLD: Assign label
assignLabel=나에게 지정
#XFLD: Overview label
overviewLabel=개요
#XFLD: Next runs field
nextRunsLabel=다음 실행:
#XFLD: Next runs field
nextRunsLabelForPopOver=다음 실행
#XFLD: Expired field
taskPopoverExpiredLabel=만료됨
#XFLD: Expired Message
taskPopoverExpiredMessage=일정이 만료되었습니다. 연장하려면 일정을 편집하고 종료일을 변경하십시오.
#XFLD: label for Day-of-month
dayOfmonthLabel=월의 날짜
#XFLD: label for Minute
minuteLabel=분
#XFLD: label for hour
hourLabel=시간
#XFLD: Last Day of month selection
lastDayText=월말일
#XFLD: Hourly unit description label
hourUnitLabel=시간
#XFLD: Daily recurrence unit description label
dayUnitLabel=월별 일자
#XFLD: Monthly receurrence unit description label
monthUnitLabel=연도 월
#XFLD: label for off
offLabel=꺼짐
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg={1}, {2}분 시작하여 매 {0}시간마다 수행합니다.
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg={1}, {2}에 시작하여 매 {0}일마다 수행합니다.
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg={1}, {2}, {3}에 시작하여 매 {0}월마다 수행합니다.
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=매주 {0}, {1}에 수행합니다.
#XMSG: Schedule expired message
scheduleExpiredLabel=일정이 만료되었습니다.
#XFLD: Create Schedule Dialog Title
createScheduleTitle="{0}"의 일정 생성
#XFLD: Edit Dialog title
createScheduleTitleNew={0}: 일정 생성
#XFLD: Edit Dialog title
editScheduleTitle="{0}"의 일정 편집
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}: 일정 편집
#XFLD: Edit Schedule Button
editSchedule=일정 편집
#XFLD: Full Label for Sunday
sunFullLabel=일요일
#XFLD: Full Label for Monday
monFullLabel=월요일
#XFLD: Full Label for Tuesday
tueFullLabel=화요일
#XFLD: Full Label for Wednesday
wedFullLabel=수요일
#XFLD: Full Label for Thursday
thuFullLabel=목요일
#XFLD: Full Label for Friday
friFullLabel=금요일
#XFLD: Full Label for Saturday
satFullLabel=토요일
#XFLD: Weekly text
weeklyText=주별
#XFLD: Set start day text
startEndDateText=시작일 및 종료일 설정
#XMSG: Schedule runs indefinitely message
validityOffText=일정은 무기한 실행됩니다.
#XFLD: Summary label
summaryLabel=요약
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg={0}시간마다 실행됩니다. 실행 주기가 매일 0:00(UTC)에 재설정됩니다.
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg={1}(UTC)에 {0}일마다 실행됩니다. 실행 주기가 매월 1일에 재설정됩니다.
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg={1}, {2}(UTC)에 {0}개월마다 실행됩니다. 실행 주기가 매년 첫 날에 재설정됩니다.
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg={0}, {1}(UTC)에 매주 실행됩니다.
#XFLD: Future runs label with time zone info
futureRunsLabel=차후 실행
#XFLD: Start & End Date label
startEndDateLabel=시작일 및 종료일 설정
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=첫 번째 실행이 10분 내에 시작될 예정이어서 실행되지 않을 수 있습니다.
#XFLD: Label for text at UTC
atUTCLabel=시간(UTC)
#XMSG: Authorize expired text
authExpiredText=반복 태스크를 실행할 수 있는 권한이 만료되었습니다. 일정 계획 태스크를 계속하려는 경우 SAP가 사용자 대신 일정을 실행할 수 있도록 권한을 갱신해야 합니다.
#XMSG: Authorization expiring soon text
authExpiringSoonText=반복 태스크를 실행할 수 있는 권한이 곧 만료됩니다. SAP가 사용자 대신 일정 실행을 계속할 수 있도록 권한을 갱신하십시오.
#XMSG: Enter as Label text
enterAsText=입력 방식
#XMSG: Enter as Label text
enterAsTextNew=설정 유형
#XMSG: standard schedule label text
simpleScheduleText=단순 일정
#XMSG: Cron expression label text
cronScheduleText=CRON 표현식
#XMSG: Time Range label text
timeRangeLabelText=시간 범위
#XMSG: Time Range label text
timeRangeLabelTextNew=일자 범위
#XMSG: Ownership label
ownershipLabelText=소유권
#XMSG: Show runs in the selected timezone label
showRunsInText=실행 표시 시간대
#XMSG: Text for schedule pause
pauseScheduleLabel=일정 일시 중지:
#XMSG: Show Warning for schedule pause
schedulePausedWarning=이 일정이 일시 중지되었습니다. 가능한 차후 실행이 없습니다.
#XMSG: Cycle run text for hourly
hourlyCycleResetText=실행 주기가 매일 00:00에 재설정됩니다
#XMSG: Cycle run text for Daily
dailyCycleResetText=실행 주기가 매월 첫 날에 재설정됩니다.
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=실행 주기가 매년 1월 1일에 재설정됩니다
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=실행 주기가 매주 일요일에 재설정됩니다
#XMSG: Day label
dayLabel=일(월)
#XMSG: Monthly label
monthlyLabel=월
#XMSG: Weekly label
weeklyLabel=일(주)
#XMSG: Time in UTC label
utcTimeLabel=현재 시간(UTC)

#XMSG: January
monthLabel1=1월
#XMSG: February
monthLabel2=2월
#XMSG: March
monthLabel3=3월
#XMSG: April
monthLabel4=4월
#XMSG: May
monthLabel5=5월
#XMSG: June
monthLabel6=6월
#XMSG: July
monthLabel7=7월
#XMSG: August
monthLabel8=8월
#XMSG: September
monthLabel9=9월
#XMSG: October
monthLabel10=10월
#XMSG: November
monthLabel11=11월
#XMSG: December
monthLabel12=12월

weekdayLabel0=일요일
weekdayLabel1=월요일
weekdayLabel2=화요일
weekdayLabel3=수요일
weekdayLabel4=목요일
weekdayLabel5=금요일
weekdayLabel6=토요일

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1={0}분마다({1}부터 {2}까지)
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2={0}분마다
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3={0}분마다({1}에 시작)
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4={0}분

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=지난 {0}시간마다({1}부터 {2}까지)
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=지난 {0}시간마다
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=지난 {0}시간마다({1}에 시작)
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=지난 {0}시

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=월의 {0}일마다({1}부터 {2}까지)
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=월의 {0}일마다
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=월의 {0}일마다({1}에 시작)
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4={0}일에

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1={0}개월마다({1}부터 {2}까지)
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2={0}개월마다
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3={0}개월마다({1}에 시작)
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4={0}에

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=주의 {0}일마다({1}부터 {2}까지)
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=주의 {0}일마다
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=주의 {0}일마다({1}에 시작)
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4={0}에
#XMSG: And text
andText=및
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=지원되는 값이나 값의 조합을 입력하십시오.
#XMSG: default information about cron string
defaultCronStringInfoText=CRON 표현식에 지원되는 값: <strong>숫자 * , / </strong>
#XMSG: Parameterized info about cron string
cronStringinfoText=선택된 필드에 지원되는 값: <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=자세한 정보
#XMSG: Hide details text
hideDetailsText=세부사항 숨기기
#XMSG: Label for starting at UTC Text
startingText=시작 시간(UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=클립보드에 복사
#XMSG: cron copied to clipboard
cronCopiedText=CRON 표현식이 클립보드로 복사되었습니다.
#XMSG: error message for invalid cron copy
invalidCronCopyText=잘못된 CRON 표현식을 복사할 수 없습니다.
#XMSG: No preview label
noPreviewText=오류가 있거나 누락된 값이 있어서 미리보기를 표시할 수 없습니다. 일정을 검토하십시오.
#XMSG: Label for create button
createText=생성
#XMSG: Label for Save button
saveText=저장
#XMSG: Label for text leave
leaveText=나가기
#XMSG: warn message when user click cancel button
cancelWarnText=나가면 변경사항이 손실됩니다.
#XMSG: Expired schedule warning
expiredTaskWarnText=이 일정은 만료되었습니다. 복제를 계속하려면 새로운 종료일을 선택하십시오.
#XMSG: Expired schedule message
expiredScheduleMessageText=이 일정은 만료되었습니다. 반복 태스크를 계속 실행하려면 새로운 종료일을 선택하십시오.
#XMSG: Expired schedule message
expiredTaskWarnTextNew=이 일정은 만료되었습니다. 연장하려면 종료일을 변경하십시오.
#XMSG: Expired schedule Text
expiredScheduleText=만료됨
#XMSG: No future runs generated
noFutureRunsText=다음 실행이 없습니다.
#XMSG: Text for Local time
localTimeText=현지 시간
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=선택된 일자 범위 안에 실행이 없습니다.
#XMSG: Warning text for expired end date
endDateExpiredText=종료일이 과거입니다.
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=시작일을 지정하십시오.
#XMSG: UTC info label
utcInfoLabelText=일정은 협정 세계시(UTC)로 생성됩니다. 현재 UTC 시간은 {0}입니다.
#XMSG: UTC info label
utcInfoLabelTextNew=협정 세계시(UTC)입니다. 현재 UTC 시간은 {0}입니다.
#XMSG: Invalid cron paste error message
invalidCronPasteText=CRON 표현식이 올바르지 않아서 붙여넣을 수 없습니다. 올바른 표현식으로 다시 시도하십시오.
#XMSG: Weekly schedule error text
invalidWeeklyRecText=일자를 하나 이상 선택하십시오.
#XFLD: No End date text
noEndDateText=종료일 없음
#XFLD: Help Button
openHelpButtonText=도움말 표시
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=일자가 지정된 월의 최대 일수를 초과했습니다.
#XMSG: Redundancy error in cron string
cronRedundantErrorText=이 패턴에 중복 표현식이 있습니다. CSV에서 * 또는 / 패턴을 제거하십시오.
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=0 ~ 59 사이의 값을 입력하십시오.
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=10보다 큰 값을 입력하십시오.
#XMSG: Error message for missing start time
startTimeErrorText=시간을 선택하십시오.
#XMSG: Error message handling for error code
invalidScheduleBodyError=요청 본문에 잘못된 데이터가 포함되어 있습니다.
#XMSG: Error message handling for error code
scheduleNotFoundError=일정을 찾을 수 없습니다.
#XMSG: Error message handling for error code
actionForbiddenError=필요한 권한이 없습니다.
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=이미 존재하는 일정을 생성하려고 시도 중입니다.
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=매일 재설정
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=매월 재설정
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=매년 재설정
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=매주 재설정
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=매시간 재설정
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=매일 재설정:
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=매월 재설정:
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=매년 재설정:
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=매주 재설정:
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=매시간 재설정:
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText={1}일마다 {0}에
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=태스크 체인과 예약된 반복 태스크를 실행할 권한을 부여하십시오.
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=일정 실행 시 스냅샷을 로드합니다. 이렇게 하면 복제 모드가 실시간 복제에서 배치 복제로 변경되고 데이터가 더 이상 실시간으로 업데이트되지 않습니다.
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=선택한 일정을 삭제하시겠습니까?
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn=선택한 오브젝트 {1}개 중 {0}개에 일정이 있습니다. \n일정을 삭제하시겠습니까?

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=선택한 오브젝트의 일정에 대한 소유권을 가지시겠습니까?
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=선택한 오브젝트 {1}개 중 {0}개에 대해 일정이 정의되어 있습니다. 해당 일정의 소유권을 가지시겠습니까?

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=선택한 오브젝트에 대한 일정을 일시 중지하시겠습니까?
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn=선택한 오브젝트 {0}개 중 {1}개에 실행 중인 일정이 있습니다. 이를 일시 중지하시겠습니까?

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=선택한 오브젝트의 일정을 다시 시작하시겠습니까?
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn=선택한 오브젝트 {0}개 중 {1}개에 일시 중지된 일정이 있습니다. 다시 시작하시겠습니까?

#XTXT: Warning text
warningText=경고
#XTXT: Text for schedule pause
pauseScheduleText=일정 일시 중지
#XTXT: Text for resume schedule
resumeScheduleText=일정 다시 시작
#XTXT: Text for assigning schedule
assignScheduleText=나에게 일정 지정

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=반복 태스크를 실행할 수 있는 권한이 {0}에 만료됩니다. 계획된 태스크를 계속 실행하려면 귀하의 동의가 다시 필요합니다.

#XMSG: Reauthorize text
reauthorize=권한 재부여

#XTXT: Duration text
duration=기간
#XTXT: Time frame label text
timeFrame=타임 프레임
#XTXT: Hours Label text
hours=시간
#XTXT: Minutes Label text
minutes=분
#XTXT: Minutes Label text
minutesNew=분

#XTXT: Minute Recurrence type text
byMinute=분
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=0 - 59 사이의 값을 입력하거나 주기를 {0}분 이상으로 설정하십시오.
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=0 ~ 59 사이의 값을 입력하십시오.
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=기간({0}분)이 예약된 주기({1}분)보다 깁니다.
#XTXT: Selected time zone At
timeZoneAt=시간
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=시작 시간
