#XMSG: Message to ask user to authorize
authorizeWarn=Upoważnij nas do wykonywania zaplanowanych powtarzających się zadań.
#XFLD: Authorize field value
authorise=Autoryzuj
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=Zaplanuj dla "{0}"
#XMSG: Ok message
ok=OK
#XMSG: Schedule
schedule=Harmonogram
#XMSG: Cancel message
cancel=Anuluj
#XMSG: Close message
close=Zamknij
#XFLD: lable to set recurrence
recurrenceLabel=Okresowość
#XFLD: label to set the recurrence value
everyLabel=Co
#XFLD: Start date field
startDateLabel=Data rozpoczęcia
#XFLD: Start date field
endDateLabel=Data zakończenia
#XFLD: TimeZone field
timeZoneLabel=Strefa czasowa
#XMSG: validation text
recurrenceValidationText1=Wprowadź wartość całkowitą wynoszącą 1 lub wię<PERSON>zą
#XMSG: validation text
recurrenceValidationText2=W<PERSON><PERSON><PERSON><PERSON> datę rozpoczęcia
#XMSG: validation text
recurrenceValidationText3=Wprowadź datę rozpoczęcia
#XMSG: validation text
noObjectSelectedText=Wybierz tabelę zdalną do zaplanowania
#XMSG: error text
errorLabel=Błąd
#XMSG: Schedule created alert message
createScheduleSuccess=Utworzono harmonogram
#XMSG: Error in creating schedule
errorCreateSchedule=Tworzenie harmonogramu nie powiodło się. Spróbuj ponownie \N {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=Zaktualizowano harmonogram
#XMSG: Error in updating schedule
errorUpdateSchedule=Aktualizacja harmonogramu nie powiodła się. Spróbuj ponownie. \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=Czy na pewno chcesz usunąć ten harmonogram?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Usunięto harmonogram.
#XMSG: Schedule deletion error message
errorSeleteSchedule=Usunięcie harmonogramu nie powiodło się. Spróbuj ponownie.
#XFLD: Authorise service title
authServiceLabel=Autoryzacja usługi
#XMSG: Redirecting message
redirectingText=Przekierowanie w celu uwierzytelnienia.
#XMSG: Redirection success message
redirectSuccess=Zostaliśmy upoważnieni do wykonywania zaplanowanych przez Ciebie powtarzających się zadań.
#XMSG: Start time label
startTimeFormatLabel=Godzina rozpoczęcia (format {0}-godzinny)
#XMSG: Start date and end date range validation
dateRangeValidationText=Data rozpoczęcia musi być wcześniejsza od daty zakończenia.
#XMSG: Delete schedule error
errorDeleteSchedule=Wystąpił błąd podczas usuwania harmonogramu \n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=Godzin(y)
#XFLD: Plural Recurrence text for Day
dayPluraltext=Dni
#XFLD: Plural Recurrence text for Month
monthPluralText=Miesiące (miesięcy)
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=godz.
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=dni
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=mies.
#XFLD: Recurrence text for Hour
hourText=Co godzinę
#XFLD: Recurrence text for Day
daytext=Codziennie
#XFLD: Recurrence text for Month
monthText=Co miesiąc
#XMSG: Start date cannot be in the past
startDateValidationText=Data początkowa nie może być w przeszłości
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=Wprowadź wartość z zakresu od 1 do 23.
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=Wprowadź wartość z zakresu od 1 do 31.
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=Wprowadź wartość z zakresu od 1 do 12.
#XMSG: Invalid date text
invalidDateText=Nieprawidłowa data
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=Okresowość
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=Data rozpoczęcia
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=Data zakończenia
#XMSG: Schedule popover end date label
naText=Nie dotyczy
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=Strefa czasowa
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=Właściciel
#XMSG: text for not available
naText=Nie dotyczy
#XMSG: String Every
everyString=Co
#XMSG: on label
onLabel=W
#XMSG: on label
onLabelMonth=W dniu miesiąca
#XFLD: Label for Sunday
sunLabel=Ndz
#XFLD: Label for Monday
monLabel=Pon
#XFLD: Label for Tuesday
tueLabel=Wt
#XFLD: Label for Wednesday
wedLabel=Śr
#XFLD: Label for Thursday
thuLabel=Czw
#XFLD: Label for Friday
friLabel=Pt
#XFLD: Label for Saturday
satLabel=Sob
#XFLD: Recurrence text for Week
weekText=Tydzień
#XFLD: Create Schedule Dialog Title
createDialogTitle=Utwórz harmonogram
#XFLD: Edit Dialog title
editDialogTitle=Edytuj harmonogram
#XFLD: Selected Object Title
selectedObjectText=Wybrane
#XMSG: Table Text
tableText=Tabela
#XMSG: View Text
viewText=Widok
#XMSG: Data flow text
dataFlowText=Przepływ danych
#XMSG: Select a day of week error message
weekRecErrorText=Wybierz co najmniej jeden dzień tygodnia
#XFLD: Label for Owner
ownerLabel=Właściciel
#XFLD: Button label for takeover schedule
takeoverLabel=Zmień właściciela
#XFLD: Created By label
createdbyLabel=Autor
#XFLD: Changed By label
changedbyLabel=Ostatnio zmodyfikowane przez
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=Uwierzytelnianie użytkownika jest wymagane, aby wykonać zaplanowane zadania w SAP Datasphere.\n Wykonanie tego zadania, spowoduje, że zaplanowane zadanie zostanie wykonane z Tobą jako osobą odpowiedzialną.\n Zanim zaplanowane zadanie zostanie wykonane, upewnij się, że wyraziłeś zgodę na wykonywanie zadań w swoim imieniu.
#XMSG: Schedule takeover success message
ownerChangeSuccessText=Pomyślne przejęcie harmonogramu.
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=Przejęcie harmonogramu nie powiodło się.
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=Przejęcie harmonogramu nie powiodło się z powodu błędu podczas aktualizacji harmonogramu.
#XFLD: Text for confirm
confirm=Potwierdź
#XMSG: Create schedule error message
createScheduleError=Tworzenie harmonogramu nie powiodło się.
#XMSG: Schedule update failed message
updateScheduleError=Aktualizacja harmonogramu nie powiodła się.
#XMSG: Schedule delete error message
deleteScheduleError=Usuwanie harmonogramu nie powiodło się.
#XFLD: Label for Frequency
frequencyLabel=Częstotliwość
#XFLD: Label for Frequency
frequencyLabelNew=Ustawienia
#XFLD: Label for repeat
repeatLabel=Powtórz
#XFLD: Label for repeat
repeatLabelNeww=Częstotliwość:
#XFLD: Labe for text at
atLabel=O godzinie
#XFLD: Label for starting
startingLabel=Uruchamianie
#XFLD: Label for Start
startLabel=Rozpocznij
#XFLD: Label for Ending
validityLabel=Ważność
#XFLD: Label for end
endLabel=Zakończ
#XMSG: Message for no end date
noEndDateMsg=Okresowość będzie obowiązywać bezterminowo.
#XFLD: Assign label
assignLabel=Przypisz do mnie
#XFLD: Overview label
overviewLabel=Przegląd
#XFLD: Next runs field
nextRunsLabel=Następne przebiegi:
#XFLD: Next runs field
nextRunsLabelForPopOver=Następne przebiegi
#XFLD: Expired field
taskPopoverExpiredLabel=Wygasłe
#XFLD: Expired Message
taskPopoverExpiredMessage=Ten harmonogram wygasł. Aby go przedłużyć, edytuj go i zmień datę zakończenia.
#XFLD: label for Day-of-month
dayOfmonthLabel=Dzień miesiąca
#XFLD: label for Minute
minuteLabel=Min
#XFLD: label for hour
hourLabel=Godz.
#XFLD: Last Day of month selection
lastDayText=Ostatni dzień miesiąca
#XFLD: Hourly unit description label
hourUnitLabel=Godzina dnia
#XFLD: Daily recurrence unit description label
dayUnitLabel=Dni na miesiąc
#XFLD: Monthly receurrence unit description label
monthUnitLabel=Miesiące w roku
#XFLD: label for off
offLabel=Wyłączone
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=Co {0} godz. od {1} {2} min.
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=Co {0} dni od {1} godz. {2}.
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=Co {0} mies. od {1} w {2} godz. {3}.
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=Co tydzień w {0} o {1}.
#XMSG: Schedule expired message
scheduleExpiredLabel=Harmonogram wygasł.
#XFLD: Create Schedule Dialog Title
createScheduleTitle=Utwórz harmonogram dla "{0}"
#XFLD: Edit Dialog title
createScheduleTitleNew={0}: Tworzenie harmonogramu
#XFLD: Edit Dialog title
editScheduleTitle=Edytuj harmonogram dla "{0}"
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}: Edytowanie harmonogramu
#XFLD: Edit Schedule Button
editSchedule=Edytuj harmonogram
#XFLD: Full Label for Sunday
sunFullLabel=ndz
#XFLD: Full Label for Monday
monFullLabel=pon
#XFLD: Full Label for Tuesday
tueFullLabel=wt
#XFLD: Full Label for Wednesday
wedFullLabel=śr
#XFLD: Full Label for Thursday
thuFullLabel=czw
#XFLD: Full Label for Friday
friFullLabel=pt
#XFLD: Full Label for Saturday
satFullLabel=sob
#XFLD: Weekly text
weeklyText=Co tydzień
#XFLD: Set start day text
startEndDateText=Ustaw datę rozpoczęcia i zakończenia
#XMSG: Schedule runs indefinitely message
validityOffText=Harmonogram będzie wykonywany przez czas nieoznaczony.
#XFLD: Summary label
summaryLabel=Podsumowanie
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=Wykonywane co {0} godz. Przebieg cyklu jest resetowany codziennie o 0:00 (UTC).
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=Wykonywane co {0} dni o godz. {1} (UTC). Przebieg cyklu jest resetowany pierwszego dnia miesiąca.
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=Wykonywane co {0} mies. w {1} o godz. {2} (UTC). Przebieg cyklu jest resetowany pierwszego dnia każdego roku.
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=Wykonywane co tydzień w {0} o {1} (UTC).
#XFLD: Future runs label with time zone info
futureRunsLabel=Przyszłe przebiegi
#XFLD: Start & End Date label
startEndDateLabel=Ustaw datę rozpoczęcia i zakończenia
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=Pierwszy przebieg może nie zostać wykonany, ponieważ ma zostać rozpoczęty w ciągu mniej niż 10 minut.
#XFLD: Label for text at UTC
atUTCLabel=O godz. (UTC)
#XMSG: Authorize expired text
authExpiredText=Uprawnienie do wykonywania powtarzających się zadań wygasło. Jeśli chcesz kontynuować planowanie zadań, musisz odnowić uprawnienie, aby umożliwić SAP wykonanie harmonogramu w Twoim imieniu.
#XMSG: Authorization expiring soon text
authExpiringSoonText=Uprawnienie do wykonywania powtarzających się zadań wkrótce wygaśnie. Odnów uprawnienie, aby umożliwić SAP dalsze wykonywanie harmonogramu w Twoim imieniu.
#XMSG: Enter as Label text
enterAsText=Otwórz jako
#XMSG: Enter as Label text
enterAsTextNew=Typ ustawienia
#XMSG: standard schedule label text
simpleScheduleText=Prosty harmonogram
#XMSG: Cron expression label text
cronScheduleText=Wyrażenie Cron
#XMSG: Time Range label text
timeRangeLabelText=Przedział czasu
#XMSG: Time Range label text
timeRangeLabelTextNew=Zakres dat
#XMSG: Ownership label
ownershipLabelText=Własność
#XMSG: Show runs in the selected timezone label
showRunsInText=Pokaż przebiegi w
#XMSG: Text for schedule pause
pauseScheduleLabel=Wstrzymaj harmonogram:
#XMSG: Show Warning for schedule pause
schedulePausedWarning=Ten harmonogram jest wstrzymany. Przyszłe przebiegi nie są dostępne.
#XMSG: Cycle run text for hourly
hourlyCycleResetText=Cykl przebiegu jest resetowany o 00:00 każdego dnia.
#XMSG: Cycle run text for Daily
dailyCycleResetText=Cykl przebiegu jest resetowany pierwszego dnia każdego miesiąca.
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=Cykl przebiegu jest resetowany 1. stycznia każdego roku.
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=Cykl przebiegu jest resetowany co tydzień w sobotę.
#XMSG: Day label
dayLabel=Dzień (miesiąc)
#XMSG: Monthly label
monthlyLabel=Miesiąc
#XMSG: Weekly label
weeklyLabel=Dzień (tydzień)
#XMSG: Time in UTC label
utcTimeLabel=Bieżący czas (UTC)

#XMSG: January
monthLabel1=Styczeń
#XMSG: February
monthLabel2=Luty
#XMSG: March
monthLabel3=Marzec
#XMSG: April
monthLabel4=Kwiecień
#XMSG: May
monthLabel5=Maj
#XMSG: June
monthLabel6=Czerwiec
#XMSG: July
monthLabel7=Lipiec
#XMSG: August
monthLabel8=Sierpień
#XMSG: September
monthLabel9=Wrzesień
#XMSG: October
monthLabel10=Październik
#XMSG: November
monthLabel11=Listopad
#XMSG: December
monthLabel12=Grudzień

weekdayLabel0=ndz
weekdayLabel1=pon
weekdayLabel2=wt
weekdayLabel3=śr
weekdayLabel4=czw
weekdayLabel5=pt
weekdayLabel6=sob

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=Co {0} minutę od {1} do {2}.
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=Co {0} minutę.
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=Co {0} minutę od {1}.
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=W {0} minucie.

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=po każdej {0} godzinie od {1} do {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=po każdej {0} godzinie
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=po każdej {0} godzinie, począwszy od {1}
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=po godzinie {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=co {0} dzień miesiąca od {1} do {2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=co {0} dzień miesiąca
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=co {0} dzień miesiąca, począwszy od {1}
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=w {0} dzień miesiąca

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=co {0} miesiąc od {1} do {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=co {0} miesiąc
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=co {0} miesiąc, począwszy od {1}
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=w {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=co {0} dzień tygodnia od {1} do {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=co {0} dzień tygodnia
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=co {0} dzień tygodnia, począwszy od {1}
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=w {0}
#XMSG: And text
andText=i
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=Wprowadź obsługiwaną wartość lub kombinację wartości:
#XMSG: default information about cron string
defaultCronStringInfoText=Obsługiwane wartości dla wyrażenia cron: <strong>cyfry * , / </strong>
#XMSG: Parameterized info about cron string
cronStringinfoText=Obsługiwane wartości dla wybranego pola: <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=Dowiedz się więcej
#XMSG: Hide details text
hideDetailsText=Ukryj szczegóły
#XMSG: Label for starting at UTC Text
startingText=Godzina rozpoczęcia (UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=Kopiuj do schowka
#XMSG: cron copied to clipboard
cronCopiedText=Wyrażenie cron skopiowano do schowka
#XMSG: error message for invalid cron copy
invalidCronCopyText=Nie można skopiować nieprawidłowego wyrażenia cron
#XMSG: No preview label
noPreviewText=Nie można wyświetlić podglądu z powodu błędów lub brakujących wartości. Sprawdź harmonogram.
#XMSG: Label for create button
createText=Utwórz
#XMSG: Label for Save button
saveText=Zapisz
#XMSG: Label for text leave
leaveText=Wyjdź
#XMSG: warn message when user click cancel button
cancelWarnText=Jeśli wyjdziesz, Twoje zmiany zostaną utracone.
#XMSG: Expired schedule warning
expiredTaskWarnText=Ten harmonogram wygasł. Wybierz nową datę zakończenia, aby kontynuować replikację.
#XMSG: Expired schedule message
expiredScheduleMessageText=Ten harmonogram wygasł. Wybierz nową datę zakończenia, aby kontynuować wykonywanie zadań okresowych.
#XMSG: Expired schedule message
expiredTaskWarnTextNew=Ten harmonogram wygasł. Aby go przedłużyć, zmień datę zakończenia.
#XMSG: Expired schedule Text
expiredScheduleText=Wygasłe
#XMSG: No future runs generated
noFutureRunsText=Brak dostępnego kolejnego przebiegu.
#XMSG: Text for Local time
localTimeText=Czas lokalny
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=Brak przebiegów w wybranym przedziale czasu.
#XMSG: Warning text for expired end date
endDateExpiredText=Data zakończenia wypada w przeszłości.
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=Określ datę rozpoczęcia.
#XMSG: UTC info label
utcInfoLabelText=Harmonogramy są tworzone w Uniwersalnym czasie koordynowanym (UTC). Bieżący czas UTC to {0}.
#XMSG: UTC info label
utcInfoLabelTextNew=Uniwersalny czas koordynowany (UTC). Bieżący czas UTC to {0}.
#XMSG: Invalid cron paste error message
invalidCronPasteText=Nie można wkleić wyrażenia cron, ponieważ jest nieprawidłowe. Spróbuj ponownie z prawidłowym wyrażeniem.
#XMSG: Weekly schedule error text
invalidWeeklyRecText=Wybierz co najmniej jeden dzień.
#XFLD: No End date text
noEndDateText=Brak daty końcowej
#XFLD: Help Button
openHelpButtonText=Pokaż pomoc
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=Data przekracza maksymalną liczbę dni dla danego miesiąca
#XMSG: Redundancy error in cron string
cronRedundantErrorText=Ten wzorzec ma zbędne wyrażenia. Usuń wzorce: * lub / z listy oddzielonej przecinkami.
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=Wprowadź wartość z zakresu od 0 do 59.
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=Wprowadź wartość powyżej 10.
#XMSG: Error message for missing start time
startTimeErrorText=Wybierz czas.
#XMSG: Error message handling for error code
invalidScheduleBodyError=Treść żądania zawiera nieprawidłowe dane.
#XMSG: Error message handling for error code
scheduleNotFoundError=Nie można znaleźć harmonogramu.
#XMSG: Error message handling for error code
actionForbiddenError=Brak wymaganych uprawnień.
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=Próbujesz utworzyć harmonogram, który już istnieje.
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=Resetuj codziennie
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=Resetuj co miesiąc
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=Resetuj co rok
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=Resetuj co tydzień
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=Resetuj co godzinę
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=Resetuj codziennie:
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=Resetuj co miesiąc:
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=Resetuj co rok:
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=Resetuj co tydzień:
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=Resetuj co godzinę:
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=O godzinie {0} co {1} dzień
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=Upoważnij nas do wykonywania łańcuchów danych, a także zaplanowanych powtarzających się zadań.
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=Przebieg harmonogramu spowoduje wczytanie migawki. W związku z tym tryb replikacji zmieni się z replikacji w czasie rzeczywistym na replikację partii, a dane nie będą już aktualizowane w czasie rzeczywistym.
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=Czy na pewno usunąć wybrane harmonogramy?
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn=Harmonogramy są zawarte w {0} z {1} wybranych obiektów. \nCzy na pewno chcesz usunąć harmonogramy?

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=Czy chcesz przejąć na własność harmonogramy dla wybranych obiektów?
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=Harmonogramy są definiowane dla {0} z {1} wybranych obiektów. Czy chcesz przejąć na własność te harmonogramy?

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=Czy chcesz wstrzymać harmonogramy wybranych obiektów?
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn=Trwa wykonywanie harmonogramów dla {0} z {1} wybranych obiektów. Czy chcesz je wstrzymać?

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=Czy chcesz wznowić harmonogramy wybranych obiektów?
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn=Istnieją wstrzymane harmonogramy dla {0} z {1} wybranych obiektów. Czy chcesz je wznowić?

#XTXT: Warning text
warningText=Ostrzeżenie
#XTXT: Text for schedule pause
pauseScheduleText=Wstrzymaj harmonogram
#XTXT: Text for resume schedule
resumeScheduleText=Wznów harmonogram
#XTXT: Text for assigning schedule
assignScheduleText=Przypisz harmonogram do mnie

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=Uprawnienia do wykonania Twoich zadań okresowych wygasną w dniu {0}. Aby kontynuować wykonywanie zaplanowanych zadań, wymagana jest Twoja ponowna zgoda.

#XMSG: Reauthorize text
reauthorize=Autoryzuj ponownie

#XTXT: Duration text
duration=Czas trwania
#XTXT: Time frame label text
timeFrame=Ramy czasowe
#XTXT: Hours Label text
hours=godz.
#XTXT: Minutes Label text
minutes=min
#XTXT: Minutes Label text
minutesNew=min

#XTXT: Minute Recurrence type text
byMinute=min
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=Wprowadź wartość z przedziału od 0 do 59 lub ustaw częstotliwość na większą lub równą {0} min
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=Wprowadź wartość pomiędzy 0 i 59.
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=Czas trwania {0} minut jest dłuższy niż zaplanowana częstotliwość {1} minut.
#XTXT: Selected time zone At
timeZoneAt=O godzinie
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=Rozpoczęcie o
