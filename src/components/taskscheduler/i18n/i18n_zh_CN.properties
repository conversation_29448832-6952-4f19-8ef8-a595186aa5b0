#XMSG: Message to ask user to authorize
authorizeWarn=授权我们运行你计划的周期性任务。
#XFLD: Authorize field value
authorise=授权
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=为 "{0}" 计划
#XMSG: Ok message
ok=确定
#XMSG: Schedule
schedule=计划
#XMSG: Cancel message
cancel=取消
#XMSG: Close message
close=关闭
#XFLD: lable to set recurrence
recurrenceLabel=重复周期
#XFLD: label to set the recurrence value
everyLabel=每
#XFLD: Start date field
startDateLabel=开始日期
#XFLD: Start date field
endDateLabel=结束日期
#XFLD: TimeZone field
timeZoneLabel=时区
#XMSG: validation text
recurrenceValidationText1=输入 1 或更大的整数值
#XMSG: validation text
recurrenceValidationText2=输入开始日期
#XMSG: validation text
recurrenceValidationText3=输入开始日期
#XMSG: validation text
noObjectSelectedText=选择要计划的远程表。
#XMSG: error text
errorLabel=错误
#XMSG: Schedule created alert message
createScheduleSuccess=已创建计划
#XMSG: Error in creating schedule
errorCreateSchedule=目前没能创建计划。请重试。\n {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=已更新计划
#XMSG: Error in updating schedule
errorUpdateSchedule=无法立刻更新计划。请重试。\n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=是否确定要删除计划？
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=已删除计划。
#XMSG: Schedule deletion error message
errorSeleteSchedule=无法立刻删除计划。请重试。
#XFLD: Authorise service title
authServiceLabel=授权服务
#XMSG: Redirecting message
redirectingText=正在重定向以验证身份。
#XMSG: Redirection success message
redirectSuccess=你已成功授权我们运行你计划的周期性任务。
#XMSG: Start time label
startTimeFormatLabel=开始时间（{0} 小时制）
#XMSG: Start date and end date range validation
dateRangeValidationText=开始日期必须早于结束日期。
#XMSG: Delete schedule error
errorDeleteSchedule=删除计划时出错\n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=小时
#XFLD: Plural Recurrence text for Day
dayPluraltext=天
#XFLD: Plural Recurrence text for Month
monthPluralText=个月
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=小时
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=天
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=个月
#XFLD: Recurrence text for Hour
hourText=按小时
#XFLD: Recurrence text for Day
daytext=按天
#XFLD: Recurrence text for Month
monthText=按月
#XMSG: Start date cannot be in the past
startDateValidationText=开始日期不能在过去
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=输入 1 至 23 之间的值。
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=输入 1 至 31 之间的值。
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=输入 1 至 12 之间的值。
#XMSG: Invalid date text
invalidDateText=无效日期
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=重复周期
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=开始日期
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=结束日期
#XMSG: Schedule popover end date label
naText=不可用
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=时区
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=所有者
#XMSG: text for not available
naText=不适用
#XMSG: String Every
everyString=每
#XMSG: on label
onLabel=日期
#XMSG: on label
onLabelMonth=号
#XFLD: Label for Sunday
sunLabel=周日
#XFLD: Label for Monday
monLabel=周一
#XFLD: Label for Tuesday
tueLabel=周二
#XFLD: Label for Wednesday
wedLabel=周三
#XFLD: Label for Thursday
thuLabel=周四
#XFLD: Label for Friday
friLabel=周五
#XFLD: Label for Saturday
satLabel=周六
#XFLD: Recurrence text for Week
weekText=周
#XFLD: Create Schedule Dialog Title
createDialogTitle=创建计划
#XFLD: Edit Dialog title
editDialogTitle=编辑计划
#XFLD: Selected Object Title
selectedObjectText=已选
#XMSG: Table Text
tableText=表
#XMSG: View Text
viewText=视图
#XMSG: Data flow text
dataFlowText=数据流
#XMSG: Select a day of week error message
weekRecErrorText=至少选择周内的一天
#XFLD: Label for Owner
ownerLabel=所有者
#XFLD: Button label for takeover schedule
takeoverLabel=更改所有者
#XFLD: Created By label
createdbyLabel=创建者
#XFLD: Changed By label
changedbyLabel=上次修改者
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=在 SAP Datasphere 中运行计划的任务需要用户身份验证。\n通过选择运行该任务，该计划的任务将会以你为所有者运行。\n在计划的任务到期运行之前，确保你已同意代表你运行任务。
#XMSG: Schedule takeover success message
ownerChangeSuccessText=计划接管成功。
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=计划接管失败。
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=由于更新计划中存在错误，计划接管失败。
#XFLD: Text for confirm
confirm=确认
#XMSG: Create schedule error message
createScheduleError=计划创建失败。
#XMSG: Schedule update failed message
updateScheduleError=计划更新失败。
#XMSG: Schedule delete error message
deleteScheduleError=计划删除失败。
#XFLD: Label for Frequency
frequencyLabel=频率
#XFLD: Label for Frequency
frequencyLabelNew=设置
#XFLD: Label for repeat
repeatLabel=重复
#XFLD: Label for repeat
repeatLabelNeww=频率：
#XFLD: Labe for text at
atLabel=时间：
#XFLD: Label for starting
startingLabel=开始
#XFLD: Label for Start
startLabel=开始
#XFLD: Label for Ending
validityLabel=有效期
#XFLD: Label for end
endLabel=结束
#XMSG: Message for no end date
noEndDateMsg=将无限期重复执行。
#XFLD: Assign label
assignLabel=分配给我
#XFLD: Overview label
overviewLabel=概览
#XFLD: Next runs field
nextRunsLabel=后续运行：
#XFLD: Next runs field
nextRunsLabelForPopOver=后续运行
#XFLD: Expired field
taskPopoverExpiredLabel=已过期
#XFLD: Expired Message
taskPopoverExpiredMessage=计划已过期。要延长，请编辑计划并更改结束日期。
#XFLD: label for Day-of-month
dayOfmonthLabel=天
#XFLD: label for Minute
minuteLabel=分钟
#XFLD: label for hour
hourLabel=小时
#XFLD: Last Day of month selection
lastDayText=月的最后一天
#XFLD: Hourly unit description label
hourUnitLabel=小时（天）
#XFLD: Daily recurrence unit description label
dayUnitLabel=每月天数
#XFLD: Monthly receurrence unit description label
monthUnitLabel=一年中的月数
#XFLD: label for off
offLabel=禁用
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=从 {1} {2} 分钟开始，每 {0} 小时执行一次。
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=从 {1} {2} 开始，每 {0} 天执行一次。
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=从 {1} 开始，每 {0} 个月在第 {2} 天的 {3} 执行一次。
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=在每周 {0} 的 {1} 执行一次。
#XMSG: Schedule expired message
scheduleExpiredLabel=计划已过期。
#XFLD: Create Schedule Dialog Title
createScheduleTitle=创建 "{0}" 的计划
#XFLD: Edit Dialog title
createScheduleTitleNew={0}：创建计划
#XFLD: Edit Dialog title
editScheduleTitle=编辑 "{0}" 的计划
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}：编辑计划
#XFLD: Edit Schedule Button
editSchedule=编辑计划
#XFLD: Full Label for Sunday
sunFullLabel=星期日
#XFLD: Full Label for Monday
monFullLabel=星期一
#XFLD: Full Label for Tuesday
tueFullLabel=星期二
#XFLD: Full Label for Wednesday
wedFullLabel=星期三
#XFLD: Full Label for Thursday
thuFullLabel=星期四
#XFLD: Full Label for Friday
friFullLabel=星期五
#XFLD: Full Label for Saturday
satFullLabel=星期六
#XFLD: Weekly text
weeklyText=按周
#XFLD: Set start day text
startEndDateText=设置开始和结束日期
#XMSG: Schedule runs indefinitely message
validityOffText=计划将无限期运行。
#XFLD: Summary label
summaryLabel=摘要
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=每隔 {0} 小时运行一次。运行周期在每天 0:00（协调世界时）重置。
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=每隔 {0} 天运行一次，运行时间为 {1}（协调世界时）。运行周期在每个月的第一天重置。
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=每隔 {0} 个月运行一次，运行时间为{1}的 {2}（协调世界时）。运行周期在每年的第一天重置。
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=每周运行一次，运行时间为 {0} 的 {1}（协调世界时）。
#XFLD: Future runs label with time zone info
futureRunsLabel=将来的运行
#XFLD: Start & End Date label
startEndDateLabel=设置开始和结束日期
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=第一次运行可能不会执行，因为运行即将在 10 分钟内开始。
#XFLD: Label for text at UTC
atUTCLabel=时间（协调世界时）
#XMSG: Authorize expired text
authExpiredText=运行周期性任务的授权已过期。如果你想继续计划执行任务，则需要续订授权，以允许 SAP 代替你运行计划。
#XMSG: Authorization expiring soon text
authExpiringSoonText=运行周期性任务的授权即将过期。请续订授权，以允许 SAP 代替你继续运行计划。
#XMSG: Enter as Label text
enterAsText=输入模式
#XMSG: Enter as Label text
enterAsTextNew=设置类型
#XMSG: standard schedule label text
simpleScheduleText=简单计划
#XMSG: Cron expression label text
cronScheduleText=Cron 表达式
#XMSG: Time Range label text
timeRangeLabelText=时间范围
#XMSG: Time Range label text
timeRangeLabelTextNew=日期范围
#XMSG: Ownership label
ownershipLabelText=所有权
#XMSG: Show runs in the selected timezone label
showRunsInText=采用此时区显示运行
#XMSG: Text for schedule pause
pauseScheduleLabel=暂停计划：
#XMSG: Show Warning for schedule pause
schedulePausedWarning=此计划已暂停。没有更多运行。
#XMSG: Cycle run text for hourly
hourlyCycleResetText=运行周期在每天的 00:00 重置。
#XMSG: Cycle run text for Daily
dailyCycleResetText=运行周期在每个月的第一天重置。
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=运行周期在每年的 1 月 1 日重置。
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=运行周期在每周的周日重置。
#XMSG: Day label
dayLabel=日（月）
#XMSG: Monthly label
monthlyLabel=月
#XMSG: Weekly label
weeklyLabel=日（周）
#XMSG: Time in UTC label
utcTimeLabel=当前时间（协调世界时）

#XMSG: January
monthLabel1=一月
#XMSG: February
monthLabel2=二月
#XMSG: March
monthLabel3=三月
#XMSG: April
monthLabel4=四月
#XMSG: May
monthLabel5=五月
#XMSG: June
monthLabel6=六月
#XMSG: July
monthLabel7=七月
#XMSG: August
monthLabel8=八月
#XMSG: September
monthLabel9=九月
#XMSG: October
monthLabel10=十月
#XMSG: November
monthLabel11=十一月
#XMSG: December
monthLabel12=十二月

weekdayLabel0=星期日
weekdayLabel1=星期一
weekdayLabel2=星期二
weekdayLabel3=星期三
weekdayLabel4=星期四
weekdayLabel5=星期五
weekdayLabel6=星期六

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1={1} 到 {2} 期间，每 {0} 分钟运行一次。
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=每 {0} 分钟运行一次。
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=从 {1} 开始，每 {0} 分钟运行一次。
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=第 {0} 分钟运行。

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1={1} 到 {2} 期间，每过 {0} 小时运行一次
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=每过 {0} 小时运行一次
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=从 {1} 开始，每过 {0} 小时运行一次
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=过 {0} 小时运行一次

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1={1} 到 {2} 期间，每个月第 {0} 天
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=每个月第 {0} 天
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=从 {1} 开始，每个月第 {0} 天
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=第 {0} 天

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1={1} 到 {2} 期间，每 {0} 个月运行一次
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=每 {0} 个月运行一次
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=从 {1} 开始，每 {0} 个月运行一次
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=在{0}运行

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1={1} 到 {2} 期间，每周第 {0} 天
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=每周第 {0} 天
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=从 {1} 开始，每周第 {0} 天
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=在 {0}
#XMSG: And text
andText=与
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=输入受支持的值或值的组合：
#XMSG: default information about cron string
defaultCronStringInfoText=Cron 表达式的受支持值：<strong>数字 * , / </strong>
#XMSG: Parameterized info about cron string
cronStringinfoText=选定字段的受支持值：<strong>{0}</strong>
#XMSG: Show details text
showDetailsText=了解更多
#XMSG: Hide details text
hideDetailsText=隐藏详细信息
#XMSG: Label for starting at UTC Text
startingText=开始时间（协调世界时）
#XMSG: Copy to clipboard label
copyToClipboardText=复制到剪贴板
#XMSG: cron copied to clipboard
cronCopiedText=Cron 表达式已复制到剪贴板
#XMSG: error message for invalid cron copy
invalidCronCopyText=无法复制无效的 Cron 表达式
#XMSG: No preview label
noPreviewText=由于存在错误或缺少值，我们无法显示预览。请检查计划。
#XMSG: Label for create button
createText=创建
#XMSG: Label for Save button
saveText=保存
#XMSG: Label for text leave
leaveText=离开
#XMSG: warn message when user click cancel button
cancelWarnText=离开时，你的更改将会丢失。
#XMSG: Expired schedule warning
expiredTaskWarnText=此计划已过期。请选择新的结束日期，以继续复制。
#XMSG: Expired schedule message
expiredScheduleMessageText=此计划已过期。请选择新的结束日期，以继续运行周期性任务。
#XMSG: Expired schedule message
expiredTaskWarnTextNew=这个计划已过期。要延长，请更改结束日期。
#XMSG: Expired schedule Text
expiredScheduleText=已过期
#XMSG: No future runs generated
noFutureRunsText=没有可用的下次运行。
#XMSG: Text for Local time
localTimeText=本地时间
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=选定的日期范围内没有运行。
#XMSG: Warning text for expired end date
endDateExpiredText=结束日期在过去。
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=指定开始日期。
#XMSG: UTC info label
utcInfoLabelText=采用协调世界时 (UTC) 创建计划。当前 UTC 时间为 {0}。
#XMSG: UTC info label
utcInfoLabelTextNew=协调世界时 (UTC)。当前 UTC 时间为 {0}。
#XMSG: Invalid cron paste error message
invalidCronPasteText=Cron 表达式无效，因此无法粘贴。请使用有效的表达式重试。
#XMSG: Weekly schedule error text
invalidWeeklyRecText=请至少选择一天。
#XFLD: No End date text
noEndDateText=无结束日期
#XFLD: Help Button
openHelpButtonText=显示帮助
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=日期超过给定月份的最大天数
#XMSG: Redundancy error in cron string
cronRedundantErrorText=此模式具有冗余表达式。请从逗号分隔列表移除模式：* 或 /。
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=输入 0 至 59 之间的值。
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=输入大于 10 的值。
#XMSG: Error message for missing start time
startTimeErrorText=选择时间。
#XMSG: Error message handling for error code
invalidScheduleBodyError=请求的正文包含无效的数据。
#XMSG: Error message handling for error code
scheduleNotFoundError=无法找到计划。
#XMSG: Error message handling for error code
actionForbiddenError=你没有所需的权限。
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=你尝试创建的计划已经存在。
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=每天重置
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=每月重置
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=每年重置
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=每周重置
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=每小时重置
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=每天重置：
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=每月重置：
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=每年重置：
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=每周重置：
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=每小时重置：
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=在每 {1} 天的 {0}
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=授权我们运行任务链和你计划的周期性任务。
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=计划运行将加载快照。因此，复制模式将从实时复制切换为批量复制，且数据将不再实时更新。
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=是否确定要删除选定计划？
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn={1} 个选定对象中有 {0} 个已制定计划。\n是否确定要删除计划？

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=是否要获得选定对象的计划所有权？
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn={1} 个选定对象中有 {0} 个已制定计划。是否要获得这些计划的所有权？

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=是否要暂停选定对象的计划？
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn={1} 个选定对象中有 {0} 个正在运行计划。是否要暂停这些计划？

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=是否要恢复选定对象的计划？
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn={1} 个选定对象中有 {0} 个已暂停计划。是否要恢复这些计划？

#XTXT: Warning text
warningText=警告
#XTXT: Text for schedule pause
pauseScheduleText=暂停计划
#XTXT: Text for resume schedule
resumeScheduleText=恢复计划
#XTXT: Text for assigning schedule
assignScheduleText=将计划分配给我

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=运行周期性任务的授权将于 {0} 到期。要继续运行你计划的任务，需要再次征得你的同意。

#XMSG: Reauthorize text
reauthorize=重新授权

#XTXT: Duration text
duration=持续时间
#XTXT: Time frame label text
timeFrame=时间范围
#XTXT: Hours Label text
hours=小时
#XTXT: Minutes Label text
minutes=分钟
#XTXT: Minutes Label text
minutesNew=分钟

#XTXT: Minute Recurrence type text
byMinute=分钟
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=输入介于 0 到 59 之间的值，或者将频率设置为大于或等于 {0} 分钟
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=输入 0 到 59 之间的值。
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1={0} 分钟的持续时间比 {1} 分钟的计划频率要长。
#XTXT: Selected time zone At
timeZoneAt=时间：
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=开始时间：
