#XMSG: Message to ask user to authorize
authorizeWarn=Pilnvarojiet mūs izpildīt regulāros uzdevu<PERSON>, ko esat ieplānojis.
#XFLD: Authorize field value
authorise=Pilnvarot
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=Ieplānot "{0}"
#XMSG: Ok message
ok=Labi
#XMSG: Schedule
schedule=Ieplānot
#XMSG: Cancel message
cancel=Atcelt
#XMSG: Close message
close=Aizvērt
#XFLD: lable to set recurrence
recurrenceLabel=Atkārtošanās
#XFLD: label to set the recurrence value
everyLabel=Katru
#XFLD: Start date field
startDateLabel=Sākuma datums
#XFLD: Start date field
endDateLabel=Beigu datums
#XFLD: TimeZone field
timeZoneLabel=Laika josla
#XMSG: validation text
recurrenceValidationText1=Ievadiet vesela skaitļa vērtību 1 vai lielāku
#XMSG: validation text
recurrenceValidationText2=Ievadiet sākuma datumu
#XMSG: validation text
recurrenceValidationText3=Ievadiet sākuma datumu
#XMSG: validation text
noObjectSelectedText=Atlasiet attālo tabulu, ko ieplānot.
#XMSG: error text
errorLabel=Kļūda
#XMSG: Schedule created alert message
createScheduleSuccess=Grafiks ir izveidots
#XMSG: Error in creating schedule
errorCreateSchedule=Šobrīd nevar izveidot grafiku. Lūdzu, mēģiniet vēlreiz. \n {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=Grafiks ir atjaunināts
#XMSG: Error in updating schedule
errorUpdateSchedule=Šobrīd nevar atjaunināt grafiku. Lūdzu, mēģiniet vēlreiz. \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=Vai tiešām vēlaties izdzēst šo grafiku?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Grafiks ir izdzēsts.
#XMSG: Schedule deletion error message
errorSeleteSchedule=Šobrīd nevar izdzēst grafiku. Lūdzu, mēģiniet vēlreiz.
#XFLD: Authorise service title
authServiceLabel=Pilnvarot pakalpojumu
#XMSG: Redirecting message
redirectingText=Pārvirzīšana autentifikācijai.
#XMSG: Redirection success message
redirectSuccess=Esat sekmīgi pilnvarojis mūs izpildīt regulārus uzdevumus, ko esat ieplānojis.
#XMSG: Start time label
startTimeFormatLabel=Sākuma laiks ({0} stundu formāts)
#XMSG: Start date and end date range validation
dateRangeValidationText=Sākuma laikam ir jābūt agrākam par beigu laiku.
#XMSG: Delete schedule error
errorDeleteSchedule=Dzēšot grafiku, radās kļūda \n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=Stunda(s)
#XFLD: Plural Recurrence text for Day
dayPluraltext=Diena(s)
#XFLD: Plural Recurrence text for Month
monthPluralText=Mēnesis(ši)
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=Stundas
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=Dienas
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=Mēneši
#XFLD: Recurrence text for Hour
hourText=Ik stundu
#XFLD: Recurrence text for Day
daytext=Ik dienu
#XFLD: Recurrence text for Month
monthText=Ik mēnesi
#XMSG: Start date cannot be in the past
startDateValidationText=Sākuma datums nevar būt pagātnē
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=Ievadiet vērtību no 1 līdz 23.
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=Ievadiet vērtību no 1 līdz 31.
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=Ievadiet vērtību no 1 līdz 12.
#XMSG: Invalid date text
invalidDateText=Nederīgs datums
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=Atkārtošanās
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=Sākuma datums
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=Beigu datums
#XMSG: Schedule popover end date label
naText=NA
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=Laika josla
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=Īpašnieks
#XMSG: text for not available
naText=NA
#XMSG: String Every
everyString=Katru
#XMSG: on label
onLabel=Kad:
#XMSG: on label
onLabelMonth=Mēneša dienā
#XFLD: Label for Sunday
sunLabel=Svētdiena
#XFLD: Label for Monday
monLabel=Pirmdiena
#XFLD: Label for Tuesday
tueLabel=Otrdiena
#XFLD: Label for Wednesday
wedLabel=Trešdiena
#XFLD: Label for Thursday
thuLabel=Ceturtdiena
#XFLD: Label for Friday
friLabel=Piektdiena
#XFLD: Label for Saturday
satLabel=Sestdiena
#XFLD: Recurrence text for Week
weekText=Nedēļa
#XFLD: Create Schedule Dialog Title
createDialogTitle=Izveidot grafiku
#XFLD: Edit Dialog title
editDialogTitle=Rediģēt grafiku
#XFLD: Selected Object Title
selectedObjectText=Atlasīts
#XMSG: Table Text
tableText=Tabula
#XMSG: View Text
viewText=Skats
#XMSG: Data flow text
dataFlowText=Datu plūsma
#XMSG: Select a day of week error message
weekRecErrorText=Atlasiet vismaz vienu nedēļas dienu
#XFLD: Label for Owner
ownerLabel=Īpašnieks
#XFLD: Button label for takeover schedule
takeoverLabel=Mainīt īpašnieku
#XFLD: Created By label
createdbyLabel=Izveidoja
#XFLD: Changed By label
changedbyLabel=Pēdējo reizi modificēja
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=Lai izpildītu ieplānotos uzdevumus risinājumā SAP Datasphere, ir nepieciešama lietotāja autentifikācija.\n Izvēloties izpildīt šo uzdevumu, šis ieplānotais uzdevums tiks izpildīts ar jums kā īpašnieku.\n Pirms pienāk ieplānotā uzdevuma izpildes laiks, pārliecinieties, vai esat devuši piekrišanu izpildīt uzdevumus jūsu vārdā.
#XMSG: Schedule takeover success message
ownerChangeSuccessText=Grafika pārņemšana bija sekmīga.
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=Grafika pārņemšana neizdevās.
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=Grafika pārņemšana neizdevās, jo radās kļūda grafika atjaunināšanā.
#XFLD: Text for confirm
confirm=Apstiprināt
#XMSG: Create schedule error message
createScheduleError=Grafika izveide neizdevās.
#XMSG: Schedule update failed message
updateScheduleError=Grafika atjaunināšana neizdevās.
#XMSG: Schedule delete error message
deleteScheduleError=Grafika dzēšana neizdevās.
#XFLD: Label for Frequency
frequencyLabel=Biežums
#XFLD: Label for Frequency
frequencyLabelNew=Iestatījumi
#XFLD: Label for repeat
repeatLabel=Atkārtošana
#XFLD: Label for repeat
repeatLabelNeww=Biežums:
#XFLD: Labe for text at
atLabel=Plkst.
#XFLD: Label for starting
startingLabel=Sākšana
#XFLD: Label for Start
startLabel=Sākums
#XFLD: Label for Ending
validityLabel=Derīgums
#XFLD: Label for end
endLabel=Beigas
#XMSG: Message for no end date
noEndDateMsg=Atkārtošanās notiks bezgalīgi.
#XFLD: Assign label
assignLabel=Piešķirt man
#XFLD: Overview label
overviewLabel=Apskats
#XFLD: Next runs field
nextRunsLabel=Nākamās izpildes:
#XFLD: Next runs field
nextRunsLabelForPopOver=Nākamās izpildes
#XFLD: Expired field
taskPopoverExpiredLabel=Beidzies derīgums
#XFLD: Expired Message
taskPopoverExpiredMessage=Grafikam ir beidzies derīgums. Lai to pagarinātu, rediģējiet grafiku un mainiet beigu datumu.
#XFLD: label for Day-of-month
dayOfmonthLabel=Mēneša diena
#XFLD: label for Minute
minuteLabel=Minūte
#XFLD: label for hour
hourLabel=Stunda
#XFLD: Last Day of month selection
lastDayText=Mēneša pēdējā diena
#XFLD: Hourly unit description label
hourUnitLabel=Dienas pēdējā stunda
#XFLD: Daily recurrence unit description label
dayUnitLabel=Diena(as) mēnesī
#XFLD: Monthly receurrence unit description label
monthUnitLabel=Gada mēneši
#XFLD: label for off
offLabel=Izslēgt
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=Notiek ik pēc {0} stundas(ām), sākot no {1} {2} minūtēs.
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=Notiek ik pēc {0} dienas(ām), sākot no {1} plkst. {2}.
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=Notiek ik pēc {0} mēneša(iem), sākot no {1} {2} plkst. {3}.
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=Notiek katru nedēļu {0} plkst. {1}.
#XMSG: Schedule expired message
scheduleExpiredLabel=Grafika derīgums ir beidzies.
#XFLD: Create Schedule Dialog Title
createScheduleTitle=Izveidot grafiku šim: “{0}”
#XFLD: Edit Dialog title
createScheduleTitleNew={0}: Izveidot grafiku
#XFLD: Edit Dialog title
editScheduleTitle=Rediģēt grafiku šim: “{0}”
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}: Rediģēt grafiku
#XFLD: Edit Schedule Button
editSchedule=Rediģēt grafiku
#XFLD: Full Label for Sunday
sunFullLabel=Svētdiena
#XFLD: Full Label for Monday
monFullLabel=Pirmdiena
#XFLD: Full Label for Tuesday
tueFullLabel=Otrdiena
#XFLD: Full Label for Wednesday
wedFullLabel=Trešdiena
#XFLD: Full Label for Thursday
thuFullLabel=Ceturtdiena
#XFLD: Full Label for Friday
friFullLabel=Piektdiena
#XFLD: Full Label for Saturday
satFullLabel=Sestdiena
#XFLD: Weekly text
weeklyText=Katru nedēļu
#XFLD: Set start day text
startEndDateText=Iestatiet sākuma un beigu datumu
#XMSG: Schedule runs indefinitely message
validityOffText=Grafiks tiks izpildīts bezgalīgi.
#XFLD: Summary label
summaryLabel=Kopsavilkums
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=Tiek izpildīts ik pēc {0} stundas(ām). Izpildes cikls tiek atiestatīts katru dienu plkst. 0:00 (UTC).
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=Tiek izpildīts ik pēc {0} dienas(ām) plkst. {1} (UTC). Izpildes cikls tiek atiestatīts katra mēneša pirmajā dienā.
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=Tiek izpildīts ik pēc {0} mēneša(iem) {1} plkst. {2} (UTC). Izpildes cikls tiek atiestatīts katra gada pirmajā dienā.
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=Tiek izpildīts katru nedēļu {0} plkst. {1} (UTC).
#XFLD: Future runs label with time zone info
futureRunsLabel=Izpildes nākotnē
#XFLD: Start & End Date label
startEndDateLabel=Iestatiet sākuma un beigu datumu
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=Pirmo izpildi, iespējams, nevarēs veikt, jo tā sāksies pēc mazāk nekā 10 minūtēm.
#XFLD: Label for text at UTC
atUTCLabel=Plkst. (UTC)
#XMSG: Authorize expired text
authExpiredText=Jūsu regulāro uzdevumu izpildes autorizācijas derīgums ir beidzies. Ja vēlaties turpināt ieplānotos uzdevumus, jums ir jāatjauno autorizācija, lai ļautu SAP izpildīt grafiku jūsu vārdā.
#XMSG: Authorization expiring soon text
authExpiringSoonText=Jūsu regulāro uzdevumu izpildes autorizācija drīz beigsies. Atjaunojiet autorizāciju, lai ļautu SAP turpināt grafika izpildi jūsu vārdā.
#XMSG: Enter as Label text
enterAsText=Ievadīt kā
#XMSG: Enter as Label text
enterAsTextNew=Iestatījuma tips
#XMSG: standard schedule label text
simpleScheduleText=Vienkāršs grafiks
#XMSG: Cron expression label text
cronScheduleText=Cron izteiksme
#XMSG: Time Range label text
timeRangeLabelText=Laika diapazons
#XMSG: Time Range label text
timeRangeLabelTextNew=Datumu diapazons
#XMSG: Ownership label
ownershipLabelText=Īpašums
#XMSG: Show runs in the selected timezone label
showRunsInText=Rādīt izpildes laikā
#XMSG: Text for schedule pause
pauseScheduleLabel=Aizturēt grafiku:
#XMSG: Show Warning for schedule pause
schedulePausedWarning=Šis grafiks ir aizturēts. Nākotnes izpildes nav iespējamas.
#XMSG: Cycle run text for hourly
hourlyCycleResetText=Izpildes cikls tiek atiestatīts katru dienu plkst. 00:00.
#XMSG: Cycle run text for Daily
dailyCycleResetText=Izpildes cikls tiek atiestatīts katra mēneša pirmajā dienā.
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=Izpildes cikls tiek atiestatīts katru gadu 1. janvārī.
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=Izpildes cikls tiek atiestatīts katru nedēļu svētdienā.
#XMSG: Day label
dayLabel=Diena (mēnesis)
#XMSG: Monthly label
monthlyLabel=Mēnesis
#XMSG: Weekly label
weeklyLabel=Diena (nedēļa)
#XMSG: Time in UTC label
utcTimeLabel=Pašreizējais laiks (UTC)

#XMSG: January
monthLabel1=Janvāris
#XMSG: February
monthLabel2=Februāris
#XMSG: March
monthLabel3=Marts
#XMSG: April
monthLabel4=Aprīlis
#XMSG: May
monthLabel5=Maijs
#XMSG: June
monthLabel6=Jūnijs
#XMSG: July
monthLabel7=Jūlijs
#XMSG: August
monthLabel8=Augusts
#XMSG: September
monthLabel9=Septembris
#XMSG: October
monthLabel10=Oktobris
#XMSG: November
monthLabel11=Novembris
#XMSG: December
monthLabel12=Decembris

weekdayLabel0=Svētdiena
weekdayLabel1=Pirmdiena
weekdayLabel2=Otrdiena
weekdayLabel3=Trešdiena
weekdayLabel4=Ceturtdiena
weekdayLabel5=Piektdiena
weekdayLabel6=Sestdiena

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=Katrā {0}. minūtē no {1} līdz {2}.
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=Katrā {0}. minūtē.
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=Katrā {0}, minūtē, sākot no {1}.
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4={0}. minūtē.

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=ik pēc {0} stundas no {1} līdz {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=ik pēc {0} stundas
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=ik pēc {0} stundas, sākot no {1}
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=ik pēc {0} stundas

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=katrā {0}. mēneša dienā no {1} līdz {2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=katrā {0}. mēneša dienā
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=katrā {0}. mēneša dienā, sākot no {1}
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4={0}. mēneša dienā

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=katrā {0}. mēnesī no {1} līdz {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=katrā {0}. mēnesī
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=katrā {0}. mēnesī, sākot no {1}
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=kad: {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=katrā {0}. nedēļas dienā no {1} līdz {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=katrā {0}. nedēļas dienā
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=katrā {0}. nedēļas dienā, sākot no {1}
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=kad: {0}
#XMSG: And text
andText=un
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=Ievadiet atbalstīto vērtību vai vērtību kombināciju:
#XMSG: default information about cron string
defaultCronStringInfoText=Cron izteiksmes atvalstītās vērtības: <strong>cipari * , / </strong>
#XMSG: Parameterized info about cron string
cronStringinfoText=Atlasītā lauka atbalstītās vērtības: <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=Plašāka informācija
#XMSG: Hide details text
hideDetailsText=Paslēpt detalizēto informāciju
#XMSG: Label for starting at UTC Text
startingText=Sākot no (UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=Kopēt uz starpliktuvi
#XMSG: cron copied to clipboard
cronCopiedText=Cron izteiksme ir nokopēta uz starpliktuvi
#XMSG: error message for invalid cron copy
invalidCronCopyText=Nevar nokopēt nederīgu Cron izteiksmi
#XMSG: No preview label
noPreviewText=Mēs nevaram parādīt priekšskatījumu, jo ir kļūdas vai tūkst vērtību. Lūdzu, pārskatiet grafiku.
#XMSG: Label for create button
createText=Izveidot
#XMSG: Label for Save button
saveText=Saglabāt
#XMSG: Label for text leave
leaveText=Pamest
#XMSG: warn message when user click cancel button
cancelWarnText=Ja pametīsit, jūsu izmaiņas tiks zaudētas.
#XMSG: Expired schedule warning
expiredTaskWarnText=Šī grafika derīgums ir beidzies. Lūdzu, atlasiet jaunu beigu datumu, lai turpinātu replicēšanu.
#XMSG: Expired schedule message
expiredScheduleMessageText=Šī grafika derīgums ir beidzies. Lūdzu, atlasiet jaunu beigu datumu, lai turpinātu izpildīt regulāros uzdevumus.
#XMSG: Expired schedule message
expiredTaskWarnTextNew=Šim grafikam ir beidzies derīgums. Lai to pagarinātu, mainiet beigu datumu.
#XMSG: Expired schedule Text
expiredScheduleText=Beidzies derīgums
#XMSG: No future runs generated
noFutureRunsText=Nākamā izpilde nav pieejama.
#XMSG: Text for Local time
localTimeText=Vietējais laiks
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=Atlasītajā datumu diapazonā nav nevienas izpildes.
#XMSG: Warning text for expired end date
endDateExpiredText=Beigu datums ir pagātnē.
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=Norādiet sākuma datumu.
#XMSG: UTC info label
utcInfoLabelText=Grafiki ir izveidoti koordinētajā universālajā laikā (UTC). Pašlaik UTC laiks ir {0}.
#XMSG: UTC info label
utcInfoLabelTextNew=Koordinētais universālais laiks (UTC). Pašreizējais UTC laiks ir {0}.
#XMSG: Invalid cron paste error message
invalidCronPasteText=Mēs nevaram ielīmēt Cron izteiksmi, jo tā ir nederīga . Lūdzu, mēģiniet vēlreiz ar derīgu izteiksmi.
#XMSG: Weekly schedule error text
invalidWeeklyRecText=Lūdzu, atlasiet vismaz vienu dienu.
#XFLD: No End date text
noEndDateText=Nav beigu datuma
#XFLD: Help Button
openHelpButtonText=Rādīt palīdzību
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=Datums pārsniedz maksimālo dienu skaitu dotajam mēnesim
#XMSG: Redundancy error in cron string
cronRedundantErrorText=Šim modelim ir liekas izteiksmes. Lūdzu, noņemiet modeļus: * vai / no komatatdalītā saraksta.
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=Ievadiet vērtību no 0 līdz 59.
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=Ievadiet vērtību virs 10.
#XMSG: Error message for missing start time
startTimeErrorText=Atlasiet laiku.
#XMSG: Error message handling for error code
invalidScheduleBodyError=Pieprasījuma pamattekstā ir nederīgi dati.
#XMSG: Error message handling for error code
scheduleNotFoundError=Šo grafiku nevar atrast.
#XMSG: Error message handling for error code
actionForbiddenError=Jums nav nepieciešamās atļaujas.
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=Jūs mēģināt izveidot grafiku, kas jau pastāv.
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=Atiestatīt katru dienu
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=Atiestatīt katru mēnesi
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=Atiestatīt katru gadu
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=Atiestatīt katru nedēļu
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=Atiestatīt katru stundu
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=Atiestatīt katru dienu:
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=Atiestatīt katru mēnesi:
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=Atiestatīt katru gadu:
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=Atiestatīt katru nedēļu:
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=Atiestatīt katru stundu:
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText={0} katrā {1}. dienā
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=Pilnvarojiet mūs izpildīt uzdevumu ķēdes, kā arī regulāros uzdevumus, ko esat ieplānojis.
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=Grafika izpilde ielādēs momentuzņēmumu. Replicēšanas režīms tādējādi pārslēgsies no reāllaika replicēšanas uz partiju replicēšanu, un dati vairs netiks atjaunināti reāllaikā.
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=Vai tiešām vēlaties izdzēst atlasītos grafikus?
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn={0} no {1} atlasītajiem objektiem ir grafiki. \nVai tiešām vēlaties izdzēst šos grafikus?

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=Vai vēlaties kļūt par atlasīto objektu grafiku īpašnieku?
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=Grafiki ir definēti {0} no {1} atlasītajiem objektiem. Vai vēlaties kļūt par šo grafiku īpašnieku?

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=Vai vēlaties aizturēt atlasīto objektu grafikus?
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn={0} no {1} atlasītajiem objektiem grafiki ir izpildē. Vai vēlaties tos aizturēt?

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=Vai vēlaties turpināt atlasīto objektu grafikus?
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn={0} no {1} atlasītajiem objektiem grafiki ir aizturēti. Vai vēlaties tos turpināt?

#XTXT: Warning text
warningText=Brīdinājums
#XTXT: Text for schedule pause
pauseScheduleText=Aizturēt grafiku
#XTXT: Text for resume schedule
resumeScheduleText=Turpināt grafiku
#XTXT: Text for assigning schedule
assignScheduleText=Piešķirt grafiku man

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=Autorizācija izpildīt jūsu atkārtojošos uzdevumus beigsies {0}. Lai turpinātu ieplānoto uzdevumu izpildīšanu, ir vēlreiz nepieciešama piekrišana.

#XMSG: Reauthorize text
reauthorize=Vēlreiz autorizēt

#XTXT: Duration text
duration=Ilgums
#XTXT: Time frame label text
timeFrame=Laika posms
#XTXT: Hours Label text
hours=Stunda(as)
#XTXT: Minutes Label text
minutes=Minūte(s)
#XTXT: Minutes Label text
minutesNew=Minūtes

#XTXT: Minute Recurrence type text
byMinute=Minūtes
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=Ievadiet vērtību diapazonā no 0 līdz 59 vai iestatiet biežumu kā ilgāku par vai vienādu ar {0}minūtēm.
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=Ievadiet vērtību no 0 līdz 59.
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=Ilgums {0} minūtes ir ilgāks par ieplānoto biežumu {1} minūtes.
#XTXT: Selected time zone At
timeZoneAt=Plkst.
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=Sākuma laiks
