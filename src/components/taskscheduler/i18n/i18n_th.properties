#XMSG: Message to ask user to authorize
authorizeWarn=ให้สิทธิแก่เราในการดำเนินการงานที่เกิดขึ้นประจำที่คุณจัดกำหนดการไว้
#XFLD: Authorize field value
authorise=ให้สิทธิ
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=กำหนดการสำหรับ "{0}"
#XMSG: Ok message
ok=ตกลง
#XMSG: Schedule
schedule=จัดกำหนดการ
#XMSG: Cancel message
cancel=ยกเลิก
#XMSG: Close message
close=ปิด
#XFLD: lable to set recurrence
recurrenceLabel=การเกิดซ้ำ
#XFLD: label to set the recurrence value
everyLabel=ทุก
#XFLD: Start date field
startDateLabel=วันที่เริ่มต้น
#XFLD: Start date field
endDateLabel=วันที่สิ้นสุด
#XFLD: TimeZone field
timeZoneLabel=เขตเวลา
#XMSG: validation text
recurrenceValidationText1=ป้อนค่าจำนวนเต็ม 1 ขึ้นไป
#XMSG: validation text
recurrenceValidationText2=ป้อนวันที่เริ่มต้น
#XMSG: validation text
recurrenceValidationText3=ป้อนวันที่เริ่มต้น
#XMSG: validation text
noObjectSelectedText=เลือกตารางระยะไกลที่จะจัดกำหนดการ
#XMSG: error text
errorLabel=ข้อผิดพลาด
#XMSG: Schedule created alert message
createScheduleSuccess=สร้างกำหนดการแล้ว
#XMSG: Error in creating schedule
errorCreateSchedule=ไม่สามารถสร้างกำหนดการได้ในขณะนี้ กรุณาลองอีกครั้ง \N {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=อัพเดทกำหนดการแล้ว
#XMSG: Error in updating schedule
errorUpdateSchedule=ไม่สามารถอัพเดทกำหนดการได้ในขณะนี้ กรุณาลองอีกครั้ง \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=คุณแน่ใจหรือไม่ว่าต้องการลบกำหนดการ?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=ลบกำหนดการแล้ว
#XMSG: Schedule deletion error message
errorSeleteSchedule=ไม่สามารถลบกำหนดการได้ในขณะนี้ กรุณาลองอีกครั้ง
#XFLD: Authorise service title
authServiceLabel=ให้สิทธิแก่บริการ
#XMSG: Redirecting message
redirectingText=กำลังเปลี่ยนเส้นทางเพื่อขอการรับรองความถูกต้อง
#XMSG: Redirection success message
redirectSuccess=คุณให้สิทธิแก่เราในการดำเนินการงานที่เกิดขึ้นประจำที่คุณจัดกำหนดการไว้ได้สำเร็จ
#XMSG: Start time label
startTimeFormatLabel=เวลาเริ่มต้น (รูปแบบ {0} ชั่วโมง)
#XMSG: Start date and end date range validation
dateRangeValidationText=วันที่เริ่มต้นต้องอยู่ก่อน (เร็วกว่า) วันที่สิ้นสุด
#XMSG: Delete schedule error
errorDeleteSchedule=เกิดข้อผิดพลาดขณะลบกำหนดการ \n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=ชั่วโมง
#XFLD: Plural Recurrence text for Day
dayPluraltext=วัน
#XFLD: Plural Recurrence text for Month
monthPluralText=เดือน
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=ชั่วโมง
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=วัน
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=เดือน
#XFLD: Recurrence text for Hour
hourText=รายชั่วโมง
#XFLD: Recurrence text for Day
daytext=รายวัน
#XFLD: Recurrence text for Month
monthText=รายเดือน
#XMSG: Start date cannot be in the past
startDateValidationText=วันที่เริ่มต้นต้องไม่เป็นวันที่ในอดีต
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=ป้อนค่าระหว่าง 1 ถึง 23
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=ป้อนค่าระหว่าง 1 ถึง 31
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=ป้อนค่าระหว่าง 1 ถึง 12
#XMSG: Invalid date text
invalidDateText=วันที่ไม่ถูกต้อง
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=การเกิดซ้ำ
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=วันที่เริ่มต้น
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=วันที่สิ้นสุด
#XMSG: Schedule popover end date label
naText=ไม่มีข้อมูล
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=เขตเวลา
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=เจ้าของ
#XMSG: text for not available
naText=N/A
#XMSG: String Every
everyString=ทุก
#XMSG: on label
onLabel=เปิด
#XMSG: on label
onLabelMonth=ในวันของเดือน
#XFLD: Label for Sunday
sunLabel=อา.
#XFLD: Label for Monday
monLabel=จ.
#XFLD: Label for Tuesday
tueLabel=อ.
#XFLD: Label for Wednesday
wedLabel=พ.
#XFLD: Label for Thursday
thuLabel=พฤ.
#XFLD: Label for Friday
friLabel=ศ.
#XFLD: Label for Saturday
satLabel=ส.
#XFLD: Recurrence text for Week
weekText=สัปดาห์
#XFLD: Create Schedule Dialog Title
createDialogTitle=สร้างกำหนดการ
#XFLD: Edit Dialog title
editDialogTitle=แก้ไขกำหนดการ
#XFLD: Selected Object Title
selectedObjectText=เลือกแล้ว
#XMSG: Table Text
tableText=ตาราง
#XMSG: View Text
viewText=มุมมอง
#XMSG: Data flow text
dataFlowText=ผังข้อมูล
#XMSG: Select a day of week error message
weekRecErrorText=เลือกอย่างน้อยหนึ่งวันของสัปดาห์
#XFLD: Label for Owner
ownerLabel=เจ้าของ
#XFLD: Button label for takeover schedule
takeoverLabel=เปลี่ยนเจ้าของ
#XFLD: Created By label
createdbyLabel=สร้างโดย
#XFLD: Changed By label
changedbyLabel=ปรับเปลี่ยนครั้งล่าสุดโดย
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=จำเป็นต้องมีการรับรองความถูกต้องของผู้ใช้เพื่อดำเนินการงานตามกำหนดการใน SAP Datasphere\n เมื่อเลือกที่จะดำเนินการงานนี้ งานตามกำหนดการจะถูกดำเนินการโดยคุณในฐานะเจ้าของ\n ก่อนที่งานตามกำหนดการจะถึงกำหนดดำเนินการ กรุณาตรวจสอบให้แน่ใจว่าคุณได้ยินยอมให้ดำเนินการงานในนามของคุณ
#XMSG: Schedule takeover success message
ownerChangeSuccessText=การนำกำหนดการมาใช้ทำได้สำเร็จ
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=การนำกำหนดการมาใช้ล้มเหลว
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=การนำกำหนดการมาใช้ล้มเหลวเนื่องจากมีข้อผิดพลาดในการอัพเดทกำหนดการ
#XFLD: Text for confirm
confirm=ยืนยัน
#XMSG: Create schedule error message
createScheduleError=การสร้างกำหนดการล้มเหลว
#XMSG: Schedule update failed message
updateScheduleError=การอัพเดทกำหนดการล้มเหลว
#XMSG: Schedule delete error message
deleteScheduleError=การลบกำหนดการล้มเหลว
#XFLD: Label for Frequency
frequencyLabel=ความถี่
#XFLD: Label for Frequency
frequencyLabelNew=การกำหนดค่า
#XFLD: Label for repeat
repeatLabel=ทำซ้ำ
#XFLD: Label for repeat
repeatLabelNeww=ความถี่:
#XFLD: Labe for text at
atLabel=เมื่อ
#XFLD: Label for starting
startingLabel=เริ่มต้น
#XFLD: Label for Start
startLabel=เริ่มต้น
#XFLD: Label for Ending
validityLabel=การมีผลใช้
#XFLD: Label for end
endLabel=สิ้นสุด
#XMSG: Message for no end date
noEndDateMsg=การเกิดซ้ำจะเกิดขึ้นอย่างไม่มีกำหนด
#XFLD: Assign label
assignLabel=กำหนดให้กับฉัน
#XFLD: Overview label
overviewLabel=ภาพรวม
#XFLD: Next runs field
nextRunsLabel=การดำเนินการครั้งถัดไป:
#XFLD: Next runs field
nextRunsLabelForPopOver=การดำเนินการครั้งถัดไป
#XFLD: Expired field
taskPopoverExpiredLabel=หมดอายุแล้ว
#XFLD: Expired Message
taskPopoverExpiredMessage=กำหนดการหมดอายุแล้ว หากต้องการขยายกำหนดการ ให้แก้ไขกำหนดการและเปลี่ยนแปลงวันที่สิ้นสุด
#XFLD: label for Day-of-month
dayOfmonthLabel=วันของเดือน
#XFLD: label for Minute
minuteLabel=นาที
#XFLD: label for hour
hourLabel=ชั่วโมง
#XFLD: Last Day of month selection
lastDayText=วันสุดท้ายของเดือน
#XFLD: Hourly unit description label
hourUnitLabel=ชั่วโมงของวัน
#XFLD: Daily recurrence unit description label
dayUnitLabel=วันต่อเดือน
#XFLD: Monthly receurrence unit description label
monthUnitLabel=เดือนของปี
#XFLD: label for off
offLabel=ปิด
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=เกิดขึ้นทุก {0} ชั่วโมงตั้งแต่ {1} เวลา {2} นาที
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=เกิดขึ้นทุก {0} วันตั้งแต่ {1} เวลา {2}
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=เกิดขึ้นทุก {0} เดือนตั้งแต่ {1} ในวันที่ {2} เวลา {3}
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=เกิดขึ้นทุกสัปดาห์ในวันที่ {0} เวลา {1}
#XMSG: Schedule expired message
scheduleExpiredLabel=กำหนดการหมดอายุแล้ว
#XFLD: Create Schedule Dialog Title
createScheduleTitle=สร้างกำหนดการสำหรับ "{0}"
#XFLD: Edit Dialog title
createScheduleTitleNew={0}: สร้างกำหนดการ
#XFLD: Edit Dialog title
editScheduleTitle=แก้ไขกำหนดการสำหรับ "{0}"
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}: แก้ไขกำหนดการ
#XFLD: Edit Schedule Button
editSchedule=แก้ไขกำหนดการ
#XFLD: Full Label for Sunday
sunFullLabel=อาทิตย์
#XFLD: Full Label for Monday
monFullLabel=จันทร์
#XFLD: Full Label for Tuesday
tueFullLabel=อังคาร
#XFLD: Full Label for Wednesday
wedFullLabel=พุธ
#XFLD: Full Label for Thursday
thuFullLabel=พฤหัสบดี
#XFLD: Full Label for Friday
friFullLabel=ศุกร์
#XFLD: Full Label for Saturday
satFullLabel=เสาร์
#XFLD: Weekly text
weeklyText=รายสัปดาห์
#XFLD: Set start day text
startEndDateText=กำหนดวันที่เริ่มต้นและสิ้นสุด
#XMSG: Schedule runs indefinitely message
validityOffText=กำหนดการจะดำเนินการอย่างไม่มีกำหนด
#XFLD: Summary label
summaryLabel=สรุป
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=ดำเนินการทุก {0} ชั่วโมง รอบการดำเนินการจะถูกรีเซ็ตทุกวัน เวลา 0:00 (UTC)
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=ดำเนินการทุก {0} วัน เวลา {1} (UTC) รอบการดำเนินการจะถูกรีเซ็ตในวันแรกของแต่ละเดือน
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=ดำเนินการทุก {0} เดือน วัน {1} เวลา {2} (UTC) รอบการดำเนินการจะถูกรีเซ็ตในวันแรกของแต่ละปี
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=ดำเนินการทุกสัปดาห์ในวันที่ {0} เวลา {1}
#XFLD: Future runs label with time zone info
futureRunsLabel=การดำเนินการในอนาคต
#XFLD: Start & End Date label
startEndDateLabel=กำหนดวันที่เริ่มต้นและสิ้นสุด
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=การดำเนินการครั้งแรกอาจไม่เกิดขึ้นเนื่องจากกำลังจะเริ่มในอีกไม่เกิน 10 นาที
#XFLD: Label for text at UTC
atUTCLabel=เวลา (UTC)
#XMSG: Authorize expired text
authExpiredText=สิทธิในการดำเนินการงานที่เกิดขึ้นประจำของคุณหมดอายุแล้ว หากต้องการดำเนินงานการจัดกำหนดการต่อ คุณจำเป็นต้องต่ออายุสิทธิเพื่ออนุญาตให้ SAP ดำเนินการจัดกำหนดการในนามของคุณต่อ
#XMSG: Authorization expiring soon text
authExpiringSoonText=สิทธิในการดำเนินการงานที่เกิดขึ้นประจำของคุณจะหมดอายุในไม่ช้า กรุณาต่ออายุสิทธิเพื่ออนุญาตให้ SAP ดำเนินการจัดกำหนดการในนามของคุณต่อ
#XMSG: Enter as Label text
enterAsText=ป้อนในรูปแบบ
#XMSG: Enter as Label text
enterAsTextNew=ประเภทการกำหนดค่า
#XMSG: standard schedule label text
simpleScheduleText=กำหนดการแบบง่าย
#XMSG: Cron expression label text
cronScheduleText=นิพจน์ Cron
#XMSG: Time Range label text
timeRangeLabelText=ช่วงเวลา
#XMSG: Time Range label text
timeRangeLabelTextNew=ช่วงวันที่
#XMSG: Ownership label
ownershipLabelText=ความเป็นเจ้าของ
#XMSG: Show runs in the selected timezone label
showRunsInText=แสดงการดำเนินการตาม
#XMSG: Text for schedule pause
pauseScheduleLabel=หยุดกำหนดการชั่วคราว:
#XMSG: Show Warning for schedule pause
schedulePausedWarning=กำหนดการนี้ถูกหยุดชั่วคราว ไม่มีการดำเนินการในอนาคต
#XMSG: Cycle run text for hourly
hourlyCycleResetText=รอบการดำเนินการจะถูกรีเซ็ตเวลา 00:00 ทุกวัน
#XMSG: Cycle run text for Daily
dailyCycleResetText=รอบการดำเนินการจะถูกรีเซ็ตวันแรกของแต่ละเดือน
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=รอบการดำเนินการจะถูกรีเซ็ตแต่ละปีในวันที่ 1 ม.ค.
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=รอบการดำเนินการจะถูกรีเซ็ตทุกสัปดาห์ในวันอาทิตย์
#XMSG: Day label
dayLabel=วัน (เดือน)
#XMSG: Monthly label
monthlyLabel=เดือน
#XMSG: Weekly label
weeklyLabel=วัน (สัปดาห์)
#XMSG: Time in UTC label
utcTimeLabel=เวลาปัจจุบัน (UTC)

#XMSG: January
monthLabel1=มกราคม
#XMSG: February
monthLabel2=กุมภาพันธ์
#XMSG: March
monthLabel3=มีนาคม
#XMSG: April
monthLabel4=เมษายน
#XMSG: May
monthLabel5=พฤษภาคม
#XMSG: June
monthLabel6=มิถุนายน
#XMSG: July
monthLabel7=กรกฎาคม
#XMSG: August
monthLabel8=สิงหาคม
#XMSG: September
monthLabel9=กันยายน
#XMSG: October
monthLabel10=ตุลาคม
#XMSG: November
monthLabel11=พฤศจิกายน
#XMSG: December
monthLabel12=ธันวาคม

weekdayLabel0=อาทิตย์
weekdayLabel1=จันทร์
weekdayLabel2=อังคาร
weekdayLabel3=พุธ
weekdayLabel4=พฤหัสบดี
weekdayLabel5=ศุกร์
weekdayLabel6=เสาร์

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=ทุกนาทีที่ {0} ตั้งแต่ {1} ถึง {2}
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=ทุกนาทีที่ {0}
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=ทุกนาทีที่ {0} โดยเริ่มในเวลา {1}
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=ในนาทีที่ {0}

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=เมื่อผ่านไปทุก {0} ชั่วโมง ตั้งแต่ {1} ถึง {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=เมื่อผ่านไปทุก {0} ชั่วโมง
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=เมื่อผ่านไปทุก {0} ชั่วโมงโดยเริ่มต้น {1}
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=เมื่อผ่านชั่วโมงที่ {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=ทุก {0} วันของเดือนตั้งแต่{1}ถึง{2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=ทุก {0} วันของเดือน
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=ทุก {0} วันของเดือนโดยเริ่มต้น {1}
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=ในวันของเดือนที่ {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=ทุกเดือนที่ {0} ตั้งแต่ {1} จนถึง {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=ทุกเดือนที่ {0}
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=ทุกเดือนที่ {0} โดยเริ่มต้น {1}
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=ใน {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=ทุก {0} วันของสัปดาห์ตั้งแต่{1}ถึง{2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=ทุก {0} วันของสัปดาห์
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=ทุก {0} วันของสัปดาห์โดยเริ่มต้น {1}
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=ใน {0}
#XMSG: And text
andText=และ
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=ป้อนค่าหรือการรวมค่าที่ได้รับการสนับสนุน:
#XMSG: default information about cron string
defaultCronStringInfoText=ค่าที่ได้รับการสนับสนุนสำหรับนิพจน์ Cron: <strong>ตัวเลข * , / </strong>
#XMSG: Parameterized info about cron string
cronStringinfoText=ค่าที่ได้รับการสนับสนุนสำหรับฟิลด์ที่เลือก: <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=เรียนรู้เพิ่มเติม
#XMSG: Hide details text
hideDetailsText=ซ่อนรายละเอียด
#XMSG: Label for starting at UTC Text
startingText=เริ่มต้นในเวลา (UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=คัดลอกไปยังคลิปบอร์ด
#XMSG: cron copied to clipboard
cronCopiedText=คัดลอกนิพจน์ Cron ไปยังคลิปบอร์ดแล้ว
#XMSG: error message for invalid cron copy
invalidCronCopyText=ไม่สามารถคัดลอกนิพจน์ Cron ที่ไม่ถูกต้อง
#XMSG: No preview label
noPreviewText=เราไม่สามารถแสดงตัวอย่างได้เนื่องจากมีข้อผิดพลาดหรือมีค่าที่ขาดหายไป กรุณาตรวจทานกำหนดการ
#XMSG: Label for create button
createText=สร้าง
#XMSG: Label for Save button
saveText=เก็บบันทึก
#XMSG: Label for text leave
leaveText=ออก
#XMSG: warn message when user click cancel button
cancelWarnText=การเปลี่ยนแปลงของคุณจะสูญหายเมื่อคุณออก
#XMSG: Expired schedule warning
expiredTaskWarnText=กำหนดการนี้หมดอายุแล้ว กรุณาเลือกวันที่สิ้นสุดใหม่เพื่อทำสำเนาต่อ
#XMSG: Expired schedule message
expiredScheduleMessageText=กำหนดการนี้หมดอายุแล้ว กรุณาเลือกวันที่สิ้นสุดใหม่เพื่อดำเนินการงานที่เกิดขึ้นประจำต่อ
#XMSG: Expired schedule message
expiredTaskWarnTextNew=กำหนดการนี้หมดอายุแล้ว หากต้องการขยายกำหนดการ ให้เปลี่ยนแปลงวันที่สิ้นสุด
#XMSG: Expired schedule Text
expiredScheduleText=หมดอายุแล้ว
#XMSG: No future runs generated
noFutureRunsText=ไม่มีการดำเนินการครั้งถัดไป
#XMSG: Text for Local time
localTimeText=เวลาท้องถิ่น
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=ไม่มีการดำเนินการภายในช่วงวันที่ที่เลือก
#XMSG: Warning text for expired end date
endDateExpiredText=วันที่สิ้นสุดอยู่ในอดีต
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=ระบุวันที่เริ่มต้น
#XMSG: UTC info label
utcInfoLabelText=กำหนดการจะถูกสร้างขึ้นตามพิกัดเวลามาตรฐานสากล (UTC) เวลา UTC ปัจจุบันคือ {0}
#XMSG: UTC info label
utcInfoLabelTextNew=พิกัดเวลามาตรฐานสากล (UTC) เวลา UTC ปัจจุบันคือ {0}
#XMSG: Invalid cron paste error message
invalidCronPasteText=เราไม่สามารถวางนิพจน์ cron เนื่องจากไม่ถูกต้อง กรุณลองอีกครั้งด้วยนิพจน์ที่ถูกต้อง
#XMSG: Weekly schedule error text
invalidWeeklyRecText=กรุณาเลือกอย่างน้อยหนึ่งวัน
#XFLD: No End date text
noEndDateText=ไม่มีวันที่สิ้นสุด
#XFLD: Help Button
openHelpButtonText=แสดงวิธีใช้
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=วันที่เกินจำนวนวันสูงสุดสำหรับเดือนที่ระบุ
#XMSG: Redundancy error in cron string
cronRedundantErrorText=รูปแบบนี้มีนิพจน์ที่ซ้ำซ้อน กรุณาย้ายรูปแบบ * หรือ / ออกจากรายการที่คั่นด้วยเครื่องหมายจุลภาค
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=ป้อนค่าระหว่าง 0 ถึง 59
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=ป้อนค่าที่สูงกว่า 10
#XMSG: Error message for missing start time
startTimeErrorText=เลือกเวลา
#XMSG: Error message handling for error code
invalidScheduleBodyError=เนื้อหาของคำขอมีข้อมูลที่ไม่ถูกต้อง
#XMSG: Error message handling for error code
scheduleNotFoundError=ไม่พบกำหนดการ
#XMSG: Error message handling for error code
actionForbiddenError=คุณไม่มีสิทธิที่จำเป็น
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=คุณกำลังพยายามสร้างกำหนดการที่มีอยู่แล้ว
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=รีเซ็ตทุกวัน
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=รีเซ็ตทุกเดือน
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=รีเซ็ตทุกปี
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=รีเซ็ตทุกสัปดาห์
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=รีเซ็ตทุกชั่วโมง
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=รีเซ็ตทุกวัน:
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=รีเซ็ตทุกเดือน:
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=รีเซ็ตทุกปี:
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=รีเซ็ตทุกสัปดาห์:
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=รีเซ็ตทุกชั่วโมง:
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=เวลา {0} ทุก {1} วัน
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=ให้สิทธิแก่เราในการดำเนินการเชนงานรวมถึงงานที่เกิดขึ้นประจำที่คุณจัดกำหนดการไว้
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=การดำเนินการตามกำหนดการจะโหลด Snapshot โหมดการทำสำเนาจะเปลี่ยนจากการทำสำเนาตามเวลาจริงเป็นการทำสำเนาแบทช์ และข้อมูลจะไม่ได้รับการอัพเดทตามเวลาจริงอีกต่อไป
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=คุณแน่ใจหรือไม่ว่าต้องการลบกำหนดการที่เลือก?
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn=ออบเจคที่เลือก {0} จาก {1} รายการมีการจัดกำหนดการ \nคุณแน่ใจหรือไม่ว่าต้องการลบกำหนดการ?

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=คุณต้องการเป็นเจ้าของกำหนดการสำหรับออบเจคที่เลือกหรือไม่?
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=กำหนดการถูกกำหนดสำหรับ {0} ของ {1} ออบเจคที่เลือก คุณต้องการเป็นเจ้าของกำหนดการเหล่านี้หรือไม่?

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=คุณต้องการหยุดกำหนดการชั่วคราวของออบเจคที่เลือกหรือไม่?
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn=ออบเจคที่เลือก {0} จาก {1} รายการกำลังดำเนินการกำหนดการ คุณต้องการหยุดชั่วคราวหรือไม่?

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=คุณต้องการดำเนินการกำหนดการของออบเจคที่เลือกต่อหรือไม่?
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn=ออบเจคที่เลือก {0} จาก {1} รายการหยุดกำหนดการชั่วคราว คุณต้องการดำเนินการต่อหรือไม่?

#XTXT: Warning text
warningText=คำเตือน
#XTXT: Text for schedule pause
pauseScheduleText=หยุดกำหนดการชั่วคราว
#XTXT: Text for resume schedule
resumeScheduleText=ดำเนินการกำหนดการต่อ
#XTXT: Text for assigning schedule
assignScheduleText=กำหนดกำหนดการให้กับฉัน

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=สิทธิในการดำเนินการงานที่เกิดขึ้นประจำของคุณจะหมดอายุเมื่อ {0} เมื่อต้องการดำเนินการงานตามกำหนดการของคุณต่อ เราต้องการความยินยอมของคุณอีกครั้ง

#XMSG: Reauthorize text
reauthorize=ให้สิทธิอีกครั้ง

#XTXT: Duration text
duration=ระยะเวลา
#XTXT: Time frame label text
timeFrame=กรอบเวลา
#XTXT: Hours Label text
hours=ชั่วโมง
#XTXT: Minutes Label text
minutes=นาที
#XTXT: Minutes Label text
minutesNew=นาที

#XTXT: Minute Recurrence type text
byMinute=นาที
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=ป้อนค่าระหว่าง 0 ถึง 59 หรือกำหนดความถี่ให้มากกว่าหรือเท่ากับ {0} นาที
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=ป้อนค่าระหว่าง 0 ถึง 59
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=ระยะเวลา {0} นาทีนานกว่าความถี่ตามกำหนดการ {1} นาที
#XTXT: Selected time zone At
timeZoneAt=เวลา
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=เริ่มต้นในเวลา
