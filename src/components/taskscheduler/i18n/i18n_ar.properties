#XMSG: Message to ask user to authorize
authorizeWarn=قم بتفويضنا بتشغيل المهام المتكررة التي قمت بجدولتها.
#XFLD: Authorize field value
authorise=تفويض
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=الجدولة من أجل "{0}"
#XMSG: Ok message
ok=صحيح
#XMSG: Schedule
schedule=الجدول الزمني
#XMSG: Cancel message
cancel=إلغاء
#XMSG: Close message
close=إغلاق
#XFLD: lable to set recurrence
recurrenceLabel=تكرار
#XFLD: label to set the recurrence value
everyLabel=كل
#XFLD: Start date field
startDateLabel=تاريخ البدء
#XFLD: Start date field
endDateLabel=تاريخ الانتهاء
#XFLD: TimeZone field
timeZoneLabel=المنطقة الزمنية
#XMSG: validation text
recurrenceValidationText1=أدخل قيمة عدد صحيح مكونة من 1 أو أكبر
#XMSG: validation text
recurrenceValidationText2=أدخل تاريخ بدء
#XMSG: validation text
recurrenceValidationText3=أدخل تاريخ بدء
#XMSG: validation text
noObjectSelectedText=حدد جدولًا بعيدًا لجدولته.
#XMSG: error text
errorLabel=خطأ
#XMSG: Schedule created alert message
createScheduleSuccess=تم إنشاء الجدول الزمني
#XMSG: Error in creating schedule
errorCreateSchedule=تعذر إنشاء الجدول الآن. يُرجى المحاولة مرة أخرى. \N {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=تم تحديث الجدول الزمني
#XMSG: Error in updating schedule
errorUpdateSchedule=تعذر تحديث الجدول الزمني الآن. يرجى المحاولة مرة أخرى. \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=هل أنت متأكد من رغبتك في حذف الجدول الزمني؟
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=تم حذف الجدول الزمني.
#XMSG: Schedule deletion error message
errorSeleteSchedule=تعذر حذف الجدول الزمني الآن. يرجى المحاولة مرة أخرى.
#XFLD: Authorise service title
authServiceLabel=تفويض الخدمة
#XMSG: Redirecting message
redirectingText=تتم إعادة التوجيه للمصادقة.
#XMSG: Redirection success message
redirectSuccess=لقد قمت بتفويضنا بنجاح بتشغيل المهام المتكررة التي قمت بجدولتها.
#XMSG: Start time label
startTimeFormatLabel=وقت البدء ({0}-تنسيق الساعة)
#XMSG: Start date and end date range validation
dateRangeValidationText=يجب أن يكون تاريخ البدء أقل من (يسبق) تاريخ الانتهاء.
#XMSG: Delete schedule error
errorDeleteSchedule=حدث خطأ أثناء حذف الجدول الزمني \n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=ساعة (ساعات)
#XFLD: Plural Recurrence text for Day
dayPluraltext=يوم (أيام)
#XFLD: Plural Recurrence text for Month
monthPluralText=شهر (شهور)
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=ساعات
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=أيام
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=شهور
#XFLD: Recurrence text for Hour
hourText=كل ساعة
#XFLD: Recurrence text for Day
daytext=يوميًا
#XFLD: Recurrence text for Month
monthText=شهري
#XMSG: Start date cannot be in the past
startDateValidationText=لا يمكن أن يقع تاريخ البدء في الماضي
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=أدخل قيمة بين 1 و23.
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=أدخل قيمة بين 1 و31.
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=أدخل قيمة بين 1 و12.
#XMSG: Invalid date text
invalidDateText=تاريخ غير صالح
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=تكرار
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=تاريخ البدء
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=تاريخ الانتهاء
#XMSG: Schedule popover end date label
naText=غير متوفر
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=المنطقة الزمنية
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=المالك
#XMSG: text for not available
naText=غير قابل للتطبيق
#XMSG: String Every
everyString=كل
#XMSG: on label
onLabel=تشغيل
#XMSG: on label
onLabelMonth=في يوم من الشهر
#XFLD: Label for Sunday
sunLabel=الأحد
#XFLD: Label for Monday
monLabel=الاثنين
#XFLD: Label for Tuesday
tueLabel=الثلاثاء
#XFLD: Label for Wednesday
wedLabel=الأربعاء
#XFLD: Label for Thursday
thuLabel=الخميس
#XFLD: Label for Friday
friLabel=الجمعة
#XFLD: Label for Saturday
satLabel=السبت
#XFLD: Recurrence text for Week
weekText=أسبوع
#XFLD: Create Schedule Dialog Title
createDialogTitle=إنشاء الجدول الزمني
#XFLD: Edit Dialog title
editDialogTitle=تحرير الجدول الزمني
#XFLD: Selected Object Title
selectedObjectText=محدد
#XMSG: Table Text
tableText=الجدول
#XMSG: View Text
viewText=عرض
#XMSG: Data flow text
dataFlowText=تدفق البيانات
#XMSG: Select a day of week error message
weekRecErrorText=حدد يومًا واحدًا من الأسبوع على الأقل
#XFLD: Label for Owner
ownerLabel=المالك
#XFLD: Button label for takeover schedule
takeoverLabel=تغيير المالك
#XFLD: Created By label
createdbyLabel=المنشئ
#XFLD: Changed By label
changedbyLabel=آخر تعديل بواسطة
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=مصادقة المستخدم مطلوبة لتشغيل المهام المجدولة في SAP Datasphere.\n باختيار تشغيل هذه المهمة، سيتم تشغيل هذه المهمة المجدولة معك بصفتك المالك.\n قبل أن يكون من المقرر تشغيل مهمة مجدولة، تأكد من أنك قد أعطيت موافقتك على تشغيل المهام نيابة عنك.
#XMSG: Schedule takeover success message
ownerChangeSuccessText=تولي الجدولة ناجح.
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=فشل تولي الجدولة الزمنية.
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=فشل تولي الجدولة الزمنية بسبب خطأ في الجدول الزمني للتحديث.
#XFLD: Text for confirm
confirm=تأكيد
#XMSG: Create schedule error message
createScheduleError=فشل إنشاء الجدول الزمني.
#XMSG: Schedule update failed message
updateScheduleError=فشل تحديث الجدول الزمني.
#XMSG: Schedule delete error message
deleteScheduleError=فشل حذف الجدول الزمني.
#XFLD: Label for Frequency
frequencyLabel=التكرار
#XFLD: Label for Frequency
frequencyLabelNew=الإعدادات
#XFLD: Label for repeat
repeatLabel=تكرار
#XFLD: Label for repeat
repeatLabelNeww=التكرار:
#XFLD: Labe for text at
atLabel=في
#XFLD: Label for starting
startingLabel=بدء
#XFLD: Label for Start
startLabel=بدء
#XFLD: Label for Ending
validityLabel=الصلاحية
#XFLD: Label for end
endLabel=الانتهاء
#XMSG: Message for no end date
noEndDateMsg=سيتم التكرار إلى الأبد.
#XFLD: Assign label
assignLabel=تعيين إليّ
#XFLD: Overview label
overviewLabel=نظرة عامة
#XFLD: Next runs field
nextRunsLabel=عمليات التشغيل التالية:
#XFLD: Next runs field
nextRunsLabelForPopOver=عمليات التشغيل التالية
#XFLD: Expired field
taskPopoverExpiredLabel=منتهي الصلاحية
#XFLD: Expired Message
taskPopoverExpiredMessage=انتهت صلاحية الجدول الزمني. لتمديده، قم بتحرير الجدول الزمني وتغيير تاريخ الانتهاء.
#XFLD: label for Day-of-month
dayOfmonthLabel=يوم من الشهر
#XFLD: label for Minute
minuteLabel=دقيقة
#XFLD: label for hour
hourLabel=ساعة
#XFLD: Last Day of month selection
lastDayText=آخر يوم من الشهر
#XFLD: Hourly unit description label
hourUnitLabel=ساعة من اليوم
#XFLD: Daily recurrence unit description label
dayUnitLabel=يوم (أيام) لكل شهر
#XFLD: Monthly receurrence unit description label
monthUnitLabel=شهور السنة
#XFLD: label for off
offLabel=إيقاف
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=يحدث كل {0} من الساعات بدءًا من {1} في غضون {2} من الدقائق.
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=يحدث كل {0} من الأيام بدءًا من {1} في الساعة {2}.
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=يحدث كل {0} من الأشهر بدءًا من {1} بتاريخ {2} في الساعة {3}.
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=يحدث كل أسبوع بتاريخ {0} في الساعة {1}.
#XMSG: Schedule expired message
scheduleExpiredLabel=انتهت صلاحية الجدول الزمني.
#XFLD: Create Schedule Dialog Title
createScheduleTitle=إنشاء الجدول الزمني "{0}"
#XFLD: Edit Dialog title
createScheduleTitleNew={0}: إنشاء جدول زمني
#XFLD: Edit Dialog title
editScheduleTitle=تحرير الجدول الزمني "{0}"
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}: تحرير الجدول الزمني
#XFLD: Edit Schedule Button
editSchedule=تحرير الجدول الزمني
#XFLD: Full Label for Sunday
sunFullLabel=الأحد
#XFLD: Full Label for Monday
monFullLabel=الاثنين
#XFLD: Full Label for Tuesday
tueFullLabel=الثلاثاء
#XFLD: Full Label for Wednesday
wedFullLabel=الأربعاء
#XFLD: Full Label for Thursday
thuFullLabel=الخميس
#XFLD: Full Label for Friday
friFullLabel=الجمعة
#XFLD: Full Label for Saturday
satFullLabel=السبت
#XFLD: Weekly text
weeklyText=أسبوعيًا
#XFLD: Set start day text
startEndDateText=تعيين تاريخي البدء والانتهاء
#XMSG: Schedule runs indefinitely message
validityOffText=سيتم تشغيل الجدول الزمني إلى الأبد.
#XFLD: Summary label
summaryLabel=الملخص
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=يتم التشغيل كل {0} من الساعات.  تتم إعادة تعيين دورة التشغيل كل يوم في الساعة 0:00 (توقيت جرينتش).
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=يتم التشغيل كل {0} من الأيام في الساعة {1} (توقيت جرينتش). تتم إعادة تعيين دورة التشغيل في أول يوم من كل شهر.
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=يتم التشغيل كل {0} من الشهور بتاريخ {1} في الساعة {2} (توقيت جرينتش). تتم إعادة تعيين دورة التشغيل في أول يوم من كل سنة.
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=يتم التشغيل كل أسبوع بتاريخ {0} في الساعة {1} (توقيت جرينتش).
#XFLD: Future runs label with time zone info
futureRunsLabel=عمليات التشغيل المستقبلية
#XFLD: Start & End Date label
startEndDateLabel=تعيين تاريخي البدء والانتهاء
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=قد لا يتم تنفيذ أول تشغيل لأنه على وشك البدء في أقل من 10 دقائق.
#XFLD: Label for text at UTC
atUTCLabel=في الساعة (توقيت جرينتش)
#XMSG: Authorize expired text
authExpiredText=انتهت صلاحية التفويض لتشغيل المهام المتكررة. تحتاج إلى تجديد التفويض للسماح لشركة SAP بتشغيل الجدولة نيابة عنك إذا كنت تريد متابعة مهام الجدولة.
#XMSG: Authorization expiring soon text
authExpiringSoonText=ستنتهي صلاحية تفويض تشغيل المهام المتكررة قريبًا. جدد التفويض للسماح لشركة SAP بمتابعة تشغيل الجدولة نيابة عنك.
#XMSG: Enter as Label text
enterAsText=إدخال كـ
#XMSG: Enter as Label text
enterAsTextNew=نوع الإعداد
#XMSG: standard schedule label text
simpleScheduleText=جدول بسيط
#XMSG: Cron expression label text
cronScheduleText=تعبير Cron
#XMSG: Time Range label text
timeRangeLabelText=النطاق الزمني
#XMSG: Time Range label text
timeRangeLabelTextNew=نطاق التواريخ
#XMSG: Ownership label
ownershipLabelText=الملكية
#XMSG: Show runs in the selected timezone label
showRunsInText=إظهار عمليات التشغيل في
#XMSG: Text for schedule pause
pauseScheduleLabel=إيقاف مؤقت للجدول:
#XMSG: Show Warning for schedule pause
schedulePausedWarning=هذا الجدول موقوف مؤقتًا. لا تتوفر أي عمليات تشغيل مستقبلية.
#XMSG: Cycle run text for hourly
hourlyCycleResetText=تتم إعادة تعيين دورة التشغيل في 00:00 كل يوم.
#XMSG: Cycle run text for Daily
dailyCycleResetText=تتم إعادة تعيين دورة التشغيل في اليوم الأول لكل شهر.
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=تتم إعادة تعيين دورة التشغيل في اليوم الأول من شهر يناير.
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=تتم إعادة تعيين دورة التشغيل في يوم الأحد من كل أسبوع.
#XMSG: Day label
dayLabel=اليوم (الشهر)
#XMSG: Monthly label
monthlyLabel=الشهر
#XMSG: Weekly label
weeklyLabel=اليوم (الأسبوع)
#XMSG: Time in UTC label
utcTimeLabel=الوقت الحالي (توقيت جرينتش)

#XMSG: January
monthLabel1=يناير
#XMSG: February
monthLabel2=فبراير
#XMSG: March
monthLabel3=مارس
#XMSG: April
monthLabel4=أبريل
#XMSG: May
monthLabel5=مايو
#XMSG: June
monthLabel6=يونيو
#XMSG: July
monthLabel7=يوليو
#XMSG: August
monthLabel8=أغسطس
#XMSG: September
monthLabel9=سبتمبر
#XMSG: October
monthLabel10=أكتوبر
#XMSG: November
monthLabel11=نوفمبر
#XMSG: December
monthLabel12=ديسمبر

weekdayLabel0=الأحد
weekdayLabel1=الاثنين
weekdayLabel2=الثلاثاء
weekdayLabel3=الأربعاء
weekdayLabel4=الخميس
weekdayLabel5=الجمعة
weekdayLabel6=السبت

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=في كل {0} دقيقة من {1} إلى {2}.
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=في كل {0} دقيقة.
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=في كل {0} دقيقة بدءًا من {1}.
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=في دقيقة {0}.

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=آخر كل {0} ساعة من {1} إلى {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=آخر كل {0} ساعة
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=آخر كل {0} ساعة بدءًا من {1}
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=آخر ساعة {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=في كل {0} يوم من الشهر من {1} خلال {2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=في كل {0} يوم من الشهر
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=في كل {0} يوم من الشهر بدءًا من {1}
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=في يوم من الشهر {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=في كل {0} شهر من {1} خلال {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=في كل {0} شهر
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=في كل {0} شهر بدءًا من {1}
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=بـ {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=في كل {0} يوم من الأسبوع من {1} خلال {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=في كل {0} يوم من الأسبوع
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=في كل {0} يوم من الأسبوع بدءًا من {1}
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=في {0}
#XMSG: And text
andText=و
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=أدخل قيمة مدعومة أو مجموعة قيم:
#XMSG: default information about cron string
defaultCronStringInfoText=القيم المدعومة لتعبيرات cron: <strong>الأرقام * , / </strong>.
#XMSG: Parameterized info about cron string
cronStringinfoText=القيم المدعومة للحقل المحدد: <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=تعرف على المزيد
#XMSG: Hide details text
hideDetailsText=إخفاء التفاصيل
#XMSG: Label for starting at UTC Text
startingText=بدءًا من (توقيت جرينتش)
#XMSG: Copy to clipboard label
copyToClipboardText=نسخ إلى الحافظة
#XMSG: cron copied to clipboard
cronCopiedText=تم نسخ تعبير cron إلى الحافظة
#XMSG: error message for invalid cron copy
invalidCronCopyText=لا يمكن نسخ تعبير cron غير صالح
#XMSG: No preview label
noPreviewText=لا يمكننا عرض معاينة نظرًا لوجود أخطاء أو قيم مفقودة. يُرجى مراجعة الجدول.
#XMSG: Label for create button
createText=إنشاء
#XMSG: Label for Save button
saveText=حفظ
#XMSG: Label for text leave
leaveText=مغادرة
#XMSG: warn message when user click cancel button
cancelWarnText=ستفقد جميع التغييرات التي أجريتها عند المغادرة.
#XMSG: Expired schedule warning
expiredTaskWarnText=انتهت صلاحية هذا الجدول. يُرجى تحديد تاريخ انتهاء جديد لمتابعة النسخ المتماثل.
#XMSG: Expired schedule message
expiredScheduleMessageText=انتهت صلاحية هذا الجدول. يُرجى تحديد تاريخ انتهاء جديد لمتابعة تشغيل المهام المتكررة.
#XMSG: Expired schedule message
expiredTaskWarnTextNew=انتهت صلاحية هذا الجدول الزمني. ولتمديده، قم بتغيير تاريخ الانتهاء.
#XMSG: Expired schedule Text
expiredScheduleText=منتهي الصلاحية
#XMSG: No future runs generated
noFutureRunsText=لا يتوفر تشغيل تالٍ.
#XMSG: Text for Local time
localTimeText=التوقيت المحلي
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=لا توجد عمليات تشغيل داخل نطاق التاريخ المحدد.
#XMSG: Warning text for expired end date
endDateExpiredText=تاريخ الانتهاء يقع في الماضي.
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=حدد تاريخ بدء.
#XMSG: UTC info label
utcInfoLabelText=يتم إنشاء الجداول الزمنية في توقيت جرينتش (UTC). توقيت جرينتش الحالي هو {0}.
#XMSG: UTC info label
utcInfoLabelTextNew=التوقيت العالمي المنسَّق (UTC). وقت توقيت جرينتش الحالي هو {0}.
#XMSG: Invalid cron paste error message
invalidCronPasteText=لا يمكنك لصق تعبير cron لأنه غير صالح. يرجى المحاولة مرة أخرى بتعبير صالح.
#XMSG: Weekly schedule error text
invalidWeeklyRecText=يُرجى تحديد يوم واحد على الأقل
#XFLD: No End date text
noEndDateText=بدون تاريخ انتهاء
#XFLD: Help Button
openHelpButtonText=إظهار المساعدة
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=يتجاوز التاريخ الحد الأقصى لعدد الأيام للشهر المحدد
#XMSG: Redundancy error in cron string
cronRedundantErrorText=هذا النمط له تعبيرات متكررة. يُرجى إزالة الأنماط: * أو / من قائمة مفصولة بفواصل.
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=أدخل قيمة بين 0 و59.
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=أدخِل قيمة أكبر من 10.
#XMSG: Error message for missing start time
startTimeErrorText=حدد وقتًا.
#XMSG: Error message handling for error code
invalidScheduleBodyError=نص الطلب يحتوي على بيانات غير صالحة.
#XMSG: Error message handling for error code
scheduleNotFoundError=لا يمكن العثور على الجدول الزمني.
#XMSG: Error message handling for error code
actionForbiddenError=ليس لديك الإذن المطلوب.
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=أنت تحاول إنشاء جدول موجود بالفعل.
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=إعادة التعيين كل يوم
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=إعادة التعيين كل شهر
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=إعادة التعيين كل سنة
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=إعادة التعيين كل أسبوع
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=إعادة التعيين كل ساعة
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=إعادة التعيين كل يوم:
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=إعادة التعيين كل شهر:
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=إعادة التعيين كل سنة:
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=إعادة التعيين كل أسبوع:
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=إعادة التعيين كل ساعة:
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=في {0} في كل {1} يوم
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=قم بتفويضنا بتشغيل سلاسل المهام والمهام المتكررة التي قمت بجدولتها.
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=سيؤدي تشغيل الجدول إلى تحميل لقطة. وبالتالي، سيتبدل نمط النسخ المتماثل من النسخ المتماثل في الوقت الفعلي إلى النسخ المتماثل للدُفعات، ولن يتم تحديث البيانات في الوقت الفعلي.
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=هل تريد بالتأكيد حذف الجداول المحددة؟
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn={0} من أصل {1} من الكائنات المحددة تحتوي على جداول. \nهل تريد بالتأكيد حذف الجداول؟

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=هل تريد الحصول على ملكية جداول الكائنات المحددة؟
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=الجداول محددة لـ {0} من أصل {1} من الكائنات المحددة. هل تريد الحصول على ملكية هذه الجداول؟

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=هل تريد إيقاف جداول الكائنات المحددة مؤقتًا؟
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn={0} من أصل {1} من الكائنات المحددة لها جداول زمنية جارية. هل تريد إيقافها مؤقتًا؟

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=هل تريد استئناف جداول الكائنات المحددة؟
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn={0} من أصل {1} من الكائنات المحددة لها جداول موقوفة مؤقتًا. هل تريد استئنافها؟

#XTXT: Warning text
warningText=تحذير
#XTXT: Text for schedule pause
pauseScheduleText=إيقاف مؤقت للجدول
#XTXT: Text for resume schedule
resumeScheduleText=استئناف الجدول
#XTXT: Text for assigning schedule
assignScheduleText=تعيين الجدول لي

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=ستنتهي صلاحية التفويض لتشغيل المهام المتكررة في {0}. لمتابعة تشغيل المهام المجدولة، نحتاج إلى موافقتك مرة أخرى.

#XMSG: Reauthorize text
reauthorize=إعادة التفويض

#XTXT: Duration text
duration=المدة
#XTXT: Time frame label text
timeFrame=الإطار الزمني
#XTXT: Hours Label text
hours=ساعة (ساعات)
#XTXT: Minutes Label text
minutes=دقيقة (دقائق)
#XTXT: Minutes Label text
minutesNew=دقائق

#XTXT: Minute Recurrence type text
byMinute=دقائق
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=أدخل قيمة بين 0 و59، أو قم بتعيين التكرار إلى أكثر من {0} من الدقائق أو يساويها
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=أدخل قيمة تقع بين 0 و59.
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=المدة التي تبلغ {0} من الدقائق أطول من التكرار المجدول وهي {1} من الدقائق.
#XTXT: Selected time zone At
timeZoneAt=في
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=البدء في
