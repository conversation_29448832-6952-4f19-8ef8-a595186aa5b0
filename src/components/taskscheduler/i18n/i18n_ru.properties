#XMSG: Message to ask user to authorize
authorizeWarn=Присвойте нам полномочия на выполнение запланированных вами повторяющихся задач.
#XFLD: Authorize field value
authorise=Присвоить полномочия
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=Запланировать для "{0}"
#XMSG: Ok message
ok=ОК
#XMSG: Schedule
schedule=Запланировать
#XMSG: Cancel message
cancel=Отменить
#XMSG: Close message
close=Закрыть
#XFLD: lable to set recurrence
recurrenceLabel=Повторение
#XFLD: label to set the recurrence value
everyLabel=Кажд.
#XFLD: Start date field
startDateLabel=Дата начала
#XFLD: Start date field
endDateLabel=Дата окончания
#XFLD: TimeZone field
timeZoneLabel=Часовой пояс
#XMSG: validation text
recurrenceValidationText1=Введите целое число от 1 и выше
#XMSG: validation text
recurrenceValidationText2=Введите дату начала
#XMSG: validation text
recurrenceValidationText3=Введите дату начала
#XMSG: validation text
noObjectSelectedText=Выберите дистанционную таблицу для планирования.
#XMSG: error text
errorLabel=Ошибка
#XMSG: Schedule created alert message
createScheduleSuccess=Планирование создано
#XMSG: Error in creating schedule
errorCreateSchedule=Не удалось создать планирование. Повторите попытку. \N {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=Планирование обновлено
#XMSG: Error in updating schedule
errorUpdateSchedule=Не удалось создать планирование. Повторите попытку. \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=Действительно удалить планирование?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Планирование удалено.
#XMSG: Schedule deletion error message
errorSeleteSchedule=Не получилось удалить планирование. Повторите попытку. \n
#XFLD: Authorise service title
authServiceLabel=Присвоить полномочия на сервис
#XMSG: Redirecting message
redirectingText=Перенаправление для аутентификации
#XMSG: Redirection success message
redirectSuccess=Вы успешно присвоили нам полномочия на выполнение запланированных вами повторяющихся задач.
#XMSG: Start time label
startTimeFormatLabel=Время начала (в {0}-часовом формате)
#XMSG: Start date and end date range validation
dateRangeValidationText=Дата начала должна быть меньше (раньше) даты окончания.
#XMSG: Delete schedule error
errorDeleteSchedule=Ошибка при удалении планирования \n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=ч
#XFLD: Plural Recurrence text for Day
dayPluraltext=дн.
#XFLD: Plural Recurrence text for Month
monthPluralText=мес.
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=ч
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=дн.
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=мес.
#XFLD: Recurrence text for Hour
hourText=Ежечасно
#XFLD: Recurrence text for Day
daytext=Ежедневно
#XFLD: Recurrence text for Month
monthText=Ежемесячно
#XMSG: Start date cannot be in the past
startDateValidationText=Дата начала не может быть в прошлом
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=Введите значение в диапазоне от 1 до 23.
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=Введите значение в диапазоне от 1 до 31.
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=Введите значение в диапазоне от 1 до 12.
#XMSG: Invalid date text
invalidDateText=Недействительная дата
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=Повторение
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=Дата начала
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=Дата окончания
#XMSG: Schedule popover end date label
naText=Н/П
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=Часовой пояс
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=Владелец
#XMSG: text for not available
naText=Н/Д
#XMSG: String Every
everyString=Кажд.
#XMSG: on label
onLabel=По
#XMSG: on label
onLabelMonth=День месяца
#XFLD: Label for Sunday
sunLabel=Вс
#XFLD: Label for Monday
monLabel=Пн
#XFLD: Label for Tuesday
tueLabel=Вт
#XFLD: Label for Wednesday
wedLabel=Ср
#XFLD: Label for Thursday
thuLabel=Чт
#XFLD: Label for Friday
friLabel=Пт
#XFLD: Label for Saturday
satLabel=Сб
#XFLD: Recurrence text for Week
weekText=Неделя
#XFLD: Create Schedule Dialog Title
createDialogTitle=Создать планирование
#XFLD: Edit Dialog title
editDialogTitle=Редактировать планирование
#XFLD: Selected Object Title
selectedObjectText=Выбрано
#XMSG: Table Text
tableText=Таблица
#XMSG: View Text
viewText=Ракурс
#XMSG: Data flow text
dataFlowText=Поток данных
#XMSG: Select a day of week error message
weekRecErrorText=Выберите минимум один день недели
#XFLD: Label for Owner
ownerLabel=Владелец
#XFLD: Button label for takeover schedule
takeoverLabel=Изменить владельца
#XFLD: Created By label
createdbyLabel=Создал
#XFLD: Changed By label
changedbyLabel=Последним изменил
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=Аутентификация пользователя требуется для выполнения запланированных задач в SAP Datasphere.\n Если выбрать выполнение этой задачи, запланированная задача будет выполнена с вами в качестве владельца.\n До наступления срока запланированной задачи убедитесь, что вы дали согласие на выполнение задач от вашего имени.
#XMSG: Schedule takeover success message
ownerChangeSuccessText=Планирование успешно принято.
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=Не удалось принять планирование.
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=Не удалось принять планирование из-за ошибки при обновлении планирования.
#XFLD: Text for confirm
confirm=Подтвердить
#XMSG: Create schedule error message
createScheduleError=Не удалось создать планирование.
#XMSG: Schedule update failed message
updateScheduleError=Не удалось обновить планирование.
#XMSG: Schedule delete error message
deleteScheduleError=Не получилось удалить планирование.
#XFLD: Label for Frequency
frequencyLabel=Частота
#XFLD: Label for Frequency
frequencyLabelNew=Настройки
#XFLD: Label for repeat
repeatLabel=Повторять
#XFLD: Label for repeat
repeatLabelNeww=Частота:
#XFLD: Labe for text at
atLabel=В
#XFLD: Label for starting
startingLabel=Начиная с
#XFLD: Label for Start
startLabel=Начало
#XFLD: Label for Ending
validityLabel=Срок действия
#XFLD: Label for end
endLabel=Конец
#XMSG: Message for no end date
noEndDateMsg=Повторять бесконечно.
#XFLD: Assign label
assignLabel=Присвоить мне
#XFLD: Overview label
overviewLabel=Обзор
#XFLD: Next runs field
nextRunsLabel=Следующие прогоны:
#XFLD: Next runs field
nextRunsLabelForPopOver=Следующие прогоны
#XFLD: Expired field
taskPopoverExpiredLabel=Срок истек
#XFLD: Expired Message
taskPopoverExpiredMessage=Срок действия планирования истек. Чтобы его продлить, отредактируйте планирование и измените дату окончания.
#XFLD: label for Day-of-month
dayOfmonthLabel=День месяца
#XFLD: label for Minute
minuteLabel=Минута
#XFLD: label for hour
hourLabel=Час
#XFLD: Last Day of month selection
lastDayText=Последний день месяца
#XFLD: Hourly unit description label
hourUnitLabel=Час дня
#XFLD: Daily recurrence unit description label
dayUnitLabel=Дней в месяц
#XFLD: Monthly receurrence unit description label
monthUnitLabel=Месяцы года
#XFLD: label for off
offLabel=Выкл.
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=Повторяется через {0} ч, начиная с {1} в {2} мин.
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=Повторяется через {0} д., начиная с {1} в {2}.
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=Повторяется через {0} мес., начиная с {1} в {2} в {3}.
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=Повторяется еженедельно в {0} в {1}.
#XMSG: Schedule expired message
scheduleExpiredLabel=Срок действия планирования истек.
#XFLD: Create Schedule Dialog Title
createScheduleTitle=Создать планирование для "{0}"
#XFLD: Edit Dialog title
createScheduleTitleNew= {0}: создать планирование
#XFLD: Edit Dialog title
editScheduleTitle=Редактировать планирование для "{0}"
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}: редактировать планирование
#XFLD: Edit Schedule Button
editSchedule=Редактировать планирование
#XFLD: Full Label for Sunday
sunFullLabel=воскресенье
#XFLD: Full Label for Monday
monFullLabel=понедельник
#XFLD: Full Label for Tuesday
tueFullLabel=вторник
#XFLD: Full Label for Wednesday
wedFullLabel=среда
#XFLD: Full Label for Thursday
thuFullLabel=четверг
#XFLD: Full Label for Friday
friFullLabel=пятница
#XFLD: Full Label for Saturday
satFullLabel=суббота
#XFLD: Weekly text
weeklyText=Еженедельно
#XFLD: Set start day text
startEndDateText=Задать даты начала и окончания
#XMSG: Schedule runs indefinitely message
validityOffText=Планирование будет выполняться бесконечно.
#XFLD: Summary label
summaryLabel=Обзор
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=Выполняется через {0} ч. Цикл выполнения сбрасывается ежедневно в 00:00 (UTC).
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=Выполняется через {0} д. в {1} (UTC). Цикл выполнения сбрасывается в первый день каждого месяца.
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=Выполняется через {0} мес. в {1} в {2} (UTC). Цикл выполнения сбрасывается в первый день каждого года.
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=Выполняется каждую неделю в {0} в {1} (UTC).
#XFLD: Future runs label with time zone info
futureRunsLabel=Будущие прогоны
#XFLD: Start & End Date label
startEndDateLabel=Задать даты начала и окончания
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=Первый прогоно, возможно, еще не выполнен, т. К. он выполняется меньше 10 минут.
#XFLD: Label for text at UTC
atUTCLabel=Время (UTC)
#XMSG: Authorize expired text
authExpiredText=Срок действия полномочия на выполнение повторяющихся задач истек. Нужно возобновить полномочие, чтобы позволить SAP планировать выполнение от вашего лица, если следует продолжать планирование задач.
#XMSG: Authorization expiring soon text
authExpiringSoonText=Срок действия полномочия на выполнение повторяющихся задач скоро истечет. Возобновите полномочие, чтобы позволить SAP планировать выполнение от вашего лица.
#XMSG: Enter as Label text
enterAsText=Ввести как
#XMSG: Enter as Label text
enterAsTextNew=Тип параметра
#XMSG: standard schedule label text
simpleScheduleText=Простое планирование
#XMSG: Cron expression label text
cronScheduleText=Выражение cron
#XMSG: Time Range label text
timeRangeLabelText=Период времени
#XMSG: Time Range label text
timeRangeLabelTextNew=Диапазон дат
#XMSG: Ownership label
ownershipLabelText=Владение
#XMSG: Show runs in the selected timezone label
showRunsInText=Показать выполнения в
#XMSG: Text for schedule pause
pauseScheduleLabel=Приостановить планирование:
#XMSG: Show Warning for schedule pause
schedulePausedWarning=Это планирование приостановлено. Будущие прогоны недоступны.
#XMSG: Cycle run text for hourly
hourlyCycleResetText=Цикл выполнения сбрасывается в 00:00 каждого дня.
#XMSG: Cycle run text for Daily
dailyCycleResetText=Цикл выполнения сбрасывается в первый день каждого месяца.
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=Цикл выполнения сбрасывается 1 января каждого года.
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=Цикл выполнения сбрасывается в воскресенье каждой недели.
#XMSG: Day label
dayLabel=День (месяца)
#XMSG: Monthly label
monthlyLabel=Месяц
#XMSG: Weekly label
weeklyLabel=День (недели)
#XMSG: Time in UTC label
utcTimeLabel=Текущее время (UTC)

#XMSG: January
monthLabel1=Январь
#XMSG: February
monthLabel2=Февраль
#XMSG: March
monthLabel3=Март
#XMSG: April
monthLabel4=Апрель
#XMSG: May
monthLabel5=Май
#XMSG: June
monthLabel6=Июнь
#XMSG: July
monthLabel7=Июль
#XMSG: August
monthLabel8=Август
#XMSG: September
monthLabel9=Сентябрь
#XMSG: October
monthLabel10=Октябрь
#XMSG: November
monthLabel11=Ноябрь
#XMSG: December
monthLabel12=Декабрь

weekdayLabel0=Воскресенье
weekdayLabel1=Понедельник
weekdayLabel2=Вторник
weekdayLabel3=Среда
weekdayLabel4=Четверг
weekdayLabel5=Пятница
weekdayLabel6=Суббота

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=Кажд. {0} мин с {1} по {2}.
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=Кажд. {0} мин.
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=Кажд. {0} мин, начиная с {1}.
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=В {0} мин.

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=через кажд. {0} ч с {1} по {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=через кажд. {0} ч
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=через кажд. {0} ч, начиная с {1}
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=через {0} ч

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=каждый {0} день месяца с {1} по {2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=каждый {0} день месяца
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=каждый {0} день месяца, начиная с {1}
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=в день месяца {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=каждый {0} месяц с {1} по {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=каждый {0} месяц
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=каждый {0} месяц, начиная с {1}
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=в {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=каждый {0} день недели с {1} по {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=каждый {0} день недели
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=каждый {0} день недели, начиная с {1}
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=в {0}
#XMSG: And text
andText=и
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=Введите поддерживаемое значение или комбинацию значений:
#XMSG: default information about cron string
defaultCronStringInfoText=Поддерживаемые значения выражения cron: <strong>числа * , - /</strong>
#XMSG: Parameterized info about cron string
cronStringinfoText=Поддерживаемые значения выбранного поля: <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=Узнать больше
#XMSG: Hide details text
hideDetailsText=Скрыть подробные данные
#XMSG: Label for starting at UTC Text
startingText=Начиная с (UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=Скопировать в буфер обмена
#XMSG: cron copied to clipboard
cronCopiedText=Выражение cron, скопированное в буфер обмена
#XMSG: error message for invalid cron copy
invalidCronCopyText=Нельзя скопировать недействительное выражение cron
#XMSG: No preview label
noPreviewText=Невозможно выполнить предварительный просмотр из-за ошибок или отсутствующих значений. Проверьте планирование.
#XMSG: Label for create button
createText=Создать
#XMSG: Label for Save button
saveText=Сохранить
#XMSG: Label for text leave
leaveText=Выйти
#XMSG: warn message when user click cancel button
cancelWarnText=При выходе изменения будут потеряны.
#XMSG: Expired schedule warning
expiredTaskWarnText=Срок действия этого плана истек. Выберите новую дату окончания, чтобы продолжить тиражирование.
#XMSG: Expired schedule message
expiredScheduleMessageText=Срок действия этого плана истек. Выберите новую дату окончания, чтобы продолжить выполнение повторяющихся задач.
#XMSG: Expired schedule message
expiredTaskWarnTextNew=Срок действия этого планирования истек. Чтобы его продлить, измените дату окончания.
#XMSG: Expired schedule Text
expiredScheduleText=Срок истек
#XMSG: No future runs generated
noFutureRunsText=Следующий прогон недоступен.
#XMSG: Text for Local time
localTimeText=Местное время
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=Нет выполнений в выбранный период времени.
#XMSG: Warning text for expired end date
endDateExpiredText=Дата окончания находится в прошлом.
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=Укажите дату начала.
#XMSG: UTC info label
utcInfoLabelText=Планы созданы по всемирному координированному времени (UTC). Текущее время UTC = {0}.
#XMSG: UTC info label
utcInfoLabelTextNew=Всемирное координированное время (UTC). Текущее время UTC - {0}.
#XMSG: Invalid cron paste error message
invalidCronPasteText=Невозможно вставить недействительное выражение cron. Повторите попытка с действительным выражением.
#XMSG: Weekly schedule error text
invalidWeeklyRecText=Выберите хотя бы один день.
#XFLD: No End date text
noEndDateText=Нет даты окончания
#XFLD: Help Button
openHelpButtonText=Показать справку
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=Дата превышает максимальное количество дней в данном месяце
#XMSG: Redundancy error in cron string
cronRedundantErrorText=Этот шаблон содержит лишние выражения. Удалите шаблоны из списка через запятую: * или /.
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=Введите значение в диапазоне от 0 до 59.
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=Введите значение больше 10.
#XMSG: Error message for missing start time
startTimeErrorText=Выберите время.
#XMSG: Error message handling for error code
invalidScheduleBodyError=Текст запроса содержит недействительные данные.
#XMSG: Error message handling for error code
scheduleNotFoundError=Невозможно найти планирование.
#XMSG: Error message handling for error code
actionForbiddenError=Отсутствуют необходимые полномочия.
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=Вы пытаетесь создать планирование, которое уже существует.
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=Сбрасывать каждый день
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=Сбрасывать каждый месяц
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=Сбрасывать каждый год
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=Сбрасывать каждую неделю
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=Сбрасывать каждый час
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=Сбрасывать каждый день:
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=Сбрасывать каждый месяц:
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=Сбрасывать каждый год:
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=Сбрасывать каждую неделю:
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=Сбрасывать каждый час:
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=В {0} каждый {1} день
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=Присвойте нам полномочия на выполнение цепочек задач, а также запланированных вами повторяющихся задач.
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=Прогон планирования загрузит мгновенный снимок. Это переключит режим тиражирования с тиражирования в реальном времени на фоновое, и данные больше не будут обновляться в реальном времени.
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=Удалить выбранное планирование?
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn={0} из {1} выбранных объектов имеют планирование. \nДействительно удалить планирование?

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=Вы хотите стать владельцем планирования для выбранных объектов?
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=Планирование определено для {0} из {1} выбранных объектов. Вы хотите стать владельцем этого планирования?

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=Приостановить планирование для выбранных объектов?
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn={0} из {1} выбранных объектов имеют запущенное планирование. Приостановить?

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=Возобновить планирование для выбранных объектов?
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn={0} из {1} выбранных объектов имеют приостановленное планирование. Возобновить?

#XTXT: Warning text
warningText=Предупреждение
#XTXT: Text for schedule pause
pauseScheduleText=Приостановить планирование
#XTXT: Text for resume schedule
resumeScheduleText=Возобновить планирование
#XTXT: Text for assigning schedule
assignScheduleText=Присвоить планирование мне

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=Полномочия на выполнение ваших периодических задач истекают {0}. Для продолжения выполнения ваших запланированных задач продлите согласие.

#XMSG: Reauthorize text
reauthorize=Повторная авторизация

#XTXT: Duration text
duration=Продолжительность
#XTXT: Time frame label text
timeFrame=Временной интервал
#XTXT: Hours Label text
hours=ч
#XTXT: Minutes Label text
minutes=мин
#XTXT: Minutes Label text
minutesNew=мин

#XTXT: Minute Recurrence type text
byMinute=мин
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=Введите значение от 0 до 59 или задайте частоту не меньше {0} минут
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=Введите значение от 0 до 59.
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=Продолжительность ({0} мин) превышает запланированную частоту ({1} мин).
#XTXT: Selected time zone At
timeZoneAt=В
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=Начиная в
