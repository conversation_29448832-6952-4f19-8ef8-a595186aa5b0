#XMSG: Message to ask user to authorize
authorizeWarn=Дозвольте нам виконувати задані вами повторювані завдання.
#XFLD: Authorize field value
authorise=Дозволити
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=Розклад для "{0}"
#XMSG: Ok message
ok=OK
#XMSG: Schedule
schedule=Розклад
#XMSG: Cancel message
cancel=Скасувати
#XMSG: Close message
close=Закрити
#XFLD: lable to set recurrence
recurrenceLabel=Повторення
#XFLD: label to set the recurrence value
everyLabel=Кожні
#XFLD: Start date field
startDateLabel=Дата початку
#XFLD: Start date field
endDateLabel=Дата завершення
#XFLD: TimeZone field
timeZoneLabel=Часовий пояс
#XMSG: validation text
recurrenceValidationText1=Введіть ціле додатне значення
#XMSG: validation text
recurrenceValidationText2=Введіть дату початку
#XMSG: validation text
recurrenceValidationText3=Введіть дату початку
#XMSG: validation text
noObjectSelectedText=Виберіть віддалену таблицю для розкладу.
#XMSG: error text
errorLabel=Помилка
#XMSG: Schedule created alert message
createScheduleSuccess=Розклад створено
#XMSG: Error in creating schedule
errorCreateSchedule=Не вдалося створити розклад прямо зараз. Спробуйте ще раз. \N {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=Розклад оновлено
#XMSG: Error in updating schedule
errorUpdateSchedule=Не вдалося оновити розклад прямо зараз. Спробуйте ще раз. \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=Справді видалити розклад?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Розклад видалено.
#XMSG: Schedule deletion error message
errorSeleteSchedule=Не вдалося видалити розклад прямо зараз. Спробуйте ще раз.
#XFLD: Authorise service title
authServiceLabel=Дати дозвіл службі
#XMSG: Redirecting message
redirectingText=Перенаправлення для автентифікації.
#XMSG: Redirection success message
redirectSuccess=Ви успішно надали нам дозвіл виконувати задані вами повторювані завдання.
#XMSG: Start time label
startTimeFormatLabel=Час початку ({0}-годинний формат)
#XMSG: Start date and end date range validation
dateRangeValidationText=Дата початку має бути меншою (наставати раніше) дати завершення
#XMSG: Delete schedule error
errorDeleteSchedule=Помилка під час видалення розкладу \n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=год.
#XFLD: Plural Recurrence text for Day
dayPluraltext=дн.
#XFLD: Plural Recurrence text for Month
monthPluralText=міс.
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=Години
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=Дні
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=Місяці
#XFLD: Recurrence text for Hour
hourText=Щогодини
#XFLD: Recurrence text for Day
daytext=Щодня
#XFLD: Recurrence text for Month
monthText=Щомісяця
#XMSG: Start date cannot be in the past
startDateValidationText=Дата початку не може бути в минулому
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=Введіть значення від 1 до 23.
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=Введіть значення від 1 до 31.
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=Введіть значення від 1 до 12.
#XMSG: Invalid date text
invalidDateText=Недійсна дата
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=Повторення
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=Дата початку
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=Дата завершення
#XMSG: Schedule popover end date label
naText=Н/Д
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=Часовий пояс
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=Власник
#XMSG: text for not available
naText=Н/Д
#XMSG: String Every
everyString=Кожні
#XMSG: on label
onLabel=Коли
#XMSG: on label
onLabelMonth=У певний день місяця
#XFLD: Label for Sunday
sunLabel=нд
#XFLD: Label for Monday
monLabel=пн
#XFLD: Label for Tuesday
tueLabel=вт
#XFLD: Label for Wednesday
wedLabel=ср
#XFLD: Label for Thursday
thuLabel=чт
#XFLD: Label for Friday
friLabel=пт
#XFLD: Label for Saturday
satLabel=сб
#XFLD: Recurrence text for Week
weekText=Тиждень
#XFLD: Create Schedule Dialog Title
createDialogTitle=Створити розклад
#XFLD: Edit Dialog title
editDialogTitle=Редагувати розклад
#XFLD: Selected Object Title
selectedObjectText=Вибрано
#XMSG: Table Text
tableText=Таблиця
#XMSG: View Text
viewText=Подання
#XMSG: Data flow text
dataFlowText=Потік даних
#XMSG: Select a day of week error message
weekRecErrorText=Виберіть принаймні один день тижня
#XFLD: Label for Owner
ownerLabel=Власник
#XFLD: Button label for takeover schedule
takeoverLabel=Змінити власника
#XFLD: Created By label
createdbyLabel=Автор створення
#XFLD: Changed By label
changedbyLabel=Автор останньої зміни
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=Для виконання запланованих завдань у SAP Datasphere потрібна автентифікація користувача.\n Якщо ви виберете запуск цього завдання, це заплановане завдання виконуватиметься від вашого імені як власника.\n Перед тим, як заплановане завдання буде запущено, переконайтеся, що ви надали свою згоду на виконання завдань від вашого імені.
#XMSG: Schedule takeover success message
ownerChangeSuccessText=Розклад успішно поглинуто.
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=Не вдалося поглинути розклад.
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=Не вдалося поглинути розклад, оскільки під час оновлення розкладу сталася помилка.
#XFLD: Text for confirm
confirm=Підтвердити
#XMSG: Create schedule error message
createScheduleError=Не вдалося створити розклад.
#XMSG: Schedule update failed message
updateScheduleError=Не вдалося оновити розклад.
#XMSG: Schedule delete error message
deleteScheduleError=Не вдалося видалити розклад.
#XFLD: Label for Frequency
frequencyLabel=Частота
#XFLD: Label for Frequency
frequencyLabelNew=Настройки
#XFLD: Label for repeat
repeatLabel=Повторення
#XFLD: Label for repeat
repeatLabelNeww=Частота:
#XFLD: Labe for text at
atLabel=О
#XFLD: Label for starting
startingLabel=Початок
#XFLD: Label for Start
startLabel=Початок
#XFLD: Label for Ending
validityLabel=Чинність
#XFLD: Label for end
endLabel=Завершення
#XMSG: Message for no end date
noEndDateMsg=Повторення відбуватиметься нескінченно довго.
#XFLD: Assign label
assignLabel=Призначити мені
#XFLD: Overview label
overviewLabel=Огляд
#XFLD: Next runs field
nextRunsLabel=Наступні запуски:
#XFLD: Next runs field
nextRunsLabelForPopOver=Наступні прогони
#XFLD: Expired field
taskPopoverExpiredLabel=Прострочено
#XFLD: Expired Message
taskPopoverExpiredMessage=Термін дії розкладу минув. Щоб його продовжити, відредагуйте розклад і змініть дату завершення.
#XFLD: label for Day-of-month
dayOfmonthLabel=День місяця
#XFLD: label for Minute
minuteLabel=Хвилина
#XFLD: label for hour
hourLabel=Година
#XFLD: Last Day of month selection
lastDayText=Останній день місяця
#XFLD: Hourly unit description label
hourUnitLabel=Година дня
#XFLD: Daily recurrence unit description label
dayUnitLabel=Днів на місяць
#XFLD: Monthly receurrence unit description label
monthUnitLabel=Місяців на рік
#XFLD: label for off
offLabel=Вимк.
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=Відбувається кожні {0} год. починаючи з {1} через {2} хв.
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=Відбувається кожні {0} дні. починаючи з {1} о {2}.
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=Відбувається кожні {0} міс. починаючи з {1} на {2} о {3}.
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=Відбувається щотижня в {0} о {1}.
#XMSG: Schedule expired message
scheduleExpiredLabel=Строк дії розкладу закінчився.
#XFLD: Create Schedule Dialog Title
createScheduleTitle=Створити розклад для "{0}"
#XFLD: Edit Dialog title
createScheduleTitleNew={0}: створення розкладу
#XFLD: Edit Dialog title
editScheduleTitle=Редагувати розклад для "{0}"
#XFLD: Edit Dialog title
editScheduleTitleNeww={0}: редагування розкладу
#XFLD: Edit Schedule Button
editSchedule=Редагувати розклад
#XFLD: Full Label for Sunday
sunFullLabel=неділя
#XFLD: Full Label for Monday
monFullLabel=понеділок
#XFLD: Full Label for Tuesday
tueFullLabel=вівторок
#XFLD: Full Label for Wednesday
wedFullLabel=середа
#XFLD: Full Label for Thursday
thuFullLabel=четвер
#XFLD: Full Label for Friday
friFullLabel=пʼятниця
#XFLD: Full Label for Saturday
satFullLabel=субота
#XFLD: Weekly text
weeklyText=Щотижня
#XFLD: Set start day text
startEndDateText=Призначити дату початку й дату завершення
#XMSG: Schedule runs indefinitely message
validityOffText=Розклад діятиме необмежений час.
#XFLD: Summary label
summaryLabel=Зведення
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=Запускається кожні {0} год. Цикл виконання оновлюється щоденно о 0:00 (UTC).
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=Запускається кожні {0} дн. о {1} (UTC). Цикл виконання оновлюється в перший день кожного місяця.
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=Запускається кожні {0} міс. на {1} о {2} (UTC). Цикл виконання оновлюється в перший день кожного року.
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=Запускається щотижня в {0} о {1} (UTC).
#XFLD: Future runs label with time zone info
futureRunsLabel=Майбутні запуски
#XFLD: Start & End Date label
startEndDateLabel=Призначити дату початку й дату завершення
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=Перший прогін може бути не виконано, оскільки він має розпочатися менш ніж за 10 хвилин.
#XFLD: Label for text at UTC
atUTCLabel=(UTC)
#XMSG: Authorize expired text
authExpiredText=Термін дії авторизації повторюваних завдань вичерпано. Потрібно поновити авторизацію, щоб дозволити SAP запускати завдання за розкладом від вашого імені, якщо ви бажаєте продовжувати планування завдань.
#XMSG: Authorization expiring soon text
authExpiringSoonText=Термін дії авторизації повторюваних завдань скоро вичерпається. Поновіть авторизацію, щоб дозволити SAP запускати завдання за розкладом від вашого імені.
#XMSG: Enter as Label text
enterAsText=Ввести як
#XMSG: Enter as Label text
enterAsTextNew=Тип настройки
#XMSG: standard schedule label text
simpleScheduleText=Простий розклад
#XMSG: Cron expression label text
cronScheduleText=Вираз cron
#XMSG: Time Range label text
timeRangeLabelText=Діапазон часу
#XMSG: Time Range label text
timeRangeLabelTextNew=Діапазон дат
#XMSG: Ownership label
ownershipLabelText=Володіння
#XMSG: Show runs in the selected timezone label
showRunsInText=Показати прогони в
#XMSG: Text for schedule pause
pauseScheduleLabel=Призупинити розклад:
#XMSG: Show Warning for schedule pause
schedulePausedWarning=Цей розклад призупинено. Подальші прогони недоступні.
#XMSG: Cycle run text for hourly
hourlyCycleResetText=Цикл виконання оновлюється о 0:00 щодня.
#XMSG: Cycle run text for Daily
dailyCycleResetText=Цикл виконання оновлюється в перший день кожного місяця.
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=Цикл виконання оновлюється 1 січня щороку.
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=Цикл виконання оновлюється щотижня в неділю.
#XMSG: Day label
dayLabel=День (місяця)
#XMSG: Monthly label
monthlyLabel=Місяць
#XMSG: Weekly label
weeklyLabel=День (тижня)
#XMSG: Time in UTC label
utcTimeLabel=Поточний час (UTC)

#XMSG: January
monthLabel1=Січень
#XMSG: February
monthLabel2=Лютий
#XMSG: March
monthLabel3=Березень
#XMSG: April
monthLabel4=Квітень
#XMSG: May
monthLabel5=Травень
#XMSG: June
monthLabel6=Червень
#XMSG: July
monthLabel7=Липень
#XMSG: August
monthLabel8=Серпень
#XMSG: September
monthLabel9=Вересень
#XMSG: October
monthLabel10=Жовтень
#XMSG: November
monthLabel11=Листопад
#XMSG: December
monthLabel12=Грудень

weekdayLabel0=неділя
weekdayLabel1=понеділок
weekdayLabel2=вівторок
weekdayLabel3=середа
weekdayLabel4=четвер
weekdayLabel5=пʼятниця
weekdayLabel6=субота

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=Кожної {0}-ї хвилини з {1} до {2}.
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=Кожної {0}-ї хвилини.
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=Кожної {0}-ї хвилини, починаючи з {1}.
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=З {0}-ї хвилини.

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=Після кожних {0} годин з {1} до {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=Після кожних {0} годин
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=Після кожних {0} годин, починаючи з {1}
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=Після {0}-ї години

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=Кожного {0}-го дня місяця з {1} до {2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=Кожного {0}-го дня місяця
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=Кожного {0}-го дня місяця, починаючи з {1}
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=З {0}-го дня місяця

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=Кожного {0}-го місяця з {1} до {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=Кожного {0}-го місяця
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=Кожного {0}-го місяця, починаючи з {1}
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=в {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=Кожного {0}-го дня тижня з {1} до {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=Кожного {0}-го дня тижня
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=Кожного {0}-го дня тижня, починаючи з {1}
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=в {0}
#XMSG: And text
andText=і
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=Введіть підтримуване значення або комбінацію значень:
#XMSG: default information about cron string
defaultCronStringInfoText=Підтримувані значення для виразу cron: <strong>числа * , /</strong>.
#XMSG: Parameterized info about cron string
cronStringinfoText=Підтримувані значення для вибраного поля: <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=Дізнатися більше
#XMSG: Hide details text
hideDetailsText=Приховати подробиці
#XMSG: Label for starting at UTC Text
startingText=Почати в такий час (UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=Копіювати до буфера обміну
#XMSG: cron copied to clipboard
cronCopiedText=Вираз cron скопійовано до буфера обміну
#XMSG: error message for invalid cron copy
invalidCronCopyText=Не вдалося скопіювати недійсний вираз cron
#XMSG: No preview label
noPreviewText=Не вдалося відтворити попередній перегляд, оскільки є помилки або немає значень. Перевірте розклад.
#XMSG: Label for create button
createText=Створити
#XMSG: Label for Save button
saveText=Зберегти
#XMSG: Label for text leave
leaveText=Вийти
#XMSG: warn message when user click cancel button
cancelWarnText=Незбережені зміни буде втрачено.
#XMSG: Expired schedule warning
expiredTaskWarnText=Цей розклад застарів. Виберіть нову дату завершення, щоб продовжити реплікацію.
#XMSG: Expired schedule message
expiredScheduleMessageText=Цей розклад застарів. Виберіть нову дату завершення, щоб запустити повторювані завдання.
#XMSG: Expired schedule message
expiredTaskWarnTextNew=Термін дії цього розкладу минув. Щоб його продовжити, змініть дату завершення.
#XMSG: Expired schedule Text
expiredScheduleText=Прострочено
#XMSG: No future runs generated
noFutureRunsText=Немає наступного прогону.
#XMSG: Text for Local time
localTimeText=Місцевий час
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=У вибраний проміжок дат прогони не заплановано.
#XMSG: Warning text for expired end date
endDateExpiredText=Дата завершення минула.
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=Укажіть дату початку.
#XMSG: UTC info label
utcInfoLabelText=Розклади створюються за координованим універсальним часом (UTC). Поточний час за UTC: {0}.
#XMSG: UTC info label
utcInfoLabelTextNew=Координований універсальний час (UTC). Поточний час за UTC: {0}.
#XMSG: Invalid cron paste error message
invalidCronPasteText=Ми не можемо вставити вираз cron, оскільки він недійсний. Спробуйте ще раз із дійсним виразом.
#XMSG: Weekly schedule error text
invalidWeeklyRecText=Виберіть принаймні один день.
#XFLD: No End date text
noEndDateText=Немає дати завершення
#XFLD: Help Button
openHelpButtonText=Показати довідку
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=Дата перевищує максимальну кількість днів у цьому місяці
#XMSG: Redundancy error in cron string
cronRedundantErrorText=Цей шаблон містить надлишкові вирази. Вилучіть шаблони: * або / зі списку, розділеного комами.
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=Введіть значення від 0 до 59.
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=Введіть значення більше 10.
#XMSG: Error message for missing start time
startTimeErrorText=Виберіть час.
#XMSG: Error message handling for error code
invalidScheduleBodyError=Запит містить недійсні дані.
#XMSG: Error message handling for error code
scheduleNotFoundError=Не вдається знайти розклад.
#XMSG: Error message handling for error code
actionForbiddenError=Ви не маєте необхідного дозволу.
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=Ви намагаєтеся створити розклад, який уже існує.
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=Скидати щодня
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=Скидати щомісяця
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=Скидати щороку
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=Скидати щотижня
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=Скидати щогодини
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=Скидати щодня:
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=Скидати щомісяця:
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=Скидати щороку:
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=Скидати щотижня:
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=Скидати щогодини:
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=О {0} кожні {1} дн.
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=Дозвольте нам виконувати заплановані вами ланцюжки завдань і повторювані завдання.
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=Під час прогону за розкладом буде завантажено знімок. Як наслідок, режим реплікації буде змінено з реплікації в реальному часі на пакетну реплікацію, а дані більше не оновлюватимуться в реальному часі.
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=Справді видалити вибраний розклад?
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn=Вибрані об''єкти ({0} з {1}) отримали статистику. \nСправді видалити розклади?

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=Справді передати вам права власності на розклади вибраних об'єктів?
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=Для вибраних об''єктів ({0} з {1}) визначено розклади. Справді передати вам права власності на ці розклади?

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=Справді призупинити розклади вибраних об'єктів?
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn=Для вибраних об''єктів ({0} з {1}) є активні розклади. \nСправді призупинити їх?

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=Справді відновити розклади вибраних об'єктів?
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn=Для вибраних об''єктів ({0} з {1}) призупинено розклади. \nСправді відновити їх розклади?

#XTXT: Warning text
warningText=Застереження
#XTXT: Text for schedule pause
pauseScheduleText=Призупинити розклад
#XTXT: Text for resume schedule
resumeScheduleText=Відновити розклад
#XTXT: Text for assigning schedule
assignScheduleText=Призначити мені розклад

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=Строк дії повноваження для запуску повторюваних завдань минає {0}. Щоб продовжити запуск ваших завдань за розкладом, нам потрібно знову отримати вашу згоду.

#XMSG: Reauthorize text
reauthorize=Повторна авторизація

#XTXT: Duration text
duration=Тривалість
#XTXT: Time frame label text
timeFrame=Період часу
#XTXT: Hours Label text
hours=год
#XTXT: Minutes Label text
minutes=хв
#XTXT: Minutes Label text
minutesNew=Хвилини

#XTXT: Minute Recurrence type text
byMinute=Хвилини
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=Введіть значення від 0 до 59 або задайте частоту щонайменше {0} хв
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=Введіть значення від 0 до 59.
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=Тривалість {0} хв більша за заплановану частоту {1} хв.
#XTXT: Selected time zone At
timeZoneAt=О
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=Час початку
