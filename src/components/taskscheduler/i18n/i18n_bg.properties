#XMSG: Message to ask user to authorize
authorizeWarn=Упълномощете ни да изпълняваме периодични задачи, които сте насрочили.
#XFLD: Authorize field value
authorise=Упълномощаване
#XMSG: Title message to scedule remote table replication
scheduleDialogTitle=Насрочване за "{0}"
#XMSG: Ok message
ok=OK
#XMSG: Schedule
schedule=График
#XMSG: Cancel message
cancel=Отказ
#XMSG: Close message
close=Затваряне
#XFLD: lable to set recurrence
recurrenceLabel=Периодичност
#XFLD: label to set the recurrence value
everyLabel=Всеки
#XFLD: Start date field
startDateLabel=Начална дата
#XFLD: Start date field
endDateLabel=Крайна дата
#XFLD: TimeZone field
timeZoneLabel=Часови пояс
#XMSG: validation text
recurrenceValidationText1=Въведете цяло число, по-голямо или равно на 1
#XMSG: validation text
recurrenceValidationText2=Въведете начална дата
#XMSG: validation text
recurrenceValidationText3=Въведете начална дата
#XMSG: validation text
noObjectSelectedText=Изберете отдалечена таблица за планиране.
#XMSG: error text
errorLabel=Грешка
#XMSG: Schedule created alert message
createScheduleSuccess=Създаден е график
#XMSG: Error in creating schedule
errorCreateSchedule=Не беше възможно създаване на графика в момента. Моля, опитайте пак. \N {0}
#XMSG: Schedule updated alert message
updateScheduleSuccess=Графикът е актуализиран
#XMSG: Error in updating schedule
errorUpdateSchedule=Не беше възможно актуализиране на графика в момента. Моля, опитайте пак. \n {0}
#XMSG: Warning message before deleting a schedule
deleteScheduleWarn=Наистина ли искате да изтриете графика?
#XMSG: Schedule deleted alert message
deleteScheduleSuccess=Графикът е изтрит.
#XMSG: Schedule deletion error message
errorSeleteSchedule=Не беше възможно изтриване на графика в момента. Моля, опитайте пак.
#XFLD: Authorise service title
authServiceLabel=Услуга за оторизиране
#XMSG: Redirecting message
redirectingText=Пренасочване към удостоверяване.
#XMSG: Redirection success message
redirectSuccess=Успешно ни упълномощихте да изпълняваме периодичните задачи, които сте насрочили.
#XMSG: Start time label
startTimeFormatLabel=Начален час ({0}-часов формат)
#XMSG: Start date and end date range validation
dateRangeValidationText=Началната дата трябва да е преди крайната дата.
#XMSG: Delete schedule error
errorDeleteSchedule=Грешка при изтриване на график \n{0}
#XFLD: Plural Recurrence text for Hour
hourPluralText=часа
#XFLD: Plural Recurrence text for Day
dayPluraltext=дни
#XFLD: Plural Recurrence text for Month
monthPluralText=месеца
#XFLD: Plural Recurrence text for Hour
hourPluralTextNew=часа
#XFLD: Plural Recurrence text for Day
dayPluraltextNew=дни
#XFLD: Plural Recurrence text for Month
monthPluralTextNew=месеца
#XFLD: Recurrence text for Hour
hourText=По часове
#XFLD: Recurrence text for Day
daytext=По дни
#XFLD: Recurrence text for Month
monthText=По месеци
#XMSG: Start date cannot be in the past
startDateValidationText=Началната дата не може да е в миналото
#XMSG: Error message for Hour recurrence
recurrenceHourValidationText=Въведете стойност между 1 и 23.
#XMSG: Error message for Day recurrence
recurrenceDayValidationText=Въведете стойност между 1 и 31.
#XMSG: Error message for Month recurrence
recurrenceMonthValidationText=Въведете стойност между 1 и 12.
#XMSG: Invalid date text
invalidDateText=Невалидна дата
#XMSG: Schedule popover recurrence label
taskPopoverRecLabel=Периодичност
#XMSG: Schedule popover start date label
taskPopoverStartDateLabel=Начална дата
#XMSG: Schedule popover end date label
taskPopoverEndDateLabel=Крайна дата
#XMSG: Schedule popover end date label
naText=Няма
#XMSG: Schedule popover time zone label
taskPoppverTimezoneLabel=Часови пояс
#XMSG: Schedule popover owner label
taskPopoverOwnerLabel=Собственик
#XMSG: text for not available
naText=Няма
#XMSG: String Every
everyString=всеки/всяка
#XMSG: on label
onLabel=в/на
#XMSG: on label
onLabelMonth=на ден от месеца
#XFLD: Label for Sunday
sunLabel=нд
#XFLD: Label for Monday
monLabel=пн
#XFLD: Label for Tuesday
tueLabel=вт
#XFLD: Label for Wednesday
wedLabel=ср
#XFLD: Label for Thursday
thuLabel=чт
#XFLD: Label for Friday
friLabel=пт
#XFLD: Label for Saturday
satLabel=сб
#XFLD: Recurrence text for Week
weekText=Седмица
#XFLD: Create Schedule Dialog Title
createDialogTitle=Създаване на график
#XFLD: Edit Dialog title
editDialogTitle=Редактиране на график
#XFLD: Selected Object Title
selectedObjectText=Избрано
#XMSG: Table Text
tableText=Таблица
#XMSG: View Text
viewText=Изглед
#XMSG: Data flow text
dataFlowText=Поток от данни
#XMSG: Select a day of week error message
weekRecErrorText=Изберете поне един ден от седмицата
#XFLD: Label for Owner
ownerLabel=Собственик
#XFLD: Button label for takeover schedule
takeoverLabel=Промяна на собственика
#XFLD: Created By label
createdbyLabel=Автор
#XFLD: Changed By label
changedbyLabel=Последно променил
#XMSG: Warning message for changing owner of schedule
ownerChangeWarnTextNew=Необходимо е удостоверяване на потребителя, за да може да изпълнявате планирани задачи в SAP Datasphere.\n Избирайки да се изпълни тази планирана задача, тя ще се изпълни с вас, като собственик.\n Преди планираната задача да се изпълни, уверете се, че сте дали съгласието си за изпълнение на задачите от ваше име.
#XMSG: Schedule takeover success message
ownerChangeSuccessText=Графикът е прехвърлен успешно.
#XMSG: Schedule takeoverfailed message
ownerChangeErrorText=Прехвърлянето на графика е неуспешно.
#XMSG: Schedule takeover failed due to schedule update failure
ownerChangeScheduleUpdateErrorText=Прехвърлянето на графика е неуспешно поради грешка в графика за актуализиране.
#XFLD: Text for confirm
confirm=Потвърждаване
#XMSG: Create schedule error message
createScheduleError=Създаването на графика е неуспешно.
#XMSG: Schedule update failed message
updateScheduleError=Актуализирането на графика е неуспешно.
#XMSG: Schedule delete error message
deleteScheduleError=Изтриването на графика е неуспешно.
#XFLD: Label for Frequency
frequencyLabel=Честота
#XFLD: Label for Frequency
frequencyLabelNew=Настройки
#XFLD: Label for repeat
repeatLabel=Повтаряне
#XFLD: Label for repeat
repeatLabelNeww=Честота:
#XFLD: Labe for text at
atLabel=В
#XFLD: Label for starting
startingLabel=Стартиране
#XFLD: Label for Start
startLabel=Начало
#XFLD: Label for Ending
validityLabel=Валидност
#XFLD: Label for end
endLabel=Край
#XMSG: Message for no end date
noEndDateMsg=Повторяемостта ще възниква без определен край.
#XFLD: Assign label
assignLabel=Дай на мен
#XFLD: Overview label
overviewLabel=Общ преглед
#XFLD: Next runs field
nextRunsLabel=Следващи изпълнения:
#XFLD: Next runs field
nextRunsLabelForPopOver=Следващи изпълнения
#XFLD: Expired field
taskPopoverExpiredLabel=С изтекъл срок
#XFLD: Expired Message
taskPopoverExpiredMessage=Срокът на графика е изтекъл. За да го удължите, редактирайте графика и променете крайната дата.
#XFLD: label for Day-of-month
dayOfmonthLabel=число
#XFLD: label for Minute
minuteLabel=минута
#XFLD: label for hour
hourLabel=час
#XFLD: Last Day of month selection
lastDayText=Последен ден от месеца
#XFLD: Hourly unit description label
hourUnitLabel=час от деня
#XFLD: Daily recurrence unit description label
dayUnitLabel=дни в месеца
#XFLD: Monthly receurrence unit description label
monthUnitLabel=месеца в годината
#XFLD: label for off
offLabel=Изключено
#XMSG: Recurrence overview message Hourly Schedule
overviewHourMsg=Изпълнява се на всеки {0} часа с начало {1} в {2} минути.
#XMSG: Recurrence overview message for Daily schedule
overviewDayMsg=Изпълнява се на всеки {0} дни с начало {1} в {2}.
#XMSG: Recurrence overview message for monthly schedule
overviewMonthMsg=Изпълнява се на всеки {0} месеца с начало {1} на {2} в {3}.
#XMSG: Recurrence overview message for weekly schedule
overviewWeekMsg=Изпълнява се всяка седмица в {0} в {1}.
#XMSG: Schedule expired message
scheduleExpiredLabel=Графикът е изтекъл.
#XFLD: Create Schedule Dialog Title
createScheduleTitle=Създаване на график за "{0}"
#XFLD: Edit Dialog title
createScheduleTitleNew={0}: създаване на график
#XFLD: Edit Dialog title
editScheduleTitle=Редактиране на график за "{0}"
#XFLD: Edit Dialog title
editScheduleTitleNeww= {0}: редактиране на график
#XFLD: Edit Schedule Button
editSchedule=Редактиране на графика
#XFLD: Full Label for Sunday
sunFullLabel=неделя
#XFLD: Full Label for Monday
monFullLabel=понеделник
#XFLD: Full Label for Tuesday
tueFullLabel=вторник
#XFLD: Full Label for Wednesday
wedFullLabel=сряда
#XFLD: Full Label for Thursday
thuFullLabel=четвъртък
#XFLD: Full Label for Friday
friFullLabel=петък
#XFLD: Full Label for Saturday
satFullLabel=събота
#XFLD: Weekly text
weeklyText=Седмично
#XFLD: Set start day text
startEndDateText=Задаване на начална и крайна дата
#XMSG: Schedule runs indefinitely message
validityOffText=Планираната задача ще се изпълява неограничено време.
#XFLD: Summary label
summaryLabel=Обобщение
#XMSG: Recurrence overview message Hourly Schedule
runsUTCHourOverviewMsg=Изпълнява се през {0} часа. Цикълът на изпълнение се рестартира всеки ден в 0:00 ч. (UTC).
#XMSG: Recurrence overview message for Daily schedule
runsUTCDayOverviewMsg=Изпълнява се през {0} дни в {1} (UTC). Изпълнението на цикъла се рестартира на всяко първо число от месеца.
#XMSG: Recurrence overview message for monthly schedule
runsUTCMonthOverviewMsg=Изпълнява се през {0} месеца на {1} в {2} (UTC). Изпълнението на цикъла се рестартира в първия ден на всяка година.
#XMSG: Recurrence overview message for weekly schedule
runsUTCWeekOverviewMsg=Изпълнява се всяка седмица в {0} в {1} (UTC).
#XFLD: Future runs label with time zone info
futureRunsLabel=Бъдещи изпълнения
#XFLD: Start & End Date label
startEndDateLabel=Задаване на начална и крайна дата
#XMSG: Error message to warn user that the first execution might not run
firstRunWarnText=Първото изпълнение може да не се състои, тъй като се очаква да започне след по-малко от 10 минути.
#XFLD: Label for text at UTC
atUTCLabel=В (UTC)
#XMSG: Authorize expired text
authExpiredText=Оторизацията за изпълнение на вашите периодични задачи е изтекла. Трябва да я подновите, за да може SAP да изпълнява графика от ваше име, ако желаете да продължите да планирате задачи.
#XMSG: Authorization expiring soon text
authExpiringSoonText=Оторизацията за изпълнение на вашите периодични задачи скоро ще изтече. Подновете я, за да може SAP да продължи да изпълнява графика от ваше име.
#XMSG: Enter as Label text
enterAsText=Въвеждане като
#XMSG: Enter as Label text
enterAsTextNew=Вид настройка
#XMSG: standard schedule label text
simpleScheduleText=Опростен график
#XMSG: Cron expression label text
cronScheduleText=Cron израз
#XMSG: Time Range label text
timeRangeLabelText=Времеви диапазон
#XMSG: Time Range label text
timeRangeLabelTextNew=Диапазон от дати
#XMSG: Ownership label
ownershipLabelText=Собственост
#XMSG: Show runs in the selected timezone label
showRunsInText=Показване на изпълненията в
#XMSG: Text for schedule pause
pauseScheduleLabel=Поставяне на графика на пауза:
#XMSG: Show Warning for schedule pause
schedulePausedWarning=Този график е поставен на пауза. Не са налични бъдещи изпълнения.
#XMSG: Cycle run text for hourly
hourlyCycleResetText=Изпълнението на цикъла се рестартира в 00:00 ч. всеки ден.
#XMSG: Cycle run text for Daily
dailyCycleResetText=Изпълнението на цикъла се рестартира на всяко първо число от месеца.
#XMSG: Cycle run text for Monthly
monthlyCycleResetText=Изпълнението на цикъла се рестартира на първи януари всяка година.
#XMSG: Cycle run text for Weekly
weeklyCycleResetText=Изпълнението на цикъла се рестартира всяка седмица в неделя.
#XMSG: Day label
dayLabel=Ден (месец)
#XMSG: Monthly label
monthlyLabel=Месец
#XMSG: Weekly label
weeklyLabel=Ден (седмица)
#XMSG: Time in UTC label
utcTimeLabel=Текущо време (UTC)

#XMSG: January
monthLabel1=януари
#XMSG: February
monthLabel2=февруари
#XMSG: March
monthLabel3=март
#XMSG: April
monthLabel4=април
#XMSG: May
monthLabel5=май
#XMSG: June
monthLabel6=юни
#XMSG: July
monthLabel7=юли
#XMSG: August
monthLabel8=август
#XMSG: September
monthLabel9=септември
#XMSG: October
monthLabel10=октомври
#XMSG: November
monthLabel11=ноември
#XMSG: December
monthLabel12=декември

weekdayLabel0=неделя
weekdayLabel1=понеделник
weekdayLabel2=вторник
weekdayLabel3=сряда
weekdayLabel4=четвъртък
weekdayLabel5=петък
weekdayLabel6=събота

#XMSG: Cron string summary for pattern a-b/n for minute. {0} {1} & {2}: Numbers or integer values
cronSummaryMinText1=На всяка {0}-а минута от {1} до {2}
#XMSG: Cron string summary for pattern */n for minute. {0}: number/Integer value.
cronSummaryMinText2=На всяка {0} минута
#XMSG: Cron string summary for pattern a/n for minute. {0} {2}: number/Integer value.
cronSummaryMinText3=На всяка {0}-а минута с начало в {1}
#XMSG: Cron string for fixed minute. {0}: number/Integer value.
cronSummaryMinText4=На минута {0}

#XMSG: Cron string summary for pattern a-b/n for hour. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryHourText1=от всеки {0}-и час от {1} до {2}
#XMSG: Cron string summary for pattern */n for hour. {0}: number/Integer value.
cronSummaryHourText2=от всеки {0}-и час
#XMSG: Cron string summary for pattern a/n for hour. {0} {1}: number/Integer value.
cronSummaryHourText3=от всеки {0}-и час с начало {1}
#XMSG: Cron string summary for fixed hour. {0}: Integer value
cronSummaryHourText4=на час {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryDayText1=на всяко {0}-о число от {1} до {2}
#XMSG: Cron string summary for pattern */n for daily. {0}: number/Integer value.
cronSummaryDayText2=на всяко {0}-о число
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryDayText3=на всяко {0}-о число с начало {1}
#XMSG: Cron string summary for fixed value. {0}: integer value
cronSummaryDayText4=на {0}-о число

#XMSG: Cron string summary for pattern a-b/n for daily. {0} {1} & {2}: Numbers or integer values of {0}.
cronSummaryMonthText1=на всеки {0}-и месец от {1} до {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryMonthText2=на всеки {0}-и месец
#XMSG: Cron string summary for pattern a/n. {0} {1}: number/Integer value.
cronSummaryMonthText3=на всеки {0}-и месец с начало {1}
#XMSG: Cron string summary for month name. {0}: Month name Jan-Feb
cronSummaryMonthText4=през {0}

#XMSG: Cron string summary for pattern a-b/n for daily. {0} : Numbers or integer values of {0}. {1} & {2}: Weekday names from Sunday-Saturday
cronSummaryWeekText1=на всеки {0}-и ден от седмицата от {1} до {2}
#XMSG: Cron string summary for pattern */n for monthly. {0}: number/Integer value.
cronSummaryWeekText2=на всеки {0}-и ден от седмицата
#XMSG: Cron string summary for pattern a/n. {0}: number/Integer value. {1}: name of weekday from Sunday-Saturday
cronSummaryWeekText3=на всеки {0}-и ден от седмицата с начало {1}
#XMSG: Cron string summary for month name. {0}: weekday name Sun-Sat
cronSummaryWeekText4=в {0}
#XMSG: And text
andText=и
#XMSG: Error message for wrong cron string pattern
cronErrorMsg=Въведете поддържана стойност или комбинация на стойности:
#XMSG: default information about cron string
defaultCronStringInfoText=Поддържани стойности за cron израз: <strong>цифри, * , / </strong>.
#XMSG: Parameterized info about cron string
cronStringinfoText=Поддържани стойности за избраното поле: <strong>{0}</strong>
#XMSG: Show details text
showDetailsText=Научете повече
#XMSG: Hide details text
hideDetailsText=Скриване на подробните данни
#XMSG: Label for starting at UTC Text
startingText=Начало в (UTC)
#XMSG: Copy to clipboard label
copyToClipboardText=Копиране в клипборд
#XMSG: cron copied to clipboard
cronCopiedText=Cron изразът е копиран в клипборда
#XMSG: error message for invalid cron copy
invalidCronCopyText=Копирането на невалиден cron израз е невъзможно
#XMSG: No preview label
noPreviewText=Не можем да покажем предварителен изглед, тъй като има грешки или липсват стойности. Моля, прегледайте графика.
#XMSG: Label for create button
createText=Създаване
#XMSG: Label for Save button
saveText=Запазване
#XMSG: Label for text leave
leaveText=Напускане
#XMSG: warn message when user click cancel button
cancelWarnText=Ако напуснете, вашите промени ще се изгубят.
#XMSG: Expired schedule warning
expiredTaskWarnText=Този график е изтекъл. Моля, изберете нова крайна дата, за да продължите с репликацията.
#XMSG: Expired schedule message
expiredScheduleMessageText=Този график е изтекъл. Моля, изберете нова крайна дата, за да продължите с изпълнението на периодичните задачи.
#XMSG: Expired schedule message
expiredTaskWarnTextNew=Срокът на този график е изтекъл. За да го удължите, променете крайната дата.
#XMSG: Expired schedule Text
expiredScheduleText=С изтекъл срок
#XMSG: No future runs generated
noFutureRunsText=Не е налично следващо изпълнение.
#XMSG: Text for Local time
localTimeText=Местно време
#XMSG: No scheduled runs within the selected date range message
noRunsInDateRangeText=Няма изпълнения в рамките на избрания диапазон от дати.
#XMSG: Warning text for expired end date
endDateExpiredText=Крайната дата е в миналото.
#XMSG: Start Date cannot be empty error message
startDateEmptyErrorText=Указване на начална дата.
#XMSG: UTC info label
utcInfoLabelText=Графиците се създават в координирано универсално време (UTC). Текущият час по UTC е {0}.
#XMSG: UTC info label
utcInfoLabelTextNew=Координирано универсално време (UTC). Текущият час по UTC е {0}.
#XMSG: Invalid cron paste error message
invalidCronPasteText=Не можем да поставим cron израза, защото е невалиден. Моля, опитайте отново с валиден израз.
#XMSG: Weekly schedule error text
invalidWeeklyRecText=Моля, изберете най-малко един ден.
#XFLD: No End date text
noEndDateText=Няма крайна дата
#XFLD: Help Button
openHelpButtonText=Покажи помощта
#XMSG: Date range cross the max days in a month error
maxDayInMonthError=Датата надвишава максималния брой дни за дадения месец
#XMSG: Redundancy error in cron string
cronRedundantErrorText=Този модел има излишни изрази. Моля, премахнете моделите: * или / от списъка, разделен със запетаи.
#XMSG: Cron error message for Minute recurrence
cronMinErrorText=Въведете стойност между 0 и 59.
#XMSG: Cron Minutes should not be less than 10 minutes
cronMinErrorLimitText=Въведете стойност по-голяма от 10.
#XMSG: Error message for missing start time
startTimeErrorText=Изберете час.
#XMSG: Error message handling for error code
invalidScheduleBodyError=Основният текст на заявката съдържа невалидни данни.
#XMSG: Error message handling for error code
scheduleNotFoundError=Графикът не може да бъде намерен.
#XMSG: Error message handling for error code
actionForbiddenError=Нямате необходимите права.
#XMSG: Error message handling for error code
scheduleAlreadyExistsError=Опитвате да създадете график, който вече съществува.
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoText=Изчистване всеки ден
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoText=Изчистване всеки месец
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoText=Изчистване всяка година
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoText=Изчистване всяка седмица
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoText=Изчистване на всеки час
#XMSG: Message for reset info for hourly schedule
hourlyResetInfoTextNew=Изчистване всеки ден:
#XMSG: Message for reset info for Daily Schedule
dailyResetInfoTextNew=Изчистване всеки месец:
#XMSG: Message for reset info for Monthly Schedule
monthlyResetInfoTextNew=Изчистване всяка година:
#XMSG: Message for reset info for Weekly Schedule
weeklyResetInfoTextNew=Изчистване всяка седмица:
#XMSG: Message for reset info for Minutes Schedule
minutesResetInfoTextNew=Изчистване всеки час:
#XMSG: Message for future run summary for non cron string daily schedule
dailyNonCronNextRunText=В {0} на всеки {1}-и ден
#XMSG: Message for authorization of Schedules & Task chains
authorizeText=Упълномощете ни да изпълняваме вериги от задачи, както и периодични задачи, които сте насрочили.
#XMSG: Warning to indicate next run will enable batch replication
warn_realtime_batch_schedule=При изпълнението на планирането ще се зареди моментна снимка. Репликацията ще премине от режим в реално време към пакетен режим и данните повече няма да се актуализират в реално време.
#XMSG: Warning message before mass deletion of schedules
massDeleteScheduleWarn=Наистина ли искате да изтриете избраните графици?
#XMSGS: Warning when partial list of selected schedules are deleted
massPartDeleteScheduleWarn={0} от {1} избрани обекта са получили графици. \nСигурни ли сте, че искате да изтриете тези графици?

#XMSG: Warning message before mass takeover of schedules
massTakeoverScheduleWarn=Желаете ли да поемете собствеността на графиците за избраните обекти?
#XMSGS: Warning when ownership of partial list of selected schedules are changed
massPartTakeoverScheduleWarn=Графиците са дефинирани за {0} от {1} избрани обекта. Желаете ли да поемете собствеността на тези графици?

#XMSG: Warning message before mass Pausing of schedules
massPauseScheduleWarn=Желаете ли да поставите на пауза графиците за избраните обекти?
#XMSGS:  Warning when partial list of selected schedules are Paused
massPartPauseScheduleWarn={0} от {1} избрани обекта изпълняват графици. Желаете ли да ги поставите на пауза?

#XMSG: Warning message before mass Resuming of schedules
massResumeScheduleWarn=Желаете ли да възобновите графиците за избраните обекти?
#XMSGS:  Warning when partial list of selected schedules are Resumed
massPartResumeScheduleWarn={0} от {1} избрани обекта са поставени на пауза. Желаете ли да ги възобновите?

#XTXT: Warning text
warningText=Предупреждение
#XTXT: Text for schedule pause
pauseScheduleText=Поставяне на график на пауза
#XTXT: Text for resume schedule
resumeScheduleText=График за възобновяване
#XTXT: Text for assigning schedule
assignScheduleText=Присъединяване на график към мен

#XMSG: Authorization expiration warning text with date as parameter.
authExpirationWarnText=Оторизацията за изпълнението на вашите периодични задачи ще изтече на {0}. За да продължите да изпълнявате вашите планирани задачи, отново имаме нужда от вашето съгласие.

#XMSG: Reauthorize text
reauthorize=Повторна оторизация

#XTXT: Duration text
duration=Времетраене
#XTXT: Time frame label text
timeFrame=Времева рамка
#XTXT: Hours Label text
hours=Часа
#XTXT: Minutes Label text
minutes=Минути
#XTXT: Minutes Label text
minutesNew=Минути

#XTXT: Minute Recurrence type text
byMinute=Минути
#XMSG: Cron error message for Minute recurrence
cronMinErrorText2=Въведете стойност между 0 и 59 или задайте честота от {0} или повече минути
#XMSG: Minutes error message for Minute recurrence
durationMinsValueStateText1=Въведете стойност между 0 и 59.
#XMSG: Duration error message for Minute recurrence
durationHoursValueStateText1=Продължителността от {0} минути е по-голяма от планираната честота от {1} минути.
#XTXT: Selected time zone At
timeZoneAt=В
#XTXT: Selected time zone  starting At for hourly
timeZoneStartingAt=Начало в
