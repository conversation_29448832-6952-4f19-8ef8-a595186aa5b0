<core:FragmentDefinition
  xmlns="sap.m"
  xmlns:l="sap.ui.layout"
  xmlns:f="sap.ui.layout.form"
  xmlns:core="sap.ui.core"
>
  <Popover
    showHeader="false"
    placement="Auto"
    contentWidth="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} ? '400px' : '300px'}"
  >
  <customHeader visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} === true}">
    <Bar>
        <contentMiddle>
            <Title text="{= ${schedulePopoverModel>/objectName} + ': ' + ${i18n_task>schedule}}"/>
        </contentMiddle>
    </Bar>
  </customHeader>
   <HBox class="sapUiSmallMarginBeginEnd sapUiSmallMarginTop" width="82%" visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} === true &amp;&amp; ${schedulePopoverModel>/isExpired} === true}">
    <MessageStrip
    type="Warning"
    showIcon="true"
    text="{i18n_task>taskPopoverExpiredMessage}"
    />
  </HBox>
    <HBox class="sapUiLargeMarginBegin">
      <f:SimpleForm
        id="TaskScheduleInfo"
        editable="false"
        layout="ResponsiveGridLayout"
        labelSpanXL="5"
        labelSpanL="5"
        labelSpanM="5"
        labelSpanS="5"
        adjustLabelSpan="false"
        emptySpanXL="0"
        emptySpanL="4"
        emptySpanM="0"
        emptySpanS="0"
        columnsXL="2"
        columnsL="1"
        columnsM="2"
        width="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} ? '400px' : '300px'}"
        singleContainerFullSize="false"
      >
        <f:content >
          <Label
            text="{i18n_task>everyLabel}"
            textAlign="Right"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} === undefined}"
          />
          <Text
            text="{schedulePopoverModel>/frequencyRecurrence}"
            textAlign="Begin"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} === undefined}"
          />
          <Label
            text="{i18n_task>onLabelMonth}"
            textAlign="Right"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/dayOfMonth} !== undefined &amp;&amp; ${schedulePopoverModel>/cronExp} === undefined}"
          />
          <Text
            text="{schedulePopoverModel>/dayOfMonth}"
            textAlign="Begin"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/dayOfMonth} !== undefined &amp;&amp; ${schedulePopoverModel>/cronExp} === undefined}"
          />
          <Label
            text="{schedulePopoverModel>/timeZoneDateLabel}"
            textAlign="Right"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} === undefined}"
          />
          <Text
            text="{schedulePopoverModel>/timeZoneDateText}"
            textAlign="Begin"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} === undefined}"
          />
          <Label
            text="{schedulePopoverModel>/resetInfoText}"
            textAlign="Right"
            class="sapUiTinyMarginTop"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} === undefined}"
          />
          <CheckBox visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} === undefined}" selected="{schedulePopoverModel>/resetSelected}" enabled="false" displayOnly="true"/>
          <Label
            text="{i18n_task>minuteLabel}"
            textAlign="Right"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} !== undefined}"
          />
          <Text
            text="{schedulePopoverModel>/cronMinute}"
            textAlign="Begin"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} !== undefined}"
          />
          <Label
            text="{i18n_task>hourLabel}"
            textAlign="Right"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} !== undefined}"
          />
          <Text
            text="{schedulePopoverModel>/cronHour}"
            textAlign="Begin"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} !== undefined}"
          />
          <Label
            text="{i18n_task>dayLabel}"
            textAlign="Right"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} !== undefined}"
          />
          <Text
            text="{schedulePopoverModel>/cronDay}"
            textAlign="Begin"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} !== undefined}"
          />
          <Label
            text="{i18n_task>monthlyLabel}"
            textAlign="Right"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} !== undefined}"
          />
          <Text
            text="{schedulePopoverModel>/cronMonth}"
            textAlign="Begin"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} !== undefined}"
          />
          <Label
            text="{i18n_task>weeklyLabel}"
            textAlign="Right"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} !== undefined}"
          />
          <Text
            text="{schedulePopoverModel>/cronWeek}"
            textAlign="Begin"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} &amp;&amp; ${schedulePopoverModel>/cronExp} !== undefined}"
          />
          <Label
            text="{i18n_task>taskPopoverRecLabel}"
            textAlign="Right"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} === false}"
          />
          <Text
            id="schedulepopoverRecInfo"
            textAlign="Begin"
            text="{schedulePopoverModel>/recurrenceString}"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} === false}"
          />
          <Label
            textAlign="Right"
            visible="{= ${featureflags>/DWCO_TASK_FRAMEWORK_LOCAL_TIME_ZONES} === true }"
            text="{i18n_task>timeZoneLabel}"
          />
          <Text
            visible="{= ${featureflags>/DWCO_TASK_FRAMEWORK_LOCAL_TIME_ZONES} === true }"
            id="schedulepopovertimeZoneInfo"
            text="{schedulePopoverModel>/timeZone}"
            textAlign="Begin"
          />
          <Label
            text="{= ${i18n_task>nextRunsLabelForPopOver}} ({schedulePopoverModel>/timeZone})"
            textAlign="Right"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} === true}"
            class="sapUiSmallMarginTopBottom"
          />
          <VBox>
          <Text
            text="{parts: ['schedulePopoverModel>/nextRuns'], formatter: '.formatNextRuns'}"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} === true &amp;&amp; ${schedulePopoverModel>/isExpired} !== true}"
            class="lineSpacingText sapUiSmallMarginTopBottom"
          />
          <Text
            text="{i18n_task>taskPopoverExpiredLabel}"
            visible="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} === true &amp;&amp; ${schedulePopoverModel>/isExpired} === true}"
            class="sapUiSmallMarginTopBottom"
            textAlign="Begin"
          />
         </VBox>

          <Label
            text="{i18n_task>taskPopoverStartDateLabel}"
            textAlign="Right"
          />
          <Text
            id="schedulepopoverStartDateInfo"
            text="{schedulePopoverModel>/startDate}"
            textAlign="Begin"
          />

          <Label
            text="{i18n_task>taskPopoverEndDateLabel}"
            textAlign="Right"
          >
          </Label>
          <Text
            id="schedulepopoverEndDateInfo"
            text="{= ${schedulePopoverModel>/endDate} !== 'NA' ?  ${schedulePopoverModel>/endDate} : ''}"
            textAlign="Begin"
          />

          <Label
            text="{i18n_task>taskPopoverOwnerLabel}"
            textAlign="Right"
            class="sapUiSmallMarginTop"
          >
          </Label>
          <Text
            id="schedulepopoverOwnerInfo"
            text="{schedulePopoverModel>/owner}"
            textAlign="Begin"
            class="sapUiSmallMarginTop"
          />
        </f:content>
      </f:SimpleForm>
    </HBox>
    <footer visible="{featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS}">
      <Bar>
          <contentRight>
              <Button id="editScheduleButton" text="{i18n_task>editSchedule}" press="onEditSchedule" />
          </contentRight>
      </Bar>
    </footer>
  </Popover>
</core:FragmentDefinition>
