/** @format */

import { isDiMonitorImprovementsEnabled } from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import { Format } from "../../reuse/utility/Format";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { ShellContainer } from "../../shell/utility/Container";
import { User } from "../../shell/utility/User";

const i18nModel = new sap.ui.model.resource.ResourceModel({
    bundleName: require("../i18n/i18n.properties"),
  }),
  timeZone = new sap.ui.model.json.JSONModel({
    timeZoneList: require("../../../../shared/task/Timezones.ts"),
  }),
  timeZoneFF = sap.ui.getCore().getModel("featureflags")?.getProperty("/DWCO_TASK_FRAMEWORK_LOCAL_TIME_ZONES"),
  isDiMonitorImpFF = sap.ui.getCore().getModel("featureflags")?.getProperty("/DWCO_DI_MONITOR_UI_IMPROVEMENTS");

export function recordAction(action: string, feature: string, eventname: string) {
  ShellContainer.get().getUsageCollectionService().recordAction({
    action: action,
    feature: feature,
    eventtype: eventname,
  });
}

export function getTaskScheduer(id: string, customURLParams?: any): Promise<any> {
  const datePattern = Format.getInstance().getUserDatePattern();
  const timePattern = Format.getInstance().getUserTimePattern();

  // read dwc task scheduler i18n and pass it to task scheduler
  return new Promise<any>((resolve) => {
    sap.ui.require(["sap/tf/scheduler/TaskScheduler"], (TaskScheduler) => {
      const simpleTaskSchedule = new TaskScheduler({
        id: id,
        userName: User.getInstance().getUserName(),
        datePattern: datePattern,
        timePattern: timePattern,
        i18n: i18nModel,
        customURLParams: customURLParams,
        timeZone: timeZone,
        timeZoneFF: timeZoneFF,
        isDiMonitorImpFF: isDiMonitorImpFF,
      });
      resolve(simpleTaskSchedule);
    });
  });
}

export function initTaskScheduerFragment(id: string, params: any): Promise<any> {
  const datePattern = Format.getInstance().getUserDatePattern();
  const timePattern = Format.getInstance().getUserTimePattern();

  // read dwc task scheduler i18n and pass it to task scheduler
  return new Promise<any>((resolve) => {
    sap.ui.require(["sap/tf/scheduler/TaskScheduler"], (TaskScheduler) => {
      const schedulingControl = new TaskScheduler({
        id: id,
        userName: User.getInstance().getUserName(),
        datePattern: datePattern,
        timePattern: timePattern,
        i18n: i18nModel,
        customURLParams: undefined,
        timeZone: timeZone,
        timeZoneFF: timeZoneFF,
        isDiMonitorImpFF: isDiMonitorImpFF,
      });
      sap.ui.core.Fragment.load({
        name: "sap.tf.scheduler.TaskSchedulerView",
        type: sap.ui.core.mvc.ViewType.XML,
        id: id + "--Inner",
        controller: schedulingControl.getTaskScheduleHelper(),
      }).then((schedulingFragment) => {
        schedulingControl.set_content(schedulingFragment);
        schedulingControl.initTaskFragment(params, params.spaceId, params.applicationId, id + "--Inner");
        schedulingControl.setModel(i18nModel, "i18n");
        resolve(schedulingControl);
      });
    });
  });
}

function formatNextRuns(nextRuns: string[]): string {
  if (!nextRuns || !Array.isArray(nextRuns)) {
    return "";
  }
  return nextRuns.filter((run) => run !== undefined && run !== null && run !== "").join("\n");
}

export async function openSchedulePopover(
  link,
  objectId,
  applicationId,
  spaceId,
  scheduleContext,
  data?: any,
  object?: any,
  onRefresh?: any,
  isSpaceLocked?: boolean
) {
  const oRessourceBundle = i18nModel.getResourceBundle();
  const naText = oRessourceBundle.getText("naText");
  const popOverFragment = require("../view/TaskSchedulePopover.fragment.xml");
  // eslint-disable-next-line @typescript-eslint/no-floating-promises
  const scheduleData = await scheduleContext.getScheduleDataForPopover(objectId, applicationId, spaceId);
  if (isDiMonitorImprovementsEnabled()) {
    scheduleData.objectName = objectId;
    const now = new Date();
    const targetDate = new Date(scheduleData.endDate);
    if (now.getTime() > targetDate.getTime()) {
      scheduleData.isExpired = true;
    }
  }
  const schedulePopoverModel = new sap.ui.model.json.JSONModel();
  schedulePopoverModel.setData(scheduleData);
  scheduleContext.setModel(schedulePopoverModel, "schedulePopoverModel");
  scheduleContext.setModel(sap.ui.getCore().getModel("featureflags"), "featureflags");
  // (scheduleContext.getView().getModel("i18n") as sap.ui.model.resource.ResourceModel).enhance(oRessourceBundle);
  scheduleContext.setModel(i18nModel, "i18n_task");

  // Add formatter function to the context
  scheduleContext.formatNextRuns = formatNextRuns;
  let scheduleInfoPopover;
  if (!scheduleInfoPopover) {
    scheduleInfoPopover = sap.ui.xmlfragment(
      scheduleContext.getId() + "--schedulePopover",
      popOverFragment,
      scheduleContext
    ) as sap.m.Popover;
    if (!isDiMonitorImprovementsEnabled()) {
      scheduleInfoPopover?.destroyFooter();
      scheduleInfoPopover?.destroyCustomHeader();
    }
    scheduleContext.addDependent(scheduleInfoPopover);
    scheduleInfoPopover.openBy(link, true);
    if (isDiMonitorImprovementsEnabled()) {
      const oEditScheduleButton = sap.ui
        .getCore()
        .byId(scheduleContext.getId() + "--schedulePopover--editScheduleButton");
      oEditScheduleButton.setEnabled(!isSpaceLocked);
      oEditScheduleButton.attachPress(() => {
        scheduleInfoPopover.close();
        scheduleInfoPopover.destroy();
        object?.view.setBusy(true);
        recordAction(`editTaskSchedule: ${applicationId}`, "taskSchedule", "onEdit");
        scheduleContext.changeTaskSchedule(
          data,
          spaceId,
          applicationId,
          () => {
            recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "edit");
            const msg = oRessourceBundle.getText("updateScheduleSuccess");
            onRefresh()?.then(() => {
              sap.m.MessageToast.show(msg);
            });
          },
          (err) => {
            recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "edit");
            MessageHandler.exception({
              exception: err.error,
              message: err.message,
            });
            object?.view.setBusy(false);
          },
          () => {
            object?.view.setBusy(false);
          }
        );
      });
    }
  } else {
    scheduleInfoPopover.openBy(link, true);
  }

  scheduleInfoPopover?.attachAfterClose(() => {
    scheduleInfoPopover.destroy();
  });
}
