<mvc:View
  height="100%"
  controllerName="sap.cdw.components.flowmonitor.controller.DataFlowDetails"
  xmlns="sap.m"
  xmlns:uxap="sap.uxap"
  xmlns:mvc="sap.ui.core.mvc"
  xmlns:layout="sap.ui.layout"
  xmlns:core="sap.ui.core"
  xmlns:t="sap.ui.table"
  xmlns:f="sap.f"
  xmlns:grid="sap.ui.layout.cssgrid"
  xmlns:unified="sap.ui.unified"
  xmlns:form="sap.ui.layout.form"
  xmlns:ac="sap.cdw.components.reuse.control.actionchecker"
  xmlns:cd="http://schemas.sap.com/sapui5/extension/sap.ui.core.CustomData/1"
>
  <ac:ActionChecker
    id="dfMonitorDetailsActionChecker"
    class="sapUiContentPadding"
    hanaState="{path:'circuitbreaker>/DataHANA', formatter:'.schedulingMenuFormatter'}"
    hanaProvisioningState="{path:'circuitbreaker>/DataHANAProvisioningState', formatter:'.schedulingMenuFormatter'}"
    spaceLocked="{spaceLockModel>/isSpaceLocked}"
    actionControlIds="detailSchedulingMenu,detailExecuteMenu,setToFailed,tfExecuteButton,stopdataflow,stoptransformationflow,resetWatermarkBtn,exeModeGrp,refeshTable"
    hiddenMode="false"
  >
  </ac:ActionChecker>
  <Page
    showHeader="false"
    id="pageContainer"
  >
    <content>
      <f:FlexibleColumnLayout
        id="detailsPaneContainer"
        backgroundDesign="Solid"
        layout="{layoutModel>/layout}"
        class="detailsPaneClass"
      >
        <f:beginColumnPages id="MasterPane">
          <Page
            id="masterRunsTable"
            backgroundDesign="List"
            showHeader="false"
            enableScrolling="false"
          >
            <uxap:ObjectPageLayout
              id="masterRunsTableOPL"
              showTitleInHeaderContent="true"
              headerContentPinnable="false"
              useIconTabBar="true"
              upperCaseAnchorBar="false"
              enableLazyLoading="true"
            >
              <uxap:headerTitle>
                <uxap:ObjectPageDynamicHeaderTitle>
                  <uxap:expandedHeading>
                    <Title
                      id="dataFlowName"
                      text="{dataflowModel>/name}"
                      tooltip="{dataflowModel>/name}"
                    />
                  </uxap:expandedHeading>
                  <uxap:snappedHeading>
                    <Title
                      text="{dataflowModel>/name}"
                      tooltip="{dataflowModel>/name}"
                    />
                  </uxap:snappedHeading>
                  <uxap:actions>
                    <Button
                      id="setToFailed"
                      text="{i18n>SET_TO_FAILED_TEXT}"
                      tooltip="{i18n>SET_TO_FAILED_TEXT}"
                      type="Transparent"
                      press="onSetToFailed"
                      enabled="{= ${/allowSetToFailed} === true}"
                      cd:actionId="monitoring/editor/setToFailed"
                      visible="{= ${/allowSetToFailed} === true}"
                    />
                    <MenuButton
                      text="{i18n>RUN_MENU_BUTTON}"
                      type="Transparent"
                      id="detailTfExecuteMenu"
                      visible="{= ${dataflowModel>/isTFRunMenuButtonVisible} === true ? true : false}"
                      cd:actionId="monitoring/editor/tfExecuteButton"
                      enabled="{= ${/isFlowRunning} === false ? true : false}"
                      >
                      <Menu>
                        <MenuItem
                          text="{i18n>RUN_LABEL}"
                          press="onTfExecute"
                        />
                        <MenuItem
                          text="{i18n>TF_RUN_WITH_SETTINGS}"
                          press="onSettingsOpen"
                        />
                      </Menu>
                    </MenuButton>
                    <Button
                      id="tfExecuteButton"
                      text="{i18n>RUN_LABEL}"
                      tooltip="{i18n>RUN}"
                      type="Transparent"
                      press="onTfExecute"
                      enabled="{= ${/isFlowRunning} === false ? true : false}"
                      cd:actionId="monitoring/editor/tfExecuteButton"
                      visible="{= ${dataflowModel>/isTFRunButtonVisible} === true ? true : false}"
                    />
                    <MenuButton
                      text="{i18n>RUN_MENU_BUTTON}"
                      type="Transparent"
                      id="detailExecuteMenu"
                      visible="{ parts: [{ path: 'dataflowModel>/isDataflow'}, { path: 'featureflags>/DWC_DUMMY_SPACE_PERMISSIONS'},
                      { path: 'privilege>/DWC_DATAINTEGRATION/update'}, {path : 'spaceLockModel>/isSpaceOpen'}, {path : 'privRemoteConnModel>/update'}], formatter:'.executeMenuVisibilityFormatter' }"
                      cd:actionId="monitoring/editor/executeDataflow"
                      enabled="{= ${/isFlowRunning} === false ? true : false}"
                      >
                      <Menu>
                        <MenuItem
                          text="{i18n>RUN_LABEL}"
                          press="onExecute"
                        />
                        <MenuItem
                          text="{i18n>CHECK_UP_LABEL}"
                          press="onCheckup"
                        />
                      </Menu>
                    </MenuButton>
                    <MenuButton
                      text="{i18n>SCHEDULE}"
                      type="Transparent"
                      id="detailSchedulingMenu"
                      visible="{= ${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} ? ${privilege>/DWC_DATAINTEGRATION/execute} === true &amp;&amp; ${spaceLockModel>/isSpaceOpen} === true : ${privRemoteConnModel>/execute} === true}"
                      enabled="{= ${dfSchedulingModel>/disableScheduling} === false}"
                      cd:actionId="monitoring/editor/detailsSchedule"
                    >
                      <Menu>
                        <MenuItem
                          text="{i18n>CREATE_SCHEDULE_LABEL}"
                          press="onCreateSchedule"
                          enabled="{= ${detailScheduleModel>/newSchedule} === true}"
                        />
                        <MenuItem
                          text="{i18n>EDIT_SCHEDULE_LABEL}"
                          press="onEditSchedule"
                          enabled="{= ${detailScheduleModel>/editSchedule} === true}"
                        />
                        <MenuItem
                          text="{i18n>DELETE_SCHEDULE_LABEL}"
                          press="onDeleteSchedule"
                          enabled="{= ${detailScheduleModel>/deleteSchedule} === true}"
                        />
                      </Menu>
                    </MenuButton>
                    <ToolbarSeparator class="sapUiTinyMarginBottom" />
                    <OverflowToolbarButton
                      id="refeshTable"
                      type="Transparent"
                      icon="sap-icon://refresh"
                      tooltip="{i18n>TEXT_REFRESH}"
                      text="{i18n>TEXT_REFRESH}"
                      press="onRefresh"
                      cd:actionId="monitoring/editor/refresh"
                    />
                    <ToolbarSeparator class="sapUiTinyMarginBottom" />
                    <OverflowToolbarButton
                      icon="sap-icon://sac/table-builder"
                      id="opendataflow"
                      tooltip="{i18n>OPEN_DATA_BUILDER}"
                      type="Transparent"
                      text="{i18n>OPEN_DATA_BUILDER}"
                      press="openFlowEditor"
                    />
                  </uxap:actions>
                </uxap:ObjectPageDynamicHeaderTitle>
              </uxap:headerTitle>
              <uxap:headerContent>
                <VBox>
                  <grid:CSSGrid>
                    <grid:customLayout>
                      <grid:GridResponsiveLayout layoutChange="onLayoutChange">
                        <grid:layoutS>
                          <grid:GridSettings
                            gridTemplateColumns="repeat(auto-fit, 12rem)"
                            gridAutoRows="auto-fit"
                            gridRowGap="1.5rem"
                            gridColumnGap="0.5rem"
                          >
                          </grid:GridSettings>
                        </grid:layoutS>
                        <grid:layout>
                          <grid:GridSettings
                            gridTemplateColumns="repeat(auto-fit, 18rem)"
                            gridAutoRows="auto-fit"
                            gridRowGap="1.5rem"
                            gridColumnGap="0.5rem"
                          >
                          </grid:GridSettings>
                        </grid:layout>
                      </grid:GridResponsiveLayout>
                    </grid:customLayout>
                    <VBox class="sapUiLargeMarginEnd sapUiSmallMarginBottom">
                      <Label
                        text="{i18n>STATUS}"
                        design="Bold"
                        class="sapUiTinyMarginBottom"
                      />
                      <ObjectStatus
                        id="idTFStatus"
                        text="{ parts: [{ path: '/lastInstanceStatus'}], formatter:'.statusTextFormatter' }"
                        tooltip="{ parts: [{ path: '/lastInstanceStatus'}], formatter:'.statusTextFormatter' }"
                        state="{ parts: [{ path: '/lastInstanceStatus'}], formatter:'.statusColorFormatter' }"
                      />
                    </VBox>
                    <VBox
                      class="sapUiLargeMarginEnd sapUiSmallMarginBottom"
                      visible="{=${dataflowModel>/isDataflow} === false ? true : false}"
                    >
                      <Label
                        text="{i18n>Runtime}"
                        design="Bold"
                        class="sapUiTinyMarginBottom"
                      />
                      <HBox class="sapUiTinyMarginBottom">
                        <ObjectStatus
                          id="runType"
                          text="{= ${dataflowModel>/isLargeSystemSpace} === undefined ? '' : (${dataflowModel>/isLargeSystemSpace} === true ? ${i18n>Apache_Spark} : ${i18n>SAP_HANA}) }"
                          />
                      </HBox>
                    </VBox>
                    <VBox class="sapUiLargeMarginEnd sapUiSmallMarginBottom">
                      <Label
                        text="{i18n>SCHEDULE}"
                        design="Bold"
                        class="sapUiTinyMarginBottom"
                      />
                      <HBox class="sapUiTinyMarginBottom">
                        <ObjectAttribute
                          title="{= ${featureflags>/DWCO_DI_MONITOR_UI_IMPROVEMENTS} ?  ${i18n>SCHEDULED_FREQUENCY}: ${i18n>REFRESH_FREQUENCY}}"
                          text="{detailScheduleModel>/refresh_frequency}"
                          active="{= ${detailScheduleModel>/refresh_frequency_active} === true &amp;&amp; ${detailScheduleModel>/isSchedulePaused} === false}"
                          press=".openSchedule($event)"
                        />
                      </HBox>
                      <HBox class="sapUiTinyMarginBottom">
                        <ObjectStatus
                          title="{i18n>LATEST_UPDATE}"
                          text="{detailScheduleModel>/formattedChangedAt}"
                        />
                      </HBox>
                      <HBox class="sapUiTinyMarginBottom">
                        <ObjectStatus
                          id="nextRun"
                          title="{i18n>NEXT_RUN}"
                          text="{detailScheduleModel>/formattedNextRun}"
                        />
                      </HBox>
                    </VBox>
                  </grid:CSSGrid>
                </VBox>
                <VBox
                  class="sapUiSmallMarginBottom"
                  visible="{= ${runtimeErrorModel>/runtimeErrorExists} === true}"
                >
                  <MessageStrip
                    id="runtimeEngineError"
                    class="sapUiTinyMargin"
                    showIcon="true"
                    type="Warning"
                    showCloseButton="false"
                    text="{i18n>DI_RUNTIME_ENGINE_ERROR}"
                  />
                </VBox>
              </uxap:headerContent>
              <uxap:sections>
                <uxap:ObjectPageSection
                  titleUppercase="false"
                  title="{i18n>LOGS}"
                  id="tfRunLogs"
                  showTitle="{= ${dataflowModel>/isDataflow} ? false : true}" >
                  <uxap:subSections>
                    <uxap:ObjectPageSubSection
                      showTitle="false"
                      mode="Expanded"
                    >
                      <uxap:blocks>
                        <Table
                          id="runsTable"
                          enableBusyIndicator="true"
                          mode="SingleSelectMaster"
                          itemPress="onTableItemPress"
                          items="{path:'/', templateShareable:false}"
                          showNoData="true"
                          noDataText="{i18n>NO_DATA}"
                          class="sapUiTinyMarginBottom sapUiTinyMarginBeginEnd noColumnBorder"
                          growing="true"
                          fixedLayout="false"
                        >
                          <headerToolbar>
                            <OverflowToolbar>
                              <Title
                                id="titleRunsList"
                                text="{i18n>RUNS}"
                                class="sapUiTinyMarginBegin"
                              />
                              <ToolbarSpacer />
                              <OverflowToolbarButton
                                icon="sap-icon://sort"
                                type="Transparent"
                                press="openLogsSortDialog"
                                id="runsTableSortBtn"
                              />
                              <OverflowToolbarButton
                                icon="sap-icon://filter"
                                type="Transparent"
                                press="openLogsFilterDialog"
                                id="runsTableFilterBtn"
                              />
                            </OverflowToolbar>
                          </headerToolbar>
                          <infoToolbar>
                            <OverflowToolbar
                              id="runsTable--FilterBar"
                              visible="false"
                            >
                              <Text text="{viewSettingsDialogModel>/runsTableFilterBarText}" />
                            </OverflowToolbar>
                          </infoToolbar>
                          <columns>
                            <Column id="idRunsStarttime">
                              <Label
                                text="{i18n>START}"
                                tooltip="{i18n>START}"
                              />
                            </Column>
                            <Column
                              id="idActivity"
                              visible="{= ${dataflowModel>/isDataflow} ? false : true}"
                            >
                              <Label
                                text="{i18n>ACTIVITY}"
                                tooltip="{i18n>ACTIVITY}"
                              />
                            </Column>
                            <Column id="idRunsStatus">
                              <Label
                                text="{i18n>STATUS}"
                                tooltip="{i18n>STATUS}"
                              />
                            </Column>
                          </columns>
                          <items>
                            <ColumnListItem
                              type="Navigation"
                              vAlign="Middle"
                            >
                              <cells>
                                <Text
                                  wrapping="false"
                                  text="{formattedStarted}"
                                  tooltip="{formattedStarted}"
                                ></Text>
                                <Text
                                  wrapping="false"
                                  text="{path:'activity', formatter:'.activityTextFormatter'}"
                                  tooltip="{activity}"
                                ></Text>
                                <ObjectStatus
                                  text="{ parts: [{ path: 'status'}, {path: 'subStatus'}], formatter:'.statusTextFormatter'}"
                                  state="{ parts: [{ path: 'status'}], formatter:'.statusColorFormatter' }"
                                  tooltip="{ parts: [{ path: 'status'}], formatter:'.statusColorFormatter' }"
                                  active="{=!!${subStatus}}"
                                  press=".openSubStatusDetail($event, ${subStatus}, ${applicationId})"
                                >
                                </ObjectStatus>
                              </cells>
                            </ColumnListItem>
                          </items>
                        </Table>

                      </uxap:blocks>
                    </uxap:ObjectPageSubSection>
                  </uxap:subSections>
                </uxap:ObjectPageSection>
                <uxap:ObjectPageSection
                  title="{i18n>BATCHES}"
                  id="batches"
                  visible="{ parts: [{path:'featureflags>/DWCO_TRF_BATCHES'}, {path: 'dataflowModel>/isLargeSystemSpace'}, {path: 'dataflowModel>/isDataflow'}], formatter:'.batchVisibilityFormatter' }"
                >
                  <uxap:subSections>
                    <uxap:ObjectPageSubSection>
                      <VBox
                        id="batchesTab">
                        <VBox
                          id="defineBatchesSection"
                          visible="{= ${tfHanaBatchesModel>/isTFPartitionedHANA} === true ? false : true}"
                          alignItems="Center">
                          <IllustratedMessage
                            id="illustratedMsgBatches"
                            title="{i18n>noBatchesTitle}"
                            description="{i18n>noBatchesDescription}"
                            illustrationType="sapIllus-NoActivities"
                          />
                          <Button
                            text="{i18n>DEFINE_BATCHES}"
                            id="defineBatchesBtn"
                            press="onEditBatches"
                          />
                        </VBox>
                        <VBox
                          id="showBatchesUI"
                          visible="{= ${tfHanaBatchesModel>/isTFPartitionedHANA} === true ? true : false}">
                          <VBox>
                            <MessageStrip
                              id="invalidBatchesStrip"
                              class="sapUiTinyMargin"
                              visible="{= ${tfHanaBatchesModel>/isPartitioningColumnValid} === false ? true : false}"
                              showIcon="true"
                              type="Error"
                              showCloseButton="false"
                              text="{i18n>EXECUTION_ERROR_INVALID_BATCH_COLUMN}">
                            </MessageStrip>
                          </VBox>
                          <VBox>
                            <OverflowToolbar
                              design="Transparent"
                              height="3rem"
                            >
                              <ToolbarSpacer />
                              <Button
                                text="{i18n>EDIT_BATCHES}"
                                id="editBatchesBtn"
                                type="Transparent"
                                press="onEditBatches"
                                visible="{= ${privilege>/DWC_DATABUILDER/read} === true &amp;&amp; ${privilege>/DWC_DATAINTEGRATION/update} &amp;&amp; ${spaceLockModel>/isSpaceLocked} !== true}"
                              >
                                <layoutData>
                                  <OverflowToolbarLayoutData priority="Low" />
                                </layoutData>
                              </Button>
                              <Button
                                text="{i18n>DELETE_BATCHES}"
                                id="deleteBatchesBtn"
                                type="Transparent"
                                press="onDeleteBatches"
                                visible="{= ${privilege>/DWC_DATABUILDER/read} === true &amp;&amp; ${privilege>/DWC_DATAINTEGRATION/update} &amp;&amp; ${spaceLockModel>/isSpaceLocked} !== true}"
                              >
                                <layoutData>
                                  <OverflowToolbarLayoutData priority="Low" />
                                </layoutData>
                              </Button>
                            </OverflowToolbar>
                          </VBox>
                          <VBox>
                            <HBox class="sapUiSmallMarginBegin sapUiSmallMarginTop">
                              <VBox justifyContent="Center" alignItems="Center" alignContent="Center">
                                <Label
                                  text="{i18n>BATCH_COLUMN}"
                                />
                              </VBox>
                              <VBox
                                class="sapUiSmallMarginBegin bottomPaddingHBox"
                                justifyContent="Center"
                                alignItems="Center"
                                alignContent="Center"
                              >
                                <Text
                                  id="batchColumnText"
                                  text="{tfHanaBatchesModel>/batchColumnDisplayName}"
                                />
                              </VBox>
                            </HBox>
                            <HBox class="sapUiSmallMarginBegin sapUiSmallMarginTop">
                              <VBox justifyContent="Center" alignItems="Center" alignContent="Center">
                                <Label
                                  text="{i18n>BATCH_SIZE}"
                                />
                              </VBox>
                              <VBox
                                class="sapUiSmallMarginBegin bottomPaddingHBox"
                                justifyContent="Center"
                                alignItems="Center"
                                alignContent="Center"
                              >
                                <Text
                                  id="batchSizeText"
                                  text="{tfHanaBatchesModel>/batchSize}"
                                />
                              </VBox>
                            </HBox>
                            <HBox
                              class="sapUiTinyMarginBegin sapUiTinyMarginTop"
                              visible="{= ${dataflowModel>/isDeltaEnabled} === true ? true : false}"
                            >
                              <VBox justifyContent="Center" alignItems="Center" alignContent="Center">
                                <CheckBox
                                  enabled="false"
                                  selected="{= ${tfHanaBatchesModel>/onlyInitialLoad} === true ? true : false}"
                                />
                              </VBox>
                              <VBox
                                justifyContent="Center"
                                alignItems="Center"
                                alignContent="Center"
                              >
                                <Text
                                  text="{i18n>onlyInitialLoad}">
                                </Text>
                              </VBox>
                            </HBox>
                          </VBox>
                        </VBox>
                      </VBox>
                      <uxap:blocks>
                      </uxap:blocks>
                    </uxap:ObjectPageSubSection>
                  </uxap:subSections>
                </uxap:ObjectPageSection>
                <uxap:ObjectPageSection
                title="{i18n>DELTA_CAPTURE_SETTINGS}"
                titleUppercase="false"
                showTitle="false"
                id="deltaSettings"
                visible="{= ${dataflowModel>/isDataflow} === true ? false : true}"
              >
                <uxap:subSections>
                  <uxap:ObjectPageSubSection>
                    <uxap:blocks>
                      <Table
                          id="deltaTableList"
                          enableBusyIndicator="true"
                          mode="SingleSelectMaster"
                          items="{path:'dataflowModel>/deltaSubscriptions', templateShareable:false}"
                          showNoData="true"
                          noDataText="{i18n>NO_DATA}"
                          class="sapUiTinyMarginBottom sapUiTinyMarginBeginEnd noColumnBorder"
                          growing="true"
                          fixedLayout="false"
                        >
                          <headerToolbar>
                            <OverflowToolbar>
                              <Title
                                id="sourceTableList"
                                text="{i18n>SOURCE_TABLE}"
                                class="sapUiTinyMarginBegin"
                              />
                              <ToolbarSpacer />
                              <Button
                                text="{i18n>RESET_WATERMARK}"
                                type="Transparent"
                                press="OnResetWatermark"
                                id="resetWatermarkBtn"
                                enabled="{ parts: [{path: 'dataflowModel>/deltaSubscriptions'}], formatter:'.isResetWatermarkEnabled' }"
                                cd:actionId="monitoring/editor/resetWatermarkButton"
                              />
                          </OverflowToolbar>
                        </headerToolbar>
                        <columns>
                          <Column id="idTechnicalName">
                            <Label
                              text="{i18n>TECHNICAL_NAME}"
                              tooltip="{i18n>TECHNICAL_NAME}"
                            />
                          </Column>
                          <Column id="idWatermark">
                            <Label
                              text="{i18n>WATERMARK}"
                              tooltip="{i18n>WATERMARK}"
                            />
                          </Column>
                        </columns>
                        <items>
                          <ColumnListItem
                            vAlign="Middle"
                          >
                            <cells>
                              <Text
                                wrapping="false"
                                text="{dataflowModel>deltaProviderName}"
                                tooltip="{dataflowModel>deltaProviderName}"
                              ></Text>
                              <Text
                                wrapping="false"
                                text="{dataflowModel>formattedHighWaterMark}"
                              ></Text>
                            </cells>
                          </ColumnListItem>
                        </items>
                      </Table>
                    </uxap:blocks>
                  </uxap:ObjectPageSubSection>
                </uxap:subSections>
                </uxap:ObjectPageSection>
                <uxap:ObjectPageSection
                title="{i18n>Settings}"
                titleUppercase="false"
                showTitle="false"
                id="tfSettings"
                visible="{= (${dataflowModel>/isLargeSystemSpace} &amp;&amp; ${dataflowModel>/isSparkSelectionEnabled}) ? true : (${dataflowModel>/isDataflow} === true || ${dataflowModel>/isLargeSystemSpace} === true ? false : ${privilege>/DWC_DATAINTEGRATION/update} === true &amp;&amp; ${spaceLockModel>/isSpaceOpen} === true) }"
              >
                <uxap:subSections>
                  <uxap:ObjectPageSubSection
                    titleUppercase="false"
                    title="{i18n>Settings}"
                    mode="Expanded"
                  >
                    <uxap:blocks>
                      <VBox visible="{= ${dataflowModel>/isLargeSystemSpace} === false }">
                        <form:SimpleForm
                          id="execModePanel"
                          layout="ResponsiveGridLayout"
                          labelSpanXL="3"
                          labelSpanL="3"
                          labelSpanM="2"
                          labelSpanS="12"
                          adjustLabelSpan="true"
                          emptySpanXL="4"
                          emptySpanL="4"
                          emptySpanM="4"
                          emptySpanS="0"
                          columnsXL="1"
                          columnsL="1"
                          columnsM="1"
                        >
                          <form:content>
                            <Label text="{i18n>RunMode}"  visible="{=${dataflowModel>/isLargeSystemSpace} === false }"/>
                            <RadioButtonGroup
                              id="exeModeGrp"
                              valueState="None"
                              selectedIndex="{dataflowModel>/executionMode}"
                              select="onExecutionModeChange"
                              cd:actionId="monitoring/editor/tfSettings"
                              visible="{=${dataflowModel>/isLargeSystemSpace} === false }"
                            >
                              <RadioButton
                                id="std"
                                text="{i18n>Standard_PO}"
                              />
                              <RadioButton
                                id="hlpm"
                                text="{i18n>HLMP_MO}"
                              />
                            </RadioButtonGroup>
                          </form:content>
                        </form:SimpleForm>
                      </VBox>
                    </uxap:blocks>
                      <!-- Settings Section starts-->
                      <VBox visible="{=${dataflowModel>/isLargeSystemSpace} === true &amp;&amp; ${dataflowModel>/isSparkSelectionEnabled} === true }">
                        <OverflowToolbar>
                          <Title
                            text="{i18n>apacheSparkSettings}"
                            class="sapUiTinyMarginBottom sapUiNoMarginBegin"
                            border-bottom="none"
                          />
                        </OverflowToolbar>
                        <RadioButtonGroup
                          id="spaceDefaultRadioGroup"
                          columns="1"
                        >
                          <RadioButton
                            id="spaceDefault"
                            text="{i18n>Use_Default}"
                            selected="{= ${dataflowModel>/sparkSelectedOption} === 'default'}"
                            select="onSpaceDefaultSelected"
                            enabled="{= ${dataflowModel>/isSpaceDefaultEnabled} }"
                          />
                        </RadioButtonGroup>
                        <form:Form editable="false">
                          <form:layout>
                            <form:ResponsiveGridLayout
                              labelSpanXL="4"
                              labelSpanL="3"
                              labelSpanM="4"
                              labelSpanS="12"
                              adjustLabelSpan="false"
                              emptySpanXL="0"
                              emptySpanL="4"
                              emptySpanM="0"
                              emptySpanS="0"
                              columnsXL="2"
                              columnsL="1"
                              columnsM="1"
                              singleContainerFullSize="false"
                            />
                          </form:layout>
                          <form:formContainers>
                            <form:FormContainer>
                              <form:formElements>
                                <form:FormElement label="{i18n>Application}">
                                  <form:fields>
                                    <Input
                                      id="applicationDefault"
                                      editable="false"
                                      value="{dataflowModel>/trfSparkSpaceDefault}"
                                      width="16.2rem"
                                    />
                                  </form:fields>
                                </form:FormElement>
                              </form:formElements>
                            </form:FormContainer>
                          </form:formContainers>
                        </form:Form>
                        <RadioButtonGroup
                          id="defineSettingsRadioGroup"
                          columns="1"
                        >
                          <RadioButton
                            id="defineSettings"
                            text="{i18n>New_Setting}"
                            selected="{= ${dataflowModel>/sparkSelectedOption} === 'newSettings'}"
                            select="onDefineSettingsSelected"
                            enabled="{= ${dataflowModel>/isSpaceSelectionEnabled} }"
                          />
                        </RadioButtonGroup>
                        <form:Form>
                          <form:layout>
                            <form:ResponsiveGridLayout
                              labelSpanXL="4"
                              labelSpanL="3"
                              labelSpanM="4"
                              labelSpanS="12"
                              adjustLabelSpan="false"
                              emptySpanXL="0"
                              emptySpanL="4"
                              emptySpanM="0"
                              emptySpanS="0"
                              columnsXL="2"
                              columnsL="1"
                              columnsM="1"
                              singleContainerFullSize="false"
                            />
                          </form:layout>
                          <form:formContainers>
                            <form:FormContainer>
                              <form:formElements>
                                <form:FormElement label="{i18n>Application}">
                                  <form:fields>
                                    <Select
                                      id="sparkSize"
                                      showSecondaryValues="true"
                                      items="{dataflowModel>/customSparkApplications}"
                                      change="onSparkApplicationChange"
                                      width="16rem"
                                      enabled="{dataflowModel>/isSparkSizeEnabled}"
                                      selectedKey="{dataflowModel>/selectedSparkApplication}"
                                    >
                                      <core:ListItem
                                        key="{dataflowModel>application}"
                                        text="{dataflowModel>application}"
                                        additionalText="{dataflowModel>description}"
                                      />
                                    </Select>
                                  </form:fields>
                                </form:FormElement>
                              </form:formElements>
                            </form:FormContainer>
                          </form:formContainers>
                        </form:Form>
                      </VBox>
                    </uxap:ObjectPageSubSection>
                  </uxap:subSections>
                </uxap:ObjectPageSection>
              </uxap:sections>
            </uxap:ObjectPageLayout>
          </Page>
        </f:beginColumnPages>
      </f:FlexibleColumnLayout>
    </content>
  </Page>
  <VBox>
    <mvc:XMLView
      id="detailSchedulingAuth"
      viewName="sap.cdw.components.taskscheduler.view.TaskScheduleAuth"
      visible="{= ${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} ? ${privilege>/DWC_DATAINTEGRATION/execute} === true &amp;&amp; ${spaceLockModel>/isSpaceOpen} === true : ${privRemoteConnModel>/execute} === true}"
    />
    <mvc:XMLView
      id="detailSchedulingDialog"
      viewName="sap.cdw.components.taskscheduler.view.TaskSchedule"
      visible="{= ${featureflags>/DWC_DUMMY_SPACE_PERMISSIONS} ? ${privilege>/DWC_DATAINTEGRATION/execute} === true &amp;&amp; ${spaceLockModel>/isSpaceOpen} === true : ${privRemoteConnModel>/execute} === true }"
    />
  </VBox>
</mvc:View>
