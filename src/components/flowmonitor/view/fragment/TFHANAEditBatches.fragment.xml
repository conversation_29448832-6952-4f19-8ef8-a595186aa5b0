<core:FragmentDefinition xmlns="sap.m" xmlns:core="sap.ui.core" xmlns:form="sap.ui.layout.form">
  <Dialog
    id="batchesDialog"
    class="sapUiContentPadding"
    title="{i18n>DEFINE_BATCHES}"
    contentWidth="600px"
    contentHeight="auto"
    draggable="true"
    resizable="false"
    afterClose="onAfterBatchesDialogClose"
  >
    <form:SimpleForm
      layout="ResponsiveGridLayout"
      columnsM="1"
      columnsL="1"
      editable="true"
    >
      <form:content>

        <HBox>
          <VBox justifyContent="Center" alignItems="Center" alignContent="Center">
            <Label text="{i18n>BATCH_SIZE}"
            />
          </VBox>
          <VBox
            class="sapUiSmallMarginBegin"
            justifyContent="Center"
            alignItems="Center"
            alignContent="Center"
          >
            <Input
              id="batchSizeInput"
              type="Number"
              value="{tfHanaBatchesModel>/inputBatchSize}"
              liveChange="onSizeValueLiveChange"
            />
          </VBox>
        </HBox>

        <CheckBox
          id="processInitialLoad"
          text="{i18n>onlyInitialLoad}"
          selected="{tfHanaBatchesModel>/isInitialLoadSelected}"
          visible="{= ${dataflowModel>/isDeltaEnabled} === true ? true : false}"
        />

        <Table
          id="columnsTable"
          items="{path: 'tfHanaBatchesModel>/partitioningColumns'}"
          class="sapUiTinyMarginBottom"
          mode="SingleSelectLeft"
          noDataText="{i18n>noColumnsText}"
        >
          <headerToolbar>
            <OverflowToolbar>
              <Title text="{tfHanaBatchesModel>/columnsText}" class="sapUiSmallMarginEnd"/>
              <ToolbarSpacer />
              <SearchField
                id="searchBatchColumns"
                width="30%"
                liveChange="onSearchBatchColumn"
              />
            </OverflowToolbar>
          </headerToolbar>
          <columns>
            <Column>
              <header>
                <Text text="{i18n>NAME}" />
              </header>
            </Column>
            <Column>
              <header>
                <Text text="{i18n>DATA_TYPE}" />
              </header>
            </Column>
          </columns>
          <items>
            <ColumnListItem vAlign="Middle">
              <cells>
                <HBox>
                  <VBox justifyContent="Center" alignItems="Center" alignContent="Center">
                    <core:Icon
                      src="sap-icon://primary-key"
                      class="sapUiTinyMarginEnd"
                      visible="{= ${tfHanaBatchesModel>key} === true ? true : false}"
                    />
                  </VBox>
                  <VBox
                    justifyContent="Center"
                    alignItems="Center"
                    alignContent="Center"
                  >
                    <Text
                      text="{ parts: [{path: 'tfHanaBatchesModel>column'}, {path: 'tfHanaBatchesModel>@EndUserText.label'}, {path: 'settings>/objectNameDisplay'}], formatter:'.columnTextFormatter' }"
                      wrapping="false"
                      tooltip= "{ parts: [{path: 'tfHanaBatchesModel>column'}, {path: 'tfHanaBatchesModel>@EndUserText.label'}, {path: 'settings>/objectNameDisplay'}], formatter:'.columnTextFormatter' }"
                    ></Text>
                  </VBox>
                </HBox>
                <Button
                  id="colDataTypeBtn"
                  text="{tfHanaBatchesModel>type}"
                  enabled="false"
                />
              </cells>
            </ColumnListItem>
          </items>
        </Table>

      </form:content>
    </form:SimpleForm>

    <!-- Buttons -->
    <buttons>
      <Button
        id="saveBatches"
        type="Emphasized"
        text="{i18n>@TXT_SAVE}"
        press="saveBatches"
        enabled="{ parts: [{path: 'tfHanaBatchesModel>/partitioningColumns'}], formatter:'.isSaveBatchesEnabled' }"
      />
      <Button
        id="cancelBatches"
        text="{i18n>TXT_CANCEL}"
        press="onEditBatchesClose"
      />
    </buttons>
  </Dialog>
</core:FragmentDefinition>
