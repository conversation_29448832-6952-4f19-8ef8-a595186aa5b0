
#XBCB: Breadcrumb
dataFlowMonitorBreadcrumb={spaceId}

#XFLD: Label for table column
SOURCE=Source
#XFLD: Label for table column
DATAFLOW=Data Flow
#XFLD: Label for table column
NAME=Name
#XFLD: Label for table column
LAST_STARTED=Last Started
#XFLD: Label for table column
INSTANCE_COUNT=Instance Count
#XFLD: Label for table column
START=Start
#XFLD: Label for table column
STOPPED=Stopped
#XFLD: Label for stopped failed
FAILLOCKED=Run Already in Progress
#XFLD: Label for table column
HANDLE_ID=Handle ID
#XFLD: Label for table column
STATUS=Status
#XFLD: Label for table column
FLOW_TYPE=Type
#XFLD: Flow type data flow text
FLOW_TYPE_DATA_FLOW=Data Flow
#XFLD: Flow type replication flow text
FLOW_TYPE_REPLICATION_FLOW=Replication Flow
#XFLD: Flow type transformation flow text
FLOW_TYPE_TRANSFORMATION_FLOW=Transformation Flow
#XFLD: Table header
RUNS=Runs
#XFLD: Table header
LAST_RUN_STATUS=Last Run Status
#XFLD: Table header
LAST_RUN_STATUS_HEADER=Status
#XFLD: Table header
SCHEDULE_OWNER=Schedule Owner
#XFLD: Label for frequency column
everyLabel=Every
#XFLD: Plural Recurrence text for Hour
hoursLabel=Hours
#XFLD: Plural Recurrence text for Day
daysLabel=Days
#XFLD: Plural Recurrence text for Month
monthsLabel=Months
#XFLD: Plural Recurrence text for Minutes
minutesLabel=Minutes
OPEN_BRACKET=(
CLOSE_BRACKET=)
#XFLD: Table header
OBJECT_TYPE=Object Type
#XFLD: Table header
LAST_RUN_DURATION=Last Run Duration
#XFLD: Table header
SCHEDULED_FREQUENCY=Scheduled Frequency
#XFLD: Table header
SCHEDULED_NEXT=Scheduled Next Run
#XFLD: Frequency type Text
DAILY=Days
#XFLD: Frequency type Text
MINUTES=Minutes
#XFLD: Frequency type Text
HOURLY=Hours
#XFLD: Frequency type Text
WEEKLY=Weeks
#XFLD: Frequency type Text
MONTHLY=Months
#XFLD: Header
RUN_DETAILS=Run Details
#XFLD: Header
DATAFLOW_RUNS=Data Flows ({0})
#XFLD: Button text
OPEN_DATAFLOW=Open in Data Flow Builder
#XFLD: Button text
OPEN_EDITOR=Open in Editor
#XFLD: Button text
OPEN_DATA_BUILDER=Open in Data Builder
#XFLD: Button text
STOP=Stop Run
#XFLD: Label text
RUN_START=Last Run Start
#XFLD: Label text
RUN_END=Last Run End
#XFLD: Label text
DURATION=Duration
#XFLD: Label text
MESSAGES=Message
#XFLD: Label text for Data flow detail action Bar
METRICS=Metrics
#XFLD: Label text
TIME_STAMP=Timestamp
#XFLD: Label text
CATEGORY=Category
#XFLD: Label text for the Metrices table header
LABEL=Operator Label
#XFLD: Label text for the Metrices table header
TYPE=Type
#XFLD: Label text for the Metrices table header
RECORDCOUNT=Record Count
#XFLD: Label text
RUN_END_DETAILS=End Date
#XFLD: Refresh tooltip
TEXT_REFRESH=Refresh
#XFLD: Settings tooltip
TEXT_SETTINGS=Select Columns
#XFLD: Label text
RUN_START_DETAILS=Start Date
#XFLD: stop data flow success message
SUCCESS_MSG=Data flow stopped
#XFLD: stop data flow error message
ERROR_MSG=Error while stopping the data flow
#XFLD: stop data flow error message
STOP_RUN_ERROR_MSG=Unable to stop the run. Looks like the data flow run has already been completed or failed. Please refresh the data flow status.
#XFLD: view Details link
VIEW_DETAILS=View Details
#XBTN: view Details link
VIEW_DETAILS_DIALOG_TITLE=Error message
#XFLD: view Details link
MSG_DATAFLOW_FAILED=Data Flow Failed.
#XFLD: view Details link
MSG_DATAFLOW_COMPLETED=Data Flow Completed.
#XFLD: status updated after sync message
MSG_DATAFLOW_STATUS_UPDATED=Run status updated to {0} based on the latest logs fetched.
#XFLD: status updated after sync message details
MSG_DETAILS_DATAFLOW_STATUS_UPDATED=The data flow run logs were outdated due to an issue. The run status is now updated based on the latest logs.
#XFLD: Label for no task log with running state title
ACTION_INFO=Action Info
#XFLD : Lable for TF analyzer dailog and menu button
TF_RUN_WITH_SETTINGS= Run with Support Settings
#XMSG: Info message for no Running state logId
NO_TASK_WITH_RUNNING_STATE=There is no tasklog with running state.
#XBTN: cancel button of select dataflow dialog
TXT_CANCEL=Cancel
#XMSG: error message for reading data from backend
BACKEND_ERROR=It looks like the data isn’t loading from the server at the moment. Try fetching the data again.
#XMSG: error message for reading data from runtime engine
RUNTIME_ENGINE_ERROR=The data flow runtime service is currently unavailable. Refresh the page to see the latest data.
#XMSG: error message for reading data from runtime engine
DI_RUNTIME_ENGINE_ERROR=The data flow runtime service is currently unavailable. Refresh the page to see the latest data.
#XMSG: Warning message when capacity unit is 0 for premium outbound
Premium_Outbound_Usage_Limit_Error_Msg_Warning=New replication flows to non-SAP target connection cannot be started because there is no remaining outbound volume available for this month.{0}{0}An administrator can increase the Premium Outbound blocks for this tenant, making additional outbound volume available for this month.
#XMSG: Error dialog message for capacity outbound
Premium_Outbound_Usage_Limit_Error_Msg_dialog_part1=The replication flow to this non-SAP target connection cannot be started
#XMSG
Premium_Outbound_Usage_Limit_Error_Msg_dialog_part2=because there is no outbound volume available for this month.
#XMSG
Premium_Outbound_Usage_Limit_Error_Msg_dialog_part3=An administrator can increase the Premium Outbound blocks for this
#XMSG
Premium_Outbound_Usage_Limit_Error_Msg_dialog_part4=tenant, making outbound volume available for this month.
#XMSG: Error dialog message for capacity outboundd
#XMSG Error dialog header
executeError=Error
#XMSG: success message after downloading diagnostics information for dataflow
MSG_DOWNLOAD_DETAILS_SUCCESS=Details downloaded
#XMSG: error message if downloading diagnostics fails
MSG_DOWNLOAD_DETAILS_ERROR=Failed to download details
#XMSG: error message for reading data from backend for details
BACKEND_ERROR_DETAILS=Complete run detail isn’t loading at the moment. Try to refresh.
#XMSG: replication flow already running message
RUN_ALREADY_IN_PROGRESS=Replication flow is already running.
#XMSG: error message for reading data from backend for messages api
BACKEND_ERROR_MESSAGES=There was an issue while fetching message details.
#XMSG: error message for reading metrices data from backend for metrices api
BACKEND_ERROR_METRICES=There was an issue while fetching metrics details.
#XMSG: error message for fetching space Id
SPACE_NOT_FOUND=There was an issue while fetching the space ID.
#XMSG: error message for histroical graph executions.
METRICS_HISTORY_NOT_FOUND=Metrics for historical runs of a data flow can’t be retrieved.
#XMSG: success message for set to failed action
SET_TO_FAILED_ACTION_SUCCESS=Object "{0}" latest task set to Failed.
#XMSG: error message for set to failed action
SET_TO_FAILED_ACTION_ERROR=Set to failed action for object "{0}" is end up with an error.
#XMSG: message for conflicting task
TASK_ALREADY_RUNNING=A conflicting task is already running for the object "{0}".
#XBTN: download run details
DOWNLOAD_RUN_DETAILS=Download Run Details
#XBTN: Schedule dropdown menu
SCHEDULE=Schedule
#XBTN: Drop down menu button to edit a schedule
EDIT_SCHEDULE_LABEL=Edit Schedule
#XBTN: Drop down menu button to clear a schedule
DELETE_SCHEDULE_LABEL=Delete Schedule
#XBTN: Drop down menu button to add new schedule
CREATE_SCHEDULE_LABEL=Create Schedule
#XBTN: Drop down menu button to assign schedule
ASSIGN_SCHEDULE_LABEL=Assign Schedule to Me
#XBTN: Drop down menu button to pause schedules
PAUSE_SCHEDULE_LABEL=Pause Schedule
#XBTN: Drop down menu button to resume schedules
RESUME_SCHEDULE_LABEL=Resume Schedule
#XBTN: Drop down menu button to execute dataflow with debug trace
CHECK_UP_LABEL=Start Run with Checkup
#XBTN: Drop down menu button to execute dataflow with debug trace
CHECK_UP=Run with Checkup

#XMSG: Info message in run with parameter dialog
RUN_WITH_PARAMETER_INFO_MESSAGE=Enter values if appropriate.
#XBTN: run button of run with parameter dialog
TXT_RUN=Run
#XMSG: Data flow run with parameter started
RUN_WITH_PARAMETER_SUCCESS_MESSAGE=Run started
#XMSG: Data flow run with parameter failed
RUN_WITH_PARAMETER_FAIL_MESSAGE=Run failed
#XMSG: Unable to fetch data flow definition error message
DATA_FLOW_FETCH_FAIL_MESSAGE=Unable to fetch data flow definition.
#XMSG: no parameter definition found in data flow JSON
DATA_FLOW_NO_PARAMETERS_MESSAGE=No parameters defined in data flow.
#XBTN: Set the datadlow execution to failed state
SET_TO_FAILED_TEXT=Set to Failed
#XFLD: Label for table column
FREQUENCY=Frequency
#XFLD: Label for table column
NEXT_RUN=Next Run
#XFLD: Header
SELECT_DATAFLOW=Select Data Flow
#XBTN: Next button
TXT_NEXT=Next
#XFLD: Label for table column
BUSINESS_NAME=Business Name
#XFLD: Label for table column
TECHNICAL_NAME=Technical Name

@btnContinue=Continue
@btnOkay=OK
@btnClose=Close
@titleExecuteBusy=Please wait.
@msgExecuteBusy=We are preparing your data for checkup
msgExecuteSuccess=Data flow checkup started
msgExecuteSuccessCheckup=Data flow checkup started for "{0}"
msgExecuteFail=Data flow checkup failed
@msgCheckupConfirmation=You will now run the data flow to perform a complete health analysis and generate detailed logs.
#XMSG: RowCount info popover text
ROWCOUNT_INFO_POPOVER_TEXT=We are unable to retrieve the record count because the data flow run has been optimized for better performance.
#XMSG: Metrics general info
METRICS_INFO_POPOVER_TEXT=It takes a while to load the metrics once the data flow run is completed. If metrics is not shown, please check after sometime.\n\n Note: Metrics are displayed only for source and target tables.
#XMSG: Dataflow run auto restarted info
DATAFLOW_RUN_RESTART_TEXT=The data flow run is being restarted, please wait.

#XFLD: Text for simulate run activity
TXT_SIMULATE_RUN = Simulate Run
#XFLD: Scheduled link
SCHEDULED=Scheduled
#XFLD: Paused link
PAUSED=Paused
#XFLD: Status text for Completed
TXT_COMPLETED=Completed
#XFLD: Status text for Failed
TXT_FAILED=Failed
#XFLD: Status text for Not started
TXT_NOT_STARTED=Not started
#XFLD: Status text for Suspended
TXT_SUSPENDED=Suspended
#XFLD: Status text for Suspending
TXT_SUSPENDING=Suspending
#XFLD: Status text for Paused
TXT_PAUSED=Paused
#XFLD: Status text for Pausing
TXT_PAUSING=Pausing
#XFLD: Status text for Pending
TXT_PENDING=Pending
#XFLD: Status text for Deleting
TXT_DELETING=Deleting
#XFLD: Status text for Deleted
TXT_DELETED=Deleted
#XFLD: Status text for Running
TXT_RUNNING=Running
#XFLD: Status text for Running for permanent runs
TXT_ACTIVE=Active
#XFLD: Status text for Initial running
TXT_INITIAL_RUNNING=Initial Running
#XFLD: Status text for Delta running
TXT_DELTA_RUNNING=Delta Running
#XFLD: Status text for Stopped
TXT_STOPPED=Stopped
#XFLD: Status text for Created
TXT_CREATED=Created
#XFLD: Status text for Partial running
TXT_PARTIAL_RUNNING=Partial running
#XFLD: Status text for Retrying
TXT_RETRYING=Retrying
#XFLD: Status text for Stopping
TXT_STOPPING=Stopping
#XFLD: Severity text for Error
TXT_RESTARTING=Restarting
#XFLD: Severity text for Error
TXT_ERROR=Error
#XFLD: Severity text for Warning
TXT_WARNING=Warning
#XFLD: Severity text for Info
TXT_INFO=Information
#XFLD: no data text for master detail table
NO_DATA=No Data
#XFLD: triggered by label
TRIGGERED_BY=Triggered by
#XFLD: run by label
RUN_BY = Run By
#XFLD run started By
RUN_STARTED_BY = Run Started By
#XFLD: execution type label
EXECUTION_TYPE=Execution Type
#XFLD: execution type status text
DIRECT=Direct
#XFLD: execution type status text
SIMPLE=Simple
#XFLD: status text
MANUAL=Manual
#XFLD: refresh frequency label
REFRESH_FREQUENCY=Refresh Frequency
#XFLD: Label for Object Header
REFRESH_FREQUENCY_NEW=Scheduled Frequency
#XFLD: refresh frequency label
REFRESH_FREQUENCY_LABEL=Frequency
#XLFD: Latest update label
LATEST_UPDATE=Latest Update
#XFLD: last transfer at label
LAST_TRANSFER_AT=Last Transfer At
#XFLD: empty cell
EMPTY_CELL=---
#XFLD: empty cell
EMPTY_CELL_NEW=
#XBTN: Run dropdown menu
RUN_DATAFLOW=Run Data Flow
##XBTN: Run dropdown menu item
RUN=Run
##XBTN: Run dropdown menu item
RUN_LABEL=Start Run
##XBTN: Start Run dropdown menu item
START_RUN=Start Run
##XBTN: Pause dropdown menu item
PAUSE_RUN=Pause Run
##XBTN: Stop Run dropdown menu item
STOP_RUN=Stop Run
##XBTN: Resume Run dropdown menu item
RESUME_RUN=Resume Run
#XMSG: direct execute success message
msgRunSuccess=Data flow run started
#XMSG: direct execute success message for data flow with name
dfRunSuccessMsg=Data flow run started for "{0}"
#XMSG: direct execute success message for Replication Flow
msgRFRunSuccess=Replication flow run started
#XMSG: direct execute failure message
msgRunFail=Data flow run failed
#XMSG: direct execute failure message for Replication Flow
msgRFRunFail=Replication flow run failed
#XFLD: Expired text
txtExpired=Expired
#XFLD: parent task chain label
parentChainLabel=Parent Task Chain
#XMSG: direct execute success message for Transformation Flow
msgTFRunSuccess=Transformation flow run started
#XMSG: direct execute success message for Transformation Flow with name
tfRunSuccessMsg=Transformation flow run started for "{0}"
#XMSG: direct execute failure message for Transformation Flow
msgTFRunFail=Transformation flow run failed

#XBUT: Icon tab all items label
ICON_TAB_ALL=All Items
#XBUT: Icon tab data flows label
ICON_TAB_DATA_FLOWS=Data Flows
#XBUT: Icon tab replication flows label
ICON_TAB_REPLICATION_FLOWS=Replication Flows
#XBUT: Icon tab transformation flows label
ICON_TAB_TRANSFORMATION_FLOWS=Transformation Flows
#XBUT: Segmented Button Text for ALL Flows
SEGMENTED_BUTTON_ALL=All Flows

#XRBL: Last hour label
LASTHOUR=Last Hour
#XRBL: Last 24 hours label
LAST24HOURS=Last 24 Hours
#XRBL: Last 1 month label
LASTMONTH=Last Month
#XBUT: Enter full screen for mid coloumn in detail view
ENTER_FULLSCREEN=Enter Full Screen Mode
#XBUT: Exit full screen for mid coloumn in detail view
EXIT_FULLSCREEN=Exit Full Screen Mode
#XBUT: Close mid column in detail view
CLOSE_SCREEN=Close Run Details

#XBTN: Replication Menu Actions
RUN_MENU_BUTTON=Run
#XBTN: Run dropdown menu
RUN_REPLICATIONFLOW=Run Replication Flow
#XBTN: Pause replication flow
PAUSE_TEXT=Pause
#XBTN: Stop replication flow
STOP_TEXT=Stop
#XFLD: Label text
DETAILS=Details
#XFLD: Label text
TARGET=Target
#XFLD: initial load progress label
INITIAL_LOAD_PROGRESS=Initial Load Progress
#XFLD: Label text
REPLICATION=Replication ({0})
#XFLD: Label text
OBJECT=Object ({0})
#XFLD: Label text
SOURCE_OBJECT=Source Object
#XFLD: Label text
TARGET_OBJECT=Target Object
#XFLD: Label text
LOAD_TYPE=Load Type
#XFLD: Label text
START_TIME=Start Time
#XFLD: Label text
END_TIME=End Time
#XFLD: Button text
OPEN_REPLICATION=Open in Replication Flow Editor
#XFLD Object Menu
TEXT_OBJECT=Object
#XFLD: Resume tooltip
OBJECT_RESUME=Resume Object
#XFLD: Pause tooltip
OBJECT_PAUSE=Pause Object
#XFLD: Restart tooltip
OBJECT_RESTART=Restart Object
#XFLD: Label text
#XFLD: Resume tooltip
TEXT_RESUME=Resume
#XFLD: Pause tooltip
TEXT_PAUSE=Pause
#XFLD: Restart tooltip
TEXT_RESTART=Restart
#XFLD: Label text
RUN_TIME_UPDATED=Run time updated
#XFLD: Label text
ALL_PARTITIONS=All Partitions
#XFLD: Label text
PARTITIONS=Partitions
#XFLD: Label text
INITIAL_LOAD_DURATION=Initial Load Duration
#XFLD: Label text
DELTA_LOAD_DURATION=Delta Load Duration
#XFLD: Label text
OPERATIONS=Operations
#XFLD: Label text
INTITIAL_LOAD_OPERATIONS=Initial Load Operations
#XFLD: Label text
DELTA_LOAD_OPERATIONS=Delta Load Operations
#XFLD: Label text
NO_OF_PARTITIONS=Number of Partitions
#XFLD: Label text
NO_OF_INITIAL_PARTITIONS=Number of Initial Partitions
#XFLD: Label text
NO_OF_DELTA_PARTITIONS=Number of Delta Partitions
#XFLD: Label text
ON_ERROR=On Error
#XFLD: Label text
INITIAL_LOAD_END_TIME=Initial Load End Time
#XFLD: Label text
DELTA_PARTITION=Object Thread Count for Delta Loads
#XFLD: Label text
STATE=State
#XMSG: replication run success message
msgReplicationRunSuccess=Replication flow run started
#XMSG: replication run failure message
msgReplicationRunFail=Replication flow run failed
#XFLD: load type text for Initial
TXT_INITIAL=Initial
#XFLD: load type text for Initial and delta
TXT_INITIAL_AND_DELTA=Initial and Delta
#XFLD: load type text for delta
DELTA_ONLY=Delta Only
#XFLD: run type label
RUN_TYPE=Run Type
#XFLD: real time text
TXT_REALTIME=Real Time
#XFLD: direct run text
TXT_DIRECT_RUN=Regular Run
#XFLD : simple run text
TXT_SIMPLE=Simple
#XFLD: real time text
TXT_SCHEDULED_RUN=Scheduled Run
#XFLD: Permanent text
TXT_PERMANENT=Permanent
#XFLD: end date text
TXT_END_DATE=End Date
#XFLD: Generate SQL Analyzer Plan file Text for Checkbox
GENERATE_SQL_ANALYZER=Generate SQL Analyzer Plan File
#XFLD: Description for Simulate Run
SIMULATE_RUN_DESCRIPTION=If you run with Simulate option, we will not save the changes to the target table.
#XFLD: Description for Generate SQL Analyzer Plan file
GENERATE_SQL_ANALYZER_DESCRIPTION=The Analyzer will require additional system resources to create the SQL Analyzer Plan file. Note that you must have installed the SQL Analyzer Tool for SAP HANA to visualize it.
#XMSG: replication flow pause success message
msgReplicationFlowPauseSuccess=The replication flow is paused.
#XMSG: replication flow pause error message
msgReplicationFlowPauseError=The replication flow cannot be paused.
#XMSG: replication flow pause error when atleast one dataset is restarting
PAUSE_RESTARTING_RF_LEVEL_VALIDATION_TEXT=The run cannot be paused because one or more object are restarting.{0} Please wait until these objects are running.
#XMSG: replication flow pause error when RF is scheduled
SCHEDULE_PAUSE_RF_LEVEL_VALIDATION_TEXT=The run cannot be paused because this would interfere with the created schedule.{0}{0}You can pause individual objects, or stop the run and pause the schedule.
#XMSG : replication flow stop error when any one of dataset is restarting
STOP_RESTARTING_RF_LEVEL_VALIDATION=The run cannot be stopped because one or more object are restarting.{0}Please wait until these objects are running.
#XSG : replcation dataset level actions are disabled when status is restarting
SCHEDULE_DATASET_LEVEL_VALIDATION_TEXT=All objects cannot be paused because this would interfere with the created schedule.{0}{0}You can stop the run and then pause schedule.
#XMSG: while creating replication flow.
CREATE_REPLICATION_TEXT=Create a Replication Flow.
#XMSG: while editing replication flow.
EDIT_REPLICATION_TEXT=Edit a Replication Flow.
#XMSG: while deleting replication flow.
DELETE_REPLICATION_TEXT=Delete a Replication Flow.
#XMSG: error message for metrics not found.
METRICS_NOT_FOUND=Metrics for replication object can’t be retrieved.
#XFLD: Label text
REPLICATION_OBJECT=Replication Object
#XMSG: replication object pause success message
msgReplicationOjectPauseSuccess=The replication object is paused.
#XMSG: replication object pause error message
msgReplicationObjectPauseError=The replication object cannot be paused.
#XMSG: replication object pause error message
msgScheduleCheckError=The schedule creatimg check failed.
#XMSG: replication object resume success message
msgReplicationOjectResumeSuccess=Replication object resumed
#XMSG: replication object resume error message
msgReplicationObjectResumeError=Replication object cannot be resumed
#XMSG: replication object restart success message
msgReplicationOjectRestartedSuccess=Replication object restarted
#XMSG: replication object restart error message
msgReplicationOjectRestartedError=Replication object cannot be restarted
#XMSG: replication object restart dialog
msgReplicationOjectRestartedWarning=Are you sure you want to restart replication object?
#XMSG: replication flow stop success message
msgReplicationFlowStopSuccess=Replication flow stopped
#XMSG: replication flow stop success message
msgReplicationFlowStopping=Stopping Replication flow
#XMSG: replication flow stop error message
msgReplicationFlowStopError=Replication flow cannot be stopped
#XMSG: replication flow pause ( flow level ) error message when dataset are in create/delete state
msgReplicationFlowPauseDeleteCreateError=The replication flow cannot be paused yet. Wait a few minutes and refresh the page. You can then pause the replication flow
#XMSG: error message for reading data from runtime engine
RF_RUNTIME_ENGINE_ERROR=The replication flow runtime service is currently unavailable. Refresh the page to see the latest data.
#XMSG: PremiumInbound Warning Message
PremiumInboundWarningMessage=Depending on the number of replication flows and the data volume to be replicated, the SAP HANA resources{0}required for replicating data through {1} may exceed the available capacity for your tenant.
#XMSG
PremiumInboundWarningMsg=Depending on the number of replication flows and the data volume to be replicated,{0}the SAP HANA resources required for replicating data through "{1}" may exceed the available capacity for your tenant.
#XMSG: replication flow error message if it contains invalid data types
invalidDatasetsMessage= The replication objects {0} contain a column with an invalid datatype. See SAP Note <a href="https://me.sap.com/notes/3569457" target="_blank">3569457</a> to solve the issue.
#XMSG: info message for ltf target to wait for merge job to complete
mergeJobPendingInfoLTF1=The data will be reflected in Local Table (File) target table only after the merge job is completed.
#XFLD: Run state for Running with errors
WITH_ERRORS=With Errors
#XFLD: Run state for Running with partial
WITH_PARTIAL=Partial
#XFLD: Run state for Running when one or more objects are failed
FAILED_OBJECTS=Failed Objects
#XFLD: Run state for Running when one or more objects are failed and paused
FAILED_PAUSED_OBJECTS=Failed / Paused Objects
#XFLD: Run state for Running when one or more objects are failed and retrying
FAILED_RETRYING_OBJECTS=Failed / Retrying Objects
#XFLD: Run state for Running when one or more objects are failed, paused and retrying
FAILED_PAUSED_RETRYING_OBJECTS=Failed / Paused / Retrying Objects
#XFLD: Run state for Running when one or more objects are paused
PAUSED_OBJECTS=Paused Objects
#XFLD: Run state for Running when one or more objects are paused and retrying
PAUSED_RETRYING_OBJECTS=Paused / Retrying Objects
#XFLD: Run state for Running when one or more objects are retrying
RETRYING_OBJECTS=Retrying Objects
#XMSG: replication flow stop dialog
msgReplicationFlowStopWarning=Are you sure you want to stop replication flow?
#XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorText=For replication flows that contain objects with load type "Initial and Delta", no schedule can be created.
XMSG: replication flow create schedule Error
msgReplicationFlowScheduleErrorTextUpdated=For replication flows that contain objects with load type "Initial and Delta" or "Delta Only", no schedule can be created.
#XFLD: Label text
RUNLOG=Run Log
#XFLD: Delta interval text
DELTA_INTERVAL_TXT=Delta Load Interval
#XFLD: Delta interval
DELTA_INTERVAL=1 hour
#XFLD: Delta interval
DELTA_REAL_TIME=Real-Time
#XFLD: Checkbox text for enabling/disabling tracelog
ENABLE_TRACELOG=Enable the tracelog
#XFLD: Status text for Paused
TXT_PAUSED_ERR=Your replication flow is running but the objects within it are paused.
#XFLD: Run state for Running with errors
WITH_ERRORS_ERR=Your replication flow is running but the objects within it have errors.
#XFLD: Run state for Running with partial
WITH_PARTIAL_ERR=Your replication flow is running, but the objects within it have a status other than "Running".
#XFLD: Run state description for Running with Failed, Paused and Retrying objects
RUNNING_FAILED_PAUSED_RETRYING_OBJECTS=The data is being loaded, but one or more objects have errors, and one or more objects have been paused by the user. Furthermore, one or more objects could not be loaded and a new attempt is planned.
#XFLD: Run state description for Running with Failed and Paused objects
RUNNING_FAILED_PAUSED_OBJECTS=The data is being loaded, but one or more objects have errors, and one or more objects have been paused by the user.
#XFLD: Run state description for Running with Failed and Retrying objects
RUNNING_FAILED_RETRYING_OBJECTS=The data is being loaded, but one or more objects have errors, and one or more objects could not be loaded and a new attempt is planned.
#XFLD: Run state description for Running with Failed objects
RUNNING_FAILED_OBJECTS=The data is being loaded, but one or more objects have errors.
#XFLD: Run state description for Running with Paused and Retrying objects
RUNNING_PAUSED_RETRYING_OBJECTS=The data is being loaded, but one or more objects have been paused by the user, and one or more objects could not be loaded and a new attempt is planned.
#XFLD: Run state description for Running with Paused objects
RUNNING_PAUSED_OBJECTS=The data is being loaded, but one or more objects have been paused by the user.
#XFLD: Run state description for Running with Retrying objects
RUNNING_RETRYING_OBJECTS=The data is being loaded, but one or more objects could not be loaded and a new attempt is planned.
#XFLD: Run state description for Active with Failed, Paused and Retrying objects
ACTIVE_FAILED_PAUSED_RETRYING_OBJECTS=The data will be loaded according to the schedule (delta load interval), but one or more objects have errors, and one or more objects have been paused by a user. Furthermore, one or more objects could not be loaded and a new attempt is planned.
#XFLD: Run state description for Active with Failed and Paused objects
ACTIVE_FAILED_PAUSED_OBJECTS=The data will be loaded according to the schedule (delta load interval), but one or more objects have errors, and one or more objects have been paused by a user.
#XFLD: Run state description for Active with Failed and Retrying objects
ACTIVE_FAILED_RETRYING_OBJECTS=The data will be loaded according to the schedule (delta load interval), but one or more objects have errors, and one or more objects could not be loaded and a new attempt is planned.
#XFLD: Run state description for Active with Failed objects
ACTIVE_FAILED_OBJECTS=The data will be loaded according to the schedule (delta load interval), but one or more objects have errors.
#XFLD: Run state description for Active with Paused and Retrying objects
ACTIVE_PAUSED_RETRYING_OBJECTS=The data will be loaded according to the schedule (delta load interval), but one or more objects has been paused by a user, and one or more objects could not be loaded and a new attempt is planned.
#XFLD: Run state description for Active with Paused objects
ACTIVE_PAUSED_OBJECTS=The data will be loaded according to the schedule (delta load interval), but one or more objects has been paused by a user.
#XFLD: Run state description for Active with Retrying objects
ACTIVE_RETRYING_OBJECTS=The data will be loaded according to the schedule (delta load interval), but one or more objects could not be loaded and a new attempt is planned.
#XFLD: Run state description for flow paused
TXT_PAUSED_DESCRIPTION=A user paused the run.
#XMSG: replication flow create schedule error
msgRFCreateScheduleError=For replication flows that contain objects with the load type "Initial and Delta", no schedule can be created.
#XFLD: Premium Outbound Volume Label
POVRF_LBL=Data Volume used.
#XFLD: Premium Outbound Date range
PODATE_LBL=Premium Outbound Integration
#XFLD : Placeholder for poi date range
LAST_POI_DAYS=Last 360 days
#XMSG: Stopping RF Message
ACTIVE_STOPPING=Stopping Replication Flow.
#XFLD: Title for parttition details
PARTITION_DETAILS=Partition Details
#XFLD: Title for partition details
PARTITION_INFORMATION=Partition Information
#XFLD: Label text for partition id
PARTITION_ID=Partition ID
#XFLD: Label text for transferMode
TRANSFER_MODE=Transfer Mode
#XFLD: Label text for correlation id
CORRELATION_ID=Correlation ID
#XFLD: Label text
ID=ID
#XFLD: Table header
RETRY_LOG=Retry Log
#XFLD: Label text for retry log table
REASON=Reason
#XFLD: Label text for retry log table
COUNT=Count
#XFLD: Label text for retry log table
STARTED_AT=Started At
#XFLD: Label text for retry log table
ENDED_AT=Ended At
#XFLD: Table header
ADDITIONAL_ERROR_LOG=Additional Error Log
#XFLD: Label text for transferring state
TXT_TRANSFERRING=Transferring
#XFLD: Label text for last data transferred at
LAST_DATA_TRANSFERRED_AT=Last Data Transfered At
#XFLD: Label text for retry information
RETRY_INFORMATION=Retry Information
#XFLD: Label text for first retry information
FIRST_RETRY_STARTED=First Retry Started
#XFLD: Label text for total retry count
TOTAL_RETRY_COUNT=Total Retry Count
#XFLD: Label text for latest retry reason
LASTEST_RETRY_REASON=Latest Retry
#XFLD: Label text for retry count for latest retry reason
RETRY_COUNT_FOR_LATEST_RETRY_REASON=Retry Count for Latest Retry Reason
#XFLD: Label text for object log
OBJECT_LOG=Object Log
#XFLD: Label text for object Details
OBJECT_DETAILS=Object Details
#XFLD: Label text for object Details
OBJECT_INFORMATION=Object Information
#XMSG: Info message in partition dialog
No_PARTITION_ID=Information at partition level is not available for replication flows that were created before 2024.21 and are in status Completed or Failed.
#XMSG: run log last row message for failed RF
MSG_REPLICATION_FAILED=Replication Flow Failed.
#XMSG: run log last row message for completed RF
MSG_REPLICATION_COMPLETED=Replication Flow Completed.

#inputParameters
#XFLD: Title for input parameters
InputParameters=Input Parameters
#XFLD: Label for input parameter value
VALUE=Value
#XTEXT: Message for run with input parameters dialogue
ipEnterValuesToRunDataflow=Enter values to run your data flow.
#XMSG: Unable to fetch data flow definition error message
dataFlowFetchFailMessage=Unable to fetch data flow definition.
#Transformation flow
#XFLD: Activity
ACTIVITY=Activity
#XMSG: cancel success message for Transformation Flow
msgTFCancelSuccess=Transformation flow cancel task started.
#XMSG: Cancel fail message for Transformation Flow
msgTFCancelFail=Transformation flow cancel task failed.
#XMSG: Cancel Run
TXT_CANCEL_RUN=Cancel Run
#XMSG: Metrics general info
TF_METRICS_INFO_POPOVER_TEXT=It takes a while to load the metrics once the transformation flow is completed.If metrics are not shown, please check after a while.
#XMSG: Message for starting mass deletion of schedules
massDeleteStarted=Deleting {0} schedules
#XMSG: Message for mass deletion failure
massDeleteFailure=Deleting schedules failed
#XMSG: Message for starting mass assign of schedules
massAssignStarted=Changing the owner of {0} schedules
#XMSG: Message for mass assign failure
massAssignFailure=Changing the owner of schedules failed
#XMSG: Message for starting mass pausing of schedules
massPauseStarted=Pausing {0} schedules
#XMSG: Message for mass pausing failure
massPauseFailure=Pausing schedules failed
#XMSG: Message for starting mass resuming of schedules
massResumeStarted=Resuming {0} schedules
#XMSG: Message for mass resuming failure
massResumeFailure=Resuming schedules failed

#~~~~~~~~~~~~~~~~~~~~~~~ Transformation flow execution messages ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message for starting execution
EXECUTION_START=Starting transformation flow run.
#XMSG: Message for successful execution
EXECUTION_SUCCESS=Transformation flow run completed.
#XMSG: Message for failed execution
EXECUTION_PROCESS_FAILED=Unable to run transformation flow.
#XMSG: Message for memory consumption of execution
EXECUTION_MEMORY_CONSUMPTION={0} MiB of peak memory used while running transformation flow.
#XMSG: Message for max resource limit on spark run
EXECUTION_SPARK_MAX_RESOURCE_LIMIT=The task runs in Apache Spark within the memory limits of {0} and {1}.
#XMSG: Message for locked space error
EXECUTION_ERROR_LOCKED_SPACE=Cannot run transformation flow. Space "{0}" is locked.
#XMSG: Message for execution timeout
EXECUTION_ERROR_TIMEOUT=A timeout error occurred. The transformation flow run has been running for {0} hours.
#XMSG: Message for not started asynchronous execution
EXECUTION_ERROR_ASYNC_NOT_STARTED=Cannot start transformation flow run. System is currently experiencing a high system load. Check the "System Monitor" for an overview of currently running tasks.
#XMSG: Message for cancelled execution
EXECUTION_ERROR_CANCELED=Transformation flow run was canceled.
#XMSG: Message for remote access in non BW Bridge space
EXECUTION_REMOTE_ACCESS_NON_BW_BRIDGE_SPACE=Access to a remote table in a space that is not a SAP BW bridge space is not supported. Check the Impact and Lineage Analysis.
#XMSG: Message for not supporting run with setting in spark run
EXECUTION_SIMULATE_RUN_UNSUPPORTED=Simulate run is not supported for the file space "{0}".
#XMSG: Message for insufficient privileges or consumption of view with DAC via dynamic SQL
EXECUTION_ERROR_INSUF_PRIV_OR_DAC_DYN_SQL=Your privileges may be insufficient. Open the Data Viewer of the view transform to see if you have the privileges required. If yes, a view consumed via dynamic SQL script may have data access control (DAC) applied to it.
#XMSG: Message for invalid batch column
EXECUTION_ERROR_INVALID_BATCH_COLUMN=The selected batch column is invalid. Select a column from the „View Transform“ operation with a supported data type.
#XMSG: Message for invalid batch size
EXECUTION_ERROR_INVALID_BATCH_SIZE=The entered batch size value is invalid. Enter a positive integer value.
#XMSG: Message used in SQL procedure for execution: roll back
EXECUTION_ROLLBACK=An error occurred. The transformation flow run has been stopped and changes have been rolled back.
#XMSG: Message used in SQL procedure for execution: preparation
EXECUTION_PREPARATION=Preparing to transform data.
#XMSG: Message used in SQL procedure for execution: reading distinct values of batch column
EXECUTION_READING_DISTINCT_VALUES_OF_BATCH_COLUMN=Reading distinct values of batch column from the "View Transform" operation.
#XMSG: Message used in SQL procedure for execution: reading distinct values of batch column completed
EXECUTION_READING_DISTINCT_VALUES_OF_BATCH_COLUMN_COMPLETED=Reading distinct values of batch column completed. Memory used: {0} MiB.
#XMSG: Message used in SQL procedure for execution: reading distinct values of batch column completed without memory
EXECUTION_READING_DISTINCT_VALUES_OF_BATCH_COLUMN_COMPLETED_WO_MEMORY=Reading distinct values of batch column completed.
#XMSG: Message used in SQL procedure for execution: disting values less than or equal to batch size
EXECUTION_CNT_DISTINCT_VALUES_LE_BATCH_SIZE=Batch column has {0} distinct values (excluding NULL). This is less than or equal to the batch size {1}. Consequently the transformation flow is not processed in batches.
#XMSG: Message used in SQL procedure for execution: truncating target table
EXECUTION_TRUNCATING_TARGET_TABLE=Truncating target table.
#XMSG: Message used in SQL procedure for execution: reading and writing of data
EXECUTION_READING_AND_WRITING_DATA=Reading data from the "View Transform" operation and writing it to the target table.
#XMSG: Message used in SQL procedure for execution: reading and writing of data of batch
EXECUTION_READING_AND_WRITING_DATA_BATCH=Reading data of batch "{0}" for values "{1}" <= "{2}" < "{3}" from the "View Transform" operation and writing it to the target table.
#XMSG: Message used in SQL procedure for execution: reading and writing of data of batch othersNotNull
EXECUTION_READING_AND_WRITING_DATA_BATCH_OTHERS_NOT_NULL=Reading data of batch "others not NULL" for values "{0}" < "{1}" OR "{0}" >= "{2}" from the "View Transform" operation and writing it to the target table.
#XMSG: Message used in SQL procedure for execution: reading and writing of data of batch othersNull
EXECUTION_READING_AND_WRITING_DATA_BATCH_OTHERS_NULL=Reading data of batch "others NULL" for values "{0}" is NULL from the "View Transform" operation and writing it to the target table.
#XMSG: Message used in SQL procedure for generating plan visualization
EXECUTION_GET_PLANVIZ=SQL Analyzer plan file for the transformation flow is created and can be downloaded.
#XMSG: Message used in SQL procedure for generating plan visualization for batch
EXECUTION_GET_PLANVIZ_BATCH=SQL Analyzer plan file for the batch is created and can be downloaded.
#XMSG: Message used in SQL procedure for generating plan visualization for reading distinct values of batch column
EXECUTION_GET_PLANVIZ_READ_DISTINCT_VAL_BATCH_COLUMN=SQL Analyzer plan file for reading distinct values of batch column is created and can be downloaded.
#XMSG: Message used in SQL procedure for generating explain plan
EXECUTION_GET_EXPLAIN_PLAN=Explain plan for the transformation flow is created and can be downloaded.
#XMSG: Message used in SQL procedure for generating plan visualization error
EXECUTION_GET_PLANVIZ_ERROR=SQL Analyzer plan file for transformation flow could not be created. Try again later.
#XMSG: Message used in SQL procedure for generating explain plan error
EXECUTION_GET_EXPLAIN_PLAN_ERROR=Explain plan for transformation flow could not be created. Try again later.
#XMSG: Message for unsupported planViz generation
EXECUTION_GET_PLANVIZ_UNSUPPORTED=SQL Analyzer plan generation is only allowed with direct execution.
#XMSG: Message for unsupported explain plan generation
EXECUTION_GET_EXPLAIN_PLAN_UNSUPPORTED=Explain plan generation is only allowed with direct execution and within simulate run.
#XMSG: Message for no batch processing in case of explain plan generation
EXECUTION_GET_EXPLAIN_PLAN_NO_BATCHES=Generating an Explain Plan prevents transformation flows from being processed in batches.
#XMSG: Message used in SQL procedure for execution: record count and memory usage
EXECUTION_RECORD_COUNT_SUCCESS={0} records written to the target table.
#XMSG: Message used in SQL procedure for execution: record count for batch including memory usage
EXECUTION_RECORD_COUNT_SUCCESS_BATCH={0} records written to the target table for the batch. Memory used: {1} MiB.
#XMSG: Message used in SQL procedure for execution: record count for batch without memory usage
EXECUTION_RECORD_COUNT_SUCCESS_BATCH_WO_MEMORY={0} records written to the target table for the batch.
#XMSG: Message used in SQL procedure for execution: total record count for batches
EXECUTION_RECORD_COUNT_SUCCESS_BATCH_TOTAL={0} records written to the target table in {1} batches.
#XMSG: Message used in SQL procedure for execution: connection details
EXECUTION_CONNECTION_DETAILS=Connection details.
#XMSG: Message used in SQL procedure for execution: no new changes
EXECUTION_NO_NEW_CHANGES=There are no new changes detected in the source delta table. Consequently, no records have been processed, as no changes have been exposed by the source delta table.
#XMSG: Message used in SQL procedure for execution: finalizing execution
EXECUTION_FINALIZING=Finalizing transformation flow run.
#XMSG: Message used in SQL procedure for start of simulate run: start execution
EXECUTION_SIMULATE_RUN_START=Simulate run for transformation flow has started. No changes will be saved in the target table.
#XMSG: Message used in SQL procedure for end of simulate run: finalizing execution
EXECUTION_SIMULATE_RUN_COMPLETED=Simulate run is completed. No changes were saved in the target table.
#XMSG: Message for using view with DAC
EXECUTION_ERROR_USING_VIEW_W_DAC=Cannot run the transformation flow. A data access control has been applied to a view that is used (directly or indirectly) by the transformation flow (view "{0}").
#XMSG: Message for using external view with DAC
EXECUTION_ERROR_USING_EXTERNAL_VIEW_W_DAC=Cannot run the transformation flow. A data access control has been applied to a view that is used (directly or indirectly) by the transformation flow. This view belongs to a different space.
#XMSG: Message for calculation of watermark
EXECUTION_CALCULATING_WATERMARK=Calculating watermark.
#XMSG: Message for starting to reset watermark
RESETTING_WATERMARK_START=Starting to reset watermark.
#XMSG: Message for sucessfull reset of watermark
RESETTING_WATERMARK_SUCCESS=Watermark reset.
#XMSG: Message for failed resetting of watermark
RESETTING_WATERMARK_FAILED=Unable to reset watermark.

#~~~~~~~~~~~~~~~~~~~~~~~ Transformation flow cancel run dialog ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#RBTN: Cancel run with rollback button
TXT_CANCEL_WITH_ROLLBACK=Cancel run with Rollback
#RBTN: Cancel run without rollback button
TXT_CANCEL_WITHOUT_ROLLBACK=Cancel run without Rollback
#XMSG: Cancel run with rollback message
TXT_CANCEL_WITH_ROLLBACK_DESC=The run will stop and what has already been processed will be canceled, including parallel changes from other apps. The data is restored to the version it had when the run started.
#XMSG: Cancel run without rollback message
TXT_CANCEL_WITHOUT_ROLLBACK_DESC=The run will stop where it is and what has already been processed will be kept, including parallel changes from other apps. Data might be altered. If you restart the run later, it will resume from where it has stopped.

#~~~~~~~~~~~~~~~~~~~~~~~ Transformation flow cancel execution messages ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XMSG: Message to indicate triggering of cancelation of execution task
CANCEL_TRIGGERED=Canceling transformation flow run (task {0}). There may be a delay until the run is canceled.
#XMSG: Message when transformation flow execution is canceled and rollback is triggered
ROLLBACK_TRIGGERED=Attempting to rollback the target table to version before task {0} was started.
#XMSG: Message when cancel run is waiting for wrapper sql procedure to get closed
WAITING_FOR_SPARK_RUN_TO_CANCEL=Waiting for {0} second(s) before checking the status of task {1}.
#XMSG: Message when failed to cancel transformation flow execution
SPARK_RUN_NOT_CANCELLED=Unable to rollback the changes done by task {0}.
#XMSG: Message when transformation flow execution is not active
CANCEL_NO_ACTIVE_TASK=Cannot cancel transformation flow run (task {0}). The transformation flow is not running.
#XMSG: Message used when cancelation task fails
CANCEL_FAILED=Unable to cancel transformation flow run.
#XMSG: Message to show in execution task the task log id of cancelation task
EXECUTION_IS_CANCELED=Canceling transformation flow run using task {0}.

#~~~~~~~~~~~~~~~~~~~~~~~ HANA Resource Limit error messages uses_in tasks ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
##XFLD: Message for out of memory
TASK_HANA_OUT_OF_MEMORY=The task failed because of an out of memory error on the SAP HANA database.
##XFLD: Message for admission control rejection
TASK_HANA_ADMISSION_CTRL_REJECTION=The task failed because of an SAP HANA Admission Control Rejection.
##XFLD: Message for HANA back pressure / rate limit of HANA client
TASK_HANA_CLIENT_RATE_LIMIT_REJECTION=The task failed because of too many active SAP HANA connections.

#XBTN: Download additional details button
DOWNLOAD_ADTNL_DETAILS=Download Additional Details
#XMSG: Download completed
downloadStarted=Download Started
#XMSG: Error while downloading content
errorInDownload=An error occurred while downloading.

#XMSG: Batches
BATCHES=Batches
#XMSG: No batches illustrated message title
noBatchesTitle=No batches have been defined yet
#XMSG: No batches illustrated message description
noBatchesDescription=Create batches by specifying criteria to break larger data volumes into smaller, more manageable parts.
#XBTN: Define batches Button
DEFINE_BATCHES=Define Batches
#XBTN: Edit batches Button
EDIT_BATCHES=Edit
#XBTN: Delete batches Button
DELETE_BATCHES=Delete
#XFLD: Label for batch column
BATCH_COLUMN=Column:
#XFLD: Label for batch size
BATCH_SIZE=Size:
#XFLD: Only initial load processing text
onlyInitialLoad=Only Process Initial Load in Batches
#XMSG: Delete confirmation text
deleteConfirmationTxt=Do you want to delete batch data?
#XMSG: Batches data deletion
batchesDataDeleted=Batch data is deleted
#XMSG: Batches data saved
batchesDataSaved=Batch data is saved
#XFLD: Column for data type
DATA_TYPE=Data Type
#XMSG: Loading batch data
loadingBatchData=Loading batch data
#XMSG: No column selected
noColumnSelected=Select a column
#XMSG: Empty input for batch size
batchSizeInvalid=Enter a batch size
#XMSG: Batch size is set to 0
zeroBatchSize=Batch size must be greater than 0.
#XMSG: Columns header text
columnsText=Columns ({0})
#XMSG: Unsaved data
unsavedData=Your batch data settings are not saved.
#XMSG: No columns available for batch processing
noColumnsText=No column has a valid data type for batch processing
#XMSG: Unable to get batch processing information
getBatchesFailed=Unable to fetch batch data
#XMSG: Invalid value for size on live change
invalidSizeValueLive=Enter a numerical value
#XMSG: 0 value for size on live change
zeroSizeValueLive=Size must be greater than 0
#XBTN: Back button for close of edit batches dialog
back=Back
#XTIT: Confirmation title
confirmation=Confirmation

#XMSG: Delta Capture Settings
DELTA_CAPTURE_SETTINGS=Delta Capture Settings
#XTIT: Source Table
SOURCE_TABLE=Source Table
#XBTN: Reset Watermark
RESET_WATERMARK=Reset Watermark
#XFLD : Watermark
WATERMARK=Watermark
#XTIT : Logs
LOGS=Logs
#XMSG
resetWatermarkWarningMsg=If you reset the watermark, the system will \ntransfer all data to the target table the next \ntime transformation flow runs. \nReset watermark?
#XBTN: Reset
Reset=Reset
#XMSG: Reset watermark task started
resetWatermarkSuccess=Reset watermark task started
#XMSG: Reset watermark task failed
resetWatermarkFail=Reset watermark task failed
#XMSG: Transformation flow is not delta enabled
trFlowNotDeltaEnabled=Transformation flow is not delta enabled
#XMSG : The transformation flow is currently not configured to load delta changes from any source tables.
illustratedMsg=The transformation flow is currently not configured to load delta changes from any source tables.

#XFLD: Label for execution mode
RunMode=Run Mode
#XFLD: Label for Standard
Standard_PO=Performance-Optimized (Recommended)
#XFLD: Label for Hana low memory processing
HLMP_MO=Memory-Optimized
#XTIT : Settings
Settings=Settings
#XMSG: Save settings success
saveExecutionModeSuccess=Run Mode changed.
#XMSG: Save settings failure
saveExecutionModeFailed=Run Mode change failed.
#XMSG: Failed to fetch runtime settings of transformation flow
fetchSettingsFailed=Failed to fetch runtime settings of transformation flow.
#XFLD: Label for Runtime information
Runtime=Runtime
#XFLD: Label for Apache Spark Runtime
Apache_Spark=Apache Spark
#XFLD: Label for SAP HANA Runtime
SAP_HANA=SAP HANA

#XFLD: Button text
Download = Download
#XMSG: failed to fetch explain plan details
FetchExplainPlanDetailsFailed = Failed to fetch explain plan details for transformation flow.
#XFLD: Text for checkbox to generate explain plan
GENERATE_EXPLAIN_PLAN = Generate Explain Plan
#XFLD: Button text
expandAll = Expand
#XFLD: Button text
collapseAll = Collapse
#XFLD: Label for OperatorName
OperatorName=OPERATOR_NAME
#XFLD: Label for OperatorDetails
OperatorDetails=OPERATOR_DETAILS
#XFLD: Label for OperatorProperties
OperatorProperties=OPERATOR_PROPERTIES
#XFLD: Label for ExecutionEngine
ExecutionEngine=EXECUTION_ENGINE
#XFLD: Label for DatabaseName
DatabaseName=DATABASE_NAME
#XFLD: Label for SchemaName
SchemaName=SCHEMA_NAME
#XFLD: Label for TableName
TableName=TABLE_NAME
#XFLD: Label for TableType
TableType=TABLE_TYPE
#XFLD: Label for TableSize
TableSize=TABLE_SIZE
#XFLD: Label for Output Size
OutputSize=OUTPUT_SIZE
#XFLD: Label for Subtree Cost
SubtreeCost=SUBTREE_COST
#XMSG: Message for explain plan with more than 1000 rows
ExplainPlanMoreThan1000Rows=The generated Explain Plan can’t be displayed as it contains more than 1000 rows. Download the file to view all the rows.
#XMSG: Error Message for error in downloading explain plan
ExplainPlanFetchError=An error occured while fetching explain plan details.
#XFLD: Label for default settings
Use_Default=Use Default
#XFLD: Label to define new setting for the flow
New_Setting=Define New Setting for This Flow
#XBTN: Application dropdown menu
Application=Application
#XMSG: Save spark settings success
saveSparkSettingsSuccess=Apache Spark Application Settings saved successfully.
#XMSG: Failed to retrieve Apache Spark settings for the transformation flow
fetchSparkSettingsFailed=Failed to retrieve Apache Spark settings for the transformation flow.
#XMSG: Failed to load resources
fetchResourcesFailed=Failed to load resources.
#XTIT : Apache Spark Settings
apacheSparkSettings=Apache Spark Settings
#XMSG: Message when input parameters values are not available during runtime
EXECUTION_MISSING_PARAMETERS=The transformation flow run failed because the input parameters values for "{0}" are not available during runtime.
#XMSG: Message when input parameters values are not valid
EXECUTION_INVALID_PARAMETERS=The transformation flow run failed because the input parameter value for parameter "{0}" is not valid.
#XMSG: Message for run with input parameters dialogue for Transformation Flow
EnterValuesToRunTransformationflow=Enter Parameter values to run your transformation flow.
#XTIT Title for  run with settings and input parameters dialogue for Transformation Flow
RunSettings=Run Settings
#XFLD: Cancelled spark run tooltip
CANCELLED_SPARK_RUN_TOOLTIP=This task run was cancelled after its start. Data now reflects your cancel run choice, with or without rollback.

#~~~ Email Notification ~~~~~~~~~~~~~~~~~~
#XFLD
@emailNotifications=Email Notifications
#XFLD
@recipientEmailAddr=Recepient List
#XFLD
@emailSubject=Email Subject
#XFLD
@emailMessage=Email Message
#XFLD
@selectEmailRecipientDialogTitle=Select Email Recipients
#XFLD
@tenantMembers=Tenant members
#XFLD
@others=Others({0})
#XFLD
@selectedEmailAddress=Selected Recipients
#XBUT
@add=Add
@placeholder=Placeholder
@description=Description
#XTOL
@copyText=Copy text
#XTOL
@replicationFlowDetailsPlaceholder=Placeholders for replication flow details
#XMSG
@placeholderCopied=Placeholder is copied
#XMSG
@invalidEmailInfo=Enter correct email address
#XMSG
@maxMembersAlreadyAdded=You have already added the maximum of 20 members
#XTOL
@enterEmailAddress=Enter user email address
#XFLD
@nsOFF=Do not send any notifications
#XFLD
@nsFAILED=Send email notification when a replication flow object has failed
#XFLD
@phreplicationFlowName=Technical name of the replication flow
#XFLD
@phLogId=Log ID of the replication flow
#XFLD
@phUser=User who ran the replication flow
#XFLD
@phLogUILink=Link to the monitoring details of the replication flow
#XFLD
@phSpaceName=Name of the space
#XMSG
@emailFormatError=Invalid email format
#XMSG
@emailFormatErrorInListText=Invalid email format entered in non-tenant members list.
#XBUT
@copyPlaceholder=Placeholder Info
#XMSG
@emailSubjectTemplateText=Notification for Replication Flow: $$replicationFlowName$$ - Space: $$spaceId$$
#XMSG
@emailUpdateError=Error in updating Email Notification list
#XMSG
@emailLoadError=Error in loading Email Notification list
#XMSG
@emailMessageTemplateText=Hello,\n\nYour replication flow labeled $$replicationFlowName$$ has one or more datasets with failed status.\n\nHere are other details about the replication flow:\n- Replication Flow technical name: $$replicationFlowName$$\n- Log ID of the replication flow run: $$logId$$\n- User who ran the replication flow: $$user$$\n- Link to the detailed log of the replication flow: $$uiLink$$\n- Name of the space: $$spaceId$$
@deleteEmailRecepient=Delete Recipient
#XMSG
@tenantOwnerDomainMatchErrorText=The email address domain does not match the tenant owner domain: {0}
#XMSG
@totalEmailIdLimitInfoText=You can select up to 20 email recipients including tenant member users and other recipients.
#XMSG
@emailDomainInfoText=Only email addresses with domain: {0} are accepted.
#XMSG
@duplicateEmailErrorText=There are duplicate email recipients in the list.
#XFLD
@notificationSettings=Notification Settings
#XMSG
@EmailNotificationSuccess=Configuration of runtime email notifications is saved.
#XMSG
@emailSubjectEmptyError=Enter an email subject
#XMSG
@emailMessageEmptyError=Enter an email message
#XBUT
TXT_SELECT=Select
@runtimeEmail=Runtime Email Notification
#XMSG
noTeamPrivilegeTxt=You do not have permission to see a list of tenant members. Use the Others tab to add email recipients manually.
#XMSG
@emailRequired=You must enter at least one email recipient.
#XBTN:
@TXT_CANCEL=Cancel
#XBTN
@TXT_SAVE=Save
#BTN
@TXT_CLOSE=Close
