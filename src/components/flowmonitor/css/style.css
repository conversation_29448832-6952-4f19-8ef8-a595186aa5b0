.masterTitle {
  overflow-wrap: anywhere;
}

.infoIconClass {
  padding-left: 3px;
}

.premiumInboundHDLFSourceWarning{
  width: 140% !important;
}

.infoIconPopoverClass {
  padding: 5px;
}

.sapFDynamicPage.detailHeight {
  height: calc(100% - 140px);
}

.metricInfoicon {
  top: -3.5rem ;
  left: 4.5rem ;
}

.metricDetailTable {
  top: -3rem;
}

.diActionCheckerClass {
  position: relative !important;
  height: 100%;
}

.wordBreak{
  word-break: break-word;
}

.messagesTableClass {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.stackedBarMicroChartClass {
  height: 1.2rem !important;
  padding-top:0px !important;
  width:11rem !important
}

.dataflowMonitorPadding{
  padding-top:16px !important;
}

.povMessageInfo {
  align-items: center;
}

/* .povMessageMonInfo {
  position: relative;
}

.povMessageMonInfo .povMessageInfoDetailBtn {
  position: absolute;
  top: -0.4rem;
} */

@media screen and (max-width:599px){
  .flowTablePadding{
    padding-right:0px !important;
    padding-left:0px !important;
  }
}


@media screen and (min-width:600px) and (max-width:1080px){
  .flowTablePadding{
    padding-right:16px !important;
    padding-left:16px !important;
  }
}


@media screen and (min-width:1081px) {
  .flowTablePadding {
    padding-right:30px !important;
    padding-left:30px !important;
  }
}

.detailsPaneClass .sapFFCLColumnSeparator {
  visibility: visible !important;
  position: static !important;
}

.flowMonitorStatusTruncate {
  display: inline !important;
}

.sapUiResponsiveContentPadding.explainPlanDialogTable .sapMDialogSection.sapUiScrollDelegate .sapMDialogScroll {
  height: 100%;
}

.customTabClass .sapMITBContent,
.objectDetailsTreeItems .sapMTreeItemBaseChildren {
  background-color: var(--sapGroup_ContentBackground);
}

.noTopPadding {
  padding-top: 0px !important;
}

.bottomPaddingHBox {
  padding-bottom: 6px
}
