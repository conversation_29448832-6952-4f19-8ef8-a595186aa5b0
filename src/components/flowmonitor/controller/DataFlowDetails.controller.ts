/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { ShellNavigationService } from "@sap/orca-shell";
import moment from "moment-timezone";
import { isDiMonitorImprovementsEnabled } from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import { getResourcesSpaces } from "../../managespaces/model/SpaceAPIProxy";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { ContentType, DataType, HttpMethod, ServiceCall } from "../../reuse/utility/ServiceCall";
import { Logger } from "../../reuse/utility/UIHelper";
import { ShellContainer } from "../../shell/utility/Container";
import { User } from "../../shell/utility/User";
import { setHelpScreenId } from "../../shell/utility/WebAssistantHelper";
import { Activity, ApplicationId } from "../../tasklog/utility/Constants";
import { ITTaskScheduleController, ITaskScheduleRequest } from "../../taskscheduler/controller/TaskSchedule.controller";
import { getTaskScheduer, openSchedulePopover, recordAction } from "../../taskscheduler/utility/ScheduleUtil";
import { IParameterTF } from "../../transformationflow/controller/TFModeler.controller";
import { ObjectNameDisplay, PreferenceChanges } from "../../userSettings/utility/Constants";
import Utils, {
  IDeltaSubscriptions,
  IEditBatchesPayload,
  IFlowDetailsViewData,
  IRuntimeSettings,
  LAYOUTMODEL,
  RUN_STATUS,
} from "../util";
import { activityTextFormatter } from "../utility/commonFormatters";
import { DataFlowDetailsPanelClass } from "./DataFlowDetailsPanel.controller";
import {
  DFExecuteMessages,
  DataFlowMonitorBase,
  DataFlowMonitorBaseClass,
  IParameter,
} from "./DataFlowMonitorBase.controller";
import { TransformationFlowDetailsPanelClass } from "./TransformationFlowDetailsPanel.controller";

export class DataFlowDetailsClass extends DataFlowMonitorBaseClass {
  private dataflowDetailsPanel: sap.ui.core.mvc.View;
  private tfFlowDetailsPanel: sap.ui.core.mvc.View;
  private spaceId: string;
  private router: sap.m.routing.Router;
  private objectId: string;
  private userPrivilege: boolean;
  private selectedTaskLogId: string;
  private runWithParameter: any;
  public inputParameterName = "detailsInputParameter";
  public viewSettingsDialogs = {};
  public activityTextFormatter = activityTextFormatter;
  public tfRunWithSettings: any;
  public tfEditBatchesDialog: sap.m.Dialog;
  public eventNamesList = [
    "dataFlowMonitorDetails",
    "dataFlowMonitorTaskLogDetails",
    "transformationFlowMonitorDetails",
    "transformationFlowMonitorTaskLogDetails",
  ];
  private illustratedMsg: sap.m.IllustratedMessage;
  private executionMode: number = 0;
  isReusableTaskScheduleFFEnabled: boolean;
  isAdoptionOfTaskSchedulerEnabled: boolean;
  isRemoteSourceSelectionAllowed: boolean;
  isSpaceDefaultSelected: boolean = false;
  remoteSourceName: string;
  /**
   * init
   *
   * @memberof DataFlowDetailsClass
   */
  public onInit(): void {
    super.onInit();
    const oDisplayModel = new sap.ui.model.json.JSONModel({});
    this.view.setModel(oDisplayModel, "selectedModel");
    const oDataflowModel = new sap.ui.model.json.JSONModel({});
    this.view.setModel(oDataflowModel, "dataflowModel");
    this.setViewSettingsModel();
    // set default scheduling model
    const detailSchedulingModel = new sap.ui.model.json.JSONModel();
    detailSchedulingModel.setSizeLimit(1000);
    this.view.setModel(detailSchedulingModel, "detailScheduleModel");
    this.router = sap.ui.core.UIComponent.getRouterFor(this) as sap.m.routing.Router;

    const userSettingsModel = new sap.ui.model.json.JSONModel({
      objectNameDisplay: User.getInstance().getObjectNameDisplay(),
    });
    this.view.setModel(userSettingsModel, "settings");
    this.registerForUserPrefChanges();
    this.isReusableTaskScheduleFFEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULING_UI");
    this.isAdoptionOfTaskSchedulerEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION");
    const runtimeErrorModel = new sap.ui.model.json.JSONModel({
      runtimeErrorExists: false,
    });
    this.view.setModel(runtimeErrorModel, "runtimeErrorModel");

    this.router.attachRouteMatched("FlowMonitorDetails", this.handleRoute.bind(this));

    // default model which contains data regarding remote table infos and performance
    const defaultModel = new sap.ui.model.json.JSONModel({});
    defaultModel.setSizeLimit(1000);
    this.view.setModel(defaultModel);
    this.handleHanaStateChange(this.onHanaStateChange.bind(this));
  }

  /**
   * Handle Hana state change for flow monitoring details
   *
   */
  public onHanaStateChange(isHanaDown?: boolean, isHanaNotProvisioned?: boolean) {
    if (isHanaDown || isHanaNotProvisioned) {
      this.view.getModel("dataflowModel").setProperty("/name", "");
      this.view.getModel("dataflowModel").setProperty("/isLargeSystemSpace", undefined);
      (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/", {});
      (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty("/", {});
      (this.view.getModel("detailScheduleModel") as sap.ui.model.json.JSONModel).setProperty("/", {});
      this.setRunMenuButtonVisibility();
    } else {
      this.onRefresh();
    }
  }

  /**
   * Handle Route for monitoring details
   * @date 06/06/2023 - 15:30:56
   * @async
   * @param {sap.ui.base.Event} event
   * @returns {*}
   */
  async handleRoute(event: sap.ui.base.Event) {
    const { isHanaDown, isHanaNotProvisioned } = Utils.getHanaState();
    if (isHanaDown || isHanaNotProvisioned) {
      this.view.getModel("dataflowModel").setProperty("/name", "");
      (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/", {});
      (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty("/", {});
      (this.view.getModel("detailScheduleModel") as sap.ui.model.json.JSONModel).setProperty("/", {});
    } else if (this.eventNamesList.includes(event.getParameter("name"))) {
      const featureFlags = sap.ui.getCore().getModel("featureflags").getData();
      this.showTransformationFlows = featureFlags.INFRA_DWC_TWO_TENANT_MODE ? true : false;
      // check space is locked and set button visibility
      this.checkSpaceLockPrivilege();
      // check if scheduling is disabled
      this.checkIsSchedulingDisabled();
      // clear models on nav
      (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/", {});
      (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty("/", {});
      (this.view.getModel("detailScheduleModel") as sap.ui.model.json.JSONModel).setProperty("/", {});
      this.resetDeltaCaptureSettings();
      this.resetExecutionModeSettings();
      this.resetDefaultViewSettings();
      this.resetBatches();
      const oLayoutModel = new sap.ui.model.json.JSONModel({});
      this.view.setModel(oLayoutModel, "layoutModel");
      oLayoutModel.setProperty("/layout", LAYOUTMODEL.TWOCOLUMNMIDEXPANDED);
      const args = event.getParameter("arguments");
      this.spaceId = args.spaceId;
      this.objectId = args.objectId;

      // get task log id
      if (args.taskLogId) {
        this.selectedTaskLogId = args.taskLogId;
      }
      // Set growing to false for the runs table, when dataflow is open from tasklog. #DW101-15204
      const growing = args.taskLogId ? false : true;
      this.setLogsTableGrowing(growing);
      // get user privilege
      this.userPrivilege = this.canStopDataflow();
      this.dataflowDetailsPanel?.destroy();
      this.dataflowDetailsPanel = null;
      this.tfFlowDetailsPanel?.destroy();
      this.tfFlowDetailsPanel = null;
      const isLargeSystemSpaceFlag = Utils.isLargeSystemSpace(this.spaceId);
      this.view.getModel("dataflowModel").setProperty("/isLargeSystemSpace", isLargeSystemSpaceFlag);
      if (
        event.getParameter("name") === "dataFlowMonitorDetails" ||
        event.getParameter("name") === "dataFlowMonitorTaskLogDetails"
      ) {
        this.view.getModel("dataflowModel").setProperty("/isDataflow", true);
        setHelpScreenId("dataFlowMonitorDetails");
        const viewData = this.createViewData(true);
        // Create view for dataflow details right panel
        this.dataflowDetailsPanel = await this.createDetailsView(
          "dataflowDetailsPanel",
          "DataFlowDetailsPanel",
          viewData
        );
        this.addViewToParent(this.dataflowDetailsPanel);
        await this.getDataflowDetails();
        this.getFlowRunDetails();
      } else if (
        this.showTransformationFlows &&
        (event.getParameter("name") === "transformationFlowMonitorDetails" ||
          event.getParameter("name") === "transformationFlowMonitorTaskLogDetails")
      ) {
        this.view.getModel("dataflowModel").setProperty("/isDataflow", false);
        setHelpScreenId("transformationFlowMonitorDetails");
        const viewData = this.createViewData(false);
        // Create view for transformationflow details right panel
        this.tfFlowDetailsPanel = await this.createDetailsView(
          "transformationFlowDetailsPanel",
          "TransformationFlowDetailsPanel",
          viewData
        );
        this.addViewToParent(this.tfFlowDetailsPanel);
        this.fetchDeltaSubscriptions();
        this.fetchBatches();
        await this.getTransformationflowDetails();
        this.getFlowRunDetails();
        await this.fetchRuntimeSettings();
        this.isRemoteSourceSelectionAllowed = Utils.isSparkSelectionSupported() && isLargeSystemSpaceFlag;
        this.view
          .getModel("dataflowModel")
          ?.setProperty("/isSparkSelectionEnabled", this.isRemoteSourceSelectionAllowed);

        if (this.isRemoteSourceSelectionAllowed) {
          await this.fetchResources();
        }
      }
      this.formatDataFlowName();
      this.getSchedule();
      this.setRunMenuButtonVisibility();
    }
  }

  /**
   * Fetch run details for TF and DF
   *
   * @public
   */
  private getFlowRunDetails() {
    this.checkSpaceLockPrivilege();
    const showActions = (this.view.getModel() as sap.ui.model.json.JSONModel).getProperty("/showActions");
    const oSelectedData = (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).getData() || {};
    const spaceLockData = (this.view.getModel("spaceLockModel") as sap.ui.model.json.JSONModel)?.getData() || {};
    const detailsData = {
      showActions: showActions,
      oSelectedData: oSelectedData,
      spaceLockData: spaceLockData,
      formattedName: this.objectId,
    };
    if (this.isDataflow()) {
      (this.dataflowDetailsPanel?.getController() as DataFlowDetailsPanelClass)?.getDataflowRunDetails(
        this.objectId,
        this.spaceId,
        detailsData
      );
    } else {
      (
        this.tfFlowDetailsPanel?.getController() as TransformationFlowDetailsPanelClass
      )?.getTransformationFlowRunDetails(this.objectId, this.spaceId, detailsData);
    }
  }

  /**
   * Creates view data for right details panel
   * @private
   * @param {boolean} isDataflow
   * @returns {IFlowDetailsViewData}
   */
  private createViewData(isDataflow: boolean): IFlowDetailsViewData {
    const spaceLockData = (this.view.getModel("spaceLockModel") as sap.ui.model.json.JSONModel)?.getData() || {};
    const privRemoteConnData =
      (this.view.getModel("privRemoteConnModel") as sap.ui.model.json.JSONModel).getData() || {};
    return {
      spaceLockData: spaceLockData,
      privRemoteConnData: privRemoteConnData,
      isDataflow: isDataflow,
      spaceId: this.spaceId,
    };
  }

  /**
   * Creates new details view for right panel
   *
   * @private
   * @async
   * @param {string} id
   * @param {string} viewName
   * @param {IFlowDetailsViewData} viewData
   * @returns {Promise<sap.ui.core.mvc.View>}
   */
  private async createDetailsView(
    id: string,
    viewName: string,
    viewData: IFlowDetailsViewData
  ): Promise<sap.ui.core.mvc.View> {
    const flowDetailsPanel = await sap.ui.core.mvc.View.create({
      type: sap.ui.core.mvc.ViewType.XML,
      id: this.createId(id),
      viewName: require(`../view/${viewName}.view.xml`),
      viewData: viewData,
    });
    return flowDetailsPanel;
  }

  /**
   * Adds xml view to parent view
   *
   * @private
   * @param {sap.ui.core.mvc.View} flowDetailsPanel
   */
  private addViewToParent(flowDetailsPanel: sap.ui.core.mvc.View) {
    const flexColumnLayout = this.view.byId("detailsPaneContainer") as sap.f.FlexibleColumnLayout;
    flexColumnLayout.addMidColumnPage(flowDetailsPanel);
  }
  /**
   * onExit
   *
   * @memberof DataFlowDetailsClass
   */
  public onExit(): void {
    this.unRegisterFromUserPrefChanges();
  }

  /**
   * Subscribe to user preference changes
   */
  protected registerForUserPrefChanges() {
    sap.ui.getCore().getEventBus().subscribe("userPreferences", "change", this.handleUserPrefsChanged, this);
  }

  /**
   * Unsubscribe from user preference changes
   */
  protected unRegisterFromUserPrefChanges() {
    sap.ui.getCore().getEventBus().unsubscribe("userPreferences", "change", this.handleUserPrefsChanged, this);
  }

  /**
   * Do action when user preference is changed
   */
  public async handleUserPrefsChanged(channel, eventName, preferenceChanges: PreferenceChanges) {
    if (!preferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY) {
      return;
    }
    const objectNameDisplay = this.getView().getModel("settings").getProperty("/objectNameDisplay");
    if (objectNameDisplay !== preferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY.newValue) {
      this.view
        .getModel("settings")
        .setProperty("/objectNameDisplay", preferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY.newValue);
    }
    (this.getView().getModel("settings") as sap.ui.model.json.JSONModel).setProperty(
      "/objectNameDisplay",
      preferenceChanges.changes.DWC_OBJECT_NAME_DISPLAY.newValue
    );
    await this.getFlowRuns();
    this.formatDataFlowName();
    this.formatBatchesInformation();
  }

  /**
   * Show data flow technical name or business name based on user preference
   *
   * @memberof DataFlowDetailsClass
   */
  public formatDataFlowName() {
    let formattedDataFlowName: string;
    const showBusinessName =
      this.view.getModel("settings")?.getProperty("/objectNameDisplay") === ObjectNameDisplay.businessName;
    const businessName = this.view.getModel().getProperty("/businessName");
    if (showBusinessName && businessName) {
      formattedDataFlowName = businessName;
    } else {
      formattedDataFlowName = this.objectId;
    }
    (this.view.getModel("dataflowModel") as sap.ui.model.json.JSONModel).setProperty("/name", formattedDataFlowName);
  }

  /**
   * on execution item press on left panel
   *
   * @memberof DataFlowDetailsClass
   */

  public async onPress(oEvent: sap.ui.base.Event) {
    let oObject;

    if (oEvent.getParameter("rowIndex") !== -1) {
      const oSource = oEvent.getSource() as sap.ui.table.Table;
      const context = oSource.getContextByIndex(oSource.getSelectedIndex());
      oObject = context ? context.getObject() : undefined;
    } else {
      oObject =
        oEvent.getParameter("rowContext") &&
        oEvent.getParameter("rowContext").getModel() &&
        oEvent.getParameter("rowContext").getModel().getProperty(oEvent.getParameter("rowContext").getPath());
    }
    (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty("/", oObject);

    // hide download for runs with no handle
    (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty("/noHandle", !oObject?.handle);

    // check for user privilege
    const userHasPrivilege =
      this.userPrivilege && this.view.getModel("selectedModel").getProperty("/status") === RUN_STATUS.RUNNING;
    (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
      "/userHasPrivilege",
      userHasPrivilege
    );
    // fetch messages
    this.getFlowRunDetails();
  }

  public getRoutePrefix(routeName: string): string {
    const hash: string = window.location.hash;

    let match: RegExpMatchArray | null = null;

    if (routeName === "dataFlowMonitor") {
      match = hash.match(/^#\/(.+?)\/([A-Z0-9_]+)\/dataFlowMonitor\//);
    } else if (routeName === "transformationFlowMonitorDetails") {
      match = hash.match(/^#\/(.+?)\/([A-Z0-9_]+)\/transformationFlowMonitorDetails\//);
    }

    return match ? match[1] : "";
  }

  /**
   * Called on table item press
   *
   * @public
   * @async
   * @param {sap.ui.base.Event} oEvent
   * @returns {*}
   */
  public async onTableItemPress(oEvent: sap.ui.base.Event) {
    let oObject;
    const oTable = this.getView().byId("runsTable") as sap.m.Table;
    if (oTable.indexOfItem(oTable.getSelectedItem()) !== -1) {
      const oSource = oEvent.getSource() as sap.m.Table;
      const context = oSource.getSelectedItem().getBindingContext();
      oObject = context ? context.getObject() : undefined;
    }
    const layoutModel = this.view.getModel("layoutModel");
    if (layoutModel?.getProperty("/layout") === LAYOUTMODEL.ONECOLUMN) {
      this.view.getModel("layoutModel").setProperty("/layout", LAYOUTMODEL.TWOCOLUMNMIDEXPANDED);
    }
    (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty("/", oObject);

    // hide download for runs with no handle
    (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty("/noHandle", !oObject?.handle);

    // check for user privilege
    const userHasPrivilege =
      this.userPrivilege && this.view.getModel("selectedModel").getProperty("/status") === RUN_STATUS.RUNNING;
    (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
      "/userHasPrivilege",
      userHasPrivilege
    );

    // Update URL hash with selected task log details
    const spaceId = this.spaceId;
    const objectId = this.objectId;
    const taskLogId = oObject?.logId;

    const isDataflow = this.isDataflow?.();
    const routeName = isDataflow ? "dataFlowMonitor" : "transformationFlowMonitorDetails";

    const prefix = this.getRoutePrefix(routeName);

    if (spaceId && objectId && taskLogId) {
      const newHash = prefix
        ? `#/${prefix}/${spaceId}/${routeName}/${objectId}/${taskLogId}`
        : `#/${spaceId}/${routeName}/${objectId}/${taskLogId}`;
      window.history.pushState({}, "", newHash);
    }
    // Fetch messages
    this.getFlowRunDetails();
  }

  /**
   * Navigates to Data Flow Modeler inside databuidler
   *
   * @memberof DataFlowDetailsClass
   */
  public openFlowEditor(): void {
    const semanticObject = "databuilder";
    const params = {
      spaceId: this.spaceId,
      model: this.objectId,
    };
    ShellNavigationService.toExternal({
      target: {
        semanticObject: semanticObject,
      },
      params,
    });
  }

  /**
   * get dataflow details
   *
   * @memberof DataFlowDetailsClass
   */
  public async getDataflowDetails() {
    if (this.objectId) {
      const showBusinessName =
        this.view.getModel("settings")?.getProperty("/objectNameDisplay") === ObjectNameDisplay.businessName;
      this.view.setBusy(true);
      try {
        const oResponse = await Utils.fetchDataFlowDetails(this.spaceId, this.objectId, showBusinessName);
        this.view.setBusy(false);
        // set formatted data to response
        oResponse.data.instances.forEach((item) => {
          item.formattedStarted = this.formatDateTime(Utils.toISOFormat(item.started));
          item.formattedStopped = this.formatDateTime(Utils.toISOFormat(item.stopped));
        });

        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/", oResponse.data.instances);
        // Store business name
        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty(
          "/businessName",
          oResponse.data?.businessName || ""
        );

        // Check if dataflow is already running
        if (oResponse.data?.lastInstance?.status === "Running") {
          (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/isFlowRunning", true);
        } else {
          (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/isFlowRunning", false);
        }

        // set generic data flow status depending on last instance status
        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty(
          "/lastInstanceStatus",
          oResponse.data.lastInstance.status
        );

        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/disableCheckup", false);
        if (oResponse?.data?.lockId) {
          const isSDPEnabled = sap.ui.getCore().getModel("featureflags")?.getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
          let allowSetToFailed = true;
          if (isSDPEnabled) {
            const privilegeModel = sap.ui.getCore().getModel("privilege");
            const spaceLockModel = this.view.getModel("spaceLockModel");
            const hasIntegratorPriv = privilegeModel.getProperty("/DWC_DATAINTEGRATION/update");
            const isSpaceOpen = spaceLockModel.getProperty("/isSpaceOpen");
            allowSetToFailed = hasIntegratorPriv && isSpaceOpen;
          } else {
            const privModel = this.view.getModel("privRemoteConnModel") as sap.ui.model.json.JSONModel;
            allowSetToFailed = privModel.getProperty("/update");
          }
          (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/allowSetToFailed", allowSetToFailed);
          (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/lockId", oResponse.data.lockId);
        }

        // hide actions if there are no instances
        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty(
          "/showActions",
          oResponse.data.instances.length ? true : false
        );

        // show selected instance when navigated from notification feed
        let selectedTasklogIndex = 0;
        if (this.selectedTaskLogId) {
          selectedTasklogIndex = oResponse.data.instances.findIndex(
            (e) => e.logId?.toString() === this.selectedTaskLogId
          );
          if (selectedTasklogIndex === -1) {
            selectedTasklogIndex = 0;
          }
        }

        this.setSelectedTableItem("runsTable", selectedTasklogIndex);

        (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
          "/",
          oResponse.data.instances[selectedTasklogIndex]
        );

        // hide download for runs with no handle
        (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
          "/noHandle",
          !oResponse.data.instances[selectedTasklogIndex]?.externalInstanceId
        );

        // set property for user privilege
        this.setUserPrivilege();

        this.handleRuntimeEngineError(oResponse.data);
      } catch (error) {
        // show error
        const errorMsg = this.i18nModel.getResourceBundle().getText("BACKEND_ERROR_DETAILS");
        MessageHandler.exception({ exception: error, message: errorMsg });
        this.view.setBusy(false);
      }
    }
  }

  /**
   * get transformationflow details
   *
   * @memberof DataFlowDetailsClass
   */
  public async getTransformationflowDetails() {
    if (this.objectId) {
      const showBusinessName =
        this.view.getModel("settings")?.getProperty("/objectNameDisplay") === ObjectNameDisplay.businessName;
      this.view.setBusy(true);
      try {
        const oResponse = await Utils.fetchTransformationFlowRuns(this.spaceId, this.objectId, showBusinessName);
        this.view.setBusy(false);
        // set formatted data to response
        const logs = oResponse.data?.runs;

        // fetch runs with activity as "EXECUTE"
        let runActivities = logs
          ?.filter((run) => run.activity === "EXECUTE")
          .sort((a, b) => (new Date(a.startTime) > new Date(b.startTime) ? -1 : 1));

        logs.forEach((item) => {
          const name = `${item.spaceId}.${item.objectId}`;
          item.name = name;
          item.status = this.statusFormatter(item.status);
          item.formattedStarted = this.formatDateTime(item.startTime);
          item.formattedStopped = this.formatDateTime(item.endTime);
        });
        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/", logs);
        // Store business name
        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty(
          "/businessName",
          oResponse.data?.businessName || ""
        );

        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/simulateRun", false);
        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/getPlanViz", false);
        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/getExplainPlan", false);

        // Check if last execute activity is already running
        if (runActivities[0]?.status === "Running") {
          (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/isFlowRunning", true);
        } else {
          (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/isFlowRunning", false);
        }

        // set generic data flow status depending on last execute activity status
        // Skip Failed(Locked) status for details header, Bug fix DW101-72897
        let firstTflog;
        if (runActivities.length > 0) {
          firstTflog = runActivities.find((run) => run.subStatus !== "LOCKED");
        }

        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/lastInstanceStatus", firstTflog?.status);

        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/disableCheckup", false);
        if (oResponse?.data?.lockId) {
          (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/lockId", oResponse.data.lockId);
        }
        // hide actions if there are no instances
        (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/showActions", logs.length ? true : false);
        // show selected instance when navigated from notification feed
        let selectedTasklogIndex = 0;
        if (this.selectedTaskLogId) {
          selectedTasklogIndex = logs.findIndex((e) => e.logId?.toString() === this.selectedTaskLogId);
          if (selectedTasklogIndex === -1) {
            selectedTasklogIndex = 0;
          }
        }
        this.setSelectedTableItem("runsTable", selectedTasklogIndex);

        (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
          "/",
          logs[selectedTasklogIndex]
        );

        // set property for user privilege
        this.setUserPrivilege();

        this.handleRuntimeEngineError(oResponse.data);
      } catch (error) {
        // show error
        const errorMsg = this.i18nModel.getResourceBundle().getText("BACKEND_ERROR_DETAILS");
        MessageHandler.exception({ exception: error, message: errorMsg });
        this.view.setBusy(false);
      }
    }
    this.setRunMenuButtonVisibility();
  }

  /**
   * set run menu button visibility in transformatiom flow details
   *
   * @memberof DataFlowDetailsClass
   */
  public setRunMenuButtonVisibility() {
    const privilegeModel = sap.ui.getCore().getModel("privilege");
    const spaceLockModel = this.view.getModel("spaceLockModel");
    const hasIntegratorPriv = privilegeModel.getProperty("/DWC_DATAINTEGRATION/update");
    const isSpaceOpen = spaceLockModel.getProperty("/isSpaceOpen");
    const dataflowModel = this.view.getModel("dataflowModel") as sap.ui.model.json.JSONModel;
    const isPlanVizEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_TRANSFORMATION_FLOW_PLAN_VIZ");
    const isExplainPlanEnabled = sap.ui
      .getCore()
      .getModel("featureflags")
      .getProperty("/DWCO_TRANSFORMATION_FLOW_EXPLAIN_PLAN");
    const isLargeSystemSpace = dataflowModel.getProperty("/isLargeSystemSpace");
    if (!this.isDataflow() && hasIntegratorPriv && isSpaceOpen && isPlanVizEnabled && !isLargeSystemSpace) {
      this.view.getModel("dataflowModel").setProperty("/isTFRunMenuButtonVisible", true);
    } else {
      this.view.getModel("dataflowModel").setProperty("/isTFRunMenuButtonVisible", false);
    }
    if (!this.isDataflow() && hasIntegratorPriv && isSpaceOpen && (!isPlanVizEnabled || isLargeSystemSpace)) {
      this.view.getModel("dataflowModel").setProperty("/isTFRunButtonVisible", true);
    } else {
      this.view.getModel("dataflowModel").setProperty("/isTFRunButtonVisible", false);
    }
    if (isPlanVizEnabled && isExplainPlanEnabled) {
      this.view.getModel("dataflowModel").setProperty("/isExplainPlanVisible", true);
    } else {
      this.view.getModel("dataflowModel").setProperty("/isExplainPlanVisible", false);
    }
  }
  /**
   * set property for user privilege
   */
  setUserPrivilege() {
    const userHasPrivilege =
      this.userPrivilege && this.view.getModel("selectedModel").getProperty("/status") === RUN_STATUS.RUNNING;
    (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).setProperty(
      "/userHasPrivilege",
      userHasPrivilege
    );
  }
  /**
   * sets selected table item
   *
   * @public
   * @param {string} tableId
   * @param {number} selectedTasklogIndex
   */
  public setSelectedTableItem(tableId: string, selectedTasklogIndex: number) {
    const oTable = this.view.byId(tableId) as sap.m.Table;
    (oTable as any).scrollToIndex(selectedTasklogIndex);
    oTable.setSelectedItem(oTable.getItems()[selectedTasklogIndex]);
  }

  /**
   * status color formatter
   *
   * @memberof DataFlowMonitorBaseClass
   */

  public statusFormatter(sUIStatus: string): any {
    switch (sUIStatus) {
      case "COMPLETED":
        return "Completed";
      case "FAILED":
        return "Failed";
      case "RUNNING":
        return "Running";
      default:
        return "";
    }
  }

  /**
   * get dataflow and message details on refresh
   *
   * @memberof DataFlowDetailsClass
   */
  public async onRefresh(): Promise<void> {
    await this.getFlowRuns();
    this.formatDataFlowName();
    this.getFlowRunDetails();
    this.fetchBatches();
    this.fetchDeltaSubscriptions();
    await this.fetchRuntimeSettings();
    if (this.isRemoteSourceSelectionAllowed) {
      await this.fetchResources();
    }
    await this.getSchedule();
  }

  /**
   * Loads Runs table
   *
   * @returns {*}
   */
  public async getFlowRuns() {
    if (this.isDataflow()) {
      await this.getDataflowDetails();
    } else {
      await this.getTransformationflowDetails();
    }
  }
  /**
   * check user privilege for stop dataflow
   *
   * @memberof DataFlowDetailsClass
   */

  public canStopDataflow(): boolean {
    const isSDPEnabled = sap.ui.getCore().getModel("featureflags")?.getProperty("/DWC_DUMMY_SPACE_PERMISSIONS");
    let dataintegrationPrivilege;
    // get privileges for user
    if (!isSDPEnabled) {
      dataintegrationPrivilege = ShellContainer.get().getPrivilegeService().getPrivilegesByType("DWC_DATAINTEGRATION");
      return (dataintegrationPrivilege && dataintegrationPrivilege.update !== false) || false;
    }
  }

  /**
   * get schedule
   *
   * @memberof DataFlowDetailsClass
   */

  public async getSchedule() {
    if (!this.spaceId || !this.objectId) {
      return;
    }
    const applicationId = this.isDataflow() ? ApplicationId.DATA_FLOWS : ApplicationId.TRANSFORMATION_FLOWS;
    const sUrl = `/tf/${this.spaceId}/schedules?applicationId=${applicationId}&objectId=${this.objectId}&activity=EXECUTE`;
    const scheduleBtn = this.view.byId("detailSchedulingMenu") as sap.m.MenuButton;
    const isSchedulingDisabled = (this.view.getModel("dfSchedulingModel") as sap.ui.model.json.JSONModel).getProperty(
      "/disableScheduling"
    );
    if (scheduleBtn) {
      scheduleBtn.setEnabled(false);
    }
    return ServiceCall.request<any>({
      url: sUrl,
      type: HttpMethod.GET,
      contentType: ContentType.APPLICATION_JSON,
    })
      .then(async (oResponse) => {
        if (oResponse.data[0] && oResponse.data[0].scheduleId) {
          // set formatted data to response
          oResponse.data[0].formattedChangedAt = this.formatDateTime(oResponse.data[0].changedAt);
          if (oResponse.data[0]?.activationStatus === "DISABLED") {
            oResponse.data[0].formattedNextRun = "";
          } else {
            oResponse.data[0].formattedNextRun = this.formatDateTime(oResponse.data[0].nextRun);
          }

          const scheduleModel = this.view.getModel("detailScheduleModel") as sap.ui.model.json.JSONModel;
          scheduleModel.setProperty("/", oResponse.data[0]);
          scheduleModel.setProperty("/newSchedule", false);
          scheduleModel.setProperty("/editSchedule", true);
          scheduleModel.setProperty("/deleteSchedule", true);
          const schedule = oResponse.data[0];
          if (schedule.activationStatus === "ENABLED") {
            if (isDiMonitorImprovementsEnabled()) {
              const frequency = schedule?.frequency
                ? `${this.getText("everyLabel")} ${schedule?.frequency?.interval} ${this.getText(
                    schedule?.frequency?.type
                  )}`
                : schedule?.cron;
              scheduleModel.setProperty("/refresh_frequency", frequency);
            } else {
              scheduleModel.setProperty("/refresh_frequency", this.i18nModel.getResourceBundle().getText("SCHEDULED"));
            }

            scheduleModel.setProperty("/isSchedulePaused", false);
          } else if (oResponse.data[0].activationStatus === "DISABLED") {
            scheduleModel.setProperty("/refresh_frequency", this.i18nModel.getResourceBundle().getText("PAUSED"));
            scheduleModel.setProperty("/isSchedulePaused", true);
          }
          scheduleModel.setProperty("/refresh_frequency_active", true);
        } else {
          this.resetScheduleModel();
        }
        if (scheduleBtn && !isSchedulingDisabled) {
          scheduleBtn.setEnabled(true);
        }
      })
      .catch(() => {
        // error
        this.resetScheduleModel();
        if (scheduleBtn && !isSchedulingDisabled) {
          scheduleBtn.setEnabled(true);
        }
      });
  }

  async initScheduleDialog() {
    if (!this["newScheduleDialog"]) {
      this["newScheduleDialog"] = await getTaskScheduer("dataFlowDetailsScheduler");
    }
  }

  /**
   * create schedule
   *
   * @memberof DataFlowDetailsClass
   */
  public async onCreateSchedule(): Promise<void> {
    const applicationId = this.isDataflow() ? ApplicationId.DATA_FLOWS : ApplicationId.TRANSFORMATION_FLOWS;
    const data: ITaskScheduleRequest = {
      objectId: this.objectId,
      applicationId: applicationId,
      activity: "EXECUTE",
      description: "Data Flow Monitoring",
      activationStatus: "ENABLED",
    };
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`createTaskSchedule: ${data.applicationId}`, "taskSchedule", "onCreate");
    } else {
      scheduleDialog = (
        this.getView().byId("detailSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const oResourceBundle = (
      this.view.getModel("i18n_task") as sap.ui.model.resource.ResourceModel
    ).getResourceBundle();
    this.view.setBusy(true);
    scheduleDialog.createTaskSchedule(
      data,
      this.spaceId,
      applicationId,
      () => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
        }
        const msg = oResourceBundle.getText("createScheduleSuccess");
        MessageHandler.success(msg);
        this.view.setBusy(false);
        this.onRefresh();
      },
      (error) => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          MessageHandler.exception({
            exception: error.error,
            message: error.message,
          });
        }
        this.view.setBusy(false);
      },
      () => {
        this.view.setBusy(false);
      }
    );
  }

  /**
   * edit schedule
   *
   * @memberof DataFlowDetailsClass
   */

  public async onEditSchedule(): Promise<void> {
    const applicationId = this.isDataflow() ? ApplicationId.DATA_FLOWS : ApplicationId.TRANSFORMATION_FLOWS;
    const data: ITaskScheduleRequest = {
      objectId: this.objectId,
      applicationId: applicationId,
      activity: "EXECUTE",
      description: "Data Flow Monitoring",
      activationStatus: "ENABLED",
    };
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      recordAction(`editTaskSchedule: ${data.applicationId}`, "taskSchedule", "onEdit");
    } else {
      scheduleDialog = (
        this.getView().byId("detailSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const oResourceBundle = (
      this.view.getModel("i18n_task") as sap.ui.model.resource.ResourceModel
    ).getResourceBundle();
    this.view.setBusy(true);
    scheduleDialog.changeTaskSchedule(
      data,
      this.spaceId,
      applicationId,
      () => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
        }
        const msg = oResourceBundle.getText("updateScheduleSuccess");
        MessageHandler.success(msg);
        this.view.setBusy(false);
        this.onRefresh();
      },
      (error) => {
        if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
          recordAction(`saveTaskSchedule: ${data.applicationId}`, "taskSchedule", "save");
          MessageHandler.exception({
            exception: error.error,
            message: error.message,
          });
        }
        this.view.setBusy(false);
      },
      () => {
        this.view.setBusy(false);
      }
    );
  }

  /**
   * delete schedule
   *
   * @memberof DataFlowDetailsClass
   */

  public async onDeleteSchedule(): Promise<void> {
    const applicationId = this.isDataflow() ? ApplicationId.DATA_FLOWS : ApplicationId.TRANSFORMATION_FLOWS;
    const data: ITaskScheduleRequest = {
      objectId: this.objectId,
      applicationId: applicationId,
      activity: "EXECUTE",
      description: "Data Flow Monitoring",
      activationStatus: "ENABLED",
    };
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
    } else {
      scheduleDialog = (
        this.getView().byId("detailSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
    }
    const oResourceBundle = (
      this.view.getModel("i18n_task") as sap.ui.model.resource.ResourceModel
    ).getResourceBundle();
    this.view.setBusy(true);
    scheduleDialog.deleteSchedule(
      data,
      this.spaceId,
      applicationId,
      () => {
        const msg = oResourceBundle.getText("deleteScheduleSuccess");
        MessageHandler.success(msg);
        this.view.setBusy(false);
        this.onRefresh();
      },
      (error) => {
        this.view.setBusy(false);
      },
      () => {
        this.view.setBusy(false);
      }
    );
  }

  /**
   * open schedule
   * @memberof DataFlowDetailsClass
   */

  public async openSchedule(oEvent: any): Promise<void> {
    const applicationId = this.isDataflow() ? ApplicationId.DATA_FLOWS : ApplicationId.TRANSFORMATION_FLOWS;
    const link = oEvent.getSource();
    const isSpaceLocked = this.getView().getModel("spaceLockModel")?.getProperty("/isSpaceLocked");
    const data: ITaskScheduleRequest = {
      objectId: this.objectId,
      applicationId: applicationId,
      activity: "EXECUTE",
      description: "Data Flow Monitoring",
      activationStatus: "ENABLED",
    };
    let scheduleDialog;
    if (this.isReusableTaskScheduleFFEnabled && this.isAdoptionOfTaskSchedulerEnabled) {
      await this.initScheduleDialog();
      scheduleDialog = this["newScheduleDialog"];
      if (isDiMonitorImprovementsEnabled()) {
        await openSchedulePopover(
          link,
          this.objectId,
          applicationId,
          this.spaceId,
          this["newScheduleDialog"],
          data,
          this,
          this.onRefresh.bind(this),
          isSpaceLocked
        );
      } else {
        await openSchedulePopover(link, this.objectId, applicationId, this.spaceId, this["newScheduleDialog"]);
      }
    } else {
      scheduleDialog = (
        this.getView().byId("detailSchedulingDialog") as sap.ui.core.mvc.View
      ).getController() as ITTaskScheduleController;
      scheduleDialog.openSchedulePopover(link, this.objectId, applicationId, this.spaceId);
    }
  }

  /**
   * on checkup confirmation
   * @memberof DataFlowDetailsClass
   */

  public async onCheckupConfirmation(): Promise<void> {
    await this.onDataflowCheckupConfirmation(this.objectId, this.spaceId);
  }

  /**
   * on direct execute
   * @memberof DataFlowDetailsClass
   */

  public async onExecute(): Promise<void> {
    await this.handleDataFlowRunWithInputParameters(this.objectId, this.spaceId);
  }

  /**
   * on run with settings
   * @memberof DataFlowDetailsClass
   */

  public async onTfExecuteWithSettings(): Promise<void> {
    let activity = Activity.EXECUTE;
    const optionalParams = this.getRunTransformationFlowOptionalParams();
    // check if simulate run is selected , activity to be passed is SIMULATE_RUN
    if (optionalParams.simulateRun) {
      activity = Activity.SIMULATE_RUN;
    }
    const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
    if (oFeatures.DWCO_TRF_INPUT_PARAMETERS_SUPPORT) {
      await this.runWithInputParameterTF(this.objectId);
    } else {
      await this.onRunTransformationFlow(this.objectId, this.spaceId, activity, optionalParams);
    }
  }

  /**
   * on run without settings
   * @memberof DataFlowDetailsClass
   */

  public async onTfExecute(): Promise<void> {
    let activity = Activity.EXECUTE;
    const optionalParams = this.getRunTransformationFlowOptionalParams();
    // check if simulate run is selected , activity to be passed is SIMULATE_RUN
    if (optionalParams.simulateRun) {
      activity = Activity.SIMULATE_RUN;
    }
    const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
    if (oFeatures.DWCO_TRF_INPUT_PARAMETERS_SUPPORT) {
      await this.handleTransformationFlowRunWithInputParameters(this.objectId, this.spaceId, activity, optionalParams);
    } else {
      await this.onRunTransformationFlow(this.objectId, this.spaceId, activity, optionalParams);
    }
  }

  /**
   * on run with settings
   * @memberof DataFlowDetailsClass
   */
  public async onExecuteWithSettings(): Promise<void> {
    this.tfRunWithSettings.close();
    await this.onTfExecuteWithSettings();
  }

  /**
   * on execute transformation flow with settings dialog open
   * @memberof DataFlowDetailsClass
   */
  public async onSettingsOpen(): Promise<void> {
    const oFeatures = sap.ui.getCore().getModel("featureflags").getData();
    const hasInputParamSupport = oFeatures.DWCO_TRF_INPUT_PARAMETERS_SUPPORT;
    let parameterList: IParameterTF[] = [];

    if (hasInputParamSupport) {
      try {
        this.showBusyDialog(true);
        parameterList = await Utils.findInputParametersInDeployedTF(this.spaceId, this.objectId);
      } catch (error) {
        this.showBusyDialog(false);
        const errorMsg = this.i18nModel.getResourceBundle().getText("BACKEND_ERROR");
        MessageHandler.exception({
          exception: error,
          message: errorMsg,
          id: "transformationFlowFetchFailedErrorMsgbox",
        });
        return;
      }
      this.showBusyDialog(false);

      const hasParams = parameterList.length > 0;
      (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/hasInputParam", hasParams);

      const parametersModel = new sap.ui.model.json.JSONModel({
        parameters: parameterList,
        minVisibleRowCount: parameterList.length,
        enableRunButton: !this.checkAreInputParametersEmptyTF(parameterList),
        hasInputParam: hasParams,
      });
      this.getView().setModel(parametersModel, "parametersModel");
    }
    const fragmentId = hasInputParamSupport
      ? this.getView().createId("settingsAndInputParametersDialog")
      : this.getView().createId("tfRunWithSettingsDialog");

    const fragmentPath = hasInputParamSupport
      ? require("../view/fragment/TFRunWithSettingsAndInputParameters.fragment.xml")
      : require("../view/fragment/TFRunWithSettings.fragment.xml");

    if (!this.tfRunWithSettings) {
      this.tfRunWithSettings = sap.ui.xmlfragment(fragmentId, fragmentPath, this);
      this.getView().addDependent(this.tfRunWithSettings);
    }

    // Open the dialog
    this.tfRunWithSettings.open();
  }

  /**
   * TF run with settings dialog close
   * @memberof DataFlowDetailsClass
   */
  public onSettingsClose(): void {
    this.tfRunWithSettings.close();
  }

  /**
   * after the TF run with settings & input parameter dialog is closed
   */
  public onAfterSettingsClose(): void {
    this.tfRunWithSettings.destroy();
    this.tfRunWithSettings = null;
    this.resetRunTransformationFlowOptionalParams();
  }

  /**
   * after the edit batches dialog is closed for TF in HANA runtime
   */
  public onAfterBatchesDialogClose(): void {
    const columnsTable = this.getView().byId("editBatchesFragment--columnsTable");
    const binding = columnsTable?.getBinding("items");
    if (binding) {
      binding["filter"]([]);
    }
    columnsTable?.["removeSelections"]();
    this.getView().byId("editBatchesFragment--searchBatchColumns")?.["setValue"]("");
  }

  /**
   * on selecting explain plan checkbox
   * @memberof DataFlowDetailsClass
   */
  public onSelectExplainPlan(): void {
    const getExplainPlan = this.view.getModel().getProperty("/getExplainPlan");
    if (getExplainPlan) {
      (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/getPlanViz", false);
    }
  }

  /**
   * on selecting simulate run checkbox
   * @memberof DataFlowDetailsClass
   */
  public onSelectSimulateRun(): void {
    const simulateRun = this.view.getModel().getProperty("/simulateRun");
    if (!simulateRun) {
      (this.view.getModel() as sap.ui.model.json.JSONModel).setProperty("/getExplainPlan", false);
    }
  }

  /**
   * reset schedule model
   *
   * @memberof DataFlowDetailsClass
   */

  public resetScheduleModel(): void {
    const scheduleModel = this.view.getModel("detailScheduleModel") as sap.ui.model.json.JSONModel;
    scheduleModel.setProperty("/newSchedule", true);
    scheduleModel.setProperty("/editSchedule", false);
    scheduleModel.setProperty("/deleteSchedule", false);
    scheduleModel.setProperty("/refresh_frequency", "");
    scheduleModel.setProperty("/formattedChangedAt", "");
    scheduleModel.setProperty("/formattedNextRun", "");
  }

  /**
   * on set to failed button press on top panel
   *
   * @memberof DataFlowDetailsClass
   */
  public async onSetToFailed() {
    this.getView().setBusy(true);
    const noTaskWithRunningStateMsg = this.view
      .getModel("i18n")
      .getResourceBundle()
      .getText("NO_TASK_WITH_RUNNING_STATE");
    const actionInfoTitle = this.view.getModel("i18n").getResourceBundle().getText("ACTION_INFO");
    const otaskLogModelData = (this.getView().getModel() as sap.ui.model.json.JSONModel)?.getData();
    const latestRunningTaskLogId = otaskLogModelData.lockId;

    if (latestRunningTaskLogId) {
      const sUrlGet = `tf/${this.spaceId}/setfailed/${latestRunningTaskLogId}`;
      ServiceCall.request<any>({
        url: sUrlGet,
        type: HttpMethod.PUT,
        contentType: ContentType.APPLICATION_JSON,
        dataType: DataType.JSON,
      })
        .then(async () => {
          this.getView().setBusy(false);
          // cancel load action was successful
          MessageHandler.success(
            this.view.getModel("i18n").getResourceBundle().getText("SET_TO_FAILED_ACTION_SUCCESS", [this.objectId])
          );
          this.onRefresh();
        })
        .catch((err) => {
          this.getView().setBusy(false);
          if (err?.length > 0 && err[0].responseJSON?.internalDetails?.code === "taskAlreadyRunning") {
            MessageHandler.exception({
              message: this.view.getModel("i18n").getResourceBundle().getText("TASK_ALREADY_RUNNING", [this.objectId]),
              exception: err,
            });
          } else {
            MessageHandler.exception({
              exception: err,
              message: this.view
                .getModel("i18n")
                .getResourceBundle()
                .getText("SET_TO_FAILED_ACTION_ERROR", [this.objectId]),
            });
          }
          this.onRefresh();
        });
      await this.stopDataFlowInstance();
    } else {
      this.getView().setBusy(false);
      sap.m.MessageBox.show(noTaskWithRunningStateMsg, {
        icon: sap.m.MessageBox.Icon.INFORMATION,
        title: actionInfoTitle,
        actions: [sap.m.MessageBox.Action.OK],
        emphasizedAction: sap.m.MessageBox.Action.OK,
      });
    }
  }

  public async stopDataFlowInstance() {
    try {
      this.getView().setBusy(true);
      const oSelectedData = (this.view.getModel("selectedModel") as sap.ui.model.json.JSONModel).getData();
      const { formattedName, handle, logId } = oSelectedData;
      await Utils.stopDataFlowRun(logId, { spaceId: this.spaceId, handle, formattedName, isSetToFailed: true });
      this.getView().setBusy(false);
    } catch (oError) {
      Logger.logError(oError);
      this.getView().setBusy(false);
    }
  }

  /**
   * on run with parameter confirmation
   *
   * @memberof DataFlowDetailsClass
   */
  public async onRunWithParameterConfirmation(): Promise<void> {
    const configurationSubstitutions = {};
    const parameters: IParameter[] = this.view.getModel("parametersModel")?.getProperty("/parameterList");
    if (Array.isArray(parameters)) {
      for (const item of parameters) {
        configurationSubstitutions[item.parameterKey] = item.parameterValue;
      }
    }
    this.showBusyDialog(true);
    const data = Utils.getRunDataFlowWithConfigurationSubstitutionPayload(
      this.objectId,
      this.spaceId,
      configurationSubstitutions
    );
    const messages: DFExecuteMessages = {
      success: "RUN_WITH_PARAMETER_SUCCESS_MESSAGE",
      fail: "RUN_WITH_PARAMETER_FAIL_MESSAGE",
      msgBoxId: "runWithParemeterFailedErrorMsgbox",
    };
    // if the checkup run is enabled.
    const runWithCheckup = (sap.ui.getCore().byId("parameterdialog--runWithCheckup") as sap.m.CheckBox)?.getSelected();
    if (runWithCheckup) {
      data.tracelevel = "DEBUG";
    }
    this["onRunWithParameterClose"]();
    await this.onExecuteDataflow(data, messages);
    this.onRefresh();
  }

  /**
   * on run with parameter confirmation
   *
   * @memberof DataFlowDetailsClass
   */
  public async onRunWithInputParameterConfirmation(): Promise<void> {
    if (!this.isDataflow()) {
      // Execute for Transformation Flow
      await this.runWithInputParameterTF(this.objectId);
    } else {
      // Execute for Dataflow
      await this.runWithInputParameter(this.objectId);
    }
    this.onRefresh();
  }

  /**
   * Create a new model for the viewSettingsDialog
   * @memberof DataFlowDetailsClass
   */
  public setViewSettingsModel() {
    const oViewSettingsDialogModel = new sap.ui.model.json.JSONModel({});
    this.view.setModel(oViewSettingsDialogModel, "viewSettingsDialogModel");
  }

  /**
   * Reset view settings dialog to default
   * @memberof DataFlowDetailsClass
   */
  public resetDefaultViewSettings() {
    this.view.getModel("viewSettingsDialogModel").setProperty("/", {});
    for (const viewSettingsDialog in this.viewSettingsDialogs) {
      this.viewSettingsDialogs[viewSettingsDialog].destroy();
    }
    const oTable = this.view.byId("runsTable");
    if (oTable) {
      const oBinding = oTable.getBinding("items") as sap.ui.model.ListBinding;
      if (oBinding) {
        oBinding.filter([]);
        oBinding.sort([]);
        oBinding.getModel().refresh(true);
      }
    }
    this.viewSettingsDialogs = {};
    (this.view.byId("runsTable--FilterBar") as sap.m.OverflowToolbar)?.setVisible(false);
  }

  /**
   * The function creates a new instance of the LogSortDialog fragment and adds it
   * as a dependent of the current view
   * @memberof DataFlowDetails
   */
  public openLogsSortDialog() {
    const dialogName = this.isDataflow() ? "LogSortDialog" : "TFRunsSortDialog";
    this.openDialogs(dialogName);
  }

  /**
   * Sort runsTable when Confirm button is pressed.
   * @param oEvent - sap.ui.base.Event
   * @memberof DataFlowDetailsClass
   */
  public handleLogsSortDialogConfirm(oEvent: sap.ui.base.Event) {
    const oTable = this.byId("runsTable"),
      mParams = oEvent.getParameters(),
      oBinding = oTable.getBinding("items") as sap.ui.model.ListBinding,
      aSorters = [];
    const bDescending = mParams.sortDescending;
    aSorters.push(new sap.ui.model.Sorter(mParams.sortItem.getKey(), bDescending));
    oBinding.sort(aSorters);
  }

  /**
   * It creates a dialog fragment if it doesn't exist, and then opens it
   * @memberof DataFlowDetails
   */
  public openLogsFilterDialog() {
    const dialogName = this.isDataflow() ? "LogFilterDialog" : "TFRunsFilterDialog";
    this.openDialogs(dialogName);
  }

  /**
   * Filter runsMessageTable when Confirm button is pressed.
   * @param oEvent - sap.ui.base.Event
   * @memberof DataFlowDetailsClass
   */
  public handleLogFilterDialogConfirm(oEvent: sap.ui.base.Event) {
    const oTable = this.byId("runsTable"),
      mParams = oEvent.getParameters(),
      oBinding = oTable.getBinding("items") as sap.ui.model.ListBinding;
    const aFilters: sap.ui.model.Filter[] = [];
    mParams.filterItems.forEach(function (oItem) {
      const aSplit = oItem.getKey().split("__");
      const sPath = aSplit[0];
      const sOperator = aSplit[1];
      const sValue1 = aSplit[2];
      const sValue2 = aSplit[3];
      let newValue1 = sValue1;
      // Now change the value of sValue1 based on sPath.
      if (sPath === "started") {
        const currentTime = Math.floor(Date.now() / 1000);
        if (sValue1 === "hour") {
          newValue1 = currentTime - 60 * 60;
        } else if (sValue1 === "day") {
          newValue1 = currentTime - 24 * 60 * 60;
        } else {
          newValue1 = currentTime - 2629743;
        }
      }
      if (sPath === "startTime") {
        if (sValue1 === "hour") {
          newValue1 = moment.utc().add(-1, "hours").format();
        } else if (sValue1 === "day") {
          newValue1 = moment.utc().add(-1, "days").format();
        } else {
          newValue1 = moment.utc().add(-1, "months").format();
        }
      }

      const oFilter = new sap.ui.model.Filter(sPath, sOperator, newValue1, sValue2);
      aFilters.push(oFilter);
    });
    // apply filter settings
    oBinding.filter(aFilters);

    // Update Filter bar
    (this.byId("runsTable--FilterBar") as sap.m.OverflowToolbar).setVisible(aFilters.length > 0);
    this.view.getModel("viewSettingsDialogModel").setProperty("/runsTableFilterBarText", mParams.filterString);
  }

  /**
   * It sets the growing property of the table to the value of the growing
   * parameter
   * @param [growing=false] - boolean - If set to true, then the table will grow.
   */
  private setLogsTableGrowing(growing = false) {
    const logsTable = this.byId("runsTable") as sap.m.Table;
    logsTable?.setGrowing(growing);
    logsTable?.invalidate();
  }

  /**
   * Sets the flag to show runtime engine error message
   * @param data The data received from API to fetch data flow details
   */
  private handleRuntimeEngineError(data: any) {
    if (data.diError) {
      (this.view.getModel("runtimeErrorModel") as sap.ui.model.json.JSONModel).setProperty("/runtimeErrorExists", true);
    } else {
      (this.view.getModel("runtimeErrorModel") as sap.ui.model.json.JSONModel).setProperty(
        "/runtimeErrorExists",
        false
      );
    }
  }

  public clearDataFlowTableSelection() {
    // Do nothing
  }

  /**
   * Open View Settings Dialogs
   * @memberof DataFlowDetailsClass
   * @param dialogName - Name of the dialog to be opened
   */
  public openDialogs(dialogName: string) {
    const pDialog = this.getViewSettingsDialog(dialogName);
    pDialog?.open();
  }

  /**
   * Get View Settings Dialogs
   * @memberof DataFlowDetailsClass
   * @param dialogName - Name of the dialog to be opened
   * @returns View Settings Dialog
   */
  public getViewSettingsDialog(dialogName: string) {
    let pDialog = this.viewSettingsDialogs[dialogName];
    if (!pDialog) {
      const fragementId = require(`../view/fragment/${dialogName}.fragment.xml`);
      const dataflowModel = this.view.getModel("dataflowModel");
      pDialog = sap.ui.xmlfragment(dialogName, fragementId, this);
      pDialog.setModel(this.i18nModel, "i18n");
      pDialog.setModel(dataflowModel, "dataflowModel");
      this.viewSettingsDialogs[dialogName] = pDialog;
      this.view.addDependent(pDialog);
    }
    return pDialog;
  }

  /**
   * Open sub status details
   *
   * @public
   * @param {sap.ui.base.Event} oEvent
   * @param {string} subStatus
   */
  public openSubStatusDetail(oEvent: sap.ui.base.Event, subStatus: string, applicationId: string) {
    Utils.openSubStatusDetail.bind(this)(oEvent, subStatus, undefined, undefined, applicationId);
  }

  /**
   * Visibility formatter for run menu button
   *
   * @public
   * @param {boolean} isDataflow
   * @param {boolean} isSDPEnabled
   * @param {boolean} dataintegrationPrivilege
   * @param {boolean} isSpaceOpen
   * @param {boolean} privRemoteConnIsUpdate
   * @returns {boolean}
   */
  public executeMenuVisibilityFormatter(
    isDataflow: boolean,
    isSDPEnabled: boolean,
    dataintegrationPrivilege: boolean,
    isSpaceOpen: boolean,
    privRemoteConnIsUpdate: boolean
  ): boolean {
    if (!isDataflow) {
      return false;
    } else if (isSDPEnabled) {
      return dataintegrationPrivilege && isSpaceOpen;
    } else {
      return privRemoteConnIsUpdate;
    }
  }

  /**
   * Checks whether it is dataflow or transformation flow
   *
   * @public
   * @returns {boolean}
   */
  public isDataflow(): boolean {
    if (!this.showTransformationFlows) {
      return true;
    }
    return (this.view.getModel("dataflowModel") as sap.ui.model.json.JSONModel).getProperty("/isDataflow");
  }

  /**
   * Handle info icon press
   *
   * @public
   * @param {sap.ui.base.Event} oEvent
   * @param {string} [id="metricInfoIconPopover"]
   * @param {string} [text="METRICS_INFO_POPOVER_TEXT"]
   */
  public handleInfoIconPopoverPress(
    oEvent: sap.ui.base.Event,
    id = "metricInfoIconPopover",
    text = "METRICS_INFO_POPOVER_TEXT"
  ) {
    if (!this.isDataflow()) {
      text = "TF_METRICS_INFO_POPOVER_TEXT";
    }
    const oIcon = oEvent.getSource() as sap.ui.core.Icon;
    const oInfoPopover =
      (this.getView().byId(id) as sap.m.ResponsivePopover) ||
      new sap.m.ResponsivePopover(this.getView().getId() + `--${id}`, {
        showHeader: false,
        contentWidth: "15rem",
      });

    const oText =
      (this.getView().byId(`${id}--text`) as sap.m.Text) ||
      new sap.m.Text(oInfoPopover.getId() + "--text", {
        text: this.i18nModel.getResourceBundle().getText(text),
      }).addStyleClass("sapUiTinyMargin infoIconPopoverClass");
    oInfoPopover.addContent(oText);

    if (oIcon.getDependents().length === 0) {
      oIcon.addDependent(oInfoPopover);
    }
    oInfoPopover.openBy(oIcon);
  }

  /**
   * Reset water mark
   *
   * @public
   */
  public OnResetWatermark() {
    sap.m.MessageBox.confirm(this.i18nModel.getResourceBundle().getText("resetWatermarkWarningMsg"), {
      title: this.i18nModel.getResourceBundle().getText("RESET_WATERMARK"),
      actions: [this.i18nModel.getResourceBundle().getText("Reset"), sap.m.MessageBox.Action.CANCEL],
      emphasizedAction: this.i18nModel.getResourceBundle().getText("Reset"),
      onClose: async (chosenAction) => {
        if (chosenAction === this.i18nModel.getResourceBundle().getText("Reset")) {
          await this.runResetWatermarkTask(this.objectId, this.spaceId, Activity.RESET_WATERMARKS);
        }
      },
    });
  }

  /**
   * on run reset watermark task for transformation flow
   * @memberof DataFlowDetailsClass
   * @param {string} formattedName
   * @param {string} spaceId
   * @param {Activity} activity
   */
  public async runResetWatermarkTask(formattedName: string, spaceId: string, activity: Activity) {
    this.showBusyDialog(true);
    try {
      const response = await Utils.runTransformationFlow(formattedName, spaceId, activity);
      if (response?.status === "success" && response?.data?.taskLogId) {
        this.showBusyDialog(false);
        MessageHandler.success(this.i18nModel.getResourceBundle().getText("resetWatermarkSuccess"));
      }
    } catch (error) {
      const errorMsg = this.i18nModel.getResourceBundle().getText("resetWatermarkFail");
      MessageHandler.exception({ exception: error, message: errorMsg, id: "tfRunFailedErrorMsgbox" });
    } finally {
      this.showBusyDialog(false);
      this.onRefresh();
    }
  }

  /**
   * Fetches delta subscription info for TF
   *
   */
  public async fetchDeltaSubscriptions() {
    if (!this.isDataflow()) {
      const oDeltaTable: sap.m.Table = this.view.byId("deltaTableList") as sap.m.Table;
      try {
        oDeltaTable?.setBusy(true);
        oDeltaTable?.setBusyIndicatorDelay(0);
        const oResponse = await Utils.fetchDeltaSubscriptions(this.spaceId, this.objectId);
        if (Array.isArray(oResponse?.data)) {
          const dataflowModel = this.view.getModel("dataflowModel");
          dataflowModel?.setProperty("/isDeltaEnabled", true);
          for (const item of oResponse.data) {
            item.formattedHighWaterMark = this.formatDateTime(item.highWaterMark);
          }
          dataflowModel?.setProperty("/deltaSubscriptions", oResponse.data);
        }
      } catch (oError) {
        if (oError?.[0]?.responseJSON?.code === "trfRuntimeDef_not_deltaEnabled") {
          this.setIllustratedMsg(oDeltaTable);
        }
        const errorMsg = this.i18nModel.getResourceBundle().getText("trFlowNotDeltaEnabled");
        Logger.logInfo(errorMsg);
      } finally {
        oDeltaTable.setBusy(false);
      }
    }
  }

  /**
   * Formatter for visibility of batches tab in HANA runtime for transformation flow
   *
   * @param {boolean} isHANATFBatchProcessingEnabled
   * @param {boolean} isLargeSystemSpace
   * @param {boolean} isDataflow
   * @returns {boolean} if batches tab should be visible
   */
  public batchVisibilityFormatter(
    isHANATFBatchProcessingEnabled: boolean,
    isLargeSystemSpace: boolean,
    isDataflow: boolean
  ): boolean {
    return isHANATFBatchProcessingEnabled && !isLargeSystemSpace && !isDataflow;
  }

  /**
   * Fetches batches information for TF in HANA runtime
   *
   * @returns {Promise<{ isError: boolean }>}
   */
  public async fetchBatches(): Promise<{ isError: boolean }> {
    let isError = false;
    const batchesTab = this.view.byId("batchesTab");
    batchesTab["setBusy"](true);
    try {
      const isLargeSystemSpace = Utils.isLargeSystemSpace(this.spaceId);
      const isHANATFBatchProcessingEnabled = sap.ui
        .getCore()
        .getModel("featureflags")
        ?.getProperty("/DWCO_TRF_BATCHES");

      if (isHANATFBatchProcessingEnabled && !isLargeSystemSpace && !this.isDataflow()) {
        const oResponse = await Utils.fetchBatches(this.spaceId, this.objectId);
        const data = oResponse?.data;
        let businessName = "";
        if (data) {
          const partitioningColumnsObject = data.partitioningColumns || {};
          const partitioningColumnsArray = [];
          for (const key in partitioningColumnsObject) {
            const partitionColumn = partitioningColumnsObject[key];
            partitioningColumnsArray.push({
              column: key,
              ...partitionColumn,
            });
            if (key === data.column) {
              businessName = partitionColumn["@EndUserText.label"];
            }
          }
          if ((!data.column || data.column !== "") && businessName === "") {
            // Removed column scenario
            businessName = data.column;
          }
          const userPreference = this.view.getModel("settings").getProperty("/objectNameDisplay");
          (this.view.getModel("tfHanaBatchesModel") as sap.ui.model.json.JSONModel)?.setData(
            {
              batchSize: data.batchSize || 0,
              batchColumn: data.column || "",
              batchColumnBusinessName: businessName,
              batchColumnDisplayName:
                userPreference === ObjectNameDisplay.businessName ? businessName : data.column || "",
              isPartitioningColumnValid: data.isPartitioningColumnValid || false,
              onlyInitialLoad: data.onlyInitialLoad || false,
              partitioningColumns: partitioningColumnsArray,
              columnsText: this.getText("columnsText", [partitioningColumnsArray.length.toString()]),
              isTFPartitionedHANA: data.batchSize > 0 && data.column !== "" ? true : false,
            },
            true
          );
        } else {
          this.resetBatches();
        }
      }
    } catch (e) {
      sap.m.MessageBox.error(this.getText("getBatchesFailed"));
      isError = true;
    } finally {
      batchesTab["setBusy"](false);
      return {
        isError,
      };
    }
  }

  /**
   * Set the batches column business/technical name
   */
  public formatBatchesInformation() {
    const isHANATFBatchProcessingEnabled = sap.ui.getCore().getModel("featureflags")?.getProperty("/DWCO_TRF_BATCHES");
    if (isHANATFBatchProcessingEnabled) {
      const tfHanaBatchesModel = this.view.getModel("tfHanaBatchesModel") as sap.ui.model.json.JSONModel;
      const tfHanaBatchesModelData = tfHanaBatchesModel?.getData();
      if (tfHanaBatchesModelData?.isTFPartitionedHANA) {
        const showBusinessName =
          this.view.getModel("settings")?.getProperty("/objectNameDisplay") === ObjectNameDisplay.businessName;
        let displayName = tfHanaBatchesModelData?.batchColumn;
        if (
          showBusinessName &&
          tfHanaBatchesModelData?.batchColumnBusinessName &&
          tfHanaBatchesModelData.batchColumnBusinessName !== ""
        ) {
          displayName = tfHanaBatchesModelData.batchColumnBusinessName;
        }
        tfHanaBatchesModel?.setProperty("/batchColumnDisplayName", displayName);
      }
    }
  }

  /**
   * Deletes the existing batch data for TF in HANA runtime
   *
   */
  public async onDeleteBatches() {
    sap.m.MessageBox.show(this.getText("deleteConfirmationTxt"), {
      icon: sap.m.MessageBox.Icon.QUESTION,
      title: this.getText("Confirmation"),
      actions: [sap.m.MessageBox.Action.DELETE, sap.m.MessageBox.Action.CANCEL],
      initialFocus: sap.m.MessageBox.Action.DELETE,
      onClose: async (action) => {
        if (action === sap.m.MessageBox.Action.DELETE) {
          try {
            this.getView().setBusy(true);
            await Utils.deleteBatches(this.spaceId, this.objectId);
          } catch (e) {
            if (e && e.length > 0 && e[1] === "parsererror") {
              sap.m.MessageToast.show(this.getText("batchesDataDeleted"));
              this.getView().getModel("tfHanaBatchesModel")?.setProperty("/isTFPartitionedHANA", false);
            }
          } finally {
            this.getView().setBusy(false);
          }
        }
      },
    });
  }

  /**
   * Function to open the edit batches dialog for TF in HANA runtime
   *
   */
  public async onEditBatches() {
    const busyDialog = new sap.m.BusyDialog("loadingBatchesDialog", {
      text: this.getText("loadingBatchData"),
    });
    busyDialog.open();
    const fetchBatchesResponse = await this.fetchBatches();
    busyDialog.close();
    busyDialog.destroy();

    if (fetchBatchesResponse?.isError) {
      return;
    }

    const tfHanaBatchesModel = this.view.getModel("tfHanaBatchesModel") as sap.ui.model.json.JSONModel;
    const tfHanaBatchesModelData = tfHanaBatchesModel?.getData();
    tfHanaBatchesModel?.setData(
      {
        isInitialLoadSelected: tfHanaBatchesModelData?.onlyInitialLoad || false,
        inputBatchSize: tfHanaBatchesModelData?.batchSize || "",
      },
      true
    );
    if (!this.tfEditBatchesDialog) {
      const fragmentId = this.getView().createId("editBatchesFragment");
      const fragmentPath = require("../view/fragment/TFHANAEditBatches.fragment.xml");
      this.tfEditBatchesDialog = sap.ui.xmlfragment(fragmentId, fragmentPath, this);
      this.getView().addDependent(this.tfEditBatchesDialog);
    }

    const columnsTable = this.view.byId("editBatchesFragment--columnsTable") as sap.m.Table;
    for (const item of columnsTable?.getItems()) {
      const itemData = item.getBindingContext("tfHanaBatchesModel").getObject();
      if (itemData.column === tfHanaBatchesModelData?.batchColumn) {
        item.setSelected(true);
      } else {
        item.setSelected(false);
      }
    }
    this.tfEditBatchesDialog.open();
  }

  /**
   * Cancel of edit batches
   *
   */
  public onEditBatchesClose() {
    sap.m.MessageBox.show(this.getText("unsavedData"), {
      icon: sap.m.MessageBox.Icon.QUESTION,
      title: this.getText("Confirmation"),
      actions: [this.getText("back"), sap.m.MessageBox.Action.CANCEL],
      initialFocus: sap.m.MessageBox.Action.DELETE,
      onClose: async (action) => {
        if (action === this.getText("back")) {
          this.tfEditBatchesDialog.close();
        }
      },
    });
  }

  /**
   * Function to save batches on click of save in batches dialog for TF in HANA runtime
   *
   * @returns {void}
   */
  public async saveBatches() {
    const selectedColumn = this.getView().byId("editBatchesFragment--columnsTable")["getSelectedItem"]();
    if (!selectedColumn) {
      sap.m.MessageToast.show(this.getText("noColumnSelected"));
      return;
    }
    const selectedItem = selectedColumn.getBindingContext("tfHanaBatchesModel").getObject();
    const isInitialLoadSelected = this.getView().byId("editBatchesFragment--processInitialLoad")["getSelected"]();
    const inputBatchSize = this.getView().getModel("tfHanaBatchesModel")?.getProperty("/inputBatchSize");
    const regex = /[^0-9]/;
    if (inputBatchSize === "" || regex.test(inputBatchSize)) {
      sap.m.MessageToast.show(this.getText("batchSizeInvalid"));
      return;
    }
    if (inputBatchSize === 0) {
      sap.m.MessageToast.show(this.getText("zeroBatchSize"));
      return;
    }
    this.tfEditBatchesDialog.close();
    const editBatchesPayload: IEditBatchesPayload = {
      column: selectedItem["column"],
      batchSize: inputBatchSize,
      onlyInitialLoad: isInitialLoadSelected,
    };
    try {
      this.getView().setBusy(true);
      const response = await Utils.editBatches(this.spaceId, this.objectId, editBatchesPayload);
      if (response?.status === "success") {
        sap.m.MessageToast.show(this.getText("batchesDataSaved"));
        const data = response.data;
        if (data?.batchSize > 0 && data?.column !== "") {
          const userPreference = this.view.getModel("settings").getProperty("/objectNameDisplay");
          (this.view.getModel("tfHanaBatchesModel") as sap.ui.model.json.JSONModel)?.setData(
            {
              isTFPartitionedHANA: true,
              batchSize: data.batchSize,
              batchColumn: data.column,
              batchColumnBusinessName: selectedItem["@EndUserText.label"],
              batchColumnDisplayName:
                userPreference === ObjectNameDisplay.businessName ? selectedItem["@EndUserText.label"] : data.column,
              onlyInitialLoad: data.onlyInitialLoad || false,
              isPartitioningColumnValid: true,
            },
            true
          );
        } else {
          this.fetchBatches();
        }
      }
    } finally {
      this.getView().setBusy(false);
    }
  }

  /**
   * Handler to validate the batch size for TF in HANA runtime
   *
   * @param {sap.ui.base.Event} oEvent
   */
  public onSizeValueLiveChange(oEvent: sap.ui.base.Event) {
    const input = oEvent.getSource() as sap.m.Input;
    const currValue = input.getValue();
    const regex = /[^0-9]/;
    if (currValue === "" || regex.test(currValue)) {
      input.setValueState(sap.ui.core.ValueState.Error);
      input.setValueStateText(this.getText("invalidSizeValueLive"));
      return;
    }
    const value = +currValue;
    if (value === 0) {
      input.setValueState(sap.ui.core.ValueState.Error);
      input.setValueStateText(this.getText("zeroSizeValueLive"));
      return;
    }
    input.setValueState(sap.ui.core.ValueState.None);
    input.setValueStateText("");
    this.getView().getModel("tfHanaBatchesModel")?.setProperty("/inputBatchSize", value);
  }

  /**
   * Search bar for columns
   *
   * @param {sap.ui.base.Event} oEvent
   */
  public onSearchBatchColumn(oEvent: sap.ui.base.Event) {
    const searchText = oEvent.getSource()["getValue"]();

    const table = this.getView().byId("editBatchesFragment--columnsTable");
    const binding = table.getBinding("items") as sap.ui.model.json.JSONListBinding;
    let aFilters = new sap.ui.model.Filter({
      filters: [],
      and: true,
    });
    if (searchText && searchText?.length > 0) {
      aFilters = new sap.ui.model.Filter({
        filters: [
          new sap.ui.model.Filter("column", sap.ui.model.FilterOperator.Contains, searchText),
          new sap.ui.model.Filter("type", sap.ui.model.FilterOperator.Contains, searchText),
        ],
        and: false,
      });
    }
    binding.filter(aFilters);
  }

  /**
   * Disable save button in edit batches dialog if no column is available for batch processing
   *
   * @param partitioningColumns
   * @returns {boolean}
   */
  public isSaveBatchesEnabled(partitioningColumns) {
    if (partitioningColumns && partitioningColumns.length > 0) {
      return true;
    }
    return false;
  }

  /**
   * Formatter to return column names for the edit batches text
   *
   * @param {string} technicalName
   * @param {string} businessName
   * @param {ObjectNameDisplay} userPreference
   * @returns {string}
   */
  public columnTextFormatter(technicalName: string, businessName: string, userPreference: ObjectNameDisplay): string {
    if (userPreference === ObjectNameDisplay.businessName) {
      return businessName;
    }
    return technicalName;
  }

  /**
   * Formatter for enabling reset watermark option for TF
   *
   * @param {*} deltaSubscriptions
   * @returns {boolean}
   */
  public isResetWatermarkEnabled(deltaSubscriptions: IDeltaSubscriptions[]): boolean {
    const privilegeModel = sap.ui.getCore().getModel("privilege");
    const spaceLockModel = this.view.getModel("spaceLockModel");
    const hasIntegratorPriv = privilegeModel.getProperty("/DWC_DATAINTEGRATION/update");
    const isSpaceOpen = spaceLockModel.getProperty("/isSpaceOpen");
    if (hasIntegratorPriv && isSpaceOpen && deltaSubscriptions?.length > 0) {
      const highWaterMark = deltaSubscriptions[0].highWaterMark;
      if (highWaterMark) {
        return true;
      }
    }
    return false;
  }

  /**
   * Sets illustrated message for non delta transformation flows
   *
   * @param {sap.m.Table} oDeltaTable
   */
  public setIllustratedMsg(oDeltaTable: sap.m.Table) {
    this.view.getModel("dataflowModel")?.setProperty("/isDeltaEnabled", false);
    if (!this.illustratedMsg) {
      this.illustratedMsg = new sap.m.IllustratedMessage({
        id: this.createId("illustratedMsg"),
        title: this.i18nModel.getResourceBundle().getText("illustratedMsg"),
        illustrationType: sap.m.IllustratedMessageType.NoEntries,
        illustrationSize: sap.m.IllustratedMessageSize.Spot,
        enableDefaultTitleAndDescription: false,
      });
    }
    oDeltaTable?.setNoData(this.illustratedMsg);
  }

  /**
   * Resets delta capture settings for TF
   *
   * @private
   */
  private resetDeltaCaptureSettings() {
    if (!this.isDataflow()) {
      const dataflowModel = this.view.getModel("dataflowModel");
      dataflowModel?.setProperty("/deltaSubscriptions", []);
      dataflowModel?.setProperty("/isDeltaEnabled", false);
      const oDeltaTable: sap.m.Table = this.view.byId("deltaTableList") as sap.m.Table;
      oDeltaTable?.setNoData(this.i18nModel.getResourceBundle().getText("NO_DATA"));
    }
  }

  /**
   * Reset the batches model for TF in HANA runtime
   *
   */
  private resetBatches() {
    if (!this.isDataflow()) {
      const tfHanaBatchesModel = new sap.ui.model.json.JSONModel({
        isTFPartitionedHANA: false,
        batchSize: 0,
        batchColumn: "",
        isPartitioningColumnValid: false,
        onlyInitialLoad: false,
        partitioningColumns: {},
      });
      this.getView().setModel(tfHanaBatchesModel, "tfHanaBatchesModel");
    }
  }

  private async setRuntimeSettings(
    data: IRuntimeSettings,
    panelId: string,
    successMessage: string,
    errorMessage: string
  ) {
    this.getView().setBusy(true);
    const execModePanel = this.view.byId(panelId) as sap.m.Panel;
    const dataflowModel = this.getView().getModel("dataflowModel") as sap.ui.model.json.JSONModel;

    execModePanel?.setBusy(true);
    try {
      const oResponse = await Utils.setRuntimeSettings(this.spaceId, this.objectId, data);
      this.getView().setBusy(false);
      if (oResponse?.status === "success") {
        MessageHandler.success(this.i18nModel.getResourceBundle().getText(successMessage));
      }
    } catch (error) {
      dataflowModel?.setProperty("/isSparkSizeEnabled", false);
      MessageHandler.exception({
        exception: error,
        message: this.i18nModel.getResourceBundle().getText(errorMessage),
        id: errorMessage,
      });
    } finally {
      execModePanel?.setBusy(false);
      this.onRefresh();
    }
  }

  public async onSpaceDefaultSelected(event: sap.ui.base.Event) {
    const isSelected = event.getParameter("selected");
    let dataflowModel = this.getView().getModel("dataflowModel") as sap.ui.model.json.JSONModel;
    dataflowModel.setProperty("/isSpaceSelectionEnabled", false);
    if (isSelected) {
      this.isSpaceDefaultSelected = true; // Ensure flag is correctly updated
      this.setSparkOptionProperties("default", false);

      if (this.remoteSourceName !== null) {
        await this.setRuntimeSettings(
          { sparkRemoteSource: "" },
          "execModePanel",
          "saveSparkSettingsSuccess",
          "fetchSparkSettingsFailed"
        );
        dataflowModel.setProperty("/isSpaceSelectionEnabled", true);
      } else {
        dataflowModel.setProperty("/isSpaceSelectionEnabled", true);
      }
    }
  }

  public async onSparkApplicationChange(event: sap.ui.base.Event) {
    const sparkRemoteSource = event.getParameter("selectedItem")?.getProperty("key");
    let dataflowModel = this.getView().getModel("dataflowModel") as sap.ui.model.json.JSONModel;
    dataflowModel.setProperty("/isSpaceDefaultEnabled", false);
    const data: IRuntimeSettings = { sparkRemoteSource };

    await this.setRuntimeSettings(data, "execModePanel", "saveSparkSettingsSuccess", "fetchSparkSettingsFailed");
    dataflowModel.setProperty("/isSpaceDefaultEnabled", true);
  }

  public onDefineSettingsSelected(event: sap.ui.base.Event) {
    const isSelected = event.getParameter("selected");

    if (isSelected) {
      this.isSpaceDefaultSelected = false; // Ensure flag is correctly updated
      this.setSparkOptionProperties("newSettings", true);
    }
  }

  private setSparkOptionProperties(option: string, isEnabled: boolean) {
    const dataflowModel = this.getView().getModel("dataflowModel") as sap.ui.model.json.JSONModel;
    dataflowModel.setProperty("/sparkSelectedOption", option);
    dataflowModel.setProperty("/isSparkSizeEnabled", isEnabled);
  }

  private async fetchResources(): Promise<void> {
    if (!this.isDataflow()) {
      const execModePanel = this.view.byId("execModePanel") as sap.m.Panel;
      const dataflowModel = this.getView().getModel("dataflowModel") as sap.ui.model.json.JSONModel;

      execModePanel?.setBusy(true);
      execModePanel?.setBusyIndicatorDelay(0);

      try {
        const { data } = await getResourcesSpaces({ query: { spaceids: this.spaceId } });
        const spaceResources = data[this.spaceId];

        if (spaceResources) {
          const applications = spaceResources.spark?.applications || [];
          const sparkApplications = applications.map((app: any) => ({
            application: app.identifier.index,
            description: `${app.calculated?.["spark.cores.max"] || 0} CPU / ${(
              app.calculated?.["spark.memory.max"] || "0g"
            )
              .replace(/g$/i, "")
              .toUpperCase()} GB`,
          }));

          const defaultSpaceResourceValue =
            applications.find((app: any) => app.preference?.transformation)?.identifier?.index || "";
          dataflowModel.setProperty("/customSparkApplications", sparkApplications);
          dataflowModel.setProperty("/trfSparkSpaceDefault", defaultSpaceResourceValue);

          const isSparkSizeEnabled = Boolean(this.remoteSourceName);
          const selectedSparkApplication =
            this.remoteSourceName && !this.isSpaceDefaultSelected ? this.remoteSourceName : null;

          const sparkSelectedOption = selectedSparkApplication ? "newSettings" : "default";

          dataflowModel?.setProperty(
            "/selectedSparkApplication",
            selectedSparkApplication || defaultSpaceResourceValue
          );
          dataflowModel.setProperty("/sparkSelectedOption", sparkSelectedOption);
          dataflowModel.setProperty("/isSparkSizeEnabled", isSparkSizeEnabled);
        }
      } catch (error) {
        await this.handleResourceError("execModePanel", "fetchResourcesFailed", error);
      } finally {
        execModePanel?.setBusy(false);
      }
    }
  }

  private async handleResourceError(panelId: string, errorMessage: string, error: any) {
    const dataflowModel = this.getView().getModel("dataflowModel") as sap.ui.model.json.JSONModel;
    dataflowModel.setProperty("/isSpaceDefaultEnabled", false);
    dataflowModel.setProperty("/isSpaceSelectionEnabled", false);
    dataflowModel.setProperty("/isSparkSizeEnabled", false);

    MessageHandler.exception({ exception: error, message: this.i18nModel.getResourceBundle().getText(errorMessage) });

    const execModePanel = this.view.byId(panelId) as sap.m.Panel;
    execModePanel?.setBusy(false);
  }

  /**
   * Fetches execution mode from backend
   *
   * @public
   * @async
   */
  public async fetchRuntimeSettings() {
    if (!this.isDataflow()) {
      const execModePanel: sap.m.Panel = this.view.byId("execModePanel") as sap.m.Panel;
      const dataflowModel = execModePanel.getModel("dataflowModel");
      try {
        execModePanel?.setBusy(true);
        execModePanel?.setBusyIndicatorDelay(0);
        const oResponse = await Utils.fetchRuntimeSettings(this.spaceId, this.objectId);
        if (oResponse?.data && oResponse.data.hasOwnProperty("executionMode")) {
          this.remoteSourceName = oResponse?.data?.sparkRemoteSource === "" ? null : oResponse?.data?.sparkRemoteSource;
          dataflowModel?.setProperty("/executionMode", oResponse.data.executionMode);
          this.executionMode = oResponse.data.executionMode;
        }
      } catch (oError) {
        const errorMsg = this.i18nModel.getResourceBundle().getText("fetchSettingsFailed");
        MessageHandler.exception({ exception: oError, message: errorMsg });
        dataflowModel?.setProperty("/executionMode", this.executionMode);
      } finally {
        execModePanel?.setBusy(false);
      }
    }
  }

  /**
   * On changing execution mode in UI
   *
   * @param {sap.ui.base.Event} oEvent
   */
  public async onExecutionModeChange(oEvent: sap.ui.base.Event) {
    const executionMode = oEvent.getParameters().selectedIndex;
    const data: IRuntimeSettings = {
      executionMode: executionMode,
    };
    const execModePanel: sap.m.Panel = this.view.byId("execModePanel") as sap.m.Panel;
    execModePanel?.setBusy(true);
    try {
      const oResponse = await Utils.setRuntimeSettings(this.spaceId, this.objectId, data);
      if (oResponse?.status === "success") {
        this.executionMode = executionMode;
        execModePanel?.setBusy(false);
        MessageHandler.success(this.i18nModel.getResourceBundle().getText("saveExecutionModeSuccess"));
      }
    } catch (error) {
      const dataflowModel = execModePanel.getModel("dataflowModel");
      dataflowModel?.setProperty("/executionMode", this.executionMode);
      execModePanel?.setBusy(false);
      const errorMsg = this.i18nModel.getResourceBundle().getText("saveExecutionModeFailed");
      MessageHandler.exception({ exception: error, message: errorMsg, id: "saveExecutionModeFailed" });
    } finally {
      execModePanel?.setBusy(false);
      this.onRefresh();
    }
  }

  /**
   * Resets execution mode settings for TF
   *
   * @private
   */
  private resetExecutionModeSettings() {
    if (!this.isDataflow()) {
      const dataflowModel = this.view.getModel("dataflowModel");
      dataflowModel?.setProperty("/executionMode", this.executionMode);
    }
  }
}

export const DataFlows = smartExtend(
  DataFlowMonitorBase,
  "sap.cdw.components.flowmonitor.controller.DataFlowDetails",
  DataFlowDetailsClass
);

sap.ui.define("sap/cdw/components/flowmonitor/controller/DataFlowDetails.controller", [], function () {
  return DataFlows;
});
