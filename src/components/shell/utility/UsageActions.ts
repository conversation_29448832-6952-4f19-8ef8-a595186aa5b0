/** @format */

// FILEOWNER: [DSP General]
/* Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved. */
export enum SpaceManagementAction {
  UnlockSpace = "unlockSpace",
  EnableDataLakeForSpace = "enableDataLakeForSpace",
  DisableDataLakeForSpace = "disableDataLakeForSpace",
  EnableLsStorageForSpace = "enableLsStorageForSpace",
  DisableLsStorageForSpace = "disableLsStorageForSpace",
  EnableLsComputeForSpace = "enableLsComputeForSpace",
  DisableLsComputeForSpace = "disableLsComputeForSpace",
  CreateTimeData = "createTimeData",
  UpdateTimeData = "updateTimeData",
  DeleteTimeData = "deleteTimeData",
}

export enum DataBuilderAction {
  ShareArtefact = "shareArtefact",
  UnshareArtefact = "unshareArtefact",
  AddFromRepository = "addFromRepository",
  ImportFromRemoteSource = "importFromRemoteSource",
  UploadCSV = "uploadCSV",
  DeleteData = "Delete Data",
  OpenHierarchy = "openHierarchy",
  ShowDataView = "showDataView",
  ShowAssociationView = "showAssociationView",
  RefreshTable = "Refresh remote table",
  EditAnnotations = "Edit CSN Annotations",
  ModelValidation = "modelValidation",
  GenAISemanticOnView = "genAISemanticOnView",
  RevertGenAISemanticOnView = "revertGenAISemanticOnView",
}

export enum AnalysisUsersAction {
  CreateAnalysisUser = "createAnalysisUser",
  DeleteAnalysisUser = "deleteAnalysisUser",
  ResetAnalysisUser = "resetAnalysisUser",
  ReactivateAnalysisUser = "reactivateAnalysisUser",
  UpdateAnalysisUser = "updateAnalysisUser",
}

export enum DatabaseUserGroupAction {
  CreateDatabaseUserGroup = "createDatabaseUserGroup",
  DeleteDatabaseUserGroup = "deleteDatabaseUserGroup",
}

export enum MdmDataPreviewActions {
  OpenColumnSetting = "mdm_openColumnSettings",
  RefreshDataPreview = "mdm_refreshDataPreview",
  CloseDataPreview = "mdm_closeDataPreview",
  OpenDataEditor = "mdm_openDataEditor",
}

export enum DataDeletionAction {
  OnDemandDeletion = "onDemandDeletion",
  LocalTableObjectRefresh = "localTableObjectRefresh",
  OpenDataEditor = "openDataEditor",
  OpenDataBuilder = "openDataBuilder",
  CreateDataDeleteSchedule = "createDataDeleteSchedule",
  DeleteLocalTableSchedule = "deleteLocalTableSchedule",
}
