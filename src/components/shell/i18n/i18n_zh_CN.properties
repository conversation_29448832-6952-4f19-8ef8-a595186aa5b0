#~~~~~~~~~~~ Old Texts for context object ~~~~~~~~~~~~~~~~~~~
@managespaces=空间管理器
@persistence=持久性
@businessCatalogs=业务目录
@searchToInsight=搜索到洞察
@application=应用程序
@remotesource=连接
@type=类型

#~~~~~~~~~~~ About dialog ~~~~~~~~~~~~~~~~~~~

#XTIT: Title for about/ information dialog
aboutDialogTitle=关于

#XTIT: Title for mroe/ detailed about information
moreAreaTitle=更多

#XBUT: Close button for about/information dialog
aboutDialogClose=关闭

#XBUT: Ok button for about/information dialog
aboutDialogOk=确定

#XFLD: Version label
aboutDialogVersion=版本

#XFLD: Platform Version label
aboutDialogPlatformVersion=平台版本

#XFLD: Commit label
aboutDialogCommit=提交

#XFLD: Repository Service Release Version label
aboutDialogRepoServiceReleaseVersion=资源库服务版本

#XFLD: Build date label
aboutDialogBuildDate=内部版本日期

#XFLD: Tenant identifier
aboutDialogTenant=租户

#XFLD: Runtime database identifier
aboutDialogDatabaseId=数据库

#XFLD: Repository database version
aboutDialogRepoVersion=资源库数据库版本

#XFLD: Runtime database version
aboutDialogDbVersion=数据库版本

#XFLD: Runtime database latest avaliable patch version
aboutDialogDbLatestPatch=最新数据库补丁版本

#XFLD: Runtime database latest avaliable patch version
aboutDialogUpgradePatch=触发补丁升级

#XFLD: Restart HANA Cloud button
aboutRestartHANACloud=重新启动 SAP HANA Cloud

#XMSG: Restart HANA Cloud message for when restart can be performed
aboutDialogRestartHANACloudBegin=重启

#XMSG: Restart HANA Cloud confirmation dialog
aboutDialogRestartHANACloudConfirmationMessageBoxText=重新启动 SAP HANA Cloud 会导致短暂停机，此时与 SAP Datasphere 的单连接可能会中断。建议在非工作时间重启。

#XMSG: Patch upgrade message for when the upgrade can be performed normally
aboutDialogUpgradeBegin=开始升级

#XMSG: Patch upgrade message for when the last upgrade attempt was not sucessfull
aboutDialogLastUpgradeFailed=没能执行 SAP HANA Cloud 升级。请重试。

#XMSG: Patch upgrade message for when the current patch version is already the latest
aboutDialogPatchAlreadyTheLatest=已安装最新版的 SAP HANA Cloud。

#XMSG: Patch upgrade message for when the customer database is busy
aboutDialogOperationInProgress=正在 SAP HANA 数据库上进行一项操作。

#XMSG: Patch upgrade message for when the tenant is internal and can't be upgraded manually
aboutDialogNotAvailableForInternalTenant=不适用于内部租户

#XMSG: Patch upgrade confirmation dialog
aboutDialogPatchUpgradeConfirmationMessageBoxText=升级 SAP HANA Cloud 会导致短暂停机，此时与 SAP Datasphere 的单连接可能会中断。建议在非工作时间升级。

#XMSG: Patch upgrade message for when the customer requests a patch upgrade
aboutDialogPatchUpgradeInProgress=正在升级 SAP HANA 数据库补丁。

#XFLD: Seal service version
aboutDialogSealServiceVersion=部署服务版本

#XFLD: Data Integration Runtime Version
aboutDialogDisRuntimeVersion=数据集成运行时版本

#XMSG: Version information not available
aboutDialogVersionRetrievalError=版本信息不可用。

#XFLD: Replication/Data Flow NAT IP (egress)
aboutDialogOutboundIPAddress=复制/数据流 NAT IP（出口）

#XFLD: SAP HANA Cloud NAT IP (egress)
aboutDialogOutboundHanaIPAddress=SAP HANA Cloud NAT IP（出口）

#XMSG: Version information not available
aboutDialogIPAddressRetrievalError=信息不可用。

#XMSG: Error while loading version data
aboutDialogLoadVersionError=加载版本信息时发生错误。

#XFLD Label for subnet ID
aboutDialogSubnetIdLabel=虚拟网络子网 ID（Microsoft Azure）

#XMSG Subnet ID not available
aboutDialogSubnetIdNotAvailable=信息不可用。

#XMSG: Placeholder message, in case the real version for "Repository Release",
#"Database Release" cannot be retrieved.
currentlyNotAvailable=当前不可用。

#~~~~~~~~~~~ Error Messages ~~~~~~~~~~~~~~~~~~~
#XMSG: error while fetching navigation data
errorNavigationFetch=获取导航数据时出错。{0}

#XMSG: error while resolving hash key for navigation
errorNavNotSupported=不支持导航至 {0}。

#XMSG: error after applying hash key to browser
errorSetHash=无法访问导航目标 {0}。

#XMSG: Error when iFrame source could not be loaded
errorIFrame=访问所需资源时出错。

#XMSG: Error when illegal navigation is invoked
illegalNavigation=无法导航到所需资源。

#~~~~~~~~~~~ Support Dialog ~~~~~~~~~~~~~~~~~~~
#XBUT Download Log button for help center/support popover
downloadLog=下载日志
#XBUT Create Support User Log button for help center/support popover
createSupportUser=创建支持用户
#XTIT Title for support popover
supportPopoverTitle=支持
#XFLD Description for create support user button
createSupportUserDescription=创建 SAP Support Engineer 支持用户，以解决你的系统中的问题。系统会向 SAP 支持人员自动发送电子邮件，告知新建的用户。
#XFLD Description for download log button
downloadLogDescription=下载浏览器会话的日志，以解决最终用户问题。
#XFLD Create Support User content text (copied from SAC)
createSupportUserContentText=你将创建支持用户，SAP 支持人员将其用于解决你的问题。问题解决后，你可以删除该支持用户。

#~~~~~~~~~~~ Space Selection Dialog ~~~~~~~~~~~~~~~~~~~
#XTIT Dialog Header
selectSpace=选择空间
#XTOL Tooltip for SearchField inside Dialog
dialogSearch=搜索空间
#XTOL Tooltip for locked space tile in the dialog
txtLockedSpace=此空间已锁定，无法在其中创建实体。
#XTOL Tooltip for Sort Button
dialogSortSpaces=按升序/降序排列空间
#XTOL Tooltip for Sort Button
dialogSortAscending=按升序排列空间
#XTOL Tooltip for Sort Button
dialogSortDescending=按降序排列空间
#XBUT Text for Dialog Cancel Button
cancel=取消
#XTXT: Info message for a locked space
spaceLockedInfo=这个空间已锁定，只能在只读模式下使用。
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB

#~~~~~~~~~~~ Request Support User~~~~~~~~~~~~~~
#XBUT, 15 to close the dialog
btnOkSupportUser=确定
#XBUT, 15 to close the dialog
btnCancelSupportUser=取消

#~~~~~~~~~~~ Feedback Popover ~~~~~~~~~~~~~~~~~~~
#XTIT: UserGreeting title for the feedback popover
feedbackPopoverTitleUserGreeting= {0}，您好！
#XMSG: Title message for the feedback popover
feedbackPopoverTitle=我们期待您的反馈。
#XTIT: Title for the long survey
longSurveyHeader=用户体验调查
#XFLD: A badge of the long survey
longSurveyBadge=新
#XFLD: A note that shows how long the survey is available until
longSurveyDeadline=截止日期 {0}
#XMSG: A message that shows the description of the long survey
longSurveyDescription=诚邀您抽出 5 分钟时间完成一个小调查，帮助我们改进产品体验！调查将在新选项卡中打开。
#XBUT: Text for the feedback button
longSurveyButtonText=参与调查
#XTOL Tooltip for the feedback button
longSurveyButtonTooltip=Qualtrics 用户体验调查的外部链接

#XTIT: Title for the short feedback survey
giveFeedbackHeader=分享您的反馈
#XMSG: A message that shows the description of the short feedback survey
giveFeedbackDescription=您的意见很重要。欢迎随时与我们分享您的想法！
#XBUT: Text for the feedback button
giveFeedbackButtonText=提供反馈
#XTOL Tooltip for the feedback button
giveFeedbackButtonTooltip=提供反馈

#XLNK Tooltip for notifications for accessibility
notificationsTooltip=通知


#~~~~~~~~~~~ Joule Info Popover ~~~~~~~~~~~~~~~~~~~
#XMSG: Text for the joule deactivation in the joule info popover
jouleDeactivatedInfoPopoverText=Joule 不再可用，因为管理员已将其取消激活。
