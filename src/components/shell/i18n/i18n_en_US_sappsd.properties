#~~~~~~~~~~~ Old Texts for context object ~~~~~~~~~~~~~~~~~~~
@managespaces=[[[Ŝρąċē Μąŋąğēŗ∙∙∙∙∙∙]]]
@persistence=[[[Ƥēŗşįşţēŋċē∙∙∙∙∙∙∙∙]]]
@businessCatalogs=[[[Ɓűşįŋēşş Ĉąţąĺŏğ∙∙∙∙∙∙∙∙]]]
@searchToInsight=[[[Ŝēąŗċĥ ţŏ Ĭŋşįğĥţ∙∙∙∙∙∙∙]]]
@application=[[[Āρρĺįċąţįŏŋ∙∙∙∙∙∙∙∙]]]
@remotesource=[[[Ĉŏŋŋēċţįŏŋş∙∙∙∙∙∙∙∙]]]
@type=[[[Ţŷρē]]]

#~~~~~~~~~~~ About dialog ~~~~~~~~~~~~~~~~~~~

#XTIT: Title for about/ information dialog
aboutDialogTitle=[[[Āƃŏűţ∙∙∙∙∙∙∙∙∙]]]

#XTIT: Title for mroe/ detailed about information
moreAreaTitle=[[[Μŏŗē]]]

#XBUT: Close button for about/information dialog
aboutDialogClose=[[[Ĉĺŏşē∙∙∙∙∙∙∙∙∙]]]

#XBUT: Ok button for about/information dialog
aboutDialogOk=[[[Ŏķ∙∙]]]

#XFLD: Version label
aboutDialogVersion=[[[Ʋēŗşįŏŋ∙∙∙∙∙∙∙]]]

#XFLD: Platform Version label
aboutDialogPlatformVersion=[[[Ƥĺąţƒŏŗɱ Ʋēŗşįŏŋ∙∙∙∙∙∙∙∙]]]

#XFLD: Commit label
aboutDialogCommit=[[[Ĉŏɱɱįţ∙∙∙∙∙∙∙∙]]]

#XFLD: Repository Service Release Version label
aboutDialogRepoServiceReleaseVersion=[[[Řēρŏşįţŏŗŷ Ŝēŗʋįċē Ʋēŗşįŏŋ∙∙∙∙∙∙∙]]]

#XFLD: Build date label
aboutDialogBuildDate=[[[Ɓűįĺƌ Ďąţē∙∙∙∙]]]

#XFLD: Tenant identifier
aboutDialogTenant=[[[Ţēŋąŋţ∙∙∙∙∙∙∙∙]]]

#XFLD: Runtime database identifier
aboutDialogDatabaseId=[[[Ďąţąƃąşē∙∙∙∙∙∙]]]

#XFLD: Repository database version
aboutDialogRepoVersion=[[[Řēρŏşįţŏŗŷ Ďąţąƃąşē Řēĺēąşē∙∙∙∙∙∙∙∙]]]

#XFLD: Runtime database version
aboutDialogDbVersion=[[[Ďąţąƃąşē Řēĺēąşē∙∙∙∙∙∙∙∙]]]

#XFLD: Runtime database latest avaliable patch version
aboutDialogDbLatestPatch=[[[Ļąţēşţ Ďąţąƃąşē Ƥąţċĥ Řēĺēąşē∙∙∙∙∙∙∙∙∙]]]

#XFLD: Runtime database latest avaliable patch version
aboutDialogUpgradePatch=[[[Ţŗįğğēŗ Ƥąţċĥ Ůρğŗąƌē∙∙∙∙∙]]]

#XFLD: Restart HANA Cloud button
aboutRestartHANACloud=[[[Řēşţąŗţ ŜĀƤ ĤĀŃĀ Ĉĺŏűƌ∙∙∙∙∙]]]

#XMSG: Restart HANA Cloud message for when restart can be performed
aboutDialogRestartHANACloudBegin=[[[Řēşţąŗţ∙∙∙∙∙∙∙]]]

#XMSG: Restart HANA Cloud confirmation dialog
aboutDialogRestartHANACloudConfirmationMessageBoxText=[[[Řēşţąŗţįŋğ ŜĀƤ ĤĀŃĀ Ĉĺŏűƌ ŵįĺĺ ċąűşē ą şĥŏŗţ ρēŗįŏƌ ŏƒ ƌŏŵŋţįɱē ŵĥēŗē şįŋğĺē ċŏŋŋēċţįŏŋş ţŏ ŜĀƤ Ďąţąşρĥēŗē ɱąŷ ƒąįĺ. Ĉŏŋşįƌēŗ ŗēşţąŗţįŋğ ŏűţşįƌē ŵŏŗķįŋğ ĥŏűŗş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XMSG: Patch upgrade message for when the upgrade can be performed normally
aboutDialogUpgradeBegin=[[[Ɓēğįŋ űρğŗąƌē∙∙∙∙∙∙]]]

#XMSG: Patch upgrade message for when the last upgrade attempt was not sucessfull
aboutDialogLastUpgradeFailed=[[[Ůŋąƃĺē ţŏ ρēŗƒŏŗɱ ŜĀƤ ĤĀŃĀ Ĉĺŏűƌ űρğŗąƌē. Ƥĺēąşē ţŗŷ ąğąįŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XMSG: Patch upgrade message for when the current patch version is already the latest
aboutDialogPatchAlreadyTheLatest=[[[Ŷŏű ąĺŗēąƌŷ ĥąʋē ţĥē ĺąţēşţ ʋēŗşįŏŋ ŏƒ ŜĀƤ ĤĀŃĀ Ĉĺŏűƌ įŋşţąĺĺēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XMSG: Patch upgrade message for when the customer database is busy
aboutDialogOperationInProgress=[[[Āŋ ŏρēŗąţįŏŋ įş įŋ ρŗŏğŗēşş ŏŋ ŷŏűŗ ŜĀƤ ĤĀŃĀ ƌąţąƃąşē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XMSG: Patch upgrade message for when the tenant is internal and can't be upgraded manually
aboutDialogNotAvailableForInternalTenant=[[[Ńŏţ ąʋąįĺąƃĺē ƒŏŗ įŋţēŗŋąĺ ţēŋąŋţş∙∙∙∙∙∙∙∙∙∙∙]]]

#XMSG: Patch upgrade confirmation dialog
aboutDialogPatchUpgradeConfirmationMessageBoxText=[[[Ůρğŗąƌįŋğ ŜĀƤ ĤĀŃĀ Ĉĺŏűƌ ŵįĺĺ ċąűşē ą şĥŏŗţ ρēŗįŏƌ ŏƒ ƌŏŵŋţįɱē ŵĥēŗē şįŋğĺē ċŏŋŋēċţįŏŋş ţŏ ŜĀƤ Ďąţąşρĥēŗē ɱąŷ ƒąįĺ. Ĉŏŋşįƌēŗ űρğŗąƌįŋğ ŏűţşįƌē ŵŏŗķįŋğ ĥŏűŗş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XMSG: Patch upgrade message for when the customer requests a patch upgrade
aboutDialogPatchUpgradeInProgress=[[[Ŷŏűŗ ŜĀƤ ĤĀŃĀ ƌąţąƃąşē ρąţċĥ űρğŗąƌē įş įŋ ρŗŏğŗēşş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XFLD: Seal service version
aboutDialogSealServiceVersion=[[[Ďēρĺŏŷɱēŋţ Ŝēŗʋįċē Ʋēŗşįŏŋ∙∙∙∙∙∙∙]]]

#XFLD: Data Integration Runtime Version
aboutDialogDisRuntimeVersion=[[[Ďąţą Ĭŋţēğŗąţįŏŋ Řűŋţįɱē Ʋēŗşįŏŋ∙∙∙∙∙∙∙∙∙∙]]]

#XMSG: Version information not available
aboutDialogVersionRetrievalError=[[[Ʋēŗşįŏŋ įŋƒŏŗɱąţįŏŋ įş ŋŏţ ąʋąįĺąƃĺē.∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XFLD: Replication/Data Flow NAT IP (egress)
aboutDialogOutboundIPAddress=[[[Řēρĺįċąţįŏŋ/Ďąţą Ƒĺŏŵ ŃĀŢ ĬƤ (ēğŗēşş)∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XFLD: SAP HANA Cloud NAT IP (egress)
aboutDialogOutboundHanaIPAddress=[[[ŜĀƤ ĤĀŃĀ Ĉĺŏűƌ ŃĀŢ ĬƤ (ēğŗēşş)∙∙∙∙∙∙∙∙∙]]]

#XMSG: Version information not available
aboutDialogIPAddressRetrievalError=[[[Ĭŋƒŏŗɱąţįŏŋ įş ŋŏţ ąʋąįĺąƃĺē.∙∙∙∙∙∙∙∙∙]]]

#XMSG: Error while loading version data
aboutDialogLoadVersionError=[[[Āŋ ēŗŗŏŗ ŏċċűŗŗēƌ ŵĥįĺē ĺŏąƌįŋğ ţĥē ʋēŗşįŏŋ įŋƒŏŗɱąţįŏŋ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XFLD Label for subnet ID
aboutDialogSubnetIdLabel=[[[Ʋįŗţűąĺ Ńēţŵŏŗķ Ŝűƃŋēţ ĬĎ (Μįċŗŏşŏƒţ Āžűŗē)∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XMSG Subnet ID not available
aboutDialogSubnetIdNotAvailable=[[[Ĭŋƒŏŗɱąţįŏŋ įş ŋŏţ ąʋąįĺąƃĺē.∙∙∙∙∙∙∙∙∙]]]

#XMSG: Placeholder message, in case the real version for "Repository Release",
#"Database Release" cannot be retrieved.
currentlyNotAvailable=[[[Ĉűŗŗēŋţĺŷ ŋŏţ ąʋąįĺąƃĺē.∙∙∙∙∙∙]]]

#~~~~~~~~~~~ Error Messages ~~~~~~~~~~~~~~~~~~~
#XMSG: error while fetching navigation data
errorNavigationFetch=[[[Ĕŗŗŏŗ ŵĥįĺē ƒēţċĥįŋğ ŋąʋįğąţįŏŋ ƌąţą. {0}]]]

#XMSG: error while resolving hash key for navigation
errorNavNotSupported=[[[Ńąʋįğąţįŏŋ ţŏ {0} įş ŋŏţ şűρρŏŗţēƌ.]]]

#XMSG: error after applying hash key to browser
errorSetHash=[[[Ńąʋįğąţįŏŋ ţąŗğēţ {0} ċąŋŋŏţ ƃē ŗēąċĥēƌ.]]]

#XMSG: Error when iFrame source could not be loaded
errorIFrame=[[[Ŝŏɱēţĥįŋğ ŵēŋţ ŵŗŏŋğ ŵĥįĺē ąċċēşşįŋğ ţĥē ŗēƣűįŗēƌ ŗēşŏűŗċē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XMSG: Error when illegal navigation is invoked
illegalNavigation=[[[Ţĥē ŗēƣűįŗēƌ ŗēşŏűŗċē ċąŋŋŏţ ƃē ŋąʋįğąţēƌ ţŏ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#~~~~~~~~~~~ Support Dialog ~~~~~~~~~~~~~~~~~~~
#XBUT Download Log button for help center/support popover
downloadLog=[[[Ďŏŵŋĺŏąƌ Ļŏğ∙∙∙∙∙∙∙]]]
#XBUT Create Support User Log button for help center/support popover
createSupportUser=[[[Ĉŗēąţē Ŝűρρŏŗţ Ůşēŗ∙∙∙∙∙]]]
#XTIT Title for support popover
supportPopoverTitle=[[[Ŝűρρŏŗţ∙∙∙∙∙∙∙]]]
#XFLD Description for create support user button
createSupportUserDescription=[[[Ĉŗēąţē ą şűρρŏŗţ űşēŗ ƒŏŗ ąŋ ŜĀƤ Ŝűρρŏŗţ Ĕŋğįŋēēŗ ţŏ ţŗŏűƃĺēşĥŏŏţ ą ρŗŏƃĺēɱ ŏŋ ŷŏűŗ şŷşţēɱ. Āŋ ēɱąįĺ įş ąűţŏɱąţįċąĺĺŷ şēŋţ ţŏ ŜĀƤ Ŝűρρŏŗţ ţŏ ŋŏţįƒŷ ţĥēɱ ŏƒ ţĥē ŋēŵĺŷ ċŗēąţēƌ űşēŗ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD Description for download log button
downloadLogDescription=[[[Ďŏŵŋĺŏąƌ ţĥē ĺŏğ ƒŏŗ ţĥē ƃŗŏŵşēŗ şēşşįŏŋ ţŏ ţŗŏűƃĺēşĥŏŏţ ēŋƌ-űşēŗ įşşűēş.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD Create Support User content text (copied from SAC)
createSupportUserContentText=[[[Ŷŏű ąŗē ąƃŏűţ ţŏ ċŗēąţē ą şűρρŏŗţ űşēŗ ţĥąţ ŵįĺĺ ƃē űşēƌ ƃŷ ŜĀƤ Ŝűρρŏŗţ ţŏ ţŗŏűƃĺēşĥŏŏţ ŷŏűŗ ρŗŏƃĺēɱ. Ŷŏű ċąŋ ƌēĺēţē ţĥįş şűρρŏŗţ űşēŗ ŏŋċē ŷŏűŗ įşşűē ĥąş ƃēēŋ ŗēşŏĺʋēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#~~~~~~~~~~~ Space Selection Dialog ~~~~~~~~~~~~~~~~~~~
#XTIT Dialog Header
selectSpace=[[[Ŝēĺēċţ Ŝρąċē∙∙∙∙∙∙∙]]]
#XTOL Tooltip for SearchField inside Dialog
dialogSearch=[[[Ŝēąŗċĥ ƒŏŗ Ŝρąċēş∙∙∙∙∙∙∙]]]
#XTOL Tooltip for locked space tile in the dialog
txtLockedSpace=[[[Ŷŏű ċąŋŋŏţ ċŗēąţē ąŋ ēŋţįţŷ įŋ ţĥįş şρąċē ƃēċąűşē įţ įş ĺŏċķēƌ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XTOL Tooltip for Sort Button
dialogSortSpaces=[[[Ŝŏŗţ Ŝρąċēş Āşċēŋƌįŋğ/Ďēşċēŋƌįŋğ∙∙∙∙∙∙∙∙∙∙]]]
#XTOL Tooltip for Sort Button
dialogSortAscending=[[[Ŝŏŗţ Ŝρąċēş Āşċēŋƌįŋğ∙∙∙∙∙]]]
#XTOL Tooltip for Sort Button
dialogSortDescending=[[[Ŝŏŗţ Ŝρąċēş Ďēşċēŋƌįŋğ∙∙∙∙∙]]]
#XBUT Text for Dialog Cancel Button
cancel=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]
#XTXT: Info message for a locked space
spaceLockedInfo=[[[Ţĥįş şρąċē įş ĺŏċķēƌ ąŋƌ įş ąʋąįĺąƃĺē ŏŋĺŷ įŋ ŗēąƌ-ŏŋĺŷ ɱŏƌē.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=[[[< 1 ĢƁ∙∙∙∙∙∙∙∙]]]

#~~~~~~~~~~~ Request Support User~~~~~~~~~~~~~~
#XBUT, 15 to close the dialog
btnOkSupportUser=[[[Ŏķ∙∙]]]
#XBUT, 15 to close the dialog
btnCancelSupportUser=[[[Ĉąŋċēĺ∙∙∙∙∙∙∙∙]]]

#~~~~~~~~~~~ Feedback Popover ~~~~~~~~~~~~~~~~~~~
#XTIT: UserGreeting title for the feedback popover
feedbackPopoverTitleUserGreeting=[[[Ĥį {0},]]]
#XMSG: Title message for the feedback popover
feedbackPopoverTitle=[[[Ţēĺĺ űş ŵĥąţ’ş ŏŋ ŷŏűŗ ɱįŋƌ.∙∙∙∙∙∙∙∙]]]
#XTIT: Title for the long survey
longSurveyHeader=[[[Ůşēŗ Ĕχρēŗįēŋċē Ŝűŗʋēŷ∙∙∙∙∙]]]
#XFLD: A badge of the long survey
longSurveyBadge=[[[ŃĔŴ∙]]]
#XFLD: A note that shows how long the survey is available until
longSurveyDeadline=[[[Āʋąįĺąƃĺē űŋţįĺ {0}]]]
#XMSG: A message that shows the description of the long survey
longSurveyDescription=[[[Ĉŏɱρĺēţē ą 5-ɱįŋűţē şűŗʋēŷ ąŋƌ ĥēĺρ űş įɱρŗŏʋē ŷŏűŗ ρŗŏƌűċţ ēχρēŗįēŋċē! Ţĥē şűŗʋēŷ ŵįĺĺ ŏρēŋ įŋ ą ŋēŵ ţąƃ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: Text for the feedback button
longSurveyButtonText=[[[Ţąķē Ŝűŗʋēŷ∙∙∙∙∙∙∙∙]]]
#XTOL Tooltip for the feedback button
longSurveyButtonTooltip=[[[Ĕχţēŗŋąĺ Ļįŋķ ţŏ Ǭűąĺţŗįċş Ůşēŗ Ĕχρēŗįēŋċē Ŝűŗʋēŷ∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]

#XTIT: Title for the short feedback survey
giveFeedbackHeader=[[[Ŝĥąŗē ŷŏűŗ Ƒēēƌƃąċķ∙∙∙∙∙]]]
#XMSG: A message that shows the description of the short feedback survey
giveFeedbackDescription=[[[Ŷŏűŗ ŏρįŋįŏŋ ɱąţţēŗş. Ŝĥąŗē ŷŏűŗ ţĥŏűğĥţş ŵįţĥ űş ąţ ąŋŷ ţįɱē!∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
#XBUT: Text for the feedback button
giveFeedbackButtonText=[[[Ģįʋē Ƒēēƌƃąċķ∙∙∙∙∙∙]]]
#XTOL Tooltip for the feedback button
giveFeedbackButtonTooltip=[[[Ģįʋē Ƒēēƌƃąċķ∙∙∙∙∙∙]]]

#XLNK Tooltip for notifications for accessibility
notificationsTooltip=[[[Ńŏţįƒįċąţįŏŋş∙∙∙∙∙∙]]]


#~~~~~~~~~~~ Joule Info Popover ~~~~~~~~~~~~~~~~~~~
#XMSG: Text for the joule deactivation in the joule info popover
jouleDeactivatedInfoPopoverText=[[[Ĵŏűĺē įş ŋŏ ĺŏŋğēŗ ąʋąįĺąƃĺē ƃēċąűşē ąŋ ąƌɱįŋįşţŗąţŏŗ ĥąş ƌēąċţįʋąţēƌ įţ.∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙∙]]]
