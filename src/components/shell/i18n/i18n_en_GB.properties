#~~~~~~~~~~~ Old Texts for context object ~~~~~~~~~~~~~~~~~~~
@managespaces=Space Manager
@persistence=Persistence
@businessCatalogs=Business Catalogue
@searchToInsight=Search to Insight
@application=Application
@remotesource=Connections
@type=Type

#~~~~~~~~~~~ About dialog ~~~~~~~~~~~~~~~~~~~

#XTIT: Title for about/ information dialog
aboutDialogTitle=About

#XTIT: Title for mroe/ detailed about information
moreAreaTitle=More

#XBUT: Close button for about/information dialog
aboutDialogClose=Close

#XBUT: Ok button for about/information dialog
aboutDialogOk=OK

#XFLD: Version label
aboutDialogVersion=Version

#XFLD: Platform Version label
aboutDialogPlatformVersion=Platform Version

#XFLD: Commit label
aboutDialogCommit=Commit

#XFLD: Repository Service Release Version label
aboutDialogRepoServiceReleaseVersion=Repository Service Version

#XFLD: Build date label
aboutDialogBuildDate=Build Date

#XFLD: Tenant identifier
aboutDialogTenant=Tenant

#XFLD: Runtime database identifier
aboutDialogDatabaseId=Database

#XFLD: Repository database version
aboutDialogRepoVersion=Repository Database Release

#XFLD: Runtime database version
aboutDialogDbVersion=Database Release

#XFLD: Runtime database latest avaliable patch version
aboutDialogDbLatestPatch=Latest Database Patch Release

#XFLD: Runtime database latest avaliable patch version
aboutDialogUpgradePatch=Trigger Patch Upgrade

#XFLD: Restart HANA Cloud button
aboutRestartHANACloud=Restart SAP HANA Cloud

#XMSG: Restart HANA Cloud message for when restart can be performed
aboutDialogRestartHANACloudBegin=Restart

#XMSG: Restart HANA Cloud confirmation dialog
aboutDialogRestartHANACloudConfirmationMessageBoxText=Restarting SAP HANA Cloud will cause a short period of downtime where single connections to SAP Datasphere may fail. Consider restarting outside working hours.

#XMSG: Patch upgrade message for when the upgrade can be performed normally
aboutDialogUpgradeBegin=Begin upgrade

#XMSG: Patch upgrade message for when the last upgrade attempt was not sucessfull
aboutDialogLastUpgradeFailed=Unable to perform SAP HANA Cloud upgrade. Please try again.

#XMSG: Patch upgrade message for when the current patch version is already the latest
aboutDialogPatchAlreadyTheLatest=You already have the latest version of SAP HANA Cloud installed.

#XMSG: Patch upgrade message for when the customer database is busy
aboutDialogOperationInProgress=An operation is in progress on your SAP HANA database.

#XMSG: Patch upgrade message for when the tenant is internal and can't be upgraded manually
aboutDialogNotAvailableForInternalTenant=Not available for internal tenants

#XMSG: Patch upgrade confirmation dialog
aboutDialogPatchUpgradeConfirmationMessageBoxText=Upgrading SAP HANA Cloud will cause a short period of downtime where single connections to SAP Datasphere may fail. Consider upgrading outside working hours.

#XMSG: Patch upgrade message for when the customer requests a patch upgrade
aboutDialogPatchUpgradeInProgress=Your SAP HANA database patch upgrade is in progress.

#XFLD: Seal service version
aboutDialogSealServiceVersion=Deployment Service Version

#XFLD: Data Integration Runtime Version
aboutDialogDisRuntimeVersion=Data Integration Runtime Version

#XMSG: Version information not available
aboutDialogVersionRetrievalError=Version information is not available.

#XFLD: Replication/Data Flow NAT IP (egress)
aboutDialogOutboundIPAddress=Replication/Data Flow NAT IP (egress)

#XFLD: SAP HANA Cloud NAT IP (egress)
aboutDialogOutboundHanaIPAddress=SAP HANA Cloud NAT IP (egress)

#XMSG: Version information not available
aboutDialogIPAddressRetrievalError=Information is not available.

#XMSG: Error while loading version data
aboutDialogLoadVersionError=An error occurred while loading the version information.

#XFLD Label for subnet ID
aboutDialogSubnetIdLabel=Virtual Network Subnet ID (Microsoft Azure)

#XMSG Subnet ID not available
aboutDialogSubnetIdNotAvailable=Information is not available.

#XMSG: Placeholder message, in case the real version for "Repository Release",
#"Database Release" cannot be retrieved.
currentlyNotAvailable=Currently not available.

#~~~~~~~~~~~ Error Messages ~~~~~~~~~~~~~~~~~~~
#XMSG: error while fetching navigation data
errorNavigationFetch=Error while fetching navigation data. {0}

#XMSG: error while resolving hash key for navigation
errorNavNotSupported=Navigation to {0} is not supported.

#XMSG: error after applying hash key to browser
errorSetHash=Navigation target {0} cannot be reached.

#XMSG: Error when iFrame source could not be loaded
errorIFrame=Something went wrong while accessing the required resource.

#XMSG: Error when illegal navigation is invoked
illegalNavigation=The required resource cannot be navigated to.

#~~~~~~~~~~~ Support Dialog ~~~~~~~~~~~~~~~~~~~
#XBUT Download Log button for help center/support popover
downloadLog=Download Log
#XBUT Create Support User Log button for help center/support popover
createSupportUser=Create Support User
#XTIT Title for support popover
supportPopoverTitle=Support
#XFLD Description for create support user button
createSupportUserDescription=Create a support user for an SAP Support Engineer to troubleshoot a problem on your system. An email is automatically sent to SAP Support to notify them of the newly created user.
#XFLD Description for download log button
downloadLogDescription=Download the log for the browser session to troubleshoot end-user issues.
#XFLD Create Support User content text (copied from SAC)
createSupportUserContentText=You are about to create a support user that will be used by SAP Support to troubleshoot your problem. You can delete this support user once your issue has been resolved.

#~~~~~~~~~~~ Space Selection Dialog ~~~~~~~~~~~~~~~~~~~
#XTIT Dialog Header
selectSpace=Select Space
#XTOL Tooltip for SearchField inside Dialog
dialogSearch=Search for Spaces
#XTOL Tooltip for locked space tile in the dialog
txtLockedSpace=You cannot create an entity in this space because it is locked.
#XTOL Tooltip for Sort Button
dialogSortSpaces=Sort Spaces Ascending/Descending
#XTOL Tooltip for Sort Button
dialogSortAscending=Sort Spaces Ascending
#XTOL Tooltip for Sort Button
dialogSortDescending=Sort Spaces Descending
#XBUT Text for Dialog Cancel Button
cancel=Cancel
#XTXT: Info message for a locked space
spaceLockedInfo=This space is locked and is available only in read-only mode.
#XFLD: SAP HANA Data Lake Storage label if less than 1GB
dataLakeStorageUsed=< 1 GB

#~~~~~~~~~~~ Request Support User~~~~~~~~~~~~~~
#XBUT, 15 to close the dialog
btnOkSupportUser=OK
#XBUT, 15 to close the dialog
btnCancelSupportUser=Cancel

#~~~~~~~~~~~ Feedback Popover ~~~~~~~~~~~~~~~~~~~
#XTIT: UserGreeting title for the feedback popover
feedbackPopoverTitleUserGreeting=Hi {0},
#XMSG: Title message for the feedback popover
feedbackPopoverTitle=Tell us what’s on your mind.
#XTIT: Title for the long survey
longSurveyHeader=User Experience Survey
#XFLD: A badge of the long survey
longSurveyBadge=NEW
#XFLD: A note that shows how long the survey is available until
longSurveyDeadline=Available until {0}
#XMSG: A message that shows the description of the long survey
longSurveyDescription=Complete a 5-minute survey and help us improve your product experience! The survey will open in a new tab.
#XBUT: Text for the feedback button
longSurveyButtonText=Take Survey
#XTOL Tooltip for the feedback button
longSurveyButtonTooltip=External Link to Qualtrics User Experience Survey

#XTIT: Title for the short feedback survey
giveFeedbackHeader=Share your Feedback
#XMSG: A message that shows the description of the short feedback survey
giveFeedbackDescription=Your opinion matters. Share your thoughts with us at any time!
#XBUT: Text for the feedback button
giveFeedbackButtonText=Give Feedback
#XTOL Tooltip for the feedback button
giveFeedbackButtonTooltip=Give Feedback

#XLNK Tooltip for notifications for accessibility
notificationsTooltip=Notifications


#~~~~~~~~~~~ Joule Info Popover ~~~~~~~~~~~~~~~~~~~
#XMSG: Text for the joule deactivation in the joule info popover
jouleDeactivatedInfoPopoverText=Joule is no longer available because an administrator has deactivated it.
