#XTIT Save Dialog
saveView=Save View
#XTIT Save Dialog, parameter is the model type (Data Flow, Table, Intelligent Lookup…)
saveModel=Save {0}
#XFLD: Save As
saveAs=Save As
txtDataPreview=Data Preview
txtProblems=Errors
currentSpace=Current Space
graphicalModeling=Graphical Modeling
sqlModeling=SQL Modeling
importModel=Import Model
uploadFile=Import CSV File
showPerformanceLog=Print Performance Log to Console
clearPerformanceLog=Clear Performance Log

selectUserImpersonation=Enter another user identifier to view the data with that user’s permissions and see which records will be visible to them. Select a space user from the list or directly enter the user identifier.
selectUserImpersonationNoUsersList=Enter another user identifier to view the data with that user’s permissions and see which records will be visible to them.
userImpersonationCrossSpaceWarning=This view has one or more sources shared from other spaces, which may have data access controls applied. As "View as User" can only simulate permissions applied to a user in the current space, you may not see exactly the same records as the user you are impersonating.
viewAsUser=View as User
usersSpace=Space {0} Users
helpUserImpersonation=You can choose someone from the list or enter any user id.
displayNameAndIdpFormat={0} ({1})


#XBUT: Upload data from file action in toolbar
uploadData=Upload Data from CSV File
allEntires=All Entries
createNew=Create New
owner=Owner
shared=Shared
hierarchies=Hierarchies
#XBUT: delete table data action in toolbar
deleteTableData=Delete Data from Table
#XBUT: edit custom annotations action in toolbar
editCSNAnnotations=Edit Custom CSN Annotations
#XMSG: Status message after data deletion has been executed
dataDeletionSuccessful=Data has been deleted.
dataDeletionFailed=There was an error while trying to delete the data.
#XMSG: Message for data preview tab
txtNoData=No data found.
txtPreviewMsg=To preview your data, solve the errors from the table definition. The data should then be visible in the table.
#~~~~~~~~~~~ Space Selection ~~~~~~~~~~~~~~~~~~~
#XMIT
#XTIT: Title for data builder
spaceSelectionTitle=Welcome to the Data Builder
#YMSG: Description for data builder
spaceSelectionDescription=Create views and tables to prepare data for your stories, and use entity-relationship models to visualize and make associations between artifacts.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for data builder
dataBuilderTitle=Welcome to the Data Builder
#YMSG: Description for data builder
dataBuilderDescription=Load data into tables, prepare and model it in views, and combine it in tightly-focused analytic models for consumption in SAP Analytics Cloud, Microsoft Excel, and other clients, apps, and tools.
#YMSG: Description for data builder if space is large system
dataBuilderDescriptionLSA=Ingest large quantities of data, share it to standard space, and consume it as the source of flows, views, and analytics models.

#XMIT Add ERModel menu button text in Data Builder Landing Pages
ERModel=Entity - Relationship Model
#XMIT Add graphicalView menu button text in Data Builder Landing Pages
graphicalView=Graphical View
#XMIT Add sqlView menu button text in Data Builder Landing Pages
sqlView=SQL View
#XMIT Add table menu button text in Data Builder Landing Pages
table=Table
#XMIT Add remote table menu button text in Data Builder Landing Pages
remoteTable=Remote Table
#XMIT Add local table menu button text in Data Builder Landing Pages
localTable=Local Table
#XMIT Add local table on file menu button text in Data Builder Landing Pages
ltfTable=Local Table (File)
#XMIT Add data flow menu button text in Data Builder Landing Pages
dataFlow=Data Flow
#XMIT Add transformationFlow menu button text in Data Builder Landing Pages
transformationFlow=Transformation Flow
#XMIT Add task chain menu button text in Data Builder Landing Pages
taskChain=Task Chain
#XMIT Add IDT menu button text in Data Builder Landing Pages
IDT=Intelligent Lookup
#XMIT Add analytic model menu button text in Data Builder Landing Pages
queryModel=Analytic Model
#XMIT Add replication flow menu button text in Data Builder Landing Pages
replicationFlow=Replication Flow
#XMIT Add e/r model menu button text in Data Builder Landing Pages
ermodel=E/R Model
#XMIT Add data access control menu button text in Data Builder landing pages
dataAccessControl=Data Access Control

#XMIT Add versions menu button text in Data Builder
versions=Versions
oldVersionLabel=Version
oldVersionLabel2=Version {0}
createVersion=Create Version
versionHistory=Version History

restoreVersion=Restore
editCommentForVersion=Edit Comment
downloadCSNForVersion=Export to CSN/JSON File

newERModel=New ER Model
newGraphicalView=New Graphical View
newSqlView=New SQL View
newTable=New Table
newDataFlow=New Data Flow
#XTXT: Breadcrumb for Databuilder -> New Analytic Model
newAnalyticModel=New Analytic Model
#XTXT: Breadcrumb for Databuilder -> New Data Access Control
newDataAccessControl=New Data Access Control
#XTXT: Breadcrumb for Databuilder -> New Intelligent Lookup
newIDT=New Intelligent Lookup
#XTXT: Breadcrumb for Databuilder -> New Task Chain
newTaskChain=New Task Chain
#XTXT: Breadcrumb for Databuilder -> New Replication Flow
newReplicationFlow=New Replication Flow
#XTXT: Breadcrumb for Databuilder -> New Transformation Flow
newTransformationFlow=New Transformation Flow

addERModel=New Entity - Relationship Model
addGraphicalView=New Graphical View
addStory=New Story
addSqlView=New SQL View
addTable=New Table
addDataFlow=New Data Flow
addQueryModel=New Analytic Model
#XTXT Intelligent Lookup tile in Databuilder landing page
addIDT=New Intelligent Lookup
#XTXT Task Chain tile in Databuilder landing page
addTaskChain=New Task Chain
#XTXT Replication Flow tile in Databuilder landing page
addReplicationFlow=New Replication Flow
#XTXT Transformation Flow tile in Databuilder landing page
addTransformationFlow=New Transformation Flow
#XTXT: Data Access Control tile in Data Builder landing page
addDataAccessControl=New Data Access Control
#XTXT: Breadcrumb for Databuilder -> Import CSV
importCsvBreadcrumb=Import CSV File
copyEntity=Copy
shareEntity=Share
addApp=New Application
uploadDataTile=Import CSV File
uploadDataTileTooltip=Import CSV File to Create New Table
uploadCsnFile=Import CSN File
uploadCsnJsonFile=Import Objects from CSN/JSON File
importEntities=Import Entities
importRemoteTables=Import Remote Tables
importRemoteDACs=Import Permissions
validateRemoteTables=Validate Remote Tables
cancel=Cancel
ok=OK
reset=Reset
help=Help
alert=Alert
checkDependencyFailed=Dependency couldn’t be checked.
descriptionTitle=Description
name=Name
fileIsUsed=The selected file is used by other files and cannot be deleted.
columnIsUsed=The selected column is used by other tables or views within this or other spaces and can’t be deleted. To delete it, please make sure that this column isn’t used.
schemaIsUsed=The selected schema is used by other files and cannot be deleted.
usedIn=(used in {0} files)
usedInHasInaccessible=(used in {0} files including {1} inaccessible dependencies)
confirm=Confirm
txtRecentFiles=Recent Files
selectentityBreadcrumb={spaceId}
editorBreadcrumb={model}
cannotOpenModel=Cannot open {0} of space {1}. It may not exist in the repository.
saving=Saving
#XMSG: Save takes more than 30 sec and switch to async
saveLongTime=Saving is taking longer than usual. Please wait.
#XMSG: Confirm AI change message
confirmAIChange=All AI-generated changes will be saved, and highlighting and review buttons removed.
#XTIT
titconfirmAI=Confirm AI-Generated Semantics
#XMSG
semanticsCanceled=Generation canceled
deploying=Deploying
openFile=Opening File
switchView=Switch View
dataView=Data View
associationView=Association View
general=General
#XTOL
save=Save
#XTOL
deploy=Deploy
#XTOL
validateAndPreview=Validate SQL and Preview Data
#XTOL
performanceLog=Performance Log

#XMSG: validation of sql statement was successfull
validateSQLOk=SQL is valid.
#XMSG: validation of sql statement failed
validateSQLFailed=SQL is invalid.
#XSEL: location of error of sql validation
locationErrorSQL=Line {0}, Column {1}

#XTOL Display a list of errors / warnings from the validation
validationStatus=Validation Messages
view=View
showHideSourceTree=Show or hide source tree
showHideDataPreview=Show or hide data preview
#XTOL
txtDataViewer=Data Viewer
#XTOL
expandOrCollapsToolbar=Expand or collapse toolbar
showHidejsonView=Show or hide CSN
details=Details
showHideProperties=Show or hide properties
edit=Edit
addFromRepository=Add from Repository
import=Import
importRemoteSource=Import from Connection
importCSN=Import from CSN File
importCsnJson=Import Objects from CSN/JSON File
export=Export
#XTOL
tools=Tools
#XTOL
uploadBtnText=Upload
exportCSN=Export CSN File
exportCsnJson=Export to CSN/JSON File
refreshRemoteTable=Refresh to import changes from underlying source
allFilesTab=All Files
erModelsTab=E/R Models
viewsTab=Views
tablesTab=Tables
sharedTab=Shared Objects
dataFlowsTab=Data Flows
#XTXT: Flows tab text
flowsTab=Flows
ilArtifactsTab=Intelligent Lookups
queryModelsTab=Analytic Models
taskChainsTab=Task Chains
dataAccessControlTab=Data Access Controls
#XTXT: Transformation Flows tree text
transformationFlowsTab=Transformation Flows
#XTXT: Replication Flows tree text
replicationFlowsTab=Replication Flows
remoteTablesTab=Remote Tables
taskChainTab=Task Chains
saveAnyway=Save Anyway
deployAnyway=Deploy Anyway
dialogTitleSaveWithErrors=Validation Messages
dialogTitleDeployWithErrors=Validation Messages
importFile=Import File
#XTXT: Delta Table tree text
deltaTableTab=Delta Table
#XTOL

#XTIT: Title for Intelligent Lookups category under Repository Panel
intelligentLookupsTab=Intelligent Lookups
#XTIT: Title for Analytic Models category under Repository Panel
analyticModelsTab=Analytic Models

#XBUT: Text for segmented buttons in data sources browser: Repository
repository=Repository
#XBUT: Text for segmented buttons in data sources browser: Others
others=Others
#XFLD: Text for segmented buttons in data sources browser: BW Process Chain Sources
BW_PROCESS_CHAIN=BW Process Chains
#XFLD: Text for segmented buttons in data sources browser: SQL Script Procedure Sources
SQL_SCRIPT_PROCEDURE=SQL Script Procedures
#XFLD
othersTabSearch=Search In: "{0}"
#XBUT: Text for segmented buttons in data sources browser: Remote Sources
remoteSources=Sources
@itemsNoMoreThan=Cannot display more than {0} items.
@itemsPartialResult=Showing {0} of {1} items. \nUse the search field to filter the list.
#XMSG: message text for message box of Create Connection
addRemoteSuccess=Connections added.
addRemoteError=Connections couldn’t be added.
#XMSG: Warning message in source tree connection in case replication fails.
replicationWarning=This connection cannot be used for data flow modeling. \n Please open the connection and save it to solve this issue.
#XMSG: Warning message when d&d from repository shared object if existing objects name duplicated to cross space name
objectWithSpaceNameExist=There is an existing object "{0}" with the same name as the source space.\n To reuse the cross space object, you need to delete or rename the object "{0}".
#XTIT: Connection Error
connectionError=Connection Error
#XMSG:(#UI_COMPONENT_RELATED) Connection Error. User can go to Connection UI to validate the connection and get more information
connectionErrorValidateForMore=An error occurred while accessing the connection. Open the "Connection" app in the side navigation panel, validate the connection, and view the details. If you do not have access to the "Connection" app, please contact your administrator.
#XTOL: Connection Error tooltip
connectionErrorTooltip=Unable to fetch data. Click for more details.

enterModelName=Please enter a model name.
enterValidModelname=Please enter a valid model name.
enterTableName=Please enter a table name.
enterValidTableName=Please enter a valid table name.
modelSavedSuccessful={0} saved successfully.
modelRestoredSuccessful=Restored version {0}.
tableSavedSuccessful=Table saved
deploymentSuccessful=Deployment successful.
deltatablecapture=Delta Capture Table
#XMSG: Text shown temporarily on tree nodes that are currently loading until real child nodes are available
loading=Loading...
#XTIT: Root node for all connections
remotesources=Connections

#XMSG: Warning message about missing sources/objects
missingSources=The following objects have been deleted and should be removed from the model.
missingObjects=The following objects have been deleted and removed from the model.
cannotRevert=The following objects have been deleted. It is therefore not possible to restore the deployed version.

#~~~~~~~~~~~~ Analytic Model editor ~~~~~~~~~~~~~~~~~
#XTIT: Title for query editor source data tab
sourceData=Source Data
cubeSourcesTitle=Sources
measureSourcesListTitle=Measure Sources
dimensionSourcesListTitle=Dimension Sources

#XFLD: Default value for business name input
defaultBusinessName=New Query

#XFLD: Query editor section header general
generalSetting=General
clearFilter=Clear
refresh=Refresh
customizeColumnsButton=Columns
#Label for search result count
results=Results
createdBy=Created by:
modifiedOn=Modified on:
description=Description:

#XTIT: Title for query editor summary tab
summary=Summary & Settings
createQueryModelBreadcrumb=New Query

#XFLD: Add cube source
addButton=Add
#XFLD: Preview cube source data
previewButton=Preview
#XFLD: Preview data in table format
dataPreviewTable=Data Preview
#XFLD: Preview data in diagram format
dataPreviewDiagram=Diagram

#~~~~~~~~~~~~ Upload File Texts ~~~~~~~~~~~~~~~~~

#XMSG: Success message after file upload
fileUploadDone=File has been uploaded.

#XMSG: MessageToast for deleting files successfully
fileDeleted=Files deleted successfully.

#XMSG: File Upload is only available if the table services flag "Data Management" is set to on
fileUploadNotAvailableForSteampunk=Tables with request management enabled cannot be uploaded.\nPlease use a data flow to transfer data into the table.

#XMSG: Busy dialog for importing Csn File
importing=Importing...

#XMSG: Message Toast for importing csn file successfully
importSuccess={0} imported successfully.

#XMSG: Message Toast for importing Csn File
importStarted={0} is being imported.\nWe will notify you when the import is complete.

#XMSG: Error message for import csn failed
importErrResponse=Failed to import {0}

#~~~~~~~~~~~~ Delete Data from Table Texts ~~~~~~~~~~~~~~~~~

#XTIT: Title of the confirmation dialog
deleteTableDataDialogTitle=Confirm Data Deletion
#XMSG: text in confirmation dialog that a user shall confirm the data deletion
deleteTableDataDialogContent=This action will delete all data loaded into table "{0}". Do you really want to do this?
#XBUT: delete Button in confirmation dialog, an additional button will be "Cancel"
deleteTableDataDialogButton=Delete Data
#XMSG: Message in the loading indicator while truncate is in progress
deleteTableDataInProgress=Deleting data of table "{0}"...
#XMSG: Message in the loading indicator while delete is started
deletionStart=We are deleting data of table "{0}" (Local)

#~~~~~~~~~~~~ Delete Data from Table during deploy Texts ~~~~~~~~~~~~~~~~~
#XTIT: Title of the truncate table dialog
truncateDialogTitle=Confirm Truncation of Table
#XMSG: Dialog content that describes the confirm of the table truncation
truncateDialogContent=The table contains data which needs to be truncated in order to deploy it. Do you want to truncate the data?
#XBUT: Button to confirm table truncate
truncateButton=Truncate & Deploy
#XMSG: Message in the loading indicator while truncate is in progress
truncateInProgress=Truncating table...
#Text for Parameters
showParameters=Show Parameters

#XMSG: text in confirmation dialog that a user shall confirm saving or not before export full CSN if model is dirty
saveBeforeExport=Do you want to save the model before the export? If you don’t do this, the export will be stopped.
#XMSG: MessageToast for exporting CSN file completed
exportingCompleted=Export completed.

#~~~~~~~~~~~ Deployment Process ~~~~~~~~~~~~~~~~~~~
#XMIT
deploymentInProgress=Please wait while we deploy the table.
deploymentStarted=Deploying {0}. We will notify you when the process is complete.
deploymentStartedWithPersistencyExist={0} is being deployed. We will let you know when it is deployed and when its persistencies are deleted, including its dependent objects(if modified).
deploymentFailure=The table couldn’t be saved. Please try again.
deployTable=Deploy Table
enterExistingTableName=This technical name already exists. Please enter another one.
checkingTableName=Checking the name...
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XBUT
txtPreviewSQL=Preview SQL
#XBUT
txtExportRuntimeCSN=Export Support Information
#YMSG: Description for data builder
importBW4Model=Import SAP BW/4 HANA model
#XFLD: Description for Import From Connection Tile in Landing Page
importFromConnection=Import from Connection
#XTIT
ip_enterValuesToGenerateData=Enter values to generate your data.
#XTIT
ip_inputParameters=Input Parameters
#XFLD
ip_name=Name
#XFLD
ip_value=Value
@none=None
#XTEXT: Text for true dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
@param_true=true
#XTEXT: Text for false dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
@param_false=false
#XMSG: Error message for Data preview with Cross space object with Input parameters
ERR_CROSS_SPACE_INPUT_PARAM_NOT_SUPPORTED=Cannot preview data :\nData preview for Views with Cross space objects containing Input Parameters is not supported.
#XMSG: Error message for Data preview with Repo browser object with Input parameters
ERR_REPO_BROWSER_INPUT_PARAM_NOT_SUPPORTED=Cannot preview data :\nData preview is not supported in the repository browser for entities with input parameters.\n To view this entity’s data, open it in its editor and use the Data Viewer there.
@noObjectsFound=No Objects Found

#~~~~~~~~~~~ Compatibility Contract ~~~~~~~~~~~~
#XMSG: MessageStrip for released views
releasedViewWarning=This object has a release state of "Released". You cannot make any changes that will interrupt the consumption of its data by objects that depend on it.
#XMSG: MessageStrip for deprecated views
deprectedViewWarning=This object has a release state of “Deprecated”. You cannot make any changes that will interrupt the consumption of its data by objects that depend on it. The successor object that consumers should migrate to is {0}.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message strip for HANA update
updateHANAVersionMessageStripText=We recommend to apply SAP HANA Cloud Patch Update 2025.2.8 or higher to obtain performance and stability improvements.
#XMSG: Message strip "Learn More" link
learnMore=Learn More

#XMSG: MessageStrip for versioned objects
versionedObjectWarning=Version {0} ({1}) - Read-Only. You cannot save or deploy this version. To restore this version, selection Version -> Restore.
#XMSG: MessageStrip for versioned objects
versionedObjectWarningRestoreNS=Version {0} ({1}) - Read-Only. You cannot save, deploy or restore this version.
#XMSG: MessageStrip for restore object
restoreObjectWarning=You are restoring version {0}. Please review the validation messages and then click Save to confirm the restore.
restoreObjectInfo=You are restoring version {0}. Please click Save to confirm the restore.
#~~~~~~~~~~~ Wrangling ~~~~~~~~~~~~~~~~~~
#XMSG: MessageToast for wrangling fails to open
wranglingFailed=Unable to start import from CSV. Please try again.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~ Artefact Sharing ~~~~~~~~~~~~~~~~~~
#XTIT: Title for artefact sharing dialog
artefactSharingDialogTitle=Share {0} "{1}"
#XTIT: Title for mulitple artefact sharing dialog
multipleArtefactSharingDialogTitle=Share {0} Models
#XFLD: Label for adding spaces to share the artefact with
addSpaces=Add Spaces
#XFLD: Placeholder for space multi input
enterSpace=Enter Space
#XFLD: Label for selected models
selectedModels=Selected Models
#XFLD: Label for shared access type
access=Access
#XFLD: Read access type
read=Read
#XBUT: Share Button
share=Share
#XFLD: Label to indicate with how many spaces the artefact is shared
sharedWithXSpacesPlural=Shared to {0} Spaces
#XFLD: Label to indicate with how many spaces the artefact is shared (singular)
sharedWithXSpacesSingular=Shared to 1 Space
#XBUT: Button to close dialog
close=Close
#XBUT: Button to unshare sharing
unshare=Unshare
#XFLD: Text to be displayed in table when artefact has not been shared yet
noDataShareDialogTableText=This artifact has not been shared yet.
#XFLD: Text to be displayed in space value help dialog search returns no results
spaceValueHelpNoData=No Spaces Found
#XTIT: Title for space value help dialog
selectSpaceValueHelpTitle=Select Space
#XMSG: Error message for loading sharing data
failedToLoadSharingData=Sorry, something went wrong while retrieving the sharing information
#XMSG: Error message for loading sharing data
failedToSpaces=Sorry, something went wrong while retrieving spaces
#XMSG: Success message for sharing a single object
succeededToShareObject=Model is shared
#XMSG: Success message for sharing a multiple objects
succeededToShareObjects=Models is shared
#XMSG: Error message for sharing a single object that is not deployed.
failedToShareUndeployedObject=Cannot share the model because it is not deployed yet. You must deploy it before sharing.
#XMSG: Error message for sharing multiple objects that are not deployed.
failedToShareUndeployedObjects=Cannot share the models because they are not deployed yet. You must deploy them before sharing.
#XMSG: Error message for sharing a single object to a locked space
failedToShareObjectLockedSpace=Cannot share the model with space {0} because the space is locked.
#XMSG: Error message for sharing multiple objects to a locked space
failedToShareObjectsLockedSpace=Cannot share the models with space {0} because the space is locked.
#XMSG: Error message for sharing a single object
failedToShareObject=Sorry, something went wrong while sharing the models
#XMSG: Error message for sharing multiple objects
failedToShareObjects=Sorry, something went wrong while sharing the models
#XMSG: Success message for unsharing a single object
succeededToUnshareObject=Model is unshared
#XMSG: Success message for unsharing a multiple objects
succeededToUnshareObjects=Models is unshared
#XMSG: Error message for unsharing a single object
failedToUnshareObject=Sorry, something went wrong while unsharing the model
#XMSG: Error message for unsharing a multiple objects
failedToUnshareObjects=Sorry, something went wrong while unsharing the models
#XTIT: Dialog title for dependency dialog
dependencyDialogTitle=An error occurred while unsharing {0}
#XMSG: Description text for dependency dialog (multiple models, multiple spaces)
dependencyDialogDescriptionPluralPlural=We could not unshare your {0} because it is used by the following models in different spaces.
#XMSG: Description text for dependency dialog (multiple models, single space)
dependencyDialogDescriptionPluralSingular=We could not unshare your {0} because it is used by the following models in a different space.
#XMSG: Description text for dependency dialog (single model, single space)
dependencyDialogDescriptionSingularSingular=We could not unshare your {0} because it is used by the following model in a different space.
#XFLD: Label to indicate in how many models the other model is used in
sharingUsedInPlural=(used in {0} models)
sharingUsedInSingular=(used in 1 model)
#XFLD Placeholder for inaccessible end user label of model in dependency dialog
inaccessibleEndUserLabel=Missing permissions to read object
#XFLD Placeholder for inaccessible fields of model in dependency dialog
notAvailable=N/A
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL
txtType=Type
txtMessage=Message
txtLocation=Location
#XMSG
txtNoProblems=No problems found
#XTOL
execute=Run
#XMSG
cannotOpenSharedObject=Cannot open {0} because you do not have access to space {1}.
#XTOL
openJsonEditor=Switch to JSON Editor.
#XTOL
txtTransformation=Add Projection
#XTOL
txtTransformationWithProjections=Edit Projection
#XBUT
addTransformation=Add Projection
#XTOL
navToMonitoringView=Open in Data Integration Monitor
#XTOL
openEmailNotification=Runtime Email Notification
#XMSG Not suffcient privileges to preview data
txtNoPrivilegesPreviewData=You do not have sufficient privileges to preview this data.
#XMSG: Info for data preview against persisted view (modified but not deployed again)
InfodataPreviewForPersitedView=The structure of your view has changed, and its persisted data might no longer be valid. Running query against view sources...
#XMSG
technicalErrorDetails=We can provide the following information to assist either you or an SAP engineer to solve the issue:<br><br>Correlation ID: {0}<br>HTTP Status: {2}<br>Technical Message: {1}
#XMSG
technicalErrorSearch=An error happened when searching the information in the space.
#XMSG Operation Node name change feature.
JOIN_NODE_EMPTYNAME=The name of the Join is empty.
#XMSG Operation Node name change feature.
UNION_NODE_EMPTYNAME=The name of the Union is empty.
#XMSG Operation Node name change feature.
PROJECTION_NODE_EMPTYNAME=The name of the Projection is empty.
#XMSG Operation Node name change feature.
CALCULATEDELMS_NODE_EMPTYNAME=The name of the Calculated Elements is empty.
#XMSG Operation Node name change feature.
FILTER_NODE_EMPTYNAME=The name of the Filter is empty.
#XTXT: Impact and Lineage analysis
impactAndLineageDialogTitle=Impact and Lineage Analysis
#XTOL: Impact and Lineage analysis button tooltip
impactButtonTooltip=Impact and Lineage Analysis
#XTOL: Generate OData Request button tooltip
oDataButtonTooltip=Generate OData Request
#XTOL: GenAI generate menu button tooltip
ai=Generate
#XBUT: Menu item of generating semantics
generateSemantics=Generate Semantics
#XCKL
selectSemanticUsage=Select Semantic Usage (Fact, Dimension, Text)
#XCKL
identifyColumns=Identify Measures and Attributes, and Set Keys and Semantic Types
#XCKL
notOverride=Do Not Override Existing Column Metadata
#XTIT Dialog title for generating semantics
generateSemanticsTitle=Generate Semantics
#XMSG: Message for generating semantics dialog
generateSemanticsMessage=We can generate semantics and other metadata for your entity to prepare it for analytic consumption.
#XMSG
generateSemanticsMessageStrip=Generation may take longer than usual due to the number of columns.
#XMSG
generateSemanticsMessageStrip2=Semantic generation is supported for objects of semantic usage "Fact", "Dimension", "Text", or "Relational Dataset" only.
#XBUT: Generate button of generating semantics dialog
generateText=Generate
#XBUT: Cancel button of generating semantics dialog
cancelText=Cancel
#XGRP add new currency conversion
addCurrencyConversion=New Currency Conversion
#XFLD Currency Conversion Views
currencyConversion=Currency Conversion Views
#XFLD Business Name  of conversion labels
businessNameLabel=Business Name
#XFLD Technical Name of conversion labels
technicalNameLabel=Technical Name
#XBUT create button label
create=Create
currencyConversionCreated=Currency Conversion created
currencyConversionCreatedDeploymentStarted=Currency Conversion created and deployment has been started.
#YMSG Currency conversion objects are being imported message
currencyConversionCreationStarted=Currency conversion objects are being imported. We will notify you when the import is completed.
currencyConversionCreationFailed=Failed to create currency conversion objects.
#XFLD Source label
conversionSource=Source
#XFLD Tables label
conversionTables=Tables
#XFLD Views label
conversionViews=Views
#XFLD Data Flows label
conversionDataFlows=Data Flows
#XFLD Replication Flows label
conversionReplicationFlow=Replication Flow
#XFLD Remote Tables label
conversionRemoteTables=Remote Tables
#XFLD Manual label
conversionSourceManual=Manual
#YINS Create TCUR* tabeles and views instruction
currencyConversionHint=Create the TCUR* currency conversion views and the necessary supporting objects either from an SAP connection or manually.
#YINS Select a source for creating a currency conversion
currencyConversionHintNew=Select a source to create TCUR* currency conversion views (and, if appropriate, the necessary supporting objects) in your space. You can choose an SAP connection, a space that has shared the TCUR* tables with you, or decide to create the objects manually yourself.
#YINS Hint that the user is able to share the currency conversion after their creation
currencyConversionShareHint=Once you’ve created the currency conversion views and their supporting objects, you can share the TCUR* tables with other spaces. The spaces to which you share these tables will then see your space as an alternative source in the Source field above.

#XFLD Unit conversion Views
unitConversion=Unit Conversion Views
#XGRP add new unit conversion
addUnitConversion=New Unit Conversion
#YMSG Unit conversion objects are being imported message
unitConversionCreationStarted=Unit conversion objects are being imported. We will notify you when the import is completed.
#YINS Create T006* tabeles and views instruction
unitConversionHint=Create the T006* unit conversion views and the necessary supporting objects either from an SAP connection or manually.
#YINS Select a source for creating a unit conversion
unitConversionHintNew=Select a source for creating T006* unit conversion views (and, if appropriate, the necessary supporting objects) in your space. You can choose an SAP connection, a space that has shared the T006* tables with you, or decide to create the objects manually yourself.
#YINS Hint that the user is able to share the unit conversion after their creation
unitConversionShareHint=Once you’ve created the unit conversion views and their supporting objects, you can share the T006* tables with other spaces. The spaces to which you share these tables will then see your space as an alternative source in the Source field above.

#XTIT: Section title Share Unit Conversion Tables
shareUnitConversionTables=Share Unit Conversion Tables
#XMSG
createRFInfoMsgNew=Using SAP Datasphere embedded, you can view the list of replication flows that were created earlier but you cannot open any of these existing replication flows or create new ones.
#XMSG
#XTIT: Section title
shareCurrencyConversionTables=Share Currency Conversion Tables
#XFLD: Object type is a context
txtTypeContext=Context
#XFLD: Object type is a simple type
txtTypeSimpleType=Simple Type

#XMSG
PROCESSINGS=Processing...

#XBUT:Import button text
@importCsnFile=Import CSN File
#XTIT:Import button text
@selectObjectsToImport=Select Objects to Import
#XTIT
@alreadyInRepository=Already in the repository.
#XTIT
@confirm=Confirm
#XMSG
@importConfirmOverrideText=Your selection depends on a number of non-selected objects that are already present in the repository. Do you want to re-import these non-selected objects and overwrite the repository versions? Some data may be lost due to structure changes.
#XMSG
@waitingToImport=Ready to Import
#XCOL
@label=Business Name
#XCOL
@status=Status

#XCOL
@name=Technical Name
#XCOL
@type=Type
#XCOL
@remoteHost=Remote Host
#XCOL
@remoteTable=Remote Table
#XCOL: Type and Semantic usage
@typeAndSemantic=Type (Semantic Usage)
#XMSG
@csnFileTypeMissmatch=Only CSN files are supported.
#XMSG
@csnFileInvalidFormat=The csn file does not have the valid csn format.
#XMSG
@csnFileInvalidFormatMissingSourceLangInI18n=There are no strings for the space source language "{0}" in the file "{1}". The source language of the space and the imported file must match.
#XMSG
@csnFileInvalidFormatMissingKeysInSourceLangNode=Some strings in the file "{0}" are missing for the source language "{1}". Define all strings for source language.
#XMSG
@csnFileInvalidFormatRedundantI18nSection=A document containing localized strings cannot be imported if the space does not define any source language. Configure your space to select one of the languages provided by the document.
#XMSG
@csnFileEmpty=The csn file is empty.
#XMSG
@importCompleted=Import Completed
#XTIT
@alreadyInModel=Already in the current model.
#XTIT
@addRepositoryObjects=Add Repository Objects
#XBUT:Import button text
@ok=OK
#XTIT
package=Folder
#XMSG
PREREQUISITES=Prerequisites
#XMSG
NOREMOTEACCESS=Remote Access Not Supported
#XMSG
DATAVALIDATIONISNOTAVAILABLE=Data validation is not available
#XMSG
DATAVALIDATIONNOTAVAILABLEFORREMOTEOBJECT=This object (or one or more of its sources) is not replicated to SAP Datasphere. Data validation is not available.
#XMSG
NODATAACCESSACCESS=Data Access Controls Not Supported
#XMSG
INFO=info
#XMSG
DATAVALIDATIONNOTAVAILABLEFORDACOBJECT=This object (or one or more of its sources) is protected by a data access control. Data validation is not available.
#XMSG
NOPARAMS=Input Parameters Not Supported
#XMSG
DATAVALIDATIONNOTAVAILABLEFORPARAMSOBJECT=This object (or one or more of its sources) contains input parameters. Data validation is not available.
#XCOL
RULE=Rule
#XCOL
CATEGORY=Category
#XCOL
OBJECT=Object
#XCOL
MESSAGE=Message
#XMSG
VALIDATEDON=Validated On
#XMSG
COPYSQL=Copy SQL
#XMSG
DOWNLOADCSN=Download CSN
#XMSG
@getCsnFail=Couldn’t access CSN definition.
#XTIT
DETAIL=Detail
#XMSG
NOTVALIDATED=Not Validated
#XMSG
DETAILS=Details
#XMSG
ALL=All
#XMSG
SQLVALIDATIONMESSAGE=To obtain a full list of the records affected, click "Copy SQL", create a new SQL view, paste the code into it, and then preview the data.
#XMSG
COPYSQLSCRIPT=Copy SQLScript
#XMSG
SQLSCRIPTVALIDATIONMESSAGE=To obtain a full list of the records affected, click "Copy SQLScript", create a new SQL view, set the "Language" to "SQLScript (Table Function)", paste the code into it, save and deploy it, and then preview the data.
#XMSG
DOWNLOADCSN_VALIDATIONMESSAGE=\nTo obtain a CSN file containing modified entity definitions which can resolve these errors, click "Download CSN".
#XMSG
DATAVALIDATIONALREADYRUNNING=Data validation is already running.
#XTXT
RUNNING=Running
#XTXT
FAILED=Failed
#XMSG
@Messages=Messages
#XLNK
@INVALIDSTATE=Invalid Release State
#XMSG
@forceImport =Force Import
#XMSG
@selected =Selected : {0}
#XMSG
validationMessagesList={0}\n\n
#XMSG
cannotImportMsg = The following objects cannot be imported: \n -{0}
#XMSG
importConfirmation = Do you really want to force the import of this file?
#XTIT: Title for warning dialog
statChangeWarningTitle =Revert to Previous Release State
#XMSG
COPIED=Copied
ERROR=error
#XMSG
CANVALIDATE=Can Validate
#XMSG
changeNotPermitted=This change is not normally permitted as it does not confirm with the standard release state lifecycle.
#XMSG
importNotPermitted=Importing these objects would change the "Release State" of {0} objects in non-standard ways:
#XMSG
objectStateChange=- {0} ("{1}" to "{2}")
#XMSG
NOTRELEASED=Not Released
#XTIT
@importOverriteReleasestate=Objects that are already present in the repository can only be selected for re-import if their release states are consistent with the objects they will be replacing.
#XTIT
revertConfirmationDialogTitle=Revert to Previous Release State
#XFLD
revertButton=Revert
#XTIT
saveAsState=Save as {0}
#XMSG
objectSetToWarning=You have set the "Release State" of this object to "{0}".
#XMSG
saveAsReleasedWarning=Once an object is released, it must remain available with a stable structure until it is deprecated. A released object can only be deprecated if a successor object is selected.
#XMSG
continueAs=When you click "Save as {0}" you will not be able to undo this action. Do you want to continue?
#XMSG
saveAsDeprecatedSuccessor=You have set the "Release State" of this object to "Deprecated" and assigned the object "{0}" as its successor.
#XMSG
saveAsDeprecatedWarning=Once an object is deprecated, it must remain in this state for at least one year (and at least two years from the date when it was released). After this time, it can be decommissioned.
#XMSG
saveAsDecommissionedWarning=Once an object is decommissioned, it can be deleted at any time.
# XMSG
RELEASED=Released
# XMSG
DEPRECATED=Deprecated
# XMSG
DECOMMISSIONED=Decommissioned
# XMSG
WARN_SUCCESSOR_RELEASE_STATE_CHANGED=You cannot change the release state of "{0}" from "{1}" to "{2}" , as it is set as the successor to the following deprecated objects: {3}
# XMSG
DEPRECATED_VIEW=\n- "{0}"
# XMSG
DATA_VALIDATION_GROUP_MESSAGE=Validation of this rule is not available because:
# XBUT
VALIDATE=Validate
# XBUT
VIEWANALYZER=Start View Analyzer
# XBUT
CANCELVALIDATE= Cancel Validate
# XSMG
DATA_VALIDATION_WARNING_MESSAGE=One or more sources of this object aren't replicated to SAP Datasphere. Data validation \nmay be slower than usual and impact the performance of the remote system.
# XSMG
DATA_VALIDATION_VIEW_ANLYZER_MESSAGE=\n\nFor details and recommendations, analyze the view in the View Analyzer.
