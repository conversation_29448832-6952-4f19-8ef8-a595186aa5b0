#XTIT Save Dialog
saveView=<PERSON><PERSON><PERSON> imagine
#XTIT Save Dialog, parameter is the model type (Data Flow, Table, Intelligent Lookup…)
saveModel=Salvare {0}
#XFLD: Save As
saveAs=Salvare ca
txtDataPreview=Previzualizare date
txtProblems=Erori
currentSpace=Spațiu curent
graphicalModeling=Modelare grafică
sqlModeling=Modelare SQL
importModel=Importare model
uploadFile=Importare fișier CSV
showPerformanceLog=Tipărire jurnal de performanță în consolă
clearPerformanceLog=Resetare jurnal de performanță

selectUserImpersonation=Introduceți un alt identificator de utilizator pentru a vizualiza datele cu permisiunile utilizatorului și a vedea înregistrările care vor fi vizibile pentru acesta. Selectați un utilizator de spațiu din listă sau introduceți direct identificatorul de utilizator.
selectUserImpersonationNoUsersList=Introduceți un alt identificator de utilizator pentru a vizualiza datele cu permisiunile utilizatorului și a vedea înregistrările care vor fi vizibile pentru acesta.
userImpersonationCrossSpaceWarning=Această imagine are una sau mai multe surse partajate din alte spații, care pot avea aplicate controale de acces la date. Deoarece "Vizualizare ca utilizator" poate simula doar permisiunile aplicate unui utilizator în spațiul curent, este posibil să nu vedeți exact aceleași înregistrări ca utilizatorul pentru care vă asumați identitatea.
viewAsUser=Vizualizare ca utilizator
usersSpace=Utilizatori spațiu {0}
helpUserImpersonation=Puteți alege o persoană din listă sau puteți introduce orice ID de utilizator.
displayNameAndIdpFormat={0} ({1})


#XBUT: Upload data from file action in toolbar
uploadData=Încărcare date din fișier CSV
allEntires=Toate intrările
createNew=Creare nou
owner=Proprietar
shared=Partajat
hierarchies=Ierarhii
#XBUT: delete table data action in toolbar
deleteTableData=Ștergere date din tabel
#XBUT: edit custom annotations action in toolbar
editCSNAnnotations=Editare adnotări definite de utilizator CSN
#XMSG: Status message after data deletion has been executed
dataDeletionSuccessful=Datele au fost șterse.
dataDeletionFailed=A apărut o eroare la încercare de ștergere date.
#XMSG: Message for data preview tab
txtNoData=Fără date găsite.
txtPreviewMsg=Pentru a previzualiza datele dvs., rezolvați erorile din definiția de tabel. Apoi, datele ar trebui să fie vizibile în tabel.
#~~~~~~~~~~~ Space Selection ~~~~~~~~~~~~~~~~~~~
#XMIT
#XTIT: Title for data builder
spaceSelectionTitle=Bun venit la generatorul de date
#YMSG: Description for data builder
spaceSelectionDescription=Creați imagini și tabele pentru a vă pregăti datele pentru relatări și utilizați modelele de relație între entități pentru a vizualiza și efectua asocieri între artefacte.

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XTIT: Title for data builder
dataBuilderTitle=Bun venit la generatorul de date
#YMSG: Description for data builder
dataBuilderDescription=Încărcați date în tabele, pregătiți-le și modelați-le în imagini și combinați-le în modele analitice strict focalizate pentru consum în SAP Analytics Cloud, Microsoft Excel și alți clienți, aplicații și instrumente.
#YMSG: Description for data builder if space is large system
dataBuilderDescriptionLSA=Introduceți cantități mari de date, partajați-le în spațiul standard și consumați-le ca sursă de fluxuri, imagini și modele analitice.

#XMIT Add ERModel menu button text in Data Builder Landing Pages
ERModel=Model entitate - relație
#XMIT Add graphicalView menu button text in Data Builder Landing Pages
graphicalView=Imagine grafică
#XMIT Add sqlView menu button text in Data Builder Landing Pages
sqlView=Imagine SQL
#XMIT Add table menu button text in Data Builder Landing Pages
table=Tabel
#XMIT Add remote table menu button text in Data Builder Landing Pages
remoteTable=Tabel la distanță
#XMIT Add local table menu button text in Data Builder Landing Pages
localTable=Tabel local
#XMIT Add local table on file menu button text in Data Builder Landing Pages
ltfTable=Tabel local (fișier)
#XMIT Add data flow menu button text in Data Builder Landing Pages
dataFlow=Flux de date
#XMIT Add transformationFlow menu button text in Data Builder Landing Pages
transformationFlow=Flux de transformare
#XMIT Add task chain menu button text in Data Builder Landing Pages
taskChain=Lanț de sarcini
#XMIT Add IDT menu button text in Data Builder Landing Pages
IDT=Căutare inteligentă
#XMIT Add analytic model menu button text in Data Builder Landing Pages
queryModel=Model analitic
#XMIT Add replication flow menu button text in Data Builder Landing Pages
replicationFlow=Flux de replicare
#XMIT Add e/r model menu button text in Data Builder Landing Pages
ermodel=Model R-E
#XMIT Add data access control menu button text in Data Builder landing pages
dataAccessControl=Control de acces la date

#XMIT Add versions menu button text in Data Builder
versions=Versiuni
oldVersionLabel=Versiune
oldVersionLabel2=Versiunea {0}
createVersion=Creare versiune
versionHistory=Istoric versiuni

restoreVersion=Restaurare
editCommentForVersion=Editare comentariu
downloadCSNForVersion=Exportare în fișier CSN/JSON

newERModel=Model R-E nou
newGraphicalView=Imagine grafică nouă
newSqlView=Imagine SQL nouă
newTable=Tabel nou
newDataFlow=Flux de date nou
#XTXT: Breadcrumb for Databuilder -> New Analytic Model
newAnalyticModel=Model analitic nou
#XTXT: Breadcrumb for Databuilder -> New Data Access Control
newDataAccessControl=Control de acces la date nou
#XTXT: Breadcrumb for Databuilder -> New Intelligent Lookup
newIDT=Căutare inteligentă nouă
#XTXT: Breadcrumb for Databuilder -> New Task Chain
newTaskChain=Lanț de sarcini nou
#XTXT: Breadcrumb for Databuilder -> New Replication Flow
newReplicationFlow=Flux de replicare nou
#XTXT: Breadcrumb for Databuilder -> New Transformation Flow
newTransformationFlow=Flux de transformare nou

addERModel=Model nou de relație între entități
addGraphicalView=Imagine grafică nouă
addStory=Relatare nouă
addSqlView=Imagine SQL nouă
addTable=Tabel nou
addDataFlow=Flux de date nou
addQueryModel=Model analitic nou
#XTXT Intelligent Lookup tile in Databuilder landing page
addIDT=Căutare inteligentă nouă
#XTXT Task Chain tile in Databuilder landing page
addTaskChain=Lanț de sarcini nou
#XTXT Replication Flow tile in Databuilder landing page
addReplicationFlow=Flux de replicare nou
#XTXT Transformation Flow tile in Databuilder landing page
addTransformationFlow=Flux de transformare nou
#XTXT: Data Access Control tile in Data Builder landing page
addDataAccessControl=Control de acces la date nou
#XTXT: Breadcrumb for Databuilder -> Import CSV
importCsvBreadcrumb=Importare fișier CSV
copyEntity=Copiere
shareEntity=Partajare
addApp=Aplicație nouă
uploadDataTile=Importare fișier CSV
uploadDataTileTooltip=Importare fișier CSV pentru a crea tabel nou
uploadCsnFile=Importare fișier CSN
uploadCsnJsonFile=Importare obiecte din fișier CSN/JSON
importEntities=Importare entități
importRemoteTables=Importare tabele la distanță
importRemoteDACs=Importare permisiuni
validateRemoteTables=Validare tabele la distanță
cancel=Anulare
ok=OK
reset=Resetare
help=Ajutor
alert=Alarmă
checkDependencyFailed=Dependența nu a putut fi verificată.
descriptionTitle=Descriere
name=Nume
fileIsUsed=Fișierul selectat este utilizat de alte fișiere și nu poate fi șters.
columnIsUsed=Coloana selectată este utilizată de alte tabele sau imagini din acest spațiu sau din alte spații și nu poate fi ștearsă. Pentru a o șterge, asigurați-vă că nu este utilizată această coloană.
schemaIsUsed=Schema selectată este utilizată de alte fișiere și nu poate fi ștearsă.
usedIn=(utilizată în {0} fișiere)
usedInHasInaccessible=(utilizată în {0} fișiere, inclusiv în {1} dependențe inaccesibile)
confirm=Confirmare
txtRecentFiles=Fișiere recente
selectentityBreadcrumb={spaceId}
editorBreadcrumb={model}
cannotOpenModel=Imposibil de deschis {0} spațiu {1}. Se poate să nu existe în registru.
saving=Salvare în curs
#XMSG: Save takes more than 30 sec and switch to async
saveLongTime=Salvarea durează mai mult decât de obicei. Așteptați.
#XMSG: Confirm AI change message
confirmAIChange=Toate modificările generate de AI vor fi salvate, iar butoanele de evidențiere și revizuire vor fi eliminate.
#XTIT
titconfirmAI=Confirmare semantică generată de AI
#XMSG
semanticsCanceled=Generare anulată
deploying=Implementare în curs
openFile=Deschidere fișier
switchView=Schimbare imagine
dataView=Imagine de date
associationView=Imagine de asociere
general=Generalități
#XTOL
save=Salvare
#XTOL
deploy=Implementare
#XTOL
validateAndPreview=Validare SQL și previzualizare date
#XTOL
performanceLog=Jurnal de performanță

#XMSG: validation of sql statement was successfull
validateSQLOk=SQL este valabil.
#XMSG: validation of sql statement failed
validateSQLFailed=SQL este nevalabil.
#XSEL: location of error of sql validation
locationErrorSQL=Linia {0}, coloana {1}

#XTOL Display a list of errors / warnings from the validation
validationStatus=Mesaje de validare
view=Vizualizare
showHideSourceTree=Afișare sau mascare arbore sursă
showHideDataPreview=Afișare sau mascare previzualizare date
#XTOL
txtDataViewer=Vizualizator de date
#XTOL
expandOrCollapsToolbar=Expandare sau comprimare bară de instrumente
showHidejsonView=Afișare sau mascare CSN
details=Detalii
showHideProperties=Afișare sau mascare proprietăți
edit=Editare
addFromRepository=Adăugare din registru
import=Importare
importRemoteSource=Importare din conexiune
importCSN=Importare din fișier CSN
importCsnJson=Importare obiecte din fișier CSN/JSON
export=Exportare
#XTOL
tools=Instrumente
#XTOL
uploadBtnText=Încărcare
exportCSN=Exportare fișier CSN
exportCsnJson=Exportare în fișier CSN/JSON
refreshRemoteTable=Împrospătați pentru a importa modificările din sursa subiacentă
allFilesTab=Toate fișierele
erModelsTab=Modele R-E
viewsTab=Imagini
tablesTab=Tabele
sharedTab=Obiecte partajate
dataFlowsTab=Fluxuri de date
#XTXT: Flows tab text
flowsTab=Fluxuri
ilArtifactsTab=Căutări inteligente
queryModelsTab=Modele analitice
taskChainsTab=Lanțuri de sarcini
dataAccessControlTab=Controale de acces la date
#XTXT: Transformation Flows tree text
transformationFlowsTab=Fluxuri de transformare
#XTXT: Replication Flows tree text
replicationFlowsTab=Fluxuri de replicare
remoteTablesTab=Tabele la distanță
taskChainTab=Lanțuri de sarcini
saveAnyway=Salvare în orice caz
deployAnyway=Implementare în orice caz
dialogTitleSaveWithErrors=Mesaje de validare
dialogTitleDeployWithErrors=Mesaje de validare
importFile=Importare fișier
#XTXT: Delta Table tree text
deltaTableTab=Tabel delta
#XTOL

#XTIT: Title for Intelligent Lookups category under Repository Panel
intelligentLookupsTab=Căutări inteligente
#XTIT: Title for Analytic Models category under Repository Panel
analyticModelsTab=Modele analitice

#XBUT: Text for segmented buttons in data sources browser: Repository
repository=Registru
#XBUT: Text for segmented buttons in data sources browser: Others
others=Altele
#XFLD: Text for segmented buttons in data sources browser: BW Process Chain Sources
BW_PROCESS_CHAIN=Lanțuri de procese BW
#XFLD: Text for segmented buttons in data sources browser: SQL Script Procedure Sources
SQL_SCRIPT_PROCEDURE=Proceduri script SQL
#XFLD
othersTabSearch=Căutare în: "{0}"
#XBUT: Text for segmented buttons in data sources browser: Remote Sources
remoteSources=Surse
@itemsNoMoreThan=Imposibil de afișat peste {0} elemente.
@itemsPartialResult=Afișare {0} din {1} elemente. \nUtilizați câmpul de căutare pentru a filtra lista.
#XMSG: message text for message box of Create Connection
addRemoteSuccess=Conexiuni adăugate.
addRemoteError=Conexiuni nu au putut fi adăugate.
#XMSG: Warning message in source tree connection in case replication fails.
replicationWarning=Această conexiune nu poate fi utilizată pentru modelare flux de date. \n Deschideți conexiunea și salvați-o pentru a rezolva această problemă.
#XMSG: Warning message when d&d from repository shared object if existing objects name duplicated to cross space name
objectWithSpaceNameExist=Există deja un obiect "{0}" cu același nume ca spațiul sursă.\n Pentru a reutiliza obiectul de spațiu încrucișat, trebuie să ștergeți sau să redenumiți obiectul "{0}".
#XTIT: Connection Error
connectionError=Eroare de conexiune
#XMSG:(#UI_COMPONENT_RELATED) Connection Error. User can go to Connection UI to validate the connection and get more information
connectionErrorValidateForMore=A apărut o eroare la accesarea conexiunii. Deschideți aplicația "Conexiune" în panoul de navigare lateral, validați conexiunea și vizualizați detaliile. Dacă nu aveți acces la aplicația "Conexiune", contactați administratorul dvs.
#XTOL: Connection Error tooltip
connectionErrorTooltip=Imposibil de obținut date. Efectuați click pentru mai multe detalii.

enterModelName=Introduceți un nume de model.
enterValidModelname=Introduceți un nume de model valabil.
enterTableName=Introduceți un nume de tabel.
enterValidTableName=Introduceți un nume de tabel valabil.
modelSavedSuccessful={0} salvat(ă) cu succes.
modelRestoredSuccessful=Versiune restaurată {0}.
tableSavedSuccessful=Tabel salvat
deploymentSuccessful=Implementare reușită.
deltatablecapture=Tabel de captură delta
#XMSG: Text shown temporarily on tree nodes that are currently loading until real child nodes are available
loading=Încărcare...
#XTIT: Root node for all connections
remotesources=Conexiuni

#XMSG: Warning message about missing sources/objects
missingSources=Următoarele obiecte au fost șterse și trebuie eliminate din model.
missingObjects=Următoarele obiecte au fost șterse și eliminate din model.
cannotRevert=Următoarele obiecte au fost șterse. În consecință, nu este posibil să restaurați versiunea implementată.

#~~~~~~~~~~~~ Analytic Model editor ~~~~~~~~~~~~~~~~~
#XTIT: Title for query editor source data tab
sourceData=Date sursă
cubeSourcesTitle=Surse
measureSourcesListTitle=Surse măsură
dimensionSourcesListTitle=Surse de dimensiune

#XFLD: Default value for business name input
defaultBusinessName=Query nou

#XFLD: Query editor section header general
generalSetting=Generalități
clearFilter=Resetare
refresh=Împrospătare
customizeColumnsButton=Coloane
#Label for search result count
results=Rezultate
createdBy=Creat de:
modifiedOn=Modificat pe:
description=Descriere:

#XTIT: Title for query editor summary tab
summary=Rezumat și setări
createQueryModelBreadcrumb=Query nou

#XFLD: Add cube source
addButton=Adăugare
#XFLD: Preview cube source data
previewButton=Previzualizare
#XFLD: Preview data in table format
dataPreviewTable=Previzualizare date
#XFLD: Preview data in diagram format
dataPreviewDiagram=Diagramă

#~~~~~~~~~~~~ Upload File Texts ~~~~~~~~~~~~~~~~~

#XMSG: Success message after file upload
fileUploadDone=Fișierul a fost încărcat.

#XMSG: MessageToast for deleting files successfully
fileDeleted=Fișierele au fost șterse cu succes.

#XMSG: File Upload is only available if the table services flag "Data Management" is set to on
fileUploadNotAvailableForSteampunk=Tabelele cu gestiune de cereri activată nu pot fi încărcate.\nUtilizați un flux de date pentru a transfera datele în tabel.

#XMSG: Busy dialog for importing Csn File
importing=Import...

#XMSG: Message Toast for importing csn file successfully
importSuccess={0} importat cu succes.

#XMSG: Message Toast for importing Csn File
importStarted={0} este în curs de import. Vă vom notifica când importul este terminat.

#XMSG: Error message for import csn failed
importErrResponse=Eroare la importare {0}

#~~~~~~~~~~~~ Delete Data from Table Texts ~~~~~~~~~~~~~~~~~

#XTIT: Title of the confirmation dialog
deleteTableDataDialogTitle=Confirmare ștergere date
#XMSG: text in confirmation dialog that a user shall confirm the data deletion
deleteTableDataDialogContent=Această acțiune va șterge toate datele încărcate în tabelul "{0}". Sigur doriți să o efectuați?
#XBUT: delete Button in confirmation dialog, an additional button will be "Cancel"
deleteTableDataDialogButton=Ștergere date
#XMSG: Message in the loading indicator while truncate is in progress
deleteTableDataInProgress=Ștergere date tabel "{0}"...
#XMSG: Message in the loading indicator while delete is started
deletionStart=Ștergem datele tabelului "{0}" (local)

#~~~~~~~~~~~~ Delete Data from Table during deploy Texts ~~~~~~~~~~~~~~~~~
#XTIT: Title of the truncate table dialog
truncateDialogTitle=Confirmare trunchiere tabel
#XMSG: Dialog content that describes the confirm of the table truncation
truncateDialogContent=Tabelul conține date care trebuie trunchiate pentru a-l implementa. Doriți să trunchiați datele?
#XBUT: Button to confirm table truncate
truncateButton=Trunchiere și implementare
#XMSG: Message in the loading indicator while truncate is in progress
truncateInProgress=Trunchiere tabel în curs...
#Text for Parameters
showParameters=Afișare parametri

#XMSG: text in confirmation dialog that a user shall confirm saving or not before export full CSN if model is dirty
saveBeforeExport=Doriți să salvați modelul înainte de export? Dacă nu efectuați această acțiune, exportul va fi oprit.
#XMSG: MessageToast for exporting CSN file completed
exportingCompleted=Export terminat.

#~~~~~~~~~~~ Deployment Process ~~~~~~~~~~~~~~~~~~~
#XMIT
deploymentInProgress=Așteptați cât timp implementăm tabelul.
deploymentStarted=Implementare {0} în curs. Vă vom notifica când procesul este terminat.
deploymentStartedWithPersistencyExist={0} este implementat. Vă vom anunța când este implementat și când sunt șterse persistențele sale, inclusiv obiectele sale dependente (dacă sunt modificate).
deploymentFailure=Tabelul nu a putut fi salvat. Reîncercați mai târziu.
deployTable=Implementare tabel
enterExistingTableName=Acest nume tehnic există deja. Introduceți altul.
checkingTableName=Verificare nume...
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XBUT
txtPreviewSQL=Previzualizare SQL
#XBUT
txtExportRuntimeCSN=Exportare informații pentru suport
#YMSG: Description for data builder
importBW4Model=Importare model SAP BW/4 HANA
#XFLD: Description for Import From Connection Tile in Landing Page
importFromConnection=Importare din conexiune
#XTIT
ip_enterValuesToGenerateData=Introduceți valori pentru a genera datele dvs.
#XTIT
ip_inputParameters=Parametri de intrare
#XFLD
ip_name=Nume
#XFLD
ip_value=Valoare
@none=Niciunul
#XTEXT: Text for true dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
@param_true=adevărat
#XTEXT: Text for false dropdown of boolean datatype for input parameter - the value should be in small case matching the actual data
@param_false=fals
#XMSG: Error message for Data preview with Cross space object with Input parameters
ERR_CROSS_SPACE_INPUT_PARAM_NOT_SUPPORTED=Imposibil de previzualizat date:\nPrevizualizarea datelor pentru imaginile cu obiecte de spațiu încrucișat care conțin parametri de intrare nu este suportată.
#XMSG: Error message for Data preview with Repo browser object with Input parameters
ERR_REPO_BROWSER_INPUT_PARAM_NOT_SUPPORTED=Imposibil de previzualizat date :\nPrevizualizarea datelor nu este suportată în browserul registrului pentru entități cu parametri de intrare.\n Pentru a vizualiza datele acestei entități, deschideți-o în editorul său și utilizați vizualizatorul de date de acolo.
@noObjectsFound=Niciun obiect găsit

#~~~~~~~~~~~ Compatibility Contract ~~~~~~~~~~~~
#XMSG: MessageStrip for released views
releasedViewWarning=Acest obiect are starea de eliberare "Eliberat". Nu puteți efectua nicio modificare care va întrerupe consumul datelor sale de obiecte care depind de acestea.
#XMSG: MessageStrip for deprecated views
deprectedViewWarning=Acest obiect are starea de eliberare "Învechit". Nu puteți efectua nicio modificare care va întrerupe consumul datelor sale de obiecte care depind de acestea. Obiectul următor la care trebuie să migreze consumatorii este {0}.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#XMSG: Message strip for HANA update
updateHANAVersionMessageStripText=Vă recomandăm să aplicați SAP HANA Cloud Patch Update 2025.2.8 sau o versiune mai recentă pentru a obține îmbunătățiri de performanță și stabilitate.
#XMSG: Message strip "Learn More" link
learnMore=Mai multe informații

#XMSG: MessageStrip for versioned objects
versionedObjectWarning=Versiunea {0} ({1}) - numai pentru citire. Nu puteți salva sau implementa această versiune. Pentru a restaura această versiune, selectați Versiune -> Restaurare.
#XMSG: MessageStrip for versioned objects
versionedObjectWarningRestoreNS=Versiunea {0} ({1}) - numai pentru citire. Nu puteți salva, implementa sau restaura această versiune.
#XMSG: MessageStrip for restore object
restoreObjectWarning=Restaurați versiunea {0}. Revizuiți mesajele de validare și apoi efectuați click pe Salvare pentru a confirma restaurarea.
restoreObjectInfo=Restaurați versiunea {0}. Efectuați click pe Salvare pentru a confirma restaurarea.
#~~~~~~~~~~~ Wrangling ~~~~~~~~~~~~~~~~~~
#XMSG: MessageToast for wrangling fails to open
wranglingFailed=Imposibil de lansat import din CSV. Încercați din nou.
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

#~~~~~~~~~~~ Artefact Sharing ~~~~~~~~~~~~~~~~~~
#XTIT: Title for artefact sharing dialog
artefactSharingDialogTitle=Partajare {0} "{1}"
#XTIT: Title for mulitple artefact sharing dialog
multipleArtefactSharingDialogTitle=Partajare {0} modele
#XFLD: Label for adding spaces to share the artefact with
addSpaces=Adăugare spații
#XFLD: Placeholder for space multi input
enterSpace=Introducere spațiu
#XFLD: Label for selected models
selectedModels=Modele selectate
#XFLD: Label for shared access type
access=Acces
#XFLD: Read access type
read=Citire
#XBUT: Share Button
share=Partajare
#XFLD: Label to indicate with how many spaces the artefact is shared
sharedWithXSpacesPlural=Partajat cu {0} spații
#XFLD: Label to indicate with how many spaces the artefact is shared (singular)
sharedWithXSpacesSingular=Partajat cu 1 spațiu
#XBUT: Button to close dialog
close=Închidere
#XBUT: Button to unshare sharing
unshare=Anulare partajare
#XFLD: Text to be displayed in table when artefact has not been shared yet
noDataShareDialogTableText=Acest artefact nu a fost încă partajat.
#XFLD: Text to be displayed in space value help dialog search returns no results
spaceValueHelpNoData=Niciun spațiu găsit
#XTIT: Title for space value help dialog
selectSpaceValueHelpTitle=Selectare spațiu
#XMSG: Error message for loading sharing data
failedToLoadSharingData=A apărut o problemă la încercarea de regăsire a informațiilor partajate
#XMSG: Error message for loading sharing data
failedToSpaces=A apărut o problemă la încercarea de regăsire a spațiilor
#XMSG: Success message for sharing a single object
succeededToShareObject=Modelul este partajat
#XMSG: Success message for sharing a multiple objects
succeededToShareObjects=Modelele sunt partajate
#XMSG: Error message for sharing a single object that is not deployed.
failedToShareUndeployedObject=Imposibil de partajat model pentru că nu este încă implementat. Trebuie să îl implementați înainte de a-l partaja.
#XMSG: Error message for sharing multiple objects that are not deployed.
failedToShareUndeployedObjects=Imposibil de partajat modele pentru că nu sunt încă implementate. Trebuie să le implementați înainte de a le partaja.
#XMSG: Error message for sharing a single object to a locked space
failedToShareObjectLockedSpace=Imposibil de partajat model cu spațiul {0} pentru că spațiul este blocat.
#XMSG: Error message for sharing multiple objects to a locked space
failedToShareObjectsLockedSpace=Imposibil de partajat modele cu spațiul {0} pentru că spațiul este blocat.
#XMSG: Error message for sharing a single object
failedToShareObject=A apărut o problemă la partajarea modelelor
#XMSG: Error message for sharing multiple objects
failedToShareObjects=A apărut o problemă la partajarea modelelor
#XMSG: Success message for unsharing a single object
succeededToUnshareObject=Partajare model anulată
#XMSG: Success message for unsharing a multiple objects
succeededToUnshareObjects=Partajare modele anulată
#XMSG: Error message for unsharing a single object
failedToUnshareObject=A apărut o problemă la anularea partajării modelului
#XMSG: Error message for unsharing a multiple objects
failedToUnshareObjects=A apărut o problemă la anularea partajării modelelor
#XTIT: Dialog title for dependency dialog
dependencyDialogTitle=A apărut o eroare la anularea partajării {0}
#XMSG: Description text for dependency dialog (multiple models, multiple spaces)
dependencyDialogDescriptionPluralPlural=Nu am putut anula partajarea pentru {0} pentru că este utilizat de următoarele modele în diferite spații.
#XMSG: Description text for dependency dialog (multiple models, single space)
dependencyDialogDescriptionPluralSingular=Nu am putut anula partajarea pentru {0} pentru că este utilizat de următoarele modele într-un spațiu diferit.
#XMSG: Description text for dependency dialog (single model, single space)
dependencyDialogDescriptionSingularSingular=Nu am putut anula partajarea pentru {0} pentru că este utilizat de următorul model într-un spațiu diferit.
#XFLD: Label to indicate in how many models the other model is used in
sharingUsedInPlural=(utilizate în {0} modele)
sharingUsedInSingular=(utilizate în 1 model)
#XFLD Placeholder for inaccessible end user label of model in dependency dialog
inaccessibleEndUserLabel=Permisiuni lipsă pentru citire obiect
#XFLD Placeholder for inaccessible fields of model in dependency dialog
notAvailable=Nu există
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
#XCOL
txtType=Tip
txtMessage=Mesaj
txtLocation=Locație
#XMSG
txtNoProblems=Nicio problemă găsită
#XTOL
execute=Executare
#XMSG
cannotOpenSharedObject=Imposibil de deschis {0} pentru că nu aveți acces la spațiu {1}.
#XTOL
openJsonEditor=Comutați la editorul JSON.
#XTOL
txtTransformation=Adăugare proiecție
#XTOL
txtTransformationWithProjections=Editare proiecție
#XBUT
addTransformation=Adăugare proiecție
#XTOL
navToMonitoringView=Deschidere în monitor de integrare date
#XMSG Not suffcient privileges to preview data
txtNoPrivilegesPreviewData=Nu aveți suficiente privilegii pentru a previzualiza aceste date.
#XMSG: Info for data preview against persisted view (modified but not deployed again)
InfodataPreviewForPersitedView=Structura imaginii dvs. s-a modificat, iar datele sale salvate persistent nu mai sunt valabile. Este executat query-ul în raport cu sursele imaginilor...
#XMSG
technicalErrorDetails=Vă putem furniza următoarele informații pentru a vă ajuta pe dvs. sau un inginer SAP să rezolvați problema:<br><br>ID corelare: {0}<br>Stare HTTP: {2}<br>Mesaj tehnic: {1}
#XMSG
technicalErrorSearch=A apărut o eroare la căutarea informațiilor în spațiu.
#XMSG Operation Node name change feature.
JOIN_NODE_EMPTYNAME=Numele pentru join este gol.
#XMSG Operation Node name change feature.
UNION_NODE_EMPTYNAME=Numele pentru uniune este gol.
#XMSG Operation Node name change feature.
PROJECTION_NODE_EMPTYNAME=Numele pentru proiecție este gol.
#XMSG Operation Node name change feature.
CALCULATEDELMS_NODE_EMPTYNAME=Numele pentru elemente calculate este gol.
#XMSG Operation Node name change feature.
FILTER_NODE_EMPTYNAME=Numele pentru filtru este gol.
#XTXT: Impact and Lineage analysis
impactAndLineageDialogTitle=Analiză impact și origine
#XTOL: Impact and Lineage analysis button tooltip
impactButtonTooltip=Analiză impact și origine
#XTOL: Generate OData Request button tooltip
oDataButtonTooltip=Generare cerere OData
#XTOL: AI menu button tooltip
ai=AI
#XBUT: Menu item of generating semantics
generateSemantics=Generare semantică
#XCKL
selectSemanticUsage=Selectare utilizare semantică (fapt, dimensiune, text)
#XCKL
identifyColumns=Identificare măsuri și atribute și setare chei și tipuri semantice
#XCKL
notOverride=Fără înlocuire metadate coloană existente
#XTIT Dialog title for generating semantics
generateSemanticsTitle=Generare semantică
#XMSG: Message for generating semantics dialog
generateSemanticsMessage=Putem genera semantică și alte metadate pentru entitatea dvs. pentru a o pregăti pentru consum analitic.
#XMSG
generateSemanticsMessageStrip=Generarea poate dura mai mult decât de obicei din cauza numărului de coloane.
#XBUT: Generate button of generating semantics dialog
generateText=Generare
#XBUT: Cancel button of generating semantics dialog
cancelText=Anulare
#XGRP add new currency conversion
addCurrencyConversion=Conversie monedă nouă
#XFLD Currency Conversion Views
currencyConversion=Imagini de conversie monedă
#XFLD Business Name  of conversion labels
businessNameLabel=Nume comercial
#XFLD Technical Name of conversion labels
technicalNameLabel=Nume tehnic
#XBUT create button label
create=Creare
currencyConversionCreated=Conversie monedă creată
currencyConversionCreatedDeploymentStarted=Conversia de monedă a fost creată și implementarea a fost lansată.
#YMSG Currency conversion objects are being imported message
currencyConversionCreationStarted=Obiectele de conversie monedă sunt importate. Vă vom notifica când importul este terminat.
currencyConversionCreationFailed=Creare nereușită obiecte de conversie monedă.
#XFLD Source label
conversionSource=Sursă
#XFLD Tables label
conversionTables=Tabele
#XFLD Views label
conversionViews=Imagini
#XFLD Data Flows label
conversionDataFlows=Fluxuri de date
#XFLD Replication Flows label
conversionReplicationFlow=Flux de replicare
#XFLD Remote Tables label
conversionRemoteTables=Tabele la distanță
#XFLD Manual label
conversionSourceManual=Manual
#YINS Create TCUR* tabeles and views instruction
currencyConversionHint=Creați imaginile de conversie monedă TCUR* și obiectele de suport necesare fie de la o conexiune SAP, fie manual.
#YINS Select a source for creating a currency conversion
currencyConversionHintNew=Selectați o sursă pentru a crea imaginile de conversie monedă TCUR* (și, dacă este corespunzător, obiectele de suport necesare) în spațiul dvs. Puteți alege o conexiune SAP, un spațiu care a partajat tabelele TCUR* cu dvs. sau puteți decide să creați manual obiectele chiar dvs.
#YINS Hint that the user is able to share the currency conversion after their creation
currencyConversionShareHint=După ce ați creat imaginile de conversie monedă și obiectele de suport ale acestora, puteți partaja tabelele TCUR* cu alte spații. Spațiile în care partajați aceste tabele vor vedea apoi spațiul dvs. ca o sursă alternativă în câmpul Sursă de mai sus.

#XFLD Unit conversion Views
unitConversion=Imagini de conversie unitate
#XGRP add new unit conversion
addUnitConversion=Conversie unitate nouă
#YMSG Unit conversion objects are being imported message
unitConversionCreationStarted=Obiectele de conversie unitate sunt în curs de importare. Vă vom notifica când importul este terminat.
#YINS Create T006* tabeles and views instruction
unitConversionHint=Creați imaginile de conversie unitate T006* și obiectele de suport necesare fie de la o conexiune SAP, fie manual.
#YINS Select a source for creating a unit conversion
unitConversionHintNew=Selectați o sursă pentru a crea imaginile de conversie unitate T006* (și, dacă este corespunzător, obiectele de suport necesare) în spațiul dvs. Puteți alege o conexiune SAP, un spațiu care a partajat tabelele T006* cu dvs. sau puteți decide să creați manual obiectele chiar dvs.
#YINS Hint that the user is able to share the unit conversion after their creation
unitConversionShareHint=După ce ați creat imaginile de conversie unitate și obiectele de suport ale acestora, puteți partaja tabelele T006* cu alte spații. Spațiile în care partajați aceste tabele vor vedea apoi spațiul dvs. ca o sursă alternativă în câmpul Sursă de mai sus.

#XTIT: Section title Share Unit Conversion Tables
shareUnitConversionTables=Partajare tabele de conversie unitate
#XMSG
createRFInfoMsgNew=Utilizând SAP Datasphere integrat, puteți vizualiza lista de fluxuri de replicare care au fost create anterior, dar nu puteți deschide niciunul dintre fluxurile de replicare existente și nici nu puteți crea fluxuri noi.
#XMSG
#XTIT: Section title
shareCurrencyConversionTables=Partajare tabele de conversie monedă
#XFLD: Object type is a context
txtTypeContext=Context
#XFLD: Object type is a simple type
txtTypeSimpleType=Tip simplu

#XMSG
PROCESSINGS=Prelucrare...

#XBUT:Import button text
@importCsnFile=Importare fișier CSN
#XTIT:Import button text
@selectObjectsToImport=Selectare obiecte de importat
#XTIT
@alreadyInRepository=Deja în registru.
#XTIT
@confirm=Confirmare
#XMSG
@importConfirmOverrideText=Selecția dvs. depinde de un număr de obiecte neselectate care sunt deja prezente în registru. Doriți să reimportați aceste obiecte neselectate și să suprascrieți versiunile de registru? Se pot pierde unele date din cauza modificărilor de structură.
#XMSG
@waitingToImport=Gata de import
#XCOL
@label=Nume comercial
#XCOL
@status=Stare

#XCOL
@name=Nume tehnic
#XCOL
@type=Tip
#XCOL
@remoteHost=Gazdă la distanță
#XCOL
@remoteTable=Tabel la distanță
#XCOL: Type and Semantic usage
@typeAndSemantic=Tip (utilizare semantică)
#XMSG
@csnFileTypeMissmatch=Sunt suportate doar fișierele CSN.
#XMSG
@csnFileInvalidFormat=Fișierul csn nu are un format csn valabil.
#XMSG
@csnFileInvalidFormatMissingSourceLangInI18n=Nu există șiruri pentru limba sursă a spațiului "{0}" în fișierul "{1}". Limba sursă a spațiului și a fișierului importat trebuie să corespundă.
#XMSG
@csnFileInvalidFormatMissingKeysInSourceLangNode=Unele șiruri din fișierul "{0}" lipsesc pentru limba sursă "{1}". Definiți toate șirurile pentru limba sursă.
#XMSG
@csnFileInvalidFormatRedundantI18nSection=Un document care conține șiruri localizate nu poate fi importat dacă spațiul nu definește nicio limbă sursă. Configurați spațiul dvs. pentru a selecta una dintre limbile furnizate de document.
#XMSG
@csnFileEmpty=Fișierul csn este gol.
#XMSG
@importCompleted=Import terminat
#XTIT
@alreadyInModel=Deja în model curent.
#XTIT
@addRepositoryObjects=Adăugare obiecte de registru
#XBUT:Import button text
@ok=OK
#XTIT
package=Folder
#XMSG
PREREQUISITES=Condiții prealabile
#XMSG
NOREMOTEACCESS=Acces la distanță nesuportat
#XMSG
DATAVALIDATIONISNOTAVAILABLE=Validare date indisponibilă
#XMSG
DATAVALIDATIONNOTAVAILABLEFORREMOTEOBJECT=Acest obiect (sau unul sau mai multe dintre sursele sale) nu este replicat în SAP Datasphere. Validarea datelor nu este disponibilă.
#XMSG
NODATAACCESSACCESS=Controale de acces la date nesuportate
#XMSG
INFO=informații
#XMSG
DATAVALIDATIONNOTAVAILABLEFORDACOBJECT=Acest obiect (sau unul sau mai multe dintre sursele sale) este protejat de un control de acces la date. Validarea datelor nu este disponibilă.
#XMSG
NOPARAMS=Parametri de intrare nesuportați
#XMSG
DATAVALIDATIONNOTAVAILABLEFORPARAMSOBJECT=Acest obiect (sau una sau mai multe dintre sursele sale) conține parametri de intrare. Validarea datelor nu este disponibilă.
#XCOL
RULE=Regulă
#XCOL
CATEGORY=Categorie
#XCOL
OBJECT=Obiect
#XCOL
MESSAGE=Mesaj
#XMSG
VALIDATEDON=Validat pe
#XMSG
COPYSQL=Copiere SQL
#XMSG
DOWNLOADCSN=Descărcare CSN
#XMSG
@getCsnFail=Definiția CSN nu a putut fi accesată.
#XTIT
DETAIL=Detaliu
#XMSG
NOTVALIDATED=Nevalidat
#XMSG
DETAILS=Detalii
#XMSG
ALL=Toate
#XMSG
SQLVALIDATIONMESSAGE=Pentru a obține o listă completă de înregistrări afectate, efectuați click pe "Copiere SQL", creați o imagine SQL nouă, lipiți codul în aceasta și apoi previzualizați datele.
#XMSG
COPYSQLSCRIPT=Copiere SQLScript
#XMSG
SQLSCRIPTVALIDATIONMESSAGE=Pentru a obține o listă completă de înregistrări afectate, efectuați click pe "Copiere script SQL", creați o imagine SQL nouă, setați "Limbă" la "SQLScript (funcție tabel)", lipiți codul în aceasta, salvați și implementați și apoi previzualizați datele.
#XMSG
DOWNLOADCSN_VALIDATIONMESSAGE=\nPentru a obține un fișier CSN care conține definiții de entitate modificate care pot rezolva aceste erori, efectuați click pe „Descărcare CSN”.
#XMSG
DATAVALIDATIONALREADYRUNNING=Validarea de date este deja în execuție.
#XTXT
RUNNING=În execuție
#XTXT
FAILED=Nereușit
#XMSG
@Messages=Mesaje
#XLNK
@INVALIDSTATE=Stare de eliberare nevalabilă
#XMSG
@forceImport=Forțare import
#XMSG
@selected=Selectate: {0}
#XMSG
validationMessagesList={0}\n\n
#XMSG
cannotImportMsg=Următoarele obiecte nu pot fi importate: \n -{0}
#XMSG
importConfirmation=Sigur doriți să forțați importul acestui fișier?
#XTIT: Title for warning dialog
statChangeWarningTitle=Inversare la stare eliberare anterioară
#XMSG
COPIED=Copiat
ERROR=eroare
#XMSG
CANVALIDATE=Poate valida
#XMSG
changeNotPermitted=Această modificare nu este permisă în mod normal, deoarece nu confirmă ciclul de existență al stării de eliberare standard.
#XMSG
importNotPermitted=Importarea acestor obiecte ar modifica "Stare eliberare" a {0} obiecte în moduri care nu sunt standard:
#XMSG
objectStateChange=- {0} (de la "{1}" la "{2}")
#XMSG
NOTRELEASED=Neeliberat
#XTIT
@importOverriteReleasestate=Obiectele care sunt deja prezente în registru pot fi selectate pentru reimport doar dacă stările lor de eliberare sunt consistente cu obiectele pe care le vor înlocui.
#XTIT
revertConfirmationDialogTitle=Inversare la stare eliberare anterioară
#XFLD
revertButton=Inversare
#XTIT
saveAsState=Salvare ca {0}
#XMSG
objectSetToWarning=Ați setat "Stare eliberare" a acestui obiect la "{0}".
#XMSG
saveAsReleasedWarning=După ce un obiect este eliberat, acesta trebuie să rămână disponibil cu o structură stabilă până când este învechit. Un obiect eliberat poate fi învechit doar dacă este selectat un obiect succesor.
#XMSG
continueAs=Când efectuați click pe "Salvare ca {0}", nu veți putea anula această acțiune. Doriți să continuați?
#XMSG
saveAsDeprecatedSuccessor=Ați setat "Stare eliberare" a acestui obiect la "Învechit" și ați alocat obiectul "{0}" ca succesorul său.
#XMSG
saveAsDeprecatedWarning=După ce un obiect este învechit, acesta trebuie să rămână în această stare cel puțin un an (și cel puțin doi ani de la data la care a fost eliberat). După acest timp, acesta poate fi scos din uz.
#XMSG
saveAsDecommissionedWarning=După ce un obiect este scos din uz, acesta poate fi șters în orice moment.
# XMSG
RELEASED=Eliberat
# XMSG
DEPRECATED=Învechit
# XMSG
DECOMMISSIONED=Scos din uz
# XMSG
WARN_SUCCESSOR_RELEASE_STATE_CHANGED=Nu puteți modifica starea de eliberare pentru "{0}" din "{1}" în "{2}", deoarece este setată ca succesor la următoarele obiecte învechite: {3}
# XMSG
DEPRECATED_VIEW=\n- "{0}"
# XMSG
DATA_VALIDATION_GROUP_MESSAGE=Validarea acestei reguli nu este disponibilă deoarece:
# XBUT
VALIDATE=Validare
# XBUT
VIEWANALYZER=Lansare analizor de imagini
# XBUT
CANCELVALIDATE=Anulare validare
# XSMG
DATA_VALIDATION_WARNING_MESSAGE=Una sau mai multe surse ale acestui obiect nu sunt replicate în SAP Datasphere. Validarea datelor \npoate fi mai lentă decât de obicei și poate afecta performanța sistemului la distanță.
# XSMG
DATA_VALIDATION_VIEW_ANLYZER_MESSAGE=\nPentru detalii și recomandări, analizați imaginea în Analizorul de imagini.
