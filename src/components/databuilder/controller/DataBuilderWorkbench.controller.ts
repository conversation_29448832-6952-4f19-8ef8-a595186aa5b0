/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { IObjectDependency, ObjectKind } from "@sap/deepsea-types";
import { State } from "@sap/dwc-circuit-breaker";
import { SpaceStatus } from "../../../../shared/spaces/types";
import { IEditorAggregatedValidations, IEditorDetails, IPreviewSQL, ISectionItem } from "../../abstractbuilder/api";
import { isTransformationFlowEnabled } from "../../abstractbuilder/commonservices/FeatureFlagCheck";
import {
  AbstractWorkbench,
  AbstractWorkbenchClass,
  IWorkbenchToolbarInfo,
} from "../../abstractbuilder/controller/AbstractWorkbench.controller";
import { ValidationsControllerClass } from "../../abstractbuilder/controller/Validations.controller";
import { getSpaceName } from "../../abstractbuilder/utility/BuilderUtils";
import * as RepositoryUtils from "../../abstractbuilder/utility/RepositoryUtils";
import { smartExtend } from "../../basecomponent/controller/BaseController.controller";
import * as BusinessCatalogUtility from "../../businesscatalogs/utility/BusinessCatalogsUtility";
import { DeploymentStatus } from "../../commonmodel/api/DocumentStorageService";
import { DataWarehouse } from "../../commonmodel/csn/csnAnnotations";
import {
  cloneJson,
  fillParamterDefaultValue,
  getEntityNameFromCSN,
  handleCsnForDataPreview,
  handlePersistedViewCsnForDataPreview,
  handleRemoveMixinForDataPreview,
  updateParamDefaults,
} from "../../commonmodel/csn/csnUtils";
import { CDSDataType, DBViewType, DataCategory } from "../../commonmodel/model/types/cds.types";
import {
  getListOfConsumedInputParameters,
  updateAlreadyAvailableIPValues,
  updateIPQueryString,
} from "../../commonmodel/utility/CommonUtils";
import { releaseStateValues } from "../../commonmodel/utility/CompatibilityContractUtils";
import { IEntityImpact, IRepoElement } from "../../commonmodel/utility/ElementsImpactLoader";
import { ILocalizedValidationMessages } from "../../commonui/control/validation/ValidationMessages";
import { QualifiedClassNames } from "../../ermodeler/js/statics/const/er.model";
import * as commonFormatter from "../../ermodeler/js/utility/commonFormatter";
import {
  objectStatusIconFormatter,
  objectStatusTextFormatter,
  objectStatusTooltipFormatter,
} from "../../ermodeler/js/utility/sharedFunctions";
import { ToolName } from "../../reuse/utility/Constants";
import { Format } from "../../reuse/utility/Format";
import { MessageHandler } from "../../reuse/utility/MessageHandler";
import { loadResourceBundle } from "../../reuse/utility/MessageLoader";
import { NamingHelper } from "../../reuse/utility/NamingHelper";
import { showDialog } from "../../reuse/utility/UIHelper";
import { isCircuitBreakerYellowStateEnabled } from "../../shell/featureState/CircuitBreakerYellowStateFeatureState";
import { ShellContainer } from "../../shell/utility/Container";
import { Crud } from "../../shell/utility/Crud";
import { DACType, ModelType, Repo, SpaceType } from "../../shell/utility/Repo";
import { DWCFeature, EventType } from "../../shell/utility/ShellUsageCollectionService";
import { DataBuilderAction } from "../../shell/utility/UsageActions";
import { SQLEditorComponentClass } from "../../sqleditor/Component";
import { prepareCSNforSQLScriptDataPreview } from "../../sqleditor/utility/SqlEditorUtility";
import { isOutputNodePersisted } from "../../viewmonitor/viewpersistencyservice/ServiceConsumption";
import { AbstractDataBuilderEditorComponentClass } from "../AbstractDataBuilderEditorComponent";
import { DataBuilderWorkbenchComponentClass } from "../Component";
import { DataBuilderToolAdapter } from "../DataBuilderToolAdapter";
import { NullEditorComponent } from "../NullEditorComponent";
import "../css/styles.css";
import { Editor, NewModelTypes, Section } from "../utility/Constants";
import {
  dataPreviewOnRepoTree,
  openObjectInEditor,
  openValidationPopover,
  parseDataPreviewFilter,
} from "../utility/DatabuilderHelper";

export class DataBuilderWorkbench extends AbstractWorkbench {
  public commonFormatter = commonFormatter;
  public erResourceModel: sap.ui.model.resource.ResourceModel;
  public gvResourceModel: sap.ui.model.resource.ResourceModel;
  public dfResourceModel: sap.ui.model.resource.ResourceModel;
  public rfResourceModel: sap.ui.model.resource.ResourceModel;
  public ilResourceModel: sap.ui.model.resource.ResourceModel;
  public commonResourceModel: sap.ui.model.resource.ResourceModel;
  public rtmResourceModel: sap.ui.model.resource.ResourceModel;
  public vmResourceModel: sap.ui.model.resource.ResourceModel;
  public tcResourceModel: sap.ui.model.resource.ResourceModel;
  public ilTaskLogsResourceModel: sap.ui.model.resource.ResourceModel;
  public tfResourceModel: sap.ui.model.resource.ResourceModel;

  public onInit(): void {
    // eslint-disable-next-line dot-notation
    window["performanceLogger"]?.enterMethod({ name: "databuilder workbench, init" }, { isInitialization: true });
    const bundleName = require("../i18n/i18n.properties");
    this.resourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
    });
    this.createCommonResourceModel();
    this.createERModelerResourceModel();
    this.createGVResourceModel();
    this.createDFResourceModel();
    this.createRFResourceModel();
    this.createILResourceModel();
    this.createTCResourceModel();
    this.createRTMResourceModel();
    this.createVMResourceModel();
    if (isTransformationFlowEnabled()) {
      this.createTFResourceModel();
    }
    this.toolAdapter = DataBuilderToolAdapter.getInstance();
    super.onInit();
    this.getView().setModel(this.erResourceModel, "i18n_erd");
    this.setupToolAdapter();
    RepositoryUtils.createNamespacesModel();
    this.getView().addStyleClass("databuilderWorkbench");
    // eslint-disable-next-line dot-notation
    window["performanceLogger"]?.leaveMethod({ name: "databuilder workbench, init" }, { isInitialization: true });
  }

  public processPreviewError(sError, name?, msgId?): string {
    const aError = sError && sError.match(/Could not find table\/view\s+(.+)\s+in schema/);
    const activeComponent = this.workbenchModel.getProperty("/activeEditor");
    const isMDMEditor = [
      Editor.ERMODELER,
      Editor.TABLEEDITOR,
      Editor.SQLBUILDER,
      Editor.VIEWBUILDER,
      Editor.TRANSFORMATIONFLOW,
      Editor.MODELING,
    ].includes(activeComponent);

    if (aError) {
      name = name || aError[1];
      sError = this.getText(isMDMEditor ? "cannotViewNotDeployed" : "cannotPreviewNotDeployed", name); // If the error match the RegExp, then convert sError to: Object ''{0}'' has never been deployed. Please do the deployment before preview.
    } else if (sError === "Repository object not found.") {
      if (name) {
        sError = this.getText(msgId || "cannotFindEntity", [name]);
      } else {
        sError = this.getText(isMDMEditor ? "txtNoPrivilegesViewData" : "txtNoPrivilegesPreviewData");
      }
    }
    return sError;
  }

  public onPreviewSQLMenu() {
    const activeComponent = this.getActiveEditor();
    if (activeComponent && activeComponent.isPreviewSQLVisible()) {
      activeComponent.getPreviewSQL();
    }
  }

  public onPreviewSQL(obj?: IPreviewSQL): void {
    const predecessors = obj.predecessors;
    const sError = this.getModelErrors(predecessors, "");
    if (sError !== "") {
      obj.problems = [
        {
          type: "Error",
          message: sError,
        },
      ];
    }
    const activeComponent = this.getActiveEditor(true);
    if (activeComponent && activeComponent.isPreviewSQLVisible()) {
      activeComponent.onPreviewSQL(obj);
    }
  }

  public onPreviewIntermediateSQL() {
    const activeComponent = this.getActiveEditor(true);
    if (activeComponent && activeComponent.isPreviewSQLVisible()) {
      activeComponent.getPreviewSQL({ isIntermediate: true });
    }
  }

  public onExportRuntimeCSN() {
    const activeComponent = this.getActiveEditor();
    if (activeComponent && activeComponent.isExportRuntimeCSNVisible()) {
      activeComponent.onExportRuntimeCSN(this.spaceId, this.isDirty());
    }
  }

  public onPreviewFilter(oEvent) {
    if (oEvent && oEvent.getParameter) {
      const column = oEvent.getParameter("column");
      let sFilter = oEvent.getParameter("value");
      sFilter = sFilter !== undefined ? sFilter.trim() : "";
      if (column) {
        const isString = column.getDefaultFilterOperator() === "Contains";
        const oFilter = parseDataPreviewFilter(sFilter, isString);

        // Modify CSN
        const columnKey = this.getPreviewUIColumnSetting(column.getIndex())?.columnKey;
        const columnLabel = column.getName();

        const sDisplayFilter = this.getText("displayFilter", [columnLabel, sFilter]);
        const currentData = this.oDataPreviewColumns[this.dataPreviewSettingsKey];
        if (currentData === undefined) {
          // For non CSN data preview, should still use the default client side filtering
          return;
        }
        if (sFilter === "") {
          delete currentData.filterInfo[columnKey];
        } else {
          currentData.filterInfo[columnKey] = { sFilter, oFilter, sDisplayFilter, isString };
        }
        this.setPreviewSettingFilterBy();
        this.setPreviewFilterText();
        // Refresh data preview
        this.previewData(
          this.dataPreviewObjectName,
          cloneJson(currentData.initialCsn),
          currentData.spaceName,
          currentData.originalObject,
          currentData.symbol
        );

        // Prevent default filter
        oEvent.preventDefault();
      }
    }
  }

  public onPreviewSort(oEvent) {
    if (oEvent && oEvent.getParameter) {
      const column = oEvent.getParameter("column");
      if (column) {
        const sSort = oEvent.getParameter("sortOrder"); // Ascending or Descending

        // Modify settings CSN
        const columnKey = this.getPreviewUIColumnSetting(column.getIndex())?.columnKey;
        const currentData = this.oDataPreviewColumns[this.dataPreviewSettingsKey];
        if (currentData === undefined) {
          // For non CSN data preview, should still use the default client side sorting
          return;
        }
        // if (sSort === currentData?.sortInfo?.[columnKey]?.sSort) {
        //   // We don't have UI to remove sort, so we have to: If sort on same order, we treate it as remove sort on the column
        //   delete currentData.sortInfo[columnKey];
        // } else {
        //   currentData.sortInfo[columnKey] = { sSort: sSort };
        // }
        // Only support sorting on single column as before
        if (columnKey !== undefined) {
          currentData.sortInfo = {}; // Reset sort info
          currentData.sortInfo[columnKey] = { sSort: sSort };
        }

        // Refresh data preview
        this.previewData(
          this.dataPreviewObjectName,
          cloneJson(currentData.initialCsn),
          currentData.spaceName,
          currentData.originalObject,
          currentData.symbol
        );

        // Prevent default sort
        oEvent.preventDefault();
      }
    }
    // Default sort, no need coding
  }

  // Refresh data preview
  public async onRefreshPress() {
    if (this.dataPreviewObjectNameFromTree !== undefined) {
      // If it is refresh data preview just after data preview from repository tree
      const fileObject = {} as any;
      fileObject.definitions = await Repo.getModelDetails(this.spaceName, this.dataPreviewObjectNameFromTree, ["csn"]);
      fileObject.definitions = fileObject.definitions.csn?.definitions;
      dataPreviewOnRepoTree.call(this, fileObject, false, this.previewData.bind(this));

      return;
    }

    let galileiModel;
    const activeComponent = this.getCurrentActiveEditor();
    if (activeComponent && activeComponent.getSelectedGalileiObject) {
      galileiModel = await activeComponent.getSelectedGalileiObject();
    }
    if (galileiModel === undefined && activeComponent?.getGalileiModelSync) {
      galileiModel = activeComponent.getGalileiModelSync();
    }
    if (galileiModel === undefined) {
      return; // Should never happen
    }
    let modelName =
      galileiModel.config?.dwcEntity ||
      galileiModel.alias ||
      galileiModel.technicalName ||
      galileiModel.shortName ||
      galileiModel.name ||
      "";
    if (
      modelName !== undefined &&
      (galileiModel.config?.dwcEntity ||
        galileiModel.isTable ||
        galileiModel.isView ||
        galileiModel.isEntity ||
        galileiModel.isModel)
    ) {
      modelName = NamingHelper.encodeDataPreviewName(modelName);
    }
    let output, oCsn;
    if (galileiModel._isSqlEditorModel) {
      output = galileiModel.output;
    } else {
      output = galileiModel.container?.output;
    }
    /* if (output?.dbViewType === DBViewType.TABLEFUNCTION) {
      // FIXME NOT GOOD! Databuilder should only handle generic parts!
      oCsn = await prepareCSNforSQLScriptDataPreview(output, NamingHelper.decodeDataPreviewName(modelName);;
    }*/
    if (output && output.parameters?.length > 0) {
      const consumedParams = getListOfConsumedInputParameters(galileiModel, output);
      updateAlreadyAvailableIPValues(consumedParams, this.workbenchModel);

      if (this.isPreviewdataActive() && consumedParams.length > 0) {
        this.showIPPopUPForDataPreview(galileiModel, consumedParams);
      } else {
        this.workbenchModel.setProperty("/isIPSelected", false);
        // It can also provide realtime oDocument to reset the settings if changed.
        const activeEditor = this.getCurrentActiveEditor(this.isObjectPageActive());
        if (activeEditor) {
          activeEditor.onToggleDetails(modelName, galileiModel);
        } else {
          const document = await (activeComponent as any).getCSN(modelName, galileiModel, {
            rootObject: galileiModel,
            dataPreview: true,
          });
          this.previewDataForObject(galileiModel, document);
        }
      }
    } else {
      this.workbenchModel.setProperty("/isIPSelected", false);
      this.workbenchModel.setProperty("/ribbonContent", "");
      this.workbenchModel.setProperty("/paramDefaults", "");
      // It can also provide realtime oDocument to reset the settings if changed.
      const activeEditor = this.getCurrentActiveEditor(this.isObjectPageActive());
      if (activeEditor) {
        activeEditor.onToggleDetails(modelName, galileiModel);
      } else if ((activeComponent as any).getCSN) {
        const document = await (activeComponent as any).getCSN(modelName, galileiModel, {
          rootObject: galileiModel,
          dataPreview: true,
        });
        this.previewDataForObject(galileiModel, document);
      }
    }
  }

  /**
   * @override
   */
  public openDocumentEditor(document: any, mustSave?) {
    openObjectInEditor(this, document, mustSave);
  }

  public async previewData(
    modelName: string,
    oDocument?: any,
    spaceName?: string,
    originalObject?: any,
    oSymbol?: any,
    dataToBeFetched?: boolean,
    paramValues?: {},
    objectStatus?: ObjectStatus | string
  ): Promise<void> {
    const activeEditor = this.getActiveEditor();
    const customPreviewResult = await activeEditor.customPreview(
      modelName,
      oDocument,
      spaceName,
      originalObject,
      oSymbol,
      dataToBeFetched,
      paramValues,
      objectStatus
    );
    if (!customPreviewResult) {
      return this.previewDataWithChecks(
        modelName,
        oDocument,
        spaceName,
        originalObject,
        oSymbol,
        dataToBeFetched,
        paramValues,
        objectStatus
      );
    }
  }

  public async previewDataWithChecks(
    modelName: string,
    oDocument?: any,
    spaceName?: string,
    originalObject?: any,
    oSymbol?: any,
    dataToBeFetched?: boolean,
    paramValues?: {},
    objectStatus?: ObjectStatus | string
  ): Promise<void> {
    return super.previewData(
      modelName,
      oDocument,
      spaceName,
      originalObject,
      oSymbol,
      dataToBeFetched,
      paramValues,
      objectStatus
    );
  }

  /**
   * Previews the data for the current given model object
   */

  public async previewDataForObject(
    oObject,
    oCsn?,
    notDeployed?,
    aPredecessors?,
    oSymbol?,
    dataToBeFetched: boolean = true
  ) {
    const previewControls = this.getPreviewDataUIControls();
    if (!previewControls) {
      return;
    }
    if (this.previewDataPending) {
      return;
    }
    if (!oObject) {
      return;
    }

    let modelName;
    // For Graphical View Output, isTable and isView are true.
    const isOutput = oObject.classDefinition.name === "Output";
    if (!isOutput && (oObject.isTable || oObject.isView || oObject.isEntity)) {
      modelName = oObject.alias || oObject.name;
    } else {
      modelName = oObject.alias || oObject.shortName || oObject.technicalName || oObject.name || "";
    }
    let sError;
    // If object is not deployed yet, we skip to preview it
    // ER, TE, table/view in GV can know the related object is deployed or not, other scenairoes have to convert sError
    if (notDeployed) {
      sError = "Could not find table/view " + modelName + " in schema";
      this.showPreviewError(sError, previewControls, modelName);
      return;
    }

    // Check current Node's all Predecessors Recursively and itself, if there is nay validate error, show all their errors and break
    sError = this.getModelErrors(aPredecessors, sError);
    const featureFlags = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();
    if (sError) {
      sError = this.getText("cannotViewInvalidDependencies", sError);
      this.showPreviewError(sError, previewControls, modelName);
      return;
    }

    if (!oObject) {
      return;
    }
    let spacename;
    spacename = this.spaceId;
    const model = await this.getGalileiModel();
    // Preview on entity directly, isModel is for sql view
    if (modelName !== undefined && (oObject.isTable || oObject.isView || oObject.isEntity || oObject.isModel) && oCsn) {
      const TempModelName = NamingHelper.encodeDataPreviewName(modelName); // Avoid to replace the CSN of this object
      const def = oCsn?.definitions[modelName] || oCsn?.definitions[TempModelName];
      if (def && this.isSqlEntity(def)) {
        modelName = oCsn?.definitions[TempModelName] ? TempModelName : modelName; // Avoid to replace the CSN of this object
      } else {
        modelName = TempModelName;
      }
    }
    const originalModelName = NamingHelper.decodeDataPreviewName(modelName);
    let consumedParams = [],
      isEREditor = false;
    if (oObject.output?.dbViewType === DBViewType.TABLEFUNCTION) {
      const activeEditor = this.getActiveEditor();
      if (
        featureFlags.DWCO_TRF_DPREVIEW_SQLSCRIPT &&
        activeEditor?.getId() === Editor.TRANSFORMATIONFLOW &&
        this.isSecondaryEditorActive
      ) {
        oCsn = await prepareCSNforSQLScriptDataPreview(oObject.output, oObject.name, this.spaceName, activeEditor);
      } else {
        // FIXME NOT GOOD! Databuilder should only handle generic parts!
        oCsn = await prepareCSNforSQLScriptDataPreview(oObject.output, oObject.name, this.spaceName);
      }
    }
    if (model.output && model.output.parameters && model.output.parameters.length > 0) {
      consumedParams = getListOfConsumedInputParameters(oObject, model.output, oCsn);
    } else if (model.qualifiedClassName === QualifiedClassNames.ER_MODELER_MODEL) {
      isEREditor = true;
      consumedParams = getListOfConsumedInputParameters(
        oObject,
        oObject,
        oCsn?.definitions ? oCsn.definitions[originalModelName] : ""
      );
    }
    const isOldPreview = !!this.getActiveEditor().getDetailsConfiguration().useDefault?.useAlwaysDefault;
    if (isOldPreview) {
      let isCrossSpaceParameterAvailable = false;
      if (!isEREditor) {
        // Once ER Modeler uses MDM Preview, we can remove this check for ER Modeler

        for (const key in oCsn?.definitions) {
          if (key !== modelName) {
            const entity = oCsn.definitions[key];
            if (entity && entity.params && Object.keys(entity.params).length > 0 && entity[DataWarehouse.space_name]) {
              isCrossSpaceParameterAvailable = true;
              break;
            }
          }
        }
        /* TODO : Remove later
        Code below is used to provide a user friendly message until we implement data preview in cross space objects with input parameters */
        let isCrossSpace = false;
        if (this.isSqlEntity(oCsn?.definitions?.[modelName]) && isCrossSpaceParameterAvailable) {
          isCrossSpace = true;
        }
        if (isEREditor && oObject.isCrossSpace && oObject.parameters?.length > 0) {
          isCrossSpace = true;
        }
        oObject?.entities?.forEach(function (entity) {
          if (entity.isCrossSpace && entity.parameters?.length > 0) {
            isCrossSpace = true;
          }
        });

        if (isCrossSpace) {
          sError = this.getText("ERR_CROSS_SPACE_INPUT_PARAM_NOT_SUPPORTED");
          this.showPreviewError(sError, previewControls, originalModelName);
          return;
        }
      }
    }
    /* TODO Ends*/
    const finallyConsumedParams = updateAlreadyAvailableIPValues(
      consumedParams,
      this.workbenchModel,
      isEREditor ? oObject?.qualifiedName : undefined
    );
    const isSilenetPreview =
      featureFlags?.DWCO_MODEL_VALIDATION && this.workbenchModel?.getProperty("/isSilentPreview");
    if (
      finallyConsumedParams.length > 0 &&
      dataToBeFetched &&
      (isEREditor ? oObject.deploymentStatus === 1 || oObject.deploymentStatus === 2 : true) &&
      !isSilenetPreview
    ) {
      this.showIPPopUPForDataPreview(
        oObject,
        consumedParams,
        oCsn?.definitions ? oCsn.definitions[originalModelName] : null,
        isEREditor
      );
    } else if (finallyConsumedParams.length > 0 && !dataToBeFetched) {
      await this.updateCSNWithIPValues(
        oObject,
        consumedParams,
        model.output,
        dataToBeFetched,
        oCsn?.definitions ? oCsn.definitions[originalModelName] : null,
        isEREditor
      );
    } else if (finallyConsumedParams.length === 0 && consumedParams.length > 0) {
      await this.updateCSNWithIPValues(
        oObject,
        consumedParams,
        model.output,
        dataToBeFetched,
        oCsn?.definitions ? oCsn.definitions[originalModelName] : null,
        isEREditor
      );
    } else if (finallyConsumedParams.length === 0 && consumedParams.length === 0) {
      this.workbenchModel.setProperty("/isIPSelected", false);
      modelName = oCsn?.definitions?.[modelName] ? modelName : originalModelName;
      delete oCsn?.definitions?.[modelName]?.params;
      await this.previewData(modelName, oCsn, spacename, oObject || oCsn, undefined, dataToBeFetched);
    } else {
      this.workbenchModel.setProperty("/isIPSelected", false);
      this.workbenchModel.setProperty("/ribbonContent", "");
      this.workbenchModel.setProperty("/paramDefaults", "");
      modelName = oCsn?.definitions?.[modelName] ? modelName : originalModelName;
      await this.previewData(modelName, oCsn, spacename, oObject || oCsn, undefined, dataToBeFetched);
    }
  }

  public getModelErrors(aPredecessors: any, sError: string) {
    if (aPredecessors) {
      const gvResourceBundle = this.gvResourceModel && this.gvResourceModel.getResourceBundle();
      const erResourceBundle = this.erResourceModel && this.erResourceModel.getResourceBundle();
      const dfResourceBundle = this.dfResourceModel && this.dfResourceModel.getResourceBundle();
      const commonResourceBundle = this.commonResourceModel && this.commonResourceModel.getResourceBundle();
      if (gvResourceBundle && erResourceBundle && dfResourceBundle && commonResourceBundle) {
        aPredecessors.forEach((p) => {
          const isSkylineERModel = p?.resource?.model?.isSkylineERModel;
          if (
            p &&
            p.aggregatedValidations &&
            p.aggregatedValidations.status === "error" &&
            p.aggregatedValidations.validations
          ) {
            p.aggregatedValidations.validations.forEach((v) => {
              if (v && v.status === "error" && v.message) {
                let error = "";
                if (v.message.groupId === "i18n_erd") {
                  error = erResourceBundle.getText(v.message.id, v.message.params);
                } else if (v.message.groupId === "i18n_vb") {
                  error = gvResourceBundle.getText(v.message.id, v.message.params);
                } else if (v.message.groupId === "i18n_df") {
                  error = dfResourceBundle.getText(v.message.id, v.message.params);
                } else if (v.message.groupId === "i18n_commonmodel") {
                  error = commonResourceBundle.getText(v.message.id, v.message.params);
                } else if (isSkylineERModel) {
                  error = erResourceBundle.getText(v.message.id, v.message.params);
                }
                sError = sError === undefined ? error : sError + "\r\n" + error;
              }
            });
          }
        });
      }
    }
    return sError;
  }

  /**
   * @override
   */
  public updateDate(
    oGalileiModel: any,
    modification: boolean,
    deployment: boolean,
    deployObjectId?: string,
    curSpace?: string
  ): Promise<boolean> {
    return new Promise((resolve) => {
      const aDeployedObjects = [];
      if (oGalileiModel) {
        // FIXME WORKBENCH need to do move inside each editor
        // here we should just call this.getActiveEditor().updateDate()
        aDeployedObjects.push(oGalileiModel);
        if (oGalileiModel.output) {
          // Graphical View or SQL View
          aDeployedObjects.push(oGalileiModel.output);
          if (oGalileiModel.nodes) {
            // Entities and DimensionNodes in Graphical View
            oGalileiModel.nodes.forEach(function (node) {
              if (node.classDefinition.name === "Entity" || node.classDefinition.name === "DimensionNode") {
                aDeployedObjects.push(node);
              }
            });
          }
        } else if (oGalileiModel.cube) {
          // Cube Builder
          aDeployedObjects.push(oGalileiModel.cube);
        } else {
          // Table Editor, ER Modeler
          if (oGalileiModel.entities) {
            oGalileiModel.entities.forEach(function (entity) {
              aDeployedObjects.push(entity);
            });
          }
        }

        let hasObjectsToGetDeployStatus = false;

        if (aDeployedObjects.length > 0) {
          const objectsToGetDeployStatus = {};
          aDeployedObjects.forEach(function (object) {
            // We still need get from repository and update object's modificationDate, deploymentDate and ["#deploymentExecutionStatus"]
            if (object.name && object.classDefinition && object.classDefinition.name !== "Output") {
              hasObjectsToGetDeployStatus = true;
              objectsToGetDeployStatus[object.name] = object;
            }
          });

          if (hasObjectsToGetDeployStatus) {
            const self = this;
            setTimeout(() => {
              if (curSpace) {
                // Clear all objects' cache
                Object.keys(objectsToGetDeployStatus).forEach((key) => {
                  if (key) {
                    Crud.get().clearCache([
                      { type: Repo.space, name: curSpace },
                      { type: Repo.model, name: key },
                    ]);
                  }
                });
              }
              // Only get current space's objects' properties
              RepositoryUtils.getObjectListProperties(getSpaceName(), Object.keys(objectsToGetDeployStatus))
                .then((properties) => {
                  if (properties) {
                    properties.forEach((prop) => {
                      if (prop) {
                        const object = objectsToGetDeployStatus[prop.name];
                        RepositoryUtils.updateObjectFileInfo(object, prop);
                        // Sync model deploy info to outpput
                        if (object && object.classDefinition.name === "Model" && oGalileiModel.output) {
                          const output = oGalileiModel.output;
                          output.modificationDate = object.modificationDate;
                          output.deploymentDate = object.deploymentDate;
                          output.setObjectStatus(object["#objectStatus"], object["#deploymentExecutionStatus"]);
                          // Sync Release Contract Dates: Check at least release date!
                          if (
                            output.updateReleaseContractCSN &&
                            typeof output.updateReleaseContractCSN === "function"
                          ) {
                            const releaseStateChanged = !(output.initialState === output.releaseState);
                            output.initialState = output.releaseState;
                            // Reset successor object if needed before updating CSN!
                            if (
                              output.successorObject &&
                              output.releaseState !== releaseStateValues.DEPRECATED &&
                              output.releaseState !== releaseStateValues.DECOMMISSIONED
                            ) {
                              output.successorObject = undefined;
                            }
                            output.updateReleaseContractCSN(prop);
                            if (releaseStateChanged) {
                              output.validate();
                              this.updateWorkbenchMessageStripEnvModel(this, {
                                isReleased: output.releaseState === releaseStateValues.RELEASED,
                                isDeprecated: output.releaseState === releaseStateValues.DEPRECATED,
                                successorObject:
                                  output.releaseState === releaseStateValues.DEPRECATED
                                    ? output.successorObject
                                    : undefined,
                              });
                            }
                          }
                        }
                      }
                    });

                    oGalileiModel.resource.clearUndoStack();
                    oGalileiModel.resource.clearRedoStack();
                  }
                  self.activeEditor.onModelDateUpdated();
                  resolve(true);
                })
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                .catch((error: Error) => {
                  resolve(false);
                });
            }, 10);
          } else {
            this.activeEditor.onModelDateUpdated();
            resolve(true);
          }
        } else {
          resolve(true);
        }
      } else {
        resolve(true);
      }
    });
  }
  /**
   * @override
   */
  public getResourceBundleExtension(): sap.base.i18n.ResourceBundle[] {
    const bundleName = require("../i18n/i18n.properties");
    const resourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: bundleName,
    });
    const commonBundleName = require("../../commonmodel/i18n/i18n.properties");
    const commonResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: commonBundleName,
    });
    const erBundleName = require("../../ermodeler/i18n/i18n.properties");
    const erResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: erBundleName,
    });
    const sqlEditorBundleName = require("../../sqleditor/i18n/i18n.properties");
    const sqlEditorResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: sqlEditorBundleName,
    });
    return [
      resourceModel.getResourceBundle(),
      commonResourceModel.getResourceBundle(),
      erResourceModel.getResourceBundle(),
      sqlEditorResourceModel.getResourceBundle(),
    ];
  }

  /**
   * @inheritdoc
   * @implements
   */
  onEditorObjectsSelectionChanged(
    selectedObjects: any[],
    updateDatapreviewCallback: Function,
    oData?: { model; selectedSymbols? }
  ): void {
    if (oData.model.output && oData.model.output.parameters && oData.model.output.parameters.length > 0) {
      let consumedParams = [];
      consumedParams = getListOfConsumedInputParameters(selectedObjects[0], oData.model.output);
      const finallyConsumedParams = updateAlreadyAvailableIPValues(consumedParams, this.workbenchModel);
      if (this.isPreviewdataActive() && finallyConsumedParams.length > 0) {
        this.showIPPopUPForDataPreview(selectedObjects[0], consumedParams);
      } else if (finallyConsumedParams.length === 0 && consumedParams.length > 0) {
        this.updateCSNWithIPValues(selectedObjects[0], consumedParams, oData.model.output);
      } else {
        this.workbenchModel.setProperty("/isIPSelected", false);
        updateDatapreviewCallback();
      }
    } else if (selectedObjects?.[0]?.resource?.model?.qualifiedClassName === "sap.cdw.ermodeler.Model") {
      let consumedParams = [];
      const selectedObject = selectedObjects[0];
      const notDeployed =
        (selectedObject.isTable || selectedObject.isView || selectedObject.isEntity) &&
        !(selectedObject.deploymentStatus === DeploymentStatus.Active || selectedObject.deploymentDate);
      consumedParams = notDeployed ? [] : getListOfConsumedInputParameters(selectedObject, selectedObject);
      const finallyConsumedParams = notDeployed
        ? []
        : updateAlreadyAvailableIPValues(consumedParams, this.workbenchModel, selectedObject.qualifiedName);
      if (this.isPreviewdataActive() && finallyConsumedParams.length > 0) {
        this.showIPPopUPForDataPreview(selectedObjects[0], consumedParams, undefined, /* isEREditor */ true);
      } else if (finallyConsumedParams.length === 0 && consumedParams.length > 0) {
        this.updateCSNWithIPValues(
          selectedObjects[0],
          consumedParams,
          undefined,
          undefined,
          undefined,
          /* isEREditor */ true
        );
      } else {
        this.workbenchModel.setProperty("/isIPSelected", false);
        updateDatapreviewCallback();
      }
    } else {
      super.onEditorObjectsSelectionChanged(selectedObjects, updateDatapreviewCallback, oData);
    }
  }

  /**
   * @inheritdoc
   * @override
   */
  public createToolbarModel(): sap.galilei.ui5.GalileiModel {
    return new sap.galilei.ui5.GalileiModel({});
  }

  /**
   * @override
   */
  public customizeWorkbenchToolbar(defaultToobarContentInfo: IWorkbenchToolbarInfo): void {
    // Share button
    const deployButtonId = this.createId("deploy");
    defaultToobarContentInfo.insertItemAfter(deployButtonId, {
      id: this.createId("shareEntity"),
      text: "{i18n>shareEntity}",
      icon: "sap-icon://share-2",
      press: this.onShareEntity.bind(this),
      visible: {
        parts: [
          { path: "workbenchEnv>/activeEditor" },
          { path: "toolbarModel>/isNew" },
          { path: "workbenchEnv>/canShareModel" },
        ],
        formatter: this.formatterShareEntityVisible.bind(this),
      },
      enabled: {
        parts: [
          { path: "workbenchEnv>/activeEditor" },
          { path: "toolbarModel>/isNew" },
          { path: "workbenchEnv>/canShareModel" },
        ],
        formatter: this.formatterShareEntityEnabled.bind(this),
      },
      customData: { key: "actionId", value: `${ToolName.DataBuilder}/workbench/shareEntity`, writeToDom: false },
    });

    const importButtonId = this.createId("import");
    defaultToobarContentInfo.insertItemBefore(importButtonId, {
      id: this.createId("addFromRepository"),
      visible: {
        parts: [
          { path: "workbenchEnv>/activeEditor" },
          { path: "workbenchEnv>/secondaryEditorId" },
          { path: "workbenchEnv>/canCreateOrUpdateModel" },
        ],
        formatter: this.formatterEditAddFromRepositoryVisible.bind(this),
      },
      text: "{i18n>addFromRepository}",
      icon: "sap-icon://add",
      press: "onAddFromRepository",
    });

    const importCSN = this.createId("importCSN");
    defaultToobarContentInfo.insertItemBefore(importCSN, {
      id: this.createId("importRemoteSource"),
      text: "{i18n>importRemoteSource}",
      press: "onImportRemoteSource",
      enabled: {
        parts: [{ path: "circuitbreaker>/DataHANA" }, { path: "workbenchEnv>/spaceAccessInfo/status" }],
        formatter: this.formatterAddFromConnectionEnabled.bind(this),
      },
      visible: {
        parts: [
          { path: "workbenchEnv>/activeEditor" },
          { path: "workbenchEnv>/secondaryEditorId" },
          { path: "workbenchEnv>/canCreateOrUpdateModel" },
        ],
        formatter: this.formatterEditAddFromRepositoryVisible.bind(this),
      },
    });

    const exportButtonId = this.createId("exportCSN");
    defaultToobarContentInfo.insertItemAfter(exportButtonId, {
      id: this.createId("previewSQLMenu"),
      text: "{i18n>txtPreviewSQL}",
      press: "onPreviewIntermediateSQL",
      visible: {
        parts: [{ path: "workbenchEnv>/activeEditor" }],
        formatter: this.formatterPreviewSQLVisible.bind(this),
      },
    });

    // Export RuntimeCsn for Analytic Model
    const previewSQLButtonId = this.createId("previewSQLMenu");
    defaultToobarContentInfo.insertItemAfter(previewSQLButtonId, {
      id: this.createId("exportRuntimeCSN"),
      text: "{i18n>txtExportRuntimeCSN}",
      press: "onExportRuntimeCSN",
      visible: {
        parts: [{ path: "workbenchEnv>/activeEditor" }],
        formatter: this.formatterExportRuntimeCSN.bind(this),
      },
    });

    const editGroup = defaultToobarContentInfo.groups[2].items;
    editGroup.push({
      id: this.createId("upload"),
      visible: {
        parts: [
          { path: "workbenchEnv>/activeEditor" },
          { path: "workbenchEnv>/canCreateOrUpdateModel" },
          { path: "workbenchEnv>/hasConsumptionUpdatePrivilege" },
          { path: "toolbarModel>/isRemote" },
          { path: "toolbarModel>/isLocalSchema" },
          { path: "toolbarModel>/isCrossSpace" },
          { path: "toolbarModel>/deploymentDate" },
          { path: "workbenchEnv>/isModelTimeDimension" },
          { path: "workbenchEnv>/isCustomerDPTenantAndSAPSpace" },
          { path: "toolbarModel>/fileStorage" },
        ],
        formatter: this.formatterUploadDataVisible.bind(this),
      },
      text: "{i18n>uploadData}",
      icon: "sap-icon://upload",
      press: "onUploadData",
      customData: { key: "actionId", value: `${ToolName.DataBuilder}/workbench/upload`, writeToDom: false },
    });
    editGroup.push({
      id: this.createId("hierarchy"),
      visible: {
        parts: [
          { path: "workbenchEnv>/activeEditor" },
          { path: "toolbarModel>/dataCategory" },
          { path: "toolbarModel>/fileStorage" },
        ],
        formatter: this.formatterShowHierarchiesButton.bind(this),
      },
      text: "{i18n>hierarchies}",
      expandedButtonText: {
        parts: [
          { path: "workbenchEnv>/activeEditor" },
          { path: "toolbarModel>/table/allHierarchies" },
          { path: "toolbarModel>/allHierarchies" },
        ],
        formatter: this.formatterShowHierarchiesCount.bind(this),
      },
      icon: "sap-icon://sac/hierarchy",
      press: "onOpenHierarchy",
      enabled: {
        parts: [{ path: "workbenchEnv>/activeEditor" }, { path: "toolbarModel>/dataCategory" }],
        formatter: this.formatterEnableHierarchiesButton.bind(this),
      },
    });
    editGroup.push({
      id: this.createId("deleteData"),
      visible: {
        parts: [
          { path: "workbenchEnv>/activeEditor" },
          { path: "workbenchEnv>/canCreateOrUpdateModel" },
          { path: "workbenchEnv>/hasConsumptionUpdatePrivilege" },
          { path: "toolbarModel>/isRemote" },
          { path: "toolbarModel>/isLocalSchema" },
          { path: "toolbarModel>/isCrossSpace" },
          { path: "toolbarModel>/deploymentDate" },
          { path: "workbenchEnv>/isModelTimeDimension" },
          { path: "workbenchEnv>/isCustomerDPTenantAndSAPSpace" },
          { path: "privilege>/DWC_DATAINTEGRATION/update" },
          { path: "privilege>/DWC_CONSUMPTION/update" },
        ],
        formatter: this.formatterDeleteDataVisible.bind(this),
      },
      enabled: {
        parts: [
          { path: "workbenchEnv>/activeEditor" },
          { path: "toolbarModel>/isRemote" },
          { path: "toolbarModel>/isLocalSchema" },
          { path: "toolbarModel>/deploymentDate" },
        ],
        formatter: this.formatterDeleteDataEnabled.bind(this),
      },
      tooltip: {
        parts: [{ path: "workbenchEnv>/activeEditor" }],
        formatter: this.formatterDeleteDataButtonText.bind(this),
      },
      text: {
        parts: [{ path: "workbenchEnv>/activeEditor" }],
        formatter: this.formatterDeleteDataButtonText.bind(this),
      },
      icon: "sap-icon://eraser",
      press: "onDeleteTableData",
      customData: { key: "actionId", value: `${ToolName.DataBuilder}/workbench/deleteData`, writeToDom: false },
    });
    editGroup.push({
      id: this.createId("editCSN"),
      visible: {
        parts: [{ path: "workbenchEnv>/activeEditor" }],
        formatter: this.formatterShowEditCSNButton.bind(this),
      },
      text: "{i18n>editCSNAnnotations}",
      icon: "sap-icon://syntax",
      press: "onEditAnnotations",
      enabled: {
        parts: [{ path: "workbenchEnv>/activeEditor" }],
        formatter: this.formatterEnableEditCSNButton.bind(this),
      },
    });

    editGroup.push({
      id: this.createId("refreshTable"),
      text: "{i18n>refreshRemoteTable}",
      visible: {
        parts: [
          { path: "workbenchEnv>/activeEditor" },
          { path: "workbenchEnv>/canCreateOrUpdateModel" },
          { path: "toolbarModel>/isRemote" },
          { path: "toolbarModel>/isLocalSchema" },
          { path: "toolbarModel>/isCrossSpace" },
          { path: "toolbarModel>/location" },
        ],
        formatter: this.formatterRefreshVisible.bind(this),
      },
      enabled: {
        parts: [
          { path: "workbenchEnv>/activeEditor" },
          { path: "toolbarModel>/isRemote" },
          { path: "toolbarModel>/isLocalSchema" },
          { path: "toolbarModel>/isRemoteDetailsLoaded" },
        ],
        formatter: this.formatterRefreshEnabled.bind(this),
      },
      tooltip: "{i18n>refreshRemoteTable}",
      icon: "sap-icon://refresh",
      press: "onRefreshTable",
      customData: { key: "actionId", value: `${ToolName.Monitoring}/editor/refresh`, writeToDom: false },
    });

    defaultToobarContentInfo.insertItemAfter(deployButtonId, {
      id: this.createId("rundataflow"),
      text: "{i18n>execute}",
      icon: "sap-icon://play",
      press: this.onRunDataflowPressed.bind(this),
      visible: {
        path: "workbenchEnv>/activeEditor",
        formatter: this.formatterRunDataflowVisible.bind(this),
      },
      enabled: {
        parts: [{ path: "workbenchEnv>/activeEditor" }],
        formatter: this.formatterRunDataFlowEnabled.bind(this),
      },
      customData: { key: "actionId", value: `${ToolName.DataBuilder}/workbench/rundataflow`, writeToDom: false },
    });

    editGroup.push({
      id: "addRFTransformation",
      tooltip: {
        parts: [{ path: "workbenchEnv>/toolbar/projectionExistsInSelectedDataset" }],
        formatter: this.textFormatterProjectionLabelRF.bind(this),
      },
      icon: "sap-icon://customize",
      press: this.onAddTransformationPressed.bind(this),
      enabled: {
        parts: [{ path: "workbenchEnv>/toolbar/isRFAddTransformationEnabled" }],
        formatter: this.formatterRFTransformationEnabled.bind(this),
      },
      visible: {
        parts: [{ path: "workbenchEnv>/activeEditor" }, { path: "workbenchEnv>/secondaryEditorId" }],
        formatter: this.formatterRFTransformationVisible.bind(this),
      },
    });

    if (sap.ui.getCore().getModel("featureflags").getProperty("/INFRA_DWC_TWO_TENANT_MODE")) {
      const toolsGroup = defaultToobarContentInfo.groups[3].items;
      toolsGroup.push({
        id: this.createId("navToMonitoringView"),
        text: "{i18n>navToMonitoringView}",
        icon: "sap-icon://sac/simulate",
        press: this.onNavToMonitoringViewPressed.bind(this),
        enabled: {
          parts: [
            { path: "workbenchEnv>/activeEditor" },
            { path: "workbenchEnv>/toolbar/isNavToMonitoringViewEnabled" },
          ],
          formatter: this.formatterNavToMonitoringViewEnabled.bind(this),
        },
        visible: {
          parts: [{ path: "workbenchEnv>/activeEditor" }],
          formatter: this.formatterNavToMonitoringViewVisible.bind(this),
        },
      });
      toolsGroup.push({
        id: this.createId("openEmailNotification"),
        text: "{i18n>openEmailNotification}",
        icon: "sap-icon://SAP-icons-TNT/content-modifier",
        press: this.onOpenEmailNotificationPressed.bind(this),
        enabled: {
          parts: [{ path: "workbenchEnv>/activeEditor" }, { path: "workbenchEnv>/toolbar/isEmailNotificationEnabled" }],
          formatter: this.formatterEmailNotificationEnabled.bind(this),
        },
        visible: {
          parts: [{ path: "workbenchEnv>/activeEditor" }],
          formatter: this.formatterEmailNotificationVisible.bind(this),
        },
      });
    }

    if (sap.ui.getCore().getModel("featureflags").getProperty("/DWCO_MODELING_GEN_AI_SEMANTIC_ENRICHMENT")) {
      const toolsGroup = defaultToobarContentInfo.groups[3].items;
      toolsGroup.push({
        isAIMenuButton: true,
        id: this.createId("ai"),
        tooltip: "{i18n>ai}",
        visible: {
          parts: [{ path: "workbenchEnv>/activeEditor" }, { path: "workbenchEnv>/canConsumeAI" }],
          formatter: this.formatterAIMenuButtonVisible.bind(this),
        },
        enabled: {
          parts: [{ path: "workbenchEnv>/activeEditor" }, { path: "workbenchEnv>/canConsumeAI" }],
          formatter: this.formatterAIMenuButtonEnabled.bind(this),
        },
        items: [
          {
            text: "{i18n>generateSemantics}",
            key: "generateSemantics",
            isConfiguration: true,
            callback: this.onGenerateSemantics.bind(this),
          },
        ],
      });
    }
  }

  /**
   * called when toolbar "GenerateSemantics" menu item is pressed
   * @param {*} event
   * @memberof DataBuilderWorkbench
   */
  public onGenerateSemantics(event): void {
    const activeComponent = this.getActiveEditor();
    // Get key from custom data
    const key = event.getSource().getCustomData()[0].getValue();
    if (key === "generateSemantics") {
      // Open AIConfig dialog
      sap.ui.require(["sap/skyline/tools/ai/AIConfigurationDialog"], (AIConfigurationDialog: any) => {
        const configuration = activeComponent["aiConfiguration"] || {
          selectSemanticUsage: {
            index: 1,
            text: this.getText("selectSemanticUsage"),
            selected: true,
          },
          identifyColumns: {
            index: 2,
            text: this.getText("identifyColumns"),
            selected: true,
          },
          notOverride: {
            index: 3,
            text: this.getText("notOverride"),
            selected: true,
            indent: 1, // child of identifyColumns
            weight: 0, // default weight is 1, 0 means no weight. If selected checkboxes' total weight is 0, then disable the generate button
          },
        };
        activeComponent["aiConfiguration"] = configuration;
        // Open AI Configuration Dialog
        const title = this.getText("generateSemanticsTitle");
        const description = this.getText("generateSemanticsMessage");
        const generateText = this.getText("generateText");
        const cancelText = this.getText("cancelText");
        // Get the number of columns
        const columns = activeComponent.getAIEntity()?.orderedElements?.length || 0;
        // Show message if columns > 100
        const showMessage = columns > 100;
        const message = this.getText("generateSemanticsMessageStrip");
        const allowedAIDataCategories = [
          DataCategory.SQLFACT,
          DataCategory.DIMENSION,
          DataCategory.TEXT,
          DataCategory.DATASET,
          DataCategory.FACT, // Analytical Dataset (deprecated) still supported
        ];
        const showMessage2 = !allowedAIDataCategories.includes(activeComponent.getAIEntity()?.dataCategory); // Show message and disable options checkboxes and generate button if not support
        const message2 = this.getText("generateSemanticsMessageStrip2");
        const oAIConfigurationDialog = new AIConfigurationDialog("oAIConfigurationDialogId", {
          title,
          description,
          configuration,
          generate: (configuration) => {
            activeComponent.generateSemantics(configuration);
          },
          cancel: (configuration) => {
            // Do nothing
          },
          generateText,
          cancelText,
          showMessage,
          message,
          showMessage2,
          message2,
        } as any);
        oAIConfigurationDialog.open();
      });
    }
  }

  /**
   * Tells if section button is visible
   * @param editor the active main editor id
   * @param objectPageId the active object page editor id
   */
  public formatterPreviewSQLVisible(editorId: Editor): boolean {
    const activeEditor = this.getEditor(editorId);
    return activeEditor.isPreviewSQLVisible();
  }

  public formatterExportRuntimeCSN(editorId: Editor): boolean {
    const activeEditor = this.getEditor(editorId);
    return activeEditor.isExportRuntimeCSNVisible();
  }

  public previewSQLDisplayFormatter(editorId: Editor) {
    return this.formatterPreviewSQLVisible(editorId);
  }

  public previewNoDataTextFormatter(message: string, isCurrentTablePreviewValid: boolean): string {
    return this.formatterPreviewNoDataText(message, isCurrentTablePreviewValid);
  }

  /**
   * @override
   * FIXME WORKBENCH. This normally should be pârt of the abstract workbench.
   * Move it to abstractWorkbench when validations.view.xml is moved from ERModeler component.
   */
  public displayValidationsDialog(
    deploy: boolean,
    aggregationValidations: IEditorAggregatedValidations
  ): Promise<ILocalizedValidationMessages> {
    const self = this;
    let controller: ValidationsControllerClass;
    return new Promise((resolve, reject) => {
      const forceDeploy = deploy && aggregationValidations?.status === sap.cdw.commonmodel.ValidationStatus.STATUS_WARN;
      showDialog(
        require("../../abstractbuilder/view/validations.view.xml"),
        self.getText(deploy ? "dialogTitleDeployWithErrors" : "dialogTitleSaveWithErrors"),
        deploy ? (forceDeploy ? self.getText("deployAnyway") : undefined) : self.getText("saveAnyway"),
        self.getText(deploy ? (forceDeploy ? "cancel" : "ok") : "cancel"),
        /* viewData*/ undefined,
        /* contentWidth*/ "500px",
        /* contentHeight*/ "300px",
        /* i18nModel*/ undefined,
        {
          galileiModel: {
            aggregatedValidations: aggregationValidations,
          },
          i18nResources: {
            i18n_vb: this.gvResourceModel,
            i18n_erd: this.erResourceModel,
            i18n_df: this.dfResourceModel,
            i18n_rf: this.rfResourceModel,
            i18n_commonmodel: this.commonResourceModel,
            i18n_il: this.ilResourceModel,
            i18n_tc: this.tcResourceModel,
            i18n_tf: this.tfResourceModel,
            "sap.dwc.remoteTable": this.rtmResourceModel,
            "sap.dwc.persistedView": this.vmResourceModel,
            "sap.dwc.ilTaskLogs": this.ilTaskLogsResourceModel,
          },
        },
        /* state*/ undefined,
        /* onAfterOpen*/ (dialog) => {
          const validationView = dialog.getContent()[0] as sap.ui.core.mvc.View;
          controller = validationView.getController() as ValidationsControllerClass;
          validationView.setModel(self.getView().getModel("i18n"), "i18n");
          const okButton = validationView?.byId("ok") as sap.m.Button;
          okButton?.setEnabled(true);
          if (okButton && aggregationValidations?.isBlocker) {
            okButton.setTooltip(self.localizeText("savePrevented"));
            okButton.setEnabled(false);
          }
        },
        undefined,
        undefined,
        /* onBeforeOpen*/ (dialog) => {
          const validationView = dialog?.getContent()?.[0] as sap.ui.core.mvc.View;
          const okButton = validationView?.byId("ok") as sap.m.Button;
          okButton?.setEnabled(false);
        }
      )
        .then(() => {
          resolve(controller?.getLocalizedMessages());
          controller?.initMessages();
        })
        .catch((e) => {
          // Cancel save
          if (e) {
            // should never happen
            console.log("[ERROR] unexpected error", e);
          }
          reject("Canceled");
        });
    });
  }

  public onImportRemoteSource(event: IEvent<sap.m.Button, {}>): void {
    const activeComponent = this.getActiveEditor(true);
    if (activeComponent.isSupportImportRemoteSource()) {
      // Record usage
      ShellContainer.get()
        .getUsageCollectionService()
        .recordAction({
          action: DataBuilderAction.ImportFromRemoteSource,
          feature: DWCFeature.DATA_BUILDER,
          eventtype: EventType.CLICK,
          options: [
            {
              param: "target",
              value: this.activeEditor.getId(),
            },
          ],
        });
      activeComponent.onImportRemoteSource(event);
    }
  }

  /**
   * Calback of upload toolbar button
   * @param event sapui5 event
   * @param objectpageMode tells if called on objectpage mode
   */
  public onUploadData(event: IEvent<sap.m.Button, {}>, objectpageMode: boolean = false): void {
    const activeEditor = this.getCurrentActiveEditor(objectpageMode);
    // Record usage
    ShellContainer.get()
      .getUsageCollectionService()
      .recordAction({
        action: DataBuilderAction.UploadCSV,
        feature: DWCFeature.DATA_BUILDER,
        eventtype: EventType.CLICK,
        options: [
          {
            param: "target",
            value: activeEditor.getId(),
          },
        ],
      });
    activeEditor.onUploadData();
  }

  public async validateSql(event: IEvent<sap.m.Button, {}>, isDeployment: boolean = false): Promise<any> {
    const sqlEditorCompponent = this.editorLoader.editorIdToComponent[Editor.SQLBUILDER] as SQLEditorComponentClass;
    if (sqlEditorCompponent) {
      return sqlEditorCompponent.validateSql(event, isDeployment);
    }
  }

  public onOpenHierarchy(event: IEvent<sap.m.Button, {}>, objectpageMode: boolean = false) {
    const activeEditor = this.getCurrentActiveEditor(objectpageMode);
    // Record usage
    ShellContainer.get()
      .getUsageCollectionService()
      .recordAction({
        action: DataBuilderAction.OpenHierarchy,
        feature: DWCFeature.DATA_BUILDER,
        eventtype: EventType.CLICK,
        options: [
          {
            param: "target",
            value: activeEditor.getId(),
          },
        ],
      });
    activeEditor.onOpenHierarchy(); // FIXME implement this
  }

  public onEditAnnotations(event: IEvent<sap.m.Button, {}>, objectpageMode: boolean = false) {
    const activeEditor = this.getCurrentActiveEditor(objectpageMode);
    // Record usage
    ShellContainer.get()
      .getUsageCollectionService()
      .recordAction({
        action: DataBuilderAction.EditAnnotations,
        feature: DWCFeature.DATA_BUILDER,
        eventtype: EventType.CLICK,
        options: [
          {
            param: "target",
            value: activeEditor.getId(),
          },
        ],
      });
    activeEditor.onEditAnnotations();
  }

  public async onDeleteTableData(event: IEvent<sap.m.Button, {}>, objectpageMode: boolean = false) {
    const activeEditor = this.getCurrentActiveEditor(objectpageMode);
    try {
      // Record usage
      ShellContainer.get()
        .getUsageCollectionService()
        .recordAction({
          action: DataBuilderAction.DeleteData,
          feature: DWCFeature.DATA_BUILDER,
          eventtype: EventType.CLICK,
          options: [
            {
              param: "target",
              value: activeEditor.getId(),
            },
          ],
        });
      await activeEditor.onDeleteTableData();
    } catch (error) {
      return;
    }
  }

  public async onRefreshTable(event: IEvent<sap.m.Button, {}>, objectpageMode: boolean = false) {
    const activeEditor = this.getCurrentActiveEditor(objectpageMode);
    try {
      // Record usage
      ShellContainer.get()
        .getUsageCollectionService()
        .recordAction({
          action: DataBuilderAction.RefreshTable,
          feature: DWCFeature.DATA_BUILDER,
          eventtype: EventType.CLICK,
          options: [
            {
              param: "target",
              value: activeEditor.getId(),
            },
          ],
        });
      await activeEditor.onRefreshTable();
    } catch (error) {
      return;
    }
  }

  /**
   * Tells if upload data button is visible
   * @param editor the active main editor id
   * @param objectPageId the active object page editor id
   */
  public formatterUploadDataVisible(
    editorId: string,
    canCreateOrUpdateModel: boolean,
    hasConsumptionUpdatePrivilege: boolean,
    isRemote?: boolean,
    isLocalSchema?: boolean,
    isCrossSpace?: boolean,
    deploymentDate?: string,
    isModelTimeDimension?: boolean,
    isCustomerDPTenantAndSAPSpace?: boolean,
    isLTF?: boolean
  ): boolean {
    return this.isUploadAvailable(
      editorId,
      canCreateOrUpdateModel,
      hasConsumptionUpdatePrivilege,
      isRemote,
      isLocalSchema,
      isCrossSpace,
      deploymentDate,
      isModelTimeDimension,
      isCustomerDPTenantAndSAPSpace,
      isLTF
    );
  }

  /**
   * Formatter for "run dataflow" button.
   *
   * @param {string} editorId
   * @returns
   * @memberof DataBuilderWorkbench
   */
  public formatterRunDataflowVisible(editorId: string) {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isRunDataflowSupported();
  }

  /**
   * Formatter for "run dataflow" button.
   *
   * @param {string} editorId
   * @returns
   * @memberof DataBuilderWorkbench
   */
  public formatterRunDataFlowEnabled(editorId: string) {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return !activeEditor.disableRunDataflow();
  }

  public formatterRFTransformationVisible(mainEditorId: string, secondaryEditorId: string) {
    const editorId = this.isSecondaryEditorActive && secondaryEditorId ? secondaryEditorId : mainEditorId;
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isAddRFTransformationVisible();
  }

  /**
   * Called when toolbar "run dataflow" is pressed
   *
   * @param {*} event
   * @memberof DataBuilderWorkbench
   */
  public onRunDataflowPressed(event): void {
    const activeComponent = this.getActiveEditor();
    activeComponent.runDataflow(event);
  }

  public textFormatterProjectionLabelRF(projectionExistsInSelectedDataset): string {
    if (projectionExistsInSelectedDataset) {
      return this.getResourceBundle().getText("txtTransformationWithProjections");
    }
    return this.getResourceBundle().getText("txtTransformation");
  }

  /**
   * Formatters transformation enabled
   *
   * @param {*} event
   * @memberof DataBuilderWorkbench
   */
  public formatterRFTransformationEnabled(isRFAddTransformationEnabled): boolean {
    if (isRFAddTransformationEnabled) {
      return true;
    }
    return false;
  }

  /**
   * Called when toolbar "Add Transformation" is pressed
   *
   * @param {*} event
   * @memberof DataBuilderWorkbench
   */
  public onAddTransformationPressed(event): void {
    const activeComponent = this.getActiveEditor();
    if (activeComponent && activeComponent.openRFTransformationDialog) {
      activeComponent.openRFTransformationDialog(event);
    }
  }

  /**
   * default behavior for monitoring navigation button visible or not
   * @param {string} editorId
   * @return {*}  {boolean}
   * @memberof DataBuilderWorkbench
   */
  public formatterNavToMonitoringViewVisible(editorId: string): boolean {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isNavToMonitoringViewVisible();
  }

  /**
   * default behavior for monitoring navigation button enabled or not
   * @param {string} editorId
   * @param {boolean} enabled
   * @return {*}  {boolean}
   * @memberof DataBuilderWorkbench
   */
  public formatterNavToMonitoringViewEnabled(editorId: string, isEnabled: boolean): boolean {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isNavToMonitoringViewEnabled(isEnabled);
  }

  /**
   * called when toolbar "Monitoring" button is pressed
   * @param {*} event
   * @memberof DataBuilderWorkbench
   */
  public onNavToMonitoringViewPressed(event): void {
    const activeComponent = this.getActiveEditor();
    if (activeComponent && activeComponent.navToMonitoringView) {
      activeComponent.navToMonitoringView(event);
    }
  }
  /**
   * called when toolbar "Email" button is pressed
   * @param {*} event
   * @memberof DataBuilderWorkbench
   */
  public onOpenEmailNotificationPressed(event): void {
    const activeComponent = this.getActiveEditor();
    if (activeComponent && activeComponent.openEmailNotificationDialog) {
      activeComponent.openEmailNotificationDialog(event);
    }
  }
  public formatterEmailNotificationEnabled(editorId: string, isEnabled: boolean): boolean {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isEmailNotificationEnabled(isEnabled);
  }
  public formatterEmailNotificationVisible(editorId: string): boolean {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isEmailNotificationVisible();
  }

  /**
   * Tells if upload data is available
   * @param editor the active main editor id
   * @param objectPageId the active object page editor id
   */
  public isUploadAvailable(
    editorId: string,
    canCreateOrUpdateModel: boolean,
    hasConsumptionUpdatePrivilege: boolean,
    isRemote?: boolean,
    isLocalSchema?: boolean,
    isCrossSpace?: boolean,
    deploymentDate?: string,
    isModelTimeDimension?: boolean,
    isCustomerDPTenantAndSAPSpace?: boolean,
    isLTF?: boolean
  ) {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    if (activeEditor.getId() !== "" /* Editor.UNDEFINED*/) {
      return activeEditor.isUploadAvailable(
        canCreateOrUpdateModel,
        hasConsumptionUpdatePrivilege,
        isRemote,
        isLocalSchema,
        isCrossSpace,
        deploymentDate,
        isModelTimeDimension,
        isCustomerDPTenantAndSAPSpace,
        isLTF
      );
    }
    return false;
  }

  /**
   * Tells if delete data button is visible
   *
   * @param {Editor} editorId active main editor id
   * @param {Editor} objectPageId active object page editor id
   * @param {boolean} canCreateOrUpdateModel
   * @param {boolean} [isRemote]
   * @param {boolean} [isLocalSchema]
   * @param {string} [deploymentDate]
   * @param {boolean} [isModelTimeDimension]
   * @param {boolean} [isCustomerDPTenantAndSAPSpace]
   * @returns {boolean}
   * @memberof DataBuilderWorkbench
   */
  public formatterDeleteDataVisible(
    editorId: Editor,
    canCreateOrUpdateModel: boolean,
    hasConsumptionUpdatePrivilege: boolean,
    isRemote?: boolean,
    isLocalSchema?: boolean,
    isCrossSpace?: boolean,
    deploymentDate?: string,
    isModelTimeDimension?: boolean,
    isCustomerDPTenantAndSAPSpace?: boolean,
    isDataIntegrationUpdate?: boolean,
    isDataConsumptionUpdate?: boolean
  ): boolean {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isDeleteDataVisible(
      canCreateOrUpdateModel,
      hasConsumptionUpdatePrivilege,
      isRemote,
      isLocalSchema,
      isCrossSpace,
      deploymentDate,
      isModelTimeDimension,
      isCustomerDPTenantAndSAPSpace,
      isDataIntegrationUpdate,
      isDataConsumptionUpdate
    );
  }

  /**
   * Tells if refresh button should is visible
   *
   * @param {Editor} editorId active main editor id
   * @param {boolean} canCreateOrUpdateModel
   * @param {boolean} [isRemote]
   * @param {boolean} [isLocalSchema]
   * @returns {boolean}
   * @memberof DataBuilderWorkbench
   */
  public formatterRefreshVisible(
    editorId: Editor,
    canCreateOrUpdateModel: boolean,
    isRemote?: boolean,
    isLocalSchema?: boolean,
    isCrossSpace?: boolean,
    location?: string
  ): boolean {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isRefreshVisible(canCreateOrUpdateModel, isRemote, isLocalSchema, isCrossSpace, location);
  }

  /**
   * Tells if refresh button should is visible
   *
   * @param {Editor} editorId active main editor id
   * @param {boolean} [isRemote]
   * @param {boolean} [isLocalSchema]
   * @param {boolean} [isRemoteDetailsLoaded]
   * @memberof DataBuilderWorkbench
   */
  public formatterRefreshEnabled(
    editorId: Editor,
    isRemote?: boolean,
    isLocalSchema?: boolean,
    isRemoteDetailsLoaded?: boolean
  ): boolean {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isRefreshEnabled(isRemote, isLocalSchema, isRemoteDetailsLoaded);
  }

  /**
   * Tells if delete data button is enabled
   *
   * @param {Editor} editorId active main editor id
   * @param {Editor} objectPageId active object page editor id
   * @param {boolean} canCreateOrUpdateModel
   * @param {boolean} [isRemote]
   * @param {boolean} [isLocalSchema]
   * @param {string} [deploymentDate]
   * @returns {boolean}
   * @memberof DataBuilderWorkbench
   */
  public formatterDeleteDataEnabled(
    editorId: Editor,
    isRemote: boolean | undefined,
    isLocalSchema: boolean | undefined,
    deploymentDate: string | undefined
  ): boolean {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isDeleteDataEnabled(isRemote, isLocalSchema, deploymentDate);
  }

  /**
   * return text and tooltip for eraser based on editor ID
   *
   * @returns {string}
   * @memberof DataBuilderWorkbench
   */
  public formatterDeleteDataButtonText(): string {
    if (this.modelId === "-newIDT") {
      return this.ilResourceModel.getResourceBundle().getText("deleteTableData");
    }
    return this.getResourceBundle().getText("deleteTableData");
  }

  /**
   * Tells if edit->AddFromRepo button is visible
   *
   * @param {Editor} mainEditorId active main editor id
   * @param {Editor} secondaryEditorId active secondary id
   * @param {Editor} objectPageId active object page editor id
   * @param {boolean} canCreateOrUpdateMode
   * @returns {boolean}
   * @memberof DataBuilderWorkbench
   */
  public formatterEditAddFromRepositoryVisible(
    mainEditorId: Editor,
    secondaryEditorId: Editor,
    canCreateOrUpdateModel: boolean = false
  ): boolean {
    const editorId = this.isSecondaryEditorActive && secondaryEditorId ? secondaryEditorId : mainEditorId;
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isEditAddFromRepositoryVisible(canCreateOrUpdateModel);
  }

  /**
   * Tells if edit->ImportFromConnection button is enabled
   * @param hanaState {string} Red or Green
   * @returns {boolean}
   * @memberof DataBuilderWorkbench
   */
  public formatterAddFromConnectionEnabled(hanaState: State, spaceStatus: SpaceStatus): boolean {
    return (
      hanaState !== State.Red &&
      // On FF removal of isCircuitBreakerYellowStateEnabled be sure to keep boolean logic consistent
      (hanaState !== State.Yellow || isCircuitBreakerYellowStateEnabled()) &&
      spaceStatus !== SpaceStatus.Locked
    );
  }

  /**
   * Tells if edit->ImportRemote button is visible
   *
   * @param {Editor} editorId active main editor id
   * @returns {boolean}
   * @memberof DataBuilderWorkbench
   */
  public formatterEditImportRemoteVisible(editorId: Editor) {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isEditImportRemoteVisible();
  }

  /**
   * Object status icon formatter
   *
   * @param {ObjectStatus} objectStatus
   * @returns {string}
   * @memberof DataBuilderWorkbench
   */
  public objectStatusIconFormatter(objectStatus: ObjectStatus, isNew: false): string {
    return objectStatusIconFormatter(objectStatus, isNew);
  }

  /**
   * Object status icon color formatter
   *
   * @param {ObjectStatus} objectStatus
   * @returns {string}
   * @memberof DataBuilderWorkbench
   */
  public objectStatusIconColorFormatter(objectStatus: ObjectStatus, isNew: false): string {
    return Format.objectStatusIconColorFormatter(objectStatus, isNew);
  }

  /**
   * Tells if showHierarchy button is visible
   *
   * @param {Editor} editorId active main editor id
   * @param {DataCategory} dataCategory
   * @returns
   * @memberof DataBuilderWorkbench
   */
  public formatterShowHierarchiesButton(editorId: Editor, dataCategory: DataCategory, isLTF?: boolean) {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isShowHierarchiesButtonVisible(dataCategory, isLTF);
  }

  /**
   * Counts the number of hierarchies
   * @param {Editor} editorId active main editor id
   * @param table table
   * @returns
   */
  public formatterShowHierarchiesCount(editorId: Editor, allHierarchies, tableAllHierarchies) {
    const hierarchies = allHierarchies || tableAllHierarchies;
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isShowHierarchiesCount(hierarchies);
  }

  /**
   * Tells if showHierarchy button is enabled
   *
   * @param {Editor} editorId active main editor id
   * @param {DataCategory} dataCategory
   * @returns
   * @memberof DataBuilderWorkbench
   */
  public formatterEnableHierarchiesButton(editorId: Editor, dataCategory: DataCategory) {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isEnableHierarchiesButton(dataCategory);
  }

  /**
   * Tells if editCSN button is visible
   *
   * @param {Editor} editorId active main editor id
   * @returns
   * @memberof DataBuilderWorkbench
   */
  public formatterShowEditCSNButton(editorId: Editor) {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    const activeEditorId = activeEditor?.getId();
    return (
      activeEditorId === Editor.VIEWBUILDER ||
      activeEditorId === Editor.SQLBUILDER ||
      activeEditorId === Editor.TABLEEDITOR
    );
  }

  /**
   * Tells if editCSN button is enabled
   *
   * @param {Editor} editorId active main editor id
   * @returns
   * @memberof DataBuilderWorkbench
   */
  public formatterEnableEditCSNButton(editorId: Editor) {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    const activeEditorId = activeEditor?.getId();
    return (
      activeEditorId === Editor.VIEWBUILDER ||
      activeEditorId === Editor.SQLBUILDER ||
      activeEditorId === Editor.TABLEEDITOR
    );
  }

  /**
   * Called when share button is pressed
   *
   * @param {IEvent<sap.m.Button, {}>} event
   * @memberof DataBuilderWorkbench
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  public onShareEntity(event: IEvent<sap.m.Button, {}>): void {
    const activeEditor = this.getActiveEditor();

    const sharingObjects: ISharingObjectList = {};
    sharingObjects[this.modelId] = {
      description: this.modelName,
      typeName: activeEditor.getModelTitle(),
      sharedWith: [],
    };
    const data = {
      spaceName: this.spaceId,
      sharingObjects,
    };
    sap.cdw.databuilder.ShareObjectsCommand.execute({
      controller: this,
      data,
    });
  }

  /**
   * Called when parameters button is pressed
   *
   * @param {IEvent<sap.m.Button, {}>} event
   * @memberof DataBuilderWorkbench
   */
  public onHandleParameters(event: IEvent<sap.m.Button, {}>): void {
    const activeEditor = this.getActiveEditor();
    activeEditor.onHandleParameters(event);
  }

  /**
   * Called when add from repository button is pressed
   *
   * @param {IEvent<sap.m.Button, {}>} event
   * @memberof DataBuilderWorkbench
   */
  public onAddFromRepository(event: IEvent<sap.m.Button, {}>): void {
    const self = this;
    const activeEditor = this.getActiveEditor(true);
    if (activeEditor.supportAddFromRepository()) {
      // Record usage
      ShellContainer.get()
        .getUsageCollectionService()
        .recordAction({
          action: DataBuilderAction.AddFromRepository,
          feature: self.getComponent().getFeature(),
          eventtype: EventType.CLICK,
          options: [
            {
              param: "target",
              value: this.activeEditor.getId(),
            },
          ],
        });
      activeEditor.onAddFromRepository(event);
    }
  }

  /**
   * Tells if AI menu button is enabled
   *
   * @param {Editor} editorId active main editor id
   * @param {boolean} canUseAI
   * @returns {boolean}
   * @memberof DataBuilderWorkbench
   */
  public formatterAIMenuButtonEnabled(editorId: Editor, canUseAI: boolean = false): boolean {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isAIMenuButtonVisible() && canUseAI !== false;
  }

  /**
   * Tells if AI menu button is visible
   *
   * @param {Editor} editorId active main editor id
   * @param {boolean} canUseAI
   * @returns {boolean}
   * @memberof DataBuilderWorkbench
   */
  public formatterAIMenuButtonVisible(editorId: Editor, canUseAI: boolean = false): boolean {
    // if (canUseAI === false) {
    //   return false;
    // }
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isAIMenuButtonVisible();
  }

  /**
   * Tells if Share button is visible
   *
   * @param {Editor} editorId active main editor id
   * @param {boolean} isNew it is a new model
   * @returns {boolean}
   * @memberof DataBuilderWorkbench
   */
  public formatterShareEntityVisible(editorId: Editor, isNew?: boolean, canShareModel: boolean = false): boolean {
    if (isNew === true) {
      return false;
    }
    if (canShareModel === false) {
      return false;
    }
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isShareEntityVisible();
  }

  /**
   * Tells if Share button is enabled
   *
   * @param {Editor} editorId active main editor id
   * @param {boolean} isNew it is a new model
   * @returns {boolean}
   * @memberof DataBuilderWorkbench
   */
  public formatterShareEntityEnabled(editorId: Editor, isNew?: boolean, canShareModel: boolean = false): boolean {
    if (isNew === true) {
      return false;
    }
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isShareEntityVisible() && canShareModel !== false;
  }

  /**
   * Tells if parameters button is visible
   *
   * @param {Editor} editorId active main editor id
   * @param {Editor} objectPageId active object page editor id
   * @returns {boolean}
   * @memberof DataBuilderWorkbench
   */
  public formatterParametersVisible(editorId: Editor): boolean {
    const activeEditor = this.getEditor(editorId) as AbstractDataBuilderEditorComponentClass;
    return activeEditor.isParametersVisible();
  }

  /**
   * Gets CSN entity from document
   * @param oCsnDocument CSN document
   * @param sEntityName the entity name
   */
  public getCsnEntityFromDocument(oCsnDocument, sEntityName) {
    let oCsnEntity;
    if (oCsnDocument && oCsnDocument.definitions) {
      oCsnEntity = sEntityName && oCsnDocument.definitions[sEntityName];
      if (!oCsnEntity) {
        sEntityName = getEntityNameFromCSN(oCsnDocument.definitions);
        oCsnEntity = sEntityName && oCsnDocument.definitions[sEntityName];
      }
    }
    return oCsnEntity;
  }

  /**
   * @override
   */
  public objectStatusTextFormatter(objectStatus, isNew: false) {
    return objectStatusTextFormatter(objectStatus, isNew, this.erResourceModel.getResourceBundle());
  }

  /**
   * @override
   */
  public objectStatusTooltipFormatter(objectStatus, isNew: false) {
    return objectStatusTooltipFormatter(objectStatus, isNew, this.erResourceModel.getResourceBundle());
  }

  public isNewModelName(curContextModel: string): boolean {
    return curContextModel === "" || (Object.values(NewModelTypes) as string[]).includes(curContextModel);
  }

  public getEditorIdFromNewModelName(newModelName: string): string {
    switch (newModelName) {
      case NewModelTypes.ERMODEL:
        return Editor.ERMODELER;
      case NewModelTypes.GRAPHICALVIEW:
        return Editor.VIEWBUILDER;
      case NewModelTypes.SQLVIEW:
        return Editor.SQLBUILDER;
      case NewModelTypes.TABLE:
        return Editor.TABLEEDITOR;
      case NewModelTypes.DATAFLOW:
        return Editor.DATAFLOWMODELER;
      case NewModelTypes.TRANSFORMATIONFLOW:
        return Editor.TRANSFORMATIONFLOW;
      case NewModelTypes.INTELLIGENTLOOKUP:
        return Editor.INTELLIGENTLOOKUP;
      case NewModelTypes.QUERYMODEL:
        return Editor.QUERYMODEL;
      case NewModelTypes.TASKCHAIN:
        return Editor.TASKCHAINMODELER;
      case NewModelTypes.REPLICATIONFLOW:
        return Editor.REPLICATIONFLOWBUILDER;
      case NewModelTypes.DATAACCESSCONTROL:
        return Editor.DATAACCESSCONTROL;
      default:
        return Editor.UNDEFINED;
    }
  }

  public getEditorIdFromDetails(modelDetails: any): IEditorDetails {
    let editorId: string;
    let details: any;
    if (modelDetails) {
      const oCSN = modelDetails.csn;
      let uiModel;

      if (oCSN && oCSN.editorSettings && oCSN.editorSettings[modelDetails.name]?.editor) {
        // check first if editor id is in "editorSettings" section, see Jira #DW13-2572
        if (oCSN.editorSettings[modelDetails.name].editor.default) {
          return {
            editorId: oCSN.editorSettings[modelDetails.name].editor.default,
            details: {
              csn: oCSN,
              file: modelDetails,
            },
          };
        }
        uiModel =
          oCSN.editorSettings[modelDetails.name].editor.uiModel || oCSN.editorSettings[modelDetails.name].uiModel;
      }

      const dataCategoryType = BusinessCatalogUtility.getDataCategory(modelDetails);
      if (dataCategoryType === BusinessCatalogUtility.Type.ERModel) {
        details = {
          csn: oCSN,
          file: modelDetails,
        };
        editorId = Editor.ERMODELER;
      } else if (dataCategoryType === BusinessCatalogUtility.Type.DAC) {
        details = {
          json: oCSN,
          file: modelDetails,
        };
        editorId = Editor.DATAACCESSCONTROL;
      } else if (dataCategoryType === BusinessCatalogUtility.Type.DataFlow) {
        details = {
          json: oCSN,
          file: modelDetails,
        };
        editorId = Editor.DATAFLOWMODELER;
      } else if (dataCategoryType === BusinessCatalogUtility.Type.ReplicationFlow) {
        details = {
          json: oCSN,
          file: modelDetails,
        };
        editorId = Editor.REPLICATIONFLOWBUILDER;
      } else if (dataCategoryType === BusinessCatalogUtility.Type.TransformationFlow) {
        details = {
          json: oCSN,
          file: modelDetails,
        };
        editorId = Editor.TRANSFORMATIONFLOW;
      } else if (dataCategoryType === BusinessCatalogUtility.Type.IntelligentLookup) {
        details = {
          json: oCSN,
          file: modelDetails,
        };
        editorId = Editor.INTELLIGENTLOOKUP;
      } else if (dataCategoryType === BusinessCatalogUtility.Type.TaskChain) {
        details = {
          json: oCSN,
          file: modelDetails,
        };
        editorId = Editor.TASKCHAINMODELER;
      } else if (dataCategoryType === BusinessCatalogUtility.Type.Cube) {
        details = {
          json: oCSN,
          file: modelDetails,
        };
        editorId = Editor.QUERYMODEL;
      } else if (BusinessCatalogUtility.isViewType(dataCategoryType)) {
        // Open Graphical View or SQL View
        const iaQueryCsn = BusinessCatalogUtility.getDefinition(modelDetails);
        const featureFlags = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();
        const isSaveWithErrorEnabled = featureFlags.DWCO_SAVE_WITH_ERROR_SQL_VIEW;
        if (
          (isSaveWithErrorEnabled ? iaQueryCsn : iaQueryCsn && iaQueryCsn.query) ||
          (iaQueryCsn &&
            iaQueryCsn["@Analytics.dbViewType"] &&
            iaQueryCsn["@Analytics.dbViewType"] === DBViewType.TABLEFUNCTION)
        ) {
          const isSql: boolean = !uiModel && this.isSqlEntity(iaQueryCsn);

          details = {
            query: iaQueryCsn,
            file: modelDetails,
          };

          if (isSql) {
            editorId = Editor.SQLBUILDER;
          } else {
            editorId = Editor.VIEWBUILDER;
          }
        } else {
          // If not a Graphical View or SQL View, open as Table
          details = {
            csn: oCSN,
            file: modelDetails,
          };
          // Open Table Editor
          editorId = Editor.TABLEEDITOR;
        }
      } else if (BusinessCatalogUtility.isEntityType(dataCategoryType)) {
        details = {
          csn: oCSN,
          file: modelDetails,
        };
        // Open Table Editor
        editorId = Editor.TABLEEDITOR;
      }
    } else {
      // TODO
    }
    return {
      editorId: editorId,
      details: details,
    };
  }

  public presetContextObject(): void {
    const ownerComponent = this.getOwnerComponent() as DataBuilderWorkbenchComponentClass;
    let activeSection = Section.DATAVIEW;
    if (
      ownerComponent.isProcessingSaveDeploy() &&
      this.getActiveEditor().supportAssociationSection() &&
      this.workbenchModel.getProperty("/activeSection") === Section.ASSOICATIONVIEW
    ) {
      activeSection = Section.ASSOICATIONVIEW;
    }
    this.workbenchModel.setProperty("/activeSection", activeSection);
  }

  public formatNewModelName(tableName: string): string {
    let tableString: string = tableName;
    if ((Object.values(NewModelTypes) as string[]).includes(tableName)) {
      // Replace preceding "-" which come from the NewModelTypes enum
      tableString = this.getText(tableName.replace(/^\-/, ""));
    }
    return tableString;
  }

  /**
   * @override
   */
  public getERResourceModel(): sap.ui.model.resource.ResourceModel {
    return this.erResourceModel;
  }

  /**
   * Creates an undefined editor, corresponds when no active editor is selected yet
   * Needs to be overriden if databuilder editor provide custom formatters...
   */
  public createUndefinedEditor(): any {
    return new NullEditorComponent();
  }

  /**
   * @override
   */
  public getActiveEditor(includeSecondaryEditor?: boolean): AbstractDataBuilderEditorComponentClass {
    return super.getActiveEditor(includeSecondaryEditor) as AbstractDataBuilderEditorComponentClass;
  }

  /**
   * @override
   */
  public async updateDataPreview(oObject: any, oSymbol?): Promise<boolean> {
    const self = this;
    const oModel = oObject.resource.model;
    const sEntityName = oObject.alias || oObject.shortName || oObject.technicalName || oObject.name;

    if (oObject.classDefinition.name === "Element" || oObject.classDefinition.name === "Association") {
      return false;
    }

    if (!this.canPreviewCSNData()) {
      const notDeployed =
        (oObject.isTable || oObject.isView || oObject.isEntity) &&
        oObject !== (oObject.container && oObject.container.output)
          ? !(oObject.deploymentStatus === DeploymentStatus.Active || oObject.deploymentDate)
          : undefined;
      this.previewDataForObject(oObject, undefined, notDeployed);
      return true;
    }
    // Loads metadata.js
    if (!sap.cdw.querybuilder.ViewModelToCsn.metadata) {
      sap.cdw.querybuilder.ViewModelToCsn.metadata = require("../../../services/metadata");
    }
    sap.cdw.querybuilder.ViewModelToCsn.getCSN(sEntityName, oModel, {
      rootObject: oObject,
      dataPreview: true,
    }).then(
      function (oCsn) {
        if (oCsn && oCsn.definitions) {
          // TODO: This may not be correct
          const aEntities = oModel.resource.selectAllObjects({ "classDefinition.name": "Entity" });
          aEntities.forEach(function (oEntity) {
            if (oEntity.csn && !oCsn.definitions[oEntity.name]) {
              const cloneCsn = cloneJson(oEntity.csn);
              handleCsnForDataPreview(cloneCsn);
              oCsn.definitions[oEntity.name] = cloneCsn;
            }
          });
          const notDeployed =
            (oObject.isTable || oObject.isView || oObject.isEntity) &&
            oObject !== (oObject.container && oObject.container.output)
              ? !(oObject.deploymentStatus === DeploymentStatus.Active || oObject.deploymentDate)
              : undefined;
          // check ouput view node is persisted or not
          const isOutputPersisted = isOutputNodePersisted(oObject, oModel);
          if (isOutputPersisted) {
            if (self.isDirty() || (oModel && oModel.deploymentStatus === DeploymentStatus.Revised)) {
              MessageHandler.success(self.getText("InfodataPreviewForPersitedView"));
            } else {
              oCsn = handlePersistedViewCsnForDataPreview(oCsn, oObject);
            }
          }
          self.previewDataForObject(
            oObject,
            oCsn,
            notDeployed,
            [oObject].concat(sap.cdw.querybuilder.NodeImpl.computeNodePredecessorsRecursive(oObject)),
            oSymbol
          );
          return true;
        }
      },
      function (oError) {
        sap.m.MessageToast.show(oError.error);
      }
    );
    return false;
  }

  /**
   * @override
   */
  public openValidationPopover(i18nResources): void {
    openValidationPopover(i18nResources);
  }

  public showIPPopUPForDataPreview(galileiModel, consumedParams, oCsn?, isEREditor?, oCallback?, cancelCallback?) {
    const minVisibleRowCount = consumedParams.length;
    return new Promise((resolve) => {
      showDialog(
        require("../../databuilder/view/InputParameterDataPreview.view.xml"),
        this.getText("ip_inputParameters"),
        this.getText("ok"),
        this.getText("cancel"),
        {
          currentNode: galileiModel,
          parameters: consumedParams,
          minVisibleRowCount: minVisibleRowCount,
        },
        "35%",
        "auto",
        null,
        null,
        null,
        (dialog: sap.m.Dialog) => {
          const ipView = dialog.getContent()[0] as sap.ui.core.mvc.View;
          const okButton = ipView?.byId("ok") as sap.m.Button;
          const inputModel = ipView?.getModel("viewData") as sap.ui.model.json.JSONModel;
          // Function to check if all inputs have values
          const onInputChange = () => {
            let enableButton = true;
            const parameters = inputModel?.getData()?.parameters || [];
            if (parameters?.length > 0) {
              for (const param of parameters) {
                if (param?.defaultForDataPreview === "" || param?.defaultForDataPreview === undefined) {
                  enableButton &&= false;
                  okButton.setEnabled(enableButton);
                } else {
                  enableButton &&= true;
                  okButton.setEnabled(enableButton);
                }
              }
            } else {
              okButton.setEnabled(false);
            }
          };
          const inputs = ipView.findAggregatedObjects(true, (obj) => obj instanceof sap.m.Input) as sap.m.Input[];

          inputs.forEach((input) => {
            input.attachLiveChange(onInputChange);
          });

          // Initial check when dialog opens
          onInputChange();
        }
      )
        .then(async (result: any) => {
          const featureFlags = (sap.ui.getCore().getModel("featureflags") as sap.ui.model.json.JSONModel).getData();
          if (oCallback) {
            oCallback(result);
            return;
          }
          let output;
          if (galileiModel?._isSqlEditorModel) {
            output = galileiModel.output;
          } else if (galileiModel?.isTable || galileiModel.isView) {
            output = galileiModel;
          } else {
            output = this.activeEditor.getEditorControl().oController.getDiagramEditor().diagram.model.output;
          }
          this.updateCSNWithIPValues(
            result.viewData.getData().currentNode,
            result.viewData.getData().parameters,
            output,
            undefined,
            oCsn,
            isEREditor
          );

          // update parameters in UI model with new values from result.viewData.getData().parameters
          updateParamDefaults(
            isEREditor,
            result.viewData.getData().parameters,
            this.workbenchModel,
            galileiModel.qualifiedName
          );
        })
        .catch((err) => {
          console.log(err);
          if (cancelCallback) {
            cancelCallback(err);
          }
        });
    });
  }

  public async updateCSNWithIPValues(
    currentNode: any,
    parameters: any,
    outputNode: any,
    dataToBeFetched: any = true,
    oCsn?: any,
    isEREditor?: any
  ) {
    const activeComponent = this.getActiveEditor(true);
    let currentNodeName;
    let isTableFunction = false;
    if (currentNode?._isSqlEditorModel && currentNode.output?.dbViewType === DBViewType.TABLEFUNCTION) {
      isTableFunction = true;
    }
    if (
      currentNode.classDefinition.name === "Output" ||
      (currentNode.classDefinition.name === "Entity" &&
        (currentNode.isView || currentNode.repositoryCSN || currentNode.isTable)) ||
      currentNode.classDefinition.name === "Table" ||
      currentNode.classDefinition.name === "View"
    ) {
      currentNodeName = NamingHelper.encodeDataPreviewName(currentNode.technicalName);
    } else if (isTableFunction || (currentNode?._isSqlEditorModel && currentNode.isNew && dataToBeFetched)) {
      currentNodeName = NamingHelper.encodeDataPreviewName(currentNode.name);
    } else {
      currentNodeName = currentNode.name;
    }
    let document;
    if (isEREditor) {
      if (currentNode?.parameters?.length > 0) {
        document = await sap.cdw.querybuilder.ViewModelToCsn.getCSN(currentNode.name, currentNode, {
          rootObject: currentNode,
          dataPreview: true,
          consumedParams: parameters,
        });
        if (isEREditor) {
          parameters.forEach((element) => {
            element.objectName = currentNode.qualifiedName;
          });
        }
        const def = document.definitions[currentNodeName];
        const args = def.query.SELECT.from.ref[0].args;
        if (args) {
          parameters.forEach((element) => {
            args[element.name] = {
              val: element.defaultForDataPreview,
            };
          });
        }
      } else {
        document = {
          definitions: {
            [currentNodeName]: oCsn,
          },
        };
      }
    } else if (currentNode?._isSqlEditorModel) {
      if (isTableFunction) {
        // FIXME NOT GOOD! Databuilder should only handle generic parts!
        document = await prepareCSNforSQLScriptDataPreview(currentNode.output, currentNode.name, this.spaceName);
      } else {
        document = (
          await (activeComponent as any).getDocument(currentNodeName, currentNode, { dataPreview: dataToBeFetched })
        ).csn;
      }
    } else if (currentNode.dbViewType === DBViewType.TABLEFUNCTION) {
      // FIXME NOT GOOD! Databuilder should only handle generic parts!
      document = await prepareCSNforSQLScriptDataPreview(currentNode, currentNode.name, this.spaceName);
    } else if (
      currentNode.isTable &&
      (currentNode.isRemote || currentNode.isLocalSchema) &&
      currentNode?.parameters?.length > 0
    ) {
      document = await sap.cdw.querybuilder.ViewModelToCsn.getCSN(currentNode.name, currentNode, {
        rootObject: currentNode,
        dataPreview: true,
      });
    } else {
      document = await (activeComponent as any).getCSN(currentNodeName, currentNode.resource?.model, {
        rootObject: currentNode,
        dataPreview: true,
      });
      if (currentNode.isCrossSpace) {
        currentNodeName = getEntityNameFromCSN(document.definitions);
      }
    }
    const nonDefaultParams = [];
    if (parameters.length > 0 && !dataToBeFetched) {
      parameters.forEach((element) => {
        if (!element.defaultForDataPreview) {
          fillParamterDefaultValue(element);
          nonDefaultParams.push(element);
        }
      });
    }
    const query = document.definitions[currentNodeName]?.query;
    const paramValues = {};
    let queryString = query ? JSON.stringify(query) : "";
    if (parameters.length > 0) {
      this.workbenchModel.setProperty("/isIPSelected", true);
      let ribbonContent = "Input Parameters: ";
      const items = [];
      parameters.forEach((element) => {
        if (
          nonDefaultParams.includes(element) &&
          [CDSDataType.DATE, CDSDataType.DATETIME, CDSDataType.TIME, CDSDataType.TIMESTAMP].includes(
            element.primitiveDataType
          )
        ) {
          queryString = queryString
            .split('{"ref":[' + JSON.stringify(element.name) + '],"param":true}')
            .join(JSON.stringify(element.defaultForDataPreview));
          queryString = queryString
            .split('"ref":[' + JSON.stringify(element.name) + '],"param":true')
            .join(JSON.stringify(element.defaultForDataPreview));
          element.defaultForDataPreview = "";
        } else {
          queryString = updateIPQueryString(element, queryString);
        }
        items.push(element.displayName + "( " + element.defaultForDataPreview + " )");
        paramValues[element.name] = element.defaultForDataPreview;
      });
      ribbonContent += items.join(", ");
      if (nonDefaultParams.length > 0 && !dataToBeFetched) {
        nonDefaultParams.forEach((element) => {
          element.defaultForDataPreview = "";
        });
      }

      /* All the mapped parameters should have some value for CDS compiler to work fine (CDS compiler limitation),
       hence setting all other parameters to a dummy value "now" */
      if (outputNode?.parameters && !isTableFunction) {
        outputNode.parameters.forEach((element) => {
          queryString = queryString.split('{"ref":["' + element.name + '"],"param":true}').join('{"val":"now"}');
        });
      }

      /* For an output node,
      csn should not have any params (CDS compiler limitation).
      As the params values are already replaced with the values user has selected,
      hence removing the params from csn for the selected node */
      if (document?.definitions) {
        handleCsnForDataPreview(document.definitions[currentNodeName]);
        delete document.definitions[currentNodeName].params;
        document.definitions[currentNodeName].query = JSON.parse(queryString);
        // Do not send mixin for any data preview
        if (document.definitions[currentNodeName].query?.SELECT?.mixin) {
          handleRemoveMixinForDataPreview(document.definitions[currentNodeName]);
        }
      }

      this.workbenchModel.setProperty("/ribbonContent", ribbonContent);

      if (isEREditor) {
        // update parameters in UI model with new values from current parameters
        updateParamDefaults(isEREditor, parameters, this.workbenchModel, currentNode.qualifiedName);
      } else {
        this.workbenchModel.setProperty("/paramDefaults", parameters);
      }

      await this.previewData(
        currentNodeName,
        document,
        this.getSpaceName(),
        currentNode,
        undefined,
        dataToBeFetched,
        paramValues
      );
      if (parameters.length > 0 && !dataToBeFetched) {
        this.workbenchModel.setProperty("/paramDefaults", "");
      }
    }
  }

  /**
   * @override
   */
  protected getCurrentActiveEditor(
    objectpageMode: boolean = false,
    activeMainEditorId?: string,
    activeObjectPageEditorId?: string
  ): AbstractDataBuilderEditorComponentClass {
    return super.getCurrentActiveEditor(
      objectpageMode,
      activeMainEditorId,
      activeObjectPageEditorId
    ) as AbstractDataBuilderEditorComponentClass;
  }

  protected setupToolAdapter(): void {
    super.setupToolAdapter();
    DataBuilderToolAdapter.getInstance().setActiveEditorInfoHandler(this.getCurrentEditorInfo.bind(this));
  }

  // PRIVATES

  private createERModelerResourceModel() {
    const erBundleName = require("../../ermodeler/i18n/i18n.properties");
    this.erResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: erBundleName,
      enhanceWith: [this.commonResourceModel.getResourceBundle()],
    });
  }

  private createGVResourceModel() {
    const gvBundleName = require("../../csnquerybuilder/i18n/i18n.properties");
    this.gvResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: gvBundleName,
      enhanceWith: [this.commonResourceModel.getResourceBundle()],
    });
  }

  private createDFResourceModel() {
    const dfBundleName = require("../../dataflowmodeler/i18n/i18n.properties");
    this.dfResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: dfBundleName,
      enhanceWith: [this.commonResourceModel.getResourceBundle()],
    });
  }

  private createTFResourceModel() {
    const tfBundleName = require("../../transformationflow/i18n/i18n.properties");
    this.tfResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: tfBundleName,
      enhanceWith: [this.commonResourceModel.getResourceBundle()],
    });
  }

  private createRFResourceModel() {
    const rfBundleName = require("../../replicationflow/i18n/i18n.properties");
    this.rfResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: rfBundleName,
      enhanceWith: [this.commonResourceModel.getResourceBundle()],
    });
  }

  private createILResourceModel() {
    const ilBundleName = require("../../intelligentlookup/i18n/i18n.properties");
    this.ilResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: ilBundleName,
      enhanceWith: [this.commonResourceModel.getResourceBundle()],
    });
  }

  private createCommonResourceModel() {
    this.commonResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../../commonmodel/i18n/i18n.properties"),
    });
  }

  private createTCResourceModel() {
    this.tcResourceModel = new sap.ui.model.resource.ResourceModel({
      bundleName: require("../../taskchainmodeler/i18n/i18n.properties"),
    });
  }

  private createRTMResourceModel() {
    const self = this;
    loadResourceBundle("sap.cdw.components.remotetablemonitor.i18n.i18n", false).then((bundle) => {
      self.rtmResourceModel = new sap.ui.model.resource.ResourceModel({
        bundle: bundle,
      });
    });
  }

  private createVMResourceModel() {
    const self = this;
    loadResourceBundle("sap.cdw.components.viewmonitor.i18n.i18n", false).then((bundle) => {
      self.vmResourceModel = new sap.ui.model.resource.ResourceModel({
        bundle: bundle,
      });
    });
  }

  private getDataAndAssociationSections(): ISectionItem[] {
    return [
      {
        text: "{i18n>dataView}",
        key: "dataView",
        width: "140px",
      },
      {
        text: "{i18n>associationView}",
        key: "associationView",
        width: "140px",
      },
    ];
  }

  /**
   * Determine if a CSN entity is a SQL View
   * @param {Object} oCsnEntity
   */
  private isSqlEntity(oCsnEntity) {
    if (oCsnEntity) {
      if (
        oCsnEntity.hasOwnProperty("@DataWarehouse.sqlEditor.query") ||
        !oCsnEntity["@DataWarehouse.querybuilder.model"]
      ) {
        return true;
      }
    }
    return false;
  }

  /**
   * Manually clears cache if needed. Currently, only does this for DAC.
   * @param galileiModel
   */
  public clearImpactedObjectsCache(galileiModel: any): void {
    try {
      const impactedDependencies: IObjectDependency[] = this.getAllImpactedDependencies(galileiModel);
      // If more manual cache invalidations are needed, should create and call methods like clearImpactedDacsCache
      this.clearImpactedDacsCache(impactedDependencies);
    } catch (_e) {
      /* Just don't throw */
    }
  }

  private clearImpactedDacsCache(dependencies: IObjectDependency[]): void {
    dependencies.forEach((dep) => {
      if (dep.kind === ObjectKind.dac) {
        Crud.get().clearCache([
          { type: SpaceType, name: this.spaceId },
          { type: DACType, name: dep.name },
        ]);
        Crud.get().clearCache([
          { type: SpaceType, name: this.spaceId },
          { type: ModelType, name: dep.name },
        ]);
      }
    });
  }

  private getAllImpactedDependencies(galileiModel: any): IObjectDependency[] {
    const allImpactedDependencies = new Map<string, IObjectDependency>();
    galileiModel.entities.toArray().forEach((entity: sap.cdw.commonmodel.Entity) => {
      const impact: IEntityImpact = entity.impact;
      impact?.repoElements.forEach((repoEl: IRepoElement) => {
        repoEl.impactedDependencies.forEach((objDep) => {
          allImpactedDependencies.set(objDep.id, objDep);
        });
      });
    });
    return [...allImpactedDependencies.values()];
  }
}

export const DataBuilderWorkbenchControllerClass = smartExtend(
  AbstractWorkbenchClass,
  "sap.cdw.components.databuilder.controller.DataBuilderWorkbench",
  DataBuilderWorkbench
);

sap.ui.define("sap/cdw/components/workbench/controller/DataBuilderWorkbench.controller", [], function () {
  return DataBuilderWorkbenchControllerClass;
});
