cpuUtilHighUp: &cpuUtilHighUp
  metric_type: cpuutil
  threshold: 90
  operator: ">"
  adjustment: "+1"
  cool_down_secs: 300
  breach_duration_secs: 120 # 2 minutes

cpuUtilUp: &cpuUtilUp
  metric_type: cpuutil
  threshold: 40
  operator: ">"
  adjustment: "+1"
  cool_down_secs: 300
  breach_duration_secs: 600 # 10 minutes

cpuUtilDown: &cpuUtilDown
  metric_type: cpuutil
  threshold: 15
  operator: "<"
  adjustment: "-1"
  cool_down_secs: 300
  breach_duration_secs: 1800 # 30 minutes

throughputHighUp: &throughputHighUp
  metric_type: throughput
  threshold: 3
  operator: ">"
  adjustment: "+1"
  cool_down_secs: 300
  breach_duration_secs: 120 # 2 minutes

throughputUp: &throughputUp
  metric_type: throughput
  threshold: 2
  operator: ">"
  adjustment: "+1"
  cool_down_secs: 300
  breach_duration_secs: 600 # 10 minutes

throughputDown: &throughputDown
  metric_type: throughput
  threshold: 1
  operator: "<"
  adjustment: "-1"
  cool_down_secs: 300
  breach_duration_secs: 600 # 10 minutes

# Define the scaling rules for the canary environment
canary: &canary
  instance_min_count: 6
  instance_max_count: 6
  scaling_rules:
    - <<: *cpuUtilHighUp
    - <<: *cpuUtilUp
    - <<: *cpuUtilDown

# Define the scaling rules for the rel environment
relCanary: &relCanary #Min & Max are the same, no scaling allowed
  instance_min_count: 6
  instance_max_count: 6
  scaling_rules:
    - <<: *cpuUtilHighUp
    - <<: *throughputHighUp
      threshold: 4
    - <<: *cpuUtilUp
    - <<: *throughputUp
      threshold: 3
    - <<: *throughputDown
      threshold: 2
    - <<: *cpuUtilDown

# Define the scaling rules for the xsmall production environments - Planned Min: 3, Max: 6
xsmall: &xsmall #Min & Max are the same, no scaling allowed
  instance_min_count: 12
  instance_max_count: 12
  scaling_rules:
    - <<: *cpuUtilHighUp
    - <<: *throughputHighUp
    - <<: *cpuUtilUp
    - <<: *throughputUp
    - <<: *throughputDown
    - <<: *cpuUtilDown

# Define the scaling rules for the small production environments - Planned Min: 4, Max: 8
small: &small #Min & Max are the same, no scaling allowed
  instance_min_count: 12
  instance_max_count: 12
  scaling_rules:
    - <<: *cpuUtilHighUp
    - <<: *throughputHighUp
      threshold: 4
    - <<: *cpuUtilUp
    - <<: *throughputUp
      threshold: 3
    - <<: *throughputDown
      threshold: 2
    - <<: *cpuUtilDown

# Define the scaling rules for the small production environments - Planned Min: 10, Max: 14
medium: &medium #Min & Max are the same, no scaling allowed
  instance_min_count: 12
  instance_max_count: 12
  scaling_rules:
    - <<: *cpuUtilHighUp
    - <<: *throughputHighUp
      threshold: 5
    - <<: *cpuUtilUp
    - <<: *throughputUp
      threshold: 4
    - <<: *throughputDown
      threshold: 3
    - <<: *cpuUtilDown

# Define the scaling rules for the small production environments - Planned Min: 17, Max: 24
large: &large #Min & Max are the same, no scaling allowed
  instance_min_count: 24
  instance_max_count: 24
  scaling_rules:
    - <<: *cpuUtilHighUp
    - <<: *throughputHighUp
      threshold: 6
    - <<: *cpuUtilUp
    - <<: *throughputUp
      threshold: 5
    - <<: *throughputDown
      threshold: 4
    - <<: *cpuUtilDown

master:
  <<: *canary

rel:
  <<: *relCanary

# Apply autoscaler configuration to all cloud providers - for the moment autoscaling is disabled as min and max are the same
# This ensures that the autoscaler will not be triggered
aws:
  xsmall:
    <<: *xsmall
  large:
    <<: *large

gcp:
  xsmall:
    <<: *xsmall
  large:
    <<: *large

azure:
  xsmall:
    <<: *xsmall
  large:
    <<: *large

alicloud:
  xsmall:
    <<: *xsmall
  large:
    <<: *large
