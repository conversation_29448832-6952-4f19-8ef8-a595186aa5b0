/** @format */

var fs = require("fs-extra");
var net = require("net");
var path = require("path");
var exec = require("./exec.js");
var execCF = exec.cf;
var execNpm = exec.npm;
var cf = require("./cf.js");

var { api, org, space, appname, appnameGlobal } = require("./endpoint.js");

var lastPortNumber = 16000;
var totalMap = {};
// can be overridden by process.env.XSUAA_CREDENTIAL_TYPE
const xsuaaDefaultCredentialType = "x509";
if (!process.env.XSUAA_CREDENTIAL_TYPE) {
  process.env.XSUAA_CREDENTIAL_TYPE = xsuaaDefaultCredentialType;
}
// service keys with long lived xsuaa credentials
let xsuaaServiceKeyName = undefined;
if (process.env.XSUAA_CREDENTIAL_TYPE === "basic") {
  xsuaaServiceKeyName = "local-dev-basic-auth";
} else if (process.env.XSUAA_CREDENTIAL_TYPE === "x509") {
  xsuaaServiceKeyName = "local-dev-x509-auth";
}
const certificateServiceKeyName = "dwaas-core-dev";
const credstoreServiceKeyName = "dwaas-core-local-dev";

var createEnvFormat = function (env) {
  var skipKeys = ["xsuaa", "credstore", "redis-cache", "message-queuing"];
  var hostKeys = ["host", "hostname", "hosts", "hostnames"];
  var portKeys = ["port", "ports"];
  var uriKeys = ["uri", "uris", "url", "urls"];

  var walkParameters = function (obj, hostCb, portCb, uriCb, path) {
    var keys = Object.keys(obj || {});
    path = path || [];
    for (var i = 0; i < keys.length; i++) {
      var curKey = keys[i];
      if (skipKeys.indexOf(curKey) > -1 && path.length === 0) {
        continue;
      }
      var curVal = obj[curKey];
      var curPath = [].concat(path, [curKey]);

      if (hostKeys.indexOf(curKey) > -1) {
        hostCb(curPath, curVal);
      }

      if (portKeys.indexOf(curKey) > -1) {
        portCb(curPath, curVal);
      }

      if (uriKeys.indexOf(curKey) > -1) {
        uriCb(curPath, curVal);
      }

      if (typeof curVal === "object") {
        walkParameters(curVal, hostCb, portCb, uriCb, curPath);
      }
    }
  };

  var collectAndMapHosts = function (obj) {
    var localHosts = [];
    var localPorts = [];

    var addHost = function (path, host) {
      localHosts.push({ path: path, host: host });
    };

    var addPort = function (path, port) {
      localPorts.push({ path: path, port: port });
    };

    var hostCb = function (path, curVal) {
      if (Array.isArray(curVal)) {
        curVal.forEach(addHost.bind(this, path));
      }
      if (typeof curVal === "string") {
        addHost(path, curVal);
      }
    };

    var portCb = function (path, curVal) {
      if (Array.isArray(curVal)) {
        curVal.forEach(addPort.bind(this, path));
      }
      if (typeof curVal === "string" || typeof curVal === "number") {
        addPort(path, curVal + "");
      }
    };

    var uriCb = function (path, curVal) {
      var fnAddUri = function (uri) {
        // The following splits are retrieving the host and port from an uri
        // As all services have their own protocol format there are a few fallbacks
        var split =
          /[^/]*\/\/[^@]*@([^:]*):(\d*)\/?/.exec(uri) ||
          /[^/]*\/\/([^:]*):(\d*)\/?/.exec(uri) ||
          /[^/]*\/\/([^/?]*)\/?/.exec(uri);
        if (!split) {
          return;
        }
        addHost(path, split[1]);
        addPort(path, split[2]);
      };
      if (Array.isArray(curVal)) {
        curVal.forEach(fnAddUri);
      }
      if (typeof curVal === "string") {
        fnAddUri(curVal);
      }
    };

    walkParameters(obj, hostCb, portCb, uriCb);

    var fnReplace = function (obj, path, oldVal, newVal) {
      for (var i = 0; i < path.length; i++) {
        if (i === path.length - 1) {
          obj[path[i]] = JSON.parse(JSON.stringify(obj[path[i]]).replace(oldVal, newVal));
        }
        obj = obj[path[i]];
      }
    };

    var fnAdjustHanaCredentials = function (obj, path) {
      for (var i = 0; i < path.length; i++) {
        if (i === path.length - 1 && obj.host && obj.port) {
          obj.encrypt = true;
          obj.sslValidateCertificate = false;
          delete obj.certificate;
        }
        obj = obj[path[i]];
      }
    };

    for (var i = 0; i < localHosts.length; i++) {
      var curHost = localHosts[i];
      var hParent = [].concat(curHost.path);
      hParent.pop();
      var curPort =
        localPorts.find((p) => JSON.stringify(p.path) === JSON.stringify(curHost.path)) ||
        localPorts.find((p) => {
          var pParent = [].concat(p.path);
          pParent.pop();
          return JSON.stringify(pParent) === JSON.stringify(hParent);
        });

      var isHanaService = curHost.host.indexOf("zeus") > -1;
      var isHanaCloud = curHost.host.indexOf(".hana.canary-eu10.hanacloud.ondemand.com") > -1;
      if (isHanaService || isHanaCloud) {
        fnAdjustHanaCredentials(obj, curHost.path);
      } else {
        var curTargetHost = curHost.host + ":" + (curPort.port ? curPort.port : "443");
        totalMap[curTargetHost] = totalMap[curTargetHost] || lastPortNumber++;

        fnReplace(obj, curHost.path, curHost.host, "localhost");
        fnReplace(obj, curPort.path, curPort.port, totalMap[curTargetHost]);
      }
    }
    return obj;
  };

  var envLines = [];
  var keys = Object.keys(env);
  for (var i = 0; i < keys.length; i++) {
    // skip some values which are known to make trouble
    if (keys[i] === "COOKIES") {
      continue;
    }

    var curKey = keys[i];
    var curValue = env[curKey];
    if (typeof curValue === "object") {
      curValue = JSON.stringify(collectAndMapHosts(curValue));
    }
    envLines.push(`${curKey}=${curValue}`);
  }
  return envLines.join("\n");
};

let approuterNames = [...Array(10)]
  .map((_, i) => (i === 0 ? "" : Array(i).fill("-venerable").join("")))
  .map((suffix) => `approuter${suffix}`);

module.exports = cf
  .getEnv([appname, appnameGlobal, ...approuterNames])
  .catch((err) => {
    console.log("Failed to fetch enviroment from CF falling back to mocks.");
    console.error(err);
    // TODO: consider to have a check to fail in local development
    return [JSON.stringify(require("./mocks/coreEnv.json")), null, JSON.stringify(require("./mocks/uiEnv.json"))];
  })
  .then(async (enviroments) => {
    var rawCoreEnv = JSON.parse(enviroments[0] || enviroments[1]);
    var rawUiEnv = JSON.parse(enviroments.slice(2).find((x) => x != null));
    var keys = Object.keys(rawCoreEnv);
    var coreEnv = {};
    var uiEnv = {};
    for (var i = 0; i < keys.length; i++) {
      var curKey = keys[i];
      Object.assign(coreEnv, rawCoreEnv[curKey]);
      Object.assign(uiEnv, rawUiEnv[curKey]);
    }

    // Adjustments for local development
    if (
      coreEnv.VCAP_APPLICATION &&
      coreEnv.VCAP_APPLICATION.application_name &&
      coreEnv.VCAP_APPLICATION.application_name === "dwaas-core"
    ) {
      // prevent from connecting to provisioning
      coreEnv.VCAP_APPLICATION.application_name = "dwaas-core-dev";
    }

    // adapt environment with request XSUAA credential type
    let coreEnvModifier = undefined;
    console.log(`Using XSUAA credential type '${process.env.XSUAA_CREDENTIAL_TYPE}'`);

    if (coreEnv.MOCK === "1") {
      console.warn("Local dev is running with mocked environment");
      coreEnvModifier = Promise.resolve(coreEnv);
    } else {
      if (xsuaaServiceKeyName) {
        await cf.replaceServiceClientCredentials(coreEnv, "dwaas-core-uaa", "xsuaa", xsuaaServiceKeyName);
      } else {
        console.warn(
          `Requested XSUAA credential type '${process.env.XSUAA_CREDENTIAL_TYPE}' is not supported for local dev and will be ignored.`
        );
        coreEnvModifier = Promise.resolve(coreEnv);
      }
      await cf.replaceServiceClientCredentials(coreEnv, "orca-cert", "certificate-service", certificateServiceKeyName);
      await cf.replaceServiceClientCredentials(coreEnv, "orca-credstore", "credstore", credstoreServiceKeyName);
    }

    return Promise.all([
      fs.writeFile(path.resolve(__dirname, "../service/.env"), createEnvFormat(coreEnv)),
      fs.writeFile(path.resolve(__dirname, "../src/.env"), createEnvFormat(uiEnv)),
    ]).then(() =>
      fs.writeFile(
        path.resolve(__dirname, "tunnel.json"),
        JSON.stringify({
          map: totalMap,
          hana: {},
        })
      )
    );
  })
  .catch((err) => {
    console.error(err);
  });

if (module === process.mainModule) {
  module.exports
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .then(() => {
      process.exit(0);
    });
}
