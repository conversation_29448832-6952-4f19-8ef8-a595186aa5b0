# Support CLI

This folder provides some scripts for support.

## Prerequisites

Please make sure you have the following installed:

1. [Node.js](https://nodejs.org/en/)
2. CF CLI ([window](https://packages.cloudfoundry.org/stable?release=windows64-exe&source=github), [mac](https://packages.cloudfoundry.org/stable?release=macosx64-binary&source=github), [linux](https://packages.cloudfoundry.org/stable?release=linux64-binary&source=github))

Once these two tools are installed it is required to install the repository globally:

```
npm config set registry "https://int.repositories.cloud.sap/artifactory/api/npm/build-releases-npm/"
npm config set "@sap:registry" "https://int.repositories.cloud.sap/artifactory/api/npm/build-releases-npm/"
npm i -g git+https://github.wdf.sap.corp/orca/data_mart_management_service.git
```

If you already have a development setup of DWC, you can also use the following
command in the repository folder to install a development version of the CLI
tool instead:

```
npm link
```

Then login using the CF CLI:

```
cf login -o <ORG> -s <SPACE> --sso
```
CF will provide you with a URL to obtain a one-time passcode. If you already have a passcode (landscape-specific) because you know the  passcode directly:

```
cf login -o <ORG> -s <SPACE> --sso-passcode <PASSCODE>
```


## Usage

With the setup done it should be possible to call the command as follows:

```
dwc --tenant <tenantID>
```

It is also possible to define multiple `api` end points to be called by adding multiple `api` flags to the command.

```
dwc -t <tenantId> --api support/versions --api health
```

### Flags

#### `tenant`

To specify the target tenant for the command use:

```
dwc [-t | --tenant] <tenantId>
```

#### `api`

To specify the APIs to be called use:

```
dwc [-a | --api] <API>
```

or to call multiple APIs

```
dwc -a <API> --api <API>
```

#### `instances`

You can target a specific instance index:

```
dwc [-i | --instance] <index>
```

or a range of instance indices:

```
dwc [-i | --instance] <min-index>..<max-index>
```

This can be used to e.g. clear cache on all instances. Use responsibly.

#### `extuser`

To specify the user who is calling the API use:

```
dwc [--extuser] <UserId>
```

If the user is not specified, then the value from environment (USERNAME or USER) is being taken.

#### `headers`

To pass an additional HTTP header to the target APIs use:

```
dwc [-H | --headers ] <HEADER:VALUE>
```

or to pass multiple headers

```
dwc -H <HEADER:VALUE> --headers <HEADER:VALUE>
```

Multiple occurrences of the same header are concatenated using a comma.


#### `secrets`

You can supply your own secrets in case you want to bypass CF.

```
dwc -t <tenantId> -a <API> ... [-s | --secrets] <path-to-secrets-file>
```

The secrets file should be a JSON in the following format:

```ts
{
  "url"?: string,
  "tenantid"?: string,
  "uaaUrl"?: string,
  "uaaCertUrl"?: string,
  "clientid"?: string,
  "clientsecret"?: string,
  "key"?: string,
  "certificate"?: string,
  "auditlog"?: {
    "url"?: string,
    "user"?: string,
    "password"?: string,
  }
}
```

#### `settings`

In order to use the support consistency check for spaces one has to use the setting.jon file.
Specify the path to the file or keep it in the root folder with .settings.json.

```
dwc -t <tenantId> -a <API> ... [--settings] <path-to-settings-file>
```

The settings file should be a JSON in the following format:

```json
{
  "userJwt": "",
  "orcaContext": "{orcaTenantId:F,orcaTenantName:4B82C}" // For instance current starkiller: HTTP_X_SAP_BOC_APPROUTER_ORCA_CONTEXT
}
```

## How To

### Request Client Credentials JWT

To simply print out the client credentials JWT that is needed for the support routes, execute:

```
dwc token
```

If you want to analyze the JWT token and need it decoded, execute:

```
dwc decodedtoken
```

### Get Health Status/Versions

Execute the following to get global dwaas-core health status:

```
dwc -a health
```

Use the following to get the health status for a specific tenant:

```
dwc -t <tenantId> -a healthTenants/<tenantId>
```

For displaying all versions, execute:

```
dwc -t <tenantId> -a support/versions
```

### Change Minimum Log Level

This changes the minimum log level for a given tenant permanently.

```
dwc -t <tenantId> -a support/tenant/minimum-log-level --loglevel <level>
```

`<level>` can be `Error`, `Warning`, `Info`, `Verbose`, or `Debug`.

### Request Support User

```
dwc -t <tenantId> -a support/tenant/[customerhana | repository | marketplace]/request-support-user --role <role> --userid <userId>
```

`<role>` can be `DWC_SUP_RO`, `DWC_SUP_WT` or `DWC_SUP_EM`.\
`<userId>` is the D/I number of the requestor.\
More details regarding available roles and included privileges can be found on [this wiki page](https://wiki.wdf.sap.corp/wiki/display/DWAAS/Grant+access+to+HANA+databases).

### Fix Customer HANA admin user inconsistency

Execute the following to fix inconsistencies with the Customer HANA DBADMIN user:

```
dwc -t <tenantId> -a support/tenant/customerhana/fix-admin-user-inconsistency
```

In case the first execution fails, CloudOps (FPA35) needs to request a new password for DBADMIN via ticket to the HANA Cloud team.
Execute the following command replacing the `<password>` parameter with the new DBADMIN password:

```
dwc -t <tenantId> -a support/tenant/customerhana/fix-admin-user-inconsistency --password <password>
```

### (Re)provision tenant

```
dwc -t <tenantId> -a support/tenant/provision
```

### Check Space Consistency

```
dwc -t <tenantId> -a support/show/space/consistency
```

### Get and set circuit breaker state

Retrieve statistics for all circuit breakers:
```
dwc -t <tenantId> -a "support/circuitbreaker"
```

The result includes both system wide dependencies and tenant-specific information for the requested tenant. Information about other tenants' customer HANA systems is omitted.

Set circuit breaker state manually:
```
dwc -t <tenantId> -a "support/circuitbreaker" --category <category> --state [red | green | enabled | disabled ]
```

The state is used to temporarily switch the state between red and green (closed/open) based on the provided breaker settings. The default behaviour on these two is that if the state is set to green it will stay in this state until the breaker is triggered. If the state is forced to red it will stay red for at least 30 seconds.

Furthermore it is possible to permanently disable the breaker with `disabled`. There is an optional parameter `reEnablementTimeoutMs` to ensure the breaker is disabled only temporarily. The default setting is that breakers automatically get re-enabled after 12 hours (or after the next instance restart).

Allowed values for `category` are taken from the [external call types](https://github.wdf.sap.corp/orca/data_mart_management_service/blob/master/service/repository/security/common/externalCallTypes.ts) module.

### Get marketplace provider access key

Create a new access key for a provider
```
dwc -t <tenantId> --app marketplace -a "support/tenant/marketplace/request-access-key" --providerid <providerid> --userid <userId>
```

### Block a DataProduct in marketplace

Blocks a DataProduct in case of non-compliance
```
dwc -t <tenantId> --app marketplace -a "support/tenant/marketplace/request-block-product" --productid <productid> --isBlocked <isBlocked> --userid <userId>
```

### Force delete a DataProduct in marketplace for testing purpose

Deletes a DataProduct
```
dwc -t <tenantId> --app marketplace -a "support/tenant/marketplace/request-delete-product" --productid <productid> --userid <userId>
```

### Delete a Provider from marketplace

Deletes a Data provider

```
dwc -t <tenantId> --app marketplace -a "support/tenant/marketplace/request-delete-provider" --providerid <providerid> --userid <userId>

### Sync delta share marketplace product to deepsea repository

Sync marketplace DataProduct definition with deepsea for delta share products
```
dwc -t <tenantId> --app marketplace -a "/support/tenant/marketplace/sync-delta-share-products" --productid <productid> --productids <productid_1,productid_2> --providerid <providerid> --userid <userid>
```

### Sync UCL formation updates

Sync UCL formation updates to create marketplace context for new formation tenant mapping.
```
dwc -t <tenantId> --app marketplace -a "/support/tenant/marketplace/sync-ucl-formation-updates" --formationid <formationid> --tenantid <tenantid> --type <type> --userid <userid>
```

### Sync catalog enrichment objects in marketplace

Sync catalog enrichment object in marketplace
```
dwc -t <tenantId> --app marketplace -a "support/tenant/marketplace/sync-enrichments" --catalogtenantid <catalogtenantid> --productid <productid> --batchsize <batchsize>
```

### Sync all marketplace objects (provider, product, installations) to deepsea

Sync all marketplace objects product, provider and installations to deepsea

```
dwc -t <tenantId> --app marketplace -a "support/tenant/marketplace/sync-all" --userid <userId>
```

### Migrate marketplace Bookmarks objects to favorite objects in deepsea

Migrate marketplace Bookmarks objects to favorite objects in deepsea from the given scope (tenant, object type). "objectTypes" are optional.

```
dwc -t <tenantId> --app marketplace -a "/support/tenant/marketplace/migrate-bookmarks-favorites" --userid <userId> --objectTypes <list of objectTypes> [DataProduct | Provider]
```

### Repair single tenant`s marketplace metadata (provider, product, installations) in deepsea

Repair single tenant`s marketplace metadata in deepsea

```
dwc -t <tenantId> --app marketplace -a "support/tenant/marketplace/repair-tenant" --userid <userId>
```

### Repair marketplace product metadata in deepsea

Repair marketplace product metadata in deepsea

```
dwc -t <tenantId> --app marketplace -a "support/tenant/marketplace/repair-dataproduct-sync" --productid <productid> --userid <userId>
```

### Repair marketplace installation metadata in deepsea

Repair marketplace installation metadata in deepsea

```
dwc -t <tenantId> --app marketplace -a "support/tenant/marketplace/repair-installation-sync" --installationid <installationid> --userid <userId>
```

### Repair marketplace provider metadata in deepsea

Repair marketplace provider metadata in deepsea

```
dwc -t <tenantId> --app marketplace -a "support/tenant/marketplace/repair-provider-sync" --providerid <providerid> --userid <userId>
```

### Delete stale marketplace objects in deepsea

Delete stale marketplace objects in deepsea from the given scope (tenant,spaces, object type). "spaces" and "objectTypes" are optional.

```
dwc -t <tenantId> --app marketplace -a "support/tenant/marketplace/delete-stale-objects-in-deepsea" --providerid <providerid> --userid <userId> --spaces <list of spaces> --objectTypes <list of objectTypes> [DataProduct | Provider | Installation ]
```
### Read and write configuration values

Read the current applicable value for a configuration parameter for a given tenant
```
dwc -t <tenantId> -a 'support/configurations/parameters/<paramKey>/effectiveValue' [ --scope tenant ]
```

Read the global default value in case your tenant does not override the value for a given parameter
```
dwc -t <tenantId> -a 'support/configurations/parameters/<paramKey>/effectiveValue' --scope deployment
```

Set a new value for the configuration parameter in the specific tenant
```
dwc -t <tenantId> -a 'support/configurations/parameters/<paramKey>/values' --newValue <newValue>
```

## List of Support Routes

this list is copied from `data_mart_management_service\service\routes\support\index.ts` and should be kept in sync with the source code.
More documentation on repository routes can be found in [this wiki](https://wiki.wdf.sap.corp/wiki/display/DWAAS/Repository+API#RepositoryAPI-support/).
You can read more on the `SEAL-Deployer service` and its support routes [here](https://github.wdf.sap.corp/orca/seal/tree/master/apps/service#support-routes).

### dwaas-core

|Route (dwaas-core/support...)              | Verb  | Documentation  | Jenkins available |
|-------------------------------------------|-------|----------------|-------------------|
|/tenant/minimum-log-level                  | POST  | Change Logging Level of HANA Database <br> Check the logLevel form a e.g. passed REST call and checks whether it is part of the LogLevel. Returns the current and previous log level and utc time, if available otherwise undefined.|yes|
|/tenant/customerhana/request-support-user  | POST  |  Grant access to HANA databases  |yes|
|/tenant/repository/request-support-user    | POST  |  Grant access to HANA databases  |yes|
|/tenant/marketplace/request-support-user   | POST  |  Grant access to HANA databases  |yes|
|/versions                                  | GET   | return version information  ||
|/tenant/customerhana/fix-admin-user-inconsistency|POST|||
|/tenant/customerhana/fix-user-manager-inconsistency|POST|||
|/tenant/customerhana/force-upgrade         | POST  |||
|/tenant/customerhana/flexible-configuration/metadata/clear| POST  | Clear Flexible Tenant metadata stored during elastic provisioning|
|/tenant/customerhana/update-hana-parameter| POST  | Change hana parameters for FTC customer hana (update_strategy: with_restart or without_restart and scriptserver: true or false) |
|/tenant/customerhana/alerts/events| GET | Fetch customer HANA alert events|
|/tenant/customerhana/alerts/rules| GET | Fetch customer HANA alert rules|
|/tenant/customerhana/metrics/definitions| GET | Fetch customer HANA metric definitions|
|/tenant/customerhana/metrics/values| GET | Fetch customer HANA metric values|
|/tenant/ip-allowlist| GET  | Fetch current ip allowlist from a given tenant|
|/tenant/ip-allowlist| DELETE  | Remove entries from the ip allowlist of the given tenant. Use ipList:["x.x.x.x", "x.x.x./x"] in the body to inform the IPs to be deleted|
|/tenant/repository/hanacloudmigration      | POST  |||
|/tenant/ip-allowlist| POST  | Add new entries into the ip allowlist of the given tenant. Use ipList:["x.x.x.x", "x.x.x./x"] in the body to inform the IPs to be included|
|/tenant/ip-allowlist/check| POST  | Check whether the given IPs are allowlisted or not. Use ipList:["x.x.x.x", "x.x.x./x"] in the body to inform the IPs to be verified|
|/tenant/repository/hanacloudmigrationstatus| GET   |get the current status of hanacloud migration for tenant|
|/tenant/repository/hanacloudmigrationswitch/:switch| POST |||
|/tenant/repository/hdi/upgrade             | POST  | Triggers an upgrade for the repository HDI container ||
|/tenant/repository/hdi/grant-access        | POST  | Grants access to the repository HDI container for the provided user. Expects `user` (mandatory) and `writeAccess` (optional) options in request body. ||
|/tenant/repository/hdi/lock                |DELETE | Clears the RepositoryLock for HDI deployment ||
|/tenant/repository/hdi/massmigrate         | POST  | Clears the RepositoryLock for MassMigrate and executes a repair route ||
|/repository/hanacloudmigration             | POST  |||
|/tenant/provision                          | POST  |||
|/tenant/repository/recovery                | POST  |Recovery of tenant schema from remote (temporary) databse||
|/delete/spaces                             |DELETE |||
|/delete/scopes                             |DELETE |Deletes scopes {"spaceIds":["A", "B"]}||
|/repair/spaces                             |PATCH  |||
|/show/space/consistency                    | GET   |||
|/repository/consistency                    | GET   |[Repository API#support/repository/consistency](https://wiki.wdf.sap.corp/wiki/display/DWAAS/Repository+API#RepositoryAPI-support/repository/consistency)||
|/repository/loghistory                    | GET   |[Repository API#support/repository/loghistory](https://wiki.wdf.sap.corp/wiki/display/DWAAS/Repository+API#RepositoryAPI-support/repository/loghistory)||
|/repository/auditadmin                     | GET   |[Repository API#support/repository/auditadmin](https://wiki.wdf.sap.corp/wiki/display/DWAAS/Repository+API#RepositoryAPI-support/repository/auditadmin)||
|/repository/credentials                    | GET   |||
|/:space/credentials                        | GET |Returns access credentials for a particular space user role (e.g. DIe user, InA user, wrangler) ||
|/environment                               | GET |List environment variables of the process||
|/circuitbreaker                            | GET | Retrieve statistics for all circuit breakers ||
|/circuitbreaker                            | PUT | Set circuit breaker state manually ||
|/circuitbreaker/test                       | GET | See [DW00-2792](https://sapjira.wdf.sap.corp/browse/DW00-2792)  ||
|/tenant/metering/reports                   | PUT | Report custom usage document to Metering Service.  ||
|/tenant/metering/reports                   | GET | Get last reports from Metering Service  ||
|/tenant/rotate-passwords                   | POST | Rotates all supported credentials for technical HANA users. See [DW00-4264](https://sapjira.wdf.sap.corp/browse/DW00-4264) and [DS00-970](https://jira.tools.sap/browse/DS00-970)||
|/tenant/service-instance                   | GET | Return list of all service instances from tenant. Also accept an instanceId parameter to return all service parameters||
|/tenant/workflows                          | GET | Return list of all workflows from tenant. Also accept an workflowId parameter to return workflow details||
|/customerhana/trigger-tenants-upgrade      | POST | Triggers the publishing of the messages for all tenants to the upgrade message queue to perform the customer hana upgrade. See [DW00-1929](https://sapjira.wdf.sap.corp/browse/DW00-1929)||
|/abap-bridge-configuration/force-refresh   | POST | Force the refresh of all BW Bridge instance status based on the latest abap service instance status in BTP. See [DW00-4558](https://sapjira.wdf.sap.corp/browse/DW00-4558) for additional user story details.||
|/abap-bridge/database-connection   | POST | Endpoint called by SPC to provide HANA Cloud credentials of a given BW Bridge system. See [wiki](https://wiki.wdf.sap.corp/wiki/display/DWAAS/BW+Bridge+Provisioning) for additional user story details.||
|/redis/status   | GET | Fetches current runtime information about redis-cache for dwaas-core ||
|/redis/slowlog   | GET | Fetches current slowlog entries for expensive commands ||
|/redis/flushall   | POST | Flushes all cache entries ||
|/redis/cache/:key   | GET | On Canary/Dev: Fetches cached content from Redis entry by its key - On Production: Returns cache TTL if existing ||
|/redis/cache/:key   | DELETE | Delete cache entry from Redis by its key ||
|/profile/memory/heapsnapshot   | GET | Get V8 heap snapshot as JSON ||
|/profile/memory/heapstatistics   | GET | Get V8 heap statistics as JSON ||
|/profile/memory/heapspacestatistics   | GET | Get V8 heap space statistics as JSON ||
|/profile/memory/gctracesenable   | POST | Enable traces for garbage collection (GC) in logs ||
|/profile/memory/gctracesdisable   | POST | Disable traces for garbage collection (GC) in logs ||
|/profile/cpu   | GET | Get a CPU profile for a certain duration ||
|/profile/eventloopblockers  | GET | Get content of the eventLoopBlockers.txt file which contains event loop blockers report. Also supports enablement of event loop blocker reporting for a duration of up to 5 minutes. ||
|/profile/eventloopblockers  | DELETE | Deletes file eventLoopBlockers.txt which contains event loop blockers report ||
|/deepsea/profile/memory/heapsnapshot   | GET | Get V8 heap snapshot as JSON from deepsea by providing application_id and instance parameters ||
|/deepsea/profile/memory/heapstatistics   | GET | Get V8 heap statistics as JSON from deepsea by providing application_id and instance parameters ||
|/deepsea/profile/memory/heapspacestatistics   | GET | Get V8 heap space statistics as JSON from deepsea by providing application_id and instance parameters ||
|/deepsea/profile/memory/gctracesenable   | POST | Enable traces for deepsea garbage collection (GC) in logs by providing application_id and instance parameters ||
|/deepsea/profile/memory/gctracesdisable   | POST | Disable traces for deepsea garbage collection (GC) in logs by providing application_id and instance parameters ||
|/deepsea/profile/cpu   | GET | Get a CPU profile in deepsea for a certain duration by providing application_id and instance parameters ||
|/deepsea/profile/eventloopblockers  | GET | Get content of the eventLoopBlockers.txt file which contains event loop blockers report from deepsea by providing application_id and instance parameters. Also supports enablement of event loop blocker reporting for a duration of up to 5 minutes. ||
|/deepsea/profile/eventloopblockers  | DELETE | Deletes file eventLoopBlockers.txt which contains event loop blockers report from deepsea by providing application_id and instance parameters ||
|/deepsea-daemon/profile/memory/heapsnapshot   | GET | Get V8 heap snapshot as JSON from deepsea-daemon by providing application_id and instance parameters. ||
|/deepsea-daemon/profile/memory/heapstatistics  | GET | Get V8 heap statistics as JSON from deepsea-daemon by providing application_id and instance parameters ||
|/deepsea-daemon/profile/memory/heapspacestatistics   | GET | Get V8 heap space statistics as JSON from deepsea-daemon by providing application_id and instance parameters ||
|/deepsea-daemon/profile/memory/gctracesenable   | POST | Enable traces for deepsea-daemon garbage collection (GC) in logs by providing application_id and instance parameters ||
|/deepsea-daemon/profile/memory/gctracesdisable   | POST | Disable traces for deepsea-daemon garbage collection (GC) in logs by providing application_id and instance parameters ||
|/deepsea-daemon/profile/cpu   | GET | Get a CPU profile in deepsea-daemon for a certain duration by providing application_id and instance parameters ||
|/deepsea-daemon/profile/eventloopblockers  | GET | Get content of the eventLoopBlockers.txt file which contains event loop blockers report from deepsea-daemon by providing application_id and instance parameters . Also supports enablement of event loop blocker reporting for a duration of up to 5 minutes. ||
|/deepsea-daemon/profile/eventloopblockers  | DELETE | Deletes file eventLoopBlockers.txt which contains event loop blockers report from deepsea-daemon by providing application_id and instance parameters ||
|/tenant/repository/workload-class   | GET | List parameters of the tenant repository workload class  ||
|/tenant/repository/workload-class   | PUT | Changes parameters of tenant repository workload class. [Examples](#put-workloadclass) ||
|/tenant/repository/workload-class-toggle   | PATCH | Enable or disable tenant repository workload class. Body example: {"ENABLED": true}||
|/tenant/repository/workload-class-statistics   | GET | List tenant repository workload class statistics  ||
|/repository/workload-class   | GET | List parameters of all repository workload classes ||
|/repository/workload-class   | PATCH | Change all repository workload classes of specific category. [Example](#patch-workloadclass) ||
|/repository/workload-class-toggle   | PATCH | Enable or disable all repository workload classes. Body example: {"ENABLED": true}||
|/repository/workload-class-statistics   | GET | List all repository workload classes statistics ||
|/repository/workload-class-consistency   | GET | List tenants without repository workload classes, tenants without workload mappings and workload classes not mapped ||
|/tenant/customerhanahdi/containers   | GET | Get information about the HDI containers deployed on CustomerHana ||
|/tenant/customerhanahdi/cache   | GET | Get the value saved on the async cache used on deployHdiContainres.ts file ||
|/tenant/customerhanahdi/cache   | DELETE | Clear the value saved on the async cache used on deployHdiContainres.ts file ||
|/auditlog/state   | POST | Alter the state of the AuditLogClient singleton to inject errors ||
|/hana/query  | GET | Returns resultsets from pre-defined SQL queries that help analyse RCAs ||
|/repository/onboard-client  | POST | Starts the onboarding client of the Repository HANA ||
|/repository/hdi/onboard-manager  | POST | Starts the onboarding of the Repository HDI Manager ||
|support/configurations/parameters/$KEY/effectiveValue  | GET | Read parameter value from the [Configuration Management Service]((https://jira.tools.sap/browse/DW00-6260) (or cache) ||
|support/configurations/parameters/$KEY/values  | POST | Set parameter value in the [Configuration Management Service]((https://jira.tools.sap/browse/DW00-6260) ||
|/support/di-e/tenant/rmspodcount  | GET | Get the DI Embedded RMS Pod Count ||
|/support/di-e/tenant/rmspodcount  | PUT | Update the DI Embedded RMS Pod Count. Expects request body. Example: {rmsPodCount: 4}. See [DW00-9026](https://jira.tools.sap/browse/DW00-9026) ||
|/support/bwbridge/create-instance  | POST | Create an instance of BW Bridge in canary landscape. ||
|/support/:space/deploy/cleanupmdsmetadata  | POST | Cleanup inconsistent MDS metadata in given space. ||
|/support/endpoints  | GET | Returns the current endpoints.ts status that is applied in the endpointsfilter middleware. ||
|/support/endpoints/disable  | POST | Disables a specific endpoint for all tenants by setting the rate limit to 0. Calls to the endpoint will return http status 429 rate limit reached. ||
|/support/endpoints/reset  | POST | Reset any endpoint changes back to the definition from endpoints.ts. ||
|/support/metering/check-metering-availability/:key | GET | List all tenants in landscape with metering report or collector enabled and another with the tenants with metering disabled. ||
|/support/cache/invalidate  | POST | Triggers cache invalidation across all instances for in-memory and/or redis. ||
|/support/tenant/tf/systemschedules/status/:pauseorresume | PUT | Updates the activation status of an existing system task schedule to PAUSE or RESUME the schedule ||
|/support/tenant/tf/systemschedules/edit | PUT | Updates the cron value of an existing system task schedule in the table TASK_SCHEDULES and sets a ROLLBACK_TIME in the TASK_SYSTEM_SCHEDULES table ||
|/support/hana/configuration/selectstatistic | POST | Set/unset the parameter table_statistics_select_enabled on HANA ||
|support/tenant/token-exchanger-configuration | POST | Configures the token exchanger with the provided logical DSP client credentials and IAS URL. This also support updating(upserting) the configuration if corresponding config already exists.
|support/tenant/token-exchanger-configuration | DELETE | Removes the token exchanger configuration including removing the logical DSP client credentials and IAS URL from the securestore

### marketplace

To use any of this plugins make sure to provide the `--app marketplace` parameters

|Route (marketplace/support...)              | Verb  | Documentation  | Jenkins available |
|-------------------------------------------|-------|----------------|-------------------|
|/tenant/marketplace/request-support-user   | POST  | Grant access to HANA databases |yes|
|/tenant/marketplace/request-access-key     | POST  | Creates access key for a specific provider ||
|/tenant/marketplace/remove-obsolete-users  | POST  | Removes users using ID or email. Both parameters can be either a list of elements to be removed (array) or individual elements  ||
|/tenant/marketplace/request-block-product  | POST  | Blocks DataProduct in case of non-compliance ||
|/tenant/marketplace/request-delete-product | POST  | Force delete DataProduct ||
|/tenant/marketplace/sync-enrichments | POST  | Sync catalog enrichment object in marketplace ||
|/tenant/marketplace/request-delete-provider | POST | Delete a Data provider ||
|/tenant/marketplace/sync-all | POST | Sync all marketplace objects to deepsea ||
|/tenant/marketplace/repair-tenant | POST | Repair single tenants marketplace metadata in deepsea ||
|/tenant/marketplace/repair-dataproduct-sync | POST | Repair marketplace product metadata in deepsea ||
|/tenant/marketplace/repair-installation-sync | POST | Repair marketplace installation metadata in deepsea ||
|/tenant/marketplace/repair-provider-sync | POST | Repair marketplace provider metadata in deepsea ||
|/tenant/marketplace/delete-stale-objects-in-deepsea | POST | Delete stale marketplace objects in deepsea ||

|/tenant/marketplace/sync-delta-share-products | POST  | Sync marketplace DataProduct definition with deepsea for delta share products ||
|/tenant/marketplace/sync-ucl-formation-updates | POST  | Sync UCL formation updates to create marketplace context for new formation tenant mapping ||
### Examples of repository workload class operations.

#### <a id="put-workloadclass"></a>Example repository tenant workload class PUT

If tenant should upgrade from PROD to HIGH workload class:
```json
{
"CATEGORY": "HIGH"
}
```
Its also possible to create a custom workload class.
Body example to create custom workload class:
```json
{
"CATEGORY": "CUSTOM",
  "CUSTOM": {
    "PRIORITY": 7,
    "TOTAL_STATEMENT_MEMORY_LIMIT": 80,
    "TOTAL_STATEMENT_MEMORY_LIMIT_UNIT": "Percent",
    "TOTAL_STATEMENT_THREAD_LIMIT": 80,
    "TOTAL_STATEMENT_THREAD_LIMIT_UNIT": "Percent",
    "ADMISSION_CONTROL_QUEUE_MEMORY_THRESHOLD": 80
  }
}
```

#### <a id="patch-workloadclass"></a>Example repository workload class PATCH
This example will change all TRIAL workload classes parameters in the repository.
```json
{
  "CATEGORY": "TRIAL",
  "PRIORITY": 4,
  "TOTAL_STATEMENT_MEMORY_LIMIT": 60,
  "TOTAL_STATEMENT_MEMORY_LIMIT_UNIT": "Percent",
  "TOTAL_STATEMENT_THREAD_LIMIT": 60,
  "TOTAL_STATEMENT_THREAD_LIMIT_UNIT": "Percent",
  "ADMISSION_CONTROL_QUEUE_MEMORY_THRESHOLD": 60
}
```

