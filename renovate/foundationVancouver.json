{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "packageRules": [{"matchPackageNames": ["@sap/credential-store-client-node", "@sap/embed-iframe", "@sap/orca-starter-security", "@sap/orca-starter-solace", "@sap/xsenv", "ajv", "express", "lodash", "passport", "uuid"], "additionalReviewers": ["orca/dw101_foundation_van"], "labels": ["DSP Foundation Vancouver"], "enabled": true, "separateMultipleMajor": true}, {"matchPackageNames": ["@sap/orca-starter-security", "@sap/orca-starter-solace"], "groupName": "orca-starter-security - solace", "prFooter": "test_dirs_to_run=[]"}, {"matchPackageNames": ["passport"], "prFooter": "test_dirs_to_run=[cypress/integration,service/tests]"}, {"matchPackageNames": ["@sap/embed-iframe"], "prFooter": "test_dirs_to_run=[cypress/integration/crossarchitecture/shell]"}, {"matchPackageNames": ["@sap/xsenv", "@sap/credential-store-client-node"], "prFooter": "test_dirs_to_run=[cypress/integration/crossarchitecture/shell,service/tests,service/reuseComponents/spaces/test,service/reuseComponents/onboarding/test]"}, {"matchPackageNames": ["express"], "prFooter": "test_dirs_to_run=[service/tests/meta,service/tests/routes/support,service/tests/routes/health]"}, {"matchPackageNames": ["lodash", "uuid"], "prFooter": "test_dirs_to_run=[]"}, {"matchPackageNames": ["ajv"], "prFooter": "test_dirs_to_run=[cypress/integration,service/tests,service/reuseComponents/spaces/test/unit]"}]}