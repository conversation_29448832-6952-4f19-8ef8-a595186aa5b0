/**
 * Test file for partition extensions functionality
 * Tests the new functionality for handling partitions when @Metadata.allowExtensions is true
 *
 * @format
 */

import { partitionsReadCsn, resetPartitionsFromExtensions } from "../src/components/commonmodel/csn/csnUtils";

describe("Partition Extensions Functionality", () => {
  let mockEntity: any;
  let mockCsnEntity: any;
  let mockCsnExtensions: any;

  beforeEach(() => {
    // Mock the feature flag function
    jest.mock("../../abstractbuilder/commonservices/FeatureFlagCheck", () => ({
      isModelingAnnotatePartitionsEnabled: jest.fn().mockReturnValue(true),
    }));

    // Mock entity
    mockEntity = {
      isTable: true,
      isLocal: true,
      partitions: {
        push: jest.fn(),
        deleteAll: jest.fn(),
        forEach: jest.fn(),
      },
      resource: {
        model: {},
      },
    };

    // Mock CSN entity with allowExtensions
    mockCsnEntity = {
      "@Metadata.allowExtensions": true,
      "@DataWarehouse.partition": {
        by: { "#": "RANGE" },
        elements: [{ "=": "ID" }],
        ranges: [{ low: "1", high: "100" }],
      },
    };

    // Mock CSN extensions with partitions
    mockCsnExtensions = {
      annotate: "test_entity",
      "@DataWarehouse.partition": {
        by: { "#": "RANGE" },
        elements: [{ "=": "NAME" }],
        ranges: [
          { low: "A", high: "M" },
          { low: "M", high: "Z" },
        ],
      },
    };

    // Mock sap.galilei.model.getClass
    global.sap = {
      galilei: {
        model: {
          getClass: jest.fn().mockReturnValue({
            create: jest.fn().mockReturnValue({
              ranges: { push: jest.fn() },
            }),
          }),
        },
      },
    };
  });

  describe("partitionsReadCsn", () => {
    it("should read partitions from extensions when allowExtensions is true and extensions exist", () => {
      partitionsReadCsn(mockEntity, mockCsnEntity, mockCsnExtensions);

      // Should set allowExtensions flag
      expect((mockEntity as any).allowExtensions).toBe(true);
      // Should set partitionsFromExtension flag
      expect((mockEntity as any).partitionsFromExtension).toBe(true);
      // Should have called push to add partition
      expect(mockEntity.partitions.push).toHaveBeenCalled();
    });

    it("should be in extensions mode even when no partitions exist in extensions", () => {
      // Create mock extensions without partitions
      const mockCsnExtensionsWithoutPartitions = {
        annotate: "test_entity",
        "@EndUserText.label": "Test Label",
        // No partition annotations
      };

      partitionsReadCsn(mockEntity, mockCsnEntity, mockCsnExtensionsWithoutPartitions);

      // Should set allowExtensions flag
      expect((mockEntity as any).allowExtensions).toBe(true);
      // Should still set partitionsFromExtension flag to true (extensions mode)
      expect((mockEntity as any).partitionsFromExtension).toBe(true);
      // Should have cleared regular partitions
      expect(mockEntity.partitions.deleteAll).toHaveBeenCalled();
      // Should NOT have called push since no partitions exist in extensions
      expect(mockEntity.partitions.push).not.toHaveBeenCalled();
    });

    it("should read partitions from main definition when allowExtensions is false", () => {
      mockCsnEntity["@Metadata.allowExtensions"] = false;

      partitionsReadCsn(mockEntity, mockCsnEntity, mockCsnExtensions);

      // Should set allowExtensions flag to false
      expect((mockEntity as any).allowExtensions).toBe(false);
      // Should set partitionsFromExtension flag to false
      expect((mockEntity as any).partitionsFromExtension).toBe(false);
      // Should have called push to add partition from main definition
      expect(mockEntity.partitions.push).toHaveBeenCalled();
    });

    it("should fall back to original behavior when feature flag is disabled", () => {
      // Mock feature flag to be disabled
      const mockFeatureFlag = require("../../abstractbuilder/commonservices/FeatureFlagCheck");
      mockFeatureFlag.isModelingAnnotatePartitionsEnabled.mockReturnValue(false);

      partitionsReadCsn(mockEntity, mockCsnEntity, mockCsnExtensions);

      // Should have called push to add partition from main definition (original behavior)
      expect(mockEntity.partitions.push).toHaveBeenCalled();
      // Should not set extension flags when feature flag is off
      expect((mockEntity as any).allowExtensions).toBeUndefined();
    });
  });

  describe("resetPartitionsFromExtensions", () => {
    it("should reset partitions from extensions to main definition", () => {
      // Set up entity as if it's in extensions mode
      (mockEntity as any).allowExtensions = true;
      (mockEntity as any).partitionsFromExtension = true;
      (mockEntity as any).actualPartitionInfo = mockCsnEntity["@DataWarehouse.partition"];

      resetPartitionsFromExtensions(mockEntity, mockCsnEntity, mockCsnExtensions);

      // Should clear current partitions
      expect(mockEntity.partitions.deleteAll).toHaveBeenCalled();
      // Should set partitionsFromExtension to false
      expect((mockEntity as any).partitionsFromExtension).toBe(false);
    });

    it("should do nothing when feature flag is disabled", () => {
      // Mock feature flag to be disabled
      const mockFeatureFlag = require("../../abstractbuilder/commonservices/FeatureFlagCheck");
      mockFeatureFlag.isModelingAnnotatePartitionsEnabled.mockReturnValue(false);

      resetPartitionsFromExtensions(mockEntity, mockCsnEntity, mockCsnExtensions);

      // Should not have called deleteAll
      expect(mockEntity.partitions.deleteAll).not.toHaveBeenCalled();
    });
  });
});
