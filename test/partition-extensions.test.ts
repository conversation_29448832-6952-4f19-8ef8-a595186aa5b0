/**
 * Test file for partition extensions functionality
 * Tests the new functionality for handling partitions when @Metadata.allowExtensions is true
 *
 * @format
 */

import {
  partitionsReadCsn,
  partitionsWriteCsn,
  resetPartitionsFromExtensions,
} from "../src/components/commonmodel/csn/csnUtils";

describe("Partition Extensions Functionality", () => {
  let mockEntity: any;
  let mockCsnEntity: any;
  let mockCsnExtensions: any;

  beforeEach(() => {
    // Mock entity
    mockEntity = {
      isTable: true,
      isLocal: true,
      partitions: {
        push: jest.fn(),
        deleteAll: jest.fn(),
        get: jest.fn(),
        length: 0,
      },
      resource: {
        applyUndoableAction: jest.fn(),
      },
    };

    // Mock CSN entity with allowExtensions
    mockCsnEntity = {
      "@Metadata.allowExtensions": true,
      "@DataWarehouse.partition": {
        by: { "#": "RANGE" },
        elements: [{ "=": "column1" }],
        ranges: [
          { low: "1", high: "10" },
          { low: "10", high: "20" },
        ],
      },
    };

    // Mock CSN extensions with partition override
    mockCsnExtensions = {
      "@DataWarehouse.partition": {
        by: { "#": "RANGE" },
        elements: [{ "=": "column2" }],
        ranges: [
          { low: "100", high: "200" },
          { low: "200", high: "300" },
        ],
      },
    };
  });

  describe("partitionsReadCsn", () => {
    beforeEach(() => {
      // Mock feature flag to be enabled
      global.sap = {
        ui: {
          getCore: jest.fn().mockReturnValue({
            getModel: jest.fn().mockReturnValue({
              getProperty: jest.fn().mockReturnValue(true), // Feature flag enabled
            }),
          }),
        },
      };
    });

    it("should read partitions from extensions when allowExtensions is true and extensions exist", () => {
      partitionsReadCsn(mockEntity, mockCsnEntity, mockCsnExtensions);

      // Should set allowExtensions flag
      expect((mockEntity as any).allowExtensions).toBe(true);
      // Should set partitionsFromExtension flag
      expect((mockEntity as any).partitionsFromExtension).toBe(true);
      // Should have called push to add partition
      expect(mockEntity.partitions.push).toHaveBeenCalled();
    });

    it("should read partitions from main definition when allowExtensions is true but no extensions", () => {
      partitionsReadCsn(mockEntity, mockCsnEntity, undefined);

      // Should set allowExtensions flag
      expect((mockEntity as any).allowExtensions).toBe(true);
      // Should not set partitionsFromExtension flag
      expect((mockEntity as any).partitionsFromExtension).toBe(false);
      // Should have called push to add partition from main definition
      expect(mockEntity.partitions.push).toHaveBeenCalled();
    });

    it("should read partitions from main definition when allowExtensions is false", () => {
      mockCsnEntity["@Metadata.allowExtensions"] = false;
      partitionsReadCsn(mockEntity, mockCsnEntity, mockCsnExtensions);

      // Should set allowExtensions flag to false
      expect((mockEntity as any).allowExtensions).toBe(false);
      // Should not set partitionsFromExtension flag
      expect((mockEntity as any).partitionsFromExtension).toBe(false);
      // Should have called push to add partition from main definition
      expect(mockEntity.partitions.push).toHaveBeenCalled();
    });

    it("should fall back to original behavior when feature flag is disabled", () => {
      // Mock feature flag to be disabled
      global.sap.ui.getCore().getModel().getProperty.mockReturnValue(false);

      partitionsReadCsn(mockEntity, mockCsnEntity, mockCsnExtensions);

      // Should have called push to add partition from main definition (original behavior)
      expect(mockEntity.partitions.push).toHaveBeenCalled();
      // Should not set extension flags when feature flag is off
      expect((mockEntity as any).allowExtensions).toBeUndefined();
    });
  });

  describe("partitionsWriteCsn", () => {
    beforeEach(() => {
      // Mock feature flag to be enabled
      global.sap = {
        ui: {
          getCore: jest.fn().mockReturnValue({
            getModel: jest.fn().mockReturnValue({
              getProperty: jest.fn().mockReturnValue(true), // Feature flag enabled
            }),
          }),
        },
      };

      (mockEntity as any).allowExtensions = true;
      mockEntity.partitions.get = jest.fn().mockReturnValue({
        partitionType: "RANGE",
        id: "column1",
        ranges: [
          { low: "1", high: "10" },
          { low: "10", high: "20" },
        ],
      });
      mockEntity.partitions.length = 1;
    });

    it("should write partitions to extensions when allowExtensions is true", () => {
      partitionsWriteCsn(mockEntity, mockCsnEntity, mockCsnExtensions);

      // Should write to extensions
      expect(mockCsnExtensions["@DataWarehouse.partition"]).toBeDefined();
      expect(mockCsnExtensions["@DataWarehouse.partition"].by["#"]).toBe("RANGE");
      expect(mockCsnExtensions["@DataWarehouse.partition"].elements[0]["="]).toBe("column1");
    });

    it("should write partitions to main CSN when allowExtensions is false", () => {
      (mockEntity as any).allowExtensions = false;
      partitionsWriteCsn(mockEntity, mockCsnEntity, mockCsnExtensions);

      // Should write to main CSN
      expect(mockCsnEntity["@DataWarehouse.partition"]).toBeDefined();
      expect(mockCsnEntity["@DataWarehouse.partition"].by["#"]).toBe("RANGE");
      expect(mockCsnEntity["@DataWarehouse.partition"].elements[0]["="]).toBe("column1");
    });

    it("should delete partitions from both main and extensions when no partitions exist", () => {
      mockEntity.partitions.get = jest.fn().mockReturnValue(undefined);
      mockEntity.partitions.length = 0;

      partitionsWriteCsn(mockEntity, mockCsnEntity, mockCsnExtensions);

      // Should delete from both
      expect(mockCsnEntity["@DataWarehouse.partition"]).toBeUndefined();
      expect(mockCsnExtensions["@DataWarehouse.partition"]).toBeUndefined();
    });
  });

  describe("resetPartitionsFromExtensions", () => {
    beforeEach(() => {
      // Mock feature flag to be enabled
      global.sap = {
        ui: {
          getCore: jest.fn().mockReturnValue({
            getModel: jest.fn().mockReturnValue({
              getProperty: jest.fn().mockReturnValue(true), // Feature flag enabled
            }),
          }),
        },
      };

      (mockEntity as any).allowExtensions = true;
      (mockEntity as any).partitionsFromExtension = true;
    });

    it("should reset partitions from extensions to main definition", () => {
      resetPartitionsFromExtensions(mockEntity, mockCsnEntity, mockCsnExtensions);

      // Should clear current partitions
      expect(mockEntity.partitions.deleteAll).toHaveBeenCalled();
      // Should remove partition info from extensions
      expect(mockCsnExtensions["@DataWarehouse.partition"]).toBeUndefined();
      // Should update flags
      expect((mockEntity as any).partitionsFromExtension).toBe(false);
    });

    it("should not reset when allowExtensions is false", () => {
      mockCsnEntity["@Metadata.allowExtensions"] = false;
      resetPartitionsFromExtensions(mockEntity, mockCsnEntity, mockCsnExtensions);

      // Should not clear partitions
      expect(mockEntity.partitions.deleteAll).not.toHaveBeenCalled();
    });

    it("should not reset when feature flag is disabled", () => {
      // Mock feature flag to be disabled
      global.sap.ui.getCore().getModel().getProperty.mockReturnValue(false);

      resetPartitionsFromExtensions(mockEntity, mockCsnEntity, mockCsnExtensions);

      // Should not clear partitions when feature flag is off
      expect(mockEntity.partitions.deleteAll).not.toHaveBeenCalled();
    });
  });
});
