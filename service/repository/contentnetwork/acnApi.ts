/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { basicRegisterRoutes as registerRoutes } from "@sap/deepsea-sqlutils/dist/repositoryApiRegistration/registerRoute";

import { PluginRegistry } from "@sap/deepsea-plugin-framework";
import { PermissionFillUtils, X_SAP_BOC_SCOPE_PERMISSIONS } from "@sap/deepsea-sac";
import {
  CodedError,
  blockNonTechnicalUser,
  bypassPrivilegeForService2Service,
  registerImplementation,
} from "@sap/deepsea-sqlutils";
import {
  ACN_PLUGIN,
  IAcnExportParams,
  IAcnImportParams,
  IAcnPluginApplicableParam,
  IAcnStatusResult,
  ICreateSpaceContentOutParam,
  IIESpaceDefinition,
  IJsonObject,
  IRepoRoutes,
  IRequestContext,
  ITags,
  RepositoryRouteKind,
} from "@sap/deepsea-types";
import { IResourceStatus } from "@sap/deepsea-types/dist";
import { ParamValidator, TimeUnit, runTaskWithNewContext } from "@sap/deepsea-utils";
import * as Logger from "@sap/dwc-logger";
import { Request, Response, Router } from "express";
import Status from "http-status-codes";
import { assertTechnicalToken } from "../../bdc/acnProvision/onboarding/utils";
import { UsageCollectionHandler } from "../../routes/feedback/usageCollectionHandler";
import { RepositoryObjectClient } from "../client/repositoryObjectClient";
import { IAcnPluginRegistry } from "../pluginframework/plugins/ICsnDataObject";
import { mergeI18nSourceLanguageValues } from "../pluginframework/plugins/acnImportExport";
import { IAcnDeletePackageObjectsParams } from "../types/acn";
import { PackageObjectsDeleteUtils } from "./PackageObjectsDeleteUtils";
import { deployAfterImport, getAfterAllImportJobStatus } from "./acnApiUtils";
import { checkLocalSchema } from "./acnDatabase";
import { buildImportChunkErrorResult, parsePotentialRepositoryPackageChunk, processAcnImportChunk } from "./acnImport";
import {
  JobType,
  getChangeSpaceUsageTrackingData,
  getMappingSpaceName,
  initializeJobStatusFromContext,
  isIntelligentApplicationOnboarding,
} from "./util";

const { logError, logVerbose, logDebug, logInfo, logPerformance, logWarning } = Logger.getLogger("acnApi");

function getMaxChunkSize(body: {
  data: {
    maxChunkSize: number;
  };
}) {
  let maxChunkSize = 1048576;
  if (body.data && body.data.maxChunkSize) {
    maxChunkSize = body.data.maxChunkSize;
  }
  return maxChunkSize;
}

export function registerApiImplementation(router: Router) {
  const acnApiRoutes: IRepoRoutes = {};

  acnApiRoutes["/repository/ei/resources"] = {
    routeType: RepositoryRouteKind.listResource,
    patch: true,
  };

  acnApiRoutes["/repository/ei/exports/:resourceId"] = {
    routeType: RepositoryRouteKind.exportedResource,
    post: true,
  };

  acnApiRoutes["/repository/ei/exports/:resourceId/chunks/:chunkNo"] = {
    routeType: RepositoryRouteKind.exportedChunk,
    post: true,
  };

  acnApiRoutes["/repository/ei/imports/afterAllImport"] = {
    routeType: RepositoryRouteKind.afterAllImport,
    post: true,
  };

  acnApiRoutes["/repository/ei/imports/:resourceId"] = {
    routeType: RepositoryRouteKind.importedResource,
    post: true,
  };

  acnApiRoutes["/repository/ei/imports/:resourceId/chunks/:chunkNo"] = {
    routeType: RepositoryRouteKind.importedChunk,
    post: true,
  };

  acnApiRoutes["/repository/ei/jobs/:jobId"] = {
    routeType: RepositoryRouteKind.exportImportJobs,
    get: true,
  };

  registerImplementation(RepositoryRouteKind.importedResource, {
    upsert: async function processImport(context: IRequestContext, req: Request, res: Response) {
      await blockNonTechnicalUser(context, req, res);
      await bypassPrivilegeForService2Service(context, req, res);
      const resourceId = req.params && req.params.resourceId;
      logVerbose(`importedResource called with resourceId:${resourceId}`, { context });
      const { content, mappingSpaceOptions, customParameters } = req.body;
      logDebug(`importedResource called with customParameters: ${JSON.stringify(customParameters)}`, { context });

      if (
        (await context.isFeatureFlagActive("DWC_DUMMY_SPACE_PERMISSIONS")) &&
        !context.getHttpHeader(X_SAP_BOC_SCOPE_PERMISSIONS)
      ) {
        await PermissionFillUtils.tryFillUserScopePermission(context);
      }

      const result = await RepositoryObjectClient.callAcnProcessImportResource(context, {
        content,
        resourceId,
        mappingSpaceOptions,
        customParameters,
      });
      res.status(Status.OK).send(result);
    },
  });

  registerImplementation(RepositoryRouteKind.importedChunk, {
    upsert: async function processImportChunk(context: IRequestContext, req: Request, res: Response) {
      await blockNonTechnicalUser(context, req, res);
      await bypassPrivilegeForService2Service(context, req, res);
      const {
        totalChunks,
        importOptions,
        selectedIndex,
        selection,
        cycle,
        content,
        sessionId,
        mappingSpaceOptions,
        globalPackageId,
        customParameters,
        objectDataMap,
      } = req.body;
      logDebug(`importedResource called with customParameters: ${JSON.stringify(customParameters)}`, { context });
      const { chunkNo, resourceId } = req.params;
      let hasLocalSchema = true;
      const chunkContent = content && JSON.parse(content);
      const spaceName = Object.getOwnPropertyNames(chunkContent).find((s) => s);
      // // TODO: Enhance mappingSpaceOptions with input custom data of previous imported Repo Package
      const mappingSpaceName = await getMappingSpaceName(context, mappingSpaceOptions, spaceName!);
      const spaceNameInUsing = mappingSpaceName ? mappingSpaceName : spaceName;
      let importContentRes = content;
      try {
        hasLocalSchema = await checkLocalSchema(context, spaceName!, chunkContent, mappingSpaceName);
        if (
          chunkContent &&
          hasLocalSchema &&
          (await context.isFeatureFlagActive("DWCO_MODELING_SUPPORT_SAC_SEAMLESS_PLANNING_INTEGRATION"))
        ) {
          // while import planning models, we need to adjust the external schema value according to the value of sac_internal in customer hana
          importContentRes = JSON.stringify(chunkContent);
        }
      } catch {
        hasLocalSchema = false;
        logDebug(`No Local Schema named found for ${spaceNameInUsing}`, { context });
      }
      const createSpaceContentResult: ICreateSpaceContentOutParam = {};
      const params: IAcnImportParams = {
        sessionId: ParamValidator.mandatoryParam(context, "sessionId", sessionId),
        chunkNumber: ParamValidator.mandatoryParam(context, "chunkNo", Number(chunkNo)),
        resourceId: ParamValidator.mandatoryParam(context, "resourceId", resourceId),
        totalChunks: ParamValidator.mandatoryParam(context, "totalChunks", totalChunks),
        importOptions: ParamValidator.mandatoryParam(context, "importOptions", importOptions),
        selectedIndex: ParamValidator.mandatoryParam(content, "selectedIndex", selectedIndex),
        selection,
        cycle: ParamValidator.numArray(context, cycle),
        content: ParamValidator.mandatoryParam(context, "content", importContentRes),
        hasLocalSchema,
        createSpaceContentResult,
        mappingSpaceOptions,
        globalPackageId,
        customParameters,
        objectDataMap,
      };
      // requiredApiResources is used to store the information needed to install the required data products
      // at least it has to contain:
      // - source space name (keys of the annotation requiredApiResources of the repo package)
      // - list of apiResources par space
      // maybe the best should be to have the value of the annotation requiredApiResources of the repo package
      const requiredApiResources = await parsePotentialRepositoryPackageChunk(context, params);
      if (requiredApiResources) {
        logInfo(`requiredApiResources: ${JSON.stringify(requiredApiResources)}`, { context });

        // initiate async Job
        const dpInstallJobStatus = await initializeJobStatusFromContext(
          context,
          undefined,
          JobType.acnDPInstall,
          `ImportChunk for ${params.resourceId}`
        );
        logInfo(`JobId to monitor status of DataProductImport: ${dpInstallJobStatus.id}`, { context });
        const pollingInfo = {
          location: `/repository/ei/jobs/${dpInstallJobStatus.id}`, // currently acn want to use this location with technical jwt, not the business jwt location /api/v1/jobstatus?jobId=${jobStatus.id}
          retryAfterSeconds: TimeUnit.SECONDS.toSeconds(3),
        };
        // res.setHeader("Retry-After", pollingInfo.retryAfterSeconds);
        res.set("Monitor-uri", pollingInfo.location);
        // res.setHeader("Location", pollingInfo.location);
        res.set("asyncjobid", dpInstallJobStatus.id); // ACN will check this header(case insensitive) to identify this is async response, and monitor the job status

        void runTaskWithNewContext(
          context,
          async (newContext) =>
            await processAcnImportChunk(newContext, spaceNameInUsing, params, requiredApiResources, dpInstallJobStatus)
        );

        res.status(Status.ACCEPTED).send({ status: Status.ACCEPTED });
        // res.status(Status.ACCEPTED).send({
        //   job: dpInstallJobStatus.id,
        //   pollingLocation: pollingInfo.location,
        //   retryAfterSeconds: pollingInfo.retryAfterSeconds,
        // }); // the body is not used by ACN now
      } else {
        try {
          const result = await processAcnImportChunk(context, spaceNameInUsing, params);
          res.status(Status.OK).send(result);
        } catch (err) {
          res
            .status(Status.OK)
            .send(
              buildImportChunkErrorResult(
                context,
                err,
                createSpaceContentResult,
                spaceNameInUsing!,
                params.importOptions
              )
            );
        }
      }
    },
    runWithTimeout: {
      upsert: 180,
    },
  });

  registerImplementation(RepositoryRouteKind.exportedResource, {
    upsert: async function processExport(context: IRequestContext, req: Request, res: Response) {
      await blockNonTechnicalUser(context, req, res);
      await bypassPrivilegeForService2Service(context, req, res);
      const resourceId = req.params && req.params.resourceId;
      const maxChunkSize = getMaxChunkSize(req.body);
      logVerbose(`export called with resourceId:${resourceId}`, { context });

      const result = await RepositoryObjectClient.callAcnProcessExportResource(context, { resourceId, maxChunkSize });
      res.status(Status.OK).send(result);
      return;
    },
  });

  registerImplementation(RepositoryRouteKind.afterAllImport, {
    upsert: async function processAfterAllImport(context: IRequestContext, req: Request, res: Response) {
      // create job status for deployment
      const deploymentJobStatus = await initializeJobStatusFromContext(
        context,
        undefined,
        JobType.acnImportAfterAllImport,
        "Deploy After Import"
      );
      logInfo(`JobId to monitor status of afterAllImport: ${deploymentJobStatus.id}`, { context });
      const pollingInfo = {
        location: `/repository/ei/jobs/${deploymentJobStatus.id}`, // currently acn want to use this location with technical jwt, not the business jwt location /api/v1/jobstatus?jobId=${jobStatus.id}
        retryAfterSeconds: TimeUnit.SECONDS.toSeconds(3),
      };
      // res.setHeader("Retry-After", pollingInfo.retryAfterSeconds);
      res.set("Monitor-uri", pollingInfo.location);
      // res.setHeader("Location", pollingInfo.location);

      // set asyncjobId to monitor the status of afterAllImport
      if (await isIntelligentApplicationOnboarding(context)) {
        await bypassPrivilegeForService2Service(context, req, res);
        res.set("asyncjobid", deploymentJobStatus.id); // ACN will check this header(case insensitive) to identify this is async response, and monitor the job status
      }

      if (
        (await context.isFeatureFlagActive("DWC_DUMMY_SPACE_PERMISSIONS")) &&
        !context.getHttpHeader(X_SAP_BOC_SCOPE_PERMISSIONS)
      ) {
        // Retrieve scope permissions for impersonated user and request to fill scope permission on header
        await PermissionFillUtils.tryFillUserScopePermission(context, true /** fillHttpHeader */);
      }

      void runTaskWithNewContext(context, async (newContext) => {
        // launch mass deployment
        await deployAfterImport(newContext, req, res, deploymentJobStatus);
        const resourcesStatus: IResourceStatus[] = req.body?.resourcesStatus;
        const customParameters: { [prop: string]: unknown } = req.body?.customParameters;
        const mappingSpaceOptions: string = req.body?.mappingSpaceOptions;
        logDebug(`importedResource called with customParameters: ${JSON.stringify(customParameters)}`, { context });

        await RepositoryObjectClient.callAcnProcessAfterAllImport(newContext, {
          resourcesStatus,
          customParameters,
          mappingSpaceOptions,
        });
        if (resourcesStatus) {
          // get sessionId from first status
          // const isWithSessionId = importStatus.length && importStatus.find((is) => is.sessionId);
          // const changelistId = (isWithSessionId as IResourceStatus)?.sessionId ?? "";
          // find registered plugin if any
          const plugins = PluginRegistry.getPluginList(ACN_PLUGIN) as IAcnPluginRegistry[];
          const acnPlugins = plugins.filter((p) => p.afterAllImport && p.isApplicable({ resourcesStatus }));
          for (const acnPlugin of acnPlugins) {
            await acnPlugin.afterAllImport!(newContext, req);
          }
        }
        // send usage tracking data to change space
        void UsageCollectionHandler.sendUsageData(context, getChangeSpaceUsageTrackingData(req)).catch((err) => {
          logWarning(`Error during sending usage tracking data to change space: ${err.message}`, { context });
        });
      });
      res.status(Status.ACCEPTED).send({ status: Status.ACCEPTED });
      // res.status(Status.ACCEPTED).send({
      //   job: deploymentJobStatus.id,
      //   pollingLocation: pollingInfo.location,
      //   retryAfterSeconds: pollingInfo.retryAfterSeconds,
      // }); // the body is not used by ACN now);
    },
  });

  registerImplementation(RepositoryRouteKind.exportedChunk, {
    upsert: async function processExportChunk(context: IRequestContext, req: Request, res: Response) {
      await blockNonTechnicalUser(context, req, res);
      await bypassPrivilegeForService2Service(context, req, res);
      const { maxChunkSize } = req.body;
      const { chunkNo, resourceId } = req.params;

      const params: IAcnExportParams = {
        maxChunkSize,
        chunkNumber: Number(chunkNo),
        resourceId: ParamValidator.mandatoryParam(context, "resourceId", resourceId),
      };

      try {
        const resultAcnExportChunk = await RepositoryObjectClient.callAcnProcessExportChunk(context, params);
        const message = resultAcnExportChunk?.message;
        // check the status of import
        if (resultAcnExportChunk?.status === "FAILED" || resultAcnExportChunk?.status === "WARNING") {
          logInfo(`Failed to export resourceId:${resourceId} with failure message ${message}`, { context });
          res.status(Status.OK).send({
            status: resultAcnExportChunk?.status,
            message,
          });
          return;
        }

        const content = resultAcnExportChunk?.content;
        const oContent = content && JSON.parse(content);
        const spaceName = oContent ? Object.getOwnPropertyNames(oContent).find((s) => s) : undefined;
        const spaceContent = spaceName ? (oContent[spaceName] as IIESpaceDefinition) : undefined;
        const objName = spaceContent?.objects && Object.getOwnPropertyNames(spaceContent.objects).find((s) => s);

        if (objName) {
          // Adjust content with plugin
          const acnPlugins = PluginRegistry.getPluginList(ACN_PLUGIN) as IAcnPluginRegistry[];

          const applicableParams = {
            spaceName: spaceName!,
            objName,
            content: spaceContent.objects![objName],
          };
          const plugins = acnPlugins.filter((p) => p.exportChunkAdjustContent && p.isApplicable(applicableParams));
          const pluginResult: IAcnStatusResult = { status: resultAcnExportChunk?.status };
          for (const plugin of plugins) {
            logInfo(`Adjust content to export for ${spaceName}.${objName} with ACN plugin '${plugin.getName()}'`, {
              context,
            });
            const pluginTime = new Date();
            try {
              const oldContent = spaceContent.objects![objName];
              spaceContent.objects![objName] = await plugin.exportChunkAdjustContent!(
                context,
                oldContent as IJsonObject,
                applicableParams as IAcnPluginApplicableParam,
                pluginResult
              );
            } catch (error) {
              logError(
                [
                  `Exception while adjusting content to export for ${spaceName}.${objName} with ACN plugin '${plugin.getName()}'`,
                  error as Error,
                ],
                { context }
              );
              throw error;
            } finally {
              logPerformance(
                pluginTime,
                `End adjusting content to export for ${spaceName}.${objName} with ACN plugin '${plugin.getName()}'`,
                { context, warningThreshold: 10000 }
              );
            }
          }

          // Update i18n?
          const spaceParams = {
            folderNames: spaceName,
            kinds: [context.getNamespaceKind()],
            details: ["qualified_name", "sourceLanguage"],
            replaceI18n: false,
          };
          const [mainObjectToExportSpace] = await RepositoryObjectClient.getObject(context, spaceParams);
          const sourceLanguage = mainObjectToExportSpace.properties.sourceLanguage;
          const spaceIdForI18n = mainObjectToExportSpace.id;
          let i18nSection: ITags | undefined = spaceContent.objects![objName].i18n || {};
          if (sourceLanguage && spaceIdForI18n && i18nSection) {
            if (spaceContent.i18n) {
              i18nSection = mergeI18nSourceLanguageValues(spaceContent.i18n, i18nSection, sourceLanguage);
            }
            if (i18nSection && i18nSection[sourceLanguage]) {
              i18nSection = await RepositoryObjectClient.getI18nSectionWithAllLanguages(context, {
                spaceId: spaceName as string,
                i18nSection: i18nSection as ITags,
                sourceLanguage,
              });
              spaceContent.i18n = i18nSection;
            }
            delete spaceContent.objects![objName].i18n;
          }
          resultAcnExportChunk!.content = JSON.stringify(oContent);
          resultAcnExportChunk!.status = pluginResult.status || resultAcnExportChunk!.status;
          resultAcnExportChunk!.message = pluginResult.message || resultAcnExportChunk!.message;
        }
        res.status(Status.OK).send(resultAcnExportChunk);
      } catch (err) {
        const message = err.message;
        logError(`${message}`, { context });
        res.status(Status.OK).send({
          status: "FAILED",
          message,
        });
      }
    },
    runWithTimeout: {
      upsert: 180,
    },
  });

  /**
   * /repository/ei/jobs/:jobId
   * A route used by ACN to track the status of an export/import operation processed by DataSphere.
   */
  registerImplementation(RepositoryRouteKind.exportImportJobs, {
    // /repository/ei/jobs/:jobId , see also ACN API documentation: https://github.wdf.sap.corp/orca/orca-contentmanager-service/blob/master/code/src/main/resources/definitions/exportImportToService.md#jobstatus
    get: async function getExportImportJobs(context: IRequestContext, req: Request, res: Response) {
      logInfo("[getAfterAllImportJobStatus] Start to get job status for after all import process", { context });
      await assertTechnicalToken(req, res);
      // get the job id from the request
      // parameter jobId is mandatory else throw error
      const jobId = ParamValidator.mandatoryParam(context, "jobId", ParamValidator.string(context, req.params.jobId));
      logInfo(`Job id to track the status of an export/import operation: ${jobId}`, { context });
      // fetch the job status from the job id
      try {
        await getAfterAllImportJobStatus(context, res, jobId);
      } catch (error) {
        logError(["[getAfterAllImportJobStatus] Failed to get job Status for after all import process", error], {
          context: req.context,
        });
        res.status(Status.BAD_REQUEST).send({ message: error.message });
      }
    },
    runWithTimeout: {
      upsert: 180,
    },
  });

  /** patch repository/ei/resources is used by acn to offboarding bdc, delete objects in this space in the correct order
   *  see also https://wiki.one.int.sap/wiki/display/OrcaDev/Package+off-boarding
   */
  registerImplementation(RepositoryRouteKind.listResource, {
    patch: async function deletePackageObjects(context: IRequestContext, req: Request, res: Response) {
      logInfo(`[deletePackageObjects] [patch /ei/resources] request received to delete package objects `, { context });

      if (!context.tenantId) {
        const errorMessage = `No tenant id is provided in the header`;
        logError(errorMessage, { context });
        throw new CodedError("NoTenantId", errorMessage, Status.BAD_REQUEST);
      }
      if (!(await context.isFeatureFlagActive("DWCO_BDC_GA"))) {
        const errorMessage = `Featureflag DWCO_BDC_GA is not enabled for tenant ${context.tenantId}}`;
        logError(errorMessage, { context });
        throw new CodedError("FFBdcOffboardingNotEnabled", errorMessage, Status.INTERNAL_SERVER_ERROR);
      }
      await blockNonTechnicalUser(context, req, res);
      await bypassPrivilegeForService2Service(context, req, res); // for bdc offboarding
      const deleteParams: IAcnDeletePackageObjectsParams =
        await PackageObjectsDeleteUtils.validateDeletePackageObjectsRequestPayload(context, req.body);
      try {
        const async = req.query.async !== "false" ? true : false; // default empty is async
        if (!async) {
          await PackageObjectsDeleteUtils.deletePackageObjects(context, deleteParams);
          res.status(Status.OK).send({ message: "Package objects deleted successfully" });
        } else {
          const { job, pollingInfo } = await PackageObjectsDeleteUtils.deletePackageObjectsWithJob(
            context,
            deleteParams
          );
          res.setHeader("Retry-After", pollingInfo.retryAfterSeconds || TimeUnit.SECONDS.toSeconds(3));
          res.set("Monitor-uri", pollingInfo.location);
          res.setHeader("Location", pollingInfo.location);
          res.set("asyncJobId", job.id); // ACN will check this header(case insensitive) to identify this is async response, and monitor the job status
          res.status(Status.ACCEPTED).send({
            job: job.id,
            pollingLocation: pollingInfo.location,
            retryAfterSeconds: pollingInfo.retryAfterSeconds,
          }); // the body is not used by ACN now
        }
      } catch (err) {
        logError([`Patch ei/resources error when delete acn package objects: ${err?.message}`, err], { context });
        throw new CodedError("deletePackageObjectsError", err, Status.INTERNAL_SERVER_ERROR);
      }
    },
    runWithTimeout: {
      upsert: 180,
    },
  });

  registerRoutes(router, acnApiRoutes);
}
