/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import {
  IAcnImportParams,
  ICreateSpaceContentOutParam,
  IIESpaceDefinition,
  IJobStatus,
  IJsonObject,
  IRequestContext,
  JsonValue,
  ObjectKind,
  StatusType,
} from "@sap/deepsea-types";
import { UniqueObjectIdentifier } from "@sap/deepsea-utils";
import * as Logger from "@sap/dwc-logger";
import { checkMultiBdcInstanceEnabled } from "../../bdc/acnProvision/bdcMultiInstance";
import { RepositoryObjectClient } from "../client/repositoryObjectClient";
import {
  ISpaceNameToApiResources,
  ImportConfiguration,
  fetchUclSharedConnection,
  installDataProductsForSpace,
} from "../importmanager/src/dataProductInstallUtils";
import { checkImportPostProcess, handleError } from "./acnApiUtils";
import { updateJobStatusValue } from "./util";

interface SpaceMappingConfig extends IJsonObject {
  spaceMapping: {
    [sourceSpaceName: string]: string;
  };
  [key: string]: any; // Index signature for type 'string'
}

interface IAcnChunkContent {
  [spaceName: string]: IIESpaceDefinition;
}

const { logError, logInfo, logPerformance } = Logger.getLogger("acnApi");

/**
 * Implement the logic to process the ACN import chunk endpoint
 * @param context
 * @param targetSpaceName name of the target space where the content will be imported
 * @param params parameters for the import
 * @param requiredApiResources in case of Repository Package depending on an Ingestion Space, the required API resources of the repo package
 * @param jobStatus if the import is executed asynchronously, the job status to update
 * @returns result to include in the endpoint response
 */
export async function processAcnImportChunk(
  context: IRequestContext,
  targetSpaceName: string | undefined,
  params: IAcnImportParams | undefined,
  requiredApiResources?: ISpaceNameToApiResources,
  jobStatus?: IJobStatus // what to do with this jobStatus?
  // need also the ucl connection, where to retrieve it?
): Promise<IJsonObject | undefined> {
  let result: IJsonObject | undefined;
  if (params) {
    try {
      let objectData: SpaceMappingConfig | undefined;
      if (targetSpaceName && requiredApiResources && Object.keys(requiredApiResources).length > 0 && jobStatus) {
        const uclSystemTenantId = params.customParameters?.uclSystemTenantId as string;
        if (uclSystemTenantId || (await context.isFeatureFlagActive("DWCO_BDC_REPOSITORY_DP_INTENT_CUSTOMER"))) {
          // log the required API resources
          const message = `requiredApiResources && uclSystemTenantId: ${JSON.stringify(
            requiredApiResources
          )}${uclSystemTenantId}`;
          logInfo(`[processAcnImportChunk] ${message}`, { context });

          // - call deepsea acn import chunk with dryRun=true in order to check usual ACN import errors and collect space mapping info and custom parameters
          const start = new Date();
          params.dryRun = true;
          // add performance logging
          const acnImportDryRunResult = await RepositoryObjectClient.callAcnProcessImportChunk(context, params);
          // log performance
          logPerformance(
            start,
            `[processAcnImportChunk] callAcnProcessImportChunk with dryRun ${acnImportDryRunResult?.status}`,
            { context }
          );
          // remove dryRun flag
          params.dryRun = false;

          if (acnImportDryRunResult?.statusResults?.status === "SUCCESS") {
            // install required data products
            objectData = await installRequiredDataProducts(
              context,
              targetSpaceName,
              params,
              requiredApiResources,
              jobStatus
            );

            // log performance
            const message = `installRequiredDataProducts ${JSON.stringify(objectData)}`;
            logPerformance(start, `[processAcnImportChunk] ${message}`, { context });
          }
        }
      }

      const acnImportResult = await RepositoryObjectClient.callAcnProcessImportChunk(context, params);
      if (acnImportResult) {
        params.createSpaceContentResult.currentObjectName = acnImportResult.currentObjectName;
        params.createSpaceContentResult.remoteEntityName = acnImportResult.remoteEntityName;
        params.createSpaceContentResult.remoteConnectionName = acnImportResult.remoteConnectionName;
        params.createSpaceContentResult.bRemoteTable = acnImportResult.bRemoteTable;
      }

      const chunkContentAfterImport = acnImportResult?.oContent;
      const message = acnImportResult?.message;
      // check the status of import
      if (acnImportResult?.failureStatus || acnImportResult?.status === "FAILED") {
        logInfo(`Failed to import in space ${targetSpaceName} with failure message ${message}`, { context });
        result = {
          status: acnImportResult.status,
          message,
        } as IJsonObject;
      }
      // import content function()
      if (chunkContentAfterImport) {
        if (targetSpaceName) {
          // check if importing is finished
          result = await checkImportPostProcess(
            context,
            params.createSpaceContentResult,
            chunkContentAfterImport,
            targetSpaceName,
            acnImportResult
          );
          if (objectData) {
            result.objectData = objectData;
          }
        }
      }
      const isBdcMultiInstance = await checkMultiBdcInstanceEnabled(context, {
        uclSystemAlias: params?.customParameters?.uclSystemAlias,
        uclSystemBusinessName: params?.customParameters?.uclSystemBusinessName,
      });
      if (acnImportResult?.customResponse && isBdcMultiInstance && result) {
        result.customResponse = acnImportResult.customResponse as JsonValue;
      }

      if (jobStatus) {
        await updateJobStatusValue(context, jobStatus, StatusType.Succeed, message, result);
      }
      return result;
    } catch (err) {
      if (jobStatus) {
        const result = buildImportChunkErrorResult(
          context,
          err,
          params.createSpaceContentResult,
          targetSpaceName!,
          params.importOptions
        );
        await updateJobStatusValue(context, jobStatus, StatusType.Succeed, result.message, result);
      } else {
        // let the caller handle the error
        throw err;
      }
    }
  }
}

/**
 * Installs the required data products for a given target space.
 *
 * @param context - The request context.
 * @param targetSpaceName - The name of the target space where data products will be installed.
 * @param params - The parameters for the ACN import, including custom parameters.
 * @param requiredApiResources - A mapping of space names to their required API resources.
 * @param jobStatus - The status of the job being executed.
 * @returns A promise that resolves to a `SpaceMappingConfig` object containing the space mapping information, or `undefined` if the installation fails.
 *
 * @remarks
 * This function performs the following steps:
 * 1. Validates the input parameters.
 * 2. Logs the required API resources and UCL system tenant ID.
 * 3. Updates the job status to indicate the installation process has started.
 * 4. Fetches the UCL shared connections.
 * 5. Logs the performance of fetching UCL shared connections.
 * 6. Iterates over the fetched UCL shared connections and installs data products for each connection.
 * 7. Logs the performance of installing data products.
 * 8. Collects and returns the space mapping information.
 * 9. Logs an error and throws an exception if fetching UCL shared connections fails.
 */
export async function installRequiredDataProducts(
  context: IRequestContext,
  targetSpaceName: string,
  params: IAcnImportParams,
  spaceToRequiredApiResource: ISpaceNameToApiResources,
  jobStatus: IJobStatus
): Promise<SpaceMappingConfig | undefined> {
  let objectData: SpaceMappingConfig | undefined;
  const uclSystemTenantId = params.customParameters?.uclSystemTenantId as string;
  if (
    targetSpaceName &&
    (uclSystemTenantId || (await context.isFeatureFlagActive("DWCO_BDC_REPOSITORY_DP_INTENT_CUSTOMER"))) &&
    spaceToRequiredApiResource &&
    Object.keys(spaceToRequiredApiResource).length > 0 &&
    jobStatus
  ) {
    // log the required API resources
    const message = `requiredApiResources && uclSystemTenantId: ${JSON.stringify(
      spaceToRequiredApiResource
    )}${uclSystemTenantId}`;
    logInfo(`[installRequiredDataProducts] ${message}`, { context });

    const start = new Date();
    // - call DP install
    // get ucl shared connection
    const uclSharedConnections = await fetchUclSharedConnection(
      context,
      { targetSpaceName, ...params },
      spaceToRequiredApiResource
    );

    // log performance
    logPerformance(start, `[installRequiredDataProducts] fetchUclSharedConnection ${uclSharedConnections.length}`, {
      context,
    });

    // get the target space UUID from the target space name
    if (uclSharedConnections.length > 0) {
      // sharedUclConnections is an array of shared UCL connections: should be only one element in the array in this user story
      for (let i = 0; i < uclSharedConnections.length; i++) {
        try {
          const uclSharedConnection = uclSharedConnections[i];
          const apiResourceEntries = Object.entries(spaceToRequiredApiResource)[i];
          const sourceSpaceName = apiResourceEntries[0];
          const requiredApiResources = apiResourceEntries[1];
          // install data products for the space
          const installParams: ImportConfiguration = {
            targetSpaceName,
            uclSystemTenantId,
            requiredApiResources,
            uclSharedConnection,
            shouldDeploySpace: !!(i === 0 && uclSystemTenantId), // deploy the target space only for the first UCL connection and when uclSystemTenantId is provided (Intelligent App case)
            jobStatus,
          };
          // install data products for the space
          const importResult = await installDataProductsForSpace(context, installParams);
          // log performance
          logPerformance(start, `[installRequiredDataProducts] installDataProductsForSpace`, { context });
          if (importResult?.ingestionSpace) {
            objectData = objectData ?? { spaceMapping: {} };
            // collect space mapping info
            if (sourceSpaceName) {
              objectData.spaceMapping[sourceSpaceName] = importResult.ingestionSpace;
            }
            if (!(await context.isFeatureFlagActive("DWCO_BDC_REPOSITORY_DP_INTENT_CUSTOMER"))) {
              // break the loop if the data products are installed successfully
              // can be removed if we want to install data products for all UCL connections but we need to adjust objectData
              break;
            }
          }
        } catch (e) {
          // log error
          logError(`[installRequiredDataProducts] ${e}`, { context });
          if (i === uclSharedConnections.length - 1) {
            throw e;
          }
        }
      }
    } else {
      // log error
      const uclFormationId = params.customParameters?.uclFormationId as string;
      const message = `Failed to fetch UCL shared connections created for UCL formation ID ${uclFormationId} and UCL system tenant ID ${uclSystemTenantId}`;
      logError(`[installRequiredDataProducts] ${message}`, { context });
      throw new Error(`${message}`);
    }
  }
  return objectData;
}

/**
 * if the chunk to import corresponds to a Repository Package
 * @param params parameters for the import
 *  parse it to detect if it depends on Data Products
 * @returns TODO: specify the result
 */
export async function parsePotentialRepositoryPackageChunk(
  context: IRequestContext,
  params: IAcnImportParams
): Promise<ISpaceNameToApiResources | undefined> {
  const dpIntentEnabled = await context.isFeatureFlagActive("DWCO_BDC_REPOSITORY_DP_INTENT");
  if (dpIntentEnabled && params.resourceId) {
    // check resourceId if it corresponds to Repo Package
    const { kind } = UniqueObjectIdentifier.parseObjectPathIdentifier(params.resourceId);
    if (kind === (ObjectKind.repositoryPackage as string)) {
      // TODO: parse content to see if it requires Data Products
      // - if true return the required data products info
      return getRequiredApiOrdIds(params);
      // return {} as IJsonObject;
    }
  }
  return undefined;
}

/**
 * get the required API resources of the repo package
 * @param params parameters for the import
 * @returns the required API resources of the repo package
 */
function getRequiredApiOrdIds(params: IAcnImportParams): ISpaceNameToApiResources | undefined {
  if (!params.content) {
    return undefined;
  }
  const oContent = JSON.parse(params.content) as IAcnChunkContent;
  for (const [, spaceContent] of Object.entries(oContent)) {
    if (spaceContent?.objects) {
      for (const [objectName, objectContent] of Object.entries(spaceContent.objects)) {
        const requiredApiResources = (
          objectContent?.repositoryPackages?.[objectName] as { requiredApiResources: ISpaceNameToApiResources }
        )?.requiredApiResources;
        if (requiredApiResources && Object.keys(requiredApiResources).length) {
          /*
              requiredApiResources: {
                S4_ING: {
                  applicationType: “S4”
                  connectionType: “HDLFS”
                  formationType: “BDC”
                  apiResources: [“sap.s4:dataProduct:SalesOrder”]
                }
                SFSF_ING: {
                  applicationType: “SFSF”
                  connectionType: “HDLFS”
                  formationType: “BDC”
                  apiResources: [“sap.sfsf:dataProduct:Invoices”, ...]
                }
                */
          return requiredApiResources;
        }
      }
    }
  }
  return undefined;
}

/**
 * build the result to return in case of error during the import chunk
 * @param context
 * @param err thrown error
 * @param createSpaceContentResult result of the import chunk
 * @param targetSpaceName name of the target space where the content should be imported
 * @param importOptions options used for the import
 * @returns
 */
export function buildImportChunkErrorResult(
  context: IRequestContext,
  err: any,
  createSpaceContentResult: ICreateSpaceContentOutParam,
  targetSpaceName: string,
  importOptions: string
): { status: string; message: string } {
  const message = handleError(err, createSpaceContentResult, targetSpaceName);
  const logMessage = message ? `${message}: ${err.message}` : err.message;
  logError([`${logMessage} [${importOptions}]: ${err.message}`, err], { context });
  return {
    status: "FAILED",
    message: message ?? "Failed to import",
  };
}
