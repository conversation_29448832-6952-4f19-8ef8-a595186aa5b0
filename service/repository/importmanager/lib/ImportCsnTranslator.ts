/** @format */

export class ImportCsnTranslator {
  // constructor() {}
  public static get() {
    return new ImportCsnTranslator();
  }

  public modifyAnnotationsInCSN(csnDefinition: any) {
    // ------
    // moved from Entity Processor to CSN Translator
    // this is a good example how the methods in ImportCsnTranslator should be built
    // - remove modeling pattern array
    // - remove empty keys in delta mappings
    // ------

    // if modeling pattern is defined as an array -> remove the array
    if (csnDefinition && csnDefinition["@ObjectModel.modelingPattern"]) {
      const modelingPattern: any = csnDefinition["@ObjectModel.modelingPattern"];
      if (Array.isArray(modelingPattern) && modelingPattern.length > 0) {
        csnDefinition["@ObjectModel.modelingPattern"] = modelingPattern[0];
      }
    }

    // workaround for bug in S/4 2302: if @Analytics.dataExtraction.delta.changeDataCapture.mapping has an empty key "" -> fix it
    if (csnDefinition && csnDefinition["@Analytics.dataExtraction.delta.changeDataCapture.mapping"]) {
      const deltaMappings = csnDefinition["@Analytics.dataExtraction.delta.changeDataCapture.mapping"];
      let deleteBlock = false;

      if (deltaMappings.length) {
        for (const d of deltaMappings) {
          const keys = Object.keys(d);
          if (keys.findIndex((k) => !k) !== -1) {
            deleteBlock = true;
            break;
          }
        }
      }
      if (deleteBlock) {
        delete csnDefinition["@Analytics.dataExtraction.delta.changeDataCapture.mapping"];
      }
    }
  }

  public addAnnoAnalyticsMeasureTypeBase(csnDefinition: ICsnDefinition) {
    // ------
    // checks for aggregation annotations:
    //  - ensures that the definition will be built in format @Aggregation.default = {"#": <aggregationMethod> }
    //  - the obsolete annotation @DefaultAggregation will be replaced as above
    //  - additionally in all cases the annotation @Analytics.Details.measureType = "BASE" will be added
    // ------
    if (csnDefinition && csnDefinition.elements) {
      for (const element of Object.values(csnDefinition.elements)) {
        if (element && ((element as any)["@Aggregation.default"] || (element as any)["@DefaultAggregation"])) {
          let aggregationValue = (element as any)["@Aggregation.default"]
            ? (element as any)["@Aggregation.default"]
            : (element as any)["@DefaultAggregation"];
          if (typeof aggregationValue === "string") {
            // if aggregationValue starts with # -> remove it
            if (aggregationValue.startsWith("#")) {
              aggregationValue = aggregationValue.substring(1);
            }
            // change value of the annotation of the aggregation default to object
            (element as any)["@Aggregation.default"] = {
              "#": aggregationValue,
            };
            if ((element as any)["@DefaultAggregation"]) {
              delete (element as any)["@DefaultAggregation"];
            }
          } else if (typeof aggregationValue === "object") {
            // also if the value is an object, change annotation to the correct format "@Aggregation.default"
            if ((element as any)["@DefaultAggregation"] && !(element as any)["@Aggregation.default"]) {
              (element as any)["@Aggregation.default"] = aggregationValue;
              delete (element as any)["@DefaultAggregation"];
            }
          }
          // add @Analytics.Details.measureType BASE
          if (
            !(
              (element as any)["@AnalyticsDetails.measureType"] &&
              (element as any)["@AnalyticsDetails.measureType"]["#"] === "BASE"
            )
          ) {
            (element as any)["@AnalyticsDetails.measureType"] = {
              "#": "BASE",
            };
          }
        }
      }
    }
  }

  public addNotNullToKeys(csnDefinition: ICsnDefinition) {
    // ------
    // checks for key fields
    // add to the key fields the property:  "notNull": true
    // ------
    if (csnDefinition && csnDefinition.elements) {
      for (const element of Object.values(csnDefinition.elements)) {
        // check if element is a key field
        if (element && (element as any).key && (element as any).key === true) {
          // add for the key field the property notNull if this is not available

          if (!(element as any).notNull) {
            (element as any).notNull = true;
          }
        }
      }
    }
  }

  public addAnnoSemanticsUoMAndCurrencyCodeTrue(csnDefinition: ICsnDefinition) {
    // ------
    // check for elements with Semantics.amount.currencyCode and Semantics.quantity.unitOfMeasure
    // for amount and currency:
    //  - ensures that the currency code always gets annotation @Semantics.currencyCode = true
    //  - ensures that the amount field references the currency code field
    //    in format @Semantics.amount.currencyCode = { "=": <currencyCodeFieldName> }
    // for quantity and unit of measure:
    //  - ensures that the quantity unit of measure always gets annotation @Semantics.unitOfMeasure = true
    //  - ensures that the quantity field references the unit of measure
    //    in format @Semantics.quantity.unitOfMeasure = { "=": <unitOfMeasure> }
    // ------
    if (csnDefinition && csnDefinition.elements) {
      for (const element of Object.values(csnDefinition.elements)) {
        if (element && (element as any)["@Semantics.quantity.unitOfMeasure"]) {
          // take the field which is the quantity unit of  of measure
          let fieldUoMName = (element as any)["@Semantics.quantity.unitOfMeasure"];
          if (typeof fieldUoMName === "object") {
            if (fieldUoMName["="] && typeof fieldUoMName["="] === "string") {
              fieldUoMName = fieldUoMName["="];
            }
          } else if (typeof fieldUoMName === "string") {
            // change value of the annotation of the quantity to object
            (element as any)["@Semantics.quantity.unitOfMeasure"] = {
              "=": fieldUoMName,
            };
          }
          // search for the field fieldUoMName in the elements list
          if (typeof fieldUoMName === "string" && csnDefinition.elements[fieldUoMName]) {
            if (
              !(
                (csnDefinition.elements[fieldUoMName] as any)["@Semantics.unitOfMeasure"] &&
                (csnDefinition.elements[fieldUoMName] as any)["@Semantics.unitOfMeasure"] === true
              )
            ) {
              // add annotation @Semantics.unitOfMeasure: true to the fieldUoM if not available
              (csnDefinition.elements[fieldUoMName] as any)["@Semantics.unitOfMeasure"] = true;
            }
          }
        } else if (element && (element as any)["@Semantics.amount.currencyCode"]) {
          // take the field which is the currency code
          let fieldCurrencyCodeName = (element as any)["@Semantics.amount.currencyCode"];
          if (typeof fieldCurrencyCodeName === "object") {
            if (fieldCurrencyCodeName["="] && typeof fieldCurrencyCodeName["="] === "string") {
              fieldCurrencyCodeName = fieldCurrencyCodeName["="];
            }
          } else if (typeof fieldCurrencyCodeName === "string") {
            // change value of the annotation of the amount to object
            (element as any)["@Semantics.amount.currencyCode"] = {
              "=": fieldCurrencyCodeName,
            };
          }
          // search for the currency code field in the elements list
          if (typeof fieldCurrencyCodeName === "string" && csnDefinition.elements[fieldCurrencyCodeName]) {
            if (
              !(
                (csnDefinition.elements[fieldCurrencyCodeName] as any)["@Semantics.currencyCode"] &&
                (csnDefinition.elements[fieldCurrencyCodeName] as any)["@Semantics.currencyCode"] === true
              )
            ) {
              // add annotation @Semantics.unitOfMeasure: true to the currency code field if not available
              (csnDefinition.elements[fieldCurrencyCodeName] as any)["@Semantics.currencyCode"] = true;
            }
          }
        }
      }
    }
  }

  public addDataStructureToAnnoObjectModelSupportCapabilities(csnDefinition: ICsnDefinition) {
    // initial logic: for modelingPattern = ANALYTICAL_FACT --> add supported capability DATA_STRUCTURE

    // check if entity with modeling pattern ANALYTICAL_CUBE
    // --> if yes, exchange modeling pattern with FACT
    // --> add supported capability DATA_STRUCTURE
    // --> remove supported capability ANALYTICAL_PROVIDER
    if (
      csnDefinition &&
      (csnDefinition as any)["@ObjectModel.modelingPattern"] &&
      (csnDefinition as any)["@ObjectModel.modelingPattern"]["#"] &&
      (csnDefinition as any)["@ObjectModel.modelingPattern"]["#"] === "ANALYTICAL_CUBE"
    ) {
      (csnDefinition as any)["@ObjectModel.modelingPattern"]["#"] = "ANALYTICAL_FACT";
    }

    // check for entities with modeling pattern ANALYTICAL_FACT
    // --> add the supported capability DATA_STRUCTURE to the supported capabilities array
    // --> check for entities with modeling pattern ANALYTICAL_FACT if the supported capability ANALYTICAL_PROVIDER exists
    // --> if yes, delete the supported capability ANALYTICAL_PROVIDER --> it is not needed for SAC
    if (
      csnDefinition &&
      (csnDefinition as any)["@ObjectModel.modelingPattern"] &&
      (csnDefinition as any)["@ObjectModel.modelingPattern"]["#"] &&
      (csnDefinition as any)["@ObjectModel.modelingPattern"]["#"] === "ANALYTICAL_FACT"
    ) {
      // check if anno @objectModel.supportedCapabilities exists as array
      if (
        !(csnDefinition as any)["@ObjectModel.supportedCapabilities"] ||
        !Array.isArray((csnDefinition as any)["@ObjectModel.supportedCapabilities"])
      ) {
        // array does not exist, initialize it
        (csnDefinition as any)["@ObjectModel.supportedCapabilities"] = [];
      }
      if (
        !(csnDefinition as any)["@ObjectModel.supportedCapabilities"].find(
          (item: any) => item["#"] === "DATA_STRUCTURE"
        )
      ) {
        // add supported capability
        (csnDefinition as any)["@ObjectModel.supportedCapabilities"].push({
          "#": "DATA_STRUCTURE",
        });
      }

      // check if the FACT has ObjectModel.supportedCapabilities = #ANALYTICAL_PROVIDER
      // if yes, delete #ANALYTICAL_PROVIDER
      if (
        (csnDefinition as any)["@ObjectModel.supportedCapabilities"].find(
          (item: any) => item["#"] === "ANALYTICAL_PROVIDER"
        )
      ) {
        // delete supported capability = ANALYTICAL_PROVIDER
        (csnDefinition as any)["@ObjectModel.supportedCapabilities"] = (csnDefinition as any)[
          "@ObjectModel.supportedCapabilities"
        ].filter((item: any) => item["#"] !== "ANALYTICAL_PROVIDER");
      }
    }
  }

  public modifySyntaxOfArraysWithString(csnDefinition: ICsnDefinition) {
    // method checks if the CSN definitions contain arrays of strings
    // eg. hierarchy: { parent: ["ParentNode"], child: ["HierarchyNode"] }
    // and converts them to arrays of objects: { parent: [{ "=" : value }],.. }
    // the CSN definitions are adjusted recursively
    this.recursiveCheckForArraysOfStrings(csnDefinition);
  }

  private recursiveCheckForArraysOfStrings(input: any, isInAnnoContext?: boolean, isElement?: boolean): any {
    // the method only checks @annotations on header level
    // and @annotations on elements level
    if (input && Array.isArray(input) && isInAnnoContext) {
      // process arrays if they are in an annotation context
      if (input.every((value) => typeof value === "string")) {
        // modify array:
        // convert string "value" to object { "=" : value }
        input = input.map((value) => ({ "=": value }));
      } else if (input.length) {
        // array: process children if relevant
        input = input.map((value) => {
          return value && (typeof value === "object" || Array.isArray(value))
            ? this.recursiveCheckForArraysOfStrings(value, isInAnnoContext)
            : value;
        });
      }
    } else if (input && typeof input === "object") {
      // object: process children if relevant
      // either annotations or elements which can contain annotations
      for (const key of Object.keys(input)) {
        input[key] =
          input[key] &&
          ((key.startsWith("@") && !key.startsWith("@DataWarehouse")) || // it is an annotation from backend
            isInAnnoContext || // it is in an annotation context
            key === "elements" || // it is the elements definition of an entity which can contain annotations
            isElement) &&
          (typeof input[key] === "object" || Array.isArray(input[key]))
            ? this.recursiveCheckForArraysOfStrings(
                input[key],
                isInAnnoContext ? true : key.startsWith("@"),
                key === "elements"
              )
            : input[key];
      }
    } else {
      // no change, and hasn't children
    }
    return input;
  }

  public removeHierarchyAnnotations(csnDefinition: ICsnDefinition) {
    /*
      remove annotation @ObjectModel.dataCategory: {"#": "HIERARCHY" }

      replace @ObjectModel.modelingPattern: #ANALYTICAL_PARENT_CHILD_HIERARCHY_NODE   with  @ObjectModel.modelingPattern: #DATA_STRUCTURE

      replace value in array: @ObjectModel.supportedCapabilities: [
      #ANALYTICAL_PARENT_CHILD_HIERARCHY_NODE, …]
      with #DATA_STRUCTURE
    */

    if ((csnDefinition as any)?.["@ObjectModel.dataCategory"]?.["#"] === "HIERARCHY") {
      delete (csnDefinition as any)["@ObjectModel.dataCategory"];
    }

    if ((csnDefinition as any)?.["@ObjectModel.modelingPattern"]?.["#"] === "ANALYTICAL_PARENT_CHILD_HIERARCHY_NODE") {
      (csnDefinition as any)["@ObjectModel.modelingPattern"]["#"] = "DATA_STRUCTURE";
    }

    const supportedCapabilities = (csnDefinition as any)?.["@ObjectModel.supportedCapabilities"];
    if (supportedCapabilities) {
      supportedCapabilities.forEach((capability: any) => {
        if (capability["#"] === "ANALYTICAL_PARENT_CHILD_HIERARCHY_NODE") {
          capability["#"] = "DATA_STRUCTURE";
        }
      });
    }
  }

  public checkTextNodeAnnotations(csnDefinition: ICsnDefinition) {
    // check if CSN definition is a text node
    // background: DW101-92639 Local tables with semantic type "Text" are giving warning due to missing text element assignment
    // ----------------------------------
    // for entities with @ObjectModel.dataCategory: {"#": "TEXT"}
    // check:
    // a) which is the relevant key attribute
    //     - there is only one key attribute (except the language key)
    //     - in case of multiple key attributes there is one key attribute defined as @ObjectModel.representativeKey: { "=": <key element> }
    // b) which is the text attribute of the node
    //     - select all non-key elements with semantics.text: true
    //       - if none - do nothing (warning remains)
    //       - if one -> this is it
    //       - if multiple -> check length, take the longest
    // c) if a pair of a) relevant key and b) relevant text attribute is found
    // add annotation
    //    "@ObjectModel.text.element" : [ { "=": <text element> } ] to the key attribute
    // ----------------------------------
    if (
      (csnDefinition as any)?.["@ObjectModel.dataCategory"]?.["#"] === "TEXT" &&
      csnDefinition.elements &&
      Object.keys(csnDefinition.elements).length > 0
    ) {
      // check if expected text anno is already present
      const keysWithCorrectAnno = Object.entries(csnDefinition.elements).filter(
        (element: any) =>
          element[1].key && // the anno has to be at a key attribute
          !element[1]["@Semantics.language"] && // the key attribute must not be the language key
          element[1]["@ObjectModel.text.element"] && // the key attribute mus have the text anno
          element[1]["@ObjectModel.text.element"].length > 0 && // the text anno value must not be empty
          element[1]["@ObjectModel.text.element"][0]["="] && // the text anno value must be an object containing key"="
          typeof element[1]["@ObjectModel.text.element"]![0]["="] === "string" && // value with text element must be a string
          element[1]["@ObjectModel.text.element"]![0]["="] !== "" // and must not be empty
      );
      if (keysWithCorrectAnno.length < 1) {
        // not found or not correctly maintained
        // adjustment required

        // determine the relevant text key
        let textKey: string | undefined;
        const entityKeys = Object.entries(csnDefinition.elements).filter(
          (element: any) => element[1].key && !element[1]["@Semantics.language"]
        );
        if (entityKeys.length === 1) {
          // only one key attribute
          textKey = entityKeys[0][0];
        } else if (entityKeys.length > 1) {
          // check representative key
          const representativeKeyAttribute = (csnDefinition as any)?.["@ObjectModel.representativeKey"];
          if (representativeKeyAttribute && entityKeys.find((key) => key[0] === representativeKeyAttribute)) {
            const representativeKeyElement = entityKeys.find((key) => key[0] === representativeKeyAttribute);
            if (representativeKeyElement) {
              textKey = representativeKeyElement[0];
            }
          }
        }

        // collect all text elements
        let textElement: string | undefined;
        const textElements = Object.entries(csnDefinition.elements).filter(
          (element: any) =>
            !element[1].key && element[1].type === "cds.String" && element[1]["@Semantics.text"] === true
        );
        if (textElements.length === 1) {
          textElement = textElements[0][0];
        } else if (textElements.length > 1) {
          // get the one with the longest length
          textElement = textElements.reduce((prev, curr) => {
            let returnVal = prev;
            let a = (prev[1] as ICsnElement).length;
            let b = (curr[1] as ICsnElement).length;
            returnVal = a && b && a > b ? prev : curr;
            return returnVal;
          })[0];
        }

        // add text element annotation to the determined key element
        if (textKey && textElement) {
          if (
            csnDefinition.elements[textKey] &&
            (!(csnDefinition.elements[textKey] as ICsnElement)["@ObjectModel.text.element"] || // key element does not have the text anno
              ((csnDefinition.elements[textKey] as ICsnElement)["@ObjectModel.text.element"] &&
                (csnDefinition.elements[textKey] as ICsnElement)["@ObjectModel.text.element"]!.length > 0 && // the text anno is present and has a length
                (typeof (csnDefinition.elements[textKey] as ICsnElement)["@ObjectModel.text.element"]![0] ===
                  "string" || // but is a just string and not an object
                  !(csnDefinition.elements[textKey] as ICsnElement)["@ObjectModel.text.element"]![0] || // or does not have the "=" syntax
                  !(csnDefinition.elements[textKey] as ICsnElement)["@ObjectModel.text.element"]![0]["="])))
          ) {
            // add (potentially overwrite) the text element annotation with correct syntax
            (csnDefinition.elements[textKey] as ICsnElement)["@ObjectModel.text.element"] = [{ "=": textElement }];
          }
        }
      }
    }
  }

  public modifySyntaxOfForeignKeyAssociationsWithObject(csnDefinition: ICsnDefinition) {
    // method checks if the CSN definition contains elements with annotation @ObjectModel.foreignKey.association
    // eg. { ID: { @ObjectModel.foreignKey.association: "_Association" } }
    // and converts them to objects: { ID: { @ObjectModel.foreignKey.association: {"=": "_Association" } }

    // method checks if the CSN definition contains elements with annotation @ObjectModel.text.association
    // eg. { ID: { @ObjectModel.text.association: "_Association" } }
    // and converts them to objects: { ID: { @ObjectModel.text.association: {"=": "_Association" } }

    if (csnDefinition && csnDefinition.elements) {
      for (const element of Object.values(csnDefinition.elements)) {
        if (element && (element as any)["@ObjectModel.foreignKey.association"]) {
          let associationValue = (element as any)["@ObjectModel.foreignKey.association"];
          if (typeof associationValue === "string") {
            // change value of the annotation of the foreignKey association to object
            (element as any)["@ObjectModel.foreignKey.association"] = {
              "=": associationValue,
            };
          } else if (typeof associationValue === "object") {
            // also if the value is an object, change annotation to the correct format "@ObjectModel.foreignKey.association"
            if (
              (element as any)["@ObjectModel.foreignKey.association"] &&
              !(element as any)["@ObjectModel.foreignKey.association"]
            ) {
              (element as any)["@ObjectModel.foreignKey.association"] = associationValue;
            }
          }
        }
        if (element && (element as any)["@ObjectModel.text.association"]) {
          let associationValue = (element as any)["@ObjectModel.text.association"];
          if (typeof associationValue === "string") {
            // change value of the annotation of the text association to object
            (element as any)["@ObjectModel.text.association"] = {
              "=": associationValue,
            };
          } else if (typeof associationValue === "object") {
            // also if the value is an object, change annotation to the correct format "@ObjectModel.text.association"
            if (
              (element as any)["@ObjectModel.text.association"] &&
              !(element as any)["@ObjectModel.text.association"]
            ) {
              (element as any)["@ObjectModel.text.association"] = associationValue;
            }
          }
        }
      }
    }
  }

  public checkPartitioningRequired(csnDefinition: ICsnDefinition) {
    // check if partitioning is required
    // therefore check if annotation @ObjectModel.usageType.sizeCategory: {"=": "XXL" } is available
    // if yes, add the annotation @DataWarehouse.partition with the structure:
    //
    // @DataWarehouse.partition: {
    //   "by": {"#" : "HASH"},
    //   "elements" : [
    //      { "=": "<keyField1>" },
    //      { "=": "<keyField2>" },
    //   ...
    //   ],
    //   "numberOfPartitions": 8
    //  }

    if ((csnDefinition as any) && (csnDefinition as any)["@ObjectModel.usageType.sizeCategory"]?.["#"] === "XXL") {
      if (!(csnDefinition as any)["@DataWarehouse.partition"]) {
        (csnDefinition as any)["@DataWarehouse.partition"] = {
          by: {
            "#": "HASH",
          },
          elements: [],
          numberOfPartitions: 8,
        };
        if ((csnDefinition as any).elements) {
          for (const key of Object.keys((csnDefinition as any).elements)) {
            if ((csnDefinition as any).elements[key].key) {
              (csnDefinition as any)["@DataWarehouse.partition"].elements.push({
                "=": key,
              });
            }
          }
        }
      }
    }
  }

  public checkIndexTypeRequired(csnDefinition: ICsnDefinition) {
    // check if index definition is required
    // therefore check if annotation @ObjectModel.usageType.sizeCategory: {"=": "XXL" } is available
    // if yes, add the annotation "DataWarehouse.primaryKey.indexType" : { "#": "INVERTED_VALUE"  }
    //
    if ((csnDefinition as any) && (csnDefinition as any)["@ObjectModel.usageType.sizeCategory"]?.["#"] === "XXL") {
      if (!(csnDefinition as any)["@DataWarehouse.primaryKey.indexType"]) {
        (csnDefinition as any)["@DataWarehouse.primaryKey.indexType"] = {
          "#": "INVERTED_VALUE",
        };
      }
    }
  }
}
