/**
 * /* eslint-disable @typescript-eslint/no-inferrable-types
 *
 * @format
 */
// eslint-disable-next-line no-restricted-imports
import {
  DependencyKind,
  IRepoCallSearchParams,
  IRepositoryElementDependency,
  ISearchResults,
  ObjectKind,
  RepositoryObjectKind,
} from "@sap/deepsea-types";
import Status from "http-status-codes";
import { JSONPath } from "jsonpath-plus";
import {
  DwcObjectTypes,
  ImportAccessTechnology,
  RemoteAccessMode,
  RemoteAccessModeSwitch,
  RepflowAssignmentStatus,
} from "../../../../shared/metadataImport/metadataImportTypes";
import { TypeIds } from "../../../connections/utils/Constants";
import { FeatureFlagProvider, IFeatureFlagsMap } from "../../../featureflags/FeatureFlagProvider";
import { HybridParserFactory } from "../../../hybridParser/HybridParserFactory";
import {
  HybridParserParameterS4UI,
  HybridParserS4UI,
  RootObject,
  Entity as S4UIEntity,
  Value,
} from "../../../hybridParser/HybridParserS4UI";
import {
  CONTEXTTYPE_SCHEMA,
  HybridParserParameter,
  ICsnExtended,
  ICsnObjectToDeploy,
  IHybridCsn,
  IParseResult,
  Type,
} from "../../../hybridParser/HybridParserType";
import { getLogger } from "../../../logger";
import { RemoteDacEntityProcessorHelper } from "../../../routes/dac/hybridHelpers/EntityProcessorHelper";
import { RemoteDacHybridImportHelper } from "../../../routes/dac/hybridHelpers/HybridImportHelper";
import * as DacTypes from "../../../routes/dac/types";
import { ITaskLogMessage, Severity } from "../../../task/logger/models";
import { RepositoryObjectClient } from "../../client/repositoryObjectClient";
import { DestinationData } from "../../essearch/lib/DataTypes";
import { IRequestContext } from "../../security/common/common";
import {
  AbapTypesFromBackend,
  DeploymentAPI,
  EntitiesFromBackend,
  Entity,
  ImportExistingObject,
  ImportI18nHandlingMode,
  ImportI18nLanguMapping,
  ImportI18nLanguRole,
  ImportProcessStep,
  ImportableEntities,
  IngestionSpaceStatus,
  OrdResponse,
  UiElemStatus,
} from "./DataTypes";
import { HttpHandler } from "./HttpHandler";
import { IEntityProcessor } from "./IEntityProcessor";
import { ImportCsnTranslator } from "./ImportCsnTranslator";
import { ImportRequest } from "./ImportRequest";
import { IngestionSpaceHandler } from "./IngestionSpaceHandler";
import { OrdProcessor } from "./OrdProcessor";

const { logError, logWarning, logInfo } = getLogger("ESImportManager_EntityProcessor");

const BATCH_SIZE = 100;

export class EntityProcessor implements IEntityProcessor {
  private destinationData: DestinationData | undefined;
  private onboardedConfig: { [key: string]: any } | undefined;
  static EntityProcessor: any;

  constructor(destinationData?: DestinationData) {
    // constructor
    this.destinationData = destinationData;
  }

  public async getImportableEntities(
    importRequest: ImportRequest,
    batchContext: IRequestContext,
    includeLocalization?: boolean,
    requestFullCsn?: boolean
  ): Promise<ImportableEntities> {
    // get importable entities from backend
    // and prepare the entity list for the ui
    const httpHandler = new HttpHandler(this.destinationData);

    // call http to get ABAP types - if required
    // note: current scenarios do not use ABAP types - an empty object will be returned
    const abapTypesFromBackend = await this.fetchAbapTypesIfRequired(importRequest, httpHandler, batchContext);

    // fetch entities from backend:
    // call the CSN_Exposure Service
    let entitiesFromBackend = await this.fetchEntities(importRequest, httpHandler, batchContext, includeLocalization);

    let responseEntities: ImportableEntities = {};
    let ordResponseWithErrors: OrdResponse | undefined;

    // process the entities
    if (entitiesFromBackend && entitiesFromBackend.entitiesWithCsn && entitiesFromBackend.entitiesWithCsn.length > 0) {
      // for DataProduct import
      // check onboarded config of given DP API to decide if replfow or remote table should be used as default
      if (importRequest.getAccessTechnology() === ImportAccessTechnology.OdcDataProducts) {
        this.onboardedConfig = await this.checkOnboardedApiConfig(entitiesFromBackend, importRequest, batchContext);

        // adjust the case "Decide Dynamically"
        if (importRequest.getRemoteAccessMode() === RemoteAccessMode.DECIDE_DYNAMICALLY) {
          if (this.onboardedConfig?.remoteAccessMode === RemoteAccessMode.VIA_REPFLOW) {
            importRequest.setRemoteAccessMode(RemoteAccessMode.VIA_REPFLOW);
          } else if (this.onboardedConfig?.remoteAccessMode === RemoteAccessMode.VIA_REMOTETABLES) {
            importRequest.setRemoteAccessMode(RemoteAccessMode.VIA_REMOTETABLES);
          } else {
            // this.onboardedConfig.notYetOnboarded
            // and others
            // e.g. in case this.onboardedConfig.repoCallEndedWithError
            // generally assume repflow
            if (this.destinationData?.connTypeId === TypeIds.HDL_FILES) {
              // prevent unrunnable repflows for HDL_FILES - suggest remotetables as default
              // legacy connection type HDL_FILES does not replication flow on runtime execution level (the repflows get onboarded correctly but don't run)
              // new UCL connections should have connection type BUSINESS_DATA_PRODUCT always
              importRequest.setRemoteAccessMode(RemoteAccessMode.VIA_REMOTETABLES);
            } else {
              // default
              // in case not yet onboarded: set local tables and repflow
              importRequest.setRemoteAccessMode(RemoteAccessMode.VIA_REPFLOW);
            }
          }
        } else if (importRequest.getRemoteAccessMode() === RemoteAccessMode.VIA_REPFLOW) {
          if (
            this.onboardedConfig?.remoteAccessModeFound &&
            this.onboardedConfig?.remoteAccessMode === RemoteAccessMode.VIA_REMOTETABLES
          ) {
            // the user has selected repflow but content was onboarded as remote tables
            // switch to VIA_REPFLOW will occur
            importRequest.setRemoteAccessModeSwitch(RemoteAccessModeSwitch.REMOTETABLES_TO_REPFLOW);
          }
        } else if (importRequest.getRemoteAccessMode() === RemoteAccessMode.VIA_REMOTETABLES) {
          if (
            this.onboardedConfig?.remoteAccessModeFound &&
            this.onboardedConfig?.remoteAccessMode === RemoteAccessMode.VIA_REPFLOW
          ) {
            // the user has selected remote tables but content was onboarded as local tables with repflow
            // switch to VIA_REMOTETABLES will occur
            importRequest.setRemoteAccessModeSwitch(RemoteAccessModeSwitch.REPFLOW_TO_REMOTETABLES);
          }
        }
      }

      // localization handling
      if (includeLocalization) {
        // insert texts with language requested in ui
        const i18nHandlingMode = importRequest.getI18nHandlingMode();
        const i18nLanguMappings = importRequest.getLanguMappingsAll();
        let textReplacementLangu: string | undefined;
        if (i18nHandlingMode === ImportI18nHandlingMode.replaceTextLabels) {
          textReplacementLangu = importRequest.getLanguForTextReplacement();
        }
        entitiesFromBackend = this.localizationHandling(
          entitiesFromBackend,
          textReplacementLangu, // for LanguHandlingMode "ReplaceTextLabels"
          i18nHandlingMode,
          i18nLanguMappings, // for languHandlingMode "passI18nAsObject"
          batchContext
        );
      }

      // get feature flags
      const featureFlags = await FeatureFlagProvider.getFeatureFlags(batchContext);

      // parse entities
      if (importRequest.getProcessStep() === ImportProcessStep.getEntityListForUI) {
        // call "lean" parser for the entity table only (import step 3)
        responseEntities = await this.parseGetImportableEntities(
          entitiesFromBackend,
          batchContext,
          importRequest,
          featureFlags
        );

        // fetch last import status for all entities
        await this.entitiesFetchLastImportStatus(responseEntities, batchContext, importRequest);
      } else {
        // in this step fetch data layer only
        // business layer will be handled in a later step
        const dataLayerOnly = DwcObjectTypes.DATALAYER_ONLY; // importRequest.getCreateDataLayerObjectsOnly();

        await importRequest.logHeartBeat(); // before parser

        // parse the entities
        // full CSN parser for import step: "execute import"
        responseEntities = await this.parseEntities(
          entitiesFromBackend,
          batchContext,
          importRequest,
          featureFlags,
          dataLayerOnly,
          abapTypesFromBackend,
          requestFullCsn
        );

        await importRequest.logHeartBeat(); // after parser

        if (responseEntities && responseEntities.entitiesToDeploy && responseEntities.entitiesToDeploy.length > 0) {
          // fetch last import status for all entities
          await this.entitiesFetchLastImportStatus(responseEntities, batchContext, importRequest);

          // call ord service
          // or in case of repflows build deployment hints section
          const ordProcessor = new OrdProcessor(importRequest, httpHandler, batchContext, this.destinationData);
          ordResponseWithErrors = await ordProcessor.handleOrdRequest(batchContext, responseEntities);

          // generate view layer around remote tables
          // this.generateViewLayer(responseEntities, this.destinationData)

          // adjust datatypes in CSN for the various remote adapters
          // note: this step has been moved here from the previous location as it belongs to Entity processing
          await this.adjustDataTypesInCsn(
            batchContext,
            importRequest.getAccessTechnology(),
            responseEntities,
            this.destinationData,
            importRequest
          );

          // eliminate from the list of elements the elements which have the annotation @Analytics.Hidden:true --> because the CDI sevice does the same
          // this is only for connection of type S4HANACLOUD ( deployment using CDI)
          this.eliminateAttributesFromEntityDefElementsInCsn(responseEntities, this.destinationData);

          // add metadata about import, e.g. last import time, mainly for enforcing a save during re-import
          // to ensure pending errors for dangling ref. handling are written
          this.addImportMetaInformation(batchContext, responseEntities, importRequest);
        }
      }
    }

    // map errors from the backend (from import structure)
    if (entitiesFromBackend.httpError && Object.keys(entitiesFromBackend.httpError).length > 0) {
      const errorLocation = entitiesFromBackend.errorLocation
        ? entitiesFromBackend.errorLocation
        : "HTTP Error from Backend";
      this.addError(responseEntities, entitiesFromBackend.httpError, "httpErrorFromBackend", errorLocation);
    }
    if (entitiesFromBackend.contentError && Object.keys(entitiesFromBackend.contentError).length > 0) {
      const errorLocation = entitiesFromBackend.contentError.location
        ? entitiesFromBackend.contentError.location
        : "Before Calling Parser";
      this.addError(responseEntities, entitiesFromBackend.contentError, "ContentErrorFromBackend", errorLocation);
    }
    if (
      entitiesFromBackend.statusMessage &&
      entitiesFromBackend.statusMessage !== "" &&
      entitiesFromBackend.statusMessage.toUpperCase() !== "OK"
    ) {
      this.addErrorMessage(responseEntities, entitiesFromBackend.statusMessage);
    }
    if (entitiesFromBackend.statusCode) {
      responseEntities.statusCode = entitiesFromBackend.statusCode;
    }

    if (
      ordResponseWithErrors &&
      ordResponseWithErrors.statusCode &&
      ordResponseWithErrors.statusCode !== Status.OK &&
      ordResponseWithErrors.statusMessage
    ) {
      const errorLocation = ordResponseWithErrors.errorLocation
        ? ordResponseWithErrors.errorLocation
        : "OrdProcessor: Generate Replication Flows";
      this.addError(responseEntities, ordResponseWithErrors, "ordProcessorError", errorLocation);
      this.addErrorMessage(responseEntities, ordResponseWithErrors.statusMessage, true, true);
    }

    // map errors from the "get abap types" request
    if (abapTypesFromBackend.httpError && Object.keys(abapTypesFromBackend.httpError).length > 0) {
      this.addError(responseEntities, abapTypesFromBackend.httpError, "httpErrorForGetAbapTypes");
    }
    if (
      abapTypesFromBackend.statusMessage &&
      abapTypesFromBackend.statusMessage !== "" &&
      entitiesFromBackend.statusMessage.toUpperCase() !== "OK"
    ) {
      this.addErrorMessage(responseEntities, abapTypesFromBackend.statusMessage);
    }
    // status code from getAbapTypes shall not overwrite status from getEntities
    if (
      responseEntities.statusCode === Status.OK &&
      abapTypesFromBackend.statusCode &&
      abapTypesFromBackend.statusCode !== Status.OK
    ) {
      responseEntities.statusCode = abapTypesFromBackend.statusCode;
    }

    // no entities from backend
    // map http errors to response object "entities"
    if (
      !entitiesFromBackend ||
      !entitiesFromBackend.entitiesWithCsn ||
      entitiesFromBackend.entitiesWithCsn.length === 0
    ) {
      // handle the situation that there is no error message (http ok) but no result
      // in this case: invalid entity
      if (!entitiesFromBackend.httpError && !abapTypesFromBackend.httpError) {
        // add an error object with information
        const noRegexCheck = true;
        if (importRequest.getAccessTechnology() === ImportAccessTechnology.OdcDataProducts) {
          this.addError(
            responseEntities,
            {
              dataProviderGuid: importRequest.getDataProviderGuid(),
              apiId: importRequest.getDataProductApiId(),
              connectionId: this.destinationData?.destinationId,
            },
            "NoEntitiesForDataProductAPI",
            "Get DataProduct Entitites from Catalog" // error location
          );
          this.addErrorMessage(
            responseEntities,
            `No entities received on DataProduct onboarding - Import request details: DataProvider ${
              importRequest.getDataProviderGuid()
                ? importRequest.getDataProviderGuid()
                : `<Data Provider GUID missing>!!`
            }, API ID ${
              importRequest.getDataProductApiId() ? importRequest.getDataProductApiId() : `<API ID missing>!!`
            }`,
            noRegexCheck
          );
          if (importRequest.getDataProviderGuid()) {
            this.addErrorMessage(
              responseEntities,
              `Error Location: GetEntities - Call which did not return entitites: [QueryBuilderOdcDataProducts, SearchAdapterOdcDataProducts]: /catalog/v1/dataProviders/${importRequest.getDataProviderGuid()}/dataProductEntitiesQuery`,
              noRegexCheck
            );
            this.addErrorMessage(
              responseEntities,
              `Check if the selected DataProduct API is exposed in the catalog and if the content is present and available for onboarding - in case of SAP content contact SAP`,
              noRegexCheck
            );
          }
          if (importRequest.getIngestionSpace() && importRequest.getSpaceId() && importRequest.getExecUser()) {
            this.addErrorMessage(
              responseEntities,
              `Onboarding job details: Ingestion space ${importRequest.getIngestionSpace()} - entities expected to be shared to target space ${importRequest.getSpaceId()} - process started by ${importRequest.getExecUser()}, running under generic technical user`,
              noRegexCheck
            );
          }
          if (!importRequest.getDataProductApiId() || importRequest.getDataProductApiId() === "") {
            // better message for the case from DW101-96486
            this.addErrorMessage(
              responseEntities,
              `ImportRequest does not contain a valid API ID - check ImportRequest parameters passed in ImportApi or metadataimport/csn (endpoint)`,
              noRegexCheck
            );
          }
        } else {
          this.addError(
            responseEntities,
            { entityName: importRequest.getImportStartEntity(), connectionId: this.destinationData?.destinationId },
            "NoResponseForEntity",
            "Get Entities from Backend (before Parser)" // error location
          );
          // additionally add the messages into the message array
          let startEntity = importRequest.getImportStartEntity();
          if (startEntity && startEntity.includes("%2F")) {
            startEntity = startEntity.replace(/%2F/g, "/");
          }
          this.addErrorMessage(responseEntities, `Could not import entity ${startEntity}`, noRegexCheck);
          this.addErrorMessage(responseEntities, `Check if selected entity ${startEntity} is valid`, noRegexCheck);
        }
      }
    }

    // add warnings
    if (entitiesFromBackend.warnings && entitiesFromBackend.warnings.length > 0) {
      this.addWarnings(responseEntities, entitiesFromBackend.warnings);
    }

    // add info messages
    if (entitiesFromBackend.infomessages && entitiesFromBackend.infomessages.length > 0) {
      this.addInfos(responseEntities, entitiesFromBackend.infomessages);
    }

    // add object to be logged
    if (entitiesFromBackend.objectToBeLogged && Object.keys(entitiesFromBackend.objectToBeLogged).length > 0) {
      this.addObjectToBeLogged(responseEntities, entitiesFromBackend.objectToBeLogged);
    }

    return responseEntities;
  }

  public async getBusinessEntities(
    iCsn: ICsnExtended,
    importRequest: ImportRequest,
    batchContext: IRequestContext,
    parseRepairedCsnMode?: boolean
  ): Promise<ImportableEntities> {
    //  public async getBusinessEntities(importableEntities: ICsnObjectToDeploy[], importRequest: ImportRequest, batchContext: IRequestContext): Promise<ImportableEntities> {
    // handle business layer
    // source for the parser will be the repaired CSN
    let responseEntities: ImportableEntities = {};
    const featureFlags = await FeatureFlagProvider.getFeatureFlags(batchContext);

    const i18nHandlingMode = importRequest.getI18nHandlingMode();

    // for S/4 Cloud and OnPrem:
    // --> repository has recreated I18n block in the full CSN after saving data layer objects
    // --> repository has exchanged the current texts for labels with the placeholder
    // --> we need to:  replace the placeholders for labels again with current texts from i18n block and remove i18n block
    // for BW/4 and BWBridge:
    // --> we get the original CSN (not from repository, but as returned from backend)
    // --> placeholders have been processed on DataLayer SAVE
    // --> if the usecase was  ImportI18nHandlingMode.replaceTextLabels (by datalayer) --> everything is correct --> nothing to do
    // --> if the uscease was  ImportI18nHandlingMode.passI18nAsObject (by datalayer) --> texts have to be replaced for business layer
    if (parseRepairedCsnMode || i18nHandlingMode !== ImportI18nHandlingMode.replaceTextLabels) {
      // i18n:
      // business layer does not support i18n, always do a text replacement
      const textReplacementLangu = importRequest.getLanguForTextReplacement();

      if (textReplacementLangu && iCsn && iCsn.definitions) {
        try {
          // stringify CSN definitions for replacement
          let definitionString = JSON.stringify(iCsn.definitions);

          if ((iCsn as { [key: string]: any }).i18n) {
            // handle the case that incoming CSN is a "repaired CSN" which has property .i18n
            // cases S/4 Cloud and OnPrem
            // from the repository we get a processed i18n section with repository-validated replacement keys
            // this is the source for the business layer
            // for business layer a text replacement with the text-replacement language has to be made (business layer does not support replacement keys)
            // afterwards the remaining i18n sections has to be mapped back to the i18nDefinitions structure for SAVE & DEPLOY
            const i18nObject = { i18n: (iCsn as { [key: string]: any }).i18n };

            // replace placeholders in the stringified object definitions (all entities)
            definitionString = this.replaceLanguageDependentTexts(i18nObject, definitionString, textReplacementLangu);

            // reformat i18n blocks so that it is passed the same way as when called from the backend
            if ((iCsn as { [key: string]: any }).i18n) {
              iCsn.i18nDefinitions = this.mapI18nToI18nDefinitions(
                (iCsn as { [key: string]: any }).i18n as II18nContent
              );
              delete (iCsn as { [key: string]: any }).i18n;
            }
          } else if ((iCsn as { [key: string]: any }).i18nDefinitions) {
            // handle the case that incoming CSN is the CSN as sent to SAVE & DEPLOY in the data layer
            // cases BW/4 and BWBridge
            // in the csn there is the i18nDefinitions section which has been built when processing the data layer
            // this is the source for the business layer
            // for business layer a text replacement with the text-replacement language has to be made (business layer does not support replacement keys)
            // this has to be done entity-by-entity because the i18nDefinitions section is organized by entity
            const i18nDefinitions: II18nDefinitions = (iCsn as { [key: string]: any }).i18nDefinitions;
            for (const entityName of Object.keys(i18nDefinitions)) {
              const i18nObject = i18nDefinitions[entityName];
              // replace placeholders in the stringified object definitions (entity by entity)
              definitionString = this.replaceLanguageDependentTexts(i18nObject, definitionString, textReplacementLangu);
            }
            // p.s. no further reformatting required - the CSN already has i18nDefinitions
          }
          // restore csn definitions from parsed string
          iCsn.definitions = JSON.parse(definitionString);
        } catch (err) {
          logError(
            `[EntityProcessor] [LocalizationHandling] [JSON Parse] Error in localization handling for Business Layer`,
            { context: batchContext }
          );
        }
      }
    }

    // parse the business layer entities
    responseEntities = await this.parseBusinessLayerEntities(
      iCsn,
      batchContext,
      importRequest,
      featureFlags,
      parseRepairedCsnMode
    );

    return responseEntities;
  }

  public async parseEntities(
    entitiesFromBackend: EntitiesFromBackend,
    batchContext: IRequestContext,
    importRequest: ImportRequest,
    featureFlags: IFeatureFlagsMap,
    dataLayerOnly?: DwcObjectTypes,
    abapTypesFromBackend?: AbapTypesFromBackend,
    requestFullCsn?: boolean
  ): Promise<ImportableEntities> {
    const res: ImportableEntities = {};

    res.entitiesForUi = [];

    let deployUsingBW = false;
    let deployUsingCDI = false;
    let deployUsingOnPremVT = false;
    // let deployUsingDpApi = false; // will be set directly to hybrid parameters in the dp api case

    let remoteConnectionExtended: string | undefined;

    let namespaceContext: string | undefined;
    let apiContext: string | undefined;
    const csnTranslator = ImportCsnTranslator.get();
    const ffPartitioning = await FeatureFlagProvider.isFeatureActive(batchContext, "DWCO_TABLE_PARTITIONING_HASH");

    if (entitiesFromBackend) {
      // check connection
      if (!this.destinationData || this.destinationData.hasNoConnectionDetails) {
        // no connection details
        // this exception is also raised in the DestinationHandler (but also check it here)
        const errorLocation = "Before Calling Parser";
        this.addError(res, { hasError: true }, "ConnectionError", errorLocation);
        this.addErrorMessage(res, `Cannot process Import Request: Invalid connection - no connection details`);

        logError(`[EntityProcessor] [ParseEntities] [CheckConnection] Invalid connection - no connection details`, {
          context: batchContext,
        });
      }
      const destinationId: string = this.destinationData?.destinationId ? this.destinationData?.destinationId : "";

      // RMS check
      // FF obsolete - removed

      const schema = this.getSchemaName(this.destinationData);

      // get CSN from entities from backend
      if (
        entitiesFromBackend.entitiesWithCsn &&
        entitiesFromBackend.entitiesWithCsn.length > 0 &&
        !this.hasError(res)
      ) {
        // collect source strings into ICsn object
        const csnVersion: ICsnVersion = { csn: "1.0" };
        const completeCsn: ICsnExtended = {
          namespace: destinationId, // set destination id (qualified name) as namespace
          definitions: {},
          i18nDefinitions: {},
          onboardingHints: {},
          "releaseContract.definitions": {},
          version: csnVersion,
          // meta?:
          // extension?:
          // objects?:
          // shares?:
        };

        // fill entities into merged CSN
        for (const entityFromEntityList of entitiesFromBackend.entitiesWithCsn) {
          if (entityFromEntityList._source.sourceString && entityFromEntityList._source.sourceString !== "") {
            try {
              // parse the source string
              const entityCsn: ICsn = JSON.parse(entityFromEntityList._source.sourceString);

              // collect the CSN definitions for this entity into the CSN object containing all entities
              if (entityCsn && entityCsn.definitions) {
                // map all key-value-pairs (EntityName + iCsnDefinition) to target CSN
                // Object.entries() returns the key-value-pairs of the object as an array
                for (const keyValuePair of Object.entries(entityCsn.definitions)) {
                  if (keyValuePair[0] && keyValuePair[1]) {
                    const entityName: string = keyValuePair[0];
                    const entityObject: any = keyValuePair[1];

                    // translate modeling pattern arrays e.g. ["ANALYTICAL_FACT"] -> "ANALYTICAL_FACT"
                    // remove empty keys in delta mappings
                    csnTranslator.modifyAnnotationsInCSN(entityObject);

                    // to prevent regressions as of now the following CSN translations are only done in case of DataProduct import
                    // translations for the use cases DataProduct

                    if (importRequest.getAccessTechnology() === ImportAccessTechnology.OdcDataProducts) {
                      // translate arrays containing strings to syntax [{ "=" : value }]
                      csnTranslator.modifySyntaxOfArraysWithString(entityObject);

                      // for entities with default aggregation SUM add annotation @AnalyticsDetails.measureType { "#" : "BASE" }
                      csnTranslator.addAnnoAnalyticsMeasureTypeBase(entityObject);

                      // for entities of type ANALYTICAL_FACT add annotation @ObjectModelSupportCapabilities { "dataStructure" : true }
                      csnTranslator.addDataStructureToAnnoObjectModelSupportCapabilities(entityObject);

                      // for entities with amount, currency, quantity, uom --> add annotation @Semantics.unitOfMeasure": true and @Semantics.currencyCode": true
                      csnTranslator.addAnnoSemanticsUoMAndCurrencyCodeTrue(entityObject);

                      // for key fields add property "notNull": true  if not available
                      csnTranslator.addNotNullToKeys(entityObject);

                      // remove not supported hierarchy annotations for DataProduct import
                      csnTranslator.removeHierarchyAnnotations(entityObject);

                      // text entities: check and repair text annotations in the key definitions
                      csnTranslator.checkTextNodeAnnotations(entityObject);

                      // for entities with @ObjectModel.foreingKey.associations --> change value to object
                      csnTranslator.modifySyntaxOfForeignKeyAssociationsWithObject(entityObject);
                    } else if (
                      // translations for the usecases SAPS4HANAOP and SAPS4HANACLOUD (via CSN_Exposure, not via VirtualTable)
                      importRequest.getAccessTechnology() === ImportAccessTechnology.Csn_Exposure &&
                      (this.destinationData?.connTypeId === TypeIds.SAPS4HANAOP ||
                        this.destinationData?.connTypeId === TypeIds.SAPS4HANACLOUD)
                    ) {
                      // translate arrays containing strings to syntax [{ "=" : value }]
                      csnTranslator.modifySyntaxOfArraysWithString(entityObject);

                      // for entities with default aggregation SUM add annotation @AnalyticsDetails.measureType { "#" : "BASE" }
                      csnTranslator.addAnnoAnalyticsMeasureTypeBase(entityObject);

                      // for entities of type ANALYTICAL_FACT add annotation @ObjectModelSupportCapabilities { "dataStructure" : true }
                      csnTranslator.addDataStructureToAnnoObjectModelSupportCapabilities(entityObject);

                      // for entities with amount, currency, quantity, uom --> add annotation @Semantics.unitOfMeasure": true and @Semantics.currencyCode": true
                      csnTranslator.addAnnoSemanticsUoMAndCurrencyCodeTrue(entityObject);

                      // for key fields add property "notNull": true  if not available
                      csnTranslator.addNotNullToKeys(entityObject);

                      // remove not supported hierarchy annotations for DataProduct import
                      csnTranslator.removeHierarchyAnnotations(entityObject);

                      // text entities: check and repair text annotations in the key definitions
                      csnTranslator.checkTextNodeAnnotations(entityObject);

                      // for entities with @ObjectModel.foreingKey.associations --> change value to object
                      csnTranslator.modifySyntaxOfForeignKeyAssociationsWithObject(entityObject);
                    }

                    completeCsn.definitions[entityName] = entityObject;
                  }
                }
              }
            } catch (err) {
              // JSON Parser error, the CSN from backend is invalid
              const errorLocation = "JSON Parser for Imported Entity";
              this.addError(res, { hasError: true }, "EntityParserError", errorLocation);
              const noRegexCheck = true;
              this.addErrorMessage(
                res,
                `Parsing error for entity ${entityFromEntityList._source.objectName}. Syntax error in entity source string from backend, source string cannot be parsed to JSON`,
                noRegexCheck
              );
              if (err.message && err.message !== "") {
                // add the technical message
                this.addErrorMessage(res, err.message, noRegexCheck);
              }
              logError(
                `[EntityProcessor] [ParseEntities] [JSON Parse] Parsing error for entity ${entityFromEntityList._source.objectName}. Syntax error in entity source string from backend, source string cannot be parsed to JSON`,
                { context: batchContext }
              );
            }
          } else {
            // Entity does not have _source.sourceString
            const errorLocation = "JSON Parser for Imported Entity";
            this.addError(res, { hasError: true }, "EntityContentError", errorLocation);
            const noRegexCheck = true;
            this.addErrorMessage(
              res,
              `Content error for entity ${entityFromEntityList._source.objectName}. Source string is empty. Backend does not deliver CSN source for this entity`,
              noRegexCheck
            );
            logError(
              `[EntityProcessor] [ParseEntities] [Content Error] Content error for entity ${entityFromEntityList._source.objectName}. Source string is empty. Backend does not deliver CSN source for this entity`,
              { context: batchContext }
            );
          }

          // fill release contract and release state definitions for each entity
          if (
            entityFromEntityList.entity &&
            entityFromEntityList.entity.releaseContract !== undefined &&
            entityFromEntityList.entity.releaseState !== undefined
          ) {
            const entityName = entityFromEntityList.entity.entityName;
            if (entityName && completeCsn["releaseContract.definitions"]) {
              const releaseContractObj: ICsnReleaseContract = {
                releaseContract: entityFromEntityList.entity.releaseContract,
                releaseState: entityFromEntityList.entity.releaseState,
              };
              completeCsn["releaseContract.definitions"][entityName] = releaseContractObj;
            }
          }
          // remember api context for later use
          namespaceContext = entityFromEntityList.entity.namespaceContext;
          apiContext = entityFromEntityList.entity.apiContext;
          // apiOrdId?: string,
          // runtimeContainerPath?: entityFromBackend.entity.runtimeContainerPath,

          // DW101-87999
          // fill schema context in complete csn
          // for the hybrid parser (so that it can properly be removed)
          if (
            entityFromEntityList.entity.schema &&
            importRequest.getAccessTechnology() === ImportAccessTechnology.OdcDataProducts
          ) {
            const dpSchemaContext = entityFromEntityList.entity.schema;
            if (!completeCsn.definitions[dpSchemaContext]) {
              completeCsn.definitions[dpSchemaContext] = {
                kind: "context",
                "@DataWarehouse.contentImport.contextType": CONTEXTTYPE_SCHEMA,
                "@HybridParser.toBeRemoved": true, // check if this whole CSN part is actually needed by hybrid parser, as it has to be removed...
              } as ICsnDefinition;
            }
          }

          // handle remote entity and remote destination depending on connection type
          if (
            this.destinationData &&
            this.destinationData.connTypeId &&
            this.destinationData.connTypeId === TypeIds.BUSINESS_DATA_PRODUCT
          ) {
            if (importRequest.getAccessTechnology() === ImportAccessTechnology.OdcDataProducts) {
              if (importRequest.getRemoteAccessMode() === RemoteAccessMode.VIA_REMOTETABLES) {
                // deploy as remote tables using SQL on Files Adapter
                // set remote connection and remote entity
                if (
                  entityFromEntityList.entity.originalEntityName &&
                  apiContext &&
                  completeCsn &&
                  completeCsn.definitions &&
                  completeCsn.definitions[entityFromEntityList.entity.originalEntityName]
                ) {
                  if (
                    // check if share name is found
                    entitiesFromBackend.onboardingHints &&
                    entitiesFromBackend.onboardingHints[apiContext] &&
                    entitiesFromBackend.onboardingHints[apiContext].apiResource &&
                    entitiesFromBackend.onboardingHints[apiContext].apiResource.length === 1 &&
                    entitiesFromBackend.onboardingHints[apiContext].apiResource[0].runtimeContainerShare &&
                    // check if schema and entity name is found
                    entityFromEntityList.entity.schema &&
                    entityFromEntityList.entity.entityName
                  ) {
                    // concatenate remote entity id for the remote-table-to-FOS adapter
                    // syntax:  <share_name>0x7F<schema_name>0x7F<table_name>
                    // example: sap_trial_costcenter:v10x7Fcostcenter0x7Fcostcenter
                    const separator = String.fromCharCode(0x7f);
                    const remoteEntityId = `${
                      // share
                      entitiesFromBackend.onboardingHints[apiContext].apiResource[0].runtimeContainerShare
                    }${separator}${
                      // schema
                      entityFromEntityList.entity.schema
                    }${separator}${
                      // entity name
                      entityFromEntityList.entity.entityName
                    }`;

                    // get relevant remote connection
                    let remoteConn: string | undefined;
                    if (importRequest.getUseIngestionSpace()) {
                      const ingestionSpaceDetails = importRequest.getIngestionSpaceDetails();
                      if (ingestionSpaceDetails) {
                        remoteConn = ingestionSpaceDetails.connectionDetails?.generatedSpaceConnection || undefined;
                      }
                    } else {
                      remoteConn = destinationId || undefined;
                    }

                    // add the annos
                    if (remoteConn && remoteEntityId) {
                      completeCsn.definitions[entityFromEntityList.entity.originalEntityName][
                        "@DataWarehouse.remote.connection"
                      ] = remoteConn;
                      completeCsn.definitions[entityFromEntityList.entity.originalEntityName][
                        "@DataWarehouse.remote.entity"
                      ] = remoteEntityId;
                    }
                  }
                  // checks and error handling
                  if (
                    !completeCsn.definitions[entityFromEntityList.entity.originalEntityName][
                      "@DataWarehouse.remote.entity"
                    ]
                  ) {
                    // error: missing remote entity id for remote table to FOS
                    const errorLocation = "Before Calling Parser";
                    const msg = `Cannot deploy entity ${entityFromEntityList.entity.entityName} from API ${apiContext} as remote table: Runtime container share or schema information for remote entity name not found, or missing prerequisites (connection metadata, onboarding hints)`;
                    const noRegexCheck = true;
                    this.addError(res, { hasError: true }, "RemoteTableConnectivityError", errorLocation);
                    this.addErrorMessage(res, msg, noRegexCheck);
                    logError(`[EntityProcessor] [BeforeParser] [SetRemoteEntityNames] [RemoteTableToFOS] ${msg}`, {
                      context: batchContext,
                    });
                  }
                }
              } else if (importRequest.getRemoteAccessMode() === RemoteAccessMode.VIA_REPFLOW) {
                // deploy as local table with replication flow
                // check if a table partitioning is required
                if (
                  ffPartitioning &&
                  entityFromEntityList.entity.originalEntityName &&
                  completeCsn &&
                  completeCsn.definitions &&
                  completeCsn.definitions[entityFromEntityList.entity.originalEntityName]
                ) {
                  // check if the CSN definition has annotation "@ObjectModel.usageType.sizeCategory" === "XXL"
                  // and add partitioning:
                  // for XXL entity number of partitions will be set to 8
                  // in parallel, in deepsea-hook for repflow the repflow param "maxConnections" will be set to 1 (#11894)
                  csnTranslator.checkPartitioningRequired(
                    completeCsn.definitions[entityFromEntityList.entity.originalEntityName]
                  );
                  csnTranslator.checkIndexTypeRequired(
                    completeCsn.definitions[entityFromEntityList.entity.originalEntityName]
                  );
                }
              }
            }
          } else if (
            this.destinationData &&
            this.destinationData.connTypeId &&
            (this.destinationData.connTypeId === TypeIds.SAPBWMODELTRANSFER ||
              this.destinationData.connTypeId === TypeIds.SAPBWBRIDGE)
          ) {
            // BW: deploy using the HANA connection of the BWMODELTRANSFER or SAPBWBRIDGE connection
            deployUsingBW = true;

            if (!schema || schema === "") {
              const errorLocation = "Before Calling Parser";
              this.addError(res, { hasError: true }, "ConnectionError", errorLocation);
              const noRegexCheck = true;
              this.addErrorMessage(
                res,
                `Cannot import and deploy entity ${entityFromEntityList.entity.entityName} via connection ${destinationId}`,
                noRegexCheck
              );
              const bwMissingSchemaErrmsg =
                `SAP BW Model Transfer Connection ${destinationId} is not up-to-date for use in the Import Manager: ` +
                `Property "schema name" is missing in the connection definition. ` +
                `Please re-enter your credentials in the Connections maintenance in order to update the schema in the connection.`;
              this.addErrorMessage(res, bwMissingSchemaErrmsg, noRegexCheck);

              logError(
                `[EntityProcessor] [ParseEntities] [SchemaForBWConnection] Cannot import and deploy entity ${entityFromEntityList.entity.entityName} via connection ${destinationId}: ${bwMissingSchemaErrmsg}`,
                { context: batchContext }
              );
            }
          } else if (
            this.destinationData &&
            this.destinationData.connTypeId &&
            this.destinationData.connTypeId === TypeIds.SAPS4HANACLOUD
          ) {
            // S4 cloud
            // for RemoteAccessMode !== VIA_REPFLOW deploy via CDI
            // use the original remote connection for CDI, no {connection}_CDI
            if (importRequest.getRemoteAccessMode() !== RemoteAccessMode.VIA_REPFLOW) {
              deployUsingCDI = true;
            }
          } else if (
            this.destinationData &&
            this.destinationData.connTypeId &&
            this.destinationData.connTypeId === TypeIds.SAPS4HANAOP
          ) {
            // do not check SAP_BASIS release
            // instead check the access technology selected by the user - for low releases only Virtual Table will be possible
            if (
              importRequest.getAccessTechnology() === ImportAccessTechnology.VirtualTable
              // this.destinationData &&
              // this.destinationData.sapBasisRelease &&
              // this.destinationData.sapBasisRelease <= "758"
            ) {
              if (
                entityFromEntityList.entity &&
                entityFromEntityList.entity.remoteAccessApi !== undefined &&
                entityFromEntityList.entity.remoteAccessApi !== "" &&
                entityFromEntityList.entity.remoteAccessEntityId !== undefined &&
                entityFromEntityList.entity.remoteAccessEntityId !== ""
                // && entityFromEntityList.entity.releaseContract && entityFromEntityList.entity.releaseContract === "C1"
              ) {
                // in this case the backend has verified that the entity is released with release contract C1
                // and dataExtraction.enabled = true
                // and that there is a valid remoteAccessEntityId ( = ODP ID )
                const entityName = entityFromEntityList.entity.entityName;
                if (entityName && completeCsn && completeCsn.definitions && completeCsn.definitions[entityName]) {
                  const entityObject = completeCsn.definitions[entityName];
                  const remoteEntityId = `${entityFromEntityList.entity.remoteAccessApi}.${entityFromEntityList.entity.remoteAccessEntityId}`;
                  entityObject["@DataWarehouse.remote.connection"] = `${destinationId}`; // set the current destination - do not use CDI in this case
                  entityObject["@DataWarehouse.remote.entity"] = remoteEntityId;
                }
              }
              deployUsingOnPremVT = true;
            } else if (importRequest.getAccessTechnology() === ImportAccessTechnology.Csn_Exposure) {
              // Case 2 : Access Technology CSN_Exposure
              // - - - - - - - - - - - - - - - - - -
              // in this case we have
              // destinationData.sapBasisRelease &&
              // destinationData.sapBasisRelease >= "756" &&
              // remoteAccessMode === RemoteAccessMode.VIA_REPFLOW
              // - - - - - - - - - - - - - - - - - -
              // do not set any @DataWarehouse.remote.entity/remote.connection annotations
              // in the ORD processor a replication flow will be created for the entity
              //
            } else {
              // legacy cases for CES
              // - - - - - - - - - - - - - - - - -
              // to be removed once
              // temporarily for testing deployment using ODP
              // currently only for space "S4_IMPORT_ODP"
              // deploy using ODP
              if (
                this.destinationData &&
                this.destinationData.connTypeId &&
                this.destinationData.connTypeId === TypeIds.SAPS4HANAOP &&
                importRequest.getSpaceId() === "S4_IMPORT_ODP"
              ) {
                // dead usecase...
                // remove completely
              } else {
                // temporarily for testing deployment using CDI
                // currently only for space <> "S4_IMPORT_ODP"
                // deploy using CDI
                // and use extended connection {conn}_CDI
                remoteConnectionExtended = `${this.destinationData.destinationId}_CDI`;
                deployUsingCDI = true;
              }
            }
          }
        }

        // fill data element definitions from onboarding hints api definition
        // currently not provided by data product import - see DW101-83775
        if (
          apiContext &&
          entitiesFromBackend &&
          entitiesFromBackend.onboardingHints &&
          entitiesFromBackend.onboardingHints[apiContext] &&
          entitiesFromBackend.onboardingHints[apiContext].apiResource &&
          entitiesFromBackend.onboardingHints[apiContext].apiResource.length > 0 &&
          entitiesFromBackend.onboardingHints[apiContext].apiResource[0].typeDefinitions
        ) {
          for (const keyValuePair of Object.entries(
            entitiesFromBackend.onboardingHints[apiContext].apiResource[0].typeDefinitions
          )) {
            if (keyValuePair[0] && keyValuePair[1]) {
              // const typeName: string = keyValuePair[0];
              // const typeDefinition: any = keyValuePair[1];
              // completeCsn.definitions[typeName] = typeDefinition;
            }
          }
          // remove type definitions from onboarding hints after mapping into the csn
          delete entitiesFromBackend.onboardingHints[apiContext].apiResource[0].typeDefinitions;
        }

        // fill abap types into merged CSN
        if (
          abapTypesFromBackend &&
          abapTypesFromBackend.abapTypesWithCsn &&
          abapTypesFromBackend.abapTypesWithCsn.length > 0 &&
          !this.hasError(res)
        ) {
          for (const typeFromAbapTypeList of abapTypesFromBackend.abapTypesWithCsn) {
            if (typeFromAbapTypeList._source.sourceString) {
              // parse the source string
              const typeCsn: ICsn = JSON.parse(typeFromAbapTypeList._source.sourceString);

              // collect the CSN definitions for this entity into the CSN object containing all entities
              if (typeCsn && typeCsn.definitions) {
                // map all key-value-pairs (EntityName + iCsnDefinition) to target CSN
                // Object.entries() returns the key-value-pairs of the object as an array
                for (const keyValuePair of Object.entries(typeCsn.definitions)) {
                  if (keyValuePair[0] && keyValuePair[1]) {
                    const typeName: string = keyValuePair[0];
                    const typeObject: any = keyValuePair[1];
                    completeCsn.definitions[typeName] = typeObject;
                  }
                }
              }
            }
          }
        }

        // add i18n definitions
        if (entitiesFromBackend.i18nDefinitions) {
          completeCsn.i18nDefinitions = entitiesFromBackend.i18nDefinitions;
          // in all usecases check consistency of placeholders vs. definitions
          this.i18nConsistencyCheck(completeCsn, importRequest);
        }

        // add onboarding hints
        if (entitiesFromBackend.onboardingHints && Object.keys(entitiesFromBackend.onboardingHints).length > 0) {
          completeCsn.onboardingHints = entitiesFromBackend.onboardingHints;
        }

        if (Object.entries(completeCsn.definitions).length > 0 && !this.hasError(res)) {
          // fill the csn into the IHybridCSN object
          const iHybridCsn: IHybridCsn = {
            schemaName: schema,
            entitiesToDeploy: completeCsn,
          };

          // data layer only
          // const createDataLayerOnly = dataLayerOnly;
          const dwcObjectTypes = dataLayerOnly;
          const targetFolder = importRequest.getTargetFolder();
          const targetFolderName = importRequest.getTargetFolderName();

          const hybridParserParameter: HybridParserParameter = {
            hybridCsn: iHybridCsn,
            remoteConnection: destinationId,
            context: batchContext,
            bw4Import: deployUsingBW,
            renameDataLayerObjects: false, // data layer objects shall not be renamed, business layer objects get prefix BE_
            // createOnlyDataLayerObjects: createDataLayerOnly,
            backendSapBasisRelease: this.destinationData?.sapBasisRelease,
            deployUsingCDI,
            deployUsingOnPremVT,
            remoteConnectionExtended,
            featureFlags,
            dwcObjectTypes,
          };

          if (importRequest.getAccessTechnology() === ImportAccessTechnology.OdcDataProducts) {
            // data product usecase: set the deployUsingDpApi flag
            // pass the API context
            // expectation is that contexts are built based on the API context and that the entities are renamed accordingly
            hybridParserParameter.deployUsingDpApi = true;
            hybridParserParameter.bw4Import =
              hybridParserParameter.deployUsingCDI =
              hybridParserParameter.deployUsingOnPremVT =
                false;
            if (namespaceContext) {
              hybridParserParameter.namespaceContext = namespaceContext;
            }
            if (apiContext) {
              hybridParserParameter.apiContext = apiContext;
            }
            if (importRequest.getRemoteAccessMode() === RemoteAccessMode.VIA_REMOTETABLES) {
              hybridParserParameter.deployUsingDpApiAsRemoteTables = true;
            }
          } else {
            // other usecases: pass the start entity name
            hybridParserParameter.entityName = importRequest.getImportStartEntity();
          }

          // set target space in usecases where target space is relevant
          let targetSpace: string | undefined;
          if (
            !importRequest.isBwBridgeScenario() ||
            // in bwbridge scenario the target space shall only be set if it is NOT a "remote tables" usecase
            (importRequest.isBwBridgeScenario() && !importRequest.isBwBridgeRemoteTablesScenario())
          ) {
            targetSpace = importRequest.spaceId!;
          }
          if (targetSpace) {
            hybridParserParameter.spaceId = targetSpace;
            if (importRequest.spaceWithSourceLanguage) {
              hybridParserParameter.spaceLangu = importRequest.getLanguForTextReplacement();
            }
          }

          if (importRequest.isBwBridgeScenario() === true) {
            hybridParserParameter.bwBridgeSpaceId = importRequest.getBwBridgeSpace();
            // hybridParserParameter.isBwBridgeRemoteTablesScenario = importRequest.isBwBridgeRemoteTablesScenario();
          }

          if (importRequest.getUseIngestionSpace() && importRequest.getIngestionSpace()) {
            hybridParserParameter.ingestionSpaceId = importRequest.getIngestionSpace();
            hybridParserParameter.importMeta = this.addImportMetaForContexts(importRequest);
          }

          const hybridParser = HybridParserFactory.createHybridParser(hybridParserParameter);
          const parseResult: IParseResult = await hybridParser.parse();
          delete parseResult.performanceLogger; // remove performance logger from response

          // map parse result
          if (parseResult) {
            if (parseResult.hasError || parseResult.hasWarnings) {
              const location = "Hybrid Parser";
              const errorId = "HybridParserError";
              this.logParserErrorsAndWarnings(res, parseResult, errorId, location);
            }

            // process the entities to deploy returned by the parser
            if (parseResult.objects && parseResult.objects.length > 0) {
              // DAC handling
              const importAuthorizations = importRequest.getImportAuthorizations() || false;
              // include permissions (BW Model Import case only)
              if (
                this.destinationData &&
                this.destinationData.connTypeId === TypeIds.SAPBWMODELTRANSFER &&
                this.destinationData.destinationId &&
                importRequest.getSpaceId() &&
                (await batchContext.isFeatureFlagActive("DWCO_MODELING_IMPORT_MANAGER_BW4_PERMISSION"))
              ) {
                // import DAC authorizations
                // this will add objects
                //  -- [Types.ERemoteDacHybridObjectType.protectedView]: DeployObjectType.Fact,
                //  -- [Types.ERemoteDacHybridObjectType.dacInput]: DeployObjectType.Fact,
                //  -- [Types.ERemoteDacHybridObjectType.dac]: DeployObjectType.DAC,
                //  -- [Types.ERemoteDacHybridObjectType.baseView]: DeployObjectType.Fact,
                //  -- [Types.ERemoteDacHybridObjectType.permissionsTable]: DeployObjectType.RemoteTable,
                const connectionFilter = {
                  name: this.destinationData.destinationId,
                  spaceIds: importRequest.getSpaceGUID(),
                };

                try {
                  await RemoteDacHybridImportHelper.extendParseResultWithoutReq(
                    batchContext,
                    importAuthorizations,
                    parseResult,
                    connectionFilter
                  );
                } catch (dacError) {
                  this.handleDacErrorOnImport(res, dacError, importRequest, batchContext);
                }
              }

              for (const csnObjectToDeploy of parseResult.objects) {
                // append the EntityToDeploy for the deployer
                if (!res.entitiesToDeploy) {
                  res.entitiesToDeploy = [];
                }
                res.entitiesToDeploy.push(csnObjectToDeploy);

                // consider for UI only the entities with Type <> SimpleType
                // collect the businessLayer/dataLayer property and the icon into the UI entity
                if (csnObjectToDeploy.type !== Type.SimpleType) {
                  const entityForUi: Entity = {};

                  // technical key
                  // set same as full-qualified
                  // e.g. "Remote.conn.entityName", or in dataProduct usecase without Remote and conn contexts: "apiContext:v1.entityName"
                  // in case of a namespaceContext, apiContext will be <namesp>.<api>:v1
                  // in this case <namesp> will be the 3rd component of the ORD DataProduct ID Prefix
                  // note: for "AlreadyExisting" entities it will later be overwritten with the Repo Guid
                  if (csnObjectToDeploy.name) {
                    entityForUi.entityId = csnObjectToDeploy.name; // key -> full-qualified name
                  }

                  // map original entity name (backend name)
                  if (csnObjectToDeploy.originalEntityName) {
                    // DW101-87999: in the DP usecase the "original entity name" can be a name with dpSchemaContext.EntityName
                    entityForUi.originalEntityName = csnObjectToDeploy.originalEntityName;
                    // DW101-87999: the "entityName" has to be without any prefixes
                    // this is used later for the assignment of _Delta entities, for the assignment of _Repflows
                    // and the definition has to remain stable - it has to remain unprefixed
                    // split off any prefixes
                    entityForUi.entityName = csnObjectToDeploy.originalEntityName.includes(".")
                      ? csnObjectToDeploy.originalEntityName.split(".").pop()
                      : csnObjectToDeploy.originalEntityName;
                    // NOTE:
                    // both the "originalEntityName" and the "entityName" will contain the suffix "_Delta" if entity is going to be deployed as delta-capture table
                    // "_Delta" is not "orginal" (from backend)
                    // this will remain in the names for consistency and key accesses during the process
                    // all key accesses to source (original from backend) have to be aware of this
                  }

                  // map qualified name (Remote.connection.backend name)
                  // this is the final prefixed name
                  // e.g. "Remote.conn.entityName", or in dataProduct usecase without Remote and conn contexts: "apiContext:v1.entityName"
                  // in case of a namespaceContext, apiContext will be <namesp>.<api>:v1
                  // in this case <namesp> will be the 3rd component of the ORD DataProduct ID Prefix
                  if (csnObjectToDeploy.name) {
                    entityForUi.qualifiedName = csnObjectToDeploy.name;
                  }

                  // map entity label
                  if (csnObjectToDeploy.endUserTextLabel) {
                    entityForUi.entityLabel = csnObjectToDeploy.endUserTextLabel;
                  }

                  // type
                  if (csnObjectToDeploy.type) {
                    entityForUi.entityType = csnObjectToDeploy.type;
                  }

                  // DAC type
                  if (csnObjectToDeploy.remoteDacObjectType) {
                    entityForUi.remoteDacObjectType = csnObjectToDeploy.remoteDacObjectType;
                  }

                  // DAC: was previously protected flag
                  if (csnObjectToDeploy.wasPreviouslyProtected) {
                    entityForUi.wasPreviouslyProtected = csnObjectToDeploy.wasPreviouslyProtected;
                    this.handleWasPreviouslyProtectedOnImport(
                      res,
                      importAuthorizations,
                      csnObjectToDeploy.name, // factView
                      csnObjectToDeploy.wasPreviouslyProtected,
                      importRequest,
                      batchContext
                    );
                  }

                  // properties from the StarSchema
                  // startEntity?: string; // entity for which the import has been started

                  // scope?: string; // Moonlight or StarSchema scope with which the import has been started

                  // from BW parser
                  if (csnObjectToDeploy.icon) {
                    entityForUi.icon = csnObjectToDeploy.icon;
                  }

                  if (csnObjectToDeploy.isBusinessLayerObject) {
                    entityForUi.isBusinessLayerObject = csnObjectToDeploy.isBusinessLayerObject;
                  }

                  // connection info
                  if (importRequest.getSpaceId()) {
                    entityForUi.spaceId = importRequest.getSpaceId();
                  }
                  if (importRequest.getSpaceGUID()) {
                    entityForUi.spaceGUID = importRequest.getSpaceGUID();
                  }
                  if (importRequest.getDestinationId()) {
                    entityForUi.connectionId = importRequest.getDestinationId();
                  }

                  // find the backend name for the parsed object (if it is an object which came from backend)
                  const entityWithCsn = entitiesFromBackend.entitiesWithCsn.find((row) =>
                    row.entity.originalEntityName
                      ? // in case it is an entity with originalEntityName (e.g. from catalog) map based on originalEntityName
                        // in this case the originalEntityName will be the same in csnToDeploy and in entitiesFromBackend
                        csnObjectToDeploy.originalEntityName &&
                        (csnObjectToDeploy.originalEntityName === row.entity.originalEntityName ||
                          csnObjectToDeploy.originalEntityName === `${row.entity.originalEntityName}_Delta`)
                      : // in case it is an entity without originalEntityName (e.g. from S4 or some unit tests) map based on entityName
                        // in that case the originalEntityName on csnToDeploy side must be the entityName
                        // example: UT EntityProcessor.spec.ts 001
                        row.entity.entityName &&
                        csnObjectToDeploy.originalEntityName &&
                        (csnObjectToDeploy.originalEntityName === row.entity.entityName ||
                          csnObjectToDeploy.originalEntityName === `${row.entity.entityName}_Delta`)
                  );
                  if (entityWithCsn && entityWithCsn.entity) {
                    // properties from the backend
                    if (entityWithCsn.entity.kind) {
                      entityForUi.kind = entityWithCsn.entity.kind;
                    }
                    if (entityWithCsn.entity.releaseContract) {
                      entityForUi.releaseContract = entityWithCsn.entity.releaseContract;
                    }
                    if (entityWithCsn.entity.releaseState) {
                      entityForUi.releaseState = entityWithCsn.entity.releaseState;
                    }
                    if (entityWithCsn.entity.modelingPattern) {
                      entityForUi.modelingPattern = entityWithCsn.entity.modelingPattern;
                    }
                    if (entityWithCsn.entity.applicationComponent) {
                      entityForUi.applicationComponent = entityWithCsn.entity.applicationComponent;
                    }
                    if (entityWithCsn.entity.softwareComponent) {
                      entityForUi.softwareComponent = entityWithCsn.entity.softwareComponent;
                    }
                    if (entityWithCsn.entity.lastModifiedAt) {
                      entityForUi.lastModifiedAt = new Date(entityWithCsn.entity.lastModifiedAt);
                    }
                    if (entityWithCsn.entity.entityOrdId) {
                      entityForUi.entityOrdId = entityWithCsn.entity.entityOrdId;
                    }
                    if (entityWithCsn.entity.catalogUuid) {
                      entityForUi.catalogUuid = entityWithCsn.entity.catalogUuid;
                    }
                    if (entityWithCsn.entity.schema) {
                      entityForUi.schema = entityWithCsn.entity.schema;
                    }
                    if (entityWithCsn.entity.apiOrdId) {
                      entityForUi.apiOrdId = entityWithCsn.entity.apiOrdId;
                    }
                    if (entityWithCsn.entity.namespaceContext) {
                      entityForUi.namespaceContext = entityWithCsn.entity.namespaceContext;
                    }
                    if (entityWithCsn.entity.apiContext) {
                      entityForUi.apiContext = entityWithCsn.entity.apiContext;
                    }
                    if (entityWithCsn.entity.runtimeContainerPath) {
                      entityForUi.runtimeContainerPath = entityWithCsn.entity.runtimeContainerPath;
                    }
                    if (entityWithCsn.entity.uclId) {
                      entityForUi.uclId = entityWithCsn.entity.uclId;
                    }
                    if (entityWithCsn.entity.genericOrdId) {
                      entityForUi.genericOrdId = entityWithCsn.entity.genericOrdId;
                    }
                  } else {
                    entityForUi.isGeneratedEntity = true;
                  }

                  if (targetFolder) {
                    entityForUi.targetFolder = targetFolder;
                  }
                  if (targetFolderName) {
                    entityForUi.targetFolderName = targetFolderName;
                  }

                  res.entitiesForUi.push(entityForUi);
                }
              }
            }

            // add the onboarding hints
            if (parseResult.onboardingHints) {
              res.onboardingHints = parseResult.onboardingHints;
            }

            // add the fullCsn to the result to pass it to the businss layer parser
            // if (requestFullCsn || parseResult.hasError) {
            //  res.fullCsn = completeCsn;
            // }
            res.parserParametersWithFullCsn = { ...hybridParserParameter, context: {} as any, featureFlags: {} as any };
          }
        }
      }
    }

    // add the original table from backend to the result
    if (entitiesFromBackend.entitiesWithCsn) {
      res.entitiesFromBackend = entitiesFromBackend.entitiesWithCsn;
    }

    // map errors from the backend (from import structure)
    if (entitiesFromBackend.httpError) {
      this.addError(res, entitiesFromBackend.httpError, "httpErrorFromBackend", entitiesFromBackend.errorLocation);
    }
    if (entitiesFromBackend.statusMessage && entitiesFromBackend.statusMessage.toUpperCase() !== "OK") {
      this.addErrorMessage(res, entitiesFromBackend.statusMessage);
    }
    if (entitiesFromBackend.statusCode) {
      res.statusCode = entitiesFromBackend.statusCode;
    }
    return res;
  }

  public async parseGetImportableEntities(
    entitiesFromBackend: EntitiesFromBackend,
    batchContext: IRequestContext,
    importRequest: ImportRequest,
    featureFlags: IFeatureFlagsMap
  ): Promise<ImportableEntities> {
    const res: ImportableEntities = {};

    res.entitiesForUi = [];

    if (entitiesFromBackend) {
      const destinationId: string = this.destinationData?.destinationId ? this.destinationData?.destinationId : "";

      // get CSN from entities from backend
      if (entitiesFromBackend.entitiesWithCsn && entitiesFromBackend.entitiesWithCsn.length > 0) {
        // instantiate value table
        const valueTable: Value[] = [];

        let startEntity: string | undefined;
        let scope: string | undefined;
        if (importRequest.getAccessTechnology() !== ImportAccessTechnology.OdcDataProducts) {
          startEntity = importRequest.getImportStartEntity();
          scope = importRequest.getCsnExposureFilterSchema();
        }

        // fetch api context and namespace context from entities (could also be derived implicitly from onboaring hint key)
        let namespaceContext: string | undefined;
        let apiContext: string | undefined;

        // map backend entities to S4UI entities
        for (const entityFromBackend of entitiesFromBackend.entitiesWithCsn) {
          if (entityFromBackend.entity.entityName) {
            const s4UIEntity: Value = {
              EntityName: startEntity || "",
              Scope: scope || "",
              EntityNameInScope: entityFromBackend.entity.entityName,
              _Entity: {
                EntityName: entityFromBackend.entity.entityName,
                EntityLabel: entityFromBackend.entity.entityLabel,
                ReleaseContract: entityFromBackend.entity.releaseContract,
                ReleaseState: entityFromBackend.entity.releaseState,
                ModelingPattern: entityFromBackend.entity.modelingPattern,
                ApplicationComponent: entityFromBackend.entity.applicationComponent,
                SoftwareComponent: entityFromBackend.entity.softwareComponent,
                LastModifiedAt: entityFromBackend.entity.lastModifiedAt,
                UclId: entityFromBackend.entity.entityOrdId,
                ApiOrdId: entityFromBackend.entity.apiOrdId,
                NamespaceContext: entityFromBackend.entity.namespaceContext,
                ApiContext: entityFromBackend.entity.apiContext,
                RuntimeContainerPath: entityFromBackend.entity.runtimeContainerPath,
              } as S4UIEntity,
            };
            valueTable.push(s4UIEntity);
            // collect api context and namespace context
            // all entities imported via same API - will be same for all
            apiContext = entityFromBackend.entity.apiContext;
            namespaceContext = entityFromBackend.entity.namespaceContext;
          }
        }

        const rootObject: RootObject = {
          "@odata.context": "",
          "@odata.metadataEtag": "",
          "@odata.count": entitiesFromBackend.entitiesWithCsn.length,
          value: valueTable,
        };

        const targetFolder = importRequest.getTargetFolder();
        const dwcObjectTypes = importRequest.getDwcObjectTypes();

        // fill the parameter for the S4UI parser
        const hybridParserParameterS4UI: HybridParserParameterS4UI = {
          hybridCsn: rootObject,
          remoteConnection: destinationId,
          context: batchContext,
          targetFolder,
          dwcObjectTypes,
          featureFlags,
        };

        if (importRequest.getAccessTechnology() === ImportAccessTechnology.OdcDataProducts) {
          hybridParserParameterS4UI.deployUsingDpApi = true;
          if (apiContext) {
            hybridParserParameterS4UI.apiContext = apiContext;
          }
          if (namespaceContext) {
            hybridParserParameterS4UI.namespaceContext = namespaceContext;
          }
        }

        // set target space in usecases where target space is relevant
        let targetSpace: string | undefined;
        if (
          !importRequest.isBwBridgeScenario() ||
          // in bwbridge scenario the target space shall only be set if it is NOT a "remote tables" usecase
          (importRequest.isBwBridgeScenario() && !importRequest.isBwBridgeRemoteTablesScenario())
        ) {
          targetSpace = importRequest.spaceId!;
        }
        if (targetSpace) {
          hybridParserParameterS4UI.spaceId = targetSpace;
          if (importRequest.spaceWithSourceLanguage) {
            hybridParserParameterS4UI.spaceLangu = importRequest.getLanguForTextReplacement();
          }
        }

        if (importRequest.isBwBridgeScenario()) {
          hybridParserParameterS4UI.bwBridgeSpaceId = importRequest.getBwBridgeSpace();
        }

        if (importRequest.getUseIngestionSpace()) {
          const ingestionSpaceHandler = new IngestionSpaceHandler(this.destinationData);
          if (importRequest.getIngestionSpace()) {
            // already fetched and saved in import request
            hybridParserParameterS4UI.ingestionSpaceId = importRequest.getIngestionSpace();
          } else {
            const ingestionSpaceNameProposal = await ingestionSpaceHandler.getIngestionSpaceName(batchContext);
            hybridParserParameterS4UI.ingestionSpaceId = ingestionSpaceNameProposal.ingestionSpaceName;
          }
        }

        // call S4UI parser
        const hybridParserS4UI = new HybridParserS4UI(hybridParserParameterS4UI);
        const parseResultS4UI = await hybridParserS4UI.parse();

        // map parse result
        if (parseResultS4UI) {
          if (parseResultS4UI.hasError) {
            // S4UI parser result object does not have warnings, just check for errors
            const location = "Hybrid Parser (S4UI)";
            const errorId = "Parse Importable Entities Error";
            this.logParserErrorsAndWarnings(res, parseResultS4UI as IParseResult, errorId, location);
          }

          if (parseResultS4UI.objects && parseResultS4UI.objects.length > 0) {
            // DAC handling
            const importAuthorizations = importRequest.getImportAuthorizations() || false;
            // include permissions (BW Model Import case only)
            if (
              this.destinationData &&
              this.destinationData.connTypeId === TypeIds.SAPBWMODELTRANSFER &&
              this.destinationData.destinationId &&
              importRequest.getSpaceId() &&
              (await batchContext.isFeatureFlagActive("DWCO_MODELING_IMPORT_MANAGER_BW4_PERMISSION"))
            ) {
              // import DAC authorizations
              // this will add objects
              //  -- [Types.ERemoteDacHybridObjectType.protectedView]: DeployObjectType.Fact,
              //  -- [Types.ERemoteDacHybridObjectType.dacInput]: DeployObjectType.Fact,
              //  -- [Types.ERemoteDacHybridObjectType.dac]: DeployObjectType.DAC,
              //  -- [Types.ERemoteDacHybridObjectType.baseView]: DeployObjectType.Fact,
              //  -- [Types.ERemoteDacHybridObjectType.permissionsTable]: DeployObjectType.RemoteTable,
              const connectionFilter = {
                name: this.destinationData.destinationId,
                spaceIds: importRequest.getSpaceGUID(),
              };

              // extend parse result with DAC entities if requested
              try {
                await RemoteDacEntityProcessorHelper.extendParseResult(
                  batchContext,
                  importAuthorizations,
                  parseResultS4UI.objects,
                  connectionFilter
                );
              } catch (dacError) {
                this.handleDacErrorOnImport(res, dacError, importRequest, batchContext);
              }
            }

            // fill the UI table
            for (const object of parseResultS4UI.objects) {
              // append the EntityToDeploy for the deployer

              const entityForUi: Entity = {};

              // technical key
              // set same as fill-qualified
              // e.g. "Remote.conn.entityName", or in dataProduct usecase without Remote and conn contexts: "apiContext:v1.entityName"
              // in case of a namespaceContext, apiContext will be <namesp>.<api>:v1
              // in this case <namesp> will be the 3rd component of the ORD DataProduct ID Prefix
              // note: for "AlreadyExisting" entities it will later be overwritten with the Repo Guid
              if (object.name) {
                entityForUi.entityId = object.name; // key -> full-qualified name
              }

              // map original entity name (backend name)
              if (object.originalEntityName) {
                // DW101-87999: in the DP usecase the "original entity name" can be a name with dpSchemaContext.EntityName
                entityForUi.originalEntityName = object.originalEntityName;
                // DW101-87999: the "entityName" has to be without any prefixes
                // this is used later for the assignment of _Delta entities, for the assignment of _Repflows
                // and the definition has to remain stable - it has to remain unprefixed
                // split off any prefixes
                entityForUi.entityName = object.originalEntityName.includes(".")
                  ? object.originalEntityName.split(".").pop()
                  : object.originalEntityName;
              }

              // map qualified name (Remote.connection.backend name)
              // this is the final prefixed name
              // e.g. "Remote.conn.entityName", or in dataProduct usecase without Remote and conn contexts: "apiContext:v1.entityName"
              // in case of a namespaceContext, apiContext will be <namesp>.<api>:v1
              // in this case <namesp> will be the 3rd component of the ORD DataProduct ID Prefix
              if (object.name) {
                entityForUi.qualifiedName = object.name;
              }

              // map entity label
              if (object.endUserTextLabel) {
                entityForUi.entityLabel = object.endUserTextLabel;
              }

              // type
              if (object.type) {
                entityForUi.entityType = object.type;
              }

              // DAC type
              if (object.remoteDacObjectType) {
                entityForUi.remoteDacObjectType = object.remoteDacObjectType;
              }

              // DAC: was previously protected flag
              if (object.wasPreviouslyProtected) {
                entityForUi.wasPreviouslyProtected = object.wasPreviouslyProtected;
                // set warning if a Fact View has DAC protection and will get unprotected on reimport without DAC
                this.handleWasPreviouslyProtectedOnImport(
                  res,
                  importAuthorizations,
                  object.name, // factView
                  object.wasPreviouslyProtected,
                  importRequest,
                  batchContext
                );
              }

              // properties from the StarSchema
              entityForUi.startEntity = startEntity;

              // scope?: string; // Moonlight or StarSchema scope with which the import has been started
              entityForUi.scope = scope;

              // from BW parser
              if (object.icon) {
                entityForUi.icon = object.icon;
              }

              if (object.isBusinessLayerObject) {
                entityForUi.isBusinessLayerObject = object.isBusinessLayerObject;
              }

              // connection info
              if (importRequest.getSpaceId()) {
                entityForUi.spaceId = importRequest.getSpaceId();
              }
              if (importRequest.getSpaceGUID()) {
                entityForUi.spaceGUID = importRequest.getSpaceGUID();
              }
              if (importRequest.getDestinationId()) {
                entityForUi.connectionId = importRequest.getDestinationId();
              }

              // find the backend name for the parsed object (if it is an object which came from backend)
              const entityWithCsn = entitiesFromBackend.entitiesWithCsn.find(
                (row) =>
                  row.entity.entityName &&
                  object.originalEntityName &&
                  object.originalEntityName === row.entity.entityName
              );
              if (entityWithCsn && entityWithCsn.entity) {
                // properties from the backend
                if (entityWithCsn.entity.kind) {
                  entityForUi.kind = entityWithCsn.entity.kind;
                }
                if (entityWithCsn.entity.releaseContract) {
                  entityForUi.releaseContract = entityWithCsn.entity.releaseContract;
                }
                if (entityWithCsn.entity.releaseState) {
                  entityForUi.releaseState = entityWithCsn.entity.releaseState;
                }
                if (entityWithCsn.entity.modelingPattern) {
                  entityForUi.modelingPattern = entityWithCsn.entity.modelingPattern;
                }
                if (entityWithCsn.entity.applicationComponent) {
                  entityForUi.applicationComponent = entityWithCsn.entity.applicationComponent;
                }
                if (entityWithCsn.entity.softwareComponent) {
                  entityForUi.softwareComponent = entityWithCsn.entity.softwareComponent;
                }
                if (entityWithCsn.entity.lastModifiedAt) {
                  entityForUi.lastModifiedAt = new Date(entityWithCsn.entity.lastModifiedAt);
                }
                if (entityWithCsn.entity.entityOrdId) {
                  entityForUi.entityOrdId = entityWithCsn.entity.entityOrdId;
                }
                if (entityWithCsn.entity.catalogUuid) {
                  entityForUi.catalogUuid = entityWithCsn.entity.catalogUuid;
                }
                if (entityWithCsn.entity.schema) {
                  entityForUi.schema = entityWithCsn.entity.schema;
                }
                if (entityWithCsn.entity.apiOrdId) {
                  entityForUi.apiOrdId = entityWithCsn.entity.apiOrdId;
                }
                if (entityWithCsn.entity.namespaceContext) {
                  entityForUi.namespaceContext = entityWithCsn.entity.namespaceContext;
                }
                if (entityWithCsn.entity.apiContext) {
                  entityForUi.apiContext = entityWithCsn.entity.apiContext;
                }
                if (entityWithCsn.entity.runtimeContainerPath) {
                  entityForUi.runtimeContainerPath = entityWithCsn.entity.runtimeContainerPath;
                }
                if (entityWithCsn.entity.uclId) {
                  entityForUi.uclId = entityWithCsn.entity.uclId;
                }
                if (entityWithCsn.entity.genericOrdId) {
                  entityForUi.genericOrdId = entityWithCsn.entity.genericOrdId;
                }
                // end user label
                if (entityWithCsn.entity.entityLabel) {
                  entityForUi.entityLabel = entityWithCsn.entity.entityLabel;
                }
              } else {
                entityForUi.isGeneratedEntity = true;
              }

              // target folder
              if (targetFolder) {
                entityForUi.targetFolder = targetFolder;
              }

              res.entitiesForUi.push(entityForUi);
            }
          }
        }
      }
    }

    // add the original table from backend to the result
    if (entitiesFromBackend.entitiesWithCsn) {
      res.entitiesFromBackend = entitiesFromBackend.entitiesWithCsn;
    }

    // add the onboarding hints
    if (entitiesFromBackend.onboardingHints) {
      res.onboardingHints = entitiesFromBackend.onboardingHints;
    }

    // map errors from the backend (from import structure)
    if (entitiesFromBackend.httpError) {
      this.addError(res, entitiesFromBackend.httpError, "httpErrorFromBackend", entitiesFromBackend.errorLocation);
    }
    if (entitiesFromBackend.statusMessage) {
      this.addErrorMessage(res, entitiesFromBackend.statusMessage);
    }
    if (entitiesFromBackend.statusCode) {
      res.statusCode = entitiesFromBackend.statusCode;
    }
    return res;
  }

  public async parseBusinessLayerEntities(
    iCsn: ICsn,
    batchContext: IRequestContext,
    importRequest: ImportRequest,
    featureFlags: IFeatureFlagsMap,
    parseRepairedCsnMode?: boolean
  ): Promise<ImportableEntities> {
    const res: ImportableEntities = {};
    res.entitiesForUi = [];

    const destinationId: string = this.destinationData?.destinationId ? this.destinationData?.destinationId : "";
    const schema = this.getSchemaName(this.destinationData);

    const iHybridCsn: IHybridCsn = {
      schemaName: schema,
      entitiesToDeploy: iCsn,
    };

    let deployUsingBW = false;
    let deployUsingCDI = false;
    let deployUsingOnPremVT = false;

    if (
      this.destinationData &&
      this.destinationData.connTypeId &&
      this.destinationData.connTypeId === TypeIds.SAPS4HANAOP
    ) {
      if (importRequest.getRemoteAccessMode() !== RemoteAccessMode.VIA_REPFLOW) {
        if (importRequest.getAccessTechnology() === ImportAccessTechnology.VirtualTable) {
          deployUsingOnPremVT = false;
        } else {
          deployUsingCDI = true;
        }
      }
    }

    if (
      this.destinationData &&
      this.destinationData.connTypeId &&
      this.destinationData.connTypeId === TypeIds.SAPS4HANACLOUD
    ) {
      // S/4HANA Cloud
      // for RemoteAccessMode !== VIA_REPFLOW deploy via CDI
      if (importRequest.getRemoteAccessMode() !== RemoteAccessMode.VIA_REPFLOW) {
        deployUsingCDI = true;
      }
    }

    if (
      this.destinationData &&
      this.destinationData.connTypeId &&
      (this.destinationData.connTypeId === TypeIds.SAPBWMODELTRANSFER ||
        this.destinationData.connTypeId === TypeIds.SAPBWBRIDGE)
    ) {
      // BW: always deploy using BW
      deployUsingBW = true;
    }

    const targetFolder = importRequest.getTargetFolder();
    const dwcObjectTypes = importRequest.getDwcObjectTypes();

    // build hybrid parser parameters to parse the business layer
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: iHybridCsn,
      remoteConnection: destinationId,
      context: batchContext,
      bw4Import: deployUsingBW,
      spaceId: importRequest.spaceId!,
      renameDataLayerObjects: false, // data layer objects shall not be renamed, business layer objects get prefix BE_
      // createOnlyDataLayerObjects: false, // create business layer
      backendSapBasisRelease: this.destinationData?.sapBasisRelease,
      deployUsingCDI,
      deployUsingOnPremVT,
      featureFlags,
      parseRepairedCsnMode,
      dwcObjectTypes,
    };

    // for import of Queries and Cubes from a BW system --> need to pass to parser always the original fullCsn
    // an need to set parameter "createOnlyBusinessLayerObjects" on true for the save step of the BE-objects
    // if (deployUsingBW) {
    //  hybridParserParameter.createOnlyBusinessLayerObjects = true;
    // }
    // if it is a bw bridge scenario: pass bridge space
    if (importRequest.isBwBridgeScenario()) {
      hybridParserParameter.bwBridgeSpaceId = importRequest.getBwBridgeSpace();
    }

    if (importRequest.spaceWithSourceLanguage) {
      hybridParserParameter.spaceLangu = importRequest.getLanguForTextReplacement();
    }

    // parse the business layer
    const hybridParser = HybridParserFactory.createHybridParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    delete parseResult.performanceLogger; // do not return the performance logger to the client

    // map parse result
    if (parseResult) {
      // log parser errors and warnings
      if (parseResult.hasError || parseResult.hasWarnings) {
        const location = "Hybrid Parser (Business Layer)";
        const errorId = "HybridParserErrorForBusinessLayer";
        this.logParserErrorsAndWarnings(res, parseResult, errorId, location);
      }

      // map business layer objects from the parse result into the entities to deploy
      if (parseResult.objects && parseResult.objects.length > 0) {
        for (const csnObjectToDeploy of parseResult.objects) {
          // append the EntityToDeploy for the deployer
          if (!res.entitiesToDeploy) {
            res.entitiesToDeploy = [];
          }
          res.entitiesToDeploy.push(csnObjectToDeploy);

          // map the other properties
          if (csnObjectToDeploy.type !== Type.SimpleType) {
            const entityForUi: Entity = {};

            // technical key
            // set same as fill-qualified
            // e.g. "Remote.conn.entityName", or in dataProduct usecase without Remote and conn contexts: "apiContext:v1.entityName"
            // in case of a namespaceContext, apiContext will be <namesp>.<api>:v1
            // in this case <namesp> will be the 3rd component of the ORD DataProduct ID Prefix
            // note: for "AlreadyExisting" entities it will later be overwritten with the Repo Guid
            if (csnObjectToDeploy.name) {
              entityForUi.entityId = csnObjectToDeploy.name; // key -> full-qualified name
            }

            // map original entity name (backend name)
            if (csnObjectToDeploy.originalEntityName) {
              // DW101-87999: in the DP usecase the "original entity name" can be a name with dpSchemaContext.EntityName
              entityForUi.originalEntityName = csnObjectToDeploy.originalEntityName;
              // DW101-87999: the "entityName" has to be without any prefixes
              // this is used later for the assignment of _Delta entities, for the assignment of _Repflows
              // and the definition has to remain stable - it has to remain unprefixed
              // split off any prefixes
              entityForUi.entityName = csnObjectToDeploy.originalEntityName.includes(".")
                ? csnObjectToDeploy.originalEntityName.split(".").pop()
                : csnObjectToDeploy.originalEntityName;
            }

            // map qualified name (Remote.connection.backend name)
            // this is the final prefixed name
            // e.g. "Remote.conn.entityName", or in dataProduct usecase without Remote and conn contexts: "apiContext:v1.entityName"
            // in case of a namespaceContext, apiContext will be <namesp>.<api>:v1
            // in this case <namesp> will be the 3rd component of the ORD DataProduct ID Prefix
            if (csnObjectToDeploy.name) {
              entityForUi.qualifiedName = csnObjectToDeploy.name;
            }

            // map entity label
            if (csnObjectToDeploy.endUserTextLabel) {
              entityForUi.entityLabel = csnObjectToDeploy.endUserTextLabel;
            }

            // type
            if (csnObjectToDeploy.type) {
              entityForUi.entityType = csnObjectToDeploy.type;
            }

            // properties from the StarSchema
            // startEntity?: string; // entity for which the import has been started

            // scope?: string; // Moonlight or StarSchema scope with which the import has been started

            // from BW parser
            if (csnObjectToDeploy.icon) {
              entityForUi.icon = csnObjectToDeploy.icon;
            }

            // connection info
            if (importRequest.getSpaceId()) {
              entityForUi.spaceId = importRequest.getSpaceId();
            }
            if (importRequest.getSpaceGUID()) {
              entityForUi.spaceGUID = importRequest.getSpaceGUID();
            }
            if (importRequest.getDestinationId()) {
              entityForUi.connectionId = importRequest.getDestinationId();
            }

            // all entities processed in this method are business layer entities
            entityForUi.isBusinessLayerObject = csnObjectToDeploy.isBusinessLayerObject;
            entityForUi.isGeneratedEntity = true;

            // target folder
            if (targetFolder) {
              entityForUi.targetFolder = targetFolder;
            }

            res.entitiesForUi.push(entityForUi);
          }
        }
      }
    }

    return res;
  }

  public async fetchEntities(
    importRequest: ImportRequest,
    httpHandler: HttpHandler,
    batchContext: IRequestContext,
    includeLocalization?: boolean
  ): Promise<EntitiesFromBackend> {
    // Call to CSN Exposure Service
    // This method calls the CSN_Exposure service in packages of the give blocksize to fetch the entities

    // The call depends on the import step:
    //  a) Process step "GetImportableEntities"
    //     Call CSN_Exposure to fetch the entity headers (without _Source and _Localization)
    //  b) Process step "ExecuteImport"
    //     Call CSN_Exposure to fetch full CSN

    const entitiesFromBackend: EntitiesFromBackend = { entitiesWithCsn: [], statusCode: 200, statusMessage: "" };

    // Process step "GetEntityListForUI"
    if (importRequest.getProcessStep() === ImportProcessStep.getEntityListForUI) {
      // check the volume of the import ...
      // as rule, if there are more than 100 entities we will stop there and ignore the fetch of the rest of the entities

      let skip = 0;
      let entitiesFromBackendPackaged: EntitiesFromBackend = {
        entitiesWithCsn: [],
        onboardingHints: {},
        statusCode: 200,
        statusMessage: "",
      };
      // get the entities from backend packaged
      do {
        entitiesFromBackendPackaged = await httpHandler.getEntitiesFromBackend(
          importRequest,
          batchContext,
          includeLocalization,
          skip,
          BATCH_SIZE
        );
        // fill the entities of the package in entitiesFromBackend (contains the total entities)
        if (entitiesFromBackendPackaged.entitiesWithCsn && entitiesFromBackendPackaged.entitiesWithCsn.length > 0) {
          for (const e1 of entitiesFromBackendPackaged.entitiesWithCsn) {
            entitiesFromBackend.entitiesWithCsn?.push(e1);
          }

          // map onboarding hints (api and dataProduct information)
          if (
            entitiesFromBackendPackaged.onboardingHints &&
            Object.keys(entitiesFromBackendPackaged.onboardingHints).length > 0
          ) {
            for (const key of Object.keys(entitiesFromBackendPackaged.onboardingHints)) {
              if (!entitiesFromBackend.onboardingHints) {
                entitiesFromBackend.onboardingHints = {};
              }
              entitiesFromBackend.onboardingHints[key] = entitiesFromBackendPackaged.onboardingHints[key];
            }
          }

          skip = skip + BATCH_SIZE;
          // if there are more than 100 entities we will stop here and ignore the fetch of the rest of the entities
          if (skip >= BATCH_SIZE) {
            // lv_not_all_entities_fetched = true;
            break;
          }

          // no more entites from backend --> break the do-loop
        } else {
          if (entitiesFromBackendPackaged.statusCode) {
            entitiesFromBackend.statusCode = entitiesFromBackendPackaged.statusCode;
          }
          if (entitiesFromBackendPackaged.statusMessage) {
            entitiesFromBackend.statusMessage = entitiesFromBackendPackaged.statusMessage;
          }
          if (entitiesFromBackendPackaged.httpError) {
            entitiesFromBackend.httpError = entitiesFromBackendPackaged.httpError;

            // set error location
            if (this.destinationData && importRequest.getAccessTechnology() === ImportAccessTechnology.VirtualTable) {
              entitiesFromBackend.errorLocation = "SELECT Entity from Virtual Table";
            } else if (importRequest.getAccessTechnology() === ImportAccessTechnology.OdcDataProducts) {
              entitiesFromBackend.errorLocation = "SELECT Entity from Data Catalog";
            } else {
              entitiesFromBackend.errorLocation = "CSN_EXPOSURE Service";
            }
          }
          break;
        }
      } while (
        entitiesFromBackendPackaged.entitiesWithCsn &&
        entitiesFromBackendPackaged.entitiesWithCsn.length === BATCH_SIZE
      );
    } else {
      // process step "Import"
      // fetch all entities in blocks of 100

      let skip = 0;
      let entitiesFromBackendPackaged: EntitiesFromBackend = {
        entitiesWithCsn: [],
        onboardingHints: {},
        statusCode: 200,
        statusMessage: "",
      };
      // get the entities from backend packaged
      do {
        entitiesFromBackendPackaged = await httpHandler.getEntitiesFromBackend(
          importRequest,
          batchContext,
          includeLocalization,
          skip,
          BATCH_SIZE
        );
        // fill the entities of the package in entitiesFromBackend (contains the total entities)
        if (entitiesFromBackendPackaged.entitiesWithCsn && entitiesFromBackendPackaged.entitiesWithCsn.length > 0) {
          for (const e1 of entitiesFromBackendPackaged.entitiesWithCsn) {
            entitiesFromBackend.entitiesWithCsn?.push(e1);
          }

          // map onboarding hints (api and dataProduct information)
          if (
            entitiesFromBackendPackaged.onboardingHints &&
            Object.keys(entitiesFromBackendPackaged.onboardingHints).length > 0
          ) {
            for (const key of Object.keys(entitiesFromBackendPackaged.onboardingHints)) {
              if (!entitiesFromBackend.onboardingHints) {
                entitiesFromBackend.onboardingHints = {};
              }
              entitiesFromBackend.onboardingHints[key] = entitiesFromBackendPackaged.onboardingHints[key];
            }
          }

          skip = skip + BATCH_SIZE;

          // no more entites from backend --> break the do-loop
        } else {
          if (entitiesFromBackendPackaged.statusCode) {
            entitiesFromBackend.statusCode = entitiesFromBackendPackaged.statusCode;
          }
          if (entitiesFromBackendPackaged.statusMessage) {
            entitiesFromBackend.statusMessage = entitiesFromBackendPackaged.statusMessage;
          }
          if (entitiesFromBackendPackaged.httpError) {
            entitiesFromBackend.httpError = entitiesFromBackendPackaged.httpError;

            // set error location
            if (this.destinationData && importRequest.getAccessTechnology() === ImportAccessTechnology.VirtualTable) {
              entitiesFromBackend.errorLocation = "SELECT Entity from Virtual Table";
            } else if (importRequest.getAccessTechnology() === ImportAccessTechnology.OdcDataProducts) {
              entitiesFromBackend.errorLocation = "SELECT Entity from Data Catalog";
            } else {
              entitiesFromBackend.errorLocation = "CSN_EXPOSURE Service";
            }
          }
          break;
        }
      } while (
        entitiesFromBackendPackaged.entitiesWithCsn &&
        entitiesFromBackendPackaged.entitiesWithCsn.length === BATCH_SIZE
      );
    }
    return entitiesFromBackend;
  }

  private getSchemaName(destinationData: DestinationData | undefined): string {
    let schemaName = "SCHEMA";

    // set Schema for BWMODELTRANSFER and SAPBWBRIDGE connections
    if (
      destinationData &&
      destinationData.connTypeId &&
      (destinationData.connTypeId === TypeIds.SAPBWMODELTRANSFER || destinationData.connTypeId === TypeIds.SAPBWBRIDGE)
    ) {
      if (destinationData.bwHanaSchema) {
        schemaName = destinationData.bwHanaSchema;
      } else {
        schemaName = "";
      }
    }
    return schemaName;
  }

  private async entitiesFetchLastImportStatus(
    entities: ImportableEntities,
    batchContext: IRequestContext,
    importRequest: ImportRequest
  ) {
    // check if repflow assignments should be fetched together with already imported entities
    // this checks for example FF prerequisites for repflow assignments
    const fetchRepflowAssignments = await importRequest.checkFetchRepflowAssignments(batchContext);

    // call the DWC repository to check if the entities have already been imported
    // and fetch import status and deployment status
    if (entities && entities.entitiesForUi && entities.entitiesForUi.length > 0) {
      await this.setImportStatusForDLEntities(entities, importRequest, fetchRepflowAssignments, batchContext);
      await this.setImportStatusForBLEntities(entities, importRequest, batchContext);
    }
  }

  private async setImportStatusForBLEntities(
    entities: ImportableEntities,
    importRequest: ImportRequest,
    batchContext: IRequestContext
  ) {
    const blObjects = entities.entitiesForUi?.filter((entity) => entity.isBusinessLayerObject);
    await this.setImportStatus(blObjects, batchContext, importRequest, false);
  }

  private async setImportStatusForDLEntities(
    entities: ImportableEntities,
    importRequest: ImportRequest,
    fetchRepflowAssignments: boolean,
    batchContext: IRequestContext
  ) {
    // create sub-array of data layer objects with references to the original entities
    const dlObjects = entities.entitiesForUi?.filter((entity) => !entity.isBusinessLayerObject);
    await this.setImportStatus(dlObjects, batchContext, importRequest, fetchRepflowAssignments);
    // entities.existingRepositoryObjects = existingObjects;  // remove from type definition - not needed outside
  }

  private async setImportStatus(
    dlObjects: Entity[] | undefined,
    batchContext: IRequestContext,
    importRequest: ImportRequest,
    fetchRepflowAssignments: boolean
  ) {
    if (dlObjects && dlObjects.length > 0) {
      // get array of qualified names
      // formally filtering: where qualifiedName is not undefined
      const qualifiedEntityNames: string[] = dlObjects
        .map((dlEntity) => dlEntity.qualifiedName)
        ?.filter((n) => typeof n === "string") as string[];
      let existingObjects: ImportExistingObject[] = [];
      if (qualifiedEntityNames && qualifiedEntityNames.length > 0) {
        // search data layer objects in repository
        existingObjects = await this.getExistingObjects(
          batchContext,
          importRequest,
          qualifiedEntityNames,
          fetchRepflowAssignments
        );
      }
      // map the status back
      if (existingObjects && existingObjects.length > 0) {
        for (const uiEntity of dlObjects) {
          let repObjectName = existingObjects.find((row) => row.name === uiEntity.qualifiedName);
          if (!repObjectName && uiEntity.qualifiedName?.endsWith("_Delta")) {
            const qualifiedNameWoDelta = uiEntity.qualifiedName?.replace(/_Delta$/, "");
            repObjectName = existingObjects.find((row) => row.name === qualifiedNameWoDelta);
          }
          if (repObjectName) {
            uiEntity.importStatus = "Already imported";
            if (!uiEntity.isBusinessLayerObject && repObjectName.deploymentDate === undefined) {
              uiEntity.importStatus = "Already imported but not deployed";
            }
            uiEntity.importedOn =
              typeof repObjectName.deploymentDate === "string"
                ? new Date(repObjectName.deploymentDate)
                : repObjectName.deploymentDate || undefined;
            uiEntity.entityId = repObjectName.id;
            // map the current folder in the "oldFolder" attribute
            // the folder may be overwritten with the target folder in the import process
            if (repObjectName.folderId) {
              uiEntity.oldFolder = repObjectName.folderId;
            }
            if (repObjectName.folderName) {
              uiEntity.oldFolderName = repObjectName.folderName;
            }
            if (fetchRepflowAssignments && repObjectName.replicationFlowName) {
              uiEntity.replicationFlow = repObjectName.replicationFlowName;
              uiEntity.repflowAssignmentStatus = RepflowAssignmentStatus.AlreadyAssigned;
              if (repObjectName.replicationFlowBusinessName) {
                uiEntity.replicationFlowBusinessName = repObjectName.replicationFlowBusinessName;
              }
            }
            // ... maybe map further properties
          } else {
            uiEntity.importStatus = "Ready for Import";
          }
        }
      } else {
        dlObjects.forEach((row) => (row.importStatus = "Ready for Import"));
      }
    }
  }

  async getExistingObjects(
    batchContext: IRequestContext,
    importRequest: ImportRequest,
    qualifiedEntityNames: string[],
    fetchRepflowAssignments: boolean
  ): Promise<ImportExistingObject[]> {
    const existingEntities: ImportExistingObject[] = [];

    // if qualifiedEntityNames contains "_Delta" entities, we need to search for the original entity names as well
    if (qualifiedEntityNames.some((name) => name.endsWith("_Delta"))) {
      const originalQualifiedEntityNames: string[] = [];
      for (const name of qualifiedEntityNames) {
        if (name.endsWith("_Delta")) {
          originalQualifiedEntityNames.push(name.replace(/_Delta$/, ""));
        }
      }
      qualifiedEntityNames = qualifiedEntityNames.concat(originalQualifiedEntityNames);
    }

    // check if some of the qualified names contain characters ":" or "~"
    let escapeRequired = false;
    if (qualifiedEntityNames.some((name) => name.includes(":") || name.includes("~"))) {
      escapeRequired = true;
    }

    if (qualifiedEntityNames.length > 0) {
      // build the string of involved entity-qualified_names separated by OR --> to be used in the search filter
      // like  qualified_name:EQ:Ent1 OR qualified_name:EQ:Ent2 OR ... qualified_name:EQ:Entn
      // const spaceQuery = `space_id:EQ:${importRequest.getSpaceGUID()}`;

      // check if the import request is for ingestion space or not
      // depending on the request, the spaceQuery will be different
      let spaceQuery = "";
      if (importRequest.getUseIngestionSpace() && importRequest.getIngestionSpace()) {
        if (importRequest.ingestionSpaceDetails && importRequest.ingestionSpaceDetails.id) {
          spaceQuery = `space_id:EQ:${importRequest.ingestionSpaceDetails.id}`;
        } else {
          spaceQuery = `space_id:EQ:${importRequest.getSpaceGUID()}`;
        }
      }

      // build entity list as InList
      let entityList = "";
      if (escapeRequired) {
        entityList = `( qualified_name:OR("${qualifiedEntityNames.join(`" "`)}") )`;
      } else {
        entityList = `( qualified_name:OR(${qualifiedEntityNames.join(" ")}) )`;
      }

      // build requested fields list
      let requestedFields = `id,qualified_name,name,deployment_date`;

      requestedFields = `${requestedFields},folder_id_ext,folder_name`;

      // compose query for the repository search
      const params: IRepoCallSearchParams = {
        query: `/repository/search/$all?$top=${qualifiedEntityNames.length}&$skip=0&$select=${requestedFields}&$apply=filter(Search.search(query='SCOPE:SEARCH_DESIGN ${spaceQuery} AND ${entityList}'))`,
        language: "en",
      };

      try {
        // call the search
        const searchResult = (await RepositoryObjectClient.callGetSearch(batchContext, params)) as ISearchResults;
        if (searchResult && searchResult.value && searchResult.value.length > 0) {
          for (const result of searchResult.value) {
            const entity = {} as ImportExistingObject;
            if (result.id) {
              entity.id = result.id;
            }
            if (result.qualified_name) {
              entity.name = result.qualified_name;
            }
            if (result.name) {
              entity.name = result.name;
            }
            if (result.deployment_date) {
              entity.deploymentDate = result.deployment_date;
            }
            if (result.folder_id_ext) {
              entity.folderId = result.folder_id_ext;
              // return the folder technical name in entity.properties
              if (result.folder_name) {
                entity.folderName = result.folder_name;
              }
            }
            existingEntities.push(entity);
          }

          if (fetchRepflowAssignments && existingEntities.length > 0) {
            const objids = existingEntities.map((entity) => entity.id);
            // fetch repflows for all entities found in search result and return repflow as additional result property
            const repflowDependencies: IRepositoryElementDependency[] =
              await RepositoryObjectClient.getObjectDependencies(batchContext, {
                ids: objids,
                // kind: ObjectKind.replicationflow,
                details: ["business_name"],
                dependencyTypes: [DependencyKind.disReplicationFlowTargetOf, DependencyKind.query],
                impact: false,
                lineage: true,
                recursive: true,
              });

            if (repflowDependencies && repflowDependencies.length > 0) {
              for (const dependency of repflowDependencies) {
                if (
                  dependency.id &&
                  dependency.dependencies &&
                  dependency.dependencies.length === 1 &&
                  existingEntities.filter((e) => e.id === dependency.id).length > 0
                ) {
                  if (dependency.dependencies[0].dependencyType === "sap.dis.replicationflow.targetOf") {
                    if (dependency.dependencies[0].name) {
                      // this contains the replication flow name

                      const entity = existingEntities.filter((e) => e.id === dependency.id)[0];
                      // map the repflow name and business name
                      entity.replicationFlowName = dependency.dependencies[0].name;
                      entity.replicationFlowBusinessName = dependency.dependencies[0].businessName || "";
                    }
                  } else if (dependency.dependencies[0].dependencyType === "csn.query.from") {
                    if (
                      dependency.dependencies[0].dependencies &&
                      dependency.dependencies[0].dependencies.length === 1 &&
                      dependency.dependencies[0].dependencies[0].dependencyType ===
                        "sap.dis.replicationflow.targetOf" &&
                      dependency.dependencies[0].dependencies[0].name
                    ) {
                      // this contains the replication flow name
                      const entity = existingEntities.filter((e) => e.id === dependency.id)[0];
                      // map the repflow name and business name
                      entity.replicationFlowName = dependency.dependencies[0].dependencies[0].name;
                      entity.replicationFlowBusinessName =
                        dependency.dependencies[0].dependencies[0].businessName || "";
                    }
                  }
                }
              }
            }
          }
        }
      } catch (err) {
        if (err) {
          // error occured during search
          logError(`[EntityProcessor] [getExistingObjects] [callRepositorySearch] Error in repository search`, {
            context: batchContext,
          });
        }
      }
    }
    return existingEntities;
  }

  private async checkOnboardedApiConfig(
    entitiesFromBackend: EntitiesFromBackend,
    importRequest: ImportRequest,
    batchContext: IRequestContext
  ): Promise<{ [k: string]: any } | undefined> {
    let onboardedConfig: { [k: string]: any } = {};
    if (
      importRequest &&
      importRequest.getAccessTechnology() === ImportAccessTechnology.OdcDataProducts &&
      importRequest.getUseIngestionSpace() &&
      importRequest.getIngestionSpace()
    ) {
      const ingestionSpaceDetails = importRequest.getIngestionSpaceDetails();
      if (ingestionSpaceDetails && ingestionSpaceDetails.status === IngestionSpaceStatus.exists) {
        const ingestionSpaceId = ingestionSpaceDetails.id;

        // get apiResource ordId from the entitiesFromBackend
        let ordId: string | undefined;
        if (
          entitiesFromBackend &&
          entitiesFromBackend.onboardingHints &&
          entitiesFromBackend.entitiesWithCsn &&
          entitiesFromBackend.entitiesWithCsn.length > 0
        ) {
          // get ord id from the first entity, all entitites are imported via the same api
          if (entitiesFromBackend.entitiesWithCsn[0].entity && entitiesFromBackend.entitiesWithCsn[0].entity.apiOrdId) {
            ordId = entitiesFromBackend.entitiesWithCsn[0].entity.apiOrdId;
          }
        }

        // get onboarded content for the given api
        if (ingestionSpaceId && ingestionSpaceId.length > 0 && ordId) {
          onboardedConfig.ordId = ordId;
          try {
            // get the onboarded content for the given
            const onboardedContent = await RepositoryObjectClient.getObject(batchContext, {
              folderIds: [ingestionSpaceId],
              filters: [`#apiResourceOrdId:${ordId}`],
              details: [
                "kind",
                "id",
                "name",
                // "csn",
                "business_name",
                "#dataProductOrdId",
                "#apiResourceOrdId",
                "creation_date",
                "#objectStatus",
                "#technicalType",
              ],
            });

            if (onboardedContent && onboardedContent.length > 0) {
              onboardedConfig.onboardedContent = onboardedContent;
              // get the default access mode to be used for this API in the given ingestion spac
              // logic:
              // - if onboarded content contains at least one entity with technical type DWC_REMOTE_TABLE assume remote access mode VIA_REMOTETABLES
              // - in all other usecases assume access mode VIA_REPFLOW
              const remoteTables = onboardedContent.filter(
                (row) =>
                  row.properties && (row.properties as { [k: string]: any })["#technicalType"] === "DWC_REMOTE_TABLE"
              );
              if (remoteTables && remoteTables.length > 0) {
                // remote table found (at least one)
                onboardedConfig.remoteAccessModeFound = true;
                onboardedConfig.remoteAccessMode = RemoteAccessMode.VIA_REMOTETABLES;
              } else {
                const repflowObj = onboardedContent.filter((row) => row.kind === ObjectKind.replicationflow);
                if (repflowObj) {
                  // set "found" only if actually found
                  onboardedConfig.remoteAccessModeFound = true;
                }
                // but return VIA_REPFLOW in any case
                onboardedConfig.remoteAccessMode = RemoteAccessMode.VIA_REPFLOW;
              }
            } else {
              onboardedConfig.notYetOnboarded = true;
            }
          } catch (err) {
            onboardedConfig.repoCallEndedWithError = true;
            onboardedConfig.error = err;
          }
        } else {
          onboardedConfig.noOrdId = true; // technical issue
          onboardedConfig.notYetOnboarded = true;
        }
      } else {
        // no ingestion space
        onboardedConfig.noIngestionSpace = true;
        onboardedConfig.notYetOnboarded = true;
      }
      return onboardedConfig;
    } else {
      // not an ODC DataProduct import using ingestion space
      return undefined;
    }
  }

  private readAnalyticalDataCategory(entityObject: any): string {
    let dataCategorySuffix = "$E";
    const dataCategory1 = entityObject["@Analytics.dataCategory"]?.["#"];
    const dataCategory2 = entityObject["@ObjectModel.dataCategory"]?.["#"];

    // if no @Analytics.dataCategory
    if (!dataCategory1 || (dataCategory1 && dataCategory1 === " ")) {
      // no @ObjectModel.dataCategory
      if (!dataCategory2 || (dataCategory2 && dataCategory2 === " ")) {
        dataCategorySuffix = "$E";
      } else if (
        (!dataCategory1 || (dataCategory1 && dataCategory1 === " ")) &&
        dataCategory2 &&
        dataCategory2 !== " "
      ) {
        switch (dataCategory2) {
          case "DIMENSION":
          case "#DIMENSION":
            dataCategorySuffix = "$P";
            break;
          case "TEXT":
          case "#TEXT":
            dataCategorySuffix = "$T";
            break;
          case "HIERARCHY":
          case "#HIERARCHY":
            dataCategorySuffix = "$H";
            break;
          case "VALUE_HELP":
          case "#VALUE_HELP":
            dataCategorySuffix = "$V";
            break;
          default:
            dataCategorySuffix = "$F";
            break;
        }
      }
    } else if (dataCategory1 && dataCategory1 !== " ") {
      switch (dataCategory1) {
        case "ANALYTICAL_DIMENSION":
        case "DIMENSION":
        case "#ANALYTICAL_DIMENSION":
        case "#DIMENSION":
          dataCategorySuffix = "$P";
          break;
        case "ANALYTICAL_TEXT":
        case "TEXT":
        case "#ANALYTICAL_TEXT":
        case "#TEXT":
          dataCategorySuffix = "$T";
          break;
        case "ANALYTICAL_HIERARCHY":
        case "HIERARCHY":
        case "#ANALYTICAL_HIERARCHY":
        case "#HIERARCHY":
          dataCategorySuffix = "$H";
          break;
        case "VALUE_HELP":
        case "#VALUE_HELP":
        case "ANALYTICAL_VALUE_HELP":
        case "#ANALYTICAL_VALUE_HELP":
          dataCategorySuffix = "$V";
          break;
        default:
          dataCategorySuffix = "$F";
          break;
      }
    }
    return dataCategorySuffix;
  }

  public checkEntityDataExtractabilityCriteria(
    deploymentAPI: DeploymentAPI,
    entityName: string,
    // hybridParserCsnExposure passes ICsn as fullCsn (case: "CDI"), ordProcessor passes ICsnObjectToDeploy (case: "RepFlow/APE")
    fullCsn: ICsn | ICsnObjectToDeploy, // both contain definitions: ICsnDefinitions, releaseContract.definitions
    destinationData?: DestinationData,
    runtimeContainerPath?: string // case: "HDL" (RepFLow for DataProducts)
  ): boolean {
    let isExtractable = false;

    switch (deploymentAPI) {
      case DeploymentAPI.CDI:
      case DeploymentAPI.APE:
        let hasReleaseContractC1 = false;
        let isCustomerEntity = false;
        let deployableAsRemoteEntity = false;
        let deployableAsRepflow = false;

        // get CSN definitions either directly (HybridParser) or by remote-name (ORD Processor)
        const csnDefinitions =
          fullCsn && fullCsn.definitions && fullCsn.definitions[entityName]
            ? fullCsn.definitions[entityName]
            : undefined;

        // check property dataExtractionEnabled
        const hasAnnoExtractionEnabled =
          csnDefinitions && (csnDefinitions as any)["@Analytics.dataExtraction.enabled"]
            ? (csnDefinitions as any)["@Analytics.dataExtraction.enabled"]
            : false;

        if (hasAnnoExtractionEnabled) {
          // in case it is a SAP owned entity:
          // check release contract == 'C1'
          if (
            fullCsn["releaseContract.definitions"] &&
            fullCsn["releaseContract.definitions"][entityName] &&
            fullCsn["releaseContract.definitions"][entityName].releaseContract
          ) {
            const releaseContract = fullCsn["releaseContract.definitions"][entityName].releaseContract;
            hasReleaseContractC1 = releaseContract === "C1" ? true : false;
          }

          // if it hasn't release contract C1
          // check if it is a Custom-defined entity
          if (!hasReleaseContractC1) {
            // temporary check:
            // check abap_meta: { ... abapLanguageVersion === 2 && entity name starts with 'YY1_' }
            // these entities do not have release contract C1 but are extractable
            const abapMeta = (csnDefinitions as any).abap_meta;
            if (abapMeta) {
              // YY1_ --> start of custom entity name in HybridParser CsnExposure
              // Remote.${destinationData.destinationId}.YY1_ --> start of custom entity name in ORD Processor
              if (
                abapMeta.abapLanguageVersion === 2 &&
                (entityName.startsWith("YY1_") ||
                  (destinationData &&
                    destinationData.destinationId &&
                    entityName.startsWith(`Remote.${destinationData.destinationId}.YY1_`)))
              ) {
                isCustomerEntity = true;
              }
            }
          }

          // check if the entity is deployable if the API is CDI
          if (csnDefinitions && deploymentAPI === DeploymentAPI.CDI) {
            deployableAsRemoteEntity = this.checkDeploymentAsRemoteEntity(csnDefinitions, destinationData)
              ? true
              : false;
          } else if (deploymentAPI === DeploymentAPI.APE) {
            deployableAsRepflow = true; // not relevant for APE
          }
        }

        // set isExtractable flag
        if (
          hasAnnoExtractionEnabled &&
          (hasReleaseContractC1 || isCustomerEntity) &&
          (deployableAsRemoteEntity || deployableAsRepflow)
        ) {
          isExtractable = true;
        }

        break;

      case DeploymentAPI.BUSINESS_DATA_PRODUCT:
        // e.g. DL Service API
        // for Service API, the entity is always extractable
        if (runtimeContainerPath) {
          isExtractable = true;
        }
        break;

      default:
        break;
    }

    return isExtractable;
  }

  public checkDeploymentAsRemoteEntity(csnDefinition: ICsnDefinition, destinationData?: DestinationData): boolean {
    let asRemoteTable = true;

    if (csnDefinition) {
      const dataCategory = this.readAnalyticalDataCategory(csnDefinition);

      if (dataCategory === "$T") {
        // check if the fields are only keys and one text field
        const defValueElements = csnDefinition.elements;
        if (defValueElements !== undefined) {
          // check if the count of remained fields is less as the count of original fields
          let textFieldCounter = 0;
          let nontextFieldCounter = 0;
          for (const elemVal of Object.values(defValueElements)) {
            if ((elemVal as any).key && (elemVal as any).key === true) {
              // it is a key field - key fields are allowed
              // continue
            } else if ((elemVal as any).type && (elemVal as any).type.includes("Association")) {
              // it is an association and not a field, do not consider
              // continue
            } else if (
              ((elemVal as any)["@Semantics.text"] && (elemVal as any)["@Semantics.text"] === true) ||
              ((elemVal as any)["@Semantics.businessDate.from"] &&
                (elemVal as any)["@Semantics.businessDate.from"] === true) ||
              ((elemVal as any)["@Semantics.businessDate.to"] &&
                (elemVal as any)["@Semantics.businessDate.to"] === true) ||
              ((elemVal as any)["@Semantics.systemDate.createdAt"] &&
                (elemVal as any)["@Semantics.systemDate.createdAt"] === true) ||
              ((elemVal as any)["@Semantics.systemDate.lastChangedAt"] &&
                (elemVal as any)["@Semantics.systemDate.lastChangedAt"] === true) ||
              ((elemVal as any)["@Semantics.systemDateTime.createdAt"] &&
                (elemVal as any)["@Semantics.systemDateTime.createdAt"] === true) ||
              ((elemVal as any)["@Semantics.systemDateTime.lastChangedAt"] &&
                (elemVal as any)["@Semantics.systemDateTime.lastChangedAt"] === true)
            ) {
              // it is a field with semantic text or date
              textFieldCounter = textFieldCounter + 1;
            } else {
              // the fields without annos above will be hidden in CDI service
              nontextFieldCounter = nontextFieldCounter + 1;
            }
          }
          // create remote table only if there is at least one field annotated with @Semantics.text:true and no non text fields
          if (
            (textFieldCounter >= 1 && nontextFieldCounter === 0) ||
            (textFieldCounter === 0 && nontextFieldCounter === 1)
          ) {
            asRemoteTable = true;
          } else {
            asRemoteTable = false;
          }
        }
      } else if (dataCategory === "$H") {
        asRemoteTable = false;
      }
    }

    return asRemoteTable;
  }

  private logParserErrorsAndWarnings(
    object: ImportableEntities,
    parseResult: IParseResult,
    errorId: string,
    location: string
  ) {
    // log parser errors
    if (parseResult.hasError) {
      if (parseResult.stack) {
        this.addError(object, { hasError: true }, errorId, location, parseResult.stack);
      } else {
        this.addError(object, { hasError: true }, errorId, location);
      }
      if (parseResult.messages) {
        for (const message of parseResult.messages) {
          const noRegexCheck = true;
          const splitParserMessages = true;
          this.addErrorMessage(object, message, noRegexCheck, splitParserMessages);
        }
      }
    }
    // log parser warnings
    if (parseResult.hasWarnings && parseResult.warnings) {
      if (location) {
        object.warningLocation = location;
      }
      for (const warning of parseResult.warnings) {
        this.addWarnings(object, warning);
      }
    }
  }

  private handleDacErrorOnImport(
    object: ImportableEntities,
    dacError: any,
    importRequest: ImportRequest,
    batchContext: IRequestContext
  ) {
    if (dacError.code && dacError.code === DacTypes.DacErrors.permissionsTableNotFound) {
      // dac not possible - dac table not implemented on backend
      const uiMessage: ITaskLogMessage = {
        severity: Severity.INFO,
        text:
          "Import Authorizations not possible: " &&
          "Your BW/4HANA backend system does not expose the Data Access Control permissions table. " &&
          "To enable the feature implement the infrastructure for Data Access Control feature on your BW/4HANA system",
        // i18n message bundle ...
      };
      if (!(importRequest.getProcessStep() === ImportProcessStep.executeImport)) {
        // synchronous: display popup with message on UI and disable "Import Authorizations" button
        importRequest.setUiNotification(uiMessage);
        const dacCheckboxDisabled: UiElemStatus = { value: false, disabled: true };
        importRequest.setUiElementStatus("ImportAuthorizations", dacCheckboxDisabled);
      } else {
        // write infos to log
        this.addInfos(object, uiMessage.text!);
        this.addInfos(object, "Fact View will be imported without Data Access Controls");
      }
      logInfo(`[EntityProcessor] [ParseEntities] [DataAccessControl] [ExtendParseresults] ${uiMessage.text!}`, {
        context: batchContext,
      });
    } else if (dacError.code && DacTypes.DacErrors.missingFactViewFromHybridModel) {
      // the selected entity does not have a fact view, this is not an error, dac simply not relevant in the current case
      const uiMessage: ITaskLogMessage = {
        severity: Severity.INFO,
        text: "Import Authoriztions not relevant for this entity: No Fact View in scope",
        // i18n message bundle ...
      };
      if (!(importRequest.getProcessStep() === ImportProcessStep.executeImport)) {
        // synchronous: display popup with message on UI
        importRequest.setUiNotification(uiMessage);
        // do not generally disable DAC in this case - this error depends on the selected entity, for other entities DAC can be set
      } else {
        this.addInfos(object, uiMessage.text!);
      }
      logInfo(`[EntityProcessor] [ParseEntities] [DataAccessControl] [ExtendParseresults] ${uiMessage.text!}`, {
        context: batchContext,
      });
    } else {
      // other DAC errors
      const uiMessage: ITaskLogMessage = {
        severity: Severity.ERROR,
        text:
          "Import Authorizations not possible: " &&
          `Data Access Control handler returns ${dacError.code ? dacError.code : "error"}`,
        // i18n message bundle ...
      };
      const errorId = "Data Access Control Error";
      const errorLocation = "Data Access Control: Extend Parse Results";
      if (!(importRequest.getProcessStep() === ImportProcessStep.executeImport)) {
        // synchronous: display popup with message on UI and disable "Import Authorizations" button
        importRequest.setUiNotification(uiMessage);
        const dacCheckboxDisabled: UiElemStatus = { value: false, disabled: true };
        importRequest.setUiElementStatus("ImportAuthorizations", dacCheckboxDisabled);
      } else {
        // async - on actual import: hard error (DAC exception)
        this.addErrorMessage(object, uiMessage.text!);
        this.addError(object, { dacError }, errorId, errorLocation);
      }
      logError(`[EntityProcessor] [ParseEntities] [DataAccessControl] [ExtendParseresults] ${uiMessage.text!}`, {
        context: batchContext,
      });
    }
  }

  private handleWasPreviouslyProtectedOnImport(
    object: ImportableEntities,
    importAuthorizations: boolean | undefined,
    factView: string,
    wasPreviouslyProtected: boolean | undefined,
    importRequest: ImportRequest,
    batchContext: IRequestContext
  ) {
    // set warning if a Fact View has DAC protection and will get unprotected on reimport without DAC
    if (factView && wasPreviouslyProtected && !importAuthorizations) {
      const uiMessage: ITaskLogMessage = {
        severity: Severity.WARNING,
        text:
          "Data Access Control Warning: " &&
          `Fact View ${factView} was authorization protected before. You are re-importing without authorizations: Fact View will lose its Data Access Control protection`,
        // i18n message bundle ...
      };
      if (!(importRequest.getProcessStep() === ImportProcessStep.executeImport)) {
        // synchronous: set popup with the warning on the UI
        importRequest.setUiNotification(uiMessage);
      } else {
        // async: write the warning to the log (also adding location if it is the first warning)
        if (!object.warningLocation) {
          object.warningLocation = "Data Access Control: Extend Parser Result";
        }
        this.addWarnings(object, uiMessage.text!);
      }
      logWarning(`[EntityProcessor] [ParseEntities] [DataAccessControl] ${uiMessage.text!}`, {
        context: batchContext,
      });
    }
  }

  private addError(
    object: ImportableEntities,
    importerror: any,
    errorId: string,
    errorLocation?: string,
    errorStack?: any
  ) {
    const msg = importerror.message;
    if (msg && msg !== "" && msg !== "ok") {
      this.addErrorMessage(object, msg); // map the message from the error object
    }
    if (importerror.messages && importerror.messages.length > 0) {
      for (const msg of importerror.messages) {
        if (msg !== "ok" && msg !== "") {
          this.addErrorMessage(object, msg);
        }
      }
    }
    if (!object.error) {
      object.error = {};
    }
    object.error[errorId] = importerror;
    // write the error location -> only for the first error
    if (errorLocation) {
      if (!object.errorLocation) {
        object.errorLocation = errorLocation;
      }
    }
    if (errorStack) {
      if (!object.errorStack) {
        object.errorStack = errorStack;
      }
    }
  }

  private addErrorMessage(
    object: ImportableEntities,
    message: string,
    noRegexCheck?: boolean,
    splitParserMessages?: boolean
  ) {
    const specialCharRegex = /[<>\[\]{}!@#%^&*~+=`\\|]/;
    if (
      message &&
      message !== "" &&
      message !== "ok" && // do not log the http-200 "ok" messages
      (noRegexCheck || // caller decides that the message shall appear in the log (independent of regex chars)
        !specialCharRegex.test(message)) // do not log json/xml or other technical messages here: exclude everything containing tech chars
    ) {
      if (!object.errorMessages) {
        object.errorMessages = [];
      }
      if (splitParserMessages && message.includes("\n")) {
        Array.prototype.push.apply(object.errorMessages, message.split("\n"));
      } else {
        object.errorMessages.push(message);
      }
    }
  }

  private addWarnings(object: ImportableEntities, message: string | string[]) {
    if (!object.warnings) {
      object.warnings = [];
    }
    if (Array.isArray(message)) {
      Array.prototype.push.apply(object.warnings, message);
    } else if (typeof message === "string" && message.includes("\n")) {
      Array.prototype.push.apply(object.warnings, message.split("\n"));
    } else if (typeof message === "string") {
      object.warnings.push(message);
    }
  }

  private addInfos(object: ImportableEntities, message: string | string[]) {
    if (!object.infos) {
      object.infos = [];
    }
    if (Array.isArray(message)) {
      Array.prototype.push.apply(object.infos, message);
    } else if (typeof message === "string" && message.includes("\n")) {
      Array.prototype.push.apply(object.infos, message.split("\n"));
    } else if (typeof message === "string") {
      object.infos.push(message);
    }
  }

  private addObjectToBeLogged(object: ImportableEntities, objectToBeLogged: { [k: string]: any } | undefined) {
    // add the original object from backend (catalog api) so that it can be logged
    if (objectToBeLogged && Object.keys(objectToBeLogged).length > 0) {
      object.objectToBeLogged = objectToBeLogged;
    }
  }

  private hasError(object: ImportableEntities): boolean {
    if (object.error && Object.keys(object.error).length > 0) {
      // object.error !== {}
      return true;
    } else {
      return false;
    }
  }

  private localizationHandling(
    entities: EntitiesFromBackend,
    textReplacementLangu: string | undefined,
    i18nHandlingMode: ImportI18nHandlingMode | undefined,
    i18nLanguMappings: ImportI18nLanguMapping[] | undefined,
    batchContext: IRequestContext
  ): EntitiesFromBackend {
    entities.i18nDefinitions = {};
    if (!entities.contentError) {
      entities.contentError = {};
    }
    if (!entities.warnings) {
      entities.warnings = [];
    }

    if (entities.entitiesWithCsn && entities.entitiesWithCsn.length > 0) {
      for (const entity of entities.entitiesWithCsn) {
        if (entity._source && entity._source.sourceString && entity._source._localizationData) {
          // map only language EN
          if (i18nHandlingMode === ImportI18nHandlingMode.replaceTextLabels && !textReplacementLangu) {
            textReplacementLangu = "en";
          }

          let i18nString = "";

          // CSN exposure returns the _localizationData as array
          // we read the item for the locale langu
          if (Array.isArray(entity._source._localizationData)) {
            if (!i18nHandlingMode || i18nHandlingMode === ImportI18nHandlingMode.replaceTextLabels) {
              // text replacement scenario:
              // extract the expected replacement language from the _localizationData array
              // and process the i18n string for this language
              const localizationData = entity._source._localizationData.find((i) => i.locale === textReplacementLangu);
              if (localizationData && localizationData.i18nString && entity._source.sourceString) {
                entity._source.sourceString = this.processI18nStringReplace(
                  batchContext,
                  entity._source.objectName,
                  localizationData.i18nString,
                  i18nHandlingMode,
                  localizationData.locale,
                  entity._source.sourceString,
                  entities.contentError
                );
              }
            } else if (i18nHandlingMode === ImportI18nHandlingMode.passI18nAsObject) {
              // pass i18n sections in entity
              // process i18ns for all included languages
              for (const localizationData of entity._source._localizationData) {
                if (localizationData.i18nString) {
                  this.mapI18nBlocks(
                    batchContext,
                    entity._source.objectName,
                    localizationData.i18nString,
                    i18nHandlingMode,
                    i18nLanguMappings, // all langu mappings
                    entities.i18nDefinitions,
                    entities.contentError,
                    entities.warnings
                  );
                }
              }
              // check i18n blocks (space source lanuaguage provided)
              this.checkI18nBlocks(
                batchContext,
                entity._source.objectName,
                i18nHandlingMode,
                i18nLanguMappings,
                entities.i18nDefinitions,
                entities.warnings
              );
            }
          } else if (typeof entity._source._localizationData === "string") {
            // the import from virtual table returns only the i18nString as a string
            // which we put into "_localizationData" in the response handler
            // - old comment -: this string can contain many languages, we cannot control the language here
            // - however -: will factually only contain a single language, the virtual table access is only able to return a single language
            i18nString = entity._source._localizationData;
            if (!i18nHandlingMode || i18nHandlingMode === ImportI18nHandlingMode.replaceTextLabels) {
              // extract the expected replacement language from the _localizationData array
              // and process the i18n string for this language
              if (i18nString && entity._source.sourceString) {
                entity._source.sourceString = this.processI18nStringReplace(
                  batchContext,
                  entity._source.objectName,
                  i18nString,
                  i18nHandlingMode,
                  textReplacementLangu,
                  entity._source.sourceString,
                  entities.contentError
                );
              }
            } else if (i18nHandlingMode === ImportI18nHandlingMode.passI18nAsObject) {
              if (i18nString) {
                this.mapI18nBlocks(
                  batchContext,
                  entity._source.objectName,
                  i18nString,
                  i18nHandlingMode,
                  i18nLanguMappings, // all langu mappings
                  entities.i18nDefinitions,
                  entities.contentError,
                  entities.warnings
                );
              }
              // check i18n blocks (space source lanuaguage provided)
              this.checkI18nBlocks(
                batchContext,
                entity._source.objectName,
                i18nHandlingMode,
                i18nLanguMappings,
                entities.i18nDefinitions,
                entities.warnings
              );
            }
          }
        }
      } // for (const entity of entities.entitiesWithCsn)
    }

    return entities;
  }

  mapI18nBlocks(
    context: IRequestContext,
    entityName: string | undefined,
    i18nString: string | undefined,
    i18nHandlingMode: ImportI18nHandlingMode | undefined,
    i18nLanguMappings: ImportI18nLanguMapping[] | undefined,
    entityI18nDefinitions: II18nDefinitions | undefined,
    _contentError: { [k: string]: any } | undefined,
    _warnings: string[] | undefined
  ) {
    if (entityName && i18nString && i18nHandlingMode === ImportI18nHandlingMode.passI18nAsObject) {
      try {
        const parsedLocalizationObj = JSON.parse(i18nString);
        this.mapI18nSectionToEntityObject(parsedLocalizationObj, i18nLanguMappings, entityName, entityI18nDefinitions);
      } catch (err) {
        // JSON Parser error for the localization data: I18N string from backend is invalid or incomplete
        _contentError = {
          hasError: true,
          message: `Error in Localization Data: Language-dependent label texts cannot be parsed, I18N string for entity ${
            entityName ?? ""
          } from backend is invalid or incomplete`,
          location: "Localization Data Handling: Process I18N Blocks",
          errorObj: err,
        };
        logError(
          `[EntityProcessor] [LocalizationHandling] [JSON Parse] Error in Localization Data: Language-dependent label texts cannot be parsed, I18N string for entity ${
            entityName ?? ""
          } from backend is invalid or incomplete`,
          { context }
        );
      }
    }
  }

  public mapI18nSectionToEntityObject(
    localitazionObj: { [key: string]: any },
    i18nLanguMappings: ImportI18nLanguMapping[] | undefined,
    entityName: string | undefined,
    entityI18nDefinitions: II18nDefinitions | undefined
  ) {
    // map the i18n input
    let i18n: { [k: string]: any } = {};
    if (localitazionObj && localitazionObj.i18n) {
      i18n = localitazionObj.i18n;
    } else if (localitazionObj && localitazionObj.I18N) {
      i18n = localitazionObj.I18N;
    }

    if (entityName && i18n) {
      // initialize i18n section in the entity definitions
      if (!entityI18nDefinitions) {
        entityI18nDefinitions = {};
      }
      if (!entityI18nDefinitions[entityName]) {
        entityI18nDefinitions[entityName] = {};
      }
      if (!entityI18nDefinitions[entityName].i18n) {
        entityI18nDefinitions[entityName].i18n = {};
      }
      // map i18n contents
      const locales = Object.keys(i18n);
      if (locales && locales.length > 0) {
        for (const locale of locales) {
          if (i18n[locale] && entityI18nDefinitions[entityName].i18n) {
            // map incoming locale
            if (!(entityI18nDefinitions[entityName].i18n as II18nContent)[locale]) {
              (entityI18nDefinitions[entityName].i18n as II18nContent)[locale] = i18n[locale];
            }
            if (i18nLanguMappings) {
              for (const languMapping of i18nLanguMappings.filter((languMap) => languMap.backendLangu === locale)) {
                // record that the locale has been processed
                languMapping.receivedFromBackend = true;
                // mapping required? e.g. case: space definition "fr-CA", backend langu "fr" -> add an i18n block for langu "fr-CA"
                if (
                  languMapping &&
                  languMapping.backendLangu &&
                  languMapping.backendLangu !== languMapping.locale &&
                  !(entityI18nDefinitions[entityName].i18n as II18nContent)[languMapping.locale]
                ) {
                  (entityI18nDefinitions[entityName].i18n as II18nContent)[languMapping.locale] = i18n[locale];
                }
              }
            }
          }
        }
      }
    }
  }

  checkI18nBlocks(
    context: IRequestContext,
    entityName: string | undefined,
    i18nHandlingMode: ImportI18nHandlingMode | undefined,
    i18nLanguMappings: ImportI18nLanguMapping[] | undefined,
    entityI18nDefinitions: II18nDefinitions | undefined,
    _warnings: string[] | undefined
  ) {
    // check if the space source language has been povided
    if (
      i18nLanguMappings &&
      i18nLanguMappings.find((languMap) => languMap.role === ImportI18nLanguRole.spaceSourceLangu)
    ) {
      const spaceSourceLangu = i18nLanguMappings.find(
        (languMap) => languMap.role === ImportI18nLanguRole.spaceSourceLangu
      )?.locale;
      if (
        entityName &&
        spaceSourceLangu &&
        entityI18nDefinitions &&
        entityI18nDefinitions[entityName] &&
        !(entityI18nDefinitions[entityName].i18n as II18nContent)[spaceSourceLangu]
      ) {
        // add an empty block for the space source language
        (entityI18nDefinitions[entityName].i18n as II18nContent)[spaceSourceLangu] = {};
        // add a warning that the space source language is missing
        if (!_warnings) {
          _warnings = [];
        }
        _warnings.push(
          `Language-dependent text labels missing for entity ${entityName}, space source language '${spaceSourceLangu}' not supplied - save entity with technical name and attribute names instead`
        );
      }
    }
  }

  processI18nStringReplace(
    context: IRequestContext,
    entityName: string | undefined,
    i18nString: string | undefined,
    i18nHandlingMode: ImportI18nHandlingMode | undefined,
    replacementLangu: string | undefined,
    entitySourceString: string,
    _contentError: { [k: string]: any } | undefined
  ): string {
    if (entityName && i18nString && i18nHandlingMode === ImportI18nHandlingMode.replaceTextLabels) {
      try {
        // parse the i18nString into an object
        const parsedLocalizationObj = JSON.parse(i18nString);
        // replace language dependent texts
        entitySourceString = this.replaceLanguageDependentTexts(
          parsedLocalizationObj,
          entitySourceString,
          replacementLangu
        );
      } catch (err) {
        // JSON Parser error for the localization data: I18N string from backend is invalid or incomplete
        _contentError = {
          hasError: true,
          message: `Error in Localization Data: Language-dependent label texts cannot be parsed, I18N string for entity ${
            entityName ?? ""
          } from backend is invalid or incomplete`,
          location: "Localization Data Handling: Process I18N String - Replace Text-Labels",
          errorObj: err,
        };
        logError(
          `[EntityProcessor] [LocalizationHandling] [JSON Parse] Error in Localization Data: Language-dependent label texts cannot be parsed, I18N string for entity ${
            entityName ?? ""
          } from backend is invalid or incomplete`,
          { context }
        );
      }
    }
    return entitySourceString;
  }

  public replaceLanguageDependentTexts(
    localitazionObj: { [key: string]: any },
    entityString: string,
    replacementLangu: string | undefined
  ): string {
    // process localization data (can come as "i18n" or "I18N")
    let replacementPatternPrefix = "";
    let i18n: { [k: string]: any } = {};
    if (localitazionObj && localitazionObj.i18n) {
      i18n = localitazionObj.i18n;
      replacementPatternPrefix = "i18n";
    } else if (localitazionObj && localitazionObj.I18N) {
      i18n = localitazionObj.I18N;
      replacementPatternPrefix = "I18N";
    }

    if (i18n) {
      // get the localization data for the expected language
      // first check for the locale langu
      // then check hard-coded for "en"
      // then check for the first language found in the delivered localization string
      let textsOfLanguageObj: { [k: string]: any } = {};
      if (replacementLangu && i18n[replacementLangu]) {
        textsOfLanguageObj = i18n[replacementLangu];
      } else if (i18n.en) {
        textsOfLanguageObj = i18n.en;
      } else if (Object.keys(i18n) && Object.keys(i18n).length > 0) {
        const firstLanguInObject = Object.keys(i18n)[0];
        if (i18n[firstLanguInObject]) {
          textsOfLanguageObj = i18n[firstLanguInObject];
        }
      }

      // the text object contains value pairs in the expected language
      // in following form: { KEY1: text1, KEY2: text2, ... }
      for (const key in textsOfLanguageObj) {
        const replacementPattern = `{${replacementPatternPrefix}>${key}}`;
        let languDependentText = textsOfLanguageObj[key];
        if (replacementPattern && entityString) {
          if (!languDependentText || languDependentText === "") {
            languDependentText = "";
          }
          entityString = this.replaceTextPlaceholdersInCsnString(entityString, replacementPattern, languDependentText);
        }
      }
    }
    return entityString;
  }

  private escapeRegExp(s: string): string {
    return s.replace(/[.*+\-?^${}()|[\]\\]/g, "\\$&"); // $& whole string
  }

  private escapeText(s: string): string {
    return s
      .replace(/[\\]/g, "\\\\")
      .replace(/[\"]/g, '\\"')
      .replace(/[\/]/g, "\\/")
      .replace(/[\b]/g, "\\b")
      .replace(/[\f]/g, "\\f")
      .replace(/[\n]/g, "\\n")
      .replace(/[\r]/g, "\\r")
      .replace(/[\t]/g, "\\t");
  }

  private replaceTextPlaceholdersInCsnString(
    csnString: string,
    replacementPattern: string,
    languDependentText: string
  ): string {
    return csnString.replace(
      new RegExp(this.escapeRegExp(replacementPattern), "g"),
      this.escapeText(languDependentText)
    );
  }

  mapI18nToI18nDefinitions(i18n: II18nContent): II18nDefinitions {
    const i18nDef: II18nDefinitions = {};
    for (const locale in i18n) {
      Object.entries(i18n[locale]).forEach(([key, value]) => {
        const entity = key.substring(
          0,
          key.indexOf("#") > 0
            ? key.indexOf("#") // field separator #
            : !key.startsWith("Remote.") && key.indexOf(".") > 0
            ? key.indexOf(".") // alternative separator . (used by BW and S/4 CDSCSN)
            : key.indexOf("@") > 0
            ? key.indexOf("@") // entity anno separator
            : key.length
        );
        if (!i18nDef[entity]) {
          i18nDef[entity] = { i18n: { [locale]: { [key]: value } } };
        } else if (!i18nDef[entity]!.i18n![locale]) {
          i18nDef[entity]!.i18n![locale] = { [key]: value };
        } else {
          i18nDef[entity]!.i18n![locale][key] = value;
        }
      });
    }
    return i18nDef;
  }

  public i18nConsistencyCheck(csnInput: ICsnExtended, importRequest: ImportRequest) {
    const spaceLangu = importRequest.getSpaceSourceLanguage();

    // check @EnduserTextLabel annotations on header and element level
    // if they have the correct format in CSN and if a value is present for all of them in I18N
    if (csnInput && csnInput.definitions) {
      for (const entity of Object.keys(csnInput.definitions)) {
        const csn = csnInput.definitions[entity];
        if (spaceLangu) {
          const mode = "checkAndNormalizeKeys"; // importRequest.getI18nConsistencyCheckMode();
          if (csnInput.i18nDefinitions && csnInput.i18nDefinitions[entity] && csnInput.i18nDefinitions[entity].i18n) {
            const i18n = csnInput.i18nDefinitions[entity].i18n;
            if (csn) {
              if (i18n && mode === "checkAndNormalizeKeys") {
                // check and normalize keys per entity
                this.i18nCheckAndNormalizeKeys(spaceLangu, entity, csn, i18n);
              }
              // delete orphaned replacement keys in CSN and I18N per entity
              this.i18nDeleteOrphanedReplacementKeys(spaceLangu, entity, csn, i18n);
            }
          } else {
            // for case without i18n blocks (Metadata_I18n FFs off) add entity name or attribute for orphan placeholders
            this.i18nReplaceOrphanedReplacementKeys(entity, csn);
          }
        } else {
          // for cases without space langu also add entity name or attribute for orphan placeholders
          this.i18nReplaceOrphanedReplacementKeys(entity, csn);
        }
      }
    }
  }

  private i18nCheckAndNormalizeKeys(
    spaceLangu: string | undefined,
    entity: string,
    csn: ICsnDefinition,
    i18n: II18nContent
  ) {
    // Check @EnduserTextLabel on Header Level
    const orig = false;
    const toUpper = true;
    if (
      !csn["@EndUserText.label"] ||
      (csn["@EndUserText.label"] !== this.i18nExpectedTextLabelKey(entity, orig) &&
        csn["@EndUserText.label"] !== this.i18nExpectedTextLabelKey(entity, toUpper))
    ) {
      const updateReplacementPattern = true;
      this.i18nSetExpectedLabelAndValues(
        spaceLangu,
        entity,
        csn,
        i18n,
        this.i18nExpectedTextLabelKey(entity, orig), // expected replacement pattern (entity header)
        csn["@EndUserText.label"], // actual replacement pattern (entity header) or undefined
        updateReplacementPattern // update the replacement pattern in CSN
      );
    } else {
      // replacement pattern in CSN correct, only update i18n if necessary
      this.i18nSetExpectedLabelAndValues(spaceLangu, entity, csn, i18n, csn["@EndUserText.label"]);
    }

    // search for @EnduserTextLabel on Field Level
    if (csn.elements) {
      for (const attr of Object.keys(csn.elements)) {
        const attrCsn = csn.elements[attr];
        if (attrCsn && attrCsn.type && attrCsn.type !== "cds.Association") {
          if (
            !(attrCsn as ICsnElement)["@EndUserText.label"] ||
            ((attrCsn as ICsnElement)["@EndUserText.label"] !==
              this.i18nExpectedTextLabelKey(entity, orig, attr, ".") &&
              (attrCsn as ICsnElement)["@EndUserText.label"] !==
                this.i18nExpectedTextLabelKey(entity, orig, attr, "#") &&
              (attrCsn as ICsnElement)["@EndUserText.label"] !==
                this.i18nExpectedTextLabelKey(entity, toUpper, attr, ".") &&
              (attrCsn as ICsnElement)["@EndUserText.label"] !==
                this.i18nExpectedTextLabelKey(entity, toUpper, attr, "#"))
          ) {
            const updateReplacementPattern = true;
            this.i18nSetExpectedLabelAndValues(
              spaceLangu,
              attr,
              attrCsn,
              i18n,
              this.i18nExpectedTextLabelKey(entity, orig, attr, "."), // expected replacement pattern for attribute
              (attrCsn as ICsnElement)["@EndUserText.label"], // actual replacement pattern for attribute or undefined
              updateReplacementPattern // update the replacement pattern in CSN
            );
          } else if ((attrCsn as ICsnElement)["@EndUserText.label"]) {
            // replacement pattern in CSN correct, only update i18n if necessary
            this.i18nSetExpectedLabelAndValues(
              spaceLangu,
              attr,
              attrCsn,
              i18n,
              (attrCsn as ICsnElement)["@EndUserText.label"]
            );
          }
        }
      }
    }
    if (csn.params) {
      for (const attr of Object.keys(csn.params)) {
        const attrCsn = csn.params[attr];
        if (attrCsn) {
          if (
            !attrCsn["@EndUserText.label"] ||
            (attrCsn["@EndUserText.label"] !== this.i18nExpectedTextLabelKey(entity, orig, attr, ".") &&
              attrCsn["@EndUserText.label"] !== this.i18nExpectedTextLabelKey(entity, orig, attr, "#") &&
              attrCsn["@EndUserText.label"] !== this.i18nExpectedTextLabelKey(entity, toUpper, attr, ".") &&
              attrCsn["@EndUserText.label"] !== this.i18nExpectedTextLabelKey(entity, toUpper, attr, "#"))
          ) {
            const updateReplacementPattern = true;
            this.i18nSetExpectedLabelAndValues(
              spaceLangu,
              attr,
              attrCsn,
              i18n,
              this.i18nExpectedTextLabelKey(entity, orig, attr, "."), // expected replacement pattern for attribute
              attrCsn["@EndUserText.label"], // actual replacement pattern for attribute or undefined
              updateReplacementPattern // update the replacement pattern in CSN
            );
          } else if (attrCsn["@EndUserText.label"]) {
            // replacement pattern in CSN correct, only update i18n if necessary
            this.i18nSetExpectedLabelAndValues(spaceLangu, attr, attrCsn, i18n, attrCsn["@EndUserText.label"]);
          }
        }
      }
    }
  }

  private i18nSetExpectedLabelAndValues(
    spaceLangu: string | undefined,
    techName: string,
    csn: ICsnDefinition | ICsnElement | IAssociation,
    i18n: II18nContent,
    patternExpected: string | undefined,
    patternIncoming?: string | undefined,
    updateReplacementPatternInCsn?: boolean
  ) {
    // remove '{i18n>' and final '}'
    const exp: string | undefined =
      patternExpected && patternExpected.startsWith("{i18n>") && patternExpected.slice(6, -1).length > 0
        ? patternExpected.slice(6, -1)
        : undefined;
    const inc: string | undefined =
      patternIncoming && patternIncoming.startsWith("{i18n>") && patternIncoming.slice(6, -1).length > 0
        ? patternIncoming.slice(6, -1)
        : undefined;
    // check for hardcoded text in input field
    const hardcodedText: string | undefined =
      patternIncoming && !patternIncoming.startsWith("{i18n>") ? patternIncoming : undefined;

    // set the expected I18N value in I18N
    if (exp) {
      for (const locale of Object.keys(i18n)) {
        i18n[locale][exp] =
          inc && i18n[locale][inc]
            ? i18n[locale][inc]
            : i18n[locale][exp]
            ? i18n[locale][exp]
            : hardcodedText && locale === spaceLangu
            ? hardcodedText
            : techName; // fallback
      }
    }

    // adjust the replacement pattern in CSN
    if (updateReplacementPatternInCsn) {
      (csn as ICsnElement)["@EndUserText.label"] = patternExpected;
    }
  }

  private i18nExpectedTextLabelKey(entity: string, toUpper?: boolean, attr?: string, separator?: string): string {
    if (attr && separator) {
      if (toUpper) {
        return `{i18n>${entity.toUpperCase()}${separator}${attr.toUpperCase()}@ENDUSERTEXT.LABEL}`;
      } else {
        return `{i18n>${entity}${separator}${attr}@ENDUSERTEXT.LABEL}`;
      }
    } else {
      if (toUpper) {
        return `{i18n>${entity.toUpperCase()}@ENDUSERTEXT.LABEL}`;
      } else {
        return `{i18n>${entity}@ENDUSERTEXT.LABEL}`;
      }
    }
  }

  private i18nDeleteOrphanedReplacementKeys(
    spaceLangu: string,
    entity: string,
    csn: ICsnDefinition,
    i18n?: II18nContent
  ) {
    if (csn) {
      if (i18n && spaceLangu && i18n[spaceLangu]) {
        // check if replacement key on header level has i18n definition in space language
        if (
          csn["@EndUserText.label"] &&
          csn["@EndUserText.label"]!.startsWith("{i18n>") &&
          csn["@EndUserText.label"]!.slice(6, -1).length > 0
        ) {
          const expectedKey = csn["@EndUserText.label"]!.slice(6, -1);
          if (!i18n[spaceLangu][expectedKey]) {
            delete csn["@EndUserText.label"]; // --- delete ------------!!
          }
        }
        // check if replacement keys on element definition have i18n definition in space language
        if (csn.elements) {
          for (const attr of Object.keys(csn.elements)) {
            const attrCsn: ICsnElement | IAssociation = csn.elements[attr];
            if (
              (attrCsn as ICsnElement)["@EndUserText.label"] &&
              (attrCsn as ICsnElement)["@EndUserText.label"]!.startsWith("{i18n>") &&
              (attrCsn as ICsnElement)["@EndUserText.label"]!.slice(6, -1).length > 0
            ) {
              const expectedKey = (attrCsn as ICsnElement)["@EndUserText.label"]!.slice(6, -1);
              if (!i18n[spaceLangu][expectedKey]) {
                delete (attrCsn as ICsnElement)["@EndUserText.label"]; // --- delete ------------!!
              }
            }
          }
        }

        // delete orphaned i18n records
        for (const locale of Object.keys(i18n)) {
          for (const replacementKey of Object.keys(i18n[locale])) {
            if (replacementKey.includes("@ENDUSERTEXT.LABEL")) {
              const expectedKey = `{i18n>${replacementKey}}`;
              if (!csn["@EndUserText.label"] || csn["@EndUserText.label"] !== expectedKey) {
                // i.e. not found already on header level
                let found = false;
                if (csn.elements) {
                  for (const attr of Object.keys(csn.elements)) {
                    if (
                      (csn.elements[attr] as ICsnElement)["@EndUserText.label"] &&
                      (csn.elements[attr] as ICsnElement)["@EndUserText.label"] === expectedKey
                    ) {
                      found = true;
                      break;
                    }
                  }
                }
                if (csn.params) {
                  for (const attr of Object.keys(csn.params)) {
                    if (
                      csn.params[attr]["@EndUserText.label"] &&
                      csn.params[attr]["@EndUserText.label"] === expectedKey
                    ) {
                      found = true;
                      break;
                    }
                  }
                }
                if (!found) {
                  delete i18n[locale][replacementKey]; // --- delete ------------!!
                }
              }
            }
          }
        }
      } else {
        // case no i18n
        // delete orphaned replacement key definitions
        if (
          csn["@EndUserText.label"] &&
          csn["@EndUserText.label"].startsWith("{i18n>") &&
          csn["@EndUserText.label"].slice(6, -1).length > 0
        ) {
          delete csn["@EndUserText.label"];
        }
        if (csn.elements) {
          for (const attr of Object.keys(csn.elements)) {
            const attrCsn: ICsnElement | IAssociation = csn.elements[attr];
            if (
              (attrCsn as ICsnElement)["@EndUserText.label"] &&
              (attrCsn as ICsnElement)["@EndUserText.label"]!.startsWith("{i18n>") &&
              (attrCsn as ICsnElement)["@EndUserText.label"]!.slice(6, -1).length > 0
            ) {
              delete (attrCsn as ICsnElement)["@EndUserText.label"];
            }
          }
        }
        if (csn.params) {
          for (const attr of Object.keys(csn.params)) {
            const attrCsn: ICsnElement | IAssociation = csn.params[attr];
            if (
              attrCsn["@EndUserText.label"] &&
              attrCsn["@EndUserText.label"]!.startsWith("{i18n>") &&
              attrCsn["@EndUserText.label"]!.slice(6, -1).length > 0
            ) {
              delete attrCsn["@EndUserText.label"];
            }
          }
        }
      }
    }
  }

  private i18nReplaceOrphanedReplacementKeys(entity: string, csn: ICsnDefinition) {
    if (csn) {
      // case no i18n
      // - add missing "@EnduserText.label" entries
      // - replace orphaned replacement key definitions with entity name or attribute name
      if (
        !csn["@EndUserText.label"] ||
        (csn["@EndUserText.label"] &&
          csn["@EndUserText.label"].startsWith("{i18n>") &&
          csn["@EndUserText.label"].slice(6, -1).length > 0)
      ) {
        csn["@EndUserText.label"] = entity;
      }
      if (csn.elements) {
        for (const attr of Object.keys(csn.elements)) {
          const attrCsn: ICsnElement | IAssociation = csn.elements[attr];
          if (
            !(attrCsn as ICsnElement)["@EndUserText.label"] ||
            ((attrCsn as ICsnElement)["@EndUserText.label"] &&
              (attrCsn as ICsnElement)["@EndUserText.label"]!.startsWith("{i18n>") &&
              (attrCsn as ICsnElement)["@EndUserText.label"]!.slice(6, -1).length > 0)
          ) {
            (attrCsn as ICsnElement)["@EndUserText.label"] = attr;
          }
        }
      }
    }
  }

  private async fetchAbapTypesIfRequired(
    importRequest: ImportRequest,
    httpHandler: HttpHandler,
    batchContext: IRequestContext
  ): Promise<AbapTypesFromBackend> {
    // check if ABAP types shall be fetched from the backend and call the backend service
    // note: the standard import scenarios do not require ABAP types - the check will always return "no import necessary"
    let abapTypesFromBackend: AbapTypesFromBackend = {};

    if (!(await this.checkAbapTypesImported(importRequest, batchContext))) {
      abapTypesFromBackend = await httpHandler.getAbapTypesFromBackend(importRequest, batchContext);
    }
    return abapTypesFromBackend;
  }

  private async checkAbapTypesImported(importRequest: ImportRequest, batchContext: IRequestContext): Promise<boolean> {
    // obsolete
    return true; // we assume Abap types are already available in repository of the space
  }

  async adjustDataTypesInCsn(
    batchContext: IRequestContext,
    accessTechnology: ImportAccessTechnology | undefined,
    entities: ImportableEntities,
    destinationData: DestinationData | undefined,
    importRequest: ImportRequest
  ) {
    // do not adjust types mapping for SAPBWMODELTRANSFER and SAPBWBRIDGE connection type
    if (
      destinationData &&
      destinationData.connTypeId !== TypeIds.SAPBWMODELTRANSFER &&
      destinationData.connTypeId !== TypeIds.SAPBWBRIDGE
    ) {
      // perform data types adjusting for deploying with CDI or APE
      if (
        destinationData.connTypeId === TypeIds.SAPS4HANACLOUD ||
        (destinationData.connTypeId === TypeIds.SAPS4HANAOP && accessTechnology === ImportAccessTechnology.Csn_Exposure)
      ) {
        await this.adjustdataTypesInCsn4HanaCloud(batchContext, entities, destinationData, importRequest);
        this.convertParamsUppercaseInCsn4HanaCloud(batchContext, entities, destinationData);
        // await this.replaceCurrencyConversionInCsn4HanaCloud(batchContext, entities, destinationData); - currency conversion is open, it needs a currency mapping table being present in HANA
      }
      // perform abap types adjusting for deploying with ODP
      else if (
        destinationData.connTypeId === TypeIds.SAPS4HANAOP &&
        accessTechnology !== ImportAccessTechnology.Csn_Exposure
      ) {
        this.adjustdataTypesInCsn4OnPrem(batchContext, entities, destinationData);
      } else if (
        destinationData?.connTypeId === TypeIds.BUSINESS_DATA_PRODUCT ||
        destinationData?.connTypeId === TypeIds.HDL_FILES
      ) {
        // do the same conversions as for S/4
        await this.adjustdataTypesInCsn4HanaCloud(batchContext, entities, destinationData, importRequest);
        this.convertParamsUppercaseInCsn4HanaCloud(batchContext, entities, destinationData);
        // care for unlimited strings since replication cannot handle them
        this.adjustdataTypesInCsn4Hdlf(batchContext, entities, destinationData);
      }
    }
  }

  addImportMetaInformation(batchContext: IRequestContext, entities: ImportableEntities, importRequest: ImportRequest) {
    if (entities && entities.entitiesToDeploy) {
      for (const objToDeploy of entities.entitiesToDeploy) {
        for (const def of Object.values(objToDeploy.definitions)) {
          const importMeta: { [k: string]: any } = { lastImportedUTC: new Date(Date.now()).toUTCString() };
          if (importRequest.getCorrelationId()) {
            importMeta.correlationId = importRequest.getCorrelationId();
          }
          if (importRequest.getTaskLogId()) {
            importMeta.taskLogId = importRequest.getTaskLogId();
          }
          if (importRequest.getAccessTechnology() === ImportAccessTechnology.OdcDataProducts) {
            // enrich import_meta with ucl entity information
            this.enrichImportMeta(importMeta, objToDeploy, entities, importRequest);
          }
          (def as any).import_meta = importMeta;
        }
      }
    }
  }

  private enrichImportMeta(
    importMeta: { [k: string]: any },
    objToDeploy: ICsnObjectToDeploy,
    entities: ImportableEntities,
    importRequest: ImportRequest
  ) {
    if (objToDeploy && objToDeploy.name && entities && entities.entitiesForUi) {
      // find record of entities.entitiesForUi where entityForUi.qualifiedName = objToDeploy.name
      const entityForUi = entities.entitiesForUi.find((entityForUi) => entityForUi.qualifiedName === objToDeploy.name);
      if (entityForUi) {
        if (entityForUi.originalEntityName) {
          // for delta-capture enabled entities HybridParser extends the original entity name (key) with "_delta"
          // this shall not appear in import_meta
          if (entityForUi.originalEntityName.endsWith("_Delta")) {
            importMeta.originalEntityName = entityForUi.originalEntityName.replace(/_Delta$/, "");
          } else {
            importMeta.originalEntityName = entityForUi.originalEntityName;
          }
        }
        if (entityForUi.qualifiedName) {
          // for delta-capture enabled entities HybridParser extends the original entity name (key) with "_delta"
          // this shall not appear in import_meta
          if (entityForUi.qualifiedName.endsWith("_Delta")) {
            importMeta.qualifiedName = entityForUi.qualifiedName.replace(/_Delta$/, "");
            importMeta.deltaCaptureTable = entityForUi.qualifiedName;
          } else {
            importMeta.qualifiedName = entityForUi.qualifiedName;
          }
        }
        if (entityForUi.uclId) {
          importMeta.uclId = entityForUi.uclId;
        }
        if (entityForUi.genericOrdId) {
          importMeta.genericOrdId = entityForUi.genericOrdId;
        }
        if (entityForUi.apiOrdId) {
          importMeta.apiOrdId = entityForUi.apiOrdId;
        }
        if (importRequest && importRequest.getUseIngestionSpace()) {
          importMeta.importedToIngestionSpace = true;
          const ingestionSpaceDetails = importRequest.getIngestionSpaceDetails();
          if (
            ingestionSpaceDetails?.connectionDetails?.currentUclConnName &&
            ingestionSpaceDetails?.connectionDetails?.generatedSpaceConnection
          ) {
            // temporarily continue to log uclConnectionName despite the fact that it is not stable
            // it is still needed for offboarding
            // mid-term offboarding to switch from uclConnection to uclSystemTenantid
            importMeta.connectionDetails = {
              uclConnectionName: ingestionSpaceDetails.connectionDetails.currentUclConnName,
              ingestionSpaceConnectionName: ingestionSpaceDetails.connectionDetails.generatedSpaceConnection,
            };
          }
          if (ingestionSpaceDetails?.createdFor?.uclSystemName) {
            importMeta.uclSystemInfo = {
              uclSystemName: ingestionSpaceDetails.createdFor.uclSystemName,
            };
            if (ingestionSpaceDetails.createdFor.uclSystemTenantId) {
              importMeta.uclSystemInfo.uclSystemTenantId = ingestionSpaceDetails.createdFor.uclSystemTenantId;
            }
          }
        }
      }
    }
  }

  private addImportMetaForContexts(importRequest: ImportRequest): { [k: string]: any } {
    const importMeta: { [k: string]: any } = { lastImportedUTC: new Date(Date.now()).toUTCString() };
    if (importRequest.getCorrelationId()) {
      importMeta.correlationId = importRequest.getCorrelationId();
    }
    if (importRequest.getTaskLogId()) {
      importMeta.taskLogId = importRequest.getTaskLogId();
    }

    // in ingestion space scenario add connection and system details
    if (importRequest && importRequest.getUseIngestionSpace()) {
      const ingestionSpaceDetails = importRequest.getIngestionSpaceDetails();
      if (
        ingestionSpaceDetails?.connectionDetails?.currentUclConnName &&
        ingestionSpaceDetails?.connectionDetails?.generatedSpaceConnection
      ) {
        // temporarily continue to log uclConnectionName despite the fact that it is not stable
        // it is still needed for offboarding
        // mid-term offboarding to switch from uclConnection to uclSystemTenantid
        importMeta.connectionDetails = {
          uclConnectionName: ingestionSpaceDetails.connectionDetails.currentUclConnName,
          ingestionSpaceConnectionName: ingestionSpaceDetails.connectionDetails.generatedSpaceConnection,
        };
      }
      if (ingestionSpaceDetails?.createdFor?.uclSystemName) {
        importMeta.uclSystemInfo = {
          uclSystemName: ingestionSpaceDetails.createdFor.uclSystemName,
        };
        if (ingestionSpaceDetails.createdFor.uclSystemTenantId) {
          importMeta.uclSystemInfo.uclSystemTenantId = ingestionSpaceDetails.createdFor.uclSystemTenantId;
        }
      }
    }

    return importMeta;
  }

  private async adjustdataTypesInCsn4HanaCloud(
    batchContext: IRequestContext,
    entities: ImportableEntities,
    destinationData: DestinationData | undefined,
    importRequest: ImportRequest
  ) {
    const ffV2B = true;
    // const ffV2B =
    //   destinationData?.connTypeId === TypeIds.BUSINESS_DATA_PRODUCT || destinationData?.connTypeId === TypeIds.HDL_FILES
    //    ? true // for HDL files always assume ffV2B mode
    //    : await FeatureFlagProvider.isFeatureActive(batchContext, "DWCO_IMPORT_MANAGER_VIA_CDI_V2B");
    const ffV2 = false;
    // const ffV2 =
    //   ffV2B !== true
    //    ? await FeatureFlagProvider.isFeatureActive(batchContext, "DWCO_IMPORT_MANAGER_VIA_CDI_V2")
    //    : false; // if ffV2B is active, ffV2 is not relevant
    if (entities && entities.entitiesToDeploy) {
      for (const objToDeploy of entities.entitiesToDeploy) {
        for (const def of Object.values(objToDeploy.definitions)) {
          // type adjustments in query
          try {
            const hits = JSONPath({
              resultType: "value",
              json: def,
              path: "$.query..cast",
            });

            for (const hit of hits) {
              if (
                destinationData?.connTypeId === TypeIds.BUSINESS_DATA_PRODUCT ||
                destinationData?.connTypeId === TypeIds.HDL_FILES
              ) {
                // this will not occur in HDL usecase
                // no changes for hit.type === "cds.Decimal"
              } else {
                if (hit.type === "cds.Decimal" && (hit.scale === "variable" || hit.scale === "floating")) {
                  if (hit.scale !== undefined) {
                    delete hit.scale;
                  }
                  if (hit.precision !== undefined) {
                    delete hit.precision;
                  }
                  hit.type = "cds.DecimalFloat";
                } else if (hit.type === "cds.Decimal" && !hit.scale && !hit.precision) {
                  // abap types decfloat16 and decfloat34:
                  // comes from backend as decimal without scale and precision
                  // convert to DecimalFloat
                  hit.type = "cds.DecimalFloat";
                }
              }
            }
          } catch (err) {
            // Todo
          }

          // type adjustments in elements
          if (def.elements !== undefined) {
            for (const el of Object.values(def.elements)) {
              // general workaround for existing bug in CSNExposure, delivering cds.Double with precision and scale
              if (el.type === "cds.Double" && (el.precision || el.scale)) {
                delete el.scale;
                delete el.precision;
              }
              // general workaround, since dwc support for Composition is still limited
              else if (el.type === "cds.Composition") {
                el.type = "cds.Association";
              }
              // general workaround, old CDS compiler cannot deal with scale="floating"
              else if (el.type === "cds.Decimal") {
                if (
                  destinationData?.connTypeId === TypeIds.BUSINESS_DATA_PRODUCT ||
                  destinationData?.connTypeId === TypeIds.HDL_FILES
                ) {
                  // all decimals which have scale "floating" (from semantical perspective)
                  // will be provided to us as Decimals with precision 34 scale 4 by SPARK
                  // --> we will not change anything
                  // workaround for temporary delivery with scale "floating"
                  if (el.scale && el.scale === "floating") {
                    el.precision = 34;
                    el.scale = 4;
                  }
                } else {
                  if (el.scale && el.scale === "floating") {
                    el.type = "cds.DecimalFloat";
                    delete el.scale;
                    delete el.precision;
                  }
                  // abap types decfloat16 and decfloat34:
                  // comes from backend as decimal without scale and precision
                  // convert to DecimalFloat
                  // else if (el.type === "cds.Decimal" && !el.scale && !el.precision) {
                  else if (!el.scale && !el.precision) {
                    el.type = "cds.DecimalFloat";
                  }
                  // if the field has annotation "currencyCode" and is of type Decimal
                  // also convert to DecimalFloat and delete scale and precision
                  // we set this only in the cases of:
                  // - data product import
                  // - SAP S/4HANA Cloud and SAP S/4HANA OP with Csn_Exposure
                  else if (
                    el["@Semantics.amount.currencyCode"] !== undefined &&
                    (destinationData?.connTypeId === TypeIds.SAPS4HANACLOUD ||
                      destinationData?.connTypeId === TypeIds.SAPS4HANAOP) &&
                    importRequest.getAccessTechnology() === ImportAccessTechnology.Csn_Exposure
                  ) {
                    if (el.scale !== undefined) {
                      delete el.scale;
                    }
                    if (el.precision !== undefined) {
                      delete el.precision;
                    }
                    el.type = "cds.DecimalFloat";
                  }
                }
              }

              if (ffV2B) {
                // in the usecase of HDL files convert dateTime to timestamp
                // the SPARK database does not support the type "cds.DateTime"
                if (
                  destinationData?.connTypeId === TypeIds.BUSINESS_DATA_PRODUCT ||
                  destinationData?.connTypeId === TypeIds.HDL_FILES
                ) {
                  if (el.type === "cds.DateTime") {
                    el.type = "cds.Timestamp";
                  }
                }
                // apart from this no further workarounds required for CDI
              } else if (ffV2) {
                /* // FF removal - obsolete - conversions which were necessary before CDI V2B - first version of V2
                if (el.type === "cds.DateTime") {
                  // convert dateTime to timestamp
                  el.type = "cds.Timestamp";
                } else if (el.type === "cds.Decimal") {
                  if (el.precision !== undefined && el.precision < 17) {
                    // enlarge any short fixed decimal to 17
                    el.precision = 17;
                  }
                  if (el["@Semantics.amount.currencyCode"] !== undefined) {
                    // currency field - delete both scale and precision and convert to DecimalFloat
                    el.type = "cds.DecimalFloat";
                    delete el.scale;
                    delete el.precision;
                  }
                }
                */
              } else {
                /* // FF removal - obsolete - conversions which were necessary before CDI V2 - original version of CDI
                if (el.type === "cds.DateTime") {
                  // convert dateTime to timestamp
                  el.type = "cds.Timestamp";
                } else if (el.type === "cds.Decimal") {
                  if (el["@Semantics.amount.currencyCode"] !== undefined) {
                    // currency field - delete both scale and precision and convert to DecimalFloat
                    if (el.scale !== undefined) {
                      delete el.scale;
                    }
                    if (el.precision !== undefined) {
                      delete el.precision;
                    }
                    el.type = "cds.DecimalFloat";
                  } else if (el.precision !== undefined && el.scale !== undefined) {
                    const analyticsSUM = (el as any)["@DefaultAggregation"]?.["#"];
                    if (analyticsSUM === undefined) {
                      if (el.precision < 17 && el.scale > 0) {
                        // the restriction "el.scale > 0" works for the cases:
                        // - BackendType DEC(5,0) example I_Product         ComparisonPriceQuantity (BackendField MARA.VPREH)
                        // - BackendType DEC(3,0) example ISupplierCompany  CheckPaidDurationInDays (LFB1.KULTG)
                        // it does not work for
                        // - BackendType QUAN(3,0) example I_Product        QuarantinePeriod        (MARA.QQTIME
                        el.precision = 17;
                      }
                    } else {
                      if (el.precision < 17) {
                        // field with aggregation annotation: keyfigure in CDI
                        el.precision = 17;
                      }
                    }

                    if (el.scale === "floating") {
                      el.scale = 6;
                    }
                  }
                }
                */
              }
            }
          }
        }
      }
    }
  }

  private convertParamsUppercaseInCsn4HanaCloud(
    batchContext: IRequestContext,
    entities: ImportableEntities,
    destinationData: DestinationData | undefined
  ) {
    /*
    convert params and param usages uppercase
    */
    if (entities && entities.entitiesToDeploy) {
      for (const objToDeploy of entities.entitiesToDeploy) {
        for (const def of Object.values(objToDeploy.definitions).filter(
          (d) => d.kind === RepositoryObjectKind.entity
        )) {
          /*
          1) args in query from (selecting from a view with parameters)
           */
          try {
            const hits = JSONPath({
              resultType: "value",
              json: def,
              path: "$.query..ref[*].args",
            });
            for (const hit of hits) {
              for (const [key, value] of Object.entries(hit)) {
                hit[key.toUpperCase()] = value;
                delete hit[key];
              }
            }
          } catch (err) {
            // Todo
          }

          /*
          2) parameters in "params" section
          */
          if ((def as any).params) {
            for (const [key, value] of Object.entries((def as any).params)) {
              (def as any).params[key.toUpperCase()] = value;
              delete (def as any).params[key];
            }
          }

          /*
          3) refs in query with param=true
           */
          try {
            const hits = JSONPath({
              resultType: "value",
              json: def,
              path: "$.query..[?(@.ref && @.param === true)]",
            });
            for (const hit of hits) {
              if (hit.ref && Array.isArray(hit.ref)) {
                hit.ref = hit.ref.map((i: string) => i.toUpperCase());
              }
            }
          } catch (err) {
            // Todo
          }
        }
      }
    }
  }
  /* private async replaceCurrencyConversionInCsn4HanaCloud(
    batchContext: IRequestContext,
    entities: ImportableEntities,
    destinationData: DestinationData | undefined
  ) {

    if (entities && entities.entitiesToDeploy) {
      for (const objToDeploy of entities.entitiesToDeploy) {
        for (const def of Object.values(objToDeploy.definitions).filter(
          (d) => d.kind === RepositoryObjectKind.entity
        )) {
          try {
            const hits = JSONPath({
              resultType: "value",
              json: def,
              path: "$.query..[?(@.func === 'currency_conversion')]",
            });
            for (const hit of hits) {
              hit.func = "CONVERT_CURRENCY";
            }
          } catch (err) {
            // Todo
          }
        }
      }
    }
  } */

  private adjustdataTypesInCsn4OnPrem(
    batchContext: IRequestContext,
    entities: ImportableEntities,
    destinationData: DestinationData | undefined
  ) {
    if (entities && entities.entitiesToDeploy) {
      // there is only one object to deploy
      for (const objToDeploy of entities.entitiesToDeploy) {
        // there is only one definition belongigng to the object to deploy
        for (const def of Object.values(objToDeploy.definitions)) {
          // type adjustments in elements
          if (def.elements !== undefined) {
            for (const el of Object.values(def.elements)) {
              // abap.dec with precision < 17 and with scale
              if (el.type === "abap.dec" && el.precision && el.precision < 17 && el.scale !== undefined) {
                const analyticsSUM = (el as any)["@DefaultAggregation"]?.["#"];

                if (analyticsSUM) {
                  // annotation available -> always enlarge
                  el.precision = 17;
                } else if (el.scale > 0) {
                  // no annotation available , with scale -> enlarge
                  el.precision = 17;
                } else {
                  // if abap.dec and precision < 17 and decimals = 0 and the field has sign -> extend to precision 17
                  if (el["@HasSign"] && el["@HasSign"] === true) {
                    el.precision = 17;
                  }
                }
              }

              // abap.quan or abap.curr with precision < 17 and with scale --> always enlarge
              else if (
                (el.type === "abap.quan" || el.type === "abap.curr") &&
                el.precision &&
                el.precision < 17 &&
                el.scale !== undefined
              ) {
                el.precision = 17;
              }
            }
          }
        }
      }
    }
  }

  private adjustdataTypesInCsn4Hdlf(
    batchContext: IRequestContext,
    entities: ImportableEntities,
    destinationData: DestinationData | undefined
  ) {
    if (entities && entities.entitiesToDeploy) {
      // there is only one object to deploy
      for (const objToDeploy of entities.entitiesToDeploy) {
        // there is only one definition belongigng to the object to deploy
        for (const def of Object.values(objToDeploy.definitions)) {
          // type adjustments in elements
          if (def.elements !== undefined) {
            for (const el of Object.values(def.elements)) {
              // fix unlimited strings
              if (el.type === "cds.String" && el.length === undefined) {
                el.length = 5000;
              }
            }
          }
        }
      }
    }
  }

  eliminateAttributesFromEntityDefElementsInCsn(
    entities: ImportableEntities,
    destinationData: DestinationData | undefined
  ) {
    // eliminate attributes from Entity only for SAPS4HANACLOUD connection, where Deployment is done using CDI
    // eliminate the attributes which are annotated with @Analytics.Hidden:true --> because the CDI service does the same
    if (
      destinationData &&
      (destinationData.connTypeId === TypeIds.SAPS4HANACLOUD || destinationData.connTypeId === TypeIds.SAPS4HANAOP)
    ) {
      if (entities && entities.entitiesToDeploy) {
        for (const objToDeploy of entities.entitiesToDeploy) {
          for (const def of Object.values(objToDeploy.definitions)) {
            if (def.kind === "entity" && def.elements && def["@DataWarehouse.remote.entity"]) {
              // remove elements with @Analytics.Hidden or @UI.Hidden (incident 758591/2024)
              for (const [elementkey, element] of Object.entries(def.elements)) {
                if (
                  ((element as any)["@Analytics.hidden"] && (element as any)["@Analytics.hidden"] === true) ||
                  ((element as any)["@UI.hidden"] && (element as any)["@UI.hidden"] === true)
                ) {
                  delete def.elements[elementkey];

                  // remove locally defined associations refering to the hidden element, too
                  const assos = Object.entries(def.elements).filter(
                    ([elName, elProps]) => elProps.type === "cds.Association"
                  );
                  for (const [assoKey, asso] of assos) {
                    const onConditions = (asso as any).on;
                    const found = onConditions.find((o: any) => o.ref && o.ref.length === 1 && o.ref[0] === elementkey);
                    if (found) {
                      delete def.elements[assoKey];
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
