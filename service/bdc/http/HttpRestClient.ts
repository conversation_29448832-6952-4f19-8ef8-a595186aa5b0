/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import type { Request as RequestOptions, ResolvedResponse } from "@sap/dwc-http-client";
import { AuthenticationMode, httpClient } from "@sap/dwc-http-client";
import { HttpHeader } from "@sap/seal-interfaces";
import { StatusCodes as HttpStatusCode } from "http-status-codes";
import { getLogger } from "../../logger";
import type { IRequestContext } from "../../repository/security/common/common";
import type { IHttpCallbackOptions, IHttpRestClient } from "./Interface";

const { logPerformance, logError } = getLogger("HttpClient");

/**
 * A simple wrapper class to retrieve or send information to  rest api
 * Example
 *
 * ```typescript
 * return new Promise<IDeployResult>((resolve) => {
 *  const httpRestClient = new HttpRestClient(
      "http://localhost:3000",
      context,
    );
    const callbackOptions: IHttpCallbackOptins = {
      responseProcessor: transformResponse,
      exceptionProcessor: transformRequestException,
      beforeSend: (postOptions: Readonly<Options>) => ({
        ...postOptions,
        auth: {
          bearer: " token for the dependent endpoint",
        },
        headers: {
          additionalhader:""Value for header
        },
      }),
    };
    return httpRestClient.post("/sync-endpoint", {
      // post body
    }, callbackOptions);
  });
    ```
 */
export class HttpRestClient<T_HttpBody = any> implements IHttpRestClient {
  constructor(
    protected readonly serviceEndpoint: string,
    protected context: IRequestContext,
    protected authScope?: string
  ) {}

  public getUrl(uri?: string) {
    if (uri && !uri.startsWith("/")) {
      uri = `/${uri}`;
    }
    return `${this.serviceEndpoint}${uri}`;
  }

  public getContext(): Readonly<IRequestContext> {
    return this.context;
  }

  public setContext(context: IRequestContext) {
    this.context = context;
  }

  /**
   * To post the request which could return either a 202 | 301 | 200 response
   *
   * @param endpoint Service uri that should be used no need to include the service host and main url
   * @param body Http post body
   */
  public async post<T_HttpResponse>(
    endpoint: string,
    body: T_HttpBody,
    callbacks: IHttpCallbackOptions
  ): Promise<T_HttpResponse> {
    try {
      const requestOptions = this.beforeSend(endpoint, { method: "POST", body }, callbacks);
      const httpResponse = await this.sendRequest(requestOptions, callbacks.retryIfThrottled);
      return await callbacks.responseProcessor(httpResponse, requestOptions.opts!.body);
    } catch (err) {
      return await callbacks.exceptionProcessor(err, body);
    }
  }

  public async get(endpoint: string, callbacks: IHttpCallbackOptions): Promise<ResolvedResponse> {
    const requestOptions = this.beforeSend(endpoint, { method: "GET", redirect: "follow" }, callbacks);

    return await this.sendRequest(requestOptions, callbacks.retryIfThrottled);
  }

  private beforeSend(
    endpoint: string,
    options: { method: "POST" | "GET"; body?: any; redirect?: "follow" },
    callbacks: IHttpCallbackOptions
  ): RequestOptions {
    try {
      const extendedOptions = callbacks.beforeSend({
        url: this.getUrl(endpoint),
        opts: {
          ...options,
          scope: this.authScope,
          acceptedStatusCodes: [],
        },
      });
      this.updateTechnicalHeaders(extendedOptions);
      extendedOptions.opts = extendedOptions.opts ?? {};
      extendedOptions.opts.authentication = extendedOptions.opts.authentication ?? AuthenticationMode.Uaa;
      return extendedOptions;
    } catch (error) {
      logError(`Processing before send for ${endpoint} failed` + String(error), { context: this.context });
      throw error;
    }
  }
  private async retryIfThrottled(
    response: ResolvedResponse,
    methodName: "GET" | "POST",
    requestOptions: RequestOptions
  ) {
    if (response.status === HttpStatusCode.TOO_MANY_REQUESTS) {
      const retryTimeout = parseInt(response.headers.get("retry-after") ?? "0", 10);
      if (retryTimeout > 0) {
        logError(
          `${methodName}: failed with statusCode: ${HttpStatusCode.TOO_MANY_REQUESTS} will retry after ${retryTimeout} seconds.`,
          { context: this.context }
        );
        return await this.sendRequest(requestOptions, false, retryTimeout);
      }
    }
    return response;
  }

  /**
   * To be able to retry incase of rate limiting or other technical error
   */
  private async sendRequest(
    requestOptions: RequestOptions,
    retryIfThrottled: boolean = false,
    timeout: number = 0
  ): Promise<ResolvedResponse> {
    let start = new Date();
    const methodName = (requestOptions.opts!.method as "GET" | "POST") ?? "GET";
    try {
      if (timeout > 0) {
        await this.sleep(timeout);
        logError(`${methodName}: ${requestOptions.url} retrying after ${timeout} seconds.`, { context: this.context });
      }

      start = logPerformance(start, `${methodName}: ${requestOptions.url} started`, { context: this.context });
      // fix request context missing for all the client so we support header forwarding correctly.
      if (requestOptions.opts && requestOptions.opts.callCategory && !requestOptions.opts.requestContext) {
        requestOptions.opts.requestContext = this.context;
      }

      const response: ResolvedResponse = await httpClient.call(requestOptions);
      start = logPerformance(start, `${methodName}: ${requestOptions.url} received with ${response.status}`, {
        context: this.context,
      });

      // special logic for retry incase of load-shedding or rate limiting
      if (retryIfThrottled) {
        return await this.retryIfThrottled(response, methodName, requestOptions);
      }

      return response;
    } catch (error) {
      logPerformance(start, `${methodName}: ${requestOptions.url} failed`, { context: this.context });
      logError(`${methodName}: ${requestOptions.url} failed` + String(error), { context: this.context });
      throw error;
    }
  }

  private sleep(timeInSeconds: number) {
    return new Promise((resolve) => setTimeout(resolve, timeInSeconds * 1000));
  }

  private updateTechnicalHeaders(requestOptions: RequestOptions) {
    if (!requestOptions.opts!.headers) {
      requestOptions.opts!.headers = {};
    }

    const headers = requestOptions.opts!.headers as { [key: string]: string };
    // make sure enable the reverse proxy trust settings in express app
    if (!headers[HttpHeader.X_FORWARDED_FOR]) {
      headers[HttpHeader.X_FORWARDED_FOR] = this.context.httpRequest?.ips.join(",") ?? "";
    }

    if (!headers[HttpHeader.SAP_CORRELATION_ID]) {
      headers[HttpHeader.SAP_CORRELATION_ID] = this.context.correlationId!;
    }
  }
}
