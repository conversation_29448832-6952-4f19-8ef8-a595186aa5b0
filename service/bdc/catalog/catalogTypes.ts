/** @format */

import { ClassNameType, PropertiesFieldType } from "./CatalogConstants";

/** @format */
export type relationshipSystemType = "CRT_BDC_TARGET_SAC" | "CRT_BDC_TARGET_DSP" | "CRT_BDC_DP_SOURCE_SYSTEM";
export type relationshipFormationType = "CRT_BDC_INSTALL_TO_FORMATION";
export type relationshipTypeIdValue = relationshipSystemType | relationshipFormationType;

export type relationshipDirectionType = "SOURCE_TO_TARGET";

export interface CatalogPayload {
  rootObjectSelector: RootObjectSelector;
  propertySelectors?: PropertySelector[];
  relatedObjectSelectors?: any[]; // not yet used/defined
  relatedSystemSelectors?: any[]; // not yet used/defined
  rootObjectTagFilters?: TagFilter[];
}
export interface TagFilter {
  propertyId: string;
  propertyValue?: PropertyValue;
}
export interface PropertyValue {
  valueString?: string;
  valueId?: string;
}
export interface RootObjectSelector {
  className: ClassNameType;
  objectSelector?: ObjectSelector;
}
export interface ObjectSelector {
  names?: string[];
  objectReferences?: ObjectReference[];
}
export interface PropertySelector {
  propertyId: PropertiesFieldType;
  className?: ClassNameType;
}
export interface ObjectReference {
  systemId: string;
  nativeUniqueName: string;
}
