/** @format */

import { relationshipDirectionType, relationshipSystemType } from "./catalogTypes";

/** @format */
export const ClassName = {
  BDC_PACKAGE: "bdcPackage",
  INSTALLED_PACKAGE: "installedPackage",
  DATA_PRODUCT: "DATA_PRODUCT",
};
export type ClassNameType = typeof ClassName[keyof typeof ClassName];

export const PropertiesField = {
  BDC_PCK_JSON: "CP_BDC_PKG_JSON_DEF",
  BDC_PCK_PREVIEW_LINK: "CP_BDC_PKG_PREVIEW_LINK",
  BDC_PCK_LANDSCAPE_TYPE: "CP_BDC_PKG_LANDSCAPE_TYPE",
  BDC_PCK_ID: "CP_BDC_PKG_ID",
  BDC_PCK_NAME: "CP_BDC_PKG_NAME",
  BDC_PCK_VERSION: "CP_BDC_PKG_VERSION",
  BDC_INSTALL_JSON: "CP_BDC_INSTALL_JSON_DEF",
  BDC_DATA_PACKAGE_ID: "CP_BDC_DATA_PACKAGE_ID",
  COCKPIT_TENANT_ID: "CP_BDC_COCKPIT_TENANT_ID",
  COMPONENT_ID: "CP_BDC_COMPONENT_ID",
  ORD_VERSION: "SP_ORD_LC_VERSION",
  ORD_STATUS: "SP_ORD_LC_RESRC_STATUS",
};
export type PropertiesFieldType = typeof PropertiesField[keyof typeof PropertiesField];
export const NavigationLinksField = {
  REL_TYPE_ID_SAC: "CRT_BDC_TARGET_SAC" as relationshipSystemType,
  REL_TYPE_ID_DSP: "CRT_BDC_TARGET_DSP" as relationshipSystemType,
  REL_TYPE_ID_SOURCE: "CRT_BDC_DP_SOURCE_SYSTEM" as relationshipSystemType,
  REL_TYPE_ID_FORMATION: "CRT_BDC_INSTALL_TO_FORMATION" as relationshipSystemType,
};
export type NavigationLinksFieldType = typeof NavigationLinksField[keyof typeof NavigationLinksField];
export const RelationshipDirection = {
  SOURCE_TO_TARGET: "SOURCE_TO_TARGET" as relationshipDirectionType,
};
export type RelationshipDirectionType = typeof RelationshipDirection[keyof typeof RelationshipDirection];
