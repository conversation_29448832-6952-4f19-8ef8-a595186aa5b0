/** @format */
// FILEOWNER: [Provisioning]

import { getLogger } from "@sap/dwc-logger";
import { notifyDeepsea } from "../../../../reuseComponents/ucl/utils/deepseaUtils";
import { IUclResponseBody, SynchronizationTenantState, UclOperation } from "../../../../ucl/types";
import { UclSystemManagement } from "../../integratedSystems/UclSystemManagement";
import { IIntegratedSystems, integratedSystems } from "../../integratedSystems/integratedSystems";
import { assertBdcTenantEnabled, logAuditConfiguration } from "../../utils";
import { OperationsHandler } from "./OperationHandler";
import { TMapSError } from "./types";

const { logInfo } = getLogger("TMapS-BDC-AssignmentOperationHandler");

export class AssignmentOperationHandler extends OperationsHandler {
  async getSynchronizationPayload(
    uclIntegratedSystems: IIntegratedSystems = integratedSystems
  ): Promise<IUclResponseBody> {
    try {
      const result = await this.getStatePayload(uclIntegratedSystems);
      await logAuditConfiguration(this.context, result.state, this.uclRequestBody);
      return result;
    } catch (error) {
      const msg = `Operation: ${UclOperation.assign} has failed. Receiver Tenant State: ${this.uclRequestBody.receiverTenant.state}, Formation Tenant State: ${this.uclRequestBody.assignedTenant.state}, Receiver Tenant Application Namespace: ${this.uclRequestBody.receiverTenant.applicationNamespace}. Error: ${error.message}`;

      await logAuditConfiguration(
        this.context,
        SynchronizationTenantState.CREATE_ERROR,
        this.uclRequestBody,
        error ?? undefined
      );
      throw new TMapSError(msg, SynchronizationTenantState.CREATE_ERROR);
    }
  }

  private async getStatePayload(uclIntegratedSystems: IIntegratedSystems = integratedSystems) {
    const bdcTenant = this.uclRequestBody.receiverTenant;
    const formationTenant = this.uclRequestBody.assignedTenant;
    switch (bdcTenant.state) {
      case SynchronizationTenantState.INITIAL:
        logInfo(`Handling synchronization ${bdcTenant.state} state`, { context: this.context });
        return await this.handleSyncStart(uclIntegratedSystems);
      case SynchronizationTenantState.CONFIG_PENDING:
        logInfo(`Handling synchronization ${bdcTenant.state} state`, { context: this.context });
        return await this.handleSyncUpdate(uclIntegratedSystems);
      case SynchronizationTenantState.CREATE_ERROR:
        if (formationTenant.configuration) {
          logInfo(`Handling synchronization UPDATE after a ${bdcTenant.state} state`, { context: this.context });
          return await this.handleSyncUpdate(uclIntegratedSystems);
        }
        logInfo(`Handling synchronization START after a ${bdcTenant.state} state`, { context: this.context });
        return await this.handleSyncStart(uclIntegratedSystems);
      default:
        return this.setUclErrorPayload(
          SynchronizationTenantState.CREATE_ERROR,
          `Unable to find the correct action for BDC State: ${bdcTenant.state}`
        );
    }
  }

  private async handleSyncStart(
    uclIntegratedSystems: IIntegratedSystems = integratedSystems
  ): Promise<IUclResponseBody> {
    await assertBdcTenantEnabled(this.context);

    const configuration = await UclSystemManagement.getSystemConfigurationPayload(
      this.context,
      this.uclRequestBody,
      uclIntegratedSystems
    );
    logInfo(`Synchronization CONFIG_PENDING for ${this.uclRequestBody.assignedTenant.uclSystemName}`, {
      context: this.context,
    });
    return { state: SynchronizationTenantState.CONFIG_PENDING, configuration };
  }

  private async handleSyncUpdate(
    uclIntegratedSystems: IIntegratedSystems = integratedSystems
  ): Promise<IUclResponseBody> {
    await UclSystemManagement.propagateSystemCredentials(this.context, this.uclRequestBody, uclIntegratedSystems);
    await notifyDeepsea(this.context, this.uclRequestBody);
    logInfo(`Synchronization READY for ${this.uclRequestBody.assignedTenant.uclSystemName}`, { context: this.context });
    return { state: SynchronizationTenantState.READY };
  }
}
