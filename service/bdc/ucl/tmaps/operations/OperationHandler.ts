/** @format */
// FILEOWNER: [Provisioning]

import { IRequestContext } from "@sap/deepsea-types";
import { getLogger } from "@sap/dwc-logger";
import Status from "http-status-codes";
import { DeepseaServiceUclFormationClient } from "../../../../connections/ucl/deepseaServiceUclFormationClient";
import { IUclRequestBody, IUclResponseBody, SynchronizationTenantState } from "../../../../ucl/types";

const logger = getLogger("TMapS-BDC-OperationHandler");
const { logInfo, logDebug, logError } = logger;

export abstract class OperationsHandler {
  constructor(protected context: IRequestContext, protected uclRequestBody: IUclRequestBody) {}

  protected async notifyDeepsea() {
    if (await this.context.isFeatureFlagActive("DWCO_BDC")) {
      logDebug(
        `notifyDeepsea - uclRequestBody.assignedTenant: ${JSON.stringify(this.uclRequestBody?.assignedTenant)}`,
        { context: this.context }
      );
      const uclFormationClient = new DeepseaServiceUclFormationClient(this.context);
      logInfo(
        `notifyDeepsea - calling DeepseaServiceUclFormationClient.sendFormationRefresh for formationId: ${this.uclRequestBody?.context?.uclFormationId}, formationName: ${this.uclRequestBody?.context?.uclFormationName}`,
        { context: this.context }
      );
      const response = await uclFormationClient.sendFormationRefresh(this.uclRequestBody);
      if (response.status !== Status.CREATED) {
        const msg = `notifyDeepsea error, sendFormationRefresh failed, status code <${
          response.status
        }>, response: <${JSON.stringify(response.body)}`;
        logError(msg, { context: this.context });
        throw new Error(`[TMapS-BDC-OperationsHandler] ${msg}`);
      }
      return response;
    }
  }

  protected setUclErrorPayload(
    state: SynchronizationTenantState.CREATE_ERROR | SynchronizationTenantState.DELETE_ERROR,
    message: string
  ): IUclResponseBody {
    return { state, error: message };
  }

  abstract getSynchronizationPayload(): Promise<IUclResponseBody>;
}
