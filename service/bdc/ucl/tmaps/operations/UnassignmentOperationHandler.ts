/** @format */
// FILEOWNER: [Provisioning]

import { getLogger } from "@sap/dwc-logger";
import { notifyDeepsea } from "../../../../reuseComponents/ucl/utils/deepseaUtils";
import { IUclResponseBody, SynchronizationTenantState, UclOperation } from "../../../../ucl/types";
import { UclSystemManagement } from "../../integratedSystems/UclSystemManagement";
import { IIntegratedSystems, integratedSystems } from "../../integratedSystems/integratedSystems";
import { getBdcTenantEnabledStatus, logAuditConfiguration } from "../../utils";
import { OperationsHandler } from "./OperationHandler";
import { TMapSError } from "./types";

const { logInfo, logWarning } = getLogger("TMapS-BDC-UnassignmentOperationHandler");

export class UnassignmentOperationHandler extends OperationsHandler {
  async getSynchronizationPayload(uclIntegratedSystems: IIntegratedSystems = integratedSystems) {
    try {
      const result = await this.getStatePayload(uclIntegratedSystems);
      await logAuditConfiguration(this.context, result.state, this.uclRequestBody);
      return result;
    } catch (error) {
      const msg = `Operation: ${UclOperation.unassign} has failed.
      Receiver Tenant  State: ${this.uclRequestBody.receiverTenant.state}, Formation Tenant State: ${this.uclRequestBody.assignedTenant.state},
      Application Namespace: ${this.uclRequestBody.receiverTenant.applicationNamespace}. Error: ${error.message}`;

      await logAuditConfiguration(
        this.context,
        SynchronizationTenantState.DELETE_ERROR,
        this.uclRequestBody,
        error ?? undefined
      );
      throw new TMapSError(msg, SynchronizationTenantState.DELETE_ERROR);
    }
  }

  private async getStatePayload(uclIntegratedSystems: IIntegratedSystems = integratedSystems) {
    switch (this.uclRequestBody.receiverTenant.state) {
      case SynchronizationTenantState.DELETING:
        return await this.handleSyncDeletion(uclIntegratedSystems);
      case SynchronizationTenantState.DELETE_ERROR:
        return await this.handleSyncDeletion(uclIntegratedSystems);
      default:
        return this.setUclErrorPayload(
          SynchronizationTenantState.DELETE_ERROR,
          `Unable to find the correct action for DS State: ${this.uclRequestBody.receiverTenant.state}`
        );
    }
  }

  private async handleSyncDeletion(
    uclIntegratedSystems: IIntegratedSystems = integratedSystems
  ): Promise<IUclResponseBody> {
    logInfo(`Handling synchronization ${this.uclRequestBody.receiverTenant.state} state`, { context: this.context });

    await UclSystemManagement.deleteSystemCredentialsRecords(this.context, this.uclRequestBody, uclIntegratedSystems);

    const enabledStatus = await getBdcTenantEnabledStatus(this.context);
    const isEnabled = !!enabledStatus.bdcEnabled;
    const isUnlocked = !enabledStatus.locked;

    const shouldNotifyDeepSea = isEnabled && isUnlocked;
    if (shouldNotifyDeepSea) {
      logInfo(`Calling notifyDeepsea for unassignment operation`, { context: this.context });
      await notifyDeepsea(this.context, this.uclRequestBody);
    } else {
      logWarning(`Skipping notifyDeepsea: System state is ${!isEnabled ? "disabled" : "locked"}`, {
        context: this.context,
      });
    }

    logInfo(`Synchronization READY for ${this.uclRequestBody.assignedTenant.uclSystemName}`, { context: this.context });
    return { state: SynchronizationTenantState.READY };
  }
}
