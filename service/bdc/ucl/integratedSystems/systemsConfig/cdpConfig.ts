import { IRequestContext } from "@sap/deepsea-types";
import { IUclRequestBody } from "../../../../ucl/types";
import { IntegratedSystemCommunicationHandlerBase } from "../IntegratedSystemCommunicationHandlerBase";
import { ApplicationNamespace } from "../../../../reuseComponents/ucl/applicationNamespace";
import { assertBlockFormationDeletion, isFFAllowProperFormationDeletionEnabled } from "../../utils";
import { getLogger } from "../../../../logger";
import { obfuscateCredentials } from "../../../../ucl/utils";
import { ConnectionProperty } from "../types";

const logger = getLogger("TMapS-BDC-CdpConfig");

const CDP_DP_GOVERNANCE_CORRELATION_ID = "sap.bds:federation.cdp:GOVERNANCE";

interface ICdpConfigurationRequest {
  credentials: {
    inboundCommunication: {
      clientCertificateAuthentication: {
        correlationIds: string[];
        certificate: string;
      };
    };
  };
}

interface ICdpConfigurationResponse {
  credentials: {
    outboundCommunication: {
      clientCertificateAuthentication: {
        correlationIds: string[];
      };
    };
  };
  additionalAttributes: {
    connectionsProperties: ConnectionProperty[];
  };
}

/**
 * This class implements the tenant mapping request handler for Customer Data Platform.
 */
export class CdpConfig extends IntegratedSystemCommunicationHandlerBase {
  getLogger(): ReturnType<typeof getLogger> {
    return logger;
  }

  async matchesApplicationNamespace(context: IRequestContext, applicationNamespace: string): Promise<boolean> {
    return applicationNamespace === ApplicationNamespace.CUSTOMER_DATA_PLATFORM;
  }

  private constructConfigurationRequest(certificate: string): ICdpConfigurationRequest {
    return {
      credentials: {
        inboundCommunication: {
          clientCertificateAuthentication: {
            correlationIds: [CDP_DP_GOVERNANCE_CORRELATION_ID],
            certificate: certificate
          }
        }
      }
    }
  }

  async handleFirstSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    const certificate = await this.getBDCTenantCertificate(context);
    const configRequest = this.constructConfigurationRequest(certificate);
    const obfuscateConfigRequest = (configRequest : ICdpConfigurationRequest) => {
      const originalCertificate = configRequest.credentials.inboundCommunication.clientCertificateAuthentication.certificate;
      const obfuscatedCertificate = originalCertificate ? obfuscateCredentials(originalCertificate) as string : originalCertificate;
      return this.constructConfigurationRequest(obfuscatedCertificate);
    }
    logger.logInfo(`handleFirstSynchronizationRequest response: ${JSON.stringify(obfuscateConfigRequest(configRequest))}`, { context });
    return configRequest;
  }

  async handleSecondSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    logger.logInfo(`handleSecondSynchronizationRequest assignedTenant.configuration: ${JSON.stringify(uclRequestBody?.assignedTenant?.configuration)}`, { context });

    const assertDefined = (value: any, errorMsg: string) => {
      if (value === undefined) {
        logger.logError(errorMsg, { context });
        throw new Error(errorMsg);
      }
    }

    const configuration = uclRequestBody?.assignedTenant?.configuration as ICdpConfigurationResponse;
    const correlationId = configuration?.credentials?.outboundCommunication?.clientCertificateAuthentication?.correlationIds?.find(id => id === CDP_DP_GOVERNANCE_CORRELATION_ID);
    assertDefined(correlationId, `Cannot find ${CDP_DP_GOVERNANCE_CORRELATION_ID} in configuration.credentials.outboundCommunication.clientCertificateAuthentication.correlationIds`);

    const connectionsProperty = configuration?.additionalAttributes?.connectionsProperties?.find(property => property.correlationIds?.includes(CDP_DP_GOVERNANCE_CORRELATION_ID) && property.hasOwnProperty("apiProtocol") && property.hasOwnProperty("url"));
    assertDefined(connectionsProperty, `Cannot find connections property in configuration.additionalAttributes.connectionsProperties where correlationIds contains ${CDP_DP_GOVERNANCE_CORRELATION_ID}, and apiProtocol and url exist`);

    return {};
  }

  async handleDeleteSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    if (!(await isFFAllowProperFormationDeletionEnabled(context))) {
      await assertBlockFormationDeletion(context, uclRequestBody);
    }
    return {};
  }
}

export const cdpConfigHandlerInstance = new CdpConfig();
