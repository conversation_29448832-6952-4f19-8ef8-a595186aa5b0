/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { ISecureStore } from "@sap/dwc-credentials";
import { IUclRequestBody } from "../../../../ucl/types";

import { getLogger } from "@sap/dwc-logger";
import { isCanary } from "../../../../lib/node";
import { SecureStore, SecureStoreKey } from "../../../../securestore";
import { assertBlockFormationDeletion, isFFAllowProperFormationDeletionEnabled } from "../../utils";
import { IntegratedSystemCommunicationHandlerBase } from "../IntegratedSystemCommunicationHandlerBase";

const logger = getLogger("TMapS-BDC-DspConfig");
const { logInfo, logError } = logger;

interface IDSPConfiguration {
  setUpSharedCatalog: boolean;
  credentials: {
    inboundCommunication: {
      oAuth: object;
    };
  };
}

export class DspConfig extends IntegratedSystemCommunicationHandlerBase {
  secureStore: ISecureStore;

  getLogger(): ReturnType<typeof getLogger> {
    return logger;
  }

  /**
   * Get the system namespace pattern for the given application namespace.
   * @param context - The request context.
   * @param applicationNamespace The application namespace
   * @returns True if the application namespace matches the system namespace pattern, false otherwise
   */
  async matchesApplicationNamespace(context: IRequestContext, applicationNamespace: string): Promise<boolean> {
    return applicationNamespace === "sap.datasphere";
  }

  /**
   * Handles the first synchronization (inbound) request from DSP (proxied from UCL).
   *
   * @param context - The request context.
   * @param uclRequestBody - The UCL request body.
   * @returns A promise that resolves with null .
   */
  async handleFirstSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    return null;
  }

  /**
   * Handles the second synchronization request.
   *
   * @param context - The request context.
   * @param uclRequestBody - The UCL request body.
   * @returns  configuration with type of IDSPConfiguration.
   */
  async handleSecondSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    // Note: setupSharedCatalog will be handled in notifyDeepsea
    const configuration: IDSPConfiguration = uclRequestBody.assignedTenant.configuration;
    if (!configuration.setUpSharedCatalog) {
      logInfo(`handleSecondSynchronizationRequest setUpSharedCatalog value is False or Not Provided`, { context });
    }

    // Validate Configuration exist
    const payload = uclRequestBody.assignedTenant.configuration?.credentials?.inboundCommunication?.oAuth;
    logInfo(`handleSecondSynchronizationRequest configuration payload oAuth obj: ${JSON.stringify(payload)}`, {
      context,
    });

    if (!payload) {
      logError(`handleSecondSynchronizationRequest oAuth expected in inboundCommunication object, does not exist`, {
        context,
      });
      throw new Error("DSP oAuth config payload does not exist");
    }

    // insert OAuth
    await this.insertDSPOauthClientInCredStore(context, payload);

    if (isCanary()) {
      let result = await this.getInstanceEntryFromCredStore(context);
      logInfo(`handleSecondSynchronizationRequest getInstanceEntryFromCredStore result: ${JSON.stringify(result)}`, {
        context,
      });
    }

    return configuration;
  }

  /**
   * Handles the deletion request to unassign DSP system from UCL Formation.
   *
   * @param context - The request context.
   * @param uclRequestBody - The UCL request body.
   * @returns A promise that resolves to empty object.
   */
  async handleDeleteSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    if (!(await isFFAllowProperFormationDeletionEnabled(context))) {
      await assertBlockFormationDeletion(context, uclRequestBody);
    }
    await this.deleteDSPOauthClientInCredStore(context);
    return null;
  }

  getTenantCredStore(context: IRequestContext): ISecureStore {
    logInfo(`getTenantCredStore: getting credStore from context`, { context });
    return SecureStore.fromRequestContext(context);
  }

  async insertDSPOauthClientInCredStore(context: IRequestContext, data: any) {
    logInfo(`start insertDSPOauthClientInCredStore`, { context });
    const secureStore = this.getTenantCredStore(context);
    logInfo(`insertDSPOauthClientInCredStore: inserting ${JSON.stringify(data)} to cred store`, { context });
    await secureStore.insert(SecureStoreKey.DSPOAuthClientCredsForBdcAcnDpInstall, data);
    logInfo(`insertDSPOauthClientInCredStore: post secureStore insert`, { context });
  }

  async deleteDSPOauthClientInCredStore(context: IRequestContext) {
    logInfo(`start deleteDSPOauthClientInCredStore`, { context });
    const secureStore = this.getTenantCredStore(context);
    logInfo(`deleteDSPOauthClientInCredStore: deleting from credStore`, { context });
    const currentEntry = await this.getInstanceEntryFromCredStore(context);
    if (currentEntry) {
      await secureStore.delete(SecureStoreKey.DSPOAuthClientCredsForBdcAcnDpInstall);
      logInfo(`deleteDSPOauthClientInCredStore: post secureStore delete`, { context });
    } else {
      logInfo(`deleteDSPOauthClientInCredStore: post secureStore delete key did not exist`, { context });
    }
  }

  async getInstanceEntryFromCredStore(context: IRequestContext): Promise<any> {
    logInfo(`getInstanceEntryFromCredStore: calling getTenantCredStore`, { context });
    const secureStore = this.getTenantCredStore(context);
    const currentEntry = await secureStore.retrieve(SecureStoreKey.DSPOAuthClientCredsForBdcAcnDpInstall);
    logInfo(`getInstanceEntryFromCredStore: currentEntry: ${JSON.stringify(currentEntry)}`, { context });
    return currentEntry;
  }
}

export const dspConfigHandlerInstance = new DspConfig();
