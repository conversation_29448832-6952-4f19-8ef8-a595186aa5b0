/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { getLogger } from "../../../../logger";
import { ApplicationNamespace } from "../../../../reuseComponents/ucl/applicationNamespace";
import { IUclRequestBody } from "../../../../ucl/types";
import { assertBlockFormationDeletion, isFFAllowProperFormationDeletionEnabled } from "../../utils";
import { IntegratedSystemCommunicationHandlerBase } from "../IntegratedSystemCommunicationHandlerBase";

const logger = getLogger("TMapS-BDC-FosConfig");

const FOS_DATA_PRODUCT_INSTALLATION = "sap.bdc.foundationService:dataproduct:INSTALLATION";

export class FosConfig extends IntegratedSystemCommunicationHandlerBase {
  static getLogger() {
    // for test usage only
    return logger;
  }

  getLogger(): ReturnType<typeof getLogger> {
    return logger;
  }

  async matchesApplicationNamespace(context: IRequestContext, applicationNamespace: string): Promise<boolean> {
    switch (applicationNamespace) {
      case ApplicationNamespace.S4_PUBLIC_CLOUD:
        return await context.isFeatureFlagActive("DWCO_UCL_TENANT_MAPPING_BDC_DSP_S4_PUBLIC_CLOUD");
      case ApplicationNamespace.HCM:
        return await context.isFeatureFlagActive("DWCO_UCL_TENANT_MAPPING_BDC_DSP_HCM");
      case ApplicationNamespace.S4PCE:
        return true;
      default:
        return false;
    }
  }

  async handleFirstSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    const correlationIds = [FOS_DATA_PRODUCT_INSTALLATION];
    return {
      credentials: {
        inboundCommunication: {
          clientCertificateAuthentication: {
            correlationIds,
            certificate: await this.getBDCTenantCertificate(context),
          },
        },
      },
    };
  }

  async handleSecondSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    // Don't need to do anything here as the DP Installation URL should be persisted by the notifyDeepsea() call in AssignmentOperationHandler.ts
    return {};
  }

  async handleDeleteSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    if (!(await isFFAllowProperFormationDeletionEnabled(context))) {
      await assertBlockFormationDeletion(context, uclRequestBody);
    }
    // Don't need to do anything here as deletion of DP installation URL is handled by notifyDeepsea() in UnassignmentOperationHandler.ts
    return {};
  }
}

export const fosConfigHandlerInstance = new FosConfig();
