/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { isCanary } from "@sap/deepsea-utils";
import { getBdcTenantCertificateChain } from "../../../../connections/ucl/uclConnectionManager";
import { getLogger } from "../../../../logger";
import { ApplicationNamespace } from "../../../../reuseComponents/ucl/applicationNamespace";
import { IUclRequestBody } from "../../../../ucl/types";
import { IntegratedSystemCommunicationHandlerBase } from "../IntegratedSystemCommunicationHandlerBase";

const logger = getLogger("TMapS-BDC-DatabricksConnectConfig");
export interface IUclRequestConnectionsPropertiesDeltaShare {
  correlationIds: string[];
  implementationStandard: string;
  host: string;
}

export interface IUclRequestConnectionsPropertiesRest {
  correlationIds: string[];
  apiProtocol: string;
  url: string;
}

interface IDatabricksConnectConfiguration {
  credentials: {
    outboundCommunication: {
      clientCertificateAuthentication: {
        correlationIds: string[];
      };
    };
  };
  additionalAttributes: {
    connectionsProperties: Array<IUclRequestConnectionsPropertiesDeltaShare | IUclRequestConnectionsPropertiesRest>;
  };
}

const DBX_GOVERNANCE_CORRELATION_ID = "sap.bds:federation.databricks:GOVERNANCE";
const DBX_BROWNFIELD_GOVERNANCE_CORRELATION_ID = "sap.bds:grants:GOVERNANCE";

export class DatabricksConnectConfig extends IntegratedSystemCommunicationHandlerBase {
  static getLogger() {
    // for test usage only
    return logger;
  }

  getLogger(): ReturnType<typeof getLogger> {
    return logger;
  }

  async matchesApplicationNamespace(context: IRequestContext, applicationNamespace: string): Promise<boolean> {
    switch (applicationNamespace) {
      case ApplicationNamespace.DATABRICKS_CONNECT:
        return true;
      case ApplicationNamespace.DATABRICKS_BROWNFIELD_CONNECT:
        return await context.isFeatureFlagActive("DWCO_UCL_TENANT_MAPPING_BDC_DSP_BROWNFIELD_DBX");
      default:
        return false;
    }
  }

  async handleFirstSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    const correlationIds = (await this.isDbxBrownfieldWorkflow(context, uclRequestBody))
      ? [DBX_BROWNFIELD_GOVERNANCE_CORRELATION_ID]
      : [DBX_GOVERNANCE_CORRELATION_ID];

    if (isCanary()) {
      logger.logInfo(`InboundCommunication: correlationIds=${JSON.stringify(correlationIds)}`, { context });
    }
    return {
      credentials: {
        inboundCommunication: {
          clientCertificateAuthentication: {
            correlationIds,
            certificate: await getBdcTenantCertificateChain(context),
          },
        },
      },
    };
  }

  async handleSecondSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    // We do not need to create a (shared) connection here, since the Governance REST url for DBX is passed on via a
    // subsequent call to "uclFormationClient.sendFormationRefresh" to the Repository where a custom class object of type
    // CP_BDC_DP_SHARE_BASE_URL gets created
    const isDbxBrownfieldWorkflow = await this.isDbxBrownfieldWorkflow(context, uclRequestBody);
    const configuration: IDatabricksConnectConfiguration = uclRequestBody.assignedTenant!.configuration;
    let governanceRestEndpointIncluded = false;
    if (configuration?.additionalAttributes?.connectionsProperties?.length > 0) {
      governanceRestEndpointIncluded = configuration.additionalAttributes.connectionsProperties.some((config) => {
        const governanceCorrelationId = isDbxBrownfieldWorkflow
          ? DBX_BROWNFIELD_GOVERNANCE_CORRELATION_ID
          : DBX_GOVERNANCE_CORRELATION_ID;
        return (
          config.correlationIds.includes(governanceCorrelationId) &&
          (config as IUclRequestConnectionsPropertiesRest).url
        );
      });
    }
    if (governanceRestEndpointIncluded) {
      logger.logInfo(`Governance REST endpoint included in second synchronization request`, { context });
    } else {
      logger.logError(`No Governance REST endpoint included in second synchronization request`, { context });
    }
  }

  async handleDeleteSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    // no action is needed here
  }

  async isDbxBrownfieldWorkflow(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<boolean> {
    const isDbxBrownfield =
      uclRequestBody.assignedTenant.applicationNamespace === ApplicationNamespace.DATABRICKS_BROWNFIELD_CONNECT;
    const isDbxBrownfieldEnabled = await context.isFeatureFlagActive("DWCO_UCL_TENANT_MAPPING_BDC_DSP_BROWNFIELD_DBX");
    return isDbxBrownfield && isDbxBrownfieldEnabled;
  }
}

export const databricksConnectConfigHandlerInstance = new DatabricksConnectConfig();
