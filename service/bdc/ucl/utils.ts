/** @format */
import { IRequestContext } from "@sap/deepsea-types";
import { TimeUnit } from "@sap/deepsea-utils";
import { FeatureFlagProvider } from "@sap/dwc-context-checks";
import { SecureStoreKey } from "@sap/dwc-credentials";
import { CodedError } from "@sap/dwc-express-utils";
import { Payload, httpClient } from "@sap/dwc-http-client";
import { getLogger } from "@sap/dwc-logger";
import { StatusCodes } from "http-status-codes";
import { getUclCredentialsManager } from ".";
import { store } from "../../configurations/main";
import { TenantInformationProvider } from "../../featureflags/TenantInformationProvider";
import { ITenantEnabled } from "../../featureflags/types";
import { isCanary } from "../../lib/node";
import { ExternalCallCategory } from "../../repository/security/common/externalCallTypes";
import { RequestContext } from "../../repository/security/requestContext";
import { getAuditLogClient } from "../../reuseComponents/auditlogger/audit-log-utils";
import { createAsyncCache } from "../../reuseComponents/caching/AsyncCacheFactory";
import {
  FormationTypeNames,
  IUclRequestBody,
  IUclRequestContext,
  IX509Credentials,
  SynchronizationTenantState,
  UclUrl,
} from "../../ucl/types";

const { logInfo, logWarning, logError } = getLogger("UclUtils-BDC");
const ASSIGNED = "Assigned";
const NOT_ASSIGNED = "Unassigned";

const formationTypeInfoCache = createAsyncCache<string, any>("formationTypeInfoCache", {
  timeout: TimeUnit.DAYS.toMillis(7),
});

interface IFormationTypeInfo {
  data: {
    result: {
      name: string;
      id: string;
    };
  };
}

interface IFormationTypeInfoList {
  data: {
    result: {
      data: Array<{
        id: string;
        name: string;
        applicationTypes: string[];
      }>;
    };
  };
}

export const fetchFormationTypePayload = (formationTypeId: string) => ({
  query: ` query{
      result: formationTemplate(id: \"${formationTypeId}\") {
        name
        id
      }
    }`,
});

export const listFormationTypePayload = {
  query: `query {
    result: formationTemplates{
      data {
        id
        name
        applicationTypes
      }
    }
  }`,
};

async function fetchUcl(context: IRequestContext, certificate: IX509Credentials, payload: any): Promise<Payload> {
  const response = await httpClient.call({
    url: `https://${isCanary() ? UclUrl.canary : UclUrl.live}/director/graphql`,
    opts: {
      method: "POST",
      requestContext: context,
      callTimeout: TimeUnit.SECONDS.toMillis(45),
      callCategory: ExternalCallCategory.UCL,
      key: certificate.privateKey,
      cert: certificate.clientCertificateChain,
      minVersion: "TLSv1.2",
      rejectUnauthorized: false,
      body: payload,
    },
  });
  return response.body;
}

async function generateTenantCertificate(context: IRequestContext, assignmentId: string) {
  const credentials: IX509Credentials = await getUclCredentialsManager(context).getCredentials(
    SecureStoreKey.UclTenantCredentials,
    assignmentId
  );
  logInfo(`Client credentials retrieved for assignment id ${assignmentId}`, { context });
  return credentials;
}

export async function clearCache(formationTypeId: string) {
  await formationTypeInfoCache.forget(formationTypeId);
}

export async function fetchFormationTypeNameWithFallback(
  context: IRequestContext,
  formationTypeId: string,
  assignmentId: string,
  retryOnError: boolean = true
): Promise<FormationTypeNames | undefined> {
  try {
    return await formationTypeInfoCache.get(formationTypeId, (_forget, _updateTimeout) =>
      fetchFormationTypeName(context, formationTypeId, assignmentId)
    );
  } catch (error) {
    logError(`Unable to get formation type or formation type doesn't exist.`, { context });
    if (retryOnError) {
      logInfo(`Clearing cache and retrying to get formation type.`, { context });
      await clearCache(formationTypeId);
      return await fetchFormationTypeNameWithFallback(context, formationTypeId, assignmentId, false);
    }
  }
}

async function fetchFormationTypeName(
  context: IRequestContext,
  formationTypeId: string,
  assignmentId: string
): Promise<FormationTypeNames | undefined> {
  try {
    const certificate = await generateTenantCertificate(context, assignmentId);
    const payload = fetchFormationTypePayload(formationTypeId);
    const formationTypes = (await fetchUcl(context, certificate, payload)) as IFormationTypeInfo;
    return formationTypes.data.result.name as FormationTypeNames;
  } catch (e) {
    throw new Error(`Unable to get formation type or formation type doesn't exist.`);
  }
}

export async function fetchListFormationsType(context: IRequestContext, assignmentId: string) {
  try {
    const certificate = await generateTenantCertificate(context, assignmentId);
    const formationTypesList = (await fetchUcl(
      context,
      certificate,
      listFormationTypePayload
    )) as IFormationTypeInfoList;
    return formationTypesList.data.result.data;
  } catch (e) {
    logError(`Unable to fetch formation type list.`, { context });
    throw new Error("Unable to fetch formation type list.");
  }
}

//TODO: extract into wrapper
export async function auditLogConfigurationActivityCall(
  context: IRequestContext,
  applicationNamespace: string,
  operation: string,
  auditLogMessage: string
): Promise<void> {
  logInfo(`[auditLogConfigurationActivityCall] start to send audit log - ${auditLogMessage}`, { context });
  const auditLogger = getAuditLogClient();
  let contextForAuditlog;
  try {
    contextForAuditlog = RequestContext.createNewForBackground(context);
    // update the tenantId to undefined just for the case of sending audit log for UCL tenant mapping process
    // set tenantId to undefined to fix the issue of invisible audit log from audit log viewer
    // if the tenantId is not set, the tenant request parameter will be set to fallbackTenantId which is configured at audit-log-utils.ts
    contextForAuditlog.userInfo = { ...contextForAuditlog.userInfo, tenantId: undefined };
    await auditLogger.configurationChange(
      {
        type: "BDC UCL Tenant Mapping",
        attributes: [
          {
            name: "Tenant Mapping: " + applicationNamespace,
            old: operation === "assign" ? NOT_ASSIGNED : ASSIGNED,
            new: `${auditLogMessage}. Process triggered by ${contextForAuditlog.userInfo.userName}`,
          },
        ],
      },
      contextForAuditlog
    );

    logInfo(`[auditLogConfigurationActivityCall] Succeed to send audit log - ${auditLogMessage}`, {
      context,
    });
  } catch (error) {
    logError([`[auditLogConfigurationActivityCall] error while sending audit log - ${auditLogMessage}`, error], {
      context,
    });
  } finally {
    void contextForAuditlog?.finish();
  }
}

export async function logAuditConfiguration(
  context: IRequestContext,
  status: SynchronizationTenantState,
  requestBody: IUclRequestBody,
  error?: Error
): Promise<void> {
  const isFeatureFlagActive = await FeatureFlagProvider.isFeatureActive(context, "DWCO_UCL_TENANT_MAPPING_BDC_TENANT");
  let auditMessage = "";
  if (isFeatureFlagActive) {
    const receiverTenantApplicationTenantId = requestBody.receiverTenant.applicationTenantId;
    const assignedTenantApplicationTenantId = requestBody.assignedTenant.applicationTenantId;
    const uclRequestBodyContext = requestBody.context;

    if (error) {
      auditMessage = getFailureAuditLogMessage(
        receiverTenantApplicationTenantId,
        assignedTenantApplicationTenantId,
        error.message,
        status,
        uclRequestBodyContext
      );
    } else {
      auditMessage = getSuccessAuditLogMessage(
        receiverTenantApplicationTenantId,
        assignedTenantApplicationTenantId,
        status,
        uclRequestBodyContext
      );
    }
    await auditLogConfigurationActivityCall(
      context,
      requestBody.assignedTenant.applicationNamespace,
      requestBody.context.operation,
      auditMessage
    );
  } else {
    logInfo("Audit logging is disabled, DWCO_UCL_TENANT_MAPPING_BDC_TENANT is off", { context });
  }
}

function getSuccessAuditLogMessage(
  consumerId: string,
  producerId: string,
  status: SynchronizationTenantState,
  uclContext?: IUclRequestContext
) {
  return `Process UCL Formation Name:<${uclContext?.uclFormationName}>
  Formation id:<${uclContext?.uclFormationId}> request receiving tenant <${consumerId}>,
  assigned tenant <${producerId}>, status <${status}>, operation: <${uclContext?.operation}>`;
}

async function isConfigUnassignTenantMappingEventAllowed(context: IRequestContext): Promise<boolean> {
  let isAllowed = false;
  try {
    // get value of DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT from configuration service
    const storeValue = await store.getEffectiveValue(context, "DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT", {
      preferTenantScope: true,
    });
    if (storeValue) {
      const unassignAllowedConfig = JSON.parse(storeValue).isAllowed;
      // if the value of DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT in configuration service is ON, the unassign tenant mapping event is allowed
      if (unassignAllowedConfig === "ON") {
        isAllowed = true;
      }
    }
  } catch (error) {
    // error to get features from configuration service, return forbidden as result for this case
    logError(["Can not get features from configuration service", error], { context });
  }
  return isAllowed;
}

function getFailureAuditLogMessage(
  consumerId: string,
  producerId: string,
  errorMessage: string,
  status: SynchronizationTenantState,
  uclContext?: IUclRequestContext
) {
  return `Failed to process UCL Formation Name:<${uclContext?.uclFormationName}>
  Formation id:<${uclContext?.uclFormationId}> request receiving tenant <${consumerId}>,
  assigned tenant <${producerId}>, status <${status}>, operation: <${uclContext?.operation}>,
   Error Message: ${errorMessage}`;
}

async function isFFBlockFormationDeletionEnabled(context: IRequestContext): Promise<boolean> {
  return await context.isFeatureFlagActive("DWCO_BDC_BLOCK_FORMATION_DELETION");
}

export async function isFFAllowProperFormationDeletionEnabled(context: IRequestContext): Promise<boolean> {
  return await context.isFeatureFlagActive("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION");
}

function getBlockFormationDeletionErrorMessage(uclRequestBody: IUclRequestBody) {
  return `Deletion is not allowed. Contact SAP Support to request changes to ${
    uclRequestBody?.context?.uclFormationName ? `${uclRequestBody?.context?.uclFormationName} formation` : "formation"
  }`;
}

export async function assertBlockFormationDeletion(context: IRequestContext, uclRequestBody: IUclRequestBody) {
  if (
    (await isFFBlockFormationDeletionEnabled(context)) &&
    !(await isConfigUnassignTenantMappingEventAllowed(context))
  ) {
    const errorMessage = getBlockFormationDeletionErrorMessage(uclRequestBody);
    logError(errorMessage, { context });
    throw new CodedError("unassignEvent", errorMessage, StatusCodes.FORBIDDEN);
  }
}

/**
 * Check if a BDC tenant is enabled and not locked.
 * Wrapper around isBdcTenantEnabled to remove dependency on TMS.
 * If TMS call fails, we default to assuming the tenant is enabled and not locked.
 *
 * @param {IRequestContext} context - request context
 * @returns {Promise<ITenantEnabled>} - tenant status details
 */
export async function getBdcTenantEnabledStatus(context: IRequestContext): Promise<ITenantEnabled> {
  let tenantStatus: ITenantEnabled = { bdcEnabled: true, locked: false }; // default to bdcEnabled and not locked

  try {
    tenantStatus = await TenantInformationProvider.isBdcTenantEnabled(context);
    logInfo(`BDC tenant enabled status: ${JSON.stringify(tenantStatus)}`, { context });
    return tenantStatus;
  } catch (error) {
    logWarning(
      `Error determining BDC tenant enabled status, defaulting to BDC tenant is enabled and not locked: ${error.message}`,
      {
        context,
      }
    );
    return tenantStatus;
  }
}

/**
 * Asserts that the BDC Cockpit tenant is enabled and not locked.
 * Throws an error if the tenant is not enabled or is locked.
 * If TMS call fails, we default to assuming the tenant is enabled and not locked.
 *
 * @param {IRequestContext} context - request context
 * @throws {CodedError} - throws a coded error if tenant is not enabled or is locked
 * @returns {Promise<void>}
 */
export async function assertBdcTenantEnabled(context: IRequestContext): Promise<void> {
  const tenantStatus = await getBdcTenantEnabledStatus(context);

  if (!tenantStatus.bdcEnabled) {
    const errorMessage = "Tenant is not BDC enabled";
    logError(errorMessage, { context });
    throw new CodedError("bdcDisabled", errorMessage, StatusCodes.METHOD_NOT_ALLOWED);
  }

  if (tenantStatus.locked) {
    const errorMessage = "Tenant is locked";
    logError(errorMessage, { context });
    throw new CodedError("tenantLocked", errorMessage, StatusCodes.LOCKED);
  }

  logInfo(`Tenant is BDC enabled and unlocked`, { context });
}
