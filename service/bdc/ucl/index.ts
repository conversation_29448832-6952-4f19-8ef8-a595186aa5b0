/** @format */
// FILEOWNER: [Provisioning]

import { IRequestContext } from "@sap/deepsea-types";
import { Hyperscaler, getHyperscaler } from "@sap/deepsea-utils";
import { SecureStoreKey as DwcSecureStoreKey, UclCredentialsManager } from "@sap/dwc-credentials";
import { httpClient } from "@sap/dwc-http-client";
import { getLogger } from "@sap/dwc-logger";
import * as xsenv from "@sap/xsenv";
import { getLandscapeId, isCanary } from "../../lib/node";
import { certServiceContext } from "../../reuseComponents/credentials";
import { SecureStore } from "../../securestore";
import {
  IUclApplicationContext,
  IUclGraphQLData,
  IUclRegistrationInfo,
  IX509Credentials,
  UclUrl,
} from "../../ucl/types";

const logger = getLogger("BDC UCL Service");

const CLD_SYSTEM_ROLE = "BDCC";

const TEMPLATE_DESCRIPTION = `"SAP Business Data Cloud"`;

// TODO update https://github.tools.sap/CPA/namespace-registry/blob/main/namespaces.yaml
const TEMPLATE_APPLICATION_NAMESPACE = `"sap.bdc-cockpit"`;

const TEMPLATE_ACCESS_LEVEL = `GLOBAL`;

const TEMPLATE_APPLICATION_INPUT = `{
  name: "{{name}}"
  providerName: "SAP"
  description: "SAP Business Data Cloud"
  localTenantID: "{{tenant-id}}"
  labels: {
      displayName: "{{display-name}}"
      region: "{{region}}"
      applicationType: "SAP Business Data Cloud"
  }
}`;

const TEMPLATE_PLACEHOLDERS = `[
  { name: "name", description: "app name" }
  { name: "display-name", description: "app display name in UIs" }
  { name: "tenant-id", description: "app's tenant id as it's known in the product's domain", jsonPath: "$.externalId" }
  { name: "region", description: "app's region as it's known in the product's domain", jsonPath: "$.additionalAttributes.systemSCPLandscapeID" }
]`;

const QUERY_RETURN_OPTIONS = `{
  id
  name
  description
  applicationNamespace
  accessLevel
  applicationInput
  placeholders {
    name
    description
    jsonPath
  }
  webhooks {
    id
    applicationTemplateID
    type
    url
    urlTemplate
    mode
    auth {
      accessStrategy
    }
  }
  labels
}`;

const SYSTEM_SCP_LANDSCAPE_ID_FOR_CANARY_AWS = "cf-eu10-canary";

export class UclRegistrationClient {
  public static create(region: string): UclRegistrationClient {
    return new UclRegistrationClient(region);
  }

  private constructor(private region: string) {}

  private getQueryCLDProvidersPayload() {
    return {
      query: `query {
        result: applicationTemplates(
          filter: [
            {key: "cldSystemRole" query: "$[*] ? (@ == \\"${CLD_SYSTEM_ROLE}\\")"},
            {key: "environment" query: "\\"${isCanary() ? `orca${this.region}` : this.region}\\""}
          ]
        ) {
          data ${QUERY_RETURN_OPTIONS}
        }
      }`,
    };
  }

  private getTenantMappingUrl(): string {
    return `https://api.${isCanary() ? this.region : getLandscapeId()}.bdc.${isCanary() ? "dev" : "cloud"}.sap/v1/bdc`;
  }

  private getTemplateWebhooks(): string {
    // We use version "datasphere:v1" as we expect to receive and send the same UCL payload format as Datasphere.
    // If we do need to define a BDC specific version, refer to https://github.tools.sap/cmp/management-plane-config/blob/master/resources/compass/config/base/compass-overrides.yaml
    return `[{
      type: APPLICATION_TENANT_MAPPING
      url: "${this.getTenantMappingUrl()}"
      version: "datasphere:v1"
      mode: ASYNC_CALLBACK
      auth: {
        accessStrategy: "sap:cmp-mtls:v1"
      }
    }]`;
  }

  private getTemplateLabelsForCanary() {
    return `{
      environment: "orca${this.region}"
      managed_app_provisioning: true
      cldSystemRole: ["${CLD_SYSTEM_ROLE}"]
      region: "orca${this.region}"
      rm_group: "bdccockpit.bdc.da.btp.sap"
      rm_type: "BDCCockpitTenant"
      rm_version: "v1"
      slisFilter: [
        {
          productId: "${CLD_SYSTEM_ROLE}",
          filter: ${this.getFilter()}
        }
      ]
    }`;
  }

  private getFilter() {
    const hyperscaler = getHyperscaler();
    let systemIdPrefix;
    let landscapeFilter;
    if (hyperscaler === Hyperscaler.AWS) {
      systemIdPrefix = `sac-orca${this.region}`;
      landscapeFilter = `{
        key: "$.additionalAttributes.systemSCPLandscapeID"
        value: ["${SYSTEM_SCP_LANDSCAPE_ID_FOR_CANARY_AWS}"]
        operation: "include"
      }`;
    } else {
      systemIdPrefix = `${hyperscaler}-${this.region}`;
      landscapeFilter = "";
    }
    let systemIdFilter = `{
      key: "$.systemId"
      value: [
        "${systemIdPrefix}_BDCC",
        "${systemIdPrefix}_BDCC_INTERNAL",
        "${systemIdPrefix}_BDCC_INTP",
      ]
      operation: "include"
    }`;
    let regionIdFilter = `{
      key: "$.regionId"
      value: [
        "NON"
      ]
      operation: "exclude"
    }`;
    return `[${[systemIdFilter, landscapeFilter, regionIdFilter].reduce(
      (result, partFilter) => (result ? (partFilter ? `${result},${partFilter}` : result) : partFilter),
      ""
    )}]`;
  }

  private getTemplateLabelsForProduction() {
    const landscapeId = getLandscapeId();
    const sidPrefix = landscapeId.toUpperCase();
    return `{
      environment: "${this.region}"
      managed_app_provisioning: true
      cldSystemRole: ["${CLD_SYSTEM_ROLE}"]
      region: "${this.region}"
      rm_group: "bdccockpit.bdc.da.btp.sap"
      rm_type: "BDCCockpitTenant"
      rm_version: "v1"
      slisFilter: [
        {
          productId: "${CLD_SYSTEM_ROLE}",
          filter: [
            {
                key: "$.systemId"
                value: [
                    "${sidPrefix}-sac-sac${landscapeId}_BDCC",
                    "${sidPrefix}-sac-sac${landscapeId}_BDCC_INTERNAL",
                    "${sidPrefix}-sac-sac${landscapeId}_BDCC_INTP",
                ]
                operation: "include"
            },
            {
                key: "$.additionalAttributes.systemSCPLandscapeID"
                value: [
                  "${this.region}"
                ]
                operation: "include"
            },
            {
                key: "$.regionId"
                value: [
                  "NON"
                ]
                operation: "exclude"
            }
          ]
        }
      ]
    }`;
  }

  private getTemplateLabels(): string {
    return isCanary() ? this.getTemplateLabelsForCanary() : this.getTemplateLabelsForProduction();
  }

  public getRegisterPayload() {
    return {
      query: `mutation {
        result: createApplicationTemplate(
          in: {
            name: "SAP Business Data Cloud (${isCanary() ? `orca${this.region}` : this.region})"
            description: ${TEMPLATE_DESCRIPTION}
            applicationNamespace: ${TEMPLATE_APPLICATION_NAMESPACE}
            accessLevel: ${TEMPLATE_ACCESS_LEVEL}
            applicationInput: ${TEMPLATE_APPLICATION_INPUT}
            placeholders: ${TEMPLATE_PLACEHOLDERS}
            webhooks: ${this.getTemplateWebhooks()}
            labels: ${this.getTemplateLabels()}
          }
        ) ${QUERY_RETURN_OPTIONS}
      }`,
    };
  }

  private getUpdatePayload(templateId: string) {
    // Note - UCL does not allow updating 'labels' property
    return {
      query: `mutation {
        result: updateApplicationTemplate(
          id: "${templateId}"
          in: {
            name: "SAP Business Data Cloud (${isCanary() ? `orca${this.region}` : this.region})"
            description: ${TEMPLATE_DESCRIPTION}
            applicationNamespace: ${TEMPLATE_APPLICATION_NAMESPACE}
            accessLevel: ${TEMPLATE_ACCESS_LEVEL}
            applicationInput: ${TEMPLATE_APPLICATION_INPUT}
            placeholders: ${TEMPLATE_PLACEHOLDERS}
            webhooks: ${this.getTemplateWebhooks()}
          }
        ) ${QUERY_RETURN_OPTIONS}
      }`,
    };
  }

  private async query(payload: any): Promise<IUclGraphQLData> {
    const url = `https://${isCanary() ? UclUrl.canary : UclUrl.live}/director/graphql`;
    const credsManager = getUclCredentialsManager({ tenantId: "dummy" } as IRequestContext);
    const serviceCreds: IX509Credentials = await credsManager.getCredentials(DwcSecureStoreKey.UclServiceCredentials);

    return await httpClient
      .call({
        url,
        opts: {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          callTimeout: 45000,
          key: serviceCreds.privateKey,
          cert: serviceCreds.clientCertificateChain,
          minVersion: "TLSv1.2",
          rejectUnauthorized: true,
          body: payload,
        },
      })
      .then((response) => {
        const body = response.body as any;
        if (body.errors) {
          throw new Error(
            (body.errors as any[]).reduce<string>(
              (msg, err) => `${msg === "errors" ? `${msg}: ` : ", "}[${err.message}]`,
              "errors"
            )
          );
        }
        const result = (response.body as any).data.result;
        if (result.data) {
          return result.data[0];
        }
        return result;
      });
  }

  public async getApplicationTemplate(): Promise<IUclGraphQLData> {
    let result;
    try {
      const payload = this.getQueryCLDProvidersPayload();
      result = await this.query(payload);
    } catch (error) {
      logger.logErrorWithOptionalContext(
        `Failed to query Business Data Cloud application template for region ${uclContext.region} with UCL service. Error: ${error.message}`
      );
      throw error;
    }
    return result;
  }

  public async registerApplicationTemplate(): Promise<IUclGraphQLData> {
    let result;
    try {
      const payload = this.getRegisterPayload();
      result = await this.query(payload);
    } catch (error) {
      logger.logErrorWithOptionalContext(
        `Failed to register Business Data Cloud application template for region ${uclContext.region} with UCL service. Error: ${error.message}`
      );
      throw error;
    }
    logger.logInfoWithOptionalContext(
      `Successfully registered Business Data Cloud application template for region ${uclContext.region} with UCL service.`
    );
    return result;
  }

  public async updateApplicationTemplate(templateId: string): Promise<IUclGraphQLData> {
    let result;
    try {
      const payload = this.getUpdatePayload(templateId);
      result = await this.query(payload);
    } catch (error) {
      logger.logErrorWithOptionalContext(
        `Failed to update Business Data Cloud application template for region ${uclContext.region} with UCL service. Error: ${error.message}`
      );
      throw error;
    }
    logger.logInfoWithOptionalContext(
      `Successfully updated Business Data Cloud application template for region ${uclContext.region} with UCL service.`
    );
    return result;
  }
}

const uclContext: IUclApplicationContext = {
  region: "",
  application: "",
};

const uclRegistration: IUclRegistrationInfo = {
  registered: false,
};

export function getUclCredentialsManager(requestContext: IRequestContext): UclCredentialsManager {
  SecureStore.initializeCache();
  return new UclCredentialsManager(requestContext, uclContext, certServiceContext ?? undefined);
}

function requiresUpdate(templateInfo: IUclGraphQLData): boolean {
  // Currently, no templates need to be updated
  return false;
}

export async function ensureRegisteredWithUCL(): Promise<void> {
  const uclClient = UclRegistrationClient.create(uclContext.region);
  let templateInfo: IUclGraphQLData;
  try {
    templateInfo = await uclClient.getApplicationTemplate();
    if (!templateInfo) {
      // register application with UCL
      templateInfo = await uclClient.registerApplicationTemplate();
    } else if (requiresUpdate(templateInfo)) {
      uclRegistration.applicationTemplateInfo = await uclClient.updateApplicationTemplate(templateInfo.id as string);
    } else {
      logger.logInfoWithOptionalContext(
        `Business Data Cloud application template for region ${uclContext.region} is already registered with UCL service.`
      );
    }
    uclRegistration.applicationTemplateInfo = templateInfo;
    uclRegistration.registered = true;
  } catch (err) {
    uclRegistration.registered = false;
    uclRegistration.lastError = err;
  }
}

try {
  const service = xsenv.getServices({ "user-provided": { name: "orca-env" } });
  if (!service) {
    throw new Error(`User-provided application configuration (orca-env) not found`);
  }

  const deploymentRegion = service["user-provided"].deployment;
  if (!deploymentRegion) {
    throw new Error(`No "org" property found in orca-env configuration`);
  }
  uclContext.region = isCanary() ? deploymentRegion : `cf-${getLandscapeId()}`; // on CLD systemSCPLandscapeID is "cf-<landscapeRegion>"

  // TODO Is there a way to get this value programmatically once BDC is running in its own service?
  // Note - This value is used for the CN subject for the application level certificate.
  // Refer to UclCredentialsManager.prototype.getCommonName function.
  uclContext.application = "businessdatacloud";

  setTimeout(() => {
    // check and, if not yet happened, register application with UCL service
    ensureRegisteredWithUCL().catch((err) => logger.logErrorWithOptionalContext({ ...err }));
  }, 3000);
} catch (err) {
  logger.logErrorWithOptionalContext({ ...err });
}

export { uclContext, uclRegistration };
