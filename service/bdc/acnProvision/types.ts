/** @format */

import { IJsonObject, IRequestContext, IStepDetail, StatusType } from "@sap/deepsea-types";
import { ResolvedResponse } from "@sap/dwc-http-client";
import { Application, BDCProvider } from "../../../shared/bdccockpit/Types";
import type { RequestOptions as Options } from "./dependencies";

export interface IBdcOnboardingPayload {
  acnPackageId?: ACNPackageId;
  uclSystemTenantId?: string;
  uclSystemAlias?: string;
  uclSystemBusinessName?: string;
  acnCategory?: string;
  uclFormationId?: string;
  uclAssignmentId?: string;
  importAnyway?: boolean;
  packageVersion?: string;
}
export interface IBdcOffboardingPayload {
  acnPackageId?: ACNPackageId;
  acnCategory?: string;
  spaceName?: string;
  packageVersion?: string;
}
export enum BdcOnboardingErrorCode {
  IS_NOT_TECHNICAL_TOKEN = "Is not technical token",
  MISSING_TENANT_ID = "Missing tenant id",
  MISSING_CORRELATION_ID = "Missing correlation id",
  MISSING_REQUIRED_PARAMETERS = "Missing required parameters",
  INTERNAL_SERVER_ERROR = "Internal Server Error",
}

// ACN
export interface IAcnImportBody {
  type: string;
  packageName?: string;
  publicPackage?: number;
  privatePackage?: number;
  allObjects: boolean;
  xsParameters: string;
  serviceParameters: string;
  user?: string;
}
export interface IAcnImportStatusOutput {
  status: string;
  message: string | null;
  debugMessage: string | null;
}
export interface IAcnImportOutput {
  jobId: string;
  packageName: string;
  started: Date | null;
  finished: Date | null;
  state: IAcnImportStatusOutput;
  objects?: IJsonObject[];
}
export interface IBdcOnboardingProgressOutput {
  importJobStatus: StatusType | undefined;
  replicationFlowJobStatus: StatusType | undefined;
}
export interface IBdcOnboardingJobIds {
  importJobId: string;
  replicationFlowJobId: string;
}
/**
 * The response object for the BDC offboarding process.
 */
export interface IBdcOffboardingResponse {
  acnDeleteJobId: string;
  acnDeleteJobLocation?: string;
  message?: string;
}
export interface ICreateAcnDeleteJobResult {
  /**
   * The job ID of the ACN delete job if any.
   */
  acnDeleteJobId: string;
  acnDeleteJobLocation: string;
}
export interface ICreateAcnDeleteJobParams {
  acnPackageId?: ACNPackageId;
  acnCategory?: string;
  spaceName?: string;
  packageVersion?: string;
}
export interface IBdcOffboardingProgressOutput {
  acnDeleteJobId: string;
  acnDeleteJobStatus: StatusType | undefined;
}
export interface IAcnDeleteBody {
  type: string;
  packageName?: string;
  publicPackage?: number;
  privatePackage?: number;
  allObjects: boolean;
  serviceParameters: string;
  user?: string;
}
export interface IAcnDeleteBodyServiceParameters {
  correlationId: string;
  customParameters: {
    BDCOffboarding: boolean;
    acnCategory: string;
  };
  packageVersion?: string;
}
export interface IBdcOnboardingAcnProgressOutput {
  jobId: string | null;
  spaceNames?: string[];
  importExecuteResult?: IStepDetail;
}
export interface IBdcCreateImportJobOutput {
  jobId: string;
}
export enum AcnImportPollingRequestStatus {
  "Pending" = "Pending",
  "Executing" = "Executing",
  "Done" = "DONE",
  "Failed" = "Failed",
  "Warning" = "Warning",
}
export interface IAcnImportPollingRestServiceConfig {
  monitoringUriComputed?: (response: ResolvedResponse) => string;
  retryAfterComputed?: () => number | undefined;
  postStatusCheck?: boolean;
  maxPollingTime?: number;
}
export interface IGenericAsyncResponse<T_ResponseStatus = PollingHttpRestRequestStatus> {
  status: T_ResponseStatus;
  requestId?: string;
}
export enum PollingHttpRestRequestStatus {
  "starting" = "starting",
  "finished" = "finished",
}
export interface IHttpCallbackOptions<T_HttpBody = any, T_FinalResponse = any> {
  /**
   * handler to process the exception response before it is sent to the error callback
   */
  exceptionProcessor: (err: unknown, requestBody: T_HttpBody) => T_FinalResponse;

  /**
   * handler to process the response before it is sent to the success or error callback
   */
  responseProcessor: (response: ResolvedResponse, requestBody: T_HttpBody) => T_FinalResponse;

  /**
   * Inject or provide any necessary information for the service integration most probably correlation-id, and bearer token
   */
  beforeSend: (options: Readonly<Options>) => Options;

  retryIfThrottled?: boolean;
}
export interface IHttpRestClient {
  getUrl: (uri: string | undefined) => string;
  post<T_HttpResponse = any>(endpoint: string, body: any, callbacks: IHttpCallbackOptions): Promise<T_HttpResponse>;
  get(endpoint: string, callbacks: IHttpCallbackOptions): Promise<ResolvedResponse>;
  setContext: (context: IRequestContext) => void;
  getContext: () => IRequestContext;
}
export interface IPollingHttpCallbackOptions<
  T_HttpBody = any,
  T_FinalResponse = any,
  T_StatusResponse = IGenericAsyncResponse
> extends IHttpCallbackOptions<T_HttpBody, T_FinalResponse> {
  /**
   * Success callback handler, after successfully finishing the request
   */
  success: (result: T_FinalResponse) => void;

  /**
   * Error callback handler, if the initial request failed due to any reason(service not available or not a valid csn or so)
   */
  error: (result: unknown) => void;

  /**
   * Callback before doing monitoring phase, generally to setup the background tasks
   */
  beforeStatusCheck: (asyncClient: IHttpRestClient) => void;

  /**
   * callback to update progress information
   */
  progress?: (status: T_StatusResponse) => Promise<void>;
}
export enum BdcOnBoardingStep {
  S_IMPORT_PACKAGE = "import package",
  S_DEPLOY_SPACE = "deploy space",
  S_UPDATE_CONNECTION = "update connections",
  S_DEPLOY = "deploy",
  S_REDEPLOY_REPAIRED_CSN = "redeploy repaired csn",
  S_LAST_STEP = S_REDEPLOY_REPAIRED_CSN,
}
export interface IAcnPackageIdentifierInfo {
  name: string;
  publicPackage: number;
  privatePackage?: number;
}
export interface RequiredProviderApplications {
  provider: BDCProvider;
  applications: Application[];
}
export type ACNPackageId = number;
export type ACNPackageWaveId = number;
export interface PackageIdWaveIdPair {
  packageId: ACNPackageId;
  packageWaveId: ACNPackageWaveId;
}
export interface ACNPackageFieldI18n {
  langCode: string;
  value: string | null;
}
export interface ACNPackageField {
  fieldType: string;
  fieldI18n: ACNPackageFieldI18n[];
  description: string | null;
  lastUpdated: string | null;
}
export interface ACNPackagePermission {
  tenantUrl: string | null;
  landscapeId: string | null;
  userId: string | null;
  oemId: string | null;
  erpNumber: string | null;
  shareWithTenantErpNumber: string | null;
  list: boolean;
  read: boolean;
  write: boolean;
  grant_access: boolean;
  change_owner: boolean;
  delete: boolean;
  lastUpdated: string | null;
  isEditable: boolean | null;
}
export interface ACNChildInfo {
  itemId: number;
  parentItemId: number | null;
  waveItemId: number;
  type: string;
  fields: ACNPackageField[];
  created: string;
  updated: string;
  size: number;
  allowSubscriptions: boolean;
  licenseTagName: string | null;
  permission: ACNPackagePermission;
  landscapeId: string;
  owner: string | null;
  ownerLandscape: string | null;
  sharedBy: string | null;
  sharedByLandscape: string | null;
  isFree: boolean | null;
  canBeRestricted: boolean | null;
  publisherName: string | null;
  purchaseUrl: string | null;
  globalItemId: string | null;
  provider: string | null;
  globalWaveItemId: string | null;
  importState: string | null;
  imported: boolean;
  importVersion: string | null;
  changedBy: string | null;
  tags: string[] | null;
  packageVersion: string;
  availablePackageVersions: string[];
}
export interface ACNPackage {
  parentId: string;
  childrenInfo: ACNChildInfo[];
}
export interface ACNPackageList {
  itemList: ACNPackage[];
}
export interface IBdcOffboardingStatusEndpoint {
  acnDeleteJobStatusEndpoint: string;
}

export type ACNRequestClient = "DWC" | "SAC";
/// /////////////////////////////
export interface IBdcOnboardingStatusEndpoint {
  importStatusEndpoint: string;
  dataReplicationStatusEndpoint: string;
}
