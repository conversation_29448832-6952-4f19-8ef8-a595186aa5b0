/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { extendSacHeaders, getSACUrl } from "@sap/deepsea-sac";
import {
  HttpStatusCode,
  IErrorDescription,
  IJsonObject,
  IRequestContext,
  IStepDetail,
  StatusType,
} from "@sap/deepsea-types";
import { BDC_ACN_IMPORT_USER } from "@sap/deepsea-utils";
import { AuthenticationMode, HttpMethod, Request, ResolvedResponse, httpClient } from "@sap/dwc-http-client";
import { Response } from "express";
import { Headers } from "node-fetch";
import {
  ACNFolder,
  ApplicationNamespace,
  BDCPackageInstallationProperties,
  BDCRequestContext,
  PackageFolder,
  SystemInfo,
} from "../../../shared/bdccockpit/Types";
import { getSystemInfoUrl } from "../../../shared/bdccockpit/utils/SystemInfoUtil";
import { isSameMajorAndGTE } from "../../../shared/bdccockpit/utils/semverUtils";
import { getTenantIdInfo } from "../../api/user";
import { isCanary } from "../../lib/node";
import { ExternalCallCategory } from "../../repository/security/common/externalCallTypes";
import { RequestContext } from "../../repository/security/requestContext";
import { ListRoles } from "../../routes/sac/types";
import { deleteCustomRole, getListRoles, getScopeAssignment } from "../../routes/space/user";
import { getFullRoleId } from "../../routes/space/util";
import { BDCPackageInstallation } from "../packageInstallation/packageInstallationTypes";
import {
  dspApplicationNamespace,
  dspProvider,
  sacApplicationNamespace,
  sacProvider,
} from "../systems/applicationNamespaceUtils";
import { getBdcLogger } from "../utils/logUtils";
import { AcnImportPollingHttpRestClient } from "./onboarding/acnImportPollingHttpResClient";
import {
  ACNChildInfo,
  ACNPackageField,
  ACNPackageId,
  ACNPackageList,
  ACNRequestClient,
  AcnImportPollingRequestStatus,
  BdcOnBoardingStep,
  IAcnImportBody,
  IAcnImportOutput,
  IAcnPackageIdentifierInfo,
  IBdcOnboardingAcnProgressOutput,
  IBdcOnboardingPayload,
  IGenericAsyncResponse,
  IHttpRestClient,
  IPollingHttpCallbackOptions,
  PackageIdWaveIdPair,
} from "./types";

const { logInfo, logError, logWarning } = getBdcLogger(__filename);
const CALL_TIMEOUT = 1 * 60 * 1000;
const RETRY_AFTER_SECONDS = 5;
const POLL_STATUS_SECONDS = 90 * 60;

// BDC predefined roles
const BDC_SCOPE_SPACE_ADMIN_ROLE = "BDC_Scope_Space_Admin";
const BDC_SCOPE_CONSUMER_ROLE = "BDC_Scope_Consumer";

/**
 * ACN category for BDC onboarding. This category is used to filter the content for BDC onboarding.
 */
//  ACN category for Bdc onboarding
export const ACN_CATEGORY_FOR_BDC_ONBOARDING = "businessDataCloud";
export const ACN_CATEGORY_FOR_BDC_OFFBOARDING = ACN_CATEGORY_FOR_BDC_ONBOARDING;

/**
 * hard-coded user ID for all ACN calls made in BDC context(create import job, getting job status...)
 */
const BDC_ONBOARDING_ACN_USER = BDC_ACN_IMPORT_USER;

/**
 * Retrieves the detail messages for import objects.
 * @param importObjects - An array of import objects.
 * @returns An object containing the detail messages for each import object.
 */
function getDetailMsgs(importObjects: IJsonObject[] | undefined): { [prop: string]: string } {
  const detailMsgs: { [prop: string]: string } = {};
  importObjects &&
    importObjects.forEach((obj) => {
      const stateInfo = obj.state as IJsonObject;
      if (stateInfo && stateInfo.status !== AcnImportPollingRequestStatus.Done) {
        const tempDetailMsg = stateInfo.message
          ? stateInfo.message
          : stateInfo.status === AcnImportPollingRequestStatus.Failed
          ? "unknown errors"
          : "";
        detailMsgs[obj.objectName as string] = `${stateInfo.status} ${tempDetailMsg}`;
      }
    });
  return detailMsgs;
}

/**
 * Retrieves the space names from the import objects.
 * @param importObjects - An array of import objects.
 * @returns An array of space names.
 */
function getSpaceNames(importObjects: IJsonObject[] | undefined): string[] | undefined {
  const spaceNames: string[] = [];
  importObjects?.forEach((obj) => {
    let tempSpacename: string;
    if (obj.objectType === "FOLDER") {
      tempSpacename = obj.objectName as string;
    } else {
      // for pactch packages, there will be no space objects information
      const tempPathId = obj.objectId as string;
      tempSpacename = tempPathId.substring(tempPathId.indexOf(":") + 1, tempPathId.indexOf("\\"));
    }
    if (tempSpacename && !spaceNames.includes(tempSpacename)) {
      spaceNames.push(tempSpacename);
    }
  });
  return spaceNames;
}

/**
 * Processes the response for creating an import job.
 * @param context - The request context.
 * @param response - The response from the import job.
 * @param requestBody - The request body for the import job.
 * @param bdcOnboardingProgressName - The name of the BDC onboarding progress.
 * @returns The progress output for the import job.
 */
async function importResponseProcessor(
  context: IRequestContext,
  response: ResolvedResponse,
  requestBody: IAcnImportBody,
  bdcOnboardingProgressName?: string
): Promise<IBdcOnboardingAcnProgressOutput> {
  const importOutput = response && (response.body as IAcnImportOutput);
  if (isCanary()) {
    logInfo(
      `[importResponseProcessor] Response body for creating import job of BDC onboarding: ${JSON.stringify(
        importOutput
      )}`,
      {
        context,
      }
    );
  }
  const progressOutput = {
    jobId: importOutput.jobId,
  } as IBdcOnboardingAcnProgressOutput;
  if (importOutput) {
    let upsertSpaceNames;
    if (
      importOutput.state.status.toLowerCase() === (AcnImportPollingRequestStatus.Done as string).toLowerCase() ||
      importOutput.state.status.toLowerCase() === (AcnImportPollingRequestStatus.Warning as string).toLowerCase()
    ) {
      upsertSpaceNames = getSpaceNames(importOutput.objects);
      if (!upsertSpaceNames || upsertSpaceNames.length === 0) {
        importOutput.state.status = "FAILED";
      }
    }

    const importRes = {} as IStepDetail;
    importRes.name = bdcOnboardingProgressName || "";
    importRes.startDate = importOutput.started;
    importRes.endDate = importOutput.finished;
    importRes.status = getStatusType(importOutput.state.status);
    const detailMsgObg = getDetailMsgs(importOutput.objects);
    detailMsgObg.message = importOutput.state.message
      ? importOutput.state.message
      : [StatusType.Succeed, StatusType.Warning].includes(importRes.status) &&
        (!upsertSpaceNames || !upsertSpaceNames.length)
      ? "Failed to find any space information from imported objects"
      : "There is no summary message";
    const importDetailMsg = JSON.stringify(detailMsgObg);
    if (importDetailMsg) {
      importRes.details = {} as IErrorDescription;
      importRes.details.msg = importDetailMsg;
      // response.status of status check request doesn't mean the error message code, we will set it after acn supports the error code in response body
    }
    progressOutput.importExecuteResult = importRes;
    progressOutput.spaceNames = upsertSpaceNames;
  }
  return progressOutput;
}

/**
 * Processes the exception for creating an import job.
 * @param context - The request context.
 * @param err - The error from the import job.
 * @param requestBody - The request body for the import job.
 * @param bdcOnboardingProgressName - The name of the BDC onboarding progress.
 * @returns The progress output for the import job.
 */
async function importExceptionProcessor(
  context: IRequestContext,
  err: unknown,
  requestBody: IAcnImportBody,
  bdcOnboardingProgressName: string
): Promise<IBdcOnboardingAcnProgressOutput> {
  const progressOutput = {
    jobId: null,
  } as IBdcOnboardingAcnProgressOutput;
  if (isCanary()) {
    logInfo(
      `[importExceptionProcessor] Response body for creating import job of BDC onboarding: ${JSON.stringify(
        requestBody
      )}`,
      {
        context,
      }
    );
  }
  const error = err as { message: string; statusCode: number; prevResponseBody?: any };
  if (requestBody) {
    const importRes = {} as IStepDetail;
    importRes.name = bdcOnboardingProgressName;
    importRes.endDate = new Date();
    if ((err as any).code === HttpStatusCode.REQUEST_TIMEOUT) {
      importRes.status = error.prevResponseBody?.state?.status || StatusType.Aborted;
    } else {
      importRes.status = StatusType.Failed;
    }
    if (error && error.message) {
      importRes.details = {} as IErrorDescription;
      importRes.details.msg = error.message;
      importRes.details.code = error.statusCode;
    }
    progressOutput.importExecuteResult = importRes;
  }
  return progressOutput;
}

/**
 * Wraps the ACN import for tests.
 */
export class WrapAcnImportForTests {
  public static async launchImport(
    httpRestServiceClient: IHttpRestClient,
    tenantId: string | undefined,
    body: IAcnImportBody,
    callbackOptions: unknown
  ): Promise<ResolvedResponse> {
    // TODO after check with ACN, we need to go with service API for first version
    // Reason is that there should be an handshake between bdc cockpit and public API service using OAuth which is not in the scope of v1
    return (httpRestServiceClient as any).post(`v4/service/jobs?tenant=${tenantId}`, body, callbackOptions as any);
    // return (httpRestServiceClient as any).post(`api/v1/content/jobs`, body, callbackOptions as any);
  }

  /**
   * Handles the success callback for the ACN integration.
   *
   * @param context - The request context.
   * @param response - The resolved response.
   * @param requestBody - The ACN import request body.
   * @param bdcOnboardingProgressName - The name of the BDC onboarding progress.
   * @returns A promise that resolves to the BDC onboarding progress output.
   */
  public static getImportJobStatusSuccessCallback(
    context: IRequestContext,
    response: ResolvedResponse,
    requestBody: IAcnImportBody,
    bdcOnboardingProgressName?: string
  ): Promise<IBdcOnboardingAcnProgressOutput> {
    return importResponseProcessor(context, response, requestBody, bdcOnboardingProgressName);
  }

  public static createImportJobSuccessCallback(
    context: IRequestContext,
    response: ResolvedResponse,
    requestBody: IAcnImportBody,
    bdcOnboardingProgressName?: string
  ): Promise<IBdcOnboardingAcnProgressOutput> {
    return Promise.resolve({
      jobId: response.body,
    } as IBdcOnboardingAcnProgressOutput);
  }

  /**
   * Handles the failure callback for the ACN integration.
   *
   * @param context - The request context.
   * @param err - The error object.
   * @param requestBody - The request body.
   * @param bdcOnboardingProgressName - The name of the BDC onboarding progress.
   * @returns A promise that resolves to the BDC onboarding progress output.
   */
  public static failureCallback(
    context: IRequestContext,
    err: unknown,
    requestBody: IAcnImportBody,
    bdcOnboardingProgressName: string
  ): Promise<IBdcOnboardingAcnProgressOutput> {
    return importExceptionProcessor(context, err, requestBody, bdcOnboardingProgressName);
  }

  /**
   * Retrieves the import status for a specific job.
   * @param httpRestServiceClient - The HTTP REST service client.
   * @param tenantId - The ID of the tenant.
   * @param jobId - The ID of the job.
   * @param callbackOptions - The callback options.
   * @returns A promise that resolves to the import status response.
   */
  public static async getImportStatus(
    httpRestServiceClient: IHttpRestClient,
    tenantId: string | undefined,
    jobId: string,
    callbackOptions: unknown
  ): Promise<ResolvedResponse> {
    return (httpRestServiceClient as any).get(getImportStatusUrl(tenantId!, jobId), callbackOptions as any);
  }
}

function getImportProgressName(packageName: string): string {
  return `${BdcOnBoardingStep.S_IMPORT_PACKAGE} ${packageName}`;
}

/**
 * Requests ACN import for BDC onboarding.
 *
 * @param context - The request context.
 * @param packIdentifierInfo - The package identifier information.
 * @param option - Optional parameters for the request.
 * @returns A promise that resolves to the BDC onboarding progress output.
 */
export async function requestAcnImport(
  context: IRequestContext,
  params: IBdcOnboardingPayload,
  replicationFlowJobId?: string,
  option?: IJsonObject
): Promise<IBdcOnboardingAcnProgressOutput> {
  // call ACN endpoint to create import job
  const packIdentifierInfo: IAcnPackageIdentifierInfo = {
    name: "BDC onboarding package name " + Date.now(), // TODO confirm with ACN to check if this parameter is required
    publicPackage: params.acnPackageId!,
  };
  const bdcOnboardingProgressName = getImportProgressName(packIdentifierInfo.name);
  const isBdcMultiInstance = await context.isFeatureFlagActive("DWCO_BDC_REPOSITORY_MULTI_INSTANCE");

  // for import SAC data package serviceParameters is empty
  const serviceParameters =
    params.uclSystemTenantId && params.uclFormationId
      ? JSON.stringify({
          postImportOptions: ["DWC_DEPLOY_AFTER_IMPORT"],
          correlationId: context.correlationId!,
          customParameters: {
            BDCOnboarding: true,
            acnCategory: params.acnCategory ?? ACN_CATEGORY_FOR_BDC_ONBOARDING,
            uclSystemTenantId: params.uclSystemTenantId,
            uclFormationId: params.uclFormationId,
            uclSystemAlias:
              isBdcMultiInstance && params.uclSystemAlias && params.uclSystemBusinessName
                ? params.uclSystemAlias
                : undefined,
            uclSystemBusinessName:
              isBdcMultiInstance && params.uclSystemAlias && params.uclSystemBusinessName
                ? params.uclSystemBusinessName
                : undefined,
            replicationFlowJobId,
            importAnyway: params.importAnyway ?? true,
            // uclAssignmentId: params.uclAssignmentId, // TODO to be confirmed
          },
          ...(params.packageVersion && { packageVersion: params.packageVersion }),
        })
      : JSON.stringify({
          correlationId: context.correlationId!,
          ...(params.packageVersion && { packageVersion: params.packageVersion }),
        });

  const body: IAcnImportBody = {
    type: "IMPORT",
    allObjects: true,
    serviceParameters,
    xsParameters: JSON.stringify({
      updateExisting: true,
      importDataOnly: false,
      dropDependent: false,
      isNewCopy: false,
    }),
    user: BDC_ONBOARDING_ACN_USER,
  };
  if (packIdentifierInfo.publicPackage > 0) {
    body.publicPackage = packIdentifierInfo.publicPackage;
  } else if (packIdentifierInfo.privatePackage && packIdentifierInfo.privatePackage > 0) {
    body.privatePackage = packIdentifierInfo.privatePackage;
  } else {
    body.packageName = packIdentifierInfo.name;
  }
  const acnJobForBdcOnboardingHttpClient: AcnImportPollingHttpRestClient<IAcnImportBody> =
    new AcnImportPollingHttpRestClient<IAcnImportBody>(getSACUrl("contentmanager"), context, {
      postStatusCheck: true,
    });
  return await new Promise<IBdcOnboardingAcnProgressOutput>(async (resolve) => {
    const callbackOptions: IPollingHttpCallbackOptions<
      IAcnImportBody,
      Promise<IBdcOnboardingAcnProgressOutput>,
      IGenericAsyncResponse<AcnImportPollingRequestStatus>
    > = {
      success: resolve,
      error: resolve,
      beforeStatusCheck: (client: IHttpRestClient) => undefined,
      responseProcessor: (response: ResolvedResponse, requestBody: IAcnImportBody) =>
        WrapAcnImportForTests.createImportJobSuccessCallback(context, response, requestBody, bdcOnboardingProgressName),
      exceptionProcessor: (err: unknown, requestBody: IAcnImportBody) =>
        WrapAcnImportForTests.failureCallback(context, err, requestBody, bdcOnboardingProgressName),
      beforeSend: (postOptions: Readonly<Request>) => setRequiredRequestHeaders(postOptions, context),
    };
    if (isCanary()) {
      logInfo(
        `[requestAcnImport] Response payload to ACN for creating import job of BDC onboarding: ${JSON.stringify(body)}`,
        {
          context,
        }
      );
    }
    await WrapAcnImportForTests.launchImport(
      acnJobForBdcOnboardingHttpClient,
      context.userInfo.tenantId,
      body,
      callbackOptions
    );
  });
}

/**
 * Retrieves the import status for a given ACN job ID.
 * @param context - The request context.
 * @param jobId - The ACN job ID.
 * @param option - polling options.
 * @returns A promise that resolves to the import status.
 */
export async function getAcnImportStatus(
  context: IRequestContext,
  jobId: string,
  option?: { retryAfterSeconds?: number; maxPollingTimeSeconds?: number }
): Promise<IBdcOnboardingAcnProgressOutput> {
  const client = new AcnImportPollingHttpRestClient<IAcnImportBody>(getSACUrl("contentmanager"), context, {
    monitoringUriComputed: (response: ResolvedResponse) =>
      // `api/v1/content/jobs/${response.body}`,
      // TODO after check with ACN, we need to go with service API for first version
      // Reason is that there should be an handshake between bdc cockpit and public API service using OAuth which is not in the scope of v1
      `v4/service/jobs/${response.body}?brief=false&tenant=${context.userInfo.tenantId}`,
    retryAfterComputed: () => option?.retryAfterSeconds ?? RETRY_AFTER_SECONDS,
    maxPollingTime: option?.maxPollingTimeSeconds ?? POLL_STATUS_SECONDS,
  });
  return await new Promise<IBdcOnboardingAcnProgressOutput>(async (resolve) => {
    const callbackOptions: IPollingHttpCallbackOptions<
      IAcnImportBody,
      Promise<IBdcOnboardingAcnProgressOutput>,
      IGenericAsyncResponse<AcnImportPollingRequestStatus>
    > = {
      success: resolve,
      error: resolve,
      beforeStatusCheck: (client: IHttpRestClient) => undefined,
      responseProcessor: (response: ResolvedResponse, requestBody: IAcnImportBody) =>
        WrapAcnImportForTests.getImportJobStatusSuccessCallback(context, response, requestBody),
      exceptionProcessor: (err: unknown, requestBody: IAcnImportBody) =>
        WrapAcnImportForTests.failureCallback(context, err, requestBody, ""),
      beforeSend: (postOptions: Readonly<Request>) => setRequiredRequestHeaders(postOptions, context),
    };
    await client.checkProgress(jobId, callbackOptions as any);
  });
}

export function getImportStatusEndpoint(tenantId: string, jobId: string) {
  return getSACUrl("contentmanager") + getImportStatusUrl(tenantId, jobId);
}

function getImportStatusUrl(tenantId: string, jobId: string) {
  return `/v4/service/jobs/${jobId}?tenant=${tenantId}`;
}

/**
 * Sets the required request headers for a given postOptions and context.
 * @param postOptions - The post options for the request.
 * @param context - The request context.
 * @returns The modified request object with the required headers set.
 */
function setRequiredRequestHeaders(postOptions: Readonly<Request>, context: IRequestContext) {
  const request = {
    ...postOptions,
  };
  request.opts = request.opts ?? {};
  request.opts.headers = extendSacHeaders(context, new Headers());
  request.opts.headers.set("referer", context.httpRequest?.path);
  request.opts.headers.set("user", context?.userInfo?.userName ?? BDC_ONBOARDING_ACN_USER);
  request.opts.callCategory = ExternalCallCategory.ACN;
  return request;
}

export async function getStoryIds(
  context: IRequestContext,
  itemId: number,
  waveId: number,
  requestClient: ACNRequestClient,
  isPublic: boolean = true
): Promise<{ storyIds: Map<string, string>; storyParentIds: Map<string, { id: string; path: string }> }> {
  try {
    const itemDetails = await getItemDetails(context, itemId, waveId, requestClient, isPublic);
    return extractStoryResourceId(itemDetails);
  } catch (error) {
    logError(`Failed to get story ids, ${error}`, {
      context,
    });
    return { storyIds: new Map(), storyParentIds: new Map() };
  }
}

async function getItemDetails(
  context: IRequestContext,
  itemId: number,
  waveId: number,
  requestClient: ACNRequestClient,
  isPublic: boolean
): Promise<any> {
  const baseUrl = getSACUrl("contentmanager");
  const url = `${baseUrl}/v4/service/items/${itemId}/${waveId}?tenant=${context.tenantId}&user=${BDC_ONBOARDING_ACN_USER}&requestClient=${requestClient}&isPublic=${isPublic}`;
  try {
    const response = await httpClient.call({
      url,
      opts: {
        method: HttpMethod.GET,
        authentication: AuthenticationMode.Uaa,
        acceptedStatusCodes: [HttpStatusCode.OK],
        headers: {},
        requestContext: context,
        callCategory: ExternalCallCategory.ACN,
        callTimeout: CALL_TIMEOUT,
      },
    });

    if (response.status !== HttpStatusCode.OK) {
      throw new Error(`Response status code:${response.status}`);
    }
    return response.body;
  } catch (error) {
    logError(`Failed to get item details for url ${url}\n${error}`, {
      context,
    });
    throw error;
  }
}

function extractStoryResourceId(body: any): {
  storyIds: Map<string, string>;
  storyParentIds: Map<string, { id: string; path: string }>;
} {
  const resourceIds: Map<string, string> = new Map();
  const parentIds: Map<string, { id: string; path: string }> = new Map();
  if (!body || !body.waveContent || !Array.isArray(body.waveContent)) {
    throw Error("Wave content must not be empty and should be of type array");
  }
  Array.from(body.waveContent).forEach((wave) => {
    const waveAny = wave as any;
    if (waveAny.type === "STORY") {
      const sourceIdParts = waveAny.sourceId.split(":");
      const sourceIdSuffix = sourceIdParts.length > 0 ? sourceIdParts[sourceIdParts.length - 1] : waveAny.sourceId;
      resourceIds.set(waveAny.name, sourceIdSuffix);

      const parentAncestorPath = JSON.parse(waveAny.ancestorPath).join(" / ");
      const parentIdParts = waveAny.parentId.split(":");
      let resourcePath = "";
      if (parentAncestorPath.search("INSIGHT_APPS") > -1) {
        resourcePath = "insightapps/";
      } else if (parentAncestorPath.search("MY_FILES") > -1) {
        resourcePath = "myfiles/";
      }
      const parentIdSuffix = parentIdParts.length > 0 ? parentIdParts[parentIdParts.length - 1] : waveAny.sourceId;
      parentIds.set(parentAncestorPath, { id: parentIdSuffix, path: resourcePath + parentIdSuffix });
    }
  });

  return { storyIds: resourceIds, storyParentIds: parentIds };
}

/**
 * Retrieves ACN package list.
 * @param requestClient - "DWC" or "SAC"
 * @param context - The request context.
 * @returns A promise that resolves to the ACNPackageList.
 */
export async function getPackageList(
  requestClient: ACNRequestClient,
  context: IRequestContext,
  isPublic: boolean
): Promise<ACNPackageList> {
  const baseUrl = getSACUrl("contentmanager");
  const url = `${baseUrl}/v4/service/items/0?globalItemId=businessDataCloud&isPublic=${isPublic}&tenant=${context.tenantId}&user=${BDC_ONBOARDING_ACN_USER}&requestClient=${requestClient}`;

  try {
    const response = await httpClient.call({
      url,
      opts: {
        method: HttpMethod.GET,
        authentication: AuthenticationMode.Uaa,
        acceptedStatusCodes: [HttpStatusCode.OK],
        headers: {},
        requestContext: context,
        callCategory: ExternalCallCategory.ACN,
        callTimeout: CALL_TIMEOUT,
      },
    });

    if (response.status !== HttpStatusCode.OK) {
      throw new Error(`Response status code:${response.status}`);
    }
    return response.body as ACNPackageList;
  } catch (error) {
    logError(`[acnIntegration] BDC Cockpit acnIntegration getPackageList, for url ${url}\n${error}`, {
      context,
    });
    return { itemList: [] };
  }
}

/**
 * return package id by package name and version or -1 if not found.
 * @param name - The package name to find
 * @param version - The package version to find
 * @param context - The request context.
 * @returns A promise that resolves to the ACNPackageId or -1.
 */
export async function getPackageId(
  name: string,
  version: string,
  requestClient: ACNRequestClient,
  context: IRequestContext
): Promise<ACNPackageId> {
  const packagesList: ACNPackageList = await getPackageList(requestClient, context, true);
  return getPackageIdFromPackageList(packagesList, name, version, context);
}

/**
 * return package id by package name and version or -1 if not found.
 * @param packagesList - ACN package list
 * @param name - The package name to find
 * @param version - The package version to find
 * @param context - The request context.
 * @returns A promise that resolves to the ACNPackageId or -1.
 */
export function getPackageIdFromPackageList(
  packagesList: ACNPackageList,
  name: string,
  version: string,
  context: IRequestContext
): ACNPackageId {
  let packageId = -1;

  const acnChild = findAcnChildInfoByNameAndVersion(packagesList, name, version);
  if (acnChild) {
    packageId = acnChild.itemId;
  }
  return packageId;
}

function getPackageIdWaveIdPair(
  packagesList: ACNPackageList,
  name: string,
  version: string,
  context: IRequestContext
): PackageIdWaveIdPair | null {
  const acnPackage = findAcnChildInfoByNameAndVersion(packagesList, name, version);
  return acnPackage
    ? {
        packageId: acnPackage.itemId,
        packageWaveId: acnPackage.waveItemId,
      }
    : null;
}

export function findAcnChildInfoByNameAndVersion(
  packagesList: ACNPackageList,
  name: string,
  version: string
): ACNChildInfo | null {
  let returnAcnChild: ACNChildInfo | null = null;
  for (const acnPackage of packagesList.itemList) {
    for (const acnChild of acnPackage.childrenInfo) {
      if (compareChildNameAndVersion(acnChild, name, version)) {
        returnAcnChild = acnChild;
        break;
      }
    }
    if (returnAcnChild !== null) {
      break;
    }
  }
  return returnAcnChild;
}

function compareChildNameAndVersion(acnChildInfo: ACNChildInfo, name: string, version: string): boolean {
  const packageName = getPackageName(acnChildInfo.fields);
  return (
    packageName &&
    packageName === name &&
    isSameMajorAndGTE(acnChildInfo.packageVersion, version) &&
    acnChildInfo.availablePackageVersions.includes(version)
  );
}

function getPackageName(fields: ACNPackageField[]): any {
  for (const packageField of fields) {
    if (packageField.fieldType === "name") {
      return packageField.fieldI18n?.[0]?.value;
    }
  }
  return "";
}

export function getStatusType(importJobStatus: string): StatusType {
  let retStatus: StatusType;
  switch (importJobStatus.toLocaleLowerCase()) {
    case "done":
      retStatus = StatusType.Succeed;
      break;
    case "warning":
      retStatus = StatusType.Warning;
      break;
    case "aborted":
      retStatus = StatusType.Aborted;
      break;
    case "failed":
      retStatus = StatusType.Failed;
      break;
    case "pending":
      retStatus = StatusType.Pending;
      break;
    case "executing":
      retStatus = StatusType.Executing;
      break;
    default:
      throw new Error(`Unknown status ${importJobStatus}`);
  }
  return retStatus;
}

export async function getDataPackagesFolders(
  context: IRequestContext,
  bdcPackageInstallation: BDCPackageInstallation,
  systemLandscape: SystemInfo[],
  acnRequestClients: ACNRequestClient[] | undefined
): Promise<ACNFolder[]> {
  const outputFolders: ACNFolder[] = [];
  let dwcPackageList: ACNPackageList | null = null;
  let sacPackageList: ACNPackageList | null = null;
  const dspContext = buildContext(
    context,
    systemLandscape,
    dspApplicationNamespace,
    bdcPackageInstallation.installationProperties
  );
  const sacContext = buildContext(
    context,
    systemLandscape,
    sacApplicationNamespace,
    bdcPackageInstallation.installationProperties
  );

  if (!acnRequestClients) {
    acnRequestClients = ["DWC", "SAC"];
  }
  for (const component of bdcPackageInstallation.originalPackage.components) {
    if (component.category === "CnPackage") {
      let packageList: ACNPackageList | null = null;
      let componentContext: IRequestContext | null = null;
      let requestClient: ACNRequestClient;
      const outputFolder: ACNFolder = {
        packageName: component.name,
        packageVersion: component.version,
        packageFolders: [],
      };

      if (component.provider === dspProvider && dspContext && acnRequestClients.includes("DWC")) {
        componentContext = dspContext;
        requestClient = "DWC";
        if (!dwcPackageList) {
          dwcPackageList = await getPackageList(requestClient, componentContext, true);
        }
        packageList = dwcPackageList;
      } else if (component.provider === sacProvider && sacContext && acnRequestClients.includes("SAC")) {
        componentContext = sacContext;
        requestClient = "SAC";
        if (!sacPackageList) {
          sacPackageList = await getPackageList(requestClient, componentContext, true);
        }
        packageList = sacPackageList;
      } else {
        continue;
      }

      if (componentContext && packageList) {
        const packageIdWaveIdPair = getPackageIdWaveIdPair(
          packageList,
          component.name,
          component.version,
          componentContext
        );
        if (packageIdWaveIdPair) {
          try {
            const itemDetails = await getItemDetails(
              componentContext,
              packageIdWaveIdPair.packageId,
              packageIdWaveIdPair.packageWaveId,
              requestClient,
              true
            );
            outputFolder.packageFolders = extractFoldersFromItemDetails(
              itemDetails,
              systemLandscape,
              component.provider,
              bdcPackageInstallation
            );
          } catch (error) {
            logError(
              `Failed to get item details packageId:${packageIdWaveIdPair.packageId}, packageWaveId: ${packageIdWaveIdPair.packageWaveId}\n${error}`,
              {
                context,
              }
            );
          }
        }
      }
      outputFolders.push(outputFolder);
    }
  }
  return outputFolders;
}

function extractFoldersFromItemDetails(
  itemDetails: any,
  systemLandscape: SystemInfo[],
  applicationNamespace: ApplicationNamespace,
  bdcPackageInstallation: BDCPackageInstallation
): PackageFolder[] {
  let folderBaseUrl: string | undefined;
  if (applicationNamespace === dspApplicationNamespace) {
    let dspSystemInfo: SystemInfo | undefined;
    const installationPropertyPerAppNamespace = bdcPackageInstallation.installationProperties.find(
      (ip) => ip.applicationNamespace === dspApplicationNamespace
    );
    dspSystemInfo = systemLandscape?.find(
      (sl) => sl.applicationTenantId === installationPropertyPerAppNamespace?.systemTenant
    );

    if (dspSystemInfo) {
      folderBaseUrl = getSystemInfoUrl(dspSystemInfo.propertyTags!);
    }
  }
  const outputFolders: PackageFolder[] = [];
  for (const waveContentItem of itemDetails.waveContent) {
    if (waveContentItem.type === "FOLDER") {
      let fullUrl: string | undefined;
      if (folderBaseUrl && applicationNamespace === dspApplicationNamespace) {
        fullUrl = `${folderBaseUrl}/dwaas-core/index.html#/managespaces&/ms/details/${waveContentItem.name}`;
      }
      outputFolders.push({
        name: waveContentItem.name,
        sourceId: waveContentItem.sourceId,
        visible: true,
        url: fullUrl === undefined ? "" : fullUrl,
      });
    }
  }
  return outputFolders;
}

function buildContext(
  context: BDCRequestContext,
  systemLandscape: SystemInfo[],
  applicationNamespace: ApplicationNamespace,
  installationProperties: BDCPackageInstallationProperties[]
): IRequestContext | null {
  let newContext: IRequestContext | null = null;
  let tenantId: string | null = null;
  const installationPropertyPerAppNamespace = installationProperties.find(
    (ip) => ip.applicationNamespace === applicationNamespace
  );
  if (!installationProperties) {
    logError(
      `Can't find installation property per application namespace: ${applicationNamespace}, in  installation properties: ${JSON.stringify(
        installationProperties
      )}`,
      { context }
    );
  }
  for (const systemInfo of systemLandscape) {
    if (systemInfo.applicationTenantId === installationPropertyPerAppNamespace?.systemTenant) {
      tenantId = systemInfo.applicationTenantId;
      break;
    }
  }

  if (tenantId) {
    newContext = RequestContext.createFromTenantId(tenantId, {
      spanContext: context.spanContext,
      preventCustomerHanaAutoUpgrade: true,
    });
    newContext.correlationId = context.correlationId;
  }
  return newContext;
}

/**
 * Drop the BDC scope roles after off boarding if both have no associated scope.
 * @param context
 * @param res
 * @returns A value indicates the result of scope roles dropping. Only for testing.
 */
export async function dropBDCScopeRoles(context: IRequestContext, res?: Response): Promise<boolean | undefined> {
  const roles: string[] = [BDC_SCOPE_SPACE_ADMIN_ROLE, BDC_SCOPE_CONSUMER_ROLE];
  try {
    logInfo(`Start to dropping the scope roles ${roles} created during onboarding.`, { context });
    const bdcOffBoardingFF = await context.isFeatureFlagActive("DWCO_BDC_GA");
    const sacTenant = await getTenantIdInfo(context);
    const roleIds = await getRoleFullIds(context, roles, sacTenant);

    if (bdcOffBoardingFF && roleIds && (await isEachScopeRoleEmpty(context, roleIds))) {
      for (const role of roleIds) {
        logInfo(`Start dropping scope role ${role}.`, { context });
        await deleteCustomRole(context, role);
        logInfo(`Scope role ${role} successfully deleted.`, { context });
      }
      // Not really need the return value, just for testing
      return true;
    }
  } catch (err) {
    // Not block off boarding if drop failed
    logWarning([`Ignoring error trying to delete scope roles: ${roles}. Cause:`, err], { context });
  }
}

/**
 * Check the two scope roles assigned during onboarding are empty
 * @param context
 * @param roleIds the id list of roles
 * @returns A value indicates if the role to check has associated scope.
 */
export async function isEachScopeRoleEmpty(
  context: IRequestContext,
  roleIds: string[] | undefined
): Promise<boolean | undefined> {
  try {
    if (roleIds) {
      for (const roleId of roleIds) {
        const scopes: string[] = await getScopeAssignment(context, roleId);
        if (scopes?.length) {
          logInfo(`Role ${roleId} still have ${scopes.length} scopes.`, { context });
          return false;
        }
      }
      logInfo(`All the roles ${roleIds} to check don't have any scope.`, { context });
      return true;
    }
  } catch (err) {
    logError([`Get scopes for roles :${roleIds} failed:`, err], { context });
  }
}

/**
 * @param context
 * @param roleName The role name.
 * @param sacTenant sac tenant id.
 * @returns The full and validated role id, if role not exist return undefined and log it.
 */
export async function getRoleFullIds(
  context: IRequestContext,
  rolesName: string[],
  sacTenant: string
): Promise<string[] | undefined> {
  try {
    logInfo(`Start checking if scope role exist ${rolesName}.`, { context });
    const listRoles: ListRoles[] = await getListRoles(context, sacTenant);
    const roleIds: string[] = [];
    for (const roleName of rolesName) {
      const role = listRoles.find((i) => i.id.name === roleName);
      if (role) {
        const roleId = getFullRoleId(role.id);
        roleIds.push(roleId);
        logInfo(`Scope role exists ${roleName} and fullId is ${roleId}.`, { context });
      } else {
        logInfo(`Scope role ${roleName} not exist.`, { context });
        return undefined;
      }
    }
    logInfo(`Scope role all exist ${rolesName}.`, { context });
    return roleIds;
  } catch (err) {
    logError([`Check role ${rolesName} failed for tenant :${sacTenant}.:`, err], { context });
  }
}
