/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { IJobStatus, IRequestContext, StatusType } from "@sap/deepsea-types";
import { ParamValidator } from "@sap/deepsea-utils";
import { Request, Response } from "express";
import Status from "http-status-codes";
import { IFinalStatus } from "../../../replicationflow/monitoring/replicationflowStatusMonitor";
import { CodedError, IErrorResponseOptions, sendErrorResponse } from "../../../server/errorResponse";
import { SacComponentInstallationDetails } from "../../packageInstallation/packageInstallationTypes";
import { getBdcLogger } from "../../utils/logUtils";
import { getAcnImportStatus, getImportStatusEndpoint, requestAcnImport } from "../acnIntegration";
import { checkMultiBdcInstanceEnabled } from "../bdcMultiInstance";
import {
  BdcOnboardingErrorCode,
  IBdcOnboardingAcnProgressOutput,
  IBdcOnboardingJobIds,
  IBdcOnboardingPayload,
  IBdcOnboardingProgressOutput,
  IBdcOnboardingStatusEndpoint,
} from "../types";
import { BdcOnboardingError } from "./bdcOnboardingError";
import {
  assertRequiredHeaders,
  assertTechnicalToken,
  createBdcOnboardingJob,
  getReplicationFlowJobStatus,
  getReplicationFlowStatusEndpoint,
  isValidJobId,
} from "./utils";
const { logInfo, logError, logWarning } = getBdcLogger(__filename);

export class BdcOnboardingController {
  static async processOnboarding(req: Request, res: Response) {
    try {
      const jobIds: IBdcOnboardingJobIds = await BdcOnboardingController.doOnboarding(req, res);
      const { importJobId, replicationFlowJobId } = jobIds;
      if (isValidJobId(importJobId) && replicationFlowJobId) {
        const statusEndpoints: IBdcOnboardingStatusEndpoint = {
          importStatusEndpoint: getImportStatusEndpoint(req.context.tenantId!, importJobId!),
          dataReplicationStatusEndpoint: getReplicationFlowStatusEndpoint(req.context, replicationFlowJobId),
        };
        res.status(Status.ACCEPTED).send(statusEndpoints);
      } else {
        throw new CodedError(
          "processOnboarding",
          `failed to create job for BDC content onboarding`,
          Status.INTERNAL_SERVER_ERROR
        );
      }
    } catch (err) {
      let options: IErrorResponseOptions = {};
      if (err instanceof BdcOnboardingError) {
        if (err.code === BdcOnboardingErrorCode.IS_NOT_TECHNICAL_TOKEN) {
          options.status = Status.FORBIDDEN;
        } else if (err.code === BdcOnboardingErrorCode.MISSING_TENANT_ID) {
          options.status = Status.NOT_FOUND;
        } else {
          options.status = Status.BAD_REQUEST;
        }
      } else {
        logError(["[onboarding] Internal server error", err], { context: req.context });
      }
      options.err = err;

      sendErrorResponse(req.context, "processOnboarding", options);
    }
  }

  /**
   * Performs the BDC onboarding process.
   * This method is used for testing purposes only.
   * Because BDC Cockpit won't call the onboarding endpoint directly, but QA needs to test the onboarding process by this endpoint.
   *
   * @param req - The Express request object.
   * @param res - The Express response object.
   * @returns A Promise that resolves to an object containing the import job ID and replication flow job ID.
   */
  static async doOnboarding(req: Request, res: Response): Promise<IBdcOnboardingJobIds> {
    await assertTechnicalToken(req, res);
    assertRequiredHeaders(req);

    return await BdcOnboardingController.onboarding(req.body, req.context);
  }

  /**
   * Performs BDC onboarding.
   *
   * @param requestBody - The request body containing the necessary data for onboarding.
   * @param context - The request context.
   * @returns A promise that resolves to an object containing the import job ID and replication flow job ID.
   */
  static async onboarding(requestBody: IBdcOnboardingPayload, context: IRequestContext): Promise<IBdcOnboardingJobIds> {
    logInfo("[onboarding] Start to BDC onboarding", { context });

    const isBdcMultiInstanceEnabled = await checkMultiBdcInstanceEnabled(context, {
      uclSystemAlias: requestBody?.uclSystemAlias,
      uclSystemBusinessName: requestBody?.uclSystemBusinessName,
    });

    const bdcOnboardingParam: IBdcOnboardingPayload = BdcOnboardingController.getBdcOnboardingPayload(
      requestBody,
      context,
      isBdcMultiInstanceEnabled
    );
    // create DWC job
    const dwcJob: IJobStatus = await createBdcOnboardingJob(context, {
      operation: "initialLoadForReplicationFlow",
    });
    // create ACN import job
    const acnJob = await BdcOnboardingController.createImportJob(context, bdcOnboardingParam, dwcJob.id);
    return {
      importJobId: acnJob.jobId!,
      replicationFlowJobId: dwcJob.id,
    };
  }

  /**
   * Creates an ACN import job for BDC onboarding.
   * @param {IRequestContext} context - The request context.
   * @param {IBdcOnboardingPayload} bdcOnboardingParam - The BDC onboarding payload.
   * @returns {Promise<string>} - The job ID of the import job.
   * @throws {CodedError} - If the ACN import job fails to launch or return a job ID.
   */
  static async createImportJob(
    context: IRequestContext,
    bdcOnboardingParam: IBdcOnboardingPayload,
    replicationFlowJobId?: string
  ): Promise<IBdcOnboardingAcnProgressOutput> {
    // create ACN import job
    const result: IBdcOnboardingAcnProgressOutput = await requestAcnImport(
      context,
      bdcOnboardingParam,
      replicationFlowJobId
    );

    if (result) {
      logInfo(
        `ACN import step finished with ACN job Id ${result.jobId} and space names: ${
          result.spaceNames ? result.spaceNames.join(", ") : ""
        }`,
        { context }
      );
      if (!result.jobId || result?.importExecuteResult?.status === StatusType.Failed) {
        logError(`The ACN import step failed to return a job id`, { context });
        throw new CodedError(
          "failedLaunchAcnImportJob",
          `Failed to launch import job from ACN: ${JSON.stringify(result?.importExecuteResult?.details)}`,
          Status.INTERNAL_SERVER_ERROR
        );
      }
    } else {
      logError(`Failed to get response after call ACN import job`, { context });
      throw new CodedError(
        "failedGetReplyFromAcnImportJob",
        "Failed to get response after call ACN import job",
        Status.INTERNAL_SERVER_ERROR
      );
    }
    return result;
  }

  /**
   * Retrieves the import job status for a given job ID.
   * @param jobIds - The IDs of the import job and the replication flow job.
   * @param context - The request context.
   * @returns A promise that resolves to an object containing the import status and data replication status.
   * The following is an example of the response object:
   * {
      "importJobStatus": {
        jobId: 135553,
        importExecuteResult: {
          name: "",
          startDate: "2024-08-12T06:10:50.154Z",
          endDate: "2024-08-12T06:11:41.142Z",
          status: "Succeed",
          details: {
            msg: "{\"message\":\"There is no summary message\"}",
            code: 200,
          },
        },
        spaceNames: [
          "BDC_CONTENT_TEST",
        ],
      },
      "replicationFlowJobStatus": {
        "id": "5ff0d17f163041f09d3137f9e1fff67a",
        "startDate": null,
        "endDate": null,
        "jobStatus": "Executing",
        "executionDetails": {
          "global": {
            "correlationId": "bbab5b5d-2294-4gd9-6671-cb3ef86fb043",
            "request": {
              "operation": "initialLoadForReplicationFlow",
              "path": "/bdc/onboarding"
            },
            "status": "Executing",
            "startDate": "2024-08-09T03:27:40.478Z"
          }
        },
        "name": ""
      }
    }
   * @throws If there is an error retrieving the import job status.
   */
  static async getOnboardingJobsStatus(
    jobIds: IBdcOnboardingJobIds,
    context: IRequestContext
  ): Promise<IBdcOnboardingProgressOutput> {
    const { importJobId, replicationFlowJobId } = jobIds;
    let acnJobStatus: IBdcOnboardingAcnProgressOutput | undefined;
    let replicationFlowJobStatus:
      | { replicationFlowJobStatus: StatusType; replicationFlowDetails: IFinalStatus }
      | undefined;
    // call ACN endpoint to get import job status
    try {
      if (isValidJobId(importJobId)) {
        acnJobStatus = await getAcnImportStatus(context, jobIds.importJobId, {
          maxPollingTimeSeconds: 20,
        });

        logInfo(
          `success try to  get acn import job status, import job id: ${importJobId}, acn job status: ${JSON.stringify(
            acnJobStatus
          )}`,
          {
            context,
          }
        );
        const logLevelAcnJobStatus = BdcOnboardingController.getLogLevelForStatus(
          acnJobStatus?.importExecuteResult?.status
        );
        logLevelAcnJobStatus(`ACN import status (after processing): ${acnJobStatus?.importExecuteResult?.status}`, {
          context,
        });
      }
    } catch (err) {
      logError([`Failed to get acn import job status for job id: ${importJobId}, error: ${err?.message}`, err], {
        context,
      });
      throw new Error(`Failed to get acn import job status for job id: ${importJobId}, error: ${err?.message}`);
    }
    try {
      replicationFlowJobStatus = {
        replicationFlowJobStatus: StatusType.Pending,
        replicationFlowDetails: {
          overallStatus: "",
          details: [],
        },
      };
      if (
        acnJobStatus?.importExecuteResult?.status === StatusType.Succeed ||
        acnJobStatus?.importExecuteResult?.status === StatusType.Warning
      ) {
        replicationFlowJobStatus = await getReplicationFlowJobStatus(context, replicationFlowJobId);
      }

      const logLevelReplicationFlowJobStatus = BdcOnboardingController.getLogLevelForStatus(
        replicationFlowJobStatus.replicationFlowJobStatus
      );
      logLevelReplicationFlowJobStatus(
        `Replication flow status (after processing): ${replicationFlowJobStatus.replicationFlowJobStatus}`,
        {
          context,
        }
      );
    } catch (err) {
      logWarning(
        [`Failed to get replication flow job status for job id: ${replicationFlowJobId}, error: ${err?.message}`, err],
        { context }
      );
      // do not throw error here, because the import job may be finished successfully
    }
    return {
      importJobStatus: acnJobStatus?.importExecuteResult?.status,
      importJobMessage: acnJobStatus?.importExecuteResult?.details?.msg,
      replicationFlowJobStatus: replicationFlowJobStatus?.replicationFlowJobStatus,
      replicationFlowJobMessage: replicationFlowJobStatus?.replicationFlowDetails?.message,
    };
  }

  /**
   * Example of the request payload:
   * {
      "acnPackageId": 123,
      "uclSystemTenantId: "cbdd8a33-125f-48a8-b8d9-deebd0b3c168",
      "acnCategory": "UCL4TEST"
    }
    acnPackageId: the unique identifier of the ACN package to import
    uclSystemTenantId: the identifier of the UCL instance where to retrieve the data from. This UCL instance must be part of the formation.
    acnCategory (optional): in order to be able to test onboarding without publishing content for all customers, we may use a test category where test package may be located.
    When this parameter is not specified, the onboarding takes the ACN package from the public category by default.
   * @param req
   * @returns
   */
  static getBdcOnboardingPayload(
    requestBody: IBdcOnboardingPayload,
    context: IRequestContext,
    isBdcMultiInstanceEnabled?: boolean
  ): IBdcOnboardingPayload {
    logInfo(
      `[onboarding] Request payload for BDC onboarding: package id: ${requestBody.acnPackageId}, source tenant id: ${context.tenantId}`,
      {
        context,
      }
    );
    const params: IBdcOnboardingPayload = {
      acnPackageId: ParamValidator.number(context, requestBody?.acnPackageId),
      uclSystemTenantId: ParamValidator.string(context, requestBody?.uclSystemTenantId),
      acnCategory: ParamValidator.string(context, requestBody?.acnCategory),
      uclFormationId: ParamValidator.string(context, requestBody?.uclFormationId),
      // uclAssignmentId: ParamValidator.string(context, requestBody?.uclAssignmentId), // TODO to be confirmed
      packageVersion: ParamValidator.string(context, requestBody?.packageVersion),
    };
    if (isBdcMultiInstanceEnabled) {
      params.uclSystemAlias = requestBody?.uclSystemAlias;
      params.uclSystemBusinessName = requestBody?.uclSystemBusinessName;
    }
    if (!params.acnPackageId || !params.uclSystemTenantId || !params.uclFormationId) {
      logError("[onboardingBdc] Missing required request parameters", { context });
      throw new BdcOnboardingError(
        "Missing required request parameters",
        BdcOnboardingErrorCode.MISSING_REQUIRED_PARAMETERS
      );
    }
    return params;
  }

  /**
   * Performs BDC onboarding.
   *
   * @param requestBody - The request body containing the necessary data for onboarding.
   * @param context - The request context.
   * @returns A promise that resolves to an object containing the import job ID and replication flow job ID.
   */
  static async onboardingSAC(
    requestBody: IBdcOnboardingPayload,
    context: IRequestContext
  ): Promise<SacComponentInstallationDetails> {
    logInfo("[onboarding] Start to BDC onboarding", { context });

    const bdcOnboardingParam: IBdcOnboardingPayload = {
      acnPackageId: ParamValidator.number(context, requestBody?.acnPackageId),
      ...(requestBody.packageVersion && { packageVersion: requestBody.packageVersion }),
    };

    if (!bdcOnboardingParam.acnPackageId) {
      logError("[onboardingBdc] Missing required request parameter: acnPackageId", { context });
      throw new BdcOnboardingError(
        "Missing required request parameters",
        BdcOnboardingErrorCode.MISSING_REQUIRED_PARAMETERS
      );
    }

    // create ACN import job
    const acnJob = await BdcOnboardingController.createImportJob(context, bdcOnboardingParam);
    return {
      importJobId: acnJob.jobId!,
    };
  }

  /**
   * Retrieves the import job status for a given job ID.
   * @param jobIds - The IDs of the import job.
   * @param context - The request context.
   * @returns A promise that resolves to an object containing the import status.
   **/
  static async getSACOnboardingJobsStatus(
    jobId: SacComponentInstallationDetails,
    context: IRequestContext
  ): Promise<IBdcOnboardingProgressOutput> {
    const importJobId = jobId.importJobId;
    let acnJobStatus: IBdcOnboardingAcnProgressOutput;
    // call ACN endpoint to get import job status
    try {
      if (!isValidJobId(importJobId)) {
        throw new Error(`Failed to get acn import job status, because there is no valid job id`);
      }

      acnJobStatus = await getAcnImportStatus(context, importJobId);
      return {
        importJobStatus: acnJobStatus?.importExecuteResult?.status,
        replicationFlowJobStatus: undefined,
      };
    } catch (err) {
      logError(`Failed to get acn import job status, import job id: ${importJobId}, ${err}`, { context });
      throw new Error(`Failed to get acn import job status, import job id: ${importJobId}`);
    }
  }

  private static getLogLevelForStatus(status: StatusType | undefined): typeof logInfo | typeof logError {
    return status === StatusType.Failed || status === StatusType.Aborted ? logError : logInfo;
  }
}
