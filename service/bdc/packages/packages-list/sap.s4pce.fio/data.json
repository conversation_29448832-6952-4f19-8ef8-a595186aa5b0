{"id": "sap.s4pce.fio_pkg", "version": "1.1.0", "name": "SAP Financial Operations Data Products (SAP S/4HANA Cloud Private Edition )", "description": "This Data Package contains Data Products for the SAP S/4HANA Cloud Private Edition area Financial Operations", "shortDescription": "Data Products for SAP S/4HANA Cloud Private Edition Financial Operations", "type": "DataPackage", "category": "finance", "deliveryStatus": "production", "applicationComponent": "BDC-DPI-S4", "requiredApplications": [{"provider": "sap.s4", "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}], "components": [{"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CashFlow:v1", "version": "1.0.0", "name": "CashFlow", "description": "Cash Flow", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2022"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4pce:dataProduct:CollectionsCodes:v1", "version": "1.0.0", "name": "CollectionsCodes", "description": "Collections Codes", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4pce:dataProduct:CollectionsCustomerContact:v1", "version": "1.0.0", "name": "CollectionsCustomerContact", "description": "Collections Customer Contact", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4pce:dataProduct:CollectionsResubmission:v1", "version": "1.0.0", "name": "CollectionsResubmission", "description": "Collections Resubmission", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4pce:dataProduct:CollectionsWorklistItem:v1", "version": "1.0.0", "name": "CollectionsWorklistItem", "description": "Collections Worklist Item", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4pce:dataProduct:DunningBlockingReason:v1", "version": "1.0.0", "name": "DunningBlockingReason", "description": "Dunning Blocking Reason", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:HouseBank:v1", "version": "1.0.0", "name": "HouseBank", "description": "House Bank", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:PaymentMethod:v1", "version": "1.0.0", "name": "PaymentMethod", "description": "Payment Method", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:PaymentTerms:v1", "version": "1.0.0", "name": "PaymentTerms", "description": "Payment Terms", "numberOfEntities": 4, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2022"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4pce:dataProduct:PromiseToPay:v1", "version": "1.0.0", "name": "PromiseToPay", "description": "Promise To Pay", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:SecuritiesAccount:v1", "version": "1.0.0", "name": "SecuritiesAccount", "description": "Securities Account", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:SecurityClass:v1", "version": "1.0.0", "name": "SecurityClass", "description": "Security Class", "numberOfEntities": 14, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:StatisticalKeyFigure:v1", "version": "1.0.0", "name": "StatisticalKeyFigure", "description": "Statistical KeyFigure", "numberOfEntities": 9, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}