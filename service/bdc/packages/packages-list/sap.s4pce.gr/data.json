{"id": "sap.s4pce.gr_pkg", "version": "1.1.0", "name": "SAP Group Reporting Data Products (SAP S/4HANA Cloud Private Edition)", "description": "This Data Package contains Data Products for the SAP S/4HANA Cloud Private Edition area Group Reporting", "shortDescription": "Data Products for SAP S/4HANA Cloud Private Edition Group Reporting", "type": "DataPackage", "category": "finance", "deliveryStatus": "production", "applicationComponent": "BDC-DPI-S4", "requiredApplications": [{"provider": "sap.s4", "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}], "components": [{"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnBillingDocumentType:v1", "version": "1.0.0", "name": "CnsldtnBillingDocumentType", "description": "Consolidation Billing Document Type", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnBusinessArea:v1", "version": "1.0.0", "name": "CnsldtnBusinessArea", "description": "Consolidation Business Area", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnControllingArea:v1", "version": "1.0.0", "name": "CnsldtnControllingArea", "description": "Consolidation Controlling Area", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnCostCenter:v1", "version": "1.0.0", "name": "CnsldtnCostCenter", "description": "Consolidation Cost Center", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnCountry:v1", "version": "1.0.0", "name": "CnsldtnCountry", "description": "Consolidation Country", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnCustomer:v1", "version": "1.0.0", "name": "CnsldtnCustomer", "description": "Consolidation Customer", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnCustomerGroup:v1", "version": "1.0.0", "name": "CnsldtnCustomerGroup", "description": "Consolidation Customer Group", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnDistributionChannel:v1", "version": "1.0.0", "name": "CnsldtnDistributionChannel", "description": "Consolidation Distribution Channel", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnDivision:v1", "version": "1.0.0", "name": "CnsldtnDivision", "description": "Consolidation Division", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnFinancialStatementItem:v1", "version": "1.0.0", "name": "CnsldtnFinancialStatementItem", "description": "Consolidation Financial Statement Item", "numberOfEntities": 7, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnFinTransactionType:v1", "version": "1.0.0", "name": "CnsldtnFinTransactionType", "description": "Consolidation Financial Transaction Type", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnFunctionalArea:v1", "version": "1.0.0", "name": "CnsldtnFunctionalArea", "description": "Consolidation Functional Area", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnGLAccount:v1", "version": "1.0.0", "name": "CnsldtnGLAccount", "description": "Consolidation General Led<PERSON> Account", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnGLChartOfAccounts:v1", "version": "1.0.0", "name": "CnsldtnGLChartOfAccounts", "description": "Consolidation General Ledger Chart Of Accounts", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnIndustry:v1", "version": "1.0.0", "name": "CnsldtnIndustry", "description": "Consolidation Industry", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnMaterial:v1", "version": "1.0.0", "name": "CnsldtnMaterial", "description": "Consolidation Material", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnMaterialGroup:v1", "version": "1.0.0", "name": "CnsldtnMaterialGroup", "description": "Consolidation Material Group", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnOrder:v1", "version": "1.0.0", "name": "CnsldtnOrder", "description": "Consolidation Order", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnPlant:v1", "version": "1.0.0", "name": "CnsldtnPlant", "description": "Consolidation Plant", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnProduct:v1", "version": "1.0.0", "name": "CnsldtnProduct", "description": "Consolidation Product", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnProductGroup:v1", "version": "1.0.0", "name": "CnsldtnProductGroup", "description": "Consolidation Product Group", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnProfitCenter:v1", "version": "1.0.0", "name": "CnsldtnProfitCenter", "description": "Consolidation Profit Center", "numberOfEntities": 10, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnSalesDistrict:v1", "version": "1.0.0", "name": "CnsldtnSalesDistrict", "description": "Consolidation Sales District", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnSalesOrganization:v1", "version": "1.0.0", "name": "CnsldtnSalesOrganization", "description": "Consolidation Sales Organization", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnSegment:v1", "version": "1.0.0", "name": "CnsldtnSegment", "description": "Consolidation Segment", "numberOfEntities": 10, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CnsldtnSupplier:v1", "version": "1.0.0", "name": "CnsldtnSupplier", "description": "Consolidation Supplier", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:ConsolidationChartOfAccounts:v1", "version": "1.0.0", "name": "ConsolidationChartOfAccounts", "description": "Consolidation Chart Of Accounts", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:ConsolidationDocumentType:v1", "version": "1.0.0", "name": "ConsolidationDocumentType", "description": "Consolidation Document Type", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:ConsolidationGroup:v1", "version": "1.0.0", "name": "ConsolidationGroup", "description": "Consolidation Group", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:ConsolidationGroupStructure:v1", "version": "1.0.0", "name": "ConsolidationGroupStructure", "description": "Consolidation Group Structure", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:ConsolidationSubitem:v1", "version": "1.0.1", "name": "ConsolidationSubitem", "description": "Consolidation Subitem", "numberOfEntities": 4, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:ConsolidationUnit:v1", "version": "1.0.0", "name": "ConsolidationUnit", "description": "Consolidation Unit", "numberOfEntities": 8, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:ConsolidationVersion:v1", "version": "1.0.0", "name": "ConsolidationVersion", "description": "Consolidation Version", "numberOfEntities": 3, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2023"}]}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}