{"id": "sap.s4pce.afc_pkg", "version": "1.1.0", "name": "SAP Accounting and Financial Close Data Products (SAP S/4HANA Cloud Private Edition)", "description": "This Data Package contains Data Products for the SAP S/4HANA Cloud Private Edition area Accounting and Financial Close", "shortDescription": "Data Products for SAP SAP S/4HANA Cloud Private Edition Accounting and Financial Close", "type": "DataPackage", "category": "finance", "deliveryStatus": "production", "applicationComponent": "BDC-DPI-S4", "requiredApplications": [{"provider": "sap.s4", "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}], "components": [{"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:BillableControl:v1", "version": "1.0.0", "name": "BillableControl", "description": "Billable Control", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:BusinessArea:v1", "version": "1.0.0", "name": "BusinessArea", "description": "Business Area", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:Company:v1", "version": "1.0.0", "name": "Company", "description": "Company", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CompanyCode:v1", "version": "1.0.0", "name": "CompanyCode", "description": "Company Code", "numberOfEntities": 9, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:ControllingArea:v1", "version": "1.0.0", "name": "ControllingArea", "description": "Controlling Area", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:ControllingObject:v1", "version": "1.0.0", "name": "ControllingObject", "description": "Controlling Object", "numberOfEntities": 3, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CostAnalysisResource:v1", "version": "1.0.0", "name": "CostAnalysisResource", "description": "Cost Analysis Resource", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CostCenter:v1", "version": "1.0.0", "name": "CostCenter", "description": "Cost Center", "numberOfEntities": 8, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CostCenterActivityType:v1", "version": "1.0.0", "name": "CostCenterActivityType", "description": "Cost Center Activity Type", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CostingValuationVariant:v1", "version": "1.0.0", "name": "CostingValuationVariant", "description": "Costing Valuation Variant", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CostOriginGroup:v1", "version": "1.0.0", "name": "CostOriginGroup", "description": "Cost Origin Group", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:EntryViewJournalEntry:v1", "version": "1.0.0", "name": "EntryViewJournalEntry", "description": "Entry View Journal Entry", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:FinancialPlanningEntryItem:v1", "version": "1.0.0", "name": "FinancialPlanningEntryItem", "description": "Financial Planning Entry Item", "numberOfEntities": 3, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:FiscalYear:v1", "version": "1.0.0", "name": "FiscalYear", "description": "Fiscal Year", "numberOfEntities": 3, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:FunctionalArea:v1", "version": "1.0.0", "name": "FunctionalArea", "description": "Functional Area", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:GeneralLedgerAccount:v1", "version": "1.1.0", "name": "GeneralLedgerAccount", "description": "General <PERSON><PERSON> Account", "numberOfEntities": 21, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:InternalOrder:v1", "version": "1.0.0", "name": "InternalOrder", "description": "Internal Order", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:JournalEntryCodes:v1", "version": "1.1.0", "name": "JournalEntryCodes", "description": "Journal Entry Codes", "numberOfEntities": 14, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:JournalEntryHeader:v1", "version": "1.0.0", "name": "JournalEntryHeader", "description": "Journal Entry Header", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:JournalEntryItemCodes:v1", "version": "1.0.0", "name": "JournalEntryItemCodes", "description": "Journal Entry Item Codes", "numberOfEntities": 14, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:Ledger:v1", "version": "1.0.0", "name": "Ledger", "description": "Ledger", "numberOfEntities": 4, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:OperatingConcern:v1", "version": "1.0.0", "name": "OperatingConcern", "description": "Operating Concern", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:ProfitCenter:v1", "version": "1.0.0", "name": "ProfitCenter", "description": "Profit Center", "numberOfEntities": 7, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:SalesTaxCode:v1", "version": "1.0.0", "name": "SalesTaxCode", "description": "Sales Tax Code", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:SalesTaxType:v1", "version": "1.0.0", "name": "SalesTaxType", "description": "Sales Tax Type", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:Segment:v1", "version": "1.0.0", "name": "Segment", "description": "Segment", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:TaxCalculationProcedure:v1", "version": "1.0.0", "name": "TaxCalculationProcedure", "description": "Tax Calculation Procedure", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:TaxJurisdiction:v1", "version": "1.0.0", "name": "TaxJurisdiction", "description": "Tax Jurisdiction", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:US_TaxSourcing:v1", "version": "1.0.0", "name": "US_TaxSourcing", "description": "USA _ Tax Sourcing", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:US_TaxState:v1", "version": "1.0.0", "name": "US_TaxState", "description": "USA _ Tax State", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:WithholdingTaxCode:v1", "version": "1.0.0", "name": "WithholdingTaxCode", "description": "Withholding Tax Code", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap", "minVersion": "2021"}]}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}