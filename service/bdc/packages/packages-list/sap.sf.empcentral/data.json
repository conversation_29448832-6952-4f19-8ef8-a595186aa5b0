{"id": "sap.sf.empcentral_pkg", "version": "1.0.0", "name": "SAP SuccessFactors Employee Central Data Products", "description": "This Data Package contains Data Products for SAP SuccessFactors Employee Central", "shortDescription": "Data Products for SAP SuccessFactors Employee Central", "type": "DataPackage", "category": "humanResources", "deliveryStatus": "development", "applicationComponent": "TODO", "requiredApplications": [{"provider": "sap.sf", "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}]}], "components": [{"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.analytics:dataProduct:CoreWorkforceFact:v1", "version": "1.0.0", "name": "CoreWorkforceFact", "description": "Core Workforce Fact", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}], "requires": [{"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.foundationobjects:dataProduct:CommonConfigurationData:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.workforce:dataProduct:Compensation:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.foundationobjects:dataProduct:CompensationStructure:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.analytics:dataProduct:DataProductConfig:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.foundationobjects:dataProduct:EnterpriseStructure:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.foundationobjects:dataProduct:PayStructure:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.extensibility:dataProduct:Picklist:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.workforce:dataProduct:Position:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.workforce:dataProduct:WorkforcePerson:v1", "minVersion": "1.0.0"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.analytics:dataProduct:LocationHierarchy:v1", "version": "1.0.0", "name": "LocationHierarchy", "description": "Location Hierarchy", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}], "requires": [{"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.foundationobjects:dataProduct:EnterpriseStructure:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.extensibility:dataProduct:Picklist:v1", "minVersion": "1.0.0"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.analytics:dataProduct:MovementReason:v1", "version": "1.0.0", "name": "MovementReason", "description": "MovementReason", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}], "requires": [{"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.foundationobjects:dataProduct:CommonConfigurationData:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.analytics:dataProduct:DataProductConfig:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.extensibility:dataProduct:Picklist:v1", "minVersion": "1.0.0"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.analytics:dataProduct:OrganizationUnit:v1", "version": "1.0.0", "name": "OrganizationUnit", "description": "Organization Unit", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}], "requires": [{"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.foundationobjects:dataProduct:OrganizationalStructure:v1", "minVersion": "1.0.0"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.analytics:dataProduct:SupervisorHierarchy:v1", "version": "1.0.0", "name": "SupervisorHierarchy", "description": "Supervisor Hierarchy", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}], "requires": [{"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.workforce:dataProduct:WorkforcePerson:v1", "minVersion": "1.0.0"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.foundationobjects:dataProduct:CommonConfigurationData:v1", "version": "1.0.0", "name": "CommonConfigurationData", "description": "Common Configuration Data", "numberOfEntities": 20, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.workforce:dataProduct:Compensation:v1", "version": "1.0.0", "name": "Compensation", "description": "Compensation", "numberOfEntities": 6, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.foundationobjects:dataProduct:CompensationStructure:v1", "version": "1.0.0", "name": "CompensationStructure", "description": "Compensation Structure", "numberOfEntities": 7, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.analytics:dataProduct:DataProductConfig:v1", "version": "1.0.0", "name": "DataProductConfig", "description": "Data Product Configuration", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.foundationobjects:dataProduct:EnterpriseStructure:v1", "version": "1.0.0", "name": "EnterpriseStructure", "description": "Enterprise Structure", "numberOfEntities": 29, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.foundationobjects:dataProduct:JobStructure:v1", "version": "1.0.0", "name": "JobStructure", "description": "Job Structure", "numberOfEntities": 17, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.foundationobjects:dataProduct:OrganizationalStructure:v1", "version": "1.0.0", "name": "OrganizationalStructure", "description": "Organizational Structure", "numberOfEntities": 10, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.foundationobjects:dataProduct:PayStructure:v1", "version": "1.0.0", "name": "PayStructure", "description": "Pay Structure", "numberOfEntities": 16, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.extensibility:dataProduct:Picklist:v1", "version": "1.0.0", "name": "Picklist", "description": "Picklist", "numberOfEntities": 4, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.workforce:dataProduct:Position:v1", "version": "1.0.0", "name": "Position", "description": "Position", "numberOfEntities": 4, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.workforce:dataProduct:WorkforcePerson:v1", "version": "1.0.0", "name": "WorkforcePerson", "description": "Workforce Person", "numberOfEntities": 10, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2505"}]}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}