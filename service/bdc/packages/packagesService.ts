/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { CodedError } from "@sap/dwc-express-utils";
import { promiseMap } from "@sap/dwc-promise-utils";
import { Request } from "express";
import * as fs from "fs-extra";
import Status from "http-status-codes";
import * as path from "path";
import {
  ApplicationNamespace,
  BDCAvailablePackage,
  BDCPreviewImage,
  BDCSystem,
  SystemInfo,
} from "../../../shared/bdccockpit/Types";
import { gt, lte, semverCoerce } from "../../../shared/bdccockpit/utils/semverUtils";
import { logError, logInfo } from "../../connections/connectionStatus";
import { IFeatureFlagsMap } from "../../featureflags/FeatureFlagProvider";
import { isCanary, isLocalHanaMocked } from "../../lib/node";
import { Activity, AuthType, IRequestContext } from "../../repository/security/common/common";
import * as enumUtil from "../../routes/c4s/internal_services/utils/enum";
import { FormationService } from "../formation/formationService";
import { PackageInstallationService } from "../packageInstallation/packageInstallationService";
import { BDCPackageInstallation } from "../packageInstallation/packageInstallationTypes";
import { isProviderOfSourceType, sourceByProviderMap } from "../systems/applicationNamespaceUtils";
import { SystemsService } from "../systems/systemsService";
import { getBdcLogger } from "../utils/logUtils";
import { PackageUtils } from "./packageUtils";
import { PackagesDao } from "./packagesDao";
import { getComponentsByType, getCreationDateOfFile, getSystemNameByApplicationNamespace } from "./packagesManager";
import { BDCPackage, DeliveryStatus } from "./packagesTypes";

const logger = getBdcLogger(__filename);

export interface IAuthorization {
  AuthType: AuthType;
  Activity: Activity;
  featureFlags?: Array<keyof IFeatureFlagsMap>;
}

const prodLikeTenantIds = ["979d43dd-00ce-4d64-abcb-acf6108a50cd", "2155606f-5c89-4321-843b-d67d82e593d4"];

const DEFAULT_DELIVERY_STATUS = "development";

export class PackagesService {
  public static async setConfigurationRepoBranch(context: IRequestContext, branchName: string): Promise<void> {
    await PackagesDao.setConfigurationRepoBranch(context, branchName);
  }
  public static async buildAndAddAvailablePackages(
    context: IRequestContext,
    packages: BDCPackage[]
  ): Promise<BDCAvailablePackage[]> {
    // this method used for "get all" and get details of single
    const packageIdParameter: string | undefined = packages.length === 1 ? packages[0].id : undefined;
    const allPackageInstallations: Map<string, BDCPackageInstallation> =
      await PackageInstallationService.getAllPackageInstallationsWithRelations(context, packageIdParameter);
    const installedPackagesMap: Map<string, BDCPackageInstallation[]> = PackagesService.buildInstalledPackagesMap(
      context,
      allPackageInstallations
    );
    const [systemInfoArr, allFormationContexts] = await Promise.all([
      SystemsService.getAllSystems(context),
      FormationService.getAllFormationContexts(context),
    ]);
    const allPackagesWithFormationContext: BDCAvailablePackage[] = [];
    await Promise.all(
      packages.map(async (pck) => {
        allPackagesWithFormationContext.push(
          await PackagesService.buildAvailablePackage(
            context,
            pck,
            systemInfoArr,
            allFormationContexts,
            installedPackagesMap.get(pck.id)!
          )
        );
      })
    );
    return allPackagesWithFormationContext.filter((pck) => pck.systems.some((sys) => sys.isInstallable));
  }

  /*
   * The response from Catalog returns flat array of all packages, we need to arrange packages in map (see @returns)
   * @param allPackageInstallations
   * @returns Key: packageId, Value: all installed packages for the packageId
   */
  private static buildInstalledPackagesMap(
    context: IRequestContext,
    allPackageInstallations: Map<string, BDCPackageInstallation>
  ) {
    const installationPackagesPerPackageId: Map<string, BDCPackageInstallation[]> = new Map();
    allPackageInstallations.forEach((installedPackage, instId) => {
      const currentPckId = installedPackage?.originalPackage?.id;
      if (!currentPckId) {
        logger.logError(`Invalid installed package detected: ${JSON.stringify(installedPackage)}`, { context });
        return;
      }
      let currentArrayForId = installationPackagesPerPackageId.get(currentPckId);
      if (!currentArrayForId) {
        currentArrayForId = [];
        installationPackagesPerPackageId.set(currentPckId, currentArrayForId);
      }
      currentArrayForId.push(installedPackage);
    });
    return installationPackagesPerPackageId;
  }

  private static async buildAvailablePackage(
    context: IRequestContext,
    pck: BDCPackage,
    systemInfoArr: SystemInfo[],
    allFormationContexts: SystemInfo[],
    installedPackages: BDCPackageInstallation[]
  ): Promise<BDCAvailablePackage> {
    const firstS4RequiredApp = pck.requiredApplications.filter((requiredApp) =>
      isProviderOfSourceType(requiredApp.provider)
    )[0]!.applications[0];
    const systems = await PackagesService.buildBdcSystems(context, pck, allFormationContexts, installedPackages);

    const { products, contents } = await getComponentsByType(
      context,
      pck.type,
      pck.components,
      systemInfoArr,
      pck.requiredApplications,
      systems
    );
    systems.forEach((system) => {
      system.productsCompatibility = PackageUtils.getProductsCompatibility(system.version, products);
    });
    const minSystemVersion = PackageUtils.getMinVersionFromArray(products?.map((product) => product.systemVersion!));
    return {
      id: pck.id,
      status: "available",
      name: pck.name,
      creationDate: pck.creationDate,
      preview: pck.preview,
      type: pck.type,
      systemType: getSystemNameByApplicationNamespace(firstS4RequiredApp.applicationNamespace as ApplicationNamespace),
      supportedSystemVersion: minSystemVersion,
      version: pck.version,
      category: pck.category,
      description: pck.description,
      products,
      contents,
      systems,
      landscape: pck.landscapeType?.name,
    } as BDCAvailablePackage;
  }

  private static async buildBdcSystems(
    context: IRequestContext,
    pck: BDCPackage,
    allFormationContexts: SystemInfo[],
    installedPackages: BDCPackageInstallation[]
  ): Promise<BDCSystem[]> {
    const availableSystemsForPackage = await FormationService.buildFormationContextAvailableForInstallationPackage(
      context,
      pck,
      allFormationContexts,
      installedPackages
    );
    return availableSystemsForPackage
      .filter(
        (system) => system.applicationNamespace === pck.requiredApplications[0].applications[0].applicationNamespace
      )
      .map((availableSystem) => ({
        applicationNamespace: availableSystem.applicationNamespace,
        tenant: availableSystem.applicationTenantId,
        formations: availableSystem.formations,
        name: availableSystem.name,
        isInstallable: availableSystem.isInstallable,
        version: semverCoerce(availableSystem.applicationVersion),
      }));
  }

  public static async readBase64Images(imagesPath: string): Promise<BDCPreviewImage[]> {
    const imagePaths = await fs.readdir(imagesPath);
    return await promiseMap<string, BDCPreviewImage>(
      imagePaths,
      async (file) => {
        const filePath = path.join(imagesPath, file);
        const fileContent = await fs.readFile(filePath);
        return { base64: fileContent.toString("base64") };
      },
      { concurrency: 2 }
    );
  }

  private static async readPackagesFromFileSystem(
    context: IRequestContext,
    listPath: string,
    filter?: (data: any) => boolean
  ): Promise<BDCPackage[]> {
    const foldersInDir = await fs.readdir(listPath, { withFileTypes: true });
    const directories = foldersInDir.filter((dirent) => dirent.isDirectory());
    return await promiseMap<fs.Dirent, BDCPackage>(
      directories,
      async (folder) => {
        try {
          const folderPath = path.join(listPath, folder.name);
          const filePath = path.join(folderPath, "data.json");
          const data = JSON.parse(await fs.readFile(filePath, "utf8"));
          delete data.preview;
          data.creationDate = data?.creationDate || (await getCreationDateOfFile(filePath));
          if (filter && !filter(data)) {
            return undefined;
          }
          const imagesPath = path.join(folderPath, "images");
          if (fs.existsSync(imagesPath)) {
            const files = await fs.promises.readdir(imagesPath);

            if (files.length > 0) {
              const preview = await PackagesService.readBase64Images(imagesPath);
              return { ...data, preview };
            }
          }
          return data;
        } catch (error) {
          logger.logError(`Failed to read file: ${JSON.stringify(error)}`, { context });
        }
      },
      {
        concurrency: 3,
      }
    );
  }

  public static async getBdcPackageWithValidation(context: IRequestContext, packageId: string): Promise<BDCPackage> {
    if (!packageId?.trim()) {
      throw new CodedError(enumUtil.ErrorCodes.InvalidInput, `Missing required parameters`, Status.BAD_REQUEST);
    }
    const bdcPackage = await PackagesService.getPackageById(context, packageId);
    if (!bdcPackage) {
      throw new CodedError(
        enumUtil.ErrorCodes.NotFound,
        `Could not find package with id ${packageId}`,
        Status.BAD_REQUEST
      );
    }
    return bdcPackage;
  }

  public static async getPackagesFilteredByEntitlement(packages: BDCPackage[], req: Request): Promise<BDCPackage[]> {
    const enabledProviders = await PackageUtils.getEnabledProviders(req.context);
    return (
      packages?.filter((pkg) => {
        const allProviders = new Set(
          pkg.requiredApplications
            .map((app) => app.provider)
            .filter((provider) => Array.from(sourceByProviderMap.keys()).includes(provider))
        );
        return [...allProviders].every((provider) => enabledProviders.has(provider));
      }) ?? []
    );
  }

  public static async getPackages(
    context?: IRequestContext,
    filter?: (data: any) => boolean,
    isDev?: boolean | undefined
  ): Promise<BDCPackage[]> {
    const repoBranch = (await PackagesDao.getConfigurationRepoBranches(context!))[0];
    const ffGetFromCatalogDev = await context?.isFeatureFlagActive("DWCO_BDC_PACKAGE_CONTENT_DEV_SUPPORT");
    const ffGetFromCatalogProd = await context?.isFeatureFlagActive("DWCO_PROD_BDC_PACKAGES_FROM_CONTENT_GIT_REPO");

    if ((ffGetFromCatalogProd && repoBranch === "prodprod") || (ffGetFromCatalogDev && repoBranch !== "prodprod")) {
      const packages: BDCPackage[] = await PackagesDao.getPackages(context!);
      // Marked out, but we may need it in future
      // PackagesService.adaptNamesForMultipleLandscapes(packages);
      return packages;
    } else {
      return (await this.getPackagesFS(context, filter, isDev)) || [];
    }
  }

  public static async filterPackagesByAvailableSystems(
    context: IRequestContext,
    packages: BDCPackage[]
  ): Promise<BDCPackage[]> {
    const systems = await SystemsService.getAllSystems(context);

    const availableSystems = new Map<string, string>();

    systems.forEach((system) => {
      if (system.applicationVersion !== undefined) {
        const systemVersion = system.applicationVersion;
        const highestVersion = availableSystems.get(system.applicationNamespace);
        if (highestVersion === undefined || gt(systemVersion, highestVersion)) {
          availableSystems.set(system.applicationNamespace, systemVersion);
        }
      } else {
        logError(
          `The following system doesn't have an applicationVersion, systemId: ${system.systemId}, applicationNamespace: ${system.applicationNamespace}, applicationTenantId: ${system.applicationTenantId}`,
          { context }
        );
      }
    });

    const filtered = packages.filter((pck: BDCPackage) => {
      if (pck.requiredApplications.length !== 1) {
        logError(`Package filtering: 'requiredApplications' supposed to have only one entry, packageId: ${pck.id}`, {
          context,
        });
        return false;
      } else if (pck.requiredApplications[0].applications.length === 0) {
        logError(`Package filtering: 'requiredApplications[0].applications' is empty, packageId: ${pck.id}`, {
          context,
        });
        return false;
      }

      const availableSystemVersion = availableSystems.get(
        pck.requiredApplications[0]?.applications[0]?.applicationNamespace
      );

      if (pck.requiredApplications[0]?.applications[0]?.minVersion !== undefined) {
        const pckMinVersion = pck.requiredApplications[0]?.applications[0]?.minVersion;
        return availableSystemVersion !== undefined && lte(pckMinVersion, availableSystemVersion);
      } else {
        logError(`The minimum version is undefined in the required application of the package, packageId: ${pck.id}`, {
          context,
        });
      }
    });

    logInfo(
      `Packages are filtered based on the available systems after receiving them from Catalog, original packages count: ${packages.length}, packages count after filtering: ${filtered.length}`,
      { context }
    );

    return filtered;
  }

  public static async getAndFilterPackages(context: IRequestContext): Promise<BDCPackage[]> {
    const packages: BDCPackage[] = await PackagesService.getPackages(context, undefined, false);

    if (!packages) {
      return [];
    }

    const filteredPackages: BDCPackage[] = await PackagesService.filterPackagesByAvailableSystems(context, packages);

    if (!filteredPackages) {
      return [];
    }

    return filteredPackages;
  }

  static adaptNamesForMultipleLandscapes(packages: BDCPackage[]) {
    const landscapeTypes = new Set<string>();
    packages.forEach((pck) => {
      landscapeTypes.add(pck.landscapeType!.id);
    });
    if (landscapeTypes.size > 1) {
      packages.forEach((pck) => {
        pck.name = `${pck.name}(${pck.landscapeType?.name})`;
      });
    }
  }

  public static async getPackagesFS(
    context?: IRequestContext,
    filter?: (data: any) => boolean,
    isDev?: boolean | undefined
  ): Promise<BDCPackage[] | undefined> {
    logger.logInfo(
      `Reading packages from file. Is development value: ${isDev}, isLocalHanaMocked(): ${isLocalHanaMocked()}, isCanary(): ${isCanary()}`,
      { context }
    );
    try {
      const showDevPackagesInProd = await context?.isFeatureFlagActive("DWCO_BDC_COCKPIT_SHOW_PKG_IN_STATE_DEV");
      const listPath = path.join(__dirname, "packages-list");
      const packages = await this.readPackagesFromFileSystem(context!, listPath, filter);
      return packages.filter((pkg) => {
        if (pkg === undefined) {
          return false;
        } else if ((isDev && isDev === true) || isLocalHanaMocked()) {
          return true;
        } else {
          const isProductionDisplayPolicy = !isCanary() || prodLikeTenantIds.includes(context?.tenantId ?? "");
          return this.isPackageAvailable(
            pkg.deliveryStatus ?? DEFAULT_DELIVERY_STATUS,
            isProductionDisplayPolicy,
            showDevPackagesInProd ?? false
          );
        }
      });
    } catch (error) {
      logger.logError(error, { context });
    }
  }

  private static isPackageAvailable(
    deliveryStatus: DeliveryStatus,
    isProductionDisplayPolicy: boolean,
    showDevPackagesInProd: boolean
  ): boolean {
    return this.isPackageAvailableInGa(deliveryStatus, isProductionDisplayPolicy, showDevPackagesInProd);
  }

  private static isPackageAvailableInGa(
    deliveryStatus: DeliveryStatus,
    isProductionDisplayPolicy: boolean,
    showDevPackagesInProd: boolean
  ) {
    return !isProductionDisplayPolicy || showDevPackagesInProd
      ? deliveryStatus === "production" || deliveryStatus === "development"
      : deliveryStatus === "production";
  }

  public static async getPackageById(context: IRequestContext, id: string): Promise<BDCPackage | undefined> {
    const ffGetFromCatalogDev = await context?.isFeatureFlagActive("DWCO_BDC_PACKAGE_CONTENT_DEV_SUPPORT");
    const ffGetFromCatalogProd = await context?.isFeatureFlagActive("DWCO_PROD_BDC_PACKAGES_FROM_CONTENT_GIT_REPO");
    const repoBranch = (await PackagesDao.getConfigurationRepoBranches(context!))[0];
    if ((ffGetFromCatalogProd && repoBranch === "prodprod") || (ffGetFromCatalogDev && repoBranch !== "prodprod")) {
      const packages = await PackagesDao.getPackages(context, id);
      return packages?.[0] || undefined;
    } else {
      const packages = await this.getPackages(context, (data) => data.id === id);
      return packages?.[0] || undefined;
    }
  }
}
