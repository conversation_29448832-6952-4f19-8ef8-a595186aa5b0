/** @format */

import { ConfigurationUtils } from "@sap/deepsea-sqlutils";
import { AuthenticationMode, httpClient, HttpMethod } from "@sap/dwc-http-client";
import Status from "http-status-codes";
import { gte } from "../../../shared/bdccockpit/utils/semverUtils";
import { IRequestContext } from "../../repository/security/common/common";
import { ExternalCallCategory } from "../../repository/security/common/externalCallTypes";
import { ClassName, PropertiesField } from "../catalog/CatalogConstants";
import { CatalogPayload, TagFilter } from "../catalog/catalogTypes";
import { buildDatasphereCatalogURL } from "../catalog/CatalogUtils";
import { getBdcLogger } from "../utils/logUtils";
import { BDCPackage } from "./packagesTypes";
const CUSTOM_SPACE = "bdc";
const CALL_TIMEOUT = 2 * 60 * 1000;
const { logError, logInfo } = getBdcLogger(__filename);

export class PackagesDao {
  private static CONFIG_REPO_BRANCH_KEY: string = "BDC_SAP_CONTENT_DEV_REPO_BRANCH";
  private static CONFIG_REPO_BRANCH_VERSION: string = "2025.11.0";
  public static async getPackages(context: IRequestContext, packageId?: string): Promise<BDCPackage[]> {
    const deepseaBaseUrl = buildDatasphereCatalogURL(context);
    const url = `${deepseaBaseUrl}/v1/customSpaces/${CUSTOM_SPACE}/dependencyQuery`;
    try {
      const headers = {
        "content-type": "application/json",
        "x-sap-boc-tenant-id": context.tenantId || "",
        "x-sap-boc-user-id": context.userInfo.userId || "",
      };
      const payloadObj: CatalogPayload = {
        rootObjectSelector: {
          className: ClassName.BDC_PACKAGE,
        },
        propertySelectors: [
          { propertyId: PropertiesField.BDC_PCK_JSON },
          { propertyId: PropertiesField.BDC_PCK_PREVIEW_LINK },
          { propertyId: PropertiesField.BDC_PCK_LANDSCAPE_TYPE },
          { propertyId: PropertiesField.BDC_PCK_ID },
          { propertyId: PropertiesField.BDC_PCK_NAME },
          { propertyId: PropertiesField.BDC_PCK_VERSION },
        ],
      };
      payloadObj.rootObjectTagFilters = await PackagesDao.buildBranchTagFilters(context);
      if (packageId) {
        // filter out by name - we may need it in future - do not delete
        // payloadObj.rootObjectSelector.objectSelector = {
        //   names: [packageId],
        // };
        if (!payloadObj.rootObjectTagFilters) {
          payloadObj.rootObjectTagFilters = [];
        }
        payloadObj.rootObjectTagFilters.push({
          propertyId: PropertiesField.BDC_PCK_ID,
          propertyValue: {
            valueString: packageId,
          },
        });
      }

      logInfo(`Payload to get packages is ${JSON.stringify(payloadObj)}`, { context });
      const response = await httpClient.call({
        url,
        opts: {
          method: HttpMethod.POST,
          authentication: AuthenticationMode.Uaa,
          body: JSON.stringify(payloadObj),
          acceptedStatusCodes: [Status.OK],
          headers,
          requestContext: context,
          callCategory: ExternalCallCategory.OneDataCatalog,
          callTimeout: CALL_TIMEOUT,
        },
      });
      if (response.status !== Status.OK) {
        throw new Error(`Response status code:${response.status}`);
      }
      logInfo(`Catalog response: ${JSON.stringify(response.body)}`, { context });
      return PackagesDao.getPackagesFromResponse(response.body, context);
    } catch (error) {
      logError(
        `[OneDataCatalog] BDC Cockpit catalog error get all installation packages with components for tenantId ${context.tenantId}, for url ${url}\n${error}`,
        {
          context,
        }
      );
      throw error;
    }
  }
  private static async buildBranchTagFilters(context: IRequestContext): Promise<TagFilter[]> {
    const rootObjectTagFilters: TagFilter[] = [];
    const branches: string[] = await this.getConfigurationRepoBranches(context);
    if (branches && branches.length > 0) {
      branches.forEach((branch) => {
        branch = branch.trim();
        if (branch.length > 0) {
          rootObjectTagFilters.push({
            propertyId: "CP_BDC_PKG_LANDSCAPE_TYPE",
            propertyValue: {
              valueString: branch,
            },
          });
        }
      });
    }
    return rootObjectTagFilters;
  }

  public static async setConfigurationRepoBranch(context: IRequestContext, branchName: string): Promise<void> {
    if (!branchName) {
      throw new Error("invalid branch name");
    }
    branchName = branchName.trim();
    await ConfigurationUtils.upsert(
      context,
      PackagesDao.CONFIG_REPO_BRANCH_KEY,
      JSON.stringify({ branch: branchName }),
      {
        useTenantScope: true,
        overrideValueVersion: PackagesDao.CONFIG_REPO_BRANCH_VERSION,
      }
    );
  }
  public static async getConfigurationRepoBranches(context: IRequestContext): Promise<string[]> {
    const defaultBranch = "prodProd";
    try {
      const branchConfig = await ConfigurationUtils.getEffectiveValue(context, PackagesDao.CONFIG_REPO_BRANCH_KEY, {
        preferTenantScope: true,
        overrideValueVersion: PackagesDao.CONFIG_REPO_BRANCH_VERSION,
      });
      logInfo(`Config Service response for key: BDC_SAP_CONTENT_DEV_REPO_BRANCH is: ${branchConfig}`, { context });
      const branchConfigObj = JSON.parse(branchConfig!);
      const branches: string[] = (branchConfigObj?.branch || "").split(",");
      if (branches.length === 0) {
        branches.push(defaultBranch);
      }
      return branches.map((br) => br.trim());
    } catch (error) {
      logError(`Failed to get branches from configuration service, Error: ${error.message}`, { context });
      return [defaultBranch];
    }
  }
  private static getPackagesFromResponse(packages: any, context: IRequestContext): BDCPackage[] {
    const packagesResult: BDCPackage[] = [];
    const packagesHighestVersionsMap: Map<string, string> = new Map(); // used to filter out packages with lower version
    packages.allObjects.forEach((object: any) => {
      const bdcPackage = PackagesDao.buildBdcPackageFromCatalogObject(context, object);
      if (bdcPackage) {
        const currentVersion = packagesHighestVersionsMap.get(bdcPackage.id);
        if (!currentVersion || gte(bdcPackage.version, currentVersion)) {
          packagesHighestVersionsMap.set(bdcPackage.id, bdcPackage.version);
          packagesResult.push(bdcPackage);
        }
      }
    });
    return packagesResult.filter((bp) => packagesHighestVersionsMap.get(bp.id) === bp.version);
  }

  private static buildBdcPackageFromCatalogObject(context: IRequestContext, object: any): BDCPackage | undefined {
    let bdcPackage: BDCPackage;
    try {
      logInfo(`Start parsing BDC Package: ${object}`, { context });
      const pckJsonObj = object.propertyTags.find(
        (propertyTag: any) => propertyTag.propertyId === PropertiesField.BDC_PCK_JSON
      );
      bdcPackage = JSON.parse(pckJsonObj.valueObjects[0].valueText);

      const previewLinksObj = object.propertyTags.find(
        (propertyTag: any) => propertyTag.propertyId === PropertiesField.BDC_PCK_PREVIEW_LINK
      );
      bdcPackage.preview = [];
      previewLinksObj?.valueObjects?.forEach((valObj: any) => {
        bdcPackage.preview.push({ base64: valObj.valueText });
      });

      const landscapeTypeObj = object.propertyTags.find(
        (propertyTag: any) => propertyTag.propertyId === PropertiesField.BDC_PCK_LANDSCAPE_TYPE
      );
      bdcPackage.landscapeType = {
        id: landscapeTypeObj.valueObjects[0].valueId,
        description: landscapeTypeObj.valueObjects[0].valueString,
      };

      const packId = object.propertyTags.find(
        (propertyTag: any) => propertyTag.propertyId === PropertiesField.BDC_PCK_ID
      );
      bdcPackage.id = packId.valueObjects![0]!.valueString;

      const packVersion = object.propertyTags.find(
        (propertyTag: any) => propertyTag.propertyId === PropertiesField.BDC_PCK_VERSION
      );
      bdcPackage.version = packVersion?.valueObjects![0]!.valueString;
      return bdcPackage;
    } catch (error) {
      logError(`Failed to read BDC package from: ${object}, error: ${JSON.stringify(error)}`, { context });
      return undefined;
    }
  }
}
