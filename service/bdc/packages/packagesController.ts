/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { Request, Response } from "express";
import Status from "http-status-codes";
import { sendErrorResponse } from "../../server/errorResponse";
import { filterAvailablePackagesByEntitlement } from "../entitlement/entitlementValidator";
import { PackagesService } from "./packagesService";
import { BDCPackage } from "./packagesTypes";

export async function setConfigurationRepoBranch(req: Request, res: Response) {
  await PackagesService.setConfigurationRepoBranch(req.context, req.query?.branch as string);
  res.status(Status.OK).send();
}
export async function getPackages(req: Request, res: Response) {
  try {
    const showAll = req.query?.showAll === "true";
    const packages: BDCPackage[] | undefined = await PackagesService.getAndFilterPackages(req.context);

    if (!packages?.length) {
      return res.status(Status.OK).send([]);
    }

    if (showAll) {
      const result = await PackagesService.getPackagesFilteredByEntitlement(packages, req);
      res.status(Status.OK).send(result);
    } else {
      let result = packages ? await PackagesService.buildAndAddAvailablePackages(req.context, packages) : [];
      result = await filterAvailablePackagesByEntitlement(req.context, result);
      res.status(Status.OK).send(result);
    }
  } catch (err) {
    sendErrorResponse(req.context, "getPackagesFailed", { err });
  }
}

export async function supportGetPackages(req: Request, res: Response) {
  try {
    if (!(await (req.context as IRequestContext).isFeatureFlagActive("DWCO_BDC_CLI_FOR_BDC_PACKAGES"))) {
      return res.status(Status.FORBIDDEN).send("Access to packages is forbidden due to feature flag.");
    }
    return await getPackages(req, res);
  } catch (err) {
    sendErrorResponse(req.context, "supportGetPackagesFailed", { err });
  }
}

export async function getPackageById(req: Request, res: Response) {
  try {
    const packageId = req.params?.packageid;
    const bdcPackage = await PackagesService.getPackageById(req.context, packageId);
    if (bdcPackage) {
      const result = await PackagesService.buildAndAddAvailablePackages(req.context, [bdcPackage]);
      res.status(Status.OK).send(result?.[0]);
    } else {
      res.status(Status.OK).send({});
    }
  } catch (err) {
    sendErrorResponse(req.context, "getPackageByIdFailed", { err });
  }
}
