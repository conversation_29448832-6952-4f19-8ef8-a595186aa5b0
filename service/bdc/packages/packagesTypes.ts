/** @format */

import { BDCBasePackage, BDCComponent, BDCDataProduct, ComponentCategory } from "../../../shared/bdccockpit/Types";
import { RequiredProviderApplications } from "../acnProvision/types";

export type DeliveryStatus = "development" | "production" | "early_adopter" | "early_adopter_development";

export interface BDCPackage extends BDCBasePackage {
  shortDescription: string;
  applicationComponent: string;
  schemaName: string;
  schemaVersion: string;
  fileProducer: string;
  requiredApplications: RequiredProviderApplications[];
  components: BDCPackageComponent[];
  deliveryStatus?: DeliveryStatus;
  landscapeType?: LandscapeType;
}
export interface LandscapeType {
  id: string;
  name: string;
}
export interface ComponentRequiry {
  category: ComponentCategory;
  minVersion: string;
}
export interface CNPackageRequiry extends ComponentRequiry {
  provider: string;
  name: string;
}

export interface DataProductRequiry extends ComponentRequiry {
  provider: string;
  ordid: string;
}

export interface ComponentWithRequiry<T extends ComponentRequiry> extends BDCComponent {
  requires: T[];
}

export type BDCContentPackage = ComponentWithRequiry<CNPackageRequiry | DataProductRequiry>;

export type BDCPackageComponent = BDCDataProduct | BDCContentPackage;
