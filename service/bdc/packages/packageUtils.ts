/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { BDCPackageType } from "../../../shared/bdccockpit/Enums";
import {
  BDCComponent,
  BDCDataProduct,
  BDCInstalledContent,
  BDCInstalledProduct,
  ComponentCategory,
} from "../../../shared/bdccockpit/Types";
import { gte, lte, semverCoerce } from "../../../shared/bdccockpit/utils/semverUtils";
import { TenantSKU } from "../entitlement/entitlementTypes";
import { getTenantEntitlementByName } from "../entitlement/unifiedServicesApiClient";
import {
  BDCPackageInstallation,
  ComponentInstallationStatus,
  InstallationStatusCode,
} from "../packageInstallation/packageInstallationTypes";
import { getBdcLogger } from "../utils/logUtils";
import { BDCPackage } from "./packagesTypes";

const { logInfo } = getBdcLogger(__filename);

export class PackageUtils {
  public static async getComponentsByType(
    context: IRequestContext,
    bdcPackageInstallation: BDCPackageInstallation
  ): Promise<{
    contents: BDCInstalledContent[];
    products: BDCInstalledProduct[];
  }> {
    const currentPackage: BDCPackage = bdcPackageInstallation.originalPackage;
    const componentsInstallationStatus: ComponentInstallationStatus[] =
      bdcPackageInstallation.componentsInstallationStatus;

    const products: BDCInstalledProduct[] = [];
    const contents: BDCInstalledContent[] = [];

    await Promise.all(
      (currentPackage?.components || []).map(async (component) => {
        if (PackageUtils.isDataProduct(component)) {
          const componentInstallationStatus = componentsInstallationStatus.find(
            (cis) => cis.componentId === component.componentId
          )!;
          const installedProduct = await PackageUtils.getInstalledProduct(
            context,
            component,
            currentPackage,
            componentInstallationStatus
          );
          if (installedProduct) {
            products.push(installedProduct);
          }
        } else {
          contents.push(PackageUtils.getInstalledContent(component));
        }
      })
    );
    return { products, contents };
  }

  public static async isPartialInstallationAllowed(
    context: IRequestContext,
    packageType: BDCPackageType
  ): Promise<boolean> {
    if (packageType !== "DataPackage") {
      return Promise.resolve(false);
    }
    return context.isFeatureFlagActive("DWCO_BDC_PARTIAL_INSTALLATION");
  }

  private static isDataProduct(component: BDCComponent): component is BDCDataProduct {
    return component.category === "DataProduct";
  }

  private static async getInstalledProduct(
    context: IRequestContext,
    component: BDCDataProduct,
    currentPackage: BDCPackage,
    componentInstallationStatus: ComponentInstallationStatus
  ): Promise<BDCInstalledProduct | undefined> {
    let requiredApplications =
      currentPackage.requiredApplications.find((req) => req.provider === component.provider)?.applications || [];
    if (await PackageUtils.isPartialInstallationAllowed(context, currentPackage.type)) {
      if (component.applications?.length) {
        requiredApplications = component.applications;
      }
    }
    const minSystemVersion = PackageUtils.getMinVersionFromArray(
      requiredApplications.map((reqApp) => reqApp.minVersion!)
    );
    logInfo(`The minSystemVersion of ${JSON.stringify(requiredApplications)} is ${minSystemVersion}`, { context });
    return {
      ordid: component.ordid,
      componentId: component.componentId,
      category: component.category,
      provider: component.provider,
      name: component.name,
      description: component.description,
      version: component.version,
      numberOfEntities: component.numberOfEntities,
      systemVersion: minSystemVersion,
      isActive: componentInstallationStatus.status === "DONE",
    };
  }

  private static getInstalledContent(component: BDCComponent): BDCInstalledContent {
    const installedComponent: BDCInstalledContent = {
      componentId: component.componentId,
      category: component.category,
      provider: component.provider,
      name: component.name,
      description: component.description,
      version: component.version,
      spaces: [],
    };

    return installedComponent;
  }

  public static getMinVersionFromArray(versions: string[]): string {
    if (versions?.map?.length === 0) {
      return "";
    }
    return versions?.map(semverCoerce).reduce((curr, prevVer) => (lte(curr, prevVer) ? curr : prevVer));
  }

  public static getProductsCompatibility(
    targetSystemVersion: string,
    products: BDCDataProduct[]
  ): { compatible: number; incompatible: number } {
    targetSystemVersion = semverCoerce(targetSystemVersion);
    if (products?.length === 0) {
      return { compatible: 0, incompatible: 0 };
    }
    return products.reduce(
      (ret, product) => {
        if (gte(targetSystemVersion, product.systemVersion)) {
          ret.compatible++;
        } else {
          ret.incompatible++;
        }
        return ret;
      },
      { compatible: 0, incompatible: 0 }
    );
  }

  public static getComponentStatusList(bdcPackageInstallation: BDCPackageInstallation): Array<{
    componentId: string;
    category: ComponentCategory;
    status: InstallationStatusCode;
  }> {
    return bdcPackageInstallation.originalPackage.components.map((component) => {
      const status =
        bdcPackageInstallation.componentsInstallationStatus.find(
          (status) => status.componentId === component.componentId
        )?.status || "PENDING";
      return {
        componentId: component.componentId,
        category: component.category,
        status,
      };
    });
  }

  public static async getEnabledProviders(context: IRequestContext): Promise<Set<string>> {
    const entitlementCheckFlag = "DWCO_BDC_ENTITLEMENT_CHECK";

    const entitlementConfig: Partial<Record<TenantSKU, string>> = {
      [TenantSKU.S4PCE]: "sap.s4pce",
      [TenantSKU.S4]: "sap.s4",
      [TenantSKU.HCM]: "sap.sf",
    };

    const enabledProviders = new Set<string>();
    const isFlagActive = await context.isFeatureFlagActive(entitlementCheckFlag);

    if (!isFlagActive) {
      return enabledProviders;
    }

    await Promise.all(
      Object.entries(entitlementConfig).map(async ([sku, provider]) => {
        const hasEntitlement = await getTenantEntitlementByName(context, sku as TenantSKU);
        if (hasEntitlement) {
          enabledProviders.add(provider);
        }
      })
    );

    return enabledProviders;
  }
}
