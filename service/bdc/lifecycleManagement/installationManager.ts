/** @format */

import { NotificationType } from "@sap/deepsea-types";
import * as semver from "semver";
import { v4 as uuidv4 } from "uuid";
import { MISSING_ENTITLEMENT } from "../../../shared/bdccockpit/Constants";
import {
  Application,
  BDCDataProduct,
  BDCPackageInstallationProperties,
  BDCRequestContext,
} from "../../../shared/bdccockpit/Types";
import { gte } from "../../../shared/bdccockpit/utils/semverUtils";
import { InstallationMessage, MessageAction } from "../../messageQueuing/consumer/PackageInstallationConsumer";
import { FlowTimeoutError } from "../dataProducts/dataProductTypes";
import { EntitlementService } from "../entitlement/entitlementService";
import { allowedToInstall, getMissingEntitlementDetails } from "../entitlement/entitlementValidator";
import { FormationService } from "../formation/formationService";
import { InstallersFacade } from "../installers/installersFacade";
import { sendInstallationMetrics } from "../metrics/metricsUtils";
import { PackageInstallationService } from "../packageInstallation/packageInstallationService";
import {
  BDCPackageInstallation,
  BDCPreviousPackageData,
  ComponentInstallationDetails,
  ComponentInstallationStatus,
  ExistingInstallationOperaitonResult,
  InstallationStatusCode,
} from "../packageInstallation/packageInstallationTypes";
import { validateBdcPackageInstallationAndMessage } from "../packageInstallation/packageInstallationUtils";
import { PackageUtils } from "../packages/packageUtils";
import { PackagesService } from "../packages/packagesService";
import { BDCPackage } from "../packages/packagesTypes";
import { PackageInstallationValidator } from "../packages/validation/packageInstallationValidator";
import { getInstallationPropertiesByProvider, isProviderOfSourceType } from "../systems/applicationNamespaceUtils";
import { SystemsService } from "../systems/systemsService";
import {
  BDCException,
  BDCExceptionType,
  createFeatureFlagOffException,
  createRetryInstallationException,
} from "../utils/BDCException";
import { getBdcLogger } from "../utils/logUtils";
import { notificationsHelper } from "../utils/notificationUtils";
import { sendPackageInstallationMessage } from "../utils/solaceUtils";
import { GraphAction, getNextComponents } from "./componentResolver";
import { SingleComponentInstallation } from "./lifecycleManagementTypes";

const { logError, logInfo, getLogFunctionForStatus, logWarning } = getBdcLogger(__filename);

interface ShouldSkipInstallation {
  shouldSkip: boolean;
  message: string;
  status: Extract<InstallationStatusCode, "DONE" | "NOT ACTIVATED">;
}

/**
 * SingleComponentInstallation is: Internal structure of the single component to validate its installation progress.
 * The structure combines:
 * - component
 * - previous installation status: could be final or one that requires validation request to installation service
 * - installation properties: in case retry of installation is required
 */

export class InstallationManager {
  static readonly MAX_NUMBER_OF_INSTALL_RETRIES: number = 3;

  static async processInstallationMessage(
    context: BDCRequestContext,
    currentInstallationMessage: InstallationMessage
  ): Promise<void> {
    logInfo(`Start installation  process for the message: ${JSON.stringify(currentInstallationMessage)}`, { context });
    // Get package installation by installationId
    const bdcPackageInstallation: BDCPackageInstallation = await this.getCurrentPackageInstallation(
      currentInstallationMessage.installationId,
      context
    );
    const bdcPackage: BDCPackage | undefined = await PackagesService.getPackageById(
      context,
      bdcPackageInstallation.originalPackage.id
    );

    validateBdcPackageInstallationAndMessage(bdcPackageInstallation, currentInstallationMessage, context);

    bdcPackageInstallation.installationCyclesCount = currentInstallationMessage.installationCyclesCount;
    const nextInstallationMessage: InstallationMessage = {
      installationId: currentInstallationMessage.installationId,
      installationCyclesCount: currentInstallationMessage.installationCyclesCount + 1,
      componentIds: [],
      action: MessageAction.INSTALL,
      tenantId: context.tenantId!,
      userId: context.userInfo.userId!,
      userName: context.userInfo.userName!,
      correlationId: currentInstallationMessage.correlationId || "",
      traceId: currentInstallationMessage.traceId ?? "",
    };

    let isInstallationFailed = false;
    try {
      await SystemsService.initiateAllSystemsCache(context);
      /* eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled */
      await Promise.all(
        // For each Component Id in Solace message, handle the component status
        currentInstallationMessage.componentIds.map(async (messageComponentId) => {
          logInfo(`Start single component validation. Id: ${messageComponentId}`, { context });
          const currentComponentInstallation: SingleComponentInstallation = this.getCurrentComponentInstallation(
            messageComponentId,
            bdcPackageInstallation,
            context
          );

          let newComponentInstallation: SingleComponentInstallation;
          const { shouldSkip, message, status } = await InstallationManager.shouldSkipInstallation(
            context,
            bdcPackageInstallation,
            currentComponentInstallation
          );
          if (shouldSkip) {
            const installDate = new Date();
            newComponentInstallation = {
              originalComponent: currentComponentInstallation.originalComponent,
              installationProperties: currentComponentInstallation.installationProperties,
              installationStatus: {
                componentId: currentComponentInstallation.originalComponent.componentId,
                componentInstallationDetails: {} as ComponentInstallationDetails,
                startTime: installDate,
                endTime: installDate,
                status,
                message,
                retryCount: 0,
              },
            };
          } else {
            newComponentInstallation = await this.processComponentInstallation(
              context,
              currentComponentInstallation,
              bdcPackageInstallation,
              bdcPackage!
            );
            this.overrideStatusOnCancellation(context, newComponentInstallation, bdcPackageInstallation);

            if (newComponentInstallation.installationStatus.status === "FAILED") {
              isInstallationFailed = true;
            }

            await InstallationManager.formationRefreshIfNeeded(context, newComponentInstallation);
          }

          InstallationManager.updateBDCPackageInstallationWithComponentStatus(
            newComponentInstallation,
            bdcPackageInstallation
          );
          InstallationManager.addNewInstallationMessageIfNeeded(newComponentInstallation, nextInstallationMessage);
        })
      );
    } catch (error) {
      logError([`failed to process installation for component`, error], { context });
    }

    await InstallationManager.doNextInstallationStep(
      context,
      isInstallationFailed,
      nextInstallationMessage,
      bdcPackageInstallation
    );

    logInfo(
      `Update installation new status in catalog for installationId: ${currentInstallationMessage.installationId}`,
      {
        context,
      }
    );
    await PackageInstallationService.updatePackageInstallation(bdcPackageInstallation, context);
  }

  static async installPackage(
    context: BDCRequestContext,
    packageId: string,
    installationProperties: BDCPackageInstallationProperties[]
  ): Promise<string> {
    const bdcPackage = await PackagesService.getBdcPackageWithValidation(context, packageId);
    const allowed = await allowedToInstall(context, installationProperties);
    if (!allowed) {
      return MISSING_ENTITLEMENT;
    }
    await notificationsHelper.sendOnboardStartedNotification({
      context,
      packageInfo: bdcPackage,
    });
    return await this.validateAndInstall(context, installationProperties, bdcPackage);
  }

  public static async endInstallation(
    context: BDCRequestContext,
    bdcPackageInstallation: BDCPackageInstallation,
    isInstallationFailed: boolean
  ) {
    const isUpdating = InstallationManager.isUpdating(bdcPackageInstallation);
    try {
      bdcPackageInstallation.endTime = new Date();
      bdcPackageInstallation.lastModifiedTime = bdcPackageInstallation.endTime;
      delete bdcPackageInstallation.previousPackageData;
      delete bdcPackageInstallation.cancellationRequested;

      if (!isInstallationFailed) {
        await PackageInstallationService.addLinks(context, bdcPackageInstallation);
      }

      await PackageInstallationService.updateInstallationComponentsIds(
        context,
        bdcPackageInstallation,
        isInstallationFailed
      );
      const applicationNamespaces = bdcPackageInstallation.installationProperties.map(
        (installationProperty) => installationProperty.applicationNamespace
      );
      await EntitlementService.addEntitlementIfNotExists(context, applicationNamespaces);
    } catch (err) {
      logError([`failing to end installation of ${bdcPackageInstallation.originalPackage.name}`, err], {
        context,
      });
    } finally {
      await notificationsHelper.sendOnboardFinishedNotification({
        context,
        notificationType: isInstallationFailed ? NotificationType.ALERT : NotificationType.SUCCESS,
        packageInstallation: bdcPackageInstallation,
        isUpdating,
      });

      logInfo(
        `[endInstallation] installation ended - id: ${bdcPackageInstallation.installationId}, is failed: ${isInstallationFailed}`,
        { context }
      );
      sendInstallationMetrics({
        context,
        bdcPackageInstallation,
        isInstallationFailed,
        isUpdating,
      });
    }
  }

  /**
   * Retry the installation of the failed package
   * @throws
   * @param context
   * @param installationId the failed installation id stored in the catalog
   */
  static async retryPackage(
    context: BDCRequestContext,
    installationId: string
  ): Promise<ExistingInstallationOperaitonResult> {
    const operationResult: ExistingInstallationOperaitonResult = { installationId };
    const currentPackageInstallation = await this.getCurrentPackageInstallation(installationId, context);
    const allowed = await allowedToInstall(context, currentPackageInstallation.installationProperties);
    if (!allowed) {
      operationResult.missingEntitlementDetails = await getMissingEntitlementDetails(
        context,
        currentPackageInstallation.installationProperties
      );
      return operationResult;
    }

    const retryFeatureFlag = "DWCO_BDC_MANUAL_ONBOARDING_RETRY";

    if (!(await context.isFeatureFlagActive(retryFeatureFlag))) {
      throw createFeatureFlagOffException(context, retryFeatureFlag);
    }

    logInfo(`retry package installation, installationId:${installationId}`, {
      context,
      fields: {
        BDC_field_installationID: installationId,
        BDC_field_FLOW: "BDCC_Flow_retry",
      },
    });

    const connectionCheckResult = await SystemsService.checkFosConnection({
      context,
      installationProps: currentPackageInstallation.installationProperties,
    });
    if (connectionCheckResult) {
      operationResult.connectionCheckResult = connectionCheckResult;
      if (connectionCheckResult.success === false) {
        throw new Error(connectionCheckResult.errorMessage);
      }
    }

    const isSuccessfullyInstalled = currentPackageInstallation.componentsInstallationStatus.every(
      (status) => status.status === "DONE" || status.status === "NOT ACTIVATED"
    );

    if (isSuccessfullyInstalled) {
      const errorMessage = `Attempted to retry package installation for an already successfully installed package. Installation ID: ${installationId}`;
      logError(errorMessage, { context });
      throw createRetryInstallationException(context, installationId, errorMessage);
    }

    const currentFailedPackageInstallation = currentPackageInstallation.originalPackage;
    await this.doDeletePackageInstallation(installationId, context);
    await PackageInstallationValidator.validatePackageReinstallRequest(
      context,
      currentFailedPackageInstallation,
      currentPackageInstallation.installationProperties
    );

    const newPackageReInstallation = InstallationManager.generateBDCPackageReInstallation(
      context,
      currentFailedPackageInstallation,
      currentPackageInstallation.installationProperties,
      installationId
    );

    await notificationsHelper.sendOnboardStartedNotification({
      context,
      packageInfo: newPackageReInstallation.originalPackage,
    });

    operationResult.installationId = await InstallationManager.doInstallPackage(context, newPackageReInstallation);
    return operationResult;
  }

  private static async doDeletePackageInstallation(installationId: string, context: BDCRequestContext) {
    await PackageInstallationService.deletePackageInstallation(installationId, context);
  }

  private static async getCurrentPackageInstallation(
    installationId: string,
    context: BDCRequestContext
  ): Promise<BDCPackageInstallation> {
    return await PackageInstallationService.getPackageInstallation(context, installationId);
  }

  /**
   * Update installed package with new not major version
   * @param context
   * @param installationId installation id stored in the catalog
   */
  static async updatePackage(
    context: BDCRequestContext,
    installationId: string
  ): Promise<ExistingInstallationOperaitonResult> {
    const operationResult: ExistingInstallationOperaitonResult = { installationId };
    const currentPackageInstallation = await this.getCurrentPackageInstallation(installationId, context);
    const allowed = await allowedToInstall(context, currentPackageInstallation.installationProperties);
    if (!allowed) {
      operationResult.missingEntitlementDetails = await getMissingEntitlementDetails(
        context,
        currentPackageInstallation.installationProperties
      );
      return operationResult;
    }

    logInfo(`Update package installationId:${installationId}`, { context });
    const currentOriginalPackage = currentPackageInstallation.originalPackage;

    const connectionCheckResult = await SystemsService.checkFosConnection({
      context,
      installationProps: currentPackageInstallation.installationProperties,
    });
    if (connectionCheckResult) {
      operationResult.connectionCheckResult = connectionCheckResult;
      if (connectionCheckResult.success === false) {
        throw new Error(connectionCheckResult.errorMessage);
      }
    }

    const newPackage = await PackagesService.getBdcPackageWithValidation(context, currentOriginalPackage.id);

    if (!InstallationManager.validateUpdateVersion(currentOriginalPackage.version, newPackage.version)) {
      throw new BDCException(
        context,
        BDCExceptionType.InvalidPackageVersion,
        `Invalid package version for update. current installed version: ${currentOriginalPackage.version},
      update new version: ${newPackage.version}`
      );
    }
    await this.doDeletePackageInstallation(installationId, context);

    await PackageInstallationValidator.validatePackageInstallationRequest(
      context,
      newPackage,
      currentPackageInstallation.installationProperties
    );

    const newPackageInstallation = InstallationManager.generateBDCPackageInstallation(
      context,
      newPackage,
      currentPackageInstallation.installationProperties,
      installationId,
      { previousOriginalPackage: currentOriginalPackage }
    );
    await notificationsHelper.sendOnboardStartedNotification({
      context,
      packageInfo: newPackage,
      isUpdating: true,
    });
    operationResult.installationId = await InstallationManager.doInstallPackage(context, newPackageInstallation);
    return operationResult;
  }

  // ////////// Private function //////////

  private static async doNextInstallationStep(
    context: BDCRequestContext,
    isInstallationFailed: boolean,
    nextInstallationStatusMessage: InstallationMessage,
    bdcPackageInstallation: BDCPackageInstallation
  ): Promise<void> {
    if (isInstallationFailed) {
      const statusInfo = bdcPackageInstallation.componentsInstallationStatus
        ? bdcPackageInstallation.componentsInstallationStatus
        : bdcPackageInstallation.componentsUninstallationStatus;
      logError(
        `Installation Failed. Components: ${nextInstallationStatusMessage.componentIds}, Tenant Id: ${
          nextInstallationStatusMessage.tenantId
        }, Installation Id: ${nextInstallationStatusMessage.installationId}, BDC Package Id: ${
          bdcPackageInstallation.originalPackage.id
        }, Status Info: ${JSON.stringify(statusInfo)}`,
        { context }
      );
      await InstallationManager.endInstallation(context, bdcPackageInstallation, isInstallationFailed);
    } else {
      if (nextInstallationStatusMessage.componentIds.length === 0) {
        logInfo(
          `Installation is finished for current level(Tenant Id: ${nextInstallationStatusMessage.tenantId}, Installation Id: ${nextInstallationStatusMessage.installationId}), starting new level.`,
          { context }
        );
        const newSetOfComponents = getNextComponents(
          bdcPackageInstallation.originalPackage.components,
          bdcPackageInstallation.componentsInstallationStatus,
          GraphAction.OnBoarding
        );

        if (newSetOfComponents.length === 0) {
          // installation finish successfully
          logInfo(
            `Installation finish successfully.Tenant Id: ${nextInstallationStatusMessage.tenantId}, Installation Id: ${nextInstallationStatusMessage.installationId}`,
            { context }
          );
          await InstallationManager.endInstallation(context, bdcPackageInstallation, isInstallationFailed);
        } else {
          newSetOfComponents.forEach((nc) => {
            nextInstallationStatusMessage.componentIds.push(nc.componentId);
          });
          await sendPackageInstallationMessage(context, nextInstallationStatusMessage);
          logInfo(
            `New Installation message created and sent (Tenant Id: ${nextInstallationStatusMessage.tenantId}, Installation Id: ${nextInstallationStatusMessage.installationId})`,
            { context }
          );
        }
      } else {
        await sendPackageInstallationMessage(context, nextInstallationStatusMessage);
        logInfo(
          `New Installation message sent (Tenant Id: ${nextInstallationStatusMessage.tenantId}, Installation Id: ${nextInstallationStatusMessage.installationId})`,
          { context }
        );
      }
    }
  }

  private static async formationRefreshIfNeeded(
    context: BDCRequestContext,
    newComponentInstallation: SingleComponentInstallation
  ) {
    logInfo(
      `Formation refresh requested for component: ${JSON.stringify(newComponentInstallation)}, New Status: ${
        newComponentInstallation.installationStatus.status
      }, Provider: ${newComponentInstallation.originalComponent.provider}`,
      { context }
    );
    if (
      newComponentInstallation.installationStatus.status === "DONE" &&
      isProviderOfSourceType(newComponentInstallation.originalComponent.provider)
    ) {
      logInfo(`Formation refresh will be performed`, { context });
      try {
        await FormationService.doFormationRefresh(
          context,
          newComponentInstallation.originalComponent,
          newComponentInstallation.installationProperties,
          newComponentInstallation.installationStatus
        );
      } catch (error) {
        logError(`[InstallationManager] doFormationRefresh failed: ${JSON.stringify(error)}`, { context });
      }
    } else {
      logInfo(
        `[InstallationManager] Formation refresh will not be performed, as status or provider are not match the flow`,
        { context }
      );
    }
  }

  private static async shouldSkipInstallation(
    context: BDCRequestContext,
    bdcPackageInstallation: BDCPackageInstallation,
    currentComponentInstallation: SingleComponentInstallation
  ): Promise<ShouldSkipInstallation> {
    // first, check minimum system version (skip data product installation if source system version is lower than min version)
    if (await PackageUtils.isPartialInstallationAllowed(context, bdcPackageInstallation.originalPackage.type)) {
      const installationPropertiesByProvider = getInstallationPropertiesByProvider(
        bdcPackageInstallation.installationProperties,
        currentComponentInstallation.originalComponent.provider
      )![0];

      const systemInfoArr = await SystemsService.getAllSystems(context);
      const selectedSourceSystem = systemInfoArr.find(
        (systemInfo) => systemInfo.applicationNamespace === installationPropertiesByProvider.applicationNamespace
      );

      const requiredProviderApps: Application[] = (
        currentComponentInstallation.originalComponent.applications || []
      ).concat(
        bdcPackageInstallation.originalPackage.requiredApplications?.find(
          (requiredProviderApp) =>
            requiredProviderApp.provider === currentComponentInstallation.originalComponent.provider
        )?.applications || []
      );

      const requiredProviderApp: Application | undefined = requiredProviderApps.find(
        (requiredProviderApp) =>
          requiredProviderApp.applicationNamespace === installationPropertiesByProvider.applicationNamespace
      );
      // HCM TODO clean up
      const isHcm = selectedSourceSystem?.applicationNamespace === "sap.sf";
      const isVersionSupported = gte(selectedSourceSystem?.applicationVersion, requiredProviderApp?.minVersion);

      if (!isHcm && !isVersionSupported) {
        return {
          shouldSkip: true,
          status: "NOT ACTIVATED",
          message: "data product requires higher version of selected source system, skipping component installation",
        };
      }
    }

    if (!InstallationManager.isUpdating(bdcPackageInstallation)) {
      return { shouldSkip: false } as ShouldSkipInstallation;
    }

    const previousInstallation = !!bdcPackageInstallation.previousPackageData!.previousOriginalPackage.components.find(
      (comp) => {
        if (comp.category === "CnPackage" && currentComponentInstallation.originalComponent.category === "CnPackage") {
          return (
            comp.provider === currentComponentInstallation.originalComponent.provider &&
            comp.name === currentComponentInstallation.originalComponent.name &&
            semver.eq(comp.version, currentComponentInstallation.originalComponent.version)
          );
        } else if (
          comp.category === "DataProduct" &&
          currentComponentInstallation.originalComponent.category === "DataProduct"
        ) {
          return (
            comp.provider === currentComponentInstallation.originalComponent.provider &&
            (comp as BDCDataProduct).ordid ===
              (currentComponentInstallation.originalComponent as BDCDataProduct).ordid &&
            semver.eq(comp.version, currentComponentInstallation.originalComponent.version)
          );
        } else {
          return false;
        }
      }
    );

    return previousInstallation
      ? {
          shouldSkip: true,
          status: "DONE",
          message: "install package update, skip component",
        }
      : ({ shouldSkip: false } as ShouldSkipInstallation);
  }

  private static addNewInstallationMessageIfNeeded(
    newSingleComponentInstallation: SingleComponentInstallation,
    nextInstallationStatusMessage: InstallationMessage
  ) {
    if (
      newSingleComponentInstallation.installationStatus.status === "EXECUTING" ||
      newSingleComponentInstallation.installationStatus.status === "ERROR" ||
      newSingleComponentInstallation.installationStatus.status === "WAITING_TO_INSTALL_READY"
    ) {
      nextInstallationStatusMessage.componentIds.push(newSingleComponentInstallation.installationStatus.componentId);
    }
  }

  /**
   * Extracts SingleComponentInstallation from BDCPackageInstallation according to component id, for the validation of installation status
   * @param messageComponentId
   * @param bdcPackageInstallation
   * @returns SingleComponentInstallation
   */
  private static getCurrentComponentInstallation(
    messageComponentId: string,
    bdcPackageInstallation: BDCPackageInstallation,
    context: BDCRequestContext
  ): SingleComponentInstallation {
    const originalComponent = bdcPackageInstallation.originalPackage.components.find(
      (component) => messageComponentId === component.componentId
    );
    if (!originalComponent) {
      const errMsg = `Component with id: ${messageComponentId} wasn't found`;
      logError(errMsg, { context });
      throw new BDCException(context, BDCExceptionType.GeneralFailure, errMsg);
    }
    const installationStatus = bdcPackageInstallation.componentsInstallationStatus.find(
      (status) => messageComponentId === status.componentId
    );
    if (!installationStatus) {
      const errMsg = `Installation Status with id: ${messageComponentId} wasn't found`;
      logError(errMsg, { context });
      throw new BDCException(context, BDCExceptionType.GeneralFailure, errMsg);
    }
    const installationProperties = bdcPackageInstallation.installationProperties;
    const componentReturn: SingleComponentInstallation = {
      originalComponent,
      installationStatus,
      installationProperties,
    };
    return componentReturn;
  }

  private static async processComponentInstallation(
    context: BDCRequestContext,
    singleComponentInstallation: SingleComponentInstallation,
    bdcPackageInstallation: BDCPackageInstallation,
    bdcPackage: BDCPackage
  ): Promise<SingleComponentInstallation> {
    const newSingleComponentInstallation: Partial<SingleComponentInstallation> = {
      originalComponent: singleComponentInstallation.originalComponent,
      installationProperties: singleComponentInstallation.installationProperties,
    };
    switch (singleComponentInstallation.installationStatus?.status) {
      case "EXECUTING": {
        try {
          newSingleComponentInstallation.installationStatus = await InstallersFacade.getInstance().getStatus(
            context,
            singleComponentInstallation.originalComponent,
            singleComponentInstallation.installationStatus,
            singleComponentInstallation.installationProperties,
            true
          );
          const logLevel = getLogFunctionForStatus(newSingleComponentInstallation.installationStatus.status);
          logLevel(
            `Executing status received for ${singleComponentInstallation.originalComponent.componentId}, status: ${newSingleComponentInstallation.installationStatus.status}.`,
            { context }
          );
        } catch (err) {
          const message =
            BDCException.BDC_EXCEPTION_NAME === err.name
              ? BDCException.addBDCCPrefixToMessage(err.message)
              : err.message;
          newSingleComponentInstallation.installationStatus = {
            ...singleComponentInstallation.installationStatus,
            message,
            status: "ERROR",
          } as ComponentInstallationStatus;
          logError(
            `Getting installation status failed. Component: ${JSON.stringify({
              provider: singleComponentInstallation.originalComponent.provider,
              id: singleComponentInstallation.originalComponent.componentId,
              retryCount: singleComponentInstallation.installationStatus.retryCount,
              message: singleComponentInstallation.installationStatus.message,
            })}.`,
            {
              context,
            }
          );
        }
        break;
      }
      case "WAITING_TO_INSTALL_READY": {
        logInfo(
          `Starting readiness test for Component: ${JSON.stringify({
            provider: singleComponentInstallation.originalComponent.provider,
            id: singleComponentInstallation.originalComponent.componentId,
            retryCount: singleComponentInstallation.installationStatus.retryCount,
          })}.`,
          {
            context,
          }
        );
        newSingleComponentInstallation.installationStatus = await InstallationManager.doInstallReadinessCheck(
          context,
          singleComponentInstallation,
          bdcPackageInstallation,
          bdcPackage
        );
        if (newSingleComponentInstallation.installationStatus.status === "EXECUTING") {
          newSingleComponentInstallation.installationStatus = await InstallationManager.startInstallation(
            singleComponentInstallation,
            context
          );
        }
        break;
      }
      case "PENDING": {
        logInfo(
          `Starting installation for component:  Component: ${JSON.stringify({
            provider: singleComponentInstallation.originalComponent.provider,
            id: singleComponentInstallation.originalComponent.componentId,
            retryCount: singleComponentInstallation.installationStatus.retryCount,
          })}.`,
          {
            context,
          }
        );
        const isDpCheckRequired = await context.isFeatureFlagActive(
          "DWCO_IA_INSTALLATION_CATALOG_DATAPRODUCT_STATUS_CHECK"
        );
        if (isDpCheckRequired) {
          newSingleComponentInstallation.installationStatus = await InstallationManager.doInstallReadinessCheck(
            context,
            singleComponentInstallation,
            bdcPackageInstallation,
            bdcPackage
          );
          if (newSingleComponentInstallation.installationStatus.status === "EXECUTING") {
            newSingleComponentInstallation.installationStatus = await InstallationManager.startInstallation(
              singleComponentInstallation,
              context
            );
          } else {
            // starting WAITING_TO_INSTALL_READY routine
            newSingleComponentInstallation.installationStatus.startTime = new Date();
            newSingleComponentInstallation.installationStatus.retryCount = 0;
          }
        } else {
          newSingleComponentInstallation.installationStatus = await InstallationManager.startInstallation(
            singleComponentInstallation,
            context
          );
        }
        break;
      }
      case "ERROR": {
        if (
          singleComponentInstallation.installationStatus.retryCount + 1 >=
          InstallationManager.MAX_NUMBER_OF_INSTALL_RETRIES
        ) {
          newSingleComponentInstallation.installationStatus =
            InstallationManager.buildNewStatusForErrorMaxRetry(singleComponentInstallation);
          logError(
            `Installation Failed. Max number of installation retries(${
              InstallationManager.MAX_NUMBER_OF_INSTALL_RETRIES
            }) has been reached. Component: ${JSON.stringify({
              provider: singleComponentInstallation.originalComponent.provider,
              id: singleComponentInstallation.originalComponent.componentId,
              retryCount: singleComponentInstallation.installationStatus.retryCount,
              message: singleComponentInstallation.installationStatus.message,
            })}.`,
            {
              context,
            }
          );
        } else {
          newSingleComponentInstallation.installationStatus = await InstallationManager.retryInstallation(
            singleComponentInstallation,
            context
          );
          logInfo(
            `Retry component installation. Component: ${JSON.stringify({
              provider: newSingleComponentInstallation.originalComponent?.provider,
              id: newSingleComponentInstallation.originalComponent?.componentId,
              retryCount: newSingleComponentInstallation.installationStatus.retryCount,
              message: newSingleComponentInstallation.installationStatus.message,
            })}.`,
            { context }
          );
        }
        break;
      }
      case "FAILED":
      case "DONE":
        newSingleComponentInstallation.installationStatus = {
          ...singleComponentInstallation.installationStatus,
          endTime: new Date(),
        };
        const logLevel = getLogFunctionForStatus(newSingleComponentInstallation.installationStatus.status);
        logLevel(
          `Component: ${singleComponentInstallation.originalComponent.componentId} status is: ${singleComponentInstallation.installationStatus?.status} no further action is needed.`,
          { context }
        );
        break;
    }
    return newSingleComponentInstallation as SingleComponentInstallation;
  }

  private static async doInstallReadinessCheck(
    context: BDCRequestContext,
    singleComponentInstallation: SingleComponentInstallation,
    bdcPackageInstallation: BDCPackageInstallation,
    bdcPackage: BDCPackage
  ): Promise<ComponentInstallationStatus> {
    let newInstallationStatus: ComponentInstallationStatus;
    try {
      const isReady = await InstallersFacade.getInstance().isReadyToInstall(
        context,
        singleComponentInstallation,
        bdcPackageInstallation,
        bdcPackage
      );
      if (isReady) {
        newInstallationStatus = {
          componentId: singleComponentInstallation.installationStatus.componentId,
          startTime: new Date(),
          status: "EXECUTING",
          message: "",
          retryCount: 0,
        } as ComponentInstallationStatus;
      } else {
        newInstallationStatus = {
          componentId: singleComponentInstallation.installationStatus.componentId,
          startTime: singleComponentInstallation.installationStatus.startTime,
          status: "WAITING_TO_INSTALL_READY",
          message: "",
          retryCount: singleComponentInstallation.installationStatus.retryCount + 1,
        } as ComponentInstallationStatus;
      }
    } catch (err) {
      let installationStatusErrorCode: InstallationStatusCode = "ERROR";
      if (err instanceof FlowTimeoutError) {
        installationStatusErrorCode = "FAILED";
      }
      newInstallationStatus = {
        ...singleComponentInstallation.installationStatus,
        message: `Exception were thrown with the message: ${err.message}`,
        status: installationStatusErrorCode,
      } as ComponentInstallationStatus;
      logError(
        `Readiness waiting failed or timeout occurred. Component: ${JSON.stringify({
          provider: singleComponentInstallation.originalComponent.provider,
          id: singleComponentInstallation.originalComponent.componentId,
          retryCount: singleComponentInstallation.installationStatus.retryCount,
          message: err.message,
        })}.`,
        {
          context,
        }
      );
    }
    return newInstallationStatus;
  }

  /**
   * Starts new installation per component
   * @param singleComponentInstallation
   * @returns ComponentInstallationStatus with status "EXECUTING" and current date as a startTime
   */
  private static async startInstallation(
    singleComponentInstallation: SingleComponentInstallation,
    context: BDCRequestContext
  ): Promise<ComponentInstallationStatus> {
    if (
      singleComponentInstallation.originalComponent.category === "CnPackage" &&
      !(await InstallationManager.isInstallACNPackage(singleComponentInstallation, context))
    ) {
      return {
        componentId: singleComponentInstallation.originalComponent.componentId,
        startTime: new Date(),
        status: "DONE",
        message: `CnPackage with version ${singleComponentInstallation.originalComponent.version} already installed`,
        retryCount: 0,
      } as ComponentInstallationStatus;
    }

    try {
      const componentInstallationDetails = await InstallersFacade.getInstance().install(
        context,
        singleComponentInstallation.originalComponent,
        singleComponentInstallation.installationProperties
      );

      return {
        componentInstallationDetails,
        componentId: singleComponentInstallation.installationStatus.componentId,
        startTime: new Date(),
        status: "EXECUTING",
        message: "",
        retryCount: 0,
      } as ComponentInstallationStatus;
    } catch (err) {
      logError(
        `Starting installation failed. Component: ${JSON.stringify({
          provider: singleComponentInstallation.originalComponent.provider,
          id: singleComponentInstallation.originalComponent.componentId,
          retryCount: singleComponentInstallation.installationStatus.retryCount,
          message: singleComponentInstallation.installationStatus.message,
        })}.`,
        {
          context,
        }
      );
      if (
        err.name === BDCException.BDC_EXCEPTION_NAME &&
        (err as BDCException).type === BDCExceptionType.ComponentInstallationRepeatRequired
      ) {
        return {
          ...singleComponentInstallation.installationStatus,
          message: err.message,
          status: "WAITING_TO_INSTALL_READY",
        } as ComponentInstallationStatus;
      }
      return {
        ...singleComponentInstallation.installationStatus,
        message:
          err.name === BDCException.BDC_EXCEPTION_NAME ? BDCException.addBDCCPrefixToMessage(err.message) : err.message,
        status: "ERROR",
      } as ComponentInstallationStatus;
    }
  }

  private static async isInstallACNPackage(
    singleComponentInstallation: SingleComponentInstallation,
    context: BDCRequestContext
  ): Promise<boolean> {
    const maxVer = await PackageInstallationService.getMaxInstalledVersionCnPackageForSrc(
      context,
      singleComponentInstallation.originalComponent,
      singleComponentInstallation.installationProperties,
      []
    );
    return !maxVer || semver.gt(singleComponentInstallation.originalComponent.version, maxVer);
  }

  /**
   *
   * @param singleComponentInstallation Retries installation for the component with at least one ERROR received
   * @returns ComponentInstallationStatus with status "EXECUTING" and retryCount incremented by 1
   */
  private static async retryInstallation(
    singleComponentInstallation: SingleComponentInstallation,
    context: BDCRequestContext
  ): Promise<ComponentInstallationStatus> {
    try {
      const newInstallationUuid = await InstallersFacade.getInstance().install(
        context,
        singleComponentInstallation.originalComponent,
        singleComponentInstallation.installationProperties
      );
      const newComponentInstallationStatus: Partial<ComponentInstallationStatus> = {
        componentInstallationDetails: newInstallationUuid,
        componentId: singleComponentInstallation.installationStatus.componentId,
        startTime: singleComponentInstallation.installationStatus.startTime,
        status: "EXECUTING",
        retryCount: singleComponentInstallation.installationStatus.retryCount + 1,
        message: "Installation retry",
      };
      return newComponentInstallationStatus as ComponentInstallationStatus;
    } catch (error) {
      logError(`Retry installation failed. Error: ${error}`, { context });
      let newStatus: InstallationStatusCode = "ERROR";
      let newRetryCount = singleComponentInstallation.installationStatus.retryCount + 1;
      if (
        error.name === BDCException.BDC_EXCEPTION_NAME &&
        (error as BDCException).type === BDCExceptionType.ComponentInstallationRepeatRequired
      ) {
        newStatus = "WAITING_TO_INSTALL_READY";
        newRetryCount = singleComponentInstallation.installationStatus.retryCount;
      }
      const newErrorComponentInstallationStatus: Partial<ComponentInstallationStatus> = {
        componentId: singleComponentInstallation.installationStatus.componentId,
        startTime: singleComponentInstallation.installationStatus.startTime,
        status: newStatus,
        message:
          error.name === BDCException.BDC_EXCEPTION_NAME
            ? BDCException.addBDCCPrefixToMessage(error.message)
            : error.message,
        retryCount: newRetryCount,
      };
      return newErrorComponentInstallationStatus as ComponentInstallationStatus;
    }
  }

  /**
   * Builds new Status for component that failed to be installed after maximum amount of retries
   * @param singleComponentInstallation
   * @returns ComponentInstallationStatus with status "FAILED", current date as an endTime and correspondent message
   */
  private static buildNewStatusForErrorMaxRetry(
    singleComponentInstallation: SingleComponentInstallation
  ): ComponentInstallationStatus {
    return {
      componentInstallationDetails: singleComponentInstallation.installationStatus.componentInstallationDetails,
      componentId: singleComponentInstallation.installationStatus.componentId,
      startTime: singleComponentInstallation.installationStatus.startTime,
      endTime: new Date(),
      status: "FAILED",
      message: BDCException.addBDCCPrefixToMessage(singleComponentInstallation.installationStatus.message),
      retryCount: singleComponentInstallation.installationStatus.retryCount,
    };
  }

  private static updateBDCPackageInstallationWithComponentStatus(
    singleComponentInstallation: SingleComponentInstallation,
    bdcPackageInstallation: BDCPackageInstallation
  ) {
    // replace InstallationStatus
    const statusIndex = bdcPackageInstallation.componentsInstallationStatus.findIndex(
      (arrStatus) => singleComponentInstallation.installationStatus.componentId === arrStatus.componentId
    );
    if (statusIndex !== -1) {
      bdcPackageInstallation.componentsInstallationStatus[statusIndex] = singleComponentInstallation.installationStatus;
    } else {
      throw new BDCException(
        null,
        BDCExceptionType.InstallationFailure,
        `Component installation status for component id: "${singleComponentInstallation.installationStatus.componentId}" not found`
      );
    }
  }

  private static overrideStatusOnCancellation(
    context: BDCRequestContext,
    component: SingleComponentInstallation,
    installation: BDCPackageInstallation
  ): void {
    if (component.installationStatus.status === "DONE" && installation?.cancellationRequested === true) {
      component.installationStatus.status = "FAILED";
      logWarning(
        `Installation was canceled; setting status to FAILED for component: ${component.originalComponent.componentId}`,
        { context }
      );
    }
  }

  private static async validateAndInstall(
    context: BDCRequestContext,
    installationProperties: BDCPackageInstallationProperties[],
    bdcPackage: BDCPackage
  ): Promise<string> {
    await PackageInstallationValidator.validatePackageInstallationRequest(context, bdcPackage, installationProperties);
    const bdcPackageInstallation = InstallationManager.generateBDCPackageInstallation(
      context,
      bdcPackage,
      installationProperties
    );
    return await InstallationManager.doInstallPackage(context, bdcPackageInstallation);
  }

  private static async doInstallPackage(
    context: BDCRequestContext,
    bdcPackageInstallation: BDCPackageInstallation
  ): Promise<string> {
    await PackageInstallationService.createPackageInstallation(bdcPackageInstallation, context);

    const initialComponentsToInstall = getNextComponents(
      bdcPackageInstallation.originalPackage.components,
      bdcPackageInstallation.componentsInstallationStatus,
      GraphAction.OnBoarding
    );
    const initialInstallationMessage: InstallationMessage = {
      installationCyclesCount: 0,
      installationId: bdcPackageInstallation.installationId,
      componentIds: initialComponentsToInstall.map((comp) => comp.componentId),
      action: MessageAction.INSTALL,
      correlationId: context.correlationId || "",
      tenantId: context.tenantId!,
      userId: context.userInfo.userId!,
      userName: context.userInfo.userName!,
      traceId: context.spanContext?.traceId ?? "",
    };
    await sendPackageInstallationMessage(context, initialInstallationMessage);
    return bdcPackageInstallation.installationId;
  }

  private static validateUpdateVersion(currentVersion: string, updateVersion: string): boolean {
    const versionsDiff = semver.diff(currentVersion, updateVersion);
    return versionsDiff !== null && versionsDiff !== "major" && versionsDiff !== "premajor";
  }

  private static generateBDCPackageReInstallation(
    context: BDCRequestContext,
    originalPackage: BDCPackage,
    installationProperties: BDCPackageInstallationProperties[],
    installationId?: string
  ): BDCPackageInstallation {
    return {
      tenantId: context.tenantId!,
      originalPackage,
      installationProperties,
      correlationId: context.correlationId || uuidv4(), // New correlationId for the new installation
      startTime: new Date(),
      lastModifiedTime: new Date(),
      installationId, // keep the same installationId
      installationCyclesCount: -1,
      componentsInstallationStatus: originalPackage.components.map(
        (component) =>
          ({
            componentId: component.componentId,
            status: "PENDING",
            retryCount: 0,
          } as ComponentInstallationStatus)
      ),
    } as BDCPackageInstallation;
  }

  private static generateBDCPackageInstallation(
    context: BDCRequestContext,
    originalPackage: BDCPackage,
    installationProperties: BDCPackageInstallationProperties[],
    installationId?: string,
    previousPackageData?: BDCPreviousPackageData
  ): BDCPackageInstallation {
    originalPackage.components.forEach((component) => (component.componentId = uuidv4()));
    return {
      tenantId: context.tenantId!,
      originalPackage,
      previousPackageData,
      installationProperties,
      correlationId: context.correlationId!,
      startTime: new Date(),
      installationId: installationId ? installationId : uuidv4(),
      installationCyclesCount: -1,
      componentsInstallationStatus: originalPackage.components.map(
        (component) =>
          ({
            componentId: component.componentId,
            status: "PENDING",
            retryCount: 0,
          } as ComponentInstallationStatus)
      ),
    } as BDCPackageInstallation;
  }

  private static isUpdating(bdcPackageInstallation: BDCPackageInstallation): boolean {
    return !!bdcPackageInstallation.previousPackageData?.previousOriginalPackage;
  }
}
