/** @format */

import { DepGraph } from "dependency-graph";
import { BDCComponent, BDCDataProduct } from "../../../shared/bdccockpit/Types";
import {
  ComponentInstallationStatus,
  ComponentsWithInstallationStatus,
  InstallationStatusCode,
} from "../packageInstallation/packageInstallationTypes";
import {
  BDCContentPackage,
  CNPackageRequiry,
  ComponentRequiry,
  ComponentWithRequiry,
  DataProductRequiry,
} from "../packages/packagesTypes";

export function isCNPackage(component: BDCComponent): component is BDCContentPackage {
  return component.category === "CnPackage";
}

export function isDataProduct(component: BDCComponent): component is BDCDataProduct {
  return component.category === "DataProduct";
}

export function isCNPackageRequiry(requiry: ComponentRequiry): requiry is CNPackageRequiry {
  return requiry.category === "CnPackage";
}

export function isDataProductRequiry(requiry: ComponentRequiry): requiry is DataProductRequiry {
  return requiry.category === "DataProduct";
}

export function isComponentWithRequiry(component: BDCComponent): component is ComponentWithRequiry<ComponentRequiry> {
  return isCNPackage(component) || isDataProduct(component);
}

export function consolidateComponentsInstallationStatus(
  cmpArr: BDCComponent[],
  cisArr: ComponentInstallationStatus[]
): ComponentsWithInstallationStatus[] {
  const componentsWithInstallationStatus: ComponentsWithInstallationStatus[] = [];

  for (const component of cmpArr) {
    const componentInstallationStatus = cisArr.find((cis) => cis.componentId === component.componentId);
    componentsWithInstallationStatus.push({
      ...component,
      ...componentInstallationStatus,
    } as ComponentsWithInstallationStatus);
  }

  return componentsWithInstallationStatus;
}

function buildComponentsDependencyGraph(
  componentsWithInstallationStatus: ComponentsWithInstallationStatus[]
): DepGraph<BDCComponent> {
  const depGraph = new DepGraph<BDCComponent>();

  for (const component of componentsWithInstallationStatus) {
    if (component.status !== "DONE" && component.status !== "NOT ACTIVATED") {
      // components in "DONE" status should not be part of the dependency graph
      depGraph.addNode(component.componentId, component);
    }
  }

  for (const component of componentsWithInstallationStatus) {
    if (isComponentWithRequiry(component)) {
      // DataProduct has no 'requires' property (no dependencies)
      component.requires?.forEach((requiry) => {
        let dependency: ComponentInstallationStatus | undefined;
        if (isCNPackageRequiry(requiry)) {
          dependency = componentsWithInstallationStatus.find(
            (component) => isCNPackage(component) && component.name === requiry.name
          );
        } else if (isDataProductRequiry(requiry)) {
          dependency = componentsWithInstallationStatus.find(
            (component) => isDataProduct(component) && component.ordid === requiry.ordid
          );
        }

        if (!dependency) {
          throw new Error(`Failed to build dependency graph. Component not found.`);
        }

        if (
          dependency.status !== "DONE" &&
          dependency.status !== "NOT ACTIVATED" &&
          component.status !== "DONE" &&
          component.status !== "NOT ACTIVATED"
        ) {
          // dependency to component with "DONE" status should not be part of the dependency graph
          depGraph.addDependency(component.componentId, dependency.componentId);
        }
      });
    }
  }

  return depGraph;
}

export function hasComponentWithStatus(
  componentsInstallationStatus: ComponentInstallationStatus[],
  installationStatus: InstallationStatusCode
): boolean {
  return componentsInstallationStatus.some(
    (componentInstallationStatus) => componentInstallationStatus.status === installationStatus
  );
}

export enum GraphAction {
  OnBoarding,
  OffBoarding,
}

/**
 * This function builds a dependency graph based on component installation requirements.
 *
 * @argument components list of components as defined in BDCPackageInstallation
 *
 * @argument componentsInstallationStatus list of component installation status corresponding to the components list
 *
 * @return list of components which should be installed next (EXECUTING components filtered out).
 *
 * @throws Error if some of the components are in FAIL status it will throw an error.
 * */
export function getNextComponents(
  components: BDCComponent[],
  componentsInstallationStatus: ComponentInstallationStatus[],
  graphAction: GraphAction
): BDCComponent[] {
  if (graphAction !== GraphAction.OnBoarding && graphAction !== GraphAction.OffBoarding) {
    throw new Error(`Invalid graph action: ${graphAction}`);
  }

  if (hasComponentWithStatus(componentsInstallationStatus, "FAILED")) {
    const errorMessage =
      graphAction === GraphAction.OnBoarding
        ? `Failed to get next component to install. A component with 'FAILED' installation status has been detected.`
        : `Failed to get next component to uninstall. A component with 'FAILED' uninstallation status has been detected.`;

    throw new Error(errorMessage);
  }

  const componentsWithInstallationStatus = consolidateComponentsInstallationStatus(
    components,
    componentsInstallationStatus
  );
  const componentsDependencyGraph = buildComponentsDependencyGraph(componentsWithInstallationStatus);

  // Construct the overall processing order for the dependency graph. If leavesOnly is true, only nodes that do not depend on any other nodes will be returned.
  const componentIds =
    graphAction === GraphAction.OnBoarding
      ? componentsDependencyGraph.overallOrder(true)
      : componentsDependencyGraph.entryNodes();
  return componentsWithInstallationStatus.filter(
    // filtering components in "EXECUTING" status
    (component) =>
      componentIds.includes(component.componentId) &&
      component.status !== "EXECUTING" &&
      component.status !== "WAITING_TO_INSTALL_READY"
  );
}
