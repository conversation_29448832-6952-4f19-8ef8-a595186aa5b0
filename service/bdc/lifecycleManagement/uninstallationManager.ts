/** @format */

import { NotificationType } from "@sap/deepsea-types";
import { deepClone } from "@sap/seal-csn/dist/utilities";
import { BDCPackageStatus, BDCPackageType, BDCUninstallationOperation } from "../../../shared/bdccockpit/Enums";
import { BDCPackageInstallationProperties, BDCRequestContext } from "../../../shared/bdccockpit/Types";
import { IFeatureFlagsMap } from "../../featureflags/FeatureFlagProvider";
import {
  InstallationMessage,
  MessageAction,
  MessageTooOldError,
} from "../../messageQueuing/consumer/PackageInstallationConsumer";
import { RequestContext } from "../../repository/security/requestContext";
import { DeleteSpaces } from "../../routes/support/spaces/deleteSpaces";
import { getDataPackagesFolders } from "../acnProvision/acnIntegration";
import { EntitlementService } from "../entitlement/entitlementService";
import { FormationService } from "../formation/formationService";
import { InstallersFacade } from "../installers/installersFacade";
import { sendUninstallationMetrics } from "../metrics/metricsUtils";
import { PackageInstallationService } from "../packageInstallation/packageInstallationService";
import {
  BDCPackageInstallation,
  ComponentInstallationDetails,
  ComponentInstallationStatus,
  ExistingInstallationOperaitonResult,
  FosComponentInstallationDetails,
} from "../packageInstallation/packageInstallationTypes";
import {
  convertOperationToMessageAction,
  extractStatusFromPackageInstallation,
  getComponentInstallRef,
  getInstalledComponentId,
} from "../packageInstallation/packageInstallationUtils";
import { BDCPackageComponent } from "../packages/packagesTypes";
import { isProviderOfSourceType } from "../systems/applicationNamespaceUtils";
import { getRelationshipSystemTypeFromBdcProvider } from "../systems/systemUtils";
import { SystemsService } from "../systems/systemsService";
import {
  BDCException,
  BDCExceptionType,
  createFeatureFlagOffException,
  createUninstallationFailureException,
} from "../utils/BDCException";
import { getBdcLogger } from "../utils/logUtils";
import { notificationsHelper } from "../utils/notificationUtils";
import { sendPackageInstallationMessage } from "../utils/solaceUtils";
import { GraphAction, getNextComponents } from "./componentResolver";

const { logError, logInfo, logWarning, getLogFunctionForStatus } = getBdcLogger(__filename);

interface SingleComponentUninstallation {
  originalComponent: BDCPackageComponent;
  installationStatusComponent: ComponentInstallationStatus;
  uninstallationStatusComponent: ComponentInstallationStatus;
  installationProperties: BDCPackageInstallationProperties[];
}

export class UninstallationManager {
  static readonly MAX_NUMBER_OF_UNINSTALL_RETRIES: number = 3;

  static async uninstallPackage({
    context,
    packageId,
    operation,
    entitlementInvalid,
    requiredFeatureFlags = {
      [BDCUninstallationOperation.RETRY]: ["DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP"],
      [BDCUninstallationOperation.CLEANUP]: ["DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP"],
    },
  }: {
    context: BDCRequestContext;
    packageId: string;
    operation: BDCUninstallationOperation;
    entitlementInvalid?: boolean;
    requiredFeatureFlags?: { [key in BDCUninstallationOperation]?: Array<keyof IFeatureFlagsMap> };
  }): Promise<ExistingInstallationOperaitonResult> {
    // Check FF per operation
    switch (operation) {
      case BDCUninstallationOperation.RETRY:
      case BDCUninstallationOperation.CLEANUP:
        const featureFlags = [
          ...(requiredFeatureFlags[BDCUninstallationOperation.RETRY] ?? []),
          ...(requiredFeatureFlags[BDCUninstallationOperation.CLEANUP] ?? []),
        ];
        await this.checkFeatureFlags(context, featureFlags);
        break;
      case BDCUninstallationOperation.UNINSTALL:
        await this.checkFeatureFlags(context, requiredFeatureFlags[BDCUninstallationOperation.UNINSTALL] ?? []);
        break;
      default:
        logError(`Uninstallation: Invalid operation: ${operation}`, { context });
        throw createUninstallationFailureException(context, `Uninstallation error, Invalid operation: ${operation}`);
    }

    let bdcPackageInstallation: BDCPackageInstallation = await PackageInstallationService.getPackageInstallation(
      context,
      packageId
    );

    this.logComponentsStatus(context, "[uninstallPackage] Uninstallation started", bdcPackageInstallation, operation);
    const operationResult: ExistingInstallationOperaitonResult = { installationId: packageId };

    const connectionCheckResult = await SystemsService.checkFosConnection({
      context,
      installationProps: bdcPackageInstallation.installationProperties,
    });
    if (connectionCheckResult) {
      operationResult.connectionCheckResult = connectionCheckResult;
      if (connectionCheckResult.success === false) {
        throw new Error(connectionCheckResult.errorMessage);
      }
    }

    await notificationsHelper.sendOffboardStartedNotification({
      context,
      packageInstallation: bdcPackageInstallation,
      action: convertOperationToMessageAction(operation),
    });
    const message = `Start uninstallation entitlementInvalid:${entitlementInvalid}, package:${JSON.stringify(
      bdcPackageInstallation
    )}`;

    logInfo(message, { context });
    try {
      UninstallationManager.verifyUninstallation(context, bdcPackageInstallation, operation, entitlementInvalid);
    } catch (error) {
      if (entitlementInvalid) {
        logInfo(`Entitlement is invalid, skip uninstallation`, { context });
        return operationResult;
      } else {
        throw error;
      }
    }

    bdcPackageInstallation = UninstallationManager.generateBDCPackageUninstallation(
      context,
      bdcPackageInstallation,
      operation
    );
    await PackageInstallationService.updatePackageInstallation(bdcPackageInstallation, context);

    operationResult.installationId = await UninstallationManager.doUninstallPackage(
      context,
      bdcPackageInstallation,
      false,
      operation
    );

    this.logComponentsStatus(context, "[uninstallPackage] method ended", bdcPackageInstallation, operation);
    return operationResult;
  }

  private static logComponentsStatus(
    context: BDCRequestContext,
    prefix: string,
    bdcPackageInstallation: BDCPackageInstallation,
    operation: BDCUninstallationOperation
  ) {
    const componentsInstallationStatusList = bdcPackageInstallation.componentsInstallationStatus
      ? `componentsInstallationStatus:[${bdcPackageInstallation.componentsInstallationStatus
          .map((component) => `(${component.componentId}, ${component.status})`)
          .join(", ")}]`
      : "componentsInstallationStatus:[]";

    const componentsUninstallationStatusList = bdcPackageInstallation.componentsUninstallationStatus
      ? `componentsUninstallationStatus:[${bdcPackageInstallation.componentsUninstallationStatus
          .map((component) => `(${component.componentId}, ${component.status})`)
          .join(", ")}]`
      : "componentsUninstallationStatus:[]";

    logInfo(
      `${prefix}, operation(${operation}), Components:${componentsInstallationStatusList}, ${componentsUninstallationStatusList}`,
      { context }
    );
  }

  private static isUpdatingWithDelete(bdcPackageInstallation: BDCPackageInstallation): boolean {
    return !!bdcPackageInstallation.previousPackageData?.previousComponentsUninstallationStatus;
  }

  static async processUninstallationMessage(
    context: BDCRequestContext,
    currentUninstallationMessage: InstallationMessage,
    forceUninstallation: boolean
  ): Promise<void> {
    logInfo(`Processing uninstallation message: ${JSON.stringify(currentUninstallationMessage)}`, { context });

    const allPackagesWithInstalledComponents: Map<string, BDCPackageInstallation> =
      await PackageInstallationService.getAllPackageInstallationsWithComponents(context);

    const bdcPackageInstallation: BDCPackageInstallation | undefined = allPackagesWithInstalledComponents.get(
      currentUninstallationMessage.installationId
    );

    if (!bdcPackageInstallation) {
      throw new BDCException(
        context,
        BDCExceptionType.UninstallationFailure,
        `Package installation not found: ${currentUninstallationMessage.installationId}`
      );
    }

    UninstallationManager.validateBdcPackageUninstallationAndMessage(
      bdcPackageInstallation,
      currentUninstallationMessage,
      context
    );

    bdcPackageInstallation.installationCyclesCount = currentUninstallationMessage.installationCyclesCount;
    const nextUninstallationMessage: InstallationMessage = UninstallationManager.generateNextUninstallationMessage(
      currentUninstallationMessage,
      context
    );
    const isUpdatingWithDelete = UninstallationManager.isUpdatingWithDelete(bdcPackageInstallation);

    logInfo(
      `processUninstallationMessage: isUpdatingWithDelete: ${isUpdatingWithDelete}, forceUninstallation: ${forceUninstallation}`,
      { context }
    );

    let isUninstallationFailed = false;
    try {
      await SystemsService.initiateAllSystemsCache(context);
      await Promise.all(
        currentUninstallationMessage.componentIds.map(async (messageComponentId) => {
          logInfo(`Start single component validation. Id: ${messageComponentId}`, { context });
          const currentComponentUninstallation: SingleComponentUninstallation =
            UninstallationManager.getCurrentComponentUninstallation(
              messageComponentId,
              bdcPackageInstallation,
              context
            );

          let newComponentUninstallation: SingleComponentUninstallation;
          if (
            await UninstallationManager.skipUninstallation(
              context,
              allPackagesWithInstalledComponents,
              bdcPackageInstallation,
              currentComponentUninstallation,
              forceUninstallation
            )
          ) {
            const uninstallDate = new Date();
            newComponentUninstallation = {
              ...currentComponentUninstallation,
              uninstallationStatusComponent: {
                componentId: currentComponentUninstallation.originalComponent.componentId,
                componentInstallationDetails: {} as ComponentInstallationDetails,
                startTime: uninstallDate,
                endTime: uninstallDate,
                status: "DONE",
                message: "uninstall package, skip component",
                retryCount: 0,
              },
            };
          } else {
            newComponentUninstallation = await this.processComponentUninstallation(
              context,
              currentComponentUninstallation
            );

            if (newComponentUninstallation.uninstallationStatusComponent.status === "FAILED") {
              if (forceUninstallation) {
                const ignoreFailMsg = forceUninstallation ? "force uninstallation" : "updating with delete";
                logError(
                  `Uninstallation component: ${newComponentUninstallation.uninstallationStatusComponent.componentId}  failed, but ${ignoreFailMsg} is enabled.`,
                  { context }
                );
                newComponentUninstallation.uninstallationStatusComponent.status = "DONE";
                newComponentUninstallation.uninstallationStatusComponent.message = BDCException.addBDCCPrefixToMessage(
                  `Uninstallation failed, but ${ignoreFailMsg} is enabled`
                );
                //TODO send notification
              } else {
                isUninstallationFailed = true;
              }
            } else if (newComponentUninstallation.uninstallationStatusComponent.status === "DONE") {
              await UninstallationManager.formationRefreshIfNeeded(
                context,
                newComponentUninstallation,
                bdcPackageInstallation
              );
            }
          }

          UninstallationManager.updateBDCPackageUninstallationWithComponentStatus(
            newComponentUninstallation,
            bdcPackageInstallation
          );
          UninstallationManager.addNewUninstallationMessageIfNeeded(
            newComponentUninstallation,
            nextUninstallationMessage
          );
        })
      );
      const isUninstallationFishedSuccessfully = await UninstallationManager.doNextUninstallationStep(
        context,
        isUninstallationFailed,
        nextUninstallationMessage,
        bdcPackageInstallation
      );

      if (isUninstallationFishedSuccessfully) {
        logInfo(`uninstallation successfully finished: ${JSON.stringify(bdcPackageInstallation)}`, {
          context,
        });

        await PackageInstallationService.deletePackageInstallation(bdcPackageInstallation.installationId, context);

        const applicationNamespaces = bdcPackageInstallation.installationProperties.map(
          (installationProperty) => installationProperty.applicationNamespace
        );
        await EntitlementService.removeEntitlementIfNotNeeded(context, applicationNamespaces);
      } else {
        if (isUninstallationFailed) {
          logError(`Uninstallation failed: ${JSON.stringify(bdcPackageInstallation)}`, {
            context,
          });
        } else {
          logInfo(
            `Update uninstallation new status in catalog for installationId: ${currentUninstallationMessage.installationId}`,
            {
              context,
            }
          );
        }
        await PackageInstallationService.updatePackageInstallation(bdcPackageInstallation, context);
      }
    } catch (error) {
      logError([`failed to process uninstallation for component`, error], { context });
    }
  }

  private static async formationRefreshIfNeeded(
    context: BDCRequestContext,
    componentUninstallation: SingleComponentUninstallation,
    bdcPackageInstallation: BDCPackageInstallation
  ) {
    if (isProviderOfSourceType(componentUninstallation.originalComponent.provider)) {
      logInfo(
        `[UninstallationManager] Formation refresh requested for component: ${JSON.stringify(
          componentUninstallation
        )}, Package Installation: ${JSON.stringify(bdcPackageInstallation)},  New Status: ${
          componentUninstallation.uninstallationStatusComponent.status
        }, Provider: ${componentUninstallation.originalComponent.provider}`,
        { context }
      );
      logInfo(`[UninstallationManager] Formation refresh will be performed`, { context });
      try {
        const uninstallationStatusForFormationRefresh: ComponentInstallationStatus = deepClone(
          componentUninstallation.uninstallationStatusComponent
        );
        const s4CurrentInstallationStatus = bdcPackageInstallation?.componentsInstallationStatus.find(
          (is) => is.componentId === componentUninstallation.originalComponent.componentId
        );
        let lastInstallationModifiedStr = (
          s4CurrentInstallationStatus?.componentInstallationDetails as FosComponentInstallationDetails[]
        )[0].modifiedAt;
        if (!lastInstallationModifiedStr) {
          lastInstallationModifiedStr = (
            s4CurrentInstallationStatus?.componentInstallationDetails as FosComponentInstallationDetails[]
          )[0]?.createdAt;
        }
        if (!lastInstallationModifiedStr) {
          // Should not happen - the createdAt and modifiedAt we get from FOS response, it should exists in successful installation.
          // As uninstall will continue no matter formation refresh, we try to call the refresh with dates one month back
          logError(`Couldn't get modifiedAt and createdAt from installation status: ${s4CurrentInstallationStatus}`, {
            context,
          });
          const lastMonth = new Date();
          lastMonth.setMonth(new Date().getMonth() - 1);
          lastInstallationModifiedStr = lastMonth.toISOString();
        }
        const lastInstallationModifiedDate = new Date(lastInstallationModifiedStr);

        const oneSecAfterLastInstallationModifiedDate = new Date(lastInstallationModifiedDate.getTime());
        oneSecAfterLastInstallationModifiedDate.setSeconds(lastInstallationModifiedDate.getSeconds() + 1);
        // 1. start time set to date when package was last modified
        // 2. modifiedAt set to one second after
        // 3. According to formation refresher logic: start time(installation package last modified) will be used
        uninstallationStatusForFormationRefresh.startTime = lastInstallationModifiedDate;
        (
          uninstallationStatusForFormationRefresh.componentInstallationDetails as FosComponentInstallationDetails[]
        )[0].modifiedAt = oneSecAfterLastInstallationModifiedDate.toISOString();
        logInfo(
          `Starting formation refresh for uninstall. Start time: ${lastInstallationModifiedDate}, modified at: ${oneSecAfterLastInstallationModifiedDate}`,
          { context }
        );
        await FormationService.doFormationRefresh(
          context,
          componentUninstallation.originalComponent,
          componentUninstallation.installationProperties,
          uninstallationStatusForFormationRefresh
        );
      } catch (error) {
        logError(`[UninstallationManager] doFormationRefresh failed: ${JSON.stringify(error)}`, { context });
      }
    } else {
      logInfo(`[UninstallationManager] Formation refresh will not be performed, as provider is not match the flow`, {
        context,
      });
    }
  }

  private static generateNextUninstallationMessage(
    currentUninstallationMessage: InstallationMessage,
    context: BDCRequestContext
  ): InstallationMessage {
    const nextUninstallationMessage: InstallationMessage = {
      installationId: currentUninstallationMessage.installationId,
      installationCyclesCount: currentUninstallationMessage.installationCyclesCount + 1,
      componentIds: [],
      action: currentUninstallationMessage.action,
      tenantId: context.tenantId!,
      userId: context.userInfo.userId!,
      userName: context.userInfo.userName!,
      correlationId: currentUninstallationMessage.correlationId || "",
      traceId: currentUninstallationMessage.traceId ?? "",
    };
    return nextUninstallationMessage;
  }

  private static validateBdcPackageUninstallationAndMessage(
    bdcPackageInstallation: BDCPackageInstallation,
    currentUninstallationMessage: InstallationMessage,
    context: BDCRequestContext
  ) {
    if (!bdcPackageInstallation) {
      throw new Error(`package installation "${currentUninstallationMessage.installationId}" not found`);
    }
    logInfo(
      `BDC Package retrieved successfully from the catalog, installationId: ${JSON.stringify(
        currentUninstallationMessage
      )}`,
      { context }
    );

    if (currentUninstallationMessage.installationCyclesCount <= bdcPackageInstallation.installationCyclesCount) {
      const messageTooOldTxt = `CorrelationId: ${currentUninstallationMessage.correlationId}. Message count: ${currentUninstallationMessage.installationCyclesCount}, bdcPackageInstallation count: ${bdcPackageInstallation.installationCyclesCount}`;
      logError(messageTooOldTxt, { context });
      throw new MessageTooOldError(messageTooOldTxt);
    }
  }

  private static verifyUninstallation(
    context: BDCRequestContext,
    bdcPackageInstallation: BDCPackageInstallation,
    operation: BDCUninstallationOperation,
    entitlementInvalid?: boolean
  ) {
    // Note:
    //  - on cleanup the status should be installation_failed
    //  - on retry-uninstallation the status should be uninstallation_failed
    const status: BDCPackageStatus = extractStatusFromPackageInstallation(bdcPackageInstallation);
    const packageName = bdcPackageInstallation.originalPackage.name;
    const packageType = bdcPackageInstallation.originalPackage.type;

    let errMessage = "";
    switch (status) {
      case BDCPackageStatus.UNINSTALLING:
        errMessage =
          packageType === BDCPackageType.INSIGHT_APPLICATION
            ? `Insight application: ${packageName} is already in uninstalling state. Cannot uninstall.`
            : `Data Product: ${packageName} is already in deactivating state. Cannot deactivate.`;
        break;
      case BDCPackageStatus.INSTALLING:
        errMessage =
          packageType === BDCPackageType.INSIGHT_APPLICATION
            ? `Insight application: ${packageName} is in installing state. Cannot uninstall.`
            : `Data Product: ${packageName} is already in activating state. Cannot deactivate.`;
        break;
      case BDCPackageStatus.INSTALLATION_FAILED:
        if (operation !== BDCUninstallationOperation.CLEANUP) {
          errMessage =
            packageType === BDCPackageType.INSIGHT_APPLICATION
              ? `Insight application: ${packageName} installation failed. Cannot uninstall. Needs to be cleaned up.`
              : `Data Product: ${packageName} is already in activating failed state. Cannot deactivate. needs to be cleaned up.`;
        }
        break;
      case BDCPackageStatus.UNINSTALLATION_FAILED:
        if (operation !== BDCUninstallationOperation.RETRY) {
          errMessage =
            packageType === BDCPackageType.INSIGHT_APPLICATION
              ? `Insight application: ${packageName} Uninstallation failed. Can only retry uninstall.`
              : `Data Product: ${packageName} is already in deactivating failed state. Can only retry deactivate.`;
        }
        break;
    }

    if (errMessage) {
      if (entitlementInvalid) {
        logInfo(`Entitlement remove: ${errMessage}`, { context });
      } else {
        logError(errMessage, { context });
      }
      throw createUninstallationFailureException(context, errMessage);
    }
  }

  private static generateBDCPackageUninstallation(
    context: BDCRequestContext,
    bdcPackageInstallation: BDCPackageInstallation,
    operation: BDCUninstallationOperation
  ): BDCPackageInstallation {
    switch (operation) {
      case BDCUninstallationOperation.RETRY:
        if (!bdcPackageInstallation.componentsUninstallationStatus?.length) {
          const message = "Retry Uninstallation failed, no components to uninstall. Should call uninstall first";
          logError(message, { context });
          throw createUninstallationFailureException(context, message);
        }

        bdcPackageInstallation.lastModifiedTime = bdcPackageInstallation.endTime;
        bdcPackageInstallation.endTime = undefined;
        bdcPackageInstallation.componentsUninstallationStatus =
          bdcPackageInstallation.componentsUninstallationStatus.map((componentStatus) =>
            componentStatus.status !== "DONE"
              ? ({
                  componentId: componentStatus.componentId,
                  status: "PENDING",
                  retryCount: 0,
                } as ComponentInstallationStatus)
              : componentStatus
          );
        break;
      case BDCUninstallationOperation.CLEANUP:
        bdcPackageInstallation.lastModifiedTime = bdcPackageInstallation.endTime;
        bdcPackageInstallation.endTime = undefined;
      case BDCUninstallationOperation.UNINSTALL:
        bdcPackageInstallation.componentsUninstallationStatus = bdcPackageInstallation.componentsInstallationStatus.map(
          (componentStatus) =>
            ({
              componentId: componentStatus.componentId,
              status: "PENDING",
              retryCount: 0,
            } as ComponentInstallationStatus)
        );
        break;
    }

    bdcPackageInstallation.correlationId = context.correlationId || "";
    return bdcPackageInstallation;
  }

  private static async doUninstallPackage(
    context: BDCRequestContext,
    bdcPackageInstallation: BDCPackageInstallation,
    forceUninstallation: boolean,
    operation: BDCUninstallationOperation
  ): Promise<string> {
    const initialComponentsToInstall = getNextComponents(
      bdcPackageInstallation.originalPackage.components,
      bdcPackageInstallation.componentsUninstallationStatus!,
      GraphAction.OffBoarding
    );

    const messageAction = forceUninstallation
      ? MessageAction.FORCE_UNINSTALL
      : convertOperationToMessageAction(operation);
    const initialUninstallationMessage: InstallationMessage = {
      installationCyclesCount: bdcPackageInstallation.installationCyclesCount + 1,
      installationId: bdcPackageInstallation.installationId,
      componentIds: initialComponentsToInstall.map((comp) => comp.componentId),
      action: messageAction,
      correlationId: context.correlationId || "",
      tenantId: context.tenantId!,
      userId: context.userInfo.userId!,
      userName: context.userInfo.userName!,
      traceId: context.spanContext?.traceId ?? "",
    };
    await sendPackageInstallationMessage(context, initialUninstallationMessage);
    return bdcPackageInstallation.installationId;
  }

  public static async skipUninstallation(
    context: BDCRequestContext,
    allPackagesWithInstalledComponents: Map<string, BDCPackageInstallation>,
    bdcPackageInstallation: BDCPackageInstallation,
    currentComponentUninstallation: SingleComponentUninstallation,
    forceUninstallation: boolean
  ): Promise<boolean> {
    const currentComponentInstallationStatus = currentComponentUninstallation.installationStatusComponent.status;
    if (
      currentComponentInstallationStatus === "NOT ACTIVATED" ||
      currentComponentInstallationStatus === "PENDING" ||
      (!forceUninstallation && currentComponentInstallationStatus !== "DONE")
    ) {
      logInfo(
        `Component ${currentComponentUninstallation.originalComponent.componentId} install status: ${currentComponentInstallationStatus}, forceUninstallation:${forceUninstallation}, skipping uninstallation.`,
        { context }
      );
      return true;
    }

    return !(await UninstallationManager.isComponentAllowForDelete(
      context,
      allPackagesWithInstalledComponents,
      bdcPackageInstallation,
      currentComponentUninstallation,
      forceUninstallation
    ));
  }

  private static async isComponentAllowForDelete(
    context: BDCRequestContext,
    allPackagesWithInstalledComponents: Map<string, BDCPackageInstallation>,
    bdcPackageInstallation: BDCPackageInstallation,
    currentComponentUninstallation: SingleComponentUninstallation,
    forceUninstallation: boolean
  ): Promise<boolean> {
    const componentId = getInstalledComponentId(currentComponentUninstallation.originalComponent);
    const systemTypeRel = getRelationshipSystemTypeFromBdcProvider(
      currentComponentUninstallation.originalComponent.provider
    );

    const installToSrcRefArr = getComponentInstallRef(
      context,
      allPackagesWithInstalledComponents,
      componentId,
      systemTypeRel
    );

    const currentComponent = installToSrcRefArr.find((ref) => ref.installId === bdcPackageInstallation.installationId);

    logInfo(
      `isComponentAllowForDelete forceUninstallation:${forceUninstallation}, component ${componentId}, installToSrcRefArr: ${JSON.stringify(
        installToSrcRefArr
      )}, currentComponent: ${JSON.stringify(currentComponent)}`,
      { context }
    );

    if (forceUninstallation) {
      if (installToSrcRefArr.length === 0) {
        logInfo(
          `isComponentAllowForDelete forceUninstallation:${forceUninstallation}, no component references
        `,
          { context }
        );
        return true;
      } else if (!currentComponent) {
        logInfo(
          `isComponentAllowForDelete forceUninstallation:${forceUninstallation}, there are other references, but not the current installation
          `,
          { context }
        );
        return false;
      }
    } else {
      if (installToSrcRefArr.length === 0 || !currentComponent) {
        throw new BDCException(
          context,
          BDCExceptionType.UninstallationFailure,
          `isComponentAllowForDelete forceUninstallation:${forceUninstallation}, system ref not found for install id: ${bdcPackageInstallation.installationId}, component id: ${componentId}`
        );
      }
    }

    let componentAllowForDelete = true;
    for (const ref of installToSrcRefArr) {
      if (ref.installId !== currentComponent.installId && ref.refId === currentComponent.refId) {
        // there is other installation with the same refId

        const otherPackageInstallation: BDCPackageInstallation | undefined = allPackagesWithInstalledComponents.get(
          ref.installId
        );

        if (!otherPackageInstallation) {
          throw new BDCException(
            context,
            BDCExceptionType.UninstallationFailure,
            `Other package installation not found: ${ref.installId}`
          );
        }

        const status: BDCPackageStatus = extractStatusFromPackageInstallation(otherPackageInstallation);
        if (status === BDCPackageStatus.INSTALLING || status === BDCPackageStatus.INSTALLED) {
          componentAllowForDelete = false;
          logInfo(
            `isComponentAllowForDelete forceUninstallation:${forceUninstallation}, not allowed to delete currentComponent ${JSON.stringify(
              currentComponent
            )}, other installation: ${JSON.stringify(ref)}`,
            { context }
          );
          break;
        }
      }
    }

    logInfo(
      `isComponentAllowForDelete forceUninstallation:${forceUninstallation},  allowed to delete currentComponent ${JSON.stringify(
        currentComponent
      )}, return ${componentAllowForDelete}`,
      { context }
    );
    return componentAllowForDelete;
  }

  private static getCurrentComponentUninstallation(
    messageComponentId: string,
    bdcPackageInstallation: BDCPackageInstallation,
    context: BDCRequestContext
  ): SingleComponentUninstallation {
    const originalPackage = bdcPackageInstallation.originalPackage;
    const componentsInstallationStatus = bdcPackageInstallation.componentsInstallationStatus;
    const componentsUninstallationStatus = bdcPackageInstallation.componentsUninstallationStatus!;

    const originalComponent = originalPackage.components.find(
      (component) => messageComponentId === component.componentId
    );
    if (!originalComponent) {
      const errMsg = `Component with id: ${messageComponentId} wasn't found`;
      logError(errMsg, { context });
      throw new BDCException(context, BDCExceptionType.UninstallationFailure, errMsg);
    }

    const installationStatusComponent = componentsInstallationStatus.find(
      (component) => messageComponentId === component.componentId
    );
    if (!installationStatusComponent) {
      const errMsg = `Installation Status with id: ${messageComponentId} wasn't found`;
      logError(errMsg, { context });
      throw new BDCException(context, BDCExceptionType.UninstallationFailure, errMsg);
    }

    const uninstallationStatusComponent = componentsUninstallationStatus?.find(
      (component) => messageComponentId === component.componentId
    );
    if (!uninstallationStatusComponent) {
      const errMsg = `Uninstallation Status with id: ${messageComponentId} wasn't found`;
      logError(errMsg, { context });
      throw new BDCException(context, BDCExceptionType.UninstallationFailure, errMsg);
    }

    const installationProperties = bdcPackageInstallation.installationProperties;
    const componentReturn: SingleComponentUninstallation = {
      originalComponent,
      installationStatusComponent,
      uninstallationStatusComponent,
      installationProperties,
    };
    return componentReturn;
  }

  private static async processComponentUninstallation(
    context: BDCRequestContext,
    singleComponentUninstallation: SingleComponentUninstallation
  ): Promise<SingleComponentUninstallation> {
    const newSingleComponentUninstallation: Partial<SingleComponentUninstallation> = {
      originalComponent: singleComponentUninstallation.originalComponent,
      installationProperties: singleComponentUninstallation.installationProperties,
    };

    switch (singleComponentUninstallation.uninstallationStatusComponent.status) {
      case "EXECUTING": {
        try {
          newSingleComponentUninstallation.uninstallationStatusComponent =
            await InstallersFacade.getInstance().getStatus(
              context,
              singleComponentUninstallation.originalComponent,
              singleComponentUninstallation.uninstallationStatusComponent,
              singleComponentUninstallation.installationProperties,
              false
            );
          const logLevelForStatus = getLogFunctionForStatus(
            newSingleComponentUninstallation.uninstallationStatusComponent.status
          );
          logLevelForStatus(
            `Uninstall executing status received for ${singleComponentUninstallation.originalComponent.componentId}, status: ${newSingleComponentUninstallation.uninstallationStatusComponent.status}.`,
            { context }
          );
        } catch (err) {
          newSingleComponentUninstallation.uninstallationStatusComponent = {
            ...singleComponentUninstallation.uninstallationStatusComponent,
            message:
              err.name === BDCException.BDC_EXCEPTION_NAME
                ? BDCException.addBDCCPrefixToMessage(err.message)
                : err.message,
            status: "ERROR",
          } as ComponentInstallationStatus;
          logError(
            `Getting uninstallation status failed. Component: ${JSON.stringify({
              provider: singleComponentUninstallation.originalComponent.provider,
              id: singleComponentUninstallation.originalComponent.componentId,
              retryCount: singleComponentUninstallation.uninstallationStatusComponent.retryCount,
              message: singleComponentUninstallation.uninstallationStatusComponent.message,
            })}.`,
            {
              context,
            }
          );
        }
        break;
      }
      case "PENDING": {
        logInfo(
          `Starting uninstallation for component:  Component: ${JSON.stringify({
            provider: singleComponentUninstallation.originalComponent.provider,
            id: singleComponentUninstallation.originalComponent.componentId,
            retryCount: singleComponentUninstallation.uninstallationStatusComponent.retryCount,
          })}.`,
          {
            context,
          }
        );
        try {
          newSingleComponentUninstallation.uninstallationStatusComponent =
            await UninstallationManager.startUninstallation(singleComponentUninstallation, context);
        } catch (err) {
          newSingleComponentUninstallation.uninstallationStatusComponent = {
            ...singleComponentUninstallation.uninstallationStatusComponent,
            message:
              err.name === BDCException.BDC_EXCEPTION_NAME
                ? BDCException.addBDCCPrefixToMessage(err.message)
                : err.message,
            status: "ERROR",
          } as ComponentInstallationStatus;
          logError(
            `Starting uninstallation failed. Component: ${JSON.stringify({
              provider: singleComponentUninstallation.originalComponent.provider,
              id: singleComponentUninstallation.originalComponent.componentId,
              retryCount: singleComponentUninstallation.uninstallationStatusComponent.retryCount,
              message: singleComponentUninstallation.uninstallationStatusComponent.message,
            })}.`,
            {
              context,
            }
          );
        }
        break;
      }
      case "ERROR": {
        if (
          singleComponentUninstallation.uninstallationStatusComponent.retryCount + 1 >=
          UninstallationManager.MAX_NUMBER_OF_UNINSTALL_RETRIES
        ) {
          newSingleComponentUninstallation.uninstallationStatusComponent =
            UninstallationManager.buildNewStatusForErrorMaxRetry(singleComponentUninstallation);
          logError(
            `Uninstallation Failed. Max number of uninstallation retries has been reached. Component: ${JSON.stringify({
              provider: singleComponentUninstallation.originalComponent.provider,
              id: singleComponentUninstallation.originalComponent.componentId,
              retryCount: singleComponentUninstallation.uninstallationStatusComponent.retryCount,
              message: singleComponentUninstallation.uninstallationStatusComponent.message,
            })}.`,
            {
              context,
            }
          );
        } else {
          newSingleComponentUninstallation.uninstallationStatusComponent =
            await UninstallationManager.retryUninstallation(singleComponentUninstallation, context);
          logInfo(
            `Retry component uninstallation. Component: ${JSON.stringify({
              provider: newSingleComponentUninstallation.originalComponent?.provider,
              id: newSingleComponentUninstallation.originalComponent?.componentId,
              retryCount: newSingleComponentUninstallation.uninstallationStatusComponent?.retryCount,
              message: newSingleComponentUninstallation.uninstallationStatusComponent?.message,
            })}.`,
            { context }
          );
        }
        break;
      }
      case "FAILED":
      case "DONE":
        newSingleComponentUninstallation.uninstallationStatusComponent = {
          ...singleComponentUninstallation.uninstallationStatusComponent,
          endTime: new Date(),
        };
        const logLevelForStatus = getLogFunctionForStatus(
          newSingleComponentUninstallation.uninstallationStatusComponent.status
        );
        logLevelForStatus(
          `Uninstall component: ${singleComponentUninstallation.originalComponent.componentId} status is: ${singleComponentUninstallation.uninstallationStatusComponent?.status} no further action is needed.`,
          { context }
        );
        break;
    }
    return newSingleComponentUninstallation as SingleComponentUninstallation;
  }

  private static async startUninstallation(
    singleComponentUninstallation: SingleComponentUninstallation,
    context: BDCRequestContext
  ): Promise<ComponentInstallationStatus> {
    const componentInstallationDetails = await InstallersFacade.getInstance().uninstall(
      context,
      singleComponentUninstallation.originalComponent,
      singleComponentUninstallation.installationProperties
    );

    return {
      componentInstallationDetails,
      componentId: singleComponentUninstallation.uninstallationStatusComponent.componentId,
      startTime: new Date(),
      status: "EXECUTING",
      message: "",
      retryCount: 0,
    } as ComponentInstallationStatus;
  }

  /**
   *
   * @param singleComponentInstallation Retries uninstallation for the component with at least one ERROR received
   * @returns ComponentInstallationStatus with status "EXECUTING" and retryCount incremented by 1
   */
  private static async retryUninstallation(
    singleComponentUninstallation: SingleComponentUninstallation,
    context: BDCRequestContext
  ): Promise<ComponentInstallationStatus> {
    try {
      const newUninstallationUuid = await InstallersFacade.getInstance().uninstall(
        context,
        singleComponentUninstallation.originalComponent,
        singleComponentUninstallation.installationProperties
      );
      const newComponentUninstallationStatus: Partial<ComponentInstallationStatus> = {
        componentInstallationDetails: newUninstallationUuid,
        componentId: singleComponentUninstallation.uninstallationStatusComponent.componentId,
        startTime: singleComponentUninstallation.uninstallationStatusComponent.startTime,
        status: "EXECUTING",
        retryCount: singleComponentUninstallation.uninstallationStatusComponent.retryCount + 1,
      };
      return newComponentUninstallationStatus as ComponentInstallationStatus;
    } catch (error) {
      logError(`Retry uninstallation failed. Error: ${error}`, { context });
      const newErrorComponentInstallationStatus: Partial<ComponentInstallationStatus> = {
        componentId: singleComponentUninstallation.uninstallationStatusComponent.componentId,
        startTime: singleComponentUninstallation.uninstallationStatusComponent.startTime,
        status: "ERROR",
        message:
          error.name === BDCException.BDC_EXCEPTION_NAME
            ? BDCException.addBDCCPrefixToMessage(error.message)
            : error.message,
        retryCount: singleComponentUninstallation.uninstallationStatusComponent.retryCount + 1,
      };
      return newErrorComponentInstallationStatus as ComponentInstallationStatus;
    }
  }

  /**
   * Builds new Status for component that failed to be installed after maximum amount of retries
   * @param singleComponentInstallation
   * @returns ComponentInstallationStatus with status "FAILED", current date as an endTime and correspondent message
   */
  private static buildNewStatusForErrorMaxRetry(
    singleComponentUninstallation: SingleComponentUninstallation
  ): ComponentInstallationStatus {
    return {
      componentInstallationDetails:
        singleComponentUninstallation.uninstallationStatusComponent.componentInstallationDetails,
      componentId: singleComponentUninstallation.uninstallationStatusComponent.componentId,
      startTime: singleComponentUninstallation.uninstallationStatusComponent.startTime,
      endTime: new Date(),
      status: "FAILED",
      message: BDCException.addBDCCPrefixToMessage(
        `Uninstallation failed after ${UninstallationManager.MAX_NUMBER_OF_UNINSTALL_RETRIES} retries. Original message: "${singleComponentUninstallation.uninstallationStatusComponent.message}"`
      ),
      retryCount: singleComponentUninstallation.uninstallationStatusComponent.retryCount,
    };
  }

  private static updateBDCPackageUninstallationWithComponentStatus(
    singleComponentUninstallation: SingleComponentUninstallation,
    bdcPackageInstallation: BDCPackageInstallation
  ) {
    const componentsUninstallationStatus = bdcPackageInstallation.componentsUninstallationStatus;
    const statusIndex = componentsUninstallationStatus?.findIndex(
      (arrStatus) => singleComponentUninstallation.uninstallationStatusComponent.componentId === arrStatus.componentId
    );
    if (statusIndex !== undefined && statusIndex !== -1) {
      componentsUninstallationStatus![statusIndex] = singleComponentUninstallation.uninstallationStatusComponent;
    } else {
      throw new BDCException(
        null,
        BDCExceptionType.UninstallationFailure,
        `Component uninstallation status for component id: "${singleComponentUninstallation.uninstallationStatusComponent.componentId}" not found`
      );
    }
  }

  private static addNewUninstallationMessageIfNeeded(
    newSingleComponentUninstallation: SingleComponentUninstallation,
    nextUninstallationStatusMessage: InstallationMessage
  ) {
    if (
      newSingleComponentUninstallation.uninstallationStatusComponent.status === "EXECUTING" ||
      newSingleComponentUninstallation.uninstallationStatusComponent.status === "ERROR"
    ) {
      nextUninstallationStatusMessage.componentIds.push(
        newSingleComponentUninstallation.uninstallationStatusComponent.componentId
      );
    }
  }

  // return true only if uninstallation is finished successfully
  private static async doNextUninstallationStep(
    context: BDCRequestContext,
    isUninstallationFailed: boolean,
    nextUninstallationStatusMessage: InstallationMessage,
    bdcPackageInstallation: BDCPackageInstallation
  ): Promise<boolean> {
    let isUninstallationFishedSuccessfully = false;
    if (isUninstallationFailed) {
      logError(
        `Uninstallation Failed. Tenant Id: ${nextUninstallationStatusMessage.tenantId}, Installation Id: ${nextUninstallationStatusMessage.installationId}`,
        { context }
      );
      await UninstallationManager.endUninstallation(
        context,
        bdcPackageInstallation,
        isUninstallationFailed,
        nextUninstallationStatusMessage.action
      );
    } else {
      if (nextUninstallationStatusMessage.componentIds.length === 0) {
        logInfo(
          `Uninstallation is finished for current level(Tenant Id: ${nextUninstallationStatusMessage.tenantId}, Installation Id: ${nextUninstallationStatusMessage.installationId}), starting new level.`,
          { context }
        );
        const newSetOfComponents = getNextComponents(
          bdcPackageInstallation.originalPackage.components,
          bdcPackageInstallation.componentsUninstallationStatus!,
          GraphAction.OffBoarding
        );
        if (newSetOfComponents.length === 0) {
          logInfo(
            `Uninstallation finished successfully.Tenant Id: ${nextUninstallationStatusMessage.tenantId}, Installation Id: ${nextUninstallationStatusMessage.installationId}`,
            { context }
          );
          await UninstallationManager.endUninstallation(
            context,
            bdcPackageInstallation,
            isUninstallationFailed,
            nextUninstallationStatusMessage.action
          );
          isUninstallationFishedSuccessfully = true;
        } else {
          newSetOfComponents.forEach((nc) => {
            nextUninstallationStatusMessage.componentIds.push(nc.componentId);
          });
          await UninstallationManager.doSendUninstallationMessage(context, nextUninstallationStatusMessage);
        }
      } else {
        await UninstallationManager.doSendUninstallationMessage(context, nextUninstallationStatusMessage);
      }
    }
    return isUninstallationFishedSuccessfully;
  }

  private static async doSendUninstallationMessage(
    context: BDCRequestContext,
    nextUninstallationStatusMessage: InstallationMessage
  ) {
    await sendPackageInstallationMessage(context, nextUninstallationStatusMessage);
    logInfo(
      `New Installation message sent (Tenant Id: ${nextUninstallationStatusMessage.tenantId}, Installation Id: ${nextUninstallationStatusMessage.installationId})`,
      { context }
    );
  }

  private static async endUninstallation(
    context: BDCRequestContext,
    bdcPackageInstallation: BDCPackageInstallation,
    isInstallationFailed: boolean,
    action: MessageAction
  ) {
    try {
      bdcPackageInstallation.endTime = new Date();
      bdcPackageInstallation.lastModifiedTime = bdcPackageInstallation.endTime;

      await PackageInstallationService.updateInstallationComponentsIds(context, bdcPackageInstallation, true);
    } catch (err) {
      logError([`failing to end uninstallation of ${bdcPackageInstallation.originalPackage.name}`, err], {
        context,
      });
    } finally {
      await notificationsHelper.sendOffboardFinishedNotification({
        context,
        notificationType: isInstallationFailed ? NotificationType.ALERT : NotificationType.SUCCESS,
        packageInstallation: bdcPackageInstallation,
        action,
      });

      logInfo(
        `Uninstallation ended - id: ${bdcPackageInstallation.installationId}, is failed: ${isInstallationFailed}`,
        { context }
      );
      sendUninstallationMetrics({
        context,
        bdcPackageInstallation,
        isInstallationFailed,
      });
    }
  }

  static async deletePackageInstallation(
    context: BDCRequestContext,
    installationId: string
  ): Promise<ExistingInstallationOperaitonResult> {
    const operationResult: ExistingInstallationOperaitonResult = { installationId };
    logInfo(`Delete package installationId:${installationId}`, { context });
    const currentPackageInstallation = await PackageInstallationService.getPackageInstallation(context, installationId);

    const connectionCheckResult = await SystemsService.checkFosConnection({
      context,
      installationProps: currentPackageInstallation.installationProperties,
    });
    if (connectionCheckResult) {
      operationResult.connectionCheckResult = connectionCheckResult;
      if (connectionCheckResult.success === false) {
        throw new Error(connectionCheckResult.errorMessage);
      }
    }

    await this.deleteDWCPackageInstallationSpaces(context, currentPackageInstallation);
    await PackageInstallationService.deletePackageInstallation(installationId, context);

    return operationResult;
  }

  private static async deleteDWCPackageInstallationSpaces(
    context: BDCRequestContext,
    currentPackageInstallation: BDCPackageInstallation
  ): Promise<void> {
    logInfo(`Delete DWC spaces for installationId:${currentPackageInstallation.installationId}`, { context });

    const systemInfo = await SystemsService.getAllSystems(context);
    const packageFolders = await getDataPackagesFolders(context, currentPackageInstallation, systemInfo, ["DWC"]);

    const spaceIds = new Set<string>();
    packageFolders.forEach((packageFolder) => {
      packageFolder.packageFolders.forEach((packageFolderItem) => {
        spaceIds.add(packageFolderItem.name);
      });
    });

    const technicalUserContext = RequestContext.createFromTenantId(context.tenantId!, {
      spanContext: context.spanContext,
    });
    technicalUserContext.correlationId = context.correlationId;
    try {
      const message = await new DeleteSpaces({ forceDelete: true }).delete(technicalUserContext, spaceIds);
      message.success.forEach((success) => {
        logInfo(`${success}`, { context });
      });
      message.warning.forEach((warning) => {
        logWarning(`${warning}`, { context });
      });

      message.fail.forEach((fail) => {
        logError(`${fail}`, { context });
      });
    } catch (error) {
      logError(`Failed to delete spaces: ${error}`, { context });
    }
  }

  static async forceDeletePackageInstallation(
    context: BDCRequestContext,
    installationId: string
  ): Promise<ExistingInstallationOperaitonResult> {
    let bdcPackageInstallation: BDCPackageInstallation = await PackageInstallationService.getPackageInstallation(
      context,
      installationId
    );

    const result: ExistingInstallationOperaitonResult = { installationId };
    const connectionCheckResult = await SystemsService.checkFosConnection({
      context,
      installationProps: bdcPackageInstallation.installationProperties,
    });

    if (connectionCheckResult) {
      result.connectionCheckResult = connectionCheckResult;
      if (connectionCheckResult.success === false) {
        return result;
      }
    }

    logInfo(`Start forceDeletePackageInstallation, package:${JSON.stringify(bdcPackageInstallation)}`, { context });
    bdcPackageInstallation = UninstallationManager.generateBDCPackageUninstallation(
      context,
      bdcPackageInstallation,
      BDCUninstallationOperation.UNINSTALL
    );
    bdcPackageInstallation.forceUninstall = true;
    await PackageInstallationService.updatePackageInstallation(bdcPackageInstallation, context);
    result.installationId = await UninstallationManager.doUninstallPackage(
      context,
      bdcPackageInstallation,
      true,
      BDCUninstallationOperation.UNINSTALL
    );
    return result;
  }

  /**
   * Throws a feature flag exception if any of the feature flags are not active
   * @param context
   * @param featureFlags
   */
  private static async checkFeatureFlags(
    context: BDCRequestContext,
    featureFlags: Array<keyof IFeatureFlagsMap>
  ): Promise<void> {
    for (const flag of featureFlags) {
      const ffEnabled = await context.isFeatureFlagActive(flag);
      if (!ffEnabled) {
        throw createFeatureFlagOffException(context, flag);
      }
    }
  }
}
