/** @format */
import { isNodeDevEnvironment } from "@sap/deepsea-utils";
import { AuthenticationMode, HttpMethod, httpClient } from "@sap/dwc-http-client";
import { SacClient } from "@sap/dwc-sac-client";
import assert from "assert";
import Status from "http-status-codes";
import { SystemInfo } from "../../../shared/bdccockpit/Types";
import { IRequestContext } from "../../repository/security/common/common";
import { ExternalCallCategory } from "../../repository/security/common/externalCallTypes";
import {
  ComponentInstallationStatus,
  FosComponentInstallationDetails,
} from "../packageInstallation/packageInstallationTypes";
import { getBdcLogger } from "../utils/logUtils";
const CALL_TIMEOUT = 2 * 60 * 1000;
const { logError, logInfo } = getBdcLogger(__filename);

export class FormationDao {
  static async makeRefreshCallPerSystem(
    context: IRequestContext,
    ordId: string,
    system: SystemInfo,
    componentInstallationStatus: ComponentInstallationStatus
  ) {
    const payload = FormationDao.buildRefreshPayloadPerSingleSystem(
      context,
      ordId,
      system,
      componentInstallationStatus
    );
    if (payload) {
      logInfo(`[makeRefreshCallPerSystem] Formation refresh payload is: ${JSON.stringify(payload)}`, { context });
      const deepseaBaseUrl = FormationDao.buildDatasphereCatalogURL(context);
      const url = `${deepseaBaseUrl}/v1/ucl/formationRefresh`;
      const headers = {
        "content-type": "application/json",
        "x-sap-boc-tenant-id": context.tenantId || "",
        "x-sap-boc-user-id": context.userInfo.userId || "",
      };
      const response = await httpClient.call({
        url,
        opts: {
          method: HttpMethod.POST,
          authentication: AuthenticationMode.Uaa,
          body: JSON.stringify(payload),
          acceptedStatusCodes: [],
          headers,
          requestContext: context,
          callCategory: ExternalCallCategory.OneDataCatalog,
          callTimeout: CALL_TIMEOUT,
        },
      });

      if (response.status !== Status.CREATED) {
        logError(
          `Failed to make a call to formation refresh. URL: ${url}, body: ${JSON.stringify(
            payload
          )}, headers: ${JSON.stringify(headers)}, response status: ${response.status}`,
          { context }
        );
      } else {
        logInfo(
          `Formation refresh status received. URL: ${url}, body: ${JSON.stringify(payload)}, headers: ${JSON.stringify(
            headers
          )}, response status: ${response.status}`,
          { context }
        );
      }
    } else {
      logError(`Formation refresh - Failed to build payload`, { context });
    }
  }
  private static buildRefreshPayloadPerSingleSystem(
    context: IRequestContext,
    ordId: string,
    systemInfo: SystemInfo,
    componentInstallationStatus: ComponentInstallationStatus
  ): any {
    try {
      const appNamespace: string | undefined = systemInfo.applicationNamespace;
      let region: string | undefined;
      const localId: string | undefined = systemInfo.applicationTenantId;
      systemInfo.propertyTags?.map((propertyTag) => {
        if (propertyTag.propertyId === "SP_UCL_DEPLOYMENT_REGION") {
          region = propertyTag.valueObjects[0].valueString;
        }
      });
      // region is allowed to be an empty string on some DCs
      if (!appNamespace || region === undefined || region === null || !localId) {
        logError(
          `Failing to build payload for refresh. appNamespace:  ${appNamespace}, region: ${region}, localId: ${localId}`,
          { context }
        );
        return null;
      }
      const system = {
        appNamespace,
        region,
        localId,
      };
      return {
        datasphereTenantId: context.userInfo.tenantId,
        systemInstanceFilters: [system],
        conditions: [
          {
            type: "ResourceUpdated",
            system,
            ordId,
            lastUpdate: FormationDao.calculateLastUpdateDate(context, componentInstallationStatus),
          },
        ],
      };
    } catch (error) {
      logError(`[buildRefreshPayloadPerSingleSystem] failed, error: ${error.message}`, { context });
      return null;
    }
  }

  private static calculateLastUpdateDate(
    context: IRequestContext,
    componentInstallationStatus: ComponentInstallationStatus
  ): string {
    logInfo(`calculate refresh UpdateDate, startTime: ${componentInstallationStatus.startTime}`, { context });
    const startTime =
      componentInstallationStatus.startTime instanceof Date
        ? componentInstallationStatus.startTime
        : new Date(componentInstallationStatus.startTime); // "2004-08-06T09:28:52.715Z"
    try {
      const createdAt = (
        componentInstallationStatus.componentInstallationDetails as FosComponentInstallationDetails[]
      )[0].createdAt;
      const modifiedAt = (
        componentInstallationStatus.componentInstallationDetails as FosComponentInstallationDetails[]
      )[0].modifiedAt;
      logInfo(`calculate refresh UpdateDate, createdAt: ${createdAt}, modifiedAt: ${modifiedAt}`, { context });
      const packageCreatedAt = new Date(createdAt as string);
      const packageModifiedAt = new Date(modifiedAt as string);
      if (startTime.getTime() < packageModifiedAt.getTime()) {
        logInfo(`startTime < than packageModifiedAt`, { context });
        return startTime.toISOString();
      } else {
        logInfo(`startTime >= than packageModifiedAt`, { context });
        return packageCreatedAt.toISOString();
      }
    } catch (error) {
      logError(`Failed to calculate refresh UpdateDate: ${error}`, { context });
      return startTime.toISOString();
    }
  }
  private static buildDatasphereCatalogURL(context: IRequestContext): string {
    const deepseaServiceName = process.env.deepseaServiceName || "deepsea";
    let url = SacClient.getSacUrl(deepseaServiceName);

    if (isNodeDevEnvironment()) {
      url = process.env.deepseaServiceUrl || url;
    }
    assert(url, "Deepsea service url should not be empty");
    url += "/catalog";
    return url;
  }
}
