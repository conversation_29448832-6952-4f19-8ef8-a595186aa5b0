/** @format */
import { BDCPackageType } from "../../../shared/bdccockpit/Enums";
import {
  BDCDataProduct,
  BDCPackageInstallationProperties,
  BDCSourceProvider,
  Formation,
  SystemInfo,
} from "../../../shared/bdccockpit/Types";
import { IRequestContext } from "../../repository/security/common/common";
import { deepClone } from "../../routes/space/ls/impl/lsSmUtils";
import { RequiredProviderApplications } from "../acnProvision/types";
import { PackageInstallationService } from "../packageInstallation/packageInstallationService";
import { BDCPackageInstallation, ComponentInstallationStatus } from "../packageInstallation/packageInstallationTypes";
import {
  extractSourceSystemIdFromInstallationPackage,
  extractTargetSystemIdFromInstallationPackage,
} from "../packageInstallation/packageInstallationUtils";
import { BDCPackage, BDCPackageComponent } from "../packages/packagesTypes";
import {
  findSourceInInstallationProperties,
  isApplicationNamespaceOfSourceType,
  isProviderOfSourceType,
} from "../systems/applicationNamespaceUtils";
import { splitSystemsToSourceAndTarget } from "../systems/systemUtils";
import { SystemsService } from "../systems/systemsService";
import { getBdcLogger } from "../utils/logUtils";
import { FormationDao } from "./formationDao";

const { logError, logInfo } = getBdcLogger(__filename);
export const INSTALL_JSON_DEF_PROPERTY_ID = "CP_BDC_INSTALL_JSON_DEF";

export class FormationService {
  static async doFormationRefresh(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationProperties: BDCPackageInstallationProperties[],
    componentInstallationStatus: ComponentInstallationStatus
  ) {
    const ordId = (component as BDCDataProduct).ordid;
    logInfo(
      `Formation refresh requested for component: ${ordId}, installationProperties: ${JSON.stringify(
        installationProperties
      )}`,
      { context }
    );
    const systemsToRefresh = await FormationService.findSystemThatRequiresRefreshForComponent(
      context,
      installationProperties,
      ordId
    );
    if (!systemsToRefresh || systemsToRefresh.length < 1) {
      logError(`Formation refresh will not be started. systemsToRefresh was not found: ${systemsToRefresh}`, {
        context,
      });
      return;
    }
    logInfo(`Starting refresh calls per systems: ${JSON.stringify(systemsToRefresh)}`, { context });
    systemsToRefresh.forEach(async (system) => {
      await FormationDao.makeRefreshCallPerSystem(context, ordId, system, componentInstallationStatus);
    });
  }

  /**
   * Builds list of source systems, where each system has list of target installations
   * @param context
   * @returns
   */
  public static async getAllFormationContexts(context: IRequestContext): Promise<SystemInfo[]> {
    const allSystems: SystemInfo[] = await SystemsService.getAllSystems(context);
    const { sourceSystems, targetSystems } = splitSystemsToSourceAndTarget(allSystems);
    const targetDspSystems: SystemInfo[] | undefined = targetSystems.get("sap.datasphere");
    const targetSacSystems: SystemInfo[] | undefined = targetSystems.get("sap.analytics");

    FormationService.markFormationsOfSourceSystemsWithHasTargetSystems(
      context,
      sourceSystems,
      targetDspSystems,
      targetSacSystems
    );
    return sourceSystems;
  }

  /*
    For each formation of source list new parameter: hasInstallationTargetSystems will be added.
    hasInstallationTargetSystems => false if formation doesn't have at least one of target systems in targetDspSystems and targetSacSystems.

    In addition installationLocationName is set
  */
  private static markFormationsOfSourceSystemsWithHasTargetSystems(
    context: IRequestContext,
    sourceSystems: SystemInfo[],
    targetDspSystems: SystemInfo[] | undefined,
    targetSacSystems: SystemInfo[] | undefined
  ) {
    sourceSystems.forEach((sourceSystem) => {
      sourceSystem.formations.forEach((formation) => {
        if (!targetDspSystems || !targetSacSystems) {
          formation.hasInstallationTargetSystems = false;
          logError(
            `Formation missing target system : targetDspSystem: ${targetDspSystems}, targetSacSystem: ${targetSacSystems}`,
            { context }
          );
        } else {
          const targetDspSystem: SystemInfo | undefined = FormationService.findTargetSystemByFormationId(
            targetDspSystems,
            formation.formationId
          );
          const targetSacSystem: SystemInfo | undefined = FormationService.findTargetSystemByFormationId(
            targetSacSystems,
            formation.formationId
          );
          if (!targetDspSystem || !targetSacSystem) {
            logError(
              `Formation is not mapped correctly: targetDspSystem: ${targetDspSystem}, targetSacSystem: ${targetSacSystem}`,
              { context }
            );
            formation.hasInstallationTargetSystems = false;
          } else {
            const name = formation.formationName ? formation.formationName : formation.formationId;
            formation.installationLocationName = `${name}, SAP Datasphere(${targetDspSystem.name}), SAP Analytics Cloud(${targetSacSystem.name})`;
            formation.hasInstallationTargetSystems = true;
          }
        }
      });
    });
  }

  /**
   * Getting all data from Catalog formationContext and installed packages
   * @param context
   * @param packageId
   * @returns
   */
  public static async getFormationContextAvailableForInstallationPackage(
    context: IRequestContext,
    bdcPackage: BDCPackage
  ): Promise<SystemInfo[]> {
    const localAllFormationContexts: SystemInfo[] = await FormationService.getAllFormationContexts(context);
    const installedPackagesSystemFormationMap: Map<string, string[]> =
      await FormationService.getInstalledSourceSystemFormationMap(context, bdcPackage.id);

    const allFormationContextsWithInstallationStatus: SystemInfo[] = FormationService.buildFormationContext(
      localAllFormationContexts,
      installedPackagesSystemFormationMap,
      bdcPackage
    );

    return allFormationContextsWithInstallationStatus;
  }

  /**
   * Getting all data as a parameters: formationContext and installed packages
   * We need this function for optimization, so we get systems and installed packages each in one call to catalog before
   * and then only build the result
   * @param context
   * @param packageId
   * @returns
   */
  public static async buildFormationContextAvailableForInstallationPackage(
    context: IRequestContext,
    bdcPackage: BDCPackage,
    allFormationContexts: SystemInfo[],
    installedPackages: BDCPackageInstallation[]
  ): Promise<SystemInfo[]> {
    // As same 'allFormationContexts' used for multiple packages, we need to clone so the result of different packages won't affect others
    const localAllFormationContexts: SystemInfo[] = deepClone(allFormationContexts).filter((systemInfo: SystemInfo) =>
      bdcPackage.requiredApplications
        .filter((requiredApp) => isProviderOfSourceType(requiredApp.provider))
        .some((requiredProviderApplications: RequiredProviderApplications) =>
          isApplicationNamespaceOfSourceType(
            systemInfo.applicationNamespace,
            requiredProviderApplications.provider as BDCSourceProvider
          )
        )
    );

    const installedPackagesSystemFormationMap: Map<string, string[]> =
      FormationService.buildFormationMapFromInstallations(installedPackages, context);

    const allFormationContextsWithInstallationStatus: SystemInfo[] = FormationService.buildFormationContext(
      localAllFormationContexts,
      installedPackagesSystemFormationMap,
      bdcPackage
    );

    return allFormationContextsWithInstallationStatus;
  }

  public static async detectFormationCatalogUuid(
    context: IRequestContext,
    installationProperties: BDCPackageInstallationProperties
  ): Promise<string | undefined> {
    let fosFormationCatalogUuid: string | undefined;
    if (installationProperties.formationCatalogUuid) {
      fosFormationCatalogUuid = installationProperties.formationCatalogUuid;
    } else {
      const systems = await SystemsService.getAllSystems(context, true);
      // Data product doesn't send formation Id, we need to calculate it
      const s4system = systems.find((sys) => sys.applicationTenantId === installationProperties.systemTenant);
      const bdcSystem = systems.find((sys) => sys.applicationNamespace === "sap.bdc-cockpit");
      logInfo(`[getFosApiDetailsGa] bdcSystem found: ${JSON.stringify(bdcSystem)}`, { context });
      const s4FormationsCatalogUuids = s4system?.formations.map((formation) => formation.catalogUuid);
      const bdcFormationsCatalogUuids = bdcSystem?.formations.map((formation) => formation.catalogUuid);
      fosFormationCatalogUuid = bdcFormationsCatalogUuids?.find((bdcForm) =>
        s4FormationsCatalogUuids?.includes(bdcForm)
      );
      logInfo(
        `[getFosApiDetailsGa] matching FOS formation catalogUuid: ${fosFormationCatalogUuid}, s4 Formations: ${JSON.stringify(
          s4FormationsCatalogUuids
        )}, bdc Formations: ${JSON.stringify(bdcFormationsCatalogUuids)}`,
        { context }
      );
    }
    if (fosFormationCatalogUuid) {
      return fosFormationCatalogUuid;
    } else {
      throw new Error(`fosFormationCatalogUuid wasn't found, process cannot continue`);
    }
  }

  public static async detectFormation(
    context: IRequestContext,
    installationProperties: BDCPackageInstallationProperties
  ): Promise<Formation> {
    const systems = await SystemsService.getAllSystems(context, true);
    const s4system = systems.find((sys) => sys.applicationTenantId === installationProperties.systemTenant);
    const bdcSystem = systems.find((sys) => sys.applicationNamespace === "sap.bdc-cockpit");
    logInfo(`bdcSystem found: ${JSON.stringify(bdcSystem)}`, { context });

    const s4FormationsCatalogUuids = s4system?.formations.map((formation) => formation.catalogUuid);
    const bdcFormations = bdcSystem?.formations;
    const detectedFormation: Formation | undefined = bdcFormations?.find((bdcForm) =>
      s4FormationsCatalogUuids?.includes(bdcForm.catalogUuid)
    );
    logInfo(
      `matching FOS formation catalogUuid: ${detectedFormation?.catalogUuid}, s4 Formations: ${JSON.stringify(
        s4FormationsCatalogUuids
      )}, bdc Formations: ${JSON.stringify(bdcFormations)}`,
      { context }
    );
    if (detectedFormation) {
      return detectedFormation;
    } else {
      throw new Error(`fosFormation wasn't found, process cannot continue`);
    }
  }

  static getBdcFormationFromSystemLandscape(context: IRequestContext, systemLandscape: SystemInfo): Formation {
    // after filtering the formation inside the system landscape they will have only one formation of bdc type
    const formation = systemLandscape?.formations?.[0];
    if (!formation) {
      logError(`There is no bdc formation inside ${systemLandscape.name} system landscape.`, { context });
    }
    return formation;
  }

  /////////// PRIVATE /////////////////
  /*
   * @param context
   * @param packageId
   * @returns  Map - Key: source uuid(s4 id),  value: list of formation catalog uuids
   */
  private static async getInstalledSourceSystemFormationMap(
    context: IRequestContext,
    packageId: string
  ): Promise<Map<string, string[]>> {
    // TODO [yan] - change after creating InstalledPackageService
    const packageInstallationsByPackageId = await PackageInstallationService.getPackageInstallationsByPackageId(
      context,
      packageId
    );
    const installedSourceSystemFormationMap: Map<string, string[]> =
      FormationService.buildFormationMapFromInstallations(packageInstallationsByPackageId.allObjects, context);
    return installedSourceSystemFormationMap;
  }

  /*
   * @param installations
   * @param context
   * @returns Map - Key: source uuid(s4 id),  value: list of formation catalog uuids
   */
  private static buildFormationMapFromInstallations(
    installations: any[],
    context: IRequestContext
  ): Map<string, string[]> {
    const installedSourceSystemFormationMap: Map<string, string[]> = new Map();
    try {
      installations?.forEach((installationPackage: any) => {
        const sourceId: string = extractSourceSystemIdFromInstallationPackage(context, installationPackage)!;
        let formationsArray = installedSourceSystemFormationMap.get(sourceId);
        if (!formationsArray) {
          formationsArray = [];
          installedSourceSystemFormationMap.set(sourceId, formationsArray);
        }
        formationsArray.push(extractTargetSystemIdFromInstallationPackage(context, installationPackage)!);
      });
    } catch (error) {
      logError(error, { context });
    }
    return installedSourceSystemFormationMap;
  }

  private static async findSystemThatRequiresRefreshForComponent(
    context: IRequestContext,
    installationProperties: BDCPackageInstallationProperties[],
    ordId: string
  ): Promise<SystemInfo[] | undefined> {
    if (!installationProperties || installationProperties.length < 1 || !ordId) {
      logError(
        `Formation refresh wasn't started. installationProperties: ${installationProperties}, component: ${ordId}`,
        { context }
      );
      return;
    }
    const installationPropertiesByProvider = findSourceInInstallationProperties(installationProperties);
    if (!installationPropertiesByProvider || installationPropertiesByProvider.length < 1) {
      logError(
        `Formation refresh wasn't started. installationPropertiesByProvider: ${installationPropertiesByProvider}`,
        { context }
      );
      return;
    }
    const systemsToRefresh: SystemInfo[] = await FormationService.findSystemsByInstallationProperties(
      context,
      installationPropertiesByProvider
    );

    return systemsToRefresh;
  }

  private static async findSystemsByInstallationProperties(
    context: IRequestContext,
    installationPropertiesByProvider: BDCPackageInstallationProperties[]
  ) {
    const appTenantIds: string[] = [];
    installationPropertiesByProvider?.forEach((provider) => {
      appTenantIds.push(provider.systemTenant);
    });
    const installedSystems: SystemInfo[] = [];
    const systemInfo = await SystemsService.getAllSystems(context);
    systemInfo.forEach((system) => {
      if (appTenantIds.includes(system.applicationTenantId)) {
        installedSystems.push(system);
      }
    });
    return installedSystems;
  }

  private static buildFormationContext(
    allFormationContexts: SystemInfo[],
    installedPackagesSystemFormationMap: Map<string, string[]>,
    bdcPackage: BDCPackage
  ) {
    if (bdcPackage.type === BDCPackageType.INSIGHT_APPLICATION) {
      return FormationService.updateSourceSystemsIsInstallablePerIA(
        allFormationContexts,
        installedPackagesSystemFormationMap
      );
    } else {
      // BDCPackageType.DATA_PACKAGE
      return FormationService.updateSourceSystemsIsInstallablePerDP(
        allFormationContexts,
        installedPackagesSystemFormationMap
      );
    }
  }

  /*
   * For insight Application we need to update the source system as well as all formations of the system:
   * If all formations(targets - DSP & SAC) of the source system are not installable the system is not installable as well
   */
  private static updateSourceSystemsIsInstallablePerIA(
    allFormationContexts: SystemInfo[],
    installedPackagesSystemFormationMap: Map<string, string[]>
  ) {
    const allFormationContextsWithInstallationStatus: SystemInfo[] = [];
    allFormationContexts!.forEach((sourceSystemInfo) => {
      sourceSystemInfo.isInstallable = false; // default
      // Get all targets(formations) where IA already installed from that system
      // IA cannot be installed only once in formation even if it has two source systems, therefore we don't extract
      // installedSystemFormations by package it from map, but rather combine all formations in one array
      const installedSystemFormations: string[] = Array.from(installedPackagesSystemFormationMap.values()).flat();
      // for each formation in source system:
      // if formation is included in installedSystemFormations, then it's not installable
      sourceSystemInfo.formations.forEach((formation) => {
        if (installedSystemFormations?.includes(formation.catalogUuid!)) {
          formation.isInstallable = false;
        } else {
          formation.isInstallable = true;
          sourceSystemInfo.isInstallable = true; // source system become installable if at least one formation become installable
        }
      });
      allFormationContextsWithInstallationStatus.push(sourceSystemInfo);
    });
    return allFormationContextsWithInstallationStatus;
  }

  /**
   * For Data Package is enough to test if it was already installed on source system
   */
  private static updateSourceSystemsIsInstallablePerDP(
    allFormationContexts: SystemInfo[],
    installedPackagesSystemFormationMap: Map<string, string[]>
  ) {
    const allFormationContextsWithInstallationStatus: SystemInfo[] = [];
    allFormationContexts!.forEach((sourceSystemInfo) => {
      // Get all targets(formations) where IA already installed from that system
      const installedSystemFormations: string[] = installedPackagesSystemFormationMap.get(sourceSystemInfo.systemId)!;
      sourceSystemInfo.isInstallable = !installedSystemFormations || installedSystemFormations.length === 0;
      allFormationContextsWithInstallationStatus.push(sourceSystemInfo);
    });
    return allFormationContextsWithInstallationStatus;
  }

  private static findTargetSystemByFormationId(systems: SystemInfo[], formationId: string): SystemInfo | undefined {
    const filteredSystems: SystemInfo[] = systems.filter((system) => {
      const filteredFormations = system.formations.filter((formation) => formation.formationId === formationId);
      return filteredFormations && filteredFormations.length > 0;
    });
    //[0] as only one dsp or sac system can be in formation
    return filteredSystems && filteredSystems.length > 0 ? filteredSystems[0] : undefined;
  }
}
