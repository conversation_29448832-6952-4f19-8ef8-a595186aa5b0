/** @format */

import {
  AnalyticsNameSpace,
  ApplicationNamespace,
  BDCPackageInstallationProperties,
  BDCProvider,
  BDCSourceProvider,
  BDCTargetProvider,
  DatasphereNameSpace,
  Formation,
  HcmNameSpace,
  S4NameSpace,
  SystemInfo,
} from "../../../shared/bdccockpit/Types";
import { BDCPackageInstallation } from "../packageInstallation/packageInstallationTypes";
import { BDCPackage } from "../packages/packagesTypes";

export const s4ApplicationNamespace: S4NameSpace = "sap.s4";
export const hcmApplicationNamespace: HcmNameSpace = "sap.sf";
export const sacApplicationNamespace: AnalyticsNameSpace = "sap.analytics";
export const dspApplicationNamespace: DatasphereNameSpace = "sap.datasphere";
export const s4pceApplicationNamespace: S4NameSpace = "sap.s4pce";
export const s4Provider: BDCProvider = "sap.s4";
export const hcmProvider: BDCProvider = "sap.sf";
export const sacProvider: BDCProvider = "sap.analytics";
export const dspProvider: BDCProvider = "sap.datasphere";

export const sourceByProviderMap = new Map<BDCProvider, ApplicationNamespace[]>([
  [s4Provider, [s4pceApplicationNamespace, s4ApplicationNamespace]],
  [hcmProvider, [hcmApplicationNamespace]],
]);
export const targetByProviderMap = new Map<BDCProvider, ApplicationNamespace[]>([
  [sacProvider, [sacApplicationNamespace]],
  [dspProvider, [dspApplicationNamespace]],
]);
export const namespaceByProviderMap = new Map<BDCProvider, ApplicationNamespace[]>([
  ...sourceByProviderMap,
  ...targetByProviderMap,
]);

export function getFormationsFromProvider(
  provider: BDCProvider,
  systemInfo: SystemInfo[],
  formationId?: string
): Formation[] | undefined {
  const namespacesInProvider = namespaceByProviderMap.get(provider);
  let formation: Formation[] = [] as Formation[];
  namespacesInProvider?.forEach((namespace) => {
    const namespaceFormation = getFormationByApplicationNamespace(systemInfo, namespace, formationId);
    if (namespaceFormation) {
      formation.push(namespaceFormation);
    }
  });

  return formation;
}

export function getInstallationPropertiesByProvider(
  installationPropertiesArr: BDCPackageInstallationProperties[],
  provider: BDCProvider
): BDCPackageInstallationProperties[] | undefined {
  const namespacesInProvider = namespaceByProviderMap.get(provider);
  return installationPropertiesArr.filter((installationProperty) =>
    namespacesInProvider?.includes(installationProperty.applicationNamespace)
  );
}
export function findSourceInInstallationProperties(
  installationPropertiesArr: BDCPackageInstallationProperties[]
): BDCPackageInstallationProperties[] {
  return installationPropertiesArr.filter((ip) => isApplicationNamespaceOfSourceType(ip.applicationNamespace));
}

export function isApplicationNamespaceExistsInProvider(
  applicationNamespace: ApplicationNamespace,
  provider: BDCProvider
): boolean {
  const providerNamespaces = namespaceByProviderMap.get(provider);
  return !!providerNamespaces?.includes(applicationNamespace);
}

export function getSystemInfoByProvider(systemInfoArr: SystemInfo[], provider: BDCProvider): SystemInfo[] | undefined {
  const nameSpacesInProvider = namespaceByProviderMap.get(provider);
  return systemInfoArr?.filter((systemInfoItem) => nameSpacesInProvider?.includes(systemInfoItem.applicationNamespace));
}

export function getFormationByApplicationNamespace(
  systemInfoArr: SystemInfo[],
  applicationNamespace: ApplicationNamespace,
  formationId?: string
): Formation | undefined {
  const sysInfo = findSystemInfoByApplicationNamespace(systemInfoArr, applicationNamespace);
  return formationId
    ? sysInfo?.formations?.find((formation) => formation.formationId === formationId)
    : sysInfo?.formations?.[0];
}

export function findSystemInfoByApplicationNamespace(
  systemInfoArr: SystemInfo[],
  applicationNamespace: ApplicationNamespace
): SystemInfo | undefined {
  return systemInfoArr?.find((systemInfo) => systemInfo.applicationNamespace === applicationNamespace);
}

/**
 *
 * @param applicationNamespace Check if provided application namespace is of Source type
 *  If sourceProvider defined, check if applicationNamespace is source and of this specific type, otherwise check for all types
 * @param sourceProvider
 * @returns
 */
export function isApplicationNamespaceOfSourceType(
  applicationNamespace: ApplicationNamespace,
  sourceProvider?: BDCSourceProvider
): boolean {
  if (sourceProvider) {
    return !!sourceByProviderMap.get(sourceProvider)?.includes(applicationNamespace);
  } else {
    return isApplicationNamespaceExistsInMapValues(applicationNamespace, sourceByProviderMap);
  }
}

export function applicationNamespaceToProvider(applicationNamespace: ApplicationNamespace): BDCProvider | undefined {
  let resultProvider: BDCProvider | undefined;
  namespaceByProviderMap.forEach((applicationNamespaces, provider) => {
    if (applicationNamespaces.includes(applicationNamespace)) {
      resultProvider = provider;
    }
  });
  return resultProvider;
}

export function isProviderOfSourceType(provider: BDCProvider): provider is BDCSourceProvider {
  return isProviderExistsInMapKeys(provider, sourceByProviderMap);
}

export function isProviderOfTargetType(provider: BDCProvider): provider is BDCTargetProvider {
  return isProviderExistsInMapKeys(provider, targetByProviderMap);
}

export function isApplicationNamespaceOfTargetType(applicationNamespace: ApplicationNamespace): boolean {
  return isApplicationNamespaceExistsInMapValues(applicationNamespace, targetByProviderMap);
}
export function isHcmProvider(bdcPackage: BDCPackage) {
  return bdcPackage.requiredApplications.some((app) => isApplicationNamespaceOfSourceType(app.provider, "sap.sf"));
}

export function isS4Provider(bdcPackage: BDCPackage) {
  return bdcPackage.requiredApplications.some((app) => isApplicationNamespaceOfSourceType(app.provider, "sap.s4"));
}

/**
 * Determines the provider namespace from the given package installation.
 *
 * It filters the required applications to only include source providers: "sap.s4" (S/4HANA) or "sap.sf" (SuccessFactors/HCM).
 * If no such provider exists, or if there is a mix of both, an error is thrown.
 *
 * @param existingInstallation - The existing package installation to evaluate.
 * @returns The provider namespace ("sap.s4" or "sap.sf").
 * @throws Error if no provider is found, or if multiple provider types are mixed.
 */
export function getProvider(existingInstallation: BDCPackageInstallation) {
  const providers = existingInstallation.originalPackage.requiredApplications
    .map((app) => app.provider)
    .filter((provider) => isProviderOfSourceType(provider));

  if (providers.length === 0) {
    throw new Error("There is no provider defined in the existing installation.");
  }
  const isUniform = providers.every((p) => p === providers[0]);

  if (!isUniform) {
    throw new Error("Multiple providers is not allowed.");
  }
  return providers[0];
}

/// Private methods ///
function isApplicationNamespaceExistsInMapValues(
  applicationNamespace: ApplicationNamespace,
  map: Map<BDCProvider, ApplicationNamespace[]>
): boolean {
  const matchedProvider = Array.from(map.values()).find((providerArray) =>
    providerArray.includes(applicationNamespace)
  );
  return !!matchedProvider;
}
function isProviderExistsInMapKeys(provider: BDCProvider, map: Map<BDCProvider, ApplicationNamespace[]>): boolean {
  return Array.from(map.keys()).includes(provider);
}
