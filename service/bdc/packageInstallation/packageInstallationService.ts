/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import * as semver from "semver";
import { BDCPackageType } from "../../../shared/bdccockpit/Enums";
import { BDCPackageInstallationProperties, BDCRequestContext, SystemInfo } from "../../../shared/bdccockpit/Types";
import { IRequestContext } from "../../repository/security/common/common";
import { RequestContext } from "../../repository/security/requestContext";
import { PropertiesField } from "../catalog/CatalogConstants";
import { relationshipSystemType } from "../catalog/catalogTypes";
import { TenantSKU } from "../entitlement/entitlementTypes";
import { installationPropertiesToTenantSKU } from "../entitlement/entitlementValidator";
import { BDCPackageComponent } from "../packages/packagesTypes";
import { SystemsService } from "../systems/systemsService";
import { INSTALLED_CONTENT_FOUND } from "../utils/IntegrationFlowConstants";
import { getBdcLogger } from "../utils/logUtils";
import { PackageInstallationDao } from "./packageInstallationDao";
import { PackageInstallationLinksFetcher } from "./packageInstallationLinksFetcher";
import {
  BDCPackageInstallation,
  ComponentInstallRef,
  ComponentInstallationStatus,
  IAllObjectResponse,
  IPackagesResponse,
  IValueObjectsTypeText,
  UCLTenantValidationResponse,
} from "./packageInstallationTypes";
import {
  convertInstallationStatusToComponentInstallationDetails,
  getAllPackageInstallationsFromResponse,
  getAllPackageInstallationsWithComponentsFromResponse,
  getInstalledComponentsId,
  sanitizePackageInstallationForLog,
} from "./packageInstallationUtils";

const { logError, logInfo } = getBdcLogger(__filename);

export class PackageInstallationService {
  public static async createPackageInstallation(
    packageInstallation: BDCPackageInstallation,
    context: IRequestContext
  ): Promise<void> {
    packageInstallation.lastModifiedTime = packageInstallation.startTime;

    await PackageInstallationDao.getInstance().createPackageInstallation(packageInstallation, context);
  }

  public static async deletePackageInstallation(installationId: string, context: IRequestContext): Promise<void> {
    await PackageInstallationDao.getInstance().deletePackageInstallation(installationId, context);
  }

  static async getPackageInstallation(
    context: IRequestContext,
    installationId: string
  ): Promise<BDCPackageInstallation> {
    const packageInstallationAny = await PackageInstallationDao.getInstance().getPackageInstallationById(
      context,
      installationId
    );
    const packageInstallation: BDCPackageInstallation =
      PackageInstallationService.getPackageInstallationFromResponse(packageInstallationAny);
    const isDataAdded = await PackageInstallationService.addLinks(context, packageInstallation);
    if (isDataAdded) {
      await PackageInstallationService.updatePackageInstallation(packageInstallation, context);
    }
    return packageInstallation;
  }

  public static async updatePackageInstallation(
    packageInstallation: BDCPackageInstallation,
    context: IRequestContext
  ): Promise<void> {
    if (packageInstallation.endTime) {
      packageInstallation.lastModifiedTime = packageInstallation.endTime;
    } else {
      packageInstallation.lastModifiedTime = new Date();
    }

    const payload = PackageInstallationService.packageInstallationToPayload(context, packageInstallation);
    await PackageInstallationDao.getInstance().updatePackageInstallation(
      context,
      packageInstallation.installationId,
      payload
    );
  }

  public static async getFlattenedInstalledPackages(context: IRequestContext): Promise<BDCPackageInstallation[]> {
    const allPackageInstallations: Map<string, BDCPackageInstallation> =
      await PackageInstallationService.getAllPackageInstallationsWithRelations(context);

    const packageInstallationsArray = Array.from(allPackageInstallations.values());

    return packageInstallationsArray.flatMap((installationList) => installationList);
  }

  /**
   *
   * @param context Following methods used by another services where only clean data of installed packages is required
   * @param packageId
   * @returns
   */
  public static async getAllPackageInstallationsWithoutRelations(
    context: IRequestContext
  ): Promise<Map<string, BDCPackageInstallation>> {
    const allPackageInstallations =
      await PackageInstallationDao.getInstance().getAllPackageInstallationsWithoutRelations(context);
    return getAllPackageInstallationsFromResponse(allPackageInstallations);
  }

  public static async getAllPackageInstallationsWithComponents(
    context: IRequestContext
  ): Promise<Map<string, BDCPackageInstallation>> {
    const allPackageInstallationsWithComponents =
      await PackageInstallationDao.getInstance().getAllPackageInstallationsWithComponents(context);
    return getAllPackageInstallationsWithComponentsFromResponse(allPackageInstallationsWithComponents);
  }

  public static async getPackageInstallationsByPackageId(context: IRequestContext, packageId: string) {
    return await PackageInstallationDao.getInstance().getPackageInstallationsWithRelationsByPackageId(
      context,
      packageId
    );
  }

  public static async getAllPackageInstallationsWithRelations(
    context: IRequestContext,
    packageId?: string
  ): Promise<Map<string, BDCPackageInstallation>> {
    const allPackageInstallations =
      await PackageInstallationDao.getInstance().getPackageInstallationsWithRelationsByPackageId(context, packageId);
    return getAllPackageInstallationsFromResponse(allPackageInstallations);
  }

  public static async getComponentInstallRef(
    context: IRequestContext,
    componentId: string,
    relationshipSystem: relationshipSystemType
  ): Promise<ComponentInstallRef[]> {
    const allObjects: IAllObjectResponse[] = await PackageInstallationDao.getInstance().getComponentInstallRef(
      context,
      componentId,
      relationshipSystem
    );

    return allObjects
      .filter((obj) => obj.navigationLinks.length > 0)
      .map((obj) => ({ installId: obj.name, refId: obj.navigationLinks[0].links[0].refId }));
  }

  public static async addLinks(
    context: IRequestContext,
    bdcPackageInstallation: BDCPackageInstallation
  ): Promise<boolean> {
    let isDataAdded = false;
    if (bdcPackageInstallation) {
      if (!bdcPackageInstallation.dspFolders || bdcPackageInstallation.dspFolders.length === 0) {
        logInfo(`DSP folders are not set for installation: ${bdcPackageInstallation.installationId}`, { context });
        await PackageInstallationLinksFetcher.addDspLinksToPackageInstallation(context, bdcPackageInstallation);
        if (bdcPackageInstallation.dspFolders && bdcPackageInstallation.dspFolders.length > 0) {
          isDataAdded = true;
        }
      }

      const areLinksNotDefined = !bdcPackageInstallation.workSpaceLinks || !bdcPackageInstallation.storyLinks;
      const areLinksEmpty =
        bdcPackageInstallation.workSpaceLinks?.size === 0 || bdcPackageInstallation.storyLinks?.size === 0;
      if (areLinksNotDefined || areLinksEmpty) {
        logInfo(`Workspace or story links are not set for installation: ${bdcPackageInstallation.installationId}`, {
          context,
        });
        await PackageInstallationLinksFetcher.addInsightAppLinksToPackageInstallation(context, bdcPackageInstallation);
        if (
          bdcPackageInstallation.workSpaceLinks &&
          bdcPackageInstallation.workSpaceLinks.size > 0 &&
          bdcPackageInstallation.storyLinks &&
          bdcPackageInstallation.storyLinks.size > 0
        ) {
          isDataAdded = true;
        }
      }
    }
    return isDataAdded;
  }

  public static async updateInstallationComponentsIds(
    context: IRequestContext,
    bdcPackageInstallation: BDCPackageInstallation,
    isInstallationFailed: boolean
  ) {
    const isBDCGA = await context.isFeatureFlagActive("DWCO_BDC_GA");
    if (isBDCGA) {
      const installedComponentsIds = isInstallationFailed
        ? []
        : getInstalledComponentsId(context, bdcPackageInstallation);
      await PackageInstallationService.doUpdateInstallationComponentsIds(
        context,
        bdcPackageInstallation.installationId,
        installedComponentsIds
      );
    }
  }

  public static async getMaxInstalledVersionCnPackageForSrc(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationProperties: BDCPackageInstallationProperties[],
    excludeInstallationIds: string[],
    allPackagesWithInstalledComponents?: Map<string, BDCPackageInstallation>
  ): Promise<string | undefined> {
    // Retrieve all packages with installed components if not provided
    const packagesWithComponents =
      allPackagesWithInstalledComponents ??
      (await PackageInstallationService.getAllPackageInstallationsWithComponents(context));

    // Find the current installation system matching the component's provider
    const currentInstallationSystem = installationProperties.find(
      (property) => property.applicationNamespace === component.provider
    );

    if (!currentInstallationSystem) {
      logInfo(`No matching installation system found for provider: ${component.provider}`, { context });
      return undefined;
    }

    let maxInstalledVersion: string | undefined;

    // Iterate through all package installations
    for (const [installationId, installation] of packagesWithComponents.entries()) {
      // Skip excluded installations
      if (excludeInstallationIds.includes(installationId)) {
        continue;
      }

      // Only process Insight Application packages
      if (installation.originalPackage?.type !== BDCPackageType.INSIGHT_APPLICATION) {
        continue;
      }

      // Find the matching component in the package
      const matchingComponent = installation.originalPackage.components.find(
        (instComponent) => instComponent.provider === component.provider && instComponent.name === component.name
      );

      if (!matchingComponent) {
        continue;
      }

      // Verify the installation property matches the current system
      const matchingProperty = installation.installationProperties.find(
        (property) =>
          property.applicationNamespace === currentInstallationSystem.applicationNamespace &&
          property.systemTenant === currentInstallationSystem.systemTenant &&
          property.systemName === currentInstallationSystem.systemName
      );

      if (!matchingProperty) {
        continue;
      }

      // Check the installation status of the component
      const installStatus = installation.componentsInstallationStatus.find(
        (status) => status.componentId === matchingComponent.componentId
      );

      if (!installStatus || installStatus.status !== "DONE") {
        continue;
      }

      // Ensure the component is not uninstalled
      const uninstallationStatus = installation.componentsUninstallationStatus?.find(
        (status) => status.componentId === matchingComponent.componentId
      );

      if (!!uninstallationStatus && uninstallationStatus.status === "DONE") {
        continue;
      }

      // Update the maximum installed version if applicable
      if (!maxInstalledVersion || semver.gt(matchingComponent.version, maxInstalledVersion)) {
        maxInstalledVersion = matchingComponent.version;
      }
    }
    return maxInstalledVersion;
  }

  public static async checkTenantForDeletionFromFormation(
    context: BDCRequestContext,
    tenantId: string | undefined,
    uclTenantMapping: any
  ): Promise<UCLTenantValidationResponse> {
    const readyForDeletion: UCLTenantValidationResponse = {
      errors: [],
    };
    try {
      if (!tenantId) {
        logError(`Missing tenant id for deletion: ${tenantId}`, { context });
        return readyForDeletion;
      }
      if (!uclTenantMapping?.tenants || !uclTenantMapping?.formation?.uclFormationTypeId) {
        logError(`Invalid UCL mapping. Tenant for deletion: ${tenantId}`, { context });
        return readyForDeletion;
      }
      // ready to be deleted if there is no BDC Cockpit tenant in UCL mapping
      const bdcCockpitTenant = uclTenantMapping.tenants.find(
        (tenantDetails: any) => tenantDetails?.applicationNamespace === "sap.bdc-cockpit"
      );
      if (!bdcCockpitTenant) {
        logInfo(`No BDC Cockpit tenant found in UCL mapping. Tenant for deletion: ${tenantId}`, {
          context,
        });
        return readyForDeletion;
      }
      const bdcCockpitContext = RequestContext.createFromTenantId(bdcCockpitTenant.applicationTenantId, {
        spanContext: context.spanContext,
        preventCustomerHanaAutoUpgrade: true,
      });
      if (!bdcCockpitContext?.userInfo?.userId) {
        (bdcCockpitContext.userInfo as any).userId = "UCL_client";
      }
      logInfo(`bdcCockpitContext.userInfo.userId=${bdcCockpitContext?.userInfo?.userId} for tenantId=${tenantId}.`, {
        context,
      });
      logInfo(`bdcCockpitContext=${JSON.stringify(bdcCockpitContext)} for tenantId=${tenantId}.`, { context });
      // ready to be deleted if requested tenant not found in the list of systems for BDC Cockpit
      const allSystems: SystemInfo[] = await SystemsService.getAllSystems(bdcCockpitContext);
      logInfo(`Systems found=${allSystems?.length} for BDC Cockpit.`, {
        context,
      });
      const tenantSystem: SystemInfo | undefined = allSystems?.find(
        (system) => system.applicationTenantId === tenantId
      );
      if (!tenantSystem) {
        logInfo(`Tenant not found in the list of systems for BDC Cockpit. Tenant for deletion: ${tenantId}`, {
          context,
        });
        return readyForDeletion;
      }
      const excludedTenantBDCFormationsCount = PackageInstallationService.countFormations(
        tenantSystem,
        uclTenantMapping.formation.uclFormationTypeId
      );
      logInfo(
        `Formations count=${excludedTenantBDCFormationsCount} with formationTypeId=${uclTenantMapping.formation.uclFormationTypeId} for tenant for deletion: ${tenantId}`,
        { context }
      );
      const tenantInstalledPackages: string[] = await PackageInstallationService.getInstalledPackagesBySystemTenantId(
        bdcCockpitContext,
        tenantId,
        excludedTenantBDCFormationsCount
      );
      if (tenantInstalledPackages?.length > 0) {
        logInfo(`Installed packages found=[${tenantInstalledPackages.join(",")}] for tenantId=${tenantId}.`, {
          context,
        });
        readyForDeletion.errors = [INSTALLED_CONTENT_FOUND];
        logInfo(`Response=${JSON.stringify(readyForDeletion)} for tenantId=${tenantId}.`, { context });
        return readyForDeletion;
      }
      logInfo(`No installed packages for tenantId=${tenantId}.`, { context });
    } catch (err) {
      logError(`Validation for tenantId=${tenantId} failed: ${err}`, { context });
    }
    return readyForDeletion;
  }

  public static countFormations(system: SystemInfo, formationTypeId: string) {
    let formationsCount = 0;
    system.formations?.forEach((formation) => {
      if (formation.formationTypeId === formationTypeId) {
        formationsCount++;
      }
    });
    return formationsCount;
  }

  // context.userInfo.tenantId is a mandatory parameter
  public static async getInstalledPackagesPerSku(context: IRequestContext) {
    const tenantId = context?.userInfo?.tenantId;
    if (!tenantId) {
      return "Missing tenant id";
    }
    let skuArr;
    try {
      const INSIGHT_APPS = "InsightApps";
      const DATA_PACKAGES = "DataPackages";
      const installedPackagesPerSku = new Map<string, Map<string, number>>();

      skuArr = Object.values(TenantSKU) as string[];
      skuArr = skuArr?.filter((sku) => sku !== TenantSKU.OTHER.toString());

      if (!context?.userInfo?.userId) {
        (context.userInfo as any).userId = "check_sku_user";
      }
      const installedPackages: BDCPackageInstallation[] =
        await PackageInstallationService.getFlattenedInstalledPackages(context);

      installedPackages?.forEach((installedPackage) => {
        if (!installedPackage.installationProperties) {
          return;
        }

        const notActiveComponent: ComponentInstallationStatus | undefined =
          installedPackage.componentsInstallationStatus?.find(
            (componentsInstallationStatus) => componentsInstallationStatus.status !== "DONE"
          );
        if (!installedPackage.componentsInstallationStatus || notActiveComponent) {
          return;
        }

        const tenantSKU: TenantSKU = installationPropertiesToTenantSKU(installedPackage.installationProperties);
        let installedData: Map<string, number> | undefined = installedPackagesPerSku.get(tenantSKU);
        if (installedData === undefined) {
          installedData = new Map<string, number>();
        }
        if (installedPackage.originalPackage?.type === BDCPackageType.INSIGHT_APPLICATION) {
          const insightAppsCounter: number = installedData.get(INSIGHT_APPS) ?? 0;
          installedData.set(INSIGHT_APPS, insightAppsCounter + 1);
        } else {
          const dataPackagesCounter: number = installedData.get(DATA_PACKAGES) ?? 0;
          installedData.set(DATA_PACKAGES, dataPackagesCounter + 1);
        }
        installedPackagesPerSku.set(tenantSKU, installedData);
      });
      skuArr = skuArr?.map((skuName) => {
        const installedData = installedPackagesPerSku.get(skuName);
        return {
          [skuName]: {
            InsightApps: installedData?.get(INSIGHT_APPS) ?? 0,
            DataPackages: installedData?.get(DATA_PACKAGES) ?? 0,
          },
        };
      });
    } catch (err) {
      logError(`Failed to get installed packages for tenantId =${tenantId}: ${err}`, { context });
    }

    logInfo(`Installed packages found for tenantId=${tenantId}: ${JSON.stringify(skuArr)}`, { context });
    return skuArr;
  }

  // //////////// Private Methods ///////////////
  private static async doUpdateInstallationComponentsIds(
    context: IRequestContext,
    installationId: string,
    componentsIds: string[]
  ): Promise<void> {
    if (!componentsIds || componentsIds.length === 0) {
      const currentComponentsIds = await PackageInstallationDao.getInstance().getInstallationComponentsId(
        context,
        installationId
      );
      if (!currentComponentsIds || currentComponentsIds.length === 0) {
        logInfo(`No components to update for installationId: ${installationId}`, { context });
        return;
      }
    }

    await PackageInstallationDao.getInstance().updateInstallationComponentsId(context, installationId, componentsIds);
  }

  /*
   * JSON.Stringify method doesn't handle Map members(just replace them with empty array)
   * Therefore we need to transfer workSpaceLinks and storyLinks maps to arrays
   */
  private static packageInstallationToPayload(
    context: IRequestContext,
    packageInstallation: BDCPackageInstallation
  ): string {
    logInfo("[packageInstallationToPayload] Payload start building payload", { context });
    // As the packageInstallation passed by reference and in order to avoid deep clone, we keeping
    // workSpaceLinks and storyLinks original values and after serialization set them back in order to not affect structure
    // in consumers of this method
    const originalWorkSpaceLinks = packageInstallation.workSpaceLinks;
    const originalStoryLinks = packageInstallation.storyLinks;
    const newPackageInstallation: any = packageInstallation;
    if (packageInstallation.workSpaceLinks) {
      newPackageInstallation.workSpaceLinks = Array.from(packageInstallation.workSpaceLinks);
    }
    if (packageInstallation.storyLinks) {
      newPackageInstallation.storyLinks = Array.from(packageInstallation.storyLinks);
    }
    const payload: string = JSON.stringify(newPackageInstallation);

    logInfo(`[packageInstallationToPayload] Payload is: ${sanitizePackageInstallationForLog(newPackageInstallation)}`, {
      context,
    });
    packageInstallation.workSpaceLinks = originalWorkSpaceLinks;
    packageInstallation.storyLinks = originalStoryLinks;
    return payload;
  }

  private static getPackageInstallationFromResponse(packages: IPackagesResponse): BDCPackageInstallation {
    if (!packages || !packages.allObjects || packages.allObjects.length === 0) {
      throw new Error("unable to get installation package");
    }

    let packageInstallation: BDCPackageInstallation | null = null;
    packages.allObjects.forEach((objectResponse) => {
      if (packageInstallation) {
        return;
      }
      objectResponse.propertyTags.forEach((propertyTagResponse) => {
        if (packageInstallation) {
          return;
        }
        if (propertyTagResponse.propertyId === PropertiesField.BDC_INSTALL_JSON) {
          propertyTagResponse.valueObjects.forEach((valueObjectType) => {
            if (packageInstallation) {
              return;
            }
            const valueObjectsTypeText = valueObjectType as IValueObjectsTypeText;
            if (valueObjectsTypeText) {
              const parsedObj: any = JSON.parse(valueObjectsTypeText.valueText);
              convertInstallationStatusToComponentInstallationDetails(parsedObj.componentsInstallationStatus);
              if (parsedObj && parsedObj.workSpaceLinks) {
                parsedObj.workSpaceLinks = new Map(parsedObj.workSpaceLinks);
              }
              if (parsedObj && parsedObj.storyLinks) {
                parsedObj.storyLinks = new Map(parsedObj.storyLinks);
              }
              packageInstallation = parsedObj as BDCPackageInstallation;
            }
          });
        }
      });
    });

    if (!packageInstallation) {
      throw new Error("unable to get installation package");
    }
    return packageInstallation;
  }

  private static async getInstalledPackagesBySystemTenantId(
    context: IRequestContext,
    tenantId: string,
    excludedTenantBDCFormationsCount: number
  ): Promise<string[]> {
    const tenantInstalledPackages: string[] = [];
    try {
      const installedPackages = await PackageInstallationService.getFlattenedInstalledPackages(context);

      installedPackages?.forEach((installedPackage) => {
        installedPackage.installationProperties?.forEach((installationProperty) => {
          if (installationProperty?.systemTenant === tenantId) {
            const isInsightApp = installedPackage?.originalPackage?.type === BDCPackageType.INSIGHT_APPLICATION;
            if (isInsightApp || (!isInsightApp && excludedTenantBDCFormationsCount === 1)) {
              tenantInstalledPackages.push(installedPackage.installationId);
            }
          }
        });
      });
      logInfo(`Found installed packages=${tenantInstalledPackages.length} for tenantId=${tenantId}.`, { context });
    } catch (err) {
      logError(`Failed to get installed packages for system tenantId=${tenantId}: ${err}`, { context });
    }
    return tenantInstalledPackages;
  }
}
