/** @format */
import { AuthenticationMode, HttpMethod, httpClient } from "@sap/dwc-http-client";
import Status from "http-status-codes";
import { ApplicationNamespace, SystemInfo } from "../../../shared/bdccockpit/Types";
import { IRequestContext } from "../../repository/security/common/common";
import { ExternalCallCategory } from "../../repository/security/common/externalCallTypes";
import { ClassName, NavigationLinksField, PropertiesField, RelationshipDirection } from "../catalog/CatalogConstants";
import {
  relatedObjectSelectorsInstallToFormation,
  relatedSystemSelectorsDspSacSource,
} from "../catalog/CatalogPayloadParts";
import { buildDatasphereCatalogURL } from "../catalog/CatalogUtils";
import { relationshipSystemType } from "../catalog/catalogTypes";
import {
  dspApplicationNamespace,
  isApplicationNamespaceOfSourceType,
  sacApplicationNamespace,
} from "../systems/applicationNamespaceUtils";
import { SystemsService } from "../systems/systemsService";
import { getBdcLogger } from "../utils/logUtils";
import {
  BDCPackageInstallation,
  IAllObjectResponse,
  IBDCInstallationPayload,
  ILinksToSet,
  IPackagesResponse,
} from "./packageInstallationTypes";
import { sanitizePackageInstallationForLog } from "./packageInstallationUtils";

const CUSTOM_SPACE = "bdc";
const CALL_TIMEOUT = 2 * 60 * 1000;
const { logError, logInfo } = getBdcLogger(__filename);

export class PackageInstallationDao {
  private static instance: PackageInstallationDao;

  public static getInstance(): PackageInstallationDao {
    if (!PackageInstallationDao.instance) {
      PackageInstallationDao.instance = new PackageInstallationDao();
    }
    return PackageInstallationDao.instance;
  }

  public async createPackageInstallation(
    packageInstallation: BDCPackageInstallation,
    context: IRequestContext
  ): Promise<void> {
    const payload: IBDCInstallationPayload = await this.createInstallationPayload(packageInstallation, context);
    const deepseaBaseUrl = buildDatasphereCatalogURL(context);
    const url = `${deepseaBaseUrl}/v1/customSpaces/${CUSTOM_SPACE}/customClasses/${ClassName.INSTALLED_PACKAGE}/members`;

    try {
      const headers = {
        "content-type": "application/json",
        "x-sap-boc-tenant-id": context.tenantId || "",
        "x-sap-boc-user-id": context.userInfo.userId || "",
      };
      logInfo(
        `createPackageInstallation started: ${sanitizePackageInstallationForLog(
          packageInstallation
        )}, body: ${JSON.stringify(payload)}`,
        {
          context,
        }
      );
      const response = await httpClient.call({
        url,
        opts: {
          method: HttpMethod.POST,
          authentication: AuthenticationMode.Uaa,
          body: payload,
          acceptedStatusCodes: [],
          headers,
          requestContext: context,
          callCategory: ExternalCallCategory.OneDataCatalog,
          callTimeout: CALL_TIMEOUT,
        },
      });

      if (response.status !== Status.CREATED) {
        throw new Error(`Response status code:${response.status}`);
      }
      logInfo(`createPackageInstallation ended, response body: ${JSON.stringify(response.body)}`, {
        context,
      });
    } catch (error) {
      logError(`BDC Cockpit catalog error for url ${url}\n${error}`, {
        context,
      });
      throw error;
    }
  }

  public async deletePackageInstallation(installationId: string, context: IRequestContext): Promise<void> {
    const deepseaBaseUrl = buildDatasphereCatalogURL(context);
    const url = `${deepseaBaseUrl}/v1/customSpaces/${CUSTOM_SPACE}/customClasses/${ClassName.INSTALLED_PACKAGE}/members/${installationId}`;

    try {
      const headers = {
        "content-type": "application/json",
        "x-sap-boc-tenant-id": context.tenantId || "",
        "x-sap-boc-user-id": context.userInfo.userId || "",
      };
      const response = await httpClient.call({
        url,
        opts: {
          method: HttpMethod.DELETE,
          authentication: AuthenticationMode.Uaa,
          acceptedStatusCodes: [Status.OK],
          headers,
          requestContext: context,
          callCategory: ExternalCallCategory.OneDataCatalog,
          callTimeout: CALL_TIMEOUT,
        },
      });
      if (response.status !== Status.OK) {
        throw new Error(`Can't delete installationId: ${installationId}. Response status code:${response.status}`);
      }
      logInfo(`deletePackageInstallation ended, URL: ${url}, response body: ${JSON.stringify(response.body)}`, {
        context,
      });
    } catch (error) {
      logError(`BDC Cockpit catalog error deletePackageInstallation, for url ${url}\n${error}`, {
        context,
      });
      throw error;
    }
  }

  public async getAllPackageInstallationsWithoutRelations(context: IRequestContext): Promise<IPackagesResponse> {
    const deepseaBaseUrl = buildDatasphereCatalogURL(context);
    const url = `${deepseaBaseUrl}/v1/customSpaces/${CUSTOM_SPACE}/dependencyQuery`;
    try {
      const headers = {
        "content-type": "application/json",
        "x-sap-boc-tenant-id": context.tenantId || "",
        "x-sap-boc-user-id": context.userInfo.userId || "",
      };
      const bodyString: string = JSON.stringify({
        rootObjectSelector: {
          className: ClassName.INSTALLED_PACKAGE,
        },
        propertySelectors: [
          { propertyId: PropertiesField.BDC_INSTALL_JSON },
          { propertyId: PropertiesField.COCKPIT_TENANT_ID },
          { propertyId: PropertiesField.BDC_DATA_PACKAGE_ID },
        ],
      });

      const response = await httpClient.call({
        url,
        opts: {
          method: HttpMethod.POST,
          authentication: AuthenticationMode.Uaa,
          body: bodyString,
          acceptedStatusCodes: [Status.OK],
          headers,
          requestContext: context,
          callCategory: ExternalCallCategory.OneDataCatalog,
          callTimeout: CALL_TIMEOUT,
        },
      });
      if (response.status !== Status.OK) {
        throw new Error(`Response status code:${response.status}`);
      }
      return response.body as IPackagesResponse;
    } catch (error) {
      logError(
        `BDC Cockpit catalog error get all installation packages for tenantId ${context.tenantId}, for url ${url}\n${error}`,
        {
          context,
        }
      );
      throw error;
    }
  }

  public async getAllPackageInstallationsWithComponents(context: IRequestContext): Promise<any> {
    const deepseaBaseUrl = buildDatasphereCatalogURL(context);
    const url = `${deepseaBaseUrl}/v1/customSpaces/${CUSTOM_SPACE}/dependencyQuery`;
    try {
      const headers = {
        "content-type": "application/json",
        "x-sap-boc-tenant-id": context.tenantId || "",
        "x-sap-boc-user-id": context.userInfo.userId || "",
      };
      const bodyString: string = JSON.stringify({
        rootObjectSelector: {
          className: ClassName.INSTALLED_PACKAGE,
        },
        propertySelectors: [
          { propertyId: PropertiesField.BDC_INSTALL_JSON },
          { propertyId: PropertiesField.COCKPIT_TENANT_ID },
          { propertyId: PropertiesField.BDC_DATA_PACKAGE_ID },
          { propertyId: PropertiesField.COMPONENT_ID },
        ],
        relatedSystemSelectors: relatedSystemSelectorsDspSacSource,
      });

      const response = await httpClient.call({
        url,
        opts: {
          method: HttpMethod.POST,
          authentication: AuthenticationMode.Uaa,
          body: bodyString,
          acceptedStatusCodes: [Status.OK],
          headers,
          requestContext: context,
          callCategory: ExternalCallCategory.OneDataCatalog,
          callTimeout: CALL_TIMEOUT,
        },
      });
      if (response.status !== Status.OK) {
        throw new Error(`Response status code:${response.status}`);
      }
      return response.body;
    } catch (error) {
      logError(
        `[OneDataCatalog] BDC Cockpit catalog error get all installation packages with components for tenantId ${context.tenantId}, for url ${url}\n${error}`,
        {
          context,
        }
      );
      throw error;
    }
  }

  /**
   *
   * @param context
   * @param packageId - if undefined - gets all
   * @returns
   */
  public async getPackageInstallationsWithRelationsByPackageId(
    context: IRequestContext,
    packageId: string | undefined
  ): Promise<IPackagesResponse> {
    const deepseaBaseUrl = buildDatasphereCatalogURL(context);
    const url = `${deepseaBaseUrl}/v1/customSpaces/${CUSTOM_SPACE}/dependencyQuery`;
    try {
      const headers = {
        "content-type": "application/json",
        "x-sap-boc-tenant-id": context.tenantId || "",
        "x-sap-boc-user-id": context.userInfo.userId || "",
      };
      const bodyObject: any = this.buildGetPackagesPayload(packageId);

      const response = await httpClient.call({
        url,
        opts: {
          method: HttpMethod.POST,
          authentication: AuthenticationMode.Uaa,
          body: JSON.stringify(bodyObject),
          acceptedStatusCodes: [Status.OK],
          headers,
          requestContext: context,
          callCategory: ExternalCallCategory.OneDataCatalog,
          callTimeout: CALL_TIMEOUT,
        },
      });
      logInfo(`Response body ${JSON.stringify(response.body)}`, { context });
      return response.body as IPackagesResponse;
    } catch (error) {
      logError(
        `[OneDataCatalog] BDC Cockpit catalog error get all installation packages for tenantId ${context.tenantId}, for url ${url}\n${error}`,
        {
          context,
        }
      );
      throw error;
    }
  }

  private buildGetPackagesPayload(packageId: string | undefined) {
    const bodyObject: any = {
      rootObjectSelector: {
        className: ClassName.INSTALLED_PACKAGE,
      },
      propertySelectors: [
        { propertyId: PropertiesField.BDC_INSTALL_JSON },
        { propertyId: PropertiesField.COCKPIT_TENANT_ID },
        { propertyId: PropertiesField.BDC_DATA_PACKAGE_ID },
      ],
      relatedSystemSelectors: relatedSystemSelectorsDspSacSource,
      relatedObjectSelectors: relatedObjectSelectorsInstallToFormation,
    };
    if (packageId) {
      bodyObject.rootObjectTagFilters = [
        {
          propertyId: PropertiesField.BDC_DATA_PACKAGE_ID,
          propertyValue: {
            valueString: packageId,
          },
        },
      ];
    }
    return bodyObject;
  }

  public async getPackageInstallationById(
    context: IRequestContext,
    installationId: string
  ): Promise<IPackagesResponse> {
    const deepseaBaseUrl = buildDatasphereCatalogURL(context);
    const url = `${deepseaBaseUrl}/v1/customSpaces/${CUSTOM_SPACE}/dependencyQuery`;

    try {
      const headers = {
        "content-type": "application/json",
        "x-sap-boc-tenant-id": context.tenantId || "",
        "x-sap-boc-user-id": context.userInfo.userId || "",
      };

      const response = await httpClient.call({
        url,
        opts: {
          method: HttpMethod.POST,
          authentication: AuthenticationMode.Uaa,
          body: JSON.stringify({
            rootObjectSelector: {
              className: ClassName.INSTALLED_PACKAGE,
              objectSelector: {
                names: [installationId],
              },
            },
            propertySelectors: [
              { propertyId: PropertiesField.BDC_INSTALL_JSON },
              { propertyId: PropertiesField.COCKPIT_TENANT_ID },
            ],
          }),
          acceptedStatusCodes: [Status.OK],
          headers,
          requestContext: context,
          callCategory: ExternalCallCategory.OneDataCatalog,
          callTimeout: CALL_TIMEOUT,
        },
      });

      if (response.status !== Status.OK) {
        throw new Error(`Response status code:${response.status}`);
      }
      logInfo(`[getPackageInstallation] Catalog response - ${JSON.stringify(response.body)}`, { context });
      return response.body as IPackagesResponse;
    } catch (error) {
      logError(
        `[OneDataCatalog] BDC Cockpit catalog error get package for installationId ${installationId}, for url ${url}\n${error}`,
        {
          context,
        }
      );
      throw error;
    }
  }

  public async updatePackageInstallation(
    context: IRequestContext,
    installationId: string,
    payload: string
  ): Promise<void> {
    const deepseaBaseUrl = buildDatasphereCatalogURL(context);
    const url = `${deepseaBaseUrl}/v1/customSpaces/${CUSTOM_SPACE}/customClasses/${ClassName.INSTALLED_PACKAGE}/members/${installationId}/propertyTags`;

    try {
      const headers = {
        "content-type": "application/json",
        "x-sap-boc-tenant-id": context.tenantId || "",
        "x-sap-boc-user-id": context.userInfo.userId || "",
      };

      const response = await httpClient.call({
        url,
        opts: {
          method: HttpMethod.PATCH,
          authentication: AuthenticationMode.Uaa,
          body: [
            {
              propertyId: PropertiesField.BDC_INSTALL_JSON,
              valueObjects: [
                {
                  valueText: payload,
                },
              ],
            },
          ],
          acceptedStatusCodes: [Status.OK],
          headers,
          requestContext: context,
          callCategory: ExternalCallCategory.OneDataCatalog,
          callTimeout: CALL_TIMEOUT,
        },
      });

      logInfo(
        `[updatePackageInstallation] installationId:${installationId}, Catalog response - ${JSON.stringify(
          response.body
        )}, URL: ${url}`,
        { context }
      );

      if (response.status !== Status.OK) {
        throw new Error(`Response status code:${response.status}`);
      }
    } catch (error) {
      logError(
        `[OneDataCatalog] BDC Cockpit catalog error update package for installationId ${installationId}, for url ${url}\n${error}`,
        {
          context,
        }
      );
      throw error;
    }
  }

  public async updateInstallationComponentsId(
    context: IRequestContext,
    installationId: string,
    componentsIds: string[]
  ): Promise<void> {
    const deepseaBaseUrl = buildDatasphereCatalogURL(context);
    const url = `${deepseaBaseUrl}/v1/customSpaces/${CUSTOM_SPACE}/customClasses/${ClassName.INSTALLED_PACKAGE}/members/${installationId}/propertyTags`;

    try {
      const headers = {
        "content-type": "application/json",
        "x-sap-boc-tenant-id": context.tenantId || "",
        "x-sap-boc-user-id": context.userInfo.userId || "",
      };

      const response = await httpClient.call({
        url,
        opts: {
          method: HttpMethod.PATCH,
          authentication: AuthenticationMode.Uaa,
          body: [
            {
              propertyId: PropertiesField.COMPONENT_ID,
              valueObjects: componentsIds.map((componentId) => ({
                valueString: componentId,
              })),
            },
          ],
          acceptedStatusCodes: [Status.OK],
          headers,
          requestContext: context,
          callCategory: ExternalCallCategory.OneDataCatalog,
          callTimeout: CALL_TIMEOUT,
        },
      });

      if (response.status !== Status.OK) {
        throw new Error(
          `[updateInstallationComponentsIds] Response status code: ${response.status}, response body: ${JSON.stringify(
            response.body
          )}`
        );
      }
      logInfo(
        `[updateInstallationComponentsIds] componentsIds:${JSON.stringify(
          componentsIds
        )}, Catalog response - ${JSON.stringify(response.body)}, URL: ${url}`,
        { context }
      );
    } catch (error) {
      logError(
        `[updateInstallationComponentsIds] BDC Cockpit catalog error update componentsIds, installationId: ${installationId}, componentsIds: ${JSON.stringify(
          componentsIds
        )}, for URL: ${url}\n${error}`,
        {
          context,
        }
      );
      throw error;
    }
  }

  public async getInstallationComponentsId(context: IRequestContext, installationId: string): Promise<string[]> {
    const deepseaBaseUrl = buildDatasphereCatalogURL(context);
    const url = `${deepseaBaseUrl}/v1/customSpaces/${CUSTOM_SPACE}/dependencyQuery`;
    try {
      const headers = {
        "content-type": "application/json",
        "x-sap-boc-tenant-id": context.tenantId || "",
        "x-sap-boc-user-id": context.userInfo.userId || "",
      };

      const bodyString: string = JSON.stringify({
        rootObjectSelector: {
          className: ClassName.INSTALLED_PACKAGE,
          objectSelector: {
            names: [installationId],
          },
        },
        propertySelectors: [{ propertyId: PropertiesField.COMPONENT_ID }],
      });

      const response = await httpClient.call({
        url,
        opts: {
          method: HttpMethod.POST,
          authentication: AuthenticationMode.Uaa,
          body: bodyString,
          acceptedStatusCodes: [Status.OK],
          headers,
          requestContext: context,
          callCategory: ExternalCallCategory.OneDataCatalog,
          callTimeout: CALL_TIMEOUT,
        },
      });
      if (response.status !== Status.OK) {
        throw new Error(`Response status code:${response.status}`);
      }

      const res = response.body as any;
      const valueStrings = res.allObjects
        .flatMap((obj: { propertyTags: any }) => obj.propertyTags)
        .filter((tag: { propertyId: string }) => tag.propertyId === PropertiesField.COMPONENT_ID)
        .flatMap((tag: { valueObjects: any }) => tag.valueObjects)
        .map((vo: { valueString: any }) => vo.valueString)
        .filter((value: undefined | string): value is string => value !== undefined);

      logInfo(
        `[getInstallationComponentsId], tenantId: ${
          context.tenantId
        }, installationId: ${installationId} catalog response - ${JSON.stringify(
          response.body
        )}, result: ${JSON.stringify(valueStrings)}`,
        { context }
      );

      return valueStrings;
    } catch (error) {
      logError(
        `[getInstallationComponentsId] BDC Cockpit catalog error getInstallationComponentsId for tenantId: ${context.tenantId}, installationId: ${installationId}, for url ${url}\n${error}`,
        {
          context,
        }
      );
      throw error;
    }
  }

  public async getComponentInstallRef(
    context: IRequestContext,
    componentId: string,
    relationshipSystem: relationshipSystemType
  ): Promise<IAllObjectResponse[]> {
    const deepseaBaseUrl = buildDatasphereCatalogURL(context);
    const url = `${deepseaBaseUrl}/v1/customSpaces/${CUSTOM_SPACE}/dependencyQuery`;
    try {
      const headers = {
        "content-type": "application/json",
        "x-sap-boc-tenant-id": context.tenantId || "",
        "x-sap-boc-user-id": context.userInfo.userId || "",
      };

      const bodyString: string = JSON.stringify({
        rootObjectSelector: {
          className: ClassName.INSTALLED_PACKAGE,
        },
        rootObjectTagFilters: [
          {
            propertyId: PropertiesField.COMPONENT_ID,
            propertyValue: {
              valueString: componentId,
            },
          },
        ],
        relatedSystemSelectors: [
          {
            relationship: {
              typeId: relationshipSystem,
              direction: RelationshipDirection.SOURCE_TO_TARGET,
            },
          },
        ],
      });

      const response = await httpClient.call({
        url,
        opts: {
          method: HttpMethod.POST,
          authentication: AuthenticationMode.Uaa,
          body: bodyString,
          acceptedStatusCodes: [Status.OK],
          headers,
          requestContext: context,
          callCategory: ExternalCallCategory.OneDataCatalog,
          callTimeout: CALL_TIMEOUT,
        },
      });
      if (response.status !== Status.OK) {
        throw new Error(`Response status code:${response.status}`);
      }

      logInfo(
        `[getComponentInstallRef], tenantId: ${
          context.tenantId
        }, componentId: ${componentId}, relationshipSystem: ${relationshipSystem}, request body:${bodyString},  Catalog response - ${JSON.stringify(
          response.body
        )}`,
        { context }
      );

      const res = response.body as any;
      return res.allObjects.filter((o: any) => o.className === ClassName.INSTALLED_PACKAGE) as IAllObjectResponse[];
    } catch (error) {
      logError(
        `BDC Cockpit catalog error get component installRef for tenantId: ${context.tenantId}, componentId: ${componentId}, relationshipSystem: ${relationshipSystem}, for url ${url}\n${error}`,
        {
          context,
        }
      );
      throw error;
    }
  }

  private async createInstallationPayload(
    packageInstallation: BDCPackageInstallation,
    context: IRequestContext
  ): Promise<IBDCInstallationPayload> {
    const s4System = packageInstallation.installationProperties.find((ip) =>
      isApplicationNamespaceOfSourceType(ip.applicationNamespace)
    );
    let formationCatalogUuid: string = packageInstallation.formationCatalogUuid!;
    if (!formationCatalogUuid) {
      formationCatalogUuid = s4System!.formationCatalogUuid!;
    }
    const systemsOfFormationAll = await SystemsService.getSystemsByFormationCatalogUuid(context, formationCatalogUuid);
    const systemsOfFormationRelevantWithSource = SystemsService.removeNonRelevantSourceSystems(
      context,
      systemsOfFormationAll,
      s4System?.systemTenant!
    );

    const linksToSet = this.getLinksToSetArray(systemsOfFormationRelevantWithSource, formationCatalogUuid);
    return {
      name: packageInstallation.installationId,
      propertyTags: [
        {
          propertyId: PropertiesField.BDC_INSTALL_JSON,
          valueObjects: [
            {
              valueText: JSON.stringify(packageInstallation),
            },
          ],
        },
        {
          propertyId: PropertiesField.COCKPIT_TENANT_ID,
          valueObjects: [
            {
              valueString: context.userInfo.tenantId!,
            },
          ],
        },
        {
          propertyId: PropertiesField.BDC_DATA_PACKAGE_ID,
          valueObjects: [
            {
              valueString: packageInstallation.originalPackage.id,
            },
          ],
        },
      ],
      linksToSet,
    };
  }

  private getLinksToSetArray(systems: SystemInfo[], id: string): ILinksToSet[] {
    const linksToSetArray: ILinksToSet[] = [];

    systems.forEach((system) => {
      const linksToSet = this.getLinksToSetByApplicationNamespace(system.applicationNamespace, system.systemId);
      if (linksToSet) {
        linksToSetArray.push(linksToSet);
      }
    });

    if (linksToSetArray.length) {
      linksToSetArray.push(this.getFormationLinksToSet(id));
    }
    return linksToSetArray;
  }

  private getLinksToSetByApplicationNamespace(
    applicationNamespace: ApplicationNamespace,
    systemId: string
  ): ILinksToSet | undefined {
    const linksToSet: ILinksToSet = {} as ILinksToSet;

    switch (applicationNamespace) {
      case dspApplicationNamespace:
        linksToSet.relationshipTypeId = NavigationLinksField.REL_TYPE_ID_DSP;
        break;
      case sacApplicationNamespace:
        linksToSet.relationshipTypeId = NavigationLinksField.REL_TYPE_ID_SAC;
        break;
      default:
        if (isApplicationNamespaceOfSourceType(applicationNamespace)) {
          linksToSet.relationshipTypeId = NavigationLinksField.REL_TYPE_ID_SOURCE;
        } else {
          return;
        }
    }

    linksToSet.links = [
      {
        systemId,
        relationshipDirection: RelationshipDirection.SOURCE_TO_TARGET,
      },
    ];

    return linksToSet;
  }

  private getFormationLinksToSet(systemId: string): ILinksToSet {
    const linksToSet: ILinksToSet = {} as ILinksToSet;
    linksToSet.relationshipTypeId = NavigationLinksField.REL_TYPE_ID_FORMATION;

    linksToSet.links = [
      {
        linkedObjectId: systemId,
        relationshipDirection: RelationshipDirection.SOURCE_TO_TARGET,
      },
    ];

    return linksToSet;
  }
}
