/** @format */
import { Request, Response } from "express";
import Status from "http-status-codes";
import { MISSING_ENTITLEMENT } from "../../../shared/bdccockpit/Constants";
import {
  BDCInstalledPackage,
  BDCPackageInstallationProperties,
  BDCRequestContext,
  BDCSystem,
  InstallationResponseBody,
  SystemInfo,
} from "../../../shared/bdccockpit/Types";
import { IRequestContext } from "../../repository/security/common/common";
import { sendErrorResponse } from "../../server/errorResponse";
import {
  getMissingEntitlementDetails,
  updateGracePeriodForInstalledPackagesByEntitlement,
} from "../entitlement/entitlementValidator";
import { InstallationManager } from "../lifecycleManagement/installationManager";
import { UninstallationManager } from "../lifecycleManagement/uninstallationManager";
import { isApplicationNamespaceOfSourceType, s4ApplicationNamespace } from "../systems/applicationNamespaceUtils";
import { SystemsService } from "../systems/systemsService";
import { getBdcLogger } from "../utils/logUtils";
import { PackageInstallationService } from "./packageInstallationService";

import { BDCPackageType, BDCUninstallationOperation } from "../../../shared/bdccockpit/Enums";
import { FormationService } from "../formation/formationService";
import { InstallationHealth } from "../lifecycleManagement/installationHealth";
import { PackageUtils } from "../packages/packageUtils";
import { getSystemNameByApplicationNamespace } from "../packages/packagesManager";
import { PackagesService } from "../packages/packagesService";
import { BDCPackage } from "../packages/packagesTypes";
import { ConnectionCheckResult } from "../systems/systemsTypes";
import { notificationsHelper } from "../utils/notificationUtils";
import {
  PackageInstallationFlowCommonProperties,
  createPackageInstallationFlowCommonProperties,
} from "./packageInstallationFlowCommonProperties";
import {
  BDCPackageInstallation,
  BDCPackageInstallationWithStatus,
  ComponentInstallationStatus,
  ExistingInstallationOperaitonResult,
} from "./packageInstallationTypes";
import {
  enrichWithSpacesData,
  extractStatusFromPackageInstallation,
  getInsideAppLinksArr,
  getMilisecondsOfDate,
  getSourceSystem,
  isSameMajorDifferentVersion,
} from "./packageInstallationUtils";

const { logInfo, logError } = getBdcLogger(__filename);

export async function installPackage(req: Request, res: Response<InstallationResponseBody>) {
  const {
    packageId,
    installationProperties,
  }: { packageId: string; installationProperties: BDCPackageInstallationProperties[] } =
    typeof req.body === "string" ? JSON.parse(req.body) : req.body;
  logInfo(
    `Install Package request accepted: packageId: ${packageId}, installationProperties: ${JSON.stringify(
      installationProperties
    )}`,
    { context: req.context }
  );

  const responseBody: InstallationResponseBody = {};

  try {
    const pifcop = createPackageInstallationFlowCommonProperties(req.context, installationProperties);
    await buildInstallationProperties(pifcop);
    const newInstallationProperties = pifcop.installationProperties;
    if (pifcop.s4InstallationFormation?.assignmentId) {
      const connectionCheckResult = await SystemsService.checkFosConnection({
        context: pifcop.context,
        assignmentId: pifcop.s4InstallationFormation.assignmentId,
      });
      if (!connectionCheckResult) {
        // communication error, or not implemented yet
        logInfo(
          `FOS connection could not be determined for assignmentId: ${pifcop.s4InstallationFormation.assignmentId}`,
          {
            context: pifcop.context,
          }
        );
      }
      responseBody.connectionCheckResult = connectionCheckResult;
      if (responseBody.connectionCheckResult?.success === false) {
        sendErrorResponse(req.context, "failed to install package", {
          err: new Error(responseBody.connectionCheckResult.errorMessage),
        });
        return;
      }
    }

    logInfo(`New Installation Properties: ${JSON.stringify(newInstallationProperties)}`, { context: req.context });
    const bdcPackage = await PackagesService.getPackageById(req.context, packageId);
    await notificationsHelper.sendOnboardStartedNotification({
      context: req.context,
      packageInfo: bdcPackage!,
    });
    const installationId = await InstallationManager.installPackage(req.context, packageId, newInstallationProperties);
    if (installationId === MISSING_ENTITLEMENT) {
      const missingEntitlementDetails = await getMissingEntitlementDetails(req.context, newInstallationProperties);
      res.status(Status.OK).send({ missingEntitlementDetails });
      return;
    }
    res.status(Status.OK).send({ ...responseBody, installationId });
  } catch (err) {
    sendErrorResponse(req.context, "failed to install package", { err });
  }
}

export async function uninstallPackage(req: Request, res: Response<InstallationResponseBody>) {
  const operation = req.query?.operation as BDCUninstallationOperation;
  const installationId = req.params?.installationId;

  logInfo(`New Uninstallation request: installationId[${installationId}], operation[${operation}]`, {
    context: req.context,
  });

  try {
    const result = await UninstallationManager.uninstallPackage({
      context: req.context,
      packageId: installationId,
      operation,
      requiredFeatureFlags: {
        [BDCUninstallationOperation.RETRY]: ["DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP"],
        [BDCUninstallationOperation.CLEANUP]: ["DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP"],
        [BDCUninstallationOperation.UNINSTALL]: ["DWCO_BDC_MANUAL_OFFBOARDING"],
      },
    });
    res.status(Status.OK).send(result);
    return;
  } catch (err) {
    sendErrorResponse(req.context, "failed to uninstall package", { err });
  }
}

async function buildInstallationProperties(
  pifcop: PackageInstallationFlowCommonProperties
): Promise<BDCPackageInstallationProperties[]> {
  await pifcop.setS4InstallationProperties();
  const newInstallationProperties = await pifcop.getNewInstallationPropertiesBySystemsOfFormation();
  pifcop.installationProperties = newInstallationProperties;
  return newInstallationProperties;
}

export async function reinstallPackage(req: Request, res: Response<InstallationResponseBody>) {
  const installationId = req.params?.installationId;
  const retry = req.query?.retry === "true";
  let errorMessage = "";
  let connectionCheckResult: ConnectionCheckResult | undefined;

  try {
    if (retry) {
      errorMessage = `failed to retry the package installation, installationId: ${installationId}`;
      const result = await InstallationManager.retryPackage(req.context, installationId);
      connectionCheckResult = result.connectionCheckResult;
    } else {
      errorMessage = `failed to update package, installationId: ${installationId}`;
      const result = await InstallationManager.updatePackage(req.context, installationId);
      connectionCheckResult = result.connectionCheckResult;
    }
  } catch (err) {
    sendErrorResponse(req.context, errorMessage, { err });
    return;
  }
  res.status(Status.OK).send({ installationId, ...(connectionCheckResult && { connectionCheckResult }) });
}

export async function deletePackage(req: Request, res: Response<InstallationResponseBody>) {
  const installationId = req.params?.installationId;
  let deleteResult: ExistingInstallationOperaitonResult = { installationId };

  deleteResult = await UninstallationManager.forceDeletePackageInstallation(req.context, installationId);

  res.status(Status.OK).send(deleteResult);
}

export async function getInstalledPackagesByTenantId(req: Request, res: Response) {
  res.send({});
}

export async function getInstalledPackageByInstallationId(req: Request, res: Response<BDCInstalledPackage[]>) {
  try {
    const installationId = req.params?.installationId;
    const installedPackage: BDCPackageInstallation = await PackageInstallationService.getPackageInstallation(
      req.context,
      installationId
    );

    const packages = await PackagesService.getPackages(req.context);
    const srcPackage = packages?.find((pack) => installedPackage.originalPackage.id === pack.id);
    const installedPackageUi: BDCInstalledPackage = await getDetailedInstalledPackageWithTargets(
      req.context,
      srcPackage,
      installedPackage
    );

    if (!installedPackageUi) {
      res.status(Status.NOT_FOUND).send([]);
      return;
    }
    res.status(Status.OK).send([installedPackageUi]);
  } catch (err) {
    sendErrorResponse(req.context, "getInstalledPackageByInstallationId", { err });
  }
}

export async function getInstalledPackages(req: Request, res: Response) {
  try {
    let packages = await getInstallationPackages(req.context, false);

    if (await (req.context as IRequestContext).isFeatureFlagActive("DWCO_BDC_ENTITLEMENT_CHECK")) {
      packages = await updateGracePeriodForInstalledPackagesByEntitlement(req.context, packages);
    }
    res.send(packages);
  } catch (err) {
    sendErrorResponse(req.context, "getInstalledPackagesFailed", { err });
  }
}

/**
 * Retrieve Installation Packages - enriched with addition data and filtering.
 *
 * @param {RequestContext} oRequestContext request context
 * @returns {object} Installation packages with items and fixed items
 * @memberof PackagesInstallationProvider
 */
async function getInstallationPackages(
  context: IRequestContext,
  isDev?: boolean
): Promise<Array<Partial<BDCInstalledPackage>>> {
  const installedPackagesUi: Array<Partial<BDCInstalledPackage>> = [];
  const installedPackagesWithStatus: BDCPackageInstallationWithStatus[] = [];
  const packages = await PackagesService.getPackages(context, undefined, isDev);
  const installedPackages = await PackageInstallationService.getFlattenedInstalledPackages(context);
  const systemInfo = await SystemsService.getAllSystems(context);

  await Promise.all(
    installedPackages.map(async (installedPack) => {
      await prepareGetOfSingleInstalledPackage(
        context,
        installedPack,
        installedPackagesUi,
        installedPackagesWithStatus
      );
    })
  );

  InstallationHealth.installationHealth(context, installedPackagesWithStatus);
  return installedPackagesUi;

  async function prepareGetOfSingleInstalledPackage(
    context: IRequestContext,
    installedPack: BDCPackageInstallation,
    installedPackagesUi: Array<Partial<BDCInstalledPackage>>,
    installedPackagesWithStatus: BDCPackageInstallationWithStatus[]
  ) {
    try {
      const origPackage = packages?.find((origPack) => installedPack?.originalPackage?.id === origPack?.id);
      const installedPackage = await buildBDCInstalledPackage(context, origPackage, installedPack, systemInfo);
      populateInstallLocationField(installedPack, systemInfo, installedPackage);
      installedPackagesUi.push(installedPackage);
      installedPackagesWithStatus.push({ ...installedPack, status: installedPackage.status });
    } catch (error) {
      logError(`Cannot build installedPackage: ${error}`, { context });
    }
  }
}

export async function getSystemLandscape(req: Request, res: Response) {
  try {
    const systemInfo = await SystemsService.getAllSystems(req.context);
    res.send(systemInfo);
  } catch (err) {
    sendErrorResponse(req.context, "getSystemLandscape", { err });
  }
}

async function getDetailedInstalledPackageWithTargets(
  context: BDCRequestContext,
  updatedPackage: BDCPackage | undefined,
  packageInstallation: BDCPackageInstallation
): Promise<BDCInstalledPackage> {
  const originalPackage = packageInstallation.originalPackage;
  const { products, contents } = await PackageUtils.getComponentsByType(context, packageInstallation);
  await enrichWithSpacesData(context, contents, packageInstallation.dspFolders);
  const minSystemVersion = PackageUtils.getMinVersionFromArray(products?.map((product) => product.systemVersion!));
  logInfo(`The minSystemVersion of ${JSON.stringify(products)} is ${minSystemVersion}`, { context });

  const systems = await FormationService.getFormationContextAvailableForInstallationPackage(
    context,
    packageInstallation.originalPackage
  );
  // get source and installation locations names
  const sourceInstallationProperty = packageInstallation.installationProperties.find((sip) =>
    isApplicationNamespaceOfSourceType(sip.applicationNamespace)
  );
  const sourceSystemOrigin = systems.find(
    (system) => system.applicationTenantId === sourceInstallationProperty?.systemTenant
  );
  const installationLocationName = sourceSystemOrigin?.formations.find(
    (formation) => formation.catalogUuid === sourceInstallationProperty?.formationCatalogUuid
  )?.installationLocationName;
  const sourceSystem: BDCSystem = {
    applicationNamespace: sourceSystemOrigin?.applicationNamespace!,
    tenant: sourceSystemOrigin?.applicationTenantId!,
    name: sourceSystemOrigin?.name!,
    version: sourceSystemOrigin?.applicationVersion!,
  };
  return {
    correlationId: packageInstallation.correlationId,
    installationId: packageInstallation.installationId,
    insightAppLinks: getInsideAppLinksArr(packageInstallation.storyLinks!),
    workspaceLinks: getInsideAppLinksArr(packageInstallation.workSpaceLinks!),
    isPackageUpdateAvailable: updatedPackage
      ? isSameMajorDifferentVersion(updatedPackage.version, originalPackage.version)
      : false,
    isBdcPackageAvailable: !!updatedPackage,
    status: extractStatusFromPackageInstallation(packageInstallation),
    installedUTC: getMilisecondsOfDate(packageInstallation.startTime),
    systems,
    products,
    contents,
    id: originalPackage.id,
    name: originalPackage.name,
    preview: originalPackage.preview,
    creationDate: originalPackage.creationDate,
    type: originalPackage.type,
    systemType: getSystemNameByApplicationNamespace(s4ApplicationNamespace),
    version: originalPackage.version,
    category: originalPackage.category,
    description: originalPackage.description,
    supportedSystemVersion: minSystemVersion,
    sourceSystem,
    installationLocationName,
    componentsInstallationStatus: extendStatusObjects(
      packageInstallation,
      packageInstallation.componentsInstallationStatus
    ),
    componentsUninstallationStatus: extendStatusObjects(
      packageInstallation,
      packageInstallation.componentsUninstallationStatus
    ),
  };
}

function extendStatusObjects(
  packageInstallation: BDCPackageInstallation,
  statusObjects: ComponentInstallationStatus[] | undefined
) {
  if (!statusObjects) {
    return statusObjects;
  }
  const failedStatusObjects = statusObjects.filter((so) => so.status === "FAILED" || so.status === "ERROR");
  const extendedStatusObjects: any[] = [];
  failedStatusObjects.forEach((statusObject) => {
    const originalComponent = packageInstallation.originalPackage.components.find(
      (originalComponent) => originalComponent.componentId === statusObject.componentId
    )!;
    const extendedStatusObject = {
      name: originalComponent.name,
      category: originalComponent.category,
      provider: originalComponent.provider,
      version: originalComponent.version,
      ...statusObject,
    };
    extendedStatusObjects.push(extendedStatusObject);
  });
  return extendedStatusObjects;
}

async function buildBDCInstalledPackage(
  context: IRequestContext,
  origPackage: BDCPackage | undefined,
  installedPack: BDCPackageInstallation,
  systemInfo: SystemInfo[]
): Promise<Partial<BDCInstalledPackage>> {
  const installedOriginalPackage = installedPack.originalPackage;
  const { products } = await PackageUtils.getComponentsByType(context, installedPack);
  const system = getSourceSystem(context, installedPack, systemInfo, products);
  return {
    correlationId: installedPack.correlationId,
    installationId: installedPack.installationId,
    isPackageUpdateAvailable: origPackage
      ? isSameMajorDifferentVersion(installedOriginalPackage.version, origPackage.version)
      : false,
    status: extractStatusFromPackageInstallation(installedPack),
    installedUTC: getMilisecondsOfDate(installedPack.startTime),
    sourceSystem: system,
    id: installedOriginalPackage.id,
    name: installedOriginalPackage.name,
    creationDate: installedOriginalPackage.creationDate,
    type: installedOriginalPackage.type,
    systemType: getSystemNameByApplicationNamespace(system.applicationNamespace),
    version: installedOriginalPackage.version,
    category: installedOriginalPackage.category,
    description: installedOriginalPackage.description,
    lastModifiedTime: installedPack.lastModifiedTime,
  };
}

function populateInstallLocationField(
  installedPack: BDCPackageInstallation,
  systemInfo: SystemInfo[],
  installedPackage: Partial<BDCInstalledPackage>
) {
  // data package doesn't have install location
  if (installedPack.originalPackage.type === BDCPackageType.DATA_PACKAGE) {
    return;
  }
  const dspInstalledProperty = installedPack.installationProperties.find(
    (ip) => ip.applicationNamespace === "sap.datasphere"
  );
  const sacInstalledProperty = installedPack.installationProperties.find(
    (ip) => ip.applicationNamespace === "sap.analytics"
  );
  // although installation properties also have name, it can be changed and not reflected, therefore we get names from the system info
  const dspSystem = systemInfo.find((si) => si.applicationTenantId === dspInstalledProperty?.systemTenant);
  const sacSystem = systemInfo.find((si) => si.applicationTenantId === sacInstalledProperty?.systemTenant);
  const formationName = findFormationNameInSystemInfoByCatalogUuid(systemInfo, installedPack.formationCatalogUuid);
  installedPackage.installLocation = `${formationName || ""}, SAP Datasphere(${dspSystem?.name}), SAP Analytics Cloud(${
    sacSystem?.name
  })`;
}

function findFormationNameInSystemInfoByCatalogUuid(
  systemInfo: SystemInfo[],
  formationCatalogUuid: string | undefined
): string | undefined {
  let formationName: string | undefined;
  if (formationCatalogUuid) {
    for (const sysInfo of systemInfo) {
      const formationMatch = sysInfo.formations.find((formation) => formation.catalogUuid === formationCatalogUuid);
      formationName = formationMatch?.formationName;
      if (formationName) {
        break;
      }
    }
  }
  return formationName;
}
