/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import * as semver from "semver";
import { BDCPackageStatus, BDCUninstallationOperation } from "../../../shared/bdccockpit/Enums";
import {
  ACNFolder,
  AdditionalInstallationProperty,
  AdditionalInstallationPropertyName,
  BDCDataProduct,
  BDCInstalledContent,
  BDCInstalledProduct,
  BDCSystem,
  SystemInfo,
} from "../../../shared/bdccockpit/Types";
import { semverCoerce } from "../../../shared/bdccockpit/utils/semverUtils";
import {
  InstallationMessage,
  MessageAction,
  MessageTooOldError,
} from "../../messageQueuing/consumer/PackageInstallationConsumer";
import { PropertiesField } from "../catalog/CatalogConstants";
import { relationshipSystemType } from "../catalog/catalogTypes";
import { PackageUtils } from "../packages/packageUtils";
import { BDCPackageComponent } from "../packages/packagesTypes";
import { dspProvider, isApplicationNamespaceOfSourceType } from "../systems/applicationNamespaceUtils";
import { createUninstallationFailureException } from "../utils/BDCException";
import { getBdcLogger } from "../utils/logUtils";
import {
  BDCPackageInstallation,
  ComponentInstallRef,
  IPackagesResponse,
  IValueObjectsTypeString,
  IValueObjectsTypeText,
  InstallationStatusCode,
} from "./packageInstallationTypes";

const { logError, logInfo, logErrorWithOptionalContext } = getBdcLogger(__filename);

export function extractStatusFromPackageInstallation(packageInstallation: BDCPackageInstallation): BDCPackageStatus {
  return packageInstallation.componentsUninstallationStatus
    ? getUnInstallStatus(packageInstallation)
    : getInstallStatus(packageInstallation);
}

export function convertOperationToMessageAction(operation?: BDCUninstallationOperation): MessageAction {
  switch (operation) {
    case BDCUninstallationOperation.RETRY:
      return MessageAction.RETRY_UNINSTALL;
    case BDCUninstallationOperation.UNINSTALL:
      return MessageAction.UNINSTALL;
    case BDCUninstallationOperation.CLEANUP:
      return MessageAction.CLEANUP;
  }
  return MessageAction.UNINSTALL;
}

export function isSameMajorDifferentVersion(version1: string, version2: string): boolean {
  const parsedVersion1 = semver.parse(version1);
  const parsedVersion2 = semver.parse(version2);

  if (!parsedVersion1 || !parsedVersion2) {
    throw new Error("Invalid version format");
  }

  return (
    parsedVersion1.major === parsedVersion2.major &&
    (parsedVersion1.minor !== parsedVersion2.minor || parsedVersion1.patch !== parsedVersion2.patch)
  );
}

export function getBDCPackageStatus(installationStatus: InstallationStatusCode): BDCPackageStatus {
  switch (installationStatus) {
    case "DONE":
      return BDCPackageStatus.INSTALLED;
    case "FAILED":
    case "ERROR":
      return BDCPackageStatus.INSTALLATION_FAILED;
    case "EXECUTING":
    case "PENDING":
      return BDCPackageStatus.INSTALLING;
    default:
      return BDCPackageStatus.INSTALLED;
  }
}

export function getInsideAppLinksArr(insideAppLinks: Map<string, string>): any[] {
  const insideAppLinksArr: any[] = [];
  if (insideAppLinks && insideAppLinks.keys()) {
    for (const key of insideAppLinks.keys()) {
      insideAppLinksArr.push({ name: key, url: insideAppLinks.get(key) });
    }
  }
  return insideAppLinksArr;
}

export function getMilisecondsOfDate(date: string | Date): string {
  const isString = typeof date === "string";
  const dateFormat: Date = isString ? new Date(date) : (date as Date);
  if (isString) {
    if (isNaN(dateFormat.getTime())) {
      return date;
    }
  }

  return dateFormat.getTime().toString();
}

export async function enrichWithSpacesData(
  context: IRequestContext,
  contents: BDCInstalledContent[],
  dspFolders?: ACNFolder[]
) {
  logInfo(`Space data for package is: ${JSON.stringify(dspFolders)}, contents: ${JSON.stringify(contents)}`, {
    context,
  });
  dspFolders?.forEach((folder) => {
    contents.forEach((content) => {
      if (content.provider === dspProvider && content.name === folder.packageName) {
        content.spaces = folder.packageFolders;
      }
    });
  });
}

export function getSourceSystem(
  context: IRequestContext,
  packageInstallation: BDCPackageInstallation,
  systemsInfo: SystemInfo[],
  products: BDCInstalledProduct[] = []
): BDCSystem {
  const sourceInstallationProperty = packageInstallation.installationProperties?.find((installationProperty) =>
    isApplicationNamespaceOfSourceType(installationProperty.applicationNamespace)
  );
  if (!sourceInstallationProperty) {
    throw new Error(`no installation properties: ${packageInstallation.installationProperties}`);
  }
  const currentSystemInfo = systemsInfo.find(
    (sysInfo) => sysInfo.applicationTenantId === sourceInstallationProperty.systemTenant
  );
  let result;
  if (!currentSystemInfo) {
    logError(
      `no system info with tenant: ${sourceInstallationProperty.systemTenant} found in system info: ${systemsInfo}`,
      {
        context,
      }
    );
    result = {
      applicationNamespace: sourceInstallationProperty?.applicationNamespace ?? "",
      tenant: sourceInstallationProperty?.systemTenant ?? "",
      name: sourceInstallationProperty?.systemName ?? "",
      version: "0",
    };
  } else {
    result = {
      applicationNamespace: currentSystemInfo?.applicationNamespace ?? "",
      tenant: currentSystemInfo?.applicationTenantId ?? "",
      name: currentSystemInfo?.name ?? "",
      version: currentSystemInfo?.applicationVersion ?? "0",
    };
  }

  return {
    ...result,
    version: semverCoerce(result.version),
    productsCompatibility: PackageUtils.getProductsCompatibility(result.version, products),
  };
}

export function getAllPackageInstallationsFromResponse(
  packages: IPackagesResponse
): Map<string, BDCPackageInstallation> {
  if (!packages || !packages.allObjects || packages.allObjects.length === 0) {
    return new Map();
  }

  const packageInstallations: Map<string, BDCPackageInstallation> = new Map();
  packages.allObjects.forEach((objectResponse) => {
    const installationId: string = objectResponse.name;
    const relationToFormation = objectResponse.navigationLinks?.find(
      (nl) => nl.relationshipTypeId === "CRT_BDC_INSTALL_TO_FORMATION"
    );
    const formationCatalogUuid = relationToFormation?.links[0]?.refId;
    let packageInstallation: BDCPackageInstallation;
    objectResponse.propertyTags.forEach((propertyTagResponse) => {
      if (packageInstallation) {
        return;
      }
      if (propertyTagResponse.propertyId === PropertiesField.BDC_INSTALL_JSON) {
        propertyTagResponse.valueObjects.forEach((valueObjectType) => {
          if (packageInstallation) {
            return;
          }
          const valueObjectsTypeText = valueObjectType as IValueObjectsTypeText;
          if (valueObjectsTypeText) {
            const packageInstallationAny = JSON.parse(valueObjectsTypeText.valueText);
            convertInstallationStatusToComponentInstallationDetails(
              packageInstallationAny.componentsInstallationStatus
            );
            convertInstallationStatusToComponentInstallationDetails(
              packageInstallationAny.componentsUninstallationStatus
            );
            packageInstallation = packageInstallationAny as BDCPackageInstallation;
            packageInstallation.navigationLinks = objectResponse.navigationLinks;
            if (formationCatalogUuid) {
              packageInstallation.formationCatalogUuid = formationCatalogUuid;
            }
            packageInstallations.set(installationId, packageInstallation);
          }
        });
      }
    });
  });
  return packageInstallations;
}

export function getAllPackageInstallationsWithComponentsFromResponse(
  packages: any
): Map<string, BDCPackageInstallation> {
  if (!packages || !packages.allObjects || packages.allObjects.length === 0) {
    return new Map();
  }

  const packageInstallations: Map<string, BDCPackageInstallation> = new Map();
  packages.allObjects.forEach((objectResponse: any) => {
    if (objectResponse.className !== "installedPackage") {
      return;
    }

    const installationId: string = objectResponse.name;
    const relationToFormation = objectResponse.navigationLinks?.find(
      (nl: any) => nl.relationshipTypeId === "CRT_BDC_INSTALL_TO_FORMATION"
    );
    const formationCatalogUuid = relationToFormation?.links[0]?.refId;
    let packageInstallation: BDCPackageInstallation | undefined;
    const installedComponentsId: string[] = [];
    objectResponse.propertyTags.forEach((propertyTagResponse: any) => {
      if (propertyTagResponse.propertyId === PropertiesField.BDC_INSTALL_JSON) {
        propertyTagResponse.valueObjects.forEach((valueObjectType: any) => {
          const valueObjectsTypeText = valueObjectType as IValueObjectsTypeText;
          if (valueObjectsTypeText) {
            const packageInstallationAny = JSON.parse(valueObjectsTypeText.valueText);
            convertInstallationStatusToComponentInstallationDetails(
              packageInstallationAny.componentsInstallationStatus
            );
            convertInstallationStatusToComponentInstallationDetails(
              packageInstallationAny.componentsUninstallationStatus
            );
            packageInstallation = packageInstallationAny as BDCPackageInstallation;
            packageInstallation.navigationLinks = objectResponse.navigationLinks;
            if (formationCatalogUuid) {
              packageInstallation.formationCatalogUuid = formationCatalogUuid;
            }
          }
        });
      } else if (propertyTagResponse.propertyId === PropertiesField.COMPONENT_ID) {
        propertyTagResponse.valueObjects.forEach((valueObjectType: any) => {
          const valueObjectsTypeString = valueObjectType as IValueObjectsTypeString;
          if (valueObjectsTypeString) {
            installedComponentsId.push(valueObjectsTypeString.valueString);
          }
        });
      }
    });
    if (packageInstallation) {
      packageInstallation.installedComponentsId = installedComponentsId;
      packageInstallations.set(installationId, packageInstallation);
    }
  });
  return packageInstallations;
}

export function extractTargetSystemIdFromInstallationPackage(
  context: IRequestContext,
  installationPackage: any
): string | undefined {
  return extractNavigationLinkRefIdFromInstallationPackage(
    context,
    installationPackage,
    "CRT_BDC_INSTALL_TO_FORMATION"
  );
}

export function extractSourceSystemIdFromInstallationPackage(
  context: IRequestContext,
  installationPackage: any
): string | undefined {
  return extractNavigationLinkRefIdFromInstallationPackage(context, installationPackage, "CRT_BDC_DP_SOURCE_SYSTEM");
}

export function getInstalledComponentsId(context: IRequestContext, bdcPackageInstallation: BDCPackageInstallation) {
  return bdcPackageInstallation.componentsInstallationStatus
    .filter((installedComp) => installedComp.status === "DONE")
    .map((installedComp) => {
      const originalComponent = bdcPackageInstallation.originalPackage.components.find(
        (origComponent) => installedComp.componentId === origComponent.componentId
      );
      if (!originalComponent) {
        logError(`Original component not found for installed component id: ${installedComp.componentId}`, {
          context,
        });
        return "";
      }
      return getInstalledComponentId(originalComponent);
    })
    .filter((id) => id.length > 0);
}

export function getInstalledComponentId(originalComponent: BDCPackageComponent): string {
  if (originalComponent.category === "DataProduct") {
    return (originalComponent as BDCDataProduct).ordid;
  } else {
    return `${originalComponent.provider}:${originalComponent.name}:${semver.major(originalComponent.version)}`;
  }
}
/**
 * packageInstallation can be too big for log tracing and exceed allowed size
 * removing workSpaceLinks, storyLinks and originalPackage will reducer the size of the printed object
 *
 * @param packageInstallation
 * @returns
 */
export function sanitizePackageInstallationForLog(packageInstallation: any): string {
  if (packageInstallation) {
    const fieldsToOmit = new Set(["workSpaceLinks", "storyLinks", "originalPackage"]);
    return JSON.stringify(packageInstallation, (key, value) => (fieldsToOmit.has(key) ? undefined : value), 2);
  } else {
    return "undefined";
  }
}
export function validateBdcPackageInstallationAndMessage(
  bdcPackageInstallation: BDCPackageInstallation,
  currentInstallationMessage: InstallationMessage,
  context: IRequestContext
) {
  if (!bdcPackageInstallation) {
    throw new Error(`package installation "${currentInstallationMessage.installationId}" not found`);
  }
  logInfo(
    `BDC Package retrieved successfully from the catalog, installationId: ${currentInstallationMessage.installationId}`,
    { context }
  );

  if (currentInstallationMessage.installationCyclesCount <= bdcPackageInstallation.installationCyclesCount) {
    const messageTooOldTxt = `CorrelationId: ${currentInstallationMessage.correlationId}. Message count: ${currentInstallationMessage.installationCyclesCount}, bdcPackageInstallation count: ${bdcPackageInstallation.installationCyclesCount}`;
    logError(messageTooOldTxt, { context });
    throw new MessageTooOldError(messageTooOldTxt);
  }
}

export function convertInstallationStatusToComponentInstallationDetails(componentsInstallationStatus: any) {
  if (componentsInstallationStatus && componentsInstallationStatus.some((obj: any) => "installationStatus" in obj)) {
    componentsInstallationStatus.forEach((cis: { componentInstallationDetails: any; installationStatus: any }) => {
      if (cis.installationStatus) {
        cis.componentInstallationDetails = cis.installationStatus;
        cis.installationStatus = undefined;
      }
    });
  }
}

export function getComponentInstallRef(
  context: IRequestContext,
  allPackagesWithInstalledComponents: Map<string, BDCPackageInstallation>,
  componentId: string,
  relationshipSystem: relationshipSystemType
): ComponentInstallRef[] {
  const componentInstallRef: ComponentInstallRef[] = [];
  allPackagesWithInstalledComponents.forEach((packageInstallation, installationId) => {
    if (packageInstallation.installedComponentsId?.includes(componentId) && packageInstallation.navigationLinks) {
      const refId = getRefIdFromNavigationLinks(packageInstallation.navigationLinks, relationshipSystem);
      if (refId) {
        componentInstallRef.push({
          installId: installationId,
          refId,
        });
      }
    }
  });
  return componentInstallRef;
}

export function findAdditionalInstallationPropertyByName(
  name: AdditionalInstallationPropertyName,
  additionalInstallationProperties: AdditionalInstallationProperty[]
): AdditionalInstallationProperty | undefined {
  return additionalInstallationProperties?.find((ap) => ap.name === name);
}

// //////////// Private Methods ///////////////
function getInstallStatus(packageInstallation: BDCPackageInstallation): BDCPackageStatus {
  const componentsInstallationStatus = packageInstallation.componentsInstallationStatus;
  if (!componentsInstallationStatus?.length) {
    const message = "Can't get installation status, installationComponents is empty";
    logErrorWithOptionalContext(message);
    throw createUninstallationFailureException(null, message);
  }

  // Take first status from the component installation status
  let packageStatus = getBDCPackageStatus(componentsInstallationStatus[0].status);

  for (const componentInstallationStatus of componentsInstallationStatus) {
    // output is INSTALLATION_FAILED if at least one component is failed/error
    if (
      packageStatus === BDCPackageStatus.INSTALLATION_FAILED ||
      componentInstallationStatus.status === "FAILED" ||
      componentInstallationStatus.status === "ERROR"
    ) {
      return BDCPackageStatus.INSTALLATION_FAILED;
    }
    // if not INSTALLATION_FAILED then output is INSTALLING if at least one component is installing
    if (
      packageStatus === BDCPackageStatus.INSTALLING ||
      componentInstallationStatus.status === "EXECUTING" ||
      componentInstallationStatus.status === "PENDING"
    ) {
      packageStatus = BDCPackageStatus.INSTALLING;
    }
  }

  return packageStatus;
}

function getUnInstallStatus(packageInstallation: BDCPackageInstallation): BDCPackageStatus {
  const componentsUnInstallationStatus = packageInstallation.componentsUninstallationStatus;
  if (!componentsUnInstallationStatus?.length) {
    const message = "Can't get uninstallation status, uninstallationComponents is empty";
    logErrorWithOptionalContext(message);
    throw createUninstallationFailureException(null, message);
  }

  for (const component of componentsUnInstallationStatus) {
    if (component.status === "FAILED" || component.status === "ERROR") {
      return BDCPackageStatus.UNINSTALLATION_FAILED;
    }
  }
  return BDCPackageStatus.UNINSTALLING;
}

function extractNavigationLinkRefIdFromInstallationPackage(
  context: IRequestContext,
  installationPackage: any,
  relationshipTypeId: string
): string | undefined {
  let refId: string | undefined;
  logInfo(
    `Extracting navigation link - refId: ${relationshipTypeId}, installationPackage: ${JSON.stringify(
      installationPackage
    )}`,
    { context }
  );
  try {
    installationPackage.navigationLinks.map((navigationLink: any) => {
      if (navigationLink.relationshipTypeId === relationshipTypeId) {
        refId = navigationLink.links[0].refId;
        logInfo(`refId found: ${refId}`, { context });
      }
    });
  } catch (error) {
    logError(`Failed to extract refId: ${error}`, { context });
  }
  return refId;
}

function getRefIdFromNavigationLinks(navigationLinks: any[], relationshipTypeId: string): string | undefined {
  let refId: string | undefined;
  navigationLinks.forEach((navigationLink) => {
    if (navigationLink.relationshipTypeId === relationshipTypeId) {
      refId = navigationLink.links[0].refId;
    }
  });
  return refId;
}
