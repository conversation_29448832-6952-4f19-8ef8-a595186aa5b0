/** @format */

import { BDCPackageStatus } from "../../../shared/bdccockpit/Enums";
import { ACNFolder, BDCComponent, BDCPackageInstallationProperties } from "../../../shared/bdccockpit/Types";
import { MissingEntitlementDetails } from "../../../shared/bdccockpit/entitlement/entitlementTypes";
import { relationshipDirectionType, relationshipTypeIdValue } from "../catalog/catalogTypes";
import { BDCPackage } from "../packages/packagesTypes";
import { ConnectionCheckResult } from "../systems/systemsTypes";
export interface BDCPackageInstallation {
  originalPackage: BDCPackage;
  previousPackageData?: BDCPreviousPackageData;
  tenantId: string; // BDC tenant id
  installationId: string;
  startTime: Date;
  endTime?: Date;
  lastModifiedTime?: Date;
  correlationId: string;
  installationCyclesCount: number;
  installationProperties: BDCPackageInstallationProperties[];
  componentsInstallationStatus: ComponentInstallationStatus[];
  componentsUninstallationStatus?: ComponentInstallationStatus[];
  dspFolders?: ACNFolder[];
  storyLinks?: Map<string, string>;
  workSpaceLinks?: Map<string, string>;
  formationCatalogUuid?: string;
  navigationLinks?: any[];
  forceUninstall?: boolean;
  installedComponentsId?: string[];
  cancellationRequested?: boolean;
}
export interface BDCPreviousPackageData {
  previousOriginalPackage: BDCPackage; // The previous original package before the update
  previousComponentsInstallationStatus?: ComponentInstallationStatus[]; // The previous install status before the update
  previousComponentsUninstallationStatus?: ComponentInstallationStatus[]; // The status of uninstallation components we should remove durning the update
}

export type BDCPackageInstallationWithStatus = BDCPackageInstallation & { status: BDCPackageStatus | undefined };

export interface DspComponentInstallationDetails {
  importJobId: string;
  replicationFlowJobId: string;
  isWaitingForDpActivation?: boolean;
}

export interface SacComponentInstallationDetails {
  importJobId: string;
}

export interface FosComponentInstallationDetails {
  ordId: string;
  createdAt?: string;
  modifiedAt?: string;
  selfHealingStartTime?: string;
}

export type ComponentInstallationDetails =
  | DspComponentInstallationDetails
  | SacComponentInstallationDetails
  | FosComponentInstallationDetails[];

export interface ComponentInstallationStatus {
  componentId: string; // guid generated by the installation manger
  componentInstallationDetails: ComponentInstallationDetails;
  startTime: Date;
  endTime: Date;
  status: InstallationStatusCode;
  systemStatus?: any;
  message: string;
  retryCount: number;
}

export type InstallationStatusCode =
  | "PENDING"
  | "WAITING_TO_INSTALL_READY"
  | "EXECUTING"
  | "ERROR"
  | "DONE"
  | "FAILED"
  | "NOT ACTIVATED";
export interface InstallationStatus {
  installationStatusCode: InstallationStatusCode;
  installationStatusMessage: string;
  systemStatus?: any; // should include status from of install/uninstall steps without internal details. 'any' as may differ per system.
}

// PENDING:                    The component installation has not yet started
// WAITING_TO_INSTALL_READY    Checking if component is ready to be installed. In DSP case it checks of required components are shared in Catalog
// EXECUTING:                  The component is currently installing
// ERROR:                      The component installation has a recoverable error
// DONE:                       The component installation successfully ended
// FAIL:                       The component installation has an unrecoverable error or retryCount has exceeded the threshold

export type ComponentsWithInstallationStatus = BDCComponent & ComponentInstallationStatus;
export interface IPropertyTag {
  propertyId: string;
  valueObjects: valueObjectsType[];
}

export interface IValueObjectsTypeString {
  valueString: string;
}

export interface IValueObjectsTypeText {
  valueText: string;
}

export interface ILinksToSet {
  relationshipTypeId: relationshipTypeIdValue;
  links: ILinks[] | IFormationLinks[] | IBdcPackageLinks[];
}

export interface ILinks {
  systemId: string;
  relationshipDirection: relationshipDirectionType;
}

export interface IFormationLinks {
  linkedObjectId: string;
  relationshipDirection: relationshipDirectionType;
}
export interface IBdcPackageLinks {
  customSpace: string;
  className: "bdcPackage";
  relationshipDirection: relationshipDirectionType;
  name: string;
}
export interface ComponentInstallRef {
  installId: string;
  refId: string;
}
export interface IBDCInstallationPayload {
  name: string;
  propertyTags: IPropertyTag[];
  linksToSet: ILinksToSet[];
}
export interface IPackagesResponse {
  rootObjectIds: string[];
  allObjects: IAllObjectResponse[];
}
export interface IAllObjectResponse {
  propertyTags: IPropertyTagResponse[];
  navigationLinks: any[];
  id: string;
  name: string;
  customSpace: string;
  className: string;
}
export interface IPropertyTagResponse extends IPropertyTag {
  namespace: string;
  name: string;
}

export interface ExistingInstallationOperaitonResult {
  installationId: string;
  connectionCheckResult?: ConnectionCheckResult;
  missingEntitlementDetails?: MissingEntitlementDetails;
}

export type valueObjectsType = IValueObjectsTypeString | IValueObjectsTypeText;
export interface UCLTenantMapping {
  tenantId: string;
  formation: UCLFormation;
  tenants: UCLTenant[];
}
export interface UCLFormation {
  uclFormationId: string;
  uclFormationName: string;
  uclFormationTypeId: string;
}
export interface UCLTenant {
  applicationNamespace: string;
  applicationTenantId: string;
  uclSystemName: string;
  uclSystemTenantId: string;
}
export interface UCLTenantValidationResponse {
  errors: string[];
}
