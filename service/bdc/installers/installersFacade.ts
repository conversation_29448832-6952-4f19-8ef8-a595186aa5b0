/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { BDCPackageInstallationProperties } from "../../../shared/bdccockpit/Types";
import { SingleComponentInstallation } from "../lifecycleManagement/lifecycleManagementTypes";
import {
  BDCPackageInstallation,
  ComponentInstallationDetails,
  ComponentInstallationStatus,
  InstallationStatus,
} from "../packageInstallation/packageInstallationTypes";
import { BDCPackage, BDCPackageComponent } from "../packages/packagesTypes";
import { dspProvider, hcmProvider, s4Provider, sacProvider } from "../systems/applicationNamespaceUtils";
import { BDCException, BDCExceptionType } from "../utils/BDCException";
import { DataProductInstaller } from "./dataProductInstaller";
import { DSPContentInstaller } from "./dspContentInstaller";
import { IAsyncInstaller } from "./installerTypes";
import { SACContentInstaller } from "./sacContentInstaller";

export class InstallersFacade {
  private static instance: InstallersFacade;
  private installers: Map<string, IAsyncInstaller<ComponentInstallationDetails>>;

  private constructor() {
    this.installers = new Map();
    this.installers.set(dspProvider, new DSPContentInstaller());
    this.installers.set(sacProvider, new SACContentInstaller());
    this.installers.set(s4Provider, new DataProductInstaller());
    this.installers.set(hcmProvider, new DataProductInstaller());
  }

  public static getInstance(): InstallersFacade {
    if (!InstallersFacade.instance) {
      InstallersFacade.instance = new InstallersFacade();
    }
    return InstallersFacade.instance;
  }

  async isReadyToInstall(
    context: IRequestContext,
    singleComponentInstallation: SingleComponentInstallation,
    bdcPackageInstallation: BDCPackageInstallation,
    bdcPackage: BDCPackage
  ): Promise<boolean> {
    const installer = this.installers.get(singleComponentInstallation.originalComponent.provider);
    if (!installer) {
      throw new BDCException(
        context,
        BDCExceptionType.InstallationFailure,
        `Installer for "${singleComponentInstallation.originalComponent.provider}" not found`
      );
    }
    return await installer.isReadyToInstall(context, singleComponentInstallation, bdcPackageInstallation, bdcPackage);
  }
  async install(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationProperties: BDCPackageInstallationProperties[]
  ): Promise<ComponentInstallationDetails> {
    const installer = this.installers.get(component.provider);
    if (!installer) {
      throw new BDCException(
        context,
        BDCExceptionType.InstallationFailure,
        `Installer for "${component.provider}" not found`
      );
    }
    return await installer.install(context, component, installationProperties);
  }

  async getStatus(
    context: IRequestContext,
    originalComponent: BDCPackageComponent,
    componentInstallationStatus: ComponentInstallationStatus,
    installationProperties: BDCPackageInstallationProperties[],
    installing: boolean
  ): Promise<ComponentInstallationStatus> {
    const installer = this.installers.get(originalComponent.provider);
    if (!installer) {
      throw new BDCException(
        context,
        BDCExceptionType.GeneralFailure,
        `Installer for "${originalComponent.provider}" not found`
      );
    }
    // TODO After installers will be implemented to return the real values: the real Status should be returned built according to the result
    const newInstallationStatus: InstallationStatus = await installer.getStatus(
      context,
      componentInstallationStatus.componentInstallationDetails,
      installationProperties,
      originalComponent.provider,
      installing
    );
    componentInstallationStatus.message = newInstallationStatus.installationStatusMessage;
    return {
      ...componentInstallationStatus,
      status: newInstallationStatus.installationStatusCode,
      message: newInstallationStatus.installationStatusMessage || "",
      systemStatus: newInstallationStatus.systemStatus,
    };
  }

  async uninstall(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationProperties: BDCPackageInstallationProperties[]
  ): Promise<ComponentInstallationDetails> {
    const installer = this.installers.get(component.provider);
    if (!installer) {
      throw new BDCException(
        context,
        BDCExceptionType.UninstallationFailure,
        `Installer for "${component.provider}" not found`
      );
    }
    return await installer.uninstall(context, component, installationProperties);
  }
}
