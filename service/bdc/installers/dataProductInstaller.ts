/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { TimeUnit } from "@sap/deepsea-utils";
import { SecureStoreKey } from "@sap/dwc-credentials";
import { httpClient } from "@sap/dwc-http-client";
import Status from "http-status-codes";
import {
  AdditionalInstallationProperty,
  AdditionalInstallationPropertyName,
  BDCDataProduct,
  BDCPackageInstallationProperties,
  BDCSourceProvider,
} from "../../../shared/bdccockpit/Types";
import { FormationService } from "../formation/formationService";
import { SingleComponentInstallation } from "../lifecycleManagement/lifecycleManagementTypes";
import {
  ComponentInstallationDetails,
  FosComponentInstallationDetails,
  InstallationStatus,
  InstallationStatusCode,
} from "../packageInstallation/packageInstallationTypes";
import { findAdditionalInstallationPropertyByName } from "../packageInstallation/packageInstallationUtils";
import { BDCPackageComponent } from "../packages/packagesTypes";
import { SystemsService } from "../systems/systemsService";
import { BDCException, BDCExceptionType } from "../utils/BDCException";
import { getTenantCredentials } from "../utils/certificateUtils";
import { getBdcLogger } from "../utils/logUtils";
import { ComponentInstaller } from "./componentInstaller";
import { IAsyncInstaller } from "./installerTypes";

const { logError, logInfo } = getBdcLogger(__filename);

interface FosComponentInstallationServerResponse {
  ordId: string;
  correlationId: string;
  version: string;
  status: string;
  createdAt?: string;
  modifiedAt?: string;
}

interface FosComponentUnInstallationServerResponse extends FosComponentInstallationServerResponse {
  offboardMode: string;
}

export class DataProductInstaller
  extends ComponentInstaller
  implements IAsyncInstaller<FosComponentInstallationDetails[]>
{
  async isReadyToInstall(
    context: IRequestContext,
    singleComponentInstallation: SingleComponentInstallation
  ): Promise<boolean> {
    return true;
  }
  async install(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationPropertiesArr: BDCPackageInstallationProperties[]
  ): Promise<FosComponentInstallationDetails[]> {
    logInfo(
      `[install] component: ${JSON.stringify(component)}, installationPropertiesArr: ${JSON.stringify(
        installationPropertiesArr
      )}`,
      { context }
    );

    const installationProperties = this.getInstallationPropertiesByProvider(
      context,
      installationPropertiesArr,
      component.provider
    );
    logInfo(`[install] installationProperties: ${JSON.stringify(installationProperties)}`, { context });
    const componentDataProduct: BDCDataProduct = component as BDCDataProduct; // TODO Check correct type
    // TODO - return mock value for demo
    if (componentDataProduct.ordid === "sap.s4.dataProduct:DummyDP") {
      return [{ ordId: componentDataProduct.ordid }];
    }
    const tenantId = installationProperties.systemTenant;
    let clientCertificateChain: string;
    let privateKey: string;
    let passphrase: string | undefined;
    let fosUrl: string | undefined;
    try {
      ({ clientCertificateChain, privateKey, passphrase, fosUrl } = await this.extractFosCredentialsAndUrl(
        context,
        installationProperties
      ));
      logInfo(`FoS install URL from additional properties is: ${fosUrl}`, { context });
      const forRequestBodyStr = JSON.stringify({
        value: [
          {
            ordId: componentDataProduct.ordid,
            correlationId: context.correlationId,
            version: componentDataProduct.version,
          },
        ],
      });

      logInfo(`Install request: fosUrl: ${fosUrl}, forRequestBodyStr: ${forRequestBodyStr}`, { context });

      const response = await httpClient.call({
        url: fosUrl as string,
        opts: {
          method: "POST",
          requestContext: context,
          callTimeout: TimeUnit.SECONDS.toMillis(45),
          callCategory: "FOS",
          headers: this.buildHeaders(context),
          acceptedStatusCodes: [
            Status.CONFLICT,
            Status.OK,
            Status.CREATED,
            Status.ACCEPTED,
            Status.NO_CONTENT,
            Status.UNAUTHORIZED,
            Status.INTERNAL_SERVER_ERROR,
            Status.BAD_REQUEST,
            Status.BAD_GATEWAY,
            Status.LOCKED,
          ],
          body: forRequestBodyStr,
          key: [{ pem: privateKey, passphrase }],
          cert: clientCertificateChain,
          minVersion: "TLSv1.2",
          rejectUnauthorized: true,
        },
      });
      const responseBody = (response.body as any).value as FosComponentInstallationServerResponse[];
      this.validateInstallResponse(context, response, responseBody, componentDataProduct, tenantId, fosUrl!);
      return this.convertFosInstallResponseToBdc(responseBody);
    } catch (error) {
      const errorMessage = `Install request failed for  tenant: ${tenantId}, ordId: ${
        componentDataProduct.ordid
      }, "FOS_URL": ${fosUrl}, error: ${JSON.stringify(error)}.`;
      logError(errorMessage, { context });
      throw error;
    }
  }
  private validateInstallResponse(
    context: IRequestContext,
    response: any,
    responseBody: FosComponentInstallationServerResponse[],
    componentDataProduct: BDCDataProduct,
    tenantId: string,
    fosUrl: string
  ): void {
    const fosCorrelationId = response.headers?.get("x-correlation-id");
    if (response.status !== Status.CREATED) {
      const errorMessage = `Install request failed for  tenant: ${tenantId}, ordId: ${
        componentDataProduct.ordid
      }, FOS_URL: ${fosUrl}, Status: ${
        response.status
      }, x-correlation-id(FOS): ${fosCorrelationId},  payload: ${JSON.stringify(responseBody)}`;
      logError(errorMessage, { context });
      if (response.status === Status.CONFLICT || response.status === Status.LOCKED) {
        throw new BDCException(
          context,
          BDCExceptionType.ComponentInstallationRepeatRequired,
          `Retriable status received from FOS: ${response.status} for component: ${componentDataProduct.ordid}`
        );
      } else {
        throw new Error(errorMessage);
      }
    } else {
      logInfo(
        `Install response is, Body: ${JSON.stringify(response.body)}, Status: ${
          response.status
        }, x-correlation-id(FOS): ${fosCorrelationId}`,
        {
          context,
        }
      );
    }
    for (const responseItem of responseBody) {
      logInfo(`FOS Install item is: ${responseItem.ordId}, status: ${responseItem.status}`, {
        context,
      });
    }
  }

  async getStatus(
    context: IRequestContext,
    componentInstallationStatus: FosComponentInstallationDetails[],
    installationPropertiesArr: BDCPackageInstallationProperties[],
    provider: BDCSourceProvider,
    installing: boolean
  ): Promise<InstallationStatus> {
    const action = installing ? "activate" : "deactivate";
    logInfo(
      `getStatus: ${action}, componentInstallationStatus: ${JSON.stringify(
        componentInstallationStatus
      )}, installationPropertiesArr: ${JSON.stringify(installationPropertiesArr)}`,
      { context }
    );

    const installationStatus: InstallationStatus = await this.doGetStatus(
      context,
      componentInstallationStatus,
      installationPropertiesArr,
      installing,
      provider
    );
    if (
      installationStatus.installationStatusCode === "FAILED" ||
      installationStatus.installationStatusCode === "ERROR"
    ) {
      await this.printDetailedStatus(
        context,
        componentInstallationStatus,
        installationPropertiesArr,
        installationStatus,
        provider
      );
    }
    return installationStatus;
  }

  async doGetStatus(
    context: IRequestContext,
    componentInstallationStatus: FosComponentInstallationDetails[],
    installationPropertiesArr: BDCPackageInstallationProperties[],
    installing: boolean,
    provider: BDCSourceProvider
  ): Promise<InstallationStatus> {
    const action = installing ? "activate" : "deactivate";

    const installationProperties = this.getInstallationPropertiesByProvider(
      context,
      installationPropertiesArr,
      provider
    );
    logInfo(`[doGetStatus] ${action}, installationProperties: ${JSON.stringify(installationProperties)}`, { context });
    const tenantId = installationProperties.systemTenant;
    const ordId = componentInstallationStatus[0].ordId;
    let clientCertificateChain: string;
    let privateKey: string;
    let passphrase: string | undefined;
    let fosUrl: string | undefined;
    try {
      ({ clientCertificateChain, privateKey, passphrase, fosUrl } = await this.extractFosCredentialsAndUrl(
        context,
        installationProperties
      ));
      const getStatusUrl = `${fosUrl}/${ordId}`;
      logInfo(`Get status: ${action}, url: ${getStatusUrl}`, { context });

      const response = await httpClient.call({
        url: getStatusUrl,
        opts: {
          method: "GET",
          requestContext: context,
          callTimeout: TimeUnit.SECONDS.toMillis(45),
          callCategory: "FOS",
          headers: this.buildHeaders(context),
          acceptedStatusCodes: [
            Status.CONFLICT,
            Status.OK,
            Status.CREATED,
            Status.ACCEPTED,
            Status.NO_CONTENT,
            Status.NOT_FOUND,
          ],
          key: [{ pem: privateKey, passphrase }],
          cert: clientCertificateChain,
          minVersion: "TLSv1.2",
          rejectUnauthorized: true,
        },
      });

      if (response.status === Status.NOT_FOUND) {
        if (installing) {
          // Data Product update brute force.
          logInfo(
            `Data Product update brute force, Deactivate ordid: ${ordId} finish successfully, url: ${getStatusUrl}, Status: ${response.status}`,
            {
              context,
            }
          );
          return { installationStatusCode: "EXECUTING", installationStatusMessage: "" };
        } else {
          // Data Product offboarding.
          logInfo(`Deactivate ordid: ${ordId} finish successfully, url: ${getStatusUrl}, Status: ${response.status}`, {
            context,
          });
          return { installationStatusCode: "DONE", installationStatusMessage: "" };
        }
      }

      if (response.status !== Status.OK) {
        const errorMessage = `Get status: ${action}, request failed for  tenant: ${tenantId}, irdId: ${ordId}, FOS_URL: ${fosUrl}, Status: ${
          response.status
        }, payload: ${JSON.stringify(response.body)}`;

        logError(errorMessage, { context });
        throw new Error(errorMessage);
      } else {
        logInfo(
          `Get status: ${action}, response is, Body: ${JSON.stringify(response.body)}, Status: ${response.status}`,
          {
            context,
          }
        );
      }

      const bodyValue = response.body as any;
      if (!installing && Array.isArray(bodyValue.data) && bodyValue.data.length === 0) {
        return { installationStatusCode: "DONE", installationStatusMessage: "" };
      }

      const ordIdToSearch = componentInstallationStatus[0].ordId; // TODO - we do one as method signature should return only one status
      const fosStatus: FosComponentInstallationServerResponse | undefined = bodyValue;
      // componentInstallationStatus passed by reference as interface return only string value, the created/modified times should ba passed to the caller by reference
      componentInstallationStatus[0].createdAt = fosStatus?.createdAt;
      componentInstallationStatus[0].modifiedAt = fosStatus?.modifiedAt;
      if (!fosStatus) {
        throw new BDCException(
          context,
          BDCExceptionType.InstallationFailure,
          `${action}: Installation ordId ${ordIdToSearch} not found in fos `
        );
      }
      // TODO currently as we return only one status
      return {
        installationStatusCode: this.convertFosStatus(context, fosStatus, installing),
        installationStatusMessage: "",
      };
    } catch (error) {
      logError(`Failed to get status ${action}, FOS_URL: ${fosUrl},  error: ${error}`, {
        context,
      });
      return { installationStatusCode: "ERROR", installationStatusMessage: error.message };
    }
  }

  async printDetailedStatus(
    context: IRequestContext,
    installerComponentInstallationStatus: ComponentInstallationDetails,
    installationPropertiesArr: BDCPackageInstallationProperties[],
    installationStatus: InstallationStatus,
    provider: BDCSourceProvider
  ): Promise<void> {
    const installationProperties = this.getInstallationPropertiesByProvider(
      context,
      installationPropertiesArr,
      provider
    );
    const tenantId = installationProperties.systemTenant;

    const fosStatus: FosComponentInstallationDetails[] =
      installerComponentInstallationStatus as FosComponentInstallationDetails[];
    if (fosStatus && fosStatus.length > 0) {
      const fosSingleStatus = fosStatus[0]; // TODO currently supports only one
      try {
        const { clientCertificateChain, privateKey, passphrase, fosUrl } = await this.extractFosCredentialsAndUrl(
          context,
          installationProperties
        );
        const getStatusUrl = `${fosUrl}/${fosSingleStatus.ordId}/statusSummary`;
        const response = await httpClient.call({
          url: getStatusUrl,
          opts: {
            method: "GET",
            requestContext: context,
            callTimeout: TimeUnit.SECONDS.toMillis(45),
            callCategory: "FOS",
            headers: this.buildHeaders(context),
            acceptedStatusCodes: [Status.CONFLICT, Status.OK, Status.CREATED, Status.ACCEPTED, Status.NO_CONTENT],
            key: [{ pem: privateKey, passphrase }],
            cert: clientCertificateChain,
            minVersion: "TLSv1.2",
            rejectUnauthorized: true,
          },
        });
        if (response.status !== Status.OK) {
          const errorMessage = `Get status request failed for  tenant: ${tenantId}, irdId: ${
            fosSingleStatus.ordId
          }. Status: ${response.status}, payload: ${JSON.stringify(response.body)}`;

          logError(errorMessage, { context });
          throw new Error(errorMessage);
        }
        const statusSummary: any = response.body as any;
        if (statusSummary) {
          const detailedMessage = `Detailed status summary for ${fosSingleStatus.ordId} is ${JSON.stringify(
            statusSummary
          )}`;
          logError(detailedMessage, {
            context,
          });
          installationStatus.systemStatus = statusSummary;
          installationStatus.installationStatusMessage = statusSummary;
        }
      } catch (error) {
        installationStatus.installationStatusMessage = error.message;
        logError(`${JSON.stringify(error)}`, { context });
      }
    }
  }

  private buildHeaders(context: IRequestContext) {
    const headers: { [key: string]: string } = {
      "content-type": "application/json",
      "x-sap-boc-tenant-id": context.tenantId || "",
      "x-sap-boc-user-id": context.userInfo.userId || "",
      "x-correlation-id": context.correlationId || "",
    };
    if (context.userInfo.userName) {
      headers["sap-user-name"] = context.userInfo.userName;
    }
    return headers;
  }

  async getFosApiDetails(
    context: IRequestContext,
    installationProperties: BDCPackageInstallationProperties
  ): Promise<AdditionalInstallationProperty[]> {
    logInfo(`[getFosApiDetails] called, installationProperties: ${JSON.stringify(installationProperties)}`, {
      context,
    });
    const fosUrl = findAdditionalInstallationPropertyByName(
      AdditionalInstallationPropertyName.fosInstallUrl,
      installationProperties.additionalInstallationProperties!
    );
    if (fosUrl?.value) {
      logInfo(
        `Additional properties found: ${JSON.stringify(installationProperties.additionalInstallationProperties)}`,
        { context }
      );
      return installationProperties.additionalInstallationProperties!;
    }
    const systems = await SystemsService.getAllSystems(context, true);
    logInfo(`[getFosApiDetails] SystemInfo retrieved successfully: ${JSON.stringify(systems)}`, { context });
    const s4system = systems.find((sys) => sys.applicationTenantId === installationProperties.systemTenant);
    logInfo(`[getFosApiDetails] s4system found: ${JSON.stringify(s4system)}`, { context });
    const fosFormationCatalogUuid = await FormationService.detectFormationCatalogUuid(context, installationProperties)!;

    const fosFormation = s4system?.formations.find((formation) => formation.catalogUuid === fosFormationCatalogUuid);
    logInfo(`[getFosApiDetails]fosFormation found: ${JSON.stringify(fosFormation)}`, { context });
    const fosProperties = await SystemsService.getFosProperties(
      context,
      fosFormation ? fosFormation.assignmentId! : undefined,
      s4system,
      fosFormationCatalogUuid
    );
    installationProperties.additionalInstallationProperties = fosProperties;
    logInfo(`[getFosApiDetails] fosProperties found: ${JSON.stringify(fosProperties)}`, { context });
    return fosProperties;
  }

  private extractFosCredentialsAndUrl = async (
    context: IRequestContext,
    installationProperties: BDCPackageInstallationProperties
  ) => {
    const additionalProperties: AdditionalInstallationProperty[] = await this.getFosApiDetails(
      context,
      installationProperties
    );
    const { clientCertificateChain, privateKey, passphrase } = await getTenantCredentials(
      context,
      SecureStoreKey.UclTenantCredentials,
      context.userInfo.tenantId!
    );
    const fosUrl = findAdditionalInstallationPropertyByName(
      AdditionalInstallationPropertyName.fosInstallUrl,
      additionalProperties
    )?.value;
    return { clientCertificateChain, privateKey, passphrase, fosUrl };
  };

  private convertFosInstallResponseToBdc(
    fosServiceResponse: FosComponentInstallationServerResponse[]
  ): FosComponentInstallationDetails[] {
    let responseWithBdcStatus: FosComponentInstallationDetails[] = [];
    if (fosServiceResponse && fosServiceResponse.length > 0) {
      responseWithBdcStatus = fosServiceResponse?.map((responseItem) => ({
        ordId: responseItem.ordId,
      }));
    }
    return responseWithBdcStatus;
  }

  private convertFosStatus(
    context: IRequestContext,
    responseItem: FosComponentInstallationServerResponse,
    installing: boolean
  ) {
    // TODO - make a switch when you know all status values FOS returns
    let installationStatus: InstallationStatusCode;
    if (
      responseItem.status === "provisioning" ||
      responseItem.status === "deprovisioning" ||
      responseItem.status === "marked-for-deletion" ||
      responseItem.status === "deleting"
    ) {
      installationStatus = "EXECUTING";
    } else if (responseItem.status === "provisioning-error" || responseItem.status === "deprovisioning-error") {
      installationStatus = "FAILED";
    } else if (responseItem.status === "active-with-errors") {
      // shouldn't happen during provisioning
      installationStatus = "DONE";
    } else if (responseItem.status === "active") {
      installationStatus = "DONE";
    } else if (responseItem.status === "inactive") {
      installationStatus = installing ? "EXECUTING" : "DONE";
    } else {
      installationStatus = "FAILED";
      logError(`Unrecognized status received: ${responseItem.status}`, { context });
    }
    return installationStatus;
  }

  async uninstall(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationPropertiesArr: BDCPackageInstallationProperties[]
  ): Promise<FosComponentInstallationDetails[]> {
    logInfo(
      `[uninstall] component: ${JSON.stringify(component)}, installationPropertiesArr: ${JSON.stringify(
        installationPropertiesArr
      )}`,
      { context }
    );
    const installationProperties = this.getInstallationPropertiesByProvider(
      context,
      installationPropertiesArr,
      component.provider
    );
    logInfo(`[uninstall] installationProperties: ${JSON.stringify(installationProperties)}`, { context });
    const componentDataProduct: BDCDataProduct = component as BDCDataProduct; // TODO Check correct type
    const tenantId = installationProperties.systemTenant;

    let responseBody: FosComponentInstallationServerResponse[];

    let fosUrl: string | undefined;
    try {
      const additionalProperties: AdditionalInstallationProperty[] = await this.getFosApiDetails(
        context,
        installationProperties
      );
      const { clientCertificateChain, privateKey, passphrase } = await getTenantCredentials(
        context,
        SecureStoreKey.UclTenantCredentials,
        context.userInfo.tenantId!
      );
      fosUrl = findAdditionalInstallationPropertyByName(
        AdditionalInstallationPropertyName.fosInstallUrl,
        additionalProperties
      )?.value;
      const forRequestBodyStr = JSON.stringify({
        value: [
          {
            ordId: componentDataProduct.ordid,
            correlationId: context.correlationId,
            offboardMode: "hard",
          },
        ],
      });

      const response = await httpClient.call({
        url: fosUrl as string,
        opts: {
          method: "PATCH",
          requestContext: context,
          callTimeout: TimeUnit.SECONDS.toMillis(45),
          callCategory: "FOS",
          headers: this.buildHeaders(context),
          acceptedStatusCodes: [Status.CONFLICT, Status.OK, Status.CREATED, Status.ACCEPTED, Status.NO_CONTENT],
          body: forRequestBodyStr,
          key: [{ pem: privateKey, passphrase }],
          cert: clientCertificateChain,
          minVersion: "TLSv1.2",
          rejectUnauthorized: true,
        },
      });
      responseBody = (response.body as any).value as FosComponentUnInstallationServerResponse[];
      if (response.status !== Status.OK) {
        const errorMessage = `Uninstall request failed for  tenant: ${tenantId}, ordId: ${
          componentDataProduct.ordid
        }, "FOS_URL": ${fosUrl}. Status: ${response.status}, payload: ${JSON.stringify(responseBody)}`;

        logError(errorMessage, { context });
        throw new Error(errorMessage);
      } else {
        logInfo(
          `[uninstall] Uninstall response is, Body: ${JSON.stringify(response.body)}, Status: ${response.status}`,
          {
            context,
          }
        );
      }
      for (const responseItem of responseBody) {
        logInfo(`[uninstall] FOS Uninstall item is: ${responseItem.ordId}, status: ${responseItem.status}`, {
          context,
        });
      }
    } catch (error) {
      logError(`FOS_URL: ${fosUrl}, error: ${JSON.stringify(error)}`, { context });
      // TODO -
      // Currently we set provisioning error on each error, but maybe we want try retry according to receiving status.
      // For instance if 500 received
      responseBody = [
        {
          ordId: componentDataProduct.ordid,
          correlationId: context.correlationId!,
          version: componentDataProduct.version,
          status: "deprovisioning-error",
        },
      ];
    }
    return this.convertFosInstallResponseToBdc(responseBody);
  }

  public sanitizeDetailedResponse(context: IRequestContext, obj: any): any {
    try {
      this.clearFirstLevelFieldsNameNotInList(obj, [
        "status",
        "onboardedAt",
        "offboardedAt",
        "dataAcquisition",
        "dataSharing",
        "dataTransformation",
      ]);
      this.clearFirstLevelFieldsNameNotInList(obj.dataAcquisition, ["status"]);
      this.clearFirstLevelFieldsNameNotInList(obj.dataSharing, ["status"]);
      this.clearFirstLevelFieldsNameNotInList(obj.dataTransformation, ["status"]);
      return obj;
    } catch (error) {
      logError(error.message, { context });
      return { status: "Failed to sanitize, please see logs for more details" };
    }
  }

  private clearFirstLevelFieldsNameNotInList(obj: any, names: string[]) {
    if (obj === null || obj === undefined || typeof obj !== "object" || Array.isArray(obj)) {
      return; // Exit safely
    }
    Object.keys(obj).forEach((key) => {
      if (!names.includes(key)) {
        delete obj[key]; // Remove unwanted properties
      }
    });
  }
}
