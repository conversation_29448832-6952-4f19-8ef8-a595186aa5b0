/** @format */

import { IRequestContext, StatusType } from "@sap/deepsea-types";
import { BDCPackageInstallationProperties, BDCProvider, BDCRequestContext } from "../../../shared/bdccockpit/Types";
import { getUCLSystemTenantId } from "../../../shared/bdccockpit/utils/SystemInfoUtil";
import { RequestContext } from "../../repository/security/requestContext";
import { ACN_CATEGORY_FOR_BDC_ONBOARDING, getPackageId } from "../acnProvision/acnIntegration";
import { BdcOffboardingController } from "../acnProvision/offboarding/bdcOffboardingController";
import { BdcOnboardingController } from "../acnProvision/onboarding/bdcOnboardingController";
import { isValidJobId } from "../acnProvision/onboarding/utils";
import { IBdcOffboardingResponse, IBdcOnboardingPayload, ICreateAcnDeleteJobParams } from "../acnProvision/types";
import { ObjectReference } from "../catalog/catalogTypes";
import { DataProductService } from "../dataProducts/dataProductService";
import { FlowTimeoutError } from "../dataProducts/dataProductTypes";
import { SingleComponentInstallation } from "../lifecycleManagement/lifecycleManagementTypes";
import {
  BDCPackageInstallation,
  DspComponentInstallationDetails,
  InstallationStatus,
  InstallationStatusCode,
} from "../packageInstallation/packageInstallationTypes";
import {
  BDCPackage,
  BDCPackageComponent,
  CNPackageRequiry,
  ComponentWithRequiry,
  DataProductRequiry,
} from "../packages/packagesTypes";
import { isApplicationNamespaceOfSourceType } from "../systems/applicationNamespaceUtils";
import { SystemsService } from "../systems/systemsService";
import { BDCException, BDCExceptionType } from "../utils/BDCException";
import { getBdcLogger } from "../utils/logUtils";
import { ComponentInstaller } from "./componentInstaller";
import { IAsyncInstaller } from "./installerTypes";

const { logInfo, logError, getLogFunctionForStatus } = getBdcLogger(__filename);

export class DSPContentInstaller
  extends ComponentInstaller
  implements IAsyncInstaller<DspComponentInstallationDetails>
{
  static SIX_HOURS_IN_MIll = 6 * 60 * 60 * 1000;
  static fromStatusTypeToInstallationStatus(statusType: StatusType): InstallationStatusCode {
    let installationStatus: InstallationStatusCode;
    switch (statusType.toLowerCase()) {
      case StatusType.Succeed.toLowerCase():
      case StatusType.Warning.toLowerCase():
        installationStatus = "DONE";
        break;
      case StatusType.Aborted.toLowerCase():
      case StatusType.Failed.toLowerCase():
        installationStatus = "FAILED";
        break;
      case StatusType.Pending.toLowerCase():
      case StatusType.Executing.toLowerCase():
        installationStatus = "EXECUTING";
        break;
      default:
        throw new BDCException(null, BDCExceptionType.GeneralFailure, `Unknown status ${statusType}`);
    }

    return installationStatus;
  }

  async isReadyToInstall(
    context: IRequestContext,
    singleComponentInstallation: SingleComponentInstallation,
    bdcPackageInstallation: BDCPackageInstallation,
    bdcPackage: BDCPackage
  ): Promise<boolean> {
    const now = new Date(Date.now());
    const diffInMs =
      now.getTime() - new Date(singleComponentInstallation?.installationStatus?.startTime || Date.now()).getTime();

    if (diffInMs >= DSPContentInstaller.SIX_HOURS_IN_MIll) {
      throw new FlowTimeoutError(
        `Timeout occurred waiting for Data Product to be activated: Now: ${now}, Start Time: ${
          singleComponentInstallation?.installationStatus?.startTime
        }, installation: ${JSON.stringify(bdcPackageInstallation)}`
      );
    }
    const originalComponent: BDCPackageComponent = bdcPackage.components.find(
      (component) => component.name === singleComponentInstallation.originalComponent.name
    )!;
    const objectReferences: ObjectReference[] = [];

    // TODO - installation properties doesn't include systemId(catalog UUID) of the system.
    // Once we have reference instead of installation properties this block is not needed
    const applicationTenantId = bdcPackageInstallation.installationProperties.find((ip) =>
      isApplicationNamespaceOfSourceType(ip.applicationNamespace)
    )!.systemTenant;
    const allSystems = await SystemsService.getAllSystems(context); // this call implement cache, so no extra call to Catalog
    const sourceSystem = allSystems.find((system) => system.applicationTenantId === applicationTenantId);
    if (!sourceSystem) {
      logError(`Cannot find system with applicationTenantId: ${applicationTenantId}`, { context });
      throw new BDCException(
        context,
        BDCExceptionType.InstallationFailure,
        `Cannot find system with applicationTenantId: ${applicationTenantId}`
      );
    }
    // end of TODO block

    (originalComponent as ComponentWithRequiry<CNPackageRequiry | DataProductRequiry>).requires.forEach((req) => {
      if (req.category === "DataProduct") {
        objectReferences.push({
          nativeUniqueName: (req as DataProductRequiry).ordid,
          systemId: sourceSystem?.systemId,
        });
      }
    });
    if (objectReferences.length === 0) {
      return true;
    }
    return await DataProductService.areAllDpDependenciesEnabled(context, objectReferences);
  }
  async install(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationPropertiesArr: BDCPackageInstallationProperties[]
  ): Promise<DspComponentInstallationDetails> {
    let componentInstallationDetails = {} as DspComponentInstallationDetails;
    try {
      logInfo(
        `Installing DSP component: ${JSON.stringify(component)}, installation properties: ${JSON.stringify(
          installationPropertiesArr
        )}`,
        { context }
      );
      const dspInstallationProperties = this.getInstallationPropertiesByProvider(
        context,
        installationPropertiesArr,
        component.provider
      );
      const dspTenantId = dspInstallationProperties.systemTenant || context.tenantId!;
      const dspContext = this.createDSPContext(dspTenantId, context);
      logInfo(`DSP Context created: ${JSON.stringify(dspContext)}`, { context });
      let params: IBdcOnboardingPayload;
      const sourceProperty = installationPropertiesArr.find((ipa) =>
        isApplicationNamespaceOfSourceType(ipa.applicationNamespace)
      );
      params = await this.buildPayloadParams(context, dspContext, dspTenantId, sourceProperty!, component);
      logInfo(`install dsp acn package with params ${JSON.stringify(params)}`, { context });
      componentInstallationDetails = await BdcOnboardingController.onboarding(params, dspContext);
      logInfo(`componentInstallationStatus received: ${JSON.stringify(componentInstallationDetails)}`, { context });
      if (!isValidJobId(componentInstallationDetails.importJobId)) {
        throw new Error(`Invalid jobId: ${JSON.stringify(componentInstallationDetails)}`);
      }
    } catch (error) {
      logError(["Failed to install DSP ACN package", error], { context });
      throw error;
    }
    componentInstallationDetails.isWaitingForDpActivation = false;
    return componentInstallationDetails;
  }

  private async buildPayloadParams(
    context: IRequestContext,
    dspContext: IRequestContext,
    dspTenantId: string,
    installationProperties: BDCPackageInstallationProperties,
    component: BDCPackageComponent
  ) {
    const systemInfo = await SystemsService.getAllSystems(context);
    const uclFormationId = installationProperties.formationId || "mock-ucl-formation-id";
    const system = systemInfo?.find((sysInfo) => installationProperties.systemName === sysInfo.name);
    const uclSystemTenantId = getUCLSystemTenantId(system?.propertyTags || []);
    const acnPackageId = await getPackageId(component.name, component.version, "DWC", dspContext);
    if (acnPackageId === -1) {
      throw new BDCException(
        context,
        BDCExceptionType.InstallationFailure,
        `ANC package id not found. component name: "${component.name}, component version: "${component.version}"`
      );
    }

    const importACNVersionFF = await context.isFeatureFlagActive("DWCO_BDC_COCKPIT_SPECIFIC_ACN_VERSION_IMPORT");

    const params: IBdcOnboardingPayload = {
      acnPackageId,
      uclFormationId,
      uclSystemTenantId,
      acnCategory: ACN_CATEGORY_FOR_BDC_ONBOARDING,
      ...(importACNVersionFF && { packageVersion: component.version }),
    };
    return params;
  }

  async getStatus(
    context: IRequestContext,
    componentInstallationStatus: DspComponentInstallationDetails,
    installationPropertiesArr: BDCPackageInstallationProperties[],
    provider: BDCProvider,
    installing: boolean
  ): Promise<InstallationStatus> {
    try {
      if (installing) {
        return await this.getOnboardingStatus(
          context,
          componentInstallationStatus,
          installationPropertiesArr,
          provider
        );
      } else {
        return await this.getOffboardingStatus(
          context,
          componentInstallationStatus,
          installationPropertiesArr,
          provider
        );
      }
    } catch (error) {
      logError(`Failed to get status: ${error}`, { context });
      throw error;
    }
  }

  /**
   *  DSP status should be determined only according importJobStatus, replicationJobStatus should be ignored
   * @param context
   * @param componentInstallationStatus
   * @param installationPropertiesArr
   * @returns
   */
  async getOnboardingStatus(
    context: IRequestContext,
    componentInstallationStatus: DspComponentInstallationDetails,
    installationPropertiesArr: BDCPackageInstallationProperties[],
    provider: BDCProvider
  ): Promise<InstallationStatus> {
    const installationProperties = this.getInstallationPropertiesByProvider(
      context,
      installationPropertiesArr,
      provider
    );
    const dspTenantId = installationProperties.systemTenant || context.tenantId!;
    const dspContext = this.createDSPContext(dspTenantId, context);
    const status = await BdcOnboardingController.getOnboardingJobsStatus(componentInstallationStatus, dspContext);
    logInfo(
      `Onboarding Status received for the Job: ${JSON.stringify(componentInstallationStatus)}, Status: ${JSON.stringify(
        status
      )}`,
      { context }
    );
    if (!status.importJobStatus) {
      throw new Error(`install job status is undefined,
        import JobID: ${componentInstallationStatus.importJobId}, importJobStatus: ${status.importJobStatus}`);
    }

    let installationStatus: InstallationStatusCode;
    const importJobStatus = DSPContentInstaller.fromStatusTypeToInstallationStatus(status.importJobStatus);

    if (importJobStatus === "DONE") {
      installationStatus = "DONE";
    } else if (importJobStatus === "FAILED") {
      installationStatus = "FAILED";
    } else if (importJobStatus === "ERROR") {
      installationStatus = "ERROR";
    } else if (importJobStatus === "EXECUTING") {
      installationStatus = "EXECUTING";
    } else {
      throw new Error(
        `Unknown status importJobStatus: ${importJobStatus}, import JobID: ${componentInstallationStatus.importJobId}`
      );
    }

    const logLevelForInstallStatus = getLogFunctionForStatus([status.importJobStatus]);
    const statusMessage = `get install status importJobStatus: ${JSON.stringify(status)}`;
    logLevelForInstallStatus(statusMessage, { context });
    return {
      installationStatusCode: installationStatus,
      installationStatusMessage: statusMessage,
      systemStatus: {
        importJobStatus: status.importJobStatus,
        replicationFlowJobStatus: status.replicationFlowJobStatus,
      },
    };
  }

  async getOffboardingStatus(
    context: IRequestContext,
    componentInstallationStatus: DspComponentInstallationDetails,
    installationPropertiesArr: BDCPackageInstallationProperties[],
    provider: BDCProvider
  ): Promise<InstallationStatus> {
    const installationProperties = this.getInstallationPropertiesByProvider(
      context,
      installationPropertiesArr,
      provider
    );
    const dspTenantId = installationProperties.systemTenant || context.tenantId!;
    const dspContext = this.createDSPContext(dspTenantId, context);

    const offBoardingJob: IBdcOffboardingResponse = { acnDeleteJobId: componentInstallationStatus.importJobId };
    const status = await BdcOffboardingController.getOffboardingJobsStatus(dspContext, offBoardingJob);
    logInfo(`OffboardingJobsStatus: ${JSON.stringify(status)}`, { context });
    if (!status.acnDeleteJobStatus) {
      throw new Error(
        `uninstall job status is undefined: ${status.acnDeleteJobStatus}, jobID: ${status.acnDeleteJobId}`
      );
    }

    const deleteJobStatus = DSPContentInstaller.fromStatusTypeToInstallationStatus(status.acnDeleteJobStatus);
    logInfo(`get uninstall status: ${deleteJobStatus}, jobID: ${status.acnDeleteJobId}`, { context });
    return { installationStatusCode: deleteJobStatus, installationStatusMessage: JSON.stringify(status) };
  }

  async uninstall(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationPropertiesArr: BDCPackageInstallationProperties[]
  ): Promise<DspComponentInstallationDetails> {
    try {
      const dspInstallationProperties = this.getInstallationPropertiesByProvider(
        context,
        installationPropertiesArr,
        component.provider
      );
      const dspTenantId = dspInstallationProperties.systemTenant || context.tenantId!;
      const dspContext = this.createDSPContext(dspTenantId, context);

      const gaFF = await dspContext.isFeatureFlagActive("DWCO_BDC_GA");
      logInfo(`DWCO_BDC_GA feature flag is ${gaFF}`, { context });

      const acnPackageId = await getPackageId(component.name, component.version, "DWC", dspContext);
      if (acnPackageId === -1) {
        throw new Error(
          `DSP uninstall package id not found. component name: "${component.name}, component version: "${component.version}"`
        );
      }

      const params: ICreateAcnDeleteJobParams = {
        acnPackageId,
      };

      logInfo(
        `uninstall dsp acn package with params ${JSON.stringify(params)}.  component name: ${
          component.name
        }, component version: ${component.version}`,
        { context }
      );

      const offboardingResponse: IBdcOffboardingResponse = await BdcOffboardingController.offboarding(
        dspContext,
        params
      );
      logInfo(
        `uninstall dsp package response.
          jobId: ${offboardingResponse.acnDeleteJobId},
          jobLocation: ${offboardingResponse.acnDeleteJobLocation},
          message: ${offboardingResponse.message}`,
        { context }
      );

      return {
        importJobId: offboardingResponse.acnDeleteJobId,
        replicationFlowJobId: "",
      };
    } catch (error) {
      logError(
        [
          `failed to uninstall dsp package. component name: ${component.name}, component version: ${component.version}`,
          error,
        ],
        { context }
      );
      throw error;
    }
  }

  private createDSPContext(tenantId: string, context: BDCRequestContext): IRequestContext {
    const dspContext = RequestContext.createFromTenantId(tenantId, {
      spanContext: context.spanContext,
      preventCustomerHanaAutoUpgrade: true,
    });
    dspContext.correlationId = context.correlationId;
    Object.defineProperty(dspContext.userInfo, "userId", {
      value: context.userInfo.userId,
      writable: true,
    });
    return dspContext;
  }
}
