/** @format */

import { IRequestContext, StatusType } from "@sap/deepsea-types";
import { BDCPackageInstallationProperties, BDCProvider, BDCRequestContext } from "../../../shared/bdccockpit/Types";
import { RequestContext } from "../../repository/security/requestContext";
import { AcnClient } from "../acnProvision/acnClient";
import { getPackageId } from "../acnProvision/acnIntegration";
import { BdcOnboardingController } from "../acnProvision/onboarding/bdcOnboardingController";
import { isValidJobId } from "../acnProvision/onboarding/utils";
import { IBdcOnboardingPayload, ICreateAcnDeleteJobParams, ICreateAcnDeleteJobResult } from "../acnProvision/types";
import { SingleComponentInstallation } from "../lifecycleManagement/lifecycleManagementTypes";
import {
  InstallationStatus,
  InstallationStatusCode,
  SacComponentInstallationDetails,
} from "../packageInstallation/packageInstallationTypes";
import { BDCPackageComponent } from "../packages/packagesTypes";
import { getBdcLogger } from "../utils/logUtils";
import { ComponentInstaller } from "./componentInstaller";
import { IAsyncInstaller } from "./installerTypes";

const { logInfo, logError } = getBdcLogger(__filename);

export class SACContentInstaller
  extends ComponentInstaller
  implements IAsyncInstaller<SacComponentInstallationDetails>
{
  async isReadyToInstall(
    context: IRequestContext,
    singleComponentInstallation: SingleComponentInstallation
  ): Promise<boolean> {
    return true;
  }
  async install(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationPropertiesArr: BDCPackageInstallationProperties[]
  ): Promise<SacComponentInstallationDetails> {
    const installationProperties = this.getInstallationPropertiesByProvider(
      context,
      installationPropertiesArr,
      component.provider
    );
    let componentInstallationStatus = {} as SacComponentInstallationDetails;
    const sacContext = this.createSACContext(installationProperties.systemTenant, context);

    try {
      const acnPackageId = await getPackageId(component.name, component.version, "SAC", sacContext);
      if (acnPackageId === -1) {
        throw new Error(
          `ANC package id not found. component name: "${component.name}, component version: "${component.version}"`
        );
      }

      const importACNVersionFF = await context.isFeatureFlagActive("DWCO_BDC_COCKPIT_SPECIFIC_ACN_VERSION_IMPORT");
      const params: IBdcOnboardingPayload = {
        acnPackageId,
        ...(importACNVersionFF && { packageVersion: component.version }),
      };

      logInfo(`install sac acn package with params ${JSON.stringify(params)}`, { context });

      componentInstallationStatus = await BdcOnboardingController.onboardingSAC(params, sacContext);
      const { importJobId } = componentInstallationStatus;
      if (!isValidJobId(importJobId)) {
        throw new Error(`invalid jobId: ${JSON.stringify(componentInstallationStatus)}`);
      }
    } catch (error) {
      logError(["failed to install SAC ACN package", error], { context });
      throw error;
    }
    return componentInstallationStatus;
  }

  async getStatus(
    context: IRequestContext,
    componentInstallationStatus: SacComponentInstallationDetails,
    installationPropertiesArr: BDCPackageInstallationProperties[],
    provider: BDCProvider,
    installing: boolean
  ): Promise<InstallationStatus> {
    const installationProperties = this.getInstallationPropertiesByProvider(
      context,
      installationPropertiesArr,
      provider
    );
    const sacContext = this.createSACContext(installationProperties.systemTenant, context);
    const status = await BdcOnboardingController.getSACOnboardingJobsStatus(componentInstallationStatus, sacContext);
    if (!status.importJobStatus) {
      throw new Error(`job status is undefined`);
    }
    const action = installing ? "install" : "uninstall";

    logInfo(
      `get status: ${action}, sac acn package jobId:${componentInstallationStatus.importJobId},
       status:${status.importJobStatus}`,
      { context }
    );

    let installationStatus: InstallationStatusCode;
    let installationMessage = "";
    const importJobStatus = status.importJobStatus;
    switch (importJobStatus) {
      case StatusType.Succeed:
      case StatusType.Warning:
        installationStatus = "DONE";
        break;
      case StatusType.Aborted:
      case StatusType.Failed:
        installationStatus = "FAILED";
        installationMessage = `get status: ${action}, fail jobId: ${componentInstallationStatus.importJobId},
           jobStatus: ${status.importJobStatus}`;
        logError(installationMessage, { context });
        break;
      case StatusType.Pending:
      case StatusType.Executing:
        installationStatus = "EXECUTING";
        break;
      default:
        throw new Error(`Unknown status ${importJobStatus}`);
    }
    return { installationStatusCode: installationStatus, installationStatusMessage: installationMessage };
  }

  async uninstall(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationPropertiesArr: BDCPackageInstallationProperties[]
  ): Promise<SacComponentInstallationDetails> {
    const installationProperties = this.getInstallationPropertiesByProvider(
      context,
      installationPropertiesArr,
      component.provider
    );
    const sacContext = this.createSACContext(installationProperties.systemTenant, context);

    try {
      const acnPackageId = await getPackageId(component.name, component.version, "SAC", sacContext);
      if (acnPackageId === -1) {
        throw new Error(
          `SAC uninstall package id not found. component name: "${component.name}, component version: "${component.version}"`
        );
      }

      const params: ICreateAcnDeleteJobParams = {
        acnPackageId,
      };

      logInfo(
        `uninstall sac acn package with params ${JSON.stringify(params)}.  component name: ${
          component.name
        }, component version: ${component.version}`,
        { context }
      );

      const acnJobResult: ICreateAcnDeleteJobResult = await AcnClient.createAcnDeleteJob(sacContext, params);
      logInfo(
        `uninstall sac acn package response. jobId: ${acnJobResult.acnDeleteJobId}, jobLocation: ${acnJobResult.acnDeleteJobLocation}`,
        { context }
      );

      return {
        importJobId: acnJobResult.acnDeleteJobId,
      };
    } catch (error) {
      logError(
        [
          `failed to uninstall SAC package. component name: ${component.name}, component version: ${component.version}`,
          error,
        ],
        { context }
      );
      throw error;
    }
  }

  private createSACContext(tenantId: string, context: BDCRequestContext): IRequestContext {
    const sacContext = RequestContext.createFromTenantId(tenantId, {
      spanContext: context.spanContext,
      preventCustomerHanaAutoUpgrade: true,
    });
    sacContext.correlationId = context.correlationId;
    return sacContext;
  }
}
