/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { BDCDataProduct, BDCPackageInstallationProperties } from "../../../../shared/bdccockpit/Types";
import { logInfo } from "../../../connections/connectionStatus";
// import { fosConfigHandlerInstance } from "../../ucl/integratedSystems/systemsConfig/fosConfig";
import { SingleComponentInstallation } from "../../lifecycleManagement/lifecycleManagementTypes";
import {
  FosComponentInstallationDetails,
  InstallationStatus,
} from "../../packageInstallation/packageInstallationTypes";
import { BDCPackageComponent } from "../../packages/packagesTypes";
import { ComponentInstaller } from "../componentInstaller";
import { IAsyncInstaller } from "../installerTypes";

export class DataProductInstallerMock
  extends ComponentInstaller
  implements IAsyncInstaller<FosComponentInstallationDetails[]>
{
  async isReadyToInstall(
    context: IRequestContext,
    singleComponentInstallation: SingleComponentInstallation
  ): Promise<boolean> {
    return true;
  }
  async install(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationPropertiesArr: BDCPackageInstallationProperties[]
  ): Promise<FosComponentInstallationDetails[]> {
    logInfo(
      `[install] component: ${JSON.stringify(component)}, installationPropertiesArr: ${JSON.stringify(
        installationPropertiesArr
      )}`,
      { context }
    );
    const installationProperties = this.getInstallationPropertiesByProvider(
      context,
      installationPropertiesArr,
      component.provider
    );
    logInfo(`[install] installationProperties: ${JSON.stringify(installationProperties)}`, { context });
    const componentDataProduct: BDCDataProduct = component as BDCDataProduct; // TODO Check correct type
    // // TODO - return mock value for demo
    // if (componentDataProduct.ordid === "sap.s4.dataProduct:DummyDP") {
    //   return [{ ordId: componentDataProduct.ordid, installationStatus: "PENDING" }];
    // }
    return [{ ordId: componentDataProduct.ordid }];
  }

  async uninstall(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationProperties: BDCPackageInstallationProperties[]
  ): Promise<FosComponentInstallationDetails[]> {
    return [] as FosComponentInstallationDetails[];
  }

  async getStatus(
    context: IRequestContext,
    componentInstallationStatus: FosComponentInstallationDetails[],
    installationPropertiesArr: BDCPackageInstallationProperties[]
  ): Promise<InstallationStatus> {
    return { installationStatusCode: "DONE", installationStatusMessage: "" };
  }
}
