/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { BDCPackageInstallationProperties } from "../../../../shared/bdccockpit/Types";
import { SingleComponentInstallation } from "../../lifecycleManagement/lifecycleManagementTypes";
import {
  DspComponentInstallationDetails,
  InstallationStatus,
} from "../../packageInstallation/packageInstallationTypes";
import { BDCPackageComponent } from "../../packages/packagesTypes";
import { ComponentInstaller } from "../componentInstaller";
import { IAsyncInstaller } from "../installerTypes";

export class DSPContentInstallerMock
  extends ComponentInstaller
  implements IAsyncInstaller<DspComponentInstallationDetails>
{
  async isReadyToInstall(
    context: IRequestContext,
    singleComponentInstallation: SingleComponentInstallation
  ): Promise<boolean> {
    return true;
  }
  async install(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationPropertiesArr: BDCPackageInstallationProperties[]
  ): Promise<DspComponentInstallationDetails> {
    return {
      importJobId: "j1",
      replicationFlowJobId: "j2",
    };
  }

  async uninstall(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationProperties: BDCPackageInstallationProperties[]
  ): Promise<DspComponentInstallationDetails> {
    return {} as DspComponentInstallationDetails;
  }

  async getStatus(
    context: IRequestContext,
    componentInstallationStatus: DspComponentInstallationDetails,
    installationPropertiesArr: BDCPackageInstallationProperties[]
  ): Promise<InstallationStatus> {
    return { installationStatusCode: "DONE", installationStatusMessage: "" };
  }
}
