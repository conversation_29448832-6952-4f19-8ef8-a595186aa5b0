/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { BDCPackageInstallationProperties } from "../../../../shared/bdccockpit/Types";
import { SingleComponentInstallation } from "../../lifecycleManagement/lifecycleManagementTypes";
import {
  InstallationStatus,
  SacComponentInstallationDetails,
} from "../../packageInstallation/packageInstallationTypes";
import { BDCPackageComponent } from "../../packages/packagesTypes";
import { ComponentInstaller } from "../componentInstaller";
import { IAsyncInstaller } from "../installerTypes";

export class SACContentInstallerMock
  extends ComponentInstaller
  implements IAsyncInstaller<SacComponentInstallationDetails>
{
  async isReadyToInstall(
    context: IRequestContext,
    singleComponentInstallation: SingleComponentInstallation
  ): Promise<boolean> {
    return true;
  }
  async install(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationPropertiesArr: BDCPackageInstallationProperties[]
  ): Promise<SacComponentInstallationDetails> {
    return { importJobId: "ijId1" };
  }

  async uninstall(
    context: IRequestContext,
    component: BDCPackageComponent,
    installationProperties: BDCPackageInstallationProperties[]
  ): Promise<SacComponentInstallationDetails> {
    return {} as SacComponentInstallationDetails;
  }

  async getStatus(
    context: IRequestContext,
    componentInstallationStatus: SacComponentInstallationDetails,
    installationPropertiesArr: BDCPackageInstallationProperties[]
  ): Promise<InstallationStatus> {
    return { installationStatusCode: "DONE", installationStatusMessage: "" };
  }
}
