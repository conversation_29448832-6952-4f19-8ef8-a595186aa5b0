/** @format */

import { TimeUnit } from "@sap/deepsea-utils";
import { IX509Credentials, SecureStoreKey } from "@sap/dwc-credentials";
import { httpClient } from "@sap/dwc-http-client";
import { ITenantInformation, TenantInformationProvider } from "@sap/dwc-tms-provider";
import Status from "http-status-codes";
import { BDCRequestContext } from "../../../shared/bdccockpit/Types";
import { isCanary } from "../../lib/node";
import { IRequestContext } from "../../repository/security/common/common";
import { ExternalCallCategory } from "../../repository/security/common/externalCallTypes";
import { RequestContext } from "../../repository/security/requestContext";
import { TenantSKU } from "../entitlement/entitlementTypes";
import { getTenantCredentials } from "../utils/certificateUtils";
import { getBdcLogger } from "../utils/logUtils";
import { getEntitlementTypeAndGroup } from "./entitlementUtils";

const { logInfo, logError } = getBdcLogger(__filename);

const VERSION = "v1";

enum baseUrl {
  canary = "canary.resource.api.sap",
  production = "global.resource.api.sap",
}

enum UnifiedServicesUrl {
  getOrgPath = "/api/resources/system.resource.api.sap/v1/filteredresources?resourceSelector=customer-landscape.resource.api.sap/v1alpha1/TenantData&recursive=true&fieldSelector=spec.localTenantId=",
  getAllEntitlements = "/v1/resource_access_review",
}

export interface TenantData {
  items: [
    {
      resource: {
        metadata: {
          mixedInto?: {
            apiVersion: string;
            type: string;
          };
          name?: string;
          path: string;
        };
        spec?: {
          systemRole: string;
          tenantId: number;
        };
      };
    }
  ];
}

function getUnifiedServicesBaseUrl() {
  return isCanary() ? baseUrl.canary : baseUrl.production;
}

export async function getTenantEntitlementByName(
  context: IRequestContext,
  entitlementName: string
): Promise<boolean | undefined> {
  const entitlements: Map<string, boolean> = await exports.getTenantEntitlements(context);
  return entitlements.get(entitlementName);
}

export async function getTenantEntitlements(context: BDCRequestContext): Promise<Map<string, boolean>> {
  // create ONE certificate chain for all tenants in the landscape
  // create a universal context, not connected to a specific tenant id
  const tenantIdForCredentialsContext = "BDCC";
  const contextForCredentials: RequestContext = RequestContext.createFromTenantId(tenantIdForCredentialsContext, {
    spanContext: context.spanContext,
  });
  // contextForCredentials.correlationId = context.correlationId;

  const credentials: IX509Credentials = await getTenantCredentials(
    contextForCredentials,
    SecureStoreKey.UclTenantCredentials,
    "TENANT"
  );
  const baseUrl: string = getUnifiedServicesBaseUrl();
  const tenantData: TenantData | undefined = await exports.getTenantData(context, credentials, baseUrl);
  const allEntitlements: Map<string, boolean> = await getAllEntitlements(context, credentials, baseUrl, tenantData);
  return allEntitlements;
}

export async function getTenantData(
  context: IRequestContext,
  credentials: IX509Credentials,
  baseUrl: string
): Promise<TenantData | undefined> {
  let response: any;
  try {
    response = await httpClient.call({
      url: `https://${baseUrl}${UnifiedServicesUrl.getOrgPath}${context.tenantId}`,
      opts: {
        method: "GET",
        requestContext: context,
        callTimeout: TimeUnit.SECONDS.toMillis(45),
        callCategory: ExternalCallCategory.UCL,
        key: [{ pem: credentials.privateKey, passphrase: credentials.passphrase }],
        cert: credentials.clientCertificateChain,
        minVersion: "TLSv1.2",
        rejectUnauthorized: true,
      },
    });
    if (response?.status === Status.OK) {
      logInfo(`Tenant data for tenantId=${context.tenantId}: ${JSON.stringify(response.body)}`, { context });
      return response.body as TenantData;
    }
  } catch (err) {
    logError(`Failed to get data for tenantId=${context.tenantId}. Error: ${err}`, { context });
  }
  logError(`Failed to get data for tenantId=${context.tenantId}. Response: ${JSON.stringify(response)}`, { context });
}

async function getAllEntitlements(
  context: IRequestContext,
  credentials: IX509Credentials,
  baseUrl: string,
  tenantData: TenantData | undefined
): Promise<Map<string, boolean>> {
  const entitlements = new Map<string, boolean>();
  const orgPath: string | undefined = tenantData?.items?.[0]?.resource?.metadata?.path;
  try {
    if (!orgPath) {
      logError(`No organization path for tenantId=${context.tenantId}. Tenant data=${JSON.stringify(tenantData)}`, {
        context,
      });
      if (await context.isFeatureFlagActive("DWCO_BDC_CIC_ENTITLEMENT_CHECK")) {
        await checkCICLicenses(context, entitlements);
      }
      return entitlements;
    }
    const entitlementTypeAndGroupMap: Map<string, string> = getEntitlementTypeAndGroup();
    for (const [type, group] of entitlementTypeAndGroupMap) {
      const allowed: boolean | undefined = await exports.getEntitlement(
        context,
        credentials,
        baseUrl,
        orgPath,
        group,
        type
      );
      if (allowed !== undefined) {
        entitlements.set(type, allowed);
      }
    }
  } catch (err) {
    logError(`Failed to get organization path for tenantId=${context.tenantId}. Error: ${err}`, { context });
  }
  return entitlements;
}

export async function getEntitlement(
  context: IRequestContext,
  credentials: IX509Credentials,
  baseUrl: string,
  orgPath: string | undefined,
  group: string,
  type: string
): Promise<boolean | undefined> {
  const requestBody = buildRequestBody(group, type);
  let response;
  try {
    response = await httpClient.call({
      url: `https://${baseUrl}${UnifiedServicesUrl.getAllEntitlements}${orgPath}`,
      opts: {
        method: "POST",
        requestContext: context,
        callTimeout: TimeUnit.SECONDS.toMillis(45),
        callCategory: ExternalCallCategory.UCL,
        acceptedStatusCodes: [Status.OK, Status.BAD_REQUEST, Status.TOO_MANY_REQUESTS],
        body: requestBody,
        key: [{ pem: credentials.privateKey, passphrase: credentials.passphrase }],
        cert: credentials.clientCertificateChain,
        minVersion: "TLSv1.2",
        rejectUnauthorized: true,
      },
    });
    if (response?.status === Status.OK) {
      const responseArr = response.body as any;
      return responseArr?.[0]?.status?.allowed;
    }
  } catch (err) {
    logError(`Failed to get entitlements for tenantId=${context.tenantId}. Error: ${err}`, { context });
  }
  logError(`Failed to get entitlements for tenantId=${context.tenantId}. Response: ${JSON.stringify(response)}.`, {
    context,
  });
}

function buildRequestBody(group: string, type: string): any[] {
  return [
    {
      action: "get",
      gvt: {
        group,
        version: VERSION,
        type,
      },
      definitionPath: "/",
      name: "*",
    },
  ];
}

export async function checkCICLicenses(context: IRequestContext, entitlements: Map<string, boolean>): Promise<void> {
  const tenantInfo: ITenantInformation | undefined = await TenantInformationProvider.getTenantInformation(context);
  logInfo(`tenantId=${context.tenantId}, tenantInfo: ${JSON.stringify(tenantInfo?.license)}`, { context });
  entitlements.set(TenantSKU.S4PCE.toString(), tenantInfo?.license?.thresholdBdcIASKUS4PCE === "1");
  entitlements.set(TenantSKU.HCM.toString(), tenantInfo?.license?.thresholdBdcIASKUHCM === "1");
  entitlements.set(TenantSKU.S4.toString(), tenantInfo?.license?.thresholdBdcIASKUS4PuC === "1");
}
