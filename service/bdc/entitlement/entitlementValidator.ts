/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import {
  ApplicationNamespace,
  BDCAvailablePackage,
  BDCInstalledPackage,
  BDCPackageInstallationProperties,
} from "../../../shared/bdccockpit/Types";
import { getTenantEntitlementByName } from "../entitlement/unifiedServicesApiClient";
import { getBdcLogger } from "../utils/logUtils";
import { createEntitlement, getEntitlement, updateEntitlement } from "./entitlementDao";
import { EntitlementService } from "./entitlementService";
import { TenantEntitlement } from "./entitlementTypes";
import { applicationNamespaceToTenantSKU, sourceSystemToTenantSKU } from "./entitlementUtils";

const { logInfo, logError } = getBdcLogger(__filename);

export async function filterAvailablePackagesByEntitlement(
  context: IRequestContext,
  availablePackages: BDCAvailablePackage[]
): Promise<BDCAvailablePackage[]> {
  let checkedPackages: BDCAvailablePackage[] = [];
  try {
    if (!availablePackages?.length) {
      return [];
    }
    const packagesMap = groupPackagesBySKU(availablePackages);
    for (const sku of packagesMap.keys()) {
      let validEntitlement: boolean | undefined = true;
      if (await (context as IRequestContext).isFeatureFlagActive("DWCO_BDC_ENTITLEMENT_CHECK")) {
        validEntitlement = await getTenantEntitlementByName(context, sku);
      }
      if (validEntitlement) {
        checkedPackages = checkedPackages.concat(packagesMap.get(sku));
      }
    }
  } catch (err) {
    logError(`Failed to get tenant's available packages by entitlement. Error ${err}`, { context });
  }
  return checkedPackages;
}

export async function updateGracePeriodForInstalledPackagesByEntitlement(
  context: IRequestContext,
  installedPackages: Array<Partial<BDCInstalledPackage>>
): Promise<Array<Partial<BDCInstalledPackage>>> {
  let checkedPackages = new Array<Partial<BDCInstalledPackage>>();
  try {
    if (!installedPackages?.length) {
      return [];
    }
    const packagesMap = groupPackagesBySKU(installedPackages);
    for (const sku of packagesMap.keys()) {
      const packagesList = packagesMap.get(sku);
      await exports.updateGracePeriodIfStarted(context, sku, packagesList);
      // export calls needed to being able to stub a method inside the file
      checkedPackages = checkedPackages.concat(packagesList);
    }
  } catch (err) {
    logError(`Failed to get tenant's installed packages by entitlement. Error ${err}`, { context });
  }
  return checkedPackages;
}

export async function allowedToInstall(
  context: IRequestContext,
  installationProperties: BDCPackageInstallationProperties[]
): Promise<boolean | undefined> {
  const tenantSKU = installationPropertiesToTenantSKU(installationProperties);
  return await getTenantEntitlementByName(context, tenantSKU);
}

export async function getMissingEntitlementDetails(
  context: IRequestContext,
  installationProperties: BDCPackageInstallationProperties[]
): Promise<{
  gracePeriodStart: string | null | undefined;
  gracePeriodDuration: number;
}> {
  const tenantSKU = installationPropertiesToTenantSKU(installationProperties);
  const tenantEntitlement = await getEntitlement(context, tenantSKU);
  if (tenantEntitlement && !tenantEntitlement.gracePeriodStart) {
    tenantEntitlement.gracePeriodStart = new Date().toISOString();
    await updateEntitlement(context, tenantEntitlement);
  }
  return {
    gracePeriodStart: tenantEntitlement?.gracePeriodStart,
    gracePeriodDuration: EntitlementService.getGracePeriodDuration(),
  };
}

export function installationPropertiesToTenantSKU(installationProperties: BDCPackageInstallationProperties[]) {
  const applicationNamespaces: ApplicationNamespace[] = installationProperties?.map(
    (installationProperty) => installationProperty.applicationNamespace
  );
  return applicationNamespaceToTenantSKU(applicationNamespaces);
}

// returns a map of all sku and its packages (Map<sku, packages[]>)
export function groupPackagesBySKU(packages: any): any {
  const checkedEntitlements = new Map<string, any>();
  packages?.forEach((pack: any) => {
    if (pack.systemType) {
      const sku = sourceSystemToTenantSKU(pack.systemType);
      let packagesList = checkedEntitlements.get(sku);
      if (packagesList === undefined) {
        checkedEntitlements.set(sku, [pack]);
      } else {
        packagesList = packagesList.concat(pack);
        checkedEntitlements.set(sku, packagesList);
      }
    }
  });
  return checkedEntitlements;
}

export async function updateGracePeriodIfStarted(
  context: IRequestContext,
  tenantSKU: string,
  installedPackages: Array<Partial<BDCInstalledPackage>>
): Promise<void> {
  try {
    let tenantEntitlement, validEntitlement;

    await Promise.all([
      (tenantEntitlement = await getEntitlement(context, tenantSKU)), // from catalog
      (validEntitlement = await getTenantEntitlementByName(context, tenantSKU)), // from Unified Services
    ]);

    if (!tenantEntitlement) {
      logInfo(`Entitlement=${tenantSKU} not found at the catalog for tenantId=${context?.tenantId}.`, { context });
      return;
    }

    if (validEntitlement) {
      // valid entitlement
      if (tenantEntitlement?.gracePeriodStart) {
        // entitlement registered, tenant was in grace period
        await endGracePeriod(context, tenantEntitlement, installedPackages);
      }
    } else {
      // no valid entitlement (false or undefined)
      logInfo(`No entitlement=${tenantSKU} for tenantId=${context?.tenantId}.`, { context });

      if (tenantEntitlement && !tenantEntitlement.gracePeriodStart) {
        // entitlement registered, no grace period started yet
        tenantEntitlement.gracePeriodStart = new Date().toISOString();
        await updateEntitlement(context, tenantEntitlement);
      }
      tenantEntitlement?.gracePeriodStart &&
        updateInstalledPackages(installedPackages, new Date(tenantEntitlement.gracePeriodStart));
    }
  } catch (err) {
    logError(`Failed to check entitlement=${tenantSKU} for grace period update for tenantId=${context?.tenantId}.`, {
      context,
    });
  }
}

export async function registerEntitlement(context: IRequestContext, entitlementName: string): Promise<void> {
  const tenantEntitlement: TenantEntitlement = {
    entitlement: entitlementName,
    lastCheckTime: new Date().toISOString(),
  };
  await createEntitlement(context, tenantEntitlement);
}

async function endGracePeriod(
  context: IRequestContext,
  tenantEntitlement: TenantEntitlement,
  installedPackages?: Array<Partial<BDCInstalledPackage>> | undefined
): Promise<void> {
  tenantEntitlement.gracePeriodStart = null;
  await updateEntitlement(context, tenantEntitlement);
  installedPackages && updateInstalledPackages(installedPackages, null);
}

export async function updateLastTimeCheck(
  context: IRequestContext,
  tenantEntitlement: TenantEntitlement,
  installedPackages?: Array<Partial<BDCInstalledPackage>>
): Promise<void> {
  tenantEntitlement.lastCheckTime = new Date().toISOString();
  await updateEntitlement(context, tenantEntitlement);
  installedPackages &&
    tenantEntitlement.gracePeriodStart &&
    updateInstalledPackages(installedPackages, new Date(tenantEntitlement.gracePeriodStart));
}

function updateInstalledPackages(
  installedPackages: Array<Partial<BDCInstalledPackage>> | undefined,
  date: Date | null
): void {
  const gracePeriodDuration = date ? EntitlementService.getGracePeriodDuration() : null;
  installedPackages?.forEach((installedPackage) => {
    installedPackage.gracePeriodStart = date;
    installedPackage.gracePeriodDuration = gracePeriodDuration;
  });
}
