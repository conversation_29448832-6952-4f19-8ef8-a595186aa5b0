/** @format */

import { Counter, Meter, ValueType } from "@opentelemetry/api";
import telemetry from "@sap/dwc-telemetry";
import { BDCRequestContext } from "../../../shared/bdccockpit/Types";
import { TenantSKU } from "../entitlement/entitlementTypes";
import { getBdcLogger } from "../utils/logUtils";

const { logInfo, logError } = getBdcLogger(__filename);

/**
 * Interface for BDC Metrics Service
 */
export interface IBdcMetricsService {
  /**
   * Increment the counter for Solace messages received.
   * @param messageType - The type of message received.
   * @param context - The request context.
   */
  incrementSolaceMessagesReceived(messageType: "Installation" | "Entitlement", context: BDCRequestContext): void;

  /**
   * Increment the counter for product installations.
   * @param status - The status of the installation.
   * @param context - The request context.
   * @param incrementBy - How much to increment (optional).
   */
  incrementDataProductInstallations(
    status: "Successful" | "Failed",
    context: BDCRequestContext,
    incrementBy?: number
  ): void;

  /**
   * Increment the counter for package installations.
   * @param packageType - The type of package.
   * @param status - The status of the installation.
   * @param sku - The SKU of the tenant.
   * @param context - The request context.
   */
  incrementBdcPackageInstallations(
    packageType: "InsightApp" | "DataPackage",
    status: "Successful" | "Failed",
    sku: TenantSKU,
    context: BDCRequestContext
  ): void;

  /**
   * Increment the counter for package uninstallations.
   * @param packageType - The type of package.
   * @param status - The status of the uninstallation.
   * @param sku - The SKU of the tenant.
   * @param context - The request context.
   */
  incrementBdcPackageUninstallations(
    packageType: "InsightApp" | "DataPackage",
    status: "Successful" | "Failed",
    sku: TenantSKU,
    context: BDCRequestContext
  ): void;

  /**
   * Increment the counter for package updates.
   * @param packageType - The type of package.
   * @param status - The status of the update.
   * @param sku - The SKU of the tenant.
   * @param context - The request context.
   */
  incrementBdcPackageUpdates(
    packageType: "InsightApp" | "DataPackage",
    status: "Successful" | "Failed",
    sku: TenantSKU,
    context: BDCRequestContext
  ): void;

  /**
   * Increment the counter for entitlement checks.
   * @param status - The status of the entitlement check.
   * @param context - The request context.
   */
  incrementEntitlementChecks(status: "Successful" | "Failed", context: BDCRequestContext): void;

  /**
   * Increment the counter for ACN installations.
   * @param status - The status of the installation.
   * @param context - The request context.
   * @param incrementBy - How much to increment (optional).
   */
  incrementAcnPackageInstallations(
    status: "Successful" | "Failed",
    context: BDCRequestContext,
    incrementBy?: number
  ): void;

  /**
   * Increment the counter for connection checks.
   * @param status - The status of the connection check.
   * @param context - The request context.
   */
  incrementConnectionChecks(status: "Successful" | "Failed", context: BDCRequestContext): void;
}

/**
 * BDC Metrics Service class
 */
export class BdcMetricsService implements IBdcMetricsService {
  private static instance: IBdcMetricsService;
  private solaceMessagesReceivedCounter: Counter;
  private productInstallationsCount: Counter;
  private packageInstallationsCount: Counter;
  private packageUninstallationsCount: Counter;
  private packageUpdatesCount: Counter;
  private acnInstallationsCount: Counter;
  private entitlementChecksCount: Counter;
  private connectionChecksCount: Counter;

  /**
   * Private constructor to initialize the metrics.
   * @param meter - The meter instance for creating counters.
   */
  private constructor(private meter: Meter) {
    this.initializeMetrics();
  }

  /**
   * Get the singleton instance of BdcMetricsService.
   * @returns The singleton instance of BdcMetricsService.
   */
  public static getInstance(): IBdcMetricsService {
    if (!BdcMetricsService.instance) {
      const meter = telemetry.metrics.getMeter();
      BdcMetricsService.instance = new BdcMetricsService(meter);
    }
    return BdcMetricsService.instance;
  }

  /**
   * Initialize all the metrics counters.
   */
  private initializeMetrics(): void {
    this.solaceMessagesReceivedCounter = this.meter.createCounter("bdcc.v1.solace.messages.received.count", {
      description: "Counter indicating the number of messages received from Solace",
      valueType: ValueType.INT,
    });
    this.productInstallationsCount = this.meter.createCounter("bdcc.v1.product.installations.count", {
      description: "Total count of product installations",
      valueType: ValueType.INT,
    });
    this.packageInstallationsCount = this.meter.createCounter("bdcc.v1.package.installations.count", {
      description: "Total count of package installations",
      valueType: ValueType.INT,
    });
    this.packageUninstallationsCount = this.meter.createCounter("bdcc.v1.package.uninstallations.count", {
      description: "Total count of package uninstallations",
      valueType: ValueType.INT,
    });
    this.packageUpdatesCount = this.meter.createCounter("bdcc.v1.package.updates.count", {
      description: "Total count of package updates",
      valueType: ValueType.INT,
    });
    this.acnInstallationsCount = this.meter.createCounter("bdcc.v1.acn.installations.count", {
      description: "Total count of ACN package installations",
      valueType: ValueType.INT,
    });
    this.entitlementChecksCount = this.meter.createCounter("bdcc.v1.entitlement.checks.count", {
      description: "Total count of entitlement checks",
      valueType: ValueType.INT,
    });
    this.connectionChecksCount = this.meter.createCounter("bdcc.v1.connection.checks.count", {
      description: "Total count of connection checks",
      valueType: ValueType.INT,
    });
  }

  /**
   * Increment the counter for Solace messages received.
   * @param messageType - The type of message received.
   * @param context - The request context.
   */
  public incrementSolaceMessagesReceived(
    messageType: "Installation" | "Entitlement",
    context: BDCRequestContext
  ): void {
    try {
      logInfo("Sending solace messages received metric", { context });
      this.solaceMessagesReceivedCounter.add(1, { messageType, tenantId: context?.tenantId || "unknown" });
    } catch (error) {
      logError(`Error on incrementSolaceMessagesReceived: ${error}`, { context });
    }
  }

  /**
   * Increment the counter for product installations.
   * @param status - The status of the installation.
   * @param context - The request context.
   * @param incrementBy - How much to increment (1 by default).
   */
  public incrementDataProductInstallations(
    status: "Successful" | "Failed",
    context: BDCRequestContext,
    incrementBy: number = 1
  ): void {
    try {
      logInfo("Incrementing product installations counter", { context });
      this.productInstallationsCount.add(incrementBy, { tenantId: context?.tenantId || "unknown", status });
    } catch (error) {
      logError(`Error on incrementDataProductInstallations: ${error}`, { context });
    }
  }

  /**
   * Increment the counter for package installations.
   * @param packageType - The type of package.
   * @param status - The status of the installation.
   * @param sku - The SKU of the tenant.
   * @param context - The request context.
   */
  public incrementBdcPackageInstallations(
    packageType: "InsightApp" | "DataPackage",
    status: "Successful" | "Failed",
    sku: TenantSKU,
    context: BDCRequestContext
  ): void {
    try {
      logInfo("Incrementing package installations counter", { context });
      this.packageInstallationsCount.add(1, {
        packageType,
        status,
        sku: sku === TenantSKU.OTHER ? "unknown" : sku,
        tenantId: context?.tenantId || "unknown",
      });
    } catch (error) {
      logError(`Error on incrementBdcPackageInstallations: ${error}`, { context });
    }
  }

  /**
   * Increment the counter for package uninstallations.
   * @param packageType - The type of package.
   * @param status - The status of the uninstallation.
   * @param sku - The SKU of the tenant.
   * @param context - The request context.
   */
  public incrementBdcPackageUninstallations(
    packageType: "InsightApp" | "DataPackage",
    status: "Successful" | "Failed",
    sku: TenantSKU,
    context: BDCRequestContext
  ): void {
    try {
      logInfo("Incrementing package uninstallations counter", { context });
      this.packageUninstallationsCount.add(1, {
        packageType,
        status,
        sku: sku === TenantSKU.OTHER ? "unknown" : sku,
        tenantId: context?.tenantId || "unknown",
      });
    } catch (error) {
      logError(`Error on incrementBdcPackageUninstallations: ${error}`, { context });
    }
  }

  /**
   * Increment the counter for package updates.
   * @param packageType - The type of package.
   * @param status - The status of the update.
   * @param sku - The SKU of the tenant.
   * @param context - The request context.
   */
  public incrementBdcPackageUpdates(
    packageType: "InsightApp" | "DataPackage",
    status: "Successful" | "Failed",
    sku: TenantSKU,
    context: BDCRequestContext
  ): void {
    try {
      logInfo("Incrementing package updates counter", { context });
      this.packageUpdatesCount.add(1, {
        packageType,
        status,
        sku: sku === TenantSKU.OTHER ? "unknown" : sku,
        tenantId: context?.tenantId || "unknown",
      });
    } catch (error) {
      logError(`Error on incrementBdcPackageUpdates: ${error}`, { context });
    }
  }

  /**
   * Increment the counter for ACN installations.
   * @param status - The status of the installation.
   * @param context - The request context.
   * @param incrementBy - How much to increment (1 by default).
   */
  public incrementAcnPackageInstallations(
    status: "Successful" | "Failed",
    context: BDCRequestContext,
    incrementBy: number = 1
  ): void {
    try {
      logInfo("Incrementing ACN installations counter", { context });
      this.acnInstallationsCount.add(incrementBy, { tenantId: context?.tenantId || "unknown", status });
    } catch (error) {
      logError(`Error on incrementAcnPackageInstallations: ${error}`, { context });
    }
  }

  /**
   * Increment the counter for entitlement checks.
   * @param status - The status of the entitlement check.
   * @param context - The request context.
   */
  public incrementEntitlementChecks(status: "Successful" | "Failed", context: BDCRequestContext): void {
    try {
      logInfo("Incrementing entitlement checks counter", { context });
      this.entitlementChecksCount.add(1, { tenantId: context?.tenantId || "unknown", status });
    } catch (error) {
      logError(`Error on incrementEntitlementChecks: ${error}`, { context });
    }
  }

  /**
   * Increment the counter for connection checks.
   * @param status - The status of the connection check.
   * @param context - The request context.
   */
  public incrementConnectionChecks(status: "Successful" | "Failed", context: BDCRequestContext): void {
    try {
      logInfo("Incrementing connection checks counter", { context });
      this.connectionChecksCount.add(1, { tenantId: context?.tenantId || "unknown", status });
    } catch (error) {
      logError(`Error on incrementConnectionChecks: ${error}`, { context });
    }
  }
}
