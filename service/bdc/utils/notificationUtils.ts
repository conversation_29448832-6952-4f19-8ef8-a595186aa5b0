/** @format */
import { IRequestContext, NotificationLinkType, NotificationType } from "@sap/deepsea-types";
import { INotification, NotificationsClient } from "@sap/dwc-notifications";
import { ITechUserNotification } from "@sap/dwc-notifications/src/types";
import { BDCPackageType } from "../../../shared/bdccockpit/Enums";
import { MessageAction } from "../../messageQueuing/consumer/PackageInstallationConsumer";
import { createNotificationFailureException } from "./BDCException";
import {
  INotificationsHelper,
  SendOffboardFinishedParams,
  SendOffboardStartedParams,
  SendOnboardFinishedParams,
  SendOnboardStartedParams,
} from "./INotificationsHelper";
import { getBdcLogger } from "./logUtils";

const { logError } = getBdcLogger(__filename);

interface NotificationParam {
  context: IRequestContext;
  type: NotificationType;
  title: string;
  installationId?: string;
}

interface TitleGenerationParams {
  context: IRequestContext;
  packageName: string;
  notificationType: NotificationType;
  actionType:
    | "activation"
    | "installation"
    | "uninstallation"
    | "deactivation"
    | "updateDataPackage"
    | "updateIntelligentApplication"
    | "cleanupInstallation"
    | "cleanupActivation"
    | "retryDeactivation"
    | "retryUninstall";
  phase: "started" | "finished";
}

// TODO: the language content are stolen from "src\components\notifications\i18n\notifications\i18n_en.properties"
// They are needed until translatable notifications work with postForTechUser
class ConcreteNotificationsHelper implements INotificationsHelper {
  private readonly BDC_INSTALLED_PACKAGES_BASE_URL = "bdc_packages&/bdc_packages/installation/";

  private async sendNotification(notificationParameters: NotificationParam): Promise<void> {
    const notificationInfo: INotification = {
      recipientList: [notificationParameters.context.userInfo.userName!],
      type: notificationParameters.type,
      title: `${notificationParameters.title} ${this.getCorrelationId(notificationParameters.context)}`,
      linkType: NotificationLinkType.NO_LINK,
      showInShell: false,
      sendAsMail: false,
    };

    if (notificationParameters.installationId) {
      notificationInfo.linkType = NotificationLinkType.VIEW;
      notificationInfo.link = JSON.stringify({
        target: {
          shellHash: this.BDC_INSTALLED_PACKAGES_BASE_URL + notificationParameters.installationId,
        },
      });
    }
    await NotificationsClient.post(notificationParameters.context, notificationInfo);
  }

  private async sendNotificationForTechUser(notificationParameters: NotificationParam): Promise<void> {
    const notificationInfo: ITechUserNotification = {
      aUserList: [notificationParameters.context.userInfo.userName!],
      type: notificationParameters.type,
      title: `${notificationParameters.title} ${this.getCorrelationId(notificationParameters.context)}`,
      linkType: NotificationLinkType.NO_LINK,
      showInShell: false,
      sendAsMail: false,
    };

    if (notificationParameters.installationId) {
      notificationInfo.linkType = NotificationLinkType.VIEW;
      notificationInfo.link = JSON.stringify({
        target: {
          shellHash: this.BDC_INSTALLED_PACKAGES_BASE_URL + notificationParameters.installationId,
        },
      });
    }
    await NotificationsClient.postForTechUser(notificationParameters.context, notificationInfo);
  }

  async sendOffboardStartedNotification(params: SendOffboardStartedParams): Promise<void> {
    const { context, packageInstallation, action } = params;
    const isDataPackage = packageInstallation.originalPackage.type === BDCPackageType.DATA_PACKAGE;
    let title = "";

    try {
      switch (action) {
        case MessageAction.UNINSTALL:
          title = this.getTitle({
            context,
            packageName: packageInstallation.originalPackage.name,
            notificationType: NotificationType.SUCCESS,
            actionType: isDataPackage ? "deactivation" : "uninstallation",
            phase: "started",
          });
          break;
        case MessageAction.RETRY_UNINSTALL:
          title = this.getTitle({
            context,
            packageName: packageInstallation.originalPackage.name,
            notificationType: NotificationType.SUCCESS,
            actionType: isDataPackage ? "retryDeactivation" : "retryUninstall",
            phase: "started",
          });
          break;
        case MessageAction.CLEANUP:
          title = this.getTitle({
            context,
            packageName: packageInstallation.originalPackage.name,
            notificationType: NotificationType.SUCCESS,
            actionType: isDataPackage ? "cleanupActivation" : "cleanupInstallation",
            phase: "started",
          });
          break;
        default:
          const errorMessage = `Unknown message action: ${action}`;
          logError(errorMessage, { context });
          return;
      }

      await this.sendNotification({
        context,
        type: NotificationType.SUCCESS,
        title,
      });
    } catch (error) {
      logError([`Failed to send offboard started notification`, error], { context });
    }
  }

  async sendOffboardFinishedNotification(params: SendOffboardFinishedParams): Promise<void> {
    const { context, notificationType, packageInstallation, action } = params;
    const isDataPackage = packageInstallation.originalPackage.type === BDCPackageType.DATA_PACKAGE;
    let title = "";

    try {
      switch (action) {
        case MessageAction.FORCE_UNINSTALL:
        case MessageAction.UNINSTALL:
          title = this.getTitle({
            context,
            packageName: packageInstallation.originalPackage.name,
            notificationType,
            actionType: isDataPackage ? "deactivation" : "uninstallation",
            phase: "finished",
          });
          break;
        case MessageAction.CLEANUP:
          title = this.getTitle({
            context,
            packageName: packageInstallation.originalPackage.name,
            notificationType,
            actionType: isDataPackage ? "cleanupActivation" : "cleanupInstallation",
            phase: "finished",
          });
          break;
        case MessageAction.RETRY_UNINSTALL:
          title = this.getTitle({
            context,
            packageName: packageInstallation.originalPackage.name,
            notificationType,
            actionType: isDataPackage ? "retryDeactivation" : "retryUninstall",
            phase: "finished",
          });
          break;
        default:
          const errorMessage = `Unknown message action: ${action}`;
          logError(errorMessage, { context });
          return;
      }

      await this.sendNotificationForTechUser({
        context,
        type: notificationType,
        title,
        installationId: packageInstallation.installationId,
      });
    } catch (error) {
      logError([`Failed to send offboard finished notification`, error], { context });
    }
  }

  async sendOnboardStartedNotification(params: SendOnboardStartedParams): Promise<void> {
    const { context, packageInfo, isUpdating = false } = params;
    const isDataPackage = packageInfo.type === BDCPackageType.DATA_PACKAGE;

    try {
      const title = this.getTitle({
        context,
        packageName: packageInfo.name,
        notificationType: NotificationType.SUCCESS,
        actionType: isUpdating
          ? isDataPackage
            ? "updateDataPackage"
            : "updateIntelligentApplication"
          : isDataPackage
          ? "activation"
          : "installation",
        phase: "started",
      });

      await this.sendNotification({
        context,
        type: NotificationType.SUCCESS,
        title,
      });
    } catch (error) {
      logError([`Failed to send onboard started notification`, error], { context });
    }
  }

  async sendOnboardFinishedNotification(params: SendOnboardFinishedParams): Promise<void> {
    const { context, notificationType, packageInstallation, isUpdating = false } = params;
    const isDataPackage = packageInstallation.originalPackage.type === BDCPackageType.DATA_PACKAGE;

    try {
      const title = this.getTitle({
        context,
        packageName: packageInstallation.originalPackage.name,
        notificationType,
        actionType: isUpdating
          ? isDataPackage
            ? "updateDataPackage"
            : "updateIntelligentApplication"
          : isDataPackage
          ? "activation"
          : "installation",
        phase: "finished",
      });

      await this.sendNotificationForTechUser({
        context,
        type: notificationType,
        title,
        installationId: packageInstallation.installationId,
      });
    } catch (error) {
      logError([`Failed to send onboard finished notification`, error], { context });
    }
  }

  // i18n friendly title generation
  // Note: Must handle exceptions to avoid crashing the notification system
  private getTitle(params: TitleGenerationParams): string {
    const { context, packageName, notificationType, actionType, phase } = params;
    const messageTemplates = {
      // Data package
      activation: {
        started: `Starting activation of ${packageName} data package.`,
        finished: {
          success: `${packageName} data package has been activated.`,
          failure: `Activation of ${packageName} data package failed. Please try again later.`,
        },
      },
      deactivation: {
        started: `Starting deactivation of ${packageName} data package.`,
        finished: {
          success: `${packageName} data package has been deactivated.`,
          failure: `Deactivation of ${packageName} data package failed. Please try again later.`,
        },
      },
      updateDataPackage: {
        started: `Update of ${packageName} data package has started.`,
        finished: {
          success: `Update of ${packageName} data package has been completed.`,
          failure: `Update of ${packageName} data package has been failed.`,
        },
      },
      cleanupActivation: {
        started: `Starting cleanup of artifacts deployed during failed activation.`,
        finished: {
          success: `All artifacts deployed during failed activation have been removed.`,
          failure: `Cleanup failed. Try again later.`,
        },
      },
      retryDeactivation: {
        started: `Starting deactivation retry. Please do not perform other actions till retry finishes.`,
        finished: {
          success: `Data package has been deactivated.`,
          failure: `Deactivation retry failed. Try again later.`,
        },
      },
      // Intelligent application
      installation: {
        started: `Starting installation of ${packageName} intelligent application.`,
        finished: {
          success: `${packageName} intelligent application has been installed.`,
          failure: `Installation of ${packageName} intelligent application failed. Please try again later.`,
        },
      },
      uninstallation: {
        started: `Starting uninstallation of ${packageName} intelligent application.`,
        finished: {
          success: `${packageName} intelligent application has been uninstalled.`,
          failure: `Uninstallation of ${packageName} intelligent application failed. Please try again later.`,
        },
      },
      updateIntelligentApplication: {
        started: `Update of ${packageName} intelligent application has started.`,
        finished: {
          success: `Update of ${packageName} intelligent application has been completed.`,
          failure: `Update of ${packageName} intelligent application has been failed.`,
        },
      },
      cleanupInstallation: {
        started: `Starting cleanup of artifacts deployed during failed installation.`,
        finished: {
          success: `All artifacts deployed during failed installation have been removed.`,
          failure: `Cleanup failed. Try again later.`,
        },
      },
      retryUninstall: {
        started: `Starting uninstallation retry. Please do not perform other actions till retry finishes.`,
        finished: {
          success: `Intelligent application has been uninstalled.`,
          failure: `Uninstallation retry failed. Try again later.`,
        },
      },
    };

    try {
      if (phase === "started") {
        return messageTemplates[actionType][phase];
      } else {
        const status = notificationType === NotificationType.SUCCESS ? "success" : "failure";
        return messageTemplates[actionType][phase][status];
      }
    } catch (error) {
      const errorMessage = `Error generating notification title for action: ${actionType}, phase: ${phase}`;
      logError([errorMessage, error], { context });
      throw createNotificationFailureException(context, errorMessage);
    }
  }

  getCorrelationId(context: IRequestContext) {
    return `CorrelationId: ${context.correlationId}`;
  }
}

export const notificationsHelper = new ConcreteNotificationsHelper();
