/** @format */
import { StatusType } from "@sap/deepsea-types";
import { getLogger } from "../../logger";
import { InstallationStatusCode } from "../packageInstallation/packageInstallationTypes";
/**
 * Using regex to support local development on Windows
 * @param filename
 * @returns
 */
const BDC_COCKPIT_LOG_PREFIX = "BDCC";
export function getBdcLogger(filename: string) {
  const logger = getLogger(
    `${BDC_COCKPIT_LOG_PREFIX}/${filename
      .split(/[/\\]/)
      .pop()
      ?.replace(/\.[^/.]+$/, "")}`
  );

  const getLogFunctionForStatus = (
    status: InstallationStatusCode | Array<StatusType | undefined> | undefined
  ): typeof logger.logInfo | typeof logger.logError => {
    const ERROR_STATUSES = new Set<InstallationStatusCode | StatusType | undefined>([
      "FAILED",
      "ERROR",
      StatusType.Failed,
      StatusType.Aborted,
    ]);

    const shouldLogError = Array.isArray(status)
      ? status.some((s) => ERROR_STATUSES.has(s))
      : ERROR_STATUSES.has(status);

    return shouldLogError ? logger.logError : logger.logInfo;
  };

  return {
    ...logger,
    getLogFunctionForStatus,
  };
}
