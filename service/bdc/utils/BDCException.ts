/** @format */

import { BDCRequestContext } from "../../../shared/bdccockpit/Types";

export class BDCException extends Error {
  public static BDC_EXCEPTION_NAME = "BDCCockpitException";
  context: BDCRequestContext | null;
  type: BDCExceptionType;

  constructor(context: BDCRequestContext | null, type: BDCExceptionType, message: string) {
    super(message);
    this.name = BDCException.BDC_EXCEPTION_NAME;
    this.context = context;
    this.type = type;
  }
  public static addBDCCPrefixToMessage(message: string): string {
    return `[${BDCException.BDC_EXCEPTION_NAME}] ${message}`;
  }
}

export enum BDCExceptionType {
  InstallationNotFound = "InstallationNotFoundException",
  GeneralFailure = "GeneralFailure",
  MissingEntitlement = "MissingEntitlementException",
  InvalidPackageVersion = "InvalidPackageVersionException",
  InstallationFailure = "InstallationFailureException",
  CancellationFailure = "CancellationFailureException",
  UninstallationFailure = "UninstallationFailureException",
  FeatureFlagOff = "FeatureFlagOffException",
  NotificationFailure = "NotificationFailureException",
  ComponentInstallationRepeatRequired = "ComponentInstallationRepeatRequired",
}

export function createNotificationFailureException(
  context: BDCRequestContext | null,
  message: string = "Notification failure exception."
): BDCException {
  return new BDCException(context, BDCExceptionType.NotificationFailure, message);
}

export function createMissingEntitlementException(
  context: BDCRequestContext | null,
  message: string = "Missing entitlement for the requested operation."
): BDCException {
  return new BDCException(context, BDCExceptionType.MissingEntitlement, message);
}

export function createInvalidPackageVersionException(
  context: BDCRequestContext | null,
  message: string = "Invalid package version for update."
): BDCException {
  return new BDCException(context, BDCExceptionType.InvalidPackageVersion, message);
}

export function createInstallationFailureException(
  context: BDCRequestContext | null,
  message: string = "Installation failed after maximum retries."
): BDCException {
  return new BDCException(context, BDCExceptionType.InstallationFailure, message);
}

export function createUninstallationFailureException(
  context: BDCRequestContext | null,
  message: string = "Uninstallation error."
): BDCException {
  return new BDCException(context, BDCExceptionType.UninstallationFailure, message);
}

export function createFeatureFlagOffException(
  context: BDCRequestContext | null,
  featureFlag: string,
  message?: string
): BDCException {
  const finalMessage = message ? `${message}` : `Feature flag is off: ${featureFlag}`;
  return new BDCException(context, BDCExceptionType.FeatureFlagOff, finalMessage);
}

export function createRetryInstallationException(
  context: BDCRequestContext | null,
  installationId: string,
  message?: string
): BDCException {
  const finalMessage = message ? `${message}` : `Retry installation exception on installation ID: ${installationId}`;
  return new BDCException(context, BDCExceptionType.InstallationFailure, finalMessage);
}
