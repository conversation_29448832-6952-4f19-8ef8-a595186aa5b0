/** @format */

import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { IValidation, IValidationData } from "../types";

export class DataLakeDownsize implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    return data.newPayload.thresholdDataLakeStorage &&
      data.newPayload.thresholdDataLakeStorage < data.currentLicenses.thresholdDataLakeStorage
      ? [ScalingErrors.DataLakeDownsize]
      : [];
  }
}
