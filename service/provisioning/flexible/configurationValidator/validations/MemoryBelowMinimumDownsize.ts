/** @format */

import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { getMinimumMemoryForDownsize } from "../../helpers";
import { IValidation, IValidationData } from "../types";

export class MemoryBelowMinimumDownsize implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    if (data.newPayload.thresholdMemory < data.currentLicenses.thresholdMemory) {
      const minimumDownsizeMemory = await getMinimumMemoryForDownsize(data.context);
      if (data.newPayload.thresholdMemory < minimumDownsizeMemory) {
        return [ScalingErrors.MemoryBelowMinimumDownsize];
      }
    }
    return [];
  }
}
