/** @format */

import { MINIMUM_MEMORY_FOR_FEATURES } from "../../../../../shared/provisioning/ftc/defaultValues";
import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { IValidation, IValidationData } from "../types";

export class EcnMinimumLicense implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    return data.newPayload.thresholdECNBlock !== 0 && data.newPayload.thresholdMemory < MINIMUM_MEMORY_FOR_FEATURES
      ? [ScalingErrors.EcnMinimumLicense]
      : [];
  }
}
