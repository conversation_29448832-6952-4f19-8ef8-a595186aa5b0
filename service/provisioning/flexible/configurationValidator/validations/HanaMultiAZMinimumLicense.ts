/** @format */

import { MINIMUM_MEMORY_FOR_MULTI_AZ } from "../../../../../shared/provisioning/ftc/defaultValues";
import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { IValidation, IValidationData } from "../types";

export class HanaMultiAZMinimumLicense implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    return data.featureFlags.DWCO_FTC_HANA_MULTI_AZ &&
      data.newPayload.options?.isHanaMultiAZEnabled &&
      data.newPayload.thresholdMemory < MINIMUM_MEMORY_FOR_MULTI_AZ
      ? [ScalingErrors.HanaMultiAZMinimumLicense]
      : [];
  }
}
