/** @format */

import { CapacityUnitsCalculatorCore } from "@sap/dwc-flexible-tenant-sizing";
import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { isCpea } from "../../../helpers";
import { IValidation, IValidationData } from "../types";

export class Overusage implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    if (isCpea(data.tenantMetadata)) {
      return [];
    }
    const calculator = new CapacityUnitsCalculatorCore(data.hyperscaler, data.region, data.featureFlags);
    const estimatedCUs = calculator.getEstimationsForUi(data.newPayload).monthlyEstimate;
    return estimatedCUs > data.currentLicenses.thresholdDWCCU ? [ScalingWarnings.OverUsage] : [];
  }
}
