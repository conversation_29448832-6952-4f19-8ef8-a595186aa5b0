/** @format */

import { BwBridgeDownsizeUpsizeEnabled } from "./BwBridgeDownsizeUpsizeEnabled";
import { BwBridgeMinimumLicense } from "./BwBridgeMinimumLicense";
import { BwBridgeMinStorageConfig } from "./BwBridgeMinStorageConfig";
import { DataLakeConnectedToSpace } from "./DataLakeConnectedToSpace";
import { DataLakeDownsize } from "./DataLakeDownsize";
import { DataLakeMinimumLicense } from "./DataLakeMinimumLicense";
import { EcnMinimumLicense } from "./EcnMinimumLicense";
import { HanaMultiAZMinimumLicense } from "./HanaMultiAZMinimumLicense";
import { HanaScriptServerMinimumLicense } from "./HanaScriptServerMinimumLicense";
import { LargeSystemsDisabledRegion } from "./LargeSystemDisabledRegion";
import { LargeSystemsIncompleteConfiguration } from "./LargeSystemsIncompleteConfiguration";
import { LargeSystemsMinimumLicense } from "./LargeSystemsMinimumLicense";
import { MemoryBelowMinimumDownsize } from "./MemoryBelowMinimumDownsize";
import { MemoryBelowSpacesAllocation } from "./MemoryBelowSpacesAllocation";
import { Overusage } from "./Overusage";
import { SizeRatios } from "./SizeRatios";
import { StorageBelowMemoryRequirement } from "./StorageBelowMemoryRequirement";
import { StorageDownsize } from "./StorageDownsize";

export const Validations = {
  BwBridgeDownsizeUpsizeEnabled: new BwBridgeDownsizeUpsizeEnabled(),
  BwBridgeMinimumLicense: new BwBridgeMinimumLicense(),
  BwBridgeMinStorageConfig: new BwBridgeMinStorageConfig(),
  DataLakeConnectedToSpace: new DataLakeConnectedToSpace(),
  DataLakeDownsize: new DataLakeDownsize(),
  DataLakeMinimumLicense: new DataLakeMinimumLicense(),
  EcnMinimumLicense: new EcnMinimumLicense(),
  HanaMultiAZMinimumLicense: new HanaMultiAZMinimumLicense(),
  HanaScriptServerMinimumLicense: new HanaScriptServerMinimumLicense(),
  LargeSystemsMinimumLicense: new LargeSystemsMinimumLicense(),
  LargeSystemsIncompleteConfiguration: new LargeSystemsIncompleteConfiguration(),
  LargeSystemsDisabledRegion: new LargeSystemsDisabledRegion(),
  MemoryBelowMinimumDownsize: new MemoryBelowMinimumDownsize(),
  MemoryBelowSpacesAllocation: new MemoryBelowSpacesAllocation(),
  StorageBelowMemoryRequirement: new StorageBelowMemoryRequirement(),
  Overusage: new Overusage(),
  SizeRatios: new SizeRatios(),
  StorageDownsize: new StorageDownsize(),
};
