/** @format */

import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { IValidation, IValidationData } from "../types";

export class MemoryBelowSpacesAllocation implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    return data.newPayload.thresholdMemory <= data.spacesOverview.assignedMemory * Math.pow(10, -9)
      ? [ScalingErrors.MemoryBelowSpacesAllocation]
      : [];
  }
}
