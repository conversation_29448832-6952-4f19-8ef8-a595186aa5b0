/** @format */

import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { IValidation, IValidationData } from "../types";

export class StorageDownsize implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    return data.newPayload.thresholdStorage < data.currentLicenses.thresholdStorage
      ? [ScalingErrors.StorageDownsize]
      : [];
  }
}
