/** @format */

import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { IValidation, IValidationData } from "../types";

export class BwBridgeMinStorageConfig implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    return data.newPayload.thresholdBWBridge1 !== data.currentLicenses.thresholdBWBridge1 &&
      data.newPayload.thresholdBWBridge1 > data.newPayload.thresholdStorage
      ? [ScalingErrors.BwBridgeMinStorageConfig]
      : [];
  }
}
