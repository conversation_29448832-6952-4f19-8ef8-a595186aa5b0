/** @format */

import { calculateMinimalHanaStorage } from "../../../../../shared/provisioning/ftc/baseValues/customerHanaValidations";
import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { IValidation, IValidationData } from "../types";

export class StorageBelowMemoryRequirement implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    if (data.newPayload.thresholdMemory > data.currentLicenses.thresholdMemory) {
      if (
        data.newPayload.thresholdStorage <
        calculateMinimalHanaStorage(
          data.newPayload.thresholdMemory,
          data.newPayload.thresholdVCPU,
          data.hyperscaler,
          data.region
        )
      ) {
        return [ScalingErrors.StorageBelowMemoryRequirement];
      }
    }
    return [];
  }
}
