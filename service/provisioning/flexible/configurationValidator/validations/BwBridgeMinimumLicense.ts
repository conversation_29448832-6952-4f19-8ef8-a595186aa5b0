/** @format */

import { MINIMUM_MEMORY_FOR_FEATURES } from "../../../../../shared/provisioning/ftc/defaultValues";
import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { IValidation, IValidationData } from "../types";

export class BwBridgeMinimumLicense implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    return data.newPayload.thresholdMemory < MINIMUM_MEMORY_FOR_FEATURES && data.newPayload.thresholdBWBridge1 !== 0
      ? [ScalingErrors.BwBridgeMinimumLicense]
      : [];
  }
}
