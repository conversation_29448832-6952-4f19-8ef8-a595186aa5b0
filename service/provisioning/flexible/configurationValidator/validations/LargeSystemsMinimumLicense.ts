/** @format */

import { MINIMUM_MEMORY_FOR_FEATURES } from "../../../../../shared/provisioning/ftc/defaultValues";
import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { IValidation, IValidationData } from "../types";

export class LargeSystemsMinimumLicense implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    return data.featureFlags.DWCO_LARGE_SYSTEMS &&
      data.newPayload.thresholdMemory < MINIMUM_MEMORY_FOR_FEATURES &&
      data.newPayload.thresholdLargeSystemsStorage !== 0 &&
      data.newPayload.thresholdLargeSystemsCompute !== 0 &&
      (data.featureFlags.DWCO_LARGE_SYSTEMS_REQUESTS ? data.newPayload.thresholdLargeSystemsRequests !== 0 : true)
      ? [ScalingErrors.LargeSystemsMinimumLicense]
      : [];
  }
}
