/** @format */

import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { IValidation, IValidationData } from "../types";

export class LargeSystemsIncompleteConfiguration implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    const storage = data.newPayload.thresholdLargeSystemsStorage;
    const compute = data.newPayload.thresholdLargeSystemsCompute;

    return data.featureFlags.DWCO_LARGE_SYSTEMS &&
      ((storage !== 0 && compute === 0) || (storage === 0 && compute !== 0))
      ? [ScalingErrors.LargeSystemsIncompleteConfiguration]
      : [];
  }
}
