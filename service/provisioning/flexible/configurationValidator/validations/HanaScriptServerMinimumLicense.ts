/** @format */

import {
  MINIMUM_MEMORY_FOR_SCRIPT_SERVER,
  MINIMUM_VCPUS_FOR_SCRIPT_SERVER,
} from "../../../../../shared/provisioning/ftc/defaultValues";
import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { IValidation, IValidationData } from "../types";

export class HanaScriptServerMinimumLicense implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    return data.newPayload.options?.isHanaScriptServerEnabled &&
      (data.newPayload.thresholdMemory < MINIMUM_MEMORY_FOR_SCRIPT_SERVER ||
        data.newPayload.thresholdVCPU < MINIMUM_VCPUS_FOR_SCRIPT_SERVER)
      ? [ScalingErrors.HanaScriptServerMinimumLicense]
      : [];
  }
}
