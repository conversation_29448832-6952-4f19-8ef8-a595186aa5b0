/** @format */

import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { hasBridgeInstanceInBtp } from "../../../bwBridge/BwBridgeService";
import { IValidation, IValidationData } from "../types";

export class BwBridgeDownsizeUpsizeEnabled implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    return data.featureFlags.DWCO_FTC_UPSIZING_BW_BRIDGE &&
      data.newPayload.thresholdBWBridge1 < data.currentLicenses.thresholdBWBridge1 &&
      (await hasBridgeInstanceInBtp(data.context))
      ? [ScalingErrors.BwBridgeDownsizeUpsizeEnabled]
      : [];
  }
}
