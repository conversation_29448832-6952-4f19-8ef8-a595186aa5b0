/** @format */

import { MINIMUM_MEMORY_FOR_FEATURES } from "../../../../../shared/provisioning/ftc/defaultValues";
import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { IValidation, IValidationData } from "../types";

export class DataLakeMinimumLicense implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    return data.newPayload.thresholdDataLakeStorage !== 0 &&
      data.newPayload.thresholdMemory < MINIMUM_MEMORY_FOR_FEATURES &&
      (data.currentLicenses.thresholdDataLakeStorage === 0 ||
        data.newPayload.thresholdMemory < data.currentLicenses.thresholdMemory)
      ? [ScalingErrors.DataLakeMinimumLicense]
      : [];
  }
}
