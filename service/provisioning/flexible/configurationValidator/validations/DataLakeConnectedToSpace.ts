/** @format */

import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { CustomerHanaRuntimeData } from "../../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { IValidation, IValidationData } from "../types";

export class DataLakeConnectedToSpace implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    if (
      data.newPayload.thresholdDataLakeStorage === 0 &&
      data.newPayload.thresholdDataLakeStorage < data.currentLicenses.thresholdDataLakeStorage &&
      data.newPayload.options?.deleteDataLake
    ) {
      const customerHanaRuntime = new CustomerHanaRuntimeData(data.context);
      const isDataLakeConnectToSpace = await customerHanaRuntime.isDataLakeConnectedToSpace();
      return isDataLakeConnectToSpace ? [ScalingErrors.DataLakeConnectedToSpace] : [];
    }
    return [];
  }
}
