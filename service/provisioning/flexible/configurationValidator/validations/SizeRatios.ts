/** @format */

import { BaseValues } from "@sap/dwc-flexible-tenant-sizing";
import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { Hyperscaler } from "../../../../lib/node";
import { IRequestContext } from "../../../../repository/security/common/common";
import { getSizeRatio } from "../../../helpers";
import { IValidation, IValidationData } from "../types";

export class SizeRatios implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    const minimumValue = getMinimumValueForRatio(data.newPayload.thresholdMemory, data.hyperscaler);
    const maximumValue = await getMaximumValueForRatio(data.context, data.newPayload.thresholdMemory, data.hyperscaler);

    if (data.newPayload.thresholdStorage < minimumValue) {
      return [ScalingWarnings.BelowRatio];
    } else if (data.newPayload.thresholdStorage > maximumValue) {
      return [ScalingWarnings.AboveRatio];
    }
    return [];
  }
}

async function getMaximumValueForRatio(
  context: IRequestContext,
  memory: number,
  hyperscaler: Hyperscaler
): Promise<number> {
  const unformattedMaximumStorage = memory * (await getSizeRatio(context));
  const thresholdStorageBaseValues = BaseValues.get("thresholdStorage", hyperscaler);
  return Math.floor(unformattedMaximumStorage / thresholdStorageBaseValues.step) * thresholdStorageBaseValues.step;
}

function getMinimumValueForRatio(memory: number, hyperscaler: Hyperscaler): number {
  const halfMemory = memory / 2; // Minimum storage for a given compute needs to be higher than half the resulting memory
  const thresholdStorageBaseValues = BaseValues.get("thresholdStorage", hyperscaler);
  const minimumBlocks = Math.ceil(halfMemory / thresholdStorageBaseValues.step);
  const minimumStorage = minimumBlocks * thresholdStorageBaseValues.step;

  return minimumStorage;
}
