/** @format */

import { ScalingErrors, ScalingWarnings } from "../../../../../shared/provisioning/ftc/types";
import { getAvailableFeaturesForCurrentLandscape } from "../../../helpers";
import { IValidation, IValidationData } from "../types";

export class LargeSystemsDisabledRegion implements IValidation {
  async run(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    const hasObjectStore = (await getAvailableFeaturesForCurrentLandscape(data.context)).hasObjectStore;
    const storage = data.newPayload.thresholdLargeSystemsStorage;
    const compute = data.newPayload.thresholdLargeSystemsCompute;

    return data.featureFlags.DWCO_LARGE_SYSTEMS && storage !== 0 && compute !== 0 && !hasObjectStore
      ? [ScalingErrors.LargeSystemsDisabledRegion]
      : [];
  }
}
