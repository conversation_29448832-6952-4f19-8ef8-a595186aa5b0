/** @format */

import { promiseMap } from "@sap/dwc-promise-utils/dist/promise-map/promise-map";
import { ScalingErrors, ScalingWarnings } from "../../../../shared/provisioning/ftc/types";
import { IValidation, IValidationData } from "./types";
import { Validations } from "./validations";

export class ConfigurationValidator {
  private constructor(private validations: IValidation[]) {}

  static build(): ConfigurationValidator {
    return new ConfigurationValidator(Object.values(Validations));
  }

  public async validate(data: IValidationData): Promise<Array<ScalingErrors | ScalingWarnings>> {
    const errors: Array<Array<ScalingErrors | ScalingWarnings>> = [];
    await promiseMap(
      this.validations,
      async (v) => {
        errors.push(await v.run(data));
      },
      { concurrency: 5 }
    );
    return Array.from(new Set(errors.flat()));
  }
}
