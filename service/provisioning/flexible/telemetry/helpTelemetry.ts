/** @format */

import { ITenantInformation } from "@sap/dwc-tms-provider";
import {
  ITelemetryStatus,
  ITenantTelemetryInfo,
  TenantCategory,
  TenantClassification,
} from "../../../../shared/provisioning/ftc/types";

export async function getTenantTelemetryInformation(
  tenantMetadata: ITenantInformation,
  status: ITelemetryStatus
): Promise<ITenantTelemetryInfo> {
  const tenantDetails = {
    tenantId: tenantMetadata.uuid,
    tenantClassification: tenantMetadata.classification!,
    requestOrigin: getRequestOriginFromTenantClassification(tenantMetadata.classification),
    requestStatus: status,
  };

  return tenantDetails;
}

export function getRequestOriginFromTenantClassification(classification?: string): TenantCategory {
  if (classification === TenantClassification.DWCCPEA) {
    return TenantCategory.CIS;
  } else if (classification === TenantClassification.DWCBDC || classification === TenantClassification.BDCC) {
    return TenantCategory.S4M;
  } else {
    return TenantCategory.SPC;
  }
}
