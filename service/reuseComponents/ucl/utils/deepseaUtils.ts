/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { getLogger } from "@sap/dwc-logger";
import Status from "http-status-codes";
import { DeepseaServiceUclFormationClient } from "../../../connections/ucl/deepseaServiceUclFormationClient";
import { IUclRequestBody } from "../../../ucl/types";

const logger = getLogger("TMapS-deepseaUtils");
const { logInfo, logDebug, logError } = logger;

export async function notifyDeepsea(context: IRequestContext, uclRequestBody: IUclRequestBody) {
  if (await context.isFeatureFlagActive("DWCO_BDC")) {
    logDebug(`notifyDeepsea - uclRequestBody.assignedTenant: ${JSON.stringify(uclRequestBody?.assignedTenant)}`, {
      context,
    });
    const uclFormationClient = new DeepseaServiceUclFormationClient(context);
    logInfo(
      `notifyDeepsea - calling DeepseaServiceUclFormationClient.sendFormationRefresh for formationId: ${uclRequestBody?.context?.uclFormationId}, formationName: ${uclRequestBody?.context?.uclFormationName}`,
      { context }
    );
    const response = await uclFormationClient.sendFormationRefresh(uclRequestBody);
    if (response.status !== Status.CREATED) {
      const msg = `notifyDeepsea error, sendFormationRefresh failed, status code <${
        response.status
      }>, response: <${JSON.stringify(response.body)}>`;
      logError(msg, { context });
      throw new Error(`[TMapS-BDC-OperationsHandler] ${msg}`);
    }
    return response;
  }
}
