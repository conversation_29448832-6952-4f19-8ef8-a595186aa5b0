/** @format */

// FILEOWNER: [Provisioning, Spaces]
/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 */

import { getLogger } from "@sap/dwc-logger";
import { promiseMap } from "@sap/dwc-promise-utils";
import { escapeDoubleQuotes } from "@sap/prom-hana-client";
import * as fs from "fs";
import path from "path";
import { WorkloadType } from "../../../../shared/api/SpaceAPI";
import { SpaceTypeExtended } from "../../../../shared/spaces/types";
import { FeatureFlagProvider } from "../../../featureflags/FeatureFlagProvider";
import { DbClient } from "../../../lib/DbClient";
import { IRequestContext } from "../../../repository/security/common/common";
import { getSpaces } from "../../../repository/spaces";
import { getJWT } from "../../credentials";
import { CustomerHana, SpacePropertyKeys, SpacePropertySections } from "../../spaces/src";
import { deployTenantTables } from "../../systemTablesDeployment/src";
import * as Constants from "./Constants";
import * as DbConnections from "./DbConnections";
import * as HanaSSOSetup from "./HanaSSOSetup";
import { updateFinalVersionInDynatrace } from "./TelemetryOnboarding";
import * as UserActivation from "./UserActivation";
import * as generateUsername from "./UserCredentialsGenerators";
import { bringYourOwnHanaDefaultUsergroup, isBringYourOwnHanaUser } from "./bringYourOwnHana/deployBringYourOwnHana";
import * as auditReader from "./dbObjects/auditReader/statements";
import * as backupOwner from "./dbObjects/backupOwner/statements";
import * as dbAdmin from "./dbObjects/dbAdmin/statements";
import * as globalOwner from "./dbObjects/globalOwner/statements";
import * as tenantOwner from "./dbObjects/tenantOwner/statements";
import * as userOwner from "./dbObjects/userOwner/statements";
import * as helper from "./helper";
import { ensurePasswordChangeIsNotNeeded } from "./rotateSystemPassword";
import { IDeployTenantObjectsOptions, ProvStep, Role } from "./types";

const { logInfo, logWarning, logError } = getLogger("Provisioning");

// The version of deployed objects:
// * Major indicates an incompatible change that needs to be handled separately
// * Minor version change indicates a compatible change such as a fix/enhancement

export const objVersion = "0.769";

// The version of the new spaces that will be created with "CREATE_SPACE" procedure

export const spaceVersion = "175";

const concurrencyValue = 15;

/* eslint-disable arrow-body-style */
export function deployTenantObjects(
  context: IRequestContext,
  dbAdminDbConfig: IDbConfig,
  options: IDeployTenantObjectsOptions = {}
) {
  logInfo("Object deployment started", { context });

  const placeholders = { ...Constants.placeholders, spaceVersion, objVersion };
  if (isBringYourOwnHanaUser(dbAdminDbConfig.user)) {
    placeholders.dbAdmin = dbAdminDbConfig.user;
    placeholders.defaultUsergroup = bringYourOwnHanaDefaultUsergroup;
  }

  // Credentials for created users
  const userPwd: Record<Role, string> = {
    dbAdmin: dbAdminDbConfig.password || "",
    userOwner: "",
    userManager: "",
    tenantOwner: "",
    tenantManager: "",
    auditReader: "",
    globalOwner: "",
    globalIna: "",
    globalTaskFramework: "",
    globalPacemaker: "",
    globalEcnAdvisor: "",
    auditReplicator: "",
    backupOwner: "",
  };

  function getUserCredentials(opts: {
    template: IDbConfig;
    user: string;
    password: string;
    currentSchema?: string;
  }): IDbConfig {
    const creds: IDbConfig = {
      ...opts.template,
      user: opts.user,
      password: opts.password,
      currentSchema: opts.currentSchema,
    };
    return creds;
  }

  // Available connections
  const dbConn: Partial<Record<Role, DbClient>> = {};

  const heartbeat = { checkpoint: new Date().getTime() };

  async function createDbConnection({
    userRole,
    useJWT = true,
  }: {
    userRole: Role;
    useJWT?: boolean;
  }): Promise<DbClient> {
    if (dbConn[userRole] != null) {
      // This avoids possible connection leaks
      return dbConn[userRole]!;
    }

    const credentials = {
      template: dbAdminDbConfig,
      user: placeholders[userRole],
      password: userPwd[userRole],
      currentSchema: placeholders[userRole] || undefined,
    };

    if (useJWT) {
      credentials.user = "";
      credentials.password = await getJWT({
        context,
        spaceId: placeholders.globalSpace,
        spaceRole: userRole,
        audience: [dbAdminDbConfig.host.split(".")[0]],
      });
    } else {
      credentials.currentSchema = undefined;
    }

    const connCredentials = getUserCredentials(credentials);

    dbConn[userRole] = await DbConnections.createDbConnection(context, connCredentials);
    return dbConn[userRole]!;
  }

  async function createDbAdminConnection() {
    await createDbConnection({ userRole: "dbAdmin", useJWT: false });

    // This avoids possible 414: ERR_SQL_ALTER_PASSWORD_NEEDED error
    await ensurePasswordChangeIsNotNeeded(
      context,
      { ...dbAdminDbConfig, user: placeholders.dbAdmin, password: userPwd.dbAdmin },
      dbConn.dbAdmin!
    );
  }

  function execStatement(userRole: Role, statement: string, params?: any[]): Promise<any> {
    return DbConnections.execStatement(context, dbConn[userRole]!, userRole, statement, params);
  }

  function execProcedure(
    userRole: Role,
    schemaName: string,
    procName: string,
    params: any,
    logParams = false
  ): Promise<any> {
    return DbConnections.execProcedure(context, dbConn[userRole]!, userRole, schemaName, procName, params, logParams);
  }

  function execStepGroup(userRole: Role, steps: ProvStep[]) {
    return DbConnections.execStepGroup(
      context,
      placeholders,
      dbConn[userRole]!,
      userRole,
      steps,
      dbConn.tenantOwner,
      heartbeat
    );
  }

  function createUsergroup(userRole: Role, usergroupName: string) {
    return UserActivation.createUsergroup(context, dbConn[userRole]!, userRole, usergroupName);
  }

  function createRole(roleOwner: Role, roleSchemaName: string | null, roleName: string) {
    return UserActivation.createRole(context, dbConn[roleOwner]!, roleOwner, roleSchemaName, roleName);
  }

  function deployCsn(userRole: Role, deployTables: string[]) {
    return deployTenantTables(
      deployTables.map((tbl) => path.resolve(__dirname, "dbObjects", userRole, tbl)),
      placeholders[userRole],
      { ...dbAdminDbConfig, user: placeholders[userRole], password: userPwd[userRole] },
      context
    );
  }

  async function deactivateUser(userRole: Role) {
    const username = placeholders[userRole];
    await UserActivation.deactivateUser(dbConn.dbAdmin!, username);
    logWarning(`${username} has been deactivated.`, { context });
  }

  async function createTechnicalUser(userRole: Role, userName?: string, userNamePrefix?: string) {
    // Check if user exists in SecureStore
    const creds = await execProcedure(
      "userOwner",
      placeholders.userOwner,
      "GET_USER_CREDENTIALS",
      { IN_USER_SPACE: placeholders.globalSpace, IN_USER_ROLE: userRole },
      true
    ).then((res) => res.OUT_CREDENTIALS);
    return await doCreateTechUser(creds, userRole, userName, userNamePrefix);
  }

  async function doCreateTechUser(
    credJSON: string,
    userRole: Role,
    userName?: string,
    userNamePrefix?: string
  ): Promise<any> {
    const credentials = credJSON ? JSON.parse(credJSON) : undefined;
    if (!credentials) {
      // No entry found - initial deployment
      logInfo(`create ${userRole} user`, { context });
      await execProcedure(
        "userOwner",
        placeholders.userOwner,
        "CREATE_TECHNICAL_USER",
        {
          IN_USER_SPACE: placeholders.globalSpace,
          IN_USER_ROLE: userRole,
          IN_PASSWORD_LENGTH: 32,
          IN_USERNAME_PREFIX: userNamePrefix ?? "",
          IN_USERNAME: userName ?? "",
        },
        false
      );
      return await updateTechUserCredentials(userRole);
    } else {
      // User already exists, get credentials and ensure that user is enabled
      // Assign user to global usergroup if not already assigned
      logInfo(`${userRole} entry found in securestore`, { context });
      placeholders[userRole] = credentials.username;
      userPwd[userRole] = credentials.password;
      await execStatement("userOwner", `ALTER USER ${placeholders[userRole]} ENABLE PASSWORD`);
      await execStatement("userOwner", `ALTER USER ${placeholders[userRole]} ACTIVATE USER NOW`);
      await execStatement("userOwner", `ALTER USER ${placeholders[userRole]} RESET CONNECT ATTEMPTS`);

      logWarning(`User ${placeholders[userRole]} reactivated.`, { context });
      return credentials;
    }
  }

  async function updateTechUserCredentials(userRole: Role): Promise<any> {
    // Get credentials to create a connection and deploy objects
    const credentials = await execProcedure(
      "userOwner",
      placeholders.userOwner,
      "GET_USER_CREDENTIALS",
      { IN_USER_SPACE: placeholders.globalSpace, IN_USER_ROLE: userRole },
      true
    ).then((cred) => {
      if (!cred) {
        return {};
      }
      return JSON.parse(cred.OUT_CREDENTIALS);
    });
    if (!credentials.password) {
      logError(`Password update failed for user role ${userRole}`, { context });
    } else {
      placeholders[userRole] = credentials.username ?? placeholders[userRole];
      userPwd[userRole] = credentials.password;
      logInfo(`Password for user ${placeholders[userRole]} successfully stored in HANA secure store`, { context });
    }
    return credentials;
  }

  async function createSupportAndAnalysisRoles() {
    // eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled
    await Promise.all([
      createRole("dbAdmin", null, placeholders.systemSupportRoleRead),
      createRole("dbAdmin", null, placeholders.systemSupportRoleEmergency),
      createRole("dbAdmin", null, placeholders.systemAnalysisRole),
      createRole("dbAdmin", null, placeholders.customerUserGroupOperator),
    ]);

    await execStepGroup("dbAdmin", dbAdmin.grantPrivilegesToSupportAndAnalysisRoles);
  }

  async function createGlobalRoles() {
    // eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled
    await Promise.all([
      // - Global roles for accessing/deploying/open SQL schema users
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::access"), // MDS access privileges
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::deploy"), // MDS deploy privileges
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::spaceOwner"), // Re-usable objects for spaceOwners
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::spaceManager"), // Re-usable objects for spaceManager
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::customerSchemaAccess"), // Open SQL schemas / consumers privileges
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::scriptServerAccess"), // Role collection for Script Server
      // - Space data/objects support roles
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dwcSpaceDataRead"), // Read DWC spaces data
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dwcSpaceDataWrite"), // DML privileges on space schema (customer) objects
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dwcSpaceMonitoringRead"), // Monitoring privileges
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dwcSpaceTecDataWrite"), // DML privileges on TEC schema objects
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dwcSpaceTecDac"), // DML privileges on TEC schema DAC objects
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dwcSpaceMonitoringReadWithGrant"), // Monitoring privileges
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dwcSpaceObjectsModify"), // Selected DDL privileges on spaces schemas
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dwcSpaceTecObjectsModify"), // Selected DDL privileges on TEC schemas
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dwcSpaceTecObjectsSelect"), // Select info objects privileges on TEC schemas
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dwcSpaceSacObjects"), // SQL Analytic Privileges on Sac objects
      // - DWC metadata support roles
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dwcMetadataRead"), // Read DWC metadata tables/views
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dwcMetadataWrite"), // Read/write DWC metadata tables/views
      // - DWC remote sources support roles
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dwcRemoteSources"), // Privileges to create virtual tables & ALTER
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dwcRemoteSourcesExtended"), // Privileges to create virtual tables, ALTER & DROP
      // - DWC MDS support roles
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::supportMdsAccess"), // Read privileges to MDS moonlight and MDS cube
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::supportMdsModify"), // Change privileges to MDS moonlight and MDS cube
      // customer usergroup
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::customerUserGroupOperator"),
      // - Audit log relevant roles
      createRole("tenantOwner", placeholders.auditReader, "$$global$$::dppAuditRead"), // Access DPP related audit logs
      createRole("tenantOwner", placeholders.auditReader, "$$global$$::analysisAuditRead"), // Analysis users related audit logs
      createRole("tenantOwner", placeholders.auditReader, "$$global$$::supportAuditRead"), // Select on audit schema for support users
      // - Global roles for SAC users
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::sac"),
      // - Global roles for DIS users
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::dis"),
      // - Global role for Wrangling users
      createRole("tenantOwner", placeholders.tenantOwner, "$$global$$::wrg"),
    ]);

    await execStepGroup("tenantOwner", tenantOwner.grantPrivilegesToRoles);
    await execStepGroup("tenantOwner", tenantOwner.grantRolesToUsers);
  }

  async function prepareUserOwner() {
    // Activate or create userOwner user - Currently the user is in DEFAULT usergroup
    const res = await execStatement(
      "dbAdmin",
      'SELECT COUNT(*) AS "RESULT" FROM "PUBLIC"."USERS" WHERE "USER_NAME" = ?',
      [placeholders.userOwner]
    );
    const userOwnerExists = +res[0].RESULT;

    const pwd = await generateUsername.generatePassword(dbConn.dbAdmin!, 64, placeholders.defaultUsergroup);
    userPwd.userOwner = pwd;

    if (userOwnerExists) {
      logInfo("userOwner detected - redeploy db objects", { context });
      await execStatement(
        "dbAdmin",
        `ALTER USER ${placeholders.userOwner} PASSWORD "${userPwd.userOwner}" NO FORCE_FIRST_PASSWORD_CHANGE`
      );
      await execStatement("dbAdmin", `ALTER USER ${placeholders.userOwner} ENABLE PASSWORD`);
      await execStatement("dbAdmin", `ALTER USER ${placeholders.userOwner} ACTIVATE USER NOW`);
      await execStatement("dbAdmin", `ALTER USER ${placeholders.userOwner} RESET CONNECT ATTEMPTS`);

      logWarning(`User owner ${placeholders.userOwner} already exists.`, { context });
    } else {
      // userOwner does not exist, create user with a random password in DEFAULT usergroup (DWC_GLOBAL_USERS does not exist)
      await execStatement(
        "dbAdmin",
        `CREATE USER ${placeholders.userOwner} PASSWORD "${userPwd.userOwner}" NO FORCE_FIRST_PASSWORD_CHANGE SET USERGROUP ${placeholders.defaultUsergroup}`
      );
      await execStatement("dbAdmin", `ALTER USER ${placeholders.userOwner} DISABLE PASSWORD LIFETIME`);

      logWarning(`User owner ${placeholders.userOwner} created.`, { context });
    }

    await execStepGroup("dbAdmin", dbAdmin.grantPrivilegesToUserOwner);
  }

  async function moveUserToUsergroup(user: string, targetUsergroup: string) {
    const res = await execStatement("dbAdmin", 'SELECT "USERGROUP_NAME" FROM "PUBLIC"."USERS" WHERE "USER_NAME" = ?', [
      user,
    ]);
    if (res[0].USERGROUP_NAME && res[0].USERGROUP_NAME !== targetUsergroup) {
      logInfo(`Move userOwner to "${targetUsergroup}" usergroup`, { context });
      await execStatement(
        "dbAdmin",
        `ALTER USER ${placeholders.userOwner} SET USERGROUP ${escapeDoubleQuotes(targetUsergroup)}`
      );
    }
  }

  async function createUsergroups() {
    // eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled
    await Promise.all([
      createUsergroup("userOwner", placeholders.globalUsergroup),
      createUsergroup("userOwner", placeholders.spaceUsergroup),
      createUsergroup("userOwner", placeholders.agentUsergroup),
      createUsergroup("userOwner", placeholders.supportUsergroup),
      createUsergroup("userOwner", placeholders.analysisUsergroup),
    ]);
  }

  async function setUserOwnerCredentials() {
    // Ensure that SecureStore is in sync for userOwner:
    // - during initial provisioning there is no entry
    // - if userManager not provided then DBADMIN user will reset the password
    try {
      await execProcedure(
        "userOwner",
        placeholders.userOwner,
        "RESET_PASSWORD_TECHNICAL_USER",
        {
          IN_USER_SPACE: placeholders.globalSpace,
          IN_USER_ROLE: "userOwner",
          IN_PASSWORD_LENGTH: 64,
        },
        true
      );
    } catch (err) {
      logError(["Reset password for userOwner failed", err], { context });
    }
    await updateTechUserCredentials("userOwner");
  }

  async function createWorkloadMappings() {
    const mappings = {
      tenantManager: placeholders.globalUiWorkloadClass,
      tenantOwner: placeholders.globalWorkloadClassBackend,
      userOwner: placeholders.globalWorkloadClassBackend,
      userManager: placeholders.globalWorkloadClassBackend,
      auditReader: placeholders.globalWorkloadClassBackend,
      globalOwner: placeholders.globalWorkloadClassBackend,
      globalTaskFramework: placeholders.globalWorkloadClassBackend,
      globalPacemaker: placeholders.globalWorkloadClassBackend,
      globalIna: placeholders.globalWorkloadClassBackend,
      auditReplicator: placeholders.globalWorkloadClassBackend,
      globalEcnAdvisor: placeholders.globalWorkloadClassBackend,
    };

    const currentState = await execStatement(
      "tenantOwner",
      `SELECT "U"."USER_NAME", "UM"."WORKLOAD_CLASS_NAME", "UM"."WORKLOAD_MAPPING_NAME" FROM "PUBLIC"."USERS" AS "U"
      LEFT JOIN "PUBLIC"."WORKLOAD_MAPPINGS" AS "UM" ON "U"."USER_NAME" = "UM"."USER_NAME"
      WHERE "U"."USERGROUP_NAME" = ?`,
      [placeholders.globalUsergroup]
    );

    for (const key in mappings) {
      const role = key as keyof typeof mappings;
      const username = placeholders[role];
      const desiredWorkloadClass = mappings[role];
      const user = currentState.find((i: { USER_NAME: any }) => i.USER_NAME === username);

      if (user?.WORKLOAD_CLASS_NAME === null) {
        await execStatement(
          "tenantOwner",
          `CREATE WORKLOAD MAPPING "SAP_DWC_WCM_${placeholders.globalSpace}_${username}" WORKLOAD CLASS "${desiredWorkloadClass}" SET 'USER NAME' = '${username}'`
        );
      } else if (user?.WORKLOAD_CLASS_NAME !== null && user?.WORKLOAD_CLASS_NAME !== desiredWorkloadClass) {
        await execStatement("tenantOwner", `DROP WORKLOAD MAPPING "${user.WORKLOAD_MAPPING_NAME}"`);
        await execStatement(
          "tenantOwner",
          `CREATE WORKLOAD MAPPING "SAP_DWC_WCM_${placeholders.globalSpace}_${username}" WORKLOAD CLASS "${desiredWorkloadClass}" SET 'USER NAME' = '${username}'`
        );
      }
    }

    // TODO Remove this migration code after some time
    // map existing custom database user groups to SAP_DWC_$$global$$::CUST_UG_USERS
    await execStatement(
      "tenantOwner",
      `SELECT USERGROUP_NAME FROM ${placeholders.tenantOwner}.DWC_USERGROUPS WHERE USERGROUP_TYPE = 'customer_owned' AND USERGROUP_NAME NOT IN (
        SELECT "USERGROUP_NAME" FROM "PUBLIC"."WORKLOAD_MAPPINGS" WHERE "WORKLOAD_CLASS_NAME" = ?)`,
      [placeholders.customUserGroupsWorkloadClass]
    ).then((res) =>
      promiseMap(
        res,
        async (u: any) => {
          await execStatement(
            "tenantOwner",
            `CREATE WORKLOAD MAPPING "SAP_DWC_WCM_${placeholders.customUserGroupsWorkloadClass}_${u.USERGROUP_NAME}" WORKLOAD CLASS "${placeholders.customUserGroupsWorkloadClass}" SET 'USERGROUP NAME' = '${u.USERGROUP_NAME}'`
          );
        },
        { concurrency: concurrencyValue }
      )
    );
  }

  async function updateAdmissionControlRejectionThreshold() {
    try {
      const currentState = await execStatement(
        "tenantOwner",
        `SELECT "VALUE", "KEY" FROM ${placeholders.tenantOwner}."SPACE_METADATA"
        WHERE "SPACE_ID" = ? AND "SECTION" = ? AND "KEY" = ?`,
        ["$$global$$", "_admissionControlSettings", "activateRejection"]
      );

      const activateRejection = Boolean(
        currentState.length
          ? currentState.find((i: { VALUE: string }) => i.VALUE.toLowerCase() === "true")?.VALUE
          : true // activate rejection threshold by default
      );

      await execStatement(
        "tenantOwner",
        `SELECT "VALUE" AS "WORKLOAD_CLASS_NAME" FROM ${placeholders.tenantOwner}."SPACE_METADATA"
          WHERE "SECTION" = '_workloadManagement' AND "KEY" LIKE 'workloadClassName%'
          AND "VALUE" NOT IN (?,?,?,?,?,?)
          AND "VALUE" IN (
            SELECT "WORKLOAD_CLASS_NAME" FROM "SYS"."WORKLOAD_CLASSES"
            WHERE admission_control_reject_cpu_threshold ${activateRejection ? "IS NULL" : "IS NOT NULL"}
        );`,
        [
          placeholders.globalWorkloadClassBackend,
          placeholders.supportUsersWorkloadClass,
          placeholders.analysisUsersWorkloadClass,
          placeholders.globalWorkloadClassInternal,
          placeholders.globalWorkloadClassCustomer,
          placeholders.agentUsersWorkloadClass,
        ]
      ).then((res) =>
        promiseMap(
          res,
          async (i: any) => {
            if (activateRejection) {
              logInfo(
                `Set rejection threshold to ${placeholders.admissionControlRejectionThreshold} for ${i.WORKLOAD_CLASS_NAME}`,
                { context }
              );
              await execStatement(
                "tenantOwner",
                `ALTER WORKLOAD CLASS "${i.WORKLOAD_CLASS_NAME}" SET 'ADMISSION CONTROL REJECT CPU THRESHOLD' = '${placeholders.admissionControlRejectionThreshold}'`
              );
            } else {
              logInfo(`Unset rejection threshold for ${i.WORKLOAD_CLASS_NAME}`, { context });
              await execStatement(
                "tenantOwner",
                `ALTER WORKLOAD CLASS "${i.WORKLOAD_CLASS_NAME}" UNSET 'ADMISSION CONTROL REJECT CPU THRESHOLD'`
              );
            }
          },
          { concurrency: concurrencyValue }
        )
      );
    } catch (err) {
      logError(["failed to update admission control rejection threshold", err], { context });
    }
  }

  async function updateWorkloadClassHierarchies(): Promise<void> {
    try {
      const currentState = await execStatement(
        "tenantOwner",
        `SELECT "VALUE", "KEY" FROM ${placeholders.tenantOwner}."SPACE_METADATA"
        WHERE "SPACE_ID" = ? AND "SECTION" = ? AND "KEY" = ?`,
        ["$$global$$", "_workloadManagement", "activateHierarchies"]
      );

      const activateHierarchies = Boolean(
        currentState.length
          ? currentState.find((i: { VALUE: string }) => i.VALUE.toLowerCase() === "true")?.VALUE
          : placeholders.activateHierarchies // default value
      );

      const workloadClasses = await execStatement(
        "tenantOwner",
        `SELECT "VALUE" as "name" FROM ${placeholders.tenantOwner}."SPACE_METADATA" AS "SM"
          INNER JOIN "PUBLIC"."WORKLOAD_CLASSES" AS "WC" ON "WC"."WORKLOAD_CLASS_NAME" = "SM"."VALUE"
          WHERE "SECTION" = '_workloadManagement' AND "KEY" like 'workloadClassName%'
          AND "WC"."WORKLOAD_CLASS_NAME" NOT IN (?, ?, ?)
          AND "WC"."PARENT_WORKLOAD_CLASS_NAME" ${activateHierarchies ? "=" : "<>"} '';`,
        [
          placeholders.globalWorkloadClassInternal,
          placeholders.globalWorkloadClassCustomer,
          placeholders.analysisUsersWorkloadClass,
        ]
      );

      for (const workloadClass of workloadClasses) {
        switch (workloadClass.name) {
          case placeholders.globalWorkloadClassBackend:
          case placeholders.globalUiWorkloadClass:
          case placeholders.supportUsersWorkloadClass:
            await updateWorkloadClassParent(
              activateHierarchies ? "SET" : "UNSET",
              workloadClass,
              placeholders.globalWorkloadClassInternal,
              activateHierarchies
            );
            break;
          case placeholders.agentUsersWorkloadClass:
          case placeholders.customUserGroupsWorkloadClass:
          case placeholders.hdiUserGroupWorkloadClass:
          default:
            await updateWorkloadClassParent(
              activateHierarchies ? "SET" : "UNSET",
              workloadClass,
              placeholders.globalWorkloadClassCustomer,
              activateHierarchies
            );
            await updateWorkloadClass(
              workloadClass.name,
              "ADMISSION CONTROL QUEUE CPU THRESHOLD",
              activateHierarchies
                ? placeholders.admissionControlQueueingThreshold
                : placeholders.admissionControlOldQueueingThreshold
            );
            break;
        }
      }
      await validateWorkloadClasses(activateHierarchies);
    } catch (error) {
      logError(["failed to update workload class hierarchies", error], { context });
    }
  }

  async function validateWorkloadClasses(activateHierarchies: boolean) {
    try {
      const workloadClasses = await execStatement(
        "tenantOwner",
        `SELECT "WT"."VALUE" as "workloadType", "SM"."VALUE" as "name", "WC"."PARENT_WORKLOAD_CLASS_NAME" as "parent" , "WC"."ADMISSION_CONTROL_QUEUE_CPU_THRESHOLD" as "queueingThreshold", "WC"."TOTAL_STATEMENT_THREAD_LIMIT" as "totalThreadLimitValue", "WC"."TOTAL_STATEMENT_THREAD_LIMIT_UNIT" as "totalThreadLimitUnit"
         FROM ${placeholders.tenantOwner}."SPACE_METADATA" AS "SM"
         LEFT JOIN ${placeholders.tenantOwner}."SPACE_METADATA" AS "WT" ON "WT"."SPACE_ID" = "SM"."SPACE_ID" AND "WT"."KEY" = 'workloadType'
         INNER JOIN "PUBLIC"."WORKLOAD_CLASSES" AS "WC" ON "WC"."WORKLOAD_CLASS_NAME" = "SM"."VALUE"
         WHERE "SM"."SECTION" = '_workloadManagement' AND "SM"."KEY" like 'workloadClassName%';`,
        []
      );

      for (const workloadClass of workloadClasses) {
        // Fix wrong queueing threshold for spaces after space migration step v49,v50,v51,v52,v53
        if (
          activateHierarchies &&
          !workloadClass.name.includes("$$global$$") &&
          workloadClass.parent === placeholders.globalWorkloadClassCustomer &&
          Number(workloadClass.queueingThreshold) !== Number(placeholders.admissionControlQueueingThreshold)
        ) {
          await updateWorkloadClass(
            workloadClass.name,
            "ADMISSION CONTROL QUEUE CPU THRESHOLD",
            placeholders.admissionControlQueueingThreshold
          );
        }

        // Fix any incorrect workload class unit after workload class hierarchy activation / deactivation
        if (
          [
            placeholders.agentUsersWorkloadClass,
            placeholders.customUserGroupsWorkloadClass,
            placeholders.hdiUserGroupWorkloadClass,
            placeholders.supportUsersWorkloadClass,
          ].includes(workloadClass.name) &&
          workloadClass.totalThreadLimitUnit === "Counter"
        ) {
          await updateWorkloadClass(
            workloadClass.name,
            "TOTAL STATEMENT THREAD LIMIT",
            `${workloadClass.totalThreadLimitValue}%`
          );
        }

        // Fix any incorrect TOTAL_STATEMENT_THREAD_LIMIT for spaces workload class
        if (
          activateHierarchies &&
          !workloadClass.name.includes("$$global$$") &&
          workloadClass.parent === placeholders.globalWorkloadClassCustomer &&
          ((workloadClass.workloadType === WorkloadType.DEFAULT &&
            workloadClass.totalThreadLimitValue !==
              Number(placeholders.defaultTotalStatementThreadLimit.replace("%", ""))) ||
            workloadClass.totalThreadLimitValue === null)
        ) {
          await updateWorkloadClass(
            workloadClass.name,
            "TOTAL STATEMENT THREAD LIMIT",
            placeholders.defaultTotalStatementThreadLimit
          );
        }
      }
    } catch (error) {
      logError(["failed to validate workload classes", error], { context });
    }
  }

  async function updateWorkloadClassParent(
    action: "SET" | "UNSET",
    workloadClass: { name: string },
    parent: string,
    activateHierarchies: boolean
  ): Promise<void> {
    try {
      logInfo(`${action} workload class parent to ${parent} for ${workloadClass.name}`, { context });

      const currentLimits = await execStatement(
        "tenantOwner",
        `SELECT "TOTAL_STATEMENT_THREAD_LIMIT" AS "threadLimitValue", "TOTAL_STATEMENT_THREAD_LIMIT_UNIT" AS "threadLimitUnit", "TOTAL_STATEMENT_MEMORY_LIMIT" AS "memoryLimitValue", "TOTAL_STATEMENT_MEMORY_LIMIT_UNIT" AS "memoryLimitUnit" FROM "PUBLIC"."WORKLOAD_CLASSES" WHERE "WORKLOAD_CLASS_NAME" = ?;`,
        [workloadClass.name]
      );

      logInfo(`Unset 'TOTAL STATEMENT THREAD LIMIT', 'TOTAL STATEMENT MEMORY LIMIT' for ${workloadClass.name}`, {
        context,
      });
      await execStatement(
        "tenantOwner",
        `ALTER WORKLOAD CLASS "${workloadClass.name}" UNSET 'TOTAL STATEMENT THREAD LIMIT', 'TOTAL STATEMENT MEMORY LIMIT';`
      );

      logInfo(`${action} parent for ${workloadClass.name}`, { context });
      await execStatement(
        "tenantOwner",
        `ALTER WORKLOAD CLASS "${workloadClass.name}" parent ${action === "SET" ? `"${parent}"` : null};`
      );

      if (currentLimits[0].threadLimitValue !== null) {
        const threadLimitUnit = currentLimits[0].threadLimitUnit;
        let threadLimitValue = currentLimits[0].threadLimitValue;
        // modify threadLimit only space workload classes
        if (!workloadClass.name.includes("$$global$$") && threadLimitUnit === "Percent") {
          if (activateHierarchies && threadLimitValue === 80) {
            threadLimitValue = `100%`;
          } else if (!activateHierarchies && threadLimitValue === 100) {
            threadLimitValue = `80%`;
          } else {
            threadLimitValue = `${threadLimitValue}%`;
          }
        } else if (workloadClass.name.includes("$$global$$") && threadLimitUnit === "Percent") {
          threadLimitValue = `${threadLimitValue}%`;
        }

        logInfo(`Set 'TOTAL STATEMENT THREAD LIMIT' value ${threadLimitValue} for ${workloadClass.name}`, {
          context,
        });
        await execStatement(
          "tenantOwner",
          `ALTER WORKLOAD CLASS "${workloadClass.name}" SET 'TOTAL STATEMENT THREAD LIMIT' = '${threadLimitValue}';`
        );
      }

      if (currentLimits[0].memoryLimitValue !== null) {
        let memoryLimitValue = currentLimits[0].memoryLimitValue;

        if (currentLimits[0].memoryLimitUnit === "Percent") {
          memoryLimitValue = `${memoryLimitValue}%`;
        }
        logInfo(`Set 'TOTAL STATEMENT MEMORY LIMIT' value ${memoryLimitValue} for ${workloadClass.name}`, {
          context,
        });
        await execStatement(
          "tenantOwner",
          `ALTER WORKLOAD CLASS "${workloadClass.name}" SET 'TOTAL STATEMENT MEMORY LIMIT' = '${memoryLimitValue}';`
        );
      }
    } catch (error) {
      logError([`failed to ${action} parent for ${workloadClass.name}`, error], { context });
    }
  }

  async function updateWorkloadClass(workloadClass: string, parameter: string, value: string): Promise<void> {
    try {
      logInfo(`Update workload class ${workloadClass} set ${parameter} to ${value}`, { context });
      await execStatement("tenantOwner", `ALTER WORKLOAD CLASS "${workloadClass}" SET '${parameter}' = '${value}'`);
    } catch (error) {
      logError([`failed to update workload class ${workloadClass}`, error], { context });
    }
  }

  async function updateWorkloadManagement(): Promise<void> {
    try {
      const result = await execStatement(
        "tenantOwner",
        `SELECT COUNT(*) AS "COUNT" FROM ${placeholders.tenantOwner}."DWC_WORKLOAD_CLASSES"`
      );
      if (result[0]?.COUNT === 0) {
        await execStatement(
          "tenantOwner",
          `UPSERT ${placeholders.tenantOwner}.DWC_WORKLOAD_CLASSES
            SELECT DISTINCT
            NULL AS "GROUP",
            "S"."SPACE_ID" AS "SPACE_ID",
            "C"."VALUE" AS "CONFIGURATION",
            'SAP_DWC_' || "S"."SPACE_ID" AS "WORKLOAD_CLASS_NAME"
            FROM ${placeholders.tenantOwner}."SPACE_METADATA" "S"
            INNER JOIN ${placeholders.tenantOwner}."SPACE_METADATA" "C" ON "C".SPACE_ID = "S"."SPACE_ID"
            AND "C"."SECTION" = '_workloadManagement' AND "C"."KEY" = 'workloadType'
            WHERE "S"."SPACE_ID" != '$$global$$'
            ORDER BY "S"."SPACE_ID";`
        );
      }
    } catch (error) {
      logError(["Failed to update Workload Management", error], { context });
    }
  }

  async function updateSpaceType(): Promise<void> {
    try {
      const result = await execStatement(
        "tenantOwner",
        `SELECT DISTINCT SPACE_ID as "spaceId"
        FROM ${placeholders.tenantOwner}."SPACE_METADATA"
        WHERE SPACE_ID NOT IN (SELECT SPACE_ID FROM ${placeholders.tenantOwner}."SPACE_METADATA" WHERE KEY = ?)
        AND SPACE_ID != ?;`,
        [SpacePropertyKeys.SpaceType, placeholders.globalSpace]
      );
      const spacesWithoutSpaceType = result.map((i: { spaceId: string }) => i.spaceId);

      if (spacesWithoutSpaceType.length) {
        logInfo(`Start update spaceType in SPACE_METADATA for ${spacesWithoutSpaceType.length} spaces`, { context });
        const repositorySpaces = await getSpaces(context, {
          details: ["id", "name", "spaceType"],
          inSpaceManagement: true,
        });
        const spacesToUpdate = repositorySpaces.filter((i) => spacesWithoutSpaceType.includes(i.name));
        for (const space of spacesToUpdate) {
          logInfo(`Set spaceType ${space.properties.spaceType} for ${space.name}`, { context });
          await execStatement(
            "tenantOwner",
            `INSERT INTO ${placeholders.tenantOwner}."SPACE_METADATA" ("SPACE_ID", "SECTION", "KEY", "VALUE")
            VALUES (?, ?, ?, ?);`,
            [
              space.name,
              SpacePropertySections.Metadata,
              SpacePropertyKeys.SpaceType,
              space?.properties?.spaceType ? space.properties.spaceType : SpaceTypeExtended.Default,
            ]
          );
        }
        logInfo(`Update spaceType in SPACE_METADATA finished`, { context });
      }
    } catch (error) {
      logError(["Failed to update spaceType in SPATE_METADATA", error], { context });
    }
  }

  /**
   * DS00-3217
   * Virtual Package creation
   * Will be used for LSA procedures
   * Temporary --> In Q4 will be replaced by HANA plugin and this code will be removed
   */
  async function createDeltaROVirtualPackage(): Promise<void> {
    if (
      !(await FeatureFlagProvider.isFeatureActive(context, "DWCO_LARGE_SYSTEMS")) ||
      (await FeatureFlagProvider.isFeatureActive(context, "DWCO_HANA_LAKEHOUSE_PLUGIN"))
    ) {
      return;
    }

    const jarFilePathQRC4 = path.resolve(__dirname, "./dbObjects/dbAdmin/lso/qrc4/DELTA_LCM_RO.jar");
    const virtualPackageName = "DELTA_RO_PACKAGE";
    // Convert .jar to BLOB
    const fileContent = fs.readFileSync(jarFilePathQRC4);

    // Drop if there's an existing virtual package
    try {
      await execStatement(
        "dbAdmin",
        `DO BEGIN
          DECLARE package_count INT;

          -- Check if the package exists in the _SYS_REMOTE_CONTROLLER schema
          SELECT COUNT(*) INTO package_count
          FROM SYS.VIRTUAL_PACKAGES
          WHERE PACKAGE_NAME = 'DELTA_RO_PACKAGE'
          AND SCHEMA_NAME = '_SYS_REMOTE_CONTROLLER';

          -- If the package exists, call the procedure to drop it
          IF :package_count > 0 THEN
              CALL SYS.VIRTUAL_PACKAGE_DROP('_SYS_REMOTE_CONTROLLER', 'DELTA_RO_PACKAGE', 'spark');
          END IF;
      END;`
      );
    } catch (error) {
      logError(`Failed to drop virtual package ${virtualPackageName}. Error: ${error}.`, { context });
    }

    try {
      // Pass the binary file content as a parameter
      await execStatement(
        "dbAdmin",
        `CALL "SYS"."VIRTUAL_PACKAGE_CREATE"('_SYS_REMOTE_CONTROLLER', 'DELTA_RO_PACKAGE', 'spark', ?);`,
        [fileContent]
      );
    } catch (error) {
      logError(`Failed to create virtual package ${virtualPackageName}. Error: ${error}.`, { context });
    }
  }

  /**
   * DS00-3217
   * LSA procedures and role creation
   * Temporary --> In Q4 will be replaced by HANA plugin and this code will be removed
   */
  async function createLSAProcedures(): Promise<void> {
    if (
      (await FeatureFlagProvider.isFeatureActive(context, "DWCO_LARGE_SYSTEMS")) &&
      !(await FeatureFlagProvider.isFeatureActive(context, "DWCO_HANA_LAKEHOUSE_PLUGIN"))
    ) {
      try {
        await execStepGroup("dbAdmin", dbAdmin.dropLSAVirtualProcedures);

        await execStepGroup("dbAdmin", dbAdmin.largeSystemsProceduresQrc4);
        await execStepGroup("dbAdmin", dbAdmin.grantExecuteToRemoteControllerTableOperationsQrc4);
      } catch (error) {
        logError(`An error occurred when trying to create LSA procedures. Error: ${error}.`, { context });
      }
    }
  }

  const provSteps: Array<() => Promise<any>> = [
    // DBADMIN ---------------------------------------------------------------------------------------------------------
    async () => await createDbAdminConnection(),
    async () => await execStepGroup("dbAdmin", dbAdmin.createAuditPolicies),
    async () => await execStepGroup("dbAdmin", dbAdmin.createODataAdapter),
    async () => await execStepGroup("dbAdmin", dbAdmin.configureSystemParams),
    async () => await execStepGroup("dbAdmin", dbAdmin.deployDbObjects),
    async () => await execStepGroup("dbAdmin", dbAdmin.scheduledJobs),
    async () => await createSupportAndAnalysisRoles(),
    async () => await prepareUserOwner(),

    // DWC_USER_OWNER --------------------------------------------------------------------------------------------------
    async () => await createDbConnection({ userRole: "userOwner", useJWT: false }),
    // When reprovisioning, temporarily move DWC_USER_OWNER to DEFAULT usergroup to allow usergroup ownership migration
    async () => await moveUserToUsergroup(placeholders.userOwner, placeholders.defaultUsergroup), // Can be removed after v0.332 is deployed for all customers
    async () => await createUsergroups(),
    async () => await execStepGroup("userOwner", userOwner.grantOperatorToDbAdmin),
    async () => await moveUserToUsergroup(placeholders.userOwner, placeholders.globalUsergroup),
    async () => await execStepGroup("userOwner", userOwner.deployDbObjects),
    async () => await setUserOwnerCredentials(),
    async () =>
      // eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled
      await Promise.all([
        createTechnicalUser("userManager", undefined, "DWC_RT_UM1_"),
        createTechnicalUser("tenantManager", undefined, "DWC_RT_TM_"),
        createTechnicalUser("globalTaskFramework", undefined, "DWC_RT_TSK_"),
        createTechnicalUser("globalPacemaker", undefined, "DWC_RT_PCM_"),
        createTechnicalUser("globalIna", undefined, "DWC_RT_INA_"),
        createTechnicalUser("auditReplicator", undefined, "DWC_RT_AR_"),
        createTechnicalUser("backupOwner", undefined, "DWC_RT_BO_"),
        createTechnicalUser("tenantOwner", placeholders.tenantOwner),
        createTechnicalUser("auditReader", placeholders.auditReader),
        createTechnicalUser("globalOwner", placeholders.globalOwner),
        createTechnicalUser("globalEcnAdvisor", undefined, "DWC_RT_ECN_"),
      ]),
    async () => {
      // Since JWT authentication is now compulsory, we delete deprecated spare userManager0 (if present)
      logInfo(`Trying to delete deprecated second User Manager (userManager0)...`, { context });
      await execProcedure(
        "userOwner",
        placeholders.userOwner,
        "DROP_TECHNICAL_USER",
        {
          IN_USER_SPACE: placeholders.globalSpace,
          IN_USER_ROLE: "userManager0",
        },
        true
      );
    },
    async () => await execStepGroup("userOwner", userOwner.grantPrivilegesToUsers),

    // DBADMIN----------------------------------------------------------------------------------------------------------
    async () => await execStepGroup("dbAdmin", dbAdmin.grantPrivilegesToUsers),

    // DWC_GLOBAL ------------------------------------------------------------------------------------------------------
    async () => await createDbConnection({ userRole: "tenantOwner", useJWT: false }),
    async () => await createDbConnection({ userRole: "globalOwner", useJWT: false }),
    async () => await deployCsn("globalOwner", globalOwner.csnObjects),
    async () => await execStepGroup("globalOwner", globalOwner.deployDbObjects),
    async () => await execStepGroup("globalOwner", globalOwner.taskFrameworkDBIndexes),
    async () => await execStepGroup("globalOwner", globalOwner.taskFrameworkConstraints),
    async () => await execStepGroup("globalOwner", globalOwner.taskFrameworkDBProcedures),
    async () => await execStepGroup("globalOwner", globalOwner.taskFrameworkSubstatusesMapping),
    async () => await execStepGroup("globalOwner", globalOwner.taskFrameworkSetIsSystemTask),
    async () => await execStepGroup("globalOwner", globalOwner.sparkTaskDetailsProcedures),
    async () => await execStepGroup("globalOwner", globalOwner.dsMeteringDBIndexes),
    async () => await execStepGroup("globalOwner", globalOwner.dsMeteringDbTriggers),
    async () => await execStepGroup("globalOwner", globalOwner.updateLocalTableStatisticsProcedures),
    async () => await execStepGroup("globalOwner", globalOwner.basicPrivileges),

    // DWC_TENANT_OWNER ------------------------------------------------------------------------------------------------
    async () => await execStepGroup("tenantOwner", tenantOwner.entityCleanUp),
    async () => await deployCsn("tenantOwner", tenantOwner.csnObjects),
    async () => await execStepGroup("tenantOwner", tenantOwner.deployDbObjects),
    async () => await createGlobalRoles(),
    async () => await execStepGroup("tenantOwner", tenantOwner.createWorkloadClasses),
    async () => await createWorkloadMappings(),
    async () => await updateAdmissionControlRejectionThreshold(),
    async () => await updateWorkloadClassHierarchies(),
    async () => await updateSpaceType(),
    async () => await updateWorkloadManagement(),
    async () => await createDeltaROVirtualPackage(),
    async () => await createLSAProcedures(),
    async () => await execStepGroup("tenantOwner", tenantOwner.enableExpensiveStatementTracing),

    // needs the tenantOwner db objects being deployed and all privileges being granted
    async () =>
      await HanaSSOSetup.setupJWTAuthStore(
        context,
        dbConn.dbAdmin!,
        placeholders.dbAdmin,
        placeholders.userOwner,
        placeholders.tenantOwner,
        dbAdminDbConfig.host,
        Constants.JWT_AUTH_PSE
      ),
    async () =>
      await execProcedure(
        "tenantOwner",
        placeholders.tenantOwner,
        "ENABLE_SPACE_USERS_JWT",
        {
          IN_SPACE_ID: placeholders.globalSpace,
          IN_ROLES: Constants.globalUserRolesForJWT.map((role) => ({ ROLE: role })),
        },
        true
      ),
    // Reset connection map for JWT authentication.
    async () => {
      for (const userRole of Constants.globalUserRolesForJWT) {
        if (dbConn[userRole]) {
          delete dbConn[userRole];
        }
        await createDbConnection({ userRole });
      }
    },
    // DWC_AUDIT_READER ------------------------------------------------------------------------------------------------
    async () => await execStepGroup("auditReader", auditReader.deployDbObjects),
    async () => await execStepGroup("auditReader", auditReader.grantPrivilegesToRolesAndUsers),

    // DWC_GLOBAL ------------------------------------------------------------------------------------------------------
    async () => await execStepGroup("globalOwner", globalOwner.resourceManagementDBObjects),
    async () => await execStepGroup("globalOwner", globalOwner.statementMonitoringDBObjects),
    async () => await execStepGroup("globalOwner", globalOwner.deploySparkTaskDetailsDBObjects),
    async () => await execStepGroup("globalOwner", globalOwner.TFExternalViewDBObjects),
    async () => await execStepGroup("globalOwner", globalOwner.etvDBProcedures),
    async () => await execStepGroup("globalOwner", globalOwner.ltfTaskProcedures),
    async () => await execStepGroup("globalOwner", globalOwner.globalSchemaPrivileges),

    // DBADMIN ---------------------------------------------------------------------------------------------------------
    async () => await execStepGroup("dbAdmin", dbAdmin.grantPrivilegesToGlobalRoles),
    async () => await helper.establishXsuaaTrust(context, dbConn.dbAdmin!, options),

    // DWC_TENANT_OWNER ------------------------------------------------------------------------------------------------
    async () => {
      if (await FeatureFlagProvider.isFeatureActive(context, "DWCO_CHECK_STORAGE_IMPROVEMENTS")) {
        logWarning(`enable CheckStorage procedure`, { context });
        await execStepGroup("tenantOwner", tenantOwner.enableCheckStorage);

        try {
          const customerHana = new CustomerHana(context, dbAdminDbConfig);
          await customerHana.updateTotalDiskSize(); // update TMS total disk size in SPACE_METADATA table
        } catch (error) {
          logError(["Failed to update total disk size in SPACE_METADATA table", error], { context });
        }
      } else {
        logWarning(`disable CheckStorage procedure`, { context });
        await execStepGroup("tenantOwner", tenantOwner.disableCheckStorage);
      }
    },
    async () => await execStepGroup("tenantOwner", tenantOwner.updateUpgradeInformationInMetadata),
    async () => await updateFinalVersionInDynatrace(context),

    // Backup owner ----------------------------------------------------------------------------------------------------
    async () => await deployCsn("backupOwner", backupOwner.csnObjects),
    async () => await execStepGroup("backupOwner", backupOwner.deployDbObjects),
    async () => await execStepGroup("backupOwner", backupOwner.grantPrivilegesToSupportRole),

    // DBADMIN ---------------------------------------------------------------------------------------------------------
    async () => {
      for (const role of Constants.globalUserRolesForJWT) {
        await execStatement("dbAdmin", `ALTER USER ${placeholders[role]} DISABLE PASSWORD`);
      }
    },
    async () =>
      // eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled
      await Promise.all([
        deactivateUser("userOwner"),
        deactivateUser("tenantOwner"),
        deactivateUser("auditReader"),
        deactivateUser("globalOwner"),
      ]),
  ];

  return provSteps
    .reduce((prev, cur) => prev.then(cur), Promise.resolve())
    .finally(() =>
      promiseMap(
        Object.keys(dbConn),
        async (c) => {
          await DbConnections.closeDbConnection(dbConn[c as Role]!);
        },
        { concurrency: concurrencyValue }
      )
    )
    .then(() => ({
      host: dbAdminDbConfig.host,
      port: +dbAdminDbConfig.port,
      username: placeholders.userManager,
      sslTrustStore: dbAdminDbConfig.sslTrustStore,
    }));
}
