-- FILEOWNER: [DataIntegration]
-- VIRTUAL PROCEDURE VERSION: [9]
-- Merge a Local Table with File Storage (LTF)
--
-- When you update this file and want to run the integration test,
-- then you have to restart the build tasks to get the latest version of the file loaded.
CREATE OR REPLACE VIRTUAL PROCEDURE "{{globalOwner}}"."LTF_MERGE_PYSPARK"(
    IN SCHEMA_NAME NVARCHAR(256),
    IN TABLE_NAME NVARCHAR(256),
    IN INBOUND_BUFFER_PATH NVARCHAR(1024),
    IN CDC_TIMESTAMP_COLUMN NVARCHAR(256),
    IN CDC_TYPE_COLUMN NVARCHAR(256),
    IN KEY_COLUMN_LIST NCLOB,
    IN FILES_FORMAT NVARCHAR(256) DEFAULT 'PARQUET',
    IN OVERWRITE_MODE BOOLEAN DEFAULT FALSE,
    IN ENABLE_LOOPED_PROCESSING BOOLEAN DEFAULT FALSE,
    OUT RESULT_JSON NCLOB
  )
SQL SECURITY INVOKER
LANGUAGE PYSPARK

AS
BEGIN

import time
import json
import http.client
import ssl
from delta import DeltaTable
from pyspark.sql.types import StructField, StringType, TimestampType

# Constants
MAX_MERGE_CHUNK_SIZE_GB = 1000
MAX_DELETE_BATCH_SIZE = 32
LOG_PREFIX_TEMPLATE = "[LTF_MERGE_PYSPARK][{}]"
FILES_FORMAT_PARQUET = ".parquet"
SOURCE_VIEW_NAME = "mergeSource"
SOURCE_VIEW_NAME_NO_DUPS = "mergeSourceNoDups"

def hdfsDeleteBatch(filesToDelete, hdlfsEndpoint, filecontainer, certfile, keyfile):
    request_url="/webhdfs/v1/?op=DELETE_BATCH"
    request_headers = {
        'x-sap-filecontainer': filecontainer,
        'Content-Type': 'application/json'
    }
    request_body = json.dumps({"files":[{ "path": path } for path in filesToDelete]})

    context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    context.load_cert_chain(certfile=certfile, keyfile=keyfile)

    connection = http.client.HTTPSConnection(hdlfsEndpoint, port=443, context=context)
    connection.request(method="POST", url=request_url, body=request_body, headers=request_headers)

    return connection.getresponse()

def hdfsParallelDelete(partitionData, hdlfsEndpoint, filecontainer, certfile, keyfile):
    filesToDelete = []
    filesNotDeleted = []

    for fileName in partitionData:
        filesToDelete.append(fileName)

        if (len(filesToDelete) >= MAX_DELETE_BATCH_SIZE):
            # delete batch call with 32 files
            response = hdfsDeleteBatch(filesToDelete, hdlfsEndpoint, filecontainer, certfile, keyfile)
            if (response.status != 200):
                filesNotDeleted += filesToDelete
            filesToDelete = []

    if (len(filesToDelete) > 0):
        # delete batch call with remaining files
        response = hdfsDeleteBatch(filesToDelete, hdlfsEndpoint, filecontainer, certfile, keyfile)
        if (response.status != 200):
            filesNotDeleted += filesToDelete

    return iter(filesNotDeleted)

def readInboundBuffer(pathFS, pathInboundBuffer, filesFormat, enableLoopedProcessing, maxChunkSizeGB=MAX_MERGE_CHUNK_SIZE_GB):
    try:
        logger.info(f"{log_prefix} Reading files in the inbound buffer ...")
        maxChunkSize = maxChunkSizeGB * 1024 * 1024 * 1024 # GB to Bytes

        currentChunkFiles = []
        currentChunkSize = 0 # in Bytes

        totalSize = 0 # in Bytes
        totalNumFiles = 0
        resultFileChunks= []

        fs = spark._jvm.org.apache.hadoop.fs.FileSystem.get(
            spark._jvm.java.net.URI(pathFS),
            spark._jsc.hadoopConfiguration()
        )
        listStatus = fs.listStatus(spark._jvm.org.apache.hadoop.fs.Path(pathInboundBuffer))

        for file in listStatus:
            fileName = file.getPath().getName()
            fileSize = file.getLen() # in Bytes

            if (not fileName.endswith(filesFormat)):
                continue

            # flush currentChunkFiles if adding the new file exceeds maxChunkSize
            if (currentChunkSize + fileSize > maxChunkSize and len(currentChunkFiles) > 0):
                resultFileChunks.append(currentChunkFiles)
                currentChunkFiles = []
                currentChunkSize = 0

                if (not enableLoopedProcessing):
                    logger.info(f"{log_prefix} Limiting processing to a single chunk as enableLoopedProcessing is disabled.")
                    break

            currentChunkFiles.append(pathInboundBuffer + "/" + fileName)
            currentChunkSize += fileSize

            totalSize += fileSize
            totalNumFiles += 1

        # append the last chunk if it is not empty
        if (len(currentChunkFiles) > 0):
            resultFileChunks.append(currentChunkFiles)

        if (len(resultFileChunks) == 0):
            logger.info(f"{log_prefix} No files to process. Exiting ...")
            return None

        logger.info(f"{log_prefix} Found {totalNumFiles} files with a total size of {totalSize} Bytes.")
        return resultFileChunks, totalNumFiles, totalSize

    except Exception as error:
        logger.error(f"{log_prefix} An error occurred while reading the inbound buffer! {str(error)}")
        return None
    
def createViewOnFiles(fileNames, filesFormat, pathFS, pathDeltaTable):
    logger.info(f"{log_prefix} Creating temporary view on the files to process ...")
    if (filesFormat == FILES_FORMAT_PARQUET):
        df = spark.read.parquet(*[pathFS + name for name in fileNames])
    else:
        dfSchema = DeltaTable.forPath(spark, pathDeltaTable).toDF().schema
        if (not OVERWRITE_MODE):
            dfSchema = dfSchema \
                .add(StructField(CDC_TYPE_COLUMN, StringType(), True)) \
                .add(StructField(CDC_TIMESTAMP_COLUMN, TimestampType(), True))
        df = spark.read \
            .option("header", True) \
            .option("escape", '"') \
            .option("nullValue", None) \
            .schema(dfSchema).csv([pathFS + name for name in fileNames])
    df.createOrReplaceTempView(SOURCE_VIEW_NAME)

def cleanUpInboundBuffer(fileNames, hdlfsEndpoint, filecontainer, certfile, keyfile):
    logger.info(f"{log_prefix} Cleaning up the inbound buffer and the temporary views ...")
    rdd = spark.sparkContext.parallelize(fileNames)
    result = rdd.mapPartitions(lambda partitionData: hdfsParallelDelete(partitionData, hdlfsEndpoint, filecontainer, certfile, keyfile)).collect()
    logger.info(f"{log_prefix} Executor results: {len(fileNames) - len(result)} of {len(fileNames)} files deleted.")

    if (len(result) > 0):
        logger.info(f"{log_prefix} Files not deleted: {result}")

    spark.sql(f"DROP VIEW IF EXISTS {SOURCE_VIEW_NAME}")
    if (not OVERWRITE_MODE):
        spark.sql(f"DROP VIEW IF EXISTS {SOURCE_VIEW_NAME_NO_DUPS}")

def mergeFilesIntoDeltaTable(fileNames):
    if (OVERWRITE_MODE):
        logger.info(f"{log_prefix} Overwriting the delta table ...")
        
        spark.sql(f"""
            INSERT OVERWRITE TABLE `{TABLE_NAME}` SELECT * FROM {SOURCE_VIEW_NAME}
        """)
    else:
        keyColumns = json.loads(KEY_COLUMN_LIST)
        keyColumnsAsString = ', '.join([f"`{key}`" for key in keyColumns])
        keyColumnsMergeCondition = 'ON ' + ' AND '.join([f"TGT.`{key}` = SRC.`{key}`" for key in keyColumns])
    
        logger.info(f"{log_prefix} Eliminating duplicates from the files to process ...")
        queryCreateView = f"""
            CREATE OR REPLACE TEMPORARY VIEW {SOURCE_VIEW_NAME_NO_DUPS} AS
            (
              SELECT *
              FROM
              (
                SELECT *, ROW_NUMBER() OVER (PARTITION BY {keyColumnsAsString} ORDER BY {CDC_TIMESTAMP_COLUMN} DESC) AS rn
                FROM {SOURCE_VIEW_NAME}
              )
              WHERE rn = 1
            )
        """
        spark.sql(queryCreateView)

        logger.info(f"{log_prefix} Merging the files into the delta table ...")
        queryMerge = f"""
            MERGE INTO `{TABLE_NAME}` AS TGT
            USING {SOURCE_VIEW_NAME_NO_DUPS} AS SRC
            {keyColumnsMergeCondition}
            WHEN MATCHED AND (SRC.`{CDC_TYPE_COLUMN}` = 'I' OR SRC.`{CDC_TYPE_COLUMN}` = 'U') THEN UPDATE SET *
            WHEN MATCHED AND SRC.`{CDC_TYPE_COLUMN}` = 'D' THEN DELETE
            WHEN NOT MATCHED AND SRC.`{CDC_TYPE_COLUMN}` <> 'D' THEN INSERT *
        """
        spark.sql(queryMerge)

def updateResult(result, status=None, message=None, numChunksProcessed=None, totalNumFiles=None, totalSize=None, endTime=None):
    if status is not None:
        result["status"] = status
    if message is not None:
        result["message"] = message
    if numChunksProcessed is not None:
        result["numChunksProcessed"] = numChunksProcessed
    if totalNumFiles is not None:
        result["totalNumFiles"] = totalNumFiles
    if totalSize is not None:
        result["totalSize"] = totalSize
    if endTime is not None:
        result["endTime"] = endTime
        result["executionTime"] = endTime - result["startTime"]

try:
    # SETUP
    spark.sparkContext.setLogLevel("INFO")
    spark.conf.set('spark.sql.caseSensitive', True)
    log_prefix = LOG_PREFIX_TEMPLATE.format(TABLE_NAME)
    logger = spark._jvm.org.apache.logging.log4j.LogManager.getLogger(__name__)
    logger.info(f"{log_prefix} Starting Setup ...")

    spark.sql("use sap_catalog")
    spark.sql(f"use SCHEMA `{SCHEMA_NAME}`")

    pathFS = spark._jsc.hadoopConfiguration().get("fs.defaultFS")
    hdlfsEndpoint = pathFS[len("hdlfs://"):]
    filecontainer = hdlfsEndpoint.partition(".")[0]
    certfile = spark._jsc.hadoopConfiguration().get("fs.hdlfs.ssl.certfile")
    keyfile = spark._jsc.hadoopConfiguration().get("fs.hdlfs.ssl.keyfile")
    pathInboundBuffer = f"{INBOUND_BUFFER_PATH}"
    pathDeltaTable = f"/dsp/{SCHEMA_NAME}/objects/{TABLE_NAME}"
    filesFormat = f".{FILES_FORMAT}".lower()

    result = {
        "status": "success",
        "message": "No files to process.",
        "startTime": time.time()
    }
    currentChunkIndex = 0

    # EXECUTE
    data = readInboundBuffer(pathFS, pathInboundBuffer, filesFormat, ENABLE_LOOPED_PROCESSING)
    if (data):
        fileChunks, totalNumFiles, totalSize = data
        numChunks = len(fileChunks)

        for fileNames in fileChunks:
            currentChunkIndex += 1
            logger.info(f"{log_prefix} Processing chunk {currentChunkIndex} of {numChunks} ...")
            createViewOnFiles(fileNames, filesFormat, pathFS, pathDeltaTable)
            mergeFilesIntoDeltaTable(fileNames)
            cleanUpInboundBuffer(fileNames, hdlfsEndpoint, filecontainer, certfile, keyfile)
        
        updateResult(
            result,
            message="Merge operation completed successfully.",
            numChunksProcessed=numChunks,
            totalNumFiles=totalNumFiles,
            totalSize=totalSize
        )

except Exception as error:
    errorMessage = f"An error occurred in LTF_MERGE_PYSPARK! {str(error)}"
    logger.error(f"{log_prefix} {errorMessage}")
    
    updateResult(
        result,
        status="error",
        message=errorMessage,
        numChunksProcessed=currentChunkIndex
    )

finally:
    updateResult(
        result,
        endTime=time.time()
    )

    RESULT_JSON = json.dumps(result) 
    logger.info(f"{log_prefix} Delta merge task finished. Runtime: {result['executionTime']} seconds.")
    logger.info(f"{log_prefix} RESULT_JSON: {RESULT_JSON}")

END;
