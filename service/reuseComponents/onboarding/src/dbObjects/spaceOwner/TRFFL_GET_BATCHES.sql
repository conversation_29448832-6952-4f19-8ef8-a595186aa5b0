-- FILEOWNER: [DataIntegration]
-- Get batches for a transformation flow
--
-- When you update this file and want to run the integration test,
-- then you have to restart the build tasks to get the latest version of the file loaded.
CREATE OR REPLACE PROCEDURE "{{spc_tec_internal}}"."TRFFL_GET_BATCHES"(
    IN TASK_LOG_ID                 BIGINT,
    IN SCHEMA_NAME                 NVARCHAR(128),
    IN VIEW_TRANSFORM_NAME         NVARCHAR(256),
    IN BATCH_PROCESSING_DESIGNTIME NCLOB,
    IN HIGH_WATERMARK_FILTER       TIMESTAMP,
    IN GET_PLANVIZ                 BOOLEAN,
    IN GET_EXPLAIN_PLAN            BOOLEAN,
    IN HINT_CLAUSE                 NCLOB,
    IN FF_DWCO_TRF_BLOCK_DYN_DAC   BOOLEAN,
    OUT BATCH_TABLE TABLE(
      BATCH_NUMBER    BIGINT,
      BATCH_ID        NVARCHAR(256),
      WHER<PERSON>_CONDITION NCLOB,
      LOW_VALUE       NVARCHAR(256),
      HIGH_VALUE      NVARCHAR(256)),
    OUT BATCH_COLUMN               NVARCHAR(256),
    OUT BATCH_COUNT                BIGINT
  )
  LANGUAGE SQLSCRIPT
  SQL SECURITY INVOKER
  DEFAULT SCHEMA "{{spc_tec_internal}}" AS
  BEGIN
    DECLARE
      SQL_STATEMENT,
      SQL_STATEMENT_SELECT_DISTINCT_VALUES,
      CURRENT_DISTINCT_VALUE,
      PREVIOUS_DISTINCT_VALUE,
      FIRST_DISTINCT_VALUE NCLOB;

    DECLARE
      LOG_MESSAGE_TEXT NVARCHAR(5000);

    DECLARE
      TASK_METRICS_LABEL_TABLE TABLE(
        "LABEL_NAME"  NVARCHAR(256),
        "LABEL_VALUE" NVARCHAR(256));

    DECLARE
      BATCHES_ONLY_INITIAL_LOAD,
      CREATE_OTHERS_NULL_BATCH,
      REMOTE_ACCESS BOOLEAN;

    DECLARE
      TABLE_NAME_DISTINCT_VALUES_BATCH_COLUMN AUTO = '#tableDistinctValuesBatchColumn';

    DECLARE
      BATCH_SIZE,
      CNT_DISTINCT_VALUES_BATCH_COLUMN,
      CURRENT_ROW_NUM,
      RECORD_COUNT BIGINT;

    DECLARE
      VAR_BATCH_TABLE TABLE(
        BATCH_NUMBER    BIGINT,
        BATCH_ID        NVARCHAR(256),
        WHERE_CONDITION NCLOB,
        LOW_VALUE       NVARCHAR(256),
        HIGH_VALUE      NVARCHAR(256));

    SELECT
      JSON_VALUE(:BATCH_PROCESSING_DESIGNTIME,'$.column'),
      JSON_VALUE(:BATCH_PROCESSING_DESIGNTIME,'$.batchSize'),
      TO_BOOLEAN(JSON_VALUE(:BATCH_PROCESSING_DESIGNTIME,'$.onlyInitialLoad'))
    INTO
      BATCH_COLUMN,
      BATCH_SIZE,
      BATCHES_ONLY_INITIAL_LOAD
    FROM
      SYS.DUMMY;

    -- ensure that the task metrics table is empty
    DELETE FROM :TASK_METRICS_LABEL_TABLE;

    CALL "{{globalOwner}}"."_TASK_METRICS_PROCEDURE"(
      TASK_LOG_ID => :TASK_LOG_ID,
      NAME => '{{MetricName.BATCH_COLUMN}}',
      VALUE => :BATCH_COLUMN,
      LABELS => :TASK_METRICS_LABEL_TABLE
    );

    CALL "{{globalOwner}}"."_TASK_METRICS_PROCEDURE"(
      TASK_LOG_ID => :TASK_LOG_ID,
      NAME => '{{MetricName.BATCH_SIZE}}',
      VALUE => TO_NVARCHAR(:BATCH_SIZE),
      LABELS => :TASK_METRICS_LABEL_TABLE
    );

    VALID_BATCH_COLUMN_TABLE = SELECT
        "COLUMN_NAME"
      FROM
        "SYS"."VIEW_COLUMNS"
      WHERE
        "SCHEMA_NAME" = :SCHEMA_NAME
        AND "VIEW_NAME" = :VIEW_TRANSFORM_NAME
        AND "COLUMN_NAME" = :BATCH_COLUMN
        AND "DATA_TYPE_NAME" IN ({{allowedHanaDataTypesForBatchColumnInList}});

    IF IS_EMPTY(:VALID_BATCH_COLUMN_TABLE) THEN
      SIGNAL SQL_ERROR_CODE {{TransformationFlowTaskSqlErrorCodes.INVALID_BATCH_COLUMN}}
        SET MESSAGE_TEXT = 'batch column "' || :BATCH_COLUMN || '" is not valid.';
    END IF;

    IF :BATCH_SIZE IS NULL OR :BATCH_SIZE < 1 THEN
      SIGNAL SQL_ERROR_CODE {{TransformationFlowTaskSqlErrorCodes.INVALID_BATCH_SIZE}}
        SET MESSAGE_TEXT = 'batch size "' || :BATCH_SIZE || '" is not valid.';
    END IF;

    IF :BATCHES_ONLY_INITIAL_LOAD = TRUE AND NOT :HIGH_WATERMARK_FILTER IS NULL THEN
      -- no batch processing for delta load
      RETURN;
    END IF;

    IF :GET_EXPLAIN_PLAN = TRUE THEN
      CALL "{{globalOwner}}"."_TASK_LOG_MESSAGES_PROCEDURE"(
        TASK_LOG_ID => :TASK_LOG_ID,
        SEVERITY=> '{{Severity.INFO}}',
        TEXT_=> 'Generating an Explain Plan prevents transformation flows from being processed in batches.',
        MESSAGE_BUNDLE_ID=>'{{TransformationFlowTaskLog.MSG_BUNDLE_ID}}',
        MESSAGE_BUNDLE_KEY=>'{{TransformationFlowTaskLog.EXECUTION_GET_EXPLAIN_PLAN_NO_BATCHES}}'
      );

      RETURN;
    END IF;

    --use dynamic SQL to create local temporary table with HANA data type of batch column
    --to have correct ordering
    --
    -- CREATE TABLE doesn't support hint clause.
    -- Therefore the table is created first with no data
    -- and afterwards the INSERT is done with the hint clause.
    --
    -- NULLS LAST to start with ROW_NUM 1 for non-NULL values

    SQL_STATEMENT_SELECT_DISTINCT_VALUES := 'SELECT
      "' || escape_double_quotes(:BATCH_COLUMN) || '",
      ROW_NUMBER() OVER (ORDER BY "' || escape_double_quotes(:BATCH_COLUMN) || '" ASC NULLS LAST)
        AS "ROW_NUM"
      FROM (SELECT DISTINCT "' || escape_double_quotes(:BATCH_COLUMN) || '" FROM "' ||
        escape_double_quotes(:SCHEMA_NAME) || '"."' || escape_double_quotes(:VIEW_TRANSFORM_NAME) || '")';

    SQL_STATEMENT = 'CREATE LOCAL TEMPORARY TABLE "'
      || escape_double_quotes(:TABLE_NAME_DISTINCT_VALUES_BATCH_COLUMN)
      || '" AS (' || :SQL_STATEMENT_SELECT_DISTINCT_VALUES || ') WITH NO DATA';

    @AnalyzerSuppress('SAP.USE_OF_DYNAMIC_SQL.PERFORMANCE')
    @AnalyzerSuppress('SAP.UNCHECKED_SQL_INJECTION_SAFETY.SECURITY')
    EXEC(SQL_STATEMENT);

    SQL_STATEMENT = 'INSERT INTO "'
      || escape_double_quotes(:TABLE_NAME_DISTINCT_VALUES_BATCH_COLUMN)
      || '" ("' || escape_double_quotes(:BATCH_COLUMN) || '", "ROW_NUM") ('
      || :SQL_STATEMENT_SELECT_DISTINCT_VALUES
      || ')' || :HINT_CLAUSE;

    -- findings for OUT variables :RECORD_COUNT and :REMOTE_ACCESS need to be suppressed
    @AnalyzerSuppress('SAP.UNNECESSARY_VARIABLE.CONSISTENCY')
    @AnalyzerSuppress('SAP.UNUSED_VARIABLE_VALUE.CONSISTENCY')
    CALL "{{spc_tec_internal}}"."TRFFL_READ_AND_WRITE"(
      SQL_STATEMENT_BEFORE_WHERE_CLAUSE => :SQL_STATEMENT,
      SQL_STATEMENT_WHERE_CONDITION     => '',
      SQL_STATEMENT_AFTER_WHERE_CLAUSE  => '',
      STEP                              => '{{TransformationFlowSteps.readDistinctValuesOfBatchColumn}}',
      TASK_LOG_ID                       => :TASK_LOG_ID,
      GET_PLANVIZ                       => :GET_PLANVIZ,
      GET_EXPLAIN_PLAN                  => :GET_EXPLAIN_PLAN,
      FF_DWCO_TRF_BLOCK_DYN_DAC         => :FF_DWCO_TRF_BLOCK_DYN_DAC,
      RECORD_COUNT                      => :RECORD_COUNT,
      REMOTE_ACCESS                     => :REMOTE_ACCESS
    );

    SQL_STATEMENT = 'DELETE FROM "' || escape_double_quotes(:TABLE_NAME_DISTINCT_VALUES_BATCH_COLUMN) ||
      '" WHERE "' || escape_double_quotes(:BATCH_COLUMN) || '" IS NULL';

    @AnalyzerSuppress('SAP.USE_OF_DYNAMIC_SQL.PERFORMANCE')
    @AnalyzerSuppress('SAP.ROW_COUNT_AFTER_DYNAMIC_SQL.BEHAVIOR')
    EXEC(SQL_STATEMENT);

    CREATE_OTHERS_NULL_BATCH := TO_BOOLEAN(::ROWCOUNT); --returns TRUE, if one NULL row has been deleted

    SQL_STATEMENT = 'SELECT COUNT(*) FROM "'
      || escape_double_quotes(:TABLE_NAME_DISTINCT_VALUES_BATCH_COLUMN) || '"';

    @AnalyzerSuppress('SAP.USE_OF_DYNAMIC_SQL.PERFORMANCE')
    EXEC(SQL_STATEMENT) INTO CNT_DISTINCT_VALUES_BATCH_COLUMN;

    -- ensure that the task metrics table is empty
    DELETE FROM :TASK_METRICS_LABEL_TABLE;

    CALL "{{globalOwner}}"."_TASK_METRICS_PROCEDURE"(
      TASK_LOG_ID => :TASK_LOG_ID,
      NAME => '{{MetricName.BATCH_COL_NUM_OF_DISTINCT_VAL_NOT_NULL}}',
      VALUE => TO_NVARCHAR(:CNT_DISTINCT_VALUES_BATCH_COLUMN),
      LABELS => :TASK_METRICS_LABEL_TABLE
    );

    IF :CNT_DISTINCT_VALUES_BATCH_COLUMN <= :BATCH_SIZE THEN
      LOG_MESSAGE_TEXT := 'Batch column has ' || :CNT_DISTINCT_VALUES_BATCH_COLUMN
        || ' distinct values (excluding NULL). This is less than or equal to the batch size ' || :BATCH_SIZE
        || '. Consequently the transformation flow is not processed in batches.';

      CALL "{{globalOwner}}"."_TASK_LOG_MESSAGES_PROCEDURE"(
        TASK_LOG_ID => :TASK_LOG_ID,
        SEVERITY=> '{{Severity.INFO}}',
        TEXT_=> :LOG_MESSAGE_TEXT,
        MESSAGE_BUNDLE_ID=>'{{TransformationFlowTaskLog.MSG_BUNDLE_ID}}',
        MESSAGE_BUNDLE_KEY=>'{{TransformationFlowTaskLog.EXECUTION_CNT_DISTINCT_VALUES_LE_BATCH_SIZE}}',
        PARAMETER_VALUES=>'["'||:CNT_DISTINCT_VALUES_BATCH_COLUMN||'","'||:BATCH_SIZE||'"]'
      );

      BATCH_COLUMN := NULL;
      BATCH_SIZE := NULL;
    END IF;

    BATCH_COUNT := 0;

    CURRENT_ROW_NUM := 1;

    WHILE (CURRENT_ROW_NUM <= CNT_DISTINCT_VALUES_BATCH_COLUMN) DO
      @AnalyzerSuppress('SAP.USE_OF_DYNAMIC_SQL.PERFORMANCE')
      EXEC('SELECT TO_NCLOB("' || escape_double_quotes(:BATCH_COLUMN) || '")
        FROM "' || escape_double_quotes(:TABLE_NAME_DISTINCT_VALUES_BATCH_COLUMN) || '"
        WHERE "ROW_NUM" = ?') INTO CURRENT_DISTINCT_VALUE  USING CURRENT_ROW_NUM;

      IF FIRST_DISTINCT_VALUE IS NULL THEN
        FIRST_DISTINCT_VALUE := :CURRENT_DISTINCT_VALUE;
      ELSE
        BATCH_COUNT := BATCH_COUNT + 1;

        @AnalyzerSuppress('SAP.DML_STATEMENTS_IN_LOOPS.PERFORMANCE')
        INSERT INTO :VAR_BATCH_TABLE
            ("BATCH_NUMBER", "BATCH_ID", "WHERE_CONDITION", "LOW_VALUE", "HIGH_VALUE")
          VALUES
            ( :BATCH_COUNT,
              :BATCH_COUNT,
              '"' || escape_double_quotes(:BATCH_COLUMN) || '" >= ''' || escape_single_quotes(:PREVIOUS_DISTINCT_VALUE) || '''
                AND "' || escape_double_quotes(:BATCH_COLUMN) || '" < ''' || escape_single_quotes(:CURRENT_DISTINCT_VALUE) || '''',
              :PREVIOUS_DISTINCT_VALUE,
              :CURRENT_DISTINCT_VALUE );
      END IF;

      PREVIOUS_DISTINCT_VALUE = :CURRENT_DISTINCT_VALUE;
      CURRENT_ROW_NUM := :CURRENT_ROW_NUM + BATCH_SIZE;
    END WHILE;

    --OTHERS NOT NULL BATCH
    BATCH_COUNT := BATCH_COUNT + 1;

    INSERT INTO :VAR_BATCH_TABLE
        ("BATCH_NUMBER", "BATCH_ID", "WHERE_CONDITION", "LOW_VALUE", "HIGH_VALUE")
      VALUES
        ( :BATCH_COUNT,
          '{{TransformationFlowSpecialBatchIds.othersNotNull}}',
          -- < FIRST_DISTINCT_VALUE is not needed, but just done to be on the safe side
          '"' || escape_double_quotes(:BATCH_COLUMN) || '" < ''' || escape_single_quotes(:FIRST_DISTINCT_VALUE) || '''
            OR "' || escape_double_quotes(:BATCH_COLUMN) || '" >= ''' || escape_single_quotes(:CURRENT_DISTINCT_VALUE) || '''',
          :FIRST_DISTINCT_VALUE,
          :CURRENT_DISTINCT_VALUE );

    IF :CREATE_OTHERS_NULL_BATCH = TRUE THEN
      --OTHERS NULL BATCH
      BATCH_COUNT := BATCH_COUNT + 1;

      INSERT INTO :VAR_BATCH_TABLE
          ("BATCH_NUMBER", "BATCH_ID", "WHERE_CONDITION", "LOW_VALUE", "HIGH_VALUE")
        VALUES
          ( :BATCH_COUNT,
            '{{TransformationFlowSpecialBatchIds.othersNull}}',
            '"' || escape_double_quotes(:BATCH_COLUMN) || '" IS NULL',
            NULL,
            NULL );
    END IF;

    -- ensure that the task metrics table is empty
    DELETE FROM :TASK_METRICS_LABEL_TABLE;

    CALL "{{globalOwner}}"."_TASK_METRICS_PROCEDURE"(
      TASK_LOG_ID => :TASK_LOG_ID,
      NAME => '{{MetricName.NUMBER_OF_BATCHES}}',
      VALUE => TO_NVARCHAR(:BATCH_COUNT),
      LABELS => :TASK_METRICS_LABEL_TABLE
    );

    BATCH_TABLE = SELECT
        "BATCH_NUMBER",
        "BATCH_ID",
        "WHERE_CONDITION",
        "LOW_VALUE",
        "HIGH_VALUE"
      FROM :VAR_BATCH_TABLE;

    SQL_STATEMENT = 'DROP TABLE "' || escape_double_quotes(:TABLE_NAME_DISTINCT_VALUES_BATCH_COLUMN) || '"';

    @AnalyzerSuppress('SAP.USE_OF_DYNAMIC_SQL.PERFORMANCE')
    EXEC(SQL_STATEMENT);
  END;
