/** @format */

// FILEOWNER: [Provisioning, Spaces]
/** Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved. */
import { Capability } from "../../../../../../shared/spaces/SpaceCapabilities";
import { SpaceType } from "../../../../../../shared/spaces/types";
import { replicationFlowRunDetailsTableName } from "../../../../../replicationflow/common/runDetailUtil";
import { viewAnalyzerResultsTableName } from "../../../../../routes/advisor/resultsTableName";
import { TransformationFlowBatchHandler } from "../../../../../routes/partitioning/api/TransformationFlowBatchHandler";
import { VALIDATION_TASK_LOG_TABLE as validationTaskLogTableName } from "../../../../../routes/remoteobject/types";
import { LocalTableTaskLogMessageBundleModel } from "../../../../../table/task/types";
import { ContentSemantic, Severity } from "../../../../../task/logger/models";
import { LabelName, MetricName } from "../../../../../task/logger/models/ITaskMetrics";
import { SQLScriptProcedureTaskLog } from "../../../../../task/nonRepository/sqlScriptProcedure/task/SQLScriptProcedureConstants";
import { ApplicationStatus } from "../../../../../task/orchestrator/services/activesession/ActiveSessionChecker";
import { TransformationFlowLoadType } from "../../../../../transformationFlow/commonUtil";
import { ViewTransformParameterSemantic } from "../../../../../transformationFlow/run/model";
import {
  TransformationFlowExecutionMode,
  TransformationFlowSpecialBatchIds,
  TransformationFlowSteps,
  TransformationFlowTaskLog,
  TransformationFlowTaskSqlErrorCodes,
  trfflExecuteRtDataTableName,
  trfflExecuteRtSettingsTableName,
} from "../../../../../transformationFlow/task/constants";
import { deltaProviderSubscriberTableName } from "../../../../deltaCapture/src/deltaProviderSubscriberManager";
import { ProvStep } from "../../types";

export const taskFrameworkSpaceProcedures: ProvStep[] = [
  {
    statementPath: "./TASK_DELETE_REFERENCING_TASK_LOG_ID.sql",
    stepSpecificPlaceholders: {
      viewAnalyzerResultsTableName,
      replicationFlowRunDetailsTableName,
      trfflExecuteRtDataTableName,
      validationTaskLogTableName,
    },
  },
  {
    statementPath: "./TASK_DELETE_REFERENCING_SCHEDULE_ID.sql",
    stepSpecificPlaceholders: { localTableVariants: "LOCAL_TABLE_VARIANTS" },
  },
  {
    statementPath: "./OPEN_SCHEMA_PROCEDURE_EXECUTE.sql",
    stepSpecificPlaceholders: {
      ...Object.entries(ApplicationStatus).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`ApplicationStatus.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(Severity).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`Severity.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(SQLScriptProcedureTaskLog).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`SQLScriptProcedureTaskLog.${key}`] = value;
        return replacements;
      }, {}),
    },
  },
];

export const dbObjects: ProvStep[] = [
  // Replaceable objects (tables/views)
  ...taskFrameworkSpaceProcedures,
  {
    statementPath: "./HDI_GRANTOR_FOR_CUPS.sql",
    precondition: {
      statement: `SELECT COUNT(*) AS "RSLT" from "PUBLIC"."FUNCTIONS" WHERE SCHEMA_NAME = '{{tenantOwner}}' AND FUNCTION_NAME = 'HDI_GRANTOR_FOR_CUPS_CHECK';`,
      preconditionValue: true,
    },
  },
  { statementPath: "./ECN_GET_OBJECTS_METADATA.sql" },
  { statementPath: "./ECN_MANAGE_OBJECTS_METADATA.sql" },
  { statementPath: "./ECN_MANAGE_TABLE_REPLICA.sql" },
  { statementPath: "./ECN_MANAGE_TABLE_REPLICA_PAGE.sql" },
  { statementPath: "./ECN_MANAGE_EXISTING_REPLICAS.sql" },
  { statementPath: "./DAC_GET_FILTER_STRING.sql" },
  {
    statementPath: "./DELTA_PROVIDER_SUBSCRIBER_GET_SUBSCRIBERS.sql",
    stepSpecificPlaceholders: { deltaProviderSubscriberTableName },
  },
  {
    statementPath: "./DELTA_PROVIDER_SUBSCRIBER_UPSERT_SUBSCRIBERS.sql",
    stepSpecificPlaceholders: { deltaProviderSubscriberTableName },
  },
  {
    statementPath: "./DELTA_PROVIDER_SUBSCRIBER_DELETE_SUBSCRIBERS.sql",
    stepSpecificPlaceholders: { deltaProviderSubscriberTableName },
  },
  {
    statementPath: "./DELTA_PROVIDER_SUBSCRIBER_CALCULATE_WATERMARK.sql",
    stepSpecificPlaceholders: { deltaProviderSubscriberTableName },
  },
  {
    statementPath: "./DELTA_PROVIDER_SUBSCRIBER_CLOSE_WATERMARK.sql",
    stepSpecificPlaceholders: { deltaProviderSubscriberTableName },
  },
  { statementPath: "./LTF_OPTIMIZE.sql" },
  { statementPath: "./LTF_TRUNCATE.sql" },
  { statementPath: "./LTF_VACUUM.sql" },
  {
    statementPath: "./TRFFL_EXECUTION_PLAN.sql",
    stepSpecificPlaceholders: {
      ...Object.entries(TransformationFlowTaskLog).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`TransformationFlowTaskLog.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(TransformationFlowSteps).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`TransformationFlowSteps.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(Severity).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`Severity.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(ContentSemantic).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`ContentSemantic.${key}`] = value;
        return replacements;
      }, {}),
    },
  },
  {
    statementPath: "./TRFFL_READ_AND_WRITE.sql",
    stepSpecificPlaceholders: {
      ...Object.entries(TransformationFlowTaskLog).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`TransformationFlowTaskLog.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(Severity).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`Severity.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(MetricName).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`MetricName.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(LabelName).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`LabelName.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(SpaceType).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`SpaceType.${key}`] = String(value);
        return replacements;
      }, {}),
      ...Object.entries(Capability).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`Capability.${key}`] = String(value);
        return replacements;
      }, {}),
      /* Enum values of TransformationFlowTaskSqlErrorCodes are of type number.
         Therefore Object.entries returns an array containing [key,value] as well as [value,key].
         To avoid not needed stepSpecificPlaceholders, it is filtered for [key,value]. */
      ...Object.entries(TransformationFlowTaskSqlErrorCodes)
        .filter(([key, value]) => typeof value === "number")
        .reduce<Record<string, string>>((replacements, [key, value]) => {
          replacements[`TransformationFlowTaskSqlErrorCodes.${key}`] = String(value);
          return replacements;
        }, {}),
      ...Object.entries(TransformationFlowSpecialBatchIds).reduce<Record<string, string>>(
        (replacements, [key, value]) => {
          replacements[`TransformationFlowSpecialBatchIds.${key}`] = value;
          return replacements;
        },
        {}
      ),
      trfflExecuteRtDataTableName,
      ...Object.entries(TransformationFlowSteps).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`TransformationFlowSteps.${key}`] = value;
        return replacements;
      }, {}),
    },
  },
  {
    statementPath: "./TRFFL_GET_BATCHES.sql",
    stepSpecificPlaceholders: {
      ...Object.entries(MetricName).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`MetricName.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(TransformationFlowTaskLog).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`TransformationFlowTaskLog.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(Severity).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`Severity.${key}`] = value;
        return replacements;
      }, {}),
      /* Enum values of TransformationFlowTaskSqlErrorCodes are of type number.
         Therefore Object.entries returns an array containing [key,value] as well as [value,key].
         To avoid not needed stepSpecificPlaceholders, it is filtered for [key,value]. */
      ...Object.entries(TransformationFlowTaskSqlErrorCodes)
        .filter(([key, value]) => typeof value === "number")
        .reduce<Record<string, string>>((replacements, [key, value]) => {
          replacements[`TransformationFlowTaskSqlErrorCodes.${key}`] = String(value);
          return replacements;
        }, {}),
      ...Object.entries(TransformationFlowSpecialBatchIds).reduce<Record<string, string>>(
        (replacements, [key, value]) => {
          replacements[`TransformationFlowSpecialBatchIds.${key}`] = value;
          return replacements;
        },
        {}
      ),
      allowedHanaDataTypesForBatchColumnInList: TransformationFlowBatchHandler.allowedHanaDataTypesForBatchColumn
        .map((allowedHanaDataType) => `'${allowedHanaDataType}'`)
        .join(", "),
      ...Object.entries(TransformationFlowSteps).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`TransformationFlowSteps.${key}`] = value;
        return replacements;
      }, {}),
    },
  },
  {
    statementPath: "./TRFFL_EXECUTE.sql",
    stepSpecificPlaceholders: {
      ...Object.entries(MetricName).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`MetricName.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(LabelName).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`LabelName.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(ApplicationStatus).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`ApplicationStatus.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(TransformationFlowTaskLog).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`TransformationFlowTaskLog.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(Severity).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`Severity.${key}`] = value;
        return replacements;
      }, {}),
      /* Enum values of TransformationFlowExecutionMode are of type number.
         Therefore Object.entries returns an array containing [key,value] as well as [value,key].
         To avoid not needed stepSpecificPlaceholders, it is filtered for [key,value]. */
      ...Object.entries(TransformationFlowExecutionMode)
        .filter(([key, value]) => typeof value === "number")
        .reduce<Record<string, string>>((replacements, [key, value]) => {
          replacements[`TransformationFlowExecutionMode.${key}`] = String(value);
          return replacements;
        }, {}),
      ...Object.entries(TransformationFlowLoadType).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`TransformationFlowLoadType.${key}`] = String(value);
        return replacements;
      }, {}),
      ...Object.entries(ViewTransformParameterSemantic).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`ViewTransformParameterSemantic.${key}`] = String(value);
        return replacements;
      }, {}),
      trfflExecuteRtDataTableName,
      trfflExecuteRtSettingsTableName,
      /* Enum values of TransformationFlowTaskSqlErrorCodes are of type number.
         Therefore Object.entries returns an array containing [key,value] as well as [value,key].
         To avoid not needed stepSpecificPlaceholders, it is filtered for [key,value]. */
      ...Object.entries(TransformationFlowTaskSqlErrorCodes)
        .filter(([key, value]) => typeof value === "number")
        .reduce<Record<string, string>>((replacements, [key, value]) => {
          replacements[`TransformationFlowTaskSqlErrorCodes.${key}`] = String(value);
          return replacements;
        }, {}),
    },
  },
  {
    statementPath: "./TRFFL_EXECUTE_SPARK.sql",
    stepSpecificPlaceholders: {
      ...Object.entries(MetricName).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`MetricName.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(LabelName).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`LabelName.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(ApplicationStatus).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`ApplicationStatus.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(TransformationFlowTaskLog).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`TransformationFlowTaskLog.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(Severity).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`Severity.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(TransformationFlowLoadType).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`TransformationFlowLoadType.${key}`] = String(value);
        return replacements;
      }, {}),
      trfflExecuteRtDataTableName,
      /* Enum values of TransformationFlowTaskSqlErrorCodes are of type number.
         Therefore Object.entries returns an array containing [key,value] as well as [value,key].
         To avoid not needed stepSpecificPlaceholders, it is filtered for [key,value]. */
      ...Object.entries(TransformationFlowTaskSqlErrorCodes)
        .filter(([key, value]) => typeof value === "number")
        .reduce<Record<string, string>>((replacements, [key, value]) => {
          replacements[`TransformationFlowTaskSqlErrorCodes.${key}`] = String(value);
          return replacements;
        }, {}),
    },
  },
  /* LTF_MERGE_PYSPARK will be de-commented as soon as it is ensured that all
     tenants support virtual procedure defined in the LANGUAGE PYSPARK.
     Up to this point in time, the virtual procure will be create at runtime of
     service/table/task/LocalTableFilesTask.ts
  {
    statementPath: "./LTF_MERGE_PYSPARK.sql",
  }, */
  {
    statementPath: "./LTF_MERGE.sql",
    stepSpecificPlaceholders: {
      ...Object.entries(MetricName).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`MetricName.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(LabelName).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`LabelName.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(ApplicationStatus).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`ApplicationStatus.${key}`] = value;
        return replacements;
      }, {}),
      ...Object.entries(LocalTableTaskLogMessageBundleModel).reduce<Record<string, string>>(
        (replacements, [key, value]) => {
          replacements[`LocalTableTaskLogMessageBundleModel.${key}`] = value;
          return replacements;
        },
        {}
      ),
      ...Object.entries(Severity).reduce<Record<string, string>>((replacements, [key, value]) => {
        replacements[`Severity.${key}`] = value;
        return replacements;
      }, {}),
    },
  },
  {
    statement: 'DROP PROCEDURE "{{spc_tec_internal}}"."CREATE_JUSTASK_NLQ_SAMPLES_IF_NOT_EXISTS"',
    precondition: {
      statement: `SELECT COUNT(*) AS "RES" FROM "PUBLIC"."OBJECTS" WHERE "SCHEMA_NAME" = ? AND "OBJECT_NAME" = ? AND "OBJECT_TYPE" = ?`,
      params: ["{{spc_tec_internal}}", "CREATE_JUSTASK_NLQ_SAMPLES_IF_NOT_EXISTS", "PROCEDURE"],
      preconditionValue: true,
    },
  },
];

export const grantTecSpacePrivilegesToUsers: ProvStep[] = [
  {
    statement:
      'GRANT SELECT ON "{{spc_tec_internal}}"."$$REMOTE_TABLE_INFO_VIEW$$" TO "{{spc_tec_internal}}"."{{space_id}}::tecObjectsSelect"',
    ignoreError: true,
  },
  {
    statement:
      'GRANT SELECT ON "{{spc_tec_internal}}"."DEPLOYED_METADATA" TO "{{spc_tec_internal}}"."{{space_id}}::tecObjectsSelect"',
    ignoreError: true,
  },
  {
    statement:
      'GRANT EXECUTE ON "{{spc_tec_internal}}"."DAC_GET_FILTER_STRING" TO "{{spc_tec_internal}}"."{{space_id}}::supportTecDac"',
    ignoreError: true,
  },
  {
    statement:
      'GRANT SELECT ON "{{spc_tec_internal}}"."$$PERSISTED_VIEW$$" TO "{{spc_tec_internal}}"."{{space_id}}::tecObjectsSelect"',
    ignoreError: true,
  },
  {
    statement:
      'GRANT SELECT ON "{{spc_tec_internal}}"."$$LOCAL_TABLE$$" TO "{{spc_tec_internal}}"."{{space_id}}::tecObjectsSelect"',
    ignoreError: true,
  },
  {
    statement:
      'GRANT SELECT ON "{{spc_tec_internal}}"."LOCAL_TABLE_OUTBOUND_METRICS" TO "{{spc_tec_internal}}"."{{space_id}}::tecObjectsSelect"',
    ignoreError: true,
  },
  {
    statement:
      'GRANT SELECT ON "{{spc_tec_internal}}"."$$REMOTE_TABLE$$" TO "{{spc_tec_internal}}"."{{space_id}}::tecObjectsSelect"',
    ignoreError: true,
  },
  {
    statement:
      'GRANT SELECT ON "{{spc_tec_internal}}"."NOTIFICATION_MAILING_LISTS" TO "{{spc_tec_internal}}"."{{space_id}}::tecObjectsSelect"',
    ignoreError: true,
  },
  {
    statement:
      'GRANT SELECT ON "{{spc_tec_internal}}"."NOTIFICATION_MAILING_RUN_CONFIG" TO "{{spc_tec_internal}}"."{{space_id}}::tecObjectsSelect"',
    ignoreError: true,
  },
  {
    statement:
      'GRANT SELECT ON "{{spc_tec_internal}}"."REPLICATIONFLOW_RUN_DETAILS" TO "{{spc_tec_internal}}"."{{space_id}}::tecObjectsSelect"',
    ignoreError: true,
  },
  {
    statement: 'GRANT UPDATE ON "{{spc_tec_internal}}"."REPLICATIONFLOW_RUN_DETAILS" TO "{{globalTaskFramework}}"',
    ignoreError: true,
  },
];

export const grantTecSpacePrivilegesToRoles: ProvStep[] = [
  {
    statement:
      'GRANT EXECUTE ON "{{spc_tec_internal}}"."DELTA_PROVIDER_SUBSCRIBER_GET_SUBSCRIBERS" TO "{{space_id}}"."{{space_id}}::dis"',
    ignoreError: true,
  },
  {
    statement:
      'GRANT EXECUTE ON "{{spc_tec_internal}}"."DELTA_PROVIDER_SUBSCRIBER_CALCULATE_WATERMARK" TO "{{space_id}}"."{{space_id}}::dis"',
    ignoreError: true,
  },
  {
    statement:
      'GRANT EXECUTE ON "{{spc_tec_internal}}"."DELTA_PROVIDER_SUBSCRIBER_CLOSE_WATERMARK" TO "{{space_id}}"."{{space_id}}::dis"',
    ignoreError: true,
  },
  {
    statement:
      'GRANT EXECUTE ON "{{spc_tec_internal}}"."DELTA_PROVIDER_SUBSCRIBER_UPSERT_SUBSCRIBERS" TO "{{space_id}}"."{{space_id}}::dis"',
    ignoreError: true,
  },
  {
    statement:
      'GRANT EXECUTE ON "{{spc_tec_internal}}"."DELTA_PROVIDER_SUBSCRIBER_DELETE_SUBSCRIBERS" TO "{{space_id}}"."{{space_id}}::dis"',
    ignoreError: true,
  },
];

export const grantPrivilegesToUsers: ProvStep[] = [
  {
    statement:
      'GRANT EXECUTE ON "{{spc_tec_internal}}"."TASK_DELETE_REFERENCING_TASK_LOG_ID" TO {{globalOwner}} WITH GRANT OPTION',
  },
  {
    statement:
      'GRANT EXECUTE ON "{{spc_tec_internal}}"."TASK_DELETE_REFERENCING_SCHEDULE_ID" TO {{globalOwner}} WITH GRANT OPTION',
  },
  {
    statement:
      'GRANT EXECUTE ON "{{spc_tec_internal}}"."ECN_GET_OBJECTS_METADATA" TO {{tenantOwner}} WITH GRANT OPTION',
  },
  {
    statement:
      'GRANT EXECUTE ON "{{spc_tec_internal}}"."ECN_MANAGE_OBJECTS_METADATA" TO {{tenantOwner}} WITH GRANT OPTION',
  },
  {
    statement:
      'GRANT EXECUTE ON "{{spc_tec_internal}}"."ECN_MANAGE_TABLE_REPLICA" TO {{tenantOwner}} WITH GRANT OPTION',
  },
  {
    statement:
      'GRANT EXECUTE ON "{{spc_tec_internal}}"."ECN_MANAGE_TABLE_REPLICA_PAGE" TO {{tenantOwner}} WITH GRANT OPTION',
  },
  ...grantTecSpacePrivilegesToUsers,
  ...grantTecSpacePrivilegesToRoles,
];
