-- FILEOWNER: [dw101_crossarchitecture]
CREATE OR REPLACE PROCEDURE "{{tenantOwner}}"."UPDATE_SPACE_RT_STORAGE_CACHE"( 
  IN IN_JOB_NAME NVARCHAR(256) DEFAULT NULL,
  IN IN_FORCE BOOLEAN DEFAULT FALSE
)
LANGUAGE SQLSCRIPT
SQL SECURITY DEFINER AS
BEGIN
  DECLARE outdated boolean;
  DECLARE valid boolean;
  DECLARE exists boolean; 
  DECLARE cacheResult TABLE LIKE "{{tenantOwner}}"."SPACE_RUNTIME_STORAGE_CACHE";
  -- Re-create persistent view cache in case it is invalid or deleted
  LOCK TABLE "{{tenantOwner}}"."SPACE_RUNTIME_STORAGE_CACHE" IN EXCLUSIVE MODE WAIT 1;
  SELECT TO_BOOLEAN(COUNT(*)), TO_BOOLEAN("IS_VALID") INTO exists, valid DEFAULT FALSE,FALSE FROM "SYS"."RESULT_CACHE"
  	WHERE "SCHEMA_NAME" = '{{tenantOwner}}'
    AND "OBJECT_NAME" = 'SPACE_RUNTIME_STORAGE'
    GROUP BY "IS_VALID";
  IF valid = FALSE THEN
    IF exists = TRUE THEN
	  	EXEC 'ALTER VIEW "{{tenantOwner}}"."SPACE_RUNTIME_STORAGE" DROP STATIC CACHE';
	  END IF;
	  EXEC 'ALTER VIEW "{{tenantOwner}}"."SPACE_RUNTIME_STORAGE" ADD STATIC CACHE RETENTION 3'; -- time unit = minutes
  END IF;
  -- Account also for null results here, because passed updates are removed from the cache update monitor at some point in time 
  SELECT
	CASE 
		WHEN SECONDS_BETWEEN(mcache."LAST_REFRESH_TIME", CURRENT_UTCTIMESTAMP) / 60 > scache."CACHE_RETENTION" THEN TO_BOOLEAN(TRUE)
		ELSE TO_BOOLEAN(FALSE)
	END AS "outdated" INTO outdated DEFAULT TRUE
  FROM M_RESULT_CACHE mcache INNER JOIN "SYS"."RESULT_CACHE" scache
    ON mcache."SCHEMA_NAME" = scache."SCHEMA_NAME" 
    AND mcache."OBJECT_NAME" = scache."OBJECT_NAME" 
	WHERE scache."SCHEMA_NAME" = '{{tenantOwner}}' 
	AND scache."OBJECT_NAME" = 'SPACE_RUNTIME_STORAGE';
  IF outdated = TRUE OR IN_FORCE = TRUE THEN
    cacheResult = SELECT * FROM "{{tenantOwner}}"."SPACE_RUNTIME_STORAGE" WITH HINT(RESULT_CACHE);
    -- update snapshot table
    TRUNCATE TABLE "{{tenantOwner}}"."SPACE_RUNTIME_STORAGE_CACHE";
    SELECT * FROM :cacheResult INTO "{{tenantOwner}}"."SPACE_RUNTIME_STORAGE_CACHE";
  END IF;
  IF IN_JOB_NAME IS NOT NULL THEN
    EXEC 'ALTER SCHEDULER JOB ' || :IN_JOB_NAME || ' DISABLE';
  END IF;
END;