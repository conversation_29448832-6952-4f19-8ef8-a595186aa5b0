-- FILEOWNER: [dw101_crossarchitecture]
CREATE OR REPLACE PROCEDURE "{{tenantOwner}}"."GET_SPACE_RUNTIME_STORAGE"(
  OUT OUT_SPACE_RUNTIME_STORAGE TABLE(...)
)
LANGUAGE SQLSCRIPT
SQL SECURITY DEFINER AS
BEGIN
    DECLARE update_job NVARCHAR(256) DEFAULT '"{{tenantOwner}}"."UPDATE_SPACE_RT_STORAGE_CACHE_JOB"';
    DECLARE tcron NVARCHAR(24);
    DECLARE t DATETIME;

	  OUT_SPACE_RUNTIME_STORAGE = SELECT * FROM "{{tenantOwner}}"."SPACE_RUNTIME_STORAGE_CACHE" ORDER BY SPACE_ID ASC;
	-- update cache via scheduled job
    t = ADD_SECONDS(CURRENT_TIMESTAMP, 1);
    tcron = TO_NVARCHAR(YEAR(t)) || ' ' || TO_NVARCHAR(MONTH(t)) || ' ' || TO_NVARCHAR(DAYOFMONTH(t)) || ' * '
          || TO_NVARCHAR(HOUR(t)) || ' ' || TO_NVARCHAR(MINUTE(t)) || ' ' || TO_NVARCHAR(TO_INT(FLOOR(SECOND(t))));
    EXEC 'ALTER SCHEDULER JOB ' || :update_job || ' CRON ''' || :tcron || ''' ENABLE PARAMETERS IN_JOB_NAME=''' || :update_job || '''';
END;