/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { DwcObjectKind } from "@sap/deepsea-types";
import { FeatureFlagProvider } from "@sap/dwc-context-checks";
import { sql } from "@sap/prom-hana-client";
import { SqlTaggedTemplate } from "@sap/prom-hana-client/dist/sql";
import { ReservedEntitySuffixes } from "@sap/seal-interfaces";
import _ from "lodash";
import { IECNMetadataSpaceAsgmtStrtg } from "../../../shared/ecn/ecnMetadataTypes";
import { RESULT_TABLE_SUFFIX } from "../../intelligentlookup/utils/commonUtil";
import { DbClient } from "../../lib/DbClient";
import { IRequestContext } from "../../repository/security/common/common";
import { CustomerHana } from "../../reuseComponents/spaces/src";
import { DesignModel, Link, Node, NodeType, StatusRequired } from "../../task/chains/models";
import { Status } from "../../task/logger/models";
import { Activity, ApplicationId } from "../../task/models";
import { TaskIdentifier, TaskWithParameters } from "../../task/models/TaskIdentifier";
import { TaskFrameworkConstants } from "../../task/shared/db/TaskFrameworkConstants";
import { DynatraceElasticityHelper, meterAndLogError } from "../monitoring/DynatraceElasticityMetrics";
import { ecnChainName, mapNameToApplicationId } from "../utils/Helper";
import { logError, logInfo } from "../utils/LoggerUtils";
import { getEcnRoutedSpacesQuery } from "../utils/sql";
import {
  ECN_CHECK_TABLE_FUNCTION,
  ECN_GET_METADATA_FUNCTION,
  ECN_GET_OBJECT_DEPENDENCIES_VIEW,
  ECN_GET_SPACE_METADATA_FUNCTION,
  EcnTasksParameters,
  IDependencyDetails,
  IEcnAssignedSpace,
  IEcnCsn,
  ITableExtendedFullPath,
  TableType,
} from "../utils/types";

const PARALLELISM_LEVEL = 4;
const INITIAL_TASK_ID = 1;
const AND_TASK_ID = 2;
const ANY_STATUS_REQUIRED = "ANY" as StatusRequired;

export class EcnTaskChainCsnBuilder {
  private _requestContext: IRequestContext;
  private _activity: string | undefined;
  private _ecnId: string;
  private _dbClient: DbClient;

  // getters and setters
  get dbClient() {
    return this._dbClient;
  }

  set dbClient(value) {
    this._dbClient = value;
  }

  constructor(requestContext: IRequestContext, ecnId: string, activity?: string) {
    this._requestContext = requestContext;
    this._ecnId = ecnId;
    this._activity = activity;
  }

  public async build(): Promise<IEcnCsn> {
    try {
      const customerHana = await CustomerHana.fromRequestContext(this._requestContext);
      this.dbClient = await customerHana.getTenantManagerClient();
      const assignedSpaces =
        this._activity === Activity.GENERATE_START_CHAIN
          ? await this.getAssignedSpaces()
          : await this.getRoutedSpaces();
      logInfo(`[ECN_TASK_CHAIN_${this._activity}] number of assigned spaces ${assignedSpaces.length}.`, {
        context: this._requestContext,
      });
      const dependencies: ITableExtendedFullPath[] | IDependencyDetails[] =
        this._activity === Activity.GENERATE_START_CHAIN
          ? await this.getDependencies(assignedSpaces as IEcnAssignedSpace[])
          : await this.getReplicatedTables();
      logInfo(`[ECN_TASK_CHAIN_${this._activity}] number of dependencies ${dependencies.length}.`, {
        context: this._requestContext,
      });
      const cleanedDependencies = _.uniqWith(
        dependencies,
        (d1: Required<ITableExtendedFullPath>, d2: Required<ITableExtendedFullPath>) =>
          d1?.SPACE_ID === d2?.SPACE_ID && d1?.TABLE_NAME === d2?.TABLE_NAME && d1?.SCHEMA_NAME === d2?.SCHEMA_NAME
      ) as Required<ITableExtendedFullPath[]>;
      // exclude remote tables
      const dependenciesWithExc = _.filter(
        cleanedDependencies,
        (t) =>
          !t.TABLE_NAME.toLowerCase().endsWith(ReservedEntitySuffixes.REMOTE_TABLE_PLUGIN_REPLICA_TABLE.toLowerCase())
      );
      // meter eligible dependencies
      meterAndLogError(
        () =>
          DynatraceElasticityHelper.eligibleDependenciesMeter.addCallback((result) => {
            result.observe(
              (dependenciesWithExc ?? []).length,
              DynatraceElasticityHelper.fillDimensions(this._requestContext.tenantId, this._ecnId) as any
            );
          }),
        this._requestContext
      ).catch((error) => logError(error, { context: this._requestContext }));

      const chunkedDependencies = _.chunk(dependenciesWithExc, PARALLELISM_LEVEL);
      const chunkedDependenciesSize = chunkedDependencies.length ?? 0;
      const name = ecnChainName(
        this._ecnId,
        this._activity as Activity.GENERATE_START_CHAIN | Activity.GENERATE_STOP_CHAIN
      );
      return {
        metadata: {
          name,
          csn: {
            taskchains: {
              [name]: {
                kind: DwcObjectKind.taskChain,
                nodes: await this.buildNodes(
                  assignedSpaces.map((a) => a.spaceId),
                  chunkedDependencies,
                  dependenciesWithExc.length
                ),
                links: this.buildLinks(
                  assignedSpaces.length,
                  dependenciesWithExc.length,
                  chunkedDependenciesSize,
                  chunkedDependenciesSize > 0 ? chunkedDependencies[chunkedDependenciesSize - 1].length : 0
                ),
              } as DesignModel,
            },
          },
        },
      };
    } catch (error) {
      logError(error, { context: this._requestContext });
      throw error;
    }
  }

  public async isSpaceRouted(spaceId: string, dbClient: DbClient): Promise<boolean> {
    try {
      this._dbClient = dbClient;
      const routedSpaces = await this.getRoutedSpaces(spaceId);
      return !!routedSpaces?.length;
    } catch (error) {
      logError(error, { context: this._requestContext });
      return false;
    }
  }

  private async buildNodes(
    assignedSpaces: IEcnAssignedSpace[] | string[],
    chunkedDependencies: any[][],
    countDependencies: number
  ): Promise<Node[]> {
    const nodes: Node[] = [];
    // Add/remove elastic compute node task
    nodes.push({
      id: INITIAL_TASK_ID,
      type: NodeType.TASK,
      taskIdentifier: {
        applicationId: ApplicationId.ELASTIC_COMPUTE_NODE,
        activity: this._activity === Activity.GENERATE_START_CHAIN ? Activity.ADD : Activity.REMOVE,
        spaceId: TaskFrameworkConstants.ECN_SPACE,
        objectId: this._ecnId,
      } as TaskIdentifier,
    });
    // Create/drop table replica tables tasks
    chunkedDependencies.forEach((chunk, globalIndex: number) => {
      chunk.forEach((dependency, chunkIndex: number) => {
        nodes.push({
          id: chunkIndex + globalIndex * PARALLELISM_LEVEL + 3,
          type: NodeType.TASK,
          taskIdentifier: {
            applicationId: mapNameToApplicationId(dependency.TABLE_NAME),
            activity:
              this._activity === Activity.GENERATE_START_CHAIN ? Activity.CREATE_REPLICA : Activity.DROP_REPLICA,
            spaceId: TaskFrameworkConstants.ECN_SPACE,
            objectId: `${dependency.SPACE_ID}.${this.mapNameToDesignTimeName(dependency.TABLE_NAME)}`,
            parameters: {
              [EcnTasksParameters.ECN_ID]: this._ecnId,
              [EcnTasksParameters.SCHEMA_NAME]: dependency.SCHEMA_NAME,
              [EcnTasksParameters.TABLE_NAME]: dependency.TABLE_NAME,
              ...(this._activity === Activity.GENERATE_START_CHAIN
                ? {
                    [EcnTasksParameters.PARENT_VIEW_NAME]: dependency.EXPOSED_OBJECT_NAME,
                    [EcnTasksParameters.PARENT_VIEW_SCHEMA_NAME]: dependency.EXPOSED_SCHEMA_NAME,
                    [EcnTasksParameters.TABLE_DISK_SIZE_GB]: dependency.DISK_SIZE_GIB,
                    ...(!!dependency?.TABLE_TYPE
                      ? { [EcnTasksParameters.TABLE_TYPE]: TableType[dependency.TABLE_TYPE as keyof typeof TableType] }
                      : {}),
                  }
                : {}),
            },
          } as TaskWithParameters,
        });
      });
    });
    // AND operator node
    if (countDependencies > 1) {
      nodes.push({
        id: AND_TASK_ID,
        type: NodeType.AND,
      } as any);
    }
    // Spaces routing tasks nodes
    const parameters = {
      [EcnTasksParameters.ECN_ID]: this._ecnId,
    };
    assignedSpaces.forEach((space, index: number) => {
      nodes.push({
        id: index + countDependencies + 3,
        type: NodeType.TASK,
        taskIdentifier: {
          applicationId: ApplicationId.SPACE,
          activity:
            this._activity === Activity.GENERATE_START_CHAIN
              ? Activity.ROUTE_COMPUTE_SERVER
              : Activity.ROUTE_COORDINATOR,
          spaceId: TaskFrameworkConstants.ECN_SPACE,
          objectId: space,
          parameters:
            index === assignedSpaces.length - 1 && this._activity === Activity.GENERATE_START_CHAIN
              ? {
                  ...parameters,
                  [EcnTasksParameters.LAST_ROUTED_SPACE]: 1,
                }
              : parameters,
        } as TaskWithParameters,
      });
    });
    return nodes;
  }

  private buildLinks(
    countAssignedSpaces: number,
    countDependencies: number,
    numberChunks: number,
    lengthLastChunk: number
  ): Link[] {
    const links: Link[] = [];
    // Replication links
    if (countDependencies >= PARALLELISM_LEVEL + 1) {
      for (let i = 1; i <= (numberChunks - 2) * 4 + lengthLastChunk; i++) {
        links.push({
          id: i,
          startNode: {
            nodeId: i + 2,
            statusRequired: ANY_STATUS_REQUIRED, // this._activity === Activity.GENERATE_START_CHAIN ? ANY_STATUS_REQUIRED : Status.COMPLETED,
          },
          endNode: {
            nodeId: i + PARALLELISM_LEVEL + 2,
          },
        });
      }
    }
    // Spaces' routing links
    if (countAssignedSpaces > 1) {
      for (let i = 1; i <= countAssignedSpaces - 1; i++) {
        links.push({
          id: -1 * i,
          startNode: {
            nodeId: i + countDependencies + 2,
            statusRequired: Status.COMPLETED,
          },
          endNode: {
            nodeId: i + countDependencies + 3,
          },
        });
      }
    }
    // From AND operator to the first routed space for start or to remove ECN for stop use case
    if (countDependencies > 1) {
      links.push({
        id: countAssignedSpaces * 100,
        startNode: {
          nodeId: AND_TASK_ID,
        },
        endNode: {
          nodeId: this._activity === Activity.GENERATE_START_CHAIN ? countDependencies + 3 : INITIAL_TASK_ID,
        },
      } as Link);
    }
    // If there is no dependency link from start to space/space to stop
    if (!countDependencies && countAssignedSpaces) {
      links.push({
        id: countAssignedSpaces * 100,
        startNode: {
          nodeId:
            this._activity === Activity.GENERATE_START_CHAIN
              ? INITIAL_TASK_ID
              : countAssignedSpaces - 1 + countDependencies + 3,
          statusRequired: Status.COMPLETED,
        },
        endNode: {
          nodeId: this._activity === Activity.GENERATE_START_CHAIN ? countDependencies + 3 : INITIAL_TASK_ID,
        },
      } as Link);
    }
    const countBranches = PARALLELISM_LEVEL > countDependencies ? countDependencies : PARALLELISM_LEVEL;
    for (let i = countBranches; i > 0; i--) {
      links.push(
        ...[
          {
            id: (countDependencies + i) * 100,
            startNode: {
              nodeId:
                this._activity === Activity.GENERATE_START_CHAIN
                  ? INITIAL_TASK_ID
                  : countAssignedSpaces + countDependencies + 2,
              statusRequired: Status.COMPLETED,
            },
            endNode: {
              nodeId: i + 2,
            },
          } as Link,
          {
            id: (countDependencies + i) * -100,
            startNode: {
              nodeId: countDependencies + 3 - i,
              statusRequired: ANY_STATUS_REQUIRED,
              /** countDependencies === 1 && this._activity === Activity.GENERATE_START_CHAIN
                  ? ANY_STATUS_REQUIRED
                  : this._activity === Activity.GENERATE_STOP_CHAIN // STOP requires COMPLETED status, otherwise countDependencies === 1 && this._activity === Activity.GENERATE_STOP_CHAIN
                  ? Status.COMPLETED
                  : ANY_STATUS_REQUIRED,
                  */
            },
            endNode: {
              nodeId:
                countDependencies === 1 && this._activity === Activity.GENERATE_START_CHAIN
                  ? 4
                  : countDependencies === 1 && this._activity === Activity.GENERATE_STOP_CHAIN
                  ? INITIAL_TASK_ID
                  : AND_TASK_ID, // 4 is the nodeId of the entry space
            },
          } as Link,
        ]
      );
    }
    return links;
  }

  private async getAssignedSpaces(): Promise<IEcnAssignedSpace[]> {
    try {
      const sqlTemplate = sql`
      SELECT
        DISTINCT
          "SPACE_ID",
          "ASSIGNMENT_STRATEGY"
      FROM
        ${sql.quoted(CustomerHana.tenantOwnerSchema)}.${sql.quoted(ECN_GET_METADATA_FUNCTION)}()
        WHERE "ECN_ID" = ${this._ecnId}
        AND "SPACE_ID" IS NOT NULL;`;
      const result = await this.dbClient.exec<{
        SPACE_ID: string;
        ASSIGNMENT_STRATEGY: IECNMetadataSpaceAsgmtStrtg;
      }>(sqlTemplate);
      return result.map((s) => ({
        spaceId: s.SPACE_ID,
        assignmentStrategy: s.ASSIGNMENT_STRATEGY,
      }));
    } catch (error) {
      logError(["EcnTaskChainCsnBuilder", error], { context: this._requestContext });
      return [];
    }
  }

  private async getRoutedSpaces(spaceId?: string): Promise<Array<Omit<IEcnAssignedSpace, "assignmentStrategy">>> {
    try {
      const ecnCheckSpaceTemplate = sql`
          SELECT DISTINCT
            BSC."SPACE_ID" AS "SPACE_ID"
          FROM ( ${sql.join([getEcnRoutedSpacesQuery()], sql``)} ) AS BSC
          WHERE BSC."ECN_ID" = ${this._ecnId} `;
      const result = await this.dbClient.exec<{
        SPACE_ID: string;
      }>(
        !spaceId
          ? ecnCheckSpaceTemplate
          : sql`${sql.join([ecnCheckSpaceTemplate], sql` AND BSC."SPACE_ID" = ${spaceId}`)}`
      );
      return result.map((s) => ({ spaceId: s.SPACE_ID })) || [];
    } catch (error) {
      logError(["EcnTaskChainCsnBuilder", error], { context: this._requestContext });
      return [];
    }
  }

  private async getDependencies(assignedSpaces: IEcnAssignedSpace[]): Promise<IDependencyDetails[]> {
    if (!assignedSpaces?.length) {
      return [];
    }
    try {
      const manualAssignStrSpaces: string[] = [];
      const assignedSpacesIds: string[] = assignedSpaces.map((s) => s.spaceId);
      const automaticAssignStrSpaces = assignedSpaces.reduce((r: string[], s) => {
        if (s.assignmentStrategy === IECNMetadataSpaceAsgmtStrtg.automatic) {
          r.push(s.spaceId);
        } else if (s.assignmentStrategy === IECNMetadataSpaceAsgmtStrtg.manual) {
          manualAssignStrSpaces.push(s.spaceId);
        }
        return r;
      }, []);
      const schemaValidatorTemplate = sql`DEPENDENCIES."SCHEMA_NAME" IN (
        SELECT "SCHEMA_NAME"
          FROM ${sql.quoted(CustomerHana.tenantOwnerSchema, "SPACE_SCHEMAS")}
            WHERE "SPACE_ID" IN ${assignedSpacesIds}
            ${
              (await FeatureFlagProvider.isFeatureActive(this._requestContext, "DWCO_ECN_NON_LOCAL_TABLE_REPLICA"))
                ? sql``
                : sql` AND "SCHEMA_TYPE" NOT IN ( 'hdi_owned', 'customer_owned' )`
            }) )`;
      const conditions: SqlTaggedTemplate[] = [];
      if (manualAssignStrSpaces.length) {
        conditions.push(
          sql.join(
            [
              sql` ( ( DEPENDENCIES."EXPOSED_SCHEMA_NAME", DEPENDENCIES."EXPOSED_OBJECT_NAME" ) IN (
          SELECT "EXPOSED_SCHEMA_NAME", "EXPOSED_OBJECT_NAME" FROM (
          SELECT
            DISTINCT RESULT."SCHEMA_NAME" AS "EXPOSED_SCHEMA_NAME",
            RESULT."OBJECT_NAME" AS "EXPOSED_OBJECT_NAME",
            COUNT(*) OVER (PARTITION BY RESULT."SCHEMA_NAME", RESULT."OBJECT_NAME") AS "ASSIGNED_OBJECT_COUNT",
            RESULT."SPACE_ID" AS "SPACE_ID"
          FROM ${sql.quoted(CustomerHana.tenantOwnerSchema)}.${sql.quoted(ECN_GET_SPACE_METADATA_FUNCTION)}(${
                this._ecnId
              }) AS RESULT
          WHERE
            RESULT."SPACE_ID" IN ${manualAssignStrSpaces} )
            WHERE "ASSIGNED_OBJECT_COUNT" >= 1 )`,
              schemaValidatorTemplate,
            ],
            sql` AND `
          )
        );
      }
      if (automaticAssignStrSpaces.length) {
        conditions.push(
          sql.join(
            [sql` ( DEPENDENCIES."EXPOSED_SCHEMA_NAME" IN ${automaticAssignStrSpaces}`, schemaValidatorTemplate],
            sql` AND `
          )
        );
      }
      const sqlTemplate = sql`SELECT
                    DISTINCT DEPENDENCIES."TABLE_NAME" AS "TABLE_NAME",
                    DEPENDENCIES."SPACE_ID" AS "SPACE_ID",
                    DEPENDENCIES."SCHEMA_NAME" AS "SCHEMA_NAME",
                    DEPENDENCIES."TABLE_TYPE" AS "TABLE_TYPE",
                    DEPENDENCIES."LOAD_UNIT" AS "LOAD_UNIT",
                    DEPENDENCIES."SCHEMA_TYPE" AS "SCHEMA_TYPE",
                    DEPENDENCIES."EXPOSED_OBJECT_NAME" AS "EXPOSED_OBJECT_NAME",
                    DEPENDENCIES."EXPOSED_SCHEMA_NAME" AS "EXPOSED_SCHEMA_NAME",
                    DEPENDENCIES."EXPOSED_OBJECT_TYPE" AS "EXPOSED_OBJECT_TYPE",
                    ROUND( TO_DOUBLE(STATISTICS."DISK_SIZE" / 1024 / 1024 /1024 ), 3) AS "DISK_SIZE_GIB"
                  FROM ${sql.quoted(CustomerHana.tenantOwnerSchema, ECN_GET_OBJECT_DEPENDENCIES_VIEW)} AS DEPENDENCIES
                    JOIN ${sql.quoted("SYS", "M_TABLE_PERSISTENCE_STATISTICS")} AS STATISTICS
                    ON DEPENDENCIES."SCHEMA_NAME" = STATISTICS."SCHEMA_NAME"
                    AND DEPENDENCIES."TABLE_NAME" = STATISTICS."TABLE_NAME"
                    WHERE ${sql.join(conditions, sql` OR `)}`;
      const dependencies = await this.dbClient.exec<IDependencyDetails>(sqlTemplate);
      meterAndLogError(
        () =>
          DynatraceElasticityHelper.discoveredDependenciesMeter.addCallback((result) => {
            result.observe(
              (dependencies ?? []).length,
              DynatraceElasticityHelper.fillDimensions(this._requestContext.tenantId, this._ecnId) as any
            );
          }),
        this._requestContext
      ).catch((error) => logError(error, { context: this._requestContext }));
      meterAndLogError(
        () =>
          DynatraceElasticityHelper.estimatedReplicatedMemoryMeter.record(
            (dependencies ?? []).reduce((acc, dep) => acc + dep.DISK_SIZE_GIB, 0),
            DynatraceElasticityHelper.fillDimensions(this._requestContext.tenantId, this._ecnId) as any
          ),
        this._requestContext
      ).catch((error) => logError(error, { context: this._requestContext }));
      return dependencies;
    } catch (error) {
      logError(error, { context: this._requestContext });
      return [];
    }
  }

  private async getReplicatedTables(): Promise<ITableExtendedFullPath[]> {
    const replicaEnabled = "ENABLED";
    try {
      const sqlTemplate = sql`
    SELECT DISTINCT "SPACE_ID",
       "SOURCE_SCHEMA_NAME" AS "SCHEMA_NAME",
       "SOURCE_TABLE_NAME" AS "TABLE_NAME"
      FROM ${sql.quoted(CustomerHana.tenantOwnerSchema)}.${sql.quoted(ECN_CHECK_TABLE_FUNCTION)}()
      WHERE "ECN_ID" = ${this._ecnId}
        AND "REPLICATION_STATUS" = ${replicaEnabled};`;
      return await this.dbClient.exec<ITableExtendedFullPath>(sqlTemplate);
    } catch (error) {
      logError(error, { context: this._requestContext });
      return [];
    }
  }

  private mapNameToDesignTimeName(name: string): string {
    if (name.endsWith(ReservedEntitySuffixes.View_Materialization_PersistencyTable_1)) {
      return name.slice(0, -1 * ReservedEntitySuffixes.View_Materialization_PersistencyTable_1.length);
    } else if (name.endsWith(ReservedEntitySuffixes.View_Materialization_PersistencyTable_2)) {
      return name.slice(0, -1 * ReservedEntitySuffixes.View_Materialization_PersistencyTable_2.length);
    } else if (name.endsWith(`${RESULT_TABLE_SUFFIX}`)) {
      return name.slice(0, -1 * RESULT_TABLE_SUFFIX.length);
    } else if (name.endsWith(ReservedEntitySuffixes.Delta_Table)) {
      return name.slice(0, -1 * ReservedEntitySuffixes.Delta_Table.length);
    } else {
      return name;
    }
  }
}
