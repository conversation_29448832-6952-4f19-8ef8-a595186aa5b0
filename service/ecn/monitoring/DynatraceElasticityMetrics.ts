/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { ValueType } from "@opentelemetry/api";
import { IRequestContext } from "@sap/deepsea-types";
import telemetry from "@sap/dwc-telemetry";
import { PerformanceClass } from "../../../shared/provisioning/ftc/types";
import { logErrorWithOptionalContext } from "../utils/LoggerUtils";

export interface IElasticityMetricDimensions {
  tenantid: string | null;
  ecnid?: string;
  action?: "Provision" | "Deprovision";
  performanceClass?: PerformanceClass;
}

export enum EcnDynatraceMetricKeys {
  // Utils
  ELASTICITY_ERRORS = "dwc.v0.elasticity.errors",
  // Design-time metrics
  DESIGNED_ECN = "dwc.v0.elasticity.designed.ecn", // Count of designed ECNs
  ASSIGNED_SPACE = "dwc.v0.elasticity.assigned.space", // Count of assigned spaces
  MANUALLY_ASSIGNED_OBJECT = "dwc.v0.elasticity.assigned.object", // Count of assigned objects
  DISCOVERED_DEPENDENCIES = "dwc.v0.elasticity.dependencies", // Gauge of discovered dependencies
  ELIGIBLE_DEPENDENCIES = "dwc.v0.elasticity.eligible.dependencies", // Gauge of eligible dependencies
  DETECTED_REPLICATED_TABLE = "dwc.v0.elasticity.detected.replicated.table", // Count of detected replicated tables
  // Runtime metrics
  PROVISIONED_ECN = "dwc.v0.elasticity.provisioned.ecn", // Count of provisioned ECNs, within the meter // FIXME: remove
  DEPROVISIONED_ECN = "dwc.v0.elasticity.deprovisioned.ecn", // Count of deprovisioned ECNs, within the meter // FIXME: remove
  METERED_ECN = "dwc.v0.elasticity.metered.ecn", // Count of metered ECNs, within the meter, add provisioning and deprovisioning as dimension
  ROUTED_SPACE = "dwc.v0.elasticity.routed.space", // Count vs Gauge, withing the task
  REPLICATED_TABLE = "dwc.v0.elasticity.replicated.table", // within the task
  COMPUTE_NODE_MEMORY = "dwc.v0.elasticity.memory", // within the task,
  COMPUTE_NODE_CPU = "dwc.v0.elasticity.cpu", // within the task
  COMPUTE_NODE_DISK = "dwc.v0.elasticity.disk", // within the task
  OVERALL_UPTIME_ECN = "dwc.v0.elasticity.uptime.ecn", // within the task
  ESTIMATED_REPLICATED_MEMORY = "dwc.v0.elasticity.estimated.replicated.memory", // within the task, histogram
  // TODO: Execution metrics
  EXECUTED_MDS_QUERIES = "dwc.v0.elasticity.executed.mds.queries", // within the task
}

export class DynatraceElasticityHelper {
  public static designedEcnMeter = telemetry.metrics.getMeter().createCounter(EcnDynatraceMetricKeys.DESIGNED_ECN, {
    description: "designed elastic compute node",
    valueType: ValueType.INT,
  });
  public static assignedSpaceMeter = telemetry.metrics.getMeter().createCounter(EcnDynatraceMetricKeys.ASSIGNED_SPACE, {
    description: "assigned spaces",
    valueType: ValueType.INT,
  });
  public static manuallyAssignedObjectMeter = telemetry.metrics
    .getMeter()
    .createCounter(EcnDynatraceMetricKeys.MANUALLY_ASSIGNED_OBJECT, {
      description: "assigned analytic objects",
      valueType: ValueType.INT,
    });
  public static discoveredDependenciesMeter = telemetry.metrics
    .getMeter()
    .createObservableGauge(EcnDynatraceMetricKeys.DISCOVERED_DEPENDENCIES, {
      description: "discovered dependencies for an elastic compute node",
      valueType: ValueType.DOUBLE,
    });
  public static eligibleDependenciesMeter = telemetry.metrics
    .getMeter()
    .createObservableGauge(EcnDynatraceMetricKeys.ELIGIBLE_DEPENDENCIES, {
      description: "eligible dependencies for an elastic compute node",
      valueType: ValueType.INT,
    });
  public static meteredEcnMeter = telemetry.metrics.getMeter().createCounter(EcnDynatraceMetricKeys.METERED_ECN, {
    description: "metered elastic compute nodes",
    valueType: ValueType.INT,
  });
  public static estimatedReplicatedMemoryMeter = telemetry.metrics
    .getMeter()
    .createHistogram(EcnDynatraceMetricKeys.ESTIMATED_REPLICATED_MEMORY, {
      description: "estimated replicated memory in GiB",
      valueType: ValueType.DOUBLE,
    });
  public static overallMemoryMeter = telemetry.metrics
    .getMeter()
    .createCounter(EcnDynatraceMetricKeys.COMPUTE_NODE_MEMORY, {
      description: "compute nodes memory",
      valueType: ValueType.INT,
    });
  public static overallCpuMeter = telemetry.metrics.getMeter().createCounter(EcnDynatraceMetricKeys.COMPUTE_NODE_CPU, {
    description: "compute nodes cores",
    valueType: ValueType.INT,
  });
  public static overallDiskMeter = telemetry.metrics
    .getMeter()
    .createCounter(EcnDynatraceMetricKeys.COMPUTE_NODE_DISK, {
      description: "compute nodes storage",
      valueType: ValueType.INT,
    });
  public static executedMDSQueriesMeter = telemetry.metrics
    .getMeter()
    .createObservableGauge(EcnDynatraceMetricKeys.EXECUTED_MDS_QUERIES, {
      description: "count of executed MDS queries",
      valueType: ValueType.INT,
    });

  public static fillDimensions = (
    tenantid: string | null,
    ecnid?: string,
    action?: "Provision" | "Deprovision",
    performanceClass?: PerformanceClass
  ): IElasticityMetricDimensions => {
    const dimensions: IElasticityMetricDimensions = { tenantid };
    if (ecnid) {
      dimensions.ecnid = ecnid;
    }
    if (action) {
      dimensions.action = action;
    }
    if (performanceClass) {
      dimensions.performanceClass = performanceClass;
    }
    return dimensions;
  };
}

export const meterAndLogError = async (fn: () => void, context: IRequestContext) => {
  try {
    fn();
  } catch (error) {
    logErrorWithOptionalContext([`error while running ${fn.name}`, error]);
    // telemetry.metrics.getMeter().createCounter(EcnDynatraceMetricKeys.ELASTICITY_ERRORS).add(1);
  }
};
