/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { HanaError } from "@sap/prom-hana-client";
import { EcnPhaseSnapshot } from "../../../shared/ecn/ecnMetadataTypes";
import { DbClient } from "../../lib/DbClient";
import { IRequestContext } from "../../repository/security/common/common";
import { CustomerHana } from "../../reuseComponents/spaces/src";
import { ITaskLogger, Severity, Status, Substatus } from "../../task/logger/models";
import { Activity, ApplicationId, Parameters } from "../../task/models";
import { IExecutable } from "../../task/models/IExecutable";
import { TaskExecuteResponse } from "../../task/models/TaskExecuteResponse";
import { overwriteLockBasedOnObserver } from "../../task/observer/overwriteLockBasedOnObserver";
import { ActiveSessionChecker } from "../../task/orchestrator/services/activesession/ActiveSessionChecker";
import { LockIdentifier, LockKey, LockedTask } from "../../task/orchestrator/services/tasklock";
import { OverwriteResponse } from "../../task/orchestrator/services/tasklock/OverwriteResponse";
import { logError } from "../../task/shared/logger/TaskFrameworkLogger";
import { EcnFlowFootPrintService } from "../impl/EcnFlowFootPrintService";
import { EcnLocalTableReplicationService } from "../impl/EcnLocalTableReplicationService";
import { isSessionErroneous, isUserTechnical, mapNameToApplicationId, validator } from "../utils/Helper";
import { EcnLogger, logWarning } from "../utils/LoggerUtils";
import {
  ComputeNodeMessage,
  EcnReplicationOperation,
  EcnStatusSnapshot,
  EcnTasksParameters,
  EcnTasksParametersLabels,
  ITableFullPath,
  TableType,
} from "../utils/types";

export class EcnReplicationTask implements IExecutable {
  _objectName: string;
  _tableName: string;
  _tableSpaceId: string;
  _ecnId: string;

  // getters und setters
  get objectName() {
    return this._objectName;
  }
  set objectName(value) {
    this._objectName = value;
  }
  get tableSpaceId() {
    return this._tableSpaceId;
  }
  set tableSpaceId(value) {
    this._tableSpaceId = value;
  }
  get tableName() {
    return this._parameters?.[EcnTasksParameters.TABLE_NAME] ?? undefined;
  }
  set tableName(value) {
    this._tableName = value;
  }
  get ecnId() {
    return this._parameters?.[EcnTasksParameters.ECN_ID] ?? undefined;
  }
  set ecnId(value) {
    this._ecnId = value;
  }

  constructor(
    protected _requestContext: IRequestContext,
    protected _spaceId: string,
    protected _objectFullName: string,
    protected _activity: string,
    protected _parameters: Parameters // ecnId and schema name, table name and parent view full path
  ) {
    const objectPath = this.spreadObjectPath(this._objectFullName);
    this._tableSpaceId = objectPath.spaceId;
    this._objectName = objectPath.technicalName;
    this._tableName = this.tableName;
    this._ecnId = this.ecnId;
  }
  async execute(taskLogger: ITaskLogger): Promise<TaskExecuteResponse> {
    const replicateToEcn = (this._activity as Activity) === Activity.CREATE_REPLICA ?? false;
    const taskLogId = taskLogger.getLogId();
    let dbClient: DbClient | undefined;
    let ecnFlowFootPrintService: EcnFlowFootPrintService | undefined;
    let schemaName: string | undefined;
    try {
      const { schema, ...res } = this.validateAndFetchParameters();
      schemaName = schema;
      if (replicateToEcn && !!res?.tableType && res?.tableType === TableType.ROW) {
        await EcnLogger.logTaskMessage(
          this._requestContext,
          taskLogger,
          ComputeNodeMessage.ECN_ROW_STORE_REPLICATION,
          `Unable to replicate the table '${schemaName}'.'${this._objectName}', since the replication of row table is deprecated.`,
          [schemaName, this._objectName],
          Severity.ERROR
        );
        return { status: Status.FAILED, subStatusCode: Substatus.PREREQ_NOT_MET };
      }
      await EcnLogger.logTaskMessage(
        this._requestContext,
        taskLogger,
        replicateToEcn
          ? ComputeNodeMessage.REPLICATE_TO_ECN_STARTED
          : ComputeNodeMessage.REMOVE_REPLICA_FROM_ECN_STARTED,
        replicateToEcn
          ? `Creation and activation of replica, for the source object '${schemaName}'.'${this._objectName}' in the elastic compute node ${this._ecnId}, has been started.`
          : `Removing replica, for the source object '${schemaName}'.'${this._objectName}' from the elastic compute node ${this._ecnId}, has been started.`,
        [schemaName, this._objectName, this._ecnId]
      );
      logWarning(`Full technical name of the replicated table "${schemaName}"."${this._tableName}".`, {
        context: this._requestContext,
      });
      if (replicateToEcn && !!res?.parentViewName && !!res?.parentViewSchemaName) {
        await EcnLogger.logTaskMessage(
          this._requestContext,
          taskLogger,
          ComputeNodeMessage.REPLICATE_PARENT_CONSUMPTION_VIEW_PATH,
          `The source object '${schemaName}'.'${this._objectName}' is replicated as dependency of the view '${res.parentViewSchemaName}'.'${res.parentViewName}.`,
          [schemaName, this._objectName, res.parentViewSchemaName, res.parentViewName],
          Severity.INFO,
          `Table Disk Size: ${this._parameters?.[EcnTasksParameters.TABLE_DISK_SIZE_GB] ?? 0} (GiB).`
        );
      }
      dbClient = await this.getDbClient();
      const localTableReplicationService = new EcnLocalTableReplicationService(this._requestContext, dbClient);
      await localTableReplicationService.manageReplica(
        validator(this._ecnId, EcnTasksParametersLabels.ECN_ID),
        this._tableSpaceId,
        [
          {
            tableName: validator(this._tableName, EcnTasksParametersLabels.TABLE_NAME),
            schemaName,
          } as ITableFullPath,
        ],
        this.mapActivityToReplicationOperation(this._activity as Activity.CREATE_REPLICA | Activity.DROP_REPLICA)!,
        taskLogId
      );
      if (await isSessionErroneous(this._requestContext, taskLogId)) {
        if (!replicateToEcn) {
          ecnFlowFootPrintService = new EcnFlowFootPrintService(this._requestContext, dbClient);
          await ecnFlowFootPrintService.updatePhaseSnapshot([
            {
              ecnId: this._ecnId,
              taskLogId: taskLogger.getLogId(),
              phase: EcnPhaseSnapshot.STOPPING,
              status: EcnStatusSnapshot.RED,
            },
          ]);
        }
        await EcnLogger.logTaskMessage(
          this._requestContext,
          taskLogger,
          replicateToEcn
            ? ComputeNodeMessage.REPLICATE_TO_ECN_FAILED
            : ComputeNodeMessage.REMOVE_REPLICA_FROM_ECN_FAILED,
          replicateToEcn
            ? `Creation and activation of replica, for the source object '${schemaName}'.'${this._objectName}', failed.`
            : `Removing replica, for the source object '${schemaName}'.'${this._objectName}', failed.`,
          [schemaName, this._objectName],
          Severity.ERROR
        );
        return { status: Status.FAILED };
      }
      return { status: Status.COMPLETED };
    } catch (error) {
      let status: TaskExecuteResponse = { status: Status.FAILED };
      logError(["EcnReplicationTask", error], { context: this._requestContext });
      if (error instanceof HanaError && error.code === 616) {
        status = { ...status, subStatusCode: Substatus.RESOURCE_LIMIT_ERROR };
      }
      if (dbClient && !replicateToEcn) {
        ecnFlowFootPrintService = new EcnFlowFootPrintService(this._requestContext, dbClient);
        await ecnFlowFootPrintService.updatePhaseSnapshot(
          [
            {
              ecnId: this._ecnId,
              taskLogId: taskLogger.getLogId(),
              phase: EcnPhaseSnapshot.STOPPING,
              status: EcnStatusSnapshot.RED,
            },
          ],
          false
        );
      }
      // use schemaName if defined otherwise use this._tableSpaceId
      const objectPath = schemaName ?? this._tableSpaceId;
      await EcnLogger.logTaskMessage(
        this._requestContext,
        taskLogger,
        replicateToEcn ? ComputeNodeMessage.REPLICATE_TO_ECN_FAILED : ComputeNodeMessage.REMOVE_REPLICA_FROM_ECN_FAILED,
        replicateToEcn
          ? `Creation and activation of replica, for the source object '${objectPath}'.'${this._objectName}', failed.`
          : `Removing replica, for the source object '${objectPath}'.'${this._objectName}', failed.`,
        [objectPath, this._objectName],
        Severity.ERROR,
        error.message
      );
      return { status: Status.FAILED };
    } finally {
      await dbClient?.close();
    }
  }

  async isAuthorized(taskLogger: ITaskLogger, isDesignTime?: boolean | undefined): Promise<boolean> {
    return isUserTechnical(this._requestContext);
  }

  getLockIdentifier(): LockIdentifier | null {
    return {
      lockKey:
        mapNameToApplicationId(this._tableName) === ApplicationId.INTELLIGENT_LOOKUP ? LockKey.EXECUTE : LockKey.WRITE,
      applicationId: mapNameToApplicationId(this._tableName),
      spaceId: this._tableSpaceId,
      objectId: this._objectName,
    };
  }

  async overwriteLock(lock: LockedTask): Promise<OverwriteResponse> {
    if (mapNameToApplicationId(this._tableName) === ApplicationId.VIEWS) {
      return await overwriteLockBasedOnObserver(lock, this._requestContext);
    } else {
      const SessionChecker = new ActiveSessionChecker(this._requestContext);
      const sessionResult = await SessionChecker.checkIfOverwritePossible(lock);
      const response = sessionResult.overwriteResponse;
      return response;
    }
  }

  protected validateAndFetchParameters(): {
    schema: string;
    parentViewName?: string;
    parentViewSchemaName?: string;
    tableType?: TableType;
  } {
    return {
      schema: validator(
        this._parameters?.[EcnTasksParameters.SCHEMA_NAME] ?? undefined,
        EcnTasksParametersLabels.SCHEMA_NAME
      ),
      ...((this._activity as Activity) === Activity.CREATE_REPLICA
        ? {
            parentViewName: this._parameters?.[EcnTasksParameters.PARENT_VIEW_NAME] ?? undefined,
            parentViewSchemaName: this._parameters?.[EcnTasksParameters.PARENT_VIEW_SCHEMA_NAME] ?? undefined,
            tableType: this._parameters?.[EcnTasksParameters.TABLE_TYPE] ?? undefined,
          }
        : {}),
    };
  }

  protected async getDbClient(): Promise<DbClient> {
    const customerHana = await CustomerHana.fromRequestContext(this._requestContext);
    return await customerHana.getTenantManagerClient();
  }

  protected mapActivityToReplicationOperation = (
    activity: Activity.CREATE_REPLICA | Activity.DROP_REPLICA
  ): EcnReplicationOperation =>
    activity === Activity.CREATE_REPLICA ? EcnReplicationOperation.ENABLE : EcnReplicationOperation.DISABLE;

  private spreadObjectPath = (
    fullPath: string,
    separator: string = "."
  ): { spaceId: string; technicalName: string } => {
    const [spaceId, ...rest] = fullPath.split(separator);
    return {
      spaceId,
      technicalName: rest.join(separator),
    };
  };
}
