/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { sql } from "@sap/prom-hana-client";
import { EcnPhaseSnapshot } from "../../../shared/ecn/ecnMetadataTypes";
import { DbClient } from "../../lib/DbClient";
import { CustomerHana } from "../../reuseComponents/spaces/src";
import { IElasticComputeNode } from "../../scp/types";
import { ITaskLogger, Severity, Status } from "../../task/logger/models";
import { TaskExecuteResponse } from "../../task/models/TaskExecuteResponse";
import { EcnFlowFootPrintService } from "../impl/EcnFlowFootPrintService";
import { EcnManagementError } from "../impl/EcnManagementError";
import { ElasticComputeNodeAPI } from "../impl/EcnRestApi";
import { isEcnProvisioned, MaxEcnInstances } from "../utils/Helper";
import { EcnLogger, logError, logInfo, logWarning } from "../utils/LoggerUtils";
import {
  ComputeNodeMessage,
  ECN_GET_METADATA_FUNCTION,
  EcnStatus,
  EcnStatusSnapshot,
  IEcnApiResponse,
  IEcnStatusSnapshotEntry,
} from "../utils/types";
import { EcnTask } from "./EcnTask";

export class AddEcnTask extends EcnTask {
  async execute(taskLogger: ITaskLogger): Promise<TaskExecuteResponse> {
    let phase = EcnPhaseSnapshot.STARTING;
    let dbClient: DbClient | undefined;
    let ecnFlowFootPrintService: EcnFlowFootPrintService | undefined;
    let ecnRestApi: ElasticComputeNodeAPI | undefined;
    try {
      const customerHana = await CustomerHana.fromRequestContext(this._requestContext);
      dbClient = await customerHana.getTenantManagerClient();
      ecnFlowFootPrintService = new EcnFlowFootPrintService(this._requestContext, dbClient);
      const currentSnapshot = (await ecnFlowFootPrintService.getSnapshot([this._ecnId])) as IEcnStatusSnapshotEntry[];
      const currentPhase = currentSnapshot?.[0]?.phase ?? undefined;
      ecnRestApi = await ElasticComputeNodeAPI.fromRequestContext(this._requestContext);
      const ecnsApiDetails = (await ecnRestApi.getEcns()) as IEcnApiResponse[];
      if ((ecnsApiDetails.filter((ecn) => !!ecn?.status?.ready ?? false) ?? []).length >= MaxEcnInstances()) {
        await EcnLogger.logTaskMessage(
          this._requestContext,
          taskLogger,
          ComputeNodeMessage.ECN_INSTANCES_MAX,
          `Maximum elastic compute node per SAP HANA Cloud instance is exceeded.`,
          [],
          Severity.ERROR
        );
        await ecnFlowFootPrintService.updatePhaseSnapshot([
          {
            ecnId: this._ecnId,
            taskLogId: taskLogger.getLogId(),
            phase,
            status: EcnStatusSnapshot.RED,
          },
        ]);
        return { status: Status.FAILED };
      }
      phase =
        (currentPhase as EcnPhaseSnapshot | undefined) === EcnPhaseSnapshot.ACTIVE
          ? (currentPhase as EcnPhaseSnapshot)
          : EcnPhaseSnapshot.STARTING;
      // do not propagate error for this case
      await ecnFlowFootPrintService.updatePhaseSnapshot(
        [
          {
            ecnId: this._ecnId,
            taskLogId: taskLogger.getLogId(),
            phase,
            status: EcnStatusSnapshot.YELLOW,
          },
        ],
        false
      );
      const ecn = await this.getEcnSizingPlan(dbClient);
      if (!ecn || !this.isIElasticComputeNode(ecn)) {
        await EcnLogger.logTaskMessage(
          this._requestContext,
          taskLogger,
          ComputeNodeMessage.ECN_NOT_CONFIGURED,
          `The elastic compute node ${this._ecnId} configuration, is not maintained accordingly. Please check your definition.`,
          [this._ecnId],
          Severity.ERROR
        );
        await ecnFlowFootPrintService.updatePhaseSnapshot([
          {
            ecnId: this._ecnId,
            taskLogId: taskLogger.getLogId(),
            phase,
            status: EcnStatusSnapshot.RED,
          },
        ]);
        return { status: Status.FAILED };
      }
      await EcnLogger.logTaskMessage(
        this._requestContext,
        taskLogger,
        ComputeNodeMessage.ECN_PROVISIONING_SIZING_PLAN,
        `The provisioning of the elastic compute node ${this._ecnId} has started with the given sizing plan: vCPUs: ${ecn.vcpu}, memory (GiB): ${ecn.memory}, and storage size(GiB): ${ecn.storage}.`,
        [this._ecnId, ecn?.vcpu.toString(), ecn?.memory.toString(), ecn?.storage.toString()]
      );
      let ecnStatus: EcnStatus;
      // get ecn by id
      const ecnApiResponse = await ecnRestApi!.getEcnById(this._ecnId);
      if (ecnApiResponse) {
        ecnStatus = EcnStatus.DEPLOYED;
      } else {
        await ecnRestApi!.createEcn({
          name: this._ecnId,
          plan: {
            vCPUs: ecn.vcpu,
            memorySizeGiB: ecn.memory,
            storageSizeGiB: ecn.storage,
          },
        });
        ecnStatus = EcnStatus.SETUP;
      }
      logInfo(`fetched ecn status after triggering the provisioning is ${ecnStatus}`, {
        context: this._requestContext,
      });
      if (this.isHCDeploymentNotRequired(ecnStatus) || (await isEcnProvisioned(this._requestContext, this._ecnId))) {
        logWarning(`[AddEcnTask]ecn ${this._ecnId} re-provisioning not required.`, { context: this._requestContext });
        await EcnLogger.logTaskMessage(
          this._requestContext,
          taskLogger,
          ComputeNodeMessage.ECN_PROVISIONED,
          `The elastic compute node ${this._ecnId} has already been provisioned.`,
          [this._ecnId],
          Severity.WARNING
        );
        await ecnFlowFootPrintService.updatePhaseSnapshot([
          {
            ecnId: this._ecnId,
            taskLogId: taskLogger.getLogId(),
            phase: EcnPhaseSnapshot.ACTIVE,
            status: EcnStatusSnapshot.GREEN,
          },
        ]);
        return { status: Status.COMPLETED };
      }
      /** update yet to be supported
      if (ecnStatus === EcnStatus.UPDATE) {
        if (this.isHCDeploymentNotRequired(ecnStatus)) {
          logWarning(
            `[AddEcnTask]attempt to update ecn ${this._ecnId} with the following sizing plan ${JSON.stringify(ecn)}`,
            { context: this._requestContext }
          );
          await EcnLogger.logTaskMessage(
            this._requestContext,
            taskLogger,
            ComputeNodeMessage.ECN_UPDATE_SIZING_PLAN,
            `The operation is not allowed, please stop the elastic compute node ${this._ecnId} and restart it to update the sizing plan.`,
            [this._ecnId],
            Severity.WARNING
          );
          await ecnFlowFootPrintService.updatePhaseSnapshot([
            {
              ecnId: this._ecnId,
              taskLogId: taskLogger.getLogId(),
              phase: EcnPhaseSnapshot.ACTIVE,
              status: EcnStatusSnapshot.RED,
            },
          ]);
          return { status: Status.FAILED };
        }
      } */
      return Status.RUNNING;
    } catch (error) {
      logError(["EcnStartTask", error], { context: this._requestContext });
      if (error instanceof EcnManagementError && !!error?.customizedErrorCode) {
        await EcnLogger.logTaskMessage(
          this._requestContext,
          taskLogger,
          error.customizedErrorCode,
          error.message,
          [],
          Severity.ERROR
        );
      }
      await EcnLogger.logTaskMessage(
        this._requestContext,
        taskLogger,
        ComputeNodeMessage.ECN_ADD_FAILED,
        `Process to add the elastic compute node ${this._ecnId} failed.`,
        [this._ecnId],
        Severity.ERROR,
        error.message
      );
      if (dbClient) {
        if (ecnFlowFootPrintService) {
          await ecnFlowFootPrintService.updatePhaseSnapshot(
            [
              {
                ecnId: this._ecnId,
                taskLogId: taskLogger.getLogId(),
                phase,
                status: EcnStatusSnapshot.RED,
              },
            ],
            false
          );
        }
      }
      return { status: Status.FAILED };
    } finally {
      await dbClient?.close();
    }
  }

  private async getEcnSizingPlan(dbClient: DbClient): Promise<IElasticComputeNode | undefined> {
    try {
      const sqlTemaplte = sql`
      SELECT
        DISTINCT "VCPU",
        "MEMORY",
        "STORAGE"
      FROM
        ${sql.quoted(CustomerHana.tenantOwnerSchema)}.${sql.quoted(ECN_GET_METADATA_FUNCTION)}()
        WHERE "ECN_ID" = ${this._ecnId};`;
      const result = await dbClient.exec<{
        VCPU: number;
        MEMORY: number;
        STORAGE: number;
      }>(sqlTemaplte);
      logWarning(`getEcnSizingPlan: fetched values are ${JSON.stringify(result)}.`, { context: this._requestContext });
      return result.map(
        (m) =>
          ({
            name: this._ecnId,
            vcpu: Number(m.VCPU),
            memory: Number(m.MEMORY),
            storage: Number(m.STORAGE),
          } as IElasticComputeNode)
      )[0];
    } catch (error) {
      logError(["getEcnSizingPlan", error], { context: this._requestContext });
    }
  }

  private isIElasticComputeNode = (value: any): value is IElasticComputeNode =>
    !!(
      !!value?.vcpu &&
      !!value?.memory &&
      !!value?.storage &&
      !isNaN(value.vcpu) &&
      !isNaN(value.memory) &&
      !isNaN(value.storage) &&
      value.vcpu > 0 &&
      value.memory > 0 &&
      value.storage > 0
    );
}
