/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { EcnPhaseSnapshot } from "../../../shared/ecn/ecnMetadataTypes";
import { DbClient } from "../../lib/DbClient";
import { AuthType, IRequestContext, Activity as RepoActivity } from "../../repository/security/common/common";
import { RequestContext } from "../../repository/security/requestContext";
import { CustomerHana } from "../../reuseComponents/spaces/src/CustomerHana";
import { createNewTechnicalAdminContext, getEcnConsumptionDetails } from "../../routes/ecn/util/utils";
import { ParallelExecutionError } from "../../task/errors/ParallelExecutionError";
import { ITaskLogger, Severity, Status, Substatus } from "../../task/logger/models";
import { FailsafeTaskLogger } from "../../task/logger/services/logger/FailsafeTaskLogger";
import { Activity, ApplicationId, IExecutable, Parameters, TaskIdentifier } from "../../task/models";
import { ScheduleType, SchedulingOption } from "../../task/models/ScheduleType";
import { TaskExecuteResponse } from "../../task/models/TaskExecuteResponse";
import { TaskWithParameters } from "../../task/models/TaskIdentifier";
import { ActiveSessionChecker } from "../../task/orchestrator/services/activesession/ActiveSessionChecker";
import { TaskExecutor } from "../../task/orchestrator/services/taskexecution/TaskExecutor";
import { LockIdentifier, LockKey, LockedTask } from "../../task/orchestrator/services/tasklock";
import { OverwriteResponse } from "../../task/orchestrator/services/tasklock/OverwriteResponse";
import { logError } from "../../task/shared/logger/TaskFrameworkLogger";
import { ECN_FLOW_FOOT_PRINT_ERROR_STATUS, EcnFlowFootPrintError } from "../impl/EcnFlowFootPrintError";
import { EcnFlowFootPrintService } from "../impl/EcnFlowFootPrintService";
import { EcnTaskChainCsnBuilder } from "../impl/EcnTaskChainCsnBuilder";
import { ecnChainName, isTaskChainRunning } from "../utils/Helper";
import { EcnLogger, logWarning } from "../utils/LoggerUtils";
import { ComputeNodeMessage, EcnStatusSnapshot, IEcnStatusSnapshotEntry, RunStatus } from "../utils/types";

export class EcnExecutorTask implements IExecutable {
  volatileParameter?: object | undefined;
  constructor(
    protected _requestContext: IRequestContext,
    protected _spaceId: string,
    protected _objectId: string,
    protected _activity: string,
    protected _parameters: Parameters
  ) {}

  async execute(taskLogger: ITaskLogger): Promise<TaskExecuteResponse> {
    const taskLogId = taskLogger.getLogId();
    let backgroundContext: IRequestContext | undefined;
    let technicalContext: IRequestContext | undefined;
    let dbClient: DbClient | undefined;
    let ecnFlowFootPrintService: EcnFlowFootPrintService | undefined;
    try {
      if (![Activity.GENERATE_START_CHAIN, Activity.GENERATE_STOP_CHAIN].includes(this._activity as Activity)) {
        throw new Error(`Unsupported task activity ${this._activity}.`);
      }
      technicalContext = createNewTechnicalAdminContext(this._requestContext);
      backgroundContext = RequestContext.createNewForBackground(this._requestContext);
      if (this._activity === Activity.GENERATE_START_CHAIN) {
        const ecnConsumptionDetails = await getEcnConsumptionDetails(backgroundContext);
        if (ecnConsumptionDetails?.limitUsage === undefined || ecnConsumptionDetails?.limitUsage === true) {
          logError(
            "Cannot start an elastic compute node as the usage limit has been reached or as no compute block-hours have been allocated yet.",
            { context: backgroundContext }
          );
          await EcnLogger.logTaskMessage(
            this._requestContext,
            taskLogger,
            ComputeNodeMessage.ECN_LIMITING_USAGE,
            "Cannot start an elastic compute node as the usage limit has been reached or as no compute block-hours have been allocated yet.",
            [],
            Severity.ERROR
          );
          return { status: Status.FAILED };
        }
      }
      await this.saveAutomatedBy(backgroundContext, taskLogger);
      const customerHana = await CustomerHana.fromRequestContext(backgroundContext);
      dbClient = await customerHana.getTenantManagerClient();
      const ecnFlowFootPrintService = new EcnFlowFootPrintService(backgroundContext, dbClient);
      await ecnFlowFootPrintService.setStatusRun(RunStatus.INITIAL, taskLogId);
      const currentSnapshot = (await ecnFlowFootPrintService.getSnapshot([
        this._objectId,
      ])) as IEcnStatusSnapshotEntry[];
      if (await this.canLockRun(currentSnapshot, backgroundContext)) {
        const runningChainLogId = currentSnapshot[0]?.chainLogId || "";
        await EcnLogger.logTaskMessage(
          this._requestContext,
          taskLogger,
          ComputeNodeMessage.ECN_LOCKED_CHAIN_RUN,
          `Task chain generation for the elastic compute node ${this._objectId} is locked hence the chain ${runningChainLogId} might still running.`,
          [this._objectId, runningChainLogId.toString()],
          Severity.WARNING
        );
        return { status: Status.FAILED, subStatusCode: Substatus.LOCKED };
      }
      const currentPhase = currentSnapshot?.[0]?.phase ?? undefined;
      let phase =
        (currentPhase as EcnPhaseSnapshot | undefined) === EcnPhaseSnapshot.ACTIVE
          ? currentPhase
          : (this._activity as Activity) === Activity.GENERATE_START_CHAIN
          ? EcnPhaseSnapshot.INITIAL
          : EcnPhaseSnapshot.ACTIVE; // TODO: check if ecn also already exist in db
      await ecnFlowFootPrintService.updatePhaseSnapshot([
        {
          ecnId: this._objectId,
          phase,
          status: EcnStatusSnapshot.GREEN,
          taskLogId,
          chainLogId: 0, // chain is yet to be triggered
        },
      ]);
      // get the data using the technical context
      const taskChainMetadata = await new EcnTaskChainCsnBuilder(
        backgroundContext,
        this._objectId,
        this._activity
      ).build();
      const taskIdentifier: TaskIdentifier = {
        applicationId: ApplicationId.TASK_CHAINS,
        activity: Activity.RUN_CHAIN_TECHNICAL,
        objectId: ecnChainName(
          this._objectId,
          this._activity as Activity.GENERATE_START_CHAIN | Activity.GENERATE_STOP_CHAIN
        ),
        spaceId: this._spaceId,
      };
      // notify = false, omit the notification
      const taskExecutor = await TaskExecutor.createDirectWithTaskIdentifier(
        technicalContext,
        taskIdentifier,
        undefined,
        false
      );

      const chainLogId = await taskExecutor.trigger(taskChainMetadata);
      phase =
        (this._activity as Activity) === Activity.GENERATE_STOP_CHAIN
          ? EcnPhaseSnapshot.STOPPING
          : (phase as EcnPhaseSnapshot) === EcnPhaseSnapshot.ACTIVE
          ? phase
          : EcnPhaseSnapshot.STARTING;
      await ecnFlowFootPrintService.updatePhaseSnapshot(
        [
          {
            ecnId: this._objectId,
            phase,
            status: EcnStatusSnapshot.YELLOW,
            taskLogId,
            chainLogId,
          },
        ],
        true,
        ECN_FLOW_FOOT_PRINT_ERROR_STATUS.CODE_CLIMB
      );
      logWarning(`[EcnExecutor][${this._activity}] ${chainLogId} triggered for ${this._objectId}`, {
        context: this._requestContext,
      });
      await EcnLogger.logTaskMessage(
        this._requestContext,
        taskLogger,
        ComputeNodeMessage.TRIGGERED_TASK_CHAIN_LOG_ID,
        `Task chain ${chainLogId} to the corresponding elastic compute node ${this._objectId} has been triggered.`,
        [chainLogId.toString(), this._objectId]
      );
      await ecnFlowFootPrintService?.setStatusRun(RunStatus.SUCCESS, taskLogId);
      return { status: Status.COMPLETED };
    } catch (error) {
      if (dbClient && ecnFlowFootPrintService) {
        await ecnFlowFootPrintService?.setStatusRun(
          RunStatus.ERROR,
          taskLogId,
          ECN_FLOW_FOOT_PRINT_ERROR_STATUS.CODE_CLIMB
        );
      }
      logError(error, { context: this._requestContext });
      await EcnLogger.logTaskMessage(
        this._requestContext,
        taskLogger,
        ComputeNodeMessage.GENERATE_TASK_CHAIN_FAILED,
        `Generate task chain for the elastic compute node ${this._objectId} failed.`,
        [this._objectId],
        Severity.ERROR,
        error.message
      );
      if (
        error instanceof EcnFlowFootPrintError &&
        !!error?.status &&
        error?.status === ECN_FLOW_FOOT_PRINT_ERROR_STATUS.CODE_CLIMB
      ) {
        return { status: Status.RUNNING };
      } else if (error instanceof ParallelExecutionError && error.code === "taskAlreadyRunning") {
        logError(`Chain trigger of ${error.lockedLogId} is locked by the run ${error.runningLogId}.`, {
          context: this._requestContext,
        });
        return { status: Status.FAILED, subStatusCode: Substatus.LOCKED };
      }
      return { status: Status.FAILED };
    } finally {
      await dbClient?.close();
      await backgroundContext?.finish();
      await technicalContext?.finish();
    }
  }

  async isAuthorized(taskLogger: ITaskLogger, isDesignTime?: boolean | undefined): Promise<boolean> {
    return !!(
      this._requestContext.hasPrivilegeOnType(AuthType.SYSTEMINFO, RepoActivity.read) &&
      this._requestContext.hasPrivilegeOnType(AuthType.SYSTEMINFO, RepoActivity.update)
    );
  }

  getLockIdentifier(): LockIdentifier | null {
    return {
      lockKey: LockKey.EXECUTE,
      applicationId: ApplicationId.ELASTIC_COMPUTE_NODE,
      objectId: this._objectId,
      spaceId: this._spaceId,
    };
  }

  async overwriteLock(lock: LockedTask): Promise<OverwriteResponse> {
    let response: OverwriteResponse = {
      takeover: false,
    };
    const SessionChecker = new ActiveSessionChecker(this._requestContext);
    const sessionResult = await SessionChecker.checkIfOverwritePossible(lock);
    response = sessionResult.overwriteResponse;
    return response;
  }

  static twinScheduling(task: TaskIdentifier): SchedulingOption {
    const subordinate: TaskWithParameters = {
      spaceId: task.spaceId,
      objectId: task.objectId,
      applicationId: ApplicationId.ELASTIC_COMPUTE_NODE,
      activity: Activity.GENERATE_STOP_CHAIN,
      parameters: {},
    };
    return { scheduleType: ScheduleType.TWIN, subordinate };
  }

  private isFlowRunning(phase: EcnPhaseSnapshot): boolean {
    // TODO: check if this needed?!
    switch (phase) {
      case EcnPhaseSnapshot.STARTING:
      case EcnPhaseSnapshot.STOPPING:
      case EcnPhaseSnapshot.ACTIVE:
      case EcnPhaseSnapshot.INACTIVE:
      case EcnPhaseSnapshot.INITIAL:
        return true;
      default:
        return false;
    }
  }

  private async canLockRun(currentSnapshot: IEcnStatusSnapshotEntry[], backgroundContext: IRequestContext) {
    return (
      !!currentSnapshot.length &&
      !!currentSnapshot[0]?.phase &&
      !!this.isFlowRunning(currentSnapshot[0]?.phase as EcnPhaseSnapshot) &&
      !!currentSnapshot[0]?.chainLogId &&
      (await isTaskChainRunning(backgroundContext, this._objectId, currentSnapshot[0]?.chainLogId))
    );
  }

  // Save the schedule initiator log id as external instance id for the automated task
  private async saveAutomatedBy(context: IRequestContext, taskLogger: ITaskLogger) {
    const failSafeTaskLogger = new FailsafeTaskLogger(context, taskLogger);
    if ((this._activity as Activity) === Activity.GENERATE_STOP_CHAIN && !!this._parameters?.tf?.chain?.externalId) {
      await failSafeTaskLogger.updateExternalInstanceId(this._parameters?.tf?.chain?.externalId);
    }
  }
}
