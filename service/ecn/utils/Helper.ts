/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { sql } from "@sap/prom-hana-client";
import { ReservedEntitySuffixes } from "@sap/seal-interfaces";
import _ from "lodash";
import { RESULT_TABLE_SUFFIX } from "../../intelligentlookup/utils/commonUtil";
import { DbClient } from "../../lib/DbClient";
import { isMaster, isStarkiller } from "../../lib/node";
import { IRequestContext } from "../../repository/security/common/common";
import { RequestContext } from "../../repository/security/requestContext";
import { CustomerHana } from "../../reuseComponents/spaces/src";
import { TaskLogsApi } from "../../task/logger/controllers/TaskLogsApi";
import { Status, Substatus } from "../../task/logger/models";
import { Activity, ApplicationId } from "../../task/models";
import { ObservedTask, ObserverResponse } from "../../task/observer/Observer";
import {
  ActiveSessionChecker,
  CheckResponse,
} from "../../task/orchestrator/services/activesession/ActiveSessionChecker";
import { TaskFrameworkConstants } from "../../task/shared/db/TaskFrameworkConstants";
import { EcnFlowFootPrintService } from "../impl/EcnFlowFootPrintService";
import { EcnManagementError } from "../impl/EcnManagementError";
import { EcnManagementService } from "../impl/EcnManagementService";
import { logError, logPerformance } from "./LoggerUtils";
import {
  ECN_START_SUFFIX,
  ECN_STOP_SUFFIX,
  EcnTasksParameters,
  IEcnObservedTask,
  MAX_ELASTIC_COMPUTE_NODE_DEV,
  MAX_ELASTIC_COMPUTE_NODE_PRODUCTIVE,
  ObservedTaskPreview,
  TASK_WRAPPER_PROCEDURE,
} from "./types";

export async function executeProcedureWithTaskWrapper(
  context: IRequestContext,
  dbClient: DbClient,
  procedureName: string,
  procedureParameters: any,
  taskLogId: number
): Promise<void> {
  try {
    const statement = await dbClient.prepare(
      sql`CALL ${sql.quoted(CustomerHana.tenantOwnerSchema, TASK_WRAPPER_PROCEDURE)}( ?, ?, ?);`
    );
    await statement.exec([procedureName, JSON.stringify(procedureParameters), taskLogId]);
  } catch (error) {
    logError(["[executeProcedureWithTaskWrapper]", error], { context });
    throw new EcnManagementError(error.message);
  }
}

export async function isSessionErroneous(context: IRequestContext, taskLogId: number): Promise<boolean> {
  const status =
    (await new ActiveSessionChecker(context)?.getConnectionDetails(taskLogId))?.APPLICATION_STATUS || "ERROR";
  return status !== "SUCCESS";
}

export function validator<T>(value: T, label: string): T {
  if (!value) {
    throw new Error(` Parameter ${label} is not provided!`);
  }
  return value;
}

export async function rectifyStatus(context: IRequestContext, runningTasks: ObservedTask[]) {
  let observerResponses: ObserverResponse[] = [];
  try {
    const checker = new ActiveSessionChecker(context);
    const checkedNonRunningTasks: CheckResponse[] = await checker.check(
      runningTasks.map((t) => ({
        logId: t.logId,
        creationTime: t.creationTime,
      }))
    );
    /**  .filter(
      t => t.status !== Status.RUNNING
    ); */
    if (!checkedNonRunningTasks.length) {
      return observerResponses;
    }
    observerResponses = _.reduce(
      runningTasks,
      (r: ObserverResponse[], task) => {
        const checkedTask = _.find(checkedNonRunningTasks, (f) => f.logId === task.logId);
        if (checkedTask) {
          r.push({
            subStatusCode: checkedTask.aborted ? Substatus.STOPPED : undefined,
            status: checkedTask.status,
            endTime: new Date(),
            ...task,
          });
        }
        return r;
      },
      []
    );
    return observerResponses;
  } catch (error) {
    throw error;
  }
}

export function mapNameToApplicationId(
  name: string
): ApplicationId.LOCAL_TABLE | ApplicationId.VIEWS | ApplicationId.INTELLIGENT_LOOKUP {
  if (
    name.endsWith(`${ReservedEntitySuffixes.View_Materialization_PersistencyTable_1}`) ||
    name.endsWith(`${ReservedEntitySuffixes.View_Materialization_PersistencyTable_2}`)
  ) {
    return ApplicationId.VIEWS;
  } else if (name.endsWith(`${RESULT_TABLE_SUFFIX}`)) {
    return ApplicationId.INTELLIGENT_LOOKUP;
  } else {
    return ApplicationId.LOCAL_TABLE;
  }
}

// Check if there is any chain related to ECN is running
export async function isTaskChainRunning(
  context: IRequestContext,
  ecnId: string,
  chainLogId?: number
): Promise<boolean> {
  let backgroundContext: IRequestContext | undefined;
  let isRunning = false;
  try {
    if (chainLogId === undefined) {
      return false;
    }
    backgroundContext = RequestContext.createNewForBackground(context);
    const taskLogsApi = new TaskLogsApi(backgroundContext, TaskFrameworkConstants.ECN_SPACE);
    const filter = {
      activity: Activity.RUN_CHAIN_TECHNICAL,
      applicationId: ApplicationId.TASK_CHAINS,
    };
    const filters = [
      { ...filter, logId: chainLogId },
      { ...filter, objectId: ecnId + ECN_START_SUFFIX },
      { ...filter, objectId: ecnId + ECN_STOP_SUFFIX },
    ];
    for (const f of filters) {
      const status = (await taskLogsApi.getLatestLogHeaders(f)).logs?.[0]?.status ?? undefined;
      if (!!status && status === Status.RUNNING) {
        isRunning = true;
        break;
      }
    }
    return isRunning;
  } catch (error) {
    logError(error, { context });
    return isRunning;
  } finally {
    await backgroundContext?.finish();
  }
}

export async function getRunningLogId(
  context: IRequestContext,
  ecnId: string,
  chainLogId: number | undefined
): Promise<number | undefined> {
  let backgroundContext: IRequestContext | undefined;
  let runningLogId;
  try {
    if (chainLogId === undefined) {
      return undefined;
    }
    backgroundContext = RequestContext.createNewForBackground(context);
    const taskLogsApi = new TaskLogsApi(backgroundContext, TaskFrameworkConstants.ECN_SPACE);
    const filter = {
      activity: Activity.RUN_CHAIN_TECHNICAL,
      applicationId: ApplicationId.TASK_CHAINS,
    };
    const filters = [
      { ...filter, logId: chainLogId },
      { ...filter, objectId: ecnId + ECN_START_SUFFIX },
      { ...filter, objectId: ecnId + ECN_STOP_SUFFIX },
    ];
    for (const f of filters) {
      const log = (await taskLogsApi.getLatestLogHeaders(f)).logs?.[0] ?? undefined;
      if (!!log?.status && log.status === Status.RUNNING) {
        runningLogId = (log?.logId as any) ?? undefined;
        break;
      }
    }
    return runningLogId;
  } catch (error) {
    logError(error, { context });
    return runningLogId;
  } finally {
    await backgroundContext?.finish();
  }
}

export async function imprintEcnFlowFootPrintViaObservedTasks(
  context: IRequestContext,
  tasks: ObservedTaskPreview[]
): Promise<void> {
  let dbClient: DbClient | undefined;
  let backgroundContext: RequestContext | undefined;
  const startTime = logPerformance(new Date(), `[imprintEcnFlowFootPrintViaObservedTasks]start updating flow print.`, {
    context,
  });
  if (!tasks.length) {
    return;
  }
  try {
    backgroundContext = RequestContext.createNewForBackground(context);
    const customerHana = await CustomerHana.fromRequestContext(backgroundContext);
    dbClient = await customerHana.getTenantManagerClient();
    const ecnFlowFootPrintService = new EcnFlowFootPrintService(backgroundContext, dbClient);
    await ecnFlowFootPrintService.updateStatusSnapshot(
      tasks
        .filter((t) => t.status === Status.FAILED && t.activity === Activity.DROP_REPLICA)
        .map(
          (o) =>
            ({
              ecnId: o.parameters?.[EcnTasksParameters.ECN_ID],
              logId: o.logId,
              activity: o.activity,
              status: o.status,
            } as IEcnObservedTask)
        )
        .filter(unique)
    );
  } catch (error) {
    // swallow the error
    logError(["imprintEcnFlowFootPrintViaObservedTasks", error], { context: backgroundContext });
  } finally {
    await dbClient?.close();
    await backgroundContext?.finish();
    logPerformance(startTime, `[imprintEcnFlowFootPrintViaObservedTasks]end updating flow print.`, { context });
  }
}

export const operationText = <T>(op: T) => (Boolean(op) ? "enabling[X]/disabling[]" : "enabling[]/disabling[X]");

export const ecnChainName = (ecnId: string, activity: Activity.GENERATE_START_CHAIN | Activity.GENERATE_STOP_CHAIN) =>
  activity === Activity.GENERATE_START_CHAIN ? `${ecnId}${ECN_START_SUFFIX}` : `${ecnId}${ECN_STOP_SUFFIX}`;

export const isUserTechnical = (context: IRequestContext) => !!context?.userInfo?.technical;

export async function isEcnProvisioned(context: IRequestContext, ecnId: string): Promise<boolean> {
  try {
    const ecnIds = await (await EcnManagementService.fromRequestContext(context)).getEcnsIdsFromDb();
    return ecnIds?.includes(ecnId) ?? false;
  } catch (error) {
    logError(error, { context });
    return false;
  }
}
// guarantee the uniqueness based on ecnId
export const unique = <T extends { ecnId: string }>(value: T, index: number, selfArray: T[]): boolean =>
  selfArray.findIndex((entry) => entry?.ecnId === value?.ecnId) === index;

export const MaxEcnInstances = (): number =>
  isStarkiller() || isMaster() ? MAX_ELASTIC_COMPUTE_NODE_DEV : MAX_ELASTIC_COMPUTE_NODE_PRODUCTIVE;
