/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { IECNMetadataSpaceAsgmtStrtg } from "../../../shared/ecn/ecnMetadataTypes";
import { PerformanceClass } from "../../../shared/provisioning/ftc/types";
import { IRequestContext } from "../../repository/security/common/common";
import { DesignModel } from "../../task/chains/models";
import { ITaskLogger, Status } from "../../task/logger/models";
import { Activity, ApplicationId } from "../../task/models";
import { ObserverResponse } from "../../task/observer/Observer";

export enum ComputeNodeMessage {
  MESSAGE_BUNDLE_ID = "sap.dwc.elasticComputeNode",
  ECN_NOT_CONFIGURED = "ECN_NOT_CONFIGURED",
  ECN_ADD_FAILED = "ECN_ADD_FAILED",
  ECN_PROVISIONING_SIZING_PLAN = "ECN_PROVISIONING_SIZING_PLAN",
  ECN_REMOVE_FAILED = "ECN_REMOVE_FAILED",
  ECN_PROVISIONED = "ECN_PROVISIONED",
  ECN_DEPROVISIONED = "ECN_DEPROVISIONED",
  ECN_UPDATE_SIZING_PLAN = "ECN_UPDATE_SIZING_PLAN",
  REMOVE_REPLICA_FROM_ECN_STARTED = "REMOVE_REPLICA_FROM_ECN_STARTED",
  ROUTING_TO_ECN_STARTED = "ROUTING_TO_ECN_STARTED",
  ROUTING_TO_ECN_FAILED = "ROUTING_TO_ECN_FAILED",
  REROUTING_TO_COORDINATOR_STARTED = "REROUTING_TO_COORDINATOR_STARTED",
  REROUTING_TO_COORDINATOR_FAILED = "REROUTING_TO_COORDINATOR_FAILED",
  TRIGGERED_TASK_CHAIN_LOG_ID = "TRIGGERED_TASK_CHAIN_LOG_ID",
  GENERATE_TASK_CHAIN_FAILED = "GENERATE_TASK_CHAIN_FAILED",
  REPLICATE_PARENT_CONSUMPTION_VIEW_PATH = "REPLICATE_PARENT_CONSUMPTION_VIEW_PATH",
  REPLICATE_TO_ECN_STARTED = "REPLICATE_TO_ECN_STARTED",
  REPLICATE_TO_ECN_FAILED = "REPLICATE_TO_ECN_FAILED",
  REMOVE_REPLICA_FROM_ECN_FAILED = "REMOVE_REPLICA_FROM_ECN_FAILED",
  ECN_OBSERVER_POLLING = "ECN_OBSERVER_POLLING",
  ECN_PROVISIONING_TIME_OUT = "ECN_PROVISIONING_TIME_OUT",
  ECN_LOCKED_CHAIN_RUN = "ECN_LOCKED_CHAIN_RUN",
  ECN_ROW_STORE_REPLICATION = "ECN_ROW_STORE_REPLICATION",
  ECN_INSTANCES_MAX = "ECN_INSTANCES_MAX",
  ECN_UPDATE_IN_PROGRESS = "ECN_UPDATE_IN_PROGRESS",
  ECN_CREATE_REPLICA_STOPPED = "ECN_CREATE_REPLICA_STOPPED",
  ECN_DROP_REPLICA_STOPPED = "ECN_DROP_REPLICA_STOPPED",
  ECN_LIMITING_USAGE = "ECN_LIMITING_USAGE",
}

export enum EcnReplicationOperation {
  ENABLE = 1,
  DISABLE = 0,
}

export enum RoutingOperation {
  TO_COMPUTE_SERVER = 1,
  TO_COORDINATOR = 0,
}

export enum EcnTasksParameters {
  ECN_ID = "e",
  VCPU = "c",
  STORAGE = "s",
  MEMORY = "m",
  SCHEMA_NAME = "ss",
  PARENT_VIEW_NAME = "v",
  PARENT_VIEW_SCHEMA_NAME = "vss",
  TABLE_NAME = "t",
  LAST_ROUTED_SPACE = "l",
  TABLE_TYPE = "g",
  TABLE_DISK_SIZE_GB = "d",
}

export enum EcnTasksParametersLabels {
  ECN_ID = "elastic compute node identifier",
  // start of the sizing plan of the elastic compute node
  VCPU = "number of vCPUs",
  STORAGE = "storage size in GiB",
  MEMORY = "memory size in GiB",
  // end of the sizing plan of the elastic compute node
  SCHEMA_NAME = "schema name",
  PARENT_VIEW_NAME = "analytic parent view name",
  TABLE_NAME = "table technical name",
  LAST_ROUTED_SPACE = "final routed space",
}

export enum ApplicationStatus {
  SUCCESS = "SUCCESS",
  INITIAL = "INITIAL",
  ERROR = "ERROR",
}

export enum EcnStatus {
  SETUP = "SETUP", // deployment
  UPDATE = "UPDATE", // sizing plan changed
  DEPLOYED = "DEPLOYED", // not update is needed
  UNDEPLOYED = "UNDEPLOYED", // already removed
}

export enum RunStatus {
  INITIAL = "INITIAL",
  SUCCESS = "SUCCESS",
  ERROR = "ERROR",
}

export enum EcnStatusSnapshot {
  YELLOW = "Y", // flow is still in running state
  GREEN = "G", // flow current run is completed
  RED = "R", // flow current run ended with errors
}

export enum TableType {
  COLUMN = "c",
  ROW = "r",
  VIRTUAL = "v",
}

export enum EcnMetrics {
  DS_COMPUTE_VCPU_HOURS_ECN = "DS_COMPUTE_VCPU_HOURS_ECN",
  DS_COMPUTE_MEMORY_GIB_HOURS_ECN = "DS_COMPUTE_MEMORY_GIB_HOURS_ECN",
  DS_STORAGE_GIB_HOURS_ECN = "DS_STORAGE_GIB_HOURS_ECN",
  DS_ELASTIC_COMPUTE_BLOCK_HOURS_ECN = "DS_ELASTIC_COMPUTE_BLOCK_HOURS_ECN",
  DS_ELASTIC_COMPUTE_PERFORMANCE_CLASS = "DS_ELASTIC_COMPUTE_PERFORMANCE_CLASS",
}

export type ObservedTaskPreview = Pick<ObserverResponse, "parameters" | "logId" | "activity" | "status">;

export interface IEcnManageProcedureRequiredParams {
  ecnId: string;
  spaceId: string;
  isEnable: boolean;
}

export interface ITableFullPath {
  schemaName: string;
  tableName: string;
  spaceId?: string; // TODO: use spaceId instead once technical task chain are supported
}

export interface IEcnManageTableProcedureParams extends IEcnManageProcedureRequiredParams {
  tables: ITableFullPath[];
}

export interface IComputeNodeContext {
  context: IRequestContext;
  taskLogger?: ITaskLogger;
}

export interface ITableExtendedFullPath {
  TABLE_NAME: string;
  SPACE_ID: string;
  SCHEMA_NAME: string;
}

export interface IDependencyDetails extends ITableExtendedFullPath {
  TABLE_TYPE: "COLUMN" | "ROW";
  LOAD_UNIT: string;
  EXPOSED_OBJECT_NAME: string;
  EXPOSED_SCHEMA_NAME: string;
  EXPOSED_OBJECT_TYPE: string;
  SCHEMA_TYPE: string;
  DISK_SIZE_GIB: number;
}

export interface ITableLocation {
  spaceId: string;
  tableName: string;
}

export interface IComputeNodeContext {
  context: IRequestContext;
  taskLogger?: ITaskLogger;
}

export interface IEcnStatusSnapshotEntry {
  ecnId: string;
  status: string;
  phase: string;
  taskLogId: number;
  chainLogId?: number | null;
  changedAt?: Date;
}

export interface IEcnStatusSnapshot {
  ECN_ID: string;
  STATUS: string;
  PHASE: string;
  TASK_LOG_ID: number;
  CHANGED_AT: Date;
  CHAIN_LOG_ID: number | null;
}

export interface IEcnManageStatusParams {
  // '{"isEnable": true, "values": [{ "ecnId": "demo", "status": "R", "phase": "STARTING", "taskLogId": 1234, "chainLogId": 2345 }]}'
  isEnable: boolean;
  values: IEcnStatusSnapshotEntry[];
}

export interface IEcnObservedTask {
  ecnId: string;
  logId: number;
  activity: Activity;
  status: Status.COMPLETED | Status.FAILED;
  isLastRouted?: boolean;
}

export interface IEcnCsn {
  metadata: {
    name: string;
    csn: {
      taskchains: {
        [key: string]: DesignModel;
      };
    };
  };
}

export interface IEcnSizingPlan {
  [EcnTasksParameters.VCPU]: number;
  [EcnTasksParameters.MEMORY]: number;
  [EcnTasksParameters.STORAGE]: number;
}

export interface IEcnAssignedSpace {
  spaceId: string;
  assignmentStrategy: IECNMetadataSpaceAsgmtStrtg;
}

export interface IMeteredEcn {
  ecnId: string;
  activity: Activity.ADD | Activity.REMOVE;
  taskLogId: number;
}

export interface IEcnMeteringResponse {
  succeeded: string[];
  rejected: string[];
}

interface IEcnSizingPlanEntry {
  VCPU: number;
  MEMORY: number;
  STORAGE: number;
}
export interface IEcnCapacityEntry extends IEcnSizingPlanEntry {
  HOST: string;
  ECN_ID: string;
}
export interface IEcnMeteringSnapshotEntry extends IEcnCapacityEntry {
  METERING_START_TIME: string;
  START_TASK_LOG_ID: number;
  METERING_END_TIME: string | null;
  END_TASK_LOG_ID: number | null;
  CREATED_AT: string;
  IS_VALID: boolean;
}

export interface IEcnMeteringSnapshot {
  host: string;
  ecnId: string;
  meteringStartTime: Date;
  startTaskLogId: number;
  meteringEndTime: Date | null;
  endTaskLogId: number | null;
  vcpu: number;
  memory: number;
  storage: number;
}

export interface IEcnMeteringPeriod {
  startTime: Date;
  endTime: Date;
}

export interface IEcnHourlyConsumption {
  startTime: string;
  endTime: string;
  meters: {
    [key: string]: number;
  };
}
export interface IEcnConsumption {
  ecnId: string;
  consumption: IEcnHourlyConsumption[];
}

/* Local interface for @MeasureUsage */
export interface IEcnMeasureUsage {
  applicationId: ApplicationId.ELASTIC_COMPUTE_NODE;
  spaceName: string;
  objectName: string;
  measurementPeriodStart: string; // use toISOString()
  measurementPeriodEnd: string;
  measures: {
    [key in EcnMetrics]: string | number;
  };
}

export interface IEcnMeasurementPeriod {
  measurementPeriodStart: string;
  measurementPeriodEnd: string;
}
export interface IEcnMonthlyWindow {
  month: number;
  start: string;
  end: string;
}

export interface IEcnMeterMonitorPayload {
  ecnId: string;
  runsCount: number;
  ranges: IEcnMonthlyWindow[];
}

interface IEcnRun {
  host: string;
  ecnId: string;
  start: string;
  end: string;
  duration: number;
  blockHours: number;
}

interface IEcnMonthlyUsage {
  month: number;
  hours: number;
  blockHours: number;
}
export interface IEcnMeterMonitorResponse {
  upTime: number;
  latestRuns: IEcnRun[];
  monthlyBlockHoursUsage: IEcnMonthlyUsage[];
}

/**
 * interface for ECN request
 * example: {
  "name": "ratest4",
  "plan": {
    "vCPUs": 8,
    "memorySizeGiB": 64,
    "storageSizeGiB": 200
  }
}
 */
export interface IEcnApiModel {
  name: string;
  plan: {
    vCPUs: number;
    memorySizeGiB: number;
    storageSizeGiB: number;
  };
}
/**
 * interface for ECN response
 * example: 	{
      "name": "dsratest3",
      "plan": {
        "performanceClass": "",
        "memorySizeGiB": 64,
        "storageSizeGiB": 200,
        "vCPUs": 8
      },
      "status": {
        "ready": true,
        "lastTransitionTimestamp": "2024-04-20T12:49:22Z",
        "lastOperation": {
          "type": "CREATE",
          "state": "COMPLETED",
          "description": "Provisioning completed"
        }
      }
    }
 */
export interface IEcnApiResponse extends IEcnApiModel {
  status: {
    ready: boolean;
    lastTransitionTimestamp: string;
    lastOperation: {
      type: string;
      state: string;
      description: string;
    };
  };
}

export interface IEcnApiResponseError {
  [key: string]: {
    code: string;
    message: string;
    target?: string;
  };
}
export interface IEcnConsumptionDetails {
  limitUsage: boolean;
  remainingComputeBlockHours?: number; // remaining block hours
  performanceClass?: PerformanceClass;
}

// advisor
export interface IEcnAdvisorLocalData {
  startTimeTimestamp: string;
  endTimeTimestamp: string;
  mdsModelName: string;
  mdsModelSchemaName: string;
  dataSources: string;
  executionHost: string;
  databaseUsers: string;
  executionCount: number;
  totalExecutionTime: number;
  totalCpuTime: number;
  maxMemoryUsed: number;
  dependenciesViewsCount: string;
  dependenciesTablesCount: string;
  dependenciesSize: number;
  dependencies: string;
  metedata: string;
}

export interface IEcnAdvisorConfiguration {
  common: {
    enabled: boolean;
    credentialID: string;
  };
  extended: {
    scheduler: {
      enabled: boolean;
      periodMinutes: number;
    };
  };
}

export interface IEcnAdvisorUpdatedConfigRequest {
  enable?: boolean;
  periodMinutes?: number;
}

export interface IEcnAdvisorConfigResponse {
  registered: boolean;
  configuration: IEcnAdvisorConfiguration;
}

export interface IPeriod {
  start: string;
  end: string;
}

export const ECN_START_SUFFIX = "_START";
export const ECN_STOP_SUFFIX = "_STOP";

// db artifacts
export const TASK_WRAPPER_PROCEDURE = "TASK_WRAPPER";
export const ECN_MANAGE_TABLE_PROCEDURE = "ECN_MANAGE_TABLE";
export const ECN_MANAGE_SPACE_PROCEDURE = "ECN_MANAGE_SPACE";
export const ECN_GET_METADATA_FUNCTION = "ECN_GET_METADATA";
export const ECN_GET_OBJECT_DEPENDENCIES_VIEW = "ECN_GET_OBJECT_DEPENDENCIES";
export const ECN_CHECK_TABLE_FUNCTION = "ECN_CHECK_TABLE";
export const ECN_GET_SPACE_METADATA_FUNCTION = "ECN_GET_SPACE_METADATA";
export const ECN_STATUS_SNAPSHOT_TABLE = "ECN_STATUS_SNAPSHOT";
export const ECN_MANAGE_STATUS_SNAPSHOT_PROCEDURE = "ECN_MANAGE_STATUS_SNAPSHOT";
export const ECN_GET_STATUS_SNAPSHOT_FUNCTION = "ECN_GET_STATUS_SNAPSHOT";
export const ECN_SET_RUN_STATUS_PROCEDURE = "ECN_SET_RUN_STATUS";
export const ECN_METERING_SNAPSHOT_TABLE = "ECN_METERING_SNAPSHOT";
export const SET_SPACE_METADATA_PROCEDURE = "SET_SPACE_METADATA";
export const ECN_MDS_ADVISORYDATA_TABLE = "ECN_MDS_ADVISORYDATA";

export const MAX_ELASTIC_COMPUTE_NODE_DEV = 16;
export const MAX_ELASTIC_COMPUTE_NODE_PRODUCTIVE = 4;
