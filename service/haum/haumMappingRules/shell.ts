/** @format */

import { HaumMappingRule } from "../usage-tracking/types";

export const shellMappingRules: HaumMappingRule[] = [
  {
    name: "Count of clicks: App Switcher > Analytics tile",
    feature: "shell",
    action: "productSwitch",
    value1: "sac",
    measurementId: "DWC_FEAT_UI_GI_APP_SWITCHER_SAC",
  },
  {
    name: "Count of clicks: In-App-Help ",
    feature: "shell",
    action: "toggleWebAssistant",
    measurementId: "DWC_FEAT_UI_GI_HELP",
  },
  {
    name: "Count of clicks: About",
    feature: "shell",
    action: "open",
    value1: "about",
    measurementId: "DWC_FEAT_UI_GI_ABOUT",
  },
  {
    name: "Count of clicks: Shell Navigate",
    feature: "shell",
    action: "navigate",
    value1: "import",
    measurementId: "DWC_FEAT_UI_TR_IMPORT",
  },
  {
    name: "Count of clicks: Shell Navigate",
    feature: "shell",
    action: "navigate",
    value1: "export",
    measurementId: "DWC_FEAT_UI_TR_EXPORT",
  },
  {
    name: "Count of clicks: Shell Navigate",
    feature: "shell",
    action: "navigate",
    value1: "activities",
    measurementId: "DWC_FEAT_UI_SE_ACTIVITIES",
  },
  {
    name: "Count of clicks: Shell Navigate",
    feature: "shell",
    action: "navigate",
    value1: "roles",
    measurementId: "DWC_FEAT_UI_SE_ROLES",
  },
  {
    name: "Count of clicks: Shell Navigate",
    feature: "shell",
    action: "navigate",
    value1: "users",
    measurementId: "DWC_FEAT_UI_SE_USERS",
  },
  {
    name: "Count of clicks: Shell Navigate",
    feature: "shell",
    action: "navigate",
    value1: "contentnetwork",
    measurementId: "DWC_FEAT_UI_GI_CONTENT_NETWORK",
  },
  {
    name: "Count of clicks: Shell Navigate",
    feature: "shell",
    action: "navigate",
    value1: "authorizations",
    measurementId: "DWC_FEAT_UI_06_STARTDAC",
  },
  {
    name: "Count of clicks: Catalog Shell Navigate",
    feature: "shell",
    action: "navigate",
    value1: "taghierarchies",
    measurementId: "DI_FEAT_UI_DSPM_1015_Open_Tags Hierarchy",
  },
  {
    name: "Count of clicks: Catalog Shell Navigate",
    feature: "shell",
    action: "navigate",
    value1: "catalogmonitoring",
    measurementId: "DI_FEAT_UI_DSPM_1015_Open_CatalogMonitoring",
  },
  {
    name: "Count of clicks: Shell Navigate",
    feature: "shell",
    action: "navigate",
    value1: "configuration",
    measurementId: "DWC_FEAT_UI_GI_ADMIN",
  },
  {
    name: "Count of clicks: Catalog Shell Navigate",
    feature: "shell",
    action: "navigate",
    value1: "cataloghome",
    measurementId: "DI_FEAT_UI_DSPM_1015_Open_Catalog",
  },
  {
    name: "Count of clicks: Semantic Onboarding Navigate",
    feature: "shell",
    action: "navigate",
    value1: "semantic_onboarding",
    measurementId: "DW_FEAT_UI_DSPM_4250_SEMANTIC_ONBARDING",
  },
  {
    name: "Count of clicks: Short Feedback Link",
    feature: "shell",
    action: "shortFeedbackLink",
    measurementId: "DWC_FEAT_UI_CLICKS_ON_SHORT_SURVEY",
  },
  {
    name: "Count of clicks: Long Survey Link",
    feature: "shell",
    action: "longSurveyLink",
    measurementId: "DWC_FEAT_UI_CLICKS_ON_LONG_SURVEY",
  },
];
