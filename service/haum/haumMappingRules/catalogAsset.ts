/** @format */

import { HaumMappingRule } from "../usage-tracking/types";

export const catalogassetMappingRules: HaumMappingRule[] = [
  {
    name: "Count of clicks: Publish or unpublish catalog asset",
    feature: "catalogAsset",
    action: "publish",
    measurementId: "DI_FEAT_UI_DSPM_1015_CatalogHome_Publish_Unpublish",
  },
  {
    name: "Count of clicks: Publish or unpublish catalog asset",
    feature: "catalogAsset",
    action: "unpublish",
    measurementId: "DI_FEAT_UI_DSPM_1015_CatalogHome_Publish_Unpublish",
  },
  {
    name: "Count of clicks: Open catalog asset in source system",
    feature: "catalogAsset",
    action: "openInSource",
    measurementId: "DI_FEAT_UI_DSPM_1015_Open_AssetInSource",
  },
  {
    name: "Count of clicks: Generate Rich Text Description",
    feature: "catalogAsset",
    action: "generateRichTextDescription",
    measurementId: "DI_FEAT_UI_DSPM_4965_GENERATE_RICH_TEXT_DESCRIPTION",
  },
  {
    name: "Count of clicks: Generate Short Description",
    feature: "catalogAsset",
    action: "generateShortDescription",
    measurementId: "DI_FEAT_UI_DSPM_4965_GENERATE_SHORT_DESCRIPTION",
  },
  {
    name: "Count of clicks: Generate Associate Tags",
    feature: "catalogAsset",
    action: "generateAssociateTags",
    measurementId: "DI_FEAT_UI_DSPM_4965_ASSOCIATE_TAGS",
  },
  {
    name: "Count of clicks: Uninstall Data Product API",
    feature: "catalogAsset",
    action: "uninstallDataProductApi",
    measurementId: "DI_FEAT_UI_DSPM_5532_DATA_PRODUCT_API_ENTITY_UNINSTALL_ACCESS",
  },
];
