/** @format */

import { HaumMappingRule } from "../usage-tracking/types";

export const shellMappingRules: HaumMappingRule[] = [
  {
    name: "Count of clicks: Access asset details page from catalog home",
    feature: "catalogHome",
    action: "openAssetDetails",
    measurementId: "DI_FEAT_UI_DSPM_1015_Open_AssetDetail",
  },
  {
    name: "Count of clicks: Access asset details page from catalog home",
    feature: "catalogHome",
    action: "openTermDetails",
    measurementId: "DI_FEAT_UI_DSPM_1015_Open_TermDetail",
  },
  {
    name: "Count of clicks: Access asset details page from catalog home",
    feature: "catalogHome",
    action: "openKpiDetails",
    measurementId: "DI_FEAT_UI_DSPM_1015_Open_KPIdetail",
  },
  {
    name: "Count of clicks: Access Data Product details page from catalog home",
    feature: "catalogHome",
    action: "openDataProduct",
    measurementId: "DI_FEAT_UI_DSPM_DSPM-4284_CatalogAccess_DataProducts",
  },
  {
    name: "Count of clicks: Access asset details page lineage tab",
    feature: "catalogAsset",
    action: "openLineageTab",
    measurementId: "DI_FEAT_UI_DSPM_1015_Open_AssetDetail_Lineage",
  },
  {
    name: "Count of clicks: Search in catalog home",
    feature: "catalogHome",
    action: (action: string) => action.startsWith("Search UI"),
    measurementId: "DI_FEAT_UI_DSPM_1015_Catalog_Search",
  },
  {
    name: "Count of clicks: Search Data Products in catalog home",
    feature: "catalogHome",
    action: (action: string) => action.startsWith("Search UI"),
    value1: ",SAP_MARKETPLACE_ACCESSIBLEDATAPRODUCTSESH",
    measurementId: "DI_FEAT_UI_DSPM_DSPM-4284_Discovery_MP_ODC",
  },
  {
    name: "Count of clicks: Open Asset Search Tab in catalog home",
    feature: "catalogHome",
    action: "openAssetSearchTab",
    measurementId: "DI_FEAT_UI_DSPM_5108_ASSSET_SEARCH",
  },
  {
    name: "Count of clicks: Open Term Search Tab in catalog home",
    feature: "catalogHome",
    action: "openTermSearchTab",
    measurementId: "DI_FEAT_UI_DSPM_5108_TERM_SEARCH",
  },
  {
    name: "Count of clicks: Open Kpi Search Tab in catalog home",
    feature: "catalogHome",
    action: "openKpiSearchTab",
    measurementId: "DI_FEAT_UI_DSPM_5108_KPI_SEARCH",
  },
  {
    name: "Count of clicks: Open Search Facet",
    feature: "catalogHome",
    action: "openSearchFacet",
    measurementId: "DI_FEAT_UI_DSPM_5108_FACET_PANEL_ACCESS",
  },
  {
    name: "Count of clicks: Oppen Tag Hierarchies",
    feature: "catalogHome",
    action: "navigate",
    value1: "taghierarchies",
    measurementId: "DI_FEAT_UI_DSPM_1015_Open_Tags Hierarchy",
  },
];
