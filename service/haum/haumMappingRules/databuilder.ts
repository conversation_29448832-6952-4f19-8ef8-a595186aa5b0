/** @format */

import { HaumMappingRule } from "../usage-tracking/types";

export const databuilderMappingRules: HaumMappingRule[] = [
  {
    name: "Execution of Data Flows",
    feature: "databuilder",
    action: "execute",
    value1: "DATAFLOWMODELER",
    measurementId: "DWC_FEAT_UI_DI_EXECUTE_FLOWS",
  },
  {
    name: "Count of clicks: Data Builder",
    feature: "databuilder",
    measurementId: "DWC_FEAT_UI_DL_DATA_BUILDER",
  },
  {
    name: "Count of clicks: Data Builder Create",
    feature: "databuilder",
    action: "create",
    value1: "ERMODELER",
    measurementId: "DWC_FEAT_UI_DL_ER_MODELER",
  },
  {
    name: "Count of clicks: Data Builder Create",
    feature: "databuilder",
    action: "create",
    value1: "GRAPHICALVIEWBUILDER",
    measurementId: "DWC_FEAT_UI_DL_GRAPHICAL_VIEW",
  },
  {
    name: "Count of clicks: Data Builder Create",
    feature: "databuilder",
    action: "create",
    value1: "SQLVIEWBUILDER",
    measurementId: "DWC_FEAT_UI_DL_NEW_SQL_VIEW",
  },
  {
    name: "Count of clicks: Data Builder Create",
    feature: "databuilder",
    action: "create",
    value1: "TABLEEDITOR",
    measurementId: "DWC_FEAT_UI_DL_TABLE_EDITOR",
  },
  {
    name: "Count of clicks: Data Builder Create",
    feature: "databuilder",
    action: "create",
    value1: "DATAFLOWMODELER",
    measurementId: "DWC_FEAT_UI_NEW_DL_DATA_FLOW",
  },
  {
    name: "Count of clicks: Create new Data Access Control",
    feature: "databuilder",
    action: "create",
    value1: "DATAACCESSCONTROL",
    measurementId: "DW_FEAT_UI_DAC_DSPM-5362_CREATENEWDAC",
  },
  {
    name: "Count of clicks: Export Data Access Control to CSN/JSON",
    feature: "databuilder",
    action: "exportCSN",
    value1: "DATAACCESSCONTROL",
    measurementId: "DWC_FEAT_UI_DAC_EXPORT2CSN",
  },
  {
    name: "Create Stacked Analytic Model",
    feature: "databuilder",
    action: "analyticModelCreateStackedModel",
    measurementId: "DW_FEAT_UI_DSPM_3609_AM_STACKED_MODEL_CREATED",
  },
  {
    name: "Save Analytic Model",
    feature: "databuilder",
    action: "analyticModelSave",
    measurementId: "DWC_FEAT_UI_DB_AM_SAVE",
  },
  {
    name: "Deploy Analytic Model",
    feature: "databuilder",
    action: "analyticModelDeploy",
    measurementId: "DWC_FEAT_UI_DB_AM_DEPLOY",
  },
  {
    name: "Create new Analytic Model",
    feature: "databuilder",
    action: "analyticModelCreate",
    measurementId: "DWC_FEAT_UI_DB_AM_CREATE",
  },
  {
    name: "Launch preview of Analytic Model",
    feature: "databuilder",
    action: "analyticModelLaunchPreview",
    measurementId: "DWC_FEAT_UI_DB_AM_LAUNCH_PREVIEW",
  },
  {
    name: "Launch model of Analytic Model",
    feature: "databuilder",
    action: "analyticModellaunchModel",
    measurementId: "DWC_FEAT_UI_DB_AM_LAUNCH_MODEL",
  },
  {
    name: "Open Analytic Model on model tab",
    feature: "databuilder",
    action: "analyticModelOpenOnModel",
    measurementId: "DWC_FEAT_UI_DB_AM_OPEN_ON_MODEL_TAB",
  },
  {
    name: "Open Analytic Model on preview tab",
    feature: "databuilder",
    action: "analyticModelOpenOnPreview",
    measurementId: "DWC_FEAT_UI_DB_AM_OPEN_ON_PREVIEW_TAB",
  },
  {
    name: "Count of added associated dimensions",
    feature: "databuilder",
    action: "analyticModelAddAssociatedDimension",
    measurementId: "DWC_FEAT_UI_DB_AM_ASSOCIATED_DIMENSION_ADD",
  },
  {
    name: "Count of deleted associated dimensions",
    feature: "databuilder",
    action: "analyticModelDeleteAssociatedDimension",
    measurementId: "DWC_FEAT_UI_DB_AM_ASSOCIATED_DIMENSION_DELETE",
  },
  {
    name: "Count of added attributes",
    feature: "databuilder",
    action: "analyticModelAddAttribute",
    measurementId: "DWC_FEAT_UI_DB_AM_ATTRIBUTE_ADD",
  },
  {
    name: "Count of clicks on add all attributes",
    feature: "databuilder",
    action: "analyticModelAddAllAttributes",
    measurementId: "DWC_FEAT_UI_DB_AM_ATTRIBUTE_ADD_ALL",
  },
  {
    name: "Count of deleted attributes",
    feature: "databuilder",
    action: "analyticModelDeleteAttribute",
    measurementId: "DWC_FEAT_UI_DB_AM_ATTRIBUTE_DELETE",
  },
  {
    name: "Count of new calculated measures",
    feature: "databuilder",
    action: "analyticModelAddCalculatedMeasure",
    measurementId: "DWC_FEAT_UI_DB_AM_MEASURE_CALC_CREATE",
  },
  {
    name: "Count of deleted calculated measures",
    feature: "databuilder",
    action: "analyticModelDeleteCalculatedMeasure",
    measurementId: "DWC_FEAT_UI_DB_AM_MEASURE_CALC_DELETE",
  },
  {
    name: "Count of new restricted measures",
    feature: "databuilder",
    action: "analyticModelAddRestrictedMeasure",
    measurementId: "DWC_FEAT_UI_DB_AM_MEASURE_RESTRICTED_CREATE",
  },
  {
    name: "Count of deleted restricted measures",
    feature: "databuilder",
    action: "analyticModelDeleteRestrictedMeasure",
    measurementId: "DWC_FEAT_UI_DB_AM_MEASURE_RESTRICTED_DELETE",
  },
  {
    name: "Count of new count distinct measures",
    feature: "databuilder",
    action: "analyticModelAddCountDistinctMeasure",
    measurementId: "DWC_FEAT_UI_DB_AM_MEASURE_COUNT_DISTINCT_CREATE",
  },
  {
    name: "Count of deleted count distinct measures",
    feature: "databuilder",
    action: "analyticModelDeleteCountDistinctMeasure",
    measurementId: "DWC_FEAT_UI_DB_AM_MEASURE_COUNT_DISTINCT_DELETE",
  },
  {
    name: "Count of new currency conversion measures",
    feature: "databuilder",
    action: "analyticModelAddCurrencyConversionMeasure",
    measurementId: "DWC_FEAT_UI_DB_AM_MEASURE_CURRENCY_CONVERSION_CREATE",
  },
  {
    name: "Count of deleted currency conversion measures",
    feature: "databuilder",
    action: "analyticModelDeleteCurrencyConversionMeasure",
    measurementId: "DWC_FEAT_UI_DB_AM_MEASURE_CURRENCY_CONVERSION_DELETE",
  },
  {
    name: "Count of new non cumulative measures",
    feature: "databuilder",
    action: "analyticModelAddNonCumulativeMeasure",
    measurementId: "DWC_FEAT_UI_DB_AM_MEASURE_NON_CUMULATIVE_CREATE",
  },
  {
    name: "Count of deleted non cumulative measures",
    feature: "databuilder",
    action: "analyticModelDeleteNonCumulativeMeasure",
    measurementId: "DWC_FEAT_UI_DB_AM_MEASURE_NON_CUMULATIVE_DELETE",
  },
  {
    name: "Count of added fact source measures",
    feature: "databuilder",
    action: "analyticModelAddFactSourceMeasure",
    measurementId: "DWC_FEAT_UI_DB_AM_MEASURE_FACT_SOURCE_ADD",
  },
  {
    name: "Count of copied measures",
    feature: "databuilder",
    action: "analyticModelCopyMeasure",
    measurementId: "DW_FEAT_UI_DSPM_4749_AM_MEASURE_COPY",
  },
  {
    name: "Count of clicks on add all fact source measures",
    feature: "databuilder",
    action: "analyticModelAddAllFactSourceMeasures",
    measurementId: "DWC_FEAT_UI_DB_AM_MEASURE_FACT_SOURCE_ADD_ALL",
  },
  {
    name: "Count of deleted fact source measures",
    feature: "databuilder",
    action: "analyticModelDeleteFactSourceMeasure",
    measurementId: "DWC_FEAT_UI_DB_AM_MEASURE_FACT_SOURCE_DELETE",
  },
  {
    name: "Count of new story filter variables",
    feature: "databuilder",
    action: "analyticModelAddStoryFilterVariable",
    measurementId: "DWC_FEAT_UI_DB_AM_VARIABLE_FILTER_CREATE",
  },
  {
    name: "Count of deleted story filter variables",
    feature: "databuilder",
    action: "analyticModelDeleteStoryFilterVariable",
    measurementId: "DWC_FEAT_UI_DB_AM_VARIABLE_FILTER_DELETE",
  },
  {
    name: "Count of new reference date variables",
    feature: "databuilder",
    action: "analyticModelAddKeyDateVariable",
    measurementId: "DWC_FEAT_UI_DB_AM_VARIABLE_REFERENCE_DATE_CREATE",
  },
  {
    name: "Count of deleted reference date variables",
    feature: "databuilder",
    action: "analyticModelDeleteKeyDateVariable",
    measurementId: "DWC_FEAT_UI_DB_AM_VARIABLE_REFERENCE_DATE_DELETE",
  },
  {
    name: "Count of new restricted variables",
    feature: "databuilder",
    action: "analyticModelAddRestrictedVariable",
    measurementId: "DWC_FEAT_UI_DB_AM_VARIABLE_RESTRICTED_CREATE",
  },
  {
    name: "Count of deleted restricted variables",
    feature: "databuilder",
    action: "analyticModelDeleteRestrictedVariable",
    measurementId: "DWC_FEAT_UI_DB_AM_VARIABLE_RESTRICTED_DELETE",
  },
  {
    name: "Count of new source variables",
    feature: "databuilder",
    action: "analyticModelAddSourceVariable",
    measurementId: "DWC_FEAT_UI_DB_AM_VARIABLE_SOURCE_CREATE",
  },
  {
    name: "Count of deleted source variables",
    feature: "databuilder",
    action: "analyticModelDeleteSourceVariable",
    measurementId: "DWC_FEAT_UI_DB_AM_VARIABLE_SOURCE_DELETE",
  },
  {
    name: "Count of new dynamic variables",
    feature: "databuilder",
    action: "analyticModelAddDynamicVariable",
    measurementId: "DWC_FEAT_UI_DSPM_4970_DYNAMIC_VAR_DFLT_CREATE",
  },
  {
    name: "Count of new global filters",
    feature: "databuilder",
    action: "analyticModelAddGlobalFilter",
    measurementId: "DWC_FEAT_UI_DB_AM_FILTER_CREATE",
  },
  {
    name: "Count of deleted global filters",
    feature: "databuilder",
    action: "analyticModelDeleteGlobalFilter",
    measurementId: "DWC_FEAT_UI_DB_AM_FILTER_DELETE",
  },
  {
    name: "Count of new data access controls",
    feature: "databuilder",
    action: "analyticModelAddDataAccessControl",
    measurementId: "DWC_FEAT_UI_DAC_DSPM-3474_ADDDACTOANALYTICMODEL",
  },
  {
    name: "Count of deleted data access controls",
    feature: "databuilder",
    action: "analyticModelDeleteDataAccessControl",
    measurementId: "DWC_FEAT_UI_DAC_DSPM-3474_DELDACFROMANALYTICMODEL",
  },
  {
    name: "Count of value help open clicks",
    feature: "databuilder",
    action: "analyticModelopenValueHelp",
    measurementId: "DWC_FEAT_UI_DB_AM_VALUE_HELP_OPEN",
  },
  {
    name: "Count of replace fact source clicks",
    feature: "databuilder",
    action: "analyticModelReplaceFactSource",
    measurementId: "DW_FEAT_UI_DSPM_4502_AM_REPLACE_FACT_CNT",
  },
  {
    name: "Click on save of odata parameters in Data Flows",
    feature: "databuilder",
    action: "odataParametersSave",
    value1: "DATAFLOWMODELER",
    measurementId: "DWC_FEAT_UI_ODATA_Q_PARAMETER",
  },
  {
    name: "Count on the run of replication flow",
    feature: "databuilder",
    action: "executeReplicationflow",
    value1: "REPLICATIONFLOW_EDITOR",
    measurementId: "DI_FEAT_UI_DSPM_491_REPLICATION_FLOW_RUNS",
  },
  {
    name: "Count of clicks: Switch Collection",
    feature: "databuilder",
    action: "collectionSwitch",
    measurementId: "DWC_FEAT_UI_DB_REPOCTRL_COLLECTION_SWITCH",
  },
  {
    name: "Count of clicks: Switch Result View",
    feature: "databuilder",
    action: "resultViewSwitch",
    measurementId: "DWC_FEAT_UI_DB_REPOCTRL_VIEW_SWITCH",
  },
  {
    name: "Count of clicks: Pagination Button",
    feature: "databuilder",
    action: "showMore",
    measurementId: "DWC_FEAT_UI_DB_REPOCTRL_PAGINATE",
  },
  {
    name: "Count of search with filters",
    feature: "databuilder",
    action: "searchWithFilters",
    measurementId: "DWC_FEAT_UI_DB_REPOCTRL_SEARCH_WITH_FILTERS",
  },
  {
    name: "Count of search with keyword",
    feature: "databuilder",
    action: "searchWithKeyword",
    measurementId: "DWC_FEAT_UI_DB_REPOCTRL_SEARCH_WITH_KEYWORD",
  },
  {
    name: "Count of search with keyword and filter",
    feature: "databuilder",
    action: "searchWithKeywordAndFilter",
    measurementId: "DWC_FEAT_UI_DB_REPOCTRL_SEARCH_WITH_KEYWORD_AND_FILTER",
  },
  {
    name: "Count of search with neither keyword nor filter",
    feature: "databuilder",
    action: "searchOverall",
    measurementId: "DWC_FEAT_UI_DB_REPOCTRL_SEARCH_OVERALL",
  },
  {
    name: "Count of navigating to folder",
    feature: "databuilder",
    action: "folderContentShow",
    measurementId: "DWC_FEAT_UI_DB_REPOCTRL_FOLDER_CONTENT_SHOW",
  },
  {
    name: "Count of open filter dialog",
    feature: "databuilder",
    action: "filterDialogOpen",
    measurementId: "DWC_FEAT_UI_DB_REPOCTRL_FILTER_DIALOG_OPEN",
  },
  {
    name: "Count of add object to favorites",
    feature: "databuilder",
    action: "favoriteAdd",
    measurementId: "DWC_FEAT_UI_DB_REPOCTRL_FAVORITE_ADD",
  },
  {
    name: "Count of clicks: Data Builder Create",
    feature: "databuilder",
    action: "create",
    measurementId: "DWC_FEAT_UI_DB_OBJECT_CREATE_VIA_MENU",
  },
  {
    name: "Count of clicks: Data Builder Create via big button",
    feature: "databuilder",
    action: "create",
    measurementId: "DWC_FEAT_UI_DB_OBJECT_CREATE_VIA_BIG_BUTTON",
  },
  {
    name: "Count of action on multi select objects",
    feature: "databuilder",
    action: "multiSelectAction",
    measurementId: "DWC_FEAT_UI_DB_REPOCTRL_MULTI_SELECT_ACTION",
  },
  {
    name: "Count of open table personalization",
    feature: "databuilder",
    action: "tableConfigOpen",
    measurementId: "DWC_FEAT_UI_DB_REPOCTRL_TABLE_CONFIG_OPEN",
  },
  {
    name: "Model Validation",
    feature: "databuilder",
    action: "modelValidation",
    measurementId: "DWC_FEAT_UI_DB_DL_VALIDATION_START",
  },
  {
    name: "Count of clicks: Import Remote Authorizations in Data Builder",
    feature: "databuilder",
    action: "import",
    value1: "IMPORTREMOTEDACS",
    measurementId: "DW_FEAT_UI_DAC_DSPM-5362_IMPORTREMOTEDAC",
  },
  {
    name: "Count of clicks: Number of Remote DACs Deployment processes started",
    feature: "databuilder",
    action: "deploy",
    value1: "Remote DAC",
    measurementId: "DW_FEAT_UI_DAC_DSPM-5362_REMOTEDACDEPLOY",
  },
  {
    name: "Open DAC lineage execution in Data Builder",
    feature: "databuilder",
    action: "analyze",
    value1: "DWC_DAC",
    measurementId: "DW_FEAT_UI_DAC_DSPM-5362_OPENDACLINEAGE",
  },
  {
    name: "Open DAC lineage execution in Data Access Control",
    feature: "databuilder",
    action: "analyze",
    value1: "DATAACCESSCONTROL",
    measurementId: "DW_FEAT_UI_DAC_DSPM-5362_OPENDACLINEAGE",
  },
  {
    name: "Count of clicks: Open DAC lineage execution in Data Builder",
    feature: "databuilder",
    action: "delete",
    value1: "DWC_DAC",
    measurementId: "DW_FEAT_UI_DAC_DSPM-5362_DELETEDAC",
  },
  {
    name: "Count of clicks: Save a copy of the Data Access Control (SaveAs/Copy)",
    feature: "databuilder",
    action: "copy",
    value1: "DWC_DAC",
    measurementId: "DW_FEAT_UI_DAC_DSPM-5362_SAVEDACCOPY",
  },
  {
    name: "Count of clicks: Deploy the Data Access Control",
    feature: "databuilder",
    action: "deploy",
    value1: "DWC_DAC",
    measurementId: "DW_FEAT_UI_DAC_DSPM-5362_DEPLOYDAC",
  },
  {
    name: "Open OData Builder API Tool",
    feature: "databuilder",
    action: "odataBuilderOpen",
    measurementId: "DWC_FEAT_UI_DB_ODATA_REQUEST_CREATE",
  },
  {
    name: "Launch version list dialog for any objects",
    feature: "databuilder",
    action: "showVersionsDialog",
    measurementId: "DW_FEAT_UI_DSPM_4132_ANYOBJ_OPEN_VERSIONS_LIST",
  },
  {
    name: "Open version for restore (Graphical View)",
    feature: "databuilder",
    action: "openVersionForRestoreGV",
    measurementId: "DW_FEAT_UI_DSPM_4132_GV_OPEN_VERSION_FOR_RESTORE",
  },
  {
    name: "Open version for restore (SQL View)",
    feature: "databuilder",
    action: "openVersionForRestoreSQL",
    measurementId: "DW_FEAT_UI_DSPM_4132_SQLV_OPEN_VERSION_4_RESTORE",
  },
  {
    name: "Download CSN of an object version",
    feature: "databuilder",
    action: "downloadVersionCSN",
    measurementId: "DW_FEAT_UI_DSPM_4132_ANYOBJ_DOWNLOAD_CSN_OF_VERS",
  },
  {
    name: "Open in new tab for TF editor",
    feature: "databuilder",
    action: "openVersionInReadonlyEditorTF",
    measurementId: "DW_FEAT_UI_DSPM_4132_TRSF_OPEN_VERSION_READONLY",
  },
  {
    name: "Open in new tab for AM editor",
    feature: "databuilder",
    action: "openVersionInReadonlyEditorAM",
    measurementId: "DW_FEAT_UI_DSPM_4132_AM_OPEN_VERSION_READONLY",
  },
  {
    name: "Open in new tab for DAC editor",
    feature: "databuilder",
    action: "openVersionInReadonlyEditorDAC",
    measurementId: "DW_FEAT_UI_DSPM_4132_DAC_OPEN_VERSION_READONLY",
  },
  {
    name: "Open in new tab for ER editor",
    feature: "databuilder",
    action: "openVersionInReadonlyEditorER",
    measurementId: "DW_FEAT_UI_DSPM_4132_ER_OPEN_VERSION_READONLY",
  },
  {
    name: "Open in new tab for GV editor",
    feature: "databuilder",
    action: "openVersionInReadonlyEditorGV",
    measurementId: "DW_FEAT_UI_DSPM_4132_GV_OPEN_VERSION_READONLY",
  },
  {
    name: "Open in new tab for repo package editor",
    feature: "databuilder",
    action: "openVersionInReadonlyEditorPCKG",
    measurementId: "DW_FEAT_UI_DSPM_4132_PCKG_OPEN_VERSION_READONLY",
  },
  {
    name: "Open in new tab for replication flow editor",
    feature: "databuilder",
    action: "openVersionInReadonlyEditorREPF",
    measurementId: "DW_FEAT_UI_DSPM_4132_REPF_OPEN_VERSION_READONLY",
  },
  {
    name: "Open in new tab for sql view editor",
    feature: "databuilder",
    action: "openVersionInReadonlyEditorSV",
    measurementId: "DW_FEAT_UI_DSPM_4132_SQLVIEW_OPEN_VERSION_READONLY",
  },
  {
    name: "Open in new tab for table editor",
    feature: "databuilder",
    action: "openVersionInReadonlyEditorTABLE",
    measurementId: "DW_FEAT_UI_DSPM_4132_TABLE_OPEN_VERSION_READONLY",
  },
  {
    name: "Open in new tab for task chain editor",
    feature: "databuilder",
    action: "openVersionInReadonlyEditorTC",
    measurementId: "DW_FEAT_UI_DSPM_4132_TC_OPEN_VERSION_READONLY",
  },
  {
    name: "Open Measure Dependencies",
    feature: "databuilder",
    action: "analyticModelOpenMeasureDependencies",
    measurementId: "DWC_FEAT_UI_DSPM_4317_AM_MEASURE_DEPENDENCIES_SHOW",
  },
  {
    name: "Switch prevent data export on",
    feature: "databuilder",
    action: "analyticModelSwitchOnPreventDataExport",
    measurementId: "DWC_FEAT_UI_DSPM_5247_AM_PREVENT_DATA_EXPORT",
  },
  {
    name: "Create Fiscal Variant Variable",
    feature: "databuilder",
    action: "analyticModelCreateFiscalVariable",
    measurementId: "DWC_FEAT_UI_DSPM_1409_CREATE_FISCAL_VARIABLE",
  },
];
