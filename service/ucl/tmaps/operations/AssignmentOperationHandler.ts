/** @format */
// FILEOWNER: [Provisioning]

import { getLogger } from "../../../logger";
import { UclSystemManagement } from "../../integratedSystems/UclSystemManagement";
import { IIntegratedSystems, integratedSystems } from "../../integratedSystems/integratedSystems";
import { IUclResponseBody, SynchronizationTenantState, UclOperation } from "../../types";
import { assertDspTenantEnabled, logAuditConfiguration } from "../../utils";
import { OperationsHandler } from "./OperationHandler";
import { TMapSError } from "./types";

const { logInfo } = getLogger("TMapS-DSP-AssignmentOperationHandler");

export class AssignmentOperationHandler extends OperationsHandler {
  async getSynchronizationPayload(
    uclIntegratedSystems: IIntegratedSystems = integratedSystems
  ): Promise<IUclResponseBody> {
    try {
      const result = await this.getStatePayload(uclIntegratedSystems);
      await logAuditConfiguration(this.context, result.state, this.uclRequestBody);
      return result;
    } catch (error) {
      const msg = `Operation: ${UclOperation.assign} has failed. Receiver Tenant State: ${this.uclRequestBody.receiverTenant.state}, Formation Tenant State: ${this.uclRequestBody.assignedTenant.state}, Receiver Tenant Application Namespace: ${this.uclRequestBody.receiverTenant.applicationNamespace}. Error: ${error.message}`;

      await logAuditConfiguration(
        this.context,
        SynchronizationTenantState.CREATE_ERROR,
        this.uclRequestBody,
        error ?? undefined
      );
      throw new TMapSError(msg, SynchronizationTenantState.CREATE_ERROR);
    }
  }

  private async getStatePayload(uclIntegratedSystems: IIntegratedSystems = integratedSystems) {
    const dsTenant = this.uclRequestBody.receiverTenant;
    const formationTenant = this.uclRequestBody.assignedTenant;
    switch (dsTenant.state) {
      case SynchronizationTenantState.INITIAL:
        logInfo(`Handling synchronization ${dsTenant.state} state`, { context: this.context });
        return await this.handleSyncStart(uclIntegratedSystems);
      case SynchronizationTenantState.CONFIG_PENDING:
        logInfo(`Handling synchronization ${dsTenant.state} state`, { context: this.context });
        return await this.handleSyncUpdate(uclIntegratedSystems);
      case SynchronizationTenantState.CREATE_ERROR:
        if (formationTenant.configuration) {
          logInfo(`Handling synchronization UPDATE after a ${dsTenant.state} state`, { context: this.context });
          return await this.handleSyncUpdate(uclIntegratedSystems);
        }
        logInfo(`Handling synchronization START after a ${dsTenant.state} state`, { context: this.context });
        return await this.handleSyncStart(uclIntegratedSystems);
      default:
        return this.setUclErrorPayload(
          SynchronizationTenantState.CREATE_ERROR,
          `Unable to find the correct action for DS State: ${dsTenant.state}`
        );
    }
  }

  private async handleSyncStart(
    uclIntegratedSystems: IIntegratedSystems = integratedSystems
  ): Promise<IUclResponseBody> {
    await assertDspTenantEnabled(this.context);

    const configuration = await UclSystemManagement.getSystemConfigurationPayload(
      this.context,
      this.uclRequestBody,
      uclIntegratedSystems
    );
    logInfo(`Synchronization CONFIG_PENDING for ${this.uclRequestBody.assignedTenant.uclSystemName}`, {
      context: this.context,
    });
    return this.getPatchPayload(SynchronizationTenantState.CONFIG_PENDING, configuration);
  }

  private async handleSyncUpdate(
    uclIntegratedSystems: IIntegratedSystems = integratedSystems
  ): Promise<IUclResponseBody> {
    const configuration = await UclSystemManagement.propagateSystemCredentials(
      this.context,
      this.uclRequestBody,
      uclIntegratedSystems
    );

    logInfo(`Synchronization READY for ${this.uclRequestBody.assignedTenant.uclSystemName}`, { context: this.context });
    return this.getPatchPayload(SynchronizationTenantState.READY, configuration);
  }

  private getPatchPayload(state: SynchronizationTenantState, configuration: any): IUclResponseBody {
    const payload = configuration ? { state, configuration } : { state };
    return payload;
  }
}
