/** @format */
import { getSACUrl } from "@sap/deepsea-sac";
import { ETenantConnections, IRequestContext } from "@sap/deepsea-types";
import { AuthenticationMode, httpClient } from "@sap/dwc-http-client";
import { DeploymentState } from "@sap/seal-interfaces";
import { promises as fs } from "fs";
import Status from "http-status-codes";
import path from "path";
import { DbClient } from "../../../lib/DbClient";
import { getLogger } from "../../../logger";
import { getClient } from "../../../meta/access";
import * as provisioningHelper from "../../../provisioning/helpers";
import { ExternalCallCategory } from "../../../repository/security/common/externalCallTypes";
import { getSpaces } from "../../../repository/spaces";
import { SpaceService } from "../../../reuseComponents/spaces/src";
import { CustomerHana } from "../../../reuseComponents/spaces/src/CustomerHana";
import { setLinkTenantUrl } from "../../../routes/sac/tenant";
import { SpaceCredentialType, getSpaceCredentials } from "../../../routes/support/getCredentials";
import { getTrustedOrigins, putTrustedOrigin } from "../../client/trustAdminServiceUclFormationClient";
import { FormationTypeNames, IUclRequestBody } from "../../types";
import {
  assertBlockFormationDeletion,
  isFFAllowProperFormationDeletionEnabled,
  isSeamlessPlanningEnabled,
  setSeamlessPlanningEnabled,
} from "../../utils";
import { IntegratedSystemCommunicationHandlerBase } from "../IntegratedSystemCommunicationHandlerBase";

const logger = getLogger("TMapS-DSP-SacConfig");
const { logInfo, logError } = logger;
interface IOAuthClientMetaData {
  url: string;
  tokenServiceUrl: string;
  clientId: string;
  clientSecret: string;
  audience: string;
}

export class SacConfig extends IntegratedSystemCommunicationHandlerBase {
  static readonly HTTP_TIMEOUT_MS = 1000 * 60 * 4; // 4 minutes
  static readonly HTTP_MAX_RETRIES = 6; // 6 retries
  static readonly HTTP_RETRY_DELAY_MS = 1000 * 10; // 10 second

  getLogger(): ReturnType<typeof getLogger> {
    return logger;
  }

  /**
   * Get the system namespace pattern for the given application namespace.
   * @param context - The request context.
   * @param applicationNamespace The application namespace
   * @returns True if the application namespace matches the system namespace pattern, false otherwise
   */
  async matchesApplicationNamespace(context: IRequestContext, applicationNamespace: string): Promise<boolean> {
    return applicationNamespace === "sap.analytics";
  }

  async getDbClient(context: IRequestContext): Promise<DbClient> {
    context.preventCustomerHanaAutoUpgrade = true;
    const hana = await CustomerHana.fromRequestContext(context);
    return await hana.getTenantManagerClient();
  }

  /**
   * Creates a DSP OAuth Client via LCS API
   * @param dspTenantUuid
   * @param sacConfigurationObject
   * @param uclAssignmentId
   * @returns IOAuthClientMetaData or empty object if error occurs
   */
  async createDSPOAuthClient(
    context: IRequestContext,
    dspTenantUuid: string,
    sacConfigurationObject: object,
    uclAssignmentId: string
  ): Promise<Partial<IOAuthClientMetaData>> {
    if (!dspTenantUuid) {
      const err = new Error(
        `Failed to create OAuthClient on DSP tenant due to invalid DSP tenant uuid: ${dspTenantUuid}.`
      );
      throw err;
    }
    if (!sacConfigurationObject) {
      const err = new Error(
        `Failed to create OAuthClient on DSP tenant ${dspTenantUuid} due to invalid SAC configuration object`
      );
      throw err;
    }
    if (!uclAssignmentId) {
      const err = new Error(
        `Failed to create OAuthClient on DSP tenant ${dspTenantUuid} due to invalid UCL assignment id: ${uclAssignmentId}`
      );
      throw err;
    }
    logInfo(`Setting up OAuth client on DSP tenant ${dspTenantUuid} with UCL assignment id ${uclAssignmentId}`, {
      context,
    });

    // e.g) https://lcs-sac-orcastarkiller.cfapps.sap.hana.ondemand.com/v1/app-integration/ucl/dsp-tenants/{dspTenantUuid}/formations/{uclAssignmentId}/config
    const lcsOAuthCreationUrl = `${getSACUrl(
      "lcs"
    )}/v1/app-integration/ucl/dsp-tenants/${dspTenantUuid}/formations/${uclAssignmentId}/config`;

    try {
      const response = await httpClient.call({
        url: lcsOAuthCreationUrl,
        opts: {
          method: "PUT",
          body: sacConfigurationObject,
          authentication: AuthenticationMode.Uaa,
          callCategory: ExternalCallCategory.UCL,
          callTimeout: SacConfig.HTTP_TIMEOUT_MS,
          retry: {
            maxRetries: SacConfig.HTTP_MAX_RETRIES,
            delay: SacConfig.HTTP_RETRY_DELAY_MS,
          },
        },
      });

      if (response.status !== Status.OK) {
        if (typeof response?.body === "object" && response?.body !== null && "msg" in response?.body) {
          const msg = (response.body as { msg: string }).msg;
          throw new Error(
            `Failed to successfully create DSP OAuth Client. Response status code from LCS API: ${response.status} with message: ${msg}`
          );
        }
        throw new Error(
          `Failed to successfully create DSP OAuth Client. Response status code from LCS API:${response.status}`
        );
      }

      logInfo(`Successfully created DSP OAuth Client via LCS API`, { context });
      return response.body as IOAuthClientMetaData;
    } catch (err) {
      const errorMessage = `Failed to create OAuthClient for DSP tenant ${dspTenantUuid} via LCS API due to error: ${err?.message}`;
      logError(errorMessage, { context });
      throw new Error(errorMessage);
    }
  }

  /**
   * Deletes DSP OAuth Client via LCS API
   * @param dspTenantUuid
   * @param sacTenantUuid
   * @param uclAssignmentId
   */
  async deleteDSPOAuthClient(context: IRequestContext, dspTenantUuid: string, uclAssignmentId: string): Promise<void> {
    if (!dspTenantUuid) {
      const err = new Error(
        `Failed to delete OAuthClient on DSP tenant due to invalid DSP tenant uuid: ${dspTenantUuid}.`
      );
      throw err;
    }
    if (!uclAssignmentId) {
      const err = new Error(
        `Failed to delete OAuthClient on DSP tenant ${dspTenantUuid} due to invalid UCL assignment id: ${uclAssignmentId}`
      );
      throw err;
    }
    logInfo(`Deleting OAuth client on DSP tenant ${dspTenantUuid} with UCL assignment id ${uclAssignmentId}`, {
      context,
    });

    // e.g) https://lcs-sac-orcastarkiller.cfapps.sap.hana.ondemand.com/v1/app-integration/ucl/dsp-tenants/{dspTenantUuid}/formations/{uclAssignmentId}/config
    const lcsOAuthDeletionUrl = `${getSACUrl(
      "lcs"
    )}/v1/app-integration/ucl/dsp-tenants/${dspTenantUuid}/formations/${uclAssignmentId}/config`;

    try {
      const response = await httpClient.call({
        url: lcsOAuthDeletionUrl,
        opts: {
          method: "DELETE",
          authentication: AuthenticationMode.Uaa,
          callCategory: ExternalCallCategory.UCL,
          callTimeout: SacConfig.HTTP_TIMEOUT_MS,
          retry: {
            maxRetries: SacConfig.HTTP_MAX_RETRIES,
            delay: SacConfig.HTTP_RETRY_DELAY_MS,
          },
        },
      });

      if (response.status !== Status.OK) {
        if (typeof response?.body === "object" && response?.body !== null && "msg" in response?.body) {
          const msg = (response.body as { msg: string }).msg;
          throw new Error(
            `Failed to successfully delete DSP OAuth Client. Response status code from LCS API: ${response.status} with message: ${msg}`
          );
        }
        throw new Error(
          `Failed to successfully delete DSP OAuth Client. Response status code from LCS API:${response.status}`
        );
      }

      logInfo(`Successfully deleted DSP OAuth Client via LCS API`, { context });
    } catch (err) {
      const errorMessage = `Failed to delete OAuthClient for DSP tenant ${dspTenantUuid} via LCS API due to error: ${err?.message}`;
      logError(errorMessage, { context });
      throw new Error(errorMessage);
    }
  }

  /**
   * Handles the first synchronization (inbound) request from SAC.
   * SAC does not require any payload in response, therefore we are returning an
   * empty object.
   *
   * @param context - The request context.
   * @returns A promise that resolves to an empty object.
   * @throws {Error} If the tenant is not a BDC Tenant.
   */
  async handleFirstSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    return {};
  }

  /**
   * Handles the second synchronization request.
   *
   * This function normally establishes a connection between the pairing system and the UCL Formation
   * via Connection Manager. However in the case of SAC, the connection is established via
   * App Connectivity Service. Therefore, this function does not need to establish a connection and
   * returns and empty object.
   *
   * @param context - The request context.
   * @param uclRequestBody - UCL request body received.
   * @returns A promise that resolves to the result of the synchronization request.
   * @throws {Error} If the tenant is not a BDC Tenant.
   */
  async handleSecondSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    const sacTenant = uclRequestBody.assignedTenant;
    const dspTenant = uclRequestBody.receiverTenant;
    const uclFormationTypeId = uclRequestBody.context.uclFormationTypeId;

    // new formation type Integrate with SAP Analytics Cloud
    const isIntegrateWithSACFormationTypeActive =
      (await context.isFeatureFlagActive("DWCO_TENANT_MAPPING_DSP_SAC_INTEGRATION")) &&
      (await this.isFormationType(
        context,
        uclFormationTypeId,
        uclRequestBody.receiverTenant.uclAssignmentId,
        FormationTypeNames.DSP_SAC
      ));
    logInfo(`isIntegrateWithSACFormationTypeActive value: ${isIntegrateWithSACFormationTypeActive}`, { context });

    // Stores seamlessPlanningEnabled flag for handleDeleteSynchronizationRequest to consume
    if (isIntegrateWithSACFormationTypeActive) {
      if (sacTenant.configuration.seamlessPlanningEnabled === undefined) {
        const msg = "SAC request body seamlessPlanningEnabled is undefined.";
        logError(msg, { context });
        throw new Error(msg);
      }
      logInfo(`setting seamlessPlanningEnabled to ${sacTenant.configuration.seamlessPlanningEnabled}`, { context });
      await setSeamlessPlanningEnabled(context, sacTenant.configuration.seamlessPlanningEnabled.toString());
      logInfo(
        `finished setting seamlessPlanningEnabled to ${sacTenant.configuration.seamlessPlanningEnabled.toString()}`,
        { context }
      );
    }

    const isDSPandSACMappingActive = await context.isFeatureFlagActive("DWCO_UCL_TENANT_MAPPING_SAC_WITH_BDC_AND_DSP");
    if (isIntegrateWithSACFormationTypeActive) {
      await setLinkTenantUrl(
        context,
        sacTenant.applicationUrl,
        ETenantConnections.BASE_SAC_CON,
        "Links DWC with SAC tenant",
        "Links DWC with SAC tenant"
      );
      logInfo(
        `handleSecondSynchronizationRequest for formation type Integration with SAP Analytics Cloud, SetTenant Link Called with: <${sacTenant.applicationUrl}>`,
        { context }
      );
    } else if (isDSPandSACMappingActive) {
      // GA Workflow
      // set SAC Tenant link - calls emp/Config
      await setLinkTenantUrl(
        context,
        sacTenant.applicationUrl,
        ETenantConnections.LINK_TENANT_URL_UCL_SAC,
        "Links DWC with SAC tenant",
        "Links DWC with SAC tenant"
      );
      logInfo(`handleSecondSynchronizationRequest SetTenant Link Called with: <${sacTenant.applicationUrl}>`, {
        context,
      });
    }

    const isJouleIntegrationActive =
      (await context.isFeatureFlagActive("INFRA_BDC_APPINT_TENANT_MAPPING")) &&
      (await this.isBdcGAFormation(context, uclFormationTypeId, dspTenant.uclAssignmentId));

    let dspOAuthClient: Partial<IOAuthClientMetaData> = {};

    if (isJouleIntegrationActive || isIntegrateWithSACFormationTypeActive) {
      const sacConfigurationObject = sacTenant.configuration;
      const dspTenantUuid = context.tenantId ?? "";
      // Create DSP OAuth Client via LCS API - This will throw error and fail sync request if failure occurs
      dspOAuthClient = await this.createDSPOAuthClient(
        context,
        dspTenantUuid,
        sacConfigurationObject,
        dspTenant.uclAssignmentId
      );
      logInfo(
        `handleSecondSynchronizationRequest oAuth Client with clientId: ${dspOAuthClient.clientId} created successfully`,
        { context }
      );
    }

    if (
      !(await provisioningHelper.isBdcTenant(context)) &&
      !isDSPandSACMappingActive &&
      !isIntegrateWithSACFormationTypeActive
    ) {
      const msg = "Only BDC tenants are allowed to handle synchronization requests from SAC systems.";
      logError(msg, { context });
      throw new Error(msg);
    }

    if (!sacTenant.applicationUrl) {
      const msg = "Application URL is missing in the UCL system request tenant info";
      logError(msg, { context });
      throw new Error(msg);
    }

    let isSetTrustedOrigins = true;
    const trustedOriginsResponse = await getTrustedOrigins(context);
    logInfo(`handleSecondSynchronizationRequest - trustedOrigins: ${JSON.stringify(trustedOriginsResponse)}`, {
      context,
    });

    if (trustedOriginsResponse.isAllowAllOrigins) {
      logInfo(
        `handleSecondSynchronizationRequest - isAllowAllOrigins is set to true, the tenant is allowing all origins`,
        { context }
      );
      isSetTrustedOrigins = false;
    }

    if (trustedOriginsResponse.origins.includes(sacTenant.applicationUrl)) {
      logInfo(`handleSecondSynchronizationRequest - trustedOrigins already contains the SAC URL`, { context });
      isSetTrustedOrigins = false;
    }

    if (isSetTrustedOrigins) {
      const sacUrlToTrust = sacTenant.applicationUrl;
      trustedOriginsResponse.origins.push(sacUrlToTrust);
      await putTrustedOrigin(context, trustedOriginsResponse.origins);
    }

    if (isJouleIntegrationActive || isIntegrateWithSACFormationTypeActive) {
      logInfo("handleSecondSynchronizationRequest returning appIntegration object", { context });
      return {
        appIntegration: dspOAuthClient,
      };
    }

    logInfo("handleSecondSynchronizationRequest returning empty object", { context });
    return {};
  }

  /**
   * Handles the deletion request to unassign SAC system from UCL Formation.
   * Normally this would clear the connection between the pairing system and the UCL Formation.
   * However, in the case of SAC, the connection is established via App Connectivity Service, and there
   * is no connection clearing on dwaas-core side.
   * Therefore, this function does not need to clear any connection and returns an empty object.
   *
   * @param context - The request context.
   * @param _uclSystem - Optional information about the UCL system.
   * @returns A promise that resolves to the result of the deletion request.
   * @throws {Error} If the tenant is not a BDC Tenant.
   */
  async handleDeleteSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    const sacTenant = uclRequestBody.assignedTenant;
    const dspTenant = uclRequestBody.receiverTenant;
    const uclFormationTypeId = uclRequestBody.context.uclFormationTypeId;

    const isDSPandSACMappingActive = await context.isFeatureFlagActive("DWCO_UCL_TENANT_MAPPING_SAC_WITH_BDC_AND_DSP");
    if (!(await isFFAllowProperFormationDeletionEnabled(context)) && isDSPandSACMappingActive) {
      await assertBlockFormationDeletion(context, uclRequestBody);
    }

    // new formation type Integration with SAP Analytics Cloud
    const isIntegrateWithSACFormationTypeActive =
      (await context.isFeatureFlagActive("DWCO_TENANT_MAPPING_DSP_SAC_INTEGRATION")) &&
      (await this.isFormationType(
        context,
        uclFormationTypeId,
        uclRequestBody.receiverTenant.uclAssignmentId,
        FormationTypeNames.DSP_SAC
      ));
    logInfo(`isIntegrateWithSACFormationTypeActive value: ${isIntegrateWithSACFormationTypeActive}`, { context });

    // Perform SAC repository clean up
    if (isIntegrateWithSACFormationTypeActive) {
      logInfo(`fetching seamlessPlanningEnabled flag`, { context });
      const seamlessPlanningEnabled = await isSeamlessPlanningEnabled(context);
      logInfo(`finished fetching seamlessPlanningEnabled flag with value ${seamlessPlanningEnabled}`, { context });

      if (seamlessPlanningEnabled) {
        logInfo(`Processing all SAC repository spaces clean up`, { context });
        const spaceService = new SpaceService(context);
        const dataRepoStates = ["ENABLED", "EMPTY", "DISABLED"];
        let spaceObjects;

        logInfo(`Fetching all SAC repository spaces with dataRepoState ENABLED, EMPTY, or DISABLED`, { context });
        try {
          spaceObjects = await getSpaces(context, {
            details: [`dataRepoState`],
            filters: `dataRepoState:${dataRepoStates.join("|")}`,
          });
        } catch (err) {
          const errorMessage = `Failed to get spaces with dataRepoState ENABLED, EMPTY, or DISABLED due to error: ${err?.message}`;
          logError(errorMessage, { context });
          throw new Error(errorMessage);
        }

        logInfo(`${spaceObjects?.length} spaces with dataRepoState ENABLED, EMPTY, or DISABLED retrieved`, { context });

        for await (const space of spaceObjects) {
          // refer to delinkRepositories in fpa-app/src/sap/fpa/services/dwcDataRepository/core/DWCDataRepositoryConfig.xsjslib for how spaceObjects are used
          const spaceID = space.name;
          const spaceRepoState = space.properties.dataRepoState;
          logInfo(`Processing space ${spaceID} for SAC repository clean up, dataRepoState: ${spaceRepoState}`, {
            context,
          });

          // 1. Removes all content from all data repository SAC schemas
          try {
            logInfo(`Removes all content from all data repository SAC schema for space ${spaceID}`, { context });
            const sacSpaceUserClient = await getClient(
              context,
              await getSpaceCredentials(context, spaceID, SpaceCredentialType.sac)
            );
            const sqlPath = path.join(__dirname, "./sql/cleanupRepositorySchema.sql");
            const cleanupRepositorySchemaQuery: string = await fs.readFile(sqlPath, "utf8");
            const result = await sacSpaceUserClient.exec(cleanupRepositorySchemaQuery);
            logInfo(`Running cleanupRepositorySchemaQuery result: ${JSON.stringify(result)}`, { context });
          } catch (err) {
            const errorMessage = `Failed to remove all content from all data repository SAC schemas for formation ID ${uclRequestBody.receiverTenant.uclAssignmentId} due to error: ${err?.message}`;
            logError(errorMessage, { context });
            throw new Error(errorMessage);
          }

          // 2. Setting all spaces with dataRepoState to DISABLED
          if (spaceRepoState !== "DISABLED") {
            try {
              logInfo(`Setting space ${spaceID} dataRepoState to DISABLED`, { context });
              logInfo(`Checking if space ${spaceID} exists`, { context });
              const existingSpace = await spaceService.getSpaceObjects(spaceID, false);
              if (!existingSpace) {
                const errorMessage = `Failed to get space for space ID: ${spaceID} due to error`;
                logError(errorMessage, { context });
                throw new Error(errorMessage);
              }
              const newSpaceDefinition = {
                version: "1.0.4",
                dataRepoState: "DISABLED",
              };
              logInfo(`Saving space new definition for space ${spaceID}`, { context });
              const newContent = { ...existingSpace.spaceDefinition, ...newSpaceDefinition };
              const saveResult = await spaceService.saveSpace(spaceID, newContent, {
                needObjectSyncWithContentLib: false,
                spaceGuid: "",
              });
              if (
                saveResult.entityResults.length > 0 &&
                saveResult.entityResults.find(
                  (e) => ![DeploymentState.SUCCESS, DeploymentState.NO_CHANGE].includes(e.deploymentState)
                )
              ) {
                const errorMessage = `Failed to save space for space ID: ${spaceID} due to error: ${JSON.stringify(
                  saveResult.messages.all
                )}`;
                logError(errorMessage, { context });
                throw new Error(errorMessage);
              }
              logInfo(`Saving space complete`, { context });
            } catch (err) {
              const errorMessage = `Failed to set space ${spaceID} with dataRepoState set to DISABLED for formation ${uclRequestBody.receiverTenant.uclAssignmentId} due to error: ${err?.message}`;
              logError(errorMessage, { context });
              throw new Error(errorMessage);
            }
          }
        }
        // reset seamlessPlanningEnabled flag that was set during formation creation
        logInfo(`Setting seamlessPlanningEnabled flag to false`, { context });
        await setSeamlessPlanningEnabled(context, "false");
        logInfo(`Finished processing all SAC repository spaces clean up`, { context });
      }
    }

    // GA Workflow
    const isJouleIntegrationActive = await context.isFeatureFlagActive("INFRA_BDC_APPINT_TENANT_MAPPING");
    if (
      (isJouleIntegrationActive &&
        (await this.isBdcGAFormation(context, uclFormationTypeId, dspTenant.uclAssignmentId))) ||
      isIntegrateWithSACFormationTypeActive
    ) {
      const dspTenantUuid = context.tenantId ?? "";
      // Delete DSP OAuth Client via LCS API - This will throw error and fail sync request if failure occurs
      await this.deleteDSPOAuthClient(context, dspTenantUuid, dspTenant.uclAssignmentId);
    }

    /**
     * TODO: DW25-1378 - Reject the request if the tenant is not a BDC Tenant.
     * Make sure the formation is not formed in UCL on BTP Cockpit & handles the
     * formation error-state gracefully.
     */
    if (!sacTenant.applicationUrl) {
      const msg = "Application URL is missing in the UCL system request tenant info";
      logError(msg, { context });
      throw new Error(msg);
    }
    const trustedOriginsResponse = await getTrustedOrigins(context);
    logInfo(`handleDeleteSynchronizationRequest - trustedOrigins: ${JSON.stringify(trustedOriginsResponse)}`, {
      context,
    });

    if (trustedOriginsResponse.origins.includes(sacTenant.applicationUrl)) {
      trustedOriginsResponse.origins.splice(trustedOriginsResponse.origins.indexOf(sacTenant.applicationUrl), 1);
      await putTrustedOrigin(context, trustedOriginsResponse.origins);
    }

    if (isIntegrateWithSACFormationTypeActive) {
      // delete SAC Tenant link in emp/Config
      await setLinkTenantUrl(context, "", ETenantConnections.BASE_SAC_CON, "", "");
      logInfo(
        `handleDeleteSynchronizationRequest for formation type Integration with SAP Analytics Cloud, SetTenant Link Called with: <"">`,
        { context }
      );
    } else if (isDSPandSACMappingActive || isIntegrateWithSACFormationTypeActive) {
      // delete SAC Tenant link in emp/Config
      await setLinkTenantUrl(context, "", ETenantConnections.LINK_TENANT_URL_UCL_SAC, "", "");
      logInfo(`handleDeleteSynchronizationRequest SetTenant Link Called with: <"">`, { context });
    }

    return {};
  }
}

export const sacConfigHandlerInstance = new SacConfig();
