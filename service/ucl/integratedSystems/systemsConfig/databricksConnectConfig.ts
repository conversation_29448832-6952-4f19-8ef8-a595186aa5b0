/** @format */

import { IX509Credentials } from "@sap/dwc-credentials";
import { CodedError } from "@sap/dwc-express-utils";
import { StatusCodes } from "http-status-codes";
import { IUclConnectionConfiguration } from "../../../connections/ucl/models/uclSharedConnection";
import { isCanary } from "../../../lib/node";
import { getLogger } from "../../../logger";
import { IRequestContext } from "../../../repository/security/common/common";
import { ApplicationNamespace } from "../../../reuseComponents/ucl/applicationNamespace";
import { handleNotifyDeepsea } from "../../../reuseComponents/ucl/utils/deepseaUtils";
import * as enumUtil from "../../../routes/c4s/internal_services/utils/enum";
import { SecureStore, SecureStoreKey } from "../../../securestore";
import { IDBXRegistrationInfo, IUclRequestBody, ImplementationStandardConnectionType } from "../../types";
import { assertCertificate, assertX509Certificate, getCertificateByAssignmentId } from "../../utils";
import { IntegratedSystemCommunicationHandlerBase } from "../IntegratedSystemCommunicationHandlerBase";
import { DatabricksConnectProducerConfig, LsSmAccessPolicyAction } from "./databricksConnectProducerConfig";

const logger = getLogger("TMapS-DSP-DbxConfig");

export interface IUclRequestConnectionsPropertiesDeltaShare {
  correlationIds: string[];
  implementationStandard: string;
  host: string;
}

export interface IUclRequestConnectionsPropertiesRest {
  correlationIds: string[];
  apiProtocol: string;
  url: string;
}

export interface IDatabricksConnectConfiguration {
  credentials: {
    inboundCommunication: {
      clientCertificateAuthentication: {
        certificate: string;
      };
    };
    outboundCommunication: {
      clientCertificateAuthentication: {
        correlationIds: string[];
      };
    };
  };
  additionalAttributes: {
    connectionsProperties: Array<IUclRequestConnectionsPropertiesDeltaShare | IUclRequestConnectionsPropertiesRest>;
  };
}

export const DBX_ALL_PUBLIC_SHARES_CORRELATION_ID = "sap.bds:deltaSharing:ALL_PUBLIC_SHARES";
export const DBX_PROVIDER_GOVERNANCE_CORRELATION_ID = "sap.bds:federation.provider:GOVERNANCE";

export class DatabricksConnectConfig extends IntegratedSystemCommunicationHandlerBase {
  /**
   * Exposes the internal logger (primarily for test usage).
   */
  static getLogger() {
    return logger;
  }

  getLogger(): ReturnType<typeof getLogger> {
    return logger;
  }

  /**
   * Checks if the given namespace matches the expected Databricks namespace.
   * @param context - The request context.
   * @param applicationNamespace - The namespace to check.
   * @returns True if the namespace is "sap.databricks".
   */
  async matchesApplicationNamespace(context: IRequestContext, applicationNamespace: string): Promise<boolean> {
    switch (applicationNamespace) {
      case ApplicationNamespace.DATABRICKS_CONNECT:
        return true;
      case ApplicationNamespace.DATABRICKS_BROWNFIELD_CONNECT:
        return await context.isFeatureFlagActive("DWCO_UCL_TENANT_MAPPING_BDC_DSP_BROWNFIELD_DBX");
      default:
        return false;
    }
  }

  /**
   * Handles the first synchronization request with Databricks.
   * @param context - The request context.
   * @param uclRequestBody - The UCL request body.
   * @returns An object containing the inbound communication credentials and (conditionally) additional attributes.
   */
  async handleFirstSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    logger.logInfo(
      `Begin DBX TenantMappingService handleFirstSynchronizationRequest for formationId: ${uclRequestBody?.context?.uclFormationId}, formationName: ${uclRequestBody?.context?.uclFormationName}`,
      { context }
    );

    const deltaShareBWFeatureIsActive = await context.isFeatureFlagActive("DWCO_BDC_DBX_BW_DELTA_SHARE");

    const correlationIds = [DBX_ALL_PUBLIC_SHARES_CORRELATION_ID];
    if (deltaShareBWFeatureIsActive) {
      correlationIds.push(DBX_PROVIDER_GOVERNANCE_CORRELATION_ID);
    }

    // for verification in X-ray test cases only.
    if (isCanary()) {
      logger.logInfo(`InboundCommunication: correlationIds=${JSON.stringify(correlationIds)}`, { context });
    }

    const result = {
      credentials: {
        inboundCommunication: {
          clientCertificateAuthentication: {
            correlationIds,
            certificate: await this.getTenantCertificateForSharedConnection(context, uclRequestBody),
          },
        },
      },
    };

    logger.logInfo(`End DBX TenantMappingService TMaps handleFirstSynchronizationRequest`, { context });

    return result;
  }

  /**
   * Handles the second synchronization request with Databricks.
   * Ensures that the required REST endpoint is included and the operation is
   * idempotent and atomic.
   *
   * If any step fails, any artifacts that were created are rolled back.
   *
   * @param context - The request context.
   * @param uclRequestBody - The UCL request body.
   * @returns A promise that resolves when the operation is complete or rejects if an error occurs.
   */
  async handleSecondSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    logger.logInfo(
      `Begin DBX TenantMappingService handleSecondSynchronizationRequest for formationId: ${uclRequestBody?.context?.uclFormationId}, formationName: ${uclRequestBody?.context?.uclFormationName}`,
      { context }
    );

    const configuration: IDatabricksConnectConfiguration = uclRequestBody.assignedTenant.configuration;

    let certificatePersisted = false;
    let sharedConnectionCreated = false;

    try {
      if (await context.isFeatureFlagActive("DWCO_BDC_DBX_BW_DELTA_SHARE")) {
        this.verifyRestEndpoint(context, configuration, DBX_PROVIDER_GOVERNANCE_CORRELATION_ID, "Provider Governance");

        const x509Cert = configuration?.credentials?.inboundCommunication?.clientCertificateAuthentication?.certificate;
        if (!x509Cert) {
          logger.logError(`handleSecondSynchronizationRequest: DBX certificate missing`, { context });
          throw new CodedError(
            enumUtil.ErrorCodes.InvalidInput,
            "Expected X.509 certificate from DBX, does not exist",
            StatusCodes.BAD_REQUEST
          );
        }

        await this.insertDBXCertIntoCredStore(context, x509Cert);
        certificatePersisted = true;

        // for verification in X-ray test cases only.
        if (isCanary()) {
          const dbxCert = await this.getDBXCertFromCredStore(context);
          if (dbxCert) {
            logger.logInfo(`handleSecondSynchronizationRequest: DBX certificate retrieved successfully`, { context });
          } else {
            logger.logError(`handleSecondSynchronizationRequest: no DBX certificate found in secure store`, {
              context,
            });
          }
        }

        if (await context.isFeatureFlagActive("DWCO_BDC_DBX_ACCESS_POLICIES")) {
          const receiverCert = await getCertificateByAssignmentId(
            context,
            uclRequestBody.receiverTenant.uclAssignmentId
          );

          const dbxRegistrationInfo = this.buildDatabricksRegistrationInfo(context, uclRequestBody);
          await this.manageAccessPolicyLifecycle(
            context,
            LsSmAccessPolicyAction.CREATE,
            receiverCert,
            x509Cert,
            dbxRegistrationInfo,
            uclRequestBody
          );
        }
      }

      // set DBX Tenant link in emp/Config
      // TODO: uncomment once the decision + dependency is resolved
      // TODO: rollback if this fails
      // await setLinkTenantUrl(
      //   context,
      //   uclRequestBody.assignedTenant.applicationUrl,
      //   ETenantConnections.LINK_TENANT_URL_UCL_DBX,
      //   "Links DWC with DBX tenant",
      //   "Links DWC with DBX tenant"
      // );
      // logger.logInfo(
      //   `handleSecondSynchronizationRequest SetTenant Link Called with: <${uclRequestBody.assignedTenant.applicationUrl}>`,
      //   {
      //     context,
      //   }
      // );

      const sharedConnectionConfiguration = this.getSharedConnectionConfiguration(context, configuration);
      await this.startCreationOfSharedConnection(context, sharedConnectionConfiguration, uclRequestBody);
      sharedConnectionCreated = true;

      await handleNotifyDeepsea(logger, context, uclRequestBody, false);

      logger.logInfo(`End DBX TenantMappingService TMaps handleSecondSynchronizationRequest`, { context });
    } catch (error) {
      logger.logError(`Error in handleSecondSynchronizationRequest, initiating rollback: ${error.message}`, {
        context,
      });

      try {
        if (certificatePersisted) {
          await this.deleteDBXCertFromCredStore(context);
          logger.logInfo(`Rolled back DBX certificate insertion successfully`, { context });
        }

        if (sharedConnectionCreated) {
          await this.deleteSharedConnection(context, uclRequestBody);
          logger.logInfo(`Rolled back shared connection creation successfully`, { context });
        }

        logger.logInfo(`Rollback completed succesfully`, { context });
      } catch (rollbackError) {
        logger.logError(`Error during rollback: ${rollbackError.message}`, { context });
      }

      throw error;
    }
  }

  /**
   * Manages the lifecycle of access policies for Databricks.
   * This includes creating and activating access policies or deleting and deactivating access policies.
   * The action is determined by the provided action parameter.
   * If the action is CREATE, it will create and activate the access policies.
   * If the action is DELETE, it will delete and deactivate the access policies.
   * If the action is invalid, it will throw an error.
   *
   * @param context - The request context.
   * @param action - The action to perform (create or delete).
   * @param x509Cert - The X.509 certificate (optional).
   * @param dbxRegistrationInfo - The registration information for Databricks.
   */
  private async manageAccessPolicyLifecycle(
    context: IRequestContext,
    action: LsSmAccessPolicyAction,
    receiverCert: IX509Credentials,
    assignedCert?: string,
    dbxRegistrationInfo?: IDBXRegistrationInfo,
    uclRequestBody?: IUclRequestBody
  ): Promise<void> {
    assertCertificate(receiverCert, context);
    assignedCert = assignedCert || (await this.getDBXCertFromCredStore(context));
    assertX509Certificate(assignedCert, context);
    const integratedSystemConfig = new DatabricksConnectProducerConfig(context, dbxRegistrationInfo, uclRequestBody);
    switch (action) {
      case LsSmAccessPolicyAction.CREATE:
        await integratedSystemConfig.createAndActivateDbxAccessPolicies(context, receiverCert, assignedCert!);
        break;
      case LsSmAccessPolicyAction.DELETE:
        await integratedSystemConfig.deleteAndDeactivateDbxAccessPolicies(context, receiverCert, assignedCert!);
        break;
      default:
        logger.logError(`Invalid action for DBX access policy lifecycle: ${action}`, { context });
        throw new CodedError(
          enumUtil.ErrorCodes.InvalidInput,
          `Invalid action for DBX access policy lifecycle: ${action}`,
          StatusCodes.METHOD_NOT_ALLOWED
        );
    }
  }

  /**
   * Retrieves shared connection configurations for DeltaShare connections.
   * @param context - The request context.
   * @param configuration - The Databricks connect configuration.
   * @returns An array of UCL connection configurations.
   */
  private getSharedConnectionConfiguration(
    context: IRequestContext,
    configuration: IDatabricksConnectConfiguration
  ): IUclConnectionConfiguration[] {
    const uclConnectionConfiguration: IUclConnectionConfiguration[] = [];
    const { additionalAttributes } = configuration;
    const { connectionsProperties } = additionalAttributes;

    for (const property of connectionsProperties) {
      // We only create a connection in the dwaas-core connection manager for the DeltaShare / HDLF connection.
      // The governance REST connection details are perpetuated to Deepsea via uclFormationClient.sendFormationRefresh
      if (
        (property as IUclRequestConnectionsPropertiesDeltaShare)?.implementationStandard ===
        ImplementationStandardConnectionType.DeltaShareV1
      ) {
        uclConnectionConfiguration.push({
          correlationIds: property.correlationIds,
          implementationStandard: ImplementationStandardConnectionType.DeltaShareV1,
          configuration: {
            host: (property as IUclRequestConnectionsPropertiesDeltaShare).host,
            rootPath: "",
            dataAccessLevel: "deltaShare",
          },
        });
      }
    }
    return uclConnectionConfiguration;
  }

  /**
   * Handles the deletion of synchronization data.
   * @param context - The request context.
   * @param uclRequestBody - The UCL request body.
   */
  async handleDeleteSynchronizationRequest(context: IRequestContext, uclRequestBody: IUclRequestBody): Promise<any> {
    logger.logInfo(
      `Begin DBX TenantMappingService handleDeleteSynchronizationRequest for formationId: ${uclRequestBody?.context?.uclFormationId}, formationName: ${uclRequestBody?.context?.uclFormationName}`,
      { context }
    );

    if (await context.isFeatureFlagActive("DWCO_BDC_DBX_ACCESS_POLICIES")) {
      const receiverCert = await getCertificateByAssignmentId(context, uclRequestBody.receiverTenant.uclAssignmentId);
      const assignedCert = await this.getDBXCertFromCredStore(context);
      // if there is no dbx certificate, no need to delete access policy in order to avoid tenant mapping to error state
      if (assignedCert) {
        await this.manageAccessPolicyLifecycle(
          context,
          LsSmAccessPolicyAction.DELETE,
          receiverCert,
          assignedCert,
          /* dbxRegistrationInfo */ undefined,
          uclRequestBody
        );
      }
    }

    if (await context.isFeatureFlagActive("DWCO_BDC_DBX_BW_DELTA_SHARE")) {
      await this.deleteDBXCertFromCredStore(context);
    }

    // delete DBX Tenant link in emp/Config
    // TODO: uncomment once the decision + dependency is resolved
    // await setLinkTenantUrl(context, "", ETenantConnections.LINK_TENANT_URL_UCL_DBX, "", "");
    // logger.logInfo(`handleDeleteSynchronizationRequest SetTenant Link Called with: <"">`, { context });

    await this.deleteSharedConnection(context, uclRequestBody);
    await handleNotifyDeepsea(logger, context, uclRequestBody, true, false);

    logger.logInfo(`End DBX TenantMappingService TMaps handleDeleteSynchronizationRequest`, { context });
  }

  /**
   * Verifies the presence of a REST endpoint based on a correlation ID.
   * @param context - The request context.
   * @param configuration - The Databricks connect configuration.
   * @param correlationId - The correlation ID to check.
   * @param endpointName - The name to use in log messages.
   * @throws Error if the endpoint does not exist.
   */
  private verifyRestEndpoint(
    context: IRequestContext,
    configuration: IDatabricksConnectConfiguration,
    correlationId: string,
    endpointName: string
  ): void {
    const connections = configuration?.additionalAttributes?.connectionsProperties || [];
    const endpointIncluded = connections.some(
      // [] is a safe default for correlationIds to avoid undefined errors.
      (config) =>
        (config.correlationIds || []).includes(correlationId) && (config as IUclRequestConnectionsPropertiesRest).url
    );
    if (endpointIncluded) {
      logger.logInfo(`${endpointName} REST endpoint included in second synchronization request`, { context });
    } else {
      logger.logError(`No ${endpointName} REST endpoint included in second synchronization request`, { context });
      throw new CodedError(
        enumUtil.ErrorCodes.InvalidInput,
        `Expected ${endpointName} REST endpoint from Databricks, does not exist`,
        StatusCodes.BAD_REQUEST
      );
    }
  }

  /**
   * Inserts the DBX certificate into the secure store.
   * @param context - The request context.
   * @param certificate - The X.509 certificate.
   */
  async insertDBXCertIntoCredStore(context: IRequestContext, certificate: string): Promise<void> {
    logger.logInfo(`Begin insertDBXCertIntoCredStore`, { context });
    const secureStore = SecureStore.fromRequestContext(context);
    await secureStore.insert(SecureStoreKey.CertificateForDBXTenantMappingConsumption, { certificate });
    logger.logInfo(`End insertDBXCertIntoCredStore`, { context });
  }

  /**
   * Deletes the DBX certificate from the secure store.
   * @param context - The request context.
   */
  async deleteDBXCertFromCredStore(context: IRequestContext): Promise<void> {
    logger.logInfo(`Begin deleteDBXCertFromCredStore`, { context });
    const secureStore = SecureStore.fromRequestContext(context);
    if (await this.getDBXCertFromCredStore(context)) {
      logger.logInfo(`deleteDBXCertFromCredStore: deleting certificate from secure store`, { context });
      await secureStore.delete(SecureStoreKey.CertificateForDBXTenantMappingConsumption);
    } else {
      logger.logWarning(
        `No certificate found in secure store for key ${SecureStoreKey.CertificateForDBXTenantMappingConsumption}`,
        { context }
      );
    }
    logger.logInfo(`End deleteDBXCertFromCredStore`, { context });
  }

  /**
   * Retrieves the DBX certificate from the secure store.
   * @param context - The request context.
   * @returns The certificate string if found, or undefined.
   */
  async getDBXCertFromCredStore(context: IRequestContext): Promise<string | undefined> {
    logger.logInfo(`Begin getDBXCertFromCredStore`, { context });
    const secureStore = SecureStore.fromRequestContext(context);
    const dbxCertData = (await secureStore.retrieve(SecureStoreKey.CertificateForDBXTenantMappingConsumption)) as
      | { certificate: string }
      | undefined;
    logger.logInfo(`End getDBXCertFromCredStore`, { context });
    return dbxCertData?.certificate;
  }

  private buildDatabricksRegistrationInfo = (
    context: IRequestContext,
    uclRequestBody: IUclRequestBody
  ): IDBXRegistrationInfo => {
    const config: IDatabricksConnectConfiguration = uclRequestBody.assignedTenant.configuration;
    const dbxProviderGovernanceUrl = this.findUrlByCorrelationId(config, DBX_PROVIDER_GOVERNANCE_CORRELATION_ID);
    const fileContainerId = DatabricksConnectProducerConfig.getCfcId(dbxProviderGovernanceUrl);
    if (!fileContainerId) {
      const msg = `CFC ID is not available in the Databricks Connect host: ${dbxProviderGovernanceUrl}. Expected format: UUID.`;
      logger.logError(msg, { context });
      throw new CodedError(enumUtil.ErrorCodes.InvalidInput, msg, StatusCodes.BAD_REQUEST);
    }
    return {
      registrationUrl: dbxProviderGovernanceUrl,
      dspAssignmentId: uclRequestBody.receiverTenant.uclAssignmentId,
      hostname: dbxProviderGovernanceUrl,
      cfcId: fileContainerId,
    };
  };

  private findUrlByCorrelationId(config: IDatabricksConnectConfiguration, correlationId: string): string {
    const connectionProperty = config?.additionalAttributes?.connectionsProperties?.find(
      (property) =>
        property.correlationIds?.includes(correlationId) && (property as IUclRequestConnectionsPropertiesRest)?.url
    ) as IUclRequestConnectionsPropertiesRest;
    return connectionProperty?.url;
  }
}
export const databricksConnectConfigHandlerInstance = new DatabricksConnectConfig();
