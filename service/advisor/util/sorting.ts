/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { AnalysisStatus, IEntityStat } from "../../advisor/common/types";
import { EntityLabel, PersistencyCandidateKeys } from "../../advisor/i18n";

const labelOrder = {
  [EntityLabel.VIEW]: 1,
  [EntityLabel.SQL_SCRIPT_VIEW]: 1, // SQL views and SQL script views have the same order
  [EntityLabel.INTELLIGENT_LOOKUP]: 2,
  [EntityLabel.REMOTE_TABLE]: 3,
  [EntityLabel.LOCAL_TABLE_FILE]: 4,
  [EntityLabel.LOCAL_TABLE]: 5,
  [EntityLabel.SHARED_OBJECT]: 6,
  [EntityLabel.EXTERNAL]: 7,
  [EntityLabel.DAC]: 8,
  [EntityLabel.UNAUTHORIZED]: 9,
};

const statusOrder = {
  [AnalysisStatus.Error]: 1,
  [AnalysisStatus.Warning]: 2,
  [AnalysisStatus.Success]: 3,
};

const persistencyCandidateOrder = {
  [PersistencyCandidateKeys.BestCandidate]: 1,
  [PersistencyCandidateKeys.BestAlternativeCandidate]: 2,
  [PersistencyCandidateKeys.GoodCandidate]: 3,
  [PersistencyCandidateKeys.NotApplicable]: 4,
};

/**
 * This function sorts entities array by following rules:
 * 1 Persistency Candidate order
 *    - BestCandidate
 *    - BestAlternativeCandidate
 *    - GoodCandidate
 *    - NotApplicable
 * 2. Descending on persistency candidate score
 * 3. Descending on persistency order
 * 4. For entities that do not have score and order defined following applies:
 *  4.1. Entity types order:
 *      - Views
 *      - Remote Tables
 *      - Local Tables
 *      - Shared Objects
 *      - DAC
 *  4.2. For the same entity type, following order for analysis status:
 *      - Error
 *      - Warning
 *      - Success
 *  4.3. Entities sorted alphabetically
 */

export function sortEntities(a: IEntityStat, b: IEntityStat, mainEntity: string) {
  if (a.persistencyCandidate && b.persistencyCandidate && a.persistencyCandidate !== b.persistencyCandidate) {
    return sortNumbersAscending(
      persistencyCandidateOrder[a.persistencyCandidate],
      persistencyCandidateOrder[b.persistencyCandidate]
    );
  } else if (a.persistencyCandidateScore! > 0 || b.persistencyCandidateScore! > 0) {
    return sortScoredEntities(
      a.persistencyCandidateScore!,
      a.persistencyOrder!,
      b.persistencyCandidateScore!,
      b.persistencyOrder!
    );
  } else {
    return sortNotScoredEntities(a, b, mainEntity);
  }
}
export const sortScoredEntities = (aScore: number, aOrder: number, bScore: number, bOrder: number): number => {
  if (!(aScore && aOrder)) {
    return 1;
  } else if (!(bScore && bOrder)) {
    return -1;
  }
  if (aScore === bScore) {
    return sortNumbersDescending(aOrder, bOrder);
  } else {
    return sortNumbersDescending(aScore, bScore);
  }
};

const sortNotScoredEntities = (a: IEntityStat, b: IEntityStat, mainEntity: string) => {
  if (a.entity === mainEntity) {
    return -1;
  } else if (b.entity === mainEntity) {
    return 1;
  } else {
    return compareEntities(a, b);
  }
};

const sortNumbersAscending = (a: number, b: number): number => a - b;

const sortNumbersDescending = (a: number, b: number): number => b - a;

const sortStrings = (a: string, b: string): number => (a.toUpperCase() < b.toUpperCase() ? -1 : 1);

const compareEntities = (a: IEntityStat, b: IEntityStat): number =>
  labelOrder[a.label] === labelOrder[b.label]
    ? sortEntitiesSameType(a, b)
    : sortNumbersAscending(labelOrder[a.label], labelOrder[b.label]);

const sortEntitiesSameType = (a: IEntityStat, b: IEntityStat): number =>
  a.analysisStatus === b.analysisStatus
    ? sortStrings(a.entity, b.entity)
    : sortNumbersAscending(statusOrder[a.analysisStatus], statusOrder[b.analysisStatus]);
