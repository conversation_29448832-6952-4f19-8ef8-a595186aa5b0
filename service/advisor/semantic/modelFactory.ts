/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { RepositoryObjectType } from "@sap/deepsea-types";
import { AttributedModel, Node } from ".";
import { AnalysisScopes, CsnRepositoryTag, IEntityDependency, IRouterContext } from "../../routes/advisor/router/types";
import { AnalysisStatus, CSN_TAGS, Category, IExplanation, Level } from "../common/types";
import { EntityLabel, MessageKeys } from "../i18n";
import { CrossSpaceNode } from "./crossspacenode";

function getNode(
  space: string,
  entity: string,
  label: EntityLabel,
  CSN: ICsn,
  isShared: boolean,
  crossSpace: boolean
): Node {
  if (crossSpace) {
    return new CrossSpaceNode(entity, label, CSN, space, isShared);
  } else {
    return new Node(entity, label, CSN, space, isShared);
  }
}

export function instantiateModel(context: IRouterContext, payload: IExplanation[]): AttributedModel {
  const start = new Date();
  const model = new AttributedModel();
  const isCrossSpace = context.scope.has(AnalysisScopes.CrossSpace);
  const entitiesWithDependencies = context.repository.getEntitiesWithDependencies();
  const fullCsn = context.repository.getCsn();
  for (const entity in entitiesWithDependencies) {
    const required: Map<string, string[]> = new Map();
    const properties = entitiesWithDependencies[entity];
    required.set(entity, properties.successors);
    model.pushReferences(required);
    const entityDefinition = fullCsn.definitions[entity] as any; // ICsnDefinition does not contain SQL script annotation
    const isShared = !isCrossSpace && context.repository.isSharedObject(entity, context.space);
    const spaceName = properties[CsnRepositoryTag.SpaceName];
    const entityLabel = getEntityLabel(context, entity, properties, entityDefinition, isShared);
    let analysisStatus = AnalysisStatus.Success;
    if (isShared) {
      payload.push({
        space: spaceName,
        entityName: entity,
        businessName: entity,
        entityLabel,
        level: Level.Warning,
        category: Category.GENERAL,
        importance: 25,
        messageKey: MessageKeys.PA_SHARED_OBJECT_SKIP,
        params: [entity],
      });
      analysisStatus = AnalysisStatus.Warning;
    }
    if (entityLabel === EntityLabel.EXTERNAL) {
      payload.push({
        space: spaceName,
        entityName: entity,
        businessName: entity,
        entityLabel,
        level: Level.Warning,
        category: Category.GENERAL,
        importance: 25,
        messageKey: MessageKeys.PA_EXTERNAL_ENTITY_SKIP,
        params: [entity],
      });
      analysisStatus = AnalysisStatus.Warning;
    }

    const entityCSN = {
      definitions: { [entity]: entityDefinition || {} },
      version: fullCsn.version,
    };
    const node = getNode(spaceName, properties.name || entity, entityLabel, entityCSN, isShared, isCrossSpace);
    node.analysisStatus = analysisStatus;
    model.updateNode(entity, node);
  }
  context.logPerformance(start, "[instantiateModel] end");
  return model;
}

function getEntityLabel(
  context: IRouterContext,
  entity: string,
  properties: IEntityDependency,
  entityDefinition: any,
  isShared: boolean
): EntityLabel {
  let entityLabel: EntityLabel;

  if (isShared) {
    entityLabel = EntityLabel.SHARED_OBJECT;
  } else if (entityDefinition && entityDefinition[CSN_TAGS.ExternalEntity]) {
    entityLabel = EntityLabel.EXTERNAL;
  } else {
    switch (properties[CsnRepositoryTag.TechnicalType]) {
      case RepositoryObjectType.DWC_VIEW:
        entityLabel = EntityLabel.VIEW;
        if (entityDefinition && entityDefinition[CSN_TAGS.SQLscript]) {
          entityLabel = EntityLabel.SQL_SCRIPT_VIEW;
        }
        break;
      case RepositoryObjectType.DWC_LOCAL_TABLE:
        const isFileStorage = context.localTableInfo.getLocalTableDetails(entity).isFileStorage;
        entityLabel = isFileStorage ? EntityLabel.LOCAL_TABLE_FILE : EntityLabel.LOCAL_TABLE;
        break;
      case RepositoryObjectType.DWC_REMOTE_TABLE:
        entityLabel = EntityLabel.REMOTE_TABLE;
        break;
      case RepositoryObjectType.DWC_DAC:
        entityLabel = EntityLabel.DAC;
        break;
      case RepositoryObjectType.DWC_IDT:
        entityLabel = EntityLabel.INTELLIGENT_LOOKUP;
        break;
      default:
        throw new Error(`Unhandled repository object type '${properties[CsnRepositoryTag.TechnicalType]}'`);
    }
  }

  return entityLabel;
}
