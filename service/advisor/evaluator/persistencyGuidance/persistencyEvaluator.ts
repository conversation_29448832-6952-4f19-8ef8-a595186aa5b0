/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { EntityCard } from "../../common/entityCard";
import { LIMITED_ADAPTERS } from "../../common/limitedAdapters";
import { AccessStatus, IEntitiesResult, IEntityResult, TRUE } from "../../common/types";
import { AttributeTags } from "../../semantic/attributes";
import { Node } from "../../semantic/node";
import { isViewPersistencySupported } from "../../util/inputParameters";
import { PersistencyCandidateKeys } from "./../../i18n";
import { sortScoredEntities } from "./../../util/sorting";
import { BaseEvaluator } from "./../base";
import {
  BEST_RANKING_VALUE,
  IPersistencyGuidanceMetric,
  IScoredView,
  PARTIALLY_PERSISTED_PENALTY,
  PERSISTENCY_RATIO,
  SECOND_BEST_RANKING_VALUE,
  Weights,
} from "./types";

/** This evaluator gives guidance on which view(s) to persist in order to improve
 *  the performance of the main view.
 */
export class PersistencyEvaluator extends BaseEvaluator {
  private topologicalOrder = 0;
  private scoredViews: IScoredView[] = [];
  public evaluate() {
    if (!this.context.entity) {
      return;
    }
    this.context.model.clearProcessed();
    // Start evaluation with the root node
    const rootNode = this.context.model.getNode(this.context.entity);
    this.evaluateNode(rootNode);
    const entitiesResult = this.getEntitiesResultScoredViews(this.scoredViews);
    this.context.modelResult = { entitiesResult };
  }

  private evaluateNode(node: Node): void {
    if (node.processed) {
      return;
    } else if (node.isView()) {
      this.processView(node);
    } else {
      node.setAttribute(AttributeTags.DataLineage, TRUE);
    }
    node.processed = true;
  }

  private processView(node: Node): void {
    const partiallyPersisted = node.isPartiallyPersisted();
    node.setAttribute(AttributeTags.DataLineage, TRUE);
    if (node.isPersisted() && !this.isMainEntity(node)) {
      const completedTask = this.context.viewInfo.getViewDetails(node.entity, false, true).completedTask;
      node.setAttribute(AttributeTags.LatestDescendantCompletedTask, String(completedTask));
      // For partially persisted nodes the processing should continue for required nodes
      if (partiallyPersisted) {
        this.context.model.applyRequired(node, (a) => this.evaluateNode(a));
        const totalRequiredRefs = this.getTotalRequiredRefs(node);
        node.setAttribute(AttributeTags.TotalRequiredRefs, totalRequiredRefs);
        const localTablesFile = this.getLocalTablesFile(node);
        node.setAttribute(AttributeTags.LocalTablesFile, localTablesFile);
        this.topologicalOrder++;
      }
    } else {
      this.context.model.applyRequired(node, (a) => this.evaluateNode(a));
      const totalRequiredRefs = this.getTotalRequiredRefs(node);
      node.setAttribute(AttributeTags.TotalRequiredRefs, totalRequiredRefs);
      const localTablesFile = this.getLocalTablesFile(node);
      node.setAttribute(AttributeTags.LocalTablesFile, localTablesFile);
      const latestDescendentTask = this.getLatestDescendantCompletedTask(node);
      node.setAttribute(AttributeTags.LatestDescendantCompletedTask, String(latestDescendentTask));
      const failedTask = this.context.viewInfo.getViewDetails(node.entity, false, true).failedTask;
      failedTask > latestDescendentTask && node.setAttribute(AttributeTags.PersistencyFailed, TRUE);
      this.topologicalOrder++;
      this.scoredViews.push(this.getScoredView(node));
    }
  }

  /**
   * Compute the scores for non-persisted views
   */
  private getScoredView(node: Node): IScoredView {
    const metric = this.getMetric(node);
    const scoredEntity: IScoredView = { name: node.entity, ...metric, score: 0, order: 0 };
    scoredEntity.score = this.computeScore(metric);
    scoredEntity.order = this.topologicalOrder;
    return scoredEntity;
  }

  /**
   * Get the metric used for rating of a view
   */
  private getMetric(node: Node): IPersistencyGuidanceMetric {
    const metric = this.getInitialMetric();
    metric.numberRequired = this.getNumberRequired(node);
    metric.numberConsumers = this.getNumberConsumers(node);
    metric.totalNumberRequired = node.getAttribute(AttributeTags.TotalRequiredRefs).length;
    const { numberVirtualRemoteTables, numberLimitedAdapters } = this.getNumberVirtualRemoteTablesLimitedAdapters(node);
    metric.numberVirtualRemoteTables = numberVirtualRemoteTables;
    metric.numberLimitedAdapter = numberLimitedAdapters;
    metric.numberLocalTablesFile = node.getAttribute(AttributeTags.LocalTablesFile).length;
    metric.isSqlScriptView = node.isSqlScriptView();
    metric.accessStatus = this.getAccessStatus(node);
    const { numberInputParameters, numberInputParametersDefaultValue } =
      this.getNumberInputParametersDefaultValue(node);
    metric.numberInputParameters = numberInputParameters;
    metric.numberInputParametersDefaultValue = numberInputParametersDefaultValue;
    metric.sourceViewHasDac = node.getAttribute(AttributeTags.ConsumesViewsWithDac).length > 0;
    metric.remoteTablesUserPropagation = node.getAttribute(AttributeTags.ConsumesRemoteTableUserPropagation).length > 0;
    metric.notFullyDeployed = !this.isFullyDeployed(node);
    metric.simulationFailed = node.getAttribute(AttributeTags.SimulationFailed) === TRUE;
    metric.persistencyFailed = node.getAttribute(AttributeTags.PersistencyFailed) === TRUE;
    return metric;
  }

  private getInitialMetric(): IPersistencyGuidanceMetric {
    return {
      isSqlScriptView: false,
      accessStatus: AccessStatus.Virtual,
      numberRequired: 0,
      numberConsumers: 0,
      totalNumberRequired: 0,
      numberVirtualRemoteTables: 0,
      numberLimitedAdapter: 0,
      numberLocalTablesFile: 0,
      numberInputParameters: 0,
      numberInputParametersDefaultValue: 0,
      sourceViewHasDac: false,
      remoteTablesUserPropagation: false,
      notFullyDeployed: false,
      simulationFailed: false,
      persistencyFailed: false,
    };
  }

  /**
   * Compute scores for a view to be recommended for persistency.
   * Scores depend on measures and weights defined.
   * '0' will be assigned as score to views that are not eligible for persistency.
   */
  private computeScore(metric: IPersistencyGuidanceMetric): number {
    let score = 0;
    if (this.isEligibleForPersistency(metric)) {
      score =
        Number(metric.isSqlScriptView) * Weights.Default +
        metric.numberRequired * Weights.Default +
        metric.numberConsumers * Weights.Default +
        metric.numberVirtualRemoteTables * Weights.RemoteTablesLimitedAdapters +
        metric.numberLimitedAdapter * Weights.RemoteTablesLimitedAdapters +
        metric.numberLocalTablesFile * Weights.LocalTablesFile +
        metric.totalNumberRequired * Weights.TotalRequired;
      // Views with parameter can only be partially persisted, therefore the score should be divided by 10
      if (metric.numberInputParameters > 0) {
        score = score / PARTIALLY_PERSISTED_PENALTY;
      }
    }
    return score;
  }

  /**
   * This method computes the attribute "LatestDescendantCompletedTask", which is required to know whether
   * a descendant view is persisted after the last failed persistency task.
   * In case a descendant is persisted after the last failed task, it is not considered as failed task.
   */
  private getLatestDescendantCompletedTask(node: Node): number {
    let latestDescendantCompletedTask = 0;
    this.context.model.applyRequired(node, (requiredNode) => {
      if (!requiredNode.isView()) {
        return;
      }
      const latestCompletedTaskRequired = Number(
        requiredNode.getAttribute(AttributeTags.LatestDescendantCompletedTask)
      );
      if (latestCompletedTaskRequired > latestDescendantCompletedTask) {
        latestDescendantCompletedTask = latestCompletedTaskRequired;
      }
    });
    return latestDescendantCompletedTask;
  }

  /**
   * Check whether a view is fully deployed
   */
  private isFullyDeployed(node: Node): boolean {
    let result = true;
    if (node.getAttribute(AttributeTags.NotFullyDeployed) === TRUE) {
      result = false;
    }
    return result;
  }

  /**
   * Check whether a view is eligible for persistency
   */
  private isEligibleForPersistency(metric: IPersistencyGuidanceMetric): boolean {
    const isParameterSupported = isViewPersistencySupported(
      metric.numberInputParameters,
      metric.numberInputParametersDefaultValue
    );

    return (
      isParameterSupported &&
      !metric.sourceViewHasDac &&
      !metric.notFullyDeployed &&
      !metric.remoteTablesUserPropagation
    );
  }

  private getNumberInputParametersDefaultValue(node: Node): {
    numberInputParameters: number;
    numberInputParametersDefaultValue: number;
  } {
    const attributeInputParameters = node.getAttribute(AttributeTags.NumberInputParameters);
    const attributeInputParametersDefaultValue = node.getAttribute(AttributeTags.NumberInputParametersDefaultValue);
    const numberInputParameters = attributeInputParameters ? Number(attributeInputParameters) : 0;
    const numberInputParametersDefaultValue = attributeInputParametersDefaultValue
      ? Number(attributeInputParametersDefaultValue)
      : 0;
    return { numberInputParameters, numberInputParametersDefaultValue };
  }

  private getNumberVirtualRemoteTablesLimitedAdapters(node: Node): {
    numberVirtualRemoteTables: number;
    numberLimitedAdapters: number;
  } {
    const remoteAdapters = node.getMap(AttributeTags.RemoteAdapter);
    const remoteReplicated = node.getMap(AttributeTags.RemoteReplicated);
    const numberVirtualRemoteTables = remoteAdapters.size - remoteReplicated.size;
    let numberLimitedAdapters = 0;
    remoteAdapters.forEach((value, key) => {
      const isReplicated = remoteReplicated.has(key);
      if (!isReplicated && LIMITED_ADAPTERS.includes(value)) {
        numberLimitedAdapters++;
      }
    });
    return { numberVirtualRemoteTables, numberLimitedAdapters };
  }

  private getEntitiesResultScoredViews(scoredViews: IScoredView[]): IEntitiesResult {
    let maximumScore: number;
    const entitiesResult: IEntitiesResult = {};
    const maxNumberCandidates = this.getMaxNumberCandidates(this.scoredViews.length);
    // Sort descending on score and order
    scoredViews.sort((a: IScoredView, b: IScoredView) => sortScoredEntities(a.score, a.order, b.score, b.order));
    scoredViews.forEach((scoredView, index) => {
      const entityResult: IEntityResult = {
        persistencyCandidate: PersistencyCandidateKeys.NotApplicable,
        persistencyCandidateScore: 0,
        persistencyOrder: 0,
        entityCard: new EntityCard(),
      };
      if (scoredView.score > 0) {
        if (index === 0) {
          entityResult.persistencyCandidate = PersistencyCandidateKeys.BestCandidate;
          maximumScore = scoredView.score;
        } else if (index <= maxNumberCandidates) {
          entityResult.persistencyCandidate = PersistencyCandidateKeys.BestAlternativeCandidate;
        } else {
          entityResult.persistencyCandidate = PersistencyCandidateKeys.GoodCandidate;
        }
        scoredView.score = this.getScaledScore(scoredView.score, maximumScore, entityResult.persistencyCandidate);
      }
      entityResult.persistencyCandidateScore = scoredView.score;
      entityResult.persistencyOrder = scoredView.order;
      entityResult.entityCard.buildPersistencyGuidanceCard(
        entityResult.persistencyCandidate,
        scoredView.score,
        scoredView.accessStatus,
        scoredView.isSqlScriptView,
        scoredView.numberRequired,
        scoredView.numberConsumers,
        scoredView.totalNumberRequired,
        scoredView.numberVirtualRemoteTables,
        scoredView.numberLimitedAdapter,
        scoredView.numberLocalTablesFile,
        scoredView.simulationFailed,
        scoredView.persistencyFailed,
        scoredView.numberInputParameters,
        scoredView.numberInputParametersDefaultValue,
        scoredView.sourceViewHasDac,
        scoredView.remoteTablesUserPropagation
      );
      entitiesResult[scoredView.name] = entityResult;
    });
    return entitiesResult;
  }

  /** Scale the score based on SAP Fiori guidelines from 0 to 10 and round to 1 decimal place. */
  private getScaledScore(score: number, maximumScore: number, persistencyCandidate: PersistencyCandidateKeys): number {
    let scaledScore = Math.round((10 * BEST_RANKING_VALUE * score) / maximumScore) / 10;
    // Only best candidate view should have a score of 10. In case another view has a score of 10, the score should be lowered to 9.9.
    if (scaledScore === BEST_RANKING_VALUE && persistencyCandidate !== PersistencyCandidateKeys.BestCandidate) {
      scaledScore = SECOND_BEST_RANKING_VALUE;
    }
    return scaledScore;
  }

  private getNumberRequired(node: Node): number {
    let numberRequired = 0;
    if (this.shouldProcessNode(node)) {
      numberRequired = this.context.model.getRequiredRefs(node).length;
    }
    return numberRequired;
  }

  private getTotalRequiredRefs(node: Node): string[] {
    const totalRequiredSet = new Set<string>(); // Set used to ensure unique elements
    const requiredRefs = this.context.model.getRequiredRefs(node);
    requiredRefs.forEach((required) => {
      const requiredNode = this.context.model.getNode(required);
      const totalRequiredRefs = requiredNode.getAttribute(AttributeTags.TotalRequiredRefs);
      if (Array.isArray(totalRequiredRefs) && totalRequiredRefs.length > 0) {
        totalRequiredRefs.forEach((entity) => totalRequiredSet.add(entity));
      }
      totalRequiredSet.add(required);
    });
    return Array.from(totalRequiredSet);
  }

  private getLocalTablesFile(node: Node): string[] {
    const localTablesFileSet = new Set<string>(); // Set used to ensure unique elements
    const requiredRefs = this.context.model.getRequiredRefs(node);
    requiredRefs.forEach((required) => {
      const requiredNode = this.context.model.getNode(required);
      const totalRequiredRefs = requiredNode.getAttribute(AttributeTags.LocalTablesFile);
      if (Array.isArray(totalRequiredRefs) && totalRequiredRefs.length > 0) {
        totalRequiredRefs.forEach((entity) => localTablesFileSet.add(entity));
      }
      this.context.model.getNode(required).isLocalTableOnFile() && localTablesFileSet.add(required);
    });
    return Array.from(localTablesFileSet);
  }

  private getNumberConsumers(node: Node): number {
    let numberConsumers = 0;
    const consumersRefs = this.context.model.getConsumersRefs(node);
    consumersRefs.forEach((consumer) => {
      const consumerNode = this.context.model.getNode(consumer);
      if (this.shouldProcessNode(consumerNode)) {
        numberConsumers++;
      }
    });
    return numberConsumers;
  }

  private getMaxNumberCandidates(numberViews: number): number {
    let maxNumberCandidates: number = Math.ceil(numberViews * PERSISTENCY_RATIO);
    if (maxNumberCandidates > 5) {
      maxNumberCandidates = 5;
    }
    return maxNumberCandidates;
  }

  private isMainEntity(node: Node): boolean {
    return node.entity === this.context.entity;
  }

  private shouldProcessNode(node: Node): boolean {
    return !node.isPersisted() || node.isPartiallyPersisted() || this.isMainEntity(node);
  }

  private getAccessStatus(node: Node): AccessStatus {
    let accessStatus = AccessStatus.Virtual;
    if (node.isPartiallyPersisted()) {
      accessStatus = AccessStatus.PartiallyPersisted;
    } else if (node.isPersisted()) {
      accessStatus = AccessStatus.Persisted;
    }
    return accessStatus;
  }
}
