/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { CapabilityKey } from "@sap-coastguard/commons";
import { DataValidator } from "@sap-coastguard/consumer-sdk-backend";
import { EntityInformationMap, QueryRestrictions, RuleSeverity } from "@sap-coastguard/interfaces";
import telemetry from "@sap/dwc-telemetry";
import { DbClient } from "../../../lib/DbClient";
import { AnalysisScopes } from "../../../routes/advisor/router/types";
import { IAdvisorContext, IValidationResult } from "../../common/types";
import { getCsnName } from "../../util/csnName";
import { BaseEvaluator } from "../base";
import { getQueryRestrictions } from "./getQueryRestrictions";
import { Logger } from "./logger";

/** This evaluator performs model validation defined in coast-guard repository
 */
export class CoastGuardEvaluator extends BaseEvaluator {
  protected dbClient: DbClient;

  constructor(protected context: IAdvisorContext) {
    super(context);
    this.dbClient = this.context.taskLogger!.getDbClient();
  }

  public async evaluateAsync() {
    const entityName = this.context.entity;
    if (!entityName) {
      return;
    }

    const entityInformation = this.getEntityInformation(entityName, this.context.space);
    const fullCSN = this.context.repository.getCsn();

    (this.context.result as IValidationResult).validationResult = {}; // ensure property is set

    const { validator, capabilities } = this.prepareValidator(entityInformation);
    const validationResult = await validator.validate(
      {
        connection: this.dbClient.nativeConnection,
        currentCSN: fullCSN as any,
        entitiesToValidate: [entityName],
        schema: this.context.spaceSchema,
        entityInformation,
        capabilities,
      },
      {
        logger: new Logger(this.context),
        telemetry,
        tenantId: this.context.taskLogger?.getRequestContext()?.tenantId ?? undefined,
      }
    );

    (this.context.result as IValidationResult).validationResult = validationResult;
  }

  /**
   * prepare the validator with the required rules and capabilities
   * @param entityInformation
   * @returns
   */
  private prepareValidator(entityInformation: EntityInformationMap) {
    const validator = new DataValidator().useViewDataValidation();
    (validator as any).config.extends = [];
    validator
      .configureRule("view-data-validation/unique-keys", RuleSeverity.error)
      .configureRule("view-data-validation/not-null-as-key", RuleSeverity.error)
      .configureRule("view-data-validation/hierarchy-no-multi-parent", RuleSeverity.error)
      .configureRule("view-data-validation/hierarchy-no-cycles", RuleSeverity.error)
      .configureRule("view-data-validation/consistent-data-types", RuleSeverity.error);

    const capabilities: CapabilityKey[] = [];
    this.extendScopedRules(validator, capabilities);

    this.context.logInfo(
      `[CoastGuardEvaluator] Validating entity with entityInformation=${JSON.stringify(
        entityInformation
      )} capabilities=${JSON.stringify(capabilities)}`
    );
    return { validator, capabilities };
  }

  /**
   * Extend validation rules based on the scope, inject any capabilities required for the validation
   * @param validator
   * @param capabilities
   */
  private extendScopedRules(validator: DataValidator, capabilities: CapabilityKey[]) {
    const scopeToRuleMapping: Array<[AnalysisScopes, string?, RuleSeverity?, CapabilityKey[]?]> = [
      [AnalysisScopes.ModelValidationRemoteTables, undefined, undefined, [CapabilityKey.DATA_VALIDATION_ON_REMOTE]],
      [AnalysisScopes.ModelValidationForeignKeys, "view-data-validation/integrity-foreign-key", RuleSeverity.error],
      [
        AnalysisScopes.ModelValidationUnassignedNodes,
        "view-data-validation/unassigned-hierarchy-nodes",
        RuleSeverity.error,
        [CapabilityKey.DISABLE_CSN_GEN_FOR_EXISTING_ENTITIES],
      ],
    ];

    scopeToRuleMapping.forEach(([scope, rule, severity, scopedCapabilities]) => {
      if (this.context.scope.has(scope)) {
        if (rule && severity) {
          validator.configureRule(rule, severity);
        }

        if (scopedCapabilities?.length) {
          capabilities.push(...scopedCapabilities);
        }
      }
    });
  }

  getEntityInformation(entityName: string, rootSpace: string) {
    const validateRemoteTables = this.context.scope.has(AnalysisScopes.ModelValidationRemoteTables);

    const entityInformation: EntityInformationMap = {};

    if (this.context.scope.has(AnalysisScopes.ModelValidationForeignKeys)) {
      this.context.dacPerEntity?.forEach((entity) => {
        const csnName = getCsnName(entity.entity, entity.space, rootSpace);
        entityInformation[csnName] = {
          ...entityInformation[csnName],
          hasDac: entity.hasDAC,
        };
      });
      this.context.remoteAdaptersPerEntity?.forEach((entity) => {
        const csnName = getCsnName(entity.entity, entity.space, rootSpace);
        entityInformation[csnName] = {
          ...entityInformation[csnName],
          hasRemote: entity.adapter.length > 0,
          queryRestrictions: getQueryRestrictions(entity.adapter),
        };
      });
    } else {
      const remoteAdapters = this.context.remoteAdapters ?? [];
      let hasRemote = false;
      if (validateRemoteTables) {
        hasRemote = remoteAdapters.length > 0;
      } else {
        hasRemote = this.context.hasRemoteTable ?? false;
      }

      const hasDAC = this.context.hasDAC;

      let queryRestrictions: QueryRestrictions | undefined;
      if (validateRemoteTables) {
        queryRestrictions = getQueryRestrictions(remoteAdapters);
      }

      entityInformation[entityName] = {
        hasDac: hasDAC,
        hasRemote,
        queryRestrictions,
      };
    }

    return entityInformation;
  }
}
