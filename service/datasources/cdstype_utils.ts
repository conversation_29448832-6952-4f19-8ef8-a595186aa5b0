/**
 *  Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { ICsnElement } from "@sap/deepsea-types";
import { CodedError } from "../server/errorResponse";

// See https://github.wdf.sap.corp/pages/cap/cds/cdl#pre-defined-types
const cdsBoolean = { name: "cds.Boolean" };
const cdsString = { name: "cds.String", length: "length" };
const cdsLargeString = { name: "cds.LargeString" };
const cdsDate = { name: "cds.Date" };
const cdsDateTime = { name: "cds.DateTime" };
const cdsTimestamp = { name: "cds.Timestamp" };
const cdsTime = { name: "cds.Time" };
const cdsInteger = { name: "cds.Integer" };
const cdsInteger64 = { name: "cds.Integer64" };
const cdsDecimal = { name: "cds.Decimal", length: "precision", scale: "scale" };
const cdsDecimalFloat = { name: "cds.DecimalFloat" };
const cdsBinary = { name: "cds.Binary", length: "length" };
const cdsLargeBinary = { name: "cds.LargeBinary" };
const cdsDouble = { name: "cds.Double" };
const cdsUuid = { name: "cds.UUID" };

const cdsHanaSmallInt = { name: "cds.hana.SMALLINT" };
const cdsHanaTinyInt = { name: "cds.hana.TINYINT" };
const cdsHanaSmallDecimal = { name: "cds.hana.SMALLDECIMAL" };
const cdsHanaReal = { name: "cds.hana.REAL" };
const cdsHanaNchar = { name: "cds.hana.NCHAR", length: "length" };
const cdsHanaBinary = { name: "cds.hana.BINARY", length: "length" };
const cdsHanaStPoint = { name: "cds.hana.ST_POINT", length: "srid" };
const cdsHanaStGeometry = { name: "cds.hana.ST_GEOMETRY", length: "srid" };

const mapNamesCdsTypes = [
  cdsBoolean,
  cdsString,
  cdsTimestamp,
  cdsTime,
  cdsDate,
  cdsDateTime,
  cdsInteger,
  cdsInteger64,
  cdsDecimal,
  cdsDecimalFloat,
  cdsDouble,
  cdsBinary,
  cdsLargeBinary,
  cdsLargeString,
  cdsUuid,
];

const mapNamesAllTypes = [
  ...mapNamesCdsTypes,
  cdsHanaSmallInt,
  cdsHanaTinyInt,
  cdsHanaSmallDecimal,
  cdsHanaReal,
  cdsHanaNchar,
  cdsHanaBinary,
  cdsHanaStPoint,
  cdsHanaStGeometry,
];

function getCompatibilityMatrix() {
  const castMatrix: { [key: string]: any[] } = {};
  castMatrix[cdsBoolean.name] = [cdsString, cdsHanaNchar];
  castMatrix[cdsString.name] = [cdsBinary, cdsHanaNchar];
  castMatrix[cdsTimestamp.name] = [cdsDate, cdsString, cdsHanaNchar];
  castMatrix[cdsTime.name] = [cdsString, cdsHanaNchar];
  castMatrix[cdsDate.name] = [cdsTimestamp, cdsString, cdsHanaNchar];
  castMatrix[cdsDateTime.name] = [cdsDate, cdsTimestamp, cdsString, cdsHanaNchar];
  castMatrix[cdsInteger.name] = [cdsInteger64, cdsDecimal, cdsDecimalFloat, cdsDouble, cdsString, cdsHanaNchar];
  castMatrix[cdsInteger64.name] = [cdsDecimal, cdsDecimalFloat, cdsDouble, cdsString, cdsHanaNchar];
  castMatrix[cdsDecimal.name] = [cdsDecimalFloat, cdsDouble, cdsString, cdsHanaNchar];
  castMatrix[cdsDecimalFloat.name] = [cdsString, cdsHanaNchar];
  castMatrix[cdsDouble.name] = [cdsString, cdsHanaNchar];
  castMatrix[cdsBinary.name] = [cdsLargeBinary];
  castMatrix[cdsLargeBinary.name] = [];
  castMatrix[cdsLargeString.name] = [cdsBinary, cdsLargeBinary, cdsHanaNchar];
  castMatrix[cdsUuid.name] = [];
  castMatrix[cdsHanaSmallInt.name] = [
    cdsInteger,
    cdsInteger64,
    cdsDecimal,
    cdsDecimalFloat,
    cdsDouble,
    cdsString,
    cdsHanaSmallDecimal,
    cdsHanaReal,
    cdsHanaNchar,
  ];
  castMatrix[cdsHanaTinyInt.name] = [
    cdsInteger,
    cdsInteger64,
    cdsDecimal,
    cdsDecimalFloat,
    cdsDouble,
    cdsString,
    cdsHanaSmallInt,
    cdsHanaSmallDecimal,
    cdsHanaReal,
    cdsHanaNchar,
  ];
  castMatrix[cdsHanaSmallDecimal.name] = [cdsDecimal, cdsDecimalFloat, cdsDouble, cdsString, cdsHanaReal, cdsHanaNchar];
  castMatrix[cdsHanaReal.name] = [cdsDouble, cdsString, cdsHanaNchar];
  castMatrix[cdsHanaNchar.name] = [cdsString, cdsLargeString];
  castMatrix[cdsHanaBinary.name] = [cdsBinary, cdsLargeBinary];
  castMatrix[cdsHanaStPoint.name] = [];
  castMatrix[cdsHanaStGeometry.name] = [];
  return castMatrix;
}

const castTypeMatrix = getCompatibilityMatrix();

const DEFAULT_SRID = 4326;

function getTypeMap() {
  return {
    BOOLEAN: cdsBoolean,
    VARCHAR: cdsString,
    NVARCHAR: cdsString,
    TIMESTAMP: cdsTimestamp,
    TIME: cdsTime,
    DATE: cdsDate,
    DATETIME: cdsDateTime,
    INTEGER: cdsInteger,
    BIGINT: cdsInteger64,
    DECIMAL: cdsDecimal,
    FLOAT: cdsDecimalFloat,
    DOUBLE: cdsDouble,
    BLOB: cdsBinary,
    CLOB: cdsLargeString,
    NCLOB: cdsLargeString,
    VARBINARY: cdsBinary,

    // HAHA native types
    CHAR: cdsHanaNchar,
    NCHAR: cdsHanaNchar,
    TINYINT: cdsHanaTinyInt,
    SMALLINT: cdsHanaSmallInt,
    SMALLDECIMAL: cdsHanaSmallDecimal,
    REAL: cdsHanaReal,
    BINARY: cdsHanaBinary,
    ST_POINT: cdsHanaStPoint,
    ST_GEOMETRY: cdsHanaStGeometry,

    // unsupported native types currently finding most suitable type
    SECONDTIME: cdsTime,
    SECONDDATE: cdsDateTime,
    LONGDATE: cdsTimestamp,
    SHORTTEXT: cdsString,
    TEXT: cdsLargeString,
    INT: cdsInteger,
    DAYDATE: cdsDate,
  };
}

export const typemap = getTypeMap();

export interface IHanaColumnBase {
  DATA_TYPE_NAME?: keyof typeof typemap;
  LENGTH?: number;
  SCALE?: number;
}
export interface IGetRemoteSourceColumnsResult extends IHanaColumnBase {
  COLUMN_NAME: string | number;
  PRIMARY_KEY_POSITION?: number;
  IS_KEY?: string;
  COMMENTS?: string;
  REMOTE_DATA_TYPE_NAME?: string;
  IS_NULLABLE?: string;
  CAPABILITIES?: string;
  IS_NEW?: boolean;
  DEFAULT_VALUE?: string;
}
export interface IGetRemoteSourceViewParametersResult extends IHanaColumnBase {
  PARAMETER_NAME: string;
  POSITION?: number;
  HAS_DEFAULT_VALUE?: string;
  IS_MANDATORY?: string;
}

interface ISdiBitMask {
  sdiAnnotation: string;
  mask: number;
  csnAnnotation?: string;
}
const aSdiCapabilitiesBitMasks: ISdiBitMask[] = [
  { sdiAnnotation: "FILTER", mask: 0b00000000000010, csnAnnotation: "EQUAL" },
  { sdiAnnotation: "GROUP", mask: 0b00000000000100 },
  { sdiAnnotation: "IN", mask: 0b00000000001000, csnAnnotation: "IN" },
  { sdiAnnotation: "INNERJOIN", mask: 0b00000000010000 },
  { sdiAnnotation: "INSERT", mask: 0b00000000100000 },
  { sdiAnnotation: "LIKE", mask: 0b00000001000000, csnAnnotation: "LIKE" },
  { sdiAnnotation: "OUTERJOIN", mask: 0b00000010000000 },
  { sdiAnnotation: "SELECT", mask: 0b00000100000000 },
  { sdiAnnotation: "SORT", mask: 0b00001000000000 },
  { sdiAnnotation: "UPDATE", mask: 0b00010000000000 },
  { sdiAnnotation: "UPSERT", mask: 0b00100000000000 },
  { sdiAnnotation: "NONEQUAL_COMPARISON", mask: 0b01000000000000, csnAnnotation: "NON_EQUAL" },
  { sdiAnnotation: "BETWEEN", mask: 0b10000000000000, csnAnnotation: "BETWEEN" },
];

export function getCdsOrHanaTypeFromName(typeName: string) {
  const curType: { name: string; length?: string; scale?: string } | undefined = mapNamesAllTypes.find(
    (t) => t.name === typeName
  );
  return curType;
}

/**
 * if dataTypes are same or newType is compatible with oldType return true
 * @param oldType: string
 * @param newType: string
 */
export function isTypeCompatible(newType: string, oldType: string) {
  let result = false;
  if (oldType && newType) {
    if (oldType !== newType) {
      const validTypes = castTypeMatrix[oldType];
      if (validTypes && validTypes.length > 0) {
        result = validTypes.find((t) => t.name === newType) !== undefined;
      }
    } else {
      result = true;
    }
  }
  return result;
}

export function getCDSTypeWithAnnotations(
  entry: IGetRemoteSourceColumnsResult,
  includeNativeType = false
): ICsnElement | undefined {
  const capabilitiesNumeric = Number(entry.CAPABILITIES);

  // check, if column is usable (selectionOnly, DW101-16175)
  if (
    !isNaN(capabilitiesNumeric) &&
    // eslint-disable-next-line no-bitwise
    !(capabilitiesNumeric & aSdiCapabilitiesBitMasks[7].mask) // bit for SELECT
  ) {
    return;
  }

  const result: ICsnElement = getCDSType(entry);
  extendWithCsnAnnotations(result, entry, includeNativeType);

  return result;
}

export function getCDSType(entry: IHanaColumnBase): ICsnElement {
  let cdsType: {
    name: string;
    length?: string;
    scale?: string;
  } = entry.DATA_TYPE_NAME && typemap[entry.DATA_TYPE_NAME] ? typemap[entry.DATA_TYPE_NAME] : cdsLargeString;

  // Manage HANA length constraints for string and binary
  if (entry.LENGTH === null) {
    entry.LENGTH = undefined;
  }
  if (entry.SCALE === null) {
    entry.SCALE = undefined;
  }
  if (entry.LENGTH) {
    if (entry.LENGTH > 5000 && cdsType === cdsString) {
      cdsType = cdsLargeString;
    } else if (entry.LENGTH > 5000 && (cdsType === cdsBinary || cdsType === cdsHanaBinary)) {
      cdsType = cdsLargeBinary;
    }
  } else if (cdsType === cdsHanaNchar) {
    entry.LENGTH = 1;
  } else if (cdsType === cdsString) {
    cdsType = cdsLargeString;
  } else if (cdsType === cdsBinary || cdsType === cdsHanaBinary) {
    cdsType = cdsLargeBinary;
  } else if (cdsType === cdsDecimal) {
    // Change cds.Decimal to cds.DecimalFloat
    cdsType = cdsDecimalFloat;
  }

  if ("PARAMETER_NAME" in entry && cdsType === cdsLargeBinary) {
    throw new CodedError(
      "getCDSType",
      "Input parameters with data type cds.LargeBinary (BLOB) are not supported.",
      400
    );
  }

  const result: ICsnElement = {
    type: cdsType.name,
  };

  if (cdsType.length) {
    // Check srid value
    if (cdsType.length === "srid") {
      if (entry.LENGTH === 8) {
        entry.LENGTH = DEFAULT_SRID;
      }
    }
    (result as any)[cdsType.length] = entry.LENGTH;
  }

  if (cdsType.scale) {
    (result as any)[cdsType.scale] = entry.SCALE;
  }

  return result;
}

interface ICsnElementForRemoteObject extends ICsnElement {
  "@Temp.isNew"?: boolean;
}

function extendWithCsnAnnotations(
  element: ICsnElementForRemoteObject,
  entry: IGetRemoteSourceColumnsResult,
  includeNativeType: boolean
) {
  const capabilitiesNumeric = Number(entry.CAPABILITIES);

  if (!isNaN(capabilitiesNumeric)) {
    const allowedExpressions = aSdiCapabilitiesBitMasks
      // eslint-disable-next-line no-bitwise
      .filter((bitMask) => bitMask.csnAnnotation && capabilitiesNumeric & bitMask.mask)
      .map((bitMask) => ({ "#": bitMask.csnAnnotation! }));
    if (allowedExpressions.length === 0) {
      element["@DataWarehouse.capabilities.filter.enabled"] = false;
    } else {
      element["@DataWarehouse.capabilities.filter.allowedExpressions"] = allowedExpressions;
    }
  }

  element["@EndUserText.label"] = entry.COMMENTS && entry.COMMENTS.trim() ? entry.COMMENTS : entry.COLUMN_NAME + "";

  if (includeNativeType) {
    const nativeType = entry.DATA_TYPE_NAME;
    element["@DataWarehouse.native.dataType"] = nativeType;
  }

  if ((entry.PRIMARY_KEY_POSITION && entry.PRIMARY_KEY_POSITION > 0) || entry.IS_KEY === "PRIMARY KEY") {
    element.key = element.notNull = true;
  }

  if (entry.IS_NULLABLE === "FALSE") {
    element.notNull = true;
  }

  if (entry.IS_NEW) {
    element["@Temp.isNew"] = true;
  }

  if (entry.DEFAULT_VALUE !== undefined && entry.DEFAULT_VALUE !== null) {
    element.default = { val: entry.DEFAULT_VALUE };
  }
}
