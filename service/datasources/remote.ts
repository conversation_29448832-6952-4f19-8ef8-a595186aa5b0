/**
 *  Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { AuthType, ObjectKind } from "@sap/deepsea-types";
import { HttpMethod, httpClient } from "@sap/dwc-http-client";
import { HanaError, escapeDoubleQuotes, escapeSingleQuotes, sql } from "@sap/prom-hana-client";
import Status, { StatusCodes } from "http-status-codes";
import { AdaptionLocationType } from "../../shared/remoteTables/types";
import { getRemoteSourceObjectTreeAsTable } from "../connections/datasources/getRemoteSourceTreeAsTable";
import * as SdiManager from "../connections/datasources/sdi";
import { ConnectionFactory } from "../connections/models/ConnectionFactory";
import * as RepoRemotes from "../connections/remotes";
import {
  BW_BRIDGE_METADATA_SERVICE_PATH,
  ConnectionCapabilities,
  IRemoteConnection,
  RemoteSourceAdapters,
  TypeIds,
} from "../connections/utils/Constants";
import { responseError } from "../dataflow/commonUtil";
import { DataFlowMetadata } from "../dataflow/metadata/metadata";
import { FeatureFlagProvider } from "../featureflags/FeatureFlagProvider";
import type { DbClient, IDbProcedureResult } from "../lib/DbClient";
import { getLogger } from "../logger/index";
import { ReplicationFlowMetadata } from "../replicationflow/metadata/metadata";
import { Activity, IRequestContext } from "../repository/security/common/common";
import { ExternalCallCategory } from "../repository/security/common/externalCallTypes";
import { RequestContext } from "../repository/security/requestContext";
import { getSpaceUuidFromName } from "../repository/spaces";
import { CustomerHana } from "../reuseComponents/spaces/src";
import { getBwBridgeConnectionBySpace } from "../routes/support/getBwBridgeConnectionBySpace";
import { CodedError } from "../server/errorResponse";
import {
  IGetRemoteSourceColumnsResult,
  IGetRemoteSourceViewParametersResult,
  getCDSType,
  getCDSTypeWithAnnotations,
} from "./cdstype_utils";

const { logError, logPerformance } = getLogger("ConnectionsRemote");

interface IResult {
  id: string;
  name: string;
  type: string;
  hasChildren: boolean;
  adapter?: string;
  typeId?: string;
  location?: string;
  children?: IResult[];
  remote?: ICdwRemoteTable;
  definitions?: ICsnDefinitions;
}

interface IResults {
  items: IResult[];
  partial?: boolean;
  stateful?: boolean;
}

interface ITableColumn extends IGetRemoteSourceColumnsResult {
  TABLE_NAME: string;
}

interface IViewParameter extends IGetRemoteSourceViewParametersResult {
  VIEW_NAME: string;
}

interface IGetChildrenPathElement {
  type: string;
  id: string;
  adapter?: string;
}

interface IPathHandlerContext {
  requestContext: IRequestContext;
  client: DbClient;
  spaceGuid: string;
  spaceName: string;
  capabilities: string[];
}

interface IVirtualTableColumn {
  COLUMN_NAME: string;
}

/**
 * Retrieves the list of connections (within a space) on expanding Connections folder of Sources tab in View and Dataflow Builder
 * @param context RequestContext
 * @param spaceId Space Guid
 * @param capabilities ConnectionCapabilities: HanaSdi/DisDataflow. Must have a single entry. Multiple entries cannot be handled
 */
async function getRemotes(context: IRequestContext, spaceId: string, capabilities: string[]): Promise<IResults> {
  const flowModeler = capabilities.includes(ConnectionCapabilities.DISDATAFLOW);
  const featureFlags = await FeatureFlagProvider.getFeatureFlags(context);
  const query: any = {
    space_ids: spaceId,
    details: [
      "name",
      "businessName",
      "remoteSourceName",
      "adapter",
      "disReplicationStatus",
      "schemaName",
      "capabilityDataflowSource",
      "capabilityDisDataflow",
      "capabilityHanaSdi",
      "capabilityPartnerSchema",
      "typeId",
    ],
  };

  const remoteSources = await RepoRemotes.getConnections(context, query);
  // last check to be replaced with a check on schemaName
  const result = (remoteSources || [])
    .filter((entry) => {
      if (flowModeler) {
        if (featureFlags.INFRA_DWC_TWO_TENANT_MODE) {
          return entry.capabilityDataflowSource === "true";
        } else {
          return entry.capabilityDisDataflow === "true";
        }
      } else {
        return entry.capabilityHanaSdi === "true" || entry.capabilityPartnerSchema === "true";
      }
    })
    .map((entry) => ({
      id: entry.schemaName /* for partner connections */ || entry.name /* technical name of the connection */,
      name: entry.businessName || entry.name,
      hasChildren: true,
      type: entry.capabilityPartnerSchema === "true" ? "partnerSchema" : flowModeler ? "remotedis" : "remote", // if several capabilities are passed to "getRemotes", this code cannot handle that
      adapter: entry.adapter,
      ...(entry.disReplicationStatus && { disReplicationStatus: entry.disReplicationStatus }),
      typeId: entry.typeId,
    }));

  return {
    partial: false,
    items: result,
  };
}

function sanitizeQualifiedIdentifier(text: string): string {
  text = text.replace(/"/g, "");
  // outString = outString.replace(/[^a-zA-Z0-9_.$#]/g, "_");
  // outString = outString.replace(/_+/g, "_");

  return text;
}

function qName(remote: string, name: string): string {
  let sQualifiedName = "";
  if (remote) {
    // Escape invalid characters
    sQualifiedName = sanitizeQualifiedIdentifier(remote) + ".";
  }
  // Escape invalid characters
  sQualifiedName += sanitizeQualifiedIdentifier(name);

  return sQualifiedName;
}

/**
 * Retrieves the CSN definition of source objects of a connection that is dragged and dropped in View and Dataflow Builder
 * @param context RequestContext
 * @param spaceName Space name
 * @param remoteInfo ICdwRemoteTable
 */
export const getCSNDefinition = async function (
  context: IRequestContext,
  remoteInfo: ICdwRemoteTable,
  spaceName: string,
  getIsNew?: boolean
): Promise<ICsnDefinitions | undefined> {
  const spaceGuid = await getSpaceUuidFromName(context, spaceName);
  // With DW00-594, remoteInfo.remoteSource is the technical name of the connection when remoteInfo.format === "dis"
  // and remote source name of the connection when remoteInfo.format === "csn"
  let connection: any;
  if (remoteInfo?.remoteSource === "$DWC") {
    connection = [
      {
        name: "$DWC",
      },
    ];
  } else {
    connection = await RepoRemotes.getConnections(context, {
      details: ["typeId", "name", "disConnectionId", "adapter", "location"],
      filters:
        remoteInfo.format === "dis" ? `name:${remoteInfo.remoteSource}` : `remoteSourceName:${remoteInfo.remoteSource}`,
      space_ids: spaceGuid,
    });
  }

  if (!connection || connection.length === 0) {
    throw new CodedError(
      "connectionNotFound",
      `Connection details not found for :: ${remoteInfo.remoteSource}`,
      StatusCodes.NOT_FOUND
    );
  }

  if (remoteInfo.format === "dis") {
    // For Dataflow Builder or replication flow
    const result = await DataFlowMetadata.getDisConnectionDetails(
      context,
      remoteInfo.remoteSource,
      remoteInfo.uniqueName,
      spaceName
    );
    result.connectionId = connection[0].name;
    result.disConnectionId = connection[0].disConnectionId;
    result.typeId = connection[0].typeId;
    return result;
  }

  // For View Builder (remoteInfo.format === "csn")
  const SDP = await FeatureFlagProvider.isFeatureActive(context, "DWC_DUMMY_SPACE_PERMISSIONS");
  if (
    spaceName &&
    (context.hasPrivilegeOnKind(ObjectKind.remote, Activity.read) ||
      (SDP && context.hasPrivilegeOnScope(spaceName, AuthType.DWC_REMOTECONNECTION, Activity.read)))
  ) {
    const customerHana = await CustomerHana.fromRequestContext(context);
    const client = await getSpaceManagerClientRemoteAccess(spaceName, context, false);
    const adapterName = connection[0].adapter?.toLowerCase();
    let viewType: string | undefined;
    let parameterDetails: IDbProcedureResult<any, IGetRemoteSourceViewParametersResult> | undefined;

    try {
      const remoteName = remoteInfo.uniqueName.split(String.fromCharCode(0x7f));
      const remoteDatabase = remoteName.length !== 3 ? `` : remoteName[0];
      const remoteSchema = remoteName.length !== 3 ? `` : remoteName[1];
      const remoteObject = remoteName.length !== 3 ? remoteInfo.uniqueName : remoteName[2];
      const details = await client.executeProcedure<any, IGetRemoteSourceColumnsResult>(
        "SYS",
        "GET_REMOTE_SOURCE_COLUMNS",
        {
          REMOTE_SOURCE_NAME: remoteInfo.remoteSource,
          REMOTE_DATABASE_NAME: remoteDatabase,
          REMOTE_OWNER_NAME: remoteSchema,
          REMOTE_OBJECT_NAME: remoteObject,
        }
      );

      const ffAbapCdsViews = await FeatureFlagProvider.isFeatureActive(
        context,
        "DWCO_REMOTE_TABLE_PARAMETER_SUPPORT_ABAP_CDS_VIEWS"
      );

      if (adapterName === RemoteSourceAdapters.SDA.HanaOdbc) {
        viewType = await getRemoteSourceViewType(client, remoteInfo.remoteSource, remoteSchema, remoteObject);

        if (viewType === "row" || viewType === "calc") {
          const options = viewType === "calc" ? { OPTIONS: "GET_CV_INPUT_PARAMETER_TYPE_INFO" } : {};
          parameterDetails = await client.executeProcedure<any, IGetRemoteSourceViewParametersResult>(
            "SYS",
            "GET_REMOTE_SOURCE_VIEW_PARAMETERS",
            {
              REMOTE_SOURCE_NAME: remoteInfo.remoteSource,
              UNIQUE_NAME: remoteInfo.uniqueName,
              ...options,
            }
          );
        }
      } else if (adapterName === RemoteSourceAdapters.SDA.AbapOdbc && ffAbapCdsViews) {
        parameterDetails = await client.executeProcedure<any, IGetRemoteSourceViewParametersResult>(
          "SYS",
          "GET_REMOTE_SOURCE_VIEW_PARAMETERS",
          {
            REMOTE_SOURCE_NAME: remoteInfo.remoteSource,
            UNIQUE_NAME: remoteInfo.uniqueName,
          }
        );
      }
      let params = parameterDetails?.tables?.[0].filter(
        (entry) => entry && entry.PARAMETER_NAME && entry.DATA_TYPE_NAME
      );

      if (viewType === "calc") {
        // TODO: remove filter with DW12-4558
        params = params?.filter((entry) => !["$$client$$", "$$language$$"].includes(entry.PARAMETER_NAME));
      }

      const getRemoteSourceTableDetails =
        connection[0].location === AdaptionLocationType.Agent ||
        connection[0].location === AdaptionLocationType.DPServer;

      if (getIsNew) {
        const virtualTableColumns: IVirtualTableColumn[] | undefined = getIsNew
          ? await getVirtualTableColumns(
              context,
              client,
              customerHana,
              spaceName,
              remoteInfo.entity!,
              connection[0].location!
            )
          : undefined;
        details.tables[0].forEach((entry) => {
          entry.IS_NEW = !virtualTableColumns?.find((e) => e.COLUMN_NAME === entry.COLUMN_NAME);
        });
      }

      if (getRemoteSourceTableDetails) {
        // Only for DP adapters (SDI)
        const columnDetails = await client.executeProcedure("SYS", "GET_REMOTE_SOURCE_TABLE_DEFINITIONS", {
          REMOTE_SOURCE_NAME: remoteInfo.remoteSource,
          UNIQUE_NAMES: [{ UNIQUE_NAME: remoteInfo.uniqueName }],
        });

        const adapterSupportedTypes: Record<string, string[]> = {
          CloudDataIntegrationAdapter: ["NVARCHAR", "DATE", "BOOLEAN", "INTEGER", "BIGINT"],
          ABAPAdapter: ["NVARCHAR", "VARCHAR", "DATE", "BOOLEAN", "INTEGER", "BIGINT"],
        };

        if (
          connection[0].adapter === "CloudDataIntegrationAdapter" ||
          (connection[0].adapter === "ABAPAdapter" && remoteInfo.uniqueName.startsWith("ABAPTABLES."))
        ) {
          if (!columnDetails.tables[6].some((e) => e.PROPERTY === "__CAPABILITIES__")) {
            details.tables[0].forEach((entry) => {
              /*
              according to SDI Column Capability bit format:
              8450 = 2^1 + 2^8 + 2^13 <=> can be filtered with EQ and BT and is SELECTable
              256 = 2^8 is SELECTable
              */
              columnDetails.tables[6].push({
                COLUMN_NAME: entry.COLUMN_NAME,
                PROPERTY: "__CAPABILITIES__",
                VALUE: adapterSupportedTypes[connection[0].adapter!].includes(entry.DATA_TYPE_NAME!) ? 8450 : 256,
              });
            });
          }
        }

        details.tables[0].forEach((entry) => {
          entry.COMMENTS = columnDetails.tables[1].find(
            (cd: { COLUMN_NAME: string }) => cd.COLUMN_NAME === entry.COLUMN_NAME
          )?.DESCRIPTION;
          entry.CAPABILITIES = columnDetails.tables[6].find(
            (cd: { COLUMN_NAME: string; PROPERTY: string }) =>
              cd.PROPERTY === "__CAPABILITIES__" && cd.COLUMN_NAME === entry.COLUMN_NAME
          )?.VALUE;
          const selMandatory = columnDetails.tables[6].find(
            (cd: { COLUMN_NAME: string; PROPERTY: string }) =>
              cd.PROPERTY === "SEL_MANDATORY" && cd.COLUMN_NAME === entry.COLUMN_NAME
          )?.VALUE;
          if (selMandatory === "X") {
            throw new CodedError("errorgetCSNDefinition", "Mandatory selection fields not supported", 400); // mandatory selection fields not supported
          }
        });
      }

      const csn: ICsn = {
        definitions: {
          [remoteInfo.entity!]: {
            kind: "entity",
            "@DataWarehouse.remote.connection": connection[0].name,
            "@DataWarehouse.remote.entity": remoteInfo.uniqueName,
            ...(await getRemoteParamAnnotations(context, connection[0].typeId)),
            elements: details.tables[0].reduce((current: { [x: string]: ICsnElement }, entry) => {
              if (entry && entry.COLUMN_NAME) {
                const cdsType = getCDSTypeWithAnnotations(entry, true);
                if (cdsType) {
                  current[entry.COLUMN_NAME] = cdsType;
                }
              }
              return current;
            }, {}),
            ...(params !== undefined && params?.length > 0
              ? {
                  params: params.reduce((current: { [x: string]: ICsnElement }, entry) => {
                    current[entry.PARAMETER_NAME] = getCDSType(entry);
                    return current;
                  }, {}),
                  "@cds.persistence.udf": true,
                }
              : {}),
          },
        },
        version: {
          csn: "1.0",
        },
      };

      csn.definitions[remoteInfo.entity!]["@ObjectModel.modelingPattern"] = {
        "#": "DATA_STRUCTURE",
      };
      csn.definitions[remoteInfo.entity!]["@ObjectModel.supportedCapabilities"] = [
        {
          "#": "DATA_STRUCTURE",
        },
        {
          "#": "SQL_DATA_SOURCE",
        },
      ];
      if (remoteInfo.label) {
        (csn.definitions[remoteInfo.entity!] as any)["@EndUserText.label"] = remoteInfo.label;
      }

      return csn.definitions;
    } catch (e) {
      if (e instanceof HanaError) {
        throw new CodedError("errorgetCSNDefinition", e, 400);
      }
      throw e;
    }
  }
};

async function getRemoteSourceViewType(
  client: DbClient,
  remoteSource: string,
  remoteSchema: string,
  remoteObject: string
): Promise<string | undefined> {
  const selectViewTypeQuery = `SELECT "VIEW_TYPE" FROM "${escapeDoubleQuotes(remoteSource)}".PUBLIC.VIEWS
    WHERE "SCHEMA_NAME" = ?
    AND "VIEW_NAME" = ?`;
  const [result] = await client.exec<{ VIEW_TYPE: string }>(selectViewTypeQuery, [remoteSchema, remoteObject]);
  return result ? result.VIEW_TYPE.toLowerCase() : undefined;
}

async function getTableViewColumns(client: DbClient, schema: string): Promise<ITableColumn[]> {
  const query = sql`
    SELECT
      VC.VIEW_NAME AS "TABLE_NAME",
      VC.COLUMN_NAME,
      VC.COMMENTS,
      VC.DATA_TYPE_NAME,
      CASE WHEN VC.DATA_TYPE_NAME IN ('ST_POINT','ST_GEOMETRY') THEN SGC.SRS_ID
        ELSE VC.LENGTH
      END AS LENGTH,
      VC.SCALE,
      VC.POSITION,
      VC.IS_NULLABLE,
      VC.DEFAULT_VALUE,
      IC."CONSTRAINT" AS IS_KEY
    FROM "VIEW_COLUMNS" AS VC
    LEFT OUTER JOIN (SELECT SCHEMA_NAME, TABLE_NAME, COLUMN_NAME, "CONSTRAINT" FROM "INDEX_COLUMNS") AS IC
      ON VC.SCHEMA_NAME = IC.SCHEMA_NAME
      AND VC.VIEW_NAME = IC.TABLE_NAME
      AND VC.COLUMN_NAME = IC.COLUMN_NAME
    LEFT OUTER JOIN SYS.ST_GEOMETRY_COLUMNS AS SGC
      ON VC.SCHEMA_NAME = SGC.SCHEMA_NAME
      AND VC.VIEW_NAME = SGC.TABLE_NAME
      AND VC.COLUMN_NAME = SGC.COLUMN_NAME
    WHERE VC.SCHEMA_NAME = ${schema}
    ${
      schema.toUpperCase() === CustomerHana.globalOwnerSchema
        ? sql`AND VC.VIEW_NAME in (SELECT OBJECT_NAME FROM "PUBLIC"."EFFECTIVE_PRIVILEGES"
          WHERE USER_NAME = CURRENT_USER AND SCHEMA_NAME = ${schema}
          AND OBJECT_TYPE = 'VIEW' AND PRIVILEGE = 'SELECT')`
        : sql``
    }
    ${
      schema.toUpperCase() !== "SYS" && schema.toUpperCase() !== CustomerHana.globalOwnerSchema
        ? // Don't show tables for SYS schema since they're not readable anyway
          sql`UNION
        SELECT
          TC.TABLE_NAME,
          TC.COLUMN_NAME,
          TC.COMMENTS,
          TC.DATA_TYPE_NAME,
          CASE WHEN TC.DATA_TYPE_NAME IN ('ST_POINT','ST_GEOMETRY') THEN SGC.SRS_ID
          ELSE TC.LENGTH
          END AS LENGTH,
          TC.SCALE,
          TC.POSITION,
          TC.IS_NULLABLE,
          TC.DEFAULT_VALUE,
          IC."CONSTRAINT" AS IS_KEY
        FROM "TABLE_COLUMNS" AS TC
        LEFT OUTER JOIN (SELECT SCHEMA_NAME, TABLE_NAME, COLUMN_NAME, "CONSTRAINT" FROM "INDEX_COLUMNS") AS IC
          ON TC.SCHEMA_NAME = IC.SCHEMA_NAME
          AND TC.TABLE_NAME = IC.TABLE_NAME
          AND TC.COLUMN_NAME = IC.COLUMN_NAME
        LEFT OUTER JOIN SYS.ST_GEOMETRY_COLUMNS AS SGC
          ON TC.SCHEMA_NAME = SGC.SCHEMA_NAME
          AND TC.TABLE_NAME = SGC.TABLE_NAME
          AND TC.COLUMN_NAME = SGC.COLUMN_NAME
        WHERE TC.SCHEMA_NAME = ${schema}`
        : sql``
    }
    ORDER BY TABLE_NAME ASC, POSITION ASC;
  `;

  return await client.exec<ITableColumn>(query);
}

async function getTableViewParameters(client: DbClient, schema: string): Promise<IViewParameter[]> {
  const query = sql`
    SELECT
      VP.VIEW_NAME,
      VP.PARAMETER_NAME,
      VP.DATA_TYPE_NAME,
      VP.LENGTH,
      VP.SCALE,
      VP.POSITION,
      VP.HAS_DEFAULT_VALUE
    FROM "VIEW_PARAMETERS" AS VP
    WHERE VP.SCHEMA_NAME = ${schema}
    ${
      schema.toUpperCase() === CustomerHana.globalOwnerSchema
        ? sql`AND VP.VIEW_NAME in (SELECT OBJECT_NAME FROM "PUBLIC"."EFFECTIVE_PRIVILEGES"
          WHERE USER_NAME = CURRENT_USER AND SCHEMA_NAME = ${schema}
          AND OBJECT_TYPE = 'VIEW' AND PRIVILEGE = 'SELECT')`
        : sql``
    }
    ORDER BY VIEW_NAME ASC, POSITION ASC;
  `;

  return await client.exec<IViewParameter>(query);
}

async function getTableViewType(client: DbClient, schema: string, viewName: string) {
  const query = sql`
    SELECT "VIEW_TYPE"
    FROM "SYS"."VIEWS"
    WHERE "SCHEMA_NAME" = ${schema}
    AND "VIEW_NAME" = ${viewName}`;

  const [result] = await client.exec<{ VIEW_TYPE: string }>(query);
  return result ? result.VIEW_TYPE.toLowerCase() : undefined;
}

async function getSpaceContent(context: IRequestContext, client: DbClient, schema: string): Promise<IResults> {
  const columnsMap: Map<string, ITableColumn[]> = new Map<string, ITableColumn[]>();
  const paramsMap: Map<string, IViewParameter[]> = new Map<string, IViewParameter[]>();

  const columns = await getTableViewColumns(client, schema);
  const items: IResult[] = [];

  if (columns !== undefined && columns.length > 0) {
    for (const entry of columns) {
      const key = entry.TABLE_NAME;

      if (columnsMap.get(key) === undefined) {
        columnsMap.set(key, []);
      }
      columnsMap.get(key)!.push(entry);
    }
  }

  const params = await getTableViewParameters(client, schema);

  if (params !== undefined && params.length > 0) {
    for (const entry of params) {
      const key = entry.VIEW_NAME;

      if (paramsMap.get(key) === undefined) {
        paramsMap.set(key, []);
      }
      paramsMap.get(key)!.push(entry);
    }
  }

  for (const tableName of columnsMap.keys()) {
    try {
      const viewType = await getTableViewType(client, schema, tableName);
      const resultEntry: IResult = {
        id: qName(schema, tableName),
        name: tableName,
        hasChildren: false,
        type: "table",
        definitions: {
          [qName(schema, tableName)]: {
            kind: "entity",
            elements: columnsMap.get(tableName)!.reduce((current: { [x: string]: ICsnElement }, entry) => {
              if (entry && entry.COLUMN_NAME) {
                const cdsType = getCDSTypeWithAnnotations(entry);
                if (cdsType) {
                  current[entry.COLUMN_NAME] = cdsType;
                }
              }
              return current;
            }, {}),
            ...(paramsMap.get(tableName) !== undefined && viewType === "row"
              ? {
                  params: paramsMap.get(tableName)!.reduce((current: { [x: string]: ICsnElement }, entry) => {
                    current[entry.PARAMETER_NAME] = getCDSType(entry);
                    return current;
                  }, {}),
                  "@cds.persistence.udf": true,
                }
              : {}),
          },
        },
      };
      items.push(resultEntry);
    } catch (err) {
      if (err instanceof CodedError) {
        logError(`Failed to get metadata for table "${tableName}": ${err}`, { context });
      } else {
        throw err;
      }
    }
  }

  return {
    partial: false,
    items,
  };
}

/**
 * Retrieves:
 * 1. Repository objects on clicking the View and Dataflow Builder
 * 2. List of connections (within a space) on expanding Connections folder of Sources tab in View and Dataflow Builder
 * 3. Connection data on browsing each connection
 * @param context RequestContext
 * @param path Empty or IResult
 * @param spaceName Space name
 * @param spaceGuid Space Guid
 * @param capabilities ConnectionCapabilities: HanaSdi/DisDataflow
 */
export async function getChildren(
  context: IRequestContext,
  path: string,
  spaceName: string,
  spaceGuid: string,
  capabilities: string[]
): Promise<SdiManager.IRemoteSourceObjectTree> {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const auditPrivileges = await customerHana.getAuditPrivileges();
  const showAuditParent = Object.keys(auditPrivileges).some((p) => auditPrivileges[p].includes(spaceName));
  if (!path) {
    // On clicking View Builder or Dataflow Builder
    const schemas = await customerHana.selectSpace(spaceName).getSchemaNames();
    // filter schemas, which are associated with a partner connection
    const query: any = {
      space_ids: spaceGuid,
      details: ["schemaName"],
      filters: "capabilityPartnerSchema:true",
    };
    const partnerConnections = await RepoRemotes.getConnections(context, query);
    schemas.customer_owned = schemas.customer_owned.filter(
      (schema) => !partnerConnections.find((partnerConnection) => partnerConnection.schemaName === schema)
    );
    return {
      partial: false,
      items: [
        { id: "remotes", name: "%%remotesources%%", hasChildren: true, type: "remotes" },
        ...[...schemas.customer_owned, ...schemas.hdi_owned, ...schemas.customer_usergroup, ...schemas.hana_owned].map(
          (name) => ({
            id: name,
            name,
            hasChildren: true,
            type: "schema",
          })
        ),
        ...(showAuditParent
          ? [{ id: "DWC_AUDIT_READER", name: "DWC_AUDIT_READER", hasChildren: true, type: "auditSchema" }]
          : []),
      ],
    };
  } else {
    // Within the Sources Tab

    const parts: IGetChildrenPathElement[] = JSON.parse(path);
    const firstElement = parts.shift()!;

    let pathHandlerContext: IPathHandlerContext;
    try {
      const startTime = new Date();
      pathHandlerContext = {
        requestContext: context,
        client: await getSpaceManagerClientRemoteAccess(spaceName, context),
        spaceGuid,
        spaceName,
        capabilities,
      };
      logPerformance(startTime, `[getChildren] Get exclusive Space Manager Client finished.`, { context });
    } catch (err) {
      throw new CodedError(`getchildren`, `unexpected error`);
    }
    return await handleGetChildrenType(pathHandlerContext, firstElement, parts);
  }
}

async function getSpaceManagerClientRemoteAccess(
  spaceName: string,
  context: IRequestContext,
  exclusive = true
): Promise<DbClient> {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const isUserPropagationEnabled = await FeatureFlagProvider.isFeatureActive(context, "DWCO_USER_PROPAGATION");
  const options = isUserPropagationEnabled ? { disableUserPropagation: true, exclusive } : { exclusive };
  return await customerHana.getSpaceManagerClient(spaceName, options);
}

async function handleGetChildrenType(
  context: IPathHandlerContext,
  part: IGetChildrenPathElement,
  parts: IGetChildrenPathElement[]
): Promise<SdiManager.IRemoteSourceObjectTree> {
  switch (part.type) {
    case "remotes":
      if (parts.length === 0) {
        return await getRemotes(context.requestContext, context.spaceGuid, context.capabilities);
      } else {
        const firstElement = parts.shift()!;
        return await handleGetChildrenRemotes(context, firstElement, parts);
      }
    case "schema":
    case "auditSchema":
      return await getSpaceContent(context.requestContext, context.client, part.id);
    default:
      return {
        items: [],
        partial: false,
        error: "Unexpected state reached",
      };
  }
}

async function getConnectionFromRepo(
  context: IRequestContext,
  spaceGuid: string,
  connectionName: string
): Promise<IRemoteConnection> {
  const [result] = await RepoRemotes.getConnections(context, {
    details: ["remoteSourceName", "typeId"],
    filters: `name:${connectionName}`,
    space_ids: spaceGuid,
  });
  if (!result) {
    throw new CodedError(
      "connectionNotFound",
      `Connection details not found for :: ${connectionName}`,
      StatusCodes.NOT_FOUND
    );
  }
  return result;
}

export async function handleGetChildrenBwBridge(
  context: IRequestContext,
  spaceName: string,
  connectionName: string,
  uniqueName: string,
  nodeType: string,
  isSearch?: boolean,
  filter?: string
): Promise<SdiManager.IRemoteSourceObjectTree> {
  const properties = await getBwBridgeConnectionBySpace(context as RequestContext, spaceName);
  const url = properties ? properties.url : "";
  if (!properties || url === "") {
    throw new Error(`Error getting details for the connection ${connectionName}`);
  }
  let completeUrl = url + BW_BRIDGE_METADATA_SERVICE_PATH;
  const node = "?nodepath=";
  const param = {
    name: uniqueName,
    type: nodeType,
  };
  completeUrl = completeUrl + node + JSON.stringify(param);
  if (isSearch) {
    const searchTerm = "&searchterm=";
    completeUrl = completeUrl + searchTerm + filter; // nodeType is filter string
  }
  try {
    const res = await callBWBridgeMetadataService(context, completeUrl);
    const result = handleBWBridgeMetadatServiceResponse(res);
    const tables = {
      items: result,
    } as SdiManager.IRemoteSourceObjectTree;
    tables.items.forEach((element) => {
      element.remote = {
        uniqueName:
          "<null>" + String.fromCharCode(0x7f) + TypeIds.SAPBWBRIDGE + String.fromCharCode(0x7f) + element.name,
        remoteSource: connectionName,
        format: "csn",
      };
      if (
        !element.hasChildren &&
        element.type !== "AREA" &&
        element.type !== "FOLDER-ADSO" &&
        element.type !== "FOLDER-IOBJ" &&
        !!element.description
      ) {
        element.id = element.name + " - " + element.description;
        element.name = element.name + " - " + element.description;
      } else {
        element.id = element.name;
      }
    });
    return tables;
  } catch (err) {
    throw err;
  }
}

export async function callBWBridgeMetadataService(context: IRequestContext, url: string) {
  const token = context.jwtToken;
  return await httpClient.call({
    url,
    opts: {
      method: HttpMethod.GET,
      headers: { Authorization: `Bearer ${token}` },
      requestContext: context,
      callCategory: ExternalCallCategory.CustomerBW,
      acceptedStatusCodes: [],
    },
  });
}

export function handleBWBridgeMetadatServiceResponse(res: any) {
  switch (res.status) {
    case StatusCodes.OK:
      return res.body;
    case StatusCodes.FORBIDDEN:
      throw new CodedError(
        `userForbiddenMetadataService`,
        `Your user doesn't have sufficient privileges to access the metadata service of the SAP BW Bridge system. Please ensure that your user in the SAP BW Bridge system has the required roles assigned. If this doesn’t help, please contact your administrator.`
      );
    case StatusCodes.UNAUTHORIZED:
      throw new CodedError(
        `userUnauthorizedMetadataService`,
        `Your user is not authorized to access the metadata service of the SAP BW Bridge system. Please ensure that your user is added in the SAP BW Bridge system and that it has the required roles assigned. If this doesn’t help, please contact your administrator.`
      );
    case undefined:
      throw new CodedError("metadataServiceError", "Failed to call the metadata service of the BW Bridge system");
    default:
      const statusCode = res.status;
      const statusMessage = res.statusText ?? Status.getStatusText(res.status);
      throw new CodedError(
        "metadataServiceError",
        `Failed to call the metadata service of the BW Bridge system. Status Code: ${statusCode}, Status Message: ${statusMessage}.`,
        statusCode,
        { list: [statusCode, statusMessage] }
      );
  }
}

async function handleGetChildrenRemotes(
  context: IPathHandlerContext,
  part: IGetChildrenPathElement,
  parts: IGetChildrenPathElement[]
): Promise<SdiManager.IRemoteSourceObjectTree> {
  switch (part.type) {
    case "remote": {
      // Browsing a connection in View Builder
      let uniqueName = "";
      let nodeType = "";
      if (parts.length > 0) {
        uniqueName = parts[parts.length - 1].id;
        nodeType = parts[parts.length - 1].type;
      }
      const connection = await getConnectionFromRepo(context.requestContext, context.spaceGuid, part.id);
      switch (connection.typeId) {
        case TypeIds.SAPBWBRIDGE: {
          return await handleGetChildrenBwBridge(
            context.requestContext,
            context.spaceName,
            connection.remoteSourceName,
            uniqueName,
            nodeType
          );
        }
        default: {
          const adapter = part.adapter || "";
          const timestamp = new Date();
          const result = await getRemoteSourceObjectTreeAsTable(
            context.requestContext,
            context.client,
            connection.remoteSourceName,
            connection.name,
            adapter,
            uniqueName
          );
          logPerformance(
            timestamp,
            `[getChildren] Get Remote Source Object Tree with exclusive db connection finished.`,
            { context: context.requestContext }
          );
          if (result.items.length > 1000) {
            return {
              items: result.items.splice(0, 1000),
              partial: true,
              error: undefined,
            };
          } else {
            return {
              items: result.items,
              partial: result.partial,
            };
          }
        }
      }
    }
    case "remotedis": {
      // Browsing a connection in Dataflow Builder
      let uniqueName = "/";
      if (parts.length > 0) {
        uniqueName = parts[parts.length - 1].id;
      }
      const connectionName = part.id; // technical name of the connection
      const timestamp = new Date();

      //  Additional param for Browse Remote Sources for Replication Flow
      let result;
      // If capabilities  has value useCFW, then it assumed that CFW has to requested for fetching Nodes not FlowAgent
      if (context.capabilities.includes("useCFW")) {
        const customConfig = ReplicationFlowMetadata.getCustomConfigFromCapabilities(context.capabilities);
        result = await ReplicationFlowMetadata.getChildren(
          context.requestContext,
          connectionName,
          context.spaceGuid,
          customConfig
        );
      } else {
        result = await DataFlowMetadata.getDisConnectionChildren(
          context.requestContext,
          connectionName,
          uniqueName,
          context.spaceGuid,
          false,
          false,
          context.capabilities
        );
      }
      logPerformance(timestamp, `[getChildren] Get DIS connection children finished.`, {
        context: context.requestContext,
      });
      return {
        items: result.items || result,
        partial: result.partial || false,
        error: undefined,
      };
    }
    case "partnerSchema": {
      const timestamp = new Date();
      const result = await getSpaceContent(context.requestContext, context.client, part.id);
      logPerformance(timestamp, `[getChildren] Get Partner Schema Content finished.`, {
        context: context.requestContext,
      });
      return result;
    }
    default:
      return {
        items: [],
        partial: false,
        error: "Unexpected state reached",
      };
  }
}

export async function searchChildren(
  context: IRequestContext,
  spaceName: string,
  connectionName: string,
  adapter: string,
  parentId: string,
  filter: string,
  capabilities: string[]
) {
  const isDataFlowModeller = capabilities ? capabilities.includes(ConnectionCapabilities.DISDATAFLOW) : false;
  const spaceGuid = await getSpaceUuidFromName(context, spaceName);
  let result;
  if (isDataFlowModeller) {
    try {
      // If capabilities has value useCFW, then it assumed that CFW has to be requested for fetching Nodes not FlowAgent
      if (capabilities.includes("useCFW")) {
        const customConfig = ReplicationFlowMetadata.getCustomConfigFromCapabilities(capabilities);
        customConfig.search = filter;
        result = await ReplicationFlowMetadata.getChildren(context, connectionName, spaceGuid, customConfig);
      } else {
        result = await DataFlowMetadata.searchDisConnection(
          context,
          connectionName,
          parentId,
          spaceName,
          filter,
          capabilities
        );
      }

      return {
        items: result,
        partial: false,
        error: undefined,
      };
    } catch (err) {
      throw responseError(err, "", context);
    }
  }
  const customerHana = await CustomerHana.fromRequestContext(context);
  const startTime = new Date();
  const client = spaceName
    ? await getSpaceManagerClientRemoteAccess(spaceName, context)
    : await customerHana.getTenantManagerClient({ exclusive: true });
  logPerformance(
    startTime,
    `[searchChildren] Get exclusive ${spaceName ? `Space Manager` : `Tenant Manager`} Client finished.`,
    { context }
  );
  const uniqueName = parentId || "";

  const connection = await RepoRemotes.getConnections(context, {
    details: ["remoteSourceName", "typeId"],
    filters: `name:${connectionName}`,
    space_ids: spaceGuid,
  });

  if (!connection || connection.length === 0) {
    throw new CodedError(
      "connectionNotFound",
      `Connection details not found for :: ${connectionName}`,
      StatusCodes.NOT_FOUND
    );
  }

  if (connection[0].typeId === TypeIds.SAPBWBRIDGE) {
    if (uniqueName && uniqueName !== "") {
      const types = uniqueName.split("$");
      const id = types[0];
      const type = types[1];
      return await handleGetChildrenBwBridge(
        context,
        spaceName,
        connection[0].remoteSourceName,
        id,
        type,
        true,
        filter
      );
    }
  } else {
    const result = await getRemoteSourceObjectTreeAsTable(
      context,
      client,
      connection[0].remoteSourceName,
      connection[0].name,
      adapter,
      uniqueName,
      filter
    );
    logPerformance(startTime, `[searchChildren] Get Remote Source Object Tree with exclusive db connection finished.`, {
      context,
    });
    return result;
  }
}

export async function grantSpaceUserToRemote(context: IRequestContext, remote: string, space: string) {
  const customerHana = await CustomerHana.fromRequestContext(context);
  /* eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled */
  const [client, spaceOwner, spaceManager] = await Promise.all([
    customerHana.getTenantManagerClient(),
    customerHana.getSpaceUserName(space, "spaceOwner"),
    customerHana.getSpaceUserName(space, "spaceManager"),
  ]);

  const query = (
    user: string
  ) => `GRANT REMOTE EXECUTE, CREATE VIRTUAL TABLE, LINKED DATABASE, CREATE REMOTE SUBSCRIPTION, PROCESS REMOTE SUBSCRIPTION EXCEPTION, ALTER
    ON REMOTE SOURCE "${escapeDoubleQuotes(remote)}"
    TO ${user}`;

  const promises = [client.exec(query(spaceOwner)), client.exec(query(spaceManager))];
  /* eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled */
  return await Promise.all(promises);
}

export async function revokeSpaceUserFromRemote(context: IRequestContext, remote: string, space: string) {
  const customerHana = await CustomerHana.fromRequestContext(context);
  /* eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled */
  const [client, spaceOwner, spaceManager] = await Promise.all([
    customerHana.getTenantManagerClient(),
    customerHana.getSpaceUserName(space, "spaceOwner"),
    customerHana.getSpaceUserName(space, "spaceManager"),
  ]);

  const query = (
    user: string
  ) => `REVOKE REMOTE EXECUTE, CREATE VIRTUAL TABLE, LINKED DATABASE, CREATE REMOTE SUBSCRIPTION, PROCESS REMOTE SUBSCRIPTION EXCEPTION, ALTER
    ON REMOTE SOURCE "${escapeDoubleQuotes(remote)}"
    FROM ${user}`;

  const promises = [client.exec(query(spaceOwner)), client.exec(query(spaceManager))];
  /* eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled */
  return await Promise.all(promises);
}

// grant privileges for the new remote source to the support role
export async function grantSpaceSupportRoleToRemote(context: IRequestContext, remote: string, space: string) {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient();

  await client.exec(`GRANT CREATE VIRTUAL TABLE, CREATE REMOTE SUBSCRIPTION, PROCESS REMOTE SUBSCRIPTION EXCEPTION, ALTER
    ON REMOTE SOURCE "${escapeDoubleQuotes(remote)}"
      TO "${CustomerHana.tenantOwnerSchema}"."$$global$$::dwcRemoteSources"`);

  await client.exec(`GRANT CREATE VIRTUAL TABLE, CREATE REMOTE SUBSCRIPTION, PROCESS REMOTE SUBSCRIPTION EXCEPTION, ALTER, DROP
    ON REMOTE SOURCE "${escapeDoubleQuotes(remote)}"
      TO "${CustomerHana.tenantOwnerSchema}"."$$global$$::dwcRemoteSourcesExtended"`);
}

// revoke privileges for the remote source from the support role
export async function revokeSpaceSupportRoleFromRemote(context: IRequestContext, remote: string, space: string) {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient();

  await client.exec(`REVOKE CREATE VIRTUAL TABLE, CREATE REMOTE SUBSCRIPTION, PROCESS REMOTE SUBSCRIPTION EXCEPTION, ALTER
    ON REMOTE SOURCE "${escapeDoubleQuotes(remote)}"
      FROM "${CustomerHana.tenantOwnerSchema}"."$$global$$::dwcRemoteSources"`);

  await client.exec(`REVOKE CREATE VIRTUAL TABLE, CREATE REMOTE SUBSCRIPTION, PROCESS REMOTE SUBSCRIPTION EXCEPTION, ALTER, DROP
    ON REMOTE SOURCE "${escapeDoubleQuotes(remote)}"
      FROM "${CustomerHana.tenantOwnerSchema}"."$$global$$::dwcRemoteSourcesExtended"`);
}

// grant privileges for the new remote source to the consumer role (DIS USER for HDL_FILES delta share)
export async function grantConsumerRoleToRemote(context: IRequestContext, remote: string, space: string) {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient();
  const roleName = `"${escapeDoubleQuotes(space)}"."${escapeDoubleQuotes(space)}::remoteSourceConsumers"`;

  await client.exec(`GRANT CREATE VIRTUAL TABLE, CREATE REMOTE SUBSCRIPTION, PROCESS REMOTE SUBSCRIPTION EXCEPTION, ALTER, DROP
    ON REMOTE SOURCE "${escapeDoubleQuotes(remote)}" TO ${roleName}`);
}

// revoke privileges for the new remote source to the consumer role (DIS USER for HDL_FILES delta share)
export async function revokeConsumerRoleFromRemote(context: IRequestContext, remote: string, space: string) {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient();
  const roleName = `"${escapeDoubleQuotes(space)}"."${escapeDoubleQuotes(space)}::remoteSourceConsumers"`;

  await client.exec(`REVOKE CREATE VIRTUAL TABLE, CREATE REMOTE SUBSCRIPTION, PROCESS REMOTE SUBSCRIPTION EXCEPTION, ALTER, DROP
    ON REMOTE SOURCE "${escapeDoubleQuotes(remote)}" FROM ${roleName}`);
}

async function getRemoteParamsFromConnectionMetadata(
  context: IRequestContext,
  connectionType: string
): Promise<ICsnRemoteParamAnnotation[]> {
  try {
    const connectionModel = ConnectionFactory.fromTypeId(TypeIds[connectionType as TypeIds], context);
    const connectionMetaSchema = await connectionModel.getAdjustedMetaschema();
    if (connectionMetaSchema.CSNInjection && connectionMetaSchema.CSNInjection.remoteTable) {
      return connectionMetaSchema.CSNInjection.remoteTable.annotations || ([] as ICsnRemoteParamAnnotation[]);
    }
  } catch (err) {
    logError(`Failed to get remote parameters from metaschema ${err}`, { context });
  }
  return [];
}

async function getRemoteParamAnnotations(context: IRequestContext, connectionType: string) {
  if (connectionType !== TypeIds.SAPSF) {
    return {};
  }
  const remoteParams = await getRemoteParamsFromConnectionMetadata(context, connectionType);

  return remoteParams.reduce(
    (annotations, annotation: ICsnRemoteParamAnnotation) => ({ ...annotations, [annotation.name]: annotation.value }),
    {}
  );
}
/**
 * For FVT, the function retrieves the columns of the artifact "fabric virtual table" supporting a remote table.
 * For non-FVT, the function retrieves the columns of the artifact "virtual table" when the remote subscription
 * of the remote table was not created with the option "WITH SCHEMA CHANGES".
 * When the remote subscription of the remote table was created with the option "WITH SCHEMA CHANGES",
 * the columns of the "toggle view" are returned.
 * @param context RequestContext
 * @param client
 * @param customerHana
 * @param spaceName
 * @param tableName
 * @param location
 */
async function getVirtualTableColumns(
  context: IRequestContext,
  client: DbClient,
  customerHana: CustomerHana,
  spaceName: string,
  tableName: string,
  location: string
): Promise<IVirtualTableColumn[] | undefined> {
  const { space_schema, spc_tec_internal } = await customerHana.selectSpace(spaceName).getSchemaNames();
  const REMOTE_TABLE_CONTROL_TABLE = "$$REMOTE_TABLE$$";
  const technicalSchema = spc_tec_internal || space_schema;
  const controlTable = `"${escapeDoubleQuotes(technicalSchema)}"."${escapeDoubleQuotes(REMOTE_TABLE_CONTROL_TABLE)}"`;

  const st = [AdaptionLocationType.Agent, AdaptionLocationType.DPServer].includes(location as AdaptionLocationType)
    ? `
      SELECT
        "C"."COLUMN_NAME"
      FROM
        "SYS"."VIEW_COLUMNS" AS "C"
      INNER JOIN
        ${controlTable} AS "RT"
      ON
        "RT"."VIEW_NAME" = "C"."VIEW_NAME"
      LEFT OUTER JOIN
        "SYS"."REMOTE_SUBSCRIPTIONS" AS "RS"
      ON
        "RS"."SCHEMA_NAME" = "RT"."OBJECT_SCHEMA_NAME"
      AND
        "RS"."SUBSCRIPTION_NAME" = "RT"."SUBSCRIPTION_NAME"
      WHERE
        "C"."SCHEMA_NAME" = ?
      AND
        "RT"."TABLE_NAME" = ?
      AND
        COALESCE( "RS"."SCHEMA_CHANGES", 'NULL' ) = 'TRUE'
      UNION ALL
      SELECT
        "C"."COLUMN_NAME"
      FROM
        "SYS"."TABLE_COLUMNS" AS "C"
      INNER JOIN
        ${controlTable} AS "RT"
      ON
        "RT"."VIRTUAL_TABLE_NAME" = "C"."TABLE_NAME"
      LEFT OUTER JOIN
        "SYS"."REMOTE_SUBSCRIPTIONS" AS "RS"
      ON
        "RS"."SCHEMA_NAME" = "RT"."OBJECT_SCHEMA_NAME"
      AND
        "RS"."SUBSCRIPTION_NAME" = "RT"."SUBSCRIPTION_NAME"
      WHERE
        "C"."SCHEMA_NAME" = ?
      AND
        "RT"."TABLE_NAME" = ?
      AND
        COALESCE( "RS"."SCHEMA_CHANGES", 'NULL' ) <> 'TRUE'`
    : `
      SELECT
        "TC"."COLUMN_NAME"
      FROM
        "SYS"."TABLE_COLUMNS" AS "TC"
      WHERE
        "TC"."SCHEMA_NAME" = ?
      AND
        "TC"."TABLE_NAME" = ?`;
  const parameters = [AdaptionLocationType.Agent, AdaptionLocationType.DPServer].includes(
    location as AdaptionLocationType
  )
    ? [
        escapeSingleQuotes(space_schema),
        escapeSingleQuotes(tableName),
        escapeSingleQuotes(space_schema),
        escapeSingleQuotes(tableName),
      ]
    : [escapeSingleQuotes(space_schema), escapeSingleQuotes(tableName)];

  try {
    return await client.exec<{ COLUMN_NAME: string }>(st, parameters);
  } catch (e) {
    // if anything goes wrong, just proceed with empty virtual table metadata -> everything will be classified as 'new'
    logError(`Could not get Virtual Table Column Metadata ${e}`, { context });
  }
}
