/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { IRequestContext } from "@sap/deepsea-types";
import { getLogger } from "../../logger";
import { ITaskLogger, Status } from "../../task/logger/models";
import { Activity, IExecutable, Parameters } from "../../task/models";
import { TaskExecuteResponse } from "../../task/models/TaskExecuteResponse";
import { LockIdentifier, LockedTask } from "../../task/orchestrator/services/tasklock";
import { OverwriteResponse } from "../../task/orchestrator/services/tasklock/OverwriteResponse";
import { OutboundTableMetricService } from "./outboundTableMetricService";
const { logWarning, logError } = getLogger("OutboundTableWeeklyMetricsTask");

export class OutboundTableWeeklyMetricsTask implements IExecutable {
  volatileParameter: object | undefined;
  constructor(
    private readonly context: IRequestContext,
    _spaceId: string,
    _objectId: string,
    _activity: Activity,
    _parameters: Parameters
  ) {}

  public async execute(taskLogger: ITaskLogger): Promise<TaskExecuteResponse> {
    let status = Status.FAILED;
    const context = this.context;
    try {
      const metricService = new OutboundTableMetricService(this.context);
      await metricService.logOutboundTableMetrics();
      status = Status.COMPLETED;
      logWarning("[Delta Read API] Successfully sent delta read api usage metrics to Dynatrace", {
        context,
      });
    } catch (error) {
      logError(["[Delta Read API] Error when sending delta read api usage metrics to Dynatrace", error], {
        context,
      });
    }
    return {
      status,
      endTime: new Date(),
    };
  }

  public async isAuthorized(taskLogger: ITaskLogger, isDesignTime?: boolean | undefined): Promise<boolean> {
    return !!this.context.userInfo.technical;
  }

  getLockIdentifier(): LockIdentifier | null {
    return null;
  }

  public async overwriteLock(lock: LockedTask): Promise<OverwriteResponse> {
    const response: OverwriteResponse = {
      takeover: true,
      status: Status.FAILED,
      endTime: new Date(),
    };
    return response;
  }
}
