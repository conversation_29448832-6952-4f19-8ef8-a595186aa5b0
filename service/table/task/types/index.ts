/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { DbClient } from "../../../lib/DbClient";
import { IRequestContext } from "../../../repository/security/common/common";

export interface ISpaceClientWithSchema extends IClientWithSchema {
  context: IRequestContext;
  spaceName: string;
  isSpaceOwner: boolean;
}

export interface IClientWithSchema {
  client: DbClient;
  internalSchemaName: string;
  technicalSchemaName: string;
}

export enum LocalTableRunStatus {
  "Initial" = "INITIAL",
  "Success" = "SUCCESS",
  "Error" = "ERROR",
}

export enum LocalTableTaskLogMessageBundleModel {
  MSG_BUNDLE_ID = "sap.dwc.localTable",
  EXECUTION_START = "EXECUTION_START",
  EXECUTION_ERROR_LOCKED_SPACE = "EXECUTION_ERROR_LOCKED_SPACE",
  EXECUTION_CONNECTION_DETAILS = "EXECUTION_CONNECTION_DETAILS",
  LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING = "LT_REMOVE_DELETED_RECORDS_RUN_STATUS_STARTING",
  LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER = "LT_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER",
  LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR = "LT_REMOVE_DELETED_RECORDS_RUN_FAILED_ERROR",
  LT_REMOVE_DELETED_RECORDS_STATUS_INFO = "LT_REMOVE_DELETED_RECORDS_STATUS_INFO",
  LT_REMOVE_DELETED_RECORDS_STATUS_WARNING = "LT_REMOVE_DELETED_RECORDS_STATUS_WARNING",
  LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED = "LT_REMOVE_DELETED_RECORDS_RUN_STATUS_COMPLETED",
  LT_REMOVE_RECORDS_RUN_STATUS_STARTING = "LT_REMOVE_RECORDS_RUN_STATUS_STARTING",
  LT_REMOVE_RECORDS_RUN_FAILED_ERROR = "LT_REMOVE_RECORDS_RUN_FAILED_ERROR",
  LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS = "LT_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS",
  LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED = "LT_REMOVE_RECORDS_RUN_STATUS_COMPLETED",
  LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS = "LT_DELTA_REMOVE_RECORDS_STATUS_NUMBER_OF_RECORDS",
  LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING = "LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING",
  LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER = "LT_DELTA_REMOVE_RECORDS_RUN_STATUS_STARTING_WITH_FILTER",
  LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED = "LT_DELTA_REMOVE_RECORDS_RUN_STATUS_COMPLETED",
  LTF_OPTIMIZE_STARTING = "LTF_OPTIMIZE_STARTING",
  LTF_OPTIMIZE_FAILED_ERROR = "LTF_OPTIMIZE_FAILED_ERROR",
  LTF_OPTIMIZE_SUCCESS = "LTF_OPTIMIZE_SUCCESS",
  LTF_VACUUM_STARTING = "LTF_VACUUM_STARTING",
  LTF_VACUUM_FAILED_ERROR = "LTF_VACUUM_FAILED_ERROR",
  LTF_VACUUM_SUCCESS = "LTF_VACUUM_SUCCESS",
  LTF_VACUUM_STATUS_INFO = "LTF_VACUUM_STATUS_INFO",
  LTF_VACUUM_STATUS_WARNING = "LTF_VACUUM_STATUS_WARNING",
  LTF_MERGE_STARTING = "LTF_MERGE_STARTING",
  LTF_AUTO_MERGE_STARTING = "LTF_AUTO_MERGE_STARTING",
  LTF_API_MERGE_STARTING = "LTF_API_MERGE_STARTING",
  LTF_MERGE_FAILED_ERROR = "LTF_MERGE_FAILED_ERROR",
  LTF_MERGE_SUCCESS = "LTF_MERGE_SUCCESS",
  LTF_ERROR_TIMEOUT = "LTF_ERROR_TIMEOUT",
  LTF_ERROR_ASYNC_NOT_STARTED = "LTF_ERROR_ASYNC_NOT_STARTED",
  LTF_ERROR_CANCELED = "LTF_ERROR_CANCELED",
  LTF_MERGE_PROCESSING = "LTF_MERGE_PROCESSING",
  LTF_MERGE_STOPPED = "LTF_MERGE_STOPPED",
  LTF_MERGE_PARTIALLY_SUCCESSFUL = "LTF_MERGE_PARTIALLY_SUCCESSFUL",
  FIND_AND_REPLACE_STATUS_STARTING = "FIND_AND_REPLACE_STATUS_STARTING",
  FIND_AND_REPLACE_FAILED_ERROR = "FIND_AND_REPLACE_FAILED_ERROR",
  FIND_AND_REPLACE_SUCCESS = "FIND_AND_REPLACE_SUCCESS",
  TASK_HANA_OUT_OF_MEMORY = "TASK_HANA_OUT_OF_MEMORY",
  TASK_HANA_ADMISSION_CTRL_REJECTION = "TASK_HANA_ADMISSION_CTRL_REJECTION",
  TASK_HANA_CLIENT_RATE_LIMIT_REJECTION = "TASK_HANA_CLIENT_RATE_LIMIT_REJECTION",
  LTF_OPTIMIZE_STOPPED = "LTF_OPTIMIZE_STOPPED",
  LTF_OPTIMIZE_PROCESSING = "LTF_OPTIMIZE_PROCESSING",
  LTF_OPTIMIZE_METRICS = "LTF_OPTIMIZE_METRICS",
  LTF_OPTIMIZE_ZORDER = "LTF_OPTIMIZE_ZORDER",
  LTF_TRUNCATE_STOPPED = "LTF_TRUNCATE_STOPPED",
  LTF_TRUNCATE_PROCESSING = "LTF_TRUNCATE_PROCESSING",
  LTF_VACUUM_STOPPED = "LTF_VACUUM_STOPPED",
  LTF_VACUUM_PROCESSING = "LTF_VACUUM_PROCESSING",
  LTF_RESOURCE_CONSUMPTION = "LTF_RESOURCE_CONSUMPTION",
  LTF_RESOURCE_ID = "LTF_RESOURCE_ID",
  LTF_ETV_FIND_AND_REPLACE_STARTING = "LTF_ETV_FIND_AND_REPLACE_STARTING",
  LTF_ETV_FIND_AND_REPLACE_STARTED = "LTF_ETV_FIND_AND_REPLACE_STARTED",
  LTF_ETV_FIND_AND_REPLACE_FAILED_TO_START = "LTF_ETV_FIND_AND_REPLACE_FAILED_TO_START",
  LTF_ETV_FIND_AND_REPLACE_COMPLETED = "LTF_ETV_FIND_AND_REPLACE_COMPLETED",
  LTF_ETV_FIND_AND_REPLACE_STOPPED = "LTF_ETV_FIND_AND_REPLACE_STOPPED",
  LTF_ETV_FIND_AND_REPLACE_ERROR = "LTF_ETV_FIND_AND_REPLACE_ERROR",
  LOCAL_TABLE_NOT_DEPLOYED = "LOCAL_TABLE_NOT_DEPLOYED",
  LOCAL_TABLE_NO_DELTA_CAPTURE = "LOCAL_TABLE_NO_DELTA_CAPTURE",
  LOCAL_TABLE_NO_FILE_STORAGE = "LOCAL_TABLE_NO_FILE_STORAGE",
  LOCAL_TABLE_NO_HANA_STORAGE = "LOCAL_TABLE_NO_HANA_STORAGE",
}

export interface ITableNameUser {
  tableName: string;
  user: string;
}
