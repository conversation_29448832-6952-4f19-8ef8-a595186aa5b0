/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [DataIntegration.RemoteTableBackend]
 */

export enum AuthTypes {
  // Added just for testing purposes until x509 becomes available
  Basic = "Basic",

  X509 = "X509",
}

export interface BasicAuth {
  schemaName: string;
  authType: AuthTypes.Basic;
  host: string;
  port: number;
  user: string;

  password: string;
}

export interface X509Auth {
  schemaName: string;
  authType: AuthTypes.X509;
  host: string;
  port: number;
  user: string;

  cert: string;
  key: string;

  certSubject?: string;
  certIssuer?: string;
}
export type DatasphereAuthBody = BasicAuth | X509Auth;
