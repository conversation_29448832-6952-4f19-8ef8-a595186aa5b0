/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [DataIntegration.RemoteTableBackend]
 */

import { ISacRequestContext, ISecureStore } from "@sap/dwc-credentials";
import { basename } from "path";
import { getLogger } from "../../logger";
import { SecureStore } from "../../securestore";
import { CredStoreEntryNotFoundError, MissingFieldError, UnsupportedAuthTypeError } from "./errors";
import { AuthTypes, BasicAuth, DatasphereAuthBody, X509Auth } from "./types";

const { logInfoWithOptionalContext, logWarningWithOptionalContext } = getLogger(basename(__filename));
export const STORE_NAME = "datasphere-metrics-credstore"; // SCHEMA
export const STORE_KEY = "datasphere-metrics"; // PATH

/**
 * Service to manage Datasphere Metrics credential inserting and fetching
 * Credentilas are stored in the CredStore under the schema(store) `STORE_NAME`
 *                                                   and path(key) `STORE_KEY`
 *
 * *** Basic Auth Type - Added just for testing purposes until X509 becomes available
 */
export class DatasphereMetricsCredentialsService {
  private readonly store: ISecureStore;

  constructor() {
    // Secure Store requires a context, but in our case we are not in a tenant scope; So we create one
    const context: ISacRequestContext = { tenantId: null };
    this.store = SecureStore.fromStoreName(context, STORE_NAME);
  }

  /**
   * Inserts or updates the given Datasphere Metrics credentials into the CredStore.
   *
   * @param body credentials object:
   *   - For Basic: { schemaName, authType: Basic, host, port, user, password }
   *         *** Added just for testing purposes until X509 becomes available
   *   - For X509:  { schemaName, authType: X509 , host, port, cert, key, [passphrase] }
   *
   * @throws {MissingFieldError}             if required fields are missing
   * @throws {UnsupportedAuthTypeError}      if the stored `authType` isn’t recognized
   */
  async upsert(body: DatasphereAuthBody): Promise<void> {
    const toStore = this.verifyCredentials(body);
    await this.store.insert(STORE_KEY, toStore);
    logInfoWithOptionalContext(`Upserted ${toStore.authType} Datasphere Metrics credentials to ${STORE_KEY}.`);
  }

  /**
   * Fetches and validates the Datasphere Metrics credentials from CredStore
   * Credentials were stored through the support route in the CredStore under the key(path) `STORE_KEY`.
   *
   * @throws {CredStoreEntryNotFoundError}   if there is nothing under the path
   * @throws {MissingFieldError}             if required fields are missing
   * @throws {UnsupportedAuthTypeError}      if the stored `authType` isn’t recognized
   *
   * @returns fetched credentials object
   */
  async fetch(): Promise<DatasphereAuthBody> {
    const toFetch = await this.store.retrieve<Partial<DatasphereAuthBody>>(STORE_KEY);

    if (!toFetch) {
      logWarningWithOptionalContext(`No Datasphere Metrics credentials found at ${STORE_KEY}.`);
      throw new CredStoreEntryNotFoundError(STORE_KEY);
    }
    logInfoWithOptionalContext(`Fetched ${toFetch.authType} Datasphere Metrics credentials from ${STORE_KEY}.`);
    return this.verifyCredentials(toFetch);
  }

  private verifyCredentials(creds: Partial<DatasphereAuthBody>): DatasphereAuthBody {
    const rawType = creds.authType?.trim();
    if (!creds || !rawType || !creds.host || creds.port === undefined || !creds.schemaName) {
      throw new MissingFieldError();
    }

    const normalized = rawType.toLowerCase();
    if (normalized === AuthTypes.Basic.toLowerCase()) {
      const basic = creds as Partial<BasicAuth>;

      if (!basic.user || !basic.password) {
        throw new MissingFieldError(AuthTypes.Basic);
      }
      return {
        schemaName: creds.schemaName,
        authType: AuthTypes.Basic,
        host: creds.host,
        port: creds.port,
        user: basic.user,
        password: basic.password,
      };
    }
    if (normalized === AuthTypes.X509.toLowerCase()) {
      const x509 = creds as Partial<X509Auth>;
      if (!x509.cert || !x509.key) {
        throw new MissingFieldError(AuthTypes.X509);
      }
      const result = {
        schemaName: creds.schemaName,
        authType: AuthTypes.X509,
        host: creds.host,
        port: creds.port,
        cert: x509.cert,
        key: x509.key,
      } as X509Auth;

      if (x509.passphrase) {
        result.passphrase = x509.passphrase;
      }
      return result;
    }
    throw new UnsupportedAuthTypeError(rawType);
  }
}
