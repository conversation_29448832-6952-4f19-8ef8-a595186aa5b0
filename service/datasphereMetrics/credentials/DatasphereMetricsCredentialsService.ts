/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [DataIntegration.RemoteTableBackend]
 */

import { ISacRequestContext, ISecureStore } from "@sap/dwc-credentials";
import { pki } from "node-forge";
import { basename } from "path";
import { getLogger } from "../../logger";
import { formatSubjectOrIssuer } from "../../routes/support/telemetryDatasphereMetrics";
import { SecureStore } from "../../securestore";
import { CredStoreEntryNotFoundError, CredentialBaseError } from "./errors";
import { createUserCertificate } from "./identity";
import { AuthTypes, DatasphereAuthBody, X509Auth } from "./types";
const { logInfoWithOptionalContext, logWarningWithOptionalContext } = getLogger(basename(__filename));
export const STORE_NAME = "datasphere-metrics-credstore"; // SCHEMA
export const STORE_KEY = "datasphere-metrics"; // PATH
const CERT_REFRESH_THRESHOLD_MS = 3 * 24 * 60 * 60 * 1000; // 3 days
/**
 * Simple service for storing/fetching fully-validated Datasphere Metrics credentials.
 * All validation and certificate creation initially done before calling this service. (Except when refreshing the certificate )
 */
export class DatasphereMetricsCredentialsService {
  private readonly store: ISecureStore;

  constructor() {
    // Secure Store requires a context
    // This is not tenant-scoped, so tenantId: null
    const context: ISacRequestContext = { tenantId: null };
    this.store = SecureStore.fromStoreName(context, STORE_NAME);
  }

  /**
   * Inserts or updates the given Datasphere Metrics credentials in the CredStore.
   * Expects a fully validated and ready-to-store object (see types).
   */
  async upsert(body: DatasphereAuthBody): Promise<void> {
    await this.store.insert(STORE_KEY, body);
    logInfoWithOptionalContext(`Upserted ${body.authType} Datasphere Metrics credentials to ${STORE_KEY}`);
  }

  /**
   * Fetches the Datasphere Metrics credentials from the CredStore.
   * Returns the stored object, or throws if not found.
   */
  async fetch(): Promise<DatasphereAuthBody> {
    const toFetch = await this.store.retrieve<Partial<DatasphereAuthBody>>(STORE_KEY);

    if (!toFetch) {
      logWarningWithOptionalContext(`No Datasphere Metrics credentials found at ${STORE_KEY}`);
      throw new CredStoreEntryNotFoundError(STORE_KEY);
    }
    if (toFetch.authType === AuthTypes.X509) {
      return await this.ensureValidX509(toFetch as X509Auth);
    }

    logInfoWithOptionalContext(`Fetched ${toFetch.authType} Datasphere Metrics credentials from ${STORE_KEY}`);
    return toFetch as DatasphereAuthBody;
  }

  /**
   * Ensures that the given X.509 credential is present, valid, and not expiring soon.
   * If not, generates a new cert&key, and returns the updated object.
   */
  private async ensureValidX509(creds: X509Auth): Promise<X509Auth> {
    const hasNoCert = !creds.cert || !creds.key;
    let expiresSoon = false;
    if (!creds.user) {
      throw new CredentialBaseError(`Cannot generate X.509 certificate: missing 'user' for schema ${creds.schemaName}`);
    }

    if (!hasNoCert) {
      try {
        const certObj = pki.certificateFromPem(creds.cert);
        expiresSoon = certObj.validity.notAfter.valueOf() <= Date.now() + CERT_REFRESH_THRESHOLD_MS;
      } catch (err) {
        expiresSoon = true;
        logWarningWithOptionalContext(
          `Failed to parse existing X.509 certificate for Datasphere Metrics for ${creds.schemaName}#${creds.user} (${err.message}), generating new certificate`
        );
      }
    }

    if (!hasNoCert && !expiresSoon) {
      return creds;
    }

    let userKeyPair;
    try {
      userKeyPair = await createUserCertificate(creds.user);
    } catch (err) {
      logWarningWithOptionalContext([
        `Failed to create X.509 certificate for ${creds.schemaName}#${creds.user}: ${err.message}`,
        err,
      ]);
      throw new CredentialBaseError("X.509 certificate generation failed");
    }

    const certChain = userKeyPair.certificates?.join("\n");

    if (!certChain || !userKeyPair.key) {
      logWarningWithOptionalContext(`Failed to generate X.509 cert or key for ${creds.schemaName}#${creds.user}`);
      throw new CredentialBaseError("Failed to generate X.509 certificate or key");
    }
    const cert = pki.certificateFromPem(userKeyPair.certificates[0]);
    const subject = formatSubjectOrIssuer(cert, true);
    const issuer = formatSubjectOrIssuer(cert, false);
    logInfoWithOptionalContext(`${subject}`);
    logInfoWithOptionalContext(`${issuer}`);

    const updated: X509Auth = {
      ...creds,
      cert: certChain,
      key: userKeyPair.key,
      certSubject: subject,
      certIssuer: issuer,
    };

    await this.store.insert(STORE_KEY, updated);
    logInfoWithOptionalContext(`Generated new X.509 cert for ${creds.user}`);
    return updated;
  }
}
