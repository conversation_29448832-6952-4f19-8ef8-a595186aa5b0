/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [DataIntegration.RemoteTableBackend]
 */

import { ExportResult, ExportResultCode } from "@opentelemetry/core";
import {
  AggregationTemporality,
  AggregationTemporalitySelector,
  InstrumentType,
  PushMetricExporter,
  ResourceMetrics,
} from "@opentelemetry/sdk-metrics";
import { basename } from "path";
import { getLogger } from "../../logger";
import { DatasphereMetricsPusher } from "./DatasphereMetricsPusher";
const { logWarningWithOptionalContext, logInfoWithOptionalContext } = getLogger(basename(__filename));

export class DatasphereMetricsExporter implements PushMetricExporter {
  protected shutdownFlag = false;
  protected temporalitySelector: AggregationTemporalitySelector;
  private readonly pusher: DatasphereMetricsPusher;
  private static readonly DEFAULT_SELECTOR = (_instrumentType: InstrumentType) => AggregationTemporality.DELTA;

  constructor(options?: { temporalitySelector?: AggregationTemporalitySelector }, pusher?: DatasphereMetricsPusher) {
    this.temporalitySelector = options?.temporalitySelector ?? DatasphereMetricsExporter.DEFAULT_SELECTOR;
    this.pusher = pusher ?? new DatasphereMetricsPusher();
  }

  export(metrics: ResourceMetrics, resultCallback: (result: ExportResult) => void): void {
    if (this.shutdownFlag) {
      resultCallback({ code: ExportResultCode.FAILED });
      return;
    }
    try {
      const tables = DatasphereMetricsPusher.formatResourceMetrics(metrics);

      const rowSummary = tables.map((t) => `${t.tableName}: ${t.data.length} rows`).join("; ");
      logInfoWithOptionalContext([`Exporter pushing tables:\n${rowSummary}`]);
      this.pusher
        .push(tables)
        .then(() => {
          logInfoWithOptionalContext([`Exporter succeeded: Pushed tables: ${rowSummary}`]);
          resultCallback({ code: ExportResultCode.SUCCESS });
        })
        .catch((err) => {
          logWarningWithOptionalContext(["Exporter failed (async)", err]);
          resultCallback({ code: ExportResultCode.FAILED });
        });
    } catch (err) {
      logWarningWithOptionalContext(["Exporter failed (sync)", err]);
      resultCallback({ code: ExportResultCode.FAILED });
    }
  }

  forceFlush(): Promise<void> {
    return this.pusher.flush();
  }

  selectAggregationTemporality(_instrumentType: InstrumentType): AggregationTemporality {
    return this.temporalitySelector(_instrumentType);
  }

  async shutdown(): Promise<void> {
    this.shutdownFlag = true;
    await this.pusher.flush();
  }
}
