/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [DataIntegration.RemoteTableBackend]
 */

import { ResourceMetrics } from "@opentelemetry/sdk-metrics";
import { basename } from "path";
import { getLogger } from "../../logger";
import { CredentialBaseError } from "../credentials/errors";
import { ConnectionBaseError, DataValidationBaseError, QueryExecutionBaseError } from "./errors";
import { DatasphereMetricsIngestor } from "./hana/DatasphereMetricsIngestor";
import { HANAConnector } from "./hana/HANAConnector";
import { Table } from "./types";
const { logInfoWithOptionalContext, logWarningWithOptionalContext, logErrorWithOptionalContext } = getLogger(
  basename(__filename)
);

export class DatasphereMetricsPusher {
  private db: DatasphereMetricsIngestor;
  private connector: HANAConnector;

  // Exposed for testing - Wrappers
  protected logInfo = logInfoWithOptionalContext;
  protected logWarning = logWarningWithOptionalContext;
  protected logError = logErrorWithOptionalContext;

  constructor(connector = new HANAConnector()) {
    this.connector = connector;
  }

  /**
   * Formats incoming OpenTelemetry ResourceMetrics into Tables.
   */
  public static formatResourceMetrics(resourceMetrics: ResourceMetrics): Table[] {
    const tables: Record<string, { headers: Set<string>; rows: Array<Record<string, string | null>> }> = {};
    const resourceAttributes = resourceMetrics.resource?.attributes ?? {};

    // For logging
    let inCount = 0;
    let outCount = 0;

    for (const scopeMetric of resourceMetrics.scopeMetrics) {
      for (const metric of scopeMetric.metrics) {
        const tableName = metric.descriptor.name;

        if (!tables[tableName]) {
          tables[tableName] = { headers: new Set(), rows: [] };
        }

        const table = tables[tableName];

        for (const dataPoint of metric.dataPoints) {
          inCount++;
          try {
            const metricValue = dataPoint.value;
            const endTimeArr = dataPoint.endTime;
            if (
              !Array.isArray(endTimeArr) ||
              endTimeArr.length !== 2 ||
              typeof endTimeArr[0] !== "number" ||
              typeof endTimeArr[1] !== "number"
            ) {
              logWarningWithOptionalContext("Invalid data point: endTime not in [seconds, nanos] form");
              continue;
            }
            const endTime = Math.floor((endTimeArr[0] * 1e9 + endTimeArr[1]) / 1e6);
            if (endTime === null || Number.isNaN(endTime)) {
              logWarningWithOptionalContext("Invalid data point: endTime is null or NaN");
              continue;
            }

            if (metricValue === null || metricValue === undefined) {
              logWarningWithOptionalContext("Invalid data point: value is null or undefined");
              continue;
            }
            const row: Record<string, string | null> = {};

            if (typeof resourceAttributes === "object" && resourceAttributes !== null) {
              for (const key in resourceAttributes) {
                if (key === null || key === undefined || key === "") {
                  logWarningWithOptionalContext("Resource attribute with invalid key: null or undefined");
                  continue;
                }
                const value = resourceAttributes[key];
                if (value !== null && value !== undefined) {
                  row[key] = String(value);
                  table.headers.add(key);
                }
              }
            } else {
              logWarningWithOptionalContext("Resource attributes not an object");
            }

            if (dataPoint.attributes && typeof dataPoint.attributes === "object") {
              for (const key in dataPoint.attributes) {
                if (key === null || key === undefined || key === "") {
                  logWarningWithOptionalContext("Data point attribute with invalid key: null or undefined");
                  continue;
                }
                const value = dataPoint.attributes[key];
                if (value === undefined) {
                  continue;
                }
                if (value === null) {
                  row[key] = null;
                  table.headers.add(key);
                  continue;
                }
                if (typeof value === "object") {
                  try {
                    row[key] = JSON.stringify(value);
                    table.headers.add(key);
                  } catch (err) {
                    logWarningWithOptionalContext("Invalid data point: value could not be serialized");
                  }
                  continue;
                }
                row[key] = String(value);
                table.headers.add(key);
              }
            }
            try {
              row.value = typeof metricValue === "object" ? JSON.stringify(metricValue) : String(metricValue);
            } catch (err) {
              logWarningWithOptionalContext(["Invalid data point: value could not be serialized", err]);
              continue;
            }
            try {
              row.timestamp = new Date(endTime).toISOString().replace("T", " ").replace("Z", "");
            } catch (err) {
              logWarningWithOptionalContext(["Invalid data point: timestamp formatting failed", err]);
              continue;
            }
            table.headers.add("value");
            table.headers.add("timestamp");

            table.rows.push(row);
          } catch (err) {
            logWarningWithOptionalContext(["Failed: invalid data point", err]);
          }
        }
      }
    }

    const finalTables: Table[] = [];

    for (const [tableName, { headers, rows }] of Object.entries(tables)) {
      const headerArray: string[] = Array.from(headers);

      if (headerArray.some((col) => col === null || col === undefined || col === "")) {
        logWarningWithOptionalContext(
          `Skipping table '${tableName}' due to invalid header columns: some columns are null or undefined`
        );
        continue;
      }

      const data: Array<Array<string | null>> = [headerArray];

      for (const row of rows) {
        const rowArray = headerArray.map((col) => {
          const x = row[col];
          return x === undefined || x === null || x === "" ? null : x;
        });
        data.push(rowArray);
      }

      finalTables.push({ tableName, data });
    }

    outCount = finalTables.reduce((sum, t) => sum + (t.data.length - 1), 0);

    logInfoWithOptionalContext([
      `DatasphereMetrics formatting function: input=${inCount}, output=${outCount}, dropped=${inCount - outCount}`,
    ]);

    return finalTables;
  }

  /**
   * Relies on db.inserAll to handle creation and insertion for each table.
   */
  public async push(toPush: Table[]): Promise<void> {
    try {
      const { connection, schemaName } = await this.connector.connect();
      this.db = new DatasphereMetricsIngestor(connection, schemaName);

      const result = await this.db.insertAll(toPush);

      this.logInfo(`Metrics push finished: ${result.insertedCount} succeeded, ${result.failed.length} failed.`);

      if (result.failed.length > 0) {
        this.logWarning(
          `Failed tables and respective errors ->\n${result.failed
            .map((f) => `${f.name}: ${f.error.message}`)
            .join("\n")}`
        );
      }
    } catch (err) {
      if (err instanceof CredentialBaseError) {
        this.logWarning(["Push failed: credential issue.", err]);
      } else if (err instanceof ConnectionBaseError) {
        this.logWarning(["Push failed: connection issue.", err]);
      } else if (err instanceof QueryExecutionBaseError) {
        this.logWarning(["Push failed: query execution issue.", err]);
      } else if (err instanceof DataValidationBaseError) {
        this.logWarning(["Push failed: data validation issue.", err]);
      } else {
        this.logError(["Push failed: unexpected error.", err]);
      }
    } finally {
      await this.connector.disconnect();
    }
  }

  public async flush(): Promise<void> {
    // No-op
    return;
  }
}
