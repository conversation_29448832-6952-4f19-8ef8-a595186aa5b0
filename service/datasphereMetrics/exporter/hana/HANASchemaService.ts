/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [DataIntegration.RemoteTableBackend]
 */

import { Connection, sql, SqlTaggedTemplate } from "@sap/prom-hana-client";
import { basename } from "path";
import { getLogger } from "../../../logger";
import { DataValidationBaseError, QueryExecutionBaseError } from "../errors";
import { Table } from "../types";
import { HANAQueryBuilder } from "./HANAQueryBuilder";

const { logInfoWithOptionalContext, logWarningWithOptionalContext } = getLogger(basename(__filename));

export class HANASchemaService {
  private readonly tableExistsCache = new Set<string>();
  private readonly columnCache: Map<string, Map<string, string>> = new Map();

  constructor(private readonly connection: Connection, private readonly schema: string) {}

  async getExistingColumns(tableName: string): Promise<Map<string, string>> {
    if (this.columnCache.has(tableName)) {
      return this.columnCache.get(tableName)!;
    }

    const query = HANAQueryBuilder.buildFetchExistingColumnsQuery(this.schema, tableName);
    try {
      const result = await this.connection.exec<{ COLUMN_NAME: string; DATA_TYPE_FULL: string }>(query);
      if (!result?.length) {
        logWarningWithOptionalContext(`No columns found for table "${tableName}".`);
      }
      const columnMap = new Map(result.map((r) => [r.COLUMN_NAME, r.DATA_TYPE_FULL]));
      this.columnCache.set(tableName, columnMap);
      return columnMap;
    } catch (err) {
      throw new QueryExecutionBaseError(`Failed to fetch existing columns for table: ${tableName}.`, err);
    }
  }

  /**
   * Ensures the table has all columns present in the provided header row.
   * Does NOT pad data rows.
   * All new columns are created with DEFAULT NULL.
   */
  async addMissingColumns(table: Table): Promise<void> {
    const headerRow = table.data[0];

    if (headerRow.some((col) => col === null || col === undefined || col === "")) {
      throw new DataValidationBaseError(
        `Header row for table "${table.tableName}" is invalid:  some columns are null or undefined`
      );
    }
    const headerRowStr = headerRow as string[];

    let existingColumns: Map<string, string>;
    try {
      existingColumns = await this.getExistingColumns(table.tableName);
    } catch (err) {
      logWarningWithOptionalContext([`Failed to fetch existing columns for ${table.tableName}.`, err]);
      throw err;
    }
    const alterStatements: SqlTaggedTemplate[] = [];

    for (const col of headerRowStr) {
      const expectedType =
        col === "timestamp"
          ? `TIMESTAMP`
          : `${HANAQueryBuilder.DEFAULT_COLUMN_TYPE}(${HANAQueryBuilder.DEFAULT_STRING_COLUMN_LENGTH})`;
      // checking if existing column in the table has expected type, to avoid QueryExecutionBaseError in ALTER later
      if (existingColumns.has(col)) {
        const existingType = (existingColumns.get(col) || "").toUpperCase();
        const expectedNormalized = expectedType.toUpperCase();

        if (existingType !== expectedNormalized) {
          logWarningWithOptionalContext(
            `Skipping column "${col}" in table "${table.tableName}": existing type "${existingType}", expected "${expectedNormalized}".`
          );
        }
        continue;
      }
      alterStatements.push(
        col === "timestamp"
          ? sql`${sql.quoted(col)} TIMESTAMP DEFAULT NULL`
          : sql`${sql.quoted(col)} NVARCHAR(1000) DEFAULT NULL`
      );
      existingColumns.set(col, expectedType);
    }

    if (alterStatements.length === 0) {
      return;
    }

    const alterSQL = HANAQueryBuilder.buildAlterTableQuery(this.schema, table.tableName, alterStatements);

    try {
      await this.connection.exec(alterSQL);
    } catch (err) {
      throw new QueryExecutionBaseError(`Failed to alter table "${table.tableName}"`, err);
    }
    this.columnCache.set(table.tableName, existingColumns);
  }

  private validateTableData(table: Table): void {
    if (
      !table.data ||
      !Array.isArray(table.data) ||
      table.data.length === 0 ||
      !Array.isArray(table.data[0]) ||
      table.data[0].length === 0
    ) {
      throw new DataValidationBaseError(`Table "${table.tableName}" must include a header row.`);
    }
  }

  async ensureTableExistsOrCreate(table: Table): Promise<void> {
    if (this.tableExistsCache.has(table.tableName)) {
      logInfoWithOptionalContext(`Table ${table.tableName} already exists.`);
      return;
    }

    const existsQuery = HANAQueryBuilder.buildTableExistsQuery(this.schema, table.tableName);
    let exists = false;
    try {
      const result = await this.connection.exec<{ COUNT: number }>(existsQuery);
      exists = result?.[0]?.COUNT > 0;
    } catch (err) {
      throw new QueryExecutionBaseError(`Failed to ensure table "${table.tableName}" exists`, err);
    }

    if (exists) {
      logInfoWithOptionalContext(`Table ${table.tableName} already exists.`);
      this.tableExistsCache.add(table.tableName);
      return;
    }

    this.validateTableData(table);
    const headerRow = table.data[0];

    if (headerRow.some((col) => col === null || col === undefined || col === "")) {
      throw new DataValidationBaseError(
        `Header row for table "${table.tableName}" is invalid: some columns are null or undefined`
      );
    }
    const headerRowStr = headerRow as string[];
    const createSQL = HANAQueryBuilder.buildCreateTableQuery(this.schema, table.tableName, headerRowStr).sql;
    try {
      await this.connection.exec(createSQL);
      this.tableExistsCache.add(table.tableName);
      logInfoWithOptionalContext(`Created table ${table.tableName}.`);
    } catch (err) {
      throw new QueryExecutionBaseError(`Failed to create table "${table.tableName}".`, err);
    }
  }

  clearCache(tableName: string): void {
    this.tableExistsCache.delete(tableName);
    this.columnCache.delete(tableName);
  }
}
