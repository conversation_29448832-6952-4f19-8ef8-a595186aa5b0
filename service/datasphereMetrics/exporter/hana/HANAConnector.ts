/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [DataIntegration.RemoteTableBackend]
 */
import { Connection, createConnection, IConnectionOptions } from "@sap/prom-hana-client";
import { basename } from "path";
import { getLogger } from "../../../logger";
import { DatasphereMetricsCredentialsService } from "../../credentials/DatasphereMetricsCredentialsService";
import { CredentialBaseError } from "../../credentials/errors";
import { AuthTypes, DatasphereAuthBody } from "../../credentials/types";
import { ConnectionBaseError } from "../errors";
import { Backoff, JitterStrategy } from "./utils/Backoff";
const { logInfoWithOptionalContext, logErrorWithOptionalContext, logWarningWithOptionalContext } = getLogger(
  basename(__filename)
);
// Exposed for testing - Wrapper
export const _createConnectionWrapper = {
  createConnection: () => createConnection(),
};

export class HANAConnector {
  private connection: Connection | undefined = undefined;
  private credentialsService = new DatasphereMetricsCredentialsService();
  private readonly maxRetries = 3;
  private backoff = new Backoff({
    startAfter: 1,
    initialDelay: 500,
    maxDelay: 10000,
    factor: 2,
    jitter: JitterStrategy.Equal,
  });
  private schemaName: string;
  private user: string;

  async connect(): Promise<{ connection: Connection; schemaName: string; user: string }> {
    if (this.connection) {
      return { connection: this.connection, schemaName: this.schemaName, user: this.user };
    }
    try {
      this.connection = _createConnectionWrapper.createConnection();
      this.connection.setAutoCommit(false);
    } catch (err) {
      logWarningWithOptionalContext(["Failed to initialize connection instance", err]);
      throw new ConnectionBaseError("Failed to initialize connection instance", err);
    }

    let creds: DatasphereAuthBody;
    try {
      creds = await this.credentialsService.fetch();
    } catch (err) {
      if (err instanceof CredentialBaseError) {
        logWarningWithOptionalContext(["Credential error while connecting to Datasphere", err]);
        throw err;
      }
      logErrorWithOptionalContext(["Unexpected error while connecting to Datasphere", err]);
      throw new ConnectionBaseError("Failed to fetch Datasphere credentials due to an unexpected error", err);
    }
    this.schemaName = creds.schemaName;
    this.user = creds.user;

    const params: IConnectionOptions = this.getConnectionParams(creds);
    for (let i = 1; i <= this.maxRetries; i++) {
      try {
        await this.connection.connect(params);
        this.backoff.reset();
        logInfoWithOptionalContext("Connected to Datasphere.");
        return { connection: this.connection, schemaName: this.schemaName, user: this.user };
      } catch (err) {
        if (i < this.maxRetries) {
          const delay = await this.backoff.trigger();
          logWarningWithOptionalContext([`Try ${i}; Retrying to connect in ${Math.round(delay)} ms...`, err]);
        } else {
          throw new ConnectionBaseError(`Try ${i}; Failed to connect to Datasphere`, err);
        }
      }
    }
    throw new ConnectionBaseError("Failed to connect to Datasphere");
  }

  private getConnectionParams(creds: DatasphereAuthBody): IConnectionOptions {
    if (creds.authType === AuthTypes.X509) {
      return {
        host: creds.host,
        port: creds.port,
        // Provides a string containing the X509 certificate or the name of a file containing the X509 certificate.
        // The certificate must be PEM-encoded in PKCS8 format. Defaults to the existing value of sslKeyStore, if specified.
        // https://help.sap.com/docs/SAP_HANA_CLIENT/f1b440ded6144a54ada97ff95dac7adf/4fe9978ebac44f35b9369ef5a4a26f4c.html
        authenticationX509: `${creds.key}\n${creds.cert}`,
      };
    } else {
      // Added just for testing purposes until x509 becomes available
      return {
        host: creds.host,
        port: creds.port,
        user: `${creds.schemaName}#${creds.user}`,
        password: creds.password,
      };
    }
  }

  async disconnect(): Promise<void> {
    if (!this.connection) {
      return;
    }
    try {
      await this.connection.close();
      logInfoWithOptionalContext("Disconnected from Datasphere.");
    } catch (err) {
      logWarningWithOptionalContext(["Error while disconnecting from Datasphere", err]);
    } finally {
      this.connection = undefined;
    }
  }
}
