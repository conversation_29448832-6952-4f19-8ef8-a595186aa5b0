/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [DataIntegration.RemoteTableBackend]
 */

import { sql, SqlTaggedTemplate } from "@sap/prom-hana-client";
import { DataValidationBaseError } from "../errors";

export class HANAQueryBuilder {
  public static readonly DEFAULT_STRING_COLUMN_LENGTH = 1000;
  public static readonly DEFAULT_COLUMN_TYPE = `NVARCHAR`;

  static buildInsertQuery(
    schema: string,
    tableName: string,
    columnNames: string[],
    dataRows: Array<Array<string | number | null>>
  ) {
    const safeColumns = columnNames.map((col) => sql.quoted(col));
    const columnSQL = sql.join(safeColumns, sql`, `);

    const selectStatements = dataRows.map((row) => {
      if (row.length !== columnNames.length) {
        throw new DataValidationBaseError("Inconsistent metrics row, has more columns than the header.");
      }
      const values = row.map((value, idx) => {
        const col = columnNames[idx];

        if (value === null || value === undefined || value === "") {
          return sql.literal(null);
        }

        if (col === "timestamp") {
          const timestampNUM = Number(value);
          if (!isNaN(timestampNUM)) {
            const formatted = new Date(timestampNUM).toISOString().replace("T", " ").replace("Z", "");
            return sql.literal(formatted);
          }

          const date = new Date(value);
          if (!isNaN(date.getTime())) {
            const formatted = date.toISOString().split(".")[0].replace("T", " ");
            return sql.literal(formatted);
          }
        }

        return sql.literal(value);
      });

      return sql`SELECT ${sql.join(values, sql`, `)} FROM DUMMY`;
    });

    const unionSQL = sql.join(selectStatements, sql`\nUNION ALL\n`);
    return sql`INSERT INTO ${sql.quoted(schema)}.${sql.quoted(tableName)} (${columnSQL}) ${unionSQL};`;
  }

  static buildSelectMaxTimestampQuery(schema: string, tableName: string) {
    return sql`
      SELECT COALESCE(MAX("timestamp"), '1970-01-01 00:00:00') AS "latestTimestamp"
      FROM ${sql.quoted(schema)}.${sql.quoted(tableName)}`;
  }

  static buildCreateTableQuery(schema: string, tableName: string, columns: string[]) {
    const columnDefs = columns.map((col) => {
      if (col === "timestamp") {
        return sql`${sql.quoted(col)} TIMESTAMP`;
      }
      return sql`${sql.quoted(col)} NVARCHAR(1000) DEFAULT NULL`;
    });

    const currentYear = new Date().getFullYear();
    return sql`
      CREATE TABLE ${sql.quoted(schema)}.${sql.quoted(tableName)} (
        "Id" BIGINT GENERATED BY DEFAULT AS IDENTITY,
        ${sql.join(columnDefs, sql`, `)},
        PRIMARY KEY ("Id", "timestamp")
      )
      PARTITION BY RANGE (YEAR("timestamp")) (
        PARTITION VALUES = ${sql.literal(currentYear)},
        PARTITION OTHERS DYNAMIC INTERVAL 1 YEAR);`;
  }

  static buildAlterTableQuery(schema: string, tableName: string, alterStatements: SqlTaggedTemplate[]) {
    return sql`
      ALTER TABLE ${sql.quoted(schema)}.${sql.quoted(tableName)}
      ADD (${sql.join(alterStatements, sql`, `)});`;
  }

  static buildTableExistsQuery(schema: string, tableName: string) {
    return sql`
      SELECT COUNT(*) AS COUNT FROM M_TABLES
      WHERE TABLE_NAME = ${sql.literal(tableName)}
      AND SCHEMA_NAME = ${sql.literal(schema)}`;
  }

  /**
   *
   * DATA_TYPE_FULL explained:
   *
   * DECIMAL / NUMERIC types with scale -> DATA_TYPE_NAME(LENGTH, SCALE)
   * DECIMAL types without scale (just precision) -> DATA_TYPE_NAME(LENGTH)
   * String types with length -> DATA_TYPE_NAME(LENGTH)
   * All others -> DATA_TYPE_NAME
   *
   */
  static buildFetchExistingColumnsQuery(schema: string, tableName: string) {
    return sql`
      SELECT
      COLUMN_NAME,
      CASE
        WHEN DATA_TYPE_NAME IN ('DECIMAL', 'NUMERIC', 'FIXED') AND SCALE IS NOT NULL THEN
          DATA_TYPE_NAME || '(' || LENGTH || ',' || SCALE || ')'

        WHEN DATA_TYPE_NAME IN ('DECIMAL', 'NUMERIC', 'FIXED') THEN
          DATA_TYPE_NAME || '(' || LENGTH || ')'

        WHEN DATA_TYPE_NAME IN ('NVARCHAR', 'VARCHAR', 'CHAR', 'NCHAR') AND LENGTH IS NOT NULL THEN
          DATA_TYPE_NAME || '(' || LENGTH || ')'

        ELSE
          DATA_TYPE_NAME
         END AS DATA_TYPE_FULL
      FROM TABLE_COLUMNS
      WHERE SCHEMA_NAME = ${sql.literal(schema)}
      AND TABLE_NAME = ${sql.literal(tableName)}`;
  }
}
