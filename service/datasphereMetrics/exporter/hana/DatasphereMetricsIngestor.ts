/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [DataIntegration.RemoteTableBackend]
 */

import { Connection } from "@sap/prom-hana-client";
import { basename } from "path";
import { getLogger } from "../../../logger";
import { DataValidationBaseError, QueryExecutionBaseError } from "../errors";
import { Table } from "../types";
import { HANAQueryBuilder } from "./HANAQueryBuilder";
import { HANASchemaService } from "./HANASchemaService";

const { logInfoWithOptionalContext, logWarningWithOptionalContext } = getLogger(basename(__filename));

export class DatasphereMetricsIngestor {
  private readonly schemaService: HANASchemaService;

  constructor(private readonly connection: Connection, private readonly schema: string) {
    this.schemaService = new HANASchemaService(connection, schema);
  }

  async insertAll(tables: Table[]): Promise<{ insertedCount: number; failed: Array<{ name: string; error: Error }> }> {
    const failures: Array<{ name: string; error: Error }> = [];

    for (const table of tables) {
      try {
        await this.schemaService.ensureTableExistsOrCreate(table);
        await this.schemaService.addMissingColumns(table);
        await this.insert(table);
        await this.connection.exec("COMMIT");
      } catch (err) {
        await this.connection.exec("ROLLBACK");
        logWarningWithOptionalContext([`Insert failed for table ${table.tableName}, rolled back.`, err]);
        failures.push({ name: table.tableName, error: err });
      }
    }
    return {
      insertedCount: tables.length - failures.length,
      failed: failures,
    };
  }

  private async insert(table: Table): Promise<void> {
    if (!Array.isArray(table.data) || table.data.length < 2) {
      throw new DataValidationBaseError("Table data must include a header and at least one row.");
    }

    const headerRow = table.data[0];
    if (!Array.isArray(headerRow) || !headerRow.includes("timestamp")) {
      throw new DataValidationBaseError("Table must contain 'timestamp' column.");
    }
    if (headerRow.some((col) => col === null || col === undefined || col === "")) {
      throw new DataValidationBaseError("Header row must not contain null or undefined values.");
    }

    const timestampIndex = headerRow.indexOf("timestamp");

    const query = HANAQueryBuilder.buildSelectMaxTimestampQuery(this.schema, table.tableName);

    let latestTimestamp = new Date("1970-01-01T00:00:00Z");
    try {
      const result = await this.connection.exec<{ latestTimestamp: string }>(query);
      latestTimestamp = new Date(result[0]?.latestTimestamp || "1970-01-01T00:00:00Z");
    } catch (err) {
      logWarningWithOptionalContext(["Failed to fetch latest timestamp, inserting anyway.", err]);
    }

    const rowsToInsert: Array<Array<string | number | null>> = [];

    for (let i = 1; i < table.data.length; i++) {
      const row = table.data[i];
      let timestamp = row[timestampIndex];
      if (timestamp === undefined || timestamp === null || timestamp === "") {
        logWarningWithOptionalContext("Timestamp is null or undefined.");
        continue;
      }
      const timestampNUM = Number(timestamp);

      if (!isNaN(timestampNUM)) {
        timestamp = new Date(timestampNUM).toISOString().replace("T", " ").replace("Z", "");
      }

      const parsedDate = new Date(timestamp);
      if (isNaN(parsedDate.getTime()) || parsedDate <= latestTimestamp) {
        logInfoWithOptionalContext(`Skipping row ${i + 1} in ${table.tableName} with timestamp: ${timestamp}`);
        continue;
      }

      row[timestampIndex] = timestamp;
      rowsToInsert.push(row);
    }

    if (rowsToInsert.length === 0) {
      return;
    }

    const headerRowStr = headerRow as string[];

    const insertQuery = HANAQueryBuilder.buildInsertQuery(this.schema, table.tableName, headerRowStr, rowsToInsert);

    try {
      await this.connection.exec(insertQuery);
      logInfoWithOptionalContext(`Inserted ${rowsToInsert.length} rows into table ${table.tableName}.`);
    } catch (err) {
      const message = err?.message || "";

      const isTableError =
        message.includes("invalid table name") ||
        message.includes("does not exist") ||
        message.includes("not find table");

      if (isTableError) {
        this.schemaService.clearCache(table.tableName);
        await this.schemaService.ensureTableExistsOrCreate(table);

        const retryQuery = HANAQueryBuilder.buildInsertQuery(this.schema, table.tableName, headerRowStr, rowsToInsert);

        try {
          await this.connection.exec(retryQuery);
          logInfoWithOptionalContext(
            `Re-inserted ${rowsToInsert.length} rows into table ${table.tableName} after recreate.`
          );
          return;
        } catch (retryErr) {
          throw new QueryExecutionBaseError(
            `Retry failed: Could not insert ${rowsToInsert.length} rows into ${table.tableName} after recreate.`,
            retryErr
          );
        }
      }

      throw new QueryExecutionBaseError(`Failed to insert ${rowsToInsert.length} rows into ${table.tableName}.`, err);
    }
  }
}
