/** @format */

import { createHash } from "crypto";
import { placeholders } from "../../reuseComponents/onboarding/src";

export function createJobId(spaceId: string, objectId: string, applicationId: string, activity: string): string {
  function stringToUUID(input: string): string {
    const hash = createHash("sha256").update(input).digest("hex");
    return hash.substring(0, 36);
  }

  return stringToUUID(`${spaceId}.${objectId}.${applicationId}.${activity}`);
}

export function createJobName(spaceId: string, objectId: string, applicationId: string, activity: string): string {
  return `${spaceId}.${objectId} ${applicationId} (${activity})`;
}

export function isValidJobSpaceId(spaceId: string) {
  const invalidSpaceIds = new Set([placeholders.globalSpace, placeholders.technicalSpaceIdTaskFramework]);

  return !invalidSpaceIds.has(spaceId);
}
