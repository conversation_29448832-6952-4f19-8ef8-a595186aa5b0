/** @format */

import * as CrunInstrumentation from "@sap/xotel-agent-ext-js/dist/clientlib/api/CrunInstrumentation";
import { JobData } from "@sap/xotel-agent-ext-js/dist/clientlib/model/jm/actions/JobData";
import { RunData } from "@sap/xotel-agent-ext-js/dist/clientlib/model/jm/actions/RunData";
import { getLogger } from "../../logger";
import { IRequestContext } from "../../repository/security/common/common";
import { Status } from "../../task/logger/models";
import { ObserverResponse } from "../../task/observer/Observer";
import type { ITask } from "../../task/orchestrator/models/ITask";
import { JobCache } from "./JobCache";
import { SDWCJobDataHandlerInstance } from "./SDWCJobDataHandler";
import {
  createExecutable,
  createExecution,
  createJob,
  createJobRun,
  createJobRunFromObserver,
  createRun,
  createSchedule,
} from "./dciHelper";
import { isValidJobSpaceId } from "./utils";

const logger = getLogger("CALMIntegration");

export class CalmJobPublishingService {
  public static async sendTaskStartData(
    logId: number,
    runId: string | undefined,
    startTime: Date,
    task: ITask,
    context: IRequestContext
  ): Promise<void> {
    try {
      if (!isValidJobSpaceId(task.spaceId)) {
        return;
      }

      const tenantId = context.tenantId!;
      const serviceType = process.env.SAP_CALM_SERVICE_TYPE!;

      const job = createJob(task.spaceId, task.objectId, task.applicationId, task.activity);
      job.addExecutables(createExecutable(logId.toString(), job.getJobName(), ""));

      if (task.scheduleId) {
        // TODO: fetch schedule data from DB
      } else {
        job.addSchedules(createSchedule(job.getJobId(), runId!, "", "", startTime));
      }

      JobCache.getInstance().set(tenantId, job.getJobId(), job);
      const jobData = new JobData(tenantId, job);

      await CrunInstrumentation.sendCrunContent(jobData, serviceType, tenantId);

      const jobRun = createJobRun(task.spaceId, task.objectId, task.applicationId, task.activity);
      jobRun.addJobMetadata(SDWCJobDataHandlerInstance, tenantId);
      const execution = createExecution(logId.toString(), Status.RUNNING, startTime);

      const run = createRun(task.scheduleId || runId!, runId!, Status.RUNNING, startTime);
      run.addExecutions(execution);
      jobRun.addSchedules(run);
      const runData = new RunData(tenantId, jobRun, SDWCJobDataHandlerInstance);

      await CrunInstrumentation.sendCrunContent(runData, serviceType, tenantId);
    } catch (error) {
      logger.logError(["[CalmJobPublishingService.sendTaskStartData] Error sending task data to CALM", error], {
        context,
      });
    }
  }

  public static async sendTaskEndData(tasks: ObserverResponse[], context: IRequestContext): Promise<void> {
    const tenantId = context.tenantId!;
    const serviceType = process.env.SAP_CALM_SERVICE_TYPE!;

    for await (const task of tasks) {
      try {
        if (!isValidJobSpaceId(task.spaceId)) {
          continue;
        }

        const jobRun = createJobRunFromObserver(task);
        jobRun.addJobMetadata(SDWCJobDataHandlerInstance, tenantId);
        const runData = new RunData(tenantId, jobRun, SDWCJobDataHandlerInstance);

        await CrunInstrumentation.sendCrunContent(runData, serviceType, tenantId);
      } catch (error) {
        logger.logError(["[CalmJobPublishingService.sendTaskEndData] Error sending task data to CALM", error], {
          context,
        });
      }
    }
  }
}

// For unit test usage only
export function _getLogger() {
  return logger;
}
