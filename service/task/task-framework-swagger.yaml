openapi: 3.0.0
info:
  version: "1.0"
  title: DWC Task Framework
  description: The Task Framework is the central componentent in Datasphere for
    managing various kinds of workload. Currently it provides logging and
    scheduling. Later we will support task chaining, resurrection, external
    systems and more.
paths:
  /tf/execute:
    post:
      summary: Pacemaker triggers a task execution.
      operationId: executeTask
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/taskBody"
        description: References the Schedule. The schedule information contains the type of
          task to be executed.
        required: true
      tags:
        - Orchestrator
      responses:
        "202":
            $ref: "#/components/responses/accepted"
        "400":
          $ref: "#/components/responses/badRequest"
        "409":
          description: A conflicting task is already running.
        "500":
          $ref: "#/components/responses/error"
  /tf/technicalexecute:
    post:
      summary: Pacemaker triggers a technical task execution without a user impersonation. This separate endpoint has been created for Dynatrace tracking purposes and flexibility.
      operationId: executeTechTask
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/taskTechnicalExecuteBody"
        description: References the Schedule. The schedule information contains the type of
          task to be executed.  Use "$$global$$" as spaceId for non-space specific logs.
        required: true
      tags:
        - Orchestrator
      responses:
        "202":
          $ref: "#/components/responses/accepted"
        "400":
          $ref: "#/components/responses/badRequest"
        "409":
          description: A conflicting task is already running.
        "500":
          $ref: "#/components/responses/error"
  /tf/global/directexecute:
    post:
      summary: Business user triggers a task execution.
      operationId: executeGlobalDirectTask
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/directBusinessTaskBody"
        description: Direct execution for a non-technical business task.
        required: true
      tags:
        - Orchestrator
      responses:
        "202":
            $ref: "#/components/responses/accepted"
        "400":
          $ref: "#/components/responses/badRequest"
        "409":
          description: A conflicting task is already running.
        "500":
          $ref: "#/components/responses/error"
  /tf/createinstantjob:
    post:
      summary: External service creates a job with Pacemaker that gets triggered instantly. Currently restricted to Data Marketplace only.
      operationId: createInstantJob
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createInstantJobBody"
        description: Pacemaker job creation on behalf of a business user.
        required: true
      tags:
        - Orchestrator
      responses:
        "202":
          $ref: "#/components/responses/accepted"
        "400":
          $ref: "#/components/responses/badRequest"
        "401":
          $ref: "#/components/responses/unauthorized"
        "403":
          $ref: "#/components/responses/forbidden"
        "500":
          $ref: "#/components/responses/error"
  /tf/schedules/consent:
    get:
      summary: Retrieve whether the caller (logged-in user) has given consent or not.
      tags:
       - Scheduler Consent
      responses:
        "200":
          description: Ok.
            'consent' properties reflects the information of consent was given or not. The 'information' section comes directly from pacemaker. For possible 'status' values, check exported enum "UserConsentStatus".
            All the information properties under 'information' are optional.
            Possible status values (as of today) are 0 = "NO_CONSENT", 1 = "CONSENT_GIVEN", 2 = "CONSENT_EXPIRED"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/getConsent"
        "500":
          $ref: "#/components/responses/error"
    put:
      summary: Give the consent for the caller (logged-in user).
      requestBody:
        description: The redirect ui path we want to redirect to after the consent has been given
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/putConsent"
      tags:
         - Scheduler Consent
      responses:
        "200":
          description: Ok - the consent was already given
        "201":
          $ref: "#/components/responses/consentCreated"
        "500":
          $ref: "#/components/responses/error"
    delete:
      summary: Delete the consent for the caller (logged-in user).
      tags:
        - Scheduler Consent
      responses:
        "200":
          description: Ok - the consent was given and has been successfully deleted.
        "404":
          description: Not Found - the consent was not given and therefore cannot be deleted.
        "500":
          $ref: "#/components/responses/error"
  /tf/schedules/consent/expirationlist:
    get:
      summary: Returns a list of users, who has scheduled a task, with their consent expiration date and the schedules count.
      tags:
       - Scheduler Consent
      responses:
        "200":
          description: Ok.
            'consent' properties reflects the information of consent was given or not. The 'information' section comes directly from pacemaker. For possible 'status' values, check exported enum "UserConsentStatus".
            All the information properties under 'information' are optional.
            Possible status values (as of today) are 0 = "NO_CONSENT", 1 = "CONSENT_GIVEN", 2 = "CONSENT_EXPIRED"
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/getConsentExpirationObj"
        "500":
          $ref: "#/components/responses/error"
  /tf/{spaceid}/schedules:
    get:
      summary: Get schedules for a specific Space
      operationId: getSchedules
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - $ref: "#/components/parameters/applicationIdQuery"
        - $ref: "#/components/parameters/activityQuery"
        - $ref: "#/components/parameters/activationStatusQuery"
        - $ref: "#/components/parameters/validFromQuery"
        - $ref: "#/components/parameters/validToQuery"
        - $ref: "#/components/parameters/createdByQuery"
        - $ref: "#/components/parameters/changedByQuery"
      tags:
        - Scheduler
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/scheduleGetResponse"
        "500":
          $ref: "#/components/responses/error"
    post:
      summary: Create a Schedule
      operationId: createSchedule
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
      requestBody:
        $ref: "#/components/requestBodies/schedule"
      tags:
        - Scheduler
      responses:
        "200":
          $ref: "#/components/responses/scheduleCreation"
        "400":
          $ref: "#/components/responses/badRequest"
        "403":
          $ref: "#/components/responses/forbidden"
        "409":
          $ref: "#/components/responses/conflict"
        "500":
          $ref: "#/components/responses/error"
  /tf/{spaceid}/schedules/{scheduleid}:
    get:
      summary: Get a specific schedule
      operationId: getSchedule
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - $ref: "#/components/parameters/scheduleIdPath"
      tags:
        - Scheduler
      responses:
        "200":
          $ref: "#/components/responses/scheduleGet"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
    put:
      summary: Update a specific Schedule
      operationId: updateSchedule
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - $ref: "#/components/parameters/scheduleIdPath"
      requestBody:
        $ref: "#/components/requestBodies/schedule"
      tags:
        - Scheduler
      responses:
        "204":
          $ref: "#/components/responses/noContent"
        "400":
          $ref: "#/components/responses/badRequest"
        "403":
          $ref: "#/components/responses/forbidden"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
    delete:
      summary: Delete a specific Schedule
      operationId: deleteSchedule
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - $ref: "#/components/parameters/scheduleIdPath"
      tags:
        - Scheduler
      responses:
        "204":
          $ref: "#/components/responses/noContent"
        "500":
          $ref: "#/components/responses/error"
  /tf/{spaceid}/schedules/{scheduleid}/ownerchange:
    put:
      summary: Update the owner of a specific Schedule
      operationId: updateScheduleOwner
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - $ref: "#/components/parameters/scheduleIdPath"
      tags:
        - Scheduler
      responses:
        "204":
          $ref: "#/components/responses/noContent"
        "400":
          $ref: "#/components/responses/badRequest"
        "403":
          $ref: "#/components/responses/forbidden"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
  /tf/{spaceid}/schedules/list/{operation}:
    put:
      summary: Update the owner of, Pause, or Resume a list of Schedules
      operationId: operateScheduleList
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                scheduleIds:
                  description: List of selected schedule ids on which the mass operation has to be performed
                  type: array
                  items:
                   type: string
                  example: ["f7c6bf82-006c-4510-86bc-61c604be745a","86f96f95-3406-4ef3-a3d9-3bd1abc3088a",...]
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - in: path
          name: operation
          description: Only the supported operations [pause, resume, change_owner] which can be performed on a list of schedules.
          required: true
          schema:
            type: string
            enum:
            - pause
            - resume
            - change_owner
      tags:
        - Scheduler
      responses:
        "202":
          $ref: "#/components/responses/accepted"
        "400":
          $ref: "#/components/responses/badRequest"
        "403":
          $ref: "#/components/responses/forbidden"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
    post:
      summary: Delete a list of Schedules
      operationId: operateDeleteScheduleList
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                scheduleIds:
                  description: List of selected schedule ids on which the mass operation has to be performed
                  type: array
                  items:
                   type: string
                  example: ["f7c6bf82-006c-4510-86bc-61c604be745a","86f96f95-3406-4ef3-a3d9-3bd1abc3088a",...]
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - in: path
          name: operation
          description: Only the supported operation [delete] which can be performed on a list of schedules.
          required: true
          schema:
            type: string
            enum:
            - delete
      tags:
        - Scheduler
      responses:
        "202":
          $ref: "#/components/responses/accepted"
        "400":
          $ref: "#/components/responses/badRequest"
        "403":
          $ref: "#/components/responses/forbidden"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
  /tf/{spaceid}/logs:
    get:
      summary: Get task logs within a space
      description: >
        GET request to /tf/{spaceId}/logs. Returns all logs within this space.
        Use "$$global$$" as spaceId for non-space specific logs. Several query
        parameter filters can be appended.
      operationId: getLogs
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - $ref: "#/components/parameters/taskLogIdQuery"
        - $ref: "#/components/parameters/applicationIdQuery"
        - $ref: "#/components/parameters/objectIdQuery"
        - $ref: "#/components/parameters/activityQuery"
        - $ref: "#/components/parameters/statusQuery"
        - $ref: "#/components/parameters/getLocksQuery"
      tags:
        - Logger
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  logs:
                    type: array
                    items:
                        oneOf:
                          - $ref: "#/components/schemas/logHeader"
                          - $ref: "#/components/schemas/pmLogEntry"
                  locks:
                    type: array
                    items:
                      $ref: "#/components/schemas/lockEntry"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
  /tf/{spaceid}/logs/{tasklogid}:
    get:
      summary: Get a specific task log header and its messages
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - $ref: "#/components/parameters/taskLogIdPath"
      tags:
        - Logger
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/logMessages"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
  /tf/global/{globalspaceid}/logs:
    get:
      summary: Get task logs for non-space specific tasks.
      description: >
        GET request to /tf/global/{globalspaceid}/logs. Returns all logs for non-space specific logs.
        Use "$$xyz$$" as spaceId where "xyz" is specific to the application for which logs are to be retrieved.
        Several query parameter filters can be appended.
      operationId: getLogsGlobal
      parameters:
        - $ref: "#/components/parameters/globalSpaceIdPath"
        - $ref: "#/components/parameters/taskLogIdQuery"
        - $ref: "#/components/parameters/applicationIdQuery"
        - $ref: "#/components/parameters/objectIdQuery"
        - $ref: "#/components/parameters/activityQuery"
        - $ref: "#/components/parameters/statusQuery"
        - $ref: "#/components/parameters/getLocksQuery"
      tags:
        - Logger
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  logs:
                    type: array
                    items:
                        oneOf:
                          - $ref: "#/components/schemas/logHeader"
                          - $ref: "#/components/schemas/pmLogEntry"
                  locks:
                    type: array
                    items:
                      $ref: "#/components/schemas/lockEntry"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
  /tf/global/{globalspaceid}/logs/{tasklogid}:
    get:
      summary: Get a specific task log header and its messages for non-space specific tasks.
      parameters:
        - $ref: "#/components/parameters/globalSpaceIdPath"
        - $ref: "#/components/parameters/taskLogIdPath"
      tags:
        - Logger
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/logMessages"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
  /tf/{spaceid}/logs/{tasklogid}/messages/{messageno}/blobcontent:
    get:
      summary: Get the BLOB object for a specific message number of a tasklogId
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - $ref: "#/components/parameters/taskLogIdPath"
        - $ref: "#/components/parameters/messageNoPath"
      tags:
        - Logger
      responses:
        "200":
          description: OK
          content:
            application/octet-stream:
              schema:
                $ref: "#/components/schemas/blobObject"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
  /tf/{spaceid}/logs/{tasklogid}/messages/{messageno}/jsondata:
    get:
      summary: Get the JSON DATA for a specific message number of a tasklogId
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - $ref: "#/components/parameters/taskLogIdPath"
        - $ref: "#/components/parameters/messageNoPath"
      tags:
        - Logger
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
  /tf/{spaceid}/setfailed/{tasklogid}:
    put:
      summary: Set the task to failed and removes locks
      description: >
        PUT request to /tf/{spaceId}/setfailed/{taskLogId} . Sets the TaskLog
        status and remove a lock (if set) for this task.
      operationId: setfailed
      parameters:
      - $ref: "#/components/parameters/spaceIdPath"
      - $ref: "#/components/parameters/taskLogIdPath"
        required: true
      tags:
        - Orchestrator
      responses:
        "200":
          $ref: "#/components/responses/success"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
  /tf/{spaceid}/{objectid}/chainable:
    get:
      summary: Get if a particular object can be part of a chain.
      description: >
        GET request to /tf/:spaceid/objectid/chainable. Returns true if object can be part of a chain,
        otherwise returns false. For instance a view can only be added to a chain if it is persistable.
        A remote table can not be added to a chain if it is currently in realtime mode.
      operationId: getIsChainable
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - $ref: "#/components/parameters/objectIdPath"
        - $ref: "#/components/parameters/applicationIdQueryMandatory"
      tags:
        - Task Chains
      responses:
        "200":
          description: If the object is chainable or not.
          content:
            application/json:
              schema:
                type: object
                properties:
                  objectCurrentlyChainable:
                    type: boolean
  /tf/{spaceid}/chainabletasks:
    get:
      summary: Get a list of tasks that are allowed in Task Chains.
      description: >
        GET request to /tf/:spaceid/chainabletasks. Returns an array of objects containing applicationIds & activities
        of tasks that are allowed in Task Chains.
      operationId: getChainableTasks
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
      tags:
        - Task Chains
      responses:
        "200":
          description: If the object is chainable or not.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    applicationId:
                      type: string
                    activity:
                      type: string
  /deploy/taskchain:
    post:
      summary: Deploys a taskchain to customerHana
      description: >
        POST request to /deploy/taskchain.
        Deploys a taskchain, including all subactivites such as dependency checks, auditlog creation and notification sending.
        Spin-off of the /deploy endpoint, in order to not mix up different logic for different objectTypes (CSN vs. JSON) too much.
      operationId: deployTaskChain
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/chainDeployBody"
      tags:
        - Task Chains
      responses:
        "200":
          description: Chain deployment was successfully TRIGGERED.
          $ref: "#/components/responses/accepted"
  /monitor/{spaceid}/taskchains:
    get:
      summary: Retrieves most recent log headers to be displayed in Task Chains Monitor.
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
      tags:
        - Task Chains
      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/taskChainsOverviewGetResponse"
        "500":
          $ref: "#/components/responses/error"
  /tf/{spaceid}/taskchains/{tasklogid}:
   get:
      summary: Get the task log header, messages, and children of a specific task chain run
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - $ref: "#/components/parameters/taskLogIdPath"
      tags:
        - Task Chains
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/taskChainDetailsResponse"
        "400":
          $ref: "#/components/responses/badRequest"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
  /tf/{spaceid}/taskchains/{chainname}/info:
    get:
      summary: Get the current status of a task chain
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - $ref: "#/components/parameters/chainNamePath"
      tags:
        - Task Chains
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/taskChainsOverviewGetResponse"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
  /tf/{spaceid}/taskchains/{chainname}/start:
    post:
      summary: Starts the run of a task chain
      operationId: runChain
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - $ref: "#/components/parameters/chainNamePath"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/chainStartBody"
        description: Task chain input parameters with their values
      tags:
        - Task Chains
      responses:
        "202":
          $ref: "#/components/responses/accepted"
        "400":
          $ref: "#/components/responses/badRequest"
        "409":
          description: A conflicting task is already running.
        "500":
          $ref: "#/components/responses/error"
  /tf/{spaceid}/taskchains/{chainname}/retry:
    post:
      summary: Retries the run of a failed task chain
      operationId: retryChain
      parameters:
        - $ref: "#/components/parameters/spaceIdPath"
        - $ref: "#/components/parameters/chainNamePath"
      tags:
        - Task Chains
      responses:
        "202":
          $ref: "#/components/responses/accepted"
        "400":
          $ref: "#/components/responses/badRequest"
        "409":
          description: A conflicting task is already running.
        "500":
          $ref: "#/components/responses/error"
  /tf/admin/usage:
    get:
      summary: Retrieves usage information about TaskLogs and TaskLogMessages.
      description: All information is aggregated over all spaces within this tenant. All
        information is returned in bytes. All the returned information is an
        aggregation over the tables "TaskLogs" and "TaskLogMessages". Other
        tables (like Schedules or technical tables created as part of the
        deployment) are not considered.
      operationId: adminGetUsage
      tags:
        - Admin
      responses:
        "200":
          description: When the logs were received successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  inMemory:
                    type: integer
                  onDisk:
                    type: integer
                  numberOfEntries:
                    type: integer
        "500":
          $ref: "#/components/responses/error"
  /tf/admin/logs/cleanup:
    put:
      summary: Removes old log messages
      description: >
        Cleanup of completed TaskLogs and their related TaskLogMessages. Removes
        all TaskLogs which are completed and which END_TIME file in the database
        are older than the given olderThan value in the body in this request.
        The request is asynchronous. It returns a 202 when the deletion processing has successfully started. Within the response, a taskLogId is returned which can be used to retrieve the log information of this cleanup task.
      tags:
        - Admin
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/cleanupBody"
        description: Logs referencing finished task logs that ended before the given date
          are deleted
        required: true
      responses:
        "202":
          $ref: "#/components/responses/accepted"
        "400":
          $ref: "#/components/responses/badRequest"
        "500":
          $ref: "#/components/responses/error"
  /tf/admin/logs/updatecleanupschedule:
    put:
      summary: Updates system schedule for clean up task
      description: >
        Updates the frequency and deletion period of the system schedule to execute a clean up task, which cleans up
        the logs of the executed tasks and their related data.
      tags:
        - Admin
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateCleanUpScheduleBody"
        description: Schedule of task logs clean up is updated.
        required: true
      responses:
        "202":
          $ref: "#/components/responses/accepted"
        "400":
          $ref: "#/components/responses/badRequest"
        "500":
          $ref: "#/components/responses/error"
  /tf/admin/logs/getcleanupschedule:
    get:
      summary: gets system schedule for clean up task
      description: >
       Gets the schedule to execute a clean up task, which cleans up
        the logs of the executed tasks and their related data.
      tags:
        - Admin
      responses:
        "200":
          $ref: "#/components/responses/scheduleGet"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
  /tf/admin/globalschedules/ownerchange:
    put:
      summary: Updates owner on an Admin schedule in the global space.
      description: >
        Updates the owner of an Admin schedule in the global space.
      tags:
        - Admin
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateAdminScheduleOwner"
        required: true
      responses:
        "204":
          $ref: "#/components/responses/noContent"
        "400":
          $ref: "#/components/responses/badRequest"
        "403":
          $ref: "#/components/responses/forbidden"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
  /tf/admin/globalschedules/update:
    put:
      summary: Updates Admin schedule in the global space.
      description: >
        Updates Admin schedule in the global space.
      tags:
        - Admin
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateAdminSchedule"
        required: true
      responses:
        "204":
          $ref: "#/components/responses/noContent"
        "400":
          $ref: "#/components/responses/badRequest"
        "403":
          $ref: "#/components/responses/forbidden"
        "404":
          $ref: "#/components/responses/notFound"
        "500":
          $ref: "#/components/responses/error"
components:
  parameters:
    spaceIdPath:
      in: path
      name: spaceid
      description: Space ID (Technical Name of the space)
      required: true
      schema:
        type: string
    globalSpaceIdPath:
      in: path
      name: globalspaceid
      description: Non-Space ID (Always starts and ends with $$, e.g; "$$xyz$$", "$$global$$", etc.)
      required: true
      schema:
        type: string
    objectIdPath:
      in: path
      name: objectid
      description: Object ID (Technical Name of the object)
      required: true
      schema:
        type: string
    chainNamePath:
      in: path
      name: chainname
      description: Chain name (Oject ID or Technical Name of the chain)
      required: true
      schema:
        type: string
    taskLogIdPath:
      in: path
      name:  tasklogid
      description: TaskLogId
      required: true
      schema:
       type: string
    messageNoPath:
      in: path
      name:  messageno
      description: MessageNo (messsage mumber of the message)
      required: true
      schema:
       type: string
    spaceNameQuery:
      in: query
      name: spaceName
      description: Space Name
      required: false
      schema:
        type: string
    taskLogIdQuery:
      in: query
      name: taskLogId
      description: Filters by taskLogId. It identifies the TaskLog containing multiple
        messges.
      required: false
      schema:
        type: string
    applicationIdQuery:
      in: query
      name: applicationId
      description: Filters by applicationId. It identifies the Application within DWC that
        executed this task.
      required: false
      schema:
        type: string
    applicationIdQueryMandatory:
      in: query
      name: applicationId
      description: Mandatory Query parameter needed to avoid another lookup of the object type.
      required: true
      schema:
        type: string
    activityQuery:
      in: query
      name: activity
      description: Filters by activity. It identifies the activity the Application.
      required: false
      schema:
        type: string
    startTimeQuery:
      in: query
      name: startTime
      description: Start Time
      required: false
      schema:
        type: string
        format: date-time
    endTimeQuery:
      in: query
      name: endTime
      description: End Time
      required: false
      schema:
        type: string
        format: date-time
    objectIdQuery:
      in: query
      name: objectId
      description: Filters by objectId. It identifies the main object this task is working
        on
      required: false
      schema:
        type: string
    statusQuery:
      in: query
      name: status
      description: Filters by status. It identifies the task's status.
      required: false
      schema:
        type: string
    activationStatusQuery:
      in: query
      name: activationStatus
      description: Acativation Status
      required: false
      schema:
        type: string
    scheduleIdPath:
      in: path
      name: scheduleid
      description: Schedule ID
      required: true
      schema:
        type: string
    createdByQuery:
      in: query
      name: createdBy
      description: Created By
      required: false
      schema:
        type: string
    changedByQuery:
      in: query
      name: changedBy
      description: Changed By
      required: false
      schema:
        type: string
    validFromQuery:
      in: query
      name: validFrom
      description: Valid From
      required: false
      schema:
        type: string
        format: date-format
    validToQuery:
      in: query
      name: validTo
      description: Valid To
      required: false
      schema:
        type: string
        format: date-format
    getLocksQuery:
      in: query
      name: getLocks
      description: Set getLocks to true to fetch locks, along with the logs
      required: false
      schema:
        type: boolean
      allowEmptyValue: false
  responses:
    success:
      description: Ok.
    accepted:
      description: Accepted.
    noContent:
      description: No Content.
    consentCreated:
      description: Created - the consent was created successfully. Returns the url to the the respective UAA, as he has not given consent for the schedule being created on his behalf.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/scheduleImpersonationConsentResponse"
    conflict:
      description: Conflict.
    unauthorized:
      description: Unauthorized.
    forbidden:
      description: Forbidden.
    scheduleCreation:
      description: Task Schedule has been created.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/scheduleCreationResponse"
    scheduleGet:
      description: OK
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/scheduleGetResponse"
    notFound:
      description: Resource was not found.
    badRequest:
      description: Bad Request. Query, Path or Body Parameters probably wrong.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/errorResponse"
    error:
      description: Internal Server Error. The the application log for more details.
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/errorResponse"
  requestBodies:
    schedule:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/schedule"
      description: Body for scheduling a task
      required: true
  schemas:
    applicationId:
      type: string
      enum:
        - REMOTE_TABLES
        - VIEWS
        - DATA_FLOWS
        - TASK_FRAMEWORK
    activity:
      type: string
      enum:
        - REPLICATE
        - REMOVE_REPLICATED_DATA
        - PERSIST
        - EXECUTE
        - TASKLOG_CLEANUP
    status:
      type: string
      enum:
        - RUNNING
        - COMPLETED
        - FAILED
    severity:
      type: string
      enum:
        - INFO
        - WARNING
        - ERROR
    message:
        type: object
        required:
          - messageNumber
          - severity
          - timestamp
        properties:
          messageNumber:
            type: number
          severity:
            $ref: "#/components/schemas/severity"
          text:
            type: string
          messageBundleId:
            type: string
          messageBundleKey:
            type: string
          parameterValues:
            type: object
          timestamp:
            type: string
            format: date-time
          details:
            type: string
          navigation:
            type: string
    activationStatus:
      type: string
      enum:
        - ENABLED
        - DISABLED
    taskBody:
      type: object
      properties:
        spaceId:
          type: string
          description: The spaceId in which this schedule is defined.
        scheduleId:
          type: string
          description: The scheduleId referencing the schedule with the task to be executed.
        job:
          type: object
          description: The job information coming in from pacemaker.
            and this documentation only describes the sections within this object that are
            used in TaskFramework so far.
          properties:
            plannedExecutionDate:
              type: string
              description: The planned execution time by pacemaker of this execution.
    directBusinessTaskBody:
      type: object
      properties:
        applicationId:
          type: string
        activity:
          type: string
        objectId:
          type: string
        spaceId:
          type: string
          description: A placeholder string such as "$$xyz$$" representative of a space.
    createInstantJobBody:
      type: object
      properties:
        user:
          type: string
        applicationId:
          type: string
        activity:
          type: string
        objectId:
          type: string
        spaceId:
          type: string
      example:
        user: "business_user"
        applicationId: "REMOTE_TABLES"
        activity: "REPLICATE"
        objectId: "Suppliers"
        spaceId: "example_space"
    taskTechnicalExecuteBody:
      type: object
      properties:
        spaceId:
          type: string
          description: The spaceId in which this schedule is defined.
        scheduleId:
          type: string
          description: The scheduleId referencing the schedule with the task to be executed.
    updateAdminScheduleOwner:
      type: object
      properties:
        scheduleId:
          type: string
          description: ScheduleId referencing the schedule that needs to be updated.
    updateAdminSchedule:
      type: object
      properties:
        scheduleId:
          type: string
          description: ScheduleId referencing the schedule that needs to be updated.
        cron:
          type: string
          description: defines the frequency in which the schedule should be executed.
    cleanupBody:
      type: object
      properties:
        olderThan:
          type: integer
          description: The date as a unix timestamp (seconds since the unix epoch). Logs older than this date are deleted
            from the database.
    updateCleanUpScheduleBody:
      type: object
      properties:
        olderThan:
          type: integer
          description: The date as a unix timestamp (seconds since the unix epoch). Logs older than this date are deleted
            from the database.
        cron:
          type: string
          description: defines the frequency in which the schedule should be executed.
    chainDeployBody:
      type: object
      properties:
          folderId:
            description: The technical name of the space
            type: string
            example: TASKFRAMEWORK
          folderGuid:
            description: The hexadecimal UUid
            type: string
            example: AC9C2E90698724CD1700455EC1783AFD
          spaceName:
            description: The technical name of the space
            type: string
            example: TASKFRAMEWORK
          name:
            description: The technical name of the task chain
            type: string
            example: sampleChainName
          content:
            type: object
            example: {
              version: {
                taskchain: "0.1"
              },
              taskchains: {
                sampleChainName: {
                    kind: sap.dwc.taskChain,
                            nodes:
                            [
                                {
                                    id: 0,
                                    type: TASK,
                                    taskIdentifier: {
                                        applicationId: REMOTE_TABLES,
                                        activity: REPLICATE,
                                        objectId: Orders
                                    }
                                },
                                {
                                    id: 1,
                                    type: TASK,
                                    taskIdentifier: {
                                        applicationId: VIEWS,
                                        activity: PERSIST,
                                        objectId: French_Orders
                                    }
                                }
                            ],
                            links:
                            [
                                {
                                    startNode: {
                                        nodeId: 0,
                                        statusRequired: COMPLETED
                                    },
                                    endNode: {
                                        nodeId: 1
                                    },
                                    id: 0
                                }
                            ]
                }
              }
            }
    chainStartBody:
      type: object
      properties:
          inputParameters:
            type: object
      example: {
          inputParameters: {
             YEAR: 2025,
             RETENTION_TIME: "90"
          }
      }
    lockEntry:
      required:
        - logId
        - spaceId
        - objectId
        - activity
        - applicationId
        - creationTime
        - lockKey
      properties:
        logId:
          type: number
        spaceId:
          type: string
        objectId:
          type: string
        activity:
          $ref: "#/components/schemas/activity"
        applicationId:
          $ref: "#/components/schemas/applicationId"
        creationTime:
          type: string
          format: date-time
        lockKey:
          type: string
    pmLogEntry:
      type: object
      required:
        - applicationId
        - activity
        - spaceId
        - objectId
        - startTime
        - status
        - user
        - messages
      properties:
        applicationId:
          $ref: "#/components/schemas/applicationId"
        activity:
          $ref: "#/components/schemas/activity"
        spaceId:
          type: string
        objectId:
          type: string
        startTime:
          type: string
          format: date-time
        endTime:
          type: string
          format: date-time
        status:
          $ref: "#/components/schemas/status"
        scheduleId:
          type: string
        user:
          type: string
        runId:
          type: string
        messages:
          type: array
          items:
            schema:
            $ref: "#/components/schemas/message"
    logHeader:
      type: object
      required:
        - logId
        - applicationId
        - activity
        - spaceId
        - objectId
        - startTime
        - status
        - user
      properties:
        logId:
          type: number
        applicationId:
          $ref: "#/components/schemas/applicationId"
        activity:
          $ref: "#/components/schemas/activity"
        spaceId:
          type: string
        objectId:
          type: string
        startTime:
          type: string
          format: date-time
        endTime:
          type: string
          format: date-time
        runTime:
          type: number
        status:
          $ref: "#/components/schemas/status"
        scheduleId:
          type: string
        user:
          type: string
        runId:
          type: string
    childLogHeader:
      type: object
      required:
        - logId
        - applicationId
        - activity
        - spaceId
        - objectId
        - status
      properties:
        logId:
          type: number
        applicationId:
          $ref: "#/components/schemas/applicationId"
        activity:
          $ref: "#/components/schemas/activity"
        spaceId:
          type: string
        objectId:
          type: string
        status:
          $ref: "#/components/schemas/status"
    logMessages:
      type: object
      required:
        - logId
        - applicationId
        - activity
        - spaceId
        - objectId
        - startTime
        - status
        - user
      properties:
        logId:
          type: number
        applicationId:
          $ref: "#/components/schemas/applicationId"
        activity:
          $ref: "#/components/schemas/activity"
        spaceId:
          type: string
        objectId:
          type: string
        startTime:
          type: string
          format: date-time
        endTime:
          type: string
          format: date-time
        runTime:
          type: number
        status:
          $ref: "#/components/schemas/status"
        scheduleId:
          type: string
        user:
          type: string
        runId:
          type: string
        parent:
          type: object
          properties:
            logId:
              type: number
            objectId:
              type: string
            spaceId:
              type: string
        messages:
          type: array
          items:
            schema:
            $ref: "#/components/schemas/message"
    blobObject:
      type: string
      format: binary
    putConsent:
      type: object
      properties:
        uiRedirectPath:
          type: string
      example:
        uiRedirectPath: "#/users"
    getConsent:
      type: object
      properties:
        consent:
          type: boolean
        information:
          type: object
          properties:
            status:
              type: number
            createdAt:
              type: string
              format: date-time
            validUntil:
              type: string
              format: date-time
      example:
        consent: true
        information:
          status: 1
          createdAt: "2020-01-01T12:34:56.789Z"
          validTo: "2020-12-31T00:00:00.000Z"
    getConsentExpirationObj:
      type: object
      properties:
        taskCount:
          type: number
        userConsentInfo:
          type: object
          properties:
            user:
              type: string
            status:
              type: number
            createdAt:
              type: string
              format: date-time
            validUntil:
              type: string
              format: date-time
      example:
        taskCount: 4
        userConsentInfo:
          user: "sample_user"
          status: 1
          createdAt: "2020-01-01T12:34:56.789Z"
          validTo: "2020-12-31T00:00:00.000Z"
    taskLogId:
      type: object
      properties:
        taskLogId:
          type: number
      example:
        taskLogId: 18234
    schedule:
      type: object
      properties:
        applicationId:
          $ref: "#/components/schemas/applicationId"
        activity:
          $ref: "#/components/schemas/activity"
        objectId:
          type: string
        description:
          type: string
        validFrom:
          type: string
          format: date-time
        validTo:
          type: string
          format: date-time
        nextRun:
          type: string
          format: date-time
        activationStatus:
          $ref: "#/components/schemas/activationStatus"
        cron:
          type: string
        payload:
          type: object
        owner:
          type: string
        uiVariant:
          type: string
    scheduleImpersonationConsentResponse:
      type: object
      properties:
        redirectUrl:
          type: string
    scheduleCreationResponse:
      type: object
      properties:
        scheduleId:
          type: string
    taskChainsOverviewGetResponse:
      type: object
      properties:
        logId:
          type: number
        applicationId:
          $ref: "#/components/schemas/applicationId"
        activity:
          $ref: "#/components/schemas/activity"
        spaceId:
          type: string
        objectId:
          type: string
        startTime:
          type: string
          format: date-time
        endTime:
          type: string
          format: date-time
        runTime:
          type: number
        status:
          $ref: "#/components/schemas/status"
        scheduleId:
          type: string
        user:
          type: string
    taskChainDetailsResponse:
      allOf:
        - $ref: "#/components/schemas/logMessages"
        - type: object
          required:
            - children
          properties:
            children:
              type: array
              items:
                schema:
                $ref: "#/components/schemas/childLogHeader"
    scheduleGetResponse:
      description: OK
      type: object
      properties:
        scheduleId:
          type: string
        application:
          $ref: "#/components/schemas/applicationId"
        activity:
          $ref: "#/components/schemas/activity"
        spaceId:
          type: string
        objectId:
          type: string
        description:
          type: string
        validFrom:
          type: string
          format: date-time
        validTo:
          type: string
          format: date-time
        activationStatus:
          $ref: "#/components/schemas/activationStatus"
        cron:
          type: string
        payload:
          type: object
        createdBy:
          type: string
        createdAt:
          type: string
          format: date-time
        changedBy:
          type: string
        changedAt:
          type: string
          format: date-time
        owner:
          type: string
        uiVariant:
          type: string
    errorResponse:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: integer
        message:
          type: string
    successNoActionResponse:
      type: object
      properties:
        executionStatus:
          type: string
          enum:
          - "SUCCESS_NO_ACTION"
