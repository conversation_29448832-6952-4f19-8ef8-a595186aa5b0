/** Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved. */

import { LogLevel } from "@sap/dwc-logger";
import { NextFunction, Request } from "express";
import { StatusCodes } from "http-status-codes";
import { IResponse } from "../../routes/meta/interfaces/IResponse";
import { Status } from "../logger/models";
import { INotifyingSchedule, ScheduleOperation } from "../logger/services/notifications/NotificationModels";
import { NotificationService } from "../logger/services/notifications/NotificationService";
import { TaskFrameworkResponse } from "../models";
import { SchedulerErrorHandler } from "../orchestrator/controllers/SchedulerErrorHandler";
import { StoredSchedule, WithNextRun } from "../scheduler/ScheduleTypes";
import { SchedulerValidation } from "../scheduler/SchedulerValidation";
import { TaskSchedulesApi } from "../scheduler/TaskSchedulesApi";
import { isTimezoneEnabled } from "../scheduler/isTimezoneEnabled";
import { logEndpoint } from "../shared/logger/TaskFrameworkLogger";
import { EnumUtils } from "../shared/util/EnumUtils";
import { ISchedulerError, SchedulerError } from "../errors/SchedulerError";


export interface TaskScheduleResponse extends TaskFrameworkResponse {
  /**
   * Task Framework response object, with values resulting from the operation
   */
  tf: {
    /**
     * Status code of the operation. These are only successful status codes, as the error handler will handle the rest
     */
    status: StatusCodes,
    /**
     * Some properties of the schedule object resulting from the operation, like `create`, `get`
     */
    schedule?: Partial<StoredSchedule & WithNextRun>,
    /**
     * List of schedules resulting from the operation, like `getAll`
     */
    scheduleList?: Array<StoredSchedule & WithNextRun>
  }
}

/**
 * Middleware to create a schedule of an object. In case of success, the `response.locals.tf.schedule` object will contain the scheduleId of the created schedule.
 * In case of error, the error handler will be called and the response will be sent, ending the request.
 */
async function create(request: Request, response: IResponse<TaskScheduleResponse>, next: NextFunction): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context = request.context;
  const spaceId: string = request.params.spaceid ?? response.locals?.spaceid;
  try {
    const validator = new SchedulerValidation(context, spaceId);
    const schedule = validator.parseCreateBody(request.body);
    await validator.validateInterval(schedule);
    const api = await TaskSchedulesApi.createInstance(context, spaceId);
    const scheduleId = await api.createSchedule(schedule);
    response.locals.tf = { schedule: { scheduleId }, status: StatusCodes.CREATED };
    next();
  } catch (err) {
    new SchedulerErrorHandler(context, "POST /tf/:spaceid/schedules", err).sendHTTPResponse();
  }
}

/**
 * Middleware to get a specific schedule using a schedule id. In case of success, the `response.locals.tf.schedule` object will contain the retrieved schedule.
 * In case of error, the error handler will be called and the response will be sent, ending the request.
 */
async function get(request: Request, response: IResponse<TaskScheduleResponse>, next: NextFunction): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context = request.context;
  const spaceId: string = request.params.spaceid ?? response.locals?.spaceid;
  try {
    const scheduleId: string = request.params.scheduleid;
    const api = await TaskSchedulesApi.createInstance(context, spaceId);
    const schedule = await api.getSchedule(scheduleId);
    const tzEnabled = await isTimezoneEnabled(context);
    if (!tzEnabled) {
      delete schedule.tzName;
    }
    response.locals.tf = { schedule, status: StatusCodes.OK };
    next();
  } catch (err) {
    new SchedulerErrorHandler(context, "GET /tf/:spaceid/schedules/:scheduleid", err).sendHTTPResponse();
  }
}

/**
 * Middleware to get all schedules based on the filters given in the query string. In case of success, the `response.locals.tf.scheduleList` object will contain the list of schedules.
 * In case of error, the error handler will be called and the response will be sent, ending the request.
 */
async function getAll(request: Request, response: IResponse<TaskScheduleResponse>, next: NextFunction): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context = request.context;
  const requestQs: Record<string, string> = request.query as Record<string, string>;
  const spaceId: string = request.params.spaceid ?? response.locals?.spaceid;
  try {
    const validator = new SchedulerValidation(context, spaceId);
    const filters = validator.parseFiltersQuery(requestQs)
    const api = await TaskSchedulesApi.createInstance(context, spaceId);
    const scheduleList = await api.listSchedules(filters);
    const tzEnabled = await isTimezoneEnabled(context);
    if (!tzEnabled) {
      scheduleList.forEach(schedule => delete schedule.tzName);
    }
    response.locals.tf = { scheduleList, status: StatusCodes.OK };
    next();
  } catch (err) {
    new SchedulerErrorHandler(context, "GET /tf/:spaceid/schedules", err).sendHTTPResponse();
  }
}

/**
 * Middleware to update an existing schedule given its schedule id. In case of success, the `response.locals.tf.status` will be a 204 No Content.
 * In case of error, the error handler will be called and the response will be sent, ending the request.
 */
async function update(request: Request, response: IResponse<TaskScheduleResponse>, next: NextFunction): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context = request.context;
  const spaceId: string = request.params.spaceid ?? response.locals?.spaceid;
  try {
    const scheduleId: string = request.params.scheduleid;
    const validator = new SchedulerValidation(context, spaceId);
    const updates = validator.parseUpdateBody(request.body);
    const api = await TaskSchedulesApi.createInstance(context, spaceId);
    await api.updateSchedule(scheduleId, updates);
    response.locals.tf = { status: StatusCodes.NO_CONTENT }
    next();
  } catch (err) {
    new SchedulerErrorHandler(context, "PUT /tf/:spaceid/schedules/:scheduleid", err).sendHTTPResponse();
  }
}

/**
 * Operations(pause|resume|delete|change_owner) on a list of schedules.
 * The body contains a list of schedule ids.
 * Controller for POST and PUT calls on the endpoint /tf/:spaceid/schedules/list/:operation
 * Refer service\task\task-framework-swagger.yaml file for /tf/{spaceid}/schedules/list/{operation} endpoint details
 */
async function operateScheduleList(request: Request, response: IResponse<TaskScheduleResponse>): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context = request.context;
  const spaceId: string = request.params.spaceid ?? response.locals?.spaceid;
  const isValidOperation = (action: ScheduleOperation): boolean =>
    (request.method === "POST" && action === ScheduleOperation.DELETE) ||
    (request.method === "PUT" && [ScheduleOperation.PAUSE, ScheduleOperation.RESUME, ScheduleOperation.CHANGE_OWNER].includes(action))

  const action = EnumUtils.safeToEnum(ScheduleOperation, request.params.operation);
  if (!action || !isValidOperation(action)) {
    response.status(StatusCodes.BAD_REQUEST).send(`Wrong operation for this request: ${request.params.operation}.`).end();
    return;
  }
  const scheduleIds: string[] = request.body.scheduleIds;
  if (!Array.isArray(scheduleIds) || scheduleIds.length === 0) {
    response.status(StatusCodes.NOT_FOUND).send(`No scheduleIds provided`).end();
    return;
  }
  const backgrooundContext = context.createNewForBackground();
  try {
    const api = await TaskSchedulesApi.createInstance(backgrooundContext, spaceId);
    response.status(StatusCodes.ACCEPTED).end();
    let failedCount = 0;
    for (const scheduleId of scheduleIds) {
      try {
        switch (action) {
          case ScheduleOperation.PAUSE:
            await api.pauseSchedule(scheduleId);
            break;
          case ScheduleOperation.RESUME:
            await api.resumeSchedule(scheduleId);
            break;
          case ScheduleOperation.CHANGE_OWNER:
            await api.updateScheduleOwner(scheduleId);
            break;
          case ScheduleOperation.DELETE:
            await api.deleteSchedule(scheduleId);
            break;
          default:
            throw new SchedulerError(ISchedulerError.ERROR_INVALID_SCHEDULE_BODY, `Wrong operation: ${action}`);
        }
      } catch (err) {
        failedCount++;
      }
    }
    const notifyingSchedule: INotifyingSchedule = {
      status: failedCount ? Status.FAILED : Status.COMPLETED,
      operation: action,
      spaceId,
      totalCount: scheduleIds.length,
      failedCount
    };
    const notifier = new NotificationService(backgrooundContext);
    await notifier.notifyScheduleOperation(notifyingSchedule);
  } catch (err) {
    new SchedulerErrorHandler(backgrooundContext, "POST /tf/:spaceid/schedules/list/:operation", err).sendHTTPResponse();
    return;
  } finally {
    await backgrooundContext.finish();
  }
}

/**
 * MIddleware to update a schedule owner
 */
async function updateOwner(request: Request, response: IResponse<TaskScheduleResponse>, next: NextFunction): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context = request.context;
  const spaceId = request.params.spaceid ?? response.locals?.spaceid;
  try {
    const scheduleId: string = request.params.scheduleid;
    const api = await TaskSchedulesApi.createInstance(context, spaceId);
    await api.updateScheduleOwner(scheduleId);
    response.locals.tf = { status: StatusCodes.NO_CONTENT };
    next();
  } catch (err) {
    new SchedulerErrorHandler(context, "PUT /tf/:spaceid/schedules/:scheduleid/changeOwner", err).sendHTTPResponse();
  }
}

/**
 * Middleware to remove (delete) a schedule given its schedule id. In case of success, the `response.locals.tf.status` will be a 204 No Content.
 * In case of error, the error handler will be called and the response will be sent, ending the request.
 */
async function remove(request: Request, response: IResponse<TaskScheduleResponse>, next: NextFunction): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context = request.context;
  const spaceId = request.params.spaceid ?? response.locals?.spaceid;
  try {
    const scheduleId: string = request.params.scheduleid;
    const api = await TaskSchedulesApi.createInstance(context, spaceId);
    await api.deleteSchedule(scheduleId);
    response.locals.tf = { status: StatusCodes.NO_CONTENT };
    next();
  } catch (err) {
    new SchedulerErrorHandler(context, "DELETE /tf/:spaceid/schedules/:scheduleid", err).sendHTTPResponse();
  }
}

/**
 * Send the response of a successful schedule operation
 */
async function sendScheduleResponse(request: Request, response: IResponse<TaskScheduleResponse>) {
  logEndpoint(LogLevel.Info, request);
  const { schedule, scheduleList, status } = response.locals.tf;
  let results: object | undefined
  if (schedule) {
    results = schedule;
  } else if (scheduleList) {
    results = scheduleList;
  }
  if (results) {
    response.status(status).json(results);
  } else {
    response.status(status).end();
  }
}

/**
 * Middleware object to be used in the routes. The funcitons in this object populate the response.locals object with the results of the operation and call the next middleware in the chain.
 * If an error occurs, the error handler will be called and the response will be sent, ending the request. In this case, the next middleware will not be called.
 */
const SchedulerMiddleware = {
  create,
  get,
  getAll,
  update,
  updateOwner,
  remove,
};

/**
 * Create a schedule and send the response. To be used in the routes as a last middleware in the chain.
 */
const createSchedule = [SchedulerMiddleware.create, sendScheduleResponse];
/**
 * Get a schedule and send the response. To be used in the routes as a last middleware in the chain.
 */
const getSchedule = [SchedulerMiddleware.get, sendScheduleResponse];
/**
 * Get all schedules and send the response. To be used in the routes as a last middleware in the chain.
 */
const getAllSchedules = [SchedulerMiddleware.getAll, sendScheduleResponse];
/**
 * Update a schedule and send the response. To be used in the routes as a last middleware in the chain.
 */
const updateSchedule = [SchedulerMiddleware.update, sendScheduleResponse];
/**
 * Update the schedule owner and send the response. To be used in the routes as a last middleware in the chain.
 */
const updateScheduleOwner = [SchedulerMiddleware.updateOwner, sendScheduleResponse];
/**
 * Delete a schedule and send the response. To be used in the routes as a last middleware in the chain.
 */
const deleteSchedule = [SchedulerMiddleware.remove, sendScheduleResponse];

export { SchedulerMiddleware, createSchedule, deleteSchedule, getAllSchedules, getSchedule, operateScheduleList, sendScheduleResponse, updateSchedule, updateScheduleOwner };

