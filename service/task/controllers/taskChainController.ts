/** Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved. */

import { IRequestContext, RepositoryObjectType } from "@sap/deepsea-types";
import { Request } from "express";
import { StatusCodes } from "http-status-codes";
import { FeatureFlagProvider } from "../../featureflags/FeatureFlagProvider";
import { LogLevel } from "../../logger";
import { RequestContext } from "../../repository/security/requestContext";
import { IResponse } from "../../routes/meta/interfaces/IResponse";
import { CodedError, sendErrorResponse } from "../../server/errorResponse";
import { IChainableParameter, parametersState, TaskCapabilities, TaskType } from "../TaskChainRegistry";
import { ChainRetry } from "../chains/ChainRetry";
import { ChainRetryNew } from "../chains/ChainRetryNew";
import { NotificationsMailingListValidator } from "../chains/NotificationsMailingListValidator";
import { chainLogsWithBusinessName, logsWithBusinessName } from "../chains/chainBusinessName";
import { DeployService } from "../chains/designtime/DeployService";
import { NotificationsMailingListEntry } from "../chains/models";
import { IObjectChainableResult } from "../chains/models/IObjectNotChainableMessage";
import { ChainRetryError } from "../errors/ChainRetryError";
import { TaskFactoryError } from "../errors/TaskFactoryError";
import { TaskChainDetails, TaskChainLog, TaskLogsApi } from "../logger/controllers/TaskLogsApi";
import { isHideParentTaskEnabled } from "../logger/isHideParentTaskEnabled";
import { IFilterParameters } from "../logger/models";
import { ITaskLog } from "../logger/models/ITaskLog";
import { Status } from "../logger/models/Status";
import { TaskLogDelete } from "../logger/services/db/TaskLogDelete";
import { NotificationsMailingList } from "../logger/services/notifications/NotificationsMailingList";
import { Activity, ApplicationId, TaskFrameworkResponse, TaskIdentifier } from "../models";
import { TaskExecutor } from "../orchestrator/services/taskexecution/TaskExecutor";
import { ScheduleFilters, StoredSchedule, WithNextRun } from "../scheduler/ScheduleTypes";
import { TaskSchedulesApi } from "../scheduler/TaskSchedulesApi";
import { logContextFinish, logEndpoint, logError, logInfo, logWarning } from "../shared/logger/TaskFrameworkLogger";
import { getAuthorizedSpaces } from "../shared/util/getAuthorizedSpaces";
import { isVirtualSpace } from "../shared/util/isVirtualSpace";
import { getTaskChainRegistry, TaskRegistryMap } from "../taskRegistryIndex";
import { Parameters } from "../orchestrator/models/Parameters";

/**
 * Handler for the endpoint GET /monitor/{spaceid}/taskchains to get details for TaskChains Overview monitor
 * See section "/monitor/{spaceid}/taskchains" in swagger file (service/task/task-framework-swagger.yaml) for details.
 */
export async function getTaskChainsOverview(request: Request, response: IResponse<TaskFrameworkResponse>): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context = request.context;
  try {
    const spaceId = request.params.spaceid;
    const task: IFilterParameters = {
      applicationId: ApplicationId.TASK_CHAINS,
      activity: Activity.RUN_CHAIN,
    };

    const api = new TaskLogsApi(context, spaceId);
    const logsResponse = await api.getLatestLogHeaders(task);
    const allRuns: TaskChainLog[] = logsResponse.logs;
    const deployer = await DeployService.fromSpaceId(context, spaceId);
    const deployedNames = await deployer.getDeployedChains();
    logInfo(`[taskChainController] Found ${deployedNames.length} deployed chains and ${allRuns.length} task log headers`, { context });
    const validRuns = allRuns.filter(r => deployedNames.includes(r.objectId!));

    if (allRuns.length > validRuns.length) {
      await deleteInvalidRuns(context, spaceId, allRuns, validRuns);
    }

    let jsonResponse: TaskChainLog[] = validRuns;

    const runObjectIds = validRuns.map(header => header.objectId);
    const notRun = deployedNames.filter(deployedName => !runObjectIds.includes(deployedName));
    if (notRun.length) {
      const notRunHeaders = await getNotRunHeaders(context, spaceId, notRun);
      jsonResponse = validRuns.concat(notRunHeaders as TaskChainLog[]);
    }

    if (request.query.includeBusinessNames === "true") {
      jsonResponse = await logsWithBusinessName<TaskChainLog>(context, spaceId, jsonResponse);
    }

    response.status(StatusCodes.OK).json(jsonResponse);
  } catch (err) {
    logError(["GET /monitor/:spaceid/taskchains", err], { context });
    sendErrorResponse(context, "", { err });
  }
}

async function getNotRunHeaders(context: IRequestContext, spaceId: string, notRun: string[]) {
  const filter: ScheduleFilters = {
    applicationId: ApplicationId.TASK_CHAINS,
    activity: Activity.RUN_CHAIN
  };
  const api = await TaskSchedulesApi.createInstance(context, spaceId);
  const schedules = await api.listSchedules(filter);
  const chainSchedules = schedules.reduce((map, schedule) => {
    map.set(schedule.objectId, schedule);
    return map;
  }, new Map<string, (StoredSchedule & WithNextRun)>());
  const headerFromSchedule = (objectId: string): ITaskLog => {
    const schedule = chainSchedules.get(objectId);
    const logHeader = {
      ...filter,
      objectId,
      spaceId,
      scheduleId: schedule?.scheduleId,
    } as ITaskLog;
    logHeader.scheduleId === undefined && delete logHeader.scheduleId;
    return logHeader;
  }
  return notRun.map(headerFromSchedule);
}

async function deleteInvalidRuns(context: IRequestContext, spaceId: string, allRuns: TaskChainLog[], validRuns: TaskChainLog[]) {
  if (isVirtualSpace(spaceId)) {
    logWarning(`[taskCahinController] deleteInvalidRuns called for spaceId = ${spaceId}`, { context })
    return;
  }
  try {
    const allIds = allRuns.map(r => r.logId!);
    const validIds = validRuns.map(r => r.logId!);
    const invalidIds = allIds.filter(id => !validIds.includes(id));
    if (invalidIds.length) {
      const deleter = await TaskLogDelete.fromContext(context);
      await deleter.deleteByLogIds(spaceId, invalidIds);
    }
  } catch (error) {
    logError([`[taskChainController] Error deleting runs of not deployed chains:`, error], { context });
  }
}

/**
 * Handler for the endpoint GET /tf/{spaceid}/taskchains/{logId} to retrieve details of a given run of a task chain
 * See section "/tf/{spaceid}/taskchains/{logId}" in swagger file (service/task/task-framework-swagger.yaml)
 * for details.
 */
export async function getTaskChainDetails(request: Request, response: IResponse<TaskFrameworkResponse>): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context = request.context;
  try {
    const spaceId = request.params.spaceid;
    const logId = Number(request.params.logid);
    if (Number.isNaN(logId)) {
      throw new CodedError("getTaskChainsDetailsBadRequest", `${logId} is not a valid logId`, StatusCodes.BAD_REQUEST);
    }
    const api = new TaskLogsApi(context, spaceId);
    if (await isHideParentTaskEnabled(context)) {
      api.authorizedSpaces = await getAuthorizedSpaces(context, spaceId);
    }
    let jsonResponse = await api.getTaskChainDetails(logId);
    if (request.query.includeBusinessNames === "true") {
      jsonResponse = await chainLogsWithBusinessName<TaskChainDetails>(context, spaceId, jsonResponse);
    }
    response.status(StatusCodes.OK).json(jsonResponse);

  } catch (err) {
    logError(["GET /tf/:spaceid/taskchains/:logid", err], { context });
    sendErrorResponse(context, "", { err });
  }
}

/**
 * Handler for the endpoint GET /tf/{spaceid}/taskchains/{chainname}/info to retrieve the current state of the chain.
 */
export async function getTaskChainInfo(request: Request, response: IResponse<TaskFrameworkResponse>): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context = request.context;
  try {
    const spaceId = request.params.spaceid;
    const objectId = request.params.chainname;
    const task: IFilterParameters = {
      objectId,
      applicationId: ApplicationId.TASK_CHAINS,
      activity: Activity.RUN_CHAIN,
    };
    const api = new TaskLogsApi(context, spaceId);
    const logsResponse = await api.getLatestLogHeaders(task);
    const latestHeaders: TaskChainLog[] = logsResponse.logs;
    const [log] = latestHeaders;
    if (!log) {
      const deployer = await DeployService.fromSpaceId(context, spaceId);
      const deployedNames = await deployer.getDeployedChains();
      if (!deployedNames.includes(objectId)) {
        throw new CodedError("chainNotFound", `no chain with name ${objectId} deployed in space ${spaceId}`, StatusCodes.NOT_FOUND);
      }
    }
    const withObjectId = log ?? { objectId };
    let jsonResponse = withObjectId;
    if (request.query.includeBusinessNames === "true") {
      const [withBizzName] = await logsWithBusinessName<ITaskLog>(context, spaceId, [withObjectId]);
      jsonResponse = withBizzName;
    }
    response.status(StatusCodes.OK).json(jsonResponse);
  } catch (err) {
    logError(["GET /tf/:spaceid/taskchains/:chainname/info", err], { context });
    sendErrorResponse(context, "", { err });
  }
}

/**
 * Handler for the endpoint POST /tf/{spaceid}/taskchains/{chainname}/start to
 * (direct) execute a task chain.
 * See section "/tf/{spaceid}/taskchains/{chainname}/start" in swagger file (service/task/task-framework-swagger.yaml)
 * for details.
 */
export async function runTaskChain(request: Request, response: IResponse<TaskFrameworkResponse>): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context = request.context;
  try {
    const spaceId = request.params.spaceid;
    const objectId = request.params.chainname;
    /** Currently the request body can contain only input parameter values. */
    const parameters: Parameters = request.body.inputParameters ? { inputParameters: request.body.inputParameters } : {};
    const task: TaskIdentifier = {
      spaceId,
      objectId,
      applicationId: ApplicationId.TASK_CHAINS,
      activity: Activity.RUN_CHAIN,
    };

    const taskExecutor = await TaskExecutor.createDirectWithTaskIdentifier(context, task, parameters);
    taskExecutor.needsCheckIsValid = true;
    const logId = await taskExecutor.trigger();
    response.status(StatusCodes.ACCEPTED).send({ logId });
  } catch (error) {
    logError(["POST /tf/:spaceid/taskchains/:chainname/start", error], { context });
    let err = error;
    if (error instanceof TaskFactoryError) {
      err = new CodedError(error.code || "chainNotFound", `${error.task?.objectId} is not a deployed a chain`, error.status || StatusCodes.BAD_REQUEST);
    }
    sendErrorResponse(context, "", { err });

  }
}

/**
 * Handler for the endpoint POST /tf/{spaceid}/taskchains/{chainname}/retry to
 * (manually) retry a task chain.
 * See section "/tf/{spaceid}/taskchains/{chainname}/retry" in swagger file (service/task/task-framework-swagger.yaml)
 * for details.
 */
export async function retryTaskChain(request: Request, response: IResponse<TaskFrameworkResponse>): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context = request.context;
  const backgroundContext = RequestContext.createNewForBackground(context);
  try {
    const spaceId = request.params.spaceid;
    const objectId = request.params.chainname;
    const taskLogApi = new TaskLogsApi(context, spaceId);
    const logHeaderResponse = await taskLogApi.getLatestLogHeaders({ objectId, applicationId: ApplicationId.TASK_CHAINS, activity: Activity.RUN_CHAIN });
    const latestLog = logHeaderResponse.logs[0];
    if (!latestLog) {
      throw new ChainRetryError("retryTaskChainBadRequest", `unable to find runs for ${objectId} in space ${spaceId}`);
    }
    if (latestLog.status !== Status.FAILED) {
      throw new ChainRetryError("retryTaskChainBadRequest", `latest run of ${objectId} has not failed, so it cannot be retried.`);
    }

    let retryChain;
    if (await FeatureFlagProvider.isFeatureActive(context, "DWCO_TASK_FRAMEWORK_DEEP_RETRY")) {
      retryChain = new ChainRetryNew(backgroundContext, latestLog);
    } else {
      retryChain = new ChainRetry(backgroundContext, latestLog);
    }
    await retryChain.initiateChainRetry();
    response.status(StatusCodes.ACCEPTED).send();
  } catch (error) {
    logError(["POST /tf/:spaceid/taskchains/:chainname/retry", error], { context });
    let err = error;
    if (error instanceof ChainRetryError) {
      err = error.codedError;
    }
    sendErrorResponse(context, "", { err });
    logInfo(`Finished backgroundContext ${context.correlationId} on error ${error.message}`, { context });
  } finally {
    logContextFinish(backgroundContext, "[taskChainController] retryTaskChain");
    await backgroundContext.finish();
  }
}

/**
 * Handler for the endpoint POST /tf/{spaceid}/taskchains/{objectId}/updatemailinglist to
 * update the mailing list of a chain in the space$TEC schema
 */
export async function updateNotificationMailingList(request: Request, response: IResponse<TaskFrameworkResponse>): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context: IRequestContext = request.context;
  try {
    const spaceId = request.params.spaceid;
    const objectId = request.params.objectid;
    const { tenantMembers, others } = request.body as NotificationsMailingListEntry;
    const mailingList = { tenantMembers, others };

    const applicationId = ApplicationId.TASK_CHAINS;

    const notificationsValidator = new NotificationsMailingListValidator(context);
    await notificationsValidator.validateNotificationCriteria(spaceId, objectId, mailingList, applicationId)

    const notificationManager = await NotificationsMailingList.fromContext(context, spaceId, objectId, applicationId);

    await notificationManager.upsertMailingList(mailingList)

    response.status(StatusCodes.ACCEPTED).send();
  } catch (err) {
    logError(["POST /tf/:spaceid/taskchains/:objectId/updatemailinglist", err], { context });
    sendErrorResponse(context, "", { err });
  }
}

/**
 * Handler for the endpoint POST /tf/{spaceid}/taskchains/{objectId}/getmailinglist to
 * update the mailing list of a chain in the space$TEC schema
 */
export async function getNotificationMailingList(request: Request, response: IResponse<TaskFrameworkResponse>): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context: IRequestContext = request.context;
  try {
    const spaceId = request.params.spaceid;
    const objectId = request.params.objectid;
    const applicationId = ApplicationId.TASK_CHAINS;

    const notificationManager = await NotificationsMailingList.fromContext(context, spaceId, objectId, applicationId);
    const mailingList = await notificationManager.getMailingList()
    response.status(StatusCodes.ACCEPTED).json(mailingList ?? { tenantMembers: [], others: [] });
  } catch (err) {
    logError(["POST /tf/:spaceid/taskchains/:objectId/getmailinglist", err], { context });
    sendErrorResponse(context, "", { err });
  }
}

/**
 * Returns whether the object (any view, remote table, dataflow) in its current state can be part of a chain or not.
 * For each object an own implementation exists which specifies with which states/structures of the object this is allowed.
 */
export async function getIsChainable(request: Request, response: IResponse<TaskFrameworkResponse>): Promise<void> {
  const context = request.context;
  const spaceName = request.params.spaceid;
  const objectName = request.params.objectid;
  const applicationId = request.query.applicationId;
  const activity = request.query.activity as Activity;
  if (!(activity in Activity)) {
    response.status(StatusCodes.BAD_REQUEST).send(`Invalid Activity: ${activity}`);
    return;
  }
  const registry = await getTaskChainRegistry(context, spaceName);
  const validator = registry.find(x => x.applicationId === applicationId && x.activity === activity);
  if (!validator) {
    throw new Error(`Missing Chain registry for the applicationId: ${applicationId} & activity ${activity} `);
  }
  try {
    const message: IObjectChainableResult = await validator?.objectValidator(context, spaceName, objectName, activity);
    response.status(StatusCodes.OK).json(message);
  } catch (error) {
    logError(["GET /tf/:spaceid/:objectid/chainable", error], { context });
    response.status(StatusCodes.INTERNAL_SERVER_ERROR).json(`An internal server error occurred upon chainability check for object ${objectName}.`);
  }
}

/**
 * Returns a list of ApplicationIds & Activities that are allowed in a Task Chain.
 */
export async function getChainableTasks(request: Request, response: IResponse<TaskFrameworkResponse>): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context = request.context;
  const spaceName = request.params.spaceid;
  try {
    const taskChainRegistry = await getTaskChainRegistry(context, spaceName);
    const chainableTasks: Array<{ applicationId: ApplicationId, activity: Activity, taskType: TaskType, technicalType: RepositoryObjectType | undefined, capabilities?: Array<Partial<{ [K in TaskCapabilities]: boolean }>> }> = [];

    for (const registry of taskChainRegistry) {
      const { applicationId, activity, featureFlags, capabilities, taskType } = registry;
      const technicalType = TaskRegistryMap.get(applicationId)?.find(task => task.activity === activity)?.repositoryObjectType;
      const task = registry.capabilities?.length ? { applicationId, activity, technicalType, capabilities, taskType } : { applicationId, activity, technicalType, taskType };
      if (featureFlags?.length) {
        try {
          // eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled
          const featureFlagsStatus = await Promise.all(featureFlags.map((flag) =>
            FeatureFlagProvider.isFeatureActive(context, flag)
          ));
          if (featureFlagsStatus.includes(false)) {
            continue;
          }
          chainableTasks.push(task);
        } catch (error) {
          logError([`Error thrown by FF provider for Feature flags ${featureFlags.join()}`, error], { context });
        }
      } else {
        chainableTasks.push(task);
      }
    }
    response.status(StatusCodes.OK).json(chainableTasks);
  } catch (error) {
    logError(["GET /tf/:spaceid/chainabletasks", error], { context });
    response.status(StatusCodes.INTERNAL_SERVER_ERROR).json(`An internal server error occurred while retrieving chainable tasks.`);
  }
}

/**
 * Returns a list of Chain Parameters.
 */
export async function getChainParameters(request: Request, response: IResponse<TaskFrameworkResponse>): Promise<void> {
  logEndpoint(LogLevel.Info, request);
  const context = request.context;
  const spaceName = request.params.spaceid;
  const objectId = request.params.objectid;
  const applicationId = request.query.applicationId as ApplicationId;
  const activity = request.query.activity as Activity;
  if (!(applicationId in ApplicationId)) {
    response.status(StatusCodes.BAD_REQUEST).send(`Invalid ApplicationId: ${applicationId}`);
    return;
  }
  if (!(activity in Activity)) {
    response.status(StatusCodes.BAD_REQUEST).send(`Invalid Activity: ${activity}`);
    return;
  }
  let parameters: IChainableParameter[] = [];
  try {
    const taskChainRegistry = await getTaskChainRegistry(context, spaceName);
    const entry = taskChainRegistry.find(x => x.applicationId === applicationId)
    const paramOption = entry?.parametersOption;
    if (paramOption?.state === parametersState.ENABLED) {
      parameters = await paramOption.retrieve(context, activity, spaceName, objectId);
    }
    response.status(StatusCodes.OK).json(parameters);
  } catch (error) {
    logError(["GET /tf/:spaceid/:objectid/parameters", error], { context });
    response.status(StatusCodes.INTERNAL_SERVER_ERROR).json(`An internal server error occurred while retrieving task parameters.`);
  }
}
