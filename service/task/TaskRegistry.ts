/** Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved. */
/* eslint-disable @sap/dwc-lints/service-dependency-analysis */
import { RepositoryObjectType } from "@sap/deepsea-types";
import { BackupTask } from "../backup/BackupTask";
import { connectionMetricCollector } from "../connections/connectionMertricCollector";
import { LsCertificateRotationTask } from "../connections/largeSystems/lsCertificateRotationTask";
import * as DisableConnectivityProxyTask from "../connections/tasks/DisableConnectivityProxyTask";
import { UclCertificateRotationTask } from "../connections/ucl/uclCertificateRotationTask";
import * as DataflowDailyMetricsTask from "../dataflow/metrices/dataflowDailyMetricsTask";
import { DataflowStopTask } from "../dataflow/task/dataflowStopTask";
import * as DataflowTask from "../dataflow/task/dataflowTask";
import * as DPSPurgeInactiveWorkforcePersonTask from "../dataplane/tasks/DPSPurgeInactiveWorkforcePersonTask";
import * as RMSPodLimitTask from "../diEmbedded/podCount/tasks/RMSPodLimitTask";
import { AddEcnTask, EcnExecutorTask, EcnReplicationTask, EcnRoutingTask, RemoveEcnTask } from "../ecn";
import { EcnAdvisorTask } from "../ecn/tasks/AdvisorTask";
import * as HaumDataTransferTask from "../haum/task/HaumDataTransferTask";
import * as ILDataDeletionTask from "../intelligentlookup/task/ilDataDeletionTask";
import * as IntelligentLookupTask from "../intelligentlookup/task/intelligentLookupTask";
import * as MetricsCollectorTask from "../metering/metrics/tasks/MetricsCollectorTask";
import * as MeteringServiceSubscriptionTask from "../metering/reporting/tasks/MeteringServiceSubscriptionTask";
import * as MeteringServiceTask from "../metering/reporting/tasks/MeteringServiceTask";
import * as MetricsReportingFromMeteringTable from "../metering/reporting/tasks/MetricsReportingFromMeteringTableTask";
import * as OneDataCatalogTask from "../oneDataCatalog/task/OneDataCatalogTask";
import { HanaKeyRenewalTask } from "../provisioning/tasks/HanaKeyRenewalTask";
import * as ReplicationflowDailyMetricsTask from "../replicationflow/metrics/replicationflowDailyMetricsTask";
import * as ReplicationflowWeeklyMetricsTask from "../replicationflow/metrics/replicationflowWeeklyMetricsTask";
import * as ReplicationflowTask from "../replicationflow/task/replicationflowTask";
import { ImportHandlerTaskBB } from "../repository/importmanager/lib/ImportHandlerTaskBB";
import { ImportHandlerTaskRE } from "../repository/importmanager/lib/ImportHandlerTaskRE";
import * as DpAgentConnectionStatusTask from "../reuseComponents/spaces/src/task/DpAgentConnectionStatusTask";
import * as SpaceLockingTask from "../reuseComponents/spaces/src/task/SpaceLockingTask";
import { SpaceMigrationTask } from "../reuseComponents/spaces/src/task/SpaceMigrationTask";
import * as CancelModelValidationTask from "../routes/advisor/task/cancelModelValidationTask";
import * as CancelPerformanceAnalysisTask from "../routes/advisor/task/cancelPerformanceAnalysisTask";
import * as CancelViewAnalyzerTask from "../routes/advisor/task/cancelViewAnalyzerTask";
import * as ModelValidationTask from "../routes/advisor/task/modelValidationTask";
import * as PerformanceAnalysisTask from "../routes/advisor/task/performanceAnalysisTask";
import * as ViewAnalyzerTask from "../routes/advisor/task/viewAnalyzerTask";
import { UploadTask } from "../routes/data/uploadTask";
import { FindAndReplaceTask } from "../routes/dataAccess/tasks/FindAndReplaceTask";
import * as AnalyticModelCleanUpTask from "../routes/querybuilder/task/AnalyticModelCleanUpTask";
import * as ReplicationTask from "../routes/remoteTableMonitor/task/ReplicationTask";
import * as StatisticsTask from "../routes/remoteTableMonitor/task/StatisticsTask";
import { RemoteTableMetricsTask } from "../routes/remoteTableMonitor/usageTracking/RemoteTableMetricsTask";
import * as ValidationTask from "../routes/remoteobject/remoteObjectValidationTask";
import * as PasswordRotationTask from "../routes/support/task/PasswordRotationTask";
import * as ViewCancelPersistencyTask from "../routes/viewPersistencyMonitor/task/ViewCancelPersistencyTask";
import * as ViewRemovePersistencyTask from "../routes/viewPersistencyMonitor/task/ViewRemovePersistencyTask";
import * as ViewStartPersistencyTask from "../routes/viewPersistencyMonitor/task/ViewStartPersistencyTask";
import { OutboundTableWeeklyMetricsTask } from "../table/metrics/outboundTableWeeklyMetricsTask";
import { LocalTableDeleteDataTask } from "../table/task/LocalTableDeleteDataTask";
import { LocalTableFilesMergeTask } from "../table/task/LocalTableFilesMergeTask";
import { LocalTableFilesOptimizeTask } from "../table/task/LocalTableFilesOptimizeTask";
import { LocalTableFilesVacuumTask } from "../table/task/LocalTableFilesVacuumTask";
import { LocalTableVariantDeleteDataTask } from "../table/task/LocalTableVariantDeleteDataTask";
import { TransformationFlowCancelTask } from "../transformationFlow/task/TransformationFlowCancelTask";
import { TransformationFlowExecutionTask } from "../transformationFlow/task/TransformationFlowExecutionTask";
import { TransformationFlowResetWatermarkTask } from "../transformationFlow/task/TransformationFlowResetWatermarkTask";
import { TaskFactoryOptions } from "./TaskRegistryTypes";
import * as CleanupTask from "./admin/CleanupTask";
import * as DailyMetricsTask from "./admin/DailyMetricsTask";
import { PacemakerTask } from "./admin/PacemakerTask";
import { ApiTask } from "./apiTask/ApiTask";
import * as ChainTask from "./chains/ChainTask";
import * as WaitTask from "./chains/WaitTask";
import { Activity, ApplicationId } from "./models";
import { noScheduling, simpleScheduling } from "./models/ScheduleType";
import { BWProcessChainTask } from "./nonRepository/bwProcessChain/task/BWProcessChainTask";
import { SQLScriptProcedureTask } from "./nonRepository/sqlScriptProcedure/task/SQLScriptProcedureTask";
import { TaskFrameworkVirtualSpaces } from "./shared/db/TaskFrameworkConstants";
import { logErrorWithOptionalContext } from "./shared/logger/TaskFrameworkLogger";
import * as NotificationTask from "./notificationTask/NotificationTask";
import { LocalTableFilesTruncateTask } from "../table/task/LocalTableFilesTruncateTask";
import { SparkApplicationIndex } from "./models/SparkApplicationIndex";
import { LocalTableFilesVariantTruncateTask } from "../table/task/LocalTableFilesVariantTruncateTask";
import { LocalTableFilesVariantVacuumTask } from "../table/task/LocalTableFilesVariantVacuumTask";

/**
 * Array of TaskFactoryOptions used by TaskFactory to create a task (IExecutable) instance for each application.
 *
 * **IMPORTANT:** Any changes in this array require
 * 1. related changes in its unit tests service/tests/task/orchestrator/controllers/taskexecution/TaskFactory.spec.ts
 * 2. **run** TaskFramework tests in your PR ([service/tests/task, service/tests/deploy/dwcIntegration/task])
 */
const TaskRegistry: TaskFactoryOptions[] = [
  {
    applicationId: ApplicationId.DATA_FLOWS,
    activity: Activity.EXECUTE,
    executable: DataflowTask.DataflowTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_DATAFLOW,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling,
    cancelActivity: Activity.CANCEL,
  },
  {
    applicationId: ApplicationId.DATA_FLOWS,
    activity: Activity.CANCEL,
    executable: DataflowStopTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_DATAFLOW,
    scheduling: noScheduling,
  },
  {
    applicationId: ApplicationId.DATA_FLOWS,
    activity: Activity.DAILY_METRICS,
    executable: DataflowDailyMetricsTask.DataflowDailyMetricsTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.INTELLIGENT_LOOKUP,
    activity: Activity.EXECUTE,
    executable: IntelligentLookupTask.IntelligentLookupTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_IDT,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.INTELLIGENT_LOOKUP,
    activity: Activity.DELETE_DATA,
    executable: ILDataDeletionTask.ILDataDeletionTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_IDT,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.INTELLIGENT_LOOKUP,
    activity: Activity.CREATE_REPLICA,
    executable: EcnReplicationTask,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ECN],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.INTELLIGENT_LOOKUP,
    activity: Activity.DROP_REPLICA,
    executable: EcnReplicationTask,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ECN],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.ONE_DATA_CATALOG,
    activity: Activity.RUN_EXTRACTION,
    executable: OneDataCatalogTask.OneDataCatalogTask,
    featureFlags: ["DWCO_TF_ODC_TASKS"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ODC],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.ONE_DATA_CATALOG,
    activity: Activity.RUN_PROFILING,
    executable: OneDataCatalogTask.OneDataCatalogTask,
    featureFlags: ["DWCO_TF_ODC_TASKS"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ODC],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.ONE_DATA_CATALOG,
    activity: Activity.RUN_RULES,
    executable: OneDataCatalogTask.OneDataCatalogTask,
    featureFlags: ["DWCO_TF_ODC_TASKS"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ODC],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.REMOTE_TABLES,
    activity: Activity.REPLICATE,
    cancelActivity: Activity.CANCEL_REPLICATION,
    executable: ReplicationTask.ReplicationTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_REMOTE_TABLE,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.REMOTE_TABLES,
    activity: Activity.ENABLE_REALTIME,
    cancelActivity: Activity.CANCEL_REPLICATION,
    executable: ReplicationTask.ReplicationTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_REMOTE_TABLE,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.REMOTE_TABLES,
    activity: Activity.REMOVE_REPLICATED_DATA,
    executable: ReplicationTask.ReplicationTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_REMOTE_TABLE,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.REMOTE_TABLES,
    activity: Activity.DISABLE_REALTIME,
    executable: ReplicationTask.ReplicationTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_REMOTE_TABLE,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.REMOTE_TABLES,
    activity: Activity.CANCEL_REPLICATION,
    executable: ReplicationTask.ReplicationTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_REMOTE_TABLE,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.REMOTE_TABLES,
    activity: Activity.REFRESH_STATISTICS,
    executable: StatisticsTask.StatisticsTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_REMOTE_TABLE,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.REMOTE_TABLES,
    activity: Activity.CREATE_STATISTICS,
    executable: StatisticsTask.StatisticsTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_REMOTE_TABLE,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.REMOTE_TABLES,
    activity: Activity.ALTER_STATISTICS,
    executable: StatisticsTask.StatisticsTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_REMOTE_TABLE,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.REMOTE_TABLES,
    activity: Activity.DROP_STATISTICS,
    executable: StatisticsTask.StatisticsTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_REMOTE_TABLE,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.REMOTE_TABLES,
    activity: Activity.DAILY_METRICS,
    executable: RemoteTableMetricsTask,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.CONNECTIONS,
    activity: Activity.COLLECT_METRICS,
    executable: connectionMetricCollector,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    featureFlags: ["DWCO_CONNECTION_METRICS"],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.LOCAL_TABLE,
    activity: Activity.COLLECT_METRICS,
    featureFlags: ["DWCO_TABLE_DELTA_UPSERT_READ_API"],
    executable: OutboundTableWeeklyMetricsTask,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.VALIDATION_SERVICE,
    activity: Activity.VALIDATE,
    executable: ValidationTask.RemoteObjectValidationTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.VIEWS,
    activity: Activity.PERSIST,
    executable: ViewStartPersistencyTask.ViewStartPersistencyTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_VIEW,
    keepLastRelevantRun: true,
    cancelActivity: Activity.CANCEL_PERSISTENCY,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.VIEWS,
    activity: Activity.REMOVE_PERSISTED_DATA,
    executable: ViewRemovePersistencyTask.ViewRemovePersistencyTask,
    isTechnicalAllowed: true, // for ACN import
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_VIEW,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.VIEWS,
    activity: Activity.CANCEL_PERSISTENCY,
    executable: ViewCancelPersistencyTask.ViewCancelPersistencyTask,
    isTechnicalAllowed: true, // for ACN import
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_VIEW,
    scheduling: simpleScheduling,
  },
  {
    applicationId: ApplicationId.VIEWS,
    activity: Activity.EXECUTE_VIEW_ANALYZER,
    executable: ViewAnalyzerTask.ViewAnalyzerTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_VIEW,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling,
    cancelActivity: Activity.CANCEL_VIEW_ANALYZER
  },
  {
    applicationId: ApplicationId.VIEWS,
    activity: Activity.CANCEL_VIEW_ANALYZER,
    executable: CancelViewAnalyzerTask.CancelViewAnalyzerTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_VIEW,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.VIEWS,
    activity: Activity.VALIDATE,
    executable: ModelValidationTask.ModelValidationTask,
    featureFlags: ["DWCO_MODEL_VALIDATION"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_VIEW,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling,
    cancelActivity: Activity.CANCEL_VALIDATE
  },
  {
    applicationId: ApplicationId.VIEWS,
    activity: Activity.CANCEL_VALIDATE,
    executable: CancelModelValidationTask.CancelModelValidationTask,
    featureFlags: ["DWCO_MODEL_VALIDATION"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_VIEW,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.VIEWS,
    activity: Activity.ANALYZE_PERFORMANCE,
    executable: PerformanceAnalysisTask.PerformanceAnalysisTask,
    featureFlags: ["DWCO_VIEW_RUNTIME_METRICS"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_VIEW,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling,
    cancelActivity: Activity.CANCEL_ANALYZE_PERFORMANCE
  },
  {
    applicationId: ApplicationId.VIEWS,
    activity: Activity.CANCEL_ANALYZE_PERFORMANCE,
    executable: CancelPerformanceAnalysisTask.CancelPerformanceAnalysisTask,
    featureFlags: ["DWCO_VIEW_RUNTIME_METRICS"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_VIEW,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.VIEWS,
    activity: Activity.CREATE_REPLICA,
    executable: EcnReplicationTask,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ECN],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.VIEWS,
    activity: Activity.DROP_REPLICA,
    executable: EcnReplicationTask,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ECN],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.TASK_CHAINS,
    activity: Activity.RUN_CHAIN,
    executable: ChainTask.ChainTask,
    cancelActivity: Activity.CANCEL,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    allowedOffSpaces: [],
    repositoryObjectType: RepositoryObjectType.DWC_TASKCHAIN,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.TASK_CHAINS,
    activity: Activity.CANCEL,
    executable: ChainTask.ChainTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    allowedOffSpaces: [],
    repositoryObjectType: RepositoryObjectType.DWC_TASKCHAIN,
    keepLastRelevantRun: true,
    featureFlags: ["DWCO_TASK_FRAMEWORK_CANCEL_CHAIN"],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.TASK_CHAINS,
    activity: Activity.RUN_BACKGROUND,
    executable: ChainTask.ChainTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.TF],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.TASK_CHAINS,
    activity: Activity.RUN_CHAIN_TECHNICAL,
    executable: ChainTask.ChainTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ECN],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.NOTIFICATION,
    activity: Activity.SEND_EMAIL,
    executable: NotificationTask.NotificationTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    featureFlags: ["DWCO_TASK_FRAMEWORK_NOTIFICATION_TASK"],
    cancelActivity: Activity.CANCEL,
    scheduling: noScheduling,
  },
  {
    applicationId: ApplicationId.NOTIFICATION,
    activity: Activity.CANCEL,
    executable: NotificationTask.NotificationTask,
    featureFlags: ["DWCO_TASK_FRAMEWORK_NOTIFICATION_TASK"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    cancelActivity: Activity.CANCEL,
    scheduling: noScheduling,
  },
  {
    applicationId: ApplicationId.WAIT,
    activity: Activity.RUN_BACKGROUND,
    executable: WaitTask.WaitTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.TF],
    scheduling: noScheduling,
    cancelActivity: Activity.CANCEL
  },
  {
    applicationId: ApplicationId.WAIT,
    activity: Activity.CANCEL,
    executable: WaitTask.WaitTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.TF],
    scheduling: noScheduling,
  },
  {
    applicationId: ApplicationId.REPLICATION_FLOWS,
    activity: Activity.RUN_PERMANENT,
    executable: ReplicationflowTask.ReplicationflowTask,
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_REPLICATIONFLOW,
    keepLastRelevantRun: true,
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.REPLICATION_FLOWS,
    activity: Activity.RUN_PERMANENT_TECHNICAL,
    executable: ReplicationflowTask.ReplicationflowTask,
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE", "DWCO_BDC"],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    repositoryObjectType: RepositoryObjectType.DWC_REPLICATIONFLOW,
    keepLastRelevantRun: true,
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.REPLICATION_FLOWS,
    activity: Activity.EXECUTE,
    executable: ReplicationflowTask.ReplicationflowTask,
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_REPLICATIONFLOW,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.TASK_FRAMEWORK,
    activity: Activity.TASKLOG_CLEANUP,
    executable: CleanupTask.CleanupTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.TASK_FRAMEWORK,
    activity: Activity.TASKLOG_CLEANUP_MANUAL,
    executable: CleanupTask.CleanupTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.TF],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.TASK_FRAMEWORK,
    activity: Activity.DAILY_METRICS,
    executable: DailyMetricsTask.DailyMetricsTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.PACEMAKER,
    activity: Activity.MONITOR,
    executable: PacemakerTask,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.REPLICATION_FLOWS,
    activity: Activity.DAILY_METRICS,
    executable: ReplicationflowDailyMetricsTask.ReplicationflowDailyMetricsTask,
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.REPLICATION_FLOWS,
    activity: Activity.WEEKLY_METRICS,
    executable: ReplicationflowWeeklyMetricsTask.ReplicationflowWeeklyMetricsTask,
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.DP_AGENTS,
    activity: Activity.DPAGENT_STATUS_NOTIFICATION,
    executable: DpAgentConnectionStatusTask.DpAgentConnectionStatusTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL, TaskFrameworkVirtualSpaces.TF],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.HANA_CONNECTIVITY_PROXY,
    activity: Activity.DISABLE,
    executable: DisableConnectivityProxyTask.DisableConnectivityProxyTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.PASSWORD_ROTATION,
    activity: Activity.CUSTOMER_HANA_ROTATE,
    executable: PasswordRotationTask.PasswordRotationTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.BACKUP,
    activity: Activity.BACKUP,
    executable: BackupTask,
    featureFlags: ["DWCO_REPOSITORY_BACKUP"],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.METERING_SERVICE,
    activity: Activity.METERING_SERVICE,
    executable: MeteringServiceTask.MeteringServiceTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.METERING_SERVICE_SUBSCRIPTION,
    activity: Activity.METERING_SERVICE_SUBSCRIPTION,
    executable: MeteringServiceSubscriptionTask.MeteringServiceSubscriptionTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.METERING_SERVICE,
    activity: Activity.REPORT_METRICS_FROM_METERING_TABLE,
    executable: MetricsReportingFromMeteringTable.MetricsReportingFromMeteringTable,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.METERING_SERVICE,
    activity: Activity.COLLECT_METRICS,
    executable: MetricsCollectorTask.MetricsCollectorTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.METERING_SERVICE,
    activity: Activity.COLLECT_METRICS_TEST,
    executable: MetricsCollectorTask.MetricsCollectorTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.METERING_SERVICE,
    activity: Activity.SET_DEFAULT_RMS_POD_LIMIT,
    executable: RMSPodLimitTask.RMSPodLimitTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.HAUM_INTEGRATION,
    activity: Activity.TRANSFER,
    executable: HaumDataTransferTask.HaumDataTransferTask,
    featureFlags: ["DWCO_INFRA_SCHEDULED_ODP_DATA_TRANSFER"],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.DATA_PLANE_SERVICE,
    activity: Activity.PURGE_WORKFORCE_PERSON_DATA,
    executable: DPSPurgeInactiveWorkforcePersonTask.DPSPurgeInactiveWorkforcePersonTask,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.ANALYTICAL_MODEL_PREVIEW,
    activity: Activity.ANALYTICAL_MODEL_CLEANUP_TASK,
    executable: AnalyticModelCleanUpTask.AnalyticModelCleanUpTask,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.SPACE_LOCKING,
    activity: Activity.LOCKING,
    executable: SpaceLockingTask.SpaceLockingTask,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.ELASTIC_COMPUTE_NODE,
    activity: Activity.ADD,
    executable: AddEcnTask,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ECN],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.ELASTIC_COMPUTE_NODE,
    activity: Activity.REMOVE,
    executable: RemoveEcnTask,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ECN],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.ELASTIC_COMPUTE_NODE,
    activity: Activity.GENERATE_START_CHAIN,
    executable: EcnExecutorTask,
    featureFlags: [],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ECN],
    scheduling: EcnExecutorTask.twinScheduling
  },
  {
    applicationId: ApplicationId.ELASTIC_COMPUTE_NODE,
    activity: Activity.GENERATE_STOP_CHAIN,
    executable: EcnExecutorTask,
    featureFlags: [],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ECN],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.LOCAL_TABLE,
    activity: Activity.CREATE_REPLICA,
    executable: EcnReplicationTask,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ECN],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.LOCAL_TABLE,
    activity: Activity.DROP_REPLICA,
    executable: EcnReplicationTask,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ECN],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.SPACE,
    activity: Activity.ROUTE_COMPUTE_SERVER,
    executable: EcnRoutingTask,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ECN],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.SPACE,
    activity: Activity.ROUTE_COORDINATOR,
    executable: EcnRoutingTask,
    featureFlags: [],
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ECN],
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.ELASTIC_COMPUTE_NODE,
    activity: Activity.COLLECT_MONITORING_DATA,
    executable: EcnAdvisorTask,
    featureFlags: ["DWCO_ECN_ADVISOR_CONFIGURATION"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.ECN],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.TRANSFORMATION_FLOWS,
    activity: Activity.EXECUTE,
    executable: TransformationFlowExecutionTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_TRANSFORMATIONFLOW,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling,
    cancelActivity: Activity.CANCEL,
    defaultSparkAppIndex: SparkApplicationIndex.APP_400
  },
  {
    applicationId: ApplicationId.TRANSFORMATION_FLOWS,
    activity: Activity.SIMULATE_RUN,
    executable: TransformationFlowExecutionTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_TRANSFORMATIONFLOW,
    keepLastRelevantRun: true,
    scheduling: noScheduling,
    cancelActivity: Activity.CANCEL
  },
  {
    applicationId: ApplicationId.TRANSFORMATION_FLOWS,
    activity: Activity.CANCEL,
    executable: TransformationFlowCancelTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_TRANSFORMATIONFLOW,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.TRANSFORMATION_FLOWS,
    activity: Activity.RESET_WATERMARKS,
    executable: TransformationFlowResetWatermarkTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_TRANSFORMATIONFLOW,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.CUSTOMER_HANA,
    activity: Activity.RENEW_SERVICE_KEY,
    executable: HanaKeyRenewalTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.LOCAL_TABLE,
    activity: Activity.DELETE_DATA,
    executable: LocalTableDeleteDataTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_LOCAL_TABLE,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling,
    defaultSparkAppIndex: SparkApplicationIndex.APP_300
  },
  {
    applicationId: ApplicationId.LOCAL_TABLE,
    activity: Activity.UPLOAD_DATA,
    executable: UploadTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_LOCAL_TABLE,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.LOCAL_TABLE,
    activity: Activity.REMOVE_DELETED_RECORDS,
    executable: LocalTableDeleteDataTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_LOCAL_TABLE,
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.LOCAL_TABLE_VARIANT,
    activity: Activity.DELETE_DATA,
    executable: LocalTableVariantDeleteDataTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_LOCAL_TABLE,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.LOCAL_TABLE_VARIANT,
    activity: Activity.TRUNCATE_FILES,
    executable: LocalTableFilesVariantTruncateTask,
    featureFlags: ["DWCO_LTA_DATA_DELETION"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_LOCAL_TABLE,
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.LOCAL_TABLE_VARIANT,
    activity: Activity.VACUUM_FILES,
    featureFlags: ["DWCO_LTA_DATA_DELETION"],
    executable: LocalTableFilesVariantVacuumTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_LOCAL_TABLE,
    scheduling: simpleScheduling
  },

  {
    applicationId: ApplicationId.LOCAL_TABLE,
    activity: Activity.MERGE_FILES,
    executable: LocalTableFilesMergeTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_LOCAL_TABLE,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling,
    defaultSparkAppIndex: SparkApplicationIndex.APP_300
  },
  {
    applicationId: ApplicationId.LOCAL_TABLE,
    activity: Activity.OPTIMIZE_FILES,
    executable: LocalTableFilesOptimizeTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_LOCAL_TABLE,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling,
    defaultSparkAppIndex: SparkApplicationIndex.APP_300
  },
  {
    applicationId: ApplicationId.LOCAL_TABLE,
    activity: Activity.TRUNCATE_FILES,
    executable: LocalTableFilesTruncateTask,
    featureFlags: ["DWCO_LOCAL_TABLE_TASKS_ACTIVE_RECORDS"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_LOCAL_TABLE,
    keepLastRelevantRun: true,
    scheduling: simpleScheduling,
    defaultSparkAppIndex: SparkApplicationIndex.APP_300
  },
  {
    applicationId: ApplicationId.LOCAL_TABLE,
    activity: Activity.VACUUM_FILES,
    executable: LocalTableFilesVacuumTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_LOCAL_TABLE,
    keepLastRelevantRun: true,
    scheduling: noScheduling,
    defaultSparkAppIndex: SparkApplicationIndex.APP_300
  },
  {
    applicationId: ApplicationId.CONNECTIONS,
    activity: Activity.UCL_CERTIFICATE_ROTATE,
    executable: UclCertificateRotationTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.CONNECTIONS,
    activity: Activity.HDLF_CERTIFICATE_ROTATE,
    executable: LsCertificateRotationTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.REPOSITORY_EXPLORER,
    activity: Activity.MODEL_IMPORT,
    executable: ImportHandlerTaskRE,
    isTechnicalAllowed: true,
    isBusinessAllowed: true,
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.BUSINESS_BUILDER,
    activity: Activity.MODEL_IMPORT,
    executable: ImportHandlerTaskBB,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.SQL_SCRIPT_PROCEDURE,
    activity: Activity.RUN,
    executable: SQLScriptProcedureTask,
    featureFlags: ["DWCO_INFRA_TASKS_PROCEDURES"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.BW_PROCESS_CHAIN,
    activity: Activity.RUN,
    executable: BWProcessChainTask,
    featureFlags: ["DWCO_INFRA_TASKS_BW_PROCESS_CHAIN"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.SPACE,
    activity: Activity.MIGRATE,
    executable: SpaceMigrationTask,
    isTechnicalAllowed: true,
    isBusinessAllowed: false,
    allowedOffSpaces: [TaskFrameworkVirtualSpaces.GLOBAL],
    scheduling: simpleScheduling
  },
  {
    applicationId: ApplicationId.API,
    activity: Activity.RUN,
    executable: ApiTask,
    featureFlags: ["DWCO_INFRA_TASKS_API_TASK"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    cancelActivity: Activity.CANCEL,
    scheduling: noScheduling,
  },
  {
    applicationId: ApplicationId.API,
    activity: Activity.CANCEL,
    executable: ApiTask,
    featureFlags: ["DWCO_INFRA_TASKS_API_TASK"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    scheduling: noScheduling
  },
  {
    applicationId: ApplicationId.API,
    activity: Activity.TEST_RUN,
    executable: ApiTask,
    featureFlags: ["DWCO_INFRA_TASKS_API_TASK"],
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    cancelActivity: Activity.CANCEL,
    scheduling: noScheduling,
  },
  {
    applicationId: ApplicationId.LOCAL_TABLE,
    activity: Activity.FIND_AND_REPLACE,
    executable: FindAndReplaceTask,
    isTechnicalAllowed: false,
    isBusinessAllowed: true,
    repositoryObjectType: RepositoryObjectType.DWC_LOCAL_TABLE,
    keepLastRelevantRun: true,
    scheduling: noScheduling,
    defaultSparkAppIndex: SparkApplicationIndex.APP_300
  },
];

/**
 * Map of TaskFactoryOptions grouped by applicationId.
 */
const TaskRegistryMap = TaskRegistry
  .reduce(
    (map: Map<ApplicationId, TaskFactoryOptions[]>, taskFactoryOption: TaskFactoryOptions) => {
      const applicationId = taskFactoryOption.applicationId;
      const taskFactoryOptions: TaskFactoryOptions[] = map.get(applicationId) || [];
      if (!taskFactoryOptions.some((x) => x.activity === taskFactoryOption.activity)) {
        if (taskFactoryOption.executable !== undefined) {
          taskFactoryOptions.push(taskFactoryOption);
        } else {
          logErrorWithOptionalContext(`[TaskRegistryMap] TaskFactoryOptions for ApplicationId '${applicationId}' and Activity '${taskFactoryOption.activity}' is missing an executable.`);
        }
      }
      map.set(applicationId, taskFactoryOptions);
      return map;
    },
    new Map<ApplicationId, TaskFactoryOptions[]>()
  );


export { TaskRegistry, TaskRegistryMap };
