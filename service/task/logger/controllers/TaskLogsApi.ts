/** Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved. */

import { IRequestContext } from "@sap/deepsea-types";
import { ILogOptions } from "@sap/dwc-logger";
import { ChainValidator } from "../../chains/ChainValidator";
import { TaskChainRun } from "../../chains/TaskChainRun";
import { TaskChainRunNode, TaskChainRunNodesWithLogs } from "../../chains/TaskChainRunNode";
import { TaskChainRunNodesHistory, TaskChainRunNodesHistoryWithLogs } from "../../chains/TaskChainRunNodesHistory";
import { ChainObjectId, NodeType, RuntimeStep, WithObjectId } from "../../chains/models";
import { Activity, ApplicationId, TaskIdentifier } from "../../models";
import { ExecuteResult } from "../../models/TaskExecuteResponse";
import { TaskWithParameters } from "../../models/TaskIdentifier";
import { ITask } from "../../orchestrator/models/ITask";
import { Parameters, TFParams } from "../../orchestrator/models/Parameters";
import { TaskFactory } from "../../orchestrator/services/taskexecution/TaskFactory";
import { LockEntry, TaskLock } from "../../orchestrator/services/tasklock";
import { isTimezoneEnabled } from "../../scheduler/isTimezoneEnabled";
import { getNoCommitClient } from "../../shared/db/HanaClient";
import { TaskFrameworkConstants } from "../../shared/db/TaskFrameworkConstants";
import { mapLogHeaders, mapSchedulesAsync } from "../../shared/db/mappingUtils";
import { logError, logInfo, logVerbose, logWarning } from "../../shared/logger/TaskFrameworkLogger";
import { isVirtualSpace } from "../../shared/util/isVirtualSpace";
import { isHideParentTaskEnabled } from "../isHideParentTaskEnabled";
import { ContentSemantic, IFilterParameters, ITaskLog, ITaskLogHeader, Status } from "../models";
import { IParentTask, ITaskLogsWithScheduleInfo, ItaskLogWithMetrics } from "../models/ITaskLog";
import { Substatus, getSubstatusName } from "../models/Substatus";
import { DisplayNameService } from "../services/DisplayNameService";
import { EndTask } from "../services/EndTask";
import { ITaskLogMessageBlob, TaskLogMessageBlobService } from "../services/logger/TaskLogMessageBlobService";
import { TaskLogger } from "../services/logger/TaskLogger";
import { TaskMetricService } from "../services/metrics/TaskMetricService";
import { NotifyingTaskLogger } from "../services/notifications/NotifyingTaskLogger";

export interface ITaskLogsResponse {
  locks: LockEntry[];
  logs: ITaskLog[];
}

export type TaskChainLog = ITaskLog & ChainObjectId;

export interface TaskChainDetails extends WithObjectId<TaskChainLog> {
  children?: TaskChainChild[]
}

interface ChildRetryHistory extends Partial<ITaskLog> {
  runNo: number,
  nodeId: number
}
interface TaskChainChild extends Omit<Partial<ITaskLog>, "status"> {
  status: Status | "NOT_TRIGGERED" | undefined,
  nodeId: number,
  retryHistory?: ChildRetryHistory[]
}

export type TaskChainsResponse = TaskChainLog[];

export class TaskLogsApi {

  /**
   * The TaskLogsApi class encapsulates all functionality of the TaskFramework (get-)logs for external consumption,
   * as in an endpoint to be displayed in the UI, for instance.
   * It is the node.js counterpart of TaskFramework's endpoints tf/:space/logs
   *
   * **IMPORTANT:** This class does not perform any authorisation check! It is the caller's responsibility
   * to check the authorisation to access the given space.
   * @param context an IRequestContext which has access to the space
   * @param spaceId the space to which the current context has access to
   */
  constructor(
    private readonly context: IRequestContext,
    private readonly spaceId: string,
  ) { }

  private logOptions: ILogOptions = { context: this.context };

  private _authorizedSpaces: string[] | undefined = undefined;

  /**
   * Gets the authorized spaces array.
   * If authorizedSpaces is not set (undefined), it will default to the spaceId
   * that was used to instantiate the TaskLogsApi class.
   *
   * **IMPORTANT:** This function does not perform any authorization check!
   * It is the caller's responsibility while setting the authorized spaces.
   * @returns an array of space names whose objects the user is authorized to access
   */
  public get authorizedSpaces(): string[] {
    if (!this._authorizedSpaces) {
      this._authorizedSpaces = [this.spaceId];
    }
    return this._authorizedSpaces;
  }

  /**
   * Sets the authorized spaces array.
   * @param spaceIds an array of space names whose objects the user is authorized to access
   * @throws an error if the spaceIds does not contain the spaceId (except for a virtual space) used to instantiate the TaskLogsApi class
   * @throws an error if spaceIds is not an array
   */
  public set authorizedSpaces(spaceIds: string[]) {
    if (!Array.isArray(spaceIds)) {
      throw new Error(`[TaskLogsApi][set authorizedSpaces] spaceIds must be an array of space names.`);
    }
    // If the spaceId used to instantiate the TaskLogsApi class is not a virtual space then it should be present in the spaceIds array.
    // Virtual spaces are not real spaces and hence cannot be checked for authorizations.
    if (!isVirtualSpace(this.spaceId) && !spaceIds.includes(this.spaceId)) {
      throw new Error(`[TaskLogsApi][set authorizedSpaces] spaceIds must contain the spaceId used to instantiate the TaskLogsApi class.`);
    }
    this._authorizedSpaces = spaceIds;
  }

  /**
   * Get log entry for the task identified by logId, along with all messages for that run.
   * If this task run occurred under a task chain, the `parent` property will be populated
   * with the chain's task logId, objectId and spaceId. And if the user does not have access
   * to the task chain, the `parent` property will have only the chain's spaceId,
   * and additional property `authorizedOnParent` as false.
   * @param logId the logId of the (sub-)task.
   * @returns An ITaskLog object with the messages populated
   */
  public async getMessagesForLogId(logId: number): Promise<ITaskLog> {
    logInfo([`[TaskLogsApi][getMessagesForLogId] spaceId = ${this.spaceId} and logId = ${logId}`], this.logOptions);
    try {
      const taskLogger = await TaskLogger.fromContext(this.context);
      const retrievedLogs = await taskLogger.getTaskLogMessages(this.spaceId, logId);
      logInfo([`[TaskLogsApi][getMessagesForLogId] found ${retrievedLogs.length} log entries`], this.logOptions);
      const displayNameService = new DisplayNameService();
      const logs = await displayNameService.replaceUserNameByDisplayName(this.context, retrievedLogs, this.spaceId);
      logInfo([`[TaskLogsApi][getMessagesForLogId] names replaced successfully`], this.logOptions);
      const log = logs[0];
      // if task with logId does not match this.space, log = undefined => skip querying for parent
      if (log) {
        const parent = await this.getParentTask(taskLogger, logId);
        logInfo([`[TaskLogsApi][getMessagesForLogId] task ${logId} has parent = ${JSON.stringify(parent)}`], this.logOptions);
        if (parent) {
          log.parent = parent;
        }
        const taskLogMessageBlobService = await TaskLogMessageBlobService.fromContextAndLogId(this.context, logId);

        const availableJsonMessageList = await taskLogMessageBlobService.getAvailableJsonMessageList();

        // returns a Map<number, string|null> which contains the messageNumber as key and contentSemantic as value.
        const availableBlobMessageMap = await taskLogMessageBlobService.getAvailableBlobMessageMap();
        log.messages?.forEach((message) => {
          if (message.messageNumber && availableBlobMessageMap?.has(message.messageNumber)) {
            message.isBlobContentAvailable = true;
            const conSemVal = availableBlobMessageMap.get(message.messageNumber);
            if (!!conSemVal) {
              message.contentSemanticValue = conSemVal;
            }
          }
          if (message.messageNumber && availableJsonMessageList?.includes(message.messageNumber)) {
            message.isJsonDataAvailable = true;
          }
        });
      }
      return log;
    } catch (error) {
      logError([`[TaskLogsApi][getMessagesForLogId] Error ${error.message}`, error], this.logOptions);
      throw error;
    }
  }

  /**
   * Gets the parent task (chain) for each child task logId in the given array.
   * @param logIds an array of logIds of the tasks
   * @returns a Map where the key is the logId of the task and the value is the parent task.
   * If the logIds is not an array or it is an empty array, a warn will be logged and an empty Map will be returned.
   * The property authorizedOnParent will be false if the parent task space is not in the authorizedSpaces array.
   */
  public async getParentsOfLogIds(logIds: number[]): Promise<Map<number, IParentTask | undefined>> {
    const parentTasks: Map<number, IParentTask | undefined> = new Map();
    if (!Array.isArray(logIds) || logIds.length === 0) {
      logWarning(`[TaskLogsApi][getParentsForLogIds] logIds must be an array of logIds.`, this.logOptions);
    } else {
      const taskLogger = await TaskLogger.fromContext(this.context);
      // eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled
      const result = await Promise.allSettled(logIds.map(logId => this.getParentTask(taskLogger, logId)));
      result.forEach((res, index) => {
        if (res.status === "fulfilled") {
          parentTasks.set(logIds[index], res.value);
        } else {
          parentTasks.set(logIds[index], undefined);
        }
      });
    }
    return parentTasks;
  }

  /**
   * Get the parent task (chain) details for the given logId.
   * @param taskLogger the TaskLogger instance
   * @param logId the logId of the task
   * @returns parent task's:
   *          1. logId, objectId and spaceId,
   *          2. or only spaceId and authorizedOnParent=false
   *             if the parent task space is not in the authorizedSpaces array,
   *             and if the parent task belongs to a virtual space, the user is considered authorized
   *             and logId, objectId and spaceId is returned,
   *          3. or undefined if there is no parent (chain)
   */
  private async getParentTask(taskLogger: TaskLogger, logId: number): Promise<IParentTask | undefined> {
    let parentTask: IParentTask | undefined;
    const parent = await taskLogger.getParentTask(logId);
    if (parent) {
      if (!(await isHideParentTaskEnabled(this.context))) {
        return parent;
      } else {
        const authorizedOnParent = isVirtualSpace(parent.spaceId) ? true : this.authorizedSpaces.includes(parent.spaceId);
        parentTask = authorizedOnParent ? parent : {
          spaceId: parent.spaceId,
          authorizedOnParent /* false */
        }
      }
    }
    return parentTask;
  }

  /**
   * Get the details of a chain run identified by logId, along with all messages for that run and
   * children runs information (status, etc)
   * @param logId the logId of the chain run
   * @returns A TaskChainDetails (extends ITaskLog) with messages and an array of children tasks summary
   */
  public async getTaskChainDetails(logId: number): Promise<TaskChainDetails> {
    try {
      const log = await this.getMessagesForLogId(logId);
      if (log && log.applicationId === ApplicationId.TASK_CHAINS) {// if chain with logId does not match spaceId, log = undefined => skip querying for children
        return await this.getChainChildrenDetails(logId, log)
      } else {
        throw new Error(`[TaskLogsApi][getTaskChainDetails] logId=${logId} is not a valid chain run.`);
      }
    } catch (error) {
      logError([`[TaskLogsApi][getTaskChainDetails] Error ${error.message}`, error], this.logOptions);
      throw error;
    }
  }

  /**
 * Currently Used by CLI as a generic way to get logs for all applications whether it is a Task Chain or another task like remote table, etc.
 * The details of a chain run identified by logId, along with all messages for that run and
 * children runs information (status, etc)
 * @param logId the logId of the chain run
 * @returns A TaskChainDetails (extends ITaskLog) with messages and an array of children tasks summary
 */
  public async getLogDetails(logId: number): Promise<TaskChainDetails> {
    try {
      const log = await this.getMessagesForLogId(logId);
      if (!log) {
        throw new Error(`[TaskLogsApi][getLogDetails] logId=${logId} is not a valid logId.`);
      }

      if (log.applicationId === ApplicationId.TASK_CHAINS) {// if chain with logId does not match spaceId, log = undefined => skip querying for children
        return await this.getChainChildrenDetails(logId, log)
      } else {
        return log;
      }
    } catch (error) {
      logError([`[TaskLogsApi][getTaskChainDetails] Error ${error.message}`, error], this.logOptions);
      throw error;
    }
  }

  /**
   * Forwards the call from the LogController to the TaskLogMessageBlobService to GET the
   * BLOB content for a specific logId and messageNo.
   * @param taskLogId the logId of the task of which the BLOB content is retrieved.
   * @param messageNo the specific messageNo of the message of which the BLOB content is retrieved.
   * @returns an object of type ITaskLogMessageBlob. It contains the BLOB content along with the contentType,
   * JSON DATA, logId and the messageNo.
   */
  public async getMessageBlobContent(taskLogId: number, messageNo: number, contentSemantic?: ContentSemantic): Promise<ITaskLogMessageBlob> {
    logInfo([`[TaskLogsApi][getMessageBlobContent] spaceId = ${this.spaceId}, taskLogId = ${taskLogId} and messageNo = ${messageNo}`], this.logOptions);
    try {
      const taskLogMessageBlobService = await TaskLogMessageBlobService.fromContextAndLogId(this.context, taskLogId);
      const messageBlobContent = await taskLogMessageBlobService.getTaskLogMessageBlob(messageNo, contentSemantic);
      return messageBlobContent;
    } catch (error) {
      logError([`[TaskLogsApi][getMessageBlobContent] Error ${error.message}`, error], this.logOptions);
      throw error;
    }
  }

  /**
   * Gets all log headers (no messages) for a task identified by `filter`. If an scheduled task failed before reaching
   * TaskFramework, the `messages` property of that run will be populated by information about the failure in Pacemaker.
   * @param filter at least one of objectId, applicationId, activity, status to identify the task
   * @param needsUpdateLocked if true **and filter contains at least one of objectId or applicationId**, will checks currently locked tasks for their actual status and automatically updates them.
   * @returns an object containing a list of the (remaining) locks and a list of log headers for the given filter. The returned log headers are the updated ones *after* all locked tasks were resolved.
   */
  public async getLogHeaders(filter: IFilterParameters, needsUpdateLocked: boolean = false, includeVariant: boolean = false): Promise<ITaskLogsResponse> {
    logVerbose([`[TaskLogsApi][getLogHeaders] needsUpdateLocked = ${needsUpdateLocked}`], this.logOptions);
    try {
      TaskLogsApi.validateFilterParameters(filter);
      logVerbose([`[TaskLogsApi][getLogHeaders] filter = ${JSON.stringify(filter)} validated`], this.logOptions);
      const { applicationId, objectId, activity } = filter;
      let locks: LockEntry[] = [];
      if (needsUpdateLocked) {
        const taskIdentifier: Partial<TaskIdentifier> = {
          applicationId,
          objectId,
          activity,
          spaceId: this.spaceId,
        };
        logInfo([`[TaskLogsApi][getLogHeaders] updating locked tasks with lockIdentifier = ${JSON.stringify(taskIdentifier)}`], this.logOptions);
        // Delete properties which are null or undefined
        Object.keys(taskIdentifier).forEach((key: keyof Partial<TaskIdentifier>) => taskIdentifier[key] === undefined && delete taskIdentifier[key]);
        locks = await this.updateLockedTasks(taskIdentifier);
        logInfo([`[TaskLogsApi][getLogHeaders] ${locks.length} active after updating locked tasks`], this.logOptions);
      }
      const taskLogger = await TaskLogger.fromContext(this.context);
      const retrievedLogs = await taskLogger.getTaskLogHeaders(this.spaceId, filter, includeVariant);
      const displayNameService = new DisplayNameService();
      const logs = await displayNameService.replaceUserNameByDisplayName(this.context, retrievedLogs, this.spaceId);
      logInfo([`[TaskLogsApi][getLogHeaders] names replaced successfully`], this.logOptions);
      const logHeaderResponse: ITaskLogsResponse = { locks, logs };
      return logHeaderResponse;
    } catch (error) {
      logError([`[TaskLogsApi][getLogHeaders] Error ${error.message}`, error], this.logOptions);
      throw error;
    }
  }

  /**
   * Gets the most recent log headers for each object in this space (according to `filter`), like the latest logs
   * for all ApplicationId.VIEWS in this space.
   * @param filter at least one of objectId, applicationId, and activity to identify the task
   * @returns an object containing a list of the log headers for the given filter. Notice that this method **does not** call any update on the locked tasks.
   */
  public async getLatestLogHeaders(filter: IFilterParameters): Promise<ITaskLogsResponse> {
    logVerbose([`[TaskLogsApi][getLatestLogHeaders]`], this.logOptions);
    try {
      TaskLogsApi.validateFilterParameters(filter);
      logVerbose([`[TaskLogsApi][getLatestLogHeaders] filter = ${JSON.stringify(filter)} validated`], this.logOptions);
      const taskLogger = await TaskLogger.fromContext(this.context);
      const retrievedLogs = await taskLogger.getLatestTaskLogs(this.spaceId, filter);
      const displayNameService = new DisplayNameService();
      const logs = await displayNameService.replaceUserNameByDisplayName(this.context, retrievedLogs, this.spaceId);
      const logHeaderResponse: ITaskLogsResponse = { locks: [], logs };
      return logHeaderResponse;
    } catch (error) {
      logError([`[TaskLogsApi][getLatestLogHeaders] Error ${error.message}`, error], this.logOptions);
      throw error;
    }
  }

  /**
   * Gets the most recent log headers for each object in this space (according to `filter`), like the latest logs
   * @param filter The applicationId, activities, and status to filter the logs. If activities is empty, logs for all activities will be returned.
   * @returns The latest (by start time) runs excluding those that are locked, filtered by the given filter.
   */
  public async getTopLatestTaskLogs(filter: { applicationId: ApplicationId, activities?: Activity[], status?: Status }): Promise<ITaskLogsResponse> {
    logVerbose([`[TaskLogsApi][getTopLatestTaskLog]`], this.logOptions);
    try {
      if (!filter.applicationId) {
        throw new Error(`filter must contain applicationId.`);
      }
      let activities = filter.activities ?? [];
      if (!Array.isArray(filter.activities)) {
        activities = [];
      }
      const clonedFilter = { ...filter, activities, spaceId: this.spaceId };
      const taskLogger = await TaskLogger.fromContext(this.context);
      const retrievedLogs = await taskLogger.getTopLatestTaskLogs(clonedFilter);
      const displayNameService = new DisplayNameService();
      const logs = await displayNameService.replaceUserNameByDisplayName(this.context, retrievedLogs, this.spaceId);
      const logHeaderResponse: ITaskLogsResponse = { locks: [], logs };
      return logHeaderResponse;
    } catch (error) {
      logError([`[TaskLogsApi][getTopLatestTaskLog] Error ${error.message}`, error], this.logOptions);
      throw error;
    }
  }

  /**
 * Gets log entry for a given logId, along with all messages for that run. In adddition to the Metrics associated with that logId
 * @param logId the logId of the task.
 * @returns an object with 2 properties. One carrying log entry and messages, the other property carries the
 * corresponding task metrics.
 */
  public async getLogsAndMetrics(logId: number): Promise<ItaskLogWithMetrics> {
    logVerbose([`[TaskLogsApi][getLogsAndMetrics]`], this.logOptions);
    try {
      const logDetails = await this.getMessagesForLogId(logId);
      const metricsList = await TaskMetricService.getMetricsList(this.context, [logId]);
      const logsAndMetrics: ItaskLogWithMetrics = {
        logDetails,
        metrics: metricsList[0]
      }
      return logsAndMetrics;
    } catch (error) {
      logError([`[TaskLogsApi][getLogsAndMetrics] Error ${error.message}`, error], this.logOptions);
      throw error;
    }
  }

  /**
   * Gets the full description (in English) of a failed run (pacemaker).
   * A failed pacemaker run means that the scheduled run did not reach TaskFramework and so, the task
   * was never triggered. TaskFramework logs these as a FAILED log with one of the sub-statuses in
   * `TaskLogsApi.getEarlyFailSubstatuses()`
   * @param substatusCode one of TaskFrameworkConstants.earlyRunFailureSubstatus
   * @returns the description of such a substatus, or an empty string if the substatus does not have a
   * description.
   */
  public static getSubstatusDescription(substatusCode: Substatus | undefined, context?: IRequestContext): string {
    const substatusString = getSubstatusName(substatusCode);
    const described = Object.keys(TaskFrameworkConstants.SubstatusDescription);
    if (substatusString && described.includes(substatusString)) {
      return TaskFrameworkConstants.SubstatusDescription[substatusString as keyof typeof TaskFrameworkConstants.SubstatusDescription];
    }
    return "";
  }

  /**
   * Returns true if the sub-status corresponds to a failed run (pacemaker).
   * A failed pacemaker run means that the scheduled run did not reach TaskFramework and so, the task
   * was never triggered. TaskFramework logs these as a FAILED log with one of the returning array of sub-statuses.
   * To get a description (in English) of the sub-status, call `TaskLogsApi.getSubstatusDescription()`
   * @returns the sub-statuses corresponding to an early failed run.
   */
  public static isEarlyFailureSubstatus(subStatusCode: Substatus): boolean {
    return subStatusCode.valueOf() >= 500 && subStatusCode.valueOf() < 600;
  }

  public static async getLogsWithScheduleInfo(context: IRequestContext, spaces?: string[], status?: Status, limit?: number): Promise<ITaskLogsWithScheduleInfo[]> {
    try {
      const taskLogger = await TaskLogger.fromContext(context);
      const logs = await taskLogger.getLogsWithScheduleInfo(spaces, status, limit);
      const tzEnabled = await isTimezoneEnabled(context);
      if (!tzEnabled) {
        logs.forEach(log => log.TZ_NAME = null);
      }
      let logsWithNextRun: ITaskLogsWithScheduleInfo[] = [];
      if (logs && logs.length) {
        logsWithNextRun = await mapSchedulesAsync(logs);
      }
      return logsWithNextRun;
    } catch (err) {
      throw err;
    }
  }

  /**
   * Update the status of locked tasks by:
   * 1. get all lock entries for the given task
   * 2. for each entry, create a dummy task of the same quadruplet
   * 3. call overwriteLock on that task to query for the status of the locked task
   * 4. ends the locked task, if finished
   * @returns an array with the logIds of tasks which remained locked (could not be resolved)
   */
  private async updateLockedTasks(identifier: Partial<TaskIdentifier>): Promise<LockEntry[]> {
    const context = this.context;
    logVerbose("[TaskLogsApi][updateLockedTasks]", this.logOptions);
    if (identifier.spaceId && (identifier.objectId || identifier.applicationId)) {
      const lockEntries: LockEntry[] = [];
      const taskLock = await TaskLock.fromContext(context);
      try {
        const lockedTasks = await taskLock.getLockedTasks(identifier);
        logInfo(`[TaskLogsApi][updateLockedTasks] found ${lockedTasks.length} locks for identifier ${JSON.stringify(identifier)}`, this.logOptions);
        for (const lock of lockedTasks) {
          try {
            const parameters: Parameters = lock.parameters || {};
            const tfParams: TFParams = {
              ...parameters.tf,
              isDirect: parameters.tf?.isDirect ?? false,
              isOnlyUpdate: true
            };
            parameters.tf = tfParams;
            const taskObject: ITask = { ...lock, parameters } as ITask;
            const task = await TaskFactory.createExecutable(context, taskObject);
            const { takeover, ...taskResult } = await task.overwriteLock(lock);
            logInfo(`[TaskLogsApi][updateLockedTasks] overwriteLock response takeover = ${takeover}$, ${JSON.stringify(taskResult)}`, this.logOptions);
            if (takeover) {
              const standardLogger = await TaskLogger.fromLogId(context, lock.logId);
              const logger = tfParams.isDirect ? new NotifyingTaskLogger(context, standardLogger) : standardLogger;
              taskResult.status = taskResult.status ?? Status.FAILED;
              logWarning(`[TaskLogsApi][updateLockedTasks] will end task ${lock.logId}, with properties = ${JSON.stringify(taskResult)}`, this.logOptions);
              const et = new EndTask(context, logger, lock as ITask);
              et.startTime = lock.creationTime;
              await et.finish(taskResult as ExecuteResult);
            } else {
              logWarning(`[TaskLogsApi][updateLockedTasks] takeover = false, leaving task ${lock.logId} locked`, this.logOptions);
              const { lockKey, applicationId, objectId, spaceId, logId, creationTime } = lock;
              lockEntries.push({ lockKey, applicationId, objectId, spaceId, logId, creationTime } as LockEntry);
            }
          } catch (err) {
            logError([`[TaskLogsApi][updateLockedTasks] failed to update lock for task ${lock.logId}`, err], this.logOptions);
            const { lockKey, applicationId, objectId, spaceId, logId, creationTime } = lock;
            lockEntries.push({ lockKey, applicationId, objectId, spaceId, logId, creationTime } as LockEntry);
          }
        }
      } catch (error) {
        logError(["[TaskLogsApi][updateLockedTasks] failed to retrieve locks for update", error], this.logOptions);
      }
      return lockEntries;
    }
    logWarning([`[TaskLogsApi][updateLockedTasks] LockedTasks not updated: spaceId and either objectId or applicationId must be defined to identify tasks to be updated (identifier = ${JSON.stringify(identifier)}).`], this.logOptions);
    return [];
  }

  private async getChainChildrenDetails(logId: number, log: ITaskLog): Promise<TaskChainDetails> {
    const dbClient = await getNoCommitClient(this.context);
    const chainRun: TaskChainRun = TaskChainRun.fromDatabaseClient(this.context, dbClient);
    const chainRunEntry = await chainRun.getRunModel(logId);
    if (chainRunEntry) {
      const chainRunNode = await TaskChainRunNode.fromDatabaseClient(this.context, dbClient);
      const nodesLogHeaders = await chainRunNode.getChainNodesLogHeaders(logId);
      const nodesLogs = mapLogHeaders<TaskChainRunNodesWithLogs, ITaskLogHeader & { chainTaskLogId: number; nodeId: number }>(nodesLogHeaders);

      const chainRunNodesHistory = await TaskChainRunNodesHistory.fromDatabaseClient(this.context, dbClient);
      const nodesHistoryLogHeaders = await chainRunNodesHistory.getChainNodesHistory(logId);
      const nodesHistory = mapLogHeaders<TaskChainRunNodesHistoryWithLogs, ITaskLogHeader & { chainTaskLogId: number; nodeId: number, runNo: number }>(nodesHistoryLogHeaders);

      const linearChildren: TaskChainChild[] = [];

      function getNodeLogs(node: RuntimeStep) {
        const [nodeLogs] = nodesLogs.filter(n => n.nodeId === node.id);
        let status: Status | "NOT_TRIGGERED" | undefined = nodeLogs?.status;
        if (!nodeLogs?.logId) {
          status = "NOT_TRIGGERED";
        } else if (!nodeLogs?.status) {
          status = undefined;
        }
        return {
          ...nodeLogs,
          logId: nodeLogs?.logId || undefined,
          status,
          nodeId: node.id
        }
      }

      function getNodeHistory(node: RuntimeStep) {
        return nodesHistory.filter(n => n.nodeId === node.id);
      }

      const narrowToTaskIdentifier = (taskIdentifier: TaskIdentifier | TaskWithParameters): TaskIdentifier => (
        {
          objectId: taskIdentifier.objectId,
          spaceId: taskIdentifier.spaceId,
          activity: taskIdentifier.activity,
          applicationId: taskIdentifier.applicationId
        }
      );
      const plan = chainRunEntry.plan;
      let orderedNodes: RuntimeStep[] = [];
      // Order of the graph. To make this work for all cases of Task Chains and as it is only used for ordering the nodes,
      // we can use isRuntimeChain = true.
      const validator = new ChainValidator(this.context, plan, true);
      orderedNodes = await validator.orderChain();

      for (const currentNode of orderedNodes) {
        // We don't want to dispay the and / or nodes.
        if (currentNode.type === NodeType.TASK && currentNode.taskIdentifier) {
          const currentChild: TaskChainChild = {
            ...getNodeLogs(currentNode),
            retryHistory: getNodeHistory(currentNode),
            ...narrowToTaskIdentifier(currentNode.taskIdentifier)
          };
          linearChildren.push(currentChild);
        }
      }
      linearChildren.sort((a, b) => {
        if (a.startTime && b.startTime) {
          return a.startTime.getTime() - b.startTime.getTime()
        } else {
          if (!a.startTime && !b.startTime) {
            return 0;
          } else {
            return (a.startTime) ? -1 : 1;
          }
        }
      });
      return { children: linearChildren, ...log };
    }
    return { children: [], ...log };
  }
  /**
   * Validate whether the given filter parameter contains at least one of the required properties:
   * ["objectId", "applicationId", "activity", "status"]
   * @param filter the IFilterParameters to be validated
   * @param exclude any of ["objectId", "applicationId", "activity", "status"] to be excluded from the validation.
   */
  private static validateFilterParameters(filter: IFilterParameters, exclude?: string): void {
    Object.keys(filter).forEach((key: keyof Partial<IFilterParameters>) => filter[key] === undefined && delete filter[key]);
    const keys = Object.keys(filter);
    let oneOfRequired = ["objectId", "applicationId", "activity", "status", "logId"];
    if (exclude && oneOfRequired.includes(exclude)) {
      oneOfRequired = oneOfRequired.filter(one => one !== exclude);
    }
    const intersection = keys.filter(key => oneOfRequired.includes(key));
    if (keys.length === 0 || intersection.length === 0) {
      throw new Error(`[TaskLogsApi][validateFilterParameters] Either objectId, applicationId, or activity must be defined to filter the logs`);
    }
  }
}
