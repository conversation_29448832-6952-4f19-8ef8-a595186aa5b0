/** Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved. */

/**
 * One key/value pair with name and value of the metric.
 * Currently only strings are supported as metrics.
 */
export interface TaskMetric {
  metricId: number,
  name: MetricName;
  value: string;
  labels?: TaskMetricLabel[];
}

export interface TaskMetricLabel {
  name: LabelName;
  value: string;
}

export enum LabelName {
  OPTIMIZATION = "OPTIMIZATION",
  OVERALL = "OVERALL",
  PARTITION = "PARTITION",
  PARTITION_LOW_VALUE = "PARTITION_LOW_VALUE",
  PARTITION_HIGH_VALUE = "PARTITION_HIGH_VALUE",
  OPERATOR = "OPERATOR",
  OPERATOR_KIND = "OPERATOR_KIND",
  LOCKED = "LOCKED",
  STEP = "STEP",
  REMOTE_RECORD_COUNT = "REMOTE_RECORD_COUNT",
  REMOTE_EXECUTION_TIME = "REMOTE_EXECUTION_TIME",
  SOURCE = "SOURCE",
  TARGET = "TARGET",
  LOAD = "LOAD",
  PROVIDER = "PROVIDER",
  HIGH_WATERMARK = "HIGH_WATERMARK",
  GRAPHICAL = "GRAPHICAL",
  SQL = "SQL",
  SQL_SCRIPT = "SQL_SCRIPT",
  UNEXPECTED = "UNEXPECTED",
  TABLE_FUNCTION = "TABLE_FUNCTION",
  STRUCTURED_FILTER = "STRUCTURED_FILTER",
  PARAMETER_VALUE = "PARAMETER_VALUE",
  MAX_VERSION = "MAX_VERSION",
  MIN_VERSION = "MIN_VERSION",
  BATCH = "BATCH",
  BATCH_LOW_VALUE = "BATCH_LOW_VALUE",
  BATCH_HIGH_VALUE = "BATCH_HIGH_VALUE",
}

/**
 * A set of metrics for a given *taskLogId*.
 */
export interface TaskMetrics {
  taskLogId: number;
  metrics: TaskMetric[];
}

export enum MetricName {

  NUMBER_OF_RECORDS = "NUMBER_OF_RECORDS",
  POLLING_COUNT = "POLLING_COUNT",
  RUNTIME_MS = "RUNTIME_MS",
  NUMBER_OF_PARTITIONS = "NUMBER_OF_PARTITIONS",
  PARTITIONING_COLUMN = "PARTITIONING_COLUMN",
  MEMORY_CONSUMPTION_MIB = "MEMORY_CONSUMPTION_MIB",
  MEMORY_CONSUMPTION_GIB = "MEMORY_CONSUMPTION_GIB",
  JOB_EXECUTION = "JOB_EXECUTION",
  EXECUTION_MODE = "EXECUTION_MODE",
  LOAD_TYPE = "LOAD_TYPE",
  TRUNCATE = "TRUNCATE",
  DELTA = "DELTA",
  REMOTE_ACCESS = "REMOTE_ACCESS",
  LOAD_UNIT_TARGET = "LOAD_UNIT_TARGET",
  VIEW_TRANSFORM_STACK_SIZE = "VIEW_TRANSFORM_STACK_SIZE",
  DAC = "DAC",
  INPUT_PARAMETER = "INPUT_PARAMETER",
  ECN_ID = "ECN_ID",
  NUMBER_OF_DELETED_RECORDS = "NUMBER_OF_DELETED_RECORDS",
  RUNTIME = "RUNTIME",
  PYTHON_OPERATOR_COUNT = "PYTHON_OPERATOR_COUNT",
  SPARK_RESOURCE_ID = "SPARK_RESOURCE_ID",
  SPARK_APPLICATION_INDEX = "SPARK_APPLICATION_INDEX",
  NUMBER_OF_BATCHES = "NUMBER_OF_BATCHES",
  BATCH_COLUMN = "BATCH_COLUMN",
  BATCH_SIZE = "BATCH_SIZE",
  BATCH_COL_NUM_OF_DISTINCT_VAL_NOT_NULL = "BATCH_COL_NUM_OF_DISTINCT_VAL_NOT_NULL",
}

export interface TaskMetricsPayload {
  taskLogId: number;
  name: MetricName;
  value: string;
  labels?: TaskMetricLabel[];
}
