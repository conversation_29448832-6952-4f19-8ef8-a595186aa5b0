/** Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved. */

import { LogLevel } from "@sap/dwc-logger";
import { sql } from "@sap/prom-hana-client";
import { DbClient } from "../../../../lib/DbClient";
import { IRequestContext } from "../../../../repository/security/common/common";
import { Activity, ApplicationId, TaskIdentifier } from "../../../models";
import { taskToString } from "../../../models/TaskIdentifier";
import { TaskFactory } from "../../../orchestrator/services/taskexecution/TaskFactory";
import { getNoCommitClient } from "../../../shared/db/HanaClient";
import { TaskFrameworkConstants } from "../../../shared/db/TaskFrameworkConstants";
import { logError, logInfo, logPerformance, logVerbose, logWarning } from "../../../shared/logger/TaskFrameworkLogger";

/**
 * Helps to delete taskLogs and all related entities connected to the task logs.
 * Deletes entries from TaskLogs, TaskLogMessages
 */
export class TaskLogDelete {

  private readonly schema: string = TaskFrameworkConstants.schema;

  private constructor(
    private readonly context: IRequestContext) { }

  /**
   * Creates a TaskLogDelete given a database client.
   */
  public static async fromContext(context: IRequestContext): Promise<TaskLogDelete> {
    return new TaskLogDelete(context);
  }

  private _client: DbClient | undefined;
  public set client(client: DbClient) {
    this._client = client;
  }

  private _shouldCommit: boolean = true;
  public set shouldCommit(shouldCommit: boolean) {
    this._shouldCommit = shouldCommit;
  }

  /**
   * Deletes all TaskLogs and related entries referencing task logId of the given *spaceId*.
   * @param spaceId the space describing what to delete.
   */
  public async deleteBySpace(spaceId: string): Promise<void> {
    logVerbose(`Deleting TaskLogs and TaskLogMessages of space ${spaceId}`, { context: this.context });
    let dbClient: DbClient | undefined;
    try {
      dbClient = await this.connect();
      try {
        const deleteLogs = sql`DELETE FROM
      ${sql.quoted(this.schema, TaskFrameworkConstants.taskLogsTableName)} AS l
      WHERE
        l.SPACE_ID = ${spaceId};`
        await dbClient.execute(deleteLogs);
        await this.commit();
      } catch (error) {
        logError([`[TaskLogDelete][deleteBySpace] Logs older than ${before} could not be deleted. Rolling back.`, error], { context: this.context });
        await dbClient.rollback();
      }
    } catch (error) {
      logError([`[TaskLogDelete][deleteBySpace] DB error at TaskLogDelete.deleteOlderThan. Logs older than ${before} could not be deleted.`, error], { context: this.context });
    } finally {
      await this.close();
    }
  }

  /**
 * Marks all log header entries corresponding to the TaskIdentifier as Deleted
 * and simultaneously deleting the related locks in the TASK_LOCK table.
 */
  public async markAsDeleted(taskIdentifiers: TaskIdentifier[]): Promise<number> {
    logVerbose(`[TaskLogDelete][markAsDeleted] Marking TaskLogs as deleted for ${taskIdentifiers.length} objects`, { context: this.context });
    let dbClient: DbClient | undefined;
    const toDeleteSet: Set<number> = new Set();
    try {
      dbClient = await this.connect();
      try {
        const jsonTable = JSON.stringify(taskIdentifiers);
        const selectForUpdateStmt =
          sql`SELECT TASK_LOG_ID FROM ${sql.quoted(this.schema, TaskFrameworkConstants.taskLogsTableName)} ts
          INNER JOIN
            JSON_TABLE(${jsonTable}, '$' COLUMNS ( SPACE_ID NVARCHAR(64) PATH '$.spaceId', OBJECT_ID NVARCHAR(256) PATH '$.objectId', APPLICATION_ID NVARCHAR(256) PATH '$.applicationId', ACTIVITY NVARCHAR(256) PATH '$.activity')) jt
              ON ts.SPACE_ID = jt.SPACE_ID AND ((jt.APPLICATION_ID = ${ApplicationId.LOCAL_TABLE_VARIANT} AND ts.OBJECT_ID LIKE_REGEXPR ('^' || jt.OBJECT_ID || '(:\\w+)?\\z')) OR (jt.APPLICATION_ID != ${ApplicationId.LOCAL_TABLE_VARIANT} AND ts.OBJECT_ID = jt.OBJECT_ID))
              AND ts.APPLICATION_ID = jt.APPLICATION_ID AND ts.ACTIVITY = jt.ACTIVITY
              WHERE IS_DELETED = false
              FOR UPDATE OF IS_DELETED IGNORE LOCKED;`;
        const sqlResult = await dbClient.execute(selectForUpdateStmt);
        const taskLogIds: number[] = sqlResult.map((row: { TASK_LOG_ID: number }) => row.TASK_LOG_ID);
        logVerbose(`[TaskLogDelete][markAsDeleted] Found ${taskLogIds.length} task logs to mark as deleted`, { context: this.context });

        // Get API RUN/TEST_RUN/CANCEL task logs that were run as part of a deleted chain
        const apiTaskLogIds = await this.getApiTaskInDeletedChains(taskIdentifiers);
        taskLogIds.push(...apiTaskLogIds);

        let numberOfUpdatedLogs = 0;
        if (taskLogIds.length > 0) {
          logVerbose(`[TaskLogDelete][markAsDeleted] Marking ${taskLogIds.length} task logs as deleted`, { context: this.context });
          taskLogIds.forEach(id => toDeleteSet.add(id));
          const toDelete = Array.from(toDeleteSet);
          const taskLogUpdateStmt =
            sql`UPDATE ${sql.quoted(this.schema, TaskFrameworkConstants.taskLogsTableName)}
              SET
                IS_DELETED = true
              WHERE
                TASK_LOG_ID IN ${toDelete}`;
          const taskLockDeleteStmt =
            sql`DELETE FROM ${sql.quoted(this.schema, TaskFrameworkConstants.taskLocksTableName)}
              WHERE
                TASK_LOG_ID IN ${toDelete}`;
          numberOfUpdatedLogs = await dbClient.execute(taskLogUpdateStmt);
          logVerbose(`[TaskLogDelete][markAsDeleted] Deleting Locks for ${taskIdentifiers.length} objects`, { context: this.context });
          await dbClient.execute(taskLockDeleteStmt);
        }
        await this.commit();
        return numberOfUpdatedLogs;
      } catch (err) {
        logError([`[TaskLogDelete][markAsDeleted] Mark as deleted for tasklogs in ${TaskFrameworkConstants.taskLogsTableName} and Deleting locks in ${TaskFrameworkConstants.taskLocksTableName} failed. Rolling back.`, err], { context: this.context });
        await dbClient.rollback();
        throw err;
      }
    } catch (error) {
      logError([`[TaskLogDelete][markAsDeleted] DB error at TaskLogDelete.markAsDeleted. Tasks ${taskIdentifiers.map(taskToString).join()} could not be marked as deleted.`, error], { context: this.context });
      return 0;
    }
  }

  /**
   * Get logIds of API/CANCEL tasks which cancelled the tasks with the given logIds
   * @param apiLogIds logIds of API tasks which were cancelled and are going to be deleted along with their chains
   * @returns logIds of API/CANCEL tasks which cancelled the tasks with the given logIds
   */
  private async getApiCancelLogIds(apiLogIds: number[]) {
    if (!Array.isArray(apiLogIds) || apiLogIds.length === 0) {
      return [];
    }
    logVerbose(`[TaskLogDelete][getApiCancelLogIds] Getting API Cancel Log IDs for log IDs: ${apiLogIds.join()}`, { context: this.context });
    let dbClient: DbClient | undefined;
    try {
      dbClient = await this.connect();
      const apiCancelQuery = sql`SELECT TASK_LOG_ID FROM ${sql.quoted(this.schema, TaskFrameworkConstants.taskLogsTableName)}
        WHERE EXTERNAL_INSTANCE_ID IN ${apiLogIds}
        AND APPLICATION_ID = ${ApplicationId.API}
        AND ACTIVITY = ${Activity.CANCEL}
        AND IS_DELETED = false
        FOR UPDATE OF IS_DELETED IGNORE LOCKED;`;
      const apiCancelResult = await dbClient.execute<{ TASK_LOG_ID: number }>(apiCancelQuery);
      const apiCancelLogIds = apiCancelResult.map((row: { TASK_LOG_ID: number }) => row.TASK_LOG_ID);
      logVerbose(`[TaskLogDelete][getApiCancelLogIds] Found ${apiCancelLogIds.length} API Cancel Log IDs`, { context: this.context });
      return apiCancelLogIds;
    } catch (error) {
      logError([`[TaskLogDelete][getApiCancelLogIds] DB error. Could not get API Cancel Log IDs for log IDs ${apiLogIds.join()}.`, error], { context: this.context });
      return [];
    }
  }

  /**
   * Find API TEST_RUN and RUN logs that were run as part of a deleted chain
   * @param taskIdentifiers All TaskIdentifiers that were deleted, including chains
   * @returns task logIds of API TEST_RUN and RUN tasks that were run as part of a deleted chain
   */
  private async getApiRunsByChainName(taskIdentifiers: TaskIdentifier[]) {
    const chains = taskIdentifiers.filter(ti => ti.applicationId === ApplicationId.TASK_CHAINS).map(ti => ({
      spaceId: ti.spaceId,
      chainName: ti.objectId,
    }));
    if (chains.length > 0) {

      let dbClient: DbClient | undefined;
      try {
        const apiRuns = TaskLogDelete.childrenTasksToJsonTable(chains, ApplicationId.API, [Activity.TEST_RUN, Activity.RUN]);
        if (!apiRuns) {
          logVerbose(`[TaskLogDelete][getApiRunsByChainName] No API Test Runs found for deleted chains`, { context: this.context });
          return [];
        }
        const selecApiRuns =
          sql`SELECT TASK_LOG_ID FROM ${sql.quoted(this.schema, TaskFrameworkConstants.taskLogsTableName)} ts
          INNER JOIN
          JSON_TABLE(${apiRuns}, '$' COLUMNS ( SPACE_ID NVARCHAR(64) PATH '$.spaceId', CHAIN_NAME NVARCHAR(256) PATH '$.chainName', APPLICATION_ID NVARCHAR(256) PATH '$.applicationId', ACTIVITY NVARCHAR(256) PATH '$.activity')) jt
              ON ts.SPACE_ID = jt.SPACE_ID AND ts.EXTERNAL_INSTANCE_ID = jt.CHAIN_NAME AND ts.APPLICATION_ID = jt.APPLICATION_ID AND ts.ACTIVITY = jt.ACTIVITY
              WHERE IS_DELETED = false
              FOR UPDATE OF IS_DELETED IGNORE LOCKED;`;
        dbClient = await this.connect();
        const apiRunsResult = await dbClient.execute<{ TASK_LOG_ID: number }>(selecApiRuns);
        const apiLogIds = apiRunsResult.map((row: { TASK_LOG_ID: number }) => row.TASK_LOG_ID);
        logVerbose(`[TaskLogDelete][getApiRunsByChainName] Found ${apiLogIds.length} API Run task logs to mark as deleted`, { context: this.context });
        return apiLogIds;
      } catch (err) {
        logWarning(`[TaskLogDelete][getApiRunsByChainName] Failed to find API Run task logs for deleted chains. Error: ${err}`, { context: this.context });
      }
    }
    return [];
  }

  /**
   * Creates a JSON table of children tasks based on the provided chains, child application ID, and child activities.
   * This method ensures that each chain is unique by using a Map to filter duplicates.
   * @param chains - An array of objects containing spaceId and chainName.
   * @param childApplicationId - The application ID for the child tasks. Call it once for each different child application ID.
   * @param childActivities - An array of activities that the child tasks will have for the same application ID.
   * @returns A JSON string representing the children tasks, or null if no children tasks are created.
   */
  private static childrenTasksToJsonTable(chains: Array<{ spaceId: string, chainName: string }>, childApplicationId: ApplicationId, childActivities: Activity[]) {
    const tempMap = new Map<string, { spaceId: string, chainName: string }>();
    chains.forEach(chain => {
      const key = `${chain.spaceId}/${chain.chainName}`;
      if (!tempMap.has(key)) {
        tempMap.set(key, chain);
      }
    });
    const childrenTasks: Array<{ spaceId: string, chainName: string, applicationId: ApplicationId, activity: Activity }> = [];
    tempMap.forEach(chain => {
      childActivities.forEach(activity => {
        childrenTasks.push({
          spaceId: chain.spaceId,
          chainName: chain.chainName,
          applicationId: childApplicationId,
          activity,
        });
      });
    });
    return childrenTasks.length > 0 ? JSON.stringify(childrenTasks) : null;
  }

  /**
   * Get logIds of API tasks (RUN, TEST_RUN, CANCEL) which were run as part of a deleted chain or cancelled such a task
   * @param taskIdentifiers All TaskIdentifiers that were deleted, including chains
   * @returns logIds of API tasks (RUN, TEST_RUN, CANCEL) that should be marked as deleted
   */
  private async getApiTaskInDeletedChains(taskIdentifiers: TaskIdentifier[]) {
    const apiRunsLogIds = await this.getApiRunsByChainName(taskIdentifiers);
    if (apiRunsLogIds.length > 0) {
      const apiCancelLogIds = await this.getApiCancelLogIds(apiRunsLogIds);
      return [...apiRunsLogIds, ...apiCancelLogIds];
    }
    return [];
  }

  /**
   * Deletes entries for which the "END_TIME" of the TaskLogs table is before the
   * given *before* Date parameter.
   * @param before the date before messages are deleted.
   */
  public async deleteOlderThan(before: Date): Promise<void> {
    logVerbose(`Deleting TaskLogs and TaskLogMessages that ended before ${before.toLocaleString()}`, { context: this.context });
    const unixTimestamp = Math.floor(before.getTime() / 1000).toString();
    let dbClient: DbClient | undefined;
    const start = new Date();
    try {
      const keepingLogs = TaskFactory.getApplicationsWithLastRelevantRun().map(e => ({ applicationId: e.applicationId, activity: e.activity }));
      dbClient = await this.connect();
      logWarning(`[TaskLogDelete][deleteOlderThan] calling procedure with parameters: [${unixTimestamp}]`, { context: this.context });
      try {
        await dbClient.executeProcedure(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLogsDeleteProcedureName, [unixTimestamp, JSON.stringify({ keepingLogs })]);
        await this.commit();
        logPerformance(LogLevel.Warning, start, `[TaskLogDelete][deleteOlderThan] delete procedure complete`, { context: this.context });
      } catch (error) {
        logError([`[TaskLogDelete][deleteOlderThan] Logs older than ${before} could not be deleted. Rolling back.`, error], { context: this.context });
        await dbClient.rollback();
      }
    } catch (error) {
      logError([`[TaskLogDelete][deleteOlderThan] DB error at TaskLogDelete.deleteOlderThan. Logs older than ${before} could not be deleted.`, error], { context: this.context });
    }
  }

  /**
   * Delete task log for a given logId, in the given spaceId. Both input parameters must be given.
   *
   */
  public async deleteByLogIds(spaceId: string, logIds: number[]) {
    if (!spaceId || !Array.isArray(logIds) || logIds.length === 0) {
      const msg = "deleteLogsByLogIds needs spaceId and and array of logIds";
      const error = new Error(msg);
      logError(["[TaskLogDelete][deleteLogsByLogIds]", error], { context: this.context });
      throw error;
    }
    let deleted = 0;
    logVerbose(`[TaskLogDelete][deleteLogsByLogIds] Deleting TaskLogs and TaskLogMessages for task logIds ${logIds.join()} in space ${spaceId}`, { context: this.context });
    let dbClient: DbClient | undefined;
    try {
      dbClient = await this.connect();
      try {
        const deleteHeader =
          sql`DELETE FROM ${sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLogsTableName)}
              WHERE SPACE_ID = ${spaceId} AND TASK_LOG_ID IN ${logIds}`;
        deleted = await dbClient.execute(deleteHeader);
        await this.commit();
        logInfo(`[TaskLogDelete][deleteLogsByLogIds] Deleted ${deleted} out of ${logIds.length} logs`, { context: this.context });
      } catch (error) {
        logError([`[TaskLogDelete][deleteLogsByLogIds] Tasks ${logIds.join()} could not be deleted. Rolling back.`, error], { context: this.context });
        await dbClient.rollback();
      }
    } catch (error) {
      logError([`[TaskLogDelete][deleteLogsByLogIds] DB error at TaskLogDelete.deleteLogsByLogId. Tasks ${logIds.join()} could not be deleted.`, error], { context: this.context });
    } finally {
      await this.close();
    }
    return deleted;
  }

  /**
   * Gets a new connection from the pool
   */
  private async connect() {
    if (!this._client) {
      logVerbose("[TaskLogDelete][connect] Getting TaskFramework connection", { context: this.context });
      this.client = await getNoCommitClient(this.context);
    }
    return this._client!;
  }

  private async commit() {
    if (this._shouldCommit) {
      logVerbose("[TaskLogDelete][commit] Committing TaskFramework connection", { context: this.context });
      await this._client?.commit();
    }
  }

  /**
   * Returns the connection to the pool, by closing it.
   */
  private async close() {
    logVerbose("[TaskLogDelete][close] Closing TaskFramework connection", { context: this.context });
    await this._client?.close();
  }
}
