/** Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved. */
/* eslint-disable @sap/dwc-lints/unbound-promises-handled */

import { <PERSON>a<PERSON>rror } from "@sap/prom-hana-client";
import { FeatureFlagProvider } from "../../featureflags/FeatureFlagProvider";
import { DbClient } from "../../lib/DbClient";
import { ILogOptions, LogLevel } from "../../logger";
import { AuthType, IRequestContext, Activity as PermissionActivity } from "../../repository/security/common/common";
import { getCfInstance } from "../../reuseComponents/utility/dynatraceUtil";
import { ITaskLogger, Status, Substatus } from "../logger/models";
import { ITranslatableParams } from "../logger/models/ITranslatableParams";
import { getSubstatusName } from "../logger/models/Substatus";
import { IRunningTaskState, RunState } from "../logger/services/heatthcheck/RunState";
import { TaskHealthcheck } from "../logger/services/heatthcheck/TaskHealthcheck";
import { TaskLogger } from "../logger/services/logger/TaskLogger";
import { Activity, ApplicationId, IExecutable, Parameters } from "../models";
import { TaskExecuteResponse } from "../models/TaskExecuteResponse";
import TaskFrameworkDynatrace from "../monitoring/TaskFrameworkDynatrace";
import { ITaskChainShapeDimension, MetricPath, toChainCounts } from "../monitoring/TaskFrameworkDynatraceTypes";
import { LockIdentifier, LockKey, LockedTask } from "../orchestrator/services/tasklock";
import { OverwriteResponse } from "../orchestrator/services/tasklock/OverwriteResponse";
import { getDbClientTaskSynchronizer, getNoCommitClient } from "../shared/db/HanaClient";
import { TaskFrameworkVirtualSpaces } from "../shared/db/TaskFrameworkConstants";
import { logError, logInfo, logPerformance, logVerbose, logWarning } from "../shared/logger/TaskFrameworkLogger";
import { getOrg } from "../shared/util/environmentUtils";
import { ChainLogger } from "./ChainLogger";
import { ChainParametersAccessor } from "./ChainParametersAccessor";
import { ChainParametersCollector } from "./ChainParametersCollector";
import { ChainRetry } from "./ChainRetry";
import { ChainRetryNew } from "./ChainRetryNew";
import { ChainValidator } from "./ChainValidator";
import { ChainParametersValidator } from "./ChainParametersValidator";
import { NodesInitializer } from "./NodesInitializer";
import { TaskChainRun, TaskChainRunWithUserEntry } from "./TaskChainRun";
import { TaskChainRunNode } from "./TaskChainRunNode";
import { DeployService } from "./designtime/DeployService";
import { DeployedMetadataObject, DesignModel, ITaskChainParameterId, NodeType, RuntimePlan, TaskChainParameterType } from "./models";
import { ChainCsn } from "./models/ChainCsn";
import { isFFEnabled } from "../TFFeatureFlagValidator";
import { ChainParametersError, ChainParametersErrorCodes } from "../errors/ChainParametersError"
import { ChainParameterValues, TaskChainParams } from "../orchestrator/models/Parameters";
export enum StatusCountKey {
  TRIGGERED = "TRIGGERED",
  NOT_TRIGGERED = "NOT_TRIGGERED",
}

export type StatusCounts = Record<Status | StatusCountKey, number>;

/**
 * ChainTask is a task that allows to trigger a "chain of tasks" - aka a set of
 * subtasks, represented in a graph.
 * This task reads out the given chain from the deployer, calculates the steps
 * to do and executes them one by one.
 * The chain finishes as COMPLETED when all tasks ran successfully,
 * In case there's an error on the way or in any of the subtasks,
 * the final chain status is FAILED even if the chain reached its end
 */
export class ChainTask implements IExecutable {
  public static readonly HEARTBEAT_MS = 5 * 60 * 1000;

  private chainLogId: number;
  private dbClient: DbClient;
  private logOptions: ILogOptions;

  private _hasVolatileChain: boolean | undefined = undefined;
  private get hasVolatileChain(): boolean {
    if (this._hasVolatileChain === undefined) { this._hasVolatileChain = !!this.volatileParameter && !!this.volatileParameter.metadata; }
    return this._hasVolatileChain;
  }

  public constructor(
    private readonly context: IRequestContext,
    private readonly spaceId: string,
    private readonly objectId: string,
    private readonly activity: Activity,
    private readonly parameters: Parameters,
  ) {
    this.logOptions = { context: this.context };
  }

  public volatileParameter: { metadata: DeployedMetadataObject } | undefined;

  async execute(logger: ITaskLogger): Promise<TaskExecuteResponse> {
    const timing = new Date();
    const chainLogger = new ChainLogger(this.context, logger);
    this.chainLogId = logger.getLogId();
    try {
      logPerformance(LogLevel.Info, timing, `[ChainTask][execute] Starting ${this.activity} for task ${this.chainLogId}, space ${this.spaceId}, object ${this.objectId}.`, { ...this.logOptions, warningThreshold: 10 });
      logVerbose(`[ChainTask][execute] Parameters passed to run: ${JSON.stringify(this.parameters, null, 2)}`, this.logOptions);
      if (this.activity === Activity.CANCEL) {
        return await this.executeCancel(chainLogger);
      } else {
        return await this.executeRun(chainLogger);
      }
    } catch (err) {
      await chainLogger.unexpectedError(err);
      logWarning(`[ChainTask][execute] Chain finishes with status FAILED`, this.logOptions);
      return { status: Status.FAILED };
    }
  }

  private async executeRun(chainLogger: ChainLogger): Promise<TaskExecuteResponse> {
    try {
      let timing = new Date();
      let metadataObject: DeployedMetadataObject | undefined;
      const isRuntimeChain = this.volatileParameter && this.volatileParameter.metadata ? true : false;
      if (isRuntimeChain) {
        metadataObject = this.volatileParameter?.metadata;
      } else {
        logVerbose(`[ChainTask][executeRun] Getting deployed metadata for repository object: ${this.objectId}, spaceId: ${this.spaceId}`, this.logOptions);
        try {
          const deployService = await DeployService.fromSpaceId(this.context, this.spaceId);
          metadataObject = await deployService.getDeployedChain(this.objectId);
          timing = logPerformance(LogLevel.Info, timing, `[ChainTask][executeRun] Got deployed metadata for task ${this.chainLogId}`, { ...this.logOptions, warningThreshold: 50 });
        } catch (error) {
          logError([`[ChainTask][executeRun] Not able to get deployed chain`, error], this.logOptions);
          if (error instanceof HanaError) {
            if (error.code === 616) {
              await chainLogger.resourceLimitError(error);
              return {
                status: Status.FAILED,
                subStatusCode: Substatus.RESOURCE_LIMIT_ERROR,
              };
            }
          }
          throw error;
        }
      }
      if (!metadataObject || Object.keys(metadataObject.csn.taskchains)[0] !== this.objectId) {
        logError(`[ChainTask][executeRun] Could not find chain ${this.objectId}`, this.logOptions);
        await chainLogger.couldNotFindChain(this.objectId);
        return {
          status: Status.FAILED,
          subStatusCode: Substatus.PREREQ_NOT_MET,
        };
      }
      const chainName: string = metadataObject.name;
      const chainCsn: ChainCsn = ChainCsn.from(metadataObject.csn);
      const designModel = chainCsn.getDesignModel();
      timing = logPerformance(LogLevel.Info, timing, `[ChainTask][executeRun] Prefilling tables for ${this.chainLogId}`, { ...this.logOptions, warningThreshold: 50 });
      await this.connect();
      const inputParametersEnabled = await isFFEnabled(this.context, "DWCO_TASK_FRAMEWORK_CHAIN_PARAMETERS");
      if (inputParametersEnabled) {
        // Validate task chain parameters
        const parametersValidator = ChainParametersValidator.getInstance(this.context, designModel);
        const validParameters = parametersValidator.isValid();
        if (!validParameters) {
          logError(`[ChainTask][executeRun] task chain has no valid parameters. Returning Status.FAILED`, this.logOptions);
          await chainLogger.notValidParameters(this.objectId);
          return {
            status: Status.FAILED,
            subStatusCode: Substatus.PREREQ_NOT_MET,
          };
        }
        // Set values for task chain input parameters
        this.setInputParameterValues(designModel);
      }
      let chainParameters: Map<number, ITaskChainParameterId[]> | undefined;
      try {
        const collector = await ChainParametersCollector.createInstance(this.context, designModel, this.chainLogId, this.parameters.tf?.chainParameterIds);
        if (collector) {
          const parametersPersistence = ChainParametersAccessor.getInstance(this.context, this.dbClient);
          collector.indexer = parametersPersistence.createParameters.bind(parametersPersistence)
          if (inputParametersEnabled) {
            // Collect parameters
            chainParameters = await collector.collectAndIndex();
          } else {
            // For now we collect only configuration parameters and do not touch any input parameters.
            // Currently input parameters are left flat in the TaskIdentifierWithParameters with their values.
            chainParameters = await collector.collectConfiguration();
          }
        }
      } catch (error) {
        if (error instanceof ChainParametersError && error.parameterType === TaskChainParameterType.CONFIG && error.code === ChainParametersErrorCodes.VALUE_SIZE_LIMIT_EXCEEDED) {
          logError(`[ChainTask][executeRun] The configuration size of a task in the chain exceeds the maximum allowed size of 100 KiB.`, this.logOptions);
          await chainLogger.configurationSizeLimitError(this.objectId);
          return {
            status: Status.FAILED,
            subStatusCode: Substatus.PREREQ_NOT_MET,
          }
        }
        throw error;
      }

      // Write a log message with input parameter values in the details. This needs to be done only for an upper chain. For a nested chain it is done in the task executor.
      if (designModel.params && !this.parameters.tf?.chainParameterIds) {
        const inputParameterValues = this.getInputParameterValues(designModel)
        await chainLogger.inputParameterValues(this.chainLogId, inputParameterValues);
      }

      const plan = RuntimePlan.generate(this.context, this.spaceId, designModel, chainParameters, inputParametersEnabled);
      const initializer = await NodesInitializer.fromContext(this.context, this.dbClient, chainCsn, chainName, chainLogger);
      await initializer.prefill(plan, this.chainLogId, this.objectId, this.spaceId);
      await this.dbClient.commit();
      timing = logPerformance(LogLevel.Info, timing, `[ChainTask][executeRun] Initialized nodes for ${this.chainLogId}`, { ...this.logOptions, warningThreshold: 50 });
      await this.close();
      timing = logPerformance(LogLevel.Info, timing, `[ChainTask][executeRun] Validating ${this.chainLogId}`, { ...this.logOptions, warningThreshold: 50 });
      const validator = new ChainValidator(this.context, plan, isRuntimeChain);
      const isDAG = await validator.validate();
      if (isDAG) {
        void this.sendDynatraceMetric(plan);
        logPerformance(LogLevel.Info, timing, `[ChainTask][executeRun] Validated ${this.chainLogId}. Returning Status.RUNNING`, { ...this.logOptions, warningThreshold: 50 });
        return {
          status: Status.RUNNING
        };
      } else {
        logError(`[ChainTask][executeRun] task chain is a not a DAG and did not pass the validation. Returning Status.FAILED`, this.logOptions);
        await chainLogger.chainNotDAG(this.objectId);
        return {
          status: Status.FAILED,
          subStatusCode: Substatus.PREREQ_NOT_MET,
        };
      }
    } catch (error) {
      logError([`[ChainTask][executeRun] Unexpected error:`, error], this.logOptions);
      throw error;
    } finally {
      await this.close();
    }
  }

  private async executeCancel(chainLogger: ChainLogger): Promise<TaskExecuteResponse> {
    try {
      const originalChainId = this.parameters.tf?.cancelId;
      if (!originalChainId) {
        logError(`[ChainTask][executeCancel] No original chain id found in parameters`, this.logOptions);
        return {
          status: Status.FAILED,
          subStatusCode: Substatus.PREREQ_NOT_MET,
        };
      }
      logInfo(`[ChainTask][executeCancel] Canceling chain ${originalChainId} with logId ${this.chainLogId}`, this.logOptions);
      await chainLogger.cancelingChain(originalChainId);
      // Delete jobs of nodes without logId
      await this.connect();
      const taskChainRunNode = await TaskChainRunNode.fromDatabaseClient(this.context, this.dbClient);
      const nodesWithoutLogIds = await taskChainRunNode.getChainNodesWithoutLogId(originalChainId);
      if (nodesWithoutLogIds.length > 0) {
        if (await FeatureFlagProvider.isFeatureActive(this.context, "DWCO_TASK_FRAMEWORK_DEEP_RETRY")) {
          const toDelete = nodesWithoutLogIds.map(node => ({
            nodeId: node, chainLogId: originalChainId
          }));
          await ChainRetryNew.deletePacemakerJob(this.context, toDelete);
        } else {
          await ChainRetry.deletePacemakerJob(this.context, originalChainId, nodesWithoutLogIds);
        }
      }
      // Set the status of the original chain to STOPPING
      const taskToProcess: IRunningTaskState[] = [{ logId: originalChainId, runState: RunState.STOPPING }];
      await TaskHealthcheck.logRunStates(this.context, taskToProcess);
      // Log a message to the original chain that it was cancelled
      const originalChainTaskLogger = await TaskLogger.fromLogId(this.context, originalChainId);
      const originalChainLogger = new ChainLogger(this.context, originalChainTaskLogger);
      await originalChainLogger.canceled(this.chainLogId);
      return {
        status: Status.RUNNING
      };

    } catch (error) {
      logError([`[ChainTask][executeCancel] Unexpected error:`, error], this.logOptions);
      throw error;
    } finally {
      await this.close();
    }
  }

  private async sendDynatraceMetric(plan: RuntimePlan) {
    const nodes = Object.values(plan);
    logInfo(`[ChainTask][execute] Sending dynatrace metrics with information about the task chain structure`, this.logOptions);
    const countOr = nodes.filter(n => n.type === NodeType.OR).length;
    const countAnd = nodes.filter(n => n.type === NodeType.AND).length;
    const countTasks = nodes.filter(n => n.type === NodeType.TASK).length;
    const maxSuccessors = Math.max(...nodes.map(n => n.successors.length));
    logInfo(`[ChainTask][execute] Task chain ${this.chainLogId} has ${countOr} ORs, ${countAnd} ANDs and ${countTasks} ChildTask, ${maxSuccessors} is the largest number of parallel running tasks.`, this.logOptions);
    const shapeDimension: ITaskChainShapeDimension = {
      cfInstance: getCfInstance(),
      org: getOrg(),
      tenantId: this.context.tenantId || "",
      numberOfANDs: toChainCounts(countAnd),
      numberOfORs: toChainCounts(countOr),
      maxParallel: toChainCounts(maxSuccessors),
      activity: this.activity
    };
    await TaskFrameworkDynatrace.instance.createMetric<ITaskChainShapeDimension>(MetricPath.CHAIN_SHAPE, shapeDimension, countTasks, this.context);
    logInfo(`[ChainTask][execute] Successfully sent dynatrace metrics`, this.logOptions);
  }

  /**
   * Gets a new connection from the pool
   */
  private async connect(): Promise<void> {
    logVerbose("[ChainTask][connect] Getting TaskFramework connection", this.logOptions);
    if (!this.dbClient) {
      this.dbClient = await getNoCommitClient(this.context);
    }
  }

  /**
   * Returns the connection to the pool, by closing it.
   */
  private async close(): Promise<void> {
    logVerbose("[ChainTask][close] Closing TaskFramework connection", this.logOptions);
    // await this.dbClient?.close();
  }

  /**
   * Returns true, if the given user is authorized to execute this chain.
   * The user needs DataIntegration update/execute permission for direct/scheduled execution
   *
   *
   * @param taskLogger The logger to write messages to.
   * @param isDesignTime true if this method is being called without an execution,
   * during the creation or update of a schedule
   * @returns a boolean indicating whether the current user is authorized
   */
  public async isAuthorized(taskLogger: ITaskLogger, isDesignTime?: boolean): Promise<boolean> {
    logInfo(`[ChainTask][isAuthorized] called for ${this.activity}`, this.logOptions);
    let result = false;

    switch (this.activity) {
      case Activity.RUN_CHAIN_TECHNICAL:
        result = this.context.userInfo.technical === true;
        break;
      case Activity.RUN_BACKGROUND:
        const isBackground = this.hasVolatileChain && this.spaceId === TaskFrameworkVirtualSpaces.TF;
        if (!isBackground) {
          logError(`[ChainTask][isAuthorized] Background task chain ${this.activity} is not background.`, this.logOptions);
        }
        result = isBackground;
        break;
      case Activity.RUN_CHAIN:
        try {
          const direct = !!this.parameters.tf?.isDirect;
          const permission = direct ? PermissionActivity.update : PermissionActivity.execute;
          result = this.context.userInfo.technical !== true && this.context.hasPrivilegeOnScope(this.spaceId, AuthType.DWC_DATAINTEGRATION, permission);
        } catch (err) {
          logError(`[ChainTask][isAuthorized] Error checking task chain ${this.activity} privilege.`, this.logOptions);
        }
        break;
      case Activity.CANCEL:
        try {
          result = this.context.userInfo.technical !== true && (this.context.hasPrivilegeOnScope(this.spaceId, AuthType.DWC_DATAINTEGRATION, PermissionActivity.update) || this.context.hasPrivilegeOnType(AuthType.SYSTEMINFO, PermissionActivity.update));
        } catch (err) {
          logError(`[ChainTask][isAuthorized] Error checking task chain ${this.activity} privilege.`, this.logOptions);
        }
        break;
      default:
        logError(`[ChainTask][isAuthorized] Not supported activity ${this.activity} for TASK_CHAINS`, this.logOptions);
        break;
    }
    logInfo(`[ChainTask][isAuthorized] Authorization check result: ${result}`, this.logOptions);
    return result;
  }

  /**
  * Returns true, if the given object exists in metadata or repo
  * @param taskLogger The logger to write messages to.
  * @returns a boolean indicating whether the current user is authorized
  */
  public async isValidObject(): Promise<boolean> {
    logInfo(`[ChainTask][isValidObject] called for ${this.activity}`, this.logOptions);
    // Check only deployed chains

    if (!this.hasVolatileChain && this.activity === Activity.RUN_CHAIN) {
      try {
        logInfo(`[ChainTask][isValidObject] Getting deployed metadata for repository object: objectId: ${this.objectId}, spaceId: ${this.spaceId}`, this.logOptions);
        const deployService = await DeployService.fromSpaceId(this.context, this.spaceId);
        const metadataObject = await deployService.getDeployedChain(this.objectId);
        return !!metadataObject && Object.keys(metadataObject.csn.taskchains)[0] === this.objectId;
      } catch (err) {
        logError([`[ChainTask][isValidObject] Error checking task chain validity`, err], this.logOptions);
        return false;
      }
    }
    return (this.hasVolatileChain && this.activity !== Activity.RUN_CHAIN) || this.activity === Activity.CANCEL;
  }

  /**
   * Returns the lock identifier of a task chain.
   * Currently two chains can't run in parallel, so we're returning a lock here.
   * It lock this chain, so that this same chain can't run in parallel.
   * @returns The lock identifier.
   */
  public getLockIdentifier(): LockIdentifier {
    return {
      lockKey: this.activity === Activity.CANCEL ? LockKey.CANCEL : LockKey.EXECUTE,
      applicationId: ApplicationId.TASK_CHAINS,
      objectId: this.objectId,
      spaceId: this.spaceId,
    } as LockIdentifier;
  }

  /**
   * Checks whether the current chain can be overwritten or not.
   * @param lock The lock to check on.
   */
  public async overwriteLock(lock: LockedTask): Promise<OverwriteResponse> {
    let result: OverwriteResponse = {
      takeover: false,
    };
    logInfo(`[ChainTask][overwriteLock] called for ${lock.logId}`, this.logOptions);
    const now = new Date().getTime();
    const creation = lock.creationTime.getTime();
    const minutes = 60 * 1000;
    if ((now - creation) < (2 * minutes)) {
      result = {
        takeover: false,
      };
      logInfo(`[ChainTask][overwriteLock] Lock ${lock.logId} creation too recent (${lock.creationTime.toISOString()}), returning RUNNING.`, this.logOptions);
      return result;
    }
    if (lock.lockKey === LockKey.CANCEL) {
      return result;
    }
    const logger = await TaskLogger.fromLogId(this.context, lock.logId);
    const chainLogger = new ChainLogger(this.context, logger);

    try {
      // Synchronizer client has preventRetry set to true
      const dbClient = await getDbClientTaskSynchronizer(this.context);
      const chainRun = TaskChainRun.fromDatabaseClient(this.context, dbClient);
      const entry: TaskChainRunWithUserEntry[] = await chainRun.get([lock.logId]);
      if (entry.length !== 1) {
        result = {
          takeover: true,
          status: Status.FAILED,
        }
        logError(`[ChainTask][overwriteLock] No TaskChainRun found for lock with logId ${lock.logId}. Returning FAILED.`, this.logOptions);
        return result;
      }

      if (entry[0].futureStatus) {
        result = {
          takeover: true,
          ...(entry[0].futureStatus) && { status: entry[0].futureStatus },
          ...(entry[0].futureSubStatus) && { subStatusCode: entry[0].futureSubStatus },
        }
        logWarning(`[ChainTask][overwriteLock] Future status found for task chain ${lock.logId}. Returning ${JSON.stringify(result)}`, this.logOptions);
        return result;
      } else {
        result = {
          takeover: false
        }
        logWarning(`[ChainTask][overwriteLock] No future status found for task chain ${lock.logId}. Returning RUNNING.`, this.logOptions);
        return result;
      }
    } catch (err) {
      await chainLogger.couldNotDetermine(lock.logId);
      result = {
        takeover: false,
      } as OverwriteResponse;
      logError([`[ChainTask][overwriteLock] Unexpected error checking for chain ${lock.logId}. Returning RUNNING.`, err], this.logOptions);
      return result;
    }
  }

  public getTranslatableParams(executionType: string, status: Status, subStatusCode?: Substatus, logId?: number): ITranslatableParams {
    if (status !== Status.FAILED && status !== Status.COMPLETED) {
      throw new Error(`Invalid status: ${status}. Expected ${Status.FAILED} or ${Status.COMPLETED}.`);
    }
    const formattedStatus = `${status.charAt(0).toUpperCase()}${status.slice(1).toLowerCase()}`;

    const titleKey = `taskChainExecution${formattedStatus}`;
    let bodyKey = `taskChainExecution${formattedStatus}WithoutSubstatus`;

    const titleParams = [executionType, ApplicationId.TASK_CHAINS, this.activity, this.objectId];
    const bodyParams = [ApplicationId.TASK_CHAINS, this.activity, this.objectId, this.spaceId];

    if (status === Status.FAILED && subStatusCode) {
      const substatusString = getSubstatusName(subStatusCode);
      if (substatusString) {
        bodyParams.push(substatusString);
        bodyKey = `taskChainExecution${formattedStatus}WithSubstatus`;
      }
    }

    return { titleKey, bodyKey, titleParams, bodyParams, link: this.buildLink(logId) };
  }

  private buildLink(logId?: number): string | undefined {
    const link = JSON.stringify({
      target: {
        semanticObject: "dataintegration",
      },
      params: {
        routeTo: "taskChainMonitorLogDetails",
        spaceId: this.spaceId,
        taskLogId: logId?.toString(),
        objectId: this.objectId,
        applicationId: ApplicationId.TASK_CHAINS,
        activity: this.activity,
      },
    });

    return link;
  }

  private getInputParameterValues(designModel: DesignModel): ChainParameterValues {
    const inputParameterValues: ChainParameterValues = {};
    const parameters: TaskChainParams = designModel.params || {};
    Object.entries(parameters).forEach(([name, value]) => {
      inputParameterValues[name] = value.val;
    })
    return inputParameterValues;
  }

  /**
   * Adjust chain design model by setting the default values of input parameters.
   *  @param designModel Design model of the chain
  */
  private setInputParameterValues(designModel: DesignModel): void {
    const inputParameters = this.parameters.inputParameters;
    if (!inputParameters) {
      return;
    }
    const parameters: TaskChainParams = designModel.params || {};
    Object.entries(parameters).forEach(([name, value]) => {
      value.val = inputParameters[name];
    })
  }
}
