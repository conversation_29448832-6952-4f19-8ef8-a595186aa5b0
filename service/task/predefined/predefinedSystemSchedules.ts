/** Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved. */
// eslint-disable-next-line @sap/dwc-lints/service-dependency-analysis
import {defineEnablementOfMeteringScheduleForCPEA, defineEnablementOfMeteringScheduleForSubscription, defineEnablementOfMeteringScheduleForTestCollect } from "../../metering/helpers";
import { PacemakerTask } from "../admin/PacemakerTask";
import { Activity, ApplicationId } from "../models";
import { ISystemTaskDefinition, enabledForTenant } from "./ISystemTaskDefinition";

/**
 * Returns and creates all Predefined System Schedules.
 */
const defaultScheduleCleanUpDays = 180;

const systemTaskRegistry: ISystemTaskDefinition[] = [
  {
    applicationId: ApplicationId.HANA_CONNECTIVITY_PROXY,
    activity: Activity.DISABLE,
    cron: "X 23 * * 6",
    version: 1.0,
    description: "Disables the connectivity proxy if not used"
  },
  {
    applicationId: ApplicationId.BACKUP,
    activity: Activity.BACKUP,
    cron: "X 1 * * *",
    version: 1.0,
    description: "Daily backup task.",
    enabledForTenant: enabledForTenant(["DWCO_REPOSITORY_BACKUP"]),
  },
  {
    applicationId: ApplicationId.PASSWORD_ROTATION,
    activity: Activity.CUSTOMER_HANA_ROTATE,
    cron: "X 2 1-7 * *",
    version: 1.0,
    description: "Changes the passwords for various technical users.",
  },
  {
    applicationId: ApplicationId.HAUM_INTEGRATION,
    activity: Activity.TRANSFER,
    cron: "X X/12 * * *",
    version: 1.0,
    description: "Transfers the data of measurement objects for HAUM integration.",
    enabledForTenant: enabledForTenant(["DWCO_INFRA_SCHEDULED_ODP_DATA_TRANSFER"]),
  },
  {
    applicationId: ApplicationId.TASK_FRAMEWORK,
    activity: Activity.TASKLOG_CLEANUP,
    cron: "X 0 1 * *",
    version: 1.0,
    payload: {
      "olderThan": defaultScheduleCleanUpDays * 24 * 60 * 60,
    },
    description: "Cleans up task logs.",
  },
  {
    applicationId: ApplicationId.TASK_FRAMEWORK,
    activity: Activity.DAILY_METRICS,
    cron: "X 20 * * *",
    version: 1.0,
    description: "Send TaskFramework dynatrace statistics",
  },
  {
    applicationId: ApplicationId.REPLICATION_FLOWS,
    activity: Activity.DAILY_METRICS,
    cron: "X 20 * * *",
    version: 1.0,
    description: "Send ReplicationFlow dynatrace statistics",
    enabledForTenant: enabledForTenant(["INFRA_DWC_TWO_TENANT_MODE"])
  },
  {
    applicationId: ApplicationId.REPLICATION_FLOWS,
    activity: Activity.WEEKLY_METRICS,
    cron: "X 20 * * 3",
    version: 1.0,
    description: "Send defected ReplicationFlow list to dynatrace",
    enabledForTenant: enabledForTenant(["INFRA_DWC_TWO_TENANT_MODE"])
  },
  {
    applicationId: ApplicationId.REMOTE_TABLES,
    activity: Activity.DAILY_METRICS,
    cron: "X X * * SAT",
    version: 1.0,
    description: "Send Remote Tables dynatrace statistics",
  },
  {
    applicationId: ApplicationId.PACEMAKER,
    activity: Activity.MONITOR,
    cron: "X * * * *",
    version: 1.2,
    description: "Dummy task to monitor Pacemaker Service migration",
    enabledForTenant: PacemakerTask.enabledForTenant,
  },
  {
    applicationId: ApplicationId.METERING_SERVICE,
    activity: Activity.METERING_SERVICE,
    cron: "X * * * *",
    version: 1.0,
    description: "Reports Capacity Units consumption to the Metering Service for CPEA.",
    enabledForTenant: defineEnablementOfMeteringScheduleForCPEA,
  },
  {
    applicationId: ApplicationId.METERING_SERVICE_SUBSCRIPTION,
    activity: Activity.METERING_SERVICE_SUBSCRIPTION,
    cron: "X X * * *",
    version: 1.0,
    description: "Reports Capacity Units consumption to the Metering Service for Subscription.",
    enabledForTenant: defineEnablementOfMeteringScheduleForSubscription,
  },
  {
    applicationId: ApplicationId.METERING_SERVICE,
    activity: Activity.REPORT_METRICS_FROM_METERING_TABLE,
    cron: "X X/6 * * *",
    version: 1.0,
    description: "Reports metrics from metering table",
  },
  {
    applicationId: ApplicationId.METERING_SERVICE,
    activity: Activity.COLLECT_METRICS,
    cron: "X * * * *",
    version: 1.0,
    description: "Collect various usage metrics and store in Customer HANA tables.",
  },
  {
    applicationId: ApplicationId.METERING_SERVICE,
    activity: Activity.COLLECT_METRICS_TEST,
    cron: "X * * * *",
    version: 1.0,
    description: "Collect various usage metrics and store in Customer HANA tables for testing Frequency.",
    enabledForTenant: defineEnablementOfMeteringScheduleForTestCollect,
  },
  {
    applicationId: ApplicationId.METERING_SERVICE,
    activity: Activity.SET_DEFAULT_RMS_POD_LIMIT,
    cron: "X 3 * * *",
    version: 1.0,
    description: "Set default RMS POD Count to correct default value.",
  },
  {
    applicationId: ApplicationId.DATA_PLANE_SERVICE,
    activity: Activity.PURGE_WORKFORCE_PERSON_DATA,
    cron: "X X/12 * * *",
    version: 1.0,
    description: "Purge inactive workforce person data without valid purpose",
  },
  {
    applicationId: ApplicationId.ANALYTICAL_MODEL_PREVIEW,
    activity: Activity.ANALYTICAL_MODEL_CLEANUP_TASK,
    cron: "X X * * *",
    version: 1.0,
    description: "[Analytic Model Cleanup] Preview Cleans up task logs in Query Builder.",
  },
  {
    applicationId: ApplicationId.SPACE_LOCKING,
    activity: Activity.LOCKING,
    cron: "X * * * *",
    version: 1.0,
    description: "Lock or unlock Spaces.",
  },
  {
    applicationId: ApplicationId.CUSTOMER_HANA,
    activity: Activity.RENEW_SERVICE_KEY,
    cron: "X X * * *",
    version: 1.0,
    description: "Triggers renewal of Customer HANA service key when data is outdated",
  },
  {
    applicationId: ApplicationId.CONNECTIONS,
    activity: Activity.UCL_CERTIFICATE_ROTATE,
    cron: "X 1 * * *",
    version: 1.0,
    description: "Rotate the client certificate for UCL connections",
  },
  {
    applicationId: ApplicationId.CONNECTIONS,
    activity: Activity.HDLF_CERTIFICATE_ROTATE,
    cron: "X 1 * * *",
    version: 1.0,
    description: "Rotate the client certificate for HDLF connections",
  },
  {
    applicationId: ApplicationId.CONNECTIONS,
    activity: Activity.COLLECT_METRICS,
    cron: "X * * * 6",
    version: 1.3,
    description: "Collect metrics from the connection objects",
    enabledForTenant: enabledForTenant(["DWCO_CONNECTION_METRICS"]),
  },
  {
    applicationId: ApplicationId.LOCAL_TABLE,
    activity: Activity.COLLECT_METRICS,
    cron: "X X * * SAT",
    version: 2.2,
    description: "Send delta read api dynatrace statistics",
    enabledForTenant: enabledForTenant(["DWCO_TABLE_DELTA_UPSERT_READ_API"]),
  },
  {
    applicationId: ApplicationId.SPACE,
    activity: Activity.MIGRATE,
    cron: "X 23 * * *",
    version: 1.0,
    description: "Check space versions and trigger migration if outdated",
  },
  {
    applicationId: ApplicationId.DATA_FLOWS,
    activity: Activity.DAILY_METRICS,
    cron: "X 20 * * *",
    version: 1.0,
    description: "Send DataFlow dynatrace statistics",
  },
];

const mappedSystemTaskRegistry = new Map<string, ISystemTaskDefinition>(
    systemTaskRegistry.map((task) => {
      const key = `${task.applicationId}.${task.activity}`;
      return [key, task];
    })
  );

export function getSystemTaskRegistryMap(): Map<string, ISystemTaskDefinition> {
  return mappedSystemTaskRegistry;
}
