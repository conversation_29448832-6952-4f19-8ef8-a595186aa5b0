/** Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved. */

import { IRequestContext } from "@sap/dwc-context-checks";
import { TaskIdentifier } from "../models/TaskIdentifier";
import { ScheduleObjectBuilder } from "./ScheduleObjectBuilder";
import { ScheduleCreation, ScheduleCreationInput, ScheduleFilters, ScheduleUpdateBase, ScheduleUserInfo, StoredSchedule, WithNextRun } from "./ScheduleTypes";
import { TaskScheduler } from "./TaskScheduler";
import { isTimezoneEnabled } from "./isTimezoneEnabled";
export class TaskSchedulesApi {

  private scheduler: TaskScheduler;

  private constructor(
    private readonly context: IRequestContext,
    private readonly isTimezoneEnabled: boolean
  ) { }

  /**
   * Creates an instance of TaskSchedulesApi for the given spaceId. This instance can be used to create, retrieve, update, and delete non-technical schedules (impersonated).
   */
  public static async createInstance(context: IRequestContext, spaceId: string): Promise<TaskSchedulesApi> {
    const tzEnabled = await isTimezoneEnabled(context);
    const instance = new TaskSchedulesApi(context, tzEnabled);
    instance.scheduler = await TaskScheduler.createInstanceImpersonated(context, spaceId);
    return instance;
  }

  /**
   * Creates a task schedule and returns the schedule id of the newly creates schedule
   * @param schedule - The schedule to create. The spaceId in the schedule should be the same as the spaceId of the TaskSchedulesApi instance.
   * User info is automatically added to the schedule based on the user in the context.
   * @returns The schedule id of the newly created schedule
   * @throws Error {@link SchedulerError} If the schedule is invalid or the schedule could not be created.
   */
  public async createSchedule(schedule: ScheduleCreationInput): Promise<string> {
    const builder = new ScheduleObjectBuilder(this.context);
    const userInfo: ScheduleUserInfo = builder.getSchedulerUserInfo();
    const scheduleCreation: ScheduleCreation = {
      ...schedule,
      ...userInfo,
    };
    if (!this.isTimezoneEnabled) {
      scheduleCreation.tzName = null;
    }
    return await this.scheduler.createSchedule(scheduleCreation);
  }

  /**
   * Retrieves a schedule by its scheduleId in the space of the TaskSchedulesApi instance
   * @param scheduleId - The schedule id of the schedule to retrieve
   * @returns The schedule with the given scheduleId, including the next run time
   * @throws Error {@link SchedulerError} If the schedule could not be found
   */
  public async getSchedule(scheduleId: string): Promise<StoredSchedule & WithNextRun> {
    const schedule = await this.scheduler.getSchedule(scheduleId);
    if (!this.isTimezoneEnabled) {
      schedule.tzName = null;
    }
    return schedule;
  }

  /**
   * Retrieves all schedules that match the given filters in the space of the TaskSchedulesApi instance
   * @param filters - The filters to apply to the schedules, besides the speceid, which is always the space of this TaskSchedulesApi instance.
   * The filters can include the applicationId, activity, objectId, activationStatus, and owner.
   * @returns An array of schedules that match the given filters, including the next run time in each schedule
   * @throws Error {@link SchedulerError} If the schedules could not be retrieved
   */
  public async listSchedules(filters: ScheduleFilters = {}): Promise<Array<StoredSchedule & WithNextRun>> {
    const list = await this.scheduler.listSchedules(filters);
    if (!this.isTimezoneEnabled) {
      list.forEach(schedule => schedule.tzName = null);
    }
    return list;
  }

  /**
   * Deletes a schedule by its scheduleId in the space of the TaskSchedulesApi instance
   * @param scheduleId - The schedule id of the schedule to delete
   * @returns A boolean indicating whether the schedule was successfully deleted (in pacemaker)
   * @throws Error {@link SchedulerError} If the schedule could not be found
   */
  public async deleteSchedule(scheduleId: string): Promise<boolean> {
    const success = await this.scheduler.deleteSchedules([scheduleId]);
    return success?.deletedJobs === 1;
  }

  /**
   * Deletes all schedules that match the given task in the space of the TaskSchedulesApi instance
   * @param task - The task to match schedules to delete. The spaceId in the task should be the same as the spaceId of the TaskSchedulesApi instance.
   * It will also delete schedules matching objectId with a variant of the task's objectId (e.g `objectId:variant`)
   * @returns A map of schedule ids to booleans indicating whether each schedule was successfully deleted
   * @throws Error {@link SchedulerError} If the schedules could not be found
   */
  public async deleteSchedules(task: TaskIdentifier) {
    const success = await this.scheduler.deleteScheduleByTaskIdentifier(task);
    return success;
  }

  /**
   * Updates a schedule by its scheduleId in the space of the TaskSchedulesApi instance
   * @param scheduleId - The schedule id of the schedule to update
   * @param updates - An object with the fields to update in the schedule. Note that fields describing the task being scheduled (like applicationId, activity, objectId, etc.) cannot be updated.
   * Only fields describing the scheddule itself (like validFrom, validTo, activationStatus, etc.) can be updated. If both cron and frequency are provided, cron will be used.
   * @throws Error {@link SchedulerError} If the schedule could not be updated
   */
  public async updateSchedule(scheduleId: string, updates: Partial<ScheduleUpdateBase>): Promise<void> {
    if (!this.isTimezoneEnabled) {
      delete updates.tzName;
    }
    return await this.scheduler.updateSchedule(scheduleId, updates);
  }
  /**
   * Pauses a schedule by its scheduleId in the space of the TaskSchedulesApi instance
   * @param scheduleId - The schedule id of the schedule to pause
   * @throws Error {@link SchedulerError} If the schedule could not be paused
   */
  public async pauseSchedule(scheduleId: string): Promise<void> {
    return await this.scheduler.pauseOrResumeSchedules([scheduleId], true);
  }

  /**
   * Resumes a schedule by its scheduleId in the space of the TaskSchedulesApi instance
   * @param scheduleId - The schedule id of the schedule to resume
   * @throws Error {@link SchedulerError} If the schedule could not be resumed
   */
  public async resumeSchedule(scheduleId: string): Promise<void> {
    return await this.scheduler.pauseOrResumeSchedules([scheduleId], false);
  }

  /**
   * Updates the owner of a schedule by its scheduleId in the space of the TaskSchedulesApi instance
   * @param scheduleId - The schedule id of the schedule to update the owner of
   * @throws Error {@link SchedulerError} If the owner could not be updated, or the schedule was not found
   */
  public async updateScheduleOwner(scheduleId: string): Promise<void> {
    const builder = new ScheduleObjectBuilder(this.context);
    const user = builder.getUserInfo();
    return await this.scheduler.updateOwner(scheduleId, user);
  }
}
