/** Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved. */

import { FeatureFlagProvider, IRequestContext } from "@sap/dwc-context-checks";
import { logDebug } from "../shared/logger/TaskFrameworkLogger";

export async function isTimezoneEnabled(context: IRequestContext) {
  const isOn = await FeatureFlagProvider.isFeatureActive(context, "DWCO_TASK_FRAMEWORK_LOCAL_TIME_ZONES");
  logDebug(`[isTimezonEnabled] Feature flag DWCO_TASK_FRAMEWORK_LOCAL_TIME_ZONES is ${isOn ? "enabled" : "disabled"}`, { context });
  return isOn;
}
