/** Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved. */
/* eslint-disable @sap/dwc-lints/unbound-promises-handled */

import { IRequestContext } from "../../repository/security/common/common";

import { logError, logInfo } from "../shared/logger/TaskFrameworkLogger";
import { IEmailNotification, NotificationsClient } from "@sap/dwc-notifications";
import { getTenantAdminAndTenantpublicFqdn } from "../logger/services/notifications/notificationsUtils";
import { ILogOptions } from "@sap/dwc-logger";
import { DisplayNameService } from "../logger/services/DisplayNameService";
import { IEndedTask } from "../logger/models/ITaskLog";
import { NotificationsMailingList } from "../logger/services/notifications/NotificationsMailingList";
import { ITaskEmailNotification } from "../notificationTask/INotification";
import { NotificationsMailingListEntry } from "../chains/models";
import { NotificationSendErrorCodes, NotificationSendRetryableError } from "../errors/NotificationSendRetryableError";
import { FailedGroup } from "../notificationTask/notificationTaskTypes";

/**
 * Service responsible for handling and sending email notifications related to tasks.
 * This includes generating email content with placeholders, managing recipient lists,
 * and logging the success or failure of sending notifications.
 */
export class TaskEmailNotificationService {
  private logOptions: ILogOptions;

  /**
   * Creates an instance of TaskEmailNotificationService.
   * @param context The request context
   */
  public constructor(
    private readonly context: IRequestContext,
  ) {
    this.logOptions = { context: this.context };
  }

  private async postEmailNotification(context: IRequestContext, notification: IEmailNotification): Promise<{ id?: unknown }> {
    try {
      return await NotificationsClient.postEmail(context, notification) as { id?: unknown };
    } catch (error) {
      logError(`[NotificationTask][postEmailNotification] Failed to send email: ${error}`, this.logOptions);
      throw error;
    }
  }

  private async sendSingleNotificationToMultipleRecipients(notification: ITaskEmailNotification, recipientList: NotificationsMailingListEntry): Promise<void> {
    const { body, subject, objectId, spaceId, logId, failedGroups } = notification;
    if (!body || !subject) {
      const missingElements = [];
      if (!body) { missingElements.push("body") };
      if (!subject) { missingElements.push("subject") };

      throw new Error("[NotificationService][sendSingleNotificationToMultipleRecipients] Could not send Email notification because of missing elements: " + missingElements.join(", "));
    }
    const promises: Array<Promise<unknown>> = [];
    const groupKeys: FailedGroup[] = [];

    const sanitizedBody = this.sanitizeHTMLAndAddLineBreaks(body);

    const finalBody = this.replacePlaceholders(sanitizedBody, notification, true);
    const footer = await this.buildNotificationFooter(objectId, spaceId);
    const computedBody = `${finalBody} ${footer}`;

    const sanitizedSubject = this.sanitizeHTMLAndAddLineBreaks(subject);
    const finalSubject = this.replacePlaceholders(sanitizedSubject, notification);

    const shouldSendToGroup = (group: FailedGroup) => !failedGroups || failedGroups.length === 0 || failedGroups.includes(group);

    if (recipientList.others.length !== 0 && shouldSendToGroup("others")) {
      const notification = this.createEmailNotification(computedBody, finalSubject, "SAP Datasphere", recipientList.others, true);
      promises.push(this.postEmailNotification(this.context, notification));
      groupKeys.push("others");
    }
    if (recipientList.tenantMembers.length !== 0 && shouldSendToGroup("tenantMembers")) {
      const notification = this.createEmailNotification(computedBody, finalSubject, "SAP Datasphere", recipientList.tenantMembers, false);
      promises.push(this.postEmailNotification(this.context, notification));
      groupKeys.push("tenantMembers");
    }

    const settledResults = await Promise.allSettled(promises);

    const failed: FailedGroup[] = [];
    settledResults.forEach((result, index) => {
      const group = groupKeys[index];
      const logPrefix = `[NotificationService][sendSingleNotificationToMultipleRecipients] (LogID: ${logId})`;

      if (result.status === "rejected") {
        failed.push(group);
        logError(`${logPrefix} Could not send Email notification for object: ${objectId} to ${group}: ${result.reason}`, this.logOptions);
      } else {
        const resultValue = result.value as { id?: unknown };
        logInfo(
          `${logPrefix} Sent Email notification for object: ${objectId} to ${group}${resultValue?.id ? ` with jobId: ${String(resultValue.id)}` : ""}`,
          this.logOptions
        );
      }
    });

    if (failed.length > 0) {
      throw new NotificationSendRetryableError({ code: NotificationSendErrorCodes.EMAIL_DELIVERY_FAILED, failedGroups: failed });
    }
  }

  /**
   * Sends email notifications for a list of tasks.
   * 
   * Each task in the list should include details like body, subject, objectId, spaceId, and optionally
   * a `params` map used to replace dynamic placeholders in the email content.
   * 
   * @param notifications Array of task email notifications to send. Each notification includes:
   *   - `body`: The raw email body, possibly with placeholders like `$$username$$`.
   *   - `subject`: The email subject line.
   *   - `logId`
   *   - `objectId`
   *   - `spaceId`
   *   - `params`
   * 
   * @returns A Promise that resolves when all notifications have been processed.
   */
  public async sendEmailNotifications(notifications: ITaskEmailNotification[]): Promise<void> {
    const backgroundContext: IRequestContext = this.context.createNewForBackground();

    try {
      notifications.forEach(notification => {
        if (!notification.mailingListName) {
          notification.mailingListName = notification.objectId;
        }
      });

      const crossSpaceMailingLists = await NotificationsMailingList.getCrossSpaceMailingLists(backgroundContext, notifications.map(task => ({ objectId: task.mailingListName as string, spaceId: task.spaceId })));
      const notificationsWithValidRecipients = crossSpaceMailingLists.filter(x => x?.mailingList?.others?.length || x?.mailingList?.tenantMembers?.length);

      const promises = notifications.map(notification => {
        const matched = notificationsWithValidRecipients.find(x => x.name === notification.mailingListName && x.spaceId === notification.spaceId);

        if (!matched) {
          logError(`[NotificationService][sendEmailNotifications] No matching task found for object: ${notification.objectId} in space: ${notification.spaceId}`, { context: this.context });
          return Promise.resolve();
        }

        return this.sendSingleNotificationToMultipleRecipients(notification, matched.mailingList)
          .catch(error => {
            logError(`[NotificationService][sendEmailNotifications] Could not send Email notification for ${notification.logId}: ${error}`, this.logOptions);
          });
      });

      await Promise.allSettled(promises);
    } catch (error) {
      logError([`[NotificationService][sendEmailNotifications] Could not send email notifications.`, error], { context: backgroundContext });
      throw error;
    } finally {
      await backgroundContext.finish();
    }
  }

  public async sendEmailNotification(notification: ITaskEmailNotification): Promise<void> {
    const backgroundContext: IRequestContext = this.context.createNewForBackground();

    try {
      if (!notification.mailingListName) {
        notification.mailingListName = notification.objectId;
      }

      const [mailingListEntry] = await NotificationsMailingList.getCrossSpaceMailingLists(backgroundContext, [{ objectId: notification.mailingListName, spaceId: notification.spaceId }]);

      const hasRecipients = mailingListEntry?.mailingList?.others?.length || mailingListEntry?.mailingList?.tenantMembers?.length;
      if (!hasRecipients) {
        const errorMsg = `[NotificationService][sendEmailNotification] No matching task found or no recipients for object: ${notification.objectId} in space: ${notification.spaceId}`;
        throw new Error(errorMsg);
      }

      await this.sendSingleNotificationToMultipleRecipients(notification, mailingListEntry.mailingList);
    } catch (error) {
      logError([`[NotificationService][sendEmailNotification] Could not send email for ${notification.logId}.`, error], { context: backgroundContext });
      throw error;
    } finally {
      await backgroundContext.finish();
    }
  }

  private replacePlaceholders(text: string, notification: ITaskEmailNotification, isBody: boolean = false): string {
    const defaultMap = this.getDefaultMap(notification);
    const mergedTaskMap = this.mergeMaps(defaultMap, notification.params);

    // Matches patterns like $$key$$ or $$key:color$$
    //   ([a-zA-Z0-9_]+) - captures the 'key' (letters, digits, or underscores)
    //   (?:#?[a-zA-Z0-9]+)? - optionally matches a color (optional '#' followed by alphanumeric)
    // Example matches: $$username$$, $$username:red$$, $$username:#ff0000$$
    return text.replace(/\$\$([a-zA-Z0-9_]+)(?::(#?[a-zA-Z0-9]+))?\$\$/g, (matched, key, color) => {
      const rawValue = mergedTaskMap.get(`$$${key}$$`);
      if (rawValue === undefined) {
        return matched;
      }

      const styledValue = color && isBody ? `<span style="color: ${color};">${rawValue}</span>` : rawValue;

      return isBody ? `<b>${styledValue}</b>` : styledValue;
    });
  }

  private getDefaultMap(notification: ITaskEmailNotification): Map<string, string> {
    const defaultMap = new Map<string, string>([
      ["$$spaceId$$", notification.spaceId],
      ["$$objectId$$", notification.objectId],
      ["$$logId$$", notification.logId.toString()],
    ]);

    return defaultMap;
  }

  private mergeMaps(defaultMap: Map<string, string>, taskMap?: Map<string, string>): Map<string, string> {
    const mergedMap = new Map(defaultMap);

    if (taskMap) {
      for (const [key, value] of taskMap.entries()) {
        mergedMap.set(key, value);
      }
    }

    return mergedMap;
  }

  private async buildNotificationFooter(objectId: string, spaceId: string): Promise<string> {
    const [adminEmail, publicFqdn] = await getTenantAdminAndTenantpublicFqdn(this.context);
    const tenantName = publicFqdn.substring(0, publicFqdn.indexOf("."));
    return `
      <br>
      <hr/>
      <p> <span style="color:#7f8c8d"> The content of this email may have been customized by the owner of the Task: ${objectId}.</span> </p>
      <p><span style="color:#7f8c8d"> Tenant name: ${tenantName}.</span> </p >
      <p><span style="color:#7f8c8d"> Space name: ${spaceId}.</span> </p >
      <p><span style="color:#7f8c8d"> If you want to be removed from the notification list for this task chain, please contact the tenant administrator <a href = "mailto:${adminEmail}" > ${adminEmail} </a>.</span > </p>
      `;
  }

  private createEmailNotification(body: string, subject: string, sender: string, recipientList: string[], isUserEmailProvided: boolean, contentType = "html"): IEmailNotification {
    return {
      sender,
      body,
      subject,
      contentType,
      bUserEmailProvided: isUserEmailProvided,
      to: recipientList
    } as IEmailNotification;
  }

  private sanitizeHTMLAndAddLineBreaks(body: string): string {
    const sanitized = body.replace(/<\/?[^>]+(>|$)/g, "");
    return sanitized.replace(RegExp("\n", "g"), "<br>");
  }

  /**
 * Adds display names to the provided list of finished tasks.
 * Replaces technical usernames with display names based on space ID.
 *
 * @param finishedTasks List of ended tasks to enrich with display names.
 * @returns A Promise resolving to the list of tasks with display names.
 */
  public async getTasksWithDisplayName(finishedTasks: IEndedTask[]) {
    const displayNameService = new DisplayNameService();
    const tasksWithDisplayName = [] as IEndedTask[];
    if (!this.context.userInfo.technical) {
      const spaceIds = finishedTasks.map(t => t.spaceId);
      for (const space of spaceIds) {
        const tasksInSpace = finishedTasks.filter(t => t.spaceId === space);
        const perSpaceWithDisplayName = await displayNameService.replaceUserNameByDisplayName(this.context, tasksInSpace, space);
        tasksWithDisplayName.push(...perSpaceWithDisplayName);
      }
    } else {
      const allTasksWithDisplayName = await displayNameService.replaceUserNameByDisplayName(this.context, finishedTasks, undefined);
      tasksWithDisplayName.push(...allTasksWithDisplayName);
    }
    return tasksWithDisplayName;
  }
}
