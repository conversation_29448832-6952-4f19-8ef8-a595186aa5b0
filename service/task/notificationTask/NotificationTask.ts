/** Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved. */
/* eslint-disable @sap/dwc-lints/unbound-promises-handled */

import { ILogOptions, LogLevel } from "../../logger";
import { IRequestContext, Activity as PermissionActivity, AuthType } from "../../repository/security/common/common";
import { Activity, ApplicationId, ChainParameters, IExecutable, Parameters } from "../models";
import { ITaskLogger, ITaskLogMessage, Severity, Status, Substatus } from "../logger/models";
import { ExecuteResult, TaskExecuteResponse } from "../models/TaskExecuteResponse";
import { LockIdentifier, LockKey, LockedTask } from "../orchestrator/services/tasklock";
import { OverwriteResponse } from "../orchestrator/services/tasklock/OverwriteResponse";
import { logError, logInfo, logPerformance, logVerbose, logWarning } from "../shared/logger/TaskFrameworkLogger";
import { TaskEmailNotificationService } from "../public/TaskEmailNotificationService";
import { ITaskEmailNotification } from "./INotification";
import { EmailPayload } from "./notificationTaskTypes";
import { MessageKey } from "./notificationTaskEnums";
import { NotificationSendRetryableError } from "../errors/NotificationSendRetryableError";
import { getTenantAdminAndTenantpublicFqdn } from "../logger/services/notifications/notificationsUtils";
import { DisplayNameService } from "../logger/services/DisplayNameService";

export class NotificationTask implements IExecutable {
  private notificationLogId: number;
  private logOptions: ILogOptions;
  private static readonly MAX_LOCK_AGE = 2 * 60 * 60 * 1000;

  chainParameters: ChainParameters | undefined;
  private configuration: EmailPayload;
  private chainName: string | undefined;

  public constructor(
    private readonly context: IRequestContext,
    private readonly spaceId: string,
    private readonly objectId: string,
    private readonly activity: Activity,
    private readonly parameters: Parameters,
  ) {
    this.logOptions = { context: this.context };
    this.chainName = this.parameters.tf?.chain?.chainName;
  }

  async execute(logger: ITaskLogger): Promise<TaskExecuteResponse> {
    const timing = new Date();
    this.notificationLogId = logger.getLogId();
    try {
      logPerformance(LogLevel.Info, timing, `[NotificationTask][execute] Starting ${this.activity} for task ${this.notificationLogId}, space ${this.spaceId}, object ${this.objectId}.`, this.logOptions);
      logVerbose(`[NotificationTask][execute] Parameters passed to run: ${JSON.stringify(this.parameters, null, 2)}`, this.logOptions);

      if (this.activity === Activity.SEND_EMAIL) {
        const message = this.buildTaskLogMessage(
          `Starting the Notification task "${this.objectId}".`,
          Severity.INFO,
          MessageKey.RUN_START,
          [this.objectId]
        );
        await logger.logMessage([message]);
        this.configuration = this.chainParameters?.Config?.configuration as EmailPayload;
        return await this.executeEmailNotification(logger);
      } else if (this.activity === Activity.CANCEL) {
        return await this.executeCancel(logger);
      }
      return { status: Status.FAILED };
    } catch (error) {
      logWarning(`[NotificationTask][execute] Notification finishes with status FAILED. objectId: ${this.objectId}, taskLogId: ${this.notificationLogId}, spaceId: ${this.spaceId}`, this.logOptions);
      const taskLogMessage = this.buildTaskLogMessage(
        `The Notification task "${this.objectId}" has failed to complete.`,
        Severity.ERROR,
        MessageKey.COMPLETION_FAILURE,
        [this.objectId],
        error.message
      );
      await logger.logMessage([taskLogMessage]);
      return { status: Status.FAILED };
    }
  }

  private async executeEmailNotification(logger: ITaskLogger): Promise<TaskExecuteResponse> {
    if (this.chainName) {
      const updated = await logger.updateExternalInstanceId(this.chainName);
      logVerbose(`[NotificationTask][executeEmailNotification] Updated (${updated === 1}) externalInstanceId to ${this.chainName} for taskLogId: ${logger.getLogId()}.`, this.logOptions);
    }
    if (!this.configuration) {
      logWarning([`[NotificationTask][executeEmailNotification] Configuration is not present.`], this.logOptions);
      return { status: Status.FAILED, subStatusCode: Substatus.PREREQ_NOT_MET };
    }

    try {
      logVerbose(`[NotificationTask][executeEmailNotification] Getting deployed metadata for repository object: ${this.objectId}, spaceId: ${this.spaceId}`, this.logOptions);

      const emailNotificationService = new TaskEmailNotificationService(this.context);
      const taskChainName = this.chainName;
      if (!taskChainName) {
        logWarning(`[NotificationTask][executeEmailNotification] Chain name is not present.`, this.logOptions);
        return { status: Status.FAILED, subStatusCode: Substatus.PREREQ_NOT_MET };
      }

      const params = this.configuration.params || new Map<string, string>();
      params.set("$$chainName$$", taskChainName);
      const [, publicFqdn] = await getTenantAdminAndTenantpublicFqdn(this.context);

      const uiLink = `<a href="https://${publicFqdn}/dwaas-core/index.html#/dataintegration&/di/logdetails/${this.spaceId}/${ApplicationId.NOTIFICATION}/${this.objectId}/${this.notificationLogId}">Notification Task Run Details</a> `
      const displayNameService = new DisplayNameService();
      const [{ user: displayName = "" }] = await displayNameService.replaceUserNameByDisplayName(this.context, [{ user: this.context.userInfo.userName }], this.spaceId);

      params.set("$$uiLink$$", uiLink);
      params.set("$$user$$", displayName);

      const notification: ITaskEmailNotification = {
        body: this.configuration.body,
        subject: this.configuration.subject,
        logId: this.notificationLogId,
        objectId: this.objectId,
        spaceId: this.spaceId,
        params,
        mailingListName: taskChainName,
      }

      try {
        await emailNotificationService.sendEmailNotification(notification);
      } catch (error) {
        if (error instanceof NotificationSendRetryableError) {
          await logger.updateTaskParameters(notification.logId, { failedGroups: error.failedGroups });
          logWarning(`[NotificationTask][executeEmailNotification] Notification send retryable error for ${this.notificationLogId}: ${error.message}`, this.logOptions);
          return {
            status: Status.RUNNING,
          };
        }
        logError(`[NotificationTask][executeEmailNotification] Failed to send email notification for ${this.notificationLogId}: ${error}`, this.logOptions);

        const taskLogMessage = this.buildTaskLogMessage(
          `The Notification task "${this.objectId}" has failed to complete.`,
          Severity.ERROR,
          MessageKey.COMPLETION_FAILURE,
          [this.objectId],
          error.message
        );
        await logger.logMessage([taskLogMessage]);
        return {
          status: Status.FAILED,
        };
      }

      logInfo(`[NotificationTask][executeEmailNotification] Completed`, this.logOptions);
      const taskLogMessage = this.buildTaskLogMessage(
        `The Notification task "${this.objectId}" has completed.`,
        Severity.INFO,
        MessageKey.COMPLETION_SUCCESS,
        [this.objectId]
      );
      await logger.logMessage([taskLogMessage]);
      return {
        status: Status.COMPLETED,
      }
    } catch (error) {
      logError(`[NotificationTask][executeEmailNotification] Could not send Email notification for ${this.notificationLogId}: ${error}`, this.logOptions);
      throw error;
    }
  }

  private async executeCancel(logger: ITaskLogger): Promise<ExecuteResult> {
    logInfo(`[NotificationTask][executeCancel]`, this.logOptions);
    if (this.parameters.tf?.cancelId) {
      const cancelId = this.parameters.tf.cancelId;
      const text = `Cancelling notification task '${this.objectId}' with logId ${cancelId}`;
      logInfo(`[NotificationTask][executeCancel] ${text}`, this.logOptions);
      const updated = await logger.updateExternalInstanceId(`${cancelId}`);
      logVerbose(`[NotificationTask][executeCancel] Updated (${updated === 1}) externalInstanceId to ${cancelId} for taskLogId: ${logger.getLogId()}.`, this.logOptions);
      const message = this.buildTaskLogMessage(text, Severity.INFO, MessageKey.CANCEL_START, [this.objectId, `${cancelId}`]);
      await logger.logMessage([message]);
      return Promise.resolve({ status: Status.RUNNING });
    }
    const text = `Nothing to cancel in ${this.objectId} when cancelId = ${this.parameters.tf?.cancelId}`;
    logWarning(`[NotificationTask][executeCancel] ${text}`, this.logOptions);
    const message = this.buildTaskLogMessage(text, Severity.ERROR, MessageKey.CANCEL_NOT_POSSIBLE, [this.objectId, `${this.parameters.tf?.cancelId}`]);
    await logger.logMessage([message]);
    return Promise.resolve({ status: Status.FAILED, subStatusCode: Substatus.PREREQ_NOT_MET });
  }

  /**
   * Returns true, if the given user is authorized to execute this notification.
   *
   *
   * @param taskLogger The logger to write messages to.
   * @returns a boolean indicating whether the current user is authorized
   */
  public async isAuthorized(taskLogger: ITaskLogger, isDesignTime = false): Promise<boolean> {
    logInfo(`[NotificationTask][isAuthorized] called for ${this.activity}`, this.logOptions);
    let result = false;

    switch (this.activity) {
      case Activity.SEND_EMAIL:
        try {
          const direct = !!this.parameters.tf?.isDirect;
          const permission = direct ? PermissionActivity.update : PermissionActivity.execute;
          result = this.context.userInfo.technical !== true && this.context.hasPrivilegeOnScope(this.spaceId, AuthType.DWC_DATAINTEGRATION, permission);
        } catch (err) {
          logError(`[NotificationTask][isAuthorized] Error checking notification task ${this.activity} privilege.`, this.logOptions);
        }
        break;
      case Activity.CANCEL:
        result = this.context.hasPrivilegeOnScope(this.spaceId, AuthType.DWC_DATAINTEGRATION, PermissionActivity.update) || this.context.hasPrivilegeOnScope(this.spaceId, AuthType.SYSTEMINFO, PermissionActivity.update);
        break;
      default:
        logError(`[NotificationTask][isAuthorized] Not supported activity ${this.activity} for NotificationTask`, this.logOptions);
        break;
    }
    logInfo(`[NotificationTask][isAuthorized] Authorization check result: ${result} `, this.logOptions);
    return result;
  }

  public getLockIdentifier(): LockIdentifier | null {
    let objectId = this.objectId;
    if (this.chainName) {
      objectId = `${this.objectId}:${this.chainName}`;
    }
    switch (this.activity) {
      case Activity.CANCEL:
        return {
          applicationId: ApplicationId.NOTIFICATION,
          objectId,
          spaceId: this.spaceId,
          lockKey: LockKey.CANCEL
        };
      case Activity.SEND_EMAIL:
        return {
          applicationId: ApplicationId.NOTIFICATION,
          objectId,
          spaceId: this.spaceId,
          lockKey: LockKey.EXECUTE
        };
      default:
        logWarning(`[NotificationTask][getLockIdentifier] Invalid activity: ${this.activity}.`, this.logOptions);
        return null;
    }
  }

  /**
   * Checks whether the current task can be overwritten or not.
   * @param lock The lock to check on.
   */
  public async overwriteLock(lock: LockedTask): Promise<OverwriteResponse> {
    const age = Date.now() - lock.creationTime.getTime();
    const isExpired = age > NotificationTask.MAX_LOCK_AGE;

    if (isExpired) {
      logWarning(`[NotificationTask][overwriteLock] ${ApplicationId.NOTIFICATION}/${this.activity}/${this.spaceId}/${this.objectId} is running longer than ${NotificationTask.MAX_LOCK_AGE}`, { context: this.context });
      return { takeover: true, status: Status.FAILED, endTime: new Date() };
    }

    return { takeover: false };
  }

  private buildTaskLogMessage(text: string, severity: Severity, messageBundleKey?: MessageKey, parameterValues?: string[], details?: string): ITaskLogMessage {
    return {
      severity,
      text,
      messageBundleId: MessageKey.MSG_BUNDLE_ID,
      messageBundleKey,
      parameterValues,
      details,
    };
  }
}
