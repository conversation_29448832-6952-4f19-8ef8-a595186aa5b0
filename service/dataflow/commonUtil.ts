/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { IGetObjectParameters, ObjectKind, RepositoryObjectType } from "@sap/deepsea-types";
import { GetObjectParameters, RepositoryObject } from "@sap/deepsea-utils";
import { AuthenticationMode, ResolvedResponse, httpClient } from "@sap/dwc-http-client";
import Status from "http-status-codes";
import { AuditLogService, IAuditLogEvent } from "../auditing";
import { LogLevel, getLogger } from "../logger";
import { RepositoryObjectClient } from "../repository/client/repositoryObjectClient";
import { IRequestContext } from "../repository/security/common/common";
import { ExternalCallCategory } from "../repository/security/common/externalCallTypes";
import {
  DeployedMetadata,
  IDeployedMetadata,
} from "../reuseComponents/spaceTecTables/src/content/deployedMetadataService";
import { deploySpaceTecTables } from "../reuseComponents/spaceTecTables/src/deploySpaceTecTables";
import { TaskLogsApi } from "../task/logger/controllers/TaskLogsApi";
import {
  DI_PROXY_SCOPE,
  FLOW_KINDS,
  GRAPHS_QUERY_DETAIL_LEVEL,
  METHODS,
  RUNTIME_INSTANCES_DEFAULT_LIMIT,
  SERVICE_TO_DI_TIMEOUT,
  circuitBreakerContext,
} from "./constants";
import { IDIProxyRequestOptions, IDIVersionDetails, IDataFlowOptions, IRequestOptions } from "./models";
import { getDiProxyServiceUrl, graphPath, graphQuery, modeler } from "./url";

const { log, logDebug, logError, logPerformance, logInfo, logWarning } = getLogger("DATAFLOW");

export class DataFLowRequestError extends Error {
  constructor(message: string, public errorDetails: any[], public body: any, public statusCode?: number) {
    super(message);
    this.name = "DataFlowRequestError";
  }
}

export function getDIProxyRequestOptions(context: IRequestContext, options: IRequestOptions): IDIProxyRequestOptions {
  const { acceptedStatusCodes, body, callCategory, callTimeout, circuitBreakerContext, headers, method } = options;

  const appEnv = JSON.parse(process.env.VCAP_APPLICATION || "");
  const deployEnv = {
    cfOrg: appEnv.organization_name,
    cfSpace: appEnv.space_name,
  };

  return {
    acceptedStatusCodes: acceptedStatusCodes || [],
    authentication: AuthenticationMode.Uaa,
    body,
    callCategory: callCategory || ExternalCallCategory.DATAFLOW,
    callTimeout: callTimeout || SERVICE_TO_DI_TIMEOUT,
    circuitBreakerContext,
    headers,
    method: method || METHODS.GET,
    requestContext: context,
    scope: DI_PROXY_SCOPE,
    xsAppName: `di-proxy-${deployEnv.cfSpace}-${deployEnv.cfOrg}`,
  };
}

/**
 * Central DataFlow request call.
 */
export async function requestDataFlow(
  caller: string,
  url: string,
  context: IRequestContext,
  options: IDataFlowOptions = {}
): Promise<any> {
  log(LogLevel.Info, `Request DataFlow service by ${caller} and ${url}.`, { context });

  let headers = {
    "Content-Type": "application/json",
  };
  if (options.headers) {
    headers = options.headers;
  }
  const requestOptions = getDIProxyRequestOptions(context, {
    body: options.body,
    callTimeout: options.timeout,
    circuitBreakerContext,
    headers,
    method: options.method,
  });

  try {
    const response = await httpClient.call({
      url: { href: url },
      opts: requestOptions,
    });
    if (response.status < 200 || response.status >= 300) {
      logError(`[DataFlowRequestError] Error calling url ${url}, caller ${caller}, Error ${response}`, { context });
      throw responseError(response, caller, context);
    }
    // Commenting this code out as it is causing 200 status reponse to fail when key 'error' is passed in array of objects in a response
    // } else if (Array.isArray(response.body)) {
    //   const body = response.body;
    //   if (body.length > 0 && body[0] && body[0].error) {
    //     throw responseError(response, caller, context);
    //   }
    // }
    // in contrast for former implementation we let low-level error fall through
    return response.body;
  } catch (error) {
    logError(`[DataFlowRequestError] Error calling url ${url}, caller ${caller}, Error ${error}`, { context });
    if (error.type === "aborted") {
      error = new DataFLowRequestError(`Failed to fetch the data.${caller} Request timed out.`, [], "aborted");
    }
    throw error;
  }
}

export async function delay(milliseconds: number) {
  return new Promise((res) => setTimeout(res, milliseconds));
}

export function responseError(response: ResolvedResponse, caller: string, context: IRequestContext) {
  const body = response.body as any;
  let description = "";
  const url = context.httpRequest?.originalUrl;
  const validURL = url && showCausesInErrorResponse(url);
  if (body && body.causes && Array.isArray(body.causes) && validURL) {
    for (let i = 0; i < body.causes.length; i++) {
      logDebug(`Cause ${i + 1}: ${body.causes[i].code}: ${body.causes[i].message}`, { context });
      description += `\nCause ${i + 1}: ${body.causes[i].message} Code:${body.causes[i].code};`;
    }
  }
  const errorInfo = {
    responseStatusCode: response.status && response.statusText,
    responseStatusMessage: response.statusText,
    bodyMessage: body && body.message,
    bodyErrorDetails: body && body.errorDetails,
    bodyErrorMore: body && body.length && body.length > 0 && body[0] && body[0].error && body[0].error.message,
  } as const;
  let errorDetailsInfo = "";
  try {
    errorDetailsInfo = JSON.stringify(errorInfo);
  } catch (err) {
    // ignore
  }
  let errorDetails =
    errorInfo.bodyErrorMore ||
    errorInfo.bodyMessage ||
    errorInfo.responseStatusMessage ||
    errorInfo.responseStatusCode ||
    "";
  errorDetails += description;
  const callerError = caller ? ` Error in ${caller}.` : "";
  const error = new DataFLowRequestError(
    `Failed to fetch the data. ${callerError} ${errorDetails} `,
    body && body.errorDetails,
    body,
    response.status
  );
  logError([error, errorDetailsInfo, body], { context });
  return error;
}

export async function makeRequest(context: IRequestContext, options: IDataFlowOptions, caller?: string) {
  if (!options.method) {
    options.method = METHODS.GET;
  }
  const resp = requestDataFlow(caller || "", getDiProxyServiceUrl(context, options.url), context, options);
  return resp;
}

export async function writeToAuditLogService(mAuditEvent: IAuditLogEvent, context: IRequestContext): Promise<any> {
  try {
    await AuditLogService.logAuditEvent(mAuditEvent, context);
  } catch (err) {
    log(LogLevel.Info, `AuditLogService.logAuditEvent ${err}`, { context });
  }
}

/**
 * Fetches the DI runtime engine version details from the di-proxy
 * @param context Request context
 * @returns Version details of DI runtime engine
 */
export async function getDisVersion(context: IRequestContext): Promise<IDIVersionDetails> {
  const requestTimeout = parseInt(process.env.SERVICE_TO_SERVICE_TIMEOUT!, 10) * 1000;
  const options = getDIProxyRequestOptions(context, {
    acceptedStatusCodes: [Status.OK],
    callTimeout: requestTimeout,
    circuitBreakerContext,
  });

  const response = await httpClient.call({
    url: getDiProxyServiceUrl(context, `/service/v2/version`),
    opts: options,
  });
  const versionDetails = response.body as IDIVersionDetails;
  if (versionDetails?.version && versionDetails.distributedRuntimeVersion) {
    versionDetails.distributedRuntimeVersion = `${versionDetails.version} (${versionDetails.distributedRuntimeVersion})`;
  }
  return versionDetails;
}

/**
 * Gets the flows from repository
 * @param context Request context
 * @param spaceName Space name
 * @param optionalParams Optional params
 * @returns Flows from repository
 */
export async function getAllFlowsFromRepository(
  context: IRequestContext,
  spaceName: string,
  optionalParams?: {
    name?: string;
    includeReplicationFlows?: boolean;
    includeTransformationFlows?: boolean;
  }
) {
  const startTime = new Date();
  const params = {
    folderNames: spaceName,
    kinds: [ObjectKind.space],
  };
  let flowsObject = {};
  try {
    const spaceObjects = await RepositoryObjectClient.getObject(context, params);
    const spaceUuid = spaceObjects[0]?.folderId;

    const oParams: IGetObjectParameters = {
      folderIds: spaceUuid,
      kinds: [FLOW_KINDS.DATA_FLOW],
      inSpaceManagement: true,
      details: ["id", "name", "creation_date", "business_name"],
    };
    if (optionalParams?.name) {
      oParams.qualifiedNames = optionalParams.name;
    }
    if (optionalParams?.includeReplicationFlows && Array.isArray(oParams.kinds)) {
      oParams.kinds.push(FLOW_KINDS.REPLICATION_FLOW);
    }
    if (optionalParams?.includeTransformationFlows && Array.isArray(oParams.kinds)) {
      oParams.kinds.push(FLOW_KINDS.TRANSFORMATION_FLOW);
    }
    const results = await RepositoryObjectClient.getObject(context, oParams);
    const validParams = new GetObjectParameters(context, oParams);
    flowsObject = results.reduce((flowsFromRepo: any, ro: RepositoryObject) => {
      flowsFromRepo[spaceName + "." + ro.name] = ro.getResult(validParams.details);
      return flowsFromRepo;
    }, {});
    logPerformance(
      startTime,
      `[FlowMonitor] Get all flows from repository records - ${Object.keys(flowsObject)?.length}`,
      { context }
    );
  } catch (error) {
    logError(`Error fetching flows from repository ${error}`, { context });
  }
  return flowsObject;
}

/**
 * Throw error in calling function if a promise is rejected
 * @param {Array<{status: string; value: any}>} promises array of objects having promise status and value
 * @param {Array<string | undefined>} errorMsgs array of error messages for each promise
 */
export function detectErrors(
  promises: Array<{ status: string; value: any }>,
  errorMsgs: Array<string | undefined>,
  showError = false
) {
  promises.forEach((promise, idx: number) => {
    if (promise.status === "rejected") {
      if (errorMsgs[idx]) {
        let errorMsg = errorMsgs[idx];
        if (showError) {
          errorMsg += `\nError ${promise.value}`;
        }
        throw new Error(errorMsg);
      } else {
        throw promise.value;
      }
    }
  });
}

export function getTaskDetailsForLogId(
  context: IRequestContext,
  startTime: Date,
  spaceName: string,
  logId: number
): Promise<any> {
  const taskLogsApi = new TaskLogsApi(context, spaceName);
  return taskLogsApi
    .getMessagesForLogId(logId)
    .then((response) => {
      logPerformance(startTime, `[DataflowMonitor] Get task logs`, { context });
      return response;
    })
    .catch((error) => {
      logError(`Error fetching task messages for space ${spaceName} Error ${error}`, { context });
      throw error;
    });
}

/**
 *
 * @returns The number of seconds current timezone is ahead
 */
export function getOffsetSeconds(): number {
  return new Date().getTimezoneOffset() * 60;
}

/**
 * gets Dataflow runtimeinstance details for given handle using graphquery endpoint,
 * which returns for historic runs as well
 * @param requestContext RequestContext
 * @param handleIds list of dataflow runtime unique identifiers
 */
export async function getDataflowRuntimeDetailUsingQuery(
  requestContext: IRequestContext,
  handleIds: string[],
  timeout?: number
): Promise<any[]> {
  const filterHandleIds = handleIds.filter((id) => id);
  if (filterHandleIds.length === 0) {
    logInfo("No handle ids provided for getDataflowRuntimeDetailsUsingQuery", { context: requestContext });
    return [];
  }
  const filters: any = ["or"];
  if (filterHandleIds.length && filterHandleIds.length > 1) {
    filterHandleIds.forEach((handle) => {
      const filter = ["equal", "handle", handle];
      filters.push(filter);
    });
  }
  const body = {
    limit: RUNTIME_INSTANCES_DEFAULT_LIMIT,
    detailLevel: GRAPHS_QUERY_DETAIL_LEVEL.GRAPH,
    sortSpecs: [["submitted", "desc"]],
    filter: filters.length > 1 ? filters : ["equal", "handle", filterHandleIds[0]],
  };
  const response = await exports.makeRequest(
    requestContext,
    { body, url: modeler + graphQuery, method: METHODS.POST, timeout },
    "getDataflowRuntimeDetailUsingQuery"
  );

  if (response && response.length <= 0) {
    logWarning(`No dataflow runtime details found for handles ${filterHandleIds.join(", ")}`, {
      context: requestContext,
    });
  }
  return response;
}

/**
 * gets Dataflow runtimeinstance details for given handle
 * @param {IRequestContext} requestContext RequestContext
 * @param {string} handle dataflow runtime unique identifier
 * @returns {Promise<any>} dataflow runtime details
 */
export async function getDataflowRuntimeDetail(requestContext: IRequestContext, handle: string): Promise<any> {
  const url = `${modeler}${graphPath}/${handle}`;
  const response = await exports.makeRequest(requestContext, { url }, "getDataflowRuntimeDetail");
  return response;
}

/**
 * If the URL starts with any of the strings in the validURL array, return true.
 * @param {string} url - The URL of the request.
 * @returns a boolean value.
 */
function showCausesInErrorResponse(url: string): boolean {
  // Add new url if you want to add causes in error message.
  const validURL = [
    "/datasources/getchildren",
    "/datasources/searchchildren",
    "/dataflow/connections",
    "/dataflow/datasets",
    "/dataflow/metadata",
  ];
  return validURL.some((checkURL) => url.startsWith(checkURL));
}

/**
 * Retrieves a dataflow object from the repository.
 * @param {IRequestContext} context Request Context
 * @param {string} dataflowname name of the dataflow object that needs to be extracted from the repository.
 * @param {string} spaceUuid space guuid of the space within which the dataflow object exists.
 * @returns {Promise<any>} the retrieved dataflow object from the repository.
 */
export async function getDataflow(context: IRequestContext, dataflowname: string, spaceUuid: string): Promise<any> {
  const oParams = {
    folderIds: spaceUuid,
    filters: "name:" + dataflowname,
    kinds: "sap.dis.dataflow",
    inSpaceManagement: true,
    requestAllDetails: true,
  };
  const validParams = new GetObjectParameters(context, oParams);
  const results = await RepositoryObjectClient.getObject(context, oParams);
  let response: any;
  // transform results
  if (results.length > 0) {
    const aResults: any = results.map((ro: RepositoryObject) => ro.getResult(validParams.details));
    response = aResults[0];
  }
  return response;
}

/**
 * Gets the list of flows from runtime table
 * @param context Request context
 * @param spaceName Space name
 * @param optionalParams Optional params
 * @returns list of flows from runtime table
 */
export async function getDeployedFlows(
  context: IRequestContext,
  spaceName: string,
  optionalParams: { listReplicationFlows: boolean; listTransformationFlows: boolean }
) {
  const startTime = new Date();
  const { listReplicationFlows, listTransformationFlows } = optionalParams;
  const dataflowRecord: IDeployedMetadata = {
    REPOSITORY_OBJECT_TYPE: RepositoryObjectType.DWC_DATAFLOW,
    NAME: "",
  };
  const dfDeployedMetadata = new DeployedMetadata(context, spaceName, dataflowRecord);
  const objectTypes = [RepositoryObjectType.DWC_DATAFLOW];

  if (listReplicationFlows) {
    objectTypes.push(RepositoryObjectType.DWC_REPLICATIONFLOW);
  }

  if (listTransformationFlows) {
    objectTypes.push(RepositoryObjectType.DWC_TRANSFORMATIONFLOW);
  }

  let deployedFlows: IDeployedMetadata[] = [];
  try {
    await deploySpaceTecTables(["DEPLOYED_METADATA"], spaceName, context);
    deployedFlows = await dfDeployedMetadata.getDeployedArtifactListByTypes(objectTypes);
    logPerformance(startTime, `[FlowMonitor] Get all deployed flows records - ${deployedFlows.length}`, {
      context,
    });
  } catch (error) {
    // Added for handling hana admission control error
    if (error?.code === 616 && error?.name === "HanaError") {
      throw error;
    }
    logError(`Error fetching deployed flows ${error}`, { context });
    throw new Error("Error fetching deployed flows");
  }
  return deployedFlows;
}

// https://stackoverflow.com/questions/35753797/stub-module-function-called-from-the-same-module
exports.makeRequest = makeRequest;
