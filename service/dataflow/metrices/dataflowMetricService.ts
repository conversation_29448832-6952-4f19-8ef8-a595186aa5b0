/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { IGetObjectParameters } from "@sap/deepsea-types/dist/getObjectParameters";
import { IRepositoryObject } from "@sap/deepsea-types/dist/object/repoObject";
import telemetry from "@sap/dwc-telemetry";
import moment from "moment";
import { getLogger } from "../../logger";
import { RepositoryObjectClient } from "../../repository/client/repositoryObjectClient";
import { IRequestContext } from "../../repository/security/common/common";
import { TaskLogsApi } from "../../task/logger/controllers/TaskLogsApi";
import { Activity, ApplicationId } from "../../task/models";
import { CONNECTION_MAPPINGS, FLOW_KINDS } from "../constants";
import { DataFlowUsageMetrics, IDataFlowConnectionType } from "./models";

const { logInfo, logError } = getLogger("DataflowMetricService");

/**
 * Log metric information of dataflow
 */
export class DataflowMetricService {
  private readonly dateTimeFormat: string = "YYYY-MM-DD HH:mm:ss.SSSSSSSSS UTC";

  constructor(private readonly context: IRequestContext, private readonly _spaceId: string) {}

  /**
   * Log connection details using dataflow metrics
   */
  public async logDataFlowMetrics(): Promise<void> {
    const context: IRequestContext = this.context;
    const taskLogsApi = new TaskLogsApi(context, this._spaceId);
    const taskLogHeaders = await taskLogsApi.getLogHeaders(
      { applicationId: ApplicationId.DATA_FLOWS, activity: Activity.DAILY_METRICS },
      true
    );
    let isInitialRun = true;
    if (taskLogHeaders.logs.length > 0) {
      isInitialRun = false;
    }
    const todayDate = moment().utc();
    const yesterdayDate = todayDate.clone().subtract(24, "hours");

    const formattedTodayDate = todayDate.format(this.dateTimeFormat);
    const formattedYesterdayDate = yesterdayDate.format(this.dateTimeFormat);

    const connectionTypes: IDataFlowConnectionType[] = await this.getConnectionTypes(
      context,
      formattedYesterdayDate,
      isInitialRun
    );

    if (connectionTypes?.length > 0) {
      logInfo(
        `Logging connection details using ${DataFlowUsageMetrics.DATA_FLOW_EXECUTION_CONNECTION_OVERVIEW}, metrics`,
        {
          context,
        }
      );
      // Log data flow data to Dynatrace via open telemetry
      const connectionDetailsHistogram = telemetry.metrics
        .getMeter()
        .createHistogram(DataFlowUsageMetrics.DATA_FLOW_EXECUTION_CONNECTION_OVERVIEW);

      connectionTypes.forEach(({ connectionType }) => {
        connectionDetailsHistogram.record(1, {
          tenantId: context.tenantId || "unknown",
          connectionType,
        });
      });
    } else {
      logInfo(`No data flow deployment happened from ${formattedYesterdayDate} to ${formattedTodayDate}`, {
        context: this.context,
      });
    }
  }

  /**
   * Gets connection details
   * @param {IRequestContext} context
   * @param {string} yesterdayDate
   * @param {boolean} isInitialRun
   * @returns {Promise<IDataFlowConnectionType[]>}
   */
  public async getConnectionTypes(
    context: IRequestContext,
    yesterdayDate: string,
    isInitialRun: boolean
  ): Promise<IDataFlowConnectionType[]> {
    let dfConnectionTypes: IDataFlowConnectionType[] = [];
    try {
      const oParams: IGetObjectParameters = {
        kinds: [FLOW_KINDS.DATA_FLOW],
        inSpaceManagement: true,
        details: [
          "modification_date",
          "#dfSourceOperatorSFTP",
          "#dfSourceOperatorRedshift",
          "#dfSourceOperatorS3",
          "#dfSourceOperatorCDI",
          "#dfSourceOperatorOData",
          "#dfSourceOperatorGCS",
          "#dfSourceOperatorHDFS",
          "#dfSourceOperatorAzureDL1",
          "#dfSourceOperatorAzureDL2",
          "#dfSourceOperatorAzureSQL",
          "#dfSourceOperatorMSSQL",
          "#dfSourceOperatorOC",
          "#dfSourceOperatorOracle",
          "#dfSourceOperatorABAP",
        ],
      };
      const results = await RepositoryObjectClient.getObject(context, oParams);
      dfConnectionTypes = this.extractConnectionTypes(results, yesterdayDate, isInitialRun);
    } catch (error) {
      logError(`Error fetching flows from repository ${error}`, { context });
    }
    return dfConnectionTypes;
  }

  /**
   * Extract connection types
   * @param {IRepositoryObject[]} repoObject
   * @param {string} yesterdayDate
   * @param {boolean} isInitialRun
   * @returns {Promise<IDataFlowConnectionType[]>}
   */
  public extractConnectionTypes(
    repoObject: IRepositoryObject[],
    yesterdayDate: string,
    isInitialRun: boolean
  ): IDataFlowConnectionType[] {
    const connectionMappings = CONNECTION_MAPPINGS;
    const result: IDataFlowConnectionType[] = [];
    let dataFlowObjects = repoObject;
    if (!isInitialRun) {
      dataFlowObjects = repoObject.filter((item) => item.modificationDate && item.modificationDate > yesterdayDate);
    }
    dataFlowObjects.forEach((item) => {
      Object.entries(connectionMappings).forEach(([key, connectionType]) => {
        if (item?.properties[key] === "1") {
          result.push({ connectionType });
        }
      });
    });

    return result;
  }
}
