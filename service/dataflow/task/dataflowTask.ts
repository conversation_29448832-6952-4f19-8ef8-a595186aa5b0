/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { promiseMap } from "@sap/dwc-promise-utils";
import { FeatureFlagProvider } from "../../featureflags/FeatureFlagProvider";
import { getLogger } from "../../logger";
import { AuthType, IRequestContext, Activity as PermissionActivity } from "../../repository/security/common/common";
import { CustomerHanaRuntimeData } from "../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { AuthorizationUtils } from "../../reuseComponents/utility/AuthorizationUtils";
import { ITaskLogMessage, ITaskLogger, Severity, Status } from "../../task/logger/models";
import { Activity, ApplicationId, IExecutable } from "../../task/models";
import { TaskExecuteResponse } from "../../task/models/TaskExecuteResponse";
import { Parameters } from "../../task/orchestrator/models/Parameters";
import { LockIdentifier, <PERSON>K<PERSON>, LockedTask } from "../../task/orchestrator/services/tasklock";
import { OverwriteResponse } from "../../task/orchestrator/services/tasklock/OverwriteResponse";
import { delay } from "../commonUtil";
import { CONCURRENT_PROMISES, RUN_GRAPH_TIMEOUT } from "../constants";
import { DataFlowMetadata } from "../metadata/metadata";
import { DataFlowRunTime } from "../runTime/runTime";
import { DataflowLockResolver } from "./dataflowLockResolver";
import { IDataFlowTaskLogMessage, setHandleId } from "./util";

const { logInfo, logError, logWarning } = getLogger("DataflowTask");

/**
 * DataflowTask implements a IExecutable and when executed,
 * it fetches a dataflow from the repository and executes it.
 */
export class DataflowTask implements IExecutable {
  static readonly MESSAGE_BUNDLE_ID = "sap.dwc.dataflowTaskLogs";
  private readonly requestContext: IRequestContext;
  private readonly spaceId: string;
  private readonly dataflowName: string;
  private readonly parameters: Parameters;
  private featureFlags: any;
  private currHandle: string;
  private authorizedCache: boolean | undefined = undefined;

  constructor(
    requestContext: IRequestContext,
    spaceId: string,
    objectId: string,
    activity: Activity,
    parameters: Parameters
  ) {
    this.requestContext = requestContext;
    this.spaceId = spaceId;
    this.dataflowName = objectId;
    this.parameters = parameters || {};
  }

  /**
   * Caches the feature flags
   */
  public async getFeatureFlags(): Promise<void> {
    if (this.featureFlags === undefined) {
      try {
        const ff = await FeatureFlagProvider.getFeatureFlags(this.requestContext);
        if (ff) {
          this.featureFlags = ff;
        } else {
          this.featureFlags = {};
        }
      } catch (error) {
        this.featureFlags = {};
      }
    }
  }

  /**
   * Authorization check if the corresponding execution is allowed.
   */
  async isAuthorized(taskLogger: ITaskLogger, isDesignTime = false): Promise<boolean> {
    try {
      const isDirectExecute = this.parameters?.tf?.isDirect;
      const dIPermissionActivity = isDirectExecute ? PermissionActivity.update : PermissionActivity.execute;
      this.authorizedCache = await AuthorizationUtils.isAuthorized(
        this.requestContext,
        AuthType.DWC_DATAINTEGRATION,
        dIPermissionActivity,
        this.spaceId
      );

      if (!this.authorizedCache) {
        await this.taskLogError(taskLogger, { text: "Missing authorizations for Dataflow" });
      }
    } catch (err) {
      await this.taskLogError(taskLogger, err.message);
      return false;
    }
    return this.authorizedCache;
  }

  /**
   * Executes a dataflow from the repository.
   *
   * @param taskLogger The logger to write information. The logger information
   * can be viewed in an administration UI.
   */
  async execute(taskLogger: ITaskLogger, isRetry?: boolean): Promise<TaskExecuteResponse> {
    if (this.featureFlags === undefined) {
      await this.getFeatureFlags();
    }
    const isRestartFFActive = this.featureFlags.DWCO_DF_WRAPPER_RESTART;

    try {
      if (!(await this.isAuthorized(taskLogger))) {
        return Status.FAILED;
      }
      await this.taskLogMessage(taskLogger, { text: `Starting Run` });
      const customerHanaRuntimeData = new CustomerHanaRuntimeData(this.requestContext);
      const isLocked = await customerHanaRuntimeData.isSpaceLocked(this.spaceId);
      // dataflow execution.
      if (!isLocked) {
        const result = await DataFlowRunTime.executeDataflow(
          this.requestContext,
          this.spaceId,
          this.dataflowName,
          taskLogger,
          {
            parameters: this.parameters.parameters,
            traceLevel: this.parameters.tracelevel,
            configurationSubstitutions: this.parameters.configurationSubstitutions,
          }
        );
        if (!result || !result.handle) {
          await this.taskLogError(taskLogger, { text: "Dataflow not found." });
          return Status.FAILED;
        }
        await this.taskLogMessage(taskLogger, { text: `Running with handle ${result.handle}` });
        this.currHandle = result.handle;

        await setHandleId(this.requestContext, taskLogger, this.dataflowName, this.currHandle);

        logWarning(`[Dataflow] ${this.dataflowName} executed with handle ${this.currHandle}`, {
          context: this.requestContext,
        });

        // Save auto restart ID
        const restartId = result.autoRestartInfo && result.autoRestartInfo.id;
        const updatedParams = { restartId, addedRestartConfig: result.addedRestartConfig };
        const logId = taskLogger.getLogId();
        await taskLogger.updateTaskParameters(logId, updatedParams);

        if (result.dataFlowInputParameters) {
          const inputParamList = Object.keys(result.dataFlowInputParameters);
          await promiseMap(
            inputParamList,
            (param: string) =>
              this.taskLogMessage(taskLogger, {
                text: `Running with input parameter ${param} ${result.dataFlowInputParameters[param]}`,
              }),
            {
              concurrency: CONCURRENT_PROMISES,
            }
          );
        }

        return Status.RUNNING;
      } else {
        await this.taskLogError(taskLogger, { text: "The space is locked." });
        return Status.FAILED;
      }
    } catch (err) {
      // Specific handling for Customer issue added under feature flag
      // This logic will check for 502 issue in the error and retrigger dataflow
      if (isRestartFFActive && !isRetry && (err.code === 502 || err.message?.includes("502"))) {
        await delay(30000);
        await this.taskLogMessage(taskLogger, { text: `Retry run for 502 issue` });
        return this.execute(taskLogger, true);
      }
      // We try to write to the TaskLog again with to publish the error information.
      // In case of db/connection error this might fail though.
      await this.taskLogError(taskLogger, { text: err.message });
      return Status.FAILED;
    }
  }

  /**
   * logging function to log execution messages.
   * @param taskLogger
   * @param status current execution status with value starting|executing|completed
   */
  private async taskLogMessage(taskLogger: ITaskLogger, message: IDataFlowTaskLogMessage) {
    logInfo(`${ApplicationId.DATA_FLOWS}.${Activity.EXECUTE} for the dataflow ${this.dataflowName} ${message.text}`, {
      context: this.requestContext,
    });
    try {
      const log = this.getDataFlowTaskLogMessage(Severity.INFO, "dataflowExecutionStatus", message);
      await taskLogger.logMessage([log]);
    } catch (taskLoggerError) {
      logError(["Could not write error log message to TaskLogger", taskLoggerError], { context: this.requestContext });
    }
  }

  /**
   * logging function to log execution failure messages.
   * @param taskLogger
   * @param message The execution failure message
   * @param details (optional)
   */
  private async taskLogError(taskLogger: ITaskLogger, message: IDataFlowTaskLogMessage) {
    logError(`${ApplicationId.DATA_FLOWS}.${Activity.EXECUTE} failed: ${message.text}, ${message.details}`, {
      context: this.requestContext,
    });
    try {
      const log = this.getDataFlowTaskLogMessage(Severity.ERROR, "dataflowExecutionFailed", message);
      await taskLogger.logMessage([log]);
    } catch (taskLoggerError) {
      logError(["Could not write error log message to TaskLogger", taskLoggerError], { context: this.requestContext });
    }
  }

  private getDataFlowTaskLogMessage(
    severity: Severity,
    messageBundleKey: string,
    message: IDataFlowTaskLogMessage
  ): ITaskLogMessage {
    const log: ITaskLogMessage = {
      severity,
      messageBundleId: DataflowTask.MESSAGE_BUNDLE_ID,
      messageBundleKey,
      parameterValues: [this.dataflowName],
      details: message.details,
      text: message.text,
    };

    if (message.timestamp) {
      log.timestamp = new Date(message.timestamp * 1000);
    }

    return log;
  }

  /**
   * DataFlow to use Parallel Execution Prevention.
   */
  getLockIdentifier(): LockIdentifier | null {
    return {
      lockKey: LockKey.EXECUTE,
      applicationId: ApplicationId.DATA_FLOWS,
      spaceId: this.spaceId,
      objectId: this.dataflowName,
    } as LockIdentifier;
  }

  /**
   * DataFlow to overwrite locks.
   */
  async overwriteLock(lock: LockedTask): Promise<OverwriteResponse> {
    const response: OverwriteResponse = {
      takeover: false,
    };

    const locksResolver = new DataflowLockResolver(this.requestContext);
    try {
      if (lock.externalInstanceId) {
        const [locksResolverResponse] = await locksResolver.resolveLocks([lock]);
        if (locksResolverResponse.status !== Status.RUNNING) {
          response.takeover = true;
          response.status = locksResolverResponse.status;
          response.endTime = locksResolverResponse.endTime;
          if (locksResolverResponse.subStatusCode) {
            response.subStatusCode = locksResolverResponse.subStatusCode;
          }
        }
      } else if (Date.now() - new Date(lock.creationTime || 0).getTime() > RUN_GRAPH_TIMEOUT) {
        response.takeover = true;
        response.status = Status.FAILED;
        response.endTime = new Date(Date.now());
      }
    } catch (error) {
      logError(error, { context: this.requestContext });
      // allow take over if we are getting 404 error or No dataflow runtime instances found message
      if (error.statusCode === 404 || error.message?.includes("No dataflow runtime instances found")) {
        response.takeover = true;
        response.status = Status.FAILED;
        response.endTime = new Date(Date.now());
      }
    }

    return response;
  }

  /**
   * Checks whether the object being executed by the dataflow is valid
   * An object is valid if it exists and of the same dataflow type
   */

  async isValidObject(): Promise<boolean> {
    try {
      const dataFlow = await DataFlowMetadata.getDeployedJSON(this.requestContext, this.spaceId, this.dataflowName);

      const isValid = dataFlow ? true : false;

      if (!isValid) {
        logWarning(`data flow ${this.spaceId}.${this.dataflowName} is not valid`, {
          context: this.requestContext,
        });
      }

      return isValid;
    } catch (error) {
      logError([`Error while checking if data flow ${this.spaceId}.${this.dataflowName} is valid`, error], {
        context: this.requestContext,
      });

      return false;
    }
  }
}
