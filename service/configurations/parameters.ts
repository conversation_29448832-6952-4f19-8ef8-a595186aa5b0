/** @format */

import { TimeUnit } from "@sap/deepsea-utils";

export const parameters = {
  DWC_TEST_PARAM_DEPLOYMENT_AND_TENANT: {
    defaultValue: "no value can be retrieved from the remote system, this is the default fallback",
    caching: {
      timeoutInMs: TimeUnit.MINUTES.toMillis(1),
      type: "IN_MEMORY" as const,
    },
  },
  DWC_TEST_PARAM_DEPLOYMENT_AND_TENANT_REDIS: {
    defaultValue: "no value can be retrieved from the remote system, this is the default fallback",
    caching: {
      timeoutInMs: TimeUnit.MINUTES.toMillis(1),
      type: "REDIS" as const,
    },
  },
  DWC_TENANT_CONFIGURATION_FEATURE_AVAILABILITY: {
    defaultValue: '{"hasDataLake": false, "hasBwBridge": false, hasObjectStore: false}',
    caching: {
      timeoutInMs: TimeUnit.MINUTES.toMillis(30),
      type: "IN_MEMORY" as const,
    },
  },
  SDP_MIGRATION_STATE: {
    defaultValue: "unavailable",
    // no cache
  },
  DWC_METERING_REPORT_ENABLEMENT: {
    defaultValue:
      '{"PREMIUM_OUTBOUND": true, "CATALOG_STORAGE": true, "DI_EXTRACTION_HOURS": true, "ELASTIC_COMPUTE_NODE": true, "FOUNDATION_BLOCK": true}',
    caching: {
      timeoutInMs: TimeUnit.MINUTES.toMillis(50),
      type: "IN_MEMORY" as const,
    },
  },
  DWC_METERING_COLLECTOR_ENABLEMENT: {
    defaultValue:
      '{"PREMIUM_OUTBOUND": true, "CATALOG_STORAGE": true, "DI_EXTRACTION_HOURS": true, "ELASTIC_COMPUTE_NODE": true, "FOUNDATION_BLOCK": true}',
    caching: {
      timeoutInMs: TimeUnit.MINUTES.toMillis(50),
      type: "IN_MEMORY" as const,
    },
  },
  DWC_GUIDED_EXPERIENCE_TENANT: {
    defaultValue: '{"type": "None"}',
    caching: {
      timeoutInMins: TimeUnit.MINUTES.toMillis(30),
      type: "REDIS" as const,
    },
  },
  DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT: {
    defaultValue: '{"isAllowed": "OFF"}',
    caching: {
      timeoutInMs: TimeUnit.MINUTES.toMillis(10),
      type: "IN_MEMORY" as const,
    },
  },
  BDC_SAP_CONTENT_DEV_REPO_BRANCH: {
    defaultValue: '{"branch": "prodProd"}',
    caching: {
      timeoutInMs: TimeUnit.MINUTES.toMillis(10),
      type: "IN_MEMORY" as const,
    },
  },
  DWC_SAC_INTEGRATION_SEAMLESS_PLANNING_ENABLED: {
    defaultValue: "false",
    caching: {
      timeoutInMs: TimeUnit.MINUTES.toMillis(10),
      type: "IN_MEMORY" as const,
    },
  },
};
