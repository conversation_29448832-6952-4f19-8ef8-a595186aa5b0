/** Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 * @format
 */
import {
  Activity,
  convertStringToArray,
  DeleteConnectionAction,
  IValidateDeleteOptions,
  ObjectKind,
  ValidationError,
  ValidationMessageCode,
  ValidationMessageSeverity,
} from "@sap/deepsea-types";
import { GetObjectParameters, RepositoryObject } from "@sap/deepsea-utils";
import { ResolvedResponse } from "@sap/dwc-http-client";
import { promiseMap } from "@sap/dwc-promise-utils";
import Status, { StatusCodes } from "http-status-codes";
import _ from "lodash";
import { inspect } from "util";
import {
  CCMAndRemoteSourceDeploymentStatus,
  CCMAndRemoteSourceDeploymentStatusResponse,
} from "../../shared/connections/connectionSharing/RemoteSourceDeploymentStatus";
import { AuditStatus } from "../auditing";
import {
  grantConsumerRoleToRemote,
  grantSpaceSupportRoleToRemote,
  grantSpaceUserToRemote,
} from "../datasources/remote";
import { FeatureFlagProvider, IFeatureFlagsMap } from "../featureflags/FeatureFlagProvider";
import { isCanary } from "../lib/node";
import { getLogger, LogLevel } from "../logger/index";
import { getIpAllowlistJson } from "../provisioning/AllowListIP/allowListDataProcessing";
import { RepositoryObjectClient } from "../repository/client/repositoryObjectClient";
import { ERR_SQL_INV_DATA_SOURCE } from "../repository/connection/errors";
import { isInsightAppOnboarding } from "../repository/contentnetwork/util";
import { AuthType, IRequestContext } from "../repository/security/common/common";
import { ExternalCallCategory } from "../repository/security/common/externalCallTypes";
import { RequestContext } from "../repository/security/requestContext";
import { getSpaces, getSpaceUuidFromName } from "../repository/spaces";
import { getSpaceNameFromUuid } from "../routes";
import { logWarning } from "../routes/dac/util";
import { CodedError } from "../server/errorResponse";
import { BWProcessChainDB } from "../task/nonRepository/bwProcessChain/db/BWProcessChainDB";
import { CCMClient } from "./ccm/CCMClient";
import { CCMCompositeId, CCMEndpoints, CCMReplicationStatus } from "./ccm/CCMConstants";
import { CCMConnectionsError } from "./ccm/errors/CCMConnectionsError";
import { CCMConnectionsErrorCode } from "./ccm/errors/CCMErrorCodes";
import { getCCMCompositeId, upsertConnectionToCCM } from "./ccm/Utils";
import { attachTLSCertificateHDLF, getCertificates, ICertificateInfo } from "./certificates/certificate";
import { connectionMetricWrapper } from "./connectionMetricWrapper";
import { ConnectionSharingUtils } from "./connectionSharing/ConnectionSharingUtils";
import { HanaCredentialClient } from "./datasources/hanaCredentialClient";
import * as SdiManager from "./datasources/sdi";
import { SdiMetadataImport } from "./datasources/SdiMetadataImport";
import { RemoteSourceObject } from "./datasources/types";
import { HanaSCCConfigClient } from "./HanaSCCConfigClient";
import { IBw4HanaDbInfo } from "./metadataImport";
import { BW4HanaDbInfoError, BW4HanaDbInfoErrorCodes } from "./metadataImport/errors/BW4HanaDbInfoError";
import { ConnectionFactory } from "./models/ConnectionFactory";
import { SIGNAVIO } from "./models/SIGNAVIO";
import { getOCNConfiguration, getOCNInstances, IOCNConfiguration, IOCNInstance } from "./openConnectors/openConnectors";
import { PartnerTileConfiguration } from "./partner/partnerTileConfiguration";
import { generateSpaceConnectionPayload, getUCLCredentialsForCCMReplication } from "./ucl/uclConnectionUtils";
import { auditLogConnectionDeleted, auditLogConnectionUpserted } from "./utils/audit";
import * as ConnectionUtils from "./utils/connectionUtils";
import { computeGetConnectionFilters, filterForSDIAdapterName } from "./utils/connectionUtils";
import {
  AuthTypes,
  ConnectionCapabilities,
  ConnectionTypeData,
  CredentialModes,
  DataAccessLevel,
  DataProvisioningOptions,
  DWCConnectionConfiguration,
  DWCConnectionProperties,
  DWCOAuth2Props,
  httpKeyDeniedList,
  IGetObjectParams,
  IMetaschema,
  IRemoteConnection,
  IRemoteConnectionWithConfig,
  OAuthGrantType,
  RealTimeReplicationStatus,
  RemoteSourceAdapters,
  RepositoryPackageNone,
  SourceLocationCategories,
  TypeIds,
} from "./utils/Constants";
import { handleUpsertRemoteSourceErrors } from "./utils/errors/handleErrors";
import { ALL_STATUS_CODES, makeDisRequest, makeLcsRequest } from "./utils/httpRequestUtils";
import { DIS_IP_ADDRESS_URL } from "./utils/url";
const { log, logError, logVerbose, logInfo, logPerformance } = getLogger("ConnectionsRemotes");

// Maximum allowed concurrency when executing mapping functions that are promise based
const DEFAULT_CONCURRENCY = 30;

/**
 * Fetches connection objects from the repository DB based on the given query.
 * An example query = {
 *  details: ["typeId"], // the properties that will be part of the returned objects.
 *  filters: "name:${ConnectionTechName}", // filter condition.
 *  space_ids: "${TheSpaceId}", // the space the connections belong to.
 *  inSpaceManagement: true // Allows access to connections of foreign spaces. Should only be used if really required.
 *  };
 *
 * Properties that are valid for filtering with the "filters" query property:
 * (Other properties are not supported and filtering should be done on caller side)
 *  typeId
 *  businessName
 *  description
 *  remoteSourceName
 *  capabilityHanaSdi
 *  capabilityModelTransfer
 *  capabilityPartnerSchema
 *
 * The query parameter "inSpaceManagement" is optional and if passed as "true" it allows space managers (with privilege space.assign)
 * to access connection objects which are part of a space the current user is not a member of.
 * Connections which are not accessible are filtered out for the result of this method.
 *
 * The method performs on the fly migration to transform outdated connection structures/values to the updated format.
 *
 * - Since DW00-4681 CCM connections can have multiple endpoints and so multiple credential ids.
 * It is not intended to be used to generally access the credential section of a CCM connection.
 *
 * - With DW00-6881 the properties:
 * capabilityReplicationflowSource
 * capabilityReplicationflowTarget
 * capabilityDataflowSource
 * capabilityDataflowTarget
 * are not supported for filtering via the "filters" property as those are computed.
 * Filtering needs to be done on the caller side.
 *
 * Call to customer HANA is made only if the remote source runtime properties like adapter Name, location, realtimeReplicationStatus etc.
 * are requested in query.details. Requesting one property will return all runtime properties.
 *
 * @param context the request context
 * @param query the query declaring which properties of which connections should be fetched.
 * @param allowForDataIntegrationMonitor allow to read connection with DWC_DATAINTEGRATION read bit temporary workaround for DW101-26036
 *
 * @returns an array of connection objects, potentially empty.
 */
export const getConnections = async function (
  context: IRequestContext,
  query: any,
  allowAccess = false
): Promise<IRemoteConnection[]> {
  if (isCanary()) {
    // DW101-80227 increased logs for investigation the random issue of automation tests
    logInfo(`getRemoteApi query ${JSON.stringify(query)}`, { context });
  }
  let spaceIds = query.folder_ids || query.space_ids;
  if (!allowAccess) {
    // In some cases privileges don't need to be checked
    const isSDP = await FeatureFlagProvider.isFeatureActive(context, "DWC_DUMMY_SPACE_PERMISSIONS");
    if (!isSDP) {
      // Check global RemoteConnection.Read privilege in non-SDP mode
      if (!context.hasPrivilegeOnType(AuthType.DWC_REMOTECONNECTION, Activity.read)) {
        throw new CodedError(
          "insufficientReadConnectionPrivilege",
          "Insufficient privilege: User is not allowed to read connections.",
          StatusCodes.FORBIDDEN
        );
      }
    } else {
      // Check scoped RemoteConnection.Read privilege in SDP-mode and only return spaces for which the user has this privilege.
      // If no space fulfills this condition, the method throws an error.
      spaceIds = await checkScopedReadPrivilege(context, spaceIds);
      if (isCanary()) {
        // DW101-80227 increased logs for investigation the random issue of automation tests
        logInfo(`getRemoteApi spaceIds after checking read privilege: ${spaceIds?.join(",")}`, { context });
      }
    }
  }

  logVerbose("Starting getConnections", { context });
  const startTime = new Date();

  // When id/space_id is missing in details, the CRUD component fails to cache the objects correctly
  const requiredDetails = ["id", "space_id"];
  const requestedDetails = convertStringToArray(query.details || query.detail);
  // Setting default properties to be fetched from the Repository if no details are requested.
  const defaultDetails = ["typeId", "name", "description", "businessName"];

  const details = [...new Set(requiredDetails.concat(requestedDetails || defaultDetails))];
  // Only requests from the Connections UI ask for the detail "isLogical".
  const includeLogicalConn = details.includes("isLogical");

  let ids = query.ids;
  if (ids && !Array.isArray(ids) && ids.split) {
    ids = ids.split(",");
  }

  const featureFlags = await FeatureFlagProvider.getFeatureFlags(context);
  if (featureFlags.INFRA_DWC_TWO_TENANT_MODE) {
    validateFilters(query.filters);
  }

  const params = {
    ids,
    folderIds: spaceIds,
    details,
    filters: computeGetConnectionFilters(includeLogicalConn, query.filters),
    inSpaceManagement: query.inSpaceManagement === "true" || query.inSpaceManagement === true,
    kinds: [ObjectKind.remote],
  };

  const connections = await getConnectionsHelper(context, params);
  logPerformance(startTime, "Finished getConnections", { context });

  return connections;
};

function getScopesWithPrivilege(
  context: IRequestContext,
  authTypeToPerform: AuthType,
  activitiesToPerform: Activity
): string[] {
  if (!context.scopes?.spaces) {
    return [];
  }
  return Object.keys(context.scopes.spaces).filter((spaceName: string) =>
    context.hasPrivilegeOnScope(spaceName, authTypeToPerform, activitiesToPerform)
  );
}

/**
 * Checks scoped RemoteConnection.Read privileges for a set of spaces
 * @param context The request context
 * @param spaceIds The space ids for which connections shall be retrieved
 * @returns The list of space ids for which the user has scoped RemoteConnection.Read privilege
 */
async function checkScopedReadPrivilege(
  context: IRequestContext,
  spaceIds: string | string[] | undefined
): Promise<string[]> {
  if (!spaceIds) {
    // No space is specified, get connections from all spaces for which user has scoped read privilege
    const allowedSpaceNames = getScopesWithPrivilege(context, AuthType.DWC_REMOTECONNECTION, Activity.read);
    if (allowedSpaceNames.length === 0) {
      throw new CodedError(
        "insufficientReadConnectionPrivilege",
        "Insufficient privilege: User is not allowed to read connections in any space.",
        StatusCodes.FORBIDDEN
      );
    }
    // Initialize an array to collect valid UUIDs
    const spaceUuids: string[] = [];
    // Fetch UUIDs and filter out empty strings
    await promiseMap(
      allowedSpaceNames,
      async (spaceName) => {
        const uuid = await getSpaceUuidFromName(context, spaceName);
        if (uuid) {
          spaceUuids.push(uuid);
        }
      },
      { concurrency: DEFAULT_CONCURRENCY }
    );

    if (spaceUuids.length === 0) {
      throw new CodedError(
        "invalidSpaceUuids",
        "Unable to retrieve valid space UUIDs for the specified spaces. Please check that the spaces exist and have valid UUIDs.",
        StatusCodes.BAD_REQUEST
      );
    }
    return spaceUuids;
  } else if (Array.isArray(spaceIds)) {
    // An array of space ids is provided, the ones with scoped read privilege present should be filtered out
    /* eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled */
    const allowedSpaceIds: string[] = await Promise.all(
      spaceIds.filter(async (spaceId) => {
        const spaceName = await getSpaceNameFromUuid(context, spaceId);
        return context.hasPrivilegeOnScope(spaceName, AuthType.DWC_REMOTECONNECTION, Activity.read);
      })
    );
    if (allowedSpaceIds.length === 0) {
      throw new CodedError(
        "insufficientReadConnectionPrivilege",
        "Insufficient privilege: User is not allowed to read connections in any of the given spaces.",
        StatusCodes.FORBIDDEN
      );
    }
    return allowedSpaceIds;
  } else {
    // A single space id is provided as a string, the most common case
    const spaceName = await getSpaceNameFromUuid(context, spaceIds);
    if (!context.hasPrivilegeOnScope(spaceName, AuthType.DWC_REMOTECONNECTION, Activity.read)) {
      throw new CodedError(
        "insufficientReadConnectionPrivilege",
        "Insufficient privilege: User is not allowed to read connections in this space.",
        StatusCodes.FORBIDDEN
      );
    }
    return [spaceIds];
  }
}

function validateFilters(filters?: string | string[]) {
  if (!filters) {
    return;
  }
  const forbiddenProperties = [
    "capabilityDataflowSource",
    "capabilityDataflowTarget",
    "capabilityReplicationflowSource",
    "capabilityReplicationflowTarget",
  ];
  if (Array.isArray(filters)) {
    filters = filters.join(",");
  }

  for (const filterProp of forbiddenProperties) {
    if (filters.includes(filterProp)) {
      throw new CodedError(
        "propertyNotSupportedForFiltering",
        `Property ${filterProp} is not supported for filtering`,
        StatusCodes.BAD_REQUEST
      );
    }
  }
}

async function getConnectionsHelper(context: IRequestContext, params: any): Promise<IRemoteConnection[]> {
  params.details = await adjustAndEnforceDetailsParam(params.details);
  // DW101-66417 increased logs for investigation
  logInfo(`getConnectionsHelper before GetObjectParameters params ${JSON.stringify(params)}`, { context });
  const validParams = new GetObjectParameters(context, params);
  params.details = Array.from(validParams.details);
  // DW101-66417 increased logs for investigation
  logInfo(`getConnectionsHelper before getObject params ${JSON.stringify(params)}`, { context });
  // Get connections from DWC repository
  const connections: RepositoryObject[] = await RepositoryObjectClient.getObject(context, params);
  if (isCanary()) {
    logInfo(
      `[getConnectionsHelper] after getObject connections ${JSON.stringify(connections)} . params: ${JSON.stringify(
        params
      )}`,
      { context }
    );
  }
  // Mapping from IRepositoryObject to IRemoteConnection
  return await computeConnectionDetails(context, connections, validParams.details);
}

async function computeConnectionDetails(
  context: IRequestContext,
  connections: RepositoryObject[],
  detailsParam: Set<string>
): Promise<IRemoteConnection[]> {
  let connDetails: any[] = connections.map((conn) => conn.getResult(detailsParam));
  // For logical connections we don't need remote source data, so we filter them out before mapping to the remote source names
  const remoteSources: string[] = connDetails
    .filter((conn) => !conn.isLogical || conn.isLogical === "false")
    .map((conn) => conn.remoteSourceName);
  // The remote source name is already fetched from the design time repository.
  // All other details like adapter name, dp agent details, real-time replication status are fetched from the customer hana database.
  // This is done before the migration part to avoid additional calls to fetch the adapterName.
  if (needRemoteSourceData(detailsParam)) {
    // remoteSourceName is required to fetch the runtime remote source data,
    // and that has been enforced in adjustAndEnforceDetailsParam
    connDetails = await addRemoteSourceData(context, connDetails, remoteSources);
  }
  // Adjust all connections with their space name
  // To optimize the call fetch the mapping with one repository call and apply it to all connections
  if (detailsParam.has("space_name")) {
    connDetails = await addSpaceName(context, connDetails);
  }

  const connectionDetails: IRemoteConnection[] = [];
  await promiseMap(
    connDetails,
    async (connDetail) => {
      try {
        if (detailsParam.has("csn")) {
          // content replaced with csn in getValidDetailsParam()
          // Renaming the object back to "content" from "csn"
          connDetail.content = computeCsn(connDetail);
          if (isCanary()) {
            logInfo(`[computeConnectionDetails] connDetail ${JSON.stringify(connDetail)}`, { context });
          }
          delete connDetail.csn;
          const connObject = connDetail.content.objects[connDetail.name];
          // Delete CredentialProperties for security reasons.
          // It might be present in Repo only for very old connections. With newer connections, credentials are not stored in the Repo.
          delete connObject.configuration?.CredentialProperties;

          // Delete schemaRegistry credentials for security reasons.
          if (connDetail.typeId === TypeIds.CONFLUENT && !connDetail.isLogical) {
            delete connObject.configuration.ConnectionProperties?.configurations?.schemaRegistry?.username;
            delete connObject.configuration.ConnectionProperties?.configurations?.schemaRegistry?.password;
          }

          // Compute property isUclSpaceConnection if it does not exist
          connObject.isUclSpaceConnection =
            connObject.isUclSpaceConnection || !!connObject.sharedUclConnection || !!connObject.uclSharedConnectionId;

          // Logical Connections don't need to get migrated.
          if (!connDetail.isLogical) {
            connDetail = await migrate(context, connDetail, detailsParam);
          }

          if (detailsParam.has("connectionMetaschema")) {
            connDetail.connectionMetaschema = await computeMetaschema(context, connDetail);
          }
          if (detailsParam.has("configuration")) {
            connDetail.configuration = computeConfiguration(connDetail);
          }
          if (detailsParam.has("usesRMS")) {
            connDetail.usesRMS = await computeUsesRMS(context, connDetail);
          }

          if (detailsParam.has("isRTRPausingSupported")) {
            connDetail.isRTRPausingSupported = await computeIsRTRPausingSupported(context, connDetail);
          }
          if (detailsParam.has("asyncRemoteSourceDeploymentStatus")) {
            connDetail.asyncRemoteSourceDeploymentStatus = await computeRemoteSourceDeploymentStatus(
              context,
              connDetail
            );
          }
          if (detailsParam.has("isConnProxyAssumedReady")) {
            const connectionModel = ConnectionFactory.fromTypeId(connDetail.typeId, context);
            const connObject = connDetail.content.objects[connDetail.name];
            if (await connectionModel.usesHanaConnectivityProxy(connObject)) {
              const sccConfig = await HanaSCCConfigClient.fromRequestContext(context);
              connDetail.isConnProxyAssumedReady = (await sccConfig.isConnectivityProxyAssumedToBeReady()).toString();
            }
          }
          if (detailsParam.has("ccmCompositeId")) {
            connDetail.ccmCompositeId = await computeCompositeId(context, connDetail);
          }
          if (detailsParam.has("#repositoryPackage")) {
            // if the connection is not part of the package the property #repositoryPackage is undefined
            connDetail.repositoryPackage = connDetail["#repositoryPackage"] || RepositoryPackageNone;
            delete connDetail["#repositoryPackage"];
          }

          await computeDataAndReplicationFlowCapabilities(context, connDetail, detailsParam);
        }
        connectionDetails.push(connDetail);
      } catch (err) {
        logError([`computeConnectionDetails failed for the connection: ${connDetail.name}`, err], { context });
      }
    },
    { concurrency: DEFAULT_CONCURRENCY }
  );

  return connectionDetails;
}

async function computeCompositeId(context: IRequestContext, connDetail: any): Promise<CCMCompositeId | undefined> {
  if (connDetail.disReplicationStatus !== CCMReplicationStatus.SUCCESSFULLY_REPLICATED) {
    return;
  }

  const configuration = connDetail.content?.objects[connDetail.name].configuration;
  const ccmCompositeId = getCCMCompositeId(context, connDetail.typeId, connDetail.disConnectionId, configuration);
  return ccmCompositeId;
}

async function computeIsRTRPausingSupported(context: IRequestContext, connDetail: any): Promise<boolean> {
  const conn = ConnectionFactory.fromTypeId(connDetail.typeId, context);
  const connObject = connDetail.content.objects[connDetail.name];
  if (!connDetail.realtimeReplicationStatus) {
    return false;
  }
  return await conn.isRTRPausingSupported(connObject, connDetail.realtimeReplicationStatus);
}

async function computeRemoteSourceDeploymentStatus(
  context: IRequestContext,
  connection: IRemoteConnection
): Promise<CCMAndRemoteSourceDeploymentStatusResponse> {
  if (!(await computeUsesRMS(context, connection))) {
    return CCMAndRemoteSourceDeploymentStatusResponse.NOT_ASYNC;
  }
  try {
    const sharing = new ConnectionSharingUtils(context);
    const status: CCMAndRemoteSourceDeploymentStatus = await sharing.getCCMConnAndCCMRemoteSourceDeploymentStatus(
      connection
    );
    return status;
  } catch (err) {
    logError([`Get remote source deployment status failed.`, err], { context });
    return CCMAndRemoteSourceDeploymentStatusResponse.NO_STATUS_ERROR;
    // if CCM is down or other error
  }
}

async function addSpaceName(context: IRequestContext, connDetails: any) {
  const uniqueSpaceIds = Array.from(new Set<string>(connDetails.map((conn: any) => conn.space_id as string)));
  const spaceObjects = await getSpaces(context, { ids: uniqueSpaceIds, details: ["id", "name"] });
  const spaceNameMapping: Record<string, string> = spaceObjects.reduce((curr, spaceObject) => {
    curr[spaceObject.id] = spaceObject.name;
    return curr;
  }, {} as Record<string, string>);
  return connDetails.map((conn: any) => {
    conn.space_name = spaceNameMapping[conn.space_id];
    return conn;
  });
}

async function computeUsesRMS(context: IRequestContext, connDetail: any): Promise<boolean> {
  // Currently no connection type uses RMS for Remote Source creation
  const conn = ConnectionFactory.fromTypeId(connDetail.typeId, context);
  const config = connDetail.content?.objects[connDetail.name].configuration;
  return await conn.usesRMSForHanaRemoteSource(config);
}

/**
 * This function does some renaming to the details (properties) parameter to match the names that's present in the Repository.
 * Also, some properties are enforced as computing certain properties or to do migration, data from other properties are required.
 */
export async function adjustAndEnforceDetailsParam(params: string[]): Promise<string[]> {
  const paramsSet = new Set<string>(params);
  await adjustAndEnforceDetailsParamHelper(paramsSet);
  if (paramsSet.has("content")) {
    // Replace "content" with "csn" as the column name in Repository is "csn"
    paramsSet.delete("content");
    paramsSet.add("csn");
  }
  return Array.from(paramsSet);
}

async function adjustAndEnforceDetailsParamHelper(details: Set<string>): Promise<Set<string>> {
  const detailsUnchanged = new Set(details); // creates a shallow copy

  // typeId is required for migration (@see migrate()) and also to get the metaschema for individual connections types
  // typId is also required for capability migration.
  const propsThatRequireTypeId = [
    "connectionMetaschema",
    "content",
    "usesRMS",
    "capabilityModelTransfer",
    "capabilityHanaSdi",
    "capabilityDisDataflow",
    "capabilityPartnerSchema",
    "capabilityDataflowSource",
    "capabilityDataflowTarget",
    "capabilityReplicationflowSource",
    "capabilityReplicationflowTarget",
  ];
  // capabilites will be removed here when capability migration is removed

  const requireTypeId = propsThatRequireTypeId.some((prop) => details.has(prop));
  if (requireTypeId) {
    details.add("typeId");
  }

  // Configuration is overwritten later in computeConfiguration()
  // content is required to migrate typeIds for older connections using the adapterName (present in content) for connections with no typeId
  // configuration is computed using the configuration object inside content
  // For capability migration capabilityHanaSdi requires content to read ABAP endpoint
  const propsThatRequireContent = [
    "configuration",
    "typeId",
    "usesRMS",
    "capabilityHanaSdi",
    "capabilityReplicationFlowSource",
  ];
  const requireContent = propsThatRequireContent.some((prop) => details.has(prop));
  if (requireContent) {
    details.add("content");
  }

  // remoteSourceName is required either to fetch the runtime data or enforce migration
  const remoteSourceDataRequired = needRemoteSourceData(details);
  if (details.has("content") || remoteSourceDataRequired) {
    details.add("remoteSourceName");
  }

  // If remoteSourceDataRequired is true, real time replication status is also fetched
  // dataAccess is required to modify the real time replication status based on federationOnly value.
  if (remoteSourceDataRequired) {
    details.add("dataAccess");
  }

  if (details.has("asyncRemoteSourceDeploymentStatus")) {
    details.add("usesRMS");
    details.add("disReplicationStatus");
    details.add("disConnectionId");
    details.add("remoteSourceName");
    details.add("space_name");
  }

  if (details.has("isRTRPausingSupported")) {
    details.add("content");
    details.add("typeId");
    // remote source data requirement is enforced outside
  }

  if (details.has("isConnProxyAssumedReady")) {
    details.add("typeId");
    details.add("content");
    details.add("name");
  }

  if (details.has("capabilityModelTransfer")) {
    // needed for capability migration
    details.add("capabilityHanaSdi");
  }

  if (details.has("content")) {
    details.add("name"); // required to delete CredentialProperties and also for generating content obj in computeContent() if csn is not present
    details.add("credentialMode"); // enforce for migration
  }

  if (details.has("ccmCompositeId")) {
    details.add("typeId");
    details.add("name");
    details.add("content");
    details.add("disReplicationStatus");
    details.add("disConnectionId");
  }

  // if there is any change we rerun to resolve parameter requirement inter dependencies.
  const changed = !_.isEqual(details, detailsUnchanged);
  if (changed) {
    return await adjustAndEnforceDetailsParamHelper(details);
  }
  return details;
}

function needRemoteSourceData(requestedDetails: Set<string>): boolean {
  const propsInRuntimeHana = [
    "adapter",
    "location",
    "agentName",
    "agentGroupName",
    "agentStatus",
    "realtimeReplicationStatus",
    "isRTRPausingSupported",
  ];
  const needRemoteSourceData = propsInRuntimeHana.some((prop) => requestedDetails.has(prop));
  return needRemoteSourceData;
}

function computeCsn(connDetail: any) {
  let csn: any = {};
  if (connDetail.csn) {
    csn = connDetail.csn;
    // TODO: Is this required? May be this is csn migration for older connections that doesn't have csn?
    // This part was there from the beginning and we are not sure about the impact if we delete it now
  } else {
    // Setting csn object manually as conn.csn is not available
    // No connection should ideally reach this point as the configuration property in the parent object might be truncated for some connections
    // ..due to the limited size of column in tags table (where configuration is stored) and we cannot use it further
    const connCopy = ConnectionUtils.deepCopy(connDetail);
    if (typeof connCopy.configuration === "string") {
      try {
        // parse configuration to access it later
        connCopy.configuration = JSON.parse(connCopy.configuration);
      } catch (err) {
        // We cannot do anything here but just log the error and pass empty configuration. (On the UI, the user can edit the connection and re-enter the properties to fix the connection)
        logVerbose(
          `${connCopy.name} connection is broken. It has an invalid configuration: ${connCopy.configuration}. Reason: ${err}`,
          { context: null }
        );
        connCopy.configuration = { ConnectionProperties: {} };
      }
    }
    // Delete props that need not be part of csn
    // TODO: There might be more props that's unnecessary. Can we just keep everything?
    delete connCopy.id;
    delete connCopy.space_id;
    delete connCopy.creator;
    delete connCopy.creation_date;
    delete connCopy.modifier;
    delete connCopy.modification_date;
    const newCsn: any = { objects: {} };
    newCsn.objects[connCopy.name] = connCopy;
    csn = newCsn;
  }

  // Delete tag values from CSN.
  // These properties are all present on the top-level of the connection detail object.
  const csnContent = csn.objects[connDetail.name];
  delete csnContent.typeId;
  delete csnContent.disReplicationStatus;
  delete csnContent.realtimeReplicationStatus;
  delete csnContent.capabilities;
  delete csnContent.capabilityHanaSdi;
  delete csnContent.capabilityDisDataflow;
  delete csnContent.capabilityModelTransfer;
  delete csnContent.capabilityPartnerSchema;
  // new capabilites do need to be deleted here as they are already excluded when repo object is built

  return csn;
}

async function computeMetaschema(context: IRequestContext, connDetail: any) {
  const connectionModel = ConnectionFactory.fromTypeId(connDetail.typeId, context);
  const metaschema = await connectionModel.getAdjustedMetaschema();
  // Some properties in the metaschema file are still only related to UI and is not stored in the Repository.
  // Retrieve such properties which are needed to be part of the connection instance (for filtering) from the metaschema file.
  return {
    categories: metaschema.categories,
    sources: metaschema.sources,
  };
}

function computeConfiguration(connDetail: any) {
  const connObject = connDetail.content?.objects[connDetail.name];
  // configuration may be truncated as property as it is stored in the tags table with limited column size
  // retrieve the value from unrestricted content JSON
  return JSON.stringify(connObject.configuration);
}

export async function addRemoteSourceData(
  context: IRequestContext,
  connectionDetails: IRemoteConnection[],
  remoteSourceNames: string[]
): Promise<IRemoteConnection[]> {
  // currently, remote source names are present in the repository even for non-SDI connections (a bug?).
  // so there could be cases where the remote source is not present in the customer hana for such connections.
  let remoteSourceData: SdiManager.IRemoteSource[] | undefined;
  try {
    remoteSourceData = await SdiManager.getRemoteSourcesDetailed(context, "", remoteSourceNames);
  } catch (err) {
    if (err.code === "tenantUpgradeInProgress") {
      logWarning("Tenant upgrade in progress. Cannot fetch remote source data.", { context });
    } else {
      throw err;
    }
  }

  if (!remoteSourceData || !remoteSourceData.length) {
    return connectionDetails;
  }

  const connDtls = connectionDetails.map((conn) => {
    const remoteSource = remoteSourceData!.find((rs) => rs.name === conn.remoteSourceName);
    if (remoteSource) {
      // Overwrite the default real-time replication status for BW Bridge. See DW101-9050
      // OR Overwrite the default real-time replication status based on dataAccess feature: see DW00-7455.
      if (conn.typeId === TypeIds.SAPBWBRIDGE || conn.dataAccess === "federationOnly") {
        remoteSource.realtimeReplicationStatus = RealTimeReplicationStatus.NOTAPPLICABLE;
      }

      Object.assign(conn, {
        // As you can fetch single properties for a connection it is also possible to just fetch the remote source data.
        // If we want to derive the adapter name via Connection.getCurrentRemoteSourceAdapterName in that case,
        // we need to load the "content" property in addition just to derive the adapter name.
        // To prevent loading that additional data we are fetching the adapter name from the remote source here directly and not via connection model.
        // The used remote source adapter must always match the result of Connection.getCurrentRemoteSourceAdapterName.
        adapter: remoteSource.adapter,
        location: remoteSource.location,
        agentName: remoteSource.agentName,
        agentGroupName: remoteSource.agentGroupName,
        agentStatus: remoteSource.agentStatus,
        realtimeReplicationStatus: remoteSource.realtimeReplicationStatus,
      });
    }
    return conn;
  });
  return connDtls;
}

/**
 * New properties are introduced for certain connections with some user stories. These properties wouldn't be present in the Repository for old connections.
 * This function does migrations to add such properties to old connections.
 *
 * PS:
 * - The cloud connector properties are not migrated for older connections
 * - The feature section is not migrated for older connections
 *
 * At some point, we should think of migrating the properties directly in the DB once DW00-7697 is done.
 */
async function migrate(context: IRequestContext, connDetail: any, detailsParam: Set<string>) {
  if (isCanary()) {
    logInfo(`[migrate] connDetail ${JSON.stringify(connDetail)}`, { context });
  }
  const clonedConnDetail = ConnectionUtils.deepCopy(connDetail);
  let connObject = clonedConnDetail.content?.objects[clonedConnDetail.name];
  // If configuration object does not exist or is empty, on-the-fly migration should be skipped
  if (!connObject.configuration || Object.keys(connObject.configuration).length === 0) {
    return clonedConnDetail;
  }
  if (isCanary()) {
    logInfo(`[migrate] connObject ${JSON.stringify(connObject)}`, { context });
  }
  // add default values for existing connections. For example, SAP S4HANA Cloud
  // has a new feature that supports ABAP ODBC adapter in DS00-1342 with the
  // property "dataProvisioningOption". For existing connections, if a connection
  // was created with dpAgent, then set "dataProvisioningOption" to "dpAgent" by default
  const connModel = ConnectionFactory.fromTypeId(clonedConnDetail.typeId, context);
  connObject = await connModel.addDefaults(connObject);

  // refactor: the following migrations should be moved into the connection models
  // using the function "addDefaults()"
  await migrateCapabilities(context, clonedConnDetail, connObject, detailsParam);
  const migrateHanaOnPrem =
    clonedConnDetail.typeId === TypeIds.HANA &&
    connObject.configuration.ConnectionProperties.configurations.database.category ===
      SourceLocationCategories.ONPREMISE;
  if (migrateHanaOnPrem) {
    await migrateProvisioningOption(
      clonedConnDetail,
      clonedConnDetail.space_id,
      clonedConnDetail.remoteSourceName,
      context
    );
  }
  await migrateAuthType(context, clonedConnDetail, connObject);
  await migrateOAuth2GrantType(clonedConnDetail, connObject);
  if (clonedConnDetail.typeId === TypeIds.HDFS) {
    await migrateHDFSCustomConfig(connObject);
  }
  if (
    clonedConnDetail.typeId === TypeIds.BIGQUERY ||
    clonedConnDetail.typeId === TypeIds.SAPS4HANACLOUD ||
    clonedConnDetail.typeId === TypeIds.CDI ||
    clonedConnDetail.typeId === TypeIds.SAPMDI ||
    clonedConnDetail.typeId === TypeIds.SAPSF ||
    clonedConnDetail.typeId === TypeIds.SAPBWBRIDGE
  ) {
    // Fix for DW101-16093. Ideally these UI feature flags can be removed generally.
    // We do not maintain them via CCM like API for these connections,
    // so we then generally remove them here for these connections to be consistent.
    // This code can be removed with DW12-2749
    removeUIFeatureProperties(connObject);
  }
  return clonedConnDetail;
}

function removeUIFeatureProperties(connObject: any) {
  if (connObject.configuration.ConnectionFeatures?.features) {
    const features = connObject.configuration.ConnectionFeatures.features;
    delete features.remoteTables;
    delete features.modelImport;
    delete features.dataflows;
  }
}

async function computeDataAndReplicationFlowCapabilities(
  context: IRequestContext,
  clonedConnDetail: any,
  detailsParam: Set<string>
) {
  const model = ConnectionFactory.fromTypeId(clonedConnDetail.typeId, context);
  const supportedCaps = await model.getSupportedCapabilities();

  if (detailsParam.has("capabilityDataflowSource") && !clonedConnDetail.capabilityDataflowSource) {
    clonedConnDetail.capabilityDataflowSource = supportedCaps
      .includes(ConnectionCapabilities.DATAFLOWSOURCE)
      .toString();
  }

  if (detailsParam.has("capabilityDataflowTarget") && !clonedConnDetail.capabilityDataflowTarget) {
    clonedConnDetail.capabilityDataflowTarget = supportedCaps
      .includes(ConnectionCapabilities.DATAFLOWTARGET)
      .toString();
  }

  if (detailsParam.has("capabilityReplicationflowSource") && !clonedConnDetail.capabilityReplicationflowSource) {
    if (clonedConnDetail.typeId === TypeIds.HDL_FILES) {
      clonedConnDetail.capabilityReplicationflowSource = "true";
    } else {
      clonedConnDetail.capabilityReplicationflowSource = supportedCaps
        .includes(ConnectionCapabilities.REPLICATIONFLOWSOURCE)
        .toString();
    }
  }

  if (detailsParam.has("capabilityReplicationflowTarget") && !clonedConnDetail.capabilityReplicationflowTarget) {
    clonedConnDetail.capabilityReplicationflowTarget = supportedCaps
      .includes(ConnectionCapabilities.REPLICATIONFLOWTARGET)
      .toString();
  }
}

async function migrateCapabilities(
  context: IRequestContext,
  clonedConnDetail: any,
  connObject: any,
  detailsParam: Set<string>
) {
  // The goal of this migration is that it is guranteed that for each capability a value is set ("true" or "false")
  // and if there is already a value set that it is corrected if necessary.

  // Generally the expectation is that for very old connections, capabilities could be undefined/not set.
  // In this case all capabilites are undefined, becuase at that time the capabilities were derived from the meta schema and not persisted.
  // After the introduction of the capabilities concept (started with capabilityHanaSdi and capabilityDisDataflow)
  // all new connections should have the capabilities set ("true" or "false") which they have supported at that time.

  // Whenever a capability is newly added to an existing connection type the capability is undefined if it is not migrated and the connetion was not saved in the mean time.
  // For the capabilityDisDataflow such a migration was done if the capability was newly added. But for example for capabilityModelTransfer such a migration was not done.
  // Whenever a new capability is added all connections need to be migrated to have the value "true" or "false" for this capability.

  // This migration should only take effect on very old connections. In this case remote tables was a required feature.
  // So a connection that supports remote tables but does not have a remote source should not exist.
  // Further, in the browsing page the check was capabilityHanaSdi !== false to filter connections. So connection with capabilityHanaSdi = "" appeared there.
  // So such connections should have the capabilityHanaSdi = true to keep the same set of connections there for the user.
  // Even if we migrate such a connection, a wrong capability can be observed via connection validation.
  const model = ConnectionFactory.fromTypeId(clonedConnDetail.typeId, context);

  if (detailsParam.has("capabilityHanaSdi")) {
    if (clonedConnDetail.capabilityHanaSdi !== "false" && clonedConnDetail.capabilityHanaSdi !== "true") {
      logInfo(
        `CapabilityHanaSdi repository migration pending for connection id: ${connObject.id}, name: ${connObject.name}, adapter: ${connObject.adapterName}`,
        { context }
      );
      // basic case
      clonedConnDetail.capabilityHanaSdi = (await model.getSupportedCapabilities())
        .includes(ConnectionCapabilities.HANASDI)
        .toString();
      // special handling on top
      if (
        clonedConnDetail.typeId === TypeIds.ABAP &&
        connObject.configuration.ConnectionProperties.ConnectionInfo.endpoint === CCMEndpoints.WSRFC
      ) {
        // is this property always there? Fallback needed?
        clonedConnDetail.capabilityHanaSdi = "false";
      }
      if (
        clonedConnDetail.typeId === TypeIds.HDL_FILES &&
        connObject.configuration.ConnectionProperties.configurations.dataAccessLevel !== DataAccessLevel.DeltaShare
      ) {
        clonedConnDetail.capabilityHanaSdi = "false";
      }
    }
  }

  // This capability was migrated and so this is just a safe guard.
  if (detailsParam.has("capabilityDisDataflow")) {
    if (clonedConnDetail.capabilityDisDataflow !== "false" && clonedConnDetail.capabilityDisDataflow !== "true") {
      logInfo(
        `CapabilityDisDataflow repository migration pending for connection id: ${connObject.id}, name: ${connObject.name}, adapter: ${connObject.adapterName}`,
        { context }
      );
      clonedConnDetail.capabilityDisDataflow = (await model.getSupportedCapabilities())
        .includes(ConnectionCapabilities.DISDATAFLOW)
        .toString();
    }
  }

  // This capability is potentially not set for connections that were created before this capability was introduced.
  if (detailsParam.has("capabilityModelTransfer")) {
    const modelTransferCap = (await model.getSupportedCapabilities()).includes(ConnectionCapabilities.MODELTRANSFER);

    // For connections that upgrade to that capability we need to decide individually
    if (clonedConnDetail.capabilityModelTransfer !== "false" && clonedConnDetail.capabilityModelTransfer !== "true") {
      logInfo(
        `CapabilityModelTransfer repository migration pending for connection id: ${connObject.id}, name: ${connObject.name}, adapter: ${connObject.adapterName}`,
        { context }
      );
      clonedConnDetail.capabilityModelTransfer = modelTransferCap.toString();
      if (clonedConnDetail.typeId === TypeIds.SAPS4HANAOP) {
        // Just a safe guard as capabilityModelTransfer should already be false for older connections
        clonedConnDetail.capabilityModelTransfer = "false";
      }
    }

    if (clonedConnDetail.typeId === TypeIds.SAPBWBRIDGE) {
      clonedConnDetail.capabilityModelTransfer = (
        modelTransferCap && clonedConnDetail.capabilityHanaSdi === "true"
      ).toString();
    }
  }

  // This capability is potentially not set for connections that were created before this capability was introduced.
  // We default partner capability to true if supported. Partner connection always have the partner capability.
  if (detailsParam.has("capabilityPartnerSchema")) {
    if (clonedConnDetail.capabilityPartnerSchema !== "false" && clonedConnDetail.capabilityPartnerSchema !== "true") {
      logInfo(
        `CapabilityPartnerSchema repository migration pending for connection id: ${connObject.id}, name: ${connObject.name}, adapter: ${connObject.adapterName}`,
        { context }
      );
      clonedConnDetail.capabilityPartnerSchema = (await model.getSupportedCapabilities())
        .includes(ConnectionCapabilities.PARTNERSCHEMA)
        .toString();
    }
  }
}

/**
 * Migrates relevant connections that do not have the dataProvisioningOption property. This property is added.
 */
async function migrateProvisioningOption(
  connDetail: any,
  spaceId: string,
  remoteSourceName: string,
  context: IRequestContext
) {
  const connObject = connDetail.content?.objects[connDetail.name];
  const connFeatures = connObject.configuration.ConnectionFeatures?.features;
  if (connFeatures) {
    const hasProvisioningOption = connFeatures.dataProvisioningOption !== undefined;
    if (!hasProvisioningOption) {
      const hasDPAgentProp = connFeatures.dpAgent !== undefined && connFeatures.dpAgent !== "";
      const provOpt = hasDPAgentProp ? DataProvisioningOptions.DP_AGENT : DataProvisioningOptions.NONE;
      Object.assign(connObject.configuration.ConnectionFeatures.features, { dataProvisioningOption: provOpt });
    } else if (connFeatures.dataProvisioningOption === "") {
      // migrate provisioning option "" to "NONE"
      Object.assign(connObject.configuration.ConnectionFeatures.features, {
        dataProvisioningOption: DataProvisioningOptions.NONE,
      });
    }
  } else if (connDetail.agentName) {
    // The remote source details already have been added to the connection if any of the runtime
    // remote source data have been requested.
    // This way we avoid calling customer hana to fetch the remote source details.
    const newFeatureProps = {
      ConnectionFeatures: {
        features: {
          dataProvisioningOption: DataProvisioningOptions.DP_AGENT,
          dpAgent: connDetail.agentName,
          dataflows: true,
          remoteTables: true,
        },
      },
    };
    Object.assign(connObject.configuration, newFeatureProps);
  } else {
    // for connections that do not have a ConnectionFeatures section we derive the values and add this section.
    const remoteSources: SdiManager.IRemoteSource[] =
      (await SdiManager.getRemoteSourcesDetailed(context, spaceId, [remoteSourceName])) || [];
    let dpAgentValue = "";
    let dataProvOptionValue = DataProvisioningOptions.NONE;
    const hasRemoteSource = remoteSources.length === 1;
    if (hasRemoteSource) {
      const remoteSource = remoteSources[0];
      dpAgentValue = remoteSource.agentName as string;
      dataProvOptionValue = DataProvisioningOptions.DP_AGENT;
    }

    const newFeatureProps = {
      ConnectionFeatures: {
        features: {
          dataProvisioningOption: dataProvOptionValue,
          dpAgent: dpAgentValue,
          dataflows: true,
          remoteTables: hasRemoteSource,
        },
      },
    };
    Object.assign(connObject.configuration, newFeatureProps);
  }
}

/**
 * Migrates relevant connections that do not have the authentication.auth_type property. This property is added.
 */
async function migrateAuthType(context: IRequestContext, clonedConnDetail: any, connObject: any) {
  const typeId: TypeIds = clonedConnDetail.typeId;
  const connConfig: DWCConnectionConfiguration = connObject.configuration;
  let auth_type: AuthTypes | undefined;
  let connProps: DWCConnectionProperties;

  switch (typeId) {
    case TypeIds.ODATA:
      connProps = connConfig.ConnectionProperties.UI as Required<DWCConnectionProperties>;
      if (!connProps.authentication) {
        const credentialMode: CredentialModes = clonedConnDetail.credentialMode;
        auth_type = credentialMode === CredentialModes.TechnicalUser ? AuthTypes.Basic : AuthTypes.NoAuth;
        // credentialMode is always defaulted to technical user for all connections regardless of the authentication type selected.
        // Here, if we do not set this to "technical user", there is some weird logic in the UI that prevents the credentials section to be displayed.
        // Previously this worked also with "none" for ODATA connection because credentialMode was exposed on the UI and the value changed to "technical user" on user selection.
        // Today, credentialMode is not exposed on the UI, instead we display the new authentication.auth_type property, that means; credentialMode can always be "technical user".
        // Not sure why is it in 2 places redundantly.
        clonedConnDetail.credentialMode = CredentialModes.TechnicalUser;
        connObject.credentialMode = CredentialModes.TechnicalUser;
      }
      break;
    case TypeIds.SAPSF:
      connProps = connConfig.ConnectionProperties.UI as Required<DWCConnectionProperties>;
      if (!connProps.authentication) {
        auth_type = AuthTypes.Basic;
      }
      break;
    case TypeIds.ABAP:
    case TypeIds.SAPS4HANACLOUD:
    case TypeIds.SAPS4HANAOP:
      // for ABAP and SAPS4HANAOP, the object "authentication" is introduced with the FF
      // DWCO_CONNECTION_ABAP_OAUTH2. In UI, the connection property object now
      // includes the "authentication" object by default and the authentication
      // type is set to "Basic" as default, therefore no FF check here
      connProps = connConfig.ConnectionProperties.ConnectionInfo as Required<DWCConnectionProperties>;
      if (!connProps || !connProps.authentication) {
        auth_type = AuthTypes.Basic;
      }
      break;
    case TypeIds.SAPMDI:
      connProps = connConfig.ConnectionProperties.configurations as Required<DWCConnectionProperties>;
      if (!connProps.authentication) {
        auth_type = AuthTypes.OAuth2;
      }
      break;
    case TypeIds.AZURESQL:
      connProps = connConfig.ConnectionProperties.configurations as Required<DWCConnectionProperties>;
      if (!connProps.authentication) {
        auth_type = AuthTypes.Basic;
      }
      break;

    default:
      // No migration required for other connection types
      return;
  }

  if (auth_type && connProps) {
    Object.assign(connProps, {
      authentication: {
        auth_type,
      },
    });
  }
}

/**
 * Migrates relevant connections that do not have the oauth2.oauth2_grant_type property. This property is added.
 */
async function migrateOAuth2GrantType(clonedConnDetail: any, connObject: any) {
  const typeId: TypeIds = clonedConnDetail.typeId;
  const connConfig: DWCConnectionConfiguration = connObject.configuration;

  switch (typeId) {
    case TypeIds.SAPMDI:
      const connProps = connConfig.ConnectionProperties.configurations as Required<DWCConnectionProperties>;
      const oAuthProps = connProps.oauth2 as Required<DWCOAuth2Props>;
      if (!oAuthProps.oauth2_grant_type) {
        oAuthProps.oauth2_grant_type = OAuthGrantType.ClientCredentials;
      }
      break;
    default:
      // No migration required for other connection types
      return;
  }
}

/**
 * Migrates HDFS connections that do not have the custom.use_custom_parameters property. This property is added.
 */
async function migrateHDFSCustomConfig(connObject: any) {
  const connProps = connObject.configuration.ConnectionProperties.configurations;
  const customParamsConfig = connProps.custom;

  // Checking whether use_custom_parameters property is present.
  // If it's present, then the value would be "true"/"false" in string and not boolean.
  if (!customParamsConfig.use_custom_parameters) {
    if (customParamsConfig.parameter && customParamsConfig.value) {
      customParamsConfig.use_custom_parameters = "true";
    } else {
      customParamsConfig.use_custom_parameters = "false";
    }
  }
}

export async function upsertConnection(
  context: IRequestContext,
  connectionInfo: any,
  spaceId: string,
  logicalConnId?: string,
  inSpaceManagement = false
) {
  const isEdit = !!connectionInfo.id;
  const isCreate = !isEdit;
  if (logicalConnId && isEdit) {
    throw new CodedError(
      "cannotEditlogicalConnection",
      `A logical connection cannot be updated. Connection payload id: ${connectionInfo.id}, logical connection id: ${logicalConnId} `,
      StatusCodes.BAD_REQUEST
    );
  }

  const spaceName = await getSpaceNameFromUuid(context, spaceId);
  // in non-SDP mode 'hasPrivilegeOnScope' internally calls 'hasPrivilegesOnType'
  // During BDC onboarding, there is no business user but the call is triggered using a tech JWT token, bypass privilege check in that case
  const isBdcOnboarding = await isInsightAppOnboarding(context);

  if (!inSpaceManagement && !isBdcOnboarding) {
    const canRead = context.hasPrivilegeOnScope(spaceName, AuthType.DWC_REMOTECONNECTION, Activity.read);
    const canCreate = context.hasPrivilegeOnScope(spaceName, AuthType.DWC_REMOTECONNECTION, Activity.create);
    const canUpdate = context.hasPrivilegeOnScope(spaceName, AuthType.DWC_REMOTECONNECTION, Activity.update);

    if (!canRead || (isCreate && !canCreate) || (isEdit && !canUpdate)) {
      throw new CodedError(
        "insufficientUpsertConnectionPrivilege",
        "Insufficient privilege: User is not allowed to upsert a connection.",
        StatusCodes.FORBIDDEN
      );
    }
  }

  if (!connectionInfo.typeId) {
    // intermediate logging to identify root cause of empty type id
    logError(`Connection type ID is undefined. Is on create: ${isCreate}, technical name: ${connectionInfo.name}`, {
      context,
    });
    throw new CodedError(`undefinedType`, `Undefined connection type`, StatusCodes.BAD_REQUEST);
  }

  if (!spaceName) {
    throw new CodedError(`spaceNotFound`, `No space name found for space Id:: ${spaceId}`, StatusCodes.BAD_REQUEST);
  }

  const featureFlags = await FeatureFlagProvider.getFeatureFlags(context);

  if (connectionInfo.uclSharedConnectionId) {
    // Space connection creation (resolve the associated uclSharedConnection and use it to fill spaceUclConnection)
    await generateSpaceConnectionPayload(context, connectionInfo);
  }
  // get the connection model based on the type id(for ucl case, it should use the typeId of uclSharedConnection resolved before )
  const connectionModel = ConnectionFactory.fromTypeId(connectionInfo.typeId, context);
  const connectionMetaSchema = await connectionModel.getAdjustedMetaschema();

  const usesRMS = await connectionModel.usesRMSForHanaRemoteSource(connectionInfo.configuration);
  if (connectionInfo.typeId === TypeIds.SAPS4HANACLOUD && usesRMS) {
    // Intermediate logic to enable model transfer for RMS based S4HANA Cloud connection internally only. For testing model transfer for RMS based connections. See DW101-9119.
    connectionInfo.capabilityModelTransfer = "true";
  }

  // A partner capability is true if and only if the connection is a partner connection. The capability is not expected to be passed by the caller.
  const supportsPartner = (await connectionModel.getSupportedCapabilities()).includes(
    ConnectionCapabilities.PARTNERSCHEMA
  );
  if (supportsPartner) {
    if (!!connectionInfo.capabilityPartnerSchema) {
      throw new CodedError(
        "partnerCapabilityCannotBeSet",
        "Partner capability cannot be set and is automatically on for partner connections",
        StatusCodes.BAD_REQUEST
      );
    }
    connectionInfo.capabilityPartnerSchema = "true";
  }
  // Prevent setting dataAccess to federationOnly for SDI Adapter via API.
  if (connectionInfo.typeId === TypeIds.HANA) {
    if (
      connectionInfo.configuration.ConnectionFeatures.features.dataAccess === "federationOnly" &&
      connectionInfo.configuration.ConnectionFeatures.features.dataProvisioningOption === "dpAgent" &&
      connectionInfo.configuration.ConnectionProperties.configurations.database.category === "onpremise"
    ) {
      throw new CodedError("forbiddenOperation", "Setting dataAcces to federationOnly is not allowed for SDI Adapter");
    }
  }
  if (connectionInfo.typeId === TypeIds.ODATA && connectionInfo.configuration.ConnectionProperties.UI.httpHeaders) {
    const deniedList = connectionInfo.configuration.ConnectionProperties.UI.httpHeaders.filter((element: any) => {
      const lowerCaseValue = element.name.trim().toLowerCase();
      const urlRegex = /^(proxy-|sec-)+/;
      if (httpKeyDeniedList.includes(lowerCaseValue) && lowerCaseValue.match(urlRegex)) {
        return true;
      }
      return false;
    });
    if (deniedList.length > 0) {
      throw new CodedError("forbiddenOperation", "There are some reserved keys in the HTTP headers");
    }
  }
  // Set the capabilities that are enabled (by the user). If the capability is supported is checke by a seperate capability validation.
  let capabilities;
  if (featureFlags.INFRA_DWC_TWO_TENANT_MODE) {
    capabilities = {
      isHanaSdiEnabled: connectionInfo.capabilityHanaSdi === "true", // is "true" or "false". Ensured by validation
      isModelTransferEnabled: connectionInfo.capabilityModelTransfer === "true",
      isPartnerSchemaEnabled: connectionInfo.capabilityPartnerSchema === "true",
      isDisDataflowSourceEnabled: connectionInfo.capabilityDataflowSource === "true",
      isDisDataflowTargetEnabled: connectionInfo.capabilityDataflowTarget === "true",
      isReplicationflowSourceEnabled: connectionInfo.capabilityReplicationflowSource === "true",
      isReplicationflowTargetEnabled: connectionInfo.capabilityReplicationflowTarget === "true",
    };
  } else {
    capabilities = {
      isHanaSdiEnabled: connectionInfo.capabilityHanaSdi === "true", // is "true" or "false". Ensured by validation
      isDisDataflowEnabled: connectionInfo.capabilityDisDataflow === "true",
      isModelTransferEnabled: connectionInfo.capabilityModelTransfer === "true",
      isPartnerSchemaEnabled: connectionInfo.capabilityPartnerSchema === "true",
    };
  }

  // We only add the adapterName to the payload here to support on the fly migration of the typeID and the S4/Hana Cloud Direct adapter that is selectable via dropdown.
  if (capabilities.isHanaSdiEnabled || capabilities.isModelTransferEnabled) {
    connectionInfo.adapterName = await connectionModel.getCurrentRemoteSourceAdapterName(connectionInfo);
  }

  // run connection data validation before creating/updating a connection
  await connectionModel.validateData(connectionInfo, spaceId);

  let existingConnObj: IRemoteConnectionWithConfig | undefined; // the connection object of the existing connection if in the edit case.
  let realTimeReplicationStatus;
  if (isCreate) {
    // technical name and remote source name can't be modified on updating a connection
    // connectionInfo.remoteSourceName in connection creation is undefined and in connection updation, it's from the "remoteSourceName" value below.
    // Creating and editing new connections after DW00-594 - Technical Name is without space name prefix. Remote Source Name has a new paradigm (DWC_<space_name>.<connection_name>)
    // Updating connections created before DW00-594 - Technical Name and Remote Source Name remain as before (<space_name>_<connection_name>) as it cannot be modified
    const remoteSourceName = `DWC_${spaceName}.${connectionInfo.name}`;
    connectionInfo.remoteSourceName = remoteSourceName;
  } else {
    const params: IGetObjectParams = {
      ids: connectionInfo.id,
      folderIds: [spaceId],
      space_ids: [spaceId],
      details: [
        "id",
        "name",
        "disReplicationStatus",
        "disConnectionId",
        "remoteSourceName",
        "configuration",
        "adapter",
        "realtimeReplicationStatus",
      ],
      inSpaceManagement: true,
    };

    const connections = await getConnections(context, params);
    if (!Array.isArray(connections) || connections.length === 0) {
      throw new CodedError("connectionNotFound", `Connection ${connectionInfo.name} does not exist.`);
    }
    const existingConnObjIntermed = connections[0];
    if (typeof existingConnObjIntermed.configuration === "string") {
      existingConnObjIntermed.configuration = JSON.parse(existingConnObjIntermed.configuration);
    }
    existingConnObj = existingConnObjIntermed as IRemoteConnectionWithConfig;

    // Prevent modifing dataAccess from federationAndReplication to federationOnly via API.
    if (existingConnObj.typeId === TypeIds.HANA) {
      if (
        existingConnObj.dataAccess === "federationAndReplication" &&
        connectionInfo.configuration.ConnectionFeatures.features.dataAccess !== "federationAndReplication"
      ) {
        throw new CodedError("forbiddenOperation", "Editing dataAcces property is not allowed");
      }
    }

    realTimeReplicationStatus = existingConnObj.realtimeReplicationStatus;
    connectionInfo.remoteSourceName = existingConnObj.remoteSourceName;
    if (
      (await connectionModel.getSupportedCapabilities()).includes(ConnectionCapabilities.HANASDI) &&
      !capabilities.isHanaSdiEnabled
    ) {
      await disableRemoteTables(
        context,
        // Temporary fix for DW101-10601 to use typeId from connectionInfo and not existingConnObj
        // Fetching typeId from the repo triggers migration code and it fails due to incomplete connection details (see DW101-10601).
        // This will be addressed and fixed with DW12-1871.
        connectionInfo.typeId,
        existingConnObj.name,
        existingConnObj.id,
        existingConnObj.remoteSourceName,
        existingConnObj.adapter as RemoteSourceAdapters,
        spaceName,
        spaceId
      );
    }
  }
  // Default the businessName to the technical name
  connectionInfo.businessName = connectionInfo.businessName || connectionInfo.name;

  /**
   * Set adapter location for connections that doesn't require a DP Agent
   * It should be possible to move that down to remote source creation (see DW12-1894)
   */
  if (capabilities.isHanaSdiEnabled) {
    await ConnectionUtils.setAdapterLocation(connectionModel, connectionInfo);
  }

  /**
   * Validate connection properties
   */
  await connectionModel.validate(connectionInfo, spaceId, existingConnObj);

  /**
   * Generate the connection object that is passed to the Design time Repository
   */
  const targetConnRepoObject = generateDeepseaConnRepoObject(spaceId, connectionInfo);

  /**
   * Replicate connections to CCM for DIS
   */
  // "connectionMetaSchema.disReplicationRequired" - For few connections, Data Flows are not enabled at the moment in DWC (but available in CCM), we still replicate the connections to CCM to avoid migration later when Data Flows are enabled
  // isDisDataflowEnabled  can be removed with removal of  INFRA_DWC_TWO_TENANT_MODE
  const usesDataFlows =
    capabilities.isDisDataflowEnabled ||
    capabilities.isDisDataflowSourceEnabled ||
    capabilities.isDisDataflowTargetEnabled;
  const usesReplicationFlow =
    capabilities.isReplicationflowSourceEnabled || capabilities.isReplicationflowTargetEnabled;
  if (usesDataFlows || usesReplicationFlow || connectionMetaSchema.disReplicationRequired) {
    await handleCCMReplication(
      context,
      existingConnObj,
      targetConnRepoObject,
      connectionInfo,
      spaceName,
      realTimeReplicationStatus
    );
  }

  /**
   * Create Open Sql Schema for Partner Connections
   */
  let partnerConnectionSchema: any = {};
  if (capabilities.isPartnerSchemaEnabled && isCreate) {
    partnerConnectionSchema = await createSQLSchemaForPartnerConnections(
      context,
      targetConnRepoObject,
      connectionInfo,
      spaceName
    );
  }

  /**
   * If connection uses HANA cloud connector, ensure that the connectivity proxy is enabled
   * For more information @see HANA as an example connection
   */
  const useHanaSCC = await connectionModel.usesHanaConnectivityProxy(connectionInfo);
  if (useHanaSCC) {
    logInfo(`Connection ${connectionInfo.name} requires HANA Connectivity Proxy`, { context });
    await ensureConnProxyIsEnabled(context, targetConnRepoObject);
  }

  /**
   * Upsert connections in DWC Repository
   */
  let repositoryIds: string[];
  try {
    log(LogLevel.Verbose, "Persisting connection information in the DWC repository.", { context });
    if (logicalConnId) {
      // For the logical connection repository object is is an update and not a create, so the ID is already known
      targetConnRepoObject.id = logicalConnId;
      logInfo(`Overwrite logical connection with id ${logicalConnId} for connection ${connectionInfo.name}`, {
        context,
      });
    }
    const repositoryId = await RepositoryObjectClient.createDocument(context, targetConnRepoObject, {
      inSpaceManagement: true,
      needObjectSyncWithContentLib: false,
    });
    repositoryIds = Object.values(repositoryId).map((el: { id: string }) => el.id);
    if (logicalConnId) {
      targetConnRepoObject.id = undefined;
    } // remove the id again to not affect any further usages of that object
  } catch (upsertObjectErr) {
    // Audit log failed activity (CREATE or UPDATE)
    await auditLogConnectionUpserted(connectionInfo.remoteSourceName, isEdit, AuditStatus.FAIL, context);

    if (isCreate) {
      await rollbackCCMAndPartnerSchema(context, targetConnRepoObject, partnerConnectionSchema, spaceName);
    }

    throw upsertObjectErr;
  }

  /**
   * Upsert remote source in Customer HANA
   */
  const isHdlfDeltaShare =
    connectionInfo.typeId === TypeIds.HDL_FILES &&
    connectionInfo.configuration.ConnectionProperties.configurations?.dataAccessLevel === DataAccessLevel.DeltaShare;
  const isBusinessDataProduct = connectionInfo.typeId === TypeIds.BUSINESS_DATA_PRODUCT;

  if (
    !usesRMS &&
    (connectionInfo.typeId === TypeIds.SAPBWMODELTRANSFER ||
      capabilities.isHanaSdiEnabled ||
      isHdlfDeltaShare ||
      isBusinessDataProduct) &&
    !!connectionInfo.location &&
    repositoryIds &&
    realTimeReplicationStatus !== "ACTIVE"
  ) {
    let remoteSource;
    try {
      // Map connection properties to SDI/SDA remote source properties and delete DIS specific properties
      remoteSource = await connectionModel.getRemoteSourcePayload(connectionInfo);
      await SdiManager.upsertRemoteSource(context, remoteSource);
    } catch (upsertRemoteSourceErr) {
      // Audit log failed activity (CREATE or UPDATE)
      await auditLogConnectionUpserted(connectionInfo.remoteSourceName, isEdit, AuditStatus.FAIL, context);

      /* Rollback on failure to create remote source */
      if (isCreate) {
        await rollbackRepoObjAndCCM(context, targetConnRepoObject, repositoryIds);
      }

      // Throws errors
      await handleUpsertRemoteSourceErrors(
        context,
        upsertRemoteSourceErr,
        connectionInfo.name,
        spaceId,
        connectionInfo.remoteSourceName
      );
    }

    // https://jira.tools.sap/browse/DW101-89838, Workaround for HANA error
    // "failed to create virtual table: SQL Error 133 - transaction rolled back by detected deadlock"
    if (isHdlfDeltaShare || isBusinessDataProduct) {
      await SdiManager.checkRemoteSource(context, connectionInfo.remoteSourceName);
    }

    try {
      await handleClientCredsForRemoteSource(context, featureFlags, remoteSource, connectionInfo);
      await grantSpaceUserToRemote(context, connectionInfo.remoteSourceName, spaceName);
      await grantSpaceSupportRoleToRemote(context, connectionInfo.remoteSourceName, spaceName); // grant privileges for the new remote source to the support role
      if (isHdlfDeltaShare || isBusinessDataProduct) {
        await grantConsumerRoleToRemote(context, connectionInfo.remoteSourceName, spaceName);
      }
    } catch (err) {
      /* Rollback on failure to grant access to remote source */
      if (isCreate) {
        const adapterName = await connectionModel.getCurrentRemoteSourceAdapterName(connectionInfo);
        await rollbackRepoObjAndRemoteSourceAndCCM(
          context,
          targetConnRepoObject,
          connectionInfo,
          adapterName,
          repositoryIds,
          spaceName
        );
      }
      throw err;
    }

    // For connection types S/4HANA OP and BW Model Transfer create virtual tables for metadata import
    if ([TypeIds.SAPS4HANAOP, TypeIds.SAPBWMODELTRANSFER].includes(connectionInfo.typeId)) {
      try {
        const connId = repositoryIds[0] || "";
        const remoteDatabaseName = "<NULL>";
        const schemaName =
          connectionInfo.typeId === TypeIds.SAPBWMODELTRANSFER
            ? connectionInfo.configuration?.ConnectionProperties?.configurations?.database?.schema
            : "<NULL>";
        const sdiMetadataImport = new SdiMetadataImport(
          context,
          spaceName,
          targetConnRepoObject.name,
          connId,
          connectionInfo.typeId,
          connectionInfo.remoteSourceName,
          remoteDatabaseName,
          schemaName
        );
        if (capabilities.isModelTransferEnabled) {
          await sdiMetadataImport.createVirtualTablesForMetadataImport();
        } else {
          await sdiMetadataImport.dropVirtualTablesForMetadataImport();
        }
      } catch (err) {
        // Just log the error at this time and raise an error during connection validation
        logError(err, { context });
      }
    }
  }

  // Audit log activity (CREATE or UPDATE)
  await auditLogConnectionUpserted(connectionInfo.remoteSourceName, isEdit, AuditStatus.SUCCESS, context);

  if (Object.keys(partnerConnectionSchema).length > 0) {
    repositoryIds.push(partnerConnectionSchema);
  }
  try {
    // dynatrace
    if (featureFlags.DWCO_CONNECTION_METRICS) {
      const state = isEdit ? "update" : "create";
      let subType = "none";
      if (connectionInfo.typeId === TypeIds.GENERICJDBC) {
        subType = connectionInfo.configuration?.ConnectionProperties?.configurations?.driverClass || "unknown";
      }
      if (connectionInfo.typeId === TypeIds.OPEN_CONNECTORS) {
        const ocnDetail = await getOCNDetails(context, spaceName);
        subType =
          ocnDetail.ocnInstances.find(
            (item) =>
              item.id.toString() === connectionInfo.configuration?.ConnectionProperties?.configurations?.instanceId
          )?.name || "unknown";
        if (subType !== "unknown") {
          const match = subType.match(/\(([^)]+)\)/);
          subType = match ? match[1] : subType;
        }
      }
      await connectionMetricWrapper.recordLiveUpsertConnectionsMetric(context, connectionInfo, state, subType);
    }
  } catch (error) {
    // Just log the error when collecting metrics fails.
    logError("Error collecting metric:" + error, { context });
  }
  return repositoryIds;
}

export async function handleClientCredsForRemoteSource(
  context: IRequestContext,
  featureFlags: IFeatureFlagsMap,
  remoteSource: RemoteSourceObject,
  connectionInfo: RemoteSourceObject
) {
  if (
    (connectionInfo.typeId === TypeIds.HDL_FILES || connectionInfo.typeId === TypeIds.BUSINESS_DATA_PRODUCT) &&
    connectionInfo.isUclSpaceConnection
  ) {
    // Nothing needs to be done, because the credentials are already saved in a PSE and attached to the remote source
    return;
  }
  // In a first step we check if X509 client credentials are at all supported for the connection type
  // For all other connection types this function does not do anything
  const supportsClientCert = supportsClientCertificate(featureFlags, connectionInfo.typeId, remoteSource.adapterName);
  if (supportsClientCert) {
    // In a second step we see if X509 client credentials are selected as authentication type
    let usesClientCert;
    // In most cases the client certificates are saved in a PSE that is created specifically for a single remote source
    // But for UCL and BW Bridge one PSE can be shared across multiple connections, hence its lifecycle should be independent from the remote sources
    const hasOwnPSE = !connectionInfo.isUclSpaceConnection && connectionInfo.typeId !== TypeIds.SAPBWBRIDGE;
    switch (connectionInfo.typeId) {
      case TypeIds.ABAP:
        usesClientCert =
          (connectionInfo as any).configuration?.ConnectionProperties?.ConnectionInfo?.authentication?.auth_type ===
          AuthTypes.X509;
        break;
      case TypeIds.CDI:
      case TypeIds.SAPS4HANACLOUD:
        const authType = remoteSource.configuration?.ConnectionProperties?.configurations?.connection?.auth_mech;
        usesClientCert = authType === AuthTypes.X509;
        break;
      case TypeIds.HANA:
        if (remoteSource.adapterName === RemoteSourceAdapters.SDI.HanaAdapter) {
          // SDI
          const authType = remoteSource.configuration?.ConnectionProperties?.configurations?.security?.auth_mech;
          usesClientCert = authType === AuthTypes.X509;
        } else {
          // SDA
          const authType = remoteSource.configuration?.ConnectionProperties?.connectionproperties?.auth_mech;
          usesClientCert = authType === AuthTypes.X509;
        }
        break;
      case TypeIds.HDL_FILES:
        usesClientCert = true;
        break;
      case TypeIds.HDLDB:
        usesClientCert =
          (connectionInfo as any).configuration?.ConnectionProperties?.configurations.authentication?.auth_type ===
          AuthTypes.X509;
        break;
      case TypeIds.SAPBWBRIDGE:
        usesClientCert = !!connectionInfo.configuration?.ConnectionProperties?.connectionproperties?.pseName;
    }
    if (usesClientCert) {
      if (hasOwnPSE) {
        // Create a PSE, save the credentials in it and attach the PSE to the remote source
        await upsertClientCredsForRemoteSource(
          context,
          remoteSource.remoteSourceName,
          connectionInfo.configuration?.CredentialProperties?.x509_client_credential
        );
        if (connectionInfo.typeId === TypeIds.HDL_FILES) {
          // For HDLF it is not enough to have TLS certificates stored in the central PSE with purpose remote source. They need to be saved in the same PSE as the client certificates.
          await attachTLSCertificateHDLF(
            context,
            remoteSource.remoteSourceName,
            connectionInfo.configuration?.ConnectionProperties?.configurations?.tlsServerCertificateFingerprint || ""
          );
        }
      } else {
        // For space connections and BW bridge connections the client credentials (and in case of HDLF also the TLS certificates) are already saved in a PSE and just need to be attached to the remote source
        const hanaCredClient = await HanaCredentialClient.fromContext(context);
        let pseName;
        if (connectionInfo.isUclSpaceConnection) {
          // For UCL the PSE name is part of the connection payload, only for old connections it needs to be derived
          pseName =
            connectionInfo.configuration?.ConnectionProperties?.configurations?.pseName ||
            hanaCredClient.getUclPseNameWithoutTypeId(connectionInfo.uclAssignmentId!);
        } else {
          // For BW bridge the PSE name is provided as part of the connection payload
          pseName = connectionInfo.configuration?.ConnectionProperties?.connectionproperties?.pseName;
          logInfo(`Client credentials for BW Bridge connection ${connectionInfo.name} are saved in PSE ${pseName}`, {
            context,
          });
        }
        // Atach the PSE to the remote source
        await hanaCredClient.attachCredential(remoteSource.remoteSourceName, pseName);
      }
    } else if (hasOwnPSE) {
      // If the lifecycle of the PSE is tied to the remote source and the client certificate is not used try to delete the PSE and credential assignment
      // It could be that a connection with client certificate is edited and changed to another authentication type
      // Note that for HDLF it is not possible to change a connection from Delta Share (Client Credentials) to File System (Keystore Credentials)
      await deleteClientCredsForRemoteSource(context, remoteSource.remoteSourceName);
    } else {
      // This case could happen, if you create a BW Bridge connection with Basic Auth, UCL connections always use client certificates
      logInfo(
        `Client certificates are supported for connection type ${connectionInfo.typeId}, but not used in connection ${connectionInfo.name}`,
        { context }
      );
    }
  }
}
/**
 *
 * @param featureFlags The map of feature flags
 * @param typeId The connection type
 * @param adapterName The name of the remote source adapter
 * @returns true if client certificates are supported on the connection type, false otherwise
 */
function supportsClientCertificate(
  featureFlags: IFeatureFlagsMap,
  typeId: TypeIds,
  adapterName: RemoteSourceAdapters
): boolean {
  const supportedTypeIds = [
    TypeIds.SAPS4HANACLOUD,
    TypeIds.CDI,
    TypeIds.HANA,
    TypeIds.BUSINESS_DATA_PRODUCT,
    TypeIds.SAPBWBRIDGE,
    TypeIds.ABAP,
  ];
  if (featureFlags.DWCO_CONNECTION_HDLDB_X509) {
    supportedTypeIds.push(TypeIds.HDLDB);
  }

  if (featureFlags.DWCO_CONNECTION_ABAP_SQL_X509) {
    supportedTypeIds.push(TypeIds.ABAP);
  }

  const isHDLFDeltaShare = typeId === TypeIds.HDL_FILES && adapterName === RemoteSourceAdapters.SDA.File;
  const supportsClientCert = isHDLFDeltaShare || supportedTypeIds.includes(typeId);
  return supportsClientCert;
}

async function upsertClientCredsForRemoteSource(
  context: IRequestContext,
  remoteSourceName: string,
  clientCredentials: any,
  client?: HanaCredentialClient
) {
  logInfo(`Upsert client credentials for remote source ${remoteSourceName}`, { context });
  const { x509_client_certificate, x509_client_private_key, x509_client_private_key_password } = clientCredentials;
  const certBundle = ConnectionUtils.createCertificateBundle(
    x509_client_certificate,
    x509_client_private_key,
    x509_client_private_key_password
  );
  const hanaCredClient = client || (await HanaCredentialClient.fromContext(context));
  await hanaCredClient.upsertCertBundle(remoteSourceName, certBundle);
  await hanaCredClient.attachCredential(remoteSourceName);
}

async function deleteClientCredsForRemoteSource(
  context: IRequestContext,
  remoteSourceName: string,
  client?: HanaCredentialClient
) {
  logInfo(`Deleting client credentials for remote source ${remoteSourceName}`, { context });
  const hanaCredClient = client || (await HanaCredentialClient.fromContext(context));
  await hanaCredClient.dropCredential(remoteSourceName);
  await hanaCredClient.deleteCredentialPSE(remoteSourceName);
}

function generateDeepseaConnRepoObject(spaceGuid: string, connectionInfo: any) {
  const connRepoObject: any = {
    id: connectionInfo.id,
    name: connectionInfo.name,
    folderId: spaceGuid,
    inSpaceManagement: true,
    documentType: "csn",
    content: {
      objects: {},
    },
  };

  // Generate object from connection payload
  const objectsContent = ConnectionUtils.deepCopy(connectionInfo);
  // Add missing properties
  objectsContent.kind = "repository.remote";
  if (connectionInfo.typeId === TypeIds.HANA) {
    objectsContent.dataAccess = connectionInfo.configuration.ConnectionFeatures.features.dataAccess;
  }
  // Delete properties that are already on the root level and don't need not to be part of the content object
  delete objectsContent.id;
  delete objectsContent.space_ids;
  // Credentials are not persisted in the repository
  delete objectsContent.configuration?.CredentialProperties;

  // During update a logical connection turns into a regular one
  if (!!connectionInfo.isLogical) {
    objectsContent.isLogical = false;
  }

  // Set the repository package in case one is selected
  const repoPackage = connectionInfo.repositoryPackage;
  if (repoPackage) {
    if (repoPackage !== RepositoryPackageNone) {
      objectsContent._meta = {};
      objectsContent._meta["#repositoryPackage"] = repoPackage;
    }
    delete objectsContent.repositoryPackage;
  }

  connRepoObject.content.objects[connectionInfo.name] = objectsContent;

  return connRepoObject;
}

async function handleCCMReplication(
  context: IRequestContext,
  existingConnObj: IRemoteConnection | undefined,
  targetConnRepoObject: any,
  connectionInfo: any,
  spaceName: string,
  realTimeReplicationStatus: string | undefined
) {
  if (connectionInfo.isUclSpaceConnection) {
    // UCL space connections come without credentials as they were saved in a PSE when creating the shared connection
    if (connectionInfo.typeId === TypeIds.HDL_FILES || connectionInfo.typeId === TypeIds.BUSINESS_DATA_PRODUCT) {
      // UCL space connections of Type HDLF or BUSINESS_DATA_PRODUCT should not be replicated to CCM as they only require a remote source
      return;
    }
    // Retrieve the UCL credentials and add them to the payload
    await getUCLCredentialsForCCMReplication(context, connectionInfo);
  }
  const isEdit = !!connectionInfo.id;
  const conn = ConnectionFactory.fromTypeId(connectionInfo.typeId, context);
  const usesRMS = await conn.usesRMSForHanaRemoteSource(connectionInfo.configuration);

  if (isEdit && realTimeReplicationStatus === "ACTIVE" && !usesRMS) {
    // We check for realTimeReplicationStatus here, because the ccm replication is skipped if realtime replication is active and then we take just the values that are in the
    // repository DB to keep the current state of the DIS replication

    existingConnObj = existingConnObj as IRemoteConnection; // Invariant that on edit there is an existing connection.
    Object.assign(targetConnRepoObject.content.objects[targetConnRepoObject.name], {
      disConnectionId: existingConnObj.disConnectionId,
      disReplicationStatus: existingConnObj.disReplicationStatus,
    });
    return;
  }

  try {
    if (connectionInfo.typeId === TypeIds.SIGNAVIO) {
      const connProps = connectionInfo.configuration.ConnectionProperties;
      const signavioBaseUrl: string = connProps.configurations.endpoint;
      const credProps = await (conn as SIGNAVIO).retrieveSignavio(signavioBaseUrl);
      Object.assign(connectionInfo.configuration.CredentialProperties, credProps);
      // also update other Signavio connections in Datasphere with new S3 credentials
      const ccmIds = await (conn as SIGNAVIO).updateSignavioCredentials(signavioBaseUrl, credProps, connectionInfo.id);
      logInfo(`Updated ${ccmIds.length} CCM connections with new certificate`, { context });
    }
    const ccmConnId = existingConnObj?.disConnectionId;
    const guids = await upsertConnectionToCCM(context, connectionInfo, ccmConnId, isEdit, spaceName);
    Object.assign(targetConnRepoObject.content.objects[targetConnRepoObject.name], {
      disConnectionId: guids.connectionId,
      disReplicationStatus: CCMReplicationStatus.SUCCESSFULLY_REPLICATED,
    });
  } catch (err) {
    if (await conn.failConnectionUpsertOnCCMReplicationFailure(connectionInfo.configuration)) {
      err.message = `The connection synchronization with an internal service (CCM) failed. ${err.message}`;
      throw err;
    }
    Object.assign(targetConnRepoObject.content.objects[targetConnRepoObject.name], {
      disReplicationStatus: CCMReplicationStatus.REPLICATION_FAILED,
    });
    logError([err], { context });
  }
}

async function createSQLSchemaForPartnerConnections(
  context: IRequestContext,
  connRepoObject: any,
  connectionInfo: any,
  spaceName: string
) {
  let partnerConnectionSchema: any = {};
  try {
    const schema = await ConnectionUtils.createSchema(context, connectionInfo.name, spaceName);
    partnerConnectionSchema = {
      schema,
    };

    Object.assign(connRepoObject.content.objects[connRepoObject.name], {
      schemaName: schema.name,
    });

    // for partners connections where IP List is given, check if the IPs are in the IP Allowlist
    if (connectionInfo.partnerIpAllowList) {
      let isPartnerIpAllowListValid = false;
      if (connectionInfo.partnerIpAllowList.length > 0) {
        // retrieve Ip Allow List
        try {
          const ipAllowList = await getIpAllowlistJson(context);
          // compare with Ip allow list
          const missingIpList = ipAllowList
            ? connectionInfo.partnerIpAllowList.filter((e: string) => !ipAllowList.ipAllowlist.includes(e))
            : connectionInfo.partnerIpAllowList;
          if (missingIpList.length === 0) {
            isPartnerIpAllowListValid = true;
          }
        } catch (err) {
          logError([`Failed to get Ip Allowlist for connection :: ${connectionInfo.name}.`, err], { context });
        }
      }

      Object.assign(connRepoObject.content.objects[connRepoObject.name], {
        isPartnerIpAllowListValid,
      });
    }
    return partnerConnectionSchema;
  } catch (err) {
    logError([`Failed to create Open Sql Schema for connection :: ${connectionInfo.name?.toUpperCase()}.`, err], {
      context,
    });
    throw err;
  }
}

async function ensureConnProxyIsEnabled(context: IRequestContext, connRepoObject: any) {
  const bcContext = RequestContext.createNewForBackground(context);
  const sccConfig = await HanaSCCConfigClient.fromRequestContext(bcContext);

  // We do this in background because its long running. We just want to eventually reach enablement. Additional check on validation.
  sccConfig.ensureConnectivityProxyEnabled().then(
    () => bcContext.finish(),
    (bcErr) => {
      logError(["[Hana Connectivity Proxy] Ensuring connectivity proxy enabled failed.", bcErr], {
        context: bcContext,
      });
      return bcContext.finish();
    }
  );
}

async function rollbackCCMAndPartnerSchema(
  context: IRequestContext,
  connRepoObject: any,
  partnerConnectionSchema: any,
  spaceName: string
) {
  // Delete connection from CCM (Rollback)
  const ccmRelicationStatus = connRepoObject.content.objects[connRepoObject.name].disReplicationStatus;
  const disConnectionId =
    connRepoObject.properties?.disConnectionId || connRepoObject.content.objects[connRepoObject.name].disConnectionId;
  if (ccmRelicationStatus === CCMReplicationStatus.SUCCESSFULLY_REPLICATED) {
    const ccm = new CCMClient(context);
    const connectionId = disConnectionId;
    try {
      await ccm.deleteConnection(connectionId); // Rollback DIS replication
    } catch (rollbackErr) {
      logError(
        [
          `[DI Proxy Service] {connections api} delete for CCM rollback failed connection with CCM Conn Id :: ${connectionId}.`,
          rollbackErr,
        ],
        { context }
      );
    }
  }

  if (!!partnerConnectionSchema.schema?.name) {
    /* Rollback open sql schema creation from DWC Repo */
    try {
      await ConnectionUtils.deleteSchemas(context, [partnerConnectionSchema.schema.name], spaceName);
    } catch (rollbackErr) {
      logError([`Failed to delete ${partnerConnectionSchema.schema.name} Open Sql Schema`, rollbackErr], { context });
    }
  }
}

async function rollbackRepoObjAndCCM(context: IRequestContext, connRepoObject: any, repositoryIds: any) {
  // Delete connection object from DWC Repo (Rollback)
  await RepositoryObjectClient.deleteObject(context, repositoryIds);

  // Delete connection from CCM (Rollback)
  const ccmRelicationStatus = connRepoObject.content.objects[connRepoObject.name].disReplicationStatus;
  const disConnectionId =
    connRepoObject.properties?.disConnectionId || connRepoObject.content.objects[connRepoObject.name].disConnectionId;
  if (ccmRelicationStatus === CCMReplicationStatus.SUCCESSFULLY_REPLICATED) {
    const ccm = new CCMClient(context);
    const connectionId = disConnectionId;
    try {
      await ccm.deleteConnection(connectionId);
    } catch (rollbackErr) {
      logError(
        [
          `[DI Proxy Service] {connections api} delete for CCM rollback failed for connection with CCM Conn Id :: ${connectionId}.`,
          rollbackErr,
        ],
        { context }
      );
    }
  }
}

async function rollbackRepoObjAndRemoteSourceAndCCM(
  context: IRequestContext,
  connRepoObject: any,
  connectionInfo: any,
  adapterName: RemoteSourceAdapters,
  repositoryIds: any,
  spaceName: string
) {
  // Delete connection object from DWC Repo (Rollback)
  await RepositoryObjectClient.deleteObject(context, repositoryIds);

  const { typeId, name, remoteSourceName } = connectionInfo;

  // Delete remote source from Customer HANA (Rollback)
  await deleteVirtualTablesAndRemoteSource(
    context,
    remoteSourceName,
    adapterName,
    name,
    repositoryIds[0],
    typeId,
    spaceName,
    connectionInfo.isUclSpaceConnection
  );

  // Delete connection from CCM (Rollback)
  const ccmRelicationStatus = connRepoObject.content.objects[connRepoObject.name].disReplicationStatus;
  const disConnectionId =
    connRepoObject.properties?.disConnectionId || connRepoObject.content.objects[connRepoObject.name].disConnectionId;
  if (ccmRelicationStatus === CCMReplicationStatus.SUCCESSFULLY_REPLICATED) {
    const ccm = new CCMClient(context);
    const connectionId = disConnectionId;
    try {
      await ccm.deleteConnection(connectionId);
    } catch (rollbackErr) {
      logError(
        [
          `[DI Proxy Service] {connections api} delete for CCM rollback failed for connection with CCM Conn Id :: ${connectionId}.`,
          rollbackErr,
        ],
        { context }
      );
    }
  }
}

async function validateConnectionDeletion(
  context: IRequestContext,
  connections: IRemoteConnection[],
  options?: IValidateDeleteOptions,
  calledFromSpaceManagement: boolean = false
) {
  if (connections.length > 0) {
    const validationResults = await RepositoryObjectClient.canDeleteObjects(
      context,
      connections.map((o) => o.id),
      options,
      calledFromSpaceManagement
    );
    if (Array.isArray(validationResults) && validationResults.length > 0) {
      validationResults.forEach((vm) => {
        if (vm.severity === ValidationMessageSeverity.Error) {
          throw new ValidationError(validationResults, vm.fullMessage);
        }
      });
    }
  }
}

export async function validateDeletionOfAllConnections(context: IRequestContext, spaceUuid: string, spaceId: string) {
  const connections = await getConnsForDelete(context, spaceUuid);
  // validateDeletionOfAllConnections is always called from space management, so calledFromSpaceManagement is set to true (introduced for DW101-66264)
  await validateConnectionDeletion(
    context,
    connections,
    {
      connectionValidation: { action: DeleteConnectionAction.DELETE_SPACE, spaceList: [spaceUuid], validate: true },
      spaceName: spaceId,
    } as IValidateDeleteOptions,
    true
  );
}

export async function deleteConnections(context: IRequestContext, spaceId: string, ids: string[]) {
  const spaceName = await getSpaceNameFromUuid(context, spaceId);
  // in non-SDP mode 'hasPrivilegeOnScope' internally calls 'hasPrivilegesOnType'
  if (!context.hasPrivilegeOnScope(spaceName, AuthType.DWC_REMOTECONNECTION, Activity.delete)) {
    throw new CodedError(
      "insufficientDeleteConnectionPrivilege",
      "Insufficient privilege: User is not allowed to delete a connection.",
      StatusCodes.FORBIDDEN
    );
  }
  // TODO what happens if one out of many deletions failes. Do we really need this function (easier with single delete)?
  if (ids.length === 0) {
    return;
  }

  const connections = await getConnsForDelete(context, spaceId, ids);

  await validateConnectionDeletion(context, connections); // validate design time dependecies
  await deleteConnectionsRuntimeData(context, connections); // remove runtime artifacts
  await RepositoryObjectClient.deleteObject(
    context,
    connections.map((conn) => conn.id)
  );

  // Audit log activity (DELETE) -- sequentially
  for (const connection of connections) {
    await auditLogConnectionDeleted(connection.name, AuditStatus.SUCCESS, context);
  }
}

async function getConnsForDelete(context: IRequestContext, spaceId: string, ids?: string[]) {
  let query = {
    space_ids: spaceId,
    details: [
      "id",
      "name",
      "typeId",
      "remoteSourceName",
      "disConnectionId",
      "capabilityPartnerSchema",
      "schemaName",
      "configuration",
      "isLogical",
      "isUclSpaceConnection",
    ],
    inSpaceManagement: true,
  };

  if (ids) {
    query = { ...query, ...{ ids } };
  }
  // allowAccess = true, because for deletion we should not check read-privilege
  const connections = await getConnections(context, query, true);
  connections.forEach((conn) => {
    if (conn.configuration && typeof conn.configuration === "string") {
      conn.configuration = JSON.parse(conn.configuration);
    }
  });

  return connections;
}

export async function deleteAllConnectionsRuntimeDataForSpaceDeletion(context: IRequestContext, spaceId: string) {
  const connections = await getConnsForDelete(context, spaceId);
  await deleteConnectionsRuntimeData(context, connections, true);
}

async function deleteConnectionsRuntimeData(
  context: IRequestContext,
  connections: IRemoteConnection[],
  isSpaceDeletion?: boolean
) {
  for (const conn of connections) {
    // Logical Connections don't have any runtime artefacts, so nothing needs to be done
    if (conn.isLogical) {
      continue;
    }

    // DELETE CCM ARTEFACT
    if (conn.disConnectionId) {
      await deleteCCMConn(conn, context);
    }

    // DELETE SCHEMA
    const spaceName = await getSpaceNameFromUuid(context, conn.space_id);
    if (conn.capabilityPartnerSchema === "true" && !!conn.schemaName) {
      await ConnectionUtils.deleteSchemas(context, [conn.schemaName], spaceName, true);
    }

    // DELETE REMOTE SOURCE
    const connModel = ConnectionFactory.fromTypeId(conn.typeId, context);
    const usesRMS = await connModel.usesRMSForHanaRemoteSource(conn.configuration as DWCConnectionConfiguration);
    if (usesRMS) {
      // remote sources of RMS connections are maintained by HANA sync service, no deletion here
      continue;
    }

    if (!!conn.remoteSourceName) {
      // With tryGetCurrentRemoteSourceAdapterName we catch and still run the delete operation if the connection does not use a remote source (e.g. S3) and has no adatper name.
      // This is a simple and robust solution. If no remote source exists, we assume it never existed or was already deleted. We continue.
      const adapterName = await connModel.tryGetCurrentRemoteSourceAdapterName(conn);
      await deleteVirtualTablesAndRemoteSource(
        context,
        conn.remoteSourceName,
        adapterName,
        conn.name,
        conn.id,
        conn.typeId,
        spaceName,
        isSpaceDeletion,
        conn.isUclSpaceConnection
      );
    } else {
      logInfo(`Remote source for connection ${conn.name} not found. Assumed to be already deleted. Continued`, {
        context,
      });
    }
  }
}

async function deleteCCMConn(conn: IRemoteConnection, context: IRequestContext) {
  const connectionId = conn.disConnectionId;
  if (!connectionId) {
    throw new Error(`No CCM connection id present for ${conn.name}`);
  }
  // Delete connection from CCM
  const ccm = new CCMClient(context);
  try {
    // It might happen that after deleting the CCM connection the overall connection deletion fails and we have a connection
    // that has the flag SUCCESSFULLY_REPLICATED and the CCM connection id set but there is no CCM connection anymore.
    // That is an inconsistency that can also happen in case of DI tenant remapping (where CCM connections are not moved).
    // In connection validation this case is checked and shown to the user.
    await ccm.deleteConnection(connectionId);
  } catch (err) {
    if (err instanceof CCMConnectionsError && err.code === CCMConnectionsErrorCode.NOT_FOUND) {
      logInfo(
        `Deletion of CCM connection with id ${connectionId} was skipped. Not found and assumed to be already deleted.`,
        { context }
      );
    } else {
      const connModel = ConnectionFactory.fromTypeId(conn.typeId, context);
      // for shared connections such as RMS based ones, the CCM connection must be deleted to delete the remote source (HANA sync service maintains the remote source).
      if (await connModel.usesRMSForHanaRemoteSource(conn.configuration as DWCConnectionConfiguration)) {
        throw err;
      }
      // TODO distinguish between CCM not reachable and other errors e.g. 400
      // If CCM is not reachable we still continue and ignore the inconsistency. Left overs are handled in the upsert case.
      // We do this for testing and dev environments as CCM is frequently down and space deletion is required by tests.
      logError([`Deletion of CCM connection with id ${connectionId} failed. Failure ignored.`, err], { context });
    }
  }
}

/**
 * Get the connection type data for all connection types
 * @param context Request Context
 * @param spaceName Space Name
 *
 * If anything fails for a particular connection type, the connection would be omitted from the result
 * If FF for a particular connection type is not active, the connection would be omitted from the result
 * Some details will be omitted from the result if space name is not provided
 */
export async function getAllConnTypesData(context: IRequestContext, spaceName?: string): Promise<ConnectionTypeData[]> {
  let supportedTypeIds: TypeIds[] = [];
  if (spaceName) {
    supportedTypeIds = await ConnectionUtils.getSupportedTypeIdsForSpace(context, spaceName);
  } else {
    supportedTypeIds = await ConnectionUtils.getSupportedConnectionTypes(context);
  }

  const adapterLocations = await getAdapterLocations(context);
  const diIpAddress = await getDIEmbeddedTenantIp(context);
  let ocnDtls: any = {};
  if (spaceName) {
    ocnDtls = await getOCNDetails(context, spaceName);
  }

  const allTypesData: ConnectionTypeData[] = [];
  for (const typeId of supportedTypeIds) {
    try {
      // Check if the FF for the connection type is active
      const connModel = ConnectionFactory.fromTypeId(typeId, context);
      const metaschema = await connModel.getAdjustedMetaschema();

      // Partner app is always disabled in canary
      if (metaschema.typeId === TypeIds.PartnerApp && !isCanary()) {
        continue;
      } else if (metaschema.featureFlag) {
        if (!(await FeatureFlagProvider.isFeatureActive(context, metaschema.featureFlag as any))) {
          continue;
        }
      }

      const typeData = await getConnTypeDataHelper(
        context,
        typeId,
        metaschema,
        spaceName,
        adapterLocations,
        diIpAddress,
        ocnDtls
      );

      allTypesData.push(typeData);

      // For partner connection tiles that were created by the user. See DW00-3615
      // The defaults are still taken from the PartnerApp metaschema
      // except for the below properties that are replaced by the ones created by the user
      const featureFlags = await FeatureFlagProvider.getFeatureFlags(context);
      if (spaceName && featureFlags.DWCO_CONNECTION_PARTNER_TEST && typeData.typeId === TypeIds.PartnerApp) {
        const partnerTileConfiguration = PartnerTileConfiguration.fromRequestContext(context);
        const partnerTiles = await partnerTileConfiguration.getPartnerTileConfiguration(spaceName);
        if (partnerTiles.length > 0) {
          const regEx = new RegExp("\\.", "g");
          partnerTiles.forEach((ele) => {
            allTypesData.push({
              ...typeData,
              partner: ele.name,
              iconLocation: ele.image,
              iFrameUrlConnectionCreation: ele.iFrameURL,
              iFramePostMessageOrigin: ele.iFramePostMessageOrigin.replace(regEx, "\\.") + "$",
            });
          });
        }
      }
    } catch (err) {
      logError([`{getAllConnTypesData} Failed to get type data for ${typeId}.`, err], { context });
    }
  }

  // return only supported connection types for LSA spaces
  if (spaceName && (await ConnectionUtils.isHdlfSpace(context, spaceName))) {
    return allTypesData.filter(
      (typeData) =>
        typeData.capabilities.includes(ConnectionCapabilities.REPLICATIONFLOWSOURCE) ||
        typeData.capabilities.includes(ConnectionCapabilities.REPLICATIONFLOWTARGET)
    );
  }

  return allTypesData;
}

/**
 * Get the connection type data for the given connection type
 * @param context Request Context
 * @param typeId Connection type id e.g. ABAP
 */
export async function getConnTypeDataById(
  context: IRequestContext,
  typeId: TypeIds,
  spaceName: string
): Promise<ConnectionTypeData> {
  if (!(await ConnectionUtils.isTypeIdSupportedForSpace(context, typeId, spaceName))) {
    throw new CodedError(
      `unsupportedConnectionTypeError`,
      `Connection type ${typeId} is not supported in space ${spaceName}.`,
      StatusCodes.BAD_REQUEST
    );
  }

  // Check if the FF for the connection type is active
  const connModel = ConnectionFactory.fromTypeId(typeId, context);
  const metaschema = await connModel.getAdjustedMetaschema();
  if (metaschema.featureFlag) {
    const isFFActive =
      (await FeatureFlagProvider.isFeatureActive(context, metaschema.featureFlag as any)) ||
      (metaschema.featureFlag === "DWC_CONNECTION_PARTNER_MOCK" && isCanary());
    if (!isFFActive) {
      throw new CodedError(`inactiveFeatureFlag`, `Feature flag for ${typeId} is not active.`, StatusCodes.FORBIDDEN);
    }
  }

  try {
    const connTypeData = await getConnTypeDataHelper(context, typeId, metaschema, spaceName);
    return connTypeData;
  } catch (err) {
    logError([`{getConnTypeDataById} Failed to get type data for ${typeId}.`, err], { context });
    throw err;
  }
}

async function getConnTypeDataHelper(
  context: IRequestContext,
  typeId: TypeIds,
  metaschema: IMetaschema,
  spaceName?: string, // Some details will be omitted from the result if space name is not provided
  adapterLocations?: SdiManager.IAdapterLocationMap[],
  diIpAddress?: string,
  ocnDtls?: { ocnConfigured: boolean; ocnInstances: IOCNInstance[] }
): Promise<ConnectionTypeData> {
  const connModel = ConnectionFactory.fromTypeId(typeId, context);
  let supportedCaps = await connModel.getSupportedCapabilities();

  // filter capabilities for LSA space -> only replication flows are supported
  // this must be done earlier to avoid remote table related properties "adapter",
  // "dpAgents" etc. to be added
  if (spaceName && (await ConnectionUtils.isHdlfSpace(context, spaceName))) {
    supportedCaps = supportedCaps.filter((cap) =>
      [ConnectionCapabilities.REPLICATIONFLOWSOURCE, ConnectionCapabilities.REPLICATIONFLOWTARGET].includes(cap)
    );
  }

  const typeData: ConnectionTypeData = {
    ...metaschema,
    capabilities: supportedCaps,
  };

  if (
    (await FeatureFlagProvider.isFeatureActive(context, "DWCO_USER_PROPAGATION")) &&
    [TypeIds.SAPS4HANACLOUD, TypeIds.SAPS4HANAOP].includes(typeId)
  ) {
    typeData.userPropagationEnabled = await SdiManager.checkUserPropagation(context);
  }

  const hasRemoteTableCap =
    supportedCaps.includes(ConnectionCapabilities.HANASDI) ||
    supportedCaps.includes(ConnectionCapabilities.MODELTRANSFER);
  if (hasRemoteTableCap) {
    const supportedSDIAdapter = filterForSDIAdapterName(connModel.getSupportedRemoteSourceAdapterNames());
    if (!!supportedSDIAdapter) {
      // We set the adapter to be used on the UI (e.g. check DP agent adapters for connection creation tile)
      typeData.adapter = { id: supportedSDIAdapter };
      if (metaschema.dpAgentRequired) {
        if (adapterLocations === undefined) {
          adapterLocations = await getAdapterLocations(context);
        }
        // Add dpAgents to response payload
        const adapterLocation: SdiManager.IAdapterLocationMap | undefined = adapterLocations.find(
          (agent: SdiManager.IAdapterLocationMap) => supportedSDIAdapter === agent.adapterName
        );
        // adapterLocation.locations[x].location is always "agent" here
        typeData.dpAgents = adapterLocation?.locations;
      }
    }
  }

  // For some connections, for ex. Redshift, the user needs to know the DI Embedded IP address
  // to add it in the allowlist of the source system
  if (metaschema.ipWhitelistingRequired) {
    if (diIpAddress === undefined) {
      diIpAddress = await getDIEmbeddedTenantIp(context);
    }
    typeData.ipAddresses = [diIpAddress];
  }

  // For open connectors
  if (spaceName && metaschema.typeId === TypeIds.OPEN_CONNECTORS) {
    if (ocnDtls === undefined) {
      ocnDtls = await getOCNDetails(context, spaceName);
    }
    typeData.ocnConfigured = ocnDtls.ocnConfigured;
    typeData.ocnInstances = ocnDtls.ocnInstances;
  }
  // For HDLF Delta Share the list of uploaded TLS server certificates is needed
  if (
    (await FeatureFlagProvider.isFeatureActive(context, "DWCO_CONNECTION_HDLFILES_X509")) &&
    typeId === TypeIds.HDL_FILES
  ) {
    const certificates: ICertificateInfo[] = await getCertificates(context);
    typeData.TLSCertificates = certificates;
  }
  return typeData;
}

async function getAdapterLocations(context: IRequestContext) {
  let adapterLocations: SdiManager.IAdapterLocationMap[] = [];
  try {
    adapterLocations = await SdiManager.getAdapterLocations(context);
  } catch (err) {
    logError([`Failed to get adapter locations.`, err], { context });
  }
  return adapterLocations;
}

export async function getOCNDetails(context: IRequestContext, spaceName: string) {
  let ocnConfig: IOCNConfiguration | undefined;
  let ocnInstances: IOCNInstance[] = [];
  let ocnConfigured = false;
  try {
    ocnConfig = await getOCNConfiguration(context, spaceName);
  } catch (err) {
    logError([`Failed to get OCN configuration.`, err], { context });
  }

  if (ocnConfig) {
    const baseURL = ocnConfig.properties.baseURL;
    const userSecret = ocnConfig.properties.credentials.userSecret;
    const orgSecret = ocnConfig.properties.credentials.organizationSecret;
    try {
      ocnInstances = await getOCNInstances(context, baseURL, userSecret, orgSecret);
      ocnConfigured = true;
    } catch (err) {
      logError([`Failed to get OCN instances.`, err], { context });
    }
  }
  return { ocnConfigured, ocnInstances };
}

async function getDIEmbeddedTenantIp(context: IRequestContext) {
  const res: any = await makeDisRequest(
    context,
    { url: `${DIS_IP_ADDRESS_URL}`, method: "GET" },
    ExternalCallCategory.DI
  );
  switch (res.status) {
    case StatusCodes.OK:
      return res.body.outboundPublicIp as string;
    default:
      const statusMsg = res.statusText ?? Status.getStatusText(res.status);
      logError(
        [
          `[DI Proxy Service] {version api} failed. Status Code ${
            res.status
          }, StatusMessage: ${statusMsg}, Cause: ${JSON.stringify(res.body ?? {})}.`,
        ],
        { context }
      );
      return "";
  }
}

/**
 * Retrieve UI metadata configuration for given connection type
 * @param context RequestContext
 * @param typeId Connection type id e.g. ABAP
 */
export async function getConnectionUIMetadata(context: IRequestContext, typeId: TypeIds, spaceName: string) {
  try {
    log(LogLevel.Info, `Retrieving UI metadata configuration for ${typeId} connection`, { context });
    const isTypeIdSupported = await ConnectionUtils.isTypeIdSupportedForSpace(context, typeId, spaceName);
    if (!isTypeIdSupported) {
      const msg = [TypeIds.ADL_GEN1, TypeIds.SAPMDI].includes(typeId)
        ? `Connection type ${typeId} is not supported anymore. Please delete all remaining ${typeId} connections.`
        : `${typeId} is not available in space ${spaceName}`;
      throw new CodedError(`connTypeNotSupportedInSpace`, msg, StatusCodes.FORBIDDEN);
    }

    const connectionModel = ConnectionFactory.fromTypeId(typeId, context);
    return await connectionModel.getAdjustedUIMetadata();
  } catch (err) {
    logError([`Failed to read UI metadata configuration for connection type :: ${typeId}.`, err], { context });
    throw err;
  }
}

/**
 * Retrieves the HANA DB information of the BW/4HANA system for the given Live Data Connection (Tunnel) via LCS service
 *
 * @param context RequestContext
 * @param sacTunnelConnection name of the Live Data Connection (Tunnel)
 *
 *  @throws {BW4HanaDbInfoError}
 */
// TODO: Move this function to the dedicated metadataImport folder
export async function getTunnelBw4HanaDbInfo(
  context: IRequestContext,
  sacTunnelConnection: string
): Promise<IBw4HanaDbInfo> {
  const res: ResolvedResponse = await makeLcsRequest(
    context,
    {
      url: `/scc/tunnel/${sacTunnelConnection}/sap/bw4/v1/dwc/dbinfo`,
      method: "GET",
    },
    ExternalCallCategory.SACLCS,
    sacTunnelConnection,
    ALL_STATUS_CODES
  );

  if (res.status === StatusCodes.OK) {
    const bw4HanaDbInfo = res.body as IBw4HanaDbInfo;
    // Although the status is OK, one of the returned values could be empty. (Applicable for cases when SAP BW/4HANA system is hosted by a third party cloud provider).
    if (!bw4HanaDbInfo.host || !bw4HanaDbInfo.port || !bw4HanaDbInfo.schema) {
      throw new BW4HanaDbInfoError(
        `[LCS Service] The host/port/schema retrieved from the HANA DB of the BW/4HANA system for live data connection (tunnel) ${sacTunnelConnection} is empty.`,
        BW4HanaDbInfoErrorCodes.HANA_DB_INFO_EMPTY
      );
    }
    return bw4HanaDbInfo;
  } else {
    const statusMessage = res.statusText ?? Status.getStatusText(res.status);
    const causes = JSON.stringify(res.body ?? {});
    throw new BW4HanaDbInfoError(
      `[LCS Service] failed to retrieve the HANA DB information of the BW/4HANA system for live data connection (tunnel) :: ${sacTunnelConnection}.
       Status Code : ${res.status}, Status Message : ${statusMessage}, Cause: ${causes}`
    );
  }
}

async function disableRemoteTables(
  context: IRequestContext,
  typeId: TypeIds,
  connName: string,
  connId: string,
  remoteSourceName: string,
  remoteSourceAdapterName: RemoteSourceAdapters,
  spaceName: string,
  spaceUUID: string
) {
  // While editing a connection, delete remote source if the user disables remote tables feature
  // Before deleting a remote source, check if there are dependent objects (e.g. a remote table)
  await validateDisableRemoteTables(context, connId, spaceUUID);

  // There are no dependent objects, remote source can be safely deleted
  try {
    await deleteVirtualTablesAndRemoteSource(
      context,
      remoteSourceName,
      remoteSourceAdapterName,
      connName,
      connId,
      typeId,
      spaceName
    );
  } catch (err) {
    throw new CodedError("disableRemoteTablesFailed", err);
  }
}

async function validateDisableRemoteTables(context: IRequestContext, connId: string, spaceUUID: string) {
  const validationResult = await RepositoryObjectClient.canDeleteObjects(context, connId);
  if (Array.isArray(validationResult) && validationResult.length) {
    const dependentObjsCommaSeparated = validationResult
      .filter((res) => res.messageCode === ValidationMessageCode.DeleteInUse)
      ?.map((obj) => obj.description)
      ?.toString();
    if (!dependentObjsCommaSeparated) {
      // No dependent objects were found, but still there were other validation errors and we throw an error.
      throw new CodedError(
        "disableRemoteTablesFailed",
        `Validation errors found for: ${connId}. ${inspect(validationResult)}.`
      );
    }

    // Build a string of pipe separated object names to use it as a filter for getObject().
    const dependentObjsPipeSeparated = dependentObjsCommaSeparated.split(",").join("|");
    const params: IGetObjectParams = {
      folderIds: [spaceUUID],
      details: ["kind"],
      filters: `name:${dependentObjsPipeSeparated}`,
      inSpaceManagement: true,
    };
    const dependentObjsInfo = await RepositoryObjectClient.getObject(context, params);
    // Data and replication flows are not relevant while disabling remote tables. Remote tables can be disabled even if a connection has data or replication flows.
    const dependentObjsFiltered = dependentObjsInfo.filter(
      (obj) => obj.kind !== ObjectKind.dataflow && obj.kind !== ObjectKind.replicationflow
    );

    if (dependentObjsFiltered.length > 0) {
      const dependentObjs = dependentObjsFiltered.map((obj) => obj.name).join(", ");
      throw new CodedError(
        "disableRemoteTablesError",
        `Remote tables cannot be disabled because the connection ${connId} is used by the following object(s): ${dependentObjs}.`,
        StatusCodes.BAD_REQUEST,
        { list: [dependentObjs] }
      );
    }
  }
}

/**
 * Deletes the given remote source from customer Hana (and also virtual tables in some cases).
 *
 * @param context the request context
 * @param remoteSourceName the name of the remote source to be deleted
 * @param adapterName the name of the adapter for the remote source
 * @param connName the technical name of the connection
 * @param typeId the connection type
 * @param spaceName name of the space
 *
 * The virtual tables being deleted explicitly are the ones that were created to support metadata import for S/4HANA OP (DW12-1113),
 * and the ones created for BW process chain task execution (DS00-391).
 * This operation is idempotent if virtual tables/remote source is not found, no error is thrown.
 */
export async function deleteVirtualTablesAndRemoteSource(
  context: IRequestContext,
  remoteSourceName: string,
  adapterName: RemoteSourceAdapters | undefined,
  connName: string,
  connId: string,
  typeId: TypeIds,
  spaceName: string,
  isSpaceDeletion?: boolean,
  isSpaceConnectionDeletion?: boolean
) {
  try {
    const featureFlags = await FeatureFlagProvider.getFeatureFlags(context);
    /**
     * For some connection types like S/4HANA on premise (for metadata import), SAP BW Bridge (for BW process chain task),
     * delete virtual tables (if they exist).
     * During space deletion this step should be skipped, because the space schema is already deleted. As a consequence,
     * not only the virtual tables but also the space owner user is already gone, so it's credentials cannot be obtained anymore (DW101-32293).
     */
    if (!isSpaceDeletion) {
      if ([TypeIds.SAPS4HANAOP, TypeIds.SAPBWMODELTRANSFER].includes(typeId)) {
        const sdiMetadataImport = new SdiMetadataImport(context, spaceName, connName, connId, typeId, remoteSourceName);
        await sdiMetadataImport.dropVirtualTablesForMetadataImport();
      }
      /**
       * Currently, the bridge connection is deleted along with the space deletion and cannot be deleted individually.
       * Therefore, the virtual tables need not be deleted separately, as they are removed along with the
       * space schema during the space deletion.
       * This safeguard is in place in case the bridge connection can be deleted individually in the future.
       * Virtual tables for the bridge connection were introduced with DS00-391 and are created when the
       * BW process chain task is executed for the first time, @see {@link BWProcessChainTask}.
       */
      if (typeId === TypeIds.SAPBWBRIDGE) {
        const bwProcessChainDb = new BWProcessChainDB(context, spaceName);
        await bwProcessChainDb.deleteVirtualTables();
      }
    }

    // For hanaodbc, dependent virtual tables in _sys_ldb need to be dropped with CASCADE, see DW101-3444
    const withCascade = adapterName === RemoteSourceAdapters.SDA.HanaOdbc;

    const supportsClientCerts = supportsClientCertificate(featureFlags, typeId, adapterName as RemoteSourceAdapters);
    if (supportsClientCerts && !isSpaceConnectionDeletion && typeId !== TypeIds.SAPBWBRIDGE) {
      // In all cases except UCL and BW bridge the lifecycle of the PSE is tied to the remote source, so here we delete both
      await deleteClientCredsForRemoteSource(context, remoteSourceName);
    }
    await SdiManager.deleteRemoteSource(context, remoteSourceName, withCascade);
  } catch (err) {
    // If ERR_SQL_INV_DATA_SOURCE - the remote source does not exist, ignore the error
    if (err.code === ERR_SQL_INV_DATA_SOURCE) {
      logInfo([`Remote source ${remoteSourceName} already deleted/does not exist.`, err], { context });
    } else {
      logError([`Failed to delete remote source :: ${remoteSourceName}.`, err], { context });
      throw err;
    }
  }
}
