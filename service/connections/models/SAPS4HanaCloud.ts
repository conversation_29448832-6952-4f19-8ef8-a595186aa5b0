/** Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved.
 * @format
 */

import { StatusCodes } from "http-status-codes";
import { extend, isPlainObject } from "lodash";
import { FeatureFlagProvider } from "../../featureflags/FeatureFlagProvider";
import { CodedError } from "../../server/errorResponse";
import {
  CCMApplicationLabels,
  CCMCredential,
  CCMCredentialTypes,
  CCMEndpoint,
  CCMEndpoints,
  CCMRequestPayload,
  NamedUserProps,
  X509Props,
} from "../ccm/CCMConstants";
import {
  IAbapOdbcParameters,
  IConnectionCredential,
  IConnectionFeature,
  IConnectionMetadata,
  IConnectionProperties,
  IObjectLiteral,
} from "../interfaces";
import {
  ABAP_ODBC_DRIVER_NAME,
  ABAP_ODBC_SERVICE_PATH,
  AuthTypes,
  BasicCredential,
  CDI_SERVICE_PATH,
  ConnectionCapabilities,
  DWCConnectionConfiguration,
  DWCConnectionProperties,
  DWCCredential,
  DataAccessLevel,
  DataProvisioningOptions,
  IAbapOdbcAdapterParams,
  IRemoteConnectionWithConfig,
  IUIMetadata,
  RemoteSourceAdapters,
  TypeIds,
  X509Credential,
} from "../utils/Constants";
import { isDataProvisioningDpAgent, mapRemoteTableOptions } from "../utils/RemoteTableOptions";
import {
  buildCDIEndpointUrl,
  checkRequiredFields,
  getApplicationLabelsMap,
  unescapeEOL,
} from "../utils/connectionUtils";
import { Connection } from "./Connection";
import { ValidationRun } from "./validation/ValidationHandler";
import { ABAPClientIdValidator } from "./validation/validators/ABAPClientIdValidator";
import { CertificateValidator } from "./validation/validators/CertificateValidator";
import { HostnameValidator } from "./validation/validators/HostnameValidator";
import { LanguageCodeValidator } from "./validation/validators/LanguageCodeValidator";
import { PortValidator } from "./validation/validators/PortValidator";
import { PrivateKeyValidator } from "./validation/validators/PrivateKeyValidator";
import { SAPSystemIdValidator } from "./validation/validators/SAPSystemIdValidator";

export enum S4HanaValidationErrorCodes {
  INVALID_REMOTE_SOURCE_ADPATER = "invalidAdapter",
  REMOTE_SOURCE_ADAPTER_CANNOT_CHANGE = "adapterCannotChange",
}

/**
 * CCM replication: true
 * Remote sources make use of the SDI CDI Adapter.
 * The Remote Tables Feature is only active when a DP Agent is selected.
 */

export class SAPS4HanaCloud extends Connection {
  public getCCMEndpointTypeForDataAndReplFlows(configuration: DWCConnectionConfiguration): CCMEndpoints {
    return CCMEndpoints.WSRFC;
  }

  protected async getConnectionProperties(payload: IObjectLiteral): Promise<IConnectionProperties> {
    checkRequiredFields(payload, ["applicationServer", "port", "client"]);
    return {
      ConnectionInfo: {
        host: payload.applicationServer,
        port: payload.port ?? "",
        client: payload.client,
        sysid: payload.systemId ?? "",
        langcode: payload.language ?? "",
        ...(payload.enableUserPropagation && {
          enableUserPropagation: `${payload.enableUserPropagation}`,
          abapSQLServiceBinding: payload.abapSQLServiceBinding ?? "",
          iasApplicationDependencyName: payload.iasApplicationDependencyName ?? "",
        }),
        authentication: {
          auth_type: payload.authType,
        },
      },
    };
  }

  /**
   * Checks if the data flows feature is enabled.
   * @param payload The payload containing connection details
   * @returns "true" if the data flows feature is enabled else "false"
   */
  private isDataflowEnabled(payload: IObjectLiteral): boolean {
    return !payload.enableUserPropagation;
  }

  /**
   * Checks if the replication flows is enabled.
   * @param payload The payload containing connection details
   * @returns True if the replication flows feature is enabled
   */
  private isReplicationflowsEnabled(payload: IObjectLiteral): boolean {
    return !payload.enableUserPropagation;
  }

  /**
   * Checks if the remote tables feature is enabled.
   *
   * @param payload The payload containing connection details
   * @returns `true` if the remote tables feature is enabled, otherwise `false`
   */
  private isRemoteTablesEnabled(payload: IObjectLiteral): boolean {
    // for option "dpAgent", the agent name must be selected
    const remoteTables = payload.remoteTables ?? {};
    const { dataProvisioningOption = DataProvisioningOptions.NONE, dataProvisioningAgent = "" } = remoteTables;

    return (
      dataProvisioningOption === DataProvisioningOptions.DIRECT ||
      (dataProvisioningOption === DataProvisioningOptions.DP_AGENT && dataProvisioningAgent !== "")
    );
  }
  private isModelImportEnabled(payload: IObjectLiteral): boolean {
    const remoteTables = payload.remoteTables ?? {};
    const { dataProvisioningOption = DataProvisioningOptions.NONE, dataProvisioningAgent = "" } = remoteTables;

    // enabled only if a data provisioning agent is selected
    return dataProvisioningOption === DataProvisioningOptions.DP_AGENT && dataProvisioningAgent !== "";
  }

  /**
   * Gets metadata of this connection.
   *
   * @param payload The payload containing connection details
   * @returns An object containing the connection metadata
   */
  protected async getConnectionMetadata(payload: IObjectLiteral): Promise<IConnectionMetadata> {
    const remoteTables = payload.remoteTables;
    return {
      ...(await super.getConnectionMetadata(payload)),
      capabilityHanaSdi: `${this.isRemoteTablesEnabled(payload)}`,
      capabilityDataflowSource: `${this.isDataflowEnabled(payload)}`,
      capabilityModelTransfer: `${this.isModelImportEnabled(payload)}`,
      capabilityReplicationflowSource: `${this.isReplicationflowsEnabled(payload)}`,
      ...(isDataProvisioningDpAgent(remoteTables) && {
        location: {
          agentName: remoteTables.dataProvisioningAgent,
          location: "agent",
          connected: true,
        },
      }),
    };
  }

  /**
   * Returns the credential properties of this connection that will be used to
   * authenticate with the remote data source.
   *
   * @param authType The type of authentication (e.g., Basic, OAuth2)
   * @param payload The payload containing the necessary authentication details
   * @returns An object containing the credential properties according to the
   * authentication type
   * @throws Error if missing authentication details
   */
  protected async getConnectionCredentialProperties(
    authType: AuthTypes,
    payload: IObjectLiteral
  ): Promise<IConnectionCredential> {
    if (authType === AuthTypes.Basic) {
      checkRequiredFields(payload, [BasicCredential.username, BasicCredential.password]);
    }

    if (authType === AuthTypes.X509) {
      checkRequiredFields(payload, [X509Credential.x509ClientCertificate, X509Credential.x509ClientPrivateKey]);
    }

    const properties = await super.getConnectionCredentialProperties(authType, payload);

    return properties;
  }

  /**
   * Gets the feature properties of the connection.
   *
   * @returns An object containing the feature properties of the connection
   */
  protected async getConnectionFeatureProperties(payload: IObjectLiteral): Promise<IConnectionFeature> {
    const featureFlags = await FeatureFlagProvider.getFeatureFlags(this.context);
    return {
      dpAgent: payload.remoteTables?.dataProvisioningAgent ?? "",
      modelImport: this.isModelImportEnabled(payload),
      remoteTables: this.isRemoteTablesEnabled(payload),
      dataflows: this.isDataflowEnabled(payload),
      replicationflows: this.isReplicationflowsEnabled(payload),
      ...mapRemoteTableOptions(payload.remoteTables),
      ...(featureFlags.DWCO_CONNECTION_RUCKSACK_S4CLOUD && {
        useFastSerialization: (payload.fastSerialization !== false).toString(),
      }),
    };
  }

  public getCCMCredentialTypeForDataAndReplFlows(configuration: DWCConnectionConfiguration): CCMCredentialTypes {
    const connProps = configuration.ConnectionProperties.ConnectionInfo as Required<DWCConnectionProperties>;
    // authentication.auth_type is available only with the introduction of DW00-5014
    // Before, it was always AuthTypes.Basic
    const authType = connProps.authentication?.auth_type || AuthTypes.Basic;
    const credType = this.getCCMEndpointCredentials(authType, undefined).typeId;
    return credType;
  }

  public async getSupportedCapabilities(): Promise<ConnectionCapabilities[]> {
    let caps: ConnectionCapabilities[] = [ConnectionCapabilities.HANASDI, ConnectionCapabilities.MODELTRANSFER];
    const featureFlags = await FeatureFlagProvider.getFeatureFlags(this.context);
    if (featureFlags.INFRA_DWC_TWO_TENANT_MODE) {
      caps = caps.concat([ConnectionCapabilities.DATAFLOWSOURCE, ConnectionCapabilities.REPLICATIONFLOWSOURCE]);
    } else {
      caps.push(ConnectionCapabilities.DISDATAFLOW);
    }
    return caps;
  }

  public async usesRMSForHanaRemoteSource(configuration: DWCConnectionConfiguration) {
    return false;
  }

  public async getCurrentRemoteSourceAdapterName(connectionInfo: any): Promise<RemoteSourceAdapters> {
    const dataProvisioningOption = connectionInfo.configuration?.ConnectionFeatures?.features?.dataProvisioningOption;
    return dataProvisioningOption === DataProvisioningOptions.DIRECT
      ? RemoteSourceAdapters.SDA.AbapOdbc
      : RemoteSourceAdapters.SDI.CloudDataIntegrationAdapter;
  }

  public getSupportedRemoteSourceAdapterNames(): RemoteSourceAdapters[] {
    return [RemoteSourceAdapters.SDI.CloudDataIntegrationAdapter, RemoteSourceAdapters.SDA.AbapOdbc];
  }

  /**
   * Adjustments for S/4 HANA Cloud UI Schema.
   *
   * @override Connection.getAdjustedUIMetadata()
   * @returns adjusted UIMetadata
   */
  public async getAdjustedUIMetadata(): Promise<IUIMetadata> {
    const metadata = await super.getAdjustedUIMetadata();
    await this.adjustForDataAndReplicationFlowFeature(metadata, false);
    return metadata;
  }

  /**
   * Transforms the connectionInfo object to match a HANA Remote Resource using
   * `CloudDataIntegration` or `abapodbc` Adapter.
   *
   * In case the authentication method is using ClientCertificates more adjustments are
   * needed to later on parse the entries into the correct XML format.
   *
   * @param connectionInfo DWC representation of the connection
   */
  public async adjustForRemoteSourceInner(connectionInfo: any) {
    let configurations = {};
    const adapterName = await this.getCurrentRemoteSourceAdapterName(connectionInfo);
    const connProps = connectionInfo.configuration.ConnectionProperties.ConnectionInfo;

    // ABAP ODBC adapter currently does not support X509
    if (adapterName === RemoteSourceAdapters.SDA.AbapOdbc) {
      const abapOdbcParams = connectionInfo.configuration.ConnectionFeatures.features.abapOdbcParameters;
      // For future implementation or refacotring, an additional parameter "adapterNames"
      // can be added to the function "getSDARemoteSourceAdapterConfig" to handle
      // different logics for different SDA adapters

      const featureFlags = await FeatureFlagProvider.getFeatureFlags(this.context);
      configurations = this.getSDARemoteSourceAdapterConfig(
        connProps,
        abapOdbcParams,
        featureFlags.DWCO_USER_PROPAGATION && connProps.enableUserPropagation === "true"
      );

      // Here we add another property to the connectionInfo object to store the
      // User Propagation settings. This is needed to later in sdi.ts
      if (featureFlags.DWCO_USER_PROPAGATION) {
        extend(connectionInfo.configuration.ConnectionProperties, {
          userPropagation: {
            enableUserPropagation: connProps.enableUserPropagation,
            iasApplicationDependencyName: connProps.iasApplicationDependencyName,
          },
        });
      }
    } else {
      configurations = this.getSDIRemoteSourceAdapterConfig(connProps);
    }

    Object.assign(connectionInfo.configuration.ConnectionProperties, { configurations });

    if (connProps.authentication?.auth_type === AuthTypes.X509) {
      // As X509 Certs blow up the size limits of credentials manageable by HANA they are not passed in
      // when creating a remote source. Instead they will have to be added as a PSE and attaching it to the
      // created remote source afterwards.
      // See https://hdbits.wdf.sap.corp/bugzilla/show_bug.cgi?id=280942#c6
      connectionInfo.configuration.ConnectionProperties.configurations.connection.auth_mech = "ClientCertificate";
      delete connectionInfo.configuration.CredentialProperties.x509_client_credential;
      delete connectionInfo.configuration.CredentialProperties.credential;
    }

    // Delete DIS specific SAPS4HANACLOUD properties
    delete connectionInfo.configuration.ConnectionProperties.ConnectionInfo;
  }

  /**
   * This function is used to add default values to the connection data object.
   * It is used to set the default values for the "dataProvisioningOption" and
   * "dataAccess" properties in the ConnectionFeatures object.
   *
   * **Note:** This funtion mutates `connectionData`.
   * @param connectionData The connection data object
   * @returns The connection data object with the default values set
   */
  public async addDefaults(connectionData: Record<string, any>): Promise<Record<string, any>> {
    // if "dpAgent" is set and "dataProvisioningOption" is not set, set it to "dpAgent"
    // and set property "dataAccess" to "federationAndReplication"
    const features = connectionData.configuration.ConnectionFeatures.features;
    const dpAgent = features.dpAgent;

    if (!features.dataProvisioningOption) {
      connectionData.configuration.ConnectionFeatures.features = {
        ...features,
        // set data provisioning option default to "none" if dpAgent is not set
        dataProvisioningOption: dpAgent ? DataProvisioningOptions.DP_AGENT : DataProvisioningOptions.NONE,
        // set data access level only if "dpAgent" is provided
        ...(dpAgent && { dataAccess: DataAccessLevel.RemoteAndReplication }),
      };
    }

    // return the updated connection data or the original one if no changes necessary
    return connectionData;
  }

  /**
   * Performs validation steps for the given connection data before creating or
   * updating a connection. Running this before is to avoid unnecessary calls to
   * database e.g. in case of editing a connection.
   *
   * @param connectionData The connection data object
   * @throws Error if the validation fails
   * @see {@linkcode checkDataProvisioningOptions} for related validations
   */
  public async validateData(connectionData: Record<string, any>): Promise<void> {
    const featureFlags = await FeatureFlagProvider.getFeatureFlags(this.context);
    const features = connectionData.configuration.ConnectionFeatures.features;
    const dpOption = features.dataProvisioningOption as DataProvisioningOptions;
    const dataAccess = features.dataAccess as DataAccessLevel;

    if (features.useFastSerialization && !featureFlags.DWCO_CONNECTION_RUCKSACK_S4CLOUD) {
      throw new Error(
        "Fast Serialization is not supported. Enable the feature flag 'DWCO_CONNECTION_RUCKSACK_TOGGLE' to use it."
      );
    }
    await this.checkDataProvisioningOptions(connectionData);
    // data access level are fixed and cannot be changed
    await this.checkDataAccess(dataAccess, dpOption);
    await this.checkUserPropagation(connectionData);
  }

  public async getCCMRequestPayload(connectionInfo: any, ccmTechnicalName: string, spaceName: string) {
    const featureFlags = await FeatureFlagProvider.getFeatureFlags(this.context);
    const isFastSerializationEnabled = featureFlags.DWCO_CONNECTION_RUCKSACK_S4CLOUD;
    const connProps = connectionInfo.configuration.ConnectionProperties.ConnectionInfo;
    const credProps = connectionInfo.configuration.CredentialProperties;
    const featureProps = connectionInfo.configuration.ConnectionFeatures.features;

    const wsrfcEndpoint = await this.getCCMPayloadWSRFCEndpoint(
      connProps,
      credProps,
      featureProps,
      isFastSerializationEnabled
    );
    const endpoints: CCMEndpoint[] = [wsrfcEndpoint];

    const payload: CCMRequestPayload = {
      name: connectionInfo.businessName,
      technicalName: ccmTechnicalName,
      description: connectionInfo.description,
      typeVersion: isFastSerializationEnabled ? "4.1.0" : "3.0.1",
      typeId: TypeIds.SAPS4HANACLOUD,
      properties: {
        client: connProps.client,
      },
      endpoints,
    };

    if (connProps.sysid) {
      payload.properties.sysid = connProps.sysid;
    }
    if (connProps.langcode) {
      payload.properties.language = connProps.langcode;
    }

    return payload;
  }

  private async getCCMPayloadWSRFCEndpoint(
    connProps: any,
    credProps: any,
    featureProps: any,
    isFastSerializationEnabled: boolean
  ): Promise<CCMEndpoint> {
    const wsrfcEndpoint: CCMEndpoint = {
      typeId: CCMEndpoints.WSRFC,
      properties: {
        wsHost: connProps.host,
        port: parseInt(connProps.port, 10),
        ...(isFastSerializationEnabled && { useFastSerialization: featureProps.useFastSerialization === "true" }),
      },
      credentials: [this.getCCMEndpointCredentials(connProps.authentication.auth_type, credProps)],
    };
    return wsrfcEndpoint;
  }

  public async mapCCMtoDWCProps(ccmConnectionData: any) {
    const ccmEndpointsData = ccmConnectionData.endpoints.find(
      (endpoint: any) => endpoint.typeId === CCMEndpoints.WSRFC
    );
    const [ccmCredentialsData] = ccmEndpointsData.credentials;

    const applicationLabels = await getApplicationLabelsMap(ccmConnectionData.applicationLabels);
    const dpAgentName = applicationLabels.get(CCMApplicationLabels.DPAgentName);
    const dpAgentLocation = applicationLabels.get(CCMApplicationLabels.DPAgentLocation);

    const featureFlags = await FeatureFlagProvider.getFeatureFlags(this.context);
    const dataProvisioningOption = applicationLabels.get(CCMApplicationLabels.DataProvisioningOption);

    const dwcConnPayload: any = {
      id: ccmConnectionData.id || undefined,
      businessName: ccmConnectionData.name,
      name: ccmConnectionData.technicalName,
      description: ccmConnectionData.description,
      credentialMode: "technicaluser",
      configuration: {
        ConnectionProperties: {
          ConnectionInfo: {
            host: ccmEndpointsData.properties.wsHost,
            client: ccmConnectionData.properties.client,
            sysid: ccmConnectionData.properties.sysid,
            langcode: ccmConnectionData.properties.language,
            port: `${ccmEndpointsData.properties.port}`,
          },
        },
        CredentialProperties: Object.assign(
          { credentials_mode: "technicaluser" },
          this.getDWCCredentials(ccmCredentialsData)
        ),
        ConnectionFeatures: {
          features: {
            dataProvisioningOption,
            // set data access level only if "dpAgent" is provided
            // data provisioning option "direct" is not handled in CCM app labels
            // see mapDWCtoCCMProps
            ...(dataProvisioningOption === DataProvisioningOptions.DP_AGENT && {
              dataAccess: DataAccessLevel.RemoteAndReplication,
            }),
          },
        },
      },
      typeId: TypeIds.SAPS4HANACLOUD,
      capabilityHanaSdi: dataProvisioningOption === DataProvisioningOptions.NONE ? "false" : "true",
      capabilityModelTransfer: dataProvisioningOption !== DataProvisioningOptions.NONE ? "true" : "false",
    };

    if (featureFlags.INFRA_DWC_TWO_TENANT_MODE) {
      dwcConnPayload.capabilityDataflowSource = "true";
      dwcConnPayload.capabilityReplicationflowSource = "true";
    } else {
      dwcConnPayload.capabilityDisDataflow = "true";
    }

    // use SDI and DP agent
    dwcConnPayload.configuration.ConnectionFeatures.features.dpAgent = dpAgentName;
    dwcConnPayload.location = {
      agentName: dpAgentName,
      location: dpAgentLocation,
      connected: true,
    };

    const useClientCert = ccmCredentialsData.typeId === CCMCredentialTypes.X509ClientCertificate;
    dwcConnPayload.configuration.ConnectionProperties.ConnectionInfo.authentication = {
      auth_type: useClientCert ? AuthTypes.X509 : AuthTypes.Basic,
    };

    return dwcConnPayload;
  }

  public async mapDWCtoCCMProps(dwcConnectionData: any) {
    let configuration = dwcConnectionData.configuration;
    if (typeof dwcConnectionData.configuration === "string") {
      configuration = JSON.parse(configuration);
    }
    const connectionProperties = configuration.ConnectionProperties;

    const port = parseInt(connectionProperties.ConnectionInfo.port, 10);
    const host = connectionProperties.ConnectionInfo.host;
    const dpAgentSelected = !!configuration.ConnectionFeatures?.features.dpAgent;
    let authType = CCMCredentialTypes.NamedUser;
    if (connectionProperties.ConnectionInfo.authentication?.auth_type === AuthTypes.X509) {
      authType = CCMCredentialTypes.X509ClientCertificate;
    }

    const responsePayload = {
      name: dwcConnectionData.businessName || dwcConnectionData.name,
      technicalName: dwcConnectionData.name,
      description: dwcConnectionData.description,
      typeId: TypeIds.SAPS4HANACLOUD,
      properties: {
        client: connectionProperties.ConnectionInfo.client,
        sysid: connectionProperties.ConnectionInfo.sysid,
        language: connectionProperties.ConnectionInfo.langcode,
      },
      endpoints: [
        {
          typeId: CCMEndpoints.WSRFC,
          properties: {
            wsHost: host,
            port,
          },
          credentials: [
            {
              typeId: authType,
              properties: {},
            },
          ],
        },
      ] as CCMEndpoint[],
    };

    if (dpAgentSelected) {
      responsePayload.endpoints.push({
        typeId: CCMEndpoints.CDI,
        properties: {
          endpoint: buildCDIEndpointUrl(host, port),
        },
        credentials: [
          {
            typeId: authType,
            properties: {},
          },
        ],
      });
    }

    // build application labels
    const applicLabels: string[] = [];
    if (dpAgentSelected) {
      applicLabels.push(`${CCMApplicationLabels.DataProvisioningOption}:${DataProvisioningOptions.DP_AGENT}`);
      applicLabels.push(`${CCMApplicationLabels.DPAgentName}:${dwcConnectionData.agentName}`);
      applicLabels.push(`${CCMApplicationLabels.DPAgentLocation}:${dwcConnectionData.location}`);
    } else {
      applicLabels.push(`${CCMApplicationLabels.DataProvisioningOption}:${DataProvisioningOptions.NONE}`);
    }

    (responsePayload as any).applicationLabels = applicLabels;
    return responsePayload;
  }

  /**
   * Extracts the correct credentials from DWC and maps them to the CCM representation.
   *
   * @param authType the DWC auth type
   * @param credProps the DWC credential properties
   * @returns a valid CCM formatted credential object
   *
   * If credProps are not passed then the credential properties are also omitted from the result.
   */
  private getCCMEndpointCredentials(authType: string, credProps: Required<DWCCredential> | undefined): CCMCredential {
    switch (authType) {
      case AuthTypes.Basic:
        return {
          typeId: CCMCredentialTypes.NamedUser,
          properties: !credProps
            ? {}
            : {
                username: credProps.credential.user,
                password: credProps.credential.password,
              },
        };
      case AuthTypes.X509:
        return {
          typeId: CCMCredentialTypes.X509ClientCertificate,
          properties: !credProps
            ? {}
            : {
                x509ClientCertificate: unescapeEOL(credProps.x509_client_credential.x509_client_certificate),
                x509ClientPrivateKey: unescapeEOL(credProps.x509_client_credential.x509_client_private_key),
                x509ClientPrivateKeyPassword: credProps.x509_client_credential.x509_client_private_key_password,
              },
        };
      default:
        throw new Error(`Invalid authentication type :: ${authType}`);
    }
  }

  /**
   * Extracts the correct credentials from CCM and maps them to the DWC
   * representation.
   *
   * @param credProps from CCM
   * @returns transformed to DWC representation of the connection
   */
  private getDWCCredentials(credProps: CCMCredential): any {
    switch (credProps.typeId) {
      case CCMCredentialTypes.X509ClientCertificate:
        const x509Props = credProps.properties as X509Props;
        return {
          x509_client_credential: {
            x509_client_certificate: x509Props.x509ClientCertificate,
            x509_client_private_key: x509Props.x509ClientPrivateKey,
            x509_client_private_key_password: x509Props.x509ClientPrivateKeyPassword,
          },
        };
      case CCMCredentialTypes.NamedUser:
        const namedProps = credProps.properties as NamedUserProps;
        return {
          credential: {
            user: namedProps.username,
            password: namedProps.password,
          },
        };
      default:
        throw new Error(`Invalid credentials type :: ${credProps.typeId}`);
    }
  }

  public async validate(
    connectionInfo: any,
    spaceId: string,
    existingConnectionInfo?: IRemoteConnectionWithConfig,
    additionalValidators: ValidationRun[] = []
  ) {
    const config = connectionInfo.configuration.ConnectionProperties.ConnectionInfo;
    const s4Validators: ValidationRun[] = [
      { validator: new HostnameValidator(config.host) },
      { validator: new ABAPClientIdValidator(config.client) },
      { validator: new PortValidator(config.port) },
    ];

    const featureFlags = await FeatureFlagProvider.getFeatureFlags(this.context);
    let supportedCapabilites = await this.getSupportedCapabilities();
    // filter for capability validator
    let filter: string[] = [];
    if (featureFlags.DWCO_USER_PROPAGATION && config.enableUserPropagation === "true") {
      supportedCapabilites = supportedCapabilites.filter(
        (cap) => cap !== ConnectionCapabilities.DATAFLOWSOURCE && cap !== ConnectionCapabilities.REPLICATIONFLOWSOURCE
      );
      // provide a filter for data flow source capability from connection info,
      // otherwise validation will fail
      filter = [ConnectionCapabilities.DATAFLOWSOURCE, ConnectionCapabilities.REPLICATIONFLOWSOURCE];
    }

    if (config.sysid) {
      s4Validators.push({ validator: new SAPSystemIdValidator(config.sysid) });
    }
    if (config.langcode) {
      s4Validators.push({ validator: new LanguageCodeValidator(config.langcode) });
    }

    const authType = connectionInfo.configuration.ConnectionProperties.ConnectionInfo.authentication?.auth_type;
    if (authType === AuthTypes.X509) {
      const privKeyProps = connectionInfo.configuration.CredentialProperties.x509_client_credential!;
      s4Validators.push({
        validator: new CertificateValidator(this.context, privKeyProps.x509_client_certificate),
        options: { exitOnError: false },
      });
      s4Validators.push({
        validator: new PrivateKeyValidator(
          this.context,
          privKeyProps.x509_client_private_key,
          privKeyProps.x509_client_private_key_password
        ),
        options: { exitOnError: false },
      });
    }

    if (existingConnectionInfo) {
      await this.checkDataProvisioningOptions(connectionInfo, existingConnectionInfo);
    }

    await super.validate(
      connectionInfo,
      spaceId,
      existingConnectionInfo,
      [...additionalValidators, ...s4Validators],
      supportedCapabilites,
      filter
    );
  }

  /**
   * Checks if the data provisioning option is valid or can be changed comparing
   * the `existingConnectionData` object.
   * @param connectionData  The connection data object
   * @param existingConnectionData Optional, the existing connection data object
   * @throws CodedError if the data provisioning option is invalid or cannot be changed
   * @see {@linkcode validateData} for related checks
   */
  private async checkDataProvisioningOptions(
    connectionData: Record<string, any>,
    existingConnectionData?: IRemoteConnectionWithConfig
  ): Promise<void> {
    const features = connectionData.configuration.ConnectionFeatures.features;
    const connInfo = connectionData.configuration.ConnectionProperties.ConnectionInfo;
    const dpOption = features.dataProvisioningOption as DataProvisioningOptions;
    const dpOptions = [DataProvisioningOptions.DIRECT, DataProvisioningOptions.DP_AGENT, DataProvisioningOptions.NONE];
    const errorCode = "forbiddenOperation";

    if (!existingConnectionData) {
      // do not allow other data provisioning options other than "none", "dpAgent" and "direct"
      if (!dpOptions.includes(dpOption)) {
        throw new CodedError(
          errorCode,
          `Data provisioning option "${dpOption}" is not supported. It must be one of "${dpOptions.join('", "')}"`,
          StatusCodes.BAD_REQUEST
        );
      }

      if (dpOption === DataProvisioningOptions.DIRECT) {
        // using "direct" data provisioning option with X509 is currently not allowed
        if (connInfo.authentication.auth_type === AuthTypes.X509) {
          throw new CodedError(
            errorCode,
            `Data provisioning option "${dpOption}" is not supported by authentication type "${AuthTypes.X509}"`,
            StatusCodes.BAD_REQUEST
          );
        }

        // checks for abap odbc connection params
        const abapOdbcParams = features.abapOdbcParameters;
        if (!isPlainObject(abapOdbcParams) || !abapOdbcParams.typeMap) {
          throw new CodedError(
            "invalidAbapOdbcParameters",
            `The property "abapOdbcParameters" is missing or invalid for data provisioning option "${dpOption}"`,
            StatusCodes.BAD_REQUEST
          );
        }
      }

      if (dpOption === DataProvisioningOptions.DP_AGENT) {
        // dpAgent must be set for "dpAgent" data provisioning option
        if (!features.dpAgent) {
          throw new CodedError(
            "invalidDPAgent",
            `The property "dpAgent" is missing or empty for data provisioning option "${dpOption}"`,
            StatusCodes.BAD_REQUEST
          );
        }
      }

      // setting dpAgent is only allowed for "dpAgent" data provisioning option
      if (features.dpAgent && dpOption !== DataProvisioningOptions.DP_AGENT) {
        throw new CodedError(
          errorCode,
          `The value of "dpAgent" must not be set and used with data provisioning option "${dpOption}"`,
          StatusCodes.BAD_REQUEST
        );
      }

      // setting ABAP ODBC params is only allowed for "direct" data provisioning option
      if (features.abapOdbcParameters && dpOption !== DataProvisioningOptions.DIRECT) {
        throw new CodedError(
          errorCode,
          `The property "abapOdbcParameters" must not be used with data provisioning option "${dpOption}"`,
          StatusCodes.BAD_REQUEST
        );
      }
    } else {
      // set default to 'none' for existing connections that were created before
      const existingDpOption =
        existingConnectionData.configuration.ConnectionFeatures!.features!.dataProvisioningOption ||
        DataProvisioningOptions.NONE;

      // changing data provisioning option is not allowed. Exceptions:
      // * if it was set to "none"
      // * not set (existing connections that were created before the introduction
      // of data provisioning options)
      if (dpOption !== existingDpOption && existingDpOption !== DataProvisioningOptions.NONE) {
        throw new CodedError(
          errorCode,
          `Changing data provisioning option from "${existingDpOption}" to "${dpOption}" is not allowed`,
          StatusCodes.BAD_REQUEST
        );
      }
    }
  }

  /**
   * Checks if the data access level is valid for the given data provisioning option.
   * @param dataAccess The data access level
   * @param dpOption The data provisioning option
   * @see {@linkcode validateData}
   */
  private async checkDataAccess(dataAccess: DataAccessLevel, dpOption: DataProvisioningOptions): Promise<void> {
    const errorCode = "forbiddenOperation";

    switch (dataAccess) {
      case DataAccessLevel.RemoteAndReplication:
        if (dpOption !== DataProvisioningOptions.DP_AGENT) {
          throw new CodedError(
            errorCode,
            `The "dataAccess" value "${dataAccess}" can only be used with data provisioning option "${DataProvisioningOptions.DP_AGENT}"`,
            StatusCodes.BAD_REQUEST
          );
        }
        break;
      case DataAccessLevel.RemoteOnly:
        if (dpOption !== DataProvisioningOptions.DIRECT) {
          throw new CodedError(
            errorCode,
            `The "dataAccess" value "${dataAccess}" can only be used with data provisioning option "${DataProvisioningOptions.DIRECT}"`,
            StatusCodes.BAD_REQUEST
          );
        }
        break;
      default:
        // dp option is set, but data access is not
        if (!dataAccess && dpOption !== DataProvisioningOptions.NONE) {
          throw new CodedError(
            errorCode,
            `The "dataAccess" value "${dataAccess}" must be set for data provisioning option "${dpOption}"`,
            StatusCodes.BAD_REQUEST
          );
        }

        if (dataAccess) {
          // data access must be not set for "none" data provisioning option
          if (dpOption === DataProvisioningOptions.NONE) {
            throw new CodedError(
              errorCode,
              `The property "dataAccess" must not be used with Data Provisioning Option "${dpOption}"`,
              StatusCodes.BAD_REQUEST
            );
          } else {
            throw new CodedError(
              errorCode,
              `The "dataAccess" value "${dataAccess}" is invalid for data provisioning option "${dpOption}"`,
              StatusCodes.BAD_REQUEST
            );
          }
        }
        break;
    }
  }

  private async checkUserPropagation(connectionData: Record<string, any>) {
    const connProps = connectionData.configuration.ConnectionProperties.ConnectionInfo;
    const dpOption = connectionData.configuration.ConnectionFeatures.features
      .dataProvisioningOption as DataProvisioningOptions;
    const errorCode = "forbiddenOperation";
    const featureFlags = await FeatureFlagProvider.getFeatureFlags(this.context);

    if (featureFlags.DWCO_USER_PROPAGATION) {
      if (connProps.enableUserPropagation === "true" && dpOption !== DataProvisioningOptions.DIRECT) {
        throw new CodedError(
          errorCode,
          `User propagation settings are not allowed for data provisioning option "${dpOption}"`,
          StatusCodes.BAD_REQUEST
        );
      }
      if (
        connProps.enableUserPropagation === "true" &&
        (!connProps.abapSQLServiceBinding || !connProps.iasApplicationDependencyName)
      ) {
        throw new CodedError(
          errorCode,
          `Missing configuration for user propagation. The properties "abapSQLServiceBinding" and "iasApplicationDependencyName" must be set.`,
          StatusCodes.BAD_REQUEST
        );
      }
    } else {
      if (connProps.enableUserPropagation === "true") {
        // user propagation is not supported
        throw new CodedError(
          errorCode,
          `User propagation is not supported. The feature is disabled.`,
          StatusCodes.BAD_REQUEST
        );
      }
    }
  }

  /**
   * Returns a SDA remote source adapter configuration from the given connection
   * properties and the ABAP ODBC connection parameters.
   * @param connectionProps The object contains connection properties such as host, port, etc.
   * @param abapOdbcParams The object contains ABAP ODBC parameters for creating
   * remote source
   */
  private getSDARemoteSourceAdapterConfig(
    connectionProps: Record<string, unknown>,
    abapOdbcParams: IAbapOdbcParameters,
    enableUserPropagation?: boolean
  ): IAbapOdbcAdapterParams {
    let sdaConfig = {
      host: connectionProps.host as string,
      port: connectionProps.port as number,
      language: ((connectionProps.langcode || "en") as string).toUpperCase(),
      servicePath: ABAP_ODBC_SERVICE_PATH,
      driver: ABAP_ODBC_DRIVER_NAME,
      // in frontend, the uid type is set to "alias". Using "uid" is not working
      // for remote tables
      uidType: abapOdbcParams.uidType || "alias",
      typeMap: abapOdbcParams.typeMap,
    };
    if (enableUserPropagation) {
      sdaConfig = extend(sdaConfig, {
        businessServicePath: `/sap/bc/sql/sql1/sap/${connectionProps.abapSQLServiceBinding}`,
        schema: connectionProps.abapSQLServiceBinding,
        enforceTokenExchangeForDataAccess: true,
      });
    }
    return sdaConfig;
  }

  /**
   * Returns a SDI remote source adapter configuration from the given connection
   * properties.
   * @param connectionProps The object contains connection properties
   */
  private getSDIRemoteSourceAdapterConfig(
    connectionProps: Record<string, unknown>
  ): Partial<IRemoteConnectionWithConfig> {
    // we do not use the default CDI settings here
    return {
      host: connectionProps.host,
      port: connectionProps.port,
      protocol: "HTTPS",
      servicePath: CDI_SERVICE_PATH,
      connection: {
        auth_mech: "Basic",
        require_csrf_header: "true",
      },
    } as Partial<IRemoteConnectionWithConfig>;
  }
}
