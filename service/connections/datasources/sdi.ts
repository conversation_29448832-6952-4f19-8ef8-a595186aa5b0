/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { escapeDoubleQuotes, escapeSingleQuotes } from "@sap/prom-hana-client";
import { AdaptionLocationType } from "../../../shared/remoteTables/types";
import { AuditActivity, AuditStatus } from "../../auditing/index";
import { FeatureFlagProvider, IFeatureFlagsMap } from "../../featureflags/FeatureFlagProvider";
import type { DbClient } from "../../lib/DbClient";
import { getLogger } from "../../logger/index";
import { IClientOptions } from "../../meta/access";
import { IRequestContext } from "../../repository/security/common/common";
import { CustomerHana } from "../../reuseComponents/spaces/src";
import { serializeConnection } from "../utils/ConnectionSerializer";
import { AuthTypes, RealTimeReplicationStatus, RemoteSourceAdapters, TypeIds } from "../utils/Constants";
import { auditLogConnection } from "../utils/audit";
import { deepCopy } from "../utils/connectionUtils";
import { getRemoteSourceObjectTreeAsTable } from "./getRemoteSourceTreeAsTable";
import { HanaCredentialClient } from "./hanaCredentialClient";
import { IAdapterLocation, RemoteSourceObject } from "./types";
import {
  appendRemoteSourceFilter,
  appendRemoteSourceFilterWithDefaultAdapters,
  getConfigurationXml,
  getCredentialXml,
  getSupportedAdapterNames,
} from "./utils";

const { logDebug, logError } = getLogger("SDI");

export interface IAdapterLocationMap {
  adapterName: string;
  locations: IAdapterLocation[];
}

interface IAdapter {
  id: any;
  name: any;
  description: any;
  isSystemAdapter: any;
}

export interface IRemoteSource {
  name: string;
  adapter: string;
  connectionInfo?: any;
  agentName?: string;
  agentGroupName?: string;
  agentStatus?: string;
  credentialMode?: string;
  location?: string;
  realtimeReplicationStatus?: RealTimeReplicationStatus;
}

export interface IRemoteSourceObjectTreeItem {
  description?: string;
  id: string;
  name: string;
  type: string;
  hasChildren: boolean;
  adapter?: string;
  location?: string;
  children?: IRemoteSourceObjectTreeItem[];
  remote?: ICdwRemoteTable;
  definitions?: ICsnDefinitions;
}

export interface IRemoteSourceObjectTree {
  items: IRemoteSourceObjectTreeItem[];
  error?: string;
  partial?: boolean;
  stateful?: boolean;
}

interface IRemoteSourceCount {
  spaceName: string;
  sourceCount: number;
}

const PROP_KEYS = {
  DISPLAY_NAME: "display_name",
  DESCRIPTION: "description",
} as const;

function parseAdapterInfo(properties: string): { [PROP_KEYS.DISPLAY_NAME]: string; [PROP_KEYS.DESCRIPTION]: string } {
  const props = properties ? properties.split(";") : [];
  return props.reduce((result, entry) => {
    const propertyKeyValuePair = entry ? entry.split("=") : [];
    if (propertyKeyValuePair.length === 2) {
      return { ...result, [propertyKeyValuePair[0]]: propertyKeyValuePair[1] };
    }
    return result;
  }, {} as { [PROP_KEYS.DISPLAY_NAME]: string; [PROP_KEYS.DESCRIPTION]: string });
}

function getAdapterLocationValue(location: IAdapterLocation): string | null {
  switch (location.location) {
    case AdaptionLocationType.DPServer:
      return AdaptionLocationType.DPServer;
    case AdaptionLocationType.IndexServer:
    case AdaptionLocationType.DataIntelligence:
      return null;
    default:
      throw new Error("Invalid DP Agent Location");
  }
}

function validateAdapterName(adapterName: string, featureFlags: IFeatureFlagsMap) {
  // Check if adapter name is alphanumeric with colon
  return /^[a-z0-9:]+$/i.test(adapterName);
}

export const getAgents = async function (context: IRequestContext, agentName?: string): Promise<any[]> {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient();

  let sql = "SELECT * FROM SYS.AGENTS";
  let table: any[];
  if (agentName) {
    sql += ` WHERE AGENT_NAME=?`;
    logDebug("getAgents SQL: " + sql + " with Parameter " + agentName, { context });
    const dbStatement = await client.prepare(sql);
    try {
      table = await dbStatement.exec([agentName]);
    } finally {
      await dbStatement.drop();
    }
  } else {
    logDebug("getAgents SQL: " + sql, { context });
    table = await client.exec(sql);
  }

  try {
    return table.map((entry) => ({
      id: entry.AGENT_NAME,
      name: entry.AGENT_NAME,
      host: entry.AGENT_HOST,
      port: entry.AGENT_PORT,
    }));
  } catch (err) {
    return err;
  }
};

export const getAdapters = async function (context: IRequestContext, adapterNames?: string[]) {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient();
  let supportedAdapterNames: string[] = adapterNames || [];
  if (supportedAdapterNames.length === 0) {
    supportedAdapterNames = (await getSupportedAdapterNames(context)) || [];
  }

  const sql = appendRemoteSourceFilter("SELECT * FROM SYS.ADAPTERS", supportedAdapterNames);
  logDebug("getAdapters SQL: " + sql, { context });
  const table: any[] = await client.exec(sql);

  try {
    const adapters: IAdapter[] = table.map((entry) => {
      const properties = parseAdapterInfo(entry.PROPERTIES);
      return {
        id: entry.ADAPTER_NAME,
        name: properties ? properties[PROP_KEYS.DISPLAY_NAME] : entry.PROPERTIES,
        description: properties ? properties[PROP_KEYS.DESCRIPTION] : entry.PROPERTIES,
        isSystemAdapter: entry.IS_SYSTEM_ADAPTER,
      };
    });
    return adapters;
  } catch (err) {
    throw err;
  }
};

/**
 * Get the adapter location, agent name & status for the given adapter.
 *
 * @param context the request context.
 * @param adapter the name of the adapter. If not specified, details will be retrieved for all supported adapters.
 * @param includeAgents flag whether to retrieve agent name & status or not. This is mainly for query optimization.
 *                      There are no agents for SDA (e.g. hanaodbc) & DP server (e.g. ODataAdapter) adapters.
 */
export const getAdapterLocations = async function (
  context: IRequestContext,
  adapter?: string,
  includeAgents: boolean = true
): Promise<IAdapterLocationMap[]> {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient();

  const queryWithAgents = `
      SELECT
        ADAPTER_LOCATIONS.ADAPTER_NAME,
        ADAPTER_LOCATIONS.LOCATION,
        M_AGENTS.AGENT_NAME,
        M_AGENTS.AGENT_STATUS
      FROM SYS.ADAPTER_LOCATIONS AS ADAPTER_LOCATIONS
      LEFT JOIN
        (SELECT
          M_AGENTS.AGENT_NAME, M_AGENTS.AGENT_STATUS
        FROM SYS.M_AGENTS AS M_AGENTS
        INNER JOIN SYS.GRANTED_PRIVILEGES AS PRIVS ON M_AGENTS.AGENT_NAME = PRIVS.OBJECT_NAME
          AND PRIVS.GRANTEE IN (SELECT USER_NAME FROM SYS.USERS WHERE USERGROUP_NAME = 'DWC_AGENT_USERS')
          AND PRIVS.PRIVILEGE = 'AGENT MESSAGING'
          AND PRIVS.OBJECT_TYPE = 'AGENT'
          AND PRIVS.GRANTEE_TYPE = 'USER')
        AS M_AGENTS
      ON M_AGENTS.AGENT_NAME = ADAPTER_LOCATIONS.AGENT_NAME`;

  const queryWithoutAgents = `
      SELECT
        ADAPTER_LOCATIONS.ADAPTER_NAME,
        ADAPTER_LOCATIONS.LOCATION
      FROM SYS.ADAPTER_LOCATIONS AS ADAPTER_LOCATIONS`;

  let sql = includeAgents ? queryWithAgents : queryWithoutAgents;

  const adapters = adapter ? [adapter] : await getSupportedAdapterNames(context);
  sql = appendRemoteSourceFilter(sql, adapters);
  logDebug("getAdapterLocations SQL: " + sql, { context });
  const locations: any[] = await client.exec(sql);
  const result: Record<string, any[]> = {};

  (locations || []).forEach((location) => {
    result[location.ADAPTER_NAME] = result[location.ADAPTER_NAME] || [];
    result[location.ADAPTER_NAME].push({
      agentName: location.AGENT_NAME,
      location: location.LOCATION,
      connected: location.AGENT_STATUS === "CONNECTED" || !location.AGENT_NAME,
    });
  });

  const adapterLocations = Object.keys(result)
    .map((key) => ({
      adapterName: key,
      locations: result[key].filter((entry) => entry.location !== "agent" || entry.agentName != null),
    }))
    .filter((entry) => entry.locations.length > 0);

  return adapterLocations;
};

export async function getDpAgentAdapterRemoteSources(
  context: IRequestContext,
  agentName: string,
  adapterName: string
): Promise<IRemoteSource[]> {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient();
  const sql = `SELECT REMOTE_SOURCE_NAME as "name" FROM SYS.REMOTE_SOURCES WHERE AGENT_NAME = '${escapeDoubleQuotes(
    agentName
  )}'  AND ADAPTER_NAME = '${escapeDoubleQuotes(adapterName)}' `;
  return await client.exec<IRemoteSource>(sql);
}

export async function getRemoteSources(context: IRequestContext, space?: string): Promise<IRemoteSource[] | undefined> {
  const customerHana = await CustomerHana.fromRequestContext(context);
  // TODO: This a security risk, as we control from outside to execute as tenant Manager - but currently there is no alternative
  const client = space ? await customerHana.getSpaceManagerClient(space) : await customerHana.getTenantManagerClient();
  const sql = 'SELECT REMOTE_SOURCE_NAME as "name", ADAPTER_NAME as "adapter" FROM SYS.REMOTE_SOURCES';
  return await client.exec<IRemoteSource>(await appendRemoteSourceFilterWithDefaultAdapters(context, sql));
}

export const getRemoteSourcesDetailed = async function (
  context: IRequestContext,
  space?: string,
  ids?: string[]
): Promise<IRemoteSource[] | undefined> {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const prefix = "RS.";
  const client = await customerHana.getTenantManagerClient();

  // Compute RTR status:
  // status is "ACTIVE" if the remote source is SDI based and has an active subscription
  // or the remote source is SDA based and there exists a corresponding RTR table
  // status is "PAUSED" if there are paused subscriptions on the remote source
  // status is "NOTAPPLICABLE" if CDC is not supported
  // status is "INACTIVE" in any other case
  const sql =
    "SELECT RS.REMOTE_SOURCE_NAME , RS.ADAPTER_NAME , RS.CONNECTION_INFO , RS.LOCATION , RS.AGENT_NAME" +
    "  , A.AGENT_STATUS, A.LAST_CONNECT_TIME, RS.AGENT_GROUP_NAME AS AGENT_GROUP_NAME" +
    "  ,CASE " +
    "    WHEN RS.IS_CDC_SUPPORTED = 'FALSE' THEN 'NOTAPPLICABLE'" +
    "    WHEN PAUSED_REMOTE_SOURCE.TOTAL_SUBSCRIPTIONS_PAUSED>0 THEN 'PAUSED'" +
    "    WHEN ACTIVE_SUBSCRIPTIONS.TOTAL_ACTIVE_SUBSCRIPTIONS>0 OR COUNT_SDA_RTR_TABLE_REMOTE_SOURCE.COUNT_SDA_RTR_TABLE>0 THEN 'ACTIVE'" +
    "     ELSE 'INACTIVE'" +
    "    END AS REAL_TIME_REPLICATION_STATUS" +
    " FROM SYS.REMOTE_SOURCES AS RS" +
    " LEFT JOIN SYS.M_AGENTS AS A ON A.AGENT_NAME = RS.AGENT_NAME" +
    // find active subscriptions as ACTIVE_SUBSCRIPTIONS.TOTAL_ACTIVE_SUBSCRIPTIONS
    " LEFT JOIN (" +
    "    SELECT REMOTE_SOURCE_NAME, COUNT(SUBSCRIPTION_NAME) AS TOTAL_ACTIVE_SUBSCRIPTIONS" +
    "    FROM SYS.M_REMOTE_SUBSCRIPTIONS" +
    "    WHERE STATE NOT IN ('CREATED') AND STATE IS NOT NULL" +
    "    GROUP BY REMOTE_SOURCE_NAME)" +
    "    AS ACTIVE_SUBSCRIPTIONS ON RS.REMOTE_SOURCE_NAME = ACTIVE_SUBSCRIPTIONS.REMOTE_SOURCE_NAME" +
    // find paused subscriptions as PAUSED_REMOTE_SOURCE.TOTAL_SUBSCRIPTIONS_PAUSED
    " LEFT JOIN (" +
    "    SELECT OBJECT_NAME AS REMOTE_SOURCE_NAME, COUNT(*) AS TOTAL_SUBSCRIPTIONS_PAUSED" +
    "    FROM SYS.REMOTE_SUBSCRIPTION_EXCEPTIONS" +
    "    WHERE UPPER(OBJECT_TYPE) = 'REMOTE SOURCE' AND ERROR_NUMBER = 154555" +
    "    GROUP BY OBJECT_NAME)" +
    "    AS PAUSED_REMOTE_SOURCE ON PAUSED_REMOTE_SOURCE.REMOTE_SOURCE_NAME = RS.REMOTE_SOURCE_NAME" +
    // find FVT table using RTR as COUNT_SDA_RTR_TABLE_REMOTE_SOURCE.COUNT_SDA_RTR_TABLE
    " LEFT JOIN (" +
    "    SELECT T.REMOTE_SOURCE_NAME,COUNT(*) AS COUNT_SDA_RTR_TABLE" +
    "    FROM SYS.VIRTUAL_TABLES AS T" +
    "    INNER JOIN SYS.VIRTUAL_TABLE_REPLICAS AS VTR ON VTR.SOURCE_SCHEMA_NAME = T.SCHEMA_NAME AND VTR.SOURCE_TABLE_NAME = T.TABLE_NAME" +
    "    WHERE VTR.IS_SNAPSHOT = 'FALSE'" +
    "    GROUP BY T.REMOTE_SOURCE_NAME)" +
    "    AS COUNT_SDA_RTR_TABLE_REMOTE_SOURCE ON COUNT_SDA_RTR_TABLE_REMOTE_SOURCE.REMOTE_SOURCE_NAME = RS.REMOTE_SOURCE_NAME";
  const remoteSources: any[] = await client.exec(
    await appendRemoteSourceFilterWithDefaultAdapters(context, sql, prefix, ids)
  );

  const result = remoteSources.map((rs) => ({
    name: rs.REMOTE_SOURCE_NAME,
    id: rs.REMOTE_SOURCE_NAME,
    adapter: rs.ADAPTER_NAME,
    location: rs.LOCATION,
    agentName: rs.AGENT_NAME,
    agentGroupName: rs.AGENT_GROUP_NAME,
    // for disconnected agents, check last connect time and if is not older than 5 minutes, the DP agent status will be considered as CONNECTED
    agentStatus:
      rs.AGENT_STATUS === "DISCONNECTED" &&
      (rs.LAST_CONNECT_TIME != null ? new Date(`${rs.LAST_CONNECT_TIME} UTC`).getTime() : 0) >
        new Date().getTime() - 5 * 60 * 1000
        ? "CONNECTED"
        : rs.AGENT_STATUS,
    type: "remote",
    space_id: space, // TODO: check with Bob if this is needed for ShellContext object
    realtimeReplicationStatus: rs.REAL_TIME_REPLICATION_STATUS,
  }));

  return result;
};

export async function getNumberOfRemoteSourcesForAllSpaces(context: IRequestContext): Promise<IRemoteSourceCount[]> {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient();

  const sql = `
    SELECT
      "GRANTEE" AS "spaceName",
      COUNT(DISTINCT "OBJECT_NAME") AS "sourceCount"
    FROM
      "PUBLIC"."GRANTED_PRIVILEGES"
    WHERE
      "OBJECT_TYPE" = 'SOURCE' AND
      -- Privileges for remote sources granted to spaceManager / spaceOwner (= space id)
      "GRANTEE" IN (SELECT DISTINCT "SPACE_ID" FROM "DWC_TENANT_OWNER"."SPACE_SCHEMAS")
    GROUP BY "GRANTEE"
    ORDER BY "sourceCount" DESC;
  `;
  return await client.exec<IRemoteSourceCount>(sql);
}
export const upsertRemoteSource = async function (
  context: IRequestContext,
  remoteSourceInfo: RemoteSourceObject
): Promise<void> {
  let clientOptions: IClientOptions = {};
  const featureFlags = await FeatureFlagProvider.getFeatureFlags(context);
  if (
    featureFlags.DWCO_USER_PROPAGATION &&
    [TypeIds.SAPS4HANACLOUD, TypeIds.SAPS4HANAOP].includes(remoteSourceInfo.typeId) &&
    remoteSourceInfo.configuration?.ConnectionProperties?.userPropagation?.enableUserPropagation === "true"
  ) {
    // Connection validation, Remote table Metadata access and deployment. These activities should be done with the technical user. This technical user should have authorizations for meta data / catalog access.
    // When browsing and importing remote tables for a connection enabled for user propagation we need to switch off the user propagation (disableUserPropagation) when retrieving the HANA connection (see [DW12-5459](https://jira.tools.sap/browse/DW12-5459)).
    clientOptions = { ...clientOptions, disableUserPropagation: true };
  }
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient(clientOptions);
  return await upsertRemoteSourceWithClient(context, remoteSourceInfo, client);
};

export const upsertRemoteSourceWithClient = async function (
  context: IRequestContext,
  remoteSourceInfo: RemoteSourceObject,
  client: any
): Promise<void> {
  // TODO if this condition is false, this is just a silent no-op. Is this what we want?
  const featureFlags = await FeatureFlagProvider.getFeatureFlags(context);
  // The auth_mech added in function adjustForRemoteSourceInner from HDLDB.ts is to facilitate retrieving the authentication type.
  const authType = remoteSourceInfo.configuration?.ConnectionProperties?.connectionproperties?.auth_mech;
  const pse = remoteSourceInfo.configuration?.ConnectionProperties?.configurations?.pseName;
  if (pse) {
    delete remoteSourceInfo.configuration?.ConnectionProperties?.configurations?.pseName;
  }
  if (remoteSourceInfo.typeId === TypeIds.HDLDB || remoteSourceInfo.typeId === TypeIds.ABAP) {
    // This property will no longer be needed later, so it is removed.
    delete remoteSourceInfo.configuration?.ConnectionProperties?.connectionproperties?.auth_mech;
  }
  let userPropagationConfig;
  if (
    featureFlags.DWCO_USER_PROPAGATION &&
    [TypeIds.SAPS4HANACLOUD, TypeIds.SAPS4HANAOP].includes(remoteSourceInfo.typeId)
  ) {
    userPropagationConfig = deepCopy(remoteSourceInfo.configuration?.ConnectionProperties?.userPropagation);
    delete remoteSourceInfo.configuration?.ConnectionProperties?.userPropagation;
  }

  const location = remoteSourceInfo.location.agentName
    ? `AGENT "${escapeDoubleQuotes(remoteSourceInfo.location.agentName)}"`
    : getAdapterLocationValue(remoteSourceInfo.location);

  if (!validateAdapterName(remoteSourceInfo.adapterName, featureFlags)) {
    throw new Error("Invalid DP Agent Adapter Name");
  }

  let configurationInfo = "";
  let credentialInfo = "";

  const bIsGenericConfig = featureFlags.DWC_ADMIN_GENERIC_DPAGENT_ADAPTER && remoteSourceInfo.configurationRaw != null;
  const bIsGenericCredentials =
    featureFlags.DWC_ADMIN_GENERIC_DPAGENT_ADAPTER && remoteSourceInfo.credentialsRaw != null;

  if (!bIsGenericConfig && !bIsGenericCredentials) {
    const { connectionProperties, credentialEntry } = serializeConnection(
      remoteSourceInfo.configuration,
      location === null
    );
    let config = connectionProperties;
    if (!!config && remoteSourceInfo.adapterName === RemoteSourceAdapters.SDA.File) {
      config += ";";
    }
    configurationInfo = `CONFIGURATION '${config}'`;
    const isX509AndABAP = authType === AuthTypes.X509 && remoteSourceInfo.typeId === TypeIds.ABAP;
    // The credential type for ABAP should not be password when using x509 authentication type, so set the credential info to ""
    credentialInfo =
      credentialEntry && !isX509AndABAP
        ? `WITH CREDENTIAL TYPE 'PASSWORD' USING '${escapeSingleQuotes(credentialEntry)}'`
        : "";
  } else {
    // Handle raw configuration information
    if (bIsGenericConfig) {
      configurationInfo = `CONFIGURATION '${escapeSingleQuotes(remoteSourceInfo.configurationRaw || "")}'`;
    } else {
      configurationInfo = getConfigurationXml(remoteSourceInfo.configuration!);
    }

    if (bIsGenericCredentials) {
      credentialInfo = `WITH CREDENTIAL TYPE 'PASSWORD' USING '${escapeSingleQuotes(
        remoteSourceInfo.credentialsRaw || ""
      )}'`;
    } else {
      credentialInfo = getCredentialXml(remoteSourceInfo.credentials);
    }
  }

  const remoteSourceExists = await client.exec(
    `SELECT REMOTE_SOURCE_NAME FROM SYS.REMOTE_SOURCES WHERE REMOTE_SOURCE_NAME = '${escapeSingleQuotes(
      remoteSourceInfo.remoteSourceName
    )}'`
  );
  const mode = remoteSourceExists && remoteSourceExists.length > 0 ? "ALTER" : "CREATE";
  let sql = `${mode} REMOTE SOURCE "${escapeDoubleQuotes(
    remoteSourceInfo.remoteSourceName
  )}" ADAPTER "${escapeDoubleQuotes(remoteSourceInfo.adapterName)}"`;
  if (location) {
    sql += ` AT LOCATION ${location}`;
  }
  sql += ` ${configurationInfo} ${credentialInfo}`;

  if (
    remoteSourceInfo.adapterName === RemoteSourceAdapters.SDA.File ||
    remoteSourceInfo.adapterName === RemoteSourceAdapters.SDA.DeltaSharing ||
    (remoteSourceInfo.typeId === TypeIds.HDLDB && authType === AuthTypes.X509) ||
    (remoteSourceInfo.typeId === TypeIds.ABAP && authType === AuthTypes.X509)
  ) {
    // these adapters are used by connection types HDL_FILES and BUSINESS_DATA_PRODUCT
    let pseName;
    const hanaCredClient = await HanaCredentialClient.fromContext(context);
    if (remoteSourceInfo.isUclSpaceConnection) {
      // For UCL the PSE name is part of the connection payload, only for old connections it needs to be derived
      pseName = pse || hanaCredClient.getUclPseNameWithoutTypeId(remoteSourceInfo.uclAssignmentId!);
    } else {
      // Creation/Update of HDLF connection via UI, BDP connections are always managed by UCL
      pseName = hanaCredClient.getPSEName(remoteSourceInfo.remoteSourceName);
      await hanaCredClient.putPseUcl(pseName);
    }
    sql += ` WITH CREDENTIAL TYPE 'X509' PSE "${escapeDoubleQuotes(pseName)}"`;
  }

  let tokenExchangerSQL = "";
  if (
    featureFlags.DWCO_USER_PROPAGATION &&
    [TypeIds.SAPS4HANACLOUD, TypeIds.SAPS4HANAOP].includes(remoteSourceInfo.typeId)
  ) {
    const enableUserPropagation = userPropagationConfig?.enableUserPropagation;
    logDebug(`enableUserPropagation: ${enableUserPropagation}`, { context });
    if (enableUserPropagation === "true") {
      const iasApplicationDependencyName = userPropagationConfig?.iasApplicationDependencyName;
      const tokenExchangerMode = mode === "CREATE" ? "WITH" : iasApplicationDependencyName ? "SET" : "UNSET";
      if (tokenExchangerMode === "SET" || tokenExchangerMode === "UNSET") {
        tokenExchangerSQL += `${mode} REMOTE SOURCE "${escapeDoubleQuotes(
          remoteSourceInfo.remoteSourceName
        )}" ${tokenExchangerMode} TOKEN EXCHANGER`;
      } else {
        sql += ` ${tokenExchangerMode} TOKEN EXCHANGER`;
      }
      if (tokenExchangerMode === "WITH") {
        sql += ` DSP_USER_PROP_IAS TARGET 'urn:sap:identity:application:provider:name:${iasApplicationDependencyName}'`;
      } else if (tokenExchangerMode === "SET") {
        tokenExchangerSQL += ` DSP_USER_PROP_IAS TARGET 'urn:sap:identity:application:provider:name:${iasApplicationDependencyName}'`;
      }
    } else if (mode === "ALTER" && enableUserPropagation === "false") {
      tokenExchangerSQL += `${mode} REMOTE SOURCE "${escapeDoubleQuotes(
        remoteSourceInfo.remoteSourceName
      )}" UNSET TOKEN EXCHANGER`;
    }
  }

  await client.exec(sql);
  if (tokenExchangerSQL.length > 0) {
    await client.exec(tokenExchangerSQL);
  }
};

/**
 * Checks whether a remote source the given `remoteSourceName` is correctly configured.
 *
 * @param context The request context containing necessary information for the operation
 * @param remoteSourceName The name of the remote source to be checked
 */
export async function checkRemoteSource(context: IRequestContext, remoteSourceName: string): Promise<void> {
  try {
    const customerHana = await CustomerHana.fromRequestContext(context);
    const client = await customerHana.getTenantManagerClient();
    await client.exec("CALL CHECK_REMOTE_SOURCE(?)", [remoteSourceName]);
    logDebug(`checkRemoteSource ${remoteSourceName} successful`, { context });
  } catch (error) {
    // do not throw error, but log it
    logError(`checkRemoteSource ${remoteSourceName} failed: ${JSON.stringify(error)}`, { context });
  }
}

/**
 * Drop a remote source which has been created as runtime artifact of a DWC connection
 * @param context Request context
 * @param remoteSourceName Remote Source to drop
 * @param withCascade Should not be required. But currently HANA Cloud connections using adapter "hanaodbc" leave virtual tables behind
 * in schema _SYS_LDB which prevent dropping the remote source without CASCADE, see DW101-3444. If HANA Cloud changed that behavior such
 * that dropping the remote source without CASCADE also drops these virtual tables, we could get rid of this optional parameter again.
 */
export async function deleteRemoteSource(context: IRequestContext, remoteSourceName: string, withCascade = false) {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient();

  await client.exec(`DROP REMOTE SOURCE "${escapeDoubleQuotes(remoteSourceName)}"${withCascade ? " CASCADE" : ""}`);
}

/**
 * @deprecated This function is deprecated. Use getRemoteSourceObjectTreeAsTable instead.
 */
export async function getRemoteSourceObjectTree(
  context: IRequestContext,
  client: DbClient,
  remoteSourceName: string,
  connectionName: string,
  adapter: string,
  uniqueName: string
): Promise<IRemoteSourceObjectTree> {
  return await getRemoteSourceObjectTreeAsTable(context, client, remoteSourceName, connectionName, adapter, uniqueName);
}

export async function pauseRemoteSource(context: IRequestContext, remoteSourceName: string) {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient();
  try {
    const sql = `ALTER REMOTE SOURCE "${escapeDoubleQuotes(remoteSourceName)}" SUSPEND CAPTURE;`;
    logDebug("pauseRemoteSource SQL: " + sql, { context });
    await client.exec(sql);
    logDebug(`pauseRemoteSource ${remoteSourceName} successful`, { context });

    await auditLogConnection(remoteSourceName, AuditActivity.UNREPLICATE, AuditStatus.SUCCESS, context);
  } catch (error) {
    await auditLogConnection(remoteSourceName, AuditActivity.UNREPLICATE, AuditStatus.FAIL, context);
    logError(`pauseRemoteSource ${remoteSourceName} failed: ${JSON.stringify(error)}`, { context });
    throw error;
  }
}

export async function restartRemoteSource(context: IRequestContext, remoteSourceName: string) {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient();
  try {
    const sql = `ALTER REMOTE SOURCE "${escapeDoubleQuotes(remoteSourceName)}" RESUME CAPTURE;`;
    logDebug("restartRemoteSource SQL: " + sql, { context });
    await client.exec(sql);
    logDebug(`restartRemoteSource ${remoteSourceName} successful`, { context });

    await auditLogConnection(remoteSourceName, AuditActivity.REPLICATE, AuditStatus.SUCCESS, context);
  } catch (error) {
    await auditLogConnection(remoteSourceName, AuditActivity.REPLICATE, AuditStatus.FAIL, context);
    logError(`restartRemoteSource ${remoteSourceName} failed: ${JSON.stringify(error)}`, { context });
    throw error;
  }
}
/**
 * Check if the "deltasharing" adapter is supported by the database.
 *
 * @param context The request context
 * @returns true if the "deltasharing" adapter is supported, false otherwise
 */
export async function deltaSharingAdapterExists(context: IRequestContext): Promise<boolean> {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient();
  const sql = `SELECT 1 FROM SYS.ADAPTERS WHERE ADAPTER_NAME = '${RemoteSourceAdapters.SDA.DeltaSharing}'`;
  try {
    const result = await client.exec(sql);
    return result.length > 0;
  } catch (err) {
    logError(`Error checking for DeltaSharingAdapter: ${err}`, { context });
    return false;
  }
}

export async function checkUserPropagation(context: IRequestContext): Promise<boolean> {
  const customerHana = await CustomerHana.fromRequestContext(context);
  const client = await customerHana.getTenantManagerClient();
  const sql = `SELECT TOKEN_EXCHANGER_NAME FROM TOKEN_EXCHANGERS WHERE TOKEN_EXCHANGER_NAME = ?`;
  const tokenExchanger = "DSP_USER_PROP_IAS";
  try {
    const result = await client.exec<IRemoteSource>(sql, [tokenExchanger]);
    return result.length > 0;
  } catch (e) {
    logError(`Failed to get token exchanger for "${tokenExchanger}". ${e}`, { context });
    return false;
  }
}

/**
 *  Checks if a remote source with the given name exists in the database.
 *
 * @param context The request context
 * @param remoteSourceName The name of the remote source to check
 * @returns true if remote source exists, false otherwise
 */
export async function checkRemoteSourceExists(context: IRequestContext, remoteSourceName: string): Promise<boolean> {
  try {
    const customerHana = await CustomerHana.fromRequestContext(context);
    const client = await customerHana.getTenantManagerClient();
    const sql = `SELECT 1 FROM SYS.REMOTE_SOURCES WHERE REMOTE_SOURCE_NAME = ?`;
    const result = await client.exec<IRemoteSource>(sql, [remoteSourceName]);
    return result.length > 0;
  } catch (err) {
    logError(`Failed to check if remote source exists. Reason: ${err}`, { context });
    throw err;
  }
}
