/** @format */

import { Attributes } from "@opentelemetry/api";
import telemetry from "@sap/dwc-telemetry";
import { isContextFromInternalTenant } from "../metering/helpers";
import { RequestContext } from "../repository/security/requestContext";
import { logError } from "./connectionStatus";
export class connectionMetricWrapper {
  private static telemetryMeter = telemetry.metrics.getMeter();
  private weeklyConnectionsHistogram;
  private weeklyAggregateConnectionsHistogram;
  constructor() {
    // Initialize histogram for weekly connections
    this.weeklyConnectionsHistogram = connectionMetricWrapper.telemetryMeter.createHistogram(
      "dwc.v1.weeklyConnectionsReport",
      {
        description:
          "Datasphere connections management API - Histogram for collecting information about connection objects stored in the database",
      }
    );
    this.weeklyAggregateConnectionsHistogram = connectionMetricWrapper.telemetryMeter.createHistogram(
      "dwc.v1.countPerType",
      {
        description: "Datasphere connections management API - Histogram tracking the number of connections per type",
      }
    );
  }

  private static liveConnectionsCounter = connectionMetricWrapper.telemetryMeter.createHistogram(
    "dwc.v1.upsertConnection",
    {
      description:
        "Datasphere connections management API - Live tracker monitoring creation and update operations for the CM service.",
    }
  );

  /**
   * Records metrics for a weekly connection.
   */
  public async recordUpsertConnectionsMetric(context: RequestContext, connection: any, subType: string): Promise<void> {
    const attributes = await connectionMetricWrapper.getMetricAttributes(
      connection.creation_date,
      context,
      connection,
      subType
    );
    this.weeklyConnectionsHistogram.record(1, attributes);
  }
  public async recordAggregateConnectionsMetric(context: RequestContext, typeId: string, count: number): Promise<void> {
    const isInternalTenant = await isContextFromInternalTenant(context);
    const attributes: Attributes = {
      tenantId: context.tenantId || "unknown",
      isInternalTenant: isInternalTenant || "unknown",
      typeId,
      count,
    };
    this.weeklyAggregateConnectionsHistogram.record(1, attributes);
  }

  /**
   * Builds metric attributes from context and connection info.
   */
  private static async getMetricAttributes(
    creationTimestamp: string,
    context: any,
    connection: any,
    subType: any
  ): Promise<Attributes> {
    const connectionInfo = connection.content.objects[connection.name];
    const isInternalTenant = await isContextFromInternalTenant(context);
    const ConnectionProp = connectionInfo.configuration?.ConnectionProperties;
    const authType =
      [
        ConnectionProp?.authentication?.auth_type,
        ConnectionProp?.configurations?.authentication?.auth_type,
        ConnectionProp?.UI?.authentication?.auth_type,
        ConnectionProp?.ConnectionInfo?.authentication?.auth_type,
      ].find((auth) => auth !== undefined) || "unknown";

    const authenticationMechanism =
      [
        ConnectionProp?.configurations?.authentication?.authenticationMechanism,
        ConnectionProp?.oauth2?.oauth2GrantType,
        ConnectionProp?.oauth2?.oauth2_grant_type,
        ConnectionProp?.UI?.oauth2?.oauth2GrantType,
        ConnectionProp?.configurations?.oauth2?.oauth2_grant_type,
        ConnectionProp?.ConnectionInfo?.oauth2Properties?.oauth2GrantType,
        ConnectionProp?.configurations?.oauth2?.oauth2GrantType,
        ConnectionProp?.ConnectionInfo?.oauth2?.oauth2GrantType,
      ].find((grant) => grant !== undefined) || "unknown";

    return {
      creationTimestamp: connectionMetricWrapper.getFormattedDate(creationTimestamp, context),
      tenantId: context.tenantId || "unknown",
      authType,
      authenticationMechanism,
      typeId: connection.typeId,
      subType: subType || "unknown",
      isInternalTenant: isInternalTenant || "unknown",
      remoteTable: connectionMetricWrapper.getRemoteTableStatus(connectionInfo),
      cloudConnector: connectionMetricWrapper.getCloudConnectorStatus(connectionInfo),
    };
  }
  /**
   * return cloud Connector status
   */
  private static getCloudConnectorStatus(connectionInfo: any): string {
    const properties = connectionInfo?.configuration?.ConnectionProperties;

    const gateways = [
      properties?.configurations?.gateway,
      properties?.ConnectionInfo?.gateway,
      properties?.UI?.gateway,
    ];

    if (gateways.some((gateway) => gateway?.cloud_connector === "true")) {
      return "true";
    }

    if (gateways.some((gateway) => gateway?.cloud_connector === "false")) {
      return "false";
    }

    return "none";
  }

  /**
   * return remoteTable status
   */
  private static getRemoteTableStatus(connectionInfo: any): string | undefined {
    const features = connectionInfo.configuration?.ConnectionFeatures?.features;

    if (features?.remoteTables === true || features?.remoteTables === "true") {
      return "enabled";
    }
    if (features?.remoteTables === false || features?.remoteTables === "false") {
      return "disabled";
    }
    if (features?.dpAgent === "") {
      return "disabled";
    }
    if (features?.dpAgent !== undefined) {
      return "enabled";
    }
    return "not supported";
  }

  /**
   * Builds metric attributes from context and connection info.
   */
  private static async getLiveMetricAttributes(
    context: RequestContext,
    connectionInfo: any,
    state: string,
    subType: string
  ): Promise<Attributes> {
    const isInternalTenant = await isContextFromInternalTenant(context);
    const ConnectionProp = connectionInfo.configuration?.ConnectionProperties;
    const authType =
      [
        ConnectionProp?.authentication?.auth_type,
        ConnectionProp?.configurations?.authentication?.auth_type,
        ConnectionProp?.UI?.authentication?.auth_type,
        ConnectionProp?.ConnectionInfo?.authentication?.auth_type,
      ].find((auth) => auth !== undefined) || "unknown";

    const authenticationMechanism =
      [
        ConnectionProp?.configurations?.authentication?.authenticationMechanism,
        ConnectionProp?.oauth2?.oauth2GrantType,
        ConnectionProp?.oauth2?.oauth2_grant_type,
        ConnectionProp?.UI?.oauth2?.oauth2GrantType,
        ConnectionProp?.configurations?.oauth2?.oauth2_grant_type,
        ConnectionProp?.ConnectionInfo?.oauth2Properties?.oauth2GrantType,
        ConnectionProp?.configurations?.oauth2?.oauth2GrantType,
        ConnectionProp?.ConnectionInfo?.oauth2?.oauth2GrantType,
      ].find((grant) => grant !== undefined) || "unknown";
    return {
      state,
      tenantId: context.tenantId || "unknown",
      typeId: connectionInfo.typeId,
      subType: subType || "unknown",
      authType,
      authenticationMechanism,
      isInternalTenant: isInternalTenant || "unknown",
      remoteTable: connectionMetricWrapper.getRemoteTableStatus(connectionInfo),
      cloudConnector: connectionMetricWrapper.getCloudConnectorStatus(connectionInfo),
    };
  }

  /**
   * Formats a timestamp as YYYY-MM-DD.
   */
  private static getFormattedDate(creationTimestamp: string, context: RequestContext): string {
    const date = new Date(creationTimestamp);
    if (isNaN(date.getTime())) {
      logError([`Invalid date: ${creationTimestamp}`], {
        context,
      });
      return "unknown";
    }

    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");

    return `${year}-${month}-${day}`;
  }

  /**
   * Records metrics for a live connection metric.
   */
  public static async recordLiveUpsertConnectionsMetric(
    context: any,
    connectionInfo: any,
    state: string,
    subType: any
  ): Promise<void> {
    const attributes = await connectionMetricWrapper.getLiveMetricAttributes(context, connectionInfo, state, subType);
    connectionMetricWrapper.liveConnectionsCounter.record(1, attributes);
  }
}
