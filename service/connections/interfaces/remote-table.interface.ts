/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 * @format
 */

import { DataAccessLevel, DataProvisioningOptions } from "../utils/Constants";

/**
 * Interface definition for ABAP ODBC adapter configuration parameters.
 */
export interface IAbapOdbcParameters {
  /** ABAP username or alias name */
  uid?: string;
  /** Type of parameter Uid (ABAP username or alias name). Default `alias` */
  uidType: string;
  /**
   * Conversion rule for values of ABAP data types, "native", "semantic" or
   * "semanticDatsTimsAsWchar". Controls how ABAP data types are mapped to ODBC types
   */
  typeMap: string;
}

/**
 * Interface definition for remote table options with which to configure remote
 * table features.
 */
export interface IRemoteTableOptions {
  /** Data provisioning option: "dpAgent", "direct" or "none" */
  dataProvisioningOption: DataProvisioningOptions;
  /** Data access level */
  dataAccess?: DataAccessLevel;
  /** ABAP ODBC adapter (ABAP SQL) configuration parameters */
  abapOdbcParameters?: IAbapOdbcParameters;
}
