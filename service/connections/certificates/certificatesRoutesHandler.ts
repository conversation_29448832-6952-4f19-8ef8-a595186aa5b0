/** Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 * @format
 */

import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { FeatureFlagProvider } from "../../featureflags/FeatureFlagProvider";
import { sendErrorResponse } from "../../server/errorResponse";
import { CertificateAPIError, CertificateAPIErrorCode } from "./CertificateAPIError";
import * as Certificate from "./certificate";

export async function createCertificate(req: Request, res: Response) {
  try {
    req.setTimeout(600000, function () {
      // After testing, it seems that this setTimeout statement has no effect. Even if the request takes 30 minutes
      // this timeout callback is never executed and also the whole request just continues
      // and happily returs with a correct result visible for the customer/client.
      // (discussed with resillience and they will take it up generally)
      throw new Error("Certificate upload request has timed out. Maximum upload time is ten minutes.");
    });
    const data = req.body;
    const context = req.context;
    const certificateResponse: Certificate.ICertificateInfo = await Certificate.createCertificate(context, data);
    if (!!certificateResponse) {
      res.send(certificateResponse);
    }
  } catch (err) {
    if (!(err instanceof CertificateAPIError)) {
      sendErrorResponse(req.context, "uploadCertError", { err });
      return;
    }
    const { respErrCode, status } = mapToResponse(err.getErrorCode());
    sendErrorResponse(req.context, respErrCode, { err, status });
  }
}

export function mapToResponse(errCode: CertificateAPIErrorCode) {
  switch (errCode) {
    case CertificateAPIErrorCode.ALREADY_EXISTS: {
      return { respErrCode: "certExistError", status: StatusCodes.BAD_REQUEST };
    }
    case CertificateAPIErrorCode.INVALID_CERT: {
      // TODO may be expired. Should be visible in user message.
      return { respErrCode: "invalidCertError", status: StatusCodes.BAD_REQUEST };
    }
    case CertificateAPIErrorCode.NOT_SPECIFIED:
    default: {
      return { respErrCode: "uploadCertError", status: StatusCodes.INTERNAL_SERVER_ERROR };
    }
  }
}

export async function getCertificates(req: Request, res: Response) {
  try {
    let purpose: string | undefined;
    if (req.query && req.query.purpose) {
      purpose = req.query.purpose as string;
    }
    const certificateDetails = await Certificate.getCertificates(req.context, purpose);
    res.send(certificateDetails);
  } catch (err) {
    sendErrorResponse(req.context, "getCertsError", { err });
  }
}

export async function deleteCertificate(req: Request, res: Response) {
  try {
    const certificateFingerprint = req.params.fingerprint;
    const certificateDetails = await Certificate.getCertificates(req.context);
    const selectedConnection = certificateDetails.find(
      (certificate: any) => certificate.fingerPrint === certificateFingerprint
    );
    if (!selectedConnection) {
      return res.status(404).json({ message: "Certificate not found" });
    } else {
      await Certificate.deleteCertificate(req.context, certificateFingerprint);
      res.send({
        message: "Certificate Deleted",
      });
    }
  } catch (err) {
    sendErrorResponse(req.context, "deleteCertError", { err });
  }
}

export async function updateCertificate(req: Request, res: Response) {
  if (!(await FeatureFlagProvider.isFeatureActive(req.context, "DWCO_X509_AUTH_USER"))) {
    return res.status(404);
  }
  try {
    const data = req.body;
    const updatedPurposes = data.purposes;
    if (!updatedPurposes || updatedPurposes.remote === undefined || updatedPurposes.x509 === undefined) {
      throw new CertificateAPIError("Request missing purpose parameters", CertificateAPIErrorCode.UPDATE_FAILED);
    } else if (!updatedPurposes.remote && !updatedPurposes.x509) {
      // One of remote or x509 must be enabled as certificate needs at least one purpose set
      throw new CertificateAPIError("Request has no purpose enabled", CertificateAPIErrorCode.UPDATE_FAILED);
    }
    const certificateFingerprint = req.params.fingerprint;
    const certificateDetails = await Certificate.getCertificates(req.context);
    const selectedCertificates = certificateDetails.filter(
      (certificate: any) => certificate.fingerPrint === certificateFingerprint
    );
    if (selectedCertificates.length === 0) {
      return res.status(404).json({ message: "Certificate not found" });
    } else {
      await Certificate.updateCertificate(req.context, selectedCertificates, updatedPurposes);
      res.send({
        message: "Certificate Updated",
      });
    }
  } catch (err) {
    sendErrorResponse(req.context, "updateCertError", { err });
  }
}
