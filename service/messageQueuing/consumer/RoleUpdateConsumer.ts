/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { <PERSON><PERSON>, StatusError, httpClient } from "@sap/dwc-http-client";
import { getLogger } from "@sap/dwc-logger";
import { MessageEvent } from "@sap/orca-starter-solace";
import { StatusCodes } from "http-status-codes";
import { ExternalCallCategories } from "../../../shared/common/ExternalCallCategories";
import { BwzCallbackConfig, fetchBwzCallbackConfig, generateModel, getMD5Hash } from "../../cdm";
import { RequestContext } from "../../repository/security/requestContext";
import { BaseConsumerQueue } from "../BaseConsumerQueue";

export enum RoleEventAction {
  MODIFIED = "modified",
  DELETED = "deleted",
  CREATED = "created",
}

export interface RoleUpdateMessage {
  tenantId: string;
  roleId: string;
  action: RoleEventAction;
  fpaVersion: string;
}

export interface MessageValidityStatus {
  valid: boolean;
  reason?: string;
}

const logger = getLogger(`Role Update Consumer`);

export const retriableStatusCodes = [
  StatusCodes.TOO_MANY_REQUESTS,
  StatusCodes.INTERNAL_SERVER_ERROR,
  StatusCodes.SERVICE_UNAVAILABLE,
  StatusCodes.GATEWAY_TIMEOUT,
];
export class RoleUpdateConsumer extends BaseConsumerQueue<RoleUpdateMessage> {
  public async consume(messageEvent: MessageEvent): Promise<void> {
    let context: IRequestContext | null = null;
    try {
      const msg = messageEvent.getMessage();
      if (!msg) {
        return;
      }
      const roleEvent = await this.decodeMessage<RoleUpdateMessage>(this.getMessageContent(msg));
      const roleEventText = JSON.stringify(roleEvent);
      logger.logInfo(`Message for user role update received with content ${roleEventText}`, {
        context,
      });
      const status = this.validateMessage(roleEvent);
      if (!status.valid) {
        logger.logError(`Message content is invalid, reason: ${status.reason}`, { context });
        return;
      }
      context = RequestContext.createFromTenantId(roleEvent.tenantId);
      // check whether to inform BWZ about new CommonData Model changes
      const callBacks = await fetchBwzCallbackConfig(context);
      if (callBacks.length === 0) {
        return;
      }
      const cdmHash = getMD5Hash(JSON.stringify(await generateModel(context)));
      for (const callBack of callBacks) {
        if (cdmHash !== callBack.cdmHash) {
          // call back BWZ instance to fetch new CDM
          await this.callBack(context, callBack);
        }
      }
    } catch (err) {
      logger.logError(`Failed to consume message for user role update. Error: ${err}`, { context });
    } finally {
      if (context !== null) {
        await context.finish();
      }
      messageEvent.acceptMessage();
    }
  }

  // expose logger for testing purposes
  public static getLogger() {
    return logger;
  }

  protected getQueueName(): string {
    return "orca.role.update";
  }

  protected getProtobufPath(): string {
    // return "./proto/RoleUpdateMessage.proto";
    throw new Error("Method not implemented."); // Mandatory, never called because of overriden decodeMessage()
  }

  protected getMessageType(): string {
    // return "com.sap.orca.protobuf.svc.ums.messaging.RoleEvent";
    throw new Error("Method not implemented."); // Mandatory, never called because of overriden decodeMessage()
  }

  public async decodeMessage<T>(content: Uint8Array): Promise<T> {
    try {
      const decodedMessage = JSON.parse(content.toString()) as T;
      return decodedMessage;
    } catch (error) {
      throw new Error("Failed to decode request message: " + error);
    }
  }

  private validateMessage(message: RoleUpdateMessage): MessageValidityStatus {
    if (!message?.roleId) {
      return { valid: false, reason: "'roleId' is missing" };
    }
    if (!message?.tenantId) {
      return { valid: false, reason: "'tenantId' is missing" };
    }
    if (!message?.action) {
      return { valid: false, reason: "'action' is missing" };
    } else if (!Object.values(RoleEventAction).includes(message.action)) {
      return {
        valid: false,
        reason: `action value ${message.action} is not one of: ${Object.values(RoleEventAction).join(", ")}`,
      };
    } else if (message?.fpaVersion && typeof message.fpaVersion !== "string") {
      return {
        valid: false,
        reason: `fpaVersion must be a string literal`,
      };
    }
    return { valid: true };
  }

  private async callBack(context: IRequestContext, callBack: BwzCallbackConfig): Promise<void> {
    const url = callBack.url;
    const headers = new Headers();
    headers.set("Authorization", `Bearer ${callBack.token}`);
    await httpClient.call({
      url,
      opts: {
        headers,
        requestContext: context,
        callCategory: ExternalCallCategories.CDM,
        retry: {
          delay: 30000,
          maxRetries: 8,
          preventRetry: (err) => {
            if (err instanceof StatusError && retriableStatusCodes.includes(err.code as StatusCodes)) {
              return false;
            }
            return true;
          },
        },
      },
    });
  }
}
