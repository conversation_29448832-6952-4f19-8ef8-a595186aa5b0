/** @format */

// FILEOWNER: [Provisioning]

import { dwcMetrics } from "@sap/dwc-flexible-tenant-sizing";
import { httpClient, HttpMethod } from "@sap/dwc-http-client";
import { getLogger } from "@sap/dwc-logger";
import { StatusCodes } from "http-status-codes";
import { Metric, TenantClassification } from "../../shared/provisioning/ftc/types";
import { TenantInformationProvider } from "../featureflags/TenantInformationProvider";
import { ITenantInformation, ITenantLicenses } from "../featureflags/types";
import { ProvisioningInformationProvider } from "../provisioning/ProvisioningInformationProvider";
import { IRequestContext } from "../repository/security/common/common";
import { ExternalCallCategory } from "../repository/security/common/externalCallTypes";
import { TimeUnit } from "../repository/security/timeunit";
import { IKeyInstance } from "../scp/types";
import { EnabledForTenant } from "../task/predefined/ISystemTaskDefinition";
import { MetricsHandlerError } from "./metrics/errors/MetricsHandlerError";
const { logError } = getLogger(`MeteringService`);

type thresholdName = keyof ITenantLicenses;
const metricToThresholdMap = new Map<thresholdName, Metric>([
  ["thresholdStorage", dwcMetrics.STORAGE],
  ["thresholdMemory", dwcMetrics.COMPUTE],
  ["thresholdDataLakeStorage", dwcMetrics.DATA_LAKE],
  ["thresholdBWBridge1", dwcMetrics.BW_BRIDGE],
]);

export const defineEnablementOfMeteringScheduleForCPEA: EnabledForTenant = async (context: IRequestContext, schedule) =>
  false;

export const defineEnablementOfMeteringScheduleForSubscription: EnabledForTenant = async (
  context: IRequestContext,
  schedule
) => false;

export const defineEnablementOfMeteringScheduleForTestCollect: EnabledForTenant = async (
  context: IRequestContext,
  schedule
) => false;

export function getMetricIdFromThreshold(thresholdName: keyof ITenantLicenses) {
  return metricToThresholdMap.get(thresholdName) || undefined;
}

export function isTddTenant(tenantInformation: ITenantInformation | ProvisioningInformationProvider) {
  return Boolean(tenantInformation.classification === TenantClassification.DWCpartnerTest);
}
export function isTestTenant(tenantInformation: ITenantInformation | ProvisioningInformationProvider) {
  return Boolean(tenantInformation.classification === TenantClassification.DWCcustomerTest);
}

export function isInternalTenant(tenantInformation: ITenantInformation | ProvisioningInformationProvider): boolean {
  return Boolean(
    tenantInformation.classification === TenantClassification.DWCinternal ||
      tenantInformation.classification === TenantClassification.DWCinternalProductive ||
      tenantInformation.classification === TenantClassification.DWCPoC
  );
}

export async function isContextFromInternalTenant(context: IRequestContext): Promise<boolean> {
  const tenantInformation = await TenantInformationProvider.getTenantInformation(context);
  if (!tenantInformation) {
    throw new Error("Tenant Information could not be fetched.");
  }
  return isInternalTenant(tenantInformation);
}

export async function requestBearerTokenForLargeSystems(
  context: IRequestContext,
  hanaKey: IKeyInstance
): Promise<string> {
  try {
    const uaaInfo = hanaKey.credentials.uaa!;
    const url = new URL("/oauth/token?grant_type=client_credentials", uaaInfo.certurl).href;

    const requestBody = new URLSearchParams();
    requestBody.append("grant_type", "client_credentials");
    requestBody.append("response_type", "token");
    requestBody.append("client_id", hanaKey.credentials.uaa?.clientid!);

    const requestOptions = {
      method: HttpMethod.POST,
      body: requestBody,
      acceptedStatusCodes: [StatusCodes.OK, StatusCodes.BAD_REQUEST],
      requestContext: context,
      callCategory: ExternalCallCategory.LargeSystemsRestAPI,
      callTimeout: TimeUnit.SECONDS.toMillis(30),
      clientId: uaaInfo.clientid,
      grant_type: "client_credentials",
      key: String(uaaInfo.key).replace(/\\n/g, "\n"),
      cert: String(uaaInfo.certificate).replace(/\\n/g, "\n"),
      minVersion: "TLSv1.2",
      circuitBreakerContext: {},
    } as any;

    const response = await httpClient.call({
      url,
      opts: requestOptions,
    });
    const token = (
      response?.body as {
        access_token?: string;
        [key: string]: unknown;
      }
    )?.access_token;
    if (!token) {
      throw new MetricsHandlerError("Could not get bearer token from HANA UAA service", StatusCodes.BAD_REQUEST);
    }
    return token;
  } catch (error) {
    logError(error, { context });
    throw new MetricsHandlerError(
      `Could not get bearer token from HANA UAA service. ${error}`,
      StatusCodes.BAD_REQUEST
    );
  }
}
