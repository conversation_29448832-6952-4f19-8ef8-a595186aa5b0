/** @format */

// FILEOWNER: [dw101_build_and_delivery]

import { IFeatureFlagsMap as IFeatureFlagsMapReuse } from "@sap/dwc-context-checks";

// This version is automatically updated by "add-version.js"
export const version = "2025.13";
export const externalFeatureFlags = {
  DWC_FEATURES: true, // enabled since 2019.17 - global flag for switching on DWC in a tenant
  // ~~~~~~~~~~~~~~~~~~~ ADMIN FLAGS ~~~~~~~~~~~~~~~~~
  DWC_ADMIN_DATABASE_CUSTOMER_USERGROUPS: false,
  DWC_ADMIN_GENERIC_DPAGENT_ADAPTER: false,
  DWC_ADMIN_ONPREMISE_DPAGENT: true, // enabled since 2020.22

  // ~~~~~~~~~~~~~~~~~~~ INFRASTRUCTURE FLAGS ~~~~~~~~~~~~~~~~~
  DWC_INFRA_FEATUREFLAGS_MICROSERVICE: false,
  DWC_INFRA_MULTI_LANGUAGE_PACK_6: false,
  DWC_INFRA_REPOSITORY_ASYNCSAVE: true, // enabled since 2020.23
  DWC_INFRA_STORAGE_DATA_UI: true, // enabled since 2020.22
  DWC_INFRA_SHOW_LASTERROR: true, // enabled since 2021.02
  DWC_INFRA_TENANT_ADMIN_ALL_SPACES: false,

  // ~~~~~~~~~~~~~~~~~~~ MODELLING FLAGS ~~~~~~~~~~~~~~~~~
  DWC_MODELING_BUSINESS_LAYER_MULTI_DELETE: true, // enabled since 2021.03
  DWC_MODELING_DATABUILDER_ASSOCIATIONS_LIST: true, // enabled since 2020.11
  DWC_MODELING_DEPLOY_AFTER_IMPORT: false,
  DWC_MODELING_NEWCOLUMNNAME: false,
  DWC_MODELING_SQL_SCRIPT_VIEW_ASSOCIATIONS: false,
  DWC_MODELING_SWITCH_TECHNICAL_NAME: true, // enabeled since 2020.14
  DWC_MODELING_TRANSACTIONCONSISTENCY: false,
  DWC_MODELING_VIEW_COMPUTE_SUBSELECT: true, // enabled since 2020.22
  DWC_MODELING_INTERNAL_SQL_HIERARCHIES: false,
  DWC_MODELING_CHANGE_SPACE_CONTENT_TRANSPORT: false,

  // ~~~~~~~~~~~~~~~~~~~ REMOTE CONNECTION/ TABLE FLAGS ~~~~~~~~~~~~~~~~~
  DWC_CONNECTION_HANA_ON_PREM_SDA: false,
  DWC_CONNECTION_MYSQL: false,
  DWC_CONNECTION_PARTNER_SNAPLOGIC: false,
  DWC_CONNECTION_PARTNER_VERSION_DRAFT: false,
  DWC_CONNECTION_REDSHIFT: false,
  DWC_CONNECTION_SEMANTIC_CDI_TYPES: true, // enabled since 2020.19
  DWC_CONNECTION_TERADATA: false,
  DWC_REMOTE_TABLE_MONITOR_CANCEL: false,

  // ~~~~~~~~~~~~~~~~~~~ SHELL FLAGS ~~~~~~~~~~~~~~~~~

  // ~~~~~~~~~~~~~~~~~~~ SPACES FLAGS ~~~~~~~~~~~~~~~~~
  DWC_SPACES_LOCKED_SPACE_ALLOWS_MODELLING: false,
  DWC_SPACES_PREVALIDATION: false,
  DWC_SPACES_SHARE_ARTEFACTS: true, // enabled since 2020.21
  INFRA_SCOPE_DEPENDENT_ROLES: false,
  DWCO_INFRA_SDPCONVERSION: false,

  // ~~~~~~~~~~~~~~~~~~~ SAC FLAGS ~~~~~~~~~~~~~~~~~
  INFRA_DWC_DEFAULT_APP: true, // enabled since 2019.23
  INFRA_DWC_GA_USER_ROLES_LICENSES: true, // enabled since 2019.22
  INFRA_DWC_SPACE_ROOT_PRIVATE_ACCESS: false,
  INFRA_DWC_TWO_TENANT_MODE: true, // Product switcher repaired
  INFRA_REPO_DWC_INTEGRATION: true, // enabled since 2019.22
  OEM_DWC_ENABLE_SELF_CONN_AUTO_UPDATE: true, // enabled since 2019.23
  OEM_DWC_LIVE_INTEGRATION: true, // enabled since 2019.23
  OEM_DWC_SHOW_CONNECTION: false,
  OEM_ACN_DWC_OBJECT_TYPES: false,
  INFRA_BDC_APPINT_TENANT_MAPPING: true, // enabled since 2025.01
  OAUTHSERVICE_MANAGED_CLIENTS: true, // enabled since 2025.01

  // ~~~~~~~~~~~~~~~~~~~ DATA CATALOG FLAGS ~~~~~~~~~~~~~~~~~
  INFRA_ONE_CATALOG: false,
  DWC_ONEDATACATALOG_FEATURES: true,

  // Data Plane Dev Tenant
  INFRA_TENANT_PROVIDER_SAP: false,

  // ~~~~~~~~~~~~~~~~~~~ BDC Flags ~~~~~~~~~~~~~~~~~
  INFRA_BDC_COCKPIT: false,
};

export interface ISafeCalculatedFeatureFlags {
  DWC_DUMMY_SPACE_PERMISSIONS: boolean;
  DWCO_DUMMY_SDPCONVERSION: boolean;
}

export function getCalculatedFeatureflags(input: IFeatureFlagsMapReuse): IFeatureFlagsMapReuse {
  return {
    DWC_DUMMY_SPACE_PERMISSIONS:
      input.DWCO_INFRA_SPACE_PERMISSIONS && input.INFRA_SCOPE_DEPENDENT_ROLES && input.INFRA_DWC_TWO_TENANT_MODE, // DWCO_INFRA_SPACE_PERMISSIONS shall be dependent on INFRA_SCOPE_DEPENDENT_ROLES (SAC FF) and 1T tenants should not (and cannot) be converted to SDP
    DWCO_DUMMY_SDPCONVERSION:
      input.INFRA_SCOPE_DEPENDENT_ROLES && input.DWCO_INFRA_PREP_SDPCONVERSION && input.INFRA_DWC_TWO_TENANT_MODE,
  };
}

export const FEATURE_FLAG_DEFINITION = {
  DWCO_LOCAL_TABLE_FILES_VACUUM_SPARK_SELECTION: {
    description: " Local Table (File) - Spark application selection for housekeeping",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4600",
    depFeature: [],
  },
  DWCO_LOCAL_TABLE_FILES_ZORDER: {
    description: "Local Table (File) - support Z-ordering in optimize task",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4150",
    depFeature: ["DWCO_LOCAL_TABLE_FILES", "DWCO_LOCAL_TABLE_FILES_SIZE_DELTA_TABLE"],
  },
  DWCO_LOCAL_TABLE_REPARTITIONING: {
    description: "Local Tables - Repartitioning",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2642",
    depFeature: [],
  },
  DWCO_LOCAL_TABLE_TASKS_ACTIVE_RECORDS: {
    description: "Local Tables - all (non-technical) task types to work with active records entity (technical debt) ",
    defaultOn: true,
    version: "2025.09",
    reqId: "DS00-4104",
    depFeature: [],
  },
  DWCO_LS_ODC_UCL_ORD_EXPOSURE: {
    description: "Implement ORD Exposure endpoints for Datasphere Data Products",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-4073",
    landscape: true,
  },
  DWCO_LS_BDC_COCKPIT_HEALTHCHECKS: {
    description: "Health service & monitoring endpoints for BDC Cockpit Tenants",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-3541",
    landscape: true,
  },
  DWCO_LS_CALM_INTEGRATION_AUTOMATION_MONITORING: {
    description: "Integrate SAP Datasphere into SAP Cloud ALM for Job & Automation Monitoring",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3081",
    landscape: true,
  },
  DWCO_LS_CALM_INTEGRATION_HEALTH_MONITORING: {
    description: "Integrate SAP Datasphere into SAP Cloud ALM for Health Monitoring",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-2419",
    landscape: true,
  },
  DWCO_LS_DWAAS_CORE_AUTOSCALER: {
    description: "Initial Landscape Feature Toggle",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2101",
    landscape: true,
  },
  DWCO_LS_MONITORING_PROVISIONING: {
    description: "Enable send provisioning Dynatrace logs",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-1315",
    landscape: true,
  },
  DWCO_LS_M_TABLE_STATISTICS_SELECT: {
    description: "Enables provisioning procedure to enable HANA monitoring view",
    defaultOn: true,
    version: "2024.24",
    reqId: "DS00-3821",
    landscape: true,
  },
  DWCO_LS_INITIAL: {
    description: "Initial Landscape Feature Toggle",
    defaultOn: true,
    version: "2024.05",
    reqId: "DW00-6279",
    landscape: true,
  },
  DWCO_LS_MARKETPLACE_DS001384_CDS_UPDATE: {
    description: "Update to @sap/cds >= 6",
    defaultOn: true,
    version: "2025.09",
    reqId: "DS00-1384",
    landscape: true,
  },
  DWCO_LSA_DELETE_INSTANCES: {
    description: "Delete HDLF and Spark failed instances",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4817",
    depFeature: [],
  },
  DWCO_LSA_RESOURCE_MONITOR: {
    description: "System Monitor: Monitoring HDLF storage and spark application usage in LSA-only spaces",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4453",
    depFeature: [],
  },
  DWCO_LS_BDCC_SUPPORT_UI: {
    description: "[Post BDC GA] Create CIC UI for BDC Cockpit support routes",
    defaultOn: true,
    version: "2025.10",
    reqId: "DS00-4612",
    landscape: true,
  },

  DWCO_TEST_ON: {
    description: "Test flag that defaults to on",
    defaultOn: true,
    version: "2020.20",
    reqId: "DW00-000",
  },
  DWCO_TEST_OFF: {
    description: "Test flag that defaults to off",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-000",
  },
  DWCO_ADD_AP10_TO_BDC_ESTIMATOR: {
    description: "[BDC Estimator]Add New Landscapes by 2025.Q2",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5283",
    depFeature: [],
  },
  DWCO_ADD_AP11_TO_BDC_ESTIMATOR: {
    description: "[BDC Estimator]Add New Landscapes by 2025.Q2",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5283",
    depFeature: [],
  },
  DWCO_ADD_CA10_TO_BDC_ESTIMATOR: {
    description: "[BDC Estimator]Add New Landscapes by 2025.Q2",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5283",
    depFeature: [],
  },
  DWCO_ADD_EU30_TO_BDC_ESTIMATOR: {
    description: "[BDC Estimator]Add New Landscapes by 2025.Q2",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5283",
    depFeature: [],
  },
  DWCO_ADD_IN30_TO_BDC_ESTIMATOR: {
    description: "[BDC Estimator]Add New Landscapes by 2025.Q2",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5283",
    depFeature: [],
  },
  DWCO_ADD_US30_TO_BDC_ESTIMATOR: {
    description: "[BDC Estimator]Add New Landscapes by 2025.Q2",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5283",
    depFeature: [],
  },
  DWCO_ADMIN_API_ACCESS: {
    description: "[LSA][ECN_Advisor]Provisining steps to enable admin-api-access",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-3221",
    depFeature: [],
  },
  DWCO_ADVISOR_CROSSSPACE_2: {
    description: "View Analyzer - Cross-Space Analysis in Partition Advisor and Preview Lineage",
    defaultOn: true,
    version: "2025.10",
    reqId: "DS00-963",
    depFeature: [],
  },
  DWCO_AI_UNITS_SKU: {
    description: "FF indicates availability of AI Units SKU on the customer side",
    defaultOn: false,
    version: "2024.24",
    reqId: "DS00-3324",
    depFeature: [],
  },
  DWCO_API_TUP_SUPPORT: {
    description: "Centralized Feature Flag for DSP TUP OAuth Client Adoption Strategy",
    defaultOn: true,
    version: "2025.09",
    reqId: "DS00-5110",
    depFeature: [],
  },
  DWCO_BDC: {
    description: "Master/Parent BDC Feature Toggle",
    defaultOn: false,
    version: "2024.20",
    reqId: "DS00-3433",
    depFeature: [],
  },
  DWCO_BDC_EXT_STORAGE_API: {
    description: "Extensions to catalog API for BDC Cockpit storage",
    defaultOn: true,
    version: "2025.12",
    reqId: "DS00-4989",
    depFeature: [],
  },
  DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP: {
    description: "[BDC] Cleanup failed installation/activation of IA/ DP ",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4821",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_GA: {
    description: "Master/Parent BDC GA Feature Toggle",
    defaultOn: false,
    version: "2024.25",
    reqId: "DS00-4076",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION: {
    description: "[BDC] UCL Unassign event  proper treatment",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4751",
    depFeature: [],
  },
  DWCO_BDC_BLOCK_FORMATION_DELETION: {
    description: "[BDC] [post-GA] Blocking deletion of UCL  Formation  ",
    defaultOn: true,
    version: "2025.05",
    reqId: "DS00-4439",
    depFeature: ["DWCO_UCL_TENANT_MAPPING_BDC_TENANT"],
  },
  DWCO_BDC_CIC_ENTITLEMENT_CHECK: {
    description: "[BDC] Enhance Entitlement Check by additional Licenses",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5063",
    depFeature: [],
  },
  DWCO_BDC_CLI_FOR_DATA_PRODUCTS: {
    description: "[BDC] CI/CD requirement for Data product on-boarding",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-4555",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_COCKPIT: {
    description: "[BDC] Working skeleton",
    defaultOn: true,
    version: "2024.23",
    reqId: "DS00-3065",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_BDC_COCKPIT_FAILURE_DETAILS: {
    description: "[BDC] Provide to BDC Cockpit user detailed information about IAs and DPs",
    defaultOn: true,
    version: "2025.09",
    reqId: "DS00-4885",
    depFeature: [],
  },
  DWCO_BDC_COCKPIT_HAUM_MEASUREMENT: {
    description: "Add HAUM Measurements to BDC Cockpit",
    defaultOn: true,
    version: "2025.09",
    reqId: "DS00-3847",
    depFeature: [],
  },
  DWCO_BDC_COCKPIT_ONE_SOURCE_FOR_ALL_IAS: {
    description: "[BDC] [HCM] Add constraints in Cockpit when installing the second insight application",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4860",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_COCKPIT_SHOW_PKG_IN_STATE_DEV: {
    description: "[BDC] [GA] Allowing BDC Package file testing in production",
    defaultOn: false,
    version: "2025.02",
    reqId: "DS00-4549",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_COCKPIT_SPECIFIC_ACN_VERSION_IMPORT: {
    description: "The installation of insight app should onboard version of DSP and SAC",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4784",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_CONTINUE_DATAPRODUCT_PROVISIONING_ON_PROVISIONING_ERROR: {
    description: "BDC Phase1 Cockpit should continue provisioning of data products after self-healing prov error",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5528",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_DBX_ACCESS_POLICIES: {
    description: "Provide Access Policy (DBX Identity Type) to enable BDC-Databricks",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4605",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_DBX_BW_DELTA_SHARE: {
    description: "[Delta Share BW] Tenant Mapping of DSP as provider of Data Products with DBX",
    defaultOn: true,
    version: "2025.09",
    reqId: "DS00-4693",
    depFeature: ["DWCO_BDC_GA", "DWCO_UCL_TENANT_MAPPING_DATA_CONNECT"],
  },
  DWCO_BDC_DELTA_SHARE_DP_LINEAGE: {
    description: "Extraction of Data Lineage for DSP Delta-Share Data Products",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4799",
    depFeature: ["DWCO_ODC_DELTASHARE_DP_EXTRACTION"],
  },
  DWCO_BDC_DP_ASSOCIATIONS: {
    description: "[HCM] Cross Data Products associations support",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5144",
    depFeature: [],
  },
  DWCO_BDC_DP_INGESTION_SPACE: {
    description: "[BDC] Data product Ingestion space",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-3814",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_DP_INGESTION_SPACE_ENTITY_NAMING: {
    description: "[BDC] [HCM] Adapt naming convention for Data Product entities",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-4737",
    depFeature: [],
  },
  DWCO_BDC_DP_INGESTION_SPACE_FEDERATION: {
    description: "[BDC] FOS Data Product Semantic onboarding : Federation via Remote Tables ",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-4264",
    depFeature: ["DWCO_BDC_DP_INGESTION_SPACE"],
  },
  DWCO_BDC_DP_INGESTION_SPACE_STABILITY: {
    description: "[BDC] Data product on-boarding: Handling of re-established Shared Connection",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-4631",
    depFeature: ["DWCO_BDC_DP_INGESTION_SPACE"],
  },
  DWCO_BDC_DP_UNINSTALLATION: {
    description: "[BDC] Datasphere Manual Data product uninstallation",
    defaultOn: true,
    version: "2025.09",
    reqId: "DS00-3713",
    depFeature: ["DWCO_BDC_DP_INGESTION_SPACE"],
  },
  DWCO_BDC_ENTITLEMENT_CHECK: {
    description: "[BDC] Insight Apps & Data Packages should be available for Installation",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-3907",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_ENTITLEMENT_OFFBOARDING: {
    description: "[BDC] Uninstall Insight App / Deactivate Data package when customer lack Insight app entitlements",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-3884",
    depFeature: ["DWCO_BDC_GA", "DWCO_BDC_ENTITLEMENT_CHECK"],
  },
  DWCO_BDC_ESTIMATOR: {
    description: "[BDC-GA] BDC Estimator - SAC, DSP, BW*PCE cards + technical foundation",
    defaultOn: false,
    version: "2025.08",
    reqId: "DS00-3839",
    depFeature: [],
  },
  DWCO_BDC_ESTIMATOR_DBX: {
    description: "[BDC-GA] BDC Estimator - DBX card",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-4254",
    depFeature: ["DWCO_BDC_ESTIMATOR"],
  },
  DWCO_BDC_EXTRACTION: {
    description: "[BDC]Enhance UCL extraction of catalog to support BDC formation",
    defaultOn: true,
    version: "2024.19",
    reqId: "DS00-3150",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_BDC_GA_UCL_TENANT_MAPPING_DSP: {
    description: "[BDC GA] Support Tenant Mapping between BDC and DSP",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-3946",
    depFeature: ["DWCO_UCL_TENANT_MAPPING_BDC_TENANT"],
  },
  DWCO_BDC_GA_DEPLOY_REPLICATION_FLOW: {
    description: "Support schema change without stopping running replication flow (BDC Data product use case) ",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-4121",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_GA_UCL_TENANT_MAPPING_DBX: {
    description: "[BDC GA] Support Tenant Mapping between BDC and DSP to DBX",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-3947",
    depFeature: ["DWCO_UCL_TENANT_MAPPING_BDC_TENANT"],
  },
  DWCO_BDC_GA_UCL_TENANT_MAPPING_S4PCE: {
    description: "[BDC GA] support UCL tenant mapping between BDC + DSP and S/4 PCE",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-3918",
    depFeature: ["DWCO_UCL_TENANT_MAPPING_BDC_TENANT"],
  },
  DWCO_BDC_INSIGHT_APP_EXTRACTION: {
    description: " [BDC] Insight apps extraction for BDC",
    defaultOn: true,
    version: "2024.22",
    reqId: "DS00-3393",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_BDC_INSIGHT_BILLING: {
    description: "BDC Insight Apps Billing Metrics",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4763",
    depFeature: [],
  },
  DWCO_BDC_LANDSCAPE_CONNECTION_STATUS: {
    description: "BDC Cockpit checking connection status",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4783",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_MANUAL_OFFBOARDING: {
    description: "[BDC] BDC Cockpit user uninstalls manually Insight App / deactivate Data package ",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-3845",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_MANUAL_ONBOARDING_RETRY: {
    description: "[BDC] Retry the installation/activation of IA and DP  after an installation/activation failure",
    defaultOn: true,
    version: "2025.09",
    reqId: "DS00-4814",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_MODEL_OFFBOARDING: {
    description: "[BDC] DSP Insight App offboarding",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-3715",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_MODEL_ONBOARDING: {
    description: "[BDC] Datasphere BDC Content Onboarding",
    defaultOn: true,
    version: "2024.22",
    reqId: "DS00-3017",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_BDC_MULTI_IA_INSTALLATIONS: {
    description: "[BDC]BDC Cockpit's support for on-boarding IA content several times from different sources",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3643",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_PACKAGE_CONTENT_DEV_SUPPORT: {
    description: "[BDC Post GA]  support Content dev git repo for BDC package",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5167",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_PARTIAL_INSTALLATION: {
    description: "[BDC] Cockpit should offer a partial install of BDC Data packages",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-3740",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_SYSTEM_API: {
    description: "[BDC]Provide API for getting Systems involved in BDC formations",
    defaultOn: true,
    version: "2024.19",
    reqId: "DS00-3149",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_BDC_STORAGE_API: {
    description: "[BDC]Provide catalog API for BDC cockpit storage",
    defaultOn: true,
    version: "2024.19",
    reqId: "DS00-3131",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_BDC_REPOSITORY_DP_INTENT: {
    description: "[BDC] Data product intent transport for Insight App ",
    defaultOn: true,
    version: "2025.06",
    reqId: "DS00-3816",
    depFeature: ["DWCO_BDC", "DWCO_BDC_DP_INGESTION_SPACE"],
  },
  DWCO_BDC_REPOSITORY_DP_INTENT_CUSTOMER: {
    description: "[BDC] Data product intent transport for Customer content ",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3979",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_REPOSITORY_EXTEND_PROPAGATION: {
    description: "Repository Core Support of CSN Extensions - Propagation",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5091",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_REPOSITORY_MULTI_INSTANCE: {
    description: "[BDC] Onboarding DSP Insight Apps content several times for different sources",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3514",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_REPOSITORY_TRANSPORT_DATA: {
    description: "[HCM] Static dimension in Datasphere ",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5194",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_S4HANACPE: {
    description: "[BDC] Cockpit Support S4 HANA Public Cloud Edition data packages and insight apps",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5195",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BDC_SPACE_COPY: {
    description: "[BDC] BDC Space Copy",
    defaultOn: true,
    version: "2024.24",
    reqId: "DS00-3021",
    depFeature: [],
  },
  DWCO_BDC_SPACE_PROTECTION: {
    description: "[BDC] BDC Space Protection",
    defaultOn: true,
    version: "2024.21",
    reqId: "DS00-3019",
    depFeature: ["DWCO_BDC", "DWCO_SPACE_CAPABILITIES"],
  },
  DWCO_BDC_UCL_DATA_PRODUCT_IMPACT: {
    description: "[BDC]Catalog Impact Analysis of LoB Data Products",
    defaultOn: true,
    version: "2025.10",
    reqId: "DS00-890",
    depFeature: ["DWCO_BDC_DP_INGESTION_SPACE"],
  },
  DWCO_LS_BDC_UCL_SUPPORT_ROUTE_CUSTOM_TEMPLATE: {
    description: "Enhance the CIC Support Route for Onboarding UCL Application Template To Accept Entire Template",
    defaultOn: true,
    version: "2025.06",
    reqId: "DS00-4700",
    landscape: true,
  },
  DWCO_BDC_UCL_TENANT_MAPPING_IBP: {
    description: "[BDC] UCL Tenant Mapping for IBP Integration with DSP",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4811",
    depFeature: ["DWCO_UCL_TENANT_MAPPING_BDC_TENANT", "DWCO_BDC_GA"],
  },
  DWCO_BDC_UCL_TENANT_MAPPING_SAC: {
    description: "UCL Tenant Mapping for SAP Datasphere/Business Data Cloud",
    defaultOn: true,
    version: "2024.21",
    reqId: "DS00-3122",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_BDC_UNISTALLATION_ON_UPDATE: {
    description: "[BDC] BDC Insight app update : Cockpit should offboard removed packages",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4753",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_BILLING_METRICS_DYNATRACE_COLLECTORS: {
    description: "Billing Metrics Collector - Dynatrace Improvements",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5287",
    depFeature: [],
  },
  DWCO_BILLING_METRICS_DYNATRACE_USAGE_REPORT: {
    description: "Billing Metrics Report - Dynatrace Improvements",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5016",
    depFeature: [],
  },
  DWCO_BRIDGE_USAGE_CHECK: {
    description: "BW bridge: Remote Table Usage Check during change of a DataStore/InfoObject/CompositeProvider",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-6695",
  },
  DWCO_CHECK_STORAGE_IMPROVEMENTS: {
    description: "Improve Space and Analysis User Locking in Customer HC",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-3060",
    depFeature: [],
  },
  DWCO_CIRCUIT_BREAKER_YELLOW_STATE: {
    description: "Circuit Breaker - Reflect Yellow State in UI Model",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4098",
    depFeature: [],
  },
  DWCO_CLI_CREATE_DELETE_DATABASEUSER: {
    description: "CLI: separate command for DB User create/delete",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1680",
    depFeature: [],
  },
  DWCO_BUILD_WZ_INTEGRATION: {
    description: "Integration with Build Work Zone",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3692",
    depFeature: [],
  },
  DWCO_BWBRIDGE_RUNTIME_SIZES: {
    description: "ACUs mapped according to the size of bw bridge instance",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4106",
    depFeature: [],
  },
  DWCO_BWPCE_SPACES: {
    description: "[BW PCE]: Space Capabilities and Creation",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-4447",
    depFeature: ["DWCO_LARGE_SYSTEMS", "DWCO_BDC_GA"],
  },
  DWCO_CALM_INTEGRATION_AUTOMATION_MONITORING: {
    description: "Integrate SAP Datasphere into SAP Cloud ALM for Job & Automation Monitoring",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3081",
    depFeature: [],
  },
  DWCO_CALM_INTEGRATION_HEALTH_MONITORING: {
    description: "Integrate SAP Datasphere into SAP Cloud ALM for Health Monitoring",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-2419",
    depFeature: [],
  },
  DWCO_CLI_RESET_DATABASEUSER_PASSWORD: {
    description: "CLI: Reset DB User Password",
    defaultOn: true,
    version: "2023.01",
    reqId: "DW00-8280",
  },
  DWCO_CLI_LIST_SPACES: {
    description: "CLI: List Spaces",
    defaultOn: true,
    version: "2023.01",
    reqId: "DW00-8284",
  },
  DWCO_CLI_SDP: {
    description: "[SDP] Adjust Existing CLI to work for Scoped Roles",
    defaultOn: true,
    version: "2023.23",
    reqId: "DW00-9486",
    depFeature: [],
  },
  DWCO_CONNECTION_ABAP_OAUTH2: {
    description: "Connection types ABAP and S4HANAOP with OAuth2",
    defaultOn: true,
    version: "2025.06",
    reqId: "DS00-3234",
    depFeature: [],
  },
  DWCO_CONNECTION_ABAP_SQL: {
    description: "Support ABAP SQL for connection type ABAP",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-3427",
    depFeature: [],
  },
  DWCO_CONNECTION_ABAP_SQL_X509: {
    description: "Support ABAP SQL with X.509 client certificate authentication",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3604",
    depFeature: [],
  },
  DWCO_CONNECTION_ADL1_UI_REMOVAL: {
    description: "Remove ADL1 support in Connection Management UI",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5136",
    depFeature: [],
  },
  DWCO_CONNECTION_AZURESQL_X509: {
    description: "Support X509 certificates for MS Azure SQL",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1624",
    depFeature: [],
  },
  DWCO_CONNECTION_BUSINESS_DATA_PRODUCT: {
    description: "Connection Type for Business Data Product with Delta Share (BDC FOS, DBX)",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-3231",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_CONNECTION_CDI_JDBC_PROPS: {
    description: "Advanced properties for CDI connection",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-181",
    depFeature: [],
  },
  DWCO_CONNECTION_CLI: {
    description: "Connection Management CLI and API",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-3003",
    depFeature: [],
  },
  DWCO_CONNECTION_CLI_2: {
    description: "Connection Management CLI - Follow-up",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-4808",
    depFeature: [],
  },
  DWCO_CONNECTION_DATA_REPL_FLOW_CAPABILITIES: {
    description: "Connection Management in Data Suite: Enhanced Type Capabilities for data flows and replication flows",
    defaultOn: true,
    version: "2023.07",
    reqId: "DW00-6881",
  },
  DWCO_CONNECTION_DWC_HDLF: {
    description: "LTF: Internal HDLF Delta Share Connection in CCM​",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-1724",
    depFeature: ["DWCO_LARGE_SYSTEMS"],
  },
  DWCO_CONNECTION_EVENT_MESH: {
    description: "Connectivity Data Plane: Enable LOB connections for Ariba, Concur and Fieldglass - via Event Mesh",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-5517",
  },
  DWCO_CONSISTENCY_FRAMEWORK: {
    description: "[Backup & Restore] Inconsistency check framework for DSP",
    defaultOn: true,
    version: "2024.18",
    reqId: "DS00-565",
    depFeature: [],
  },
  DWCO_CONNECTION_HANA_DATA_LAKE: {
    description: "Connection to HANA Data Lake (IQ) Standalone",
    defaultOn: true,
    version: "2021.23",
    reqId: "DW00-4597",
  },
  DWCO_CALM_INTEGRATION_SECURITY_SETTINGS: {
    description: "[SEC-379] Support Central API for Customer Managed Security Settings",
    defaultOn: true,
    version: "2025.05",
    reqId: "DS00-2292",
    depFeature: [],
  },
  DWCO_CAPACITY_CONSUMPTION: {
    description: "Toggles UI and Routes for the Capacity Unit Consumption Dashboard.",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-2681",
    depFeature: [],
  },
  DWCO_CATALOG_BW4HANA_PUSH_SCENARIO: {
    description: "BW Push Scenario - Data Lineage of BW-created Local Tables",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4862",
    depFeature: ["DWCO_CATALOG_BW4HANA_EXTRACTION"],
  },
  DWCO_CATALOG_BWLEGACY_EXTRACTION: {
    description: "Catalog Metadata Extraction of BW Legacy Systems",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2771",
    depFeature: [],
  },
  DWCO_CATALOG_BW_BRIDGE_EXTRACTION2: {
    description: "BW Bridge Improvements",
    defaultOn: true,
    version: "2025.10",
    reqId: "DS00-1341",
    depFeature: ["DWCO_TF_ODC_TASKS"],
  },
  DWCO_CATALOG_BDC_PACKAGES: {
    description: "[BDC Post GA]  storing bdc packages in  BDC cockpit catalog  ",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5040",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_CATALOG_BW4HANA_EXTRACTION: {
    description: "Metadata extraction of SAP BW/4HANA",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2033",
    depFeature: ["DWCO_CATALOG_BW_BRIDGE_EXTRACTION2"],
  },
  DWCO_CATALOG_DB_USER: {
    description: "Independent Catalog Database Access User",
    defaultOn: true,
    version: "2024.23",
    reqId: "DS00-187",
    depFeature: [],
  },
  DWCO_CATALOG_GEN_AI_PHASE_1: {
    description: "Exposing GenAI in Catalog",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-1810",
    depFeature: [],
  },
  DWCO_CATALOG_NEW_HAUM_MEASUREMENT: {
    description: "Add Haum Measurements to Catalog",
    defaultOn: true,
    version: "2024.15",
    reqId: "DS00-2525",
    depFeature: [],
  },
  DWCO_CATALOG_PUBLISH_EXTRACTION: {
    description: "Auto publishing on system extraction",
    defaultOn: true,
    version: "2024.18",
    reqId: "DS00-2151",
    depFeature: [],
  },
  DWCO_CATALOG_SAC_LINEAGE_FOLDERS: {
    description: "Expose nested hierarchy in SAC lineage",
    defaultOn: true,
    version: "2024.15",
    reqId: "DS00-2528",
    depFeature: [],
  },
  DWCO_CATALOG_SAC_TO_BW4HANA_LINEAGE_EXTRACTION: {
    description: "Data Lineage from SAC Story to BW objects",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2645",
    depFeature: ["DWCO_CATALOG_BW4HANA_EXTRACTION"],
  },
  DWCO_CATALOG_SHARED_STORAGE: {
    description: "Sharing Data Catalog Storage Across Customer's Tenants",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-1380",
    depFeature: ["DWCO_CATALOG_DB_USER", "DWCO_BDC_GA"],
  },
  DWCO_CATALOG_SHARED_STORAGE_UI: {
    description: "Shared Catalog UI across Customer Tenants",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-3898",
    depFeature: ["DWCO_CATALOG_SHARED_STORAGE", "DWCO_BDC_GA"],
  },
  DWCO_CATALOG_TERM_AND_KPI_IMPORT: {
    description: "Support KPI and glossary term import",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1338",
    depFeature: [],
  },
  DWCO_CATALOG_WORKLOAD_APIS: {
    description: "Add catalog database user workload class management support",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-3666",
    depFeature: ["DWCO_CATALOG_DB_USER"],
  },
  DWCO_CENTRAL_BW_PCE_SHARING_TO_DBX: {
    description: "[BW PCE Sharing to DBX]  Feature Flag Enablement",
    defaultOn: false,
    version: "2025.11",
    reqId: "DS00-4788",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_CHANGE_MANAGEMENT_SQL_VIEW: {
    description: "Change Management : Improve Validation in SQL View impacted by a change",
    defaultOn: true,
    version: "2022.25",
    reqId: "DW00-4550",
  },
  DWCO_CONNECTION_HDLFILES_X509: {
    description:
      "Connection Management: Add Delta Sharing support to HDLF Connection Type + HANA Remote Source Creation",
    defaultOn: false,
    version: "2024.07",
    reqId: "DW00-10576",
    depFeature: [],
  },
  DWCO_CONNECTION_HDLF_DELTASHARING: {
    description: "Use HANA deltasharing adapter for BUSINESS_DATA_PRODUCT",
    defaultOn: false,
    version: "2025.02",
    reqId: "DS00-3381",
    depFeature: [],
  },
  DWCO_CONNECTION_HDLF_REMOTE_TABLES: {
    description: "Support remote tables for connection type HDLF",
    defaultOn: false,
    version: "2024.22",
    reqId: "DS00-2640",
    depFeature: [],
  },
  DWCO_CONNECTION_HDLDB_X509: {
    description: "Support X.509 authentication for HDLDB",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1216",
    depFeature: [],
  },
  DWCO_CONNECTION_METRICS: {
    description: "Usage Measurement for Datasphere Connection Management",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-2277",
    depFeature: [],
  },
  DWCO_CONNECTION_MSSQL_AS_RF_SOURCE: {
    description: "Enable connection type MSSQL as replication flow source",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-3464",
    depFeature: [],
  },
  DWCO_CONNECTION_ONELAKE: {
    description: "Connection type Microsoft OneLake",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4132",
    depFeature: [],
  },
  DWCO_CONNECTION_PARTNER_TEST: {
    description: "Partner Test Connection",
    defaultOn: false,
    version: "2021.16",
    reqId: "DW00-3615",
    isBizToggle: true,
  },
  DWCO_CONNECTION_RENOVATE_SFTP: {
    description: "Renovate Connection Type Generic SFTP to new UI framework",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-3491",
    depFeature: [],
  },
  DWCO_CONNECTION_RUCKSACK_ABAP: {
    description: "Toggle Rucksack feature for SAP ABAP",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3562",
    depFeature: [],
  },
  DWCO_CONNECTION_RUCKSACK_S4CLOUD: {
    description: "Toggle Rucksack feature for S/4 HANA Cloud",
    defaultOn: true,
    version: "2025.12",
    reqId: "DS00-3498",
    depFeature: [],
  },
  DWCO_CONNECTION_HTTP: {
    description: "Connection type HTTP",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1617",
    depFeature: [],
  },
  DWCO_CONNECTION_SCC_CONFLUENT: {
    description: "Support SAP Cloud Connector for Confluent Cloud in Connection Management",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4975",
    depFeature: [],
  },
  DWCO_CONNECTION_SCC_GBQ: {
    description: "Support SAP Cloud Connector for Google Big Query in Connection Management",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4974",
    depFeature: [],
  },
  DWCO_CONNECTION_SCC_OBJECTSTORES: {
    description: "Support SAP Cloud Connector for Hyperscaler Object Stores and Azure SQL",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4973",
    depFeature: [],
  },
  DWCO_CONNECTION_SFTP_SOURCE: {
    description: "Connection type SFTP as replication flow source",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4969",
    depFeature: [],
  },
  DWCO_CONNECTION_SFTP_TARGET: {
    description: "Connection type SFTP as replication flow target",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-4203",
    depFeature: [],
  },
  DWCO_CONNECTION_SIGNAVIO: {
    description: "Connection type SIGNAVIO",
    defaultOn: false,
    version: "2025.02",
    reqId: "DS00-2751",
    depFeature: [],
  },
  DWCO_CONNECTION_SIGNAVIO_MAPPING: {
    description: "Signavio Connection Tenant Mapping (GA)",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2911",
    depFeature: [],
  },
  DWCO_CONNECTION_SUCCESSFACTORS_SERVER_PAGINATION: {
    description: "SAP SuccessFactors connection type: use server-side pagination in deployed virtual tables",
    defaultOn: true,
    version: "2021.19",
    reqId: "DW00-4510",
  },
  DWCO_CONNECTION_WASB: {
    description: "Connection Creation for WASB - Microsoft Windows Azure Storage Blobs",
    defaultOn: true,
    version: "2022.01",
    reqId: "DW00-5032",
  },
  DWCO_CONSUMPTION_API_NEW_ROUTES: {
    description: "[Consumption API] New API Routes",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-871",
    depFeature: [],
  },
  DWCO_CONSUMPTION_API_TUP_SUPPORT: {
    description: "DW15 Public API Endpoint - TUP OAuth Clients adoption",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5256",
    depFeature: ["DWCO_API_TUP_SUPPORT"],
  },
  DWCO_CUSTOMER_HANA_WM_LIMITS: {
    description: "Workload Management: Restrictions on unlimit or high value settings",
    defaultOn: true,
    version: "2023.04",
    reqId: "DW00-8266",
    depFeature: [],
  },
  DWCO_DAC_USER_IMPERSONATION: {
    description: "DAC User Impersonation in Relational Preview",
    defaultOn: true,
    version: "2024.15",
    reqId: "DS00-651",
    depFeature: [],
  },
  DWCO_DB_VERSION_CHECKER_CACHE: {
    description: "DB Version Checker - Tenant Upgrade without Downtime",
    defaultOn: true,
    version: "2023.24",
    reqId: "DW00-8408",
    depFeature: [],
  },
  DWCO_DEEPSEA_INDEX_METRICS: {
    description: "Dynatrace missing metrics for Deepsea",
    defaultOn: true,
    version: "2024.02",
    reqId: "DW00-11143",
    depFeature: [],
  },
  DWCO_DEEPSEA_PROFILE_SUPPORT_ROUTES: {
    description: "Add profiling support to repository",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-11174",
    depFeature: [],
  },
  DWCO_DEFAULT_VALUE_IP: {
    description: "Default Value for Input Parameters",
    defaultOn: true,
    version: "2022.04",
    reqId: "DW00-5303",
  },
  DWCO_DATA_MARKETPLACE_CONTAINER_BETA: {
    description: "[Marketplace] Additional HDI Containers for Beta Delivery incl. Support & Demo Cases",
    defaultOn: false,
    version: "2022.02",
    reqId: "DW00-5242",
    isBizToggle: true,
  },
  DWCO_DATA_MARKETPLACE_CONTAINER_DEMO: {
    description: "[Marketplace] Additional HDI Containers for Beta Delivery incl. Support & Demo Cases",
    defaultOn: false,
    version: "2022.02",
    reqId: "DW00-5242",
    isBizToggle: true,
  },
  DWCO_DAC_DCPP_IMPROV: {
    description: "DAC - Condition provider enhancements ",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-3570",
    depFeature: [],
  },
  DWCO_DAC_DEPLOYMENT_STATUS: {
    description: "DAC editor improvement - Last deployed status section",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-3652",
    depFeature: [],
  },
  DWCO_DAC_HAUM_REPO: {
    description: "DAC Repository Based Usage Tracking",
    defaultOn: true,
    version: "2024.05",
    reqId: "DW00-10796",
    depFeature: [],
  },
  DWCO_DAC_SAVE_FOLDER_SUPPORT: {
    description: "DAC Support to offer Save/ Save as options for folder support",
    defaultOn: true,
    version: "2024.01",
    reqId: "DW00-11077",
    depFeature: [],
  },
  DWCO_DAC_SF_STATIC_TO_DYNAMIC: {
    description: "Move static DACs to use dynamic structured filters",
    defaultOn: true,
    version: "2025.05",
    reqId: "DS00-2712",
    depFeature: ["DWCO_DAC_DCPP_IMPROV"],
  },
  DWCO_DAC_SOURCE_LINK: {
    description: "Data Access Control - Access source entities from DAC editor",
    defaultOn: true,
    version: "2024.22",
    reqId: "DS00-2673",
    depFeature: [],
  },
  DWCO_DAC_SUPPORT_CALC_VIEW: {
    description: "DAC Row-Level Security support for Calc. View with Structured Filters",
    defaultOn: true,
    version: "2024.20",
    reqId: "DS00-1141",
    depFeature: [],
  },
  DWCO_DAC_SUPPORT_HIERARCHY_DIRECTORY: {
    description: "DAC support for Hierarchy with directory",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1710",
    depFeature: ["DWCO_MODELING_EXTERNAL_HIERARCHY_SUPPORT_COMPOUNDING"],
  },
  DWCO_DAC_SUPPORT_VALIDATION_USERS: {
    description: "DAC support for validation user",
    defaultOn: true,
    version: "2024.20",
    reqId: "DS00-938",
    depFeature: [],
  },
  DWCO_DAC_VALUES_STRUCTURED_FILTER: {
    description: "DAC Single values type update to use Structured Filter",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-1586",
    depFeature: [],
  },
  DWCO_DATA_MARKETPLACE_CONTAINER_SUPPORT: {
    description: "[Marketplace] Additional HDI Containers for Beta Delivery incl. Support & Demo Cases",
    defaultOn: false,
    version: "2022.02",
    reqId: "DW00-5242",
    isBizToggle: true,
  },
  DWCO_DIS_DATAFLOW_JSON_EDITOR: {
    description: "JSON Pipeline Editor for Data Plane",
    defaultOn: true,
    version: "2021.14",
    reqId: "DM00-2491",
    depFeature: ["DWCO_INFRA_DATA_PLANE"],
  },
  DWCO_DI_IP_IMPROVE: {
    description: "_Allow improvement of IP allowlisting process for new tenants_",
    defaultOn: true,
    version: "2024.18",
    reqId: "DW00-10910",
    depFeature: [],
  },
  DWCO_DI_MONITOR_UI_IMPROVEMENTS: {
    description: "Data Integration Monitoring - UI Improvements",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1802",
    depFeature: [],
  },
  DWCO_DL_SOURCE_OBJECT: {
    description: "Terminology Harmonization for Source Object",
    defaultOn: true,
    version: "2025.06",
    reqId: "DS00-280",
    depFeature: [],
  },
  DWCO_DM_SCROLL_EXPERIENCE: {
    description: "Data Maintenance - Improving the data loading and scrolling experience of Data Preview",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-9119",
    depFeature: [],
  },
  DWCO_DPS_DATAFLOW_AWSS3_TARGET: {
    description: "Datasuite | Support Non-DWC Targets | Object stores of hyper scalers - Amazon S3 (DPS)",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-8270",
  },
  DWCO_DPS_REPLICATION_FLOW_CONVERTER: {
    description: "Replication Flow converter for DPS Event Mesh",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-7527",
  },
  DWCO_DPS_REPLICATION_FLOW_UI: {
    description: "Replication Flow - Data Layer Landing page Version 1",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-7664",
  },
  DWCO_DS005069_MARKETPLACE_TUP_SUPPORT: {
    description: "Data Marketplace-Specific Feature Flag for TUP Adoption",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-5069",
    depFeature: ["DWCO_API_TUP_SUPPORT"],
  },
  DWCO_DSP_UCL_DATAPRODUCT_DELETION: {
    description: "Enable deletion of DSP UCL Data Products",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5196",
    depFeature: ["DWCO_ODC_DELTASHARE_DP_SHARE"],
  },
  DWCO_DS_CATALOG_METERING_REPORTING: {
    description: "[FTC] Datasphere: Include ODC Storage to metering and reporting",
    defaultOn: true,
    version: "2023.19",
    reqId: "DW00-9111",
    depFeature: [],
  },
  DWCO_DS_DATAFLOW_AWSS3_TARGET: {
    description: "Datasuite | Support Non-DWC Targets | Object stores of hyper scalers - Amazon S3",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-7364",
  },
  DWCO_DS_DATAFLOW_AZUREDL_TARGET: {
    description: "Datasuite | Support Non-DWC Targets | Object stores of hyper scalers - AzureDataLake",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-7368",
  },
  DWCO_DS_DATAFLOW_GCS_TARGET: {
    description: "Datasuite | Support Non-DWC Targets | Object stores of hyper scalers - Google Cloud storage",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-7372",
  },
  DWCO_DS_DATAFLOW_HANA_TARGET: {
    description: "Datasuite | Support Non-DWC Targets | HANA",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-7298",
  },
  DWCO_DS_REPLICATION_FLOW_ALLOW_SAME_SOURCE: {
    description: "Allow to re-use same source objects in multiple replication flow",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-4421",
    depFeature: [],
  },
  DWCO_DS_REPLICATION_FLOW_CONFLUENT_AS_A_SOURCE: {
    description: "Confluent Kafka as a source in Replication Flow",
    defaultOn: true,
    version: "2024.23",
    reqId: "DS00-1396",
    depFeature: [],
  },
  DWCO_DS_REPLICATION_FLOW_CONFLUENT_AS_A_SOURCE_EXTENDED: {
    description: "Confluent Kafka as a source - Enhancements(Expand Array and configure opcode, PK)",
    defaultOn: true,
    version: "2025.10",
    reqId: "DS00-2706",
    depFeature: [],
  },
  DWCO_DS_REPLICATION_FLOW_DELTA_ONLY_LOAD_TYPE: {
    description: "Support Delta load type in Datasphere Replication Flow UI",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3657",
    depFeature: [],
  },
  DWCO_DS_REPLICATION_FLOW_EMAIL_NOTIFICATION: {
    description: "Email notification when replication object fails ",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4266",
    depFeature: [],
  },
  DWCO_DS_REPLICATION_FLOW_ENHANCEMENT_ABAP_TYPE_CLEAN_DEPLOY: {
    description: "Replication Flow Enhancements - Configure ABAP Type & clean deployment improvements",
    defaultOn: true,
    version: "2025.04",
    reqId: "DS00-3069",
    depFeature: [],
  },
  DWCO_DS_REPLICATION_FLOW_ERROR_MESSAGES_ENHANCEMENTS: {
    description: "Replication Flow monitoring error message improvements - Display additional errors and retry info",
    defaultOn: true,
    version: "2024.21",
    reqId: "DS00-2709",
    depFeature: [],
  },
  DWCO_DELETE_DATA_LOCAL_HANA_TABLE: {
    description: "Local Table  - Deletion of Data from Delta enabled HANA Table ",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-3901",
    depFeature: [],
  },
  DWCO_DF_ABAP_PREVIEW: {
    description: "Dataflows- ABAP Sources Data Preview",
    defaultOn: true,
    version: "2024.04",
    reqId: "DW00-10968",
    depFeature: [],
  },
  DWCO_DF_CON_DT_METRICS: {
    description: "Dynatrace Metrics for Dataflow connectivity usage",
    defaultOn: true,
    version: "2025.06",
    reqId: "DS00-4137",
    depFeature: [],
  },
  DWCO_DF_OC_FIL_PD: {
    description: "Support Filter pushdown for Open Connectors in Dataflow",
    defaultOn: true,
    version: "2024.09",
    reqId: "DS00-1206",
    depFeature: [],
  },
  DWCO_DF_PYVERSION_COMPL: {
    description: "DataFlow: Python compliance (UI changes)",
    defaultOn: true,
    version: "2025.12",
    reqId: "DS00-4639",
    depFeature: [],
  },
  DWCO_DF_WRAPPER_RESTART: {
    description: "Auto restart of data flow",
    defaultOn: true,
    version: "2021.23",
    reqId: "DW01-3877",
  },
  DWCO_DS_REPLICATION_FLOW_HANA_SQL_VIEW_AS_SOURCE: {
    description: "RF UI: Support Hana SQL views as a source",
    defaultOn: true,
    version: "2025.12",
    reqId: "DS00-3072",
    depFeature: [],
  },
  DWCO_DS_REPLICATION_FLOW_MSSQL_SOURCE_WITH_CFW: {
    description: "Support Microsoft SQL Server as source  in Replication Flow UI",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-2055",
    depFeature: [],
  },
  DWCO_DS_REPLICATION_FLOW_MS_ONELAKE_AS_TARGET: {
    description: "Support MS OneLake as a target in Replication Flow UI",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4224",
    depFeature: [],
  },
  DWCO_DS_REPLICATION_FLOW_SFTP_SOURCE: {
    description: "Support SFTP as a source in Replication Flow UI",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5080",
    depFeature: [],
  },
  DWCO_DS_REPLICATION_FLOW_SFTP_TARGET: {
    description: "Support SFTP as a target in replication flow UI",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-4227",
    depFeature: [],
  },
  DWCO_DS_REPLICATION_FLOW_SIGNAVIO_TARGET: {
    description: "RF UI: Support Signavio as a target in Replication Flow ",
    defaultOn: false,
    version: "2025.02",
    reqId: "DS00-2982",
    depFeature: [],
  },
  DWCO_DS_REPLICATION_FLOW_SUPPORT_HDLF_SOURCE: {
    description: "Support HDLF as source in replication flow UI",
    defaultOn: true,
    version: "2024.09",
    reqId: "DW00-11352",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_DS_REPLICATION_FLOW_SUPPORT_LARGE_PARQUET_FILE: {
    description: "Support large parquet file during initial load in Replication Flow UI",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5026",
    depFeature: [],
  },
  DWCO_DS_REPLICATION_FLOW_SUPPORT_RUN_TIME_PARAMS: {
    description: "Support max connection thread and delta interval property in Replication Flow Run time",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4335",
    depFeature: [],
  },
  DWCO_DS_REPLICATION_FLOW_UI_HAUM_CLICK_TRACKING: {
    description: "Replication Flow: HAUM usage tracking for executed replication flow(click based)",
    defaultOn: true,
    version: "2023.10",
    reqId: "DW00-7799",
    depFeature: [],
  },
  DWCO_DW009374_DATA_PRODUCT_POC: {
    description: "Data Product PoC",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-9374",
    depFeature: [],
  },
  DWCO_DWC_MA_ROUTER_DS00_678_RATE_LIMITER: {
    description: "Add rate limiter to BW Bridge Approuter",
    defaultOn: true,
    version: "2024.14",
    reqId: "DS00-678",
    depFeature: [],
  },
  DWCO_DYNATRACE_REPORT_CUS: {
    description: "Report billing unexpected behavior on dynatrace",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-11734",
    depFeature: [],
  },
  DWCO_ENDPOINTS_WITH_OPENAPI_DOCS: {
    description: "Data Maintenance - Enable Preview for Cross Space Sharing with Input Parameter",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-805",
    depFeature: [],
  },
  DWCO_ECN_ADVISOR_CONFIGURATION: {
    description: "[Elasticity] ECN Advisor Configuration for MDS Load",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-215",
    depFeature: [],
  },
  DWCO_ECN_NON_LOCAL_TABLE_REPLICA: {
    description: "[Elasticity] HDI and OpenSQL Schemas Table Replication",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-3746",
    depFeature: [],
  },
  DWCO_ELASTICITY_ECN: {
    description: "[Elasticity]: New Task Types for ECN Management",
    defaultOn: true,
    version: "2024.13",
    reqId: "DW00-8365",
  },
  DWCO_ELASTICITY_ECN_METERING: {
    description: "[Elasticity]: ECN Metering Foundation",
    defaultOn: true,
    version: "2024.13",
    reqId: "DW00-9622",
    depFeature: ["DWCO_ELASTICITY_ECN"],
  },
  DWCO_ENTERPRISE_SEARCH_FOR_DEPLOYED_OBJ: {
    description: "Provide Search API for SAC to search for Deployed Objects",
    defaultOn: true,
    version: "2024.03",
    reqId: "DW00-9991",
  },
  DWCO_FTC_BW_BRIDGE_NEW_SIZES: {
    description: "[BW bridge] Enable additional BW bridge Storage Sizes",
    defaultOn: false,
    version: "2025.03",
    reqId: "DS00-1805",
    depFeature: [],
  },
  DWCO_FLEXIBLE_RATIO_8: {
    description: "Flexible Initial Tenant Configuration",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-2822",
  },
  DWCO_ETV_LTF_PERF_RES: {
    description: "Minimize HANA compute for Data Preview use cases in LSA-only spaces",
    defaultOn: true,
    version: "2025.10",
    reqId: "DS00-4750",
    depFeature: [],
  },
  DWCO_ETV_LTF_SPARK_DATA_FETCH: {
    description: "Support optimized filtering for Find & Replace on LTF artifacts",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5066",
    depFeature: [],
  },
  DWCO_FLEXIBLE_RATIO_10: {
    description: "Flexible Initial Tenant Configuration",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-2822",
  },
  DWCO_GEO_DATA_CLEANSING_ON_ERROR: {
    description: "Geo Calculated Column - Cleansing Checkbox for Impossible Values",
    defaultOn: true,
    version: "2023.07",
    reqId: "DW00-6741",
  },
  DWCO_FTC_HANA_MULTI_AZ: {
    description: "[Flexible Tenant Configuration] Enable HANA Multi-AZ through FTC",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3387",
    depFeature: [],
  },
  DWCO_FTC_MEMORY_DOWNSIZING: {
    description: "_Enable Downsizing of Memory on FTC_",
    defaultOn: true,
    version: "2024.15",
    reqId: "DW00-7837",
    depFeature: [],
  },
  DWCO_FTC_PREMIUM_OUTBOUND: {
    description: "[FTC] Datasphere:  Setting licenses for Premium Outbound Integration and reporting to MaaS",
    defaultOn: true,
    version: "2023.24",
    reqId: "DW00-10012",
    depFeature: [],
  },
  DWCO_FTC_PREMIUM_OUTBOUND_CARD: {
    description: "[Flexible Tenant Configuration] Consumption information for Premium Outbound Integration",
    defaultOn: true,
    version: "2023.24",
    reqId: "DW00-10914",
    depFeature: ["DWCO_FTC_PREMIUM_OUTBOUND"],
  },
  DWCO_FTC_UPSIZING_BW_BRIDGE: {
    description: "Enable upsizing of BW Bridge",
    defaultOn: true,
    version: "2025.04",
    reqId: "DS00-4080",
    depFeature: [],
  },
  DWCO_GDPR_ENTERPRISE_SEARCH: {
    description: "GDPR Handling of tracked Data in DWC in Context of Search",
    defaultOn: true,
    version: "2022.18",
    reqId: "DW00-6479",
  },
  DWCO_GRAPH_ONTOLOGY_EDITOR: {
    description: "Conceptual Model: Basic Editor",
    defaultOn: false,
    version: "2024.13",
    reqId: "DW00-9376",
    depFeature: [],
  },
  DWCO_HANA_TOOLING: {
    description: "Adjust URLs for SAP HANA Cloud tools",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-2728",
    depFeature: [],
  },
  DWCO_HARMONIZATION_OBJECT_SELECTION: {
    description: "Harmonization of object selection behavior within Data Layer",
    defaultOn: true,
    version: "2024.07",
    reqId: "DW00-10264",
    depFeature: [],
  },
  DWCO_GRAPH_ONTOLOGY_EDITOR_DETACHEDPROPS: {
    description: "Support detached properties",
    defaultOn: false,
    version: "2024.24",
    reqId: "DS00-1181",
    depFeature: ["DWCO_GRAPH_ONTOLOGY_EDITOR"],
  },
  DWCO_GRAPH_REPO_EXPLORER: {
    description: "Integrate ontology data with Deepsea repository",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-2817",
    depFeature: ["DWCO_GRAPH_ONTOLOGY_EDITOR"],
  },
  DWCO_HANA_LAKEHOUSE_PLUGIN: {
    description: "[LSA-After GA]Remove HANA Plugin logic implemented via DSP Provisioning",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3597",
    depFeature: [],
  },
  DWCO_HANA_PATCH_UPGRADE: {
    description: "Enable the customers to do a HANA upgrade for their customer database",
    defaultOn: true,
    version: "2024.21",
    reqId: "DS00-296",
    depFeature: [],
  },
  DWCO_INTERNAL_LIGHTWEIGHT_DSP: {
    description: "Datasphere without Customer HANA. Datasphere connected to centrally shared Multi-tenanted HANA",
    defaultOn: false,
    version: "2024.21",
    reqId: "DS00-2800",
    depFeature: [],
  },
  DWCO_IL_RT_STORAGE_OPTIMIZATION: {
    description: "Deleting obsolete records from internal table",
    defaultOn: true,
    version: "2024.18",
    reqId: "DS00-377",
    depFeature: [],
  },
  DWCO_IL_HAUM_REPO_TRACKING: {
    description: "HAUM repository tracking for Intelligent Lookup - Milestone 1",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-6451",
  },
  DWCO_HCM_ENTITLEMENT: {
    description: "[BDC] Cockpit Support HCM data packages and insight apps",
    defaultOn: true,
    version: "2025.12",
    reqId: "DS00-4912",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_HCM_PACKAGES: {
    description: "[BDC] Cockpit Support HCM intelligent apps",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5400",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_HDLF_SPACE_CAPABILITY: {
    description: "[LSA]: Space Capability 'Large System'",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-3049",
    depFeature: ["DWCO_LARGE_SYSTEMS"],
  },
  DWCO_HDLF_SPACE_IMPORT_TRANSPORT: {
    description: "Gap Closing HDLF Spaces: transporting and importing",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4874",
    depFeature: [],
  },
  DWCO_HORIZON_THEME: {
    description: "Adoption for SAP Horizon Theme",
    defaultOn: true,
    version: "2025.05",
    reqId: "DS00-2389",
    depFeature: [],
  },
  DWCO_IA_INSTALLATION_CATALOG_DATAPRODUCT_STATUS_CHECK: {
    description: "Data products should be active in Catalog for IA Datasphere content onboarding ",
    defaultOn: true,
    version: "2025.12",
    reqId: "DS00-5284",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_IL_HANDLE_MISSING_LOOKUP_RECORDS: {
    description: "Intelligent Lookup - Handle missing lookup records in workarea",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-9183",
  },
  DWCO_IL_LOCK_RELEASE: {
    description: "Handling node.js server failure during execution",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-4900",
  },
  DWCO_IMPORT_EXPORT_IP_ALLOWLIST_FILES: {
    description: "Enable import and export from ip allowlist",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-979",
    depFeature: [],
  },
  DWCO_IMPORT_METADATA_APE: {
    description: "Import Metadata: Using APE for S/4HANA Cloud connection with current Communication Scenario",
    defaultOn: true,
    version: "2024.09",
    reqId: "DW00-9836",
    depFeature: [],
  },
  DWCO_IMPORT_METADATA_APE_ONPREMISE: {
    description: "Import Meta Data Wizard - Using APE for S/4HANA on Premise connections",
    defaultOn: true,
    version: "2024.13",
    reqId: "DW00-11161",
    depFeature: [],
  },
  DWCO_IMPORT_METADATA_CSN_EXPOSURE_SERVICE: {
    description: "Adapt CSN_EXPOSURE to the new service strategy of S/4 HANA cloud",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1798",
    depFeature: [],
  },
  DWCO_INA_METRICS: {
    description: "Improved Metrics for InA Operations",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4139",
    depFeature: [],
  },
  DWCO_INFRA_DATA_PLANE: {
    description: "Master flag for data plane mode",
    defaultOn: false,
    version: "2022.16",
    reqId: "DW00-3704",
    isBizToggle: true,
  },
  DWCO_INFRA_DATA_PLANE_PROTECT_SPACES: {
    description: "Data Plane: Customer/SAP Space UI should be read-only except member list",
    defaultOn: true,
    version: "2022.12",
    reqId: "DW00-6486",
    depFeature: ["DWCO_INFRA_DATA_PLANE"],
  },
  DWCO_INFRA_DATA_PLANE_ACN_CONSTRAINTS: {
    description: "Data Plane customer constraints : ACN Export & Import",
    defaultOn: true,
    version: "2022.12",
    reqId: "DW00-6435",
    depFeature: ["DWCO_INFRA_DATA_SUITE_PROVISIONING"],
  },
  DWCO_INFRA_DATA_PLANE_ACTIVATION_OPTION: {
    description: "Data Integration Monitor : Data Plane constraint for SAP Space",
    defaultOn: true,
    version: "2022.12",
    reqId: "DW00-6443",
    depFeature: ["DWCO_INFRA_DATA_PLANE"],
  },
  DWCO_INFRA_DATA_PLANE_DAC_TYPE_DCL: {
    description: "Computing Effective DAC Type for DPS",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-6906",
    depFeature: ["DWCO_INFRA_DATA_PLANE"],
  },
  DWCO_INFRA_DATA_PLANE_SPACES_AUDIT: {
    description: "DPS2208: Audit Logging available in Customer Space UI",
    defaultOn: true,
    version: "2022.14",
    reqId: "DW00-7000",
    depFeature: ["DWCO_INFRA_DATA_PLANE"],
  },
  DWCO_INFRA_DATA_SUITE_PROVISIONING: {
    description: "_Enables provisioning of Data Plane content in Data Suite_",
    defaultOn: true,
    version: "2023.21",
    reqId: "DW00-9070",
    depFeature: [],
  },
  DWCO_INFRA_EXPENSIVE_STATEMENTS_TRACE_ENABLEMENT: {
    description: "Activate expensive statements trace",
    defaultOn: true,
    version: "2021.13",
    reqId: "DW00-3917",
  },
  DWCO_INFRA_PREP_SDPCONVERSION: {
    description: "[SDP] Conversion: Preparation FF to allow Landscape dependent SDP-Conversion",
    defaultOn: true,
    version: "2023.21",
    reqId: "DW00-10000",
    depFeature: [],
  },
  DWCO_INFRA_PROTECT_SAP_CONTENT: {
    description: "Data Plane: SAP Delivered Content Protection",
    defaultOn: true,
    version: "2022.12",
    reqId: "DW00-6379",
    depFeature: ["DWCO_INFRA_DATA_SUITE_PROVISIONING"],
  },
  DWCO_INFRA_REPOSITORY_ACCESS_REASON: {
    description: "Repository Object Access Restriction Reasons",
    defaultOn: true,
    version: "2024.09",
    reqId: "DW00-10885",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_CLI_READ: {
    description: "Automatic CLI CRUD Integration - Read",
    defaultOn: true,
    version: "2024.03",
    reqId: "DW00-9046",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_CLI_WRITE: {
    description: "Automatic CLI CRUD Integration - Write",
    defaultOn: true,
    version: "2024.09",
    reqId: "DW00-9919",
    depFeature: ["DWCO_INFRA_REPOSITORY_CLI_READ"],
  },
  DWCO_INFRA_REPOSITORY_CSN_INTEROP: {
    description: "Deepsea - compute and expose CSN Interop for a service",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-4368",
    depFeature: ["DWCO_INFRA_REPOSITORY_SERVICE_DEFINITION"],
  },
  DWCO_INFRA_REPOSITORY_DAEMON: {
    description: "Process Long Requests with Daemon service",
    defaultOn: true,
    version: "2023.21",
    reqId: "DW00-6676",
  },
  DWCO_INFRA_REPOSITORY_LOGICAL_DELETE: {
    description: "Logical Deletion of Space: Introduce Space Property",
    defaultOn: true,
    version: "2024.21",
    reqId: "DS00-1615",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_REUSE_USAGE_TRACKING: {
    description: "Use new MO Calculation framework for Repository",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-2533",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_SHARE_OPTIMIZATION: {
    description: "Introduce new Feature Flag DWCO_INFRA_REPOSITORY_SHARE_OPTIMIZATION",
    defaultOn: false,
    version: "2023.23",
    reqId: "DW01-12638",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_SHARE_REFACTORING: {
    description: "Repository Cross Space Sharing Refactoring",
    defaultOn: true,
    version: "2024.21",
    reqId: "DS00-1527",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_SMART_RATE_LIMITING: {
    description: "Improve Repository Service Rate Limiting",
    defaultOn: true,
    version: "2023.12",
    reqId: "DW00-6265",
  },
  DWCO_INFRA_REPOSITORY_DEEPSEA_CONNECTION: {
    description: "Deepsea migration - Connection",
    defaultOn: true,
    version: "2024.13",
    reqId: "DW00-10350",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_DEEPSEA_DATAFLOW: {
    description: "Deepsea migration - Dataflow",
    defaultOn: true,
    version: "2023.23",
    reqId: "DW00-10528",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_DEEPSEA_OTHERS: {
    description: "Deepsea migration - Remove remaining DB calls from dwaas-core",
    defaultOn: true,
    version: "2024.18",
    reqId: "DS00-594",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_DEEPSEA_SPACE: {
    description: "Deepsea migration - Space",
    defaultOn: true,
    version: "2024.09",
    reqId: "DW00-10423",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_DANGLING_UNION_SOURCES: {
    description: "Data plane: Dangling Sources in Union Views",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-5194",
  },
  DWCO_INFRA_REPOSITORY_EXTENSION_CORE: {
    description: "Repository Core Support of CSN Extensions",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-4880",
  },
  DWCO_INFRA_REPOSITORY_GIT_POC: {
    description: "[POC] Github support for Deepsea",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4587",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_HIDDEN_PERSPECTIVE: {
    description: "Support @DataWarehouse.tooling.hidden annotation",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-4355",
  },
  DWCO_INFRA_REPOSITORY_INTERNAL_ESEARCH: {
    description: "Optimize Hana Repository Memory with ESearch",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-1766",
  },
  DWCO_INFRA_REPOSITORY_I18N_CORE: {
    description: "Repository to Manage LoB Multi-lingual import and deployment",
    defaultOn: true,
    version: "2022.11",
    reqId: "DW00-1164",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_I18N_CHANGE_LANGUAGE: {
    description: "I18N: Allow changing space source language",
    defaultOn: true,
    version: "2024.19",
    reqId: "DS00-1612",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_I18N_HAUM: {
    description: "I18N support for metadata - HAUM support",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-261",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_MULTI_DB: {
    description: "Multiple Repository database instance",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-6668",
  },
  DWCO_INFRA_REPOSITORY_PACKAGE_HAUM: {
    description: "Repository Package Usage Tracking",
    defaultOn: true,
    version: "2024.16",
    reqId: "DS00-1023",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_QUERY_DEPD: {
    description: "Repository Dependency Extraction Refactoring",
    defaultOn: true,
    version: "2024.16",
    reqId: "DS00-1641",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_SERVICE_DEFINITION: {
    description: "Deepsea - Introduce service definition concept",
    defaultOn: true,
    version: "2025.05",
    reqId: "DS00-3927",
    depFeature: [],
  },
  DWCO_INFRA_REPOSITORY_SERVICE_UI_CALL: {
    description: "Deepsea - Support More Modeling Routes",
    defaultOn: true,
    version: "2023.11",
    reqId: "DW00-7244",
  },
  DWCO_INFRA_SCHEDULED_ODP_DATA_TRANSFER: {
    description: "[HAUM] Transfer existing repository-based usage data to ODP",
    defaultOn: true,
    version: "2022.08",
    reqId: "DW00-3907",
    depFeature: [],
  },
  DWCO_INFRA_SAP_TENANT: {
    description: "Data Plane: SAP Delivered Content Protection",
    defaultOn: false,
    version: "2022.12",
    reqId: "DW00-6804",
    isBizToggle: true,
  },
  DWCO_INFRA_SDPCONVERSION: {
    description: "[SDP] Space Membership Conversion",
    defaultOn: true,
    version: "2023.21",
    reqId: "DW00-7093",
    depFeature: ["DWCO_INFRA_PREP_SDPCONVERSION"],
  },
  DWCO_INFRA_SDPCONVERSION_RETRY: {
    description: "[SDP] Conversion: Retry Mechanism during Conversion",
    defaultOn: true,
    version: "2023.21",
    reqId: "DW00-10004",
    depFeature: ["DWCO_INFRA_SDPCONVERSION"],
  },
  DWCO_INFRA_SPACE_PERMISSIONS: {
    description: "[SDP] Redesign of endpoint filter, routes & permissions checks",
    defaultOn: true,
    version: "2023.21",
    reqId: "DW00-6558",
  },
  DWCO_INFRA_TASKS_API_TASK: {
    description: "Task Framework: (RESTful) API Task",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1359",
    depFeature: [],
  },
  DWCO_INFRA_TASKS_BW_PROCESS_CHAIN: {
    description: "Task Framework - BW process chain task (Call Bridge Process Chain)",
    defaultOn: true,
    version: "2025.05",
    reqId: "DS00-391",
    depFeature: [],
  },
  DWCO_INFRA_TASKS_CANCEL_REGISTRY: {
    description: "Task Framework - enable cancel task in the registry",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-3210",
    depFeature: [],
  },
  DWCO_INFRA_TASKS_DYNATRACE: {
    description: "Dynatrace: Add simple TaskFramework metrics",
    defaultOn: true,
    version: "2021.22",
    reqId: "DW00-3342",
  },
  DWCO_INFRA_SPDCONVERSION_CLEANUP: {
    description: "[SDP] Conversion: Retry Mechanism for Clean-Up",
    defaultOn: true,
    version: "2023.21",
    reqId: "DW00-10008",
    depFeature: ["DWCO_INFRA_SDPCONVERSION"],
  },
  DWCO_INFRA_TASK_SCHEDULE_PAUSE: {
    description: "Task Framework - individual schedules pause",
    defaultOn: true,
    version: "2023.08",
    reqId: "DW00-5906",
    depFeature: [],
  },
  DWCO_INFRA_TASK_ZOMBIE_JOBS: {
    description: "Garbage collection Zombie Jobs",
    defaultOn: true,
    version: "2022.09",
    reqId: "DW00-5106",
  },
  DWCO_INFRA_TASKS_HEARTBEAT: {
    description: "Task Framework: Enable tasks to set a Health Timestamp",
    defaultOn: true,
    version: "2022.01",
    reqId: "DW00-4715",
  },
  DWCO_INFRA_TASKS_PROCEDURES: {
    description: "Task Chains - call open schema procedures",
    defaultOn: true,
    version: "2024.13",
    reqId: "DS00-390",
    depFeature: [],
  },
  DWCO_IMPORT_METADATA_AMT: {
    description: "Import Metadata: Determine analytical measure type",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-4180",
  },
  DWCO_IMPORT_METADATA_VIA_DATA_PRODUCT: {
    description:
      "Import Entity Wizard: [UCL] Semantic onboarding via Data Product Detail Page without CSN_EXPOSURE for LOB Applications",
    defaultOn: true,
    version: "2024.22",
    reqId: "DS00-1239",
    depFeature: ["DWCO_IMPORT_METADATA_VARIANT_CONSOLIDATION", "DWCO_BDC"],
  },
  DWCO_IMPORT_METADATA_VARIANT_CONSOLIDATION: {
    description:
      "Import Entity Wizard: Harmonize Import Wizard Variants for Semantic Onboarding and Data Layer Landing Page/Repository",
    defaultOn: true,
    version: "2024.22",
    reqId: "DS00-2351",
    depFeature: [],
  },
  DWCO_IMPORT_METADATA_VIA_UCL_SYSTEM_TYPE: {
    description:
      "Import Entity Wizard: [UCL] Semantic onboarding via system type without CSN_EXPOSURE for LOB Applications",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-193",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_IMPORT_METADATA_I18N: {
    description: "Import Meta Data Wizard - i18N Handling in Import Wizard",
    defaultOn: true,
    version: "2024.09",
    reqId: "DS00-671",
    depFeature: [],
  },
  DWCO_IL_WORKAREA_CONFIG: {
    description: "Improved configuration of work areas in intelligent lookup",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-8646",
    depFeature: [],
  },
  DWCO_IMPORT_MANAGER_BWBRIDGE_MS: {
    description: "Import Metadata: Multiple selected entities for BW Bridge",
    defaultOn: true,
    version: "2023.10",
    reqId: "DW00-6238",
  },
  DWCO_IMPORT_MANAGER_VIA_CDI_V2: {
    description: "Import Metadata: Use CDI services version $2 to solve data-type incompatibilities",
    defaultOn: true,
    version: "2022.22",
    reqId: "DW00-6831",
  },
  DWCO_IMPORT_MANAGER_VIA_CDI_V2B: {
    description: "Import Metadata: Adapt CDI $2 service for the upcoming data access technologies",
    defaultOn: true,
    version: "2023.05",
    reqId: "DW00-7350",
  },
  DWCO_MODEL_VALIDATION: {
    description: "Data Layer Modeler - Model Validation",
    defaultOn: true,
    version: "2024.13",
    reqId: "DW00-10342",
    depFeature: [],
  },
  DWCO_MONITORING_ADMISSION_CONTROL: {
    description: "Tenant Monitoring UI - Include Admission Control events",
    defaultOn: true,
    version: "2023.04",
    reqId: "DW00-7795",
    depFeature: [],
  },
  DWCO_MODELING_AM_AGGREGATION_AVG_REFACTORING: {
    description: "Analytic Model - Refactor Standard Aggregation",
    defaultOn: true,
    version: "2024.09",
    reqId: "DS00-644",
    depFeature: [],
  },
  DWCO_MODELING_ALLOW_DOTS: {
    description: "Allow DOTs in Modeling (Namespace)",
    defaultOn: true,
    version: "2024.18",
    reqId: "DS00-3046",
    depFeature: [],
  },
  DWCO_MODELING_AM_CROSS_SPACE_SHARING: {
    description: "Analytic Model - Stacking - Cross Space Sharing of Analytic Models",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2745",
    depFeature: ["DWCO_MODELING_ANALYTIC_MODEL_STACKING"],
  },
  DWCO_MODELING_AM_DAC_SUPPORT: {
    description: "Analytic Model support for DAC",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-2325",
    depFeature: ["DWCO_DAC_SUPPORT_CALC_VIEW"],
  },
  DWCO_MODELING_AM_DERIVATION_RESTRICTED_AND_FILTER: {
    description: "Analytic Model - Variable Derivation for Filter and Restricted Measure Variables",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4033",
    depFeature: [],
  },
  DWCO_MODELING_AM_DYNAMIC_VARIABLES: {
    description: "Analytic Model - Dynamic Default Variables in SAC Variable Prompt",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-2718",
    depFeature: [],
  },
  DWCO_MODELING_AM_FORMAT_OF_MEASURES: {
    description: "Analytic Model: standard formatting of measures",
    defaultOn: true,
    version: "2025.09",
    reqId: "DS00-948",
    depFeature: [],
  },
  DWCO_MODELING_AM_GRAND_TOTAL: {
    description: "Analytic Model - Calculated Measures - add Support to GrandTotal",
    defaultOn: true,
    version: "2025.05",
    reqId: "DS00-4367",
    depFeature: [],
  },
  DWCO_MODELING_AM_LIFECYCLE_HANDLING: {
    description: "Analytic Model - Introduce status changes to deploy for changes to underlying hierarchy",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-1142",
    depFeature: [],
  },
  DWCO_MODELING_AM_LIFECYCLE_HANDLING_STATUS: {
    description: "Analytic Model - Updating status after changes to dependencies",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3656",
    depFeature: ["DWCO_MODELING_AM_LIFECYCLE_HANDLING"],
  },
  DWCO_MODELING_AM_MEASURE_DEPENDENCIES: {
    description: "Analytic Model - Measure dependency graph",
    defaultOn: true,
    version: "2025.09",
    reqId: "DS00-3678",
    depFeature: [],
  },
  DWCO_MODELING_AM_MULTI_RANGE: {
    description: "Analytic Model - Variable Default Values for Multiple Range",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-3629",
    depFeature: [],
  },
  DWCO_MODELING_AM_MULTI_SINGLE_VALUE: {
    description: "Analytic Model - Variable Default Values for Multiple Single",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-2720",
    depFeature: [],
  },
  DWCO_MODELING_AM_MULTI_STRUCTURE: {
    description: "Analytic Model - Multi Structure Query support",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4102",
    depFeature: [],
  },
  DWCO_MODELING_AM_NEW_DIM_HANDLING: {
    description: "Analytic Model - Dimension handling",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-659",
    depFeature: [],
  },
  DWCO_MODELING_AM_PREVIEW_BOOKMARK: {
    description: "Analytic Model - Supportability Request: Export Import of the Preview Selection in Analytic",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4792",
    depFeature: [],
  },
  DWCO_MODELING_AM_PROTECT_DATA_EXPORT: {
    description: "Analytic Model - Protect data preview export of data",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4369",
    depFeature: [],
  },
  DWCO_MODELING_AM_REPLACE_SOURCES: {
    description: "Analytic Model - Replace FactSource/Dimension Source in Analytic Model",
    defaultOn: true,
    version: "2025.09",
    reqId: "DS00-577",
    depFeature: [],
  },
  DWCO_MODELING_AM_RESTORE_VERSION: {
    description: "Analytic Model: Restore version",
    defaultOn: true,
    version: "2025.05",
    reqId: "DS00-3880",
    depFeature: ["DWCO_MODELING_TECHNICAL_VERSIONS_RESTORE"],
  },
  DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE: {
    description: "Analytic Model - Stacking - Variable Handling - Set Value",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4070",
    depFeature: [],
  },
  DWCO_MODELING_AM_UNBOOKED_EXCEPTION_AGGREGATION: {
    description: "Analytic Model - Support FIrst/Last/Average with unbooked",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4550",
    depFeature: [],
  },
  DWCO_MODELING_AM_UNIT_CONVERSION: {
    description: "Analytic Model - Unit Conversion",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-10486",
    depFeature: [],
  },
  DWCO_MODELING_AM_YTD_QTD_MTD: {
    description: "Analytic Model - add support of YTD/QTD/MTD and %GrandTotal in the Calculated Measure Editor",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-578",
    depFeature: [],
  },
  DWCO_MODELING_ANALYTIC_MODEL_STACKING: {
    description: "Analytic Model - Stacking - Analytic Measures - runtime/deployment",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-653",
    depFeature: [],
  },
  DWCO_MODELING_TECHNICAL_VERSIONS_RESTORE: {
    description: "Basic object versioning : Restore version - GVB, SQLB",
    defaultOn: true,
    version: "2025.05",
    reqId: "DS00-1171",
    depFeature: ["DWCO_MODELING_TECHNICAL_VERSIONS"],
  },
  DWCO_MODELING_UNIT_CONVERSION_VIEWS: {
    description: "Enabling Unit ConversionTables and Views",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-197",
    depFeature: [],
  },
  DWCO_MODELING_ANALYTIC_MODEL_STACKING_LIFECYCLE: {
    description: "Analytic Model - Stacking - Life Cycle Handling - Set Repository Status of Dependent AMs",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-795",
    depFeature: [],
  },
  DWCO_MODELING_ANNOTATE_PARTITIONS: {
    description: "[BDC] Allow Customer to create some partitions on Ingestion space local table ",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4590",
    depFeature: ["DWCO_INFRA_REPOSITORY_EXTENSION_CORE"],
  },
  DWCO_MODELING_ASSOC_CHECKS: {
    description:
      "_Improve UI and validation of different kinds of associations Text, Foreign Key Ensure correct CSN annotation are generated_",
    defaultOn: true,
    version: "2024.06",
    reqId: "DW00-10026",
    depFeature: [],
  },
  DWCO_MODELING_BROWSER_LAZY: {
    description: "Optimize Backend calls for Browser editors",
    defaultOn: true,
    version: "2022.16",
    reqId: "DW00-6663",
  },
  DWCO_MODELING_BUSINESS_LAYER_CSN_GENERATION: {
    description: "Business Layer: Asynchronous perspective deployment",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-663",
    depFeature: [],
  },
  DWCO_MODELING_BB_REPOSITORY_EXPLORER: {
    description: "List Business Layer Objects in Repository Explorer / Data Lineage",
    defaultOn: true,
    version: "2024.03",
    reqId: "DW00-7775",
    depFeature: [],
  },
  DWCO_MODELING_BB_REPOSITORY_PACKAGE: {
    description: "Repository Package support in Business Layer editors",
    defaultOn: true,
    version: "2024.11",
    reqId: "DW00-10030",
    depFeature: [],
  },
  DWCO_MODELING_BB_ROUTING_TECHNICAL_NAMES: {
    description: "BL - UI routing via technical names",
    defaultOn: true,
    version: "2023.18",
    reqId: "DW00-10293",
    depFeature: [],
  },
  DWCO_MODELING_BB_TIME_DIMENSION: {
    description: "Time Dimension for Business Layer",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-3680",
  },
  DWCO_MODELING_BB_INT_DIM_NAMES: {
    description: "Business Layer: Perspective Internal Dimension Names",
    defaultOn: true,
    version: "2023.08",
    reqId: "DW00-9190",
    depFeature: [],
  },
  DWCO_MODELING_CSN_DEPSANALYZER: {
    description: "[CSN Dependencies Analyzer] Use deepsea route rather than deepsea libraries in UI",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-626",
    depFeature: [],
  },
  DWCO_MODELING_COMPATIBILITY_CONTRACTS: {
    description: "_Introduction of compatibility contracts in data layer_",
    defaultOn: true,
    version: "2024.18",
    reqId: "DW00-8798",
    depFeature: [],
  },
  DWCO_MODELING_CATALOG_TYPE_SEMANTIC_TYPE: {
    description: "Adjust filter section for business catalog",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-3300",
  },
  DWCO_MODELING_COLUMN_VALUE_HELP: {
    description: "Column value help/Content assist for column data in expression editors of Data Builder",
    defaultOn: true,
    version: "2023.13",
    reqId: "DW00-4546",
  },
  DWCO_MODELING_DATA_ACTIVATION_CONFIGURATION: {
    description: "Configuration settings in the editors for data activation",
    defaultOn: true,
    version: "2021.13",
    reqId: "DW00-3763",
    depFeature: ["DWCO_INFRA_DATA_SUITE_PROVISIONING"],
  },
  DWCO_MODELING_DAC_RESTORE_VERSION: {
    description: "DAC Support for Technical Version Restore",
    defaultOn: true,
    version: "2025.06",
    reqId: "DS00-1522",
    depFeature: ["DWCO_MODELING_TECHNICAL_VERSIONS_RESTORE"],
  },
  DWCO_MODELING_DATABUILDER_LANDING: {
    description:
      "Folder support : Refactoring of Data Layer Landing Page using Search Composite Control (Repository Explorer)",
    defaultOn: true,
    version: "2024.03",
    reqId: "DW00-8334",
    depFeature: [],
  },
  DWCO_MODELING_DM_COUNT_PERFORMANCE: {
    description: "Data Preview - Repeated select count(*) make preview performance really slow",
    defaultOn: false,
    version: "2023.05",
    reqId: "DW00-9274",
    depFeature: [],
  },
  DWCO_MODELING_DFFERENTIATE_UNMANAGED_TABLES: {
    description: "Differentiate between Local Table, HDI / Open SQL Schema and Remote tables",
    defaultOn: true,
    version: "2022.16",
    reqId: "DW00-3550",
  },
  DWCO_MODELING_DM_EDIT_FIND_REPLACE: {
    description: "Data Maintenance - Support find and replace in column value of a filtered set",
    defaultOn: true,
    version: "2024.01",
    reqId: "DW00-4142",
    depFeature: [],
  },
  DWCO_MODELING_DM_KEY_COLUMNS: {
    description: "Key and Mandatory column related handling in Data Maintenance",
    defaultOn: true,
    version: "2021.20",
    reqId: "DW00-4707",
  },
  DWCO_MODELING_DM_OPT_DATAVIEWER: {
    description:
      "Data Viewer - Provide an optimized and consistent  browsing experience in data preview component across all data sources",
    defaultOn: true,
    version: "2024.10",
    reqId: "DW00-10822",
    depFeature: [],
  },
  DWCO_MODELING_DM_USABILITY: {
    description: "Data Maintenance - usability enhancements",
    defaultOn: true,
    version: "2021.17",
    reqId: "DW00-4409",
  },
  DWCO_MODELING_DL_TEXTASSOC: {
    description: "Data layer Text table and Text association support",
    defaultOn: true,
    version: "2021.12",
    reqId: "DW00-3100",
  },
  DWCO_MODELING_EDITOR_SETTINGS: {
    description: "Performance View Builder : Move Galilei Model to dedicated editorSettings section",
    defaultOn: true,
    version: "2023.15",
    reqId: "DW00-7805",
  },
  DWCO_MODELING_EDV_LTF_FIND_REPLACE: {
    description: "Support Find & Replace for LTF",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-3416",
    depFeature: [],
  },
  DWCO_MODELING_EXTENSIONS_PROPAGATION: {
    description: "[BDC] Datasphere Extensions propagation M11 5star - Feature Flag",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4739",
    depFeature: [],
  },
  DWCO_MODELING_EXTERNAL_HIERARCHY_SUPPORT_COMPOUNDING: {
    description: "SEAL External hierarchies with directory - Support compound keys in hierarchy ID",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-3435",
    depFeature: [],
  },
  DWCO_MODELING_GEN_AI_SEMANTIC_ENRICHMENT: {
    description: "Gen AI - Semantic Enrichment : Feature Flag Story",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4126",
    depFeature: [],
  },
  DWCO_MODELING_GVE_CSN_GENERATION: {
    description: "[GVE] Refactoring/Improving GVE CSN generation",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4095",
    depFeature: [],
  },
  DWCO_MODELING_IL_SEARCH: {
    description: "I&L : Search",
    defaultOn: true,
    version: "2023.01",
    reqId: "DW00-4814",
  },
  DWCO_MODELING_IL_VIEWPORT: {
    description: "I&L performance and resilience",
    defaultOn: true,
    version: "2023.24",
    reqId: "DW00-8986",
    depFeature: [],
  },
  DWCO_MODELING_IMPORT_MANAGER_BW4_PERMISSION: {
    description: "Import Meta Data Wizard - Include Import Permissions capability into BW/4 Connection Type",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-11158",
    depFeature: ["DWCO_MODELING_COMPATIBILITY_CONTRACTS"],
  },
  DWCO_MODELING_IMPORT_MANAGER_CUBES: {
    description: "Import Metadata: Handling of Cubes",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-5827",
  },
  DWCO_MODELING_IMPORT_ORD: {
    description: "Import Metadata: Mapping to runtime APIs (ORD-integration)",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-4104",
  },
  DWCO_MODELING_IMPORVE_SHARING_AFTER_DEPLOYMENT: {
    description: "[Resilience] Improve sharing concept after deployment",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4878",
    depFeature: [],
  },
  DWCO_MODELING_IMPROVE_OUTPUT_UPDATE: {
    description: "Performance View builder : Reduce time to update output",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-7417",
  },
  DWCO_MODELING_INCREASE_LENGTH_OF_TECHNICAL_NAMES: {
    description: "Increase length of technical name of entities and elements",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2761",
    depFeature: [],
  },
  DWCO_MODELING_INCREASE_MAX_LENGTH_OF_ENTITIES: {
    description: "Increase length of technical name of entities",
    defaultOn: true,
    version: "2022.16",
    reqId: "DW00-6388",
  },
  DWCO_MODELING_METADATAIMPORT_WORKERPOOL: {
    description: "Metadata import - move event loop blocking parts to workerpool",
    defaultOn: false,
    version: "2024.22",
    reqId: "DS00-1163",
    depFeature: [],
  },
  DWCO_MODELING_ODATA_ADAPTER_RESTRICTION: {
    description: "Data Maintenance - Handle OData Adapter restrictions in data preview",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-9182",
  },
  DWCO_MODELING_TECHNICAL_VERSIONS: {
    description: "Basic object versioning : Version history & Download CSN",
    defaultOn: true,
    version: "2025.05",
    reqId: "DS00-1013",
    depFeature: [],
  },
  DWCO_MODELING_VALIDATION_COLUMNS_IN_ASSOC: {
    description: "Data Validation - Columns that are used in Association",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2679",
    depFeature: [],
  },
  DWCO_MODELING_VALIDATION_ENABLE_REMOTE_TABLES: {
    description: "Data Validation - Support Remote Tables",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-2675",
    depFeature: [],
  },
  DWCO_MODELING_VALIDATION_NODE_ELEMENT_CHECK: {
    description: "Data Validation - Check Node Elements",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4997",
    depFeature: [],
  },
  DWCO_MODELING_VALIDATION_REFERENTIAL_INTEGRITY: {
    description: "Data Validation - Referential Integrity of Associations",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-2677",
    depFeature: [],
  },
  DWCO_MODELING_VALIDATION_UNASSIGNED_NODES: {
    description: "Data Validation - Validation check for existence of unassigned nodes",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4114",
    depFeature: [],
  },
  DWCO_MODELING_VIEW_INHERITED_ASSOCIATIONS: {
    description: "Association inherited in stacked view",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-2987",
  },
  DWCO_MARKETPLACE_DS003444_CONSUMPTION_API: {
    description: "First Consumer-API endpoints and CLI commands",
    defaultOn: true,
    version: "2025.10",
    reqId: "DS00-3444",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DS004458_TRACE_ID: {
    description: "Marketplace: Unify correlation id and trace id in dynatrace via W3C Trace Context",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4458",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DS005182_LISTING_ERROR_HANDLING: {
    description: "Error Message Display for Data Product Listing Failures",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5182",
    depFeature: ["DWCO_MARKETPLACE_DELTASHARE_MVP_DS00_4097"],
  },
  DWCO_MARKETPLACE_DS005475_PROVIDER_DELETION_ODATA: {
    description: "Provider Deletion via OData API in Marketplace",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5475",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DS00829_CXP3_ACCESS_POLICIES: {
    description: "[CX] Phase 3 - Create Objects in DSI",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-829",
    depFeature: [],
  },
  DWCO_LTA_DYNA_DATE_SELECT: {
    description: "LTA - Enhance selective data deletion with dynamic date ranges",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4804",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DELTASHARE_FORMATIONS_DS005180: {
    description: "Dynamic Tenant Formation Management with Contextual User Assignment and Data Marketplace Integrity",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5180",
    depFeature: ["DWCO_MARKETPLACE_DELTASHARE_MVP_DS00_4097"],
  },
  DWCO_MARKETPLACE_DELTASHARE_MVP_DS00_4097: {
    description: "Marketplace support for delta share products MVP",
    defaultOn: true,
    version: "2025.10",
    reqId: "DS00-4097",
    depFeature: ["DWCO_MODELING_SERVICE_DEPLOYMENT"],
  },
  DWCO_MARKETPLACE_DS001242_PUBLIC_API: {
    description: "Publish OpenAPI specification for Public APIs on SAP API Business Hub",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-1242",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DS001245_INPUT_PARAMETERS: {
    description: "Enable Input Parameters for Data Products",
    defaultOn: true,
    version: "2024.11",
    reqId: "DS00-1245",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DS00906_LOGGER: {
    description: "Adapt @sap/dwc-logger in marketplace service",
    defaultOn: true,
    version: "2024.19",
    reqId: "DS00-906",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DS001409_USERID_UPDATE: {
    description: "User Identification via Non-Changing Property: Phase 1: User Name Collection",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1409",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DS001666_DATAFINDER: {
    description: "Integrate Datarade Datafinder link",
    defaultOn: true,
    version: "2024.16",
    reqId: "DS00-1666",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DW008794_RESTRICT_VISIBILITY: {
    description: "Restrict My Products & Data Sharing Cockpit Space Visibility",
    defaultOn: true,
    version: "2023.15",
    reqId: "DW00-8794",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DW008955_LICENSE_DATA_SELECTION: {
    description: "License-specific Data Selection",
    defaultOn: true,
    version: "2025.01",
    reqId: "DW00-8955",
    depFeature: [],
  },
  DWCO_LS_REMOTE_TABLE_USAGE_TRACKING_DATASPHERE: {
    description: "Remote Tables - Usage Tracking with Datasphere",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4967",
    landscape: true,
  },
  DWCO_LTA_DATA_DELETION: {
    description: "Local Table File- Support on-demand and scheduled data deletion",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4802",
    depFeature: ["DWCO_LOCAL_TABLE_TASKS_ACTIVE_RECORDS", "DWCO_LTA_DYNA_DATE_SELECT"],
  },
  DWCO_LTA_DETAIL_SCREENS: {
    description: "Local Table (File) - Monitor Details Screen [Optimize and Merge] in Data Integration Monitor",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-3215",
    depFeature: ["DWCO_LOCAL_TABLE_FILES_MONITOR", "DWCO_LARGE_SYSTEMS_SPARK_SELECTION", "DWCO_LARGE_SYSTEMS_APPS_API"],
  },
  DWCO_LARGE_SYSTEMS_ALLOWLIST: {
    description: "LSA - Implement the IP Allowlist for Spark and HDLF Instances",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4761",
    depFeature: [],
  },
  DWCO_LARGE_SYSTEMS_APPS_API: {
    description: "[LSA]: Read API for Spark and HDLF Applications",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-3791",
    depFeature: [],
  },
  DWCO_LARGE_SYSTEMS_BW_PCE: {
    description: "[BW PCE]: Grant & Revoke Access to HDLF",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-4446",
    depFeature: ["DWCO_LARGE_SYSTEMS"],
  },
  DWCO_LARGE_SYSTEMS_METERING: {
    description: "[LSA]Add Spark and HDFL to DS Billing Metric Report",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-2347",
    depFeature: ["DWCO_LARGE_SYSTEMS"],
  },
  DWCO_LARGE_SYSTEMS_REQUESTS: {
    description: "[LSA]Create the Object Store Requests license and update FTC/Estimator/Metering",
    defaultOn: true,
    version: "2025.12",
    reqId: "DS00-4342",
    depFeature: ["DWCO_LARGE_SYSTEMS"],
  },
  DWCO_LARGE_SYSTEMS_SPARK_DEFAULT: {
    description: "[LSA]: UI Default Spark Applications in a HDLF Space",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-4760",
    depFeature: [],
  },
  DWCO_LARGE_SYSTEMS_SPARK_SELECTION: {
    description: "LSA: Spark application selection for tasks",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-3798",
    depFeature: ["DWCO_LARGE_SYSTEMS"],
  },
  DWCO_LOCAL_TABLE_FILES_SIZE_DELTA_TABLE: {
    description: "Local Table (File) - Size of DeltaTable in Monitor",
    defaultOn: true,
    version: "2025.10",
    reqId: "DS00-3293",
    depFeature: ["DWCO_LOCAL_TABLE_FILES_MONITOR"],
  },
  DWCO_LOCAL_TABLE_FILES_SIZE_INBOUND_BUFFER: {
    description: "Local Table (File) - Size of Inbound Buffer in Monitor",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4100",
    depFeature: ["DWCO_LOCAL_TABLE_FILES_MONITOR"],
  },
  DWCO_LOCAL_TABLE_FILES_VACUUM: {
    description: "Local Table (File) - Housekeeping of change records in Table Editor and Task Chain(Vacuum)",
    defaultOn: true,
    version: "2025.05",
    reqId: "DS00-3682",
    depFeature: ["DWCO_LOCAL_TABLE_FILES"],
  },
  DWCO_LARGE_SYSTEMS_SUPPORT_USER: {
    description: "[LSA]: HDLF Support User",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4325",
    depFeature: [],
  },
  DWCO_LOCAL_TABLE_FILES: {
    description: "Local Tables with File Storage (LTF) - Designtime and Deployment",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-619",
    depFeature: [],
  },
  DWCO_LOCAL_TABLE_FILES_AUTO_MERGE: {
    description: "Local Table (File) - auto merge for new data in inbound buffer",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-4014",
    depFeature: ["DWCO_LOCAL_TABLE_FILES_MERGE"],
  },
  DWCO_LOCAL_TABLE_FILES_BWPUSH: {
    description: "Local Table (File) - designtime and deployment adjustments for BW PCE Push",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-4339",
    depFeature: ["DWCO_LOCAL_TABLE_FILES"],
  },
  DWCO_LOCAL_TABLE_FILES_BWPUSH_MERGE: {
    description: "Local Table (File) - merge task adjustments for BW PCE Push",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-4341",
    depFeature: ["DWCO_LOCAL_TABLE_FILES_BWPUSH"],
  },
  DWCO_LOCAL_TABLE_FILES_BWPUSH_UI: {
    description: "Local Table (File) - user interface adjustments for BW PCE Push ",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-4340",
    depFeature: ["DWCO_LOCAL_TABLE_FILES_BWPUSH"],
  },
  DWCO_LOCAL_TABLE_FILES_EDITOR: {
    description: "Local Tables with File Storage (LTF) - Table Editor",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-1705",
    depFeature: ["DWCO_LOCAL_TABLE_FILES"],
  },
  DWCO_LOCAL_TABLE_FILES_HANAQRC4_2024: {
    description: "Local Table (File) - additional data types",
    defaultOn: true,
    version: "2025.12",
    reqId: "DS00-4023",
    depFeature: ["DWCO_LOCAL_TABLE_FILES"],
  },
  DWCO_LOCAL_TABLE_FILES_MERGE: {
    description: "Local Tables with File Storage (LTF) - Inbound Buffer Merge Task",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-620",
    depFeature: ["DWCO_LOCAL_TABLE_FILES"],
  },
  DWCO_LOCAL_TABLE_FILES_MERGE_LOOPED_PROCESSING: {
    description: "Local Table (File) - Merge Scalability: Looped Processing",
    defaultOn: true,
    version: "2025.12",
    reqId: "DS00-4861",
    depFeature: ["DWCO_LOCAL_TABLE_FILES_MERGE", "DWCO_LOCAL_TABLE_FILES_SIZE_DELTA_TABLE"],
  },
  DWCO_LOCAL_TABLE_FILES_MONITOR: {
    description: "Local Tables with File Storage (LTF) - Merge in Task Chain and Monitor Overview Screen",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-1707",
    depFeature: ["DWCO_LOCAL_TABLE_FILES"],
  },
  DWCO_JOULE_AI_CONFIGURATION_SCREEN: {
    description: "AI Activation Screen Section for Joule and AI Service Tab visibility",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-3654",
    depFeature: ["DWCO_UCL_JOULE"],
  },
  DWCO_JOULE_DWAASCORE_ENHANCED_SPACE_INFO: {
    description: "Third Joule Content Release (2507 Joule Release Cycle) - Scope: Enhanced Space Informational Cases",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5404",
    depFeature: [],
  },
  DWCO_JOULE_DWAASCORE_FUNCTIONAL_BASIC: {
    description: "[Joule-Func] [Joule Content Delivery] dwaas-core enhancements",
    defaultOn: true,
    version: "2025.04",
    reqId: "DS00-4052",
    depFeature: ["DWCO_UCL_JOULE"],
  },
  DWCO_JOULE_UI_INTEGRATION: {
    description: "Joule Web client UI integration in Datasphere",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-3639",
    depFeature: ["DWCO_UCL_JOULE"],
  },
  DWCO_LARGE_SYSTEMS: {
    description: "[Large Systems]: Initial Feature Flag Enablement",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-1177",
    depFeature: [],
  },
  DWCO_IP_ALLOWLIST_REFRESH: {
    description: "[IP Allowlist] Reconciliation of new IPs from DI",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4914",
    depFeature: [],
  },
  DWCO_IP_VALUE_HELP: {
    description: "_Feature Flag for Input Parameter Value Help_",
    defaultOn: true,
    version: "2023.24",
    reqId: "DW00-8176",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DS00_4684_HAUM_PKG1: {
    description: "HAUM Integration Package 1",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4684",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DW0011638_LICENSE_PRODUCT_CTRL: {
    description: "Enhance License Scope/Product Assignment Control",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-11638",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DW00_10322_CATENA_X_API_KEY: {
    description: "Catena-X: API Key Support for Connect Plugin & Connect Data Marketplace with EDC Connector",
    defaultOn: true,
    version: "2023.25",
    reqId: "DW00-10322",
    depFeature: ["DWCO_MARKETPLACE_DW00_8188_CATENA_X_REGISTRATION"],
  },
  DWCO_MARKETPLACE_DW00_10961_CATENA_X_ACCESS_POLICIES: {
    description: "Create Access Policies in Catena-X",
    defaultOn: true,
    version: "2024.07",
    reqId: "DW00-10961",
    depFeature: ["DWCO_MARKETPLACE_DW00_8188_CATENA_X_REGISTRATION"],
  },
  DWCO_MARKETPLACE_DW00_8188_CATENA_X_REGISTRATION: {
    description: "Private Data Marketplace - Connect Plugin & Connect Data Marketplace with EDC Connector in Catena-X",
    defaultOn: false,
    version: "2023.25",
    reqId: "DW00-8188",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DW00_8770_CROSS_TENANT_LIVE_CONSUMPTION: {
    description: "Allow Cross-Tenant Consumption with Live Access",
    defaultOn: true,
    version: "2023.14",
    reqId: "DW00-8770",
    depFeature: [],
  },
  DWCO_MARKETPLACE_DW00_6812_DUPLICATE_PRODUCT: {
    description: "Data Product Duplication",
    defaultOn: true,
    version: "2024.10",
    reqId: "DS00-717",
    depFeature: [],
  },
  DWCO_MARKETPLACE_ENABLEMENT: {
    description: "Marketplace Data Marketplace Activation",
    defaultOn: true,
    version: "2022.02",
    reqId: "DW00-5042",
  },
  DWCO_MARKETPLACE_ENHANCED_MALWARE_SCANNER: {
    description: "Data Integration Monitor for BW Bridge",
    defaultOn: true,
    version: "2022.24",
    reqId: "DW00-5648",
    depFeature: [],
  },
  DWCO_MARKETPLACE_USAGE_STATISTICS_PROVIDER: {
    description: "Usage Statistics for Data Providers",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3446",
    depFeature: [],
  },
  DWCO_MARKETPLACE_USER_AUTHORIZATION_AGENT_ADOPTION: {
    description: "Authorization Agent adoption in marketplace",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5392",
    depFeature: ["DWCO_USER_AUTHORIZATION_AGENT_ADOPTION"],
  },
  DWCO_MA_PROVISION_ABAPCANARY: {
    description: "Enhancements for BW Bridge Provisioning",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-4558",
    depFeature: [],
  },
  DWCO_SAVE_WITH_ERROR_SQL_VIEW: {
    description: "Save with errors in SQL View Builder",
    defaultOn: true,
    version: "2023.17",
    reqId: "DW00-4892",
  },
  DWCO_SCOPE_DEPENDENT_ROLES_OVERVIEW: {
    description: "[SDP] User and Roles Management Overview UI",
    defaultOn: true,
    version: "2024.05",
    reqId: "DW00-10844",
    depFeature: [],
  },
  DWCO_MODELING_NAMESPACE: {
    description: "Namespace MVP Feature Flag",
    defaultOn: true,
    version: "2024.21",
    reqId: "DW00-10112",
    depFeature: ["DWCO_BDC", "DWCO_MODELING_ALLOW_DOTS"],
  },
  DWCO_MODELING_PARAMETER_LINEAGE: {
    description: "Change Management : input parameter better display",
    defaultOn: true,
    version: "2022.01",
    reqId: "DW00-3414",
  },
  DWCO_MODELING_REPOSITORY_PACKAGE: {
    description: "Feature Flag Story for Repository Package MVP",
    defaultOn: true,
    version: "2023.19",
    reqId: "DW00-9021",
    depFeature: [],
  },
  DWCO_MODELING_SOURCE_ENTITY_LIST: {
    description: "Refactoring of SourceEntityList using Enterprise Search UI",
    defaultOn: true,
    version: "2024.03",
    reqId: "DW00-6838",
  },
  DWCO_MODELING_SUPPORT_DISPLAY_ATTRIBUTES: {
    description: "Differentiate display attributes and navigation attributes",
    defaultOn: true,
    version: "2025.05",
    reqId: "DW00-9565",
    depFeature: [],
  },
  DWCO_MODELING_SUPPORT_FISCAL_TIME: {
    description: "Fiscal Calendar Support - Feature Flag",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4767",
    depFeature: [],
  },
  DWCO_MODELING_SUPPORT_SAC_SEAMLESS_PLANNING_INTEGRATION: {
    description: "Consumption of SAC planning models in Datasphere",
    defaultOn: true,
    version: "2024.09",
    reqId: "DW00-10458",
    depFeature: [],
  },
  DWCO_MODELING_SHOW_HANA_STATE: {
    description: "Designtime/Runtime Separation in Modeling: Show HANA state",
    defaultOn: true,
    version: "2021.16",
    reqId: "DW00-3054",
  },
  DWCO_MODELING_SQL_SCRIPT_VIEW_ASSOCIATIONS: {
    description: "Associations in SQL Script View Editor(Table Function)",
    defaultOn: true,
    version: "2022.03",
    reqId: "DW00-2626",
  },
  DWCO_MODELING_SERVICE_DEPLOYMENT: {
    description: "SEAL - Support service deployment [Delta Share]",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-3929",
    depFeature: [],
  },
  DWCO_MODELING_SAVE_FOLDER_SUPPORT: {
    description: "Folder support : Save/Save as should offer a folder selection",
    defaultOn: true,
    version: "2024.03",
    reqId: "DW00-8330",
    depFeature: [],
  },
  DWCO_MODELING_SEAL_SKIP_UNCHANGED_ENTITIES: {
    description: "SEAL - Enhanced deployment status for the clients",
    defaultOn: true,
    version: "2023.24",
    reqId: "DW00-9992",
    depFeature: [],
  },
  DWCO_MODELING_SKIP_UNKNOWN_ANNOTATION: {
    description: "Data Layer Editors should keep unknown annotations",
    defaultOn: true,
    version: "2022.03",
    reqId: "DW00-1802",
  },
  DWCO_NOTIFY_CONSUMPTION_THRESHOLD: {
    description: "Notify when consumption-based features hits threshold",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3398",
    depFeature: [],
  },
  DWCO_ODATA_BATCH_SUPPORT: {
    description: "[Consumption API] Enable $batch call for GET requests within ODATA API's.",
    defaultOn: true,
    version: "2025.12",
    reqId: "DS00-3595",
    depFeature: [],
  },
  DWCO_ODATA_CURSOR_RESULT_SET: {
    description: "[Consumption API] Switch Firefly InA to cursor ResultSet for memory handling",
    defaultOn: true,
    version: "2025.10",
    reqId: "DS00-4868",
    depFeature: [],
  },
  DWCO_ODATA_NLQ_HIERARCHY: {
    description: "NLQ Metadata - Hierarchy for Dimensions",
    defaultOn: true,
    version: "2025.10",
    reqId: "DS00-4822",
    depFeature: ["DWCO_SAC_JUSTASK_NLQ_METADATA"],
  },
  DWCO_ODATA_RELATIONAL_ASSOCIATION: {
    description: "[Consumption API] OData - allow associations for relational consumption",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1158",
    depFeature: [],
  },
  DWCO_ODATA_SEMANTIC_TYPE: {
    description: "[Consumption API] Exposure of Semantic Usage Type, Semantic Type and Label Column",
    defaultOn: true,
    version: "2025.04",
    reqId: "DS00-2826",
    depFeature: [],
  },
  DWCO_ODATA_UI_SERVICE_URL: {
    description: "[Consumption API] Expose Service Route on OData UI Editor",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5074",
    depFeature: [],
  },
  DWCO_ODC_CATALOG_DATABASE_INTEGRITY_CHECK: {
    description: "Catalog Database Storage Integrity Checks",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-2005",
    depFeature: [],
  },
  DWCO_ODC_DATA_PRODUCT_MONITORING: {
    description: "Monitor status of shared/installed Data Products in Databricks",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4151",
    depFeature: [],
  },
  DWCO_ODC_DELTASHARE_DP_EXTRACTION: {
    description: "Extract DSP delta-share data products from UCL",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-4072",
    depFeature: [
      "DWCO_ODC_UCL_ORD_EXPOSURE",
      "DWCO_ODC_UCL_DATA_PRODUCT_MONITORING",
      "DWCO_CENTRAL_BW_PCE_SHARING_TO_DBX",
    ],
  },
  DWCO_ODC_DELTASHARE_DP_SHARE: {
    description: "Share 'Delta Share' Data Products with Databricks",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4074",
    depFeature: ["DWCO_ODC_DELTASHARE_DP_EXTRACTION", "DWCO_CENTRAL_BW_PCE_SHARING_TO_DBX"],
  },
  DWCO_ODC_DM_DETAILS_PAGE_466: {
    description: "Common Details Page for Marketplace Data Products and Data Providers",
    defaultOn: true,
    version: "2024.20",
    reqId: "DS00-466",
    depFeature: ["DWCO_ODC_DM_UNIFIED_SEARCH_DS00_468"],
  },
  DWCO_ODC_DM_ENRICHMENT_EVENTS_DW00_10238: {
    description: "Keep catalog enrichment up-to-date in Marketplace",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-10238",
    depFeature: [],
  },
  DWCO_ODC_DM_LINEAGE_467: {
    description: "Marketplace Data Products Lineage",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-467",
    depFeature: ["DWCO_ODC_DM_DETAILS_PAGE_466"],
  },
  DWCO_ODC_DM_SEARCH_RESILIENCE: {
    description: "Resilience of One Data Catalog Search",
    defaultOn: true,
    version: "2024.19",
    reqId: "DS00-1278",
    depFeature: [],
  },
  DWCO_ODC_DM_UNIFIED_SEARCH_DS00_468: {
    description: "Catalog Search for Data Providers",
    defaultOn: true,
    version: "2024.13",
    reqId: "DS00-468",
    depFeature: [],
  },
  DWCO_ODC_EXTRACTION_METERING: {
    description: "Metering of extracting metadata into One Data Catalog",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-9490",
    depFeature: [],
  },
  DWCO_ODC_HDI_SUPPORT_USER: {
    description: "HDI Supportability for Catalog and C4S Storage",
    defaultOn: true,
    version: "2024.09",
    reqId: "DS00-1377",
    depFeature: [],
  },
  DWCO_ODC_MD_NAVIGATION_CONVERGENCE: {
    description: "marketplace and catalog navigation convergence",
    defaultOn: true,
    version: "2025.05",
    reqId: "DS00-465",
    depFeature: [],
  },
  DWCO_ODC_MOVE_PAIRING_CONFIG: {
    description: "Move catalog service user configuration from global to tenant storage",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-3245",
    depFeature: [],
  },
  DWCO_ODC_REBRANDING: {
    description: "Branding Changes for Databricks in Catalog",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-4749",
    depFeature: [],
  },
  DWCO_ODC_REFERENCED_ONLY_LINEAGE_IMPROVEMENT: {
    description: "Improving Data Lineage referenced-only assets visualization",
    defaultOn: true,
    version: "2025.05",
    reqId: "DS00-503",
    depFeature: [],
  },
  DWCO_ODC_SOLACE_EXCLUSIVE_PUBLISHER: {
    description: "_deepsea-daemon and deepsea publishes messages to Solace instead of RabbitMQ_",
    defaultOn: true,
    version: "2023.08",
    reqId: "DW00-8889",
    depFeature: [],
  },
  DWCO_ODC_SUPPORT_ROUTE: {
    description: "Integrate catalog support route in CIC UI",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5453",
    depFeature: [],
  },
  DWCO_ODC_UCL_DATABRICKS_POLLING: {
    description:
      "Implement UclDatabricksOrdPolling task to inform Datasphere Catalog about new/updated Data Products exposed via Databricks Connect",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-4147",
    depFeature: [],
  },
  DWCO_ODC_UCL_DATA_PRODUCT_DISPLAY: {
    description: "[UCL] SAP Data product detail page in ODC",
    defaultOn: true,
    version: "2024.22",
    reqId: "DS00-980",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_ODC_UCL_DATA_PRODUCT_FROM_DATABRICKS: {
    description: "[BDC] Enable shares from Databricks to Catalog",
    defaultOn: true,
    version: "2024.23",
    reqId: "DS00-3045",
    depFeature: ["DWCO_UCL_TENANT_MAPPING_DATA_CONNECT", "DWCO_IMPORT_METADATA_VIA_DATA_PRODUCT"],
  },
  DWCO_ODC_UCL_DATA_PRODUCT_INPUT_PORT: {
    description: "[BDC Post GA] Extract and load  derived data product input port definitions",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5285",
    depFeature: [],
  },
  DWCO_ODC_UCL_DATA_PRODUCT_INSTALL_FROM_DATABRICKS2: {
    description: "[BDC] Brownfield Databricks as a Producer of Data Products via BDC Catalog",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5263",
    depFeature: [],
  },
  DWCO_ODC_UCL_DATA_PRODUCT_MONITORING: {
    description: "Monitoring of UCL Data Products Extraction",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-2302",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_ODC_UCL_DATA_PRODUCT_SEARCH: {
    description: "[UCL] SAP Data products in Catalog search page",
    defaultOn: true,
    version: "2024.22",
    reqId: "DS00-981",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_ODC_UCL_DATA_PRODUCT_SHARE_TO_CDP: {
    description: "[BDC Post GA]Allow sharing Data Products to CDP via Catalog",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4747",
    depFeature: [],
  },
  DWCO_ODC_UCL_DATA_PRODUCT_SHARE_TO_DATABRICKS: {
    description: "[BDC] Allow sharing Data Products to Databricks via Catalog",
    defaultOn: true,
    version: "2024.23",
    reqId: "DS00-3044",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_ODC_UCL_DATA_PRODUCT_SHARE_TO_DATABRICKS2: {
    description: "[BDC] Brownfield Databricks as a consumer of Data Products via BDC Catalog",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5261",
    depFeature: ["DWCO_ODC_UCL_DATA_PRODUCT_SHARE_TO_CDP"],
  },
  DWCO_ODC_UCL_EXTRACT_AND_LOAD: {
    description: "[UCL] Catalog extract and load data products ORD information from systems of a formation",
    defaultOn: true,
    version: "2024.18",
    reqId: "DS00-973",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_ODC_UCL_ORD_EXPOSURE: {
    description: "Implement ORD Exposure endpoints for Datasphere Data Products",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-4073",
    depFeature: [
      "DWCO_MARKETPLACE_DELTASHARE_MVP_DS00_4097",
      "DWCO_LS_ODC_UCL_ORD_EXPOSURE",
      "DWCO_BDC_GA",
      "DWCO_CENTRAL_BW_PCE_SHARING_TO_DBX",
    ],
  },
  DWCO_ODC_UCL_SYSTEM_EXTRACTOR_VERSION: {
    description: "Support of UCL extractor capability",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5011",
    depFeature: [],
  },
  DWCO_ODC_UX_IMPROVEMENTS_25Q1: {
    description: "UX Improvements for Catalog Q2 2025",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1619",
    depFeature: [],
  },
  DWCO_ONEDATACATALOG_DAEMON_TENANT_UPGRADE: {
    description: "Support Background Catalog Tenant Upgrade Within Daemon",
    defaultOn: true,
    version: "2024.01",
    reqId: "DW00-8074",
    depFeature: [],
  },
  DWCO_ONEDATACATALOG_SEAMLESS_PLANNING: {
    description: "Seamless planning support in Catalog",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-314",
    depFeature: [],
  },
  DWCO_ONEDATACATALOG_SAC_EXTERNAL_VERSION: {
    description: "Support extracting lineage between SAC model and DS fact table in Catalog",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2913",
    depFeature: [],
  },
  DWCO_RESILIENCY_HANA_DOC_EXTENSION: {
    description: "Extend HANA Doc",
    defaultOn: true,
    version: "2025.04",
    reqId: "DS00-3675",
    depFeature: [],
  },
  DWCO_RESILIENT_SUPPORT_ENDPOINTS: {
    description: "Make support endpoints robust against overload",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5046",
    depFeature: ["DWCO_WORKLOAD_MANAGEMENT_GROUPS"],
  },
  DWCO_REUSABLE_ERMODELER: {
    description: "[Refactoring] ERModeler: Design revisited and provide a generic Skyline version",
    defaultOn: true,
    version: "2025.12",
    reqId: "DS00-625",
    depFeature: [],
  },
  DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION: {
    description: "Adoption of re-usable scheduler in Editors and Monitors",
    defaultOn: true,
    version: "2024.24",
    reqId: "DS00-579",
    depFeature: ["DWCO_REUSABLE_TASK_SCHEDULING_UI"],
  },
  DWCO_REUSABLE_TASK_SCHEDULING_UI: {
    description: "Re-usable Dialog for scheduling of Tasks",
    defaultOn: true,
    version: "2024.13",
    reqId: "DS00-277",
    depFeature: [],
  },
  DWCO_REPOSITORY_BACKUP: {
    description:
      "Backup and Restore in DWC (assuming still existing tenant) - Introduce minimal automation for RepositoryDB recovery",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-10095",
    depFeature: [],
  },
  DWCO_REPOSITORY_EXPLORER_SHARED_OBJECTS: {
    description: "Repository Explorer: Handling of Shared Objects",
    defaultOn: true,
    version: "2022.24",
    reqId: "DW00-7362",
  },
  DWCO_REPOSITORY_EXPLORER_TABLE: {
    description: "Repository Explorer: Add Table.Table as alternative for M.Table",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-3501",
  },
  DWCO_REP_SEARCH_ERROR_HANDLING: {
    description: "Resilience of Repository Search",
    defaultOn: true,
    version: "2024.20",
    reqId: "DS00-1277",
    depFeature: [],
  },
  DWCO_REP_SEARCH_FOLDER_ASSIGNMENT_SHARED_OBJECTS: {
    description: "Enable Folder Assignments of Shared Objects",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1602",
    depFeature: [],
  },
  DWCO_REP_SEARCH_NATURAL_LANGUAGE: {
    description: "Natural Language Search in Repository Explorer [Beta-Shipment]",
    defaultOn: false,
    version: "2024.19",
    reqId: "DS00-1425",
    depFeature: [],
  },
  DWCO_REMOTE_TABLE_USAGE_FEDERATION: {
    description: "Remote Tables - Federation Usage in Dynatrace",
    defaultOn: true,
    version: "2024.24",
    reqId: "DS00-461",
    depFeature: [],
  },
  DWCO_REMOTE_TABLE_USAGE_SOURCE_MODEL: {
    description: "Remote Tables - Extend Usage Tracking with ABAP Source Model",
    defaultOn: true,
    version: "2024.16",
    reqId: "DS00-2329",
    depFeature: [],
  },
  DWCO_ONEDATACATALOG_PUBLIC_API_STAGE_1: {
    description: "Public APIs for Datasphere Catalog - Stage 1",
    defaultOn: true,
    version: "2024.10",
    reqId: "DW00-9789",
    depFeature: [],
  },
  DWCO_ONEDATACATALOG_RECOVERY: {
    description: "Backup & Recovery For One Data Catalog GA",
    defaultOn: true,
    version: "2023.21",
    reqId: "DW00-8052",
    depFeature: [],
  },
  DWCO_ONEDATACATALOG_ROLES: {
    description: "Support more roles for Catalog",
    defaultOn: true,
    version: "2023.25",
    reqId: "DW00-9926",
    depFeature: [],
  },
  DWCO_PACEMAKER_SERVICE_ENABLED: {
    description: "Pacemaker (CSF): Pacemaker Client - Service based APIs (Phase II)",
    defaultOn: true,
    version: "2024.22",
    reqId: "DW00-6696",
    depFeature: [],
  },
  DWCO_REMOTE_TABLE_CONSISTENCY_CHECKS: {
    description: "Remote Table Deployment - Consistency Checks (Data Types, Columns,..)",
    defaultOn: true,
    version: "2022.11",
    reqId: "DW00-5707",
    depFeature: ["DWCO_REMOTE_TABLE_REFRESH_FVT"],
  },
  DWCO_PROD_BDC_PACKAGES_FROM_CONTENT_GIT_REPO: {
    description: "Support Content dev Production git repo for BDC packages release by content team",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3848",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_REDESIGN_FTC_UI: {
    description: "Redesign of FTC UI (clearly separate consumption and allocation)",
    defaultOn: true,
    version: "2024.24",
    reqId: "DS00-1824",
    depFeature: [],
  },
  DWCO_REMOTETABLES_BWBRIDGE_LINEAGE: {
    description: "Data Lineage from Remote Tables to BW Bridge objects",
    defaultOn: true,
    version: "2024.09",
    reqId: "DW00-9777",
    depFeature: [],
  },
  DWCO_REMOTE_TABLE_CDI_RT_ASYNC: {
    description: "Remote Tables - Resiliency for real-time replication using SDI CDI Adapter",
    defaultOn: true,
    version: "2022.10",
    reqId: "DW00-6531",
  },
  DWCO_REMOTE_TABLE_HDLF_DELTA: {
    description: "Remote Tables - Delta from DeltaShare (HDLF / DBX)",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3593",
    depFeature: [],
  },
  DWCO_REMOTE_TABLE_PARAMETER_SUPPORT_ABAP_CDS_VIEWS: {
    description: "Remote Tables - Parameter Support - ABAP CDS Views",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2067",
    depFeature: [],
  },
  DWCO_REMOTE_TABLE_REFRESH_FVT: {
    description: "Remote Table Refresh: Refresh for FVT based connections in editor",
    defaultOn: true,
    version: "2022.11",
    reqId: "DW00-2015",
  },
  DWCO_SELF_SERVICE_TENANT_MAPPING: {
    description: "BDC Self-Service UCL Tenant Mapping for LOBs and Partners (via FOS)",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4857",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_SEMANTIC_ON_BOARDING: {
    description: "Semantic On-boarding for Metadata Import, MP DP and ACN",
    defaultOn: true,
    version: "2023.25",
    reqId: "DW00-9963",
    depFeature: [],
  },
  DWCO_SPACES_ASSIGNABLE_BY_SAML: {
    description: "Space assignment via SAML attributes",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-3200",
  },
  DWCO_SPACE_API_DEFINITIONS_ENABLED: {
    description: "[Marketplace] Space Deployment API with CSN",
    defaultOn: true,
    version: "2022.02",
    reqId: "DW00-5404",
  },
  DWCO_SPACE_CAPABILITIES: {
    description: "Space Capabilities",
    defaultOn: true,
    version: "2024.21",
    reqId: "DS00-3251",
    depFeature: [],
  },
  DWCO_SPACES_CURRENCY_CONFIG: {
    description: "Currency configuration in Spaces",
    defaultOn: true,
    version: "2022.11",
    reqId: "DW00-4293",
  },
  DWCO_SPACES_CURRENCY_CONVERSION_DATA_SHARING: {
    description: "Currency Conversion: Cross Space Sharing",
    defaultOn: true,
    version: "2023.01",
    reqId: "DW00-8642",
  },
  DWCO_SPACES_DATALAKE_MONITORING: {
    description: "Data Lake Monitoring",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-1560",
  },
  DWCO_REP_SEARCH_NATURAL_LANGUAGE_ELEMENTS: {
    description: "Natural Language Search in Repository: Search via additional model properties",
    defaultOn: true,
    version: "2025.12",
    reqId: "DS00-4702",
    depFeature: [],
  },
  DWCO_REP_SEARCH_NATURAL_LANGUAGE_GA: {
    description: "Natural Language Search in Repository",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-3419",
    depFeature: [],
  },
  DWCO_REP_SEARCH_NATURAL_LANGUAGE_RELATIONS: {
    description: "Natural Language Search in Repository: Search via impact and lineage",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4742",
    depFeature: [],
  },
  DWCO_REP_SEARCH_NATURAL_LANGUAGE_UX: {
    description: "Natural Language Search in Repository: UX improvement for natural language explain",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4743",
    depFeature: [],
  },
  DWCO_RESILIENCE_CUSTOM_CONNECTION_POOL: {
    description: "Tenant-based hana connection pool limits",
    defaultOn: true,
    version: "2024.23",
    reqId: "DW00-10390",
    depFeature: [],
  },
  DWCO_RESILIENCY_AUDITLOG_BUFFERING_DEEPSEA: {
    description: "_Enable usage of @sap/dwc-audit-logger on deepsea_",
    defaultOn: true,
    version: "2024.02",
    reqId: "DW00-7097",
    depFeature: [],
  },
  DWCO_RESILIENCY_GETOBJECT_FORBIDDEN_CASES_ERROR: {
    description: "Get repository objects resilience - reject problematic requests",
    defaultOn: true,
    version: "2025.06",
    reqId: "DW00-10666",
    depFeature: [],
  },
  DWCO_RESILIENCY_GETOBJECT_FORBIDDEN_CASES_LOGGING: {
    description: "Get repository objects resilience - log problematic requests",
    defaultOn: true,
    version: "2023.24",
    reqId: "DW00-8166",
    depFeature: [],
  },
  DWCO_SAC_JUSTASK_NLQ_METADATA: {
    description: "Provide metadata for JustAsk on datasphere models",
    defaultOn: true,
    version: "2025.04",
    reqId: "DS00-2666",
    depFeature: [],
  },
  DWCO_SAC_JUSTASK_NLQ_SAMPLES: {
    description: "Provide samples for JustAsk metadata on datasphere models",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3850",
    depFeature: ["DWCO_SAC_JUSTASK_NLQ_METADATA"],
  },
  DWCO_SAP_PASSPORT: {
    description: "PoC for SAP Passport",
    defaultOn: true,
    version: "2025.01",
    reqId: "DW00-5625",
  },
  DWCO_SPACES_HDI_MAPPING: {
    description: "Include mapping of the customer HANA Cloud instance to customer's CloudFoundry Space",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4993",
    depFeature: [],
  },
  DWCO_SPACES_LOGICAL_DELETE: {
    description: "Backup & Restore: Logical Space Deletion",
    defaultOn: true,
    version: "2024.21",
    reqId: "DS00-1729",
    depFeature: ["DWCO_INFRA_REPOSITORY_LOGICAL_DELETE"],
  },
  DWCO_SPACES_SQL_USER_AUTHENTICATION: {
    description: "JWT/SAML Authentication for SQL User",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-1967",
  },
  DWCO_SPACE_SPARK_MANAGEMENT: {
    description: "[LSA]: Space Management - Spark Applications Management",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-1178",
    depFeature: ["DWCO_LARGE_SYSTEMS"],
  },
  DWCO_SQL_INLINE_VALIDATION: {
    description: "SQL View Editor - Syntax Validation of SQL Query",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4440",
    depFeature: [],
  },
  DWCO_SQL_VIEW_HELPERS: {
    description: "SQL View Helpers within SQL View Editor",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-274",
    depFeature: [],
  },
  DWCO_SUPPORT_ROUTE_HANA_ALERTS: {
    description: "Support Route for HANA Metrics & Alerts",
    defaultOn: true,
    version: "2023.20",
    reqId: "DW00-10586",
    depFeature: [],
  },
  DWCO_TABLE_DELTA_UPSERT_BLOCK_DELTA_BLEEDING: {
    description: "_Focus Scenario Local Table Delta - Block Data Bleeding_",
    defaultOn: true,
    version: "2024.11",
    reqId: "DW00-10035",
    depFeature: [],
  },
  DWCO_TABLE_DELTA_UPSERT_READ_API: {
    description: "Local Table with Delta Capture - Delta Read API",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1520",
    depFeature: [],
  },
  DWCO_TABLE_PARTITIONING_HASH: {
    description: "Local Tables - Hash Partitioning",
    defaultOn: true,
    version: "2025.10",
    reqId: "DS00-341",
    depFeature: [],
  },
  DWCO_TABLE_TECHNICAL_VERSIONS_RESTORE: {
    description: "Basic object versioning : Restore version - Local Tables",
    defaultOn: true,
    version: "2025.05",
    reqId: "DS00-3304",
    depFeature: ["DWCO_MODELING_TECHNICAL_VERSIONS_RESTORE"],
  },
  DWCO_TABLE_USAGE_DESIGNTIME: {
    description: "Local Tables - Usage Tracking in Dynatrace (File Storage and HANA)",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3153",
    depFeature: [],
  },
  DWCO_TABLE_USAGE_RUNTIME: {
    description: "Local Tables - Usage Tracking in Dynatrace (File Storage and HANA)",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3153",
    depFeature: [],
  },
  DWCO_TASKCHAIN_DIGRAM_ORIENTATION_MODE: {
    description: "Task Chain - Diagram orientation mode",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2958",
    depFeature: [],
  },
  DWCO_TASKLOGID_SPARK_RESOURCEID: {
    description: "Storing of Spark ResourceIds for TaskLogIds",
    defaultOn: true,
    version: "2025.06",
    reqId: "DS00-4251",
    depFeature: ["DWCO_LARGE_SYSTEMS"],
  },
  DWCO_TASK_CHAINS_REMOVE_DATA: {
    description: "Task Chains - Remove persisted data as tasks in task chain",
    defaultOn: true,
    version: "2024.19",
    reqId: "DS00-288",
    depFeature: [],
  },
  DWCO_TASK_FRAMEWORK_CANCEL_CHAIN: {
    description: "Task Chains - allow to cancel a complete task chain",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-3209",
    depFeature: [],
  },
  DWCO_TASK_FRAMEWORK_CHAIN_PARAMETERS: {
    description: "Task Framework Parameters - enable input parameters in (nested) task chains",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-907",
    depFeature: [],
  },
  DWCO_TASK_FRAMEWORK_DEEP_RETRY: {
    description: "Task chains: Adjust the restart feature to nested task chains Deep Retry",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-3394",
    depFeature: [],
  },
  DWCO_TASK_FRAMEWORK_ERROR_PORTS: {
    description: "Error handling (ports) in Task Chains",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-3905",
    depFeature: [],
  },
  DWCO_TASK_FRAMEWORK_FIVE_MIN_SCHED: {
    description: "Increase scheduling interval from 10m to 5m",
    defaultOn: true,
    version: "2025.11",
    reqId: "DS00-4892",
    depFeature: [],
  },
  DWCO_TASK_FRAMEWORK_LOCAL_TIME_ZONES: {
    description: "Task Framework: Enable to schedule tasks in local time zones - including daylight saving",
    defaultOn: true,
    version: "2025.06",
    reqId: "DS00-3213",
    depFeature: [],
  },
  DWCO_TASK_FRAMEWORK_NOTIFICATION_TASK: {
    description: "Task Framework - notification task",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4716",
    depFeature: [],
  },
  DWCO_TASK_FRAMEWORK_SHARE_TASK_CHAINS: {
    description: "Allow sharing Task Chains and consuming shared chains (as child tasks) from other spaces",
    defaultOn: true,
    version: "2024.23",
    reqId: "DS00-392",
    depFeature: [],
  },
  DWCO_TABLE_HANA_INDEX_TYPE: {
    description: "Local Tables - setting primary key index type as expert option",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4968",
    depFeature: ["DWCO_TABLE_PARTITIONING_HASH"],
  },
  DWCO_TABLE_INTERFACE: {
    description: "Local Table - stable read and write interface",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-6958",
  },
  DWCO_TF_ODC_TASKS: {
    description: "Use DWC task framework for One Data Catalog pipeline jobs",
    defaultOn: true,
    version: "2025.10",
    reqId: "DW00-7898",
  },
  DWCO_TASK_FRAMEWORK_TUP: {
    description: "Datasphere CLI for tasks - TUP OAuth Clients adoption",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5252",
    depFeature: ["DWCO_API_TUP_SUPPORT"],
  },
  DWCO_TENANT_MAPPING_DSP_SAC_INTEGRATION: {
    description: "Implement DSP support for Integration with SAP Analytics Cloud formation",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5133",
    depFeature: [],
  },
  DWCO_TENANT_UPGRADE_ZERO_DOWNTIME: {
    description: "[TUw/oD]Turn-off tenant upgrade blocker",
    defaultOn: true,
    version: "2024.18",
    reqId: "DS00-714",
    depFeature: [],
  },
  DWCO_TRANSFORMATION_FLOW_EXPLAIN_PLAN: {
    description: "Generation of Hana ExplainPlan for Transformation Flow with Hana runtime",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-3053",
    depFeature: ["DWCO_TRANSFORMATION_FLOW_PLAN_VIZ"],
  },
  DWCO_TRANSFORMATION_FLOW_PLAN_VIZ: {
    description: "Enable generation of PlanViz files during TrF execution",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-1833",
    depFeature: [],
  },
  DWCO_TRF_BATCHES: {
    description: "Transformation Flow - Processing in Batches (HANA runtime)",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5041",
    depFeature: [],
  },
  DWCO_TRF_BLOCK_DYN_DAC: {
    description: "Transformation Flow [Technical Debt]: Block transformation SQL script",
    defaultOn: true,
    version: "2025.06",
    reqId: "DS00-3856",
    depFeature: ["DWCO_VIEW_PERSISTENCY_BLOCK_DYN_DAC"],
  },
  DWCO_TRF_CLI_VAL: {
    description: "ValidationSupport for Data Flow and Transformation Flow CRUD and Import/Export CLI operations",
    defaultOn: true,
    version: "2024.07",
    reqId: "DS00-1050",
    depFeature: [],
  },
  DWCO_TRF_COL_LINEAGE: {
    description: "Column Lineage in Transformation Flows",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-525",
    depFeature: [],
  },
  DWCO_TRF_DPREVIEW_SQLSCRIPT: {
    description: "TrF - Data preview for sql script operator.",
    defaultOn: true,
    version: "2025.03",
    reqId: "DS00-2655",
    depFeature: [],
  },
  DWCO_TRF_EDITOR_MSG_PROPAGATION: {
    description:
      "Transformation Flow - Propagation of validations and change management messages from view transform (secondary editor) into primary editor",
    defaultOn: true,
    version: "2024.01",
    reqId: "DW00-10965",
    depFeature: [],
  },
  DWCO_TRF_HANA_LTF_SOURCE: {
    description: "Trf consuming Local Tables with File Storage (LTF) in HANA runtime",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-1664",
    depFeature: ["DWCO_LARGE_SYSTEMS"],
  },
  DWCO_TRF_INPUT_PARAMETERS_SUPPORT: {
    description: "Parameter Support in Transformation Flows for direct and taskchain executions",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2411",
    depFeature: ["DWCO_TASK_FRAMEWORK_CHAIN_PARAMETERS"],
  },
  DWCO_TRF_RT_DATA_TO_GLOBAL: {
    description: "Transformation Flow [Technical Debt]: Move Transformation flow runtime data table",
    defaultOn: true,
    version: "2025.08",
    reqId: "DS00-4813",
    depFeature: [],
  },
  DWCO_TRF_SPARK_AGGR: {
    description: "TrF - Support aggregation SUM, COUNT and LAST in delta mode in Spark",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4364",
    depFeature: [],
  },
  DWCO_TRF_SPARK_CUST_CONFIG: {
    description: "Enable TrF(Spark) to run with custom spark configs for troubleshooting purpose",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4353",
    depFeature: [],
  },
  DWCO_TRF_SPARK_DEPLOY: {
    description:
      "Enable TrF to be deployed with spark as runtime and create required artifacts for spark job execution",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-1632",
    depFeature: ["DWCO_LARGE_SYSTEMS"],
  },
  DWCO_TRF_SPARK_PRI_ED_DND: {
    description: "Supporting drag and drop of tables in Transformation Flow (Spark) primary editor",
    defaultOn: true,
    version: "2025.12",
    reqId: "DS00-4383",
    depFeature: [],
  },
  DWCO_TRF_SPARK_PYTHON: {
    description: "Support Python Operator in TrF with Spark runtime",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-3015",
    depFeature: ["DWCO_TRF_SPARK_EDITOR_MONITORING"],
  },
  DWCO_TRF_SPARK_EDITOR_MONITORING: {
    description: "Enable TrF Editor and monitoring screens to support Spark as runtime",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-1631",
    depFeature: ["DWCO_LARGE_SYSTEMS"],
  },
  DWCO_TRF_SPARK_PY_LIBS: {
    description: "TrF (Spark) Python Operator to support additional libraries",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-4373",
    depFeature: ["DWCO_TRF_SPARK_EDITOR_MONITORING"],
  },
  DWCO_TRF_SPARK_PY_USER_DEFINED_BATCHES: {
    description: "Support User-Defined Batch Processing in TrF (Spark) Python Operator",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4375",
    depFeature: [],
  },
  DWCO_TRF_SPARK_ROLLBACK: {
    description: "Support rollback during cancel Run in TrF (Spark)",
    defaultOn: true,
    version: "2025.07",
    reqId: "DS00-4357",
    depFeature: ["DWCO_LARGE_SYSTEMS"],
  },
  DWCO_TRF_SPARK_RUN: {
    description: "Support TrF execution with Spark as runtime",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-1634",
    depFeature: ["DWCO_LARGE_SYSTEMS"],
  },
  DWCO_UCL_CONNECTION_SPACE_VISIBILITY: {
    description: "[UCL] controling access to Shared UCL connections",
    defaultOn: true,
    version: "2024.11",
    reqId: "DW00-11496",
    depFeature: [],
  },
  DWCO_TRF_SPARK_SHARED_HANA_REMOTE_DELTA_SHARE_SRC: {
    description: "TrF (Spark) - Enable Local Table(Hana) and Remote Table (Delta shares) as sources for TrF",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4419",
    depFeature: [],
  },
  DWCO_TUP_OAUTH_CLIENT_ADOPTION: {
    description: "Allowlist for DSP endpoints exposed to TUP (Technical User Purpose) OAuth Client",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5004",
    depFeature: [],
  },
  DWCO_UCL_CONNECTION_LIFECYCLE: {
    description: "[UCL] life cycle support for UCL Connections",
    defaultOn: true,
    version: "2024.11",
    reqId: "DS00-902",
    depFeature: [],
  },
  DWCO_UCL_INTEGRATION_TRANSPORT: {
    description: "[UCL] transport of Space UCL connections",
    defaultOn: true,
    version: "2024.11",
    reqId: "DS00-1180",
    depFeature: [],
  },
  DWCO_UCL_JOULE: {
    description: "Activates tenant mapping support for Joule formations (Joule provisioning)",
    defaultOn: false,
    version: "2025.01",
    reqId: "DS00-3560",
    depFeature: [],
  },
  DWCO_UCL_POC: {
    description: "[UCL] PoC for integration with Unified Customer Landscape",
    defaultOn: false,
    version: "9999.99",
    reqId: "DW00-10490",
    depFeature: [],
  },
  DWCO_USER_AUTHORIZATION_AGENT_ADOPTION: {
    description: "UMS Authorization Agent Adoption",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4470",
    depFeature: [],
  },
  DWCO_USER_PROPAGATION: {
    description: "Support User Propagation - Main Feature Flag",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2236",
    depFeature: [],
  },
  DWCO_UCL_TENANT_MAPPING_S4_PCE: {
    description: "[BDC] support UCL tenant mapping between BDC-DSP cockpit and S/4 PCE",
    defaultOn: true,
    version: "2024.22",
    reqId: "DS00-3326",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_UCL_TENANT_MAPPING_SAC_WITH_BDC_AND_DSP: {
    description: "[BDC GA] Support Tenant Mapping between BDC and DSP with SAC ",
    defaultOn: true,
    version: "2025.02",
    reqId: "DS00-3919",
    depFeature: ["DWCO_UCL_TENANT_MAPPING_BDC_TENANT"],
  },
  DWCO_UCL_TM_HANA_CLOUD: {
    description: "[UCL] Support Hana Cloud SQL Connection in Tenant Mapping",
    defaultOn: true,
    version: "2024.25",
    reqId: "DS00-2034",
    depFeature: [],
  },
  DWCO_UCL_TENANT_MAPPING_BDC_CDP: {
    description: "[BDC Post GA] UCL Tenant Mapping for CDP Integration",
    defaultOn: true,
    version: "2025.10",
    reqId: "DS00-4825",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_UCL_TENANT_MAPPING_BDC_DSP_BROWNFIELD_DBX: {
    description: "Enable tenant mapping between DBX brownfield tenants and BDC Cockpit",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5264",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_UCL_TENANT_MAPPING_BDC_DSP_HCM: {
    description: "UCL Tenant Mapping for BDC Integration with HCM",
    defaultOn: true,
    version: "2025.12",
    reqId: "DS00-4790",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_UCL_TENANT_MAPPING_BDC_DSP_S4_PUBLIC_CLOUD: {
    description: "UCL Tenant Mapping for S4 HANA Public Cloud",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-5158",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_UCL_TENANT_MAPPING_BDC_TENANT: {
    description: "UCL tenant mapping API for BDC tenants",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-3543",
    depFeature: ["DWCO_BDC_GA"],
  },
  DWCO_UCL_TENANT_MAPPING_DATA_CONNECT: {
    description: "[BDC] support UCL tenant mapping between BDC-DSP cockpit and Data Connect Service for Databricks",
    defaultOn: true,
    version: "2024.21",
    reqId: "DS00-3247",
    depFeature: ["DWCO_BDC"],
  },
  DWCO_UNIVERSAL_MODELS: {
    description: "LCS : INA gateway support for the consumption of repo based Universal models",
    defaultOn: true,
    version: "2024.06",
    reqId: "DW00-10462",
    depFeature: [],
  },
  DWCO_USER_PROPAGATION_CONFIG_TOKEN_EXCHANGER: {
    description: "S/4 user propagation - configure the HANA token exchanger",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-1728",
    depFeature: ["DWCO_USER_PROPAGATION"],
  },
  DWCO_USER_PROPAGATION_SET_SESSION_VARIABLE: {
    description: "S/4 user propagation - pass IAS token to HANA",
    defaultOn: true,
    version: "2025.01",
    reqId: "DS00-1726",
    depFeature: ["DWCO_USER_PROPAGATION"],
  },
  DWCO_VIEW_MONITOR_IMPROVEMENTS: {
    description: "View Monitoring Improvements",
    defaultOn: true,
    version: "2025.04",
    reqId: "DS00-289",
    depFeature: [],
  },
  DWCO_VIEW_PERSISTENCY_ADVISE_ON_OOM_ERRORS: {
    description: "View Persistency: Advise on OOM Errors (Resiliency)",
    defaultOn: true,
    version: "2025.10",
    reqId: "DS00-4038",
    depFeature: [],
  },
  DWCO_VIEW_PERSISTENCY_BLOCK_DYN_DAC: {
    description: "View Persistency [Technical Debt]: Block view persistency SQL script",
    defaultOn: true,
    version: "2025.06",
    reqId: "DS00-3854",
    depFeature: ["DWCO_DAC_SF_STATIC_TO_DYNAMIC"],
  },
  DWCO_VIEW_PERSISTENCY_NO_REPOSITORY: {
    description: "View Persistency: Enable View Monitor when Repository is down (Resiliency+Performance)",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4040",
    depFeature: [],
  },
  DWCO_VIEW_PERSISTENCY_PARTITIONING: {
    description: "View Cache - Partitioned Execution",
    defaultOn: true,
    version: "2021.22",
    reqId: "DW00-3380",
  },
  DWCO_REPOSITORY_DYNATRACE_CUSTOM_METRIC: {
    description: "Dynatrace: Add custom metric for repository requests",
    defaultOn: true,
    version: "2022.17",
    reqId: "DW00-6680",
  },
  DWCO_VIEW_PERSISTENCY_REMOVE_DAC_JOIN_TUDF: {
    description: "View Persistence - Remove Support for old DAC-Types based on JOIN to TUDFs (single-value DAC)",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-2247",
    depFeature: ["DWCO_DAC_VALUES_STRUCTURED_FILTER"],
  },
  DWCO_VIEW_RUNTIME_METRICS: {
    description: "View Analyzer - Runtime Metrics for Views",
    defaultOn: true,
    version: "2025.09",
    reqId: "DS00-4366",
    depFeature: [],
  },
  DWCO_WORKLOAD_MANAGEMENT_GROUPS: {
    description: "Customer HC: Assign resources based on Workload Groups",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-1029",
    depFeature: [],
  },
  DWCO_WORKLOAD_MANAGEMENT_UI: {
    description: "Workload Management UI",
    defaultOn: true,
    version: "2024.18",
    reqId: "DS00-1028",
    depFeature: [],
  },
  DWCO_X509_AUTH_USER: {
    description: "Support OpenSQL x.509 Authentication",
    defaultOn: false,
    version: "9999.99",
    reqId: "DS00-4988",
    depFeature: [],
  },
};
