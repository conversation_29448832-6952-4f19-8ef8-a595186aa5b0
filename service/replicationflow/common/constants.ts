/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { IOptions } from "@sap/dwc-circuit-breaker";
import { TaskFrameworkConstants } from "../../task/shared/db/TaskFrameworkConstants";

export const SERVICE_TO_DI_TIMEOUT = 240000;

export const RUN_GRAPH_TIMEOUT = 120000;

export const fetchDIStatusTimer = 1 * 60 * 1000;

export const autoRestartTimer = 30 * 1000; // 30 seconds

export const RUNTIME_INSTANCES_DEFAULT_LIMIT = 100;

export const retryTimer = 1 * 60 * 1000; // 1 minute

export const timeoutTimer = 10 * 60 * 60 * 1000; // 10 hours

export const CONCURRENT_PROMISES = 4;

export const LONG_POLL_FREQUENCY_DIVIDER_MINUTES = 10; // 10 mins
export const SHORT_POLL_FREQUENCY_DIVIDER_MINUTES = 2; // 2 mins

export const DIG_DELETE_CHECK_TIMER = 60 * 1000;

export const DB_WRITE_RETRY_DELAY = 10 * 1000; // 10 secs

export const DB_WRITE_RETRY_COUNT = 3;

export const circuitBreakerContext: IOptions = {
  volumeThreshold: 2,
};

export const RF_REQUEST_TIMEOUT_MS: number = 50 * 1000;

export const RF_REPO_KIND = "sap.dis.replicationflow";
export const ABAP_CLUSTER_TABLE = "CLUSTER";

export const DEPLOYED_METADATA = "DEPLOYED_METADATA";
export const DELTA_PROVIDER_SUBSCRIBER = "DELTA_PROVIDER_SUBSCRIBER";
export const BDC_CONNECTION_TYPE = "BUSINESS_DATA_PRODUCT";

export enum DIG_REPLICATION_DATASET_STATUS {
  CREATED = "CREATED",
  INITIAL_RUNNING = "INITIAL_RUNNING",
  DELTA_RUNNING = "DELTA_RUNNING",
  RUNNING = "RUNNING",
  SUSPENDING = "SUSPENDING",
  SUSPENDED = "SUSPENDED",
  RETRYING = "RETRYING",
  ERROR = "ERROR",
  COMPLETED = "COMPLETED",
  DELETING = "DELETING",
  RESTARTING = "RESTARTING",
  DELTA_RETRYING = "DELTA_RETRYING",
  INITIAL_RETRYING = "INITIAL_RETRYING",
}

export enum DIG_REPLICATION_FLOW_STATUS {
  SUSPENDED = "SUSPENDED",
  DELETING = "DELETING",
}

export enum DIG_LOAD_TYPE {
  INITIAL = "INITIAL",
  REPLICATE = "REPLICATE",
  DELTA = "DELTA",
  SAP_BO_EVENT_REPLICATE = "SAP_BO_EVENT_REPLICATE",
}

export const DELTA_LOAD_TYPES = [DIG_LOAD_TYPE.REPLICATE, DIG_LOAD_TYPE.DELTA];

export enum METHODS {
  POST = "POST",
  GET = "GET",
  PUT = "PUT",
  DELETE = "DELETE",
  PATCH = "PATCH",
}

export enum RESPONSE_MSG {
  SUCCESS_REPLICATION_FLOW_DATASET_TERMINATED = "REPLICATION_FLOW_TASK_STOPPED_SUCCESSFULLY",
  ERROR_REPLICATION_FLOW_DATASET_NOT_FOUND = "ERROR_DATASET_NOT_FOUND",
  ERROR_RUNNING_TASK_NOT_FOUND = "REPLICATION_FLOW_TASK_NOT_FOUND",
  ERROR_REPLICATION_FLOW_PAUSE_TRIGGERED_ON_SCHEDULE = "ERROR_REPLICATION_FLOW_PAUSE_TRIGGERED_ON_SCHEDULE",
  ERROR_DATASET_PAUSE_TRIGGERED_LAST_DATASET_ON_SCHEDULE = "ERROR_DATASET_PAUSE_TRIGGERED_LAST_DATASET_ON_SCHEDULE",
  ERROR_REPLICATION_FLOW_PAUSE_TRIGGERED_ON_DATASET_RESTARTING = "ERROR_REPLICATION_FLOW_PAUSE_TRIGGERED_ON_DATASET_RESTARTING",
  ERROR_REPLICATION_FLOW_STOP_TRIGGERED_ON_DATASET_RESTARTING = "ERROR_REPLICATION_FLOW_STOP_TRIGGERED_ON_DATASET_RESTARTING",
  ERROR_DATASET_PAUSE_TRIGGERED_ON_RESTARTING = "ERROR_DATASET_PAUSE_TRIGGERED_ON_RESTARTING",
  ERROR_DATASET_RESTART_TRIGGERED_ON_RESTARTING = "ERROR_DATASET_RESTART_TRIGGERED_ON_RESTARTING",
  ERROR_DATASET_RESUME_TRIGGERED_ON_RESTARTING = "ERROR_DATASET_RESUME_TRIGGERED_ON_RESTARTING",
  ERROR_REPLICATION_FLOW_STOP_TRIGGERED_DATASET_NOT_DELETING = "ERROR_REPLICATION_FLOW_STOP_TRIGGERED_DATASET_NOT_DELETING",
  ERROR_REPLICATION_FLOW_STOP_TRIGGERED_STOPPING = "ERROR_REPLICATION_FLOW_STOP_TRIGGERED_STOPPING",
}

export enum LOG_MSG {
  FLOW_COUNTER_EXHAUSTED = "GET_RETRY_EXHAUSTED_FOR_FLOW_DELETE",
  TASKS_COUNTER_EXHAUSTED = "GET_RETRY_EXHAUSTED_FOR_TASKS_DELETE",
  DIG_FLOW_DELETION_SUCCESSFUL = "DIG_FLOW_DELETION_SUCCESSFUL",
  DIG_FLOW_DELETION_UNSUCCESSFUL = "DIG_FLOW_DELETION_UNSUCCESSFUL",
  DIG_TASKS_DELETION_UNSUCCESSFUL = "DIG_TASKS_DELETION_UNSUCCESSFUL",
}

export const enum LOG_TYPE {
  MESSAGE = "Message",
  ERROR = "Error",
}

export enum USAGE_MSG {
  LIMITING_SERVICE_UNAVAILABLE = "LIMITING_SERVICE_UNAVAILABLE",
  LIMITING_TASK_ERROR = "The run has been stopped because there is no outbound volume for non-SAP target connections available for this month. \n An administrator can increase the Premium Outbound blocks for this tenant, making outbound volume available for this month.",
  LIMITING_USAGE_LOG = "Limiting usage has reached. Stopping all the replication flows for premium outbound connections.",
}

export enum TASK_LOG_MSG {
  DELETING_PREVIOUS_RMS_TASKS = "Deleting previously replicated objects",
  DEPLOYING_DATASET = "Deploying datasets in runtime",
  RF_WITH_NO_DATASETS = "Replication Flow does not have any datasets",
  DELETION_NOT_COMPLETED = "Object replications failed because maximum number of deletion retries reached. \n You can check the availability of the target system and either restart the objects or start a new run.",
  STOP_SUCCESS = "Replication task cleanup completed",
}

export enum RF_I18_MSG {
  MSG_BUNDLE_ID = "i18n_rf",
  TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY = "TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY",
  TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF = "TASKCHAIN_DELTA_REPLICATIONFLOW_NO_SUPPORT_CURRENTLY_DELTA_OFF",
}

export enum OBJECT_STORE_FILE_FORMAT {
  PARQUET = "PARQUET",
}
export enum OBJECT_STORE_PROPERTIES {
  APACHE_SPARK_COMPATIBILITY_MODE_TRUE = "SPARK",
  APACHE_SPARK_COMPATIBILITY_MODE_FALSE = "NONE",
  USE_DUPLICATE_SUPPRESSION_INITIAL_LOAD_TRUE = "true",
  USE_DUPLICATE_SUPPRESSION_INITIAL_LOAD_FALSE = "false",
}

export enum HDLF_CONN {
  CONNECTION_ID_DWC = "$DWC",
  CONNECTION_TYPE_HDLF = "HDL_FILES",
  CONNECTION_TYPE_HANA = "HANA",
  CONST_PARTIAL_CONTAINER_PATH = "_SYS_SAP_DSP_HANA_REMOTE_SOURCES", // The constant part of the path for HDLF Deltashare
}

export enum DELTA_PARTITION {
  DELTA_PARTITION_MAX_VALUE = 10,
  DELTA_PARTITION_MIN_VALUE = 1,
}

export enum CONTAINERS {
  CDS_CONTAINER = "/CDS",
  CDS_EXTRACTION = "/CDS_EXTRACTION",
  ODP = "/ODP",
  SLT = "/SLT",
}

export enum ABAP_DPID_COLUMN {
  COL_NAME_FOR_HANA_TARGETS = "__load_package_id",
  DATATYPE_FOR_HANA_TARGETS = "binary",
  LENGTH_FOR_HANA_TARGETS = 256,
  COL_NAME_FOR_NON_ABAP_TARGETS = "__load_record_id",
  DATATYPE_FOR_NON_ABAP_TARGETS = "string",
  LENGTH_FOR_NON_ABAP_TARGETS = 44,
}

export enum NON_HANA_TARGETS {
  S3 = "S3",
  GCS = "GCS",
  ADL = "ADL",
  ADL_GEN1 = "ADL_GEN1", // ?
  ADL_GEN2 = "ADL_GEN2", // ?
  HDL = "HDL",
  HDL_FILES = "HDL_FILES",
  GBQ = "BIGQUERY",
  CONFLUENT = "CONFLUENT",
}

export enum DWC_CONN {
  CONNECTION_ID_DWC = "$DWC",
  CONNECTION_TYPE_HANA = "HANA",
}

export enum LTF_CONN {
  CONNECTION_ID_DWC_HDLF = "DWC_HDLF",
  CONNECTION_TYPE_HDL_FILES = "HDL_FILES",
  INBOUND_BUFFER_TABLE_POSTFIX = "/_inbound_buffer",
}

export enum ABAP_CONN_TYPES {
  SAPS4HANAOP = "SAPS4HANAOP",
  SAPS4HANACLOUD = "SAPS4HANACLOUD",
  ABAP = "ABAP",
}

export const SOURCE_OBJECT_TYPE = {
  TABLE: "TABLE",
  VIEW: "VIEW",
};

export const CFW_NODE_TYPE = {
  // ToDo : Tentative names derived from https://github.tools.sap/connectivity-services/connectivity-framework/blob/d61aa3034c3d0542f3563b93bbed51c6e1559746/proto/connectivity/connector/v1/metadata.proto
  NODE_TYPE_UNSPECIFIED: "UNSPECIFIED",
  NODE_TYPE_TABLE: "TABLE",
  NODE_TYPE_VIEW: "VIEW",
  NODE_TYPE_FILE: "FILE",
  NODE_TYPE_OBJECT: "OBJECT" /* General object */,
  NODE_TYPE_CONTAINER: "CONTAINER" /* i.e. schema (DB) or folder/bucket (file system) */,
  NODE_TYPE_FUNCTION: "FUNCTION",
  NODE_TYPE_UNBOUND:
    "UNBOUND" /* an object that only delivers an endless stream of data and does not allow bound access */,
};

export const ARROW_TO_VTYPE = {
  //Reference https://jira.tools.sap/browse/DS00-400
  ARROW_TYPE_UINT8: "uint8",
  ARROW_TYPE_INT16: "int16",
  ARROW_TYPE_INT32: "int32",
  ARROW_TYPE_INT64: "int64",
  ARROW_TYPE_BOOL: "bool",
  ARROW_TYPE_FLOAT32: "float32",
  ARROW_TYPE_FLOAT64: "float64",
  float32: "float32",
  float64: "float64",
  ARROW_TYPE_BINARY: "binary",
  ARROW_TYPE_BOOLEAN: "bool",
  ARROW_TYPE_UTF8: "string",
  ARROW_TYPE_FIXEDSIZEBINARY: "binary",
  ARROW_TYPE_DECIMAL128: "decimal",
  ARROW_TYPE_DECIMAL256: "decimal",
  ARROW_TYPE_DATEDAY: "date",
  ARROW_TYPE_TIMEMILLI: "time",
  ARROW_TYPE_TIMEMICRO: "time",
  ARROW_TYPE_TIMESTAMPMILLITZ: "timestamp",
  ARROW_TYPE_TIMESTAMPMICROTZ: "timestamp",
  ARROW_TYPE_TIMESTAMPMILLI: "timestamp",
  ARROW_TYPE_TIMESTAMPMICRO: "timestamp",
  ARROW_TYPE_TIMESTAMPNANOTZ: "timestamp",
  ARROW_TYPE_EXTENSIONTYPE_SAP_100NS_TIMESTAMP: "timestamp",
  ARROW_TYPE_TIMESTAMPSEC: "timestamp",
  ARROW_TYPE_TIMENANO: "time",
};

export enum OBJECTSTORE_CONNECTION_TYPES {
  S3 = "S3", // Amazon Simple Storage Service
  GCS = "GCS", // Google Cloud Storage
  ADL = "ADL", // Azure Data Lake
  ADL_GEN1 = "ADL_GEN1", // Azure Data Lake Generation 1
  ADL_GEN2 = "ADL_GEN2", // Azure Data Lake Generation 2
  HDL = "HDL",
  HDL_FILES = "HDL_FILES",
}

export enum OBJECTSTORE_SOURCE_PROPERTY {
  maxPartitionsLowerLimit = 1,
  maxPartitionsUpperLimit = 2147483647,
}

export enum OBJECTSTORE_SOURCE_CSV_ENCODING {
  UTF8 = "UTF-8",
  UTF16 = "UTF-16",
  ISO_8859_1 = "ISO-8859-1",
}

export enum HanaSQLErrorCode {
  TOO_MANY_VALUES_ERR_CODE = 269,
}

export const SIGNAVIO_CONNECTION_TYPE = "SIGNAVIO";
export const MSSQL_CONNECTION_TYPE = "MSSQL";
export const MSONELAKE_CONNECTION_TYPE = "ONELAKE";
export const SFTP_CONNECTION_TYPE = "SFTP";

export const BDC_RELEVANT = "BDC_Relevant";

export const DEPLOYMENT_IN_PROGRESS_WAIT_TIME = 3000;

export const DELTA_COLUMN_NAMES = {
  modeElement: "com.sap.datasuite.cdc.mode.column",
  dateTimeElement: "com.sap.datasuite.cdc.timestamp.column",
};

export const DELTA_TABLE_CATEGORY = {
  key: "com.sap.datasuite.tableCategory",
  value: "DELTA",
};

export enum PREMIUM_OUTBOUND_RELEVANT {
  OFF = "OFF",
  ON = "ON",
}

export const NOTIFICATION_MAILING_RUN_CONFIG_TB = "NOTIFICATION_MAILING_RUN_CONFIG";
export const NOTIFICATION_MAILING_LISTS_TB = TaskFrameworkConstants.taskChainNotificationMailingTableName;

export const METRIC_CONNECTION_TYPE_SEPARATOR = ":;";
