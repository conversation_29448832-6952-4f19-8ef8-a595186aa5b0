/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { escapeDoubleQuotes } from "@sap/prom-hana-client";
import { StatusCodes } from "http-status-codes";
import { DbClient } from "../../lib/DbClient";
import { getLogger } from "../../logger";
import { IRequestContext } from "../../repository/security/common/common";
import { CustomerHana } from "../../reuseComponents/spaces/src/CustomerHana";
import { doesTableExist } from "../../reuseComponents/systemTablesDeployment/src/helper";
import { CodedError } from "../../server/errorResponse";
import { delay } from "./commonUtil";
import { DB_WRITE_RETRY_COUNT, DB_WRITE_RETRY_DELAY, HanaSQLErrorCode } from "./constants";

const { logError, logWarning } = getLogger("RunDetailUtil");

export interface IReplicationFlowRunDetails {
  TASK_LOG_ID: number;
  DATASET_EXTERNAL_ID?: string;
  DATASET_TASK_LOG_ID?: number;
  NAME?: string;
  FLOW_EXTERNAL_ID?: string;
  DATASET_EXTERNAL_STATUS?: string;
  UPDATED_AT?: Date;
  DETAILS?: any;
  LAST_DATA_TRANSFERRED_AT?: string;
  SOURCE_OBJECT_NAME?: string;
  TARGET_OBJECT_NAME?: string;
  SOURCE_CONNECTION_NAME?: string;
  TARGET_CONNECTION_NAME?: string;
  LOAD_TYPE?: string;
}
export const replicationFlowRunDetailsTableName = "REPLICATIONFLOW_RUN_DETAILS";

export class RunDetailUtil {
  constructor(
    private readonly requestContext: IRequestContext,
    private readonly spaceName: string,
    private replicationFlowRunDetails: IReplicationFlowRunDetails[],
    private isTerminating?: boolean
  ) {
    this.requestContext = requestContext;
    this.spaceName = spaceName;
    this.replicationFlowRunDetails = replicationFlowRunDetails;
    this.isTerminating = isTerminating;
  }

  private async getDbClient(): Promise<DbClient> {
    const customerHana = await CustomerHana.fromRequestContext(this.requestContext);
    return await customerHana.getSpaceOwnerClient(this.spaceName, {
      exclusive: true,
      preventAutoClose: true,
    });
  }

  private async getTecSchemaName() {
    const customerHana = await CustomerHana.fromRequestContext(this.requestContext);
    const customerHanaSpace = customerHana.selectSpace(this.spaceName);
    return (await customerHanaSpace.getSchemaNames()).spc_tec_internal;
  }

  public async getReplicationflowDetailsByTaskLogId(): Promise<IReplicationFlowRunDetails[] | undefined> {
    let dbClient: DbClient | undefined;
    try {
      if (this.replicationFlowRunDetails?.length) {
        const taskIdList = this.replicationFlowRunDetails.map((rfRunDetail) => rfRunDetail.TASK_LOG_ID).join(",");
        dbClient = await this.getDbClient();
        const tecSchemaName = await this.getTecSchemaName();
        const selectQuery = `SELECT * FROM "${escapeDoubleQuotes(
          tecSchemaName
        )}"."${replicationFlowRunDetailsTableName}"
        WHERE TASK_LOG_ID IN (?)`;
        // Check if table exists
        const tableExist = await doesTableExist(tecSchemaName, replicationFlowRunDetailsTableName, dbClient);
        if (tableExist) {
          const result = (await dbClient.exec(selectQuery, [taskIdList])) as IReplicationFlowRunDetails[];
          return result;
        }
      }
    } catch (error) {
      logError(error, { context: dbClient?.context });
      throw error;
    } finally {
      await dbClient?.close();
    }
    return undefined;
  }

  public async upsertReplicationFlowRunDetails(): Promise<boolean> {
    if (this.replicationFlowRunDetails?.length > 0) {
      const rfWithMissingDetails = this.replicationFlowRunDetails.filter((x) => !x.DETAILS);
      if (rfWithMissingDetails.length > 0) {
        const errMessage = "DETAILS object missing for this upsert operation";
        throw new CodedError("jsonObjectMissing", errMessage, StatusCodes.BAD_REQUEST);
      }
      // prepare the table details & db client
      const tecSchemaName = await this.getTecSchemaName();
      const dbClient = await this.getDbClient();
      const upsertQuery = `UPSERT "${escapeDoubleQuotes(tecSchemaName)}"."${replicationFlowRunDetailsTableName}"
        VALUES(?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, TO_NCLOB(?), ?, ?, ?, ?, ?, ?) WITH PRIMARY KEY`;
      const values = this.replicationFlowRunDetails.map((rfDetail) => [
        rfDetail.TASK_LOG_ID ?? null,
        rfDetail.DATASET_EXTERNAL_ID ?? null,
        rfDetail.DATASET_TASK_LOG_ID ?? null,
        rfDetail.NAME ?? null,
        rfDetail.FLOW_EXTERNAL_ID ?? null,
        rfDetail.DATASET_EXTERNAL_STATUS ?? null,
        JSON.stringify(rfDetail.DETAILS) ?? null,
        rfDetail.LAST_DATA_TRANSFERRED_AT ?? null,
        rfDetail.SOURCE_OBJECT_NAME ?? null,
        rfDetail.TARGET_OBJECT_NAME ?? null,
        rfDetail.SOURCE_CONNECTION_NAME ?? null,
        rfDetail.TARGET_CONNECTION_NAME ?? null,
        rfDetail.LOAD_TYPE ?? null,
      ]);
      let counter = 1;
      let resultStatus = false;
      while (counter <= DB_WRITE_RETRY_COUNT) {
        try {
          // To-do: Remove this logic after handling this in live patch during rf deployment
          if (this.isTerminating === false && this.replicationFlowRunDetails?.length > 0) {
            await this.performDeleteTask(dbClient, tecSchemaName);
          }
          const prepared = await dbClient.prepare(upsertQuery);
          await prepared.execBatch(values);
          await dbClient.commit();
          await prepared?.drop();
          resultStatus = true;
          logWarning("[upsertReplicationFlowRunDetails] Replication flow run details stored successfully", {
            context: this.requestContext,
          });
          break;
        } catch (error) {
          if (error.code === HanaSQLErrorCode.TOO_MANY_VALUES_ERR_CODE) {
            logWarning(error, { context: dbClient?.context });
            break;
          } else {
            logError(`Retry(${counter}) Error ${error}`, { context: dbClient?.context });
            counter++;
            await delay(DB_WRITE_RETRY_DELAY);
          }
        }
      }
      await dbClient?.close();
      return resultStatus;
    } else {
      const errMessage =
        "[upsertReplicationFlowRunDetails] Replication flow run details are missing for this upsert operation";
      logError(errMessage, { context: this.requestContext });
      return false;
    }
  }

  public async performDeleteTask(dbClient: DbClient, tecSchemaName: string) {
    try {
      const deleteValues = this.replicationFlowRunDetails.map((item) => item.TASK_LOG_ID).join(", ");
      const deleteQuery = `DELETE FROM "${escapeDoubleQuotes(tecSchemaName)}"."${replicationFlowRunDetailsTableName}"
        WHERE "TASK_LOG_ID" IN (${deleteValues});`;
      await dbClient.execute(deleteQuery);
      await dbClient.commit();
    } catch (error) {
      throw new Error(`Failed to delete replication flow run details: ${error}`);
    }
  }
}
