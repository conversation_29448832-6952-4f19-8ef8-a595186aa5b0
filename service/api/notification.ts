/** @format */

// FILEOWNER: [Spaces]
// This file contains stolen code

import type { IMessageList } from "@sap-nekton/core-types";
import { StatusType } from "@sap/deepsea-types";
import { getLogger } from "@sap/dwc-logger";
import { INotification, NotificationLinkType, NotificationType, NotificationsClient } from "@sap/dwc-notifications";
import { FeatureFlagProvider } from "../featureflags/FeatureFlagProvider";
import { ISpaceContentCopyAndDeployResult } from "../repository/api/common/copySpaceContent";
import { IRequestContext } from "../repository/security/common/common";
import type { IDeployResult } from "../reuseComponents/deployment/src";

const { logError } = getLogger("SPACE_DEPLOY_ROUTES_NOTIFICATIONS");

export async function sendNotification(
  context: IRequestContext,
  spaceId: string,
  spaceDeployResult: IDeployResult,
  oldSpaceId?: string,
  copySpaceContentAndDeployResult?: ISpaceContentCopyAndDeployResult
) {
  const spaceDeployHasError = spaceDeployResult?.messages.hasError;
  const userName = context.userInfo.userName!;
  const additionalParameters = {
    status: spaceDeployHasError ? "failure" : "success",
    spaceName: spaceId,
    notificationSource: "deployment",
    results: "TODO3",
  };

  const isSpaceCopyActive = await FeatureFlagProvider.isFeatureActive(context, "DWCO_BDC_SPACE_COPY");

  const title = getTitle(spaceDeployHasError, isSpaceCopyActive, oldSpaceId, copySpaceContentAndDeployResult);
  const body =
    createNotificationBodyFromMessage(context, spaceDeployResult.messages, copySpaceContentAndDeployResult) ??
    "openSpace";

  const notificationInfo: INotification = {
    recipientList: [userName],
    type: spaceDeployHasError ? NotificationType.ALERT : NotificationType.SUCCESS,
    title,
    body,
    linkType: NotificationLinkType.VIEW,
    link: `{"target":{"semanticObject":"managespaces"},"params":{"spaceId":"${spaceId}"}}`,
    para: JSON.stringify(additionalParameters),
    showInShell: false,
    sendAsMail: false,
  };

  if ((await FeatureFlagProvider.isFeatureActive(context, "DWCO_BDC_SPACE_COPY")) && oldSpaceId) {
    notificationInfo.dspTranslationParameters = {
      titleParameters: spaceDeployHasError ? [spaceId] : [oldSpaceId, spaceId],
      bodyParameters: [spaceId],
    };
  } else {
    notificationInfo.dspTranslationParameters = {
      titleParameters: [spaceId],
      bodyParameters: [spaceId],
    };
  }

  try {
    await NotificationsClient.post(context, notificationInfo);
  } catch (error) {
    logError([`sendNotification failed `, error], { context });
  }
}

function createNotificationBodyFromMessage(
  context: IRequestContext,
  messages: IMessageList,
  copySpaceContentAndDeployResult?: ISpaceContentCopyAndDeployResult
): string | undefined {
  let spaceCopyOrDeployHasError = false;
  if (copySpaceContentAndDeployResult) {
    const { spaceContentCopyResult, spaceContentDeployResult } = copySpaceContentAndDeployResult;
    // check if space content copy or deploy has error
    const isCopyFailed = spaceContentCopyResult.status === StatusType.Failed;
    const isDeployFailed = spaceContentDeployResult ? !spaceContentDeployResult.isSpaceContentDeploySuccessful : false;
    spaceCopyOrDeployHasError = isCopyFailed || isDeployFailed;
  }
  if (messages.length > 0 || spaceCopyOrDeployHasError) {
    const messageArray: string[] = [];
    let correlationText = "";
    if (messages.hasError || spaceCopyOrDeployHasError) {
      if (context.correlationId) {
        correlationText = `\n\n Correlation ID: ${context.correlationId}`;
      }
    }
    messageArray.push(...messages.all.map((m) => m.description));
    // enhance the message with space content copy and deploy result messages
    const copySpaceContentAndDeployMsg = getSpaceContentCopyAndDeployMsg(copySpaceContentAndDeployResult);
    const actualMessage = copySpaceContentAndDeployMsg ?? messageArray.join("\n\n");
    // truncating the message body to have 5000 character length, limitation from sac notification service
    return `${actualMessage.substring(0, 5000 - correlationText.length)}${correlationText}`;
  }
}

export function getNotificationLink(spaceId: string, modelName: string, oNotificationInstructions: any) {
  let sNavigationLink;
  if (oNotificationInstructions) {
    // Use dedicated navigation target
    // NOTE: ensure that the target is stringified
    sNavigationLink =
      typeof oNotificationInstructions.navigationTarget === "string"
        ? oNotificationInstructions.navigationTarget
        : JSON.stringify(oNotificationInstructions.navigationTarget);
  } else {
    // Default to data builder object
    sNavigationLink = `{\"target\":{\"semanticObject\":\"databuilder\"},\"params\":{\"spaceId\":\"${spaceId}\",\"model\":\"${modelName}\"}}`;
  }

  return sNavigationLink;
}

/**
 * generate title based on space deploy result and space content copy and deploy result
 * @param spaceDeployHasError - flag to check if space deploy has error
 * @param isSpaceCopyActive - flag to check if space copy FF is enabled
 * @param oldSpaceId - source space id
 * @param copySpaceContentAndDeployResult - Result of space content copy and deploy
 * @returns
 */
function getTitle(
  spaceDeployHasError: boolean,
  isSpaceCopyActive: boolean,
  oldSpaceId?: string,
  copySpaceContentAndDeployResult?: ISpaceContentCopyAndDeployResult
): string {
  // if space deploy failed, show the space deploy error message
  if (spaceDeployHasError) {
    return "spaceDeploymentUnsuccessful";
  }
  // if space copy is enabled and old space id is present, adjust the title based on the space content deployment result or space deployment result
  if (isSpaceCopyActive && oldSpaceId) {
    const spaceContentDeployResult = copySpaceContentAndDeployResult?.spaceContentDeployResult;
    const spaceContentCopyResult = copySpaceContentAndDeployResult?.spaceContentCopyResult;
    // if no space content deploy triggered, check if space content copy was successful
    if (!spaceContentDeployResult) {
      return spaceContentCopyResult?.status === StatusType.Succeed
        ? "spaceContentCopySuccessful"
        : "spaceContentCopyUnsuccessful";
    }
    // if space content deploy was triggered, show the space content deploy result
    return spaceContentDeployResult.isSpaceContentDeploySuccessful
      ? "spaceContentCopyAndDeploymentSuccessful"
      : "spaceContentDeploymentUnsuccessful";
  }
  return "spaceDeploymentSuccessful";
}

/**
 * get message of content copy and deploy if any failed
 * @param copySpaceContentAndDeployResult - Result of space content copy and deploy
 * @returns
 */
function getSpaceContentCopyAndDeployMsg(copySpaceContentAndDeployResult?: ISpaceContentCopyAndDeployResult) {
  const spaceContentCopyMsg =
    copySpaceContentAndDeployResult?.spaceContentCopyResult.status === StatusType.Failed
      ? copySpaceContentAndDeployResult?.spaceContentCopyResult?.message
      : undefined;
  const spaceContentDeployMsg = !copySpaceContentAndDeployResult?.spaceContentDeployResult
    ?.isSpaceContentDeploySuccessful
    ? copySpaceContentAndDeployResult?.spaceContentDeployResult?.spaceContentDeployMessage
    : undefined;
  return spaceContentCopyMsg ?? spaceContentDeployMsg;
}
