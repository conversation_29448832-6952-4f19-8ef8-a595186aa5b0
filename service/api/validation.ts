/** @format */

// FILEOWNER: [Spaces]
/** @format */

import { ISpaceContentCopyParams } from "@sap/deepsea-types";
import { CodedError } from "@sap/dwc-express-utils";
import { getLogger } from "@sap/dwc-logger";
import { ObjectType, validateName } from "@sap/dwc-name-validator";
import Ajv, { ValidateFunction, ValidationError } from "ajv";
import keywords from "ajv-keywords";
import { StatusCodes } from "http-status-codes";
import * as semver from "semver";
import { IUser } from "../../shared/api/ScopedUsers";
import * as API from "../../shared/api/SpaceAPI";
import { logDebug } from "../ecn/utils/LoggerUtils";
import { copySpaceContent } from "../repository/api/common/copySpaceContent";
import { IRequestContext } from "../repository/security/common/common";
import { RequestContext } from "../repository/security/requestContext";
import { getSpaces, getSpacesFromNames } from "../repository/spaces";
import { MAX_LENGTH_SPACENAME, SpaceService } from "../reuseComponents/spaces/src";
import { IUsersRole } from "../routes/space/types";
import {
  crudRole,
  getAllUsersForSdpConversion,
  getInheritance,
  getScopeAssignment,
  retrieveRolesByScope,
} from "../routes/space/user";
import apischema from "./apischema.json";
import idschema from "./apischemaids.json";
import { CLIScopeRoleCreationInfo, transformReqBodyForListRoles, transformReqBodyForReadRole } from "./scopeRole";
import roleAddUserSchema from "./scopeRoleApischema/roleAddUsersApischema.json";
import scopeRoleCreateSchema from "./scopeRoleApischema/scopedRoleCreateSchema.json";
import scopeRoleUpdateSchema from "./scopeRoleApischema/scopedRoleUpdateSchema.json";
import updateUserScopeAssignmentSchema from "./scopeRoleApischema/updateUserScopeAssignmentSchema.json";
import userCreationSchema from "./scopeRoleApischema/userCreationSchema.json";
import userScopeAssignmentSchema from "./scopeRoleApischema/userScopeAssignmentSchema.json";
import userScopeAssignmentSchemaV2 from "./scopeRoleApischema/userScopeAssignmentSchemaV2.json";
import userUpdateSchema from "./scopeRoleApischema/userUpdateSchema.json";
import spaceDeinitionSchema from "./spaceDefinitionSchema.json";
import workloadManagementSchema from "./workloadManagementSchema.json";

const { logErrorWithOptionalContext } = getLogger(__filename);

const ajv = new Ajv({ allErrors: true, strict: false });

ajv.compile(spaceDeinitionSchema);
const validateApiSchema = ajv.compile(apischema);
const validateIdSchema = ajv.compile(idschema);

const validateScopeRoleCreateSchema = ajv.compile(scopeRoleCreateSchema);
const validateScopeRoleUpdateSchema = ajv.compile(scopeRoleUpdateSchema);
const ajvWithRegex = keywords(ajv, "regexp");
const validateUserCreationSchema = ajvWithRegex.compile(userCreationSchema);
const validateUserUpdateSchema = ajvWithRegex.compile(userUpdateSchema);
const validateWorkloadManagementSchema = ajvWithRegex.compile(workloadManagementSchema);
const regexUsername = /^[0-9_\p{L}]{1,20}$/u;

function handleSchemaValidatorError(e: ValidationError) {
  logErrorWithOptionalContext(e.errors.map((err) => `handleSchemaValidatorError ${JSON.stringify(err)}`).join("\n"));
  const message = e.errors.map((err) => `${err.instancePath} ${err.message} ${JSON.stringify(err.params)}`).join("\n");
  throw new CodedError("spaceApiValidation", message, StatusCodes.BAD_REQUEST);
}

async function validateApiJson(json: any) {
  let data;
  try {
    data = await validateApiSchema(json);
  } catch (err) {
    handleSchemaValidatorError(err);
  }
  if (!data) {
    throw new CodedError(
      "spaceApiMissingData",
      `saveAndDeploy validateApiSchema failed ${JSON.stringify(data)}`,
      StatusCodes.BAD_REQUEST
    );
  }
}

async function validateSpaceIds(json: any) {
  for (const [spaceId, content] of Object.entries(json)) {
    let valid = validateName()(ObjectType.Space, spaceId);
    if (valid.status === "ERROR") {
      throw new CodedError(
        "spaceApiBadSpaceId",
        `space id: ${spaceId} is not valid: ${JSON.stringify(valid)}`,
        StatusCodes.BAD_REQUEST
      );
    }
    if ((content as any).spaceDefinition?.dbusers) {
      for (const dbUser of Object.keys((content as any).spaceDefinition?.dbusers)) {
        valid = validateName()(ObjectType.DatabaseUser, dbUser);
        if (valid.status === "ERROR") {
          throw new CodedError(
            "spaceApiBadDbUser",
            `DB user: ${dbUser} in space id: ${spaceId} is not valid: ${JSON.stringify(valid)}`,
            StatusCodes.BAD_REQUEST
          );
        }
      }
    }
    if (
      (content as any).spaceDefinition?.label?.length === 0 ||
      (content as any).spaceDefinition?.label?.length > MAX_LENGTH_SPACENAME
    ) {
      throw new CodedError(
        "spaceApiBadSpaceName",
        `Invalid space name: Can not be empty and can have a maximum of ${MAX_LENGTH_SPACENAME} characters`,
        StatusCodes.BAD_REQUEST
      );
    }
  }
}

async function validateIdJson(json: any) {
  try {
    await validateIdSchema(json);
  } catch (err) {
    handleSchemaValidatorError(err);
  }
}

async function validateAssignedStorageAndRamCombinations(json: API.DataWarehouseCloudAPI) {
  Object.values(json).forEach((space) => {
    const storage = space.spaceDefinition?.assignedStorage;
    const ram = space.spaceDefinition?.assignedRam;
    if (typeof storage === "number" && storage !== 0 && storage < 100000000) {
      throw new CodedError(
        "spaceApiAssignedStorage",
        `assignedStorage should be undefined, 0, or >=100000000, but it is ${storage}`,
        StatusCodes.BAD_REQUEST
      );
    }
    if (typeof ram === "number" && ram < 0) {
      throw new CodedError(
        "spaceApiAssignedStorage",
        `assignedRam should be undefined, 0, or > 0, but it is ${ram}`,
        StatusCodes.BAD_REQUEST
      );
    }
    if (ram && ram > 0 && storage === 0) {
      throw new CodedError(
        "spaceApiAssignedStorage",
        `assignedRam > 0 && assignedStorage === 0 not allowed(assignedRam: ${ram}, assignedStorage: ${storage})`,
        StatusCodes.BAD_REQUEST
      );
    }
  });
  return await Promise.resolve();
}

async function validateWorkloadType(json: API.DataWarehouseCloudAPI) {
  Object.values(json).forEach((space) => {
    const workloadType = space.spaceDefinition?.workloadType;
    if (workloadType && !(workloadType === API.WorkloadType.DEFAULT || workloadType === API.WorkloadType.CUSTOM)) {
      throw new CodedError("spaceApiWorkloadType", `workloadType value is not valid`, StatusCodes.BAD_REQUEST);
    }
  });
  return await Promise.resolve();
}

function validateSpaceApiVersion(json: API.DataWarehouseCloudAPI) {
  let version: string | undefined;
  for (const space of Object.values(json)) {
    if (space.spaceDefinition) {
      if (version === undefined) {
        // first space
        version = space.spaceDefinition.version;
        if (!semver.valid(version)) {
          throw new CodedError(
            "spaceApiVersionWrong",
            `Illegal Space API version: ${version}`,
            StatusCodes.BAD_REQUEST
          );
        }
        if (!semver.satisfies(version, SpaceService.SUPPORTED_VERSIONS)) {
          throw new CodedError(
            "spaceApiVersionOutOfRange",
            `Space API version ${version} not supported`,
            StatusCodes.BAD_REQUEST
          );
        }
      } else {
        if (version !== space.spaceDefinition.version) {
          throw new CodedError(
            "spaceApiVersionMultiple",
            `JSON contains at least two different Space API versions: ${version} and ${space.spaceDefinition.version}`,
            StatusCodes.BAD_REQUEST
          );
        }
      }
    }
  }
  return Promise.resolve();
}

const validators = {
  GET: [validateIdJson],
  DELETE: [validateIdJson],
  PUT: [
    validateApiJson,
    validateSpaceIds,
    validateSpaceApiVersion,
    validateAssignedStorageAndRamCombinations,
    validateWorkloadType,
  ],
  PATCH: [
    validateApiJson,
    validateSpaceIds,
    validateSpaceApiVersion,
    validateAssignedStorageAndRamCombinations,
    validateWorkloadType,
  ],
};

export async function validate(verb: keyof typeof validators, json: any) {
  for (const func of validators[verb]) {
    await func(json);
  }
}

export async function validateScopeRoleCreationJson(json: any) {
  let data;
  try {
    data = await validateScopeRoleCreateSchema(json);
  } catch (err) {
    handleSchemaValidatorError(err);
  }
  if (!data) {
    throw new CodedError(
      "scopeRoleCreationApiBadData",
      `scoped-role create validateScopeRoleCreateSchema failed ${JSON.stringify(validateScopeRoleCreateSchema.errors)}`,
      StatusCodes.BAD_REQUEST
    );
  }
}

export const userRoleAdd = ajv.compile(roleAddUserSchema);
export const userScopeAssign = ajv.compile(userScopeAssignmentSchema);
export const userScopeAssignV2 = ajv.compile(userScopeAssignmentSchemaV2);
export const updateScopeAssign = ajv.compile(updateUserScopeAssignmentSchema);

export async function validateUserScopeAssignment(json: any, validFn: ValidateFunction) {
  let data;
  try {
    data = await validFn(json);
  } catch (err) {
    handleSchemaValidatorError(err);
  }
  if (!data) {
    throw new CodedError(
      "UserScope Assignment",
      `validateApiSchema failed ${JSON.stringify(validFn.errors)}`,
      StatusCodes.BAD_REQUEST
    );
  }
}

export async function validateUserRoleAssignment(json: any, validFn: ValidateFunction) {
  let data;
  try {
    data = await validFn(json);
  } catch (err) {
    handleSchemaValidatorError(err);
  }
  if (!data) {
    throw new CodedError(
      "ScopeRole Assignment",
      `validateApiSchema failed ${JSON.stringify(validFn.errors)}`,
      StatusCodes.BAD_REQUEST
    );
  }
}

export async function validateUserCreationJson(json: any) {
  let data;
  try {
    data = await validateUserCreationSchema(json);
  } catch (err) {
    handleSchemaValidatorError(err);
  }
  if (!data) {
    throw new CodedError(
      "userCreationApiBadData",
      `users create validateUserCreationSchema failed ${JSON.stringify(validateUserCreationSchema.errors)}`,
      StatusCodes.BAD_REQUEST
    );
  }
}

export async function validateScopeRoleUpdateJson(json: any) {
  let data;
  try {
    data = await validateScopeRoleUpdateSchema(json);
  } catch (err) {
    handleSchemaValidatorError(err);
  }
  if (!data) {
    throw new CodedError(
      "scopeRoleUpdateApiBadData",
      `scoped-role update validateScopeRoleUpdateSchema failed ${JSON.stringify(validateScopeRoleUpdateSchema.errors)}`,
      StatusCodes.BAD_REQUEST
    );
  }
}

export async function validateUserUpdateJson(json: any) {
  let data;
  try {
    data = await validateUserUpdateSchema(json);
  } catch (err) {
    handleSchemaValidatorError(err);
  }
  if (!data) {
    throw new CodedError(
      "userUpdateApiBadData",
      `users update validateUserUpdateSchema failed ${JSON.stringify(validateUserUpdateSchema.errors)}`,
      StatusCodes.BAD_REQUEST
    );
  }
}

export async function validateWorkloadManagementJson(json: any): Promise<boolean> {
  let data;
  try {
    data = await validateWorkloadManagementSchema(json);
  } catch (err) {
    handleSchemaValidatorError(err);
  }
  if (!data) {
    throw new CodedError(
      "workloadManagementApiBadData",
      `workload management update validateWorkloadManagementSchema failed ${JSON.stringify(
        validateWorkloadManagementSchema.errors
      )}`,
      StatusCodes.BAD_REQUEST
    );
  } else {
    return data;
  }
}

const regexScopeRoleID = /^PROFILE:.+:[a-zA-Z0-9_]+$/;

export function isRoleIdComplete(role: string): boolean {
  return regexScopeRoleID.test(role);
}

export async function roleExists(context: IRequestContext, roleID: string, sacTenant: string) {
  const body = transformReqBodyForReadRole(roleID);
  // "not found or no permissions" error will be raised if the role does not exist
  await crudRole(context, body, sacTenant);
}

export async function validateRole(context: RequestContext, sacTenant: string, roleId: string, isScopeRole: boolean) {
  const body = transformReqBodyForListRoles();
  const roles = await crudRole(context, body, sacTenant);
  let role;
  if (isRoleIdComplete(roleId)) {
    role = roles.find((obj: any) => `${obj.id.type}:${obj.id.package}:${obj.id.name}` === roleId);
  } else {
    role = roles.find((obj: any) => obj.id.name === roleId);
  }

  if (!role) {
    throw new CodedError("roleIDValidation", "Role doesn't exist", StatusCodes.BAD_REQUEST);
  }

  validateRoleIsScoped(role, roleId, isScopeRole);

  return `${role.id.type}:${role.id.package}:${role.id.name}`;
}

function validateRoleIsScoped(role: any, roleId: string, isScopeRole: boolean): void {
  if (role.isScopeRole !== isScopeRole) {
    throw new CodedError(
      "roleIDValidation",
      `Role ${roleId} is not a ${isScopeRole ? "scoped" : "global"} role`,
      StatusCodes.BAD_REQUEST
    );
  }
}

export async function validateInheritance(role: CLIScopeRoleCreationInfo, context: RequestContext, sacTenant: string) {
  const inheritance = role.inheritance;
  const roles = await getInheritance(context, [sacTenant]);
  if (isRoleIdComplete(inheritance)) {
    if (!roles.find((obj) => obj.id === inheritance)) {
      throw new CodedError(
        "roleIDValidation",
        "Role doesn't exist or cannot be inherited from.",
        StatusCodes.BAD_REQUEST
      );
    }
  } else {
    const roleId = roles.find((obj) => obj.name === inheritance)?.id;
    if (roleId) {
      role.inheritance = roleId;
    } else {
      throw new CodedError(
        "roleIDValidation",
        "Role doesn't exist or cannot be inherited from.",
        StatusCodes.BAD_REQUEST
      );
    }
  }
}

export async function validateScopeRoles(context: IRequestContext, scopeId: string, users: IUsersRole[]) {
  const roles = await retrieveRolesByScope(context, scopeId);
  users.forEach((user: IUsersRole) => {
    if (isRoleIdComplete(user.role)) {
      if (!roles.find((obj) => obj.id === user.role)) {
        const error = `Role ${user.role} doesn't exist or cannot be assigned to the scope ${scopeId}`;
        logErrorWithOptionalContext(error);
        throw new CodedError("roleIDValidation", error, StatusCodes.BAD_REQUEST);
      }
    } else {
      const roleId = roles.find((obj) => obj.name === user.role)?.id;
      if (roleId) {
        user.role = roleId;
      } else {
        const error = `Role ${user.role} doesn't exist or cannot be assigned to the scope ${scopeId}`;
        logErrorWithOptionalContext(error);
        throw new CodedError("roleIDValidation", error, StatusCodes.BAD_REQUEST);
      }
    }
  });
}

export async function validUserScopeAssignment(
  context: IRequestContext,
  roleID: string,
  sacTenant: string,
  data: IUser[]
) {
  const roleScopes: string[] = await getScopeAssignment(context, roleID);
  const usersList = (await getAllUsersForSdpConversion(context, sacTenant, false, false)).map((u) => u.userName);

  const assignScopes = new Set<string>();
  data.forEach((item) => {
    const user = item.name;
    if (!usersList.includes(user)) {
      throw new CodedError("spaceApiValidation", `User :${user} does not exist`, StatusCodes.BAD_REQUEST);
    }
    assignScopes.add(item.scope || "");
  });
  const invalidScopes = [...assignScopes].filter((x) => !roleScopes.includes(x));
  if (invalidScopes.length > 0) {
    throw new CodedError(
      "spaceApiValidation",
      `Scopes :${JSON.stringify(invalidScopes)} not exist in role's scope list`,
      StatusCodes.BAD_REQUEST
    );
  }
}

export async function validSpaceList(context: IRequestContext, spacesList: string[]) {
  const result = await getSpaces(context);
  const spaces = result.map((r) => r.qualifiedName);
  const invalidSpaces = spacesList.filter((x) => !spaces.includes(x));
  if (invalidSpaces.length > 0) {
    throw new CodedError(
      "spaceApiValidation",
      `Spaces :${JSON.stringify(invalidSpaces)} does not exist `,
      StatusCodes.BAD_REQUEST
    );
  }
}

export function validateUserName(names: string[]): void {
  const userNameUITooltip = `User ID(userName) can only contain letter, digit, underscore("_") or
  multi-language characters with maximum length 20`;

  const allValidations: Array<{ userName: string; valid: boolean }> = [];

  names.forEach((n) => {
    const v = regexUsername.test(n);
    allValidations.push({ userName: n, valid: v });
  });

  const invalidNames = allValidations.filter((v) => v.valid === false);

  if (invalidNames.length > 0) {
    throw new CodedError(
      "userDeleteApiBadData",
      `users delete validateUsername failed, because ${userNameUITooltip}. Check your provided user names and
       validation results: ${JSON.stringify(allValidations)}`,
      StatusCodes.BAD_REQUEST
    );
  }
}

/**
 * Validate to copy space content
 * @param context The request context
 * @param spaceContentCopyParams The space content copy parameters
 */
export async function validateCopySpaceContent(
  context: IRequestContext,
  spaceContentCopyParams: ISpaceContentCopyParams
): Promise<void> {
  const { sourceSpaceTechnicalName, targetSpaceTechnicalName } = spaceContentCopyParams;
  logDebug(`Process validation of space ${sourceSpaceTechnicalName} `, {
    context,
  });

  // Check if target space exist, we should reject it if target space exist
  const targetSpace = await getSpacesFromNames(context, targetSpaceTechnicalName, ["kind", "name"]);
  if (targetSpace.length > 0) {
    throw new CodedError(
      "spaceCopyValidation",
      `Failed to copy space ${sourceSpaceTechnicalName}. The destination space ${targetSpaceTechnicalName} already exists.`
    );
  }
  // Call 'copySpaceContent' in dryRun mode, Checking privilege for copying time, copying sharing and copying other space content
  try {
    await copySpaceContent(context, { ...spaceContentCopyParams, dryRun: true, async: false, inSpaceManagement: true });
  } catch (err) {
    logDebug(`Failed to validate the space to be copied ${sourceSpaceTechnicalName}.`, {
      context,
    });
    throw new CodedError(
      "spaceCopyValidation",
      `Failed to copy space ${sourceSpaceTechnicalName}, ${err.message}`,
      StatusCodes.BAD_REQUEST
    );
  }
}
