/** @format */

// FILEOWNER: [Spaces]
/** @format */
const Busboy = require("busboy");

// Usage of RepositoryLock to be removed as part of deepsea migration space
// eslint-disable-next-line no-restricted-imports
import {
  AuditActivity,
  IGetObjectParameters,
  IRepositoryObject,
  ISpaceContentCopyParams,
  ObjectKind,
  StatusType,
  UclRootSpacename,
} from "@sap/deepsea-types";
import { ParamValidator, RepositoryObject, TimeUnit, isArrayNotEmpty, runTaskWithNewContext } from "@sap/deepsea-utils";
import { getLogger } from "@sap/dwc-logger";
import { promiseMap } from "@sap/dwc-promise-utils";
import { DeploymentState, RuntimeImpact } from "@sap/seal-interfaces";
import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { IUser, IUserAssigned, UmsTypes } from "../../shared/api/ScopedUsers";
import * as API from "../../shared/api/SpaceAPI";
import { Capability } from "../../shared/spaces/SpaceCapabilities";
import { IWorkloadManagement } from "../../shared/workloadmanagement/types";
import { ApiConnectionService } from "../connections/api/apiConnectionService";
import {
  createCertificate,
  deleteCertificate,
  getCertificates,
} from "../connections/certificates/certificatesRoutesHandler";
import { getConnectionStatus } from "../connections/connectionStatus";
import { deleteConnections } from "../connections/remotes";
import { FeatureFlagProvider } from "../featureflags/FeatureFlagProvider";
import {
  ISpaceContentCopyAndDeployResult,
  processCopySpaceContentAndDeploy,
} from "../repository/api/common/copySpaceContent";
import { RepositoryObjectClient } from "../repository/client/repositoryObjectClient";
import { convertJobStatus } from "../repository/contentnetwork/util";
import { authorizeSpacesToConsumeUclSharedConnection } from "../repository/dataproducts/dataProductsUtils";
import { writeDeploymentStateInRepo } from "../repository/plugins/deployPlugin";
import { BackendSpaceCapabilities } from "../repository/security/BackendSpaceCapabilities";
import { IRequestContext } from "../repository/security/common/common";
import { getSpaceUuidFromName } from "../repository/spaces";
import { getMessageList, transformErrorToMessage, type IDeployResult } from "../reuseComponents/deployment/src";
import { SpaceLockService } from "../reuseComponents/locking/src/SpaceLockService";
import { CustomerHana } from "../reuseComponents/spaces/src";
import { DeploySpaceOptions } from "../reuseComponents/spaces/src/types/spaceTypes";
import {
  CreateOrUpdateUser,
  DeleteUser,
  IRoleUserAssignment,
  IScopeUserAssignment,
  ScopeAssignments,
  ScopeUserAssignments,
} from "../routes/sac/types";
import * as dbusers from "../routes/space/databaseUsers";
import { lsSmLogInfo } from "../routes/space/ls/impl/lsSmUtils";
import { IUsersRole, IUsersRoleList } from "../routes/space/types";
import {
  assignAvailableScopedRolesToCopiedSpace,
  assignUsersToScopeWithScopedRole,
  createUpdateDeleteUsers,
  crudRole,
  getAllUsersForSdpConversion,
  getScopeAssignment,
  getScopeUserAssignmentRoleOrScope,
  getScopeUsers,
  postScopeAssignment,
  postScopeUserAssignmentRoleOrScope,
  postUserAssignmentToGlobalRole,
  unassignUsersToScopeWithScopedRole,
} from "../routes/space/user";
import { CodedError, sendErrorResponse } from "../server/errorResponse";
import { getDiscoveryFileContent } from "./cliSupport";
import { buildResponse } from "./connectionValidationMessage";
import { upsert } from "./connections";
import { sendNotification } from "./notification";
import { Api, displayWarningMessageToCLIUsers } from "./routes-util.js";
import {
  CLIScopeRoleCreationInfo,
  CLIScopeRoleUpdateInfo,
  getNextVersion,
  getUsersFromRole,
  simplifyListGlobalRoles,
  simplifyListRoles,
  simplifyScopeRole,
  transformReqBodyForCreateScopeRole,
  transformReqBodyForDeleteScopeRole,
  transformReqBodyForListRoles,
  transformReqBodyForReadRole,
  transformReqBodyForUpdateScopeRole,
} from "./scopeRole";
import {
  CLIUserCreationInfo,
  CLIUserInfo,
  CLIUserUpdateInfo,
  CONTENT_TYPE_USERS_DETAILS,
  getTenantIdInfo,
  transformReqBodyForCreateUsers,
  transformReqBodyForDeleteUsers,
  transformReqBodyForUpdateUsers,
  transformResBodyForListUsers,
  transformResBodyForScopedRoleListUsers,
} from "./user";
import {
  updateScopeAssign,
  userRoleAdd,
  userScopeAssign,
  userScopeAssignV2,
  validSpaceList,
  validUserScopeAssignment,
  validate,
  validateCopySpaceContent,
  validateInheritance,
  validateRole,
  validateScopeRoleCreationJson,
  validateScopeRoleUpdateJson,
  validateScopeRoles,
  validateUserCreationJson,
  validateUserName,
  validateUserRoleAssignment,
  validateUserScopeAssignment,
  validateUserUpdateJson,
  validateWorkloadManagementJson,
} from "./validation";

const logger = getLogger(__filename);
const { logError, logDebug, logWarning, logInfo } = logger;

const deprecatedProperties = [
  {
    name: "priority",
    deprecatedWithWave: "2024.17",
    decommissionedAfterWave: "",
    customMessage: "",
    sapHelpUrl:
      "https://help.sap.com/whats-new/48017b2cc4834fc6b6cae87097bd9e4d?Category=Space%20Management&locale=en-US&Version=2024.17",
  },
  {
    name: "workloadClass",
    deprecatedWithWave: "2024.17",
    decommissionedAfterWave: "",
    customMessage: "",
    sapHelpUrl:
      "https://help.sap.com/whats-new/48017b2cc4834fc6b6cae87097bd9e4d?Category=Space%20Management&locale=en-US&Version=2024.17",
  },
  {
    name: "workloadType",
    deprecatedWithWave: "2024.17",
    decommissionedAfterWave: "",
    customMessage: "",
    sapHelpUrl:
      "https://help.sap.com/whats-new/48017b2cc4834fc6b6cae87097bd9e4d?Category=Space%20Management&locale=en-US&Version=2024.17",
  },
];

/**
 * Mass operation on spaces is currently not supported
 */
function getSpaceId(context: IRequestContext, data: API.DataWarehouseCloudAPI) {
  const spaceIds = Object.keys(data);
  if (spaceIds.length > 1) {
    // This should currently not occur as we do not support to deploy more then one space, but once we do might need this check.
    const message = `Mass spaces operation is not supported ${spaceIds.toString()}`;
    logError(message, { context });
    throw new CodedError("PUTMassDeployment", message, StatusCodes.BAD_REQUEST);
  }
  return spaceIds[0];
}

/**
 *  Saving of the space CSN in the Repository.
 *
 * @param data.spaceDefinition is being updated and used during deployment
 */
async function runDesignTimeSave(
  context: IRequestContext,
  options: DeploySpaceOptions = { spaceGuid: "" },
  data: API.DataWarehouseCloudAPI,
  res: Response,
  syncDeployment: boolean
) {
  return await runTaskWithNewContext(context, async (context: IRequestContext) => {
    if (options.spaceName && hasSpaceDefinition(data)) {
      await writeDeploymentStateInRepo(
        context,
        [options.spaceGuid],
        DeploymentState.INPROGRESS,
        options.spaceName,
        ObjectKind.space
      );
    }
    return await Api.getInstance().saveCsn(context, data, res, syncDeployment, options);
  });
}

async function runRuntimeDeployment(
  context: IRequestContext,
  spaceGuid: string = "",
  data: API.DataWarehouseCloudAPI,
  res: Response,
  syncDeployment: boolean,
  spaceId: string,
  options: DeploySpaceOptions
): Promise<IDeployResult | undefined> {
  return await runTaskWithNewContext(
    context,
    async (context: IRequestContext) => {
      try {
        if (hasSpaceDefinition(data)) {
          // Needs to be set again, in case the space did not existed at the previous point.
          await writeDeploymentStateInRepo(context, [spaceGuid], DeploymentState.INPROGRESS, spaceId, ObjectKind.space);
        }
        const result = await Api.getInstance().deployCsn(context, data, options);
        if (result && syncDeployment) {
          if (
            !result.messages.hasError &&
            result.entityResults.every((e) =>
              [DeploymentState.SUCCESS, DeploymentState.NO_CHANGE].includes(e.deploymentState)
            )
          ) {
            // Header by default contains the content-type" in service/server/index.ts which leads to an error in the http client when empty (undefined) is returned.
            // Therefore the header "content-type" will be set to "" to be aligned with the HTTP response guid line.
            res.setHeader("content-type", "");
            if (
              (await FeatureFlagProvider.isFeatureActive(context, "DWC_DUMMY_SPACE_PERMISSIONS")) &&
              (await FeatureFlagProvider.isFeatureActive(context, "DWCO_CLI_SDP"))
            ) {
              displayWarningMessageToCLIUsers(context, data, res, StatusCodes.CREATED);
            } else {
              res.status(StatusCodes.CREATED).send();
            }
          } else {
            let status = StatusCodes.INTERNAL_SERVER_ERROR;
            let code = "spaceDeploymentFailed";
            if (result.messages.errors.length === 1) {
              const err = result.messages.errors[0];
              if (err.internalDetails) {
                const isNUmber = Number(err.internalDetails);
                if (!isNaN(parseFloat(err.internalDetails)) && isFinite(isNUmber)) {
                  status = isNUmber;
                }
              }
              code = err.messageCode;
            }
            throw new CodedError(
              code,
              `Space deployment failed PUT space
              result.messages.all ${JSON.stringify(result.messages.all)}
              result.entityResults ${JSON.stringify(result.entityResults)}
              result.messages.hasError ${result.messages.hasError}
              data ${JSON.stringify(data)}`,
              status
            );
          }
        }

        // TODO In async mode error cannot resend back as connection is already closed see also manageSpaceDeploymentError explanation?
        // Sync or async result back
        return result;
      } catch (err) {
        logError(
          [
            `runRuntimeDeployment error spaceId ${spaceId}
            data ${JSON.stringify(data)} syncDeployment ${syncDeployment} spaceGuid ${spaceGuid}`,
            err,
          ],
          { context }
        );
        throw err;
      }
    },
    `Deployment of Space ${Object.keys(data).join(", ")}`
  );
}

/**
 * In Express, once a response has been sent using res.send(), res.json(), or similar methods, you cannot send another response for the same request.
 * This is because sending a response completes the HTTP request-response cycle, and the connection between the client and the server is typically closed after the response is sent.
 *
 * (Not possible as Accepted 202 is expected)
 * If you try to send another response for the same request, you'll get the "Cannot set headers after they are sent to the client" error.
 * This is because the headers for the response are sent when you call res.send() or similar methods, and you can't modify the headers after they've been sent.
 * If you need to send multiple pieces of data to the client, you should send them all in one response. For example, you can send an array or an object that contains all the data.
 *
 * (Maybe irrelevant with removing Repo dependency)
 * If you need to perform multiple operations and send a response after each one, you should consider using a different architecture,
 * such as WebSockets or Server-Sent Events, which allow the server to send multiple messages to the client over a single connection.
 */
async function manageSpaceDeploymentError(
  context: IRequestContext,
  err: Error,
  req: Request,
  spaceGuid: string,
  spaceName: string
) {
  await runTaskWithNewContext(
    context,
    async (context) => {
      logError(
        [
          `Space deployment failed PUT spaces spaceGuid ${spaceGuid}
          headersSent ${req.context.httpResponse?.headersSent}
          spaceName ${spaceName} body ${Object.keys(req.body).join(", ")}`,
          err,
        ],
        { context }
      );
      // DW101-7772 Async cannot be send due to the response already being sent
      if (req.context.httpResponse?.headersSent) {
        logError(
          `manageSpaceDeploymentError cannot send error again in async mode
          spaceName ${spaceName} headersSent ${req.context.httpResponse?.headersSent} `,
          { context }
        );
      } else {
        // Has to be req.context because httpResponse: undefined, in createNewForBackground
        sendErrorResponse(req.context, "spaceAPIPutFailed", { err });
      }
      await writeDeploymentStateInRepo(context, [spaceGuid], DeploymentState.FAILURE, spaceName, ObjectKind.space);
      // DW101-92920 overwrite with internalDetails, as only description shown in the UI as text
      const messages = getMessageList(transformErrorToMessage(err, logger as any));
      messages.all.forEach((m) => {
        if (m.internalDetails) {
          m.description = m.internalDetails;
        }
      });
      // DW10-2332 Restructure of Promise.all
      await promiseMap<string, void>(
        Object.keys(req.body),
        async (spaceId) => {
          const result = {
            entityResults: [
              {
                entityName: "deployCsn",
                deploymentState: DeploymentState.FAILURE,
                pluginName: spaceId,
                runtimeImpact: RuntimeImpact.Unknown,
              },
            ],
            messages,
          };
          await Api.getInstance().checkAndLogStep(context, spaceId, result, AuditActivity.DEPLOY);
          // Send Notification because AuditActivity.DEPLOY
          await sendNotification(context, spaceId, result);
        },
        { concurrency: 3 }
      );
    },
    `sendErrorResponse and writeDeploymentStateInRepo spaceGuid ${spaceGuid}`
  );
}

/**
 *  WORKAROUND DW101-67623 in case the space does not exist and 400 incorrectly typed URL is being thrown
 */
async function getSpaceUuidFromNameWorkaround(context: IRequestContext, spaceId: string): Promise<string> {
  let spaceGuid = "";
  try {
    logInfo(`Space ${spaceId} create or update PUT`, { context });
    spaceGuid = await getSpaceUuidFromName(context, spaceId);
  } catch (err) {
    // If 400 bad request because space does not exist which is correct otherwise keep throwing
    if (err.status === StatusCodes.BAD_REQUEST && err.message.endsWith("doesn't exist")) {
      logDebug(`getSpaceUuidFromName expected err ${JSON.stringify(err)}`, { context });
      spaceGuid = ""; // Space does not exist
    } else {
      // Some other issue
      logError([`getSpaceUuidFromName failed`, err], { context });
      throw err;
    }
  }
  return spaceGuid;
}

/**
 * DW101-69506 Parallel Deployment of Spaces with the same spaceId creates double entry in Repo
 * Try to acquire first a lock before trying to save into the repository the space CSN object.
 * Afterwords try to deploy the saved CSN object into the customer hana.
 */
async function saveAndDeployWithLock(
  context: IRequestContext,
  req: Request,
  res: Response,
  spaceId: string,
  spaceGuid: string,
  syncDeployment: boolean,
  data: API.DataWarehouseCloudAPI,
  oldSpaceId?: string,
  deployContent?: boolean
) {
  logInfo(`saveAndDeployWithLock ${spaceId} correlation id ${context.correlationId}`, { context });
  let spaceDeployResult: IDeployResult | undefined;
  let spaceContentCopyAndDeployResult: ISpaceContentCopyAndDeployResult | undefined;
  const lock = await SpaceLockService.fromRequestContext(context);
  const SPACE_LOCK_TIMEOUT_SEC =
    Number(process.env?.SPACE_LOCK_TIMEOUT_SEC || TimeUnit.MINUTES.toSeconds(5)) +
    Number(
      // NOTE - Longer timeout in case of hdlfStorage capability
      (await BackendSpaceCapabilities.get(context)).hasCapability(spaceId, Capability.hdlfStorage)
        ? TimeUnit.MINUTES.toSeconds(10)
        : 0
    );
  lsSmLogInfo(
    `Will saveAndDeployWithLock the ${spaceId} space id and ${spaceGuid} space guid for ${SPACE_LOCK_TIMEOUT_SEC} space lock timeout in seconds`,
    {
      context,
    }
  );
  if (await lock.tryAcquireLock(spaceId, SPACE_LOCK_TIMEOUT_SEC)) {
    try {
      const options: DeploySpaceOptions = {
        spaceGuid,
        enforceDatabaseUserDeletion: req?.query?.enforceDatabaseUserDeletion === "true",
        spaceName: spaceId,
        newSpaceCreation: !spaceGuid,
        skipWorkloadManagement: req?.query?.skipWorkloadManagement === "true",
      };

      spaceGuid = await runDesignTimeSave(context, options, data, res, syncDeployment);
      spaceDeployResult = await runRuntimeDeployment(context, spaceGuid, data, res, syncDeployment, spaceId, options);

      // continue the copy of space content and content deployment only when space deployment succeeds
      if (oldSpaceId && !spaceDeployResult?.messages.hasError) {
        // this indicates that the space content is being copied and deployed if required
        spaceContentCopyAndDeployResult = await runSpaceContentCopyAndDeploy(
          context,
          oldSpaceId,
          spaceId,
          deployContent
        );
      }
    } catch (err) {
      logError([`saveAndDeployWithLock failed spaceId ${spaceId}`, err], {
        context,
      });
      throw err;
    } finally {
      const released = await lock.tryReleaseLock(spaceId);
      logInfo(`saveAndDeployWithLock lock spaceId ${spaceId} released ${released}`, { context });
      if (spaceDeployResult) {
        // Send Notification because AuditActivity.DEPLOY and after releasing the lock
        // In an not error case the result should always be defined
        // In case of an exception and result = undefined the notification will be send during manageSpaceDeploymentError
        await sendNotification(context, spaceId, spaceDeployResult, oldSpaceId, spaceContentCopyAndDeployResult);
      }
    }
  } else {
    const released = await lock.tryReleaseLock(spaceId);
    logInfo(`saveAndDeployWithLock spaceId ${spaceId} was already locked released ${released}`, { context });
    throw new CodedError(
      "spaceDeploymentFailed",
      `Space ${spaceId} is already locked, please try again later!`,
      StatusCodes.CONFLICT
    );
  }
}

function fixSpaceType(data: API.DataWarehouseCloudAPI) {
  // TODO: This is a quick fix for wrong saving of UI Space Type as CSN property in "src\components\managespaces\model\Space.ts", assignCSN
  for (const spaceId in data) {
    const spaceDefinition = data[spaceId].spaceDefinition;
    if (spaceDefinition && spaceDefinition.spaceType) {
      if (spaceDefinition.spaceType !== "abapbridge" && spaceDefinition.spaceType !== "marketplace") {
        delete spaceDefinition.spaceType;
      }
    }
  }
}

/**
 * Remove promiseMap as mass deployment is currently not supported
 */
export async function putSpaces(req: Request, res: Response) {
  const { context } = req;
  let spaceGuid = "";
  let spaceId = "";
  let oldSpaceId = "";
  try {
    if (await FeatureFlagProvider.isFeatureActive(context, "DWCO_WORKLOAD_MANAGEMENT_UI")) {
      res.setHeader("X-DSP-API-DEPRECATED-PROPERTIES", JSON.stringify(deprecatedProperties));
    }
    const syncDeployment = req?.query?.async === "false";
    logInfo(`PUT space synchronousDeployment ${syncDeployment} query ${JSON.stringify(req?.query)}`, { context });
    const data = req.body as API.DataWarehouseCloudAPI;
    replaceSourceLanguageInSpaces(data);
    spaceId = getSpaceId(context, data);

    const isSpaceContentCopy = !!(
      (await FeatureFlagProvider.isFeatureActive(context, "DWCO_BDC_SPACE_COPY")) &&
      req?.query?.newSpaceId &&
      req?.query?.newSpaceLabel
    );
    if (isSpaceContentCopy) {
      const prepareInfo = await prepareSpaceContentCopy(context, req, spaceId, data);
      spaceId = prepareInfo.newSpaceId;
      oldSpaceId = prepareInfo.oldSpaceId;
    }

    fixSpaceType(req.body);
    await validate("PUT", req.body);
    spaceGuid = await getSpaceUuidFromNameWorkaround(context, spaceId);
    logDebug(`Create or Update space id ${spaceId} spaceDefinition ${JSON.stringify(data.spaceDefinition)}`, {
      context,
    });
    const deployContent = req?.query?.deployContent?.toString().toLowerCase() === "true" ? true : false;
    logWarning(`PUT deploySpace spaceId ${spaceId} spaceGuid ${spaceGuid} syncDeployment ${syncDeployment}`, {
      context,
    });
    await saveAndDeployWithLock(context, req, res, spaceId, spaceGuid, syncDeployment, data, oldSpaceId, deployContent);
  } catch (err) {
    logError([`Space upsert failed PUT space ${Object.keys(req.body).join(", ")}`, err], { context });
    if (err instanceof CodedError && err.code === "spaceCopyValidation") {
      return sendErrorResponse(context, err.code, { err });
    }
    await manageSpaceDeploymentError(context, err, req, spaceGuid, spaceId);
  }
}

async function runSpaceContentCopyAndDeploy(
  context: IRequestContext,
  oldSpaceId: string,
  newSpaceId: string,
  deployContent?: boolean
): Promise<ISpaceContentCopyAndDeployResult | undefined> {
  return await runTaskWithNewContext(
    context,
    async (context: IRequestContext) => {
      let copySpaceContentAndDeployResult: ISpaceContentCopyAndDeployResult | undefined;
      try {
        const spaceContentCopyParams: ISpaceContentCopyParams = {
          sourceSpaceTechnicalName: oldSpaceId,
          targetSpaceTechnicalName: newSpaceId,
          deployContent,
          inSpaceManagement: true,
        };
        copySpaceContentAndDeployResult = await processCopySpaceContentAndDeploy(context, spaceContentCopyParams);
      } catch (err) {
        logError([`Space content copy failed from ${oldSpaceId} to ${newSpaceId}`, err], { context });
        copySpaceContentAndDeployResult = {
          spaceContentCopyResult: { status: StatusType.Failed, jobId: "COPY_FAILED", message: err.message },
        };
      }
      return copySpaceContentAndDeployResult;
    },
    `Space content copy and deploy from ${oldSpaceId} to ${newSpaceId}`
  );
}

function generateSpaceDefinitionForSpaceContentCopy(
  data: API.DataWarehouseCloudAPI,
  newSpaceId: string,
  newSpaceLabel: string
) {
  for (const spaceId in data) {
    if (data[spaceId].spaceDefinition) {
      (data[spaceId].spaceDefinition as API.SpaceDefinition).label = newSpaceLabel;
      data[newSpaceId] = data[spaceId];
      delete data[newSpaceId].spaceDefinition?.members;
      delete data[newSpaceId].spaceDefinition?.dbusers;
      delete data[newSpaceId].spaceDefinition?.hdicontainers;
      delete data[newSpaceId].spaceDefinition?.dataRepoState;

      // remove bdc capability from the space definition for the new space
      if (isArrayNotEmpty(data[newSpaceId].spaceDefinition?.capabilities)) {
        data[newSpaceId].spaceDefinition!.capabilities = data[newSpaceId].spaceDefinition!.capabilities!.filter(
          (capability) => capability !== Capability.bdc.valueOf()
        );
      }

      // clear the potential invalid properties from the space definition, see https://jira.tools.sap/browse/DW101-85379
      delete (data[newSpaceId].spaceDefinition as any)?.deployment_date;
      delete (data[newSpaceId].spaceDefinition as any)?.deploymentInProgressStart;
      delete (data[newSpaceId].spaceDefinition as any)?.deploymentState;

      delete data[spaceId];
    }
  }
}

export async function patchSpaces(req: Request, res: Response) {
  const { context } = req;
  try {
    if (await FeatureFlagProvider.isFeatureActive(context, "DWCO_WORKLOAD_MANAGEMENT_UI")) {
      res.setHeader("X-DSP-API-DEPRECATED-PROPERTIES", JSON.stringify(deprecatedProperties));
    }
    const data = req.body as API.DataWarehouseCloudAPI;
    replaceSourceLanguageInSpaces(data);
    fixSpaceType(req.body);
    await validate("PATCH", req.body);
    const options: DeploySpaceOptions = {
      spaceGuid: "",
      enforceDatabaseUserDeletion: req?.query?.enforceDatabaseUserDeletion === "true",
    };
    await Api.getInstance().saveCsn(context, data, res, undefined, options);
  } catch (err) {
    logError([`Space save failed PATCH space ${Object.keys(req.body).join(", ")}`, err], { context });
    sendErrorResponse(context, "spaceAPIPatchFailed", { err });
  }
}

async function prepareSpaceContentCopy(
  context: IRequestContext,
  req: Request,
  oldSpaceId: string,
  data: API.DataWarehouseCloudAPI
) {
  const newSpaceId = ParamValidator.mandatoryParam(
    context,
    "newSpaceId",
    ParamValidator.string(context, req.query.newSpaceId)
  );
  const newSpaceLabel = ParamValidator.mandatoryParam(
    context,
    "newSpaceLabel",
    ParamValidator.string(context, req.query.newSpaceLabel)
  );
  await validateCopySpaceContent(context, {
    sourceSpaceTechnicalName: oldSpaceId,
    targetSpaceTechnicalName: newSpaceId,
  });
  generateSpaceDefinitionForSpaceContentCopy(data, newSpaceId, newSpaceLabel);
  await assignAvailableScopedRolesToCopiedSpace(context, oldSpaceId, newSpaceId);
  return { newSpaceId, newSpaceLabel, oldSpaceId };
}

function replaceSourceLanguageInSpaces(data: API.DataWarehouseCloudAPI) {
  for (const spaceId in data) {
    if (data[spaceId].spaceDefinition?.sourceLanguage === "") {
      (data[spaceId].spaceDefinition as API.SpaceDefinition).sourceLanguage = undefined;
    }
  }
}

function parseQueryParameter(par: string | undefined) {
  if (!par || par === "false") {
    return false;
  }
  if (par === "true") {
    return true;
  }
  return par.split(",");
}

function getQueryBody(req: Request<any, any, any, any>) {
  let json = req.body;
  if (!json || Object.keys(json).length === 0) {
    if (!req.query.space) {
      throw new CodedError("spaceApiMissingSpace", 'Specify at least query parameter "space"', StatusCodes.BAD_REQUEST);
    }
    json = {
      [req.query.space]: {
        definitions: parseQueryParameter(req.query.definitions),
        connections: parseQueryParameter(req.query.connections),
        spaceDefinition: parseQueryParameter(req.query.spaceDefinition),
        spaceDefinitionUiProperties: parseQueryParameter(req.query.spaceDefinitionUiProperties),
      },
    };
  }
  return json;
}

export async function getSpaces(req: Request, res: Response) {
  const { context } = req;
  try {
    const json = getQueryBody(req);
    await validate("GET", json);
    const result = await Api.getInstance().getContent(context, json as API.IDwcApiQuery);
    if (await FeatureFlagProvider.isFeatureActive(context, "DWCO_WORKLOAD_MANAGEMENT_UI")) {
      res.setHeader("X-DSP-API-DEPRECATED-PROPERTIES", JSON.stringify(deprecatedProperties));
    }

    // ignore CSN dbUsers, read them from schema
    if ((await FeatureFlagProvider.isFeatureActive(context, "DWCO_CLI_CREATE_DELETE_DATABASEUSER")) && result) {
      for (const spaceId in result) {
        if (result[spaceId].spaceDefinition) {
          result[spaceId].spaceDefinition!.dbusers = {};

          const dbUsers = await dbusers.getDatabaseUsersForSpace(context, spaceId, true);
          for (const dbUser of dbUsers) {
            result[spaceId].spaceDefinition!.dbusers![dbUser.username] = {
              ingestion: {
                auditing: {
                  dppChange: dbUser.details.dppChange,
                  dppRead: dbUser.details.dppRead,
                },
              },
              consumption: {
                consumptionWithGrant: dbUser.details.consumptionWithGrant,
                spaceSchemaAccess: dbUser.details.spaceSchemaAccess,
                scriptServerAccess: dbUser.details.scriptServerAccess,
                enablePasswordPolicy: dbUser.details.enablePasswordPolicy,
                localSchemaAccess: dbUser.details.localSchemaAccess,
                hdiGrantorForCupsAccess: dbUser.details.hdiGrantorForCupsAccess,
              },
            };
          }
        }
      }
    }

    res.status(StatusCodes.OK).send(result);
  } catch (err) {
    logError([`Space GET failed: space:${req.query?.space}`, err], { context });
    sendErrorResponse(context, "spaceAPIGetFailed", { err });
  }
}

export async function listGlobalRoles(req: Request, res: Response) {
  const { context } = req;
  try {
    const sacTenant = await getTenantIdInfo(context);
    logInfo(`fetching global roles from UMS`, { context });
    const body = transformReqBodyForListRoles();
    const roles = await crudRole(context, body, sacTenant);
    const results = simplifyListGlobalRoles(roles);
    res.status(StatusCodes.OK).send(results);
  } catch (err) {
    logError([`global role list failed,`, err], { context });
    sendErrorResponse(context, "rolesListFailed", { err });
  }
}

export async function listScopeRoles(req: Request, res: Response) {
  const { context } = req;
  try {
    const sacTenant = await getTenantIdInfo(context);
    logInfo(`fetching all scope roles from UMS`, { context });
    const body = transformReqBodyForListRoles();
    const roles = await crudRole(context, body, sacTenant);
    const results = simplifyListRoles(roles);
    res.status(StatusCodes.OK).send(results);
  } catch (err) {
    logError([`scoped-role list failed,`, err], { context });
    sendErrorResponse(context, "scopeRolesListFailed", { err });
  }
}

export async function createScopeRole(req: Request, res: Response) {
  const { context } = req;
  try {
    // req.body.inheritance can be a string or an array of strings. If it is an array of strings only the first element is used.
    req.body.inheritance = Array.isArray(req.body.inheritance) ? req.body.inheritance[0] : req.body.inheritance;
    await validateScopeRoleCreationJson(req.body);
    const role: CLIScopeRoleCreationInfo = req.body;
    const sacTenant = await getTenantIdInfo(context);
    await validateInheritance(role, context, sacTenant);

    const body = transformReqBodyForCreateScopeRole(role, sacTenant);

    const resBody = await crudRole(context, body, sacTenant);
    res.status(StatusCodes.CREATED).send(resBody);
  } catch (err) {
    logError([`scoped-role creation failed: role:${req.body}`, err], { context });
    sendErrorResponse(context, "scopedRoleCreationFailed", { err });
  }
}

export async function deleteScopeRole(req: Request, res: Response) {
  const { context } = req;
  try {
    let roleName = req.params.role.trim();
    const sacTenant = await getTenantIdInfo(context);

    roleName = await validateRole(context, sacTenant, roleName, true);
    const body = transformReqBodyForDeleteScopeRole(roleName);
    await crudRole(context, body, sacTenant);
    res.status(StatusCodes.NO_CONTENT).send();
  } catch (err) {
    logError([`scoped-role delete failed: role:${req.body}`, err], { context });
    sendErrorResponse(context, "scopedRoleDeleteFailed", { err });
  }
}

export async function updateScopeRole(req: Request, res: Response) {
  const { context } = req;
  try {
    let roleId = req.params.role.trim();
    const sacTenant = await getTenantIdInfo(context);

    roleId = await validateRole(context, sacTenant, roleId, true);

    // req.body.inheritance can be a string or an array of strings. If it is an array of strings only the first element is used.
    req.body.inheritance = Array.isArray(req.body.inheritance) ? req.body.inheritance[0] : req.body.inheritance;
    await validateScopeRoleUpdateJson(req.body);
    const role: CLIScopeRoleUpdateInfo = req.body;
    role.id = roleId;

    await validateInheritance(role, context, sacTenant);

    const readBody = transformReqBodyForReadRole(role.id);
    const readRes = await crudRole(context, readBody, sacTenant);

    const version = getNextVersion(readRes);
    const body = transformReqBodyForUpdateScopeRole(role, version);
    const resBody = await crudRole(context, body, sacTenant);

    res.status(StatusCodes.OK).send(resBody);
  } catch (err) {
    logError([`scoped-role update failed: role:${req.body}`, err], { context });
    sendErrorResponse(context, "scopedRoleUpdateFailed", { err });
  }
}
export async function readScopeRole(req: Request, res: Response) {
  const { context } = req;
  try {
    const sacTenant = await getTenantIdInfo(context);
    let roleId = req.params.role.trim();
    roleId = await validateRole(context, sacTenant, roleId, true);

    const body = transformReqBodyForReadRole(roleId);
    const role = await crudRole(context, body, sacTenant);
    const result = simplifyScopeRole(role);

    res.status(StatusCodes.OK).send(result);
  } catch (err) {
    logError([`scoped-role read failed: role:${req.body}`, err], { context });
    sendErrorResponse(context, "scopedRoleReadFailed", { err });
  }
}

export async function listUsers(req: Request, res: Response) {
  const { context } = req;
  let detail = false;
  let users: CLIUserInfo[];
  try {
    const accept = req.headers.accept;
    if (accept === CONTENT_TYPE_USERS_DETAILS) {
      detail = true;
    }
    const sacTenant = await getTenantIdInfo(context);
    const usersList = await getAllUsersForSdpConversion(context, sacTenant, false, detail);
    users = transformResBodyForListUsers(usersList, detail);
    res.status(StatusCodes.OK).send(users);
  } catch (err) {
    logError([`Users list failed,`, err], { context });
    sendErrorResponse(context, "usersListFailed", { err });
  }
}

export async function createUser(req: Request, res: Response) {
  const { context } = req;
  let body: CreateOrUpdateUser[] = [];
  try {
    await validateUserCreationJson(req.body);
    const users = req.body as CLIUserCreationInfo[];
    body = await transformReqBodyForCreateUsers(users, context);
    const sacTenant = await getTenantIdInfo(context);
    const result = await createUpdateDeleteUsers(context, body, sacTenant);

    res.status(StatusCodes.OK).send(result);
  } catch (err) {
    logError([`Users creation failed, users:${body}`, err], { context });
    sendErrorResponse(context, "usersCreationFailed", { err });
  }
}

export async function updateUser(req: Request, res: Response) {
  const { context } = req;
  let body: CreateOrUpdateUser[] = [];
  try {
    await validateUserUpdateJson(req.body);
    const users = req.body as CLIUserUpdateInfo[];
    body = await transformReqBodyForUpdateUsers(users, context);
    const sacTenant = await getTenantIdInfo(context);
    const result = await createUpdateDeleteUsers(context, body, sacTenant);

    res.status(StatusCodes.OK).send(result);
  } catch (err) {
    logError([`User update failed, user:${body}`, err], { context });
    sendErrorResponse(context, "userUpdateFailed", { err });
  }
}

export async function deleteUser(req: Request, res: Response) {
  const { context } = req;
  let body: DeleteUser[] = [];
  try {
    const userNames = (req.query.users as string).split(",");
    validateUserName(userNames);
    body = transformReqBodyForDeleteUsers(userNames);
    const sacTenant = await getTenantIdInfo(context);
    const result = await createUpdateDeleteUsers(context, body, sacTenant);
    res.status(StatusCodes.OK).send(result);
  } catch (err) {
    logError([`Users deletion failed, user:${body}`, err], { context });
    sendErrorResponse(context, "userDeletionFailed", { err });
  }
}

async function listNormalSpaces(context: IRequestContext): Promise<RepositoryObject[]> {
  const oParams: IGetObjectParameters = {
    details: ["name"],
    kind: ObjectKind.space,
    filters: "spaceType:<NULL>|abapbridge",
  };
  return await RepositoryObjectClient.getObject(context, oParams);
}
function getConnectionsName(connections: any) {
  return connections.map((connection: any) => connection.name);
}
async function getConnectionCapabilities(connections: any, context: any) {
  const featureFlags = await FeatureFlagProvider.getFeatureFlags(context);

  return connections.map((connection: any) => {
    const commonCapabilities = {
      name: connection.name,
      capabilityHanaSdi: connection.capabilityHanaSdi,
      capabilityModelTransfer: connection.capabilityModelTransfer,
      capabilityPartnerSchema: connection.capabilityPartnerSchema,
    };

    if (featureFlags.INFRA_DWC_TWO_TENANT_MODE) {
      return {
        ...commonCapabilities,
        capabilityDataflowSource: connection.capabilityDataflowSource,
        capabilityDataflowTarget: connection.capabilityDataflowTarget,
        capabilityReplicationflowSource: connection.capabilityReplicationflowSource,
        capabilityReplicationflowTarget: connection.capabilityReplicationflowTarget,
      };
    } else {
      return {
        ...commonCapabilities,
        capabilityDisDataflow: connection.capabilityDisDataflow,
      };
    }
  });
}

function getConnectionsMetadata(connections: any) {
  return connections.map((connection: any) => ({
    name: connection.name,
    businessName: connection.businessName,
    typeId: connection.typeId,
    creation_date: connection.creation_date,
    creator: connection.creator,
    realtimeReplicationStatus: connection.realtimeReplicationStatus || "NOTAPPLICABLE",
  }));
}
export async function delCertificate(req: Request, res: Response) {
  try {
    const response = await deleteCertificate(req, res);
    res.status(StatusCodes.OK).send(response);
  } catch (err) {
    logError("Configuration certificate delete failed:", { context: req.context });
    sendErrorResponse(req.context, "deleteCertificate", { err });
  }
}

export async function getAllCertificates(req: Request, res: Response) {
  try {
    const response = await getCertificates(req, res);
    res.status(StatusCodes.OK).send(response);
  } catch (err) {
    logError("Configuration certificate list failed:", { context: req.context });
    sendErrorResponse(req.context, "ListCertificate", { err });
  }
}
/*
 * Handles the request to get job status.
 *
 * Note: we have an exisiting endpoint to get job status api/vi/jobstatus
 * The different between these two endpoints is that current one convert jobstatus values to new recommended values
 * Succeed -> Success
 * Aborted -> Canceled
 * Executing -> Running
 * Cancelling -> Canceling
 *
 * @param req - The HTTP request object, which includes the context for the operation and jobId.
 * @param res - The HTTP response object used to send the response back to the client.
 *
 * @throws Will log and handle any errors that occur during the retrieval of job status.
 * */
export async function getJobStatus(req: Request<any, any, any, any>, res: Response) {
  try {
    const jobId = req.query && req.query.jobId;
    const aResults = jobId && (await RepositoryObjectClient.callGetJobStatus(req.context, { ids: [jobId] }));
    if (aResults) {
      const jobStatusRes = Array.isArray(aResults) ? aResults[0] : aResults;
      delete jobStatusRes.preExecutionDetails;
      const converteJobStatusRes = await convertJobStatus(jobStatusRes);
      res.status(StatusCodes.OK).send(converteJobStatusRes);
    } else {
      res.sendStatus(StatusCodes.NOT_FOUND); // Not Found
    }
  } catch (err) {
    logError("Get job status failed:", { context: req.context });
    sendErrorResponse(req.context, "getJobStatus", { err });
  }
}

/**
 * Handles the request to list system connections.
 *
 * This function retrieves a list of UCL shared connections based on the specified parameters
 * and sends the result as a response. If an error occurs during the process, it logs the error
 * and sends an appropriate error response.
 *
 * @param req - The HTTP request object, which includes the context for the operation.
 * @param res - The HTTP response object used to send the response back to the client.
 *
 * @throws Will log and handle any errors that occur during the retrieval of system connections.
 */
export async function listSystemConnection(req: Request, res: Response) {
  try {
    const params: IGetObjectParameters = {
      folderNames: UclRootSpacename,
      kind: ObjectKind.uclSharedConnection, // ucl shared connection
      details: [
        "businessName",
        "name",
        "creation_date",
        "modification_date",
        "uclSystemInfo_uclSystemName",
        "uclSystemTenantId",
        "uclSystemInfo_systemType",
        "uclFormationId",
        "#consumedSpacesInUse",
      ],
    };
    const uclConnections: IRepositoryObject[] = await RepositoryObjectClient.getObject(req.context, params);
    res.status(StatusCodes.OK).send(uclConnections);
  } catch (err) {
    logError("List system-connection failed:", { context: req.context });
    sendErrorResponse(req.context, "ListSystemConnection", { err });
  }
}

/**
 * Authorizes spaces to consume a UCL shared connection
 * @param req - The HTTP request object containing the context and query parameters.
 * @param res - The HTTP response object used to send the response back to the client.
 * @throws Will log an error and send an error response if the authorization process fails.
 *
 * The function performs the following steps:
 * 1. Validates the input parameters `technical-name` and `spaces` from the query string.
 * 2. Calls the `authorizeSpacesToConsumeUclSharedConnection` function to process the authorization: retrieves the UCL shared connection,
 * and creates the necessary dependencies for the specified consumer spaces.
 * 3. Sends a success response with the result if the operation is successful.
 * 4. Logs the operation details for auditing purposes.
 * 5. Handles errors by logging them and sending an appropriate error response.
 */
export async function authorizeSystemConnectionToConsumerSpaces(req: Request, res: Response) {
  const context = req.context;
  try {
    // validate input parameters
    const uclSharedConnectionName = ParamValidator.mandatoryParam(
      context,
      "technical-name",
      ParamValidator.string(context, req.query?.["technical-name"])
    );
    const consumerSpaces = ParamValidator.mandatoryParam(
      context,
      "spaces",
      ParamValidator.string(context, req.query?.spaces)
    );

    // authorize the consumer spaces to consume the UCL shared connection
    const result = await authorizeSpacesToConsumeUclSharedConnection(context, uclSharedConnectionName, consumerSpaces);

    res.status(StatusCodes.OK).send(result);
    logInfo(
      `processing authorizeSystemConnection with technical-name ${uclSharedConnectionName} and spaces ${consumerSpaces}`,
      {
        context,
      }
    );
  } catch (err) {
    logError(["Authorize system-connection failed:", err], { context: req.context });
    sendErrorResponse(context, "AuthorizeSystemConnection", { err });
  }
}
export async function uploadCertificate(req: Request, res: Response) {
  try {
    if (!req.query.description || req.query.description === "null") {
      throw new Error("Description query parameter is required");
    }
    const busboy = Busboy({ headers: req.headers });

    busboy.on("file", (fieldname: any, stream: any, info: any) => {
      const { filename } = info;
      try {
        if (fieldname !== "file") {
          return busboy.emit("error", new Error(`Unsupported fieldname: ${fieldname}`));
        }
        let cert = "";

        stream.on("data", (chunk: any) => {
          cert += chunk.toString();
        });
        stream.on("end", async () => {
          const certificate = cert.replace(/\r/g, "");
          const data = {
            cdata: certificate,
            filename,
            comment: req.query.description,
          };
          req.body = data;
          try {
            const response = await createCertificate(req, res);
            res.status(StatusCodes.OK).send(response);
          } catch (err) {
            logError("Configuration certificate upload failed:", { context: req.context });
            sendErrorResponse(req.context, "uploadCertificate", { err });
          }
        });
      } catch (err) {
        busboy.emit("error", err);
      }
    });

    req.pipe(busboy);
  } catch (err) {
    logError(`Configuration certificate upload failed:`, { context: req.context });
    sendErrorResponse(req.context, "uploadCertificate", { err });
  }
}

export async function getConnection(req: Request, res: Response) {
  try {
    const { context } = req;
    const spaceId = req.params.space;
    const connectionName = req.params.name;
    const connectionsList = await new ApiConnectionService(context).getConnectionsFromSpace(spaceId);
    const response = connectionsList.filter((connection: any) => connection.name === connectionName);
    // Check if a connection was found
    if (response.length === 0) {
      // If no connection is found, return a 404 response with a message
      return res
        .status(StatusCodes.NOT_FOUND)
        .send({ message: `Connection '${connectionName}' not found in space '${spaceId}'.` });
    }
    res.status(StatusCodes.OK).send(response[0]);
  } catch (err) {
    logError(`Spaces Connections GET failed: `, { context: req.context });
    sendErrorResponse(req.context, "getConnections", { err });
  }
}

export async function getConnections(req: Request, res: Response) {
  try {
    const { context } = req;
    const spaceId = req.params.space;
    const allDetails = req.query.details;
    const nameQuery = req.query.name;
    const featuresQuery = req.query.features;
    const top = parseInt(req.query.$top as string, 10) || 10; // Default to 10 items if not specified
    const skip = parseInt(req.query.$skip as string, 10) || 0; // Default to 0 if not specified
    const connectionsList = await new ApiConnectionService(context).getConnectionsFromSpace(spaceId);
    let response;
    if (allDetails === "true") {
      response = connectionsList;
    } else if (nameQuery === "true") {
      response = getConnectionsName(connectionsList);
    } else if (featuresQuery === "true") {
      response = await getConnectionCapabilities(connectionsList, context);
    } else {
      response = getConnectionsMetadata(connectionsList);
    }

    // Sort by businessName property in ascending order
    response.sort((a: any, b: any) => a.businessName.localeCompare(b.businessName, "en", { sensitivity: "base" }));

    // For test compatibility, don't apply pagination if all items are requested
    let paginatedResponse;
    if (req.query.$top || req.query.$skip) {
      paginatedResponse = response.slice(skip, skip + top);
    } else {
      paginatedResponse = response;
    }

    res.status(StatusCodes.OK).send(paginatedResponse);
  } catch (err) {
    logError(`Spaces Connections GET failed: `, { context: req.context });
    sendErrorResponse(req.context, "getConnections", { err });
  }
}
export async function deleteConnection(req: Request, res: Response) {
  try {
    const { context } = req;
    const spaceId = req.params.space;
    const connectionName = req.params.name;
    const spaceGuid = await getSpaceUuidFromNameWorkaround(context, spaceId);
    const connectionsList = await new ApiConnectionService(context).getConnectionsFromSpace(spaceId);
    const selectedConnection = connectionsList.filter((connection) => connection.name === connectionName);
    const selectedId = selectedConnection[0].id;
    await deleteConnections(context, spaceGuid, [selectedId]);
    const result = { message: `Connection ${connectionName} was deleted` };
    res.status(StatusCodes.OK).send(result);
  } catch (err) {
    logError(`delete Connections failed: `, { context: req.context });
    sendErrorResponse(req.context, "deleteConnections", { err });
  }
}
export async function createConnection(req: Request, res: Response) {
  try {
    const { context } = req;
    const spaceId = req.params.space;
    const spaceGuid = await getSpaceUuidFromNameWorkaround(context, spaceId);
    await upsert(req, res, spaceGuid);
  } catch (err) {
    logError(`Create Connections failed: `, { context: req.context });
    sendErrorResponse(req.context, "createConnections", { err });
  }
}

export async function validateConnection(req: Request, res: Response) {
  try {
    const { context } = req;
    const spaceId = req.params.space;
    const spaceGuid = await getSpaceUuidFromNameWorkaround(context, spaceId);
    const connectionName = req.params.name;
    const connectionsList = await new ApiConnectionService(context).getConnectionsFromSpace(spaceId);
    const selectedConnection = connectionsList.filter((connection) => connection.name === connectionName);
    const selectedId = selectedConnection[0].id;
    const stat: any = await getConnectionStatus(context, spaceGuid, selectedId);
    res.status(StatusCodes.OK).send(buildResponse(stat));
  } catch (err) {
    logError(`Validate Connections failed: `, { context: req.context });
    sendErrorResponse(req.context, "validateConnections", { err });
  }
}

export async function editConnection(req: Request, res: Response) {
  try {
    const { context } = req;
    const spaceId = req.params.space;
    const connectionName = req.params.name;
    const connectionsList = await new ApiConnectionService(context).getConnectionsFromSpace(spaceId);
    const selectedConnection = connectionsList.filter((connection) => connection.name === connectionName);
    const selectedId = selectedConnection[0].id;
    const selectedTypeId = selectedConnection[0].typeId;
    req.query.typeId = selectedTypeId;
    const spaceGuid = await getSpaceUuidFromNameWorkaround(context, spaceId);
    await upsert(req, res, spaceGuid, selectedId);
  } catch (err) {
    logError(`Create Connections failed: `, { context: req.context });
    sendErrorResponse(req.context, "createConnections", { err });
  }
}

export async function listSpaces(req: Request, res: Response) {
  try {
    const result = await listNormalSpaces(req.context);
    res.status(StatusCodes.OK).send(result.map((r) => r.qualifiedName).sort());
  } catch (err) {
    logError(`Spaces GET failed: `, { context: req.context });
    sendErrorResponse(req.context, "spacesAPIGetFailed", { err });
  }
}

export async function delSpaces(req: Request, res: Response) {
  const { context } = req;
  let spaceId: string | undefined;
  const logical = req.query?.logical === "true";
  try {
    const json = getQueryBody(req);
    fixSpaceType(req.body);
    await validate("DELETE", json);
    spaceId = getSpaceId(context, json);
    logWarning(`DEL spaceId ${spaceId} logical ${logical}`, { context });
    await Api.getInstance().deleteContent(context, json as API.IDwcApiQuery, logical);
    res.status(StatusCodes.NO_CONTENT).send({});
  } catch (err) {
    logError([`Space DELETE failed logical ${logical} space ${req.query?.space ?? spaceId}`, err], { context });
    sendErrorResponse(context, "spaceAPIDeleteFailed", { err });
  }
}

export async function getSpaceUsers(req: Request, res: Response) {
  try {
    req.params.spaceid = req.params.space;
    let detail = false;
    const accept = req.headers.accept;
    if (accept === CONTENT_TYPE_USERS_DETAILS) {
      detail = true;
    }
    const usersList = await getScopeUsers(req, res);
    const response = detail ? usersList : usersList.map((u) => u.userName);
    res.status(StatusCodes.OK).send(response);
  } catch (err) {
    logError(`Spaces users GET failed: `, { context: req.context });
    sendErrorResponse(req.context, "getUsersbyScope", { err });
  }
}

export async function assignUsersToScope(req: Request, res: Response) {
  try {
    const { context } = req;
    req.params.spaceid = req.params.space;
    // input provided in the old format {id: "user1", role: "role1"}
    const hasRoleFormat = req.body.every((user: IUsersRole) => user.hasOwnProperty("role"));
    if (hasRoleFormat) {
      await validateUserScopeAssignment(req.body, userScopeAssign);
      req.body = transformRequestBody(req.body);
    } else {
      await validateUserScopeAssignment(req.body, userScopeAssignV2);
      req.body = transformRequestBodyV2(req.body);
    }
    await validateScopeRoles(context, req.params.spaceid, req.body);
    await assignUsersToScopeWithScopedRole(req, res);
  } catch (err) {
    logError(`Spaces users assignment failed: `, { context: req.context });
    sendErrorResponse(req.context, "assignUsersToScopeWithScopedRole", { err });
  }
}

export async function unassignUsersToScope(req: Request, res: Response) {
  try {
    const { context } = req;
    req.params.spaceid = req.params.space;
    await validateUserScopeAssignment(req.body, userScopeAssign);
    await validateScopeRoles(context, req.params.spaceid, req.body);
    req.body = transformRequestBody(req.body);
    await unassignUsersToScopeWithScopedRole(req, res);
  } catch (err) {
    logError(`Spaces users unassignment failed: `, { context: req.context });
    sendErrorResponse(req.context, "unassignUsersToScopeWithScopedRole", { err });
  }
}

export async function updateScopeUsers(req: Request, res: Response) {
  try {
    const { context } = req;
    req.params.spaceid = req.params.space;
    await validateUserScopeAssignment(req.body, updateScopeAssign);
    const assignedUsers = await getScopeUsers(req, res);
    const users = req.body as IUsersRoleList[];
    checkUsersListValidity(users, assignedUsers);
    const usersToBeAssigned = getUsersToBeAssigned(users, assignedUsers);
    const usersToBeUnassigned = getUsersToBeUnassigned(users, assignedUsers);
    await validateScopeRoles(context, req.params.spaceid, [...usersToBeAssigned, ...usersToBeUnassigned]);
    if (usersToBeAssigned.length > 0) {
      req.body = usersToBeAssigned;
      await assignUsersToScopeWithScopedRole(req, res);
    }
    if (usersToBeUnassigned.length > 0) {
      req.body = usersToBeUnassigned;
      await unassignUsersToScopeWithScopedRole(req, res);
    }
  } catch (err) {
    logError(`Spaces users update failed: `, { context: req.context });
    sendErrorResponse(req.context, "updateUsersToScopeWithScopedRole", { err });
  }
}

export async function addRemoveUsersScopedRole(req: Request, res: Response) {
  const { context } = req;
  let reqBody;
  try {
    await validateUserRoleAssignment(req.body, userRoleAdd);
    reqBody = await getAddRemoveUserAssignmentBody(req);
    const respBody = await postScopeUserAssignmentRoleOrScope(context, reqBody);
    res.status(StatusCodes.OK).send(respBody);
  } catch (err) {
    logError([`Add/remove users to role failed with:${reqBody}`, err], { context });
    sendErrorResponse(context, "RoleAssignPostFailed", { err });
  }
}

export async function readScopeRoleUsers(req: Request, res: Response) {
  const { context } = req;
  let role = req.params.role;
  const sacTenant = await getTenantIdInfo(context);
  try {
    role = await validateRole(context, sacTenant, role, true);

    const usersScopes: IScopeUserAssignment[] = await getScopeUserAssignmentRoleOrScope(context, role, UmsTypes.ROLE);
    const respBody = transformResBodyForScopedRoleListUsers(usersScopes);
    res.status(StatusCodes.OK).send(respBody);
  } catch (err) {
    logError([`Read role:${role} failed:`, err], { context });
    sendErrorResponse(context, "RoleReadFailed", { err });
  }
}

export async function readGlobalRoleUsers(req: Request, res: Response) {
  const { context } = req;
  let role = req.params.role;
  try {
    const sacTenant = await getTenantIdInfo(context);

    role = await validateRole(context, sacTenant, role, false);
    const body = transformReqBodyForReadRole(role);
    const roleResp = await crudRole(context, body, sacTenant);
    const users = getUsersFromRole(roleResp);

    res.status(StatusCodes.OK).send(users);
  } catch (err) {
    logError([`Read role:${role} users failed:`, err], { context });
    sendErrorResponse(context, "RoleUsersReadFailed", { err });
  }
}

export async function massAssignUserToGlobalRole(req: Request, res: Response) {
  const { context } = req;
  let reqBody;
  const role = req.params.role;
  const isAssign = req.originalUrl.includes("mass-delete") ? false : true;
  try {
    const sacTenant = await getTenantIdInfo(context);
    const roleWithPrefix = await validateRole(context, sacTenant, role, false);
    reqBody = getAssignUsersBody(req);
    await postUserAssignmentToGlobalRole(context, reqBody, roleWithPrefix, isAssign, sacTenant);
    if (isAssign) {
      res.status(StatusCodes.OK).send(`Users ${req.query.users} assigned to role ${role}`);
    } else {
      res.status(StatusCodes.OK).send(`Users ${req.query.users} unassigned from role ${role}`);
    }
  } catch (err) {
    logError([`Assign users to role:${role} failed:`, err], { context });
    sendErrorResponse(context, "RoleUsersAssignPostFailed", { err });
  }
}

function getAssignUsersBody(req: Request): IRoleUserAssignment {
  const users: string = (req.query.users as string) || "";

  if (users.trim() === "") {
    throw new CodedError("rolesApiValidation", "Users list can't be empty", StatusCodes.BAD_REQUEST);
  }
  const userList = users.split(",");

  const reqBody: IRoleUserAssignment = {
    type: UmsTypes.LIST,
    users: userList.map((user) => ({
      name: user,
      isTeam: false,
    })),
  };
  return reqBody;
}

async function getAddRemoveUserAssignmentBody(
  req: Request<any, any, any, any>
): Promise<[string, ScopeUserAssignments]> {
  const { context } = req;
  let role = req.params.role;
  const sacTenant = await getTenantIdInfo(context);
  role = await validateRole(context, sacTenant, role, true);

  const reqData = {
    type: UmsTypes.ROLE,
    data: {
      assign: [] as IUser[],
      unassign: [] as IUser[],
    },
  };
  const isAssign = req.originalUrl.includes("mass-delete") ? false : true;
  req.body.forEach((user: any) => {
    user.scopes.forEach((scope: any) => {
      if (isAssign) {
        reqData.data.assign.push({ id: user.id, name: user.id, scope });
      } else {
        reqData.data.unassign.push({ id: user.id, name: user.id, scope });
      }
    });
  });
  if (reqData.data.assign.length > 0) {
    await validUserScopeAssignment(context, role, sacTenant, reqData.data.assign);
  }

  return [role, reqData];
}

export async function massAssignScopesRoles(req: Request, res: Response) {
  const { context } = req;
  let role = req.params.role;
  const sacTenant = await getTenantIdInfo(context);
  try {
    role = await validateRole(context, sacTenant, role, true);

    const respBody = await postScopeAssignment(context, role, sacTenant, await generateAssignScopesBody(req), false);
    res.status(StatusCodes.OK).send(respBody);
  } catch (err) {
    logError([`Assign role:${role} failed:`, err], { context });
    sendErrorResponse(context, "RoleAssignPostFailed", { err });
  }
}

async function generateAssignScopesBody(req: Request): Promise<ScopeAssignments> {
  const isAssign = req.originalUrl.includes("mass-delete") ? false : true;
  const scopes: string = (req.query.scopes as string) || "";
  const type = req.query.type || "space";

  if (scopes.trim() === "") {
    throw new CodedError("spaceApiValidation", "Scopes list can't be empty", StatusCodes.BAD_REQUEST);
  }
  const scopeList = scopes.split(",");
  if (isAssign && type === "space") {
    await validSpaceList(req.context, scopeList);
  }

  let assignList: string[] = [];
  let unassignList: string[] = [];
  if (isAssign) {
    assignList = scopeList;
  } else {
    unassignList = scopeList;
  }

  const reqBody: ScopeAssignments = {
    type: UmsTypes.ROLE,
    data: {
      unassign: { scopes: unassignList },
      assign: { scopes: assignList },
    },
  };
  return reqBody;
}

function checkUsersListValidity(users: IUsersRoleList[], assignedUsers: IUserAssigned[]) {
  const assignUserIDs = assignedUsers.map((u) => u.userName);
  users.forEach((user) => {
    if (!assignUserIDs.includes(user.id)) {
      throw new CodedError("UserScope Assignment", `invalid users provided`, StatusCodes.BAD_REQUEST);
    }
  });
}

function transformRequestBody(users: IUsersRole[]): IUsersRole[] {
  const usersForUMSRequest = [] as IUsersRole[];
  users.forEach((user) => {
    usersForUMSRequest.push({
      id: user.id,
      name: user.id,
      role: user.role,
    });
  });
  return usersForUMSRequest;
}

function transformRequestBodyV2(users: IUsersRoleList[]): IUsersRole[] {
  const usersForUMSRequest = [] as IUsersRole[];
  users.forEach((user) => {
    user.roles.forEach((role) => {
      usersForUMSRequest.push({
        id: user.id,
        name: user.id,
        role,
      });
    });
  });
  return usersForUMSRequest;
}

function getUsersToBeAssigned(newUsers: IUsersRoleList[], assignedUsers: IUserAssigned[]): IUsersRole[] {
  const usersToBeAssigned = [] as IUsersRole[];
  newUsers.forEach((user) => {
    const userRoles = assignedUsers.find((u) => u.userName === user.id)?.roles.map((r) => r.id);
    user.roles.forEach((role) => {
      if (userRoles && !userRoles.includes(role)) {
        usersToBeAssigned.push({
          id: user.id,
          name: user.id,
          role,
        });
      }
    });
  });
  return usersToBeAssigned;
}

function getUsersToBeUnassigned(newUsers: IUsersRoleList[], assignedUsers: IUserAssigned[]): IUsersRole[] {
  const usersToBeUnassigned = [] as IUsersRole[];
  assignedUsers.forEach((user) => {
    const userRoles = newUsers.find((u) => u.id === user.userName)?.roles;
    if (userRoles) {
      user.roles
        .map((r) => r.id)
        .forEach((role) => {
          if (!userRoles.includes(role)) {
            usersToBeUnassigned.push({
              id: user.userName,
              name: user.userName,
              role,
            });
          }
        });
    }
  });
  return usersToBeUnassigned;
}

export async function readRoleScopes(req: Request, res: Response) {
  const { context } = req;
  let role = req.params.role;
  try {
    const sacTenant = await getTenantIdInfo(context);
    role = await validateRole(context, sacTenant, role, true);

    const scopes: string[] = await getScopeAssignment(context, role);
    res.status(StatusCodes.OK).send(scopes);
  } catch (err) {
    logError([`Read role:${role} failed:`, err], { context });
    sendErrorResponse(context, "RoleReadFailed", { err });
  }
}

function hasSpaceDefinition(data: API.DataWarehouseCloudAPI): boolean {
  return !!Object.values(data)[0].spaceDefinition;
}

export async function getCliDiscovery(req: Request, res: Response) {
  const { context } = req;
  try {
    res.status(StatusCodes.OK).send(await getDiscoveryFileContent(context));
  } catch (err) {
    logError(
      [`Error during retrieval of CLI discovery file: ${err.message}. CorrelationId: ${context.correlationId}`, err],
      { context }
    );
    sendErrorResponse(context, "cliDiscoveryFileRetrievalFailed", { err });
  }
}

export async function getWorkloadmanagement(req: Request, res: Response) {
  try {
    const customerHana = await CustomerHana.fromRequestContext(req.context);
    const workloadManagement = await customerHana.getWorkloadManagement();
    res.status(StatusCodes.OK).send(workloadManagement);
  } catch (err) {
    logError(`/api/v1/workloadmanagement GET failed`, { context: req.context });
    sendErrorResponse(req.context, "/api/v1/workloadmanagement GET failed", { err });
  }
}

export async function putWorkloadmanagement(req: Request, res: Response) {
  try {
    const requestData = { ...req.body };
    delete requestData.updateInProgress;
    await validateWorkloadManagementJson(requestData);
    const workloadManagement = requestData as IWorkloadManagement;
    const async = req?.query?.async === "true";
    const forceUpdate = req?.query?.forceUpdate === "true";
    if (async) {
      const backgroundContext = req.context.createNewForBackground();
      const customerHana = await CustomerHana.fromRequestContext(backgroundContext);
      void customerHana.updateWorkloadManagement(workloadManagement, true, forceUpdate);
      res.status(StatusCodes.ACCEPTED).send({});
    } else {
      const customerHana = await CustomerHana.fromRequestContext(req.context);
      await customerHana.updateWorkloadManagement(workloadManagement, false, forceUpdate);
      res.status(StatusCodes.OK).send({});
    }
  } catch (err) {
    logError(`/api/v1/workloadmanagement PUT failed`, { context: req.context });
    sendErrorResponse(req.context, "/api/v1/workloadmanagement PUT failed", { err });
  }
}
