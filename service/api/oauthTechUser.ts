/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [dw101_foundation_van]
 *
 * @module oauthTechUser - Technical User Purpose (TUP) OAuth Client
 * @description
 * This module provides a middleware to manage API access management for TUP OAuth Client.
 *
 * @see JIRA:FPA00-54042
 */

import { IRequestContext } from "@sap/deepsea-types";
import { sendError } from "@sap/dwc-context-checks";
import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";

import oauthTechnicalUserAllowlistedPaths from "../api/oauthTechUserAllowlistedPaths.json";
import { FeatureFlagProvider } from "../featureflags/FeatureFlagProvider";

/**
 * Path definition for allowlist.
 * @property pattern - Regular expression pattern for path.
 * @property featureFlag - name of a valid feature flag. If defined,
 * the feature flags must be active.
 *
 * @description
 * For URL paths in the allowlist, use the patterns below to define regular
 * expression for allowed path.
 *
 * @note
 * For any new pattern, please document in the example below.
 * For any new pattern, please add unit tests, including edge cases.
 * For any new path, be aware that the target will be in lowercase (case-insensitive).
 * For any new path, please perform TUP validation first.
 *
 * @example
 * `^/api/v1/tenantinformation/signavio$`
 *    exact match "/api/v1/tenantinformation/signavio"
 *
 * `^/api/v1/bwpce/.*$`
 *    start from "/api/v1/bwpc/", excluding "/api/v1/bwpce"
 *
 */
export interface Path {
  pattern: string;
  featureFlag?: string;
}

async function getEffectivePathPatterns(context: IRequestContext, paths: Path[]) {
  const effectivePathPatterns = [];
  for (const path of paths) {
    if (!path.featureFlag || (await FeatureFlagProvider.isFeatureActive(context, path.featureFlag))) {
      effectivePathPatterns.push(path.pattern);
    }
  }
  return effectivePathPatterns;
}

/**
 * @return middleware to manage API access for technical user purpose oauth client.
 *
 * @description
 * In case of a request from a technical user purpose oauth client, and the path is
 * not in the allowlist, the middleware will return a 403 Forbidden error.
 */
export function oauthTechnicalUserMiddleware(paths: Path[] = oauthTechnicalUserAllowlistedPaths) {
  return async (req: Request, res: Response, next: any) => {
    const context = req.context;
    if (context?.userInfo?.isOAuth) {
      if (!(await FeatureFlagProvider.isFeatureActive(context, "DWCO_API_TUP_SUPPORT"))) {
        return sendError(
          context,
          StatusCodes.FORBIDDEN,
          `Technical user purpose oauth client requests are not allowed.`
        );
      }

      if (!(await FeatureFlagProvider.isFeatureActive(context, "DWCO_TUP_OAUTH_CLIENT_ADOPTION"))) {
        const effectivePathPatterns = await getEffectivePathPatterns(context, paths);

        if (
          effectivePathPatterns.length === 0 || // No path is allowed
          !new RegExp(effectivePathPatterns.join("|")).test(context.httpRequest?.path?.toLowerCase() || "")
        ) {
          return sendError(
            context,
            StatusCodes.FORBIDDEN,
            `Path ${req.path} not found in allowlist for technical user purpose oauth client.`
          );
        }
      }
    }

    next();
  };
}
