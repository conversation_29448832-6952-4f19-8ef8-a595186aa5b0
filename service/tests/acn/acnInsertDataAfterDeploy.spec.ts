/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { ObjectKind, StatusType } from "@sap/deepsea-types";
import { assert } from "chai";
import { Router } from "express";
import sinon from "sinon";
import { registerApiImplementation as registerStandardApiImpl } from "../../repository/api/common/standardApi";
import { RepositoryObjectClient } from "../../repository/client/repositoryObjectClient";
import { registerApiImplementation as registerAcnApiImpl } from "../../repository/contentnetwork/acnApi";
import * as DPUtil from "../../repository/contentnetwork/util";
import { toAsyncRouter } from "../../routes/async-router";
import { sampleDataObj, sampleDataTable } from "../inputs/csnDataObject";
import { assertIsDefined } from "../lib/utility";
import { describeWithFF } from "../repository/featureFlagTestMatrix";
import * as RepoTestUtility from "../repository/repository.utility";

import {
  handleDataInsertion,
  handleDataInsertionResultAndUpdateJobStatus,
  wrapForLoadTest,
} from "../../repository/contentnetwork/dataInsertionAfterDeploy";
import {
  createDWCAdministratorContextWithTechUser,
  prepareAcnStubs,
  restoreAcnStubs,
} from "../utility/acnImportExport";

describeWithFF([{ DWCO_BDC_REPOSITORY_TRANSPORT_DATA: true }], "ACN Insert Data After Deploy", function () {
  this.timeout(240000);

  const router = toAsyncRouter(Router());
  let context;
  const preparations: RepoTestUtility.IPreparation[] = [];
  registerStandardApiImpl(router);
  registerAcnApiImpl(router);
  let spaceId;
  let tableId;
  let loadDataStub;
  let importedObjects: any, mockDependency: any;
  before(async function () {
    await RepoTestUtility.recordActiveConnections();
    context = createDWCAdministratorContextWithTechUser();
    await RepoTestUtility.setUpTenantSchema(await RepoTestUtility.callGetAdminDBClient(context), context);
    RepoTestUtility.prepareStubs();
    prepareAcnStubs();
    this.sandbox = sinon.createSandbox();
    loadDataStub = RepoTestUtility.addStub(wrapForLoadTest, "loadDataToDeployedEntity").resolves();

    const preparationSpace = await RepoTestUtility.prepareSpace(preparations, context, undefined, undefined);
    spaceId = preparationSpace.spaceUuid;
    // create a sample table in the preparation space
    const tableResult = await RepoTestUtility.createDocument(preparationSpace, sampleDataTable);
    assertIsDefined(tableResult);
    tableId = tableResult.smallData.id;
    // mock importedObjects
    importedObjects = {
      id: tableId,
      kind: ObjectKind.entity,
      folderId: spaceId,
    };
    mockDependency = [
      {
        properties: {
          "#isDataTransportAllowed": true,
        },
        id: tableId,
        kind: "entity",
        name: "smallData",
        folderId: spaceId,
        dependencies: [
          {
            id: "dataObjectId",
            qualifiedName: "smallData::data",
            content: {
              dependency: {},
              object: {
                id: "dataObjectId",
                qualifiedName: "smallData::data",
                content: {
                  dataObjects: {
                    "smallData::data": {
                      kind: "sap.dwc.dataObject",
                      "@DataWarehouse.enclosingObject": "AgeRange2",
                      format: "json",
                      data: "[]",
                    },
                  },
                },
              },
            },
          },
        ],
        inaccessibleDependencies: [],
      },
    ];
  });

  after(async function () {
    await RepoTestUtility.deleteSpaces(preparations);
    RepoTestUtility.restoreStubs();
    await context.finish();
    await RepoTestUtility.assertNoConnectionLeak();
    restoreAcnStubs();
    sinon.restore();
    this.sandbox.restore();
  });

  afterEach(async function () {
    sinon.resetHistory();
    RepoTestUtility.resetStubHistory();
    this.sandbox.reset();
    this.sandbox.resetHistory();
  });

  it.skip("should not insert data, if data is empty in dataObject", async function () {
    // mock getDependencies to return the table as dependency and not allowed for data insertion
    RepoTestUtility.addStub(RepositoryObjectClient, "getObjectDependencies").resolves(mockDependency);
    await handleDataInsertion(context, [importedObjects.id], spaceId, {} as any);
    assert.strictEqual(loadDataStub.notCalled, true);
  });

  it.skip("should not insert data, if data is not present in dataObject", async function () {
    delete mockDependency[0].dependencies[0].content.object.content.dataObjects["smallData::data"].data;
    RepoTestUtility.addStub(RepositoryObjectClient, "getObjectDependencies").resolves(mockDependency);
    await handleDataInsertion(context, [importedObjects.id], spaceId, {} as any);
    assert.strictEqual(loadDataStub.notCalled, true);
  });

  it.skip("should not insert data, if the object table is not allowed for data insertion", async function () {
    // mock getDependencies to return the table as dependency and not allowed for data insertion
    mockDependency[0].dependencies[0].content.object.content.dataObjects["smallData::data"].data =
      sampleDataObj.dataObjects["smallData::data"].data;
    mockDependency[0].properties["#isDataTransportAllowed"] = false;
    RepoTestUtility.addStub(RepositoryObjectClient, "getObjectDependencies").resolves(mockDependency);
    await handleDataInsertion(context, [importedObjects.id], spaceId, {} as any);
    assert.strictEqual(loadDataStub.notCalled, true);
  });

  it.skip("should not insert data, if the object table is deployed already and not an initial deployment", async function () {
    // mock getDependencies to return the table as dependency
    mockDependency[0].dependencies[0].content.object.content.dataObjects["smallData::data"].data =
      sampleDataObj.dataObjects["smallData::data"].data;
    mockDependency[0].properties["#isDataTransportAllowed"] = true;
    RepoTestUtility.addStub(RepositoryObjectClient, "getObjectDependencies").resolves(mockDependency);
    // mock version to return a version that is not the initial deployment greater than 1
    RepoTestUtility.addStub(RepositoryObjectClient, "getVersion").resolves([
      {
        id: tableId,
        version: 2,
        versionDate: "2025-06-03 16:44:29.505000000",
        modifier: "VIN",
        type: "DEPLOY",
      },
    ]);
    await handleDataInsertion(context, [importedObjects.id], spaceId, {} as any);
    assert.strictEqual(loadDataStub.notCalled, true);
  });

  it.skip("should insert data, if the data is present and table is allowed for insertion and deployed initially", async function () {
    // mock getDependencies to return the table as dependency
    mockDependency[0].dependencies[0].content.object.content.dataObjects["smallData::data"].data =
      sampleDataObj.dataObjects["smallData::data"].data;
    mockDependency[0].properties["#isDataTransportAllowed"] = true;
    RepoTestUtility.addStub(RepositoryObjectClient, "getObjectDependencies").resolves(mockDependency);
    // mock version to return a version that is not the initial deployment greater than 1
    RepoTestUtility.addStub(RepositoryObjectClient, "getVersion").resolves([
      {
        id: tableId,
        version: 1,
        versionDate: "2025-06-03 16:44:29.505000000",
        modifier: "VIN",
        type: "DEPLOY",
      },
    ]);
    await handleDataInsertion(context, [importedObjects.id], spaceId, {} as any);
    assert.strictEqual(loadDataStub.calledOnce, true);
  });

  it.skip("should get the status of the data insertion and store in jobStatus", async function () {
    // mock job status
    const jobStatus = await DPUtil.initializeJobStatus(context, StatusType.Executing, true);
    jobStatus.executionDetails!.steps = [];
    const dataInsertionResult = { spaceA: { objA: StatusType.Succeed }, spaceB: { objB: "Skipped" } };

    // case1: when data insertion is successful and some entities are skipped
    await handleDataInsertionResultAndUpdateJobStatus(context, jobStatus, dataInsertionResult);
    assert.strictEqual(jobStatus.executionDetails!.steps[0].status, StatusType.Succeed);
    assert.strictEqual(
      jobStatus.executionDetails!.steps[0].details!.msg,
      "Transported data was not loaded as data already exists in the relevant tables or no data to load."
    );
    assert.strictEqual(jobStatus.executionDetails!.steps[0].details!.desc, JSON.stringify(dataInsertionResult));

    // case2: when data insertion is successful
    dataInsertionResult.spaceB = { objB: StatusType.Succeed };
    await handleDataInsertionResultAndUpdateJobStatus(context, jobStatus, dataInsertionResult);
    assert.strictEqual(jobStatus.executionDetails!.steps[0].status, StatusType.Succeed);
    assert.strictEqual(jobStatus.executionDetails!.steps[0].details!.msg, "Transported data loaded.");
    assert.strictEqual(jobStatus.executionDetails!.steps[0].details!.desc, JSON.stringify(dataInsertionResult));

    // case3: when data insertion fails
    dataInsertionResult.spaceB = { objB: StatusType.Failed };
    await handleDataInsertionResultAndUpdateJobStatus(context, jobStatus, dataInsertionResult);
    assert.strictEqual(jobStatus.executionDetails!.steps[0].status, StatusType.Failed);
    assert.strictEqual(jobStatus.executionDetails!.steps[0].details!.msg, "Transported data could not be loaded.");
    assert.strictEqual(jobStatus.executionDetails!.steps[0].details!.desc, JSON.stringify(dataInsertionResult));
  });
});
