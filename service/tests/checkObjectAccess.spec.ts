/**
 * Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { X_SAP_BOC_SCOPE_PERMISSIONS } from "@sap/deepsea-sac/dist/headers";
import { defer } from "@sap/deepsea-sqlutils";
import { Activities, AuthType, IJsonObject } from "@sap/deepsea-types";
import assert from "assert";
import { expect } from "chai";
import { Router } from "express";
import { mockReq as sinonMockReq } from "sinon-express-mock";
import { registerApiImplementation as registerStandardApiImpl } from "../repository/api/common/standardApi";
import { RepositoryAccessControlClient } from "../repository/client/repositoryAccessControlClient";
import { RepositoryObjectClient } from "../repository/client/repositoryObjectClient";
import { RequestContext } from "../repository/security/requestContext";
import { TimeUnit } from "../repository/security/timeunit";
import { toAsync<PERSON>outer } from "../routes/async-router";
import { assertIsDefined, assertIsNotDefined } from "./lib/utility";
import umsSampleForSpacePermissions from "./repository/inputs/umsSampleForSpacePermissions.json";
import * as RepoTestUtility from "./repository/repository.utility";
import { assertNoConnectionLeak, recordActiveConnections } from "./repository/repository.utility";

describe("Check object access - repoClient", function () {
  this.timeout(TimeUnit.MINUTES.toMillis(2));
  const preparations: RepoTestUtility.IPreparation[] = [];
  const router = toAsyncRouter(Router());
  let modelerContext, adminContext: RequestContext | undefined;
  let oContent: IJsonObject;
  let contextIsSDPSet = defer();

  // register standard  API implementation
  registerStandardApiImpl(router);
  before(async () => {
    await recordActiveConnections();
    await RepoTestUtility.enforceFeatureFlags(
      {
        INFRA_DWC_TWO_TENANT_MODE: false,
      },
      adminContext
    );
    adminContext = RepoTestUtility.createDWCAdministratorContext();
    modelerContext = RepoTestUtility.createDWCModelerContext();
  });

  after(async () => {
    await modelerContext?.finish();
    await adminContext?.finish();
    await assertNoConnectionLeak();
  });

  beforeEach(async function () {
    // init stubs
    RepoTestUtility.restoreStubs();
    RepoTestUtility.prepareStubs(adminContext);
    const resourceId: string = RepoTestUtility.randomString(31).toUpperCase();
    oContent = {
      definitions: {
        E1: {
          kind: "entity",
          elements: {
            element1: { type: "cds.Integer" },
          },
          contentlibProperties: `{\"resourceId\":\"${resourceId}\"}`,
        },
      },
    };
    contextIsSDPSet = defer();
    RepoTestUtility.addStub(RequestContext, "isDummySpacePermission").callsFake((r: RequestContext) => {
      const result = r.isFeatureFlagActive("DWC_DUMMY_SPACE_PERMISSIONS");
      contextIsSDPSet.resolve();
      return result;
    });
  });

  afterEach(async function () {
    await RepoTestUtility.deleteSpaces(preparations);
    RepoTestUtility.restoreStubs();
  });

  it("Access Control Test 01 - Admin role using object.id", async function () {
    const preparation = await RepoTestUtility.prepareSpace(preparations, adminContext);
    assertIsDefined(preparation.spaceUuid);
    const res: any = await RepositoryObjectClient.createDocument(adminContext!, {
      documentType: "csn",
      folderId: preparation.spaceUuid,
      content: oContent,
    });
    assert.notStrictEqual(res, null);
    assert.notStrictEqual(res.E1.id, null);

    RepoTestUtility.resetStubHistory();
    const repoNonFilObjs = await RepositoryObjectClient.getObject(adminContext!, { ids: res.E1.id });
    let repoObjs = await RepositoryAccessControlClient.checkReadAccess(adminContext!, {
      objectUuids: repoNonFilObjs.map((o) => o.id),
      inSpaceManagement: false,
    });
    assert.strictEqual(repoObjs && repoObjs.length, 1);
    repoObjs = await RepositoryAccessControlClient.checkUpdateAccess(adminContext!, {
      objectUuids: repoNonFilObjs.map((o) => o.id),
      inSpaceManagement: false,
    });
    assert.strictEqual(repoObjs && repoObjs.length, 1);
    repoObjs = await RepositoryAccessControlClient.checkDeleteAccess(adminContext!, {
      objectUuids: repoNonFilObjs.map((o) => o.id),
      inSpaceManagement: false,
    });
    assert.strictEqual(repoObjs && repoObjs.length, 1);
  });

  it("Access Control Test 02 - Modeler role using object.id", async function () {
    await RepoTestUtility.prepareSpace(preparations, modelerContext);
    const preparation = preparations[preparations.length - 1];
    assertIsDefined(preparation.spaceUuid);
    const res: any = await RepositoryObjectClient.createDocument(modelerContext, {
      documentType: "csn",
      folderId: preparation.spaceUuid,
      content: oContent,
    });
    assert.notStrictEqual(res, null);
    assert.notStrictEqual(res.E1.id, null);

    RepoTestUtility.resetStubHistory();
    const repoNonFilObjs = await RepositoryObjectClient.getObject(modelerContext, { ids: res.E1.id });
    let repoObjs = await RepositoryAccessControlClient.checkReadAccess(modelerContext, {
      objectUuids: repoNonFilObjs.map((o) => o.id),
      inSpaceManagement: false,
    });
    assert.strictEqual(repoObjs && repoObjs.length, 1);
    repoObjs = await RepositoryAccessControlClient.checkUpdateAccess(modelerContext, {
      objectUuids: repoNonFilObjs.map((o) => o.id),
      inSpaceManagement: false,
    });
    assert.strictEqual(repoObjs && repoObjs.length, 1);
    repoObjs = await RepositoryAccessControlClient.checkDeleteAccess(modelerContext, {
      objectUuids: repoNonFilObjs.map((o) => o.id),
      inSpaceManagement: false,
    });
    assert.strictEqual(repoObjs && repoObjs.length, 1);
  });

  it("Access Control Test 03 - Admin role using space.id", async function () {
    adminContext = RepoTestUtility.createDWCAdministratorContext();
    await RepoTestUtility.prepareSpace(preparations, adminContext);
    const preparation = preparations[preparations.length - 1];
    assertIsDefined(preparation.spaceUuid);

    RepoTestUtility.resetStubHistory();
    let repoObjs = await RepositoryAccessControlClient.checkReadAccess(adminContext, {
      objectUuids: [preparation.spaceUuid],
      inSpaceManagement: false,
    });
    assert.strictEqual(repoObjs && repoObjs.length, 1);
    repoObjs = await RepositoryAccessControlClient.checkUpdateAccess(adminContext, {
      objectUuids: [preparation.spaceUuid],
      inSpaceManagement: false,
    });
    assert.strictEqual(repoObjs && repoObjs.length, 1);
    repoObjs = await RepositoryAccessControlClient.checkDeleteAccess(adminContext, {
      objectUuids: [preparation.spaceUuid],
      inSpaceManagement: false,
    });
    assert.strictEqual(repoObjs && repoObjs.length, 1);
  });

  it("Access Control Test 04 - Modeler role using space.id", async function () {
    modelerContext = RepoTestUtility.createDWCModelerContext();
    await RepoTestUtility.prepareSpace(preparations, modelerContext);
    const preparation = preparations[preparations.length - 1];
    assertIsDefined(preparation.spaceUuid);

    RepoTestUtility.resetStubHistory();
    const repoObjs = await RepositoryAccessControlClient.checkReadAccess(modelerContext, {
      objectUuids: [preparation.spaceUuid],
      inSpaceManagement: false,
    });
    assert.strictEqual(repoObjs && repoObjs.length, 1);
  });

  it("Should checkObjectAccess work for space permissions with decoded context.scopes", async function () {
    await RepoTestUtility.enforceFeatureFlags({ DWC_DUMMY_SPACE_PERMISSIONS: true });
    const context = await RequestContext.create(req, null as any);
    await contextIsSDPSet.promise;
    assertIsDefined(context.scopes);
    const checkSpaceIdsWithScopePermissionRead = await RepositoryAccessControlClient.checkReadAccess(context, {
      spaceNames: "SpaceA",
    });
    assert.strictEqual(checkSpaceIdsWithScopePermissionRead && checkSpaceIdsWithScopePermissionRead.length, 1);
  });

  it("Should checkObjectAccess NOT work for space permissions when space is not available in decoded context.scopes", async function () {
    await RepoTestUtility.enforceFeatureFlags({ DWC_DUMMY_SPACE_PERMISSIONS: true });
    const context = await RequestContext.create(req, null as any);
    await contextIsSDPSet.promise;
    assertIsDefined(context.scopes!.spaces.SpaceA);
    // verify context.scopes has no spaceB
    assertIsNotDefined(context.scopes!.spaces.SpaceB);
    const checkSpaceIdsWithScopePermissionRead = await RepositoryAccessControlClient.checkReadAccess(context, {
      spaceNames: "SpaceB",
    });
    assert.strictEqual(checkSpaceIdsWithScopePermissionRead && checkSpaceIdsWithScopePermissionRead.length, 0);
  });

  it("should throw error when hasPrivilegesOnScopeWithKind fails", async function () {
    const SPACE_NAME = "SPACE";
    const ERROR_MESSAGE = "Failed to perform hasPrivilegesOnScopeWithKind on read and SPACE with";
    await RepoTestUtility.enforceFeatureFlags({ DWC_DUMMY_SPACE_PERMISSIONS: true });
    const context = await RequestContext.create(req, null as any);
    await contextIsSDPSet.promise;
    assertIsDefined(context.scopes);
    RepoTestUtility.addStub(context, "hasPrivilegesOnScopeWithKind").throws(ERROR_MESSAGE);
    try {
      await RepositoryAccessControlClient.checkReadAccess(context, { spaceNames: [SPACE_NAME] });
      expect.fail();
    } catch (e) {
      expect(e.message).contains(ERROR_MESSAGE);
    }
  });

  it("Should test functional coverage for hasAtLeastOnePrivilegeOnScope", async function () {
    const context = await RequestContext.create(req, null as any);
    assertIsDefined(context.scopes);
    let testPrvilege = context.hasAtLeastOnePrivilegeOnScope(AuthType.DWC_SPACES, Activities.read);
    assert.strictEqual(testPrvilege, true);

    // remove privileges from context.scopes.spaces
    context.scopes.spaces = {};
    testPrvilege = context.hasAtLeastOnePrivilegeOnScope(AuthType.DWC_SPACES, Activities.read);
    assert.strictEqual(testPrvilege, false);
  });

  // as string
  const req = mockReq({
    get: (x) => {
      if (x === "Authorization") {
        return `Bearer ${mockJwt({ [X_SAP_BOC_SCOPE_PERMISSIONS]: umsSampleForSpacePermissions })}`;
      }
      if (x === X_SAP_BOC_SCOPE_PERMISSIONS) {
        return JSON.stringify(umsSampleForSpacePermissions);
      }
      return null;
    },
  });

  function mockReq(options: object = {}) {
    return sinonMockReq({
      headers: {},
      ...options,
    });
  }

  function mockJwt(options: object = {}) {
    const decoded = {
      jti: "abc",
      given_name: "Given Name",
      family_name: "Family Name",
      scope: ["scope1", "scope2"],
      cid: "cidabc",
      grant_type: "authorization_code",
      user_id: "useridabc",
      user_name: "User Name",
      email: "<EMAIL>",
      zid: "zidabc",
      "xs.user.attributes": { displayName: ["display name"] },
      ...options,
    };

    return `123.${Buffer.from(JSON.stringify(decoded)).toString("base64")}.abc`;
  }
});
