/** Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved. */
import { expect } from "chai";
import sinon from "sinon";
import { TaskRegistry } from "../../../task/taskRegistryIndex";
import { getAccessContext } from "../../repository/repository.utility";
import { overwriteFeatureFlags } from "../testutil";
import { getSystemTaskRegistryMap } from "../../../task/predefined/predefinedSystemSchedules";
import { ISystemTaskDefinition } from "../../../task/predefined/ISystemTaskDefinition";

describe.skip("predefinedSystemSchedules", function() {

  beforeEach(function() {
    this.sandbox = sinon.createSandbox();
  });
  afterEach(function() {
    this.sandbox.restore();
  });

  describe("enabledForTenant", function() {
    const technicalContext = getAccessContext({techUser: true});
    const systemTaskRegistry: Map<string, ISystemTaskDefinition> = getSystemTaskRegistryMap();

    systemTaskRegistry.forEach((stDefinition, key) => {
      it(`properties in systemTaskRegistry ${key} should correctly match with corresponding FF entry in the TaskFactorySettings`, async function() {
        const taskSetting = TaskRegistry.find(s => s.applicationId === stDefinition.applicationId && s.activity === stDefinition.activity);
        if (taskSetting && taskSetting.featureFlags && taskSetting.featureFlags.length !== 0) {
          const ffList = taskSetting?.featureFlags;
          const ffObject = {};
          ffList.forEach(ff => {
            ffObject[ff] = false;
          });
          await overwriteFeatureFlags(this.sandbox, technicalContext, ffObject);
          expect(stDefinition).to.have.property("enabledForTenant");
          expect(await stDefinition.enabledForTenant!(technicalContext, stDefinition)).equals(false);
        }
      });

    });
  });

});
