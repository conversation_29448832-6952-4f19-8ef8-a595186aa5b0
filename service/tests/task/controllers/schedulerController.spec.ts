/** Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved. */
import chai, { expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import sinon from "sinon";
import sinonChai from "sinon-chai";
import { SchedulerMiddleware, operateScheduleList } from "../../../task/controllers/schedulerController";
import { ISchedulerError, SchedulerError } from "../../../task/errors/SchedulerError";
import { Status } from "../../../task/logger/models";
import { ScheduleOperation } from "../../../task/logger/services/notifications/NotificationModels";
import { NotificationService } from "../../../task/logger/services/notifications/NotificationService";
import { Activity, ApplicationId } from "../../../task/models";
import { ActivationStatus, UiVariant } from "../../../task/scheduler/ScheduleEnums";
import { ScheduleCreationInput, ScheduleUpdateBase, StoredSchedule, WithNextRun } from "../../../task/scheduler/ScheduleTypes";
import { SchedulerValidation } from "../../../task/scheduler/SchedulerValidation";
import { TaskSchedulesApi } from "../../../task/scheduler/TaskSchedulesApi";
import { buildQueryMocks } from "../mocks/queryMocks";

describe("schedulerController", () => {
  chai.should();
  chai.use(chaiAsPromised);
  chai.use(sinonChai);

  let sandbox: sinon.SinonSandbox;

  beforeEach(async () => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe("SchedulerMiddleware.create", () => {
    let nextSpy: sinon.SinonSpy;
    const spaceid = "spaceId";
    const validBody = {
      applicationId: "TASK_FRAMEWORK",
      activity: "EXECUTE",
      objectId: "objectid",
      description: "objectid",
      activationStatus: "ENABLED",
      validFrom: "2024-08-29T00:00:00Z",
      validTo: "",
      cron: "3 9 */1 * *",
      uiVariant: "FORM",
      durationMin: 180
    };
    const validScheduleCreation: ScheduleCreationInput = {
      spaceId: spaceid,
      applicationId: ApplicationId.TASK_FRAMEWORK,
      activity: Activity.EXECUTE,
      objectId: "objectid",
      validFrom: new Date("2024-08-29T00:00:00Z"),
      validTo: undefined,
      activationStatus: ActivationStatus.ENABLED,
      description: "objectid",
      payload: undefined,
      uiVariant: UiVariant.FORM,
      durationMin: 180,
      cron: "3 9 */1 * *",
      tzName: null
    };
    let createStub: sinon.SinonStub;
    let parseStub: sinon.SinonStub;
    let validateStub: sinon.SinonStub;
    beforeEach(async () => {
      parseStub = sandbox.stub(SchedulerValidation.prototype, "parseCreateBody");
      parseStub.returns(validScheduleCreation);
      validateStub = sandbox.stub(SchedulerValidation.prototype, "validateInterval");
      createStub = sandbox.stub(TaskSchedulesApi.prototype, "createSchedule");
      createStub.resolves("schedule-id");
      nextSpy = sandbox.spy();
    });
    it("should create a schedule", async () => {
      const { req, res } = buildQueryMocks({ spaceid }, {}, { ...validBody });
      await SchedulerMiddleware.create(req, res, nextSpy);
      expect(res.locals.tf.status).to.be.equal(201);
      expect(res.locals.tf.schedule).to.be.deep.equal({ scheduleId: "schedule-id" });
      expect(createStub).to.be.calledWith(validScheduleCreation);
      expect(nextSpy).to.be.calledOnce;
    });
    it("should create a schedule with a timezone", async () => {
      const { req, res } = buildQueryMocks({ spaceid }, {}, { ...validBody, tzName: "Europe/Berlin" });
      const validScheduleCreationWithTz: ScheduleCreationInput = { ...validScheduleCreation, tzName: "Europe/Berlin" };
      parseStub.returns(validScheduleCreationWithTz);
      await SchedulerMiddleware.create(req, res, nextSpy);
      expect(res.locals.tf.status).to.be.equal(201);
      expect(res.locals.tf.schedule).to.be.deep.equal({ scheduleId: "schedule-id" });
      expect(createStub).to.be.calledWith(validScheduleCreationWithTz);
      expect(nextSpy).to.be.calledOnce;
    });
    it("should create a schedule with spaceid in locals", async () => {
      const { req, res } = buildQueryMocks({}, {}, { ...validBody });
      res.locals = { spaceid };
      await SchedulerMiddleware.create(req, res, nextSpy);
      expect(res.locals.tf.status).to.be.equal(201);
      expect(res.locals.tf.schedule).to.be.deep.equal({ scheduleId: "schedule-id" });
      expect(createStub).to.be.calledWith(validScheduleCreation);
    });
    it("should send error response if error is thrown during parsing", async () => {
      parseStub.throws(new SchedulerError(ISchedulerError.ERROR_INVALID_SCHEDULE_BODY, "parsing error"));
      const { req, res } = buildQueryMocks({ spaceid }, {}, { ...validBody, cron: "invalid" });
      await SchedulerMiddleware.create(req, res, nextSpy);
      expect(res.status).to.be.calledWith(400);
      expect(res.send.getCall(0).args[0]).to.be.haveOwnProperty("code", ISchedulerError.ERROR_INVALID_SCHEDULE_BODY);
      expect(createStub).to.not.be.called;
      expect(nextSpy).to.not.be.called
    });
    it("should send error response if TaskSchedulesApi.createSchedule rejects", async () => {
      createStub.rejects(new SchedulerError(ISchedulerError.ERROR_CREATE_SCHEDULE, "create error"));
      const { req, res } = buildQueryMocks({ spaceid }, {}, { ...validBody });
      await SchedulerMiddleware.create(req, res, nextSpy);
      expect(nextSpy).to.not.be.called;
      expect(res.status).to.be.calledWith(500);
      expect(res.send.getCall(0).args[0]).to.be.haveOwnProperty("code", ISchedulerError.ERROR_CREATE_SCHEDULE);
    });
    it("should send error response if validation fails", async () => {
      validateStub.throws(new SchedulerError(ISchedulerError.ERROR_INVALID_SCHEDULE_BODY, "invalid interval"));
      const { req, res } = buildQueryMocks({ spaceid }, {}, { ...validBody });
      await SchedulerMiddleware.create(req, res, nextSpy);
      expect(nextSpy).to.not.be.called;
      expect(res.status).to.be.calledWith(400);
      expect(res.send.getCall(0).args[0]).to.be.haveOwnProperty("code", ISchedulerError.ERROR_INVALID_SCHEDULE_BODY);
    });
  });
  describe("SchedulerMiddleware.get", () => {
    let nextSpy: sinon.SinonSpy;
    const spaceId = "spaceid";
    const scheduleId = "scheduleId";
    const user = "user";
    const storedSchedule: StoredSchedule & WithNextRun = {
      scheduleId,
      externalScheduleId: "externalScheduleId",
      spaceId,
      applicationId: ApplicationId.TASK_CHAINS,
      activity: Activity.RUN_CHAIN,
      objectId: "objectId",
      cron: "30 * * * *",
      tzName: "Europe/Berlin",
      validFrom: new Date(),
      validTo: new Date(),
      activationStatus: ActivationStatus.ENABLED,
      createdBy: user,
      changedBy: user,
      owner: user,
      uiVariant: UiVariant.EXPERT,
      payload: { test: "test" },
      description: "description",
      durationMin: undefined,
      nextRun: new Date(),
      changedAt: new Date(),
      createdAt: new Date(),
    };
    let getStub: sinon.SinonStub;
    beforeEach(async () => {
      getStub = sandbox.stub(TaskSchedulesApi.prototype, "getSchedule");
      getStub.resolves(storedSchedule);
      nextSpy = sandbox.spy();
    });
    it("should get a schedule", async () => {
      const { req, res } = buildQueryMocks({ spaceid: spaceId, scheduleid: scheduleId });
      await SchedulerMiddleware.get(req, res, nextSpy);
      expect(nextSpy).to.be.calledOnce;
      expect(res.locals.tf.status).to.be.equal(200);
      expect(res.locals.tf.schedule).to.be.deep.equal(storedSchedule);
      expect(getStub).to.be.calledWith(scheduleId);
    });
    it("should get a schedule with space in locals", async () => {
      const { req, res } = buildQueryMocks({ scheduleid: scheduleId });
      res.locals = { spaceid: spaceId };
      await SchedulerMiddleware.get(req, res, nextSpy);
      expect(nextSpy).to.be.calledOnce;
      expect(res.locals.tf.status).to.be.equal(200);
      expect(res.locals.tf.schedule).to.be.deep.equal(storedSchedule);
      expect(getStub).to.be.calledWith(scheduleId);
    });
    it("should send error response if TaskSchedulesApi.getSchedule rejects", async () => {
      getStub.rejects(new SchedulerError(ISchedulerError.ERROR_SCHEDULE_NOT_FOUND, "not found error"));
      const { req, res } = buildQueryMocks({ spaceid: scheduleId, scheduleid: scheduleId });
      await SchedulerMiddleware.get(req, res, nextSpy);
      expect(nextSpy).to.not.be.called;
      expect(res.status).to.be.calledWith(404);
      expect(res.send.getCall(0).args[0]).to.be.haveOwnProperty("code", ISchedulerError.ERROR_SCHEDULE_NOT_FOUND);
    });
  });
  describe("SchedulerMiddleware.getAll", () => {
    let nextSpy: sinon.SinonSpy;
    const spaceId = "spaceid";
    const user = "user";
    const storedSchedule: StoredSchedule & WithNextRun = {
      scheduleId: "scheduleId",
      externalScheduleId: "externalScheduleId",
      spaceId,
      applicationId: ApplicationId.TASK_CHAINS,
      activity: Activity.RUN_CHAIN,
      objectId: "objectId",
      cron: "30 * * * *",
      tzName: "Europe/Berlin",
      validFrom: new Date(),
      validTo: new Date(),
      activationStatus: ActivationStatus.ENABLED,
      createdBy: user,
      changedBy: user,
      owner: user,
      uiVariant: UiVariant.EXPERT,
      payload: { test: "test" },
      description: "description",
      durationMin: undefined,
      nextRun: new Date(),
      changedAt: new Date(),
      createdAt: new Date(),
    };
    let listSchedulesStub: sinon.SinonStub;
    let parseFilterStub: sinon.SinonStub;
    beforeEach(async () => {
      parseFilterStub = sandbox.stub(SchedulerValidation.prototype, "parseFiltersQuery");
      parseFilterStub.returns({});
      listSchedulesStub = sandbox.stub(TaskSchedulesApi.prototype, "listSchedules");
      listSchedulesStub.resolves([storedSchedule]);
      nextSpy = sandbox.spy();
    });
    it("should get all schedules in the space (no filters)", async () => {
      const { req, res } = buildQueryMocks({ spaceid: spaceId });
      await SchedulerMiddleware.getAll(req, res, nextSpy);
      expect(nextSpy).to.be.calledOnce;
      expect(res.locals.tf.status).to.be.equal(200);
      expect(res.locals.tf.scheduleList).to.be.deep.equal([storedSchedule]);
      expect(listSchedulesStub).to.be.calledWith({});
    });
    it("should get all schedules with space in locals (no filters)", async () => {
      const { req, res } = buildQueryMocks({});
      res.locals = { spaceid: spaceId };
      await SchedulerMiddleware.getAll(req, res, nextSpy);
      expect(nextSpy).to.be.calledOnce;
      expect(res.locals.tf.status).to.be.equal(200);
      expect(res.locals.tf.scheduleList).to.be.deep.equal([storedSchedule]);
      expect(listSchedulesStub).to.be.calledWith({});
    });
    it("should get all schedules for a space with filters", async () => {
      parseFilterStub.returns({ applicationId: ApplicationId.TASK_CHAINS });
      const { req, res } = buildQueryMocks({ spaceid: spaceId }, { applicationid: "TASK_CHAINS" });
      await SchedulerMiddleware.getAll(req, res, nextSpy);
      expect(nextSpy).to.be.calledOnce;
      expect(res.locals.tf.status).to.be.equal(200);
      expect(res.locals.tf.scheduleList).to.be.deep.equal([storedSchedule]);
      expect(listSchedulesStub).to.be.calledWith({ applicationId: ApplicationId.TASK_CHAINS });
    });
    it("should send empty array when no schedules found", async () => {
      listSchedulesStub.resolves([])
      const { req, res } = buildQueryMocks({ spaceid: spaceId });
      await SchedulerMiddleware.getAll(req, res, nextSpy);
      expect(nextSpy).to.be.calledOnce;
      expect(res.locals.tf.status).to.be.equal(200);
      expect(res.locals.tf.scheduleList).to.be.deep.equal([]);
      expect(listSchedulesStub).to.be.calledWith({});
    });
    it("should send error response if filter is invalid", async () => {
      parseFilterStub.throws(new SchedulerError(ISchedulerError.ERROR_INVALID_SCHEDULE_BODY, "invalid filter"));
      const { req, res } = buildQueryMocks({ spaceid: spaceId }, { applicationid: "TASK_CHAINS" });
      await SchedulerMiddleware.getAll(req, res, nextSpy);
      expect(nextSpy).to.not.be.called;
      expect(res.status).to.be.calledWith(400);
      expect(res.send.getCall(0).args[0]).to.be.haveOwnProperty("code", ISchedulerError.ERROR_INVALID_SCHEDULE_BODY);
      expect(listSchedulesStub).to.not.be.called;
    });
  });
  describe("SchedulerMiddleware.update", () => {
    const spaceId = "spaceid";
    let nextSpy: sinon.SinonSpy;
    const scheduleId = "schedule-id";
    const validBody = {
      applicationId: ApplicationId.TASK_FRAMEWORK,
      activity: Activity.EXECUTE,
      objectId: "object-id",
      description: "objectid",
      activationStatus: "ENABLED",
      validFrom: "2024-08-29T00:00:00Z",
      validTo: "",
      cron: "3 9 */1 * *",
      uiVariant: "FORM",
      durationMin: 180,
      tzName: "Europe/Berlin"
    };
    const updates: Partial<ScheduleUpdateBase> = {
      validFrom: new Date("2024-08-29T00:00:00Z"),
      activationStatus: ActivationStatus.ENABLED,
      description: "objectid",
      uiVariant: UiVariant.FORM,
      durationMin: 180,
      cron: "3 9 */1 * *",
      tzName: "Europe/Berlin"
    }
    let updateStub: sinon.SinonStub;
    let parseStub: sinon.SinonStub;
    beforeEach(async () => {
      parseStub = sandbox.stub(SchedulerValidation.prototype, "parseUpdateBody");
      parseStub.returns(updates);
      updateStub = sandbox.stub(TaskSchedulesApi.prototype, "updateSchedule");
      updateStub.resolves();
      nextSpy = sandbox.spy();
    });
    it("should update a schedule", async () => {
      const { req, res } = buildQueryMocks({ spaceid: spaceId, scheduleid: scheduleId }, {}, { ...validBody });
      await SchedulerMiddleware.update(req, res, nextSpy);
      expect(res.locals.tf.status).to.be.equal(204);
      expect(updateStub).to.be.calledWith(scheduleId, updates);
      expect(nextSpy).to.be.calledOnce;
    });
    it("should update a schedule with space in locals", async () => {
      const { req, res } = buildQueryMocks({ scheduleid: scheduleId }, {}, { ...validBody });
      res.locals = { spaceid: spaceId };
      await SchedulerMiddleware.update(req, res, nextSpy);
      expect(res.locals.tf.status).to.be.equal(204);
      expect(updateStub).to.be.calledWith(scheduleId, updates);
      expect(nextSpy).to.be.calledOnce;
    });
    it("should send error response if error is thrown during parsing", async () => {
      parseStub.throws(new SchedulerError(ISchedulerError.ERROR_INVALID_SCHEDULE_BODY, "parsing error"));
      const { req, res } = buildQueryMocks({ spaceid: spaceId, scheduleid: scheduleId }, {}, { ...validBody, cron: "invalid" });
      await SchedulerMiddleware.update(req, res, nextSpy);
      expect(res.status).to.be.calledWith(400);
      expect(res.send.getCall(0).args[0]).to.be.haveOwnProperty("code", ISchedulerError.ERROR_INVALID_SCHEDULE_BODY);
      expect(updateStub).to.not.be.called;
      expect(nextSpy).to.not.be.called;
    });
    it("should send error response if TaskSchedulesApi.updateSchedule rejects", async () => {
      updateStub.rejects(new SchedulerError(ISchedulerError.ERROR_UPDATE_SCHEDULE, "update error"));
      const { req, res } = buildQueryMocks({ spaceid: spaceId, scheduleid: scheduleId }, {}, { ...validBody });
      await SchedulerMiddleware.update(req, res, nextSpy);
      expect(res.status).to.be.calledWith(500);
      expect(res.send.getCall(0).args[0]).to.be.haveOwnProperty("code", ISchedulerError.ERROR_UPDATE_SCHEDULE);
      expect(nextSpy).to.not.be.called;
    });
  });
  describe("SchedulerMiddleware.delete", () => {
    const spaceId = "spaceid";
    const scheduleId = "schedule-id";
    let deleteStub: sinon.SinonStub;
    let nextSpy: sinon.SinonSpy;
    beforeEach(async () => {
      deleteStub = sandbox.stub(TaskSchedulesApi.prototype, "deleteSchedule");
      deleteStub.resolves(true);
      nextSpy = sandbox.spy();
    });
    it("should delete a schedule", async () => {
      const { req, res } = buildQueryMocks({ spaceid: spaceId, scheduleid: scheduleId });
      await SchedulerMiddleware.remove(req, res, nextSpy);
      expect(res.locals.tf.status).to.be.equal(204);
      expect(deleteStub).to.be.calledWith(scheduleId);
      expect(nextSpy).to.be.calledOnce;
    });
    it("should delete a schedule with space in locals", async () => {
      const { req, res } = buildQueryMocks({ scheduleid: scheduleId });
      res.locals = { spaceid: spaceId };
      await SchedulerMiddleware.remove(req, res, nextSpy);
      expect(res.locals.tf.status).to.be.equal(204);
      expect(deleteStub).to.be.calledWith(scheduleId);
      expect(nextSpy).to.be.calledOnce;
    });
    it("should send success response if schedule is not deleted", async () => {
      deleteStub.resolves(false);
      const { req, res } = buildQueryMocks({ spaceid: spaceId, scheduleid: scheduleId });
      await SchedulerMiddleware.remove(req, res, nextSpy);
      expect(res.locals.tf.status).to.be.equal(204);
      expect(deleteStub).to.be.calledWith(scheduleId);
      expect(nextSpy).to.be.calledOnce;
    });
    it("should send error response if TaskSchedulesApi.deleteSchedule rejects", async () => {
      deleteStub.rejects(new SchedulerError(ISchedulerError.ERROR_SCHEDULE_NOT_FOUND, "not found error"));
      const { req, res } = buildQueryMocks({ spaceid: spaceId, scheduleid: scheduleId });
      await SchedulerMiddleware.remove(req, res, nextSpy);
      expect(res.status).to.be.calledWith(404);
      expect(res.send.getCall(0).args[0]).to.be.haveOwnProperty("code", ISchedulerError.ERROR_SCHEDULE_NOT_FOUND);
      expect(nextSpy).to.not.be.called;
    });
  });
  describe("SchedulerMiddleware.updateOwner", () => {
    const spaceId = "spaceid";
    const scheduleId = "schedule-id";
    let updateOwnerStub: sinon.SinonStub;
    let nextSpy: sinon.SinonSpy;
    beforeEach(async () => {
      updateOwnerStub = sandbox.stub(TaskSchedulesApi.prototype, "updateScheduleOwner");
      updateOwnerStub.resolves();
      nextSpy = sandbox.spy();
    });
    it("should update schedule owner", async () => {
      const { req, res } = buildQueryMocks({ spaceid: spaceId, scheduleid: scheduleId }, {}, {});
      await SchedulerMiddleware.updateOwner(req, res, nextSpy);
      expect(res.locals.tf.status).to.be.equal(204);
      expect(updateOwnerStub).to.be.calledWith(scheduleId);
      expect(nextSpy).to.be.calledOnce;
    });
    it("should update schedule owner with space in locals", async () => {
      const { req, res } = buildQueryMocks({ scheduleid: scheduleId }, {}, {});
      res.locals = { spaceid: spaceId };
      await SchedulerMiddleware.updateOwner(req, res, nextSpy);
      expect(res.locals.tf.status).to.be.equal(204);
      expect(updateOwnerStub).to.be.calledWith(scheduleId);
      expect(nextSpy).to.be.calledOnce;
    });
    it("should send error response if TaskSchedulesApi.updateScheduleOwner rejects", async () => {
      updateOwnerStub.rejects(new SchedulerError(ISchedulerError.ERROR_SCHEDULE_NOT_FOUND, "not found error"));
      const { req, res } = buildQueryMocks({ spaceid: spaceId, scheduleid: scheduleId }, {}, {});
      await SchedulerMiddleware.updateOwner(req, res, nextSpy);
      expect(res.status).to.be.calledWith(404);
      expect(res.send.getCall(0).args[0]).to.be.haveOwnProperty("code", ISchedulerError.ERROR_SCHEDULE_NOT_FOUND);
      expect(nextSpy).to.not.be.called;
    });
  });
  describe("operateScheduleList", () => {
    const spaceId = "spaceid";
    const scheduleIds = ["schedule-id-1", "schedule-id-2"];
    let pauseStub: sinon.SinonStub;
    let resumeStub: sinon.SinonStub;
    let updateOwnerStub: sinon.SinonStub;
    let deleteStub: sinon.SinonStub;
    let notificationStub: sinon.SinonStub;
    beforeEach(async () => {
      notificationStub = sandbox.stub(NotificationService.prototype, "notifyScheduleOperation");
      notificationStub.resolves();
      pauseStub = sandbox.stub(TaskSchedulesApi.prototype, "pauseSchedule");
      pauseStub.resolves();
      resumeStub = sandbox.stub(TaskSchedulesApi.prototype, "resumeSchedule");
      resumeStub.resolves();
      updateOwnerStub = sandbox.stub(TaskSchedulesApi.prototype, "updateScheduleOwner");
      updateOwnerStub.resolves();
      deleteStub = sandbox.stub(TaskSchedulesApi.prototype, "deleteSchedule");
      deleteStub.resolves(true);
    });
    afterEach(() => {
      notificationStub.resetHistory();
      pauseStub.resetHistory();
      resumeStub.resetHistory();
      updateOwnerStub.resetHistory();
      deleteStub.resetHistory();
    });
    it("should send error response if operation is invalid", async () => {
      const { req, res } = buildQueryMocks({ spaceid: spaceId, operation: "invalid" }, {}, { scheduleIds });
      req.method = "POST";
      await operateScheduleList(req, res);
      expect(res.status).to.be.calledWith(400);
      expect(res.send.getCall(0).args[0]).to.be.equal("Wrong operation for this request: invalid.");
      expect(pauseStub).to.not.be.called;
      expect(resumeStub).to.not.be.called;
      expect(updateOwnerStub).to.not.be.called;
      expect(deleteStub).to.not.be.called;
      expect(notificationStub).to.not.be.called;
    });
    it("should send error response if request method does not match operation", async () => {
      const { req, res } = buildQueryMocks({ spaceid: spaceId, operation: "pause" }, {}, { scheduleIds });
      req.method = "POST";
      await operateScheduleList(req, res);
      expect(res.status).to.be.calledWith(400);
      expect(res.send.getCall(0).args[0]).to.be.equal("Wrong operation for this request: pause.");
      expect(pauseStub).to.not.be.called;
      expect(resumeStub).to.not.be.called;
      expect(updateOwnerStub).to.not.be.called;
      expect(deleteStub).to.not.be.called;
      expect(notificationStub).to.not.be.called;
    });
    it("should send error response if scheduleIds array is empty", async () => {
      const { req, res } = buildQueryMocks({ spaceid: spaceId, operation: "pause" }, {}, { scheduleIds: [] });
      req.method = "PUT";
      await operateScheduleList(req, res);
      expect(res.status).to.be.calledWith(404);
      expect(res.send.getCall(0).args[0]).to.be.equal("No scheduleIds provided");
      expect(pauseStub).to.not.be.called;
      expect(resumeStub).to.not.be.called;
      expect(updateOwnerStub).to.not.be.called;
      expect(deleteStub).to.not.be.called;
      expect(notificationStub).to.not.be.called;
    });
    it("should pause schedules", async () => {
      const { req, res } = buildQueryMocks({ spaceid: spaceId, operation: "pause" }, {}, { scheduleIds });
      req.method = "PUT";
      await operateScheduleList(req, res);
      expect(res.status).to.be.calledWith(202);
      expect(pauseStub).to.be.calledTwice;
      expect(notificationStub).to.be.calledWith({ status: Status.COMPLETED, operation: ScheduleOperation.PAUSE, spaceId, totalCount: 2, failedCount: 0 });
    });
    it("should resume schedules", async () => {
      const { req, res } = buildQueryMocks({ spaceid: spaceId, operation: "resume" }, {}, { scheduleIds });
      req.method = "PUT";
      await operateScheduleList(req, res);
      expect(res.status).to.be.calledWith(202);
      expect(resumeStub).to.be.calledTwice;
      expect(notificationStub).to.be.calledWith({ status: Status.COMPLETED, operation: ScheduleOperation.RESUME, spaceId, totalCount: 2, failedCount: 0 });
    });
    it("should update owner of schedules", async () => {
      const { req, res } = buildQueryMocks({ spaceid: spaceId, operation: "change_owner" }, {}, { scheduleIds });
      req.method = "PUT";
      await operateScheduleList(req, res);
      expect(res.status).to.be.calledWith(202);
      expect(updateOwnerStub).to.be.calledTwice;
      expect(notificationStub).to.be.calledWith({ status: Status.COMPLETED, operation: ScheduleOperation.CHANGE_OWNER, spaceId, totalCount: 2, failedCount: 0 });
    });
    it("should delete schedules", async () => {
      const { req, res } = buildQueryMocks({ spaceid: spaceId, operation: "delete" }, {}, { scheduleIds });
      req.method = "POST";
      await operateScheduleList(req, res);
      expect(res.status).to.be.calledWith(202);
      expect(deleteStub).to.be.calledTwice;
      expect(notificationStub).to.be.calledWith({ status: Status.COMPLETED, operation: ScheduleOperation.DELETE, spaceId, totalCount: 2, failedCount: 0 });
    });
    it("should notify the failure if one TaskSchedulesApi.pauseSchedule rejects", async () => {
      pauseStub.onSecondCall().rejects(new SchedulerError(ISchedulerError.ERROR_SCHEDULE_NOT_FOUND, "not found error"));
      const { req, res } = buildQueryMocks({ spaceid: spaceId, operation: "pause" }, {}, { scheduleIds });
      req.method = "PUT";
      await operateScheduleList(req, res);
      expect(res.status).to.be.calledWith(202);
      expect(notificationStub).to.be.calledWith({ status: Status.FAILED, operation: ScheduleOperation.PAUSE, spaceId, totalCount: 2, failedCount: 1 });
    });
  });
});
