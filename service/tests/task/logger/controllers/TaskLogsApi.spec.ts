/** Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved. */

import chai, { expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import sinon from "sinon";
import sinon<PERSON>hai from "sinon-chai";
import { DbClient } from "../../../../lib/DbClient";
import { IRequestContext } from "../../../../repository/security/common/common";
import { TaskChainRun } from "../../../../task/chains/TaskChainRun";
import { TaskChainRunNode, TaskChainRunNodesWithLogs } from "../../../../task/chains/TaskChainRunNode";
import { TaskChainRunNodesHistory, TaskChainRunNodesHistoryWithLogs } from "../../../../task/chains/TaskChainRunNodesHistory";
import { ChainCsn } from "../../../../task/chains/models/ChainCsn";
import { TaskChainDetails, TaskLogsApi } from "../../../../task/logger/controllers/TaskLogsApi";
import * as HideParentTaskFFs from "../../../../task/logger/isHideParentTaskEnabled";
import { ContentSemantic, ITaskLogMessage, Severity, Status } from "../../../../task/logger/models";
import { IParentTask, ITaskLogHeader, ItaskLogWithMetrics } from "../../../../task/logger/models/ITaskLog";
import { LabelName, MetricName, TaskMetrics } from "../../../../task/logger/models/ITaskMetrics";
import { Substatus } from "../../../../task/logger/models/Substatus";
import { DisplayNameService } from "../../../../task/logger/services/DisplayNameService";
import * as EndTask from "../../../../task/logger/services/EndTask";
import { FailsafeTaskLoggerInternal } from "../../../../task/logger/services/logger/FailsafeTaskLoggerInternal";
import { RuntimeCalculator } from "../../../../task/logger/services/logger/RuntimeCalculator";
import { ITaskLogMessageBlob, TaskLogMessageBlobService } from "../../../../task/logger/services/logger/TaskLogMessageBlobService";
import { TaskLogger } from "../../../../task/logger/services/logger/TaskLogger";
import { TaskMetricService } from "../../../../task/logger/services/metrics/TaskMetricService";
import { NotifyingTaskLogger } from "../../../../task/logger/services/notifications/NotifyingTaskLogger";
import { Activity, ApplicationId } from "../../../../task/models";
import { TaskFactory } from "../../../../task/orchestrator/services/taskexecution/TaskFactory";
import { LockEntry, LockIdentifier, LockKey, LockedTask, TaskLock } from "../../../../task/orchestrator/services/tasklock";
import { OverwriteResponse } from "../../../../task/orchestrator/services/tasklock/OverwriteResponse";
import { ActivationStatus } from "../../../../task/scheduler/ScheduleEnums";
import * as HanaClient from "../../../../task/shared/db/HanaClient";
import { TaskFrameworkConstants, TaskFrameworkVirtualSpaces } from "../../../../task/shared/db/TaskFrameworkConstants";
import { getAccessContext } from "../../../repository/repository.utility";
import validLinearRuntime from "../../chains/sampledata/validLinear/runtime.json";
import { mockDbClient } from "../../mocks/DbClientMock";
import { ExecutableMock } from "../../mocks/ExecutableMock";

describe("TaskLogApi", () => {
  chai.should();
  chai.use(sinonChai);
  chai.use(chaiAsPromised);

  const context = getAccessContext();
  const mocklogHeader =
    {
      logId: 13,
      applicationId: ApplicationId.REMOTE_TABLES,
      activity: Activity.REPLICATE,
      objectId: "SomeTable",
      spaceId: "dummyspace",
      status: Status.FAILED,
      startTime: new Date(2021, 0, 31),
      user: "d00",
      scheduleId: "xyz",
      runId: "abc",
      externalInstanceId: undefined,
      subStatusCode: Substatus.SET_TO_FAILED,
      subStatus: "SET_TO_FAILED",
    } as ITaskLogHeader;

  let mockLogMessages: ITaskLogMessage[];
  before(function() {
    this.sandbox = sinon.createSandbox();
  });
  beforeEach(async function() {
    mockLogMessages =
      [
        {
          messageNumber: 1,
          severity: Severity.INFO,
          text: "Task 13 started",
          timestamp: new Date(2021, 0, 31),
          parameterValues: ["13"],
          link: { messageBundleKey: "someI18nKey", url: "http://some.url" },
        } as ITaskLogMessage,
        {
          messageNumber: 2,
          severity: Severity.WARNING,
          text: "Task 13 ended",
          timestamp: new Date(2021, 1, 1),
          parameterValues: ["13"],
        } as ITaskLogMessage,
      ];

    this.displayNamesStub = this.sandbox.stub(DisplayNameService.prototype, "replaceUserNameByDisplayName")
    this.displayNamesStub.callsFake(async (context, logs, spaceId) => (logs.map(log => ({ ...log, user: "Test User" }))));
    this.getTaskHeadersStub = this.sandbox.stub(TaskLogger.prototype, "getTaskLogHeaders");
    this.getTaskHeadersStub.resolves([mocklogHeader]);
    this.getLatestLogsStub = this.sandbox.stub(TaskLogger.prototype, "getLatestTaskLogs");
    this.getLatestLogsStub.resolves([mocklogHeader]);
    this.getTaskMessageStub = this.sandbox.stub(TaskLogger.prototype, "getTaskLogMessages");
    this.getParentStub = this.sandbox.stub(TaskLogger.prototype, "getParentTask");
    this.getLogsWithScheduleInfoStub = this.sandbox.stub(TaskLogger.prototype, "getLogsWithScheduleInfo");
    this.getAvailableBlobMessageMapStub = this.sandbox.stub(TaskLogMessageBlobService.prototype, "getAvailableBlobMessageMap");
    this.getAvailableJsonMessageListStub = this.sandbox.stub(TaskLogMessageBlobService.prototype, "getAvailableJsonMessageList");
    this.isHideParentTaskEnabledStub = this.sandbox.stub(HideParentTaskFFs, "isHideParentTaskEnabled");
    this.isHideParentTaskEnabledStub.resolves(true);
  });

  afterEach(async function() {
    await DisplayNameService.forgetCache()
    this.sandbox.restore();
  });

  describe("get and set authorizedSpaces", function() {
    it("should be able to set and get the authorized spaces", async function() {
      const api = new TaskLogsApi(context, "dummyspace1");
      api.authorizedSpaces = ["dummyspace1", "dummyspace2"];
      const actual = api.authorizedSpaces;
      expect(actual).to.deep.equal(["dummyspace1", "dummyspace2"]);
    });

    it("should return spaceId of the class instance in the authorized spaces if it is not set (undefined)", async function() {
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = api.authorizedSpaces;
      expect(actual).to.deep.equal(["dummyspace"]);
    });

    it("should throw error if setting authorized spaces does not contain the spaceId used to instantiate the TaskLogsApi class", async function() {
      const api = new TaskLogsApi(context, "dummyspace1");
      // "dummyspace1" is expected in the authorized spaces and hence error should be thrown
      expect(() => api.authorizedSpaces = ["dummyspace2", "dummyspace3"]).to.throw(
        "[TaskLogsApi][set authorizedSpaces] spaceIds must contain the spaceId used to instantiate the TaskLogsApi class."
      );
    });

    it("should not throw error if setting authorized spaces does not contain the spaceId used to instantiate the TaskLogsApi class and it's a virtual space", async function() {
      const api = new TaskLogsApi(context, TaskFrameworkVirtualSpaces.ECN);
      // "$$ecn$$" is not expected in the authorized spaces and hence no error should be thrown
      api.authorizedSpaces = ["dummyspace1", "dummyspace2"];
      const actual = api.authorizedSpaces;
      expect(actual).to.deep.equal(["dummyspace1", "dummyspace2"]);
    });

    it("should throw error if setting authorized spaces does not contain a spaceId", async function() {
      const api = new TaskLogsApi(context, "dummyspace");
      expect(() => api.authorizedSpaces = []).to.throw(
        "[TaskLogsApi][set authorizedSpaces] spaceIds must contain the spaceId used to instantiate the TaskLogsApi class."
      );
    });

    it("should throw error if setting authorized spaces is not done with an array", async function() {
      const api = new TaskLogsApi(context, "dummyspace");
      expect(() => api.authorizedSpaces = "someObject" as any).to.throw(
        "[TaskLogsApi][set authorizedSpaces] spaceIds must be an array of space names."
      );
    });
  });

  describe("getLogHeaders", function() {
    it("should return log headers for objectId, no messages, no locks", async function() {
      await DisplayNameService.forgetCache()
      const expected = { locks: [], logs: [{ ...mocklogHeader, user: "Test User" }] };
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = await api.getLogHeaders({ objectId: "SomeTable" });
      expect(this.getTaskHeadersStub).to.be.calledWith("dummyspace", { objectId: "SomeTable" });
      expect(this.displayNamesStub).calledOnce;
      expect(this.getParentStub).not.called;
      expect(this.getTaskMessageStub).not.called;
      expect(actual).to.deep.equal(expected);
    });

    it("should call getLogHeaders with includeVariant set to true when handling a variant.", async function() {
      await DisplayNameService.forgetCache()
      const expected = { locks: [], logs: [{ ...mocklogHeader, user: "Test User" }] };
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = await api.getLogHeaders({ objectId: "SomeTable" }, false, true);
      expect(this.getTaskHeadersStub).to.be.calledWith("dummyspace", { objectId: "SomeTable" });
      expect(this.getTaskHeadersStub).to.be.calledWithExactly("dummyspace", { objectId: "SomeTable" }, true);
      expect(this.displayNamesStub).calledOnce;
      expect(this.getParentStub).not.called;
      expect(this.getTaskMessageStub).not.called;
      expect(actual).to.deep.equal(expected);
    });
  });

  describe("getParentsOfLogIds", function() {
    it("should return parents for taskLogIds", async function() {
      const mockParent: IParentTask = {
        logId: 10,
        objectId: "parent_chain",
        spaceId: "dummyspace"
      };
      this.getParentStub.resolves(mockParent);
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = await api.getParentsOfLogIds([13]);
      expect(this.getParentStub).to.be.calledWith(13);
      expect(actual).to.be.instanceOf(Map);
      expect(actual.get(13)).to.deep.equal(mockParent);
    });
    it("should return undefined for taskLogIds with no parent", async function() {
      this.getParentStub.resolves(undefined);
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = await api.getParentsOfLogIds([13]);
      expect(this.getParentStub).to.be.calledWith(13);
      expect(actual).to.be.instanceOf(Map);
      expect(actual.get(13)).to.be.undefined;
    });
    it("should return an empty Map if no taskLogIds provided", async function() {
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = await api.getParentsOfLogIds([]);
      expect(this.getParentStub).not.called;
      expect(actual).to.be.instanceOf(Map);
      expect(actual.size).to.equal(0);
    });
    it("should return a Map with parents or undefined for taskLogIds", async function() {
      this.getParentStub.callsFake(async (logId: number) => {
        if (logId % 2 === 0) {
          return undefined;
        } else {
          const chainLogId = logId * 10;
          return { logId: chainLogId, objectId: `parent_chain_${chainLogId}`, spaceId: "dummyspace" };
        }
      });
      const api = new TaskLogsApi(context, "dummyspace");
      const logIds = Array.from({ length: 3 }, (_, i) => i + 1);
      const actual = await api.getParentsOfLogIds(logIds);
      expect(actual).to.be.instanceOf(Map);
      expect(actual.get(1)).to.deep.equal({ logId: 10, objectId: "parent_chain_10", spaceId: "dummyspace" });
      expect(actual.get(2)).to.be.undefined;
      expect(actual.get(3)).to.deep.equal({ logId: 30, objectId: "parent_chain_30", spaceId: "dummyspace" });
    });
    it("should hide parent task if space is not authorized and FF enabled", async function() {
      this.isHideParentTaskEnabledStub.resolves(true);
      this.getParentStub.callsFake(async (logId: number) => {
        const chainLogId = logId * 10;
        const parent = { logId: chainLogId, objectId: `parent_chain_${chainLogId}` };
        if (logId % 2 === 0) {
          return { ...parent, spaceId: "not_authorized_space" };
        } else {
          return { ...parent, spaceId: "dummyspace" };
        }
      });
      const api = new TaskLogsApi(context, "dummyspace");
      api.authorizedSpaces = ["dummyspace"];
      const logIds = Array.from({ length: 3 }, (_, i) => i + 1);
      const actual = await api.getParentsOfLogIds(logIds);
      expect(actual).to.be.instanceOf(Map);
      expect(actual.get(1)).to.deep.equal({ logId: 10, objectId: "parent_chain_10", spaceId: "dummyspace" });
      expect(actual.get(2)).to.deep.equal({ spaceId: "not_authorized_space", authorizedOnParent: false });
      expect(actual.get(3)).to.deep.equal({ logId: 30, objectId: "parent_chain_30", spaceId: "dummyspace" });
    });
  });

  describe("getMessagesForLogId", function() {
    it("should return log header and messages for taskLogId, no parent", async function() {
      this.getParentStub.resolves(undefined);
      this.getTaskMessageStub.resolves([{ ...mocklogHeader, messages: mockLogMessages }]);
      const expected = { ...mocklogHeader, messages: mockLogMessages, user: "Test User" };
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = await api.getMessagesForLogId(13);
      expect(this.getTaskMessageStub).to.be.calledWith("dummyspace", 13);
      expect(this.getParentStub).to.be.calledWith(13);
      expect(this.displayNamesStub).calledOnce;
      expect(actual).to.deep.equal(expected);
    });

    it("should return log header, messages and parent for taskLogId (Hide parent FF on)", async function() {
      const mockParent: IParentTask = {
        logId: 10,
        objectId: "parent_chain",
        spaceId: "dummyspace"
      };
      this.getParentStub.resolves(mockParent);
      this.getTaskMessageStub.resolves([{ ...mocklogHeader, messages: mockLogMessages }]);
      const expected = { ...mocklogHeader, messages: mockLogMessages, parent: mockParent, user: "Test User" };
      const api = new TaskLogsApi(context, "dummyspace");
      api.authorizedSpaces = ["dummyspace"];
      const actual = await api.getMessagesForLogId(13);
      expect(this.getTaskMessageStub).to.be.calledWith("dummyspace", 13);
      expect(this.getParentStub).to.be.calledWith(13);
      expect(this.displayNamesStub).calledOnce;
      expect(actual).to.deep.equal(expected);
    });

    it("should return log header, messages and parent for taskLogId (Hide parent FF off)", async function() {
      this.isHideParentTaskEnabledStub.resolves(false);
      const mockParent: IParentTask = {
        logId: 10,
        objectId: "parent_chain",
        spaceId: "dummyspace"
      };
      this.getParentStub.resolves(mockParent);
      this.getTaskMessageStub.resolves([{ ...mocklogHeader, messages: mockLogMessages }]);
      const expected = { ...mocklogHeader, messages: mockLogMessages, parent: mockParent, user: "Test User" };
      const api = new TaskLogsApi(context, "dummyspace");
      const getAuthorizedSpacesSpy = this.sandbox.spy(api, "authorizedSpaces", ["get"]);
      const actual = await api.getMessagesForLogId(13);
      expect(getAuthorizedSpacesSpy.get).to.be.not.called;
      expect(this.getTaskMessageStub).to.be.calledWith("dummyspace", 13);
      expect(this.getParentStub).to.be.calledWith(13);
      expect(this.displayNamesStub).calledOnce;
      expect(actual).to.deep.equal(expected);
      getAuthorizedSpacesSpy.get.restore();
    });

    it("should return log header, messages and only partial parent details for taskLogId if user is not authorized on parent task space", async function() {
      const mockParent: IParentTask = {
        logId: 10,
        objectId: "parent_chain",
        spaceId: "dummyparentspace"
      };
      this.getParentStub.resolves(mockParent);
      this.getTaskMessageStub.resolves([{ ...mocklogHeader, messages: mockLogMessages }]);
      const mockPartialParent: IParentTask = {
        spaceId: "dummyparentspace",
        authorizedOnParent: false
      };
      const expected = { ...mocklogHeader, messages: mockLogMessages, parent: mockPartialParent, user: "Test User" };
      const api = new TaskLogsApi(context, "dummyspace");
      // setting authorizedSpaces that excludes the parent space "dummyparentspace"
      api.authorizedSpaces = ["dummyspace"];
      const actual = await api.getMessagesForLogId(13);
      expect(this.getTaskMessageStub).to.be.calledWith("dummyspace", 13);
      expect(this.getParentStub).to.be.calledWith(13);
      expect(this.displayNamesStub).calledOnce;
      expect(actual).to.deep.equal(expected);
    });

    it("should return log header, messages and parent for taskLogId id authorizedSpaces is not set (undefined)", async function() {
      const mockParent: IParentTask = {
        logId: 10,
        objectId: "parent_chain",
        spaceId: "dummyspace"
      };
      this.getParentStub.resolves(mockParent);
      this.getTaskMessageStub.resolves([{ ...mocklogHeader, messages: mockLogMessages }]);
      const expected = { ...mocklogHeader, messages: mockLogMessages, parent: mockParent, user: "Test User" };
      const api = new TaskLogsApi(context, "dummyspace");
      // authorizedSpaces is not set in the class instance
      const actual = await api.getMessagesForLogId(13);
      expect(this.getTaskMessageStub).to.be.calledWith("dummyspace", 13);
      expect(this.getParentStub).to.be.calledWith(13);
      expect(this.displayNamesStub).calledOnce;
      expect(actual).to.deep.equal(expected);
    });

    it("should return log header, messages and parent for taskLogId if parent task space is virtual space", async function() {
      const mockParent: IParentTask = {
        logId: 10,
        objectId: "parent_chain",
        spaceId: TaskFrameworkVirtualSpaces.ECN
      };
      this.getParentStub.resolves(mockParent);
      this.getTaskMessageStub.resolves([{ ...mocklogHeader, messages: mockLogMessages }]);
      const expected = { ...mocklogHeader, messages: mockLogMessages, parent: mockParent, user: "Test User" };
      const api = new TaskLogsApi(context, "dummyspace");
      // setting authorizedSpaces that excludes the parent space "$$ecn$$" (valid)
      api.authorizedSpaces = ["dummyspace"];
      const actual = await api.getMessagesForLogId(13);
      expect(this.getTaskMessageStub).to.be.calledWith("dummyspace", 13);
      expect(this.getParentStub).to.be.calledWith(13);
      expect(this.displayNamesStub).calledOnce;
      expect(actual).to.deep.equal(expected);
    });

    it("should return undefined and not call get parent", async function() {
      this.getTaskMessageStub.resolves([]);
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = await api.getMessagesForLogId(13);
      expect(this.getParentStub).not.called;
      expect(actual).to.be.undefined;
    });

    it("should return messages with isBlobContentAvailable set to true only when a BLOB is available - WITHOUT contentSemantic", async function() {
      this.getTaskMessageStub.resolves([{ ...mocklogHeader, messages: mockLogMessages }]);
      this.getParentStub.resolves(undefined);
      this.getAvailableBlobMessageMapStub.resolves(new Map<number, string | null>().set(2, null)); // only the 2nd message will have blob content. And no contentSemantic
      this.getAvailableJsonMessageListStub.resolves(); // no JSON DATA.
      const api = new TaskLogsApi(context, "dummyspace");
      expect(mockLogMessages[0]).to.not.have.property("isBlobContentAvailable");
      expect(mockLogMessages[1]).to.not.have.property("isBlobContentAvailable");
      expect(mockLogMessages[0]).to.not.have.property("contentSemanticValue");
      expect(mockLogMessages[1]).to.not.have.property("contentSemanticValue");
      await api.getMessagesForLogId(13);
      expect(this.getAvailableBlobMessageMapStub).calledOnce;
      expect(this.getAvailableJsonMessageListStub).calledOnce;
      expect(mockLogMessages[0]).to.not.have.property("isBlobContentAvailable");
      expect(mockLogMessages[1]).to.have.property("isBlobContentAvailable").equals(true);
      expect(mockLogMessages[0]).to.not.have.property("contentSemanticValue");
      expect(mockLogMessages[1]).to.not.have.property("contentSemanticValue");
    });

    it("should return messages with isBlobContentAvailable set to true only when a BLOB is available - WITH contentSemantic", async function() {
      this.getTaskMessageStub.resolves([{ ...mocklogHeader, messages: mockLogMessages }]);
      this.getParentStub.resolves(undefined);
      this.getAvailableBlobMessageMapStub.resolves(new Map<number, string | null>().set(2, "sample_content_semantic")); // only the 2nd message will have blob content. Has contentSemantic
      this.getAvailableJsonMessageListStub.resolves(); // no JSON DATA.
      const api = new TaskLogsApi(context, "dummyspace");
      expect(mockLogMessages[0]).to.not.have.property("isBlobContentAvailable");
      expect(mockLogMessages[1]).to.not.have.property("isBlobContentAvailable");
      expect(mockLogMessages[0]).to.not.have.property("contentSemanticValue");
      expect(mockLogMessages[1]).to.not.have.property("contentSemanticValue");
      await api.getMessagesForLogId(13);
      expect(this.getAvailableBlobMessageMapStub).calledOnce;
      expect(this.getAvailableJsonMessageListStub).calledOnce;
      expect(mockLogMessages[0]).to.not.have.property("isBlobContentAvailable");
      expect(mockLogMessages[1]).to.have.property("isBlobContentAvailable").equals(true);
      expect(mockLogMessages[0]).to.not.have.property("contentSemanticValue");
      expect(mockLogMessages[1]).to.have.property("contentSemanticValue").equals("sample_content_semantic");
    });

    it("should return messages with isJsonDataAvailable set to true only when a JSON DATA is available", async function() {
      this.getTaskMessageStub.resolves([{ ...mocklogHeader, messages: mockLogMessages }]);
      this.getParentStub.resolves(undefined);
      this.getAvailableBlobMessageMapStub.resolves(); // no blob content.
      this.getAvailableJsonMessageListStub.resolves([1]); // only the 1st message will have JSON DATA.
      const api = new TaskLogsApi(context, "dummyspace");
      expect(mockLogMessages[0]).to.not.have.property("isJsonDataAvailable");
      expect(mockLogMessages[1]).to.not.have.property("isJsonDataAvailable");
      await api.getMessagesForLogId(13);
      expect(this.getAvailableBlobMessageMapStub).calledOnce;
      expect(this.getAvailableJsonMessageListStub).calledOnce;
      expect(mockLogMessages[0]).to.have.property("isJsonDataAvailable").equals(true);
      expect(mockLogMessages[1]).to.not.have.property("isJsonDataAvailable");
    });
  });

  describe("getTaskChainDetails", function() {
    beforeEach(async function() {
      const dbClientStub = mockDbClient.call(this, this.sandbox) as DbClient;
      this.sandbox.stub(HanaClient, "getNoCommitClient").resolves(dbClientStub);
      this.sandbox.stub(RuntimeCalculator, "inMilliseconds").returns(3600000);
    });
    const mockChainHeader =
      {
        logId: 13,
        applicationId: ApplicationId.TASK_CHAINS,
        activity: Activity.RUN_CHAIN,
        objectId: "chain_13",
        spaceId: "MY_SPACE",
        status: Status.RUNNING,
        startTime: new Date(2021, 0, 31),
        user: "d00",
        scheduleId: "xyz",
        runId: "abc",
        externalInstanceId: undefined,
        subStatusCode: undefined,
        subStatus: undefined,
      } as ITaskLogHeader;

    const mockChainNodes: TaskChainRunNodesWithLogs[] = [
      {
        NODE_ID: 1,
        TASK_LOG_ID: 21,
        START_TIME: new Date(2021, 0, 31),
        END_TIME: new Date(2021, 0, 31, 1),
        STATUS: Status.COMPLETED,
        SUB_STATUS_CODE: null
      } as unknown as TaskChainRunNodesWithLogs,
      {
        NODE_ID: 2,
        TASK_LOG_ID: 22,
        START_TIME: new Date(2021, 0, 31, 2),
        END_TIME: null,
        STATUS: Status.RUNNING,
        SUB_STATUS_CODE: null
      } as unknown as TaskChainRunNodesWithLogs,
      {
        NODE_ID: 3,
        TASK_LOG_ID: null,
        START_TIME: null,
        END_TIME: null,
        STATUS: null,
        SUB_STATUS_CODE: null
      } as unknown as TaskChainRunNodesWithLogs];

    const mockChainNodesHistory: TaskChainRunNodesHistoryWithLogs[] = [
      {
        NODE_ID: 2,
        RUN_NO: 1,
        TASK_LOG_ID: 20,
        START_TIME: new Date(2021, 0, 31, 1),
        END_TIME: new Date(2021, 0, 31, 2),
        STATUS: Status.FAILED,
        SUB_STATUS_CODE: Substatus.FORBIDDEN
      } as TaskChainRunNodesHistoryWithLogs];

    it(`should return the chain's log header, messages and children with their history`, async function() {
      this.sandbox.stub(TaskChainRun.prototype, "getRunModel").resolves({ json: ChainCsn.from({ taskchains: {} }), plan: validLinearRuntime });
      this.sandbox.stub(TaskChainRunNode.prototype, "getChainNodesLogHeaders").resolves(mockChainNodes);
      this.sandbox.stub(TaskChainRunNodesHistory.prototype, "getChainNodesHistory").resolves(mockChainNodesHistory);
      this.getParentStub.resolves(undefined);
      this.getTaskMessageStub.resolves([{ ...mockChainHeader, messages: mockLogMessages }]);

      const expected: TaskChainDetails = {
        ...mockChainHeader,
        user: "Test User",
        messages: mockLogMessages,
        children: [
          {
            applicationId: ApplicationId.REMOTE_TABLES,
            activity: Activity.REPLICATE,
            objectId: "Customers",
            spaceId: "MY_SPACE",
            status: Status.COMPLETED,
            logId: 21,
            nodeId: 1,
            startTime: new Date(2021, 0, 31),
            endTime: new Date(2021, 0, 31, 1),
            runTime: 3600000,
            retryHistory: []
          },
          {
            applicationId: ApplicationId.VIEWS,
            activity: Activity.PERSIST,
            objectId: "Products",
            spaceId: "MY_SPACE",
            status: Status.RUNNING,
            logId: 22,
            nodeId: 2,
            startTime: new Date(2021, 0, 31, 2),
            runTime: 3600000,
            retryHistory:
              [
                {
                  nodeId: 2,
                  runNo: 1,
                  logId: 20,
                  status: Status.FAILED,
                  subStatusCode: Substatus.FORBIDDEN,
                  subStatus: "FORBIDDEN",
                  runTime: 3600000,
                  startTime: new Date(2021, 0, 31, 1),
                  endTime: new Date(2021, 0, 31, 2)
                }
              ]
          },
          {
            applicationId: ApplicationId.DATA_FLOWS,
            activity: Activity.EXECUTE,
            objectId: "MY_FLOW",
            spaceId: "MY_SPACE",
            status: "NOT_TRIGGERED",
            logId: undefined,
            nodeId: 3,
            retryHistory: []
          }
        ]
      };
      const api = new TaskLogsApi(context, "MY_SPACE");
      const actual = await api.getTaskChainDetails(13);
      expect(actual).to.deep.equal(expected);
    });

    it("should return messages if a chain has no run", async function() {
      this.sandbox.stub(TaskChainRun.prototype, "getRunModel").resolves();
      this.getParentStub.resolves(undefined);
      this.getTaskMessageStub.resolves([{ ...mockChainHeader, messages: mockLogMessages }]);
      const expected: TaskChainDetails = {
        ...mockChainHeader,
        user: "Test User",
        messages: mockLogMessages,
        children: []
      };
      const api = new TaskLogsApi(context, "MY_SPACE");
      const actual = await api.getTaskChainDetails(13);
      expect(actual).to.deep.equal(expected);
    });

    it("should throw error if not a chain", async function() {
      this.getParentStub.resolves(undefined);
      this.getTaskMessageStub.resolves([{ ...mockChainHeader, applicationId: ApplicationId.VIEWS, messages: mockLogMessages }]);
      const api = new TaskLogsApi(context, "MY_SPACE");
      await expect(api.getTaskChainDetails(13)).eventually.rejectedWith("[TaskLogsApi][getTaskChainDetails] logId=13 is not a valid chain run.")
    });
  });

  describe("getMessageBlobContent", function() {
    beforeEach(function() {
      this.getTaskLogMessageBlobStub = this.sandbox.stub(TaskLogMessageBlobService.prototype, "getTaskLogMessageBlob");
    });

    it("should return the blob content object associated with a message as a type of ITaskLogMessageBlob - WITHOUT contentSemantic", async function() {
      const messageBlobContent = {
        logId: 13,
        messageNumber: 2,
        contentType: "json",
        contentBlob: {}
      } as ITaskLogMessageBlob;

      this.getTaskLogMessageBlobStub.resolves(messageBlobContent);
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = await api.getMessageBlobContent(13, 2);
      expect(this.getTaskLogMessageBlobStub).to.be.calledWith(2, undefined);
      expect(this.getTaskLogMessageBlobStub).calledOnce;
      expect(actual).to.deep.equal(messageBlobContent);
    });

    it("should return the blob content object associated with a message as a type of ITaskLogMessageBlob - WITH contentSemantic", async function() {
      const messageBlobContent = {
        logId: 13,
        messageNumber: 2,
        contentType: "json",
        contentBlob: {},
        jsonData: {},
        contentSemantic: "PlanVizXML"
      } as ITaskLogMessageBlob;

      this.getTaskLogMessageBlobStub.resolves(messageBlobContent);
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = await api.getMessageBlobContent(13, 2, ContentSemantic.PLAN_VIZ_XML);
      expect(this.getTaskLogMessageBlobStub).to.be.calledWith(2, "PlanVizXML");
      expect(this.getTaskLogMessageBlobStub).calledOnce;
      expect(actual).to.deep.equal(messageBlobContent);
    });
  });

  describe("getLatestLogHeaders", function() {
    it("should return latest log headers for objectId, no messages", async function() {
      const expected = { locks: [], logs: [{ ...mocklogHeader, user: "Test User" }] };
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = await api.getLatestLogHeaders({ applicationId: ApplicationId.REMOTE_TABLES });
      expect(this.getLatestLogsStub).to.be.calledWith("dummyspace", { applicationId: ApplicationId.REMOTE_TABLES });
      expect(this.displayNamesStub).calledOnce;
      expect(this.getParentStub).not.called;
      expect(this.getTaskMessageStub).not.called;
      expect(actual).to.deep.equal(expected);
    });
    it("should retrieve the most recent log headers for each object in thie space for multiple activities", async function() {
      const dbStub = this.sandbox.stub(TaskLogger.prototype, "getTopLatestTaskLogs");
      dbStub.resolves([mocklogHeader]);
      const expected = { locks: [], logs: [{ ...mocklogHeader, user: "Test User" }] };
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = await api.getTopLatestTaskLogs({ applicationId: ApplicationId.REMOTE_TABLES, activities: [Activity.REPLICATE], status: Status.FAILED });
      const callArgs = dbStub.getCall(0).args;
      expect(callArgs).to.be.deep.equal([{ spaceId: "dummyspace", applicationId: ApplicationId.REMOTE_TABLES, activities: [Activity.REPLICATE], status: Status.FAILED }]);
      expect(this.displayNamesStub).calledOnce;
      expect(this.getParentStub).not.called;
      expect(this.getTaskMessageStub).not.called;
      expect(actual).to.deep.equal(expected);
    });
    it("should call getTopLatestTaskLogs with all activities if activities is empty", async function() {
      const dbStub = this.sandbox.stub(TaskLogger.prototype, "getTopLatestTaskLogs");
      dbStub.resolves([mocklogHeader]);
      const api = new TaskLogsApi(context, "dummyspace");
      const filter = { applicationId: ApplicationId.REMOTE_TABLES, status: Status.FAILED };
      await api.getTopLatestTaskLogs(filter);
      const expectedActivities = [];
      const callArgs = dbStub.getCall(0).args;
      expect(callArgs).to.be.deep.equal([{ spaceId: "dummyspace", applicationId: ApplicationId.REMOTE_TABLES, activities: expectedActivities, status: Status.FAILED }]);
      expect(this.displayNamesStub).calledOnce;
      expect(this.getParentStub).not.called;
      expect(this.getTaskMessageStub).not.called;
    });
  });

  describe("updateLockedTasks", () => {
    const filter = {
      applicationId: ApplicationId.REMOTE_TABLES,
      objectId: "SomeTable"
    };
    const lockedTask: LockedTask = {
      spaceId: "dummyspace",
      applicationId: ApplicationId.REMOTE_TABLES,
      objectId: "SomeTable",
      logId: 12,
      activity: Activity.REPLICATE,
      scheduleId: undefined,
      lockKey: LockKey.WRITE,
      creationTime: new Date(2020, 1, 1),
      parameters: {
        tf: {
          isDirect: true,
          scheduleId: undefined
        }
      }
    };
    const identifier: Partial<LockIdentifier> = { spaceId: lockedTask.spaceId, applicationId: lockedTask.applicationId, objectId: lockedTask.objectId };

    const expectedLock = {
      lockKey: lockedTask.lockKey,
      applicationId: lockedTask.applicationId,
      objectId: lockedTask.objectId,
      spaceId: lockedTask.spaceId,
      logId: lockedTask.logId,
      creationTime: lockedTask.creationTime
    } as LockEntry;

    let fakeHandleEndTask;
    let endTaskSpy;
    let fakeTimers: sinon.SinonFakeTimers;

    beforeEach(async function() {
      fakeTimers = sinon.useFakeTimers({
        now: new Date(2021, 1, 1),
        shouldAdvanceTime: false,
      });
      this.getLocksStub = this.sandbox.stub(TaskLock.prototype, "getLockedTasks");
      this.createTaskStub = this.sandbox.stub(TaskFactory, "createExecutable");
      this.sandbox.stub(FailsafeTaskLoggerInternal.prototype, "logMessage");
      this.getTaskHeadersStub.resolves([mocklogHeader]);

      endTaskSpy = sinon.spy();
      fakeHandleEndTask = sinon.stub();
      // Spying the constructor:
      // * Import the file (module) as an object and treat the class EndTask as a property of that object
      // * Stub the object EndTask (property of EndTask) to call a fake function with a spy
      // With this we stub the constructor when called with *new* in the productive code.
      // https://github.com/sinonjs/sinon/issues/1892#issuecomment-787439171
      this.sandbox.stub(EndTask, "EndTask").callsFake((context, logger, task) => {
        endTaskSpy(logger);
        return {
          finish: (executeResult) => fakeHandleEndTask(executeResult),
        }
      });
    });
    afterEach(async function() {
      fakeTimers?.restore();
    });

    it("should return logs and locks, takeover = false", async function() {
      this.getLocksStub.resolves([lockedTask]);
      const executable = new ExecutableMock(
        true, undefined, undefined, 0, undefined,
        { takeover: false, status: Status.COMPLETED, endTime: new Date(2020, 12, 1) }
      );
      this.createTaskStub.resolves(executable);
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = await api.getLogHeaders(filter, true);
      expect(this.getLocksStub).to.be.calledWith(identifier);
      expect(actual).to.deep.equal({ locks: [expectedLock], logs: [{ ...mocklogHeader, user: "Test User" }] });
    });

    it("should refresh locks and return logs, takeover = true, locked task is direct", async function() {
      lockedTask.parameters = {
        tf: {
          isDirect: true,
          scheduleId: undefined
        }
      };
      this.getLocksStub.resolves([lockedTask]);
      const overwriteResponse: OverwriteResponse = { takeover: true, status: Status.FAILED, endTime: new Date(2020, 12, 1), subStatusCode: Substatus.STOPPED };
      const executable = new ExecutableMock(
        true, undefined, undefined, 0, undefined, overwriteResponse
      );
      this.createTaskStub.resolves(executable);
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = await api.getLogHeaders(filter, true);
      expect(this.getLocksStub).to.be.calledWith(identifier);
      expect(fakeHandleEndTask).to.be.calledWith({
        status: overwriteResponse.status,
        endTime: overwriteResponse.endTime,
        subStatusCode: overwriteResponse.subStatusCode
      });
      expect(actual).to.deep.equal({ locks: [], logs: [{ ...mocklogHeader, user: "Test User" }] });
      // tf.isDirect = true => Notifying logger is called
      expect(endTaskSpy.args[0][0]).instanceOf(NotifyingTaskLogger);
    });
    it("should refresh locks and return logs, takeover = true, locked task is scheduled", async function() {
      lockedTask.parameters = {
        tf: {
          isDirect: false,
          scheduleId: "xyz"
        }
      };
      this.getLocksStub.resolves([lockedTask]);
      const overwriteResponse: OverwriteResponse = { takeover: true, status: Status.COMPLETED, endTime: new Date(2020, 12, 1) };
      const executable = new ExecutableMock(
        true, undefined, undefined, 0, undefined, overwriteResponse
      );
      this.createTaskStub.resolves(executable);
      const api = new TaskLogsApi(context, "dummyspace");
      const actual = await api.getLogHeaders(filter, true);
      expect(this.getLocksStub).to.be.calledWith(identifier);
      expect(fakeHandleEndTask).to.be.calledWith({
        status: overwriteResponse.status,
        endTime: overwriteResponse.endTime
      });
      expect(actual).to.deep.equal({ locks: [], logs: [{ ...mocklogHeader, user: "Test User" }] });
      // tf.isDirect = false => Notifying logger should not be called
      expect(endTaskSpy.args[0][0]).not.instanceOf(NotifyingTaskLogger);
    });
    it("Parameters are passed properly to task constructor", async function() {
      lockedTask.parameters = {
        startTime: new Date("2023-10-31T08:00:00Z"),
        taskId: 123,
        taskName: "xyz",
        tf: {
          isDirect: false,
          notify: false,
          isOnlyUpdate: false
        }
      };
      this.getLocksStub.resolves([lockedTask]);
      const overwriteResponse: OverwriteResponse = { takeover: false };
      const executable = new ExecutableMock(
        true, undefined, undefined, 0, undefined, overwriteResponse
      );
      this.createTaskStub.resolves(executable);
      const api = new TaskLogsApi(context, "dummyspace");
      await api.getLogHeaders(filter, true);
      expect(this.createTaskStub).calledWith(sinon.match.any, lockedTask);
    })
  });

  describe("failed runs", () => {
    it("should return the description of a failed run", function() {
      expect(TaskLogsApi.getSubstatusDescription(Substatus.FAIL_CONSENT_EXPIRED)).equal(TaskFrameworkConstants.SubstatusDescription.FAIL_CONSENT_EXPIRED);
    });

    it("should check whether the substatus is a failed run", function() {
      expect(TaskLogsApi.isEarlyFailureSubstatus(Substatus.FAIL_CONSENT_NOT_AVAILABLE)).to.be.true;
      expect(TaskLogsApi.isEarlyFailureSubstatus(Substatus.SET_TO_FAILED)).to.be.false;
    });
  });

  describe("error handling", function() {
    it("should throw error if there is an unkown query filter", async function() {
      const filter = { scheduleId: "xyz" };
      const api = new TaskLogsApi(context, "dummyspace");
      await expect(api.getLatestLogHeaders(filter)).to.be.rejectedWith(/Either objectId, applicationId, or activity must be defined to filter the logs/);
    });
  });

  describe("TaskLogsWithScheduleInfo", function() {
    const fakeNow = new Date("2025-02-12T12:00:00Z"); // 2025-02-12 12:00:00 UTC => 2025-02-12 07:00:00 in New York
    const someTimeAgo = new Date(fakeNow.getTime() - 2 * 60 * 60 * 1000);
    let fakeTimers: sinon.SinonFakeTimers;
    let timerSandbox: sinon.SinonSandbox;
    afterEach(function() {
      fakeTimers.restore();
    });
    beforeEach(function() {
      timerSandbox = sinon.createSandbox();
      fakeTimers = timerSandbox.useFakeTimers({
        now: fakeNow,
        shouldAdvanceTime: false,
      });
    });
    after(function() {
      timerSandbox.restore();
    });
    const taskLogsWithScheduleInfo = {
      TASK_LOG_ID: 1,
      STATUS: Status.FAILED,
      START_TIME: someTimeAgo,
      USER: "user",
      END_TIME: fakeNow,
      SCHEDULE_ID: "scheduleId",
      APPLICATION_ID: ApplicationId.REMOTE_TABLES,
      ACTIVITY: Activity.REPLICATE,
      SPACE_ID: "Space1",
      OBJECT_ID: "Object1",
      CRON: "0 10 * * *", // every day at 10:00
      VALID_FROM: someTimeAgo,
      VALID_TO: null,
      ACTIVATION_STATUS: ActivationStatus.ENABLED,
      TZ_NAME: null
    };
    it("should get logs with next schedule run", async function() {
      this.getLogsWithScheduleInfoStub.resolves([taskLogsWithScheduleInfo])
      const [logs] = await TaskLogsApi.getLogsWithScheduleInfo(context, ["Space1", "Space2"], Status.FAILED, 5);
      expect(this.getLogsWithScheduleInfoStub).to.be.calledWith(["Space1", "Space2"], Status.FAILED, 5);
      expect(logs).to.have.deep.property("nextRun", new Date("2025-02-13T10:00:00Z")); // next day at 10:00 UTC
    });
    it("should throw if there is an error in getting the data", async function() {
      this.getLogsWithScheduleInfoStub.rejects(new Error("DB Error"));
      await expect(TaskLogsApi.getLogsWithScheduleInfo(context, ["Space1"], Status.FAILED, 5)).to.be.rejectedWith("DB Error");
    });
    it("should return logs without next schedule run when schedule is not active", async function() {
      this.getLogsWithScheduleInfoStub.resolves([{ ...taskLogsWithScheduleInfo, ACTIVATION_STATUS: ActivationStatus.DISABLED }]);
      const [logs] = await TaskLogsApi.getLogsWithScheduleInfo(context, ["Space1"], Status.FAILED, 5);
      expect(logs).to.not.have.property("nextRun");
    });
    it("should return logs with next schedule run in a different time zone", async function() {
      this.getLogsWithScheduleInfoStub.resolves([{ ...taskLogsWithScheduleInfo, TZ_NAME: "America/New_York" }]);
      const [logs] = await TaskLogsApi.getLogsWithScheduleInfo(context, ["Space1"], Status.FAILED, 5);
      expect(logs).to.have.deep.property("nextRun", new Date("2025-02-12T15:00:00Z")); // same day at 10:00 in New York
    });
  });

  describe("getLogsAndMetrics", function() {
    const metrics: TaskMetrics = {
      taskLogId: mocklogHeader.logId,
      metrics: [{
        metricId: 1,
        name: MetricName.MEMORY_CONSUMPTION_GIB,
        value: "test",
        labels: [{ name: LabelName.PARTITION, value: "valueTest" }],
      }]
    };

    beforeEach(function() {
      this.getMessagesForLogId = this.sandbox.stub(TaskLogsApi.prototype, "getMessagesForLogId");
      this.getMessagesForLogId.resolves({ ...mocklogHeader, messages: mockLogMessages })
      this.getMetricsList = this.sandbox.stub(TaskMetricService, "getMetricsList");
      this.getMetricsList.callsFake((context: IRequestContext, taskLogIds: number[]) => [metrics]);
    });

    it("should successfully return logs and metrics", async function() {
      const expected: ItaskLogWithMetrics = {
        logDetails: { ...mocklogHeader, messages: mockLogMessages },
        metrics
      }
      const api = new TaskLogsApi(context, "dummyspace");
      const result = await api.getLogsAndMetrics(mocklogHeader.logId)
      expect(this.getMessagesForLogId).to.be.calledWith(mocklogHeader.logId);
      expect(this.getMetricsList).to.be.calledWith(sinon.match.any, [mocklogHeader.logId]);
      expect(result).to.deep.equal(expected);
    });
  });
});
