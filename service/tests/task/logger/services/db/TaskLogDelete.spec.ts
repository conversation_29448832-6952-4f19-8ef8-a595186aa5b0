/** Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved. */

import { SqlTaggedTemplate } from "@sap/prom-hana-client/dist/sql";
import chai, { expect } from "chai";
import sinon from "sinon";
import sinonChai from "sinon-chai";
import { TaskLogDelete } from "../../../../../task/logger/services/db/TaskLogDelete";
import { Activity, ApplicationId } from "../../../../../task/models";
import { TaskFactory } from "../../../../../task/orchestrator/services/taskexecution/TaskFactory";
import * as HanaClient from "../../../../../task/shared/db/HanaClient";
import { TaskFrameworkConstants } from "../../../../../task/shared/db/TaskFrameworkConstants";
import { getTestContext } from "../../../../routes/c4s/internal_services/helper";
import { loadCustomAssertions } from "../../../equalIgnoreWhiteSpace";
import { mockDbClient } from "../../../mocks/DbClientMock";

describe("TaskLogDelete", () => {
  chai.use(sinonChai);
  let dbClientStub: any;
  let fakeExecuteStatement;
  let fakeExecuteProcedureStatement;

  loadCustomAssertions();
  beforeEach(async function() {
    this.sandbox = sinon.createSandbox();
    dbClientStub = {
      ...mockDbClient(this.sandbox),
      prepare: this.sandbox.stub(),
      execute: this.sandbox.stub(),
      executeProcedure: this.sandbox.stub(),
      commit: this.sandbox.stub(),
      rollback: this.sandbox.stub(),
    }
    this.sandbox.stub(HanaClient, "getNoCommitClient").resolves(dbClientStub);
    fakeExecuteStatement = dbClientStub.execute;
    fakeExecuteProcedureStatement = dbClientStub.executeProcedure;
  });
  afterEach(function() {
    this.sandbox.restore();
  });

  describe("Delete TaskLogs", () => {
    it("delete one task log header by logId", async function() {
      const dbService = await TaskLogDelete.fromContext(getTestContext());
      await dbService.deleteByLogIds("fake-space", [1133]);
      expect(fakeExecuteStatement).callCount(1)
      const firstCall = fakeExecuteStatement.args[0][0] as SqlTaggedTemplate;
      expect(firstCall.sql).equalIgnoreWhiteSpace(
        /* sql */`DELETE FROM "DWC_GLOBAL"."TASK_LOGS" WHERE SPACE_ID = ? AND TASK_LOG_ID IN (?)`
      );
      expect(firstCall.params).to.be.deep.equal(["fake-space", 1133]);
    });

    it("delete several task log headers by logId", async function() {
      const dbService = await TaskLogDelete.fromContext(getTestContext());
      await dbService.deleteByLogIds("fake-space", [111, 222, 333]);
      expect(fakeExecuteStatement).callCount(1)
      const firstCall = fakeExecuteStatement.args[0][0] as SqlTaggedTemplate;
      expect(firstCall.sql).equalIgnoreWhiteSpace(
        /* sql */`DELETE FROM "DWC_GLOBAL"."TASK_LOGS" WHERE SPACE_ID = ? AND TASK_LOG_ID IN (?, ?, ?)`
      );
      expect(firstCall.params).to.be.deep.equal(["fake-space", 111, 222, 333]);
    });

    it("delete TASK_LOGS in a space", async function() {
      const dbService = await TaskLogDelete.fromContext(getTestContext());
      await dbService.deleteBySpace("dummyspace");
      expect(fakeExecuteStatement).callCount(1)
      const firstCall = fakeExecuteStatement.args[0][0] as SqlTaggedTemplate;
      expect(firstCall.sql).equalIgnoreWhiteSpace(
        /* sql */`DELETE FROM
        "DWC_GLOBAL"."TASK_LOGS" AS l
        WHERE
          l.SPACE_ID = ?;`
      );
      expect(firstCall.params).to.be.deep.equal(["dummyspace"]);
    });
  });

  describe("Delete older logs", () => {
    it("Delete older logs", async function() {
      const context = getTestContext();
      this.sandbox.stub(TaskFactory, "getApplicationsWithLastRelevantRun")
        .returns([
          { applicationId: ApplicationId.INTELLIGENT_LOOKUP, activity: Activity.EXECUTE },
          { applicationId: ApplicationId.INTELLIGENT_LOOKUP, activity: Activity.DELETE_DATA }
        ]);
      const dbService = await TaskLogDelete.fromContext(context);
      await dbService.deleteOlderThan(new Date(1577876400 * 1000));
      const expectedJson = JSON.stringify({
        keepingLogs: [
          { applicationId: ApplicationId.INTELLIGENT_LOOKUP, activity: Activity.EXECUTE },
          { applicationId: ApplicationId.INTELLIGENT_LOOKUP, activity: Activity.DELETE_DATA }
        ]
      });
      expect(fakeExecuteProcedureStatement).callCount(1)
      expect(fakeExecuteProcedureStatement).calledWith(TaskFrameworkConstants.schema, "TASK_LOGS_DELETE", ["1577876400", expectedJson]);
    });
  });

  describe("Mark logs of deleted object as deleted in TASK_LOGS table and delete the associated locks from the TASK_LOCKS table", function() {
    it("should delete logs and locks of a chain and its non-repository children", async function() {
      const dbResultFromId = (id: number) => ({ TASK_LOG_ID: id });
      // A chain with 2 functional tasks under it. The chain was cancelled, one functional task ran successfully and the other was cancelled.
      const chainLogIds = [123, 321]; // The first logId is the chain run, the second is the chain cancel
      const functionalRunsLogIds = [456, 789];
      const functionalCancelLogIds = [987];
      const deletedChains = [
        { applicationId: ApplicationId.TASK_CHAINS, activity: Activity.RUN_CHAIN, objectId: "chain", spaceId: "test space" },
        { applicationId: ApplicationId.TASK_CHAINS, activity: Activity.CANCEL, objectId: "chain", spaceId: "test space" }
      ];
      fakeExecuteStatement.onCall(0).resolves(chainLogIds.map(dbResultFromId)); // The first call returns the logId of the repository object's run
      // Two api tasks ran under the chain, so the second call returns the logIds of the API runs
      fakeExecuteStatement.onCall(1).resolves(functionalRunsLogIds.map(dbResultFromId));
      // One of the API tasks was cancelled, so the third call returns the logId of the API cancel
      fakeExecuteStatement.onCall(2).resolves(functionalCancelLogIds.map(dbResultFromId));
      // The fourth call is to mark the logs as deleted (5 logs in total)
      fakeExecuteStatement.onCall(3).resolves(5);
      // The fifth call is to delete the locks of the logs, if any
      fakeExecuteStatement.onCall(4).resolves();
      const logDeleter = await TaskLogDelete.fromContext(getTestContext());
      const logsDeleted = await logDeleter.markAsDeleted(deletedChains);
      expect(logsDeleted).to.equal(5);
      expect(fakeExecuteStatement).callCount(5);

      const deleteRuns = fakeExecuteStatement.args[1][0] as SqlTaggedTemplate;
      expect(deleteRuns.sql).to.be.equalIgnoreWhiteSpace(
        /* sql */`SELECT TASK_LOG_ID FROM "DWC_GLOBAL"."${TaskFrameworkConstants.taskLogsTableName}" ts
          INNER JOIN
          JSON_TABLE(?, '$' COLUMNS ( SPACE_ID NVARCHAR(64) PATH '$.spaceId', CHAIN_NAME NVARCHAR(256) PATH '$.chainName', APPLICATION_ID NVARCHAR(256) PATH '$.applicationId', ACTIVITY NVARCHAR(256) PATH '$.activity')) jt
              ON ts.SPACE_ID = jt.SPACE_ID AND ts.EXTERNAL_INSTANCE_ID = jt.CHAIN_NAME AND ts.APPLICATION_ID = jt.APPLICATION_ID AND ts.ACTIVITY = jt.ACTIVITY
              WHERE IS_DELETED = false
              FOR UPDATE OF IS_DELETED IGNORE LOCKED;`);

      const deletedCancel = fakeExecuteStatement.args[2][0] as SqlTaggedTemplate;
      expect(deletedCancel.sql).to.be.equalIgnoreWhiteSpace(
        /* sql */`SELECT TASK_LOG_ID FROM "DWC_GLOBAL"."${TaskFrameworkConstants.taskLogsTableName}" ts
                INNER JOIN
                JSON_TABLE(?, '$' COLUMNS ( APPLICATION_ID NVARCHAR(256) PATH '$.applicationId', ACTIVITY NVARCHAR(256) PATH '$.activity')) jt
                ON ts.APPLICATION_ID = jt.APPLICATION_ID AND ts.ACTIVITY = jt.ACTIVITY
                WHERE EXTERNAL_INSTANCE_ID IN (?, ?)
                AND IS_DELETED = false
                FOR UPDATE OF IS_DELETED IGNORE LOCKED;`);
      const [_actualJson, ...actualLogIds] = deletedCancel.params;
      expect(actualLogIds).to.deep.equal(functionalRunsLogIds);
      // Mark the logs as deleted should be called with all 5 logIds
      const markAllAsDeleted = fakeExecuteStatement.args[3][0] as SqlTaggedTemplate;
      expect(markAllAsDeleted.params).to.deep.equal([...chainLogIds, ...functionalRunsLogIds, ...functionalCancelLogIds]);
    });

    it("Queries to mark logs as deleted and to delete the locks should be correct", async function() {
      fakeExecuteStatement.onFirstCall().resolves([{ TASK_LOG_ID: 1 }, { TASK_LOG_ID: 2 }, { TASK_LOG_ID: 3 }]);
      const deletedObject = { applicationId: ApplicationId.REMOTE_TABLES, activity: Activity.REPLICATE, objectId: "test object", spaceId: "test space" };
      const logDelete = await TaskLogDelete.fromContext(getTestContext());
      await logDelete.markAsDeleted([deletedObject]);

      expect(fakeExecuteStatement).callCount(3);
      const executedSql1 = fakeExecuteStatement.args[0][0] as SqlTaggedTemplate;

      const normalizedExecutedSql1 = executedSql1.sql.replace(/\s+/g, ' ').trim();

      const expectedSql1 = `SELECT TASK_LOG_ID FROM "DWC_GLOBAL"."${TaskFrameworkConstants.taskLogsTableName}" ts
                      INNER JOIN
                        JSON_TABLE(?, '$' COLUMNS (
                          SPACE_ID NVARCHAR(64) PATH '$.spaceId',
                          OBJECT_ID NVARCHAR(256) PATH '$.objectId',
                          APPLICATION_ID NVARCHAR(256) PATH '$.applicationId',
                          ACTIVITY NVARCHAR(256) PATH '$.activity')) jt
                          ON ts.SPACE_ID = jt.SPACE_ID
                          AND ((jt.APPLICATION_ID = ? AND
                          ts.OBJECT_ID LIKE_REGEXPR ('^' || jt.OBJECT_ID || '(:\\w+)?\\z'))
                          OR (jt.APPLICATION_ID != ? AND ts.OBJECT_ID = jt.OBJECT_ID))
                          AND ts.APPLICATION_ID = jt.APPLICATION_ID
                          AND ts.ACTIVITY = jt.ACTIVITY
                      WHERE IS_DELETED = false
                      FOR UPDATE OF IS_DELETED IGNORE LOCKED;`.replace(/\s+/g, ' ').trim();

      expect(normalizedExecutedSql1).equalIgnoreWhiteSpace(expectedSql1);
      const fakeExecuteStatementParams = fakeExecuteStatement.args[0][0].params;
      const parsedFirstParam = JSON.parse(fakeExecuteStatementParams[0]);

      const parsedParams = [
        parsedFirstParam,
        fakeExecuteStatementParams[1],
        fakeExecuteStatementParams[2]
      ];

      const expectedParams = [
        [
          {
            applicationId: deletedObject.applicationId,
            activity: Activity.REPLICATE,
            objectId: deletedObject.objectId,
            spaceId: deletedObject.spaceId
          }
        ],
        ApplicationId.LOCAL_TABLE_VARIANT, ApplicationId.LOCAL_TABLE_VARIANT];
      expect(parsedParams).to.deep.equal(expectedParams);

      const statement2 = fakeExecuteStatement.args[1][0] as SqlTaggedTemplate;
      expect(statement2.sql)
        .to.be.equalIgnoreWhiteSpace(
          /* sql */`UPDATE "DWC_GLOBAL"."${TaskFrameworkConstants.taskLogsTableName}"
          SET
            IS_DELETED = true
          WHERE
            TASK_LOG_ID IN (?, ?, ?)`
        );
      expect(statement2.params).deep.equal([1, 2, 3]);

      const statement3 = fakeExecuteStatement.args[2][0] as SqlTaggedTemplate;
      expect(statement3.sql)
        .to.be.equalIgnoreWhiteSpace(
          /* sql */`DELETE FROM "DWC_GLOBAL"."${TaskFrameworkConstants.taskLocksTableName}"
          WHERE
            TASK_LOG_ID IN (?, ?, ?)`
        );
      expect(statement3.params).deep.equal([1, 2, 3]);
    });

    it("Should not check for update and delete queries when select query returns empty", async function() {
      fakeExecuteStatement.onCall(0).returns([]);
      const deletedObject = { applicationId: ApplicationId.REMOTE_TABLES, activity: Activity.REPLICATE, objectId: "test object", spaceId: "test space" };
      const logDelete = await TaskLogDelete.fromContext(getTestContext());
      await logDelete.markAsDeleted([deletedObject]);

      expect(fakeExecuteStatement).callCount(1);
      const statement1 = fakeExecuteStatement.args[0][0] as SqlTaggedTemplate;

      const normalizedExecutedSql1 = statement1.sql.replace(/\s+/g, ' ').trim();

      const expectedSql1 = `SELECT TASK_LOG_ID FROM "DWC_GLOBAL"."${TaskFrameworkConstants.taskLogsTableName}" ts
                      INNER JOIN
                        JSON_TABLE(?, '$' COLUMNS (
                          SPACE_ID NVARCHAR(64) PATH '$.spaceId',
                          OBJECT_ID NVARCHAR(256) PATH '$.objectId',
                          APPLICATION_ID NVARCHAR(256) PATH '$.applicationId',
                          ACTIVITY NVARCHAR(256) PATH '$.activity')) jt
                          ON ts.SPACE_ID = jt.SPACE_ID
                          AND ((jt.APPLICATION_ID = ? AND
                          ts.OBJECT_ID LIKE_REGEXPR ('^' || jt.OBJECT_ID || '(:\\w+)?\\z'))
                          OR (jt.APPLICATION_ID != ? AND ts.OBJECT_ID = jt.OBJECT_ID))
                          AND ts.APPLICATION_ID = jt.APPLICATION_ID
                          AND ts.ACTIVITY = jt.ACTIVITY
                      WHERE IS_DELETED = false
                      FOR UPDATE OF IS_DELETED IGNORE LOCKED;`.replace(/\s+/g, ' ').trim();

      expect(normalizedExecutedSql1).to.be.equalIgnoreWhiteSpace(expectedSql1);
      const fakeExecuteStatementParams = fakeExecuteStatement.args[0][0].params;
      const parsedFirstParam = JSON.parse(fakeExecuteStatementParams[0]);

      const parsedParams = [
        parsedFirstParam,
        fakeExecuteStatementParams[1],
        fakeExecuteStatementParams[2]
      ];

      const expectedParams = [
        [
          {
            applicationId: deletedObject.applicationId,
            activity: Activity.REPLICATE,
            objectId: deletedObject.objectId,
            spaceId: deletedObject.spaceId
          }
        ],
        ApplicationId.LOCAL_TABLE_VARIANT, ApplicationId.LOCAL_TABLE_VARIANT];
      expect(parsedParams).to.deep.equal(expectedParams);
    });
  });
});
