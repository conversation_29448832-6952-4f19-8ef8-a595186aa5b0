/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
// FILEOWNER: [DW101_data_access_control]

import { TimeUnit } from "@sap/deepsea-utils";
import { expect, use } from "chai";
import chaiAsPromised from "chai-as-promised";
import { DbClient } from "../../../../../lib/DbClient";
import { getClient } from "../../../../../meta/access";
import { RequestContext } from "../../../../../repository/security/requestContext";
import { CustomerHana, SupportUserRole } from "../../../../../reuseComponents/spaces/src";
import { IPreparation, restoreStubs } from "../../../../repository/repository.utility";
import { deleteSpaces, prepareSpaceUnstub } from "../../space.utility";

use(chaiAsPromised);

describe.skip(__filename, async function () {
  this.timeout(TimeUnit.MINUTES.toMillis(1));

  enum ErrorMessages {
    INSUFFICIENT_PRIVILEGE = "insufficient privilege",
    INVALID_OBJECT = "invalid name of function or procedure",
    INVALID_SCHEMA = "invalid schema name",
    NOT_SUPPORT_USER = "You do not have the privileges to run this procedure. Please contact Support.",
    WRONG_PARAMETERS = "wrong number or types of parameters in call",
  }

  const USER_NAME = "RANDOM_USER";
  const PROTECTED_OBJECT = "CONDITION_PROVIDER";
  const FILTER_STRING_TRUE = "1 = 1";
  const FILTER_STRING_FALSE = "1 = 1 AND 1 = 0";

  const preparations: IPreparation[] = [];

  let context: RequestContext;
  let customerHana: CustomerHana;
  let spaceId: string;

  before(async function () {
    const preparation = (await prepareSpaceUnstub(preparations, context, undefined, "SPACE_TEST", undefined, {
      deployRuntime: true,
    })) as IPreparation;
    customerHana = await CustomerHana.fromRequestContext(preparation.context);
    context = preparation.context;
    spaceId = preparation.spaceId;

    const spaceOwnerClient = await customerHana.getSpaceOwnerClient(spaceId);
    await spaceOwnerClient.exec(
      [
        `CREATE OR REPLACE PROCEDURE "${spaceId}"."${PROTECTED_OBJECT}$DCPP" (`,
        `  OUT FILTER_STRING NCLOB,`,
        `  IN USER_NAME NVARCHAR(256) DEFAULT NULL`,
        `)`,
        `LANGUAGE SQLSCRIPT`,
        `SQL SECURITY DEFINER`,
        `DEFAULT SCHEMA "${spaceId}"`,
        `READS SQL DATA AS`,
        `BEGIN`,
        `  IF :USER_NAME = '${USER_NAME}' THEN`,
        `    FILTER_STRING := '${FILTER_STRING_TRUE}';`,
        `  ELSE`,
        `    FILTER_STRING := '${FILTER_STRING_FALSE}';`,
        `  END IF;`,
        `END;`,
      ].join("\n")
    );
  });

  after(async function () {
    restoreStubs();
    await deleteSpaces(preparations);
  });

  async function callGetFilterStringProcedure(client: DbClient, objectName: string, userId: string) {
    return client.executeProcedure(spaceId + "$TEC", "DAC_GET_FILTER_STRING", {
      IN_OBJECT_NAME: objectName,
      IN_USER_NAME: userId,
    });
  }

  async function assertProcedureFailure(client: DbClient, error: string, objectName: string) {
    await expect(callGetFilterStringProcedure(client, objectName, "user")).to.be.rejectedWith(error);
  }

  async function getSupportUserClient(supportUserRole: SupportUserRole) {
    const supportUser = await customerHana.createSupportUser(supportUserRole, 1, "A000000");
    return getClient(context, supportUser.credentials);
  }

  async function getDbClient(customerHana: CustomerHana, getClient: string, spaceId?: string): Promise<DbClient> {
    return customerHana[getClient](spaceId);
  }

  [SupportUserRole.ReadOnly, SupportUserRole.Writable, SupportUserRole.Emergency].forEach(async (supportUserRole) => {
    it(`should allow consumption for getSupportUserClient (${supportUserRole})`, async () => {
      const supportUserClient = await getSupportUserClient(supportUserRole);
      const result = await callGetFilterStringProcedure(supportUserClient, PROTECTED_OBJECT, USER_NAME);
      expect(result).to.deep.equal({ parameters: { FILTER_STRING: FILTER_STRING_TRUE }, tables: [[]] });
    });
  });

  it(`should handle different users`, async () => {
    const supportUserClient = await getSupportUserClient(SupportUserRole.Writable);
    const result = await callGetFilterStringProcedure(supportUserClient, PROTECTED_OBJECT, "otherUser");
    expect(result).to.deep.equal({ parameters: { FILTER_STRING: FILTER_STRING_FALSE }, tables: [[]] });
  });

  it(`should handle invalid object`, async () => {
    const supportUserClient = await getSupportUserClient(SupportUserRole.Writable);
    await assertProcedureFailure(supportUserClient, ErrorMessages.INVALID_OBJECT, "DOES_NOT_EXIST");
  });

  [
    {
      getClient: "getAuditReplicatorClient",
      error: ErrorMessages.WRONG_PARAMETERS,
    },
    {
      getClient: "getBackupOwnerClient",
      error: ErrorMessages.WRONG_PARAMETERS,
    },
    {
      getClient: "getGlobalPacemakerClient",
      error: ErrorMessages.WRONG_PARAMETERS,
    },
    {
      getClient: "getGlobalTaskFrameworkClient",
      error: ErrorMessages.INSUFFICIENT_PRIVILEGE,
    },
    {
      getClient: "getTenantManagerClient",
      error: ErrorMessages.INSUFFICIENT_PRIVILEGE,
    },
  ].forEach(async ({ getClient, error }) => {
    it(`should block consumption for ${getClient}`, async () => {
      const client = await getDbClient(customerHana, getClient);
      await assertProcedureFailure(client, error, PROTECTED_OBJECT);
    });
  });

  [
    {
      getClient: "getSpaceDisUserClient",
      error: ErrorMessages.INSUFFICIENT_PRIVILEGE,
    },
    {
      getClient: "getSpaceInaUserClient",
      error: ErrorMessages.WRONG_PARAMETERS,
    },
    {
      getClient: "getSpaceManagerClient",
      error: ErrorMessages.INSUFFICIENT_PRIVILEGE,
    },
    {
      getClient: "getSpaceOwnerClient",
      error: ErrorMessages.NOT_SUPPORT_USER,
    },
    {
      getClient: "getSpaceSACUserClient",
      error: ErrorMessages.WRONG_PARAMETERS,
    },
    {
      getClient: "getSpaceWranglerClient",
      error: ErrorMessages.WRONG_PARAMETERS,
    },
  ].forEach(async ({ getClient, error }) => {
    it(`should block consumption for ${getClient}`, async () => {
      const client = await getDbClient(customerHana, getClient, spaceId);
      await assertProcedureFailure(client, error, PROTECTED_OBJECT);
    });
  });
});
