/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { expect } from "chai";
import { default as sinon } from "sinon";
import { PersistencyCandidateKeys } from "../../advisor/i18n";
import {
  LocalTableInfoProxy,
  ReplicationInfoProxy,
  RepositoryProxy,
  ViewInfoProxy,
} from "../../routes/advisor/access/";
import { IAdvisorResult, getAdviceCoordinator } from "./../../advisor";
import { AnalysisScopes, Eligibility, IRouterContext } from "./../../routes/advisor/router/types";
import entitiesWithDependencies from "./data/persistencyGuidance/EntitiesWithDependencies.json";
import fullsCsn from "./data/persistencyGuidance/fullCsn.json";
import remoteTableInfo from "./data/persistencyGuidance/remoteTableInfo.json";
import viewInfoNotPersisted from "./data/persistencyGuidance/viewInfoNotPersisted.json";
import viewInfoPersisted from "./data/persistencyGuidance/viewInfoPersisted.json";

describe("service/advisor/persistencyGuidance", function () {
  const repository = new RepositoryProxy();
  const context: IRouterContext = {
    space: "TEST_SPACE",
    spaceSchema: "TEST_SPACE",
    technicalSchema: "TEST_TEC",
    scope: new Set(),
    repository,
    viewInfo: new ViewInfoProxy(),
    remoteTableInfo: new ReplicationInfoProxy(),
    localTableInfo: new LocalTableInfoProxy(),
    candidates: [],
    isEligible: Eligibility.undefined,
    logInfo: (message) => {},
    logDebug: (message) => {},
    logError: (message) => {},
    logPerformance: (startTime, message) => new Date(),
  };

  before(function () {
    context.entity = "TEST_SPACE.View_002_001";
    context.scope.add(AnalysisScopes.CrossSpace);
    context.remoteTableInfo = new ReplicationInfoProxy(remoteTableInfo.remoteTableInfo as any);
    context.repository.setCSN(fullsCsn as any);
    context.repository.setEntitiesWithDependencies(entitiesWithDependencies as any);
  });

  after(function () {
    sinon.restore();
  });

  describe("Check results of persistency evaluator", function () {
    it("Model with not persisted views, replicated and virtual remote tables", async function () {
      context.viewInfo = new ViewInfoProxy(
        viewInfoNotPersisted.viewInfoList as any,
        viewInfoNotPersisted.viewPersistencyRuntimeInfo as any,
        viewInfoNotPersisted.viewsWithTaskInfo as any
      );
      const coordinator = getAdviceCoordinator(context);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).to.be.eq(11);
      expect(
        result.entityStats.filter((item) => item.persistencyCandidate === PersistencyCandidateKeys.BestCandidate).length
      ).to.be.eq(1);
      expect(
        result.entityStats.filter(
          (item) => item.persistencyCandidate === PersistencyCandidateKeys.BestAlternativeCandidate
        ).length
      ).to.be.eq(2);
      expect(
        result.entityStats.filter((item) => item.persistencyCandidate === PersistencyCandidateKeys.GoodCandidate).length
      ).to.be.eq(4);
      expect(
        result.entityStats.filter((item) => item.persistencyCandidate === PersistencyCandidateKeys.NotApplicable).length
      ).to.be.eq(4);
    });
    it("Model with one persisted views, replicated and virtual remote tables", async function () {
      context.viewInfo = new ViewInfoProxy(
        viewInfoPersisted.viewInfoList as any,
        viewInfoPersisted.viewPersistencyRuntimeInfo as any,
        viewInfoPersisted.viewsWithTaskInfo as any
      );
      const coordinator = getAdviceCoordinator(context);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).to.be.eq(11);
      expect(
        result.entityStats.filter((item) => item.persistencyCandidate === PersistencyCandidateKeys.BestCandidate).length
      ).to.be.eq(1);
      expect(
        result.entityStats.filter(
          (item) => item.persistencyCandidate === PersistencyCandidateKeys.BestAlternativeCandidate
        ).length
      ).to.be.eq(1);
      expect(
        result.entityStats.filter((item) => item.persistencyCandidate === PersistencyCandidateKeys.GoodCandidate).length
      ).to.be.eq(3);
      expect(
        result.entityStats.filter((item) => item.persistencyCandidate === PersistencyCandidateKeys.NotApplicable).length
      ).to.be.eq(6);
    });
  });

  describe("Check entity cards", function () {
    let result: IAdvisorResult;
    before(async function () {
      context.viewInfo = new ViewInfoProxy(
        viewInfoNotPersisted.viewInfoList as any,
        viewInfoNotPersisted.viewPersistencyRuntimeInfo as any,
        viewInfoNotPersisted.viewsWithTaskInfo as any
      );
      const coordinator = getAdviceCoordinator(context);
      result = await coordinator.runAdvisor();
    });
    it("Check entity card for the best candidate", async function () {
      const bestCandidateEntity = result.entityStats.filter(
        (item) => item.persistencyCandidate === PersistencyCandidateKeys.BestCandidate
      )[0];
      expect(bestCandidateEntity.card).is.not.undefined;
      const header = {
        name: "DATA_PERSISTENCE_CANDIDATE_SCORE",
        value: "10",
        messages: [
          {
            messageKey: "PG_BEST_CANDIDATE",
          },
        ],
      };
      expect(bestCandidateEntity.card?.header).deep.equal(header);
      expect(bestCandidateEntity.card?.metric.length).to.be.eq(9);
    });
    it("Check entity card for a best alternative candidate", async function () {
      const bestAlternativeCandidate = result.entityStats.filter(
        (item) => item.entity === "SbookAndSflightJoinSpfli"
      )[0];
      expect(bestAlternativeCandidate.card).is.not.undefined;
      const header = {
        name: "DATA_PERSISTENCE_CANDIDATE_SCORE",
        value: "9",
        messages: [
          {
            messageKey: "PG_BEST_ALTERNATIVE",
          },
        ],
      };
      expect(bestAlternativeCandidate.card?.header).deep.equal(header);
      expect(bestAlternativeCandidate.card?.metric.length).to.be.eq(8);
    });
    it("Check entity card for a good candidate", async function () {
      const goodCandidate = result.entityStats.filter((item) => item.entity === "View_002_001")[0];
      expect(goodCandidate.card).is.not.undefined;
      const header = {
        name: "DATA_PERSISTENCE_CANDIDATE_SCORE",
        value: "8",
        messages: [
          {
            messageKey: "PG_GOOD_CANDIDATE",
          },
        ],
      };
      expect(goodCandidate.card?.header).deep.equal(header);
      expect(goodCandidate.card?.metric.length).to.be.eq(8);
    });
    it("Check entity card for a remote table", async function () {
      const notApplicable = result.entityStats.filter((item) => item.entity === "SBOOK")[0];
      expect(notApplicable.card).is.not.undefined;
      const header = {
        name: "DATA_PERSISTENCE_CANDIDATE_SCORE",
        value: "NOT_APPLICABLE",
        messages: [{ messageKey: "PG_REMOTE_TABLES" }],
      };
      expect(notApplicable.card?.header).deep.equal(header);
      expect(notApplicable.card?.metric.length).to.be.eq(5);
    });
  });
});
