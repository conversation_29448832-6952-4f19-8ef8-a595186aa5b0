/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { IGetVersionResult, IRepositoryElementDependency } from "@sap/deepsea-types";
import { expect } from "chai";
import { default as sinon } from "sinon";
import { RepositoryObjectClient } from "../../repository/client/repositoryObjectClient";
import { RequestContext } from "../../repository/security/requestContext";
import {
  DependencyGraph,
  LocalTableInfoProxy,
  checkDeployedCsn,
  retrieveCsnWithResolvedTypes,
  retrieveDependencyGraphFullCsn,
  retrieveFullCSN,
} from "../../routes/advisor/access";
import { ReplicationInfoProxy, RepositoryProxy, ViewInfoProxy } from "../../routes/advisor/access/";
import { CrossSpaceDependencyProcessor } from "../../routes/advisor/access/crossSpaceGraph";
import { retrieveValidationCsn } from "../../routes/advisor/access/repository";
import * as RepoTestUtility from "../repository/repository.utility";
import { AnalysisScopes, Eligibility, IRouterContext } from "./../../routes/advisor/router/types";
import sflight3LevelView from "./data/Sflight3LevelView.json";
import Sflight3LevelViewCsnWithResolvedTypes from "./data/Sflight3LevelViewCsnWithResolvedTypes.json";
import Sflight3LevelViewRepositoryObject from "./data/Sflight3LevelViewRepositoryObject.json";
import sflight3LevelViewVersionResults from "./data/Sflight3LevelViewVersionResults.json";
import dependencyGraphWithCycle from "./data/dependencyGraphWithCycle.json";
import dependencyGraphWithMultiplePaths from "./data/dependencyGraphWithMultiplePaths.json";
import dependencyGraphWithSelfDependencies from "./data/dependencyGraphWithSelfDependencies.json";
import dependencyGraphWithSharedObjects from "./data/dependencyGraphWithSharedObjects.json";
import retrieveValidationCsn_expected from "./data/retrieveValidationCsn/expected.json";
import retrieveValidationCsn_getObject from "./data/retrieveValidationCsn/getObject.json";
import retrieveValidationCsn_getObjectDependencies from "./data/retrieveValidationCsn/getObjectDependencies.json";
import retrieveValidationCsn_second_level_expected from "./data/retrieveValidationCsn/secondLevelDependencies/expected.json";
import retrieveValidationCsn_second_level_getObject from "./data/retrieveValidationCsn/secondLevelDependencies/getObject.json";
import retrieveValidationCsn_second_level_getObjectDependencies from "./data/retrieveValidationCsn/secondLevelDependencies/getObjectDependencies.json";

function generateVersionResults(docResult: Record<string, any>): IGetVersionResult[] {
  return Object.keys(docResult).map((docKey) => ({
    id: docResult[docKey].id,
    repairedContent: `{\"definitions\":{\"${docKey}\":{\"kind\":\"entity\"}}}`,
    content: "",
    version: "99",
  }));
}

describe.skip("service/tests/advisor/repositoryAccess", function () {
  this.timeout(120000);
  const preparations: RepoTestUtility.IPreparation[] = [];
  let context: RequestContext;
  const testContext: IRouterContext = {
    space: "TEST_SPACE",
    spaceSchema: "TEST_SPACE",
    technicalSchema: "TEST_SPACE$TECH",
    scope: new Set(),
    repository: new RepositoryProxy(),
    remoteTableInfo: new ReplicationInfoProxy(),
    localTableInfo: new LocalTableInfoProxy(),
    viewInfo: new ViewInfoProxy(),
    candidates: [],
    isEligible: Eligibility.undefined,
    logInfo: (message) => {},
    logDebug: (message) => {},
    logError: (message) => {},
    logPerformance: (startTime, message) => new Date(),
  };
  const request: any = {
    params: {},
    query: {},
    body: {},
  };
  let docResult;

  before(async function () {
    context = RepoTestUtility.createDWCAdministratorContext();
    await RepoTestUtility.setUpTenantSchema(await RepoTestUtility.callGetAdminDBClient(context), context);
    RepoTestUtility.prepareStubs(context);
    const preparation = await RepoTestUtility.prepareSpace(preparations, context);
    expect(preparation.spaceUuid).to.not.be.undefined;
    request.context = context;
    request.params = { space: preparation.spaceId, name: "Sflight3LevelView" };
    docResult = await RepoTestUtility.createDocument(preparation, sflight3LevelView, 201);
    expect(docResult).to.not.be.undefined;
    delete docResult.validationWarnings;
  });

  after(async function () {
    await RepoTestUtility.deleteSpaces(preparations);
    RepoTestUtility.restoreStubs();
  });

  afterEach(function () {
    sinon.restore();
  });

  describe("Retrieve dependency graph and full CSN", function () {
    it("Retrieve dependency graph", async function () {
      sinon.stub(DependencyGraph.prototype, "isDeployed").returns(true);
      sinon.stub(RepositoryObjectClient, "getVersion").resolves(sflight3LevelViewVersionResults as any[]);
      const dependencyGraph = await retrieveDependencyGraphFullCsn(
        request.params.name,
        request.params.space,
        request.context
      );
      expect(Object.keys(dependencyGraph.entitiesWithDependencies).length).to.equal(10);
      Object.keys(dependencyGraph.entitiesWithDependencies).forEach((entity) => {
        expect(dependencyGraph.entitiesWithDependencies[entity]).to.not.be.undefined;
      });
      expect(Object.keys(dependencyGraph.fullCsn.definitions).length).to.equal(10);
      expect(dependencyGraph.entitiesWithDependencies.Sflight3LevelView.successors.length).to.equal(3);
      expect(dependencyGraph.entitiesWithDependencies.Sflight3LevelView.successors).to.have.members([
        "SbookAndSflightJoinSpfli",
        "SpfliJoinScarr",
        "Airline_DAC",
      ]);
      expect(dependencyGraph.entitiesWithDependencies.SpfliJoinScarr.successors.length).to.equal(2);
      expect(dependencyGraph.entitiesWithDependencies.SpfliJoinScarr.successors).to.have.members(["SPFLI", "SCARR"]);
      expect(dependencyGraph.entitiesWithDependencies.SbookAndSflightJoinSpfli.successors.length).to.equal(2);
      expect(dependencyGraph.entitiesWithDependencies.SbookAndSflightJoinSpfli.successors).to.have.members([
        "SflightJoinSpfli",
        "SBOOK",
      ]);
      expect(dependencyGraph.entitiesWithDependencies.Airline_DAC.successors).deep.equal(["AirlineResponsible"]);
    });

    it("Entity does not exist in repository", async function () {
      sinon.stub(RepositoryObjectClient, "getObject").resolves([]);
      try {
        await retrieveDependencyGraphFullCsn("Entity_does_not_exist", testContext.space, context);
        expect.fail("Should throw an error");
      } catch (error) {
        const errorMessage = "Cannot find repository object 'Entity_does_not_exist' in space 'TEST_SPACE'";
        expect(error.message).to.equal(errorMessage);
      }
    });

    it("Dependencies cannot be retrieved from repository", async function () {
      sinon.stub(RepositoryObjectClient, "getObject").resolves([""] as any);
      sinon.stub(RepositoryObjectClient, "getObjectDependencies").resolves([]);
      try {
        await retrieveDependencyGraphFullCsn("Entity_does_not_exist", testContext.space, context);
        expect.fail("Should throw an error");
      } catch (error) {
        const errorMessage =
          "Cannot find dependencies for repository object 'Entity_does_not_exist' in space 'TEST_SPACE'";
        expect(error.message).to.equal(errorMessage);
      }
    });

    it("Deployed full CSN cannot be retrieved from repository", async function () {
      sinon.stub(DependencyGraph.prototype, "isDeployed").returns(true);
      sinon.stub(RepositoryObjectClient, "getVersion").resolves(undefined);
      try {
        await retrieveDependencyGraphFullCsn(request.params.name, request.params.space, context);
        expect.fail("Should throw an error");
      } catch (error) {
        const errorMessage = "Failed to retrieve CSN for entities in model from repository";
        expect(error.message).to.equal(errorMessage);
      }
    });

    it("Check deployed Csn", function () {
      try {
        checkDeployedCsn(["Sflight3LevelView"], { definitions: {} } as any);
        expect.fail("Should throw an error");
      } catch (error) {
        const errorMessage = "Deployed CSN of entity 'Sflight3LevelView' cannot be found in repository";
        expect(error.message).to.equal(errorMessage);
      }
    });

    it("Dependency graph with cycle", function () {
      const request: any = {
        context,
        params: { space: testContext.space, name: "View_01" },
        query: {},
        body: {},
      };
      try {
        new DependencyGraph(
          [request.params.name],
          request.params.space,
          [dependencyGraphWithCycle as IRepositoryElementDependency],
          request.context
        );
        expect.fail("Should throw an error");
      } catch (error) {
        const errorMessage =
          "Cycle containing object 'View_01' found. Other potentially involved objects 'View_02','View_03'.";
        expect(error.message).to.contain(errorMessage);
      }
    });

    it("Dependency graph with multiple paths", function () {
      const request: any = {
        context,
        params: { space: testContext.space, name: "View_002_001" },
        query: {},
        body: {},
      };
      const dependencyGraph = new DependencyGraph(
        [request.params.name],
        request.params.space,
        [dependencyGraphWithMultiplePaths as IRepositoryElementDependency],
        request.context
      );
      expect(Object.keys(dependencyGraph.getEntitiesWithDependencies()).length).to.equal(6);
    });

    it("Dependency graph with shared objects", function () {
      const request: any = {
        context,
        params: { space: testContext.space, name: "ViewProductsIntegerJoin" },
        query: {},
        body: {},
      };
      const dependencyGraph = new DependencyGraph(
        [request.params.name],
        request.params.space,
        [dependencyGraphWithSharedObjects as IRepositoryElementDependency],
        request.context
      );
      const entitiesWithDependencies = dependencyGraph.getEntitiesWithDependencies();
      expect(Object.keys(entitiesWithDependencies).length).to.equal(3);
      expect(dependencyGraph.isShared("ViewProductsIntegerJoin")).to.equal(false);
      expect(entitiesWithDependencies.ViewProductsIntegerJoin.successors.length).to.equal(2);
      expect(entitiesWithDependencies["ViewProductsIntegerJoin(SPACE_01)"].successors.length).to.equal(0);
      expect(entitiesWithDependencies["ViewProductsIntegerJoin(SPACE_02)"].successors.length).to.equal(0);
    });

    it("Dependency graph with entities with dependencies to itself", function () {
      const request: any = {
        context,
        params: { space: testContext.space, name: "View_On_Remote_Table" },
        query: {},
        body: {},
      };
      const dependencyGraph = new DependencyGraph(
        [request.params.name],
        request.params.space,
        [dependencyGraphWithSelfDependencies as IRepositoryElementDependency],
        request.context
      );
      const entitiesWithDependencies = dependencyGraph.getEntitiesWithDependencies();
      expect(Object.keys(entitiesWithDependencies).length).to.equal(2);
      expect(entitiesWithDependencies.View_On_Remote_Table.successors.length).to.equal(1);
      expect(entitiesWithDependencies.View_On_Remote_Table.successors[0]).to.equal("Remote_Table");
      expect(entitiesWithDependencies.Remote_Table.successors.length).to.equal(0);
    });
  });

  describe("retrieveCsnWithResolvedTypes", function () {
    it("Retrieve CSN", async function () {
      testContext.space = request.params.space;
      testContext.entity = request.params.name;
      sinon.stub(RepositoryObjectClient, "getObject").resolves(Sflight3LevelViewRepositoryObject as any[]);
      await retrieveCsnWithResolvedTypes(testContext, request.context);
      const entities = testContext.repository.getEntities();
      expect(entities.length).to.equal(1);
      expect(entities[0]).to.equal(testContext.entity);
      expect(testContext.repository.getCsn()).to.deep.equal(Sflight3LevelViewCsnWithResolvedTypes);
    });

    it("Throw error for not defined entity", async function () {
      testContext.entity = undefined;
      try {
        await retrieveCsnWithResolvedTypes(testContext, request.context);
        expect.fail("Should throw an error");
      } catch (error) {
        const errorMessage = `Entity is not defined`;
        expect(error.message).to.equal(errorMessage);
      }
    });

    it("Throw error for non-existing object", async function () {
      testContext.entity = "DoesNotExist";
      try {
        await retrieveCsnWithResolvedTypes(testContext, request.context);
        expect.fail("Should throw an error");
      } catch (error) {
        const errorMessage = `Cannot find repository object '${testContext.entity}' in space '${testContext.space}'`;
        expect(error.message).to.equal(errorMessage);
      }
    });

    it("#deployedCsn can not be retrieved from repository", async function () {
      testContext.entity = request.params.name;
      try {
        const repositoryObjectCopy = structuredClone(Sflight3LevelViewRepositoryObject) as any;
        delete repositoryObjectCopy[0].properties["#deployedCsn"];
        sinon.stub(RepositoryObjectClient, "getObject").resolves(repositoryObjectCopy);
        await retrieveCsnWithResolvedTypes(testContext, request.context);
        expect.fail("Should throw an error");
      } catch (error) {
        const errorMessage = `#deployedCsn of entity '${testContext.entity}' cannot be found in repository`;
        expect(error.message).to.equal(errorMessage);
      }
    });

    it("#deployedCsnForConsumption can not be retrieved from repository", async function () {
      testContext.entity = request.params.name;
      try {
        const repositoryObjectCopy = structuredClone(Sflight3LevelViewRepositoryObject) as any;
        delete repositoryObjectCopy[0].properties["#deployedCsnForConsumption"];
        sinon.stub(RepositoryObjectClient, "getObject").resolves(repositoryObjectCopy);
        await retrieveCsnWithResolvedTypes(testContext, request.context);
        expect.fail("Should throw an error");
      } catch (error) {
        const errorMessage = `#deployedCsnForConsumption of entity '${testContext.entity}' cannot be found in repository`;
        expect(error.message).to.equal(errorMessage);
      }
    });
  });

  describe("retrieveValidationCsn", async function () {
    it("should retrieve the CSN", async function () {
      testContext.space = "D070420";
      testContext.entity = "My_View_1";
      sinon.stub(RepositoryObjectClient, "getObject").resolves(retrieveValidationCsn_getObject as any[]);
      sinon
        .stub(RepositoryObjectClient, "getObjectDependencies")
        .resolves(retrieveValidationCsn_getObjectDependencies as any[]);

      const result = await retrieveValidationCsn(testContext, request.context);
      const entities = testContext.repository.getEntitiesWithDependencies();

      expect(result).to.deep.equal([
        { space: "D070420", entity: "My_View_1" },
        { space: "COASTGUARD", entity: "FLJET" },
        { space: "D070420_SHARE_2", entity: "View_1" },
      ]);
      expect(entities).to.deep.equal({
        My_View_1: {
          id: "37EF9120B8938C2C19001CFEF0093E1F",
          "#objectStatus": 1,
          "#shared": "false",
          "#spaceName": "D070420",
          "#technicalType": "DWC_VIEW",
          successors: [],
        },
      });
      expect(testContext.repository.getCsn()).to.deep.equal(retrieveValidationCsn_expected);
    });
    it("should retrieve the CSN with fixed target in shared space", async function () {
      testContext.space = "D070420";
      testContext.entity = "My_View_1";
      sinon.stub(RepositoryObjectClient, "getObject").resolves(retrieveValidationCsn_second_level_getObject as any[]);
      sinon
        .stub(RepositoryObjectClient, "getObjectDependencies")
        .resolves(retrieveValidationCsn_second_level_getObjectDependencies as any[]);

      const result = await retrieveValidationCsn(testContext, request.context);
      const entities = testContext.repository.getEntitiesWithDependencies();

      expect(result).to.deep.equal([
        { space: "D070420", entity: "My_View_1" },
        { space: "COASTGUARD", entity: "FLJET" },
        { space: "D070420_SHARE_2", entity: "View_1" },
      ]);
      expect(entities).to.deep.equal({
        My_View_1: {
          id: "37EF9120B8938C2C19001CFEF0093E1F",
          "#objectStatus": 1,
          "#shared": "false",
          "#spaceName": "D070420",
          "#technicalType": "DWC_VIEW",
          successors: [],
        },
      });
      expect(testContext.repository.getCsn()).to.deep.equal(retrieveValidationCsn_second_level_expected);
    });
  });

  describe("Cross space analysis", async function () {
    it("Dependency graph with entities with dependencies to itself (Cross Space)", function () {
      const request: any = {
        context,
        params: { space: testContext.space, name: "View_On_Remote_Table" },
        query: {},
        body: {},
      };
      const objectDependencies = [dependencyGraphWithSelfDependencies as IRepositoryElementDependency];
      const crossSpaceGraph = CrossSpaceDependencyProcessor.getCrossSpaceGraph(request, objectDependencies);
      const entitiesWithDependencies = crossSpaceGraph.getEntitiesWithDependencies();
      expect(Object.keys(entitiesWithDependencies).length).to.equal(2);
      expect(entitiesWithDependencies["SPACE_01.View_On_Remote_Table"].successors.length).to.equal(1);
      expect(entitiesWithDependencies["SPACE_01.View_On_Remote_Table"].successors[0]).to.equal("SPACE_01.Remote_Table");
      expect(entitiesWithDependencies["SPACE_01.Remote_Table"].successors.length).to.equal(0);
    });

    it("Retrieve deployed CSN - retrieveFullCSN()", async function () {
      testContext.space = request.params.space;
      testContext.entity = request.params.name;
      await retrieveFullCSN("Sflight3LevelView", testContext.space, testContext, request.context);
      const entities = testContext.repository.getEntities();
      expect(entities.length).to.equal(1);
      expect(entities[0]).to.equal(testContext.entity);
    });

    it("Retrieve deployed CSN - retrieveFullCSN() with AnalysisScopes.CrossSpace", async function () {
      const versionResults = generateVersionResults(docResult);
      sinon.stub(RepositoryObjectClient, "getVersion").resolves(versionResults);
      sinon.stub(CrossSpaceDependencyProcessor.prototype, "isDeployed").returns(true);
      testContext.space = request.params.space;
      testContext.entity = request.params.name;
      testContext.scope.add(AnalysisScopes.CrossSpace);
      await retrieveFullCSN("Sflight3LevelView", testContext.space, testContext, request.context);
      const entities = testContext.repository.getEntities();
      expect(entities.length).to.equal(10);
      expect(entities[0]).to.equal(`${testContext.space}.${testContext.entity}`);
    });

    it("Retrieve deployed CSN - retrieveFullCSN() Error retrieveIDs()", async function () {
      testContext.space = request.params.space;
      testContext.entity = request.params.name;
      testContext.scope.add(AnalysisScopes.CrossSpace);
      try {
        await retrieveFullCSN("Xflight3LevelView", testContext.space, testContext, request.context);
        expect.fail("Should throw an error");
      } catch (error) {
        const errorMessage = `Cannot find repository object 'Xflight3LevelView' in space '${testContext.space}'`;
        expect(error.message).to.equal(errorMessage);
      }
    });

    it("Retrieve deployed CSN - retrieveFullCSN() Error retrieveDependencies()", async function () {
      sinon.stub(RepositoryObjectClient, "getObjectDependencies").resolves([]);
      testContext.space = request.params.space;
      testContext.entity = request.params.name;
      testContext.scope.add(AnalysisScopes.CrossSpace);
      try {
        await retrieveFullCSN("Sflight3LevelView", testContext.space, testContext, request.context);
        expect.fail("Should throw an error");
      } catch (error) {
        const errorMessage = `Cannot find dependencies for repository object '${testContext.space}.Sflight3LevelView'`;
        expect(error.message).to.equal(errorMessage);
      }
    });

    it("Retrieve deployed CSN - retrieveFullCSN() Error getVersion()", async function () {
      const versionResults = generateVersionResults(docResult);
      versionResults.splice(2, 1);
      sinon.stub(RepositoryObjectClient, "getVersion").resolves(versionResults);
      sinon.stub(CrossSpaceDependencyProcessor.prototype, "isDeployed").returns(true);
      testContext.space = request.params.space;
      testContext.entity = request.params.name;
      testContext.scope.add(AnalysisScopes.CrossSpace);
      try {
        await retrieveFullCSN("Sflight3LevelView", testContext.space, testContext, request.context);
        expect.fail("Should throw an error");
      } catch (error) {
        const errorMessage = `Deployed CSN of entity '${testContext.space}.Airline_DAC' cannot be found in repository`;
        expect(error.message).to.equal(errorMessage);
      }
    });

    it("Retrieve deployed CSN with empty repairedContent", async function () {
      const versionResults = generateVersionResults(docResult);
      versionResults[0].content = versionResults[0].repairedContent;
      versionResults[0].repairedContent = "";
      sinon.stub(RepositoryObjectClient, "getVersion").resolves(versionResults);
      sinon.stub(CrossSpaceDependencyProcessor.prototype, "isDeployed").returns(true);
      testContext.space = request.params.space;
      testContext.entity = request.params.name;
      testContext.scope.add(AnalysisScopes.CrossSpace);
      await retrieveFullCSN("Sflight3LevelView", testContext.space, testContext, request.context);
      const entities = testContext.repository.getEntities();
      expect(entities.length).to.equal(10);
      expect(entities[0]).to.equal(`${testContext.space}.${testContext.entity}`);
    });
  });
});
