/** @format */

import { expect } from "chai";
import { ScalingErrors } from "../../../../../shared/provisioning/ftc/types";
import { StorageDownsize } from "../../../../provisioning/flexible/configurationValidator/validations/StorageDownsize";

describe("Validation: Storage Downsize", () => {
  const validation = new StorageDownsize();
  let data: any;

  beforeEach(() => {
    data = {
      newPayload: {
        thresholdStorage: 0,
      },
      currentLicenses: {
        thresholdStorage: 512,
      },
    };
  });

  it("should return StorageDownsize error if storage is being downsized", async () => {
    data.newPayload.thresholdStorage = 256;
    const result = await validation.run(data);
    expect(result).to.deep.equal([ScalingErrors.StorageDownsize]);
  });

  it("should return empty array if storage is not being downsized", async () => {
    data.newPayload.thresholdStorage = 512;
    const result = await validation.run(data);
    expect(result).to.deep.equal([]);

    data.newPayload.thresholdStorage = 1024;
    const result2 = await validation.run(data);
    expect(result2).to.deep.equal([]);
  });
});
