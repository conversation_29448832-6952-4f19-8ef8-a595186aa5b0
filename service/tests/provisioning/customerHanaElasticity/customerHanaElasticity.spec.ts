/** @format */

import { TimeUnit } from "@sap/deepsea-utils";
import { httpClient } from "@sap/dwc-http-client";
import { ITenantLicenses } from "@sap/dwc-tms-provider";
import chai, { expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import sinon from "sinon";
import { ICalculatePayload } from "../../../../shared/provisioning/ftc/ICalculatePayload";
import {
  FlexibleTenantStatus,
  IGetLicense,
  PerformanceClass,
  ScalingWarnings,
  TenantClassification,
  TenantType,
} from "../../../../shared/provisioning/ftc/types";
import { FeatureFlagProvider } from "../../../featureflags/FeatureFlagProvider";
import { TenantInformationProvider } from "../../../featureflags/TenantInformationProvider";
import * as node from "../../../lib/node";
import { isLocalHanaMocked, isPullRequestValidation, testTenantUuid } from "../../../lib/node";
import { ExtraParamsHandler } from "../../../provisioning/extraParameters/ExtraParamsHandler";
import { ElasticCustomerHanaProvisioning } from "../../../provisioning/flexible/ElasticCustomerHanaProvisioning";
import { submitNewSize } from "../../../provisioning/flexible/customerHanaElasticity";
import { getLicense } from "../../../provisioning/flexible/getTenantLicense";
import * as flexibleHelper from "../../../provisioning/flexible/helpers";
import {
  bwBridgeConfigurationFailed,
  dataLakeConfigurationFailed,
  hanaConfigurationFailed,
  isFlexibleConfigurationNeeded,
  notEnoughStorageElapsedTime,
  setOrUpdateFtcMetadata,
} from "../../../provisioning/flexible/helpers";
import { UpdateHandler } from "../../../provisioning/flexible/updateHandler/UpdateHandler";
import * as helpers from "../../../provisioning/flexible/updateHandler/helpers";
import {
  generateLicensesInMetadataFormat,
  readFtcMetadata,
} from "../../../provisioning/flexible/updateHandler/helpers";
import { IFtcMetadata } from "../../../provisioning/flexible/updateHandler/types";
import * as helper from "../../../provisioning/helpers";
import { getAvailableFeaturesForCurrentLandscape, isFlexibleTenant } from "../../../provisioning/helpers";
import { IExtraParams, ITenantMetadata } from "../../../provisioning/types";
import { RequestContext } from "../../../repository/security/requestContext";
import { CustomerHanaRuntimeData } from "../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { HanaCloudService } from "../../../scp/availableServices/HanaCloudService";
import { IHanaServiceParameters } from "../../../scp/types";
import { TenantManagementService } from "../../../tms/TenantManagementService";
import { licenseObject } from "./utils";

chai.use(chaiAsPromised);

describe("service/provisioning/customerHanaElasticity", function () {
  const context = RequestContext.createFromTenantId(testTenantUuid);

  let sandbox: sinon.SinonSandbox;
  let tmsStub: sinon.SinonStub;
  let tmsClearCacheStub: sinon.SinonStub;
  let tmsCheckOperationStub: sinon.SinonStub;
  let tmsFetchStub: sinon.SinonStub;
  let ffStub: sinon.SinonStub;
  let landscapeIdStub: sinon.SinonStub;
  let customerHanaRunTimeStub: sinon.SinonStub;

  beforeEach(function () {
    sandbox = sinon.createSandbox();

    tmsStub = sandbox.stub(TenantInformationProvider, "getTenantInformation");
    const license = licenseObject("15000", "32", "2", "256", "0", "0", "0");
    tmsStub.resolves({
      uuid: testTenantUuid,
      consumerAccountDisplayName: "displayName",
      versionUuid: "xxx-xxx-xxx",
      ...license,
      inconsistent: false,
      inconsistentReason: "",
    });

    ffStub = sandbox.stub(FeatureFlagProvider, "getFeatureFlags");
    ffStub.resolves({});
    tmsFetchStub = sandbox.stub(TenantInformationProvider, "fetchTenantInformation");

    sandbox.stub(httpClient, "call");
    landscapeIdStub = sandbox.stub(node, "getLandscapeId");
    sandbox.stub(CustomerHanaRuntimeData.prototype, "isDataLakeConnectedToSpace");

    customerHanaRunTimeStub = sandbox.stub(CustomerHanaRuntimeData.prototype, "getOverallAssignedStorage");
    customerHanaRunTimeStub.resolves({ memory: { assigned: 0 } });
  });

  afterEach(function () {
    sandbox.restore();
  });

  describe("Tenant Flexibility", () => {
    let result: boolean;
    let stubReadFtcMetadata: sinon.SinonStub;

    beforeEach(() => {
      stubReadFtcMetadata = sandbox
        .stub(helpers, "readFtcMetadata")
        .resolves({ metadata: { status: FlexibleTenantStatus.COMPLETED } } as any);
    });
    afterEach(() => {
      stubReadFtcMetadata.restore();
    });

    it("should return true if tenant is flexible.", () => {
      result = isFlexibleTenant({
        type: TenantType.CPEA,
        freeTier: false,
        license: { thresholdDWCCU: "0" } as Partial<ITenantLicenses>,
      } as ITenantMetadata);
      sinon.assert.match(result, true);

      result = isFlexibleTenant({
        type: TenantType.DWC,
        freeTier: false,
        license: { thresholdDWCCU: "4300" } as Partial<ITenantLicenses>,
      } as ITenantMetadata);
      sinon.assert.match(result, true);
      result = isFlexibleTenant({
        type: TenantType.DWC,
        freeTier: true,
        license: { thresholdDWCCU: "4300" } as Partial<ITenantLicenses>,
      } as ITenantMetadata);
      sinon.assert.match(result, true);
    });

    it("should return false if tenant is not flexible.", () => {
      let result: boolean;

      result = isFlexibleTenant({
        type: TenantType.DWC,
        freeTier: false,
        license: { thresholdDWCCU: "0" } as Partial<ITenantLicenses>,
      } as ITenantMetadata);
      sinon.assert.match(result, false);

      result = isFlexibleTenant({
        type: TenantType.CPEA,
        freeTier: true,
        license: { thresholdDWCCU: "0" } as Partial<ITenantLicenses>,
      } as ITenantMetadata);
      sinon.assert.match(result, false);
    });

    it("Should show an error message if it is not a flexible tenant in submitNewSize", async function () {
      async function testErrorMessage(license) {
        const readSpaceMetadataStub = sandbox.stub(helper, "readSpaceMetadata");

        let error = "";
        try {
          readSpaceMetadataStub.resolves(false);
          tmsStub.resolves(license);

          const body: ICalculatePayload = {
            thresholdMemory: 512,
            thresholdVCPU: 32,
            thresholdStorage: 512,
            thresholdDataLakeStorage: 512,
            thresholdBWBridge1: 0,
            thresholdRmsNodeHours: 0,
            thresholdRmsPremiumOutbound: 0,
            thresholdCatalogStorage: 0,
            thresholdECNBlock: 0,
            thresholdECNPerformanceClass: PerformanceClass.MEMORY,
            thresholdLargeSystemsStorage: 0,
            thresholdLargeSystemsCompute: 0,
            thresholdLargeSystemsRequests: 0,
            options: {},
          };
          await submitNewSize(context, body);
        } catch (err) {
          error = err.message;
        }
        sinon.assert.match(error, `Tenant is not flexible`);

        readSpaceMetadataStub.restore();
      }

      const freeTierSpecs = {
        ...licenseObject("0", "0", "0", "0", "0", "0", "0"),
        type: TenantType.CPEA,
        freeTier: true,
      };
      await testErrorMessage(freeTierSpecs);
      await testErrorMessage(licenseObject("0", "0", "0", "0", "0", "0", "0"));
    });

    it("Should submitNewSize without thresholdLargeSystemsRequests if DWCO_LARGE_SYSTEMS_REQUESTS is false", async function () {
      ffStub.resolves({ DWCO_LARGE_SYSTEMS: true, DWCO_LARGE_SYSTEMS_REQUESTS: false });

      tmsClearCacheStub = sandbox.stub(TenantInformationProvider, "clearCache");
      tmsCheckOperationStub = sandbox.stub(TenantManagementService.prototype, "checkForOperationInProgress");
      sandbox.stub(ElasticCustomerHanaProvisioning.prototype, "waitForHanaAvailability" as any).resolves(true);
      const updateHandlerStartStub = sandbox.stub(UpdateHandler.prototype, "startUpdateQueue").resolves();
      const updateHandlerSpy = sandbox.spy(UpdateHandler, "createUpdateHandler");

      const hanaCloudServiceStub = sandbox.stub(HanaCloudService.prototype);
      sandbox.stub(HanaCloudService, "fromRequestContext").returns(Promise.resolve(hanaCloudServiceStub));

      tmsClearCacheStub.resolves();
      tmsCheckOperationStub.resolves(false);

      const body: ICalculatePayload = {
        thresholdMemory: 128,
        thresholdVCPU: 32,
        thresholdStorage: 384,
        thresholdDataLakeStorage: 10,
        thresholdBWBridge1: 0,
        thresholdRmsNodeHours: 0,
        thresholdRmsPremiumOutbound: 0,
        thresholdCatalogStorage: 0,
        thresholdECNBlock: 0,
        thresholdECNPerformanceClass: PerformanceClass.MEMORY,
        thresholdLargeSystemsStorage: 0,
        thresholdLargeSystemsCompute: 0,
        thresholdLargeSystemsRequests: 0,
        options: {},
      };

      const licenses = {
        ...licenseObject("10000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"),
        type: TenantType.CPEA,
      };

      tmsStub.resolves(licenses);
      tmsFetchStub.resolves(licenses);

      await submitNewSize(context, body);

      expect(updateHandlerStartStub.calledOnce).to.be.true;

      const updateHanlderLicenseArg = updateHandlerSpy.getCall(0).args[1];

      expect(updateHanlderLicenseArg).to.not.have.property("thresholdLargeSystemsRequests");
      expect(updateHanlderLicenseArg).to.have.property("thresholdLargeSystemsCompute", "0");
      expect(updateHanlderLicenseArg).to.have.property("thresholdLargeSystemsStorage", "0");
      expect(updateHanlderLicenseArg).to.have.property("thresholdMemory");
    });

    it("Should submitNewSize with thresholdLargeSystemsRequests if DWCO_LARGE_SYSTEMS_REQUESTS is true", async function () {
      ffStub.resolves({ DWCO_LARGE_SYSTEMS: true, DWCO_LARGE_SYSTEMS_REQUESTS: true });

      tmsClearCacheStub = sandbox.stub(TenantInformationProvider, "clearCache");
      tmsCheckOperationStub = sandbox.stub(TenantManagementService.prototype, "checkForOperationInProgress");
      sandbox.stub(ElasticCustomerHanaProvisioning.prototype, "waitForHanaAvailability" as any).resolves(true);
      const updateHandlerStartStub = sandbox.stub(UpdateHandler.prototype, "startUpdateQueue").resolves();
      const updateHandlerSpy = sandbox.spy(UpdateHandler, "createUpdateHandler");

      const hanaCloudServiceStub = sandbox.stub(HanaCloudService.prototype);
      sandbox.stub(HanaCloudService, "fromRequestContext").returns(Promise.resolve(hanaCloudServiceStub));

      tmsClearCacheStub.resolves();
      tmsCheckOperationStub.resolves(false);

      const body: ICalculatePayload = {
        thresholdMemory: 128,
        thresholdVCPU: 32,
        thresholdStorage: 384,
        thresholdDataLakeStorage: 10,
        thresholdBWBridge1: 0,
        thresholdRmsNodeHours: 0,
        thresholdRmsPremiumOutbound: 0,
        thresholdCatalogStorage: 0,
        thresholdECNBlock: 0,
        thresholdECNPerformanceClass: PerformanceClass.MEMORY,
        thresholdLargeSystemsStorage: 0,
        thresholdLargeSystemsCompute: 0,
        thresholdLargeSystemsRequests: 0,
        options: {},
      };

      const licenses = {
        ...licenseObject("10000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"),
        type: TenantType.CPEA,
      };

      tmsStub.resolves(licenses);
      tmsFetchStub.resolves(licenses);

      await submitNewSize(context, body);

      expect(updateHandlerStartStub.calledOnce).to.be.true;

      const updateHanlderLicenseArg = updateHandlerSpy.getCall(0).args[1];

      expect(updateHanlderLicenseArg).to.have.property("thresholdLargeSystemsRequests", "0");
      expect(updateHanlderLicenseArg).to.have.property("thresholdMemory", "128");
    });

    it("Should submitNewSize without thresholdLargeSystemsRequests, thresholdLargeSystemsCompute and thresholdLargeSystemsStorage if DWCO_LARGE_SYSTEMS is false", async function () {
      ffStub.resolves({ DWCO_LARGE_SYSTEMS: false, DWCO_LARGE_SYSTEMS_REQUESTS: true });

      tmsClearCacheStub = sandbox.stub(TenantInformationProvider, "clearCache");
      tmsCheckOperationStub = sandbox.stub(TenantManagementService.prototype, "checkForOperationInProgress");
      sandbox.stub(ElasticCustomerHanaProvisioning.prototype, "waitForHanaAvailability" as any).resolves(true);
      const updateHandlerStartStub = sandbox.stub(UpdateHandler.prototype, "startUpdateQueue").resolves();
      const updateHandlerSpy = sandbox.spy(UpdateHandler, "createUpdateHandler");

      const hanaCloudServiceStub = sandbox.stub(HanaCloudService.prototype);
      sandbox.stub(HanaCloudService, "fromRequestContext").returns(Promise.resolve(hanaCloudServiceStub));

      tmsClearCacheStub.resolves();
      tmsCheckOperationStub.resolves(false);

      const body: ICalculatePayload = {
        thresholdMemory: 128,
        thresholdVCPU: 32,
        thresholdStorage: 384,
        thresholdDataLakeStorage: 10,
        thresholdBWBridge1: 0,
        thresholdRmsNodeHours: 0,
        thresholdRmsPremiumOutbound: 0,
        thresholdCatalogStorage: 0,
        thresholdECNBlock: 0,
        thresholdECNPerformanceClass: PerformanceClass.MEMORY,
        thresholdLargeSystemsStorage: 0,
        thresholdLargeSystemsCompute: 0,
        thresholdLargeSystemsRequests: 0,
        options: {},
      };

      const licenses = {
        ...licenseObject("10000", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"),
        type: TenantType.CPEA,
      };

      tmsStub.resolves(licenses);
      tmsFetchStub.resolves(licenses);

      await submitNewSize(context, body);

      expect(updateHandlerStartStub.calledOnce).to.be.true;

      const updateHanlderLicenseArg = updateHandlerSpy.getCall(0).args[1];

      expect(updateHanlderLicenseArg).to.not.have.property("thresholdLargeSystemsCompute");
      expect(updateHanlderLicenseArg).to.not.have.property("thresholdLargeSystemsStorage");
      expect(updateHanlderLicenseArg).to.not.have.property("thresholdLargeSystemsRequests");
      expect(updateHanlderLicenseArg).to.have.property("thresholdMemory", "128");
    });
  });

  it("should return current license", async function () {
    ffStub.resolves({});
    tmsFetchStub.resolves(
      licenseObject("1000", "64", "4", "512", "0", "0", "0", "1", "2", "3", PerformanceClass.COMPUTE, "4", "5", "1000")
    );
    sinon.assert.match(await getLicense(context), {
      thresholdDWCCU: 1000,
      thresholdStorage: 512,
      thresholdMemory: 64,
      thresholdVCPU: 4,
      thresholdDataLakeStorage: 0,
      thresholdBWBridge1: 0,
      thresholdRmsPremiumOutbound: 0,
      thresholdRmsNodeHours: 1,
      thresholdCatalogStorage: 2,
      thresholdECNBlock: 3,
      thresholdECNPerformanceClass: PerformanceClass.COMPUTE,
      thresholdLargeSystemsStorage: 4,
      thresholdLargeSystemsCompute: 5,
      thresholdLargeSystemsRequests: 1000,
    });

    tmsFetchStub.resolves(
      licenseObject(
        undefined!,
        "64",
        "4",
        "512",
        "0",
        "0",
        "0",
        "10",
        "20",
        "30",
        PerformanceClass.HIGH_COMPUTE,
        "40",
        "50",
        "2000"
      )
    );
    sinon.assert.match(await getLicense(context), {
      thresholdDWCCU: 0,
      thresholdStorage: 512,
      thresholdMemory: 64,
      thresholdVCPU: 4,
      thresholdDataLakeStorage: 0,
      thresholdBWBridge1: 0,
      thresholdRmsPremiumOutbound: 0,
      thresholdRmsNodeHours: 10,
      thresholdCatalogStorage: 20,
      thresholdECNBlock: 30,
      thresholdECNPerformanceClass: PerformanceClass.HIGH_COMPUTE,
      thresholdLargeSystemsStorage: 40,
      thresholdLargeSystemsCompute: 50,
      thresholdLargeSystemsRequests: 2000,
    });

    tmsFetchStub.resolves(
      licenseObject(
        "0",
        "64",
        "4",
        "512",
        "10",
        "256",
        "0",
        "2",
        "4",
        "6",
        PerformanceClass.HIGH_MEMORY,
        "8",
        "10",
        "3000"
      )
    );
    sinon.assert.match(await getLicense(context), {
      thresholdDWCCU: 0,
      thresholdStorage: 512,
      thresholdMemory: 64,
      thresholdVCPU: 4,
      thresholdDataLakeStorage: 10,
      thresholdBWBridge1: 256,
      thresholdRmsPremiumOutbound: 0,
      thresholdRmsNodeHours: 2,
      thresholdCatalogStorage: 4,
      thresholdECNBlock: 6,
      thresholdECNPerformanceClass: PerformanceClass.HIGH_MEMORY,
      thresholdLargeSystemsStorage: 8,
      thresholdLargeSystemsCompute: 10,
      thresholdLargeSystemsRequests: 3000,
    });

    tmsFetchStub.resolves(
      licenseObject(
        "0",
        "64",
        "4",
        "512",
        "90",
        "4096",
        "0",
        "3",
        "6",
        "9",
        PerformanceClass.MEMORY,
        "12",
        "15",
        "4000"
      )
    );
    sinon.assert.match(await getLicense(context), {
      thresholdDWCCU: 0,
      thresholdStorage: 512,
      thresholdMemory: 64,
      thresholdVCPU: 4,
      thresholdDataLakeStorage: 90,
      thresholdBWBridge1: 4096,
      thresholdRmsPremiumOutbound: 0,
      thresholdRmsNodeHours: 3,
      thresholdCatalogStorage: 6,
      thresholdECNBlock: 9,
      thresholdECNPerformanceClass: PerformanceClass.MEMORY,
      thresholdLargeSystemsStorage: 12,
      thresholdLargeSystemsCompute: 15,
      thresholdLargeSystemsRequests: 4000,
    });
  });

  it("should return license with free tier, classification, type and expiration date", async () => {
    const license: ITenantLicenses = {
      thresholdDWCCU: "1000",
      thresholdStorage: "512",
      thresholdMemory: "64",
      thresholdVCPU: "4",
      thresholdDataLakeStorage: "0",
      thresholdBWBridge1: "0",
      thresholdRmsPremiumOutbound: "0",
      thresholdRmsNodeHours: "1",
      thresholdCatalogStorage: "2",
      thresholdECNBlock: "3",
      thresholdECNPerformanceClass: PerformanceClass.COMPUTE,
      thresholdLargeSystemsStorage: "4",
      thresholdLargeSystemsCompute: "5",
      thresholdLargeSystemsRequests: "1000",
    };
    tmsFetchStub.resolves({
      license,
      freeTier: "true",
      classification: TenantClassification.DWC,
      type: TenantType.CPEA,
      expirationDate: new Date("2023-12-31T23:59:59Z"),
    });

    sinon.assert.match(await getLicense(context), {
      thresholdDWCCU: 1000,
      thresholdStorage: 512,
      thresholdMemory: 64,
      thresholdVCPU: 4,
      thresholdDataLakeStorage: 0,
      thresholdBWBridge1: 0,
      thresholdRmsPremiumOutbound: 0,
      thresholdRmsNodeHours: 1,
      thresholdCatalogStorage: 2,
      thresholdECNBlock: 3,
      thresholdECNPerformanceClass: PerformanceClass.COMPUTE,
      thresholdLargeSystemsStorage: 4,
      thresholdLargeSystemsCompute: 5,
      thresholdLargeSystemsRequests: 1000,
      freeTier: true,
      tenantClassification: TenantClassification.DWC,
      tenantType: TenantType.CPEA,
      expirationDate: new Date("2023-12-31T23:59:59Z"),
    } as IGetLicense);
  });

  it("should validate if tenant needs to be configured", async function () {
    tmsFetchStub.resolves(licenseObject("0", "0", "0", "0", "0", "0", "0"));
    sinon.assert.match(await isFlexibleConfigurationNeeded(context), false);

    tmsFetchStub.resolves(licenseObject("100", "64", "4", "256", "0", "0", "0"));
    sinon.assert.match(await isFlexibleConfigurationNeeded(context), false);

    tmsFetchStub.resolves(licenseObject("100", "0", "0", "0", "0", "0", "0"));
    sinon.assert.match(await isFlexibleConfigurationNeeded(context), true);
  });

  describe("Flexible Tenant Configuration metadata", function () {
    this.timeout(5000);
    let hanaServiceParametersStub: sinon.SinonStub;
    let isCanaryStub: sinon.SinonStub;
    const context = RequestContext.createFromTenantId(testTenantUuid);
    const licenses = {
      thresholdBWBridge1: "256",
      thresholdMemory: "32",
      thresholdVCPU: "2",
      thresholdDataLakeCompute: "6",
      thresholdDataLakeStorage: "5",
      thresholdStorage: "256",
    };

    const metadataValue: IFtcMetadata = {
      licenses,
      status: FlexibleTenantStatus.NOT_COMPLETED,
      timestamp: new Date("2023-07-12T14:50:11.371Z"),
    };

    const mockedScpParams = {
      data: {
        slaLevel: "standard",
        availabilityZonePlacement: {
          highAvailabilityCrossMultiAZEnabled: false,
        },
        enabledservices: {
          scriptserver: false,
        },
      },
    } as IHanaServiceParameters;

    before(function () {
      if (!isLocalHanaMocked() && !isPullRequestValidation()) {
        this.skip(); // Only executes when HANA is available
      }
    });
    beforeEach(function () {
      sandbox.stub(flexibleHelper, "notEnoughStorageElapsedTime").returns(true);
      hanaServiceParametersStub = sandbox.stub(HanaCloudService.prototype, "getHanaServiceParameters");
      hanaServiceParametersStub.resolves(mockedScpParams);
      tmsFetchStub.resolves({
        uuid: testTenantUuid,
        consumerAccountDisplayName: "displayName",
        versionUuid: "xxx-xxx-xxx",
        license: licenses,
        inconsistent: false,
        inconsistentReason: "",
      });
      tmsStub.resolves({
        uuid: testTenantUuid,
        consumerAccountDisplayName: "displayName",
        versionUuid: "xxx-xxx-xxx",
        license: licenses,
        inconsistent: false,
        inconsistentReason: "",
      });
      isCanaryStub = sandbox.stub(node, "isCanary").returns(true);
    });

    it("Should write/read/delete metadata on SPACE_METADATA table", async () => {
      // write
      await setOrUpdateFtcMetadata(context, metadataValue);

      // read
      let result = (await readFtcMetadata(context)).metadata;
      sinon.assert.match(result, { ...metadataValue, timestamp: result.timestamp });

      // delete
      await setOrUpdateFtcMetadata(context, null);
      result = (await readFtcMetadata(context)).metadata;
      sinon.assert.match(Boolean(result), true);
    });

    it("should show warning of 6 hours for hana configuration when is canary", async () => {
      await setOrUpdateFtcMetadata(context, metadataValue);
      const result = (await readFtcMetadata(context)).errors;
      sinon.assert.match(result[0], ScalingWarnings.CustomerHanaLessThanSixHoursUpdate);
    });

    it("should show warning of 24 hours for hana configuration when landscape isn't canary", async () => {
      isCanaryStub.returns(false);
      await setOrUpdateFtcMetadata(context, metadataValue);
      const result = (await readFtcMetadata(context)).errors;
      sinon.assert.match(result[0], ScalingWarnings.CustomerHanaLessThanTwentyFourHoursUpdate);
    });

    it("should set isHanaScriptServerEnabled if option is undefined (not enabled)", async () => {
      await setOrUpdateFtcMetadata(context, metadataValue);
      const metadata = await readFtcMetadata(context);
      sinon.assert.match(metadata.metadata.options?.isHanaScriptServerEnabled, false);
    });

    it("should set isHanaScriptServerEnabled if option is undefined (enabled)", async () => {
      hanaServiceParametersStub.resolves({
        data: {
          slaLevel: "standard",
          availabilityZonePlacement: {
            highAvailabilityCrossMultiAZEnabled: false,
          },
          enabledservices: {
            scriptserver: true,
          },
        },
      });
      await setOrUpdateFtcMetadata(context, metadataValue);
      const metadata = await readFtcMetadata(context);
      sinon.assert.match(metadata.metadata.options?.isHanaScriptServerEnabled, true);
    });

    it("should not set isHanaScriptServerEnabled if option is already set", async () => {
      await setOrUpdateFtcMetadata(context, {
        ...metadataValue,
        options: { isHanaScriptServerEnabled: true, isHanaMultiAZEnabled: false },
      });
      const metadata = await readFtcMetadata(context);
      sinon.assert.match(metadata.metadata.options?.isHanaScriptServerEnabled, true);
      sinon.assert.notCalled(hanaServiceParametersStub);
    });

    it("should set isHanaMultiAZEnabled if option is undefined (not enabled)", async () => {
      await setOrUpdateFtcMetadata(context, metadataValue);
      const metadata = await readFtcMetadata(context);
      sinon.assert.match(metadata.metadata.options?.isHanaMultiAZEnabled, false);
    });

    it("should set isHanaMultiAZEnabled if option is undefined (enabled)", async () => {
      hanaServiceParametersStub.resolves({
        data: {
          slaLevel: "elevated",
          availabilityZonePlacement: {
            highAvailabilityCrossMultiAZEnabled: true,
          },
          enabledservices: {
            scriptserver: false,
          },
        },
      });
      await setOrUpdateFtcMetadata(context, metadataValue);
      const metadata = await readFtcMetadata(context);
      sinon.assert.match(metadata.metadata.options?.isHanaMultiAZEnabled, true);
    });

    it("should not set isHanaMultiAZEnabled if option is already set", async () => {
      await setOrUpdateFtcMetadata(context, {
        ...metadataValue,
        options: { isHanaMultiAZEnabled: true, isHanaScriptServerEnabled: false },
      });
      const metadata = await readFtcMetadata(context);
      sinon.assert.match(metadata.metadata.options?.isHanaMultiAZEnabled, true);
      sinon.assert.notCalled(hanaServiceParametersStub);
    });
  });

  describe("Flexible Tenant Configuration new metadata format", function () {
    const notCompletedMetadata = {
      licenses: {
        thresholdStorage: "1024",
        thresholdMemory: "32",
        thresholdVCPU: "2",
        thresholdDataLakeStorage: "5",
        thresholdBWBridge1: "256",
      },
      timestamp: new Date("2021-08-27T13:11:18.284Z"),
      status: FlexibleTenantStatus.NOT_COMPLETED,
    };
    const completedMetadata = {
      licenses: {
        thresholdStorage: "1024",
        thresholdMemory: "32",
        thresholdVCPU: "2",
        thresholdDataLakeStorage: "5",
        thresholdBWBridge1: "256",
      },
      timestamp: new Date("2021-08-27T13:11:18.284Z"),
      status: FlexibleTenantStatus.COMPLETED,
    };

    const failedMetadata = {
      licenses: {
        thresholdStorage: "1024",
        thresholdMemory: "32",
        thresholdVCPU: "2",
        thresholdDataLakeStorage: "5",
        thresholdBWBridge1: "256",
      },
      timestamp: new Date("2021-08-27T13:11:18.284Z"),
      status: FlexibleTenantStatus.FAILED,
    };
    it("should check if something happened in less than a given hour threshold", () => {
      const hourThreshold = 6;
      const originalTime = new Date().getTime();
      const notEnoughTime = new Date(
        originalTime - Math.ceil(TimeUnit.HOURS.toMillis(hourThreshold)) + Math.ceil(TimeUnit.HOURS.toMillis(1))
      );
      const enoughTime = new Date(originalTime - Math.ceil(TimeUnit.HOURS.toMillis(hourThreshold)));

      sinon.assert.match(notEnoughStorageElapsedTime(notEnoughTime, hourThreshold), true);
      sinon.assert.match(notEnoughStorageElapsedTime(enoughTime, hourThreshold), false);
      ffStub.returns({});
    });
    it("should check for hana configuration failures", () => {
      sinon.assert.match(hanaConfigurationFailed(notCompletedMetadata, 32, 2, 1024), false);
      sinon.assert.match(hanaConfigurationFailed(notCompletedMetadata, 64, 4, 1048), false);

      sinon.assert.match(hanaConfigurationFailed(completedMetadata, 32, 2, 1024), false);
      sinon.assert.match(hanaConfigurationFailed(completedMetadata, 64, 4, 1048), false);

      sinon.assert.match(hanaConfigurationFailed(failedMetadata, 32, 2, 1024), false);
      sinon.assert.match(hanaConfigurationFailed(failedMetadata, 64, 4, 1048), true);
    });
    it("should check for BwBridge configuration failures", () => {
      sinon.assert.match(bwBridgeConfigurationFailed(notCompletedMetadata, 256), false);
      sinon.assert.match(bwBridgeConfigurationFailed(notCompletedMetadata, 360), false);

      sinon.assert.match(bwBridgeConfigurationFailed(completedMetadata, 256), false);
      sinon.assert.match(bwBridgeConfigurationFailed(completedMetadata, 360), false);

      sinon.assert.match(bwBridgeConfigurationFailed(failedMetadata, 256), false);
      sinon.assert.match(bwBridgeConfigurationFailed(failedMetadata, 360), true);
    });
    it("should check for datalake configuration failures", () => {
      sinon.assert.match(dataLakeConfigurationFailed(notCompletedMetadata, 6), false);
      sinon.assert.match(dataLakeConfigurationFailed(notCompletedMetadata, 5), false);

      sinon.assert.match(dataLakeConfigurationFailed(completedMetadata, 6), false);
      sinon.assert.match(dataLakeConfigurationFailed(completedMetadata, 5), false);

      sinon.assert.match(dataLakeConfigurationFailed(failedMetadata, 5), false);
      sinon.assert.match(dataLakeConfigurationFailed(failedMetadata, 4), true);
    });
  });

  describe("Landscape Features", () => {
    let oldValueFeatureAvailability: string | undefined;

    before(() => {
      oldValueFeatureAvailability = process.env.USE_CONFIG_STORE_FEATURE_AVAILABILITY_PARAM;
      process.env.USE_CONFIG_STORE_FEATURE_AVAILABILITY_PARAM = "false";
    });

    after(() => {
      process.env.USE_CONFIG_STORE_FEATURE_AVAILABILITY_PARAM = oldValueFeatureAvailability;
    });

    it("should return datalake, bwBridge and objectStore for eu10 landscape", async () => {
      landscapeIdStub.returns("eu10");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: true,
      });
    });
    it("should return datalake, bwBridge and objectStore for eu11 landscape", async () => {
      landscapeIdStub.returns("eu11");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: true,
      });
    });
    it("should return datalake, bwBridge and objectStore for ap10 landscape", async () => {
      landscapeIdStub.returns("ap10");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: true,
      });
    });
    it("should return datalake and bwBridge for ap11 landscape", async () => {
      landscapeIdStub.returns("ap11");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: false,
      });
    });
    it("should return no datalake, bwBridge nor objectStore for br10 landscape", async () => {
      landscapeIdStub.returns("br10");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: false,
        hasBwBridge: true,
        hasObjectStore: false,
      });
    });
    it("should return datalake, bwBridge and objectStore for jp10 landscape", async () => {
      landscapeIdStub.returns("jp10");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: true,
      });
    });
    it("should return datalake, bwBridge and objectStore for us10 landscape", async () => {
      landscapeIdStub.returns("us10");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: true,
      });
    });
    it("should return no datalake, bwBridge nor objectStore for ca10 landscape", async () => {
      landscapeIdStub.returns("ca10");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: false,
        hasBwBridge: true,
        hasObjectStore: false,
      });
    });
    it("should return no datalake, bwBridge nor objectStore for ap12 landscape", async () => {
      landscapeIdStub.returns("ap12");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: false,
        hasBwBridge: true,
        hasObjectStore: false,
      });
    });
    it("should return datalake, bwBridge and objectStore for eu20 landscape", async () => {
      landscapeIdStub.returns("eu20");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: true,
      });
    });
    it("should return datalake, bwBridge and objectStore for us20 landscape", async () => {
      landscapeIdStub.returns("us20");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: true,
      });
    });
    it("should return datalake and bwBridge for ch20 landscape", async () => {
      landscapeIdStub.returns("ch20");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: false,
      });
    });
    it("should return datalake, bwBridge and objectStore for us30 landscape", async () => {
      landscapeIdStub.returns("us30");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: true,
      });
    });
    it("should return datalake, bwBridge and objectStore for in30 landscape", async () => {
      landscapeIdStub.returns("in30");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: true,
      });
    });
    it("should return datalake, bwBridge and objectStore for eu30 landscape", async () => {
      landscapeIdStub.returns("eu30");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: true,
      });
    });
    it("should return datalake, bwBridge and objectStore for il30 landscape", async () => {
      landscapeIdStub.returns("il30");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: true,
      });
    });
    it("should return datalake and objectStore for sa30 landscape", async () => {
      landscapeIdStub.returns("sa30");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: false,
        hasObjectStore: true,
      });
    });
    it("should return datalake and bwBridge for jp30 landscape", async () => {
      landscapeIdStub.returns("jp30");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: false,
      });
    });
    it("should return devLandscape if provided landscape is invalid", async () => {
      landscapeIdStub.returns("invalidLanscape");
      sinon.assert.match(await getAvailableFeaturesForCurrentLandscape(context), {
        hasDataLake: true,
        hasBwBridge: true,
        hasObjectStore: true,
      });
    });
  });
  describe("ExtraParam FeatureFlag", () => {
    it("Should set feature flags correctly - true case", async () => {
      const extraHandle: IExtraParams | any = {
        dwc: {
          featureFlag: {
            DWC_PROV_TEST_OUTBOUNDIP: true,
          },
        },
      };

      const expectedWithFeatureFlagsOn = {
        dwc: {
          featureFlag: {
            DWC_PROV_TEST_OUTBOUNDIP: true,
          },
        },
      };
      const newParametersWithFeatureFlag = new ExtraParamsHandler(extraHandle);
      newParametersWithFeatureFlag.setFeatureFlag();

      sandbox.assert.match(extraHandle, expectedWithFeatureFlagsOn);
    });
    it("Should set feature flags correctly - false case", async () => {
      const extraHandle: IExtraParams | any = {
        dwc: {
          featureFlag: {
            DWC_PROV_TEST_OUTBOUNDIP: false,
          },
        },
      };

      const expectedWithFeatureFlagsOn = {
        dwc: {
          featureFlag: {
            DWC_PROV_TEST_OUTBOUNDIP: false,
          },
        },
      };
      const newParametersWithFeatureFlag = new ExtraParamsHandler(extraHandle);
      newParametersWithFeatureFlag.setFeatureFlag();

      sandbox.assert.match(extraHandle, expectedWithFeatureFlagsOn);
    });
    it("Should set feature flags correctly - undefined case", async () => {
      const extraHandle: IExtraParams | any = {};

      const expectedWithFeatureFlagsOn = {};
      const newParametersWithFeatureFlag = new ExtraParamsHandler(extraHandle);
      newParametersWithFeatureFlag.setFeatureFlag();

      sandbox.assert.match(extraHandle, expectedWithFeatureFlagsOn);
    });
  });

  describe("Change licenses for metadata format", async () => {
    function createInitialLicense(license?): IGetLicense {
      return {
        thresholdDWCCU: license.thresholdDWCCU ?? 15000,
        thresholdMemory: license.thresholdMemory ?? 32,
        thresholdVCPU: license.thresholdVCPU ?? 2,
        thresholdStorage: license.thresholdStorage ?? 256,
        thresholdDataLakeStorage: 0,
        thresholdBWBridge1: 0,
        thresholdRmsPremiumOutbound: license.thresholdRmsPremiumOutbound ?? 0,
        thresholdRmsNodeHoursIncluded: 200,
        thresholdRmsConcurrencyIncluded: 2,
        thresholdCatalogStorageIncluded: 512,
        thresholdCatalogNodeHoursIncluded: 100,
        thresholdCatalogConcurrencyIncluded: 1,
        thresholdRmsNodeHours: license.thresholdRmsNodeHours ?? 0,
        thresholdRmsConcurrency: 0,
        thresholdCatalogNodeHours: 0,
        thresholdCatalogConcurrency: 0,
        thresholdCatalogStorage: 0,
        thresholdECNBlock: 0,
        thresholdECNPerformanceClass: PerformanceClass.COMPUTE,
        thresholdLargeSystemsStorage: 0,
        thresholdLargeSystemsCompute: 0,
        thresholdLargeSystemsRequests: 0,
      };
    }

    function createExpectLicenseInMetadataFormat(license?) {
      return {
        thresholdMemory: license.thresholdMemory ?? "32",
        thresholdVCPU: license.thresholdVCPU ?? "2",
        thresholdStorage: license.thresholdStorage ?? "256",
        thresholdBWBridge1: license.thresholdBWBridge1 ?? "0",
        thresholdDataLakeStorage: "0",
        thresholdRmsPremiumOutbound: "0",
        thresholdRmsConcurrency: "0",
        thresholdRmsNodeHours: "0",
        thresholdCatalogStorage: "0",
        thresholdECNBlock: "0",
        thresholdECNPerformanceClass: PerformanceClass.COMPUTE,
        thresholdLargeSystemsStorage: "0",
        thresholdLargeSystemsCompute: "0",
        thresholdLargeSystemsRequests: "0",
      };
    }
    it("Should make Licenses be in metadata format", async () => {
      const initialLicense = createInitialLicense({});
      const expectLicenseInMetadataFormat = createExpectLicenseInMetadataFormat({});
      const licenseInMetadataFormat = generateLicensesInMetadataFormat(initialLicense);
      sinon.assert.match(licenseInMetadataFormat, expectLicenseInMetadataFormat);
    });

    it("Should make Licenses be in metadata format with initialValue of bw bridge undefined", async () => {
      const initialLicense = createInitialLicense({ thresholdBWBridge1: undefined });
      const expectLicenseInMetadataFormat = createExpectLicenseInMetadataFormat({ thresholdBWBridge1: "0" });
      const licenseInMetadataFormat = generateLicensesInMetadataFormat(initialLicense);
      sinon.assert.match(licenseInMetadataFormat, expectLicenseInMetadataFormat);
    });

    it("Should make Licenses be in metadata format with initialValue of storage and compute equal to 0", async () => {
      const initialLicense = createInitialLicense({ thresholdStorage: 0, thresholdMemory: 0, thresholdVCPU: 0 });
      const expectLicenseInMetadataFormat = createExpectLicenseInMetadataFormat({
        thresholdMemory: "0",
        thresholdVCPU: "0",
        thresholdStorage: "0",
      });
      const licenseInMetadataFormat = generateLicensesInMetadataFormat(initialLicense);
      sinon.assert.match(licenseInMetadataFormat, expectLicenseInMetadataFormat);
    });
  });
});
