/** @format */

import { Request, ResolvedResponse, StatusError, httpClient } from "@sap/dwc-http-client";
import { MessageEvent } from "@sap/orca-starter-solace";
import * as chai from "chai";
import chaiAsPromised from "chai-as-promised";
import { StatusCodes } from "http-status-codes";
import sinon from "sinon";
import sinonChai from "sinon-chai";
import { ExternalCallCategories } from "../../../../shared/common/ExternalCallCategories";
import { BwzCallbackConfig, ICdmPayload, getMD5Hash } from "../../../cdm";
import * as BwzCallBack from "../../../cdm/bwzCallback";
import * as Generator from "../../../cdm/generator";
import { testTenantUuid } from "../../../lib/node";
import {
  RoleEventAction,
  RoleUpdateConsumer,
  RoleUpdateMessage,
  retriableStatusCodes,
} from "../../../messageQueuing/consumer/RoleUpdateConsumer";
chai.use(sinonChai);
chai.use(chaiAsPromised);

const expect = chai.expect;
const PORTAL_TEST_URL =
  "https://portal-service.cfapps.test_landscape.hana.ondemand.com/semantic/public/callback/autosync/ef3878";
const mockCDM = {} as ICdmPayload;
const retriableCallbackErrors: StatusError[] = retriableStatusCodes.map(
  (code) => new StatusError(code, "The request failed but is retriable")
);
const nonRetriableCallbackError = new StatusError(
  StatusCodes.UNAUTHORIZED,
  "This error makes the request non-retriable"
);

describe("Consuming user role update events", function () {
  this.timeout(30000);
  let messageEvent: MessageEvent;
  let sandbox: sinon.SinonSandbox;
  let fetchBwzCallbackConfigStub: sinon.SinonStub;
  let httpClientCallStub: sinon.SinonStub;
  let cdmGenerator: sinon.SinonStub;
  let logErrorSpy: sinon.SinonSpy;

  const roleEventConsumer = new RoleUpdateConsumer();
  const roleUpdateMessage: RoleUpdateMessage = {
    roleId: "Test_Role",
    action: RoleEventAction.MODIFIED,
    fpaVersion: "123.456",
    tenantId: testTenantUuid,
  };
  const bwzCallbackConfigs: BwzCallbackConfig[] = [
    {
      url: PORTAL_TEST_URL,
      cdmHash: "d41d8cd98f00b204e9800998ecf8427e",
      cdmProvider: "datasphere_1",
      token: "callback-access-token-1",
      prefer: "odata.track-changes, sap-cflp-callback-on-change;odata.callback;maxpagesize=200",
    },
    {
      url: PORTAL_TEST_URL,
      cdmHash: "900150983cd24fb0d6963f7d28e17f72",
      cdmProvider: "datasphere_2",
      token: "callback-access-token-2",
      prefer: "odata.track-changes, sap-cflp-callback-on-change;odata.callback;maxpagesize=200",
    },
  ];

  beforeEach(function () {
    sandbox = sinon.createSandbox();
    logErrorSpy = sandbox.spy(RoleUpdateConsumer.getLogger(), "logError");

    messageEvent = {
      acceptMessage: sandbox.stub(),
      rejectMessage: sandbox.stub(),
      releaseMessage: sandbox.stub(),
      getMessage: () => ({
        body: Buffer.from(JSON.stringify(roleUpdateMessage)),
      }),
    } as unknown as MessageEvent;

    const callOrig = httpClient.call;
    httpClientCallStub = sandbox.stub(httpClient, "call").callsFake(async (req: Request): Promise<ResolvedResponse> => {
      if (req.url === PORTAL_TEST_URL) {
        return {
          ok: true,
          redirected: false,
          status: 200,
          statusText: "OK",
          url: req.url,
          type: "basic",
        } as ResolvedResponse;
      }
      return callOrig.bind(this)(req);
    });

    cdmGenerator = sandbox.stub(Generator, "generateModel").resolves(mockCDM);
  });

  afterEach(async function () {
    // eslint-disable-next-line @typescript-eslint/unbound-method
    expect(messageEvent.acceptMessage).to.have.been.calledOnce;

    sandbox.restore();
  });

  it("Should consume a role change event and call back Build Workzone", async function () {
    const callbackConfigs = bwzCallbackConfigs;
    fetchBwzCallbackConfigStub = sandbox.stub(BwzCallBack, "fetchBwzCallbackConfig").resolves(callbackConfigs);

    await roleEventConsumer.consume(messageEvent);

    expect(fetchBwzCallbackConfigStub).to.have.been.calledOnce;
    expect(cdmGenerator).to.have.been.calledOnce;
    expect(httpClientCallStub.callCount).to.be.equal(callbackConfigs.length);
    // eslint-disable-next-line @typescript-eslint/prefer-for-of
    for (let i = 0; i < callbackConfigs.length; ++i) {
      const callBackRequest = httpClientCallStub.args[i][0] as Request;
      expect(callBackRequest).to.have.property("url").that.is.equal(callbackConfigs[i].url);
      const requestOpts = callBackRequest.opts;
      expect(!!requestOpts).to.be.true;
      expect(requestOpts!.headers).to.not.be.undefined;
      expect((requestOpts!.headers as unknown as Headers).get("Authorization")).to.include(callbackConfigs[i].token);
      expect(requestOpts!.callCategory).to.be.equal(ExternalCallCategories.CDM);
      expect(requestOpts!.retry).to.not.be.undefined;
      expect(requestOpts!.retry!.delay).to.be.a("number");
      expect(requestOpts!.retry!.maxRetries).to.be.a("number");
      expect(requestOpts!.retry!.preventRetry).to.be.a("function");
      retriableCallbackErrors.forEach((err) => expect(requestOpts!.retry!.preventRetry!(err)).to.be.false);
      expect(requestOpts!.retry!.preventRetry!(nonRetriableCallbackError)).to.be.true;
    }
  });

  it("Should accept message and return if the message is empty", async function () {
    messageEvent.getMessage = () => null;
    fetchBwzCallbackConfigStub = sandbox.stub(BwzCallBack, "fetchBwzCallbackConfig").resolves();
    const decodeMessageSpy = sandbox.spy(roleEventConsumer, "decodeMessage");
    const validateMessageSpy = sandbox.spy(roleEventConsumer as any, "validateMessage");
    const callBackSpy = sandbox.spy(roleEventConsumer as any, "callBack");

    await roleEventConsumer.consume(messageEvent);

    expect(decodeMessageSpy).to.be.not.have.been.called;
    expect(validateMessageSpy).to.not.have.been.called;
    expect(fetchBwzCallbackConfigStub).to.not.have.been.called;
    expect(cdmGenerator).to.not.have.been.called;
    expect(callBackSpy).to.not.have.been.called;
  });

  it("Should validate message, accept it and return if there are no stored call back configurations", async function () {
    fetchBwzCallbackConfigStub = sandbox.stub(BwzCallBack, "fetchBwzCallbackConfig").resolves([]);
    const decodeMessageSpy = sandbox.spy(roleEventConsumer, "decodeMessage");
    const validateMessageSpy = sandbox.spy(roleEventConsumer as any, "validateMessage");
    const callBackSpy = sandbox.spy(roleEventConsumer as any, "callBack");

    await roleEventConsumer.consume(messageEvent);

    expect(decodeMessageSpy).to.have.been.calledOnce;
    const roleEvent = await decodeMessageSpy.returnValues[0];
    expect(validateMessageSpy).to.have.been.calledOnceWith(roleEvent);
    expect(fetchBwzCallbackConfigStub).to.have.been.calledOnce;
    expect(cdmGenerator).to.not.have.been.called;
    expect(callBackSpy).to.not.have.been.called;
  });

  it("Should only call back build WZ if the CDM has changed since last pull", async function () {
    const callbackConfigs: BwzCallbackConfig[] = [
      {
        url: PORTAL_TEST_URL,
        cdmHash: getMD5Hash(JSON.stringify(mockCDM)),
        cdmProvider: "datasphere",
        token: "callback-access-token",
        prefer: "odata.track-changes, sap-cflp-callback-on-change;odata.callback;maxpagesize=200",
      },
    ];
    fetchBwzCallbackConfigStub = sandbox.stub(BwzCallBack, "fetchBwzCallbackConfig").resolves(callbackConfigs);
    const decodeMessageSpy = sandbox.spy(roleEventConsumer, "decodeMessage");
    const validateMessageSpy = sandbox.spy(roleEventConsumer as any, "validateMessage");
    const callBackSpy = sandbox.spy(roleEventConsumer as any, "callBack");

    await roleEventConsumer.consume(messageEvent);

    expect(decodeMessageSpy).to.have.been.calledOnce;
    const roleEvent = await decodeMessageSpy.returnValues[0];
    expect(validateMessageSpy).to.have.been.calledOnceWith(roleEvent);
    expect(fetchBwzCallbackConfigStub).to.have.been.calledOnce;
    expect(cdmGenerator).to.have.been.calledOnce;
    expect(callBackSpy).to.not.have.been.called;
  });

  it("Should fail to consume the message with an error that contains 'Failed to decode request message:' if the message content is incompatible with decode", async function () {
    messageEvent.getMessage = () => ({
      body: Buffer.from("+incompatible message+"),
    });
    fetchBwzCallbackConfigStub = sandbox.stub(BwzCallBack, "fetchBwzCallbackConfig").resolves();
    const decodeMessageSpy = sandbox.spy(roleEventConsumer, "decodeMessage");
    const validateMessageSpy = sandbox.spy(roleEventConsumer as any, "validateMessage");
    const callBackSpy = sandbox.spy(roleEventConsumer as any, "callBack");

    await roleEventConsumer.consume(messageEvent);

    expect(decodeMessageSpy).to.be.calledOnce;
    await expect(decodeMessageSpy.getCall(0).returnValue)
      .to.eventually.be.rejectedWith(Error)
      .that.has.property("message")
      .that.include("Failed to decode request message:");
    expect(logErrorSpy).to.have.been.calledOnce;
    const errorMessage = logErrorSpy.args[0][0];
    expect(errorMessage).to.include("Failed to decode request message:");
    expect(validateMessageSpy).to.not.have.been.called;
    expect(fetchBwzCallbackConfigStub).to.not.have.been.called;
    expect(cdmGenerator).to.not.have.been.called;
    expect(callBackSpy).to.not.have.been.called;
  });

  it("Should throw an error if 'roleId', 'tenantId' or 'action' is missing in the message", async function () {
    const faultyMessages: Array<Partial<RoleUpdateMessage>> = [
      {
        action: RoleEventAction.MODIFIED,
        fpaVersion: "123.456",
        tenantId: testTenantUuid,
      },
      {
        roleId: "Test_Role",
        fpaVersion: "123.456",
        tenantId: testTenantUuid,
      },
      {
        roleId: "Test_Role",
        action: RoleEventAction.MODIFIED,
        fpaVersion: "123.456",
      },
    ];
    fetchBwzCallbackConfigStub = sandbox.stub(BwzCallBack, "fetchBwzCallbackConfig").resolves();
    const decodeMessageSpy = sandbox.spy(roleEventConsumer, "decodeMessage");
    const validateMessageSpy = sandbox.spy(roleEventConsumer as any, "validateMessage");
    const callBackSpy = sandbox.spy(roleEventConsumer as any, "callBack");

    for (const message of faultyMessages) {
      messageEvent.getMessage = () => ({
        body: Buffer.from(JSON.stringify(message)),
      });
      decodeMessageSpy.resetHistory();
      validateMessageSpy.resetHistory();
      callBackSpy.resetHistory();
      logErrorSpy.resetHistory();
      (messageEvent.acceptMessage as unknown as sinon.SinonSpy).resetHistory();

      await roleEventConsumer.consume(messageEvent);

      expect(decodeMessageSpy).to.have.been.calledOnce;
      const roleEvent = await decodeMessageSpy.returnValues[0];
      expect(validateMessageSpy).to.have.been.calledOnceWith(roleEvent);
      expect(validateMessageSpy.getCall(0).returnValue).to.be.have.property("valid").that.is.false;
      expect(logErrorSpy).to.have.been.calledOnce;
      expect(logErrorSpy.args[0][0]).to.include("Message content is invalid");
      expect(fetchBwzCallbackConfigStub).to.not.have.been.called;
      expect(cdmGenerator).to.not.have.been.called;
      expect(callBackSpy).to.not.have.been.called;
    }
  });

  it("Should accept only known role event actions", async function () {
    const messageWithInvalidAction = {
      roleId: "Test_Role",
      action: "invalidAction",
      fpaVersion: "123.456",
      tenantId: testTenantUuid,
    };

    messageEvent.getMessage = () => ({
      body: Buffer.from(JSON.stringify(messageWithInvalidAction)),
    });
    fetchBwzCallbackConfigStub = sandbox.stub(BwzCallBack, "fetchBwzCallbackConfig").resolves();
    const decodeMessageSpy = sandbox.spy(roleEventConsumer, "decodeMessage");
    const validateMessageSpy = sandbox.spy(roleEventConsumer as any, "validateMessage");
    const callBackSpy = sandbox.spy(roleEventConsumer as any, "callBack");

    await roleEventConsumer.consume(messageEvent);

    expect(decodeMessageSpy).to.have.been.calledOnce;
    const roleEvent = await decodeMessageSpy.returnValues[0];
    expect(validateMessageSpy).to.have.been.calledOnceWith(roleEvent);
    expect(validateMessageSpy.getCall(0).returnValue).to.be.have.property("valid").that.is.false;
    expect(logErrorSpy).to.have.been.calledOnce;
    expect(logErrorSpy.args[0][0]).to.include("Message content is invalid");
    expect(fetchBwzCallbackConfigStub).to.not.have.been.called;
    expect(cdmGenerator).to.not.have.been.called;
    expect(callBackSpy).to.not.have.been.called;
  });

  it("Should accept messages without fpaVersion", async function () {
    const messageWithMissingFpaVersion = {
      roleId: "Test_Role",
      action: RoleEventAction.CREATED,
      tenantId: testTenantUuid,
    };
    messageEvent.getMessage = () => ({
      body: Buffer.from(JSON.stringify(messageWithMissingFpaVersion)),
    });
    fetchBwzCallbackConfigStub = sandbox.stub(BwzCallBack, "fetchBwzCallbackConfig").resolves(bwzCallbackConfigs);
    const decodeMessageSpy = sandbox.spy(roleEventConsumer, "decodeMessage");
    const validateMessageSpy = sandbox.spy(roleEventConsumer as any, "validateMessage");
    const callBackSpy = sandbox.spy(roleEventConsumer as any, "callBack");

    await roleEventConsumer.consume(messageEvent);

    expect(decodeMessageSpy).to.have.been.calledOnce;
    const roleEvent = await decodeMessageSpy.returnValues[0];
    expect(validateMessageSpy).to.have.been.calledOnceWith(roleEvent);
    expect(validateMessageSpy.getCall(0).returnValue).to.be.have.property("valid").that.is.true;
    expect(fetchBwzCallbackConfigStub).to.have.been.calledOnce;
    expect(cdmGenerator).to.have.been.calledOnce;
    expect(callBackSpy).to.have.been.calledTwice;
    expect(logErrorSpy).to.not.have.been.called;
  });

  it("Should not accept message if the fpaVersion is not a literal", async function () {
    const messageWithNonLiteralFpaVersion = {
      roleId: "Test_Role",
      action: RoleEventAction.CREATED,
      fpaVersion: 123.456,
      tenantId: testTenantUuid,
    };
    messageEvent.getMessage = () => ({
      body: Buffer.from(JSON.stringify(messageWithNonLiteralFpaVersion)),
    });
    fetchBwzCallbackConfigStub = sandbox.stub(BwzCallBack, "fetchBwzCallbackConfig").resolves();
    const decodeMessageSpy = sandbox.spy(roleEventConsumer, "decodeMessage");
    const validateMessageSpy = sandbox.spy(roleEventConsumer as any, "validateMessage");
    const callBackSpy = sandbox.spy(roleEventConsumer as any, "callBack");

    await roleEventConsumer.consume(messageEvent);

    expect(decodeMessageSpy).to.have.been.calledOnce;
    const roleEvent = await decodeMessageSpy.returnValues[0];
    expect(validateMessageSpy).to.have.been.calledOnceWith(roleEvent);
    expect(validateMessageSpy.getCall(0).returnValue).to.be.have.property("valid").that.is.false;
    expect(logErrorSpy).to.have.been.calledOnce;
    expect(logErrorSpy.args[0][0]).to.include("Message content is invalid");
    expect(fetchBwzCallbackConfigStub).to.not.have.been.called;
    expect(cdmGenerator).to.not.have.been.called;
    expect(callBackSpy).to.not.have.been.called;
  });
});
