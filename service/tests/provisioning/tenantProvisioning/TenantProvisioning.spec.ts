/** @format */

import { MessageEvent } from "@sap/orca-starter-solace";
import chai, { expect } from "chai";
import sinonChai from "sinon-chai";

import { TimeUnit, truncate } from "@sap/deepsea-utils";
import { TenantClassification } from "@sap/dwc-tms-provider";
import chaiAsPromised from "chai-as-promised";
import sinon, { SinonStub } from "sinon";
import { TenantType } from "../../../../shared/provisioning/ftc/types";
import { DiEPodCount } from "../../../diEmbedded/podCount/DiEPodCount";
import { FeatureFlagProvider } from "../../../featureflags/FeatureFlagProvider";
import { TenantInformationProvider } from "../../../featureflags/TenantInformationProvider";
import { ITenantInformation, TenantStatus, TmsTags } from "../../../featureflags/types";
import { DbClient } from "../../../lib/DbClient";
import * as node from "../../../lib/node";
import { testTenantUuid } from "../../../lib/node";
import {
  ITenantProvisioningMessage,
  TenantProvisioningConsumer,
} from "../../../messageQueuing/consumer/TenantProvisioningConsumer";
import * as index from "../../../provisioning";
import { TenantProvisioning } from "../../../provisioning/TenantProvisioning";
import { AbapBridgeService } from "../../../provisioning/bwBridge/BwBridgeService";
import { ExtraParamsHandler } from "../../../provisioning/extraParameters/ExtraParamsHandler";
import { ElasticCustomerHanaProvisioning } from "../../../provisioning/flexible/ElasticCustomerHanaProvisioning";
import { getRequestOriginFromTenantClassification } from "../../../provisioning/flexible/telemetry/helpTelemetry";
import * as helpers from "../../../provisioning/helpers";
import { BasicProvisioning } from "../../../provisioning/provisioningFlows/BasicProvisioning";
import { ProvisioningActions } from "../../../provisioning/types";
import { RepositoryOnboardingClient } from "../../../repository/client/repositoryOnboardingClient";
import { RequestContext } from "../../../repository/security/requestContext";
import * as DbConnections from "../../../reuseComponents/onboarding/src/DbConnections";
import * as adminActivator from "../../../reuseComponents/onboarding/src/adminActivator";
import { ProvStep } from "../../../reuseComponents/onboarding/src/types";
import { CustomerHana } from "../../../reuseComponents/spaces/src";
import { CustomerHanaRuntimeData } from "../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import * as logLevel from "../../../routes/support/logging/setMinimumLogLevel";
import { TenantMetadataBuilder } from "./TenantMetadataBuilder";
import { buildProvisioningMessage } from "./provisioningMetadataCreation";

chai.use(sinonChai);
chai.use(chaiAsPromised);

describe("service/provisioning/TenantProvisioning", function () {
  const context = RequestContext.createFromTenantId(testTenantUuid);
  const sandbox = sinon.createSandbox();

  let ffStub, ffSetStub: any;
  let repositoryActivitiesStub: SinonStub;
  let createHana: SinonStub;
  let configureHanaSystemParams: SinonStub;
  let createNativeTenantForLightweightTenant: SinonStub;
  let provisionLightweightDSPArtifacts: SinonStub;
  let createOrUpdateDataLakeStub: SinonStub;
  let deleteDataLakeForFlexibleTenantStub: SinonStub;
  let provisionCustomerHanaArtifactsStub: SinonStub;
  let storeExternalHanaCredentialsStub: SinonStub;
  let sendSkipMessageStub: SinonStub;
  let sendInProgressMessageStub: SinonStub;
  let deprovisioningOnUpdateStub: SinonStub;
  let saveResourcesStub: SinonStub;
  let updateHanaStub: SinonStub;
  let hasBridgeInstanceStub: SinonStub;
  let RmsPodStub: SinonStub;
  let isDataLakeConnectedToSpaceStub: SinonStub;
  let isSdpConversionActiveStub: SinonStub;
  let setSdpConversionStatusAsSuccessfulStub: SinonStub;
  let isStarkiller: SinonStub;
  let adminApiAccessStub: SinonStub;
  let updateAbapBrigdeInstanceStub: SinonStub;
  let isBwBridgeUpsizingEnabledStub: SinonStub;
  let makeTenantMetadataBuilder: TenantMetadataBuilder;
  let tenantInformationProviderStub: SinonStub;

  function createTestStubs() {
    sandbox.stub(TimeUnit.prototype, "toMillis").returns(0);
    sandbox.stub(RequestContext, "createFromTenantId").returns(context);
    sandbox.stub(TenantProvisioningConsumer, "reply").resolves();
    sandbox.stub(logLevel, "setMinimumLogLevel").returns(Promise.resolve());
    sendSkipMessageStub = sandbox.stub(TenantProvisioning.prototype, "sendSkipMessage" as any);
    sendInProgressMessageStub = sandbox.stub(TenantProvisioning.prototype, "sendInProgressMessage" as any);
    repositoryActivitiesStub = sandbox.stub(TenantProvisioning.prototype, "executeRepositoryActivities" as any);
    createOrUpdateDataLakeStub = sandbox.stub(TenantProvisioning.prototype, "createOrUpdateDataLake" as any);
    deleteDataLakeForFlexibleTenantStub = sandbox.stub(
      TenantProvisioning.prototype,
      "deleteDataLakeForFlexibleTenant" as any
    );
    provisionCustomerHanaArtifactsStub = sandbox.stub(
      TenantProvisioning.prototype,
      "provisionCustomerHanaArtifacts" as any
    );
    isSdpConversionActiveStub = sandbox
      .stub(TenantProvisioning.prototype, "isSdpConversionActive" as any)
      .resolves(true);
    setSdpConversionStatusAsSuccessfulStub = sandbox.stub(
      TenantProvisioning.prototype,
      "setSdpConversionStatusAsSuccessful" as any
    );
    deprovisioningOnUpdateStub = sandbox.stub(index, "deprovisioningOnUpdate" as any);
    saveResourcesStub = sandbox.stub(helpers, "saveResources" as any);
    hasBridgeInstanceStub = sandbox.stub(AbapBridgeService, "hasBridgeInstanceInBtp" as any);
    (updateAbapBrigdeInstanceStub = sandbox.stub(AbapBridgeService, "updateAbapBrigdeInstance")),
      (isDataLakeConnectedToSpaceStub = sandbox.stub(CustomerHanaRuntimeData.prototype, "isDataLakeConnectedToSpace"));
    isStarkiller = sandbox.stub(node, "isStarkiller");
    ffSetStub = sandbox.stub(FeatureFlagProvider, "setFeatureflag").resolves();
    ffStub = sandbox.stub(FeatureFlagProvider, "getFeatureFlags");
    sandbox.useFakeTimers({
      now: new Date(2024, 9, 1, 0, 0),
      shouldAdvanceTime: false,
      toFake: ["Date"],
    });

    RmsPodStub = sandbox.stub(DiEPodCount.prototype, "updateRMSPodCount").resolves();
    sandbox.stub(CustomerHanaRuntimeData.prototype, "getStorageConsumptionOverview").resolves();
    sandbox.stub(CustomerHanaRuntimeData.prototype, "getOverallAssignedStorage").resolves();
    adminApiAccessStub = sandbox.stub(TenantProvisioning.prototype, "provisionAdminApiAccess" as any);
    isBwBridgeUpsizingEnabledStub = sandbox.stub(helpers, "isBwBridgeUpsizingEnabled");
    sandbox.stub(CustomerHana, "fromTenantId").resolves(sandbox.createStubInstance(CustomerHana));
    tenantInformationProviderStub = sandbox.stub(TenantInformationProvider, "getTenantInformation");
  }

  before(async function () {
    createTestStubs();
  });

  after(function () {
    sandbox.restore();
    sinon.restore();
  });

  afterEach(function () {
    sandbox.resetHistory();
  });

  function makeProvisioningMessage(
    action: ProvisioningActions,
    tenantMetadata: any,
    oldTenantMetadata?: any
  ): ITenantProvisioningMessage {
    return buildProvisioningMessage(context, action, tenantMetadata, oldTenantMetadata);
  }

  describe("Tenant creation", function () {
    function assertCreationCallOrderForFlexible() {
      sandbox.assert.callOrder(
        repositoryActivitiesStub,
        createHana,
        provisionCustomerHanaArtifactsStub,
        createOrUpdateDataLakeStub,
        isSdpConversionActiveStub,
        setSdpConversionStatusAsSuccessfulStub,
        configureHanaSystemParams,
        RmsPodStub
      );
    }

    function assertCreationCallOrderForNonFlexible() {
      sandbox.assert.callOrder(
        repositoryActivitiesStub,
        createHana,
        provisionCustomerHanaArtifactsStub,
        createOrUpdateDataLakeStub,
        isSdpConversionActiveStub,
        setSdpConversionStatusAsSuccessfulStub,
        configureHanaSystemParams,
        RmsPodStub
      );
    }

    function assertCreationCallOrderForLightweightDSP() {
      sandbox.assert.callOrder(
        repositoryActivitiesStub,
        createNativeTenantForLightweightTenant,
        provisionLightweightDSPArtifacts,
        isSdpConversionActiveStub,
        setSdpConversionStatusAsSuccessfulStub,
        RmsPodStub
      );
    }
    before(async function () {
      createHana = sandbox.stub(TenantProvisioning.prototype, "createHana" as any);
      configureHanaSystemParams = sandbox.stub(TenantProvisioning.prototype, "configureHanaSystemParams" as any);
      createNativeTenantForLightweightTenant = sandbox.stub(
        TenantProvisioning.prototype,
        "createNativeTenantForLightweightTenant" as any
      );
      provisionLightweightDSPArtifacts = sandbox.stub(
        TenantProvisioning.prototype,
        "provisionLightweightDSPArtifacts" as any
      );
      storeExternalHanaCredentialsStub = sandbox.stub(
        TenantProvisioning.prototype,
        "storeExternalHanaCredentials" as any
      );
    });

    beforeEach(() => {
      isStarkiller.resolves(false);
      ffStub.resolves({});
      repositoryActivitiesStub.resolves();
      createHana.resolves();
      makeTenantMetadataBuilder = new TenantMetadataBuilder();
    });

    it("should call correct workflow steps for Standard Tenant with licenses using memory/vcpu and storage", async function () {
      const tenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().build();

      await ffStub.resolves({ DWCO_M_TABLE_STATISTICS_SELECT: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      await tenantProvisioning.createTenant();

      assertCreationCallOrderForNonFlexible();
    });

    it("should call correct workflow steps for Flexible Tenant", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withMinimumSizeTenantLicense()
        .build();

      await ffStub.resolves({ DWCO_M_TABLE_STATISTICS_SELECT: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      await tenantProvisioning.createTenant();
      assertCreationCallOrderForFlexible();
    });

    it("should call correct workflow steps for CIS Free Tier Tenant", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantType(TenantType.CPEA)
        .withTenantFreeTier(true)
        .build();

      await ffStub.resolves({ DWCO_M_TABLE_STATISTICS_SELECT: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      await tenantProvisioning.createTenant();
      assertCreationCallOrderForFlexible();
    });

    it("should call correct workflow steps for CIS Standard Tenant", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantType(TenantType.CPEA)
        .build();

      await ffStub.resolves({ DWCO_M_TABLE_STATISTICS_SELECT: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      await tenantProvisioning.createTenant();
      assertCreationCallOrderForFlexible();
    });

    it("should call correct workflow steps for BYOH", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withMinimumSizeTenantLicense()
        .build();

      const extraParams = new ExtraParamsHandler({
        dwc: {
          bringYourOwnHana: {
            instanceName: "db1",
            host: "mydb.hanacloud.ondemand.com",
            port: "443",
            password: "Initial1!",
            user: "TA_USER",
            certificate: "",
          },
        },
      });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message, extraParams);
      await tenantProvisioning.createTenant();
      sandbox.assert.callOrder(
        repositoryActivitiesStub,
        storeExternalHanaCredentialsStub,
        provisionCustomerHanaArtifactsStub,
        isSdpConversionActiveStub,
        setSdpConversionStatusAsSuccessfulStub
      );
    });

    it("should call correct workflow steps for lightweight DSP with lightweight DSP ON", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantLicense({ thresholdDataLakeStorage: "1", thresholdDataLakeCompute: "4" })
        .withTenantType(TenantType.CPEA)
        .withTenantFreeTier(false)
        .build();

      isStarkiller.resolves(true);

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      await tenantProvisioning.createTenant();
      assertCreationCallOrderForLightweightDSP();
    });

    it("should call the admin api access provisioning for new tenants", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withMinimumSizeTenantLicense()
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      await tenantProvisioning.createTenant();

      sandbox.assert.callCount(adminApiAccessStub, 1);
    });

    it("Should fail tenant creation if Repository Onboarding fails", async () => {
      repositoryActivitiesStub.throws(new Error("Repository Error"));

      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withMinimumSizeTenantLicense()
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      await expect(tenantProvisioning.createTenant()).to.be.rejectedWith("Repository Error");
    });

    it("Should fail tenant creation if Customer HANA creation fails", async () => {
      createHana.throws(new Error("Hana Error"));

      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withMinimumSizeTenantLicense()
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      await expect(tenantProvisioning.createTenant()).to.be.rejectedWith("Hana Error");
    });

    it("Should get request origin from especific tentant classification", async () => {
      const getRequestOriginCis = getRequestOriginFromTenantClassification(TenantClassification.DWC_CPEA);
      expect(getRequestOriginCis).to.be.equal("CIS");

      const getRequestOriginSapBdc = getRequestOriginFromTenantClassification(TenantClassification.DWC_BDC);
      expect(getRequestOriginSapBdc).to.be.equal("S4M");

      const getRequestOriginSapCockpit = getRequestOriginFromTenantClassification(TenantClassification.BDCC);
      expect(getRequestOriginSapCockpit).to.be.equal("S4M");

      const getRequestOriginSpc = getRequestOriginFromTenantClassification(TenantClassification.DWC_POC);
      expect(getRequestOriginSpc).to.be.equal("SPC");
    });

    it("Should set BDC Feature Flags if DWCBDC_INTERNAL", async () => {
      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withMinimumSizeTenantLicense()
        .withTenantClassification(TenantClassification.DWC_BDC_INTERNAL)
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      await tenantProvisioning.enableBdcToggles(tenantMetadata as ITenantInformation);

      expect(ffSetStub).to.be.calledWith(context, "DWCO_BDC", true);
      expect(ffSetStub).to.be.calledWith(context, "DWCO_BDC_GA", true);
    });

    it("Should set BDC Feature Flags if DWCBDC", async () => {
      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withMinimumSizeTenantLicense()
        .withTenantClassification(TenantClassification.DWC_BDC)
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      await tenantProvisioning.enableBdcToggles(tenantMetadata as ITenantInformation);

      expect(ffSetStub).to.be.calledWith(context, "DWCO_BDC", true);
      expect(ffSetStub).to.be.calledWith(context, "DWCO_BDC_GA", true);
    });

    it("Should NOT set BDC Feature Flags if DWC", async () => {
      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withMinimumSizeTenantLicense()
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      ffStub.resetHistory();
      await tenantProvisioning.enableBdcToggles(tenantMetadata as ITenantInformation);

      expect(ffSetStub).to.not.be.called;
    });

    it("Should not throw if DWCO_BDC and DWCO_BDC_GA feature flags fail to be enabled.", async function () {
      ffSetStub.throws(new Error("FF Failed"));
      sandbox.stub(BasicProvisioning.prototype, "enableBdcTogglesWithPersist" as any).throws();

      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withMinimumSizeTenantLicense()
        .withTenantClassification(TenantClassification.DWC_BDC)
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);

      const tenantProvisioning = new TenantProvisioning(context, message);

      await expect(tenantProvisioning.createTenant()).to.be.fulfilled;
    });
  });

  describe("Tenant update", function () {
    beforeEach(() => {
      makeTenantMetadataBuilder = new TenantMetadataBuilder();
    });

    before(async function () {
      updateHanaStub = sandbox.stub(TenantProvisioning.prototype, "updateHana" as any);
    });

    it("should skip when status is FOR_DELETION", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantStatus(TenantStatus.FOR_DELETION)
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      await tenantProvisioning.updateTenant({} as MessageEvent);
      sandbox.assert.called(sendSkipMessageStub);
    });

    it("license with 3 maximum parallel pods", async function () {
      const tenantInfo = {
        tags: [TmsTags.DWC_FEATURE_TAG],
        type: undefined,
        license: {},
        status: TenantStatus.ACTIVE,
        uuid: "",
        versionUuid: "",
        useCloudId: false,
      } as ITenantInformation;

      tenantInformationProviderStub.resolves(tenantInfo);

      const oldMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantLicense({
          thresholdRmsConcurrency: "0",
        })
        .build();

      const tenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantLicense({
          thresholdRmsConcurrency: "2",
        })
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, oldMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      await tenantProvisioning.updateTenant({} as MessageEvent);

      sandbox.assert.calledWithMatch(RmsPodStub, 3);
    });
    it("license with 3 maximum parallel pods thresholdRmsConcurrencyIncluded change", async function () {
      const tenantInfo = {
        tags: [TmsTags.DWC_FEATURE_TAG],
        type: undefined,
        license: {},
        status: TenantStatus.ACTIVE,
        uuid: "",
        versionUuid: "",
        useCloudId: false,
      } as ITenantInformation;

      tenantInformationProviderStub.resolves(tenantInfo);

      tenantInformationProviderStub.resolves(tenantInfo);
      const oldMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantLicense({
          thresholdRmsConcurrencyIncluded: "0",
        })
        .build();

      const tenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantLicense({
          thresholdRmsConcurrencyIncluded: "2",
        })
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, oldMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      await tenantProvisioning.updateTenant({} as MessageEvent);

      sandbox.assert.calledWithMatch(RmsPodStub, 2);
    });

    it("license should not change", async function () {
      const oldMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantLicense({ thresholdRmsConcurrency: "2" })
        .build();

      const tenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantLicense({ thresholdRmsConcurrency: "2" })
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, oldMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      await tenantProvisioning.updateTenant({} as MessageEvent);

      sandbox.assert.notCalled(RmsPodStub);
    });

    it("should skip update and trigger DWC removal from SAC", async function () {
      const tenantMetadata = makeTenantMetadataBuilder.build();

      const extraParams = new ExtraParamsHandler({
        dwc: {
          tenantLifeCycle: {
            dropDwc: true,
          },
        },
      });

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message, extraParams);
      await tenantProvisioning.updateTenant({} as MessageEvent);
      sandbox.assert.called(deprovisioningOnUpdateStub);
    });

    it("should skip update if extraParams skipConfig is set.", async function () {
      const tenantMetadata = makeTenantMetadataBuilder.build();

      const extraParams = new ExtraParamsHandler({
        dwc: {
          tenantLifeCycle: {
            skipConfig: true,
          },
        },
      });

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message, extraParams);
      await tenantProvisioning.updateTenant({} as MessageEvent);
      sandbox.assert.called(saveResourcesStub);
      sandbox.assert.called(sendSkipMessageStub);
    });

    it("when tenant gets suspended, should save resources and skip update", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantType(TenantType.DWC)
        .withTenantStatus(TenantStatus.SUSPENDED)
        .build();

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withTenantType(TenantType.DWC)
        .withTenantStatus(TenantStatus.ACTIVE)
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, oldTenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      await tenantProvisioning.updateTenant({} as MessageEvent);
      sandbox.assert.called(saveResourcesStub);
      sandbox.assert.called(sendSkipMessageStub);
    });

    it("when a suspended tenant gets set to active, should restart resources", async function () {
      const tenantMetadata = makeTenantMetadataBuilder.withTenantStatus(TenantStatus.ACTIVE).build();

      const oldTenantMetadata = makeTenantMetadataBuilder.withTenantStatus(TenantStatus.SUSPENDED).build();

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, oldTenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      await tenantProvisioning.updateTenant({} as MessageEvent);
      sandbox.assert.called(saveResourcesStub);
      sandbox.assert.notCalled(sendSkipMessageStub);
    });

    it("should not call upsize functions since no licenses changed", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withMinimumSizeTenantLicense()
        .build();

      const messageForStandardTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        tenantMetadata
      );
      const standardTenantProvisioning = new TenantProvisioning(context, messageForStandardTenant);
      await standardTenantProvisioning.updateTenant({} as MessageEvent);

      const messageForFlexibleTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        tenantMetadata
      );
      const flexibleTenantProvisioning = new TenantProvisioning(context, messageForFlexibleTenant);
      await flexibleTenantProvisioning.updateTenant({} as MessageEvent);

      sandbox.assert.notCalled(createOrUpdateDataLakeStub);
      sandbox.assert.notCalled(updateHanaStub);
    });

    it("should call both HANA and Data Lake upsize functions", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdStorage: "256",
          thresholdMemory: "64",
          thresholdVCPU: "4",
          thresholdDataLakeStorage: "2",
          thresholdDataLakeCompute: "8",
        })
        .build();

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdStorage: "128",
          thresholdMemory: "32",
          thresholdVCPU: "2",
          thresholdDataLakeStorage: "1",
          thresholdDataLakeCompute: "4",
        })
        .build();

      const messageForStandardTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const standardTenantProvisioning = new TenantProvisioning(context, messageForStandardTenant);
      await standardTenantProvisioning.updateTenant({} as MessageEvent);

      const messageForFlexibleTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const flexibleTenantProvisioning = new TenantProvisioning(context, messageForFlexibleTenant);
      await flexibleTenantProvisioning.updateTenant({} as MessageEvent);

      sandbox.assert.called(createOrUpdateDataLakeStub);
      sandbox.assert.called(updateHanaStub);
    });

    it("should only call HANA upsize functions", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdStorage: "256",
          thresholdMemory: "64",
          thresholdVCPU: "4",
          thresholdDataLakeStorage: "1",
          thresholdDataLakeCompute: "4",
        })
        .build();

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withMinimumSizeTenantLicense()
        .build();

      const messageForStandardTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const standardTenantProvisioning = new TenantProvisioning(context, messageForStandardTenant);
      await standardTenantProvisioning.updateTenant({} as MessageEvent);

      const messageForFlexibleTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const flexibleTenantProvisioning = new TenantProvisioning(context, messageForFlexibleTenant);
      await flexibleTenantProvisioning.updateTenant({} as MessageEvent);

      sandbox.assert.called(updateHanaStub);
      sandbox.assert.notCalled(createOrUpdateDataLakeStub);
    });

    it("should throw if thresholdCompute is given a value in update", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdStorage: "256",
          thresholdMemory: "64",
          thresholdVCPU: "4",
          thresholdCompute: "2",
        })
        .build();

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdStorage: "128",
          thresholdMemory: "32",
          thresholdVCPU: "2",
          thresholdCompute: "0",
        })
        .build();

      const messageForFlexibleTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const flexibleTenantProvisioning = new TenantProvisioning(context, messageForFlexibleTenant);

      await expect(flexibleTenantProvisioning.updateTenant({} as MessageEvent)).to.eventually.be.rejectedWith(
        "thresholdCompute license is not valid after commercial reset."
      );

      sandbox.assert.notCalled(updateHanaStub);
    });

    it("should only call Data Lake upsize functions when ff is off", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "2",
          thresholdDataLakeCompute: "8",
        })
        .build();

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "1",
          thresholdDataLakeCompute: "4",
        })
        .build();

      const messageForStandardTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const standardTenantProvisioning = new TenantProvisioning(context, messageForStandardTenant);
      await standardTenantProvisioning.updateTenant({} as MessageEvent);

      const messageForFlexibleTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const flexibleTenantProvisioning = new TenantProvisioning(context, messageForFlexibleTenant);
      await flexibleTenantProvisioning.updateTenant({} as MessageEvent);

      sandbox.assert.called(createOrUpdateDataLakeStub);
      sandbox.assert.notCalled(updateHanaStub);
    });
    it("should call both HANA and Data Lake Deletion Function when FF is true and deleteDataLake extraparameter is true", async function () {
      const extraParams = new ExtraParamsHandler({
        dwc: {
          tenantLifeCycle: {
            deleteDataLake: true,
          },
        },
      });

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "1",
          thresholdDataLakeCompute: "4",
        })
        .build();

      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "0",
          thresholdDataLakeCompute: "0",
        })
        .build();

      isDataLakeConnectedToSpaceStub.returns(false);

      const messageForStandardTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const standardTenantProvisioning = new TenantProvisioning(context, messageForStandardTenant, extraParams);
      await standardTenantProvisioning.updateTenant({} as MessageEvent);

      const messageForFlexibleTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const flexibleTenantProvisioning = new TenantProvisioning(context, messageForFlexibleTenant, extraParams);
      await flexibleTenantProvisioning.updateTenant({} as MessageEvent);

      sandbox.assert.notCalled(createOrUpdateDataLakeStub);
      sandbox.assert.called(deleteDataLakeForFlexibleTenantStub);
    });
    it("should call only Data Lake Deletion Function when FF is true, deleteDataLake extraparameter is true and it's flexible tenant", async function () {
      const extraParams = new ExtraParamsHandler({
        dwc: {
          tenantLifeCycle: {
            deleteDataLake: true,
          },
        },
      });

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "1",
          thresholdDataLakeCompute: "4",
        })
        .build();

      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "0",
          thresholdDataLakeCompute: "0",
        })
        .build();

      isDataLakeConnectedToSpaceStub.returns(false);

      const messageForFlexibleTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const flexibleTenantProvisioning = new TenantProvisioning(context, messageForFlexibleTenant, extraParams);
      await flexibleTenantProvisioning.updateTenant({} as MessageEvent);

      sandbox.assert.notCalled(createOrUpdateDataLakeStub);
      sandbox.assert.called(deleteDataLakeForFlexibleTenantStub);
    });

    it("should throw error during data lake delete if data lake is connect to a space", async function () {
      let errorMessage;
      const extraParams = new ExtraParamsHandler({
        dwc: {
          tenantLifeCycle: {
            deleteDataLake: true,
          },
        },
      });

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "1",
          thresholdDataLakeCompute: "4",
        })
        .build();

      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "0",
          thresholdDataLakeCompute: "0",
        })
        .build();

      isDataLakeConnectedToSpaceStub.returns(true);

      const messageForFlexibleTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const flexibleTenantProvisioning = new TenantProvisioning(context, messageForFlexibleTenant, extraParams);

      try {
        await flexibleTenantProvisioning.updateTenant({} as MessageEvent);
      } catch (error) {
        errorMessage = error.message;
      } finally {
        sandbox.assert.match(
          errorMessage,
          "Couldn't delete Data Lake instance because Data Lake is being consumed by a Datasphere Space"
        );
      }
    });

    it("should throw error during data lake delete if data lake deletion extraParam is undefined", async function () {
      let errorMessage;

      const oldTenantMetadata = new TenantMetadataBuilder()
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "1",
          thresholdDataLakeCompute: "4",
        })
        .build();

      const tenantMetadata = new TenantMetadataBuilder().withFlexibleTenantConfiguration().build();

      isDataLakeConnectedToSpaceStub.returns(false);

      const messageForFlexibleTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const flexibleTenantProvisioning = new TenantProvisioning(context, messageForFlexibleTenant);

      try {
        await flexibleTenantProvisioning.updateTenant({} as MessageEvent);
      } catch (error) {
        errorMessage = error.message;
      } finally {
        sandbox.assert.match(
          errorMessage,
          "Couldn't delete Data Lake instance because the payload ExtraParam deleteDataLake: true is missing"
        );
      }
    });

    it("should throw error during data lake delete if data lake deletion extraParam is false", async function () {
      let errorMessage;

      const extraParams = new ExtraParamsHandler({
        dwc: {
          tenantLifeCycle: {
            deleteDataLake: false,
          },
        },
      });

      const oldTenantMetadata = new TenantMetadataBuilder()
        .withFlexibleTenantConfiguration()
        .withMinimumSizeTenantLicense()
        .withTenantLicense({
          thresholdDataLakeStorage: "1",
          thresholdDataLakeCompute: "4",
        })
        .build();

      const tenantMetadata = new TenantMetadataBuilder()
        .withMinimumSizeTenantLicense()
        .withFlexibleTenantConfiguration()
        .build();

      isDataLakeConnectedToSpaceStub.returns(false);

      const messageForFlexibleTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const flexibleTenantProvisioning = new TenantProvisioning(context, messageForFlexibleTenant, extraParams);

      try {
        await flexibleTenantProvisioning.updateTenant({} as MessageEvent);
      } catch (error) {
        errorMessage = error.message;
      } finally {
        sandbox.assert.match(
          errorMessage,
          "Couldn't delete Data Lake instance because the payload ExtraParam deleteDataLake: true is missing"
        );
      }
    });

    it("should call only Data Lake create Function when FF is true, dataLake storage and dataLake compute is not zero", async function () {
      const extraParams = new ExtraParamsHandler({
        dwc: {
          tenantLifeCycle: {
            deleteDataLake: false,
          },
        },
      });

      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "2",
          thresholdDataLakeCompute: "4",
        })
        .build();

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "0",
          thresholdDataLakeCompute: "0",
        })
        .build();

      const messageForFlexibleTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const flexibleTenantProvisioning = new TenantProvisioning(context, messageForFlexibleTenant, extraParams);
      await flexibleTenantProvisioning.updateTenant({} as MessageEvent);

      sandbox.assert.notCalled(deleteDataLakeForFlexibleTenantStub);
      sandbox.assert.called(createOrUpdateDataLakeStub);
    });

    it("should call only Data Lake create Function when FF is true, dataLake storage is not zero and dataLake compute is zero", async function () {
      const extraParams = new ExtraParamsHandler({
        dwc: {
          tenantLifeCycle: {
            deleteDataLake: false,
          },
        },
      });

      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "2",
          thresholdDataLakeCompute: "0",
        })
        .build();

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "1",
          thresholdDataLakeCompute: "4",
        })
        .build();

      const messageForFlexibleTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const flexibleTenantProvisioning = new TenantProvisioning(context, messageForFlexibleTenant, extraParams);
      await flexibleTenantProvisioning.updateTenant({} as MessageEvent);

      sandbox.assert.notCalled(deleteDataLakeForFlexibleTenantStub);
      sandbox.assert.called(createOrUpdateDataLakeStub);
    });

    it("should call only Data Lake create Function when FF is true, dataLake storage is zero and dataLake compute is not zero", async function () {
      const extraParams = new ExtraParamsHandler({
        dwc: {
          tenantLifeCycle: {
            deleteDataLake: false,
          },
        },
      });

      const tenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "0",
          thresholdDataLakeCompute: "8",
        })
        .build();

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withFlexibleTenantConfiguration()
        .withTenantLicense({
          thresholdDataLakeStorage: "1",
          thresholdDataLakeCompute: "4",
        })
        .build();

      const messageForFlexibleTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );
      const flexibleTenantProvisioning = new TenantProvisioning(context, messageForFlexibleTenant, extraParams);
      await flexibleTenantProvisioning.updateTenant({} as MessageEvent);

      sandbox.assert.notCalled(deleteDataLakeForFlexibleTenantStub);
      sandbox.assert.called(createOrUpdateDataLakeStub);
    });

    it("should not call function hasBridgeInstanceInBtp when BW Bridge License doesn't change", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantLicense({
          thresholdBWBridge1: "256",
        })
        .build();

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withTenantLicense({
          thresholdBWBridge1: "256",
        })
        .build();

      const messageForStandardTenant = makeProvisioningMessage(
        ProvisioningActions.UPDATE,
        tenantMetadata,
        oldTenantMetadata
      );

      const standardTenantProvisioning = new TenantProvisioning(context, messageForStandardTenant);
      await standardTenantProvisioning.updateTenant({} as MessageEvent);

      sandbox.assert.notCalled(hasBridgeInstanceStub);
    });

    it("Should return error message when license change and there is bridge instance and ff DWCO_FTC_UPSIZING_BW_BRIDGE is off", async function () {
      let errorMessage;

      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantLicense({
          thresholdBWBridge1: "512",
        })
        .build();

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withTenantLicense({
          thresholdBWBridge1: "256",
        })
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, oldTenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      isBwBridgeUpsizingEnabledStub.resolves(false);
      hasBridgeInstanceStub.returns(true);

      try {
        await tenantProvisioning.updateTenant({} as MessageEvent);
      } catch (error) {
        errorMessage = error.message;
      } finally {
        sandbox.assert.match(
          errorMessage,
          "BW Bridge license can only be changed after deleting the existing instances of BW Bridge linked to this tenant."
        );
      }
    });

    it("Should not return error message when the new license is smaller and there is not bridge instance and ff DWCO_FTC_UPSIZING_BW_BRIDGE is on", async function () {
      let error;

      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantLicense({
          thresholdBWBridge1: "256",
        })
        .build();

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withTenantLicense({
          thresholdBWBridge1: "512",
        })
        .build();

      hasBridgeInstanceStub.resolves(false);

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, oldTenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      isBwBridgeUpsizingEnabledStub.resolves(false);

      try {
        await tenantProvisioning.updateTenant({} as MessageEvent);
      } catch (e) {
        error = e;
      } finally {
        sandbox.assert.match(error, undefined);
      }
    });

    it("Should call updateAbapBrigdeInstance when new BW Bridge license is bigger than old and there is instance one and ff DWCO_FTC_UPSIZING_BW_BRIDGE is on ", async function () {
      isBwBridgeUpsizingEnabledStub.resolves(true);

      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantLicense({
          thresholdBWBridge1: "512",
        })
        .build();

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withTenantLicense({
          thresholdBWBridge1: "256",
        })
        .build();

      hasBridgeInstanceStub.resolves(truncate);

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, oldTenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      await tenantProvisioning.updateTenant({} as MessageEvent);

      hasBridgeInstanceStub.returns(true);

      sandbox.assert.called(updateAbapBrigdeInstanceStub);
    });

    it("Should npt call updateAbapBrigdeInstance when new BW Bridge license is bigger than old one and there is not instance and ff DWCO_FTC_UPSIZING_BW_BRIDGE is on ", async function () {
      isBwBridgeUpsizingEnabledStub.resolves(true);

      const tenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantLicense({ thresholdBWBridge1: "512" })
        .build();

      const oldTenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantLicense({ thresholdBWBridge1: "256" })
        .build();

      hasBridgeInstanceStub.resolves(false);

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, oldTenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      await tenantProvisioning.updateTenant({} as MessageEvent);

      hasBridgeInstanceStub.returns(true);

      sandbox.assert.notCalled(updateAbapBrigdeInstanceStub);
    });

    it("should provision admin api access im upgrade if the extra params flag is set.", async function () {
      const tenantMetadata = makeTenantMetadataBuilder.build();

      const extraParams = new ExtraParamsHandler({
        dwc: {
          tenantLifeCycle: {
            provisionAdminApiAccess: true,
          },
        },
      });

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message, extraParams);
      await tenantProvisioning.updateTenant({} as MessageEvent);

      sandbox.assert.called(adminApiAccessStub);
    });

    it("Should send IN PROGRESS message to solace containing the values of changed licenses", async function () {
      const tenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().build();
      const oldTenantMetadata = makeTenantMetadataBuilder
        .withTenantLicense({ thresholdStorage: "0", thresholdMemory: "0", thresholdVCPU: "0" })
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, oldTenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      await tenantProvisioning.updateTenant({} as MessageEvent);

      sendInProgressMessageStub.resolves();

      const expected = [
        { thresholdStorage: { old: "0", new: "128" } },
        { thresholdMemory: { old: "0", new: "32" } },
        { thresholdVCPU: { old: "0", new: "2" } },
      ];
      sandbox.assert.calledWith(sendInProgressMessageStub, JSON.stringify(expected));
    });

    it("Should not fail tenant update if licenses Solace licenses log fails", async function () {
      const tenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().build();
      const oldTenantMetadata = makeTenantMetadataBuilder.build();

      const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, oldTenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      sendInProgressMessageStub.throws();
      expect(await tenantProvisioning.updateTenant({} as MessageEvent)).to.not.throw;
    });
  });

  describe("Exceptional scenarios", function () {
    beforeEach(() => {
      makeTenantMetadataBuilder = new TenantMetadataBuilder();
    });

    describe("General", function () {
      after(function () {
        sandbox.restore();
        sinon.restore();
      });

      it("standard - should try to update HANA but fail due to invalid license format", async function () {
        const tenantMetadata = makeTenantMetadataBuilder
          .withTenantLicense({
            thresholdCompute: "4",
          })
          .build();

        const oldTenantMetadata = makeTenantMetadataBuilder
          .withTenantLicense({
            thresholdCompute: "2",
          })
          .build();

        const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, oldTenantMetadata);
        const tenantProvisioning = new TenantProvisioning(context, message);
        const updateTenant = tenantProvisioning.updateTenant({} as MessageEvent);
        await chai.assert.isRejected(updateTenant, "thresholdCompute license is not valid after commercial reset.");
      });

      it("DPS - Should skip when xsn version changes", async function () {
        const tenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().withXsnVersion("1").build();
        const oldTenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().withXsnVersion("2").build();

        const message = makeProvisioningMessage(ProvisioningActions.UPDATE, tenantMetadata, oldTenantMetadata);
        const tenantProvisioning = new TenantProvisioning(context, message);
        const skipMessage = `XSN version change has been detected. Skipped provisioning.`;

        await tenantProvisioning.updateTenant({} as MessageEvent);
        sandbox.assert.calledWith(sendSkipMessageStub, skipMessage);
        sandbox.assert.notCalled(deprovisioningOnUpdateStub);
        sandbox.assert.notCalled(saveResourcesStub);
        sandbox.assert.notCalled(updateHanaStub);
        sandbox.assert.notCalled(createOrUpdateDataLakeStub);
      });
    });

    describe("Enable HANA monitoring view", function () {
      let createStandardHanaInstanceStub: SinonStub;

      beforeEach(async function () {
        sandbox.restore();
        createTestStubs();
      });

      after(async function () {
        sandbox.restore();
      });

      const step: ProvStep = {
        statement:
          "ALTER SYSTEM ALTER CONFIGURATION ('indexserver.ini','SYSTEM') SET ('sql', 'table_statistics_select_enabled') = 'true' WITH RECONFIGURE",
        ignoreError: true,
        log: "Setting table_statistics_select_enabled value in indexserver.ini to true",
        precondition: {
          statement: `SELECT COUNT(*) FROM PUBLIC.M_INIFILE_CONTENTS WHERE SECTION= ? AND KEY= ? AND VALUE= ? AND FILE_NAME='indexserver.ini' AND LAYER_NAME='SYSTEM'`,
          params: ["sql", "table_statistics_select_enabled", "true"],
          preconditionValue: false,
          log: "Verifyng table_statistics_select_enabled value in indexserver.ini",
        },
      };

      function createProvStepTestStubs() {
        sandbox.stub(adminActivator, "activateAdminUser").resolves();
        sandbox.stub(adminActivator, "deactivateAdminUser").resolves();
        sandbox.stub(DbConnections, "createDbConnection").resolves({
          close() {
            return Promise.resolve();
          },
        } as DbClient);
        sendInProgressMessageStub.resolves();
        sandbox.stub(RepositoryOnboardingClient, "handleOnboarding" as any).resolves();
        sandbox.stub(ElasticCustomerHanaProvisioning, "createOrUpdateDataLake").resolves();
        createStandardHanaInstanceStub = sandbox.stub(ElasticCustomerHanaProvisioning, "createStandardHanaInstance");
        createStandardHanaInstanceStub.resolves({} as IDbConfig);
      }

      async function createTenantTest(extraParams?: ExtraParamsHandler) {
        const tenantMetadata = makeTenantMetadataBuilder
          .withMinimumSizeTenantLicense()
          .withFlexibleTenantConfiguration()
          .build();
        const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
        const tenantProvisioning = new TenantProvisioning(context, message, extraParams);
        await tenantProvisioning.createTenant();
      }

      it("Should call 'execStep' with prov step with ff enabled from provider", async function () {
        await ffStub.resolves({ DWCO_LS_M_TABLE_STATISTICS_SELECT: true });

        createProvStepTestStubs();

        const execStepStub = sandbox.stub(DbConnections, "execStep");
        execStepStub.resolves();

        await createTenantTest();

        sandbox.assert.calledWith(execStepStub, sinon.match.any, sinon.match.any, "dbAdmin", step);
      });

      it("Should call 'execStep' with prov step with ff enabled from extra params", async function () {
        await ffStub.resolves({ DWCO_LS_M_TABLE_STATISTICS_SELECT: false });

        const extraParams = new ExtraParamsHandler({
          dwc: {
            featureFlag: {
              DWCO_LS_M_TABLE_STATISTICS_SELECT: true,
            },
          },
        });

        createProvStepTestStubs();

        const execStepStub = sandbox.stub(DbConnections, "execStep");
        execStepStub.resolves();

        await createTenantTest(extraParams);

        sandbox.assert.calledWith(execStepStub, sinon.match.any, sinon.match.any, "dbAdmin", step);
      });

      it("Shouldn't call 'execStep' with prov step with ff disabled", async function () {
        await ffStub.resolves({ DWCO_LS_M_TABLE_STATISTICS_SELECT: false });

        const extraParams = new ExtraParamsHandler({
          dwc: {
            featureFlag: {
              DWCO_LS_M_TABLE_STATISTICS_SELECT: false,
            },
          },
        });

        createProvStepTestStubs();

        const execStepStub = sandbox.stub(DbConnections, "execStep");
        execStepStub.resolves();

        await createTenantTest(extraParams);

        sandbox.assert.notCalled(execStepStub);
      });

      it("Should fails when there is no customer Hana connection", async function () {
        await ffStub.resolves({});

        const extraParams = new ExtraParamsHandler({
          dwc: {
            featureFlag: {
              DWCO_LS_M_TABLE_STATISTICS_SELECT: true,
            },
          },
        });

        createProvStepTestStubs();

        createStandardHanaInstanceStub.restore();
        createStandardHanaInstanceStub = sandbox.stub(ElasticCustomerHanaProvisioning, "createStandardHanaInstance");
        createStandardHanaInstanceStub.resolves(undefined);

        const execStepStub = sandbox.stub(DbConnections, "execStep");

        await createTenantTest(extraParams);

        sandbox.assert.notCalled(execStepStub);
      });
    });
  });
});
