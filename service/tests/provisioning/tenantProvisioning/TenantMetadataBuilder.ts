/** @format */

import { ITenantInformation, ITenantLicenses, TenantClassification, TenantStatus } from "@sap/dwc-tms-provider";
import { TenantType } from "../../../../shared/provisioning/ftc/types";

export class TenantMetadataBuilder {
  private metadata: Partial<ITenantInformation>;

  constructor() {
    this.metadata = {
      uuid: "uuid-tenant",
      tenantId: "tenantId",
      status: TenantStatus.ACTIVE,
      classification: "DWC",
      type: "DWC",
      freeTier: false,
      xsnVersion: "1",
      license: {
        thresholdDataWarehouseCloudUser: "0",
        thresholdBusinessDataCloudUser: "0",
        thresholdDWCCU: "0",
        thresholdStorage: "0",
        thresholdMemory: "0",
        thresholdVCPU: "0",
        thresholdCompute: "0",
        thresholdRmsNodeHours: "0",
        thresholdDataLakeStorage: "0",
        thresholdDataLakeCompute: "0",
        thresholdBWBridge1: "0",
        thresholdRmsConcurrency: "0",
        thresholdCatalogNodeHours: "0",
        thresholdCatalogConcurrency: "0",
        thresholdCatalogStorage: "0",
        thresholdRmsPremiumOutbound: "0",
        thresholdECNBlock: "0",
        thresholdECNPerformanceClass: "Memory",
        thresholdLargeSystemsCompute: "0",
        thresholdLargeSystemsStorage: "0",
      },
    };
  }

  withMinimumSizeTenantLicense() {
    this.metadata.license = {
      ...this.metadata.license,
      thresholdStorage: "128",
      thresholdMemory: "32",
      thresholdVCPU: "2",
    };

    return this;
  }

  withFlexibleTenantConfiguration() {
    this.metadata.license = {
      ...this.metadata.license,
      thresholdDWCCU: "1000",
    };

    return this;
  }

  withXsnVersion(xsnVersion: string) {
    this.metadata.xsnVersion = xsnVersion;

    return this;
  }

  withTenantLicense(license: Partial<ITenantLicenses>) {
    this.metadata.license = {
      ...this.metadata.license,
      ...license,
    };

    return this;
  }

  withTenantUuid(uuid: string) {
    this.metadata.uuid = uuid;

    return this;
  }

  withTenantId(tenantId: string) {
    this.metadata.tenantId = tenantId;

    return this;
  }

  withTenantStatus(tenantStatus: TenantStatus) {
    this.metadata.status = tenantStatus;

    return this;
  }

  withTenantType(tenantType: TenantType) {
    this.metadata.type = tenantType;

    return this;
  }

  withTenantFreeTier(freeTier: boolean) {
    this.metadata.freeTier = freeTier;

    return this;
  }

  withTenantClassification(tenantClassification: TenantClassification) {
    this.metadata.classification = tenantClassification;

    return this;
  }

  build() {
    return JSON.parse(JSON.stringify(this.metadata));
  }
}
