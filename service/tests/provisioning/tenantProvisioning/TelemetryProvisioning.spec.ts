/** @format */

import { HistogramInstrument } from "@opentelemetry/sdk-metrics/build/src/Instruments";
import { httpClient } from "@sap/dwc-http-client";
import chai, { expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import sinon, { SinonSandbox, SinonStub } from "sinon";
import sinonChai from "sinon-chai";
import { OperationResult, TenantClassification } from "../../../../shared/provisioning/ftc/types";
import { DiEPodCount } from "../../../diEmbedded/podCount/DiEPodCount";
import { FeatureFlagProvider } from "../../../featureflags/FeatureFlagProvider";
import * as node from "../../../lib/node";
import { testTenantUuid } from "../../../lib/node";
import * as replyHelper from "../../../messageQueuing/QueueMigrationUtils";
import { ITenantProvisioningMessage } from "../../../messageQueuing/consumer/TenantProvisioningConsumer";
import { TenantProvisioning } from "../../../provisioning/TenantProvisioning";
import { ExtraParamsHandler } from "../../../provisioning/extraParameters/ExtraParamsHandler";
import { FtcTelemetry } from "../../../provisioning/flexible/telemetry/ftcTelemetry";
import { FtcTelemetryGetter } from "../../../provisioning/flexible/telemetry/ftcTelemetryGetter";
import { FTC_TELEMETRY_METRICS } from "../../../provisioning/flexible/telemetry/metricsDefinition";
import { ProvisioningActions } from "../../../provisioning/types";
import { RequestContext } from "../../../repository/security/requestContext";
import { CustomerHana } from "../../../reuseComponents/spaces/src/CustomerHana";
import * as logLevel from "../../../routes/support/logging/setMinimumLogLevel";
import { TenantMetadataBuilder } from "./TenantMetadataBuilder";

chai.use(sinonChai);
chai.use(chaiAsPromised);

describe("service/provisioning/TenantProvisioning", function () {
  const context = RequestContext.createFromTenantId(testTenantUuid);
  let sandbox: SinonSandbox;

  let ffStub: any;
  let repositoryActivitiesStub: SinonStub;
  let createHanaStub: SinonStub;
  let configureHanaSystemParams: SinonStub;
  let createNativeTenantForLightweightTenant: SinonStub;
  let provisionLightweightDSPArtifacts: SinonStub;
  let createOrUpdateDataLakeStub: SinonStub;
  let provisionCustomerHanaArtifactsStub: SinonStub;
  let storeExternalHanaCredentialsStub: SinonStub;
  let isSdpConversionActiveStub: SinonStub;
  let setSdpConversionStatusAsSuccessfulStub: SinonStub;
  let isStarkiller: SinonStub;
  let adminApiAccessStub: SinonStub;
  let getHistogramStub: sinon.SinonStub;
  let createTenantHistogramStub: sinon.SinonStubbedInstance<HistogramInstrument>;
  let makeTenantMetadataBuilder: TenantMetadataBuilder;

  function createTestStubs() {
    sandbox = sinon.createSandbox();
    sandbox.stub(logLevel, "setMinimumLogLevel").returns(Promise.resolve());
    sandbox.stub(replyHelper, "reply").returns(Promise.resolve());
    sandbox.stub(httpClient, "call").resolves();

    sandbox.stub(CustomerHana, "fromTenantId").resolves(sandbox.createStubInstance(CustomerHana));

    repositoryActivitiesStub = sandbox.stub(TenantProvisioning.prototype, "executeRepositoryActivities" as any);
    createOrUpdateDataLakeStub = sandbox.stub(TenantProvisioning.prototype, "createOrUpdateDataLake" as any);

    provisionCustomerHanaArtifactsStub = sandbox.stub(
      TenantProvisioning.prototype,
      "provisionCustomerHanaArtifacts" as any
    );
    isSdpConversionActiveStub = sandbox
      .stub(TenantProvisioning.prototype, "isSdpConversionActive" as any)
      .resolves(true);
    setSdpConversionStatusAsSuccessfulStub = sandbox.stub(
      TenantProvisioning.prototype,
      "setSdpConversionStatusAsSuccessful" as any
    );

    isStarkiller = sandbox.stub(node, "isStarkiller");
    ffStub = sandbox.stub(FeatureFlagProvider, "getFeatureFlags");

    sandbox.useFakeTimers({
      now: new Date(2024, 9, 1, 0, 0),
      shouldAdvanceTime: false,
      toFake: ["Date"],
    });

    getHistogramStub = sandbox.stub(FtcTelemetryGetter.prototype, "getHistogram");
    createTenantHistogramStub = sandbox.createStubInstance(HistogramInstrument);
    adminApiAccessStub = sandbox.stub(TenantProvisioning.prototype, "provisionAdminApiAccess" as any);
    createHanaStub = sandbox.stub(TenantProvisioning.prototype, "createHana" as any);

    configureHanaSystemParams = sandbox.stub(TenantProvisioning.prototype, "configureHanaSystemParams" as any);
    createNativeTenantForLightweightTenant = sandbox.stub(
      TenantProvisioning.prototype,
      "createNativeTenantForLightweightTenant" as any
    );
    provisionLightweightDSPArtifacts = sandbox.stub(
      TenantProvisioning.prototype,
      "provisionLightweightDSPArtifacts" as any
    );
    storeExternalHanaCredentialsStub = sandbox.stub(
      TenantProvisioning.prototype,
      "storeExternalHanaCredentials" as any
    );
    sandbox.stub(DiEPodCount.prototype, "updateRMSPodCount").resolves();
  }

  function makeProvisioningMessage(
    action: ProvisioningActions,
    tenantMetadata: any,
    oldTenantMetadata?: any
  ): ITenantProvisioningMessage {
    return {
      context,
      tenantUuid: testTenantUuid,
      collaborators: ["dwaas-core"],
      dataAny: {
        action,
        tenantMetadata,
        oldTenantMetadata,
      },
    };
  }

  beforeEach(() => {
    createTestStubs();
    makeTenantMetadataBuilder = new TenantMetadataBuilder();
    isStarkiller.resolves(false);
    ffStub.resolves({});
  });

  afterEach(function () {
    sandbox.restore();
  });

  describe("Tenant Details to Dynatrace ", () => {
    it("Should send tenant details to Dynatrace when tenant creation was successful", async () => {
      const tenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantClassification(TenantClassification.DWCCPEA)
        .build();

      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      getHistogramStub
        .withArgs(FTC_TELEMETRY_METRICS.FTC_WORKFLOW_PROVISIONING_CREATION)
        .returns(createTenantHistogramStub);

      await tenantProvisioning.createTenant();

      const expectValue = {
        tenantId: tenantMetadata.uuid,
        tenantClassification: tenantMetadata.classification,
        requestOrigin: "CIS",
        requestStatus: OperationResult.SUCCESS,
        error: "",
      };

      expect(createTenantHistogramStub.record).to.have.been.calledWith(0, expectValue);
    });

    it("Should send tenant details to Dynatrace when failed to executing Repository HANA operations", async () => {
      const tenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().build();

      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      getHistogramStub
        .withArgs(FTC_TELEMETRY_METRICS.FTC_WORKFLOW_PROVISIONING_CREATION)
        .returns(createTenantHistogramStub);

      repositoryActivitiesStub.throws(new Error("Error"));

      await expect(tenantProvisioning.createTenant()).to.be.rejected;

      const expectValue = {
        tenantId: tenantMetadata.uuid,
        tenantClassification: tenantMetadata.classification,
        requestOrigin: "SPC",
        requestStatus: OperationResult.FAILED,
        error: "Failed to executing Repository HANA onboarding operations",
      };

      expect(createTenantHistogramStub.record).to.have.been.calledWith(0, expectValue);
    });

    it("Should send tenant details to Dynatrace when failed to store External Hana Credentials when bring your own HANA", async () => {
      const tenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().build();

      const extraParams = new ExtraParamsHandler({
        dwc: {
          bringYourOwnHana: {
            instanceName: "db1",
            host: "mydb.hanacloud.ondemand.com",
            port: "443",
            password: "Initial1!",
            user: "TA_USER",
            certificate: "",
          },
        },
      });

      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message, extraParams);

      getHistogramStub
        .withArgs(FTC_TELEMETRY_METRICS.FTC_WORKFLOW_PROVISIONING_CREATION)
        .returns(createTenantHistogramStub);

      storeExternalHanaCredentialsStub.throws(new Error("Error"));

      await expect(tenantProvisioning.createTenant()).to.be.rejected;

      const expectValue = {
        tenantId: tenantMetadata.uuid,
        tenantClassification: tenantMetadata.classification,
        requestOrigin: "SPC",
        requestStatus: OperationResult.FAILED,
        error: "Failed to store External Hana Credentials",
      };

      expect(createTenantHistogramStub.record).to.have.been.calledWith(0, expectValue);
    });

    it("Should send tenant details to Dynatrace when failed to execute Native Tenant instance provisioning", async () => {
      const tenantMetadata = makeTenantMetadataBuilder.build();

      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      getHistogramStub
        .withArgs(FTC_TELEMETRY_METRICS.FTC_WORKFLOW_PROVISIONING_CREATION)
        .returns(createTenantHistogramStub);

      isStarkiller.resolves(true);

      createNativeTenantForLightweightTenant.throws(new Error("Error"));

      await expect(tenantProvisioning.createTenant()).to.be.rejected;

      const expectValue = {
        tenantId: tenantMetadata.uuid,
        tenantClassification: tenantMetadata.classification,
        requestOrigin: "SPC",
        requestStatus: OperationResult.FAILED,
        error: "Failed to create Native Tenant for LightweightTenant",
      };

      expect(createTenantHistogramStub.record).to.have.been.calledWith(0, expectValue);
    });

    it("Should send tenant details to Dynatrace when failed to configure HANA params", async () => {
      const tenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().build();

      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      getHistogramStub
        .withArgs(FTC_TELEMETRY_METRICS.FTC_WORKFLOW_PROVISIONING_CREATION)
        .returns(createTenantHistogramStub);

      configureHanaSystemParams.throws(new Error("Error"));

      await expect(tenantProvisioning.createTenant()).to.be.rejected;

      const expectValue = {
        tenantId: tenantMetadata.uuid,
        tenantClassification: tenantMetadata.classification,
        requestOrigin: "SPC",
        requestStatus: OperationResult.FAILED,
        error: "Failed to configure HANA params",
      };

      expect(createTenantHistogramStub.record).to.have.been.calledWith(0, expectValue);
    });

    it("Should send tenant details to Dynatrace when failed to create HANA", async () => {
      const tenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().build();

      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      getHistogramStub
        .withArgs(FTC_TELEMETRY_METRICS.FTC_WORKFLOW_PROVISIONING_CREATION)
        .returns(createTenantHistogramStub);

      createHanaStub.throws(new Error("Error"));

      await expect(tenantProvisioning.createTenant()).to.be.rejected;

      const expectValue = {
        tenantId: tenantMetadata.uuid,
        tenantClassification: tenantMetadata.classification,
        requestOrigin: "SPC",
        requestStatus: OperationResult.FAILED,
        error: "Failed to create HANA",
      };

      expect(createTenantHistogramStub.record).to.have.been.calledWith(0, expectValue);
    });

    it("Should send tenant details to Dynatrace when failed to provisioning LightweightDSPArtifacts", async () => {
      const tenantMetadata = makeTenantMetadataBuilder.build();

      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      getHistogramStub
        .withArgs(FTC_TELEMETRY_METRICS.FTC_WORKFLOW_PROVISIONING_CREATION)
        .returns(createTenantHistogramStub);

      isStarkiller.resolves(true);

      provisionLightweightDSPArtifacts.throws(new Error("Error"));

      await expect(tenantProvisioning.createTenant()).to.be.rejected;

      const expectValue = {
        tenantId: tenantMetadata.uuid,
        tenantClassification: tenantMetadata.classification,
        requestOrigin: "SPC",
        requestStatus: OperationResult.FAILED,
        error: "Failed to provision LightweightDSP Artifacts",
      };

      expect(createTenantHistogramStub.record).to.have.been.calledWith(0, expectValue);
    });

    it("Should send tenant details to Dynatrace when failed to provision Customer HANA Artifacts", async () => {
      const tenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().build();

      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      getHistogramStub
        .withArgs(FTC_TELEMETRY_METRICS.FTC_WORKFLOW_PROVISIONING_CREATION)
        .returns(createTenantHistogramStub);

      provisionCustomerHanaArtifactsStub.throws(new Error("Error"));

      await expect(tenantProvisioning.createTenant()).to.be.rejected;

      const expectValue = {
        tenantId: tenantMetadata.uuid,
        tenantClassification: tenantMetadata.classification,
        requestOrigin: "SPC",
        requestStatus: OperationResult.FAILED,
        error: "Failed to provision customer HANA Artifacts",
      };

      expect(createTenantHistogramStub.record).to.have.been.calledWith(0, expectValue);
    });

    it("Should send tenant details to Dynatrace when failed to create or update DataLake", async () => {
      const tenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().build();

      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      getHistogramStub
        .withArgs(FTC_TELEMETRY_METRICS.FTC_WORKFLOW_PROVISIONING_CREATION)
        .returns(createTenantHistogramStub);

      createOrUpdateDataLakeStub.throws(new Error("Error"));

      await expect(tenantProvisioning.createTenant()).to.be.rejected;

      const expectValue = {
        tenantId: tenantMetadata.uuid,
        tenantClassification: tenantMetadata.classification,
        requestOrigin: "SPC",
        requestStatus: OperationResult.FAILED,
        error: "Failed to create or update DataLake",
      };

      expect(createTenantHistogramStub.record).to.have.been.calledWith(0, expectValue);
    });

    it("Should send tenant details to Dynatrace when failed to verify if SDP conversion is active", async () => {
      const tenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().build();

      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      getHistogramStub
        .withArgs(FTC_TELEMETRY_METRICS.FTC_WORKFLOW_PROVISIONING_CREATION)
        .returns(createTenantHistogramStub);

      isSdpConversionActiveStub.throws(new Error("Error"));

      await expect(tenantProvisioning.createTenant()).to.be.rejected;

      const expectValue = {
        tenantId: tenantMetadata.uuid,
        tenantClassification: tenantMetadata.classification,
        requestOrigin: "SPC",
        requestStatus: OperationResult.FAILED,
        error: "Failed to verify if SDP conversion is active",
      };

      expect(createTenantHistogramStub.record).to.have.been.calledWith(0, expectValue);
    });

    it("Should send tenant details to Dynatrace when failed to set SDP conversion status to successful", async () => {
      const tenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().build();

      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      getHistogramStub
        .withArgs(FTC_TELEMETRY_METRICS.FTC_WORKFLOW_PROVISIONING_CREATION)
        .returns(createTenantHistogramStub);

      setSdpConversionStatusAsSuccessfulStub.throws(new Error("Error"));

      await expect(tenantProvisioning.createTenant()).to.be.rejected;

      const expectValue = {
        tenantId: tenantMetadata.uuid,
        tenantClassification: tenantMetadata.classification,
        requestOrigin: "SPC",
        requestStatus: OperationResult.FAILED,
        error: "Failed to set SDP conversion status as successful",
      };

      expect(createTenantHistogramStub.record).to.have.been.calledWith(0, expectValue);
    });

    it("Should send tenant details to Dynatrace when failed to provision Admin Api Access", async () => {
      const tenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().build();

      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);

      getHistogramStub
        .withArgs(FTC_TELEMETRY_METRICS.FTC_WORKFLOW_PROVISIONING_CREATION)
        .returns(createTenantHistogramStub);

      adminApiAccessStub.throws(new Error("Error"));

      await expect(tenantProvisioning.createTenant()).to.be.rejected;

      const expectValue = {
        tenantId: tenantMetadata.uuid,
        tenantClassification: tenantMetadata.classification,
        requestOrigin: "SPC",
        requestStatus: OperationResult.FAILED,
        error: "Failed to provision Admin Api Access",
      };

      expect(createTenantHistogramStub.record).to.have.been.calledWith(0, expectValue);
    });

    it("Should not send tenant details to Dynatrace when feature flag was disabled", async () => {
      const tenantMetadata = makeTenantMetadataBuilder.withMinimumSizeTenantLicense().build();

      const recordTenantCreationStub = sandbox.stub(FtcTelemetry.prototype, "recordTenantCreation");

      await ffStub.resolves({ DWCO_MONITORING_PROVISIONING: false });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      await tenantProvisioning.createTenant();

      expect(recordTenantCreationStub.notCalled).to.be.true;
    });
  });
});
