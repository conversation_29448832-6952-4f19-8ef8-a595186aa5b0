/** @format */

import { sleep } from "@sap/deepsea-utils";
import { TenantClassification } from "@sap/dwc-tms-provider";
import { MessageEvent } from "@sap/orca-starter-solace";
import chai, { expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import sinon, { SinonStub } from "sinon";
import sinonChai from "sinon-chai";
import { FeatureFlagProvider } from "../../../featureflags/FeatureFlagProvider";
import { testTenantUuid } from "../../../lib/node";
import { TenantProvisioningConsumer } from "../../../messageQueuing/consumer/TenantProvisioningConsumer";
import { TenantProvisioning } from "../../../provisioning/TenantProvisioning";
import { ProvisioningActions } from "../../../provisioning/types";
import { RequestContext } from "../../../repository/security/requestContext";
import { TenantMetadataBuilder } from "./TenantMetadataBuilder";
import { buildProvisioningMessage } from "./provisioningMetadataCreation";

chai.use(sinonChai);
chai.use(chaiAsPromised);

describe("service/provisioning/TenantProvisioning - Brownfield Migration", function () {
  const context = RequestContext.createFromTenantId(testTenantUuid);
  const sandbox = sinon.createSandbox();

  let ffSetStub: any;
  let sendInProgressMessageStub: SinonStub;
  let makeTenantMetadataBuilder: TenantMetadataBuilder;

  function createTestStubs() {
    sandbox.stub(RequestContext, "createFromTenantId").returns(context);
    sandbox.stub(TenantProvisioningConsumer, "reply").resolves();
    sandbox.stub(FeatureFlagProvider, "isFeatureActive").resolves(false);

    sendInProgressMessageStub = sandbox.stub(TenantProvisioning.prototype, "sendInProgressMessage" as any);

    ffSetStub = sandbox.stub(FeatureFlagProvider, "setFeatureflag").resolves();
  }

  before(async function () {
    createTestStubs();
  });

  beforeEach(() => {
    makeTenantMetadataBuilder = new TenantMetadataBuilder();
  });

  after(function () {
    sandbox.restore();
    sinon.restore();
  });

  afterEach(function () {
    sandbox.resetHistory();
  });

  describe("Tenant update - Datasphere BDC Brownfield migration", function () {
    it("Should enable DWCO_BDC and DWCO_BDC_GA if classification changes to any BDC classification.", async function () {
      const oldTenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantClassification(TenantClassification.DWC)
        .build();
      const tenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantClassification(TenantClassification.DWC_BDC)
        .build();

      const message = buildProvisioningMessage(context, ProvisioningActions.UPDATE, tenantMetadata, oldTenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      sendInProgressMessageStub.resolves();

      await tenantProvisioning.updateTenant({} as MessageEvent);
      await sleep(10); // Allow time for async operations to complete

      expect(ffSetStub).to.be.calledWith(context, "DWCO_BDC", true);
      expect(ffSetStub).to.be.calledWith(context, "DWCO_BDC_GA", true);
    });

    it("Should not enable DWCO_BDC and DWCO_BDC_GA if classification does not change.", async function () {
      const oldTenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantClassification(TenantClassification.DWC_BDC)
        .build();
      const tenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantClassification(TenantClassification.DWC_BDC)
        .build();

      const message = buildProvisioningMessage(context, ProvisioningActions.UPDATE, tenantMetadata, oldTenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      sendInProgressMessageStub.resolves();

      await tenantProvisioning.updateTenant({} as MessageEvent);
      await sleep(10); // Allow time for async operations to complete

      expect(ffSetStub).to.be.not.be.called;
    });

    it("Should not enable DWCO_BDC and DWCO_BDC_GA if classification changes to anything other then 'contains BDC'.", async function () {
      const oldTenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantClassification(TenantClassification.DWC_BDC)
        .build();
      const tenantMetadata = makeTenantMetadataBuilder
        .withMinimumSizeTenantLicense()
        .withTenantClassification(TenantClassification.DWC)
        .build();

      const message = buildProvisioningMessage(context, ProvisioningActions.UPDATE, tenantMetadata, oldTenantMetadata);
      const tenantProvisioning = new TenantProvisioning(context, message);
      sendInProgressMessageStub.resolves();

      await tenantProvisioning.updateTenant({} as MessageEvent);
      await sleep(10); // Allow time for async operations to complete

      expect(ffSetStub).to.be.not.be.called;
    });
  });
});
