/** @format */

import { AuditLogClient } from "@sap/dwc-audit-logger";
import { NotificationType } from "@sap/dwc-notifications";
import { ITenantLicenses } from "@sap/dwc-tms-provider";
import chai, { expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import sinon from "sinon";
import sinonChai from "sinon-chai";
import { OperationResult, TenantClassification } from "../../../../shared/provisioning/ftc/types";
import { TenantInformationProvider } from "../../../featureflags/TenantInformationProvider";
import * as hyperscalerHelper from "../../../lib/node";
import * as nodeutil from "../../../lib/node";
import { Hyperscaler, isLocalHanaMocked, isPullRequestValidation, testTenantUuid } from "../../../lib/node";
import * as access from "../../../meta/access";
import { ProvisioningCredentials } from "../../../provisioning/ProvisioningCredentials";
import { ElasticCustomerHanaProvisioning } from "../../../provisioning/flexible/ElasticCustomerHanaProvisioning";
import * as flexibleHelpers from "../../../provisioning/flexible/helpers";
import * as updateHandlerHelpers from "../../../provisioning/flexible/updateHandler/helpers";
import * as helper from "../../../provisioning/helpers";
import { CustomerHanaProvisioning } from "../../../provisioning/standard/CustomerHanaProvisioning";
import { DataLakeSizeTypes, ITenantMetadata, ServiceOperations, ServiceStatus } from "../../../provisioning/types";
import { RequestContext } from "../../../repository/security/requestContext";
import * as adminActivator from "../../../reuseComponents/onboarding/src/adminActivator";
import { ICredstoreTenantEntry } from "../../../reuseComponents/onboarding/src/types";
import { CustomerHana } from "../../../reuseComponents/spaces/src/CustomerHana";
import { getSystemCredentials } from "../../../reuseComponents/spaces/src/tenantUsers";
import * as ipAllowlist from "../../../routes/security/ipAllowlist";
import * as Versions from "../../../routes/support/versions";
import { HanaCloudService } from "../../../scp/availableServices/HanaCloudService";
import { IScpServiceInstance, ServicePlans } from "../../../scp/types";
import { TenantManagementService } from "../../../tms/TenantManagementService";
chai.use(sinonChai);
chai.use(chaiAsPromised);
describe("service/provisioning/ElasticCustomerHanaProvisioning", function () {
  this.timeout(30000);
  let context: RequestContext;
  let sandbox: sinon.SinonSandbox;

  let hanaCloudServiceStub: any;
  let customerHanaStub: any;
  let tmsStub: any;
  let waitInstanceDeletionSpy: any;
  let getHyperscalerStub: sinon.SinonStub;
  let diIpAllowlistEnabledFFStub: sinon.SinonStub;
  let isConsumingOutboundIPEnabled: sinon.SinonStub;
  let getDIsOutboundIPsStub: sinon.SinonStub;
  let isCanaryStub: sinon.SinonStub;

  before(function () {
    context = RequestContext.createFromTenantId(testTenantUuid);
    sandbox = sinon.createSandbox();
  });

  beforeEach(function () {
    hanaCloudServiceStub = sandbox.stub(HanaCloudService.prototype);
    sandbox.stub(HanaCloudService, "fromRequestContext").returns(Promise.resolve(hanaCloudServiceStub));
    hanaCloudServiceStub.getKey.returns({
      id: "key",
      name: "key",
      credentials: {
        certificate: "key",
        driver: "key",
        host: "key",
        port: "key",
        url: "key",
      },
    });

    customerHanaStub = sandbox.stub(CustomerHana, "fromTenantId"); // called by isHanaAlreadyConfigured()
    getHyperscalerStub = sandbox.stub(hyperscalerHelper, "getHyperscaler").returns(Hyperscaler.AWS);
    sandbox.stub(CustomerHanaProvisioning.prototype, "defaultDelay" as any).get(() => 10);
    diIpAllowlistEnabledFFStub = sandbox.stub(helper, "isDiIpAllowlistEnabled");
    isConsumingOutboundIPEnabled = sandbox.stub(helper, "isConsumingOutboundIP");
    getDIsOutboundIPsStub = sandbox.stub(ipAllowlist, "getDIsOutboundIPs");
    isCanaryStub = sandbox.stub(nodeutil, "isCanary").returns(true);
  });

  afterEach(function () {
    sandbox.restore();
  });

  const createTenantMetadata = (
    thresholdMemory = "0",
    thresholdVCPU = "0",
    thresholdStorage = "0",
    thresholdDataLakeCompute = "0",
    thresholdDataLakeStorage = "0",
    thresholdDWCCU = "1500"
  ) => ({
    uuid: testTenantUuid,
    versionUuid: "mock",
    useCloudId: false,
    consumerAccountDisplayName: "testTenant",
    license: {
      thresholdMemory,
      thresholdVCPU,
      thresholdStorage,
      thresholdDataLakeStorage,
      thresholdDataLakeCompute,
      thresholdBWBridge1: "0",
      thresholdDWCCU,
    },
  });
  const createNewTenantMetadata = (
    thresholdMemory = "0",
    thresholdVCPU = "0",
    thresholdStorage = "0",
    thresholdDataLakeCompute = "0",
    thresholdDataLakeStorage = "0",
    thresholdDWCCU = "1500"
  ) => ({
    uuid: testTenantUuid,
    versionUuid: "mock",
    useCloudId: false,
    consumerAccountDisplayName: "testTenant",
    license: {
      thresholdMemory,
      thresholdVCPU,
      thresholdStorage,
      thresholdDataLakeStorage,
      thresholdDataLakeCompute,
      thresholdBWBridge1: "0",
      thresholdDWCCU,
    },
  });

  describe(" Hana update with thresholdMemory and thresholdVPCU", function () {
    beforeEach(function () {
      sandbox.stub(ElasticCustomerHanaProvisioning.prototype, "waitForHanaAvailability" as any).resolves(true);
      hanaCloudServiceStub.getService.resolves({
        id: "hana",
        name: "hana",
        instanceName: "hana",
        plan: "hana",
        status: ServiceStatus.succeeded,
        lastOperation: ServiceOperations.update,
      } as IScpServiceInstance);
      hanaCloudServiceStub.getHanaServiceParameters.resolves({
        data: { vcpu: 2, memory: 32, storage: 128 },
      });
    });

    it("Should update Customer HANA instance using thresholdMemory and thresholdVCPU.", async function () {
      const commercialResetLicenses = {
        thresholdMemory: "128",
        thresholdVCPU: "8",
        thresholdStorage: "512",
      } as ITenantLicenses;
      const tenantMetadata: ITenantMetadata = { ...createTenantMetadata(), license: commercialResetLicenses };

      await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

      const sentPayload = hanaCloudServiceStub.updateHanaServiceParametersInBackground.args[0][0];

      sinon.assert.match(sentPayload.data.memory, 128);
      sinon.assert.match(sentPayload.data.storage, 512);
      sinon.assert.match(sentPayload.data.vcpu, 8);
    });
    it("Should update Customer HANA instance with new Storage if Memory and VCPU are invalid but they are not changing.", async function () {
      // 120GB is valid for old AWS Compute blocks, but does not match new blocks of 16GB
      hanaCloudServiceStub.getHanaServiceParameters.resolves({
        data: { vcpu: 4, memory: 120, storage: 128 },
      });

      const commercialResetLicenses = {
        thresholdMemory: "120",
        thresholdVCPU: "4",
        thresholdStorage: "512",
      } as ITenantLicenses;
      const tenantMetadata: ITenantMetadata = { ...createTenantMetadata(), license: commercialResetLicenses };

      await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

      const sentPayload = hanaCloudServiceStub.updateHanaServiceParametersInBackground.args[0][0];

      sinon.assert.match(sentPayload.data.memory, 120);
      sinon.assert.match(sentPayload.data.storage, 512);
      sinon.assert.match(sentPayload.data.vcpu, 4);
    });

    it("Should throw if configuration is invalid.", async function () {
      const commercialResetLicenses = {
        thresholdMemory: "5000",
        thresholdVCPU: "2",
        thresholdStorage: "128",
      } as ITenantLicenses;
      const tenantMetadata: ITenantMetadata = { ...createTenantMetadata(), license: commercialResetLicenses };

      await expect(ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata)).to.eventually.be.rejectedWith(
        "Memory 5000 and VCPU 2 is not a valid combination."
      );
    });
  });
  describe("Hana provisioning with thresholdMemory and thresholdVPCU", function () {
    beforeEach(function () {
      sandbox.stub(ElasticCustomerHanaProvisioning.prototype, "isHanaAlreadyConfigured" as any).resolves(false);
      sandbox.stub(ProvisioningCredentials, "storeCertificate").resolves(true as any);
      hanaCloudServiceStub.getService.onFirstCall().resolves(false);
      hanaCloudServiceStub.getService.onSecondCall().resolves({
        id: "hana",
        name: "hana",
        instanceName: "hana",
        plan: "hana",
        status: ServiceStatus.succeeded,
      } as IScpServiceInstance);
    });
    afterEach(() => {
      sandbox.restore();
    });

    it("Should throw error and not create Customer HANA instance if no licenses are provided.", async function () {
      const tenantMetadata: ITenantMetadata = createTenantMetadata();

      await expect(
        ElasticCustomerHanaProvisioning.createStandardHanaInstance(context, tenantMetadata)
      ).to.be.rejectedWith("Invalid license values for initial provisioning");
    });

    it("Should create Customer HANA using values from license.", async function () {
      const commercialResetLicenses = {
        thresholdMemory: "256",
        thresholdVCPU: "16",
        thresholdStorage: "768",
      } as ITenantLicenses;

      const tenantMetadata: ITenantMetadata = {
        ...createTenantMetadata(),
        license: commercialResetLicenses,
      };

      await ElasticCustomerHanaProvisioning.createStandardHanaInstance(context, tenantMetadata);

      const sentPayload = hanaCloudServiceStub.createHanaCloudService.args[0][2];

      sinon.assert.match(sentPayload.data.memory, 256);
      sinon.assert.match(sentPayload.data.vcpu, 16);
      sinon.assert.match(sentPayload.data.storage, 768);
    });

    it("Should throw if initial configuration is invalid.", async function () {
      const commercialResetLicenses = {
        thresholdMemory: "5000",
        thresholdVCPU: "2",
        thresholdStorage: "128",
        thresholdDWCCU: "43000",
      } as ITenantLicenses;

      const tenantMetadata: ITenantMetadata = {
        ...createTenantMetadata(),
        license: commercialResetLicenses,
      };

      await expect(
        ElasticCustomerHanaProvisioning.createStandardHanaInstance(context, tenantMetadata)
      ).to.eventually.be.rejectedWith("Memory 5000 and VCPU 2 is not a valid combination.");
    });

    it("Should create Customer HANA using values from license with 384 of storage, 128 of memory ,8 of VCPU and 4300 CUs.", async function () {
      const commercialResetLicenses = {
        thresholdStorage: "384",
        thresholdDWCCU: "4300",
        thresholdVCPU: "8",
        thresholdMemory: "128",
      } as ITenantLicenses;

      const tenantMetadata: ITenantMetadata = {
        ...createTenantMetadata(),
        license: commercialResetLicenses,
      };

      await ElasticCustomerHanaProvisioning.createStandardHanaInstance(context, tenantMetadata);

      const sentPayload = hanaCloudServiceStub.createHanaCloudService.args[0][2];

      sinon.assert.match(sentPayload.data.memory, 128);
      sinon.assert.match(sentPayload.data.vcpu, 8);
      sinon.assert.match(sentPayload.data.storage, 384);
    });
  });

  it("Should create Customer HANA instance with 64GB of memory, 4vCPU and 256GB of storage if using thresholdMemory license", async function () {
    sandbox.stub(ElasticCustomerHanaProvisioning.prototype, "isHanaAlreadyConfigured" as any).resolves(false);
    hanaCloudServiceStub.getService.onFirstCall().resolves(false);

    sandbox.stub(ProvisioningCredentials, "storeCertificate").resolves(true as any);
    hanaCloudServiceStub.getService.onSecondCall().resolves({
      id: "hana",
      name: "hana",
      instanceName: "hana",
      plan: "hana",
      status: ServiceStatus.succeeded,
    } as IScpServiceInstance);

    const commercialResetLicenses = {
      thresholdMemory: "64",
      thresholdVCPU: "4",
      thresholdStorage: "256",
      thresholdDataLakeCompute: "0",
      thresholdDataLakeStorage: "0",
      thresholdBWBridge1: "0",
    } as ITenantLicenses;

    const tenantMetadata: ITenantMetadata = { ...createTenantMetadata(), license: commercialResetLicenses };
    await ElasticCustomerHanaProvisioning.createStandardHanaInstance(context, tenantMetadata);

    const sentPayload = hanaCloudServiceStub.createHanaCloudService.args[0][2];

    sinon.assert.match(sentPayload.data.memory, 64);
    sinon.assert.match(sentPayload.data.storage, 256);
    sinon.assert.match(sentPayload.data.vcpu, 4);
  });
  it("Should throw error and not create Customer HANA instance if no license is provided", async function () {
    sandbox.stub(ElasticCustomerHanaProvisioning.prototype, "isHanaAlreadyConfigured" as any).resolves(false);
    hanaCloudServiceStub.getService.onFirstCall().resolves(false);

    sandbox.stub(ProvisioningCredentials, "storeCertificate").resolves(true as any);
    hanaCloudServiceStub.getService.onSecondCall().resolves({
      id: "hana",
      name: "hana",
      instanceName: "hana",
      plan: "hana",
      status: ServiceStatus.succeeded,
    } as IScpServiceInstance);

    const tenantMetadata: ITenantMetadata = createTenantMetadata();
    await expect(
      ElasticCustomerHanaProvisioning.createStandardHanaInstance(context, tenantMetadata)
    ).to.be.rejectedWith("Invalid license values for initial provisioning");
  });

  it("should validate create request - instantiate initial HANA", async function () {
    const tenantMetadata: ITenantMetadata = createNewTenantMetadata("384", "24", "1024", "0", "0");
    customerHanaStub.throwsException(500);

    hanaCloudServiceStub.getService.returns(
      Promise.resolve({
        id: "hana",
        name: "hana",
        instanceName: "hana",
        plan: "hana",
        status: ServiceStatus.succeeded,
      } as IScpServiceInstance)
    );

    sandbox.stub(ProvisioningCredentials, "storeCertificate").returns(
      Promise.resolve({
        user: "user",
        password: "password",
        host: "host",
        port: 443,
      })
    );

    await ElasticCustomerHanaProvisioning.createStandardHanaInstance(context, tenantMetadata);
  });

  it("should throw if licenses are invalid - instantiate initial HANA", async function () {
    customerHanaStub.throwsException(500);
    const licenses = {
      thresholdMemory: "0",
      thresholdVCPU: "4",
      thresholdStorage: "512",
      thresholdDataLakeCompute: "0",
      thresholdDataLakeStorage: "0",
      thresholdBWBridge1: "0",
    } as ITenantLicenses;
    const tenantMetadata: ITenantMetadata = { ...createTenantMetadata(), license: licenses };
    await expect(
      ElasticCustomerHanaProvisioning.createStandardHanaInstance(context, tenantMetadata)
    ).to.be.rejectedWith("Invalid license values for initial provisioning");
  });

  it("should throw if licenses are in old thresholdCompute format- instantiate initial HANA", async function () {
    customerHanaStub.throwsException(500);
    const licenses = {
      thresholdStorage: "512",
      thresholdCompute: "4",
      thresholdDataLakeCompute: "0",
      thresholdDataLakeStorage: "0",
      thresholdBWBridge1: "0",
    } as ITenantLicenses;
    const tenantMetadata: ITenantMetadata = { ...createTenantMetadata(), license: licenses };
    await expect(
      ElasticCustomerHanaProvisioning.createStandardHanaInstance(context, tenantMetadata)
    ).to.be.rejectedWith("Invalid license values for initial provisioning");
  });

  it("should allow license input for minimum configuration - instantiate initial HANA", async function () {
    const tenantMetadata: ITenantMetadata = createNewTenantMetadata("32", "2", "256");
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    customerHanaStub.throwsException(500);
    const createHanaInstanceSpy = sandbox.spy(ElasticCustomerHanaProvisioning.prototype, "createHanaInstance");
    sandbox.stub(helper, "awaitFor");
    hanaCloudServiceStub.getHanaServiceParameters.returns(Promise.resolve({ data: { vcpu: 4, storage: 360 } }));
    sandbox.stub(ProvisioningCredentials, "retrieveCredentials").returns(
      Promise.resolve({
        data: {
          user: "user",
          password: "password",
          certificate: "certificate",
          activator: {
            user: "user",
            password: "password",
            lastPassword: "lastPassword",
          },
        },
      } as ICredstoreTenantEntry)
    );

    await ElasticCustomerHanaProvisioning.createStandardHanaInstance(context, tenantMetadata);
    sinon.assert.calledOnce(createHanaInstanceSpy);
  });
  it("should validate create request - instantiate initial HANA with DWCO_DI_IP_IMPROVE: true", async function () {
    diIpAllowlistEnabledFFStub.resolves(true);
    getDIsOutboundIPsStub.resolves(["0.0.0.0/0"]);
    const tenantMetadata: ITenantMetadata = createNewTenantMetadata("384", "24", "1024", "0", "0");
    customerHanaStub.throwsException(500);

    hanaCloudServiceStub.getService.resolves({
      id: "hana",
      name: "hana",
      instanceName: "hana",
      plan: "hana",
      status: ServiceStatus.succeeded,
    } as IScpServiceInstance);

    sandbox.stub(ProvisioningCredentials, "storeCertificate").resolves({
      user: "user",
      password: "password",
      host: "host",
      port: 443,
    });

    await ElasticCustomerHanaProvisioning.createStandardHanaInstance(context, tenantMetadata);
  });
  it("should validate create request - instantiate initial HANA with DWC_PROV_TEST_OUTBOUNDIP extra parameter", async function () {
    isConsumingOutboundIPEnabled.resolves(true);
    getDIsOutboundIPsStub.resolves(["0.0.0.0/0"]);
    const tenantMetadata: ITenantMetadata = createNewTenantMetadata("384", "24", "1024", "0", "0");
    customerHanaStub.throwsException(500);

    hanaCloudServiceStub.getService.resolves({
      id: "hana",
      name: "hana",
      instanceName: "hana",
      plan: "hana",
      status: ServiceStatus.succeeded,
    } as IScpServiceInstance);

    sandbox.stub(ProvisioningCredentials, "storeCertificate").resolves({
      user: "user",
      password: "password",
      host: "host",
      port: 443,
    });

    await ElasticCustomerHanaProvisioning.createStandardHanaInstance(context, tenantMetadata);
  });

  it("should validate update request - perform changes in hana and create data lake", async function () {
    if (!isLocalHanaMocked() && !isPullRequestValidation()) {
      this.skip(); // Only executes when HANA is available
    }

    const tenantMetadata: ITenantMetadata = createNewTenantMetadata("384", "24", "1024", "10", "10");
    tmsStub = sandbox.stub(TenantInformationProvider, "fetchTenantInformation");
    await tmsStub.returns({
      license: {
        thresholdDWCCU: "10000",
        thresholdMemory: "256",
        thresholdVCPU: "16",
        thresholdStorage: "512",
        thresholdDataLakeStorage: "0",
        thresholdDataLakeCompute: "0",
      },
    });

    hanaCloudServiceStub.getHanaServiceParameters.returns(Promise.resolve({ data: { vcpu: 16, storage: 1000 } }));
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    hanaCloudServiceStub.createHanaCloudService.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");
    hanaCloudServiceStub.getHanaCloudService.onCall(2).returns(Promise.resolve({ id: "HanaIDMock" }));
    hanaCloudServiceStub.getHanaCloudService.onCall(3).returns(Promise.resolve({ id: "DataLakeIDMock" }));

    customerHanaStub.restore();
    const hanaCredentials = await CustomerHana.fromRequestContext(context);
    const systemCredentials = await getSystemCredentials(context);
    const systemConfig = {
      ...(await hanaCredentials.getUserManager()),
      user: systemCredentials.username,
      password: systemCredentials.password,
    };
    const client = await access.getClient(context, systemConfig, { exclusive: true });
    await client.exec(`CREATE ROLE "HANA_SYSRDL#CG_ADMIN_ROLE"`);
    const getClientStub = sandbox.stub(access, "getClient");
    getClientStub.returns(Promise.resolve(client));
    const activateAdminStub = sandbox.stub(adminActivator, "activateAdminUser");
    activateAdminStub.returns(Promise.resolve());

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);
    await ElasticCustomerHanaProvisioning.createOrUpdateDataLake(context, tenantMetadata);

    await client.exec(`DROP ROLE "HANA_SYSRDL#CG_ADMIN_ROLE"`);

    sinon.assert.called(hanaCloudServiceStub.getHanaServiceParameters);
    sinon.assert.calledWith(hanaCloudServiceStub.createHanaCloudService, ServicePlans.dataLake);
    sinon.assert.calledWith(hanaCloudServiceStub.createHanaCloudService, ServicePlans.connection);
  });

  it("should validate create/update request - perform upsize for HANA Data Lake", async function () {
    if (!isLocalHanaMocked() && !isPullRequestValidation()) {
      this.skip(); // Only executes when HANA is available
    }

    const tenantMetadata: ITenantMetadata = createNewTenantMetadata("384", "24", "1024", "98", "90");
    tmsStub = sandbox.stub(TenantInformationProvider, "fetchTenantInformation");
    await tmsStub.returns({
      license: {
        thresholdDWCCU: "10000",
        thresholdMemory: "360",
        tresholdVCPU: "24",
        thresholdStorage: "1024",
        thresholdDataLakeStorage: "12",
        thresholdDataLakeCompute: "10",
      },
    });

    hanaCloudServiceStub.getHanaServiceParameters.returns(Promise.resolve({ data: { vcpu: 16, storage: 1000 } }));
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    hanaCloudServiceStub.getDataLakeServiceParameters.returns(
      Promise.resolve({ data: { provisioned_size_gib: 5120 } })
    );
    hanaCloudServiceStub.updateDataLakeServiceParameters.returns(Promise.resolve());
    hanaCloudServiceStub.createHanaCloudService.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");
    hanaCloudServiceStub.getHanaCloudService.returns(Promise.resolve(true));
    hanaCloudServiceStub.getDataLakeInstance.returns(Promise.resolve(true));

    customerHanaStub.restore();
    const hanaCredentials = await CustomerHana.fromRequestContext(context);
    const systemCredentials = await getSystemCredentials(context);
    const systemConfig = {
      ...(await hanaCredentials.getUserManager()),
      user: systemCredentials.username,
      password: systemCredentials.password,
    };
    const client = await access.getClient(context, systemConfig, { exclusive: true });
    sandbox.stub(access, "getClient").returns(Promise.resolve(client));
    const activateAdminStub = sandbox.stub(adminActivator, "activateAdminUser");
    activateAdminStub.returns(Promise.resolve());

    await ElasticCustomerHanaProvisioning.createOrUpdateDataLake(context, tenantMetadata);
    sinon.assert.calledWith(hanaCloudServiceStub.updateDataLakeServiceParameters, {
      data: {
        provisioned_size_gib: 92160,
        num_writers: 6,
        writer_type: "large",
        num_coordinators: 1,
        coordinator_type: "small",
      },
    });
  });

  it("should correctly validate HANA Cloud resizing with downsizing", async function () {
    const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);
    sinon.assert.match(await provisioning.validateHanaResizingForCommercialReset(8, 8, 128, 128, 384, 384), undefined);
    sinon.assert.match(await provisioning.validateHanaResizingForCommercialReset(2, 2, 32, 32, 128, 128), undefined);
    sinon.assert.match(await provisioning.validateHanaResizingForCommercialReset(2, 2, 32, 32, 128, 256), undefined);
    sinon.assert.match(await provisioning.validateHanaResizingForCommercialReset(8, 16, 128, 256, 384, 704), undefined);
    sinon.assert.match(await provisioning.validateHanaResizingForCommercialReset(8, 4, 128, 64, 384, 384), undefined);

    await expect(provisioning.validateHanaResizingForCommercialReset(8, 8, 128, 128, 640, 384)).to.be.rejectedWith(
      "HANA Cloud storage downsizing operations are not available"
    );

    await expect(provisioning.validateHanaResizingForCommercialReset(8, 8, 128, 128, 384, 28000)).to.be.rejectedWith(
      "Requested HANA Cloud size is not part of the valid range"
    );

    await expect(provisioning.validateHanaResizingForCommercialReset(8, 16, 128, 256, 384, 576)).to.be.rejectedWith(
      "Requested HANA Cloud size is not enough for selected memory."
    );
  });

  it("should correctly calculate data lake compute sizes", async function () {
    const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);

    sinon.assert.match(provisioning.calculateDataLakeCompute(0), {
      cores: 0,
      writerNumber: -1,
      writerType: DataLakeSizeTypes.small,
      coordinatorNumber: 1,
      coordinatorType: DataLakeSizeTypes.small,
      sizeToProvision: 0,
    });
    sinon.assert.match(provisioning.calculateDataLakeCompute(1), {
      cores: 4,
      writerNumber: 1,
      writerType: DataLakeSizeTypes.small,
      coordinatorNumber: 1,
      coordinatorType: DataLakeSizeTypes.small,
      sizeToProvision: 1024,
    });
    sinon.assert.match(provisioning.calculateDataLakeCompute(3), {
      cores: 4,
      writerNumber: 1,
      writerType: DataLakeSizeTypes.small,
      coordinatorNumber: 1,
      coordinatorType: DataLakeSizeTypes.small,
      sizeToProvision: 3072,
    });
    sinon.assert.match(provisioning.calculateDataLakeCompute(5), {
      cores: 6,
      writerNumber: 2,
      writerType: DataLakeSizeTypes.small,
      coordinatorNumber: 1,
      coordinatorType: DataLakeSizeTypes.small,
      sizeToProvision: 5120,
    });
    sinon.assert.match(provisioning.calculateDataLakeCompute(8), {
      cores: 8,
      writerNumber: 3,
      writerType: DataLakeSizeTypes.small,
      coordinatorNumber: 1,
      coordinatorType: DataLakeSizeTypes.small,
      sizeToProvision: 8192,
    });
    sinon.assert.match(provisioning.calculateDataLakeCompute(10), {
      cores: 10,
      writerNumber: 1,
      writerType: DataLakeSizeTypes.medium,
      coordinatorNumber: 1,
      coordinatorType: DataLakeSizeTypes.small,
      sizeToProvision: 10240,
    });
    sinon.assert.match(provisioning.calculateDataLakeCompute(15), {
      cores: 18,
      writerNumber: 1,
      writerType: DataLakeSizeTypes.large,
      coordinatorNumber: 1,
      coordinatorType: DataLakeSizeTypes.small,
      sizeToProvision: 15360,
    });
    sinon.assert.match(provisioning.calculateDataLakeCompute(53), {
      cores: 50,
      writerNumber: 3,
      writerType: DataLakeSizeTypes.large,
      coordinatorNumber: 1,
      coordinatorType: DataLakeSizeTypes.small,
      sizeToProvision: 54272,
    });
    sinon.assert.match(provisioning.calculateDataLakeCompute(90), {
      cores: 98,
      writerNumber: 6,
      writerType: DataLakeSizeTypes.large,
      coordinatorNumber: 1,
      coordinatorType: DataLakeSizeTypes.small,
      sizeToProvision: 92160,
    });
  });

  it("should correctly validate Data Lake resizing", async () => {
    const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);

    sinon.assert.match(provisioning.validateDataLakeResizing(5120, 5120), false);
    sinon.assert.match(provisioning.validateDataLakeResizing(10240, 5120), true);

    let errorMessage;

    try {
      provisioning.validateDataLakeResizing(1024, 5120);
    } catch (e) {
      errorMessage = e.message;
    }
    sinon.assert.match(errorMessage, "HANA Data Lake downsizing operations are not available");

    try {
      provisioning.validateDataLakeResizing(97280, 5120);
    } catch (e) {
      errorMessage = e.message;
    }
    sinon.assert.match(errorMessage, "Requested HANA Data Lake size is not part of the valid range");

    try {
      provisioning.validateDataLakeResizing(512, 0);
    } catch (e) {
      errorMessage = e.message;
    }
    sinon.assert.match(errorMessage, "Requested HANA Data Lake size is not part of the valid range");
  });

  it("should update Hana to AWS configuration of thresholdStorage: 9664, thresholdVCPU: 120, thresholdMemory: 3600", async () => {
    const tenantMetadata = createNewTenantMetadata("3600", "120", "9664");

    hanaCloudServiceStub.getHanaServiceParameters.returns(Promise.resolve({ data: { vcpu: 16, storage: 1000 } }));
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

    sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
      data: {
        vcpu: 120,
        memory: 3600,
        storage: 9664,
      },
    });
  });

  it("should update Hana to AWS configuration of thresholdStorage: 16000, thresholdMemory: 5970", async () => {
    const tenantMetadata = createNewTenantMetadata("5970", "440", "16000");
    hanaCloudServiceStub.getHanaServiceParameters.returns(Promise.resolve({ data: { vcpu: 16, storage: 1000 } }));
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

    sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
      data: {
        vcpu: 440,
        memory: 5970,
        storage: 16000,
      },
    });
  });

  it("should update Hana from AWS configuration from thresholdStorage: 9664, thresholdMemory: 3600 to thresholdStorage: 16000, thresholdMemory: 5970", async () => {
    const tenantMetadata = createNewTenantMetadata("5970", "440", "16000");

    hanaCloudServiceStub.getHanaServiceParameters.returns(
      Promise.resolve({ data: { vcpu: 120, memory: 3600, storage: 9664 } })
    );
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

    sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
      data: {
        vcpu: 440,
        memory: 5970,
        storage: 16000,
      },
    });
  });

  it("should update Hana to Azure configuration of thresholdStorage: 9664, thresholdVCPU: 120, thresholdMemory: 3776", async () => {
    getHyperscalerStub.returns(Hyperscaler.Azure);
    const tenantMetadata = createNewTenantMetadata("1920", "120", "9664");

    hanaCloudServiceStub.getHanaServiceParameters.returns(Promise.resolve({ data: { vcpu: 16, storage: 1000 } }));
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

    sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
      data: {
        vcpu: 120,
        memory: 1920,
        storage: 9664,
      },
    });
  });

  it("should update Hana to Azure configuration of thresholdStorage: 14080, thresholdMemory: 5600", async () => {
    getHyperscalerStub.returns(Hyperscaler.Azure);
    const tenantMetadata = createNewTenantMetadata("5600", "412", "14080");

    hanaCloudServiceStub.getHanaServiceParameters.returns(Promise.resolve({ data: { vcpu: 16, storage: 1000 } }));
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

    sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
      data: {
        vcpu: 412,
        memory: 5600,
        storage: 14080,
      },
    });
  });

  it("should update Hana from Azure configuration from thresholdStorage: 9216, thresholdMemory: 3776 to thresholdStorage: 14080, thresholdMemory: 5600", async () => {
    getHyperscalerStub.returns(Hyperscaler.Azure);
    const tenantMetadata = createNewTenantMetadata("5600", "412", "14080");

    hanaCloudServiceStub.getHanaServiceParameters.returns(
      Promise.resolve({ data: { vcpu: 120, memory: 3776, storage: 9480 } })
    );
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

    sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
      data: {
        vcpu: 412,
        memory: 5600,
        storage: 14080,
      },
    });
  });
  it("should update hana from GCP configuration of thresholdStorage: 9344, thresholdMemory: 3700", async () => {
    getHyperscalerStub.returns(Hyperscaler.GCP);
    const tenantMetadata = createNewTenantMetadata("3700", "124", "9344");

    hanaCloudServiceStub.getHanaServiceParameters.returns(Promise.resolve({ data: { vcpu: 16, storage: 1000 } }));
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

    sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
      data: {
        vcpu: 124,
        memory: 3700,
        storage: 9344,
      },
    });
  });
  it("should update hana from GCP configuration of thresholdStorage: 14464, thresholdMemory: 5750", async () => {
    getHyperscalerStub.returns(Hyperscaler.GCP);
    const tenantMetadata = createNewTenantMetadata("5750", "204", "14464");

    hanaCloudServiceStub.getHanaServiceParameters.returns(Promise.resolve({ data: { vcpu: 16, storage: 1000 } }));
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

    sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
      data: {
        vcpu: 204,
        memory: 5750,
        storage: 14464,
      },
    });
  });
  it("should update Hana from GCP configuration from thresholdStorage: 9480, thresholdMemory: 3700 to thresholdStorage: 14464, thresholdMemory: 5750", async () => {
    getHyperscalerStub.returns(Hyperscaler.GCP);
    const tenantMetadata = createNewTenantMetadata("5750", "204", "14464");

    hanaCloudServiceStub.getHanaServiceParameters.returns(
      Promise.resolve({ data: { vcpu: 156, memory: 3700, storage: 9480 } })
    );
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

    sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
      data: {
        vcpu: 204,
        memory: 5750,
        storage: 14464,
      },
    });
  });
  it("should update Hana downsizing compute from thresholdStorage: 840, thresholdMemory: 360 to thresholdStorage: 840, thresholdMemory: 256", async () => {
    const tenantMetadata = createNewTenantMetadata("256", "16", "840");

    hanaCloudServiceStub.getHanaServiceParameters.returns(
      Promise.resolve({ data: { vcpu: 24, memory: 360, storage: 840 } })
    );
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

    sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
      data: {
        vcpu: 16,
        memory: 256,
        storage: 840,
      },
    });
  });

  it("should update Hana downsizing compute from thresholdStorage: 2280, thresholdMemory: 840 to thresholdStorage: 2280, thresholdMemory: 320", async () => {
    const tenantMetadata = createNewTenantMetadata("320", "20", "2280");

    hanaCloudServiceStub.getHanaServiceParameters.returns(
      Promise.resolve({ data: { vcpu: 56, memory: 840, storage: 2280 } })
    );
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

    sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
      data: {
        vcpu: 20,
        memory: 320,
        storage: 2280,
      },
    });
  });
  it("should not reduce storage when downsizing from thresholdStorage: 768, thresholdMemory: 300 to thresholdStorage: 768, thresholdMemory: 240", async () => {
    const tenantMetadata = createNewTenantMetadata("240", "16", "768");

    hanaCloudServiceStub.getHanaServiceParameters.returns(
      Promise.resolve({ data: { vcpu: 20, memory: 300, storage: 840 } })
    );
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

    sinon.assert.neverCalledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
      data: {
        vcpu: 16,
        memory: 240,
        storage: 768,
      },
    });
  });
  it("should update Hana downsizing compute from thresholdStorage: 4040, thresholdMemory: 1500 to thresholdStorage: 4040, thresholdMemory: 192", async () => {
    const tenantMetadata = createNewTenantMetadata("192", "12", "4040");

    hanaCloudServiceStub.getHanaServiceParameters.returns(
      Promise.resolve({ data: { vcpu: 100, memory: 1500, storage: 4040 } })
    );
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

    sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
      data: {
        vcpu: 12,
        memory: 192,
        storage: 4040,
      },
    });
  });
  it("should update Hana from AWS configuration from thresholdStorage: 3072, thresholdMemory: 1200 to thresholdStorage: 5120, thresholdMemory: 1800", async () => {
    const tenantMetadata = createNewTenantMetadata("1800", "120", "5120");

    hanaCloudServiceStub.getHanaServiceParameters.returns(
      Promise.resolve({ data: { vcpu: 80, memory: 1200, storage: 3240 } })
    );
    hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
    sandbox.stub(helper, "awaitFor");

    await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

    sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
      data: {
        vcpu: 120,
        memory: 1800,
        storage: 5120,
      },
    });
  });
  it("should calculate RmsConcurency", async () => {
    const license = {
      thresholdMemory: "60",
      thresholdVCPU: "4",
      thresholdDataLakeStorage: "256",
      thresholdDataLakeCompute: "0",
      thresholdDWCCU: "15000",
      thresholdBWBridge1: "0",
    };
    const metadata = { license, classification: TenantClassification.DWC };
    const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);

    expect(await provisioning.calculateRmsConcurrency(99, metadata as any)).to.be.equal(0);
    expect(await provisioning.calculateRmsConcurrency(100, metadata as any)).to.be.equal(1);
    expect(await provisioning.calculateRmsConcurrency(199, metadata as any)).to.be.equal(1);
    expect(await provisioning.calculateRmsConcurrency(700, metadata as any)).to.be.equal(7);
    expect(await provisioning.calculateRmsConcurrency(72000, metadata as any)).to.be.equal(8);
    expect(await provisioning.calculateRmsConcurrency(1000, metadata as any)).to.be.equal(8);
  });

  it("should throw when trying to update HANA without thresholdMemory, thresholdStorage or thresholdVCPU", async () => {
    const tenantMetadata = createNewTenantMetadata("0", "0", "128");

    hanaCloudServiceStub.getHanaServiceParameters.returns(
      Promise.resolve({ data: { memory: 32, vcpu: 2, storage: 128 } })
    );
    sandbox.stub(helper, "awaitFor");

    let errorMsg;
    try {
      await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);
    } catch (error) {
      errorMsg = error.message;
    }
    sinon.assert.match(
      errorMsg,
      "thresholdVCPU, thresholdMemory and thresholdStorage must be defined in license update."
    );
    sinon.assert.notCalled(hanaCloudServiceStub.updateHanaServiceParametersInBackground);
  });

  describe("Data Lake instance delete", () => {
    it("deleteDataLakeInstance, should delete data lake instance", async () => {
      const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);

      sandbox.stub(ElasticCustomerHanaProvisioning, "getInstanceName").returns(HanaCloudService.HANA_INSTANCE_NAME);
      waitInstanceDeletionSpy = sandbox.spy(provisioning, "waitInstanceDeletion");

      hanaCloudServiceStub.deleteServiceInBackground.resolves();
      const serviceInstanceExpec = {
        status: ServiceStatus.progress,
        lastOperation: ServiceOperations.delete,
      } as IScpServiceInstance;

      hanaCloudServiceStub.getService.onFirstCall().resolves(serviceInstanceExpec);
      hanaCloudServiceStub.getService.onSecondCall().resolves(null);

      await provisioning.deleteDataLakeInstance();
      sinon.assert.calledOnce(hanaCloudServiceStub.deleteServiceInBackground);
      sinon.assert.calledTwice(hanaCloudServiceStub.getService);
      sinon.assert.calledOnce(waitInstanceDeletionSpy);
    });

    it("deleteDataLakeInstance, should throw an error when instance deletion fails and the last operation is delete", async () => {
      const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);
      waitInstanceDeletionSpy = sandbox.spy(provisioning, "waitInstanceDeletion");
      sandbox.stub(ElasticCustomerHanaProvisioning, "getInstanceName").returns(HanaCloudService.HANA_INSTANCE_NAME);
      hanaCloudServiceStub.deleteServiceInBackground.resolves();
      const serviceInstanceExpec = {
        status: ServiceStatus.failed,
        lastOperation: ServiceOperations.delete,
      } as IScpServiceInstance;
      hanaCloudServiceStub.getService.resolves(serviceInstanceExpec);

      let hasThrown = false;
      try {
        await provisioning.deleteDataLakeInstance();
      } catch (error) {
        hasThrown = true;
      }
      sinon.assert.match(hasThrown, true);
      sinon.assert.calledOnce(hanaCloudServiceStub.deleteServiceInBackground);
      sinon.assert.calledOnce(hanaCloudServiceStub.getService);
    });

    it("deleteDataLakeInstance, should throw an error when instance deletion fails and the last operation is not delete", async () => {
      const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);
      waitInstanceDeletionSpy = sandbox.spy(provisioning, "waitInstanceDeletion");
      sandbox.stub(ElasticCustomerHanaProvisioning, "getInstanceName").returns(HanaCloudService.HANA_INSTANCE_NAME);
      hanaCloudServiceStub.deleteServiceInBackground.resolves();
      const serviceInstanceExpec = {
        status: ServiceStatus.failed,
        lastOperation: ServiceOperations.update,
      } as IScpServiceInstance;
      hanaCloudServiceStub.getService.resolves(serviceInstanceExpec);

      let hasThrown = false;
      try {
        await provisioning.deleteDataLakeInstance();
      } catch (error) {
        hasThrown = true;
      }
      sinon.assert.match(hasThrown, true);
      sinon.assert.calledOnce(hanaCloudServiceStub.deleteServiceInBackground);
      sinon.assert.calledOnce(hanaCloudServiceStub.getService);
    });
  });
  describe("Data Lake connection delete", () => {
    it("deleteDataLakeConnection, should delete data lake connection", async () => {
      const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);
      sandbox.stub(ElasticCustomerHanaProvisioning, "getInstanceName").returns(HanaCloudService.HANA_INSTANCE_NAME);
      waitInstanceDeletionSpy = sandbox.spy(provisioning, "waitInstanceDeletion");

      hanaCloudServiceStub.deleteServiceInBackground.resolves();
      const serviceInstanceExpec = {
        status: ServiceStatus.progress,
        lastOperation: ServiceOperations.delete,
      } as IScpServiceInstance;

      hanaCloudServiceStub.getService.onFirstCall().resolves(serviceInstanceExpec);
      hanaCloudServiceStub.getService.onSecondCall().resolves(null);

      await provisioning.deleteDataLakeConnection();
      sinon.assert.calledOnce(hanaCloudServiceStub.deleteServiceInBackground);
      sinon.assert.calledTwice(hanaCloudServiceStub.getService);
      sinon.assert.calledOnce(waitInstanceDeletionSpy);
    });

    it("deleteDataLakeConnection, should throw error when instance delete is fail and operation is delete", async () => {
      const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);
      sandbox.stub(ElasticCustomerHanaProvisioning, "getInstanceName").returns(HanaCloudService.HANA_INSTANCE_NAME);
      waitInstanceDeletionSpy = sandbox.spy(provisioning, "waitInstanceDeletion");
      hanaCloudServiceStub.deleteServiceInBackground.resolves();
      const serviceInstanceExpec = {
        status: ServiceStatus.failed,
        lastOperation: ServiceOperations.delete,
      } as IScpServiceInstance;
      hanaCloudServiceStub.getService.resolves(serviceInstanceExpec);

      let hasThrown = false;
      try {
        await provisioning.deleteDataLakeConnection();
      } catch (error) {
        hasThrown = true;
      }
      sinon.assert.match(hasThrown, true);
      sinon.assert.calledOnce(hanaCloudServiceStub.deleteServiceInBackground);
      sinon.assert.calledOnce(hanaCloudServiceStub.getService);
    });

    it("deleteDataLakeConnection, should throw error when instance delete is fail and operation is not delete", async () => {
      const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);
      sandbox.stub(ElasticCustomerHanaProvisioning, "getInstanceName").returns(HanaCloudService.HANA_INSTANCE_NAME);
      waitInstanceDeletionSpy = sandbox.spy(provisioning, "waitInstanceDeletion");
      hanaCloudServiceStub.deleteServiceInBackground.resolves();
      const serviceInstanceExpec = {
        status: ServiceStatus.failed,
        lastOperation: ServiceOperations.update,
      } as IScpServiceInstance;
      hanaCloudServiceStub.getService.resolves(serviceInstanceExpec);

      let hasThrown = false;
      try {
        await provisioning.deleteDataLakeConnection();
      } catch (error) {
        hasThrown = true;
      }
      sinon.assert.match(hasThrown, true);

      sinon.assert.calledOnce(hanaCloudServiceStub.deleteServiceInBackground);
      sinon.assert.calledOnce(hanaCloudServiceStub.getService);
    });
  });

  describe("Data Lake connection and instance delete", () => {
    it("deleteDataLakeInstanceAndConnection, should delete data lake connection and instance", async () => {
      hanaCloudServiceStub.deleteServiceInBackground.resolves();
      const serviceInstanceExpec = {
        status: ServiceStatus.progress,
        lastOperation: ServiceOperations.delete,
      } as IScpServiceInstance;

      hanaCloudServiceStub.getService.onFirstCall().resolves(serviceInstanceExpec);
      hanaCloudServiceStub.getService.onSecondCall().resolves(undefined);
      hanaCloudServiceStub.getService.onThirdCall().resolves(serviceInstanceExpec);
      hanaCloudServiceStub.getService.onCall(3).resolves(undefined);

      await ElasticCustomerHanaProvisioning.deleteDataLakeInstanceAndConnection(context);
      sinon.assert.calledTwice(hanaCloudServiceStub.deleteServiceInBackground);
      sinon.assert.callCount(hanaCloudServiceStub.getService, 4);
    });

    it("deleteDataLakeInstanceAndConnection, should not delete data lake instance and connnection", async () => {
      hanaCloudServiceStub.deleteServiceInBackground.resolves();
      const serviceInstanceExpec = {
        status: ServiceStatus.failed,
        lastOperation: ServiceOperations.delete,
      } as IScpServiceInstance;

      hanaCloudServiceStub.getService.onFirstCall().resolves(serviceInstanceExpec);

      let hasThrown = false;
      try {
        await ElasticCustomerHanaProvisioning.deleteDataLakeInstanceAndConnection(context);
      } catch (error) {
        hasThrown = true;
      }
      sinon.assert.match(hasThrown, true);
      sinon.assert.calledOnce(hanaCloudServiceStub.deleteServiceInBackground);
      sinon.assert.callCount(hanaCloudServiceStub.getService, 1);
    });
  });

  describe("Hana update strategy property for Near Zero Downtime", () => {
    it("should update Hana with update strategy without_restart and change it back to with_restart on non-canary landscape", async () => {
      isCanaryStub.returns(false);
      const tenantMetadata = createNewTenantMetadata("384", "24", "1024");

      hanaCloudServiceStub.getHanaServiceParameters.returns(
        Promise.resolve({ data: { vcpu: 16, memory: 240, storage: 768 } })
      );
      hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
      sandbox.stub(helper, "awaitFor");

      await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

      sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
        data: {
          vcpu: 24,
          memory: 384,
          storage: 1024,
          update_strategy: "without_restart",
        },
      });

      sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
        data: {
          update_strategy: "with_restart",
        },
      });
    });

    it("should update Hana without update strategy property on canary landscape", async () => {
      isCanaryStub.returns(true);
      const tenantMetadata = createNewTenantMetadata("384", "24", "1024");

      hanaCloudServiceStub.getHanaServiceParameters.returns(
        Promise.resolve({ data: { vcpu: 16, memory: 240, storage: 768 } })
      );
      hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());
      sandbox.stub(helper, "awaitFor");

      await ElasticCustomerHanaProvisioning.updateHana(context, tenantMetadata);

      sinon.assert.calledOnce(hanaCloudServiceStub.updateHanaServiceParametersInBackground);
      sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, {
        data: {
          vcpu: 24,
          memory: 384,
          storage: 1024,
        },
      });
    });
  });

  describe("convertDataLakeBlockToMegabyte", () => {
    it("calculate convertDataLakeBlockToMegabyte correctly ", async () => {
      const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);

      const catalogStorageInMbOne = provisioning.convertDataLakeBlockToMegabyte(0);
      expect(catalogStorageInMbOne).to.equal(0);

      const catalogStorageInMbTwo = provisioning.convertDataLakeBlockToMegabyte(1);
      expect(catalogStorageInMbTwo).to.equal(1024);

      const catalogStorageInMbThree = provisioning.convertDataLakeBlockToMegabyte(3);
      expect(catalogStorageInMbThree).to.equal(3072);
    });
  });

  describe("Upgrade Customer Hana to latest Patch Version", () => {
    let waitForHanaAvailabilityStub;
    let getHanaLatestPatchVersionStub;
    let waitForUpdateStub;
    let sendNotificationStub;
    let savePatchUpgradeLastOperationMetadataStub;
    let isHanaPatchUpgradeEnabledStub;
    let addTagStub: sinon.SinonStub;
    let removeTagStub: sinon.SinonStub;

    const patchUpgradePayload = {
      data: {
        update_strategy: "without_restart",
        productVersion: {
          releaseCycle: "release_cycle_value",
          track: "0000.00",
          id: "0000.00.00",
        },
      },
    };

    const updateStrategyPayload = {
      data: {
        update_strategy: "with_restart",
      },
    };

    beforeEach(function () {
      addTagStub = sandbox.stub(TenantManagementService.prototype, "addTags").resolves(true);
      removeTagStub = sandbox.stub(TenantManagementService.prototype, "removeTags").resolves();

      isHanaPatchUpgradeEnabledStub = sandbox.stub(helper, "isHanaPatchUpgradeEnabled");

      waitForHanaAvailabilityStub = sandbox
        .stub(ElasticCustomerHanaProvisioning.prototype, "waitForHanaAvailability" as any)
        .resolves(true);

      waitForUpdateStub = sandbox
        .stub(ElasticCustomerHanaProvisioning.prototype, "waitForUpdate" as any)
        .resolves(true);

      sendNotificationStub = sandbox.stub(flexibleHelpers, "sendNotification").resolves();
      savePatchUpgradeLastOperationMetadataStub = sandbox
        .stub(flexibleHelpers, "savePatchUpgradeLastOperationMetadata")
        .resolves();

      getHanaLatestPatchVersionStub = sandbox.stub(Versions, "getHanaLatestPatchVersion").resolves({
        releaseCycle: "release_cycle_value",
        track: "0000.00",
        id: "0000.00.00",
        expirationDate: "2024-10-10",
        version: {
          buildId: "000000000",
        },
      });
    });
    afterEach(function () {
      sandbox.restore();
    });

    it("Should upgrade customer hana patch version, send notification and change update strategy back after the upgrade if DWCO_HANA_PATCH_UPGRADE is on", async () => {
      isHanaPatchUpgradeEnabledStub.resolves(true);
      hanaCloudServiceStub.updateHanaServiceParametersInBackground.resolves();

      const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);
      await provisioning.upgradeToLatestPatch();

      sinon.assert.calledOnce(addTagStub);
      sinon.assert.calledOnce(removeTagStub);
      sinon.assert.calledOnce(isHanaPatchUpgradeEnabledStub);
      sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, patchUpgradePayload);
      sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, updateStrategyPayload);
      sinon.assert.calledTwice(waitForUpdateStub);
      sinon.assert.calledOnce(savePatchUpgradeLastOperationMetadataStub);
      sinon.assert.calledWith(savePatchUpgradeLastOperationMetadataStub, context, OperationResult.SUCCESS);
      sinon.assert.calledTwice(sendNotificationStub);
      sinon.assert.calledWith(
        sendNotificationStub,
        context,
        sinon.match.any,
        sinon.match.any,
        NotificationType.SUCCESS
      );
      sinon.assert.calledOnce(getHanaLatestPatchVersionStub);
      sinon.assert.calledTwice(waitForHanaAvailabilityStub);
    });

    it("Should not upgrade customer hana patch version and not perform any actions if DWCO_HANA_PATCH_UPGRADE is off", async () => {
      isHanaPatchUpgradeEnabledStub.resolves(false);

      const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);
      await provisioning.upgradeToLatestPatch();

      sinon.assert.notCalled(addTagStub);
      sinon.assert.notCalled(removeTagStub);
      sinon.assert.calledOnce(isHanaPatchUpgradeEnabledStub);
      sinon.assert.notCalled(getHanaLatestPatchVersionStub);
      sinon.assert.notCalled(hanaCloudServiceStub.updateHanaServiceParametersInBackground);
      sinon.assert.notCalled(waitForUpdateStub);
      sinon.assert.notCalled(savePatchUpgradeLastOperationMetadataStub);
      sinon.assert.notCalled(sendNotificationStub);
      sinon.assert.notCalled(waitForHanaAvailabilityStub);
    });

    it("Should throw error and send notification when updateHanaServiceParametersInBackground fails in version upgrade if DWCO_HANA_PATCH_UPGRADE is on", async () => {
      isHanaPatchUpgradeEnabledStub.resolves(true);
      hanaCloudServiceStub.updateHanaServiceParametersInBackground.rejects();

      const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);
      await expect(provisioning.upgradeToLatestPatch()).to.be.rejectedWith(`HANA Cloud patch upgrade operation failed`);

      sinon.assert.calledOnce(addTagStub);
      sinon.assert.calledOnce(removeTagStub);
      sinon.assert.calledOnce(getHanaLatestPatchVersionStub);
      sinon.assert.calledOnce(hanaCloudServiceStub.updateHanaServiceParametersInBackground);
      sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, patchUpgradePayload);
      sinon.assert.notCalled(waitForUpdateStub);
      sinon.assert.calledOnce(savePatchUpgradeLastOperationMetadataStub);
      sinon.assert.calledWith(savePatchUpgradeLastOperationMetadataStub, context, OperationResult.FAILED);
      sinon.assert.calledTwice(sendNotificationStub);
      sinon.assert.calledWith(sendNotificationStub, context, sinon.match.any, sinon.match.any, NotificationType.ALERT);
      sinon.assert.calledOnce(waitForHanaAvailabilityStub);
    });

    it("Should throw error in patch upgrade when updateHanaServiceParametersInBackground fails in update strategy change if DWCO_HANA_PATCH_UPGRADE is on", async () => {
      isHanaPatchUpgradeEnabledStub.resolves(true);
      hanaCloudServiceStub.updateHanaServiceParametersInBackground.withArgs(patchUpgradePayload).resolves();
      hanaCloudServiceStub.updateHanaServiceParametersInBackground.withArgs(updateStrategyPayload).rejects();
      hanaCloudServiceStub.updateHanaServiceParametersInBackground.returns(Promise.resolve());

      const provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);

      await expect(provisioning.upgradeToLatestPatch()).to.be.rejectedWith(
        `HANA Cloud update-strategy change failed for patch version upgrade`
      );
      sinon.assert.calledOnce(addTagStub);
      sinon.assert.calledOnce(removeTagStub);
      sinon.assert.calledOnce(getHanaLatestPatchVersionStub);
      sinon.assert.calledTwice(hanaCloudServiceStub.updateHanaServiceParametersInBackground);
      sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, patchUpgradePayload);
      sinon.assert.calledWith(hanaCloudServiceStub.updateHanaServiceParametersInBackground, updateStrategyPayload);
      sinon.assert.calledOnce(waitForUpdateStub);
      sinon.assert.calledTwice(waitForHanaAvailabilityStub);
    });
  });

  describe("Toggle Multi AZ for Customer HANA", () => {
    let setOrUpdateFtcMetadataStub: sinon.SinonStub;
    let readMetadataStub: sinon.SinonStub;
    let auditLogStub: sinon.SinonStub;
    let provisioning;

    beforeEach(async () => {
      hanaCloudServiceStub.updateHanaServiceParametersInBackground.resolves();
      hanaCloudServiceStub.getService.resolves({ status: "succeeded" });
      readMetadataStub = sandbox
        .stub(updateHandlerHelpers, "readFtcMetadata")
        .resolves({ metadata: { options: { isHanaMultiAZEnabled: false } }, errors: [] });
      setOrUpdateFtcMetadataStub = sandbox.stub(flexibleHelpers, "setOrUpdateFtcMetadata").resolves();
      sandbox.stub(helper, "awaitFor").resolves();
      auditLogStub = sandbox.stub(AuditLogClient.getInstance(), "configurationChange").resolves({
        success: sandbox.stub().resolves(),
        failure: sandbox.stub().resolves(),
      });
      provisioning = await ElasticCustomerHanaProvisioning.fromContext(context);
    });

    it("should update HANA service with the correct payload when enable is true", async () => {
      const expectedPayload = {
        data: {
          slaLevel: "elevated",
          availabilityZonePlacement: { highAvailabilityCrossMultiAZEnabled: true },
        },
      };

      await provisioning.handleMultiAZAndMetadata(true);

      expect(hanaCloudServiceStub.updateHanaServiceParametersInBackground).to.be.calledOnce;
      expect(hanaCloudServiceStub.updateHanaServiceParametersInBackground).to.be.calledWith(expectedPayload);
    });

    it("should update HANA service with the correct payload when enable is false", async () => {
      const expectedPayload = {
        data: {
          slaLevel: "standard",
          availabilityZonePlacement: undefined,
        },
      };

      await provisioning.handleMultiAZAndMetadata(false);

      expect(hanaCloudServiceStub.updateHanaServiceParametersInBackground).to.be.calledOnce;
      expect(hanaCloudServiceStub.updateHanaServiceParametersInBackground).to.be.calledWith(expectedPayload);
    });

    it("should throw error if update fails and not update metadata", async () => {
      hanaCloudServiceStub.updateHanaServiceParametersInBackground.rejects(new Error("Update failed"));
      await expect(provisioning.handleMultiAZAndMetadata(true)).to.be.rejected;

      expect(setOrUpdateFtcMetadataStub).to.not.be.called;
    });

    it("should set isHanaMultiAZEnabled on metadata after a successful toggle", async () => {
      await provisioning.handleMultiAZAndMetadata(true);
      expect(setOrUpdateFtcMetadataStub).to.be.calledWith(context, { options: { isHanaMultiAZEnabled: true } });

      await provisioning.handleMultiAZAndMetadata(false);
      expect(setOrUpdateFtcMetadataStub).to.be.calledWith(context, { options: { isHanaMultiAZEnabled: false } });
    });

    it("should throw error if metadata is missing", async () => {
      readMetadataStub.rejects(Error("No metadata"));
      await expect(provisioning.handleMultiAZAndMetadata(true)).to.be.rejected;
    });

    it("should throw error if updating metadata errors", async () => {
      setOrUpdateFtcMetadataStub.rejects(new Error("Metadata update failed"));
      await expect(provisioning.handleMultiAZAndMetadata(true)).to.be.rejected;
    });

    it("should call AuditLog with the correct parameters", async () => {
      await provisioning.handleMultiAZAndMetadata(true);

      expect(auditLogStub).to.be.calledWithMatch({
        type: "Toggle MultiAZ for Customer HANA",
        attributes: [
          {
            name: "MultiAZ Enabled",
            old: "false",
            new: "true",
          },
        ],
      });
    });

    it("should call AuditLog with old: unknown if metadata has no options", async () => {
      readMetadataStub.resolves({ metadata: { options: {} }, errors: [] });
      await provisioning.handleMultiAZAndMetadata(true);

      expect(auditLogStub).to.be.calledWithMatch({
        type: "Toggle MultiAZ for Customer HANA",
        attributes: [
          {
            name: "MultiAZ Enabled",
            old: "unknown",
            new: "true",
          },
        ],
      });
    });

    it("should call AuditLog with old: unknown if metadata option is missing", async () => {
      readMetadataStub.resolves({ metadata: { options: {} }, errors: [] });
      await provisioning.handleMultiAZAndMetadata(true);

      expect(auditLogStub).to.be.calledWithMatch({
        type: "Toggle MultiAZ for Customer HANA",
        attributes: [
          {
            name: "MultiAZ Enabled",
            old: "unknown",
            new: "true",
          },
        ],
      });
    });
  });
});
