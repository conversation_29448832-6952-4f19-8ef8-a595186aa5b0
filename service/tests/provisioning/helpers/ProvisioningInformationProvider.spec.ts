/** @format */

import { expect, use } from "chai";
import chaiAsPromised from "chai-as-promised";
import sinon from "sinon";
import { TenantClassification } from "../../../../shared/provisioning/ftc/types";
import { FeatureFlagProvider } from "../../../featureflags/FeatureFlagProvider";
import { TenantInformationProvider } from "../../../featureflags/TenantInformationProvider";
import { ITenantInformation, TenantStatus, TmsTags } from "../../../featureflags/types";
import { testTenantUuid } from "../../../lib/node";
import { ProvisioningInformationProvider } from "../../../provisioning/ProvisioningInformationProvider";
import { RequestContext } from "../../../repository/security/requestContext";
import { CustomerHanaRuntimeData } from "../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";

use(chaiAsPromised);

const tenantInfo = {
  tags: [TmsTags.DWC_FEATURE_TAG],
  type: undefined,
  license: {
    thresholdBWBridge1: "10",
    thresholdCatalogConcurrency: "10",
    thresholdCatalogConcurrencyIncluded: "1",
    thresholdCatalogNodeHours: "10",
    thresholdCatalogNodeHoursIncluded: "50",
    thresholdCatalogStorage: "10",
    thresholdCatalogStorageIncluded: "256",
    thresholdMemory: "10",
    thresholdVCPU: "10",
    thresholdDWCCU: "10",
    thresholdDataLakeStorage: "10",
    thresholdDataLakeCompute: "10",
    thresholdRmsConcurrency: "10",
    thresholdRmsConcurrencyIncluded: "1",
    thresholdRmsNodeHours: "10",
    thresholdRmsNodeHoursIncluded: "100",
    thresholdRmsPremiumOutbound: "10",
    thresholdStorage: "10",
    thresholdDataWarehouseCloudUser: undefined,
  },
  status: TenantStatus.ACTIVE,
  uuid: "",
  versionUuid: "",
  useCloudId: false,
} as ITenantInformation;

describe("service/provisioning/ProvisioningInformationProvider.ts", function () {
  let context: RequestContext;
  let sandbox: sinon.SinonSandbox;

  let TenantInformationProviderStub: sinon.SinonStub;
  let FeatureFlagProviderStub: sinon.SinonStub;
  let CustomerHanaRuntimeDataStub: sinon.SinonStub;

  before(function () {
    context = RequestContext.createFromTenantId(testTenantUuid);
    sandbox = sinon.createSandbox();
  });

  beforeEach(function () {
    TenantInformationProviderStub = sandbox
      .stub(TenantInformationProvider, "getTenantInformation")
      .resolves(tenantInfo);
    FeatureFlagProviderStub = sandbox.stub(FeatureFlagProvider, "getFeatureFlags").returns(Promise.resolve() as any);
    CustomerHanaRuntimeDataStub = sandbox
      .stub(CustomerHanaRuntimeData.prototype, "getOverallAssignedStorage")
      .resolves();
  });

  afterEach(function () {
    sandbox.restore();
    sinon.resetHistory();
  });

  it("Should get all services", async () => {
    await ProvisioningInformationProvider.fromContext(context);

    sinon.assert.called(TenantInformationProviderStub);
    sinon.assert.called(FeatureFlagProviderStub);
    sinon.assert.notCalled(CustomerHanaRuntimeDataStub);
  });

  it("Should call customer hana to get spaces overview", async () => {
    const infoProvider = await ProvisioningInformationProvider.fromContext(context);
    infoProvider.getLazySpacesOverview();
    sinon.assert.called(CustomerHanaRuntimeDataStub);
  });

  describe("filterLicenses()", async () => {
    it("Should return values if included values are set", async () => {
      TenantInformationProviderStub.resolves(tenantInfo);

      const provInformationProvider = await ProvisioningInformationProvider.fromContext(context);
      const definedLicenses = provInformationProvider.license;
      sinon.assert.match(definedLicenses, {
        thresholdBWBridge1: "10",
        thresholdCatalogConcurrency: "10",
        thresholdCatalogNodeHours: "10",
        thresholdCatalogStorage: "10",
        thresholdMemory: "10",
        thresholdVCPU: "10",
        thresholdDWCCU: "10",
        thresholdDataLakeStorage: "10",
        thresholdDataLakeCompute: "10",
        thresholdRmsConcurrency: "10",
        thresholdRmsNodeHours: "10",
        thresholdRmsPremiumOutbound: "10",
        thresholdStorage: "10",
        thresholdRmsNodeHoursIncluded: "100",
        thresholdRmsConcurrencyIncluded: "1",
        thresholdCatalogStorageIncluded: "256",
        thresholdCatalogNodeHoursIncluded: "50",
        thresholdCatalogConcurrencyIncluded: "1",
      });
    });

    it("return zeros if non values are not set for licenses, included licenses should return the fallback values", async () => {
      tenantInfo.license = {};

      TenantInformationProviderStub.resolves(tenantInfo);

      const provInformationProvider = await ProvisioningInformationProvider.fromContext(context);
      const definedLicenses = provInformationProvider.license;
      sinon.assert.match(definedLicenses, {
        thresholdBWBridge1: "0",
        thresholdCatalogConcurrency: "0",
        thresholdCatalogNodeHours: "0",
        thresholdCatalogStorage: "0",
        thresholdMemory: "0",
        thresholdVCPU: "0",
        thresholdDWCCU: "0",
        thresholdDataLakeStorage: "0",
        thresholdDataLakeCompute: "0",
        thresholdRmsConcurrency: "0",
        thresholdRmsNodeHours: "0",
        thresholdRmsPremiumOutbound: "0",
        thresholdStorage: "0",
        thresholdRmsNodeHoursIncluded: "100",
        thresholdRmsConcurrencyIncluded: "1",
        thresholdCatalogStorageIncluded: "256",
        thresholdCatalogNodeHoursIncluded: "50",
        thresholdCatalogConcurrencyIncluded: "1",
      });
    });

    it("return zeros if non values are not set for licenses, included licenses (if zeros) should return the fallback values", async () => {
      tenantInfo.license = {
        thresholdCatalogConcurrencyIncluded: "0",
        thresholdCatalogNodeHoursIncluded: "0",
        thresholdCatalogStorageIncluded: "0",
        thresholdRmsConcurrencyIncluded: "0",
        thresholdRmsNodeHoursIncluded: "0",
      };

      TenantInformationProviderStub.resolves(tenantInfo);

      const provInformationProvider = await ProvisioningInformationProvider.fromContext(context);
      const definedLicenses = provInformationProvider.license;
      sinon.assert.match(definedLicenses, {
        thresholdBWBridge1: "0",
        thresholdCatalogConcurrency: "0",
        thresholdCatalogNodeHours: "0",
        thresholdCatalogStorage: "0",
        thresholdMemory: "0",
        thresholdVCPU: "0",
        thresholdDWCCU: "0",
        thresholdDataLakeStorage: "0",
        thresholdDataLakeCompute: "0",
        thresholdRmsConcurrency: "0",
        thresholdRmsNodeHours: "0",
        thresholdRmsPremiumOutbound: "0",
        thresholdStorage: "0",
        thresholdRmsNodeHoursIncluded: "100",
        thresholdRmsConcurrencyIncluded: "1",
        thresholdCatalogStorageIncluded: "256",
        thresholdCatalogNodeHoursIncluded: "50",
        thresholdCatalogConcurrencyIncluded: "1",
      });
    });

    it("return zeros  as int if non values are not set for licenses, included licenses (if zeros) should return the fallback values", async () => {
      tenantInfo.license = {
        thresholdCatalogConcurrencyIncluded: "0",
        thresholdCatalogNodeHoursIncluded: "0",
        thresholdCatalogStorageIncluded: "0",
        thresholdRmsConcurrencyIncluded: "0",
        thresholdRmsNodeHoursIncluded: "0",
      };

      TenantInformationProviderStub.resolves(tenantInfo);

      const provInformationProvider = await ProvisioningInformationProvider.fromContext(context);
      const definedLicenses = provInformationProvider.licenseAsInteger;
      sinon.assert.match(definedLicenses, {
        thresholdBWBridge1: 0,
        thresholdCatalogConcurrency: 0,
        thresholdCatalogNodeHours: 0,
        thresholdCatalogStorage: 0,
        thresholdMemory: 0,
        thresholdVCPU: 0,
        thresholdDWCCU: 0,
        thresholdDataLakeStorage: 0,
        thresholdDataLakeCompute: 0,
        thresholdRmsConcurrency: 0,
        thresholdRmsNodeHours: 0,
        thresholdRmsPremiumOutbound: 0,
        thresholdStorage: 0,
        thresholdRmsNodeHoursIncluded: 100,
        thresholdRmsConcurrencyIncluded: 1,
        thresholdCatalogStorageIncluded: 256,
        thresholdCatalogNodeHoursIncluded: 50,
        thresholdCatalogConcurrencyIncluded: 1,
      });
    });

    it("should get tenant information with skip cache (call fetchTenantInformation)", async () => {
      const fetchTenantInformationStub = sandbox
        .stub(TenantInformationProvider, "fetchTenantInformation")
        .resolves(tenantInfo);

      tenantInfo.license = {};

      fetchTenantInformationStub.resolves(tenantInfo);

      const provInformationProvider = await ProvisioningInformationProvider.fromContext(context, true);
      const definedLicenses = provInformationProvider.license;

      sinon.assert.called(fetchTenantInformationStub);
      sinon.assert.notCalled(TenantInformationProviderStub);
      sinon.assert.match(definedLicenses, {
        thresholdBWBridge1: "0",
        thresholdCatalogConcurrency: "0",
        thresholdCatalogNodeHours: "0",
        thresholdCatalogStorage: "0",
        thresholdMemory: "0",
        thresholdVCPU: "0",
        thresholdDWCCU: "0",
        thresholdDataLakeStorage: "0",
        thresholdDataLakeCompute: "0",
        thresholdRmsConcurrency: "0",
        thresholdRmsNodeHours: "0",
        thresholdRmsPremiumOutbound: "0",
        thresholdStorage: "0",
        thresholdRmsNodeHoursIncluded: "100",
        thresholdRmsConcurrencyIncluded: "1",
        thresholdCatalogStorageIncluded: "256",
        thresholdCatalogNodeHoursIncluded: "50",
        thresholdCatalogConcurrencyIncluded: "1",
      });
    });

    it("Edge case: should return fallback values for included licenses if classification is DWC", async () => {
      tenantInfo.license = {};
      tenantInfo.classification = TenantClassification.DWC;

      TenantInformationProviderStub.resolves(tenantInfo);

      const provInformationProvider = await ProvisioningInformationProvider.fromContext(context);
      const definedLicenses = provInformationProvider.license;
      sinon.assert.match(definedLicenses, {
        thresholdBWBridge1: "0",
        thresholdCatalogConcurrency: "0",
        thresholdCatalogNodeHours: "0",
        thresholdCatalogStorage: "0",
        thresholdMemory: "0",
        thresholdVCPU: "0",
        thresholdDWCCU: "0",
        thresholdDataLakeStorage: "0",
        thresholdDataLakeCompute: "0",
        thresholdRmsConcurrency: "0",
        thresholdRmsNodeHours: "0",
        thresholdRmsPremiumOutbound: "0",
        thresholdStorage: "0",
        thresholdRmsNodeHoursIncluded: "200",
        thresholdRmsConcurrencyIncluded: "2",
        thresholdCatalogStorageIncluded: "512",
        thresholdCatalogNodeHoursIncluded: "100",
        thresholdCatalogConcurrencyIncluded: "1",
      });
    });

    it("Edge case: should return fallback values for included licenses if classification is DWC and is freeTier", async () => {
      tenantInfo.license = {};
      tenantInfo.classification = TenantClassification.DWC;
      tenantInfo.freeTier = true;

      TenantInformationProviderStub.resolves(tenantInfo);

      const provInformationProvider = await ProvisioningInformationProvider.fromContext(context);
      const definedLicenses = provInformationProvider.license;
      sinon.assert.match(definedLicenses, {
        thresholdBWBridge1: "0",
        thresholdCatalogConcurrency: "0",
        thresholdCatalogNodeHours: "0",
        thresholdCatalogStorage: "0",
        thresholdMemory: "0",
        thresholdVCPU: "0",
        thresholdDWCCU: "0",
        thresholdDataLakeStorage: "0",
        thresholdDataLakeCompute: "0",
        thresholdRmsConcurrency: "0",
        thresholdRmsNodeHours: "0",
        thresholdRmsPremiumOutbound: "0",
        thresholdStorage: "0",
        thresholdRmsNodeHoursIncluded: "200",
        thresholdRmsConcurrencyIncluded: "2",
        thresholdCatalogStorageIncluded: "512",
        thresholdCatalogNodeHoursIncluded: "100",
        thresholdCatalogConcurrencyIncluded: "1",
      });
    });

    it("Edge case: should return fallback values for included licenses if classification is DWCCPEA and is freeTier", async () => {
      tenantInfo.license = {};
      tenantInfo.classification = TenantClassification.DWCCPEA;
      tenantInfo.freeTier = true;

      TenantInformationProviderStub.resolves(tenantInfo);

      const provInformationProvider = await ProvisioningInformationProvider.fromContext(context);
      const definedLicenses = provInformationProvider.license;
      sinon.assert.match(definedLicenses, {
        thresholdBWBridge1: "0",
        thresholdCatalogConcurrency: "0",
        thresholdCatalogNodeHours: "0",
        thresholdCatalogStorage: "0",
        thresholdMemory: "0",
        thresholdVCPU: "0",
        thresholdDWCCU: "0",
        thresholdDataLakeStorage: "0",
        thresholdDataLakeCompute: "0",
        thresholdRmsConcurrency: "0",
        thresholdRmsNodeHours: "0",
        thresholdRmsPremiumOutbound: "0",
        thresholdStorage: "0",
        thresholdRmsNodeHoursIncluded: "100",
        thresholdRmsConcurrencyIncluded: "1",
        thresholdCatalogStorageIncluded: "256",
        thresholdCatalogNodeHoursIncluded: "50",
        thresholdCatalogConcurrencyIncluded: "1",
      });
    });
    it("Edge case: should throw error if no tenant information is found", async () => {
      const tenantInfoUndefined = undefined;

      TenantInformationProviderStub.resolves(tenantInfoUndefined);

      await expect(ProvisioningInformationProvider.fromContext(context)).to.eventually.be.rejected;
    });
    it("Edge case: should not throw if tenant information has no license", async () => {
      const tenantInfoNoLicense = {
        ...tenantInfo,
        license: undefined,
      };
      TenantInformationProviderStub.resolves(tenantInfoNoLicense);

      await expect(ProvisioningInformationProvider.fromContext(context)).to.not.be.rejected;
    });
  });
});
