/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import * as CrunInstrumentation from "@sap/xotel-agent-ext-js/dist/clientlib/api/CrunInstrumentation";
import chai, { expect } from "chai";
import sinon from "sinon";
import { _getLogger, CalmJobPublishingService } from "../../../calm/jm/CalmJobPublishingService";
import * as CalmJmUtils from "../../../calm/jm/utils";
import { RequestContext } from "../../../repository/security/requestContext";
import { ITaskLogsResponse, TaskLogsApi } from "../../../task/logger/controllers/TaskLogsApi";
import { Status } from "../../../task/logger/models";
import { Activity, ApplicationId } from "../../../task/models";
import { ObserverResponse } from "../../../task/observer/Observer";
import type { ITask } from "../../../task/orchestrator/models/ITask";
chai.use(require("chai-as-promised"));

describe("CalmJobPublishingService", () => {
  let origServiceType;

  beforeEach(() => {
    origServiceType = process.env.SAP_CALM_SERVICE_TYPE;
  });

  afterEach(() => {
    process.env.SAP_CALM_SERVICE_TYPE = origServiceType;
    sinon.restore();
  });

  describe("sendTaskStartData", () => {
    let mockLogId: number;
    let mockRunId: string | undefined;
    let mockStartTime: Date;
    let mockTask: ITask;
    let mockContext: IRequestContext;
    let stubLogError;
    let stubSendCrunContent;

    beforeEach(() => {
      process.env.SAP_CALM_SERVICE_TYPE = "mockServiceType";
      mockLogId = 123456;
      mockRunId = "mockRunId";
      mockStartTime = new Date();
      mockContext = { tenantId: "mockTenantId" } as RequestContext;
      mockTask = {
        scheduleId: "mockScheduleId",
        applicationId: ApplicationId.TRANSFORMATION_FLOWS,
        activity: Activity.EXECUTE,
        objectId: "mockObjectId",
        spaceId: "mockSpaceId",
      } as ITask;

      stubLogError = sinon.stub(_getLogger(), "logError");
      stubSendCrunContent = sinon.stub(CrunInstrumentation, "sendCrunContent").resolves();
    });

    it("sends job data to calm", async () => {
      await CalmJobPublishingService.sendTaskStartData(mockLogId, mockRunId, mockStartTime, mockTask, mockContext);

      expect(stubSendCrunContent.called).to.be.true;
      const jobData = stubSendCrunContent.firstCall.args[0];
      expect(jobData.getTenantId()).to.equal(mockContext.tenantId);
      expect(jobData.containsData()).to.be.true;
    });

    it("sends run data to calm", async () => {
      await CalmJobPublishingService.sendTaskStartData(mockLogId, mockRunId, mockStartTime, mockTask, mockContext);

      expect(stubSendCrunContent.called).to.be.true;
      const runData = stubSendCrunContent.secondCall.args[0];
      expect(runData.getTenantId()).to.equal(mockContext.tenantId);
      expect(runData.payload.length > 0).to.be.true;
      expect(stubSendCrunContent.secondCall.args[1]).to.equal("mockServiceType");
      expect(stubSendCrunContent.secondCall.args[2]).to.equal("mockTenantId");
    });

    it("logs error when data push fails", async () => {
      stubSendCrunContent.throws();
      await CalmJobPublishingService.sendTaskStartData(mockLogId, mockRunId, mockStartTime, mockTask, mockContext);
      expect(stubLogError.called).to.be.true;
    });
  });

  describe("sendTaskEndData", () => {
    let mockContext: IRequestContext;
    let mockTasks: ObserverResponse[];
    let stubLogError;
    let stubSendCrunContent;

    beforeEach(() => {
      process.env.SAP_CALM_SERVICE_TYPE = "mockServiceType";
      mockContext = { tenantId: "mockTenantId" } as RequestContext;
      mockTasks = [
        {
          status: Status.COMPLETED,
          startTime: new Date(),
          endTime: new Date(),
          user: "mockUser",
          runId: "mockRunId",
          logId: 123456,
          creationTime: new Date(),
          scheduleId: "mockScheduleId",
          applicationId: ApplicationId.TRANSFORMATION_FLOWS,
          activity: Activity.EXECUTE,
          spaceId: "mockSpaceId",
          objectId: "mockObjectId",
        },
      ];

      stubLogError = sinon.stub(_getLogger(), "logError");
      stubSendCrunContent = sinon.stub(CrunInstrumentation, "sendCrunContent").resolves();
    });

    it("sends run data to calm", async () => {
      await CalmJobPublishingService.sendTaskEndData(mockTasks, mockContext);

      expect(stubSendCrunContent.calledOnce).to.be.true;
      const runData = stubSendCrunContent.firstCall.args[0];
      expect(runData.getTenantId()).to.equal(mockContext.tenantId);
      expect(runData.containsData()).to.be.true;
    });

    it("logs error when data push fails", async () => {
      stubSendCrunContent.throws();
      await CalmJobPublishingService.sendTaskEndData(mockTasks, mockContext);
      expect(stubLogError.called).to.be.true;
    });
  });

  describe("sendTaskRunDataByLogId", () => {
    it("sends run data to calm", async () => {
      const mockLogId = 123456;
      const mockSpaceId = "mockSpaceId";
      const mockContext: IRequestContext = { tenantId: "mockTenantId" } as RequestContext;
      sinon.stub(CalmJmUtils, "isValidJobSpaceId").returns(true);
      const stubSendCrunContent = sinon.stub(CrunInstrumentation, "sendCrunContent").resolves();
      sinon.stub(TaskLogsApi.prototype, "getLogHeaders").resolves({
        locks: [],
        logs: [
          {
            logId: mockLogId,
            spaceId: mockSpaceId,
            objectId: "mockObjectId",
            applicationId: ApplicationId.TRANSFORMATION_FLOWS,
            activity: Activity.EXECUTE,
            scheduleId: "mockScheduleId",
            runId: "mockRunId",
            status: Status.COMPLETED,
            startTime: new Date(),
            endTime: new Date(),
          },
        ],
      } as ITaskLogsResponse);

      await CalmJobPublishingService.sendTaskRunDataByLogId(mockLogId, mockSpaceId, mockContext);

      expect(stubSendCrunContent.calledOnce).to.be.true;
      const runData = stubSendCrunContent.firstCall.args[0];
      expect(runData.getTenantId()).to.equal(mockContext.tenantId);
      expect(runData.containsData()).to.be.true;
      expect(stubSendCrunContent.firstCall.args[1]).to.equal(process.env.SAP_CALM_SERVICE_TYPE);
      expect(stubSendCrunContent.firstCall.args[2]).to.equal(mockContext.tenantId);
    });

    it("logs error if no logs are found", async () => {
      const mockLogId = 123456;
      const stubLogError = sinon.stub(_getLogger(), "logError");
      sinon.stub(CalmJmUtils, "isValidJobSpaceId").returns(true);
      sinon.stub(TaskLogsApi.prototype, "getLogHeaders").resolves({
        locks: [],
        logs: [],
      });

      await CalmJobPublishingService.sendTaskRunDataByLogId(mockLogId, "mockSpaceId", {
        tenantId: "mockTenantId",
      } as RequestContext);

      expect(stubLogError.calledOnce).to.be.true;
      expect(stubLogError.firstCall.args[0][1].message).to.equal(
        `No task log found. Expected task log to exist for logId: ${mockLogId}`
      );
    });

    it("logs error if push to calm fails", async () => {
      const mockLogId = 123456;
      const mockSpaceId = "mockSpaceId";
      const stubLogError = sinon.stub(_getLogger(), "logError");
      sinon.stub(CalmJmUtils, "isValidJobSpaceId").returns(true);
      const mockError = new Error("sendCrunContent error");
      sinon.stub(CrunInstrumentation, "sendCrunContent").throws(mockError);
      sinon.stub(TaskLogsApi.prototype, "getLogHeaders").resolves({
        locks: [],
        logs: [
          {
            logId: mockLogId,
            spaceId: mockSpaceId,
            objectId: "mockObjectId",
            applicationId: ApplicationId.TRANSFORMATION_FLOWS,
            activity: Activity.EXECUTE,
            scheduleId: "mockScheduleId",
            runId: "mockRunId",
            status: Status.COMPLETED,
            startTime: new Date(),
            endTime: new Date(),
          },
        ],
      } as ITaskLogsResponse);

      await CalmJobPublishingService.sendTaskRunDataByLogId(mockLogId, mockSpaceId, {
        tenantId: "mockTenantId",
      } as RequestContext);

      expect(stubLogError.calledOnce).to.be.true;
      expect(stubLogError.firstCall.args[0][0]).to.equal(
        `[CalmJobPublishingService.sendTaskRunDataByLogId] Error sending task run data to CALM for logId: ${mockLogId}`
      );
      expect(stubLogError.firstCall.args[0][1]).to.equal(mockError);
    });

    it("returns early if spaceId is invalid", async () => {
      const stubIsValidSpaceId = sinon.stub(CalmJmUtils, "isValidJobSpaceId").returns(false);
      const stubGetLogHeaders = sinon.stub(TaskLogsApi.prototype, "getLogHeaders").resolves();
      const stubSendCrunContent = sinon.stub(CrunInstrumentation, "sendCrunContent").resolves();

      await CalmJobPublishingService.sendTaskRunDataByLogId(123456, "mockSpaceId", {} as RequestContext);

      expect(stubIsValidSpaceId.calledOnce).to.be.true;
      expect(stubGetLogHeaders.called).to.be.false;
      expect(stubSendCrunContent.called).to.be.false;
    });
  });
});
