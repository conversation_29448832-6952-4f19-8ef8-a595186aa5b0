/** Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved.
 * @format
 */
import { ObjectKind } from "@sap/deepsea-types";
import { RepositoryObject } from "@sap/deepsea-utils";
import * as assert from "assert";
import chai, { expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import { Router } from "express";
import sinon from "sinon";
import { ccmMockServer } from "../../../../deploy/mocks/ccmMock";
import { SpaceType } from "../../../../shared/spaces/types";
import { ApiConnectionService } from "../../../connections/api/apiConnectionService";
import { CCMClient } from "../../../connections/ccm/CCMClient";
import { CCMEndpoints, CCMReplicationStatus } from "../../../connections/ccm/CCMConstants";
import { buildAndCheckCCMTechnicalName } from "../../../connections/ccm/Utils";
import { ConnectionSharingUtils } from "../../../connections/connectionSharing/ConnectionSharingUtils";
import * as SDIManager from "../../../connections/datasources/sdi";
import * as Remotes from "../../../connections/remotes";
import { RealTimeReplicationStatus, RemoteSourceAdapters, TypeIds } from "../../../connections/utils/Constants";
import * as ConnectionUtils from "../../../connections/utils/connectionUtils";
import * as RemoteManager from "../../../datasources/remote";
import { registerApiImplementation } from "../../../repository/api/spaceApi";
import { RepositoryObjectClient } from "../../../repository/client/repositoryObjectClient";
import { RequestContext } from "../../../repository/security/requestContext";
import * as Space from "../../../repository/spaces";
import { CustomerHana, IGetClientOptions } from "../../../reuseComponents/spaces/src";
import * as DeleteSpace from "../../../reuseComponents/spaces/src/deleteSpace";
import { toAsyncRouter } from "../../../routes/async-router";
import * as Users from "../../../routes/space/user";
import { CodedError } from "../../../server/errorResponse";
import * as RepoTestUtility from "../../repository/repository.utility";
import { getCCMLike_HDLFiles } from "../model/data/HDLFilesPayloads";
import { getCCMLike_S3 } from "../model/data/S3Payloads";
import { getCPEMDataOAuthClientCred } from "./data/cpem.data";
import { getLOBDataOAuthClientCred } from "./data/eventingestion.data";
import { getSAPS4HANACLOUD_WSRFC_CDI_DPAGENT } from "./data/s4hanacloud.data";
import { getSAPBWBrdigeData } from "./data/sapbwbridge.data";
import { assertAPIConnDeepEqual } from "./utils";

chai.use(chaiAsPromised);

registerApiImplementation(toAsyncRouter(Router()));

let context: RequestContext;
let spaceId: string;
let getSpaceTypeFromNameStub: sinon.SinonStub;
let getRemoteSourcesDetailedStub: sinon.SinonStub;
let featureFlags: any;

function getRemoteSourceDetailsMock(connDefn: any, spaceId: string, adapterName: string) {
  return {
    name: `DWC_${spaceId}.${connDefn.name}`,
    adapter: adapterName,
    location: "location",
    agentName: "agentName",
    agentStatus: "connected",
    realtimeReplicationStatus: RealTimeReplicationStatus.INACTIVE,
  } as SDIManager.IRemoteSource;
}

describe("service/connections/api/apiConnectionService", function () {
  this.timeout(120000);
  const preparations: RepoTestUtility.IPreparation[] = [];
  let preparation: RepoTestUtility.IPreparation;
  before(async function () {
    context = RepoTestUtility.createDWCAdministratorContext();
    // Mock CCM Connections Service
    await ccmMockServer.start();
    ccmMockServer.stubProxyService();

    RepoTestUtility.prepareStubs(context, { skipCustomerHanaStub: true });
    RepoTestUtility.addStub(RemoteManager, "grantSpaceUserToRemote", {});
    RepoTestUtility.addStub(RemoteManager, "revokeSpaceUserFromRemote", {});
    RepoTestUtility.addStub(RemoteManager, "grantSpaceSupportRoleToRemote", {});
    RepoTestUtility.addStub(RemoteManager, "revokeSpaceSupportRoleFromRemote", {});
    RepoTestUtility.addStub(SDIManager, "upsertRemoteSource", {});
    RepoTestUtility.addStub(SDIManager, "deleteRemoteSource", {});
    RepoTestUtility.addStub(SDIManager, "getRemoteSourcesDetailed", []);
    RepoTestUtility.addStub(ConnectionUtils, "setAdapterLocation", {});
    RepoTestUtility.addStub(Users, "getSpaceUsers", null);
    RepoTestUtility.addStub(DeleteSpace, "deleteTeamInSacRepo", null);
    RepoTestUtility.addStub(DeleteSpace, "deleteRuntimeSpace", null);

    preparation = await RepoTestUtility.prepareSpace(preparations, context);
    spaceId = preparation.spaceId;

    // stub getTenantManager and let it return a result
    const customerHanaReplacement = RepoTestUtility.getFromRequestContextStub(
      context,
      undefined
    ) as unknown as CustomerHana;
    // eslint-disable-next-line @typescript-eslint/unbound-method
    customerHanaReplacement.getTenantManager = async (opt: IGetClientOptions | undefined) =>
      await Promise.resolve({ host: "191923820329Id.url.com", user: "tenantUser" } as IDbConfig);
    customerHanaReplacement.getSpaceUserName = async (
      spaceId: string,
      spaceRole: string,
      options?: IGetClientOptions
    ) => await Promise.resolve("tenantUser");
    RepoTestUtility.addStub(CustomerHana, "fromRequestContext").resolves(customerHanaReplacement);
    RepoTestUtility.addStub(Space, "getSpaceUuidFromName").returns(preparation.spaceUuid as any);
  });

  after(async function () {
    await RepoTestUtility.deleteSpaces(preparations);
    RepoTestUtility.restoreStubs();
    await context.finish();
    await ccmMockServer.stop();
    ccmMockServer.restoreProxyService();
  });

  beforeEach(async function () {
    featureFlags = {
      DWCO_CONNECTION_EVENT_MESH: true,
      INFRA_DWC_TWO_TENANT_MODE: true,
    };
    await RepoTestUtility.enforceFeatureFlags(featureFlags);
    getRemoteSourcesDetailedStub = RepoTestUtility.addStub(SDIManager, "getRemoteSourcesDetailed", []);
    // For regular spaces, the space type is undefined. Specific space types for special spaces has been stubbed it their respective tests.
    getSpaceTypeFromNameStub = RepoTestUtility.addStub(Space, "getSpaceTypeFromName").returns(
      Promise.resolve(undefined)
    );
    RepoTestUtility.addStub(ConnectionSharingUtils.prototype, "waitForConnectionDeployed").resolves();
  });

  afterEach(async function () {
    await ccmMockServer.resetMockStatus(context.tenantId!);
  });

  it("Should be able to create/get Ariba connection via CCM like API", async function () {
    const api = new ApiConnectionService(context);
    const connInputHDL_Files = await getCCMLike_HDLFiles();
    const connInputCPEM = getCPEMDataOAuthClientCred();
    const connInput = getLOBDataOAuthClientCred(TypeIds.SAPARIBA_EVENT_INGESTION, "Ariba");
    const conn = {
      [connInputHDL_Files.technicalName]: connInputHDL_Files,
      [connInput.technicalName]: connInput,
      [connInputCPEM.technicalName]: connInputCPEM,
    };
    await api.upsertConnections(conn, spaceId);
    const connOutput = (await api.getConnections(spaceId))[connInput.technicalName];
    expect(connOutput.endpoints[0].credentials[0].properties.oauth2TokenEndpoint).not.to.be.undefined;
    expect(connOutput.endpoints[0].credentials[0].properties.oauth2ClientSecret).to.be.undefined;
    expect(connOutput.endpoints[0].credentials[0].properties.oauth2ClientId).to.be.undefined;
    assertAPIConnDeepEqual(connInput, connOutput);

    const query = {
      space_ids: [preparation.spaceUuid],
      filters: `name:${connInput.technicalName}|${connInputCPEM.technicalName}|${connInputHDL_Files.technicalName}`,
      details: [
        "id",
        "content",
        "configuration",
        "capabilityHanaSdi",
        "capabilityDataflowSource",
        "capabilityDataflowTarget",
        "capabilityReplicationflowSource",
        "capabilityReplicationflowTarget",
        "capabilityModelTransfer",
        "capabilityPartnerSchema",
        "disReplicationStatus",
        "typeId",
        "disConnectionId",
      ],
    };

    // Retrieve Connection
    const connGetResult = await Remotes.getConnections(context, query);
    expect(connGetResult.length).to.eq(3);
    const dwcConnGetObj: any = connGetResult.find((e) => e.name === connInput.technicalName);
    const configuration = JSON.parse(dwcConnGetObj.configuration);
    const configurations = configuration.ConnectionProperties.configurations;
    assert.strictEqual(dwcConnGetObj.name, connInput.technicalName);
    assert.strictEqual(configurations.url, "https://endpoint");
    assert.strictEqual(configurations.sourceId, "sourceId");
    assert.strictEqual(dwcConnGetObj.capabilityHanaSdi, "false");
    assert.strictEqual(dwcConnGetObj.capabilityDataflowSource, "false");
    assert.strictEqual(dwcConnGetObj.capabilityModelTransfer, "false");
    assert.strictEqual(dwcConnGetObj.capabilityPartnerSchema, "false");
    assert.strictEqual(dwcConnGetObj.capabilityReplicationflowSource, "true");
    assert.strictEqual(dwcConnGetObj.disReplicationStatus, CCMReplicationStatus.SUCCESSFULLY_REPLICATED);
    assert.strictEqual(dwcConnGetObj.typeId, connInput.typeId);
    assert.strictEqual(dwcConnGetObj.disConnectionId.includes("-"), true);

    await Remotes.deleteConnections(
      context,
      preparation.spaceUuid,
      connGetResult.map((conn) => conn.id)
    );
  });

  it("Should be able to create/get Concur connection via CCM like API", async function () {
    const api = new ApiConnectionService(context);
    const connInputS3 = await getCCMLike_S3();
    const connInputCPEM = getCPEMDataOAuthClientCred();
    const conn = {
      [connInputS3.technicalName]: connInputS3,
      [connInputCPEM.technicalName]: connInputCPEM,
    };
    await api.upsertConnections(conn, spaceId);
    const connInput = getLOBDataOAuthClientCred(TypeIds.SAPCONCUR_EVENT_INGESTION, "Concur");
    await api.upsertConnections(
      {
        [connInput.technicalName]: connInput,
      },
      spaceId
    );
    const connOutput = (await api.getConnections(spaceId))[connInput.technicalName];
    expect(connOutput.endpoints[0].credentials[0].properties.oauth2TokenEndpoint).not.to.be.undefined;
    expect(connOutput.endpoints[0].credentials[0].properties.oauth2ClientSecret).to.be.undefined;
    expect(connOutput.endpoints[0].credentials[0].properties.oauth2ClientId).to.be.undefined;
    assertAPIConnDeepEqual(connInput, connOutput);

    const query = {
      space_ids: [preparation.spaceUuid],
      filters: `name:${connInput.technicalName}|${connInputCPEM.technicalName}|${connInputS3.technicalName}`,
      details: [
        "id",
        "content",
        "configuration",
        "capabilityHanaSdi",
        "capabilityDataflowSource",
        "capabilityDataflowTarget",
        "capabilityReplicationflowSource",
        "capabilityReplicationflowTarget",
        "capabilityModelTransfer",
        "capabilityPartnerSchema",
        "disReplicationStatus",
        "typeId",
        "disConnectionId",
      ],
    };

    // Retrieve Connection
    const connGetResult = await Remotes.getConnections(context, query);
    expect(connGetResult.length).to.eq(3);
    const dwcConnGetObj: any = connGetResult.find((e) => e.name === connInput.technicalName);
    const configuration = JSON.parse(dwcConnGetObj.configuration);
    const configurations = configuration.ConnectionProperties.configurations;
    assert.strictEqual(dwcConnGetObj.name, connInput.technicalName);
    assert.strictEqual(configurations.url, "https://endpoint");
    assert.strictEqual(configurations.sourceId, "sourceId");
    assert.strictEqual(dwcConnGetObj.capabilityHanaSdi, "false");
    assert.strictEqual(dwcConnGetObj.capabilityDataflowSource, "false");
    assert.strictEqual(dwcConnGetObj.capabilityModelTransfer, "false");
    assert.strictEqual(dwcConnGetObj.capabilityPartnerSchema, "false");
    assert.strictEqual(dwcConnGetObj.capabilityReplicationflowSource, "true");
    assert.strictEqual(dwcConnGetObj.disReplicationStatus, CCMReplicationStatus.SUCCESSFULLY_REPLICATED);
    assert.strictEqual(dwcConnGetObj.typeId, connInput.typeId);
    assert.strictEqual(dwcConnGetObj.disConnectionId.includes("-"), true);

    await Remotes.deleteConnections(
      context,
      preparation.spaceUuid,
      connGetResult.map((conn) => conn.id)
    );
  });

  it("Should be able to create/get Fieldglass connection via CCM like API", async function () {
    const api = new ApiConnectionService(context);
    const connInputS3 = await getCCMLike_S3();
    const connInputCPEM = getCPEMDataOAuthClientCred();
    const conn = {
      [connInputS3.technicalName]: connInputS3,
      [connInputCPEM.technicalName]: connInputCPEM,
    };
    await api.upsertConnections(conn, spaceId);
    const connInput = getLOBDataOAuthClientCred(TypeIds.SAPFIELDGLASS_EVENT_INGESTION, "Fieldglass");
    await api.upsertConnections(
      {
        [connInput.technicalName]: connInput,
      },
      spaceId
    );
    const connOutput = (await api.getConnections(spaceId))[connInput.technicalName];
    expect(connOutput.endpoints[0].credentials[0].properties.oauth2TokenEndpoint).not.to.be.undefined;
    expect(connOutput.endpoints[0].credentials[0].properties.oauth2ClientSecret).to.be.undefined;
    expect(connOutput.endpoints[0].credentials[0].properties.oauth2ClientId).to.be.undefined;
    assertAPIConnDeepEqual(connInput, connOutput);

    const query = {
      space_ids: [preparation.spaceUuid],
      filters: `name:${connInput.technicalName}|${connInputCPEM.technicalName}|${connInputS3.technicalName}`,
      details: [
        "id",
        "content",
        "configuration",
        "capabilityHanaSdi",
        "capabilityDataflowSource",
        "capabilityDataflowTarget",
        "capabilityReplicationflowSource",
        "capabilityReplicationflowTarget",
        "capabilityModelTransfer",
        "capabilityPartnerSchema",
        "disReplicationStatus",
        "typeId",
        "disConnectionId",
      ],
    };

    // Retrieve Connection
    const connGetResult = await Remotes.getConnections(context, query);
    expect(connGetResult.length).to.eq(3);
    const dwcConnGetObj: any = connGetResult.find((e) => e.name === connInput.technicalName);
    const configuration = JSON.parse(dwcConnGetObj.configuration);
    const configurations = configuration.ConnectionProperties.configurations;
    assert.strictEqual(dwcConnGetObj.name, connInput.technicalName);
    assert.strictEqual(configurations.url, "https://endpoint");
    assert.strictEqual(configurations.sourceId, "sourceId");
    assert.strictEqual(dwcConnGetObj.capabilityHanaSdi, "false");
    assert.strictEqual(dwcConnGetObj.capabilityDataflowSource, "false");
    assert.strictEqual(dwcConnGetObj.capabilityModelTransfer, "false");
    assert.strictEqual(dwcConnGetObj.capabilityPartnerSchema, "false");
    assert.strictEqual(dwcConnGetObj.capabilityReplicationflowSource, "true");
    assert.strictEqual(dwcConnGetObj.disReplicationStatus, CCMReplicationStatus.SUCCESSFULLY_REPLICATED);
    assert.strictEqual(dwcConnGetObj.typeId, connInput.typeId);
    assert.strictEqual(dwcConnGetObj.disConnectionId.includes("-"), true);

    await Remotes.deleteConnections(
      context,
      preparation.spaceUuid,
      connGetResult.map((conn) => conn.id)
    );
  });

  describe("SAPS4HANACLOUD", () => {
    it("Should be able to create SAPS4HANACLOUD connection via CCM like API", async function () {
      const connInput = getSAPS4HANACLOUD_WSRFC_CDI_DPAGENT();
      connInput.technicalName = "S4HanaCloudWithAgentConn";
      RepoTestUtility.addStub(SDIManager, "getRemoteSourcesDetailed", [
        {
          name: `DWC_${spaceId}.${connInput.technicalName}`,
          location: "agent",
          agentName: "dwc_managed_dp_agent",
        },
      ]);
      const conn = {
        [connInput.technicalName]: connInput,
      };
      const api = new ApiConnectionService(context);
      await api.upsertConnections(conn, spaceId);

      const query = {
        space_ids: [preparation.spaceUuid],
        filters: `name:${connInput.technicalName}`,
        details: [
          "content",
          "configuration",
          "capabilityHanaSdi",
          "capabilityDataflowSource",
          "capabilityReplicationflowSource",
          "capabilityModelTransfer",
          "capabilityPartnerSchema",
          "disReplicationStatus",
          "typeId",
          "disConnectionId",
        ],
      };

      // Retrieve Connection
      const connGetResult = await Remotes.getConnections(context, query);
      expect(connGetResult.length).to.eq(1);
      const dwcConnGetObj: any = connGetResult[0];
      const configuration = JSON.parse(dwcConnGetObj.configuration);
      const configurations = configuration.ConnectionProperties.ConnectionInfo;
      assert.strictEqual(dwcConnGetObj.name, connInput.technicalName);
      assert.strictEqual(configurations.host, "localhost");
      assert.strictEqual(dwcConnGetObj.capabilityHanaSdi, "true");
      assert.strictEqual(dwcConnGetObj.capabilityDataflowSource, "true");
      assert.strictEqual(dwcConnGetObj.capabilityReplicationflowSource, "true");
      assert.strictEqual(dwcConnGetObj.capabilityModelTransfer, "true");
      assert.strictEqual(dwcConnGetObj.capabilityPartnerSchema, "false");
      assert.strictEqual(dwcConnGetObj.disReplicationStatus, CCMReplicationStatus.SUCCESSFULLY_REPLICATED);
      assert.strictEqual(dwcConnGetObj.typeId, connInput.typeId);
      assert.strictEqual(dwcConnGetObj.disConnectionId.includes("-"), true);

      await Remotes.deleteConnections(context, preparation.spaceUuid, [dwcConnGetObj.id]);
    });

    it("Should be able to create/get SAPS4HANACLOUD connection via CCM like API with data provisioning option agent", async function () {
      const connInput = getSAPS4HANACLOUD_WSRFC_CDI_DPAGENT();
      connInput.technicalName = "S4HanaCloudWithAgent";
      RepoTestUtility.addStub(SDIManager, "getRemoteSourcesDetailed", [
        {
          name: `DWC_${spaceId}.${connInput.technicalName}`,
          location: "agent",
          agentName: "dwc_managed_dp_agent",
        },
      ]);
      const conn = {
        [connInput.technicalName]: connInput,
      };
      const api = new ApiConnectionService(context);
      await api.upsertConnections(conn, spaceId);
      const connOutput = (await api.getConnections(spaceId))[connInput.technicalName];
      assertAPIConnDeepEqual(connInput, connOutput);

      const ccmConn = await getCCMConnection(connInput.technicalName, spaceId);
      expect(ccmConn.endpoints.length).to.eq(1);
      expect(ccmConn.endpoints[0].runtimeConfigs).to.be.undefined;
      expect(ccmConn.endpoints[0].typeId).to.eq(CCMEndpoints.WSRFC);
    });
  });

  it("Should be able to create/get CPEM connection via CCM like API", async function () {
    const connInput = getCPEMDataOAuthClientCred();
    const conn = {
      [connInput.technicalName]: connInput,
    };
    const api = new ApiConnectionService(context);
    await api.upsertConnections(conn, spaceId);
    const connOutput = (await api.getConnections(spaceId))[connInput.technicalName];
    expect(connOutput.endpoints[0].credentials[0].properties.oauth2TokenEndpoint).not.to.be.undefined;
    expect(connOutput.endpoints[0].credentials[0].properties.oauth2ClientSecret).to.be.undefined;
    expect(connOutput.endpoints[0].credentials[0].properties.oauth2ClientId).to.be.undefined;
    assertAPIConnDeepEqual(connInput, connOutput);

    const query = {
      space_ids: [preparation.spaceUuid],
      filters: `name:${connInput.technicalName}`,
      details: [
        "content",
        "configuration",
        "capabilityHanaSdi",
        "capabilityDataflowSource",
        "capabilityDataflowTarget",
        "capabilityReplicationflowSource",
        "capabilityReplicationflowTarget",
        "capabilityModelTransfer",
        "capabilityPartnerSchema",
        "disReplicationStatus",
        "typeId",
        "disConnectionId",
      ],
    };

    // Retrieve Connection
    const connGetResult = await Remotes.getConnections(context, query);
    expect(connGetResult.length).to.eq(1);
    const dwcConnGetObj: any = connGetResult[0];
    const configuration = JSON.parse(dwcConnGetObj.configuration);
    const configurations = configuration.ConnectionProperties.configurations;
    assert.strictEqual(dwcConnGetObj.name, connInput.technicalName);
    assert.strictEqual(configurations.host, "dummy.sap.hana.ondemand.com");
    assert.strictEqual(dwcConnGetObj.capabilityHanaSdi, "false");
    assert.strictEqual(dwcConnGetObj.capabilityDataflowSource, "false");
    assert.strictEqual(dwcConnGetObj.capabilityModelTransfer, "false");
    assert.strictEqual(dwcConnGetObj.capabilityPartnerSchema, "false");
    assert.strictEqual(dwcConnGetObj.disReplicationStatus, CCMReplicationStatus.SUCCESSFULLY_REPLICATED);
    assert.strictEqual(dwcConnGetObj.typeId, connInput.typeId);
    assert.strictEqual(dwcConnGetObj.disConnectionId.includes("-"), true);

    await Remotes.deleteConnections(context, preparation.spaceUuid, [dwcConnGetObj.id]);
  });

  it("Should be able to create/get SAPBWBRIDGE connection via CCM like API", async function () {
    getSpaceTypeFromNameStub.returns(Promise.resolve(SpaceType.AbapBridge));
    // CCM like connection definition
    const connInput = getSAPBWBrdigeData();
    getRemoteSourcesDetailedStub.returns([
      getRemoteSourceDetailsMock({ name: connInput.technicalName }, spaceId, RemoteSourceAdapters.SDA.HanaOdbc),
    ]);

    const conn = {
      [connInput.technicalName]: connInput,
    };
    const api = new ApiConnectionService(context);
    await api.upsertConnections(conn, spaceId);
    const connOutput = (await api.getConnections(spaceId))[connInput.technicalName];
    assertAPIConnDeepEqual(connInput, connOutput);

    const query = {
      space_ids: [preparation.spaceUuid],
      filters: `name:${connInput.technicalName}`,
      details: [
        "content",
        "configuration",
        "capabilityHanaSdi",
        "capabilityDataflowSource",
        "capabilityDataflowTarget",
        "capabilityReplicationflowSource",
        "capabilityReplicationflowTarget",
        "capabilityModelTransfer",
        "capabilityPartnerSchema",
        "disReplicationStatus",
        "typeId",
        "disConnectionId",
        "realtimeReplicationStatus",
      ],
    };

    // Retrieve Connection
    const connGetResult = await Remotes.getConnections(context, query);
    expect(connGetResult.length).to.eq(1);
    const dwcConnGetObj: any = connGetResult[0];
    const configuration = JSON.parse(dwcConnGetObj.configuration);
    const connectionProps = configuration.ConnectionProperties.connectionproperties;
    assert.strictEqual(dwcConnGetObj.name, connInput.technicalName);
    assert.strictEqual(connectionProps.server, "localhost");
    assert.strictEqual(dwcConnGetObj.capabilityHanaSdi, "true");
    assert.strictEqual(dwcConnGetObj.capabilityDataflowSource, "false");
    assert.strictEqual(dwcConnGetObj.capabilityModelTransfer, "true");
    assert.strictEqual(dwcConnGetObj.capabilityPartnerSchema, "false");
    assert.strictEqual(dwcConnGetObj.realtimeReplicationStatus, RealTimeReplicationStatus.NOTAPPLICABLE);
    assert.strictEqual(dwcConnGetObj.typeId, connInput.typeId);

    await Remotes.deleteConnections(context, preparation.spaceUuid, [dwcConnGetObj.id]);
  });

  it("Should be able to create/get HDLFiles connection via CCM like API", async function () {
    // CCM like connection definition
    const connInput = await getCCMLike_HDLFiles();

    const conn = {
      [connInput.technicalName]: connInput,
    };
    const api = new ApiConnectionService(context);
    await api.upsertConnections(conn, spaceId);
    const connOutput = (await api.getConnections(spaceId))[connInput.technicalName];
    assertAPIConnDeepEqual(connInput, connOutput);

    const query = {
      space_ids: [preparation.spaceUuid],
      filters: `name:${connInput.technicalName}`,
      details: [
        "content",
        "configuration",
        "capabilityHanaSdi",
        "capabilityDataflowSource",
        "capabilityDataflowTarget",
        "capabilityReplicationflowSource",
        "capabilityReplicationflowTarget",
        "capabilityModelTransfer",
        "capabilityPartnerSchema",
        "disReplicationStatus",
        "typeId",
        "disConnectionId",
        "realtimeReplicationStatus",
      ],
    };

    // Retrieve Connection
    const connGetResult = await Remotes.getConnections(context, query);
    expect(connGetResult.length).to.eq(1);
    const dwcConnGetObj: any = connGetResult[0];
    const configuration = JSON.parse(dwcConnGetObj.configuration);
    const connProps = configuration.ConnectionProperties.configurations;
    assert.strictEqual(dwcConnGetObj.name, connInput.technicalName);
    assert.strictEqual(connProps.host, "localhost");
    assert.strictEqual(connProps.rootPath, "/");
    assert.strictEqual(dwcConnGetObj.capabilityHanaSdi, "false");
    assert.strictEqual(dwcConnGetObj.capabilityDataflowSource, "true");
    assert.strictEqual(dwcConnGetObj.capabilityReplicationflowTarget, "true");
    assert.strictEqual(dwcConnGetObj.capabilityModelTransfer, "false");
    assert.strictEqual(dwcConnGetObj.capabilityPartnerSchema, "false");
    assert.strictEqual(dwcConnGetObj.typeId, connInput.typeId);
    assert.strictEqual(dwcConnGetObj.disConnectionId.includes("-"), true);

    await Remotes.deleteConnections(context, preparation.spaceUuid, [dwcConnGetObj.id]);
  });

  it("Should be able to create/get S3 connection via CCM like API", async function () {
    // CCM like connection definition
    const connInput = await getCCMLike_S3();

    const conn = {
      [connInput.technicalName]: connInput,
    };
    const api = new ApiConnectionService(context);
    await api.upsertConnections(conn, spaceId);
    const connOutput = (await api.getConnections(spaceId))[connInput.technicalName];
    assertAPIConnDeepEqual(connInput, connOutput);

    const query = {
      space_ids: [preparation.spaceUuid],
      filters: `name:${connInput.technicalName}`,
      details: [
        "content",
        "configuration",
        "capabilityHanaSdi",
        "capabilityDataflowSource",
        "capabilityDataflowTarget",
        "capabilityReplicationflowSource",
        "capabilityReplicationflowTarget",
        "capabilityModelTransfer",
        "capabilityPartnerSchema",
        "disReplicationStatus",
        "typeId",
        "disConnectionId",
        "realtimeReplicationStatus",
      ],
    };

    // Retrieve Connection
    const connGetResult = await Remotes.getConnections(context, query);
    expect(connGetResult.length).to.eq(1);
    const dwcConnGetObj: any = connGetResult[0];
    const configuration = JSON.parse(dwcConnGetObj.configuration);
    const connProps = configuration.ConnectionProperties.configurations;
    assert.strictEqual(dwcConnGetObj.name, connInput.technicalName);
    assert.strictEqual(connProps.endpoint, "s3.amazonaws.com");
    assert.strictEqual(connProps.protocol, "https");
    assert.strictEqual(connProps.rootPath, "/");
    assert.strictEqual(dwcConnGetObj.capabilityHanaSdi, "false");
    assert.strictEqual(dwcConnGetObj.capabilityDataflowSource, "true");
    assert.strictEqual(dwcConnGetObj.capabilityReplicationflowTarget, "true");
    assert.strictEqual(dwcConnGetObj.capabilityModelTransfer, "false");
    assert.strictEqual(dwcConnGetObj.capabilityPartnerSchema, "false");
    assert.strictEqual(dwcConnGetObj.typeId, connInput.typeId);
    assert.strictEqual(dwcConnGetObj.disConnectionId.includes("-"), true);

    await Remotes.deleteConnections(context, preparation.spaceUuid, [dwcConnGetObj.id]);
  });

  describe("Logical Connections", () => {
    it("Should NOT be able to update a logical connection if no logical connection is present", async function () {
      await RepoTestUtility.enforceFeatureFlags(featureFlags);

      const name = "S4HanaCloudWithDirectUpdateLogical";
      const connInput = getSAPS4HANACLOUD_WSRFC_CDI_DPAGENT();
      connInput.technicalName = name;
      const conn = {
        [connInput.technicalName]: connInput,
      };

      const api = new ApiConnectionService(context);
      await expect(api.updateLogicalConnections(conn, spaceId))
        .to.be.eventually.rejectedWith(CodedError)
        .and.to.include({
          code: "connectionDoesNotExist",
        });
    });

    it("Should be able to update a logical connection via the API", async function () {
      await RepoTestUtility.enforceFeatureFlags(featureFlags);

      const name = "S4HanaCloudWithDirectLogical";
      const logicalConnection = {
        qualifiedName: name,
        kind: ObjectKind.remote,
        name,
        folderId: preparation.spaceUuid,
        inSpaceManagement: true,
        documentType: "csn",
        content: { objects: {} },
      };
      logicalConnection.content.objects[name] = {
        kind: "repository.remote",
        isLogical: true,
        configuration: {},
      };
      // insert logical connection
      const repositoryIds = await RepositoryObjectClient.createDocument(context, logicalConnection, {
        inSpaceManagement: true,
        needObjectSyncWithContentLib: false,
      });
      const repositoryId = Object.values(repositoryIds).map((el: { id: string }) => el.id)[0];

      // Update logical connection via API call
      const connInput = getSAPS4HANACLOUD_WSRFC_CDI_DPAGENT();
      connInput.technicalName = name;
      const conn = {
        [connInput.technicalName]: connInput,
      };

      RepoTestUtility.addStub(SDIManager, "getRemoteSourcesDetailed", [
        {
          name: `DWC_${spaceId}.${connInput.technicalName}`,
          location: "agent",
          agentName: "dwc_managed_dp_agent",
        },
      ]);

      const api = new ApiConnectionService(context);
      await api.updateLogicalConnections(conn, spaceId);
      const connOutput = (await api.getConnections(spaceId))[connInput.technicalName];
      assertAPIConnDeepEqual(connInput, connOutput);

      // Check if logical connection was replaced
      const params = {
        ids: repositoryId,
        folderId: preparation.spaceUuid,
        details: ["name", "isLogical", "csn"],
        inSpaceManagement: true,
        kinds: [ObjectKind.remote],
      };
      const repoConnections: RepositoryObject[] = await RepositoryObjectClient.getObject(context, params);
      expect(repoConnections.length).to.eq(1);
      expect(repoConnections[0].properties.isLogical).to.be.undefined;

      // Check if content is present
      const connContent = repoConnections[0].content as any;
      expect(connContent.objects[name].configuration).not.to.be.undefined;

      // Check if CCM connection was created
      const ccmConn = await getCCMConnection(connInput.technicalName, spaceId);
      expect(ccmConn.endpoints.length).to.eq(1);
      expect(ccmConn.endpoints.find((e) => e.typeId === CCMEndpoints.WSRFC)).not.to.be.undefined;
    });
  });

  describe("General invalid API payloads", function () {
    const ctx = new RequestContext({});
    this.timeout(30000);
    beforeEach(async function () {
      this.sandbox = sinon.createSandbox();
      this.sandbox.stub(Remotes, "upsertConnection").returns(Promise.resolve(["12345"]));
      this.sandbox.stub(Remotes, "getConnections").returns([]);
    });

    afterEach(async function () {
      this.sandbox.restore();
      await ctx.finish();
    });

    it("Should fail for invalid technical name", async function () {
      const connInput = await getCCMLike_S3();
      connInput.technicalName = "S3:Invalid";
      const conn = {
        [connInput.technicalName]: connInput,
      };

      try {
        await new ApiConnectionService(ctx).upsertConnections(conn, "MYSPACE");
        expect.fail();
      } catch (err) {
        expect(err).to.be.instanceOf(CodedError);
        expect(err.code).to.equal(`invalidSchema`);
        expect(err.status).to.equal(400);
      }
    });

    it("Should fail for invalid technical name length", async function () {
      const connInput = await getCCMLike_S3();
      connInput.technicalName = "This_Technical_Name_exceeds_40_characters";
      const conn = {
        [connInput.technicalName]: connInput,
      };

      try {
        await new ApiConnectionService(ctx).upsertConnections(conn, "MYSPACE");
        expect.fail();
      } catch (err) {
        expect(err).to.be.instanceOf(CodedError);
        expect(err.code).to.equal(`invalidSchema`);
        expect(err.status).to.equal(400);
      }
    });

    it("Should fail for invalid type Id", async function () {
      const ccmConnectionData: any = {
        S3_Conn: {
          name: "S3 Connection",
          technicalName: "S3_Conn",
          typeId: "Invalid",
        },
      };
      try {
        await new ApiConnectionService(ctx).upsertConnections(ccmConnectionData, "MYSPACE");
        expect.fail();
      } catch (err) {
        expect(err).to.be.instanceOf(CodedError);
        expect(err.code).to.equal(`invalidConnectionTypeId`);
        expect(err.status).to.equal(400);
      }
    });

    it("Should fail for valid but unsupported type Id for the API", async function () {
      const ccmConnectionData: any = {
        ABAP_Conn: {
          name: "ABAP Connection",
          technicalName: "ABAP_Conn",
          typeId: "ABAP",
        },
      };
      try {
        await new ApiConnectionService(ctx).upsertConnections(ccmConnectionData, "MYSPACE");
        expect.fail();
      } catch (err) {
        expect(err).to.be.instanceOf(CodedError);
        expect(err.code).to.equal(`unsupportedConnectionTypeForApi`);
        expect(err.status).to.equal(400);
      }
    });

    it("Should fail if connection object key name and technical name of the connection payload is not same", async function () {
      const ccmConnectionData: any = {
        S3_One: {
          name: "S3 Connection",
          technicalName: "S3_Two",
          typeId: "S3",
        },
      };
      try {
        await new ApiConnectionService(ctx).upsertConnections(ccmConnectionData, "MYSPACE");
        expect.fail();
      } catch (err) {
        expect(err).to.be.instanceOf(CodedError);
        expect(err.code).to.equal(`connectionNameMismatch`);
        expect(err.status).to.equal(400);
      }
    });
  });
});

async function getCCMConnection(connTechName: string, spaceId: string) {
  const ccmClient = new CCMClient(context);
  const ccmConn = await ccmClient.getConnection(buildAndCheckCCMTechnicalName(connTechName, spaceId));
  return ccmConn;
}
