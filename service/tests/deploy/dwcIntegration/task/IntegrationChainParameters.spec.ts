/** Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved. */
/* eslint-disable @sap/dwc-lints/service-dependency-analysis */

import { IRequestContext } from "@sap/deepsea-types";
import { Client, ITenant } from "@sap/pacemaker";
import { sql } from "@sap/prom-hana-client";
import chai, { expect } from "chai";
import sinon, { SinonSandbox } from "sinon";
import sinonChai from "sinon-chai";
import { IntegrationHelper } from ".";
import { RequestContext } from "../../../../repository/security/requestContext";
import taskFrameworkTables from "../../../../reuseComponents/onboarding/src/dbObjects/globalOwner/taskFrameworkTables.json";
import { ChainObserver } from "../../../../task/chains/ChainObserver";
import { ChainTask } from "../../../../task/chains/ChainTask";
import { DeployService } from "../../../../task/chains/designtime/DeployService";
import { Status } from "../../../../task/logger/models";
import { Activity, ApplicationId, Parameters } from "../../../../task/models";
import { ObserverFactory } from "../../../../task/observer/ObserverFactory";
import { TaskSynchronizerService } from "../../../../task/observer/TaskSynchronizerService";
import { TaskFactory } from "../../../../task/orchestrator/services/taskexecution/TaskFactory";
import { TaskFrameworkConstants } from "../../../../task/shared/db/TaskFrameworkConstants";
import { agentWithDefaults } from "../../../routes/route.utilty";
import { ControllableMock } from "../../../task/mocks/ControllableMock";
import { mockTenant } from "../../../task/mocks/pacemakerMock";
import { setupIntegrationTestSuite, type IDeployResult, type ISpaceTestClient } from "../../common";
import { createTestAdminRequestContext, getTestServerfromContext } from "../../restServer";
import { runStatement } from "./SqlTestHelper";
import { chainInputParams, nestedChainInputParams, nestedChainInputParamsReference } from "../../../task/chains/sampledata/parameters/inputParameters";
import { FeatureFlagProvider } from "@sap/dwc-context-checks/dist/featureflags/feature-flag-provider";

describe("IntegrationChainParameters", function() {
  chai.use(sinonChai);
  const spaceTestClient: ISpaceTestClient = setupIntegrationTestSuite(this);
  let sandbox: SinonSandbox;
  let deployedTables: IDeployResult;
  let testHelper: IntegrationHelper;
  let dbConfig: IDbConfig;
  let context: RequestContext;

  before(async function() {
    sandbox = sinon.createSandbox();
    dbConfig = spaceTestClient.getSpaceOwnerDbConfig();
    testHelper = new IntegrationHelper(dbConfig);
    context = createTestAdminRequestContext(spaceTestClient.getRequestContextInfo(), true);
    deployedTables = await spaceTestClient.executeDeploy(
      taskFrameworkTables);
    await testHelper.deployTfDbIndexes(context);
  });

  beforeEach(async function() {
    context = createTestAdminRequestContext(spaceTestClient.getRequestContextInfo(), true);
    await testHelper.mockdbClient(context, sandbox);
    await testHelper.mockSpaceAccess(sandbox);
    await testHelper.mockNotifications(sandbox);
    testHelper.mockNoDevEnvironment(sandbox);
    testHelper.stubDynatrace(sandbox);
    sandbox.stub(TaskSynchronizerService, "start").resolves();
    sandbox.stub(TaskSynchronizerService, "stop").resolves();
    sandbox.stub(FeatureFlagProvider, "getFeatureFlags").returns(
      Promise.resolve({
        DWCO_TASK_FRAMEWORK_CHAIN_PARAMETERS: true,
      } as any)
    );
  });

  after(async function() {
    await spaceTestClient.cleanup(true);
  });

  afterEach(async function() {
    await testHelper.cleanupDeployedTables(spaceTestClient, deployedTables.entityResults);
    await context.finish();
    sandbox.restore();
  });

  it("Chain with input parameters", async function() {
    const firstNodeId = 1;
    const secondNodeId = 2;
    const firstObjectId = "TABLE_01";
    const secondObjectId = "TABLE_02";
    const deployServiceStub = {
      getDeployedChain: sandbox.stub().resolves(chainInputParams),
    };
    sandbox.stub(DeployService, "fromSpaceId").resolves(deployServiceStub as any as DeployService);
    const chainSpy = sandbox.spy(ChainTask.prototype, "execute");
    const createJobStub = sandbox.stub();
    createJobStub.resolves();
    const tenantMock: ITenant = {
      ...mockTenant(sandbox),
      createJob: createJobStub,
      getJobs: sandbox.stub().resolves([])
    };
    sandbox.stub(Client, "getTenant").callsFake(async () => Promise.resolve({ ...tenantMock, getId: () => ("fake-tenant") }));
    sandbox.stub(ObserverFactory, "getObservedApplications").returns([ApplicationId.TASK_CHAINS]);
    sandbox.stub(ObserverFactory, "createObserver")
      .callsFake(async (context: IRequestContext, _: ApplicationId) => new ChainObserver(context));

    const impersonatedServer = getTestServerfromContext(spaceTestClient.getRequestContextInfo(), false);
    const executeResponse = await agentWithDefaults(impersonatedServer)
      .post("/tf/myspace/taskchains/Task_Chain_01/start").send();
    expect(executeResponse.status).to.be.equal(202);

    // Wait for the chain to have done the initialization
    await chainSpy.returnValues[0];
    const logsTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLogsTableName);
    const locksTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLocksTableName);
    const resultLogs = await runStatement(context, sql`select tl.* from ${logsTable} tl where OBJECT_ID = 'Task_Chain_01'`, dbConfig);
    expect(resultLogs.length).deep.equal(1);
    const chainLogId = resultLogs[0].TASK_LOG_ID;

    // We manually modify the time of task to skip the buffer in the synchronizer
    await runStatement(context, sql`update ${locksTable} set CREATION_TIME = '2022-03-01 00:00:00' where OBJECT_ID = 'Task_Chain_01'`, dbConfig);

    // Check generated runtime plan
    const taskChainRunsTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskChainRunsTableName);
    let result = await runStatement(context, sql`SELECT "PLAN" FROM ${taskChainRunsTable} WHERE "CHAIN_TASK_LOG_ID" = ${chainLogId}`, dbConfig);
    const runtimePlan = JSON.parse(result[0].PLAN);
    expect(runtimePlan[1].chainParameterIds).to.deep.equal({ Input: { retentionTimeInDays: 1 } });
    expect(runtimePlan[2].chainParameterIds).to.deep.equal({ Input: { PARAM_COUNTRY: 2, RETENTION_TIME: 3 } });

    // Check task start message details in the TASK_LOGS_MESSAGES table for the chain
    const taskLogsMessagesTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLogMessagesTableName);
    result = await runStatement(context, sql`SELECT "DETAILS" FROM ${taskLogsMessagesTable} WHERE "TASK_LOG_ID" = 1 AND MESSAGE_BUNDLE_KEY = 'taskHasInputParameters'`, dbConfig);
    let details = JSON.parse(result[0].DETAILS);
    expect(details).to.deep.equal({ P_RET_TIME: "90" });

    // Pretend to trigger the first child task with a onetime job, mocking it before.
    const mockRT = new ControllableMock();
    sandbox.stub(TaskFactory, "createExecutable").resolves(mockRT);
    const impServer = getTestServerfromContext(spaceTestClient.getRequestContextInfo());
    await agentWithDefaults(impServer, "fake-tenant")
      .post("/tf/execute").send(JSON.stringify(
        {
          applicationId: ApplicationId.REMOTE_TABLES,
          activity: Activity.REPLICATE,
          objectId: firstObjectId,
          spaceId: "myspace",
          payload: {
            tf: {
              chain: {
                chainLogId,
                nodeId: firstNodeId,
              },
              chainParameterIds: runtimePlan[1].chainParameterIds
            },
          } as Parameters,
          job: {},
          referenceId: `task-trigger-chain-${chainLogId}-node-${firstNodeId}`,
        }
      ));

    mockRT.resolve(Status.COMPLETED);
    // Verify chain input parameters for the first node
    expect(mockRT.chainParameters).to.deep.equal({ Input: { retentionTimeInDays: '100' } });

    // Check parameters in the TASK_LOGS table
    const taskLogsTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLogsTableName);
    result = await runStatement(context, sql`SELECT "PARAMETERS" FROM ${taskLogsTable} WHERE "OBJECT_ID" = ${firstObjectId}`, dbConfig);
    let parameters = JSON.parse(result[0].PARAMETERS);
    expect(parameters.tf.chainParameterIds).to.deep.equal({ Input: { retentionTimeInDays: 1 } });

    // Check task start message details in the TASK_LOGS_MESSAGES table
    result = await runStatement(context, sql`SELECT "DETAILS" FROM ${taskLogsMessagesTable} WHERE "TASK_LOG_ID" = 2 AND MESSAGE_BUNDLE_KEY = 'taskHasInputParameters'`, dbConfig);
    details = JSON.parse(result[0].DETAILS);
    expect(details).to.deep.equal({ retentionTimeInDays: '100' });

    // Pretend to trigger the second child task with a onetime job, mocking it before.
    await agentWithDefaults(impServer, "fake-tenant")
      .post("/tf/execute").send(JSON.stringify(
        {
          applicationId: ApplicationId.REMOTE_TABLES,
          activity: Activity.REPLICATE,
          objectId: secondObjectId,
          spaceId: "myspace",
          payload: {
            tf: {
              chain: {
                chainLogId,
                nodeId: secondNodeId,
              },
              chainParameterIds: runtimePlan[2].chainParameterIds
            },
          } as Parameters,
          job: {},
          referenceId: `task-trigger-chain-${chainLogId}-node-${secondNodeId}`,
        }
      ));

    mockRT.resolve(Status.COMPLETED);
    // Verify chain input parameters for the second node
    expect(mockRT.chainParameters).to.deep.equal({ Input: { PARAM_COUNTRY: 'DE', RETENTION_TIME: '90' } });

    // Check parameters in the TASK_LOGS table
    result = await runStatement(context, sql`SELECT "PARAMETERS" FROM ${taskLogsTable} WHERE "OBJECT_ID" = ${secondObjectId}`, dbConfig);
    parameters = JSON.parse(result[0].PARAMETERS);
    expect(parameters.tf.chainParameterIds).to.deep.equal({ Input: { PARAM_COUNTRY: 2, RETENTION_TIME: 3 } });

    // Check task start message details in the TASK_LOGS_MESSAGES table
    result = await runStatement(context, sql`SELECT "DETAILS" FROM ${taskLogsMessagesTable} WHERE "TASK_LOG_ID" = 3 AND MESSAGE_BUNDLE_KEY = 'taskHasInputParameters'`, dbConfig);
    details = JSON.parse(result[0].DETAILS);
    expect(details).to.deep.equal({ PARAM_COUNTRY: 'DE', RETENTION_TIME: '90' });
  });

  it("Nested chain with input parameters", async function() {
    const firstNodeId = 1;
    const secondNodeId = 2;
    const firstObjectId = "TABLE_03";
    const secondObjectId = "Task_Chain_01";
    const deployServiceStub = {
      getDeployedChain: sandbox.stub().resolves(nestedChainInputParams),
    };
    sandbox.stub(DeployService, "fromSpaceId").resolves(deployServiceStub as any as DeployService);
    const chainSpy = sandbox.spy(ChainTask.prototype, "execute");
    const createJobStub = sandbox.stub();
    createJobStub.resolves();
    const tenantMock: ITenant = {
      ...mockTenant(sandbox),
      createJob: createJobStub,
      getJobs: sandbox.stub().resolves([])
    };
    sandbox.stub(Client, "getTenant").callsFake(async () => Promise.resolve({ ...tenantMock, getId: () => ("fake-tenant") }));
    sandbox.stub(ObserverFactory, "getObservedApplications").returns([ApplicationId.TASK_CHAINS]);
    sandbox.stub(ObserverFactory, "createObserver")
      .callsFake(async (context: IRequestContext, _: ApplicationId) => new ChainObserver(context));

    const impersonatedServer = getTestServerfromContext(spaceTestClient.getRequestContextInfo(), false);
    const executeResponse = await agentWithDefaults(impersonatedServer)
      .post("/tf/myspace/taskchains/Task_Chain_02/start").send();
    expect(executeResponse.status).to.be.equal(202);

    // Wait for the chain to have done the initialization
    await chainSpy.returnValues[0];
    const logsTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLogsTableName);
    const locksTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLocksTableName);
    const resultLogs = await runStatement(context, sql`select tl.* from ${logsTable} tl where OBJECT_ID = 'Task_Chain_02'`, dbConfig);
    expect(resultLogs.length).deep.equal(1);
    const chainLogId = resultLogs[0].TASK_LOG_ID;

    // We manually modify the time of task to skip the buffer in the synchronizer
    await runStatement(context, sql`update ${locksTable} set CREATION_TIME = '2022-03-01 00:00:00' where OBJECT_ID = 'Task_Chain_02'`, dbConfig);

    // Check generated runtime plan
    const taskChainRunsTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskChainRunsTableName);
    let result = await runStatement(context, sql`SELECT "PLAN" FROM ${taskChainRunsTable} WHERE "CHAIN_TASK_LOG_ID" = ${chainLogId}`, dbConfig);
    const runtimePlan = JSON.parse(result[0].PLAN);

    expect(runtimePlan[1].chainParameterIds).to.deep.equal({ Input: { YEAR: 4 } });
    expect(runtimePlan[2].chainParameterIds).to.deep.equal({ Input: { P_RET_TIME: 5 } });

    // Pretend to trigger the first child task with a onetime job, mocking it before.
    const mockTask = new ControllableMock();
    sandbox.stub(TaskFactory, "createExecutable").resolves(mockTask);
    const impServer = getTestServerfromContext(spaceTestClient.getRequestContextInfo());
    await agentWithDefaults(impServer, "fake-tenant")
      .post("/tf/execute").send(JSON.stringify(
        {
          applicationId: ApplicationId.REMOTE_TABLES,
          activity: Activity.REPLICATE,
          objectId: firstObjectId,
          spaceId: "myspace",
          payload: {
            tf: {
              chain: {
                chainLogId,
                nodeId: firstNodeId,
              },
              chainParameterIds: runtimePlan[1].chainParameterIds
            },
          } as Parameters,
          job: {},
          referenceId: `task-trigger-chain-${chainLogId}-node-${firstNodeId}`,
        }
      ));

    mockTask.resolve(Status.COMPLETED);
    // Verify chain input parameters for the first node

    expect(mockTask.chainParameters).to.deep.equal({ Input: { YEAR: 2025 } });

    // Check parameters in the TASK_LOGS table
    const taskLogsTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLogsTableName);
    result = await runStatement(context, sql`SELECT "PARAMETERS" FROM ${taskLogsTable} WHERE "OBJECT_ID" = ${firstObjectId}`, dbConfig);
    let parameters = JSON.parse(result[0].PARAMETERS);
    expect(parameters.tf.chainParameterIds).to.deep.equal({ Input: { YEAR: 4 } });

    // Check task start message details in the TASK_LOGS_MESSAGES table
    const taskLogsMessagesTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLogMessagesTableName);
    result = await runStatement(context, sql`SELECT "DETAILS" FROM ${taskLogsMessagesTable} WHERE "TASK_LOG_ID" = 5 AND MESSAGE_BUNDLE_KEY = 'taskHasInputParameters'`, dbConfig);
    let details = JSON.parse(result[0].DETAILS);
    expect(details).to.deep.equal({ YEAR: 2025 });

    // Pretend to trigger the second child task with a onetime job, mocking it before.
    await agentWithDefaults(impServer, "fake-tenant")
      .post("/tf/execute").send(JSON.stringify(
        {
          applicationId: ApplicationId.TASK_CHAINS,
          activity: Activity.RUN_CHAIN,
          objectId: secondObjectId,
          spaceId: "myspace",
          payload: {
            tf: {
              chain: {
                chainLogId,
                nodeId: secondNodeId,
              },
              chainParameterIds: runtimePlan[2].chainParameterIds
            },
          } as Parameters,
          job: {},
          referenceId: `task-trigger-chain-${chainLogId}-node-${secondNodeId}`,
        }
      ));

    mockTask.resolve(Status.COMPLETED);
    // Verify chain input parameters for the second node
    expect(mockTask.chainParameters).to.deep.equal({ Input: { P_RET_TIME: '30' } });

    // Check parameters in the TASK_LOGS table
    result = await runStatement(context, sql`SELECT "PARAMETERS" FROM ${taskLogsTable} WHERE "OBJECT_ID" = ${secondObjectId}`, dbConfig);
    parameters = JSON.parse(result[0].PARAMETERS);
    expect(parameters.tf.chainParameterIds).to.deep.equal({ Input: { P_RET_TIME: 5 } });

    // Check task start message details in the TASK_LOGS_MESSAGES table
    result = await runStatement(context, sql`SELECT "DETAILS" FROM ${taskLogsMessagesTable} WHERE "TASK_LOG_ID" = 6 AND MESSAGE_BUNDLE_KEY = 'taskHasInputParameters'`, dbConfig);
    details = JSON.parse(result[0].DETAILS);
    expect(details).to.deep.equal({ P_RET_TIME: '30' });
  });

  it("Nested chain with input parameter with reference", async function() {
    const firstNodeId = 1;
    const secondNodeId = 2;
    const firstObjectId = "TABLE_03";
    const secondObjectId = "Task_Chain_01";
    const deployServiceStub = {
      getDeployedChain: sandbox.stub().resolves(nestedChainInputParamsReference),
    };
    sandbox.stub(DeployService, "fromSpaceId").resolves(deployServiceStub as any as DeployService);
    const chainSpy = sandbox.spy(ChainTask.prototype, "execute");
    const createJobStub = sandbox.stub();
    createJobStub.resolves();
    const tenantMock: ITenant = {
      ...mockTenant(sandbox),
      createJob: createJobStub,
      getJobs: sandbox.stub().resolves([])
    };
    sandbox.stub(Client, "getTenant").callsFake(async () => Promise.resolve({ ...tenantMock, getId: () => ("fake-tenant") }));
    sandbox.stub(ObserverFactory, "getObservedApplications").returns([ApplicationId.TASK_CHAINS]);
    sandbox.stub(ObserverFactory, "createObserver")
      .callsFake(async (context: IRequestContext, _: ApplicationId) => new ChainObserver(context));

    const impersonatedServer = getTestServerfromContext(spaceTestClient.getRequestContextInfo(), false);
    const executeResponse = await agentWithDefaults(impersonatedServer)
      .post("/tf/myspace/taskchains/Task_Chain_03/start").send();
    expect(executeResponse.status).to.be.equal(202);

    // Wait for the chain to have done the initialization
    await chainSpy.returnValues[0];
    const logsTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLogsTableName);
    const locksTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLocksTableName);
    const resultLogs = await runStatement(context, sql`select tl.* from ${logsTable} tl where OBJECT_ID = 'Task_Chain_03'`, dbConfig);
    expect(resultLogs.length).deep.equal(1);
    const chainLogId = resultLogs[0].TASK_LOG_ID;

    // We manually modify the time of task to skip the buffer in the synchronizer
    await runStatement(context, sql`update ${locksTable} set CREATION_TIME = '2022-03-01 00:00:00' where OBJECT_ID = 'Task_Chain_03'`, dbConfig);

    // Check generated runtime plan
    const taskChainRunsTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskChainRunsTableName);
    let result = await runStatement(context, sql`SELECT "PLAN" FROM ${taskChainRunsTable} WHERE "CHAIN_TASK_LOG_ID" = ${chainLogId}`, dbConfig);
    const runtimePlan = JSON.parse(result[0].PLAN);

    expect(runtimePlan[1].chainParameterIds).to.deep.equal({ Input: { YEAR: 6 } });
    expect(runtimePlan[2].chainParameterIds).to.deep.equal({ Input: { P_RET_TIME: 7 } });

    // Check task start message details in the TASK_LOGS_MESSAGES table for the parent chain
    const taskLogsMessagesTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLogMessagesTableName);
    result = await runStatement(context, sql`SELECT "DETAILS" FROM ${taskLogsMessagesTable} WHERE "TASK_LOG_ID" = 7 AND MESSAGE_BUNDLE_KEY = 'taskHasInputParameters'`, dbConfig);
    let details = JSON.parse(result[0].DETAILS);
    expect(details).to.deep.equal({ P_TIME: "120" });

    // Pretend to trigger the first child task with a onetime job, mocking it before.
    const mockTask = new ControllableMock();
    sandbox.stub(TaskFactory, "createExecutable").resolves(mockTask);
    const impServer = getTestServerfromContext(spaceTestClient.getRequestContextInfo());
    await agentWithDefaults(impServer, "fake-tenant")
      .post("/tf/execute").send(JSON.stringify(
        {
          applicationId: ApplicationId.REMOTE_TABLES,
          activity: Activity.REPLICATE,
          objectId: firstObjectId,
          spaceId: "myspace",
          payload: {
            tf: {
              chain: {
                chainLogId,
                nodeId: firstNodeId,
              },
              chainParameterIds: runtimePlan[1].chainParameterIds
            },
          } as Parameters,
          job: {},
          referenceId: `task-trigger-chain-${chainLogId}-node-${firstNodeId}`,
        }
      ));

    mockTask.resolve(Status.COMPLETED);
    // Verify chain input parameters for the first node

    expect(mockTask.chainParameters).to.deep.equal({ Input: { YEAR: 2025 } });

    // Check parameters in the TASK_LOGS table
    const taskLogsTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLogsTableName);
    result = await runStatement(context, sql`SELECT "PARAMETERS" FROM ${taskLogsTable} WHERE "OBJECT_ID" = ${firstObjectId}`, dbConfig);
    let parameters = JSON.parse(result[0].PARAMETERS);
    expect(parameters.tf.chainParameterIds).to.deep.equal({ Input: { YEAR: 6 } });

    // Check task start message details in the TASK_LOGS_MESSAGES table for the first child task
    result = await runStatement(context, sql`SELECT "DETAILS" FROM ${taskLogsMessagesTable} WHERE "TASK_LOG_ID" = 8 AND MESSAGE_BUNDLE_KEY = 'taskHasInputParameters'`, dbConfig);
    details = JSON.parse(result[0].DETAILS);
    expect(details).to.deep.equal({ YEAR: 2025 });

    // Pretend to trigger the second child task with a onetime job, mocking it before.
    await agentWithDefaults(impServer, "fake-tenant")
      .post("/tf/execute").send(JSON.stringify(
        {
          applicationId: ApplicationId.TASK_CHAINS,
          activity: Activity.RUN_CHAIN,
          objectId: secondObjectId,
          spaceId: "myspace",
          payload: {
            tf: {
              chain: {
                chainLogId,
                nodeId: secondNodeId,
              },
              chainParameterIds: runtimePlan[2].chainParameterIds
            },
          } as Parameters,
          job: {},
          referenceId: `task-trigger-chain-${chainLogId}-node-${secondNodeId}`,
        }
      ));

    mockTask.resolve(Status.COMPLETED);
    // Verify chain input parameters for the second node
    expect(mockTask.chainParameters).to.deep.equal({ Input: { P_RET_TIME: '120' } });

    // Check parameters in the TASK_LOGS table
    result = await runStatement(context, sql`SELECT "PARAMETERS" FROM ${taskLogsTable} WHERE "OBJECT_ID" = ${secondObjectId}`, dbConfig);
    parameters = JSON.parse(result[0].PARAMETERS);
    expect(parameters.tf.chainParameterIds).to.deep.equal({ Input: { P_RET_TIME: 7 } });

    // Check task start message details in the TASK_LOGS_MESSAGES table
    result = await runStatement(context, sql`SELECT "DETAILS" FROM ${taskLogsMessagesTable} WHERE "TASK_LOG_ID" = 9 AND MESSAGE_BUNDLE_KEY = 'taskHasInputParameters'`, dbConfig);
    details = JSON.parse(result[0].DETAILS);
    expect(details).to.deep.equal({ P_RET_TIME: '120' });
  });

  it("Trigger task chain by setting value of input parameter", async function() {
    const deployServiceStub = {
      getDeployedChain: sandbox.stub().resolves(chainInputParams),
    };
    sandbox.stub(DeployService, "fromSpaceId").resolves(deployServiceStub as any as DeployService);
    const chainSpy = sandbox.spy(ChainTask.prototype, "execute");
    const createJobStub = sandbox.stub();
    createJobStub.resolves();
    const tenantMock: ITenant = {
      ...mockTenant(sandbox),
      createJob: createJobStub,
      getJobs: sandbox.stub().resolves([])
    };
    sandbox.stub(Client, "getTenant").callsFake(async () => Promise.resolve({ ...tenantMock, getId: () => ("fake-tenant") }));
    sandbox.stub(ObserverFactory, "getObservedApplications").returns([ApplicationId.TASK_CHAINS]);
    sandbox.stub(ObserverFactory, "createObserver")
      .callsFake(async (context: IRequestContext, _: ApplicationId) => new ChainObserver(context));

    const impersonatedServer = getTestServerfromContext(spaceTestClient.getRequestContextInfo(), false);
    const requestBody = { "inputParameters": { "P_RET_TIME": "30" } }
    const executeResponse = await agentWithDefaults(impersonatedServer)
      .post("/tf/myspace/taskchains/Task_Chain_01/start").send(requestBody);
    expect(executeResponse.status).to.be.equal(202);

    // Wait for the chain to have done the initialization
    await chainSpy.returnValues[0];
    const logsTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLogsTableName);
    const locksTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLocksTableName);
    const resultLogs = await runStatement(context, sql`select tl.* from ${logsTable} tl where OBJECT_ID = 'Task_Chain_01'`, dbConfig);
    expect(resultLogs.length).deep.equal(1);
    const chainLogId = resultLogs[0].TASK_LOG_ID;

    // We manually modify the time of task to skip the buffer in the synchronizer
    await runStatement(context, sql`update ${locksTable} set CREATION_TIME = '2022-03-01 00:00:00' where OBJECT_ID = 'Task_Chain_01'`, dbConfig);

    // Check generated runtime plan
    const taskChainRunsTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskChainRunsTableName);
    let result = await runStatement(context, sql`SELECT "PLAN" FROM ${taskChainRunsTable} WHERE "CHAIN_TASK_LOG_ID" = ${chainLogId}`, dbConfig);
    const runtimePlan = JSON.parse(result[0].PLAN);
    expect(runtimePlan[1].chainParameterIds).to.deep.equal({ Input: { retentionTimeInDays: 8 } });
    expect(runtimePlan[2].chainParameterIds).to.deep.equal({ Input: { PARAM_COUNTRY: 9, RETENTION_TIME: 10 } });

    // Check task start message details in the TASK_LOGS_MESSAGES table for the chain
    const taskLogsMessagesTable = sql.quoted(TaskFrameworkConstants.schema, TaskFrameworkConstants.taskLogMessagesTableName);
    result = await runStatement(context, sql`SELECT "DETAILS" FROM ${taskLogsMessagesTable} WHERE "TASK_LOG_ID" = ${chainLogId} AND MESSAGE_BUNDLE_KEY = 'taskHasInputParameters'`, dbConfig);
    const details = JSON.parse(result[0].DETAILS);
    expect(details).to.deep.equal({ P_RET_TIME: "30" });
  });
});
