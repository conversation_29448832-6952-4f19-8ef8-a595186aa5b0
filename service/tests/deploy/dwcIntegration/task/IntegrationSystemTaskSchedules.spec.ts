/** Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved. */

import { IRequestContext } from "@sap/deepsea-types";
import { expect } from "chai";
import { Express } from "express";
import sinon, { SinonSandbox } from "sinon";
import { ITaskTestTable, IntegrationHelper } from ".";
import * as isCanaryFile from "../../../../lib/node";
import { RequestContext } from "../../../../repository/security/requestContext";
import taskFrameworkTables from "../../../../reuseComponents/onboarding/src/dbObjects/globalOwner/taskFrameworkTables.json";
import { Activity, ApplicationId } from "../../../../task/models";
import { PredefinedScheduleService } from "../../../../task/predefined/PredefinedScheduleService";
import { ISystemTaskDefinition } from "../../../../task/predefined/ISystemTaskDefinition";
import * as ExportHelper from "../../../../task/predefined/predefinedSystemSchedules";
import { TaskFrameworkConstants } from "../../../../task/shared/db/TaskFrameworkConstants";
import { agentWithDefaults } from "../../../routes/route.utilty";
import { setupIntegrationTestSuite, type IDeployResult, type ISpaceTestClient } from "../../common";
import { createTestAdminRequestContext, getTestServerfromContext } from "../../restServer";
import taskSchedulesDbRecords from "../task/TaskSchedulesDbRecords.json";
import taskSystemSchedulesDbRecords from "../task/TaskSystemSchedulesDbRecords.json";
import { getAllTaskSystemSchedules, retrieveSystemSchedules } from "./SystemTaskTestHelper";
import { PacemakerJobManager } from "../../../../task/predefined/PacemakerJobManager";
import { ITenant } from "@sap/pacemaker";
import { ExecutableMock } from "../../../task/mocks/ExecutableMock";
import { Status } from "../../../../task/logger/models";
import { TaskFactory } from "../../../../task/orchestrator/services/taskexecution/TaskFactory";

describe("IntegrationSystemTaskSchedules", function() {
  this.timeout(600000);
  const spaceTestClient: ISpaceTestClient = setupIntegrationTestSuite(this);

  let context: RequestContext;
  let sandbox: SinonSandbox;
  let deployedTables: IDeployResult;
  let testHelper: IntegrationHelper;
  let dbConfig: IDbConfig;
  let server: Express;
  let createExecutableStub: sinon.SinonStub;
  let tenant: ITenant;

  const testSystemTaskRegistry: ISystemTaskDefinition[] = [
    {
      applicationId: ApplicationId.HANA_CONNECTIVITY_PROXY,
      activity: Activity.DISABLE,
      cron: "X 23 * * 6",
      version: 1.0,
      description: "Disables the connectivity proxy if not used",
    },
    {
      applicationId: ApplicationId.METERING_SERVICE,
      activity: Activity.METERING_SERVICE,
      cron: "X X/12 * * SAT",
      version: 1.0,
      description: "metering",
    },
    {
      applicationId: ApplicationId.PASSWORD_ROTATION,
      activity: Activity.CUSTOMER_HANA_ROTATE,
      cron: "0 0 * * SAT",
      version: 1.1,
      description: "passwordRotation",
    },
    {
      applicationId: ApplicationId.HAUM_INTEGRATION,
      activity: Activity.TRANSFER,
      cron: "0 X * * SAT",
      version: 1.0,
      description: "HaumIntegration",
    }
  ];

  function getTestSystemTaskRegistryMap() {
    return new Map<string, ISystemTaskDefinition>(
      testSystemTaskRegistry.map(task => {
        const key = `${task.applicationId}.${task.activity}`;
        return [key, task];
      })
    );
  };

  before(async function() {
    sandbox = sinon.createSandbox();
    const featureFlags = {};
    dbConfig = spaceTestClient.getSpaceOwnerDbConfig();
    testHelper = new IntegrationHelper(dbConfig);
    context = createTestAdminRequestContext(spaceTestClient.getRequestContextInfo(), false);
    server = getTestServerfromContext(spaceTestClient.getRequestContextInfo(), false);
    deployedTables = await spaceTestClient.executeDeploy(
      taskFrameworkTables,
      featureFlags);
    await testHelper.deployTfDbIndexes(context);
  });

  beforeEach(async function() {
    sandbox.reset();
    const { fakeTenant } = await testHelper.mockPacemaker(sandbox);
    tenant = fakeTenant;
    await testHelper.mockdbClient(context, sandbox);
    await testHelper.mockSpaceAccess(sandbox);
    // DB table TASK_SCHEDULES is filled with a passwordRotation system task
    const tsTable: ITaskTestTable = { name: TaskFrameworkConstants.taskSchedulesTableName, data: taskSchedulesDbRecords };
    await testHelper.prePopulateTable(tsTable, context);
    // DB table TASK_SYSTEM_SCHEDULES is filled with a passwordRotation system task
    const stsTable: ITaskTestTable = { name: TaskFrameworkConstants.taskSystemSchedulesTableName, data: taskSystemSchedulesDbRecords };
    await testHelper.prePopulateTable(stsTable, context);
    sandbox.stub(isCanaryFile, "isCanary").returns(true);
    createExecutableStub = sandbox.stub(TaskFactory, 'createExecutable');
    sandbox.stub(PacemakerJobManager.prototype, "createJob").resolves("externalScheduleId");
    sandbox.stub(PacemakerJobManager.prototype, "updateJob").resolves(true);
    sandbox.stub(PacemakerJobManager.prototype, "deleteJobs").resolves(1);
  });

  after(async function() {
    await context.finish();
    await spaceTestClient.cleanup(true);
  });

  afterEach(async function() {
    await testHelper.cleanupDeployedTables(spaceTestClient, deployedTables.entityResults);
    sandbox.restore();
  });

  describe("PredefinedScheduleService.manageSystemTasks", function() {
    beforeEach(async function() {
      tenant.getId = () => "6b87366f-952f-492e-be6e-29e3659f518b";
      context.tenantId = "6b87366f-952f-492e-be6e-29e3659f518b";
    });

    it("should create all schedules definition when specific FFs are on", async function() {
      server = getTestServerfromContext(spaceTestClient.getRequestContextInfo(), true);
      const allSystemTasks = ExportHelper.getSystemTaskRegistryMap();
      allSystemTasks.forEach(s => s.enabledForTenant = (context: IRequestContext, schedule: ISystemTaskDefinition) => Promise.resolve(true));
      sandbox.stub(ExportHelper, "getSystemTaskRegistryMap").returns(allSystemTasks);
      const service = new PredefinedScheduleService(context, tenant);
      await service.manageSystemTasks();
      const result = await retrieveSystemSchedules(context, dbConfig);
      expect(result.length).to.be.deep.equal(26);
      const stsResults = await getAllTaskSystemSchedules(context, dbConfig);
      expect(stsResults.length).to.be.deep.equal(26);

      for (const schedule of result) {
        const task = new ExecutableMock(true, Status.COMPLETED);
        createExecutableStub.resolves(task);

        const body = {
          scheduleId: schedule.SCHEDULE_ID,
          applicationId: schedule.APPLICATION_ID,
          activity: schedule.ACTIVITY,
          objectId: schedule.OBJECT_ID,
          spaceId: schedule.SPACE_ID,
          job: {},
        }

        const technicalExecuteResult = await agentWithDefaults(server, "6b87366f-952f-492e-be6e-29e3659f518b").post("/tf/technicalexecute").send(JSON.stringify(body));
        expect(technicalExecuteResult.status).to.be.equal(202);
        expect(task.isCalled).to.be.true;
      }
    });

    it("creates/updates schedules definition when specific FFs are on", async function() {
      const testSchedules = getTestSystemTaskRegistryMap();
      sandbox.stub(ExportHelper, "getSystemTaskRegistryMap").returns(testSchedules);
      testSchedules.forEach(s => s.enabledForTenant = (context: IRequestContext, schedule: ISystemTaskDefinition) => Promise.resolve(true));
      const service = new PredefinedScheduleService(context, tenant);
      await service.manageSystemTasks();
      const result = await retrieveSystemSchedules(context, dbConfig);
      expect(result.length).to.be.deep.equal(4);
      const stsResults = await getAllTaskSystemSchedules(context, dbConfig);
      expect(stsResults.length).to.be.deep.equal(4);
    });

    it("should delete all schedules if their corresponding FFs are off", async function() {
      const testSchedules = getTestSystemTaskRegistryMap();
      sandbox.stub(ExportHelper, "getSystemTaskRegistryMap").returns(testSchedules);
      testSchedules.forEach(s => s.enabledForTenant = (context: IRequestContext, schedule: ISystemTaskDefinition) => Promise.resolve(false));
      const service = new PredefinedScheduleService(context, tenant);
      await service.manageSystemTasks();
      const result = await retrieveSystemSchedules(context, dbConfig);
      expect(result.length).to.be.deep.equal(0);
      const stsResults = await getAllTaskSystemSchedules(context, dbConfig);
      expect(stsResults.length).to.be.deep.equal(0);
    });

    it("should have a randomly generated cronstring per tenant", async function() {
      const testSchedules = getTestSystemTaskRegistryMap();
      sandbox.stub(ExportHelper, "getSystemTaskRegistryMap").returns(testSchedules);
      testSchedules.forEach(s => s.enabledForTenant = (context: IRequestContext, schedule: ISystemTaskDefinition) => Promise.resolve(true));
      const service = new PredefinedScheduleService(context, tenant);
      await service.manageSystemTasks();
      const result = await retrieveSystemSchedules(context, dbConfig);
      const cronStrings = result.map(r => r.CRON);
      // Expect all cron-strings to be updated, including the third one, which
      // exists already in the database with a different CRON settings.
      expect(cronStrings).deep.equal([
        "47 23 * * 6",
        "0 23 * * SAT",
        "47 11/12 * * SAT",
        "0 0 * * SAT"
      ]);
    });
  });

  describe("PUT /support/tenant/tf/systemschedules/status/:pauseorresume", function() {
    it("should pause and resume a schedule", async function() {
      const reqBody = {
        applicationId: "password_rotation", // should accept lower case as well
        activity: "customer_hana_rotate"
      }
      let schedule = await retrieveSystemSchedules(context, dbConfig);
      expect(schedule[0].ACTIVATION_STATUS).to.be.equal("ENABLED");

      // triggering to pause the schedule
      let response = await agentWithDefaults(server).put(`/support/tenant/tf/systemschedules/status/pause`).send(reqBody);
      expect(response.status).to.be.equal(204);
      schedule = await retrieveSystemSchedules(context, dbConfig);
      expect(schedule[0].ACTIVATION_STATUS).to.be.equal("DISABLED");

      // triggering to resume the schedule
      response = await agentWithDefaults(server).put(`/support/tenant/tf/systemschedules/status/resume`).send(reqBody);
      expect(response.status).to.be.equal(204);
      schedule = await retrieveSystemSchedules(context, dbConfig);
      expect(schedule[0].ACTIVATION_STATUS).to.be.equal("ENABLED");
    });

    it("should reject with status 400 in case of invalid operation parameter", async function() {
      const reqBody = {
        applicationId: "PASSWORD_ROTATION",
        activity: "CUSTOMER_HANA_ROTATE"
      }

      // triggering endpoint with wrong parameter 'abcde'
      const response = await agentWithDefaults(server).put(`/support/tenant/tf/systemschedules/status/abcde`).send(reqBody);
      expect(response.status).to.be.equal(400);
      expect(response.error.text).to.be.equal("Wrong operation parameter: abcde");
    });

    it("should reject with status 400 in case of trying to pause an already disabled schedule", async function() {
      const reqBody = {
        applicationId: "PASSWORD_ROTATION",
        activity: "CUSTOMER_HANA_ROTATE"
      }
      // check initial state
      let schedule = await retrieveSystemSchedules(context, dbConfig);
      expect(schedule[0].ACTIVATION_STATUS).to.be.equal("ENABLED");

      // triggering to pause the schedule
      let response = await agentWithDefaults(server).put(`/support/tenant/tf/systemschedules/status/pause`).send(reqBody);
      expect(response.status).to.be.equal(204);
      schedule = await retrieveSystemSchedules(context, dbConfig);
      expect(schedule[0].ACTIVATION_STATUS).to.be.equal("DISABLED");

      // trying to pause the schedule again
      response = await agentWithDefaults(server).put(`/support/tenant/tf/systemschedules/status/pause`).send(reqBody);
      expect(response.status).to.be.equal(400);
      expect(response.body.details.message).to.equal(`Bad Request: "Schedule with applicationId: PASSWORD_ROTATION and activity: CUSTOMER_HANA_ROTATE is already DISABLED, so cannot trigger again.".`);

      // no further change in state
      schedule = await retrieveSystemSchedules(context, dbConfig);
      expect(schedule[0].ACTIVATION_STATUS).to.be.equal("DISABLED");
    });

    it("should reject with status 400 in case of trying to resume an already enabled schedule", async function() {
      const reqBody = {
        applicationId: "PASSWORD_ROTATION",
        activity: "CUSTOMER_HANA_ROTATE"
      }
      // check initial state
      let schedule = await retrieveSystemSchedules(context, dbConfig);
      expect(schedule[0].ACTIVATION_STATUS).to.be.equal("ENABLED");

      // trying to resume the schedule
      const response = await agentWithDefaults(server).put(`/support/tenant/tf/systemschedules/status/resume`).send(reqBody);
      expect(response.status).to.be.equal(400);
      expect(response.body.details.message).to.equal(`Bad Request: "Schedule with applicationId: PASSWORD_ROTATION and activity: CUSTOMER_HANA_ROTATE is already ENABLED, so cannot trigger again.".`);

      // no change in state
      schedule = await retrieveSystemSchedules(context, dbConfig);
      expect(schedule[0].ACTIVATION_STATUS).to.be.equal("ENABLED");
    });
  });

  describe("PUT /support/tenant/tf/systemschedules/edit", function() {
    it("should update the cron of the schedule and set the rollbackTime correctly", async function() {
      const reqBody = {
        applicationId: "PASSWORD_ROTATION",
        activity: "CUSTOMER_HANA_ROTATE",
        cron: "0 0 * * SAT",
        rollbackTime: "2124-08-28T23:59:59.999Z"
      }
      let schedule = await retrieveSystemSchedules(context, dbConfig);
      let tsSchedules = await getAllTaskSystemSchedules(context, dbConfig);
      expect(schedule[0].CRON).to.be.equal("29 2 1 *\/2 *");
      expect(tsSchedules[0].ROLLBACK_TIME).to.be.null;

      // changing the CRON in TASK_SCHEDULES table and setting the ROLLBACK_TIME in TASK_SYSTEM_SCHEDULES table
      const response = await agentWithDefaults(server).put(`/support/tenant/tf/systemschedules/edit`).send(reqBody);
      expect(response.status).to.be.equal(204);
      schedule = await retrieveSystemSchedules(context, dbConfig);
      tsSchedules = await getAllTaskSystemSchedules(context, dbConfig);
      expect(schedule[0].CRON).to.be.equal("0 0 * * SAT");
      expect(tsSchedules[0].ROLLBACK_TIME).to.be.equal("2124-08-28 23:59:59.999000000");
    });

    it("should reject with status 404 in case of invalid applicationId or activity", async function() {
      const invalidReqBody = {
        applicationId: "HAUM_INTEGRATION", // invalid applicationId activity combo
        activity: "CUSTOMER_HANA_ROTATE",
        cron: "0 0 * * SAT",
        rollbackTime: "2124-08-28T23:59:59.999Z"
      }

      // triggering endpoint with invalid body
      const response = await agentWithDefaults(server).put(`/support/tenant/tf/systemschedules/edit`).send(invalidReqBody);
      expect(response.status).to.be.equal(404);
      expect(response.body).to.haveOwnProperty("code").to.equal("scheduleNotFoundError");
    });

    it("should reject with status 400 in case the schedule is paused", async function() {
      const reqBody = {
        applicationId: "PASSWORD_ROTATION",
        activity: "CUSTOMER_HANA_ROTATE",
        cron: "0 0 * * SAT",
        rollbackTime: "2124-08-28T23:59:59.999Z"
      }
      // check initial state
      let schedule = await retrieveSystemSchedules(context, dbConfig);
      let tsSchedules = await getAllTaskSystemSchedules(context, dbConfig);
      expect(schedule[0].ACTIVATION_STATUS).to.be.equal("ENABLED");
      expect(schedule[0].CRON).to.be.equal("29 2 1 *\/2 *");
      expect(tsSchedules[0].ROLLBACK_TIME).to.be.null;

      // pausing the schedule
      let response = await agentWithDefaults(server).put(`/support/tenant/tf/systemschedules/status/pause`).send(reqBody);
      expect(response.status).to.be.equal(204);
      schedule = await retrieveSystemSchedules(context, dbConfig);
      expect(schedule[0].ACTIVATION_STATUS).to.be.equal("DISABLED");

      // trying to change the CRON in TASK_SCHEDULES table and set the ROLLBACK_TIME in TASK_SYSTEM_SCHEDULES table
      response = await agentWithDefaults(server).put(`/support/tenant/tf/systemschedules/edit`).send(reqBody);
      expect(response.status).to.be.equal(400);
      expect(response.body.details.message).to.equal(`Bad Request: "Schedule with applicationId: PASSWORD_ROTATION and activity: CUSTOMER_HANA_ROTATE is DISABLED, and cannot be modified.".`);

      // no change in the state
      schedule = await retrieveSystemSchedules(context, dbConfig);
      tsSchedules = await getAllTaskSystemSchedules(context, dbConfig);
      expect(schedule[0].CRON).to.be.equal("29 2 1 *\/2 *");
      expect(tsSchedules[0].ROLLBACK_TIME).to.be.null;
    });
  });
});
