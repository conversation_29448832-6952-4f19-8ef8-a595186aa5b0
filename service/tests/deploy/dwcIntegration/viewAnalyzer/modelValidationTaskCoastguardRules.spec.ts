/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
// @TODO move to unit tests we want to check from model validation task if correct rules are configured
import { CapabilityKey } from "@sap-coastguard/commons";
import { DataValidator } from "@sap-coastguard/consumer-sdk-backend";
import { UserNames } from "@sap/deepsea-exports";
import { FeatureFlagProvider } from "@sap/dwc-context-checks";
import { expect } from "chai";
import { default as sinon } from "sinon";
import { IFeatureFlagsMap } from "../../../../featureflags/FeatureFlagProvider";
import { DbClient } from "../../../../lib/DbClient";
import { RequestContext } from "../../../../repository/security/requestContext";
import { createDbConnection } from "../../../../reuseComponents/onboarding/src/DbConnections";
import taskFrameworkTables from "../../../../reuseComponents/onboarding/src/dbObjects/globalOwner/taskFrameworkTables.json";
import { CustomerHanaRuntimeData } from "../../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import * as RemoteTablesLineageAPI from "../../../../routes/advisor/api/remoteTablesLineageAPI";
import { AnalysisScopes } from "../../../../routes/advisor/router/types";
import * as Util from "../../../../routes/advisor/router/util";
import { PreRequisites4PersistencyChecker } from "../../../../routes/viewPersistencyMonitor/util/PreRequisites4PersistencyChecker";
import { Status } from "../../../../task/logger/models";
import { Activity, ApplicationId } from "../../../../task/models";
import { TaskExecuteResponse } from "../../../../task/models/TaskExecuteResponse";
import { TaskExecutor } from "../../../../task/orchestrator/services/taskexecution/TaskExecutor";
import { TaskFrameworkConstants } from "../../../../task/shared/db/TaskFrameworkConstants";
import { setupIntegrationTestSuite, useSpaceTestClientForDeploy } from "../../common";
import { createTestAdminRequestContext } from "../../restServer";
import { IntegrationHelper } from "../task/IntegrationHelper";
import View_Join_001_002_mocks from "./data/View_Join_0001_002_mocks.json";
import { createRouterContext, deployEntitiesAndFillWithData, mockCustomerHana, mockExternalDbAccess } from "./helper";

describe("Model Validation Task", function () {
  const spaceTestClient = setupIntegrationTestSuite(this, undefined, true);
  let testHelper: IntegrationHelper;
  const entityName = "View_Join_001_002";
  const spaceName = spaceTestClient.getSpaceName();
  const spaceSchema = spaceTestClient.getSchemaName();
  const techSchemaName = spaceTestClient.getTechSchemaName();
  let commonFeatureFlags: IFeatureFlagsMap;

  before(async function () {
    testHelper = new IntegrationHelper(spaceTestClient.getSpaceOwnerDbConfig());
    await spaceTestClient.executeDeploy(taskFrameworkTables);
    await testHelper.deployTfDbIndexes(createContext());
    await deployEntitiesAndFillWithData(spaceTestClient);
  });

  beforeEach(async function () {
    useSpaceTestClientForDeploy(spaceTestClient);
    await testHelper.mockPacemaker(sinon);
    await testHelper.mockdbClient(createContext(), sinon);
    await testHelper.mockSpaceAccess(sinon);
    testHelper.stubDynatrace(sinon);
    sinon
      .stub(TaskFrameworkConstants, "schema")
      .get(() => IntegrationHelper.removeQuotes(spaceTestClient.getSchemaName()));
    sinon.stub(UserNames, "displayNamesByUserNames").resolves(new Map());
    testHelper.resetCounters();
    const requestContext = createContext();
    await mockExternalDbAccess(sinon);
    const routerContext = createRouterContext(
      entityName,
      spaceName,
      spaceSchema,
      techSchemaName,
      View_Join_001_002_mocks.repository.fullCsn as any,
      View_Join_001_002_mocks.repository.entitiesWithDependencies as any,
      requestContext
    );
    routerContext.scope.delete(AnalysisScopes.CrossSpace);
    sinon.stub(Util, "getContext").returns(routerContext);
    const dbClient = await createDbConnection(requestContext, spaceTestClient.getSpaceOwnerDbConfig());
    mockCustomerHana(sinon, dbClient, spaceSchema, techSchemaName);
    sinon.stub(RemoteTablesLineageAPI, "getVirtualRemoteTableAdaptersAPI").resolves([]);
    commonFeatureFlags = {
      DWCO_MODEL_VALIDATION: true,
    } as any;
  });

  afterEach(async function () {
    sinon.restore();
  });

  function createContext(): RequestContext {
    return createTestAdminRequestContext(spaceTestClient.getRequestContextInfo(), false);
  }

  describe("Model Validation execution: coastguard  validator check", function () {
    beforeEach(async function () {
      sinon.stub(DbClient.prototype as any, "executeProcedure").resolves();
    });
    it("Execute View Model Validation", async function () {
      sinon.stub(FeatureFlagProvider, "getFeatureFlags").resolves(commonFeatureFlags); // avoid any feature flag activation
      sinon.stub(CustomerHanaRuntimeData.prototype, "isSpaceLocked").resolves(false);
      sinon.stub(PreRequisites4PersistencyChecker.prototype, "check").resolves({ preRequisitesFulfilled: true });
      const stub = sinon.stub(DataValidator.prototype, "configureRule").returnsThis();
      const modelValidationTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.VALIDATE,
        entityName,
        spaceName,
        {}
      );
      await modelValidationTaskExecutor.trigger();
      const viewAnalyzerTask: Promise<TaskExecuteResponse> = modelValidationTaskExecutor.getTaskPromise();
      const finalViewAnalyzerStatus: TaskExecuteResponse = await viewAnalyzerTask;
      expect(finalViewAnalyzerStatus).equal(Status.COMPLETED);
      expect(stub.args).to.be.deep.eq([
        ["view-data-validation/unique-keys", "error"],
        ["view-data-validation/not-null-as-key", "error"],
        ["view-data-validation/hierarchy-no-multi-parent", "error"],
        ["view-data-validation/hierarchy-no-cycles", "error"],
        ["view-data-validation/consistent-data-types", "error"],
      ]);
    });
    it("Execute View Model Validation: DWCO_MODELING_VALIDATION_UNASSIGNED_NODES", async function () {
      commonFeatureFlags.DWCO_MODELING_VALIDATION_UNASSIGNED_NODES = true;
      sinon.stub(FeatureFlagProvider, "getFeatureFlags").resolves(commonFeatureFlags);
      sinon.stub(CustomerHanaRuntimeData.prototype, "isSpaceLocked").resolves(false);
      sinon.stub(PreRequisites4PersistencyChecker.prototype, "check").resolves({ preRequisitesFulfilled: true });
      const stub = sinon.stub(DataValidator.prototype, "configureRule").returnsThis();
      const validateStub = sinon.stub(DataValidator.prototype, "validate").returnsThis();
      const modelValidationTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.VALIDATE,
        entityName,
        spaceName,
        {}
      );
      await modelValidationTaskExecutor.trigger();
      const viewAnalyzerTask: Promise<TaskExecuteResponse> = modelValidationTaskExecutor.getTaskPromise();
      const finalViewAnalyzerStatus: TaskExecuteResponse = await viewAnalyzerTask;
      expect(finalViewAnalyzerStatus).equal(Status.COMPLETED);
      expect(stub.args).to.be.deep.eq([
        ["view-data-validation/unique-keys", "error"],
        ["view-data-validation/not-null-as-key", "error"],
        ["view-data-validation/hierarchy-no-multi-parent", "error"],
        ["view-data-validation/hierarchy-no-cycles", "error"],
        ["view-data-validation/consistent-data-types", "error"],
        ["view-data-validation/unassigned-hierarchy-nodes", "error"],
      ]);
      expect(validateStub.args[0][0].capabilities).to.be.deep.eq([CapabilityKey.DISABLE_CSN_GEN_FOR_EXISTING_ENTITIES]);
    });
    it("Execute View Model Validation: DWCO_MODELING_VALIDATION_ENABLE_REMOTE_TABLES", async function () {
      commonFeatureFlags.DWCO_MODELING_VALIDATION_ENABLE_REMOTE_TABLES = true;
      sinon.stub(FeatureFlagProvider, "getFeatureFlags").resolves(commonFeatureFlags);
      sinon.stub(CustomerHanaRuntimeData.prototype, "isSpaceLocked").resolves(false);
      sinon.stub(PreRequisites4PersistencyChecker.prototype, "check").resolves({ preRequisitesFulfilled: true });
      const stub = sinon.stub(DataValidator.prototype, "validate").returnsThis();
      const modelValidationTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.VALIDATE,
        entityName,
        spaceName,
        {}
      );
      await modelValidationTaskExecutor.trigger();
      const viewAnalyzerTask: Promise<TaskExecuteResponse> = modelValidationTaskExecutor.getTaskPromise();
      const finalViewAnalyzerStatus: TaskExecuteResponse = await viewAnalyzerTask;
      expect(finalViewAnalyzerStatus).equal(Status.COMPLETED);
      expect(stub.args[0][0].capabilities).to.be.deep.eq([CapabilityKey.DATA_VALIDATION_ON_REMOTE]);
    });
  });
});
