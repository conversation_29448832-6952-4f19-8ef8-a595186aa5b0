/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { expect } from "chai";
import { default as sinon } from "sinon";
import { getAdviceCoordinator } from "../../../../advisor";
import { AnalysisStatus } from "../../../../advisor/common/types";
import { EntityLabel, MessageKeys } from "../../../../advisor/i18n";
import { Node } from "../../../../advisor/semantic/node";
import { FeatureFlagProvider } from "../../../../featureflags/FeatureFlagProvider";
import { testTenantUuid } from "../../../../lib/node";
import { RequestContext } from "../../../../repository/security/requestContext";
import * as ViewPersistencyRuntimeInfo from "../../../../resourceMonitoring/utilities/viewPersistencyRuntimeInfo";
import { createDbConnection } from "../../../../reuseComponents/onboarding/src/DbConnections";
import { SealPluginDbArtifactsUpdater } from "../../../../reuseComponents/spaces/src/SealPluginDbArtifactsUpdater";
import { executeStatement } from "../../../../reuseComponents/systemTablesDeployment/src/dbClient";
import { retrieveViewInfo } from "../../../../routes/advisor/access";
import { ReplicationInfoProxy, RepositoryProxy, ViewInfoProxy } from "../../../../routes/advisor/access/";
import * as TaskFramework from "../../../../routes/advisor/access/taskFramework";
import { AnalysisScopes, IEntityTable } from "../../../../routes/advisor/router/types";
import * as Util from "../../../../routes/advisor/router/util";
import { ViewAnalyzerTaskLogger } from "../../../../routes/advisor/task/viewAnalyzerTaskLogger";
import * as UtilPartitioning from "../../../../routes/partitioning/utils/util";
import { ISpace, ReplicationType } from "../../../../routes/viewPersistencyMonitor/Interface";
import {
  ViewPersistencyExecutionMode,
  ViewPersistencyExecutor,
} from "../../../../routes/viewPersistencyMonitor/util/ViewPersistencyExecutor";
import * as UtilViewPersistency from "../../../../routes/viewPersistencyMonitor/util/util";
import { ITaskLogger } from "../../../../task/logger/models";
import { IgnoringLogger } from "../../../../task/logger/services/logger/IgnoringLogger";
import { TaskMetricService } from "../../../../task/logger/services/metrics/TaskMetricService";
import { Activity } from "../../../../task/models";
import { Parameters } from "../../../../task/orchestrator/models/Parameters";
import { setupIntegrationTestSuiteForUseOfDwcGlobalMockTables } from "../../common";
import { default as ObjectsToDeploy } from "./data/ObjectsToDeploy.json";
import { default as View_On_Remote_Table_mocks } from "./data/View_On_Remote_Table_mocks.json";
import {
  createRouterContext,
  deployEntitiesAndFillWithData,
  disableExpensiveStatementTrace,
  enableExpensiveStatementTrace,
  mockCustomerHana,
} from "./helper";
import { getTestModel } from "./modelFactory";

describe("View Analyzer: Memory evaluator", function () {
  const requestContext = RequestContext.createFromTenantId(testTenantUuid);
  const { spaceTestClient, taskLogIdGenerator } = setupIntegrationTestSuiteForUseOfDwcGlobalMockTables(this, 2);
  const spaceName = spaceTestClient.getSpaceName();
  const spaceSchema = spaceTestClient.getSchemaName();
  const technicalSchema = spaceTestClient.getTechSchemaName();
  const entityName = "View_Join_001_002";

  before(async () => {
    await deployEntitiesAndFillWithData(spaceTestClient);
    await enableExpensiveStatementTrace(spaceTestClient, requestContext);
  });

  after(async function () {
    await disableExpensiveStatementTrace(spaceTestClient, requestContext);
  });

  describe("Tests with not persisted views", function () {
    beforeEach(async () => {
      const dbConfig = spaceTestClient.getSpaceOwnerDbConfig();
      const dbClient = await createDbConnection(requestContext, dbConfig);
      mockCustomerHana(sinon, dbClient, spaceSchema, technicalSchema);
    });

    afterEach(() => {
      sinon.restore();
    });

    it("Execute View Analyzer with memory consumption for all views", async function () {
      const taskParameters = {
        withMemoryAnalysis: true,
        maximumMemoryConsumptionInGiB: 1,
      };
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
      const routerContext = await getRouterContext(entityName, taskParameters);
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(9);
      const views = result.entityStats.filter((item) => item.label === EntityLabel.VIEW);
      expect(views.length).equals(3);
      views.forEach((view) => {
        switch (view.entity) {
          case "View_001":
            expect(view.numberRecords).equals(2);
            break;
          case "View_002":
            expect(view.numberRecords).equals(1);
            break;
          case "View_Join_001_002":
            expect(view.numberRecords).equals(1);
            break;
        }
        expect(view.peakMemoryinMB).greaterThan(0);
        expect(view.persistencyRuntime).greaterThan(0);
        expect(Number.isInteger(view.persistencyRuntime)).equals(true);
      });
    });

    it("Execute View Analyzer with memory consumption for one view", async function () {
      const viewName = "View_001";
      const taskParameters = {
        withMemoryAnalysis: true,
        maximumMemoryConsumptionInGiB: 1,
        viewsWithMemoryAnalysis: [{ space: spaceName, entity: viewName }],
      };
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
      const routerContext = await getRouterContext(entityName, taskParameters);
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(9);
      const views = result.entityStats.filter((item) => item.label === EntityLabel.VIEW);
      expect(views.length).equals(3);
      views.forEach((view) => {
        if (view.entity === viewName) {
          expect(view.numberRecords).equals(2);
          expect(view.peakMemoryinMB).greaterThan(0);
          expect(view.persistencyRuntime).greaterThan(0);
        } else {
          expect(view.numberRecords).is.undefined;
          expect(view.peakMemoryinMB).is.undefined;
          expect(view.persistencyRuntime).is.undefined;
        }
      });
    });

    it("Persistency simulation should stop after View_001", async function () {
      const taskParameters = {
        withMemoryAnalysis: true,
        maximumMemoryConsumptionInGiB: 0.000001,
      };
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
      const routerContext = await getRouterContext(entityName, taskParameters);
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(9);
      const views = result.entityStats.filter((item) => item.label === EntityLabel.VIEW);
      expect(views.length).equals(3);
      const viewsWithMemoryConsumption = views.filter(
        (view) => view.numberRecords! > 0 && view.peakMemoryinMB! > 0 && view.persistencyRuntime! > 0
      );
      expect(viewsWithMemoryConsumption.length).equals(1);
    });

    it("For canceled task, persistency simulation should not be performed", async function () {
      const taskParameters = {
        withMemoryAnalysis: true,
        maximumMemoryConsumptionInGiB: 1,
      };
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(true);
      const routerContext = await getRouterContext(entityName, taskParameters);
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(9);
      const views = result.entityStats.filter((item) => item.label === EntityLabel.VIEW);
      expect(views.length).equals(3);
      const viewsWithMemoryConsumption = views.filter(
        (view) => view.numberRecords! > 0 && view.peakMemoryinMB! > 0 && view.persistencyRuntime! > 0
      );
      expect(viewsWithMemoryConsumption.length).equals(0);
    });

    it("Execute View Analyzer without memory consumption", async function () {
      const taskParameters = {};
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
      const routerContext = await getRouterContext(entityName, taskParameters);
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(9);
      const views = result.entityStats.filter((item) => item.label === EntityLabel.VIEW);
      expect(views.length).equals(3);
      views.forEach((view) => {
        expect(view.numberRecords).is.undefined;
        expect(view.peakMemoryinMB).is.undefined;
        expect(view.persistencyRuntime).is.undefined;
      });
    });

    it("Execute View Analyzer for view on remote table", async function () {
      const taskParameters = {};
      const entityName = "View_On_Remote_Table";
      const routerContext = await getRouterContext(entityName, taskParameters);
      const remoteTableInfo = JSON.parse(JSON.stringify(View_On_Remote_Table_mocks.remoteTableInfo));
      remoteTableInfo[0].OBJECT_SCHEMA_NAME = spaceSchema;
      routerContext.remoteTableInfo = new ReplicationInfoProxy(remoteTableInfo);
      const viewInfoList = JSON.parse(JSON.stringify(View_On_Remote_Table_mocks.viewInfo.viewInfoList));
      viewInfoList[0].OBJECT_SCHEMA_NAME = spaceSchema;
      routerContext.viewInfo = new ViewInfoProxy(viewInfoList);
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(2);
      const view = result.entityStats.filter((item) => item.label === EntityLabel.VIEW);
      const remoteTable = result.entityStats.filter((item) => item.label === EntityLabel.REMOTE_TABLE);
      expect(view[0].numberRecords).is.undefined;
      expect(view[0].peakMemoryinMB).is.undefined;
      expect(view[0].persistencyRuntime).is.undefined;
      expect(remoteTable[0].numberRecords).equals(10);
      expect(remoteTable[0].peakMemoryinMB).is.undefined;
      expect(remoteTable[0].persistencyRuntime).is.undefined;
    });

    it("Errors occur during execution of memory evaluator", async function () {
      const taskParameters = {
        withMemoryAnalysis: true,
        maximumMemoryConsumptionInGiB: 1,
      };
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
      const routerContext = await getRouterContext(entityName, taskParameters);
      routerContext.spaceSchema = "Does_not_exist";
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(12);
      const entitiesWithError = result.entityStats.filter((item) => item.analysisStatus === AnalysisStatus.Error);
      expect(entitiesWithError.length).equals(3);
      const errorMessages = result.payload.filter((item) => item.messageKey === MessageKeys.PA_ERROR_MESSAGE);
      expect(errorMessages.length).equals(3);
    });

    it("Should emit warning in case view is partitioned", async function () {
      const viewName = "View_001";
      const taskParameters = {
        withMemoryAnalysis: true,
        maximumMemoryConsumptionInGiB: 1,
        viewsWithMemoryAnalysis: [{ space: spaceName, entity: viewName }],
      };
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
      const routerContext = await getRouterContext(entityName, taskParameters);
      sinon.stub(Util, "getContext").returns(routerContext);
      sinon.stub(Node.prototype, "isPartitioned").returns(true);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(10);
      const viewStat = result.entityStats.filter((item) => item.entity === viewName);
      expect(viewStat.length).equal(1);
      expect(viewStat[0].analysisStatus).equals(AnalysisStatus.Warning);
      const viewPayload = result.payload.filter(
        (item) => item.entityName === viewName && item.messageKey === MessageKeys.PA_WARNING_MESSAGE
      );
      expect(viewPayload.length).equal(1);
    });
  });

  describe("Tests with persisted and not persisted views", function () {
    const dbConfig = spaceTestClient.getSpaceOwnerDbConfig();
    const spaceDetails: ISpace = {
      spaceName,
      spaceSchema,
      technicalSchema,
    };
    const persistedView = "View_001";
    const featureFlags = {
      ViewMaterialization: true,
    };
    const viewPersistencyRuntimeInfo = [
      { VIEW_NAME: persistedView, SPACE_NAME: spaceName, RECORDS: 100, PEAK_MEMORY: 10, RUN_TIME: 5 },
    ];
    const spaceData = [
      {
        space: spaceName,
        entity: persistedView,
        globalName: `${spaceName}.${entityName}`,
        id: "123",
        technicalType: "DWC_VIEW",
        sharedTo: [],
      },
      {
        space: spaceName,
        entity: "View_002",
        globalName: `${spaceName}.${entityName}`,
        id: "123",
        technicalType: "DWC_VIEW",
        sharedTo: [],
      },
    ];
    const spaces = new Map<string, IEntityTable[]>();
    spaces.set(spaceName, spaceData);

    before(async () => {
      const dbClient = await createDbConnection(requestContext, dbConfig);
      const taskLogger = await IgnoringLogger.fromLogId(taskLogIdGenerator.getNext());
      sinon.stub(FeatureFlagProvider, "getFeatureFlags").resolves(featureFlags as any);
      sinon.stub(SealPluginDbArtifactsUpdater.prototype, "update");
      sinon.stub(UtilPartitioning, "getSpaceDetails").resolves(spaceDetails);
      const taskMetricServiceStub = sinon.createStubInstance(TaskMetricService);
      sinon.stub(TaskMetricService, "fromContextAndLogId").resolves(taskMetricServiceStub as any);
      // Execute deploy to install View Persistency database artifacts
      await spaceTestClient.executeDeploy(ObjectsToDeploy.definitions[persistedView] as any, featureFlags);
      await new ViewPersistencyExecutor(
        requestContext,
        dbClient,
        taskLogger,
        persistedView,
        {
          space: spaceDetails,
          switchOn: true,
          replicationType: ReplicationType.BATCH,
        },
        spaceTestClient.getSpaceUserName(),
        undefined,
        {},
        undefined,
        ViewPersistencyExecutionMode.asynchronous_local_polling
      ).execute();
    });

    beforeEach(async () => {
      const dbClient = await createDbConnection(requestContext, dbConfig);
      mockCustomerHana(sinon, dbClient, spaceSchema, technicalSchema);
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
      sinon.stub(UtilViewPersistency, "getSpaceManagerClient").resolves(dbClient);
    });

    afterEach(() => {
      sinon.restore();
    });

    it("Persistency simulation should not be be executed for persisted view", async function () {
      const taskParameters = {
        withMemoryAnalysis: true,
        maximumMemoryConsumptionInGiB: 1,
        viewsWithMemoryAnalysis: [],
      };
      sinon.stub(ViewPersistencyRuntimeInfo, "getViewPersistencyRuntimeInfo").resolves(viewPersistencyRuntimeInfo);
      sinon.stub(TaskFramework, "getViewsWithTaskInfo").resolves([]);
      const routerContext = await getRouterContext(entityName, taskParameters);
      routerContext.repository.setSpaces(spaces);
      sinon.stub(Util, "getContext").returns(routerContext);
      await retrieveViewInfo(routerContext, requestContext, true);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(9);
      const views = result.entityStats.filter((item) => item.label === EntityLabel.VIEW);
      expect(views.length).equals(3);
      const viewsWithMemoryConsumption = views.filter(
        (view) => view.numberRecords! > 0 && view.peakMemoryinMB! > 0 && view.persistencyRuntime! > 0
      );
      expect(viewsWithMemoryConsumption.length).equals(3);
      const entityStatPersistedView = views.filter((view) => view.entity === persistedView);
      expect(entityStatPersistedView[0].isPersisted).is.true;
      expect(entityStatPersistedView[0].numberRecords).equals(100);
      expect(entityStatPersistedView[0].peakMemoryinMB).equals(10);
      expect(entityStatPersistedView[0].persistencyRuntime).equals(5);
    });

    it("Persistency runtime info results are missing", async function () {
      const taskParameters = {
        withMemoryAnalysis: true,
        maximumMemoryConsumptionInGiB: 1,
        viewsWithMemoryAnalysis: [],
      };
      const viewPersistencyRuntimeInfoNull = [
        { VIEW_NAME: persistedView, SPACE_NAME: spaceName, RECORDS: null, PEAK_MEMORY: null, RUN_TIME: 5 },
      ];
      sinon.stub(ViewPersistencyRuntimeInfo, "getViewPersistencyRuntimeInfo").resolves(viewPersistencyRuntimeInfoNull);
      sinon.stub(TaskFramework, "getViewsWithTaskInfo").resolves([]);
      const routerContext = await getRouterContext(entityName, taskParameters);
      routerContext.repository.setSpaces(spaces);
      sinon.stub(Util, "getContext").returns(routerContext);
      await retrieveViewInfo(routerContext, requestContext, true);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(9);
      const views = result.entityStats.filter((item) => item.label === EntityLabel.VIEW);
      expect(views.length).equals(3);
      const viewsWithMemoryConsumption = views.filter(
        (view) => view.numberRecords! > 0 && view.peakMemoryinMB! > 0 && view.persistencyRuntime! > 0
      );
      expect(viewsWithMemoryConsumption.length).equals(2);
      const entityStatPersistedView = views.filter((view) => view.entity === persistedView);
      expect(entityStatPersistedView[0].isPersisted).is.true;
      expect(entityStatPersistedView[0].numberRecords).is.undefined;
      expect(entityStatPersistedView[0].peakMemoryinMB).is.undefined;
      expect(entityStatPersistedView[0].persistencyRuntime).equals(5);
    });

    it("Persistency simulation should be executed for persisted view", async function () {
      const taskParameters = {
        withMemoryAnalysis: true,
        maximumMemoryConsumptionInGiB: 1,
        viewsWithMemoryAnalysis: [
          { space: spaceName, entity: persistedView },
          { space: spaceName, entity: "View_002" },
        ],
      };
      sinon.stub(ViewPersistencyRuntimeInfo, "getViewPersistencyRuntimeInfo").resolves(viewPersistencyRuntimeInfo);
      sinon.stub(TaskFramework, "getViewsWithTaskInfo").resolves([]);
      const routerContext = await getRouterContext(entityName, taskParameters);
      routerContext.repository.setSpaces(spaces);
      sinon.stub(Util, "getContext").returns(routerContext);
      await retrieveViewInfo(routerContext, requestContext, true);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(9);
      const views = result.entityStats.filter((item) => item.label === EntityLabel.VIEW);
      expect(views.length).equals(3);
      const viewsWithMemoryConsumption = views.filter(
        (view) => view.numberRecords! > 0 && view.peakMemoryinMB! > 0 && view.persistencyRuntime! > 0
      );
      expect(viewsWithMemoryConsumption.length).equals(2);
      const entityStatPersistedView = views.filter((view) => view.entity === persistedView);
      expect(entityStatPersistedView[0].isPersisted).is.true;
      expect(entityStatPersistedView[0].numberRecords).to.not.equal(100);
      expect(entityStatPersistedView[0].peakMemoryinMB).to.not.equal(10);
      expect(entityStatPersistedView[0].persistencyRuntime).to.not.equal(5);
    });
  });

  describe("Views with input parameters", function () {
    beforeEach(async () => {
      const dbConfig = spaceTestClient.getSpaceOwnerDbConfig();
      const dbClient = await createDbConnection(requestContext, dbConfig);
      mockCustomerHana(sinon, dbClient, spaceSchema, technicalSchema);
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
    });

    afterEach(() => {
      sinon.restore();
    });

    it("View with one input parameter and default value", async function () {
      const entity = "View_One_Parameter_Default_Value";
      const taskParameters = {
        withMemoryAnalysis: true,
        maximumMemoryConsumptionInGiB: 1,
        viewsWithMemoryAnalysis: [{ space: spaceName, entity }],
      };
      const routerContext = await getRouterContext(entity, taskParameters);
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(6);
      const viewWithParameter = result.entityStats.filter((item) => item.entity === entity)[0];
      expect(viewWithParameter.analysisStatus).eq(AnalysisStatus.Success);
      expect(viewWithParameter.numberRecords).equals(1);
      expect(viewWithParameter.peakMemoryinMB).greaterThan(0);
      expect(viewWithParameter.persistencyRuntime).greaterThan(0);
    });

    it("View with one input parameter and no default value", async function () {
      const entity = "View_One_Parameter_No_Default_Value";
      const taskParameters = {
        withMemoryAnalysis: true,
        maximumMemoryConsumptionInGiB: 1,
        viewsWithMemoryAnalysis: [{ space: spaceName, entity }],
      };
      const routerContext = await getRouterContext(entity, taskParameters);
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(6);
      const viewWithParameter = result.entityStats.filter((item) => item.entity === entity)[0];
      expect(viewWithParameter.analysisStatus).eq(AnalysisStatus.Warning);
      expect(viewWithParameter.numberRecords).is.undefined;
      expect(viewWithParameter.peakMemoryinMB).is.undefined;
      expect(viewWithParameter.persistencyRuntime).is.undefined;
    });

    it("View with two input parameters and default values", async function () {
      const entity = "View_Two_Parameters_Default_Value";
      const taskParameters = {
        withMemoryAnalysis: true,
        maximumMemoryConsumptionInGiB: 1,
        viewsWithMemoryAnalysis: [{ space: spaceName, entity }],
      };

      const routerContext = await getRouterContext(entity, taskParameters);
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(6);
      const viewWithParameter = result.entityStats.filter((item) => item.entity === entity)[0];
      expect(viewWithParameter.analysisStatus).eq(AnalysisStatus.Warning);
      expect(viewWithParameter.numberRecords).is.undefined;
      expect(viewWithParameter.peakMemoryinMB).is.undefined;
      expect(viewWithParameter.persistencyRuntime).is.undefined;
    });
  });

  describe("Tests with non existing objects", function () {
    before(async () => {
      const dbConfig = spaceTestClient.getSpaceOwnerDbConfig();
      const dbClient = await createDbConnection(requestContext, dbConfig);
      mockCustomerHana(sinon, dbClient, spaceSchema, technicalSchema);
      await executeStatement(dbClient, `DROP TABLE "Table_001"`);
      await executeStatement(dbClient, `DROP VIEW "View_001"`);
    });
    after(() => {
      sinon.restore();
    });
    it("Execute View Analyzer with memory consumption and non-existing view", async function () {
      const viewName = "View_001";
      const taskParameters = {
        withMemoryAnalysis: true,
        maximumMemoryConsumptionInGiB: 1,
        viewsWithMemoryAnalysis: [{ space: spaceName, entity: viewName }],
      };
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
      const routerContext = await getRouterContext(entityName, taskParameters);
      sinon.stub(Util, "getContext").returns(routerContext);
      sinon.stub(RepositoryProxy.prototype, "isFullyDeployed").returns(false);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(15);
      expect(result.payload.filter((item) => item.messageKey === MessageKeys.PA_NOT_FULLY_DEPLOYED).length).to.be.eq(5);
      const view = result.entityStats.filter((item) => item.label === EntityLabel.VIEW && item.entity === viewName);
      expect(view[0].numberRecords).is.undefined;
    });
  });

  async function getRouterContext(entityName: string, taskParameters: Parameters) {
    const taskLogger: ITaskLogger = await IgnoringLogger.fromLogId(1);
    const viewAnalyzerTaskLogger = await ViewAnalyzerTaskLogger.getInstance(
      taskLogger,
      Activity.EXECUTE_VIEW_ANALYZER,
      spaceName,
      requestContext,
      taskParameters
    );
    const testModel = getTestModel(spaceName, entityName);
    const routerContext = createRouterContext(
      `${spaceName}.${entityName}`,
      spaceName,
      spaceSchema,
      technicalSchema,
      testModel.fullCsn,
      testModel.entitiesWithDependencies,
      requestContext,
      viewAnalyzerTaskLogger
    );
    routerContext.scope.add(AnalysisScopes.CrossSpace);
    return routerContext;
  }
});
