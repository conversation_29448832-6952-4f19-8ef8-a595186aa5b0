/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { expect } from "chai";
import { default as sinon } from "sinon";
import { getAdviceCoordinator } from "../../../../advisor/";
import { AnalysisStatus } from "../../../../advisor/common/types";
import { EntityLabel, MessageKeys } from "../../../../advisor/i18n";
import { Node } from "../../../../advisor/semantic/node";
import { testTenantUuid } from "../../../../lib/node";
import { RequestContext } from "../../../../repository/security/requestContext";
import { createDbConnection } from "../../../../reuseComponents/onboarding/src/DbConnections";
import * as PlanViz from "../../../../reuseComponents/utility/planViz";
import { AnalysisScopes } from "../../../../routes/advisor/router/types";
import * as Util from "../../../../routes/advisor/router/util";
import { ViewAnalyzerTaskLogger } from "../../../../routes/advisor/task/viewAnalyzerTaskLogger";
import { ITaskLogger } from "../../../../task/logger/models";
import { IgnoringLogger } from "../../../../task/logger/services/logger/IgnoringLogger";
import { Activity } from "../../../../task/models";
import { Parameters } from "../../../../task/orchestrator/models/Parameters";
import { setupIntegrationTestSuite } from "../../common";
import { createRouterContext, deployEntitiesAndFillWithData, mockCustomerHana } from "./helper";
import { getTestModel } from "./modelFactory";

describe("View Analyzer: PlanViz evaluator", function () {
  const requestContext = RequestContext.createFromTenantId(testTenantUuid);
  const spaceTestClient = setupIntegrationTestSuite(this);
  const spaceName = spaceTestClient.getSpaceName();
  const spaceSchema = spaceTestClient.getSchemaName();
  const technicalSchema = spaceTestClient.getTechSchemaName();
  const entityName = "View_Join_001_002";
  const baseTestModel = getTestModel(spaceName, entityName);

  before(async () => {
    await deployEntitiesAndFillWithData(spaceTestClient);
  });

  describe("Tests with not persisted views", function () {
    beforeEach(async () => {
      const dbConfig = spaceTestClient.getSpaceOwnerDbConfig();
      const dbClient = await createDbConnection(requestContext, dbConfig);
      mockCustomerHana(sinon, dbClient, spaceSchema, technicalSchema);
    });

    afterEach(() => {
      sinon.restore();
    });

    it("Generate PlanViz for main view", async function () {
      const taskParameters = {
        withPlanViz: true,
        viewWithPlanViz: entityName,
      };
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
      const routerContext = await getRouterContext(taskParameters);
      routerContext.entity = `${spaceName}.${entityName}`;
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(9);
      const views = result.entityStats.filter((item) => item.label === EntityLabel.VIEW);
      expect(views.length).equals(3);
      const mainView = views.filter((item) => item.entity === entityName);
      expect(mainView[0].analysisStatus).equals(AnalysisStatus.Success);
    });

    it("Generate PlanViz for an underlying view", async function () {
      const taskParameters = {
        withPlanViz: true,
        viewWithPlanViz: "View_001",
      };
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
      const routerContext = await getRouterContext(taskParameters);
      routerContext.entity = `${spaceName}.${entityName}`;
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(9);
      const views = result.entityStats.filter((item) => item.label === EntityLabel.VIEW);
      expect(views.length).equals(3);
      const underlyingView = views.filter((item) => item.entity === "View_001");
      expect(underlyingView[0].analysisStatus).equals(AnalysisStatus.Success);
    });

    it("Error occurs during persistency simulation", async function () {
      const taskParameters = {
        withPlanViz: true,
        viewWithPlanViz: { space: spaceName, entity: "View_001" },
      };
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
      const routerContext = await getRouterContext(taskParameters);
      routerContext.entity = `${spaceName}.${entityName}`;
      routerContext.spaceSchema = "Does_not_exist";
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      const entitiesWithError = result.entityStats.filter((item) => item.analysisStatus === AnalysisStatus.Error);
      expect(entitiesWithError.length).equals(1);
      const errorMessages = result.payload.filter((item) => item.messageKey === MessageKeys.PA_ERROR_MESSAGE);
      expect(errorMessages.length).equals(1);
    });

    it("Error occurs during PlanViz generation", async function () {
      const viewName = "View_001";
      const taskParameters = {
        withPlanViz: true,
        viewWithPlanViz: { space: spaceName, entity: "View_001" },
      };
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
      const routerContext = await getRouterContext(taskParameters);
      routerContext.entity = `${spaceName}.${entityName}`;
      sinon.stub(Util, "getContext").returns(routerContext);
      const planVizStub = sinon.stub(PlanViz, "executeAndGeneratePlanViz");
      planVizStub.onCall(0).resolves({ XMLPlan: "", executionError: new Error("Some error") });
      planVizStub.onCall(1).resolves({ XMLPlan: "XML Plan file" });
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(10);
      const viewStat = result.entityStats.filter((item) => item.entity === viewName);
      expect(viewStat.length).equal(1);
      expect(viewStat[0].analysisStatus).equals(AnalysisStatus.Error);
      const viewPayload = result.payload.filter(
        (item) => item.entityName === viewName && item.messageKey === MessageKeys.PA_ERROR_MESSAGE
      );
      expect(viewPayload.length).equal(1);
    });

    it("Should emit warning in case view is partitioned", async function () {
      const viewName = "View_001";
      const taskParameters = {
        withPlanViz: true,
        viewWithPlanViz: { space: spaceName, entity: "View_001" },
      };
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
      const routerContext = await getRouterContext(taskParameters);
      routerContext.entity = `${spaceName}.${entityName}`;
      sinon.stub(Util, "getContext").returns(routerContext);
      sinon.stub(Node.prototype, "isPartitioned").returns(true);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(5);
      expect(result.payload.length).equals(10);
      const viewStat = result.entityStats.filter((item) => item.entity === viewName);
      expect(viewStat.length).equal(1);
      expect(viewStat[0].analysisStatus).equals(AnalysisStatus.Warning);
      const viewPayload = result.payload.filter(
        (item) => item.entityName === viewName && item.messageKey === MessageKeys.PA_WARNING_MESSAGE
      );
      expect(viewPayload.length).equal(1);
    });
  });

  describe("Tests with views with parameters", function () {
    let entity = "";
    const taskParameters = {
      withPlanViz: true,
      viewWithPlanViz: entity,
    };
    let viewAnalyzerTaskLoggerSpy: sinon.SinonSpy;
    beforeEach(async () => {
      const dbConfig = spaceTestClient.getSpaceOwnerDbConfig();
      const dbClient = await createDbConnection(requestContext, dbConfig);
      mockCustomerHana(sinon, dbClient, spaceSchema, technicalSchema);
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(false);
      viewAnalyzerTaskLoggerSpy = sinon.spy(ViewAnalyzerTaskLogger.prototype, "logMessageWithBlob");
    });

    afterEach(() => {
      sinon.restore();
    });

    it("View with input parameter with default value", async function () {
      entity = "View_One_Parameter_Default_Value";
      const routerContext = await getRouterContext(taskParameters);
      routerContext.entity = `${spaceName}.${entity}`;
      const testModel = getTestModel(spaceName, entity);
      routerContext.repository.setCSN(testModel.fullCsn);
      routerContext.repository.setEntitiesWithDependencies(testModel.entitiesWithDependencies);
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(6);
      expect(result.payload.length).equals(12);
      const views = result.entityStats.filter((item) => item.label === EntityLabel.VIEW);
      expect(views.length).equals(4);
      const mainView = views.filter((item) => item.entity === entity);
      expect(mainView[0].analysisStatus).equals(AnalysisStatus.Success);
      expect(viewAnalyzerTaskLoggerSpy.called).to.be.true;
    });

    it("View with input parameter with no default value", async function () {
      const entity = "View_One_Parameter_No_Default_Value";
      const testModel = getTestModel(spaceName, entity);
      const routerContext = await getRouterContext(taskParameters);
      routerContext.entity = `${spaceName}.${entity}`;
      routerContext.repository.setCSN(testModel.fullCsn);
      routerContext.repository.setEntitiesWithDependencies(testModel.entitiesWithDependencies);
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(6);
      expect(result.payload.length).equals(13);
      const views = result.entityStats.filter((item) => item.label === EntityLabel.VIEW);
      expect(views.length).equals(4);
      const mainView = views.filter((item) => item.entity === entity);
      expect(mainView[0].analysisStatus).equals(AnalysisStatus.Warning);
      expect(viewAnalyzerTaskLoggerSpy.called).to.be.false;
    });

    it("View with two input parameter with default values", async function () {
      const entity = "View_Two_Parameters_Default_Value";
      const testModel = getTestModel(spaceName, entity);
      const routerContext = await getRouterContext(taskParameters);
      routerContext.entity = `${spaceName}.${entity}`;
      routerContext.repository.setCSN(testModel.fullCsn);
      routerContext.repository.setEntitiesWithDependencies(testModel.entitiesWithDependencies);
      sinon.stub(Util, "getContext").returns(routerContext);
      const coordinator = getAdviceCoordinator(routerContext);
      const result = await coordinator.runAdvisor();
      expect(result.entityStats.length).equals(6);
      expect(result.payload.length).equals(13);
      const views = result.entityStats.filter((item) => item.label === EntityLabel.VIEW);
      expect(views.length).equals(4);
      const mainView = views.filter((item) => item.entity === entity);
      expect(mainView[0].analysisStatus).equals(AnalysisStatus.Warning);
      expect(viewAnalyzerTaskLoggerSpy.called).to.be.true;
    });
  });

  async function getRouterContext(taskParameters: Parameters) {
    const taskLogger: ITaskLogger = await IgnoringLogger.fromLogId(1);
    const viewAnalyzerTaskLogger = await ViewAnalyzerTaskLogger.getInstance(
      taskLogger,
      Activity.EXECUTE_VIEW_ANALYZER,
      spaceName,
      requestContext,
      taskParameters
    );

    const routerContext = createRouterContext(
      entityName,
      spaceName,
      spaceSchema,
      technicalSchema,
      baseTestModel.fullCsn,
      baseTestModel.entitiesWithDependencies,
      requestContext,
      viewAnalyzerTaskLogger
    );

    routerContext.scope.add(AnalysisScopes.PlanViz);
    routerContext.scope.delete(AnalysisScopes.MemoryEvaluator);
    return routerContext;
  }
});
