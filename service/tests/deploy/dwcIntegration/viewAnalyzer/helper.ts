/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { expect } from "chai";
import { SinonSandbox } from "sinon";
import { DbClient } from "../../../../lib/DbClient";
import { IRequestContext } from "../../../../repository/security/common/common";
import { RequestContext } from "../../../../repository/security/requestContext";
import { CustomerHana } from "../../../../reuseComponents/spaces/src";
import { CustomerHanaSpace } from "../../../../reuseComponents/spaces/src/CustomerHanaSpace";
import { executeStatement, getDbClient } from "../../../../reuseComponents/systemTablesDeployment/src/dbClient";
import * as ObjectDependency from "../../../../routes/advisor/access/objectDependency";
import * as Remote from "../../../../routes/advisor/access/remote";
import * as Repository from "../../../../routes/advisor/access/repository";
import * as ViewMonitor from "../../../../routes/advisor/access/viewmonitor";
import { AnalysisScopes, IEntitiesWithDependencies, IRouterContext } from "../../../../routes/advisor/router/types";
import * as Util from "../../../../routes/advisor/router/util";
import { ViewAnalyzerTaskLogger } from "../../../../routes/advisor/task/viewAnalyzerTaskLogger";
import type { ISpaceTestClient } from "../../common";
import ObjectsToDeploy from "./data/ObjectsToDeploy.json";

export function createRouterContext(
  entityName: string,
  spaceName: string,
  spaceSchema: string,
  technicalSchema: string,
  fullCsn: ICsn,
  entitiesWithDependencies: IEntitiesWithDependencies,
  requestContext: IRequestContext,
  taskLogger?: ViewAnalyzerTaskLogger
): IRouterContext {
  const routerContext = Util.getContext(entityName, spaceName, requestContext);
  routerContext.repository.setCSN(fullCsn);
  routerContext.repository.setEntitiesWithDependencies(entitiesWithDependencies);
  routerContext.spaceSchema = spaceSchema;
  routerContext.technicalSchema = technicalSchema;
  routerContext.scope.add(AnalysisScopes.MemoryEvaluator);
  routerContext.scope.add(AnalysisScopes.CrossSpace);
  routerContext.taskLogger = taskLogger;
  return routerContext;
}

export async function mockExternalDbAccess(sandbox: SinonSandbox) {
  sandbox.stub(Remote, "retrieveReplication").resolves();
  sandbox.stub(ObjectDependency, "retrieveRemoteTablesUserPropagation").resolves([]);
  sandbox.stub(Repository, "retrieveFullCSN").resolves();
  sandbox.stub(Repository, "retrieveCsnWithResolvedTypes").resolves();
  sandbox.stub(Repository, "retrieveValidationCsn").resolves();
  sandbox.stub(ViewMonitor, "retrieveViewInfo").resolves();
  sandbox.stub(Util, "getSpaceSchemas").resolves();
}

export async function deployEntitiesAndFillWithData(spaceTestClient: ISpaceTestClient) {
  // Deploy entities and fill tables with data
  const entityName = "View_Join_001_002";
  const deploymentResult = await spaceTestClient.executeDeploy(ObjectsToDeploy as any);
  expect(deploymentResult.messages.errors.length).to.eq(
    0,
    `Deploy session has the following messages: ${JSON.stringify(deploymentResult.messages.all)}`
  );
  await spaceTestClient.insertTableData(
    "Table_001",
    ["FIELD_A", "FIELD_B", "FIELD_C"],
    [
      ["AAAA_001", "BBBB_001", "CCCV_001"],
      ["AAAA_002", "BBBB_002", "CCCV_002"],
    ]
  );
  await spaceTestClient.insertTableData(
    "Table_002",
    ["FIELD_A", "FIELD_D", "FIELD_E"],
    [["AAAA_001", "DDDD_001", "EEEE_001"]]
  );
  const resultInsert = await spaceTestClient.selectData(entityName);
  expect(resultInsert.length).to.equal(1);
}

export function mockCustomerHana(
  sandbox: SinonSandbox,
  dbClient: DbClient,
  spaceSchema: string,
  technicalSchema: string
) {
  const schemaNames = {
    space_schema: spaceSchema,
    spc_tec_internal: technicalSchema,
  };
  const customerHanaSpace = {
    getSchemaNames: sandbox.stub().resolves(schemaNames),
  } as unknown as CustomerHanaSpace;
  const customerHana = {
    selectSpace: sandbox.stub().returns(customerHanaSpace),
    getSpaceOwnerClient: sandbox.stub().returns(dbClient),
    getSpaceManagerClient: sandbox.stub().returns(dbClient),
    getTenantManagerClient: sandbox.stub().returns(dbClient),
    getGlobalTaskFrameworkClient: sandbox.stub().returns(dbClient),
    getSpaceOwner: sandbox.stub().returns({ user: spaceSchema, host: "localhost", port: 30042, password: "Manager1" }),
  } as unknown as CustomerHana;
  sandbox.stub(CustomerHana, "fromRequestContext").resolves(customerHana);
}

export async function enableExpensiveStatementTrace(spaceTestClient: ISpaceTestClient, requestContext: RequestContext) {
  // Create connection with SYSTEM user in order to activate expensive Statement trace,
  // because Space Owner has no Authorization
  const dbSystemClient = await getDbClient(
    {
      ...spaceTestClient.getConnectionInfo(),
      ...spaceTestClient.getHanaTestClient().getRootUser(),
    },
    true,
    requestContext
  );
  // With this configuration setting, memory consumption is filled/logged only in Hana Cloud
  let enableExpensiveStatementTrace = `ALTER SYSTEM ALTER CONFIGURATION ('global.ini', 'SYSTEM')
                                            SET ('expensive_statement', 'enable') = 'true'
                                            WITH RECONFIGURE;`;
  await executeStatement(dbSystemClient, enableExpensiveStatementTrace);
  enableExpensiveStatementTrace = `ALTER SYSTEM ALTER CONFIGURATION ('global.ini', 'SYSTEM')
                                            SET ('expensive_statement', 'threshold_memory') = '1000'
                                            WITH RECONFIGURE;`;
  await executeStatement(dbSystemClient, enableExpensiveStatementTrace);
  await dbSystemClient.close();
}

export async function disableExpensiveStatementTrace(
  spaceTestClient: ISpaceTestClient,
  requestContext: RequestContext
) {
  const dbSystemClient = await getDbClient(
    {
      ...spaceTestClient.getConnectionInfo(),
      ...spaceTestClient.getHanaTestClient().getRootUser(),
    },
    true,
    requestContext
  );
  let disableExpensiveStatementTrace = `ALTER SYSTEM ALTER CONFIGURATION ('global.ini', 'SYSTEM')
                                          UNSET ('expensive_statement', 'enable')
                                          WITH RECONFIGURE;`;
  await executeStatement(dbSystemClient, disableExpensiveStatementTrace);
  disableExpensiveStatementTrace = `ALTER SYSTEM ALTER CONFIGURATION ('global.ini', 'SYSTEM')
                                          UNSET ('expensive_statement', 'threshold_memory')
                                          WITH RECONFIGURE`;
  await executeStatement(dbSystemClient, disableExpensiveStatementTrace);
  await dbSystemClient.close();
}
