/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { UserNames } from "@sap/deepsea-exports";
import { <PERSON>a<PERSON>rror } from "@sap/prom-hana-client";
import { expect } from "chai";
import { Express } from "express";
import { default as sinon } from "sinon";
import { FeatureFlagProvider } from "../../../../featureflags/FeatureFlagProvider";
import { DbClient } from "../../../../lib/DbClient";
import { RequestContext } from "../../../../repository/security/requestContext";
import { createDbConnection } from "../../../../reuseComponents/onboarding/src/DbConnections";
import taskFrameworkTables from "../../../../reuseComponents/onboarding/src/dbObjects/globalOwner/taskFrameworkTables.json";
import { CustomerHanaRuntimeData } from "../../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { AuthorizationUtils } from "../../../../reuseComponents/utility/AuthorizationUtils";
import * as ExplainPlan from "../../../../reuseComponents/utility/explainPlan";
import * as PlanViz from "../../../../reuseComponents/utility/planViz";
import { RequestBodyAttributes } from "../../../../routes/advisor/router/types";
import * as Util from "../../../../routes/advisor/router/util";
import { CancelAnalyzer } from "../../../../routes/advisor/task/cancelAnalyzer";
import { ViewAnalyzerTask } from "../../../../routes/advisor/task/viewAnalyzerTask";
import { ViewAnalyzerTaskLogger } from "../../../../routes/advisor/task/viewAnalyzerTaskLogger";
import { dbErrorCodes } from "../../../../routes/viewPersistencyMonitor/util/Constants";
import { Status, Substatus } from "../../../../task/logger/models";
import { Activity, ApplicationId, Parameters } from "../../../../task/models";
import { TaskExecuteResponse } from "../../../../task/models/TaskExecuteResponse";
import { TaskExecutor } from "../../../../task/orchestrator/services/taskexecution/TaskExecutor";
import { TaskFrameworkConstants } from "../../../../task/shared/db/TaskFrameworkConstants";
import { agentWithDefaults } from "../../../routes/route.utilty";
import type { ISpaceTestClient } from "../../common";
import { setupIntegrationTestSuite, useSpaceTestClientForDeploy } from "../../common";
import { createTestAdminRequestContext, getTestServerfromContext } from "../../restServer";
import { IntegrationHelper } from "../task/IntegrationHelper";
import { createRouterContext, deployEntitiesAndFillWithData, mockCustomerHana, mockExternalDbAccess } from "./helper";
import { getTestModel } from "./modelFactory";

describe("View Analyzer Task", function () {
  const spaceTestClient: ISpaceTestClient = setupIntegrationTestSuite(this, undefined, true);
  let server: Express;
  let testHelper: IntegrationHelper;
  let dbConfig: IDbConfig;
  const entityName = "View_Join_001_002";
  const spaceName = spaceTestClient.getSpaceName();
  const spaceSchema = spaceTestClient.getSchemaName();
  const techSchemaName = spaceTestClient.getTechSchemaName();
  let viewAnalyzerLogId: number;

  const testModel = getTestModel(spaceName, entityName);

  before(async function () {
    dbConfig = spaceTestClient.getSpaceOwnerDbConfig();
    server = getTestServerfromContext(spaceTestClient.getRequestContextInfo(), false);
    testHelper = new IntegrationHelper(dbConfig);
    await spaceTestClient.executeDeploy(taskFrameworkTables);
    await testHelper.deployTfDbIndexes(createContext());
    await deployEntitiesAndFillWithData(spaceTestClient);
  });

  beforeEach(async function () {
    useSpaceTestClientForDeploy(spaceTestClient);
    server = getTestServerfromContext(spaceTestClient.getRequestContextInfo(), false);
    await testHelper.mockPacemaker(sinon);
    await testHelper.mockdbClient(createContext(), sinon);
    await testHelper.mockSpaceAccess(sinon);
    testHelper.stubDynatrace(sinon);
    sinon.stub(UserNames, "displayNamesByUserNames").resolves(new Map());
    sinon.stub(TaskFrameworkConstants, "schema").get(() => IntegrationHelper.removeQuotes(dbConfig.currentSchema));
    testHelper.resetCounters();
    const requestContext = createContext();
    sinon.stub(FeatureFlagProvider, "getFeatureFlags").returns(
      Promise.resolve({
        DWCO_MODEL_VALIDATION: true,
        DWCO_USER_PROPAGATION: true,
      } as any)
    );
    await mockExternalDbAccess(sinon);
    const routerContext = createRouterContext(
      entityName,
      spaceName,
      spaceSchema,
      techSchemaName,
      testModel.fullCsn,
      testModel.entitiesWithDependencies,
      requestContext
    );
    sinon.stub(Util, "getContext").returns(routerContext);
    const dbClient = await createDbConnection(requestContext, dbConfig);
    mockCustomerHana(sinon, dbClient, spaceSchema, techSchemaName);
  });

  afterEach(async function () {
    sinon.restore();
  });

  function createContext(): RequestContext {
    return createTestAdminRequestContext(spaceTestClient.getRequestContextInfo(), false);
  }

  describe("View Analyzer Execution", function () {
    beforeEach(async function () {
      sinon.stub(DbClient.prototype as any, "executeProcedure").resolves();
    });
    it("Execute View Analyzer", async function () {
      const requestBody = {
        [RequestBodyAttributes.WithMemoryAnalysis]: true,
        [RequestBodyAttributes.MaximumMemoryConsumptionInGiB]: 1,
      };
      sinon.stub(CustomerHanaRuntimeData.prototype, "isSpaceLocked").resolves(false);
      const viewAnalyzerTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.EXECUTE_VIEW_ANALYZER,
        entityName,
        spaceName,
        requestBody
      );
      viewAnalyzerLogId = await viewAnalyzerTaskExecutor.trigger();
      const viewAnalyzerTask: Promise<TaskExecuteResponse> = viewAnalyzerTaskExecutor.getTaskPromise();
      const finalViewAnalyzerStatus: TaskExecuteResponse = await viewAnalyzerTask;
      expect(finalViewAnalyzerStatus).equal(Status.COMPLETED);
    });
    it("Get Task Logs", async function () {
      const response = await agentWithDefaults(server).get(`/tf/${spaceName}/logs/${viewAnalyzerLogId}`);
      const responseBody = response.body;
      expect(responseBody.objectId).to.equal(entityName);
      expect(responseBody.messages.length).to.equal(6);
      expect(responseBody.applicationId).to.equal(ApplicationId.VIEWS);
      expect(responseBody.activity).to.equal(Activity.EXECUTE_VIEW_ANALYZER);
    });
    it("Get View Analyzer result", async function () {
      const response = await agentWithDefaults(server).get(`/advisor/${spaceName}/result/${viewAnalyzerLogId}`);
      const responseBody = response.body;
      expect(responseBody.entityStats.length).to.equal(5);
      expect(responseBody.payload.length).to.equal(9);
    });
    it("Execute View Analyzer - canceled task", async function () {
      const requestBody = {
        [RequestBodyAttributes.WithMemoryAnalysis]: true,
        [RequestBodyAttributes.MaximumMemoryConsumptionInGiB]: 1,
      };
      sinon.stub(ViewAnalyzerTaskLogger.prototype, "isTaskCanceled").resolves(true);
      const viewAnalyzerTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.EXECUTE_VIEW_ANALYZER,
        entityName,
        spaceName,
        requestBody
      );
      viewAnalyzerLogId = await viewAnalyzerTaskExecutor.trigger();
      const viewAnalyzerTask: Promise<TaskExecuteResponse> = viewAnalyzerTaskExecutor.getTaskPromise();
      const finalViewAnalyzerStatus: TaskExecuteResponse = await viewAnalyzerTask;
      expect(finalViewAnalyzerStatus).equal(Status.FAILED);
    });
    it("Execute View Analyzer by using endpoint with POST method", async function () {
      const requestBody = {
        [RequestBodyAttributes.WithMemoryAnalysis]: true,
        [RequestBodyAttributes.MaximumMemoryConsumptionInGiB]: 1,
      };
      const response = await agentWithDefaults(server)
        .post(`/advisor/${spaceName}/execute/${entityName}`)
        .send(requestBody);
      expect(response.status).to.equal(202);
      expect(response.body.status).to.equal("Running");
    });
  });

  describe("View Analyzer Execution failures", function () {
    it("Execute View Analyzer when space is locked", async function () {
      const viewName = "View_001";
      const requestBody = {
        [RequestBodyAttributes.WithMemoryAnalysis]: true,
        [RequestBodyAttributes.MaximumMemoryConsumptionInGiB]: 1,
      };
      sinon.stub(CustomerHanaRuntimeData.prototype, "isSpaceLocked").resolves(true);
      const viewAnalyzerTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.EXECUTE_VIEW_ANALYZER,
        viewName,
        spaceName,
        requestBody
      );
      viewAnalyzerLogId = await viewAnalyzerTaskExecutor.trigger();
      const viewAnalyzerTask: Promise<TaskExecuteResponse> = viewAnalyzerTaskExecutor.getTaskPromise();
      const finalViewAnalyzerStatus: TaskExecuteResponse = await viewAnalyzerTask;
      expect(finalViewAnalyzerStatus).equal(Status.FAILED);
    });

    it("No authorization to execute View Analyzer", async function () {
      const requestBody = {
        [RequestBodyAttributes.WithMemoryAnalysis]: true,
        [RequestBodyAttributes.MaximumMemoryConsumptionInGiB]: 1,
      };
      sinon.stub(AuthorizationUtils, "isAuthorized").resolves(false);
      sinon.stub(DbClient.prototype as any, "executeProcedure").resolves();
      const response = await agentWithDefaults(server)
        .post(`/advisor/${spaceName}/execute/${entityName}`)
        .send(requestBody);
      expect(response.status).to.equal(403);
    });

    it("Error during authorization check", async function () {
      const requestBody = {
        [RequestBodyAttributes.WithMemoryAnalysis]: true,
        [RequestBodyAttributes.MaximumMemoryConsumptionInGiB]: 1,
      };
      sinon.stub(AuthorizationUtils, "isAuthorized").rejects(new Error("Some error"));
      sinon.stub(DbClient.prototype as any, "executeProcedure").resolves();
      const response = await agentWithDefaults(server)
        .post(`/advisor/${spaceName}/execute/${entityName}`)
        .send(requestBody);
      expect(response.status).to.equal(403);
    });

    it("Close execution finishes with error", async function () {
      const requestBody = {
        [RequestBodyAttributes.WithMemoryAnalysis]: true,
        [RequestBodyAttributes.MaximumMemoryConsumptionInGiB]: 1,
      };
      sinon.stub(CustomerHanaRuntimeData.prototype, "isSpaceLocked").resolves(false);
      const dbClientStub = sinon.stub(DbClient.prototype, "executeProcedure");
      dbClientStub.onCall(0).resolves();
      dbClientStub.onCall(1).rejects(new Error("Some error"));
      const viewAnalyzerTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.EXECUTE_VIEW_ANALYZER,
        entityName,
        spaceName,
        requestBody
      );
      viewAnalyzerLogId = await viewAnalyzerTaskExecutor.trigger();
      const viewAnalyzerTask: Promise<TaskExecuteResponse> = viewAnalyzerTaskExecutor.getTaskPromise();
      const finalViewAnalyzerStatus: TaskExecuteResponse = await viewAnalyzerTask;
      expect(finalViewAnalyzerStatus).equal(Status.FAILED);
    });

    it("Resource limit error occurs during execution of View Analyzer", async function () {
      const viewName = "View_001";
      const requestBody = {
        [RequestBodyAttributes.WithMemoryAnalysis]: true,
        [RequestBodyAttributes.MaximumMemoryConsumptionInGiB]: 1,
      };
      const viewAnalyzerTaskSpy = sinon.spy(ViewAnalyzerTask.prototype, "execute");
      sinon
        .stub(CustomerHanaRuntimeData.prototype, "isSpaceLocked")
        .rejects(new HanaError("", dbErrorCodes.OUT_OF_MEMORY));
      sinon.stub(DbClient.prototype as any, "executeProcedure").resolves();
      const viewAnalyzerTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.EXECUTE_VIEW_ANALYZER,
        viewName,
        spaceName,
        requestBody
      );
      viewAnalyzerLogId = await viewAnalyzerTaskExecutor.trigger();
      const viewAnalyzerTask: Promise<TaskExecuteResponse> = viewAnalyzerTaskExecutor.getTaskPromise();
      await viewAnalyzerTask;
      expect(await viewAnalyzerTaskSpy.firstCall.returnValue).to.deep.equal({
        status: Status.FAILED,
        subStatusCode: Substatus.RESOURCE_LIMIT_ERROR,
      });
    });
  });

  describe("View Analyzer with PlanViz", function () {
    beforeEach(async function () {
      sinon.stub(DbClient.prototype as any, "executeProcedure").resolves();
    });
    it("Execute View Analyzer", async function () {
      const requestBody = {
        [RequestBodyAttributes.WithPlanViz]: true,
      };
      sinon.stub(CustomerHanaRuntimeData.prototype, "isSpaceLocked").resolves(false);
      const viewAnalyzerTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.EXECUTE_VIEW_ANALYZER,
        entityName,
        spaceName,
        requestBody
      );
      viewAnalyzerLogId = await viewAnalyzerTaskExecutor.trigger();
      const viewAnalyzerTask: Promise<TaskExecuteResponse> = viewAnalyzerTaskExecutor.getTaskPromise();
      const finalViewAnalyzerStatus: TaskExecuteResponse = await viewAnalyzerTask;
      expect(finalViewAnalyzerStatus).equal(Status.COMPLETED);
    });
    it("Get Task Logs", async function () {
      const response = await agentWithDefaults(server).get(`/tf/${spaceName}/logs/${viewAnalyzerLogId}`);
      const responseBody = response.body;
      expect(responseBody.objectId).to.equal(entityName);
      expect(responseBody.messages.length).to.equal(5);
      expect(responseBody.applicationId).to.equal(ApplicationId.VIEWS);
      expect(responseBody.activity).to.equal(Activity.EXECUTE_VIEW_ANALYZER);
    });
    it("Get PlanViz", async function () {
      const response = await agentWithDefaults(server).get(
        `/advisor/${spaceName}/logs/${viewAnalyzerLogId}/messages/4/planviz`
      );
      expect(response.status).to.equal(200);
      expect(response.body).to.not.be.empty;
    });
    it("PlanViz cannot be retrieved from Task Log endpoint", async function () {
      const response = await agentWithDefaults(server).get(
        `/tf/${spaceName}/logs/${viewAnalyzerLogId}/messages/4/blobcontent`
      );
      expect(response.status).to.equal(404);
    });
    it("Technical error - task should fail", async function () {
      const requestBody = {
        [RequestBodyAttributes.WithPlanViz]: true,
      };
      sinon.stub(CustomerHanaRuntimeData.prototype, "isSpaceLocked").resolves(false);
      const planVizStub = sinon.stub(PlanViz, "executeAndGeneratePlanViz");
      planVizStub.rejects(new Error("Some error"));
      const viewAnalyzerTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.EXECUTE_VIEW_ANALYZER,
        entityName,
        spaceName,
        requestBody
      );
      viewAnalyzerLogId = await viewAnalyzerTaskExecutor.trigger();
      const viewAnalyzerTask: Promise<TaskExecuteResponse> = viewAnalyzerTaskExecutor.getTaskPromise();
      const finalViewAnalyzerStatus: TaskExecuteResponse = await viewAnalyzerTask;
      expect(finalViewAnalyzerStatus).equal(Status.FAILED);
    });
    it("Error during PlanViz generation - task should complete", async function () {
      const requestBody = {
        [RequestBodyAttributes.WithPlanViz]: true,
      };
      sinon.stub(CustomerHanaRuntimeData.prototype, "isSpaceLocked").resolves(false);
      const planVizStub = sinon.stub(PlanViz, "executeAndGeneratePlanViz");
      planVizStub.onCall(0).resolves({ XMLPlan: "", executionError: new Error("Some error") });
      planVizStub.onCall(1).resolves({ XMLPlan: "XML Plan file" });
      const viewAnalyzerTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.EXECUTE_VIEW_ANALYZER,
        entityName,
        spaceName,
        requestBody
      );
      viewAnalyzerLogId = await viewAnalyzerTaskExecutor.trigger();
      const viewAnalyzerTask: Promise<TaskExecuteResponse> = viewAnalyzerTaskExecutor.getTaskPromise();
      const finalViewAnalyzerStatus: TaskExecuteResponse = await viewAnalyzerTask;
      expect(finalViewAnalyzerStatus).equal(Status.COMPLETED);
    });
  });

  describe("View Analyzer with ExplainPlan", function () {
    beforeEach(async function () {
      sinon.stub(DbClient.prototype as any, "executeProcedure").resolves();
    });
    it("Execute View Analyzer", async function () {
      const requestBody = {
        [RequestBodyAttributes.WithMemoryAnalysis]: false,
        [RequestBodyAttributes.WithExplainPlan]: true,
      };
      sinon.stub(CustomerHanaRuntimeData.prototype, "isSpaceLocked").resolves(false);
      const viewAnalyzerTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.EXECUTE_VIEW_ANALYZER,
        entityName,
        spaceName,
        requestBody
      );
      viewAnalyzerLogId = await viewAnalyzerTaskExecutor.trigger();
      const viewAnalyzerTask: Promise<TaskExecuteResponse> = viewAnalyzerTaskExecutor.getTaskPromise();
      const finalViewAnalyzerStatus: TaskExecuteResponse = await viewAnalyzerTask;
      expect(finalViewAnalyzerStatus).equal(Status.COMPLETED);
    });
    it("Get Task Logs", async function () {
      const response = await agentWithDefaults(server).get(`/tf/${spaceName}/logs/${viewAnalyzerLogId}`);
      const responseBody = response.body;
      expect(responseBody.objectId).to.equal(entityName);
      expect(responseBody.messages.length).to.equal(5);
      expect(responseBody.applicationId).to.equal(ApplicationId.VIEWS);
      expect(responseBody.activity).to.equal(Activity.EXECUTE_VIEW_ANALYZER);
    });
    it("Get ExplainPlan", async function () {
      const response = await agentWithDefaults(server).get(
        `/advisor/${spaceName}/logs/${viewAnalyzerLogId}/messages/4/explainplan`
      );
      expect(response.status).to.equal(200);
      expect(response.body).to.not.be.empty;
    });
    it("ExplainPlan cannot be retrieved for wrong message number", async function () {
      const response = await agentWithDefaults(server).get(
        `/advisor/${spaceName}/logs/${viewAnalyzerLogId}/messages/3/explainplan`
      );
      expect(response.status).to.equal(404);
    });
    it("ExplainPlan cannot be retrieved from Task Log endpoint", async function () {
      const response = await agentWithDefaults(server).get(
        `/tf/${spaceName}/logs/${viewAnalyzerLogId}/messages/4/blobcontent`
      );
      expect(response.status).to.equal(404);
    });
    it("Technical error - task should fail", async function () {
      sinon.stub(ExplainPlan, "generateExplainPlan").rejects(new Error("Some error"));

      const requestBody = {
        [RequestBodyAttributes.WithExplainPlan]: true,
      };
      sinon.stub(CustomerHanaRuntimeData.prototype, "isSpaceLocked").resolves(false);
      const viewAnalyzerTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.EXECUTE_VIEW_ANALYZER,
        entityName,
        spaceName,
        requestBody
      );
      viewAnalyzerLogId = await viewAnalyzerTaskExecutor.trigger();
      const viewAnalyzerTask: Promise<TaskExecuteResponse> = viewAnalyzerTaskExecutor.getTaskPromise();
      const finalViewAnalyzerStatus: TaskExecuteResponse = await viewAnalyzerTask;
      expect(finalViewAnalyzerStatus).equal(Status.FAILED);
    });
  });

  describe("View Analyzer Cancel Task", function () {
    beforeEach(async function () {
      sinon.stub(DbClient.prototype as any, "executeProcedure").resolves();
    });
    it("Execute Cancel View Analyzer", async function () {
      const requestBody = {
        [RequestBodyAttributes.TaskLogIdToCancel]: 1,
      };
      const viewAnalyzerCancelTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.CANCEL_VIEW_ANALYZER,
        entityName,
        spaceName,
        requestBody
      );
      await viewAnalyzerCancelTaskExecutor.trigger();
      const viewAnalyzerCancelTask: Promise<TaskExecuteResponse> = viewAnalyzerCancelTaskExecutor.getTaskPromise();
      const finalViewAnalyzerCancelStatus: TaskExecuteResponse = await viewAnalyzerCancelTask;
      expect(finalViewAnalyzerCancelStatus).equal(Status.COMPLETED);
    });
    it("Cancel View Analyzer - running task", async function () {
      const requestBody = {
        [RequestBodyAttributes.TaskLogIdToCancel]: 1,
      };
      sinon.stub(CancelAnalyzer.prototype, "isRunningTask").resolves(true);
      const viewAnalyzerCancelTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.CANCEL_VIEW_ANALYZER,
        entityName,
        spaceName,
        requestBody
      );
      await viewAnalyzerCancelTaskExecutor.trigger();
      const viewAnalyzerCancelTask: Promise<TaskExecuteResponse> = viewAnalyzerCancelTaskExecutor.getTaskPromise();
      const finalViewAnalyzerCancelStatus: TaskExecuteResponse = await viewAnalyzerCancelTask;
      expect(finalViewAnalyzerCancelStatus).equal(Status.COMPLETED);
    });
    it("Cancel View Analyzer with not valid request body", async function () {
      const requestBody = {};
      sinon.stub(CancelAnalyzer.prototype, "isRunningTask").resolves(true);
      const viewAnalyzerCancelTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.CANCEL_VIEW_ANALYZER,
        entityName,
        spaceName,
        requestBody
      );
      await viewAnalyzerCancelTaskExecutor.trigger();
      const viewAnalyzerCancelTask: Promise<TaskExecuteResponse> = viewAnalyzerCancelTaskExecutor.getTaskPromise();
      const finalViewAnalyzerCancelStatus: TaskExecuteResponse = await viewAnalyzerCancelTask;
      expect(finalViewAnalyzerCancelStatus).equal(Status.FAILED);
    });
    it("Cancel View Analyzer triggered from System Monitor", async function () {
      const parameters: Parameters = {
        tf: { cancelId: 1, isDirect: true },
      };
      sinon.stub(CancelAnalyzer.prototype, "isRunningTask").resolves(true);
      const viewAnalyzerCancelTaskExecutor = await TaskExecutor.createDirect(
        createContext(),
        ApplicationId.VIEWS,
        Activity.CANCEL_VIEW_ANALYZER,
        entityName,
        spaceName,
        parameters
      );
      await viewAnalyzerCancelTaskExecutor.trigger();
      const viewAnalyzerCancelTask: Promise<TaskExecuteResponse> = viewAnalyzerCancelTaskExecutor.getTaskPromise();
      const finalViewAnalyzerCancelStatus: TaskExecuteResponse = await viewAnalyzerCancelTask;
      expect(finalViewAnalyzerCancelStatus).equal(Status.COMPLETED);
    });
    it("Cancel View Analyzer by using endpoint with POST method", async function () {
      const requestBody = {
        applicationId: "VIEWS",
        activity: "CANCEL_VIEW_ANALYZER",
        spaceId: spaceName,
        objectId: entityName,
        parameters: { [RequestBodyAttributes.TaskLogIdToCancel]: 1 },
      };
      const response = await agentWithDefaults(server).post(`/tf/directexecute`).send(requestBody);
      expect(response.status).to.equal(202);
    });
  });
});
