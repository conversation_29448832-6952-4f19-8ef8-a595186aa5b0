/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [DataIntegration.RemoteTableBackend]
 */

import { Connection } from "@sap/prom-hana-client";
import { expect, use } from "chai";
import chaiAsPromised from "chai-as-promised";
import sinon from "sinon";
import sinonChai from "sinon-chai";
// Added due to different order of initialization in tests
import "../../../../datasphereMetrics";
import { DatasphereMetricsCredentialsService } from "../../../../datasphereMetrics/credentials/DatasphereMetricsCredentialsService";
import { CredentialBaseError } from "../../../../datasphereMetrics/credentials/errors";
import { AuthTypes, BasicAuth, X509Auth } from "../../../../datasphereMetrics/credentials/types";
import { ConnectionBaseError } from "../../../../datasphereMetrics/exporter/errors";
import { HANAConnector, _createConnectionWrapper } from "../../../../datasphereMetrics/exporter/hana/HANAConnector";

use(chaiAsPromised);
use(sinonChai);

describe("HANAConnector", () => {
  let sandbox: sinon.SinonSandbox;
  let connectionMock: sinon.SinonStubbedInstance<Connection>;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    connectionMock = sinon.createStubInstance(Connection);
    sandbox.stub(_createConnectionWrapper, "createConnection").returns(connectionMock);
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe("connect()", () => {
    it("should connect using Basic credentials", async () => {
      const creds: BasicAuth = {
        schemaName: "METRICS_SCHEMA",
        authType: AuthTypes.Basic,
        host: "local",
        port: 443,
        user: "basicUser",
        password: "secret",
      };

      sandbox.stub(DatasphereMetricsCredentialsService.prototype, "fetch").resolves(creds);

      const db = new HANAConnector();
      const { schemaName } = await db.connect();
      expect(schemaName).to.equal("METRICS_SCHEMA");

      expect(connectionMock.connect).to.have.been.calledOnceWithExactly({
        host: creds.host,
        port: creds.port,
        user: `${creds.schemaName}#${creds.user}`,
        password: creds.password,
      });
      expect(connectionMock.setAutoCommit).to.have.been.calledWith(false);
    });

    it("should connect using refreshed X509 credentials when stored ones are invalid", async () => {
      const storedCreds: X509Auth = {
        schemaName: "METRICS_SCHEMA",
        authType: AuthTypes.X509,
        host: "x509.local",
        port: 443,
        user: "MY_USER",
        cert: "cccc",
        key: "kkkk",
        certSubject: "",
        certIssuer: "",
      };

      sandbox.stub(DatasphereMetricsCredentialsService.prototype, "fetch").resolves(storedCreds);

      const db = new HANAConnector();
      await db.connect();

      expect(connectionMock.connect).to.have.been.calledOnce;

      const [options] = connectionMock.connect.getCall(0).args;

      expect(options).to.include({
        host: "x509.local",
        port: 443,
      });
      expect(options).to.not.include({
        cert: storedCreds.cert,
        key: storedCreds.key,
      });

      expect(connectionMock.setAutoCommit).to.have.been.calledWith(false);
    });

    it("should throw ConnectionBaseError if connection creation fails", async () => {
      (_createConnectionWrapper.createConnection as sinon.SinonStub).throws(new Error("fail"));

      const db = new HANAConnector();

      await expect(db.connect()).to.be.rejectedWith("Failed to initialize connection instance");
    });

    it("should re-throw CredentialBaseError if fetch fails", async () => {
      const fetchStub = sandbox
        .stub(DatasphereMetricsCredentialsService.prototype, "fetch")
        .rejects(new CredentialBaseError("bad creds"));

      const db = new HANAConnector();

      await expect(db.connect()).to.be.rejectedWith(CredentialBaseError);
      expect(fetchStub).to.have.been.calledOnce;
      expect(connectionMock.setAutoCommit).to.have.been.calledWith(false);
    });

    it("should throw if connection.connect fails", async () => {
      const creds: BasicAuth = {
        schemaName: "METRICS_SCHEMA",
        authType: AuthTypes.Basic,
        host: "local",
        port: 443,
        user: "user",
        password: "pass",
      };

      sandbox.stub(DatasphereMetricsCredentialsService.prototype, "fetch").resolves(creds);
      connectionMock.connect.rejects(new ConnectionBaseError("connect failed"));

      const db = new HANAConnector();
      await expect(db.connect()).to.be.rejectedWith(ConnectionBaseError);
      expect(connectionMock.setAutoCommit).to.have.been.calledWith(false);
    });

    it("should retry connection up to 3 times before succeeding", async () => {
      const fetchStub = sandbox.stub(DatasphereMetricsCredentialsService.prototype, "fetch").resolves({
        schemaName: "METRICS_SCHEMA",
        authType: AuthTypes.Basic,
        host: "localhost",
        port: 443,
        user: "user",
        password: "pass",
      });

      const connectStub = connectionMock.connect;
      connectStub.onCall(0).rejects(new Error("fail 1"));
      connectStub.onCall(1).rejects(new Error("fail 2"));
      connectStub.onCall(2).resolves();

      const db = new HANAConnector();
      await db.connect();

      expect(connectStub.callCount).to.equal(3);
      expect(fetchStub).to.have.been.calledOnce;
      expect(connectionMock.setAutoCommit).to.have.been.calledWith(false);
    });

    it("should throw ConnectionBaseError if all retries fail", async () => {
      sandbox.stub(DatasphereMetricsCredentialsService.prototype, "fetch").resolves({
        schemaName: "METRICS_SCHEMA",
        authType: AuthTypes.Basic,
        host: "localhost",
        port: 443,
        user: "user",
        password: "pass",
      });

      const error = new Error("oopsie");
      connectionMock.connect.rejects(error);

      const db = new HANAConnector();

      await expect(db.connect()).to.be.rejectedWith("Failed to connect to Datasphere");
      expect(connectionMock.connect.callCount).to.equal(3);
      expect(connectionMock.setAutoCommit).to.have.been.calledWith(false);
    });

    it("should not reconnect if connection already exists", async () => {
      const db = new HANAConnector();
      (db as any).connection = connectionMock;

      const creds: BasicAuth = {
        schemaName: "MY_SCHEMA",
        authType: AuthTypes.Basic,
        host: "h",
        port: 123,
        user: "u",
        password: "p",
      };

      sandbox.stub(DatasphereMetricsCredentialsService.prototype, "fetch").resolves(creds);

      await db.connect();
      expect(connectionMock.connect).to.not.have.been.called;
    });
    describe("getConnectionParams()", () => {
      it("should correctly transform Basic credentials to connection params", () => {
        const db = new HANAConnector();
        const creds: BasicAuth = {
          schemaName: "s",
          authType: AuthTypes.Basic,
          host: "h",
          port: 443,
          user: "u",
          password: "p",
        };

        const result = (db as any).getConnectionParams(creds);
        expect(result).to.deep.equal({ host: "h", port: 443, user: "s#u", password: "p" });
      });
    });
  });

  describe("disconnect()", () => {
    it("should disconnect if connection is active", async () => {
      const db = new HANAConnector();
      // manually inject mocked connection for testing only
      (db as any).connection = connectionMock;

      await db.disconnect();

      expect(connectionMock.close).to.have.been.calledOnce;
    });

    it("should not throw if no active connection exists", async () => {
      const db = new HANAConnector();
      // manually inject mocked connection for testing only
      (db as any).connection = undefined;

      await expect(db.disconnect()).to.not.be.rejected;
      expect(connectionMock.close).to.not.have.been.called;
    });

    it("should log a warning if disconnect throws but not crash", async () => {
      const db = new HANAConnector();
      (db as any).connection = connectionMock;
      connectionMock.close.rejects(new Error("disconnect failed"));

      await expect(db.disconnect()).to.not.be.rejected;
      expect(connectionMock.close).to.have.been.called;
    });
  });
});
