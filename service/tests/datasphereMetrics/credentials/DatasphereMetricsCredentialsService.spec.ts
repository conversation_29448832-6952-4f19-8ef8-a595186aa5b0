/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [DataIntegration.RemoteTableBackend]
 */
import chai, { expect } from "chai";
import * as forge from "node-forge";
import sinon from "sinon";
import sinonChai from "sinon-chai";
// Added due to different order of initialization in tests
import "../../../datasphereMetrics";
import { formatSubjectOrIssuer } from "../../../routes/support/telemetryDatasphereMetrics";

import {
  DatasphereMetricsCredentialsService,
  STORE_KEY,
} from "../../../datasphereMetrics/credentials/DatasphereMetricsCredentialsService";
import { CredStoreEntryNotFoundError } from "../../../datasphereMetrics/credentials/errors";
import * as identityModule from "../../../datasphereMetrics/credentials/identity";
import { AuthTypes, BasicAuth, X509Auth } from "../../../datasphereMetrics/credentials/types";

import * as SecureStoreModule from "../../../securestore";
chai.use(sinonChai);

describe("DatasphereMetricsCredentialsService", () => {
  let storeStub: { insert: sinon.SinonStub; retrieve: sinon.SinonStub };
  let service: DatasphereMetricsCredentialsService;
  let createCertStub: sinon.SinonStub;

  function makeValidX509(days = 10): X509Auth {
    const keys = forge.pki.rsa.generateKeyPair(2048);
    const cert = forge.pki.createCertificate();
    cert.publicKey = keys.publicKey;
    cert.serialNumber = "01";
    const now = new Date();
    cert.validity.notBefore = now;
    cert.validity.notAfter = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
    cert.setSubject([{ shortName: "CN", value: "test" }]);
    cert.setIssuer([{ shortName: "CN", value: "testCA" }]);
    cert.sign(keys.privateKey, forge.md.sha256.create());
    return {
      schemaName: "METRICS_SCHEMA",
      authType: AuthTypes.X509,
      host: "host",
      port: 443,
      user: "MY_USER",
      cert: forge.pki.certificateToPem(cert),
      key: forge.pki.privateKeyToPem(keys.privateKey),
      certSubject: formatSubjectOrIssuer(cert, true),
      certIssuer: formatSubjectOrIssuer(cert, false),
    };
  }

  beforeEach(() => {
    storeStub = { insert: sinon.stub().resolves(), retrieve: sinon.stub().resolves() };
    sinon.stub(SecureStoreModule.SecureStore, "fromStoreName").returns(storeStub as any);

    createCertStub = sinon.stub(identityModule, "createUserCertificate").resolves({
      certificates: ["LEAF_CERT", "ROOT_CERT"],
      key: "PRIVATE_KEY",
    });
    service = new DatasphereMetricsCredentialsService();
  });

  afterEach(() => {
    sinon.restore();
  });

  it("should insert Basic credentials", async () => {
    const basic: BasicAuth = {
      schemaName: "METRICS_SCHEMA",
      authType: AuthTypes.Basic,
      host: "host",
      port: 443,
      user: "user",
      password: "pass",
    };

    await service.upsert(basic);
    expect(storeStub.insert).to.have.been.calledOnceWithExactly(STORE_KEY, basic);
  });

  it("should insert X509 credentials", async () => {
    const x509 = makeValidX509();
    await service.upsert(x509);
    expect(storeStub.insert).to.have.been.calledOnceWithExactly(STORE_KEY, x509);
  });

  it("should fetch Basic credentials as is", async () => {
    const basic: BasicAuth = {
      schemaName: "METRICS_SCHEMA",
      authType: AuthTypes.Basic,
      host: "host",
      port: 443,
      user: "user",
      password: "pass",
    };
    storeStub.retrieve.resolves(basic);
    const result = await service.fetch();
    expect(result).to.deep.equal(basic);
  });

  it("should fetch valid (not expiring) X.509 credentials as is", async () => {
    const x509 = makeValidX509(10);
    storeStub.retrieve.resolves(x509);
    const result = await service.fetch();
    if (result.authType === AuthTypes.X509) {
      expect(result.cert).to.equal(x509.cert);
      expect(result.key).to.equal(x509.key);
    } else {
      throw new Error("Expected X509 credentials");
    }
    expect(createCertStub).to.not.have.been.called;
  });

  it("should refresh X.509 if missing cert/key", async () => {
    const x509 = { ...makeValidX509(), cert: "", key: "" };
    storeStub.retrieve.resolves(x509);

    const keys = forge.pki.rsa.generateKeyPair(2048);
    const cert = forge.pki.createCertificate();
    cert.publicKey = keys.publicKey;
    cert.serialNumber = "01";
    const now = new Date();
    cert.validity.notBefore = now;
    cert.validity.notAfter = new Date(now.getTime() + 10 * 24 * 60 * 60 * 1000);
    cert.setSubject([{ shortName: "CN", value: "test" }]);
    cert.setIssuer([{ shortName: "CN", value: "testCA" }]);
    cert.sign(keys.privateKey, forge.md.sha256.create());
    const pemCert = forge.pki.certificateToPem(cert);
    const pemKey = forge.pki.privateKeyToPem(keys.privateKey);

    createCertStub.resolves({ certificates: [pemCert], key: pemKey });

    const result = await service.fetch();
    if (result.authType === AuthTypes.X509) {
      expect(result.cert).to.equal(pemCert);
      expect(result.key).to.equal(pemKey);
    }
    expect(createCertStub).to.have.been.called;
    expect(storeStub.insert).to.have.been.called;
  });

  it("should refresh X.509 if cert is expiring in < 3 days", async () => {
    const x509 = makeValidX509(1);
    storeStub.retrieve.resolves(x509);

    const { cert: pemCert, key: pemKey } = makeValidX509();
    createCertStub.resolves({ certificates: [pemCert], key: pemKey });

    const result = await service.fetch();
    if (result.authType === AuthTypes.X509) {
      expect(result.cert).to.equal(pemCert);
      expect(result.key).to.equal(pemKey);
    }
    expect(createCertStub).to.have.been.called;
    expect(storeStub.insert).to.have.been.called;
  });

  it("should refresh X.509 if cert cannot be parsed", async () => {
    const x509 = { ...makeValidX509(), cert: "NOT_A_CERT" };
    storeStub.retrieve.resolves(x509);

    const { cert: pemCert, key: pemKey } = makeValidX509();

    createCertStub.resolves({ certificates: [pemCert], key: pemKey });

    const result = await service.fetch();
    if (result.authType === AuthTypes.X509) {
      expect(result.cert).to.equal(pemCert);
      expect(result.key).to.equal(pemKey);
    }
    expect(createCertStub).to.have.been.called;
    expect(storeStub.insert).to.have.been.called;
  });

  it("should throw CredStoreEntryNotFoundError if not found", async () => {
    storeStub.retrieve.resolves(undefined);
    try {
      await service.fetch();
      throw new Error("Expected fetch to reject!");
    } catch (err) {
      expect(err).to.be.instanceOf(CredStoreEntryNotFoundError);
    }
  });
});
