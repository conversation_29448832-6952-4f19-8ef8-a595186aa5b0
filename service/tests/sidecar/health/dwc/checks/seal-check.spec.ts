/** @format */

import { expect } from "chai";
import { HealthRequestContext } from "../../../../../sidecar/context/index.js";
import { SealCheck } from "../../../../../sidecar/health/dwc/checks/seal-check.js";
import { StatusCodeHealth, TimeoutStatusCode } from "../../../../../sidecar/health/types.js";
import { EnvUtils } from "../../../../../sidecar/utils/env-utils.js";

/**
 * In Offline Mode Tests rely on mock provided in deploy/mocks/seal.js
 */
describe("/service/sidecar/health/dwc/checks/seal-check", function () {
  describe("SealCheck", function () {
    it("should use /health as the default endpoint when no path is provided", function () {
      const timeoutSignal = AbortSignal.timeout(1000);
      const instance = new SealCheck(timeoutSignal);
      // eslint-disable-next-line dot-notation
      expect(instance["endpoint"]).to.satisfy((endpoint: string) => endpoint.endsWith("/health"));
    });

    it.skip("should handle successful health check", async function () {
      const fakeContext = {} as HealthRequestContext;
      const timeoutSignal = AbortSignal.timeout(1000);
      const instance = new SealCheck(timeoutSignal);

      const result = await instance.check(fakeContext);

      expect(result).to.be.deep.equal({
        service: "seal",
        status: "UP",
        type: "OPTIONAL",
        statusCodes: [],
      });
    });

    // These tests expect to modulate the external response from the service which is not possible in online mode
    if (EnvUtils.isLocalHanaMocked()) {
      it.skip("should handle failed health check", async function () {
        const fakeContext = {} as HealthRequestContext;
        const timeoutSignal = AbortSignal.timeout(1000);
        const instance = new SealCheck(timeoutSignal);

        // eslint-disable-next-line dot-notation
        instance["endpoint"] = instance["endpoint"] + "?status=500";

        const result = await instance.check(fakeContext);

        expect(result).to.be.deep.equal({
          service: "seal",
          status: "DOWN",
          type: "OPTIONAL",
          statusCodes: [StatusCodeHealth.DeployerNotAvailable],
          message: 'seal Service is not reachable: {"health":"DOWN"}',
        });
      });

      it("should handle timeout error", async function () {
        const fakeContext = {} as HealthRequestContext;
        const timeoutSignal = AbortSignal.timeout(100);
        const instance = new SealCheck(timeoutSignal);

        // eslint-disable-next-line dot-notation
        instance["endpoint"] = instance["endpoint"] + "?delayMs=150";

        const result = await instance.check(fakeContext);

        expect(result).to.be.deep.equal({
          service: "seal",
          status: "DOWN",
          type: "OPTIONAL",
          statusCodes: [TimeoutStatusCode.SealTimeout],
          message: "seal Service Health Check request timed out: The user aborted a request.",
        });
      });
    }
  });
});
