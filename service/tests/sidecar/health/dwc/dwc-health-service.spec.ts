/** @format */

import { ServiceType } from "@sap/deepsea-types";
import { expect } from "chai";
import { StatusCodes } from "http-status-codes";
import sinon from "sinon";
import { HealthRequestContext } from "../../../../sidecar/context/index.js";
import { TenantHanaUpgradeState } from "../../../../sidecar/hana/upgrade-state.js";
import { CredStoreCheck } from "../../../../sidecar/health/checks/cred-store-check.js";
import { TmsCheck } from "../../../../sidecar/health/checks/tms-check.js";
import { CustomerHanaCheck } from "../../../../sidecar/health/dwc/checks/customer-hana-check.js";
import { DeepseaCheck } from "../../../../sidecar/health/dwc/checks/deepsea-check.js";
import { DwcTenantEnabledCheck } from "../../../../sidecar/health/dwc/checks/dwc-tenant-enabled-check.js";
import { UserManagerCheck } from "../../../../sidecar/health/dwc/checks/usermanager-check.js";
import { DwcHealthService } from "../../../../sidecar/health/dwc/dwc-health-service.js";
import { Health, IHealth, StatusCode, StatusCodeHealth } from "../../../../sidecar/health/types.js";
import { EnvUtils } from "../../../../sidecar/utils/env-utils.js";

// Taken from existing implementation - Anonymous
const expectedResNoAuth = [];

// Taken from existing implementation - Authenticated
const expectedResAuth = {
  status: "UP",
  statusCode: StatusCodes.OK,
  statusCodes: [],
  services: [
    {
      service: "deepsea",
      type: "OPTIONAL",
      status: "UP",
      statusCodes: [],
    },
    {
      service: "cred-store-health-check",
      type: "OPTIONAL",
      status: "UP",
      statusCodes: [],
    },
    {
      service: "tenant-management",
      type: "OPTIONAL",
      status: "UP",
      statusCodes: [],
    },
    {
      service: "user-management-service",
      type: "OPTIONAL",
      status: "UP",
      statusCodes: [],
    },
    {
      service: "seal",
      type: "OPTIONAL",
      status: "UP",
      statusCodes: [],
    },
    {
      service: "cred-store-quota",
      type: "OPTIONAL",
      status: "UP",
      statusCodes: [],
      quota: {
        allowedQuota: {
          bindings: 10000,
          credentials: 100000,
          storage: 1024,
          namespaces: 100000,
          proxyInstances: 100,
        },
        usedQuota: {
          bindings: 59,
          credentials: 7820,
          storage: 17.77,
          namespaces: 3564,
          proxyInstances: 0,
        },
        percents: {
          bindings: 0.59,
          credentials: 7.82,
          storage: 1.74,
          namespaces: 3.56,
        },
      },
    },
    {
      service: "dataMarketplace",
      status: "UP",
      statusCodes: [],
      type: "OPTIONAL",
    },
    {
      service: "dragonet",
      status: "UP",
      statusCodes: [],
      type: "OPTIONAL",
    },
  ],
};

const expectedResAuthSingleFailure = {
  status: "DOWN",
  statusCode: StatusCodeHealth.DeepseaDown,
  statusCodes: [StatusCodeHealth.DeepseaDown],
  services: [
    {
      service: "deepseaFake",
      type: "MANDATORY",
      status: "DOWN",
      statusCodes: [StatusCodeHealth.DeepseaDown],
    },
    {
      service: "cred-store-health-check",
      type: "OPTIONAL",
      status: "UP",
      statusCodes: [],
    },
    {
      service: "tenant-management",
      type: "OPTIONAL",
      status: "UP",
      statusCodes: [],
    },
    {
      service: "user-management-service",
      type: "OPTIONAL",
      status: "UP",
      statusCodes: [],
    },
    {
      service: "seal",
      type: "OPTIONAL",
      status: "UP",
      statusCodes: [],
    },
    {
      service: "cred-store-quota",
      type: "OPTIONAL",
      status: "UP",
      statusCodes: [],
      quota: {
        allowedQuota: {
          bindings: 10000,
          credentials: 100000,
          storage: 1024,
          namespaces: 100000,
          proxyInstances: 100,
        },
        usedQuota: {
          bindings: 59,
          credentials: 7820,
          storage: 17.77,
          namespaces: 3564,
          proxyInstances: 0,
        },
        percents: {
          bindings: 0.59,
          credentials: 7.82,
          storage: 1.74,
          namespaces: 3.56,
        },
      },
    },
    {
      service: "dataMarketplace",
      status: "UP",
      statusCodes: [],
      type: "OPTIONAL",
    },
    {
      service: "dragonet",
      status: "UP",
      statusCodes: [],
      type: "OPTIONAL",
    },
  ],
};

const expectedResAuthMultipleFailure = {
  status: "DOWN",
  statusCode: StatusCode.MultipleErrors,
  statusCodes: [StatusCodeHealth.DeepseaDown, StatusCodeHealth.CredStoreDown],
  services: [
    {
      service: "deepseaFake",
      type: "MANDATORY",
      status: "DOWN",
      statusCodes: [StatusCodeHealth.DeepseaDown],
    },
    {
      service: "credStoreFake",
      type: "MANDATORY",
      status: "DOWN",
      statusCodes: [StatusCodeHealth.CredStoreDown],
    },
    {
      service: "tmsCheckFake",
      type: "OPTIONAL",
      status: "DOWN",
      statusCodes: [StatusCodeHealth.TmsDown],
    },
    {
      service: "user-management-service",
      type: "OPTIONAL",
      status: "UP",
      statusCodes: [],
    },
    {
      service: "seal",
      type: "OPTIONAL",
      status: "UP",
      statusCodes: [],
    },
    {
      service: "cred-store-quota",
      type: "OPTIONAL",
      status: "UP",
      statusCodes: [],
      quota: {
        allowedQuota: {
          bindings: 10000,
          credentials: 100000,
          storage: 1024,
          namespaces: 100000,
          proxyInstances: 100,
        },
        usedQuota: {
          bindings: 59,
          credentials: 7820,
          storage: 17.77,
          namespaces: 3564,
          proxyInstances: 0,
        },
        percents: {
          bindings: 0.59,
          credentials: 7.82,
          storage: 1.74,
          namespaces: 3.56,
        },
      },
    },
    {
      service: "dataMarketplace",
      status: "UP",
      statusCodes: [],
      type: "OPTIONAL",
    },
    {
      service: "dragonet",
      status: "UP",
      statusCodes: [],
      type: "OPTIONAL",
    },
  ],
};

describe.skip("/service/sidecar/health/health-service", function () {
  this.timeout(5000);

  afterEach(function () {
    sinon.restore();
  });

  describe("health", function () {
    describe("anoynmous access", function () {
      const fakeAnonymContext = {} as unknown as HealthRequestContext;

      it("should return only status codes when jwtToken is not set", async function () {
        const service = new DwcHealthService({ checkTimeoutMs: 30000 });
        const result = await service.healthCheck(fakeAnonymContext);
        expect(result).to.not.be.undefined;
        expect(result).to.be.deep.eq({
          overallStatusCode: StatusCodes.OK,
          value: expectedResNoAuth,
        });
      });

      it("should return failing status code when a single mandatory check fails", async function () {
        sinon.stub(DeepseaCheck.prototype, "check").resolves({
          service: DeepseaCheck.name,
          status: Health.DOWN,
          type: ServiceType.MAND,
          statusCodes: [StatusCodeHealth.DeepseaDown],
        } as IHealth);

        const service = new DwcHealthService({ checkTimeoutMs: 30000 });
        const result = await service.healthCheck(fakeAnonymContext);
        expect(result).to.not.be.undefined;
        expect(result).to.be.deep.eq({
          overallStatusCode: StatusCodeHealth.DeepseaDown,
          value: [StatusCodeHealth.DeepseaDown],
        });
      });

      it("should return all failing status code when a multiple mandatory check fails", async function () {
        sinon.stub(DeepseaCheck.prototype, "check").resolves({
          service: "deepseaFake",
          status: Health.DOWN,
          type: ServiceType.MAND,
          statusCodes: [StatusCodeHealth.DeepseaDown],
        } as IHealth);
        sinon.stub(CredStoreCheck.prototype, "check").resolves({
          service: "credStoreFake",
          status: Health.DOWN,
          type: ServiceType.MAND,
          statusCodes: [StatusCodeHealth.CredStoreDown],
        } as IHealth);
        // Optional checks are not reported
        sinon.stub(TmsCheck.prototype, "check").resolves({
          service: "tmsCheckFake",
          status: Health.DOWN,
          type: ServiceType.OPT,
          statusCodes: [StatusCodeHealth.TmsDown],
        } as IHealth);

        const service = new DwcHealthService({ checkTimeoutMs: 30000 });
        const result = await service.healthCheck(fakeAnonymContext);
        expect(result).to.not.be.undefined;
        expect(result).to.be.deep.eq({
          overallStatusCode: StatusCode.MultipleErrors,
          value: [StatusCodeHealth.DeepseaDown, StatusCodeHealth.CredStoreDown],
        });
      });

      it("should only ever have a single health check execution", async function () {
        const deepseaCheckStub = sinon.stub(DeepseaCheck.prototype, "check").callsFake(async (context) => {
          await new Promise((resolve) => setTimeout(resolve, 200));
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore - sinon adds a wrappedMethod property to the function
          return DeepseaCheck.prototype.wrappedMethod(context);
        });

        const service = new DwcHealthService({ checkTimeoutMs: 30000 });
        const result1 = service.healthCheck(fakeAnonymContext);
        const result2 = service.healthCheck(fakeAnonymContext);
        const result3 = service.healthCheck(fakeAnonymContext);
        // eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled
        await Promise.all([result1, result2, result3]);
        expect(deepseaCheckStub.calledOnce).to.be.true;
        const result4 = service.healthCheck(fakeAnonymContext);
        await result4;
        expect(deepseaCheckStub.calledTwice).to.be.true;
      });
    });
    for (const removeAuthHealthChecks of [true, false]) {
      describe(`authenticated access ${removeAuthHealthChecks ? "without" : "with"} additional checks`, function () {
        const fakeAuthContext = { jwtToken: "fake" } as unknown as HealthRequestContext;
        let expectedResAuthSub;
        let expectedResAuthSingleFailureSub;
        let expectedResAuthMultipleFailureSub;

        let originalEnv;
        before(() => {
          originalEnv = process.env.REMOVE_AUTHORIZED_HEALTH_CHECKS;
          process.env.REMOVE_AUTHORIZED_HEALTH_CHECKS = removeAuthHealthChecks.toString();
          if (removeAuthHealthChecks) {
            const removedServices = (s) =>
              s.service !== "cred-store-quota" &&
              s.service !== "seal" &&
              s.service !== "dragonet" &&
              s.service !== "dataMarketplace";

            expectedResAuthSub = {
              ...expectedResAuth,
              ...{ services: expectedResAuth.services.filter((s) => removedServices(s)) },
            };
            expectedResAuthSingleFailureSub = {
              ...expectedResAuthSingleFailure,
              ...{
                services: expectedResAuthSingleFailure.services.filter((s) => removedServices(s)),
              },
            };
            expectedResAuthMultipleFailureSub = {
              ...expectedResAuthMultipleFailure,
              ...{ services: expectedResAuthMultipleFailure.services.filter((s) => removedServices(s)) },
            };
          } else {
            expectedResAuthSub = expectedResAuth;
            expectedResAuthSingleFailureSub = expectedResAuthSingleFailure;
            expectedResAuthMultipleFailureSub = expectedResAuthMultipleFailure;
          }
        });
        after(() => {
          process.env.REMOVE_AUTHORIZED_HEALTH_CHECKS = originalEnv;
        });

        it("should return full result when jwtToken is set", async function () {
          const service = new DwcHealthService({ checkTimeoutMs: 30000 });
          const result = await service.healthCheck(fakeAuthContext);
          expect(result).to.not.be.undefined;
          expect(result).to.be.deep.eq({
            overallStatusCode: StatusCodes.OK,
            value: expectedResAuthSub,
          });
        });

        it("should return failing status code when a single mandatory check fails", async function () {
          sinon.stub(DeepseaCheck.prototype, "check").resolves({
            service: "deepseaFake",
            status: Health.DOWN,
            type: ServiceType.MAND,
            statusCodes: [StatusCodeHealth.DeepseaDown],
          } as IHealth);

          const service = new DwcHealthService({ checkTimeoutMs: 30000 });
          const result = await service.healthCheck(fakeAuthContext);
          expect(result).to.not.be.undefined;
          expect(result).to.be.deep.eq({
            overallStatusCode: StatusCodeHealth.DeepseaDown,
            value: expectedResAuthSingleFailureSub,
          });
        });

        it("should return all failing status code when a multiple mandatory check fails", async function () {
          sinon.stub(DeepseaCheck.prototype, "check").resolves({
            service: "deepseaFake",
            status: Health.DOWN,
            type: ServiceType.MAND,
            statusCodes: [StatusCodeHealth.DeepseaDown],
          } as IHealth);
          sinon.stub(CredStoreCheck.prototype, "check").resolves({
            service: "credStoreFake",
            status: Health.DOWN,
            type: ServiceType.MAND,
            statusCodes: [StatusCodeHealth.CredStoreDown],
          } as IHealth);
          // Optional checks are not reported
          sinon.stub(TmsCheck.prototype, "check").resolves({
            service: "tmsCheckFake",
            status: Health.DOWN,
            type: ServiceType.OPT,
            statusCodes: [StatusCodeHealth.TmsDown],
          } as IHealth);

          const service = new DwcHealthService({ checkTimeoutMs: 30000 });
          const result = await service.healthCheck(fakeAuthContext);
          expect(result).to.not.be.undefined;
          expect(result).to.be.deep.eq({
            overallStatusCode: StatusCode.MultipleErrors,
            value: expectedResAuthMultipleFailureSub,
          });
        });
      });
    }
  });

  describe("healthTenants", function () {
    const tenantId = EnvUtils.isLocalHanaMocked() ? "DWC_DEV_REPO" : "02da35ed-4897-4e21-9223-4e64b08f7ce9";

    describe("anonymous access", function () {
      const fakeNoAuthTenantContext = { tenantId, userInfo: { tenantId } } as unknown as HealthRequestContext;

      it("should return only status codes when jwtToken is not set", async function () {
        const service = new DwcHealthService({ checkTimeoutMs: 30000 });
        const result = await service.tenantHealthCheck(fakeNoAuthTenantContext);
        expect(result).to.not.be.undefined;
        expect(result).to.be.deep.eq({
          overallStatusCode: StatusCodes.OK,
          value: expectedResNoAuth,
        });
      });

      it("should return failing status code when a single mandatory check fails", async function () {
        sinon.stub(CustomerHanaCheck.prototype, "check").resolves({
          service: "customerHanaFake",
          status: Health.DOWN,
          type: ServiceType.MAND,
          statusCodes: [StatusCodeHealth.CustomerHanaCloudNotReachable],
        } as IHealth);

        const service = new DwcHealthService({ checkTimeoutMs: 30000 });
        const result = await service.tenantHealthCheck(fakeNoAuthTenantContext);
        expect(result).to.not.be.undefined;
        expect(result).to.be.deep.eq({
          overallStatusCode: StatusCodeHealth.CustomerHanaCloudNotReachable,
          value: [StatusCodeHealth.CustomerHanaCloudNotReachable],
        });
      });

      it("should skip other checks when first pre-check (TMS) fails", async function () {
        sinon.stub(DwcTenantEnabledCheck.prototype, "check").resolves({
          service: "tenantEnabledFake",
          status: Health.DOWN,
          type: ServiceType.MAND,
          statusCodes: [StatusCode.NotDwcEnabled],
        } as IHealth);
        const customerHanaCheckStub = sinon.stub(CustomerHanaCheck.prototype, "check").resolves({
          service: "customerHanaFake",
          status: Health.DOWN,
          type: ServiceType.MAND,
          statusCodes: [StatusCodeHealth.CustomerHanaCloudNotReachable],
        } as IHealth);

        const service = new DwcHealthService({ checkTimeoutMs: 30000 });
        const result = await service.tenantHealthCheck(fakeNoAuthTenantContext);
        expect(result).to.not.be.undefined;
        expect(result).to.be.deep.eq({
          overallStatusCode: StatusCode.NotDwcEnabled,
          value: [StatusCode.NotDwcEnabled],
        });
        expect(customerHanaCheckStub.notCalled).to.be.true;
      });

      it("should skip other checks when first pre-check (TMS) shows tenant is not enabled", async function () {
        sinon.stub(DwcTenantEnabledCheck.prototype, "check").resolves({
          service: "tenantEnabledFake",
          status: Health.DOWN,
          type: ServiceType.MAND,
          statusCodes: [StatusCode.NotDwcEnabled],
          tms: {
            dwcEnabled: false,
            dwcLocked: true,
          },
        } as unknown as IHealth);
        const customerHanaCheckStub = sinon.stub(CustomerHanaCheck.prototype, "check").resolves({
          service: "customerHanaFake",
          status: Health.DOWN,
          type: ServiceType.MAND,
          statusCodes: [StatusCodeHealth.CustomerHanaCloudNotReachable],
        } as IHealth);

        const service = new DwcHealthService({ checkTimeoutMs: 30000 });
        const result = await service.tenantHealthCheck(fakeNoAuthTenantContext);
        expect(result).to.not.be.undefined;
        expect(result).to.be.deep.eq({
          overallStatusCode: StatusCode.NotDwcEnabled,
          value: [StatusCode.NotDwcEnabled],
        });
        expect(customerHanaCheckStub.notCalled).to.be.true;
      });

      it("should skip other checks when first pre-check (TMS) shows tenant enabled but locked", async function () {
        sinon.stub(DwcTenantEnabledCheck.prototype, "check").resolves({
          service: "tenantEnabledFake",
          status: Health.WARN,
          type: ServiceType.MAND,
          statusCodes: [StatusCode.TenantLocked],
          tms: {
            dwcEnabled: true,
            dwcLocked: true,
          },
        } as unknown as IHealth);
        const customerHanaCheckStub = sinon.stub(CustomerHanaCheck.prototype, "check").resolves({
          service: "customerHanaFake",
          status: Health.DOWN,
          type: ServiceType.MAND,
          statusCodes: [StatusCodeHealth.CustomerHanaCloudNotReachable],
        } as IHealth);

        const service = new DwcHealthService({ checkTimeoutMs: 30000 });
        const result = await service.tenantHealthCheck(fakeNoAuthTenantContext);
        expect(result).to.not.be.undefined;
        expect(result).to.be.deep.eq({
          overallStatusCode: StatusCodes.OK,
          value: [],
        });
        expect(customerHanaCheckStub.notCalled).to.be.true;
      });

      it("should skip other checks when second pre-check (UserManager) fails", async function () {
        sinon.stub(UserManagerCheck.prototype, "check").resolves({
          service: "userManagerFake",
          status: Health.DOWN,
          type: ServiceType.MAND,
          statusCodes: [StatusCodeHealth.CredStoreCannotFindCredentials],
          tms: {
            dwcEnabled: true,
            dwcLocked: true,
          },
        } as unknown as IHealth);
        const customerHanaCheckStub = sinon.stub(CustomerHanaCheck.prototype, "check").resolves({
          service: "customerHanaFake",
          status: Health.DOWN,
          type: ServiceType.MAND,
          statusCodes: [StatusCodeHealth.CustomerHanaCloudNotReachable],
        } as IHealth);

        const service = new DwcHealthService({ checkTimeoutMs: 30000 });
        const result = await service.tenantHealthCheck(fakeNoAuthTenantContext);
        expect(result).to.not.be.undefined;
        expect(result).to.be.deep.eq({
          overallStatusCode: StatusCodeHealth.CredStoreCannotFindCredentials,
          value: [StatusCodeHealth.CredStoreCannotFindCredentials],
        });
        expect(customerHanaCheckStub.notCalled).to.be.true;
      });

      it("should only ever have a single health check execution", async function () {
        const customerHanaCheckStub = sinon.stub(CustomerHanaCheck.prototype, "check").callsFake(async (context) => {
          await new Promise((resolve) => setTimeout(resolve, 200));
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore - sinon adds a wrappedMethod property to the function
          return CustomerHanaCheck.prototype.wrappedMethod(context);
        });

        const service = new DwcHealthService({ checkTimeoutMs: 30000 });
        const result1 = service.tenantHealthCheck(fakeNoAuthTenantContext);
        const result2 = service.tenantHealthCheck(fakeNoAuthTenantContext);
        const result3 = service.tenantHealthCheck(fakeNoAuthTenantContext);
        expect(result1).to.not.be.eq(result2);
        expect(result1).to.not.be.eq(result3);
        // eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled
        await Promise.all([result1, result2, result3]);
        expect(customerHanaCheckStub.calledOnce).to.be.true;
        const result4 = service.tenantHealthCheck(fakeNoAuthTenantContext);
        expect(result1).to.not.be.eq(result4);
        await result4;
        expect(customerHanaCheckStub.calledTwice, `Called ${customerHanaCheckStub.callCount} times`).to.be.true;
      });
    });
    for (const removeAuthHealthChecks of [true, false]) {
      describe(`authenticated access ${removeAuthHealthChecks ? "without" : "with"} additional checks`, function () {
        let originalEnv;

        before(() => {
          originalEnv = process.env.REMOVE_AUTHORIZED_HEALTH_CHECKS;
          process.env.REMOVE_AUTHORIZED_HEALTH_CHECKS = removeAuthHealthChecks.toString();
        });

        after(() => {
          process.env.REMOVE_AUTHORIZED_HEALTH_CHECKS = originalEnv;
        });

        const fakeAuthTenantContext = {
          tenantId,
          userInfo: { tenantId },
          jwtToken: "fakeToken",
        } as unknown as HealthRequestContext;

        it("should return full health status when jwtToken is set", async function () {
          const service = new DwcHealthService({ checkTimeoutMs: 30000 });
          const result = await service.tenantHealthCheck(fakeAuthTenantContext);
          expect(result).to.not.be.undefined;
          expect(result).to.be.deep.eq({
            overallStatusCode: StatusCodes.OK,
            value: {
              status: "UP",
              tenantId,
              statusCode: StatusCodes.OK,
              statusCodes: [],
              services: [
                {
                  service: "tenant-management",
                  status: "UP",
                  statusCodes: [],
                  tms: {
                    bdcEnabled: false,
                    dwcEnabled: true,
                    locked: false,
                    xsn: false,
                  },
                  type: "MANDATORY",
                },
                {
                  service: "user-manager",
                  status: "UP",
                  statusCodes: [],
                  type: "MANDATORY",
                },
                {
                  service: "customer-hana",
                  status: "UP",
                  statusCodes: [],
                  type: "MANDATORY",
                },
                ...(removeAuthHealthChecks
                  ? []
                  : [
                      {
                        service: "dwc-di-service",
                        status: "UP",
                        statusCodes: [],
                        type: "OPTIONAL",
                      },
                    ]),
              ],
            },
          });
        });

        it("should return failing status code when a single mandatory check fails", async function () {
          sinon.stub(CustomerHanaCheck.prototype, "check").resolves({
            service: "customerHanaFake",
            status: Health.DOWN,
            type: ServiceType.MAND,
            statusCodes: [StatusCodeHealth.CustomerHanaCloudNotReachable],
          } as IHealth);

          const service = new DwcHealthService({ checkTimeoutMs: 30000 });
          const result = await service.tenantHealthCheck(fakeAuthTenantContext);
          expect(result).to.not.be.undefined;
          expect(result).to.be.deep.eq({
            overallStatusCode: StatusCodeHealth.CustomerHanaCloudNotReachable,
            value: {
              status: "DOWN",
              tenantId,
              statusCode: StatusCodeHealth.CustomerHanaCloudNotReachable,
              statusCodes: [StatusCodeHealth.CustomerHanaCloudNotReachable],
              services: [
                {
                  service: "tenant-management",
                  status: "UP",
                  statusCodes: [],
                  tms: {
                    bdcEnabled: false,
                    dwcEnabled: true,
                    locked: false,
                    xsn: false,
                  },
                  type: "MANDATORY",
                },
                {
                  service: "user-manager",
                  status: "UP",
                  statusCodes: [],
                  type: "MANDATORY",
                },
                {
                  service: "customerHanaFake",
                  status: "DOWN",
                  statusCodes: [StatusCodeHealth.CustomerHanaCloudNotReachable],
                  type: "MANDATORY",
                },
                ...(removeAuthHealthChecks
                  ? []
                  : [
                      {
                        service: "dwc-di-service",
                        status: "UP",
                        statusCodes: [],
                        type: "OPTIONAL",
                      },
                    ]),
              ],
            },
          });
        });

        it("should skip other checks when first pre-check (TMS) fails", async function () {
          sinon.stub(DwcTenantEnabledCheck.prototype, "check").rejects(new Error("Unexpected error"));
          const customerHanaCheckStub = sinon.stub(CustomerHanaCheck.prototype, "check").resolves({
            service: "customerHanaFake",
            status: Health.DOWN,
            type: ServiceType.MAND,
            statusCodes: [StatusCodeHealth.CustomerHanaCloudNotReachable],
          } as IHealth);

          const service = new DwcHealthService({ checkTimeoutMs: 30000 });
          const result = await service.tenantHealthCheck(fakeAuthTenantContext);
          expect(result).to.not.be.undefined;
          expect(result).to.be.deep.eq({
            overallStatusCode: StatusCode.UnexpectedError,
            value: {
              message: "Health check execution failed for one or more services",
              status: "DOWN",
              tenantId,
              statusCode: StatusCode.UnexpectedError,
              statusCodes: [StatusCode.UnexpectedError],
              services: [
                {
                  message: "Unexpected error",
                  service: "tenant-management",
                  status: "DOWN",
                  statusCodes: [915],
                  type: "MANDATORY",
                },
              ],
            },
          });
          expect(customerHanaCheckStub.notCalled).to.be.true;
        });

        it("should skip other checks when first pre-check (TMS) shows tenant is not enabled", async function () {
          sinon.stub(DwcTenantEnabledCheck.prototype, "check").resolves({
            service: "tenantEnabledFake",
            status: Health.DOWN,
            type: ServiceType.MAND,
            statusCodes: [StatusCode.NotDwcEnabled],
            tms: {
              bdcEnabled: false,
              dwcEnabled: false,
              locked: false,
              xsn: false,
            },
          } as unknown as IHealth);
          const customerHanaCheckStub = sinon.stub(CustomerHanaCheck.prototype, "check").resolves({
            service: "customerHanaFake",
            status: Health.DOWN,
            type: ServiceType.MAND,
            statusCodes: [StatusCodeHealth.CustomerHanaCloudNotReachable],
          } as IHealth);

          const service = new DwcHealthService({ checkTimeoutMs: 30000 });
          const result = await service.tenantHealthCheck(fakeAuthTenantContext);
          expect(result).to.not.be.undefined;
          expect(result).to.be.deep.eq({
            overallStatusCode: StatusCode.NotDwcEnabled,
            value: {
              status: "DOWN",
              tenantId,
              statusCode: StatusCode.NotDwcEnabled,
              statusCodes: [StatusCode.NotDwcEnabled],
              services: [
                {
                  service: "tenantEnabledFake",
                  status: "DOWN",
                  statusCodes: [StatusCode.NotDwcEnabled],
                  tms: {
                    bdcEnabled: false,
                    dwcEnabled: false,
                    locked: false,
                    xsn: false,
                  },
                  type: "MANDATORY",
                },
              ],
            },
          });
          expect(customerHanaCheckStub.notCalled).to.be.true;
        });

        it("should skip other checks when first pre-check (TMS) shows tenant enabled but locked", async function () {
          sinon.stub(DwcTenantEnabledCheck.prototype, "check").resolves({
            service: "tenantEnabledFake",
            status: Health.WARN,
            type: ServiceType.MAND,
            statusCodes: [StatusCode.TenantLocked],
            tms: {
              dwcEnabled: true,
              dwcLocked: true,
            },
          } as unknown as IHealth);
          const customerHanaCheckStub = sinon.stub(CustomerHanaCheck.prototype, "check").resolves({
            service: "customerHanaFake",
            status: Health.DOWN,
            type: ServiceType.MAND,
            statusCodes: [StatusCodeHealth.CustomerHanaCloudNotReachable],
          } as IHealth);

          const service = new DwcHealthService({ checkTimeoutMs: 30000 });
          const result = await service.tenantHealthCheck(fakeAuthTenantContext);
          expect(result).to.not.be.undefined;
          expect(result).to.be.deep.eq({
            overallStatusCode: StatusCodes.OK,
            value: {
              status: "WARN",
              tenantId,
              statusCode: StatusCodes.OK,
              statusCodes: [],
              services: [
                {
                  service: "tenantEnabledFake",
                  status: "WARN",
                  statusCodes: [StatusCode.TenantLocked],
                  tms: {
                    dwcEnabled: true,
                    dwcLocked: true,
                  },
                  type: "MANDATORY",
                },
              ],
            },
          });
          expect(customerHanaCheckStub.notCalled).to.be.true;
        });

        it("should skip other checks when second pre-check (UserManager) fails", async function () {
          sinon.stub(UserManagerCheck.prototype, "check").resolves({
            service: "userManagerFake",
            status: Health.DOWN,
            type: ServiceType.MAND,
            statusCodes: [StatusCodeHealth.CredStoreCannotFindCredentials],
            tms: {
              dwcEnabled: true,
              dwcLocked: true,
            },
          } as unknown as IHealth);
          const customerHanaCheckStub = sinon.stub(CustomerHanaCheck.prototype, "check").resolves({
            service: "customerHanaFake",
            status: Health.DOWN,
            type: ServiceType.MAND,
            statusCodes: [StatusCodeHealth.CustomerHanaCloudNotReachable],
          } as IHealth);

          const service = new DwcHealthService({ checkTimeoutMs: 30000 });
          const result = await service.tenantHealthCheck(fakeAuthTenantContext);
          expect(result).to.not.be.undefined;
          expect(result).to.be.deep.eq({
            overallStatusCode: StatusCodeHealth.CredStoreCannotFindCredentials,
            value: {
              status: "DOWN",
              tenantId,
              statusCode: StatusCodeHealth.CredStoreCannotFindCredentials,
              statusCodes: [StatusCodeHealth.CredStoreCannotFindCredentials],
              services: [
                {
                  service: "tenant-management",
                  status: "UP",
                  statusCodes: [],
                  tms: {
                    bdcEnabled: false,
                    dwcEnabled: true,
                    locked: false,
                    xsn: false,
                  },
                  type: "MANDATORY",
                },
                {
                  service: "userManagerFake",
                  status: "DOWN",
                  statusCodes: [908],
                  tms: {
                    dwcEnabled: true,
                    dwcLocked: true,
                  },
                  type: "MANDATORY",
                },
              ],
            },
          });
          expect(customerHanaCheckStub.notCalled).to.be.true;
        });

        it("should only ever have a single health check execution", async function () {
          const customerHanaCheckStub = sinon.stub(CustomerHanaCheck.prototype, "check").callsFake(async (context) => {
            await new Promise((resolve) => setTimeout(resolve, 200));
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore - sinon adds a wrappedMethod property to the function
            return CustomerHanaCheck.prototype.wrappedMethod(context);
          });

          const service = new DwcHealthService({ checkTimeoutMs: 30000 });
          const result1 = service.tenantHealthCheck(fakeAuthTenantContext);
          const result2 = service.tenantHealthCheck(fakeAuthTenantContext);
          const result3 = service.tenantHealthCheck(fakeAuthTenantContext);
          // eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled
          await Promise.all([result1, result2, result3]);
          expect(customerHanaCheckStub.calledOnce).to.be.true;
          const result4 = service.tenantHealthCheck(fakeAuthTenantContext);
          await result4;
          expect(customerHanaCheckStub.calledTwice, `Called ${customerHanaCheckStub.callCount} times`).to.be.true;
        });

        it("should always return after checkTimeoutMs with all service checks completed or timed out", async function () {
          // This will delay the customer hana upgrade check by 1 second --> too long for the overall healthcheck
          sinon.stub(TenantHanaUpgradeState, "get").callsFake(async (context, options) => {
            await new Promise((resolve) => setTimeout(resolve, 1000));
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore - sinon adds a wrappedMethod property to the function
            return TenantHanaUpgradeState.get.wrappedMethod.bind(TenantHanaUpgradeState)(context, options);
          });

          const service = new DwcHealthService({ checkTimeoutMs: 500 });
          const start = performance.now();
          const result1 = await service.tenantHealthCheck(fakeAuthTenantContext);
          const end = performance.now();
          // Check that tenantHealthCheck did not wait for customer-hana check to complete
          expect(end - start).to.be.lessThan(1000); // 1s
          expect(result1).to.be.deep.eq({
            overallStatusCode: 804,
            value: {
              status: "DOWN",
              statusCode: 804,
              statusCodes: [804],
              tenantId,
              services: [
                {
                  service: "tenant-management",
                  status: "UP",
                  statusCodes: [],
                  tms: {
                    bdcEnabled: false,
                    dwcEnabled: true,
                    locked: false,
                    xsn: false,
                  },
                  type: "MANDATORY",
                },
                {
                  service: "user-manager",
                  status: "UP",
                  statusCodes: [],
                  type: "MANDATORY",
                },
                {
                  message: "customer-hana Service is not reachable: The operation was aborted due to timeout",
                  service: "customer-hana",
                  status: "DOWN",
                  statusCodes: [804],
                  type: "MANDATORY",
                },
                ...(removeAuthHealthChecks
                  ? []
                  : [
                      {
                        service: "dwc-di-service",
                        status: "UP",
                        statusCodes: [],
                        type: "OPTIONAL",
                      },
                    ]),
              ],
            },
          });
        });
      });
    }
  });
});
