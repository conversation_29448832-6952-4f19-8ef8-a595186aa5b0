/** @format */

import { httpClient } from "@sap/dwc-http-client";
import { expect } from "chai";
import express, { Request, Response } from "express";
import * as http from "http";
import sinon from "sinon";
import { RequestContext } from "../../../../repository/security/requestContext";
import { HealthRequestContext, X_SAP_DWC_TENANT_ID } from "../../../../sidecar/context/index";
import { healthRouter } from "../../../../sidecar/health/dwc/router.js";
import { EnvUtils } from "../../../../sidecar/utils/env-utils.js";

const app = express();
app.use(express.json());
app.use((req: Request, res: Response, next: any) => {
  req.context = new HealthRequestContext({
    userInfo: {
      tenantId: req.headers[X_SAP_DWC_TENANT_ID] as string,
    },
    jwtToken: req.headers.authorization || null,
  }) as unknown as RequestContext;
  next();
});
app.use(healthRouter);
const server = new http.Server(app);

describe.skip("/service/sidecar/health/dwc/router", function () {
  const tenantId = EnvUtils.isLocalHanaMocked() ? "DWC_DEV_REPO" : "02da35ed-4897-4e21-9223-4e64b08f7ce9";

  before(async function () {
    await new Promise<void>((res) => {
      server.timeout = 60 * 1000; // 1 minute;
      server.listen(3010, function () {
        res();
      });
    });
  });

  afterEach(function () {
    sinon.restore();
  });

  after(async function () {
    await new Promise((res) => server.close(res));
  });

  describe("healthTenants endpoint", function () {
    it("should respond with 404 when no tenant information is set in the header or path", async function () {
      const resp = await httpClient.call({
        url: "http://localhost:3010/healthTenants",
        opts: {
          acceptedStatusCodes: [404],
        },
      });
      expect(resp.body).to.deep.equal({
        statusCode: 404,
        message: "The tenant ID has not been provided in the URL or header.",
      });
    });

    it("should respond with 400 when tenant information is set in the header and path but differs", async function () {
      const resp = await httpClient.call({
        url: "http://localhost:3010/healthTenants/tenant1",
        opts: {
          headers: {
            [X_SAP_DWC_TENANT_ID]: "tenant2",
          },
          acceptedStatusCodes: [400],
        },
      });
      expect(resp.body).to.deep.equal({
        statusCode: 400,
        message: 'The tenant ID in the URL and header do not match: "tenant1", "tenant2"',
      });
    });

    it("should respond with 400 when tenant ID is invalid", async function () {
      const resp = await httpClient.call({
        url: "http://localhost:3010/healthTenants/invalid_tenant_id!",
        opts: {
          acceptedStatusCodes: [400],
        },
      });
      expect(resp.body).to.deep.equal({
        statusCode: 400,
        message: "The tenant ID is invalid.",
      });
    });

    it("should respond with 200 and empty array when tenant information is valid and no jwt token is set", async function () {
      const resp = await httpClient.call({
        url: `http://localhost:3010/healthTenants/${tenantId}`,
        opts: {
          headers: {},
          acceptedStatusCodes: [200],
        },
      });
      expect(resp.body).to.deep.equal([]);
    });

    let originalEnv: string | undefined;
    for (const removeAuthHealthChecks of [true, false]) {
      it(`should respond with 200 and health check result WHEN tenant information is valid AND jwt token is set - REMOVE_AUTHORIZED_HEALTH_CHECKS ${
        removeAuthHealthChecks ? "on" : "off"
      }`, async function () {
        originalEnv = process.env.REMOVE_AUTHORIZED_HEALTH_CHECKS;
        process.env.REMOVE_AUTHORIZED_HEALTH_CHECKS = removeAuthHealthChecks.toString();
        try {
          const resp = await httpClient.call({
            url: `http://localhost:3010/healthTenants/${tenantId}`,
            opts: {
              headers: {
                authorization: "Bearer fakeJwtToken",
              },
              acceptedStatusCodes: [200],
            },
          });
          expect(resp.body).to.deep.equal({
            status: "UP",
            statusCode: 200,
            statusCodes: [],
            tenantId,
            services: [
              {
                service: "tenant-management",
                status: "UP",
                statusCodes: [],
                tms: {
                  bdcEnabled: false,
                  dwcEnabled: true,
                  locked: false,
                  xsn: false,
                },
                type: "MANDATORY",
              },
              {
                service: "user-manager",
                status: "UP",
                statusCodes: [],
                type: "MANDATORY",
              },
              {
                service: "customer-hana",
                status: "UP",
                statusCodes: [],
                type: "MANDATORY",
              },
              ...(removeAuthHealthChecks
                ? []
                : [
                    {
                      service: "dwc-di-service",
                      status: "UP",
                      statusCodes: [],
                      type: "OPTIONAL",
                    },
                  ]),
            ],
          });
        } finally {
          process.env.REMOVE_AUTHORIZED_HEALTH_CHECKS = originalEnv;
        }
      });
    }
  });
});
