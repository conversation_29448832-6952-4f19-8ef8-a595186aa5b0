/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { HistogramInstrument } from "@opentelemetry/sdk-metrics/build/src/Instruments";
import { IRepositoryObject, IRequestContext } from "@sap/deepsea-types";
import telemetry from "@sap/dwc-telemetry";
import { expect } from "chai";
import sinon from "sinon";
import { DataflowMetricService } from "../../../dataflow/metrices/dataflowMetricService";
import { DataFlowUsageMetrics } from "../../../dataflow/metrices/models";
import { testTenantUuid } from "../../../lib/node";
import { RequestContext } from "../../../repository/security/requestContext";
import { TaskLogsApi } from "../../../task/logger/controllers/TaskLogsApi";
import { Status } from "../../../task/logger/models";
import { Activity, ApplicationId } from "../../../task/models";

describe("service/tests/dataflow/metrices/dataflowMetricService.spec.ts", function () {
  let context: IRequestContext,
    getLogHeaders: sinon.SinonStub,
    getConnectionTypes: sinon.SinonStub,
    dataflowMetricService: DataflowMetricService,
    histogramSuccessStub: sinon.SinonStubbedInstance<HistogramInstrument>,
    createHistogramStub: sinon.SinonStub;

  before(function () {
    context = sinon.stub(RequestContext.createFromTenantId(testTenantUuid));
    getLogHeaders = sinon.stub(TaskLogsApi.prototype, "getLogHeaders");
    getConnectionTypes = sinon.stub(DataflowMetricService.prototype, "getConnectionTypes");
    dataflowMetricService = new DataflowMetricService(context, "$$global$$");
    histogramSuccessStub = sinon.createStubInstance(HistogramInstrument);
    createHistogramStub = sinon.stub(telemetry.metrics.getMeter(), "createHistogram");
    createHistogramStub
      .withArgs(DataFlowUsageMetrics.DATA_FLOW_EXECUTION_CONNECTION_OVERVIEW)
      .returns(histogramSuccessStub);
  });

  after(function () {
    sinon.restore();
  });

  afterEach(function () {
    histogramSuccessStub.record.resetHistory();
  });

  it("should log the data flow data via open telemetry if data flow is modified in last 24hours", async function () {
    const connectionTypes = [
      { connectionType: "GCS" },
      { connectionType: "OData" },
      { connectionType: "ADL V2" },
      { connectionType: "ABAP" },
    ];
    const spaceId = "$$global";
    const taskLogsResp = {
      logs: [
        {
          activity: Activity.DAILY_METRICS,
          applicationId: ApplicationId.DATA_FLOWS,
          endTime: new Date("2021-04-27T16:46:26.521Z"),
          logId: 6622,
          objectId: "$$system$$",
          spaceId,
          startTime: new Date("2021-04-27T16:45:59.202Z"),
          status: Status.COMPLETED,
        },
      ],
    };
    getLogHeaders.resolves(taskLogsResp);
    getConnectionTypes.resolves(connectionTypes);
    await dataflowMetricService.logDataFlowMetrics();
    expect(histogramSuccessStub.record.called).to.be.true;
  });

  it("should not log the data flow data via open telemetry if data flow is not modified in last 24 hours", async function () {
    const connectionTypes = [];
    const spaceId = "$$global$$";
    const taskLogsResp = {
      logs: [
        {
          activity: Activity.DAILY_METRICS,
          applicationId: ApplicationId.DATA_FLOWS,
          endTime: new Date("2025-01-27T16:46:26.521Z"),
          logId: 6622,
          objectId: "$$system$$",
          spaceId,
          startTime: new Date("2025-01-27T16:45:59.202Z"),
          status: Status.COMPLETED,
        },
      ],
    };
    getLogHeaders.resolves(taskLogsResp);
    getConnectionTypes.resolves(connectionTypes);
    await dataflowMetricService.logDataFlowMetrics();
    expect(histogramSuccessStub.record.called).to.be.false;
  });

  it("extractConnectionTypes() should filter connection types based on modofication date when its not the initial run", async function () {
    const repoObjects: IRepositoryObject[] = [
      {
        properties: {
          "#dfSourceOperatorRedshift": "0",
          "#dfSourceOperatorOracle": "0",
          "#dfSourceOperatorAzureDL2": "0",
          "#dfSourceOperatorOC": "0",
          "#dfSourceOperatorAzureDL1": "0",
          "#dfSourceOperatorGCS": "0",
          "#dfSourceOperatorSFTP": "0",
          "#dfSourceOperatorS3": "0",
          "#dfSourceOperatorMSSQL": "0",
          "#dfSourceOperatorAzureSQL": "0",
          "#dfSourceOperatorHDFS": "0",
          "#dfSourceOperatorOData": "1",
          "#dfSourceOperatorCDI": "0",
          "#dfSourceOperatorABAP": "0",
        },
        id: "48110650A117EB481800805E38D2C1A3",
        changeVersion: -99,
        qualifiedName: "Products_DF_00A",
        kind: "sap.dis.dataflow",
        name: "Products_DF_00A",
        folderId: "67020650A117EB481800805E38D2C1A3",
        modificationDate: "2025-04-24 20:57:48.070000000 UTC",
        hash: "0103d8af156a31515000c399fdc2af194366d76d7caa37de7b26eb4ddc1153d9",
        creator: "I354129",
      },
      {
        properties: {
          "#dfSourceOperatorHDFS": "0",
          "#dfSourceOperatorCDI": "0",
          "#dfSourceOperatorMSSQL": "0",
          "#dfSourceOperatorAzureDL2": "0",
          "#dfSourceOperatorGCS": "0",
          "#dfSourceOperatorRedshift": "0",
          "#dfSourceOperatorOracle": "0",
          "#dfSourceOperatorAzureSQL": "0",
          "#dfSourceOperatorABAP": "1",
          "#dfSourceOperatorOC": "0",
          "#dfSourceOperatorAzureDL1": "0",
          "#dfSourceOperatorSFTP": "0",
          "#dfSourceOperatorOData": "0",
          "#dfSourceOperatorS3": "0",
        },
        id: "47110650A117EB481800805E38D2C1A3",
        changeVersion: -99,
        qualifiedName: "Products_DF_009",
        kind: "sap.dis.dataflow",
        name: "Products_DF_009",
        folderId: "67020650A117EB481800805E38D2C1A3",
        modificationDate: "2024-11-21 20:57:48.070000000 UTC",
        hash: "ea5b61de3588a051307adca22ff8be6661506be19ca49cb3f62c4391fbd29734",
        creator: "I354129",
      },
    ];

    const result = dataflowMetricService.extractConnectionTypes(
      repoObjects,
      "2025-04-23 22:57:48.070000000 UTC",
      false
    );
    expect(result).to.deep.equal([{ connectionType: "OData" }]); // only one object is modified in specified 24 hours
  });

  it("extractConnectionTypes() should not filter connection types based on modofication date when its the initial run", async function () {
    const repoObjects: IRepositoryObject[] = [
      {
        properties: {
          "#dfSourceOperatorRedshift": "0",
          "#dfSourceOperatorOracle": "0",
          "#dfSourceOperatorAzureDL2": "0",
          "#dfSourceOperatorOC": "0",
          "#dfSourceOperatorAzureDL1": "0",
          "#dfSourceOperatorGCS": "0",
          "#dfSourceOperatorSFTP": "0",
          "#dfSourceOperatorS3": "0",
          "#dfSourceOperatorMSSQL": "0",
          "#dfSourceOperatorAzureSQL": "0",
          "#dfSourceOperatorHDFS": "0",
          "#dfSourceOperatorOData": "1",
          "#dfSourceOperatorCDI": "0",
          "#dfSourceOperatorABAP": "0",
        },
        id: "48110650A117EB481800805E38D2C1A3",
        changeVersion: -99,
        qualifiedName: "Products_DF_00A",
        kind: "sap.dis.dataflow",
        name: "Products_DF_00A",
        folderId: "67020650A117EB481800805E38D2C1A3",
        modificationDate: "2025-04-24 20:57:48.070000000 UTC",
        hash: "0103d8af156a31515000c399fdc2af194366d76d7caa37de7b26eb4ddc1153d9",
        creator: "I354129",
      },
      {
        properties: {
          "#dfSourceOperatorHDFS": "0",
          "#dfSourceOperatorCDI": "0",
          "#dfSourceOperatorMSSQL": "0",
          "#dfSourceOperatorAzureDL2": "0",
          "#dfSourceOperatorGCS": "0",
          "#dfSourceOperatorRedshift": "0",
          "#dfSourceOperatorOracle": "0",
          "#dfSourceOperatorAzureSQL": "0",
          "#dfSourceOperatorABAP": "1",
          "#dfSourceOperatorOC": "0",
          "#dfSourceOperatorAzureDL1": "0",
          "#dfSourceOperatorSFTP": "0",
          "#dfSourceOperatorOData": "0",
          "#dfSourceOperatorS3": "0",
        },
        id: "47110650A117EB481800805E38D2C1A3",
        changeVersion: -99,
        qualifiedName: "Products_DF_009",
        kind: "sap.dis.dataflow",
        name: "Products_DF_009",
        folderId: "67020650A117EB481800805E38D2C1A3",
        modificationDate: "2024-11-21 20:57:48.070000000 UTC",
        hash: "ea5b61de3588a051307adca22ff8be6661506be19ca49cb3f62c4391fbd29734",
        creator: "I354129",
      },
    ];

    const result = dataflowMetricService.extractConnectionTypes(repoObjects, "2025-04-23 22:57:48.070000000 UTC", true);
    expect(result).to.deep.equal([{ connectionType: "OData" }, { connectionType: "ABAP" }]);
  });
});
