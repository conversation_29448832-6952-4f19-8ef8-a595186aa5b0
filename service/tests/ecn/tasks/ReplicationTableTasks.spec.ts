/* Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved. */

import { Statement } from "@sap/prom-hana-client";
import chai, { expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import sinon from "sinon";
import { LocalTableObserver, EcnReplicationTask } from "../../../ecn";
import { EcnLocalTableReplicationService } from "../../../ecn/impl/EcnLocalTableReplicationService";
import * as utilities from "../../../ecn/utils/Helper";
import { EcnTasksParameters } from "../../../ecn/utils/types";
import { DbClient } from "../../../lib/DbClient";
import { RequestContext } from "../../../repository/security/requestContext";
import { CustomerHana } from "../../../reuseComponents/spaces/src/CustomerHana";
import { TaskLogsApi } from "../../../task/logger/controllers/TaskLogsApi";
import { Status, Substatus } from "../../../task/logger/models";
import { TaskLogger } from "../../../task/logger/services/logger/TaskLogger";
import { Activity, ApplicationId, Parameters } from "../../../task/models";
import { ObservedTask } from "../../../task/observer/Observer";
import {
  ActiveSessionChecker,
  CheckResponse,
} from "../../../task/orchestrator/services/activesession/ActiveSessionChecker";
import { LockKey } from "../../../task/orchestrator/services/tasklock";
import { runGetLockIdentifierTest, runIsAuthorizedTest, runOverwriteLockTestForSessionChecker } from "./utils";
chai.use(chaiAsPromised);

describe("Elastic Compute Node Replication Tables Tasks", function() {
  this.timeout(120000);

  let taskLogger: TaskLogger;
  let ecnLocalTableReplicationService: any;
  let logMessageStub: any;
  let isSessionErroneousStub: any;
  let dbClientStub: any;
  let statementStub: any;
  let activeSessionCheckerStub: any;

  const spaceId = "$$ecn$$";
  const ecnId = "ecn01";
  const schemaName = "test_schema";
  const tableSpaceName = "test_space";
  const tableName = "CONTEXT.test_table";
  const tableFullPath = `${tableSpaceName}.${tableName}`;
  const parameters: Parameters = {
    [EcnTasksParameters.ECN_ID]: ecnId,
    [EcnTasksParameters.SCHEMA_NAME]: schemaName,
    [EcnTasksParameters.TABLE_NAME]: tableName,
    [EcnTasksParameters.PARENT_VIEW_NAME]: "view",
    [EcnTasksParameters.PARENT_VIEW_SCHEMA_NAME]: "VIEW_SCHEMA",
    [EcnTasksParameters.TABLE_DISK_SIZE_GB]: 15,
  };
  const context: RequestContext = {
    finish: () => { },
    userInfo: {
      technical: true,
    },
  } as any;
  // tasks under test
  const localTableCreateReplicaTask = new EcnReplicationTask(
    context,
    spaceId,
    tableFullPath,
    Activity.CREATE_REPLICA,
    parameters
  );
  const localTableDropReplicaTask = new EcnReplicationTask(
    context,
    spaceId,
    tableFullPath,
    Activity.DROP_REPLICA,
    parameters
  );

  before(async () => {
    statementStub = sinon.createStubInstance(Statement);
    statementStub = {
      exec: sinon.stub(),
    };
    dbClientStub = sinon.stub(DbClient.prototype);
    dbClientStub = {
      exec: sinon.stub(),
      prepare: () => Promise.resolve(statementStub),
      close: sinon.stub(),
    };
    const customerHanaMock = Promise.resolve({
      getTenantManagerClient: () => Promise.resolve(dbClientStub),
    } as any);
    sinon.stub(CustomerHana, "fromRequestContext").returns(customerHanaMock as any);
    dbClientStub.exec.resolves([]);
    const getMessagesForLogIdStub = sinon.stub(TaskLogsApi.prototype, "getMessagesForLogId");
    getMessagesForLogIdStub.resolves({});
    ecnLocalTableReplicationService = sinon.stub(EcnLocalTableReplicationService.prototype);
    logMessageStub = sinon.stub(TaskLogger.prototype, "logMessage");
    logMessageStub.resolves();
    taskLogger = await TaskLogger.fromLogId(context, 1234);
    isSessionErroneousStub = sinon.stub(utilities, "isSessionErroneous");
    activeSessionCheckerStub = sinon.stub(ActiveSessionChecker.prototype, "check");
  });

  afterEach(() => {
    sinon.resetHistory();
  });

  after(() => {
    sinon.restore();
  });
  describe("[CREATE_REPLICA]service/ecn/task/EcnReplicationTask.ts", () => {
    runIsAuthorizedTest(
      localTableCreateReplicaTask,
      EcnReplicationTask,
      taskLogger,
      spaceId,
      tableFullPath,
      Activity.CREATE_REPLICA
    );

    runGetLockIdentifierTest(localTableCreateReplicaTask, ApplicationId.LOCAL_TABLE, tableSpaceName, tableName);

    runOverwriteLockTestForSessionChecker(
      localTableCreateReplicaTask,
      ApplicationId.LOCAL_TABLE,
      Activity.CREATE_REPLICA,
      spaceId,
      tableFullPath
    );
    describe("execute() - with authorization", () => {
      it("should return FAILED, hence parameters are invalid", async () => {
        const taskExecution = await new EcnReplicationTask(
          context,
          spaceId,
          tableFullPath,
          Activity.CREATE_REPLICA,
          {}
        ).execute(taskLogger);
        expect(taskExecution).to.deep.equal({ status: Status.FAILED });
      });

      it("should return FAILED, hence table replication in the db failed", async () => {
        ecnLocalTableReplicationService.manageReplica.rejects();
        const taskExecution = await localTableCreateReplicaTask.execute(taskLogger);
        expect(taskExecution).to.deep.equal({ status: Status.FAILED });
      });

      it("should return FAILED, hence replication status is failed", async () => {
        ecnLocalTableReplicationService.manageReplica = () => Promise.resolve();
        isSessionErroneousStub.resolves(true);
        const taskExecution = await localTableCreateReplicaTask.execute(taskLogger);
        expect(taskExecution).to.deep.equal({ status: Status.FAILED });
      });

      it("should return FAILED, hence the to be replicated table is of type row", async () => {
        const taskExecution = await new EcnReplicationTask(
          context,
          spaceId,
          tableFullPath,
          Activity.CREATE_REPLICA,
          {
            [EcnTasksParameters.ECN_ID]: ecnId,
            [EcnTasksParameters.SCHEMA_NAME]: schemaName,
            [EcnTasksParameters.TABLE_NAME]: tableName,
            [EcnTasksParameters.PARENT_VIEW_NAME]: "view",
            [EcnTasksParameters.PARENT_VIEW_SCHEMA_NAME]: "VIEW_SCHEMA",
            [EcnTasksParameters.TABLE_TYPE]: "r"
          }
        ).execute(taskLogger);
        expect(taskExecution).to.deep.equal({ status: Status.FAILED, subStatusCode: Substatus.PREREQ_NOT_MET });
      });

      it("should return COMPLETED", async () => {
        ecnLocalTableReplicationService.manageReplica = () => Promise.resolve();
        isSessionErroneousStub.resolves(false);
        const taskExecution = await localTableCreateReplicaTask.execute(taskLogger);
        expect(taskExecution).to.deep.equal({ status: Status.COMPLETED });
      });
    });
  });

  describe("[DROP_REPLICA]service/ecn/task/EcnReplicationTask.ts", () => {
    describe("execute() - with authorization", () => {
      it("should return FAILED, hence parameters are invalid", async () => {
        const taskExecution = await new EcnReplicationTask(
          context,
          spaceId,
          tableFullPath,
          Activity.CREATE_REPLICA,
          {}
        ).execute(taskLogger);
        expect(taskExecution).to.deep.equal({ status: Status.FAILED });
      });

      it("should return FAILED, hence the session is erroneous", async () => {
        ecnLocalTableReplicationService.manageReplica = () => Promise.resolve();
        isSessionErroneousStub.resolves(true);
        statementStub.exec.resolves();
        const taskExecution = await localTableDropReplicaTask.execute(taskLogger);
        expect(taskExecution).to.deep.equal({ status: Status.FAILED });
      });

      it("should return FAILED, hence remove replica on db failed ", async () => {
        ecnLocalTableReplicationService.manageReplica = () => Promise.reject(new Error("TR failed."));
        const taskExecution = await localTableDropReplicaTask.execute(taskLogger);
        expect(taskExecution).to.deep.equal({ status: Status.FAILED });
      });

      it("should return COMPLETED", async () => {
        ecnLocalTableReplicationService.manageReplica = () => Promise.resolve();
        isSessionErroneousStub.resolves(false);
        const taskExecution = await localTableDropReplicaTask.execute(taskLogger);
        expect(taskExecution).to.deep.equal({ status: Status.COMPLETED });
      });
    });
  });

  describe("service/ecn/task/LocalTableObserver.ts", () => {
    // cut
    let localTableObserver: LocalTableObserver;
    // test data
    const parameters: Parameters = {
      [EcnTasksParameters.ECN_ID]: ecnId,
    };
    const observedTasks: ObservedTask[] = [
      {
        lockKey: LockKey.EXECUTE,
        applicationId: ApplicationId.LOCAL_TABLE,
        activity: Activity.DROP_REPLICA,
        objectId: "space1.table1",
        spaceId,
        logId: 1,
        creationTime: new Date(),
        startTime: new Date(),
        user: "User",
        runId: "345678901",
        parameters,
      },
      {
        lockKey: LockKey.EXECUTE,
        applicationId: ApplicationId.LOCAL_TABLE,
        activity: Activity.CREATE_REPLICA,
        objectId: "space2.table2",
        spaceId,
        logId: 2,
        creationTime: new Date(),
        startTime: new Date(),
        user: "User",
        runId: "567890123",
        parameters,
      } as ObservedTask,
      {
        lockKey: LockKey.EXECUTE,
        applicationId: ApplicationId.LOCAL_TABLE,
        activity: Activity.DROP_REPLICA,
        objectId: "space3.table3",
        spaceId,
        logId: 3,
        creationTime: new Date(),
        startTime: new Date(),
        user: "User",
        runId: "456789012",
        parameters,
      } as ObservedTask,
      {
        lockKey: LockKey.EXECUTE,
        applicationId: ApplicationId.LOCAL_TABLE,
        activity: Activity.CREATE_REPLICA,
        objectId: "space4.table4",
        spaceId,
        logId: 4,
        creationTime: new Date(),
        startTime: new Date(),
        user: "User",
        runId: "234567890",
        parameters,
      } as ObservedTask,
    ];

    const tasksWithRectifiedStatus: CheckResponse[] = [
      {
        logId: 1,
        status: Status.COMPLETED,
        aborted: false,
      },
      {
        logId: 3,
        status: Status.FAILED,
        aborted: true,
      },
      /**   {
          logId: 4,
          status: Status.RUNNING,
          aborted: false,
        }, */
    ];

    it("should return no tasks, unable to get status application", async () => {
      activeSessionCheckerStub.rejects(new Error("Failed"));
      localTableObserver = new LocalTableObserver(context);
      const observerResponse = await localTableObserver.observe(observedTasks);
      expect(observerResponse).to.be.an("array");
      expect(observerResponse.length).equal(0);
    });

    it("should return no tasks, thus no running replication", async () => {
      activeSessionCheckerStub.resolves([]);
      localTableObserver = new LocalTableObserver(context);
      const observerResponse = await localTableObserver.observe(observedTasks);
      expect(observerResponse).to.be.an("array");
      expect(observerResponse.length).equal(0);
    });

    it("should return tasks with adjusted status", async () => {
      activeSessionCheckerStub.resolves(tasksWithRectifiedStatus);
      localTableObserver = new LocalTableObserver(context);
      const observerResponse = await localTableObserver.observe(observedTasks);
      expect(observerResponse).to.be.an("array");
      expect(observerResponse.length).equal(2);
      const completedTask = observerResponse.filter((t) => t.logId === 1)[0];
      const failedTask = observerResponse.filter((t) => t.logId === 3)[0];
      expect(completedTask.status).equal(Status.COMPLETED);
      expect(failedTask.status).equal(Status.FAILED);
      expect(failedTask.subStatusCode).equal(Substatus.STOPPED);
    });
  });
});
