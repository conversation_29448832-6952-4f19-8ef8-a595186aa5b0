/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { FeatureFlagProvider } from "@sap/dwc-context-checks";
import { ReservedEntitySuffixes } from "@sap/seal-interfaces";
import chai, { expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import sinon from "sinon";
import { IECNMetadataSpaceAsgmtStrtg } from "../../../../shared/ecn/ecnMetadataTypes";
import { EcnTaskChainCsnBuilder } from "../../../ecn/impl/EcnTaskChainCsnBuilder";
import {
  ECN_CHECK_TABLE_FUNCTION,
  ECN_GET_METADATA_FUNCTION,
  ECN_GET_OBJECT_DEPENDENCIES_VIEW,
} from "../../../ecn/utils/types";
import { RESULT_TABLE_SUFFIX } from "../../../intelligentlookup/utils/commonUtil";
import { DbClient } from "../../../lib/DbClient";
import { IRequestContext } from "../../../repository/security/common/common";
import { CustomerHana } from "../../../reuseComponents/spaces/src";
import { Activity } from "../../../task/models";

chai.use(chaiAsPromised);

describe("service/ecn/impl/EcnTaskChainCsnBuilder.ts", () => {
  // constants
  const context = {} as IRequestContext;
  const ecnId = "ecn0";
  const startActivity = Activity.GENERATE_START_CHAIN;
  const stopActivity = Activity.GENERATE_STOP_CHAIN;
  const sizingPlan = {
    VCPU: 8,
    MEMORY: 120,
    STORAGE: 360,
  };

  // cut
  let cut: EcnTaskChainCsnBuilder;
  let dbClientStub: any;

  const spaces = (count: number): any[] => {
    const res: any = [];
    for (let i = 1; i <= count; i++) {
      res.push({
        SPACE_ID: `space_test${i}`,
        ASSIGNMENT_STRATEGY: i % 2 === 0 ? IECNMetadataSpaceAsgmtStrtg.manual : IECNMetadataSpaceAsgmtStrtg.automatic,
      });
    }
    return res;
  };
  const tables = (count: number): any[] => {
    const res: any = [];
    for (let i = 1; i <= count; i++) {
      res.push({
        TABLE_NAME: `table_test${i}`,
        SPACE_ID: `space_test${i}`,
        SCHEMA_NAME: `SPACE_TEST${i}`,
        EXPOSED_OBJECT_NAME: `view_test${i}`,
        EXPOSED_SCHEMA_NAME: `SCHEMA_TEST${i}`,
        DISK_SIZE_GIB: 15,
        TABLE_TYPE: "COLUMN",
      });
    }
    return res;
  };

  before(async () => {
    dbClientStub = sinon.createStubInstance(DbClient);
    dbClientStub = {
      exec: sinon.stub(),
      close: sinon.stub(),
    };
    const customerHanaMock = Promise.resolve({
      getTenantManagerClient: () => Promise.resolve(dbClientStub),
    } as any);
    sinon.stub(CustomerHana, "fromRequestContext").returns(customerHanaMock);
    sinon.stub(FeatureFlagProvider, "isFeatureActive").resolves(true);
  });

  afterEach(() => {
    sinon.resetHistory();
  });

  after(() => {
    sinon.restore();
  });

  describe("Start usage of an elastic compute node", () => {
    const chainName = `${ecnId}_START`;
    it("should throw error for an invalid sizing plan", async () => {
      cut = new EcnTaskChainCsnBuilder(context, ecnId, startActivity);
      dbClientStub.exec.resolves(undefined);
      await expect(cut.build()).to.eventually.to.be.fulfilled;
    });

    it("should pass for adding one elastic compute node", async () => {
      const expectedValue = require("./data/TaskChainCsn1.json");
      cut = new EcnTaskChainCsnBuilder(context, ecnId, startActivity);
      dbClientStub.exec = async (s) => {
        if (s.sql.includes("VCPU")) {
          return [sizingPlan];
        }
      };
      const csn = await cut.build();
      expect(csn.metadata.csn.taskchains[`${chainName}`].nodes.length).to.equal(1);
      expect(csn.metadata.csn.taskchains[`${chainName}`].links.length).to.equal(0);
      expect(csn).to.deep.equal(expectedValue);
    });
    it("should pass for routing one space", async () => {
      cut = new EcnTaskChainCsnBuilder(context, ecnId, startActivity);
      const expectedValue = require("./data/TaskChainCsn2.json");
      dbClientStub.exec = async (s) => {
        if (s.sql.includes(ECN_GET_METADATA_FUNCTION) && !s.sql.includes("MEMORY")) {
          return spaces(1);
        } else if (s.sql.includes("VCPU")) {
          return [sizingPlan];
        } else {
          return [];
        }
      };
      const csn = await cut.build();
      expect(csn.metadata.csn.taskchains[`${chainName}`].nodes.length).to.equal(2);
      expect(csn.metadata.csn.taskchains[`${chainName}`].links.length).to.equal(1);
      expect(csn).to.deep.eq(expectedValue);
    });

    it("should pass for routing multiple spaces and replicating an excluded tables", async () => {
      cut = new EcnTaskChainCsnBuilder(context, ecnId, startActivity);
      const expectedValue = require("./data/TaskChainCsn7.json");
      dbClientStub.exec = async (s) => {
        if (s.sql.includes(ECN_GET_METADATA_FUNCTION) && !s.sql.includes("MEMORY")) {
          return spaces(1);
        } else if (s.sql.includes("VCPU")) {
          return [sizingPlan];
        } else {
          return [
            {
              TABLE_NAME: `table_test${ReservedEntitySuffixes.REMOTE_TABLE_PLUGIN_REPLICA_TABLE}`,
              SPACE_ID: `space_test$1`,
              SCHEMA_NAME: `SPACE_TEST$1`,
              EXPOSED_OBJECT_NAME: `view_test`,
              EXPOSED_SCHEMA_NAME: `SCHEMA_TEST`,
              DISK_SIZE_GIB: 55,
            },
            {
              TABLE_NAME: `table_test${ReservedEntitySuffixes.REMOTE_TABLE_PLUGIN_REPLICA_TABLE.toLowerCase()}`,
              SPACE_ID: `space_test$1`,
              SCHEMA_NAME: `SPACE_TEST$1`,
              EXPOSED_OBJECT_NAME: `view_test`,
              EXPOSED_SCHEMA_NAME: `SCHEMA_TEST`,
              DISK_SIZE_GIB: 55,
            },
            {
              TABLE_NAME: `table_test${ReservedEntitySuffixes.View_Materialization_PersistencyTable_2}`,
              SPACE_ID: `space_test$1`,
              SCHEMA_NAME: `SPACE_TEST$1`,
              EXPOSED_OBJECT_NAME: `view_test`,
              EXPOSED_SCHEMA_NAME: `SCHEMA_TEST`,
              DISK_SIZE_GIB: 55,
            },
            {
              TABLE_NAME: `table_test${ReservedEntitySuffixes.View_Materialization_PersistencyTable_1}`,
              SPACE_ID: `space_test$1`,
              SCHEMA_NAME: `SPACE_TEST$1`,
              EXPOSED_OBJECT_NAME: `view_test`,
              EXPOSED_SCHEMA_NAME: `SCHEMA_TEST`,
              DISK_SIZE_GIB: 55,
            },
            {
              TABLE_NAME: `table_test${RESULT_TABLE_SUFFIX}`,
              SPACE_ID: `space_test$1`,
              SCHEMA_NAME: `SPACE_TEST$1`,
              EXPOSED_OBJECT_NAME: `view_test`,
              EXPOSED_SCHEMA_NAME: `SCHEMA_TEST`,
              DISK_SIZE_GIB: 55,
            },
            {
              TABLE_NAME: `table_test_Delta${ReservedEntitySuffixes.Delta_Table}`,
              SPACE_ID: `space_test$1`,
              SCHEMA_NAME: `SPACE_TEST$1`,
              EXPOSED_OBJECT_NAME: `view_test`,
              EXPOSED_SCHEMA_NAME: `SCHEMA_TEST`,
              DISK_SIZE_GIB: 55,
            },
          ];
        }
      };
      const csn = await cut.build();
      expect(csn.metadata.csn.taskchains[`${chainName}`].nodes.length).to.equal(7);
      expect(csn.metadata.csn.taskchains[`${chainName}`].links.length).to.equal(9);
      expect(csn).to.deep.eq(expectedValue);
    });

    it("should pass for replicating one table and routing multiple spaces", async () => {
      cut = new EcnTaskChainCsnBuilder(context, ecnId, startActivity);
      const expectedValue = require("./data/TaskChainCsn3.json");
      dbClientStub.exec = async (s) => {
        if (s.sql.includes(ECN_GET_METADATA_FUNCTION) && !s.sql.includes("MEMORY")) {
          return spaces(3);
        } else if (s.sql.includes("VCPU")) {
          return [sizingPlan];
        } else if (s.sql.includes(ECN_GET_OBJECT_DEPENDENCIES_VIEW)) {
          return tables(1);
        }
      };
      const csn = await cut.build();
      expect(csn.metadata.csn.taskchains[`${chainName}`].nodes.length).to.equal(5);
      expect(csn.metadata.csn.taskchains[`${chainName}`].links.length).to.equal(4);
      expect(csn).to.deep.eq(expectedValue);
    });

    it("should pass for replicating less than 4 tables", async () => {
      cut = new EcnTaskChainCsnBuilder(context, ecnId, startActivity);
      const expectedValue = require("./data/TaskChainCsn4.json");
      dbClientStub.exec = async (s) => {
        if (s.sql.includes(ECN_GET_METADATA_FUNCTION) && !s.sql.includes("MEMORY")) {
          return spaces(3);
        } else if (s.sql.includes("VCPU")) {
          return [sizingPlan];
        } else if (s.sql.includes(ECN_GET_OBJECT_DEPENDENCIES_VIEW)) {
          return tables(2);
        }
      };
      const csn = await cut.build();
      expect(csn.metadata.csn.taskchains[`${chainName}`].nodes.length).to.equal(7);
      expect(csn.metadata.csn.taskchains[`${chainName}`].links.length).to.equal(7);
      expect(csn).to.deep.eq(expectedValue);
    });

    it("should pass for replicating more than 4 tables", async () => {
      cut = new EcnTaskChainCsnBuilder(context, ecnId, startActivity);
      const expectedValue = require("./data/TaskChainCsn5.json");
      dbClientStub.exec = async (s) => {
        if (s.sql.includes(ECN_GET_METADATA_FUNCTION) && !s.sql.includes("MEMORY")) {
          return spaces(3);
        } else if (s.sql.includes("VCPU")) {
          return [sizingPlan];
        } else if (s.sql.includes(ECN_GET_OBJECT_DEPENDENCIES_VIEW)) {
          return tables(9);
        }
      };
      const csn = await cut.build();
      expect(csn.metadata.csn.taskchains[`${chainName}`].nodes.length).to.equal(14);
      expect(csn.metadata.csn.taskchains[`${chainName}`].links.length).to.equal(16);
      expect(csn).to.deep.eq(expectedValue);
    });

    it("should pass for routing one space and replicating one table", async () => {
      cut = new EcnTaskChainCsnBuilder(context, ecnId, startActivity);
      const expectedValue = require("./data/TaskChainCsn6.json");
      dbClientStub.exec = async (s) => {
        if (s.sql.includes(ECN_GET_METADATA_FUNCTION) && !s.sql.includes("MEMORY")) {
          return spaces(1);
        } else if (s.sql.includes("VCPU")) {
          return [sizingPlan];
        } else if (s.sql.includes(ECN_GET_OBJECT_DEPENDENCIES_VIEW)) {
          return tables(1);
        }
      };
      const csn = await cut.build();
      expect(csn.metadata.csn.taskchains[`${chainName}`].nodes.length).to.equal(3);
      expect(csn.metadata.csn.taskchains[`${chainName}`].links.length).to.equal(2);
      expect(csn).to.deep.eq(expectedValue);
    });

    it("should pass for mass replication-routing", async () => {
      cut = new EcnTaskChainCsnBuilder(context, ecnId, startActivity);
      dbClientStub.exec = async (s) => {
        if (s.sql.includes(ECN_GET_METADATA_FUNCTION) && !s.sql.includes("MEMORY")) {
          return spaces(77);
        } else if (s.sql.includes("VCPU")) {
          return [sizingPlan];
        } else if (s.sql.includes(ECN_GET_OBJECT_DEPENDENCIES_VIEW)) {
          return tables(126);
        }
      };
      const csn = await cut.build();
      expect(csn.metadata.csn.taskchains[`${chainName}`].nodes.length).to.equal(205);
      expect(csn.metadata.csn.taskchains[`${chainName}`].links.length).to.equal(207);
    });
  });

  describe("Stop usage of an elastic compute node", () => {
    const chainName = `${ecnId}_STOP`;
    it("should pass for removing elastic compute node only", async () => {
      const expectedValue = require("./data/TaskChainCsn10.json");
      cut = new EcnTaskChainCsnBuilder(context, ecnId, stopActivity);
      dbClientStub.exec = () => Promise.reject(new Error("Dummy error"));
      const csn = await cut.build();
      expect(csn.metadata.csn.taskchains[`${chainName}`].nodes.length).to.equal(1);
      expect(csn.metadata.csn.taskchains[`${chainName}`].links.length).to.equal(0);
      expect(csn).to.deep.equal(expectedValue);
    });
    it("should pass for re-routing a space", async () => {
      cut = new EcnTaskChainCsnBuilder(context, ecnId, stopActivity);
      const expectedValue = require("./data/TaskChainCsn20.json");
      dbClientStub.exec = async (s) => {
        if (s.sql.includes("SPACE_USERS")) {
          return spaces(1);
        } else {
          return [];
        }
      };
      const csn = await cut.build();
      expect(csn.metadata.csn.taskchains[`${chainName}`].nodes.length).to.equal(2);
      expect(csn.metadata.csn.taskchains[`${chainName}`].links.length).to.equal(1);
      expect(csn).to.deep.eq(expectedValue);
    });

    it("should pass for re-routing two spaces", async () => {
      cut = new EcnTaskChainCsnBuilder(context, ecnId, stopActivity);
      const expectedValue = require("./data/TaskChainCsn21.json");
      dbClientStub.exec = async (s) => {
        if (s.sql.includes("SPACE_USERS")) {
          return spaces(2);
        } else {
          return [];
        }
      };
      const csn = await cut.build();
      expect(csn.metadata.csn.taskchains[`${chainName}`].nodes.length).to.equal(3);
      expect(csn.metadata.csn.taskchains[`${chainName}`].links.length).to.equal(2);
      expect(csn).to.deep.eq(expectedValue);
    });

    it("should pass for re-route one space and removing replica for one table", async () => {
      cut = new EcnTaskChainCsnBuilder(context, ecnId, stopActivity);
      const expectedValue = require("./data/TaskChainCsn30.json");
      dbClientStub.exec = async (s) => {
        if (s.sql.includes(ECN_CHECK_TABLE_FUNCTION) && s.sql.includes("SPACE_USERS")) {
          return spaces(1);
        } else if (s.sql.includes(ECN_CHECK_TABLE_FUNCTION)) {
          return tables(1);
        }
      };
      const csn = await cut.build();
      expect(csn.metadata.csn.taskchains[`${chainName}`].nodes.length).to.equal(3);
      expect(csn.metadata.csn.taskchains[`${chainName}`].links.length).to.equal(2);
      expect(csn).to.deep.eq(expectedValue);
    });

    it("should pass for mass replica removal", async () => {
      cut = new EcnTaskChainCsnBuilder(context, ecnId, stopActivity);
      const expectedValue = require("./data/TaskChainCsn40.json");
      dbClientStub.exec = async (s) => {
        if (s.sql.includes(ECN_CHECK_TABLE_FUNCTION) && s.sql.includes("SPACE_USERS")) {
          return spaces(2);
        } else if (s.sql.includes(ECN_CHECK_TABLE_FUNCTION)) {
          return tables(21);
        }
      };
      const csn = await cut.build();
      expect(csn.metadata.csn.taskchains[`${chainName}`].nodes.length).to.equal(25);
      expect(csn.metadata.csn.taskchains[`${chainName}`].links.length).to.equal(27);
      expect(csn).to.deep.eq(expectedValue);
    });
  });
});
