/** @format */

import chai, { expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import { beforeEach, test } from "mocha";
import Sinon from "sinon";
import sinonChai from "sinon-chai";
import { ActiveHandlesRestartDelay } from "../../../routes/health/active-handles-delay";
import {
  ACTIVE_HANDLES_MONITORING_WINDOW_MS,
  ActiveHandlesMonitor,
  CF_HEALTHCHECK_INTERVAL_MS,
  ExtendedProcess,
  MINIMUM_LIVENESS_CHECKS,
} from "../../../routes/health/active-handles-monitor.js";

chai.use(sinonChai);
chai.use(chaiAsPromised);

describe("/service/routes/active-handles-monitor", function () {
  // This test can get slowed down by the fake timer in CICD runs
  // more time should be given to the test to complete
  this.timeout(120000); // 2m

  let fakeTimer: Sinon.SinonFakeTimers;
  let resourceInfoStub: Sinon.SinonStub;
  let breakerSpy: Sinon.SinonSpy;
  let monitor: ActiveHandlesMonitor;

  async function handleAsyncTick(rejectionMsg?: "threshold reached" | "Breaker is open" | null) {
    const breakerCallCount = breakerSpy.callCount;
    await fakeTimer.tickAsync(CF_HEALTHCHECK_INTERVAL_MS);

    if (rejectionMsg === null) {
      expect(breakerSpy.callCount).to.be.eq(breakerCallCount);
      return;
    }

    expect(breakerSpy.callCount).to.be.eq(breakerCallCount + 1);
    if (rejectionMsg) {
      await expect(getLastReturnValue(breakerSpy)).to.be.rejectedWith(rejectionMsg);
    } else {
      await expect(getLastReturnValue(breakerSpy)).to.eventually.be.eq(undefined);
    }
  }

  function noActiveHandlesSpike() {
    resourceInfoStub.resetBehavior();
    resourceInfoStub.returns([
      // Active Handles
      "TCPSocketWrap",
      "TCPSocketWrap",
      "TCPSocketWrap",
      // Active resources
      "Immediate",
      "Immediate",
      "Immediate",
      "Immediate",
      "Immediate",
      "Timeout",
      "Timeout",
      "Timeout",
      "Timeout",
      "Timeout",
      "Timeout",
    ]);
  }

  function activeHandlesSpike() {
    resourceInfoStub.resetBehavior();
    resourceInfoStub.returns([
      // Active handles
      "TCPSocketWrap",
      "TCPSocketWrap",
      "TCPSocketWrap",
      "TCPSocketWrap",
      "TCPSocketWrap",
      "TCPSocketWrap",
      "TCPSocketWrap",
      "TCPSocketWrap",
      // Active resources
      "TCPServerWrap",
      "ProcessWrap",
      "PipeWrap",
      "GetAddrInfoReqWrap",
      "fsReqPromise",
      "FSReqCallback",
      "Immediate",
      "Timeout",
    ]);
  }

  before(function () {
    fakeTimer = Sinon.useFakeTimers({ now: 100 });
    resourceInfoStub = Sinon.stub(process as ExtendedProcess, "getActiveResourcesInfo");
  });

  after(function () {
    fakeTimer.restore();
    Sinon.restore();
  });

  context("GIVEN no delayer is not used", function () {
    beforeEach(function () {
      monitor = new ActiveHandlesMonitor({ threshold: 5 });
      // eslint-disable-next-line dot-notation -- want to spy on private method
      breakerSpy = Sinon.spy(monitor["breaker"], "fire");
      noActiveHandlesSpike();
    });

    afterEach(function () {
      monitor.stop();
    });

    test(`WHEN active handles are below threshold
          AND check interval is reached after 30 seconds
          THEN checkHandles method is called without throwing any exceptions`, async function () {
      await handleAsyncTick();

      expect(resourceInfoStub).to.be.calledOnce;
      for (let i = 0; i < 100; i++) {
        await handleAsyncTick();
        expect(monitor.hasHandlesSpike()).to.be.false;
      }
    });

    test(`GIVEN monitor is still in the warmup period
          WHEN active handles is above the threshold
          AND the check is triggered
          THEN hasHandlesSpike still returns false
          AND after warmup period hasHandlesSpike always returns true`, async function () {
      activeHandlesSpike();
      // eslint-disable-next-line dot-notation -- want to spy on private method
      expect(monitor["breaker"].warmUp).to.be.true;
      for (let i = 0; i < MINIMUM_LIVENESS_CHECKS; i++) {
        await handleAsyncTick("threshold reached");
        // eslint-disable-next-line dot-notation -- want to spy on private method
        if (monitor["breaker"].warmUp) {
          expect(monitor.hasHandlesSpike()).to.be.false;
        } else {
          expect(monitor.hasHandlesSpike()).to.be.true;
        }
      }
      // Reset time is 1 minute hence we need to wait for 1 minute then
      // the breaker is half opened and should fail again. We want to treat
      // open and half open as a handles spike indicator.
      const runsAfterWarmup = 100;
      for (let i = 0; i < runsAfterWarmup; i++) {
        await handleAsyncTick("Breaker is open");
        expect(monitor.hasHandlesSpike()).to.be.true;
        await handleAsyncTick("Breaker is open");
        expect(monitor.hasHandlesSpike()).to.be.true;
        await handleAsyncTick("threshold reached");
        expect(monitor.hasHandlesSpike()).to.be.true;
      }
    });

    test(`GIVEN monitor is still in the warmup period
          WHEN active handles are failing in the warmup period
          AND a single check is below the threshold
          THEN hasHandles spike returns false`, async function () {
      activeHandlesSpike();
      // eslint-disable-next-line dot-notation -- want to spy on private method
      expect(monitor["breaker"].warmUp).to.be.true;

      for (let i = 0; i < MINIMUM_LIVENESS_CHECKS - 1; i++) {
        await handleAsyncTick("threshold reached");
        expect(monitor.hasHandlesSpike()).to.be.false;
      }
      noActiveHandlesSpike();

      await handleAsyncTick();
      expect(monitor.hasHandlesSpike()).to.be.false;
      // eslint-disable-next-line dot-notation -- want to spy on private method
      expect(monitor["breaker"].warmUp).to.be.false;

      activeHandlesSpike();

      for (let i = 0; i < 19; i++) {
        await handleAsyncTick("threshold reached");
        expect(monitor.hasHandlesSpike()).to.be.false;
      }
      // At this point the last successful check will be rotated out of the time window
      await handleAsyncTick("threshold reached");
      expect(monitor.hasHandlesSpike()).to.be.true;
    });

    test(`GIVEN monitor is not inside the warmup period
          WHEN active handles are flapping (error rate around 50%)
          THEN hasHandles spike returns false`, async function () {
      // eslint-disable-next-line dot-notation -- want to spy on private method
      while (monitor["breaker"].warmUp) {
        await handleAsyncTick();
      }
      // Error Rate is about 50%
      for (let i = 0; i < 100; i++) {
        activeHandlesSpike();
        await handleAsyncTick("threshold reached");
        noActiveHandlesSpike();
        await handleAsyncTick();
        expect(monitor.hasHandlesSpike()).to.be.false;
      }
    });

    test(`GIVEN monitor is not inside the warmup period
          WHEN active handles are degraded heavily (error rate around 90%)
          THEN hasHandles spike returns false`, async function () {
      // eslint-disable-next-line dot-notation -- want to spy on private method
      while (monitor["breaker"].warmUp) {
        await handleAsyncTick();
      }
      // Error Rate is around 90%
      let consecutiveFailures = 0;
      for (let i = 0; i < 100; i++) {
        if (consecutiveFailures === MINIMUM_LIVENESS_CHECKS - 1) {
          noActiveHandlesSpike();
          await handleAsyncTick();
          consecutiveFailures = 0;
        }
        const failure = Math.random() <= 0.9;
        if (failure) {
          consecutiveFailures++;
          activeHandlesSpike();
          await handleAsyncTick("threshold reached");
        } else {
          noActiveHandlesSpike();
          await handleAsyncTick();
        }
        expect(monitor.hasHandlesSpike()).to.be.false;
      }
    });
  });

  context("GIVEN a delayer (mocked)", function () {
    let delayMock: ActiveHandlesRestartDelay;
    let delayMonitorStub: Sinon.SinonStub;
    let setStub: Sinon.SinonStub;

    beforeEach(function () {
      delayMonitorStub = Sinon.stub();
      setStub = Sinon.stub();
      delayMock = {
        delayMonitor: delayMonitorStub,
        set: setStub,
      } as unknown as ActiveHandlesRestartDelay;

      monitor = new ActiveHandlesMonitor({ threshold: 5, delayer: delayMock });
      // eslint-disable-next-line dot-notation -- want to spy on private method
      breakerSpy = Sinon.spy(monitor["breaker"], "fire");
      activeHandlesSpike();
    });

    afterEach(function () {
      monitor.stop();
    });

    test(`GIVEN a delayer is used that is not delaying
          WHEN active handles is above the threshold
          AND the check is triggered
          THEN hasHandlesSpike returns true
          AND delayer is notified of the event`, async function () {
      delayMonitorStub.resolves(false);
      // eslint-disable-next-line dot-notation -- want to spy on private method
      expect(monitor["breaker"].warmUp).to.be.true;
      for (let i = 0; i < MINIMUM_LIVENESS_CHECKS; i++) {
        await handleAsyncTick("threshold reached");
        // eslint-disable-next-line dot-notation -- want to spy on private method
        if (monitor["breaker"].warmUp) {
          expect(monitor.hasHandlesSpike()).to.be.false;
          expect(setStub).not.to.be.called;
        } else {
          expect(monitor.hasHandlesSpike()).to.be.true;
        }
      }
      expect(delayMonitorStub).callCount(MINIMUM_LIVENESS_CHECKS);
      expect(setStub).to.be.calledOnce;
    });

    test(`GIVEN a delayer is used that is delaying
          WHEN active handles is above the threshold
          THEN hasHandlesSpike returns false
          AND breaker is not fired`, async function () {
      delayMonitorStub.resolves(true);
      // eslint-disable-next-line dot-notation -- want to spy on private method
      expect(monitor["breaker"].warmUp).to.be.true;
      for (let i = 0; i < MINIMUM_LIVENESS_CHECKS; i++) {
        await handleAsyncTick(null);
        // eslint-disable-next-line dot-notation -- want to spy on private method
        if (monitor["breaker"].warmUp) {
          expect(monitor.hasHandlesSpike()).to.be.false;
        } else {
          expect(monitor.hasHandlesSpike()).to.be.false;
        }
        expect(setStub).not.to.be.called;
      }
      expect(delayMonitorStub).callCount(MINIMUM_LIVENESS_CHECKS);
    });
  });

  context("GIVEN a delayer (active-handle-delay)", function () {
    let delayer: ActiveHandlesRestartDelay;
    let delayMonitorSpy: Sinon.SinonSpy;
    const delayTime = ACTIVE_HANDLES_MONITORING_WINDOW_MS * 2;

    beforeEach(function () {
      delayer = new ActiveHandlesRestartDelay({ delayTime });
      delayMonitorSpy = Sinon.spy(delayer, "delayMonitor");
      monitor = new ActiveHandlesMonitor({
        threshold: 5,
        delayer,
      });
      // eslint-disable-next-line dot-notation -- want to spy on private method
      breakerSpy = Sinon.spy(monitor["breaker"], "fire");
      activeHandlesSpike();
    });

    afterEach(async function () {
      monitor.stop();
      await delayer.clear();
    });

    test(`GIVEN a delayer is used that is delaying
          WHEN active handles is above the threshold
          AND no previous restart was recorded in the last hour
          THEN delayMonitor will not delay
          UNLESS a restart was recorded after the delay monitor is active`, async function () {
      await handleAsyncTick("threshold reached");
      await expect(getLastReturnValue(delayMonitorSpy)).eventually.eq(false);
      await delayer.set();
      const delayTicks = delayTime / CF_HEALTHCHECK_INTERVAL_MS - 1;
      for (let i = 0; i < delayTicks; i++) {
        await handleAsyncTick(null);
        await expect(getLastReturnValue(delayMonitorSpy)).eventually.eq(true);
      }
      await handleAsyncTick("threshold reached");
      await expect(getLastReturnValue(delayMonitorSpy)).eventually.eq(false);
    });
  });
});

function getLastReturnValue(spy: Sinon.SinonSpy) {
  return spy.getCall(spy.callCount - 1).returnValue;
}
