/** @format */

import { ITenantLicenses, TenantClassification, TenantInformationProvider } from "@sap/dwc-tms-provider";
import Status from "http-status-codes";
import sinon, { SinonStub } from "sinon";
import { mockReq, mockRes } from "sinon-express-mock";
import { FtcSizeRangesValidator } from "../../../../shared/provisioning/ftc/baseValues/FtcSizeRangesValidator";
import { FlexibleTenantStatus, IUsageConsumption, Measure } from "../../../../shared/provisioning/ftc/types";
import * as hyperscalerHelper from "../../../lib/node";
import { Hyperscaler, testTenantUuid } from "../../../lib/node";
import * as usageServicesHelper from "../../../metering/usageServices/usageServices";
import * as bwBridgeHelper from "../../../provisioning/bwBridge/helper";
import { ElasticCustomerHanaProvisioning } from "../../../provisioning/flexible/ElasticCustomerHanaProvisioning";
import { FtcRangesValidatorGetter } from "../../../provisioning/flexible/FtcRangesValidatorGetter";
import * as provisioningElasticity from "../../../provisioning/flexible/customerHanaElasticity";
import * as getTenantLicense from "../../../provisioning/flexible/getTenantLicense";
import * as flexibleHelper from "../../../provisioning/flexible/helpers";
import * as updateHandlerHelper from "../../../provisioning/flexible/updateHandler/helpers";
import { IFtcMetadata } from "../../../provisioning/flexible/updateHandler/types";
import * as provHelper from "../../../provisioning/helpers";
import { licensesToInt } from "../../../provisioning/helpers";
import { RequestContext } from "../../../repository/security/requestContext";
import {
  calculateCapacityUnitsRoute,
  getConfigurationStatusRoute,
  getFtcBackendDataRoute,
  getLandscapeFeaturesRoute,
  getLicenseRoute,
  submitNewSizeRoute,
  upgradeToLatestPatchRoute,
} from "../../../routes/security/customerHanaElasticity";
import * as errorResponse from "../../../server/errorResponse";

describe("service/routes/security/customerHanaElasticity", () => {
  let context: RequestContext;
  let req: any;
  let res: any;
  let sandbox: sinon.SinonSandbox;
  let sendErrorResponseStub: sinon.SinonStub;

  beforeEach(function () {
    sandbox = sinon.createSandbox();

    context = RequestContext.createFromTenantId(testTenantUuid);
    req = mockReq({ context });
    res = mockRes();
    sendErrorResponseStub = sandbox.stub(errorResponse, "sendErrorResponse");
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe("getLicenseRoute", () => {
    let getLicenseStub: any;

    beforeEach(() => {
      getLicenseStub = sandbox.stub(getTenantLicense, "getLicense");
    });

    it("should send license", async () => {
      const license = {
        thresholdDWCCU: 1000,
        thresholdStorage: 512,
        thresholdMemory: 32,
        thresholdVCPU: 2,
        thresholdDataLakeStorage: 0,
        thresholdBWBridge1: 0,
      };
      getLicenseStub.returns(license);

      await getLicenseRoute(req, res);

      sinon.assert.calledWithMatch(res.status, Status.OK);
      sinon.assert.calledWithMatch(res.send, { license });
    });

    it("should not find license", async () => {
      getLicenseStub.returns(null);

      await getLicenseRoute(req, res);

      sinon.assert.calledWithMatch(res.status, Status.NOT_FOUND);
      sinon.assert.calledWithMatch(res.send);
    });

    it("should catch an error and send error response", async () => {
      getLicenseStub.throws();
      sendErrorResponseStub.returns(null);

      await getLicenseRoute(req, res);

      sinon.assert.calledOnce(getLicenseStub);
      sinon.assert.calledWith(sendErrorResponseStub, req.context, "getLicenseFailed");
    });
  });

  describe("getConfigurationStatusRoute", () => {
    let isFlexibleConfigurationNeededStub: any;

    beforeEach(() => {
      isFlexibleConfigurationNeededStub = sandbox.stub(flexibleHelper, "isFlexibleConfigurationNeeded");
    });

    it("should send isConfigurationNeeded: true", async () => {
      isFlexibleConfigurationNeededStub.returns(true);

      await getConfigurationStatusRoute(req, res);

      sinon.assert.calledWithMatch(res.status, Status.OK);
      sinon.assert.calledWithMatch(res.send, { isConfigurationNeeded: true });
    });

    it("should catch error and send isConfigurationNeeded: false", async () => {
      isFlexibleConfigurationNeededStub.throws();

      await getConfigurationStatusRoute(req, res);

      sinon.assert.calledWithMatch(res.status, Status.OK);
      sinon.assert.calledWithMatch(res.send, { isConfigurationNeeded: false });
    });
  });

  describe("getLandscapeFeaturesRoute", () => {
    let getLandscapeFeaturesStub: any;

    beforeEach(() => {
      getLandscapeFeaturesStub = sandbox.stub(provHelper, "getAvailableFeaturesForCurrentLandscape");
    });

    it("should send landscapeFeatures", async () => {
      const landscapeFeatures = { landscape: "DAC", features: { hasDataLake: true, hasBwBrdige: true } };
      getLandscapeFeaturesStub.returns(landscapeFeatures);

      await getLandscapeFeaturesRoute(req, res);

      sinon.assert.calledWithMatch(res.status, Status.OK);
      sinon.assert.calledWithMatch(res.send, landscapeFeatures);
    });

    it("should catch an error and send error response", async () => {
      getLandscapeFeaturesStub.throws();
      sendErrorResponseStub.returns(null);

      await getLandscapeFeaturesRoute(req, res);

      sinon.assert.calledOnce(getLandscapeFeaturesStub);
      sinon.assert.calledWith(sendErrorResponseStub, req.context, "getLandscapeFeaturesFailed");
    });
  });

  describe("calculateCapacityUnitsRoute", () => {
    let calculateAndValidateCapacityUnitsStub: any;

    beforeEach(() => {
      calculateAndValidateCapacityUnitsStub = sandbox.stub(provisioningElasticity, "calculateAndValidateCapacityUnits");
    });

    it("should send calculateResponse", async () => {
      const calculateResponse = { thresholdDWCCU: 20000 };
      calculateAndValidateCapacityUnitsStub.returns(calculateResponse);

      await calculateCapacityUnitsRoute(req, res);

      sinon.assert.calledWithMatch(res.status, Status.OK);
      sinon.assert.calledWithMatch(res.send, calculateResponse);
    });

    it("should catch an error and send error response", async () => {
      calculateAndValidateCapacityUnitsStub.throws();
      sendErrorResponseStub.returns(null);

      await calculateCapacityUnitsRoute(req, res);

      sinon.assert.calledOnce(calculateAndValidateCapacityUnitsStub);
      sinon.assert.calledWith(sendErrorResponseStub, req.context, "calculateCapacityUnitsFailed");
    });
  });

  describe("submitNewSizeRoute", () => {
    let submitNewSizeStub: any;

    beforeEach(() => {
      submitNewSizeStub = sandbox.stub(provisioningElasticity, "submitNewSize");
    });

    it("should send an empty array", async () => {
      submitNewSizeStub.returns(null);

      await submitNewSizeRoute(req, res);

      sinon.assert.calledWithMatch(res.status, Status.OK);
      sinon.assert.calledWithMatch(res.send, {});
    });

    it("should catch an error and send error response", async () => {
      submitNewSizeStub.throws();
      sendErrorResponseStub.returns(null);

      await submitNewSizeRoute(req, res);

      sinon.assert.calledOnce(submitNewSizeStub);
      sinon.assert.calledWith(sendErrorResponseStub, req.context, "submitNewSizeFailed");
    });
  });

  describe("getFtcBackendDataRoute", () => {
    let readFtcMetadataStub: sinon.SinonStub;
    let hasBridgeInstanceOnHanaStub: sinon.SinonStub;
    let getUsageValuesStub: sinon.SinonStub;
    let getSizeRangesStub: sinon.SinonStub;
    let getHyperscalerStub: sinon.SinonStub;
    let ftcRangesValidatorStub: sinon.SinonStubbedInstance<FtcSizeRangesValidator>;

    beforeEach(() => {
      readFtcMetadataStub = sandbox
        .stub(updateHandlerHelper, "readFtcMetadata")
        .resolves({ metadata: { licenses: {} }, errors: [] });
      hasBridgeInstanceOnHanaStub = sandbox.stub(bwBridgeHelper, "hasBridgeInstanceOnHana").resolves();
      getUsageValuesStub = sandbox.stub(usageServicesHelper, "getMetricUsageInfo").resolves();
      ftcRangesValidatorStub = sinon.createStubInstance(FtcSizeRangesValidator);
      sandbox.stub(FtcRangesValidatorGetter, "fromContext").resolves(ftcRangesValidatorStub);
      getSizeRangesStub = ftcRangesValidatorStub.getSizeRanges.returns({} as any);
      getHyperscalerStub = sandbox.stub(hyperscalerHelper, "getHyperscaler").returns(Hyperscaler.AWS);
    });

    it("should send metadata and errors", async () => {
      const metadata: IFtcMetadata = {
        licenses: { thresholdMemory: "32", thresholdVCPU: "2", thresholdStorage: "256" },
        timestamp: new Date(),
        status: FlexibleTenantStatus.COMPLETED,
      };
      const expectedMetadata = {
        ...metadata,
        licenses: licensesToInt(metadata.licenses as Partial<ITenantLicenses>),
        hasBridgeInstance: true,
      };
      hasBridgeInstanceOnHanaStub.resolves(true);
      readFtcMetadataStub.returns({ metadata, errors: ["MockError"] });

      await getFtcBackendDataRoute(req, res);

      sinon.assert.calledWithMatch(res.send, { metadata: expectedMetadata, errors: ["MockError"] });
    });

    it("should send a metadata with only hasBridgeInstance if readFtcMetadata returns empty metadata", async () => {
      readFtcMetadataStub.returns({ metadata: null, errors: [] });
      hasBridgeInstanceOnHanaStub.resolves(true);

      await getFtcBackendDataRoute(req, res);

      sinon.assert.calledWithMatch(res.send, { metadata: { hasBridgeInstance: true } });
    });

    it("should send sizeRanges", async () => {
      const sizeRanges = { compute: { min: 2, max: 60, step: 1 } };
      getSizeRangesStub.returns(sizeRanges);

      await getFtcBackendDataRoute(req, res);

      sinon.assert.calledWithMatch(res.send, { sizeRanges });
    });

    it("should send usage information", async () => {
      const usageInfo: IUsageConsumption = {
        [Measure.DI_EXTRACTION_HOURS]: { allocatedValue: 200, usedValue: 0, overusageValue: 0 },
      };
      getUsageValuesStub.resolves(usageInfo);

      await getFtcBackendDataRoute(req, res);

      sinon.assert.calledWithMatch(res.send, { usageInfo });
    });

    it("should send hyperscaler", async () => {
      getHyperscalerStub.returns(Hyperscaler.GCP);
      await getFtcBackendDataRoute(req, res);

      sinon.assert.calledWithMatch(res.send, { hyperscaler: Hyperscaler.GCP });
    });

    it("should catch an error and send error response", async () => {
      readFtcMetadataStub.throws();
      sendErrorResponseStub.returns(null);

      await getFtcBackendDataRoute(req, res);

      sinon.assert.calledWith(sendErrorResponseStub, req.context, "getFtcBackendDataFailed");
    });
  });

  describe("upgradeToLatestPatchRoute", () => {
    let getTenantInformationStub: SinonStub;
    let upgradeToLatestPatchStub: SinonStub;

    beforeEach(() => {
      upgradeToLatestPatchStub = sandbox
        .stub(ElasticCustomerHanaProvisioning.prototype, "upgradeToLatestPatch")
        .resolves();
      getTenantInformationStub = sandbox.stub(TenantInformationProvider, "getTenantInformation");
    });
    afterEach(() => {
      sandbox.restore();
    });

    it("Should block patch upgrade request and reply with error if tenant is internal", async () => {
      getTenantInformationStub.resolves({ classification: TenantClassification.DWC_INTERNAL });
      sendErrorResponseStub.returns(null);

      await upgradeToLatestPatchRoute(req, res);
      sinon.assert.notCalled(upgradeToLatestPatchStub);
      sinon.assert.calledWith(
        sendErrorResponseStub,
        req.context,
        "Manual HANA patch upgrade not available for internal tenants."
      );
    });

    it("Should block patch upgrade reply with error if tenant information cannot be fetched (getTenantInformation returns undefined)", async () => {
      getTenantInformationStub.resolves(undefined);
      sendErrorResponseStub.returns(null);

      await upgradeToLatestPatchRoute(req, res);
      sinon.assert.notCalled(upgradeToLatestPatchStub);
      sinon.assert.calledWith(
        sendErrorResponseStub,
        req.context,
        "upgradeToLatestPatch Failed: Tenant Information could not be fetched."
      );
    });
  });
});
