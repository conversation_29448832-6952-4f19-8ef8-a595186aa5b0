/** @format */

import { SDPMigrationValueType } from "@sap/deepsea-types";
import { RepositoryObject, TimeUnit } from "@sap/deepsea-utils";
import { ReadOptions, WriteOptions } from "@sap/dwc-configuration";
import { Context } from "@sap/dwc-configuration/src/types/configurationService";
import { HttpMethod } from "@sap/dwc-http-client";
import { Request, SacClient } from "@sap/dwc-sac-client";
import { expect } from "chai";
import { UmsTypes } from "../../../../shared/api/ScopedUsers";
import { store } from "../../../configurations/main";
import { isLocalHanaMocked, isPullRequestValidation } from "../../../lib/node";
import * as Spaces from "../../../repository/spaces";
import { spaceDefaults } from "../../../reuseComponents/spaces/src";
import { ListRoles, SCOPE_USER_ASSIGNMENT, ScopeUserAssignments, scopeAssignment } from "../../../routes/sac/types";
import { SdpConversion, maxEntriesInBody } from "../../../routes/space/SdpConversion";
import { getValue, upsertValue } from "../../../routes/space/configService";
import {
  getListRoles,
  getScopeUserAssignmentRoleOrScope,
  postScopeAssignment,
  postScopeUserAssignmentRoleOrScope,
} from "../../../routes/space/user";
import {
  getFullRoleId,
  isCustomRole,
  isCustomScopeRole,
  isStandardRole,
  isStandardScopeRole,
} from "../../../routes/space/util";
import { clearListRoles } from "../../mocks/sacMock";
import * as RepoTestUtility from "../../repository/repository.utility";
import { IPreparation } from "../../repository/repository.utility";

describe("service/routes/space/sdpConversion.ts", function () {
  this.timeout(TimeUnit.MINUTES.toMillis(10));

  const configServiceLocal = {
    uuid: "69e3524e-e355-470a-89d3-48661d4fda4a",
    paramId: "",
    version: "2023.17.0", // version when the service should be available in the tenants
    scope: "",
    scopeId: "",
    value: "",
  };

  const preparations: RepoTestUtility.IPreparation[] = [];
  const context = RepoTestUtility.getFullAccessContextTechnicalForSdp();
  const hanaGateway = SacClient.getSacUrl("hana-gateway");

  const marketplaceSpaceId = `MARKETPLACE`;
  let marketplaceSpace: RepoTestUtility.IPreparation;
  let sdpConversion: SdpConversion;

  before(async function () {
    clearListRoles(); // Ensures that the listRoles are clean to not have some leftovers from other tests.
    if (!isLocalHanaMocked() && !isPullRequestValidation()) {
      this.skip();
    }
    RepoTestUtility.addStub(store, "upsert").callsFake(
      (ctx: Context, key: string, value: string, options?: WriteOptions) => {
        expect(key).to.be.eq("SDP_MIGRATION_STATE");
        expect(options?.useTenantScope).to.be.true;
        configServiceLocal.paramId = key;
        configServiceLocal.scope = options?.useTenantScope ? "TENANT" : "";
        configServiceLocal.scopeId = ctx.userInfo.tenantId || "";
        configServiceLocal.value = value;
        return configServiceLocal as any;
      }
    );

    (context.userInfo.isAdmin as any) = true; // Requires to fetch the spaces for getSpaces
    // Prepare spaces with a set of users
    const spaceMetaData = JSON.parse(JSON.stringify(spaceDefaults));
    spaceMetaData.members.push({ name: "utest_username", type: "user" });
    await RepoTestUtility.prepareSpaces(preparations, undefined, spaceMetaData);
    spaceMetaData.members.push({ name: "DWC_CURRENT_SPACE_USER", type: "user" });
    await RepoTestUtility.prepareSpaces(preparations, undefined, spaceMetaData);
    spaceMetaData.members.push({ name: "UTEST_SDP", type: "user" });
    await RepoTestUtility.prepareSpaces(preparations, undefined, spaceMetaData);
    spaceMetaData.members.push({ name: "DWC_MODELER", type: "user" });
    spaceMetaData.spaceType = marketplaceSpaceId.toLowerCase();
    marketplaceSpace = (await RepoTestUtility.prepareSpaces([], marketplaceSpaceId, spaceMetaData, {
      expectSuccess: false,
    })) as IPreparation;

    await RepoTestUtility.enforceFeatureFlags({
      DWCO_INFRA_SDPCONVERSION: true,
      DWCO_DUMMY_SDPCONVERSION: true,
      DWCO_INFRA_SPDCONVERSION_CLEANUP: true,
    });

    process.env.SDP_CONVERSION_RETRY_MAX = "0";
    process.env.SDP_CONVERSION_RETRY_DELAY_SECONDS = "5";

    // Do the actual conversion and check in the tests whether it was successful, or retrigger the conversion to check that nothing is being created twice
    sdpConversion = new SdpConversion(context, { retries: 0 });
    await sdpConversion.convertNonSdpTenants();
  });

  after(async () => {
    if (marketplaceSpace && !Array.isArray(marketplaceSpace)) {
      preparations.push(marketplaceSpace);
    }
    if (preparations.length) {
      await RepoTestUtility.deleteSpaces(preparations);
    }
    RepoTestUtility.restoreStubs();
  });

  it("checks that the marketplace is not converted", async function () {
    const listRoles: ListRoles[] = await getListRoles(context, sdpConversion.sacTenant);

    const onlyStandardRolesIds = listRoles.filter((i) => isStandardScopeRole(i)).map((l) => l.id);
    const customRolesWithSuffixIds = listRoles.filter((cr) => isCustomScopeRole(cr)).map((l) => getFullRoleId(l.id));

    for (const id of onlyStandardRolesIds && customRolesWithSuffixIds) {
      const urlScopeAssignment = `${hanaGateway}${scopeAssignment}${id}`;
      const assignedScopes = (await SacClient.call({
        url: urlScopeAssignment,
        opts: {
          context,
          method: HttpMethod.GET,
          caller: "Unis_test",
        },
      } as Request)) as any;

      if (assignedScopes.includes(marketplaceSpaceId)) {
        expect.fail(
          `assignedScopes ${assignedScopes.toString()} should not contain the marketplace space ${marketplaceSpaceId}`
        );
      }
    }
  });

  it("checks creation of new scopedRoles", async function () {
    const listRoles: ListRoles[] = await getListRoles(context, sdpConversion.sacTenant);

    const allCustomRoles = listRoles.filter((i) => i.id.package.includes(`t.`));
    const customOriginalRoles = allCustomRoles.filter((i) => isCustomRole(i));
    const customScopedRoles = allCustomRoles.filter((i) => isCustomScopeRole(i));

    expect(customOriginalRoles.length).to.be.eq(customScopedRoles.length);
    for (const org of customOriginalRoles) {
      const found = customScopedRoles.find(
        (c) => c.id.name === org.id.name + sdpConversion.customScopeRoleConventionSuffix
      );
      if (!found) {
        expect.fail(
          `Custom ScopeRole not found customScopedRoles ${JSON.stringify(customScopedRoles)} org ${JSON.stringify(org)}`
        );
      }
    }

    const standardRoles = listRoles.filter((i) => isStandardRole(i));
    const standardScopedRoles = listRoles.filter((i) => isStandardScopeRole(i));
    // Should not have more then already defined global and standard roles
    const missing = standardRoles.find((s) => !standardScopedRoles.some((ss) => s.id.name === ss.id.name));
    expect(missing).to.not.be.undefined;
    expect(missing!.name).to.be.eq("Data_Warehouse_Cloud_Administrator");
    const ExcludeData_Warehouse_Cloud_Administrator = -1;
    expect(
      standardRoles.length + ExcludeData_Warehouse_Cloud_Administrator,
      `The following role might be missing ${JSON.stringify(missing)}`
    ).to.be.eq(standardScopedRoles.length);

    const otherRoles = listRoles.filter(
      (i) => !i.id.package.includes(`t.`) && !i.id.name.includes(sdpConversion.profilePackageStandardRoles)
    );
    const otherFound = otherRoles.find((o) => o.id.name.includes("scope"));
    if (otherFound) {
      expect.fail(`No other role should exist with the syntax scope ${JSON.stringify(otherFound)}`);
    }
  });

  it.skip("checks that all spaces had been assigned to all scoped roles", async function () {
    const listRoles: ListRoles[] = await getListRoles(context, sdpConversion.sacTenant);
    const onlyStandardRolesIds = listRoles.filter((i) => isStandardScopeRole(i)).map((l) => l.id);
    const customRolesWithSuffixIds = listRoles
      .filter((cr) => cr.id.name.endsWith(sdpConversion.customScopeRoleConventionSuffix))
      .map((l) => getFullRoleId(l.id));

    for (const id of onlyStandardRolesIds && customRolesWithSuffixIds) {
      const urlScopeAssignment = `${hanaGateway}${scopeAssignment}${id}`;
      const assignedScopes = (await SacClient.call({
        url: urlScopeAssignment,
        opts: {
          context,
          method: HttpMethod.GET,
          caller: "Unis_test",
        },
      } as Request)) as any;

      for (const prep of preparations) {
        if (!assignedScopes.includes(prep.spaceId)) {
          expect.fail(`assignedScopes roles id ${id} not found in the assigned space ${prep.spaceId}`);
        }
      }
    }

    const allOtherRolesIds = listRoles
      .filter(
        (o) =>
          !o.id.name.endsWith(sdpConversion.customScopeRoleConventionSuffix) &&
          !o.id.name.startsWith(sdpConversion.profilePackageStandardScopedRoles.split(":")[2])
      )
      .map((l) => getFullRoleId(l.id));
    for (const id of allOtherRolesIds) {
      const urlScopeAssignment = `${hanaGateway}${scopeAssignment}${id}`;
      const assignedScopes = (await SacClient.call({
        url: urlScopeAssignment,
        opts: {
          context,
          method: HttpMethod.GET,
          caller: "Unis_test",
        },
      } as Request)) as any;

      // All other roles should not contain any assign scopes (spaces)
      expect(assignedScopes, `Not empty for id ${id}`).to.be.empty;
    }
  });

  it("Checks that nothing had been created twice by Retrigger the CleanUp", async function () {
    const listRoles: ListRoles[] = await sdpConversion.cleanUpSdpConversion();
    for (const role of listRoles) {
      expect(role.id.name.endsWith(sdpConversion.customScopeRoleConventionSuffix)).to.be.false;
      if (role.id.name.startsWith(sdpConversion.profilePackageStandardScopedRoles)) {
        const listRoles = await getScopeUserAssignmentRoleOrScope(context, getFullRoleId(role.id), UmsTypes.ROLE);
        for (const listRole of listRoles) {
          const { displayName, scopes, userName } = listRole;
          expect(displayName).to.be.undefined;
          expect(userName).to.be.undefined;
          expect(scopes).to.be.empty;
        }
      }
    }
  });

  it("Checks that the assignment of users and scopes to ScopeRoles have been assigned properly - Second conversion", async function () {
    await sdpConversion.convertNonSdpTenants();
    const listRoles: ListRoles[] = await sdpConversion.cleanUpSdpConversion();
    for (const role of listRoles) {
      if (isCustomScopeRole(role) || isStandardScopeRole(role)) {
        const userAssignments = await getScopeUserAssignmentRoleOrScope(context, getFullRoleId(role.id), UmsTypes.ROLE);
        for (const user of userAssignments) {
          const { scopes } = user;
          if (scopes && scopes.length) {
            // SDP ScopedRoles Check
            expect(
              role.id.name.startsWith(sdpConversion.profilePackageStandardScopedRoles.split(":")[2]) ||
                role.id.name.endsWith(sdpConversion.customScopeRoleConventionSuffix)
            ).to.be.true;
            for (const prep of preparations) {
              const found = scopes.find((s: string) => s === prep.spaceId);
              if (!found) {
                expect.fail(`scope ${JSON.stringify(prep.spaceId)} not found in the assigned spaces.`);
              }
            }
          } else {
            // Non SDP converted roles
            expect(
              !role.id.name.startsWith(sdpConversion.profilePackageStandardScopedRoles) &&
                !role.id.name.endsWith(sdpConversion.customScopeRoleConventionSuffix)
            ).to.be.true;
          }
        }
      }
    }
  });

  it.skip("checks allow tenant conversion works even if it contains more then header limit size spaces", async function () {
    process.env.SDP_CONVERSION_REJECTION_SPACES_LIMIT = "1";
    try {
      const newConversion = new SdpConversion(context);
      const state = await newConversion.convertNonSdpTenants();
      expect.fail(`Expected to trow an exception ${state}`);
    } catch (err) {
      expect(err.status).to.be.eq(422);
      expect(err.isCodedError).to.be.true;
    } finally {
      try {
        process.env.SDP_CONVERSION_ENFORCE_ALLOWLIST = `["${context.userInfo.tenantId}"]`;
        const newConversion = new SdpConversion(context);
        await newConversion.convertNonSdpTenants();
      } catch (err) {
        expect.fail(`This time the clean up should have worked due to SDP_CONVERSION_ENFORCE_ALLOWLIST`);
      }
    }
  });

  it("checks allow tenant clean up anyway even if it contains more then header limit size spaces", async function () {
    process.env.SDP_CONVERSION_REJECTION_SPACES_LIMIT = "1";
    try {
      const newConversion = new SdpConversion(context);
      await newConversion.cleanUpSdpConversion();
    } catch (err) {
      expect.fail(`Not Expected to trow an exception ${JSON.stringify(err)}`);
    }
  });

  it("checks if Config service sets and retrieves the correct properties", async function () {
    RepoTestUtility.addStub(store, "getEffectiveValue").callsFake(
      (ctx: Context, key: string, options?: ReadOptions) => {
        expect(options?.auditLogAnyway).to.be.false;
        expect(configServiceLocal).to.be.deep.eq({
          uuid: "69e3524e-e355-470a-89d3-48661d4fda4a",
          paramId: key,
          version: "2023.17.0",
          scope: "TENANT",
          scopeId: ctx.userInfo.tenantId,
          value: SDPMigrationValueType.FAILEDWITHLIMIT,
        });
        return configServiceLocal.value as unknown as string;
      }
    );

    await upsertValue(context, SDPMigrationValueType.FAILEDWITHLIMIT);
    const stored = await getValue(context);
    expect(stored).to.be.eq(SDPMigrationValueType.FAILEDWITHLIMIT);
  });

  it("checks that Scoped_Data_Warehouse_Cloud_Administrator is never part of the result set", async function () {
    const listRoles: ListRoles[] = await getListRoles(context, sdpConversion.sacTenant);
    const found = listRoles.find((l) => l.id.name === "Scoped_Data_Warehouse_Cloud_Administrator");
    expect(found).to.be.undefined;
  });

  it("checks that empty CSN content will not break up the SDP Conversion", async function () {
    const emptyContentSpace = {
      content: null as any,
    };
    try {
      RepoTestUtility.addStub(Spaces, "getSpaces").returns(Promise.resolve([emptyContentSpace as RepositoryObject]));
      await sdpConversion.convertNonSdpTenants();
    } catch (err) {
      expect.fail(`empty CSN content should not fail SDP conversion err ${JSON.stringify(err)}`);
    }
  });

  it("checks the test count of assign and unassign", async function () {
    const obj = {
      someRoleId: {
        type: "someRoleId",
        data: {
          assign: [
            {
              name: "test",
              package: "test",
            },
            {
              name: "test2",
              package: "test2",
            },
          ],
          unassign: [
            {
              name: "test",
              package: "test",
            },
          ],
        },
      },
      someRoleId3: {
        type: "someRoleId3",
        data: {
          assign: [
            {
              name: "test3",
              package: "test3",
            },
          ],
          unassign: [
            {
              name: "test",
              package: "test",
            },
          ],
        },
      },
    };

    const count = sdpConversion.count(obj as any);
    expect(count).to.be.eq(5);
  });

  it("retryPromiseMap should pass the parameter to function", function () {
    const conversion = new SdpConversion(context);
    const scopeUserAssignments = {
      type: UmsTypes.ROLE,
      data: {
        assign: [],
        unassign: [],
      },
    };

    it("postScopeUserAssignmentRoleOrScope", async () => {
      const targetUrl = `${hanaGateway}${SCOPE_USER_ASSIGNMENT}&role?type=${UmsTypes.ROLE}&conversion=true`;
      RepoTestUtility.addStub(SacClient, "call").callsFake(async (callerInfo, context, options) => {
        expect(callerInfo.url).to.equal(targetUrl);
        return { assign: 0, unassign: 0 };
      });
      await conversion.retryPromiseMap<[string, ScopeUserAssignments]>(
        Object.entries({ role: scopeUserAssignments }),
        [true, 10000],
        postScopeUserAssignmentRoleOrScope
      );
    });

    it("postScopeAssignment", async () => {
      const targetUrl = `${hanaGateway}${scopeAssignment}role?type=${UmsTypes.ROLE}&tenant&conversion=true`;
      RepoTestUtility.addStub(SacClient, "call").callsFake(async (callerInfo, context, options) => {
        expect(callerInfo.url).to.equal(targetUrl);
        return { assign: 0, unassign: 0 };
      });
      await conversion.retryPromiseMap<string>(["role"], ["tenant", scopeUserAssignments, true], postScopeAssignment);
    });
  });
});

describe("maxEntriesInBody", () => {
  it("success", function () {
    const body = {};
    body["roleAssignments"] = {
      type: UmsTypes.ROLE,
      data: {
        assign: ["scope1", "scope2"],
        unassign: ["scope3"],
      },
    };
    body["userRoleAssignments"] = {
      type: UmsTypes.ROLE,
      data: {
        assign: [
          {
            name: "user1",
            scopeAssignment: "scope1",
            id: "id",
          },
        ],
        unassign: [],
      },
    };
    expect(maxEntriesInBody(body)).to.equal(3);
  });
});
