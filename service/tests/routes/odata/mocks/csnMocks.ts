/** @format */

import { IRepositoryElementDependency, IRepositoryObject } from "@sap/deepsea-types";
import { ParameterSelectionType, VariableOperator } from "../../../../../shared/odata/paramDefinitions";

export const CsnDocumentMocks = () => ({
  localTable: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local Table",
        elements: {
          COURSEID: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEID",
            key: true,
          },
          SUBJECTID: {
            type: "cds.Integer64",
            "@EndUserText.label": "SUBJECTID",
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
          "/BIC/COURSEFEE": {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
      },
    },
  },
  localTableWithParams: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local Table",
        elements: {
          COURSEID: {
            type: "cds.String",
            "@EndUserText.label": "COURSEID",
            key: true,
          },
          SUBJECTID: {
            type: "cds.Integer64",
            "@EndUserText.label": "SUBJECTID",
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
        params: {
          COURSEIDPARAM: {
            "@EndUserText.label": "COURSEIDFILTER",
            type: "cds.String",
            length: 20,
            default: "1002",
          },
          SUBJECTIDPARAM: {
            "@EndUserText.label": "SUBJECTID",
            type: "cds.Integer64",
            default: "102",
          },
          COURSEFEEPARAM: {
            "@EndUserText.label": "COURSEFEE",
            type: "cds.Integer64",
            default: "2000",
          },
        },
      },
    },
  },
  localTableWithDateParam: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local Table",
        elements: {
          COURSEID: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEID",
            key: true,
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
          DATE: {
            "@EndUserText.label": "DATE",
            type: "cds.Date",
            default: {
              val: "2023-12-11",
              literal: "date",
            },
          },
          DATETIMEPARAM: {
            type: "cds.DateTime",
            "@EndUserText.label": "DATETIME",
          },
        },
        params: {
          COURSEIDPARAM: {
            "@EndUserText.label": "COURSEIDPARAM",
            type: "cds.String",
            length: 20,
            default: "1002",
          },
          COURSEFEEPARAM: {
            "@EndUserText.label": "COURSEFEE",
            type: "cds.Integer64",
            default: "2000",
          },
          DATEPARAM: {
            "@EndUserText.label": "DateParam",
            type: "cds.Date",
            default: "2023-12-14",
          },
          DATETIMEPARAM: {
            "@EndUserText.label": "DateTimeParam",
            type: "cds.DateTime",
            default: "2023-12-01 09:30:00",
          },
        },
      },
    },
  },
  localTableWithDiffParamTypes: {
    definitions: {
      Local_Table: {
        "@EndUserText.label": "Local_Table",
        kind: "entity",
        elements: {
          ItemID: {
            "@EndUserText.label": "Item ID",
            type: "cds.Integer64",
            key: true,
            notNull: true,
          },
          Opportunity: {
            "@EndUserText.label": "Opportunity ID",
            type: "cds.Integer64",
          },
          Product: {
            "@EndUserText.label": "Product ID",
            type: "cds.String",
            length: 5000,
          },
          Quantity: {
            "@EndUserText.label": "Quantity",
            precision: 20,
            scale: 3,
            type: "cds.Decimal",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Semantics.quantity.unitOfMeasure": {
              "=": "Unit",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          Value: {
            "@EndUserText.label": "Value",
            precision: 20,
            scale: 2,
            type: "cds.Decimal",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Semantics.amount.currencyCode": {
              "=": "Currency",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          Unit: {
            "@EndUserText.label": "Unit",
            type: "cds.String",
            length: 5000,
            "@Semantics.unitOfMeasure": true,
          },
          UNIT: {
            "@EndUserText.label": "Unit",
            type: "cds.String",
            length: 5000,
            "@Semantics.unitOfMeasure": true,
          },
          Currency: {
            "@EndUserText.label": "Currency",
            type: "cds.String",
            length: 5000,
            "@Semantics.currencyCode": true,
          },
          FactAttr: {
            "@EndUserText.label": "FactAttr",
            type: "cds.String",
            length: 5000,
            "@ObjectModel.text.element": [
              {
                "=": "FactAttrLabel",
              },
            ],
          },
          FactAttrLabel: {
            "@EndUserText.label": "FactAttrLabel",
            type: "cds.String",
            length: 5000,
            "@Semantics.text": true,
          },
          ItemStatus: {
            "@EndUserText.label": "ItemStatus ID",
            type: "cds.Integer64",
          },
          ControllingArea: {
            "@EndUserText.label": "Controlling Area",
            type: "cds.Integer64",
          },
          SendingCostCenter: {
            "@EndUserText.label": "Sending Cost Center",
            type: "cds.Integer64",
          },
          ReceivingCostCenter: {
            "@EndUserText.label": "Receiving Cost Center",
            type: "cds.Integer64",
          },
          Calc_Boolean: {
            "@EndUserText.label": "Calc Boolean",
            type: "cds.Boolean",
          },
          Calc_DateTime: {
            "@EndUserText.label": "Calc DateTime",
            type: "cds.DateTime",
          },
          Calc_Date: {
            "@EndUserText.label": "Calc Date",
            type: "cds.Date",
          },
          Calc_Decimal: {
            "@EndUserText.label": "Calc Decimal",
            precision: 15,
            scale: 2,
            type: "cds.Decimal",
          },
          Calc_DecimalFloat: {
            "@EndUserText.label": "Calc DecimalFloat",
            type: "cds.DecimalFloat",
          },
          Calc_Integer: {
            "@EndUserText.label": "Calc Integer",
            type: "cds.Integer",
          },
          Calc_Integer64: {
            "@EndUserText.label": "Calc Integer64",
            type: "cds.Integer64",
          },
          Calc_SimpleTypeString25: {
            "@EndUserText.label": "Calc_SimpleTypeString25",
            type: "cds.String",
            length: 25,
          },
          Calc_String20: {
            "@EndUserText.label": "Calc_String20",
            type: "cds.String",
            length: 20,
          },
          Calc_Time: {
            "@EndUserText.label": "Calc_Time",
            type: "cds.Time",
          },
          Calc_Timestamp: {
            "@EndUserText.label": "Calc_Timestamp",
            type: "cds.Timestamp",
          },
          Calc_UUID: {
            "@EndUserText.label": "Calc_UUID",
            type: "cds.UUID",
          },
          Calc_Binary: {
            "@EndUserText.label": "Calc_Binary",
            type: "cds.Binary",
            length: 10,
          },
          Calc_Hana_Bin: {
            "@EndUserText.label": "Calc_Hana_Bin",
            type: "cds.Binary",
            length: 10,
          },
          Calc_TinyInt: {
            "@EndUserText.label": "Calc_TinyInt",
            type: "cds.Double",
          },
          Calc_SmallInt: {
            "@EndUserText.label": "Calc_SmallInt",
            type: "cds.Integer",
          },
          Calc_SmallDecimal: {
            "@EndUserText.label": "Calc_SmallDecimal",
            type: "cds.Double",
          },
          Calc_SmallReal: {
            "@EndUserText.label": "Calc_SmallReal",
            type: "cds.Double",
          },
        },
        params: {
          SRC_BOOLEAN: {
            "@EndUserText.label": "Src Boolean",
            type: "cds.Boolean",
            default: true,
          },
          SRC_DECIMAL: {
            "@EndUserText.label": "Src Decimal",
            precision: 5,
            scale: 2,
            type: "cds.Decimal",
            default: "3.14",
          },
          SRC_DATE: {
            "@EndUserText.label": "Src Date",
            type: "cds.Date",
            default: "2023-02-13",
          },
          SRC_DATETIME: {
            "@EndUserText.label": "Src_DateTime",
            type: "cds.DateTime",
            default: "2023-02-13 11:11:11",
          },
          SRC_DECIMALFLOAT: {
            "@EndUserText.label": "Src DecimalFloat",
            type: "cds.DecimalFloat",
            default: "3.14159265359",
          },
          SRC_INTEGER: {
            "@EndUserText.label": "Src Integer",
            type: "cds.Integer",
            default: "1234567890",
          },
          SRC_INTEGER64: {
            "@EndUserText.label": "Src Integer64",
            type: "cds.Integer64",
            default: "1234567890123456",
          },
          SRC_SIMPLETYPESTRING25: {
            default: "ABCD1234abcd1234",
            "@EndUserText.label": "Src SimpleTypeString25",
            type: "cds.String",
            length: 25,
          },
          SRC_STRING20: {
            default: "ABCD1234abcd1234",
            "@EndUserText.label": "Src String20",
            type: "cds.String",
            length: 20,
          },
          SRC_TIME: {
            "@EndUserText.label": "Src Time",
            type: "cds.Time",
            default: "12:34:56",
          },
          SRC_TIMESTAMP: {
            "@EndUserText.label": "Src Timestamp",
            type: "cds.Timestamp",
            default: "2023-02-13 12:34:56.12345678",
          },
          SRC_UUID: {
            "@EndUserText.label": "Src UUID",
            type: "cds.UUID",
            default: "12345678-9abc-def0-1234-56789abcdf00",
          },
          SRC_BINARY: {
            "@EndUserText.label": "SRC_BINARY",
            type: "cds.Binary",
            length: 10,
            default: "1010101010",
          },
          SRC_HANA_BIN: {
            "@EndUserText.label": "SRC_HANA_BIN",
            type: "cds.hana.BINARY",
            length: 10,
            default: "0101010101",
          },
          SRC_TINYINT: {
            "@EndUserText.label": "SRC_TINYINT",
            type: "cds.hana.TINYINT",
            default: "20",
          },
          SRC_SMALLINT: {
            "@EndUserText.label": "SRC_SMALLINT",
            type: "cds.hana.SMALLINT",
            default: "20",
          },
          SRC_SMALLDECIMAL: {
            "@EndUserText.label": "SRC_SMALLDECIMAL",
            type: "cds.hana.SMALLDECIMAL",
            default: "10.1",
          },
          SRC_REAL: {
            "@EndUserText.label": "SRC_REAL",
            type: "cds.hana.REAL",
            default: "20.2",
          },
        },
      },
    },
  },
  localTableWithAssociations: {
    definitions: {
      Local_Table: {
        kind: "entity",
        elements: {
          ItemID: {
            "@EndUserText.label": "ItemID",
            type: "cds.Integer64",
            key: true,
            notNull: true,
          },
          Opportunity: {
            "@EndUserText.label": "Opportunity",
            type: "cds.Integer64",
            "@ObjectModel.foreignKey.association": {
              "=": "MCT_Opportunities_Vi",
            },
          },
          Product: {
            "@EndUserText.label": "Product",
            type: "cds.String",
            length: 5000,
            "@ObjectModel.foreignKey.association": {
              "=": "_MCT_Prod_View",
            },
          },
          Quantity: {
            "@EndUserText.label": "Quantity",
            precision: 20,
            scale: 3,
            type: "cds.Decimal",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Semantics.quantity.unitOfMeasure": {
              "=": "Unit",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          Value: {
            "@EndUserText.label": "Value",
            precision: 20,
            scale: 2,
            type: "cds.Decimal",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Semantics.amount.currencyCode": {
              "=": "Currency",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          ControllingArea: {
            "@EndUserText.label": "Controlling Area",
            type: "cds.Integer64",
            "@ObjectModel.foreignKey.association": {
              "=": "MCT_Controlling_Area",
            },
          },
          MCT_Controlling_Area: {
            type: "cds.Association",
            on: [
              {
                ref: ["ControllingArea"],
              },
              "=",
              {
                ref: ["MCT_Controlling_Area", "ID"],
              },
            ],
            target: "MCT_Controlling_Area_View",
            "@EndUserText.label": "MCT Controlling Area View",
          },
        },
      },
      MCT_Controlling_Area_View: {
        kind: "entity",
        elements: {
          ID: {
            "@EndUserText.label": "ID",
            type: "cds.Integer64",
            key: true,
            notNull: true,
            "@Analytics.dimension": true,
          },
          Name: {
            "@EndUserText.label": "Name",
            type: "cds.String",
            length: 100,
            "@Analytics.dimension": true,
            "@Semantics.text": true,
          },
          Manager: {
            "@EndUserText.label": "Manager",
            type: "cds.Integer64",
          },
        },
      },
    },
  },
  noElements: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local Table",
        elements: {},
      },
    },
  },
  analyticModelWithVars: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local_Table",
        elements: {
          COURSEID: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEID",
            key: true,
            notNull: true,
          },
          SUBJECTID: {
            "@EndUserText.label": "SUBJECTID",
            type: "cds.Integer64",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
        params: {
          COURSEIDPARAM: {
            "@EndUserText.label": "COURSEIDFILTER",
            type: "cds.String",
            length: 20,
            default: "1002",
          },
          SUBJECTIDPARAM: {
            "@EndUserText.label": "SUBJECTID",
            type: "cds.Integer64",
          },
          COURSEFEEPARAM: {
            "@EndUserText.label": "COURSEFEE",
            type: "cds.Integer64",
            default: "2000",
          },
        },
      },
      OpportunityItems: {
        kind: "entity",
        "@ObjectModel.supportedCapabilities": [
          {
            "#": "ANALYTICAL_FACT",
          },
        ],
        "@EndUserText.label": "OpportunityItems",
        elements: {
          COURSEID: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEID",
            key: true,
          },
          SUBJECTID: {
            type: "cds.Integer64",
            "@EndUserText.label": "SUBJECTID",
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
      },
    },
  },
  analyticModelWithDerivedVar: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local_Table",
        elements: {
          COURSEID: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEID",
            key: true,
            notNull: true,
          },
          SUBJECTID: {
            "@EndUserText.label": "SUBJECTID",
            type: "cds.Integer64",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
        params: {
          COURSEIDPARAM: {
            "@EndUserText.label": "COURSEIDFILTER",
            type: "cds.String",
            length: 20,
            default: "1002",
            "@Analytics.variable.hidden": true,
          },
        },
      },
      OpportunityItems: {
        kind: "entity",
        "@ObjectModel.supportedCapabilities": [
          {
            "#": "ANALYTICAL_FACT",
          },
        ],
        "@EndUserText.label": "OpportunityItems",
        elements: {
          COURSEID: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEID",
            key: true,
          },
          SUBJECTID: {
            type: "cds.Integer64",
            "@EndUserText.label": "SUBJECTID",
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
      },
    },
  },
  analyticModelWithFilterVars1: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local_Table",
        elements: {
          "PRODUCT∞D0": {
            "@EndUserText.label": "Product ID",
            "@Consumption.filter.selectionType": {
              "#": "SINGLE",
            },
            "@Consumption.filter.mandatory": false,
            "@Consumption.filter.hidden": false,
            "@Consumption.filter.multipleSelections": false,
            "@Consumption.filter.defaultValue": "PR-1003",
            "@ObjectModel.text.element": [
              {
                "=": "Product∞D0∞T",
              },
            ],
            type: "cds.String",
            length: 5000,
          },
          SUBJECTID: {
            "@EndUserText.label": "SUBJECTID",
            type: "cds.Integer64",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
        params: {},
      },
      OpportunityItems: {
        kind: "entity",
        "@ObjectModel.supportedCapabilities": [
          {
            "#": "ANALYTICAL_FACT",
          },
        ],
        "@EndUserText.label": "OpportunityItems",
        elements: {
          COURSEID: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEID",
            key: true,
          },
          SUBJECTID: {
            type: "cds.Integer64",
            "@EndUserText.label": "SUBJECTID",
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
      },
    },
  },
  analyticModelWithFilterVars2: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local_Table",
        elements: {
          "PRODUCT∞D0": {
            "@EndUserText.label": "Product ID",
            "@Consumption.filter.selectionType": {
              "#": "SINGLE",
            },
            "@Consumption.filter.mandatory": false,
            "@Consumption.filter.hidden": false,
            "@Consumption.filter.multipleSelections": true,
            "@Consumption.filter.defaultRanges": [
              {
                sign: "AnalyticModelDefaultRangeSign.INCLUDE",
                option: "AnalyticModelDefaultRangeOption.EQ",
                low: "PR-1000",
                high: "",
              },
              {
                sign: "AnalyticModelDefaultRangeSign.INCLUDE",
                option: "AnalyticModelDefaultRangeOption.EQ",
                low: "PR-1010",
                high: "",
              },
            ],
            "@ObjectModel.text.element": [
              {
                "=": "Product∞D0∞T",
              },
            ],
            type: "cds.String",
            length: 5000,
          },
          SUBJECTID: {
            "@EndUserText.label": "SUBJECTID",
            type: "cds.Integer64",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
        params: {},
      },
      OpportunityItems: {
        kind: "entity",
        "@ObjectModel.supportedCapabilities": [
          {
            "#": "ANALYTICAL_FACT",
          },
        ],
        "@EndUserText.label": "OpportunityItems",
        elements: {
          COURSEID: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEID",
            key: true,
          },
          SUBJECTID: {
            type: "cds.Integer64",
            "@EndUserText.label": "SUBJECTID",
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
      },
    },
  },
  analyticModelWithNoSingleValuedVars1: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local_Table",
        elements: {
          COURSEID: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEID",
            key: true,
            notNull: true,
          },
          SUBJECTID: {
            "@EndUserText.label": "SUBJECTID",
            type: "cds.Integer64",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
        params: {
          COURSEIDPARAM: {
            "@EndUserText.label": "COURSEIDFILTER",
            type: "cds.String",
            length: 20,
            "@AnalyticsDetails.variable.usageType": {
              "#": "FILTER",
            },
            "@AnalyticsDetails.variable.selectionType": {
              "#": "SINGLE",
            },
            "@AnalyticsDetails.variable.referenceElement": {
              "=": "Status",
            },
            "@AnalyticsDetails.variable.multipleSelections": true,
            "@AnalyticsDetails.variable.mandatory": false,
          },
        },
      },
      OpportunityItems: {
        kind: "entity",
        "@ObjectModel.supportedCapabilities": [
          {
            "#": "ANALYTICAL_FACT",
          },
        ],
        "@EndUserText.label": "OpportunityItems",
        elements: {
          COURSEID: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEID",
            key: true,
          },
          SUBJECTID: {
            type: "cds.Integer64",
            "@EndUserText.label": "SUBJECTID",
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
      },
    },
  },
  analyticModelWithNoSingleValuedVars2: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local_Table",
        elements: {
          COURSEID: {
            "@EndUserText.label": "COURSEID",
            "@Consumption.filter.selectionType": {
              "#": "SINGLE",
            },
            "@Consumption.filter.mandatory": false,
            "@Consumption.filter.hidden": false,
            "@Consumption.filter.multipleSelections": true,
            "@Consumption.filter.defaultRanges": [
              {
                sign: "I",
                option: "EQ",
                low: "1002",
              },
            ],
            type: "cds.String",
            length: 20,
          },
          SUBJECTID: {
            "@EndUserText.label": "SUBJECTID",
            type: "cds.Integer64",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
        params: {},
      },
      OpportunityItems: {
        kind: "entity",
        "@ObjectModel.supportedCapabilities": [
          {
            "#": "ANALYTICAL_FACT",
          },
        ],
        "@EndUserText.label": "OpportunityItems",
        elements: {
          COURSEID: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEID",
            key: true,
          },
          SUBJECTID: {
            type: "cds.Integer64",
            "@EndUserText.label": "SUBJECTID",
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
      },
    },
  },
  analyticModelWithFilterVarsInParams1: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local_Table",
        elements: {
          "PRODUCT∞D0": {
            "@EndUserText.label": "Product ID",
            "@Consumption.filter.selectionType": {
              "#": "SINGLE",
            },
            "@Consumption.filter.mandatory": false,
            "@Consumption.filter.hidden": false,
            "@Consumption.filter.multipleSelections": false,
            "@Consumption.filter.defaultValue": "PR-1003",
            "@ObjectModel.text.element": [
              {
                "=": "Product∞D0∞T",
              },
            ],
            type: "cds.String",
            length: 5000,
          },
          SUBJECTID: {
            "@EndUserText.label": "SUBJECTID",
            type: "cds.Integer64",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
        params: {
          "PRODUCT∞D0": {
            "@EndUserText.label": "Product ID",
            type: "cds.String",
            "@AnalyticsDetails.variable.defaultValue": "PR-1003",
            "@Analytics.variable.hidden": false,
            "@AnalyticsDetails.variable.mandatory": false,
            "@AnalyticsDetails.variable.selectionType": {
              "#": "SINGLE",
            },
            "@AnalyticsDetails.variable.multipleSelections": false,
            length: 5000,
          },
        },
      },
      OpportunityItems: {
        kind: "entity",
        "@ObjectModel.supportedCapabilities": [
          {
            "#": "ANALYTICAL_FACT",
          },
        ],
        "@EndUserText.label": "OpportunityItems",
        elements: {
          COURSEID: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEID",
            key: true,
          },
          SUBJECTID: {
            type: "cds.Integer64",
            "@EndUserText.label": "SUBJECTID",
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
      },
    },
  },
  analyticModelWithFilterVarsInParams2: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local_Table",
        elements: {
          "PRODUCT∞D0": {
            "@EndUserText.label": "Product ID",
            "@Consumption.filter.selectionType": {
              "#": "SINGLE",
            },
            "@Consumption.filter.mandatory": false,
            "@Consumption.filter.hidden": false,
            "@Consumption.filter.multipleSelections": true,
            "@Consumption.filter.defaultRanges": [
              {
                sign: "AnalyticModelDefaultRangeSign.INCLUDE",
                option: "AnalyticModelDefaultRangeOption.EQ",
                low: "PR-1000",
                high: "",
              },
              {
                sign: "AnalyticModelDefaultRangeSign.INCLUDE",
                option: "AnalyticModelDefaultRangeOption.EQ",
                low: "PR-1010",
                high: "",
              },
            ],
            "@ObjectModel.text.element": [
              {
                "=": "Product∞D0∞T",
              },
            ],
            type: "cds.String",
            length: 5000,
          },
          SUBJECTID: {
            "@EndUserText.label": "SUBJECTID",
            type: "cds.Integer64",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
        params: {
          "PRODUCT∞D0": {
            "@EndUserText.label": "Product ID",
            type: "cds.String",
            "@Analytics.variable.hidden": false,
            "@AnalyticsDetails.variable.mandatory": false,
            "@AnalyticsDetails.variable.selectionType": {
              "#": "SINGLE",
            },
            "@AnalyticsDetails.variable.multipleSelections": true,
            "@AnalyticsDetails.variable.defaultRanges": [
              {
                sign: "AnalyticModelDefaultRangeSign.INCLUDE",
                option: "AnalyticModelDefaultRangeOption.EQ",
                low: "PR-1000",
                high: "",
              },
              {
                sign: "AnalyticModelDefaultRangeSign.INCLUDE",
                option: "AnalyticModelDefaultRangeOption.EQ",
                low: "PR-1010",
                high: "",
              },
            ],
            length: 5000,
            "@Dragonet.variable.originalDataType": "cds.String",
            "@Dragonet.variable.selectionType": "MultipleSingleValue",
          },
        },
      },
      OpportunityItems: {
        kind: "entity",
        "@ObjectModel.supportedCapabilities": [
          {
            "#": "ANALYTICAL_FACT",
          },
        ],
        "@EndUserText.label": "OpportunityItems",
        elements: {
          COURSEID: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEID",
            key: true,
          },
          SUBJECTID: {
            type: "cds.Integer64",
            "@EndUserText.label": "SUBJECTID",
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
      },
    },
  },
  analyticModelWithIntervalVarsInParams: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local_Table",
        elements: {
          "PRODUCT∞D0": {
            "@EndUserText.label": "Product ID",
            "@Consumption.filter.selectionType": {
              "#": "INTERVAL",
            },
            "@Consumption.filter.mandatory": false,
            "@Consumption.filter.hidden": false,
            "@Consumption.filter.multipleSelections": false,
            "@Consumption.filter.defaultValue": "10",
            "@Consumption.filter.defaultValueHigh": "20",
            "@ObjectModel.text.element": [
              {
                "=": "Product∞D0∞T",
              },
            ],
            type: "cds.String",
            length: 5000,
          },
          SUBJECTID: {
            "@EndUserText.label": "SUBJECTID",
            type: "cds.Integer64",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
        params: {
          "PRODUCT∞D0": {
            "@EndUserText.label": "Product ID",
            type: "cds.String",
            "@AnalyticsDetails.variable.defaultValue": "10",
            "@AnalyticsDetails.variable.defaultValueHigh": "20",
            "@Analytics.variable.hidden": false,
            "@AnalyticsDetails.variable.mandatory": false,
            "@AnalyticsDetails.variable.selectionType": {
              "#": "INTERVAL",
            },
            "@AnalyticsDetails.variable.multipleSelections": false,
            length: 5000,
          },
        },
      },
    },
  },
  analyticModelWithIntervalVarsEqOperatorInParams: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local_Table",
        elements: {
          "PRODUCT∞D0": {
            "@EndUserText.label": "Product ID",
            "@Consumption.filter.selectionType": {
              "#": "INTERVAL",
            },
            "@Consumption.filter.mandatory": false,
            "@Consumption.filter.hidden": false,
            "@Consumption.filter.multipleSelections": false,
            "@Consumption.filter.defaultValue": "10",
            "@ObjectModel.text.element": [
              {
                "=": "Product∞D0∞T",
              },
            ],
            type: "cds.String",
            length: 5000,
          },
          SUBJECTID: {
            "@EndUserText.label": "SUBJECTID",
            type: "cds.Integer64",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
        params: {
          "PRODUCT∞D0": {
            "@EndUserText.label": "Product ID",
            type: "cds.String",
            "@AnalyticsDetails.variable.defaultValue": "10",
            "@Analytics.variable.hidden": false,
            "@AnalyticsDetails.variable.mandatory": false,
            "@AnalyticsDetails.variable.selectionType": {
              "#": "INTERVAL",
            },
            "@AnalyticsDetails.variable.multipleSelections": false,
            length: 5000,
          },
        },
      },
    },
  },
  analyticModelWithRangeVarsInParams: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local_Table",
        elements: {
          "PRODUCT∞D0": {
            "@EndUserText.label": "Product ID",
            "@Consumption.filter.selectionType": {
              "#": "RANGE",
            },
            "@Consumption.filter.mandatory": false,
            "@Consumption.filter.hidden": false,
            "@Consumption.filter.multipleSelections": false,
            "@Consumption.filter.defaultRanges": [{ low: "10", high: "20", option: "BT" }],
            "@ObjectModel.text.element": [
              {
                "=": "Product∞D0∞T",
              },
            ],
            type: "cds.String",
            length: 5000,
          },
          SUBJECTID: {
            "@EndUserText.label": "SUBJECTID",
            type: "cds.Integer64",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
        params: {
          "PRODUCT∞D0": {
            "@EndUserText.label": "Product ID",
            type: "cds.String",
            "@AnalyticsDetails.variable.defaultRanges": [{ low: "10", high: "20", option: "BT" }],
            "@Analytics.variable.hidden": false,
            "@AnalyticsDetails.variable.mandatory": false,
            "@AnalyticsDetails.variable.selectionType": {
              "#": "RANGE",
            },
            "@AnalyticsDetails.variable.multipleSelections": false,
            length: 5000,
          },
        },
      },
    },
  },
  analyticModelWithMultiRangeVarsInParams: {
    definitions: {
      Local_Table: {
        kind: "entity",
        "@EndUserText.label": "Local_Table",
        elements: {
          "PRODUCT∞D0": {
            "@EndUserText.label": "Product ID",
            "@Consumption.filter.selectionType": {
              "#": "RANGE",
            },
            "@Consumption.filter.mandatory": false,
            "@Consumption.filter.hidden": false,
            "@Consumption.filter.multipleSelections": false,
            "@Consumption.filter.defaultRanges": [
              { low: "10", high: "20", option: "BT" },
              { low: "30", option: "EQ" },
            ],
            "@ObjectModel.text.element": [
              {
                "=": "Product∞D0∞T",
              },
            ],
            type: "cds.String",
            length: 5000,
          },
          SUBJECTID: {
            "@EndUserText.label": "SUBJECTID",
            type: "cds.Integer64",
            "@AnalyticsDetails.measureType": {
              "#": "BASE",
            },
            "@Aggregation.default": {
              "#": "SUM",
            },
          },
          INSTRUCTORID: {
            type: "cds.Integer64",
            "@EndUserText.label": "INSTRUCTORID",
          },
          COURSEFEE: {
            type: "cds.Integer64",
            "@EndUserText.label": "COURSEFEE",
          },
        },
        params: {
          "PRODUCT∞D0": {
            "@EndUserText.label": "Product ID",
            type: "cds.String",
            "@AnalyticsDetails.variable.defaultRanges": [
              { low: "10", high: "20", option: "BT" },
              { low: "30", option: "EQ" },
            ],
            "@Analytics.variable.hidden": false,
            "@AnalyticsDetails.variable.mandatory": false,
            "@AnalyticsDetails.variable.selectionType": {
              "#": "RANGE",
            },
            "@AnalyticsDetails.variable.multipleSelections": false,
            length: 5000,
          },
        },
      },
    },
  },
});

export const CsnElementsMocks = () => ({
  withoutFilterVar: CsnDocumentMocks().localTableWithParams.definitions.Local_Table.elements as unknown as ICsnElements,

  withFilterVar: {
    ...CsnDocumentMocks().analyticModelWithFilterVars1.definitions.Local_Table.elements,
    Opportunity: {
      "@EndUserText.label": "Opportunity",
      "@Consumption.filter.selectionType": {
        "#": "SINGLE",
      },
      "@Consumption.filter.mandatory": false,
      "@Consumption.filter.hidden": false,
      "@Consumption.filter.multipleSelections": false,
      type: "cds.String",
      length: 5000,
    },
  } as unknown as ICsnElements,

  withMultiSingleValuedFilterVar: {
    ...CsnDocumentMocks().localTableWithParams.definitions.Local_Table.elements,
    Opportunity: {
      "@EndUserText.label": "Opportunity",
      "@Consumption.filter.defaultRanges": [
        {
          sign: "I",
          option: "EQ",
          low: "10",
        },
      ],
      "@Consumption.filter.selectionType": {
        "#": "SINGLE",
      },
      "@Consumption.filter.mandatory": false,
      "@Consumption.filter.hidden": false,
      "@Consumption.filter.multipleSelections": true,
      type: "cds.String",
      length: 0,
    },
    BooleanElement: {
      "@EndUserText.label": "BooleanElement",
      "@Consumption.filter.defaultRanges": [
        {
          sign: "I",
          option: "EQ",
          low: "false",
        },
      ],
      "@Consumption.filter.selectionType": {
        "#": "SINGLE",
      },
      "@Consumption.filter.mandatory": false,
      "@Consumption.filter.hidden": false,
      "@Consumption.filter.multipleSelections": true,
      type: "cds.Boolean",
    },
  } as unknown as ICsnElements,

  withSingleValuedFilterVar: {
    ...CsnDocumentMocks().localTableWithParams.definitions.Local_Table.elements,
    ProductCategory: {
      "@EndUserText.label": "ProductCategory",
      "@Consumption.filter.selectionType": {
        "#": "SINGLE",
      },
      "@Consumption.filter.mandatory": false,
      "@Consumption.filter.hidden": false,
      "@Consumption.filter.multipleSelections": false,
      "@Consumption.derivation.lookupEntity": {
        "=": "MCT_Products",
      },
      type: "cds.Integer64",
    },
  } as unknown as ICsnElements,

  withIntervalFilterVar: {
    ...CsnDocumentMocks().localTableWithParams.definitions.Local_Table.elements,
    Opportunity: {
      "@EndUserText.label": "Opportunity",
      "@Consumption.filter.selectionType": {
        "#": "INTERVAL",
      },
      "@Consumption.filter.defaultValue": false,
      "@Consumption.filter.defaultValueHigh": true,
      "@Consumption.filter.mandatory": false,
      "@Consumption.filter.hidden": false,
      "@Consumption.filter.multipleSelections": false,
      type: "cds.Boolean",
    },
  } as unknown as ICsnElements,

  withRangeFilterVar: {
    ...CsnDocumentMocks().localTableWithParams.definitions.Local_Table.elements,
    Opportunity: {
      "@EndUserText.label": "Opportunity",
      "@Consumption.filter.selectionType": {
        "#": "RANGE",
      },
      "@Consumption.filter.defaultRanges": [{ low: "10", high: "20", option: "BT" }],
      "@Consumption.filter.mandatory": false,
      "@Consumption.filter.hidden": false,
      "@Consumption.filter.multipleSelections": false,
      type: "cds.String",
      length: 5000,
    },
  } as unknown as ICsnElements,
});

export const CsnParamsMocks = () => ({
  default: CsnDocumentMocks().localTableWithParams.definitions.Local_Table.params as unknown as ICsnParams,

  withManyTypes: CsnDocumentMocks().localTableWithDiffParamTypes.definitions.Local_Table
    .params as unknown as ICsnParams,

  withMultiSingleValuedVar: CsnDocumentMocks().analyticModelWithNoSingleValuedVars1.definitions.Local_Table
    .params as unknown as ICsnParams,

  withIntervalVar: CsnDocumentMocks().analyticModelWithIntervalVarsInParams.definitions.Local_Table
    .params as unknown as ICsnParams,

  withRangeVar: CsnDocumentMocks().analyticModelWithRangeVarsInParams.definitions.Local_Table
    .params as unknown as ICsnParams,
});

export const ParamsMapMocks = () => ({
  validInteger: {
    name: "validInteger",
    dataType: "cds.Integer",
    value: "10",
    selectionType: ParameterSelectionType.SINGLE_VALUE,
  },
  validTrueBoolean: {
    name: "validTrueBoolean",
    dataType: "cds.Boolean",
    value: "true",
    selectionType: ParameterSelectionType.SINGLE_VALUE,
  },
  validFalseBoolean: {
    name: "validFalseBoolean",
    dataType: "cds.Boolean",
    value: false as any,
    selectionType: ParameterSelectionType.SINGLE_VALUE,
  },
  validString: {
    name: "validString",
    dataType: "cds.String",
    value: "'This_Is_a_Valid String123À-ÿ_. `/\\$!@#&%^=~?+|<>-:{}*[]'",
    selectionType: ParameterSelectionType.SINGLE_VALUE,
  },
  validStringAlphabets: {
    name: "validStringAlphabets",
    dataType: "cds.String",
    value: "'Кириллица Ελληνικό ქართული'",
    selectionType: ParameterSelectionType.SINGLE_VALUE,
  },
  validEmptyString: {
    name: "validEmptyString",
    dataType: "cds.String",
    value: "''",
    selectionType: ParameterSelectionType.SINGLE_VALUE,
  },
  validDate: {
    name: "validDate",
    dataType: "cds.Date",
    value: "2021-09-10",
    selectionType: ParameterSelectionType.SINGLE_VALUE,
  },
  validDateTime: {
    name: "validDateTime",
    dataType: "cds.DateTime",
    value: "2021-09-10T14:30:00.000Z",
    selectionType: ParameterSelectionType.SINGLE_VALUE,
  },
  validBinary: {
    name: "validBinary",
    dataType: "cds.Binary",
    value: "binary'MTAxMDEwMTA='",
    length: 20,
    selectionType: ParameterSelectionType.SINGLE_VALUE,
  },
  validStringForMultiSingleValued: {
    name: "validStringForMultiSingleValued",
    dataType: "cds.String",
    value: `'IN "test/string"'`,
    selectionType: ParameterSelectionType.MULTIPLE_SINGLE_VALUE,
  },
  validIntegerForIntervalIN: {
    name: "validIntegerForIntervalIN",
    dataType: "cds.Integer",
    value: "'IN 10,20'",
    selectionType: ParameterSelectionType.INTERVAL,
  },
  validIntegerForIntervalBT: {
    name: "validIntegerForIntervalBT",
    dataType: "cds.Integer",
    value: "'BT 10,20'",
    selectionType: ParameterSelectionType.INTERVAL,
  },
  validIntegerForIntervalEQ: {
    name: "validIntegerForIntervalEQ",
    dataType: "cds.Integer",
    value: "'EQ 10'",
    selectionType: ParameterSelectionType.INTERVAL,
  },
  validIntegerForRange: {
    name: "validIntegerForRange",
    dataType: "cds.Integer",
    value: "'GE 10'",
    selectionType: ParameterSelectionType.RANGE,
    operator: VariableOperator.GE,
  },

  invalidString: {
    name: "invalidString",
    dataType: "cds.String",
    value: "not-a-string",
    selectionType: ParameterSelectionType.SINGLE_VALUE,
  },
  invalidCharactersString: {
    name: "invalidCharactersString",
    dataType: "cds.String",
    value: "'Invalid ) Characters ;'",
    selectionType: ParameterSelectionType.SINGLE_VALUE,
  },
  invalidDate: {
    name: "invalidDate",
    dataType: "cds.Date",
    value: "not-a-date",
    selectionType: ParameterSelectionType.SINGLE_VALUE,
  },
  invalidDateTime: {
    name: "invalidDateTime",
    dataType: "cds.DateTime",
    value: "not-a-dateTime",
    selectionType: ParameterSelectionType.SINGLE_VALUE,
  },
  invalidStringLength: {
    name: "invalidStringLength",
    dataType: "cds.String",
    value: "'exceeding-string'",
    length: 5,
    selectionType: ParameterSelectionType.SINGLE_VALUE,
  },
  invalidLengthForMultiSingleValued: {
    name: "invalidLengthForMultiSingleValued",
    dataType: "cds.String",
    value: "'",
    selectionType: ParameterSelectionType.MULTIPLE_SINGLE_VALUE,
  },
  invalidValuesQuantityForMultiSingleValued: {
    name: "invalidValuesQuantityForMultiSingleValued",
    dataType: "cds.String",
    value: "'IN '",
    selectionType: ParameterSelectionType.MULTIPLE_SINGLE_VALUE,
  },
  invalidValuesQuantityForGERange: {
    name: "invalidValuesQuantityForGERange",
    dataType: "cds.Integer",
    value: "'GE '",
    selectionType: ParameterSelectionType.RANGE,
  },
  invalidValuesQuantityForBTRange: {
    name: "invalidValuesQuantityForBTRange",
    dataType: "cds.Integer",
    value: "'BT 10,'",
    selectionType: ParameterSelectionType.RANGE,
  },
  invalidValuesQuantityForInterval: {
    name: "invalidValuesQuantityForInterval",
    dataType: "cds.Integer",
    value: "'IN 10,'",
    selectionType: ParameterSelectionType.INTERVAL,
  },

  undefinedDataType: {
    name: "undefinedDataType",
    value: "'IN '",
    selectionType: ParameterSelectionType.MULTIPLE_SINGLE_VALUE,
  },
});

export const repoResponseDependenciesAssociations = {
  properties: {
    "#spaceName": "SPACE_SCHEMA",
  },
  id: "BE2321E516A62667190002CEF785CDA7",
  changeVersion: -99,
  qualifiedName: "MCT_Controlling_Area_View",
  kind: "entity",
  name: "MCT_Controlling_Area_View",
  folderId: "9A3D134046C26102180074B64F15020F",
  hash: "dc19d969a9d2043c64fb5ae4769a36a1f62b7c36d24f8f680c26150a9e4f89f5",
  dependencies: [
    {
      id: "FFD0D1B5C04DC04419005FC2F4EE6C7B",
      kind: "entity",
      folderId: "9A3D134046C26102180074B64F15020F",
      dependencyType: "csn.entity.association",
      qualifiedName: "Local_Table",
      impact: true,
      lineage: false,
      properties: {
        "#spaceName": "SCHEMA",
      },
      inaccessibleDependencies: [],
      dependencies: [],
      name: "Local_Table",
    },
  ],
  inaccessibleDependencies: [],
} as IRepositoryElementDependency;

export const expectedRepoResponseIdAssociation = {
  properties: {
    "#technicalType": "DWC_VIEW",
  },
  id: "BE2321E516A62667190002CEF785CDA7",
  changeVersion: -99,
  qualifiedName: "MCT_Controlling_Area_View",
  kind: "entity",
  name: "MCT_Controlling_Area_View",
  creator: "TEST",
  folderId: "9A3D134046C26102180074B64F15020F",
  hash: "dc19d969a9d2043c64fb5ae4769a36a1f62b7c36d24f8f680c26150a9e4f89f5",
} as IRepositoryObject;

export const repoResponseDependencies = {
  properties: {
    "#spaceName": "SCHEMA",
  },
  id: "BE2321E516A62667190002CEF785CDA7",
  changeVersion: -99,
  qualifiedName: "Local_Table",
  kind: "entity",
  name: "Local_Table",
  folderId: "9A3D134046C26102180074B64F15020F",
  hash: "dc19d969a9d2043c64fb5ae4769a36a1f62b7c36d24f8f680c26150a9e4f89f5",
  dependencies: [],
  inaccessibleDependencies: [],
} as IRepositoryElementDependency;

export const expectedRepoResponseId = {
  properties: {
    "#technicalType": "DWC_VIEW",
  },
  id: "BE2321E516A62667190002CEF785CDA7",
  changeVersion: -99,
  qualifiedName: "Local_Table",
  kind: "entity",
  name: "Local_Table",
  creator: "TEST",
  folderId: "9A3D134046C26102180074B64F15020F",
  hash: "dc19d969a9d2043c64fb5ae4769a36a1f62b7c36d24f8f680c26150a9e4f89f5",
} as IRepositoryObject;

export const expectedRepoResponseIdAM = {
  id: "BE2321E516A62667190002CEF785CDA7",
  changeVersion: -99,
  properties: {
    "#technicalType": "DWC_ANALYTIC_MODEL",
  },
  qualifiedName: "analyticaModelWithAssociations",
  kind: "entity",
  name: "analyticaModelWithAssociations",
  creator: "TEST",
  folderId: "9A3D134046C26102180074B64F15020F",
  hash: "dc19d969a9d2043c64fb5ae4769a36a1f62b7c36d24f8f680c26150a9e4f89f5",
} as IRepositoryObject;
