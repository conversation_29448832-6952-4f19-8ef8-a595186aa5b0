/** @format */

import { FeatureFlagProvider } from "@sap/dwc-context-checks";
import chai, { expect } from "chai";
import sinon from "sinon";
import sinon<PERSON>hai from "sinon-chai";
import { mockReq } from "sinon-express-mock";
import { RepositoryObjectClient } from "../../../../../repository/client/repositoryObjectClient";
import { RequestContext } from "../../../../../repository/security/requestContext";
import * as getSpaceUuidFromNameLib from "../../../../../routes/dataAccess/lib/getSpaceUuidFromName";
import * as resolvedDbClientLib from "../../../../../routes/dataAccess/shared/resolveDbClient";
import * as resolveFullCsnLib from "../../../../../routes/dataAccess/shared/resolveFullCsn";
import { CsnRepositoryTag } from "../../../../../routes/dataAccess/types";
import { FetchConsumptionCSN } from "../../../../../routes/odata/consumption/fetchConsumptionCsn";
import { getAccessContext } from "../../../../repository/repository.utility";
import {
  CsnDocumentMocks,
  expectedRepoResponseIdAM,
  expectedRepoResponseIdAssociation,
  repoResponseDependenciesAssociations,
} from "../../mocks/csnMocks";

chai.use(sinonChai);
describe("fetchConsumptionCsn", () => {
  let testContext: RequestContext;
  let sandbox: sinon.SinonSandbox;
  let resolveFullCsnStub: sinon.SinonStub;
  let resolveFullCsnWithAdvisorStub: sinon.SinonStub;
  let getObjectStub: sinon.SinonStub;
  let getObjectDependenciesStub: sinon.SinonStub;
  let invalidateCacheSpy: sinon.SinonSpy;

  let ffProviderStub: sinon.SinonStub;
  let relationalAssociationFFStub: sinon.SinonStub;

  const SCHEMA_NAME = "SCHEMA";
  const ENTITY_NAME = "Local_Table";
  const SPACE_ID = "SPACEID";

  const req = mockReq({
    body: {},
    params: { space: SCHEMA_NAME, entity: ENTITY_NAME },
  });

  let res: any;

  before(() => {
    sandbox = sinon.createSandbox();
    testContext = getAccessContext();
    req.context = testContext;
    sandbox.stub(resolvedDbClientLib, "resolveDbClient").callsFake(() => Promise.resolve({} as any));
    sandbox.stub(getSpaceUuidFromNameLib, "getSpaceUuidFromName").resolves(SPACE_ID);

    resolveFullCsnStub = sandbox.stub(resolveFullCsnLib, "resolveFullCsn");
    resolveFullCsnWithAdvisorStub = sandbox.stub(resolveFullCsnLib, "resolveFullCsnWithAdvisor");

    getObjectStub = sandbox.stub(RepositoryObjectClient, "getObject");
    getObjectDependenciesStub = sandbox.stub(RepositoryObjectClient, "getObjectDependencies");

    invalidateCacheSpy = sinon.spy(FetchConsumptionCSN, "invalidateCacheEntries");

    ffProviderStub = sandbox.stub(FeatureFlagProvider, "isFeatureActive");
    relationalAssociationFFStub = ffProviderStub
      .callThrough()
      .withArgs(sinon.match.any, "DWCO_ODATA_RELATIONAL_ASSOCIATION");
  });

  beforeEach(() => {
    res = { locals: {} };
    resolveFullCsnStub.resolves(CsnDocumentMocks().localTable);
    resolveFullCsnWithAdvisorStub.resolves(CsnDocumentMocks().localTable);
    relationalAssociationFFStub.resolves(false);
  });

  afterEach(() => {
    resolveFullCsnStub.resetHistory();
    resolveFullCsnWithAdvisorStub.resetHistory();
    getObjectDependenciesStub.resetHistory();
    invalidateCacheSpy.resetHistory();
  });

  after(() => {
    sandbox.restore();
  });

  describe("csnForConsumption", () => {
    afterEach(async () => {
      await FetchConsumptionCSN.listenToDeployer({
        tenantId: testContext.tenantId,
        spaceId: SCHEMA_NAME,
        correlationId: "testCorrelationID",
        entityNames: [ENTITY_NAME],
      });
    });

    it("should call resolveFullCsnWithAdvisor method", async () => {
      await FetchConsumptionCSN.csnForConsumption(req, res);
      expect(resolveFullCsnWithAdvisorStub).to.have.been.calledOnce;
    });

    it("should not call resolveFullCsnWithAdvisor if is analytic model", async () => {
      res.locals.isAnalyticModel = true;
      await FetchConsumptionCSN.csnForConsumption(req, res);
      expect(resolveFullCsnWithAdvisorStub).to.not.have.been.called;
      expect(resolveFullCsnStub).to.have.been.calledOnce;
    });

    it("should return csn with extended annotations", async () => {
      const expectedMaxRowSize = 100000;
      const fullCsn = await FetchConsumptionCSN.csnForConsumption(req, res);
      expect(fullCsn.definitions[ENTITY_NAME]["@cds.query.limit.max"]).to.eql(expectedMaxRowSize);
      expect(fullCsn.definitions[ENTITY_NAME]["@cds.query.limit.default"]).to.eql(expectedMaxRowSize / 2);
    });

    it("should return default annotation values when empty csn elements", async () => {
      resolveFullCsnWithAdvisorStub.resolves(CsnDocumentMocks().noElements);
      const defaultMaxRows = 1000;

      const fullCsn = await FetchConsumptionCSN.csnForConsumption(req, res);
      expect(fullCsn.definitions[ENTITY_NAME]["@cds.query.limit.max"]).to.eql(defaultMaxRows);
      expect(fullCsn.definitions[ENTITY_NAME]["@cds.query.limit.default"]).to.eql(defaultMaxRows / 2);
    });

    it("should call resolveFullCsnWithAdvisor with argument for associations when `DWCO_ODATA_RELATIONAL_ASSOCIATION` is enabled", async () => {
      relationalAssociationFFStub.resolves(true);

      const adaptationContext = {
        deployedEntity: {},
        entityName: ENTITY_NAME,
        spaceId: SPACE_ID,
        schemaName: SCHEMA_NAME,
        dbClient: {},
        requestContext: req.context,
        isPersistedEntity: false,
        checkPrivilege: false,
      };

      await FetchConsumptionCSN.csnForConsumption(req, res);

      relationalAssociationFFStub.resolves(false);

      expect(resolveFullCsnWithAdvisorStub).to.have.been.calledWith(
        adaptationContext,
        req,
        CsnRepositoryTag.DeployedCsnForConsumptionWithAssociations
      );
    });

    it("should call fetch csn only once when cache enabled", async () => {
      await FetchConsumptionCSN.csnForConsumption(req, res);
      await FetchConsumptionCSN.csnForConsumption(req, res);

      expect(resolveFullCsnWithAdvisorStub).to.have.been.calledOnce;
    });

    it("should call fetch csn twice when cache cleared", async () => {
      await FetchConsumptionCSN.csnForConsumption(req, res);
      await FetchConsumptionCSN.listenToDeployer({
        tenantId: testContext.tenantId,
        spaceId: SCHEMA_NAME,
        correlationId: "testCorrelationID",
        entityNames: [ENTITY_NAME],
      });
      await FetchConsumptionCSN.csnForConsumption(req, res);

      expect(resolveFullCsnWithAdvisorStub).to.have.been.calledTwice;
    });

    it("should call fetch csn twice when cache cleared of association when `DWCO_ODATA_RELATIONAL_ASSOCIATION` is enabled ", async () => {
      relationalAssociationFFStub.resolves(true);

      resolveFullCsnWithAdvisorStub.resolves(CsnDocumentMocks().localTableWithAssociations);

      getObjectStub.resolves([expectedRepoResponseIdAssociation]);
      getObjectDependenciesStub.resolves([repoResponseDependenciesAssociations]);

      await FetchConsumptionCSN.csnForConsumption(req, res);
      await FetchConsumptionCSN.listenToDeployer({
        tenantId: testContext.tenantId,
        spaceId: "SPACE_SCHEMA",
        correlationId: "testCorrelationID",
        entityNames: ["MCT_Controlling_Area_View"],
      });

      await FetchConsumptionCSN.csnForConsumption(req, res);

      relationalAssociationFFStub.resolves(false);

      expect(resolveFullCsnWithAdvisorStub).to.have.been.calledTwice;
      expect(invalidateCacheSpy).to.have.been.calledTwice;
    });

    it("should not call getObjectsToInvalidate once when AM and when `DWCO_ODATA_RELATIONAL_ASSOCIATION` is enabled ", async () => {
      relationalAssociationFFStub.resolves(true);

      getObjectStub.resolves([expectedRepoResponseIdAM]);

      await FetchConsumptionCSN.listenToDeployer({
        tenantId: testContext.tenantId,
        spaceId: "SPACE_SCHEMA",
        correlationId: "testCorrelationID",
        entityNames: ["analyticModelWithAssociations"],
      });

      relationalAssociationFFStub.resolves(false);

      expect(getObjectDependenciesStub).to.not.have.been.called;
    });

    it("should throw error when resolveFullCsnWithAdvisorStub raises error", async () => {
      const error = new Error();
      resolveFullCsnWithAdvisorStub.rejects(error);

      try {
        await FetchConsumptionCSN.csnForConsumption(req, res);
        expect.fail();
      } catch (e) {
        expect(e).to.be.exist;
      }
    });

    it("should replace inavalid chars in elements", async () => {
      res.locals.isAnalyticModel = true;
      const csn = await FetchConsumptionCSN.csnForConsumption(req, res);
      expect(Object.keys(csn.definitions.Local_Table.elements!)).to.eql([
        "COURSEID",
        "SUBJECTID",
        "INSTRUCTORID",
        "COURSEFEE",
        "_BIC_COURSEFEE",
      ]);
    });
  });

  describe("calculateMaxRowNumber", () => {
    let sandbox: sinon.SinonSandbox;
    let getRowSizeStub: sinon.SinonStub;
    let getElementsCountStub: sinon.SinonStub;

    const entityDefinition = {} as any;

    before(() => {
      sandbox = sinon.createSandbox();
      getElementsCountStub = sandbox.stub(FetchConsumptionCSN, "getElementsCount");
      getRowSizeStub = sandbox.stub(FetchConsumptionCSN, "getRowSize");
    });

    afterEach(() => {
      getElementsCountStub.reset();
      getRowSizeStub.reset();
    });

    after(() => {
      getElementsCountStub.restore();
      getRowSizeStub.restore();
    });

    it("should calculate max row number", async () => {
      // 100 MB / (20 KB * 2) = 2500 rows
      // 10M cells / 10K elements = 1000 rows
      // Min(2500, 1000) = 1000
      // Min(1000, MAX_CELL_NUMBER) = 1000

      const isAnalytical = true;

      getRowSizeStub.returns(20000);
      getElementsCountStub.returns(10000);

      const maxRowNumber = await FetchConsumptionCSN.calculateMaxRowNumber(req.context, entityDefinition, isAnalytical);

      expect(maxRowNumber).to.eql(1000);
    });

    it("should return minimum row number when row size is zero", async () => {
      // Min(MIN_ROW_NUMBER, MAX_CELL_NUMBER)

      const isAnalytical = false;

      getRowSizeStub.returns(0);

      const maxRowNumber = await FetchConsumptionCSN.calculateMaxRowNumber(req.context, entityDefinition, isAnalytical);

      expect(maxRowNumber).to.eql(FetchConsumptionCSN.MIN_ROW_NUMBER);
    });

    it("should return maximum row number when calculated row number exceeds limit", async () => {
      // 50 MB / (1 B * 2) = 25M rows
      // Min(25M, MAX_CELL_NUMBER)

      const isAnalytical = false;

      getRowSizeStub.returns(1);

      const maxRowNumber = await FetchConsumptionCSN.calculateMaxRowNumber(req.context, entityDefinition, isAnalytical);

      expect(maxRowNumber).to.eql(FetchConsumptionCSN.MAX_ROW_NUMBER);
    });
  });

  describe("adjustMaxRowNumberBasedOnCells", () => {
    const csnElements = {} as any;
    let getElementsCountStub: sinon.SinonStub;

    before(() => {
      getElementsCountStub = sandbox.stub(FetchConsumptionCSN, "getElementsCount");
    });

    it("low number of elements", () => {
      const MAX_ROW_NUMBER = 50000;
      getElementsCountStub.returns(3);

      const adjustedMaxRowNumber = FetchConsumptionCSN.adjustMaxRowNumberBasedOnCells(
        testContext,
        csnElements,
        MAX_ROW_NUMBER
      );

      expect(adjustedMaxRowNumber).to.eql(MAX_ROW_NUMBER);
    });

    it("high number of elements", () => {
      const MAX_ROW_NUMBER = 50000;
      getElementsCountStub.returns(250);

      const adjustedMaxRowNumber = FetchConsumptionCSN.adjustMaxRowNumberBasedOnCells(
        testContext,
        csnElements,
        MAX_ROW_NUMBER
      );

      expect(adjustedMaxRowNumber).to.eql(40000);
    });
  });

  describe("getRowSize", () => {
    it("should return correct value for csn document elements", () => {
      const expectedRowSize = 30448;
      const rowSize = FetchConsumptionCSN.getRowSize(
        CsnDocumentMocks().localTableWithDiffParamTypes.definitions[ENTITY_NAME] as any,
        testContext
      );
      expect(rowSize).to.eql(expectedRowSize);
    });
  });

  describe("getDataTypeSize", () => {
    it("should return default value when datatype cds.Integer is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.Integer");
      expect(size).to.eql(4);
    });

    it("should return default value when datatype hana.SMALLINT is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("hana.SMALLINT");
      expect(size).to.eql(4);
    });

    it("should return default value when datatype hana.TINYINT is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("hana.TINYINT");
      expect(size).to.eql(4);
    });

    it("should return default value when datatype cds.hana.SMALLINT is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.hana.SMALLINT");
      expect(size).to.eql(4);
    });

    it("should return default value when datatype cds.hana.TINYINT is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.hana.TINYINT");
      expect(size).to.eql(4);
    });

    it("should return default value when datatype cds.Integer64 is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.Integer64");
      expect(size).to.eql(8);
    });

    it("should return default value when datatype cds.Decimal is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.Decimal");
      expect(size).to.eql(20);
    });

    it("should return default value when datatype cds.hana.SMALLDECIMAL is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.hana.SMALLDECIMAL");
      expect(size).to.eql(20);
    });

    it("should return default value when datatype hana.SMALLDECIMAL is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("hana.SMALLDECIMAL");
      expect(size).to.eql(20);
    });

    it("should return default value when datatype cds.hana.REAL is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.hana.REAL");
      expect(size).to.eql(20);
    });

    it("should return default value when datatype hana.REAL is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("hana.REAL");
      expect(size).to.eql(20);
    });

    it("should return default value when datatype cds.Double is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.Double");
      expect(size).to.eql(20);
    });

    it("should return default value when datatype cds.BinaryFloat is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.BinaryFloat");
      expect(size).to.eql(20);
    });

    it("should return default value when datatype cds.Date is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.Date");
      expect(size).to.eql(10);
    });

    it("should return default value when datatype cds.LocalDate is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.LocalDate");
      expect(size).to.eql(10);
    });

    it("should return default value when datatype cds.Time is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.Time");
      expect(size).to.eql(8);
    });

    it("should return default value when datatype cds.LocalTime is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.LocalTime");
      expect(size).to.eql(8);
    });

    it("should return default value when datatype cds.DateTime is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.DateTime");
      expect(size).to.eql(20);
    });

    it("should return default value when datatype cds.UTCDateTime is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.UTCDateTime");
      expect(size).to.eql(20);
    });

    it("should return default value when datatype cds.Timestamp is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.Timestamp");
      expect(size).to.eql(24);
    });

    it("should return default value when datatype cds.UTCTimestamp is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.UTCTimestamp");
      expect(size).to.eql(24);
    });

    it("should return default value when datatype cds.String is found", () => {
      const expectedSize = 35;
      const size = FetchConsumptionCSN.getDataTypeSize("cds.String", expectedSize);
      expect(size).to.eql(expectedSize);
    });

    it("should return default value when datatype cds.hana.CHAR is found", () => {
      const expectedSize = 35;
      const size = FetchConsumptionCSN.getDataTypeSize("cds.hana.CHAR", expectedSize);
      expect(size).to.eql(expectedSize);
    });

    it("should return default value when datatype cds.hana.NCHAR is found", () => {
      const expectedSize = 35;
      const size = FetchConsumptionCSN.getDataTypeSize("cds.hana.NCHAR", expectedSize);
      expect(size).to.eql(expectedSize);
    });

    it("should return default value when datatype cds.hana.VARCHAR is found", () => {
      const expectedSize = 35;
      const size = FetchConsumptionCSN.getDataTypeSize("cds.hana.VARCHAR", expectedSize);
      expect(size).to.eql(expectedSize);
    });

    it("should return default value when datatype hana.CHAR is found", () => {
      const expectedSize = 35;
      const size = FetchConsumptionCSN.getDataTypeSize("hana.CHAR", expectedSize);
      expect(size).to.eql(expectedSize);
    });

    it("should return default value when datatype hana.NCHAR is found", () => {
      const expectedSize = 35;
      const size = FetchConsumptionCSN.getDataTypeSize("hana.NCHAR", expectedSize);
      expect(size).to.eql(expectedSize);
    });

    it("should return default value when datatype hana.VARCHAR is found", () => {
      const expectedSize = 35;
      const size = FetchConsumptionCSN.getDataTypeSize("hana.VARCHAR", expectedSize);
      expect(size).to.eql(expectedSize);
    });

    it("should return default value when datatype cds.Binary is found", () => {
      const expectedSize = 35;
      const size = FetchConsumptionCSN.getDataTypeSize("cds.Binary", expectedSize);
      expect(size).to.eql(expectedSize);
    });

    it("should return default value when datatype hana.BINARY is found", () => {
      const expectedSize = 35;
      const size = FetchConsumptionCSN.getDataTypeSize("hana.BINARY", expectedSize);
      expect(size).to.eql(expectedSize);
    });

    it("should return default value when datatype cds.hana.BINARY is found", () => {
      const expectedSize = 35;
      const size = FetchConsumptionCSN.getDataTypeSize("cds.hana.BINARY", expectedSize);
      expect(size).to.eql(expectedSize);
    });

    it("should return default value when datatype cds.LargeBinary is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.LargeBinary");
      expect(size).to.eql(5000);
    });

    it("should return default value when datatype cds.LargeString is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.LargeString");
      expect(size).to.eql(5000);
    });

    it("should return default value when datatype cds.hana.CLOB is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.hana.CLOB");
      expect(size).to.eql(5000);
    });

    it("should return default value when datatype hana.CLOB is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("hana.CLOB");
      expect(size).to.eql(5000);
    });

    it("should return default value when datatype cds.UUID is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.UUID");
      expect(size).to.eql(36);
    });

    it("should return default value when datatype hana.ST_POINT is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("hana.ST_POINT");
      expect(size).to.eql(100);
    });

    it("should return default value when datatype hana.ST_GEOMETRY is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("hana.ST_GEOMETRY");
      expect(size).to.eql(100);
    });

    it("should return default value when datatype cds.hana.ST_POINT is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.hana.ST_POINT");
      expect(size).to.eql(100);
    });

    it("should return default value when datatype cds.hana.ST_GEOMETRY is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("cds.hana.ST_GEOMETRY");
      expect(size).to.eql(100);
    });

    it("should return default value when datatype is not is found", () => {
      const size = FetchConsumptionCSN.getDataTypeSize("unknown");
      expect(size).to.eql(100);
    });
  });
});
