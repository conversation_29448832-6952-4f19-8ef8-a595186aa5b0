/**
 * eslint-disable no-only-tests/no-only-tests
 *
 * @format
 */

import { SacClient } from "@sap/dwc-sac-client";
import { escapeQuery } from "@sap/enterprise-search-objects";
import { assert } from "chai";
import { Express } from "express";
import sinon from "ts-sinon";
import { testTenantUuid } from "../../../lib/node";
import { RequestContext } from "../../../repository/security/requestContext";
import { agentWithDefaults, getTestServer } from "../../routes/route.utilty";
import * as JouleRolesTestData from "./rolesTestData";

const jouleAllRolesEndpoint = "/joule/allroles";

describe("/joule/allroles", function () {
  let context: RequestContext;
  let server: Express;
  let agent: any;
  let sandbox: sinon.SinonSandbox;

  before(() => {
    context = RequestContext.createFromTenantId(testTenantUuid);
    server = getTestServer({ tenantId: testTenantUuid, context });
    agent = agentWithDefaults(server, testTenantUuid);
    sandbox = sinon.createSandbox();
  });

  afterEach(async () => {
    sandbox.restore();
  });

  it("should return all roles", async () => {
    sandbox.stub(SacClient, "call").resolves(JouleRolesTestData.mockEshProfilesResponse);
    let response = await agent.get(jouleAllRolesEndpoint).query({ showDetails: "true" });
    const expectedRoles = JSON.parse(
      JSON.stringify(JouleRolesTestData.testRolesResultAll.sort((a, b) => a.name.localeCompare(b.name)))
    );
    const expectedResult = {
      count: 4,
      countGlobalRoles: 2,
      countScopedRoles: 2,
      roles: expectedRoles,
    };
    assert.equal(response.status, 200);
    assert.deepEqual(response.body, expectedResult);

    // showDetails = false
    response = await agent.get(jouleAllRolesEndpoint).query({ showDetails: "false" });
    expectedResult.roles = expectedRoles.map((role) => {
      delete role.userIds;
      delete role.scopeIds;
      return role;
    });

    assert.equal(response.status, 200);
    assert.deepEqual(response.body, expectedResult);
  });

  it("check ESH Query", async () => {
    const sacClientStub = sandbox.stub(SacClient, "call").resolves({
      value: [JouleRolesTestData.mockEshProfiles.TEST_GLOBAL_ROLE],
    });
    const query = {
      $top: 1,
      $skip: 1,
      profileId: "PROFILE:t.D:TEST_GLOBAL_ROLE",
      name: "TEST GLOBAL ROLE",
      startsWith: "TEST",
      endsWith: "ROLE",
      contains: "GLOBAL",
      roleType: 0,
      description: "Test",
      userId: "USER1,USER2",
      spaceId: "SCOPE1,SCOPE2",
      createdFrom: "2025-05-22T00:00:00.0Z",
      createdTo: "2025-05-22T23:59:59.0Z",
      showDetails: "true",
    };
    const response = await agent.get(jouleAllRolesEndpoint).query(query);

    const expectedResult = {
      count: 1,
      countGlobalRoles: 1,
      countScopedRoles: 0,
      roles: JouleRolesTestData.testRolesResultAll.filter((role) => role.id === "TEST_GLOBAL_ROLE"),
    };
    const { url } = sacClientStub.getCall(0).args[0];
    const eshQueryParams = new URL(url).searchParams;

    assert(eshQueryParams.has("$top", "1"), "Error query parameter: $top");
    assert(eshQueryParams.has("$skip", "1"), "Error query parameter: $skip");
    assert(eshQueryParams.has("$orderby", "NAME"), "Error query parameter: $skip");
    assert(eshQueryParams.has("facets", "SCOPE_ROLE"), "Error query parameter: facets");
    assert(
      eshQueryParams.has(
        "$apply",
        `filter(Search.search(query='SCOPE:esh_ums_profiles ((NAME:EQ:"${query.name}" AND NAME:EQ:"${
          query.startsWith
        }*" AND NAME:EQ:"*${query.endsWith}" AND NAME:"${query.contains}"~0.7) AND (PROFILE_ID:EQ(S):${escapeQuery(
          query.profileId
        )} AND SCOPE_ROLE:EQ(S):${query.roleType} AND DESCRIPTION:"${
          query.description
        }" AND USER_ID:AND(${query.userId.replace(",", " ")}) AND SCOPE_NAME:AND(${query.spaceId.replace(
          ",",
          " "
        )}) AND CREATED_AT:GE:"${query.createdFrom}" AND CREATED_AT:LE:"${query.createdTo}"))'))`
      ),
      "Error query parameter: $apply"
    );

    assert.equal(response.status, 200);
    assert.deepEqual(response.body, expectedResult);
  });

  it("check ESH Query - predefined roles: Global", async () => {
    const sacClientStub = sandbox.stub(SacClient, "call").resolves({
      value: [JouleRolesTestData.mockEshProfiles.TEST_GLOBAL_ROLE_PREDEFINED],
    });
    const query: any = {
      name: "DW Administrator",
      startsWith: "DW",
      endsWith: "Administrator",
      contains: "DW Administrator",
      roleType: 0,
      userId: "TEST_USER_1",
      showDetails: "true",
    };

    const expectedRoleID = "*" + escapeQuery(":Data_Warehouse_Cloud_Administrator");
    const response = await agent.get(jouleAllRolesEndpoint).query(query);

    const expectedResult = {
      count: 1,
      countGlobalRoles: 1,
      countScopedRoles: 0,
      roles: JouleRolesTestData.testRolesResultAll.filter((role) => role.id === "Data_Warehouse_Cloud_Administrator"),
    };
    const { url } = sacClientStub.getCall(0).args[0];
    const eshQueryParams = new URL(url).searchParams;

    assert(eshQueryParams.has("facets", "SCOPE_ROLE"), "Error query parameter: facets");
    assert(
      eshQueryParams.has(
        "$apply",
        `filter(Search.search(query='SCOPE:esh_ums_profiles ((PROFILE_ID:OR(${expectedRoleID}) OR (NAME:EQ:"${query.name}" AND NAME:EQ:"${query.startsWith}*" AND NAME:EQ:"*${query.endsWith}" AND NAME:"${query.contains}"~0.7)) AND (SCOPE_ROLE:EQ(S):${query.roleType} AND USER_ID:AND(${query.userId})))'))`
      ),
      "Error query parameter: $apply"
    );
    assert.equal(response.status, 200);
    assert.deepEqual(response.body, expectedResult);
  });

  it("check ESH Query - predefined roles: Scoped", async () => {
    // predefined scoped role
    const sacClientStub = sandbox.stub(SacClient, "call").resolves({
      value: [JouleRolesTestData.mockEshProfiles.TEST_SCOPED_ROLE_PREDEFINED],
    });

    const query = {
      name: "DW Scoped Space Administrator",
      startsWith: "DW",
      endsWith: "Administrator",
      contains: "DW Scoped",
      roleType: 1,
      userId: "TEST_USER_1",
      spaceId: "TEST_SCOPE_1",
      showDetails: "true",
    };
    const expectedRoleID = "*" + escapeQuery(":Scoped_Data_Warehouse_Cloud_Space_Administrator");
    const expectedResult = {
      count: 1,
      countGlobalRoles: 0,
      countScopedRoles: 1,
      roles: JouleRolesTestData.testRolesResultAll.filter(
        (role) => role.id === "Scoped_Data_Warehouse_Cloud_Space_Administrator"
      ),
    };

    const response = await agent.get(jouleAllRolesEndpoint).query(query);
    const { url } = sacClientStub.getCall(0).args[0];
    const eshQueryParams = new URL(url).searchParams;

    assert(eshQueryParams.has("facets", "SCOPE_ROLE"), "Error query parameter: facets");
    assert(
      eshQueryParams.has(
        "$apply",
        `filter(Search.search(query='SCOPE:esh_ums_profiles ((PROFILE_ID:OR(${expectedRoleID}) OR (NAME:EQ:"${query.name}" AND NAME:EQ:"${query.startsWith}*" AND NAME:EQ:"*${query.endsWith}" AND NAME:"${query.contains}"~0.7)) AND (SCOPE_ROLE:EQ(S):${query.roleType} AND USER_ID:AND(${query.userId}) AND SCOPE_NAME:AND(${query.spaceId})))'))`
      ),
      "Error query parameter: $apply"
    );
    assert.equal(response.status, 200);
    assert.deepEqual(response.body, expectedResult);
  });

  it("check ESH Query - roleType without name filters", async () => {
    const sacClientStub = sandbox.stub(SacClient, "call").resolves({});
    const query = { roleType: 1 };
    const response = await agent.get(jouleAllRolesEndpoint).query(query);
    const { url } = sacClientStub.getCall(0).args[0];
    const eshQueryParams = new URL(url).searchParams;

    assert(eshQueryParams.has("facets", "SCOPE_ROLE"), "Error query parameter: facets");
    assert(
      eshQueryParams.has(
        "$apply",
        `filter(Search.search(query='SCOPE:esh_ums_profiles (SCOPE_ROLE:EQ(S):${query.roleType})'))`
      ),
      "Error query parameter: $apply"
    );
    assert.equal(response.status, 200);
  });

  it("should return no roles", async () => {
    sandbox.stub(SacClient, "call").resolves({});
    const response = await agent.get(jouleAllRolesEndpoint);
    const expectedResult = {
      count: 0,
      countGlobalRoles: 0,
      countScopedRoles: 0,
      roles: [],
    };
    assert.equal(response.status, 200);
    assert.deepEqual(response.body, expectedResult);
  });

  it("should return error: ESH Search failed", async () => {
    sandbox.stub(SacClient, "call").resolves(JouleRolesTestData.mockEshErrorResponse);
    const response = await agent.get(jouleAllRolesEndpoint);
    assert.equal(response.status, 400);
    assert.equal(response.body.code, "enterpriseSearchFailed");
  });

  it("should return error: other error message", async () => {
    sandbox.stub(SacClient, "call").rejects(new Error());
    const response = await agent.get(jouleAllRolesEndpoint);
    assert.equal(response.status, 500);
    assert.equal(response.body.code, "getRolesForJouleFailed");
  });
});
