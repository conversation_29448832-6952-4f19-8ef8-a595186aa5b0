/** @format */

import { IJouleRole } from "../../../routes/joule/types";

export const mockEshProfiles = {
  TEST_GLOBAL_ROLE: {
    "@com.sap.vocabularies.Search.v1.Ranking": 0,
    "@odata.context": "$metadata#esh_ums_profiles",
    CHANGED_AT: "2025-05-22T00:39:06.3710000Z",
    CHANGED_BY: "",
    CREATED_AT: "2025-05-22T00:39:06.3710000Z",
    DEFAULT_ASSIGN: "Default Role",
    DESCRIPTION: "Joule Test Role: Global",
    LICENSE_TYPE: "SAP Datasphere",
    NAME: "TEST_GLOBAL_ROLE",
    OWNER: "EPM_SERVICE",
    PREDEFINED_ROLE: "Custom Role",
    PROFILE_ID: "PROFILE:t.D:TEST_GLOBAL_ROLE",
    SCOPE_ROLE: "Global Role",
    SELF_SERVICE: 0,
    SCOPE_NAME: [],
    TEAM_NAME: [],
    USER_ID: ["TEST_USER_1", "TEST_USER_2"],
    NUMBER_OF_USERS: 2,
    NUMBER_OF_SCOPES: 0,
    NUMBER_OF_TEAMS: 0,
  },
  TEST_GLOBAL_ROLE_PREDEFINED: {
    "@com.sap.vocabularies.Search.v1.Ranking": 0,
    "@odata.context": "$metadata#esh_ums_profiles",
    CHANGED_AT: null,
    CHANGED_BY: null,
    CREATED_AT: "2025-05-22T00:39:06.3710000Z",
    DEFAULT_ASSIGN: "Not Default Role",
    DESCRIPTION: "Joule Test Role: Global predefined",
    LICENSE_TYPE: "SAP Datasphere",
    NAME: "DW Administrator",
    OWNER: null,
    PREDEFINED_ROLE: "Standard Role",
    PROFILE_ID: "PROFILE:sap.dwc:Data_Warehouse_Cloud_Administrator",
    SCOPE_ROLE: "Global Role",
    SELF_SERVICE: 0,
    SCOPE_NAME: [],
    TEAM_NAME: [],
    USER_ID: ["TEST_USER_1", "TEST_USER_2", "TEST_USER_3"],
    NUMBER_OF_USERS: 3,
    NUMBER_OF_SCOPES: 0,
    NUMBER_OF_TEAMS: 0,
  },
  TEST_SCOPED_ROLE: {
    "@com.sap.vocabularies.Search.v1.Ranking": 0,
    "@odata.context": "$metadata#esh_ums_profiles",
    CHANGED_AT: null,
    CHANGED_BY: null,
    CREATED_AT: null,
    DEFAULT_ASSIGN: "Not Default Role",
    DESCRIPTION: "Joule Test Role: Scoped",
    LICENSE_TYPE: "SAP Datasphere",
    NAME: "TEST_SCOPED_ROLE",
    OWNER: null,
    PREDEFINED_ROLE: "Custom Role",
    PROFILE_ID: "PROFILE:t.D:TEST_SCOPED_ROLE",
    SCOPE_ROLE: "Scoped Role",
    SELF_SERVICE: 0,
    SCOPE_NAME: ["TEST_SCOPE_1", "TEST_SCOPE_2"],
    TEAM_NAME: [],
    USER_ID: ["TEST_USER_1", "TEST_USER_2"],
    NUMBER_OF_USERS: 2,
    NUMBER_OF_SCOPES: 2,
    NUMBER_OF_TEAMS: 0,
  },
  TEST_SCOPED_ROLE_PREDEFINED: {
    "@com.sap.vocabularies.Search.v1.Ranking": 0,
    "@odata.context": "$metadata#esh_ums_profiles",
    CHANGED_AT: null,
    CHANGED_BY: null,
    CREATED_AT: null,
    DEFAULT_ASSIGN: "Not Default Role",
    DESCRIPTION: "Joule Test Role: Scoped predefined",
    LICENSE_TYPE: "SAP Datasphere",
    NAME: "Scoped Data Warehouse Cloud Space Administrator",
    OWNER: null,
    PREDEFINED_ROLE: "Standard Role",
    PROFILE_ID: "PROFILE:sap.dwc:Scoped_Data_Warehouse_Cloud_Space_Administrator",
    SCOPE_ROLE: "Scoped Role",
    SELF_SERVICE: 0,
    SCOPE_NAME: ["TEST_SCOPE_1", "TEST_SCOPE_2", "TEST_SCOPE_3"],
    TEAM_NAME: [],
    USER_ID: ["TEST_USER_1", "TEST_USER_2", "TEST_USER_3"],
    NUMBER_OF_USERS: 3,
    NUMBER_OF_SCOPES: 3,
    NUMBER_OF_TEAMS: 0,
  },
};
export const mockEshProfilesResponse = {
  value: Object.values(mockEshProfiles),
};

export const testRolesResultAll: IJouleRole[] = [
  {
    ranking: 0,
    id: "TEST_GLOBAL_ROLE",
    name: "TEST_GLOBAL_ROLE",
    profileId: "PROFILE:t.D:TEST_GLOBAL_ROLE",
    description: "Joule Test Role: Global",
    isScoped: false,
    isPredefined: false,
    createdOn: "2025-05-22T00:39:06.3710000Z",
    scopeIds: [],
    userIds: ["TEST_USER_1", "TEST_USER_2"],
    countUsers: 2,
    countScopes: 0,
  },
  {
    ranking: 0,
    id: "Data_Warehouse_Cloud_Administrator",
    name: "DW Administrator",
    profileId: "PROFILE:sap.dwc:Data_Warehouse_Cloud_Administrator",
    description: "Joule Test Role: Global predefined",
    isScoped: false,
    isPredefined: true,
    createdOn: "2025-05-22T00:39:06.3710000Z",
    scopeIds: [],
    userIds: ["TEST_USER_1", "TEST_USER_2", "TEST_USER_3"],
    countUsers: 3,
    countScopes: 0,
  },
  {
    ranking: 0,
    id: "TEST_SCOPED_ROLE",
    name: "TEST_SCOPED_ROLE",
    profileId: "PROFILE:t.D:TEST_SCOPED_ROLE",
    description: "Joule Test Role: Scoped",
    isScoped: true,
    isPredefined: false,
    createdOn: null,
    scopeIds: ["TEST_SCOPE_1", "TEST_SCOPE_2"],
    userIds: ["TEST_USER_1", "TEST_USER_2"],
    countUsers: 2,
    countScopes: 2,
  },
  {
    ranking: 0,
    id: "Scoped_Data_Warehouse_Cloud_Space_Administrator",
    name: "DW Scoped Space Administrator",
    profileId: "PROFILE:sap.dwc:Scoped_Data_Warehouse_Cloud_Space_Administrator",
    description: "Joule Test Role: Scoped predefined",
    isScoped: true,
    isPredefined: true,
    createdOn: null,
    scopeIds: ["TEST_SCOPE_1", "TEST_SCOPE_2", "TEST_SCOPE_3"],
    userIds: ["TEST_USER_1", "TEST_USER_2", "TEST_USER_3"],
    countUsers: 3,
    countScopes: 3,
  },
];

export const mockEshErrorResponse = {
  error: {
    code: "9620106",
    message:
      "exception 9620106: Error in $apply: syntax error, unexpected unterminated string literal, expecting STRING, position 4383-4384",
  },
};
