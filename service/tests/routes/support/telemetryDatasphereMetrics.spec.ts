/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [DataIntegration.RemoteTableBackend]
 */

import sinon from "sinon";

import { AuditLogClient } from "@sap/dwc-audit-logger";
import { expect } from "chai";
import { Request, Response } from "express";
import HttpStatusCode from "http-status-codes";
import * as identityModule from "../../../datasphereMetrics/credentials/identity";
import { FeatureFlagProvider } from "../../../featureflags/FeatureFlagProvider";
import { RequestContext } from "../../../repository/security/requestContext";
import * as auditLogUtils from "../../../reuseComponents/auditlogger/audit-log-utils";
import { datasphereMetricsCredentials } from "../../../routes/support/telemetryDatasphereMetrics";

describe("datasphereMetricsCredentials support route", () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let statusStub: sinon.SinonStub;
  let sendStub: sinon.SinonStub;
  let ffStub: sinon.SinonStub;
  let auditStub: sinon.SinonStub;
  let createUserCertificateStub: sinon.SinonStub;

  // Valid fake PEMs for X.509 happy path
  const VALID_FAKE_LEAF_PEM = `
-----BEGIN CERTIFICATE-----
MIIBwjCCAWugAwIBAgIJANnHCj6U1z7fMAoGCCqGSM49BAMCMBUxEzARBgNVBAMMCnRlc3RfbGVhZjAgFw0yNDA2MDQwMDAwMDBaGA8yMTI0MDUxMTAwMDAwMFowFTEXMBUGA1UEAwwOdGVzdF9sZWFmX2NlcnQwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNC
AAR9hOElhRUP5x1Y8KtDJi7kssHHwr5ykp52XQOfBJ4htn8R3ybvT7h7FynS1++UdPjR4Ry1j7KScxAN1mkncK9Uo1MwUTAdBgNVHQ4EFgQUC3jrlqyblyiStqZXzMHdYQ2MVdQwHwYDVR0jBBgwFoAUC3jrlqyblyiStqZXzMHdYQ2MVdQwDwYDVR0TAQH/BAUwAwEB/zAKBggqhkjOPQQDAgNIADBFAiEA0Pq3VUyLu8IulQt1lrlTOrZ1fwQvdcUfZfN+n1Vb3t8CIAG8aAnFgHWBWPCrO8x9j3gU5iIh8gf6evrj4PpK5jqM
-----END CERTIFICATE-----
`.trim();

  const VALID_FAKE_ROOT_PEM = `
-----BEGIN CERTIFICATE-----
MIIBwjCCAWugAwIBAgIJANnHCj6U1z7fMAoGCCqGSM49BAMCMBUxEzARBgNVBAMMCnRlc3RfbGVhZjAgFw0yNDA2MDQwMDAwMDBaGA8yMTI0MDUxMTAwMDAwMFowFTEXMBUGA1UEAwwOdGVzdF9sZWFmX2NlcnQwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNC
AAR9hOElhRUP5x1Y8KtDJi7kssHHwr5ykp52XQOfBJ4htn8R3ybvT7h7FynS1++UdPjR4Ry1j7KScxAN1mkncK9Uo1MwUTAdBgNVHQ4EFgQUC3jrlqyblyiStqZXzMHdYQ2MVdQwHwYDVR0jBBgwFoAUC3jrlqyblyiStqZXzMHdYQ2MVdQwDwYDVR0TAQH/BAUwAwEB/zAKBggqhkjOPQQDAgNIADBFAiEA0Pq3VUyLu8IulQt1lrlTOrZ1fwQvdcUfZfN+n1Vb3t8CIAG8aAnFgHWBWPCrO8x9j3gU5iIh8gf6evrj4PpK5jqM
-----END CERTIFICATE-----
`.trim();

  const basicBody = {
    schemaName: "test_schema",
    authType: "Basic",
    host: "local",
    port: 443,
    user: "user",
    password: "pass",
  };

  const x509Body = {
    schemaName: "test_schema",
    authType: "X509",
    host: "local",
    port: 443,
    user: "user",
  };

  beforeEach(() => {
    statusStub = sinon.stub();
    sendStub = sinon.stub();
    res = {
      status: statusStub.returns({ send: sendStub }),
    };
    req = {
      context: {} as RequestContext,
      body: { ...basicBody },
    };

    ffStub = sinon.stub(FeatureFlagProvider, "isLandscapeFeatureActive").resolves(true);

    auditStub = sinon.stub().resolves();
    sinon.stub(auditLogUtils, "getAuditLogClient").returns({ securityMessage: auditStub } as unknown as AuditLogClient);

    createUserCertificateStub = sinon.stub(identityModule, "createUserCertificate").resolves({
      certificates: [VALID_FAKE_LEAF_PEM, VALID_FAKE_ROOT_PEM],
      key: "PRIVATE_KEY",
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  it("should return 403, feature flag is OFF", async () => {
    ffStub.resolves(false);

    await datasphereMetricsCredentials(req as Request, res as Response);

    expect(statusStub.calledOnceWithExactly(HttpStatusCode.FORBIDDEN)).to.be.true;
    expect(sendStub.calledOnce).to.be.true;
    expect(sendStub.calledWithMatch({ message: "Feature not available." })).to.be.true;
  });

  it("should return 400 if required fields are missing (schemaName, host, port, authType, user)", async () => {
    req.body = { authType: "X509" };

    await datasphereMetricsCredentials(req as Request, res as Response);

    expect(statusStub.calledOnceWithExactly(HttpStatusCode.BAD_REQUEST)).to.be.true;
    expect(
      sendStub.calledWithMatch({
        message: "Missing required fields: schemaName, authType, host, port, user.",
      })
    ).to.be.true;
  });

  it("should return 400 if required fields for Basic auth are missing", async () => {
    req.body = {
      schemaName: "foo",
      authType: "Basic",
      host: "bar",
      port: 443,
      user: "user",
    };

    await datasphereMetricsCredentials(req as Request, res as Response);

    expect(statusStub.calledOnceWithExactly(HttpStatusCode.BAD_REQUEST)).to.be.true;
    expect(
      sendStub.calledWithMatch({
        message: "Missing required field for Basic auth: password.",
      })
    ).to.be.true;
  });

  it("should return 400 if unsupported authType is provided", async () => {
    req.body = {
      schemaName: "foo",
      authType: "Bearer",
      host: "bar",
      port: 443,
      user: "u",
      password: "p",
    };

    await datasphereMetricsCredentials(req as Request, res as Response);

    expect(statusStub.calledOnceWithExactly(HttpStatusCode.BAD_REQUEST)).to.be.true;
    expect(
      sendStub.calledWithMatch({
        message: "Unsupported authType: Bearer",
      })
    ).to.be.true;
  });

  it("should return 200 for valid Basic", async () => {
    req.body = { ...basicBody };
    await datasphereMetricsCredentials(req as Request, res as Response);

    expect(auditStub.calledOnce).to.be.true;
    expect(statusStub.calledOnceWithExactly(HttpStatusCode.OK)).to.be.true;
    expect(
      sendStub.calledWithMatch({
        message: "Datasphere Metrics credentials successfully stored in Credential Store.",
      })
    ).to.be.true;
  });

  it("should return 200 for valid X.509", async () => {
    req.body = { ...x509Body };

    await datasphereMetricsCredentials(req as Request, res as Response);

    expect(createUserCertificateStub.calledOnce).to.be.true;
  });

  it("should return 500 on unexpected error", async () => {
    createUserCertificateStub.rejects(new Error("oops"));
    req.body = { ...x509Body };
    await datasphereMetricsCredentials(req as Request, res as Response);

    expect(statusStub.calledOnceWithExactly(HttpStatusCode.INTERNAL_SERVER_ERROR)).to.be.true;
    expect(sendStub.calledOnce).to.be.true;
    expect(
      sendStub.calledWithMatch({
        message: "An unexpected error occurred.",
      })
    ).to.be.true;
  });

  it("should return 500 if cert generation fails for X.509", async () => {
    req.body = { ...x509Body };
    createUserCertificateStub.rejects(new Error("bad cert"));

    await datasphereMetricsCredentials(req as Request, res as Response);

    expect(createUserCertificateStub.calledOnce).to.be.true;
    expect(statusStub.calledOnceWithExactly(HttpStatusCode.INTERNAL_SERVER_ERROR)).to.be.true;
    expect(
      sendStub.calledWithMatch({
        message: "An unexpected error occurred.",
      })
    ).to.be.true;
  });

  it("should return 500 if X509 cert or key is missing after generation", async () => {
    createUserCertificateStub.resolves({
      certificates: [],
      key: "",
    });

    req.body = { ...x509Body };

    await datasphereMetricsCredentials(req as Request, res as Response);

    expect(statusStub.calledOnceWithExactly(HttpStatusCode.INTERNAL_SERVER_ERROR)).to.be.true;
    expect(sendStub.calledOnce).to.be.true;
    expect(sendStub.calledWithMatch({ message: "Failed to generate X509 certificate." })).to.be.true;
  });
});

import { AuthTypes } from "../../../datasphereMetrics/credentials/types";
import { getDatasphereMetricsCredentials } from "../../../routes/support/telemetryDatasphereMetrics";
import { SecureStore } from "../../../securestore";

describe("getDatasphereMetricsCredentials", () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let statusStub: sinon.SinonStub;
  let jsonStub: sinon.SinonStub;
  let sendStub: sinon.SinonStub;

  const credentials = {
    authType: AuthTypes.X509,
    certSubject: "CN=test-subject,OU=some-ou,O=org,C=DE",
    certIssuer: "CN=test-issuer,OU=issuer-ou,O=issuer-org,C=DE",
  };

  beforeEach(() => {
    req = {};

    jsonStub = sinon.stub();
    sendStub = sinon.stub();
    statusStub = sinon.stub().returns({ json: jsonStub, send: sendStub });

    res = {
      status: statusStub,
    };

    sinon.stub(FeatureFlagProvider, "isLandscapeFeatureActive").resolves(true);
    sinon.stub(SecureStore, "fromStoreName").returns({
      retrieve: sinon.stub().resolves(credentials),
    } as any);
  });

  afterEach(() => {
    sinon.restore();
  });

  it("should return 403 when feature flag is off", async () => {
    (FeatureFlagProvider.isLandscapeFeatureActive as sinon.SinonStub).resolves(false);

    await getDatasphereMetricsCredentials(req as Request, res as Response);

    expect(statusStub.calledOnceWithExactly(HttpStatusCode.FORBIDDEN)).to.be.true;
  });

  it("should return 404 if credentials not found", async () => {
    (SecureStore.fromStoreName as sinon.SinonStub).returns({
      retrieve: sinon.stub().resolves(undefined),
    });

    await getDatasphereMetricsCredentials(req as Request, res as Response);

    expect(statusStub.calledOnceWithExactly(HttpStatusCode.NOT_FOUND)).to.be.true;
    expect(sendStub.calledOnceWithExactly("No Datasphere Metrics credentials found.")).to.be.true;
  });

  it("should return 200 with correct response", async () => {
    await getDatasphereMetricsCredentials(req as Request, res as Response);

    expect(statusStub.calledOnceWithExactly(HttpStatusCode.OK)).to.be.true;
    expect(
      jsonStub.calledOnceWithExactly({
        x509Config: {
          x509Enabled: true,
          subjectName: credentials.certSubject,
          issuerName: credentials.certIssuer,
        },
        consumption: {
          localSchemaAccess: true,
          scriptServerAccess: true,
          spaceSchemaAccess: true,
          hdiGrantorForCupsAccess: true,
          consumptionWithGrant: true,
        },
        ingestion: {
          auditing: {
            dppRead: {
              isAuditPolicyActive: true,
              retentionPeriod: 30,
            },
            dppChange: {
              isAuditPolicyActive: true,
              retentionPeriod: 30,
            },
          },
        },
      })
    ).to.be.true;
  });

  it("should return 500 on unexpected error", async () => {
    (SecureStore.fromStoreName as sinon.SinonStub).throws(new Error("boom"));

    await getDatasphereMetricsCredentials(req as Request, res as Response);

    expect(statusStub.calledOnceWithExactly(HttpStatusCode.INTERNAL_SERVER_ERROR)).to.be.true;
    expect(sendStub.calledOnceWithExactly("Unexpected error occurred.")).to.be.true;
  });
});
