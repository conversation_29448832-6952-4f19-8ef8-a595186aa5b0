/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import chai, { assert, expect } from "chai";
import _ from "lodash";
import sinon, { SinonStub } from "sinon";
import sinonChai from "sinon-chai";
import { DwcObjectTypes } from "../../../../../../shared/metadataImport/metadataImportTypes";
import {
  ANALYTICMODEL_VERSION_IN_TESTS,
  AnalyticModelAggregationTypes,
  AnalyticModelCalculatedMeasureOperandType,
  AnalyticModelConstantSelectionType,
  AnalyticModelConversionTypeType,
  AnalyticModelErrorHandling,
  AnalyticModelExceptionAggregationType,
  AnalyticModelFilterOperandType,
  AnalyticModelMeasureType,
  AnalyticModelReferenceDateType,
  AnalyticModelRestrictedMeasureOperandType,
  AnalyticModelSourceCurrencyType,
  AnalyticModelTargetCurrencyType,
  IAnalyticModel,
  IAnalyticModelCalculatedMeasure,
  IAnalyticModelCalculatedMeasureOperandConstant,
  IAnalyticModelCurrencyConversionMeasure,
  IAnalyticModelFilter,
  IAnalyticModelRestrictedMeasure,
  IAnalyticModelRestrictedMeasureOperandConstant,
} from "../../../../../../shared/queryBuilder/AnalyticModel";
import {
  CsnXprToAMHelper,
  IANALYTIC_MODEL_LOCALIZED_MESSAGE,
  IConvertFormulaResult,
  XPR_PARTS,
} from "../../../../../../shared/queryBuilder/CsnXprToAMHelper";
import { ICreateExpressionResult } from "../../../../../../shared/queryBuilder/QueryModelValidator";
import { FeatureFlagProvider } from "../../../../../featureflags/FeatureFlagProvider";
import { HybridParserFactory } from "../../../../../hybridParser/HybridParserFactory";
import { HybridParserParameter, ICsnObjectToDeploy, IParseResult } from "../../../../../hybridParser/HybridParserType";
import { Commons } from "../../../../../hybridParser/businessLayer/Commons";
import { RequestContext } from "../../../../../repository/security/requestContext";
import { QueryModelBuilder } from "../../../../../routes/querybuilder/metaDataImport/QueryModelBuilder";
import { AnalyticModelCsnConverterV2 } from "../../../../../routes/querybuilder/services/AnalyticModelCsnConverterV2";
import AnalyticModelCsnConverterHelper from "../../../../../routes/querybuilder/utils/AnalyticModelCsnConverterHelper";
import AnalyticModelHelper from "../../../../../routes/querybuilder/utils/AnalyticModelHelper";
import * as TestHelper from "../../../c4s/internal_services/helper";
import { AMI_BW4HCPR_EXAMPLE_FML_I18N } from "../../../c4s/internal_services/unit_tests/testData/bw4hybridcsn/AMI_BW4HCPR_EXAMPLE_FML_i18n";
import { BW4HCPR_EXAMPLE_I18N } from "../../../c4s/internal_services/unit_tests/testData/bw4hybridcsn/BW4HCPR_EXAMPLE";
import { anEmptyCube } from "../../mocks/emptyCubeModel";
import { TestUtil_stubAnalyticModelHelper } from "../helper/TestHelperFunctions";
import {
  AMI_SAMPLE_CALCULATED_MEASURES,
  AMI_SAMPLE_CALCULATED_MEASURES_QUERY_MODEL,
  AMI_SAMPLE_CALCULATED_MEASURES_QUERY_MODEL_WO_BUSINESSNAMES,
  AMI_SAMPLE_CALCULATED_MEASURES_WO_BUSINESSNAMES,
} from "./mockData/AMI_SAMPLE_CALCULATED_MEASURES";
import { ASPARUS, ASPARUS_DATALAYER_OBJECTS_CSN, ASPARUS_QUERY_MODEL } from "./mockData/ASPARUS";
import {
  BW4HCPR_EXAMPLE_CMPLX_RKF_IMPORT_CSN,
  BW4HCPR_EXAMPLE_CMPLX_RKF_WITH_CONTEXT_QUERY_MODEL,
  BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN,
  MOONLIGHT,
} from "./mockData/BW4HCPR_EXAMPLE_CMPLX_RKF";
import { BW4HCPR_Q001_QUERY } from "./mockData/BW4HCPR_Q001-QUERY";
import {
  BWHYB_BASIC_OPERATORS,
  BWHYB_BASIC_OPERATORS_DATALAYER_OBJECTS_CSN,
  BWHYB_BASIC_OPERATORS_QUERY_MODEL,
} from "./mockData/BWHYB_BASIC_OPERATORS";
import { MEASURE_COUNT_DISTINCT } from "./mockData/COUNT_DISTINCT_MEASURE";
import { CUSTISSUE_CS20230005819268 } from "./mockData/CustIssue_ CS20230005819268";
import { CS20230005819234_ORIG_CSN } from "./mockData/CustIssue_CS20230005819234";
import { DWC_NMS_CP1_Q2 } from "./mockData/DWC_NMS_CP1_Q2";
import { JS_AGGR_QUERY, JS_AGGR_QUERY_DATALAYER_CSN, JS_AGGR_QUERY_QUERY_MODEL } from "./mockData/JS_AGGR_QUERY";
import { JS_CONST_SEL } from "./mockData/JS_CONST_SEL";
import { JS_FIL_VAR_1 } from "./mockData/JS_FIL_VAR_1";
import { JS_RES_KYF_VAR_1 } from "./mockData/JS_RES_KYF_VAR_1";
import { JS_RES_KYF_VAR_2 } from "./mockData/JS_RES_KYF_VAR_2";
import { JS_RES_KYF_VAR_3 } from "./mockData/JS_RES_KYF_VAR_3";
import {
  QUERY_WITH_CURRENCY_CONVERSION_CURRENTDATE,
  QUERY_WITH_CURRENCY_CONVERSION_CURRENTDATE_CUBE_MODEL,
  QUERY_WITH_CURRENCY_CONVERSION_FIXDATE,
  QUERY_WITH_CURRENCY_CONVERSION_FIXDATE_CUBE_MODEL,
} from "./mockData/QUERY_WITH_CURRENCY_CONVERSION";
import { AMI_SAMPLERKF_IMPORTCSN, AMI_SAMPLERKF_WITH_CONTEXT_QUERY_MODEL } from "./mockData/SampleRkf";
import {
  YET_ANOTHER_QUERY_WITH_CC,
  YET_ANOTHER_QUERY_WITH_CC_CREATED_CSN,
  YET_ANOTHER_QUERY_WITH_CC_CUBE_MODEL2,
  YET_ANOTHER_QUERY_WITH_CC_DATALAYER_CSN,
} from "./mockData/YET_ANOTHER_QUERY_WITH_CC";

chai.use(sinonChai);

const TEST_SPACE = "TEST_SPACE";
const TEST_CONNECTION_W4D = "W4D";
const TEST_CONNECTION_D2T = "D2T";

// stringified CSN converts key of type number to string
function makeJSONComparable(json: any): any {
  return JSON.parse(JSON.stringify(json));
}

function assert_deepStrictEqualAnalyticModel(op1: IAnalyticModel, op2: IAnalyticModel) {
  assert.deepStrictEqual(cloneAndRemoveVersionFromModel(op1), cloneAndRemoveVersionFromModel(op2));
}

function cloneAndRemoveVersionFromModel(op1: IAnalyticModel): IAnalyticModel {
  const clone = _.cloneDeep(op1);
  delete (clone as any).version;
  return clone;
}

function cloneAndRemoveVersionFromModelAsBackpack(op1: any): any {
  const clone = _.cloneDeep(op1);
  delete clone.businessLayerDefinitions[Object.keys(clone.businessLayerDefinitions)[0]].version;
  return clone;
}

function assert_deepStrictEqualCsnWithAnalyticModel(op1: any, op2: any) {
  const cloneOp1 = cloneAndRemoveVersionFromModelAsBackpack(op1);
  const cloneOp2 = cloneAndRemoveVersionFromModelAsBackpack(op2);

  // Previous test skips were due to the version property not being removed. Make sure the backpack version gone from op1 and op2 before comparing.
  expect(cloneOp1.businessLayerDefinitions[Object.keys(cloneOp1.businessLayerDefinitions)[0]].version).to.not.exist;
  expect(cloneOp2.businessLayerDefinitions[Object.keys(cloneOp2.businessLayerDefinitions)[0]].version).to.not.exist;

  assert.deepStrictEqual(cloneOp1, cloneOp2);
}

describe("BW/4 hybrid import data layer query tests", () => {
  let sandbox: sinon.SinonSandbox;
  let context: RequestContext;
  let isFeatureActiveStub: SinonStub;

  let emptyConverter: AnalyticModelCsnConverterV2;

  before(() => {
    context = TestHelper.getTestContext();

    emptyConverter = new AnalyticModelCsnConverterV2(context, anEmptyCube, TEST_SPACE);
  });

  after(async () => {
    sinon.restore();
    await context.finish();
  });

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    TestUtil_stubAnalyticModelHelper(sandbox);
    isFeatureActiveStub = sandbox.stub(FeatureFlagProvider, "isFeatureActive");
    isFeatureActiveStub.withArgs(sinon.match.any, "DWCO_MODELING_CB_CONSTANT_SELECTION").resolves(true);
    isFeatureActiveStub.resolves(true);
  });

  afterEach(() => {
    sandbox.restore();
  });

  function createParser(hybridParserParameter: HybridParserParameter) {
    const hybridParser = HybridParserFactory.createHybridParser(hybridParserParameter);
    sandbox.stub(hybridParser, "checkOldConnectionNameWithSpacePrefix").callsFake(async () => false);
    return hybridParser;
  }

  it("should convert a DL CSN with complex restricted measure into a cube model", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: BW4HCPR_EXAMPLE_CMPLX_RKF_IMPORT_CSN,
      remoteConnection: TEST_CONNECTION_W4D,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: true,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const definitions = hybridParser.getFullCsn().definitions;
    const queryDefinition = definitions["Remote.W4D.DLBW4HCPR_EXAMPLE_CMPLX_RKF__V_REP_JOIN"];
    const rkfElement = queryDefinition.elements!.VC_MAX_CMPLX_FILTER;

    const rkfCol = queryDefinition.query?.SELECT?.columns[8];
    expect(rkfCol).not.be.undefined;

    const factSourceToElementMapping: Map<string, string> = new Map();
    QueryModelBuilder.createElementMapping(queryDefinition, factSourceToElementMapping);

    const dummyCubeModel: IAnalyticModel = {
      identifier: {
        key: "aName",
      },
      text: "a cube model",
      sourceModel: {
        factSources: {},
        dimensionSources: {},
      },
      exposedAssociations: {},
      attributes: {},
      measures: {},
      version: ANALYTICMODEL_VERSION_IN_TESTS,
      supportedCapabilities: {},
    };

    const qRkf = QueryModelBuilder.convertCsnXprToRestrictedMeasure(
      dummyCubeModel,
      "VC_MAX_CMPLX_FILTER",
      rkfElement,
      rkfCol,
      factSourceToElementMapping
    );

    assert.deepStrictEqual(
      makeJSONComparable(qRkf),
      makeJSONComparable({
        measureType: "AnalyticModelMeasureType.RestrictedMeasure",
        key: "m:0:0VC_MAX",
        text: "Maximal Purchase Quantity(DE)",
        formula:
          "[0] BETWEEN [1] AND [2] AND [3] = [4] AND (([5] = [6] OR [7] LIKE [8]) AND  NOT ([9] = [10])) AND (([11] = [12] AND [13] = [14] AND [15] = [16]) OR ([17] = [18] AND [19] = [20] AND [21] = [22]) OR ([23] = [24] AND [25] = [26] AND [27] = [28]) OR ([29] = [30] AND [31] = [32] AND [33] = [34]) OR ([35] = [36] AND [37] = [38] AND [39] = [40]) OR ([41] = [42] AND [43] = [44] AND [45] = [46]) OR ([47] = [48] AND [49] = [50] AND [51] = [52]) OR ([53] = [54] AND [55] = [56] AND [57] = [58]) OR ([59] = [60] AND [61] = [62] AND [63] = [64]) OR ([65] = [66] AND [67] = [68] AND [69] = [70]) OR ([71] = [72] AND [73] = [74] AND [75] = [76]) OR ([77] = [78] AND [79] = [80] AND [81] = [82]) OR ([83] = [84] AND [85] = [86] AND [87] = [88]) OR ([89] = [90] AND [91] = [92] AND [93] = [94]) OR ([95] = [96] AND [97] = [98] AND [99] = [100]) OR ([101] = [102] AND [103] = [104] AND [105] = [106]) OR ([107] = [108] AND [109] = [110] AND [111] = [112]) OR ([113] = [114] AND [115] = [116] AND [117] = [118]) OR ([119] = [120] AND [121] = [122] AND [123] = [124])) AND ([125] <= [126] OR [127] >= [128]) AND  NOT ([129] = [130])",
        elements: {
          "0": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0FISCPER",
          },
          "1": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "1999005",
          },
          "2": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "1999009",
          },
          "3": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0FISCVARNT",
          },
          "4": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "K4",
          },
          "5": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "6": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "7": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "8": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "D%",
          },
          "9": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "10": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "AUS",
          },
          "11": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "12": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "GB",
          },
          "13": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "14": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "HA",
          },
          "15": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "16": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "17": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "18": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "GB",
          },
          "19": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "20": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "LON",
          },
          "21": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "22": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "23": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "24": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "25": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "26": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "BAW",
          },
          "27": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "28": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "001",
          },
          "29": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "30": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "31": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "32": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "BAW",
          },
          "33": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "34": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "002",
          },
          "35": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "36": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "37": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "38": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "BAW",
          },
          "39": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "40": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "41": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "42": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "43": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "44": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "BAY",
          },
          "45": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "46": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "47": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "48": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "49": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "50": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "BER",
          },
          "51": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "52": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "53": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "54": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "55": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "56": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "BRA",
          },
          "57": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "58": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "59": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "60": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "61": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "62": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "HAM",
          },
          "63": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "64": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "65": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "66": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "67": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "68": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "HES",
          },
          "69": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "70": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "71": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "72": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "73": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "74": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "MVP",
          },
          "75": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "76": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "77": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "78": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "79": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "80": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "NIE",
          },
          "81": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "82": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "83": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "84": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "85": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "86": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "NRW",
          },
          "87": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "88": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "89": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "90": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "91": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "92": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "RLP",
          },
          "93": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "94": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "95": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "96": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "97": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "98": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "SAC",
          },
          "99": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "100": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "101": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "102": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "103": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "104": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "SAR",
          },
          "105": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "106": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "107": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "108": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "109": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "110": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "SCA",
          },
          "111": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "112": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "113": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "114": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "115": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "116": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "SHO",
          },
          "117": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "118": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "119": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "120": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
          "121": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "122": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "THU",
          },
          "123": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_FIELD",
          },
          "124": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "000",
          },
          "125": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "126": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "Z",
          },
          "127": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "128": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "A",
          },
          "129": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_TYPE",
          },
          "130": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "1",
          },
        },
        isAuxiliary: false,
        aggregation: "AnalyticModelAggregationTypes.MAX",
        formulaRaw:
          "\"0FISCPER\" BETWEEN '1999005' AND '1999009' AND \"0FISCVARNT\" = 'K4' AND ((\"0VC_COUN\" = 'DE' OR \"0VC_COUN\" LIKE 'D%') AND  NOT (\"0VC_COUN\" = 'AUS')) AND ((\"0VC_COUN\" = 'GB' AND \"0VC_REG\" = 'HA' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'GB' AND \"0VC_REG\" = 'LON' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'BAW' AND \"0VC_FIELD\" = '001') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'BAW' AND \"0VC_FIELD\" = '002') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'BAW' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'BAY' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'BER' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'BRA' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'HAM' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'HES' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'MVP' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'NIE' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'NRW' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'RLP' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'SAC' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'SAR' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'SCA' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'SHO' AND \"0VC_FIELD\" = '000') OR (\"0VC_COUN\" = 'DE' AND \"0VC_REG\" = 'THU' AND \"0VC_FIELD\" = '000')) AND (\"0VC_REG\" <= 'Z' OR \"0VC_REG\" >= 'A') AND  NOT (\"0VC_TYPE\" = '1')",
      })
    );
    const cubeModel = new QueryModelBuilder("Remote.W4D.DLBW4HCPR_EXAMPLE_CMPLX_RKF__V_REP_JOIN", queryDefinition);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    assert_deepStrictEqualAnalyticModel(result.cubeModel, BW4HCPR_EXAMPLE_CMPLX_RKF_WITH_CONTEXT_QUERY_MODEL);

    sandbox
      .stub(AnalyticModelHelper, "loadRepoObjectDefinitions")
      .resolves(structuredClone(BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN));

    sandbox.stub(AnalyticModelHelper, "getEntityFullCsn").callsFake(() => BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN);

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN.definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);
  }).timeout(10000);

  it("should convert a DL CSN into a cube model", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: MOONLIGHT,
      remoteConnection: TEST_CONNECTION_W4D,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: true,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const definitions = hybridParser.getFullCsn().definitions;
    const queryDefinition = definitions["Remote.W4D.DLMOONLIGHT"];

    const factSourceToElementMapping: Map<string, string> = new Map();
    QueryModelBuilder.createElementMapping(queryDefinition, factSourceToElementMapping);

    const cubeModel = new QueryModelBuilder("Remote.W4D.DLMOONLIGHT", queryDefinition);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    sandbox
      .stub(AnalyticModelHelper, "loadRepoObjectDefinitions")
      .callsFake(async (ctx, space, objectNames) => ({ definitions: structuredClone(definitions), idMapping: {} }));

    sandbox.stub(AnalyticModelHelper, "getEntityFullCsn").callsFake(() => BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN);

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);
  });

  it("should convert a DL CSN with currency conversion measure current date into a cube model", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: QUERY_WITH_CURRENCY_CONVERSION_CURRENTDATE,
      remoteConnection: TEST_CONNECTION_W4D,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: true,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const definitions = hybridParser.getFullCsn().definitions;
    const queryDefinition = definitions["Remote.W4D.DLJS_CTTNM"];

    const ccElement = queryDefinition.elements!.VC_AMT_USD;
    expect(ccElement).not.be.undefined;

    const ccCol = queryDefinition.query?.SELECT?.columns[2];
    expect(ccCol).not.be.undefined;

    const factSourceToElementMapping: Map<string, string> = new Map();
    QueryModelBuilder.createElementMapping(queryDefinition, factSourceToElementMapping);

    const cubeModel = new QueryModelBuilder("Remote.W4D.DLJS_CTTNM", queryDefinition);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    assert_deepStrictEqualAnalyticModel(result.cubeModel, QUERY_WITH_CURRENCY_CONVERSION_CURRENTDATE_CUBE_MODEL);

    sandbox
      .stub(AnalyticModelHelper, "loadRepoObjectDefinitions")
      .resolves(structuredClone(BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN));

    sandbox.stub(AnalyticModelHelper, "getEntityFullCsn").callsFake(() => BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN);

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN.definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);

    // assert.deepStrictEqual(
    //   makeJSONComparable(cubeCsnResult.csn),
    //   makeJSONComparable(QUERY_WITH_CURRENCY_CONVERSION_CURRENTDATE_CUBE_CSN));
  });

  it.skip("should convert a yet another DL CSN with currency conversion measure with current date into a cube model", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: YET_ANOTHER_QUERY_WITH_CC,
      remoteConnection: TEST_CONNECTION_W4D,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: true,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const definitions = hybridParser.getFullCsn().definitions;
    const queryDefinition = definitions["Remote.W4D.DLAMI_TEST_2CURRS"];

    const ccElement1 = queryDefinition.elements!.AMOUNT_IN_USD;
    expect(ccElement1).not.be.undefined;

    const ccCol1 = queryDefinition.query?.SELECT?.columns[8];
    expect(ccCol1).not.be.undefined;
    expect((ccCol1 as any).as).be.equal("AMOUNT_IN_USD");

    const factSourceToElementMapping: Map<string, string> = new Map();
    QueryModelBuilder.createElementMapping(queryDefinition, factSourceToElementMapping);

    const cubeModel = new QueryModelBuilder("Remote.W4D.DLJS_CTTNM", queryDefinition);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    assert_deepStrictEqualAnalyticModel(result.cubeModel, YET_ANOTHER_QUERY_WITH_CC_CUBE_MODEL2);

    sandbox
      .stub(AnalyticModelHelper, "loadRepoObjectDefinitions")
      .resolves(structuredClone(YET_ANOTHER_QUERY_WITH_CC_DATALAYER_CSN));

    sandbox.stub(AnalyticModelHelper, "getEntityFullCsn").callsFake(() => YET_ANOTHER_QUERY_WITH_CC_DATALAYER_CSN);

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      YET_ANOTHER_QUERY_WITH_CC_DATALAYER_CSN.definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);

    assert_deepStrictEqualCsnWithAnalyticModel(cubeCsnResult.csn, YET_ANOTHER_QUERY_WITH_CC_CREATED_CSN);
  });

  it("should convert a DL CSN with currency conversion measure fix date into a cube model", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: QUERY_WITH_CURRENCY_CONVERSION_FIXDATE,
      remoteConnection: TEST_CONNECTION_W4D,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: true,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const definitions = hybridParser.getFullCsn().definitions;
    const queryDefinition = definitions["Remote.W4D.DLAMI_JS_CTTNM"];

    const ccElement = queryDefinition.elements!.VC_AMT_USD;
    expect(ccElement).not.be.undefined;

    const ccCol = queryDefinition.query?.SELECT?.columns[2];
    expect(ccCol).not.be.undefined;

    const factSourceToElementMapping: Map<string, string> = new Map();
    QueryModelBuilder.createElementMapping(queryDefinition, factSourceToElementMapping);

    const cubeModel = new QueryModelBuilder("Remote.W4D.DLAMI_JS_CTTNM", queryDefinition);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    assert_deepStrictEqualAnalyticModel(result.cubeModel, QUERY_WITH_CURRENCY_CONVERSION_FIXDATE_CUBE_MODEL);

    sandbox
      .stub(AnalyticModelHelper, "loadRepoObjectDefinitions")
      .resolves(structuredClone(BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN));

    sandbox.stub(AnalyticModelHelper, "getEntityFullCsn").callsFake(() => BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN);

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN.definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);
    // assert.deepStrictEqual(
    //   makeJSONComparable(cubeCsnResult.csn),
    //   makeJSONComparable(QUERY_WITH_CURRENCY_CONVERSION_FIXDATE_CUBE_CSN));
  });

  it("should convert a DL CSN with restricted measure into a cube model", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: AMI_SAMPLERKF_IMPORTCSN,
      remoteConnection: TEST_CONNECTION_W4D,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: true,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const definitions = hybridParser.getFullCsn().definitions;
    const queryDefinition = definitions["Remote.W4D.DLAMI_SAMPLERKF"];
    const rkfElement = queryDefinition.elements!.AMOUNT_BW;

    const rkfCol = queryDefinition.query?.SELECT?.columns[3];
    expect(rkfCol).not.be.undefined;

    const factSourceToElementMapping: Map<string, string> = new Map();
    QueryModelBuilder.createElementMapping(queryDefinition, factSourceToElementMapping);

    const dummyCubeModel: IAnalyticModel = {
      identifier: {
        key: "aName",
      },
      text: "a cube model",
      sourceModel: {
        factSources: {},
        dimensionSources: {},
      },
      exposedAssociations: {},
      attributes: {},
      measures: {},
      version: ANALYTICMODEL_VERSION_IN_TESTS,
      supportedCapabilities: {},
    };

    const qRkf = QueryModelBuilder.convertCsnXprToRestrictedMeasure(
      dummyCubeModel,
      "AMOUNT_BW",
      rkfElement,
      rkfCol,
      factSourceToElementMapping
    );

    assert.deepStrictEqual(
      makeJSONComparable(qRkf),
      makeJSONComparable({
        measureType: "AnalyticModelMeasureType.RestrictedMeasure",
        key: "m:0:0VC_AMT",
        text: "Amount  BW",
        formula: "[0] = [1]",
        elements: {
          "0": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_REG",
          },
          "1": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "BAW",
          },
        },
        isAuxiliary: false,
        aggregation: "AnalyticModelAggregationTypes.SUM",
        formulaRaw: "\"0VC_REG\" = 'BAW'",
      })
    );
    const cubeModel = new QueryModelBuilder("Remote.W4D.AMI_SAMPLERKF", queryDefinition);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    assert_deepStrictEqualAnalyticModel(result.cubeModel, AMI_SAMPLERKF_WITH_CONTEXT_QUERY_MODEL);

    sandbox
      .stub(AnalyticModelHelper, "loadRepoObjectDefinitions")
      .resolves(structuredClone(BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN));

    sandbox.stub(AnalyticModelHelper, "getEntityFullCsn").callsFake(() => BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN);

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN.definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);
  }).timeout(5000);

  it("should convert a DL CSN with caclulated & restricted measure into a cube model", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: AMI_SAMPLE_CALCULATED_MEASURES,
      remoteConnection: TEST_CONNECTION_W4D,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: true,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const definitions = hybridParser.getFullCsn().definitions;
    const queryDefinition = definitions["Remote.W4D.DLAMI_SAMPLE_CALCULATED_MEASURES"];

    const ckfElement = queryDefinition.elements!.AMOUNT_DIV_100;
    const rkfElement = queryDefinition.elements!.AMOUNT_DIV_100_DE;

    expect(ckfElement).not.be.undefined;
    expect(rkfElement).not.be.undefined;

    const ckfCol = queryDefinition.query?.SELECT?.columns[3];
    const rkfCol = queryDefinition.query?.SELECT?.columns[2];

    expect(ckfCol).not.be.undefined;
    expect(rkfCol).not.be.undefined;

    const factSourceToElementMapping: Map<string, string> = new Map();
    QueryModelBuilder.createElementMapping(queryDefinition, factSourceToElementMapping);

    const dummyCubeModel: IAnalyticModel = {
      identifier: {
        key: "aName",
      },
      text: "a cube model",
      sourceModel: {
        factSources: {},
        dimensionSources: {},
      },
      exposedAssociations: {},
      attributes: {},
      measures: {},
      version: ANALYTICMODEL_VERSION_IN_TESTS,
      supportedCapabilities: {},
    };

    const qRkf = QueryModelBuilder.convertCsnXprToRestrictedMeasure(
      dummyCubeModel,
      "AMOUNT_DIV_100_DE",
      rkfElement,
      rkfCol,
      factSourceToElementMapping
    );
    const qCkf = QueryModelBuilder.convertCsnXprToCalculatedMeasure(
      dummyCubeModel,
      "AMOUNT_DIV_100",
      ckfElement,
      ckfCol,
      factSourceToElementMapping
    );

    assert.deepStrictEqual(
      makeJSONComparable(qRkf),
      makeJSONComparable({
        measureType: "AnalyticModelMeasureType.RestrictedMeasure",
        key: "AMOUNT_DIV_100",
        text: "Selection Amount div 100 DE",
        formula: "[0] = [1]",
        elements: {
          "0": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "1": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
        },
        isAuxiliary: false,
        aggregation: "AnalyticModelAggregationTypes.SUM",
        formulaRaw: "\"0VC_COUN\" = 'DE'",
      })
    );

    assert.deepStrictEqual(
      makeJSONComparable(qCkf),
      makeJSONComparable({
        measureType: "AnalyticModelMeasureType.CalculatedMeasure",
        formula: "([0]/[1])",
        elements: {
          "0": {
            operandType: "AnalyticModelCalculatedMeasureOperandType.Element",
            key: "ET0917EPSAT5GEKMUVLF7IIUL",
          },
          "1": {
            operandType: "AnalyticModelCalculatedMeasureOperandType.ConstantValue",
            value: 100,
          },
        },
        text: "Amount div 100",
        isAuxiliary: false,
        formulaRaw: "(ET0917EPSAT5GEKMUVLF7IIUL/100)",
      })
    );

    const cubeModel = new QueryModelBuilder("Remote.W4D.DLAMI_SAMPLE_CALCULATED_MEASURES", queryDefinition);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    assert_deepStrictEqualAnalyticModel(result.cubeModel, AMI_SAMPLE_CALCULATED_MEASURES_QUERY_MODEL);

    sandbox
      .stub(AnalyticModelHelper, "loadRepoObjectDefinitions")
      .resolves(structuredClone(BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN));

    sandbox.stub(AnalyticModelHelper, "getEntityFullCsn").callsFake(() => BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN);

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN.definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);
  });

  it("should convert a DL CSN with caclulated & restricted measure w/o business names into a cube model", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: AMI_SAMPLE_CALCULATED_MEASURES_WO_BUSINESSNAMES,
      remoteConnection: TEST_CONNECTION_W4D,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: true,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const definitions = hybridParser.getFullCsn().definitions;
    const queryDefinition = definitions["Remote.W4D.DLAMI_SAMPLE_CALCULATED_MEASURES"];

    const ckfElement = queryDefinition.elements!.AMOUNT_DIV_100;
    const rkfElement = queryDefinition.elements!.AMOUNT_DIV_100_DE;

    expect(ckfElement).not.be.undefined;
    expect(rkfElement).not.be.undefined;

    const ckfCol = queryDefinition.query?.SELECT?.columns[3];
    const rkfCol = queryDefinition.query?.SELECT?.columns[2];

    expect(ckfCol).not.be.undefined;
    expect(rkfCol).not.be.undefined;

    const factSourceToElementMapping: Map<string, string> = new Map();
    QueryModelBuilder.createElementMapping(queryDefinition, factSourceToElementMapping);

    const dummyCubeModel: IAnalyticModel = {
      identifier: {
        key: "aName",
      },
      text: "a cube model",
      sourceModel: {
        factSources: {},
        dimensionSources: {},
      },
      exposedAssociations: {},
      attributes: {},
      measures: {},
      version: ANALYTICMODEL_VERSION_IN_TESTS,
      supportedCapabilities: {},
    };

    const qRkf = QueryModelBuilder.convertCsnXprToRestrictedMeasure(
      dummyCubeModel,
      "AMOUNT_DIV_100_DE",
      rkfElement,
      rkfCol,
      factSourceToElementMapping
    );
    const qCkf = QueryModelBuilder.convertCsnXprToCalculatedMeasure(
      dummyCubeModel,
      "AMOUNT_DIV_100",
      ckfElement,
      ckfCol,
      factSourceToElementMapping
    );

    assert.deepStrictEqual(
      makeJSONComparable(qRkf),
      makeJSONComparable({
        measureType: "AnalyticModelMeasureType.RestrictedMeasure",
        key: "AMOUNT_DIV_100",
        text: "AMOUNT_DIV_100_DE",
        formula: "[0] = [1]",
        elements: {
          "0": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "0VC_COUN",
          },
          "1": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.ConstantValue",
            value: "DE",
          },
        },
        isAuxiliary: false,
        aggregation: "AnalyticModelAggregationTypes.SUM",
        formulaRaw: "\"0VC_COUN\" = 'DE'",
      })
    );

    assert.deepStrictEqual(
      makeJSONComparable(qCkf),
      makeJSONComparable({
        measureType: "AnalyticModelMeasureType.CalculatedMeasure",
        formula: "([0]/[1])",
        elements: {
          "0": {
            operandType: "AnalyticModelCalculatedMeasureOperandType.Element",
            key: "ET0917EPSAT5GEKMUVLF7IIUL",
          },
          "1": {
            operandType: "AnalyticModelCalculatedMeasureOperandType.ConstantValue",
            value: 100,
          },
        },
        text: "AMOUNT_DIV_100",
        isAuxiliary: false,
        formulaRaw: "(ET0917EPSAT5GEKMUVLF7IIUL/100)",
      })
    );

    const cubeModel = new QueryModelBuilder("Remote.W4D.DLAMI_SAMPLE_CALCULATED_MEASURES", queryDefinition);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    assert_deepStrictEqualAnalyticModel(result.cubeModel, AMI_SAMPLE_CALCULATED_MEASURES_QUERY_MODEL_WO_BUSINESSNAMES);

    sandbox
      .stub(AnalyticModelHelper, "loadRepoObjectDefinitions")
      .resolves(structuredClone(BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN));

    sandbox.stub(AnalyticModelHelper, "getEntityFullCsn").callsFake(() => BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN);

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      BW4HCPR_WITH_CONTEXT_DATALAYEROBJECTS_CSN.definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);
  });

  it("should convert a DL CSN with exception aggregation measure into a cube model", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: JS_AGGR_QUERY,
      remoteConnection: TEST_CONNECTION_W4D,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: true,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const definitions = hybridParser.getFullCsn().definitions;
    const queryDefinition = definitions["Remote.W4D.DLQ_ASPARUS"];

    const exceptionAggregationElement = queryDefinition.elements!["0VC_ORD"];
    expect(exceptionAggregationElement).not.be.undefined;

    const exceptionAggregationCol = queryDefinition.query?.SELECT?.columns[15];
    expect(exceptionAggregationCol).not.be.undefined;

    const factSourceToElementMapping: Map<string, string> = new Map();
    QueryModelBuilder.createElementMapping(queryDefinition, factSourceToElementMapping);

    const qkf = QueryModelBuilder.convertToMeasure("0VC_ORD", exceptionAggregationElement, exceptionAggregationCol!);

    assert.deepStrictEqual(
      makeJSONComparable(qkf),
      makeJSONComparable({
        measureType: "AnalyticModelMeasureType.FactSourceMeasure",
        sourceKey: "0",
        text: "Number of Orders",
        key: "0VC_ORD",
        isAuxiliary: true,
        exceptionAggregationType: "AnalyticModelExceptionAggregationType.COUNTNULL",
        exceptionAggregationAttributes: ["0FISCPER"],
      })
    );

    const cubeModel = new QueryModelBuilder("Remote.W4D.DLQ_ASPARUS", queryDefinition);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    assert_deepStrictEqualAnalyticModel(result.cubeModel, JS_AGGR_QUERY_QUERY_MODEL);

    sandbox
      .stub(AnalyticModelHelper, "loadRepoObjectDefinitions")
      .resolves(structuredClone(JS_AGGR_QUERY_DATALAYER_CSN));

    sandbox.stub(AnalyticModelHelper, "getEntityFullCsn").callsFake(() => JS_AGGR_QUERY_DATALAYER_CSN);

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      JS_AGGR_QUERY_DATALAYER_CSN.definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);

    const cubeDefinition = cubeCsnResult.csn!.definitions["Remote.W4D.DLQ_ASPARUS"];
    expect(cubeDefinition).not.undefined;
    const elements = cubeDefinition.elements;
    expect(elements).not.undefined;
    const measureWithExceptionAggregation = elements!["0VC_ORD"];
    expect(measureWithExceptionAggregation).not.undefined;
    const exceptionAggregationAnnotation =
      measureWithExceptionAggregation["@AnalyticsDetails.exceptionAggregationSteps"];
    expect(exceptionAggregationAnnotation).not.undefined;
    const step = exceptionAggregationAnnotation[0];
    expect(step.exceptionAggregationBehavior["#"]).equal("COUNTNULL");
    expect(step.exceptionAggregationElements.length).equal(1);
    expect(step.exceptionAggregationElements[0]).equal("0FISCPER");
  });

  it("should create an analytic model in a bw bridge import (AMI_BW4HCPR_EXAMPLE_FML_I18N) with i18n", async () => {
    const connectionName = "CONN1";
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: AMI_BW4HCPR_EXAMPLE_FML_I18N,
      remoteConnection: connectionName,
      context,
      bw4Import: true,
      spaceId: "aTestSpaceId",
      renameDataLayerObjects: false,
      featureFlags: {} as any,
      bwBridgeSpaceId: "BWBRIDGE",
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
    };

    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const fullCsn = hybridParser.getFullCsn();
    const definitions = fullCsn.definitions;
    const i18n = (fullCsn as any).i18n;
    expect(i18n).not.undefined;
    const queryDefinition = definitions[`Remote.${connectionName}.AMI_BW4HCPR_EXAMPLE_FML_QUERY`];

    const cubeModel = new QueryModelBuilder(
      `Remote.${connectionName}.AMI_BW4HCPR_EXAMPLE_FML_QUERY`,
      queryDefinition,
      i18n
    );
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    expect(result.cubeModel.text).not.undefined;
    expect(result.cubeModel.text.startsWith("{i18n")).be.false;

    for (const attrKey in result.cubeModel.attributes || {}) {
      const attribute = result.cubeModel.attributes![attrKey];
      const textValue = attribute.text;
      expect(textValue.startsWith("{i18n")).be.false;
    }

    for (const measureKey in result.cubeModel.measures || {}) {
      const measure = result.cubeModel.measures![measureKey];
      const textValue = measure.text;
      expect(textValue.startsWith("{i18n")).be.false;
    }

    const queryObj = parseResult.objects?.find((obj) => obj.name === "Remote.CONN1.AMI_BW4HCPR_EXAMPLE_FML_QUERY");
    expect(queryObj).not.undefined;
    assert.deepStrictEqual(queryObj?.i18n, {
      fr: {
        "<EMAIL>": "0VC_MAX",
        "<EMAIL>": "0VC_MIN",
        "<EMAIL>": "Version d'exercice",
        "<EMAIL>": "Exercice comptable",
        "<EMAIL>": "Unité de quantité",
        "<EMAIL>": "0VC_COUN",
        "<EMAIL>": "0VC_CUST",
        "<EMAIL>": "0VC_MAX",
        "<EMAIL>": "0VC_MIN",
        "<EMAIL>": "Formule 1",
        "<EMAIL>": "AMI_BW4HCPR_EXAMPLE_FML",
      },
    });
  });

  it("should create an analytic model with filter variable from BW import", async () => {
    const connectionName = "CONN1";
    const queryName = "JS_FIL_VAR_1";
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: JS_FIL_VAR_1,
      remoteConnection: connectionName,
      context,
      bw4Import: true,
      spaceId: "aTestSpaceId",
      renameDataLayerObjects: false,
      featureFlags: {} as any,
      bwBridgeSpaceId: "BWBRIDGE",
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
    };

    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const fullCsn = hybridParser.getFullCsn();
    const definitions = fullCsn.definitions;
    const i18n = (fullCsn as any).i18n;
    expect(i18n).not.undefined;
    const queryDefinition = definitions[`Remote.${connectionName}.${queryName}`];
    expect(queryDefinition).not.be.undefined;

    const variableNames = Commons.checkForVariablesInCsn(queryDefinition);
    expect(variableNames).be.deep.equal(["JSHCHA2"]);

    const cubeModel = new QueryModelBuilder(`Remote.${connectionName}.${queryName}`, queryDefinition, i18n);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    expect(result.cubeModel.variables).not.undefined;
    expect(result.cubeModel.variables).not.undefined;
    expect(result.cubeModel.variables!.JSHCHA2).not.undefined;

    expect(result.cubeModel.variables!.JSHCHA2).be.deep.equal({
      mandatory: false,
      selectionType: "AnalyticModelVariableSelectionType.SINGLE",
      multipleSelections: false,
      parameterType: "AnalyticModelParameterType.StoryFilter",
      referenceAttribute: "JSHCHA2",
      defaultValue: "B01",
    });

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      fullCsn.definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);

    expect(cubeCsnResult.csn).not.undefined;
    expect(cubeCsnResult.csn!.definitions).not.undefined;
    expect(cubeCsnResult.csn!.definitions[`Remote.${connectionName}.${queryName}`]).not.undefined;
    expect(cubeCsnResult.csn!.definitions[`Remote.${connectionName}.${queryName}`].elements).not.undefined;
    expect(cubeCsnResult.csn!.definitions[`Remote.${connectionName}.${queryName}`].elements!["JSHCHA2∞D0"]).not
      .undefined;

    expect(
      cubeCsnResult.csn!.definitions[`Remote.${connectionName}.${queryName}`].elements!["JSHCHA2∞D0"]
    ).be.deep.equal({
      "@EndUserText.label": "{i18n>JS_FIL_VAR_1.JSHCHA2AM_SAMPLE_VAR_RESTRICTED_MEASURE (1)@ENDUSERTEXT.LABEL}",
      "@ObjectModel.foreignKey.association": {
        "=": "_JSHCHA2∞0",
      },
      "@Consumption.filter.selectionType": {
        "#": "SINGLE",
      },
      "@Consumption.filter.mandatory": false,
      "@Consumption.filter.hidden": false,
      "@Consumption.filter.multipleSelections": false,
      "@Consumption.filter.defaultValue": "B01",
      "@ObjectModel.text.element": [
        {
          "=": "JSHCHA2∞D0∞T",
        },
      ],
      type: "cds.String",
      length: 5,
    });
  });

  it("should create an analytic model with filter variable (SINGLE) for restricted measure from BW import", async () => {
    const connectionName = "CONN1";
    const queryName = "JS_RES_KYF_VAR_1";
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: JS_RES_KYF_VAR_1,
      remoteConnection: connectionName,
      context,
      bw4Import: true,
      spaceId: "aTestSpaceId",
      renameDataLayerObjects: false,
      featureFlags: {} as any,
      bwBridgeSpaceId: "BWBRIDGE",
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
    };

    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    // const validate = hybridParser.validateOutputCsn();
    // assert.equal(validate.hasError, false);

    const fullCsn = hybridParser.getFullCsn();
    const definitions = fullCsn.definitions;
    const i18n = (fullCsn as any).i18n;
    expect(i18n).not.undefined;
    const queryDefinition = definitions[`Remote.${connectionName}.${queryName}`];
    expect(queryDefinition).not.be.undefined;

    const variableNames = Commons.checkForVariablesInCsn(queryDefinition);
    expect(variableNames).be.deep.equal(["JSHCHA2_LOW", "JSHCHA2_HIGH"]);

    const cubeModel = new QueryModelBuilder(`Remote.${connectionName}.${queryName}`, queryDefinition, i18n);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    expect(result.cubeModel.variables).not.undefined;
    expect(result.cubeModel.variables).not.undefined;
    expect(result.cubeModel.variables!.JSHCHA2_LOW).not.undefined;
    expect(result.cubeModel.variables!.JSHCHA2_HIGH).not.undefined;

    expect(result.cubeModel.variables!.JSHCHA2_LOW).be.deep.equal({
      mandatory: false,
      selectionType: "AnalyticModelVariableSelectionType.SINGLE",
      multipleSelections: false,
      parameterType: "AnalyticModelParameterType.Filter",
      referenceAttribute: "JSHCHA2",
      text: "{i18n><EMAIL>}",
    });
    expect(result.cubeModel.variables!.JSHCHA2_HIGH).be.deep.equal({
      mandatory: false,
      selectionType: "AnalyticModelVariableSelectionType.SINGLE",
      multipleSelections: false,
      parameterType: "AnalyticModelParameterType.Filter",
      referenceAttribute: "JSHCHA2",
      text: "{i18n><EMAIL>}",
    });

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      fullCsn.definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);

    expect(cubeCsnResult.csn!.definitions[`Remote.${connectionName}.${queryName}`].params!.JSHCHA2_LOW).be.deep.equal({
      "@EndUserText.label": "{i18n><EMAIL>}",
      "@AnalyticsDetails.variable.usageType": {
        "#": "FILTER",
      },
      "@AnalyticsDetails.variable.selectionType": {
        "#": "SINGLE",
      },
      "@AnalyticsDetails.variable.referenceElement": {
        "=": "JSHCHA2∞D0",
      },
      "@AnalyticsDetails.variable.multipleSelections": false,
      "@AnalyticsDetails.variable.mandatory": false,
      type: "cds.String",
      length: 5,
    });
    expect(cubeCsnResult.csn!.definitions[`Remote.${connectionName}.${queryName}`].params!.JSHCHA2_HIGH).be.deep.equal({
      "@EndUserText.label": "{i18n><EMAIL>}",
      "@AnalyticsDetails.variable.usageType": {
        "#": "FILTER",
      },
      "@AnalyticsDetails.variable.selectionType": {
        "#": "SINGLE",
      },
      "@AnalyticsDetails.variable.referenceElement": {
        "=": "JSHCHA2∞D0",
      },
      "@AnalyticsDetails.variable.multipleSelections": false,
      "@AnalyticsDetails.variable.mandatory": false,
      type: "cds.String",
      length: 5,
    });
  });

  it("should create an analytic model with filter variable (INTERVAL) for restricted measure from BW import", async () => {
    const connectionName = "CONN1";
    const queryName = "JS_RES_KYF_VAR_2";
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: JS_RES_KYF_VAR_2,
      remoteConnection: connectionName,
      context,
      bw4Import: true,
      spaceId: "aTestSpaceId",
      renameDataLayerObjects: false,
      featureFlags: {} as any,
      bwBridgeSpaceId: "BWBRIDGE",
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
    };

    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    // const validate = hybridParser.validateOutputCsn();
    // assert.equal(validate.hasError, false);

    const fullCsn = hybridParser.getFullCsn();
    const definitions = fullCsn.definitions;
    const i18n = (fullCsn as any).i18n;
    expect(i18n).not.undefined;
    const queryDefinition = definitions[`Remote.${connectionName}.${queryName}`];
    expect(queryDefinition).not.be.undefined;

    const variableNames = Commons.checkForVariablesInCsn(queryDefinition);
    expect(variableNames).be.deep.equal(["JSHCHA2_INTVL"]);

    const cubeModel = new QueryModelBuilder(`Remote.${connectionName}.${queryName}`, queryDefinition, i18n);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    expect(result.cubeModel.variables).not.undefined;
    expect(result.cubeModel.variables).not.undefined;
    expect(result.cubeModel.variables!.JSHCHA2_INTVL).not.undefined;

    expect(result.cubeModel.variables!.JSHCHA2_INTVL).be.deep.equal({
      mandatory: true,
      selectionType: "AnalyticModelVariableSelectionType.INTERVAL",
      multipleSelections: false,
      parameterType: "AnalyticModelParameterType.Filter",
      referenceAttribute: "JSHCHA2",
      text: "{i18n><EMAIL>}",
      defaultValue: {
        lowValue: "B01",
        highValue: "B10",
      },
    });

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      fullCsn.definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);

    expect(cubeCsnResult.csn!.definitions[`Remote.${connectionName}.${queryName}`].params!.JSHCHA2_INTVL).be.deep.equal(
      {
        "@EndUserText.label": "{i18n><EMAIL>}",
        "@AnalyticsDetails.variable.usageType": {
          "#": "FILTER",
        },
        "@AnalyticsDetails.variable.selectionType": {
          "#": "INTERVAL",
        },
        "@AnalyticsDetails.variable.referenceElement": {
          "=": "JSHCHA2∞D0",
        },
        "@AnalyticsDetails.variable.multipleSelections": false,
        "@AnalyticsDetails.variable.defaultValue": "B01",
        "@AnalyticsDetails.variable.defaultValueHigh": "B10",
        "@AnalyticsDetails.variable.mandatory": true,
        type: "cds.String",
        length: 5,
      }
    );
  });

  it("should create an analytic model with filter variable (RANGE) for restricted measure from BW import", async () => {
    const connectionName = "CONN1";
    const queryName = "JS_RES_KYF_VAR_3";
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: JS_RES_KYF_VAR_3,
      remoteConnection: connectionName,
      context,
      bw4Import: true,
      spaceId: "aTestSpaceId",
      renameDataLayerObjects: false,
      featureFlags: {} as any,
      bwBridgeSpaceId: "BWBRIDGE",
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
    };

    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    // const validate = hybridParser.validateOutputCsn();
    // assert.equal(validate.hasError, false);

    const fullCsn = hybridParser.getFullCsn();
    const definitions = fullCsn.definitions;
    const i18n = (fullCsn as any).i18n;
    expect(i18n).not.undefined;
    const queryDefinition = definitions[`Remote.${connectionName}.${queryName}`];
    expect(queryDefinition).not.be.undefined;

    const variableNames = Commons.checkForVariablesInCsn(queryDefinition);
    expect(variableNames).be.deep.equal(["JSHCHA2_RANGE"]);

    const cubeModel = new QueryModelBuilder(`Remote.${connectionName}.${queryName}`, queryDefinition, i18n);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    expect(result.cubeModel.variables).not.undefined;
    expect(result.cubeModel.variables).not.undefined;
    expect(result.cubeModel.variables!.JSHCHA2_RANGE).not.undefined;

    expect(result.cubeModel.variables!.JSHCHA2_RANGE).be.deep.equal({
      mandatory: false,
      selectionType: "AnalyticModelVariableSelectionType.RANGE",
      multipleSelections: true,
      parameterType: "AnalyticModelParameterType.Filter",
      referenceAttribute: "JSHCHA2",
      text: "Range",
      defaultValue: [
        {
          lowValue: "B01",
          sign: "AnalyticModelDefaultRangeSign.INCLUDE",
          option: "AnalyticModelDefaultRangeOption.EQ",
        },
      ],
    });

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      fullCsn.definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);

    expect(cubeCsnResult.csn!.definitions[`Remote.${connectionName}.${queryName}`].params!.JSHCHA2_RANGE).be.deep.equal(
      {
        "@EndUserText.label": "Range",
        "@AnalyticsDetails.variable.usageType": {
          "#": "FILTER",
        },
        "@AnalyticsDetails.variable.selectionType": {
          "#": "RANGE",
        },
        "@AnalyticsDetails.variable.referenceElement": {
          "=": "JSHCHA2∞D0",
        },
        "@AnalyticsDetails.variable.multipleSelections": true,
        "@AnalyticsDetails.variable.defaultRanges": [
          {
            sign: "I",
            option: "EQ",
            low: "B01",
            high: undefined,
          },
        ],
        "@AnalyticsDetails.variable.mandatory": false,
        type: "cds.String",
        length: 5,
      }
    );
  });

  it("should return a text/key/label i18n placeholder for a label depending on passed i18n and a languagekey", async () => {
    const i18n_singleKey = {
      fr: {
        "<EMAIL>": "Maximum",
        "<EMAIL>": "Le minimum",
        "<EMAIL>": "Version d'exercice",
      },
    };

    const i18n_multipleKey = {
      fr: {
        "<EMAIL>": "Maximum",
        "<EMAIL>": "Le minimum",
        "<EMAIL>": "Version d'exercice",
      },
      de: {
        "<EMAIL>": "Maximalwert",
        "<EMAIL>": "Minimalwert",
        "<EMAIL>": "Test Version",
      },
    };

    expect(
      Commons.getI18nOrLabelOrKey(
        "abc",
        { "@EndUserText.label": "{i18n><EMAIL>}" } as any,
        i18n_singleKey,
        undefined
      )
    ).be.equal("Le minimum");

    expect(
      Commons.getI18nOrLabelOrKey("abc", { "@EndUserText.label": "{i18n>WRONG_KEY}" } as any, i18n_singleKey, undefined)
    ).be.equal("{i18n>WRONG_KEY}");

    expect(Commons.getI18nOrLabelOrKey("abc", {} as any, i18n_singleKey, undefined)).be.equal("abc");

    expect(
      Commons.getI18nOrLabelOrKey(
        "abc",
        { "@EndUserText.label": "{i18n><EMAIL>}" } as any,
        i18n_singleKey,
        "fr"
      )
    ).be.equal("Le minimum");

    expect(
      Commons.getI18nOrLabelOrKey(
        "abc",
        { "@EndUserText.label": "{i18n><EMAIL>}" } as any,
        i18n_singleKey,
        "de"
      )
    ).be.equal("{i18n><EMAIL>}");

    expect(
      Commons.getI18nOrLabelOrKey(
        "abc",
        { "@EndUserText.label": "{i18n><EMAIL>}" } as any,
        i18n_multipleKey,
        undefined
      )
    ).be.equal("{i18n><EMAIL>}");

    expect(
      Commons.getI18nOrLabelOrKey(
        "abc",
        { "@EndUserText.label": "{i18n><EMAIL>}" } as any,
        i18n_multipleKey,
        "de"
      )
    ).be.equal("Minimalwert");

    expect(
      Commons.getI18nOrLabelOrKey(
        "abc",
        { "@EndUserText.label": "{i18n><EMAIL>}" } as any,
        i18n_multipleKey,
        "fr"
      )
    ).be.equal("Le minimum");
  });

  it("should create an analytic model in a bw bridge import (BW4HCPR_EXAMPLE_I18N) with i18n", async () => {
    const connectionName = "CONN1";
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: BW4HCPR_EXAMPLE_I18N,
      remoteConnection: connectionName,
      context,
      bw4Import: true,
      spaceId: "aTestSpaceId",
      renameDataLayerObjects: false,
      featureFlags: {} as any,
      bwBridgeSpaceId: "BWBRIDGE",
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
    };

    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const fullCsn = hybridParser.getFullCsn();
    const definitions = fullCsn.definitions;
    const i18n = (fullCsn as any).i18n;
    expect(i18n).not.undefined;
    const queryDefinition = definitions[`Remote.${connectionName}.BW4HCPR_EXAMPLE_QUERY`];

    const cubeModel = new QueryModelBuilder(`Remote.${connectionName}.BW4HCPR_EXAMPLE_QUERY`, queryDefinition, i18n);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    expect(result.cubeModel.text).not.undefined;
    expect(result.cubeModel.text.startsWith("{i18n")).be.false;

    for (const attrKey in result.cubeModel.attributes || {}) {
      const attribute = result.cubeModel.attributes![attrKey];
      const textValue = attribute.text;
      expect(textValue.startsWith("{i18n")).be.false;
    }

    for (const measureKey in result.cubeModel.measures || {}) {
      const measure = result.cubeModel.measures![measureKey];
      const textValue = measure.text;
      expect(textValue.startsWith("{i18n")).be.false;
    }

    const queryObj = parseResult.objects?.find((obj) => obj.name === "Remote.CONN1.BW4HCPR_EXAMPLE_QUERY");
    expect(queryObj).not.undefined;
    assert.deepStrictEqual(queryObj?.i18n, {
      fr: {
        "<EMAIL>": "0VC_MAX",
        "<EMAIL>": "Unité de quantité",
        "<EMAIL>": "0VC_COUN",
        "<EMAIL>": "0VC_CUST",
        "<EMAIL>": "0VC_MAX",
        "<EMAIL>": "BW4HCPR_EXAMPLE",
      },
    });
  });

  it("should create an analytic model w/ count distinct measure", async () => {
    const connectionName = "CONN1";
    const TARGET_SPACE_ID = "aTestSpaceId";
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: MEASURE_COUNT_DISTINCT,
      remoteConnection: connectionName,
      context,
      bw4Import: true,
      spaceId: TARGET_SPACE_ID,
      renameDataLayerObjects: false,
      featureFlags: {} as any,
      bwBridgeSpaceId: "BWBRIDGE",
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
    };

    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const fullCsn = hybridParser.getFullCsn();

    const dlObjects = parseResult.objects;
    const dlAnalyticModel = dlObjects?.filter((objects) => objects.isDataLayerQuery);

    expect(dlAnalyticModel).not.undefined;
    expect(dlAnalyticModel?.length).equal(1);

    const name = dlAnalyticModel![0].name;
    const queryCsn = dlAnalyticModel![0].definitions[name];

    const cubeModel = new QueryModelBuilder(name, queryCsn);
    const result = cubeModel.createCubeModel();
    expect(result.hasError).be.false;

    const countDistinctAmJSON = result.cubeModel.measures?.ET0917EPSAT5RRRIUTM46U0XY;
    expect(countDistinctAmJSON).deep.equal({
      measureType: "AnalyticModelMeasureType.CountDistinct",
      countDistinctAttributes: ["JSBASIS"],
      text: "{i18n><EMAIL>}",
      isAuxiliary: false,
    });

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TARGET_SPACE_ID,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      fullCsn.definitions
    );
    expect(cubeCsnResult.hasErrors).be.false;
    const cubeCsn = cubeCsnResult.csn?.definitions[name];
    expect(cubeCsn).not.undefined;
    const countDistinctMeasureCsn = cubeCsn?.elements?.ET0917EPSAT5RRRIUTM46U0XY;
    expect(countDistinctMeasureCsn).deep.equal({
      "@EndUserText.label": "{i18n><EMAIL>}",
      "@AnalyticsDetails.measureType": {
        "#": "BASE",
      },
      "@Aggregation.default": {
        "#": "COUNT_DISTINCT",
      },
      "@Aggregation.referenceElement": ["JSBASIS∞D0"],
      type: "cds.Integer",
    });
  });

  it("should convert a DL CSN with filter into a cube model", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: ASPARUS,
      remoteConnection: TEST_CONNECTION_W4D,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: true,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const definitions = hybridParser.getFullCsn().definitions;
    const queryDefinition = definitions["Remote.W4D.DLQ_ASPARUS"];

    const whereClause = queryDefinition.query?.SELECT?.where;
    expect(whereClause).not.be.undefined;

    const cubeModel = new QueryModelBuilder("Remote.W4D.DLQ_ASPARUS", queryDefinition);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;
    expect(result.cubeModel.filter).not.be.undefined;

    assert_deepStrictEqualAnalyticModel(result.cubeModel, ASPARUS_QUERY_MODEL);

    sandbox
      .stub(AnalyticModelHelper, "loadRepoObjectDefinitions")
      .resolves(structuredClone(ASPARUS_DATALAYER_OBJECTS_CSN));

    sandbox.stub(AnalyticModelHelper, "getEntityFullCsn").callsFake(() => ASPARUS_DATALAYER_OBJECTS_CSN);

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      ASPARUS_DATALAYER_OBJECTS_CSN.definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);
    expect(cubeCsnResult.csn?.definitions["Remote.W4D.DLQ_ASPARUS"].query?.SELECT?.where).not.undefined;
  });

  it.skip("should convert a DL CSN with basic types into a cube model", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: BWHYB_BASIC_OPERATORS,
      remoteConnection: TEST_CONNECTION_D2T,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: true,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const definitions = hybridParser.getFullCsn().definitions;
    const queryDefinition = definitions["Remote.D2T.DLBWHYB_BASIC_OPERATORS"];

    expect(queryDefinition).not.be.undefined;

    const cubeModel = new QueryModelBuilder("Remote.D2T.DLBWHYB_BASIC_OPERATORS", queryDefinition);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;
    expect(result.cubeModel.filter).be.undefined;

    assert_deepStrictEqualAnalyticModel(result.cubeModel, BWHYB_BASIC_OPERATORS_QUERY_MODEL);

    sandbox
      .stub(AnalyticModelHelper, "loadRepoObjectDefinitions")
      .resolves(structuredClone(BWHYB_BASIC_OPERATORS_DATALAYER_OBJECTS_CSN));

    sandbox.stub(AnalyticModelHelper, "getEntityFullCsn").callsFake(() => BWHYB_BASIC_OPERATORS_DATALAYER_OBJECTS_CSN);

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      BWHYB_BASIC_OPERATORS_DATALAYER_OBJECTS_CSN.definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);
  });

  it("should convert a DL CSN with hidden measures into a cube model", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: DWC_NMS_CP1_Q2,
      remoteConnection: TEST_CONNECTION_D2T,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: true,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const definitions = hybridParser.getFullCsn().definitions;
    const queryDefinition = definitions["Remote.D2T.DLDWC_NMS_CP1_Q2"];

    expect(queryDefinition).not.be.undefined;

    const cubeModel = new QueryModelBuilder("Remote.D2T.DLDWC_NMS_CP1_Q2", queryDefinition);
    const result = cubeModel.createCubeModel();

    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    expect(result.cubeModel?.measures?.DWC_NMS_CP1_RKF1.isAuxiliary).be.true;
    expect(result.cubeModel?.measures?.DWC_NMS_CP1_CKF1.isAuxiliary).be.true;
    expect(result.cubeModel?.measures?.DWC_NMS_CP1_CKF2.isAuxiliary).be.true;
    expect(result.cubeModel?.measures?.["0HT_PF"].isAuxiliary).be.true;
    expect(result.cubeModel?.measures?.["0HT_QTY"].isAuxiliary).be.true;

    sandbox
      .stub(AnalyticModelHelper, "loadRepoObjectDefinitions")
      .resolves({ definitions: structuredClone(definitions), idMapping: {} });

    sandbox.stub(AnalyticModelHelper, "getEntityFullCsn").callsFake(() => definitions as any);

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);
  });

  it("should convert a DL CSN with i18n into cube to save and deploy", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: BW4HCPR_Q001_QUERY,
      remoteConnection: TEST_CONNECTION_D2T,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: false,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const objMap: Map<string, ICsnObjectToDeploy> = new Map();
    parseResult.objects?.forEach((obj) => {
      objMap.set(obj.name, obj);
    });

    const definitions = hybridParser.getFullCsn().definitions;
    const queryName = "Remote.D2T.BW4HCPR_Q001_QUERY";

    const queryDefinition = definitions[queryName];
    const i18n = objMap.get(queryName)?.i18n;

    expect(queryDefinition).not.be.undefined;
    expect(i18n).not.be.undefined;

    const qmBuilder_save = new QueryModelBuilder(queryName, queryDefinition, i18n, "de");
    const resultSave = qmBuilder_save.createCubeModel();

    expect(resultSave.hasError).be.false;
    expect(resultSave.cubeModel).not.be.undefined;

    const qmBuilder_deploy = new QueryModelBuilder(queryName, queryDefinition);
    const resultDeploy = qmBuilder_deploy.createCubeModel();

    expect(resultDeploy.hasError).be.false;
    expect(resultDeploy.cubeModel).not.be.undefined;

    expect(resultSave.cubeModel.attributes?.["0FISCPER"]?.text).be.equal("Geschäftsjahr / Periode");
    expect(resultDeploy.cubeModel.attributes?.["0FISCPER"]?.text).be.equal(
      "{i18n><EMAIL>}"
    );
  });

  it("CUSTISSUE_CS20230005819268 wrong handling od val=''", async () => {
    const supportJson = CUSTISSUE_CS20230005819268;
    const queryDefinitions: ICsnDefinitions = {};
    supportJson.input.objectsToDeployInBatch.forEach((objectToDeployInBatch) => {
      if (!objectToDeployInBatch.isBusinessLayerObject) {
        Object.assign(queryDefinitions, objectToDeployInBatch.objectToDeploy.definitions);
      }
    });

    const cubeModel = new QueryModelBuilder(
      "Remote.SAP_B4H_C88_MT.DLYQU_Y08NL1001_KDE_TEST_DAC1",
      queryDefinitions["Remote.SAP_B4H_C88_MT.DLYQU_Y08NL1001_KDE_TEST_DAC1"]
    );
    const result = cubeModel.createCubeModel();
    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      queryDefinitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);
  });

  it("CUSTISSUE_CS20230005819234 not marked as attribute", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: CS20230005819234_ORIG_CSN,
      remoteConnection: TEST_CONNECTION_W4D,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: false,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      bwBridgeSpaceId: "bridge",
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    assert.equal(parseResult.hasWarnings, true);
    assert.deepStrictEqual(parseResult.warnings, [
      "Skip usage of associated dimension P42BWAGRU because attributes P42BWAGRU are not in the elements of the query",
    ]);

    const definitions = hybridParser.getFullCsn().definitions;
    const queryDefinition = definitions["Remote.W4D.S0_VZ19M02_SCRAP_STORAGE_S_CP"];

    expect(queryDefinition).not.be.undefined;

    const cubeModel = new QueryModelBuilder(
      "Remote.W4D.S0_VZ19M02_SCRAP_STORAGE_S_CP",
      definitions["Remote.W4D.S0_VZ19M02_SCRAP_STORAGE_S_CP"]
    );
    const result = cubeModel.createCubeModel();
    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);
  }).timeout(10000);

  it("should convert a query with constant selection into analytic model", async () => {
    const hybridParserParameter: HybridParserParameter = {
      hybridCsn: JS_CONST_SEL,
      remoteConnection: TEST_CONNECTION_W4D,
      context,
      bw4Import: true,
      spaceId: TEST_SPACE,
      renameDataLayerObjects: true,
      dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      featureFlags: {} as any,
    };
    const hybridParser = createParser(hybridParserParameter);
    const parseResult: IParseResult = await hybridParser.parse();
    assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
    const validate = hybridParser.validateOutputCsn();
    assert.equal(validate.hasError, false);

    const definitions = hybridParser.getFullCsn().definitions;
    const queryDefinition = definitions["Remote.W4D.DLJS_CONST_SEL"];

    expect(queryDefinition).not.be.undefined;

    const cubeModel = new QueryModelBuilder("Remote.W4D.DLJS_CONST_SEL", definitions["Remote.W4D.DLJS_CONST_SEL"]);
    const result = cubeModel.createCubeModel();
    expect(result.hasError).be.false;
    expect(result.cubeModel).not.be.undefined;

    const rmWithConstanSelections = Object.keys(result.cubeModel.measures!).filter(
      (key) =>
        result.cubeModel.measures![key].measureType === AnalyticModelMeasureType.RestrictedMeasure &&
        (result.cubeModel.measures![key] as IAnalyticModelRestrictedMeasure).constantSelectionType !== undefined
    );

    expect(rmWithConstanSelections.length).be.equal(4);
    assert.deepStrictEqual(rmWithConstanSelections, [
      "FORMEL_KONSTANTENAUSWAHL",
      "CONSTANT_MAX",
      "CONSTANT_MIN",
      "JS_CONST_AMT",
    ]);

    assert.deepStrictEqual(result.cubeModel.measures!.FORMEL_KONSTANTENAUSWAHL, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      key: "AMOUNT_MAL_2",
      text: "Konstante Selektion auf Formel",
      formula: "",
      elements: {},
      isAuxiliary: false,
      aggregation: AnalyticModelAggregationTypes.SUM,
      constantSelectionType: AnalyticModelConstantSelectionType.Selected,
      constantSelectionAttributes: ["0BC_COUN", "0BC_FIELD", "0BC_REG"],
    });
    assert.deepStrictEqual(result.cubeModel.measures!.CONSTANT_MAX, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      key: "0BC_MAX",
      text: "Konstante Selektion ohne Filter",
      formula: "",
      elements: {},
      isAuxiliary: false,
      aggregation: AnalyticModelAggregationTypes.MAX,
      constantSelectionType: AnalyticModelConstantSelectionType.All,
    });
    assert.deepStrictEqual(result.cubeModel.measures!.CONSTANT_MIN, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      key: "0BC_MIN",
      text: "Konstante Selektion auf 0VC_REG mit Filter",
      formula: "[0] IN ([1],[2])",
      elements: {
        "0": {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "0BC_REG",
        },
        "1": {
          operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
          value: "BAW",
        },
        "2": {
          operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
          value: "NRD",
        },
      },
      isAuxiliary: false,
      aggregation: AnalyticModelAggregationTypes.MIN,
      formulaRaw: "\"0BC_REG\" IN ('BAW','NRD')",
      constantSelectionType: AnalyticModelConstantSelectionType.Selected,
      constantSelectionAttributes: ["0BC_REG"],
    });
    assert.deepStrictEqual(result.cubeModel.measures!.JS_CONST_AMT, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      key: "0BC_AMT",
      text: "Filter auf 0BC_CUST",
      formula: "[0] = [1]",
      elements: {
        "0": {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "0BC_CUST",
        },
        "1": {
          operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
          value: "000001",
        },
      },
      isAuxiliary: false,
      aggregation: AnalyticModelAggregationTypes.SUM,
      formulaRaw: "\"0BC_CUST\" = '000001'",
      constantSelectionType: AnalyticModelConstantSelectionType.Selected,
      constantSelectionAttributes: ["0BC_CUST"],
    });

    const cubeCsnResult = await AnalyticModelCsnConverterHelper.convertQueryModel(
      context,
      result.cubeModel,
      TEST_SPACE,
      true, // executePredeployTasks
      false, // executeImpactValidations
      undefined,
      undefined,
      definitions
    );

    expect(cubeCsnResult.hasErrors).be.equal(false);
  });

  it("should convert a simple CSN filter into query model filter and vice versa", async () => {
    const whereClause = [
      "(",
      "(",
      {
        ref: ["$projection", "0FISCVARNT"],
      },
      "=",
      {
        val: "K4",
      },
      ")",
      "and",
      "(",
      {
        ref: ["$projection", "0FISCYEAR"],
      },
      "=",
      {
        val: "1999",
      },
      ")",
      ")",
    ];

    const factSourceToElementMapping: Map<string, string> = new Map();
    factSourceToElementMapping.set("0FISCVARNT", "0FISCVARNT");
    factSourceToElementMapping.set("0FISCYEAR", "0FISCYEAR");

    const dummyCubeModel: IAnalyticModel = {
      identifier: {
        key: "aName",
      },
      text: "a cube model",
      sourceModel: {
        factSources: {},
        dimensionSources: {},
      },
      exposedAssociations: {},
      attributes: {},
      measures: {},
      version: ANALYTICMODEL_VERSION_IN_TESTS,
      supportedCapabilities: {},
    };

    const filter = QueryModelBuilder.convertCsnXprToFilter(dummyCubeModel, whereClause, factSourceToElementMapping);
    expect(filter).not.undefined;
    expect(filter).not.null;

    const filterModel: IAnalyticModelFilter = {
      text: "global filter",
      formula: "(([0] = [1]) and ([2] = [3]))",
      elements: {
        "0": {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0FISCVARNT",
        },
        "1": {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "K4",
        },
        "2": {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0FISCYEAR",
        },
        "3": {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "1999",
        },
      },
    };
    assert.deepStrictEqual(makeJSONComparable(filter), makeJSONComparable(filterModel));

    const filterCsn = emptyConverter.convertFilterToCsnXpr(filterModel);
    assert.deepStrictEqual(makeJSONComparable(filterCsn), makeJSONComparable(whereClause));
  });

  it("should convert a simple CSN filter w/ empty val into query model filter", async () => {
    const whereClause = [
      "NOT",
      "(",
      { ref: ["Y08ML1005"] },
      "=",
      { val: "N" },
      ")",
      "AND",
      { ref: ["Y08ML1052"] },
      "=",
      { val: "" },
      "AND",
      "(",
      "NOT",
      "(",
      { ref: ["Y08ML1093"] },
      "=",
      { val: "" },
      "OR",
      { ref: ["Y08ML1093"] },
      "=",
      { val: "N" },
      "OR",
      { ref: ["Y08ML1093"] },
      "=",
      { val: "O" },
      ")",
      ")",
    ];

    const factSourceToElementMapping: Map<string, string> = new Map();
    factSourceToElementMapping.set("Y08ML1005", "Y08ML1005");
    factSourceToElementMapping.set("Y08ML1052", "Y08ML1052");
    factSourceToElementMapping.set("Y08ML1093", "Y08ML1093");
    factSourceToElementMapping.set("Y08ML1093", "Y08ML1093");
    factSourceToElementMapping.set("Y08ML1093", "Y08ML1052");

    const dummyCubeModel: IAnalyticModel = {
      identifier: {
        key: "aName",
      },
      text: "a cube model",
      sourceModel: {
        factSources: {},
        dimensionSources: {},
      },
      exposedAssociations: {},
      attributes: {},
      measures: {},
      version: ANALYTICMODEL_VERSION_IN_TESTS,
      supportedCapabilities: {},
    };

    const filter = QueryModelBuilder.convertCsnXprToFilter(dummyCubeModel, whereClause, factSourceToElementMapping);
    expect(filter).not.undefined;
    expect(filter).not.null;

    const filterModel: IAnalyticModelFilter = {
      text: "global filter",
      formula: " NOT ([0] = [1]) AND [2] = [3] AND ( NOT ([4] = [5] OR [6] = [7] OR [8] = [9]))",
      elements: {
        0: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "Y08ML1005",
        },
        1: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "N",
        },
        2: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "Y08ML1052",
        },
        3: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "",
        },
        4: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "Y08ML1052",
        },
        5: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "",
        },
        6: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "Y08ML1052",
        },
        7: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "N",
        },
        8: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "Y08ML1052",
        },
        9: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "O",
        },
      },
    };
    assert.deepStrictEqual(makeJSONComparable(filter), makeJSONComparable(filterModel));
  });

  it("should convert a simple filter w/ formulaRaw into CSN where clause", async () => {
    const filterModel: IAnalyticModelFilter = {
      text: "global filter",
      formula: "(([0] = [1]) and ([2] = [3]))",
      formulaRaw: `(("0FISCVARNT" = 'K4') and ("0FISCYEAR" = '1999'))`,
      elements: {
        "0": {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0FISCVARNT",
        },
        "1": {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "K4",
        },
        "2": {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0FISCYEAR",
        },
        "3": {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "1999",
        },
      },
    };
    const filterCsn = emptyConverter.convertFilterToCsnXpr(filterModel);
    assert.deepStrictEqual(
      makeJSONComparable(filterCsn),
      makeJSONComparable([
        {
          xpr: [
            {
              ref: ["$projection", "0FISCVARNT"],
            },
            "=",
            {
              val: "K4",
            },
          ],
        },
        "and",
        {
          xpr: [
            {
              ref: ["$projection", "0FISCYEAR"],
            },
            "=",
            {
              val: "1999",
            },
          ],
        },
      ])
    );
  });

  it("should convert a complex CSN filter into query model filter", async () => {
    const whereClause = [
      {
        ref: ["0VC_REG"],
      },
      "IN",
      "(",
      {
        val: "BAW",
      },
      ",",
      {
        val: "BAY",
      },
      ")",
      "AND",
      "(",
      "(",
      {
        ref: ["0VC_CUST"],
      },
      "BETWEEN",
      {
        val: "000027",
      },
      "AND",
      {
        val: "000038",
      },
      "OR",
      {
        ref: ["0VC_CUST"],
      },
      "BETWEEN",
      {
        val: "000100",
      },
      "AND",
      {
        val: "000110",
      },
      "OR",
      {
        ref: ["0VC_CUST"],
      },
      "=",
      {
        val: "000001",
      },
      "OR",
      {
        ref: ["0VC_CUST"],
      },
      "=",
      {
        val: "000002",
      },
      "OR",
      {
        ref: ["0VC_CUST"],
      },
      "=",
      {
        val: "000003",
      },
      "OR",
      {
        ref: ["0VC_CUST"],
      },
      "=",
      {
        val: "000004",
      },
      "OR",
      {
        ref: ["0VC_CUST"],
      },
      "=",
      {
        val: "000005",
      },
      "OR",
      {
        ref: ["0VC_CUST"],
      },
      "=",
      {
        val: "000006",
      },
      "OR",
      {
        ref: ["0VC_CUST"],
      },
      "=",
      {
        val: "000007",
      },
      "OR",
      {
        ref: ["0VC_CUST"],
      },
      "=",
      {
        val: "000008",
      },
      "OR",
      {
        ref: ["0VC_CUST"],
      },
      "=",
      {
        val: "000009",
      },
      ")",
      "AND",
      "NOT",
      "(",
      {
        ref: ["0VC_CUST"],
      },
      "=",
      {
        val: "000104",
      },
      ")",
      ")",
    ];

    const factSourceToElementMapping: Map<string, string> = new Map();
    factSourceToElementMapping.set("0VC_REG", "0VC_REG");
    factSourceToElementMapping.set("0VC_CUST", "0VC_CUST");

    const dummyCubeModel: IAnalyticModel = {
      identifier: {
        key: "aName",
      },
      text: "a cube model",
      sourceModel: {
        factSources: {},
        dimensionSources: {},
      },
      exposedAssociations: {},
      attributes: {},
      measures: {},
      version: ANALYTICMODEL_VERSION_IN_TESTS,
      supportedCapabilities: {},
    };

    const filter = QueryModelBuilder.convertCsnXprToFilter(dummyCubeModel, whereClause, factSourceToElementMapping);
    expect(filter).not.undefined;
    expect(filter).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(filter),
      makeJSONComparable({
        text: "global filter",
        formula:
          "[0] IN ([1],[2]) AND (([3] BETWEEN [4] AND [5] OR [6] BETWEEN [7] AND [8] OR [9] = [10] OR [11] = [12] OR [13] = [14] OR [15] = [16] OR [17] = [18] OR [19] = [20] OR [21] = [22] OR [23] = [24] OR [25] = [26]) AND  NOT ([27] = [28]))",
        elements: {
          "0": {
            operandType: AnalyticModelFilterOperandType.Attribute,
            key: "0VC_REG",
          },
          "1": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "BAW",
          },
          "2": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "BAY",
          },
          "3": {
            operandType: AnalyticModelFilterOperandType.Attribute,
            key: "0VC_CUST",
          },
          "4": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "000027",
          },
          "5": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "000038",
          },
          "6": {
            operandType: AnalyticModelFilterOperandType.Attribute,
            key: "0VC_CUST",
          },
          "7": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "000100",
          },
          "8": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "000110",
          },
          "9": {
            operandType: AnalyticModelFilterOperandType.Attribute,
            key: "0VC_CUST",
          },
          "10": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "000001",
          },
          "11": {
            operandType: AnalyticModelFilterOperandType.Attribute,
            key: "0VC_CUST",
          },
          "12": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "000002",
          },
          "13": {
            operandType: AnalyticModelFilterOperandType.Attribute,
            key: "0VC_CUST",
          },
          "14": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "000003",
          },
          "15": {
            operandType: AnalyticModelFilterOperandType.Attribute,
            key: "0VC_CUST",
          },
          "16": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "000004",
          },
          "17": {
            operandType: AnalyticModelFilterOperandType.Attribute,
            key: "0VC_CUST",
          },
          "18": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "000005",
          },
          "19": {
            operandType: AnalyticModelFilterOperandType.Attribute,
            key: "0VC_CUST",
          },
          "20": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "000006",
          },
          "21": {
            operandType: AnalyticModelFilterOperandType.Attribute,
            key: "0VC_CUST",
          },
          "22": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "000007",
          },
          "23": {
            operandType: AnalyticModelFilterOperandType.Attribute,
            key: "0VC_CUST",
          },
          "24": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "000008",
          },
          "25": {
            operandType: AnalyticModelFilterOperandType.Attribute,
            key: "0VC_CUST",
          },
          "26": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "000009",
          },
          "27": {
            operandType: AnalyticModelFilterOperandType.Attribute,
            key: "0VC_CUST",
          },
          "28": {
            operandType: AnalyticModelFilterOperandType.ConstantValue,
            value: "000104",
          },
        },
      })
    );
  });

  it("should convert a query model currency conversion measure with func into csn function call", async () => {
    const measure: IAnalyticModelCurrencyConversionMeasure = {
      measureType: AnalyticModelMeasureType.CurrencyConversionMeasure,
      key: "0VC_AMT",
      // sourceCurrencyKey: "0CURRENCY",
      sourceCurrencyType: AnalyticModelSourceCurrencyType.derived,
      targetCurrencyType: AnalyticModelTargetCurrencyType.constantValue,
      targetCurrency: { value: "USD" },
      conversionTypeType: AnalyticModelConversionTypeType.constantValue,
      conversionType: {
        value: "M",
      },
      referenceDateType: AnalyticModelReferenceDateType.sqlFunction,
      referenceDate: {
        functionName: "CURRENT_DATE",
      },
      client: "000",
      errorHandling: AnalyticModelErrorHandling.null,
      text: "Betrag",
      isAuxiliary: false,
    };

    const currencyConversionFunctionCall = AnalyticModelCsnConverterV2.createCurrencyConversionFunctionCall(
      "VC_AMT_USD",
      measure,
      measure.key,
      "willi",
      "test_space"
    );
    expect(currencyConversionFunctionCall).not.undefined;
    expect(currencyConversionFunctionCall).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(currencyConversionFunctionCall),
      makeJSONComparable({
        as: "VC_AMT_USD",
        func: "CONVERT_CURRENCY",
        args: {
          AMOUNT: {
            ref: ["$projection", "0VC_AMT"],
          },
          TARGET_UNIT: {
            val: "USD",
          },
          CONVERSION_TYPE: {
            val: "M",
          },
          CLIENT: {
            val: "000",
          },
          ERROR_HANDLING: {
            val: "set to null",
          },
          PRECISIONS_TABLE: {
            val: "SAP.CURRENCY.VIEW.TCURX",
          },
          CONFIGURATION_TABLE: {
            val: "SAP.CURRENCY.VIEW.TCURV",
          },
          PREFACTORS_TABLE: {
            val: "SAP.CURRENCY.VIEW.TCURF",
          },
          RATES_TABLE: {
            val: "SAP.CURRENCY.VIEW.TCURR",
          },
          NOTATIONS_TABLE: {
            val: "SAP.CURRENCY.VIEW.TCURN",
          },
          REFERENCE_DATE: {
            ref: ["$projection", "willi"],
          },
        },
      })
    );
  });

  it("should convert a query model currency conversion measure with fixed date into csn function call", async () => {
    const measure: IAnalyticModelCurrencyConversionMeasure = {
      measureType: AnalyticModelMeasureType.CurrencyConversionMeasure,
      key: "0VC_AMT",
      // sourceCurrencyKey: "0CURRENCY",
      sourceCurrencyType: AnalyticModelSourceCurrencyType.derived,
      targetCurrencyType: AnalyticModelTargetCurrencyType.constantValue,
      targetCurrency: { value: "EUR" },
      conversionTypeType: AnalyticModelConversionTypeType.constantValue,
      conversionType: {
        value: "EURX",
      },
      referenceDateType: AnalyticModelReferenceDateType.constantValue,
      referenceDate: {
        value: "20200101",
      },
      client: "000",
      errorHandling: AnalyticModelErrorHandling.null,
      text: "Amount (USD)",
      isAuxiliary: false,
    };

    const currencyConversionFunctionCall = AnalyticModelCsnConverterV2.createCurrencyConversionFunctionCall(
      "VC_AMT_USD",
      measure,
      measure.key,
      undefined,
      "TEST_SPACE"
    );
    expect(currencyConversionFunctionCall).not.undefined;
    expect(currencyConversionFunctionCall).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(currencyConversionFunctionCall),
      makeJSONComparable({
        as: "VC_AMT_USD",
        func: "CONVERT_CURRENCY",
        args: {
          AMOUNT: {
            ref: ["$projection", "0VC_AMT"],
          },
          TARGET_UNIT: {
            val: "EUR",
          },
          CONVERSION_TYPE: {
            val: "EURX",
          },
          CLIENT: {
            val: "000",
          },
          ERROR_HANDLING: {
            val: "set to null",
          },
          PRECISIONS_TABLE: {
            val: "SAP.CURRENCY.VIEW.TCURX",
          },
          CONFIGURATION_TABLE: {
            val: "SAP.CURRENCY.VIEW.TCURV",
          },
          PREFACTORS_TABLE: {
            val: "SAP.CURRENCY.VIEW.TCURF",
          },
          RATES_TABLE: {
            val: "SAP.CURRENCY.VIEW.TCURR",
          },
          NOTATIONS_TABLE: {
            val: "SAP.CURRENCY.VIEW.TCURN",
          },
          REFERENCE_DATE: {
            val: "20200101",
          },
        },
      })
    );
  });

  it("should convert a simple calculated measure with bracketsinto CSN xpr", async () => {
    const calculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "(([0]-[1])/[2])",
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "00O2TGSQ2MY28OVVBB5VIJIV2",
        },
        1: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "00O2TGSQ2MY28OVVBB5VIJP6M",
        },
        2: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
      },
      text: "Difference in 1000",
      isAuxiliary: false,
    };

    const factSourceToElementMapping: Map<string, string> = new Map();
    factSourceToElementMapping.set("0FISCVARNT", "0FISCVARNT");
    factSourceToElementMapping.set("0FISCYEAR", "0FISCYEAR");

    const calcXpr = emptyConverter.convertCalculatedMeasureToCsnXpr(calculatedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr.xpr),
      makeJSONComparable([
        {
          xpr: [
            {
              ref: ["$projection", "00O2TGSQ2MY28OVVBB5VIJIV2"],
            },
            "-",
            {
              ref: ["$projection", "00O2TGSQ2MY28OVVBB5VIJP6M"],
            },
          ],
        },
        "/",
        {
          val: 1000,
        },
      ])
    );
  });

  it("should convert a simple calculated measure w/o brackets into CSN xpr", async () => {
    const calculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "([0]-[1]/[2])",
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "00O2TGSQ2MY28OVVBB5VIJIV2",
        },
        1: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "00O2TGSQ2MY28OVVBB5VIJP6M",
        },
        2: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
      },
      text: "Difference in 1000",
      isAuxiliary: false,
    };

    const factSourceToElementMapping: Map<string, string> = new Map();
    factSourceToElementMapping.set("0FISCVARNT", "0FISCVARNT");
    factSourceToElementMapping.set("0FISCYEAR", "0FISCYEAR");

    const calcXpr = emptyConverter.convertCalculatedMeasureToCsnXpr(calculatedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr.xpr),
      makeJSONComparable([
        {
          ref: ["$projection", "00O2TGSQ2MY28OVVBB5VIJIV2"],
        },
        "-",
        {
          ref: ["$projection", "00O2TGSQ2MY28OVVBB5VIJP6M"],
        },
        "/",
        {
          val: 1000,
        },
      ])
    );
  });

  it("should convert a simple calculated measure with unary operator into CSN xpr", async () => {
    const calculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "GREATEST([0],[1])+LEAST(GREATEST([2],[3]),[7],[8])-CAST([9] AS INTEGER)",
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        1: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        2: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        "3": {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        4: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        5: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        6: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        7: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        8: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        9: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
      },
      text: "blabla",
      isAuxiliary: false,
    };

    const calcXpr = emptyConverter.convertCalculatedMeasureToCsnXpr(calculatedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr.xpr),
      makeJSONComparable([
        {
          func: "GREATEST",
          args: [
            {
              val: 1000,
            },
            {
              val: 1000,
            },
          ],
        },
        "+",
        {
          func: "LEAST",
          args: [
            {
              func: "GREATEST",
              args: [
                {
                  val: 1000,
                },
                {
                  val: 1000,
                },
              ],
            },
            {
              val: 1000,
            },
            {
              val: 1000,
            },
          ],
        },
        "-",
        {
          val: 1000,
          cast: {
            type: "cds.Integer",
          },
        },
      ])
    );
  });

  it("should convert a simple calculated measure with CASE into CSN xpr", async () => {
    const formula = "CASE WHEN [0] <> [1] THEN [2]*[2] WHEN NOT [3] > [4] THEN [5] ELSE [6] END";

    const calculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula,
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        1: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        2: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        3: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        4: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        5: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        6: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
      },
      text: "blabla",
      isAuxiliary: false,
    };

    const calcXpr = emptyConverter.convertCalculatedMeasureToCsnXpr(calculatedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr.xpr),
      makeJSONComparable([
        "case",
        "when",
        {
          val: 1000,
        },
        "<>",
        {
          val: 1000,
        },
        "then",
        {
          val: 1000,
        },
        "*",
        {
          val: 1000,
        },
        "when",
        "not",
        {
          val: 1000,
        },
        ">",
        {
          val: 1000,
        },
        "then",
        {
          val: 1000,
        },
        "else",
        {
          val: 1000,
        },
        "end",
      ])
    );
  });

  it("should fail to convert a complex calculated measure with CASE into CSN xpr and back to formula", async () => {
    const formula =
      "(1 + 2) / (4 * 9) + 2 + (123 + 123) + LEAST(123,456)+54.4846 + CASE 2 WHEN GREATEST(1,LEAST(1, 4),(1)) THEN 2 ELSE 3 END - CAST(3.14 AS INTEGER) + TO_INTEGER(3.14)";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);
    assert.deepStrictEqual(xpr, [
      {
        descriptionId: "validationDescriptionNoValidCaseExpression",
        messageId: "validationMessageNoValidCaseExpression",
        params: [],
        error: "Found wrong statement after case statement. Must be when.",
      },
    ]);
  });

  it("should convert a complex calculated measure with CASE into CSN xpr and back to formula", async () => {
    const formula =
      "(1 + 2) / (4 * 9) + 2 + (123 + 123) + LEAST(123,456)+54.4846 + CASE WHEN GREATEST(1,LEAST(1, 4),(1)) THEN 2 ELSE 3 END - CAST(3.14 AS INTEGER) + TO_INTEGER(3.14)";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);
    const newMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult)?.expr,
      newMeasure,
      undefined,
      undefined
    );

    expect(newMeasure.formula).be.equal(
      "([0]+[1])/([2]*[3])+[4]+([5]+[6])+LEAST([7],[8])+[9]+ case when GREATEST([10],LEAST([11],[12]),[13]) then [14] else [15] end -CAST([16] AS INTEGER)+TO_INTEGER([17])"
    );

    const createdFormula = CsnXprToAMHelper.convertFormulaToString(newMeasure);
    expect(createdFormula).be.equal(
      "(1+2)/(4*9)+2+(123+123)+LEAST(123,456)+54.4846+ case when GREATEST(1,LEAST(1,4),1) then 2 else 3 end -CAST(3.14 AS INTEGER)+TO_INTEGER(3.14)"
    );

    // double-check, does it work with formatted formula as well

    const xpr2 = AnalyticModelCsnConverterV2.createXprFromFormula(createdFormula);
    const newMeasure2: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr2 as ICreateExpressionResult)?.expr,
      newMeasure2,
      undefined,
      undefined
    );

    expect(newMeasure2.formula).be.equal(
      "([0]+[1])/([2]*[3])+[4]+([5]+[6])+LEAST([7],[8])+[9]+ case when GREATEST([10],LEAST([11],[12]),[13]) then [14] else [15] end -CAST([16] AS INTEGER)+TO_INTEGER([17])"
    );

    const createdFormula2 = CsnXprToAMHelper.convertFormulaToString(newMeasure2);
    expect(createdFormula2).be.equal(
      "(1+2)/(4*9)+2+(123+123)+LEAST(123,456)+54.4846+ case when GREATEST(1,LEAST(1,4),1) then 2 else 3 end -CAST(3.14 AS INTEGER)+TO_INTEGER(3.14)"
    );
  });

  it("should convert a restricted measure with IN op into CSN xpr and back to formula", async () => {
    const formula = "Region IN ('Germany', 'USA')";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);
    const newMeasure: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "BASE_MEASURE",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newMeasure,
      undefined,
      undefined
    );

    expect(newMeasure.formula).be.equal("[0] in ([1],[2])");

    const createdFormula = CsnXprToAMHelper.convertFormulaToString(newMeasure);
    expect(createdFormula).be.equal("Region in ('Germany','USA')");
  });

  it("should convert a calculated measure with 1 constant value into CSN and back to formula", async () => {
    const formula = "42";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);
    const newMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newMeasure,
      undefined,
      undefined
    );

    expect(newMeasure.formula).be.equal("[0]");

    const createdFormula = CsnXprToAMHelper.convertFormulaToString(newMeasure);
    expect(createdFormula).be.equal("42");

    // double-check, does it work with formatted formula as well

    const xpr2 = AnalyticModelCsnConverterV2.createXprFromFormula(createdFormula);
    const newMeasure2: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr2 as ICreateExpressionResult).expr,
      newMeasure2,
      undefined,
      undefined
    );

    expect(newMeasure2.formula).be.equal("[0]");

    const createdFormula2 = CsnXprToAMHelper.convertFormulaToString(newMeasure2);
    expect(createdFormula2).be.equal("42");
  });

  it("should convert a formula raw string with element names with special characters into analytic model measure and back to formula", async () => {
    const formula = 'op1 + "%$ some element name"';

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);
    const newMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newMeasure,
      undefined,
      undefined
    );

    assert.deepStrictEqual(newMeasure, {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "[0]+[1]",
      elements: {
        "0": {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "op1",
        },
        "1": {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "%$ some element name",
        },
      },
      text: "",
    });

    const createdFormula = CsnXprToAMHelper.convertFormulaToString(newMeasure);
    expect(createdFormula).be.equal('op1+"%$ some element name"');

    // double-check, does it work with formatted formula as well

    const xpr2 = AnalyticModelCsnConverterV2.createXprFromFormula(createdFormula);
    const newMeasure2: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr2 as ICreateExpressionResult).expr,
      newMeasure2,
      undefined,
      undefined
    );

    expect(newMeasure2.formula).be.equal("[0]+[1]");

    const createdFormula2 = CsnXprToAMHelper.convertFormulaToString(newMeasure2);
    expect(createdFormula2).be.equal('op1+"%$ some element name"');
  });

  it("should convert a simple calculated measure with func into CSN xpr", async () => {
    const calculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "GREATEST([0],[1])",
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
        1: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 1000,
        },
      },
      text: "blabla",
      isAuxiliary: false,
    };

    const calcXpr = emptyConverter.convertCalculatedMeasureToCsnXpr(calculatedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr),
      makeJSONComparable({
        func: "GREATEST",
        args: [
          {
            val: 1000,
          },
          {
            val: 1000,
          },
        ],
      })
    );
  });

  it("should convert a simple calculated measure with only one op into CSN xpr", async () => {
    const calculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "[0]",
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "anotherMeasure",
        },
      },
      text: "blabla",
      isAuxiliary: false,
    };

    const calcXpr = emptyConverter.convertCalculatedMeasureToCsnXpr(calculatedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr),
      makeJSONComparable({
        ref: ["$projection", "anotherMeasure"],
      })
    );
  });

  it("should convert a simple calculated measure with cast into CSN xpr", async () => {
    const calculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      text: "CalculatedMeasure1",
      isAuxiliary: true,
      formula: "CAST(CAST([0]*[1] AS DOUBLE)*[2] AS DOUBLE)",
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "Quantity",
        },
        1: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "Value",
        },
        2: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "CalculatedMeasure",
        },
      },
    };

    const calcXpr = emptyConverter.convertCalculatedMeasureToCsnXpr(calculatedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr),
      makeJSONComparable({
        xpr: [
          {
            xpr: [
              {
                xpr: [
                  {
                    ref: ["$projection", "Quantity"],
                  },
                  "*",
                  {
                    ref: ["$projection", "Value"],
                  },
                ],
                cast: {
                  type: "cds.Double",
                },
              },
              "*",
              {
                ref: ["$projection", "CalculatedMeasure"],
              },
            ],
            cast: {
              type: "cds.Double",
            },
          },
        ],
      })
    );
  });

  it("should convert a simple calculated measure with a key word into CSN xpr", async () => {
    const calculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      text: "CalculatedMeasure1",
      isAuxiliary: true,
      formula: "[0]*[1]",
      formulaRaw: '"key"*Value',
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "key",
        },
        1: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "Value",
        },
      },
    };

    const calcXpr = emptyConverter.convertCalculatedMeasureToCsnXpr(calculatedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr),
      makeJSONComparable({
        xpr: [
          {
            ref: ["$projection", "key"],
          },
          "*",
          {
            ref: ["$projection", "Value"],
          },
        ],
      })
    );
  });

  it("should convert a simple restricted measure into CSN xpr", async () => {
    const restrictedMeasure: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      key: "Book-Rating",
      text: "RestrictedMeasure",
      formula: "[0] != [1]",
      elements: {
        0: {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "User-ID",
        },
        1: {
          operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
          value: "1112",
        },
      },
      isAuxiliary: false,
    };

    const calcXpr = emptyConverter.convertRestrictedMeasureToCsnXpr(restrictedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr),
      makeJSONComparable([
        "case",
        "when",
        {
          ref: ["$projection", "User-ID"],
        },
        "!=",
        {
          val: "1112",
        },
        "then",
        {
          ref: ["$projection", "Book-Rating"],
        },
        "end",
      ])
    );
  });

  it("should convert a simple restricted measure w/ formulaRaw into CSN xpr", async () => {
    const restrictedMeasure: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      key: "Book-Rating",
      text: "RestrictedMeasure",
      formula: "[0] != [1]",
      formulaRaw: `"User-ID" != '1112'`,
      elements: {
        0: {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "User-ID",
        },
        1: {
          operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
          value: "1112",
        },
      },
      isAuxiliary: false,
    };

    const calcXpr = emptyConverter.convertRestrictedMeasureToCsnXpr(restrictedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr),
      makeJSONComparable([
        "case",
        "when",
        {
          ref: ["$projection", "User-ID"],
        },
        "!=",
        {
          val: "1112",
        },
        "then",
        {
          ref: ["$projection", "Book-Rating"],
        },
        "end",
      ])
    );
  });

  it("should convert a simple restricted w/o restriction (formula) into CSN xpr", async () => {
    const restrictedMeasure: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      key: "Value",
      text: "ExceptionAggregation",
      formula: "",
      elements: {},
      isAuxiliary: false,
      aggregation: AnalyticModelAggregationTypes.SUM,
      exceptionAggregationType: AnalyticModelExceptionAggregationType.COUNT,
      exceptionAggregationAttributes: ["Product"],
    };

    const calcXpr = emptyConverter.convertRestrictedMeasureToCsnXpr(restrictedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr),
      makeJSONComparable({
        ref: ["$projection", "Value"],
      })
    );
  });

  it("should convert a simple restricted w/o restriction (formula) w/ formulaRaw into CSN xpr", async () => {
    const restrictedMeasure: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      key: "Value",
      text: "ExceptionAggregation",
      formula: "",
      formulaRaw: "",
      elements: {},
      isAuxiliary: false,
      aggregation: AnalyticModelAggregationTypes.SUM,
      exceptionAggregationType: AnalyticModelExceptionAggregationType.COUNT,
      exceptionAggregationAttributes: ["Product"],
    };

    const calcXpr = emptyConverter.convertRestrictedMeasureToCsnXpr(restrictedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr),
      makeJSONComparable({
        ref: ["$projection", "Value"],
      })
    );
  });

  it("should convert a simple CSN xpr into cube model measure", async () => {
    // const newMeasure: IAnalyticModelRestrictedMeasure = {
    //   measureType: AnalyticModelMeasureType.RestrictedMeasure,
    //   key: "",
    //   formula: "",
    //   elements: {},
    //   text: "",
    // };

    const xpr = [
      {
        expr: {
          xpr: [
            {
              ref: ["Opportunity"],
            },
            "=",
            {
              ref: [":FILTER_FOR_OPPORTUNITY_ID"],
              param: true,
            },
          ],
        },
      },
    ];

    const newMeasure = QueryModelBuilder.convertCsnXprToRestrictedMeasure(
      undefined as any,
      "aKey",
      {} as any,
      xpr[0].expr,
      undefined as any
    );

    expect(newMeasure.formula).be.equal("[0] = [1]");
    expect(Object.keys(newMeasure.elements!).length).be.equal(2);
    expect(newMeasure.elements![0].operandType).be.equal(AnalyticModelRestrictedMeasureOperandType.Attribute);
    expect(newMeasure.elements![1].operandType).be.equal(AnalyticModelRestrictedMeasureOperandType.FilterVariable);
  });

  it("should convert a simple CSN xpr into cube model measure", async () => {
    // const newMeasure: IAnalyticModelRestrictedMeasure = {
    //   measureType: AnalyticModelMeasureType.RestrictedMeasure,
    //   key: "",
    //   formula: "",
    //   elements: {},
    //   text: "",
    // };

    const xpr = [
      {
        expr: {
          xpr: [
            "case",
            "when",
            {
              ref: ["ActlPlanLineItmKeyFig", "DateFunction"],
            },
            "in",
            {
              list: [
                {
                  val: "FISCALYEARTODATELASTYEAR",
                },
                {
                  val: "FISCALQUARTERTODATELY",
                },
                {
                  val: "FISCALPERIODTODATELASTYEAR",
                },
                {
                  val: "PREVIOUSFISCALYEAR",
                },
                {
                  val: "FISCALYEAR2YEARSAGO",
                },
              ],
            },
            "and",
            {
              ref: ["ActlPlanLineItmKeyFig", "ActualPlanCode"],
            },
            "=",
            {
              val: "A",
            },
            "then",
            {
              ref: ["$projection", "ActlOpgPrftAmtInGlobCrcy"],
            },
            "end",
          ],
        },
      },
    ];

    const newMeasure = QueryModelBuilder.convertCsnXprToRestrictedMeasure(
      undefined as any,
      "aKey",
      {} as any,
      xpr[0].expr,
      undefined as any
    );

    expect(newMeasure.formula).be.equal("[0] in ([1],[2],[3],[4],[5]) and [6] = [7]");
    expect(Object.keys(newMeasure.elements!).length).be.equal(8);
  });

  it("should traverse a CSN xpr with func call", async () => {
    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula("MAX(MIN(MEASURE1, MEASURE2), 1000)");
    const funcCalls: Array<{
      funcName: string;
      noParams: number;
    }> = [];
    const callback = (type: XPR_PARTS, xpr: any): void => {
      if (type === XPR_PARTS.REF) {
        xpr.ref.unshift("$projection");
      } else if (type === XPR_PARTS.FUNC) {
        funcCalls.push({
          funcName: xpr.func,
          noParams: xpr.args.length,
        });
      }
    };
    CsnXprToAMHelper.traverse((xpr as ICreateExpressionResult)?.expr, callback);

    assert.deepStrictEqual((xpr as ICreateExpressionResult)?.expr, {
      func: "MAX",
      args: [
        {
          func: "MIN",
          args: [
            {
              ref: ["$projection", "MEASURE1"],
            },
            {
              ref: ["$projection", "MEASURE2"],
            },
          ],
        },
        {
          val: 1000,
        },
      ],
    });
    assert.deepStrictEqual(funcCalls, [
      {
        funcName: "MAX",
        noParams: 2,
      },
      {
        funcName: "MIN",
        noParams: 2,
      },
    ]);
  });

  it("should traverse a CSN xpr with func call", async () => {
    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula("MAX(MIN(MEASURE1 /100, MEASURE2 / 100), 1000)");
    const callback = (type: XPR_PARTS, xpr: any) => {
      if (type === XPR_PARTS.REF) {
        xpr.ref.unshift("$projection");
      }
    };
    CsnXprToAMHelper.traverse((xpr as ICreateExpressionResult)?.expr, callback);

    assert.deepStrictEqual((xpr as ICreateExpressionResult)?.expr, {
      func: "MAX",
      args: [
        {
          func: "MIN",
          args: [
            {
              xpr: [
                {
                  ref: ["$projection", "MEASURE1"],
                },
                "/",
                {
                  val: 100,
                },
              ],
            },
            {
              xpr: [
                {
                  ref: ["$projection", "MEASURE2"],
                },
                "/",
                {
                  val: 100,
                },
              ],
            },
          ],
        },
        {
          val: 1000,
        },
      ],
    });
  });

  it("should traverse a simple CSN xpr with only 1 element", async () => {
    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula('"0VC_ORD"');
    const callback = (type: XPR_PARTS, xpr: any) => {
      if (type === XPR_PARTS.REF) {
        xpr.ref.unshift("$projection");
      }
    };
    CsnXprToAMHelper.traverse((xpr as ICreateExpressionResult)?.expr, callback);

    assert.deepStrictEqual((xpr as ICreateExpressionResult)?.expr, {
      ref: ["$projection", "0VC_ORD"],
    });
  });

  it("should traverse a simple CSN xpr formula", async () => {
    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula("MEASURE1+MEASURE2");
    const refs: string[] = [];
    const callback = (type: XPR_PARTS, xpr: any) => {
      if (type === XPR_PARTS.REF) {
        refs.push(xpr.ref[0]);
        xpr.ref.unshift("$projection");
      }
    };
    CsnXprToAMHelper.traverse((xpr as ICreateExpressionResult)?.expr, callback);

    assert.deepStrictEqual((xpr as ICreateExpressionResult)?.expr, {
      xpr: [
        {
          ref: ["$projection", "MEASURE1"],
        },
        "+",
        {
          ref: ["$projection", "MEASURE2"],
        },
      ],
    });

    assert.deepStrictEqual(refs, ["MEASURE1", "MEASURE2"]);
  });

  it("should traverse a simple CSN xpr formula with variable", async () => {
    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula("MEASURE1*:VARIABLE1");
    const variables: string[] = [];
    const callback = (type: XPR_PARTS, xpr: any) => {
      if (type === XPR_PARTS.REF) {
        xpr.ref.unshift("$projection");
      } else if (type === XPR_PARTS.PARAM) {
        variables.push(xpr.ref[0]);
      }
    };
    CsnXprToAMHelper.traverse((xpr as ICreateExpressionResult)?.expr, callback);

    assert.deepStrictEqual((xpr as ICreateExpressionResult)?.expr, {
      xpr: [
        {
          ref: ["$projection", "MEASURE1"],
        },
        "*",
        {
          ref: ["VARIABLE1"],
          param: true,
        },
      ],
    });

    assert.deepStrictEqual(variables, ["VARIABLE1"]);
  });

  it("should traverse a function with named parameters", async () => {
    const func = {
      as: "Inital_Stock",
      func: "CUMULATE_OVER_TIME",
      args: {
        MEASURE: {
          ref: ["$projection", "Quantity"],
        },
        TIME_DIMENSION: {
          ref: ["$projection", "BookingDate"],
        },
        AGGREGATION_BEHAVIOR: {
          val: "FIRST",
        },
        RECORD_TYPE: {
          ref: ["$projection", "RecordType"],
        },
        VALIDITY_START_TIME: {
          val: "2024-01-01",
        },
        VALIDITY_END_TIME: {
          val: "2024-02-28",
        },
      },
    };

    AnalyticModelCsnConverterV2.createXprFromFormula("MEASURE1*:VARIABLE1");
    const namedParams: string[] = [];
    const callback = (type: XPR_PARTS, xpr: any) => {
      if (type === XPR_PARTS.NAMEDPARAM) {
        namedParams.push(xpr);
      }
    };
    CsnXprToAMHelper.traverse(func, callback);
    expect(namedParams).to.deep.equal([
      "MEASURE",
      "TIME_DIMENSION",
      "AGGREGATION_BEHAVIOR",
      "RECORD_TYPE",
      "VALIDITY_START_TIME",
      "VALIDITY_END_TIME",
    ]);
  });

  it("should add info to convert result with element & variable", async () => {
    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula("MEASURE1*:VARIABLE1");

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult)?.expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [],
      variables: ["VARIABLE1"],
      elements: ["MEASURE1"],
    });
  });

  it("should add info to convert result with func calls", async () => {
    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula("MAX(MAX(MEASURE1, MEASURE2), 1000)");

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [
        {
          funcName: "MAX",
          noParams: 2,
          args: [
            {
              func: "MAX",
              args: [
                {
                  ref: ["MEASURE1"],
                },
                {
                  ref: ["MEASURE2"],
                },
              ],
            },
            {
              val: 1000,
            },
          ],
        },
        {
          funcName: "MAX",
          noParams: 2,
          args: [
            {
              ref: ["MEASURE1"],
            },
            {
              ref: ["MEASURE2"],
            },
          ],
        },
      ],
      variables: [],
      elements: ["MEASURE1", "MEASURE2"],
    });
  });

  it("should add info to convert result with func calls", async () => {
    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula("MAX(MAX(MEASURE1, MEASURE2))");

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [
        {
          funcName: "MAX",
          noParams: 1,
          args: [
            {
              func: "MAX",
              args: [
                {
                  ref: ["MEASURE1"],
                },
                {
                  ref: ["MEASURE2"],
                },
              ],
            },
          ],
        },
        {
          funcName: "MAX",
          noParams: 2,
          args: [
            {
              ref: ["MEASURE1"],
            },
            {
              ref: ["MEASURE2"],
            },
          ],
        },
      ],
      variables: [],
      elements: ["MEASURE1", "MEASURE2"],
    });
  });

  it("should add info to convert result with expresion and func calls and variable", async () => {
    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(
      "MEASURE3 + MAX(MIN(MEASURE1, MEASURE2),1000) * :VARIABLE1"
    );

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [
        {
          funcName: "MAX",
          noParams: 2,
          args: [
            {
              func: "MIN",
              args: [
                {
                  ref: ["MEASURE1"],
                },
                {
                  ref: ["MEASURE2"],
                },
              ],
            },
            {
              val: 1000,
            },
          ],
        },
        {
          funcName: "MIN",
          noParams: 2,
          args: [
            {
              ref: ["MEASURE1"],
            },
            {
              ref: ["MEASURE2"],
            },
          ],
        },
      ],
      variables: ["VARIABLE1"],
      elements: ["MEASURE3", "MEASURE1", "MEASURE2"],
    });
  });

  it("should add info to convert result with func call with constant", async () => {
    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula("ABS(0)");

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [
        {
          funcName: "ABS",
          noParams: 1,
          args: [
            {
              val: 0,
            },
          ],
        },
      ],
      variables: [],
      elements: [],
    });

    const newMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newMeasure,
      undefined,
      undefined
    );

    expect(newMeasure.elements?.[0]).not.undefined;
    expect(newMeasure.elements?.[0].operandType).be.equal(AnalyticModelCalculatedMeasureOperandType.ConstantValue);

    const newMeasure2: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult)?.expr,
      newMeasure2,
      undefined,
      "aRestMeasure"
    );

    expect(newMeasure2.elements?.[0]).not.undefined;
    expect(newMeasure2.elements?.[0].operandType).be.equal(AnalyticModelRestrictedMeasureOperandType.ConstantValue);
  });

  it("should convert formulas w/ case into calculated or restricted measure", async () => {
    const formula = "CASE WHEN country <> 'DE' THEN measure1*2 WHEN NOT country > 'GB' THEN measure*3 ELSE measure END";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [
        {
          funcName: "CASE",
          noParams: 0,
        },
      ],
      variables: [],
      elements: ["country", "measure1", "measure"],
    });

    const newMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newMeasure,
      undefined,
      undefined
    );

    expect(newMeasure.elements?.[0]).not.undefined;
    // expect(newMeasure.elements?.[0].operandType).be.equal(CalculatedMeasureOperandType.ConstantValue);

    const newMeasure2: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newMeasure2,
      undefined,
      "aRestMeasure"
    );

    expect(newMeasure2.elements?.[0]).not.undefined;
    // expect(newMeasure2.elements?.[0].operandType).be.equal(RestrictedMeasureOperandType.ConstantValue);
  });

  it("should convert formulas w/ string constants into calculated or restricted measure", async () => {
    const formula = "ID='Willi'";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [],
      variables: [],
      elements: ["ID"],
    });

    const newMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newMeasure,
      undefined,
      undefined
    );

    expect(newMeasure.elements?.[0].operandType).equal(AnalyticModelCalculatedMeasureOperandType.Element);
    expect(newMeasure.elements?.[1].operandType).equal(AnalyticModelCalculatedMeasureOperandType.ConstantValue);
    expect((newMeasure.elements?.[1] as IAnalyticModelCalculatedMeasureOperandConstant).value).equal("Willi");

    const newMeasure2: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newMeasure2,
      undefined,
      "aRestMeasure"
    );

    expect(newMeasure2.elements?.[0].operandType).equal(AnalyticModelRestrictedMeasureOperandType.Attribute);
    expect(newMeasure2.elements?.[1].operandType).equal(AnalyticModelRestrictedMeasureOperandType.ConstantValue);
    expect((newMeasure2.elements?.[1] as IAnalyticModelRestrictedMeasureOperandConstant).value).equal("Willi"); // expect(newMeasure2.elements?.[0].operandType).be.equal(RestrictedMeasureOperandType.ConstantValue);
  });

  it("should convert formulas w/ castinto calculated or restricted measure", async () => {
    const formula = "CAST (country AS VARCHAR)";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    /*
{
  xpr: [
    {
      ref: [
        "country",
      ],
      cast: {
        type: "VARCHAR",
      },
    },
  ],
}
    */

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [
        {
          funcName: "CAST",
          noParams: 1,
          type: "VARCHAR",
        },
      ],
      variables: [],
      elements: ["country"],
    });

    const newMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newMeasure,
      undefined,
      undefined
    );

    assert.deepStrictEqual(newMeasure, {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "CAST([0] AS VARCHAR)",
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "country",
        },
      },
      text: "",
    });

    const newMeasure2: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newMeasure2,
      undefined,
      "aRestMeasure"
    );

    assert.deepStrictEqual(newMeasure2, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "CAST([0] AS VARCHAR)",
      elements: {
        0: {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "country",
        },
      },
      text: "",
      key: "",
    });
  });

  it("should convert formulas w/ cast into calculated or restricted measure", async () => {
    const formula = "CAST (measure1 * measure2 AS VARCHAR)";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    /*
{
  xpr: [
    {
      xpr: [
        {
          ref: [
            "measure1",
          ],
        },
        "*",
        {
          ref: [
            "measure2",
          ],
        },
      ],
      cast: {
        type: "VARCHAR",
      },
    },
  ],
}
    */

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [
        {
          funcName: "CAST",
          noParams: 1,
          type: "VARCHAR",
        },
      ],
      variables: [],
      elements: ["measure1", "measure2"],
    });

    const newMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newMeasure,
      undefined,
      undefined
    );

    assert.deepStrictEqual(newMeasure, {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "CAST([0]*[1] AS VARCHAR)",
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "measure1",
        },
        1: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "measure2",
        },
      },
      text: "",
    });

    const newMeasure2: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newMeasure2,
      undefined,
      "aRestMeasure"
    );

    assert.deepStrictEqual(newMeasure2, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "CAST([0]*[1] AS VARCHAR)",
      elements: {
        0: {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "measure1",
        },
        1: {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "measure2",
        },
      },
      text: "",
      key: "",
    });
  });

  it("should convert formulas w/ nested cast into calculated or restricted measure", async () => {
    const formula = "CAST (CAST (measure1 * measure2 AS VARCHAR) * measure2 AS VARCHAR)";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    /*
{
{
  xpr: [
    {
      xpr: [
        {
          xpr: [
            {
              ref: [
                "measure1",
              ],
            },
            "*",
            {
              ref: [
                "measure2",
              ],
            },
          ],
          cast: {
            type: "VARCHAR",
          },
        },
        "*",
        {
          ref: [
            "measure2",
          ],
        },
      ],
      cast: {
        type: "VARCHAR",
      },
    },
  ],
}

cds6:

xpr: {
  xpr: [
    {
      xpr: [
        {
          ref: [
            "measure1",
          ],
        },
        "*",
        {
          ref: [
            "measure2",
          ],
        },
      ],
      cast: {
        type: "VARCHAR",
      },
    },
    "*",
    {
      ref: [
        "measure2",
      ],
    },
  ],
  cast: {
    type: "VARCHAR",
  },
}

    */

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [
        {
          funcName: "CAST",
          noParams: 1,
          type: "VARCHAR",
        },
        {
          funcName: "CAST",
          noParams: 1,
          type: "VARCHAR",
        },
      ],
      variables: [],
      elements: ["measure1", "measure2"],
    });

    const newMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newMeasure,
      undefined,
      undefined
    );

    assert.deepStrictEqual(newMeasure, {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "CAST(CAST([0]*[1] AS VARCHAR)*[2] AS VARCHAR)",
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "measure1",
        },
        1: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "measure2",
        },
        2: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "measure2",
        },
      },
      text: "",
    });

    const newMeasure2: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newMeasure2,
      undefined,
      "aRestMeasure"
    );

    assert.deepStrictEqual(newMeasure2, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "CAST(CAST([0]*[1] AS VARCHAR)*[2] AS VARCHAR)",
      elements: {
        0: {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "measure1",
        },
        1: {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "measure2",
        },
        2: {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "measure2",
        },
      },
      text: "",
      key: "",
    });
  });

  it("should fail to convert formula w/ IN operator w/o brackets", async () => {
    const formula = "A IN X";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    expect((xpr as IANALYTIC_MODEL_LOCALIZED_MESSAGE[]).length).be.equal(1);
    expect((xpr as IANALYTIC_MODEL_LOCALIZED_MESSAGE[])[0].descriptionId).be.equal(
      "validationMessageNoValidInExpression"
    );
    expect((xpr as IANALYTIC_MODEL_LOCALIZED_MESSAGE[])[0].messageId).be.equal(
      "validationDescriptionNoValidInExpression"
    );
  });

  it("should convert formula w/ IN operator w/ 1 op in brackets into calculated or restricted measure and back", async () => {
    const formula = "A IN (X)";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    /*

 {
  expr: {
    xpr: [
      {
        ref: [
          "A",
        ],
      },
      "in",
      {
        xpr: [
          {
            ref: [
              "X",
            ],
          },
        ],
      },
    ],
  },
}

     */

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [],
      variables: [],
      elements: ["A", "X"],
    });

    const newCalculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };
    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newCalculatedMeasure,
      undefined,
      undefined
    );

    assert.deepStrictEqual(newCalculatedMeasure, {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "[0] in ([1])",
      elements: {
        "0": {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "A",
        },
        "1": {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "X",
        },
      },
      text: "",
    });

    const newRestrictedMeasure: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newRestrictedMeasure,
      undefined,
      "aRestMeasure"
    );

    assert.deepStrictEqual(newRestrictedMeasure, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "[0] in ([1])",
      elements: {
        "0": {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "A",
        },
        "1": {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "X",
        },
      },
      text: "",
      key: "",
    });

    const rebuildFormula = CsnXprToAMHelper.convertFormulaToString(newCalculatedMeasure);
    expect(rebuildFormula).be.equal("A in (X)");

    const calcXpr = emptyConverter.convertRestrictedMeasureToCsnXpr(newRestrictedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr),
      makeJSONComparable([
        "case",
        "when",
        {
          ref: ["$projection", "A"],
        },
        "in",
        "(",
        {
          ref: ["$projection", "X"],
        },
        ")",
        "then",
        {
          ref: ["$projection", ""],
        },
        "end",
      ])
    );
  });

  it("should convert formula w/ IN operator w/ 1 op (constant) in brackets into calculated or restricted measure and back", async () => {
    const formula = "A IN ('X')";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    /*

{
  expr: {
    xpr: [
      {
        ref: [
          "A",
        ],
      },
      "in",
      {
        xpr: [
          {
            val: "X",
          },
        ],
      },
    ],
  },
}

     */

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [],
      variables: [],
      elements: ["A"],
    });

    const newCalculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };
    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newCalculatedMeasure,
      undefined,
      undefined
    );

    assert.deepStrictEqual(newCalculatedMeasure, {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "[0] in ([1])",
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "A",
        },
        1: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: "X",
        },
      },
      text: "",
    });

    const newRestrictedMeasure: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newRestrictedMeasure,
      undefined,
      "aRestMeasure"
    );

    assert.deepStrictEqual(newRestrictedMeasure, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "[0] in ([1])",
      elements: {
        0: {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "A",
        },
        1: {
          operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
          value: "X",
        },
      },
      text: "",
      key: "",
    });

    const rebuildFormula = CsnXprToAMHelper.convertFormulaToString(newCalculatedMeasure);
    expect(rebuildFormula).be.equal("A in ('X')");

    const calcXpr = emptyConverter.convertRestrictedMeasureToCsnXpr(newRestrictedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr),
      makeJSONComparable([
        "case",
        "when",
        {
          ref: ["$projection", "A"],
        },
        "in",
        "(",
        {
          val: "X",
        },
        ")",
        "then",
        {
          ref: ["$projection", ""],
        },
        "end",
      ])
    );
  });

  it("should convert formula w/ IN operator w/ 1 op (constant) in brackets w/ formulaRaw into calculated or restricted measure and back", async () => {
    const formula = "A IN ('X')";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    /*

{
  expr: {
    xpr: [
      {
        ref: [
          "A",
        ],
      },
      "in",
      {
        xpr: [
          {
            val: "X",
          },
        ],
      },
    ],
  },
}

     */

    const newCalculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };
    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newCalculatedMeasure,
      undefined,
      undefined
    );

    assert.deepStrictEqual(newCalculatedMeasure, {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "[0] in ([1])",
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "A",
        },
        1: {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: "X",
        },
      },
      text: "",
    });

    const newRestrictedMeasure: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newRestrictedMeasure,
      undefined,
      "aRestMeasure"
    );

    assert.deepStrictEqual(newRestrictedMeasure, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "[0] in ([1])",
      elements: {
        0: {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "A",
        },
        1: {
          operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
          value: "X",
        },
      },
      text: "",
      key: "",
    });

    const rebuildFormula = CsnXprToAMHelper.convertFormulaToString(newCalculatedMeasure);
    expect(rebuildFormula).be.equal("A in ('X')");

    newRestrictedMeasure.formulaRaw = formula;

    const calcXpr = emptyConverter.convertRestrictedMeasureToCsnXpr(newRestrictedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr),
      makeJSONComparable([
        "case",
        "when",
        {
          ref: ["$projection", "A"],
        },
        "in",
        {
          xpr: [
            {
              val: "X",
            },
          ],
        },
        "then",
        {
          ref: ["$projection", ""],
        },
        "end",
      ])
    );
  });

  it("should convert formula w/ IN operator w/ 2 op into calculated or restricted measure and back", async () => {
    const formula = "A IN (X,Y)";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    /*

{
  xpr: [
    {
      ref: [
        "A",
      ],
    },
    "in",
    {
      list: [
        {
          ref: [
            "X",
          ],
        },
        {
          ref: [
            "Y",
          ],
        },
      ],
    },
  ],
}
     */

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [],
      variables: [],
      elements: ["A", "X", "Y"],
    });

    const newCalculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };
    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newCalculatedMeasure,
      undefined,
      undefined
    );

    assert.deepStrictEqual(newCalculatedMeasure, {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "[0] in ([1],[2])",
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "A",
        },
        1: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "X",
        },
        2: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "Y",
        },
      },
      text: "",
    });

    const newRestrictedMeasure: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newRestrictedMeasure,
      undefined,
      "aRestMeasure"
    );

    assert.deepStrictEqual(newRestrictedMeasure, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "[0] in ([1],[2])",
      elements: {
        "0": {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "A",
        },
        "1": {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "X",
        },
        "2": {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "Y",
        },
      },
      text: "",
      key: "",
    });

    const rebuildFormula = CsnXprToAMHelper.convertFormulaToString(newCalculatedMeasure);
    expect(rebuildFormula).be.equal("A in (X,Y)");
  });

  it("should convert formula w/ IN operator w/ 2 op w/ formulaRaw into calculated or restricted measure and back", async () => {
    const formula = "A IN (X,Y)";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    /*

{
  xpr: [
    {
      ref: [
        "A",
      ],
    },
    "in",
    {
      list: [
        {
          ref: [
            "X",
          ],
        },
        {
          ref: [
            "Y",
          ],
        },
      ],
    },
  ],
}
     */

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [],
      variables: [],
      elements: ["A", "X", "Y"],
    });

    const newCalculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };
    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newCalculatedMeasure,
      undefined,
      undefined
    );

    assert.deepStrictEqual(newCalculatedMeasure, {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "[0] in ([1],[2])",
      elements: {
        0: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "A",
        },
        1: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "X",
        },
        2: {
          operandType: AnalyticModelCalculatedMeasureOperandType.Element,
          key: "Y",
        },
      },
      text: "",
    });

    const newRestrictedMeasure: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newRestrictedMeasure,
      undefined,
      "aRestMeasure"
    );

    assert.deepStrictEqual(newRestrictedMeasure, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "[0] in ([1],[2])",
      elements: {
        "0": {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "A",
        },
        "1": {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "X",
        },
        "2": {
          operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
          key: "Y",
        },
      },
      text: "",
      key: "",
    });

    newRestrictedMeasure.formulaRaw = formula;

    const calcXpr = emptyConverter.convertRestrictedMeasureToCsnXpr(newRestrictedMeasure);
    expect(calcXpr).not.undefined;
    expect(calcXpr).not.null;

    assert.deepStrictEqual(
      makeJSONComparable(calcXpr),
      makeJSONComparable([
        "case",
        "when",
        {
          ref: ["$projection", "A"],
        },
        "in",
        {
          list: [
            {
              ref: ["$projection", "X"],
            },
            {
              ref: ["$projection", "Y"],
            },
          ],
        },
        "then",
        {
          ref: ["$projection", ""],
        },
        "end",
      ])
    );
  });

  it("should convert formula w/ IN operator w/ one variable as formula into calculated or restricted measure and back", async () => {
    const formula = ":A_VARIABLE";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    /*

    {
      ref: [
        "A_VARIABLE",
      ],
      param: true,
    }
    */

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [],
      variables: ["A_VARIABLE"],
      elements: [],
    });

    const newCalculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };
    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newCalculatedMeasure,
      undefined,
      undefined
    );

    assert.deepStrictEqual(newCalculatedMeasure, {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "[0]",
      elements: {
        "0": {
          operandType: AnalyticModelCalculatedMeasureOperandType.FormulaVariable,
          key: "A_VARIABLE",
        },
      },
      text: "",
    });

    const newRestrictedMeasure: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newRestrictedMeasure,
      undefined,
      "aRestMeasure"
    );

    assert.deepStrictEqual(newRestrictedMeasure, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "[0]",
      elements: {
        "0": {
          operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
          key: "A_VARIABLE",
        },
      },
      text: "",
      key: "",
    });

    const rebuildFormula = CsnXprToAMHelper.convertFormulaToString(newCalculatedMeasure);
    expect(rebuildFormula).be.equal(":A_VARIABLE");
  });

  it("should convert formula w/ cast into calculated or restricted measure and back", async () => {
    const formula = "cast( 'asdf' as VARCHAR)";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    /*

    {
      val: "asdf",
      cast: {
        type: "VARCHAR",
      },
    }
    */

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [
        {
          funcName: "CAST",
          noParams: 1,
          type: "VARCHAR",
        },
      ],
      variables: [],
      elements: [],
    });

    const newCalculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };
    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newCalculatedMeasure,
      undefined,
      undefined
    );

    assert.deepStrictEqual(newCalculatedMeasure, {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "CAST([0] AS VARCHAR)",
      elements: {
        "0": {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: "asdf",
        },
      },
      text: "",
    });

    const newRestrictedMeasure: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newRestrictedMeasure,
      undefined,
      "aRestMeasure"
    );

    assert.deepStrictEqual(newRestrictedMeasure, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "CAST([0] AS VARCHAR)",
      elements: {
        "0": {
          operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
          value: "asdf",
        },
      },
      text: "",
      key: "",
    });

    const rebuildFormula = CsnXprToAMHelper.convertFormulaToString(newCalculatedMeasure);
    expect(rebuildFormula).be.equal("CAST('asdf' AS VARCHAR)");
  });

  it("should convert formula w/ only 0 into calculated or restricted measure and back", async () => {
    const formula = "0";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    /*
      {
        val: 0,
      }
    */

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult).expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [],
      variables: [],
      elements: [],
    });

    const newCalculatedMeasure: IAnalyticModelCalculatedMeasure = {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "",
      elements: {},
      text: "",
    };
    CsnXprToAMHelper.convertCsnXprIntoCalculatedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newCalculatedMeasure,
      undefined,
      undefined
    );

    assert.deepStrictEqual(newCalculatedMeasure, {
      measureType: AnalyticModelMeasureType.CalculatedMeasure,
      formula: "[0]",
      elements: {
        "0": {
          operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
          value: 0,
        },
      },
      text: "",
    });

    const newRestrictedMeasure: IAnalyticModelRestrictedMeasure = {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "",
      elements: {},
      text: "",
      key: "",
    };

    CsnXprToAMHelper.convertCsnXprIntoRestrictedMeasure(
      (xpr as ICreateExpressionResult).expr,
      newRestrictedMeasure,
      undefined,
      "aRestMeasure"
    );

    assert.deepStrictEqual(newRestrictedMeasure, {
      measureType: AnalyticModelMeasureType.RestrictedMeasure,
      formula: "[0]",
      elements: {
        0: {
          operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
          value: 0,
        },
      },
      text: "",
      key: "",
    });

    const rebuildFormula = CsnXprToAMHelper.convertFormulaToString(newCalculatedMeasure);
    expect(rebuildFormula).be.equal("0");
  });

  it("should validate a restricted measure", async () => {
    const notValidformula1 = "A_VARIABLE";
    const notValidformula2 =
      ":A_VARIABLE and (PRODUCT = 'E') and (COUNTRY = 'DE' or COUNTRY='EN') or (PRODUCT = :A_VARIABLE)";
    const notValidformula3 =
      ":A_VARIABLE or (PRODUCT = 'E') or (COUNTRY = 'DE' or (COUNTRY='EN' and COUNTRY = 'US')) or (PRODUCT = :A_VARIABLE)";
    // const notValidformula4 = "PRODUCT is not";

    const validformula1 =
      ":A_VARIABLE = COUNTRY and (PRODUCT = 'E') and (COUNTRY = 'DE' or COUNTRY='EN') or (PRODUCT = :A_VARIABLE)";
    const validformula2 = "PRODUCT is null";
    const validformula3 = "PRODUCT is not null";

    const nv1 = AnalyticModelCsnConverterV2.createXprFromFormula(notValidformula1);
    const nv2 = AnalyticModelCsnConverterV2.createXprFromFormula(notValidformula2);
    const nv3 = AnalyticModelCsnConverterV2.createXprFromFormula(notValidformula3);
    // expect(AnalyticModelCsnConverter.createXprFromFormula(notValidformula4)).to.throw((err) => {});

    const v1 = AnalyticModelCsnConverterV2.createXprFromFormula(validformula1);
    const v2 = AnalyticModelCsnConverterV2.createXprFromFormula(validformula2);
    const v3 = AnalyticModelCsnConverterV2.createXprFromFormula(validformula3);

    const errors1 = CsnXprToAMHelper.restrictedMeasureExpressionValidation((nv1 as ICreateExpressionResult).expr);
    const errors2 = CsnXprToAMHelper.restrictedMeasureExpressionValidation((nv2 as ICreateExpressionResult).expr);
    const errors3 = CsnXprToAMHelper.restrictedMeasureExpressionValidation((nv3 as ICreateExpressionResult).expr);

    const noErrors1 = CsnXprToAMHelper.restrictedMeasureExpressionValidation((v1 as ICreateExpressionResult).expr);
    const noErrors2 = CsnXprToAMHelper.restrictedMeasureExpressionValidation((v2 as ICreateExpressionResult).expr);
    const noErrors3 = CsnXprToAMHelper.restrictedMeasureExpressionValidation((v3 as ICreateExpressionResult).expr);

    expect(errors1.length).not.equal(0);
    expect(errors2.length).not.equal(0);
    expect(errors3.length).not.equal(0);

    expect(noErrors1.length).equal(0);
    expect(noErrors2.length).equal(0);
    expect(noErrors3.length).equal(0);
  });

  it("should validate a restricted measure with BETWEEN operator", async () => {
    const validformula1 = "PRODUCT BETWEEN 4711 AND 0815 AND COUNTRY = 'DE'";

    const v1 = AnalyticModelCsnConverterV2.createXprFromFormula(validformula1);
    const noErrors1 = CsnXprToAMHelper.restrictedMeasureExpressionValidation((v1 as ICreateExpressionResult).expr);
    expect(noErrors1.length).equal(0);
  });

  it("should convert formula into filter and back", async () => {
    const filter: IAnalyticModelFilter = {
      text: "global filter",
      formula:
        "[0] IN ([1],[2]) AND (([3] BETWEEN [4] AND [5] OR [6] BETWEEN [7] AND [8] OR [9] = [10] OR [11] = [12] OR [13] = [14] OR [15] = [16] OR [17] = [18] OR [19] = [20] OR [21] = [22] OR [23] = [24] OR [25] = [26]) AND  NOT ([27] = [28]))",
      elements: {
        0: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_REG",
        },
        1: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "BAW",
        },
        2: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "BAY",
        },
        3: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        4: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000027",
        },
        5: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000038",
        },
        6: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        7: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000100",
        },
        8: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000110",
        },
        9: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        10: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000001",
        },
        11: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        12: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000002",
        },
        13: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        14: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000003",
        },
        15: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        16: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000004",
        },
        17: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        18: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000005",
        },
        19: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        20: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000006",
        },
        21: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        22: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000007",
        },
        23: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        24: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000008",
        },
        25: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        26: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000009",
        },
        27: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        28: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000104",
        },
      },
    };

    const formula = CsnXprToAMHelper.convertFormulaToString(filter);

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula);

    /*
      {
        val: 0,
      }
    */

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult)?.expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [],
      variables: [],
      elements: ["0VC_REG", "0VC_CUST"],
    });

    const newFilter: IAnalyticModelFilter = {
      formula: "",
      elements: {},
      text: "",
    };
    CsnXprToAMHelper.convertCsnXprIntoFilter((xpr as ICreateExpressionResult)?.expr, newFilter, undefined);

    assert.deepStrictEqual(newFilter, {
      formula:
        "[0] in ([1],[2]) and (([3] between [4] and [5] or [6] between [7] and [8] or [9] = [10] or [11] = [12] or [13] = [14] or [15] = [16] or [17] = [18] or [19] = [20] or [21] = [22] or [23] = [24] or [25] = [26]) and  not ([27] = [28]))",
      elements: {
        0: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_REG",
        },
        1: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "BAW",
        },
        2: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "BAY",
        },
        3: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        4: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000027",
        },
        5: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000038",
        },
        6: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        7: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000100",
        },
        8: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000110",
        },
        9: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        10: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000001",
        },
        11: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        12: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000002",
        },
        13: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        14: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000003",
        },
        15: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        16: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000004",
        },
        17: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        18: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000005",
        },
        19: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        20: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000006",
        },
        21: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        22: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000007",
        },
        23: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        24: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000008",
        },
        25: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        26: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000009",
        },
        27: {
          operandType: AnalyticModelFilterOperandType.Attribute,
          key: "0VC_CUST",
        },
        28: {
          operandType: AnalyticModelFilterOperandType.ConstantValue,
          value: "000104",
        },
      },
      text: "",
    });

    const rebuildFormula = CsnXprToAMHelper.convertFormulaToString(newFilter);
    expect(rebuildFormula).be.equal(
      "\"0VC_REG\" in ('BAW','BAY') and ((\"0VC_CUST\" between '000027' and '000038' or \"0VC_CUST\" between '000100' and '000110' or \"0VC_CUST\" = '000001' or \"0VC_CUST\" = '000002' or \"0VC_CUST\" = '000003' or \"0VC_CUST\" = '000004' or \"0VC_CUST\" = '000005' or \"0VC_CUST\" = '000006' or \"0VC_CUST\" = '000007' or \"0VC_CUST\" = '000008' or \"0VC_CUST\" = '000009') and  not (\"0VC_CUST\" = '000104'))"
    );
  });

  it("should add info to convert result with parameterless function & variable", async () => {
    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula("CASE WHEN :DATE = CURRENT_DATE THEN 1 ELSE 0 END");

    const convertResult: IConvertFormulaResult = {
      funcCalls: [],
      variables: [],
      elements: [],
    };

    CsnXprToAMHelper.addExprInfo((xpr as ICreateExpressionResult)?.expr, convertResult);

    assert.deepStrictEqual(convertResult, {
      funcCalls: [
        {
          funcName: "CASE",
          noParams: 0,
        },
        {
          funcName: "CURRENT_DATE",
          noParams: 0,
        },
      ],
      variables: ["DATE"],
      elements: [],
    });
  });

  it("should create an error for case statement with wrong syntax", async () => {
    const formula =
      "case when true then case a when MAX(MIN(0,case when true then 7 else 5 end),MIN(9,8)) then 9 end else 1 end";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula, false);
    const errors = CsnXprToAMHelper.caseValidation((xpr as ICreateExpressionResult)?.expr);
    assert.deepStrictEqual(errors, [
      {
        descriptionId: "validationDescriptionNoValidCaseExpression",
        messageId: "validationMessageNoValidCaseExpression",
        params: [],
        error: "Found wrong statement after case statement. Must be when.",
      },
    ]);
  });

  it("should create NOT an error for case statement with correct syntax", async () => {
    const formula =
      "case when true then case when MAX(MIN(0,case when true then 7 else 5 end),MIN(9,8)) then 9 end else 1 end";

    const xpr = AnalyticModelCsnConverterV2.createXprFromFormula(formula, false);
    const errors = CsnXprToAMHelper.caseValidation((xpr as ICreateExpressionResult)?.expr);
    expect(errors.length).be.equal(0);
  });
});
