/** @format */

import assert from "assert";
import chai, { expect } from "chai";
import _ from "lodash";
import sinon from "sinon";
import sinonChai from "sinon-chai";
import { DwcObjectTypes } from "../../../../../../shared/metadataImport/metadataImportTypes";
import {
  AnalyticModelAggregationTypes,
  AnalyticModelAttributeType,
  AnalyticModelCalculatedMeasureOperandType,
  AnalyticModelConversionTypeType,
  AnalyticModelDefaultRangeOption,
  AnalyticModelDefaultRangeSign,
  AnalyticModelErrorHandling,
  AnalyticModelExceptionAggregationType,
  AnalyticModelFilterOperandType,
  AnalyticModelMeasureType,
  AnalyticModelParameterType,
  AnalyticModelReferenceDateType,
  AnalyticModelRestrictedMeasureOperandType,
  AnalyticModelSourceCurrencyType,
  AnalyticModelSourceParameterMappingType,
  AnalyticModelSourceType,
  AnalyticModelSourceUnitType,
  AnalyticModelTargetCurrencyType,
  AnalyticModelTargetUnitType,
  AnalyticModelVariableProcessingType,
  AnalyticModelVariableSelectionType,
  IAnalyticModel,
  IAnalyticModelCalculatedMeasure,
  IAnalyticModelFilterSelection,
  IAnalyticModelRestrictedMeasure,
} from "../../../../../../shared/queryBuilder/AnalyticModel";
import {
  IConvertFormulaResult,
  IGetCsnDefinitionFunction,
  MODEL_ELEMENT,
} from "../../../../../../shared/queryBuilder/CsnXprToAMHelper";
import {
  ExprTypeValidator,
  IComparisonDetails,
  IFuncParameterDetails,
} from "../../../../../../shared/queryBuilder/ExprTypeValidator";
import {
  ICreateExpressionResult,
  QueryModelValidator,
  ValidationMessage,
  ValidationMessageType,
} from "../../../../../../shared/queryBuilder/QueryModelValidator";
import { fIsFeatureFlagActive } from "../../../../../../shared/queryBuilder/analyticModelValidators/AnalyticModelValidator";
import { TechnicalNameValidator } from "../../../../../../shared/queryBuilder/analyticModelValidators/TechnicalNameValidator";
import { HybridParserFactory } from "../../../../../hybridParser/HybridParserFactory";
import { HybridParserParameter, IParseResult } from "../../../../../hybridParser/HybridParserType";
import { QueryModelBuilder } from "../../../../../routes/querybuilder/metaDataImport/QueryModelBuilder";
import { AnalyticModelCsnConverterV2 } from "../../../../../routes/querybuilder/services/AnalyticModelCsnConverterV2";
import {
  analyticModelCdsParseExprSync,
  createCdsFormulaToExprConverter,
} from "../../../../../routes/querybuilder/utils/AnalyticModelCdsXprParserHelper";
import {
  fullCsnDataLayerObjects2,
  fullCsnDataLayerObjects3,
  fullCsnwithSimpleTypes,
} from "../../mocks/fullCsnOfDataLayerObjects";
import { E2E_CURRENCY_CONVERSION } from "../metaDataImport/mockData/E2E_CURRENCY_CONVERSION";
import { E2E_CURRENCY_CONVERSION_2 } from "../metaDataImport/mockData/E2E_CURRENCY_CONVERSION_2";
import {
  AM_Invalid_RM_AM,
  AM_Invalid_RM_AM_2,
  AM_confliction_constant_selection_all_attr,
  AM_confliction_constant_selection_selected_attr,
  AM_validFilterExpression,
  AM_wrong_type_params_time_between_funcs,
  DATA_LAYER_ENTITIES,
} from "../mocks/AM_Invalid_RM_AM";
import { amCsnMockNo21 } from "../mocks/csnConverterNo21";
import {
  convertFormulaExpressionResult,
  validateDimKeyMissingFullCsn,
  validateDimKeyMissingModel,
  validateDimMapAttributeModel,
  validateMapAttributeFullCsn,
} from "../mocks/validationMocks";
import { ALEX_WRONG_HIER_TEST_AM, ALEX_WRONG_HIER_TEST_CSNS } from "./mockData/WrongHierarchyLevelName";
import {
  amError17,
  amError18,
  amError6,
  amError9,
  amWarning2,
  analyticModelWithRestrictedMeasureEqualsNull,
  sourceModelsByNameError17,
  sourceModelsByNameError18,
  sourceModelsByNameError6,
  sourceModelsByNameError9,
  sourceModelsByNameWarning2,
} from "./mocks/ValidationMocks";

import { amCsnMockNo35 } from "../mocks/csnConverterNo35";
import { validationError19FullCsn } from "./mocks/validationError19";

import { TestUtil_enhanceAnalyticDesignTimeCsnWithMctModelFullCSN, validate } from "../helper/TestHelperFunctions";
import fCreateXprMock from "../helper/fCreateXprMock";
import { analyticModelWithBooleanAttribute, factSourceWithBooleanDimension } from "../mocks/AM_BooleanDimensions";
import { amCsnMockNo37 } from "../mocks/csnConverterNo37";
import { amCsnMockNo41 } from "../mocks/csnConverterNo41";
import { amCsnMockNo42 } from "../mocks/csnConverterNo42";
import { amCsnMockNo43 } from "../mocks/csnConverterNo43";
import { amCsnMockNo45 } from "../mocks/csnConverterNo45";
import { MeasureValidationMock } from "../mocks/measureValidationMocks";
import { ALEX_AM_WITH_CC_DERIVATION_2 } from "../mocks/stackedModels/ALEX_AM_WITH_CC_DERIVATION_2";
import { ALEX_AUX_MEASURE_2 } from "../mocks/stackedModels/ALEX_AUX_MEASURE_2";
import {
  stackedCalculatedMeasure,
  stackedCalculatedMeasureWithAggregation,
  stackedFilterVariables_1a,
  stackedFilterVariables_1b,
  stackedVariablesUsedInExpression,
} from "../mocks/validateStackedVariables";
import { variableNameConflictsValidationMock } from "../mocks/variableNameConflictMock";
import { validationError20FullCsn } from "./mocks/validateAmGeneral20";
import { validationError21FullCsn } from "./mocks/validateAmGeneral21";
import { validateMockTechnicalNames } from "./mocks/validateMockTechnicalNames";

chai.use(sinonChai);

describe("Analytic Model - Validation", () => {
  let sandbox: sinon.SinonSandbox;

  before(() => {});

  after(async () => {
    sinon.restore();
  });

  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  context("measure cycle detection", () => {
    it("should return no cycles for empty analytic model w/o cycles", async () => {
      const analyticModel: IAnalyticModel = {
        text: "",
        exposedAssociations: {},
        version: "",
        identifier: { key: "test" },
        sourceModel: {
          factSources: {},
        },
        supportedCapabilities: {},
      };
      const cycles = QueryModelValidator.detectCyclesInModel(analyticModel);
      expect(cycles.length).be.equal(0);
    });
    it("should return no cycles for analytic model w/ some measures w/o cycles", async () => {
      const analyticModel: IAnalyticModel = {
        text: "",
        exposedAssociations: {},
        version: "",
        identifier: { key: "test" },
        sourceModel: {
          factSources: {},
        },
        measures: {
          Value: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "1",
            key: "Value",
            text: "Value",
          },
          Quantity: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "1",
            key: "Quantity",
            text: "the Quantity",
          },
          Price: {
            measureType: AnalyticModelMeasureType.CalculatedMeasure,
            formula: "[0]/[1]", // "[Value]/[Quantity]",
            elements: {
              0: {
                operandType: AnalyticModelCalculatedMeasureOperandType.Element,
                key: "Value",
              },
              1: {
                operandType: AnalyticModelCalculatedMeasureOperandType.Element,
                key: "Quantity",
              },
            },
            text: "the Price",
          },
          TheAnswer: {
            measureType: AnalyticModelMeasureType.CalculatedMeasure,
            formula: "[0]", // "42",
            elements: {
              0: {
                operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
                value: 42,
              },
            },
            text: "THE answer to all",
          },
          stupidCalculation: {
            measureType: AnalyticModelMeasureType.CalculatedMeasure,
            formula: "[0]/[1]", // "[Value]/42",
            elements: {
              0: {
                operandType: AnalyticModelCalculatedMeasureOperandType.Element,
                key: "Value",
              },
              1: {
                operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
                value: 42,
              },
            },
            text: "stupid calculation",
          },
          aRestrictedMeasure: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            key: "TheAnswer",
            text: "RestrictedMeasure",
            formula: "[0] != [1]",
            elements: {
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "User-ID",
              },
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: "1112",
              },
            },
            isAuxiliary: false,
          },
        },
        supportedCapabilities: {},
      };
      const cycles = QueryModelValidator.detectCyclesInModel(analyticModel);
      expect(cycles.length).be.equal(0);
    });

    it("should return cycles for analytic model w/ calculated measure w/ direct cycle in analytic measure", async () => {
      const analyticModel: IAnalyticModel = {
        text: "",
        exposedAssociations: {},
        version: "",
        identifier: { key: "test" },
        sourceModel: {
          factSources: {},
        },
        measures: {
          stupidCalculation: {
            measureType: AnalyticModelMeasureType.CalculatedMeasure,
            formula: "[0]/[1]", // "[Value]/42",
            elements: {
              0: {
                operandType: AnalyticModelCalculatedMeasureOperandType.Element,
                key: "stupidCalculation",
              },
              1: {
                operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
                value: 42,
              },
            },
            text: "stupid calculation",
          },
        },
        supportedCapabilities: {},
      };
      const cycles = QueryModelValidator.detectCyclesInModel(analyticModel);
      expect(cycles.length).be.equal(1);
      assert.deepStrictEqual(cycles, [
        {
          path: ["stupidCalculation", "stupidCalculation"],
        },
      ]);
    });

    it("should return cycles for analytic model w/ calculated measure w/ direct cycle in restricted measure", async () => {
      const analyticModel: IAnalyticModel = {
        text: "",
        exposedAssociations: {},
        version: "",
        identifier: { key: "test" },
        sourceModel: {
          factSources: {},
        },
        measures: {
          stupidCalculation: {
            key: "stupidCalculation",
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            formula: "",
            elements: {},
            text: "stupid calculation",
          },
        },
        supportedCapabilities: {},
      };
      const cycles = QueryModelValidator.detectCyclesInModel(analyticModel);
      expect(cycles.length).be.equal(1);
      assert.deepStrictEqual(cycles, [
        {
          path: ["stupidCalculation", "stupidCalculation"],
        },
      ]);
    });

    it("should return cycles for analytic model w/ calculated measure w/ indirect cycle", async () => {
      const analyticModel: IAnalyticModel = {
        text: "",
        exposedAssociations: {},
        version: "",
        identifier: { key: "test" },
        sourceModel: {
          factSources: {},
        },
        measures: {
          stupidCalculationA: {
            measureType: AnalyticModelMeasureType.CalculatedMeasure,
            formula: "[0]/[1]", // "[Value]/42",
            elements: {
              0: {
                operandType: AnalyticModelCalculatedMeasureOperandType.Element,
                key: "stupidCalculationB",
              },
              1: {
                operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
                value: 42,
              },
            },
            text: "stupid calculation",
          },
          stupidCalculationB: {
            measureType: AnalyticModelMeasureType.CalculatedMeasure,
            formula: "[0]/[1]", // "[Value]/42",
            elements: {
              0: {
                operandType: AnalyticModelCalculatedMeasureOperandType.Element,
                key: "stupidCalculationA",
              },
              1: {
                operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
                value: 42,
              },
            },
            text: "stupid calculation",
          },
        },
        supportedCapabilities: {},
      };
      const cycles = QueryModelValidator.detectCyclesInModel(analyticModel);
      expect(cycles.length).be.equal(2);
      assert.deepStrictEqual(cycles, [
        {
          path: ["stupidCalculationA", "stupidCalculationB", "stupidCalculationA"],
        },
        {
          path: ["stupidCalculationB", "stupidCalculationA", "stupidCalculationB"],
        },
      ]);
    });

    it("should return cycles for analytic model w/ calculated measure w/ indirect cycle and measure in between", async () => {
      const analyticModel: IAnalyticModel = {
        text: "",
        exposedAssociations: {},
        version: "",
        identifier: { key: "test" },
        sourceModel: {
          factSources: {},
        },
        measures: {
          stupidCalculationA: {
            measureType: AnalyticModelMeasureType.CalculatedMeasure,
            formula: "[0]/[1]", // "[Value]/42",
            elements: {
              0: {
                operandType: AnalyticModelCalculatedMeasureOperandType.Element,
                key: "stupidCalculationB",
              },
              1: {
                operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
                value: 42,
              },
            },
            text: "stupid calculation",
          },
          stupidCalculationB: {
            measureType: AnalyticModelMeasureType.CalculatedMeasure,
            formula: "[0]/[1]", // "[Value]/42",
            elements: {
              0: {
                operandType: AnalyticModelCalculatedMeasureOperandType.Element,
                key: "stupidCalculationC",
              },
              1: {
                operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
                value: 42,
              },
            },
            text: "stupid calculation",
          },
          stupidCalculationC: {
            measureType: AnalyticModelMeasureType.CalculatedMeasure,
            formula: "[0]/[1]", // "[Value]/42",
            elements: {
              0: {
                operandType: AnalyticModelCalculatedMeasureOperandType.Element,
                key: "stupidCalculationA",
              },
              1: {
                operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
                value: 42,
              },
            },
            text: "stupid calculation",
          },
          stupidCalculationD: {
            measureType: AnalyticModelMeasureType.CalculatedMeasure,
            formula: "[0]/[1]", // "[Value]/42",
            elements: {
              0: {
                operandType: AnalyticModelCalculatedMeasureOperandType.Element,
                key: "stupidCalculationC",
              },
              1: {
                operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
                value: 42,
              },
            },
            text: "stupid calculation",
          },
        },
        supportedCapabilities: {},
      };
      const cycles = QueryModelValidator.detectCyclesInModel(analyticModel);
      expect(cycles.length).be.equal(3);
      assert.deepStrictEqual(cycles, [
        {
          path: ["stupidCalculationA", "stupidCalculationB", "stupidCalculationC", "stupidCalculationA"],
        },
        {
          path: ["stupidCalculationB", "stupidCalculationC", "stupidCalculationA", "stupidCalculationB"],
        },
        {
          path: ["stupidCalculationC", "stupidCalculationA", "stupidCalculationB", "stupidCalculationC"],
        },
      ]);
    });

    it("should return cycles for analytic model w/ restricted measures w/ cycle", async () => {
      const analyticModel: IAnalyticModel = {
        identifier: {
          key: "Currency_ANA_Test",
        },
        text: "Currency_ANA_Test",
        sourceModel: {
          factSources: {
            0: {
              text: "Global_SS_Few_Currencies_View",
              dataEntity: {
                key: "Global_SS_Few_Currencies_View",
              },
            },
          },
          dimensionSources: {},
        },
        exposedAssociations: {},
        attributes: {
          "Row ID": {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Row ID",
              },
            },
            text: "Row ID",
          },
          "Order ID": {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Order ID",
              },
            },
            text: "Order ID",
          },
          "Order Date": {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Order Date",
              },
            },
            text: "Order Date",
          },
          "Ship Date": {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Ship Date",
              },
            },
            text: "Ship Date",
          },
          "Ship Mode": {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Ship Mode",
              },
            },
            text: "Ship Mode",
          },
          "Customer ID": {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Customer ID",
              },
            },
            text: "Customer ID",
          },
          "Customer Name": {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Customer Name",
              },
            },
            text: "Customer Name",
          },
          Segment: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              "0": {
                key: "Segment",
              },
            },
            text: "Segment",
          },
          "Postal Code": {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Postal Code",
              },
            },
            text: "Postal Code",
          },
          City: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "City",
              },
            },
            text: "City",
          },
          State: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "State",
              },
            },
            text: "State",
          },
          Country: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Country",
              },
            },
            text: "Country",
          },
          Currency: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Currency",
              },
            },
            text: "Currency",
          },
          CurrencyName: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "CurrencyName",
              },
            },
            text: "CurrencyName",
          },
          Region: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Region",
              },
            },
            text: "Region",
          },
          Market: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Market",
              },
            },
            text: "Market",
          },
          "Product ID": {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              "0": {
                key: "Product ID",
              },
            },
            text: "Product ID",
          },
          "Product Name": {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Product Name",
              },
            },
            text: "Product Name",
          },
          "Sub-Category": {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Sub-Category",
              },
            },
            text: "Sub-Category",
          },
          Category: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Category",
              },
            },
            text: "Category",
          },
          "Order Priority": {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Order Priority",
              },
            },
            text: "Order Priority",
          },
        },
        measures: {
          sumne_ond_calc: {
            measureType: AnalyticModelMeasureType.CalculatedMeasure,
            text: "sumne ond calc",
            isAuxiliary: false,
            formulaRaw: "Currency_Restriction / Res_Measure1",
            formula: "[0]/[1]",
            elements: {
              0: {
                operandType: AnalyticModelCalculatedMeasureOperandType.Element,
                key: "Currency_Restriction",
              },
              1: {
                operandType: AnalyticModelCalculatedMeasureOperandType.Element,
                key: "Res_Measure1",
              },
            },
            exceptionAggregationType: AnalyticModelExceptionAggregationType.AVERAGENULLZERO,
            exceptionAggregationAttributes: [
              "Order ID",
              "Order Date",
              "Ship Date",
              "Ship Mode",
              "Customer ID",
              "Customer Name",
              "Segment",
              "Postal Code",
              "City",
              "State",
              "Country",
              "Currency",
              "CurrencyName",
              "Region",
              "Market",
              "Product ID",
              "Product Name",
              "Sub-Category",
              "Category",
              "Order Priority",
            ],
          },
          Res_Measure1: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "Res_Measure1",
            isAuxiliary: false,
            formula: "[0] between [1] and [2]",
            formulaRaw: "Market BETWEEN :MARKET_VARIABLE AND :MARKET_VARIABLE",
            elements: {
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Market",
              },
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
                key: "MARKET_VARIABLE",
              },
              2: {
                operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
                key: "MARKET_VARIABLE",
              },
            },
            key: "Currency_Restriction",
          },
          Currency_Restriction: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "Currency_Restriction",
            isAuxiliary: false,
            formula: "[0] in ([1])",
            formulaRaw: "Currency IN (:CURRENCY)",
            elements: {
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Currency",
              },
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
                key: "CURRENCY",
              },
            },
            key: "Res_Measure1",
          },
          Sales_Curr__Qty: {
            measureType: AnalyticModelMeasureType.CalculatedMeasure,
            text: "Sales_Curr / Qty",
            isAuxiliary: false,
            formulaRaw: "Sales_Curr / Quantity",
            formula: "[0]/[1]",
            elements: {
              0: {
                operandType: AnalyticModelCalculatedMeasureOperandType.Element,
                key: "Sales_Curr",
              },
              1: {
                operandType: AnalyticModelCalculatedMeasureOperandType.Element,
                key: "Quantity",
              },
            },
          },
          Sales_Curr: {
            measureType: AnalyticModelMeasureType.CurrencyConversionMeasure,
            text: "Sales_Curr",
            isAuxiliary: false,
            sourceCurrencyType: AnalyticModelSourceCurrencyType.derived,
            key: "Sales",
            client: "200",
            errorHandling: AnalyticModelErrorHandling.fail,
            targetCurrency: {
              value: "USD",
            },
            targetCurrencyType: AnalyticModelTargetCurrencyType.constantValue,
            referenceDate: {
              key: "Order Date",
            },
            referenceDateType: AnalyticModelReferenceDateType.attribute,
            conversionTypeType: AnalyticModelConversionTypeType.constantValue,
            conversionType: {
              value: "M",
            },
          },
          Sales: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Sales",
            key: "Sales",
            isAuxiliary: false,
          },
          Quantity: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Quantity",
            key: "Quantity",
            isAuxiliary: false,
          },
          Discount: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Discount",
            key: "Discount",
            isAuxiliary: false,
          },
          Profit: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Profit",
            key: "Profit",
            isAuxiliary: false,
          },
          "Shipping Cost": {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Shipping Cost",
            key: "Shipping Cost",
            isAuxiliary: false,
          },
          "Unit Price": {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Unit Price",
            key: "Unit Price",
            isAuxiliary: false,
          },
        },
        version: "1.0.6",
        variables: {
          "0CALDAY": {
            text: "0CALDAY",
            parameterType: AnalyticModelParameterType.KeyDate,
            defaultValue: "2023-05-03",
          },
          CURRENCY: {
            text: "currency",
            parameterType: AnalyticModelParameterType.Filter,
            multipleSelections: true,
            selectionType: AnalyticModelVariableSelectionType.MULTIPLE_SINGLE,
            referenceAttribute: "Currency",
            mandatory: true,
            defaultValue: [],
          },
          "Order Date": {
            parameterType: AnalyticModelParameterType.StoryFilter,
            multipleSelections: false,
            selectionType: AnalyticModelVariableSelectionType.RANGE,
            referenceAttribute: "Order Date",
            mandatory: true,
            defaultValue: {
              lowValue: "2000-05-11",
              sign: AnalyticModelDefaultRangeSign.INCLUDE,
              option: AnalyticModelDefaultRangeOption.GE,
            },
          },
          MARKET_VARIABLE: {
            text: "Market_Variable",
            parameterType: AnalyticModelParameterType.Filter,
            multipleSelections: false,
            selectionType: AnalyticModelVariableSelectionType.RANGE,
            referenceAttribute: "Market",
            mandatory: true,
            defaultValue: {
              lowValue: "",
              highValue: "",
              sign: AnalyticModelDefaultRangeSign.INCLUDE,
              option: AnalyticModelDefaultRangeOption.BT,
            },
          },
        },
        supportedCapabilities: {},
      };
      const cycles = QueryModelValidator.detectCyclesInModel(analyticModel);
      expect(cycles.length).be.equal(2);
      assert.deepStrictEqual(cycles, [
        {
          path: ["Res_Measure1", "Currency_Restriction", "Res_Measure1"],
        },
        {
          path: ["Currency_Restriction", "Res_Measure1", "Currency_Restriction"],
        },
      ]);
    });
  });

  context("General Checks", () => {
    // it.only("Recreate Master / Customer Issue", async () => {
    //   const sourceModelsByName: any = ;
    //   const analyticModel: any = ;

    //   const messages = await QueryModelValidator.validate(analyticModel, sourceModelsByName);
    //   debugger;
    //   expect(messages.filter((msg) => msg.type === ValidationMessageType.ERROR)).to.have.lengthOf(0);
    // });

    it("Error - #1 - should return an error, if an attribute and measure name clash", async () => {
      const analyticModel: IAnalyticModel = {
        identifier: { key: "SalesOrder_AM_W2301" },
        text: "SalesOrder_AM_W2301",
        sourceModel: {
          factSources: { 0: { text: "JoinViewAssociation", dataEntity: { key: "JoinViewAssociation" } } },
          dimensionSources: {
            0: {
              text: "S_ID",
              dataEntity: { key: "JoinView_2" },
              associationContexts: [
                { sourceKey: "0", sourceType: AnalyticModelSourceType.Fact, associationSteps: ["_JoinView_"] },
              ],
            },
          },
        },
        exposedAssociations: {},
        attributes: {
          S_ID: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: { 0: { key: "S_ID" } },
            text: "S_ID",
          },
          E_ID: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: { 0: { key: "E_ID" } },
            text: "E_ID",
          },
          E_ID1: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "0",
            key: "E_ID",
            text: "E_ID",
          },
          NAME: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "0",
            key: "NAME",
            text: "NAME",
          },
          M_ID: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "0",
            key: "M_ID",
            text: "M_ID",
          },
          S_ID1: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "0",
            key: "S_ID",
            text: "S_ID",
          },
          AMOUNT: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "0",
            key: "AMOUNT",
            text: "Amount, but not a measure",
          },
        },
        measures: {
          AMOUNT: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Amount, a measure",
            key: "AMOUNT",
            isAuxiliary: false,
          },
        },
        version: "1.0.0",
        variables: {},
        supportedCapabilities: {},
      };

      const messages = await QueryModelValidator.validate(analyticModel, createCdsFormulaToExprConverter);
      expect(messages).to.have.lengthOf(1);
      expect(messages[0].messageKey).to.equal("validationMessageAttributeMeasureTechnicalName");
      expect(messages[0].type).to.equal(ValidationMessageType.ERROR);
    });

    it("Error - #2 - should return an error, if a mapping field of an assoc. is also an attribute", async () => {
      const messages = await QueryModelValidator.validate(
        validateDimMapAttributeModel,
        createCdsFormulaToExprConverter,
        validateMapAttributeFullCsn
      );
      expect(messages).to.have.lengthOf(1);
      expect(messages[0].messageKey).to.equal("validationMessageAssocTargetIsAttribute");
      expect(messages[0].type).to.equal(ValidationMessageType.ERROR);
      expect(messages[0].propertyPath).to.equal(undefined);
    });

    it("Error - #3 - should return errors if restricted measures & filters has invalid expression", async () => {
      const analyticModel: IAnalyticModel = {
        identifier: {
          key: "ALEX_AM_WRONG_RESTRICTED_MEASURE",
        },
        text: "ALEX_AM_WRONG_RESTRICTED_MEASURE",
        sourceModel: {
          factSources: {
            0: {
              text: "ALEX_ADS_W_SF",
              dataEntity: {
                key: "ALEX_ADS_W_SF",
              },
            },
          },
          dimensionSources: {},
        },
        exposedAssociations: {},
        attributes: {
          ItemID: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ItemID",
              },
            },
            text: "Item ID",
          },
          Opportunity: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Opportunity",
              },
            },
            text: "Opportunity ID",
          },
          Product: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Product",
              },
            },
            text: "Product ID",
          },
          Unit: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Unit",
              },
            },
            text: "Unit",
          },
          Currency: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Currency",
              },
            },
            text: "Currency",
          },
          FactAttr: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "FactAttr",
              },
            },
            text: "FactAttr",
          },
          ItemStatus: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ItemStatus",
              },
            },
            text: "ItemStatus ID",
          },
          ControllingArea: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ControllingArea",
              },
            },
            text: "Controlling Area Fact",
          },
          SendingCostCenter: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              "0": {
                key: "SendingCostCenter",
              },
            },
            text: "Sending Cost Center Fact",
          },
          ReceivingCostCenter: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ReceivingCostCenter",
              },
            },
            text: "Receiving Cost Center Fact",
          },
        },
        measures: {
          RestrictedMeasure: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "RestrictedMeasure",
            isAuxiliary: false,
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "ItemID",
            key: "Quantity",
          },
          RestrictedMeasure2: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "RestrictedMeasure",
            isAuxiliary: false,
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Itemid=4711",
            key: "Quantity",
          },
          Quantity: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Quantity",
            key: "Quantity",
            isAuxiliary: false,
          },
          Value: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Value",
            key: "Value",
            isAuxiliary: false,
          },
          CalculatedMeasure: {
            measureType: AnalyticModelMeasureType.CalculatedMeasure,
            text: "RestrictedMeasure",
            isAuxiliary: false,
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Value /",
          },
        },
        version: "1.0.2",
        variables: {
          Product: {
            selectionType: AnalyticModelVariableSelectionType.RANGE,
            multipleSelections: false,
            parameterType: AnalyticModelParameterType.StoryFilter,
            referenceAttribute: "Product",
            defaultValue: {
              lowValue: "",
              highValue: "",
              sign: AnalyticModelDefaultRangeSign.INCLUDE,
              option: AnalyticModelDefaultRangeOption.BT,
            },
            mandatory: false,
          },
        },
        filter: {
          text: "global filter",
          formulaRaw: "Itemid=4711",
          formula: "",
          elements: [],
        },
        supportedCapabilities: {},
      };
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        fullCsnDataLayerObjects3.definitions,
        isFeatureFlagActive
      );
      expect(messages).to.have.lengthOf(4);
      expect(messages[0].descriptionKey).to.equal("measureExpressionDimensionNotFound");
      expect(messages[0].type).to.equal(ValidationMessageType.ERROR);
      expect(messages[0].propertyPath).to.equal("/filter/expression");
      expect(messages[1].descriptionKey).to.equal("validationDescriptionRestrictedInvalidExpression");
      expect(messages[1].type).to.equal(ValidationMessageType.ERROR);
      expect(messages[1].propertyPath).to.equal("/measures/RestrictedMeasure/expression");
      expect(messages[2].descriptionKey).to.equal("measureExpressionDimensionNotFound");
      expect(messages[2].type).to.equal(ValidationMessageType.ERROR);
      expect(messages[2].propertyPath).to.equal("/measures/RestrictedMeasure2/expression");
      expect(messages[3].descriptionKey).to.equal("validationDescriptionBackendGeneric");
      expect(messages[3].type).to.equal(ValidationMessageType.ERROR);
      expect(messages[3].propertyPath).to.equal("/measures/CalculatedMeasure/expression");
    });

    it("Error - #4 - should return error if count distinct measure measure contains invalid attribute", async () => {
      const analyticModel: IAnalyticModel = {
        identifier: {
          key: "ALEX_AM_WRONG_COUNT_DISTINCT_MEASURE",
        },
        text: "ALEX_AM_WRONG_COUNT_DISTINCT_MEASURE",
        sourceModel: {
          factSources: {
            0: {
              text: "ALEX_ADS_W_SF",
              dataEntity: {
                key: "ALEX_ADS_W_SF",
              },
            },
          },
          dimensionSources: {},
        },
        exposedAssociations: {},
        attributes: {
          ItemID: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ItemID",
              },
            },
            text: "Item ID",
          },
          Opportunity: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Opportunity",
              },
            },
            text: "Opportunity ID",
          },
          Product: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Product",
              },
            },
            text: "Product ID",
          },
          Unit: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Unit",
              },
            },
            text: "Unit",
          },
          Currency: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Currency",
              },
            },
            text: "Currency",
          },
          FactAttr: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "FactAttr",
              },
            },
            text: "FactAttr",
          },
          ItemStatus: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ItemStatus",
              },
            },
            text: "ItemStatus ID",
          },
          ControllingArea: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ControllingArea",
              },
            },
            text: "Controlling Area Fact",
          },
          SendingCostCenter: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              "0": {
                key: "SendingCostCenter",
              },
            },
            text: "Sending Cost Center Fact",
          },
          ReceivingCostCenter: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ReceivingCostCenter",
              },
            },
            text: "Receiving Cost Center Fact",
          },
        },
        measures: {
          CountDistinct: {
            measureType: AnalyticModelMeasureType.CountDistinct,
            text: "CountDistinctMeasure",
            isAuxiliary: false,
            aggregation: AnalyticModelAggregationTypes.SUM,
            countDistinctAttributes: ["ItemID", "Opportunity_1", "Product", "Item_Status"],
          },
          Quantity: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Quantity",
            key: "Quantity",
            isAuxiliary: false,
          },
          Value: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Value",
            key: "Value",
            isAuxiliary: false,
          },
        },
        version: "1.0.2",
        variables: {
          Product: {
            selectionType: AnalyticModelVariableSelectionType.RANGE,
            multipleSelections: false,
            parameterType: AnalyticModelParameterType.StoryFilter,
            referenceAttribute: "Product",
            defaultValue: {
              lowValue: "",
              highValue: "",
              sign: AnalyticModelDefaultRangeSign.INCLUDE,
              option: AnalyticModelDefaultRangeOption.BT,
            },
            mandatory: false,
          },
        },
        supportedCapabilities: {},
      };

      const messages = await QueryModelValidator.validate(analyticModel, createCdsFormulaToExprConverter);
      expect(messages).to.have.lengthOf(2);
      expect(messages[0].messageKey).to.equal("validationMessageCountDistinctInvalidAttribute");
      expect(messages[0].type).to.equal(ValidationMessageType.ERROR);
      assert.deepStrictEqual(messages[0].parameters, ["CountDistinct", "Opportunity_1"]);

      expect(messages[1].messageKey).to.equal("validationMessageCountDistinctInvalidAttribute");
      expect(messages[1].type).to.equal(ValidationMessageType.ERROR);
      assert.deepStrictEqual(messages[1].parameters, ["CountDistinct", "Item_Status"]);
    });

    it("Error - #5 - should return error if leveled hierarchy contains invalid attribute", async () => {
      const messages = await QueryModelValidator.validate(
        ALEX_WRONG_HIER_TEST_AM,
        createCdsFormulaToExprConverter,
        ALEX_WRONG_HIER_TEST_CSNS.definitions
      );
      const errorMessages = messages.filter((message) => message.type === ValidationMessageType.ERROR);
      expect(errorMessages).to.have.lengthOf(1);
      expect(errorMessages[0].messageKey).to.equal("validationMessageAttributeMissingInDimensionSource");
      expect(errorMessages[0].type).to.equal(ValidationMessageType.ERROR);
      assert.deepStrictEqual(errorMessages[0].parameters, ["YEAR", "MCT_Products"]);
    });

    it("Error - #6 - should return error if mapping target is no key", async () => {
      // Modify mock to match test - remove key indicator from mapping target field
      const mockSourceModelsByNameError6 = _.cloneDeep(sourceModelsByNameError6);
      delete mockSourceModelsByNameError6.MCT_ProfitCenter.elements.ProfitCenter.key;

      // Validate
      const messages = await QueryModelValidator.validate(
        amError6,
        createCdsFormulaToExprConverter,
        mockSourceModelsByNameError6
      );
      const errorMessages = messages.filter((message) => message.type === ValidationMessageType.ERROR);
      expect(errorMessages).to.have.length.greaterThan(0);
      expect(errorMessages[0].messageKey).to.equal("validationMessageCompoundAssocFieldNotKey");
      expect(errorMessages[0].type).to.equal(ValidationMessageType.ERROR);
    });

    it("Error - #7 - should return error if mapping dimension (attribute) is not selected in the UI (=> not part of the AM definition)", async () => {
      // Modify mock to match test - remove mapping source fields from attributes list
      const mockFS = _.cloneDeep(amError6);
      const mockDS = _.cloneDeep(amError6);
      delete mockFS.attributes!.ControllingArea;
      delete mockDS.attributes!.ParentCostCenter;

      // Validate FactSources
      const messagesFS = await QueryModelValidator.validate(
        mockFS,
        createCdsFormulaToExprConverter,
        sourceModelsByNameError6
      );
      const errorMessagesFS = messagesFS.filter((message) => message.type === ValidationMessageType.ERROR);
      expect(errorMessagesFS).to.have.lengthOf(1);
      expect(errorMessagesFS[0].messageKey).to.equal("validationMessageAssocSourceFieldNotSelected");
      expect(errorMessagesFS[0].type).to.equal(ValidationMessageType.ERROR);
      expect(errorMessagesFS[0].parameters).to.eql(["ControllingArea", "MCT_OpportunityItems"]);
      expect(errorMessagesFS[0].propertyPath).to.equal(undefined);

      // Validate DimensionSources
      const messagesDS = await QueryModelValidator.validate(
        mockDS,
        createCdsFormulaToExprConverter,
        sourceModelsByNameError6
      );
      const errorMessagesDS = messagesDS.filter((message) => message.type === ValidationMessageType.ERROR);
      expect(errorMessagesDS).to.have.lengthOf(1);
      expect(errorMessagesDS[0].messageKey).to.equal("validationMessageAssocSourceFieldNotSelected");
      expect(errorMessagesDS[0].type).to.equal(ValidationMessageType.ERROR);
      expect(errorMessagesDS[0].parameters).to.eql(["ParentCostCenter", "MCT_CostCenter"]);
      expect(errorMessagesDS[0].propertyPath).to.equal(undefined);
    });

    it("Error - #8 - should return error(s) if association target doesn't match the dataEntity.key", async () => {
      // Modify mock to match test - remove mapping source fields from attributes list
      const mockAM = _.cloneDeep(amError6);
      mockAM.sourceModel.dimensionSources![1].dataEntity.key = "MCT_ProfitCenter";
      // corresponding association target is MCT_CostCenter

      // Validate
      const messages = await QueryModelValidator.validate(
        mockAM,
        createCdsFormulaToExprConverter,
        sourceModelsByNameError6
      );
      const wrongTargetIndex = messages.findIndex(
        (message) => (message.messageKey ?? "") === "validationMessageAssociationWrongTarget"
      );
      expect(wrongTargetIndex).to.be.greaterThan(-1);
      expect(messages[wrongTargetIndex].type).to.equal(ValidationMessageType.ERROR);
      expect(messages[wrongTargetIndex].parameters).to.eql([
        "MCT_CCSending",
        "MCT_OpportunityItems",
        "MCT_CostCenter",
        "MCT_ProfitCenter",
        "Sending Cost Center",
      ]);
    });

    it("Error - #9 - should return error if parent child hierarchy attribute is hidden", async () => {
      // Modify mock to match test
      const sourceModelsByName = _.cloneDeep(sourceModelsByNameError9);
      sourceModelsByName.MCT_Employees.elements.Manager["@Analytics.hidden"] = true;

      // Validate
      const messages = await QueryModelValidator.validate(
        amError9,
        createCdsFormulaToExprConverter,
        sourceModelsByName
      );
      const errorMessages = messages.filter((message) => message.type === ValidationMessageType.ERROR);

      expect(errorMessages.length).to.equal(1);
      expect(errorMessages[0].messageKey).to.equal("validationMessageParentChildHierarchyAttributeHidden");
      expect(errorMessages[0].type).to.equal(ValidationMessageType.ERROR);
      expect(errorMessages[0].parameters).to.eql([
        "Product Manager",
        "Manager",
        "Organizational Hierarchy",
        "MCT_Employees",
      ]);
    });

    it("Error - #10 - should return error if leveled hierarchy attribute is hidden", async () => {
      // Modify mock to match test - Hide attribute ProductGroup of MCT_Products
      const sourceModelsByName = _.cloneDeep(sourceModelsByNameError9);
      sourceModelsByName.MCT_Products.elements.ProductGroup["@Analytics.hidden"] = true;

      // Validate
      const messages = await QueryModelValidator.validate(
        amError9,
        createCdsFormulaToExprConverter,
        sourceModelsByName
      );

      const errorMessages = messages.filter((message) => message.type === ValidationMessageType.ERROR);
      expect(errorMessages.length).to.equal(1);
      expect(errorMessages[0].messageKey).to.equal("validationMessageLeveledHierarchyAttributeHidden");
      expect(errorMessages[0].type).to.equal(ValidationMessageType.ERROR);
      expect(errorMessages[0].parameters).to.eql(["Product ID", "ProductGroup", "Prod_Group", "MCT_Products"]);
    });

    it("Error - #11 - should return error if text of dimension key is hidden", async () => {
      // Modify mock to match test - Hide text element (Name) of MCT_Products
      const sourceModelsByName = _.cloneDeep(sourceModelsByNameError9);
      sourceModelsByName.MCT_Products.elements.Name["@Analytics.hidden"] = true;

      // Validate
      const messages = await QueryModelValidator.validate(
        amError9,
        createCdsFormulaToExprConverter,
        sourceModelsByName
      );

      const indexHiddenTextForKey = messages.findIndex(
        (message) => (message.messageKey ?? "") === "validationMessageTextElementHiddenForKey"
      );
      expect(indexHiddenTextForKey).to.be.greaterThan(-1);
      expect(messages[indexHiddenTextForKey].type).to.equal(ValidationMessageType.ERROR);
      expect(messages[indexHiddenTextForKey].parameters).to.eql(["MCT_Products", "Name", "Product ID"]);
    });

    it("Error - #12 - should return error if text of dimension attribute is hidden", async () => {
      // Modify mock to match test - Hide text element (Name) of MCT_Products
      const sourceModelsByName = _.cloneDeep(sourceModelsByNameError9);
      sourceModelsByName.MCT_OpportunityItems.elements.FactAttrLabel["@Analytics.hidden"] = true;

      // Validate
      const messages = await QueryModelValidator.validate(
        amError9,
        createCdsFormulaToExprConverter,
        sourceModelsByName
      );

      const indexHiddenTextForKey = messages.findIndex(
        (message) => (message.messageKey ?? "") === "validationMessageTextElementHiddenForAttribute"
      );
      expect(indexHiddenTextForKey).to.be.greaterThan(-1);
      expect(messages[indexHiddenTextForKey].type).to.equal(ValidationMessageType.ERROR);
      expect(messages[indexHiddenTextForKey].parameters).to.eql([
        "FactAttr",
        "MCT_OpportunityItems",
        "FactAttrLabel",
        "FactAttr",
      ]);
    });

    it("Error - #13 - should return error if key of dimension is hidden", async () => {
      // Modify mock to match test - Hide text element (Name) of MCT_Products
      const sourceModelsByName = _.cloneDeep(sourceModelsByNameError9);
      sourceModelsByName.MCT_Products.elements.ID["@Analytics.hidden"] = true;

      // Validate
      const messages = await QueryModelValidator.validate(
        amError9,
        createCdsFormulaToExprConverter,
        sourceModelsByName
      );

      const indexHiddenTextForKey = messages.findIndex(
        (message) => (message.messageKey ?? "") === "validationMessageKeyHidden"
      );
      expect(indexHiddenTextForKey).to.be.greaterThan(-1);
      expect(messages[indexHiddenTextForKey].type).to.equal(ValidationMessageType.ERROR);
      expect(messages[indexHiddenTextForKey].parameters).to.eql(["MCT_Products", "ID", "Product ID", "Id"]);
    });

    it("Error - #14 - should return error if leveled hierarchy is empty", async () => {
      // Modify mock to match test - Clear hierarchy of MCT_Products
      const sourceModelsByName = _.cloneDeep(sourceModelsByNameError9);
      sourceModelsByName.MCT_Products["@Hierarchy.leveled"][0].levels = [];

      // Validate
      const messages = await QueryModelValidator.validate(
        amError9,
        createCdsFormulaToExprConverter,
        sourceModelsByName
      );
      const errorMessages = messages.filter((message) => message.type === ValidationMessageType.ERROR);

      expect(errorMessages.length).to.equal(1);
      expect(errorMessages[0].messageKey).to.equal("validationMessageLeveledHierarchyNoLevels");
      expect(errorMessages[0].type).to.equal(ValidationMessageType.ERROR);
      expect(errorMessages[0].parameters).to.eql(["Product ID", "Prod_Group", "MCT_Products"]);
    });

    it("Error - #15 - should return error if parent/child hierarchy is empty", async () => {
      // Modify mock to match test - Clear hierarchy of MCT_Employees
      const sourceModelsByName = _.cloneDeep(sourceModelsByNameError9);
      sourceModelsByName.MCT_Employees["@Hierarchy.parentChild"][0].recurse.parent = [];
      sourceModelsByName.MCT_Employees["@Hierarchy.parentChild"][0].recurse.child = [];

      // Validate
      const messages = await QueryModelValidator.validate(
        amError9,
        createCdsFormulaToExprConverter,
        sourceModelsByName
      );

      const errorMessages = messages.filter((message) => message.type === ValidationMessageType.ERROR);
      expect(errorMessages.length).to.equal(1);
      expect(errorMessages[0].messageKey).to.equal("validationMessageParentChildHierarchyEmpty");
      expect(errorMessages[0].type).to.equal(ValidationMessageType.ERROR);
      expect(errorMessages[0].parameters).to.eql(["Product Manager", "Organizational Hierarchy", "MCT_Employees"]);
    });

    it("Error - #16 - should return error if mapping dimension source attribute is hidden", async () => {
      const sourceModelsByName = _.cloneDeep(sourceModelsByNameError6);
      sourceModelsByName.MCT_OpportunityItems.elements.SendingCostCenter["@Analytics.hidden"] = true;

      const messages = await QueryModelValidator.validate(
        amError6,
        createCdsFormulaToExprConverter,
        sourceModelsByName
      );
      const errorMessages = messages.filter((message) => message.type === ValidationMessageType.ERROR);

      expect(errorMessages).to.have.lengthOf(1);
      expect(errorMessages[0].messageKey).to.equal("validationMessageKeyHiddenInDimensionSource");
      expect(errorMessages[0].type).to.equal(ValidationMessageType.ERROR);
      expect(errorMessages[0].parameters).to.eql([
        "SendingCostCenter",
        "MCT_CCSending",
        "Sending Cost Center",
        "MCT_OpportunityItems",
      ]);
    });

    it("Error - #17 - should return error if parameters have a cyclic dependency", async () => {
      const sourceModelsByName = _.cloneDeep(sourceModelsByNameError17);
      const isFeatureFlagActive: fIsFeatureFlagActive = async (): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        amError17,
        createCdsFormulaToExprConverter,
        sourceModelsByName,
        isFeatureFlagActive
      );
      const errorMessages = messages.filter((message) => message.type === ValidationMessageType.ERROR);

      expect(errorMessages).to.have.lengthOf(2);
      expect(errorMessages[0].messageKey).to.equal("validationMessageParameterCyclicDependency");
      expect(errorMessages[0].parameters).to.eql(["EXCHANGERATETYPEVAR", "EXCHANGERATE"]);
    });

    it("Error - #18 - should return error if parameters are removed from underlying source", async () => {
      const sourceModelsByName = _.cloneDeep(sourceModelsByNameError18);
      const isFeatureFlagActive: fIsFeatureFlagActive = async (): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        amError18,
        createCdsFormulaToExprConverter,
        sourceModelsByName,
        isFeatureFlagActive
      );
      const errorMessages = messages.filter((message) => message.type === ValidationMessageType.ERROR);

      expect(errorMessages).to.have.lengthOf(3);
      assert.deepStrictEqual(errorMessages, [
        {
          message: 'Attribute "Calc_Date" does not exist in fact source "MCT_Opportunity_W_Param_AM"',
          messageKey: "validationMessageAttributeMissingInFactSource",
          descriptionKey: "validationDescriptionAttributeMissingInFactSource",
          type: "Error",
          parameters: ["Calc_Date", "MCT_Opportunity_W_Param_AM"],
          propertyPath: "/attributes/Calc_Date",
          validatorName: "SourcesValidator",
        },
        {
          message: "Input parameter ('SRC_DATE') is missing in the fact source ('MCT_Opportunity_W_Param_AM')",
          messageKey: "validationMessageADSMissingInputParameter",
          descriptionKey: "validationDescriptionADSMissingInputParameter",
          type: "Error",
          parameters: ["SRC_DATE", "MCT_Opportunity_W_Param_AM"],
          propertyPath: "/sourceModel/factSources/0/parameterMappings/SRC_DATE",
          validatorName: "VariableValidator",
        },
        {
          message: "Variable 'SRC_DATE' is not used",
          messageKey: "validationMessageInputParameterMappingMissing",
          descriptionKey: "validationDescriptionInputParameterMappingMissing",
          type: "Error",
          parameters: ["SRC_DATE"],
          propertyPath: "/variables/SRC_DATE",
          validatorName: "VariableValidator",
        },
      ]);
    });

    it("Error - #19 - should return error if compoundKeySequence is present, but not if only representativeKey is", async () => {
      // Error: compoundKeySequence is present
      const sourceModelsByNameError = _.cloneDeep(validationError19FullCsn.definitions) as any;
      const sourceModelsByNameSuccess = _.cloneDeep(validationError19FullCsn.definitions) as any;
      const isFeatureFlagActive: fIsFeatureFlagActive = async (): Promise<boolean> => true;

      sourceModelsByNameError.MCT_Products["@DataWarehouse.compoundKeySequence"] = [{ "=": "thisNameDoesntMatter" }];

      const errorValidationResult = await QueryModelValidator.validate(
        validationError19FullCsn.businessLayerDefinitions.validationError19AM,
        createCdsFormulaToExprConverter,
        sourceModelsByNameError,
        isFeatureFlagActive
      );

      expect(errorValidationResult).to.have.lengthOf(1);

      // Success: only representativeKey is present
      sourceModelsByNameSuccess.MCT_Products["@ObjectModel.representativeKey"] = "ID";

      const successValidationResult = await QueryModelValidator.validate(
        validationError19FullCsn.businessLayerDefinitions.validationError19AM,
        createCdsFormulaToExprConverter,
        sourceModelsByNameSuccess,
        isFeatureFlagActive
      );

      expect(successValidationResult).to.have.lengthOf(0);
    });

    it("Error - #20 - should return error if RKF with exception aggregation on base with AVG", async () => {
      const sourceModelsByName = _.cloneDeep(validationError20FullCsn.definitions) as any;
      const isFeatureFlagActive: fIsFeatureFlagActive = async (): Promise<boolean> => true;

      const errorValidationResult = await QueryModelValidator.validate(
        validationError20FullCsn.businessLayerDefinitions.TEST_AM_DW101_92792 as any,
        createCdsFormulaToExprConverter,
        sourceModelsByName,
        isFeatureFlagActive
      );

      expect(errorValidationResult).to.have.lengthOf(1);
      expect(errorValidationResult[0].messageKey ?? "").to.equal("validationMessageRestrictedExceptionOnBaseWithAVG");
    });

    it("Error - #21 - invalid namespace", async () => {
      // Start with a valid namespace
      const sourceModelsByName = _.cloneDeep(validationError21FullCsn.definitions) as any;
      const model = _.cloneDeep(validationError21FullCsn.businessLayerDefinitions["sap.TEST_AM_WITH_NAMESPACE"]) as any;
      const isFeatureFlagActive: fIsFeatureFlagActive = async (): Promise<boolean> => true;

      const successfulValidationResult = await QueryModelValidator.validate(
        model,
        createCdsFormulaToExprConverter,
        sourceModelsByName,
        isFeatureFlagActive
      );

      expect(successfulValidationResult).to.have.lengthOf(0);

      model.identifier.key = "this.namespace.doesnt.exist.TEST_AM_WITH_NAMESPACE";

      const errorValidationResult = await QueryModelValidator.validate(
        model,
        fCreateXprMock,
        sourceModelsByName,
        isFeatureFlagActive
      );

      expect(errorValidationResult).to.have.lengthOf(1);
      expect(errorValidationResult[0].messageKey ?? "").to.equal("validationMessageNamespaceNotFound");
    });

    it("Error - #22 - invalid namespace, but child namespaces are allowed", async () => {
      // Start with a valid namespace
      const sourceModelsByName = _.cloneDeep(validationError21FullCsn.definitions) as any;
      const model = _.cloneDeep(validationError21FullCsn.businessLayerDefinitions["sap.TEST_AM_WITH_NAMESPACE"]) as any;
      const isFeatureFlagActive: fIsFeatureFlagActive = async (): Promise<boolean> => true;

      sourceModelsByName.this = {
        allowChildNamespaces: false,
      };
      sourceModelsByName["this.namespace"] = {
        allowChildNamespaces: true,
      };

      model.identifier.key = "this.namespace.doesnt.exist.TEST_AM_WITH_NAMESPACE";

      const errorValidationResult = await QueryModelValidator.validate(
        model,
        fCreateXprMock,
        sourceModelsByName,
        isFeatureFlagActive
      );

      expect(errorValidationResult).to.have.lengthOf(0);
    });

    it("Warning - #1 - should return a warning, if a dimension source has no keys", async () => {
      const messages = await QueryModelValidator.validate(
        validateDimKeyMissingModel,
        createCdsFormulaToExprConverter,
        validateDimKeyMissingFullCsn
      );
      expect(messages).to.have.lengthOf(1);
      expect(messages[0].messageKey).to.equal("validationMessageDimensionSourceNoKey");
      expect(messages[0].type).to.equal(ValidationMessageType.WARN);
    });

    it("Warning - #2 - should return a warning, if an attribute has multiple associations", async () => {
      const sourceModelsByName = _.cloneDeep(sourceModelsByNameWarning2);
      const isFeatureFlagActive: fIsFeatureFlagActive = async (): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        amWarning2,
        createCdsFormulaToExprConverter,
        sourceModelsByName,
        isFeatureFlagActive
      );
      const warningMessages = messages.filter((message) => message.type === ValidationMessageType.WARN);

      expect(warningMessages).to.have.lengthOf(1);
      expect(warningMessages[0].messageKey).to.equal("validationMessageMultipleAssociationsPerAttribute");
      expect(warningMessages[0].parameters).to.eql(["Product", "MCT_OpportunityItems"]);
    });
  });

  context("Type checks for expressions", () => {
    it("should return error if a comparison in filter expression has incompatible types", async () => {
      const analyticModel: IAnalyticModel = {
        identifier: {
          key: "ALEX_AM_FOR_VALIDATION_TESTING",
        },
        text: "ALEX_AM_FOR_VALIDATION_TESTING",
        sourceModel: {
          factSources: {
            0: {
              text: "ALEX_OPPORTUNITIES_W_IPS",
              dataEntity: {
                key: "ALEX_OPPORTUNITIES_W_IPS",
              },
              parameterMappings: {
                PARAM_ITEM_STATUS: {
                  mappingType: AnalyticModelSourceParameterMappingType.MapToSourceParameter,
                  variableName: "PARAM_ITEM_STATUS",
                },
              },
            },
          },
          dimensionSources: {
            0: {
              text: "Product ID",
              dataEntity: {
                key: "MCT_Products_2",
              },
              associationContexts: [
                {
                  sourceKey: "0",
                  sourceType: AnalyticModelSourceType.Fact,
                  associationSteps: ["_MCT_Produ"],
                },
              ],
            },
            1: {
              text: "Opportunity ID",
              dataEntity: {
                key: "MCT_Opportunities",
              },
              associationContexts: [
                {
                  sourceKey: "0",
                  sourceType: AnalyticModelSourceType.Fact,
                  associationSteps: ["_MCT_Oppor"],
                },
              ],
            },
            2: {
              text: "Product ID",
              dataEntity: {
                key: "MCT_Products",
              },
              associationContexts: [
                {
                  sourceKey: "0",
                  sourceType: AnalyticModelSourceType.Fact,
                  associationSteps: ["_MCT_Prod1"],
                },
              ],
            },
            3: {
              text: "Receiving Cost Center Fact",
              dataEntity: {
                key: "MCT_CostCenter",
              },
              associationContexts: [
                {
                  sourceKey: "0",
                  sourceType: AnalyticModelSourceType.Fact,
                  associationSteps: ["_MCT_CostC"],
                },
              ],
            },
            4: {
              text: "Sending Cost Center Fact",
              dataEntity: {
                key: "MCT_CostCenter",
              },
              associationContexts: [
                {
                  sourceKey: "0",
                  sourceType: AnalyticModelSourceType.Fact,
                  associationSteps: ["_MCT_Cost1"],
                },
              ],
            },
            5: {
              text: "Controlling Area Fact",
              dataEntity: {
                key: "MCT_ControllingArea",
              },
              associationContexts: [
                {
                  sourceKey: "0",
                  sourceType: AnalyticModelSourceType.Fact,
                  associationSteps: ["_MCT_Contr"],
                },
              ],
            },
          },
        },
        exposedAssociations: {},
        attributes: {
          ItemID: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ItemID",
              },
            },
            text: "Item ID",
          },
          Opportunity: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Opportunity",
              },
            },
            text: "Opportunity ID",
          },
          Product: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Product",
              },
            },
            text: "Product ID",
          },
          Unit: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Unit",
              },
            },
            text: "Unit",
          },
          Currency: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Currency",
              },
            },
            text: "Currency",
          },
          FactAttr: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "FactAttr",
              },
            },
            text: "FactAttr",
          },
          ItemStatus: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ItemStatus",
              },
            },
            text: "ItemStatus ID",
          },
          ControllingArea: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ControllingArea",
              },
            },
            text: "Controlling Area Fact",
          },
          SendingCostCenter: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "SendingCostCenter",
              },
            },
            text: "Sending Cost Center Fact",
          },
          ReceivingCostCenter: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ReceivingCostCenter",
              },
            },
            text: "Receiving Cost Center Fact",
          },
          Color: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "0",
            key: "Color",
            text: "Color",
          },
          SalesOrg: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "1",
            key: "SalesOrg",
            text: "SalesOrg",
          },
          ExpectedClosingDate: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "1",
            key: "ExpectedClosingDate",
            text: "ExpectedClosingDate",
          },
          Status: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "1",
            key: "Status",
            text: "Status",
          },
          Responsible: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "1",
            key: "Responsible",
            text: "Responsible",
          },
          Customer: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "1",
            key: "Customer",
            text: "Customer",
          },
          ProductCategory: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "2",
            key: "ProductCategory",
            text: "ProductCategory",
          },
          ProductGroup: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "2",
            key: "ProductGroup",
            text: "ProductGroup",
          },
          ProductManager: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "2",
            key: "ProductManager",
            text: "Product Manager",
          },
          Manager: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "3",
            key: "Manager",
            text: "Manager",
          },
          ParentControllingArea: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "3",
            key: "ParentControllingArea",
            text: "Parent Controlling Area",
          },
          ParentCostCenter: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "3",
            key: "ParentCostCenter",
            text: "Parent Cost Center",
          },
          Manager1: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "4",
            key: "Manager",
            text: "Manager",
          },
          ParentControllingArea1: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "4",
            key: "ParentControllingArea",
            text: "Parent Controlling Area",
          },
          ParentCostCenter1: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "4",
            key: "ParentCostCenter",
            text: "Parent Cost Center",
          },
          Manager2: {
            attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
            sourceKey: "5",
            key: "Manager",
            text: "Manager",
          },
          BooleanStatus: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "BooleanStatus",
              },
            },
            text: "BooleanStatus",
          },
        },
        measures: {
          VALID_RM_VALUE_ON_ITEM_ID_VAR: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_ITEM_ID_VAR",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "ItemID=:RMV_FOR_ITEM_ID",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
                key: "RMV_FOR_ITEM_ID",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "ItemID",
              },
            },
            key: "Value",
          },
          VALID_RM_VALUE_ON_OPPORTUNITY_ID_VAR: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_OPPORTUNITY_ID_VAR",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Opportunity=:RMV_FOR_OPPORTUNITY_ID",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
                key: "RMV_FOR_OPPORTUNITY_ID",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Opportunity",
              },
            },
            key: "Value",
          },
          VALID_RM_VALUE_ON_PRODUCT_ID_VAR: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_PRODUCT_ID_VAR",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Product=:RMV_FOR_PRODUCT_ID",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
                key: "RMV_FOR_PRODUCT_ID",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Product",
              },
            },
            key: "Value",
          },
          VALID_RM_VALUE_ON_CURRENCY_VAR: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_CURRENCY_VAR",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Currency=:RMV_FOR_CURRENCY",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
                key: "RMV_FOR_CURRENCY",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Currency",
              },
            },
            key: "Value",
          },
          VALID_RM_VALUE_ON_MANAGER_VAR: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_CURRENCY_VAR",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Manager=:RMV_FOR_MANAGER",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
                key: "RMV_FOR_MANAGER",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Manager",
              },
            },
            key: "Value",
          },

          VALID_RM_VALUE_ON_ITEM_ID_VALUE: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_ITEM_ID_VALUE",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "ItemID=4711",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: 4711,
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "ItemID",
              },
            },
            key: "Value",
          },
          VALID_RM_VALUE_ON_OPPORTUNITY_ID_VALUE: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_OPPORTUNITY_ID_VALUE",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Opportunity=4812",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: 4812,
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Opportunity",
              },
            },
            key: "Value",
          },
          VALID_RM_VALUE_ON_PRODUCT_ID_VALUE: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_PRODUCT_ID",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Product='aProduct'",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: "aProduct",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Product",
              },
            },
            key: "Value",
          },
          VALID_RM_VALUE_ON_CURRENCY_VALUE: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_CURRENCY_VALUE",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Currency='EUR'",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: "EUR",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Currency",
              },
            },
            key: "Value",
          },
          VALID_RM_VALUE_ON_MANAGER_VAL: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_MANAGER_VAL",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Manager=400000016",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: 400000016,
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Manager",
              },
            },
            key: "Value",
          },
          VALID_RM_VALUE_ON_BOOLEANSTATUS_VAL: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_BOOLEANSTATUS_VAL",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "BooleanStatus=1",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: 1,
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "BooleanStatus",
              },
            },
            key: "Value",
          },
          VALID_RM_VALUE_ON_MANAGER_VAL_IN: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_MANAGER_VAL_IN",
            isAuxiliary: false,
            formula: "[0] IN [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Manager IN (400000016)",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: 400000016,
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Manager",
              },
            },
            key: "Value",
          },
          VALID_RM_VALUE_ON_MANAGER_VAL_NOT_IN: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_MANAGER_VAL_IN",
            isAuxiliary: false,
            formula: "[0] NOT IN [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Manager NOT IN (400000016)",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: 400000016,
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Manager",
              },
            },
            key: "Value",
          },

          INVALID_RM_VALUE_ON_ITEM_ID_VAR: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "INVALID_RM_VALUE_ON_ITEM_ID_VAR",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "ItemID=:RMV_FOR_CURRENCY",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
                key: "RMV_FOR_CURRENCY",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "ItemID",
              },
            },
            key: "Value",
          },
          INVALID_RM_VALUE_ON_OPPORTUNITY_ID_VAR: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "INVALID_RM_VALUE_ON_OPPORTUNITY_ID_VAR",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Opportunity=:RMV_FOR_ITEM_ID",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
                key: "RMV_FOR_ITEM_ID",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Opportunity",
              },
            },
            key: "Value",
          },
          INVALID_RM_VALUE_ON_PRODUCT_ID_VAR: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "INVALID_RM_VALUE_ON_PRODUCT_ID_VAR",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Product=:RMV_FOR_MANAGER",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
                key: "RMV_FOR_MANAGER",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Product",
              },
            },
            key: "Value",
          },
          INVALID_RM_VALUE_ON_CURRENCY_VAR: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "INVALID_RM_VALUE_ON_CURRENCY_VAR",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Currency=:RMV_FOR_ITEM_ID",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
                key: "RMV_FOR_ITEM_ID",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Currency",
              },
            },
            key: "Value",
          },
          INVALID_RM_VALUE_ON_MANAGER_VAR: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "INVALID_RM_VALUE_ON_CURRENCY_VAR",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Manager=:RMV_FOR_OPPORTUNITY_ID",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
                key: "RMV_FOR_OPPORTUNITY_ID",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Manager",
              },
            },
            key: "Value",
          },

          INVALID_RM_VALUE_ON_ITEM_ID_VALUE: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "INVALID_RM_VALUE_ON_ITEM_ID_VALUE",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "ItemID='4711'",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: "4711",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "ItemID",
              },
            },
            key: "Value",
          },
          INVALID_RM_VALUE_ON_OPPORTUNITY_ID_VALUE: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "INVALID_RM_VALUE_ON_OPPORTUNITY_ID_VALUE",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Opportunity='4812'",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: "4812",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Opportunity",
              },
            },
            key: "Value",
          },
          INVALID_RM_VALUE_ON_PRODUCT_ID_VALUE: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "INVALID_RM_VALUE_ON_PRODUCT_ID",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Product=true",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: true,
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Product",
              },
            },
            key: "Value",
          },
          INVALID_RM_VALUE_ON_CURRENCY_VALUE: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "INVALID_RM_VALUE_ON_CURRENCY_VALUE",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Currency=false",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: false,
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "EUR",
              },
            },
            key: "Value",
          },
          INVALID_RM_VALUE_ON_MANAGER_VAL: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "INVALID_RM_VALUE_ON_MANAGER_VAL",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "Manager='400000016'",
            elements: {
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: "400000016",
              },
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "Manager",
              },
            },
            key: "Value",
          },
          INVALID_RM_VALUE_ON_BOOLEANSTATUS_VAL: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_BOOLEANSTATUS_VAL",
            isAuxiliary: false,
            formula: "[0] = [1]",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "BooleanStatus=false",
            elements: {
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "BooleanStatus",
              },
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: false,
              },
            },
            key: "Value",
          },
          INVALID_RM_VALUE_ON_BOOLEANSTATUS_VAL_IN: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "VALID_RM_VALUE_ON_BOOLEANSTATUS_VAL_IN",
            isAuxiliary: false,
            formula: "[0] IN ([1])",
            aggregation: AnalyticModelAggregationTypes.SUM,
            formulaRaw: "BooleanStatus IN (false)",
            elements: {
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "BooleanStatus",
              },
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: false,
              },
            },
            key: "Value",
          },
          Quantity: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Quantity",
            key: "Quantity",
            isAuxiliary: false,
          },
          Value: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Value",
            key: "Value",
            isAuxiliary: false,
          },
        },
        version: "1.0.2",
        variables: {
          PARAM_ITEM_STATUS: {
            parameterType: AnalyticModelParameterType.Input,
            text: "PARAM_ITEM_STATUS",
          },
          RMV_FOR_ITEM_ID: {
            text: "RMV_FOR_ITEM_ID",
            parameterType: AnalyticModelParameterType.Filter,
            multipleSelections: false,
            selectionType: AnalyticModelVariableSelectionType.SINGLE,
            referenceAttribute: "ItemID",
            mandatory: true,
          },
          RMV_FOR_OPPORTUNITY_ID: {
            text: "RMV FOR OPPORTUNITY ID",
            parameterType: AnalyticModelParameterType.Filter,
            multipleSelections: false,
            selectionType: AnalyticModelVariableSelectionType.SINGLE,
            referenceAttribute: "Opportunity",
            mandatory: true,
            defaultValue: "",
          },
          RMV_FOR_PRODUCT_ID: {
            text: "RMV FOR PRODUCT ID",
            parameterType: AnalyticModelParameterType.Filter,
            multipleSelections: false,
            selectionType: AnalyticModelVariableSelectionType.SINGLE,
            referenceAttribute: "Product",
            mandatory: true,
            defaultValue: "",
          },
          RMV_FOR_CURRENCY: {
            text: "RMV FOR CURRENCY",
            parameterType: AnalyticModelParameterType.Filter,
            multipleSelections: false,
            selectionType: AnalyticModelVariableSelectionType.SINGLE,
            referenceAttribute: "Currency",
            mandatory: true,
            defaultValue: "",
          },
          RMV_FOR_MANAGER: {
            text: "RMV FOR MANAGER",
            parameterType: AnalyticModelParameterType.Filter,
            multipleSelections: false,
            selectionType: AnalyticModelVariableSelectionType.SINGLE,
            referenceAttribute: "Manager",
            mandatory: true,
            defaultValue: "",
          },
        },
        filter: {
          text: "Filter",
          formulaRaw: "Product=4722",
          formula: "[0] = [1]",
          elements: {
            0: {
              operandType: AnalyticModelFilterOperandType.Attribute,
              key: "Product",
            },
            1: {
              operandType: AnalyticModelFilterOperandType.ConstantValue,
              value: 4722,
            },
          },
        },
        supportedCapabilities: {},
      };

      const getCsn: IGetCsnDefinitionFunction = (name: string): ICsnDefinition =>
        fullCsnDataLayerObjects2.definitions[name];

      const filterXpr = analyticModelCdsParseExprSync(analyticModel.filter!.formulaRaw!);

      const checkTypeCompatibility1 = ExprTypeValidator.checkTypeCompatibility(
        analyticModel,
        getCsn,
        MODEL_ELEMENT.FILTER,
        "",
        filterXpr.expr,
        {}
      );

      const comparisonDetails: Map<string, IComparisonDetails[]> = new Map();

      for (const measureKey in analyticModel.measures) {
        const measure = analyticModel.measures[measureKey];
        if (measure.measureType === AnalyticModelMeasureType.RestrictedMeasure) {
          const measureXpr = AnalyticModelCsnConverterV2.createXprFromFormula(measure.formulaRaw!);
          const checkTypeCompatibility = ExprTypeValidator.checkTypeCompatibility(
            analyticModel,
            getCsn,
            MODEL_ELEMENT.RESTRICTED_MEASURE,
            measureKey,
            (measureXpr as ICreateExpressionResult).expr,
            {},
            true
          );
          if (checkTypeCompatibility.length) {
            comparisonDetails.set(measureKey, checkTypeCompatibility);
          }
        }
      }

      expect(checkTypeCompatibility1.length).be.equal(1);
      expect(comparisonDetails.size).be.equal(12);
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        fullCsnDataLayerObjects2.definitions,
        isFeatureFlagActive
      );

      expect(messages.length).be.equal(15);

      const warnings = messages
        .filter((message) => message.type === ValidationMessageType.WARN)
        .filter(
          (message) =>
            message.descriptionKey !== "validationDescriptionWrongSemanticForTextTargetOfDimOrFact" &&
            message.descriptionKey !== "validationDescriptionMultipleAssociationsPerAttribute"
        );
      const errors = messages.filter((message) => message.type === ValidationMessageType.ERROR);

      expect(warnings.length).be.equal(13);
      expect(errors.length).be.equal(1);

      expect(warnings[0].message).be.equal("Filter 'Filter' has no valid expression");
      expect(warnings[0].messageKey).be.equal("validationMessageNoValidFilterConditionIncompatibleTypesFilter");
      expect(warnings[0].type).be.equal(ValidationMessageType.WARN);

      expect(warnings[10].messageKey).be.equal("validationMessageNoValidFilterConditionIncompatibleTypesRKF");
      expect(warnings[10].descriptionKey).be.equal("validationDescriptionNoValidFilterConditionIncompatibleTypesRKF");

      expect(warnings[11].messageKey).be.equal("validationMessageNoValidFilterConditionNotSupportedBooleanConstants");
      expect(warnings[11].descriptionKey).be.equal(
        "validationDescriptionNoValidFilterConditionNotSupportedBooleanConstants"
      );
      expect(warnings[12].messageKey).be.equal("validationMessageNoValidFilterConditionNotSupportedBooleanConstants");
      expect(warnings[12].descriptionKey).be.equal(
        "validationDescriptionNoValidFilterConditionNotSupportedBooleanConstants"
      );
    });

    it("should return error for comparison in restricted measure expression (simple type)", async () => {
      const analyticModel: IAnalyticModel = {
        identifier: {
          key: "ALEX_TEST_VAL_SIMPLETYPE",
        },
        text: "ALEX_TEST_VAL_SIMPLETYPE",
        sourceModel: {
          factSources: {
            "0": {
              text: "PMF Opportunity Items",
              dataEntity: {
                key: "PMF_OpportunityItems",
              },
            },
          },
          dimensionSources: {
            "1": {
              key: "1",
              text: "Receiving Cost Center",
              dataEntity: {
                key: "PMF_CostCenter",
              },
              associationContexts: [
                {
                  sourceKey: "0",
                  sourceType: AnalyticModelSourceType.Fact,
                  associationSteps: ["PMF_CCReceiving"],
                },
              ],
            },
            "2": {
              key: "2",
              text: "Sending Cost Center",
              dataEntity: {
                key: "PMF_CostCenter",
              },
              associationContexts: [
                {
                  sourceKey: "0",
                  sourceType: AnalyticModelSourceType.Fact,
                  associationSteps: ["PMF_CCSending"],
                },
              ],
            },
          },
        },
        exposedAssociations: {},
        attributes: {
          ItemID: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ItemID",
              },
            },
            text: "Item ID",
          },
          Opportunity: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Opportunity",
              },
            },
            text: "Opportunity ID",
          },
          Product: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Product",
              },
            },
            text: "Product ID",
          },
          Unit: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Unit",
              },
            },
            text: "Unit",
          },
          Currency: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "Currency",
              },
            },
            text: "Currency",
          },
          FactAttrLabel: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "FactAttrLabel",
              },
            },
            text: "FactAttrLabel",
          },
          ItemStatus: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ItemStatus",
              },
            },
            text: "ItemStatus ID",
          },
          ControllingArea: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ControllingArea",
              },
            },
            text: "Controlling Area",
          },
          SendingCostCenter: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "SendingCostCenter",
              },
            },
            text: "Sending Cost Center",
          },
          ReceivingCostCenter: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "ReceivingCostCenter",
              },
            },
            text: "Receiving Cost Center",
          },
          TestSimpleType: {
            attributeType: AnalyticModelAttributeType.FactSourceAttribute,
            attributeMapping: {
              0: {
                key: "TestSimpleType",
              },
            },
            text: "TestSimpleType",
          },
        },
        measures: {
          RM_simpleTypeAttaribute: {
            measureType: AnalyticModelMeasureType.RestrictedMeasure,
            text: "RM_simpleTypeAttaribute",
            isAuxiliary: false,
            formula: "[0] = [1]",
            formulaRaw: "TestSimpleType = 4711",
            elements: {
              0: {
                operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
                key: "TestSimpleType",
              },
              1: {
                operandType: AnalyticModelRestrictedMeasureOperandType.ConstantValue,
                value: 4711,
              },
            },
            key: "Quantity",
          },
          Quantity: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Quantity",
            key: "Quantity",
            isAuxiliary: false,
          },
          Value: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "Value",
            key: "Value",
            isAuxiliary: false,
          },
          NoCurrency: {
            measureType: AnalyticModelMeasureType.FactSourceMeasure,
            sourceKey: "0",
            text: "NoCurrency",
            key: "NoCurrency",
            isAuxiliary: false,
          },
        },
        version: "1.0.7",
        variables: {},
        supportedCapabilities: {},
      };

      const isFeatureFlagActive: fIsFeatureFlagActive = async (): Promise<boolean> => true;

      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        fullCsnwithSimpleTypes.definitions,
        isFeatureFlagActive,
        {
          "dwc.Principal": "cds.String",
        }
      );

      expect(messages.length > 0).be.true;

      const filteredMessages = messages.filter(
        (message) => message.descriptionKey === "validationDescriptionNoValidFilterConditionIncompatibleTypesRKF"
      );
      assert.deepStrictEqual(filteredMessages[0], {
        message: "Restricted 'RM_simpleTypeAttaribute' has no valid expression",
        messageKey: "validationMessageNoValidFilterConditionIncompatibleTypesRKF",
        descriptionKey: "validationDescriptionNoValidFilterConditionIncompatibleTypesRKF",
        type: "Warning",
        parameters: ["TestSimpleType", "dwc.Principal", "4711", "number", "RM_simpleTypeAttaribute"],
        propertyPath: "/measures/RM_simpleTypeAttaribute/expression",
        validatorName: "MeasureValidator",
      });
    });

    it("should return errors for invalid expressions", async () => {
      const analyticModel: IAnalyticModel = AM_Invalid_RM_AM.businessLayerDefinitions.AM_Invalid_RM_AM;
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        AM_Invalid_RM_AM.definitions,
        isFeatureFlagActive
      );

      const filteredMessages = messages.filter((message) => message.type === ValidationMessageType.ERROR);
      expect(filteredMessages.length).be.equal(5);
      expect(filteredMessages[0].message).be.equal(
        "Restricted measure 'RM_Constant_Expression' has no valid expression. Operator '+' not allowed"
      );
      expect(filteredMessages[1].message).be.equal(
        "Restricted measure 'RM_Wrong_Operand_Order' has no valid expression. Left side operand PR-1001 of = must be a dimension"
      );
      expect(filteredMessages[2].message).be.equal(
        "Restricted measure 'RM_Wrong_Operand_Order_NOT' has no valid expression. Left side operand PR-1001 of = must be a dimension"
      );
      expect(filteredMessages[3].message).be.equal(
        "Restricted measure 'RM_Wrong_Operand_Order_NOT_AND' has no valid expression. Left side operand PR-1001 of <= must be a dimension"
      );
      expect(filteredMessages[4].message).be.equal(
        "Restricted measure 'RM_Wrong_Operand_ISNULL' has no valid expression. Left side operand PR-1001 of is must be a dimension"
      );
    });

    it("should return errors for invalid expressions", async () => {
      const analyticModel: IAnalyticModel = AM_Invalid_RM_AM_2.businessLayerDefinitions.AM_Invalid_RM_AM_2;
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        AM_Invalid_RM_AM_2.definitions,
        isFeatureFlagActive
      );

      expect(messages.length).be.equal(3);
      expect(messages[0].message).be.equal(
        "Restricted measure 'RestrictedMeasure_IN' has no valid expression. Right side operand ID of in must not be a dimension"
      );
      expect(messages[1].message).be.equal(
        "Restricted measure 'RestrictedMeasure_BETWEEN' has no valid expression. Right side operand Opportunity of between must not be a dimension"
      );
      expect(messages[2].message).be.equal(
        "Restricted measure 'RestrictedMeasure_EQ' has no valid expression. Right side operand Opportunity of = must not be a dimension"
      );
    });

    it("should return errors for restricted measure using = null or != null", async () => {
      const analyticModel = structuredClone(analyticModelWithRestrictedMeasureEqualsNull) as IAnalyticModel;
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messagesEqualsNull = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        DATA_LAYER_ENTITIES.definitions,
        isFeatureFlagActive
      );
      expect(messagesEqualsNull.length).be.equal(1);
      expect(messagesEqualsNull[0]).to.deep.equal({
        message:
          "Restricted measure 'RestrictedMeasure' has no valid expression. Operator '=' does not support NULL as right side operand.",
        messageKey: "validationMessageRestrictedMeasureEqualOrNotEqualsNull",
        descriptionKey: "validationDescriptionRestrictedMeasureEqualsNull",
        type: ValidationMessageType.ERROR,
        parameters: ["RestrictedMeasure", "="],
        propertyPath: "/measures/RestrictedMeasure/expression",
        validatorName: "MeasureValidator",
      });

      (
        analyticModel.measures!.RestrictedMeasure as IAnalyticModelRestrictedMeasure
      ).formulaRaw = `"ControllingArea∞D2" != NULL`;
      const messagesNotEqualsNull = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        DATA_LAYER_ENTITIES.definitions,
        isFeatureFlagActive
      );
      expect(messagesNotEqualsNull.length).be.equal(1);
      expect(messagesNotEqualsNull[0]).to.deep.equal({
        message:
          "Restricted measure 'RestrictedMeasure' has no valid expression. Operator '!=' does not support NULL as right side operand.",
        messageKey: "validationMessageRestrictedMeasureEqualOrNotEqualsNull",
        descriptionKey: "validationDescriptionRestrictedMeasureNotEqualsNull",
        type: ValidationMessageType.ERROR,
        parameters: ["RestrictedMeasure", "!="],
        propertyPath: "/measures/RestrictedMeasure/expression",
        validatorName: "MeasureValidator",
      });
    });

    it("should return warnings for invalid type in time *_between funcs", async () => {
      const analyticModel: IAnalyticModel = AM_wrong_type_params_time_between_funcs;
      const getCsn: IGetCsnDefinitionFunction = (name: string): ICsnDefinition =>
        AM_validFilterExpression.definitions[name];
      const getParameterIssues = (measureKey: string): IFuncParameterDetails[] => {
        const measure = analyticModel.measures?.[measureKey] as IAnalyticModelCalculatedMeasure;
        const measureXpr = analyticModelCdsParseExprSync(measure.formulaRaw!);
        const checkTypeCompatibility1 = ExprTypeValidator.checkFuncParameterTypeCompatibility(
          analyticModel,
          getCsn,
          MODEL_ELEMENT.CALCULATED_MEASURE,
          measureKey,
          measureXpr.expr
        );
        return checkTypeCompatibility1;
      };

      expect(getParameterIssues("DAYS_BETWEEN_MDS_1").length).equal(0);
      const warn1 = getParameterIssues("DAYS_BETWEEN_MDS_2");
      expect(warn1.length).equal(1);
      assert.deepStrictEqual(warn1, [
        {
          errorI18n: {
            descriptionId: "validationDescriptionCalculatedInvalidParameter",
            messageId: "validationMessageCalculatedInvalidParameter",
            params: ["DAYS_BETWEEN_MDS_2", "2", "DAYS_BETWEEN"],
            error: 'Parameter "no 2" of used function "DAYS_BETWEEN" has a wrong type',
          },
        },
      ]);
      const warn2 = getParameterIssues("MonthsBetween");
      expect(warn2.length).equal(1);
      assert.deepStrictEqual(warn2, [
        {
          errorI18n: {
            descriptionId: "validationDescriptionCalculatedInvalidParameter",
            messageId: "validationMessageCalculatedInvalidParameter",
            params: ["MonthsBetween", "1", "MONTHS_BETWEEN"],
            error: 'Parameter "no 1" of used function "MONTHS_BETWEEN" has a wrong type',
          },
        },
      ]);
      const warn3 = getParameterIssues("SecondsBetween");
      expect(warn3.length).equal(2);
      assert.deepStrictEqual(warn3, [
        {
          errorI18n: {
            descriptionId: "validationDescriptionCalculatedInvalidParameter",
            messageId: "validationMessageCalculatedInvalidParameter",
            params: ["SecondsBetween", "1", "SECONDS_BETWEEN"],
            error: 'Parameter "no 1" of used function "SECONDS_BETWEEN" has a wrong type',
          },
        },
        {
          errorI18n: {
            descriptionId: "validationDescriptionCalculatedInvalidParameter",
            messageId: "validationMessageCalculatedInvalidParameter",
            params: ["SecondsBetween", "2", "SECONDS_BETWEEN"],
            error: 'Parameter "no 2" of used function "SECONDS_BETWEEN" has a wrong type',
          },
        },
      ]);
      const warn4 = getParameterIssues("YEARS_BETWEEN");
      expect(warn4.length).equal(1);
      assert.deepStrictEqual(warn4, [
        {
          errorI18n: {
            descriptionId: "validationDescriptionCalculatedInvalidParameter",
            messageId: "validationMessageCalculatedInvalidParameter",
            params: ["YEARS_BETWEEN", "1", "YEARS_BETWEEN"],
            error: 'Parameter "no 1" of used function "YEARS_BETWEEN" has a wrong type',
          },
        },
      ]);
    });

    it("should return no warning for valid filter expressions", async () => {
      const analyticModel: IAnalyticModel = AM_validFilterExpression.businessLayerDefinitions.D029388_VALIDATION_ISSUES;
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        AM_validFilterExpression.definitions,
        isFeatureFlagActive
      );

      const filteredMessages = messages.filter((message) => message.type === ValidationMessageType.ERROR);
      expect(filteredMessages.length).be.equal(0);
    });

    it("should return a warning for RM with constant selection for a dim and global filter", async () => {
      const analyticModel: IAnalyticModel = AM_confliction_constant_selection_selected_attr;
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        AM_Invalid_RM_AM_2.definitions,
        isFeatureFlagActive
      );

      expect(messages.length).not.equal(0);
      expect(messages[0].descriptionKey).be.equal("validationDescriptionRestrictedConstantSelectionGlobalFilter");
      expect(messages[0].messageKey).be.equal("validationMessageRestrictedConstantSelectionGlobalFilter");
      assert.deepStrictEqual(messages[0].parameters, ["RestrictedMeasure", "Product"]);
      expect(messages[0].type).be.equal(ValidationMessageType.WARN);
    });

    it("should return a warning for RM with constant selection for all dims and global filter", async () => {
      const analyticModel: IAnalyticModel = AM_confliction_constant_selection_all_attr;
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        AM_Invalid_RM_AM_2.definitions,
        isFeatureFlagActive
      );

      expect(messages.length).not.equal(0);
      expect(messages[0].descriptionKey).be.equal("validationDescriptionRestrictedConstantSelectionGlobalFilter");
      expect(messages[0].messageKey).be.equal("validationMessageRestrictedConstantSelectionAllGlobalFilter");
      assert.deepStrictEqual(messages[0].parameters, ["RestrictedMeasure"]);
      expect(messages[0].type).be.equal(ValidationMessageType.WARN);
    });

    it("DW15-3988 part 1 should return a warning if NOT text association has to refer to text entity", async () => {
      const hybridParserParameter: HybridParserParameter = {
        hybridCsn: E2E_CURRENCY_CONVERSION,
        remoteConnection: "W4D",
        context: null as any,
        bw4Import: true,
        spaceId: "TEST_SPACE",
        renameDataLayerObjects: false,
        dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
        featureFlags: {} as any,
      };
      const hybridParser = HybridParserFactory.createHybridParser(hybridParserParameter);
      sandbox.stub(hybridParser, "checkOldConnectionNameWithSpacePrefix").callsFake(async () => false);
      const parseResult: IParseResult = await hybridParser.parse();
      assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
      const validate = hybridParser.validateOutputCsn();
      assert.equal(validate.hasError, false);

      const definitions = hybridParser.getFullCsn().definitions;
      const queryDefinition = definitions["Remote.W4D.E2E_CURRENCY_CONVERSION"];
      expect(queryDefinition).not.be.undefined;

      const cubeModel = new QueryModelBuilder("Remote.W4D.E2E_CURRENCY_CONVERSION", queryDefinition);
      const result = cubeModel.createCubeModel();

      expect(result.hasError).be.false;
      expect(result.cubeModel).not.be.undefined;
      expect(result.cubeModel.filter).be.undefined;

      const analyticModel: IAnalyticModel = result.cubeModel;
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        definitions,
        isFeatureFlagActive
      );

      expect(messages.length).equal(2);

      expect(messages[0].descriptionKey).be.equal("validationDescriptionWrongSemanticForTextTargetOfDimOrFact");
      expect(messages[0].messageKey).be.equal("validationMessageWrongSemanticForTextTargetOfFact");
      assert.deepStrictEqual(messages[0].parameters, [
        "0VC_REG",
        "Remote.W4D.0BWVC_C38",
        "Remote.W4D.0VC_REG",
        "_0VC_REG",
      ]);
      expect(messages[0].type).be.equal(ValidationMessageType.WARN);

      expect(messages[1].descriptionKey).be.equal("validationDescriptionWrongSemanticForTextTargetOfDimOrFact");
      expect(messages[1].messageKey).be.equal("validationMessageWrongSemanticForTextTargetOfFact");
      assert.deepStrictEqual(messages[1].parameters, [
        "40BWVC_C38-0VC_REG01",
        "Remote.W4D.0BWVC_C38",
        "Remote.W4D.40BWVC_C38_0VC_REG01",
        "_40BWVC_C38-0VC_REG01",
      ]);
      expect(messages[1].type).be.equal(ValidationMessageType.WARN);
    });

    it("DW15-3988 part 2 should return a warning if dim or fact has conflicting settings for text resolution", async () => {
      const hybridParserParameter: HybridParserParameter = {
        hybridCsn: E2E_CURRENCY_CONVERSION_2,
        remoteConnection: "W4D",
        context: null as any,
        bw4Import: true,
        spaceId: "TEST_SPACE",
        renameDataLayerObjects: false,
        dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
        featureFlags: {} as any,
      };
      const hybridParser = HybridParserFactory.createHybridParser(hybridParserParameter);
      sandbox.stub(hybridParser, "checkOldConnectionNameWithSpacePrefix").callsFake(async () => false);
      const parseResult: IParseResult = await hybridParser.parse();
      assert.equal(parseResult.hasError, false, JSON.stringify(parseResult.messages));
      const validate = hybridParser.validateOutputCsn();
      assert.equal(validate.hasError, false);

      const definitions = hybridParser.getFullCsn().definitions;
      const queryDefinition = definitions["Remote.W4D.E2E_CURRENCY_CONVERSION"];
      expect(queryDefinition).not.be.undefined;

      const cubeModel = new QueryModelBuilder("Remote.W4D.E2E_CURRENCY_CONVERSION", queryDefinition);
      const result = cubeModel.createCubeModel();

      expect(result.hasError).be.false;
      expect(result.cubeModel).not.be.undefined;
      expect(result.cubeModel.filter).be.undefined;

      const analyticModel: IAnalyticModel = result.cubeModel;
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        definitions,
        isFeatureFlagActive
      );

      expect(messages.length).equal(1);

      expect(messages[0].descriptionKey).be.equal("validationDescriptionConflictingTextResolvingSetting");
      expect(messages[0].messageKey).be.equal("validationMessageWrongSemanticForTextTargetOfFact");
      assert.deepStrictEqual(messages[0].parameters, ["0VC_REG", "Remote.W4D.0BWVC_C38"]);
      expect(messages[0].type).be.equal(ValidationMessageType.WARN);
    });
  });

  context("Data type check for variables and parameters", () => {
    function parseFormulaValidator(analyticModel: any) {
      return (measureKey: string, convertFormulaResult: IConvertFormulaResult): ValidationMessage[] => {
        const messages = QueryModelValidator.validateParsedFormula(measureKey, analyticModel, convertFormulaResult);
        return messages;
      };
    }
    // used amCsnMockNo21 as mock for these scenarios
    it("#1 should return Error for invalid variable dataType that is mapped to Input parameter", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo21.businessLayerDefinitions.TEST_NO_21);
      analyticModel.variables = {
        VALUEPARA1: {
          parameterType: AnalyticModelParameterType.Input,
          text: "ValueParam_1",
          defaultValue: 113,
          dataType: {
            type: "cds.Integer",
          },
        },
      } as any;
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        amCsnMockNo21.definitions,
        isFeatureFlagActive
      );

      expect(messages.length).equal(1);

      assert.deepStrictEqual(messages[0], {
        message: "Datatypes of mapped parameters for input parameter 'VALUEPARA1' do not match",
        messageKey: "validationMessageSourceVariableTypeMismatch",
        descriptionKey: "validationDescriptionSourceVariableTypeMismatch",
        type: "Error",
        parameters: [
          "VALUEPARA1",
          "Integer",
          "factSource",
          "MCT_OpportunityItems_View",
          "VALUEPARAM",
          "String(10)",
          "noValueHelp",
        ],
        propertyPath: "/variables/VALUEPARA1",
        translatableParams: ["factSource", "noValueHelp"],
        additionalPropertyPath: [],
        validatorName: "VariableValidator",
      });
    });

    it("#2 Calculated measure: should return warnings & Error for incompatible data type in calculated measure formula", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo21.businessLayerDefinitions.TEST_NO_21);
      analyticModel.variables = {
        VALUEPARA1: {
          parameterType: AnalyticModelParameterType.Input,
          text: "ValueParam_1",
          defaultValue: 113,
          dataType: {
            type: "cds.Integer",
          },
        },
      } as any;
      analyticModel.measures.CALCULATED_MEASURE = {
        measureType: "AnalyticModelMeasureType.CalculatedMeasure",
        isAuxiliary: false,
        formulaRaw: "Currency = :VALUEPARA1",
        formula: "[0] = [1]",
        elements: {
          "0": {
            operandType: "AnalyticModelCalculatedMeasureOperandType.Element",
            key: "Currency",
          },
          "1": {
            operandType: "AnalyticModelCalculatedMeasureOperandType.FormulaVariable",
            key: "VALUEPARA1",
          },
        },
      } as any;
      const getCsn: IGetCsnDefinitionFunction = (name: string): ICsnDefinition => amCsnMockNo21.definitions[name];
      const getParameterIssues = (measureKey: string): IFuncParameterDetails[] => {
        const measure = analyticModel.measures?.[measureKey] as IAnalyticModelCalculatedMeasure;
        const measureXpr = analyticModelCdsParseExprSync(measure.formulaRaw!);
        const checkTypeCompatibility = ExprTypeValidator.checkTypeCompatibility(
          analyticModel,
          getCsn,
          MODEL_ELEMENT.CALCULATED_MEASURE,
          "",
          measureXpr.expr,
          {},
          false
        );
        return checkTypeCompatibility;
      };
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        amCsnMockNo21.definitions,
        isFeatureFlagActive
      );
      expect(messages.length).equal(3);
      assert.deepStrictEqual(messages, [
        {
          message: "Calculated 'CALCULATED_MEASURE' has no valid expression",
          messageKey: "invalidCalculatedMeasureFilterConditionTypeMismatch",
          descriptionKey: "validationDescriptionNoValidFilterConditionIncompatibleTypesRKF",
          type: "Warning",
          parameters: ["Currency", "cds.String", "VALUEPARA1", "cds.Integer", "CALCULATED_MEASURE"],
          propertyPath: "/measures/CALCULATED_MEASURE/expression",
          validatorName: "MeasureValidator",
        },
        {
          message: "Datatypes of mapped parameters for input parameter 'VALUEPARA1' do not match",
          messageKey: "validationMessageTypeMismatchInputParam",
          descriptionKey: "validationDescriptionTypeMismatchInputParam",
          type: "Error",
          parameters: [
            "VALUEPARA1",
            "factSource",
            "MCT_OpportunityItems_View",
            "VALUEPARAM",
            "String(10)",
            "noValueHelp",
            "calculatedMeasure",
            "CALCULATED_MEASURE",
            "VALUEPARA1",
            "Integer",
            "noValueHelp",
          ],
          propertyPath: "/variables/VALUEPARA1",
          translatableParams: ["factSource", "noValueHelp", "calculatedMeasure", "noValueHelp"],
          additionalPropertyPath: ["/measures/CALCULATED_MEASURE"],
          validatorName: "VariableValidator",
        },
        {
          message: "Datatypes of mapped parameters for input parameter 'VALUEPARA1' do not match",
          messageKey: "validationMessageSourceVariableTypeMismatch",
          descriptionKey: "validationDescriptionSourceVariableTypeMismatch",
          type: "Error",
          parameters: [
            "VALUEPARA1",
            "Integer",
            "factSource",
            "MCT_OpportunityItems_View",
            "VALUEPARAM",
            "String(10)",
            "noValueHelp",
            "calculatedMeasure",
            "CALCULATED_MEASURE",
            "VALUEPARA1",
            "Integer",
            "noValueHelp",
          ],
          propertyPath: "/variables/VALUEPARA1",
          translatableParams: ["factSource", "noValueHelp", "calculatedMeasure", "noValueHelp"],
          additionalPropertyPath: ["/measures/CALCULATED_MEASURE"],
          validatorName: "VariableValidator",
        },
      ]);
      expect(getParameterIssues("CALCULATED_MEASURE").length).equal(1);
      const warning = getParameterIssues("CALCULATED_MEASURE");
      expect(warning.length).equal(1);
      assert.deepStrictEqual(warning[0].errorI18n, {
        descriptionId: "validationDescriptionNoValidFilterConditionIncompatibleTypesRKF",
        messageId: "invalidCalculatedMeasureFilterConditionTypeMismatch",
        params: ["Currency", "cds.String", "VALUEPARA1", "cds.Integer", ""],
        error: "types are not compatible",
      });
    });

    it("#3 currency conversion measure: should return Error for incompatible data type in currency conversion measure", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo21.businessLayerDefinitions.TEST_NO_21);
      analyticModel.variables.TARGET_CURRENCY = {
        parameterType: AnalyticModelParameterType.Input,
        text: "Target Currency",
        defaultValue: "EUR",
        dataType: {
          type: "cds.Integer",
        },
      };
      analyticModel.measures = {
        Value: {
          measureType: "AnalyticModelMeasureType.FactSourceMeasure",
          sourceKey: "0",
          text: "Value",
          key: "Value",
        },
        CURRENCY_MEASURE: {
          key: "Value",
          targetCurrencyType: "AnalyticModelTargetCurrencyType.variable",
          targetCurrency: {
            key: "TARGET_CURRENCY",
          },
          conversionType: {
            key: "Currency",
          },
          conversionTypeType: "AnalyticModelConversionTypeType.attribute",
          referenceDateType: "AnalyticModelReferenceDateType.constantValue",
          referenceDate: {
            value: "20240418",
          },
          sourceCurrencyType: "AnalyticModelSourceCurrencyType.derived",
          measureType: "AnalyticModelMeasureType.CurrencyConversionMeasure",
          text: "Currency Measure",
          client: "000",
          errorHandling: "AnalyticModelErrorHandling.null",
        },
      };
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        amCsnMockNo21.definitions,
        isFeatureFlagActive
      );
      expect(messages.length).equal(1);
      assert.deepStrictEqual(messages, [
        {
          message: "Datatypes of mapped parameters for input parameter 'TARGET_CURRENCY' do not match",
          messageKey: "validationMessageSourceVariableTypeMismatch",
          descriptionKey: "validationDescriptionSourceVariableTypeMismatch",
          type: "Error",
          parameters: [
            "TARGET_CURRENCY",
            "Integer",
            "CC_currencyConversionMeasureTargetCurrency",
            "CURRENCY_MEASURE",
            "TARGET_CURRENCY",
            "String(3)",
            "noValueHelp",
          ],
          propertyPath: "/variables/TARGET_CURRENCY",
          translatableParams: ["CC_currencyConversionMeasureTargetCurrency", "noValueHelp"],
          additionalPropertyPath: ["/measures/CURRENCY_MEASURE"],
          validatorName: "VariableValidator",
        },
      ]);
    });

    it("#4 should return Error for incompatible data type in calculated measure formula & currency conversion measure", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo21.businessLayerDefinitions.TEST_NO_21);
      analyticModel.variables.TARGET_CURRENCY = {
        parameterType: AnalyticModelParameterType.Input,
        text: "Target Currency",
        defaultValue: "EUR",
        dataType: {
          type: "cds.String",
          length: 10,
        },
      };
      analyticModel.measures = {
        Value: {
          measureType: "AnalyticModelMeasureType.FactSourceMeasure",
          sourceKey: "0",
          text: "Value",
          key: "Value",
        },
        CURRENCY_MEASURE: {
          key: "Value",
          targetCurrencyType: "AnalyticModelTargetCurrencyType.variable",
          targetCurrency: {
            key: "TARGET_CURRENCY",
          },
          conversionType: {
            key: "Currency",
          },
          conversionTypeType: "AnalyticModelConversionTypeType.attribute",
          referenceDateType: "AnalyticModelReferenceDateType.constantValue",
          referenceDate: {
            value: "20240418",
          },
          sourceCurrencyType: "AnalyticModelSourceCurrencyType.derived",
          measureType: "AnalyticModelMeasureType.CurrencyConversionMeasure",
          text: "Currency Measure",
          isAuxiliary: false,
          client: "000",
          errorHandling: "AnalyticModelErrorHandling.null",
        },
        CALCULATED_MEASURE: {
          measureType: "AnalyticModelMeasureType.CalculatedMeasure",
          isAuxiliary: false,
          formulaRaw: "Opportunity IN (:TARGET_CURRENCY)",
          formula: "[0] IN ([1])",
          elements: {
            "0": {
              operandType: "AnalyticModelCalculatedMeasureOperandType.Element",
              key: "Opportunity",
            },
            "1": {
              operandType: "AnalyticModelCalculatedMeasureOperandType.FormulaVariable",
              key: "TARGET_CURRENCY",
            },
          },
        },
      };
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        amCsnMockNo21.definitions,
        isFeatureFlagActive
      );
      expect(messages.length).equal(3);
      assert.deepStrictEqual(messages, [
        {
          message: "Calculated 'CALCULATED_MEASURE' has no valid expression",
          messageKey: "invalidCalculatedMeasureFilterConditionTypeMismatch",
          descriptionKey: "validationDescriptionNoValidFilterConditionIncompatibleTypesRKF",
          type: "Warning",
          parameters: ["Opportunity", "cds.Integer64", "TARGET_CURRENCY", "string", "CALCULATED_MEASURE"],
          propertyPath: "/measures/CALCULATED_MEASURE/expression",
          validatorName: "MeasureValidator",
        },
        {
          message: "Datatypes of mapped parameters for input parameter 'TARGET_CURRENCY' do not match",
          messageKey: "validationMessageTypeMismatchInputParam",
          descriptionKey: "validationDescriptionTypeMismatchInputParam",
          type: "Error",
          parameters: [
            "TARGET_CURRENCY",
            "CC_currencyConversionMeasureTargetCurrency",
            "CURRENCY_MEASURE",
            "TARGET_CURRENCY",
            "String(3)",
            "noValueHelp",
            "calculatedMeasure",
            "CALCULATED_MEASURE",
            "TARGET_CURRENCY",
            "String(10)",
            "noValueHelp",
          ],
          propertyPath: "/variables/TARGET_CURRENCY",
          translatableParams: [
            "CC_currencyConversionMeasureTargetCurrency",
            "noValueHelp",
            "calculatedMeasure",
            "noValueHelp",
          ],
          additionalPropertyPath: ["/measures/CURRENCY_MEASURE", "/measures/CALCULATED_MEASURE"],
          validatorName: "VariableValidator",
        },
        {
          message: "Datatypes of mapped parameters for input parameter 'TARGET_CURRENCY' do not match",
          messageKey: "validationMessageSourceVariableTypeMismatch",
          descriptionKey: "validationDescriptionSourceVariableTypeMismatch",
          type: "Error",
          parameters: [
            "TARGET_CURRENCY",
            "String(10)",
            "CC_currencyConversionMeasureTargetCurrency",
            "CURRENCY_MEASURE",
            "TARGET_CURRENCY",
            "String(3)",
            "noValueHelp",
            "calculatedMeasure",
            "CALCULATED_MEASURE",
            "TARGET_CURRENCY",
            "String(10)",
            "noValueHelp",
          ],
          propertyPath: "/variables/TARGET_CURRENCY",
          translatableParams: [
            "CC_currencyConversionMeasureTargetCurrency",
            "noValueHelp",
            "calculatedMeasure",
            "noValueHelp",
          ],
          additionalPropertyPath: ["/measures/CURRENCY_MEASURE", "/measures/CALCULATED_MEASURE"],
          validatorName: "VariableValidator",
        },
      ]);
      const getCsn: IGetCsnDefinitionFunction = (name: string): ICsnDefinition => amCsnMockNo21.definitions[name];
      const getParameterIssues = (measureKey: string): IFuncParameterDetails[] => {
        const measure = analyticModel.measures?.[measureKey] as IAnalyticModelCalculatedMeasure;
        const measureXpr = analyticModelCdsParseExprSync(measure.formulaRaw!);
        const checkTypeCompatibility = ExprTypeValidator.checkTypeCompatibility(
          analyticModel,
          getCsn,
          MODEL_ELEMENT.CALCULATED_MEASURE,
          "",
          measureXpr.expr,
          {},
          false
        );
        return checkTypeCompatibility;
      };

      expect(getParameterIssues("CALCULATED_MEASURE").length).equal(1);
      const warning = getParameterIssues("CALCULATED_MEASURE");
      expect(warning.length).equal(1);
      assert.deepStrictEqual(warning[0].errorI18n, {
        descriptionId: "validationDescriptionNoValidFilterConditionIncompatibleTypesRKF",
        messageId: "invalidCalculatedMeasureFilterConditionTypeMismatch",
        params: ["Opportunity", "cds.Integer64", "TARGET_CURRENCY", "string", ""],
        error: "types are not compatible",
      });
    });

    it("#5 should return warning while using unknown dataType variable in calculated measure formula", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo21.businessLayerDefinitions.TEST_NO_21);
      analyticModel.variables.CALCULATED_VARIABLE = {
        parameterType: AnalyticModelParameterType.Input,
        text: "calculated variable",
      };
      analyticModel.measures = {
        CALCULATED_MEASURE: {
          measureType: "AnalyticModelMeasureType.CalculatedMeasure",
          isAuxiliary: false,
          formulaRaw: "Opportunity IN (:CALCULATED_VARIABLE)",
          formula: "[0] IN ([1])",
          elements: {
            "0": {
              operandType: "AnalyticModelCalculatedMeasureOperandType.Element",
              key: "Opportunity",
            },
            "1": {
              operandType: "AnalyticModelCalculatedMeasureOperandType.FormulaVariable",
              key: "CALCULATED_VARIABLE",
            },
          },
        },
      };
      const getCsn: IGetCsnDefinitionFunction = (name: string): ICsnDefinition => amCsnMockNo21.definitions[name];
      const getParameterIssues = (measureKey: string): IFuncParameterDetails[] => {
        const measure = analyticModel.measures?.[measureKey] as IAnalyticModelCalculatedMeasure;
        const measureXpr = analyticModelCdsParseExprSync(measure.formulaRaw!);
        const checkTypeCompatibility = ExprTypeValidator.checkTypeCompatibility(
          analyticModel,
          getCsn,
          MODEL_ELEMENT.CALCULATED_MEASURE,
          "",
          measureXpr.expr,
          {},
          false
        );
        return checkTypeCompatibility;
      };
      expect(getParameterIssues("CALCULATED_MEASURE").length).equal(1);
      const warning = getParameterIssues("CALCULATED_MEASURE");
      expect(warning.length).equal(1);
      assert.deepStrictEqual(warning[0].errorI18n, {
        descriptionId: "validationDescriptionNoValidFilterConditionIncompatibleTypesRKF",
        messageId: "invalidCalculatedMeasureFilterConditionTypeMismatch",
        params: ["Opportunity", "cds.Integer64", "CALCULATED_VARIABLE", "unknown", ""],
        error: "types are not compatible",
      });
    });

    it("#6 No error- if reference attributes in calculated measure expression is not the same", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo21.businessLayerDefinitions.TEST_NO_21);
      analyticModel.measures = {
        CALCULATED_MEASURE: {
          measureType: "AnalyticModelMeasureType.CalculatedMeasure",
          formulaRaw: "Currency + '  ' +  FactAttr",
          formula: "[0]+[1]+[2]",
          elements: {
            "0": {
              operandType: "AnalyticModelCalculatedMeasureOperandType.Element",
              key: "Currency",
            },
            "1": {
              operandType: "AnalyticModelCalculatedMeasureOperandType.ConstantValue",
              value: "  ",
            },
            "2": {
              operandType: "AnalyticModelCalculatedMeasureOperandType.Element",
              key: "FactAttr",
            },
          },
        },
      };
      const getCsn: IGetCsnDefinitionFunction = (name: string): ICsnDefinition => amCsnMockNo21.definitions[name];
      const getParameterIssues = (measureKey: string): IFuncParameterDetails[] => {
        const measure = analyticModel.measures?.[measureKey] as IAnalyticModelCalculatedMeasure;
        const measureXpr = analyticModelCdsParseExprSync(measure.formulaRaw!);
        const checkTypeCompatibility = ExprTypeValidator.checkTypeCompatibility(
          analyticModel,
          getCsn,
          MODEL_ELEMENT.CALCULATED_MEASURE,
          "",
          measureXpr.expr,
          {},
          false
        );
        return checkTypeCompatibility;
      };
      expect(getParameterIssues("CALCULATED_MEASURE").length).equal(0);
    });

    it("#7 should return no error while using fact measure in calculation expression & should return warning while using unknown variable data type", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo21.businessLayerDefinitions.TEST_NO_21);
      analyticModel.variables.NEW_VAR = {
        parameterType: AnalyticModelParameterType.Input,
        text: "new standard variable",
      };
      analyticModel.measures = {
        MEASURE1: {
          measureType: AnalyticModelMeasureType.CalculatedMeasure,
          formulaRaw: "Value + 2",
          formula: "[0]+[1]",
          elements: {
            "0": {
              operandType: AnalyticModelCalculatedMeasureOperandType.Element,
              key: "Value",
            },
            "1": {
              operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
              value: 2,
            },
          },
        },
        MEASURE2: {
          measureType: AnalyticModelMeasureType.CalculatedMeasure,
          formulaRaw: ":NEW_VAR + 2",
          formula: "[0]+[1]",
          elements: {
            "0": {
              operandType: AnalyticModelCalculatedMeasureOperandType.FormulaVariable,
              key: "NEW_VAR",
            },
            "1": {
              operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
              value: 2,
            },
          },
        },
      };
      const getCsn: IGetCsnDefinitionFunction = (name: string): ICsnDefinition => amCsnMockNo21.definitions[name];
      const getParameterIssues = (measureKey: string): IFuncParameterDetails[] => {
        const measure = analyticModel.measures?.[measureKey] as IAnalyticModelCalculatedMeasure;
        const measureXpr = analyticModelCdsParseExprSync(measure.formulaRaw!);
        const checkTypeCompatibility = ExprTypeValidator.checkTypeCompatibility(
          analyticModel,
          getCsn,
          MODEL_ELEMENT.CALCULATED_MEASURE,
          "",
          measureXpr.expr,
          {},
          false
        );
        return checkTypeCompatibility;
      };
      // no error while using fact measure in calculation expression
      expect(getParameterIssues("MEASURE1").length).equal(0);
      expect(getParameterIssues("MEASURE2").length).equal(1);
      // warning while using variable with unknown data type in calculation expression
      assert.deepStrictEqual(getParameterIssues("MEASURE2")[0].errorI18n, {
        descriptionId: "validationDescriptionNoValidFilterConditionIncompatibleTypesRKF",
        messageId: "invalidCalculatedMeasureFilterConditionTypeMismatch",
        params: ["2", "number", "NEW_VAR", "unknown", ""],
        error: "types are not compatible",
      });
    });

    it("#8 Should return error when using Standard variable in restricted measure and filter variable in calculated measure", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo21.businessLayerDefinitions.TEST_NO_21);
      analyticModel.variables = {
        CURRENCY: {
          text: "currency",
          parameterType: AnalyticModelParameterType.Filter,
          multipleSelections: true,
          selectionType: AnalyticModelVariableSelectionType.MULTIPLE_SINGLE,
          referenceAttribute: "Currency",
          mandatory: true,
          defaultValue: [],
        },
        NEW_VAR: {
          parameterType: AnalyticModelParameterType.Input,
          text: "new standard variable",
        },
      };
      analyticModel.measures = {
        MEASURE1: {
          measureType: AnalyticModelMeasureType.CalculatedMeasure,
          formulaRaw: "Currency != :CURRENCY",
          formula: "[0] != [1]",
          elements: {
            "0": {
              operandType: AnalyticModelCalculatedMeasureOperandType.Element,
              key: "Currency",
            },
            "1": {
              operandType: AnalyticModelCalculatedMeasureOperandType.FormulaVariable,
              key: "CURRENCY",
            },
          },
        },
        MEASURE2: {
          measureType: AnalyticModelMeasureType.RestrictedMeasure,
          key: "Quantity",
          text: "Restricted Measure",
          formula: "[0] != [1]",
          elements: {
            0: {
              operandType: AnalyticModelRestrictedMeasureOperandType.Attribute,
              key: "Currency",
            },
            1: {
              operandType: AnalyticModelRestrictedMeasureOperandType.FilterVariable,
              key: "NEW_VAR",
            },
          },
        },
        Quantity: {
          measureType: AnalyticModelMeasureType.FactSourceMeasure,
          sourceKey: "0",
          text: "Quantity",
          key: "Quantity",
          isAuxiliary: false,
        },
      };
      const getValidateParsedFormula = parseFormulaValidator(analyticModel);
      const measure1 = analyticModel.measures.MEASURE1;
      const convertFormulaResultInCalculatedMeasure = convertFormulaExpressionResult(
        "",
        measure1.formula,
        ["CURRENCY"],
        ["Currency"],
        measure1.elements,
        measure1.measureType
      );
      expect(getValidateParsedFormula("MEASURE1", convertFormulaResultInCalculatedMeasure).length).equal(1);
      assert.deepStrictEqual(getValidateParsedFormula("MEASURE1", convertFormulaResultInCalculatedMeasure)[0], {
        message: "Calculated measure 'MEASURE1': Variable 'CURRENCY' is used with incorrect type",
        messageKey: "validationMessageCalculatedInvalidExpression",
        descriptionKey: "measureContainsIncorrectVariableType",
        type: "Error",
        parameters: ["MEASURE1", "CURRENCY"],
      });

      const measure2 = analyticModel.measures.MEASURE2;
      const convertFormulaResultInRestrictedMeasure = convertFormulaExpressionResult(
        "",
        measure2.formula,
        ["NEW_VAR"],
        ["Currency"],
        measure2.elements,
        measure2.measureType
      );
      expect(getValidateParsedFormula("MEASURE2", convertFormulaResultInRestrictedMeasure).length).equal(1);
      assert.deepStrictEqual(getValidateParsedFormula("MEASURE2", convertFormulaResultInRestrictedMeasure)[0], {
        message: "Restricted measure 'MEASURE2': Variable 'NEW_VAR' is used with incorrect type",
        messageKey: "validationMessageRestrictedInvalidExpression",
        descriptionKey: "measureContainsIncorrectVariableType",
        type: "Error",
        parameters: ["MEASURE2", "NEW_VAR"],
      });
    });

    it("#9 No error- no formula validation check in calculated measure", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo21.businessLayerDefinitions.TEST_NO_21);
      analyticModel.measures = {
        MEASURE3: {
          text: "Calculated Measure",
          measureType: AnalyticModelMeasureType.CalculatedMeasure,
          formulaRaw: "CASE WHEN Quantity > Opportunity THEN 1 ELSE 0 END",
          formula: "case when [0] > [1] then [2] else [3] end",
          elements: {
            0: {
              operandType: AnalyticModelCalculatedMeasureOperandType.Element,
              key: "Quantity",
            },
            1: {
              operandType: AnalyticModelCalculatedMeasureOperandType.Element,
              key: "Opportunity",
            },
            2: {
              operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
              value: 1,
            },
            3: {
              operandType: AnalyticModelCalculatedMeasureOperandType.ConstantValue,
              value: 0,
            },
          },
        },
        Quantity: {
          measureType: AnalyticModelMeasureType.FactSourceMeasure,
          sourceKey: "0",
          text: "Quantity",
          key: "Quantity",
          isAuxiliary: false,
        },
      };
      const getValidateParsedFormula = parseFormulaValidator(analyticModel);
      const measure3 = analyticModel.measures.MEASURE3;
      const convertFormulaResult = convertFormulaExpressionResult(
        "CASE",
        measure3.formula,
        [],
        ["Quantity", "Opportunity"],
        measure3.elements,
        measure3.measureType
      );
      expect(getValidateParsedFormula("MEASURE3", convertFormulaResult).length).equal(0);
    });
  });

  context("Data Access Control checks", () => {
    it("should return no errors for valid DAC", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);
      await validate(analyticModel, definitions, []);
    });

    it("should return error if a criterion is not mapped", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      analyticModel.dataAccessControls!.THE_DAC_Boolean.dacMapping = {};
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);
      await validate(analyticModel, definitions, [
        {
          descriptionKey: "validationDescriptionDacMappingMissing",
          message: "Missing criterion mapping in Data Access Control",
          messageKey: "validationMessageDacMappingMissing",
          parameters: ["THE_DAC_Boolean"],
          propertyPath: "/dataAccessControls/THE_DAC_Boolean",
          type: ValidationMessageType.ERROR,
          validatorName: "DataAccessControlValidator",
        },
      ]);
    });

    it("should return error if mapped criterion does not exist", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      analyticModel.dataAccessControls!.THE_DAC_Boolean.dacMapping = {
        product: "Product",
        name: "ReceivingCostCenter",
      };
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);
      await validate(analyticModel, definitions, [
        {
          descriptionKey: "validationDescriptionDacTargetCriterionMissing",
          message: "Target criterion used in mapping in Data Access Control is missing",
          messageKey: "validationMessageDacTargetCriterionMissing",
          parameters: ["name", "THE_DAC_Boolean"],
          propertyPath: "/dataAccessControls/THE_DAC_Boolean",
          type: ValidationMessageType.ERROR,
          validatorName: "DataAccessControlValidator",
        },
      ]);
    });

    it("should return error if mapped attribute does not exist", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      analyticModel.dataAccessControls!.THE_DAC_Boolean.dacMapping = {
        product: "Product123",
      };
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);
      await validate(analyticModel, definitions, [
        {
          descriptionKey: "validationDescriptionDacSourceAttributeMissing",
          message: "Source attribute used in mapping in Data Access Control is missing",
          messageKey: "validationMessageDacSourceAttributeMissing",
          parameters: ["Product123", "THE_DAC_Boolean"],
          propertyPath: "/dataAccessControls/THE_DAC_Boolean",
          type: ValidationMessageType.ERROR,
          validatorName: "DataAccessControlValidator",
        },
      ]);
    });

    it("should return error if there are boolean and hierarchy DACs at the same time in the model", async () => {
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> =>
        featureFlag !== "DWCO_DAC_SF_STATIC_TO_DYNAMIC"; // regular case for now is feature flag disabled
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      analyticModel.dataAccessControls!.THE_DAC_Hierarchy = {
        dacMapping: {
          product: "Product",
        },
      };
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);
      (definitions as any).THE_DAC_Hierarchy = _.cloneDeep(definitions.THE_DAC_Boolean);
      delete (definitions as any).THE_DAC_Hierarchy["@DataWarehouse.dataAccessControl.definition"].restrictionElements;
      (definitions as any).THE_DAC_Hierarchy["@DataWarehouse.dataAccessControl.definition"].hierarchyEntity = {};
      (definitions as any).THE_DAC_Hierarchy["@DataWarehouse.dataAccessControl.definition"].sourceNodeElement =
        "sourceNodeElement";
      await validate(
        analyticModel,
        definitions,
        [
          {
            descriptionKey: "validationDescriptionDacBooleanHierarchiesNotSupported",
            message: "Data Access Control - Boolean and Hierarchies DACs not supported in the same model",
            messageKey: "validationMessageDacBooleanHierarchiesNotSupported",
            parameters: ["THE_DAC_Boolean", "THE_DAC_Hierarchy"],
            type: ValidationMessageType.ERROR,
            validatorName: "DataAccessControlValidator",
          },
        ],
        isFeatureFlagActive
      );
    });

    it("should return error if there are boolean and values DACs at the same time in the model", async () => {
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> =>
        featureFlag !== "DWCO_DAC_SF_STATIC_TO_DYNAMIC"; // regular case for now is feature flag disabled

      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      analyticModel.dataAccessControls!.THE_DAC_VALUES = {
        dacMapping: {
          product: "Product",
        },
      };
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);
      (definitions as any).THE_DAC_VALUES = _.cloneDeep(definitions.THE_DAC_Boolean);
      delete (definitions as any).THE_DAC_VALUES["@DataWarehouse.dataAccessControl.definition"].restrictionElements;
      await validate(
        analyticModel,
        definitions,
        [
          {
            descriptionKey: "validationDescriptionDacBooleanSingleValuesNotSupported",
            message: "Data Access Control - Boolean and Single Values DACs not supported in the same model",
            messageKey: "validationMessageDacBooleanSingleValuesNotSupported",
            parameters: ["THE_DAC_Boolean", "THE_DAC_VALUES"],
            type: ValidationMessageType.ERROR,
            validatorName: "DataAccessControlValidator",
          },
        ],
        isFeatureFlagActive
      );
    });

    it("should return success if there are boolean and hierarchy DACs at the same time in the model (DWCO_DAC_SF_STATIC_TO_DYNAMIC enabled)", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      analyticModel.dataAccessControls!.THE_DAC_Hierarchy = {
        dacMapping: {
          product: "Product",
        },
      };
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);
      (definitions as any).THE_DAC_Hierarchy = _.cloneDeep(definitions.THE_DAC_Boolean);
      delete (definitions as any).THE_DAC_Hierarchy["@DataWarehouse.dataAccessControl.definition"].restrictionElements;
      (definitions as any).THE_DAC_Hierarchy["@DataWarehouse.dataAccessControl.definition"].hierarchyEntity = {};
      (definitions as any).THE_DAC_Hierarchy["@DataWarehouse.dataAccessControl.definition"].sourceNodeElement =
        "sourceNodeElement";
      await validate(analyticModel, definitions, []);
    });

    it("should return success if there are boolean and values DACs at the same time in the model (DWCO_DAC_SF_STATIC_TO_DYNAMIC enabled)", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      analyticModel.dataAccessControls!.THE_DAC_VALUES = {
        dacMapping: {
          product: "Product",
        },
      };
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);
      (definitions as any).THE_DAC_VALUES = _.cloneDeep(definitions.THE_DAC_Boolean);
      delete (definitions as any).THE_DAC_VALUES["@DataWarehouse.dataAccessControl.definition"].restrictionElements;
      await validate(analyticModel, definitions, []);
    });

    it("should return error if there are DACs mapped to dimension attributes if the model has data source variables", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      // Make use of the standard variable -> avoid validation message
      (analyticModel.measures!.Calculated_Measure as any).formulaRaw = ":STANDARD_VARIABLE";
      (analyticModel.measures!.Calculated_Measure as any).elements = {
        "0": {
          operandType: AnalyticModelCalculatedMeasureOperandType.FormulaVariable,
          key: "STANDARD_VARIABLE",
        },
      };

      analyticModel.dataAccessControls!.THE_DAC_Boolean.dacMapping = {
        product: "ProductCategory_Product",
      };
      analyticModel.variables = {
        STANDARD_VARIABLE: {
          text: "Standard Variable 1",
          parameterType: AnalyticModelParameterType.Input,
          variableProcessingType: AnalyticModelVariableProcessingType.MANUAL_INPUT,
          order: 1,
          dataType: {
            type: "cds.String",
            length: 10,
          },
        },
      };
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);

      const featureFlagFn: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => {
        if (featureFlag === "DWCO_MODELING_SUPPORT_FISCAL_TIME") {
          return false;
        }
        return true;
      };
      await validate(
        analyticModel,
        definitions,
        [
          {
            message: "Mapping is not supported for DAC",
            messageKey: "validationMessageDacMappingToDimensionAttributeNotSupported",
            descriptionKey: "validationDescriptionDacMappingToDimensionAttributeNotSupported",
            type: ValidationMessageType.ERROR,
            parameters: ["THE_DAC_Boolean"],
            validatorName: "DataAccessControlValidator",
            propertyPath: "/dataAccessControls/THE_DAC_Boolean",
          },
        ],
        featureFlagFn
      );
    });

    it("should return error if there are DACs mapped to dimension attributes if the model has data source variables (DWCO_MODELING_SUPPORT_FISCAL_TIME enabled)", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      analyticModel.dataAccessControls!.THE_DAC_Boolean.dacMapping = {
        product: "ProductCategory_Product",
      };
      analyticModel.variables = {
        FISCAL: {
          text: "Fiscal",
          parameterType: AnalyticModelParameterType.FiscalVariant,
          variableProcessingType: AnalyticModelVariableProcessingType.MANUAL_INPUT,
          order: 1,
          defaultValue: "A",
        },
      };
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);

      await validate(analyticModel, definitions, [
        {
          message: "Mapping is not supported for DAC",
          messageKey: "validationMessageDacMappingToDimensionAttributeNotSupported",
          descriptionKey: "validationDescriptionDacMappingToDimensionAttributeNotSupportedFiscal",
          type: ValidationMessageType.ERROR,
          parameters: ["THE_DAC_Boolean"],
          validatorName: "DataAccessControlValidator",
          propertyPath: "/dataAccessControls/THE_DAC_Boolean",
        },
      ]);
    });
  });

  context("GrandTotal checks", () => {
    it("should return error if GrandTotal parameter is not a measure", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      analyticModel.measures!.GrandTotal1ParamNotMeasure = {
        text: "GrandTotal1ParamNotMeasure",
        measureType: AnalyticModelMeasureType.CalculatedMeasure,
        isAuxiliary: false,
        formulaRaw: "GrandTotal(ControllingArea)",
        formula: "GrandTotal([0])",
        elements: {
          "0": {
            operandType: AnalyticModelCalculatedMeasureOperandType.Element,
            key: "ControllingArea",
          },
        },
      };
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);
      await validate(analyticModel, definitions, [
        {
          message: "Calculated 'GrandTotal1ParamNotMeasure' has no valid expression",
          messageKey: "validationMessageCalculatedInvalidParameter",
          descriptionKey: "validationDescriptionCalculatedInvalidParameter",
          type: ValidationMessageType.ERROR,
          parameters: ["GrandTotal1ParamNotMeasure", "1", "GrandTotal"],
          propertyPath: "/measures/GrandTotal1ParamNotMeasure/expression",
          validatorName: "MeasureValidator",
        },
      ]);
    });

    it("should return error of GrandTotal has more than 1 parameter", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      analyticModel.measures!.GrandTotal2Params = {
        text: "GrandTotal2Params",
        measureType: AnalyticModelMeasureType.CalculatedMeasure,
        isAuxiliary: false,
        formulaRaw: "GrandTotal(Quantity, Value)",
      };
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);
      await validate(analyticModel, definitions, [
        {
          message: "Calculated measure GrandTotal2Params: Wrong number of parameters in function GrandTotal",
          messageKey: "validationMessageCalculatedInvalidExpression",
          descriptionKey: "measureExpressionFunctionParameterMismatch",
          type: ValidationMessageType.ERROR,
          parameters: ["GrandTotal2Params", "GrandTotal"],
          propertyPath: "/measures/GrandTotal2Params/expression",
          validatorName: "MeasureValidator",
        },
      ]);
    });

    it("should return error of GrandTotal has 0 parameters", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      analyticModel.measures!.GrandTotal0Params = {
        text: "GrandTotal0Params",
        measureType: AnalyticModelMeasureType.CalculatedMeasure,
        isAuxiliary: false,
        formulaRaw: "GrandTotal()",
      };
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);
      await validate(analyticModel, definitions, [
        {
          message: "Calculated measure GrandTotal0Params: Wrong number of parameters in function GrandTotal",
          messageKey: "validationMessageCalculatedInvalidExpression",
          descriptionKey: "measureExpressionFunctionParameterMismatch",
          type: ValidationMessageType.ERROR,
          parameters: ["GrandTotal0Params", "GrandTotal"],
          propertyPath: "/measures/GrandTotal0Params/expression",
          validatorName: "MeasureValidator",
        },
      ]);
    });

    it("should return no errors if GrandTotal has exactly one parameter which is a measure", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      analyticModel.measures!.GrandTotalCorrect = {
        text: "GrandTotalCorrect",
        measureType: AnalyticModelMeasureType.CalculatedMeasure,
        isAuxiliary: false,
        formulaRaw: "GrandTotal(Quantity)",
        formula: "GrandTotal([0])",
        elements: {
          "0": {
            operandType: AnalyticModelCalculatedMeasureOperandType.Element,
            key: "Quantity",
          },
        },
      };
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);
      await validate(analyticModel, definitions, []);
    });

    it("should return no errors if GrandTotal is not exactly written GrandTotal", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      analyticModel.measures!.GrandTotalCorrect = {
        text: "GrandTotalCorrect",
        measureType: AnalyticModelMeasureType.CalculatedMeasure,
        isAuxiliary: false,
        formulaRaw: "graNDtOTal(Quantity)",
        formula: "graNDtOTal([0])",
        elements: {
          "0": {
            operandType: AnalyticModelCalculatedMeasureOperandType.Element,
            key: "Quantity",
          },
        },
      };
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);
      await validate(analyticModel, definitions, []);
    });
  });

  context("Technical Name checks", () => {
    it("should return warnings if technical names is too long", async () => {
      const analyticModel = _.cloneDeep(validateMockTechnicalNames) as any;

      // 1 - ANALYTIC MODEL: Max length is 50 for entities
      analyticModel.identifier.key = "MCT_OpportunityItemsMCT_OpportunityItemsMCT_OpportunityItems";

      // 2 - FACT SOURCE: Max length is 50 for entities (entity name used for fact sources)
      analyticModel.sourceModel.factSources.MCT_OpportunityItemsMCT_OpportunityItemsMCT_OpportunityItems =
        analyticModel.sourceModel.factSources.MCT_OpportunityItems;
      delete analyticModel.sourceModel.factSources.MCT_OpportunityItems;

      // 3 - DIMENSIONS: Max length is 20 for associations
      analyticModel.sourceModel.dimensionSources._ControllingAreaWithATooLongName =
        analyticModel.sourceModel.dimensionSources._ControllingArea;
      delete analyticModel.sourceModel.dimensionSources._ControllingArea;

      // 4 - ATTRIBUTE: Max length is 30 for elements, "ControllingArea_ReceivingCostCenter" is already too long
      analyticModel.attributes.ReceivingCostCenter_With_Technical_Name_That_Is_Too_Long =
        analyticModel.attributes.ReceivingCostCenter;
      delete analyticModel.attributes.ReceivingCostCenter;

      // 5 - MEASURE: Max length is 30 for elements
      analyticModel.measures.RKF_Measure_With_Technical_Name_That_Is_Too_Long = analyticModel.measures.RKF;
      delete analyticModel.measures.RKF;

      // 6 - VARIABLE: Max length is 30 for parameters
      analyticModel.variables.VAR_RKF_MEASURE_WITH_TECHNICAL_NAME_THAT_IS_TOO_LONG = analyticModel.variables.VAR_RKF;
      delete analyticModel.variables.VAR_RKF;

      const validationMessages = await new TechnicalNameValidator({
        caller: {},
        analyticModel,
        sourceModelsByName: {}, // not needed for technical name validations
      }).validate();

      expect(validationMessages.length).equal(6);
      validationMessages.forEach((message) => {
        expect(message.messageKey).to.contain("TechnicalNameExceedingMaxLength");
        expect(message.type).to.equal(ValidationMessageType.WARN);
      });
    });

    it("should return error if technical names have invalid characters", async () => {
      const analyticModel = _.cloneDeep(validateMockTechnicalNames) as any;

      // 1 - ANALYTIC MODEL: ~ is an invalid character
      analyticModel.identifier.key = "TEST~NAME";

      // 2 - FACT SOURCE: ∫ is an invalid character
      analyticModel.sourceModel.factSources["∫MCT_OpportunityItems"] =
        analyticModel.sourceModel.factSources.MCT_OpportunityItems;
      delete analyticModel.sourceModel.factSources.MCT_OpportunityItems;

      // 3 - DIMENSION: ® is an invalid character
      analyticModel.sourceModel.dimensionSources["®ControllingArea"] =
        analyticModel.sourceModel.dimensionSources._ControllingArea;
      delete analyticModel.sourceModel.dimensionSources._ControllingArea;

      // 4 - ATTRIBUTE: π is an invalid character
      analyticModel.attributes["πRCC"] = analyticModel.attributes.ReceivingCostCenter;
      delete analyticModel.attributes.ReceivingCostCenter;

      // 5 - MEASURE: ∂ is an invalid character
      analyticModel.measures["RKF∂RKF"] = analyticModel.measures.RKF;
      delete analyticModel.measures.RKF;

      // 6 - VARIABLE: « is an invalid character
      analyticModel.variables["VAR«RKF"] = analyticModel.variables.VAR_RKF;
      delete analyticModel.variables.VAR_RKF;

      const validationMessages = await new TechnicalNameValidator({
        caller: {},
        analyticModel,
        sourceModelsByName: {}, // not needed for technical name validations
      }).validate();

      expect(validationMessages.length).equal(6);
      validationMessages.forEach((message) => {
        expect(message.messageKey).to.contain("TechnicalNameInvalid");
        expect(message.type).to.equal(ValidationMessageType.ERROR);
      });
    });

    it("should return error if technical names contain ∞, but don't match adaption", async () => {
      const analyticModel = _.cloneDeep(validateMockTechnicalNames) as any;

      // 1 - DIMENSION: only "∞" followed by a number at the end of the name is valid
      analyticModel.sourceModel.dimensionSources["_Controlling∞0Area"] =
        analyticModel.sourceModel.dimensionSources._ControllingArea;
      delete analyticModel.sourceModel.dimensionSources._ControllingArea;

      // 2 - ATTRIBUTE: only "∞D" followed by a number at the end of the name is valid
      analyticModel.attributes["Controlling∞D0Area"] = analyticModel.attributes.ControllingArea;
      delete analyticModel.attributes.ReceivingCostCenter;

      const validationMessages = await new TechnicalNameValidator({
        caller: {},
        analyticModel,
        sourceModelsByName: {}, // not needed for technical name validations
      }).validate();

      expect(validationMessages.length).equal(2);
      validationMessages.forEach((message) => {
        expect(message.messageKey).to.contain("TechnicalNameInvalid");
      });
    });

    it("should return NO error if technical names match adaption", async () => {
      const analyticModel = _.cloneDeep(validateMockTechnicalNames) as any;

      // 1 - DIMENSION: "∞" followed by a number is valid
      analyticModel.sourceModel.dimensionSources["_ControllingArea∞0"] =
        analyticModel.sourceModel.dimensionSources._ControllingArea;
      delete analyticModel.sourceModel.dimensionSources._ControllingArea;

      // 2 - ATTRIBUTE: "∞D" followed by a number is valid
      analyticModel.attributes["ControllingArea∞D0"] = analyticModel.attributes.ControllingArea;
      delete analyticModel.attributes.ReceivingCostCenter;

      const validationMessages = await new TechnicalNameValidator({
        caller: {},
        analyticModel,
        sourceModelsByName: {}, // not needed for technical name validations
      }).validate();

      expect(validationMessages.length).equal(0);
    });

    it("45 should return NO Error in stacking case if no variables are mapped", async () => {
      const fullCsn = TestUtil_enhanceAnalyticDesignTimeCsnWithMctModelFullCSN(ALEX_AM_WITH_CC_DERIVATION_2);

      const analyticModel = _.cloneDeep(
        ALEX_AM_WITH_CC_DERIVATION_2.businessLayerDefinitions.Alex_AM_with_CC_Derivation_2
      );
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        fullCsn.definitions,
        isFeatureFlagActive
      );
      expect(messages.length).equal(0);
    });

    it("46 should not a return a validation message during the unit conversion", async () => {
      const fullCsn = TestUtil_enhanceAnalyticDesignTimeCsnWithMctModelFullCSN(ALEX_AUX_MEASURE_2);

      const analyticModel = _.cloneDeep(ALEX_AUX_MEASURE_2.businessLayerDefinitions.ALEX_AUX_MEASURE_2);
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;
      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        fullCsn.definitions,
        isFeatureFlagActive
      );
      expect(messages.length).equal(1);
      expect(messages[0]).to.deep.equal({
        message: 'Measure "Value" is auxiliary in underlying analytic model "ALEX_AUX_MEASURE"',
        messageKey: "validationMessageMeasureIsAuxiliaryInFactSource",
        descriptionKey: "validationDescriptionMeasureIsAuxiliaryFactSource",
        type: "Error",
        parameters: ["Value", "ALEX_AUX_MEASURE"],
        propertyPath: "/measures/Value",
        validatorName: "MeasureValidator",
      });
    });

    it("47 unit conversion measure: should return Error for an non existing base measure in unit conversion measure", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo45.businessLayerDefinitions.TEST_NO_45);

      analyticModel.variables!.TARGET_UNIT = {
        parameterType: AnalyticModelParameterType.Input,
        text: "Target Unit",
        defaultValue: "KG",
        dataType: {
          type: "cds.Integer",
        },
      };
      analyticModel.measures = {
        Quantity: {
          measureType: AnalyticModelMeasureType.FactSourceMeasure,
          sourceKey: "0",
          text: "Quantity",
          key: "Quantity",
          isAuxiliary: false,
        },
        UnitConversionMeasure: {
          isAuxiliary: false,
          text: "UnitConversionMeasure",
          measureType: AnalyticModelMeasureType.UnitConversionMeasure,
          key: "NotQuantityMeasure",
          sourceUnitType: AnalyticModelSourceUnitType.derived,
          targetUnitType: AnalyticModelTargetUnitType.variable,
          targetUnit: {
            key: "TARGET_UNIT",
          },
          client: "000",
          errorHandling: AnalyticModelErrorHandling.null,
        },
      };
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;

      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        amCsnMockNo45.definitions,
        isFeatureFlagActive
      );

      expect(messages.length).equal(2);

      assert.deepStrictEqual(messages, [
        {
          message: "Unit conversion measure 'UnitConversionMeasure' has non existent source measure",
          messageKey: "validationMessageUnitConversionNonExistentSourceMeasure",
          descriptionKey: "validationDescriptionUnitConversionInvalidSourceMeasure",
          type: "Error",
          parameters: ["UnitConversionMeasure", "NotQuantityMeasure"],
          propertyPath: "/measures/UnitConversionMeasure/sourceMeasure",
          validatorName: "MeasureValidator",
        },
        {
          message: "Datatypes of mapped parameters for input parameter 'TARGET_UNIT' do not match",
          messageKey: "validationMessageSourceVariableTypeMismatch",
          descriptionKey: "validationDescriptionSourceVariableTypeMismatch",
          type: "Error",
          parameters: ["TARGET_UNIT", "Integer"],
          translatableParams: [],
          propertyPath: "/variables/TARGET_UNIT",
          additionalPropertyPath: [],
          validatorName: "VariableValidator",
        },
      ]);
    });

    it("48 unit conversion measure: should return Error an invalid value in the target unit variable", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo45.businessLayerDefinitions.TEST_NO_45);

      analyticModel.measures = {
        Quantity: {
          measureType: AnalyticModelMeasureType.FactSourceMeasure,
          sourceKey: "0",
          text: "Quantity",
          key: "Quantity",
          isAuxiliary: false,
        },
        UnitConversionMeasure: {
          isAuxiliary: false,
          text: "UnitConversionMeasure",
          measureType: AnalyticModelMeasureType.UnitConversionMeasure,
          key: "Quantity",
          sourceUnitType: AnalyticModelSourceUnitType.derived,
          targetUnitType: AnalyticModelTargetUnitType.variable,
          targetUnit: {
            key: "NON_EXISTING_VARIABLE",
          },
          client: "000",
          errorHandling: AnalyticModelErrorHandling.null,
        },
      };
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;

      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        amCsnMockNo45.definitions,
        isFeatureFlagActive
      );

      expect(messages.length).equal(1);

      assert.deepStrictEqual(messages, [
        {
          message: "Unit conversion measure 'UnitConversionMeasure' contains non-existent variables",
          messageKey: "validationMessageUnitConversionVariableIncorrect",
          descriptionKey: "validationDescriptionUnitConversionTargetDimensionMissing",
          type: "Error",
          parameters: ["UnitConversionMeasure", "NON_EXISTING_VARIABLE"],
          propertyPath: "/measures/UnitConversionMeasure",
          additionalPropertyPath: ["/measures/UnitConversionMeasure/targetUnit"],
          validatorName: "MeasureValidator",
        },
      ]);
    });

    it("49 unit conversion measure: should return Error an empty value in the target unit constant value", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo45.businessLayerDefinitions.TEST_NO_45);

      analyticModel.measures = {
        Quantity: {
          measureType: AnalyticModelMeasureType.FactSourceMeasure,
          sourceKey: "0",
          text: "Quantity",
          key: "Quantity",
          isAuxiliary: false,
        },
        UnitConversionMeasure: {
          isAuxiliary: false,
          text: "UnitConversionMeasure",
          measureType: AnalyticModelMeasureType.UnitConversionMeasure,
          key: "Quantity",
          sourceUnitType: AnalyticModelSourceUnitType.derived,
          targetUnitType: AnalyticModelTargetUnitType.constantValue,
          targetUnit: {
            value: "",
          },
          client: "000",
          errorHandling: AnalyticModelErrorHandling.null,
        },
      };
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;

      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        amCsnMockNo45.definitions,
        isFeatureFlagActive
      );

      expect(messages.length).equal(1);

      assert.deepStrictEqual(messages, [
        {
          message: "Unit conversion measure 'UnitConversionMeasure' is not valid",
          messageKey: "VAL_UC_INVALID",
          descriptionKey: "VAL_UC_EMPTY_TARGET_UNIT",
          type: "Error",
          parameters: ["UnitConversionMeasure"],
          propertyPath: "/measures/UnitConversionMeasure/targetUnit",
          validatorName: "MeasureValidator",
        },
      ]);
    });

    it("50 unit conversion measure: source unit does not exist", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo45.businessLayerDefinitions.TEST_NO_45);

      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;

      const definitions = _.cloneDeep(amCsnMockNo45.definitions);

      analyticModel.measures.UnitConversionMeasure = {
        isAuxiliary: false,
        text: "UnitConversionMeasure",
        measureType: "AnalyticModelMeasureType.UnitConversionMeasure",
        key: "",
        sourceUnitType: "AnalyticModelSourceUnitType.derived",
        targetUnitType: "AnalyticModelTargetUnitType.constantValue",
        targetUnit: {
          value: "KG",
        },
        client: "000",
        errorHandling: "AnalyticModelErrorHandling.null",
      };

      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        definitions,
        isFeatureFlagActive
      );

      expect(messages.length).equal(1);

      assert.deepStrictEqual(messages, [
        {
          message: "Unit conversion measure 'UnitConversionMeasure' has no valid source measure",
          messageKey: "validationMessageUnitConversionInvalidSourceMeasure",
          descriptionKey: "validationDescriptionUnitConversionInvalidSourceMeasure",
          type: "Error",
          parameters: ["UnitConversionMeasure"],
          propertyPath: "/measures/UnitConversionMeasure/sourceMeasure",
          validatorName: "MeasureValidator",
        },
      ]);
    });

    it("51 unit conversion measure: should return Error if target unit variable has wrong subtype", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo45.businessLayerDefinitions.TEST_NO_45);

      analyticModel.measures = {
        Quantity: {
          measureType: AnalyticModelMeasureType.FactSourceMeasure,
          sourceKey: "0",
          text: "Quantity",
          key: "Quantity",
          isAuxiliary: false,
        },
        UnitConversionMeasure: {
          key: "Quantity",
          text: "Conversion Measure",
          isAuxiliary: false,
          client: "000",
          errorHandling: "AnalyticModelErrorHandling.null",
          targetUnitType: "AnalyticModelTargetUnitType.variable",
          targetUnit: {
            key: "TARGET_UNIT_1",
          },
          sourceUnitType: "AnalyticModelSourceUnitType.derived",
          measureType: "AnalyticModelMeasureType.UnitConversionMeasure",
        },
      };

      analyticModel.variables = {
        TARGET_UNIT_1: {
          parameterType: "AnalyticModelParameterType.FiscalVariant",
          text: "Target Unit 1",
          variableProcessingType: "AnalyticModelVariableProcessingType.ManualInput",
          order: 1,
          defaultValue: "GJ",
        },
      };
      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => true;

      const messages = await QueryModelValidator.validate(
        analyticModel,
        createCdsFormulaToExprConverter,
        amCsnMockNo45.definitions,
        isFeatureFlagActive
      );

      expect(messages.length).equal(2);

      assert.deepStrictEqual(messages, [
        {
          message: "Unit conversion measure 'UnitConversionMeasure' is not valid",
          messageKey: "VAL_UC_INVALID",
          descriptionKey: "VAL_UC_TARGET_UNIT_WRONG_VARIABLE_TYPE",
          type: "Error",
          parameters: ["UnitConversionMeasure"],
          propertyPath: "/measures/UnitConversionMeasure/targetUnit",
          validatorName: "MeasureValidator",
        },
        {
          message: "Fiscal variant variable 'TARGET_UNIT_1' is used in the model",
          messageKey: "validationMessageFiscalVariantVariableUsed",
          descriptionKey: "validationDescriptionFiscalVariantVariableUsed",
          type: "Error",
          parameters: ["TARGET_UNIT_1"],
          propertyPath: "/variables/TARGET_UNIT_1",
          validatorName: "VariableValidator",
        },
      ]);
    });
  });

  context("Variables checks", () => {
    it("should return an error if a dynamic variable is mapped to a Manual Input variable", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo37.businessLayerDefinitions.TEST_NO_37);
      const definitions = _.cloneDeep(amCsnMockNo37.definitions);
      await validate(analyticModel, definitions, [
        {
          descriptionKey: "validationMessageDescriptionDynamicLookupEntityParameterRefConstantVariable",
          message:
            "Variable 'REPORTING_CURRENCY' lookup entity parameter mapping is dynamic mapping to a non-hidden variable.",
          messageKey: "validationMessageDynamicLookupEntityParameterRefConstantVariable",
          parameters: ["REPORTING_CURRENCY", "COUNTRY", "COUNTRY"],
          propertyPath: "/variables/REPORTING_CURRENCY/targetEntityParam/COUNTRY",
          type: ValidationMessageType.ERROR,
          validatorName: "VariableValidator",
        },
      ]);
    });

    it("should return an error if a dynamic variable is mapped to a Manual Input variable in a transitive way", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo41.businessLayerDefinitions.TEST_NO_41);
      const definitions = _.cloneDeep(amCsnMockNo41.definitions);
      await validate(analyticModel, definitions, [
        {
          descriptionKey: "validationMessageDescriptionDynamicLookupEntityParameterRefConstantVariable",
          message:
            "Variable 'REPORTING_CURRENCY' lookup entity parameter mapping is dynamic mapping to a non-hidden variable.",
          messageKey: "validationMessageDynamicLookupEntityParameterRefConstantVariable",
          parameters: ["REPORTING_CURRENCY", "COUNTRY1", "COUNTRY"],
          propertyPath: "/variables/REPORTING_CURRENCY/targetEntityParam/COUNTRY",
          type: ValidationMessageType.ERROR,
          validatorName: "VariableValidator",
        },
        {
          message: "Variable 'COUNTRY1' result column data type does not match",
          messageKey: "validationMessageLookupEntityResultColumnType",
          descriptionKey: "validationDescriptionMessageLookupEntityResultColumnType",
          type: ValidationMessageType.WARN,
          parameters: ["COUNTRY1", "Country", "String(5)", "CurrencyOfCountry_Copy", "COUNTRY1", "unknown"],
          propertyPath: "/variables/COUNTRY1/resultElement",
          validatorName: "VariableValidator",
        },
      ]);
    });

    it("should return an error if a dynamic variable is mapped to a variable that doesn't exist", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo43.businessLayerDefinitions.TEST_NO_43);
      const definitions = _.cloneDeep(amCsnMockNo43.definitions);
      await validate(analyticModel, definitions, [
        {
          message: "Variable 'REPORTING_CURRENCY' lookup entity parameter mapping refers to non existing variable",
          messageKey: "validationMessageLookupEntityParameterRefNonExistingVariable",
          descriptionKey: "validationMessageDescriptionLookupEntityParameterRefNonExistingVariable",
          type: ValidationMessageType.ERROR,
          parameters: ["REPORTING_CURRENCY"],
          propertyPath: "/variables/REPORTING_CURRENCY",
          validatorName: "VariableValidator",
        },
        {
          message: "Variable 'COUNTRY' is not used",
          messageKey: "validationMessageInputParameterMappingMissing",
          descriptionKey: "validationDescriptionInputParameterMappingMissing",
          type: ValidationMessageType.ERROR,
          parameters: ["COUNTRY"],
          propertyPath: "/variables/COUNTRY",
          validatorName: "VariableValidator",
        },
      ]);
    });

    it("should return an error if the variables are mapped in a LOOP, creating a cyclic dependency", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo42.businessLayerDefinitions.TEST_NO_42);
      const definitions = _.cloneDeep(amCsnMockNo42.definitions);
      await validate(analyticModel, definitions, [
        {
          message: "Variable 'REPORTING_CURRENCY' has a cyclic dependency.",
          messageKey: "validationMessageParameterCyclicDependency",
          descriptionKey: "validationDescriptionParameterCyclicDependency",
          type: ValidationMessageType.ERROR,
          parameters: ["REPORTING_CURRENCY", "COUNTRY1"],
          propertyPath: "/variables/REPORTING_CURRENCY/targetEntityParam/COUNTRY1",
          validatorName: "VariableValidator",
        },
        {
          message: "Variable 'COUNTRY1' has a cyclic dependency.",
          messageKey: "validationMessageParameterCyclicDependency",
          descriptionKey: "validationDescriptionParameterCyclicDependency",
          type: ValidationMessageType.ERROR,
          parameters: ["COUNTRY1", "COUNTRY"],
          propertyPath: "/variables/COUNTRY1/targetEntityParam/COUNTRY",
          validatorName: "VariableValidator",
        },
        {
          message: "Variable 'COUNTRY' has a cyclic dependency.",
          messageKey: "validationMessageParameterCyclicDependency",
          descriptionKey: "validationDescriptionParameterCyclicDependency",
          type: ValidationMessageType.ERROR,
          parameters: ["COUNTRY", "REPORTING_CURRENCY"],
          propertyPath: "/variables/COUNTRY/targetEntityParam/REPORTING_CURRENCY",
          validatorName: "VariableValidator",
        },
        {
          message: "Variable 'COUNTRY' lookup entity parameter mapping is dynamic mapping to a non-hidden variable.",
          messageKey: "validationMessageDynamicLookupEntityParameterRefConstantVariable",
          descriptionKey: "validationMessageDescriptionDynamicLookupEntityParameterRefConstantVariable",
          type: ValidationMessageType.ERROR,
          parameters: ["COUNTRY", "REPORTING_CURRENCY", "COUNTRY"],
          propertyPath: "/variables/COUNTRY/targetEntityParam/COUNTRY",
          validatorName: "VariableValidator",
        },
        {
          message: "Variable 'COUNTRY1' result column data type does not match",
          messageKey: "validationMessageLookupEntityResultColumnType",
          descriptionKey: "validationDescriptionMessageLookupEntityResultColumnType",
          type: ValidationMessageType.WARN,
          parameters: ["COUNTRY1", "Country", "String(5)", "CurrencyOfCountry_Copy", "COUNTRY1", "unknown"],
          propertyPath: "/variables/COUNTRY1/resultElement",
          validatorName: "VariableValidator",
        },
        {
          message: "Variable 'COUNTRY' result column data type does not match",
          messageKey: "validationMessageLookupEntityResultColumnType",
          descriptionKey: "validationDescriptionMessageLookupEntityResultColumnType",
          type: ValidationMessageType.WARN,
          parameters: ["COUNTRY", "Country", "String(5)", "CurrencyOfCountry_Copy", "COUNTRY", "unknown"],
          propertyPath: "/variables/COUNTRY/resultElement",
          validatorName: "VariableValidator",
        },
      ]);
    });

    it("should not return a warning if interval variable has both values", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);

      analyticModel.variables!.ReceivingCostCenter = {
        referenceAttribute: "ReceivingCostCenter",
        parameterType: AnalyticModelParameterType.StoryFilter,
        selectionType: AnalyticModelVariableSelectionType.INTERVAL,
        multipleSelections: false,
        mandatory: false,
        order: 3,
        defaultValue: {
          lowValue: "40",
          highValue: "42",
        },
      };
      await validate(analyticModel, definitions, []);
    });

    it("should not return a warning if interval variable has no values", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);

      analyticModel.variables!.ReceivingCostCenter = {
        referenceAttribute: "ReceivingCostCenter",
        parameterType: AnalyticModelParameterType.StoryFilter,
        selectionType: AnalyticModelVariableSelectionType.INTERVAL,
        multipleSelections: false,
        mandatory: false,
        order: 3,
        defaultValue: {
          lowValue: "",
          highValue: "",
        },
      };
      await validate(analyticModel, definitions, []);

      analyticModel.variables!.ReceivingCostCenter = {
        referenceAttribute: "ReceivingCostCenter",
        parameterType: AnalyticModelParameterType.StoryFilter,
        selectionType: AnalyticModelVariableSelectionType.INTERVAL,
        multipleSelections: false,
        mandatory: false,
        order: 3,
      };
      await validate(analyticModel, definitions, []);
    });

    it("should return a warning if interval variable has only to value", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);

      analyticModel.variables!.ReceivingCostCenter = {
        referenceAttribute: "ReceivingCostCenter",
        parameterType: AnalyticModelParameterType.StoryFilter,
        selectionType: AnalyticModelVariableSelectionType.INTERVAL,
        multipleSelections: false,
        mandatory: false,
        order: 3,
        defaultValue: {
          lowValue: "",
          highValue: "42",
        },
      };
      await validate(analyticModel, definitions, [
        {
          message: 'Variable ReceivingCostCenter needs to have "From" and "To" value set',
          messageKey: "validationMessageVariableFromOrToValueMissing",
          descriptionKey: "validationDescriptionVariableFromOrToValueMissing",
          type: ValidationMessageType.WARN,
          parameters: ["ReceivingCostCenter"],
          propertyPath: "/variables/ReceivingCostCenter/defaultValue",
          validatorName: "VariableValidator",
        },
      ]);
    });

    it("should return a warning if interval variable has only from value", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);

      analyticModel.variables!.ReceivingCostCenter = {
        referenceAttribute: "ReceivingCostCenter",
        parameterType: AnalyticModelParameterType.StoryFilter,
        selectionType: AnalyticModelVariableSelectionType.INTERVAL,
        multipleSelections: false,
        mandatory: false,
        order: 3,
        defaultValue: {
          lowValue: "42",
          highValue: "",
        },
      };
      await validate(analyticModel, definitions, [
        {
          message: 'Variable ReceivingCostCenter needs to have "From" and "To" value set',
          messageKey: "validationMessageVariableFromOrToValueMissing",
          descriptionKey: "validationDescriptionVariableFromOrToValueMissing",
          type: ValidationMessageType.WARN,
          parameters: ["ReceivingCostCenter"],
          propertyPath: "/variables/ReceivingCostCenter/defaultValue",
          validatorName: "VariableValidator",
        },
      ]);
    });

    it("should return a warning if range variable has only to value", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);

      analyticModel.variables!.ReceivingCostCenter = {
        referenceAttribute: "ReceivingCostCenter",
        parameterType: AnalyticModelParameterType.StoryFilter,
        selectionType: AnalyticModelVariableSelectionType.RANGE,
        multipleSelections: true,
        mandatory: false,
        order: 4,
        defaultValue: [
          {
            lowValue: "",
            highValue: "BB",
            sign: AnalyticModelDefaultRangeSign.INCLUDE,
            option: AnalyticModelDefaultRangeOption.BT,
          },
        ],
      };
      await validate(analyticModel, definitions, [
        {
          message: 'Variable ReceivingCostCenter needs to have "From" and "To" value set',
          messageKey: "validationMessageVariableFromOrToValueMissing",
          descriptionKey: "validationDescriptionVariableFromOrToValueMissing",
          type: ValidationMessageType.WARN,
          parameters: ["ReceivingCostCenter"],
          propertyPath: "/variables/ReceivingCostCenter/defaultValue/0",
          validatorName: "VariableValidator",
        },
      ]);
    });

    it("should return a warning if range variable has only from value", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);

      analyticModel.variables!.ReceivingCostCenter = {
        referenceAttribute: "ReceivingCostCenter",
        parameterType: AnalyticModelParameterType.StoryFilter,
        selectionType: AnalyticModelVariableSelectionType.RANGE,
        multipleSelections: true,
        mandatory: false,
        order: 4,
        defaultValue: [
          {
            lowValue: "AA",
            highValue: "",
            sign: AnalyticModelDefaultRangeSign.INCLUDE,
            option: AnalyticModelDefaultRangeOption.BT,
          },
        ],
      };
      await validate(analyticModel, definitions, [
        {
          message: 'Variable ReceivingCostCenter needs to have "From" and "To" value set',
          messageKey: "validationMessageVariableFromOrToValueMissing",
          descriptionKey: "validationDescriptionVariableFromOrToValueMissing",
          type: ValidationMessageType.WARN,
          parameters: ["ReceivingCostCenter"],
          propertyPath: "/variables/ReceivingCostCenter/defaultValue/0",
          validatorName: "VariableValidator",
        },
      ]);
    });

    it("should return only one warning if there is more than one invalid value", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);

      analyticModel.variables!.ReceivingCostCenter = {
        referenceAttribute: "ReceivingCostCenter",
        parameterType: AnalyticModelParameterType.StoryFilter,
        selectionType: AnalyticModelVariableSelectionType.RANGE,
        multipleSelections: true,
        mandatory: false,
        order: 4,
        defaultValue: [
          {
            lowValue: "",
            highValue: "",
            sign: AnalyticModelDefaultRangeSign.INCLUDE,
            option: AnalyticModelDefaultRangeOption.BT,
          },
          {
            lowValue: "AA",
            highValue: "",
            sign: AnalyticModelDefaultRangeSign.INCLUDE,
            option: AnalyticModelDefaultRangeOption.BT,
          },
          {
            lowValue: "",
            highValue: "BB",
            sign: AnalyticModelDefaultRangeSign.INCLUDE,
            option: AnalyticModelDefaultRangeOption.BT,
          },
        ],
      };
      await validate(analyticModel, definitions, [
        {
          message: 'Variable ReceivingCostCenter needs to have "From" and "To" value set',
          messageKey: "validationMessageVariableFromOrToValueMissing",
          descriptionKey: "validationDescriptionVariableFromOrToValueMissing",
          type: ValidationMessageType.WARN,
          parameters: ["ReceivingCostCenter"],
          propertyPath: "/variables/ReceivingCostCenter/defaultValue/1",
          validatorName: "VariableValidator",
        },
      ]);
    });

    it("should not return a warning if range variable has from and to values", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);

      analyticModel.variables!.ReceivingCostCenter = {
        referenceAttribute: "ReceivingCostCenter",
        parameterType: AnalyticModelParameterType.StoryFilter,
        selectionType: AnalyticModelVariableSelectionType.RANGE,
        multipleSelections: true,
        mandatory: false,
        order: 4,
        defaultValue: [
          {
            lowValue: "AA",
            highValue: "BB",
            sign: AnalyticModelDefaultRangeSign.INCLUDE,
            option: AnalyticModelDefaultRangeOption.BT,
          },
        ],
      };
      await validate(analyticModel, definitions, []);
    });

    it("should not return a warning if range variable has both values empty", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);

      analyticModel.variables!.ReceivingCostCenter = {
        referenceAttribute: "ReceivingCostCenter",
        parameterType: AnalyticModelParameterType.StoryFilter,
        selectionType: AnalyticModelVariableSelectionType.RANGE,
        multipleSelections: true,
        mandatory: false,
        order: 4,
        defaultValue: [
          {
            lowValue: "",
            highValue: "",
            sign: AnalyticModelDefaultRangeSign.INCLUDE,
            option: AnalyticModelDefaultRangeOption.BT,
          },
        ],
      };
      await validate(analyticModel, definitions, []);
    });

    it("should return an error if story filter or filter variable refers to a boolean dimension with non single value filter type", async () => {
      const analyticModel = _.cloneDeep(analyticModelWithBooleanAttribute);
      const definitions = _.cloneDeep(factSourceWithBooleanDimension.definitions);
      await validate(analyticModel, definitions, [
        {
          message: "Variable 'Calc_Bool' filters a boolean attribute and should use single value filter type",
          messageKey: "validationMessageVariableFiltersBooleanAttributeWithoutSingleValueFilter",
          descriptionKey: "validationDescriptionVariableFiltersBooleanAttributeWithoutSingleValueFilter",
          type: ValidationMessageType.ERROR,
          parameters: ["Calc_Bool", "Calc_Bool"],
          propertyPath: "/variables/Calc_Bool",
          validatorName: "VariableValidator",
        },
        {
          message:
            "Variable 'RESTRICTED_MEASURE_VARIABLE_1' filters a boolean attribute and should use single value filter type",
          messageKey: "validationMessageVariableFiltersBooleanAttributeWithoutSingleValueFilter",
          descriptionKey: "validationDescriptionVariableFiltersBooleanAttributeWithoutSingleValueFilter",
          type: ValidationMessageType.ERROR,
          parameters: ["RESTRICTED_MEASURE_VARIABLE_1", "Calc_Bool"],
          propertyPath: "/variables/RESTRICTED_MEASURE_VARIABLE_1",
          validatorName: "VariableValidator",
        },
      ]);
    });

    it("should return no errors if story filter or filter variable refers to a boolean dimension with single value filter type", async () => {
      const analyticModel = _.cloneDeep(analyticModelWithBooleanAttribute);
      const definitions = _.cloneDeep(factSourceWithBooleanDimension.definitions);
      (analyticModel.variables!.Calc_Bool as IAnalyticModelFilterSelection).multipleSelections = false;
      (analyticModel.variables!.Calc_Bool as IAnalyticModelFilterSelection).selectionType =
        AnalyticModelVariableSelectionType.SINGLE;
      (analyticModel.variables!.RESTRICTED_MEASURE_VARIABLE_1 as IAnalyticModelFilterSelection).selectionType =
        AnalyticModelVariableSelectionType.SINGLE;
      await validate(analyticModel, definitions, []);
    });

    it("should return an error if range variable has lowValue > highValue", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);

      analyticModel.variables!.ReceivingCostCenter = {
        referenceAttribute: "ReceivingCostCenter",
        parameterType: AnalyticModelParameterType.StoryFilter,
        selectionType: AnalyticModelVariableSelectionType.RANGE,
        multipleSelections: true,
        mandatory: false,
        order: 4,
        defaultValue: [
          {
            lowValue: "1",
            highValue: "2",
            sign: AnalyticModelDefaultRangeSign.INCLUDE,
            option: AnalyticModelDefaultRangeOption.BT,
          },
          {
            lowValue: "3",
            highValue: "2",
            sign: AnalyticModelDefaultRangeSign.INCLUDE,
            option: AnalyticModelDefaultRangeOption.BT,
          },
          {
            lowValue: "4",
            highValue: "5",
            sign: AnalyticModelDefaultRangeSign.INCLUDE,
            option: AnalyticModelDefaultRangeOption.BT,
          },
        ],
      };
      await validate(analyticModel, definitions, [
        {
          message: "Variable ReceivingCostCenter does not have a valid default value",
          messageKey: "validationMessageVariableDefaultValue",
          descriptionKey: "validationMessageVariableDefaultValue",
          type: ValidationMessageType.ERROR,
          parameters: ["ReceivingCostCenter"],
          propertyPath: "/variables/ReceivingCostCenter/defaultValue",
          validatorName: "VariableValidator",
        },
      ]);
    });

    it("should return no error if range variable has lowValue <= highValue", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);

      analyticModel.variables!.ReceivingCostCenter = {
        referenceAttribute: "ReceivingCostCenter",
        parameterType: AnalyticModelParameterType.StoryFilter,
        selectionType: AnalyticModelVariableSelectionType.RANGE,
        multipleSelections: true,
        mandatory: false,
        order: 4,
        defaultValue: [
          {
            lowValue: "1",
            highValue: "2",
            sign: AnalyticModelDefaultRangeSign.INCLUDE,
            option: AnalyticModelDefaultRangeOption.BT,
          },
          {
            lowValue: "3",
            highValue: "4",
            sign: AnalyticModelDefaultRangeSign.INCLUDE,
            option: AnalyticModelDefaultRangeOption.BT,
          },
          {
            lowValue: "8",
            sign: AnalyticModelDefaultRangeSign.INCLUDE,
            option: AnalyticModelDefaultRangeOption.EQ,
          },
        ],
      };
      await validate(analyticModel, definitions, []);
    });

    it("should return an error if standard variable has default value's length > Data Type's length ", async () => {
      const analyticModel = _.cloneDeep(amCsnMockNo35.businessLayerDefinitions.TEST_NO_35);
      const definitions = _.cloneDeep(amCsnMockNo35.definitions);

      analyticModel.variables!.STANDARD_VARIABLE = {
        text: "Standard Variable",
        parameterType: AnalyticModelParameterType.Input,
        variableProcessingType: AnalyticModelVariableProcessingType.MANUAL_INPUT,
        order: 1,
        dataType: {
          type: "cds.String",
          length: 1,
        },
        defaultValue: "test",
      };
      analyticModel.measures!.Cal_Measure = {
        text: "Calculated Measure4",
        measureType: AnalyticModelMeasureType.CalculatedMeasure,
        isAuxiliary: false,
        formulaRaw: ":STANDARD_VARIABLE",
        formula: "[0]",
        elements: {
          "0": {
            operandType: AnalyticModelCalculatedMeasureOperandType.FormulaVariable,
            key: "STANDARD_VARIABLE",
          },
        },
      };
      await validate(analyticModel, definitions, [
        {
          message: "Variable STANDARD_VARIABLE does not have a valid default value",
          messageKey: "validationMessageVariableDefaultValue",
          descriptionKey: "validationMessageVariableDefaultValue",
          type: ValidationMessageType.ERROR,
          parameters: ["STANDARD_VARIABLE"],
          propertyPath: "/variables/STANDARD_VARIABLE/defaultValue",
          validatorName: "VariableValidator",
        },
      ]);
    });
  });

  context("Story filter variable checks in stack model", () => {
    it("1a# Validate stacked story filter variable: Should return error when one source mapped in more than one filter variable", async () => {
      const analyticModel = _.cloneDeep(stackedFilterVariables_1a.businessLayerDefinitions.TEST_NO_47);
      const definitions = _.cloneDeep(stackedFilterVariables_1a.definitions);
      const expectedResult = [
        {
          message:
            "Filter variable ('Fir_Manager1') already exists in the underlying analytic model ('GC_AM_VARIABLE_ORDER_STORY_FILTER')",
          messageKey: "validationMessageFilterVariable",
          descriptionKey: "validationDescriptionFilterVariable",
          type: ValidationMessageType.ERROR,
          parameters: ["Fir_Manager1", "GC_AM_VARIABLE_ORDER_STORY_FILTER"],
          propertyPath: "/variables/Fir_Manager1",
          validatorName: "VariableValidator",
        },
        {
          message:
            "Filter variable ('Opportunity') already exists in the underlying analytic model ('GC_AM_VARIABLE_ORDER_STORY_FILTER')",
          messageKey: "validationMessageFilterVariable",
          descriptionKey: "validationDescriptionFilterVariable",
          type: ValidationMessageType.ERROR,
          parameters: ["Opportunity", "GC_AM_VARIABLE_ORDER_STORY_FILTER"],
          propertyPath: "/variables/Opportunity",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });
    it("1b# Validate stacked story filter variable with compound keys: Should return error when one source mapped in more than one filter variable", async () => {
      const analyticModel = _.cloneDeep(stackedFilterVariables_1b.businessLayerDefinitions.TEST_NO_49);
      const definitions = _.cloneDeep(stackedFilterVariables_1b.definitions);
      const expectedResult = [
        {
          message:
            "Filter variable ('ParentControllingArea_Sending_') already exists in the underlying analytic model ('GC_AM_VARIABLE_ORDER_2')",
          messageKey: "validationMessageFilterVariable",
          descriptionKey: "validationDescriptionFilterVariable",
          type: ValidationMessageType.ERROR,
          parameters: ["ParentControllingArea_Sending_", "GC_AM_VARIABLE_ORDER_2"],
          propertyPath: "/variables/ParentControllingArea_Sending_",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });
    it("2a# Validate stacked variable with Range and Interval filter type: Should return error when variable is used in the expression with Range/Interval value", async () => {
      const analyticModel = _.cloneDeep(stackedVariablesUsedInExpression.businessLayerDefinitions.TEST_NO_50);
      const definitions = _.cloneDeep(stackedVariablesUsedInExpression.definitions);
      const expectedResult = [
        {
          message: "Variable RESTRICTED_VARIABLE does not have a valid default value",
          messageKey: "validationMessageVariableDefaultValue",
          descriptionKey: "validationDescriptionVariableDefaultValue",
          type: ValidationMessageType.ERROR,
          parameters: ["RESTRICTED_VARIABLE", "Measure_W_Variable"],
          propertyPath: "/sourceModel/factSources/Variable_Used_In_Exp/parameterMappings/RESTRICTED_VARIABLE",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });
    it("2b# Validate stacked variable with Range and Interval filter type: No error when variable is used in the expression with single value", async () => {
      const analyticModel = _.cloneDeep(stackedVariablesUsedInExpression.businessLayerDefinitions.TEST_NO_50);
      analyticModel.sourceModel.factSources.Variable_Used_In_Exp.parameterMappings = {
        RESTRICTED_VARIABLE: {
          mappingType: AnalyticModelSourceParameterMappingType.ConstantValues,
          constantValue: ["PR-1012", "PR-1014"],
        },
      };
      const definitions = _.cloneDeep(stackedVariablesUsedInExpression.definitions);
      const expectedResult = [];
      await validate(analyticModel, definitions, expectedResult);
    });
  });

  context("Measure checks in stack model", () => {
    it("#1 Validate stacked calculated measure: No error when calculated measure in the base model has the used dimensions added to the exception aggregation dimensions ", async () => {
      const analyticModel = _.cloneDeep(stackedCalculatedMeasureWithAggregation.businessLayerDefinitions.TEST_NO_46);
      const definitions = _.cloneDeep(stackedCalculatedMeasureWithAggregation.definitions);
      await validate(analyticModel, definitions, []);
    });

    it("#2 Validate stacked calculated measure: Should return error when calculated measure in the base model has the used dimensions and the dimension has no exception aggregation or not include in stack model", async () => {
      const analyticModel = _.cloneDeep(stackedCalculatedMeasure.businessLayerDefinitions.TEST_NO_48);
      const definitions = _.cloneDeep(stackedCalculatedMeasure.definitions);
      const expectedResult = [
        {
          message: "Calculated measure 'Calculated_Measure' has no valid expression",
          messageKey: "validationMessageStackCalculatedMeasureUseDimension",
          descriptionKey: "validationDescriptionStackCalculatedMeasureUseDimension",
          type: ValidationMessageType.ERROR,
          parameters: ["Calculated_Measure", "Category_PROD_ID"],
          propertyPath: "/measures/Calculated_Measure/expression",
          validatorName: "MeasureValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });
  });

  context("X_OF_DIMENSION exception aggregation type validations", () => {
    it("should return error for all measure types with DWCO_MODELING_AM_UNBOOKED_EXCEPTION_AGGREGATION deactivated", async () => {
      const analyticModel = _.cloneDeep(
        MeasureValidationMock.businessLayerDefinitions.First_Last_and_Average_of_Dimension
      );
      const definitions = _.cloneDeep(MeasureValidationMock.definitions);
      const expectedResult = [
        {
          message: "Exception aggregation type 'AVERAGE OF DIMENSION' of 'Value' is invalid",
          messageKey: "validationExceptionAggregationUnsupportedType",
          descriptionKey: "validationExceptionAggregationUnsupportedTypeDescription",
          type: ValidationMessageType.ERROR,
          parameters: ["AVERAGE OF DIMENSION", "AnalyticModelMeasureType.FactSourceMeasure"],
          propertyPath: "/measures/Value/exceptionAggregationDimensions",
          validatorName: "MeasureValidator",
        },
      ];
      const featureFlagFn: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> => {
        if (featureFlag === "DWCO_MODELING_AM_UNBOOKED_EXCEPTION_AGGREGATION") {
          return false;
        }
        return true;
      };

      await validate(analyticModel, definitions, expectedResult, featureFlagFn);
    });

    context("DWCO_MODELING_AM_UNBOOKED_EXCEPTION_AGGREGATION activated", () => {
      it("should not return error for Fact Source measures", async () => {
        const analyticModel = _.cloneDeep(
          MeasureValidationMock.businessLayerDefinitions.First_Last_and_Average_of_Dimension
        );
        const definitions = _.cloneDeep(MeasureValidationMock.definitions);
        const expectedResult = [];

        await validate(analyticModel, definitions, expectedResult);
      });

      it("should return error if restricted measure has a source measure with exception aggregation defined", async () => {
        const analyticModel = _.cloneDeep(
          MeasureValidationMock.businessLayerDefinitions.First_Last_and_Average_of_Dimension
        );
        analyticModel.measures.Restricted_Measure = {
          text: "Restricted Measure",
          measureType: "AnalyticModelMeasureType.RestrictedMeasure",
          isAuxiliary: false,
          key: "Value",
          constantSelectionType: "AnalyticModelConstantSelectionType.None",
          formula: "",
          formulaRaw: "",
          elements: {},
          exceptionAggregationType: "AnalyticModelExceptionAggregationType.AVERAGE_OF_DIMENSION",
          exceptionAggregationAttributes: ["ExpectedClosingDate_Opportunit"],
        };
        const definitions = _.cloneDeep(MeasureValidationMock.definitions);
        definitions.First_Last_and_Average_of_Dimension.elements.Restricted_Measure = {
          "@EndUserText.label": "Restricted Measure",
          "@AnalyticsDetails.measureType": {
            "#": "RESTRICTION",
          },
          "@AnalyticsDetails.exceptionAggregationSteps": [
            {
              exceptionAggregationBehavior: {
                "#": "AVERAGE_OF_DIMENSION",
              },
              exceptionAggregationElements: ["ExpectedClosingDate_Opportunit"],
            },
          ],
        };
        definitions.First_Last_and_Average_of_Dimension.query.SELECT.columns.push({
          ref: ["$projection", "Value"],
          as: "Restricted_Measure",
        });
        const expectedResult = [
          {
            message: "Source measure 'Value' of restricted measure 'Restricted_Measure' has exception aggregation",
            messageKey: "validationExceptionAggregationSourceHasExceptionAggregation",
            descriptionKey: "validationExceptionAggregationSourceHasExceptionAggregationDescription",
            type: ValidationMessageType.ERROR,
            parameters: ["Value", "AVERAGE OF DIMENSION"],
            propertyPath: "/measures/Restricted_Measure/exceptionAggregationType",
            validatorName: "MeasureValidator",
          },
        ];
        await validate(analyticModel, definitions, expectedResult);
      });

      it("should return error for non-fact source measure or non-restricted measure", async () => {
        const analyticModel = _.cloneDeep(
          MeasureValidationMock.businessLayerDefinitions.First_Last_and_Average_of_Dimension
        );
        analyticModel.measures.Calculated_Measure = {
          text: "Restricted Measure",
          measureType: "AnalyticModelMeasureType.CalculatedMeasure",
          isAuxiliary: false,
          constantSelectionType: "AnalyticModelConstantSelectionType.None",
          formula: "[0]",
          formulaRaw: "1",
          elements: {
            operandType: "AnalyticModelCalculatedMeasureOperandType.ConstantValue",
            value: 1,
          },
          exceptionAggregationType: "AnalyticModelExceptionAggregationType.AVERAGE_OF_DIMENSION",
          exceptionAggregationAttributes: ["ExpectedClosingDate_Opportunit"],
        };
        const definitions = _.cloneDeep(MeasureValidationMock.definitions);
        definitions.First_Last_and_Average_of_Dimension.elements.Calculated_Measure = {
          "@EndUserText.label": "Calculated Measure",
          "@AnalyticsDetails.measureType": {
            "#": "CALCULATION",
          },
          "@Aggregation.default": {
            "#": "FORMULA",
          },
          "@AnalyticsDetails.exceptionAggregationSteps": [
            {
              exceptionAggregationBehavior: {
                "#": "AVERAGE_OF_DIMENSION",
              },
              exceptionAggregationElements: ["ExpectedClosingDate_Opportunit"],
            },
          ],
        };
        definitions.First_Last_and_Average_of_Dimension.query.SELECT.columns.push({
          ref: ["$projection", "Value"],
          as: "Calculated_Measure",
        });
        const expectedResult = [
          {
            message: "Exception aggregation type 'AVERAGE OF DIMENSION' of 'Calculated_Measure' is invalid",
            messageKey: "validationExceptionAggregationUnsupportedType",
            descriptionKey: "validationExceptionAggregationUnsupportedTypeDescription",
            type: ValidationMessageType.ERROR,
            parameters: ["AVERAGE OF DIMENSION", "AnalyticModelMeasureType.CalculatedMeasure"],
            propertyPath: "/measures/Calculated_Measure/exceptionAggregationDimensions",
            validatorName: "MeasureValidator",
          },
        ];
        await validate(analyticModel, definitions, expectedResult);
      });
    });
  });

  context("Variable name conflicts for stacked models", () => {
    const createVariable = (analyticModel, variableName) => {
      analyticModel.variables = {
        [variableName]: {
          text: "Standard Variable 1",
          parameterType: "AnalyticModelParameterType.Input",
          variableProcessingType: "AnalyticModelVariableProcessingType.ManualInput",
          order: 1,
          dataType: {
            type: "cds.Integer",
          },
        },
      };
      analyticModel.measures.Calculated_Measure4 = {
        text: "Calculated Measure4",
        measureType: "AnalyticModelMeasureType.CalculatedMeasure",
        isAuxiliary: false,
        formulaRaw: `:${variableName}`,
        formula: "[0]",
        elements: {
          "0": {
            operandType: "AnalyticModelCalculatedMeasureOperandType.FormulaVariable",
            key: variableName,
          },
        },
      };
    };

    const removeMeasure = (analyticModel, measureName) => {
      delete analyticModel.measures[measureName];
    };

    it("should not return error or warning if there are no variables in stacked analytic model", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [];

      await validate(analyticModel, definitions, expectedResult);
    });

    it("should not return error or warning if there are variables with different names in stacked analytic model", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      analyticModel.variables = {
        STANDARD_VARIABLE_1: {
          text: "Standard Variable 1",
          parameterType: "AnalyticModelParameterType.Input",
          variableProcessingType: "AnalyticModelVariableProcessingType.ManualInput",
          order: 1,
          dataType: {
            type: "cds.Integer",
          },
        },
        RESTRICTED_MEASURE_VARIABLE_2: {
          text: "Restricted Measure Variable 1",
          parameterType: "AnalyticModelParameterType.Filter",
          referenceAttribute: "ItemID",
          selectionType: "AnalyticModelVariableSelectionType.SINGLE",
          variableProcessingType: "AnalyticModelVariableProcessingType.ManualInput",
          multipleSelections: false,
          mandatory: false,
          order: 2,
        },
        ItemID: {
          referenceAttribute: "ItemID",
          parameterType: "AnalyticModelParameterType.StoryFilter",
          selectionType: "AnalyticModelVariableSelectionType.SINGLE",
          variableProcessingType: "AnalyticModelVariableProcessingType.ManualInput",
          multipleSelections: false,
          mandatory: false,
          order: 3,
        },
      };
      analyticModel.measures.Calculated_Measure4 = {
        text: "Calculated Measure4",
        measureType: "AnalyticModelMeasureType.CalculatedMeasure",
        isAuxiliary: false,
        formulaRaw: ":STANDARD_VARIABLE_1",
        formula: "[0]",
        elements: {
          "0": {
            operandType: "AnalyticModelCalculatedMeasureOperandType.FormulaVariable",
            key: "STANDARD_VARIABLE_1",
          },
        },
      };
      analyticModel.measures.Restricted_Measure2 = {
        text: "Restricted Measure2",
        measureType: "AnalyticModelMeasureType.RestrictedMeasure",
        isAuxiliary: false,
        key: "Quantity",
        constantSelectionType: "AnalyticModelConstantSelectionType.None",
        formula: "[0] = [1]",
        formulaRaw: "ItemID = :RESTRICTED_MEASURE_VARIABLE_2",
        elements: {
          "0": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.Attribute",
            key: "ItemID",
          },
          "1": {
            operandType: "AnalyticModelRestrictedMeasureOperandType.FilterVariable",
            key: "RESTRICTED_MEASURE_VARIABLE_2",
          },
        },
      };
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [];

      await validate(analyticModel, definitions, expectedResult);
    });

    it("should return error for name conflict with reference date variable from underlying model", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      createVariable(analyticModel, "REFERENCE_DATE");
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [
        {
          message: "Variable name 'REFERENCE_DATE' is not unique",
          messageKey: "validationMessageStackedVariableNameNotUnique",
          descriptionKey: "validationDescriptionStackedVariableNameNotUnique",
          type: ValidationMessageType.ERROR,
          parameters: ["REFERENCE_DATE"],
          propertyPath: "/variables/REFERENCE_DATE",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });

    it("should return error for name conflict with variable used in parameter mapping in underlying model", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      createVariable(analyticModel, "PARAM_1");
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [
        {
          message: "Variable name 'PARAM_1' is not unique",
          messageKey: "validationMessageStackedVariableNameNotUnique",
          descriptionKey: "validationDescriptionStackedVariableNameNotUnique",
          type: ValidationMessageType.ERROR,
          parameters: ["PARAM_1"],
          propertyPath: "/variables/PARAM_1",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });

    it("should return error for name conflict with standard variable used in calculated measure from underlying model if measure is included", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      createVariable(analyticModel, "PARAM_2");
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [
        {
          message: "Variable name 'PARAM_2' is not unique",
          messageKey: "validationMessageStackedVariableNameNotUnique",
          descriptionKey: "validationDescriptionStackedVariableNameNotUnique",
          type: ValidationMessageType.ERROR,
          parameters: ["PARAM_2"],
          propertyPath: "/variables/PARAM_2",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });

    it("should return warning for name conflict with standard variable used in calculated measure from underlying model if measure is not included", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      createVariable(analyticModel, "PARAM_2");
      removeMeasure(analyticModel, "Calculated_Measure2");
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [
        {
          message: "Variable name 'PARAM_2' may result in conflicts",
          messageKey: "validationMessageStackedVariableNameConflict",
          descriptionKey: "validationDescriptionStackedVariableNameConflict",
          type: ValidationMessageType.WARN,
          parameters: ["PARAM_2"],
          propertyPath: "/variables/PARAM_2",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });

    it("should return error for name conflict with restricted measure variable used in restricted measure from underlying model if measure is included", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      createVariable(analyticModel, "RESTRICTED_MEASURE_VARIABLE_1");
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [
        {
          message: "Variable name 'RESTRICTED_MEASURE_VARIABLE_1' is not unique",
          messageKey: "validationMessageStackedVariableNameNotUnique",
          descriptionKey: "validationDescriptionStackedVariableNameNotUnique",
          type: ValidationMessageType.ERROR,
          parameters: ["RESTRICTED_MEASURE_VARIABLE_1"],
          propertyPath: "/variables/RESTRICTED_MEASURE_VARIABLE_1",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });

    it("should return warning for name conflict with restricted measure variable used in restricted measure from underlying model if measure is not included", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      createVariable(analyticModel, "RESTRICTED_MEASURE_VARIABLE_1");
      removeMeasure(analyticModel, "Restricted_Measure");
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [
        {
          message: "Variable name 'RESTRICTED_MEASURE_VARIABLE_1' may result in conflicts",
          messageKey: "validationMessageStackedVariableNameConflict",
          descriptionKey: "validationDescriptionStackedVariableNameConflict",
          type: ValidationMessageType.WARN,
          parameters: ["RESTRICTED_MEASURE_VARIABLE_1"],
          propertyPath: "/variables/RESTRICTED_MEASURE_VARIABLE_1",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });

    it("should return error for name conflict with standard variable used in currency conversion measure from underlying model if measure is included", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      createVariable(analyticModel, "TARGET_CURRENCY_1");
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [
        {
          message: "Variable name 'TARGET_CURRENCY_1' is not unique",
          messageKey: "validationMessageStackedVariableNameNotUnique",
          descriptionKey: "validationDescriptionStackedVariableNameNotUnique",
          type: ValidationMessageType.ERROR,
          parameters: ["TARGET_CURRENCY_1"],
          propertyPath: "/variables/TARGET_CURRENCY_1",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });

    it("should return warning for name conflict with standard variable used in currency conversion measure from underlying model if measure is not included", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      createVariable(analyticModel, "TARGET_CURRENCY_1");
      removeMeasure(analyticModel, "Currency_Conversion_Measure1");
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [
        {
          message: "Variable name 'TARGET_CURRENCY_1' may result in conflicts",
          messageKey: "validationMessageStackedVariableNameConflict",
          descriptionKey: "validationDescriptionStackedVariableNameConflict",
          type: ValidationMessageType.WARN,
          parameters: ["TARGET_CURRENCY_1"],
          propertyPath: "/variables/TARGET_CURRENCY_1",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });

    it("should return error for name conflict with standard variable used in variable derivation if variable from underlying model is inherited", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      createVariable(analyticModel, "COUNTRY1");
      removeMeasure(analyticModel, "Restriction_Calculation_var");
      removeMeasure(analyticModel, "Calculation_two_levels");
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [
        {
          message: "Variable name 'COUNTRY1' is not unique",
          messageKey: "validationMessageStackedVariableNameNotUnique",
          descriptionKey: "validationDescriptionStackedVariableNameNotUnique",
          type: ValidationMessageType.ERROR,
          parameters: ["COUNTRY1"],
          propertyPath: "/variables/COUNTRY1",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });

    it("should return warning for name conflict with standard variable used in variable derivation if variable from underlying model is not inherited or used in measure indirectly (source measure or measure for calculation)", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      createVariable(analyticModel, "COUNTRY1");
      removeMeasure(analyticModel, "Calculation_Derived_var");
      removeMeasure(analyticModel, "Restriction_Calculation_var");
      removeMeasure(analyticModel, "Calculation_two_levels");
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [
        {
          message: "Variable name 'COUNTRY1' may result in conflicts",
          messageKey: "validationMessageStackedVariableNameConflict",
          descriptionKey: "validationDescriptionStackedVariableNameConflict",
          type: ValidationMessageType.WARN,
          parameters: ["COUNTRY1"],
          propertyPath: "/variables/COUNTRY1",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });

    it("should return error for name conflict with variable from underlying model that is used in a measure indirectly (source measure from restricted measure uses the variable) if measure is included", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      createVariable(analyticModel, "COUNTRY1");
      removeMeasure(analyticModel, "Calculation_Derived_var");
      removeMeasure(analyticModel, "Calculation_two_levels");
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [
        {
          message: "Variable name 'COUNTRY1' is not unique",
          messageKey: "validationMessageStackedVariableNameNotUnique",
          descriptionKey: "validationDescriptionStackedVariableNameNotUnique",
          type: ValidationMessageType.ERROR,
          parameters: ["COUNTRY1"],
          propertyPath: "/variables/COUNTRY1",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });

    it("should return error for name conflict with variable from underlying model that is used in a measure indirectly (measure in calculated measure uses the variable) if measure is included", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      createVariable(analyticModel, "COUNTRY1");
      removeMeasure(analyticModel, "Calculation_Derived_var");
      removeMeasure(analyticModel, "Restriction_Calculation_var");
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [
        {
          message: "Variable name 'COUNTRY1' is not unique",
          messageKey: "validationMessageStackedVariableNameNotUnique",
          descriptionKey: "validationDescriptionStackedVariableNameNotUnique",
          type: ValidationMessageType.ERROR,
          parameters: ["COUNTRY1"],
          propertyPath: "/variables/COUNTRY1",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });

    it("should return error for name conflict with variable from underlying model with set value", async () => {
      const analyticModel = _.cloneDeep(
        variableNameConflictsValidationMock.businessLayerDefinitions.GC_DEPENDENCIES_VALIDATIONS_TEST_2
      );
      createVariable(analyticModel, "PARAM_2");
      analyticModel.sourceModel.factSources.GC_DEPENDENCIES_VALI.parameterMappings = {
        PARAM_2: {
          mappingType: AnalyticModelSourceParameterMappingType.ConstantValue,
          constantValue: 5,
        },
      };
      const definitions = _.cloneDeep(variableNameConflictsValidationMock.definitions);
      const expectedResult = [
        {
          message: "Variable name 'PARAM_2' is not unique",
          messageKey: "validationMessageStackedVariableNameNotUnique",
          descriptionKey: "validationDescriptionStackedVariableNameNotUnique",
          type: ValidationMessageType.ERROR,
          parameters: ["PARAM_2"],
          propertyPath: "/variables/PARAM_2",
          validatorName: "VariableValidator",
        },
      ];
      await validate(analyticModel, definitions, expectedResult);
    });
  });
});
