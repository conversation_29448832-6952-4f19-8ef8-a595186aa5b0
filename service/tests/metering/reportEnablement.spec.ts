/** @format */

import sinon from "sinon";
import { TenantClassification } from "../../../shared/provisioning/ftc/types";
import { TenantInformationProvider } from "../../featureflags/TenantInformationProvider";
import * as libNode from "../../lib/node";
import * as enablement from "../../metering/helpers";
import { RequestContext } from "../../repository/security/requestContext";
import {
  cpeaFreeTierTenant,
  cpeaStandardTenant,
  spcInternalTenant,
  spcSubscriptionTenant,
  spcTestTenant,
} from "./reporting/helpers";

describe("Define report enablement", () => {
  const context = RequestContext.createFromTenantId(libNode.testTenantUuid);
  const sandbox = sinon.createSandbox();
  let tenantInformationSub;

  const productiveCisTenantsToTest = [{ ...cpeaStandardTenant }, { ...cpeaFreeTierTenant }];

  const productiveSpcTenants = [
    {
      ...spcSubscriptionTenant,
    },
    { ...spcSubscriptionTenant, type: undefined },
  ];

  const testAndInternalTenantsToTest = [
    { ...spcInternalTenant },
    { ...spcTestTenant },
    {
      ...spcInternalTenant,
      classification: TenantClassification.DWCinternalProductive,
    },
    { ...spcInternalTenant, classification: TenantClassification.DWCpartnerTest },
    { ...spcInternalTenant, classification: TenantClassification.DWCPoC },
  ];

  const developmentPlanScenarioToTest = [
    { ...spcInternalTenant, classification: TenantClassification.Development },
    { ...spcInternalTenant, classification: "any-other-classification" },
  ];

  beforeEach(() => {
    tenantInformationSub = sandbox.stub(TenantInformationProvider, "getTenantInformation");
  });

  afterEach(() => {
    sandbox.restore();
  });
  describe("SPC enablement", () => {
    let isCanaryStub;
    before(() => {});
    beforeEach(() => {
      isCanaryStub = sandbox.stub(libNode, "isCanary");
    });

    it("Should enable report for classification DWC", async () => {
      const defineEnablementSpy = sandbox.spy(enablement, "defineEnablementOfMeteringScheduleForSubscription");
      for (const tenant of productiveSpcTenants) {
        tenantInformationSub.resolves(tenant);
        const result = await enablement.defineEnablementOfMeteringScheduleForSubscription(context, undefined as any);
        sinon.assert.match(result, false);
      }
      sinon.assert.match(defineEnablementSpy.callCount, 2);
    });

    it("Should enable report for Test and Internal tenants", async () => {
      const defineEnablementSpy = sandbox.spy(enablement, "defineEnablementOfMeteringScheduleForSubscription");
      for (const tenant of testAndInternalTenantsToTest) {
        tenantInformationSub.resolves(tenant);
        const result = await enablement.defineEnablementOfMeteringScheduleForSubscription(context, undefined as any);
        sinon.assert.match(result, false);
      }
      sinon.assert.match(defineEnablementSpy.callCount, 5);
    });

    it("Should enable report for development Tenants - CANARY", async () => {
      isCanaryStub.returns(true);
      const defineEnablementSpy = sandbox.spy(enablement, "defineEnablementOfMeteringScheduleForSubscription");
      for (const tenant of developmentPlanScenarioToTest) {
        tenantInformationSub.resolves(tenant);
        const result = await enablement.defineEnablementOfMeteringScheduleForSubscription(context, undefined as any);
        sinon.assert.match(result, false);
      }
      sinon.assert.match(defineEnablementSpy.callCount, 2);
    });

    it("Should enable report for development Tenants - NOT CANARY", async () => {
      isCanaryStub.returns(false);
      const defineEnablementSpy = sandbox.spy(enablement, "defineEnablementOfMeteringScheduleForSubscription");
      for (const tenant of developmentPlanScenarioToTest) {
        tenantInformationSub.resolves(tenant);
        const result = await enablement.defineEnablementOfMeteringScheduleForSubscription(context, undefined as any);
        sinon.assert.match(result, false);
      }
      sinon.assert.match(defineEnablementSpy.callCount, 2);
    });
  });

  describe("CiS enablement", () => {
    it("Should enable report for CiS tenants", async () => {
      const defineEnablementSpy = sandbox.spy(enablement, "defineEnablementOfMeteringScheduleForCPEA");
      for (const tenant of productiveCisTenantsToTest) {
        tenantInformationSub.resolves(tenant);
        const result = await enablement.defineEnablementOfMeteringScheduleForCPEA(context, undefined as any);
        sinon.assert.match(result, false);
      }
      sinon.assert.match(defineEnablementSpy.callCount, 2);
    });
  });
});
