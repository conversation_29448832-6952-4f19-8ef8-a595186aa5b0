/** @format */
import * as chai from "chai";
import chaiAsPromised from "chai-as-promised";
import sinon, { SinonSpy, SinonStub, SinonStubbedInstance } from "sinon";
import { Measure, TenantClassification, TenantType } from "../../../../shared/provisioning/ftc/types";
import { AnalyticsProvisioningService } from "../../../aps/AnalyticsProvisioningService";
import { store } from "../../../configurations/main";
import { FeatureFlagProvider, IFeatureFlagsMap } from "../../../featureflags/FeatureFlagProvider";
import { TenantInformationProvider } from "../../../featureflags/TenantInformationProvider";
import { testTenantUuid } from "../../../lib/node";
import { MeteringService } from "../../../metering/MeteringService";
import { MeteringUtils } from "../../../metering/MeteringUtils";
import { MetricsHandler } from "../../../metering/metrics/MetricsHandler";
import { MetricsReportingStatus } from "../../../metering/metrics/types";
import * as formatterConfig from "../../../metering/reporting/reportHandlingConfig";
import * as usageReport from "../../../metering/reporting/usageReportHandling";
import { RequestContext } from "../../../repository/security/requestContext";
import { CustomerHanaRuntimeData } from "../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { apsTenantInformation } from "../utils";
import { MockedFormatter } from "./MockedFormatter";
import { createCollectedMetrics } from "./helpers";

chai.use(chaiAsPromised);

describe("service/metering/LicenseMeteringReporting.spec.ts", () => {
  const sandbox = sinon.createSandbox();
  const context = RequestContext.createFromTenantId(testTenantUuid);
  let getTenantInformationStub: SinonStub, ffStub: SinonStub, getEffectiveValueStub: SinonStub;
  let checkReportAvailabilitySpy: SinonSpy;
  let metricsHandlerStub: SinonStubbedInstance<MetricsHandler>;

  const tenantInformation = {
    uuid: testTenantUuid,
    consumerAccountId: testTenantUuid,
    classification: TenantClassification.DWC,
    type: TenantType.DWC,
    license: {
      thresholdStorage: "256",
      thresholdDataLakeStorage: "3",
      thresholdMemory: "32",
      thresholdVCPU: "4",
    },
    crmTenantId: "00000",
  };

  const usageReportFeatureFlags: Partial<IFeatureFlagsMap> = {
    DWCO_LARGE_SYSTEMS_REQUESTS: true,
  };

  beforeEach(() => {
    sandbox.stub(Date.prototype, "toISOString").returns("2020-01-01T00:00:00.000Z");
    ffStub = sandbox.stub(FeatureFlagProvider, "getFeatureFlags");
    metricsHandlerStub = sandbox.stub(MetricsHandler.prototype);
    sandbox.stub(MeteringService.prototype, "reportUsageWithFallback").resolves();
    sandbox
      .stub(AnalyticsProvisioningService.prototype, "fetchTenantList")
      .resolves({ body: [apsTenantInformation] } as any);
    getTenantInformationStub = sandbox.stub(TenantInformationProvider, "getTenantInformation");
    checkReportAvailabilitySpy = sandbox.spy(MeteringUtils, "checkAvailability");
    getEffectiveValueStub = sandbox
      .stub(store, "getEffectiveValue")
      .resolves(
        '{"PREMIUM_OUTBOUND": true, "CATALOG_STORAGE": true, "DI_EXTRACTION_HOURS": true, "ELASTIC_COMPUTE_NODE": true, "FOUNDATION_BLOCK": true, "THRESHOLD_DATA_LAKE_STORAGE": true, "THRESHOLD_STORAGE": true, "THRESHOLD_BW_BRIDGE": true, "THRESHOLD_MEMORY": true, "LARGE_SYSTEMS_STORAGE": true, "LARGE_SYSTEMS_COMPUTE": true, "LARGE_SYSTEMS_REQUESTS": true}'
      );
  });
  afterEach(() => {
    sandbox.restore();
  });

  describe("Metrics Reporting - Configuration Service Parameter off", () => {
    beforeEach(() => {
      getTenantInformationStub.resolves(tenantInformation);
      ffStub.resolves(usageReportFeatureFlags as IFeatureFlagsMap);
      sandbox.stub(formatterConfig, "getUsageFormatterForMetric").returns(new MockedFormatter(context) as any);
      sandbox.stub(CustomerHanaRuntimeData.prototype, "getOverallAssignedStorage").resolves();
    });
    afterEach(() => {
      sandbox.restore();
    });
    it("Should not report for Premium Outbound, only for Catalog Storage.", async () => {
      getEffectiveValueStub.resolves(
        '{"PREMIUM_OUTBOUND": false, "CATALOG_STORAGE": true, "DI_EXTRACTION_HOURS": false, "ELASTIC_COMPUTE_NODE": false, "FOUNDATION_BLOCK": false, "THRESHOLD_DATA_LAKE_STORAGE": false, "THRESHOLD_STORAGE": false, "THRESHOLD_BW_BRIDGE": false, "THRESHOLD_MEMORY": false, "LARGE_SYSTEMS_STORAGE": false, "LARGE_SYSTEMS_COMPUTE": false, "LARGE_SYSTEMS_REQUESTS": false}'
      );
      const metricsToReport = createCollectedMetrics(
        Measure.CATALOG_STORAGE as Measure,
        1,
        MetricsReportingStatus.PENDING
      );
      const metricsToReport2 = createCollectedMetrics(
        Measure.CATALOG_STORAGE as Measure,
        2,
        MetricsReportingStatus.PENDING
      );

      metricsHandlerStub.getMetricsForReporting.resolves([...metricsToReport.reports, ...metricsToReport2.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await usageReport.reportMetrics(context);
      chai.expect(metricsHandlerStub.updateMetricsReportingStatus.callCount).to.be.equal(1);
      chai.expect(metricsHandlerStub.getMetricsForReporting.getCall(0).args[3]).to.be.deep.equal(["CATALOG_STORAGE"]);
    });

    it("Should not report for Catalog Storage, only for Premium Outbound.", async () => {
      getEffectiveValueStub.resolves(
        '{"PREMIUM_OUTBOUND": true, "CATALOG_STORAGE": false, "DI_EXTRACTION_HOURS": false, "ELASTIC_COMPUTE_NODE": false, "FOUNDATION_BLOCK": false, "THRESHOLD_DATA_LAKE_STORAGE": false, "THRESHOLD_STORAGE": false, "THRESHOLD_BW_BRIDGE": false, "THRESHOLD_MEMORY": false, "LARGE_SYSTEMS_STORAGE": false, "LARGE_SYSTEMS_COMPUTE": false, "LARGE_SYSTEMS_REQUESTS": false}'
      );
      const metricsToReport = createCollectedMetrics(
        Measure.PREMIUM_OUTBOUND as Measure,
        1,
        MetricsReportingStatus.PENDING
      );
      const metricsToReport2 = createCollectedMetrics(
        Measure.PREMIUM_OUTBOUND as Measure,
        2,
        MetricsReportingStatus.PENDING
      );

      metricsHandlerStub.getMetricsForReporting.resolves([...metricsToReport.reports, ...metricsToReport2.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await usageReport.reportMetrics(context);

      chai.expect(metricsHandlerStub.updateMetricsReportingStatus.callCount).to.be.equal(1);
      chai.expect(metricsHandlerStub.getMetricsForReporting.getCall(0).args[3]).to.be.deep.equal(["PREMIUM_OUTBOUND"]);
    });

    it("Should only report ECN.", async () => {
      getEffectiveValueStub.resolves(
        '{"PREMIUM_OUTBOUND": false, "CATALOG_STORAGE": false, "DI_EXTRACTION_HOURS": false, "ELASTIC_COMPUTE_NODE": true, "FOUNDATION_BLOCK": false, "THRESHOLD_DATA_LAKE_STORAGE": false,"THRESHOLD_STORAGE": false, "THRESHOLD_BW_BRIDGE": false, "THRESHOLD_MEMORY": false, "LARGE_SYSTEMS_STORAGE": false, "LARGE_SYSTEMS_COMPUTE": false, "LARGE_SYSTEMS_REQUESTS": false}'
      );
      const metricsToReport = createCollectedMetrics(
        Measure.ELASTIC_COMPUTE_NODE as Measure,
        1,
        MetricsReportingStatus.PENDING
      );
      const metricsToReport2 = createCollectedMetrics(
        Measure.ELASTIC_COMPUTE_NODE as Measure,
        2,
        MetricsReportingStatus.PENDING
      );
      const metricsToReport3 = createCollectedMetrics(
        Measure.ELASTIC_COMPUTE_NODE as Measure,
        3,
        MetricsReportingStatus.PENDING
      );
      const metricsToReport4 = createCollectedMetrics(
        Measure.ELASTIC_COMPUTE_NODE as Measure,
        4,
        MetricsReportingStatus.PENDING
      );

      metricsHandlerStub.getMetricsForReporting.resolves([
        ...metricsToReport.reports,
        ...metricsToReport2.reports,
        ...metricsToReport3.reports,
        ...metricsToReport4.reports,
      ]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await usageReport.reportMetrics(context);

      chai.expect(metricsHandlerStub.updateMetricsReportingStatus.callCount).to.be.equal(1);
      chai
        .expect(metricsHandlerStub.getMetricsForReporting.getCall(0).args[3])
        .to.be.deep.equal(["ELASTIC_COMPUTE_NODE"]);
    });

    it("Should only report for Threshold Data Lake Storage.", async () => {
      getEffectiveValueStub.resolves(
        '{"PREMIUM_OUTBOUND": false, "CATALOG_STORAGE": false, "DI_EXTRACTION_HOURS": false, "ELASTIC_COMPUTE_NODE": false, "FOUNDATION_BLOCK": false, "THRESHOLD_DATA_LAKE_STORAGE": true,"THRESHOLD_STORAGE": false, "THRESHOLD_BW_BRIDGE": false, "THRESHOLD_MEMORY": false, "LARGE_SYSTEMS_STORAGE": false, "LARGE_SYSTEMS_COMPUTE": false, "LARGE_SYSTEMS_REQUESTS": false}'
      );
      const metricsToReport = createCollectedMetrics(
        Measure.THRESHOLD_DATA_LAKE_STORAGE as Measure,
        1,
        MetricsReportingStatus.PENDING
      );
      const metricsToReport2 = createCollectedMetrics(
        Measure.THRESHOLD_DATA_LAKE_STORAGE as Measure,
        1,
        MetricsReportingStatus.PENDING
      );

      metricsHandlerStub.getMetricsForReporting.resolves([...metricsToReport.reports, ...metricsToReport2.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await usageReport.reportMetrics(context);

      chai.expect(metricsHandlerStub.updateMetricsReportingStatus.callCount).to.be.equal(1);
      chai
        .expect(metricsHandlerStub.getMetricsForReporting.getCall(0).args[3])
        .to.be.deep.equal(["THRESHOLD_DATA_LAKE_STORAGE"]);
    });

    it("Should only report for Threshold Memory.", async () => {
      getEffectiveValueStub.resolves(
        '{"PREMIUM_OUTBOUND": false, "CATALOG_STORAGE": false, "DI_EXTRACTION_HOURS": false, "ELASTIC_COMPUTE_NODE": false, "FOUNDATION_BLOCK": false, "THRESHOLD_DATA_LAKE_STORAGE": false,"THRESHOLD_STORAGE": false, "THRESHOLD_BW_BRIDGE": false, "THRESHOLD_MEMORY": true, "LARGE_SYSTEMS_STORAGE": false, "LARGE_SYSTEMS_COMPUTE": false, "LARGE_SYSTEMS_REQUESTS": false}'
      );
      const metricsToReport = createCollectedMetrics(
        Measure.THRESHOLD_MEMORY as Measure,
        1,
        MetricsReportingStatus.PENDING
      );

      metricsHandlerStub.getMetricsForReporting.resolves([...metricsToReport.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await usageReport.reportMetrics(context);

      chai.expect(metricsHandlerStub.updateMetricsReportingStatus.callCount).to.be.equal(1);
      chai.expect(metricsHandlerStub.getMetricsForReporting.getCall(0).args[3]).to.be.deep.equal(["THRESHOLD_MEMORY"]);
    });

    it("Should only report for LARGE_SYSTEMS_REQUESTS.", async () => {
      getEffectiveValueStub.resolves(
        '{"PREMIUM_OUTBOUND": false, "CATALOG_STORAGE": false, "DI_EXTRACTION_HOURS": false, "ELASTIC_COMPUTE_NODE": false, "FOUNDATION_BLOCK": false, "THRESHOLD_DATA_LAKE_STORAGE": false,"THRESHOLD_STORAGE": false, "THRESHOLD_BW_BRIDGE": false, "THRESHOLD_MEMORY": false, "LARGE_SYSTEMS_STORAGE": false, "LARGE_SYSTEMS_COMPUTE": false, "LARGE_SYSTEMS_REQUESTS": true}'
      );
      const metricsToReport = createCollectedMetrics(
        Measure.LARGE_SYSTEMS_REQUESTS as Measure,
        1,
        MetricsReportingStatus.PENDING
      );

      metricsHandlerStub.getMetricsForReporting.resolves([...metricsToReport.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await usageReport.reportMetrics(context);

      chai.expect(metricsHandlerStub.updateMetricsReportingStatus.callCount).to.be.equal(1);
      chai
        .expect(metricsHandlerStub.getMetricsForReporting.getCall(0).args[3])
        .to.be.deep.equal(["LARGE_SYSTEMS_REQUESTS"]);
    });
    it("Should not report for LARGE_SYSTEMS_REQUESTS if FF DWCO_LARGE_SYSTEMS_REQUESTS is OFF.", async () => {
      ffStub.resolves({ DWCO_LARGE_SYSTEMS_REQUESTS: false });
      getEffectiveValueStub.resolves(
        '{"PREMIUM_OUTBOUND": false, "CATALOG_STORAGE": false, "DI_EXTRACTION_HOURS": false, "ELASTIC_COMPUTE_NODE": false, "FOUNDATION_BLOCK": false, "THRESHOLD_DATA_LAKE_STORAGE": false,"THRESHOLD_STORAGE": false, "THRESHOLD_BW_BRIDGE": false, "THRESHOLD_MEMORY": false, "LARGE_SYSTEMS_STORAGE": false, "LARGE_SYSTEMS_COMPUTE": false, "LARGE_SYSTEMS_REQUESTS": true}'
      );
      const metricsToReport = createCollectedMetrics(
        Measure.LARGE_SYSTEMS_REQUESTS as Measure,
        1,
        MetricsReportingStatus.PENDING
      );
      const metricsToReport2 = createCollectedMetrics(
        Measure.LARGE_SYSTEMS_REQUESTS as Measure,
        1,
        MetricsReportingStatus.PENDING
      );

      metricsHandlerStub.getMetricsForReporting.resolves([...metricsToReport.reports, ...metricsToReport2.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await usageReport.reportMetrics(context);

      chai.expect(metricsHandlerStub.updateMetricsReportingStatus.callCount).to.be.equal(0);
    });
  });
  describe("Edge cases", () => {
    const reportEnablement = {
      PREMIUM_OUTBOUND: true,
      CATALOG_STORAGE: true,
      DI_EXTRACTION_HOURS: true,
      ELASTIC_COMPUTE_NODE: true,
      FOUNDATION_BLOCK: true,
      THRESHOLD_DATA_LAKE_STORAGE: true,
      THRESHOLD_MEMORY: true,
      THRESHOLD_STORAGE: true,
      THRESHOLD_BW_BRIDGE: true,
      LARGE_SYSTEMS_STORAGE: true,
      LARGE_SYSTEMS_COMPUTE: true,
      LARGE_SYSTEMS_REQUESTS: true,
    };

    it("Should return a parameter set to true if not a boolean value", async () => {
      getEffectiveValueStub.resolves(
        '{"PREMIUM_OUTBOUND": test, "CATALOG_STORAGE": "string", "DI_EXTRACTION_HOURS": 20, "ELASTIC_COMPUTE_NODE": true, "FOUNDATION_BLOCK": true, "THRESHOLD_DATA_LAKE_STORAGE": true, "THRESHOLD_STORAGE": true, "THRESHOLD_BW_BRIDGE": true, "THRESHOLD_MEMORY": true, "LARGE_SYSTEMS_STORAGE": true, "LARGE_SYSTEMS_COMPUTE": false, "LARGE_SYSTEMS_REQUESTS": false}'
      );
      await MeteringUtils.checkAvailability(context, "DWC_METERING_REPORT_ENABLEMENT");
      chai.expect(await checkReportAvailabilitySpy.returnValues[0]).to.eql(reportEnablement);
    });
    it("Should include missing value set to true if not listed in configStore ", async () => {
      reportEnablement.CATALOG_STORAGE = false;
      getEffectiveValueStub.resolves(
        '{"PREMIUM_OUTBOUND": true, "CATALOG_STORAGE": false, "DI_EXTRACTION_HOURS": true, "ELASTIC_COMPUTE_NODE": true}'
      );
      await MeteringUtils.checkAvailability(context, "DWC_METERING_REPORT_ENABLEMENT");
      chai.expect(await checkReportAvailabilitySpy.returnValues[0]).to.eql(reportEnablement);
    });
  });
});
