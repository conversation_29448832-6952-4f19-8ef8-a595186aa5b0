/**
 * eslint-disable no-console
 *
 * @format
 */

import chai, { expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import sinon from "sinon";
import sinonChai from "sinon-chai";

import { HistogramInstrument } from "@opentelemetry/sdk-metrics/build/src/Instruments";
import { Measure, TenantClassification, TenantType } from "../../../../shared/provisioning/ftc/types";
import { FeatureFlagProvider, IFeatureFlagsMap } from "../../../featureflags/FeatureFlagProvider";
import { TenantInformationProvider } from "../../../featureflags/TenantInformationProvider";
import { ITenantInformation } from "../../../featureflags/types";
import { testTenantUuid } from "../../../lib/node";
import { MeteringService } from "../../../metering/MeteringService";
import { MetricsReportingResponseDto, MetricsReportingStatus } from "../../../metering/metrics/types";
import * as MetricsHelpers from "../../../metering/reporting/helper";
import * as formatterConfig from "../../../metering/reporting/reportHandlingConfig";
import { metricsToReport, STATUS_TO_REPORT } from "../../../metering/reporting/reportHandlingConfig";
import { reportMetrics } from "../../../metering/reporting/usageReportHandling";
import { RequestContext } from "../../../repository/security/requestContext";
import { CustomerHanaRuntimeData } from "../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { createCollectedMetrics } from "./helpers";
//This comment prevents circular import error caused by MetricsHandler import
import { dwcMetrics, FTCCalculators } from "@sap/dwc-flexible-tenant-sizing";
import { metricsEnablement } from "../../../../shared/provisioning/ftc/types";
import { MeteringUtils } from "../../../metering/MeteringUtils";
import { MetricsHandler } from "../../../metering/metrics/MetricsHandler";
import { BwBridgeFormatter } from "../../../metering/reporting/formatters/BwBridgeFormatter";
import { ComputeFormatter } from "../../../metering/reporting/formatters/ComputeFormatter";
import { DataLakeFormatter } from "../../../metering/reporting/formatters/DataLakeFormatter";
import { DIFormatter } from "../../../metering/reporting/formatters/DIFormatter";
import { ElasticComputeNodesFormatter } from "../../../metering/reporting/formatters/ElasticComputeNodesFormatters";
import { FoundationBlockFormatter } from "../../../metering/reporting/formatters/FoundationBlockFormatter";
import { LargeSystemsComputeFormatter } from "../../../metering/reporting/formatters/LargeSystemsComputeFormatter";
import { LargeSystemsRequestsFormatter } from "../../../metering/reporting/formatters/LargeSystemsRequestsFormatter";
import { LargeSystemsStorageFormatter } from "../../../metering/reporting/formatters/LargeSystemsStorageFormatter";
import { OdcStorageFormatter } from "../../../metering/reporting/formatters/OdcStorageFormatter";
import { PremiumOutboundFormatter } from "../../../metering/reporting/formatters/PremiumOutboundFormatter";
import { StorageFormatter } from "../../../metering/reporting/formatters/StorageFormatter";
import { MockedFormatter } from "./MockedFormatter";

chai.use(sinonChai);
chai.use(chaiAsPromised);

describe("reportHandler.ts", function () {
  const context = RequestContext.createFromTenantId(testTenantUuid, {
    userInfo: { displayName: "testUser", tenantId: testTenantUuid, technical: true },
  });
  let newSandbox: sinon.SinonSandbox;
  let metricsHandlerStub;
  let reportStub: sinon.SinonStub;
  let recordOnTelemetryStub: sinon.SinonStub;
  let enableReportOldMetricsStub: sinon.SinonStub;

  const tenantInformation = {
    uuid: testTenantUuid,
    consumerAccountId: testTenantUuid,
    classification: TenantClassification.DWC,
    type: TenantType.DWC,
    license: {
      thresholdStorage: "256",
      thresholdDataLakeStorage: "5",
      thresholdMemory: "32",
      thresholdVCPU: "2",
    },
    crmTenantId: "00000",
  } as ITenantInformation;

  function prepareStubs() {
    reportStub = newSandbox.stub(MeteringService.prototype, "reportUsageWithFallback");
    metricsHandlerStub = newSandbox.stub(MetricsHandler.prototype);
    recordOnTelemetryStub = newSandbox.stub(HistogramInstrument.prototype, "record");
    enableReportOldMetricsStub = newSandbox.stub(MetricsHelpers, "isReportOldMetricsEnabled").returns(false);
    newSandbox.stub(TenantInformationProvider, "getTenantInformation").resolves(tenantInformation);
    newSandbox.stub(formatterConfig, "getUsageFormatterForMetric").returns(new MockedFormatter(context) as any);
    newSandbox.stub(CustomerHanaRuntimeData.prototype, "getOverallAssignedStorage").resolves();
    newSandbox.stub(MeteringUtils, "checkAvailability").resolves({
      PREMIUM_OUTBOUND: true,
      CATALOG_STORAGE: true,
      DI_EXTRACTION_HOURS: true,
      ELASTIC_COMPUTE_NODE: true,
      FOUNDATION_BLOCK: true,
      THRESHOLD_DATA_LAKE_STORAGE: true,
      THRESHOLD_MEMORY: true,
      THRESHOLD_STORAGE: true,
      THRESHOLD_BW_BRIDGE: true,
      LARGE_SYSTEMS_STORAGE: true,
      LARGE_SYSTEMS_COMPUTE: true,
      LARGE_SYSTEMS_REQUESTS: true,
    } as metricsEnablement);
  }

  function prepareFeatureFlags(
    features: { [key: string]: boolean } = {
      DWCO_LARGE_SYSTEMS_REQUESTS: true,
      DWCO_BILLING_METRICS_DYNATRACE_USAGE_REPORT: true,
    }
  ) {
    newSandbox.stub(FeatureFlagProvider, "getFeatureFlags").resolves(features as unknown as IFeatureFlagsMap);
  }

  function assertMetricsWereReported(orderedExpectedMetrics: Measure[]) {
    orderedExpectedMetrics.forEach((value, index) => {
      expect(metricsHandlerStub.getMetricsForReporting.getCall(index).args[3]).to.be.deep.equal([value]);
    });
    expect(metricsHandlerStub.getMetricsForReporting.getCalls().length).to.be.equal(orderedExpectedMetrics.length);
  }

  before(() => {
    newSandbox = sinon.createSandbox();
  });

  after(() => {
    newSandbox.restore();
  });

  describe("Metrics Reporting", function () {
    beforeEach(() => {
      prepareStubs();
      prepareFeatureFlags();
    });

    afterEach(() => {
      newSandbox.restore();
    });

    it("Should sucessfully format, report usage and update measure status.", async () => {
      const metricsToReport = createCollectedMetrics(Measure.PREMIUM_OUTBOUND, 1, MetricsReportingStatus.PENDING);
      reportStub.resolves(true);
      metricsHandlerStub.getMetricsForReporting.onCall(0).resolves([...metricsToReport.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();
      await reportMetrics(context, [Measure.PREMIUM_OUTBOUND]);

      const timestamp = new Date(metricsToReport.reports[0].measurementPeriodStart).toISOString();
      const calculator = new FTCCalculators.PremiumOutboundCalculator();
      const expectedUsageReport = {
        usage: [
          {
            timestamp: timestamp.substring(0, timestamp.length - 1),
            service: { id: "data-analytics", plan: "standard" },
            consumer: { environment: "CF", region: "cf-eu10-canary", subAccount: "DWC_DEV_REPO" },
            measures: [
              {
                id: "capacity_units_hour",
                value: calculator.calculateCusFromBlocks(metricsToReport.reports[0].measureValue),
              },
              { id: dwcMetrics.PREMIUM_OUTBOUND, value: metricsToReport.reports[0].measureValue },
            ],
            id: metricsToReport.ids[0],
            customDimensions: { dimension1: "00000", dimension2: "DWC" },
          },
        ],
      };

      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(0).args[1]).to.be.deep.equal(
        expectedUsageReport.usage
      );
      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(0).args[2]).to.be.deep.equal(
        MetricsReportingStatus.CONFIRMED
      );

      expect(reportStub.getCall(0).args[0]).to.be.deep.equal(expectedUsageReport);
    });

    it("Should update measure with failed status if report to MaaS fails.", async () => {
      const metricsToReport = createCollectedMetrics(
        Measure.PREMIUM_OUTBOUND as Measure,
        1,
        MetricsReportingStatus.PENDING
      );

      reportStub.throws();
      metricsHandlerStub.getMetricsForReporting.resolves([...metricsToReport.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();
      const timestamp = new Date(metricsToReport.reports[0].measurementPeriodStart).toISOString();
      const calculator = new FTCCalculators.PremiumOutboundCalculator();
      const expectedUsageReport = {
        usage: [
          {
            timestamp: timestamp.substring(0, timestamp.length - 1),
            service: { id: "data-analytics", plan: "standard" },
            consumer: { environment: "CF", region: "cf-eu10-canary", subAccount: "DWC_DEV_REPO" },
            measures: [
              {
                id: "capacity_units_hour",
                value: calculator.calculateCusFromBlocks(metricsToReport.reports[0].measureValue),
              },
              { id: dwcMetrics.PREMIUM_OUTBOUND, value: metricsToReport.reports[0].measureValue },
            ],
            id: metricsToReport.ids[0],
            customDimensions: { dimension1: "00000", dimension2: "DWC" },
          },
        ],
      };

      await reportMetrics(context);

      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(0).args[1]).to.be.deep.equal(
        expectedUsageReport.usage
      );
      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(0).args[2]).to.be.deep.equal(
        MetricsReportingStatus.FAILED
      );
    });

    it("Should report for multiple metrics.", async () => {
      const metricsToReport1 = createCollectedMetrics(Measure.PREMIUM_OUTBOUND, 1, MetricsReportingStatus.PENDING);
      const metricsToReport2 = createCollectedMetrics(Measure.DI_EXTRACTION_HOURS, 1, MetricsReportingStatus.PENDING);

      reportStub.onCall(0).resolves();
      reportStub.onCall(1).resolves();
      metricsHandlerStub.getMetricsForReporting.onCall(0).resolves([...metricsToReport1.reports]);
      metricsHandlerStub.getMetricsForReporting.onCall(1).resolves([...metricsToReport2.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await reportMetrics(context, [Measure.PREMIUM_OUTBOUND, Measure.DI_EXTRACTION_HOURS]);

      const timestamp = new Date(metricsToReport1.reports[0].measurementPeriodStart).toISOString();
      const premiumOutboundCalculator = new FTCCalculators.PremiumOutboundCalculator();
      const nodeHoursCalculator = new FTCCalculators.DiNodeHoursCalculator();
      const expectedUsageReport1 = {
        usage: [
          {
            id: metricsToReport1.ids[0],
            consumer: { environment: "CF", region: "cf-eu10-canary", subAccount: "DWC_DEV_REPO" },
            customDimensions: { dimension1: "00000", dimension2: "DWC" },
            measures: [
              {
                id: "capacity_units_hour",
                value: premiumOutboundCalculator.calculateCusFromBlocks(metricsToReport1.reports[0].measureValue),
              },
              { id: dwcMetrics.PREMIUM_OUTBOUND, value: metricsToReport1.reports[0].measureValue },
            ],
            service: { id: "data-analytics", plan: "standard" },
            timestamp: timestamp.substring(0, timestamp.length - 1),
          },
        ],
      };
      const expectedUsageReport2 = {
        usage: [
          {
            timestamp: timestamp.substring(0, timestamp.length - 1),
            service: { id: "data-analytics", plan: "standard" },
            consumer: { environment: "CF", region: "cf-eu10-canary", subAccount: "DWC_DEV_REPO" },
            measures: [
              {
                id: "capacity_units_hour",
                value: nodeHoursCalculator.calculateCusFromBlocks(metricsToReport2.reports[0].measureValue),
              },
              { id: dwcMetrics.DI_NODE_HOUR, value: metricsToReport2.reports[0].measureValue },
            ],
            id: metricsToReport2.ids[0],
            customDimensions: { dimension1: "00000", dimension2: "DWC" },
          },
        ],
      };

      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(0).args[1]).to.be.deep.equal(
        expectedUsageReport1.usage
      );
      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(0).args[2]).to.be.deep.equal(
        MetricsReportingStatus.CONFIRMED
      );

      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(1).args[1]).to.be.deep.equal(
        expectedUsageReport2.usage
      );
      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(1).args[2]).to.be.deep.equal(
        MetricsReportingStatus.CONFIRMED
      );

      expect(reportStub.getCall(0).args[0]).to.be.deep.equal(expectedUsageReport1);
      expect(reportStub.getCall(1).args[0]).to.be.deep.equal(expectedUsageReport2);
    });

    it("Should not interrupt further reports if first one fails.", async () => {
      const metricsToReport1 = createCollectedMetrics(Measure.PREMIUM_OUTBOUND, 1, MetricsReportingStatus.PENDING);
      const metricsToReport2 = createCollectedMetrics(Measure.DI_EXTRACTION_HOURS, 1, MetricsReportingStatus.PENDING);

      reportStub.onCall(0).throws();
      reportStub.onCall(1).resolves(true);
      metricsHandlerStub.getMetricsForReporting.onCall(0).resolves([...metricsToReport1.reports]);
      metricsHandlerStub.getMetricsForReporting.onCall(1).resolves([...metricsToReport2.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await reportMetrics(context, [Measure.PREMIUM_OUTBOUND, Measure.DI_EXTRACTION_HOURS] as any as Measure[]);

      const timestamp = new Date(metricsToReport1.reports[0].measurementPeriodStart).toISOString();
      const premiumOutboundCalculator = new FTCCalculators.PremiumOutboundCalculator();
      const nodeHoursCalculator = new FTCCalculators.DiNodeHoursCalculator();
      const expectedUsageReport1 = {
        usage: [
          {
            timestamp: timestamp.substring(0, timestamp.length - 1),
            service: { id: "data-analytics", plan: "standard" },
            consumer: { environment: "CF", region: "cf-eu10-canary", subAccount: "DWC_DEV_REPO" },
            measures: [
              {
                id: "capacity_units_hour",
                value: premiumOutboundCalculator.calculateCusFromBlocks(metricsToReport1.reports[0].measureValue),
              },
              {
                id: dwcMetrics.PREMIUM_OUTBOUND,
                value: metricsToReport1.reports[0].measureValue,
              },
            ],
            id: metricsToReport1.ids[0],
            customDimensions: { dimension1: "00000", dimension2: "DWC" },
          },
        ],
      };
      const expectedUsageReport2 = {
        usage: [
          {
            timestamp: timestamp.substring(0, timestamp.length - 1),
            service: { id: "data-analytics", plan: "standard" },
            consumer: { environment: "CF", region: "cf-eu10-canary", subAccount: "DWC_DEV_REPO" },
            measures: [
              {
                id: "capacity_units_hour",
                value: nodeHoursCalculator.calculateCusFromBlocks(metricsToReport2.reports[0].measureValue),
              },
              {
                id: dwcMetrics.DI_NODE_HOUR,
                value: metricsToReport2.reports[0].measureValue,
              },
            ],
            id: metricsToReport2.ids[0],
            customDimensions: { dimension1: "00000", dimension2: "DWC" },
          },
        ],
      };

      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(0).args[1]).to.be.deep.equal(
        expectedUsageReport1.usage
      );
      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(0).args[2]).to.be.deep.equal(
        MetricsReportingStatus.FAILED
      );

      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(1).args[1]).to.be.deep.equal(
        expectedUsageReport2.usage
      );
      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(1).args[2]).to.be.deep.equal(
        MetricsReportingStatus.CONFIRMED
      );
    });

    it("Should break Usage documents into multiple arrays.", async () => {
      const usageAmount = 9050;
      const expetedCalls = Math.ceil(usageAmount / 100);
      const metricsToReport1 = createCollectedMetrics(
        Measure.PREMIUM_OUTBOUND,
        usageAmount,
        MetricsReportingStatus.PENDING
      );

      reportStub.resolves(true);
      metricsHandlerStub.getMetricsForReporting.onCall(0).resolves([...metricsToReport1.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await reportMetrics(context, [Measure.PREMIUM_OUTBOUND, Measure.DI_EXTRACTION_HOURS] as any as Measure[]);

      expect(reportStub.callCount).to.be.equal(expetedCalls);
    });

    it("Should report metrics specified in reportHandlingConfig.ts.", async () => {
      const metricsToReport1 = createCollectedMetrics(Measure.PREMIUM_OUTBOUND, 1, MetricsReportingStatus.PENDING);

      reportStub.resolves(true);
      metricsHandlerStub.getMetricsForReporting.resolves([...metricsToReport1.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      const ff = {};

      await reportMetrics(context);
      const metricsToReportList = metricsToReport(ff as any);
      for (let index = 0; index < metricsToReportList.length; index++) {
        expect(metricsHandlerStub.getMetricsForReporting.getCall(index).args[2]).to.be.deep.equal(STATUS_TO_REPORT);
        expect(metricsHandlerStub.getMetricsForReporting.getCall(index).args[3]).to.be.deep.equal([
          metricsToReportList[index],
        ]);
      }
      expect(reportStub.callCount).to.be.equal(metricsToReportList.length);
    });
  });
  describe("Metrics Reporting - Telemetry", () => {
    beforeEach(() => {
      prepareStubs();
    });
    afterEach(() => {
      newSandbox.restore();
    });
    const telemetryDimensions = {
      measureName: Measure.PREMIUM_OUTBOUND,
      reportingStatus: MetricsReportingStatus.CONFIRMED,
      tenantUuid: tenantInformation.uuid,
      subAccountUuid: "DWC_DEV_REPO",
      instanceid: "not_defined",
      tenantClassification: tenantInformation.classification,
    };

    it("should record successfully reported metrics on telemetry sending tenant information", async () => {
      prepareFeatureFlags({
        DWCO_BILLING_METRICS_DYNATRACE_USAGE_REPORT: true,
      });
      const reportedBlocks = 20;
      const metricsToReport = createCollectedMetrics(
        Measure.PREMIUM_OUTBOUND,
        1,
        MetricsReportingStatus.PENDING,
        reportedBlocks
      );
      reportStub.resolves(true);
      metricsHandlerStub.getMetricsForReporting.resolves([...metricsToReport.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await reportMetrics(context, [Measure.PREMIUM_OUTBOUND]);

      // Record number of lines reported
      expect(recordOnTelemetryStub.getCall(0).args[0]).to.be.deep.equal(1);
      expect(recordOnTelemetryStub.getCall(0).args[1]).to.be.deep.equal(telemetryDimensions);
      // Number of CUs reported
      const calculator = new FTCCalculators.PremiumOutboundCalculator();
      const expectedRecordedCUs = calculator.calculateCusFromBlocks(reportedBlocks);
      expect(recordOnTelemetryStub.getCall(1).args[0]).to.be.deep.equal(expectedRecordedCUs);
      expect(recordOnTelemetryStub.getCall(1).args[1]).to.be.deep.equal(telemetryDimensions);
      // // Number of Blocks reported
      expect(recordOnTelemetryStub.getCall(2).args[0]).to.be.deep.equal(reportedBlocks);
      expect(recordOnTelemetryStub.getCall(2).args[1]).to.be.deep.equal(telemetryDimensions);

      expect(recordOnTelemetryStub.callCount).to.be.equal(3);
    });
    it("should record failed and not reported metrics on telemetry sending tenant information", async () => {
      prepareFeatureFlags({
        DWCO_BILLING_METRICS_DYNATRACE_USAGE_REPORT: true,
      });
      reportStub.throws();
      const reportedBlocks = 20;
      const metricsToReport = createCollectedMetrics(
        Measure.PREMIUM_OUTBOUND,
        1,
        MetricsReportingStatus.PENDING,
        reportedBlocks
      );
      metricsHandlerStub.getMetricsForReporting.resolves([...metricsToReport.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await reportMetrics(context, [Measure.PREMIUM_OUTBOUND]);

      const telemetryDimensionsForFailedReport = {
        ...telemetryDimensions,
        reportingStatus: MetricsReportingStatus.FAILED,
      };

      // Record number of lines reported
      expect(recordOnTelemetryStub.getCall(0).args[0]).to.be.deep.equal(1);
      expect(recordOnTelemetryStub.getCall(0).args[1]).to.be.deep.equal(telemetryDimensionsForFailedReport);
      // Number of CUs reported
      const calculator = new FTCCalculators.PremiumOutboundCalculator();
      const expectedRecordedCUs = calculator.calculateCusFromBlocks(reportedBlocks);
      expect(recordOnTelemetryStub.getCall(1).args[0]).to.be.deep.equal(expectedRecordedCUs);
      expect(recordOnTelemetryStub.getCall(1).args[1]).to.be.deep.equal(telemetryDimensionsForFailedReport);
      // // Number of Blocks reported
      expect(recordOnTelemetryStub.getCall(2).args[0]).to.be.deep.equal(reportedBlocks);
      expect(recordOnTelemetryStub.getCall(2).args[1]).to.be.deep.equal(telemetryDimensionsForFailedReport);

      expect(recordOnTelemetryStub.callCount).to.be.equal(3);
    });

    it("should not try to record on telemetry if DWCO_BILLING_METRICS_DYNATRACE_USAGE_REPORT is off", async () => {
      prepareFeatureFlags({
        DWCO_BILLING_METRICS_DYNATRACE_USAGE_REPORT: false,
      });
      expect(recordOnTelemetryStub.getCalls().length).to.be.equal(0);
    });
  });

  describe("Metrics Reporting - Disabled Feature Flags", function () {
    async function generateUsageForAll() {
      const ff = await FeatureFlagProvider.getFeatureFlags(context);
      const metricsToReportList = metricsToReport(ff);

      const metricsToReportDto: MetricsReportingResponseDto[] = [];
      for (const measure of metricsToReportList) {
        metricsToReportDto.push(...createCollectedMetrics(measure, 1, MetricsReportingStatus.PENDING).reports);
      }
      return metricsToReportDto;
    }
    beforeEach(() => {
      prepareStubs();
    });

    afterEach(() => {
      newSandbox.restore();
    });

    it("Should report all metrics when all FFs are true", async () => {
      prepareFeatureFlags({
        DWCO_LARGE_SYSTEMS_REQUESTS: true,
      });

      const metricsToReport = await generateUsageForAll();

      reportStub.resolves();
      metricsHandlerStub.getMetricsForReporting.resolves(metricsToReport);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await reportMetrics(context);

      const orderedExpectedMetrics: Measure[] = [
        "PREMIUM_OUTBOUND",
        "CATALOG_STORAGE",
        "ELASTIC_COMPUTE_NODE",
        "FOUNDATION_BLOCK",
        "DI_EXTRACTION_HOURS",
        "THRESHOLD_DATA_LAKE_STORAGE",
        "THRESHOLD_MEMORY",
        "THRESHOLD_STORAGE",
        "THRESHOLD_BW_BRIDGE",
        "LARGE_SYSTEMS_STORAGE",
        "LARGE_SYSTEMS_COMPUTE",
        "LARGE_SYSTEMS_REQUESTS",
      ];
      assertMetricsWereReported(orderedExpectedMetrics);
    });

    it("Should report for Premium Outbound, ECN, Catalog Storage and Foundation Block.", async () => {
      const metricsToReport = await generateUsageForAll();

      reportStub.resolves();
      metricsHandlerStub.getMetricsForReporting.resolves(metricsToReport);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await reportMetrics(context);

      const orderedExpectedMetrics: Measure[] = [
        "PREMIUM_OUTBOUND",
        "CATALOG_STORAGE",
        "ELASTIC_COMPUTE_NODE",
        "FOUNDATION_BLOCK",
        "DI_EXTRACTION_HOURS",
        "THRESHOLD_DATA_LAKE_STORAGE",
        "THRESHOLD_MEMORY",
        "THRESHOLD_STORAGE",
        "THRESHOLD_BW_BRIDGE",
        "LARGE_SYSTEMS_STORAGE",
        "LARGE_SYSTEMS_COMPUTE",
        "LARGE_SYSTEMS_REQUESTS",
      ];
      assertMetricsWereReported(orderedExpectedMetrics);
    });
  });

  describe("Metrics Reporting - Report Older Metrics", () => {
    beforeEach(() => {
      prepareStubs();
      prepareFeatureFlags();
      enableReportOldMetricsStub.returns(true);
    });

    afterEach(() => {
      newSandbox.restore();
    });

    it("Should call getMetricsForReporting again for older metrics", async () => {
      const metricsToReport = createCollectedMetrics(Measure.PREMIUM_OUTBOUND, 2, MetricsReportingStatus.PENDING);

      reportStub.onCall(0).resolves();
      metricsHandlerStub.getMetricsForReporting.onCall(0).resolves([metricsToReport.reports[0]]);
      metricsHandlerStub.getMetricsForReporting.onCall(1).resolves([metricsToReport.reports[1]]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await reportMetrics(context, [Measure.PREMIUM_OUTBOUND]);

      const timestamp = new Date(metricsToReport.reports[0].measurementPeriodStart).toISOString();
      const calculator = new FTCCalculators.PremiumOutboundCalculator();
      const expectedUsageReport = {
        usage: [
          {
            id: metricsToReport.ids[0],
            consumer: { environment: "CF", region: "cf-eu10-canary", subAccount: "DWC_DEV_REPO" },
            customDimensions: { dimension1: "00000", dimension2: "DWC" },
            measures: [
              {
                id: "capacity_units_hour",
                value: calculator.calculateCusFromBlocks(metricsToReport.reports[0].measureValue),
              },
              { id: dwcMetrics.PREMIUM_OUTBOUND, value: metricsToReport.reports[0].measureValue },
            ],
            service: { id: "data-analytics", plan: "standard" },
            timestamp: timestamp.substring(0, timestamp.length - 1),
          },
          {
            id: metricsToReport.ids[1],
            consumer: { environment: "CF", region: "cf-eu10-canary", subAccount: "DWC_DEV_REPO" },
            customDimensions: { dimension1: "00000", dimension2: "DWC" },
            measures: [
              {
                id: "capacity_units_hour",
                value: calculator.calculateCusFromBlocks(metricsToReport.reports[1].measureValue),
              },
              { id: dwcMetrics.PREMIUM_OUTBOUND, value: metricsToReport.reports[1].measureValue },
            ],
            service: { id: "data-analytics", plan: "standard" },
            timestamp: timestamp.substring(0, timestamp.length - 1),
          },
        ],
      };

      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(0).args[1]).to.be.deep.equal(
        expectedUsageReport.usage
      );
      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(0).args[2]).to.be.deep.equal(
        MetricsReportingStatus.CONFIRMED
      );

      expect(reportStub.getCall(0).args[0]).to.be.deep.equal(expectedUsageReport);
    });
    it("Should report 24 of the older metrics", async () => {
      const newMetricsToReport = createCollectedMetrics(Measure.PREMIUM_OUTBOUND, 1, MetricsReportingStatus.PENDING);
      const oldMetricsToReport = createCollectedMetrics(Measure.PREMIUM_OUTBOUND, 50, MetricsReportingStatus.PENDING);

      reportStub.onCall(0).resolves();
      metricsHandlerStub.getMetricsForReporting.onCall(0).resolves([...newMetricsToReport.reports]);
      metricsHandlerStub.getMetricsForReporting.onCall(1).resolves([...oldMetricsToReport.reports]);
      metricsHandlerStub.updateMetricsReportingStatus.resolves();

      await reportMetrics(context, [Measure.PREMIUM_OUTBOUND]);

      const timestamp = new Date(newMetricsToReport.reports[0].measurementPeriodStart).toISOString();
      const calculator = new FTCCalculators.PremiumOutboundCalculator();
      const expectedUsageReport = {
        usage: [
          {
            id: newMetricsToReport.ids[0],
            consumer: { environment: "CF", region: "cf-eu10-canary", subAccount: "DWC_DEV_REPO" },
            customDimensions: { dimension1: "00000", dimension2: "DWC" },
            measures: [
              {
                id: "capacity_units_hour",
                value: calculator.calculateCusFromBlocks(newMetricsToReport.reports[0].measureValue),
              },
              { id: dwcMetrics.PREMIUM_OUTBOUND, value: newMetricsToReport.reports[0].measureValue },
            ],
            service: { id: "data-analytics", plan: "standard" },
            timestamp: timestamp.substring(0, timestamp.length - 1),
          },
        ],
      };
      for (let i = 0; i < 24; i++) {
        expectedUsageReport.usage.push({
          id: oldMetricsToReport.ids[i],
          consumer: { environment: "CF", region: "cf-eu10-canary", subAccount: "DWC_DEV_REPO" },
          customDimensions: { dimension1: "00000", dimension2: "DWC" },
          measures: [
            {
              id: "capacity_units_hour",
              value: calculator.calculateCusFromBlocks(oldMetricsToReport.reports[i].measureValue),
            },
            { id: dwcMetrics.PREMIUM_OUTBOUND, value: oldMetricsToReport.reports[i].measureValue },
          ],
          service: { id: "data-analytics", plan: "standard" },
          timestamp: timestamp.substring(0, timestamp.length - 1),
        });
      }

      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(0).args[1]).to.be.deep.equal(
        expectedUsageReport.usage
      );
      expect(metricsHandlerStub.updateMetricsReportingStatus.getCall(0).args[2]).to.be.deep.equal(
        MetricsReportingStatus.CONFIRMED
      );

      expect(reportStub.getCall(0).args[0]).to.be.deep.equal(expectedUsageReport);
    });
  });

  describe("reportHandler.ts - getUsageFormatterForMetric function", () => {
    let measure: any;

    afterEach(() => {
      newSandbox.restore();
    });

    const context = RequestContext.createFromTenantId(testTenantUuid);

    it("Should return a formatter if it exists", async () => {
      const ffStub = newSandbox.stub(FeatureFlagProvider, "getFeatureFlags");
      ffStub.resolves({ DWCO_LARGE_SYSTEMS_REQUESTS: true } as unknown as IFeatureFlagsMap);

      // Each pair is an in and out param
      const measureFormatter: Array<{ measure: Measure; formatter: any }> = [
        { measure: Measure.PREMIUM_OUTBOUND, formatter: PremiumOutboundFormatter },
        { measure: Measure.CATALOG_STORAGE, formatter: OdcStorageFormatter },
        { measure: Measure.ELASTIC_COMPUTE_NODE, formatter: ElasticComputeNodesFormatter },
        { measure: Measure.FOUNDATION_BLOCK, formatter: FoundationBlockFormatter },
        { measure: Measure.THRESHOLD_DATA_LAKE_STORAGE, formatter: DataLakeFormatter },
        { measure: Measure.THRESHOLD_MEMORY, formatter: ComputeFormatter },
        { measure: Measure.THRESHOLD_STORAGE, formatter: StorageFormatter },
        { measure: Measure.THRESHOLD_BW_BRIDGE, formatter: BwBridgeFormatter },
        { measure: Measure.LARGE_SYSTEMS_STORAGE, formatter: LargeSystemsStorageFormatter },
        { measure: Measure.LARGE_SYSTEMS_COMPUTE, formatter: LargeSystemsComputeFormatter },
        { measure: Measure.LARGE_SYSTEMS_REQUESTS, formatter: LargeSystemsRequestsFormatter },
      ];

      for (const { measure, formatter } of measureFormatter) {
        const formatInstance = await formatterConfig.getUsageFormatterForMetric(context, measure);
        expect(formatInstance).to.be.instanceOf(formatter);
      }
    });

    it("Should throw error if there is no formatter for a measure", async () => {
      measure = "undefined";

      const throwErrorMessage = `Measure ${measure} has no formatter defined.`;

      await expect(formatterConfig.getUsageFormatterForMetric(context, measure)).to.eventually.be.rejectedWith(
        throwErrorMessage
      );
    });

    it("Should return the DI formatter", async () => {
      measure = "DI_EXTRACTION_HOURS";

      const formatMetric = await formatterConfig.getUsageFormatterForMetric(context, measure);

      expect(formatMetric).to.be.instanceOf(DIFormatter);
    });

    it("Should throw when trying to get the Large Systems Requests formatter if the FF is off ", async () => {
      measure = Measure.LARGE_SYSTEMS_REQUESTS;

      const throwErrorMessage = `Measure ${measure} formatter exists but its ff has been disabled`;

      const ffStub = newSandbox.stub(FeatureFlagProvider, "getFeatureFlags");
      ffStub.resolves({ DWCO_LARGE_SYSTEMS_REQUESTS: false } as unknown as IFeatureFlagsMap);

      await expect(formatterConfig.getUsageFormatterForMetric(context, measure)).to.eventually.be.rejectedWith(
        throwErrorMessage
      );
    });
  });
});
