/** @format */
// FILEOWNER: [Provisioning]

import { Hyperscaler } from "@sap/deepsea-utils";
import { DS_HOURLY_PRICES, dwcMetrics } from "@sap/dwc-flexible-tenant-sizing";
import { SinonStub, default as sinon } from "sinon";
import { Measure, TenantClassification, TenantType } from "../../../../../shared/provisioning/ftc/types";
import { AnalyticsProvisioningService, IApsTenantInformation } from "../../../../aps/AnalyticsProvisioningService";
import { FeatureFlagProvider, IFeatureFlagsMap } from "../../../../featureflags/FeatureFlagProvider";
import { TenantInformationProvider } from "../../../../featureflags/TenantInformationProvider";
import { ITenantInformation } from "../../../../featureflags/types";
import * as GetHyperscalerFile from "../../../../lib/node";
import { testTenantUuid } from "../../../../lib/node";
import { MeteringUtils } from "../../../../metering/MeteringUtils";
import { IMemoryDimensions } from "../../../../metering/metrics/tasks/measures/types/thresholdMemoryTypes";
import { MetricsReportingStatus } from "../../../../metering/metrics/types";
import { ComputeFormatter } from "../../../../metering/reporting/formatters/ComputeFormatter";
import { METERING_SERVICE_ID, dwcPlans } from "../../../../metering/types";
import { RequestContext } from "../../../../repository/security/requestContext";
import { CustomerHanaRuntimeData } from "../../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { createCollectedMetrics } from "../helpers";

describe("ComputeFormatter.ts", function () {
  const context = RequestContext.createFromTenantId(testTenantUuid);
  const sandbox = sinon.createSandbox();
  let tenantStub: SinonStub;
  let apsStub: sinon.SinonStubbedInstance<AnalyticsProvisioningService>;
  let getHyperscalerStub: SinonStub;
  before(() => {
    sandbox.stub(FeatureFlagProvider, "getFeatureFlags").resolves({} as IFeatureFlagsMap);
    tenantStub = sandbox.stub(TenantInformationProvider, "getTenantInformation");
    sandbox.stub(CustomerHanaRuntimeData.prototype, "getOverallAssignedStorage").resolves();
    sandbox.stub(CustomerHanaRuntimeData.prototype, "getStorageConsumptionOverview").resolves();
    apsStub = sandbox.createStubInstance(AnalyticsProvisioningService);
    getHyperscalerStub = sandbox.stub(GetHyperscalerFile, "getHyperscaler");
  });
  after(() => {
    sandbox.restore();
  });
  const COMPUTE_BLOCK_PRICE = DS_HOURLY_PRICES.thresholdCompute;
  describe("Compute formatter", () => {
    const standardCpeaTenant = {
      type: TenantType.CPEA,
      classification: TenantClassification.DWCCPEA,
      consumerAccountId: "consumerAccountId",
      freeTier: false,
      tierChangedOn: new Date(0).toISOString(),
      crmTenantId: "0000",
      license: {
        thresholdDataWarehouseCloudUser: "1000000",
        thresholdStorage: "256",
        thresholdMemory: "32",
        thresholdVCPU: "2",
        thresholdDataLakeStorage: "5",
        thresholdDataLakeCompute: "6",
        thresholdDWCCU: "10000",
        thresholdBWBridge1: "256",
        thresholdRmsNodeHours: "500",
        thresholdRmsNodeHoursIncluded: "200",
        thresholdRmsConcurrency: "2",
        thresholdRmsConcurrencyIncluded: "2",
        thresholdCatalogNodeHours: "2",
        thresholdCatalogNodeHoursIncluded: "200",
        thresholdCatalogConcurrency: "2",
        thresholdCatalogConcurrencyIncluded: "1",
        thresholdCatalogStorage: "1024",
        thresholdCatalogStorageIncluded: "512",
        thresholdRmsPremiumOutbound: "5",
      },
    } as ITenantInformation;

    before(() => {
      sandbox.stub(AnalyticsProvisioningService, "newFromCache").resolves(apsStub as any);
      apsStub.getTenantInfoFromCacheWithFallback.resolves({
        tenantuuid: testTenantUuid,
        type: TenantType.CPEA,
        globalaccountid: "global-account-uuid",
        subaccountid: "sub-account-uuid",
        serviceid: METERING_SERVICE_ID,
        planid: "plan-uuid",
        instanceid: "instance-uuid",
      } as IApsTenantInformation);
    });

    it("Should correctly format usage entries for MEMORY price in AWS Hyperscaler", async () => {
      tenantStub.resolves(standardCpeaTenant);
      getHyperscalerStub.returns(Hyperscaler.AWS);
      const memory = 32;
      const computeBlocks = 32 / 16;
      const dimensions: IMemoryDimensions = { thresholdVCPU: 2 };
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(
        Measure.THRESHOLD_MEMORY,
        1,
        MetricsReportingStatus.PENDING,
        memory,
        undefined,
        dimensions
      );

      const computeFormatter = new ComputeFormatter(context);
      const result = await computeFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: COMPUTE_BLOCK_PRICE.memory * computeBlocks,
          },
          {
            id: dwcMetrics.COMPUTE,
            value: computeBlocks,
          },
        ],
        id: mockedMetrics.reports[0].reportingId,
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly format usage entries for HIGH MEMORY price in AWS Hyperscaler", async () => {
      tenantStub.resolves(standardCpeaTenant);
      getHyperscalerStub.returns(Hyperscaler.AWS);
      const memory = 3600;
      const computeBlocks = 3600 / 16;
      const dimensions: IMemoryDimensions = { thresholdVCPU: 120 };
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(
        Measure.THRESHOLD_MEMORY,
        1,
        MetricsReportingStatus.PENDING,
        memory,
        undefined,
        dimensions
      );

      const computeFormatter = new ComputeFormatter(context);
      const result = await computeFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: COMPUTE_BLOCK_PRICE.highMemory * computeBlocks,
          },
          {
            id: dwcMetrics.COMPUTE,
            value: computeBlocks,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly format usage entries for COMPUTE price in AWS Hyperscaler", async () => {
      tenantStub.resolves(standardCpeaTenant);
      getHyperscalerStub.returns(Hyperscaler.AWS);
      const memory = 32;
      const computeBlocks = 32 / 16;
      const dimensions: IMemoryDimensions = { thresholdVCPU: 4 };
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(
        Measure.THRESHOLD_MEMORY,
        1,
        MetricsReportingStatus.PENDING,
        memory,
        undefined,
        dimensions
      );

      const computeFormatter = new ComputeFormatter(context);
      const result = await computeFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: COMPUTE_BLOCK_PRICE.compute * computeBlocks,
          },
          {
            id: dwcMetrics.COMPUTE,
            value: computeBlocks,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly format usage entries for HIGH COMPUTE price in AWS Hyperscaler", async () => {
      tenantStub.resolves(standardCpeaTenant);
      getHyperscalerStub.returns(Hyperscaler.AWS);
      const memory = 32;
      const computeBlocks = 32 / 16;
      const dimensions: IMemoryDimensions = { thresholdVCPU: 8 };
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(
        Measure.THRESHOLD_MEMORY,
        1,
        MetricsReportingStatus.PENDING,
        memory,
        undefined,
        dimensions
      );

      const computeFormatter = new ComputeFormatter(context);
      const result = await computeFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: COMPUTE_BLOCK_PRICE.highCompute * computeBlocks,
          },
          {
            id: dwcMetrics.COMPUTE,
            value: computeBlocks,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly format usage entries for MEMORY price in Azure Hyperscaler", async () => {
      tenantStub.resolves(standardCpeaTenant);
      getHyperscalerStub.returns(Hyperscaler.Azure);
      const memory = 1024;
      const computeBlocks = 1024 / 16;
      const dimensions: IMemoryDimensions = { thresholdVCPU: 64 };
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(
        Measure.THRESHOLD_MEMORY,
        1,
        MetricsReportingStatus.PENDING,
        memory,
        undefined,
        dimensions
      );

      const computeFormatter = new ComputeFormatter(context);
      const result = await computeFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: COMPUTE_BLOCK_PRICE.memory * computeBlocks,
          },
          {
            id: dwcMetrics.COMPUTE,
            value: computeBlocks,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly format usage entries for HIGH MEMORY price in Azure Hyperscaler", async () => {
      tenantStub.resolves(standardCpeaTenant);
      getHyperscalerStub.returns(Hyperscaler.Azure);
      const memory = 5595;
      const computeBlocks = 5595 / 16;
      const dimensions: IMemoryDimensions = { thresholdVCPU: 204 };
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(
        Measure.THRESHOLD_MEMORY,
        1,
        MetricsReportingStatus.PENDING,
        memory,
        undefined,
        dimensions
      );

      const computeFormatter = new ComputeFormatter(context);
      const result = await computeFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: COMPUTE_BLOCK_PRICE.highMemory * computeBlocks,
          },
          {
            id: dwcMetrics.COMPUTE,
            value: computeBlocks,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly format usage entries for COMPUTE price in Azure Hyperscaler", async () => {
      tenantStub.resolves(standardCpeaTenant);
      getHyperscalerStub.returns(Hyperscaler.Azure);
      const memory = 32;
      const computeBlocks = 32 / 16;
      const dimensions: IMemoryDimensions = { thresholdVCPU: 4 };
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(
        Measure.THRESHOLD_MEMORY,
        1,
        MetricsReportingStatus.PENDING,
        memory,
        undefined,
        dimensions
      );

      const computeFormatter = new ComputeFormatter(context);
      const result = await computeFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: COMPUTE_BLOCK_PRICE.compute * computeBlocks,
          },
          {
            id: dwcMetrics.COMPUTE,
            value: computeBlocks,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly format usage entries for HIGH COMPUTE price in Azure Hyperscaler", async () => {
      tenantStub.resolves(standardCpeaTenant);
      getHyperscalerStub.returns(Hyperscaler.Azure);
      const memory = 352;
      const computeBlocks = 352 / 16;
      const dimensions: IMemoryDimensions = { thresholdVCPU: 88 };
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(
        Measure.THRESHOLD_MEMORY,
        1,
        MetricsReportingStatus.PENDING,
        memory,
        undefined,
        dimensions
      );

      const computeFormatter = new ComputeFormatter(context);
      const result = await computeFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: COMPUTE_BLOCK_PRICE.highCompute * computeBlocks,
          },
          {
            id: dwcMetrics.COMPUTE,
            value: computeBlocks,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly format usage entries for MEMORY price in GCP Hyperscaler", async () => {
      tenantStub.resolves(standardCpeaTenant);
      getHyperscalerStub.returns(Hyperscaler.GCP);
      const memory = 960;
      const computeBlocks = 960 / 16;
      const dimensions: IMemoryDimensions = { thresholdVCPU: 60 };
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(
        Measure.THRESHOLD_MEMORY,
        1,
        MetricsReportingStatus.PENDING,
        memory,
        undefined,
        dimensions
      );

      const computeFormatter = new ComputeFormatter(context);
      const result = await computeFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: COMPUTE_BLOCK_PRICE.memory * computeBlocks,
          },
          {
            id: dwcMetrics.COMPUTE,
            value: computeBlocks,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly format usage entries for HIGH MEMORY price in GCP Hyperscaler", async () => {
      tenantStub.resolves(standardCpeaTenant);
      getHyperscalerStub.returns(Hyperscaler.GCP);
      const memory = 3700;
      const computeBlocks = 3700 / 16;
      const dimensions: IMemoryDimensions = { thresholdVCPU: 124 };
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(
        Measure.THRESHOLD_MEMORY,
        1,
        MetricsReportingStatus.PENDING,
        memory,
        undefined,
        dimensions
      );

      const computeFormatter = new ComputeFormatter(context);
      const result = await computeFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: COMPUTE_BLOCK_PRICE.highMemory * computeBlocks,
          },
          {
            id: dwcMetrics.COMPUTE,
            value: computeBlocks,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly format usage entries for COMPUTE price in GCP Hyperscaler", async () => {
      tenantStub.resolves(standardCpeaTenant);
      getHyperscalerStub.returns(Hyperscaler.GCP);
      const memory = 32;
      const computeBlocks = 32 / 16;
      const dimensions: IMemoryDimensions = { thresholdVCPU: 4 };
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(
        Measure.THRESHOLD_MEMORY,
        1,
        MetricsReportingStatus.PENDING,
        memory,
        undefined,
        dimensions
      );

      const computeFormatter = new ComputeFormatter(context);
      const result = await computeFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: COMPUTE_BLOCK_PRICE.compute * computeBlocks,
          },
          {
            id: dwcMetrics.COMPUTE,
            value: computeBlocks,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly format usage entries for HIGH COMPUTE price in GCP Hyperscaler", async () => {
      tenantStub.resolves(standardCpeaTenant);
      getHyperscalerStub.returns(Hyperscaler.GCP);
      const memory = 288;
      const computeBlocks = 288 / 16;
      const dimensions: IMemoryDimensions = { thresholdVCPU: 72 };
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(
        Measure.THRESHOLD_MEMORY,
        1,
        MetricsReportingStatus.PENDING,
        memory,
        undefined,
        dimensions
      );

      const computeFormatter = new ComputeFormatter(context);
      const result = await computeFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: COMPUTE_BLOCK_PRICE.highCompute * computeBlocks,
          },
          {
            id: dwcMetrics.COMPUTE,
            value: computeBlocks,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });
  });
});
