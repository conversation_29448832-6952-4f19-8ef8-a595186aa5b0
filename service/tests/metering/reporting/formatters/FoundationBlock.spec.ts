/** @format */
// FILEOWNER: [Provisioning]

import { dwcMetrics } from "@sap/dwc-flexible-tenant-sizing";
import { default as sinon, SinonStub } from "sinon";
import { DEFAULT_FOUNDATION_BLOCK_CONSUMPTION } from "../../../../../shared/provisioning/ftc/defaultValues";
import { Measure, TenantClassification, TenantType } from "../../../../../shared/provisioning/ftc/types";
import { AnalyticsProvisioningService, IApsTenantInformation } from "../../../../aps/AnalyticsProvisioningService";
import { FeatureFlagProvider, IFeatureFlagsMap } from "../../../../featureflags/FeatureFlagProvider";
import { TenantInformationProvider } from "../../../../featureflags/TenantInformationProvider";
import { ITenantInformation } from "../../../../featureflags/types";
import { testTenantUuid } from "../../../../lib/node";
import { MeteringUtils } from "../../../../metering/MeteringUtils";
import { MetricsReportingStatus } from "../../../../metering/metrics/types";
import { FoundationBlockFormatter } from "../../../../metering/reporting/formatters/FoundationBlockFormatter";
import { dwcPlans, METERING_SERVICE_ID } from "../../../../metering/types";
import { licensesToString } from "../../../../provisioning/helpers";
import { standardPlanIncludedLicenses } from "../../../../provisioning/types";
import { RequestContext } from "../../../../repository/security/requestContext";
import { CustomerHanaRuntimeData } from "../../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { createCollectedMetrics } from "../helpers";

describe("FoundationBlockFormatter.ts", function () {
  const context = RequestContext.createFromTenantId(testTenantUuid);
  const sandbox = sinon.createSandbox();
  let tenantStub: SinonStub;
  let apsStub: sinon.SinonStubbedInstance<AnalyticsProvisioningService>;
  before(() => {
    sandbox.stub(FeatureFlagProvider, "getFeatureFlags").resolves({} as IFeatureFlagsMap);
    tenantStub = sandbox.stub(TenantInformationProvider, "getTenantInformation");
    sandbox.stub(CustomerHanaRuntimeData.prototype, "getOverallAssignedStorage").resolves();
    sandbox.stub(CustomerHanaRuntimeData.prototype, "getStorageConsumptionOverview").resolves();
    apsStub = sandbox.createStubInstance(AnalyticsProvisioningService);
  });
  after(() => {
    sandbox.restore();
  });
  const FOUNDATION_BLOCK_PRICE = 0.926;
  describe("Foundation Block formatter", () => {
    const standardCpeaTenant = {
      type: TenantType.CPEA,
      classification: TenantClassification.DWCCPEA,
      consumerAccountId: "consumerAccountId",
      freeTier: false,
      tierChangedOn: new Date(0).toISOString(),
      crmTenantId: "0000",
      license: {
        ...licensesToString(standardPlanIncludedLicenses),
      },
    } as ITenantInformation;

    const freeCpeaTenant = {
      type: TenantType.CPEA,
      classification: TenantClassification.DWCCPEA,
      consumerAccountId: "consumerAccountId",
      freeTier: true,
      tierChangedOn: new Date(0).toISOString(),
      crmTenantId: "0000",
      license: {
        ...licensesToString(standardPlanIncludedLicenses),
      },
    } as ITenantInformation;

    before(() => {
      sandbox.stub(AnalyticsProvisioningService, "newFromCache").resolves(apsStub as any);
      apsStub.getTenantInfoFromCacheWithFallback.resolves({
        tenantuuid: testTenantUuid,
        type: TenantType.CPEA,
        globalaccountid: "global-account-uuid",
        subaccountid: "sub-account-uuid",
        serviceid: METERING_SERVICE_ID,
        planid: "plan-uuid",
        instanceid: "instance-uuid",
      } as IApsTenantInformation);
    });

    it("Should correctly format usage entries for Standard Plan", async () => {
      tenantStub.resolves(standardCpeaTenant);
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(
        Measure.FOUNDATION_BLOCK,
        1,
        MetricsReportingStatus.PENDING,
        DEFAULT_FOUNDATION_BLOCK_CONSUMPTION
      );

      const foundationBlockFormatter = new FoundationBlockFormatter(context);
      const result = await foundationBlockFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: FOUNDATION_BLOCK_PRICE,
          },
          {
            id: dwcMetrics.FOUNDATION_BLOCK,
            value: 1,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly format usage entries for Free Plan", async () => {
      tenantStub.resolves(freeCpeaTenant);
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(
        Measure.FOUNDATION_BLOCK,
        1,
        MetricsReportingStatus.PENDING,
        DEFAULT_FOUNDATION_BLOCK_CONSUMPTION
      );

      const foundationBlockFormatter = new FoundationBlockFormatter(context);
      const result = await foundationBlockFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.FREE },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: FOUNDATION_BLOCK_PRICE,
          },
          {
            id: dwcMetrics.FOUNDATION_BLOCK,
            value: 1,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });
  });
});
