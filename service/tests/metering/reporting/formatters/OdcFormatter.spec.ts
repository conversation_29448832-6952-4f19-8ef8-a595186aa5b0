/** @format */
import { DS_HOURLY_PRICES, dwcMetrics, FTCCalculators } from "@sap/dwc-flexible-tenant-sizing";
import { default as sinon, SinonStub } from "sinon";
import { Measure, TenantClassification, TenantType } from "../../../../../shared/provisioning/ftc/types";
import { AnalyticsProvisioningService, IApsTenantInformation } from "../../../../aps/AnalyticsProvisioningService";
import { FeatureFlagProvider } from "../../../../featureflags/FeatureFlagProvider";
import { TenantInformationProvider } from "../../../../featureflags/TenantInformationProvider";
import { ITenantInformation } from "../../../../featureflags/types";
import * as libNode from "../../../../lib/node";
import { testTenantUuid } from "../../../../lib/node";
import { MeteringUtils } from "../../../../metering/MeteringUtils";
import { MetricsHandler } from "../../../../metering/metrics/MetricsHandler";
import { MetricsReportingStatus } from "../../../../metering/metrics/types";
import { OdcStorageFormatter } from "../../../../metering/reporting/formatters/OdcStorageFormatter";
import { dwcPlans, METERING_SERVICE_ID } from "../../../../metering/types";
import { licensesToString } from "../../../../provisioning/helpers";
import { freePlanIncludedLicenses, standardPlanIncludedLicenses } from "../../../../provisioning/types";
import { RequestContext } from "../../../../repository/security/requestContext";
import { CustomerHanaRuntimeData } from "../../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { createCollectedMetrics } from "../helpers";

describe("OdcStorageFormatter.ts", function () {
  const context = RequestContext.createFromTenantId(testTenantUuid);
  const sandbox = sinon.createSandbox();
  let isCanaryStub: SinonStub;
  let tenantStub: SinonStub;
  before(() => {
    tenantStub = sandbox.stub(TenantInformationProvider, "getTenantInformation");
    sandbox.stub(FeatureFlagProvider, "getFeatureFlags").resolves({} as any);
    sandbox.stub(MetricsHandler.prototype, "getTotalUsageFromPeriod").resolves(0);
    sandbox.stub(CustomerHanaRuntimeData.prototype, "getOverallAssignedStorage").resolves();
    isCanaryStub = sandbox.stub(libNode, "isCanary");
  });
  after(() => {
    sandbox.restore();
  });

  function convertOdcMbToBlocks(storageInMb: number) {
    const blockValueInGb = 1; // 1Gb = 1 Block
    return storageInMb / 1024 / blockValueInGb;
  }

  function calculateOdcOverusageCost(valueInMb: number, includedValue: number) {
    if (valueInMb > includedValue) {
      // Calculate how much of over usage and convert to Gb.
      const overUsageInBlocks = convertOdcMbToBlocks(valueInMb - includedValue);

      /**
       * 1 block = 1Gb
       * We charge for entire blocks only. If the customer over use from 1mb to 1023mb, we charge 1 block.
       * If the customer over use 1.01gb to 1.99gb, we charge 2 blocks.
       * In this sense, we round up the Block (Gb) value.
       */
      const blockToCharge = Math.ceil(overUsageInBlocks);
      return new FTCCalculators.CatalogStorageCalculator().calculateCusFromBlocks(blockToCharge);
    }
    return 0;
  }

  describe("Consumption scenarios", () => {
    const freeCpeaTenant = {
      type: TenantType.CPEA,
      classification: TenantClassification.DWCCPEA,
      consumerAccountId: "consumerAccountId",
      freeTier: true,
      crmTenantId: "0000",
      license: {
        ...licensesToString(freePlanIncludedLicenses),
      },
    } as ITenantInformation;
    const standardCpeaTenant = {
      type: TenantType.CPEA,
      classification: TenantClassification.DWCCPEA,
      consumerAccountId: "consumerAccountId",
      freeTier: false,
      tierChangedOn: new Date(0).toISOString(),
      crmTenantId: "0000",
      license: {
        ...licensesToString(standardPlanIncludedLicenses),
      },
    } as ITenantInformation;

    before(() => {
      const apsStub = sandbox.createStubInstance(AnalyticsProvisioningService);
      sandbox.stub(AnalyticsProvisioningService, "newFromCache").resolves(apsStub as any);
      apsStub.getTenantInfoFromCacheWithFallback.resolves({
        tenantuuid: testTenantUuid,
        type: TenantType.CPEA,
        globalaccountid: "global-account-uuid",
        subaccountid: "sub-account-uuid",
        serviceid: METERING_SERVICE_ID,
        planid: "plan-uuid",
        instanceid: "instance-uuid",
      } as IApsTenantInformation);
    });

    it("Should correctly format usage entries - 1Mb", async () => {
      const odcFormatter = new OdcStorageFormatter(context);

      tenantStub.resolves(standardCpeaTenant);
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(Measure.CATALOG_STORAGE, 1, MetricsReportingStatus.PENDING);

      const result = await odcFormatter.formatUsage(mockedMetrics.reports);

      const expectedValue = convertOdcMbToBlocks(mockedMetrics.reports[0].measureValue);
      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: 0,
          },
          {
            id: dwcMetrics.CATALOG_STORAGE,
            value: expectedValue,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };

      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly format usage entries - 1Gb", async () => {
      const odcFormatter = new OdcStorageFormatter(context);

      tenantStub.resolves(standardCpeaTenant);
      const meteringUtil = await MeteringUtils.fromContext(context);
      const measureValue = 1024;
      const mockedMetrics = createCollectedMetrics(
        Measure.CATALOG_STORAGE,
        1,
        MetricsReportingStatus.PENDING,
        measureValue
      );

      const result = await odcFormatter.formatUsage(mockedMetrics.reports);

      const expectedBlocks = 1;
      const expectedCost = calculateOdcOverusageCost(
        measureValue,
        standardPlanIncludedLicenses.thresholdCatalogStorageIncluded
      );
      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: expectedCost,
          },
          {
            id: dwcMetrics.CATALOG_STORAGE,
            value: expectedBlocks,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };

      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly report capacity units in case over usage for Standard Plan - Minimum cost", async () => {
      tenantStub.resolves(standardCpeaTenant);

      const included = standardPlanIncludedLicenses.thresholdCatalogStorageIncluded;

      const mockedMetrics = createCollectedMetrics(
        Measure.CATALOG_STORAGE,
        1,
        MetricsReportingStatus.PENDING,
        included + 1
      );

      const odcFormatter = new OdcStorageFormatter(context);

      const result = await odcFormatter.formatUsage([...mockedMetrics.reports]);

      const expectedOverusageCost = 1 * DS_HOURLY_PRICES.thresholdCatalogStorage;
      const overusageReported = result[0].measures[0].value;

      sinon.assert.match(overusageReported, expectedOverusageCost);
    });

    it("Should correctly report capacity units in case over usage for Free Plan - Minimum cost", async () => {
      tenantStub.resolves(freeCpeaTenant);

      const included = freePlanIncludedLicenses.thresholdCatalogStorageIncluded;

      const mockedMetrics = createCollectedMetrics(
        Measure.CATALOG_STORAGE,
        1,
        MetricsReportingStatus.PENDING,
        included + 1
      );

      const odcFormatter = new OdcStorageFormatter(context);

      const result = await odcFormatter.formatUsage([...mockedMetrics.reports]);

      const expectedOverusageCost = 1 * DS_HOURLY_PRICES.thresholdCatalogStorage;
      const overusageReported = result[0].measures[0].value;

      sinon.assert.match(overusageReported, expectedOverusageCost);
    });

    it("Should correctly report capacity units in case over usage for Standard Plan", async () => {
      tenantStub.resolves(standardCpeaTenant);

      const included = standardPlanIncludedLicenses.thresholdCatalogStorageIncluded;

      const mockedMetrics1 = createCollectedMetrics(
        Measure.CATALOG_STORAGE,
        1,
        MetricsReportingStatus.PENDING,
        included + 1
      );
      const mockedMetrics2 = createCollectedMetrics(
        Measure.CATALOG_STORAGE,
        1,
        MetricsReportingStatus.PENDING,
        included
      );
      const mockedMetrics3 = createCollectedMetrics(
        Measure.CATALOG_STORAGE,
        1,
        MetricsReportingStatus.PENDING,
        included + 2000
      );

      const odcFormatter = new OdcStorageFormatter(context);

      const result = await odcFormatter.formatUsage([
        ...mockedMetrics1.reports,
        ...mockedMetrics2.reports,
        ...mockedMetrics3.reports,
      ]);

      let expectedOverusageCost = 0;
      expectedOverusageCost =
        expectedOverusageCost + calculateOdcOverusageCost(mockedMetrics1.reports[0].measureValue, included);
      expectedOverusageCost =
        expectedOverusageCost + calculateOdcOverusageCost(mockedMetrics2.reports[0].measureValue, included);
      expectedOverusageCost =
        expectedOverusageCost + calculateOdcOverusageCost(mockedMetrics3.reports[0].measureValue, included);

      let overusageReported = 0;
      for (const usage of result) {
        // usage.measures[0].value reffers to Capacity Units value
        overusageReported = overusageReported + usage.measures[0].value;
      }
      sinon.assert.match(overusageReported.toFixed(10), expectedOverusageCost.toFixed(10));
    });

    it("Should correctly report capacity units in case over usage for Free Plan", async () => {
      tenantStub.resolves(freeCpeaTenant);

      const included = freePlanIncludedLicenses.thresholdCatalogStorageIncluded;

      const mockedMetrics1 = createCollectedMetrics(
        Measure.CATALOG_STORAGE,
        1,
        MetricsReportingStatus.PENDING,
        included + 1
      );
      const mockedMetrics2 = createCollectedMetrics(
        Measure.CATALOG_STORAGE,
        1,
        MetricsReportingStatus.PENDING,
        included
      );
      const mockedMetrics3 = createCollectedMetrics(
        Measure.CATALOG_STORAGE,
        1,
        MetricsReportingStatus.PENDING,
        included + 2000
      );

      const odcFormatter = new OdcStorageFormatter(context);

      const result = await odcFormatter.formatUsage([
        ...mockedMetrics1.reports,
        ...mockedMetrics2.reports,
        ...mockedMetrics3.reports,
      ]);

      let expectedOverusageCost = 0;
      expectedOverusageCost =
        expectedOverusageCost + calculateOdcOverusageCost(mockedMetrics1.reports[0].measureValue, included);
      expectedOverusageCost =
        expectedOverusageCost + calculateOdcOverusageCost(mockedMetrics2.reports[0].measureValue, included);
      expectedOverusageCost =
        expectedOverusageCost + calculateOdcOverusageCost(mockedMetrics3.reports[0].measureValue, included);

      let overusageReported = 0;
      for (const usage of result) {
        // usage.measures[0].value reffers to Capacity Units value
        overusageReported = overusageReported + usage.measures[0].value;
      }
      sinon.assert.match(overusageReported.toFixed(10), expectedOverusageCost.toFixed(10));
    });

    it("Should correctly calculate the capacity units in case overusage - Multiple over usage", async () => {
      tenantStub.resolves(standardCpeaTenant);
      const included = standardPlanIncludedLicenses.thresholdCatalogStorageIncluded;

      const mockedMetrics = createCollectedMetrics(
        Measure.CATALOG_STORAGE,
        1000,
        MetricsReportingStatus.PENDING,
        null,
        () => 512 + Math.floor(Math.random() * (20 * 1024)) // Random overusage betwenn 0 and 20Gb
      );

      let expectedOverUsage = 0;
      for (const measure of mockedMetrics.reports) {
        expectedOverUsage = calculateOdcOverusageCost(measure.measureValue, included);
      }

      const odcFormatter = new OdcStorageFormatter(context);
      const result = await odcFormatter.formatUsage(mockedMetrics.reports);

      let reportedOverUsage = 0;
      for (const report of result) {
        reportedOverUsage = report.measures[0].value;
      }

      sinon.assert.match(reportedOverUsage, expectedOverUsage);
    });

    it("Should correctly calculate the capacity units in case overusage - Max over usage", async () => {
      tenantStub.resolves(standardCpeaTenant);
      const included = standardPlanIncludedLicenses.thresholdCatalogStorageIncluded;

      const mockedMetrics = createCollectedMetrics(
        Measure.CATALOG_STORAGE,
        1,
        MetricsReportingStatus.PENDING,
        512 + 20 * 1024 // Max is 20Gb + Included
      );

      const expectedOverUsage = calculateOdcOverusageCost(20 * 1024, included);

      const odcFormatter = new OdcStorageFormatter(context);
      const result = await odcFormatter.formatUsage(mockedMetrics.reports);

      const reportedOverUsage = result[0].measures[0].value;

      sinon.assert.match(reportedOverUsage, expectedOverUsage);
    });

    it("Should not report capacity units if there is no over usage", async () => {
      tenantStub.resolves(freeCpeaTenant);

      const mockedMetrics = createCollectedMetrics(Measure.CATALOG_STORAGE, 10, MetricsReportingStatus.PENDING, 10);
      const odcFormatter = new OdcStorageFormatter(context);

      const result = await odcFormatter.formatUsage(mockedMetrics.reports);

      const included = freePlanIncludedLicenses.thresholdCatalogStorageIncluded;
      const expectedOverusage = Math.max(0, (mockedMetrics.total - included) / 1024);
      let overusageReported = 0;
      for (const usage of result) {
        overusageReported = overusageReported + usage.measures[0].value;
      }
      const overusedCost = new FTCCalculators.CatalogStorageCalculator().calculateCusFromBlocks(expectedOverusage);
      sinon.assert.match(overusageReported.toFixed(10), overusedCost.toFixed(10));
    });

    it("Should report the usage under correct plan in case of updates from Free to Standard", async () => {
      tenantStub.resolves({
        ...standardCpeaTenant,
        tierChangedOn: new Date(new Date().getTime() + 10000).toISOString(),
      });

      const mockedMetrics = createCollectedMetrics(Measure.CATALOG_STORAGE, 1, MetricsReportingStatus.PENDING, 1);
      const odcFormatter = new OdcStorageFormatter(context);
      const result = await odcFormatter.formatUsage(mockedMetrics.reports);

      sinon.assert.match(result[0].service.plan, dwcPlans.FREE);
    });
  });

  describe("Subscription scenarios", () => {
    const subscriptionTenant = {
      type: TenantType.DWC,
      classification: TenantClassification.DWC,
      consumerAccountId: "consumerAccountId",
      freeTier: false,
      crmTenantId: "0000",
      license: {
        ...licensesToString(freePlanIncludedLicenses),
      },
    } as ITenantInformation;

    beforeEach(() => {
      isCanaryStub.returns(false);
    });
    it("Should correctly format usage entries", async () => {
      tenantStub.resolves(subscriptionTenant);

      const odcFormatter = new OdcStorageFormatter(context);

      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(Measure.CATALOG_STORAGE, 1, MetricsReportingStatus.PENDING);

      const result = await odcFormatter.formatUsage(mockedMetrics.reports);

      const expectedValue = convertOdcMbToBlocks(mockedMetrics.total);
      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: 0,
          },
          {
            id: dwcMetrics.CATALOG_STORAGE,
            value: expectedValue,
          },
        ],
        customDimensions: {
          dimension1: subscriptionTenant.crmTenantId,
          dimension2: subscriptionTenant.classification,
        },
      };

      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly report Subscription tenants under Standard Plan", async () => {
      tenantStub.resolves(subscriptionTenant);
      const mockedMetrics = createCollectedMetrics(Measure.CATALOG_STORAGE, 1, MetricsReportingStatus.PENDING, 1);
      const odcFormatter = new OdcStorageFormatter(context);

      const result = await odcFormatter.formatUsage(mockedMetrics.reports);

      sinon.assert.match(result[0].service.plan, dwcPlans.STANDARD);
    });
  });
});
