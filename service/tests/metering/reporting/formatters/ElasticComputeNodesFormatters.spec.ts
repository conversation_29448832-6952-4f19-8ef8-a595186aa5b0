/** @format */
// FILEOWNER: [Provisioning]

import { DS_HOURLY_PRICES, dwcMetrics } from "@sap/dwc-flexible-tenant-sizing";
import chai, { expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import { SinonStub, default as sinon } from "sinon";
import { Measure, TenantClassification, TenantType } from "../../../../../shared/provisioning/ftc/types";
import { AnalyticsProvisioningService, IApsTenantInformation } from "../../../../aps/AnalyticsProvisioningService";
import { FeatureFlagProvider } from "../../../../featureflags/FeatureFlagProvider";
import { TenantInformationProvider } from "../../../../featureflags/TenantInformationProvider";
import { ITenantInformation } from "../../../../featureflags/types";
import { testTenantUuid } from "../../../../lib/node";
import { MeteringUtils } from "../../../../metering/MeteringUtils";
import { MetricsHandler } from "../../../../metering/metrics/MetricsHandler";
import { IEcnDimensions } from "../../../../metering/metrics/tasks/measures/types/elasticComputeNodesTypes";
import { MetricsReportingStatus } from "../../../../metering/metrics/types";
import { ElasticComputeNodesFormatter } from "../../../../metering/reporting/formatters/ElasticComputeNodesFormatters";
import { METERING_SERVICE_ID, dwcPlans } from "../../../../metering/types";
import { licensesToString } from "../../../../provisioning/helpers";
import { standardPlanIncludedLicenses } from "../../../../provisioning/types";
import { RequestContext } from "../../../../repository/security/requestContext";
import { CustomerHanaRuntimeData } from "../../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { createCollectedMetrics } from "../helpers";

const ecnPrices = DS_HOURLY_PRICES.thresholdECNBlock;
chai.use(chaiAsPromised);

describe("ElasticComputeNodesFormatter.ts", function () {
  const context = RequestContext.createFromTenantId(testTenantUuid);
  const sandbox = sinon.createSandbox();
  let tenantStub: SinonStub;
  let ffStub: SinonStub;
  before(() => {
    tenantStub = sandbox.stub(TenantInformationProvider, "getTenantInformation");
    ffStub = sandbox.stub(FeatureFlagProvider, "getFeatureFlags");
    sandbox.stub(MetricsHandler.prototype, "getTotalUsageFromPeriod").resolves(0);
    sandbox.stub(CustomerHanaRuntimeData.prototype, "getOverallAssignedStorage").resolves();
  });
  after(() => {
    sandbox.restore();
  });

  beforeEach(() => {
    ffStub.resolves({} as any);
  });

  describe("Consumption scenarios", () => {
    const standardCpeaTenant = {
      type: TenantType.CPEA,
      classification: TenantClassification.DWCCPEA,
      consumerAccountId: "consumerAccountId",
      freeTier: false,
      tierChangedOn: new Date(0).toISOString(),
      crmTenantId: "0000",
      license: {
        ...licensesToString(standardPlanIncludedLicenses),
      },
    } as ITenantInformation;

    before(() => {
      const apsStub = sandbox.createStubInstance(AnalyticsProvisioningService);
      sandbox.stub(AnalyticsProvisioningService, "newFromCache").resolves(apsStub as any);
      apsStub.getTenantInfoFromCacheWithFallback.resolves({
        tenantuuid: testTenantUuid,
        type: TenantType.CPEA,
        globalaccountid: "global-account-uuid",
        subaccountid: "sub-account-uuid",
        serviceid: METERING_SERVICE_ID,
        planid: "plan-uuid",
        instanceid: "instance-uuid",
      } as IApsTenantInformation);
    });

    describe("Should correctly format usage entries", async () => {
      it("for single performanceClass MEMORY", async () => {
        const ecnFormatter = new ElasticComputeNodesFormatter(context);

        const dimension: IEcnDimensions = {
          blockHoursByPerformanceClass: {
            memory: 10,
            compute: 0,
            highCompute: 0,
          },
        };

        tenantStub.resolves(standardCpeaTenant);
        const meteringUtil = await MeteringUtils.fromContext(context);
        const mockedMetrics = createCollectedMetrics(
          Measure.ELASTIC_COMPUTE_NODE,
          1,
          MetricsReportingStatus.PENDING,
          10,
          undefined,
          dimension
        );

        const result = await ecnFormatter.formatUsage(mockedMetrics.reports);
        const expectedCost = mockedMetrics.reports[0].measureValue * ecnPrices.memory;

        const expectedReport = {
          id: mockedMetrics.reports[0].reportingId,
          timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
            0,
            mockedMetrics.reports[0].measurementPeriodStart.length - 1
          ),
          service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
          consumer: meteringUtil.consumer,
          measures: [
            {
              id: dwcMetrics.CAPACITY_UNITS_HOUR,
              value: expectedCost,
            },
            {
              id: dwcMetrics.ELASTIC_COMPUTE_NODES_HOUR,
              value: mockedMetrics.reports[0].measureValue,
            },
          ],
          customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
        };

        expect(result).to.be.deep.equal([expectedReport]);
      });

      it("for single performanceClass COMPUTE", async () => {
        const ecnFormatter = new ElasticComputeNodesFormatter(context);
        const dimension: IEcnDimensions = {
          blockHoursByPerformanceClass: {
            compute: 10,
            memory: 0,
            highCompute: 0,
          },
        };

        tenantStub.resolves(standardCpeaTenant);
        const meteringUtil = await MeteringUtils.fromContext(context);
        const mockedMetrics = createCollectedMetrics(
          Measure.ELASTIC_COMPUTE_NODE,
          1,
          MetricsReportingStatus.PENDING,
          10,
          undefined,
          dimension
        );

        const result = await ecnFormatter.formatUsage(mockedMetrics.reports);
        const expectedCost = mockedMetrics.reports[0].measureValue * ecnPrices.compute;

        const expectedReport = {
          id: mockedMetrics.reports[0].reportingId,
          timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
            0,
            mockedMetrics.reports[0].measurementPeriodStart.length - 1
          ),
          service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
          consumer: meteringUtil.consumer,
          measures: [
            {
              id: dwcMetrics.CAPACITY_UNITS_HOUR,
              value: expectedCost,
            },
            {
              id: dwcMetrics.ELASTIC_COMPUTE_NODES_HOUR,
              value: mockedMetrics.reports[0].measureValue,
            },
          ],
          customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
        };

        expect(result).to.be.deep.equal([expectedReport]);
      });

      it("for single performanceClass HIGH COMPUTE", async () => {
        const ecnFormatter = new ElasticComputeNodesFormatter(context);

        const dimension: IEcnDimensions = {
          blockHoursByPerformanceClass: {
            highCompute: 10,
            compute: 0,
            memory: 0,
          },
        };

        tenantStub.resolves(standardCpeaTenant);
        const meteringUtil = await MeteringUtils.fromContext(context);
        const mockedMetrics = createCollectedMetrics(
          Measure.ELASTIC_COMPUTE_NODE,
          1,
          MetricsReportingStatus.PENDING,
          10,
          undefined,
          dimension
        );

        const result = await ecnFormatter.formatUsage(mockedMetrics.reports);
        const expectedCost = mockedMetrics.reports[0].measureValue * ecnPrices.highCompute;

        const expectedReport = {
          id: mockedMetrics.reports[0].reportingId,
          timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
            0,
            mockedMetrics.reports[0].measurementPeriodStart.length - 1
          ),
          service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
          consumer: meteringUtil.consumer,
          measures: [
            {
              id: dwcMetrics.CAPACITY_UNITS_HOUR,
              value: expectedCost,
            },
            {
              id: dwcMetrics.ELASTIC_COMPUTE_NODES_HOUR,
              value: mockedMetrics.reports[0].measureValue,
            },
          ],
          customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
        };

        expect(result).to.be.deep.equal([expectedReport]);
      });

      it("for multiple performanceClasses", async () => {
        const ecnFormatter = new ElasticComputeNodesFormatter(context);

        const dimension: IEcnDimensions = {
          blockHoursByPerformanceClass: {
            compute: 10,
            memory: 10,
            highCompute: 10,
          },
        };

        tenantStub.resolves(standardCpeaTenant);
        const meteringUtil = await MeteringUtils.fromContext(context);
        const mockedMetrics = createCollectedMetrics(
          Measure.ELASTIC_COMPUTE_NODE,
          1,
          MetricsReportingStatus.PENDING,
          30,
          undefined,
          dimension
        );

        const result = await ecnFormatter.formatUsage(mockedMetrics.reports);
        const expectedCost =
          dimension.blockHoursByPerformanceClass.compute! * ecnPrices.compute +
          dimension.blockHoursByPerformanceClass.memory! * ecnPrices.memory +
          dimension.blockHoursByPerformanceClass.highCompute! * ecnPrices.highCompute;

        const expectedReport = {
          id: mockedMetrics.reports[0].reportingId,
          timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
            0,
            mockedMetrics.reports[0].measurementPeriodStart.length - 1
          ),
          service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
          consumer: meteringUtil.consumer,
          measures: [
            {
              id: dwcMetrics.CAPACITY_UNITS_HOUR,
              value: expectedCost,
            },
            {
              id: dwcMetrics.ELASTIC_COMPUTE_NODES_HOUR,
              value: mockedMetrics.reports[0].measureValue,
            },
          ],
          customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
        };

        expect(result).to.be.deep.equal([expectedReport]);
      });

      it("should throw if performanceClass is not supported", async () => {
        const ecnFormatter = new ElasticComputeNodesFormatter(context);

        const dimension: any = {
          blockHoursByPerformanceClass: {
            highMemory: 10,
          },
        };

        tenantStub.resolves(standardCpeaTenant);
        const mockedMetrics = createCollectedMetrics(
          Measure.ELASTIC_COMPUTE_NODE,
          1,
          MetricsReportingStatus.PENDING,
          30,
          undefined,
          dimension
        );

        await expect(ecnFormatter.formatUsage(mockedMetrics.reports)).to.be.rejected;
      });
    });
  });
});
