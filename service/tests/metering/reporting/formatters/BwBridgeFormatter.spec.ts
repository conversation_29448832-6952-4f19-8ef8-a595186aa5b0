/** @format */
// FILEOWNER: [Provisioning]

import { DS_HOURLY_PRICES, dwcMetrics } from "@sap/dwc-flexible-tenant-sizing";
import { SinonStub, default as sinon } from "sinon";
import { Measure, TenantClassification, TenantType } from "../../../../../shared/provisioning/ftc/types";
import { AnalyticsProvisioningService, IApsTenantInformation } from "../../../../aps/AnalyticsProvisioningService";
import { FeatureFlagProvider, IFeatureFlagsMap } from "../../../../featureflags/FeatureFlagProvider";
import { TenantInformationProvider } from "../../../../featureflags/TenantInformationProvider";
import { ITenantInformation } from "../../../../featureflags/types";
import { testTenantUuid } from "../../../../lib/node";
import { MeteringUtils } from "../../../../metering/MeteringUtils";
import { MetricsReportingStatus } from "../../../../metering/metrics/types";
import { BwBridgeFormatter } from "../../../../metering/reporting/formatters/BwBridgeFormatter";
import { METERING_SERVICE_ID, dwcPlans } from "../../../../metering/types";
import { RequestContext } from "../../../../repository/security/requestContext";
import { CustomerHanaRuntimeData } from "../../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { createCollectedMetrics } from "../helpers";

describe("BwBridgeFormatter.ts", function () {
  const context = RequestContext.createFromTenantId(testTenantUuid);
  const sandbox = sinon.createSandbox();
  let tenantStub: SinonStub;
  let apsStub: sinon.SinonStubbedInstance<AnalyticsProvisioningService>;
  before(() => {
    sandbox.stub(FeatureFlagProvider, "getFeatureFlags").resolves({} as IFeatureFlagsMap);
    tenantStub = sandbox.stub(TenantInformationProvider, "getTenantInformation");
    sandbox.stub(CustomerHanaRuntimeData.prototype, "getOverallAssignedStorage").resolves();
    sandbox.stub(CustomerHanaRuntimeData.prototype, "getStorageConsumptionOverview").resolves();
    apsStub = sandbox.createStubInstance(AnalyticsProvisioningService);
  });
  after(() => {
    sandbox.restore();
  });
  const BW_BRIDGE_BLOCK_PRICE = DS_HOURLY_PRICES.thresholdBWBridge1;
  describe("BW Bridge formatter", () => {
    const standardCpeaTenant = {
      type: TenantType.CPEA,
      classification: TenantClassification.DWCCPEA,
      consumerAccountId: "consumerAccountId",
      freeTier: false,
      tierChangedOn: new Date(0).toISOString(),
      crmTenantId: "0000",
      license: {
        thresholdDataWarehouseCloudUser: "1000000",
        thresholdStorage: "256",
        thresholdMemory: "32",
        thresholdVCPU: "2",
        thresholdDataLakeStorage: "5",
        thresholdDataLakeCompute: "6",
        thresholdDWCCU: "10000",
        thresholdBWBridge1: "256",
        thresholdRmsNodeHours: "500",
        thresholdRmsNodeHoursIncluded: "200",
        thresholdRmsConcurrency: "2",
        thresholdRmsConcurrencyIncluded: "2",
        thresholdCatalogNodeHours: "2",
        thresholdCatalogNodeHoursIncluded: "200",
        thresholdCatalogConcurrency: "2",
        thresholdCatalogConcurrencyIncluded: "1",
        thresholdCatalogStorage: "1024",
        thresholdCatalogStorageIncluded: "512",
        thresholdRmsPremiumOutbound: "5",
      },
    } as ITenantInformation;

    before(() => {
      sandbox.stub(AnalyticsProvisioningService, "newFromCache").resolves(apsStub as any);
      apsStub.getTenantInfoFromCacheWithFallback.resolves({
        tenantuuid: testTenantUuid,
        type: TenantType.CPEA,
        globalaccountid: "global-account-uuid",
        subaccountid: "sub-account-uuid",
        serviceid: METERING_SERVICE_ID,
        planid: "plan-uuid",
        instanceid: "instance-uuid",
      } as IApsTenantInformation);
    });

    it("Should correctly format usage entries for Standard Plan", async () => {
      tenantStub.resolves(standardCpeaTenant);
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(Measure.THRESHOLD_BW_BRIDGE, 1, MetricsReportingStatus.PENDING, 128);

      const bwBridgeFormatter = new BwBridgeFormatter(context);
      const result = await bwBridgeFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: BW_BRIDGE_BLOCK_PRICE,
          },
          {
            id: dwcMetrics.BW_BRIDGE,
            value: 1,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });

    it("Should correctly multiply the price value with the threshold bw bridge", async () => {
      tenantStub.resolves(standardCpeaTenant);
      const meteringUtil = await MeteringUtils.fromContext(context);
      const mockedMetrics = createCollectedMetrics(Measure.THRESHOLD_BW_BRIDGE, 1, MetricsReportingStatus.PENDING, 640);

      const bwBridgeFormatter = new BwBridgeFormatter(context);
      const result = await bwBridgeFormatter.formatUsage(mockedMetrics.reports);

      const expectedReport = {
        id: mockedMetrics.reports[0].reportingId,
        timestamp: mockedMetrics.reports[0].measurementPeriodStart.slice(
          0,
          mockedMetrics.reports[0].measurementPeriodStart.length - 1
        ),
        service: { ...meteringUtil.service, plan: dwcPlans.STANDARD },
        consumer: meteringUtil.consumer,
        measures: [
          {
            id: dwcMetrics.CAPACITY_UNITS_HOUR,
            value: BW_BRIDGE_BLOCK_PRICE * 5,
          },
          {
            id: dwcMetrics.BW_BRIDGE,
            value: 5,
          },
        ],
        customDimensions: { dimension1: "0000", dimension2: "DWCCPEA" },
      };
      sinon.assert.match(result, [expectedReport]);
    });
  });
});
