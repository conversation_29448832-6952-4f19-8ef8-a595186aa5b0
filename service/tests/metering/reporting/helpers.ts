/**
 * Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved.
 * FILEOWNER: [Provisioning]
 * @format
 */

import { Measure } from "@sap/dwc-flexible-tenant-sizing";
import { v4 as uuid } from "uuid";
import { TenantClassification, TenantType } from "../../../../shared/provisioning/ftc/types";
import { ITenantInformation } from "../../../featureflags/types";
import { testTenantUuid } from "../../../lib/node";
import { IEcnDimensions } from "../../../metering/metrics/tasks/measures/types/elasticComputeNodesTypes";
import { ILargeSystemsDimensions } from "../../../metering/metrics/tasks/measures/types/largeSystemsTypes";
import { IMemoryDimensions } from "../../../metering/metrics/tasks/measures/types/thresholdMemoryTypes";
import { MetricsReportingResponseDto, MetricsReportingStatus } from "../../../metering/metrics/types";
import { getCurrentDate, getMonthStart } from "../../../metering/reporting/usageReportHandling";
import { licensesToString } from "../../../provisioning/helpers";
import { freePlanIncludedLicenses, standardPlanIncludedLicenses } from "../../../provisioning/types";

/**
 * @param reports
 * Array of measures to be reported
 * @param total
 * Sum of of measures
 * @param ids
 *  Array of UUIDs from measures
 */

interface MockedMetricsHelper {
  reports: MetricsReportingResponseDto[];
  total: number;
  ids: string[];
}
export function createCollectedMetrics(
  measureName: Measure,
  amount: number,
  status: MetricsReportingStatus,
  fixMetricValue?,
  randomFormula = () => Math.random(),
  dimensions?: IEcnDimensions | IMemoryDimensions | ILargeSystemsDimensions
): MockedMetricsHelper {
  const reports: MetricsReportingResponseDto[] = [];
  let total = 0;
  const ids: string[] = [];
  for (let index = 0; index < amount; index++) {
    const report = {
      measurementPeriodStart: getMonthStart(),
      measurementPeriodEnd: getCurrentDate(),
      measureName,
      measureValue: fixMetricValue || randomFormula(),
      status,
      reportingId: uuid(),
      changedAt: getCurrentDate(),
      dimensions,
    } as MetricsReportingResponseDto;
    total += report.measureValue;
    reports.push(report);
    ids.push(report.reportingId);
  }
  return { reports, total, ids };
}

export const cpeaFreeTierTenant = {
  uuid: testTenantUuid,
  type: TenantType.CPEA,
  classification: TenantClassification.DWCCPEA,
  consumerAccountId: "consumerAccountId",
  freeTier: true,
  crmTenantId: "crm-tenant-id",
  license: {
    ...licensesToString({
      ...freePlanIncludedLicenses,
      thresholdMemory: "60",
      thresholdVCPU: "4",
      thresholdStorage: "128",
    }),
  },
} as ITenantInformation;

export const cpeaStandardTenant = {
  uuid: testTenantUuid,
  type: TenantType.CPEA,
  classification: TenantClassification.DWCCPEA,
  consumerAccountId: "consumerAccountId",
  freeTier: false,
  crmTenantId: "crm-tenant-id",
  license: {
    ...licensesToString({
      ...standardPlanIncludedLicenses,
      thresholdMemory: "120",
      thresholdVCPU: "8",
      thresholdStorage: "256",
    }),
  },
} as ITenantInformation;

export const spcSubscriptionTenant = {
  uuid: testTenantUuid,
  type: TenantType.DWC,
  classification: TenantClassification.DWC,
  consumerAccountId: "consumerAccountId",
  freeTier: false,
  crmTenantId: "crm-tenant-id",
  license: {
    ...licensesToString({
      ...standardPlanIncludedLicenses,
      thresholdMemory: "120",
      thresholdVCPU: "8",
      thresholdStorage: "256",
    }),
  },
} as ITenantInformation;

export const spcTestTenant = {
  uuid: testTenantUuid,
  type: TenantType.DWC,
  classification: TenantClassification.DWCcustomerTest,
  consumerAccountId: "consumerAccountId",
  freeTier: false,
  crmTenantId: "crm-tenant-id",
  license: {
    ...licensesToString({
      ...freePlanIncludedLicenses,
      thresholdMemory: "60",
      thresholdVCPU: "4",
      thresholdStorage: "128",
    }),
  },
} as ITenantInformation;

export const spcInternalTenant = {
  uuid: testTenantUuid,
  type: TenantType.DWC,
  classification: TenantClassification.DWCinternal,
  consumerAccountId: "consumerAccountId",
  freeTier: false,
  license: {
    ...licensesToString({
      ...freePlanIncludedLicenses,
      thresholdMemory: "60",
      thresholdVCPU: "4",
      thresholdStorage: "128",
    }),
  },
} as ITenantInformation;
