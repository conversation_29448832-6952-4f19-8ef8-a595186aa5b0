/** @format */

import sinon from "sinon";
import { TenantClassification } from "../../../shared/provisioning/ftc/types";
import { AnalyticsProvisioningService } from "../../aps/AnalyticsProvisioningService";
import { FeatureFlagProvider, IFeatureFlagsMap } from "../../featureflags/FeatureFlagProvider";
import { TenantInformationProvider } from "../../featureflags/TenantInformationProvider";
import * as libNode from "../../lib/node";
import { testTenantUuid } from "../../lib/node";
import { MeteringUtils } from "../../metering/MeteringUtils";
import { dwcPlans } from "../../metering/types";
import { RequestContext } from "../../repository/security/requestContext";
import {
  cpeaFreeTierTenant,
  cpeaStandardTenant,
  spcInternalTenant,
  spcSubscriptionTenant,
  spcTestTenant,
} from "./reporting/helpers";
import { apsTenantInformation } from "./utils";

describe("MeteringUtils.ts", () => {
  const context = RequestContext.createFromTenantId(testTenantUuid);
  const sandbox = sinon.createSandbox();
  let getTenantInformationStub, isCanaryStub, ffStub;

  const standardPlanToTest = [
    {
      ...spcSubscriptionTenant,
    },
    { ...spcInternalTenant },
    { ...spcTestTenant },
    {
      ...spcInternalTenant,
      classification: TenantClassification.DWCinternalProductive,
    },
    { ...spcInternalTenant, classification: TenantClassification.DWCpartnerTest },
    { ...spcInternalTenant, classification: TenantClassification.DWCPoC },
  ];

  const developmentPlanToTest = [
    { ...spcInternalTenant, classification: TenantClassification.Development },
    { ...spcInternalTenant, classification: "any-other-classification" },
  ];

  const featureFlags = {} as IFeatureFlagsMap;

  beforeEach(() => {
    ffStub = sandbox.stub(FeatureFlagProvider, "getFeatureFlags");
    ffStub.resolves(featureFlags);

    isCanaryStub = sandbox.stub(libNode, "isCanary");
    getTenantInformationStub = sandbox.stub(TenantInformationProvider, "getTenantInformation");
    sandbox
      .stub(AnalyticsProvisioningService.prototype, "getTenantInfoFromCacheWithFallback")
      .resolves(apsTenantInformation);
    sandbox
      .stub(AnalyticsProvisioningService.prototype, "fetchTenantList")
      .returns(Promise.resolve({ body: [apsTenantInformation] }) as any);
  });

  afterEach(() => {
    sandbox.restore();
  });
  describe("Define Plan", () => {
    it("Should correctly define plan for classification DWC", async () => {
      const spyDefinePlan = sandbox.spy(MeteringUtils, "definePlan");

      for (const tenant of standardPlanToTest) {
        getTenantInformationStub.resolves(tenant);
        const utils = await MeteringUtils.fromContext(context);
        sinon.assert.match(utils.plan, dwcPlans.STANDARD);
      }
      sinon.assert.match(spyDefinePlan.callCount, 6);
    });

    it("Should correctly define plan for Standard CiS tenants", async () => {
      getTenantInformationStub.resolves(cpeaStandardTenant);
      const utils = await MeteringUtils.fromContext(context);
      sinon.assert.match(utils.plan, dwcPlans.STANDARD);
    });

    it("Should correctly define plan for Free CiS tenants", async () => {
      getTenantInformationStub.resolves(cpeaFreeTierTenant);
      const utils = await MeteringUtils.fromContext(context);
      sinon.assert.match(utils.plan, dwcPlans.FREE);
    });

    it("Should correctly define plan for Development and unmapped classifications in case is CANARY landscape", async () => {
      isCanaryStub.returns(true);
      const spyDefinePlan = sandbox.spy(MeteringUtils, "definePlan");

      for (const tenant of developmentPlanToTest) {
        getTenantInformationStub.resolves(tenant);
        const utils = await MeteringUtils.fromContext(context);
        sinon.assert.match(utils.plan, dwcPlans.STANDARD);
      }
      sinon.assert.match(spyDefinePlan.callCount, 2);
    });

    it("Should define plan using TMS freeTier Property instead of using license combination - CIS Tenant", async () => {
      getTenantInformationStub.resolves({
        ...cpeaFreeTierTenant,
        freeTier: true,
        license: { thresholdMemory: "120", thresholdVCPU: "8", thresholdStorage: "256" },
      });
      const utils = await MeteringUtils.fromContext(context);
      sinon.assert.match(utils.plan, dwcPlans.FREE);
    });
  });

  describe("Define dimensions", () => {
    it("Should correctly map custom dimensions - crmTenantId and classification exist", async () => {
      getTenantInformationStub.resolves(cpeaStandardTenant);
      const utils = await MeteringUtils.fromContext(context);
      sinon.assert.match(utils.dimensions, { dimension1: "crm-tenant-id", dimension2: "DWCCPEA" });
    });

    it("Should correctly map custom dimensions - crmTenantId is undefined", async () => {
      getTenantInformationStub.resolves({ ...cpeaStandardTenant, crmTenantId: undefined });
      const utils = await MeteringUtils.fromContext(context);
      sinon.assert.match(utils.dimensions, { dimension2: "DWCCPEA" });
    });

    it("Should correctly map custom dimensions - classification is undefined", async () => {
      getTenantInformationStub.resolves({ ...cpeaStandardTenant, classification: undefined });
      const utils = await MeteringUtils.fromContext(context);
      sinon.assert.match(utils.dimensions, { dimension1: "crm-tenant-id" });
    });

    it("Should correctly map custom dimensions - crmTenantId and classification are undefined", async () => {
      getTenantInformationStub.resolves({ ...cpeaStandardTenant, crmTenantId: undefined, classification: undefined });
      const utils = await MeteringUtils.fromContext(context);
      sinon.assert.match(utils.dimensions, undefined);
    });
  });

  it("Should throw for Development and unmapped classifications in case IS NOT CANARY landscape", async () => {
    isCanaryStub.returns(false);
    const spyDefinePlan = sandbox.spy(MeteringUtils, "definePlan");

    for (const tenant of developmentPlanToTest) {
      getTenantInformationStub.resolves(tenant);
      try {
        await MeteringUtils.fromContext(context);
      } catch (error) {
        sinon.assert.match(error.message, "Could not define plan for reporting.");
      }
    }
    sinon.assert.match(spyDefinePlan.callCount, 2);
  });
});
