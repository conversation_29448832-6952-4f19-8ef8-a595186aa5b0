/*
 * @format
 */

import { expect } from "chai";
import sinon from "sinon";
import { Measure, TenantClassification, TenantType } from "../../../../shared/provisioning/ftc/types";
import { FeatureFlagProvider, IFeatureFlagsMap } from "../../../featureflags/FeatureFlagProvider";
import { TenantInformationProvider } from "../../../featureflags/TenantInformationProvider";
import { ITenantInformation } from "../../../featureflags/types";
import { testTenantUuid } from "../../../lib/node";
import { MetricsHandler } from "../../../metering/metrics/MetricsHandler";
import { ILargeSystemsDimensions } from "../../../metering/metrics/tasks/measures/types/largeSystemsTypes";
import { MetricsReportingResponseDto } from "../../../metering/metrics/types";
import { getUsageValues } from "../../../metering/usageServices/usageServices";
import { RequestContext } from "../../../repository/security/requestContext";

describe("usageServices.ts", function () {
  const context = RequestContext.createFromTenantId(testTenantUuid);
  let newSandbox: sinon.SinonSandbox;
  type MeasuredServices = keyof typeof Measure;
  const measureList: MeasuredServices[] = [
    Measure.PREMIUM_OUTBOUND,
    Measure.DI_EXTRACTION_HOURS,
    Measure.CATALOG_STORAGE,
    Measure.ELASTIC_COMPUTE_NODE,
    Measure.LARGE_SYSTEMS_STORAGE,
    Measure.LARGE_SYSTEMS_COMPUTE,
    Measure.LARGE_SYSTEMS_REQUESTS,
  ];

  type MeasureMap = Map<MeasuredServices, number>;
  let resultMap: MeasureMap;
  const expectedResultMap: MeasureMap = new Map([
    [Measure.PREMIUM_OUTBOUND, 10],
    [Measure.DI_EXTRACTION_HOURS, 10],
    [Measure.CATALOG_STORAGE, 1],
    [Measure.ELASTIC_COMPUTE_NODE, 10],
    [Measure.LARGE_SYSTEMS_STORAGE, 1],
    [Measure.LARGE_SYSTEMS_COMPUTE, 1],
    [Measure.LARGE_SYSTEMS_REQUESTS, 1000],
  ]);

  const tenantInformation = {
    uuid: testTenantUuid,
    consumerAccountId: testTenantUuid,
    classification: TenantClassification.DWC,
    type: TenantType.DWC,
    license: {
      thresholdStorage: "256",
      thresholdDataLakeStorage: "5",
      thresholdMemory: "32",
      thresholdVCPU: "2",
    },
    crmTenantId: "00000",
  } as ITenantInformation;

  const mockedData: MetricsReportingResponseDto[] = [
    {
      measurementPeriodStart: "2025-01-10T00:00:00.000Z",
      measurementPeriodEnd: "2025-01-10T01:00:00.000Z",
      measureName: Measure.CATALOG_STORAGE,
      measureValue: 1,
      status: "CONFIRMED",
      reportingId: "1",
      changedAt: "2025-01-10T01:00:00.000Z",
      dimensions: {
        largeSystemsUsage: {
          Storage: 0,
          APICalls: 1000,
          SparkVCPU: 0,
          SparkMemory: 4,
          SparkStorage: 0,
        },
        largeSystemsHANACU: {
          Storage: 0,
          APICalls: 0,
          SparkVCPU: 0,
          SparkMemory: 0,
          SparkStorage: 0,
        },
        isLsaRequestsOn: true,
      } as ILargeSystemsDimensions,
    },
    {
      measurementPeriodStart: "2025-01-10T00:00:00.000Z",
      measurementPeriodEnd: "2025-01-10T01:00:00.000Z",
      measureName: Measure.LARGE_SYSTEMS_STORAGE,
      measureValue: 1,
      status: "CONFIRMED",
      reportingId: "2",
      changedAt: "2025-01-10T01:00:00.000Z",
      dimensions: {
        largeSystemsUsage: {
          Storage: 0,
          APICalls: 1000,
          SparkVCPU: 0,
          SparkMemory: 4,
          SparkStorage: 0,
        },
        largeSystemsHANACU: {
          Storage: 0,
          APICalls: 0,
          SparkVCPU: 0,
          SparkMemory: 0,
          SparkStorage: 0,
        },
        isLsaRequestsOn: true,
      } as ILargeSystemsDimensions,
    },
  ];

  function prepareStubs() {
    newSandbox.stub(MetricsHandler.prototype, "getMetricsForReporting").resolves(mockedData);
    newSandbox.stub(MetricsHandler.prototype, "getTotalUsageFromPeriod").resolves(10);
    newSandbox.stub(TenantInformationProvider, "getTenantInformation").resolves(tenantInformation);
  }
  function prepareFeatureFlags(
    features = {
      DWCO_LARGE_SYSTEMS_REQUESTS: true,
    }
  ) {
    newSandbox.stub(FeatureFlagProvider, "getFeatureFlags").resolves(features as IFeatureFlagsMap);
  }

  before(() => {
    newSandbox = sinon.createSandbox();
    prepareStubs();
  });

  after(() => {
    newSandbox.restore();
  });

  describe("Usage Services", function () {
    beforeEach(() => {
      newSandbox.restore();
      prepareStubs();
      prepareFeatureFlags();
      newSandbox.useFakeTimers(new Date(Date.UTC(2025, 1, 15, 15, 30, 0, 0))); // 2025-01-23 15:30:00:000 yyyy-MM-DD
    });
    afterEach(() => {
      newSandbox.restore();
    });

    it("Should get usage values map from getUsageValues function.", async () => {
      resultMap = await getUsageValues(context, measureList);

      expect(resultMap).to.be.deep.equal(expectedResultMap);
    });
  });
});
