/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { TimeUnit } from "@sap/deepsea-utils";
import chai from "chai";
import sinon from "sinon";
import { DwcObjectTypes } from "../../../../shared/metadataImport/metadataImportTypes";
import { IFeatureFlagsMap } from "../../../featureflags/FeatureFlagProvider";
import { HybridParserFactory } from "../../../hybridParser/HybridParserFactory";
import { startupServiceWorkers } from "../../../workerthread/startupHook";
import { getAccessContext } from "../../repository/repository.utility";
import { getJestSnapshotPlugin } from "../../snapshotResolver";
import { getC_OverduePO } from "../testdata/S4/C_OverduePO";
import { ESH_S_PROCUREMENTPROJECT } from "../testdata/S4/ESH_S_PROCUREMENTPROJECT";
import { I_Bank_Account } from "../testdata/S4/I_Bank_Account";
import { I_CustomerReturnRateCube } from "../testdata/S4/I_CustomerReturnRateCube";
import { I_FINANCIALPLANNINGENTRYITEM } from "../testdata/S4/I_FinancialPlanningEntryItem/I_FinancialPlanningEntryItem";
import { I_GLAccountLineItem } from "../testdata/S4/I_GLAccountLineItem";
import { I_MaterialStock } from "../testdata/S4/I_MaterialStock";
import { I_PRACostCenter } from "../testdata/S4/I_PRACostCenter";
import { I_SalesOrder } from "../testdata/S4/I_SalesOrder";
import { ZFM_ESH_TEST_CUBE } from "../testdata/S4/ZFM_ESH_TEST_CUBE";
import { repeairedCsn } from "../testdata/S4/repairedCsn";
import { parseAndSnapshot } from "./utils";

chai.use(getJestSnapshotPlugin());

describe("CSN exposure import parser tests - S/4", function () {
  this.timeout(TimeUnit.MINUTES.toMillis(1));

  const TEST_SPACE = "TEST_SPACE";
  const TEST_CONNECTION = `${TEST_SPACE}_W4D`;

  let sandbox: sinon.SinonSandbox;

  before(() => {
    startupServiceWorkers();
    sandbox = sinon.createSandbox();
    sandbox.useFakeTimers(new Date(0));
  });

  after(() => {
    sandbox.restore();
  });

  it("should return import objects for repaired CSN", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: repeairedCsn,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as IFeatureFlagsMap,
        parseRepairedCsnMode: true,
      }),
      {
        validateCsn: false,
      }
    );
  });

  it("should return import objects for I_PRACostCenter (S/4, new text assoc mode)", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: I_PRACostCenter,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as IFeatureFlagsMap,
      }),
      {
        validateCsn: false,
      }
    );
  });

  it("should return import objects for ESH_S_PROCUREMENTPROJECT (S/4", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: ESH_S_PROCUREMENTPROJECT,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as IFeatureFlagsMap,
      }),
      {
        validateCsn: false,
      }
    );
  });

  it("should rename names within @Consumption.valueHelpDefinition", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: ESH_S_PROCUREMENTPROJECT,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as IFeatureFlagsMap,
      }),
      {
        validateCsn: false,
      }
    );
  });

  it("should return import objects for S4 CSN ZFM_ESH_TEST_CUBE", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: ZFM_ESH_TEST_CUBE,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as IFeatureFlagsMap,
      }),
      {
        validateCsn: false,
      }
    );
  });

  it("should return import objects for S4 CSN I_Bank_Account", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: I_Bank_Account,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as IFeatureFlagsMap,
      }),
      {
        validateCsn: false,
      }
    );
  });

  it("should return objects for S4 CSN I_FinancialPlanningEntryItem and remove cycles in BL objects", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: I_FINANCIALPLANNINGENTRYITEM,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
        featureFlags: {} as any,
      }),
      {
        validateCsn: false,
      }
    );
  });

  it("should return import objects for S4 CSN C_OverduePO", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: getC_OverduePO(),
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as any,
      }),
      {
        validateCsn: false,
      }
    );
  });

  it("should return import objects for S4 CSN I_GLAccountLineItem", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: I_GLAccountLineItem,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as IFeatureFlagsMap,
      }),
      {
        validateCsn: false,
      }
    );
  });

  it("should return import only dl objects objects for S4 CSN C_OverduePO", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: getC_OverduePO(),
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
        featureFlags: {} as any,
      }),
      {
        validateCsn: false,
      }
    );
  });

  it("should return import objects for S4 CSN I_CustomerReturnRateCube", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: I_CustomerReturnRateCube,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        deployUsingCDI: true,
        featureFlags: {} as any,
      }),
      {
        validateCsn: false,
      }
    );
  });

  it("should return import objects for S4 CSN I_MaterialStock", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: I_MaterialStock,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as any,
      }),
      {
        validateCsn: false,
      }
    );
  });

  it("should return import objects for S4 CSN I_SalesOrder", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: I_SalesOrder,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as any,
        deployUsingCDI: true,
        parseRepairedCsnMode: true,
      }),
      {
        validateCsn: false,
      }
    );
  });
});
