/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import chai from "chai";
import sinon from "sinon";
import { DwcObjectTypes } from "../../../../shared/metadataImport/metadataImportTypes";
import { IFeatureFlagsMap } from "../../../featureflags/FeatureFlagProvider";
import { HybridParserFactory } from "../../../hybridParser/HybridParserFactory";
import { startupServiceWorkers } from "../../../workerthread/startupHook";
import { getAccessContext } from "../../repository/repository.utility";
import { getJestSnapshotPlugin } from "../../snapshotResolver";
import { BW4CSN_BW4HCPR_EXAMPLE } from "../testdata/bw4hybridcsn/BW4CSN_BW4HCPR_EXAMPLE";
import { BW4CSN_BW4HCPR_EXAMPLE_FML } from "../testdata/bw4hybridcsn/BW4CSN_BW4HCPR_EXAMPLE_FML";
import { CR_CC_01 } from "../testdata/bw4hybridcsn/CR_CC_01";
import { CUBE_WITH_TEXTASSOCIATIONS } from "../testdata/bw4hybridcsn/CUBE_WITH_TEXTASSOCIATIONS";
import { Z5ECM001 } from "../testdata/bw4hybridcsn/Z5ECM001";
import { parseAndSnapshot } from "./utils";

chai.use(getJestSnapshotPlugin());

describe("CSN exposure import parser tests - BW/4", function () {
  this.timeout(10000);

  const TEST_SPACE = "TEST_SPACE";
  const TEST_CONNECTION = `${TEST_SPACE}_W4D`;

  let sandbox: sinon.SinonSandbox;

  before(() => {
    startupServiceWorkers();
    sandbox = sinon.createSandbox();
    sandbox.useFakeTimers(new Date(0));
  });

  after(() => {
    sandbox.restore();
  });

  it("should return remote tables and business objects for cube BW4HCPR_EXAMPLE w/o renaming data layer objects", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: BW4CSN_BW4HCPR_EXAMPLE,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: true,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as any,
      })
    );
  });

  it("should return valid CSN for CR_CC_01 don't rename DL objects", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: CR_CC_01,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: true,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as any,
      })
    );
  });

  it("should return remote tables and business objects for cube BW4HCPR_EXAMPLE w/o renaming data layer objects", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: BW4CSN_BW4HCPR_EXAMPLE,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: true,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as any,
      })
    );
  });

  it("should return all dwc objects for BW4HCPR_EXAMPLE_FML (csn exposure mode)", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: BW4CSN_BW4HCPR_EXAMPLE_FML,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: true,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as any,
        dwcObjectTypes: DwcObjectTypes.ALL,
      })
    );
  });

  it("should return dl & be dwc objects for BW4HCPR_EXAMPLE_FML (csn exposure mode)", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: BW4CSN_BW4HCPR_EXAMPLE_FML,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: true,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as any,
        dwcObjectTypes: DwcObjectTypes.DATALAYER_AND_BUSINESSENTITIES,
      })
    );
  });

  it("should return only dl objects for BW4HCPR_EXAMPLE_FML (csn exposure mode)", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: BW4CSN_BW4HCPR_EXAMPLE_FML,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: true,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as any,
        dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
      })
    );
  });

  it("should return objects for cubes from BW with new CSNdefinition (SET) - CSN exposure mode", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: Z5ECM001,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: true,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        dwcObjectTypes: DwcObjectTypes.ALL,
        featureFlags: { DWCO_MODELING_METADATAIMPORT_WORKERPOOL: true } as any,
      })
    );
  }).timeout(60000);

  it("should return import objects for CUBE_WITH_TEXTASSOCIATIONS in CSN exposure mode", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: CUBE_WITH_TEXTASSOCIATIONS,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: true,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as IFeatureFlagsMap,
      })
    );
  });

  it("should return DL import objects for CUBE_WITH_TEXTASSOCIATIONS in CSN exposure mode", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: CUBE_WITH_TEXTASSOCIATIONS,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: true,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        dwcObjectTypes: DwcObjectTypes.DATALAYER_ONLY,
        featureFlags: {} as IFeatureFlagsMap,
      })
    );
  });

  it("should return BL import objects for CUBE_WITH_TEXTASSOCIATIONS in CSN exposure mode", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: CUBE_WITH_TEXTASSOCIATIONS,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: true,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        dwcObjectTypes: DwcObjectTypes.ALL,
        featureFlags: {} as IFeatureFlagsMap,
      })
    );
  });
  it("should return import objects for CUBE_WITH_TEXTASSOCIATIONS (S/4, old resolved text assoc mode)", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: CUBE_WITH_TEXTASSOCIATIONS,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as IFeatureFlagsMap,
      })
    );
  });

  it("should return import objects for CUBE_WITH_TEXTASSOCIATIONS (S/4, new text assoc mode)", async () => {
    await parseAndSnapshot(
      HybridParserFactory.createHybridParser({
        hybridCsn: CUBE_WITH_TEXTASSOCIATIONS,
        remoteConnection: TEST_CONNECTION,
        context: getAccessContext(),
        bw4Import: false,
        spaceId: TEST_SPACE,
        renameDataLayerObjects: false,
        featureFlags: {} as IFeatureFlagsMap,
      })
    );
  });
});
