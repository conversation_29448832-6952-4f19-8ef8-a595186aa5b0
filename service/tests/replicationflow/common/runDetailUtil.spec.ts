/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { assert } from "chai";
import sinon from "sinon";
import { IReplicationFlowRunDetails, RunDetailUtil } from "../../../replicationflow/common/runDetailUtil";
import { RequestContext } from "../../../repository/security/requestContext";
import * as tableUtility from "../../../reuseComponents/systemTablesDeployment/src/helper";
import ReplicationFlowTestUtils from "../replicationflowTestUtils";

describe("service/tests/replicationflow/common/runDetailUtil.spec.ts", function () {
  this.timeout(60000);
  describe("runtime detail utility methods", function () {
    beforeEach(function () {
      this.sandbox = sinon.createSandbox();
    });

    before(async function () {
      this.contextStub = sinon.stub(RequestContext.createFromTenantId(ReplicationFlowTestUtils.DUMMY_SCHEMA));
    });

    afterEach(function () {
      this.sandbox.restore();
    });
    it("getReplicationflowDetailsByTaskLogId() returns the runtime details", async function () {
      const replicationFlowRunDetails: IReplicationFlowRunDetails = {
        TASK_LOG_ID: 12312,
        DATASET_EXTERNAL_ID: "QWE-ASD-ZXCV",
        DATASET_TASK_LOG_ID: 123456,
        NAME: ReplicationFlowTestUtils.DUMMY_FLOW_NAME,
        FLOW_EXTERNAL_ID: "qwert-assdfg",
        DATASET_EXTERNAL_STATUS: "completed",
        UPDATED_AT: new Date(),
        DETAILS: {},
      };
      const runtimeDetail = new RunDetailUtil(this.contextStub, ReplicationFlowTestUtils.DUMMY_SPACE, [
        replicationFlowRunDetails,
      ]);

      this.sandbox.stub(RunDetailUtil.prototype, "getDbClient").resolves({
        exec: () => Promise.resolve([replicationFlowRunDetails]),
        close: () => {},
      });
      this.sandbox.stub(RunDetailUtil.prototype, "getTecSchemaName").resolves(ReplicationFlowTestUtils.DUMMY_SCHEMA);
      this.sandbox.stub(tableUtility, "doesTableExist").resolves(true);
      const result = await runtimeDetail.getReplicationflowDetailsByTaskLogId();
      assert.deepStrictEqual(result?.[0], replicationFlowRunDetails);
    });

    /**
     * @issueid  DW101-77968
     */
    it("getReplicationflowDetailsByTaskLogId() returns the runtime details with new fields", async function () {
      const replicationFlowRunDetails: IReplicationFlowRunDetails = {
        TASK_LOG_ID: 12312,
        DATASET_EXTERNAL_ID: "QWE-ASD-ZXCV",
        DATASET_TASK_LOG_ID: 123456,
        NAME: ReplicationFlowTestUtils.DUMMY_FLOW_NAME,
        FLOW_EXTERNAL_ID: "qwert-assdfg",
        DATASET_EXTERNAL_STATUS: "completed",
        UPDATED_AT: new Date(),
        DETAILS: {},
        LAST_DATA_TRANSFERRED_AT: "",
        SOURCE_OBJECT_NAME: "SALES_DATA",
        TARGET_OBJECT_NAME: "SALES_DATA",
        SOURCE_CONNECTION_NAME: "AB_ODE",
        TARGET_CONNECTION_NAME: "$DWC",
        LOAD_TYPE: "REPLICATE",
      };
      const runtimeDetail = new RunDetailUtil(this.contextStub, ReplicationFlowTestUtils.DUMMY_SPACE, [
        replicationFlowRunDetails,
      ]);

      this.sandbox.stub(RunDetailUtil.prototype, "getDbClient").resolves({
        exec: () => Promise.resolve([replicationFlowRunDetails]),
        close: () => {},
      });
      this.sandbox.stub(RunDetailUtil.prototype, "getTecSchemaName").resolves(ReplicationFlowTestUtils.DUMMY_SCHEMA);
      this.sandbox.stub(tableUtility, "doesTableExist").resolves(true);
      const result = await runtimeDetail.getReplicationflowDetailsByTaskLogId();
      assert.deepStrictEqual(result?.[0], replicationFlowRunDetails);
    });

    it("getReplicationflowDetailsByTaskLogId() returns an error", async function () {
      const replicationFlowRunDetails: IReplicationFlowRunDetails = {
        TASK_LOG_ID: 12312,
        DATASET_EXTERNAL_ID: "QWE-ASD-ZXCV",
        DATASET_TASK_LOG_ID: 123456,
        NAME: ReplicationFlowTestUtils.DUMMY_FLOW_NAME,
        FLOW_EXTERNAL_ID: "qwert-assdfg",
        DATASET_EXTERNAL_STATUS: "completed",
        UPDATED_AT: new Date(),
        DETAILS: {},
      };
      const runtimeDetail = new RunDetailUtil(this.contextStub, ReplicationFlowTestUtils.DUMMY_SPACE, [
        replicationFlowRunDetails,
      ]);
      this.sandbox.stub(RunDetailUtil.prototype, "getDbClient").rejects("mockError");
      try {
        await runtimeDetail.getReplicationflowDetailsByTaskLogId();
      } catch (err) {
        assert.deepStrictEqual(err.name, "mockError");
      }
    });

    it("getReplicationflowDetailsByTaskLogId() returns undefined when details are not supplied", async function () {
      const runtimeDetail = new RunDetailUtil(this.contextStub, ReplicationFlowTestUtils.DUMMY_SPACE, []);
      const result = await runtimeDetail.getReplicationflowDetailsByTaskLogId();
      assert.deepStrictEqual(result, undefined);
    });

    it("upsertReplicationFlowRunDetails() thorws error when json are missing", async function () {
      const replicationFlowRunDetails: IReplicationFlowRunDetails = {
        TASK_LOG_ID: 12312,
        DETAILS: undefined,
      };
      const runtimeDetail = new RunDetailUtil(this.contextStub, ReplicationFlowTestUtils.DUMMY_SPACE, [
        replicationFlowRunDetails,
      ]);
      try {
        await runtimeDetail.upsertReplicationFlowRunDetails();
      } catch (err) {
        assert.equal(err.code, "jsonObjectMissing");
        assert.equal(err.status, 400);
      }
    });

    /**
     * @issueid DW101-103305
     */
    it("upsertReplicationFlowRunDetails() should log warning when too many values", async function () {
      const replicationFlowRunDetails: IReplicationFlowRunDetails = {
        TASK_LOG_ID: 12312,
        DATASET_EXTERNAL_ID: "QWE-ASD-ZXCV",
        DATASET_TASK_LOG_ID: 123456,
        NAME: ReplicationFlowTestUtils.DUMMY_FLOW_NAME,
        FLOW_EXTERNAL_ID: "qwert-assdfg",
        DATASET_EXTERNAL_STATUS: "RETRYING",
        UPDATED_AT: new Date(),
        DETAILS: {},
      };
      const runtimeDetail = new RunDetailUtil(this.contextStub, ReplicationFlowTestUtils.DUMMY_SPACE, [
        replicationFlowRunDetails,
      ]);
      this.sandbox.stub(RunDetailUtil.prototype, "getDbClient").resolves({
        prepare: () => ({
          execBatch: () => {
            const error = new Error("Too many values");
            (error as any).code = 269;
            throw error;
          },
          drop: () => Promise.resolve(),
        }),
        commit: () => Promise.resolve(),
        close: () => Promise.resolve(),
      });
      this.sandbox.stub(RunDetailUtil.prototype, "getTecSchemaName").resolves(ReplicationFlowTestUtils.DUMMY_SCHEMA);
      try {
        await runtimeDetail.upsertReplicationFlowRunDetails();
      } catch (err) {
        assert.equal(err.code, 269);
      }
    });

    /**
     * @issueid DW101-88350
     * @issueid DW101-92436
     */
    it("upsertReplicationFlowRunDetails() should call performDeleteTask when isTerminating is false and with replicationFlowRunDetails", async function () {
      const replicationFlowRunDetails: IReplicationFlowRunDetails = {
        TASK_LOG_ID: 12312,
        DATASET_EXTERNAL_ID: "QWE-ASD-ZXCV",
        DATASET_TASK_LOG_ID: 123456,
        NAME: ReplicationFlowTestUtils.DUMMY_FLOW_NAME,
        FLOW_EXTERNAL_ID: "qwert-assdfg",
        DATASET_EXTERNAL_STATUS: "RETRYING",
        UPDATED_AT: new Date(),
        DETAILS: {},
        LAST_DATA_TRANSFERRED_AT: "",
        SOURCE_OBJECT_NAME: "SALES_DATA",
        TARGET_OBJECT_NAME: "SALES_DATA",
        SOURCE_CONNECTION_NAME: "AB_ODE",
        TARGET_CONNECTION_NAME: "$DWC",
        LOAD_TYPE: "REPLICATE",
      };
      const runtimeDetail = new RunDetailUtil(
        this.contextStub,
        ReplicationFlowTestUtils.DUMMY_SPACE,
        [replicationFlowRunDetails],
        false
      );
      const performDeleteTaskStub = this.sandbox.stub(runtimeDetail, "performDeleteTask").resolves();
      this.sandbox.stub(RunDetailUtil.prototype, "getDbClient").resolves({
        prepare: () => ({
          execBatch: () => Promise.resolve([replicationFlowRunDetails]),
          drop: () => Promise.resolve(),
        }),
        commit: () => Promise.resolve(),
        close: () => Promise.resolve(),
      });
      this.sandbox.stub(RunDetailUtil.prototype, "getTecSchemaName").resolves(ReplicationFlowTestUtils.DUMMY_SCHEMA);
      this.sandbox.stub(tableUtility, "doesTableExist").resolves(true);
      await runtimeDetail.upsertReplicationFlowRunDetails();
      assert.strictEqual(performDeleteTaskStub.calledOnce, true, "performDeleteTaskStub called once");
    });
  });
});
