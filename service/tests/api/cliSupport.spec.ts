/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { defer } from "@sap/deepsea-sqlutils";
import { delay, isArrayNotEmpty, TimeUnit } from "@sap/deepsea-utils";
import { httpClient, ResolvedResponse } from "@sap/dwc-http-client";
import { SacClient } from "@sap/dwc-sac-client";
import chai, { assert } from "chai";
import chaiAsPromised from "chai-as-promised";
import { Request, Response } from "express";
import fs from "fs-extra";
import Status from "http-status-codes";
import * as path from "path";
import rewire from "rewire";
import sinon from "sinon";
import { mockRes } from "sinon-express-mock";
import { Capability } from "../../../shared/spaces/SpaceCapabilities";
import { FeatureFlagProvider, IFeatureFlagsMap } from "../../featureflags/FeatureFlagProvider";
import { testTenantUuid } from "../../lib/node";
import { RepositoryObjectClient } from "../../repository/client/repositoryObjectClient";
import { spaceDefaults } from "../../reuseComponents/spaces/src";
import * as uaaHelper from "../../reuseComponents/utility/uaaHelper";
import { UsageCollectionHandler } from "../../routes/feedback/usageCollectionHandler";
import { CodedError } from "../../server/errorResponse";
import * as RepoTestUtility from "../repository/repository.utility";
import { IPreparation } from "../repository/repository.utility";
import { agentWithDefaults, expectStatus, getTestServer } from "../routes/route.utilty";

const cli = rewire("../../api/cliSupport");
chai.use(chaiAsPromised);

describe.skip("/service/api/cliSupport.ts", function () {
  let fetchUaaTokenViaPasscodeGrantStub: sinon.SinonStub;
  let fakeNext: sinon.SinonSpy;
  let dwcCliRequest;
  let sandbox: sinon.SinonSandbox;
  let fsStub: sinon.SinonStub;
  let fetchUaaInfoStub: sinon.SinonStub;
  let getTenantIdFromHttpRequestStub: sinon.SinonStub;
  let httpClientStub: sinon.SinonStub;
  let usageCollectionHandlerStub: sinon.SinonStub;

  const dwcCliResponse: Response = {
    statusCode: 200,
    json: {},

    setHeader(name, value) {
      this.json = {
        etagHeaderName: name,
        etagHeaderValue: value,
      } as any;
    },

    status(status) {
      this.statusCode = status;
      return this;
    },

    send(msg) {
      this.json = msg;
    },
  } as Response;

  const passcodeJwtToken: uaaHelper.IUaaToken = {
    access_token: "ABCD1234",
    token_type: "bearer",
    expires_in: 60,
    scope: "scope1,scope2",
    jti: "test",
  };

  const uaaInfo: uaaHelper.IUaaInfo = {
    zone_name: "tenantId",
  };

  const userAuthResponse = {
    user: {
      userName: "userId",
      authTypes: [
        {
          name: "DWC_SPACEFILE",
          auth: {
            create: true,
            read: true,
            update: true,
            delete: true,
            execute: false,
            maintain: false,
            assign: true,
            createp: false,
            share: false,
          },
        },
      ],
    },
    defaultApp: "DWC",
  } as any;

  /**
   * User-Agent format:
   * @sap/dwc-cli+YYYY.M.D
   * @sap/dwc-cli+YYYY.MM.D
   * @sap/dwc-cli+YYYY.M.D-YYYYMMDDHHMMSSSS-SNAPSHOT
   * @sap/dwc-cli+YYYY.MM.D-YYYYMMDDHHMMSSSS-SNAPSHOT
   * @sap/datasphere-cli+YYYY.M.D
   * @sap/datasphere-cli+YYYY.MM.D
   * @sap/datasphere-cli+YYYY.M.D-YYYYMMDDHHMMSSSS-SNAPSHOT
   * @sap/datasphere-cli+YYYY.MM.D-YYYYMMDDHHMMSSSS-SNAPSHOT
   */
  const userAgent = "@sap/dwc-cli+2024.23.0-2024101108531612-SNAPSHOT";

  before(async function () {
    dwcCliRequest = { context: RepoTestUtility.createAdminRequestContext(testTenantUuid) } as Request;
    dwcCliRequest.path = "/api/v1/content";
    dwcCliRequest.headers = {
      passcode: "pass",
      publicfqdn: "host",
      "user-agent": userAgent,
    };
    dwcCliRequest.context.correlationId = "dummyCorrelationId";
    dwcCliRequest.method = "GET";
    sandbox = sinon.createSandbox();
    fsStub = sandbox.stub(fs, "readJSON");
    fsStub
      .withArgs(path.resolve(__dirname, "../../api/discovery.json"), { encoding: "utf8" })
      .resolves({ file: "fileContent" });
    fsStub
      .withArgs(path.resolve(__dirname, "../../api/whitelistedPaths.json"), { encoding: "utf8" })
      .resolves(["/api/v1/content", "/api/v1/discovery"]);
    fetchUaaInfoStub = sandbox.stub(uaaHelper, "fetchUaaInfo").callsFake(async () => uaaInfo);
    this.originalCliCacheTimeout = process.env.CLI_METRICS_CACHE_TIMEOUT_MINS;
    process.env.CLI_METRICS_CACHE_TIMEOUT_MINS = "0.001";
  });

  after(function () {
    sandbox.restore();
    process.env.CLI_METRICS_CACHE_TIMEOUT_MINS = this.originalCliCacheTimeout;
    cli.__set__("CACHED_CLI_DISCOVERY_ETAGS", new Map<string, string>());
  });

  describe("CLI Middleware Checks", function () {
    this.timeout(TimeUnit.MINUTES.toMillis(5));

    const preparations: IPreparation[] = [];
    const spaceMetaData = JSON.parse(JSON.stringify(spaceDefaults));

    before(async () => {
      dwcCliRequest.method = "PUT";
      spaceMetaData.capabilities = [Capability.hdlfStorage];
      await RepoTestUtility.enforceFeatureFlags({ DWCO_SPACE_CAPABILITIES: true });
      await RepoTestUtility.prepareSpaceUnstub(preparations, undefined, undefined, undefined, spaceMetaData);
    });

    after(function () {
      RepoTestUtility.deleteSpaces(preparations);
      RepoTestUtility.restoreStubs();
    });

    it("cli.cliRequestDenyHdlfSpaceskMiddleware should throw when CLI tries to creating a HDLF space", async function () {
      const spaceId = preparations[0].spaceId;
      dwcCliRequest = { ...dwcCliRequest, body: { [preparations[0].spaceId]: { spaceDefinition: spaceMetaData } } };
      const clonedCliRequest = Object.assign({}, dwcCliRequest);
      const clonedCliResponse = Object.assign({}, dwcCliResponse);
      await cli.cliRequestDenyHdlfSpaceskMiddleware()(clonedCliRequest, clonedCliResponse, fakeNext);
      assert.deepStrictEqual(clonedCliResponse.statusCode, 400);
      assert.deepStrictEqual(clonedCliResponse.json.toString(), `HDLF space creation ${spaceId} forbidden via CLI.`);
    });

    it("cli.cliRequestDenyHdlfSpaceskMiddleware should throw when CLI tries to edit HDLF space", async function () {
      const spaceId = preparations[0].spaceId;
      dwcCliRequest = { ...dwcCliRequest, query: { space: spaceId } };
      const clonedCliRequest = Object.assign({}, dwcCliRequest);
      const clonedCliResponse = Object.assign({}, dwcCliResponse);
      await cli.cliRequestDenyHdlfSpaceskMiddleware()(clonedCliRequest, clonedCliResponse, () => {
        assert.fail(
          `next() function should not have been called, there seems to be another issue with the checks before!`
        );
      });
      assert.deepStrictEqual(clonedCliResponse.statusCode, 400);
      assert.deepStrictEqual(clonedCliResponse.json.toString(), `HDLF space update ${spaceId} forbidden via CLI.`);
    });
  });

  describe("/api/v1/content", () => {
    const nonCliRequest = {} as Request;
    nonCliRequest.path = "/api/v1/content";
    nonCliRequest.headers = { Authorization: "Bearer access_token" };

    const nonSpaceApiRequest = {} as Request;
    nonSpaceApiRequest.path = "/api/v1/serverInfo";
    nonSpaceApiRequest.headers = { Authorization: "Bearer access_token" };

    const mockResponse = mockRes();

    const cliMetricsData = {
      actiondata: [
        {
          action: "GET",
          eventtype: "NA",
          feature: "cli",
          lastaccessedfeature: "NA",
          numoferrormessages: 0,
          sequenceno: 1,
        },
      ],
      language: "NA",
      usertype: "NA",
      devicepixelratio: -1,
      screenheight: -1,
      screenwidth: -1,
      host: "NA",
      sessionid: "NA",
      windowheight: -1,
      windowwidth: -1,
      pixeldepth: -1,
      colordepth: -1,
      browser: "NA",
      browserVersion: "NA",
      browserIsMobile: false,
      os: "NA",
      osVersion: "NA",
      system: "NA",
      retina: false,
      touch: false,
      websocket: false,
    };

    beforeEach(async function () {
      sinon.restore();
      fakeNext = sinon.fake();
      fetchUaaTokenViaPasscodeGrantStub = sinon
        .stub(uaaHelper, "fetchUaaTokenViaPasscodeGrant")
        .callsFake(async () => passcodeJwtToken);
      getTenantIdFromHttpRequestStub = sinon
        .stub(SacClient, "getSacTenantIdFromHttpRequest")
        .callsFake(async () => "2");
      httpClientStub = sinon
        .stub(httpClient, "call")
        .callsFake(async (options) => ({ status: 200, body: userAuthResponse } as any));
      usageCollectionHandlerStub = sinon
        .stub(UsageCollectionHandler, "sendUsageData")
        .callsFake(async () => ({} as ResolvedResponse));
      sinon.stub(FeatureFlagProvider, "getFeatureFlags").callsFake(async () => ({} as IFeatureFlagsMap));
    });

    afterEach(function () {
      sinon.restore();
    });

    it("cli.cliSupportValidationMiddleware should not throw error for valid cli request with appropriate feature flags enabled", async function () {
      dwcCliRequest = { ...dwcCliRequest, method: "GET" };
      await cli.cliSupportValidationMiddleware()(dwcCliRequest, mockResponse, fakeNext);
      await delay(100);
      const usageCollectionHandlerStubArg = Object.assign({}, usageCollectionHandlerStub.getCall(0).args[1]);
      assert.deepStrictEqual(usageCollectionHandlerStub.calledOnce, true);
      assert.deepStrictEqual(usageCollectionHandlerStub.calledOnce, true);
      assert(usageCollectionHandlerStubArg.actiondata[0].actiontimestamp);
      delete usageCollectionHandlerStubArg.actiondata[0].actiontimestamp;
      assert(usageCollectionHandlerStubArg.actiondata[0].actiontimezone);
      delete usageCollectionHandlerStubArg.actiondata[0].actiontimezone;
      assert.deepStrictEqual(usageCollectionHandlerStubArg, cliMetricsData);
      assert.deepStrictEqual(fakeNext.calledOnce, true);
      assert.deepStrictEqual(getTenantIdFromHttpRequestStub.calledOnce, true);
      assert.deepStrictEqual(dwcCliRequest.headers, {
        passcode: "pass",
        publicfqdn: "host",
        "x-sap-boc-approuter-orca-context": JSON.stringify({ orcaTenantId: "2" }),
        "user-agent": userAgent,
      });
    });

    it("cli.cliSupportValidationMiddleware should not throw error for non cli request with appropriate feature flags enabled", async function () {
      await cli.cliSupportValidationMiddleware()(nonCliRequest, mockResponse, fakeNext);
      await delay(100);
      assert.deepStrictEqual(fakeNext.calledOnce, true);
      assert.deepStrictEqual(usageCollectionHandlerStub.notCalled, true);
      assert.deepStrictEqual(getTenantIdFromHttpRequestStub.notCalled, true);
    });

    it("cli.cliSupportValidationMiddleware should not throw error for non space api request with appropriate feature flags enabled", async function () {
      await cli.cliSupportValidationMiddleware()(nonSpaceApiRequest, mockResponse, fakeNext);
      await delay(100);
      assert.deepStrictEqual(fakeNext.calledOnce, true);
      assert.deepStrictEqual(usageCollectionHandlerStub.notCalled, true);
      assert.deepStrictEqual(getTenantIdFromHttpRequestStub.notCalled, true);
    });

    it("cli.cliRequestEnrichmentMiddleware should not send error response non cli request", async function () {
      await cli.cliRequestEnrichmentMiddleware()(nonCliRequest, mockResponse, fakeNext);
      assert.deepStrictEqual(fakeNext.calledOnce, true);
      assert.deepStrictEqual(fetchUaaTokenViaPasscodeGrantStub.notCalled, true);
      assert.deepStrictEqual(fetchUaaInfoStub.notCalled, true);
      assert.deepStrictEqual(httpClientStub.notCalled, true);
    });

    it("cli.cliRequestEnrichmentMiddleware should not send error response non space api request", async function () {
      await cli.cliRequestEnrichmentMiddleware()(nonSpaceApiRequest, mockResponse, fakeNext);
      assert.deepStrictEqual(fakeNext.calledOnce, true);
      assert.deepStrictEqual(fetchUaaTokenViaPasscodeGrantStub.notCalled, true);
      assert.deepStrictEqual(fetchUaaInfoStub.notCalled, true);
      assert.deepStrictEqual(httpClientStub.notCalled, true);
    });

    it("cli.cliRequestEnrichmentMiddleware should not read discovery file multiple times for cli etag header", async function () {
      const clonedCliRequest1 = Object.assign({}, dwcCliRequest);
      const clonedCliResponse1 = Object.assign({}, dwcCliResponse);
      await cli.cliRequestEnrichmentMiddleware()(clonedCliRequest1, clonedCliResponse1, fakeNext);
      await cli.getCliDiscoveryEtagMiddleware()(clonedCliRequest1, clonedCliResponse1, fakeNext);
      assert.deepStrictEqual(
        clonedCliResponse1.json,
        {
          etagHeaderName: "x-sap-dwc-cli-discovery-etag",
          etagHeaderValue: "ekxw765Oj6X+h0xkBvkJrCcahqi5lKOhlKqTSS/gQ+s=",
        } as any,
        JSON.stringify(clonedCliResponse1.json)
      );

      const clonedCliRequest2 = Object.assign({}, dwcCliRequest);
      const clonedCliResponse2 = Object.assign({}, dwcCliResponse);
      await cli.cliRequestEnrichmentMiddleware()(clonedCliRequest2, clonedCliResponse2, fakeNext);
      await cli.getCliDiscoveryEtagMiddleware()(clonedCliRequest2, clonedCliResponse2, fakeNext);
      assert.deepStrictEqual(
        clonedCliResponse2.json,
        {
          etagHeaderName: "x-sap-dwc-cli-discovery-etag",
          etagHeaderValue: "ekxw765Oj6X+h0xkBvkJrCcahqi5lKOhlKqTSS/gQ+s=",
        } as any,
        JSON.stringify(clonedCliResponse2.json)
      );
    });

    it("cli.cliRequestEnrichmentMiddleware should enrich request with additional headers and not send error response", async function () {
      const clonedCliRequest = Object.assign({}, dwcCliRequest);
      const clonedCliResponse = Object.assign({}, dwcCliResponse);
      await cli.cliRequestEnrichmentMiddleware()(clonedCliRequest, clonedCliResponse, fakeNext);
      await cli.getCliDiscoveryEtagMiddleware()(clonedCliRequest, clonedCliResponse, fakeNext);
      assert.deepStrictEqual(fakeNext.calledTwice, true);
      assert.deepStrictEqual(fetchUaaTokenViaPasscodeGrantStub.calledOnce, true);
      assert.deepStrictEqual(httpClientStub.calledOnce, true);
      assert.deepStrictEqual(clonedCliRequest.headers.authorization, "Bearer ABCD1234");
      assert.deepStrictEqual(
        clonedCliRequest.headers["x-sap-boc-permissions"],
        '{"authschema":{"activityToBit":{"create":0,"read":1,"update":2,"delete":3,"execute":4,"maintain":5,"assign":6,"createp":7,"share":8},"bitToActivity":["create","read","update","delete","execute","maintain","assign","createp","share"],"length":9},"authtypes":[{"name":"DWC_SPACEFILE","auth":79}]}'
      );
    });

    it("cli.cliRequestEnrichmentMiddleware should return 401 when user dosen't have sufficient permissions", async function () {
      httpClientStub.restore();
      httpClientStub = sandbox
        .stub(httpClient, "call")
        .callsFake(async (options) => ({ status: 200, body: {} } as any));
      const clonedCliRequest = Object.assign({}, dwcCliRequest);
      const clonedCliResponse = Object.assign({}, dwcCliResponse);
      await cli.cliRequestEnrichmentMiddleware()(clonedCliRequest, clonedCliResponse, fakeNext);
      assert.deepStrictEqual(clonedCliResponse.statusCode, 401);
      assert.deepStrictEqual(
        clonedCliResponse.json,
        "Required privileges are missing for the user to execute the request." as any
      );
      assert.deepStrictEqual(fakeNext.notCalled, true);
      assert.deepStrictEqual(fetchUaaTokenViaPasscodeGrantStub.calledOnce, true);
      assert.deepStrictEqual(httpClientStub.calledOnce, true);
    });

    it("cli.cliRequestEnrichmentMiddleware should return 401 when fetch of auth token via passcode is unauthorized", async function () {
      const unauthorizedError = new CodedError("unauthorized", "Invalid Passcode.", 401);
      fetchUaaTokenViaPasscodeGrantStub.restore();
      fetchUaaTokenViaPasscodeGrantStub = sandbox
        .stub(uaaHelper, "fetchUaaTokenViaPasscodeGrant")
        .throws(unauthorizedError);
      const clonedCliRequest = Object.assign({}, dwcCliRequest);
      const clonedCliResponse = Object.assign({}, dwcCliResponse);
      await cli.cliRequestEnrichmentMiddleware()(clonedCliRequest, clonedCliResponse, fakeNext);
      assert.deepStrictEqual(clonedCliResponse.statusCode, 401);
      assert.deepStrictEqual(clonedCliResponse.json, "Invalid Passcode." as any);
      assert.deepStrictEqual(fakeNext.notCalled, true);
      assert.deepStrictEqual(fetchUaaTokenViaPasscodeGrantStub.calledOnce, true);
      assert.deepStrictEqual(httpClientStub.notCalled, true);
    });

    it("cli.cliRequestEnrichmentMiddleware should return internal server error when problems in fetch of auth token", async function () {
      const uaaError = new CodedError(
        "uaaRateLimitExceeded",
        "uaa call rate limit exceeded. Please try after sometime."
      );
      fetchUaaTokenViaPasscodeGrantStub.restore();
      fetchUaaTokenViaPasscodeGrantStub = sandbox.stub(uaaHelper, "fetchUaaTokenViaPasscodeGrant").throws(uaaError);
      const clonedCliRequest = Object.assign({}, dwcCliRequest);
      const clonedCliResponse = Object.assign({}, dwcCliResponse);
      await cli.cliRequestEnrichmentMiddleware()(clonedCliRequest, clonedCliResponse, fakeNext);
      assert.deepStrictEqual(clonedCliResponse.statusCode, 500);
      assert.deepStrictEqual(clonedCliResponse.json, "uaa call rate limit exceeded. Please try after sometime." as any);
      assert.deepStrictEqual(fakeNext.notCalled, true);
      assert.deepStrictEqual(fetchUaaTokenViaPasscodeGrantStub.calledOnce, true);
      assert.deepStrictEqual(httpClientStub.notCalled, true);
    });

    it("cli.tenantIDRequestEnrichmentMiddleware should enrich request with additional headers and not send error response", async function () {
      const clonedCliRequest = Object.assign({}, dwcCliRequest);
      const clonedCliResponse = Object.assign({}, dwcCliResponse);
      await cli.tenantIDRequestEnrichmentMiddleware()(clonedCliRequest, clonedCliResponse, fakeNext);
      assert.deepStrictEqual(fakeNext.calledOnce, true);
      assert.deepStrictEqual(fetchUaaInfoStub.calledOnce, true);
      assert.deepStrictEqual(clonedCliRequest.headers["x-sap-boc-tenant-id"], "tenantId");
    });

    it("cli.tenantIDRequestEnrichmentMiddleware should not return internal server error when problems in fetch of tenant information", async function () {
      const uaaError = new CodedError("noUaaAvailable", "no uaa information in environment found");
      fetchUaaInfoStub.restore();
      fetchUaaInfoStub = sandbox.stub(uaaHelper, "fetchUaaInfo").throws(uaaError);
      const clonedCliRequest = Object.assign({}, dwcCliRequest);
      const clonedCliResponse = Object.assign({}, dwcCliResponse);
      await cli.tenantIDRequestEnrichmentMiddleware()(clonedCliRequest, clonedCliResponse, fakeNext);
      assert.deepStrictEqual(clonedCliResponse.statusCode, 200);
      assert.deepStrictEqual(clonedCliResponse.json, {} as any);
      assert.deepStrictEqual(fakeNext.called, true);
    });

    it("cli.cliRequestEnrichmentMiddleware should enrich request with additional headers and cli etag for discovery route", async function () {
      const clonedCliRequest = Object.assign({}, dwcCliRequest);
      const clonedCliResponse = Object.assign({}, dwcCliResponse);
      clonedCliRequest.path = "/api/v1/discovery";
      await cli.cliRequestEnrichmentMiddleware()(clonedCliRequest, clonedCliResponse, fakeNext);
      await cli.getCliDiscoveryEtagMiddleware()(clonedCliRequest, clonedCliResponse, fakeNext);
      assert.deepStrictEqual(fakeNext.calledTwice, true);
      assert.deepStrictEqual(fetchUaaTokenViaPasscodeGrantStub.calledOnce, true);
      assert.deepStrictEqual(httpClientStub.calledOnce, true);
      assert.deepStrictEqual(clonedCliRequest.headers.authorization, "Bearer ABCD1234");
      assert.deepStrictEqual(
        clonedCliRequest.headers["x-sap-boc-permissions"],
        '{"authschema":{"activityToBit":{"create":0,"read":1,"update":2,"delete":3,"execute":4,"maintain":5,"assign":6,"createp":7,"share":8},"bitToActivity":["create","read","update","delete","execute","maintain","assign","createp","share"],"length":9},"authtypes":[{"name":"DWC_SPACEFILE","auth":79}]}'
      );
      assert.deepStrictEqual(
        clonedCliResponse.json,
        {
          etagHeaderName: "x-sap-dwc-cli-discovery-etag",
          etagHeaderValue: "ekxw765Oj6X+h0xkBvkJrCcahqi5lKOhlKqTSS/gQ+s=",
        } as any,
        JSON.stringify(clonedCliResponse.json)
      );
    });
  });

  describe("/api/v1/spaces", () => {
    beforeEach(async function () {
      cli.__set__("CACHED_CLI_DISCOVERY_ETAGS", new Map<string, string>());
      fakeNext = sinon.fake();
      fetchUaaTokenViaPasscodeGrantStub = sinon
        .stub(uaaHelper, "fetchUaaTokenViaPasscodeGrant")
        .callsFake(async () => passcodeJwtToken);
      getTenantIdFromHttpRequestStub = sinon
        .stub(SacClient, "getSacTenantIdFromHttpRequest")
        .callsFake(async () => "2");
      httpClientStub = sinon
        .stub(httpClient, "call")
        .callsFake(async (options) => ({ status: 200, body: userAuthResponse } as any));
      usageCollectionHandlerStub = sinon
        .stub(UsageCollectionHandler, "sendUsageData")
        .callsFake(async () => ({} as ResolvedResponse));
      sinon
        .stub(FeatureFlagProvider, "getFeatureFlags")
        .callsFake(async () => ({ DWCO_CLI_LIST_SPACES: true } as IFeatureFlagsMap));
    });

    afterEach(function () {
      sinon.restore();
    });

    it("cli.cliRequestEnrichmentMiddleware should enrich request with additional headers and cli etag for spaces route", async function () {
      const clonedCliRequest = Object.assign({}, dwcCliRequest);
      const clonedCliResponse = Object.assign({}, dwcCliResponse);
      clonedCliRequest.path = "/api/v1/spaces";
      await cli.cliRequestEnrichmentMiddleware()(clonedCliRequest, clonedCliResponse, fakeNext);
      await cli.getCliDiscoveryEtagMiddleware()(clonedCliRequest, clonedCliResponse, fakeNext);
      assert.deepStrictEqual(fakeNext.calledTwice, true);
      assert.deepStrictEqual(fetchUaaTokenViaPasscodeGrantStub.calledOnce, true);
      assert.deepStrictEqual(httpClientStub.calledOnce, true);
      assert.deepStrictEqual(clonedCliRequest.headers.authorization, "Bearer ABCD1234");
      assert.deepStrictEqual(
        clonedCliRequest.headers["x-sap-boc-permissions"],
        '{"authschema":{"activityToBit":{"create":0,"read":1,"update":2,"delete":3,"execute":4,"maintain":5,"assign":6,"createp":7,"share":8},"bitToActivity":["create","read","update","delete","execute","maintain","assign","createp","share"],"length":9},"authtypes":[{"name":"DWC_SPACEFILE","auth":79}]}'
      );
      assert.deepStrictEqual(
        clonedCliResponse.json,
        {
          etagHeaderName: "x-sap-dwc-cli-discovery-etag",
          etagHeaderValue: "+uftIkH+VXk94yIDRWou+A3Qu1tVLi6uzoqB5i7oIMw=",
        } as any,
        JSON.stringify(clonedCliResponse.json)
      );
    });
  });

  describe("/api/v1/spaces/:space/databaseusers/:databaseuser/resetpassword", () => {
    beforeEach(async function () {
      cli.__set__("CACHED_CLI_DISCOVERY_ETAGS", new Map<string, string>());
      fakeNext = sinon.fake();
      fetchUaaTokenViaPasscodeGrantStub = sinon
        .stub(uaaHelper, "fetchUaaTokenViaPasscodeGrant")
        .callsFake(async () => passcodeJwtToken);
      getTenantIdFromHttpRequestStub = sinon
        .stub(SacClient, "getSacTenantIdFromHttpRequest")
        .callsFake(async () => "2");
      httpClientStub = sinon
        .stub(httpClient, "call")
        .callsFake(async (options) => ({ status: 200, body: userAuthResponse } as any));
      usageCollectionHandlerStub = sinon
        .stub(UsageCollectionHandler, "sendUsageData")
        .callsFake(async () => ({} as ResolvedResponse));
      sinon
        .stub(FeatureFlagProvider, "getFeatureFlags")
        .callsFake(async () => ({ DWCO_CLI_RESET_DATABASEUSER_PASSWORD: true } as IFeatureFlagsMap));
    });

    afterEach(function () {
      sinon.restore();
    });

    it("cli.cliRequestEnrichmentMiddleware should enrich request with additional headers and cli etag for discovery route", async function () {
      const clonedCliRequest = Object.assign({}, dwcCliRequest);
      const clonedCliResponse = Object.assign({}, dwcCliResponse);
      clonedCliRequest.path = "/api/v1/spaces/:space/databaseusers/:databaseuser/resetpassword";
      await cli.cliRequestEnrichmentMiddleware()(clonedCliRequest, clonedCliResponse, fakeNext);
      await cli.getCliDiscoveryEtagMiddleware()(clonedCliRequest, clonedCliResponse, fakeNext);
      assert.deepStrictEqual(fakeNext.calledTwice, true);
      assert.deepStrictEqual(fetchUaaTokenViaPasscodeGrantStub.calledOnce, true);
      assert.deepStrictEqual(httpClientStub.calledOnce, true);
      assert.deepStrictEqual(clonedCliRequest.headers.authorization, "Bearer ABCD1234");
      assert.deepStrictEqual(
        clonedCliRequest.headers["x-sap-boc-permissions"],
        '{"authschema":{"activityToBit":{"create":0,"read":1,"update":2,"delete":3,"execute":4,"maintain":5,"assign":6,"createp":7,"share":8},"bitToActivity":["create","read","update","delete","execute","maintain","assign","createp","share"],"length":9},"authtypes":[{"name":"DWC_SPACEFILE","auth":79}]}'
      );
      assert.deepStrictEqual(
        clonedCliResponse.json,
        {
          etagHeaderName: "x-sap-dwc-cli-discovery-etag",
          etagHeaderValue: "q68H65IRxJuYZ7YKCMmV7oMAGO3lAgQos+Vl1UF6/f4=",
        } as any,
        JSON.stringify(clonedCliResponse.json)
      );
    });
  });

  describe("/api/v1/spaces/:space/:repoobjecttype/:objecttechname", function () {
    this.timeout(TimeUnit.SECONDS.toMillis(30));
    after(() => {
      RepoTestUtility.restoreStubs();
    });
    it("Should exclude commands from CLI if the FF is disabled", async () => {
      RepoTestUtility.restoreStub("getFeatureFlags");
      const tenantId = testTenantUuid;
      const context = RepoTestUtility.createAdminRequestContext(tenantId);
      const server = getTestServer({ tenantId, context });
      await RepoTestUtility.enforceFeatureFlags({ DWCO_INFRA_REPOSITORY_CLI_WRITE: false }, context);
      await agentWithDefaults(server)
        .get("/api/v1/discovery")
        .expect(expectStatus(Status.OK))
        .expect((res) => {
          const { tags, paths } = res.body;
          const exposedTags = tags.filter((tag: { name: string }) => tag.name.match(/^objects .+? (create|update)$/));
          if (isArrayNotEmpty(exposedTags)) {
            throw new Error(
              `The CLI write tags (${exposedTags
                .map((tag: { name: string }) => tag.name)
                .join(", ")}) are exposed unexpectedly`
            );
          }

          const exposedPaths = Object.entries(paths).filter(
            ([path, pathObj]: [string, any]) =>
              path !== "/dwaas-core/api/v1/spaces/{space}/connections" &&
              ((path.match(/^\/dwaas-core\/api\/v1\/spaces\/{space}\/[^\/]+\/{technicalName}$/) && pathObj.put) ||
                (path.match(/^\/dwaas-core\/api\/v1\/spaces\/{space}\/[^\/]+$/) && pathObj.post))
          );
          if (isArrayNotEmpty(exposedPaths)) {
            throw new Error(`The CLI save paths (${exposedPaths.join(", ")}) are exposed unexpectedly`);
          }
        });
    });
  });

  describe("/api/v1/configuration/system-connections", function () {
    let getObjectStub: sinon.SinonStub;
    this.timeout(TimeUnit.SECONDS.toMillis(30));
    beforeEach(async () => {
      sinon.restore();
      fakeNext = sinon.fake();
      getObjectStub = sinon.stub(RepositoryObjectClient, "getObject");
    });
    afterEach(function () {
      sinon.restore();
    });
    after(() => {
      RepoTestUtility.restoreStubs();
    });
    it("Should exclude commands from CLI if the FF is disabled", async () => {
      RepoTestUtility.restoreStub("getFeatureFlags");
      const tenantId = testTenantUuid;
      const context = RepoTestUtility.createAdminRequestContext(tenantId);
      const server = getTestServer({ tenantId, context });

      await RepoTestUtility.enforceFeatureFlags(
        {
          DWCO_BDC_CLI_FOR_DATA_PRODUCTS: false,
        },
        context
      );
      await agentWithDefaults(server)
        .get("/api/v1/discovery")
        .expect(expectStatus(Status.OK))
        .expect((res) => {
          const { tags, paths } = res.body;
          const exposedTags = tags.filter((tag: { name: string }) =>
            tag.name.startsWith("configuration system-connections")
          );
          if (isArrayNotEmpty(exposedTags)) {
            throw new Error(
              `The CLI tags (${exposedTags
                .map((tag: { name: string }) => JSON.stringify(tag.name))
                .join(", ")}) are exposed unexpectedly`
            );
          }

          const exposedPaths = Object.keys(paths).filter((path) =>
            path.match(/^\/dwaas-core\/api\/v1\/configuration\/system-connections.*$/)
          );
          if (isArrayNotEmpty(exposedPaths)) {
            throw new Error(`The CLI paths (${exposedPaths.join(", ")}) are exposed unexpectedly`);
          }
        });
    });

    it("Should call RepositoryObjectClient.getObject with expected parameters", async () => {
      RepoTestUtility.restoreStub("getFeatureFlags");
      const tenantId = testTenantUuid;
      const context = RepoTestUtility.createAdminRequestContext(tenantId);
      const server = getTestServer({ tenantId, context });
      await RepoTestUtility.enforceFeatureFlags(
        {
          DWCO_BDC_CLI_FOR_DATA_PRODUCTS: true,
        },
        context
      );
      const getResult = [
        {
          properties: {
            uclSystemInfo_uclSystemName: "QKZ_550_ORD",
            uclSystemInfo_systemType: "Internal S4HANA PCE",
          },
          id: "CCA8273006E7061F19003BAE6BDC4DC2",
          changeVersion: -99,
          qualifiedName: "019aed6a_b8d5_4e63_9d5f_93cb3bbc1dcd_HDL",
          kind: "sap.shared.connection",
          name: "019aed6a_b8d5_4e63_9d5f_93cb3bbc1dcd_HDL",
          folderId: "F4151650C91819D31800E8321F1A1D25",
          modificationDate: "2024-09-26 13:17:04.227000000 UTC",
          creationDate: "2024-09-16 19:00:16.072000000 UTC",
          hash: "802ac913333c51c36cbcdc523909044936be923449312838e4250801fbcf5ac5",
        },
        {
          properties: {
            uclSystemInfo_uclSystemName: "BDF570",
            uclSystemInfo_systemType: "SAP S/4HANA Cloud Private Edition",
          },
          id: "927262B2C04DC04419005FC2F4EE6C7B",
          changeVersion: -99,
          qualifiedName: "2dcc7945_1a55_479f_b19a_e3f5b4002b93_BDP",
          kind: "sap.shared.connection",
          name: "2dcc7945_1a55_479f_b19a_e3f5b4002b93_BDP",
          folderId: "F4151650C91819D31800E8321F1A1D25",
          modificationDate: "2025-01-29 13:05:36.586000000 UTC",
          creationDate: "2025-01-29 13:05:36.586000000 UTC",
          hash: "79768d635f0af92b0756b82f958419baf5197752c923b995c47b63e92b3c193f",
        },
        {
          properties: {
            uclSystemInfo_systemType: "SAP BDC Partner OEM Solution",
            uclSystemInfo_uclSystemName: "DBX Connect",
          },
          id: "6C245552A5BD47211900EFA27A43302E",
          changeVersion: -99,
          qualifiedName: "62cd758d_10f4_49ae_a627_d97d1408ad99_HDL",
          kind: "sap.shared.connection",
          name: "62cd758d_10f4_49ae_a627_d97d1408ad99_HDL",
          folderId: "F4151650C91819D31800E8321F1A1D25",
          modificationDate: "2025-04-10 09:21:01.530000000 UTC",
          creationDate: "2024-10-11 22:31:45.162000000 UTC",
          hash: "893ecef9da0b12e28e99b1e8cbb17a75cd69e74730a7f119c74231ff116ca9cc",
        },
      ];
      getObjectStub.callsFake(async () => getResult);

      await agentWithDefaults(server)
        .get("/api/v1/configuration/system-connections")
        .expect(expectStatus(Status.OK))
        .expect((res) => {
          const body = res.body;
          assert.deepStrictEqual(body, getResult);
        });
      assert.deepStrictEqual(getObjectStub.calledOnce, true);
      assert.deepStrictEqual(getObjectStub.args[0].length, 2);
      assert.deepStrictEqual(getObjectStub.args[0][1], {
        folderNames: "SAP-UCL",
        kind: "sap.shared.connection",
        details: [
          "businessName",
          "name",
          "creation_date",
          "modification_date",
          "uclSystemInfo_uclSystemName",
          "uclSystemTenantId",
          "uclSystemInfo_systemType",
          "uclFormationId",
          "#consumedSpacesInUse",
        ],
      } as any);
    });
  });

  describe("api/v1/catalog/dataproducts", function () {
    let getObjectStub: sinon.SinonStub;
    let updateJobStatusStub: sinon.SinonStub;
    const updatePromise = defer();
    this.timeout(TimeUnit.SECONDS.toMillis(30));
    beforeEach(async () => {
      sinon.restore();
      fakeNext = sinon.fake();
      getObjectStub = sinon.stub(RepositoryObjectClient, "getObject");
      updateJobStatusStub = sinon.stub(RepositoryObjectClient, "updateJobStatus");
    });
    afterEach(function () {
      sinon.restore();
    });
    after(() => {
      RepoTestUtility.restoreStubs();
    });
    it("Should exclude commands from CLI if the FF is disabled", async () => {
      RepoTestUtility.restoreStub("getFeatureFlags");
      const tenantId = testTenantUuid;
      const context = RepoTestUtility.createAdminRequestContext(tenantId);
      const server = getTestServer({ tenantId, context });

      await RepoTestUtility.enforceFeatureFlags(
        {
          DWCO_BDC_CLI_FOR_DATA_PRODUCTS: false,
        },
        context
      );
      await agentWithDefaults(server)
        .get("/api/v1/discovery")
        .expect(expectStatus(Status.OK))
        .expect((res) => {
          const { tags, paths } = res.body;
          const exposedTags = tags.filter((tag: { name: string }) => tag.name.startsWith("catalog data-products"));
          if (isArrayNotEmpty(exposedTags)) {
            throw new Error(
              `The CLI tags (${exposedTags
                .map((tag: { name: string }) => JSON.stringify(tag.name))
                .join(", ")}) are exposed unexpectedly`
            );
          }

          const exposedPaths = Object.keys(paths).filter((path) =>
            path.match(/^\/dwaas-core\/api\/v1\/catalog\/dataproducts.*$/)
          );
          if (isArrayNotEmpty(exposedPaths)) {
            throw new Error(`The CLI paths (${exposedPaths.join(", ")}) are exposed unexpectedly`);
          }
        });
    });

    it("Should call RepositoryObjectClient.updateJobStatus to set it FAILED when no ucl connection is found", async () => {
      RepoTestUtility.restoreStub("getFeatureFlags");
      const tenantId = testTenantUuid;
      const context = RepoTestUtility.createAdminRequestContext(tenantId);
      const server = getTestServer({ tenantId, context });
      await RepoTestUtility.enforceFeatureFlags(
        {
          DWCO_BDC_CLI_FOR_DATA_PRODUCTS: true,
        },
        context
      );
      getObjectStub.callsFake(async () => []);
      let i = 0;
      updateJobStatusStub.callsFake(async () => {
        if (++i > 2) {
          updatePromise.resolve();
        }
      });
      await agentWithDefaults(server)
        .post(
          "/api/v1/catalog/dataproducts/install?system-connection=cb29df37_bf79_4d42_8419_3f7b9e9fe89e_BDP&api-resource-ord-id=sap.s4com:apiResource:CompanyCode:v1&space=SAP_REUSE"
        )
        .expect(expectStatus(Status.ACCEPTED));
      await updatePromise.promise;

      assert.deepStrictEqual(updateJobStatusStub.calledThrice, true);
      assert.deepStrictEqual(updateJobStatusStub.args[0].length, 2);
      const jobStatusDefn = updateJobStatusStub.args[0][1];
      assert.deepStrictEqual(jobStatusDefn.jobStatus, "Failed");
      assert.deepStrictEqual(
        jobStatusDefn.executionDetails.global.detail.msg,
        'Error occurred while installing data product API "sap.s4com:apiResource:CompanyCode:v1" in space "SAP_REUSE": UCL shared connection named "cb29df37_bf79_4d42_8419_3f7b9e9fe89e_BDP" not found.'
      );
    });
  });

  describe("CRUD users, scoped roles and space users allocation", function () {
    this.timeout(TimeUnit.SECONDS.toMillis(30));
    after(() => {
      RepoTestUtility.restoreStubs();
    });
    it("Should exclude commands from CLI if the FF is disabled", async () => {
      const tenantId = RepoTestUtility.getTestTenantId();
      const context = RepoTestUtility.createAdminRequestContext(tenantId);
      await RepoTestUtility.enforceFeatureFlags({ DWCO_CLI_SDP: true, DWC_DUMMY_SPACE_PERMISSIONS: false }, context);
      const server = getTestServer({ tenantId, context });
      await agentWithDefaults(server)
        .get("/api/v1/discovery")
        .expect(expectStatus(Status.OK))
        .expect((res) => {
          const { tags, paths } = res.body;
          const exposedTags = tags.filter(
            (tag: { name: string }) =>
              tag.name.startsWith("users") || tag.name.startsWith("scoped-roles") || tag.name.startsWith("spaces users")
          );
          if (isArrayNotEmpty(exposedTags)) {
            throw new Error(
              `The CLI tags (${exposedTags
                .map((tag: { name: string }) => tag.name)
                .join(", ")}) are exposed unexpectedly`
            );
          }
          const exposedPaths = Object.keys(paths).filter(
            (path) =>
              path.match(/\/dwaas-core\/api\/v1\/users/) ||
              path.match(/\/dwaas-core\/api\/v1\/scopedroles.*$/) ||
              path.match(/\/dwaas-core\/api\/v1\/spaces\/{space}\/users.*$/)
          );
          if (isArrayNotEmpty(exposedPaths)) {
            throw new Error(`The CLI paths (${exposedPaths.join(", ")}) are exposed unexpectedly`);
          }
        });
    });
  });
});
