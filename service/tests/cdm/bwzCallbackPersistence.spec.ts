/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { SecureStore } from "@sap/dwc-credentials";
import { sql } from "@sap/prom-hana-client";
import chai from "chai";
import {
  deleteBwzCallbackConfig,
  fetchBwzCallbackConfig,
  storeBwzCallbackConfig,
  toSecureStoreKey,
} from "../../cdm/bwzCallback";
import { BwzCallbackConfig, BwzTokenStoragePayload } from "../../cdm/types";
import { getMD5Hash } from "../../cdm/utils";
import { DbClient } from "../../lib/DbClient";
import { testTenantUuid } from "../../lib/node";
import { RequestContext } from "../../repository/security/requestContext";
import { CustomerHana } from "../../reuseComponents/spaces/src";
import {
  PersistentObject,
  checkValidObjectsOfType,
  getPersistentObject,
  isValidTable,
} from "../routes/persistenceUtils";

const persistenceLayer: PersistentObject[] = [
  {
    schema: CustomerHana.tenantOwnerSchema,
    name: "BWZ_CALLBACK_CONFIG",
    type: "TABLE",
  },
];

const persistenceKeyMap = new Map<string, string>()
  .set("CALLBACK_URL", "url")
  .set("CFLP_PROVIDER_ID", "cdmProvider")
  .set("PREFER", "prefer")
  .set("CDM_HASH", "cdmHash");

const incomingCallBackConfig: BwzCallbackConfig = {
  url: "https://portal-service-1.cfapps.some-landscape.hana.ondemand.com/semantic/public/callback/autosync/abc123",
  cdmProvider: "provider_1",
  cdmHash: getMD5Hash(
    JSON.stringify({
      "@odata.context": "$metadata#entities",
      value: [
        {
          id: "entity_11",
        },
        {
          id: "entity_12",
        },
      ],
    })
  ),
  prefer: "odata.track-changes, sap-cflp-callback-on-change; odata.callback; maxpagesize=200",
  token: `callback-token-${crypto.randomUUID()}`,
};

async function cleanupPersistence(context: IRequestContext, tenantManagerDbClient: DbClient) {
  const secureStore = SecureStore.fromRequestContext(context);

  const saved = await fetchBwzCallbackConfig(context);
  for (const config of saved) {
    const credsKey = toSecureStoreKey(config.url, config.cdmProvider);
    const creds = await secureStore.retrieve<BwzTokenStoragePayload>(credsKey);
    if (creds) {
      await secureStore.delete(credsKey);
    }
  }

  await tenantManagerDbClient.execute(
    sql`TRUNCATE TABLE ${sql.quoted(CustomerHana.tenantOwnerSchema, "BWZ_CALLBACK_CONFIG")}`
  );
}

describe("Build Work Zone Integration: persistence layer", function () {
  this.timeout(60000);
  let context: RequestContext;
  let tenantManagerDbClient: DbClient;

  beforeEach(async () => {
    context = RequestContext.createFromTenantId(testTenantUuid);
    context.preventCustomerHanaAutoUpgrade = true;
    const customerHana = await CustomerHana.fromRequestContext(context);
    tenantManagerDbClient = await customerHana.getTenantManagerClient();
  });

  this.afterEach(async () => {
    await cleanupPersistence(context, tenantManagerDbClient);
    if (context) {
      await context.finish();
    }
  });

  it("Should confirm that all persistency objects exist and are active", async () => {
    // check that all persistence layer objects have been successfully deployed
    // eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled
    const objects = await Promise.all(
      persistenceLayer.map((entry) => getPersistentObject(tenantManagerDbClient, entry.schema!, entry.name))
    );
    for (let i = 0; i < objects.length; i++) {
      const object = objects[i];
      chai
        .expect(persistenceLayer[i].name)
        .to.be.equal(object?.name, `${persistenceLayer[i].name} not found on the database`);
      chai
        .expect(persistenceLayer[i].type)
        .to.be.equal(
          object?.type,
          `${persistenceLayer[i].name} exists but has type ${object?.type} instead of the expected ${persistenceLayer[i].type}`
        );
    }
    // check that all tables are valid
    await checkValidObjectsOfType(persistenceLayer, tenantManagerDbClient, "TABLE", isValidTable);
  });

  it.skip("Should save Build WZ callback configuration on Customer HANA except the callback access token", async function () {
    await storeBwzCallbackConfig(context, incomingCallBackConfig);

    const expected: Partial<BwzCallbackConfig> = {
      ...incomingCallBackConfig,
    };
    delete expected.token;
    const saved = await tenantManagerDbClient.execute<any>(
      sql`SELECT * FROM ${sql.quoted(CustomerHana.tenantOwnerSchema, "BWZ_CALLBACK_CONFIG")};`
    );

    chai.expect(saved).to.be.an("array").and.to.have.length(1);
    const resultKeys = Object.keys(saved[0]);
    chai.expect(resultKeys).to.have.length(4);

    const actual = {};
    for (const key of persistenceKeyMap.keys()) {
      chai.expect(resultKeys).to.include(key);
      actual[persistenceKeyMap.get(key)!] = saved[0][key];
    }
    chai.expect(actual).to.be.deep.equal(expected);
  });

  it("Should save Build WZ callback access token into CF credstore", async function () {
    await storeBwzCallbackConfig(context, incomingCallBackConfig);

    const secureStore = SecureStore.fromRequestContext(context);
    const token =
      (
        await secureStore.retrieve<BwzTokenStoragePayload>(
          toSecureStoreKey(incomingCallBackConfig.url, incomingCallBackConfig.cdmProvider)
        )
      )?.token || null;

    chai.expect(token).to.be.equal(incomingCallBackConfig.token);
  });

  it("Should read stored Build WZ callback configuration including access token", async function () {
    await storeBwzCallbackConfig(context, incomingCallBackConfig);

    const saved = await fetchBwzCallbackConfig(context);
    chai.expect(saved).to.be.an("array").and.to.have.length(1);
    chai.expect(saved[0]).to.deep.equal(incomingCallBackConfig);
  });

  it("Should store different Build WZ callback configurations for different Build WZ URLs", async function () {
    const configs = [
      {
        ...incomingCallBackConfig,
        url: "https://url_1",
      },
      {
        ...incomingCallBackConfig,
        url: "https://url_2",
      },
    ];

    for (const config of configs) {
      await storeBwzCallbackConfig(context, config);
    }

    const saved = await fetchBwzCallbackConfig(context);
    for (const config of saved) {
      chai.expect(configs).to.be.deep.include(config);
    }
  });

  it("Should store different Build WZ callback configurations for different content providers", async function () {
    const configs = [
      {
        ...incomingCallBackConfig,
        cdmProvider: "provider_1",
      },
      {
        ...incomingCallBackConfig,
        cdmProvider: "provider_2",
      },
    ];

    for (const config of configs) {
      await storeBwzCallbackConfig(context, config);
    }

    const saved = await fetchBwzCallbackConfig(context);
    for (const config of saved) {
      chai.expect(configs).to.be.deep.include(config);
    }
  });

  it("Should delete previousely stored Build WZ callback configuration with full key", async function () {
    await storeBwzCallbackConfig(context, incomingCallBackConfig);

    await deleteBwzCallbackConfig(context, {
      url: incomingCallBackConfig.url,
      cdmProvider: incomingCallBackConfig.cdmProvider,
    });

    const result = await fetchBwzCallbackConfig(context);

    chai.expect(result).to.be.empty;
  });

  it.skip("Should delete all previousely stored Build WZ callback configurations for a given callback URL", async function () {
    const configs = [
      {
        ...incomingCallBackConfig,
        cdmProvider: "provider_1",
      },
      {
        ...incomingCallBackConfig,
        cdmProvider: "provider_2",
      },
    ];

    for (const config of configs) {
      await storeBwzCallbackConfig(context, config);
    }
    await deleteBwzCallbackConfig(context, {
      url: incomingCallBackConfig.url,
    });

    const result = await fetchBwzCallbackConfig(context);

    chai.expect(result).to.be.empty;
  });

  it("Should delete all previousely stored Build WZ callback configurations for a given content provider", async function () {
    const configs = [
      {
        ...incomingCallBackConfig,
        url: "url_1",
      },
      {
        ...incomingCallBackConfig,
        url: "url_2",
      },
    ];

    for (const config of configs) {
      await storeBwzCallbackConfig(context, config);
    }
    await deleteBwzCallbackConfig(context, {
      cdmProvider: incomingCallBackConfig.cdmProvider,
    });

    const result = await fetchBwzCallbackConfig(context);

    chai.expect(result).to.be.empty;
  });
});
