/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { getImplementation } from "@sap/deepsea-sqlutils";
import * as technicalUtil from "@sap/deepsea-sqlutils/dist/technicalUserUtils";
import { ObjectKind, RepositoryRouteKind } from "@sap/deepsea-types";
import { TimeUnit } from "@sap/deepsea-utils";
import { expect } from "chai";
import path from "path";
import { RepositoryObjectClient } from "../../../repository/client/repositoryObjectClient";
import { CustomerHana } from "../../../reuseComponents/spaces/src";
import * as reuse from "../../../table/reuse";
import * as RepoTestUtility from "../../repository/repository.utility";

describe(path.basename(__filename), function () {
  this.timeout(TimeUnit.MINUTES.toMillis(5));

  const tenantId = RepoTestUtility.randomTenantId("acnExport").toLowerCase();

  let context;

  before(async () => {
    await RepoTestUtility.recordActiveConnections();
    await RepoTestUtility.prepareTenant(tenantId);
    await RepoTestUtility.enforceFeatureFlags(
      {
        DWCO_BDC_REPOSITORY_TRANSPORT_DATA: true,
      },
      context
    );

    context = RepoTestUtility.getTenantRequestContext(tenantId);
  });

  after(async () => {
    await context.finish();
    await RepoTestUtility.cleanupTenant(tenantId);
  });

  it("should export data objects with a warning if the number of rows exceeds the limit", async () => {
    const processAcnExportChunk = getImplementation(RepositoryRouteKind.exportedChunk)?.upsert;
    expect(processAcnExportChunk).to.be.a("function");

    const exportedChunk = {
      TEST_SPACE: {
        objects: {
          DataObject1: {
            dataObjects: {
              DataObject1: {
                kind: ObjectKind.dataObject,
                "@DataWarehouse.enclosingObject": "Table",
              },
            },
          },
        },
      },
    };
    RepoTestUtility.addStub(technicalUtil, "blockNonTechnicalUser").resolves(undefined);
    RepoTestUtility.addStub(technicalUtil, "bypassPrivilegeForService2Service").resolves(undefined);
    RepoTestUtility.addStub(RepositoryObjectClient, "callAcnProcessExportChunk").resolves({
      content: JSON.stringify(exportedChunk),
      totalChunks: 1,
      status: "SUCCESS",
      message: "",
    });

    RepoTestUtility.addStub(reuse, "getLocalTableColumns").resolves([
      {
        name: "ID",
        type: "string",
        position: 1,
      },
    ]);

    const data = [{ ID: "1" }];
    RepoTestUtility.addStub(CustomerHana, "fromRequestContext").resolves({
      getSpaceManagerClient: async () => ({
        exec: async (query: string) => {
          if (query.includes("COUNT")) {
            return [{ COUNT: 3001 }];
          } else {
            return data;
          }
        },
      }),
    });

    RepoTestUtility.addStub(RepositoryObjectClient, "getObject").resolves([
      {
        properties: {},
      },
    ]);

    const req = {
      body: {
        maxChunkSize: 1000,
      },
      params: {
        chunkNo: 1,
        resourceId: "testResource",
      },
    };
    const res = {
      status: () => ({
        send: (body: any) => {
          exportedChunk.TEST_SPACE.objects.DataObject1.dataObjects.DataObject1["data"] = JSON.stringify(data);
          exportedChunk.TEST_SPACE.objects.DataObject1.dataObjects.DataObject1["format"] = "json";
          expect(body?.content).to.equal(JSON.stringify(exportedChunk));
          expect(body?.status).to.equal("WARNING");
        },
      }),
    };

    await processAcnExportChunk!(context, req as any, res as any);
  });
});
