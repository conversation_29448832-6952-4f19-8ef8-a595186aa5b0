/** @format */

import { IAcnChunkContent } from "@sap/deepsea-crud";
import { IRepositoryObject, IUniqueIdentifierByPath } from "@sap/deepsea-types";
import { TimeUnit, UniqueObjectIdentifier } from "@sap/deepsea-utils";
import { expect } from "chai";
import sinon from "sinon";
import { RepositoryObjectClient } from "../../../repository/client/repositoryObjectClient";
import * as CheckImportPostProcess from "../../../repository/contentnetwork/acnApiUtils";
import { parsePotentialDataProductChunk, processAcnImportChunk } from "../../../repository/contentnetwork/acnImport";
import { WrapSpaceDeployForTest } from "../../../repository/contentnetwork/middleware/spaceDeploy";
import * as DataProductTestUtility from "../../../repository/dataproducts/dataProductsUtils";
import { wrapForTestsAcnImportApi } from "../../../repository/importmanager/src/ImportApi";
import { matchUclSharedConnections } from "../../../repository/importmanager/src/dataProductInstallUtils";
import { uclSharedConnection } from "../../inputs/dataProductControler/getObjectResults";
import * as RepoTestUtility from "../../repository/repository.utility";

let mockServiceInstance: any;
describe("processAcnImportChunk for data product intent", function () {
  this.timeout(TimeUnit.MINUTES.toMillis(5));
  const tenantId = RepoTestUtility.randomTenantId("acnDeleteSpace").toLowerCase();
  let context;
  let sandbox: sinon.SinonSandbox;
  let processImportChunkStub;
  let mockDeployRuntime: sinon.SinonStub;
  let executeImportStub;
  let getCatalogV1SystemStub;
  let getCatalogV1SystemCallsCount = 0;
  let postCatalogV1DependencyStub;
  let getObjectStub;
  let noConsumingTargetSpace = false; // set to true to test the intelligent app onboarding case
  let notDeployedTargetSpace = false; // set to true to test the case where the target space is not deployed  let getObjectStub;
  let createCanBeConsumedBySpaceDependencyStub;
  let checkImportPostProcessStub;
  let createSpaceServiceStub;
  let executeImportApiFunctionStub;
  let executeImportApiFunctionCallsCount = 0;
  before(async () => {
    await RepoTestUtility.recordActiveConnections();
    await RepoTestUtility.prepareTenant(tenantId);
    sandbox = sinon.createSandbox();
  });

  beforeEach(async () => {
    context = RepoTestUtility.getTenantRequestContext(tenantId);
    mockDeployRuntime = sinon.stub();
    processImportChunkStub = RepoTestUtility.addStub(RepositoryObjectClient, "callAcnProcessImportChunk").resolves({
      statusResults: {
        status: "SUCCESS",
      },
      message: "Import completed successfully",
      importStatus: true,
      oContent: {
        Space1: {
          id: "space1-id",
          name: "Space1",
          description: "Sample space definition",
          type: "SpaceType",
        },
      },
      spaceName2Id: {
        Space1: "space1-id",
      },
      currentObjectName: "SampleObject",
      bRemoteTable: false,
      remoteEntityName: "RemoteEntity",
      remoteConnectionName: "RemoteConnection",
    });
    getCatalogV1SystemCallsCount = 0;
    getCatalogV1SystemStub = RepoTestUtility.addStub(RepositoryObjectClient, "callGetCatalogV1SystemQuery").callsFake(
      async (_context, _params) => {
        const systemId = `testSystemId${++getCatalogV1SystemCallsCount}`;
        return [
          {
            systemId,
            applicationTenantId: "testTenantId",
            applicationNamespace: "testNamespace",
            applicationVersion: "testVersion",
            name: "testName",
            className: "SYSTEM",
            formations: [],
            propertyTags: [],
          },
        ];
      }
    );
    const importHeaderStatus = {
      importId: "testImportId",
    };
    executeImportStub = RepoTestUtility.addStub(wrapForTestsAcnImportApi, "executeImportApiFunction").resolves(
      importHeaderStatus
    );
    mockDeployRuntime.resolves({
      messages: {
        hasError: false,
        infos: ["Space deployed successfully"],
      },
    });
    mockServiceInstance = {
      deployRuntime: mockDeployRuntime,
    };
    createSpaceServiceStub = RepoTestUtility.addStub(WrapSpaceDeployForTest, "createSpaceService").returns(
      mockServiceInstance
    );
    createCanBeConsumedBySpaceDependencyStub = RepoTestUtility.addStub(
      DataProductTestUtility,
      "createCanBeConsumedBySpaceDependency"
    ).resolves();
    executeImportApiFunctionCallsCount = 0;
    executeImportApiFunctionStub = RepoTestUtility.addStub(
      wrapForTestsAcnImportApi,
      "executeImportApiFunction"
    ).callsFake(async (context, _connectionId, _targetSpaceUuid, _metadataImportParameters, _executionParameters?) => {
      const ingestionSpace =
        executeImportApiFunctionCallsCount++ === 0 ? "NEW_ING" : `ING_${executeImportApiFunctionCallsCount}`;
      const result = {
        status: "SUCCESS",
        messages: {
          hasError: false,
          infos: ["Space deployed successfully"],
        },
        ingestionSpace,
      };

      return result;
    });
    checkImportPostProcessStub = RepoTestUtility.addStub(CheckImportPostProcess, "checkImportPostProcess").resolves({
      status: "SUCCESS",
      messages: {
        hasError: false,
        infos: ["Space deployed successfully"],
      },
    });
    RepoTestUtility.addStub(RepositoryObjectClient, "updateJobStatus");
    noConsumingTargetSpace = false; // reset the flag for the next test
    notDeployedTargetSpace = false; // reset the flag for the next test
    getObjectStub = RepoTestUtility.addStub(RepositoryObjectClient, "getObject").callsFake(async (_context, params) => {
      if (
        params.filters?.length === 3 &&
        params.filters[0] === "uclSystemTenantId:test-tenant-id" &&
        params.filters[1] === "uclFormationId:test-formation-id" &&
        params.filters[2] === "typeId:HDLFS"
      ) {
        return [uclSharedConnection];
      } else if (params.filters?.startsWith("name:") && params.kind === "sap.dwc.space") {
        return [{ id: "spaceTargetId" }];
      } else if (params.kind === "sap.shared.connection" && params.details?.includes("#sourceSystemMapping")) {
        const localUCL1 = buildTestUclConnection({ "*": "testSystemId1" }, "test-tenant-id-1", "ucl1");
        const localUCL2 = buildTestUclConnection({ "*": "testSystemId2" }, "test-tenant-id-2");
        return [localUCL2, localUCL1]; // inverse order to check the implementation is not dependent on the order of the connections
      } else if (
        params.details?.includes("#objectStatus") &&
        params.dependencyFilters?.startsWith("source:ucl.shared.canBeConsumedBySpace")
      ) {
        if (noConsumingTargetSpace) {
          return []; // target space is authorized to consumed the 2nd system
        } else {
          const res: any = {
            id: "spaceTargetId",
            properties: { "#objectStatus": notDeployedTargetSpace ? "0" : "1" }, // target space is deployed space
          };
          return [res];
        }
      } else if (params.details?.includes("#objectStatus")) {
        return [
          {
            properties: { "#objectStatus": "1" }, // target space is deployed space
          },
        ];
      }
      return [];
    });
  });

  afterEach(async () => {
    await context.finish();
    RepoTestUtility.restoreStubs();
    sinon.restore();
    sandbox.restore();
  });

  it("Intelligent Application Onboarding case - should process ACN import chunk successfully", async () => {
    noConsumingTargetSpace = true;
    const catalogApiCatalogIds = {
      rootObjectIds: ["1231111111111111111", "2231111111111111111"],
    };
    postCatalogV1DependencyStub = RepoTestUtility.addStub(
      RepositoryObjectClient,
      "callPostCatalogV1DependencyQuery"
    ).callsFake(async (context, queryBody) => {
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences.length).to.equal(1);
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences[0].nativeUniqueName).to.equal(
        "sap.s4:dataProduct:SalesOrder"
      );
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences[0].systemId).to.equal("testSystemId1");
      return catalogApiCatalogIds;
    });
    const mockParams = {
      customParameters: { uclSystemTenantId: "test-tenant-id", uclFormationId: "test-formation-id" },
      createSpaceContentResult: {},
      importOptions: "test-options",
    } as any;
    const mockRequiredApiResources = {
      BDF940: {
        applicationType: "S4",
        connectionType: "HDLFS",
        formationType: "BDC",
        apiResources: ["sap.s4:dataProduct:SalesOrder"],
        systemId: "testSystemId1",
      },
    };
    const mockJobStatus = { status: "IN_PROGRESS" } as any;

    const result = await processAcnImportChunk(
      context,
      "targetSpaceName",
      mockParams,
      mockRequiredApiResources,
      mockJobStatus
    );

    expect(result).to.deep.equal({
      status: "SUCCESS",
      messages: {
        hasError: false,
        infos: ["Space deployed successfully"],
      },
      objectData: { spaceMapping: { BDF940: "NEW_ING" } },
    });
    expect(processImportChunkStub.calledTwice).to.be.true; // 2 ACN import calls one in dryRun and one to execute the import
    expect(getObjectStub.calledTwice).to.be.true; // 2 getObject: get UCL, get target space name
    expect(getCatalogV1SystemStub.calledOnce).to.be.true;
    expect(postCatalogV1DependencyStub.calledOnce).to.be.true;
    expect(executeImportStub.calledOnce).to.be.true;
    expect(createSpaceServiceStub.calledOnce).to.be.true;
    expect(createCanBeConsumedBySpaceDependencyStub.calledOnce).to.be.true;
    expect(executeImportApiFunctionStub.calledOnce).to.be.true;
    expect(checkImportPostProcessStub.calledOnce).to.be.true;
  });

  it("Repository Package With 1 Ingestion Space - should process ACN import chunk successfully", async () => {
    await RepoTestUtility.enforceFeatureFlags({ DWCO_BDC_REPOSITORY_DP_INTENT_CUSTOMER: true }, context);
    const catalogApiCatalogIds = {
      rootObjectIds: ["1231111111111111111", "2231111111111111111"],
    };
    let postCatalogV1DependencyCallsCount = 0;
    postCatalogV1DependencyStub = RepoTestUtility.addStub(
      RepositoryObjectClient,
      "callPostCatalogV1DependencyQuery"
    ).callsFake(async (context, queryBody) => {
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences.length).to.equal(1);
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences[0].nativeUniqueName).to.equal(
        "sap.s4:dataProduct:SalesOrder"
      );
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences[0].systemId).to.equal(
        `testSystemId${++postCatalogV1DependencyCallsCount}`
      );
      return catalogApiCatalogIds;
    });
    const mockParams = {
      createSpaceContentResult: {},
      importOptions: "test-options",
    } as any;
    const mockRequiredApiResources = {
      BDF940: {
        applicationType: "S4",
        connectionType: "HDLFS",
        formationType: "BDC",
        apiResources: ["sap.s4:dataProduct:SalesOrder"],
        systemId: "testSystemId1",
      },
    };
    const mockJobStatus = { status: "IN_PROGRESS" } as any;

    const result = await processAcnImportChunk(
      context,
      "targetSpaceName2", // needs to be changed else the UT fails due to space name cache
      mockParams,
      mockRequiredApiResources,
      mockJobStatus
    );

    expect(result).to.deep.equal({
      status: "SUCCESS",
      messages: {
        hasError: false,
        infos: ["Space deployed successfully"],
      },
      objectData: {
        spaceMapping: { BDF940: "NEW_ING" },
        dataProductInstallationStatus: {
          BDF940: {
            apiResources: ["sap.s4:dataProduct:SalesOrder"],
            systemId: "testSystemId1",
          },
        },
      },
    });
    expect(processImportChunkStub.calledTwice).to.be.true; // 2 ACN import calls one in dryRun and one to execute the import
    expect(getObjectStub.callCount).to.equal(3); // 3 calls: get UCL, get target space name, check target space can consume
    expect(getCatalogV1SystemStub.calledOnce).to.be.true;
    expect(postCatalogV1DependencyStub.calledOnce).to.be.true;
    expect(executeImportStub.calledOnce).to.be.true;
    expect(createSpaceServiceStub.called).to.be.false; // no deployment of the target space
    expect(createSpaceServiceStub.called).to.be.false; // no creation of canBeConsumedBySpace dependency
    expect(executeImportApiFunctionStub.calledOnce).to.be.true;
    expect(checkImportPostProcessStub.calledOnce).to.be.true; // only one post process call
  });

  it("Repository Package With 2 Ingestion Space - should process ACN import chunk successfully", async () => {
    await RepoTestUtility.enforceFeatureFlags({ DWCO_BDC_REPOSITORY_DP_INTENT_CUSTOMER: true }, context);
    const catalogApiCatalogIds = {
      rootObjectIds: ["1231111111111111111", "2231111111111111111"],
    };
    let postCatalogV1DependencyCallsCount = 0;
    postCatalogV1DependencyStub = RepoTestUtility.addStub(
      RepositoryObjectClient,
      "callPostCatalogV1DependencyQuery"
    ).callsFake(async (context, queryBody) => {
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences.length).to.equal(1);
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences[0].nativeUniqueName).to.equal(
        "sap.s4:dataProduct:SalesOrder"
      );
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences[0].systemId).to.equal(
        `testSystemId${++postCatalogV1DependencyCallsCount}`
      );
      return catalogApiCatalogIds;
    });
    const mockParams = {
      createSpaceContentResult: {},
      importOptions: "test-options",
    } as any;
    const mockRequiredApiResources = {
      BDF940: {
        applicationType: "S4",
        connectionType: "HDLFS",
        formationType: "BDC",
        apiResources: ["sap.s4:dataProduct:SalesOrder"],
        systemId: "testSystemId1",
      },
      BDF950: {
        applicationType: "S4",
        connectionType: "HDLFS",
        formationType: "BDC",
        apiResources: ["sap.s4:dataProduct:SalesOrder"],
        systemId: "testSystemId2",
      },
    };
    const mockJobStatus = { status: "IN_PROGRESS" } as any;

    const result = await processAcnImportChunk(
      context,
      "targetSpaceName3", // needs to be changed else the UT fails due to space name cache
      mockParams,
      mockRequiredApiResources,
      mockJobStatus
    );

    expect(result).to.deep.equal({
      status: "SUCCESS",
      messages: {
        hasError: false,
        infos: ["Space deployed successfully"],
      },
      objectData: {
        spaceMapping: { BDF940: "NEW_ING", BDF950: "ING_2" },
        dataProductInstallationStatus: {
          BDF940: {
            apiResources: ["sap.s4:dataProduct:SalesOrder"],
            systemId: "testSystemId1",
          },
          BDF950: {
            apiResources: ["sap.s4:dataProduct:SalesOrder"],
            systemId: "testSystemId2",
          },
        },
      },
    });
    expect(processImportChunkStub.calledTwice).to.be.true; // 2 ACN import calls one in dryRun and one to execute the import
    expect(getObjectStub.callCount).to.equal(4); // 4 calls: get UCL, get target space name, check target space can consume (x2 because 2 spaces)
    expect(getCatalogV1SystemStub.calledTwice).to.be.true; // 2 calls for 2 systems
    expect(postCatalogV1DependencyStub.calledTwice).to.be.true; // 2 calls for 2 systems
    expect(executeImportStub.calledTwice).to.be.true; // 2 calls for 2 systems
    expect(createSpaceServiceStub.called).to.be.false; // no deployment of the target space
    expect(createSpaceServiceStub.called).to.be.false; // no creation of canBeConsumedBySpace dependency
    expect(executeImportApiFunctionStub.calledTwice).to.be.true; // 2 calls for 2 systems
    expect(checkImportPostProcessStub.calledOnce).to.be.true; // only one post process call
  });

  it("The UCL shared connections should be matched as expected ", () => {
    const mockRequiredApiResources: any = {
      BDF940: {
        applicationType: "S4",
        connectionType: "HDLFS",
        formationType: "BDC",
        apiResources: ["sap.s4:dataProduct:SalesOrder"],
        systemId: "testSystemId1",
      },
    };
    const targetSpaceName = "targetSpaceName";
    const uclBDF940WithWildCards = buildTestUclConnection(
      { "*": "testSystemId1" },
      "test-tenant-id-1",
      "BDF940 with WC"
    );
    const uclBDF940ForOtherSpace = buildTestUclConnection(
      { otherSpace1: "testSystemId1" },
      "test-tenant-id-2",
      "BDF940 for other"
    );
    const uclBDF940ForOtherSpaceAndWC = buildTestUclConnection(
      { otherSpace2: "testSystemId2", "*": "testSystemId1" },
      "test-tenant-id-2",
      "BDF940 for other+WC"
    );
    const uclBDF940ForGoodSpace = buildTestUclConnection(
      { [targetSpaceName]: "testSystemId1" },
      "test-tenant-id-2",
      "BDF940 for good"
    );
    const uclBDF950WithWildCards = buildTestUclConnection(
      { "*": "testSystemId2" },
      "test-tenant-id-2",
      "BDF950 with WC"
    );

    // *** Test single ingestion space case ***
    // wildcard mapping should match
    let result = matchUclSharedConnections(context, targetSpaceName, mockRequiredApiResources, [
      uclBDF940ForOtherSpace,
      uclBDF940WithWildCards,
    ]);
    expect(result).to.have.length(1);
    expect(result.map((o) => o.name)).to.deep.equal(["BDF940 with WC"]);
    // wildcard mapping should match (case 2)
    result = matchUclSharedConnections(context, targetSpaceName, mockRequiredApiResources, [
      uclBDF940ForOtherSpace,
      uclBDF940ForOtherSpaceAndWC,
    ]);
    expect(result).to.have.length(1);
    expect(result.map((o) => o.name)).to.deep.equal(["BDF940 for other+WC"]);
    // specific mapping should match
    result = matchUclSharedConnections(context, targetSpaceName, mockRequiredApiResources, [
      uclBDF940ForOtherSpace,
      uclBDF940ForGoodSpace,
    ]);
    expect(result).to.have.length(1);
    expect(result.map((o) => o.name)).to.deep.equal(["BDF940 for good"]);
    // specific mapping should match even if wildcard mapping exists
    result = matchUclSharedConnections(context, targetSpaceName, mockRequiredApiResources, [
      uclBDF940ForOtherSpace,
      uclBDF940WithWildCards,
      uclBDF940ForGoodSpace,
    ]);
    expect(result).to.have.length(1);
    expect(result.map((o) => o.name)).to.deep.equal(["BDF940 for good"]);
    // specific mapping should match even if wildcard mapping exists (case 2)
    result = matchUclSharedConnections(context, targetSpaceName, mockRequiredApiResources, [
      uclBDF940ForOtherSpace,
      uclBDF940ForGoodSpace,
      uclBDF940ForOtherSpaceAndWC,
    ]);
    expect(result).to.have.length(1);
    expect(result.map((o) => o.name)).to.deep.equal(["BDF940 for good"]);

    // *** Test two ingestion spaces case ***
    mockRequiredApiResources.BDF950 = {
      applicationType: "S4",
      connectionType: "HDLFS",
      formationType: "BDC",
      apiResources: ["sap.s4:dataProduct:SalesOrder"],
      systemId: "testSystemId2",
    };
    // keep order with wildcard mapping (1)
    result = matchUclSharedConnections(context, targetSpaceName, mockRequiredApiResources, [
      uclBDF940WithWildCards,
      uclBDF950WithWildCards,
    ]);
    expect(result).to.have.length(2);
    expect(result.map((o) => o.name)).to.deep.equal(["BDF940 with WC", "BDF950 with WC"]);
    // keep order with wildcard mapping (2)
    result = matchUclSharedConnections(context, targetSpaceName, mockRequiredApiResources, [
      uclBDF950WithWildCards,
      uclBDF940WithWildCards,
    ]);
    expect(result).to.have.length(2);
    expect(result.map((o) => o.name)).to.deep.equal(["BDF940 with WC", "BDF950 with WC"]);
    // keep order with specific mapping (1)
    result = matchUclSharedConnections(context, targetSpaceName, mockRequiredApiResources, [
      uclBDF940ForGoodSpace,
      uclBDF950WithWildCards,
    ]);
    expect(result).to.have.length(2);
    expect(result.map((o) => o.name)).to.deep.equal(["BDF940 for good", "BDF950 with WC"]);
    // keep order with specific mapping (2)
    result = matchUclSharedConnections(context, targetSpaceName, mockRequiredApiResources, [
      uclBDF950WithWildCards,
      uclBDF940ForGoodSpace,
    ]);
    expect(result).to.have.length(2);
    expect(result.map((o) => o.name)).to.deep.equal(["BDF940 for good", "BDF950 with WC"]);
    // missing target connection (1)
    result = matchUclSharedConnections(context, targetSpaceName, mockRequiredApiResources, [uclBDF940ForGoodSpace]);
    expect(result).to.have.length(1);
    expect(result.map((o) => o.name)).to.deep.equal(["BDF940 for good"]);
    // missing target connection (2)
    result = matchUclSharedConnections(context, targetSpaceName, mockRequiredApiResources, [uclBDF950WithWildCards]);
    expect(result).to.have.length(2);
    expect(result.map((o) => o.name)).to.deep.equal([, "BDF950 with WC"]);
  });

  it("Repository Package With 1 Ingestion Space - should fail if target space cannot consume DP", async () => {
    noConsumingTargetSpace = true; // set the flag to test the case where the target space cannot consume the DP
    await RepoTestUtility.enforceFeatureFlags({ DWCO_BDC_REPOSITORY_DP_INTENT_CUSTOMER: true }, context);
    const catalogApiCatalogIds = {
      rootObjectIds: ["1231111111111111111", "2231111111111111111"],
    };
    let postCatalogV1DependencyCallsCount = 0;
    postCatalogV1DependencyStub = RepoTestUtility.addStub(
      RepositoryObjectClient,
      "callPostCatalogV1DependencyQuery"
    ).callsFake(async (context, queryBody) => {
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences.length).to.equal(1);
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences[0].nativeUniqueName).to.equal(
        "sap.s4:dataProduct:SalesOrder"
      );
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences[0].systemId).to.equal(
        `testSystemId${++postCatalogV1DependencyCallsCount}`
      );
      return catalogApiCatalogIds;
    });
    const mockParams = {
      createSpaceContentResult: {},
      importOptions: "test-options",
    } as any;
    const mockRequiredApiResources = {
      BDF940: {
        applicationType: "S4",
        connectionType: "HDLFS",
        formationType: "BDC",
        apiResources: ["sap.s4:dataProduct:SalesOrder"],
        systemId: "testSystemId1",
      },
    };
    const mockJobStatus = { status: "IN_PROGRESS", executionDetails: { global: {}, steps: [] } } as any;

    const result = await processAcnImportChunk(
      context,
      "targetSpaceName4", // needs to be changed else the UT fails due to space name cache
      mockParams,
      mockRequiredApiResources,
      mockJobStatus
    );
    expect(result).to.be.undefined;
    expect(mockJobStatus.executionDetails.global?.detail?.msg).to.equal(
      "undefined: [installDataProductsForSpace] Space targetSpaceName4 is not found or is not consuming the shared UCL connection ucl1"
    );
  });

  it("Repository Package With 1 Ingestion Space - should fail if target space is not deployed", async () => {
    notDeployedTargetSpace = true; // set the flag to test the case where the target space is not deployed
    await RepoTestUtility.enforceFeatureFlags({ DWCO_BDC_REPOSITORY_DP_INTENT_CUSTOMER: true }, context);
    const catalogApiCatalogIds = {
      rootObjectIds: ["1231111111111111111", "2231111111111111111"],
    };
    let postCatalogV1DependencyCallsCount = 0;
    postCatalogV1DependencyStub = RepoTestUtility.addStub(
      RepositoryObjectClient,
      "callPostCatalogV1DependencyQuery"
    ).callsFake(async (context, queryBody) => {
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences.length).to.equal(1);
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences[0].nativeUniqueName).to.equal(
        "sap.s4:dataProduct:SalesOrder"
      );
      expect(queryBody.rootObjectSelector.objectSelector.objectReferences[0].systemId).to.equal(
        `testSystemId${++postCatalogV1DependencyCallsCount}`
      );
      return catalogApiCatalogIds;
    });
    const mockParams = {
      createSpaceContentResult: {},
      importOptions: "test-options",
    } as any;
    const mockRequiredApiResources = {
      BDF940: {
        applicationType: "S4",
        connectionType: "HDLFS",
        formationType: "BDC",
        apiResources: ["sap.s4:dataProduct:SalesOrder"],
        systemId: "testSystemId1",
      },
    };
    const mockJobStatus = { status: "IN_PROGRESS", executionDetails: { global: {}, steps: [] } } as any;

    const result = await processAcnImportChunk(
      context,
      "targetSpaceName4", // needs to be changed else the UT fails due to space name cache
      mockParams,
      mockRequiredApiResources,
      mockJobStatus
    );
    expect(result).to.be.undefined;
    expect(mockJobStatus.executionDetails.global?.detail?.msg).to.equal(
      "undefined: [installDataProductsForSpace] Space targetSpaceName4 is not deployed"
    );
  });
});

function buildTestUclConnection(sourceMappings: any, systemTenantId: string, name?: string): IRepositoryObject {
  const localUCL = JSON.parse(JSON.stringify(uclSharedConnection));
  localUCL.name = name ?? localUCL.name;
  localUCL.properties["#sourceSystemMapping"] = JSON.stringify(sourceMappings);
  localUCL.properties.uclSystemTenantId = systemTenantId;
  return localUCL as IRepositoryObject;
}

describe("parsePotentialRepositoryPackageChunk for data product intent", function () {
  this.timeout(TimeUnit.MINUTES.toMillis(5));
  const tenantId = RepoTestUtility.randomTenantId("acnDeleteSpace").toLowerCase();
  let context;
  let sandbox: sinon.SinonSandbox;

  before(async () => {
    await RepoTestUtility.recordActiveConnections();
    await RepoTestUtility.prepareTenant(tenantId);
    sandbox = sinon.createSandbox();
  });

  beforeEach(async () => {
    context = RepoTestUtility.getTenantRequestContext(tenantId);
  });

  afterEach(async () => {
    await context.finish();
    RepoTestUtility.restoreStubs();
    sinon.restore();
    sandbox.restore();
  });

  it("should parse potential repository package chunk for data product intent", async () => {
    await RepoTestUtility.enforceFeatureFlags(
      { DWCO_BDC_REPOSITORY_DP_INTENT_CUSTOMER: true, DWCO_BDC_REPOSITORY_DP_INTENT: true },
      context
    );
    const contentMocked = {
      IMPORT_1750232227634: {
        objects: {
          ER2: {
            repositoryPackages: {
              ER2: {
                requiredApiResources: {
                  S4_ING: {
                    applicationType: "S4",
                    connectionType: "HDLFS",
                    formationType: "BDC",
                    apiResources: ["sap.s4:dataProduct:SalesOrder"],
                  },
                  SFSF_ING: {
                    applicationType: "SFSF",
                    connectionType: "HDLFS",
                    formationType: "BDC",
                    apiResources: ["sap.sfsf:dataProduct:Invoices"],
                  },
                },
              },
            },
          },
        },
      },
    };
    sinon.stub(UniqueObjectIdentifier, "parseObjectPathIdentifier").returns({
      kind: "repositoryPackage",
      spaceName: "",
      objectQName: "",
    } as IUniqueIdentifierByPath);

    const mockParams = {
      sessionId: "8257a76e-a084-11e9-a2a3-2a2ae2dbcce4",
      chunkNumber: 1,
      resourceId: "ER2 resource id",
      totalChunks: 1,
      importOptions: "DO_NOT_UPDATE_EXISTING",
      selectedIndex: 0,
      content: JSON.stringify(contentMocked),
    } as any;

    const result = await parsePotentialDataProductChunk(context, mockParams);
    expect(result).to.deep.equal({
      needToAsyncInstallDataProducts: false,
      requiredApiResources: {
        S4_ING: {
          applicationType: "S4",
          connectionType: "HDLFS",
          formationType: "BDC",
          apiResources: ["sap.s4:dataProduct:SalesOrder"],
        },
        SFSF_ING: {
          applicationType: "SFSF",
          connectionType: "HDLFS",
          formationType: "BDC",
          apiResources: ["sap.sfsf:dataProduct:Invoices"],
        },
      },
    });
  });
  it("should filter out apiResources by dataProductInstallationStatus in objectDataMap", async () => {
    await RepoTestUtility.enforceFeatureFlags(
      { DWCO_BDC_REPOSITORY_DP_INTENT_CUSTOMER: true, DWCO_BDC_REPOSITORY_DP_INTENT: true },
      context
    );
    const contentMocked = {
      IMPORT_1750232227634: {
        objects: {
          ER2: {
            definitions: {
              ER2: {
                kind: "entity",
                elements: {
                  ER21: {
                    key: true,
                    type: "cds.Integer",
                    notNull: true,
                  },
                },
                _meta: {
                  dependencies: {
                    folderAssignment: null,
                  },
                },
              },
              IngestionSpaceOne: {
                kind: "context",
                "@EndUserText.label": "IngestionSpaceOne (SAP-Managed)",
                "@DataWarehouse.space.schema": "IngestionSpaceOne",
                "@DataWarehouse.space.name": "IngestionSpaceOne",
              },
              IngestionSpaceTwo: {
                kind: "context",
                "@EndUserText.label": "IngestionSpaceTwo (SAP-Managed)",
                "@DataWarehouse.space.schema": "IngestionSpaceTwo",
                "@DataWarehouse.space.name": "IngestionSpaceTwo",
              },
            },
            sourceSystems: {
              IngestionSpaceOne: {
                "@DataWarehouse.listOfApiOrdIds": ["sap.s4:apiResource:Product:v1", "DataProduct2"],
                "@DataWarehouse.systemTenantId": "systemTenantIdOne",
              },
              IngestionSpaceTwo: {
                "@DataWarehouse.listOfApiOrdIds": ["DataProduct3", "DataProduct4"],
                "@DataWarehouse.systemTenantId": "systemTenantIdTwo",
              },
            },
          },
        },
      },
    };
    const mockParams = {
      sessionId: "8257a76e-a084-11e9-a2a3-2a2ae2dbcce4",
      chunkNumber: 1,
      resourceId: "ER2 resource id",
      totalChunks: 1,
      importOptions: "DO_NOT_UPDATE_EXISTING",
      selectedIndex: 0,
      content: JSON.stringify(contentMocked),
      objectDataMap: {
        "ER2 resource id": {
          spaceMapping: { IngestionSpaceOne: "systemTenantIdOne", BDF950: "ING_2" },
          dataProductInstallationStatus: {
            IngestionSpaceOne: {
              apiResources: ["sap.s4:apiResource:Product:v1", "DataProduct2"],
              systemId: "systemTenantIdOne",
            },
            IngestionSpaceTwo: {
              apiResources: ["DataProduct3", "DataProduct4"],
              systemId: "systemTenantIdTwo",
            },
          },
        },
      },
    } as any;

    const result = await parsePotentialDataProductChunk(context, mockParams);
    expect(result).to.deep.equal({
      needToAsyncInstallDataProducts: true,
      requiredApiResources: {},
    });
  });
  it("should filter out apiResources by dataProductInstallationStatus in objectDataMap2", async () => {
    await RepoTestUtility.enforceFeatureFlags(
      { DWCO_BDC_REPOSITORY_DP_INTENT_CUSTOMER: true, DWCO_BDC_REPOSITORY_DP_INTENT: true },
      context
    );
    const contentMocked = {
      IMPORT_1750232227634: {
        objects: {
          ER2: {
            definitions: {
              ER2: {
                kind: "entity",
                elements: {
                  ER21: {
                    key: true,
                    type: "cds.Integer",
                    notNull: true,
                  },
                },
                _meta: {
                  dependencies: {
                    folderAssignment: null,
                  },
                },
              },
              IngestionSpaceOne: {
                kind: "context",
                "@EndUserText.label": "IngestionSpaceOne (SAP-Managed)",
                "@DataWarehouse.space.schema": "IngestionSpaceOne",
                "@DataWarehouse.space.name": "IngestionSpaceOne",
              },
              IngestionSpaceTwo: {
                kind: "context",
                "@EndUserText.label": "IngestionSpaceTwo (SAP-Managed)",
                "@DataWarehouse.space.schema": "IngestionSpaceTwo",
                "@DataWarehouse.space.name": "IngestionSpaceTwo",
              },
            },
            sourceSystems: {
              IngestionSpaceOne: {
                "@DataWarehouse.listOfApiOrdIds": ["sap.s4:apiResource:Product:v1"],
                "@DataWarehouse.systemTenantId": "systemTenantIdOne",
              },
              IngestionSpaceTwo: {
                "@DataWarehouse.listOfApiOrdIds": ["DataProduct3", "DataProduct4"],
                "@DataWarehouse.systemTenantId": "systemTenantIdTwo",
              },
            },
          },
        },
      },
    };
    const mockParams = {
      sessionId: "8257a76e-a084-11e9-a2a3-2a2ae2dbcce4",
      chunkNumber: 1,
      resourceId: "ER2 resource id",
      totalChunks: 1,
      importOptions: "DO_NOT_UPDATE_EXISTING",
      selectedIndex: 0,
      content: JSON.stringify(contentMocked),
      objectDataMap: {
        "ER2 resource id": {
          spaceMapping: { IngestionSpaceOne: "systemTenantIdOne", BDF950: "ING_2" },
          dataProductInstallationStatus: {
            IngestionSpaceOne: {
              apiResources: ["sap.s4:apiResource:Product:v1", "DataProduct2"],
              systemId: "systemTenantIdOne",
            },
            IngestionSpaceTwo: {
              apiResources: ["DataProduct3", "DataProduct4"],
              systemId: "systemTenantIdTwo",
            },
          },
        },
      },
    } as any;

    const result = await parsePotentialDataProductChunk(context, mockParams);
    expect(result).to.deep.equal({
      needToAsyncInstallDataProducts: true,
      requiredApiResources: {},
    });
  });
  it("should filter some apiResources by dataProductInstallationStatus in objectDataMap1", async () => {
    await RepoTestUtility.enforceFeatureFlags(
      { DWCO_BDC_REPOSITORY_DP_INTENT_CUSTOMER: true, DWCO_BDC_REPOSITORY_DP_INTENT: true },
      context
    );
    const contentMocked = {
      IMPORT_1750232227634: {
        objects: {
          ER2: {
            definitions: {
              ER2: {
                kind: "entity",
                elements: {
                  ER21: {
                    key: true,
                    type: "cds.Integer",
                    notNull: true,
                  },
                },
                _meta: {
                  dependencies: {
                    folderAssignment: null,
                  },
                },
              },
              IngestionSpaceOne: {
                kind: "context",
                "@EndUserText.label": "IngestionSpaceOne (SAP-Managed)",
                "@DataWarehouse.space.schema": "IngestionSpaceOne",
                "@DataWarehouse.space.name": "IngestionSpaceOne",
              },
            },
            sourceSystems: {
              IngestionSpaceOne: {
                "@DataWarehouse.listOfApiOrdIds": ["sap.s4:apiResource:Product:v1", "DataProduct5"],
                "@DataWarehouse.systemTenantId": "systemTenantIdOne",
              },
            },
          },
        },
      },
    };
    const mockParams = {
      sessionId: "8257a76e-a084-11e9-a2a3-2a2ae2dbcce4",
      chunkNumber: 1,
      resourceId: "ER2 resource id",
      totalChunks: 1,
      importOptions: "DO_NOT_UPDATE_EXISTING",
      selectedIndex: 0,
      content: JSON.stringify(contentMocked),
      objectDataMap: {
        "ER2 resource id": {
          spaceMapping: { IngestionSpaceOne: "systemTenantIdOne", BDF950: "ING_2" },
          dataProductInstallationStatus: {
            IngestionSpaceOne: {
              apiResources: ["sap.s4:apiResource:Product:v1", "DataProduct2"],
              systemId: "systemTenantIdOne",
            },
            IngestionSpaceTwo: {
              apiResources: ["DataProduct3", "DataProduct4"],
              systemId: "systemTenantIdTwo",
            },
          },
        },
      },
    } as any;

    const result = await parsePotentialDataProductChunk(context, mockParams);
    expect(result).to.deep.equal({
      needToAsyncInstallDataProducts: true,
      requiredApiResources: {
        IngestionSpaceOne: {
          apiResources: ["DataProduct5"],
          systemId: "systemTenantIdOne",
        },
      },
    });
    // Verify that the sourceSystems property is removed after parsing
    const content = JSON.parse(mockParams.content) as IAcnChunkContent;
    const contentExpected = JSON.parse(JSON.stringify(contentMocked));
    delete contentExpected.IMPORT_1750232227634.objects.ER2.sourceSystems;
    expect(content).to.deep.equal(contentExpected);
  });
  it("should filter some apiResources by dataProductInstallationStatus in objectDataMap2", async () => {
    await RepoTestUtility.enforceFeatureFlags(
      { DWCO_BDC_REPOSITORY_DP_INTENT_CUSTOMER: true, DWCO_BDC_REPOSITORY_DP_INTENT: true },
      context
    );
    const contentMocked = {
      IMPORT_1750232227634: {
        objects: {
          ER2: {
            definitions: {
              ER2: {
                kind: "entity",
                elements: {
                  ER21: {
                    key: true,
                    type: "cds.Integer",
                    notNull: true,
                  },
                },
                _meta: {
                  dependencies: {
                    folderAssignment: null,
                  },
                },
              },
              IngestionSpaceOne: {
                kind: "context",
                "@EndUserText.label": "IngestionSpaceOne (SAP-Managed)",
                "@DataWarehouse.space.schema": "IngestionSpaceOne",
                "@DataWarehouse.space.name": "IngestionSpaceOne",
              },
              IngestionSpaceTwo: {
                kind: "context",
                "@EndUserText.label": "IngestionSpaceTwo (SAP-Managed)",
                "@DataWarehouse.space.schema": "IngestionSpaceTwo",
                "@DataWarehouse.space.name": "IngestionSpaceTwo",
              },
            },
            sourceSystems: {
              IngestionSpaceOne: {
                "@DataWarehouse.listOfApiOrdIds": ["sap.s4:apiResource:Product:v1", "DataProduct5"],
                "@DataWarehouse.systemTenantId": "systemTenantIdOne",
              },
              IngestionSpaceTwo: {
                "@DataWarehouse.listOfApiOrdIds": ["DataProduct3", "DataProduct4", "DataProduct6"],
                "@DataWarehouse.systemTenantId": "systemTenantIdTwo",
              },
            },
          },
        },
      },
    };
    const mockParams = {
      sessionId: "8257a76e-a084-11e9-a2a3-2a2ae2dbcce4",
      chunkNumber: 1,
      resourceId: "ER2 resource id",
      totalChunks: 1,
      importOptions: "DO_NOT_UPDATE_EXISTING",
      selectedIndex: 0,
      content: JSON.stringify(contentMocked),
      objectDataMap: {
        "ER2 resource id": {
          spaceMapping: { IngestionSpaceOne: "systemTenantIdOne", BDF950: "ING_2" },
          dataProductInstallationStatus: {
            IngestionSpaceOne: {
              apiResources: ["sap.s4:apiResource:Product:v1", "DataProduct2"],
              systemId: "systemTenantIdOne",
            },
            IngestionSpaceTwo: {
              apiResources: ["DataProduct3", "DataProduct4"],
              systemId: "systemTenantIdTwo",
            },
          },
        },
      },
    } as any;

    const result = await parsePotentialDataProductChunk(context, mockParams);
    expect(result).to.deep.equal({
      needToAsyncInstallDataProducts: true,
      requiredApiResources: {
        IngestionSpaceOne: {
          apiResources: ["DataProduct5"],
          systemId: "systemTenantIdOne",
        },
        IngestionSpaceTwo: {
          apiResources: ["DataProduct6"],
          systemId: "systemTenantIdTwo",
        },
      },
    });
    // Verify that the sourceSystems property is removed after parsing
    const content = JSON.parse(mockParams.content) as IAcnChunkContent;
    const contentExpected = JSON.parse(JSON.stringify(contentMocked));
    delete contentExpected.IMPORT_1750232227634.objects.ER2.sourceSystems;
    expect(content).to.deep.equal(contentExpected);
  });
});
