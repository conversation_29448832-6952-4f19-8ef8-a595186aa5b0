/** @format */
import { IRequestContext } from "@sap/deepsea-types";
import chai from "chai";
import chaiAsPromised from "chai-as-promised";
import sinon, { SinonStub } from "sinon";
import sinonChai from "sinon-chai";
import { IntegratedSystemCommunicationHandlerBase } from "../../../../../bdc/ucl/integratedSystems/IntegratedSystemCommunicationHandlerBase";
import { DspConfig } from "../../../../../bdc/ucl/integratedSystems/systemsConfig/dspConfig";
import { store } from "../../../../../configurations/main";
import { testTenantUuid } from "../../../../../lib/node";
import { RequestContext } from "../../../../../repository/security/requestContext";
import { IUclRequestBody, IUclRequestTenantInfo } from "../../../../../ucl/types";

chai.use(chaiAsPromised);
chai.use(sinonChai);
const expect = chai.expect;

describe("service/bdc/ucl/integratedSystems/systemsConfig/dspConfig.ts", () => {
  let sandbox: sinon.SinonSandbox;
  let context: IRequestContext;
  let dspConfigHandlerInstance: IntegratedSystemCommunicationHandlerBase;
  const MOCK_UCL_REQUEST_TENANT_INFO = {
    uclAssignmentId: "mock_ucl_assignment_id",
    configuration: {
      setUpSharedCatalog: true,
      credentials: {
        inboundCommunication: {
          oAuth: {},
        },
      },
    },
  } as IUclRequestTenantInfo;
  const MOCK_UCL_REQUEST_BODY = {
    assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
  } as IUclRequestBody;

  let ffStub: SinonStub;
  before(() => {
    sandbox = sinon.createSandbox();
  });

  beforeEach(() => {
    context = RequestContext.createFromTenantId(testTenantUuid);
    dspConfigHandlerInstance = new DspConfig();
    ffStub = sandbox.stub(context, "isFeatureFlagActive");
  });

  afterEach(() => {
    void context.finish();
    sandbox.restore();
  });

  it("getLogger", () => {
    const result = dspConfigHandlerInstance.getLogger();
    expect(result).not.to.be.undefined;
  });

  describe("matchesApplicationNamespace", () => {
    it("should return true if applicationNamespace is 'sap.datasphere'", async () => {
      const result = await dspConfigHandlerInstance.matchesApplicationNamespace(context, "sap.datasphere");
      expect(result).to.be.equal(true);
    });

    it("should return false if applicationNamespace is not 'sap.datasphere'", async () => {
      const result = await dspConfigHandlerInstance.matchesApplicationNamespace(context, "mock_namespace");
      expect(result).to.be.equal(false);
    });
  });

  it("handleFirstSynchronizationRequest should return null", async () => {
    const result = await dspConfigHandlerInstance.handleFirstSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.null;
  });

  it("handleSecondSynchronizationRequest should return config object", async () => {
    const insertDSPOauthClientInCredStoreStub = sandbox
      .stub(dspConfigHandlerInstance as DspConfig, "insertDSPOauthClientInCredStore")
      .resolves();

    const result = await dspConfigHandlerInstance.handleSecondSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);

    expect(result).to.be.deep.equal({
      setUpSharedCatalog: true,
      credentials: {
        inboundCommunication: {
          oAuth: {},
        },
      },
    });

    expect(insertDSPOauthClientInCredStoreStub).to.have.been.calledWith(context, {});
  });

  it("handleDeleteSynchronizationRequest should return empty object", async () => {
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    const deleteDSPOauthClientInCredStoreStub = sandbox
      .stub(dspConfigHandlerInstance as DspConfig, "deleteDSPOauthClientInCredStore")
      .resolves();
    const result = await dspConfigHandlerInstance.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.null;
    expect(deleteDSPOauthClientInCredStoreStub).to.have.been.calledWith(context);
  });
});

describe("Unassign DSP from formation", () => {
  let sandbox: sinon.SinonSandbox;
  let context: IRequestContext;
  let dspConfigHandlerInstance: IntegratedSystemCommunicationHandlerBase;
  const MOCK_UCL_REQUEST_TENANT_INFO = {
    uclAssignmentId: "mock_ucl_assignment_id",
    configuration: {
      setUpSharedCatalog: true,
      credentials: {
        inboundCommunication: {
          oAuth: {},
        },
      },
    },
  } as IUclRequestTenantInfo;
  const MOCK_UCL_REQUEST_BODY = {
    assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
  } as IUclRequestBody;

  let ffStub: SinonStub;
  let storeStub: sinon.SinonStub;
  before(() => {
    sandbox = sinon.createSandbox();
  });

  beforeEach(() => {
    context = RequestContext.createFromTenantId(testTenantUuid);
    dspConfigHandlerInstance = new DspConfig();
    ffStub = sandbox.stub(context, "isFeatureFlagActive");
  });

  afterEach(() => {
    void context.finish();
    sandbox.restore();
  });

  it("handleDeleteSynchronizationRequest is allowed when FF is active and specific configuration value is ON", async () => {
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    const deleteDSPOauthClientInCredStoreStub = sandbox
      .stub(dspConfigHandlerInstance as DspConfig, "deleteDSPOauthClientInCredStore")
      .resolves();
    const result = await dspConfigHandlerInstance.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.null;
    expect(deleteDSPOauthClientInCredStoreStub).to.have.been.calledWith(context);

    chai.expect(storeStub.calledOnce).to.equal(true);
    chai.expect(storeStub.args[0][1]).to.be.equal("DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT");
    chai.expect(storeStub.args[0][2]).to.be.deep.eq({ preferTenantScope: true });
  });

  it("handleDeleteSynchronizationRequest is not allowed when specific configuration value is OFF", async () => {
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    const deleteDSPOauthClientInCredStoreStub = sandbox
      .stub(dspConfigHandlerInstance as DspConfig, "deleteDSPOauthClientInCredStore")
      .resolves();
    try {
      await dspConfigHandlerInstance.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    } catch (error) {
      chai.expect(error).to.be.an("Error");
      expect(error.message).to.equal("Deletion is not allowed. Contact SAP Support to request changes to formation");
    }

    chai.expect(storeStub.calledOnce).to.equal(true);
    chai.expect(storeStub.args[0][1]).to.be.equal("DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT");
    chai.expect(storeStub.args[0][2]).to.be.deep.eq({ preferTenantScope: true });
    sinon.assert.notCalled(deleteDSPOauthClientInCredStoreStub);
  });

  it("handleDeleteSynchronizationRequest is not allowed when fails to retrieve specific configuration value from configuration service", async () => {
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").throws(new Error("fail to get configuration"));
    const deleteDSPOauthClientInCredStoreStub = sandbox
      .stub(dspConfigHandlerInstance as DspConfig, "deleteDSPOauthClientInCredStore")
      .resolves();
    try {
      await dspConfigHandlerInstance.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    } catch (error) {
      chai.expect(error).to.be.an("Error");
      expect(error.message).to.equal("Deletion is not allowed. Contact SAP Support to request changes to formation");
    }

    chai.expect(storeStub.calledOnce).to.equal(true);
    chai.expect(storeStub.args[0][1]).to.be.equal("DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT");
    chai.expect(storeStub.args[0][2]).to.be.deep.eq({ preferTenantScope: true });
    sinon.assert.notCalled(deleteDSPOauthClientInCredStoreStub);
  });

  it("handleDeleteSynchronizationRequest is allowed to do unassignment properly", async () => {
    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): ON
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): ON
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "ON"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(true);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    let deleteDSPOauthClientInCredStoreStub = sandbox
      .stub(dspConfigHandlerInstance as DspConfig, "deleteDSPOauthClientInCredStore")
      .resolves();
    let result = await dspConfigHandlerInstance.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.null;
    // allow to do unassignment properly
    expect(deleteDSPOauthClientInCredStoreStub).to.have.been.calledWith(context);
    chai.expect(storeStub.calledOnce).to.equal(false);

    deleteDSPOauthClientInCredStoreStub.restore();
    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): ON
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): ON
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "OFF"}
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    deleteDSPOauthClientInCredStoreStub = sandbox
      .stub(dspConfigHandlerInstance as DspConfig, "deleteDSPOauthClientInCredStore")
      .resolves();
    result = await dspConfigHandlerInstance.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.null;
    // allow to do unassignment properly
    expect(deleteDSPOauthClientInCredStoreStub).to.have.been.calledWith(context);
    chai.expect(storeStub.calledOnce).to.equal(false);

    deleteDSPOauthClientInCredStoreStub.restore();
    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): ON
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): OFF
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "ON"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(true);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    deleteDSPOauthClientInCredStoreStub = sandbox
      .stub(dspConfigHandlerInstance as DspConfig, "deleteDSPOauthClientInCredStore")
      .resolves();
    result = await dspConfigHandlerInstance.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.null;
    // allow to do unassignment properly
    expect(deleteDSPOauthClientInCredStoreStub).to.have.been.calledWith(context);
    chai.expect(storeStub.calledOnce).to.equal(false);

    deleteDSPOauthClientInCredStoreStub.restore();
    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): ON
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): OFF
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "OFF"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(true);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    deleteDSPOauthClientInCredStoreStub = sandbox
      .stub(dspConfigHandlerInstance as DspConfig, "deleteDSPOauthClientInCredStore")
      .resolves();
    result = await dspConfigHandlerInstance.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.null;
    // block to do unassignment
    expect(deleteDSPOauthClientInCredStoreStub).to.have.been.calledWith(context);
    chai.expect(storeStub.calledOnce).to.equal(false);

    deleteDSPOauthClientInCredStoreStub.restore();
    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): OFF
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): ON
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "ON"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(false);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    deleteDSPOauthClientInCredStoreStub = sandbox
      .stub(dspConfigHandlerInstance as DspConfig, "deleteDSPOauthClientInCredStore")
      .resolves();
    result = await dspConfigHandlerInstance.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.null;
    // allow to do unassignment properly
    expect(deleteDSPOauthClientInCredStoreStub).to.have.been.calledWith(context);
    chai.expect(storeStub.calledOnce).to.equal(true);
    chai.expect(storeStub.args[0][1]).to.be.equal("DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT");
    chai.expect(storeStub.args[0][2]).to.be.deep.eq({ preferTenantScope: true });

    deleteDSPOauthClientInCredStoreStub.restore();
    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): OFF
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): ON
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "OFF"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(false);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    deleteDSPOauthClientInCredStoreStub = sandbox
      .stub(dspConfigHandlerInstance as DspConfig, "deleteDSPOauthClientInCredStore")
      .resolves();
    try {
      await dspConfigHandlerInstance.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    } catch (error) {
      chai.expect(error).to.be.an("Error");
      expect(error.message).to.equal("Deletion is not allowed. Contact SAP Support to request changes to formation");
    }
    // block to do unassignment properly
    sinon.assert.notCalled(deleteDSPOauthClientInCredStoreStub);
    chai.expect(storeStub.calledOnce).to.equal(true);
    chai.expect(storeStub.args[0][1]).to.be.equal("DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT");
    chai.expect(storeStub.args[0][2]).to.be.deep.eq({ preferTenantScope: true });

    deleteDSPOauthClientInCredStoreStub.restore();
    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): OFF
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): OFF
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "ON"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(false);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    deleteDSPOauthClientInCredStoreStub = sandbox
      .stub(dspConfigHandlerInstance as DspConfig, "deleteDSPOauthClientInCredStore")
      .resolves();
    result = await dspConfigHandlerInstance.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.null;
    // allow to do unassignment properly
    expect(deleteDSPOauthClientInCredStoreStub).to.have.been.calledWith(context);
    sinon.assert.notCalled(storeStub);

    deleteDSPOauthClientInCredStoreStub.restore();
    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): OFF
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): OFF
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "OFF"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(false);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    deleteDSPOauthClientInCredStoreStub = sandbox
      .stub(dspConfigHandlerInstance as DspConfig, "deleteDSPOauthClientInCredStore")
      .resolves();
    result = await dspConfigHandlerInstance.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.null;
    // allow to do unassignment properly
    expect(deleteDSPOauthClientInCredStoreStub).to.have.been.calledWith(context);
    sinon.assert.notCalled(storeStub);

    deleteDSPOauthClientInCredStoreStub.restore();
    storeStub.restore();
  });
});
