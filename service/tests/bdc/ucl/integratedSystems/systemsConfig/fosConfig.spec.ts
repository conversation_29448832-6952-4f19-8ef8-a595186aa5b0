/** @format */
import { SecureS<PERSON><PERSON>ey, UclCredentialsManager } from "@sap/dwc-credentials";
import { IAuthType } from "@sap/dwc-permissions";
import * as chai from "chai";
import * as chaiAsPromised from "chai-as-promised";
import sinon, { SinonStub } from "sinon";
import sinonChai from "sinon-chai";
import { FosConfig } from "../../../../../bdc/ucl/integratedSystems/systemsConfig/fosConfig";
import { store } from "../../../../../configurations/main";
import { testTenantUuid } from "../../../../../lib/node";
import { AuthType } from "../../../../../repository/security/common/common";
import { Activities, RequestContext } from "../../../../../repository/security/requestContext";
import { IUclRequestBody, IX509Credentials } from "../../../../../ucl/types";

chai.use(chaiAsPromised.default);
chai.use(sinonChai);
const expect = chai.expect;

describe("service/bdc/ucl/integratedSystems/systemsConfig/fosConfig.ts", () => {
  let sandbox: sinon.SinonSandbox;
  let context: RequestContext;
  let fosConfig: FosConfig;
  let ffStub: SinonStub;
  let storeStub: sinon.SinonStub;

  before(() => {
    sandbox = sinon.createSandbox();
  });

  beforeEach(() => {
    const authType: IAuthType = {
      auth: Activities.read,
      name: AuthType.SYSTEMINFO,
    };
    context = RequestContext.createFromTenantId(testTenantUuid, { authtypes: [authType] });

    fosConfig = new FosConfig();
    ffStub = sandbox.stub(context, "isFeatureFlagActive");
  });

  afterEach(() => {
    void context.finish();
    sandbox.restore();
  });

  it("getLogger", () => {
    const result = fosConfig.getLogger();
    expect(result).not.to.be.undefined;
  });

  describe("matchesApplicationNamespace", () => {
    it("should return true if applicationNamespace is 'sap.s4pce'", async () => {
      const result = await fosConfig.matchesApplicationNamespace(context, "sap.s4pce");
      expect(result).to.be.equal(true);
    });

    it("should return true if applicationNamespace is 'sap.sf' when DWCO_UCL_TENANT_MAPPING_BDC_DSP_HCM is on", async () => {
      ffStub.withArgs("DWCO_UCL_TENANT_MAPPING_BDC_DSP_HCM").returns(true);
      const result = await fosConfig.matchesApplicationNamespace(context, "sap.sf");
      expect(result).to.be.equal(true);
    });

    it("should return false if applicationNamespace is 'sap.sf' when DWCO_UCL_TENANT_MAPPING_BDC_DSP_HCM is off", async () => {
      ffStub.withArgs("DWCO_UCL_TENANT_MAPPING_BDC_DSP_HCM").returns(false);
      const result = await fosConfig.matchesApplicationNamespace(context, "sap.sf");
      expect(result).to.be.equal(false);
    });

    it("should return true if applicationNamespace is 'sap.s4' when DWCO_UCL_TENANT_MAPPING_BDC_DSP_S4_PUBLIC_CLOUD is on", async () => {
      ffStub.withArgs("DWCO_UCL_TENANT_MAPPING_BDC_DSP_S4_PUBLIC_CLOUD").returns(true);
      const result = await fosConfig.matchesApplicationNamespace(context, "sap.s4");
      expect(result).to.be.equal(true);
    });

    it("should return false if applicationNamespace is 'sap.s4' when DWCO_UCL_TENANT_MAPPING_BDC_DSP_S4_PUBLIC_CLOUD is off", async () => {
      ffStub.withArgs("DWCO_UCL_TENANT_MAPPING_BDC_DSP_S4_PUBLIC_CLOUD").returns(false);
      const result = await fosConfig.matchesApplicationNamespace(context, "sap.s4");
      expect(result).to.be.equal(false);
    });

    it("should return false if applicationNamespace is 'mock_namespace'", async () => {
      const result = await fosConfig.matchesApplicationNamespace(context, "mock_namespace");
      expect(result).to.be.equal(false);
    });
  });

  it("handleFirstSynchronizationRequest", async () => {
    const MOCK_CERTIFICATE_CHAIN = "mock_certificate_chain";
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    };
    const MOCK_UCL_REQUEST_BODY = {
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;
    const getCredentialsStub = sandbox.stub(UclCredentialsManager.prototype, "getCredentials").resolves({
      clientCertificateChain: MOCK_CERTIFICATE_CHAIN,
    } as IX509Credentials);

    const result = await fosConfig.handleFirstSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);

    expect(getCredentialsStub).to.be.calledOnceWith(SecureStoreKey.UclBdcTenantCredentials);
    const correlationIds = ["sap.bdc.foundationService:dataproduct:INSTALLATION"];
    expect(result).to.be.deep.equal({
      credentials: {
        inboundCommunication: {
          clientCertificateAuthentication: {
            correlationIds,
            certificate: MOCK_CERTIFICATE_CHAIN,
          },
        },
      },
    });
  });

  it("handleSecondSynchronizationRequest", async () => {
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    };
    const MOCK_UCL_REQUEST_BODY = {
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;
    const result = await fosConfig.handleSecondSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.empty;
  });

  it("handleDeleteSynchronizationRequest", async () => {
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    };
    const MOCK_UCL_REQUEST_BODY = {
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;
    const result = await fosConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.empty;
  });

  it("handleDeleteSynchronizationRequest is allowed when FF is active and specific configuration value is ON", async () => {
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    };
    const MOCK_UCL_REQUEST_BODY = {
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;
    const result = await fosConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.empty;
  });

  it("handleDeleteSynchronizationRequest is not allowed when specific configuration value is OFF", async () => {
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    };
    const MOCK_UCL_REQUEST_BODY = {
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;
    try {
      await fosConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    } catch (error) {
      chai.expect(error).to.be.an("Error");
      expect(error.message).to.equal("Deletion is not allowed. Contact SAP Support to request changes to formation");
    }
  });

  it("handleDeleteSynchronizationRequest is not allowed when fails to retrieve specific configuration value from configuration service", async () => {
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    sandbox.stub(store, "getEffectiveValue").throws(new Error("fail to get configuration"));
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    };
    const MOCK_UCL_REQUEST_BODY = {
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;
    try {
      await fosConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    } catch (error) {
      chai.expect(error).to.be.an("Error");
      expect(error.message).to.equal("Deletion is not allowed. Contact SAP Support to request changes to formation");
    }
  });

  it("handleDeleteSynchronizationRequest is allowed to do unassignment properly", async () => {
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    };
    const MOCK_UCL_REQUEST_BODY = {
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;
    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): ON
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): ON
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "ON"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(true);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    let result = await fosConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({});
    chai.expect(storeStub.calledOnce).to.equal(false);

    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): ON
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): ON
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "OFF"}
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    result = await fosConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({});
    // allow to do unassignment properly
    chai.expect(storeStub.calledOnce).to.equal(false);

    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): ON
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): OFF
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "ON"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(true);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    result = await fosConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({});
    // allow to do unassignment properly
    chai.expect(storeStub.calledOnce).to.equal(false);

    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): ON
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): OFF
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "OFF"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(true);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    result = await fosConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({});
    // allow to do unassignment
    chai.expect(storeStub.calledOnce).to.equal(false);

    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): OFF
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): ON
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "ON"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(false);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    result = await fosConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({});
    // allow to do unassignment properly
    chai.expect(storeStub.calledOnce).to.equal(true);
    chai.expect(storeStub.args[0][1]).to.be.equal("DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT");
    chai.expect(storeStub.args[0][2]).to.be.deep.eq({ preferTenantScope: true });

    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): OFF
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): ON
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "OFF"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(false);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    try {
      await fosConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    } catch (error) {
      chai.expect(error).to.be.an("Error");
      expect(error.message).to.equal("Deletion is not allowed. Contact SAP Support to request changes to formation");
    }
    // block to do unassignment properly
    chai.expect(storeStub.calledOnce).to.equal(true);
    chai.expect(storeStub.args[0][1]).to.be.equal("DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT");
    chai.expect(storeStub.args[0][2]).to.be.deep.eq({ preferTenantScope: true });

    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): OFF
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): OFF
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "ON"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(false);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    result = await fosConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({});
    // allow to do unassignment properly
    sinon.assert.notCalled(storeStub);

    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): OFF
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): OFF
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "OFF"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(false);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    result = await fosConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({});
    // allow to do unassignment properly
    sinon.assert.notCalled(storeStub);

    storeStub.restore();
  });
});
