/** @format */
import { SecureStore<PERSON>ey, UclCredentialsManager } from "@sap/dwc-credentials";
import { IAuthType } from "@sap/dwc-permissions";
import * as chai from "chai";
import * as chaiAsPromised from "chai-as-promised";
import sinon, { SinonStub } from "sinon";
import sinonChai from "sinon-chai";
import { store } from "../../../../../configurations/main";
import { testTenantUuid } from "../../../../../lib/node";
import { AuthType } from "../../../../../repository/security/common/common";
import { Activities, RequestContext } from "../../../../../repository/security/requestContext";
import { IUclRequestBody, IX509Credentials } from "../../../../../ucl/types";
import { CdpConfig } from "../../../../../bdc/ucl/integratedSystems/systemsConfig/cdpConfig";

chai.use(chaiAsPromised.default);
chai.use(sinonChai);
const expect = chai.expect;

describe("service/bdc/ucl/integratedSystems/systemsConfig/cdpConfig.ts", () => {
  const CDP_DP_GOVERNANCE_CORRELATION_ID = "sap.bds:federation.cdp:GOVERNANCE";

  let sandbox: sinon.SinonSandbox;
  let context: RequestContext;
  let cdpConfig: CdpConfig;
  let ffStub: SinonStub;
  let storeStub: sinon.SinonStub;

  before(() => {
    sandbox = sinon.createSandbox();
  });

  beforeEach(() => {
    const authType: IAuthType = {
      auth: Activities.read,
      name: AuthType.SYSTEMINFO,
    };
    context = RequestContext.createFromTenantId(testTenantUuid, { authtypes: [authType] });

    cdpConfig = new CdpConfig();
    ffStub = sandbox.stub(context, "isFeatureFlagActive");
  });

  afterEach(() => {
    void context.finish();
    sandbox.restore();
  });

  it("getLogger", () => {
    const result = cdpConfig.getLogger();
    expect(result).not.to.be.undefined;
  });

  describe("matchesApplicationNamespace", () => {
    it("should return true if applicationNamespace is 'sap.cdp'", async () => {
      const result = await cdpConfig.matchesApplicationNamespace(context, "sap.cdp");
      expect(result).to.be.equal(true);
    });

    it("should return false if applicationNamespace is 'mock_namespace'", async () => {
      const result = await cdpConfig.matchesApplicationNamespace(context, "mock_namespace");
      expect(result).to.be.equal(false);
    });
  });

  it("handleFirstSynchronizationRequest", async () => {
    const MOCK_CERTIFICATE_CHAIN = "mock_certificate_chain";
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    };
    const MOCK_UCL_REQUEST_BODY = {
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;
    const getCredentialsStub = sandbox.stub(UclCredentialsManager.prototype, "getCredentials").resolves({
      clientCertificateChain: MOCK_CERTIFICATE_CHAIN,
    } as IX509Credentials);

    const result = await cdpConfig.handleFirstSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);

    expect(getCredentialsStub).to.be.calledOnceWith(SecureStoreKey.UclBdcTenantCredentials);
    expect(result).to.be.deep.equal({
      credentials: {
        inboundCommunication: {
          clientCertificateAuthentication: {
            correlationIds: [CDP_DP_GOVERNANCE_CORRELATION_ID],
            certificate: MOCK_CERTIFICATE_CHAIN,
          },
        },
      },
    });
  });

  describe("handleSecondSynchronizationRequest", () => {
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_DP_GOVERNANCE_API_PROTOCOL = "mock_dp_governance_api_protocol";
    const MOCK_DP_GOVERNANCE_URL = "mock_dp_governance_url";

    function constructMockUclRequestBody(): IUclRequestBody {
      const MOCK_UCL_REQUEST_TENANT_INFO = {
        uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
        configuration: {
          credentials: {
            outboundCommunication: {
              clientCertificateAuthentication: {
                correlationIds: [CDP_DP_GOVERNANCE_CORRELATION_ID],
              },
            },
          },
          additionalAttributes: {
            connectionsProperties: [
              {
                apiProtocol: MOCK_DP_GOVERNANCE_API_PROTOCOL,
                correlationIds: [CDP_DP_GOVERNANCE_CORRELATION_ID],
                url: MOCK_DP_GOVERNANCE_URL,
              },
            ],
          },
        },
      };
      const MOCK_UCL_REQUEST_BODY = {
        assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
      } as IUclRequestBody;
      return MOCK_UCL_REQUEST_BODY;
    }

    it("handleSecondSynchronizationRequest returns empty configuration", async () => {
      const mockUclRequestBody = constructMockUclRequestBody();
      const result = await cdpConfig.handleSecondSynchronizationRequest(context, mockUclRequestBody);
      expect(result).to.be.empty;
    });

    it("handleSecondSynchronizationRequest throws error if configuration is missing", async () => {
      const mockUclRequestBody = constructMockUclRequestBody();
      delete mockUclRequestBody.assignedTenant.configuration;
      try {
        await cdpConfig.handleSecondSynchronizationRequest(context, mockUclRequestBody);
      } catch (err) {
        return expect(err.message).to.equal(
          `Cannot find ${CDP_DP_GOVERNANCE_CORRELATION_ID} in configuration.credentials.outboundCommunication.clientCertificateAuthentication.correlationIds`
        );
      }
      expect(false).to.equal(true, "Expect function to throw error and not reach here");
    });

    it("handleSecondSynchronizationRequest throws error if configuration.credentials is missing", async () => {
      const mockUclRequestBody = constructMockUclRequestBody();
      delete mockUclRequestBody.assignedTenant.configuration.credentials;
      try {
        await cdpConfig.handleSecondSynchronizationRequest(context, mockUclRequestBody);
      } catch (err) {
        return expect(err.message).to.equal(
          `Cannot find ${CDP_DP_GOVERNANCE_CORRELATION_ID} in configuration.credentials.outboundCommunication.clientCertificateAuthentication.correlationIds`
        );
      }
      expect(false).to.equal(true, "Expect function to throw error and not reach here");
    });

    it("handleSecondSynchronizationRequest throws error if configuration.credentials.outboundCommunication is missing", async () => {
      const mockUclRequestBody = constructMockUclRequestBody();
      delete mockUclRequestBody.assignedTenant.configuration.credentials.outboundCommunication;
      try {
        await cdpConfig.handleSecondSynchronizationRequest(context, mockUclRequestBody);
      } catch (err) {
        return expect(err.message).to.equal(
          `Cannot find ${CDP_DP_GOVERNANCE_CORRELATION_ID} in configuration.credentials.outboundCommunication.clientCertificateAuthentication.correlationIds`
        );
      }
      expect(false).to.equal(true, "Expect function to throw error and not reach here");
    });

    it("handleSecondSynchronizationRequest throws error if configuration.credentials.outboundCommunication.clientCertificateAuthentication is missing", async () => {
      const mockUclRequestBody = constructMockUclRequestBody();
      delete mockUclRequestBody.assignedTenant.configuration.credentials.outboundCommunication
        .clientCertificateAuthentication;
      try {
        await cdpConfig.handleSecondSynchronizationRequest(context, mockUclRequestBody);
      } catch (err) {
        return expect(err.message).to.equal(
          `Cannot find ${CDP_DP_GOVERNANCE_CORRELATION_ID} in configuration.credentials.outboundCommunication.clientCertificateAuthentication.correlationIds`
        );
      }
      expect(false).to.equal(true, "Expect function to throw error and not reach here");
    });

    it("handleSecondSynchronizationRequest throws error if configuration.credentials.outboundCommunication.clientCertificateAuthentication.correlationIds is missing", async () => {
      const mockUclRequestBody = constructMockUclRequestBody();
      delete mockUclRequestBody.assignedTenant.configuration.credentials.outboundCommunication
        .clientCertificateAuthentication.correlationIds;
      try {
        await cdpConfig.handleSecondSynchronizationRequest(context, mockUclRequestBody);
      } catch (err) {
        return expect(err.message).to.equal(
          `Cannot find ${CDP_DP_GOVERNANCE_CORRELATION_ID} in configuration.credentials.outboundCommunication.clientCertificateAuthentication.correlationIds`
        );
      }
      expect(false).to.equal(true, "Expect function to throw error and not reach here");
    });

    it(
      "handleSecondSynchronizationRequest throws error if configuration.credentials.outboundCommunication.clientCertificateAuthentication.correlationIds does not contain " +
        CDP_DP_GOVERNANCE_CORRELATION_ID,
      async () => {
        const mockUclRequestBody = constructMockUclRequestBody();
        mockUclRequestBody.assignedTenant.configuration.credentials.outboundCommunication.clientCertificateAuthentication.correlationIds =
          ["mock_correlation_id"];
        try {
          await cdpConfig.handleSecondSynchronizationRequest(context, mockUclRequestBody);
        } catch (err) {
          return expect(err.message).to.equal(
            `Cannot find ${CDP_DP_GOVERNANCE_CORRELATION_ID} in configuration.credentials.outboundCommunication.clientCertificateAuthentication.correlationIds`
          );
        }
        expect(false).to.equal(true, "Expect function to throw error and not reach here");
      }
    );

    it("handleSecondSynchronizationRequest throws error if configuration.additionalAttributes is missing", async () => {
      const mockUclRequestBody = constructMockUclRequestBody();
      delete mockUclRequestBody.assignedTenant.configuration.additionalAttributes;
      try {
        await cdpConfig.handleSecondSynchronizationRequest(context, mockUclRequestBody);
      } catch (err) {
        return expect(err.message).to.equal(
          `Cannot find connections property in configuration.additionalAttributes.connectionsProperties where correlationIds contains ${CDP_DP_GOVERNANCE_CORRELATION_ID}, and apiProtocol and url exist`
        );
      }
      expect(false).to.equal(true, "Expect function to throw error and not reach here");
    });

    it("handleSecondSynchronizationRequest throws error if configuration.additionalAttributes.connectionsProperties is missing", async () => {
      const mockUclRequestBody = constructMockUclRequestBody();
      delete mockUclRequestBody.assignedTenant.configuration.additionalAttributes.connectionsProperties;
      try {
        await cdpConfig.handleSecondSynchronizationRequest(context, mockUclRequestBody);
      } catch (err) {
        return expect(err.message).to.equal(
          `Cannot find connections property in configuration.additionalAttributes.connectionsProperties where correlationIds contains ${CDP_DP_GOVERNANCE_CORRELATION_ID}, and apiProtocol and url exist`
        );
      }
      expect(false).to.equal(true, "Expect function to throw error and not reach here");
    });

    it("handleSecondSynchronizationRequest throws error if configuration.additionalAttributes.connectionsProperties does not contain a property with correlationIds defined", async () => {
      const mockUclRequestBody = constructMockUclRequestBody();
      mockUclRequestBody.assignedTenant.configuration.additionalAttributes.connectionsProperties.forEach(
        (property) => delete property.correlationIds
      );
      try {
        await cdpConfig.handleSecondSynchronizationRequest(context, mockUclRequestBody);
      } catch (err) {
        return expect(err.message).to.equal(
          `Cannot find connections property in configuration.additionalAttributes.connectionsProperties where correlationIds contains ${CDP_DP_GOVERNANCE_CORRELATION_ID}, and apiProtocol and url exist`
        );
      }
      expect(false).to.equal(true, "Expect function to throw error and not reach here");
    });

    it(
      "handleSecondSynchronizationRequest throws error if configuration.additionalAttributes.connectionsProperties does not contain a property with correlationIds that contains " +
        CDP_DP_GOVERNANCE_CORRELATION_ID,
      async () => {
        const mockUclRequestBody = constructMockUclRequestBody();
        mockUclRequestBody.assignedTenant.configuration.additionalAttributes.connectionsProperties.forEach(
          (property) => (property.correlationIds = ["mock_correlation_id"])
        );
        try {
          await cdpConfig.handleSecondSynchronizationRequest(context, mockUclRequestBody);
        } catch (err) {
          return expect(err.message).to.equal(
            `Cannot find connections property in configuration.additionalAttributes.connectionsProperties where correlationIds contains ${CDP_DP_GOVERNANCE_CORRELATION_ID}, and apiProtocol and url exist`
          );
        }
        expect(false).to.equal(true, "Expect function to throw error and not reach here");
      }
    );

    it(
      "handleSecondSynchronizationRequest throws error if configuration.additionalAttributes.connectionsProperties contains a property with correlationIds that contains " +
        CDP_DP_GOVERNANCE_CORRELATION_ID +
        " and with apiProtocol missing",
      async () => {
        const mockUclRequestBody = constructMockUclRequestBody();
        mockUclRequestBody.assignedTenant.configuration.additionalAttributes.connectionsProperties.forEach(
          (property) => {
            if (property.correlationIds.includes(CDP_DP_GOVERNANCE_CORRELATION_ID)) {
              delete property.apiProtocol;
            }
          }
        );
        try {
          await cdpConfig.handleSecondSynchronizationRequest(context, mockUclRequestBody);
        } catch (err) {
          return expect(err.message).to.equal(
            `Cannot find connections property in configuration.additionalAttributes.connectionsProperties where correlationIds contains ${CDP_DP_GOVERNANCE_CORRELATION_ID}, and apiProtocol and url exist`
          );
        }
        expect(false).to.equal(true, "Expect function to throw error and not reach here");
      }
    );

    it(
      "handleSecondSynchronizationRequest throws error if configuration.additionalAttributes.connectionsProperties contains a property with correlationIds that contains " +
        CDP_DP_GOVERNANCE_CORRELATION_ID +
        " and with url missing",
      async () => {
        const mockUclRequestBody = constructMockUclRequestBody();
        mockUclRequestBody.assignedTenant.configuration.additionalAttributes.connectionsProperties.forEach(
          (property) => {
            if (property.correlationIds.includes(CDP_DP_GOVERNANCE_CORRELATION_ID)) {
              delete property.url;
            }
          }
        );
        try {
          await cdpConfig.handleSecondSynchronizationRequest(context, mockUclRequestBody);
        } catch (err) {
          return expect(err.message).to.equal(
            `Cannot find connections property in configuration.additionalAttributes.connectionsProperties where correlationIds contains ${CDP_DP_GOVERNANCE_CORRELATION_ID}, and apiProtocol and url exist`
          );
        }
        expect(false).to.equal(true, "Expect function to throw error and not reach here");
      }
    );
  });

  it("handleDeleteSynchronizationRequest", async () => {
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    };
    const MOCK_UCL_REQUEST_BODY = {
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;
    const result = await cdpConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.empty;
  });

  it("handleDeleteSynchronizationRequest is allowed when FF is active and specific configuration value is ON", async () => {
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    };
    const MOCK_UCL_REQUEST_BODY = {
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;
    const result = await cdpConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.empty;
  });

  it("handleDeleteSynchronizationRequest is not allowed when specific configuration value is OFF", async () => {
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    };
    const MOCK_UCL_REQUEST_BODY = {
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;
    try {
      await cdpConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    } catch (error) {
      chai.expect(error).to.be.an("Error");
      expect(error.message).to.equal("Deletion is not allowed. Contact SAP Support to request changes to formation");
    }
  });

  it("handleDeleteSynchronizationRequest is not allowed when fails to retrieve specific configuration value from configuration service", async () => {
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    sandbox.stub(store, "getEffectiveValue").throws(new Error("fail to get configuration"));
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    };
    const MOCK_UCL_REQUEST_BODY = {
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;
    try {
      await cdpConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    } catch (error) {
      chai.expect(error).to.be.an("Error");
      expect(error.message).to.equal("Deletion is not allowed. Contact SAP Support to request changes to formation");
    }
  });

  it("handleDeleteSynchronizationRequest is allowed to do unassignment properly", async () => {
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    };
    const MOCK_UCL_REQUEST_BODY = {
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;
    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): ON
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): ON
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "ON"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(true);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    let result = await cdpConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({});
    chai.expect(storeStub.calledOnce).to.equal(false);

    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): ON
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): ON
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "OFF"}
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    result = await cdpConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({});
    // allow to do unassignment properly
    chai.expect(storeStub.calledOnce).to.equal(false);

    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): ON
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): OFF
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "ON"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(true);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    result = await cdpConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({});
    // allow to do unassignment properly
    chai.expect(storeStub.calledOnce).to.equal(false);

    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): ON
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): OFF
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "OFF"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(true);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    result = await cdpConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({});
    // allow to do unassignment
    chai.expect(storeStub.calledOnce).to.equal(false);

    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): OFF
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): ON
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "ON"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(false);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    result = await cdpConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({});
    // allow to do unassignment properly
    chai.expect(storeStub.calledOnce).to.equal(true);
    chai.expect(storeStub.args[0][1]).to.be.equal("DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT");
    chai.expect(storeStub.args[0][2]).to.be.deep.eq({ preferTenantScope: true });

    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): OFF
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): ON
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "OFF"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(false);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    try {
      await cdpConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    } catch (error) {
      chai.expect(error).to.be.an("Error");
      expect(error.message).to.equal("Deletion is not allowed. Contact SAP Support to request changes to formation");
    }
    // block to do unassignment properly
    chai.expect(storeStub.calledOnce).to.equal(true);
    chai.expect(storeStub.args[0][1]).to.be.equal("DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT");
    chai.expect(storeStub.args[0][2]).to.be.deep.eq({ preferTenantScope: true });

    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): OFF
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): OFF
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "ON"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(false);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "ON"}');
    result = await cdpConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({});
    // allow to do unassignment properly
    sinon.assert.notCalled(storeStub);

    storeStub.restore();

    // DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION(FF): OFF
    // DWCO_BDC_BLOCK_FORMATION_DELETION(FF): OFF
    // DWC_ALLOW_UNASSIGN_TENANT_MAPPING_EVENT(config): {"isAllowed": "OFF"}
    ffStub.withArgs("DWCO_BDC_ALLOW_PROPER_FORMATION_DELETION").returns(false);
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    storeStub = sandbox.stub(store, "getEffectiveValue").resolves('{"isAllowed": "OFF"}');
    result = await cdpConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({});
    // allow to do unassignment properly
    sinon.assert.notCalled(storeStub);

    storeStub.restore();
  });
});
