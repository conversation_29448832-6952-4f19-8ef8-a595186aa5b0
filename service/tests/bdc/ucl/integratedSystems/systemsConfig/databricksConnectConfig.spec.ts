/** @format */
import { IX509Credentials, SecureStoreKey, UclCredentialsManager } from "@sap/dwc-credentials";
import { IAuthType } from "@sap/dwc-permissions";
import * as chai from "chai";
import * as chaiAsPromised from "chai-as-promised";
import sinon, { SinonStub } from "sinon";
import sinonChai from "sinon-chai";
import {
  DatabricksConnectConfig,
  IUclRequestConnectionsPropertiesRest,
} from "../../../../../bdc/ucl/integratedSystems/systemsConfig/databricksConnectConfig";
import { testTenantUuid } from "../../../../../lib/node";
import { AuthType } from "../../../../../repository/security/common/common";
import { Activities, RequestContext } from "../../../../../repository/security/requestContext";
import { IntegratedSystemCommunicationHandler } from "../../../../../reuseComponents/ucl/integratedSystems/IntegratedSystemCommunicationHandler";
import { SynchronizationRequestLoggingProxy } from "../../../../../reuseComponents/ucl/integratedSystems/proxies/SynchronizationRequestLoggingProxy";
import { IUclRequestBody, IUclRequestContext, IUclRequestTenantInfo } from "../../../../../ucl/types";

chai.use(chaiAsPromised.default);
chai.use(sinonChai);
const expect = chai.expect;

describe("service/bdc/ucl/integratedSystems/systemsConfig/databricksConnectConfig.ts", () => {
  let sandbox: sinon.SinonSandbox;
  let context: RequestContext;
  let databricksConnectConfig: IntegratedSystemCommunicationHandler;
  let ffStub: SinonStub;

  before(() => {
    sandbox = sinon.createSandbox();
  });

  beforeEach(() => {
    const authType: IAuthType = {
      auth: Activities.read,
      name: AuthType.SYSTEMINFO,
    };
    context = RequestContext.createFromTenantId(testTenantUuid, { authtypes: [authType] });

    // wrap instance of DatabricksConnectConfig in SynchronizationRequestLoggingProxy
    // as we validate the calls to the logger
    databricksConnectConfig = new DatabricksConnectConfig();
    databricksConnectConfig = SynchronizationRequestLoggingProxy.create(databricksConnectConfig);
    ffStub = sandbox.stub(context, "isFeatureFlagActive");
  });

  afterEach(() => {
    void context.finish();
    sandbox.restore();
  });

  it("getLogger", () => {
    const result = databricksConnectConfig.getLogger();
    expect(result).not.to.be.undefined;
  });

  describe("matchesApplicationNamespace", () => {
    it("should return true if applicationNamespace is 'sap.databricks'", async () => {
      const result = await databricksConnectConfig.matchesApplicationNamespace(context, "sap.databricks");
      expect(result).to.be.equal(true);
    });

    it("should return true if applicationNamespace is 'sap.bdcconnect' if DWCO_UCL_TENANT_MAPPING_BDC_DSP_BROWNFIELD_DBX is on", async () => {
      ffStub.withArgs("DWCO_UCL_TENANT_MAPPING_BDC_DSP_BROWNFIELD_DBX").returns(true);
      const result = await databricksConnectConfig.matchesApplicationNamespace(context, "sap.bdcconnect");
      expect(result).to.be.equal(true);
    });

    it("should return false if applicationNamespace is 'mock_namespace'", async () => {
      const result = await databricksConnectConfig.matchesApplicationNamespace(context, "mock_namespace");
      expect(result).to.be.equal(false);
    });
  });

  it("handleFirstSynchronizationRequest", async () => {
    const correlationIds = ["sap.bds:federation.databricks:GOVERNANCE"];
    const MOCK_BDC_TENANT_CERTIFICATE_CHAIN = "mock_bdc_tenant_certificate_chain";
    const getCredentialsStub = sandbox.stub(UclCredentialsManager.prototype, "getCredentials").resolves({
      clientCertificateChain: MOCK_BDC_TENANT_CERTIFICATE_CHAIN,
    } as IX509Credentials);
    const isDbxBrownfieldWorkflowStub = sandbox
      .stub(DatabricksConnectConfig.prototype, "isDbxBrownfieldWorkflow")
      .resolves(false);
    const MOCK_UCL_REQUEST_BODY = {} as IUclRequestBody;
    const result = await databricksConnectConfig.handleFirstSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(getCredentialsStub).to.be.calledOnceWith(SecureStoreKey.UclBdcTenantCredentials);
    expect(isDbxBrownfieldWorkflowStub).to.be.calledOnceWith(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({
      credentials: {
        inboundCommunication: {
          clientCertificateAuthentication: {
            correlationIds,
            certificate: MOCK_BDC_TENANT_CERTIFICATE_CHAIN,
          },
        },
      },
    });
  });

  it("handleFirstSynchronizationRequest with DBX brownfield", async () => {
    const correlationIds = ["sap.bds:grants:GOVERNANCE"];
    const MOCK_BDC_TENANT_CERTIFICATE_CHAIN = "mock_bdc_tenant_certificate_chain";
    const getCredentialsStub = sandbox.stub(UclCredentialsManager.prototype, "getCredentials").resolves({
      clientCertificateChain: MOCK_BDC_TENANT_CERTIFICATE_CHAIN,
    } as IX509Credentials);
    const isDbxBrownfieldWorkflowStub = sandbox
      .stub(DatabricksConnectConfig.prototype, "isDbxBrownfieldWorkflow")
      .resolves(true);
    const MOCK_UCL_REQUEST_BODY = {} as IUclRequestBody;
    const result = await databricksConnectConfig.handleFirstSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);
    expect(getCredentialsStub).to.be.calledOnceWith(SecureStoreKey.UclBdcTenantCredentials);
    expect(isDbxBrownfieldWorkflowStub).to.be.calledOnceWith(context, MOCK_UCL_REQUEST_BODY);
    expect(result).to.be.deep.equal({
      credentials: {
        inboundCommunication: {
          clientCertificateAuthentication: {
            correlationIds,
            certificate: MOCK_BDC_TENANT_CERTIFICATE_CHAIN,
          },
        },
      },
    });
  });

  it("handleSecondSynchronizationRequest - success case", async () => {
    const logInfoStub = sandbox.stub(DatabricksConnectConfig.getLogger(), "logInfo");
    const isDbxBrownfieldWorkflowStub = sandbox
      .stub(DatabricksConnectConfig.prototype, "isDbxBrownfieldWorkflow")
      .resolves(false);

    const MOCK_SHARED_UCL_SYNCHRONIZATION_CONTEXT = {
      uclFormationTypeId: "mock_ucl_formation_type_id",
    } as IUclRequestContext;

    const MOCK_PROTOCOL = "mock_protocol";
    const MOCK_URL = "mock_url";

    const MOCK_UCL_REQUEST_REST: IUclRequestConnectionsPropertiesRest = {
      correlationIds: ["sap.bds:federation.databricks:GOVERNANCE"],
      apiProtocol: MOCK_PROTOCOL,
      url: MOCK_URL,
    };

    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: "mock_ucl_assignment_id",
      configuration: {
        credentials: {
          outboundCommunication: {
            clientCertificateAuthentication: {
              correlationIds: ["sap.bds:federation.databricks:GOVERNANCE"],
            },
          },
        },
        additionalAttributes: {
          connectionsProperties: [MOCK_UCL_REQUEST_REST],
        },
      },
    } as IUclRequestTenantInfo;

    const MOCK_UCL_REQUEST_BODY = {
      context: MOCK_SHARED_UCL_SYNCHRONIZATION_CONTEXT,
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;

    await databricksConnectConfig.handleSecondSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);

    // we verify the succesful handling by receiving a call to the info logger
    expect(logInfoStub).to.be.calledWith(`Governance REST endpoint included in second synchronization request`);
    expect(isDbxBrownfieldWorkflowStub).to.be.calledOnceWith(context, MOCK_UCL_REQUEST_BODY);
  });

  it("handleSecondSynchronizationRequest - success case with DBX brownfield", async () => {
    const logInfoStub = sandbox.stub(DatabricksConnectConfig.getLogger(), "logInfo");
    const isDbxBrownfieldWorkflowStub = sandbox
      .stub(DatabricksConnectConfig.prototype, "isDbxBrownfieldWorkflow")
      .resolves(true);

    const MOCK_SHARED_UCL_SYNCHRONIZATION_CONTEXT = {
      uclFormationTypeId: "mock_ucl_formation_type_id",
    } as IUclRequestContext;

    const MOCK_PROTOCOL = "mock_protocol";
    const MOCK_URL = "mock_url";

    const MOCK_UCL_REQUEST_REST: IUclRequestConnectionsPropertiesRest = {
      correlationIds: ["sap.bds:grants:GOVERNANCE"],
      apiProtocol: MOCK_PROTOCOL,
      url: MOCK_URL,
    };

    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: "mock_ucl_assignment_id",
      configuration: {
        credentials: {
          outboundCommunication: {
            clientCertificateAuthentication: {
              correlationIds: ["sap.bds:grants:GOVERNANCE"],
            },
          },
        },
        additionalAttributes: {
          connectionsProperties: [MOCK_UCL_REQUEST_REST],
        },
      },
    } as IUclRequestTenantInfo;

    const MOCK_UCL_REQUEST_BODY = {
      context: MOCK_SHARED_UCL_SYNCHRONIZATION_CONTEXT,
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;

    await databricksConnectConfig.handleSecondSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);

    // we verify the succesful handling by receiving a call to the info logger
    expect(logInfoStub).to.be.calledWith(`Governance REST endpoint included in second synchronization request`);
    expect(isDbxBrownfieldWorkflowStub).to.be.calledOnceWith(context, MOCK_UCL_REQUEST_BODY);
  });

  it("handleSecondSynchronizationRequest - failure case", async () => {
    const logErrorStub = sandbox.stub(DatabricksConnectConfig.getLogger(), "logError");
    const isDbxBrownfieldWorkflowStub = sandbox
      .stub(DatabricksConnectConfig.prototype, "isDbxBrownfieldWorkflow")
      .resolves(false);

    const MOCK_SHARED_UCL_SYNCHRONIZATION_CONTEXT = {
      uclFormationTypeId: "mock_ucl_formation_type_id",
    } as IUclRequestContext;

    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: "mock_ucl_assignment_id",
      configuration: {
        credentials: {
          outboundCommunication: {
            clientCertificateAuthentication: {
              correlationIds: ["sap.bds:federation.databricks:GOVERNANCE"],
            },
          },
        },
      },
    } as IUclRequestTenantInfo;

    const MOCK_UCL_REQUEST_BODY = {
      context: MOCK_SHARED_UCL_SYNCHRONIZATION_CONTEXT,
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;

    await databricksConnectConfig.handleSecondSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);

    // we verify the correct handling of the error case by checking that logError was called once
    expect(logErrorStub).to.be.calledWith(`No Governance REST endpoint included in second synchronization request`);
    expect(isDbxBrownfieldWorkflowStub).to.be.calledOnceWith(context, MOCK_UCL_REQUEST_BODY);
  });

  it("handleSecondSynchronizationRequest - failure case with DBX brownfield", async () => {
    const logErrorStub = sandbox.stub(DatabricksConnectConfig.getLogger(), "logError");
    const isDbxBrownfieldWorkflowStub = sandbox
      .stub(DatabricksConnectConfig.prototype, "isDbxBrownfieldWorkflow")
      .resolves(true);

    const MOCK_SHARED_UCL_SYNCHRONIZATION_CONTEXT = {
      uclFormationTypeId: "mock_ucl_formation_type_id",
    } as IUclRequestContext;

    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclAssignmentId: "mock_ucl_assignment_id",
      configuration: {
        credentials: {
          outboundCommunication: {
            clientCertificateAuthentication: {
              correlationIds: ["sap.bds:grants:GOVERNANCE"],
            },
          },
        },
      },
    } as IUclRequestTenantInfo;

    const MOCK_UCL_REQUEST_BODY = {
      context: MOCK_SHARED_UCL_SYNCHRONIZATION_CONTEXT,
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;

    await databricksConnectConfig.handleSecondSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY);

    // we verify the correct handling of the error case by checking that logError was called once
    expect(logErrorStub).to.be.calledWith(`No Governance REST endpoint included in second synchronization request`);
    expect(isDbxBrownfieldWorkflowStub).to.be.calledOnceWith(context, MOCK_UCL_REQUEST_BODY);
  });

  it("handleDeleteSynchronizationRequest", async () => {
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(false);
    const logErrorStub = sandbox.stub(DatabricksConnectConfig.getLogger(), "logError");

    const MOCK_UCL_SYSTEM_TENANT_ID = "mock_ucl_system_tenant_id";
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclSystemTenantId: MOCK_UCL_SYSTEM_TENANT_ID,
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    } as IUclRequestTenantInfo;
    const MOCK_SHARED_UCL_SYNCHRONIZATION_CONTEXT = {
      uclFormationTypeId: "mock_ucl_formation_type_id",
    } as IUclRequestContext;
    const MOCK_UCL_REQUEST_BODY = {
      context: MOCK_SHARED_UCL_SYNCHRONIZATION_CONTEXT,
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;

    // we verify the successful handling by checking that no errors are thrown or logged
    expect(async () => await databricksConnectConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY))
      .not.to.throw;
    expect(logErrorStub).not.to.be.called;
  });

  it("handleDeleteSynchronizationRequest is allowed when FF is active and specific configuration value is ON", async () => {
    ffStub.withArgs("DWCO_BDC_BLOCK_FORMATION_DELETION").returns(true);
    const logErrorStub = sandbox.stub(DatabricksConnectConfig.getLogger(), "logError");

    const MOCK_UCL_SYSTEM_TENANT_ID = "mock_ucl_system_tenant_id";
    const MOCK_UCL_ASSIGNMENT_ID = "mock_ucl_assignment_id";
    const MOCK_UCL_REQUEST_TENANT_INFO = {
      uclSystemTenantId: MOCK_UCL_SYSTEM_TENANT_ID,
      uclAssignmentId: MOCK_UCL_ASSIGNMENT_ID,
    } as IUclRequestTenantInfo;
    const MOCK_SHARED_UCL_SYNCHRONIZATION_CONTEXT = {
      uclFormationTypeId: "mock_ucl_formation_type_id",
    } as IUclRequestContext;
    const MOCK_UCL_REQUEST_BODY = {
      context: MOCK_SHARED_UCL_SYNCHRONIZATION_CONTEXT,
      assignedTenant: MOCK_UCL_REQUEST_TENANT_INFO,
    } as IUclRequestBody;

    // we verify the successful handling by checking that no errors are thrown or logged
    expect(async () => await databricksConnectConfig.handleDeleteSynchronizationRequest(context, MOCK_UCL_REQUEST_BODY))
      .not.to.throw;
    expect(logErrorStub).not.to.be.called;
  });
});
