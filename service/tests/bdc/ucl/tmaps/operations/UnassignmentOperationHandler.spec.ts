/** @format */
import { ResolvedResponse } from "@sap/dwc-http-client";
import * as chai from "chai";
import * as chaiAsPromised from "chai-as-promised";
import sinon from "sinon";
import sinonChai from "sinon-chai";
import { UclSystemManagement } from "../../../../../bdc/ucl/integratedSystems/UclSystemManagement";
import { UnassignmentOperationHandler } from "../../../../../bdc/ucl/tmaps/operations/UnassignmentOperationHandler";
import * as utils from "../../../../../bdc/ucl/utils";
import { DeepseaServiceUclFormationClient } from "../../../../../connections/ucl/deepseaServiceUclFormationClient";
import { testTenantUuid } from "../../../../../lib/node";
import { RequestContext } from "../../../../../repository/security/requestContext";
import * as deepseaUtils from "../../../../../reuseComponents/ucl/utils/deepseaUtils";
import { IUclRequestBody, SynchronizationTenantState, UclOperation } from "../../../../../ucl/types";
import {
  TestsIntegratedSystemsNameSpace,
  getTenantFieldOfUclRequestBody,
  getUclSynchronizationContext,
} from "../helper";

chai.use(chaiAsPromised.default);
chai.use(sinonChai);
const expect = chai.expect;

describe("service/bdc/ucl/tmaps/operations/UnassignmentOperationHandler.ts", () => {
  let sandbox: sinon.SinonSandbox;
  let context: RequestContext;
  let ffProviderStub: sinon.SinonStub;
  let sendFormationRefreshStub: sinon.SinonStub;

  before(() => {
    sandbox = sinon.createSandbox();
  });

  beforeEach(() => {
    context = RequestContext.createFromTenantId(testTenantUuid);
    sandbox.stub(UclSystemManagement, "deleteSystemCredentialsRecords").resolves();
    sandbox.stub(utils, "logAuditConfiguration").resolves();
    ffProviderStub = sandbox.stub(context, "isFeatureFlagActive");
    ffProviderStub.callsFake((_) => Promise.resolve(false));
    sendFormationRefreshStub = sandbox
      .stub(DeepseaServiceUclFormationClient.prototype, "sendFormationRefresh")
      .resolves({ status: 201 } as ResolvedResponse);
  });

  afterEach(() => {
    void context.finish();
    sandbox.restore();
  });

  describe("Basic checks", () => {
    let getBdcTenantEnabledStatusStub: sinon.SinonStub;

    beforeEach(() => {
      getBdcTenantEnabledStatusStub = sandbox.stub(utils, "getBdcTenantEnabledStatus");
      getBdcTenantEnabledStatusStub.resolves({ bdcEnabled: true, locked: false });
    });

    it("Should handle start of deletion", async () => {
      ffProviderStub.callsFake((ff) => {
        if (ff === "DWCO_BDC") {
          return Promise.resolve(true);
        }
      });

      const bdcTenantState = SynchronizationTenantState.DELETING;
      const formationTenantState = SynchronizationTenantState.DELETING;
      const uclRequestBody: IUclRequestBody = {
        context: getUclSynchronizationContext(UclOperation.unassign),
        receiverTenant: getTenantFieldOfUclRequestBody(
          bdcTenantState,
          "sap.bdc-cockpit" as TestsIntegratedSystemsNameSpace
        ),
        assignedTenant: getTenantFieldOfUclRequestBody(
          formationTenantState,
          "test.namespace" as TestsIntegratedSystemsNameSpace
        ),
      };
      const unassignmentHandler = new UnassignmentOperationHandler(context, uclRequestBody);
      await expect(unassignmentHandler.getSynchronizationPayload()).to.eventually.be.deep.equal({
        state: SynchronizationTenantState.READY,
      });

      // check if the FormationRefresh is sent if FF is enabled
      expect(sendFormationRefreshStub.callCount).to.equal(1);

      expect(utils.logAuditConfiguration).to.be.calledOnceWith(
        context,
        SynchronizationTenantState.READY,
        uclRequestBody
      );
    });

    it("Should handle retry of deletion after a fail", async () => {
      getBdcTenantEnabledStatusStub.resolves({ bdcEnabled: false, locked: false });

      const bdcTenantState = SynchronizationTenantState.DELETE_ERROR;
      const formationTenantState = SynchronizationTenantState.DELETE_ERROR;
      const uclRequestBody: IUclRequestBody = {
        context: getUclSynchronizationContext(UclOperation.unassign),
        receiverTenant: getTenantFieldOfUclRequestBody(
          bdcTenantState,
          "sap.bdc-cockpit" as TestsIntegratedSystemsNameSpace
        ),
        assignedTenant: getTenantFieldOfUclRequestBody(
          formationTenantState,
          "test.namespace" as TestsIntegratedSystemsNameSpace
        ),
      };
      const unassignmentHandler = new UnassignmentOperationHandler(context, uclRequestBody);
      await expect(unassignmentHandler.getSynchronizationPayload()).to.eventually.be.deep.equal({
        state: SynchronizationTenantState.READY,
      });

      // check if the FormationRefresh is not sent if FF is disabled
      expect(sendFormationRefreshStub.callCount).to.equal(0);

      expect(utils.logAuditConfiguration).to.be.calledOnceWith(
        context,
        SynchronizationTenantState.READY,
        uclRequestBody
      );
    });

    it("Should throw with unmapped state of synchronization", async () => {
      const bdcTenantState = SynchronizationTenantState.CREATE_ERROR;
      const formationTenantState = SynchronizationTenantState.READY;
      const uclRequestBody: IUclRequestBody = {
        context: getUclSynchronizationContext(UclOperation.unassign),
        receiverTenant: getTenantFieldOfUclRequestBody(
          bdcTenantState,
          "sap.bdc-cockpit" as TestsIntegratedSystemsNameSpace
        ),
        assignedTenant: getTenantFieldOfUclRequestBody(
          formationTenantState,
          "test.namespace" as TestsIntegratedSystemsNameSpace
        ),
      };
      const unassignmentHandler = new UnassignmentOperationHandler(context, uclRequestBody);
      await expect(unassignmentHandler.getSynchronizationPayload()).to.eventually.be.deep.equal({
        state: SynchronizationTenantState.DELETE_ERROR,
        error: `Unable to find the correct action for DS State: ${bdcTenantState}`,
      });

      expect(utils.logAuditConfiguration).to.be.calledOnceWith(
        context,
        SynchronizationTenantState.DELETE_ERROR,
        uclRequestBody
      );
    });
  });

  describe("handleSyncDeletion", () => {
    let unassignmentHandler: UnassignmentOperationHandler;
    let uclRequestBody: IUclRequestBody;
    let getBdcTenantEnabledStatusStub: sinon.SinonStub;

    beforeEach(() => {
      ffProviderStub.callsFake((ff) => {
        if (ff === "DWCO_BDC") {
          return Promise.resolve(true);
        }
      });

      uclRequestBody = {
        context: getUclSynchronizationContext(UclOperation.unassign),
        receiverTenant: getTenantFieldOfUclRequestBody(
          SynchronizationTenantState.DELETING,
          "sap.bdc-cockpit" as TestsIntegratedSystemsNameSpace
        ),
        assignedTenant: getTenantFieldOfUclRequestBody(
          SynchronizationTenantState.DELETING,
          "test.namespace" as TestsIntegratedSystemsNameSpace
        ),
      };

      unassignmentHandler = new UnassignmentOperationHandler(context, uclRequestBody);

      getBdcTenantEnabledStatusStub = sandbox.stub(utils, "getBdcTenantEnabledStatus");
    });

    it("Should call notifyDeepsea when system is enabled and unlocked", async () => {
      getBdcTenantEnabledStatusStub.resolves({ bdcEnabled: true, locked: false });

      const notifyDeepseaStub = sandbox.stub(deepseaUtils, "notifyDeepsea").resolves();

      await (unassignmentHandler as any).handleSyncDeletion();

      expect(getBdcTenantEnabledStatusStub).to.have.been.called;
      expect(notifyDeepseaStub).to.have.been.calledOnce;
    });

    it("Should not call notifyDeepsea when system is disabled", async () => {
      getBdcTenantEnabledStatusStub.resolves({ bdcEnabled: false, locked: false });

      const notifyDeepseaStub = sandbox.stub(deepseaUtils, "notifyDeepsea").resolves();

      await (unassignmentHandler as any).handleSyncDeletion();

      expect(getBdcTenantEnabledStatusStub).to.have.been.called;
      expect(notifyDeepseaStub).not.to.have.been.called;
    });

    it("Should not call notifyDeepsea when system is locked", async () => {
      getBdcTenantEnabledStatusStub.resolves({ bdcEnabled: true, locked: true });

      const notifyDeepseaStub = sandbox.stub(deepseaUtils, "notifyDeepsea").resolves();

      await (unassignmentHandler as any).handleSyncDeletion();

      expect(getBdcTenantEnabledStatusStub).to.have.been.called;
      expect(notifyDeepseaStub).not.to.have.been.called;
    });

    it("Should not call notifyDeepsea when system is both disabled and locked", async () => {
      getBdcTenantEnabledStatusStub.resolves({ bdcEnabled: false, locked: true });

      const notifyDeepseaStub = sandbox.stub(deepseaUtils, "notifyDeepsea").resolves();

      await (unassignmentHandler as any).handleSyncDeletion();

      expect(getBdcTenantEnabledStatusStub).to.have.been.called;
      expect(notifyDeepseaStub).not.to.have.been.called;
    });
  });
});
