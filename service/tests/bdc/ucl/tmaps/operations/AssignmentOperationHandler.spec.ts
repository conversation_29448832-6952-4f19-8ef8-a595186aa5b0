/** @format */
import { ResolvedResponse } from "@sap/dwc-http-client";
import * as chai from "chai";
import * as chaiAsPromised from "chai-as-promised";
import sinon from "sinon";
import sinon<PERSON>hai from "sinon-chai";
import { UclSystemManagement } from "../../../../../bdc/ucl/integratedSystems/UclSystemManagement";
import { IntegratedSystemsNameSpace } from "../../../../../bdc/ucl/integratedSystems/integratedSystems";
import { AssignmentOperationHandler } from "../../../../../bdc/ucl/tmaps/operations/AssignmentOperationHandler";
import * as utils from "../../../../../bdc/ucl/utils";
import { DeepseaServiceUclFormationClient } from "../../../../../connections/ucl/deepseaServiceUclFormationClient";
import { testTenantUuid } from "../../../../../lib/node";
import { RequestContext } from "../../../../../repository/security/requestContext";
import { IUclRequestBody, SynchronizationTenantState, UclOperation } from "../../../../../ucl/types";
import {
  TestsIntegratedSystemsNameSpace,
  getTenantFieldOfUclRequestBody,
  getUclSynchronizationContext,
} from "../helper";

chai.use(chaiAsPromised.default);
chai.use(sinonChai);
const expect = chai.expect;

describe("service/bdc/ucl/tmaps/operations/AssignmentOperationHandler.ts", () => {
  let sandbox: sinon.SinonSandbox;
  let context: RequestContext;
  let ffProviderStub: sinon.SinonStub;
  let sendFormationRefreshStub: sinon.SinonStub;
  let assertBdcTenantEnabledStub: sinon.SinonStub;

  before(() => {
    sandbox = sinon.createSandbox();
  });

  beforeEach(() => {
    context = RequestContext.createFromTenantId(testTenantUuid);
    sandbox.stub(UclSystemManagement, "getSystemConfigurationPayload").resolves({});
    sandbox.stub(UclSystemManagement, "propagateSystemCredentials").resolves();
    sandbox.stub(utils, "logAuditConfiguration").resolves();
    ffProviderStub = sandbox.stub(context, "isFeatureFlagActive");
    ffProviderStub.callsFake((_) => Promise.resolve(false));
    sendFormationRefreshStub = sandbox
      .stub(DeepseaServiceUclFormationClient.prototype, "sendFormationRefresh")
      .resolves({ status: 201 } as ResolvedResponse);
    assertBdcTenantEnabledStub = sandbox.stub(utils, "assertBdcTenantEnabled").resolves();
  });

  afterEach(() => {
    void context.finish();
    sandbox.restore();
  });

  describe("Basic checks", () => {
    it("Should handle start of synchronization", async () => {
      const dsTenantState = SynchronizationTenantState.INITIAL;
      const formationTenantState = SynchronizationTenantState.INITIAL;
      const uclRequestBody: IUclRequestBody = {
        context: getUclSynchronizationContext(UclOperation.assign),
        receiverTenant: getTenantFieldOfUclRequestBody(
          dsTenantState,
          "sap.datasphere" as TestsIntegratedSystemsNameSpace
        ),
        assignedTenant: getTenantFieldOfUclRequestBody(formationTenantState, IntegratedSystemsNameSpace.DWC),
      };
      const assignmentHandler = new AssignmentOperationHandler(context, uclRequestBody);
      await expect(assignmentHandler.getSynchronizationPayload()).to.eventually.be.deep.equal({
        state: SynchronizationTenantState.CONFIG_PENDING,
        configuration: {},
      });

      expect(assertBdcTenantEnabledStub).to.be.calledOnce;
      expect(utils.logAuditConfiguration).to.be.calledOnceWith(
        context,
        SynchronizationTenantState.CONFIG_PENDING,
        uclRequestBody
      );
    });

    it("Should fail synchronization start if BDC tenant is not enabled", async () => {
      const dsTenantState = SynchronizationTenantState.INITIAL;
      const formationTenantState = SynchronizationTenantState.INITIAL;
      const uclRequestBody: IUclRequestBody = {
        context: getUclSynchronizationContext(UclOperation.assign),
        receiverTenant: getTenantFieldOfUclRequestBody(
          dsTenantState,
          "sap.datasphere" as TestsIntegratedSystemsNameSpace
        ),
        assignedTenant: getTenantFieldOfUclRequestBody(formationTenantState, IntegratedSystemsNameSpace.DWC),
      };

      const bdcTenantError = new Error("BDC tenant is disabled or locked");
      assertBdcTenantEnabledStub.rejects(bdcTenantError);

      const assignmentHandler = new AssignmentOperationHandler(context, uclRequestBody);

      await expect(assignmentHandler.getSynchronizationPayload()).to.be.rejected;
      expect(assertBdcTenantEnabledStub).to.be.calledOnce;
      expect(UclSystemManagement.getSystemConfigurationPayload).not.to.have.been.called;
    });

    it("Should handle retry of synchronization after a fail during the first wave os synchronization", async () => {
      const dsTenantState = SynchronizationTenantState.CREATE_ERROR;
      const formationTenantState = SynchronizationTenantState.CONFIG_PENDING;
      const uclRequestBody: IUclRequestBody = {
        context: getUclSynchronizationContext(UclOperation.assign),
        receiverTenant: getTenantFieldOfUclRequestBody(
          dsTenantState,
          "sap.datasphere" as TestsIntegratedSystemsNameSpace
        ),
        assignedTenant: getTenantFieldOfUclRequestBody(formationTenantState, IntegratedSystemsNameSpace.DWC),
      };
      const assignmentHandler = new AssignmentOperationHandler(context, uclRequestBody);
      await expect(assignmentHandler.getSynchronizationPayload()).to.eventually.be.deep.equal({
        state: SynchronizationTenantState.CONFIG_PENDING,
        configuration: {},
      });

      expect(assertBdcTenantEnabledStub).to.be.calledOnceWith(context);
      expect(utils.logAuditConfiguration).to.be.calledOnceWith(
        context,
        SynchronizationTenantState.CONFIG_PENDING,
        uclRequestBody
      );
    });

    it("Should handle retry of synchronization after a fail during the second wave of synchronization", async () => {
      const dsTenantState = SynchronizationTenantState.CREATE_ERROR;
      const formationTenantState = SynchronizationTenantState.CONFIG_PENDING;
      const uclRequestBody: IUclRequestBody = {
        context: getUclSynchronizationContext(UclOperation.assign),
        receiverTenant: getTenantFieldOfUclRequestBody(
          dsTenantState,
          "sap.datasphere" as TestsIntegratedSystemsNameSpace,
          undefined
        ),
        assignedTenant: getTenantFieldOfUclRequestBody(formationTenantState, IntegratedSystemsNameSpace.DWC, {}),
      };
      const assignmentHandler = new AssignmentOperationHandler(context, uclRequestBody);
      await expect(assignmentHandler.getSynchronizationPayload()).to.eventually.be.deep.equal({
        state: SynchronizationTenantState.READY,
      });

      // check if the FormationRefresh is not sent if FF is disabled
      expect(sendFormationRefreshStub.callCount).to.equal(0);

      expect(utils.logAuditConfiguration).to.be.calledOnceWith(
        context,
        SynchronizationTenantState.READY,
        uclRequestBody
      );
    });

    it("Should handle end of synchronization", async () => {
      ffProviderStub.callsFake((ff) => {
        if (ff === "DWCO_BDC") {
          return Promise.resolve(true);
        }
      });
      const dsTenantState = SynchronizationTenantState.CONFIG_PENDING;
      const formationTenantState = SynchronizationTenantState.READY;
      const uclRequestBody: IUclRequestBody = {
        context: getUclSynchronizationContext(UclOperation.assign),
        receiverTenant: getTenantFieldOfUclRequestBody(
          dsTenantState,
          "sap.datasphere" as TestsIntegratedSystemsNameSpace,
          undefined
        ),
        assignedTenant: getTenantFieldOfUclRequestBody(formationTenantState, IntegratedSystemsNameSpace.DWC, undefined),
      };
      const assignmentHandler = new AssignmentOperationHandler(context, uclRequestBody);
      await expect(assignmentHandler.getSynchronizationPayload()).to.eventually.be.deep.equal({
        state: SynchronizationTenantState.READY,
      });
      // check if the FormationRefresh is sent if FF is enabled
      expect(sendFormationRefreshStub.callCount).to.equal(1);

      expect(utils.logAuditConfiguration).to.be.calledOnceWith(
        context,
        SynchronizationTenantState.READY,
        uclRequestBody
      );
    });

    it("Should throw with unmapped state of synchronization", async () => {
      const dsTenantState = SynchronizationTenantState.DELETE_ERROR;
      const formationTenantState = SynchronizationTenantState.READY;
      const uclRequestBody: IUclRequestBody = {
        context: getUclSynchronizationContext(UclOperation.assign),
        receiverTenant: getTenantFieldOfUclRequestBody(
          dsTenantState,
          "sap.datasphere" as TestsIntegratedSystemsNameSpace,
          undefined
        ),
        assignedTenant: getTenantFieldOfUclRequestBody(formationTenantState, IntegratedSystemsNameSpace.DWC, undefined),
      };
      const assignmentHandler = new AssignmentOperationHandler(context, uclRequestBody);
      await expect(assignmentHandler.getSynchronizationPayload()).to.eventually.be.deep.equal({
        state: SynchronizationTenantState.CREATE_ERROR,
        error: `Unable to find the correct action for BDC State: ${dsTenantState}`,
      });

      expect(utils.logAuditConfiguration).to.be.calledOnceWith(
        context,
        SynchronizationTenantState.CREATE_ERROR,
        uclRequestBody
      );
    });
  });
});
