/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { httpClient } from "@sap/dwc-http-client";
import { deepClone } from "@sap/seal-csn/dist/utilities";
import { expect } from "chai";
import Status from "http-status-codes";
import { Headers } from "node-fetch";
import sinon from "sinon";
import {
  AdditionalInstallationProperty,
  AdditionalInstallationPropertyName,
  BDCDataProduct,
  BDCPackageInstallationProperties,
  BDCRequestContext,
  BDCSourceProvider,
  SystemInfo,
} from "../../../../shared/bdccockpit/Types";
import { FormationService } from "../../../bdc/formation/formationService";
import { DataProductInstaller } from "../../../bdc/installers/dataProductInstaller";
import {
  FosComponentInstallationDetails,
  InstallationStatus,
} from "../../../bdc/packageInstallation/packageInstallationTypes";
import { SystemsDao } from "../../../bdc/systems/systemsDao";
import { SystemsService } from "../../../bdc/systems/systemsService";
import * as certificateUtils from "../../../bdc/utils/certificateUtils";
import { IntegratedSystemCommunicationHandlerBase } from "../../../ucl/integratedSystems/IntegratedSystemCommunicationHandlerBase";

const dataProductInstaller: DataProductInstaller = new DataProductInstaller();
const bdcFormationTenantId = "c62f62d1-8a5a-4176-8d1c-9e631980c9ae";
const uiContext: IRequestContext = {
  userInfo: { tenantId: "02da35ed-4897-4e21-9223-4e64b08f7ce9" },
} as IRequestContext;
const bdcPackageInstallationProperties: BDCPackageInstallationProperties = {
  applicationNamespace: "sap.s4",
  systemTenant: "7b4ffea5-4bda-41cd-801b-09d25159c9de",
  systemName: "N",
};

const systemLandscape: SystemInfo[] = [
  {
    systemId: "F7E281B0B7573E0619001BB60198679E",
    applicationTenantId: "7b4ffea5-4bda-41cd-801b-09d25159c9de",
    applicationNamespace: "sap.s4",
    name: "ucl-poc-fr02-ectafvwk",
    className: "SYSTEM",
    formations: [
      {
        formationId: "cf133358-7f24-4b51-a75f-d183299cf8b8",
        formationTypeId: "c62f62d1-8a5a-4176-8d1c-9e631980c9ae",
        assignmentId: "c8174472-b25b-4b36-a17a-702bff40ada0",
      },
      {
        formationId: "cf133358-7f24-4b51-a75f-d183299cf8b8_",
        formationTypeId: "c62f62d1-8a5a-4176-8d1c-9e631980c9ae_",
        assignmentId: "c8174472-b25b-4b36-a17a-702bff40ada0_",
      },
      {
        formationId: "cf133358-7f24-4b51-a75f-d183299cf8b8__",
        formationTypeId: "c62f62d1-8a5a-4176-8d1c-9e631980c9ae__",
        assignmentId: "c8174472-b25b-4b36-a17a-702bff40ada0__",
      },
    ],
    propertyTags: [],
  },
  {
    systemId: "60492FC075B0070919003E5E47033B64",
    applicationTenantId: "868420b5-09ba-4c0f-b6e8-2542a5b04781",
    applicationNamespace: "sap.s4",
    name: "Data Product Provider Mock",
    className: "SYSTEM",
    formations: [
      {
        formationId: "cf133358-7f24-4b51-a75f-d183299cf8b8",
        formationTypeId: "c62f62d1-8a5a-4176-8d1c-9e631980c9ae",
        assignmentId: "ed7c1bed-80b6-4792-947b-95c72c53cdf9",
      },
    ],
    propertyTags: [],
  },
  {
    systemId: "564E35D30526B7EF18006F6A264C27D9",
    applicationTenantId: "02da35ed-4897-4e21-9223-4e64b08f7ce9",
    applicationNamespace: "sap.s4",
    name: "dwc-starkiller-hc-ttm.starkiller",
    className: "SYSTEM",
    formations: [
      {
        formationId: "cf133358-7f24-4b51-a75f-d183299cf8b8",
        formationTypeId: "c62f62d1-8a5a-4176-8d1c-9e631980c9ae",
        assignmentId: "44db12ad-d71c-4696-be8b-335e651d20bb",
      },
      {
        formationId: "cf133358-7f24-4b51-a75f-d183299cf8b8___",
        formationTypeId: "c62f62d1-8a5a-4176-8d1c-9e631980c9ae___",
        assignmentId: "44db12ad-d71c-4696-be8b-335e651d20bb___",
      },
    ],
    propertyTags: [],
  },
];

const mockRequestContexts = [
  {
    description: "user has userName",
    requestContext: {
      tenantId: "mockTenantId",
      userInfo: {
        tenantId: "mockTenantId",
        userName: "mockUser",
        userId: "mockUserId",
      },
      isFeatureFlagActive(el: string): Promise<boolean> {
        return Promise.resolve(false);
      },
      correlationId: "mockCorrelationId",
    } as IRequestContext,
    expectedHeaders: {
      "content-type": "application/json",
      "sap-user-name": "mockUser",
      "x-sap-boc-tenant-id": "mockTenantId",
      "x-sap-boc-user-id": "mockUserId",
      "x-correlation-id": "mockCorrelationId",
    },
  },
  {
    description: "user has empty userName",
    requestContext: {
      tenantId: "mockTenantId",
      userInfo: {
        tenantId: "mockTenantId",
        userName: "",
        userId: "mockUserId",
      },
      isFeatureFlagActive(el: string): Promise<boolean> {
        return Promise.resolve(false);
      },
      correlationId: "mockCorrelationId",
    } as IRequestContext,
    expectedHeaders: {
      "content-type": "application/json",
      "x-sap-boc-tenant-id": "mockTenantId",
      "x-sap-boc-user-id": "mockUserId",
      "x-correlation-id": "mockCorrelationId",
    },
  },
  {
    description: "user has null userName",
    requestContext: {
      tenantId: "mockTenantId",
      userInfo: {
        tenantId: "mockTenantId",
        userId: "mockUserId",
      },
      isFeatureFlagActive(el: string): Promise<boolean> {
        return Promise.resolve(false);
      },
      correlationId: "mockCorrelationId",
    } as IRequestContext,
    expectedHeaders: {
      "content-type": "application/json",
      "x-sap-boc-tenant-id": "mockTenantId",
      "x-sap-boc-user-id": "mockUserId",
      "x-correlation-id": "mockCorrelationId",
    },
  },
];
const mockPackageComponent: BDCDataProduct = {
  ordid: "mockOrdid",
  version: "mockVersion",
  provider: "sap.s4",
} as BDCDataProduct;
const mockInstallationProperties: BDCPackageInstallationProperties[] = [
  {
    applicationNamespace: "sap.s4",
    systemTenant: "mockSystemTenant",
    systemName: "mockSystem",
    formationId: "mockformationId",
    formationCatalogUuid: "mockformationCatalogUuid",
    additionalInstallationProperties: [],
  },
];

const getHttpClientStub = (status?: number) => {
  const headers = new Headers();
  headers.append("x-correlation-id", "mockCorrelationId");
  return sinon.stub(httpClient, "call").resolves({
    ok: true,
    redirected: false,
    statusText: "",
    type: "basic",
    url: "",
    status: status ? status : Status.CREATED,
    body: { value: [{ ordId: "mockOrdid", status: "active" }] },
    headers,
  });
};
const detailedMessageRealExample: any = {
  status: "deprovisioning-error",
  onboardedAt: "2025-03-21T19:47:54.935770Z",
  offboardedAt: "2025-03-24T15:41:09.629443Z",
  dataAcquisition: {
    status: "success",
    lastStatusUpdates: [
      {
        resourceId: "sap.s4com.dphome:dataSource:ZDPAllDataTypes5:v1.1.0",
        resourceStatus: "success",
        status: "success",
        correlationId: "43dc5f23-6fbd-4f25-9fcb-5a6a386d6e4b",
        lastSuccessAt: "2025-03-26T06:16:47.306850Z",
        loggedAt: "2025-03-26T06:16:47.306850Z",
        createdAt: "2025-03-26T06:16:47.305693Z",
      },
    ],
  },
  dataTransformation: {
    status: "error",
    lastStatusUpdates: [
      {
        resourceId: "sap.s4com.dphome:dataProduct:ZDPAllDataTypes5:v1",
        resourceStatus: "none",
        status: "none",
        correlationId: "43dc5f23-6fbd-4f25-9fcb-5a6a386d6e4b",
        loggedAt: "2025-03-21T19:50:25.769034Z",
        createdAt: "2025-03-21T19:50:25.778149Z",
      },
      {
        resourceId: "sap.s4com.dphome:dataset:ZDPAllDataTypes5:v1.1.0",
        resourceStatus: "none",
        status: "none",
        correlationId: "43dc5f23-6fbd-4f25-9fcb-5a6a386d6e4b",
        loggedAt: "2025-03-21T19:50:25.514447Z",
        createdAt: "2025-03-21T19:50:25.536106Z",
      },
      {
        resourceId: "sap.s4com.dphome:dataSource:ZDPAllDataTypes5:v1.1.0",
        resourceStatus: "error",
        status: "error",
        correlationId: "43dc5f23-6fbd-4f25-9fcb-5a6a386d6e4b",
        lastSuccessAt: "2025-03-24T15:17:33.032560Z",
        loggedAt: "2025-03-24T15:17:49.317369Z",
        createdAt: "2025-03-24T15:23:01.187541Z",
        messages: [
          {
            code: "16384",
            type: "ERROR",
            text: "WORKFLOW_ERROR",
          },
        ],
      },
    ],
  },
  dataSharing: {
    status: "unshare-error",
    lastStatusUpdates: [
      {
        resourceId: "sap.s4com.dphome:dataProduct:ZDPAllDataTypes5:v1",
        resourceStatus: "unshare-error",
        status: "unshare-error",
        correlationId: "648089b80dab210788beb7c30206450e",
        loggedAt: "2025-03-26T09:58:15.371723Z",
        createdAt: "2025-03-26T09:58:15.381159Z",
        messages: [
          {
            code: "",
            type: "ERROR",
            text: "Unable to create or store tenant specific ord document",
          },
        ],
      },
    ],
  },
};
describe("FoS installer tests", () => {
  afterEach(() => {
    sinon.restore();
  });
  describe("Sanity checks", () => {
    let detectFormationCatalogUuidStub: sinon.SinonStub;
    const fosFormationCatalogUuidStub = "123456789";

    beforeEach(() => {
      detectFormationCatalogUuidStub = sinon.stub(FormationService, "detectFormationCatalogUuid");
    });
    afterEach(() => {
      detectFormationCatalogUuidStub.restore();
    });

    it("getFosBaseUrl - sanity", async () => {
      // Prepare mocks and data for sanity
      sinon.stub(SystemsService, "getAllSystems").callsFake(() => Promise.resolve(systemLandscape));
      sinon
        .stub(SystemsService, "getFosProperties")
        .callsFake(async (context: IRequestContext, fosAssignmentId: string) => [
          { name: AdditionalInstallationPropertyName.fosInstallUrl, value: `http://fos-url.com` },
          { name: AdditionalInstallationPropertyName.fosSystemAssignmentId, value: "12345" },
        ]);
      sinon
        .stub(IntegratedSystemCommunicationHandlerBase.prototype, "isBdcFormation")
        .callsFake(async (context: IRequestContext, formationTypeId: string, assignmentId: string) =>
          Promise.resolve(formationTypeId === bdcFormationTenantId)
        );
      detectFormationCatalogUuidStub.callsFake(
        async (context: IRequestContext, installationProperties: BDCPackageInstallationProperties) => {
          Promise.resolve(fosFormationCatalogUuidStub);
        }
      );

      // Run & Validate
      const fosApiDetails: AdditionalInstallationProperty[] = await dataProductInstaller.getFosApiDetails(
        uiContext,
        bdcPackageInstallationProperties
      );
      expect(
        fosApiDetails.find((ap) => ap.name === AdditionalInstallationPropertyName.fosInstallUrl)?.value
      ).to.be.equal(`http://fos-url.com`);
    });

    it("getFosBaseUrl - no system landscape", async () => {
      sinon.stub(SystemsService, "getFosProperties").callsFake(() => Promise.resolve([]));
      sinon.stub(SystemsService, "getAllSystems").callsFake(() => Promise.resolve(systemLandscape));
      try {
        await dataProductInstaller.getFosApiDetails(uiContext, bdcPackageInstallationProperties);
      } catch (error) {
        expect(error.message.startsWith("Failed to get FOS API details")).to.be.true;
      }
      // As we don'd have UCL tenant mapping now this is default value for starkiller tests.
      // Remove after UCL tenant mapping implementation
    });

    it("getFosBaseUrl - getFosInstallBaseUrl error", async () => {
      // Prepare mocks and data for sanity
      sinon.stub(SystemsDao, "getBdcSystems").callsFake(() => Promise.resolve(systemLandscape));
      sinon.stub(SystemsService, "getFosProperties").callsFake(() => Promise.resolve([]));
      sinon
        .stub(IntegratedSystemCommunicationHandlerBase.prototype, "isBdcFormation")
        .callsFake(async (context: IRequestContext, formationTypeId: string, assignmentId: string) =>
          Promise.resolve(formationTypeId === bdcFormationTenantId)
        );
      try {
        await dataProductInstaller.getFosApiDetails(uiContext, bdcPackageInstallationProperties);
      } catch (error) {
        expect(error.message.startsWith("Failed to get FOS API details")).to.be.true;
      }
    });
  });

  describe("API checks", () => {
    let httpClientStub: sinon.SinonStub;
    let convertFosInstallResponseToBdcStub: sinon.SinonStub;
    let getTenantCredentialsStub: sinon.SinonStub;
    let getFosApiDetailsStub: sinon.SinonStub;

    const mockRequestContext: IRequestContext = mockRequestContexts[0].requestContext;
    const componentInstallationStatus = [{ ordId: "mockOrdId" }];
    const provider: BDCSourceProvider = "sap.s4";
    const installationStatus: InstallationStatus = {
      installationStatusCode: "DONE",
      installationStatusMessage: "",
    };

    const validateCommonStubCalls = () => {
      expect(httpClientStub.calledOnce, "httpClient.call should be called once").to.be.true;
      expect(getTenantCredentialsStub.calledOnce, "getTenantCredentials should be called once").to.be.true;
      expect(getFosApiDetailsStub.calledOnce, "getFosApiDetails should be called once").to.be.true;
    };
    const validateHttpCall = (method: string, url: string) => {
      const callArgs = httpClientStub.getCall(0).args[0];
      expect(callArgs.url, "httpClient.call url should match the expected URL").to.equal(url);
      expect(callArgs.opts?.method, "httpClient.call method should match the expected method").to.equal(method);
      expect(callArgs.opts?.key, "httpClient.call key should match the expected key").to.deep.equal([
        {
          pem: "mockPrivateKey",
          passphrase: "mockPassphrase",
        },
      ]);
      expect(callArgs.opts?.cert, "httpClient.call cert should match the expected certificate").to.equal(
        "mockClientCertificateChain"
      );
      expect(callArgs.opts?.minVersion, "httpClient.call minVersion should be 'TLSv1.2'").to.equal("TLSv1.2");
      expect(callArgs.opts?.rejectUnauthorized, "httpClient.call rejectUnauthorized should be true").to.be.true;
    };
    const validateAcceptedStatusCodes = (actualStatusCodes: number[] | undefined, expectedStatusCodes: number[]) => {
      expect(actualStatusCodes, "httpClient.call acceptedStatusCodes should match expected status codes").to.deep.equal(
        expectedStatusCodes
      );
    };
    const validateHttpHeaders = (expectedHeaders: { [key: string]: string | undefined }) => {
      const callArgs = httpClientStub.getCall(0).args[0];
      expect(callArgs.opts?.headers, "httpClient.call headers should match expected headers").to.deep.equal(
        expectedHeaders
      );
    };
    const validateHttpBody = (expectedBodyContent: any) => {
      const callArgs = httpClientStub.getCall(0).args[0];
      expect(callArgs.opts?.body, "httpClient.call body should match expected body").to.equal(
        JSON.stringify({
          value: [expectedBodyContent],
        })
      );
    };
    beforeEach(() => {
      convertFosInstallResponseToBdcStub = sinon
        .stub(dataProductInstaller as any, "convertFosInstallResponseToBdc")
        .returns([{ ordId: "mockOrdId" }]);
      getTenantCredentialsStub = sinon.stub(certificateUtils, "getTenantCredentials").resolves({
        clientCertificateChain: "mockClientCertificateChain",
        privateKey: "mockPrivateKey",
        passphrase: "mockPassphrase",
      });
      getFosApiDetailsStub = sinon
        .stub(dataProductInstaller, "getFosApiDetails")
        .resolves([{ name: AdditionalInstallationPropertyName.fosInstallUrl, value: "http://fos-url.com" }]);
      sinon.stub(dataProductInstaller as any, "convertFosStatus").returns("DONE");
    });

    describe("Installation requests", () => {
      beforeEach(async () => {
        httpClientStub = getHttpClientStub();
      });
      describe("install()", () => {
        let result: FosComponentInstallationDetails[];
        beforeEach(async () => {
          result = await dataProductInstaller.install(
            mockRequestContext,
            mockPackageComponent,
            mockInstallationProperties
          );
        });
        it("should return expected details", async () => {
          expect(result)
            .to.be.an("array")
            .with.deep.members([{ ordId: "mockOrdId" }]);
        });
        it("should call necessary functions properly", async () => {
          expect(convertFosInstallResponseToBdcStub.calledOnce, "convertFosInstallResponseToBdc should be called once")
            .to.be.true;
          validateCommonStubCalls();
        });
        it("should make http calls properly", async () => {
          validateHttpCall("POST", "http://fos-url.com");
          validateAcceptedStatusCodes(httpClientStub.getCall(0).args[0].opts?.acceptedStatusCodes, [
            Status.CONFLICT,
            Status.OK,
            Status.CREATED,
            Status.ACCEPTED,
            Status.NO_CONTENT,
            Status.UNAUTHORIZED,
            Status.INTERNAL_SERVER_ERROR,
            Status.BAD_REQUEST,
            Status.BAD_GATEWAY,
            Status.LOCKED,
          ]);
          validateHttpBody({
            ordId: mockPackageComponent.ordid,
            correlationId: mockRequestContext.correlationId,
            version: mockPackageComponent.version,
          });
        });
      });
      describe("uninstall()", () => {
        let result: FosComponentInstallationDetails[];
        beforeEach(async () => {
          result = await dataProductInstaller.uninstall(
            mockRequestContext,
            mockPackageComponent,
            mockInstallationProperties
          );
        });
        it("should return expected details", async () => {
          expect(result)
            .to.be.an("array")
            .with.deep.members([{ ordId: "mockOrdId" }]);
        });
        it("should call necessary functions properly", async () => {
          expect(convertFosInstallResponseToBdcStub.calledOnce, "convertFosInstallResponseToBdc should be called once")
            .to.be.true;
          validateCommonStubCalls();
        });
        it("should make http calls properly", async () => {
          validateHttpCall("PATCH", "http://fos-url.com");
          validateAcceptedStatusCodes(httpClientStub.getCall(0).args[0].opts?.acceptedStatusCodes, [
            Status.CONFLICT,
            Status.OK,
            Status.CREATED,
            Status.ACCEPTED,
            Status.NO_CONTENT,
            Status.NOT_FOUND,
          ]);
          validateHttpBody({
            ordId: mockPackageComponent.ordid,
            correlationId: mockRequestContext.correlationId,
            offboardMode: "hard",
          });
        });
      });
    });
    describe("Status requests", () => {
      beforeEach(async () => {
        httpClientStub = getHttpClientStub(Status.OK);
      });
      describe("doGetStatus()", () => {
        let result: InstallationStatus;
        beforeEach(async () => {
          result = await dataProductInstaller.doGetStatus(
            mockRequestContext,
            componentInstallationStatus,
            mockInstallationProperties,
            true,
            provider
          );
        });
        it("should return expected status", async () => {
          expect(result.installationStatusCode, "Result installationStatusCode should be 'DONE'").to.equal("DONE");
          expect(result.installationStatusMessage, "Result installationStatusMessage should be empty").to.be.empty;
        });
        it("should call necessary functions properly", async () => {
          validateCommonStubCalls();
        });
        it("should make http calls properly", async () => {
          validateHttpCall("GET", "http://fos-url.com/mockOrdId");
          validateAcceptedStatusCodes(httpClientStub.getCall(0).args[0].opts?.acceptedStatusCodes, [
            Status.CONFLICT,
            Status.OK,
            Status.CREATED,
            Status.ACCEPTED,
            Status.NO_CONTENT,
            Status.NOT_FOUND,
          ]);
        });
      });
      describe("printDetailedStatus()", () => {
        beforeEach(async () => {
          const componentInstallationStatus = [{ ordId: "mockOrdId" }];
          const installationStatus: InstallationStatus = {
            installationStatusCode: "DONE",
            installationStatusMessage: "",
          };
          await dataProductInstaller.printDetailedStatus(
            mockRequestContext,
            componentInstallationStatus,
            mockInstallationProperties,
            installationStatus,
            provider
          );
        });
        it("should call necessary functions properly", async () => {
          validateCommonStubCalls();
        });
        it("should make http calls properly", async () => {
          validateHttpCall("GET", "http://fos-url.com/mockOrdId/statusSummary");
          validateAcceptedStatusCodes(httpClientStub.getCall(0).args[0].opts?.acceptedStatusCodes, [
            Status.CONFLICT,
            Status.OK,
            Status.CREATED,
            Status.ACCEPTED,
            Status.NO_CONTENT,
          ]);
        });
      });
    });
    describe("HTTP header validations", () => {
      mockRequestContexts.forEach(({ description, requestContext, expectedHeaders }) => {
        describe(`Installation requests when ${description}`, () => {
          beforeEach(async () => {
            httpClientStub = getHttpClientStub();
          });
          it("should set correct headers for install", async () => {
            await dataProductInstaller.install(requestContext, mockPackageComponent, mockInstallationProperties);
            validateHttpHeaders(expectedHeaders);
          });

          it("should set correct headers for uninstall", async () => {
            await dataProductInstaller.uninstall(requestContext, mockPackageComponent, mockInstallationProperties);
            validateHttpHeaders(expectedHeaders);
          });
        });
        describe(`Status requests when ${description}`, () => {
          beforeEach(async () => {
            httpClientStub = getHttpClientStub(Status.OK);
          });
          it("should set correct headers for doGetStatus", async () => {
            await dataProductInstaller.doGetStatus(
              requestContext,
              componentInstallationStatus,
              mockInstallationProperties,
              true,
              provider
            );
            validateHttpHeaders(expectedHeaders);
          });
          it("should set correct headers for printDetailedStatus", async () => {
            await dataProductInstaller.printDetailedStatus(
              requestContext,
              componentInstallationStatus,
              mockInstallationProperties,
              installationStatus,
              provider
            );
            validateHttpHeaders(expectedHeaders);
          });
        });
      });
    });
  });
});
describe("Detailed message sanitizing test", () => {
  it("Detailed message sanitization - sanity", async () => {
    const sanitizedMessage = new DataProductInstaller().sanitizeDetailedResponse(
      mockRequestContexts[0].requestContext,
      detailedMessageRealExample
    );
    expect(Object.keys(sanitizedMessage).length).to.be.equal(6);
    expect(sanitizedMessage.status).to.be.equal("deprovisioning-error");
    expect(sanitizedMessage.offboardedAt).to.be.equal("2025-03-24T15:41:09.629443Z");
    expect(sanitizedMessage.onboardedAt).to.be.equal("2025-03-21T19:47:54.935770Z");

    expect(Object.keys(sanitizedMessage.dataAcquisition).length).to.be.equal(1);
    expect(sanitizedMessage.dataAcquisition.status).to.be.equal("success");

    expect(Object.keys(sanitizedMessage.dataTransformation).length).to.be.equal(1);
    expect(sanitizedMessage.dataTransformation.status).to.be.equal("error");

    expect(Object.keys(sanitizedMessage.dataSharing).length).to.be.equal(1);
    expect(sanitizedMessage.dataSharing.status).to.be.equal("unshare-error");
  });
  it("Detailed message sanitization - dataSharing is missing", async () => {
    const cloneDetailedMessageRealExample = deepClone(detailedMessageRealExample);
    delete cloneDetailedMessageRealExample["dataSharing"];
    const sanitizedMessage = new DataProductInstaller().sanitizeDetailedResponse(
      mockRequestContexts[0].requestContext,
      cloneDetailedMessageRealExample
    );
    expect(Object.keys(sanitizedMessage).length).to.be.equal(5);
    expect(sanitizedMessage.status).to.be.equal("deprovisioning-error");
    expect(sanitizedMessage.offboardedAt).to.be.equal("2025-03-24T15:41:09.629443Z");
    expect(sanitizedMessage.onboardedAt).to.be.equal("2025-03-21T19:47:54.935770Z");

    expect(Object.keys(sanitizedMessage.dataAcquisition).length).to.be.equal(1);
    expect(sanitizedMessage.dataAcquisition.status).to.be.equal("success");

    expect(Object.keys(sanitizedMessage.dataTransformation).length).to.be.equal(1);
    expect(sanitizedMessage.dataTransformation.status).to.be.equal("error");

    expect(sanitizedMessage.dataSharing).to.be.equal(undefined);
  });
  it("Detailed message sanitization - undefined", async () => {
    const sanitizedMessage = new DataProductInstaller().sanitizeDetailedResponse(
      mockRequestContexts[0].requestContext,
      undefined
    );
    expect(sanitizedMessage.status).to.be.equal("Failed to sanitize, please see logs for more details");
  });
});
describe("Self healing status calculation", () => {
  let clock: sinon.SinonFakeTimers;
  const mockContext: BDCRequestContext = {
    tenantId: "test-tenant",
    userInfo: { userId: "test-user-id", userName: "test-user-name" },
    spanContext: { traceId: "test-trace-id" },
  } as BDCRequestContext;
  let nineHoursAgoDate: Date;
  let oneHourAgoDate: Date;
  beforeEach(() => {
    // Mock Date.now() to return a specific timestamp (e.g., 2025-01-01T10:00:00.000Z)
    // You provide the milliseconds since epoch
    const fixedTimestamp = new Date("2025-01-01T10:00:00.000Z").getTime();
    clock = sinon.useFakeTimers(fixedTimestamp);
    nineHoursAgoDate = new Date(Date.now() - 9 * 60 * 60 * 1000);
    oneHourAgoDate = new Date(Date.now() - 1 * 60 * 60 * 1000);

    // Alternatively, if you just want to freeze time at the current moment of `useFakeTimers` call:
    // clock = sinon.useFakeTimers();
  });

  afterEach(() => {
    // Restore the original Date.now() and other time functions
    clock.restore();
  });
  it("Detailed message sanitization - sanity", async () => {
    const response1 = await dataProductInstaller.handleStatusForSelfHealing(mockContext, "DONE", {
      selfHealingStartTime: undefined,
    } as FosComponentInstallationDetails);
    expect(response1.newInstallationStatusCode).to.be.eq("DONE");
    expect(response1.newSelfHealingStartTime).to.be.undefined;

    const response2 = await dataProductInstaller.handleStatusForSelfHealing(mockContext, "DONE", {
      selfHealingStartTime: nineHoursAgoDate.toUTCString(),
    } as FosComponentInstallationDetails);
    expect(response2.newInstallationStatusCode).to.be.eq("DONE");
    expect(response2.newSelfHealingStartTime).to.be.undefined;

    const response3 = await dataProductInstaller.handleStatusForSelfHealing(mockContext, "EXECUTING", {
      selfHealingStartTime: nineHoursAgoDate.toUTCString(),
    } as FosComponentInstallationDetails);
    expect(response3.newInstallationStatusCode).to.be.eq("EXECUTING");
    expect(response3.newSelfHealingStartTime).to.be.undefined;

    const response4 = await dataProductInstaller.handleStatusForSelfHealing(mockContext, "EXECUTING", {
      selfHealingStartTime: undefined,
    } as FosComponentInstallationDetails);
    expect(response4.newInstallationStatusCode).to.be.eq("EXECUTING");
    expect(response4.newSelfHealingStartTime).to.be.undefined;

    const response5 = await dataProductInstaller.handleStatusForSelfHealing(mockContext, "PENDING", {
      selfHealingStartTime: undefined,
    } as FosComponentInstallationDetails);
    expect(response5.newInstallationStatusCode).to.be.eq("PENDING");
    expect(response5.newSelfHealingStartTime).to.be.undefined;

    const response6 = await dataProductInstaller.handleStatusForSelfHealing(mockContext, "NOT ACTIVATED", {
      selfHealingStartTime: undefined,
    } as FosComponentInstallationDetails);
    expect(response6.newInstallationStatusCode).to.be.eq("NOT ACTIVATED");
    expect(response6.newSelfHealingStartTime).to.be.undefined;

    const response7 = await dataProductInstaller.handleStatusForSelfHealing(mockContext, "WAITING_TO_INSTALL_READY", {
      selfHealingStartTime: undefined,
    } as FosComponentInstallationDetails);
    expect(response7.newInstallationStatusCode).to.be.eq("WAITING_TO_INSTALL_READY");
    expect(response7.newSelfHealingStartTime).to.be.undefined;
  });
  it("Self healing status calculation - failing first time", async () => {
    const response8 = await dataProductInstaller.handleStatusForSelfHealing(mockContext, "FAILED", {
      selfHealingStartTime: undefined,
    } as FosComponentInstallationDetails);
    // Status changed to EXECUTING, start time to "now"
    expect(response8.newInstallationStatusCode).to.be.eq("EXECUTING");
    expect(new Date(response8.newSelfHealingStartTime!).getTime()).to.be.eq(new Date(Date.now()).getTime());
  });
  it("Self healing status calculation - failing withing grace period, i.e. less than 8 hour ago", async () => {
    const response9 = await dataProductInstaller.handleStatusForSelfHealing(mockContext, "FAILED", {
      selfHealingStartTime: oneHourAgoDate.toUTCString(),
    } as FosComponentInstallationDetails); // one hour ago
    // Status changed to EXECUTING, start stays the same
    expect(response9.newInstallationStatusCode).to.be.eq("EXECUTING");
    expect(response9.newSelfHealingStartTime).to.be.eq(oneHourAgoDate.toUTCString());
  });
  it("Self healing status calculation - failing after grace period, i.e. more than 8 hour ago", async () => {
    const response10 = await dataProductInstaller.handleStatusForSelfHealing(mockContext, "FAILED", {
      selfHealingStartTime: nineHoursAgoDate.toUTCString(),
    } as FosComponentInstallationDetails);
    // Status stays FAILED, start time reset i.e. undefined
    expect(response10.newInstallationStatusCode).to.be.eq("FAILED");
    expect(response10.newSelfHealingStartTime).to.be.eq(nineHoursAgoDate.toUTCString());
  });
});
