/** @format */

import { httpClient } from "@sap/dwc-http-client";
import { ITenantInformation, TenantInformationProvider } from "@sap/dwc-tms-provider";
import { SpanContext } from "@sap/dwc-tracing";
import { expect } from "chai";
import Status from "http-status-codes";
import sinon from "sinon";
import { BDCRequestContext } from "../../../../shared/bdccockpit/Types";
import { TenantSKU } from "../../../bdc/entitlement/entitlementTypes";
import * as unifiedServicesApiClient from "../../../bdc/entitlement/unifiedServicesApiClient";
import { checkCICLicenses, getTenantEntitlementByName } from "../../../bdc/entitlement/unifiedServicesApiClient";
import * as certificateUtils from "../../../bdc/utils/certificateUtils";

describe("unifiedServicesApiClient", () => {
  let context: BDCRequestContext;
  let getTenantCredentialsStub: sinon.SinonStub;

  beforeEach(async () => {
    context = {
      spanContext: { traceId: "trace-id", spanId: "span-id", traceFlags: 1 },
      correlationId: "correlation-id",
      userInfo: { tenantId: "tenant-id" },
      isFeatureFlagActive: sinon.stub().resolves(false),
    } as unknown as BDCRequestContext;
    getTenantCredentialsStub = sinon.stub(certificateUtils, "getTenantCredentials").resolves({
      clientCertificateChain: "client-cert",
      privateKey: "private-key",
      passphrase: "passphrase",
    });
  });

  afterEach(() => {
    sinon.restore();
  });

  describe("getTenantEntitlementByName context tests", () => {
    beforeEach(async () => {
      sinon.stub(httpClient, "call").resolves({ status: Status.OK } as any);
    });

    const testCases = [
      { description: "with spanContext", spanContext: { traceId: "trace-id", spanId: "span-id", traceFlags: 1 } },
      { description: "with empty spanContext", spanContext: {} as SpanContext },
      { description: "with undefined spanContext", spanContext: undefined },
    ];

    testCases.forEach(({ description, spanContext }) => {
      it(`should get tenant entitlements ${description}`, async () => {
        context.spanContext = spanContext;
        await getTenantEntitlementByName(context, TenantSKU.S4.toString());
        const contextForCredentials = getTenantCredentialsStub.getCall(0).args[0] as BDCRequestContext;
        expect(contextForCredentials.userInfo.tenantId, "tenantId should be BDCC").to.equal("BDCC");
        expect(contextForCredentials.spanContext, "spanContext should match the context spanContext").to.deep.equal(
          context.spanContext
        );
      });
    });
  });

  describe("getTenantEntitlementByName entitlement test", () => {
    const entitlements = new Map<string, boolean>([
      [TenantSKU.S4PCE.toString(), false],
      [TenantSKU.HCM.toString(), false],
      [TenantSKU.S4.toString(), false],
    ]);
    let hasEntitlement: boolean | undefined;

    beforeEach(async () => {
      sinon.stub(unifiedServicesApiClient, "getTenantEntitlements").resolves(entitlements);
    });

    it(`expected to return false for entitlement CoreERPIASAPManagedTenant`, async () => {
      hasEntitlement = await getTenantEntitlementByName(context, TenantSKU.S4PCE.toString());
      expect(hasEntitlement).to.equal(false);
    });

    it(`expected to return false for entitlement PublicERPInsightAppTenant`, async () => {
      hasEntitlement = await getTenantEntitlementByName(context, TenantSKU.S4.toString());
      expect(hasEntitlement).to.equal(false);
    });

    it(`expected to return false for entitlement HCMAnalyticsInsightAppTenant`, async () => {
      hasEntitlement = await getTenantEntitlementByName(context, TenantSKU.HCM.toString());
      expect(hasEntitlement).to.equal(false);
    });
  });

  describe("Unified Services flow test", () => {
    let hasEntitlement: boolean | undefined;

    beforeEach(async () => {
      sinon.stub(unifiedServicesApiClient, "getTenantData").resolves({
        items: [
          {
            resource: {
              metadata: {
                path: "orgPath",
              },
            },
          },
        ],
      });
      sinon.stub(unifiedServicesApiClient, "getEntitlement").resolves(true);
    });

    it(`expected to return true for entitlement CoreERPIASAPManagedTenant`, async () => {
      hasEntitlement = await getTenantEntitlementByName(context, TenantSKU.S4PCE.toString());
      expect(hasEntitlement).to.equal(true);
    });

    it(`expected to return true for entitlement PublicERPInsightAppTenant`, async () => {
      hasEntitlement = await getTenantEntitlementByName(context, TenantSKU.S4.toString());
      expect(hasEntitlement).to.equal(true);
    });

    it(`expected to return true for entitlement HCMAnalyticsInsightAppTenant`, async () => {
      hasEntitlement = await getTenantEntitlementByName(context, TenantSKU.HCM.toString());
      expect(hasEntitlement).to.equal(true);
    });
  });

  describe("checkCICLicenses entitlement test", () => {
    const entitlements = new Map<string, boolean>();
    const tenantInfo: ITenantInformation = {
      uuid: "uuid",
      versionUuid: "versionUuid",
      useCloudId: false,
      license: {
        thresholdBdcIASKUS4PCE: "false",
        thresholdBdcIASKUHCM: "true",
        thresholdBdcIASKUS4PuC: "true",
      },
    };

    beforeEach(async () => {
      sinon.stub(TenantInformationProvider, "getTenantInformation").resolves(tenantInfo);
    });

    it(`expected to return false for entitlement CoreERPIASAPManagedTenant`, async () => {
      await checkCICLicenses(context, entitlements);
      expect(entitlements.get(TenantSKU.S4PCE.toString())).to.equal(false);
    });

    it(`expected to return false for entitlement PublicERPInsightAppTenant`, async () => {
      await checkCICLicenses(context, entitlements);
      expect(entitlements.get(TenantSKU.S4.toString())).to.equal(true);
    });

    it(`expected to return true for entitlement HCMAnalyticsInsightAppTenant`, async () => {
      await checkCICLicenses(context, entitlements);
      expect(entitlements.get(TenantSKU.HCM.toString())).to.equal(true);
    });
  });

  describe("CIC licenses flow test", () => {
    const tenantInfo: ITenantInformation = {
      uuid: "uuid",
      versionUuid: "versionUuid",
      useCloudId: false,
      license: {
        thresholdBdcIASKUS4PCE: "true",
        thresholdBdcIASKUHCM: "false",
        thresholdBdcIASKUS4PuC: "true",
      },
    };
    let hasEntitlement: boolean | undefined;

    beforeEach(async () => {
      context = {
        spanContext: { traceId: "trace-id", spanId: "span-id", traceFlags: 1 },
        correlationId: "correlation-id",
        userInfo: { tenantId: "tenant-id" },
        isFeatureFlagActive: sinon.stub().resolves(true),
      } as unknown as BDCRequestContext;
      sinon.stub(unifiedServicesApiClient, "getTenantData").resolves(undefined);
      sinon.stub(TenantInformationProvider, "getTenantInformation").resolves(tenantInfo);
    });

    it(`expected to return true for entitlement CoreERPIASAPManagedTenant`, async () => {
      hasEntitlement = await getTenantEntitlementByName(context, TenantSKU.S4PCE.toString());
      expect(hasEntitlement).to.equal(true);
    });

    it(`expected to return true for entitlement PublicERPInsightAppTenant`, async () => {
      hasEntitlement = await getTenantEntitlementByName(context, TenantSKU.S4.toString());
      expect(hasEntitlement).to.equal(true);
    });

    it(`expected to return false for entitlement HCMAnalyticsInsightAppTenant`, async () => {
      hasEntitlement = await getTenantEntitlementByName(context, TenantSKU.HCM.toString());
      expect(hasEntitlement).to.equal(false);
    });
  });
});
