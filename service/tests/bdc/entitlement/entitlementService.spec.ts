/** @format */

import { expect } from "chai";
import sinon from "sinon";
import { BDCRequestContext } from "../../../../shared/bdccockpit/Types";
import * as entitlementDao from "../../../bdc/entitlement/entitlementDao";
import * as entitlementService from "../../../bdc/entitlement/entitlementService";
import { EntitlementService } from "../../../bdc/entitlement/entitlementService";
import * as unifiedServicesApiClient from "../../../bdc/entitlement/unifiedServicesApiClient";
import * as metricsUtils from "../../../bdc/metrics/metricsUtils";
import * as solaceUtils from "../../../bdc/utils/solaceUtils";

describe("EntitlementService Tests", () => {
  let mockContext: BDCRequestContext;
  let sendMetricStub: sinon.SinonStub;
  let getAllEntitlementsStub: sinon.SinonStub;
  let getTenantEntitlementByNameStub: sinon.SinonStub;

  beforeEach(() => {
    mockContext = { tenantId: "testTenant", userInfo: { userId: "testUser" } } as BDCRequestContext;
    mockContext.isFeatureFlagActive = sinon.stub().resolves(true);
    sendMetricStub = sinon.stub(metricsUtils, "invokeMetricsSafely");
    getAllEntitlementsStub = sinon.stub(entitlementDao, "getAllEntitlements");
    getTenantEntitlementByNameStub = sinon.stub(unifiedServicesApiClient, "getTenantEntitlementByName");
    sinon.stub(solaceUtils, "sendEntitlementCheckMessage").resolves();
    sinon.stub(entitlementDao, "updateEntitlement").resolves();
    sinon.stub(entitlementDao, "deleteEntitlement").resolves();
    sinon.stub(entitlementService.EntitlementService as any, "isGracePeriodExpired").returns(false);
    sinon.stub(entitlementService.EntitlementService as any, "isEntitlementNameValid").resolves(true);
  });

  afterEach(() => {
    sinon.restore();
  });

  const setupEntitlementStubs = () => {
    getAllEntitlementsStub.resolves([
      { entitlement: "VALID_ENTITLEMENT", lastCheckTime: null },
      { entitlement: "INVALID_ENTITLEMENT", lastCheckTime: null },
    ]);
    getTenantEntitlementByNameStub.withArgs(mockContext, "VALID_ENTITLEMENT").resolves(true);
    getTenantEntitlementByNameStub.withArgs(mockContext, "INVALID_ENTITLEMENT").resolves(false);
  };

  const verifyMetricCalls = (expectedCalls: Array<{ status: string; count: number }>) => {
    expect(sendMetricStub.callCount, "sendMetricStub call count mismatch").to.equal(expectedCalls.length);
    expectedCalls.forEach((call, index) => {
      expect(sendMetricStub.getCall(index).args[0], `Expected a function at call ${index}`).to.be.a("function");
      expect(sendMetricStub.getCall(index).args[1], `Expected mockContext at call ${index}`).to.equal(mockContext);
      const metricCall = sendMetricStub.getCall(index).args[0];
      const mockMetricsService = { incrementEntitlementChecks: sinon.spy() };
      metricCall(mockMetricsService);
      expect(
        mockMetricsService.incrementEntitlementChecks.calledWith(call.status, mockContext),
        `incrementEntitlementChecks should be called with '${call.status}' at call ${index}. Actual: ${
          mockMetricsService.incrementEntitlementChecks.getCall(0)?.args[0]
        }, Expected: ${call.status}`
      ).to.be.true;
    });
  };

  it("should send successful status metric for both valid and invalid entitlements", async () => {
    setupEntitlementStubs();
    await EntitlementService.checkEntitlements(mockContext);
    verifyMetricCalls([
      { status: "Successful", count: 1 },
      { status: "Successful", count: 1 },
    ]);
  });

  it("should send failed status when getTenantEntitlementByName returns undefined", async () => {
    setupEntitlementStubs();
    getTenantEntitlementByNameStub.withArgs(mockContext, "VALID_ENTITLEMENT").resolves(undefined);
    await EntitlementService.checkEntitlements(mockContext);
    verifyMetricCalls([
      { status: "Failed", count: 1 },
      { status: "Successful", count: 1 },
    ]);
  });
});
