/** @format */
// FILEOWNER: [Provisioning]
import { HistogramInstrument } from "@opentelemetry/sdk-metrics/build/src/Instruments";
import chai, { expect } from "chai";
import sinonChai from "sinon-chai";

import chaiAsPromised from "chai-as-promised";
import sinon, { SinonStub } from "sinon";
import { OperationResult, TenantClassification, TenantType } from "../../../../shared/provisioning/ftc/types";
import { FeatureFlagProvider } from "../../../featureflags/FeatureFlagProvider";
import { ITenantInformation } from "../../../featureflags/types";

import { TimeUnit } from "@sap/deepsea-utils";
import { httpClient } from "@sap/dwc-http-client";
import { MessageEvent } from "@sap/orca-starter-solace";
import { testTenantUuid } from "../../../lib/node";
import * as migrationUltils from "../../../messageQueuing/QueueMigrationUtils";
import {
  ITenantProvisioningMessage,
  TenantProvisioningConsumer,
} from "../../../messageQueuing/consumer/TenantProvisioningConsumer";
import { TenantProvisioning } from "../../../provisioning/TenantProvisioning";
import { FtcTelemetryGetter } from "../../../provisioning/flexible/telemetry/ftcTelemetryGetter";
import { FTC_TELEMETRY_METRICS } from "../../../provisioning/flexible/telemetry/metricsDefinition";
import { BasicProvisioning } from "../../../provisioning/provisioningFlows/BasicProvisioning";
import { BdcTenantProvisioning } from "../../../provisioning/provisioningFlows/BdcTenantProvisioning";
import * as toggleBdcHelper from "../../../provisioning/provisioningFlows/enableBdcToggles";
import { ProvisioningActions } from "../../../provisioning/types";
import { RepositoryOnboardingClient } from "../../../repository/client/repositoryOnboardingClient";
import { RequestContext } from "../../../repository/security/requestContext";
import { TenantMetadataBuilder } from "../../provisioning/tenantProvisioning/TenantMetadataBuilder";

chai.use(sinonChai);
chai.use(chaiAsPromised);

describe("service/bdc/provisioning/BdcTenantProvisioning", function () {
  const context = RequestContext.createFromTenantId(testTenantUuid);
  const sandbox = sinon.createSandbox();
  const notBdcErrorMsg = "Not a BDC tenant.";
  let getHistogramStub: sinon.SinonStub;
  let createTenantHistogramStub: sinon.SinonStubbedInstance<HistogramInstrument>;
  let ffStub, ffSetStub: SinonStub;
  let makeTenantMetadataBuilder: TenantMetadataBuilder;
  let repositoryOnboardingClientStub: SinonStub;

  beforeEach(async function () {
    sandbox.stub(TimeUnit.prototype, "toMillis").returns(0);
    sandbox.stub(RequestContext, "createFromTenantId").returns(context);
    repositoryOnboardingClientStub = sandbox.stub(RepositoryOnboardingClient, "handleOnboarding");
    repositoryOnboardingClientStub.resolves();
    sandbox.stub(migrationUltils, "reply").resolves();
    getHistogramStub = sandbox.stub(FtcTelemetryGetter.prototype, "getHistogram");
    createTenantHistogramStub = sandbox.createStubInstance(HistogramInstrument);
    ffStub = sandbox.stub(FeatureFlagProvider, "getFeatureFlags");
    ffSetStub = sandbox.stub(FeatureFlagProvider, "setFeatureflag");

    sandbox.useFakeTimers({
      now: new Date(2024, 9, 1, 0, 0),
      shouldAdvanceTime: false,
      toFake: ["Date"],
    });

    makeTenantMetadataBuilder = new TenantMetadataBuilder();
  });

  afterEach(function () {
    sandbox.restore();
  });

  function makeProvisioningMessage(
    action: ProvisioningActions,
    tenantMetadata: any,
    oldTenantMetadata?: any
  ): ITenantProvisioningMessage {
    return {
      context,
      tenantUuid: testTenantUuid,
      collaborators: ["dwaas-core"],
      dataAny: {
        action,
        tenantMetadata,
        oldTenantMetadata,
      },
    };
  }

  describe("BDC tenant update", function () {
    /**
     * At moment there is no Update actions to be taken
     * during tenant update, so we just skip it.
     */

    it("Should send skip message if update method is called.", async function () {
      const skipSpy = sandbox.spy(BdcTenantProvisioning.prototype, "sendSkipMessage" as any);
      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantType(TenantType.BDC)
        .withTenantClassification(TenantClassification.BDCC)
        .withTenantLicense({
          thresholdBusinessDataCloudUser: "1",
        })
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new BdcTenantProvisioning(context, message);

      await tenantProvisioning.updateTenant();

      sinon.assert.calledOnce(skipSpy);
    });
  });

  describe("BDC tenant creation", function () {
    function assertIfRepositoryOnboardingIsNotCalled(repositoryOnboardingClientStub: SinonStub) {
      sinon.assert.notCalled(repositoryOnboardingClientStub);
    }
    beforeEach(() => {
      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });
    });

    it("Should call repository onboarding during tenant creation.", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantType(TenantType.BDC)
        .withTenantClassification(TenantClassification.BDCC)
        .withTenantLicense({
          thresholdBusinessDataCloudUser: "1",
        })
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new BdcTenantProvisioning(context, message);

      await tenantProvisioning.createTenant();

      sinon.assert.calledOnce(repositoryOnboardingClientStub);
    });

    it("Should throw if repository onboarding fails.", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantType(TenantType.BDC)
        .withTenantClassification(TenantClassification.BDCC)
        .withTenantLicense({
          thresholdBusinessDataCloudUser: "1",
        })
        .build();

      repositoryOnboardingClientStub.throws(new Error("Repository failed"));

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new BdcTenantProvisioning(context, message);

      await expect(tenantProvisioning.createTenant()).to.be.rejectedWith("Repository failed");
    });

    it("Should throw if not BDC type tenant.", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantType(TenantType.DWC)
        .withTenantLicense({
          thresholdBusinessDataCloudUser: "1",
        })
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new BdcTenantProvisioning(context, message);

      await expect(tenantProvisioning.createTenant()).to.be.rejectedWith(notBdcErrorMsg);
    });

    it("Should throw if has no thresholdBusinessDataCloudUser license.", async function () {
      const tenantMetadata = makeTenantMetadataBuilder.build();
      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new BdcTenantProvisioning(context, message);

      await expect(tenantProvisioning.createTenant()).to.be.rejectedWith(notBdcErrorMsg);
    });

    it("Should throw if has thresholdDatawarehouseCloudUser license.", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantType(TenantType.BDC)
        .withTenantClassification(TenantClassification.BDCC)
        .withTenantLicense({
          thresholdDataWarehouseCloudUser: "1",
        })
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new BdcTenantProvisioning(context, message);

      await expect(tenantProvisioning.createTenant()).to.be.rejectedWith(notBdcErrorMsg);
    });

    it("Should not execute repository onboarding if is not BDC tenant", async function () {
      const tenantMetadata = makeTenantMetadataBuilder.withTenantType(TenantType.DWC).build();
      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new BdcTenantProvisioning(context, message);

      await expect(tenantProvisioning.createTenant()).to.be.rejected;
      assertIfRepositoryOnboardingIsNotCalled(repositoryOnboardingClientStub);
    });

    it("Should send tenant details to Dynatrace when tenant creation from BDC was successful", async () => {
      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantType(TenantType.BDC)
        .withTenantClassification(TenantClassification.BDCC)
        .withTenantLicense({
          thresholdBusinessDataCloudUser: "1",
        })
        .build();

      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantFromBDC = new BdcTenantProvisioning(context, message);

      getHistogramStub
        .withArgs(FTC_TELEMETRY_METRICS.FTC_WORKFLOW_PROVISIONING_CREATION)
        .returns(createTenantHistogramStub);

      await tenantFromBDC.createTenant();

      const expectValue = {
        tenantId: tenantMetadata.uuid,
        tenantClassification: tenantMetadata.classification,
        requestOrigin: "S4M",
        requestStatus: OperationResult.SUCCESS,
        error: "",
      };

      expect(createTenantHistogramStub.record).to.have.been.calledWith(0, expectValue);
    });

    it("Should send tenant details to Dynatrace when tenant creation from BDC was failed", async () => {
      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantType(TenantType.BDC)
        .withTenantClassification(TenantClassification.BDCC)
        .withTenantLicense({
          thresholdBusinessDataCloudUser: "1",
        })
        .build();

      ffStub.resolves({ DWCO_LS_MONITORING_PROVISIONING: true });

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantFromBDC = new BdcTenantProvisioning(context, message);

      getHistogramStub
        .withArgs(FTC_TELEMETRY_METRICS.FTC_WORKFLOW_PROVISIONING_CREATION)
        .returns(createTenantHistogramStub);

      repositoryOnboardingClientStub.rejects();

      await expect(tenantFromBDC.createTenant()).to.be.rejected;

      const expectValue = {
        tenantId: tenantMetadata.uuid,
        tenantClassification: tenantMetadata.classification,
        requestOrigin: "S4M",
        requestStatus: OperationResult.FAILED,
        error: "BDC tenant configuration failed",
      };

      expect(createTenantHistogramStub.record).to.have.been.calledWith(0, expectValue);
    });
  });

  describe("Enable BDC feature flags", function () {
    it("Should enabled DWCO_BDC and DWCO_BDC_GA feature flags for BDCC Classification.", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantType(TenantType.BDC)
        .withTenantClassification(TenantClassification.BDCC)
        .withTenantLicense({
          thresholdBusinessDataCloudUser: "1",
        })
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new BdcTenantProvisioning(context, message);

      ffStub.resolves({ DWCO_BDC: false, DWCO_BDC_GA: false });

      await tenantProvisioning.createTenant();

      expect(ffSetStub).to.be.callCount(2);
      expect(ffSetStub).to.be.calledWith(context, "DWCO_BDC", true);
      expect(ffSetStub).to.be.calledWith(context, "DWCO_BDC_GA", true);
    });

    it("Should enabled DWCO_BDC and DWCO_BDC_GA feature flags for BDCC_INTERNAL Classification.", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantType(TenantType.BDC)
        .withTenantClassification(TenantClassification.BDCC_INTERNAL)
        .withTenantLicense({
          thresholdBusinessDataCloudUser: "1",
        })
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new BdcTenantProvisioning(context, message);

      ffStub.resolves({ DWCO_BDC: false, DWCO_BDC_GA: false });

      await tenantProvisioning.createTenant();

      expect(ffSetStub).to.be.callCount(2);
      expect(ffSetStub).to.be.calledWith(context, "DWCO_BDC", true);
      expect(ffSetStub).to.be.calledWith(context, "DWCO_BDC_GA", true);
    });

    it("Should skip DWCO_BDC and DWCO_BDC_GA feature flags for other classifications.", async function () {
      const classificationsToSkip = [
        TenantClassification.DWC,
        TenantClassification.DWCPoC,
        TenantClassification.DWCinternal,
        TenantClassification.DWCinternalProductive,
        TenantClassification.DWCCPEA,
        TenantClassification.DWCcustomerTest,
        TenantClassification.DWCpartnerTest,
      ];

      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantLicense({
          thresholdBusinessDataCloudUser: "1",
        })
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new BdcTenantProvisioning(context, message);

      for (const classification of classificationsToSkip) {
        tenantMetadata.classification = classification;
        await tenantProvisioning.enableBdcToggles(tenantMetadata as ITenantInformation);
      }
      expect(ffSetStub).to.not.be.called;
    });

    it("Should not throw if DWCO_BDC and DWCO_BDC_GA feature flags fail to be enabled.", async function () {
      const tenantMetadata = makeTenantMetadataBuilder
        .withTenantType(TenantType.BDC)
        .withTenantClassification(TenantClassification.BDCC)
        .withTenantLicense({
          thresholdBusinessDataCloudUser: "1",
        })
        .build();

      const message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      const tenantProvisioning = new BdcTenantProvisioning(context, message);

      ffStub.resolves({ DWCO_BDC: false, DWCO_BDC_GA: false });
      ffSetStub.throws(new Error("FF Failed"));
      sandbox.stub(BasicProvisioning.prototype, "enableBdcTogglesWithPersist" as any).throws();
      await expect(tenantProvisioning.createTenant()).to.be.fulfilled;
    });
  });

  describe("TenantProvisioningConsumer for BDC", () => {
    let bdcProvStub, provStub, messageEvent;

    const decodedMsg = {
      context: "dummy",
      tenantUuid: "dummy",
      tenantName: "dummy",
      dataAny: {
        action: ProvisioningActions.UPDATE,
        tenantMetadata: {
          type: TenantType.DWC,
          uuid: "dummy",
          license: { dummy: false },
        },
        extraParams: { fakeProp: true },
        oldTenantMetadata: {
          uuid: "dummy",
          license: { dummy: false },
        },
      },
    };

    beforeEach(() => {
      bdcProvStub = sandbox.stub(BdcTenantProvisioning.prototype, "updateTenant").resolves();
      provStub = sandbox.stub(TenantProvisioning.prototype, "updateTenant").resolves();
      messageEvent = {
        acceptMessage: sandbox.stub().returns(undefined),
        rejectMessage: sandbox.stub().returns(undefined),
        releaseMessage: sandbox.stub().returns(undefined),
      } as unknown as MessageEvent;
      sandbox.stub(httpClient, "call").resolves();
    });

    this.afterEach(() => {
      sandbox.restore();
    });

    it("TenantProvisioningConsumer should call BdcTenantProvisioning if tenant type is BDC", async () => {
      const provisioning = new TenantProvisioningConsumer();
      const message = { body: { content: Buffer.from("dummy") } } as any;
      messageEvent.getMessage = sandbox.stub().returns(message);

      decodedMsg.dataAny.tenantMetadata.type = TenantType.BDC;
      sandbox.stub(TenantProvisioningConsumer.prototype, "decodeMessage").resolves(decodedMsg as any);

      await provisioning.consume(messageEvent);

      sinon.assert.calledOnce(bdcProvStub);
      sinon.assert.notCalled(provStub);
    });

    it("TenantProvisioningConsumer should NOT call BdcTenantProvisioning if tenant type is NOT BDC", async () => {
      const provisioning = new TenantProvisioningConsumer();
      const message = { body: { content: Buffer.from("dummy") } } as any;
      messageEvent.getMessage = sandbox.stub().returns(message);

      decodedMsg.dataAny.tenantMetadata.type = TenantType.DWC;
      sandbox.stub(TenantProvisioningConsumer.prototype, "decodeMessage").resolves(decodedMsg as any);

      await provisioning.consume(messageEvent);

      sinon.assert.calledOnce(provStub);
      sinon.assert.notCalled(bdcProvStub);
    });
  });

  describe("Persist BDC toggle enablement", () => {
    let isFeatureActiveStub: SinonStub;
    let tenantMetadata: any;
    let message: any;
    let tenantProvisioning: any;

    beforeEach(function () {
      isFeatureActiveStub = sandbox.stub(FeatureFlagProvider, "isFeatureActive");

      tenantMetadata = makeTenantMetadataBuilder
        .withTenantType(TenantType.BDC)
        .withTenantClassification(TenantClassification.BDCC)
        .withTenantLicense({
          thresholdBusinessDataCloudUser: "1",
        })
        .build();

      message = makeProvisioningMessage(ProvisioningActions.CREATE, tenantMetadata);
      tenantProvisioning = new BdcTenantProvisioning(context, message);
    });

    it("Should persist DWCO_BDC and DWCO_BDC_GA enablement and succeed at second try.", async function () {
      isFeatureActiveStub.onFirstCall().throws();
      isFeatureActiveStub.resolves(false);

      ffSetStub.resolves(true);

      const toggleSpy = sandbox.spy(toggleBdcHelper, "enableBdcTogglesForProvisioning");
      await tenantProvisioning.enableBdcToggles(tenantMetadata as ITenantInformation);

      expect(toggleSpy).to.callCount(2);
      expect(isFeatureActiveStub).to.callCount(3);
      expect(ffSetStub).to.be.calledWith(context, "DWCO_BDC", true);
      expect(ffSetStub).to.be.calledWith(context, "DWCO_BDC_GA", true);
    });

    it("Should persist DWCO_BDC and DWCO_BDC_GA at maximum 6 times.", async function () {
      isFeatureActiveStub.throws();

      const toggleSpy = sandbox.spy(toggleBdcHelper, "enableBdcTogglesForProvisioning");
      await tenantProvisioning.enableBdcToggles(tenantMetadata as ITenantInformation);

      expect(ffSetStub).to.not.be.called;
      expect(toggleSpy).to.callCount(6);
    });
  });
});
