/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import chai, { assert, expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import sinon, { SinonStub } from "sinon";
import { PackageInstallationValidator } from "../../../service/bdc/packages/validation/packageInstallationValidator";
import { MISSING_ENTITLEMENT } from "../../../shared/bdccockpit/Constants";
import { BDCPackageCategory, BDCPackageType } from "../../../shared/bdccockpit/Enums";
import { BDCComponent, BDCRequestContext } from "../../../shared/bdccockpit/Types";
import { FormationService } from "../../bdc/formation/formationService";
import { InstallersFacade } from "../../bdc/installers/installersFacade";
import * as componentResolverModule from "../../bdc/lifecycleManagement/componentResolver";
import { GraphAction } from "../../bdc/lifecycleManagement/componentResolver";
import { InstallationManager } from "../../bdc/lifecycleManagement/installationManager";
import { UninstallationManager } from "../../bdc/lifecycleManagement/uninstallationManager";
import { PackageInstallationService } from "../../bdc/packageInstallation/packageInstallationService";
import {
  BDCPackageInstallation,
  ComponentInstallationStatus,
  InstallationStatusCode,
} from "../../bdc/packageInstallation/packageInstallationTypes";
import { PackagesService } from "../../bdc/packages/packagesService";
import { BDCPackage, BDCPackageComponent } from "../../bdc/packages/packagesTypes";
import {
  dspApplicationNamespace,
  dspProvider,
  hcmApplicationNamespace,
  hcmProvider,
  s4ApplicationNamespace,
  s4Provider,
  sacApplicationNamespace,
  sacProvider,
} from "../../bdc/systems/applicationNamespaceUtils";
import { SystemsService } from "../../bdc/systems/systemsService";
import { BDCException, BDCExceptionType } from "../../bdc/utils/BDCException";
import { notificationsHelper } from "../../bdc/utils/notificationUtils";
import * as solaceUtilsModule from "../../bdc/utils/solaceUtils";
import { InstallationMessage, MessageAction } from "../../messageQueuing/consumer/PackageInstallationConsumer";
import { RequestContext } from "../../repository/security/requestContext";
import { FeatureFlagUtilsBDC } from "./utils/featureFlagUtilsBDC";

chai.use(chaiAsPromised);

const CREATE_CONTEXT_OPTS = {
  spanContext: { traceId: "123", spanId: "456", traceFlags: 0 },
};

const mockContext: BDCRequestContext = RequestContext.createFromTenantId("1", CREATE_CONTEXT_OPTS);
mockContext.isFeatureFlagActive = async function (ff: string) {
  if (ff === "DWCO_BDC_PACKAGE_CONTENT_DEV_SUPPORT") {
    return false;
  }
  if (ff === "DWCO_IA_INSTALLATION_CATALOG_DATAPRODUCT_STATUS_CHECK") {
    return true;
  }
  return true;
};
const mockFeatureFlagUtils = new FeatureFlagUtilsBDC();
mockFeatureFlagUtils.addFlag("DWCO_BDC_GA", false);
mockFeatureFlagUtils.addFlag("DWCO_IA_INSTALLATION_CATALOG_DATAPRODUCT_STATUS_CHECK", true);
mockFeatureFlagUtils.applyToContext(mockContext);

const testInstallationId = "testInstallationId1";

const packageInstallationInitial: BDCPackageInstallation = {
  originalPackage: {
    id: "dde76823-bd53-4bdf-9cca-63a37a1fec47",
    version: "1.0.0",
    name: "OurVeryFirstTry",
    description: "Common mark long text",
    shortDescription: "Plain text short description, 255 characters",
    preview: [{ url: "preview url" }],
    type: BDCPackageType.INSIGHT_APPLICATION,
    category: BDCPackageCategory.FINANCE,
    applicationComponent: "FIN-AB-CDE",
    schemaName: "bdcPackage",
    schemaVersion: "1.0.0",
    fileProducer: "BDCDesigner",
    requiredApplications: [
      {
        provider: s4Provider,
        applications: [
          {
            category: "application",
            minVersion: "24.09",
            applicationNamespace: s4ApplicationNamespace,
          },
          {
            category: "application",
            minVersion: "21.03",
            applicationNamespace: s4ApplicationNamespace,
          },
        ],
      },
    ],
    components: [
      {
        componentId: "c25cb6bc-256e-4076-8898-b7dcc6ba4119",
        category: "CnPackage",
        provider: sacProvider,
        name: "SAC_App1",
        version: "1.1.0",
        description: "",
        requires: [
          {
            category: "CnPackage",
            provider: dspProvider,
            name: "DSP_App1_Models",
            minVersion: "1.0.0",
          },
        ],
      },
      {
        componentId: "2d914b05-8847-4eaa-83f2-e34cfb62efc6",
        category: "CnPackage",
        provider: dspProvider,
        name: "DSP_App1_Models",
        version: "1.1.0",
        description: "",
        requires: [
          {
            category: "DataProduct",
            provider: s4Provider,
            ordid: "sap.s4.dataProduct:DP_A",
            minVersion: "1.0.0",
          },
          {
            category: "DataProduct",
            provider: s4Provider,
            ordid: "sap.s4.dataProduct:DP_B",
            minVersion: "1.0.0",
          },
        ],
      },
      {
        componentId: "7f5ec360-96fa-4839-89e4-12510e0319a4",
        category: "DataProduct",
        provider: s4Provider,
        ordid: "sap.s4.dataProduct:DP_A",
        version: "1.0.0",
        name: "Liquidity & Cash",
        description: "The best Liquidity & Cash product",
        numberOfEntities: 33,
      },
      {
        componentId: "71dfca4b-22e3-4400-8f55-6d7258f1126e",
        category: "DataProduct",
        provider: s4Provider,
        ordid: "sap.s4.dataProduct:DP_B",
        version: "1.0.0",
        name: "Inventory",
        numberOfEntities: 13,
        description: "The best Inventory product",
      },
    ],
  },
  tenantId: "testTenantId1",
  installationId: testInstallationId,
  correlationId: "correlationId_TEST_2",
  installationCyclesCount: -1,
  installationProperties: [
    {
      applicationNamespace: dspApplicationNamespace,
      systemTenant: "DSPtenant",
      systemName: "MySDSP",
    },
    {
      applicationNamespace: sacApplicationNamespace,
      systemTenant: "SACtenant",
      systemName: "MySAC",
    },
    {
      applicationNamespace: s4ApplicationNamespace,
      systemTenant: "S4tenant",
      systemName: "MyS4",
    },
  ],
  componentsInstallationStatus: [
    {
      componentId: "c25cb6bc-256e-4076-8898-b7dcc6ba4119",
      componentInstallationDetails: { importJobId: "" },
      startTime: new Date(),
      endTime: new Date(),
      status: "PENDING",
      message: "message1",
      retryCount: 0,
    },
    {
      componentId: "2d914b05-8847-4eaa-83f2-e34cfb62efc6",
      componentInstallationDetails: { importJobId: "" },
      startTime: new Date(),
      endTime: new Date(),
      status: "PENDING",
      message: "message2",
      retryCount: 0,
    },
    {
      componentId: "7f5ec360-96fa-4839-89e4-12510e0319a4",
      componentInstallationDetails: { importJobId: "1104" },
      startTime: new Date(),
      endTime: new Date(),
      status: "PENDING",
      message: "message3",
      retryCount: 0,
    },
    {
      componentId: "71dfca4b-22e3-4400-8f55-6d7258f1126e",
      componentInstallationDetails: { importJobId: "1099" },
      startTime: new Date(),
      endTime: new Date(),
      status: "PENDING",
      message: "message4",
      retryCount: 0,
    },
  ],
} as BDCPackageInstallation;

describe("bdc Installation manager tests", function () {
  let catalogServiceGetPackageStub: sinon.SinonStub;
  let catalogServiceUpdatePackageStub: sinon.SinonStub;
  let systemServiceInitAllSystemsStub: sinon.SinonStub;

  let installersFacadeStubGetStatus: sinon.SinonStub;

  let sendSolacePackageInstallationMessageMock: sinon.SinonStub;
  let getNextComponentsToInstallMock: sinon.SinonStub;

  beforeEach(() => {
    catalogServiceUpdatePackageStub = sinon.stub(PackageInstallationService, "updatePackageInstallation");
    systemServiceInitAllSystemsStub = sinon.stub(SystemsService, "initiateAllSystemsCache");
    systemServiceInitAllSystemsStub.resolves(undefined);
    sinon.stub(FormationService, "doFormationRefresh");
    sinon.stub(notificationsHelper as any, "sendNotification").resolves();
    sinon.stub(notificationsHelper as any, "sendNotificationForTechUser").resolves();
    const installersFacadeInstance = InstallersFacade.getInstance();
    sinon.stub(installersFacadeInstance, "install").returns(Promise.resolve({ importJobId: "12345" }));
    installersFacadeStubGetStatus = sinon.stub(installersFacadeInstance, "getStatus");
    sinon.stub(installersFacadeInstance, "isReadyToInstall").returns(Promise.resolve(false));
    (InstallationManager as any).installersFacade = installersFacadeInstance;
    (UninstallationManager as any).installersFacade = installersFacadeInstance;

    sendSolacePackageInstallationMessageMock = sinon.stub(solaceUtilsModule, "sendPackageInstallationMessage");
    getNextComponentsToInstallMock = sinon.stub(componentResolverModule, "getNextComponents");
    sinon.stub(SystemsService, "checkFosConnection").resolves(undefined);
  });

  afterEach(() => {
    sinon.restore();
    packageInstallationInitial.installationCyclesCount = -1;
  });

  after(() => {
    sinon.restore();
  });

  const getInstallationMessage = (context: BDCRequestContext, componentIds: string[]): InstallationMessage => ({
    installationId: testInstallationId,
    installationCyclesCount: 0,
    componentIds,
    action: MessageAction.INSTALL,
    tenantId: context.tenantId!,
    userId: context.userInfo.userId!,
    userName: context.userInfo.userName!,
    correlationId: context.correlationId || "",
    traceId: context.spanContext?.traceId ?? "",
  });

  it("should throw FeatureFlagOffException if the feature flag is off", async () => {
    catalogServiceGetPackageStub = sinon
      .stub(PackageInstallationService, "getPackageInstallation")
      .resolves(packageInstallationInitial);
    sinon.stub(require("../../bdc/entitlement/entitlementValidator"), "allowedToInstall").resolves(true);
    const mockContextTest: BDCRequestContext = RequestContext.createFromTenantId("1", CREATE_CONTEXT_OPTS);
    const testFeatureFlagUtils = new FeatureFlagUtilsBDC();
    testFeatureFlagUtils.addFlag("DWCO_BDC_MANUAL_ONBOARDING_RETRY", false);
    testFeatureFlagUtils.applyToContext(mockContextTest);

    expect(await mockContextTest.isFeatureFlagActive("DWCO_BDC_MANUAL_ONBOARDING_RETRY")).to.be.false;
    const error = await expect(
      InstallationManager.retryPackage(mockContextTest, testInstallationId)
    ).to.be.rejectedWith(BDCException);
    expect(error.type).to.equal(BDCExceptionType.FeatureFlagOff);
  });

  it("should throw InstallationFailure if the package is already successfully installed", async () => {
    const testPackageInstallation: BDCPackageInstallation = JSON.parse(JSON.stringify(packageInstallationInitial));
    testPackageInstallation.componentsInstallationStatus.forEach((status) => (status.status = "DONE"));

    catalogServiceGetPackageStub = sinon
      .stub(PackageInstallationService, "getPackageInstallation")
      .returns(Promise.resolve(testPackageInstallation));

    expect(await mockContext.isFeatureFlagActive("DWCO_BDC_MANUAL_ONBOARDING_RETRY")).to.be.true;
    const error = await expect(InstallationManager.retryPackage(mockContext, testInstallationId)).to.be.rejectedWith(
      BDCException
    );
    expect(error.type).to.equal(BDCExceptionType.InstallationFailure);
    expect(error.message).to.contains(testPackageInstallation.installationId);
  });

  it("should retry package installation should reset all data", async () => {
    const testPackageInstallation: BDCPackageInstallation = JSON.parse(JSON.stringify(packageInstallationInitial));
    testPackageInstallation.componentsInstallationStatus[2].status = "DONE";
    testPackageInstallation.componentsInstallationStatus[3].status = "DONE";

    const originalComponentIds = testPackageInstallation.componentsInstallationStatus.map(
      (status) => status.componentId
    );

    catalogServiceGetPackageStub = sinon
      .stub(PackageInstallationService, "getPackageInstallation")
      .returns(Promise.resolve(testPackageInstallation));

    const deletePackageStub = sinon
      .stub(PackageInstallationService, "deletePackageInstallation")
      .returns(Promise.resolve());
    const validatePackageReinstallRequestStub = sinon
      .stub(PackageInstallationValidator, "validatePackageReinstallRequest")
      .returns(Promise.resolve());

    const doInstallPackageStub = sinon.stub(InstallationManager as any, "doInstallPackage").returns(undefined);
    await InstallationManager.retryPackage(mockContext, testInstallationId);

    sinon.assert.calledOnce(catalogServiceGetPackageStub);
    sinon.assert.calledOnce(deletePackageStub);
    sinon.assert.calledOnce(validatePackageReinstallRequestStub);
    sinon.assert.calledOnce(doInstallPackageStub);

    const callArgs = doInstallPackageStub.getCall(0).args;
    const packageInstallation = callArgs[1] as BDCPackageInstallation;
    expect(packageInstallation.endTime).to.be.undefined;
    expect(packageInstallation.installationCyclesCount).to.equal(-1);
    expect(packageInstallation.correlationId).to.not.equal(testPackageInstallation.correlationId);
    expect(packageInstallation.installationId).to.equal(testPackageInstallation.installationId);

    const componentInstallationStatuses = packageInstallation.componentsInstallationStatus;

    componentInstallationStatuses.forEach((componentInstallationStatus, index) => {
      expect(componentInstallationStatus.componentId).to.equal(originalComponentIds[index]);
      expect(componentInstallationStatus.retryCount).to.equal(0);
      expect(componentInstallationStatus.status).to.equal("PENDING");

      expect(componentInstallationStatus.endTime).to.be.undefined;
      expect(componentInstallationStatus.message).to.be.undefined;
    });

    deletePackageStub.restore();
    validatePackageReinstallRequestStub.restore();
    doInstallPackageStub.restore();
  });

  it("Call1 - All(4) components in status PENDING - start with last two", async () => {
    /** Prepare test proprietary mocks */
    // Set CatalogService to return initial package installation
    catalogServiceGetPackageStub = sinon
      .stub(PackageInstallationService, "getPackageInstallation")
      .returns(Promise.resolve(packageInstallationInitial));
    // Set CatalogService to test component installation statuses - first two statuses are still "PENDING", next two in "EXECUTING"
    catalogServiceUpdatePackageStub.callsFake(
      (packageInstallation: BDCPackageInstallation, context: BDCRequestContext) => {
        assert.equal(packageInstallation.componentsInstallationStatus[0].status, "PENDING");
        assert.equal(packageInstallation.componentsInstallationStatus[1].status, "PENDING");
        assert.equal(packageInstallation.componentsInstallationStatus[2].status, "WAITING_TO_INSTALL_READY");
        assert.equal(packageInstallation.componentsInstallationStatus[3].status, "WAITING_TO_INSTALL_READY");
      }
    );
    const context: BDCRequestContext = RequestContext.createFromTenantId("1", CREATE_CONTEXT_OPTS);
    // Set first installation cycle to start with two last components
    const installationMessage = getInstallationMessage(context, [
      packageInstallationInitial.componentsInstallationStatus[2].componentId,
      packageInstallationInitial.componentsInstallationStatus[3].componentId,
    ]);
    /** Make the tested method call */
    await InstallationManager.processInstallationMessage(mockContext, installationMessage);

    /** Validations */
    // Validate that we return same values, as they only moved to "EXECUTING" state
    sinon.assert.calledWith(sendSolacePackageInstallationMessageMock, mockContext, {
      action: MessageAction.INSTALL,
      tenantId: "1",
      userId: undefined,
      userName: undefined,
      installationId: testInstallationId,
      correlationId: context.correlationId,
      installationCyclesCount: 1,
      componentIds: [
        packageInstallationInitial.componentsInstallationStatus[2].componentId,
        packageInstallationInitial.componentsInstallationStatus[3].componentId,
      ],
      traceId: CREATE_CONTEXT_OPTS.spanContext.traceId,
    });
  });

  it("Call2 - two in status EXECUTING and two with PENDING", async () => {
    /** Prepare test proprietary mocks */
    // Set CatalogService to return initial package installation
    // And change status to EXECUTING as initial condition for current test
    const testPackageInstallation: BDCPackageInstallation = structuredClone(packageInstallationInitial);
    testPackageInstallation.componentsInstallationStatus[2].status = "EXECUTING";
    testPackageInstallation.componentsInstallationStatus[3].status = "EXECUTING";

    catalogServiceGetPackageStub = sinon
      .stub(PackageInstallationService, "getPackageInstallation")
      .returns(Promise.resolve(testPackageInstallation));

    // Make sure that when CatalogService update called - first two statuses are still "PENDING", next two in "DONE"
    catalogServiceUpdatePackageStub.callsFake(
      (packageInstallation: BDCPackageInstallation, context: BDCRequestContext) => {
        assert.equal(packageInstallation.componentsInstallationStatus[0].status, "PENDING");
        assert.equal(packageInstallation.componentsInstallationStatus[0].status, "PENDING");
        assert.equal(packageInstallation.componentsInstallationStatus[2].status, "DONE");
        assert.equal(packageInstallation.componentsInstallationStatus[3].status, "DONE");
      }
    );
    // InstallerFacade is called to check the Executing components status, set it to return status "DONE"
    installersFacadeStubGetStatus.callsFake(
      (
        context: BDCRequestContext,
        originalComponent: BDCPackageComponent,
        componentInstallationStatus: ComponentInstallationStatus
      ) => {
        const newComponentInstallationStatus: ComponentInstallationStatus = {
          componentInstallationDetails: componentInstallationStatus.componentInstallationDetails,
          componentId: componentInstallationStatus.componentId,
          startTime: componentInstallationStatus.startTime,
          endTime: componentInstallationStatus.endTime,
          status: "DONE",
          message: componentInstallationStatus.message,
          retryCount: componentInstallationStatus.retryCount,
        };
        return newComponentInstallationStatus;
      }
    );

    // Prepare componentResolver to return next set of Ids when called
    getNextComponentsToInstallMock.callsFake(
      (
        components: BDCComponent[],
        componentsInstallationStatus: ComponentInstallationStatus[],
        graphAction: GraphAction
      ) => [
        { componentId: testPackageInstallation.componentsInstallationStatus[0].componentId },
        { componentId: testPackageInstallation.componentsInstallationStatus[1].componentId },
      ]
    );
    const installationMessage = getInstallationMessage(mockContext, [
      testPackageInstallation.componentsInstallationStatus[2].componentId,
      testPackageInstallation.componentsInstallationStatus[3].componentId,
    ]);
    /** Make the tested method call */
    await InstallationManager.processInstallationMessage(mockContext, installationMessage);

    /** Validations */
    // Validate that we return next set of values as returned from  as returned from componentResolver, as current(index: 2 and 3) all DONE
    sinon.assert.calledWith(sendSolacePackageInstallationMessageMock, mockContext, {
      action: MessageAction.INSTALL,
      userName: undefined,
      tenantId: "1",
      userId: undefined,
      installationId: testInstallationId,
      correlationId: mockContext.correlationId,
      installationCyclesCount: 1,
      componentIds: [
        testPackageInstallation.componentsInstallationStatus[0].componentId,
        testPackageInstallation.componentsInstallationStatus[1].componentId,
      ],
      traceId: CREATE_CONTEXT_OPTS.spanContext.traceId,
    });
  });
  it("Call3 - two in status EXECUTING and two with DONE - one failing first time", async () => {
    /** Prepare test proprietary mocks */
    // Set CatalogService to return initial package installation
    // And change status to initial condition for current test
    const testPackageInstallation: BDCPackageInstallation = structuredClone(packageInstallationInitial);
    testPackageInstallation.componentsInstallationStatus[0].status = "EXECUTING";
    testPackageInstallation.componentsInstallationStatus[1].status = "EXECUTING";
    testPackageInstallation.componentsInstallationStatus[2].status = "DONE";
    testPackageInstallation.componentsInstallationStatus[3].status = "DONE";

    catalogServiceGetPackageStub = sinon
      .stub(PackageInstallationService, "getPackageInstallation")
      .returns(Promise.resolve(testPackageInstallation));

    // Make sure that when CatalogService update called - first two statuses are still "PENDING", next two in "DONE"
    catalogServiceUpdatePackageStub.callsFake(
      (packageInstallation: BDCPackageInstallation, context: BDCRequestContext) => {
        assert.equal(packageInstallation.componentsInstallationStatus[0].status, "DONE");
        assert.equal(packageInstallation.componentsInstallationStatus[1].status, "ERROR");
        assert.equal(packageInstallation.componentsInstallationStatus[2].status, "DONE");
        assert.equal(packageInstallation.componentsInstallationStatus[3].status, "DONE");
      }
    );
    // InstallerFacade is called to check the Executing components status, set it to return status "DONE" for
    // first component and FAILED to the second
    installersFacadeStubGetStatus.callsFake(
      (
        context: BDCRequestContext,
        originalComponent: BDCPackageComponent,
        componentInstallationStatus: ComponentInstallationStatus
      ) => {
        const newComponentInstallationStatus: Partial<ComponentInstallationStatus> = {
          componentInstallationDetails: componentInstallationStatus.componentInstallationDetails,
          componentId: componentInstallationStatus.componentId,
          startTime: componentInstallationStatus.startTime,
          endTime: componentInstallationStatus.endTime,
          message: componentInstallationStatus.message,
          retryCount: 0,
        };
        let newStatus: InstallationStatusCode;
        if (originalComponent.componentId === testPackageInstallation.componentsInstallationStatus[0].componentId) {
          newStatus = "DONE";
        } else {
          newStatus = "ERROR";
          newComponentInstallationStatus.retryCount = 1;
        }
        newComponentInstallationStatus.status = newStatus;
        return newComponentInstallationStatus as ComponentInstallationStatus;
      }
    );

    const installationMessage = getInstallationMessage(mockContext, [
      testPackageInstallation.componentsInstallationStatus[0].componentId,
      testPackageInstallation.componentsInstallationStatus[1].componentId,
    ]);
    /** Make the tested method call */
    await InstallationManager.processInstallationMessage(mockContext, installationMessage);

    /** Validations */
    // Validate that we return next set of values as returned from  as returned from componentResolver, as current(index: 2 and 3) all DONE
    sinon.assert.calledWith(sendSolacePackageInstallationMessageMock, mockContext, {
      action: MessageAction.INSTALL,
      userName: undefined,
      tenantId: "1",
      userId: undefined,
      installationId: testInstallationId,
      correlationId: mockContext.correlationId,
      installationCyclesCount: 1,
      componentIds: [testPackageInstallation.componentsInstallationStatus[1].componentId],
      traceId: CREATE_CONTEXT_OPTS.spanContext.traceId,
    });
  });
  it("Call3.5 - two in status EXECUTING and two with DONE - one failing first time", async () => {
    /** Prepare test proprietary mocks */
    // Set CatalogService to return initial package installation
    // And change status to initial condition for current test
    const testPackageInstallation: BDCPackageInstallation = structuredClone(packageInstallationInitial);
    testPackageInstallation.componentsInstallationStatus[0].status = "PENDING";
    testPackageInstallation.componentsInstallationStatus[1].status = "PENDING";
    testPackageInstallation.componentsInstallationStatus[2].status = "DONE";
    testPackageInstallation.componentsInstallationStatus[3].status = "DONE";

    catalogServiceGetPackageStub = sinon
      .stub(PackageInstallationService, "getPackageInstallation")
      .returns(Promise.resolve(testPackageInstallation));

    // Make sure that when CatalogService update called - first two statuses are still "PENDING", next two in "DONE"
    catalogServiceUpdatePackageStub.callsFake(
      (packageInstallation: BDCPackageInstallation, context: BDCRequestContext) => {
        assert.equal(packageInstallation.componentsInstallationStatus[0].status, "WAITING_TO_INSTALL_READY");
        assert.equal(packageInstallation.componentsInstallationStatus[1].status, "WAITING_TO_INSTALL_READY");
        assert.equal(packageInstallation.componentsInstallationStatus[2].status, "DONE");
        assert.equal(packageInstallation.componentsInstallationStatus[3].status, "DONE");
      }
    );
    // InstallerFacade is called to check the Executing components status, set it to return status "DONE" for
    // first component and FAILED to the second
    installersFacadeStubGetStatus.callsFake(
      (
        context: BDCRequestContext,
        originalComponent: BDCPackageComponent,
        componentInstallationStatus: ComponentInstallationStatus
      ) => {
        const newComponentInstallationStatus: Partial<ComponentInstallationStatus> = {
          componentInstallationDetails: componentInstallationStatus.componentInstallationDetails,
          componentId: componentInstallationStatus.componentId,
          startTime: componentInstallationStatus.startTime,
          endTime: componentInstallationStatus.endTime,
          message: componentInstallationStatus.message,
          retryCount: 0,
        };
        let newStatus: InstallationStatusCode;
        if (originalComponent.componentId === testPackageInstallation.componentsInstallationStatus[0].componentId) {
          newStatus = "DONE";
        } else {
          newStatus = "ERROR";
          newComponentInstallationStatus.retryCount = 1;
        }
        newComponentInstallationStatus.status = newStatus;
        return newComponentInstallationStatus as ComponentInstallationStatus;
      }
    );

    const installationMessage = getInstallationMessage(mockContext, [
      testPackageInstallation.componentsInstallationStatus[0].componentId,
      testPackageInstallation.componentsInstallationStatus[1].componentId,
    ]);
    /** Make the tested method call */
    await InstallationManager.processInstallationMessage(mockContext, installationMessage);

    /** Validations */
    // Validate that we return next set of values as returned from  as returned from componentResolver, as current(index: 2 and 3) all DONE
    sinon.assert.calledWith(sendSolacePackageInstallationMessageMock, mockContext, {
      action: MessageAction.INSTALL,
      userName: undefined,
      tenantId: "1",
      userId: undefined,
      installationId: testInstallationId,
      correlationId: mockContext.correlationId,
      installationCyclesCount: 1,
      componentIds: [
        testPackageInstallation.componentsInstallationStatus[0].componentId,
        testPackageInstallation.componentsInstallationStatus[1].componentId,
      ],
      traceId: CREATE_CONTEXT_OPTS.spanContext.traceId,
    });
  });
  it("Call4 - two in status EXECUTING and two with DONE - one failing for the third time", async () => {
    /** Prepare test proprietary mocks */
    // Set CatalogService to return initial package installation
    // And change status to initial condition for current test
    const testPackageInstallation: BDCPackageInstallation = structuredClone(packageInstallationInitial);
    testPackageInstallation.componentsInstallationStatus[0].status = "EXECUTING";
    testPackageInstallation.componentsInstallationStatus[1].status = "ERROR";
    testPackageInstallation.componentsInstallationStatus[1].retryCount = 3;
    testPackageInstallation.componentsInstallationStatus[2].status = "DONE";
    testPackageInstallation.componentsInstallationStatus[3].status = "DONE";

    catalogServiceGetPackageStub = sinon
      .stub(PackageInstallationService, "getPackageInstallation")
      .returns(Promise.resolve(testPackageInstallation));

    // Make sure that when CatalogService update called - first two statuses are still "PENDING", next two in "DONE"
    catalogServiceUpdatePackageStub.callsFake(
      (packageInstallation: BDCPackageInstallation, context: BDCRequestContext) => {
        assert.equal(packageInstallation.componentsInstallationStatus[0].status, "DONE");
        assert.equal(packageInstallation.componentsInstallationStatus[1].status, "FAILED");
        assert.equal(packageInstallation.componentsInstallationStatus[2].status, "DONE");
        assert.equal(packageInstallation.componentsInstallationStatus[3].status, "DONE");
      }
    );
    // InstallerFacade is called to check the Executing components status, set it to return status "DONE" for
    // first component and FAILED to the second
    installersFacadeStubGetStatus.callsFake(
      (
        context: BDCRequestContext,
        originalComponent: BDCPackageComponent,
        componentInstallationStatus: ComponentInstallationStatus
      ) => {
        const newComponentInstallationStatus: Partial<ComponentInstallationStatus> = {
          componentInstallationDetails: componentInstallationStatus.componentInstallationDetails,
          componentId: componentInstallationStatus.componentId,
          startTime: componentInstallationStatus.startTime,
          endTime: componentInstallationStatus.endTime,
          message: componentInstallationStatus.message,
          retryCount: 0,
        };
        let newStatus: InstallationStatusCode;
        if (originalComponent.componentId === testPackageInstallation.componentsInstallationStatus[0].componentId) {
          newStatus = "DONE";
        } else {
          newStatus = "ERROR";
          newComponentInstallationStatus.retryCount = 3;
        }
        newComponentInstallationStatus.status = newStatus;
        return newComponentInstallationStatus as ComponentInstallationStatus;
      }
    );

    const installationMessage = getInstallationMessage(mockContext, [
      testPackageInstallation.componentsInstallationStatus[0].componentId,
      testPackageInstallation.componentsInstallationStatus[1].componentId,
    ]);
    /** Make the tested method call */
    await InstallationManager.processInstallationMessage(mockContext, installationMessage);

    /** Validations */
    // Validate that we after failure, next iteration never called
    sinon.assert.notCalled(sendSolacePackageInstallationMessageMock);
  });
});
describe("installationManager", () => {
  let catalogServiceGetPackageStub: SinonStub;
  let mockContextRetry: any;
  let validatePackageReinstallRequestStub: SinonStub;
  let doInstallPackageStub: SinonStub;

  beforeEach(() => {
    // TODO: this test file has a very weird structure. delete this restore and fix the structure
    sinon.restore();
    catalogServiceGetPackageStub = sinon.stub(PackageInstallationService, "getPackageInstallation");
    validatePackageReinstallRequestStub = sinon.stub(PackageInstallationValidator, "validatePackageReinstallRequest");
    // TODO: I am not retroactively testing all features in the file right now. these should be removed and proper mocking of only outside libs added
    doInstallPackageStub = sinon.stub(InstallationManager as any, "doInstallPackage").resolves("installationId");
    sinon.stub(InstallationManager as any, "doDeletePackageInstallation");
    mockContextRetry = {
      isFeatureFlagActive: (ff) => Promise.resolve(ff === "DWCO_BDC_MANUAL_ONBOARDING_RETRY"),
    };
  });

  after(() => {
    sinon.restore();
  });

  describe("installPackage", () => {
    let packagesProviderStub: SinonStub;

    beforeEach(() => {
      packagesProviderStub = sinon.stub(PackagesService, "getPackages");
    });

    afterEach(() => {
      packagesProviderStub.restore();
    });

    it(`should throw if missing packageId`, async () => {
      const error = await expect(InstallationManager.installPackage({} as BDCRequestContext, "", [])).to.be.rejected;

      expect(error).to.be.an("Error");
      expect(error.message).to.equal(`Missing required parameters`);
    });

    it(`should throw if bdc package not found`, async () => {
      const packageId = "123;";
      packagesProviderStub.returns([]);

      const error = await expect(InstallationManager.installPackage(mockContext, packageId, [])).to.be.rejected;

      expect(error.message).to.equal(`Could not find package with id ${packageId}`);
    });

    it("should call validateAndInstall if S4 entitlement is true and package is S4", async () => {
      const mockPackage: BDCPackage = {
        ...packageInstallationInitial.originalPackage,
        requiredApplications: [
          {
            provider: s4Provider, // or hcmProvider depending on test
            applications: [
              {
                applicationNamespace: s4ApplicationNamespace, // or hcmApplicationNamespace
                category: "application",
                minVersion: "1.0.0",
              },
            ],
          },
        ],
      };

      const mockContext = {
        isFeatureFlagActive: sinon.stub().callsFake((flag) => Promise.resolve(flag === "DWCO_BDC_ENTITLEMENT_CHECK")),
      } as unknown as BDCRequestContext;

      const packagesProviderStub = sinon.stub(PackagesService, "getBdcPackageWithValidation").resolves(mockPackage);
      const validateAndInstallStub = sinon.stub(InstallationManager as any, "validateAndInstall").resolves("ok");
      const allowedToInstallStub = sinon
        .stub(require("../../bdc/entitlement/entitlementValidator"), "allowedToInstall")
        .resolves(true);

      const result = await InstallationManager.installPackage(mockContext, mockPackage.id, []);

      expect(result).to.equal("ok");
      sinon.assert.calledOnce(allowedToInstallStub);

      packagesProviderStub.restore();
      validateAndInstallStub.restore();
      allowedToInstallStub.restore();
    });

    it("should call validateAndInstall if HCM entitlement is true and package is HCM", async () => {
      const mockPackage: BDCPackage = {
        ...packageInstallationInitial.originalPackage,
        requiredApplications: [
          {
            provider: hcmProvider,
            applications: [
              {
                applicationNamespace: hcmApplicationNamespace,
                category: "application",
                minVersion: "1.0.0",
              },
            ],
          },
        ],
      };

      const mockContext = {
        isFeatureFlagActive: sinon.stub().callsFake((flag) => Promise.resolve(flag === "DWCO_BDC_ENTITLEMENT_CHECK")),
      } as unknown as BDCRequestContext;

      const packagesProviderStub = sinon.stub(PackagesService, "getBdcPackageWithValidation").resolves(mockPackage);
      const validateAndInstallStub = sinon.stub(InstallationManager as any, "validateAndInstall").resolves("ok");
      const allowedToInstallStub = sinon
        .stub(require("../../bdc/entitlement/entitlementValidator"), "allowedToInstall")
        .resolves(true);

      const result = await InstallationManager.installPackage(mockContext, mockPackage.id, []);

      expect(result).to.equal("ok");
      sinon.assert.calledOnce(allowedToInstallStub);

      packagesProviderStub.restore();
      validateAndInstallStub.restore();
      allowedToInstallStub.restore();
    });

    it("should not install if entitlement is required but not present", async () => {
      const mockPackage: BDCPackage = {
        ...packageInstallationInitial.originalPackage,
        requiredApplications: [
          {
            provider: s4Provider,
            applications: [
              {
                applicationNamespace: s4ApplicationNamespace,
                category: "application",
                minVersion: "1.0.0",
              },
            ],
          },
        ],
      };

      const mockContext = {
        isFeatureFlagActive: sinon.stub().callsFake((flag) => Promise.resolve(flag === "DWCO_BDC_ENTITLEMENT_CHECK")),
      } as unknown as BDCRequestContext;

      const packagesProviderStub = sinon.stub(PackagesService, "getBdcPackageWithValidation").resolves(mockPackage);
      const validateAndInstallStub = sinon.stub(InstallationManager as any, "validateAndInstall").resolves("ok");
      const allowedToInstallStub = sinon
        .stub(require("../../bdc/entitlement/entitlementValidator"), "allowedToInstall")
        .resolves(false);

      const result = await InstallationManager.installPackage(mockContext, mockPackage.id, []);
      expect(result).to.equal(MISSING_ENTITLEMENT);

      sinon.assert.notCalled(validateAndInstallStub);
      sinon.assert.calledOnceWithExactly(allowedToInstallStub, mockContext, []);

      packagesProviderStub.restore();
      validateAndInstallStub.restore();
      allowedToInstallStub.restore();
    });
  });

  describe("retryPackage", () => {
    beforeEach(() => {
      const mockPackageInstallation: BDCPackageInstallation = structuredClone(packageInstallationInitial);
      catalogServiceGetPackageStub.returns(Promise.resolve(mockPackageInstallation));
    });

    it("should throw if feature flag is off", async () => {
      sinon.stub(require("../../bdc/entitlement/entitlementValidator"), "allowedToInstall").resolves(true);
      mockContextRetry.isFeatureFlagActive = () => Promise.resolve(false);

      const error = await expect(InstallationManager.retryPackage(mockContextRetry, "installationId")).to.be.rejected;

      expect(error.message).to.equal("Feature flag is off: DWCO_BDC_MANUAL_ONBOARDING_RETRY");
    });

    describe("connection check has no result", () => {
      beforeEach(() => {
        sinon.stub(require("../../bdc/entitlement/entitlementValidator"), "allowedToInstall").resolves(true);
        sinon.stub(SystemsService, "checkFosConnection").resolves(undefined);
      });

      it("should not affect the operation result", async () => {
        const result = await InstallationManager.retryPackage(mockContext, "installationId");
        expect(result.connectionCheckResult).to.be.undefined;
      });
    });

    describe("connection check fails", () => {
      beforeEach(() => {
        sinon.stub(require("../../bdc/entitlement/entitlementValidator"), "allowedToInstall").resolves(true);
        sinon.stub(SystemsService, "checkFosConnection").resolves({ success: false, errorMessage: "error" });
      });

      it("should stop retry, and connection status should be added to the exception", async () => {
        const error = await expect(InstallationManager.retryPackage(mockContext, "installationId")).to.be.rejected;

        expect(error.message).to.contains("error");
      });
    });

    describe("connection check succceeds", () => {
      beforeEach(() => {
        sinon.stub(require("../../bdc/entitlement/entitlementValidator"), "allowedToInstall").resolves(true);
        sinon.stub(SystemsService, "checkFosConnection").resolves({ success: true });
      });

      it("should NOT stop retry, and connection status should be added to the result", async () => {
        const result = await InstallationManager.retryPackage(mockContext, "installationId");
        expect(result.connectionCheckResult?.success).to.be.true;
        expect(result.installationId).to.equal("installationId");
        sinon.assert.calledOnce(validatePackageReinstallRequestStub);
      });
    });

    describe("should not retry to install if entitlement is required but not present", () => {
      const missingEntitlementDetails = { gracePeriodStart: "2025-06-09T13:04:34.175Z", gracePeriodDuration: 1800000 };

      beforeEach(() => {
        sinon.stub(require("../../bdc/entitlement/entitlementValidator"), "allowedToInstall").resolves(false);
        sinon
          .stub(require("../../bdc/entitlement/entitlementValidator"), "getMissingEntitlementDetails")
          .resolves(missingEntitlementDetails);
      });

      it("should return missingEntitlementDetails", async () => {
        const result = await InstallationManager.retryPackage(mockContext, "installationId");
        expect(result.missingEntitlementDetails).to.deep.equal(missingEntitlementDetails);
      });
    });
  });

  describe("updatePackage", () => {
    beforeEach(() => {
      const mockPackageInstallation: BDCPackageInstallation = structuredClone(packageInstallationInitial);
      catalogServiceGetPackageStub.returns(Promise.resolve(mockPackageInstallation));
      sinon
        .stub(PackagesService, "getBdcPackageWithValidation")
        .resolves({ ...mockPackageInstallation.originalPackage, version: "1.0.1" });
      sinon.stub(PackageInstallationValidator, "validatePackageInstallationRequest").resolves();
      sinon.stub(notificationsHelper as any, "sendNotification").resolves();
      sinon.stub(notificationsHelper as any, "sendNotificationForTechUser").resolves();
    });

    describe("connection check has no result", () => {
      beforeEach(() => {
        sinon.stub(require("../../bdc/entitlement/entitlementValidator"), "allowedToInstall").resolves(true);
        sinon.stub(SystemsService, "checkFosConnection").resolves(undefined);
      });

      it("should not affect the operation result", async () => {
        const result = await InstallationManager.updatePackage(mockContext, "installationId");
        expect(result.connectionCheckResult).to.be.undefined;
      });
    });

    describe("connection check fails", () => {
      beforeEach(() => {
        sinon.stub(require("../../bdc/entitlement/entitlementValidator"), "allowedToInstall").resolves(true);
        sinon.stub(SystemsService, "checkFosConnection").resolves({ success: false, errorMessage: "error" });
      });

      it("should stop the update, and connection status should be added to the exception", async () => {
        const error = await expect(InstallationManager.updatePackage(mockContext, "installationId")).to.be.rejected;
        expect(error.message).to.contains("error");
      });
    });

    describe("connection check succeeds", () => {
      beforeEach(() => {
        sinon.stub(SystemsService, "checkFosConnection").resolves({ success: true });
      });

      it("should NOT stop the update, and connection status should be added to the result", async () => {
        const result = await InstallationManager.updatePackage(mockContext, "installationId");
        expect(result.connectionCheckResult?.success).to.be.true;
        expect(result.installationId).to.equal("installationId");
        sinon.assert.calledOnce(doInstallPackageStub);
      });
    });

    describe("should not update if entitlement is required but not present", () => {
      const missingEntitlementDetails = { gracePeriodStart: "2025-06-09T13:04:34.175Z", gracePeriodDuration: 1800000 };

      beforeEach(() => {
        sinon.stub(require("../../bdc/entitlement/entitlementValidator"), "allowedToInstall").resolves(false);
        sinon
          .stub(require("../../bdc/entitlement/entitlementValidator"), "getMissingEntitlementDetails")
          .resolves(missingEntitlementDetails);
      });

      it("should return missingEntitlementDetails", async () => {
        const result = await InstallationManager.updatePackage(mockContext, "installationId");
        expect(result.missingEntitlementDetails).to.deep.equal(missingEntitlementDetails);
      });
    });
  });
});
