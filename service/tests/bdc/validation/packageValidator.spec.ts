/** @format */
import { IRequestContext } from "@sap/deepsea-types";
import { expect } from "chai";
import { BDCPackageCategory, BDCPackageType } from "../../../../shared/bdccockpit/Enums";
import { PackagesService } from "../../../bdc/packages/packagesService";
import {
  BDCContentPackage,
  BDCPackage,
  CNPackageRequiry,
  DataProductRequiry,
} from "../../../bdc/packages/packagesTypes";

describe("Package Validation", () => {
  let packages: BDCPackage[] | undefined = [];
  const bdcProviders = ["sap.datasphere", "sap.s4", "sap.analytics", "sap.sf"];
  const applicationNamespaces = bdcProviders.concat("sap.s4pce");
  const componentCategories = ["DataProduct", "CnPackage"];

  before(async () => {
    const mockContext: IRequestContext = {
      tenantId: "test-tenant",
      userInfo: { userId: "test-user" },
    } as IRequestContext;
    mockContext.isFeatureFlagActive = async function (ff: string) {
      if (ff === "DWCO_BDC_PACKAGE_CONTENT_DEV_SUPPORT" || ff === "DWCO_PROD_BDC_PACKAGES_FROM_CONTENT_GIT_REPO") {
        return false;
      }
      return true;
    };
    packages = await PackagesService.getPackages(mockContext);
  });

  it(`Validate package type`, async () => {
    let packageId = "";
    let packageType = "";
    try {
      packages?.forEach((pack) => {
        packageId = pack.id;
        packageType = pack.type;
        expect(Object.values(BDCPackageType).includes(packageType as BDCPackageType)).to.equal(true);
      });
    } catch (err) {
      throw new Error(`Wrong package type = ${packageType} in package id = ${packageId}`);
    }
  });

  it(`Validate package category`, async () => {
    let packageId = "";
    let packageCategory = "";
    try {
      packages?.forEach((pack) => {
        packageId = pack.id;
        packageCategory = pack.category;
        expect(Object.values(BDCPackageCategory).includes(packageCategory as BDCPackageCategory)).to.equal(true);
      });
    } catch (err) {
      throw new Error(`Wrong package category = ${packageCategory} in package id = ${packageId}`);
    }
  });

  it(`Validate required applications provider`, async () => {
    let packageId = "";
    let provider = "";
    try {
      packages?.forEach((pack) => {
        packageId = pack.id;
        pack.requiredApplications.forEach((requiredApplication) => {
          provider = requiredApplication.provider;
          expect(bdcProviders.includes(provider)).to.equal(true);
        });
      });
    } catch (err) {
      throw new Error(`Wrong required applications provider = ${provider} in package id = ${packageId}`);
    }
  });

  it(`Validate applicationNamespace`, async () => {
    let packageId = "";
    let applicationNamespace = "";
    try {
      packages?.forEach((pack) => {
        packageId = pack.id;
        pack.requiredApplications.forEach((requiredApplication) => {
          requiredApplication.applications.forEach((app) => {
            applicationNamespace = app.applicationNamespace;
            expect(applicationNamespaces.includes(applicationNamespace)).to.equal(true);
          });
        });
      });
    } catch (err) {
      throw new Error(`Wrong applicationNamespace = ${applicationNamespace} in package id = ${packageId}`);
    }
  });

  it(`Validate component category`, async () => {
    let packageId = "";
    let category = "";
    try {
      packages?.forEach((pack) => {
        packageId = pack.id;
        pack.components.forEach((component) => {
          category = component.category;
          expect(componentCategories.includes(category)).to.equal(true);
        });
      });
    } catch (err) {
      throw new Error(`Wrong component category = ${category} in package id = ${packageId}`);
    }
  });

  it(`Validate component provider`, async () => {
    let packageId = "";
    let provider = "";
    try {
      packages?.forEach((pack) => {
        packageId = pack.id;
        pack.components.forEach((component) => {
          provider = component.provider;
          expect(bdcProviders.includes(provider)).to.equal(true);
        });
      });
    } catch (err) {
      throw new Error(`Wrong component provider = ${provider} in package id = ${packageId}`);
    }
  });

  it(`Validate component (of type CNPackage) requires' category`, async () => {
    let packageId = "";
    let category = "";
    try {
      packages?.forEach((pack) => {
        packageId = pack.id;
        pack.components.forEach((component) => {
          if (component as BDCContentPackage) {
            component = component as BDCContentPackage;
            component.requires?.forEach((requiry) => {
              category = requiry.category;
              expect(componentCategories.includes(category)).to.equal(true);
            });
          }
        });
      });
    } catch (err) {
      throw new Error(`Wrong component requires' category = ${category} in package id = ${packageId}. Error: ${err}`);
    }
  });

  it(`Validate component (of type CNPackage) requires' provider`, async () => {
    let packageId = "";
    let provider = "";
    try {
      packages?.forEach((pack) => {
        packageId = pack.id;
        pack.components.forEach((component) => {
          if (component as BDCContentPackage) {
            component = component as BDCContentPackage;
            component.requires?.forEach((requiry) => {
              provider = requiry.provider;
              expect(bdcProviders.includes(provider)).to.equal(true);
            });
          }
        });
      });
    } catch (err) {
      throw new Error(`Wrong component requires' provider = ${provider} in package id = ${packageId}. Error: ${err}`);
    }
  });

  it(`Validate component dependencies`, async () => {
    let packageId = "";
    let category = "";
    let provider = "";
    let name, ordid;
    try {
      packages?.forEach((pack) => {
        packageId = pack.id;
        pack.components.forEach((component) => {
          if (component as BDCContentPackage) {
            component = component as BDCContentPackage;
            component.requires?.forEach((requiry) => {
              category = requiry.category;
              provider = requiry.provider;
              name = (requiry as CNPackageRequiry) && (requiry as CNPackageRequiry).name;
              ordid = (requiry as DataProductRequiry) && (requiry as DataProductRequiry).ordid;
              const fcomponent = pack.components.find(
                (dependencyComponent) =>
                  dependencyComponent.category === category &&
                  dependencyComponent.provider === provider &&
                  (("name" in dependencyComponent && dependencyComponent.name === (requiry as CNPackageRequiry).name) ||
                    ("ordid" in dependencyComponent &&
                      dependencyComponent.ordid === (requiry as DataProductRequiry).ordid))
              );
              expect(fcomponent !== undefined).to.equal(true);
            });
          }
        });
      });
    } catch (err) {
      throw new Error(
        `Component is missing for dependency with category = ${category}, provider = ${provider}, name/ordid = ${
          name ? name : ordid
        } in package id = ${packageId}. Error: ${err}`
      );
    }
  });
});
