/** @format */

import { SpanContext } from "@sap/dwc-tracing";
import chai, { expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import * as fs from "fs-extra";
import { StatusCodes } from "http-status-codes";
import * as path from "path";
import sinon from "sinon";
import { BDCRequestContext } from "../../../../shared/bdccockpit/Types";
import { PackageInstallationService } from "../../../bdc/packageInstallation/packageInstallationService";
import { BDCPackageInstallation, UCLTenantMapping } from "../../../bdc/packageInstallation/packageInstallationTypes";
import { SystemsService } from "../../../bdc/systems/systemsService";

chai.use(chaiAsPromised);

describe("PackageInstallationService", () => {
  let context: BDCRequestContext;

  beforeEach(() => {
    context = {
      spanContext: { traceId: "trace-id", spanId: "span-id", traceFlags: 1 },
      correlationId: "correlation-id",
      userInfo: { tenantId: "tenant-id" },
      isFeatureFlagActive: sinon.stub().resolves(false),
    } as unknown as BDCRequestContext;
  });

  afterEach(() => {
    sinon.restore();
  });

  const collectDataFromFile = async (filePath: string) => {
    try {
      const absolutePath = path.resolve(__dirname, filePath);
      const data = JSON.parse(await fs.readFile(absolutePath, "utf8"));
      return data;
    } catch (err) {
      throw new Error(`Failed to read data from file: ${filePath}. Error: ${err}`);
    }
  };

  describe("cancelInstallation", () => {
    const packageInstallationInitial: BDCPackageInstallation = {
      installationId: "testInstallationId",
      componentsInstallationStatus: [
        { componentId: "c1", status: "PENDING" },
        { componentId: "c2", status: "PENDING" },
        { componentId: "c3", status: "PENDING" },
        { componentId: "c4", status: "PENDING" },
      ],
      cancellationRequested: false,
    } as BDCPackageInstallation;

    let catalogServiceGetPackageStub: sinon.SinonStub;
    let catalogServiceUpdatePackageStub: sinon.SinonStub;

    beforeEach(() => {
      catalogServiceGetPackageStub = sinon.stub(PackageInstallationService, "getPackageInstallation");
      catalogServiceUpdatePackageStub = sinon.stub(PackageInstallationService, "updatePackageInstallation");
    });

    afterEach(() => {
      sinon.restore();
    });

    it("should throw NOT_FOUND if installation does not exist", async () => {
      catalogServiceGetPackageStub.resolves(undefined);

      await expect(PackageInstallationService.cancelInstallation(context, "testInstallationId", true))
        .to.be.rejectedWith(Error)
        .and.eventually.have.property("status", StatusCodes.NOT_FOUND);
    });

    it("should throw CONFLICT if no components are in EXECUTING state", async () => {
      const testInstallation = structuredClone(packageInstallationInitial);
      testInstallation.componentsInstallationStatus.forEach((c) => (c.status = "DONE"));
      catalogServiceGetPackageStub.resolves(testInstallation);

      await expect(PackageInstallationService.cancelInstallation(context, "testInstallationId", true))
        .to.be.rejectedWith(Error)
        .and.eventually.have.property("status", StatusCodes.CONFLICT);
    });

    it("should set cancellationRequested to true and update installation if EXECUTING component exists", async () => {
      const testInstallation = structuredClone(packageInstallationInitial);
      testInstallation.componentsInstallationStatus[0].status = "EXECUTING";
      testInstallation.cancellationRequested = false;
      catalogServiceGetPackageStub.resolves(testInstallation);

      await PackageInstallationService.cancelInstallation(context, "testInstallationId", true);

      expect(testInstallation.cancellationRequested).to.be.true;
      sinon.assert.calledOnceWithExactly(catalogServiceUpdatePackageStub, testInstallation, context);
    });

    it("should set cancellationRequested to false if explicitly passed as false", async () => {
      const testInstallation = structuredClone(packageInstallationInitial);
      testInstallation.componentsInstallationStatus[0].status = "EXECUTING";
      delete testInstallation.cancellationRequested;
      catalogServiceGetPackageStub.resolves(testInstallation);

      await PackageInstallationService.cancelInstallation(context, "testInstallationId", false);

      expect(testInstallation.cancellationRequested).to.be.false;
      sinon.assert.calledOnceWithExactly(catalogServiceUpdatePackageStub, testInstallation, context);
    });
  });

  describe("checkTenantForDeletionFromFormation", () => {
    let tenantId: string;
    let uclTenantMapping: UCLTenantMapping;
    let getAllSystemsStub: sinon.SinonStub;

    beforeEach(() => {
      tenantId = "test-tenant-id";
      uclTenantMapping = {
        tenantId,
        tenants: [
          {
            applicationNamespace: "sap.bdc-cockpit",
            applicationTenantId: "bdc-cockpit-tenant-id",
            uclSystemName: "UCL System Name",
            uclSystemTenantId: "UCL System Tenant ID",
          },
        ],
        formation: {
          uclFormationTypeId: "formation-type-id",
          uclFormationId: "formation-id",
          uclFormationName: "formation-name",
        },
      };
      getAllSystemsStub = sinon.stub(SystemsService, "getAllSystems").resolves([
        {
          className: "System",
          applicationNamespace: "sap.s4",
          systemId: "system-id",
          name: "system-name",
          applicationTenantId: tenantId,
          formations: [
            {
              formationId: "formation-id",
              formationTypeId: "formation-type-id",
              formationName: "formation-name",
            },
          ],
        },
      ]);
      sinon.stub(PackageInstallationService as any, "getInstalledPackagesBySystemTenantId").resolves({
        errors: [],
      });
    });

    const testCases = [
      { description: "with spanContext", spanContext: { traceId: "trace-id", spanId: "span-id", traceFlags: 1 } },
      { description: "with empty spanContext", spanContext: {} as SpanContext },
      { description: "with undefined spanContext", spanContext: undefined },
    ];

    testCases.forEach(({ description, spanContext }) => {
      it(`should call getAllSystems ${description}`, async () => {
        context.spanContext = spanContext;
        const result = await PackageInstallationService.checkTenantForDeletionFromFormation(
          context,
          tenantId,
          uclTenantMapping
        );
        expect(result, "Expected result to be an empty array of installedPackages and errors").to.deep.equal({
          errors: [],
        });
        expect(getAllSystemsStub.calledOnce, "Expected getAllSystems to be called once").to.be.true;
        const bdcCockpitContext = getAllSystemsStub.getCall(0).args[0] as BDCRequestContext;
        expect(bdcCockpitContext.userInfo.tenantId, "Expected tenantId to match bdc-cockpit-tenant-id").to.equal(
          "bdc-cockpit-tenant-id"
        );
        expect(
          bdcCockpitContext.spanContext,
          "Expected spanContext to match the original context spanContext"
        ).to.deep.equal(context.spanContext);
      });
    });
  });

  describe("getInstalledPackagesPerSku", () => {
    let installedPackages: BDCPackageInstallation[] | undefined;
    const installedPackagesPath = "test_data/installed_packages.json";

    describe("check installed packages", () => {
      let installedPackagesPerSku: any;

      before(async () => {
        installedPackages = await collectDataFromFile(installedPackagesPath);
        sinon.stub(PackageInstallationService, "getFlattenedInstalledPackages").resolves(installedPackages);
        installedPackagesPerSku = await PackageInstallationService.getInstalledPackagesPerSku(context);
      });

      it(`expected to return number of found insight apps: 3`, async () => {
        expect(installedPackagesPerSku[0].CoreERPIASAPManagedTenant.InsightApps).to.equal(3);
      });

      it(`expected to return number of found data packages: 17`, async () => {
        expect(installedPackagesPerSku[0].CoreERPIASAPManagedTenant.DataPackages).to.equal(17);
      });

      it(`expected to return number of found insight apps: 0`, async () => {
        expect(installedPackagesPerSku[1].HCMAnalyticsInsightAppTenant.InsightApps).to.equal(0);
      });

      it(`expected to return number of found data packages: 0`, async () => {
        expect(installedPackagesPerSku[1].HCMAnalyticsInsightAppTenant.DataPackages).to.equal(0);
      });
    });

    describe("check missing tenant id", () => {
      let contextWithEmptyTenant: BDCRequestContext;
      let result: any;

      beforeEach(async () => {
        contextWithEmptyTenant = {
          spanContext: { traceId: "trace-id", spanId: "span-id", traceFlags: 1 },
          correlationId: "correlation-id",
          userInfo: { tenantId: null },
          isFeatureFlagActive: sinon.stub().resolves(false),
        } as unknown as BDCRequestContext;
        installedPackages = await collectDataFromFile(installedPackagesPath);
        sinon.stub(PackageInstallationService, "getFlattenedInstalledPackages").resolves(installedPackages);
        result = await PackageInstallationService.getInstalledPackagesPerSku(contextWithEmptyTenant);
      });

      it(`expected to return tenantId=null`, async () => {
        expect(contextWithEmptyTenant.userInfo.tenantId).to.equal(null);
      });

      it(`expected to return installedPackages.length > 0`, async () => {
        expect(installedPackages?.length).to.be.greaterThan(0);
      });

      it(`expected to return "Missing tenant id"`, async () => {
        expect(result).to.equal("Missing tenant id");
      });
    });
  });
});
