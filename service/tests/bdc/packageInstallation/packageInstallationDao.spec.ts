/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { httpClient } from "@sap/dwc-http-client";
import { expect } from "chai";
import sinon from "sinon";
import { PropertiesField } from "../../../bdc/catalog/CatalogConstants";
import { PackageInstallationDao } from "../../../bdc/packageInstallation/packageInstallationDao";

describe("getInstallationComponentsId", () => {
  const mockContext = {
    tenantId: "tenant123",
    userInfo: { userId: "user456" },
  } as unknown as IRequestContext;

  const installationId = "installation789";
  const componentId = "componentXYZ";

  let httpStub: sinon.SinonStub;

  beforeEach(() => {
    httpStub = sinon.stub(httpClient, "call");
  });

  afterEach(() => {
    sinon.restore();
  });

  it("should return component IDs from response", async () => {
    httpStub.resolves({
      status: 200,
      body: {
        allObjects: [
          {
            propertyTags: [
              {
                propertyId: PropertiesField.COMPONENT_ID,
                valueObjects: [{ valueString: componentId }],
              },
            ],
          },
        ],
      },
    });

    const dao = new PackageInstallationDao();
    const result = await dao.getInstallationComponentsId(mockContext, installationId);

    expect(result).to.deep.equal([componentId]);
    expect(httpStub.calledOnce).to.be.true;
  });

  it("should return an empty array if allObjects is empty", async () => {
    httpStub.resolves({ status: 200, body: { allObjects: [] } });

    const dao = new PackageInstallationDao();
    const result = await dao.getInstallationComponentsId(mockContext, installationId);

    expect(result).to.deep.equal([]);
  });

  it("should return an empty array if propertyTags is missing", async () => {
    httpStub.resolves({
      status: 200,
      body: {
        allObjects: [
          {
            propertyTags: [], // fontos, hogy ne legyen undefined
          },
        ],
      },
    });

    const dao = new PackageInstallationDao();
    const result = await dao.getInstallationComponentsId(mockContext, installationId);

    expect(result).to.deep.equal([]);
  });

  it("should return an empty array if valueObjects is missing", async () => {
    httpStub.resolves({
      status: 200,
      body: {
        allObjects: [
          {
            propertyTags: [
              {
                propertyId: PropertiesField.COMPONENT_ID,
                valueObjects: [],
              },
            ],
          },
        ],
      },
    });

    const dao = new PackageInstallationDao();
    const result = await dao.getInstallationComponentsId(mockContext, installationId);

    expect(result).to.deep.equal([]);
  });

  it("should filter out undefined valueStrings", async () => {
    httpStub.resolves({
      status: 200,
      body: {
        allObjects: [
          {
            propertyTags: [
              {
                propertyId: PropertiesField.COMPONENT_ID,
                valueObjects: [{}, { valueString: componentId }],
              },
            ],
          },
        ],
      },
    });

    const dao = new PackageInstallationDao();
    const result = await dao.getInstallationComponentsId(mockContext, installationId);

    expect(result).to.deep.equal([componentId]);
  });

  it("should throw an error if response status is not OK", async () => {
    httpStub.resolves({ status: 500, body: {} });

    const dao = new PackageInstallationDao();

    try {
      await dao.getInstallationComponentsId(mockContext, installationId);
      throw new Error("Should have thrown");
    } catch (err: any) {
      expect(err.message).to.equal("Response status code:500");
    }
  });

  it("should throw an error if httpClient.call fails", async () => {
    httpStub.rejects(new Error("Network error"));

    const dao = new PackageInstallationDao();

    try {
      await dao.getInstallationComponentsId(mockContext, installationId);
      throw new Error("Should have thrown");
    } catch (err: any) {
      expect(err.message).to.equal("Network error");
    }
  });
});
