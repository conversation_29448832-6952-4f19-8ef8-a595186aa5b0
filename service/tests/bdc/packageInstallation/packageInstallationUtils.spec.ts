/** @format */

import { expect } from "chai";
import { cloneDeep } from "lodash";

import * as semver from "semver";

import {
  BDCPackageCategory,
  BDCPackageStatus,
  BDCPackageType,
  BDCUninstallationOperation,
} from "../../../../shared/bdccockpit/Enums";
import {
  ACNFolder,
  AdditionalInstallationProperty,
  AdditionalInstallationPropertyName,
  BDCInstalledContent,
} from "../../../../shared/bdccockpit/Types";
import { PropertiesField } from "../../../bdc/catalog/CatalogConstants";
import {
  BDCPackageInstallation,
  InstallationStatusCode,
} from "../../../bdc/packageInstallation/packageInstallationTypes";
import {
  convertInstallationStatusToComponentInstallationDetails,
  convertOperationToMessageAction,
  enrichWithSpacesData,
  extractStatusFromPackageInstallation,
  findAdditionalInstallationPropertyByName,
  getAllPackageInstallationsFromResponse,
  getAllPackageInstallationsWithComponentsFromResponse,
  getBDCPackageStatus,
  getInsideAppLinksArr,
  getInstalledComponentsId,
  getMillisecondsOfDate,
  getSourceSystem,
  isSameMajorDifferentVersion,
  sanitizePackageInstallationForLog,
  validateBdcPackageInstallationAndMessage,
} from "../../../bdc/packageInstallation/packageInstallationUtils";
import { MessageAction, MessageTooOldError } from "../../../messageQueuing/consumer/PackageInstallationConsumer";

describe("test packages status", () => {
  it("status from catalog to BDCPackageStatus", async () => {
    expect(getBDCPackageStatus("DONE")).to.equal(BDCPackageStatus.INSTALLED);
    expect(getBDCPackageStatus("FAILED")).to.equal(BDCPackageStatus.INSTALLATION_FAILED);
    expect(getBDCPackageStatus("ERROR")).to.equal(BDCPackageStatus.INSTALLATION_FAILED);
    expect(getBDCPackageStatus("EXECUTING")).to.equal(BDCPackageStatus.INSTALLING);
    expect(getBDCPackageStatus("PENDING")).to.equal(BDCPackageStatus.INSTALLING);
    //DEFAULT CASE
    expect(getBDCPackageStatus("WAITING_TO_INSTALL_READY")).to.equal(BDCPackageStatus.INSTALLED);
    expect(getBDCPackageStatus("NOT ACTIVATED")).to.equal(BDCPackageStatus.INSTALLED);
  });
});

describe("test convertOperationToMessageAction", () => {
  const testCases = [
    { operation: BDCUninstallationOperation.RETRY, expected: MessageAction.RETRY_UNINSTALL },
    { operation: BDCUninstallationOperation.UNINSTALL, expected: MessageAction.UNINSTALL },
    { operation: BDCUninstallationOperation.CLEANUP, expected: MessageAction.CLEANUP },
    { operation: undefined, expected: MessageAction.UNINSTALL },
    { operation: "INVALID_OPERATION" as BDCUninstallationOperation, expected: MessageAction.UNINSTALL },
  ];

  testCases.forEach(({ operation, expected }) => {
    it(`should return ${expected} for operation: ${operation}`, () => {
      const result = convertOperationToMessageAction(operation);
      expect(result).to.equal(expected);
    });
  });
});

describe("test installation status", () => {
  let packageInstallation: BDCPackageInstallation = {
    startTime: new Date("2023-07-25T10:00:00Z"),
    endTime: new Date("2023-07-25T10:05:00Z"),
    correlationId: "22222222",
    installationCyclesCount: 12,
    originalPackage: {
      id: "pkg-1234",
      name: "Sample Package",
      version: "1.0.0",
      shortDescription: "This is a sample BDC package for testing.",
      applicationComponent: "SampleComponent",
      schemaName: "SampleSchema",
      schemaVersion: "1.0",
      fileProducer: "SampleProducer",
      preview: [],
      type: BDCPackageType.INSIGHT_APPLICATION,
      creationDate: "2023-07-25T10:00:00Z",
      category: BDCPackageCategory.HUMAN_RESOURCES,
      description: "",

      requiredApplications: [
        {
          provider: "sap.s4",
          applications: [
            {
              applicationNamespace: "sap.s4",
              category: "application",
              minVersion: "1.0.0",
            },
          ],
        },
      ],
      components: [
        {
          componentId: "comp-1234",
          category: "DataProduct",
          provider: "sap.s4",
          version: "1.0.0",
          name: "SampleDataProduct",
          description: "Sample data product component",
          ordid: "sample-ordid",
          numberOfEntities: 10,
        },
        {
          componentId: "comp-5678",
          category: "CnPackage",
          provider: "sap.datasphere",
          version: "1.0.0",
          name: "SampleCNPackage",
          description: "Sample CN package component",
          requires: [
            {
              category: "DataProduct",
              minVersion: "1.0.0",
              provider: "SampleProvider",
              ordid: "sample-ordid",
            },
          ],
        },
      ],
    },
    tenantId: "tenant-1234",
    installationId: "inst-1234",
    installationProperties: [
      {
        applicationNamespace: "sap.s4",
        systemTenant: "tenant-1234",
        systemName: "",
      },
    ],
    componentsInstallationStatus: [
      {
        componentId: "comp-1234",
        componentInstallationDetails: {
          importJobId: "job-1234",
        },
        startTime: new Date("2023-07-25T10:00:00Z"),
        endTime: new Date("2023-07-25T10:05:00Z"),
        status: "DONE",
        message: "Installation completed successfully",
        retryCount: 0,
      },
      {
        componentId: "comp-5678",
        componentInstallationDetails: {
          importJobId: "job-5678",
        },
        startTime: new Date("2023-07-25T10:06:00Z"),
        endTime: new Date("2023-07-25T10:10:00Z"),
        status: "DONE",
        message: "Installation completed successfully",
        retryCount: 0,
      },
    ],
  };

  const allInstallationStatusCombinations: Array<{
    statuses: [InstallationStatusCode, InstallationStatusCode];
    expected: BDCPackageStatus;
  }> = [
    // INSTALLATION_FAILED scenarios
    { statuses: ["FAILED", "DONE"], expected: BDCPackageStatus.INSTALLATION_FAILED },
    { statuses: ["DONE", "FAILED"], expected: BDCPackageStatus.INSTALLATION_FAILED },
    { statuses: ["ERROR", "DONE"], expected: BDCPackageStatus.INSTALLATION_FAILED },
    { statuses: ["DONE", "ERROR"], expected: BDCPackageStatus.INSTALLATION_FAILED },
    { statuses: ["EXECUTING", "ERROR"], expected: BDCPackageStatus.INSTALLATION_FAILED },
    { statuses: ["ERROR", "EXECUTING"], expected: BDCPackageStatus.INSTALLATION_FAILED },
    { statuses: ["PENDING", "ERROR"], expected: BDCPackageStatus.INSTALLATION_FAILED },
    { statuses: ["ERROR", "PENDING"], expected: BDCPackageStatus.INSTALLATION_FAILED },

    // INSTALLING scenarios
    { statuses: ["EXECUTING", "EXECUTING"], expected: BDCPackageStatus.INSTALLING },
    { statuses: ["DONE", "EXECUTING"], expected: BDCPackageStatus.INSTALLING },
    { statuses: ["EXECUTING", "DONE"], expected: BDCPackageStatus.INSTALLING },
    { statuses: ["PENDING", "EXECUTING"], expected: BDCPackageStatus.INSTALLING },
    { statuses: ["EXECUTING", "PENDING"], expected: BDCPackageStatus.INSTALLING },
    { statuses: ["PENDING", "PENDING"], expected: BDCPackageStatus.INSTALLING },
    { statuses: ["DONE", "PENDING"], expected: BDCPackageStatus.INSTALLING },
    { statuses: ["PENDING", "DONE"], expected: BDCPackageStatus.INSTALLING },

    // INSTALLED scenarios
    { statuses: ["DONE", "DONE"], expected: BDCPackageStatus.INSTALLED },
  ];

  allInstallationStatusCombinations.forEach(({ statuses: [status1, status2], expected }) => {
    it(`should return ${expected} for statuses: ${status1} and ${status2}`, async () => {
      const packageInstallationCopy = cloneDeep(packageInstallation);
      packageInstallationCopy.componentsInstallationStatus[0].status = status1;
      packageInstallationCopy.componentsInstallationStatus[1].status = status2;

      expect(extractStatusFromPackageInstallation(packageInstallationCopy)).to.equal(expected);
    });
  });

  const allUninstallStatusCombinations: Array<{
    statuses: [InstallationStatusCode, InstallationStatusCode];
    expected: BDCPackageStatus;
  }> = [
    // UNINSTALLATION_FAILED scenarios
    { statuses: ["FAILED", "DONE"], expected: BDCPackageStatus.UNINSTALLATION_FAILED },
    { statuses: ["DONE", "FAILED"], expected: BDCPackageStatus.UNINSTALLATION_FAILED },
    { statuses: ["ERROR", "DONE"], expected: BDCPackageStatus.UNINSTALLATION_FAILED },
    { statuses: ["DONE", "ERROR"], expected: BDCPackageStatus.UNINSTALLATION_FAILED },

    // UNINSTALLING scenarios
    { statuses: ["EXECUTING", "EXECUTING"], expected: BDCPackageStatus.UNINSTALLING },
    { statuses: ["DONE", "EXECUTING"], expected: BDCPackageStatus.UNINSTALLING },
    { statuses: ["EXECUTING", "DONE"], expected: BDCPackageStatus.UNINSTALLING },
    { statuses: ["PENDING", "EXECUTING"], expected: BDCPackageStatus.UNINSTALLING },
    { statuses: ["EXECUTING", "PENDING"], expected: BDCPackageStatus.UNINSTALLING },
    { statuses: ["PENDING", "PENDING"], expected: BDCPackageStatus.UNINSTALLING },
    { statuses: ["DONE", "PENDING"], expected: BDCPackageStatus.UNINSTALLING },
    { statuses: ["PENDING", "DONE"], expected: BDCPackageStatus.UNINSTALLING },
  ];

  allUninstallStatusCombinations.forEach(({ statuses: [status1, status2], expected }) => {
    it(`should return ${expected} for uninstallation statuses: ${status1} and ${status2}`, async () => {
      const packageInstallationCopy = cloneDeep(packageInstallation);
      packageInstallationCopy.componentsUninstallationStatus = [
        {
          componentId: "comp-1",
          componentInstallationDetails: {
            importJobId: "job-1",
          },
          startTime: new Date(),
          endTime: new Date(),
          status: status1,
          message: "Status 1 message",
          retryCount: 0,
        },
        {
          componentId: "comp-2",
          componentInstallationDetails: {
            importJobId: "job-2",
          },
          startTime: new Date(),
          endTime: new Date(),
          status: status2,
          message: "Status 2 message",
          retryCount: 0,
        },
      ];

      expect(extractStatusFromPackageInstallation(packageInstallationCopy)).to.equal(expected);
    });
  });
  it("test sanitizePackageInstallationForLog", async () => {
    const packageInstallationCopy = cloneDeep(packageInstallation);
    packageInstallationCopy.workSpaceLinks = new Map([["l1", "v2"]]);
    packageInstallationCopy.storyLinks = new Map([["l1", "v2"]]);
    const sanitizedPackageInstallationStr = sanitizePackageInstallationForLog(packageInstallation as any);
    const sanitizedPackageInstallationObj = JSON.parse(sanitizedPackageInstallationStr);
    expect(sanitizedPackageInstallationObj.originalPackage).to.be.undefined;
    expect(sanitizedPackageInstallationObj.storyLinks).to.be.undefined;
    expect(sanitizedPackageInstallationObj.workSpaceLinks).to.be.undefined;
  });
  it("test sanitizePackageInstallationForLog with undefined", async () => {
    const sanitizedPackageInstallationStr = sanitizePackageInstallationForLog(undefined);
    expect(sanitizedPackageInstallationStr).to.be.equal("undefined");
  });
});

describe("getSourceSystem", () => {
  const mockContext = {} as any;

  it("should find system for sap.s4 applicationNamespace", () => {
    const result = getSourceSystem(
      mockContext,
      {
        installationProperties: [
          {
            applicationNamespace: "sap.s4",
            systemTenant: "tenant-1234",
            systemName: "S4-System",
          },
        ],
      } as any,
      [
        {
          systemId: "sys-1",
          className: "S4",
          name: "S4-System",
          applicationNamespace: "sap.s4",
          applicationTenantId: "tenant-1234",
          applicationVersion: "1.0.0",
          formations: [],
        },
      ],
      []
    );

    expect(result).to.deep.include({
      applicationNamespace: "sap.s4",
      tenant: "tenant-1234",
      name: "S4-System",
      version: semver.coerce("1.0.0")?.version,
    });
  });

  it("should find system for sap.sf applicationNamespace", () => {
    const result = getSourceSystem(
      mockContext,
      {
        installationProperties: [
          {
            applicationNamespace: "sap.sf",
            systemTenant: "tenant-5678",
            systemName: "HCM-System",
          },
        ],
      } as any,
      [
        {
          systemId: "sys-2",
          className: "HCM",
          name: "HCM-System",
          applicationNamespace: "sap.sf",
          applicationTenantId: "tenant-5678",
          applicationVersion: "2.0.0",
          formations: [],
        },
      ],
      []
    );

    expect(result).to.deep.include({
      applicationNamespace: "sap.sf",
      tenant: "tenant-5678",
      name: "HCM-System",
      version: semver.coerce("2.0.0")?.version,
    });
  });

  it("should fallback when system info not found", () => {
    const result = getSourceSystem(
      mockContext,
      {
        installationProperties: [
          {
            applicationNamespace: "sap.sf",
            systemTenant: "tenant-9999",
            systemName: "HCM-System",
          },
        ],
      } as any,
      [],
      []
    );

    expect(result).to.deep.include({
      applicationNamespace: "sap.sf",
      tenant: "tenant-9999",
      name: "HCM-System",
      version: semver.coerce("0")?.version,
    });
  });

  it("should throw error if no sap.s4 or sap.sf namespace found", () => {
    const call = () => getSourceSystem(mockContext, { installationProperties: [] } as any, [], []);
    expect(call).to.throw("no installation properties");
  });
});

describe("isSameMajorDifferentVersion", () => {
  it("should compare major version and detect difference", () => {
    const result = isSameMajorDifferentVersion("1.2.3", "1.4.5");
    expect(result).to.equal(true);
  });

  it("should compare major version and detect difference", () => {
    const result = isSameMajorDifferentVersion("1.4.5", "1.2.3");
    expect(result).to.equal(true);
  });

  it("should throw error on invalid version format", () => {
    expect(() => isSameMajorDifferentVersion("not-a-version", "1.0.0")).to.throw("Invalid version format");
  });
});

describe("getMillisecondsOfDate", () => {
  it("should return milliseconds string for valid date", () => {
    const str = getMillisecondsOfDate("2023-01-01T00:00:00Z");
    expect(str).to.equal("1672531200000");
  });

  it("should return milliseconds for Date instance", () => {
    const date = new Date("2023-01-01T00:00:00Z");
    const result = getMillisecondsOfDate(date);
    expect(result).to.equal(date.getTime().toString());
  });

  it("should return the string if date string is invalid", () => {
    const result = getMillisecondsOfDate("invalid-date");
    expect(result).to.equal("invalid-date");
  });
});

describe("getAllPackageInstallationsFromResponse", () => {
  it("should return empty map for empty input array", () => {
    const result = getAllPackageInstallationsFromResponse({ allObjects: [] } as any);
    expect(result.size).to.equal(0);
  });

  it("should return empty map when allObjects is undefined", () => {
    const result = getAllPackageInstallationsFromResponse({} as any);
    expect(result.size).to.equal(0);
  });

  it("should return empty map when packages is null", () => {
    const result = getAllPackageInstallationsFromResponse(null as any);
    expect(result.size).to.equal(0);
  });

  it("should skip object if propertyTags is empty", () => {
    const result = getAllPackageInstallationsFromResponse({
      allObjects: [
        {
          name: "id1",
          propertyTags: [],
          navigationLinks: [],
        },
      ],
    } as any);
    expect(result.size).to.equal(0);
  });

  it("should skip object if no BDC_INSTALL_JSON property is present", () => {
    const result = getAllPackageInstallationsFromResponse({
      allObjects: [
        {
          name: "id2",
          propertyTags: [
            {
              propertyId: "OTHER_ID",
              valueObjects: [],
            },
          ],
          navigationLinks: [],
        },
      ],
    } as any);
    expect(result.size).to.equal(0);
  });

  it("should skip if valueObjects is empty", () => {
    const result = getAllPackageInstallationsFromResponse({
      allObjects: [
        {
          name: "id3",
          propertyTags: [
            {
              propertyId: PropertiesField.BDC_INSTALL_JSON,
              valueObjects: [],
            },
          ],
          navigationLinks: [],
        },
      ],
    } as any);
    expect(result.size).to.equal(0);
  });

  it("should parse valid packageInstallation and set formationCatalogUuid", () => {
    const json = {
      componentsInstallationStatus: [],
      componentsUninstallationStatus: [],
    };
    const result = getAllPackageInstallationsFromResponse({
      allObjects: [
        {
          name: "valid1",
          propertyTags: [
            {
              propertyId: PropertiesField.BDC_INSTALL_JSON,
              valueObjects: [{ valueText: JSON.stringify(json) }],
            },
          ],
          navigationLinks: [
            {
              relationshipTypeId: "CRT_BDC_INSTALL_TO_FORMATION",
              links: [{ refId: "formation-123" }],
            },
          ],
        },
      ],
    } as any);

    expect(result.size).to.equal(1);
    const pkg = result.get("valid1");
    expect(pkg?.formationCatalogUuid).to.equal("formation-123");
  });

  it("should not set formationCatalogUuid if relationshipTypeId not found", () => {
    const json = {
      componentsInstallationStatus: [],
      componentsUninstallationStatus: [],
    };
    const result = getAllPackageInstallationsFromResponse({
      allObjects: [
        {
          name: "valid2",
          propertyTags: [
            {
              propertyId: PropertiesField.BDC_INSTALL_JSON,
              valueObjects: [{ valueText: JSON.stringify(json) }],
            },
          ],
          navigationLinks: [
            {
              relationshipTypeId: "OTHER_REL",
              links: [{ refId: "other-id" }],
            },
          ],
        },
      ],
    } as any);

    const pkg = result.get("valid2");
    expect(pkg?.formationCatalogUuid).to.be.undefined;
  });

  it("should parse only first valid valueObject and skip remaining", () => {
    const json1 = {
      componentsInstallationStatus: [],
      componentsUninstallationStatus: [],
      custom: "parsed",
    };
    const json2 = {
      custom: "should-not-be-parsed",
    };

    const result = getAllPackageInstallationsFromResponse({
      allObjects: [
        {
          name: "valid3",
          propertyTags: [
            {
              propertyId: PropertiesField.BDC_INSTALL_JSON,
              valueObjects: [{ valueText: JSON.stringify(json1) }, { valueText: JSON.stringify(json2) }],
            },
          ],
          navigationLinks: [],
        },
      ],
    } as any);

    const pkg = result.get("valid3") as any;
    expect(pkg?.custom).to.equal("parsed");
  });
});

describe("getInsideAppLinksArr", () => {
  it("should return an array of objects with name and url from a valid map", () => {
    const map = new Map<string, string>([
      ["Link1", "https://example.com/1"],
      ["Link2", "https://example.com/2"],
    ]);
    const result = getInsideAppLinksArr(map);
    expect(result).to.deep.equal([
      { name: "Link1", url: "https://example.com/1" },
      { name: "Link2", url: "https://example.com/2" },
    ]);
  });

  it("should return an empty array if map is empty", () => {
    const map = new Map<string, string>();
    const result = getInsideAppLinksArr(map);
    expect(result).to.deep.equal([]);
  });

  it("should return an empty array if input is undefined", () => {
    const result = getInsideAppLinksArr(undefined as any);
    expect(result).to.deep.equal([]);
  });

  it("should return an empty array if input is null", () => {
    const result = getInsideAppLinksArr(null as any);
    expect(result).to.deep.equal([]);
  });
});

describe("getAllPackageInstallationsWithComponentsFromResponse", () => {
  it("should return empty map when packages is null", () => {
    const result = getAllPackageInstallationsWithComponentsFromResponse(null);
    expect(result.size).to.equal(0);
  });

  it("should return empty map when allObjects is undefined", () => {
    const result = getAllPackageInstallationsWithComponentsFromResponse({} as any);
    expect(result.size).to.equal(0);
  });

  it("should return empty map when allObjects is empty", () => {
    const result = getAllPackageInstallationsWithComponentsFromResponse({ allObjects: [] });
    expect(result.size).to.equal(0);
  });

  it("should skip objects with non-installedPackage className", () => {
    const result = getAllPackageInstallationsWithComponentsFromResponse({
      allObjects: [
        {
          className: "somethingElse",
          name: "id1",
          propertyTags: [],
        },
      ],
    });
    expect(result.size).to.equal(0);
  });

  it("should parse packageInstallation and collect component IDs", () => {
    const json = {
      componentsInstallationStatus: [{ name: "A" }],
      componentsUninstallationStatus: [{ name: "B" }],
    };

    const result = getAllPackageInstallationsWithComponentsFromResponse({
      allObjects: [
        {
          className: "installedPackage",
          name: "valid1",
          navigationLinks: [
            {
              relationshipTypeId: "CRT_BDC_INSTALL_TO_FORMATION",
              links: [{ refId: "formation-123" }],
            },
          ],
          propertyTags: [
            {
              propertyId: PropertiesField.BDC_INSTALL_JSON,
              valueObjects: [{ valueText: JSON.stringify(json) }],
            },
            {
              propertyId: PropertiesField.COMPONENT_ID,
              valueObjects: [{ valueString: "comp-1" }, { valueString: "comp-2" }],
            },
          ],
        },
      ],
    });

    expect(result.size).to.equal(1);
    const pkg = result.get("valid1");
    expect(pkg?.formationCatalogUuid).to.equal("formation-123");
    expect(pkg?.installedComponentsId).to.deep.equal(["comp-1", "comp-2"]);
  });

  it("should handle missing COMPONENT_ID and set empty component list", () => {
    const json = {
      componentsInstallationStatus: [],
      componentsUninstallationStatus: [],
    };

    const result = getAllPackageInstallationsWithComponentsFromResponse({
      allObjects: [
        {
          className: "installedPackage",
          name: "valid2",
          navigationLinks: [],
          propertyTags: [
            {
              propertyId: PropertiesField.BDC_INSTALL_JSON,
              valueObjects: [{ valueText: JSON.stringify(json) }],
            },
          ],
        },
      ],
    });

    const pkg = result.get("valid2");
    expect(pkg?.installedComponentsId).to.deep.equal([]);
  });

  it("should skip adding to map if packageInstallation was never set", () => {
    const result = getAllPackageInstallationsWithComponentsFromResponse({
      allObjects: [
        {
          className: "installedPackage",
          name: "missingJSON",
          navigationLinks: [],
          propertyTags: [
            {
              propertyId: "OTHER_ID",
              valueObjects: [],
            },
          ],
        },
      ],
    });

    expect(result.size).to.equal(0);
  });
});

describe("enrichWithSpacesData", () => {
  it("should enrich contents with matching folder spaces", async () => {
    const context = {} as any;
    const contents: BDCInstalledContent[] = [
      {
        provider: "sap.datasphere",
        name: "pkg1",
        spaces: [],
        componentId: "comp1",
        category: "DataProduct",
        version: "1.0.0",
      },
      {
        provider: "sap.datasphere",
        name: "pkg2",
        spaces: [],
        componentId: "comp2",
        category: "DataProduct",
        version: "1.0.0",
      },
      {
        provider: "sap.s4",
        name: "pkg1",
        spaces: [],
        componentId: "comp3",
        category: "DataProduct",
        version: "1.0.0",
      },
    ];
    const dspFolders: ACNFolder[] = [
      {
        packageName: "pkg1",
        packageVersion: "1.0.0",
        packageFolders: [
          { name: "spaceA", sourceId: "srcA", visible: true, url: "https://example.com/spaceA" },
          { name: "spaceB", sourceId: "srcB", visible: true, url: "https://example.com/spaceB" },
        ],
      },
      {
        packageName: "pkg2",
        packageVersion: "1.0.0",
        packageFolders: [{ name: "spaceC", sourceId: "srcC", visible: true, url: "https://example.com/spaceC" }],
      },
    ];

    await enrichWithSpacesData(context, contents, dspFolders);

    expect(contents[0].spaces).to.deep.equal([
      { name: "spaceA", sourceId: "srcA", visible: true, url: "https://example.com/spaceA" },
      { name: "spaceB", sourceId: "srcB", visible: true, url: "https://example.com/spaceB" },
    ]);
    expect(contents[1].spaces).to.deep.equal([
      { name: "spaceC", sourceId: "srcC", visible: true, url: "https://example.com/spaceC" },
    ]);
    expect(contents[2].spaces).to.deep.equal([]); // no enrichment for other provider
  });

  it("should not modify contents if dspFolders is undefined", async () => {
    const context = {} as any;
    const contents: BDCInstalledContent[] = [
      {
        provider: "sap.datasphere",
        name: "pkg1",
        spaces: [],
        componentId: "comp1",
        category: "DataProduct",
        version: "1.0.0",
      },
    ];
    await enrichWithSpacesData(context, contents, undefined);
    expect(contents[0].spaces).to.deep.equal([]);
  });

  it("should not enrich content if package names do not match", async () => {
    const context = {} as any;
    const contents: BDCInstalledContent[] = [
      {
        provider: "sap.datasphere",
        name: "pkg1",
        spaces: [],
        componentId: "comp2",
        category: "DataProduct",
        version: "1.0.0",
      },
    ];
    const dspFolders: ACNFolder[] = [
      {
        packageName: "otherPkg",
        packageVersion: "1.0.0",
        packageFolders: [{ name: "spaceX", sourceId: "srcX", visible: true, url: "https://example.com/spaceX" }],
      },
    ];
    await enrichWithSpacesData(context, contents, dspFolders);
    expect(contents[0].spaces).to.deep.equal([]);
  });

  it("should not enrich content if provider does not match dspProvider", async () => {
    const context = {} as any;
    const contents: BDCInstalledContent[] = [
      {
        provider: "sap.sf",
        name: "pkg1",
        spaces: [],
        componentId: "comp3",
        category: "DataProduct",
        version: "1.0.0",
      },
    ];
    const dspFolders: ACNFolder[] = [
      {
        packageName: "pkg1",
        packageVersion: "1.0.0",
        packageFolders: [{ name: "spaceY", sourceId: "srcY", visible: true, url: "https://example.com/spaceY" }],
      },
    ];
    await enrichWithSpacesData(context, contents, dspFolders);
    expect(contents[0].spaces).to.deep.equal([]);
  });
});

describe("getInstalledComponentsId", () => {
  const mockContext = { user: "test-user" } as any;

  it("should return empty array if no components have status DONE", () => {
    const installation = {
      componentsInstallationStatus: [
        { status: "PENDING", componentId: "comp1" },
        { status: "FAILED", componentId: "comp2" },
      ],
      originalPackage: {
        components: [],
      },
    } as any;

    const result = getInstalledComponentsId(mockContext, installation);
    expect(result).to.deep.equal([]);
  });

  it("should return array with empty string if matching original component not found", () => {
    const installation = {
      componentsInstallationStatus: [{ status: "DONE", componentId: "missing" }],
      originalPackage: {
        components: [],
      },
    } as any;

    const result = getInstalledComponentsId(mockContext, installation);
    expect(result).to.deep.equal([]);
  });

  it("should return ordid if category is DataProduct", () => {
    const installation = {
      componentsInstallationStatus: [{ status: "DONE", componentId: "dp1" }],
      originalPackage: {
        components: [
          {
            componentId: "dp1",
            category: "DataProduct",
            ordid: "ORD-XYZ",
          },
        ],
      },
    } as any;

    const result = getInstalledComponentsId(mockContext, installation);
    expect(result).to.deep.equal(["ORD-XYZ"]);
  });

  it("should return provider:name:majorVersion if category is not DataProduct", () => {
    const installation = {
      componentsInstallationStatus: [{ status: "DONE", componentId: "svc1" }],
      originalPackage: {
        components: [
          {
            componentId: "svc1",
            category: "Service",
            provider: "prov",
            name: "compX",
            version: "2.4.5",
          },
        ],
      },
    } as any;

    const result = getInstalledComponentsId(mockContext, installation);
    expect(result).to.deep.equal(["prov:compX:2"]);
  });

  it("should filter out missing components (returning empty string)", () => {
    const installation = {
      componentsInstallationStatus: [
        { status: "DONE", componentId: "exists" },
        { status: "DONE", componentId: "missing" },
      ],
      originalPackage: {
        components: [
          {
            componentId: "exists",
            category: "Service",
            provider: "P",
            name: "N",
            version: "1.2.3",
          },
        ],
      },
    } as any;

    const result = getInstalledComponentsId(mockContext, installation);
    expect(result).to.deep.equal(["P:N:1"]);
  });
});

describe("validateBdcPackageInstallationAndMessage", () => {
  const context = { user: "test-user" } as any;

  it("should throw an error if bdcPackageInstallation is null", () => {
    const message = {
      installationId: "inst-123",
      installationCyclesCount: 3,
      correlationId: "corr-abc",
    } as any;

    expect(() => {
      validateBdcPackageInstallationAndMessage(null as any, message, context);
    }).to.throw(`package installation "inst-123" not found`);
  });

  it("should throw MessageTooOldError if message cycle is less than or equal to current", () => {
    const message = {
      installationId: "inst-456",
      installationCyclesCount: 2,
      correlationId: "corr-def",
    } as any;

    const installation = {
      installationCyclesCount: 2,
    } as any;

    expect(() => {
      validateBdcPackageInstallationAndMessage(installation, message, context);
    }).to.throw(MessageTooOldError);
  });

  it("should not throw if message cycle is newer than current", () => {
    const message = {
      installationId: "inst-789",
      installationCyclesCount: 5,
      correlationId: "corr-ghi",
    } as any;

    const installation = {
      installationCyclesCount: 3,
    } as any;

    expect(() => {
      validateBdcPackageInstallationAndMessage(installation, message, context);
    }).to.not.throw();
  });
});

describe("convertInstallationStatusToComponentInstallationDetails", () => {
  it("should do nothing if componentsInstallationStatus is null", () => {
    expect(() => {
      convertInstallationStatusToComponentInstallationDetails(null);
    }).to.not.throw();
  });

  it("should do nothing if componentsInstallationStatus is empty array", () => {
    const input: any[] = [];
    convertInstallationStatusToComponentInstallationDetails(input);
    expect(input).to.deep.equal([]);
  });

  it("should do nothing if no object has 'installationStatus' property", () => {
    const input: any[] = [{ someOtherProp: 1 }];
    convertInstallationStatusToComponentInstallationDetails(input);
    expect(input).to.deep.equal([{ someOtherProp: 1 }]);
  });

  it("should copy installationStatus to componentInstallationDetails and remove installationStatus", () => {
    const input: any[] = [
      {
        componentId: "abc",
        installationStatus: { progress: 80 },
      },
    ];
    convertInstallationStatusToComponentInstallationDetails(input);
    expect(input[0].componentInstallationDetails).to.deep.equal({ progress: 80 });
    expect(input[0].installationStatus).to.be.undefined;
  });

  it("should skip elements where installationStatus is falsy", () => {
    const input: any[] = [
      {
        componentId: "x",
        installationStatus: null,
      },
    ];
    convertInstallationStatusToComponentInstallationDetails(input);
    expect(input[0]).to.have.property("installationStatus", null);
    expect(input[0]).to.not.have.property("componentInstallationDetails");
  });
});

describe("findAdditionalInstallationPropertyByName", () => {
  it("should return the matching property by name", () => {
    const properties: AdditionalInstallationProperty[] = [
      { name: AdditionalInstallationPropertyName.fosInstallUrl, value: "url" },
      { name: AdditionalInstallationPropertyName.fosSystemAssignmentId, value: "sys-id" },
    ];

    const result = findAdditionalInstallationPropertyByName(
      AdditionalInstallationPropertyName.fosSystemAssignmentId,
      properties
    );

    expect(result).to.deep.equal({
      name: AdditionalInstallationPropertyName.fosSystemAssignmentId,
      value: "sys-id",
    });
  });

  it("should return undefined if property with name not found", () => {
    const properties: AdditionalInstallationProperty[] = [
      { name: AdditionalInstallationPropertyName.fosInstallUrl, value: "url" },
    ];

    const result = findAdditionalInstallationPropertyByName(
      AdditionalInstallationPropertyName.selfHealingCustomTimeout,
      properties
    );

    expect(result).to.be.undefined;
  });

  it("should return undefined if additionalInstallationProperties is undefined", () => {
    const result = findAdditionalInstallationPropertyByName(
      AdditionalInstallationPropertyName.fosInstallUrl,
      undefined as any
    );
    expect(result).to.be.undefined;
  });

  it("should return undefined if additionalInstallationProperties is empty", () => {
    const result = findAdditionalInstallationPropertyByName(AdditionalInstallationPropertyName.fosInstallUrl, []);
    expect(result).to.be.undefined;
  });
});
