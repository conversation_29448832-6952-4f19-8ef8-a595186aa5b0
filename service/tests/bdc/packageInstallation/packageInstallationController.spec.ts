/** @format */

import { oAuthUtil } from "@sap/dwc-http-client";
import chai, { expect } from "chai";
import chaiAsPromised from "chai-as-promised";
import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import sinon from "sinon";
import sinon<PERSON>hai from "sinon-chai";
import { MISSING_ENTITLEMENT } from "../../../../shared/bdccockpit/Constants";
import {
  BDCPackageCategory,
  BDCPackageStatus,
  BDCPackageType,
  BDCUninstallationOperation,
  SourceSystemType,
} from "../../../../shared/bdccockpit/Enums";
import * as entitlementValidator from "../../../bdc/entitlement/entitlementValidator";
import { FormationService } from "../../../bdc/formation/formationService";
import { InstallationHealth } from "../../../bdc/lifecycleManagement/installationHealth";
import { InstallationManager } from "../../../bdc/lifecycleManagement/installationManager";
import { UninstallationManager } from "../../../bdc/lifecycleManagement/uninstallationManager";
import * as packageInstallationController from "../../../bdc/packageInstallation/packageInstallationController";
import * as pifcop from "../../../bdc/packageInstallation/packageInstallationFlowCommonProperties";
import { PackageInstallationService } from "../../../bdc/packageInstallation/packageInstallationService";
import * as packageInstallationUtils from "../../../bdc/packageInstallation/packageInstallationUtils";
import { PackageUtils } from "../../../bdc/packages/packageUtils";
import { PackagesDao } from "../../../bdc/packages/packagesDao";
import * as packagesManager from "../../../bdc/packages/packagesManager";
import { PackagesService } from "../../../bdc/packages/packagesService";
import { BDCPackage } from "../../../bdc/packages/packagesTypes";
import * as applicationNamespaceUtils from "../../../bdc/systems/applicationNamespaceUtils";
import { SystemsService } from "../../../bdc/systems/systemsService";
import { BDCExceptionType } from "../../../bdc/utils/BDCException";
import { notificationsHelper } from "../../../bdc/utils/notificationUtils";
import * as errorResponse from "../../../server/errorResponse";

chai.use(sinonChai);
chai.use(chaiAsPromised);

const TENANT_ID = "awesome-tenant-id";
const OAUTH_TOKEN = "awesome-token";
const PACKAGE: BDCPackage = {
  applicationComponent: "",
  category: BDCPackageCategory.HUMAN_RESOURCES,
  components: [],
  creationDate: "",
  description: "",
  fileProducer: "",
  id: "",
  name: "",
  preview: [],
  requiredApplications: [],
  schemaName: "",
  schemaVersion: "",
  shortDescription: "",
  type: BDCPackageType.DATA_PACKAGE,
  version: "",
};

describe("packageInstallationController", () => {
  let req: any;
  let res: Partial<Response>;
  let sendStub: sinon.SinonStub;
  let statusStub: sinon.SinonStub;
  let sendErrorResponseStub: sinon.SinonStub;

  beforeEach(() => {
    req = {
      context: {
        tenantId: TENANT_ID,
        isFeatureFlagActive: sinon.stub().resolves(true),
      },
      body: {},
      params: {},
      query: {},
    };
    sendStub = sinon.stub();
    statusStub = sinon.stub().returns({ send: sendStub });
    res = {
      status: statusStub,
      send: sendStub,
    };

    sendErrorResponseStub = sinon.stub(errorResponse, "sendErrorResponse");
    sinon.stub(PackagesService, "getPackageById").resolves(PACKAGE);
  });

  afterEach(() => {
    sinon.restore();
  });

  describe("installPackage", () => {
    let installPackageStub: sinon.SinonStub;
    let pifcopMock: any;
    const installPackage = async () =>
      await packageInstallationController.installPackage(req as Request, res as Response);

    beforeEach(() => {
      pifcopMock = {
        setS4InstallationProperties: sinon.stub().resolves(),
        getNewInstallationPropertiesBySystemsOfFormation: sinon.stub().resolves(),
      };
      sinon
        .stub(pifcop, "createPackageInstallationFlowCommonProperties")
        .callsFake((context, installationProperties) => {
          pifcopMock = { context, installationProperties, ...pifcopMock };
          return pifcopMock;
        });
      sinon.stub(PackagesDao, "getConfigurationRepoBranches").callsFake((context: any) => {
        return Promise.resolve(["prodProd"]);
      });
      sinon.stub(oAuthUtil, "getOAuthToken").callsFake(async (_opts: any) => {
        return OAUTH_TOKEN;
      });
      sinon.stub(SystemsService, "checkFosConnection").resolves({ success: true });
      sinon.stub(notificationsHelper as any, "sendNotification").resolves();
      sinon.stub(notificationsHelper as any, "sendNotificationForTechUser").resolves();
      installPackageStub = sinon.stub(InstallationManager, "installPackage").resolves("test-installation-id");
    });

    it("should succeed by default (installing GA packages)", async () => {
      await installPackage();
      expect(statusStub).to.have.been.calledWith(200);
    });

    describe("check connection to FOS", async () => {
      beforeEach(() => {
        pifcopMock.s4InstallationFormation = { assignmentId: "test-assignment-id" };
      });

      it("should succeed", async () => {
        await installPackage();
        expect(statusStub).to.have.been.calledWith(200);
        expect(sendStub).to.have.been.calledWith({
          connectionCheckResult: { success: true },
          installationId: "test-installation-id",
        });
      });

      describe("when FOS connection check fails", () => {
        beforeEach(() => {
          (SystemsService.checkFosConnection as sinon.SinonStub).resolves({ success: false });
        });

        it("should send an error response", async () => {
          await installPackage();
          expect(sendErrorResponseStub).to.have.been.calledWith(
            sinon.match.any,
            "failed to install package",
            sinon.match.any
          );
        });
      });

      describe("when the FOS connection is indeterministic", () => {
        beforeEach(() => {
          (SystemsService.checkFosConnection as sinon.SinonStub).resolves(undefined);
        });

        it("should succeed without connection info", async () => {
          await installPackage();
          expect(statusStub).to.have.been.calledWith(200);
          expect(sendStub).to.have.been.calledWith({
            connectionCheckResult: undefined,
            installationId: "test-installation-id",
          });
        });
      });
    });

    it("should send the missing entitlement details when the entitlement check fails", async () => {
      installPackageStub.resolves(MISSING_ENTITLEMENT);
      const entitlementDetails = { gracePeriodStart: null, gracePeriodDuration: 0 };
      sinon.stub(entitlementValidator, "getMissingEntitlementDetails").resolves(entitlementDetails);
      await installPackage();
      expect(sendStub).to.have.been.calledWith({ missingEntitlementDetails: entitlementDetails });
    });
  });

  describe("cancelInstallation", () => {
    let cancelInstallationStub: sinon.SinonStub;
    const cancelInstallation = async () =>
      await packageInstallationController.cancelInstallation(req as Request, res as Response);

    beforeEach(() => {
      req.params = { installationId: "test-installation-id" };
      req.body = { cancellationRequested: true };
      cancelInstallationStub = sinon.stub(PackageInstallationService, "cancelInstallation").resolves();
    });

    it("should call PackageInstallationService.cancelInstallation with cancellationRequested and return 200", async () => {
      await cancelInstallation();
      expect(cancelInstallationStub).to.have.been.calledWith(req.context, "test-installation-id", true);
      expect(statusStub).to.have.been.calledWith(StatusCodes.OK);
      expect(sendStub).to.have.been.calledWith({ message: "Cancellation accepted" });
    });

    it("should call sendErrorResponse if PackageInstallationService.cancelInstallation throws (CancellationFailure)", async () => {
      const codedError = new errorResponse.CodedError(
        BDCExceptionType.CancellationFailure,
        "No components were in a cancelable state.",
        StatusCodes.CONFLICT
      );
      cancelInstallationStub.rejects(codedError);

      await cancelInstallation();
      expect(sendErrorResponseStub).to.have.been.calledWith(req.context, "cancelInstallation", { err: codedError });
    });
  });

  describe("uninstallPackage", () => {
    let uninstallPackageStub: sinon.SinonStub;
    const uninstallPackage = async () =>
      await packageInstallationController.uninstallPackage(req as Request, res as Response);

    beforeEach(() => {
      req.query = { operation: BDCUninstallationOperation.UNINSTALL };
      uninstallPackageStub = sinon.stub(UninstallationManager, "uninstallPackage").resolves({} as any);
      sinon.stub(notificationsHelper as any, "sendNotification").resolves();
      sinon.stub(notificationsHelper as any, "sendNotificationForTechUser").resolves();
      sinon.stub(oAuthUtil, "getOAuthToken").callsFake(async (_opts: any) => {
        return OAUTH_TOKEN;
      });
    });

    it("should succeed by default", async () => {
      await uninstallPackage();
      expect(statusStub).to.have.been.calledWith(200);
      expect(uninstallPackageStub).to.have.been.calledWith({
        context: sinon.match.any,
        packageId: sinon.match.any,
        operation: req.query.operation,
        requiredFeatureFlags: {
          [BDCUninstallationOperation.RETRY]: ["DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP"],
          [BDCUninstallationOperation.CLEANUP]: ["DWCO_BDC_FAILED_PACKAGE_MANUAL_OFFBOARDING_RETRY_AND_CLEANUP"],
          [BDCUninstallationOperation.UNINSTALL]: ["DWCO_BDC_MANUAL_OFFBOARDING"],
        },
      });
    });

    describe("when the uninstall fails", () => {
      beforeEach(() => {
        uninstallPackageStub.rejects(new Error("Uninstall failed"));
      });

      it("should send an error response", async () => {
        await uninstallPackage();
        expect(sendErrorResponseStub).to.have.been.calledWith(
          sinon.match.any,
          "failed to uninstall package",
          sinon.match.any
        );
      });
    });
  });

  describe("reinstallPackage", () => {
    let retryPackageStub: sinon.SinonStub;

    const reinstallPackage = async () =>
      await packageInstallationController.reinstallPackage(req as Request, res as Response);

    beforeEach(() => {
      req.params = { installationId: "test-installation-id" };
      req.query = { retry: "true" };
      retryPackageStub = sinon.stub(InstallationManager, "retryPackage").resolves({
        installationId: "test-installation-id",
        connectionCheckResult: { success: true },
      });
    });

    it("should succeed by default", async () => {
      await reinstallPackage();
      expect(statusStub).to.have.been.calledWith(200);
      expect(sendStub).to.have.been.calledWith({
        installationId: "test-installation-id",
        connectionCheckResult: { success: true },
      });
    });

    describe("when it is an update request", () => {
      let updatePackageStub: sinon.SinonStub;

      beforeEach(() => {
        req.query.retry = "false";
        updatePackageStub = sinon.stub(InstallationManager, "updatePackage").resolves({
          installationId: "test-installation-id",
          connectionCheckResult: { success: true },
        });
      });

      it("should call updatePackage", async () => {
        await reinstallPackage();
        expect(updatePackageStub).to.have.been.calledWith(sinon.match.any, "test-installation-id");
      });
    });

    describe("when reinstallPackage fails", () => {
      beforeEach(() => {
        retryPackageStub.rejects(new Error("Reinstall failed"));
      });

      it("should send an error response", async () => {
        await reinstallPackage();
        expect(sendErrorResponseStub).to.have.been.calledWith(
          sinon.match.any,
          `failed to retry the package installation, installationId: test-installation-id`,
          sinon.match.any
        );
      });
    });

    describe("when connection check result is undefined", () => {
      beforeEach(() => {
        retryPackageStub.resolves({
          installationId: "test-installation-id",
          connectionCheckResult: undefined,
        });
      });

      it("should succeed without connection info", async () => {
        await reinstallPackage();
        expect(sendStub).to.have.been.calledWith({
          installationId: "test-installation-id",
        });
      });
    });
  });

  describe("deletePackage", () => {
    let forceDeletePackageInstallationStub: sinon.SinonStub;

    const deletePackage = async () =>
      await packageInstallationController.deletePackage(req as Request, res as Response);

    const expectSuccess = () => {
      expect(statusStub).to.have.been.calledWith(200);
      expect(sendStub).to.have.been.calledWith({
        installationId: req.params.installationId,
      });
    };

    beforeEach(() => {
      req.params = { installationId: "test-installation-id" };
      forceDeletePackageInstallationStub = sinon
        .stub(UninstallationManager, "forceDeletePackageInstallation")
        .resolves({
          installationId: req.params.installationId,
        });
    });

    it("should call forceDeletePackageInstallation", async () => {
      req.query = {};
      await deletePackage();
      expect(forceDeletePackageInstallationStub).to.have.been.calledWith(req.context, "test-installation-id");
      expectSuccess();
    });
  });

  describe("getInstalledPackageByInstallationId", () => {
    let getPackageInstallationStub: sinon.SinonStub;
    let fakeInstalledPackage: any;

    const getInstalledPackageByInstallationId = async () =>
      await packageInstallationController.getInstalledPackageByInstallationId(req as Request, res as Response);

    beforeEach(() => {
      req.params = { installationId: "test-installation-id" };
      fakeInstalledPackage = {
        originalPackage: {
          id: "pkg1",
          components: [
            {
              componentId: "component1",
              name: "Test Package",
              category: "DataProduct",
              provider: "sap.s4",
              version: "1.0",
            },
          ],
        },
        installationProperties: [],
        componentsInstallationStatus: [
          {
            componentId: "component1",
            componentInstallationDetails: {} as any,
            startTime: new Date("2024-06-10T00:00:00Z"),
            endTime: new Date("2024-06-10T01:00:00Z"),
            status: "FAILED",
            message: "Installation failed",
            retryCount: 0,
          },
        ],
      };
      const fakePackages = [{ id: "pkg1" }];

      getPackageInstallationStub = sinon
        .stub(PackageInstallationService, "getPackageInstallation")
        .resolves(fakeInstalledPackage);
      sinon.stub(PackagesService, "getPackages").resolves(fakePackages as any);
      sinon.stub(PackageUtils, "getComponentsByType").resolves({} as any);
      sinon.stub(FormationService, "getFormationContextAvailableForInstallationPackage").resolves([]);
      sinon.stub(packageInstallationUtils, "getInsideAppLinksArr").returns([]);
      sinon.stub(packageInstallationUtils, "isSameMajorDifferentVersion").returns(true);
      sinon.stub(packageInstallationUtils, "extractStatusFromPackageInstallation").returns(BDCPackageStatus.INSTALLED);
      sinon.stub(packageInstallationUtils, "getMillisecondsOfDate").returns("123456900");
      sinon.stub(packagesManager, "getSystemNameByApplicationNamespace").returns(SourceSystemType.S4);
      sinon.stub(applicationNamespaceUtils, "isApplicationNamespaceOfSourceType").returns(true);
    });

    it("should return the installed package when found", async () => {
      await getInstalledPackageByInstallationId();

      expect(statusStub).to.have.been.calledWith(200);
      expect(sendStub).to.have.been.calledWith([
        {
          correlationId: undefined,
          installationId: undefined,
          insightAppLinks: [],
          workspaceLinks: [],
          isPackageUpdateAvailable: true,
          isBdcPackageAvailable: true,
          status: "active",
          installedUTC: "123456900",
          systems: [],
          products: undefined,
          contents: undefined,
          id: "pkg1",
          name: undefined,
          preview: undefined,
          creationDate: undefined,
          type: undefined,
          systemType: "SAP S/4 HANA Cloud",
          version: undefined,
          category: undefined,
          description: undefined,
          supportedSystemVersion: undefined,
          sourceSystem: {
            applicationNamespace: undefined,
            tenant: undefined,
            name: undefined,
            version: undefined,
          },
          installationLocationName: undefined,
          componentsInstallationStatus: [
            {
              ...fakeInstalledPackage.originalPackage.components[0],
              ...fakeInstalledPackage.componentsInstallationStatus[0],
            },
          ],
          componentsUninstallationStatus: undefined,
        },
      ]);
    });

    it("should send error response if an error is thrown", async () => {
      getPackageInstallationStub.rejects(new Error("Some error"));
      await getInstalledPackageByInstallationId();
      expect(sendErrorResponseStub).to.have.been.calledWith(
        req.context,
        "getInstalledPackageByInstallationId",
        sinon.match.any
      );
    });
  });

  describe("getInstalledPackages", () => {
    let updateGracePeriodStub: sinon.SinonStub;

    const getInstalledPackages = async () =>
      await packageInstallationController.getInstalledPackages(req as Request, res as Response);

    beforeEach(() => {
      const fakePackages = [{ id: "pkg1" }];

      sinon.stub(PackagesService, "getPackages").resolves(fakePackages as any);
      sinon.stub(PackageInstallationService, "getFlattenedInstalledPackages").resolves([
        {
          originalPackage: { id: fakePackages[0].id, name: "Test Package" } as any,
          installationProperties: [],
          correlationId: "corr-id-123",
          installationId: "inst-id-123",
          startTime: new Date("2024-06-10T00:00:00Z"),
          lastModifiedTime: new Date("2024-06-10T00:00:00Z"),
        },
        {
          originalPackage: { id: "pkg2", name: "Another Package", type: BDCPackageType.DATA_PACKAGE } as any,
          installationProperties: [{ applicationNamespace: "sap.datasphere", systemTenant: "test-tenant" }],
          correlationId: "corr-id-123",
          installationId: "inst-id-123",
          startTime: new Date("2024-06-10T00:00:00Z"),
          lastModifiedTime: new Date("2024-06-10T00:00:00Z"),
        },
      ] as any);
      sinon.stub(PackageUtils, "getComponentsByType").resolves({} as any);
      sinon.stub(packageInstallationUtils, "getSourceSystem").returns({
        applicationNamespace: "sap.s4",
        tenant: "test-tenant",
        name: "Test System",
        version: "1.0",
      });
      sinon.stub(packageInstallationUtils, "isSameMajorDifferentVersion").returns(true);
      sinon.stub(packageInstallationUtils, "extractStatusFromPackageInstallation").returns(BDCPackageStatus.INSTALLED);
      sinon.stub(packageInstallationUtils, "getMillisecondsOfDate").returns("123456900");
      sinon.stub(packagesManager, "getSystemNameByApplicationNamespace").returns(SourceSystemType.S4);
      sinon.stub(SystemsService, "getAllSystems").returns([{ applicationTenantId: "test-tenant" }] as any);
      sinon.stub(InstallationHealth, "installationHealth");

      updateGracePeriodStub = sinon
        .stub(entitlementValidator, "updateGracePeriodForInstalledPackagesByEntitlement")
        .callsFake(async (_ctx, pkgs) => pkgs);
    });

    afterEach(() => {
      sinon.restore();
    });

    it("should return installed packages and update grace period if feature flag is active", async () => {
      await getInstalledPackages();
      expect(updateGracePeriodStub).to.have.been.called;
      expect(sendStub).to.have.been.calledWith([
        {
          correlationId: "corr-id-123",
          installationId: "inst-id-123",
          isPackageUpdateAvailable: true,
          status: "active",
          installedUTC: "123456900",
          sourceSystem: {
            applicationNamespace: "sap.s4",
            tenant: "test-tenant",
            name: "Test System",
            version: "1.0",
          },
          id: "pkg1",
          name: "Test Package",
          creationDate: undefined,
          type: undefined,
          systemType: "SAP S/4 HANA Cloud",
          version: undefined,
          category: undefined,
          description: undefined,
          lastModifiedTime: new Date("2024-06-10T00:00:00Z"),
          installLocation: "SAP Datasphere(-), SAP Analytics Cloud(-)",
        },
        {
          correlationId: "corr-id-123",
          installationId: "inst-id-123",
          isPackageUpdateAvailable: false,
          status: "active",
          installedUTC: "123456900",
          sourceSystem: {
            applicationNamespace: "sap.s4",
            tenant: "test-tenant",
            name: "Test System",
            version: "1.0",
          },
          id: "pkg2",
          name: "Another Package",
          creationDate: undefined,
          type: "DataPackage",
          systemType: "SAP S/4 HANA Cloud",
          version: undefined,
          category: undefined,
          description: undefined,
          lastModifiedTime: new Date("2024-06-10T00:00:00Z"),
        },
      ]);
    });

    it("should not call updateGracePeriodForInstalledPackagesByEntitlement if feature flag is inactive", async () => {
      (req.context.isFeatureFlagActive as sinon.SinonStub).resolves(false);
      const updateGracePeriodIfStartedStub: sinon.SinonStub = sinon
        .stub(entitlementValidator, "updateGracePeriodIfStarted")
        .resolves();
      await getInstalledPackages();
      expect(updateGracePeriodIfStartedStub).not.to.have.been.called;
      expect(sendStub).to.have.been.called;
    });

    it("should send error response if an error is thrown", async () => {
      (PackagesService.getPackages as sinon.SinonStub).rejects(new Error("Failed to get packages"));
      await getInstalledPackages();
      expect(sendErrorResponseStub).to.have.been.calledWith(req.context, "getInstalledPackagesFailed", sinon.match.any);
    });
  });

  describe("getSystemLandscape", () => {
    let getAllSystemsStub: sinon.SinonStub;

    const getSystemLandscape = async () =>
      await packageInstallationController.getSystemLandscape(req as Request, res as Response);

    beforeEach(() => {
      getAllSystemsStub = sinon
        .stub(SystemsService, "getAllSystems")
        .returns([{ applicationTenantId: "test-tenant" }] as any);
    });

    it("should return system landscape as is", async () => {
      await getSystemLandscape();
      expect(sendStub).to.have.been.calledWith([{ applicationTenantId: "test-tenant" }]);
    });

    it("should send error response if getAllSystems throws", async () => {
      getAllSystemsStub.rejects(new Error("Failed to get systems"));
      await getSystemLandscape();
      expect(sendErrorResponseStub).to.have.been.calledWith(req.context, "getSystemLandscape", sinon.match.any);
    });
  });
});
