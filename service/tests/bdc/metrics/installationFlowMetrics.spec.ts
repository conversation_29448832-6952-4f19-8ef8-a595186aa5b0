/**
 * @format
 */
import sinon from "sinon";
import { BDCPackageType } from "../../../../shared/bdccockpit/Enums";
import { BDCRequestContext } from "../../../../shared/bdccockpit/Types";
import { TenantSKU } from "../../../bdc/entitlement/entitlementTypes";
import { InstallationManager } from "../../../bdc/lifecycleManagement/installationManager";
import * as metricsUtils from "../../../bdc/metrics/metricsUtils";
import { PackageInstallationService } from "../../../bdc/packageInstallation/packageInstallationService";
import { BDCPackageInstallation } from "../../../bdc/packageInstallation/packageInstallationTypes";
import { PackageInstallationValidator } from "../../../bdc/packages/validation/packageInstallationValidator";
import { testMetricSending } from "./testMetricSending";

describe("installationManager", () => {
  beforeEach(() => {
    sinon.stub(InstallationManager as any, "doDeletePackageInstallation");
    sinon.stub(PackageInstallationService, "getPackageInstallation");
    sinon.stub(PackageInstallationValidator, "validatePackageReinstallRequest");
    sinon.stub(InstallationManager as any, "doInstallPackage").resolves("installationId");
  });

  after(() => {
    sinon.restore();
  });

  describe("endInstallation", () => {
    let sendMetricStub: sinon.SinonStub;
    let mockContext: BDCRequestContext;
    let mockPackageInstallation: BDCPackageInstallation;

    beforeEach(() => {
      sendMetricStub = sinon.stub(metricsUtils, "invokeMetricsSafely");
      mockContext = { tenantId: "testTenant" } as BDCRequestContext;
      mockPackageInstallation = {
        originalPackage: {
          systemType: "SAP S/4 HANA Cloud",
          type: BDCPackageType.INSIGHT_APPLICATION,
        },
        installationProperties: [
          {
            applicationNamespace: "sap.s4",
          },
          {
            applicationNamespace: "sap.datasphere",
          },
        ],
      } as BDCPackageInstallation;
    });

    afterEach(() => {
      sinon.restore();
    });

    const testMetrics = async (isFailed: boolean, expectedStatus: "Failed" | "Successful") => {
      await InstallationManager.endInstallation(mockContext, mockPackageInstallation, isFailed);
      await testMetricSending(
        mockContext,
        sendMetricStub,
        [mockPackageInstallation.originalPackage.type, expectedStatus, TenantSKU.S4, mockContext],
        "incrementBdcPackageInstallations"
      );
    };

    it("should send metric with correct dimension if installation failed", async () => {
      await testMetrics(true, "Failed");
    });

    it("should send metric with correct dimension if installation succeeded", async () => {
      await testMetrics(false, "Successful");
    });
  });
});
