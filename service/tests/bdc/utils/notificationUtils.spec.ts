/** @format */

import { IRequestContext, NotificationType } from "@sap/deepsea-types";
import { NotificationsClient } from "@sap/dwc-notifications";
import { expect } from "chai";
import sinon from "sinon";
import { BDCPackageType } from "../../../../shared/bdccockpit/Enums";
import { BDCPackage } from "../../../bdc/packages/packagesTypes";
import { notificationsHelper } from "../../../bdc/utils/notificationUtils";
import { MessageAction } from "../../../messageQueuing/consumer/PackageInstallationConsumer";

describe("notificationUtils Tests", () => {
  let notificationsClientPostStub: sinon.SinonStub;
  let notificationsClientPostForTechUserStub: sinon.SinonStub;
  const mockContext = { userInfo: { userName: "testUser" } } as IRequestContext;

  beforeEach(() => {
    notificationsClientPostStub = sinon.stub(NotificationsClient, "post");
    notificationsClientPostForTechUserStub = sinon.stub(NotificationsClient, "postForTechUser");
  });

  afterEach(() => {
    sinon.restore();
  });

  describe("Error handling for notifications", () => {
    it("should not affect business flow if NotificationsClient.post throws an error", async () => {
      const bdcPackage = { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE } as BDCPackage;

      notificationsClientPostStub.rejects(new Error("Test error"));

      expect(() =>
        notificationsHelper.sendOnboardStartedNotification({
          context: mockContext,
          packageInfo: bdcPackage,
        })
      ).to.not.throw();

      expect(notificationsClientPostStub.calledOnce, "NotificationsClient.post should be called once").to.be.true;
    });

    it("should not affect business flow if NotificationsClient.postForTechUser throws an error", async () => {
      const packageInstallation = {
        originalPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE },
        installationId: "testInstallationId",
      } as any;

      notificationsClientPostForTechUserStub.rejects(new Error("Test error"));

      expect(() =>
        notificationsHelper.sendOffboardFinishedNotification({
          context: mockContext,
          notificationType: NotificationType.SUCCESS,
          packageInstallation,
          action: MessageAction.UNINSTALL,
        })
      ).to.not.throw();
      expect(() =>
        notificationsHelper.sendOnboardFinishedNotification({
          context: mockContext,
          notificationType: NotificationType.SUCCESS,
          packageInstallation,
        })
      ).to.not.throw();

      expect(
        notificationsClientPostForTechUserStub.calledTwice,
        "NotificationsClient.postForTechUser should be called twice"
      ).to.be.true;
    });
  });

  describe("sendOnboardStartedNotification", () => {
    let sendNotificationStub: sinon.SinonStub;
    beforeEach(() => {
      sendNotificationStub = sinon.stub(notificationsHelper as any, "sendNotification");
    });

    const testCases = [
      {
        description: "should send correct notification for data package",
        bdcPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE } as BDCPackage,
        expectedTitle: "Starting activation of TestDataPackage data package.",
      },
      {
        description: "should send correct notification for intelligent application",
        bdcPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION } as BDCPackage,
        expectedTitle: "Starting installation of TestIntelligentApp intelligent application.",
      },
      {
        description: "should send correct update notification for data package",
        bdcPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE } as BDCPackage,
        isUpdating: true,
        expectedTitle: "Update of TestDataPackage data package has started.",
      },
      {
        description: "should send correct update notification for intelligent application",
        bdcPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION } as BDCPackage,
        isUpdating: true,
        expectedTitle: "Update of TestIntelligentApp intelligent application has started.",
      },
    ];

    testCases.forEach(({ description, bdcPackage, isUpdating, expectedTitle }) => {
      it(description, async () => {
        await notificationsHelper.sendOnboardStartedNotification({
          context: mockContext,
          packageInfo: bdcPackage,
          isUpdating,
        });
        expect(sendNotificationStub.calledOnce, "sendNotification should be called once").to.be.true;
        const actualArgs = sendNotificationStub.firstCall.args[0];
        expect(actualArgs).to.deep.equal({
          context: mockContext,
          type: NotificationType.SUCCESS,
          title: expectedTitle,
        });
      });
    });
  });

  describe("sendOnboardFinishedNotification", () => {
    let sendNotificationForTechUserStub: sinon.SinonStub;
    beforeEach(() => {
      sendNotificationForTechUserStub = sinon.stub(notificationsHelper as any, "sendNotificationForTechUser");
    });

    const testCases = [
      {
        description: "should send success notification for data package activation",
        packageInstallation: {
          originalPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.SUCCESS,
        expectedTitle: "TestDataPackage data package has been activated.",
      },
      {
        description: "should send failure notification for data package activation",
        packageInstallation: {
          originalPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.ALERT,
        expectedTitle: "Activation of TestDataPackage data package failed. Please try again later.",
      },
      {
        description: "should send success notification for intelligent application installation",
        packageInstallation: {
          originalPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.SUCCESS,
        expectedTitle: "TestIntelligentApp intelligent application has been installed.",
      },
      {
        description: "should send failure notification for intelligent application installation",
        packageInstallation: {
          originalPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.ALERT,
        expectedTitle: "Installation of TestIntelligentApp intelligent application failed. Please try again later.",
      },
      {
        description: "should send success notification for data package update",
        packageInstallation: {
          originalPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.SUCCESS,
        isUpdating: true,
        expectedTitle: "Update of TestDataPackage data package has been completed.",
      },
      {
        description: "should send failure notification for data package update",
        packageInstallation: {
          originalPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.ALERT,
        isUpdating: true,
        expectedTitle: "Update of TestDataPackage data package has been failed.",
      },
      {
        description: "should send success notification for intelligent application update",
        packageInstallation: {
          originalPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.SUCCESS,
        isUpdating: true,
        expectedTitle: "Update of TestIntelligentApp intelligent application has been completed.",
      },
      {
        description: "should send failure notification for intelligent application update",
        packageInstallation: {
          originalPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.ALERT,
        isUpdating: true,
        expectedTitle: "Update of TestIntelligentApp intelligent application has been failed.",
      },
    ];

    testCases.forEach(({ description, packageInstallation, notificationType, isUpdating, expectedTitle }) => {
      it(description, async () => {
        await notificationsHelper.sendOnboardFinishedNotification({
          context: mockContext,
          notificationType,
          packageInstallation,
          isUpdating,
        });

        expect(sendNotificationForTechUserStub.calledOnce, "sendNotificationForTechUser should be called once").to.be
          .true;
        const actualArgs = sendNotificationForTechUserStub.firstCall.args[0];
        expect(actualArgs).to.deep.equal({
          context: mockContext,
          type: notificationType,
          title: expectedTitle,
          installationId: packageInstallation.installationId,
        });
      });
    });
  });

  describe("sendOffboardStartedNotification", () => {
    let sendNotificationStub: sinon.SinonStub;
    beforeEach(() => {
      sendNotificationStub = sinon.stub(notificationsHelper as any, "sendNotification");
    });

    const testCases = [
      {
        description: "should send correct notification for data package deactivation",
        bdcPackageInstallation: {
          originalPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE },
        } as any,
        action: MessageAction.UNINSTALL,
        expectedTitle: "Starting deactivation of TestDataPackage data package.",
      },
      {
        description: "should send correct notification for intelligent application uninstallation",
        bdcPackageInstallation: {
          originalPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION },
        } as any,
        action: MessageAction.UNINSTALL,
        expectedTitle: "Starting uninstallation of TestIntelligentApp intelligent application.",
      },
      {
        description: "should send correct notification for cleanup activation",
        bdcPackageInstallation: {
          originalPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE },
        } as any,
        action: MessageAction.CLEANUP,
        expectedTitle: "Starting cleanup of artifacts deployed during failed activation.",
      },
      {
        description: "should send correct notification for retry deactivation",
        bdcPackageInstallation: {
          originalPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE },
        } as any,
        action: MessageAction.RETRY_UNINSTALL,
        expectedTitle: "Starting deactivation retry. Please do not perform other actions till retry finishes.",
      },
      {
        description: "should send correct notification for retry uninstall",
        bdcPackageInstallation: {
          originalPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION },
        } as any,
        action: MessageAction.RETRY_UNINSTALL,
        expectedTitle: "Starting uninstallation retry. Please do not perform other actions till retry finishes.",
      },
      {
        description: "should send correct notification for cleanup installation",
        bdcPackageInstallation: {
          originalPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION },
        } as any,
        action: MessageAction.CLEANUP,
        expectedTitle: "Starting cleanup of artifacts deployed during failed installation.",
      },
    ];

    testCases.forEach(({ description, bdcPackageInstallation, action, expectedTitle }) => {
      it(description, async () => {
        await notificationsHelper.sendOffboardStartedNotification({
          context: mockContext,
          packageInstallation: bdcPackageInstallation,
          action,
        });
        expect(sendNotificationStub.calledOnce, "sendNotification should be called once").to.be.true;
        const actualArgs = sendNotificationStub.firstCall.args[0];
        expect(actualArgs).to.deep.equal({
          context: mockContext,
          type: NotificationType.SUCCESS,
          title: expectedTitle,
        });
      });
    });
  });

  describe("sendOffboardFinishedNotification", () => {
    let sendNotificationForTechUserStub: sinon.SinonStub;
    beforeEach(() => {
      sendNotificationForTechUserStub = sinon.stub(notificationsHelper as any, "sendNotificationForTechUser");
    });

    const testCases = [
      {
        description: "should send success notification for retry uninstall",
        packageInstallation: {
          originalPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.SUCCESS,
        action: MessageAction.RETRY_UNINSTALL,
        expectedTitle: "Intelligent application has been uninstalled.",
      },
      {
        description: "should send failure notification for retry uninstall",
        packageInstallation: {
          originalPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.ALERT,
        action: MessageAction.RETRY_UNINSTALL,
        expectedTitle: "Uninstallation retry failed. Try again later.",
      },
      {
        description: "should send success notification for cleanup installation",
        packageInstallation: {
          originalPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.SUCCESS,
        action: MessageAction.CLEANUP,
        expectedTitle: "All artifacts deployed during failed installation have been removed.",
      },
      {
        description: "should send failure notification for cleanup installation",
        packageInstallation: {
          originalPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.ALERT,
        action: MessageAction.CLEANUP,
        expectedTitle: "Cleanup failed. Try again later.",
      },
      {
        description: "should send success notification for data package retry deactivation",
        packageInstallation: {
          originalPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.SUCCESS,
        action: MessageAction.RETRY_UNINSTALL,
        expectedTitle: "Data package has been deactivated.",
      },
      {
        description: "should send failure notification for data package retry deactivation",
        packageInstallation: {
          originalPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.ALERT,
        action: MessageAction.RETRY_UNINSTALL,
        expectedTitle: "Deactivation retry failed. Try again later.",
      },
      {
        description: "should send success notification for cleanup activation",
        packageInstallation: {
          originalPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.SUCCESS,
        action: MessageAction.CLEANUP,
        expectedTitle: "All artifacts deployed during failed activation have been removed.",
      },
      {
        description: "should send failure notification for cleanup activation",
        packageInstallation: {
          originalPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.ALERT,
        action: MessageAction.CLEANUP,
        expectedTitle: "Cleanup failed. Try again later.",
      },
      {
        description: "should send success notification for data package deactivation",
        packageInstallation: {
          originalPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.SUCCESS,
        action: MessageAction.UNINSTALL,
        expectedTitle: "TestDataPackage data package has been deactivated.",
      },
      {
        description: "should send failure notification for data package deactivation",
        packageInstallation: {
          originalPackage: { name: "TestDataPackage", type: BDCPackageType.DATA_PACKAGE },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.ALERT,
        action: MessageAction.UNINSTALL,
        expectedTitle: "Deactivation of TestDataPackage data package failed. Please try again later.",
      },
      {
        description: "should send success notification for intelligent application uninstallation",
        packageInstallation: {
          originalPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.SUCCESS,
        action: MessageAction.UNINSTALL,
        expectedTitle: "TestIntelligentApp intelligent application has been uninstalled.",
      },
      {
        description: "should send failure notification for intelligent application uninstallation",
        packageInstallation: {
          originalPackage: { name: "TestIntelligentApp", type: BDCPackageType.INSIGHT_APPLICATION },
          installationId: "testInstallationId",
        } as any,
        notificationType: NotificationType.ALERT,
        action: MessageAction.UNINSTALL,
        expectedTitle: "Uninstallation of TestIntelligentApp intelligent application failed. Please try again later.",
      },
    ];

    testCases.forEach(({ description, packageInstallation, notificationType, action, expectedTitle }) => {
      it(description, async () => {
        await notificationsHelper.sendOffboardFinishedNotification({
          context: mockContext,
          notificationType,
          packageInstallation,
          action,
        });

        expect(sendNotificationForTechUserStub.calledOnce, "sendNotificationForTechUser should be called once").to.be
          .true;
        const actualArgs = sendNotificationForTechUserStub.firstCall.args[0];
        expect(actualArgs).to.deep.equal({
          context: mockContext,
          type: notificationType,
          title: expectedTitle,
          installationId: packageInstallation.installationId,
        });
      });
    });
  });
});
