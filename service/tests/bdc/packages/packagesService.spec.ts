/** @format */

import { expect } from "chai";

import { IRequestContext } from "@sap/deepsea-types";
import sinon from "sinon";
import { PackageUtils } from "../../../bdc/packages/packageUtils";
import { PackagesService } from "../../../bdc/packages/packagesService";
import packages from "./data/packages.json";

describe("getPackagesFilteredByEntitlement", () => {
  let mockContext: any;
  let getEnabledProvidersStub: sinon.SinonStub;
  beforeEach(() => {
    getEnabledProvidersStub = sinon.stub(PackageUtils, "getEnabledProviders");

    mockContext = {
      tenantId: "test-tenant",
      userInfo: { userId: "test-user" },
      isFeatureFlagActive: async () => true,
    } as unknown as IRequestContext;
  });

  afterEach(() => {
    sinon.restore();
  });

  describe("Package Filtering", () => {
    it("should return all packages if sap.s4 and sap.sf entitlements are available", async () => {
      getEnabledProvidersStub.resolves(new Set(["sap.s4", "sap.sf"]));

      const result = await PackagesService.getPackagesFilteredByEntitlement(
        packages as any,
        { context: mockContext } as any
      );

      expect(result.length).to.equal(packages.length);
    });

    it("should return only packages with entitled provider 'sap.sf' from JSON", async () => {
      getEnabledProvidersStub.resolves(new Set(["sap.sf"]));

      const result = await PackagesService.getPackagesFilteredByEntitlement(
        packages as any,
        { context: mockContext } as any
      );

      expect(result).to.be.an("array");
      expect(result.length).to.equal(2);
      expect(result.every((pkg) => pkg.requiredApplications.every((app) => app.provider === "sap.sf"))).to.be.true;
    });

    it("should return no packages if no entitlement is available", async () => {
      getEnabledProvidersStub.resolves(new Set());

      const result = await PackagesService.getPackagesFilteredByEntitlement(
        packages as any,
        { context: mockContext } as any
      );

      expect(result).to.be.an("array").that.is.empty;
    });
  });
});
