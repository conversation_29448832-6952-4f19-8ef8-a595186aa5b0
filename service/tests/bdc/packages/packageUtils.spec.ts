/** @format */

import { expect } from "chai";
import sinon from "sinon";
import * as EntitlementClient from "../../../bdc/entitlement/unifiedServicesApiClient";
import { BDCPackageInstallation } from "../../../bdc/packageInstallation/packageInstallationTypes";
import { PackageUtils } from "../../../bdc/packages/packageUtils";

describe("PackageUtils Tests", () => {
  describe("getComponentStatusList", () => {
    const testCases = [
      {
        description: "should return the correct component status list",
        mockPackageInstallation: {
          originalPackage: {
            components: [
              { componentId: "comp-1", category: "DataProduct" },
              { componentId: "comp-2", category: "CnPackage" },
            ],
          },
          componentsInstallationStatus: [
            { componentId: "comp-1", status: "DONE" },
            { componentId: "comp-2", status: "FAILED" },
          ],
        } as BDC<PERSON>ackageInstallation,
        expected: [
          { componentId: "comp-1", category: "DataProduct", status: "DONE" },
          { componentId: "comp-2", category: "CnPackage", status: "FAILED" },
        ],
      },
      {
        description: "should return PENDING status for components without installation status",
        mockPackageInstallation: {
          originalPackage: {
            components: [
              { componentId: "comp-1", category: "DataProduct" },
              { componentId: "comp-2", category: "CnPackage" },
            ],
          },
          componentsInstallationStatus: [{ componentId: "comp-1", status: "DONE" }],
        } as BDCPackageInstallation,
        expected: [
          { componentId: "comp-1", category: "DataProduct", status: "DONE" },
          { componentId: "comp-2", category: "CnPackage", status: "PENDING" },
        ],
      },
      {
        description: "should handle empty components list",
        mockPackageInstallation: {
          originalPackage: {
            components: [],
          },
          componentsInstallationStatus: [],
        } as unknown as BDCPackageInstallation,
        expected: [],
      },
      {
        description: "should handle components with mixed statuses",
        mockPackageInstallation: {
          originalPackage: {
            components: [
              { componentId: "comp-1", category: "DataProduct" },
              { componentId: "comp-2", category: "CnPackage" },
              { componentId: "comp-3", category: "DataProduct" },
            ],
          },
          componentsInstallationStatus: [
            { componentId: "comp-1", status: "DONE" },
            { componentId: "comp-2", status: "FAILED" },
          ],
        } as BDCPackageInstallation,
        expected: [
          { componentId: "comp-1", category: "DataProduct", status: "DONE" },
          { componentId: "comp-2", category: "CnPackage", status: "FAILED" },
          { componentId: "comp-3", category: "DataProduct", status: "PENDING" },
        ],
      },
    ];

    testCases.forEach(({ description, mockPackageInstallation, expected }) => {
      it(description, () => {
        const result = PackageUtils.getComponentStatusList(mockPackageInstallation);
        expect(result).to.deep.equal(expected);
      });
    });
  });
});

describe("getEnabledProviders", () => {
  let getTenantEntitlementByNameStub: sinon.SinonStub;
  let mockContext: any;
  beforeEach(() => {
    getTenantEntitlementByNameStub = sinon.stub(EntitlementClient, "getTenantEntitlementByName");
    mockContext = {
      isFeatureFlagActive: async () => true,
    };
  });

  afterEach(() => {
    sinon.restore();
  });

  it("should return empty set if no entitlement is available", async () => {
    getTenantEntitlementByNameStub.resolves(false);

    const providers = await PackageUtils.getEnabledProviders(mockContext);
    expect(providers.size).to.equal(0);
  });

  it("should return sap.s4pce if only S4PCE entitlement is available", async () => {
    getTenantEntitlementByNameStub.callsFake(async (context, sku) => sku === "CoreERPIASAPManagedTenant");

    const providers = await PackageUtils.getEnabledProviders(mockContext);
    expect(Array.from(providers)).to.deep.equal(["sap.s4pce"]);
  });

  it("should return sap.s4 if only S4 entitlement is available", async () => {
    getTenantEntitlementByNameStub.callsFake(async (context, sku) => sku === "PublicERPInsightAppTenant");

    const providers = await PackageUtils.getEnabledProviders(mockContext);
    expect(Array.from(providers)).to.deep.equal(["sap.s4"]);
  });

  it("should return sap.sf if only HCM entitlement is available", async () => {
    getTenantEntitlementByNameStub.callsFake(async (context, sku) => sku === "HCMAnalyticsInsightAppTenant");

    const providers = await PackageUtils.getEnabledProviders(mockContext);
    expect(Array.from(providers)).to.deep.equal(["sap.sf"]);
  });

  it("should return sap.s4pce, sap.s4 and sap.sf if all entitlements are available", async () => {
    getTenantEntitlementByNameStub.resolves(true);

    const providers = await PackageUtils.getEnabledProviders(mockContext);
    expect(Array.from(providers)).to.have.members(["sap.s4pce", "sap.s4", "sap.sf"]);
  });
});
