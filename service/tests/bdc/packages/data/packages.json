[{"id": "ZIA_HCM_TEST", "version": "1.0.0", "name": "IA for testing HCM", "description": " IA for testing HCM", "shortDescription": " IA for testing HCM", "preview": [{"url": "..."}], "type": "InsightApp", "category": "humanResources", "requiredApplications": [{"provider": "sap.sf", "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2405"}]}], "components": [{"category": "CnPackage", "provider": "sap.analytics", "name": "ZIA_HCM_TEST", "version": "1.0.0", "requires": [{"category": "CnPackage", "provider": "sap.datasphere", "name": "ZIA_HCM_AM", "minVersion": "1.0.0"}]}, {"category": "CnPackage", "provider": "sap.datasphere", "name": "ZIA_HCM_AM", "version": "1.0.0", "requires": [{"category": "CnPackage", "provider": "sap.datasphere", "name": "ZIA_HCM_REUSE", "minVersion": "1.0.0"}]}, {"category": "CnPackage", "provider": "sap.datasphere", "name": "ZIA_HCM_REUSE", "version": "1.0.0", "requires": [{"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.workforce:dataProduct:Position:v1", "minVersion": "1.0.0"}]}, {"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.workforce:dataProduct:Position:v1", "version": "1.0.0", "name": "Data Product Position", "description": " Job position is the smallest unit of the org structure in a certain part of a company and comprises a specific occurrence of a job..", "numberOfEntities": 3}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}, {"id": "zz.hcm_rrr_poc_data_package_Position", "version": "1.0.0", "name": "(Position) V1.0.1", "description": "(Position) V1.0.0", "shortDescription": "(Position) V1.0.0", "type": "DataPackage", "category": "humanResources", "deliveryStatus": "development", "applicationComponent": "BDC-DPI-HCM", "requiredApplications": [{"provider": "sap.sf", "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2411"}]}], "components": [{"category": "DataProduct", "provider": "sap.sf", "ordid": "sap.sf.workforce:dataProduct:Position:v1", "version": "1.0.0", "name": "Position", "description": "Position", "numberOfEntities": 1, "applications": [{"category": "application", "applicationNamespace": "sap.sf", "minVersion": "2411"}]}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}, {"id": "BDC_Test_FOS_InsightApp_Dummy_Product", "version": "1.0.0", "name": "BDC Test FOS InsightApp Dummy Product", "description": "This insight app consists of a SAC story and a DPS analytical model based on local tables. It uses a single delta table ( CostCenterText )  from the Cost Center delta share", "shortDescription": "An Insight app with real data product", "preview": [{"url": "..."}], "type": "InsightApp", "category": "finance", "requiredApplications": [{"provider": "sap.s4", "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "21.03"}]}], "components": [{"category": "CnPackage", "provider": "sap.analytics", "name": "BDC FOS INSIGHTS", "version": "1.0.0", "requires": [{"category": "CnPackage", "provider": "sap.datasphere", "name": "BDC_FOS_COSTCENTER", "minVersion": "1.0.0"}]}, {"category": "CnPackage", "provider": "sap.datasphere", "name": "BDC_FOS_COSTCENTER", "version": "1.0.0", "requires": [{"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4.dataProduct:DummyDP", "minVersion": "1.0.0"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4.dataProduct:DummyDP", "version": "1.0.0", "name": "Cost Center", "description": " Cost Center Data Product in FOS", "numberOfEntities": 8}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}, {"id": "TestInsightAppMultiDSP", "version": "1.0.0", "name": "BDCTestInsightAppMultiDPS", "description": "This insight app consists of a SAC story and 2 DSP spaces. Analytical model is in space BDC_DEPENDENT_TEST and it uses views shared from BDC_CONTENT_TEST. The source of rep flows is S4 PCE tenant QKZ. Repository packages have been used to produce DSP ACN Content", "shortDescription": "Test Insight App with Multiple spaces", "preview": [{"url": "..."}], "type": "InsightApp", "category": "finance", "requiredApplications": [{"provider": "sap.s4", "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "21.03"}]}], "components": [{"category": "CnPackage", "description": "The best Business Area", "provider": "sap.analytics", "name": "BDC_INSIGHTAPP_MULTIPLE_DSP", "version": "1.0.0", "requires": [{"category": "CnPackage", "provider": "sap.datasphere", "name": "BDC_DEPENDENT_TEST", "minVersion": "1.0.0"}]}, {"category": "CnPackage", "description": "The best Business Area 2", "provider": "sap.datasphere", "name": "BDC_DEPENDENT_TEST", "version": "1.0.0", "requires": [{"category": "CnPackage", "provider": "sap.datasphere", "name": "BDC_CONTENT_TEST", "minVersion": "1.0.0"}]}, {"category": "CnPackage", "description": "The best Business Area 3", "provider": "sap.datasphere", "name": "BDC_CONTENT_TEST", "version": "1.0.0", "requires": [{"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4.dataProduct:DummyDP", "minVersion": "1.0.0"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4.dataProduct:DummyDP", "version": "1.0.0", "name": "Dummy Data Product", "description": " for testingInsightApp without having actual DPs", "numberOfEntities": 5}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}, {"id": "BDC_CONSOLIDATION_IA", "version": "1.0.0", "name": "Insight App for Consolidations", "description": "This insight app consists of a SAC story and 2 DPS analytical models based on the data products Consolidation Units and Consolidation Chart of Account. This IA is mainly created for technical E2E testing purposes", "shortDescription": "Insight App for Consolidations", "preview": [{"url": "..."}], "type": "InsightApp", "category": "finance", "requiredApplications": [{"provider": "sap.s4", "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "21.03"}]}], "components": [{"category": "CnPackage", "provider": "sap.analytics", "name": "BCD_SAC_IA_CONSOLIDATION", "version": "1.0.0", "requires": [{"category": "CnPackage", "provider": "sap.datasphere", "name": "BDC_DSP_IA_CONSOLIDATION", "minVersion": "1.0.0"}]}, {"category": "CnPackage", "provider": "sap.datasphere", "name": "BDC_DSP_IA_CONSOLIDATION", "version": "1.0.0", "requires": [{"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.gr:dataProduct:ConsolidationUnit:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.gr:dataProduct:ConsolidationChartOfAccounts:v1", "minVersion": "1.0.0"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.gr:dataProduct:ConsolidationUnit:v1", "version": "1.0.0", "name": "Data Product Consolidation Unit", "description": "The smallest entity of the corporate group structure that can be used as the basis for performing a consolidation.", "numberOfEntities": 8}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.gr:dataProduct:ConsolidationChartOfAccounts:v1", "version": "1.0.0", "name": "Data Product Consolidation Chart of Accounts", "description": "A consolidation chart of accounts is a systemic grouping of financial statement items that belong together and are used for group reporting and consolidation. For example, the predefined consolidation chart of accounts is Y1.", "numberOfEntities": 2}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}, {"id": "BDC_CASH_FLOW_IA", "version": "1.0.0", "name": "Insight App for Actual and Forecast Cash Flow of a Company", "deliveryStatus": "production", "description": "This insight app consists of an SAC story and 2 Datasphere analytical models based on the data products Cash Flow and CompanyCode", "shortDescription": "Insight App for Actual and Forecast Cash Flow of a Company", "preview": [{"url": "..."}], "type": "InsightApp", "category": "finance", "requiredApplications": [{"provider": "sap.s4", "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2022"}]}], "components": [{"category": "CnPackage", "provider": "sap.analytics", "name": "BDC_SAC_IA_CASHFLOW", "version": "1.0.0", "requires": [{"category": "CnPackage", "provider": "sap.datasphere", "name": "BDC_DSP_IA_CASHFLOW", "minVersion": "1.0.0"}]}, {"category": "CnPackage", "provider": "sap.datasphere", "name": "BDC_DSP_IA_CASHFLOW", "version": "1.0.0", "requires": [{"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.afc:dataProduct:CompanyCode:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.fio:dataProduct:CashFlow:v1", "minVersion": "1.0.0"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.afc:dataProduct:CompanyCode:v1", "version": "1.0.0", "name": "Data Product Company Code", "description": "A company code is the smallest organizational unit of external accounting for which a complete, self-contained set of accounts can be created.", "numberOfEntities": 9}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.fio:dataProduct:CashFlow:v1", "version": "1.0.0", "name": "Data Product Cash Flow", "description": "The Cash Flow is a key measurement of health for a company's financial status, includes cash receipts and cash payments over a given period of time. Cash flows can be actual cash flows which are confirmed by banks, and forecasted cash flows which are planned but not confirmed yet. Cash flows can be from different business components, for instance, sales, purchase, account payable, account receivables in the same system, or via inbound webservice from remote systems. Cash flows are the important information for Cash Managers to understand the short, mid and long term financial situation.", "numberOfEntities": 2}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}, {"id": "BusinessArea1", "name": "Business Area", "type": "DataPackage", "version": "1.4.0", "creationDate": "2024-09-23T00:00:00Z", "description": "Business Area Product", "shortDescription": "Business Area Product description", "preview": [{"url": "..."}], "category": "finance", "applicationComponent": "FIN-AB-CDE", "requiredApplications": [{"provider": "sap.s4", "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "21.03"}, {"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "21.03"}]}], "components": [{"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.afc:dataProduct:BusinessArea:v1", "version": "1.0.0", "name": "Business Area Product", "description": "The best Business Area", "numberOfEntities": 33}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}, {"id": "early_adopter_development", "version": "1.0.0", "name": "early_adopter_development", "description": "This insight app consists of a SAC story and a DPS analytical model based on local tables. It uses a single delta table ( CostCenterText )  from the Cost Center delta share", "shortDescription": "An Insight app with real data product", "deliveryStatus": "early_adopter_development", "preview": [{"url": "..."}], "type": "InsightApp", "category": "finance", "requiredApplications": [{"provider": "sap.s4", "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "21.03"}]}], "components": [{"category": "CnPackage", "provider": "sap.analytics", "name": "BDC FOS INSIGHTS", "version": "1.0.0", "requires": [{"category": "CnPackage", "provider": "sap.datasphere", "name": "BDC_FOS_COSTCENTER", "minVersion": "1.0.0"}]}, {"category": "CnPackage", "provider": "sap.datasphere", "name": "BDC_FOS_COSTCENTER", "version": "1.0.0", "requires": [{"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4.dataProduct:DummyDP", "minVersion": "1.0.0"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4.dataProduct:DummyDP", "version": "1.0.0", "name": "Cost Center", "description": " Cost Center Data Product in FOS", "numberOfEntities": 8}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}, {"id": "early_adopter", "version": "1.0.0", "name": "early_adopter", "deliveryStatus": "early_adopter", "description": "This insight app consists of a SAC story and 2 DPS analytical models based on the data products EntryViewJournalEntry, CompanyCode, Supplier, Customer, Ledger and ProfitCenter", "shortDescription": "Insight App for AP/AR open items of a company", "preview": [{"url": "..."}], "type": "InsightApp", "category": "finance", "requiredApplications": [{"provider": "sap.s4", "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}], "components": [{"category": "CnPackage", "provider": "sap.analytics", "name": "BDC_SAC_IA_APAROpenItem", "version": "1.0.0", "requires": [{"category": "CnPackage", "provider": "sap.datasphere", "name": "BDC_DSP_IA_APAROpenItem", "minVersion": "1.0.0"}]}, {"category": "CnPackage", "provider": "sap.datasphere", "name": "BDC_DSP_IA_APAROpenItem", "version": "1.0.0", "requires": [{"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.afc:dataProduct:CompanyCode:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.afc:dataProduct:Ledger:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.afc:dataProduct:EntryViewJournalEntry:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.dm:dataProduct:Customer:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.dm:dataProduct:Supplier:v1", "minVersion": "1.0.0"}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.cross:dataProduct:Country:v1", "minVersion": "1.0.0"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.afc:dataProduct:CompanyCode:v1", "version": "1.0.0", "name": "CompanyCode", "description": "Company Code", "numberOfEntities": 9}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.afc:dataProduct:Ledger:v1", "version": "1.0.0", "name": "Ledger", "description": "Ledger", "numberOfEntities": 4}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.dm:dataProduct:Customer:v1", "version": "1.0.0", "name": "Customer", "description": "Customer", "numberOfEntities": 13}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.dm:dataProduct:Supplier:v1", "version": "1.0.0", "name": "Supplier", "description": "Supplier", "numberOfEntities": 7}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.afc:dataProduct:EntryViewJournalEntry:v1", "version": "1.0.0", "name": "EntryViewJournalEntry", "description": "Entry View Journal Entry", "numberOfEntities": 1}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.cross:dataProduct:Country:v1", "version": "1.0.0", "name": "CountryRegion", "description": "Country/Region", "numberOfEntities": 2}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}, {"id": "production", "version": "1.0.1", "name": "production", "description": "This Data Package contains Data Products for the SAP S/4HANA area Cross Applications", "shortDescription": "Data Products for SAP S/4HANA Cross Applications", "type": "DataPackage", "category": "finance", "deliveryStatus": "production", "applicationComponent": "BDC-DPI-S4", "requiredApplications": [{"provider": "sap.s4", "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}], "components": [{"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:Country:v1", "version": "1.0.0", "name": "CountryRegion", "description": "Country / Region", "numberOfEntities": 2, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:HANACurrency:v1", "version": "1.0.0", "name": "HANACurrency", "description": "HANA Currency", "numberOfEntities": 8, "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}, {"id": "development", "version": "1.0.0", "name": "development", "description": "Data Product Version Upgrade Test V1.0.0", "shortDescription": "Data Product Version Upgrade Test V1.0.0", "type": "DataPackage", "category": "finance", "deliveryStatus": "development", "applicationComponent": "BDC-DPI-S4", "requiredApplications": [{"provider": "sap.s4", "applications": [{"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2408"}, {"category": "application", "applicationNamespace": "sap.s4pce", "minVersion": "2021"}]}], "components": [{"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:CostCenterActivityType:v1", "version": "1.0.0", "name": "CostCenterActivityType", "description": "Cost Center Activity Type", "numberOfEntities": 6}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com:dataProduct:JournalEntryCodes:v1", "version": "1.0.0", "name": "JournalEntryCodes", "description": "Journal Entry Codes", "numberOfEntities": 12}, {"category": "DataProduct", "provider": "sap.s4", "ordid": "sap.s4com.dphome:dataProduct:ZDPAllDataTypes5:v1", "version": "1.0.0", "name": "ZDPAllDataTypes5", "description": "Z DP All Data Types5", "numberOfEntities": 1}], "schemaName": "bdcPackage", "schemaVersion": "1.0.0", "fileProducer": "B<PERSON>Designer"}]