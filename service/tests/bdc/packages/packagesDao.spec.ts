/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import assert from "assert";
import { PropertiesField } from "../../../bdc/catalog/CatalogConstants";
import { PackagesDao } from "../../../bdc/packages/packagesDao";
import { BDCPackage } from "../../../bdc/packages/packagesTypes";

describe("Packages from Catalog", () => {
  const mockContext: IRequestContext = {
    tenantId: "test-tenant",
    userInfo: { userId: "test-user" },
  } as IRequestContext;
  mockContext.isFeatureFlagActive = async function (ff: string) {
    return true;
  };

  describe("Available Packages", () => {
    it("Get packages - sanity", async () => {
      const bdcPackages: BDCPackage[] = await (PackagesDao as any)["getPackagesFromResponse"](catalogData, mockContext);
      assert.equal(bdcPackages.length, 5);

      const testingPackage = bdcPackages[1];
      // test JSON parsing
      assert.equal(testingPackage.category, "finance");
      assert.equal(testingPackage.type, "InsightApp");
      assert.equal(testingPackage.id, "BDC_InsightApp_BusinessArea_YAN3");
      assert.equal(testingPackage.name, "Test app for a single insight app");
      assert.equal(testingPackage.version, "1.0.0");

      // test preview
      assert.equal(testingPackage.preview.length, 2);
      const previewLinksCatalogObj = catalogData.allObjects[1].propertyTags.find(
        (propertyTag: any) => propertyTag.propertyId === PropertiesField.BDC_PCK_PREVIEW_LINK
      );
      assert.equal(testingPackage.preview[0].base64, (previewLinksCatalogObj?.valueObjects[0] as any).valueText);
      assert.equal(testingPackage.preview[1].base64, (previewLinksCatalogObj?.valueObjects[1] as any).valueText);

      // Test landscape type
      const landscapeTypeCatalogObj = catalogData.allObjects[1].propertyTags.find(
        (propertyTag: any) => propertyTag.propertyId === PropertiesField.BDC_PCK_LANDSCAPE_TYPE
      );
      assert.equal(testingPackage.landscapeType?.id, (landscapeTypeCatalogObj?.valueObjects[0] as any).valueId);
      assert.equal(testingPackage.landscapeType?.name, (landscapeTypeCatalogObj?.valueObjects[0] as any).valueString);

      // Test id
      const catalogIdObj = catalogData.allObjects[1].propertyTags.find(
        (propertyTag: any) => propertyTag.propertyId === PropertiesField.BDC_PCK_ID
      );
      assert.equal(testingPackage.id, (catalogIdObj?.valueObjects[0] as any).valueString);
      // Test version
      const catalogVersionObj = catalogData.allObjects[1].propertyTags.find(
        (propertyTag: any) => propertyTag.propertyId === PropertiesField.BDC_PCK_VERSION
      );
      assert.equal(testingPackage.version, (catalogVersionObj?.valueObjects[0] as any).valueString);
    });
  });
});
const catalogData = {
  rootObjectIds: [
    "ED272230211C75621900F692BA138C98",
    "0D08AE5065B8E45C1900AB66A324622C",
    "919AA35065B8E45C1900AB66A324622C",
    "3BCF9F5065B8E45C1900AB66A324622C",
    "AC25AC5065B8E45C1900AB66A324622C",
  ],
  allObjects: [
    {
      propertyTags: [
        {
          propertyId: "CP_BDC_PKG_JSON_DEF",
          namespace: "bdc",
          name: "SAP BDC Package JSON Definition",
          valueObjects: [
            {
              valueText:
                '{"id":"BDC_InsightApp_BusinessArea","version":"1.0.0","name":"Test app for a single insight app","description":"This insight app consists of a SAC story and a DPS analytical model based on local tables. It uses a single data product Business Area","shortDescription":"An Insight app with real data product","deliveryStatus":"early_adopter_development","preview":[{"url":"..."}],"type":"InsightApp","category":"finance","requiredApplications":[{"provider":"sap.s4","applications":[{"category":"application","applicationNamespace":"sap.s4pce","minVersion":"21.03"}]}],"components":[{"category":"CnPackage","provider":"sap.analytics","name":"BDC_TEST_BUSINESSAREA","version":"1.0.0","requires":[{"category":"CnPackage","provider":"sap.datasphere","name":"BDC_IA_BUSINESSAREA_1.0.0","minVersion":"1.0.0"}]},{"category":"CnPackage","provider":"sap.datasphere","name":"BDC_IA_BUSINESSAREA_1.0.0","version":"1.0.0","requires":[{"category":"DataProduct","provider":"sap.s4","ordid":"sap.s4com.afc:dataProduct:BusinessArea:v1","version":"1.0.0"}]},{"category":"DataProduct","provider":"sap.s4","ordid":"sap.s4com.afc:dataProduct:BusinessArea:v1","version":"1.0.0","name":"Cost Center","description":" Cost Center Data Product in FOS","numberOfEntities":8}],"schemaName":"bdcPackage","schemaVersion":"1.0.0","fileProducer":"BDCDesigner"}',
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_ID",
          namespace: "bdc",
          name: "SAP BDC Package ID",
          valueObjects: [
            {
              valueString: "pkg_id_1",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_NAME",
          namespace: "bdc",
          name: "SAP BDC Package Name",
          valueObjects: [
            {
              valueString: "Test app for a single insight app",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_VERSION",
          namespace: "bdc",
          name: "SAP BDC Package Version",
          valueObjects: [
            {
              valueString: "1.0.0",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_LANDSCAPE_TYPE",
          namespace: "bdc",
          name: "Package Landscape Type",
          valueObjects: [
            {
              valueId: "VD_BDC_PKG_LT_DEV",
              valueString: "Dev",
            },
          ],
        },
      ],
      navigationLinks: [],
      id: "ED272230211C75621900F692BA138C98",
      name: "BDC_InsightApp_BusinessArea_v1",
      customSpace: "bdc",
      className: "bdcPackage",
      lastModifiedAt: null,
      createdAt: null,
    },
    {
      propertyTags: [
        {
          propertyId: "CP_BDC_PKG_LANDSCAPE_TYPE",
          namespace: "bdc",
          name: "Package Landscape Type",
          valueObjects: [
            {
              valueId: "VD_BDC_PKG_LT_STAGE_CAN",
              valueString: "Stage Canary",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_VERSION",
          namespace: "bdc",
          name: "SAP BDC Package Version",
          valueObjects: [
            {
              valueString: "1.0.0",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_NAME",
          namespace: "bdc",
          name: "SAP BDC Package Name",
          valueObjects: [
            {
              valueString: "Test app for a single insight app yan",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_ID",
          namespace: "bdc",
          name: "SAP BDC Package ID",
          valueObjects: [
            {
              valueString: "BDC_InsightApp_BusinessArea_YAN3",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_JSON_DEF",
          namespace: "bdc",
          name: "SAP BDC Package JSON Definition",
          valueObjects: [
            {
              valueText:
                '{"id":"BDC_InsightApp_BusinessArea","version":"1.0.0","name":"Test app for a single insight app","description":"This insight app consists of a SAC story and a DPS analytical model based on local tables. It uses a single data product Business Area","shortDescription":"An Insight app with real data product","deliveryStatus":"early_adopter_development","preview":[{"url":"..."}],"type":"InsightApp","category":"finance","requiredApplications":[{"provider":"sap.s4","applications":[{"category":"application","applicationNamespace":"sap.s4pce","minVersion":"21.03"}]}],"components":[{"category":"CnPackage","provider":"sap.analytics","name":"BDC_TEST_BUSINESSAREA","version":"1.0.0","requires":[{"category":"CnPackage","provider":"sap.datasphere","name":"BDC_IA_BUSINESSAREA_1.0.0","minVersion":"1.0.0"}]},{"category":"CnPackage","provider":"sap.datasphere","name":"BDC_IA_BUSINESSAREA_1.0.0","version":"1.0.0","requires":[{"category":"DataProduct","provider":"sap.s4","ordid":"sap.s4com.afc:dataProduct:BusinessArea:v1","version":"1.0.0"}]},{"category":"DataProduct","provider":"sap.s4","ordid":"sap.s4com.afc:dataProduct:BusinessArea:v1","version":"1.0.0","name":"Cost Center","description":" Cost Center Data Product in FOS","numberOfEntities":8}],"schemaName":"bdcPackage","schemaVersion":"1.0.0","fileProducer":"BDCDesigner"}',
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_PREVIEW_LINK",
          namespace: "bdc",
          name: "SAP BDC Package Preview Link",
          valueObjects: [
            {
              valueText:
                "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",
            },
            {
              valueText:
                "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",
            },
          ],
        },
      ],
      navigationLinks: [],
      id: "0D08AE5065B8E45C1900AB66A324622C",
      name: "BDC_InsightApp_BusinessArea_YAN3",
      customSpace: "bdc",
      className: "bdcPackage",
      lastModifiedAt: null,
      createdAt: null,
    },
    {
      propertyTags: [
        {
          propertyId: "CP_BDC_PKG_JSON_DEF",
          namespace: "bdc",
          name: "SAP BDC Package JSON Definition",
          valueObjects: [
            {
              valueText:
                '{"id":"BDC_InsightApp_BusinessArea","version":"1.0.0","name":"Test app for a single insight app","description":"This insight app consists of a SAC story and a DPS analytical model based on local tables. It uses a single data product Business Area","shortDescription":"An Insight app with real data product","deliveryStatus":"early_adopter_development","preview":[{"url":"..."}],"type":"InsightApp","category":"finance","requiredApplications":[{"provider":"sap.s4","applications":[{"category":"application","applicationNamespace":"sap.s4pce","minVersion":"21.03"}]}],"components":[{"category":"CnPackage","provider":"sap.analytics","name":"BDC_TEST_BUSINESSAREA","version":"1.0.0","requires":[{"category":"CnPackage","provider":"sap.datasphere","name":"BDC_IA_BUSINESSAREA_1.0.0","minVersion":"1.0.0"}]},{"category":"CnPackage","provider":"sap.datasphere","name":"BDC_IA_BUSINESSAREA_1.0.0","version":"1.0.0","requires":[{"category":"DataProduct","provider":"sap.s4","ordid":"sap.s4com.afc:dataProduct:BusinessArea:v1","version":"1.0.0"}]},{"category":"DataProduct","provider":"sap.s4","ordid":"sap.s4com.afc:dataProduct:BusinessArea:v1","version":"1.0.0","name":"Cost Center","description":" Cost Center Data Product in FOS","numberOfEntities":8}],"schemaName":"bdcPackage","schemaVersion":"1.0.0","fileProducer":"BDCDesigner"}',
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_NAME",
          namespace: "bdc",
          name: "SAP BDC Package Name",
          valueObjects: [
            {
              valueString: "Test app for a single insight app yan",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_VERSION",
          namespace: "bdc",
          name: "SAP BDC Package Version",
          valueObjects: [
            {
              valueString: "1.0.0",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_ID",
          namespace: "bdc",
          name: "SAP BDC Package ID",
          valueObjects: [
            {
              valueString: "BDC_InsightApp_BusinessArea_YAN",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_LANDSCAPE_TYPE",
          namespace: "bdc",
          name: "Package Landscape Type",
          valueObjects: [
            {
              valueId: "VD_BDC_PKG_LT_DEV",
              valueString: "Dev",
            },
          ],
        },
      ],
      navigationLinks: [],
      id: "919AA35065B8E45C1900AB66A324622C",
      name: "BDC_InsightApp_BusinessArea_YAN",
      customSpace: "bdc",
      className: "bdcPackage",
      lastModifiedAt: null,
      createdAt: null,
    },
    {
      propertyTags: [
        {
          propertyId: "CP_BDC_PKG_LANDSCAPE_TYPE",
          namespace: "bdc",
          name: "Package Landscape Type",
          valueObjects: [
            {
              valueId: "VD_BDC_PKG_LT_DEV",
              valueString: "Dev",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_VERSION",
          namespace: "bdc",
          name: "SAP BDC Package Version",
          valueObjects: [
            {
              valueString: "1.0.0",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_NAME",
          namespace: "bdc",
          name: "SAP BDC Package Name",
          valueObjects: [
            {
              valueString: "Test app for a single insight app",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_ID",
          namespace: "bdc",
          name: "SAP BDC Package ID",
          valueObjects: [
            {
              valueString: "BDC_InsightApp_BusinessArea",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_JSON_DEF",
          namespace: "bdc",
          name: "SAP BDC Package JSON Definition",
          valueObjects: [
            {
              valueText:
                '{"id":"BDC_InsightApp_BusinessArea","version":"1.0.0","name":"Test app for a single insight app","description":"This insight app consists of a SAC story and a DPS analytical model based on local tables. It uses a single data product Business Area","shortDescription":"An Insight app with real data product","deliveryStatus":"early_adopter_development","preview":[{"url":"..."}],"type":"InsightApp","category":"finance","requiredApplications":[{"provider":"sap.s4","applications":[{"category":"application","applicationNamespace":"sap.s4pce","minVersion":"21.03"}]}],"components":[{"category":"CnPackage","provider":"sap.analytics","name":"BDC_TEST_BUSINESSAREA","version":"1.0.0","requires":[{"category":"CnPackage","provider":"sap.datasphere","name":"BDC_IA_BUSINESSAREA_1.0.0","minVersion":"1.0.0"}]},{"category":"CnPackage","provider":"sap.datasphere","name":"BDC_IA_BUSINESSAREA_1.0.0","version":"1.0.0","requires":[{"category":"DataProduct","provider":"sap.s4","ordid":"sap.s4com.afc:dataProduct:BusinessArea:v1","version":"1.0.0"}]},{"category":"DataProduct","provider":"sap.s4","ordid":"sap.s4com.afc:dataProduct:BusinessArea:v1","version":"1.0.0","name":"Cost Center","description":" Cost Center Data Product in FOS","numberOfEntities":8}],"schemaName":"bdcPackage","schemaVersion":"1.0.0","fileProducer":"BDCDesigner"}',
            },
          ],
        },
      ],
      navigationLinks: [],
      id: "3BCF9F5065B8E45C1900AB66A324622C",
      name: "BDC_InsightApp_BusinessArea",
      customSpace: "bdc",
      className: "bdcPackage",
      lastModifiedAt: null,
      createdAt: null,
    },
    {
      propertyTags: [
        {
          propertyId: "CP_BDC_PKG_JSON_DEF",
          namespace: "bdc",
          name: "SAP BDC Package JSON Definition",
          valueObjects: [
            {
              valueText:
                '{"id":"BDC_InsightApp_BusinessArea","version":"1.0.0","name":"Test app for a single insight app","description":"This insight app consists of a SAC story and a DPS analytical model based on local tables. It uses a single data product Business Area","shortDescription":"An Insight app with real data product","deliveryStatus":"early_adopter_development","preview":[{"url":"..."}],"type":"InsightApp","category":"finance","requiredApplications":[{"provider":"sap.s4","applications":[{"category":"application","applicationNamespace":"sap.s4pce","minVersion":"21.03"}]}],"components":[{"category":"CnPackage","provider":"sap.analytics","name":"BDC_TEST_BUSINESSAREA","version":"1.0.0","requires":[{"category":"CnPackage","provider":"sap.datasphere","name":"BDC_IA_BUSINESSAREA_1.0.0","minVersion":"1.0.0"}]},{"category":"CnPackage","provider":"sap.datasphere","name":"BDC_IA_BUSINESSAREA_1.0.0","version":"1.0.0","requires":[{"category":"DataProduct","provider":"sap.s4","ordid":"sap.s4com.afc:dataProduct:BusinessArea:v1","version":"1.0.0"}]},{"category":"DataProduct","provider":"sap.s4","ordid":"sap.s4com.afc:dataProduct:BusinessArea:v1","version":"1.0.0","name":"Cost Center","description":" Cost Center Data Product in FOS","numberOfEntities":8}],"schemaName":"bdcPackage","schemaVersion":"1.0.0","fileProducer":"BDCDesigner"}',
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_NAME",
          namespace: "bdc",
          name: "SAP BDC Package Name",
          valueObjects: [
            {
              valueString: "Test app for a single insight app yan",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_VERSION",
          namespace: "bdc",
          name: "SAP BDC Package Version",
          valueObjects: [
            {
              valueString: "1.0.0",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_ID",
          namespace: "bdc",
          name: "SAP BDC Package ID",
          valueObjects: [
            {
              valueString: "BDC_InsightApp_BusinessArea_YAN2",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_PREVIEW_LINK",
          namespace: "bdc",
          name: "SAP BDC Package Preview Link",
          valueObjects: [
            {
              valueText:
                "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",
            },
            {
              valueText:
                "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",
            },
          ],
        },
        {
          propertyId: "CP_BDC_PKG_LANDSCAPE_TYPE",
          namespace: "bdc",
          name: "Package Landscape Type",
          valueObjects: [
            {
              valueId: "VD_BDC_PKG_LT_DEV",
              valueString: "Dev",
            },
          ],
        },
      ],
      navigationLinks: [],
      id: "AC25AC5065B8E45C1900AB66A324622C",
      name: "BDC_InsightApp_BusinessArea_YAN2",
      customSpace: "bdc",
      className: "bdcPackage",
      lastModifiedAt: null,
      createdAt: null,
    },
  ],
};
