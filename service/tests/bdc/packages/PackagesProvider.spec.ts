/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import assert from "assert";
import * as fs from "fs";
import * as path from "path";
import sinon, { SinonSandbox } from "sinon";
import { ApplicationNamespace, SystemInfo } from "../../../../shared/bdccockpit/Types";
import { lte, semverCoerce } from "../../../../shared/bdccockpit/utils/semverUtils";
import { PackageInstallationService } from "../../../bdc/packageInstallation/packageInstallationService";
import { PackagesService } from "../../../bdc/packages/packagesService";
import { BDCPackage, DeliveryStatus } from "../../../bdc/packages/packagesTypes";
import { SystemsDao } from "../../../bdc/systems/systemsDao";
import { SystemsService } from "../../../bdc/systems/systemsService";
import * as nodeModule from "../../../lib/node";
import { deepClone } from "../../../routes/space/ls/impl/lsSmUtils";

let catalogDaoGetSystemsStub: sinon.SinonStub;
let catalogServiceGetTargetSystemsStub: sinon.SinonStub;
let catalogServiceGetAllPackageInstallationsWithRelationsStub: sinon.SinonStub;

describe("Available Packages", () => {
  let sandbox: SinonSandbox;

  const mockContext: IRequestContext = {
    tenantId: "test-tenant",
    userInfo: { userId: "test-user" },
  } as IRequestContext;
  mockContext.isFeatureFlagActive = async function (ff: string) {
    return true;
  };

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    catalogDaoGetSystemsStub = sandbox.stub(SystemsDao as any, "getSystems");

    catalogServiceGetTargetSystemsStub = sandbox.stub(SystemsService, "getTargetSystems");
    catalogServiceGetAllPackageInstallationsWithRelationsStub = sandbox.stub(
      PackageInstallationService,
      "getAllPackageInstallationsWithRelations"
    );
  });

  afterEach(() => {
    sandbox.restore();
    catalogDaoGetSystemsStub.restore();
    catalogServiceGetTargetSystemsStub.restore();
    catalogServiceGetAllPackageInstallationsWithRelationsStub.restore();
  });

  describe("Available Packages", () => {
    it("All packages available", async () => {
      catalogServiceGetTargetSystemsStub.callsFake(() => {
        const targetSystems: Map<ApplicationNamespace, SystemInfo[]> = new Map();
        targetSystems.set("sap.analytics", deepClone(analyticsSystem) as SystemInfo[]);
        targetSystems.set("sap.datasphere", deepClone(datasphereSystem) as SystemInfo[]);
        return targetSystems;
      });
      catalogServiceGetAllPackageInstallationsWithRelationsStub.callsFake(() => deepClone([]));
      const packages = readJsonFile("./data/packages.json");
      const installableSourceSystems = deepClone(s4pceSystem);
      installableSourceSystems[0].isInstallable = true;
      installableSourceSystems.push(deepClone(hcmSystem));
      installableSourceSystems[1].isInstallable = true;
      catalogDaoGetSystemsStub.withArgs(mockContext).resolves(allSystems);

      const availablePackages = await PackagesService.buildAndAddAvailablePackages(mockContext, packages);
      assert.equal(packages.length, availablePackages.length);
    });
    it("One packages installed", async () => {
      catalogServiceGetTargetSystemsStub.callsFake(() => {
        const targetSystems: Map<ApplicationNamespace, SystemInfo[]> = new Map();
        targetSystems.set("sap.analytics", deepClone(analyticsSystem) as SystemInfo[]);
        targetSystems.set("sap.datasphere", deepClone(datasphereSystem) as SystemInfo[]);
        return targetSystems;
      });
      const packages = readJsonFile("./data/packages.json");
      catalogServiceGetAllPackageInstallationsWithRelationsStub.callsFake(() => deepClone(instPackage.allObjects));
      const installableSourceSystem = deepClone(s4pceSystem);
      installableSourceSystem[0].isInstallable = true;
      catalogDaoGetSystemsStub.withArgs(mockContext).callsFake(() => deepClone(allSystems));

      const availablePackages = await PackagesService.buildAndAddAvailablePackages(mockContext, packages);
      assert.equal(packages.length - 1, availablePackages.length);
    });
    it("Filter available packages: let available system be S4PCE with 2022 as version, then available packages should be only those whose applicationNamespace is sap.s4pce and their minimum version is equal or lower than 2022", async () => {
      catalogServiceGetTargetSystemsStub.callsFake(() => {
        const targetSystems: Map<ApplicationNamespace, SystemInfo[]> = new Map();
        targetSystems.set("sap.analytics", deepClone(analyticsSystem) as SystemInfo[]);
        targetSystems.set("sap.datasphere", deepClone(datasphereSystem) as SystemInfo[]);
        return targetSystems;
      });
      catalogServiceGetAllPackageInstallationsWithRelationsStub.callsFake(() => []);
      const packages: BDCPackage[] = readJsonFile("./data/packages.json");
      const installableSourceSystems = deepClone(s4pceSystem);
      installableSourceSystems[0].isInstallable = true;

      catalogDaoGetSystemsStub.withArgs(mockContext).callsFake(() => deepClone(specificAvailableSystems));

      const filteredPackages = await PackagesService.filterPackagesByAvailableSystems(mockContext, packages);

      const availablePackages = await PackagesService.buildAndAddAvailablePackages(mockContext, filteredPackages);

      let s4pcePckCounter = 0;
      packages.forEach((pck: BDCPackage) => {
        if (
          pck.requiredApplications[0]?.applications[0]?.applicationNamespace === "sap.s4pce" &&
          lte(semverCoerce(pck.requiredApplications[0]?.applications[0]?.minVersion), "2022")
        ) {
          s4pcePckCounter++;
        }
      });

      assert.equal(s4pcePckCounter, availablePackages.length);
    });
    it("Filter available packages: let available system be S4PCE, let the original packages list be empty, then the available packages list should be empty without any error", async () => {
      catalogServiceGetTargetSystemsStub.callsFake(() => {
        const targetSystems: Map<ApplicationNamespace, SystemInfo[]> = new Map();
        targetSystems.set("sap.analytics", deepClone(analyticsSystem) as SystemInfo[]);
        targetSystems.set("sap.datasphere", deepClone(datasphereSystem) as SystemInfo[]);
        return targetSystems;
      });
      catalogServiceGetAllPackageInstallationsWithRelationsStub.callsFake(() => []);
      const packages: BDCPackage[] = [];
      const installableSourceSystems = deepClone(s4pceSystem);
      installableSourceSystems[0].isInstallable = true;

      catalogDaoGetSystemsStub.withArgs(mockContext).callsFake(() => deepClone(specificAvailableSystems));

      const filteredPackages = await PackagesService.filterPackagesByAvailableSystems(mockContext, packages);

      const availablePackages = await PackagesService.buildAndAddAvailablePackages(mockContext, filteredPackages);

      assert.equal(0, availablePackages.length);
    });
    it("Filter available packages: let available systems be nothing, then the available packages list should be empty without any error", async () => {
      catalogServiceGetTargetSystemsStub.callsFake(() => {
        const targetSystems: Map<ApplicationNamespace, SystemInfo[]> = new Map();
        targetSystems.set("sap.analytics", deepClone(analyticsSystem) as SystemInfo[]);
        targetSystems.set("sap.datasphere", deepClone(datasphereSystem) as SystemInfo[]);
        return targetSystems;
      });
      catalogServiceGetAllPackageInstallationsWithRelationsStub.callsFake(() => []);
      const packages: BDCPackage[] = readJsonFile("./data/packages.json");

      catalogDaoGetSystemsStub.withArgs(mockContext).callsFake(() => []);

      const filteredPackages = await PackagesService.filterPackagesByAvailableSystems(mockContext, packages);

      const availablePackages = await PackagesService.buildAndAddAvailablePackages(mockContext, filteredPackages);

      assert.equal(0, availablePackages.length);
    });
  });

  describe("Package Filtering", () => {
    let isCanaryStub: sinon.SinonStub;
    let isLocalHanaMockedStub: sinon.SinonStub;
    let readPackagesFromFileSystemStub: sinon.SinonStub;
    beforeEach(() => {
      isCanaryStub = sinon.stub(nodeModule, "isCanary").returns(false);
      isLocalHanaMockedStub = sinon.stub(nodeModule, "isLocalHanaMocked").returns(false);

      readPackagesFromFileSystemStub = sinon.stub(PackagesService as any, "readPackagesFromFileSystem");

      readPackagesFromFileSystemStub.callsFake(async (listPath: string, filter?: (data: any) => boolean) => {
        const fixedListPath = "./data/packages.json";

        return readPackagesFromFileSystem(fixedListPath);
      });
    });
    afterEach(() => {
      isCanaryStub.restore();
      isLocalHanaMockedStub.restore();
      readPackagesFromFileSystemStub.restore();
    });

    const runTest = async (isCanary: boolean, showDevPackagesInProd: boolean, expectedStatuses: DeliveryStatus[]) => {
      isCanaryStub.returns(!isCanary);

      const mockContext = {
        tenantId: "test-tenant",
        userInfo: { userId: "test-user" },
        isFeatureFlagActive: async (ff: string): Promise<boolean> => {
          if (ff === "DWCO_BDC_COCKPIT_SHOW_PKG_IN_STATE_DEV") {
            return showDevPackagesInProd;
          } else {
            return false;
          }
        },
      } as IRequestContext;

      const filteredPackages: BDCPackage[] | undefined = await PackagesService.getPackages(mockContext);

      const filteredStatuses: DeliveryStatus[] = Array.from(
        new Set((filteredPackages ?? []).map((pkg) => pkg.deliveryStatus ?? ("development" as DeliveryStatus)))
      );

      assert.deepStrictEqual(filteredStatuses.sort(), expectedStatuses.sort());
      isCanaryStub.restore();
    };

    it("Ga && !isProductionPolicy - 1", async () => {
      const isProductionPolicy = false;
      const showDevPackagesInProd = true;

      await runTest(isProductionPolicy, showDevPackagesInProd, ["production", "development"]);
    });

    it("Ga && !isProductionPolicy - 2", async () => {
      const isProductionPolicy = false;
      const showDevPackagesInProd = false;

      await runTest(isProductionPolicy, showDevPackagesInProd, ["production", "development"]);
    });

    it("GA && isProductionPolicy && ShowDevPackagesInProd", async () => {
      const isProductionPolicy = true;
      const showDevPackagesInProd = true;

      await runTest(isProductionPolicy, showDevPackagesInProd, ["production", "development"]);
    });

    it("GA && isProductionPolicy && !ShowDevPackagesInProd", async () => {
      const isProductionPolicy = true;
      const showDevPackagesInProd = false;

      await runTest(isProductionPolicy, showDevPackagesInProd, ["production"]);
    });
  });
});

// Function to read JSON
function readJsonFile(filePath: string): any {
  const absolutePath = path.resolve(__dirname, filePath);
  const fileContent = fs.readFileSync(absolutePath, "utf-8");
  return JSON.parse(fileContent);
}

function readPackagesFromFileSystem(listPath: string): Promise<BDCPackage[]> {
  const data = readJsonFile(listPath);
  delete data.preview;
  data.creationDate = data?.creationDate || "";
  return data;
}

const bdcSystem = [
  {
    systemId: "3A3A11E4539A670B190084E2411F8BC4-bdc",
    applicationTenantId: "85ff2295-3119-4170-86b0-216dab90edb7-bdc",
    applicationNamespace: "sap.bdc-cockpit",
    applicationVersion: "9999",
    name: "bdc",
    className: "SYSTEM",
    formations: [
      {
        catalogUuid: "90263691E3DD4721190043CE640AF6FC",
        formationId: "8dc6301e-b21d-4232-bc75-53a59ee98faa",
        formationTypeId: "d6d3492c-a1b0-40a6-8fb8-43b7097568ae",
        assignmentId: "332922c0-cedd-4e2e-9be6-cb5601084013",
        formationName: "test1",
      },
      {
        catalogUuid: "90263691E3DD4721190043CE640AF6FC2",
        formationId: "8dc6301e-b21d-4232-bc75-53a59ee98faa2",
        formationTypeId: "d6d3492c-a1b0-40a6-8fb8-43b7097568ae2",
        assignmentId: "332922c0-cedd-4e2e-9be6-cb56010840132",
        formationName: "test2",
      },
    ],
  },
];
const s4pceSystem = [
  {
    systemId: "212B3691E3DD4721190043CE640AF6FC",
    applicationTenantId: "730850865",
    applicationNamespace: "sap.s4pce",
    applicationVersion: "2022 / 00 / 00",
    name: "BDF_550_Dev",
    className: "SYSTEM",
    formations: [
      {
        catalogUuid: "90263691E3DD4721190043CE640AF6FC",
        formationId: "8dc6301e-b21d-4232-bc75-53a59ee98faa",
        formationTypeId: "d6d3492c-a1b0-40a6-8fb8-43b7097568ae",
        assignmentId: "67afb815-682b-4ae9-83e0-bd163bdc8365",
        formationName: "test1",
      },
    ],
    propertyTags: [
      {
        propertyId: "SP_UCL_DEPLOYMENT_REGION",
        name: "Deployment Region",
        namespace: "com.sap.ucl",
        valueObjects: [
          {
            valueString: "",
          },
        ],
      },
      {
        propertyId: "SP_UCL_SYSTEM_TYPE",
        name: "System Type",
        namespace: "com.sap.ucl",
        valueObjects: [
          {
            valueString: "SAP S/4HANA Cloud Private Edition",
          },
        ],
      },
      {
        propertyId: "SPESU",
        name: "System URL",
        namespace: "catalog",
        valueObjects: [
          {
            valueString: "https://ldcibdf.devsys.net.sap",
          },
        ],
      },
      {
        propertyId: "SP_UCL_SYSTEM_VERSION",
        name: "System Version",
        namespace: "com.sap.ucl",
        valueObjects: [
          {
            valueString: "2025 / 00 / 00",
          },
        ],
      },
    ],
  },
];
const hcmSystem = {
  systemId: "212B3691E3DD4721190043CE640AF6FC",
  applicationTenantId: "730850865",
  applicationNamespace: "sap.sf",
  applicationVersion: "2411 / 00 / 00",
  name: "BDF_550_Dev",
  className: "SYSTEM",
  formations: [
    {
      catalogUuid: "90263691E3DD4721190043CE640AF6FQ",
      formationId: "8dc6301e-b21d-4232-bc75-53a59ee98faa2",
      formationTypeId: "d6d3492c-a1b0-40a6-8fb8-43b7097568aq",
      assignmentId: "67afb815-682b-4ae9-83e0-bd163bdc8364",
      formationName: "test1",
    },
  ],
  propertyTags: [
    {
      propertyId: "SP_UCL_DEPLOYMENT_REGION",
      name: "Deployment Region",
      namespace: "com.sap.ucl",
      valueObjects: [
        {
          valueString: "",
        },
      ],
    },
    {
      propertyId: "SP_UCL_SYSTEM_TYPE",
      name: "System Type",
      namespace: "com.sap.ucl",
      valueObjects: [
        {
          valueString: "Successfactor",
        },
      ],
    },
    {
      propertyId: "SPESU",
      name: "System URL",
      namespace: "catalog",
      valueObjects: [
        {
          valueString: "https://ldcibdf.devsys.net.sap",
        },
      ],
    },
    {
      propertyId: "SP_UCL_SYSTEM_VERSION",
      name: "System Version",
      namespace: "com.sap.ucl",
      valueObjects: [
        {
          valueString: "2025 / 00 / 00",
        },
      ],
    },
  ],
};

const analyticsSystem = [
  {
    systemId: "3A3A11E4539A670B190084E2411F8BC4",
    applicationTenantId: "85ff2295-3119-4170-86b0-216dab90edb7",
    applicationNamespace: "sap.analytics",
    applicationVersion: "9999",
    name: "sac-bdc-starkiller-hc-ga.starkiller",
    className: "SYSTEM",
    formations: [
      {
        catalogUuid: "90263691E3DD4721190043CE640AF6FC",
        formationId: "8dc6301e-b21d-4232-bc75-53a59ee98faa",
        formationTypeId: "d6d3492c-a1b0-40a6-8fb8-43b7097568ae",
        assignmentId: "332922c0-cedd-4e2e-9be6-cb5601084013",
        formationName: "test1",
      },
    ],
    propertyTags: [
      {
        propertyId: "SP_UCL_DEPLOYMENT_REGION",
        name: "Deployment Region",
        namespace: "com.sap.ucl",
        valueObjects: [
          {
            valueString: "sac-orcastarkiller_INT",
          },
        ],
      },
      {
        propertyId: "SP_UCL_SYSTEM_TYPE",
        name: "System Type",
        namespace: "com.sap.ucl",
        valueObjects: [
          {
            valueString: "SAP Analytics Cloud (orcastarkiller)",
          },
        ],
      },
      {
        propertyId: "SPESU",
        name: "System URL",
        namespace: "catalog",
        valueObjects: [
          {
            valueString: "https://sac-bdc-starkiller-hc-ga.starkiller.hanacloudservices.cloud.sap",
          },
        ],
      },
    ],
  },
];
const datasphereSystem = [
  {
    systemId: "393A11E4539A670B190084E2411F8BC4",
    applicationTenantId: "4aa9f0aa-8f84-4094-a5db-010a0b997063",
    applicationNamespace: "sap.datasphere",
    applicationVersion: "9999",
    name: "dsp-bdc-starkiller-hc-ga.starkiller",
    className: "SYSTEM",
    formations: [
      {
        catalogUuid: "90263691E3DD4721190043CE640AF6FC",
        formationId: "8dc6301e-b21d-4232-bc75-53a59ee98faa",
        formationTypeId: "d6d3492c-a1b0-40a6-8fb8-43b7097568ae",
        assignmentId: "7b8daf92-f8ba-4141-bed2-ffb5c4db9496",
        formationName: "test1",
      },
    ],
    propertyTags: [
      {
        propertyId: "SP_UCL_DEPLOYMENT_REGION",
        name: "Deployment Region",
        namespace: "com.sap.ucl",
        valueObjects: [
          {
            valueString: "cf-eu10-canary",
          },
        ],
      },
      {
        propertyId: "SP_UCL_SYSTEM_TYPE",
        name: "System Type",
        namespace: "com.sap.ucl",
        valueObjects: [
          {
            valueString: "SAP Datasphere",
          },
        ],
      },
      {
        propertyId: "SPESU",
        name: "System URL",
        namespace: "catalog",
        valueObjects: [
          {
            valueString: "https://dsp-bdc-starkiller-hc-ga.starkiller.hanacloudservices.cloud.sap",
          },
        ],
      },
    ],
  },
];
const datasphereNonBdcSystem = [
  {
    systemId: "393A11E4539A670B190084E2411F8BC4",
    applicationTenantId: "4aa9f0aa-8f84-4094-a5db-010a0b997063",
    applicationNamespace: "sap.datasphere",
    applicationVersion: "9999",
    name: "dsp-bdc-starkiller-hc-ga.starkiller",
    className: "SYSTEM",
    formations: [
      {
        catalogUuid: "90263691E3DD4721190043CE640AF6FC33",
        formationId: "8dc6301e-b21d-4232-bc75-53a59ee98faa33",
        formationTypeId: "d6d3492c-a1b0-40a6-8fb8-43b7097568ae33",
        assignmentId: "7b8daf92-f8ba-4141-bed2-ffb5c4db949633",
        formationName: "test3",
      },
    ],
    propertyTags: [
      {
        propertyId: "SP_UCL_DEPLOYMENT_REGION",
        name: "Deployment Region",
        namespace: "com.sap.ucl",
        valueObjects: [
          {
            valueString: "cf-eu10-canary",
          },
        ],
      },
      {
        propertyId: "SP_UCL_SYSTEM_TYPE",
        name: "System Type",
        namespace: "com.sap.ucl",
        valueObjects: [
          {
            valueString: "SAP Datasphere",
          },
        ],
      },
      {
        propertyId: "SPESU",
        name: "System URL",
        namespace: "catalog",
        valueObjects: [
          {
            valueString: "https://dsp-bdc-starkiller-hc-ga.starkiller.hanacloudservices.cloud.sap",
          },
        ],
      },
    ],
  },
];
const allSystems = bdcSystem
  .concat(s4pceSystem)
  .concat(hcmSystem)
  .concat(analyticsSystem)
  .concat(datasphereSystem)
  .concat(datasphereNonBdcSystem);

const specificAvailableSystems = bdcSystem
  .concat(s4pceSystem)
  .concat(analyticsSystem)
  .concat(datasphereSystem)
  .concat(datasphereNonBdcSystem);

const instPackage = {
  rootObjectIds: [
    "6F910311F23A592D1900FE1E0614FEB7",
    "8D9A4513F23A592D1900FE1E0614FEB7",
    "D5020311F23A592D1900FE1E0614FEB7",
    "29F3EC10F23A592D1900FE1E0614FEB7",
    "8719F212F23A592D1900FE1E0614FEB7",
    "A218F212F23A592D1900FE1E0614FEB7",
  ],
  allObjects: [
    {
      originalPackage: {
        id: "BDC_Test_FOS_InsightApp_Dummy_Product",
      },
      propertyTags: [
        {
          propertyId: "CP_BDC_INSTALL_JSON_DEF",
          namespace: "bdc",
          name: "Package Installation Status Info",
          valueObjects: [
            {
              valueText: "{}",
            },
          ],
        },
        {
          propertyId: "CP_BDC_DATA_PACKAGE_ID",
          namespace: "bdc",
          name: "Data Package ID",
          valueObjects: [
            {
              valueString: "pylon-pkg-id",
            },
          ],
        },
        {
          propertyId: "CP_BDC_COCKPIT_TENANT_ID",
          namespace: "bdc",
          name: "Tenant ID",
          valueObjects: [
            {
              valueString: "20a76535-ccb4-4033-9773-af4345f2b916",
            },
          ],
        },
      ],
      navigationLinks: [
        {
          relationshipTypeId: "CRT_BDC_INSTALL_TO_FORMATION",
          links: [
            {
              refId: "90263691E3DD4721190043CE640AF6FC",
              direction: "SOURCE_TO_TARGET",
            },
          ],
        },
        {
          relationshipTypeId: "CRT_BDC_TARGET_DSP",
          links: [
            {
              refId: "393A11E4539A670B190084E2411F8BC4",
              direction: "SOURCE_TO_TARGET",
            },
          ],
        },
        {
          relationshipTypeId: "CRT_BDC_TARGET_SAC",
          links: [
            {
              refId: "3A3A11E4539A670B190084E2411F8BC4",
              direction: "SOURCE_TO_TARGET",
            },
          ],
        },
        {
          relationshipTypeId: "CRT_BDC_DP_SOURCE_SYSTEM",
          links: [
            {
              refId: "212B3691E3DD4721190043CE640AF6FC",
              direction: "SOURCE_TO_TARGET",
            },
          ],
        },
      ],
      id: "29F3EC10F23A592D1900FE1E0614FEB7",
      name: "I345",
      customSpace: "bdc",
      className: "installedPackage",
      lastModifiedAt: null,
      createdAt: null,
    },
  ],
};
