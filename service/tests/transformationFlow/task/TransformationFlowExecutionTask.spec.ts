/** @format */

import { <PERSON>a<PERSON>rror } from "@sap/prom-hana-client";
import chai, { expect } from "chai";
import sinon from "sinon";
import sinon<PERSON><PERSON> from "sinon-chai";
import { FeatureFlagProvider } from "../../../featureflags/FeatureFlagProvider";
import { DbClient } from "../../../lib/DbClient";
import { testTenantUuid } from "../../../lib/node";
import { RequestContext } from "../../../repository/security/requestContext";
import * as expensiveMemory from "../../../resourceMonitoring/expensiveMemory";
import * as SpaceVersionChecker from "../../../reuseComponents/onboarding/src/SpaceVersionChecker";
import * as deploySpaceTecTables from "../../../reuseComponents/spaceTecTables/src/deploySpaceTecTables";
import { CustomerHana, ISchemaNames } from "../../../reuseComponents/spaces/src";
import { CustomerHanaRuntimeData } from "../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { CustomerHanaSpace } from "../../../reuseComponents/spaces/src/CustomerHanaSpace";
import { ProcedureBasedTaskCentralPollingInfo } from "../../../reuseComponents/utility/ProcedureBasedTaskObserver";
import * as objectDependency from "../../../routes/advisor/access/objectDependency";
import { LsSmApi } from "../../../routes/space/ls/lsSmApi";
import { PreRequisites4PersistencyChecker } from "../../../routes/viewPersistencyMonitor/util/PreRequisites4PersistencyChecker";
import { Status, Substatus } from "../../../task/logger/models";
import { LabelName, MetricName, TaskMetricsPayload } from "../../../task/logger/models/ITaskMetrics";
import { TaskLogger } from "../../../task/logger/services/logger/TaskLogger";
import { TaskMetricService } from "../../../task/logger/services/metrics/TaskMetricService";
import { Activity } from "../../../task/models";
import { ExecuteResult } from "../../../task/models/TaskExecuteResponse";
import { ChainParameters } from "../../../task/orchestrator/models/Parameters";
import TransformationFlowUtils, {
  TransformationFlowLoadType,
  TransformationFlowRuntime,
} from "../../../transformationFlow/commonUtil";
import { TransformationFlowExecutionRuntimeSettings } from "../../../transformationFlow/monitor/TransformationFlowExecutionRuntimeSettings";
import {
  IRuntimeTransformationFlowDefinition,
  IViewTransformParameterExtended,
  ViewTransformParameterSemantic,
  ViewTransformType,
} from "../../../transformationFlow/run/model";
import TransformationFlowRun from "../../../transformationFlow/run/run";
import { TransformationFlowExecutionTask } from "../../../transformationFlow/task/TransformationFlowExecutionTask";
import { TransformationFlowTask } from "../../../transformationFlow/task/TransformationFlowTask";
import { TransformationFlowTaskLog } from "../../../transformationFlow/task/constants";

chai.use(sinonChai);

describe("TransformationFlowExecutionTask", function () {
  const context = RequestContext.createFromTenantId(testTenantUuid);
  const transformationFlowName = "SAMPLE_TRANSFORMATION_FLOW";
  const spaceName = "SAMPLE_SPACE";
  const asyncCallId = 16;
  const taskLogId = 23;
  const centralPollingStartTime = new Date("2023-06-28T07:10:26");

  let taskLoggerStub: sinon.SinonStubbedInstance<TaskLogger>;
  let dbClientStub: sinon.SinonStubbedInstance<DbClient>;
  let isSpaceLockedStub: sinon.SinonStub;
  let getRuntimeTransformationFlowDefinitionStub: sinon.SinonStub;
  let deploySpaceTecTablesStub: sinon.SinonStub;
  let undeploySpaceTecTablesStub: sinon.SinonStub;
  let trackSingleResourceConsumpConnectionStartStub: sinon.SinonStub;
  let trackSingleResourceConsumpConnectionEndStub: sinon.SinonStub;
  let taskMetricServiceInsertListStub: sinon.SinonStub;
  let mapErrorToMessageAndSubstatusSpy: sinon.SinonSpy;
  let isSpaceVersionEqualOrHigherStub: sinon.SinonStub;
  let getConsumedViewsWithDacStub: sinon.SinonStub;
  let retrieveRemoteTablesDependencyStub: sinon.SinonStub;

  before(() => {
    sinon.stub(FeatureFlagProvider, "getFeatureFlags").resolves({
      DWCO_TRANSFORMATION_FLOW_PLAN_VIZ: true,
      DWCO_TRF_BLOCK_DYN_DAC: true,
      DWCO_LARGE_SYSTEMS_SPARK_SELECTION: true,
      DWCO_TRF_INPUT_PARAMETERS_SUPPORT: true,
      DWCO_LOCAL_TABLE_FILES_SIZE_DELTA_TABLE: true,
      DWCO_LSA_RESOURCE_MONITOR: true,
      DWCO_TRF_BATCHES: true,
      DWCO_TRF_SPARK_AGGR: true,
    } as any);

    taskLoggerStub = sinon.createStubInstance(TaskLogger);
    taskLoggerStub.getLogId.returns(taskLogId);

    dbClientStub = sinon.createStubInstance(DbClient);

    const customerHanaSpaceStub = sinon.createStubInstance(CustomerHanaSpace);

    const schemaNames: Partial<ISchemaNames> = { space_schema: spaceName, spc_tec_internal: spaceName + `$TEC` };

    customerHanaSpaceStub.getSchemaNames.resolves(schemaNames as any);

    const customerHanaStub = sinon.createStubInstance(CustomerHana);

    customerHanaStub.getSpaceOwnerClient.withArgs(spaceName, sinon.match.any).resolves(dbClientStub as any);

    customerHanaStub.selectSpace.withArgs(spaceName).returns(customerHanaSpaceStub as any);

    sinon
      .stub(CustomerHana, "fromRequestContext")
      .withArgs(context)
      .resolves(customerHanaStub as any);

    isSpaceLockedStub = sinon.stub(CustomerHanaRuntimeData.prototype, "isSpaceLocked");

    getRuntimeTransformationFlowDefinitionStub = sinon.stub(
      TransformationFlowRun,
      "getRuntimeTransformationFlowDefinition"
    );

    sinon
      .stub(TransformationFlowExecutionTask.prototype as any, "getCentralPollingStartTime")
      .returns(centralPollingStartTime);
    sinon.stub(LsSmApi.prototype, "getSparkApplicationsConfig").resolves([
      {
        identifier: {
          index: "400",
          label: "Medium",
        },
        preference: {
          transformation: true,
        },
      },
    ]);
    sinon.stub(TransformationFlowExecutionRuntimeSettings.prototype, "get").resolves({
      transformationFlowName: "SAMPLE_TRANSFORMATION_FLOW",
      executionMode: 0,
      sparkRemoteSource: "400",
      batchProcessingDesigntime: null,
    });
    sinon.stub(TransformationFlowUtils, "getSpaceDefaultSparkApplicationName").returns("400");
    sinon.stub(TransformationFlowUtils, "getCpuAndMemoryForSparkRemoteSource").returns("168 vCPU, 672 GiB");

    deploySpaceTecTablesStub = sinon.stub(deploySpaceTecTables, "deploySpaceTecTables");
    undeploySpaceTecTablesStub = sinon.stub(deploySpaceTecTables, "undeploySpaceTecTables");

    trackSingleResourceConsumpConnectionStartStub = sinon.stub(
      expensiveMemory,
      "trackSingleResourceConsumpConnectionStart"
    );

    trackSingleResourceConsumpConnectionEndStub = sinon.stub(
      expensiveMemory,
      "trackSingleResourceConsumpConnectionEnd"
    );

    taskMetricServiceInsertListStub = sinon.stub(TaskMetricService, "insertList");

    mapErrorToMessageAndSubstatusSpy = sinon.spy(TransformationFlowTask, "mapErrorToMessageAndSubstatus");

    isSpaceVersionEqualOrHigherStub = sinon.stub(SpaceVersionChecker, "isSpaceVersionEqualOrHigher").resolves(true);

    getConsumedViewsWithDacStub = sinon.stub(PreRequisites4PersistencyChecker, "getConsumedViewsWithDac");

    retrieveRemoteTablesDependencyStub = sinon.stub(objectDependency, "retrieveRemoteTablesDependency");
  });

  beforeEach(() => {
    isSpaceLockedStub.resetBehavior();
    getRuntimeTransformationFlowDefinitionStub.resetBehavior();
    dbClientStub.execute.resetBehavior();
    getConsumedViewsWithDacStub.resolves([]);
    retrieveRemoteTablesDependencyStub.resetBehavior();
  });

  afterEach(() => {
    sinon.resetHistory();
    dbClientStub.execute.resetHistory();
    taskLoggerStub.updateExternalInstanceId.resetHistory();
  });

  after(() => {
    sinon.restore();
  });

  describe("execute", () => {
    const sourceColumnName2 = "s2";
    const targetColumnName2 = "t2";
    const sourceColumnName3 = "s3";
    const targetColumnName3 = "t3";
    const deltaLeadingSourceName = "SAMPLE_DELTA_LEAD_SRC";

    const runtimeDefinitionTemplate = {
      transformationFlowGuid: "SAMPLE_GUID",
      viewTransformName: transformationFlowName + `$VT`,
      datasphereTargetName: "SAMPLE_TARGET_TABLE",
      attributeMappings: [
        { source: "s1", target: "t1" },
        { source: sourceColumnName2, target: targetColumnName2 },
        { source: sourceColumnName3, target: targetColumnName3 },
        { source: "s4", target: "t4" },
      ],
      viewTransformOperations: [
        { id: "sqltransform1", parent_id: "sqlscripttransform3", type: ViewTransformType.sql },
        { id: "sqltransform2", parent_id: "sqlscripttransform3", type: ViewTransformType.sql },
        { id: "sqltransform3", parent_id: "sqlscripttransform3", type: ViewTransformType.sql },
        { id: "sqlscripttransform1", parent_id: "sqlscripttransform3", type: ViewTransformType.sqlScript },
        { id: "sqlscripttransform2", parent_id: "sqlscripttransform3", type: ViewTransformType.sqlScript },
        { id: "sqlscripttransform3", parent_id: undefined, type: ViewTransformType.graphical },
      ],
      runtime: TransformationFlowRuntime.hana,
      pythonOperations: [
        { id: "python1", parent_id: "sqlscripttransform3" },
        { id: "python2", parent_id: "python1" },
      ],
      incrementalAggregationEnabled: true,
    };

    const runtimeDefinitionNotDeltaEnabled: IRuntimeTransformationFlowDefinition = {
      ...runtimeDefinitionTemplate,
      loadType: TransformationFlowLoadType.initialOnly,
      truncate: true,
      deltaEnabled: false,
      sqlTargetName: runtimeDefinitionTemplate.datasphereTargetName,
      datasphereTargetName: runtimeDefinitionTemplate.datasphereTargetName,
    };

    const runtimeDefinitionDeltaEnabledInitialOnly: IRuntimeTransformationFlowDefinition = {
      ...runtimeDefinitionTemplate,
      loadType: TransformationFlowLoadType.initialOnly,
      truncate: false,
      deltaEnabled: true,
      sqlTargetName: runtimeDefinitionTemplate.datasphereTargetName + `$T`,
      datasphereTargetName: runtimeDefinitionTemplate.datasphereTargetName,
      deltaLeadingSourceName,
      viewTransformCdcTstmpColumnName: sourceColumnName2,
      viewTransformCdcTypeColumnName: sourceColumnName3,
      targetCdcTstmpColumnName: targetColumnName2,
      targetCdcTypeColumnName: targetColumnName3,
    };

    const runtimeDefinitionDeltaEnabledInitialAndDelta: IRuntimeTransformationFlowDefinition = {
      ...runtimeDefinitionDeltaEnabledInitialOnly,
      loadType: TransformationFlowLoadType.initialAndDelta,
    };

    const runtimeDefinitionDeltaEnabledInitialAndDeltaViewTransformParameters: IRuntimeTransformationFlowDefinition = {
      ...runtimeDefinitionDeltaEnabledInitialAndDelta,
      viewTransformParameters: [
        { name: "EXTRACTION_MODE_Parameter", semantic: ViewTransformParameterSemantic.extractionMode },
        { name: "REQTSN_LOW_Parameter", semantic: ViewTransformParameterSemantic.lowerBoundary },
        { name: "REQTSN_HIGH_Parameter", semantic: ViewTransformParameterSemantic.upperBoundary },
      ],
    };
    const runtimeDefinitionWithInputParameters: IRuntimeTransformationFlowDefinition = {
      ...runtimeDefinitionDeltaEnabledInitialAndDelta,
      viewTransformParameters: [
        {
          name: "PARAM_1",
          semantic: ViewTransformParameterSemantic.userDefined,
          defaultValue: "test",
          dataType: "cds.String",
        },
        {
          name: "PARAM_2",
          semantic: ViewTransformParameterSemantic.userDefined,
          defaultValue: "test",
          dataType: "cds.String",
        },
        {
          name: "PARAM_3",
          semantic: ViewTransformParameterSemantic.userDefined,
          defaultValue: "10",
          dataType: "cds.Integer",
        },
      ],
    };
    const viewTransformParametersExtended: IViewTransformParameterExtended[] = [
      {
        name: "PARAM_1",
        semantic: ViewTransformParameterSemantic.userDefined,
        defaultValue: "test",
        dataType: "cds.String",
        value: "value1",
      },
      {
        name: "PARAM_2",
        semantic: ViewTransformParameterSemantic.userDefined,
        defaultValue: "test",
        dataType: "cds.String",
        value: "value2",
      },
      {
        name: "PARAM_3",
        semantic: ViewTransformParameterSemantic.userDefined,
        defaultValue: "10",
        dataType: "cds.Integer",
        value: "12",
      },
    ];
    const viewTransformParametersExtendedTaskChain: IViewTransformParameterExtended[] = [
      {
        name: "PARAM_1",
        semantic: ViewTransformParameterSemantic.userDefined,
        defaultValue: "test",
        dataType: "cds.String",
        value: "chain1",
      },
      {
        name: "PARAM_2",
        semantic: ViewTransformParameterSemantic.userDefined,
        defaultValue: "test",
        dataType: "cds.String",
        value: "chain2",
      },
      {
        name: "PARAM_3",
        semantic: ViewTransformParameterSemantic.userDefined,
        defaultValue: "10",
        dataType: "cds.Integer",
        value: "20",
      },
    ];
    const sqlPrefix = `CALL "SAMPLE_SPACE$TEC"."TRFFL_EXECUTE" (TASK_LOG_ID => ${taskLogId}, SCHEMA_NAME => '${spaceName}', TRANSFORMATION_FLOW_GUID => '${runtimeDefinitionTemplate.transformationFlowGuid}', `;

    const sqlSuffix = ` ASYNC`;

    const columnMappingsSnippetDeltaEnabled = `COLUMN_MAPPINGS_JSON => '${JSON.stringify(
      runtimeDefinitionTemplate.attributeMappings.filter(
        (attributeMapping) => attributeMapping.target !== targetColumnName2
      )
    )}'`;

    const testCases: Array<{
      activity: Activity;
      spaceIsLocked: boolean;
      consumedViewsWithDac?: Array<{ viewName: string; spaceId: string }>;
      hasRemoteTable?: boolean;
      runtimeDefinition: IRuntimeTransformationFlowDefinition;
      dbClientExecuteError?: HanaError;
      executeResultExpected: ExecuteResult;
      sqlExpected?: string;
      planViz?: boolean;
      simulateRun?: boolean;
      flowParameters?: { [key: string]: any };
      chainParameters?: ChainParameters;
    }> = [
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: true,
        runtimeDefinition: runtimeDefinitionNotDeltaEnabled,
        executeResultExpected: { status: Status.FAILED, subStatusCode: undefined },
      },
      {
        activity: Activity.SIMULATE_RUN,
        spaceIsLocked: true,
        runtimeDefinition: runtimeDefinitionNotDeltaEnabled,
        executeResultExpected: { status: Status.FAILED, subStatusCode: undefined },
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        consumedViewsWithDac: [{ viewName: "SAMPLE_VIEW", spaceId: spaceName }],
        runtimeDefinition: runtimeDefinitionNotDeltaEnabled,
        executeResultExpected: { status: Status.FAILED, subStatusCode: undefined },
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        consumedViewsWithDac: [{ viewName: "SAMPLE_VIEW", spaceId: "externalSpaceName" }],
        runtimeDefinition: runtimeDefinitionNotDeltaEnabled,
        executeResultExpected: { status: Status.FAILED, subStatusCode: undefined },
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        hasRemoteTable: true,
        runtimeDefinition: runtimeDefinitionNotDeltaEnabled,
        executeResultExpected: { status: Status.FAILED, subStatusCode: undefined },
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        runtimeDefinition: runtimeDefinitionNotDeltaEnabled,
        executeResultExpected: { status: Status.RUNNING },
        planViz: true,
        sqlExpected:
          sqlPrefix +
          `SQL_TARGET_TABLE_NAME => '${runtimeDefinitionNotDeltaEnabled.sqlTargetName}', DATASPHERE_TARGET_TABLE_NAME => '${runtimeDefinitionNotDeltaEnabled.datasphereTargetName}', TRUNCATE_TARGET_TABLE => ${runtimeDefinitionNotDeltaEnabled.truncate}, ` +
          `LOAD_TYPE => '${runtimeDefinitionNotDeltaEnabled.loadType}', DELTA_ENABLED => ${runtimeDefinitionNotDeltaEnabled.deltaEnabled}, ` +
          `VIEW_TRANSFORM_NAME => '${runtimeDefinitionTemplate.viewTransformName}', TRANSFORMATION_FLOW_NAME => '${transformationFlowName}', ` +
          `COLUMN_MAPPINGS_JSON => '${JSON.stringify(
            runtimeDefinitionTemplate.attributeMappings
          )}', FF_DWCO_TRF_BLOCK_DYN_DAC => true, FF_DWCO_TRF_INPUT_PARAMETERS_SUPPORT => true, ` +
          `FF_DWCO_TRF_BATCHES => true, GET_PLANVIZ => true)` +
          sqlSuffix,
      },
      {
        activity: Activity.SIMULATE_RUN,
        spaceIsLocked: false,
        runtimeDefinition: runtimeDefinitionNotDeltaEnabled,
        executeResultExpected: { status: Status.RUNNING },
        planViz: true,
        simulateRun: true,
        sqlExpected:
          sqlPrefix +
          `SQL_TARGET_TABLE_NAME => '${runtimeDefinitionNotDeltaEnabled.sqlTargetName}', DATASPHERE_TARGET_TABLE_NAME => '${runtimeDefinitionNotDeltaEnabled.datasphereTargetName}', TRUNCATE_TARGET_TABLE => ${runtimeDefinitionNotDeltaEnabled.truncate}, ` +
          `LOAD_TYPE => '${runtimeDefinitionNotDeltaEnabled.loadType}', DELTA_ENABLED => ${runtimeDefinitionNotDeltaEnabled.deltaEnabled}, ` +
          `VIEW_TRANSFORM_NAME => '${runtimeDefinitionTemplate.viewTransformName}', TRANSFORMATION_FLOW_NAME => '${transformationFlowName}', ` +
          `COLUMN_MAPPINGS_JSON => '${JSON.stringify(
            runtimeDefinitionTemplate.attributeMappings
          )}', FF_DWCO_TRF_BLOCK_DYN_DAC => true, FF_DWCO_TRF_INPUT_PARAMETERS_SUPPORT => true, ` +
          `FF_DWCO_TRF_BATCHES => true, GET_PLANVIZ => true, SIMULATE_RUN => true)` +
          sqlSuffix,
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        runtimeDefinition: {
          ...runtimeDefinitionNotDeltaEnabled,
          targetCdcTstmpColumnName: targetColumnName2,
          targetCdcTypeColumnName: targetColumnName3,
        },
        executeResultExpected: { status: Status.RUNNING },
        planViz: false,
        sqlExpected:
          sqlPrefix +
          `SQL_TARGET_TABLE_NAME => '${runtimeDefinitionNotDeltaEnabled.sqlTargetName}', DATASPHERE_TARGET_TABLE_NAME => '${runtimeDefinitionNotDeltaEnabled.datasphereTargetName}', TRUNCATE_TARGET_TABLE => ${runtimeDefinitionNotDeltaEnabled.truncate}, ` +
          `LOAD_TYPE => '${runtimeDefinitionNotDeltaEnabled.loadType}', DELTA_ENABLED => ${runtimeDefinitionNotDeltaEnabled.deltaEnabled}, ` +
          `VIEW_TRANSFORM_NAME => '${runtimeDefinitionTemplate.viewTransformName}', TRANSFORMATION_FLOW_NAME => '${transformationFlowName}', ` +
          columnMappingsSnippetDeltaEnabled +
          `, FF_DWCO_TRF_BLOCK_DYN_DAC => true, FF_DWCO_TRF_INPUT_PARAMETERS_SUPPORT => true, ` +
          `FF_DWCO_TRF_BATCHES => true, TARGET_TABLE_CDC_TST_COLUMN => '${targetColumnName2}', TARGET_TABLE_CDC_TYPE_COLUMN => '${targetColumnName3}', ` +
          `GET_PLANVIZ => false)` +
          sqlSuffix,
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        runtimeDefinition: runtimeDefinitionDeltaEnabledInitialOnly,
        executeResultExpected: { status: Status.RUNNING },
        planViz: false,
        sqlExpected:
          sqlPrefix +
          `SQL_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialOnly.sqlTargetName}', DATASPHERE_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialOnly.datasphereTargetName}', TRUNCATE_TARGET_TABLE => ${runtimeDefinitionDeltaEnabledInitialOnly.truncate}, ` +
          `LOAD_TYPE => '${runtimeDefinitionDeltaEnabledInitialOnly.loadType}', DELTA_ENABLED => ${runtimeDefinitionDeltaEnabledInitialOnly.deltaEnabled}, ` +
          `DELTA_PROVIDER_NAME => '${runtimeDefinitionDeltaEnabledInitialOnly.deltaLeadingSourceName}', ` +
          `VIEW_TRANSFORM_NAME => '${runtimeDefinitionTemplate.viewTransformName}', TRANSFORMATION_FLOW_NAME => '${transformationFlowName}', ` +
          columnMappingsSnippetDeltaEnabled +
          `, FF_DWCO_TRF_BLOCK_DYN_DAC => true, FF_DWCO_TRF_INPUT_PARAMETERS_SUPPORT => true, ` +
          `FF_DWCO_TRF_BATCHES => true, VIEW_TRANSFORM_CDC_TST_COLUMN => '${sourceColumnName2}', VIEW_TRANSFORM_CDC_TYPE_COLUMN => '${sourceColumnName3}', ` +
          `TARGET_TABLE_CDC_TST_COLUMN => '${targetColumnName2}', TARGET_TABLE_CDC_TYPE_COLUMN => '${targetColumnName3}', ` +
          `GET_PLANVIZ => false)` +
          sqlSuffix,
      },
      {
        activity: Activity.SIMULATE_RUN,
        spaceIsLocked: false,
        runtimeDefinition: runtimeDefinitionDeltaEnabledInitialOnly,
        executeResultExpected: { status: Status.RUNNING },
        planViz: false,
        simulateRun: true,
        sqlExpected:
          sqlPrefix +
          `SQL_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialOnly.sqlTargetName}', DATASPHERE_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialOnly.datasphereTargetName}', TRUNCATE_TARGET_TABLE => ${runtimeDefinitionDeltaEnabledInitialOnly.truncate}, ` +
          `LOAD_TYPE => '${runtimeDefinitionDeltaEnabledInitialOnly.loadType}', DELTA_ENABLED => ${runtimeDefinitionDeltaEnabledInitialOnly.deltaEnabled}, ` +
          `DELTA_PROVIDER_NAME => '${runtimeDefinitionDeltaEnabledInitialOnly.deltaLeadingSourceName}', ` +
          `VIEW_TRANSFORM_NAME => '${runtimeDefinitionTemplate.viewTransformName}', TRANSFORMATION_FLOW_NAME => '${transformationFlowName}', ` +
          columnMappingsSnippetDeltaEnabled +
          `, FF_DWCO_TRF_BLOCK_DYN_DAC => true, FF_DWCO_TRF_INPUT_PARAMETERS_SUPPORT => true, ` +
          `FF_DWCO_TRF_BATCHES => true, VIEW_TRANSFORM_CDC_TST_COLUMN => '${sourceColumnName2}', VIEW_TRANSFORM_CDC_TYPE_COLUMN => '${sourceColumnName3}', ` +
          `TARGET_TABLE_CDC_TST_COLUMN => '${targetColumnName2}', TARGET_TABLE_CDC_TYPE_COLUMN => '${targetColumnName3}', ` +
          `GET_PLANVIZ => false, SIMULATE_RUN => true)` +
          sqlSuffix,
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        runtimeDefinition: runtimeDefinitionDeltaEnabledInitialAndDelta,
        executeResultExpected: { status: Status.RUNNING },
        planViz: false,
        sqlExpected:
          sqlPrefix +
          `SQL_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.sqlTargetName}', DATASPHERE_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.datasphereTargetName}', TRUNCATE_TARGET_TABLE => ${runtimeDefinitionDeltaEnabledInitialAndDelta.truncate}, ` +
          `LOAD_TYPE => '${runtimeDefinitionDeltaEnabledInitialAndDelta.loadType}', DELTA_ENABLED => ${runtimeDefinitionDeltaEnabledInitialAndDelta.deltaEnabled}, ` +
          `DELTA_PROVIDER_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.deltaLeadingSourceName}', ` +
          `VIEW_TRANSFORM_NAME => '${runtimeDefinitionTemplate.viewTransformName}', TRANSFORMATION_FLOW_NAME => '${transformationFlowName}', ` +
          columnMappingsSnippetDeltaEnabled +
          `, FF_DWCO_TRF_BLOCK_DYN_DAC => true, FF_DWCO_TRF_INPUT_PARAMETERS_SUPPORT => true, ` +
          `FF_DWCO_TRF_BATCHES => true, VIEW_TRANSFORM_CDC_TST_COLUMN => '${sourceColumnName2}', VIEW_TRANSFORM_CDC_TYPE_COLUMN => '${sourceColumnName3}', ` +
          `TARGET_TABLE_CDC_TST_COLUMN => '${targetColumnName2}', TARGET_TABLE_CDC_TYPE_COLUMN => '${targetColumnName3}', ` +
          `GET_PLANVIZ => false)` +
          sqlSuffix,
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        runtimeDefinition: runtimeDefinitionDeltaEnabledInitialAndDelta,
        executeResultExpected: { status: Status.RUNNING },
        planViz: true,
        sqlExpected:
          sqlPrefix +
          `SQL_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.sqlTargetName}', DATASPHERE_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.datasphereTargetName}', TRUNCATE_TARGET_TABLE => ${runtimeDefinitionDeltaEnabledInitialAndDelta.truncate}, ` +
          `LOAD_TYPE => '${runtimeDefinitionDeltaEnabledInitialAndDelta.loadType}', DELTA_ENABLED => ${runtimeDefinitionDeltaEnabledInitialAndDelta.deltaEnabled}, ` +
          `DELTA_PROVIDER_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.deltaLeadingSourceName}', ` +
          `VIEW_TRANSFORM_NAME => '${runtimeDefinitionTemplate.viewTransformName}', TRANSFORMATION_FLOW_NAME => '${transformationFlowName}', ` +
          columnMappingsSnippetDeltaEnabled +
          `, FF_DWCO_TRF_BLOCK_DYN_DAC => true, FF_DWCO_TRF_INPUT_PARAMETERS_SUPPORT => true, ` +
          `FF_DWCO_TRF_BATCHES => true, VIEW_TRANSFORM_CDC_TST_COLUMN => '${sourceColumnName2}', VIEW_TRANSFORM_CDC_TYPE_COLUMN => '${sourceColumnName3}', ` +
          `TARGET_TABLE_CDC_TST_COLUMN => '${targetColumnName2}', TARGET_TABLE_CDC_TYPE_COLUMN => '${targetColumnName3}', ` +
          `GET_PLANVIZ => true)` +
          sqlSuffix,
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        runtimeDefinition: runtimeDefinitionNotDeltaEnabled,
        dbClientExecuteError: new HanaError("", 4),
        executeResultExpected: { status: Status.FAILED, subStatusCode: Substatus.RESOURCE_LIMIT_ERROR },
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        runtimeDefinition: runtimeDefinitionDeltaEnabledInitialAndDeltaViewTransformParameters,
        executeResultExpected: { status: Status.RUNNING },
        planViz: false,
        sqlExpected:
          sqlPrefix +
          `SQL_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.sqlTargetName}', DATASPHERE_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.datasphereTargetName}', TRUNCATE_TARGET_TABLE => ${runtimeDefinitionDeltaEnabledInitialAndDelta.truncate}, ` +
          `LOAD_TYPE => '${runtimeDefinitionDeltaEnabledInitialAndDelta.loadType}', DELTA_ENABLED => ${runtimeDefinitionDeltaEnabledInitialAndDelta.deltaEnabled}, ` +
          `DELTA_PROVIDER_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.deltaLeadingSourceName}', ` +
          `VIEW_TRANSFORM_NAME => '${runtimeDefinitionTemplate.viewTransformName}', TRANSFORMATION_FLOW_NAME => '${transformationFlowName}', ` +
          columnMappingsSnippetDeltaEnabled +
          `, FF_DWCO_TRF_BLOCK_DYN_DAC => true, FF_DWCO_TRF_INPUT_PARAMETERS_SUPPORT => true, ` +
          `FF_DWCO_TRF_BATCHES => true, VIEW_TRANSFORM_PARAMETERS_JSON => '${JSON.stringify(
            runtimeDefinitionDeltaEnabledInitialAndDeltaViewTransformParameters.viewTransformParameters
          )}', ` +
          `VIEW_TRANSFORM_CDC_TST_COLUMN => '${sourceColumnName2}', VIEW_TRANSFORM_CDC_TYPE_COLUMN => '${sourceColumnName3}', ` +
          `TARGET_TABLE_CDC_TST_COLUMN => '${targetColumnName2}', TARGET_TABLE_CDC_TYPE_COLUMN => '${targetColumnName3}', ` +
          `GET_PLANVIZ => false)` +
          sqlSuffix,
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        runtimeDefinition: { ...runtimeDefinitionNotDeltaEnabled, runtime: TransformationFlowRuntime.spark },
        executeResultExpected: { status: Status.FAILED, subStatusCode: undefined },
      },
      {
        activity: Activity.SIMULATE_RUN,
        spaceIsLocked: false,
        runtimeDefinition: { ...runtimeDefinitionNotDeltaEnabled, runtime: TransformationFlowRuntime.spark },
        executeResultExpected: { status: Status.FAILED, subStatusCode: undefined },
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        runtimeDefinition: { ...runtimeDefinitionDeltaEnabledInitialOnly, runtime: TransformationFlowRuntime.spark },
        executeResultExpected: { status: Status.RUNNING },
        sqlExpected:
          sqlPrefix.replace("TRFFL_EXECUTE", "TRFFL_EXECUTE_SPARK") +
          `SQL_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialOnly.sqlTargetName}', DATASPHERE_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialOnly.datasphereTargetName}', TRUNCATE_TARGET_TABLE => ${runtimeDefinitionDeltaEnabledInitialOnly.truncate}, ` +
          `LOAD_TYPE => '${runtimeDefinitionDeltaEnabledInitialOnly.loadType}', DELTA_ENABLED => ${runtimeDefinitionDeltaEnabledInitialOnly.deltaEnabled}, ` +
          `DELTA_PROVIDER_NAME => '${runtimeDefinitionDeltaEnabledInitialOnly.deltaLeadingSourceName}', ` +
          `VIRTUAL_PROCEDURE_NAME => 'TRF_${transformationFlowName}_$VP', ` +
          `APPLICATION_INDEX => '400', ` +
          `REMOTE_SOURCE_NAME => 'SAMPLE_SPACE.LS_SM_SPARK_400', ` +
          `FF_DWCO_LOCAL_TABLE_FILES_SIZE_DELTA_TABLE => true, ` +
          `FF_DWCO_LSA_RESOURCE_MONITOR => true, ` +
          `FF_DWCO_TRF_INPUT_PARAMETERS_SUPPORT => true)` +
          sqlSuffix,
      },
      {
        activity: Activity.SIMULATE_RUN,
        spaceIsLocked: false,
        runtimeDefinition: { ...runtimeDefinitionDeltaEnabledInitialOnly, runtime: TransformationFlowRuntime.spark },
        executeResultExpected: { status: Status.FAILED, subStatusCode: undefined },
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        runtimeDefinition: runtimeDefinitionWithInputParameters,
        executeResultExpected: { status: Status.RUNNING },
        planViz: false,
        flowParameters: {
          PARAM_1: "value1",
          PARAM_2: "value2",
          PARAM_3: "12",
        },
        sqlExpected:
          sqlPrefix +
          `SQL_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.sqlTargetName}', DATASPHERE_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.datasphereTargetName}', TRUNCATE_TARGET_TABLE => ${runtimeDefinitionDeltaEnabledInitialAndDelta.truncate}, ` +
          `LOAD_TYPE => '${runtimeDefinitionDeltaEnabledInitialAndDelta.loadType}', DELTA_ENABLED => ${runtimeDefinitionDeltaEnabledInitialAndDelta.deltaEnabled}, ` +
          `DELTA_PROVIDER_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.deltaLeadingSourceName}', ` +
          `VIEW_TRANSFORM_NAME => '${runtimeDefinitionTemplate.viewTransformName}', TRANSFORMATION_FLOW_NAME => '${transformationFlowName}', ` +
          columnMappingsSnippetDeltaEnabled +
          `, FF_DWCO_TRF_BLOCK_DYN_DAC => true, FF_DWCO_TRF_INPUT_PARAMETERS_SUPPORT => true, ` +
          `FF_DWCO_TRF_BATCHES => true, VIEW_TRANSFORM_PARAMETERS_JSON => '${JSON.stringify(
            viewTransformParametersExtended
          )}', ` +
          `VIEW_TRANSFORM_CDC_TST_COLUMN => '${sourceColumnName2}', VIEW_TRANSFORM_CDC_TYPE_COLUMN => '${sourceColumnName3}', ` +
          `TARGET_TABLE_CDC_TST_COLUMN => '${targetColumnName2}', TARGET_TABLE_CDC_TYPE_COLUMN => '${targetColumnName3}', ` +
          `GET_PLANVIZ => false)` +
          sqlSuffix,
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        runtimeDefinition: { ...runtimeDefinitionWithInputParameters, runtime: TransformationFlowRuntime.spark },
        executeResultExpected: { status: Status.RUNNING },
        planViz: false,
        flowParameters: {
          PARAM_1: "value1",
          PARAM_2: "value2",
          PARAM_3: "12",
        },
        sqlExpected:
          sqlPrefix.replace("TRFFL_EXECUTE", "TRFFL_EXECUTE_SPARK") +
          `SQL_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.sqlTargetName}', DATASPHERE_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.datasphereTargetName}', TRUNCATE_TARGET_TABLE => ${runtimeDefinitionDeltaEnabledInitialAndDelta.truncate}, ` +
          `LOAD_TYPE => '${runtimeDefinitionDeltaEnabledInitialAndDelta.loadType}', DELTA_ENABLED => ${runtimeDefinitionDeltaEnabledInitialAndDelta.deltaEnabled}, ` +
          `DELTA_PROVIDER_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.deltaLeadingSourceName}', ` +
          `VIRTUAL_PROCEDURE_NAME => 'TRF_${transformationFlowName}_$VP', ` +
          `APPLICATION_INDEX => '400', ` +
          `REMOTE_SOURCE_NAME => 'SAMPLE_SPACE.LS_SM_SPARK_400', ` +
          `FF_DWCO_LOCAL_TABLE_FILES_SIZE_DELTA_TABLE => true, ` +
          `FF_DWCO_LSA_RESOURCE_MONITOR => true, ` +
          `FF_DWCO_TRF_INPUT_PARAMETERS_SUPPORT => true, ` +
          `INPUT_PARAMETERS_JSON => '${JSON.stringify(viewTransformParametersExtended)}')` +
          sqlSuffix,
      },
      {
        activity: Activity.EXECUTE,
        spaceIsLocked: false,
        runtimeDefinition: { ...runtimeDefinitionWithInputParameters, runtime: TransformationFlowRuntime.spark },
        executeResultExpected: { status: Status.RUNNING },
        planViz: false,
        chainParameters: {
          Input: {
            PARAM_1: "chain1",
            PARAM_2: "chain2",
            PARAM_3: "20",
          },
        },
        sqlExpected:
          sqlPrefix.replace("TRFFL_EXECUTE", "TRFFL_EXECUTE_SPARK") +
          `SQL_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.sqlTargetName}', DATASPHERE_TARGET_TABLE_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.datasphereTargetName}', TRUNCATE_TARGET_TABLE => ${runtimeDefinitionDeltaEnabledInitialAndDelta.truncate}, ` +
          `LOAD_TYPE => '${runtimeDefinitionDeltaEnabledInitialAndDelta.loadType}', DELTA_ENABLED => ${runtimeDefinitionDeltaEnabledInitialAndDelta.deltaEnabled}, ` +
          `DELTA_PROVIDER_NAME => '${runtimeDefinitionDeltaEnabledInitialAndDelta.deltaLeadingSourceName}', ` +
          `VIRTUAL_PROCEDURE_NAME => 'TRF_${transformationFlowName}_$VP', ` +
          `APPLICATION_INDEX => '400', ` +
          `REMOTE_SOURCE_NAME => 'SAMPLE_SPACE.LS_SM_SPARK_400', ` +
          `FF_DWCO_LOCAL_TABLE_FILES_SIZE_DELTA_TABLE => true, ` +
          `FF_DWCO_LSA_RESOURCE_MONITOR => true, ` +
          `FF_DWCO_TRF_INPUT_PARAMETERS_SUPPORT => true, ` +
          `INPUT_PARAMETERS_JSON => '${JSON.stringify(viewTransformParametersExtendedTaskChain)}')` +
          sqlSuffix,
      },
    ];

    function deploySpaceTecTablesCalls() {
      const spaceTecTablesToBeDeployedExpected = ["TRFFL_EXECUTE_RT_SETTINGS", "DELTA_PROVIDER_SUBSCRIBER"];

      expect(deploySpaceTecTablesStub).to.be.calledOnceWithExactly(
        spaceTecTablesToBeDeployedExpected,
        spaceName,
        context
      );

      expect(undeploySpaceTecTablesStub).to.be.calledOnceWithExactly(["TRFFL_EXECUTE_RT_DATA"], spaceName, context);
    }

    function trackSingleResourceConsumpCalls() {
      expect(trackSingleResourceConsumpConnectionStartStub).to.be.calledOnceWithExactly(taskLogId, context, {
        dbClient: dbClientStub,
        time: sinon.match.string,
      });

      expect(trackSingleResourceConsumpConnectionEndStub).to.be.calledOnceWithExactly(taskLogId, context, {
        dbClient: dbClientStub,
        time: sinon.match.string,
      });
    }

    testCases.forEach((testCase) => {
      const { executeResultExpected, sqlExpected, ...testCaseInput } = testCase;
      const transformationFlowExecutionTask = new TransformationFlowExecutionTask(
        context,
        spaceName,
        transformationFlowName,
        testCase.activity,
        {
          settings: {
            getPlanViz: testCase.planViz,
            simulateRun: testCase.simulateRun,
          },
          flowParameters: testCase.flowParameters,
        }
      );
      it(JSON.stringify(testCaseInput), async () => {
        if (testCase.consumedViewsWithDac) {
          getConsumedViewsWithDacStub.resolves(testCase.consumedViewsWithDac);
        }
        transformationFlowExecutionTask.chainParameters = testCase.chainParameters;

        retrieveRemoteTablesDependencyStub
          .rejects()
          .withArgs(sinon.match.any, spaceName, [
            { space: spaceName, entity: testCase.runtimeDefinition.viewTransformName },
          ])
          .resolves(testCase.hasRemoteTable ?? false ? [{ space: spaceName, entity: "testRemoteTable" }] : []);

        if (testCase.dbClientExecuteError) {
          dbClientStub.execute.throws(testCase.dbClientExecuteError);
        } else {
          dbClientStub.execute.callsFake(async (sql: string) => {
            if (sql.startsWith("CALL") && sql.endsWith("ASYNC")) {
              return [{ ASYNC_CALL_ID: asyncCallId }];
            }
          });
        }
        isSpaceLockedStub.withArgs(spaceName).resolves(testCase.spaceIsLocked);

        getRuntimeTransformationFlowDefinitionStub
          .withArgs(context, transformationFlowName, spaceName, dbClientStub)
          .resolves(testCase.runtimeDefinition);

        const result = await transformationFlowExecutionTask.execute(taskLoggerStub);
        expect(result).to.deep.equal(testCase.executeResultExpected);

        if (testCase.executeResultExpected.status === Status.RUNNING) {
          expect(dbClientStub.execute).to.be.calledOnceWithExactly(testCase.sqlExpected);

          const centralPollingInfo: ProcedureBasedTaskCentralPollingInfo = {
            asyncCallId,
            pollStart: centralPollingStartTime,
          };
          expect(taskLoggerStub.updateExternalInstanceId).to.be.calledOnceWithExactly(
            JSON.stringify(centralPollingInfo)
          );

          trackSingleResourceConsumpCalls();

          deploySpaceTecTablesCalls();

          const taskMetricsPayloadsExpected: TaskMetricsPayload[] = [
            { taskLogId, name: MetricName.LOAD_TYPE, value: testCase.runtimeDefinition.loadType },
            { taskLogId, name: MetricName.TRUNCATE, value: String(testCase.runtimeDefinition.truncate) },
            {
              taskLogId,
              name: MetricName.DELTA,
              value: String(testCase.runtimeDefinition.viewTransformCdcTstmpColumnName ? true : false),
              labels: [{ name: LabelName.SOURCE, value: "" }],
            },
            {
              taskLogId,
              name: MetricName.DELTA,
              value: String(testCase.runtimeDefinition.targetCdcTstmpColumnName ? true : false),
              labels: [{ name: LabelName.TARGET, value: "" }],
            },
          ];

          taskMetricsPayloadsExpected.push({
            taskLogId,
            name: MetricName.VIEW_TRANSFORM_STACK_SIZE,
            value: String(testCase.runtimeDefinition.viewTransformOperations?.length),
            labels: [
              { name: LabelName.SQL, value: "3" },
              { name: LabelName.SQL_SCRIPT, value: "2" },
              { name: LabelName.GRAPHICAL, value: "1" },
            ],
          });

          taskMetricsPayloadsExpected.push({
            taskLogId,
            name: MetricName.RUNTIME,
            value: testCase.runtimeDefinition.runtime!,
          });

          if (testCase.runtimeDefinition.runtime === TransformationFlowRuntime.spark) {
            const logArgs = taskLoggerStub.logMessage.getCall(0).args[0][0];
            expect(logArgs).to.be.an("object");
            expect(logArgs).to.include({
              messageBundleKey: "EXECUTION_SPARK_MAX_RESOURCE_LIMIT",
              text: "The task runs in Apache Spark within the memory limits of 168 vCPU and 672 GiB.",
            });
            taskMetricsPayloadsExpected.push({
              taskLogId,
              name: MetricName.PYTHON_OPERATOR_COUNT,
              value: String(testCase.runtimeDefinition.pythonOperations?.length),
            });
            taskMetricsPayloadsExpected.push({
              taskLogId,
              name: MetricName.INCREMENTAL_AGGREGATION,
              value: testCase.runtimeDefinition.incrementalAggregationEnabled ? "true" : "false",
            });
          }

          expect(taskMetricServiceInsertListStub).to.be.calledOnceWithExactly(context, taskMetricsPayloadsExpected);
        } else {
          if (
            testCase.consumedViewsWithDac ||
            testCase.hasRemoteTable ||
            testCase.runtimeDefinition.runtime === TransformationFlowRuntime.spark
          ) {
            trackSingleResourceConsumpCalls();
            expect(deploySpaceTecTablesStub).not.to.be.called;
            expect(undeploySpaceTecTablesStub).not.to.be.called;
            expect(dbClientStub.execute).not.to.be.called;
          } else if (!testCase.dbClientExecuteError) {
            expect(trackSingleResourceConsumpConnectionStartStub).not.to.be.called;
            expect(deploySpaceTecTablesStub).not.to.be.called;
            expect(undeploySpaceTecTablesStub).not.to.be.called;
            expect(dbClientStub.execute).not.to.be.called;
            expect(trackSingleResourceConsumpConnectionEndStub).not.to.be.called;
          } else {
            deploySpaceTecTablesCalls();
          }
          if (
            testCase.spaceIsLocked ||
            (testCase.activity === Activity.SIMULATE_RUN &&
              testCase.runtimeDefinition.runtime === TransformationFlowRuntime.spark)
          ) {
            expect(mapErrorToMessageAndSubstatusSpy).not.to.be.called;
          } else {
            expect(mapErrorToMessageAndSubstatusSpy).to.be.calledOnceWith(
              context,
              `Unable to run transformation flow.`,
              TransformationFlowTaskLog.EXECUTION_PROCESS_FAILED
            );
          }
          expect(taskLoggerStub.updateExternalInstanceId).not.to.be.called;
          expect(taskMetricServiceInsertListStub).not.to.be.called;
        }

        if (
          (testCase.sqlExpected === undefined && !testCase.dbClientExecuteError) ||
          (testCase.activity === Activity.SIMULATE_RUN &&
            testCase.runtimeDefinition.runtime === TransformationFlowRuntime.spark)
        ) {
          expect(isSpaceVersionEqualOrHigherStub).not.to.be.called;
        } else {
          expect(isSpaceVersionEqualOrHigherStub).to.be.calledOnceWithExactly(context, spaceName, "177", true);
        }
        const runtimeSettings = await new TransformationFlowExecutionRuntimeSettings(context, spaceName).get(
          transformationFlowName
        );

        expect(runtimeSettings.sparkRemoteSource).to.equal("400");
      });
    });
    it("should fail when the space version is lower than expected", async () => {
      const transformationFlowExecutionTask = new TransformationFlowExecutionTask(
        context,
        spaceName,
        transformationFlowName,
        Activity.EXECUTE,
        {}
      );
      isSpaceVersionEqualOrHigherStub.resolves(false);
      isSpaceLockedStub.withArgs(spaceName).resolves(false);
      getRuntimeTransformationFlowDefinitionStub
        .withArgs(context, transformationFlowName, spaceName, dbClientStub)
        .resolves(runtimeDefinitionNotDeltaEnabled);
      retrieveRemoteTablesDependencyStub
        .rejects()
        .withArgs(sinon.match.any, spaceName, [
          { space: spaceName, entity: runtimeDefinitionNotDeltaEnabled.viewTransformName },
        ])
        .resolves([]);

      const result = await transformationFlowExecutionTask.execute(taskLoggerStub);

      expect((result as ExecuteResult).status).to.equal(Status.FAILED);
      expect(mapErrorToMessageAndSubstatusSpy.args[0][3].code).to.equal("spaceVersionTooLow");
      expect(mapErrorToMessageAndSubstatusSpy.args[0][3].message).to.equal(
        `Space version of space ${spaceName} is too low to run transformation flow`
      );
    });
  });

  it("cancelAsyncCall", async () => {
    const asyncCallId = 25;

    await TransformationFlowExecutionTask.cancelAsyncCall(asyncCallId, context, dbClientStub as any);

    expect(dbClientStub.execute).to.be.calledOnceWithExactly(`CANCEL ASYNC CALL ${asyncCallId}`);
  });
});
