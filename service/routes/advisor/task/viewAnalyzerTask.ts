/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { getLogger } from "@sap/dwc-logger";
import { Level } from "../../../advisor/common/types";
import { getAdvisorResult } from "../../../advisor/driver/result";
import { MessageKeys, ViewAnalyzerTaskLogModel } from "../../../advisor/i18n/messagekeys";
import { sortEntities } from "../../../advisor/util";
import { FeatureFlagProvider } from "../../../featureflags/FeatureFlagProvider";
import { AuthType, Activity as PermissionActivity } from "../../../repository/security/common/common";
import { RequestContext } from "../../../repository/security/requestContext";
import { CustomerHanaRuntimeData } from "../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { AuthorizationUtils } from "../../../reuseComponents/utility/AuthorizationUtils";
import { ITaskLogger, Severity, Status, Substatus } from "../../../task/logger/models";
import { Activity } from "../../../task/models";
import { TaskExecuteResponse } from "../../../task/models/TaskExecuteResponse";
import { Parameters } from "../../../task/orchestrator/models/Parameters";
import { LockKey } from "../../../task/orchestrator/services/tasklock";
import {
  retrieveFullCSN,
  retrieveLocalTableInfo,
  retrieveRemoteTableInfo,
  retrieveReplication,
  retrieveViewInfo,
} from "../access";
import { AnalysisScopes, ExecutionStatus, IRouterContext, RequestBodyAttributes } from "../router/types";
import { getContext, getSpaceSchemas, viewAnalyzerLogger } from "../router/util";
import { getAdviceCoordinator } from "./../../../advisor";
import { AnalyzerTask } from "./analyzerTask";
import { taskLogError } from "./viewAnalyzerTaskLogger";

const { logInfo, logError } = getLogger(viewAnalyzerLogger);

/**
 * Implements task for View Analyzer execution
 */
export class ViewAnalyzerTask extends AnalyzerTask {
  protected getLockKey(): LockKey {
    return LockKey.EXECUTE_VIEW_ANALYZER;
  }

  protected async checkAuthorization(taskLogger: ITaskLogger): Promise<boolean> {
    // Check if the task is a cancel task triggered from the system monitor
    if (
      this.parameters?.tf?.cancelId !== undefined &&
      this.activity === Activity.CANCEL_VIEW_ANALYZER &&
      this.requestContext.hasPrivilegeOnType(AuthType.SYSTEMINFO, PermissionActivity.update)
    ) {
      logInfo(`The process to cancel the analyzing of the view ${this.viewName} was triggered from System Monitor.`, {
        context: this.requestContext,
      });
      return true;
    }
    const isAuthorized = await AuthorizationUtils.isAuthorized(
      this.requestContext,
      AuthType.DWC_DATAINTEGRATION,
      PermissionActivity.update,
      this.spaceName
    );
    if (!isAuthorized) {
      await taskLogError(
        taskLogger,
        this.requestContext,
        "Missing authorizations for View Analyzer",
        ViewAnalyzerTaskLogModel.VIEW_ANALYZER_MISSING_AUTH
      );
    }
    return isAuthorized;
  }

  protected async executeTask(): Promise<TaskExecuteResponse> {
    const response: TaskExecuteResponse = { status: Status.COMPLETED };
    let executionStatus: ExecutionStatus;
    await this.logRequestBodyAttributes();
    const routerContext = await this.getRouterContext();
    await this.getRequiredInfo(routerContext);
    const advisor = getAdviceCoordinator(routerContext);
    const result = await advisor.runAdvisor();
    result.entityStats.sort((a, b) => sortEntities(a, b, this.viewName));
    const canceled = await this.viewAnalyzerTaskLogger.isTaskCanceled();
    await this.viewAnalyzerTaskLogger.storeViewAnalyzerResult(this.taskLogId, this.viewName, result, canceled);
    if (canceled) {
      await this.viewAnalyzerTaskLogger.logMessage(
        `View Analyzer execution for view ${this.viewName} was stopped via a cancellation task`,
        ViewAnalyzerTaskLogModel.VIEW_ANALYZER_CANCELED,
        [this.viewName]
      );
      response.status = Status.FAILED;
      response.subStatusCode = Substatus.CANCELLED;
      executionStatus = ExecutionStatus.Error;
    } else if (this.viewAnalyzerTaskLogger.hasErrorMessage()) {
      response.status = Status.FAILED;
      executionStatus = ExecutionStatus.Error;
    } else {
      executionStatus = ExecutionStatus.Success;
    }
    await this.closeExecutionStatus(executionStatus);
    return response;
  }

  /** Check whether space is locked */
  protected async isSpaceLocked(taskLogger: ITaskLogger): Promise<boolean> {
    const customerHanaRuntimeData = new CustomerHanaRuntimeData(this.requestContext);
    const isLocked = await customerHanaRuntimeData.isSpaceLocked(this.spaceName);
    if (isLocked) {
      await taskLogError(
        taskLogger,
        this.requestContext,
        `Cannot execute View Analyzer for view ${this.viewName}, because space  ${this.spaceName} is locked`,
        ViewAnalyzerTaskLogModel.VIEW_ANALYZER_LOCKED_SPACE_ERROR,
        [this.viewName, this.spaceName]
      );
    }
    return isLocked;
  }

  protected async getRequiredInfo(routerContext: IRouterContext) {
    /** Create a temporary background context and finish it at the end, in order to ensure that all database connections
        that are opened within this method will be closed
    */
    const tempBackgroundContext = RequestContext.createNewForBackground(this.requestContext);
    try {
      let promises = [
        getSpaceSchemas(routerContext, tempBackgroundContext),
        retrieveFullCSN(this.viewName, this.spaceName, routerContext, tempBackgroundContext),
      ];
      /* eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled */
      await Promise.all(promises);

      if (await FeatureFlagProvider.isFeatureActive(this.requestContext, "DWCO_USER_PROPAGATION")) {
        promises = [
          retrieveViewInfo(routerContext, tempBackgroundContext, true),
          retrieveRemoteTableInfo(routerContext, tempBackgroundContext, this.viewName),
          retrieveLocalTableInfo(routerContext, tempBackgroundContext),
        ];
      } else {
        promises = [
          retrieveViewInfo(routerContext, tempBackgroundContext, true),
          retrieveReplication(routerContext, tempBackgroundContext),
          retrieveLocalTableInfo(routerContext, tempBackgroundContext),
        ];
      }

      /* eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled */
      await Promise.all(promises);
    } finally {
      await tempBackgroundContext.finish();
    }
  }

  protected async getRouterContext(): Promise<IRouterContext> {
    const routerContext: IRouterContext = getContext(this.viewName, this.spaceName, this.requestContext);
    routerContext.taskLogger = this.viewAnalyzerTaskLogger;

    if (this.parameters[RequestBodyAttributes.WithPlanViz]) {
      routerContext.scope.add(AnalysisScopes.PlanViz);
    }

    routerContext.scope.add(AnalysisScopes.MemoryEvaluator);
    routerContext.scope.add(AnalysisScopes.CrossSpace);
    routerContext.entity = `${this.spaceName}.${this.viewName}`;

    if (this.parameters[RequestBodyAttributes.WithExplainPlan]) {
      routerContext.scope.add(AnalysisScopes.ExplainPlan);
    }

    if (await FeatureFlagProvider.isFeatureActive(this.requestContext, "DWCO_ADVISOR_CROSSSPACE_2")) {
      routerContext.scope.add(AnalysisScopes.CrossSpacePartitionColumn);
    }

    return routerContext;
  }

  protected async logRequestBodyAttributes(): Promise<void> {
    const requestParameters: Parameters = {};
    const requestAttributesValues: string[] = Object.values(RequestBodyAttributes);
    Object.keys(this.parameters)
      .filter((item) => requestAttributesValues.includes(item))
      .forEach((item) => (requestParameters[item] = this.parameters[item]));
    const details = JSON.stringify(requestParameters);
    await this.viewAnalyzerTaskLogger.logMessage(
      `Starting View Analyzer execution.`,
      ViewAnalyzerTaskLogModel.VIEW_ANALYZER_EXECUTION_STARTED,
      [],
      Severity.INFO,
      details
    );
    logInfo(
      `[ViewAnalyzerTask] Task is started for: "${this.viewName}" with task: ${
        this.taskLogId
      }.\n Task parameters: ${JSON.stringify(this.parameters)}`,
      {
        context: this.requestContext,
      }
    );
  }

  protected async logErrorMessage(taskLogger: ITaskLogger, error: any): Promise<void> {
    const msgText = "View Analyzer execution failed";
    logError(`[ViewAnalyzerTask] ${msgText} with error: ${error.message}\n${error.stack}`, {
      context: this.requestContext,
    });
    await taskLogError(
      taskLogger,
      this.requestContext,
      msgText,
      ViewAnalyzerTaskLogModel.VIEW_ANALYZER_EXECUTION_FAILED,
      [],
      error
    );
    if (!this.viewAnalyzerTaskLogger.isViewAnalyzerResultStored()) {
      await this.storeViewAnalyzerGlobalError();
    }
  }

  /** Store a global error message in case of a severe failure */
  private async storeViewAnalyzerGlobalError(): Promise<void> {
    const result = getAdvisorResult(false, true);
    result.globalPayload = [
      {
        level: Level.Error,
        messageKey: MessageKeys.PG_NO_RESULT,
        params: [],
      },
    ];
    await this.viewAnalyzerTaskLogger.storeViewAnalyzerResult(this.taskLogId, this.viewName, result, false);
  }
}
