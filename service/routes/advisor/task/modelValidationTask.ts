/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { FeatureFlagProvider } from "@sap/dwc-context-checks";
import { getLogger } from "@sap/dwc-logger";
import { ViewAnalyzerTaskLogModel } from "../../../advisor/i18n/messagekeys";
import { IFeatureFlagsMap } from "../../../featureflags/FeatureFlagProvider";
import { AuthType, Activity as PermissionActivity } from "../../../repository/security/common/common";
import { RequestContext } from "../../../repository/security/requestContext";
import { CustomerHanaRuntimeData } from "../../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { AuthorizationUtils } from "../../../reuseComponents/utility/AuthorizationUtils";
import { ITaskLogger, Status, Substatus } from "../../../task/logger/models";
import { Activity } from "../../../task/models/Activity";
import { TaskExecuteResponse } from "../../../task/models/TaskExecuteResponse";
import { LockKey } from "../../../task/orchestrator/services/tasklock";
import {
  retrieveCsnWithResolvedTypes,
  retrieveDataAccessControlDependency,
  retrieveVirtualRemoteTableAdapters,
  retrieveVirtualRemoteTablesDependency,
} from "../access";
import { retrieveDataAccessControlDependencyPerEntity } from "../access/eligible";
import { retrieveVirtualRemoteTableAdaptersPerEntity } from "../access/objectDependency";
import { retrieveValidationCsn } from "../access/repository";
import { AnalysisScopes, ExecutionStatus, IRouterContext } from "../router/types";
import { getContext, getSpaceSchemas, viewAnalyzerLogger } from "../router/util";
import { getAdviceCoordinator } from "./../../../advisor";
import { AnalyzerTask } from "./analyzerTask";
import { taskLogError } from "./viewAnalyzerTaskLogger";

const { logInfo, logError } = getLogger(viewAnalyzerLogger);

/**
 * Implements task for Model Validation
 */
export class ModelValidationTask extends AnalyzerTask {
  protected getLockKey(): LockKey {
    return LockKey.VALIDATE;
  }

  protected async checkAuthorization(): Promise<boolean> {
    // Check if the task is a cancel task triggered from the system monitor
    if (
      this.parameters?.tf?.cancelId !== undefined &&
      this.activity === Activity.CANCEL_VALIDATE &&
      this.requestContext.hasPrivilegeOnType(AuthType.SYSTEMINFO, PermissionActivity.update)
    ) {
      logInfo(
        `The process to cancel the model validation of the view ${this.viewName} was triggered from System Monitor.`,
        {
          context: this.requestContext,
        }
      );
      return true;
    }
    return await AuthorizationUtils.isAuthorized(
      this.requestContext,
      AuthType.DWC_DATABUILDER,
      PermissionActivity.update,
      this.spaceName
    );
  }

  protected async executeTask(): Promise<TaskExecuteResponse> {
    const response: TaskExecuteResponse = { status: Status.COMPLETED };
    let executionStatus: ExecutionStatus;
    const routerContext = await this.getRouterContext();
    await this.getRequiredInfo(routerContext);
    const advisor = getAdviceCoordinator(routerContext);
    const result = await advisor.runAdvisor();
    const canceled = await this.viewAnalyzerTaskLogger.isTaskCanceled();
    await this.viewAnalyzerTaskLogger.storeViewAnalyzerResult(this.taskLogId, this.viewName, result, canceled);
    if (canceled) {
      await this.viewAnalyzerTaskLogger.logMessage(
        `Model Validation execution for view ${this.viewName} was stopped via a cancellation task`,
        ViewAnalyzerTaskLogModel.MODEL_VALIDATION_CANCELED,
        [this.viewName]
      );
      response.status = Status.FAILED;
      response.subStatusCode = Substatus.CANCELLED;
      executionStatus = ExecutionStatus.Error;
    } else {
      executionStatus = ExecutionStatus.Success;
    }
    await this.closeExecutionStatus(executionStatus);
    return response;
  }

  /** Check whether space is locked */
  protected async isSpaceLocked(taskLogger: ITaskLogger): Promise<boolean> {
    const customerHanaRuntimeData = new CustomerHanaRuntimeData(this.requestContext);
    const isLocked = await customerHanaRuntimeData.isSpaceLocked(this.spaceName);
    if (isLocked) {
      await taskLogError(
        taskLogger,
        this.requestContext,
        `Cannot execute Model Validation for view ${this.viewName}, because space ${this.spaceName} is locked`,
        ViewAnalyzerTaskLogModel.MODEL_VALIDATION_LOCKED_SPACE_ERROR,
        [this.viewName, this.spaceName]
      );
    }
    return isLocked;
  }

  protected async getRequiredInfo(routerContext: IRouterContext) {
    /** Create a temporary background context and finish it at the end, in order to ensure that all database connections
        that are opened within this method will be closed
    */
    const tempBackgroundContext = RequestContext.createNewForBackground(this.requestContext);
    try {
      await getSpaceSchemas(routerContext, tempBackgroundContext);

      if (routerContext.scope.has(AnalysisScopes.ModelValidationForeignKeys)) {
        const entityInfos = await retrieveValidationCsn(routerContext, tempBackgroundContext);

        /* eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled */
        await Promise.all([
          retrieveDataAccessControlDependencyPerEntity(routerContext, tempBackgroundContext, entityInfos),
          retrieveVirtualRemoteTableAdaptersPerEntity(routerContext, tempBackgroundContext, entityInfos),
        ]);
      } else {
        const promises = [retrieveDataAccessControlDependency(routerContext, tempBackgroundContext)];

        if (routerContext.scope.has(AnalysisScopes.ModelValidationRemoteTables)) {
          promises.push(retrieveVirtualRemoteTableAdapters(routerContext, tempBackgroundContext));
        } else {
          promises.push(retrieveVirtualRemoteTablesDependency(routerContext, tempBackgroundContext));
        }

        promises.push(retrieveCsnWithResolvedTypes(routerContext, tempBackgroundContext));

        /* eslint-disable-next-line @sap/dwc-lints/unbound-promises-handled */
        await Promise.all(promises);
      }
    } finally {
      await tempBackgroundContext.finish();
    }
  }

  protected async getRouterContext(): Promise<IRouterContext> {
    const routerContext: IRouterContext = getContext(this.viewName, this.spaceName, this.requestContext);
    routerContext.taskLogger = this.viewAnalyzerTaskLogger;
    routerContext.scope.add(AnalysisScopes.ModelValidation);

    await this.getFeatureFlagBasedScopes(routerContext);
    return routerContext;
  }

  private async getFeatureFlagBasedScopes(routerContext: IRouterContext) {
    const featureFlagsToScopeMapping: Array<[feature: keyof IFeatureFlagsMap, AnalysisScopes]> = [
      ["DWCO_MODELING_VALIDATION_REFERENTIAL_INTEGRITY", AnalysisScopes.ModelValidationForeignKeys],
      ["DWCO_MODELING_VALIDATION_ENABLE_REMOTE_TABLES", AnalysisScopes.ModelValidationRemoteTables],
      ["DWCO_MODELING_VALIDATION_UNASSIGNED_NODES", AnalysisScopes.ModelValidationUnassignedNodes],
    ];

    for (const [flag, scope] of featureFlagsToScopeMapping) {
      if (await FeatureFlagProvider.isFeatureActive(this.requestContext, flag)) {
        routerContext.scope.add(scope);
      }
    }
  }

  protected async logErrorMessage(taskLogger: ITaskLogger, error: any): Promise<void> {
    const msgText = "Model Validation execution failed";
    logError(`[ModelValidationTask] ${msgText} with error: ${error.message}\n${error.stack}`, {
      context: this.requestContext,
    });
    await taskLogError(
      taskLogger,
      this.requestContext,
      msgText,
      ViewAnalyzerTaskLogModel.CANCEL_MODEL_VALIDATION_FAILED,
      [],
      error
    );
  }
}
