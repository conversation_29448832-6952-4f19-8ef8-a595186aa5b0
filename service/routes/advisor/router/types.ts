/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { ObjectStatus } from "../../../repository/types/objectStatus";
import { Status } from "../../../task/logger/models";
import { LocalTableInfoProxy, ReplicationInfoProxy, RepositoryProxy, ViewInfoProxy } from "../access";
import { ViewAnalyzerTaskLogger } from "../task/viewAnalyzerTaskLogger";

export type IErrorHandler = (err: Error) => boolean;

export enum Eligibility {
  undefined,
  inconclusive,
  true,
  false,
}

export interface IRouterContext {
  /** entity name */
  entity?: string;

  /** space names */
  space: string;

  /** name of DB schema */
  spaceSchema: string;

  /** name of technical DB schema */
  technicalSchema: string;

  /** extend of analysis: make expensive evaluators selectable */
  scope: Set<AnalysisScopes>;

  /** CSN definition of view to analyze */
  repository: RepositoryProxy;

  /** view persistency information for current space */
  viewInfo: ViewInfoProxy;

  /** remote table replication status  */
  remoteTableInfo: ReplicationInfoProxy;

  /** local table info  */
  localTableInfo: LocalTableInfoProxy;

  errorHandler?: IErrorHandler[];

  /** Task Logger  */
  taskLogger?: ViewAnalyzerTaskLogger;

  /** candidates for partitioning column as calculated by Partitioner */
  candidates?: string[];

  /** does view meet prerequisites for view persistency ? */
  isEligible: Eligibility;

  /** does view contain directly or indirectly DAC cross-space ? */
  hasDAC?: boolean;
  dacPerEntity?: Array<{ hasDAC: boolean } & ISpaceEntity>;

  /** does view contain directly or indirectly virtual remote table cross-space ? */
  hasRemoteTable?: boolean;

  /** remote adapters belonging to virtual remote tables used directly or indirectly by the view */
  remoteAdapters?: string[];
  remoteAdaptersPerEntity?: Array<{ adapter: string[] } & ISpaceEntity>;

  /**
   * @param message Message to be logged
   */
  logInfo(message: any): void;

  /**
   * @param message Message to be logged
   */
  logDebug(message: any): void;

  /**
   * @param errorDetails Error or error message to be logged
   */
  logError(errorDetails: string): void;

  /**
   * @param startTime Starting time to calculate the performance
   * @param message Message to be logged
   */
  logPerformance(startTime: Date, message: any): Date;
}

export interface IEntityDependency {
  id: string;
  name?: string;
  "#objectStatus": ObjectStatus;
  "#shared": string;
  "#spaceName": string;
  "#technicalType": string;
  successors: string[];
}

export interface IEntitiesWithDependencies {
  [name: string]: IEntityDependency;
}

export interface IParameters {
  withMemoryAnalysis?: boolean;
  maximumMemoryConsumptionInGiB?: number;
  viewsWithMemoryAnalysis?: string[] | ISpaceEntity[];
  withPlanViz?: boolean;
  viewWithPlanViz?: string | ISpaceEntity;
  withExplainPlan?: boolean;
  viewWithExplainPlan?: string | ISpaceEntity;
  explainPlan?: boolean;
}

export enum CsnRepositoryTag {
  Id = "id",
  ObjectStatus = "#objectStatus",
  DeploymentExecutionStatus = "#deploymentExecutionStatus",
  DeploymentState = "deploymentState",
  Shared = "#shared",
  SpaceName = "#spaceName",
  TechnicalType = "#technicalType",
  DeployedCsn = "#deployedCsn",
  DeployedCsnForConsumption = "#deployedCsnForConsumption",
}

export enum AnalysisScopes {
  // values representing use cases and user selectable options
  // number range 1 .. 19

  MemoryEvaluator = 1,
  PartitionColumn = 2,
  PlanViz = 4,
  Preview = 5,
  ExplainPlan = 6,

  // values representing feature flags
  // number range 20 .. 39

  CrossSpace = 20, //                DS00-957
  CrossSpacePartitionColumn = 21, // DS00-963
  PerformanceAnalysis = 22, //       DS00-4366

  // values for model validation (coast guard)
  // number range 40 .. 59

  ModelValidation = 40, // use case value

  ModelValidationRemoteTables = 41,
  ModelValidationForeignKeys = 42,
  ModelValidationUnassignedNodes = 43,
}

export enum ExecutionStatus {
  Initial = "INITIAL",
  Success = "SUCCESS",
  Error = "ERROR",
}

export enum RequestBodyAttributes {
  WithMemoryAnalysis = "withMemoryAnalysis",
  MaximumMemoryConsumptionInGiB = "maximumMemoryConsumptionInGiB",
  ViewsWithMemoryAnalysis = "viewsWithMemoryAnalysis",
  TaskLogIdToCancel = "taskLogIdToCancel",
  WithPlanViz = "withPlanViz",
  ViewWithPlanViz = "viewWithPlanViz",
  WithExplainPlan = "withExplainPlan",
  ViewWithExplainPlan = "viewWithExplainPlan",
  ExplainPlan = "explainPlan",
}

export enum RequestQueryParameters {
  TaskLogId = "taskLogId",
}

export interface IPersistencyRuntimeMeasures {
  numberRecords: string;
  peakMemory: string;
  persistencyRuntime: string;
}

export interface ISpaceEntity {
  space: string;
  entity: string;
}

export interface ISpaceEntityRemoteTableAdapter {
  space: string;
  entity: string;
  adapterName: string;
}

export interface IRemoteTablesLineageProperties {
  hasRemoteTable: boolean;
  hasRemoteTableOtherSpaces: boolean;
  spaceRemoteTables: string[];
  otherSpacesRemoteTables: ISpaceEntity[];
}

export interface ILineageProperties extends IRemoteTablesLineageProperties {
  hasDAC: boolean;
}

export interface IViewWithTaskInfo {
  VIEW_NAME: string;
  SPACE_NAME: string;
  STATUS: Status;
  TASK_LOG_ID: number;
}

export interface IEntityTable {
  space: string;
  entity: string;
  globalName: string;
  id: string;
  technicalType: string;
  sharedTo: string[];
}

export interface ISharing {
  space: string;
  entity: string;
  sharedToSpaces: string[];
}
