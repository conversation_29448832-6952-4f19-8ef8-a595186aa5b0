/** @format */

import { ISacListUser } from "@sap/deepsea-types";
export { IObjectParameter } from "@sap/deepsea-types";
export interface ISacListUserMetadata {
  userName: string;
  userId: string;
  createTime: string;
  deactivated: false;
}
export interface ISacListUserExtended extends ISacListUser {
  isUserConcurrent?: boolean;
}

export interface IJouleUser {
  userName: string;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  email?: string;
  roles?: string[];
  manager?: string;
  isUserDeactivated?: boolean;
  createdOn?: string;
  licenseType?: string;
  isSystemOwner?: boolean;
  isConcurrent?: boolean;
  isSupportUser?: boolean;
}

export enum OrderDirection {
  Ascending = "asc",
  Descending = "desc",
}

export interface IJouleRole {
  ranking: number;
  id: string;
  name: string;
  profileId: string;
  description?: string;
  isScoped: boolean;
  isPredefined: boolean;
  userIds: string[];
  countUsers: number;
  countScopes: number;
}

export enum RoleType {
  GLOBAL = 0,
  SCOPED = 1,
}

export interface IEshRole {
  "@com.sap.vocabularies.Search.v1.Ranking": number;
  CHANGED_AT: string;
  CHANGED_BY: string;
  CREATED_AT: string;
  DESCRIPTION: string;
  LICENSE_TYPE: string;
  NAME: string;
  OWNER: string;
  PREDEFINED_ROLE: "Standard Role" | "Custom Role";
  PROFILE_ID: string;
  SCOPE_ROLE: "Global Role" | "Scoped Role";
  SCOPE_NAME: string[];
  TEAM_NAME: string[];
  USER_ID: string[];
  NUMBER_OF_USERS: number;
  NUMBER_OF_SCOPES: number;
  NUMBER_OF_TEAMS: number;
}

export interface IJouleRoleResponse {
  count: number;
  countGlobalRoles: number;
  countScopedRoles: number;
  roles: IJouleRole[];
}
