/** @format */

import { CodedError, sendErrorResponse } from "@sap/dwc-express-utils";
import { HttpMethod } from "@sap/dwc-http-client/dist/types";
import * as Logger from "@sap/dwc-logger";
import { SacClient } from "@sap/dwc-sac-client";
import {
  Comparison,
  DateTimeValue,
  Expression,
  IESSearchOptions,
  IToStatement,
  InList,
  InListOperator,
  Phrase,
  SearchQueryComparisonOperator,
  SearchQueryLogicalOperator,
  Term,
  escapeQuery,
  getEshSearchQuery,
} from "@sap/enterprise-search-objects";
import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { ParsedQs } from "qs";
import { RequestContext } from "../../repository/security/requestContext";
import { predefinedRoleNames } from "./predefinedRoleNames";
import { IEshRole, IJouleRoleResponse } from "./types";

const { logError, logInfo } = Logger.getLogger("Joule/Roles");
const hanaGateway = SacClient.getSacUrl("hana-gateway");
const eshUrl = "/sap/fpa/services/rest/security/enterprise-search";

export async function getAllRolesRoute(req: Request, res: Response<IJouleRoleResponse>) {
  const { context, query } = req;
  try {
    // check if search name is a translated value
    const enhancedQuery = getPredefinedRoleIds(query);

    const eshQueryString = buildEshQueryString(enhancedQuery);

    res.send(await getRolesFromESH(context, eshQueryString));
  } catch (err) {
    logError([`Getting Roles for Joule failed,`, err], { context });
    sendErrorResponse(context, "getRolesForJouleFailed", { err });
  }
}

function getPredefinedRoleIds(query: ParsedQs): ParsedQs {
  const filterKeysPredefined = ["name", "contains", "startsWith", "endsWith", "roleType"];
  const queryKeys = Object.keys(query).filter((key) => filterKeysPredefined.includes(key));
  if (queryKeys.length > 0) {
    const filteredRoleIds = predefinedRoleNames
      .filter((role) =>
        queryKeys.every((key) => {
          const queryValue = query[key] as string;
          switch (key) {
            case "name":
              return role.name_en === queryValue;
            case "contains":
              return role.name_en.toLowerCase().includes(queryValue.toLowerCase());
            case "startsWith":
              return role.name_en.toLowerCase().startsWith(queryValue.toLowerCase());
            case "endsWith":
              return role.name_en.toLowerCase().endsWith(queryValue.toLowerCase());
            case "roleType":
              return role.roleType === parseInt(queryValue, 10);
          }
        })
      )
      .map((role) => role.id);

    if (filteredRoleIds.length > 0) {
      const id = query.id ? `${query.id as string},` : "";
      query.id = id + filteredRoleIds.join(",");
    }
  }
  return query;
}

// allowed query parameters
const filterKeys = [
  "id",
  "profileId",
  "name",
  "startsWith",
  "endsWith",
  "contains",
  "roleType",
  "description",
  "userId",
  "spaceId",
  "createdFrom",
  "createdTo",
] as const;

type AllowedQueryKeys = typeof filterKeys[number];

function buildEshQueryString(queryParams: ParsedQs): string {
  // filter out valid query params
  // id_filter => id
  // name_filter => name, contains, startsWith, endsWith
  // other_filter => spaceId, userId, description, profileId, createdFrom, createdTo
  // build query => (OR(ids) or AND(name)) AND (AND(other))

  const queryKeys = Object.keys(queryParams).filter((key) => filterKeys.includes(key as AllowedQueryKeys));
  const { id, limit } = queryParams;
  const nameFilterItems: IToStatement[] = [];
  const otherFilterItems: IToStatement[] = [];

  const eshSearchOptions: IESSearchOptions = {
    facets: ["SCOPE_ROLE"],
    scope: "esh_ums_profiles",
  };

  if (limit) {
    eshSearchOptions.$top = parseInt(limit as string, 10);
  }

  if (queryKeys.length > 0) {
    // build query (ID OR NAME_FILTERS) AND (OTHER_FILTERS)
    queryKeys.forEach((key) => {
      const value = queryParams[key] as string;
      switch (key) {
        case "profileId":
          otherFilterItems.push(
            new Comparison({
              property: "PROFILE_ID",
              operator: SearchQueryComparisonOperator.EqualCaseSensitive,
              value: new Term({ term: value }),
            })
          );
          break;
        case "name":
          nameFilterItems.push(
            new Comparison({
              property: "NAME",
              operator: SearchQueryComparisonOperator.EqualCaseInsensitive,
              value: new Phrase({ phrase: value }), // phrase supports spaces
            })
          );
          break;
        case "contains":
          nameFilterItems.push(
            new Comparison({
              property: "NAME",
              operator: SearchQueryComparisonOperator.Search,
              value: new Phrase({ phrase: value, searchOptions: { fuzzinessThreshold: 0.7 } }),
            })
          );
          break;
        case "startsWith":
          nameFilterItems.push(
            new Comparison({
              property: "NAME",
              operator: SearchQueryComparisonOperator.EqualCaseInsensitive, // only EQ can accept wildcards
              value: new Phrase({ phrase: escapeQuery(value) + "*", doEshEscaping: false }), // escape only query value not the wildcard
            })
          );
          break;
        case "endsWith":
          nameFilterItems.push(
            new Comparison({
              property: "NAME",
              operator: SearchQueryComparisonOperator.EqualCaseInsensitive,
              value: new Phrase({ phrase: "*" + escapeQuery(value), doEshEscaping: false }),
            })
          );
          break;
        case "roleType":
          otherFilterItems.push(
            new Comparison({
              property: "SCOPE_ROLE",
              operator: SearchQueryComparisonOperator.EqualCaseSensitive,
              value,
            })
          );
          break;
        case "description":
          otherFilterItems.push(
            new Comparison({
              property: "DESCRIPTION",
              operator: SearchQueryComparisonOperator.Search,
              value: new Phrase({ phrase: value }),
            })
          );
          break;
        case "userId":
          otherFilterItems.push(
            new Comparison({
              property: "USER_ID",
              operator: SearchQueryComparisonOperator.Search,
              value: new InList({
                operator: InListOperator.AND,
                values: value.split(","),
              }),
            })
          );
          break;
        case "spaceId":
          otherFilterItems.push(
            new Comparison({
              property: "SCOPE_NAME",
              operator: SearchQueryComparisonOperator.Search,
              value: new InList({
                operator: InListOperator.AND,
                values: value.split(","),
              }),
            })
          );
          break;
        case "createdFrom":
          otherFilterItems.push(
            new Comparison({
              property: "CREATED_AT",
              operator: SearchQueryComparisonOperator.GreaterThanOrEqualCaseInsensitive,
              value: new DateTimeValue({ value }),
            })
          );
          break;
        case "createdTo":
          otherFilterItems.push(
            new Comparison({
              property: "CREATED_AT",
              operator: SearchQueryComparisonOperator.LessThanOrEqualCaseInsensitive,
              value: new DateTimeValue({ value }),
            })
          );
          break;
        default:
          return;
      }
    });

    const eshOtherFilters = new Expression({
      operator: SearchQueryLogicalOperator.AND,
      items: otherFilterItems,
    });

    const eshNameFilters = new Expression({
      operator: SearchQueryLogicalOperator.AND,
      items: nameFilterItems,
    });

    if (id) {
      const eshIdFilter = new Comparison({
        property: "PROFILE_ID",
        operator: SearchQueryComparisonOperator.Search,
        value: new InList({
          operator: InListOperator.OR,
          values: (id as string).split(",").map((val) => "*" + escapeQuery(":" + val)),
        }),
      });

      // (id OR name) AND others
      eshSearchOptions.searchQueryFilter = new Expression({
        operator: SearchQueryLogicalOperator.AND,
        items: [
          new Expression({
            operator: SearchQueryLogicalOperator.OR,
            items: [eshIdFilter, eshNameFilters],
          }),
          eshOtherFilters,
        ],
      });
    } else {
      // (name AND others)
      eshSearchOptions.searchQueryFilter = new Expression({
        operator: SearchQueryLogicalOperator.AND,
        items: [eshNameFilters, eshOtherFilters],
      });
    }
  }

  const queryString = getEshSearchQuery(eshSearchOptions);
  return queryString;
}

interface IEshResponse {
  value: IEshRole[];
  error?: Record<string, any>;
}
async function getRolesFromESH(context: RequestContext, queryString: string): Promise<IJouleRoleResponse> {
  const caller = getRolesFromESH.name;
  const url = `${hanaGateway}${eshUrl}${queryString}`;
  logInfo(`getRolesFromESH: Retrieving Roles from Enterprise Search Url: ${url}`, { context });
  try {
    const responseEsh = (await SacClient.call({
      url,
      opts: {
        caller,
        context,
        method: HttpMethod.GET,
      },
    })) as IEshResponse;

    if (responseEsh.hasOwnProperty("error")) {
      throw new CodedError(
        "enterpriseSearchFailed",
        `Enterprise Search Url: ${url}`,
        StatusCodes.BAD_REQUEST,
        responseEsh.error
      );
    }

    const eshRoles = responseEsh.hasOwnProperty("value")
      ? responseEsh.value.map((role: IEshRole) => {
          const roleId = role.PROFILE_ID.split(":").pop() ?? role.PROFILE_ID;
          const roleName = predefinedRoleNames.find((r) => r.id === roleId)?.name_en ?? role.NAME;

          return {
            ranking: role["@com.sap.vocabularies.Search.v1.Ranking"],
            id: roleId,
            name: roleName,
            profileId: role.PROFILE_ID,
            description: role.DESCRIPTION,
            createdOn: role.CREATED_AT,
            isScoped: role.SCOPE_ROLE === "Scoped Role" ? true : false,
            isPredefined: role.PREDEFINED_ROLE === "Standard Role" ? true : false,
            scopeIds: role.SCOPE_NAME,
            userIds: role.USER_ID,
            countUsers: role.NUMBER_OF_USERS,
            countScopes: role.NUMBER_OF_SCOPES,
          };
        })
      : [];

    return {
      count: eshRoles.length,
      countGlobalRoles: eshRoles.filter((role) => role.isScoped === false).length,
      countScopedRoles: eshRoles.filter((role) => role.isScoped === true).length,
      roles: eshRoles.sort((a, b) => a.name.localeCompare(b.name)),
    };
  } catch (err) {
    logError([`Getting Roles from ESH failed,`, err], { context });
    throw err;
  }
}
