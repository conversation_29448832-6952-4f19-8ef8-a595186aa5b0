/** @format */

import type { SqlTaggedTemplate } from "@sap/prom-hana-client";
import { sql } from "@sap/prom-hana-client";
import { Request, Response } from "express";
import { getSpaces } from "../../repository/spaces";
import { <PERSON>er<PERSON><PERSON> } from "../../reuseComponents/spaces/src";
import { sendErrorResponse } from "../../server/errorResponse";
import { parseBoolean } from "../support/utils";

const isFiniteNumber = (n: number) => !isNaN(n) && isFinite(n);

/**
 * Constructs a SQL WHERE clause based on the provided filtering criteria.
 *
 * @format
 * @param params - An object containing the filtering criteria.
 * @param params.spaceIds - An array of space IDs to filter by.
 * @param params.dppRead - A boolean indicating whether to filter by read policy. If `null`, includes spaces with either read or change policies.
 * @param params.dppChange - A boolean indicating whether to filter by change policy. If `null`, includes spaces with either read or change policies.
 * @param params.retentionPeriod - A specific retention period to filter by.
 * @param params.retentionPeriodGt - A minimum retention period to filter by (greater than).
 * @param params.retentionPeriodLt - A maximum retention period to filter by (less than).
 * @returns A SQL WHERE clause as a template literal, incorporating the specified filters.
 */
function buildWhereClause({
  spaceIds,
  dppRead,
  dppChange,
  retentionPeriod,
  retentionPeriodGt,
  retentionPeriodLt,
}: {
  spaceIds: string[];
  dppRead: boolean | null;
  dppChange: boolean | null;
  retentionPeriod: number;
  retentionPeriodGt: number;
  retentionPeriodLt: number;
}) {
  let WHERE = sql`WHERE "SPACE_ID" IN (${sql.join(spaceIds, sql`, `)})`;
  const retentionValue = getRetentionValue({ retentionPeriod, retentionPeriodGt, retentionPeriodLt });
  const retentionPeriodOperator = getRetentionOperator({ retentionPeriod, retentionPeriodGt, retentionPeriodLt });

  if (dppRead === null && dppChange === null) {
    WHERE = sql`${WHERE} AND ("HAS_READ_POLICY" = 'true' OR "HAS_CHANGE_POLICY" = 'true')`;
    if (retentionValue) {
      WHERE = sql`${WHERE} AND ("RETENTION_PERIOD_READ" ${retentionPeriodOperator} ${retentionValue} OR "RETENTION_PERIOD_CHANGE" ${retentionPeriodOperator} ${retentionValue})`;
    }
  } else if ((dppRead === true || dppRead === false) && dppChange === null) {
    WHERE = sql`${WHERE} AND "HAS_READ_POLICY" = ${!!dppRead}`;
    if (retentionValue) {
      WHERE = sql`${WHERE} AND "RETENTION_PERIOD_READ" ${retentionPeriodOperator} ${retentionValue}`;
    }
  } else if ((dppChange === true || dppChange === false) && dppRead === null) {
    WHERE = sql`${WHERE} AND "HAS_CHANGE_POLICY" = ${!!dppChange}`;
    if (retentionValue) {
      WHERE = sql`${WHERE} AND "RETENTION_PERIOD_CHANGE" ${retentionPeriodOperator} ${retentionValue}`;
    }
  } else {
    WHERE = sql`${WHERE} AND "HAS_READ_POLICY" = ${!!dppRead} AND "HAS_CHANGE_POLICY" = ${!!dppChange}`;
    if (retentionValue) {
      WHERE = appendRetentionFilters({
        WHERE,
        dppRead,
        dppChange,
        retentionPeriodOperator,
        retentionValue,
      });
    }
  }

  return WHERE;
}

/**
 * Determines the retention value based on the provided parameters.
 *
 * The function evaluates the `retentionPeriod`, `retentionPeriodGt`, and `retentionPeriodLt`
 * in order of priority and returns the first valid (non-NaN) value. If none of the values
 * are valid, it returns `null`.
 *
 * @param params - An object containing the retention period parameters.
 * @param params.retentionPeriod - The primary retention period value.
 * @param params.retentionPeriodGt - The greater-than retention period value, used if `retentionPeriod` is invalid.
 * @param params.retentionPeriodLt - The less-than retention period value, used if both `retentionPeriod` and `retentionPeriodGt` are invalid.
 *
 * @returns The first valid retention period value, or `null` if all values are invalid.
 */
function getRetentionValue({
  retentionPeriod,
  retentionPeriodGt,
  retentionPeriodLt,
}: {
  retentionPeriod: number;
  retentionPeriodGt: number;
  retentionPeriodLt: number;
}) {
  return isFiniteNumber(retentionPeriod)
    ? retentionPeriod
    : isFiniteNumber(retentionPeriodGt)
    ? retentionPeriodGt
    : isFiniteNumber(retentionPeriodLt)
    ? retentionPeriodLt
    : null;
}

/**
 * Determines the SQL operator to use based on the provided retention period parameters.
 *
 * @param params - An object containing the retention period parameters.
 * @param params.retentionPeriod - The exact retention period value. If this is a valid number, the operator will be `=`.
 * @param params.retentionPeriodGt - The retention period greater-than value. If this is a valid number and `retentionPeriod` is not, the operator will be `>`.
 * @param params.retentionPeriodLt - The retention period less-than value. If this is a valid number and neither `retentionPeriod` nor `retentionPeriodGt` are valid, the operator will be `<`.
 * @returns The SQL operator (`=`, `>`, `<`) as a template literal, or `null` if no valid retention period parameter is provided.
 */
function getRetentionOperator({
  retentionPeriod,
  retentionPeriodGt,
  retentionPeriodLt,
}: {
  retentionPeriod: number;
  retentionPeriodGt: number;
  retentionPeriodLt: number;
}) {
  return isFiniteNumber(retentionPeriod)
    ? sql`=`
    : isFiniteNumber(retentionPeriodGt)
    ? sql`>`
    : isFiniteNumber(retentionPeriodLt)
    ? sql`<`
    : null;
}

/**
 * Appends retention filters to the provided SQL WHERE clause based on the specified conditions.
 *
 * @param {object} params - The parameters for appending retention filters.
 * @param {SqlTaggedTemplate} params.WHERE - The initial SQL WHERE clause to which the filters will be appended.
 * @param {boolean | null} params.dppRead - Indicates whether the "RETENTION_PERIOD_READ" filter should be applied.
 * @param {boolean | null} params.dppChange - Indicates whether the "RETENTION_PERIOD_CHANGE" filter should be applied.
 * @param {any} params.retentionPeriodOperator - The SQL operator to use for the retention period comparison (e.g., '=', '<', '>').
 * @param {number} params.retentionValue - The retention period value to compare against.
 * @returns {SqlTaggedTemplate} - The updated SQL WHERE clause with the appended retention filters.
 */
function appendRetentionFilters({
  WHERE,
  dppRead,
  dppChange,
  retentionPeriodOperator,
  retentionValue,
}: {
  WHERE: SqlTaggedTemplate;
  dppRead: boolean | null;
  dppChange: boolean | null;
  retentionPeriodOperator: any;
  retentionValue: number;
}) {
  if (dppRead && dppChange) {
    WHERE = sql`${WHERE} AND ("RETENTION_PERIOD_READ" ${retentionPeriodOperator} ${retentionValue} AND "RETENTION_PERIOD_CHANGE" ${retentionPeriodOperator} ${retentionValue})`;
  } else if (dppRead && !dppChange) {
    WHERE = sql`${WHERE} AND "RETENTION_PERIOD_READ" ${retentionPeriodOperator} ${retentionValue}`;
  } else if (!dppRead && dppChange) {
    WHERE = sql`${WHERE} AND "RETENTION_PERIOD_CHANGE" ${retentionPeriodOperator} ${retentionValue}`;
  }
  return WHERE;
}

/**
 * Builds an SQL query for retrieving data from the "JOULE_SPACE_AUDIT_LOGS" table.
 *
 * @param params - An object containing the parameters for building the SQL query.
 * @param params.WHERE - The SQL WHERE clause to filter the query results.
 * @param params.dppRead - A boolean indicating whether to include "RETENTION_PERIOD_READ" in the ORDER BY clause.
 * @param params.dppChange - A boolean indicating whether to include "RETENTION_PERIOD_CHANGE" in the ORDER BY clause.
 * @param params.order - The sorting order (e.g., "ASC" or "DESC") to be applied in the ORDER BY clause.
 * @returns The constructed SQL query as a template literal.
 */
function buildSqlQuery({
  WHERE,
  dppRead,
  dppChange,
  order,
}: {
  WHERE: SqlTaggedTemplate;
  dppRead: boolean | null;
  dppChange: boolean | null;
  order: string;
}) {
  let sqlQuery = sql`
    SELECT
      *
    FROM
      "DWC_TENANT_OWNER"."JOULE_SPACE_AUDIT_LOGS"
    ${WHERE}`;

  if ((dppRead === null && dppChange === null) || (dppRead && dppChange)) {
    sqlQuery = sql`${sqlQuery} ORDER BY "RETENTION_PERIOD_READ" ${sql.unquoted(
      order
    )}, "RETENTION_PERIOD_CHANGE" ${sql.unquoted(order)}`;
  } else if (dppRead && !dppChange) {
    sqlQuery = sql`${sqlQuery} ORDER BY "RETENTION_PERIOD_READ" ${sql.unquoted(
      order
    )}, "RETENTION_PERIOD_CHANGE" ${sql.unquoted(order)}`;
  } else if (!dppRead && dppChange) {
    sqlQuery = sql`${sqlQuery} ORDER BY "RETENTION_PERIOD_CHANGE" ${sql.unquoted(
      order
    )}, "RETENTION_PERIOD_READ" ${sql.unquoted(order)}`;
  }

  return sqlQuery;
}

/**
 * Handles the retrieval of audit log spaces based on query parameters and sends the result as a response.
 *
 * @param req - The HTTP request object, containing query parameters and context.
 *   - `dppRead` (optional): A boolean query parameter to filter spaces by DPP read access.
 *   - `dppChange` (optional): A boolean query parameter to filter spaces by DPP change access.
 *   - `retentionPeriod` (optional): A number query parameter to filter spaces by exact retention period.
 *   - `retentionPeriodGt` (optional): A number query parameter to filter spaces with retention period greater than the specified value.
 *   - `retentionPeriodLt` (optional): A number query parameter to filter spaces with retention period less than the specified value.
 *   - `order` (optional): A string query parameter to specify the sorting order, either "ASC" or "DESC". Defaults to "DESC".
 * @param res - The HTTP response object used to send the result back to the client.
 *
 * @returns Sends an array of objects containing audit policy details:
 *  - `SPACE_ID`: The ID of the space.
 * - `HAS_READ_POLICY`: A boolean indicating if the space has a read policy.
 * - `RETENTION_PERIOD_READ`: The retention period for read access.
 * - `HAS_CHANGE_POLICY`: A boolean indicating if the space has a change policy.
 * - `RETENTION_PERIOD_CHANGE`: The retention period for change access.
 */
export const getAuditLogSpacesRoute = async (req: Request, res: Response) => {
  try {
    const {
      dppRead: dppReadQuery,
      dppChange: dppChangeQuery,
      retentionPeriod,
      retentionPeriodGt,
      retentionPeriodLt,
      order: orderQuery,
    } = req.query;

    const dppRead = dppReadQuery ? parseBoolean(dppReadQuery, "dppRead") : null;
    const dppChange = dppChangeQuery ? parseBoolean(dppChangeQuery, "dppChange") : null;
    const order = (orderQuery as string)?.toUpperCase() === "ASC" ? "ASC" : "DESC";

    const spaces = await getSpaces(req.context, { inSpaceManagement: true });
    const spaceIds = spaces.map((space) => space.name);

    const WHERE = buildWhereClause({
      spaceIds,
      dppRead,
      dppChange,
      retentionPeriod: Number(retentionPeriod),
      retentionPeriodGt: Number(retentionPeriodGt),
      retentionPeriodLt: Number(retentionPeriodLt),
    });

    const sqlQuery = buildSqlQuery({ WHERE, dppRead, dppChange, order });

    const customerHana = await CustomerHana.fromRequestContext(req.context);
    const client = await customerHana.getTenantManagerClient();

    const result = await client.exec<{
      SPACE_ID: string;
      HAS_READ_POLICY: boolean;
      RETENTION_PERIOD_READ: number;
      HAS_CHANGE_POLICY: boolean;
      RETENTION_PERIOD_CHANGE: number;
    }>(sql`${sqlQuery}`);

    res.send(result);
  } catch (err) {
    return sendErrorResponse(req.context, "getAuditLogSpacesRouteFailed", { err });
  }
};
