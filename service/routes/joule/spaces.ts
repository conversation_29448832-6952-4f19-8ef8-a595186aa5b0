/** @format */
/* eslint-disable @typescript-eslint/ban-types */

import { Activity, AuthType, IRepositoryObject } from "@sap/deepsea-types";
import { CodedError } from "@sap/dwc-express-utils";
import { ObjectType, validateName } from "@sap/dwc-name-validator";
import { ErrorCode, WarningCode } from "@sap/dwc-name-validator/dist/constants";
import { Request, Response } from "express";
import Status, { StatusCodes } from "http-status-codes";
import { ISpaceCount } from "../../../shared/api/ScopedUsers";
import {
  IPostSpaceNameValidationBody,
  IPostSpaceNameValidationResponse,
  JouleSpaceNameValidationErrorCode,
  JouleSpaceNameValidationStatus,
} from "../../../shared/joule/types";
import { ResourceConsumption, SpaceStatus } from "../../../shared/spaces/types";
import { FeatureFlagProvider } from "../../featureflags/FeatureFlagProvider";
import { CustomerHana } from "../../reuseComponents/spaces/src";
import { CustomerHanaRuntimeData } from "../../reuseComponents/spaces/src/CustomerHanaRuntimeData";
import { sendErrorResponse } from "../../server/errorResponse";
import { getUserCountByScope } from "../space/user";
import { executeEnterpriseSearch, executeRuntimeCall } from "./spacesUtils";
import { OrderDirection } from "./types";

export interface EnterpriseSearchResult extends IRepositoryObject {
  spaceType: string;
  deploymentStatus: string;
  capabilitiesList: string | null;
  modelCount?: number;
  connectionCount?: number;
}

export interface SpaceRunTimeResult {
  name: string;
  status: string;
  disk: ResourceConsumption & { diskUsagePercentage: number };
  memory: ResourceConsumption & { memoryUsagePercentage: number };
  lockReason: string | null;
}

export interface SpaceUserCountResult {
  name: string;
  count: number;
}
export interface MergedResult extends EnterpriseSearchResult {
  name: string;
  status?: string;
  disk?: ResourceConsumption & { diskUsagePercentage: number };
  memory?: ResourceConsumption & { memoryUsagePercentage: number };
  lockReason?: string | null;
  userCount?: number;
}

enum SortingFailedReason {
  None = "None",
  Unauthorized = "Unauthorized",
  NotSupportedOrderBy = "NotSupportedOrderBy",
  NotSupportedOrderDirection = "NotSupportedOrderDirection",
  NotSupportedBoth = "NotSupportedBoth",
}

enum ZeroSpacesReason {
  None = "None",
  NotFound = "NotFound",
  Unauthorized = "Unauthorized",
}

export const sortingRules = new Map<string, { order: OrderDirection; manualSorting: boolean }>([
  ["name", { order: OrderDirection.Ascending, manualSorting: false }],
  ["creator", { order: OrderDirection.Ascending, manualSorting: false }],
  ["businessName", { order: OrderDirection.Ascending, manualSorting: false }],
  ["spaceType", { order: OrderDirection.Ascending, manualSorting: false }],
  ["deploymentDate", { order: OrderDirection.Descending, manualSorting: false }],
  ["creationDate", { order: OrderDirection.Descending, manualSorting: false }],
  ["status", { order: OrderDirection.Ascending, manualSorting: true }],
  ["memoryUsed", { order: OrderDirection.Descending, manualSorting: true }],
  ["memoryAssigned", { order: OrderDirection.Descending, manualSorting: true }],
  ["memoryUsagePercentage", { order: OrderDirection.Descending, manualSorting: true }],
  ["diskUsed", { order: OrderDirection.Descending, manualSorting: true }],
  ["diskAssigned", { order: OrderDirection.Descending, manualSorting: true }],
  ["diskUsagePercentage", { order: OrderDirection.Descending, manualSorting: true }],
]);

export interface ScopedPrivileges {
  type: AuthType;
  bits: Activity[];
}

export async function postJouleSpaceNameValidationRoute(
  req: Request<{}, {}, IPostSpaceNameValidationBody>,
  res: Response<IPostSpaceNameValidationResponse>
) {
  try {
    const customerHana = await CustomerHana.fromRequestContext(req.context);
    const customerHanaRuntimeData = new CustomerHanaRuntimeData(req.context);
    const { disk, memory } = await customerHanaRuntimeData.getStorageConsumptionOverview();
    const minSpaceSizeInByte: number = Math.pow(10, 9);

    let spaceId = req.body?.spaceId;
    let spaceName = req.body?.spaceName;
    let errorCode = JouleSpaceNameValidationErrorCode.NONE;
    const errorDetails: string[] = [];

    if (spaceId == null) {
      const validationResult = validateName({ computeDerivedName: true })(ObjectType.Space, spaceName?.toUpperCase());
      spaceId = validationResult.derivationResults?.derivedName ?? "";
    }
    if (spaceName == null) {
      spaceName = spaceId;
    }

    const validationResult = validateName({ computeDerivedName: true })(ObjectType.Space, spaceId);
    let status = JouleSpaceNameValidationStatus[validationResult.status];

    if (status !== JouleSpaceNameValidationStatus.OK) {
      switch (validationResult.failedValidations?.[0]?.errorCode) {
        case ErrorCode.nameValidator_InvalidCharacter:
          errorCode = JouleSpaceNameValidationErrorCode.INVALID_CHARACTER;
          validationResult.failedValidations?.[0]?.matchedSubstrings?.forEach((invalidChar) => {
            errorDetails.push(invalidChar.value);
          });
          break;
        case ErrorCode.nameValidator_ReservedKeyword:
          errorCode = JouleSpaceNameValidationErrorCode.RESERVED_KEYWORD;
          errorDetails.push(String(validationResult.failedValidations?.[0]?.details?.keywordNotAllowed ?? ""));
          break;
        case ErrorCode.nameValidator_ReservedPrefix:
        case WarningCode.nameValidator_warning_ReservedPrefix:
          errorCode = JouleSpaceNameValidationErrorCode.RESERVED_PREFIX;
          errorDetails.push(String(validationResult.failedValidations?.[0]?.details?.reservedPrefix ?? ""));
          break;
      }
    } else if (await customerHana.spaceExists(spaceId)) {
      status = JouleSpaceNameValidationStatus.ERROR;
      errorCode = JouleSpaceNameValidationErrorCode.SPACE_EXISTS;
    } else if (
      (disk.total ?? 0) - (disk.assigned ?? 0) < minSpaceSizeInByte ||
      (memory.total ?? 0) - (memory.assigned ?? 0) < minSpaceSizeInByte
    ) {
      status = JouleSpaceNameValidationStatus.ERROR;
      errorCode = JouleSpaceNameValidationErrorCode.INSUFFICIENT_STORAGE;
    }

    res.json({
      spaceId,
      spaceName,
      status,
      errorCode,
      errorDetails,
    });
  } catch (err) {
    sendErrorResponse(req.context, "postJouleSpaceNameValidationFailed", { err });
  }
}

export async function getSpacesForJoule(req: Request, res: Response) {
  try {
    let spaceCount;
    let top;
    let sortingFailedReason = SortingFailedReason.None;
    let zeroSpacesReason = ZeroSpacesReason.None;
    //  Check FF
    const isFeatureFlagActive = await FeatureFlagProvider.isFeatureActive(
      req.context,
      "DWCO_JOULE_DWAASCORE_ENHANCED_SPACE_INFO"
    );

    if (isFeatureFlagActive) {
      sortingRules.set("userCount", { order: OrderDirection.Descending, manualSorting: true });
      sortingRules.set("connectionCount", { order: OrderDirection.Descending, manualSorting: false });
      sortingRules.set("modelCount", { order: OrderDirection.Descending, manualSorting: false });
    }
    //  Check if spaceStatusFilter is set, if not use default value ''
    const spaceStatusFilter = req.query?.status || "";

    //  Check if status=Critical and orderBy is not set --> Set orderBy to diskUsagePercentage
    //  If orderBy is not set and Space Status filter is not critical , use default value 'name'
    let orderBy =
      spaceStatusFilter === SpaceStatus.Critical && !req.query?.orderBy
        ? "diskUsagePercentage"
        : (req.query?.orderBy as string) || "name";

    //  Check if orderDirection is set, if not use orderDirection defined in the rule set for the orderBy
    //  If orderBy is not found in the rule set (because it is not supported), we use the default orderDirection Ascending
    let orderDirection =
      (req.query?.orderDirection as OrderDirection) || sortingRules.get(orderBy)?.order || OrderDirection.Ascending;

    // Validate if orderBy and orderDirection are valid, else apply fallback (default sorting)
    if (!sortingRules.has(orderBy) || !Object.values(OrderDirection).includes(orderDirection)) {
      if (!sortingRules.has(orderBy)) {
        sortingFailedReason = !Object.values(OrderDirection).includes(orderDirection)
          ? SortingFailedReason.NotSupportedBoth
          : SortingFailedReason.NotSupportedOrderBy;
        orderBy = "name"; // Default to 'name' if orderBy is not valid
        orderDirection = OrderDirection.Ascending; // Default order direction for 'name'
      } else {
        sortingFailedReason = SortingFailedReason.NotSupportedOrderDirection;
        orderDirection = sortingRules.get(orderBy)!.order; // Use default order for valid orderBy
      }
    }

    //  Read query parameters and create filters + define top value
    const privileges = (req.query?.scopedPrivileges as string) || undefined;
    const userCount = (req.query?.userCount as string) || undefined;
    // Joule can display a maximum of 30 results.
    // However, for attributes not retrieved via EnterpriseSearch (such as status or userCount),
    // we cannot limit the result set from the Enterprise Search to 30, because all spaces must be fetched first to apply filtering afterward.
    if (
      spaceStatusFilter !== "" ||
      privileges ||
      (isFeatureFlagActive && userCount) ||
      ["memory", "disk", "userCount"].some((keyword) => orderBy.includes(keyword))
    ) {
      top = "10000";
    } else {
      top = "30";
    }

    const spaceTypeFilter = req.query?.spaceType ? createSpaceTypeFilter(req.query?.spaceType as string) : "";
    const spaceFilter = req.query?.space
      ? createSpaceFilter(req.query?.space as string, req.query?.spaceExactSearch as string)
      : "";
    const deploymentDateFilter =
      req.query?.deploymentDateFrom || req.query?.deploymentDateTo ? createDeploymentDateFilter(req) : "";
    const lifecycleStatusFilter = req.query?.lifecycleStatus
      ? createLifecycleStatusFilter(req.query?.lifecycleStatus as string)
      : "";
    const creatorFilter = req.query?.creator ? createCreatorFilter(req.query?.creator as string) : "";
    const creationDateFilter =
      req.query?.creationDateFrom || req.query?.creationDateTo ? createCreationDateFilter(req) : "";
    const deploymentStatusFilter = req.query?.deploymentStatus
      ? createDeploymentStatusFilter(req.query?.deploymentStatus as string)
      : "";
    const spaceCapabilityFilter = req.query?.spaceCapabilities
      ? createSpaceCapabilityFilter(req.query?.spaceCapabilities as string)
      : "";
    //  Execute Enterprise Search
    const responseEnterprise = await executeEnterpriseSearch(
      req,
      top,
      orderBy,
      orderDirection,
      lifecycleStatusFilter,
      spaceTypeFilter,
      spaceFilter,
      deploymentDateFilter,
      creatorFilter,
      creationDateFilter,
      deploymentStatusFilter,
      spaceCapabilityFilter
    );
    //  If no spaces are found, we do not need to execute the runtime call.
    //  Therefore, we can directly return an empty array
    if (responseEnterprise.spaces.length === 0) {
      zeroSpacesReason = ZeroSpacesReason.NotFound;
      res
        .status(Status.OK)
        .send({ result: [], spaceCount: 0, orderBy, orderDirection, sortingFailedReason, zeroSpacesReason });
      return;
    }
    //  Execute Runtime Call
    const responseRuntime = await executeRuntimeCall(
      req,
      responseEnterprise.spaces.map((space) => space.name)
    );

    let mergedResult;
    if (isFeatureFlagActive) {
      let responseSpaceUserCount: ISpaceCount[] | undefined;
      //  Execute Space-User-Count call if FF is true
      try {
        responseSpaceUserCount = await getUserCountByScope(req.context);
        //  responseSpaceUserCount only contains spaces that have users assigned.
        //  Therefore, if it does not contain all spaces, we need to add the missing spaces with a userCount of 0
        responseEnterprise.spaces.forEach((element) => {
          const existInSpaceUserCount = responseSpaceUserCount!.some((userCount) => userCount.name === element.name);
          if (!existInSpaceUserCount) {
            responseSpaceUserCount!.push({ name: element.name, count: 0 });
          }
        });
      } catch (error) {
        //  In case we get an error (for instance missing authorization), we set the responseSpaceUserCount to undefined
        responseSpaceUserCount = undefined;
        if (error.status === StatusCodes.UNAUTHORIZED) {
          if (orderBy === "userCount") {
            //  If the user is not allowed to fetch the userCount and we are sorting by userCount, we switch to name (default sorting)
            orderBy = "name";
            //  Set orderDirection to Ascending as this is the default sorting for "name"
            orderDirection = OrderDirection.Ascending;
            sortingFailedReason = SortingFailedReason.Unauthorized;
            //  We modify the sortingRules (ManualSorting:true) , because ES Call (which is normally used to sort by name) was already made
            //  Therefore, we need to manually sort
            sortingRules.set("name", { order: OrderDirection.Ascending, manualSorting: true });
          } else {
            zeroSpacesReason = ZeroSpacesReason.Unauthorized;
            res
              .status(Status.OK)
              .send({ result: [], spaceCount: 0, orderBy, orderDirection, sortingFailedReason, zeroSpacesReason });
          }
        }
      }
      //  Merge results of Enterprise Search, Runtime Call, and userCount if FF is active
      mergedResult = mergeResults(responseEnterprise.spaces, responseRuntime, responseSpaceUserCount);
    } else {
      //  Merge results of Enterprise Search and Runtime Call if FF not active
      mergedResult = mergeResults(responseEnterprise.spaces, responseRuntime);
    }

    //  Apply status filter if set and save the spaceCount
    if (spaceStatusFilter !== "") {
      mergedResult = filterForSpaceStatus(mergedResult, spaceStatusFilter as string);
      spaceCount = mergedResult.length;
    } else {
      spaceCount = responseEnterprise.spaceCount;
    }
    if (sortingRules.get(orderBy)?.manualSorting) {
      mergedResult = defineOrder(mergedResult, orderBy, orderDirection, isFeatureFlagActive);
    }

    //  Check if userCount param is set
    if (isFeatureFlagActive && userCount) {
      mergedResult = mergedResult.filter((space) => space.userCount === Number(userCount));
      spaceCount = mergedResult.length;
    }
    //  Check if user has required privileges for the space
    if (privileges) {
      //  Privilege string example: DWC_REMOTECONNECTION:read,update;DWC_SPACES:read
      //  ";" --> separates the different privileges
      //  ":" --> separates the privilege type from the bits

      const scopedPrivileges: ScopedPrivileges[] = privileges.split(";").map((privilege) => {
        const [type, bits] = privilege.split(":");
        return { type: type as AuthType, bits: (bits?.split(",") ?? []) as unknown as Activity[] };
      });

      //  Filter the mergedResult based on the scopedPrivileges
      mergedResult = mergedResult.filter((space) =>
        scopedPrivileges.every((privilege) =>
          privilege.bits.every((bit) =>
            Activity[bit] == null
              ? false
              : req.context.hasPrivilegeOnScope(space.name, privilege.type, Activity[bit] as unknown as Activity)
          )
        )
      );

      spaceCount = mergedResult.length;
    }

    //  In case the space status filter is set, it could be that we have more than 30 results.
    //  Therefore, we reduce the result set to 30 as Joule can only display 30 results
    mergedResult = mergedResult.slice(0, 30);
    res
      .status(Status.OK)
      .send({ result: mergedResult, spaceCount, orderBy, orderDirection, sortingFailedReason, zeroSpacesReason });
  } catch (err) {
    return sendErrorResponse(req.context, "getSpacesForJouleFailed", { err });
  }
}

function filterForSpaceStatus(mergedResult: MergedResult[], spaceStatusFilter: string): MergedResult[] {
  const spaceStatusFilterToLowerCase = spaceStatusFilter.toLowerCase();
  return mergedResult.filter((space: MergedResult) => space.status === spaceStatusFilterToLowerCase);
}

export function mergeResults(
  enterpriseSearchResult: EnterpriseSearchResult[],
  runtimeResult: SpaceRunTimeResult[],
  userCountResult?: ISpaceCount[] | undefined
): MergedResult[] {
  const mergedArray = [];

  // Preprocess runtimeResult into a hash map (faster lookup O(1) compared to Array.find)
  const runtimeMap = new Map(runtimeResult.map((item) => [item.name, item]));

  // Preprocess userCountResult into a hash map (if it exists, otherwise empty map - faster lookup O(1) compared to Array.find)
  const userCountMap = userCountResult ? new Map(userCountResult.map((item) => [item.name, item])) : new Map();

  // Iterate over enterpriseSearchResult and merge results
  for (const enterpriseItem of enterpriseSearchResult) {
    const matchingRuntimeItem = runtimeMap.get(enterpriseItem.name);
    const matchingUserCountItem = userCountMap.get(enterpriseItem.name);

    mergedArray.push({
      ...enterpriseItem,
      ...(matchingRuntimeItem || {}),
      ...(matchingUserCountItem ? { userCount: matchingUserCountItem.count } : { userCount: null }),
    });
  }

  return mergedArray;
}

function createSpaceFilter(space: string, spaceExactSearch: string): string {
  const exact_search = spaceExactSearch ? "):EQ:" : " business_name):";
  return ` AND (name${encodeURIComponent(exact_search)}"${encodeURIComponent(space)}"`;
}

function createSpaceCapabilityFilter(spaceCapabilities: string): string {
  //  Split the input string into an array of values
  const values = spaceCapabilities.split(",");

  // Map each value to a query filter
  const filters = values.map((value) => {
    const trimmedValue = value.trim();
    return `capabilities_list:EQ:*${encodeURIComponent(trimmedValue)}*`;
  });
  //  join the results
  return ` AND ${filters.join(" AND ")}`;
}

function createDeploymentDateFilter(req: Request): string {
  if (req.query?.deploymentDateFrom && req.query?.deploymentDateTo) {
    const deploymentDateFrom = modifyDateFormat(req.query?.deploymentDateFrom as string);
    const deploymentDateTo = modifyDateFormat(req.query?.deploymentDateTo as string);
    return ` AND deployment_date:GE:"${encodeURIComponent(
      deploymentDateFrom
    )}" AND deployment_date:LE:"${encodeURIComponent(deploymentDateTo)}"`;
  } else if (req.query?.deploymentDateFrom) {
    const deploymentDateFrom = modifyDateFormat(req.query?.deploymentDateFrom as string);
    return ` AND deployment_date:GE:"${encodeURIComponent(deploymentDateFrom)}"`;
  } else if (req.query?.deploymentDateTo) {
    const deploymentDateTo = modifyDateFormat(req.query?.deploymentDateTo as string);
    return ` AND deployment_date:LE:"${encodeURIComponent(deploymentDateTo)}"`;
  }
  return "";
}

function modifyDateFormat(date: string): string {
  const originalDate = new Date(date);
  originalDate.setSeconds(0, 0);
  let modifiedDate = originalDate.toISOString();
  const splitDate = modifiedDate.split("T");
  modifiedDate = splitDate[0] + " " + splitDate[1].split(".")[0];
  return modifiedDate.replace(/"/g, "");
}

function createCreationDateFilter(req: Request): string {
  if (req.query?.creationDateFrom && req.query?.creationDateTo) {
    const creationDateFrom = modifyDateFormat(req.query?.creationDateFrom as string);
    const creationDateTo = modifyDateFormat(req.query?.creationDateTo as string);
    return ` AND creation_date:GE:"${encodeURIComponent(creationDateFrom)}" AND creation_date:LE:"${encodeURIComponent(
      creationDateTo
    )}"`;
  } else if (req.query?.creationDateFrom) {
    const creationDateFrom = modifyDateFormat(req.query?.creationDateFrom as string);
    return ` AND creation_date:GE:"${encodeURIComponent(creationDateFrom)}"`;
  } else if (req.query?.creationDateTo) {
    const creationDateTo = modifyDateFormat(req.query?.creationDateTo as string);
    return ` AND creation_date:LE:"${encodeURIComponent(creationDateTo)}"`;
  }
  return "";
}

function createSpaceTypeFilter(spaceType: string): string {
  const spaceTypeLowerCase = spaceType.toLowerCase();
  const hasDatasphere = spaceTypeLowerCase.includes("datasphere");
  const hasBwBridge = spaceTypeLowerCase.includes("bw bridge");

  if (hasDatasphere || hasBwBridge) {
    const notPrefix = hasDatasphere ? "NOT " : "";
    return ` AND (${encodeURIComponent(notPrefix)}space_type:EQ(S):abapbridge)`;
  }
  return "";
}

function createLifecycleStatusFilter(lifecycleStatus: string): string {
  if (lifecycleStatus === "BOTH") {
    return ` AND (lifecycle_status:EQ:DELETED OR (NOT lifecycle_status:EQ:DELETED))`;
  } else if (lifecycleStatus === "DELETED") {
    return `AND lifecycle_status:EQ:DELETED`;
  }
  return "";
}

function createCreatorFilter(creator: string): string {
  return `AND creator:EQ:${encodeURIComponent(creator)}`;
}

function createDeploymentStatusFilter(deploymentStatus: string): string {
  return `AND deployment_status:EQ:${encodeURIComponent(deploymentStatus)}`;
}

export function defineOrder(
  mergedResult: MergedResult[],
  orderBy: string,
  orderDirection: OrderDirection,
  isFeatureFlagActive: boolean
): MergedResult[] {
  let compareFunction: (a: any, b: any) => number;

  const compareStrings = (a: string, b: string) => {
    if (orderDirection === OrderDirection.Ascending) {
      return a.localeCompare(b);
    } else {
      return b.localeCompare(a);
    }
  };

  const compareNumbers = (a: number, b: number) => {
    if (orderDirection === OrderDirection.Ascending) {
      return a - b;
    } else {
      return b - a;
    }
  };
  if (["name", "status"].includes(orderBy)) {
    compareFunction = (a, b) => compareStrings(a[orderBy] ?? "", b[orderBy] ?? "");
  } else if (["memoryUsed", "diskUsed"].includes(orderBy)) {
    const baseKey = orderBy.split(/(?=[A-Z])/)[0]; // Extract "memory" or "disk"
    compareFunction = (a, b) => compareNumbers(a[baseKey]?.used ?? 0, b[baseKey]?.used ?? 0);
  } else if (["memoryAssigned", "diskAssigned"].includes(orderBy)) {
    const baseKey = orderBy.split(/(?=[A-Z])/)[0]; // Extract "memory" or "disk"
    compareFunction = (a, b) => compareNumbers(a[baseKey]?.assigned ?? 0, b[baseKey]?.assigned ?? 0);
  } else if (["memoryUsagePercentage"].includes(orderBy)) {
    compareFunction = (a, b) =>
      compareNumbers(a.memory?.memoryUsagePercentage ?? 0, b.memory?.memoryUsagePercentage ?? 0);
  } else if (["diskUsagePercentage"].includes(orderBy)) {
    compareFunction = (a, b) => compareNumbers(a.disk?.diskUsagePercentage ?? 0, b.disk?.diskUsagePercentage ?? 0);
  } else if (isFeatureFlagActive && ["userCount"].includes(orderBy)) {
    compareFunction = (a, b) => compareNumbers(a.userCount ?? 0, b.userCount ?? 0);
  } else {
    throw new CodedError("invalidOrderBy", `Invalid orderBy parameter: ${orderBy}`, StatusCodes.BAD_REQUEST);
  }
  return mergedResult.sort(compareFunction);
}
