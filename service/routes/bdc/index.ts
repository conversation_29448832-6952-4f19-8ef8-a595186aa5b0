/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { Router } from "express";
import { getDataReplicationJobStatus } from "../../bdc/acnProvision/dataReplicationJobStatus";
import { BdcOffboardingController } from "../../bdc/acnProvision/offboarding/bdcOffboardingController";
import { BdcOnboardingController } from "../../bdc/acnProvision/onboarding/bdcOnboardingController";
import {
  cancelInstallation,
  deletePackage,
  getInstalledPackageByInstallationId,
  getInstalledPackages,
  getSystemLandscape,
  installPackage,
  reinstallPackage,
  uninstallPackage,
  updateInstalledPackageSelfHealingTimeout,
} from "../../bdc/packageInstallation/packageInstallationController";
import {
  getPackageById,
  getPackages,
  setConfigurationRepoBranch,
  supportGetPackages,
} from "../../bdc/packages/packagesController";
import { handleCheckConnectionRequest } from "../../bdc/systems/systemConnectionController";
import { toAsyncRouter } from "../async-router";
import {
  deleteUclApplicationTemplateRoute,
  getUclApplicationTemplateRoute,
  patchUclApplicationTemplateRoute,
  postUclApplicationTemplateRoute,
} from "./support/uclApplicationTemplate";
import { handleNotificationRoute } from "./ucl/uclNotifications";

export const bdcRouter = toAsyncRouter(Router());

// The routes /bdc/onboarding and /bdc/offboarding will be deprecated
bdcRouter.post("/bdc/onboarding", BdcOnboardingController.processOnboarding);
bdcRouter.post("/bdc/offboarding", BdcOffboardingController.processOffboarding);

// The new routes to be used are with support has prefix
bdcRouter.post("/support/bdc/onboarding", BdcOnboardingController.processOnboarding);
bdcRouter.post("/support/bdc/offboarding", BdcOffboardingController.processOffboarding);

bdcRouter.post("/bdc/installedpackages", installPackage);
bdcRouter.post("/bdc/installedpackages/:installationId/cancel", cancelInstallation);
bdcRouter.put("/bdc/installedpackages/:installationId", reinstallPackage);
bdcRouter.delete("/bdc/installedpackages/:installationId", deletePackage);
// TODO: we might only use one "get" '/bdc/installedpackages/:installationId' - where :installationId is optional
bdcRouter.get("/bdc/installedpackages/:installationId", getInstalledPackageByInstallationId);
bdcRouter.get("/bdc/installedpackages", getInstalledPackages);
bdcRouter.post("/bdc/installedpackages/:installationid/selfhealingtimeout", updateInstalledPackageSelfHealingTimeout);

bdcRouter.get("/bdc/packages/:packageid", getPackageById);
bdcRouter.get("/bdc/packages", getPackages);
bdcRouter.post("/bdc/packages/setrepobranch", setConfigurationRepoBranch);
bdcRouter.get("/bdc/systemlandscape", getSystemLandscape);
bdcRouter.delete("/bdc/uninstallpackage/:installationId", uninstallPackage);

// get data replication status
// TODO: deprecate this route in favor of the one with support prefix
bdcRouter.get("/bdc/datareplicationstatus/:jobid", getDataReplicationJobStatus);

// get data replication status
bdcRouter.get("/support/tenant/bdc/datareplicationstatus/:jobid", getDataReplicationJobStatus);

// UCL tenant mapping
bdcRouter.patch("/bdc/ucl/tenant-mapping/:tenantid", handleNotificationRoute);

// UCL support routes
bdcRouter.get("/bdc/support/ucl/application-template", getUclApplicationTemplateRoute);
bdcRouter.post("/bdc/support/ucl/application-template", postUclApplicationTemplateRoute);
bdcRouter.patch("/bdc/support/ucl/application-template", patchUclApplicationTemplateRoute);
bdcRouter.delete("/bdc/support/ucl/application-template", deleteUclApplicationTemplateRoute);

bdcRouter.get("/bdc/check-connection/:assignmentId", handleCheckConnectionRequest);

// support routes for packages operations
bdcRouter.get("/bdc/support/packages", supportGetPackages);
