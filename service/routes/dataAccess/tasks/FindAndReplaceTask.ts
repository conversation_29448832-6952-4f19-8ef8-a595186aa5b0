/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
// FILEOWNER: [data-viewer]

import { CodedError } from "@sap/dwc-express-utils";
import { DbClient } from "../../../lib/DbClient";
import { AuthType, IRequestContext, Activity as PermissionActivity } from "../../../repository/security/common/common";
import * as resourceMonitoring from "../../../resourceMonitoring/expensiveMemory";
import * as dbVersionChecker from "../../../reuseComponents/onboarding/src/dbVersionChecker";
import { CustomerHana, CustomerHanaRuntimeData } from "../../../reuseComponents/spaces/src";
import { AuthorizationUtils } from "../../../reuseComponents/utility/AuthorizationUtils";
import { FeatureFlagUtils } from "../../../reuseComponents/utility/FeatureFlagUtils";
import { LocalTableFilesTask } from "../../../table/task/LocalTableFilesTask";
import { LocalTableTask } from "../../../table/task/LocalTableTask";
import { LocalTableTaskLogMessageBundleModel } from "../../../table/task/types";
import { ITaskLogger, Severity, Status } from "../../../task/logger/models";
import { Activity } from "../../../task/models";
import { TaskExecuteResponse } from "../../../task/models/TaskExecuteResponse";
import { Parameters } from "../../../task/orchestrator/models/Parameters";
import { logger } from "../lib/logger";

export class FindAndReplaceTask extends LocalTableFilesTask {
  requiredDBVersion: string;
  constructor(
    requestContext: IRequestContext,
    spaceName: string,
    tableName: string,
    activity: Activity,
    parameters: Parameters
  ) {
    super(requestContext, spaceName, tableName, activity, parameters);
    if (activity === Activity.FIND_AND_REPLACE) {
      this.messageBundleKeyStarting = LocalTableTaskLogMessageBundleModel.FIND_AND_REPLACE_STATUS_STARTING;
      this.messageBundleTextStarting = "Starting to find and replace with Local Table (File).";
      this.messageBundleKeyFailed = LocalTableTaskLogMessageBundleModel.FIND_AND_REPLACE_FAILED_ERROR;
      this.messageBundleTextFailed = "An error occurred while performing find and replace with Local Table (File).";
      this.requiredDBVersion = "0.760"; // check if this is the correct version for the activity
      this.procedureName = "LTF_ETV_FIND_AND_REPLACE";
      this.additionalParameters.push({
        name: `CONFIGURATION`,
        value: JSON.stringify(this.parameters.configuration),
      });
    } else {
      throw new Error(`Invalid activity: ${activity}`);
    }
  }

  /**
   * @override
   */
  async isAuthorized(taskLogger: ITaskLogger, isDesignTime?: boolean): Promise<boolean> {
    if (!this.spaceName) {
      logger.logError("Invalid space", { context: this.requestContext });
      throw new Error(`spaceNotFound: Space '${this.spaceName}' does not exist.`);
    }

    if (this.authorizedCache !== undefined) {
      logger.logInfo("isAuthorized - uses result from cache", { context: this.requestContext });
    } else {
      this.authorizedCache =
        (await AuthorizationUtils.isAuthorized(
          this.requestContext,
          AuthType.DWC_CONSUMPTION,
          PermissionActivity.update,
          this.spaceName
        )) ||
        (await AuthorizationUtils.isAuthorized(
          this.requestContext,
          AuthType.DWC_CONSUMPTION,
          PermissionActivity.execute,
          this.spaceName
        ));
      if (!this.authorizedCache) {
        logger.logWarning("Missing authorization", { context: this.requestContext });
      }
    }
    return this.authorizedCache;
  }

  async execute(taskLogger: ITaskLogger): Promise<TaskExecuteResponse> {
    let dbClient: DbClient | undefined;
    try {
      const customerHanaRuntimeData = new CustomerHanaRuntimeData(this.requestContext);
      const isLocked = await customerHanaRuntimeData.isSpaceLocked(this.spaceName);
      if (isLocked) {
        await this.logTaskMessage(
          taskLogger,
          Severity.ERROR,
          LocalTableTaskLogMessageBundleModel.EXECUTION_ERROR_LOCKED_SPACE,
          `Space ''${this.spaceName}'' is locked.`,
          undefined,
          [this.spaceName]
        );
        return { status: Status.FAILED, subStatusCode: undefined };
      }

      const taskLogId = taskLogger.getLogId();

      const customerHana = await CustomerHana.fromRequestContext(this.requestContext);
      const schemaNames = await customerHana.selectSpace(this.spaceName).getSchemaNames();
      dbClient = await this.getDbClient();
      const tecSchemaName = schemaNames.spc_tec_internal;

      await resourceMonitoring.trackSingleResourceConsumpConnectionStart(taskLogId, this.requestContext, {
        dbClient,
        time: new Date().toISOString(),
      });

      const dbVersionEqualOrHigher = await dbVersionChecker.isDbVersionEqualOrHigher(
        this.requestContext,
        this.requiredDBVersion
      );
      if (!dbVersionEqualOrHigher) {
        throw new CodedError(`dbVersionTooLow`, `DB version of space is too low to execute the Find and Replace task.`);
      }

      await this.logTaskMessage(
        taskLogger,
        Severity.INFO,
        this.messageBundleKeyStarting,
        this.messageBundleTextStarting,
        undefined,
        undefined
      );

      this.additionalParameters.push({
        name: "TEC_SCHEMA_NAME",
        value: tecSchemaName,
      });
      this.additionalParameters.push({
        name: "FEATURE_FLAG_JSON",
        value: await FeatureFlagUtils.getJson(this.requestContext, [
          "DWCO_LOCAL_TABLE_FILES_SIZE_DELTA_TABLE",
          "DWCO_LSA_RESOURCE_MONITOR",
        ]),
      });

      const { remoteSourceName, sparkApp } = await this.getSparkRemoteSourceDetails(
        schemaNames.space_schema,
        taskLogger
      );

      const sqlParameters: Array<{ name: string; value: any }> = [
        { name: `TASK_LOG_ID`, value: taskLogId },
        { name: `SCHEMA_NAME`, value: this.spaceName },
        { name: `TABLE_NAME`, value: this.tableName },
        { name: `REMOTE_SOURCE_NAME`, value: remoteSourceName },
        { name: `APPLICATION_INDEX`, value: sparkApp },
        ...this.additionalParameters,
      ];

      await this.executeAsync(CustomerHana.globalOwnerSchema, this.procedureName, sqlParameters, taskLogger);
      return Status.RUNNING;
    } catch (error) {
      const result = await LocalTableTask.mapErrorToMessageAndSubstatus(
        this.requestContext,
        this.activity,
        this.messageBundleTextFailed,
        this.messageBundleKeyFailed,
        error
      );
      await taskLogger.logMessage([result.taskLogMessage]);
      logger.logWarning(`Error during Find and Replace task: ${error.stack}`, { context: this.requestContext });
      return { status: Status.FAILED, subStatusCode: result.substatus };
    } finally {
      if (dbClient) {
        try {
          await resourceMonitoring.trackSingleResourceConsumpConnectionEnd(taskLogger.getLogId(), this.requestContext, {
            dbClient,
            time: new Date().toISOString(),
          });
        } catch (error) {
          logger.logError(`Error during resource-consumption tracking: ${error.stack}`, {
            context: this.requestContext,
          });
        }
      }
    }
  }
}
