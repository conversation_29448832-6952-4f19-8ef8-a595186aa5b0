/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import {
  DecoratorType,
  DeploymentRequestType,
  GenericDeploymentRequest,
  ILayer,
  ILayerConfig,
  LayerType,
} from "../../../interfaces";
import { createLayer } from "../../../types";
import {
  auditMassDeploy,
  finishDeployContext,
  notifyAsyncDeploymentEnd,
  notifyAsyncDeploymentStart,
  prepareDeployContext,
  sendMetrics,
  sendResponse,
} from "../../common";
import { validateDeploymentProgress, validateDeploymentRequest, validateLockedSpace } from "../../validators";
import { deployCsn } from "../../common/csn/DeployCsn";
import { prepareDeployMarketplaceDataProductMassCsn } from "./PrepareMarketplaceProductMassDeploy";
import { addMarketplaceDataProductsToHistory } from "./addHistory";

export function getDeployMarketplaceDataProductRequestProcessingLayers(req: GenericDeploymentRequest): ILayer[] {
  const { async, skipNotification, forceSendNotification } = req.body;
  const { isMassDeployment } = req;
  const skipNotificationMiddleWare = !async || skipNotification;
  const layerConfigs: ILayerConfig[] = [
    /**
     * Ensures that the request is well-formatted
     */
    {
      type: LayerType.Validation,
      handler: validateDeploymentRequest,
      identifier: DeploymentRequestType.DeployMarketplaceDataProduct,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },
    /**
     * Ensures that another deployment for the same object is not in progress
     */
    {
      type: LayerType.Validation,
      handler: validateDeploymentProgress,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
      /**
       * @see SKIPPED FOR MASS DEPLOYMENT
       */
      skipWhen: isMassDeployment,
    },
    /**
     * Ensures that deployment is not started on a locked space
     */
    {
      type: LayerType.Validation,
      handler: validateLockedSpace,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },
    /**
     * Notifies the start of the async deployment
     */
    {
      type: LayerType.StartNotification,
      handler: notifyAsyncDeploymentStart,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
      /**
       * @see SKIPPED FOR SYNC DEPLOYMENT
       */
      skipWhen: skipNotificationMiddleWare,
    },
    /**
     * Prepares CSN for deployment
     */
    {
      type: LayerType.Processing,
      handler: prepareDeployMarketplaceDataProductMassCsn,
      decorators: [
        { type: DecoratorType.DeployMetricsProcessor },
        { type: DecoratorType.PerformanceLogger, configuration: { warningThreshold: 5000 } },
      ],
    },
    /**
     * Prepares deployer context so we can access information from targeting space.
     */
    {
      type: LayerType.Initialization,
      handler: prepareDeployContext,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },

    /**
     * Deploys the CSN
     */
    {
      type: LayerType.Processing,
      handler: deployCsn,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }, { type: DecoratorType.PerformanceLogger }],
    },
    /**
     * Add the deployed marketplace data product to OBJECT_HISTORY
     */
    {
      type: LayerType.Processing,
      handler: addMarketplaceDataProductsToHistory,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }, { type: DecoratorType.PerformanceLogger }],
    },
    /**
     * Audit deploy
     */
    {
      type: LayerType.Audit,
      handler: auditMassDeploy,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },

    /**
     * Finish the deploy context opened as part of prepareDeployContext
     */
    {
      type: LayerType.Cleanup,
      handler: finishDeployContext,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },
    /**
     * Notify the end of the deployment
     */
    {
      type: LayerType.Termination,
      handler: notifyAsyncDeploymentEnd,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
      /**
       * @see SKIPPED FOR SYNC DEPLOYMENT
       */
      skipWhen: !forceSendNotification && skipNotificationMiddleWare,
    },

    /**
     * Send the results and status back to the client in case of sync deployment
     */
    {
      type: LayerType.Termination,
      handler: sendResponse,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
      /**
       * @see SKIPPED FOR SYNC DEPLOYMENT
       */
      skipWhen: async,
    },
    /**
     * Send deploy route metrics to dynatrace
     */
    {
      type: LayerType.MetricsReporter,
      identifier: DeploymentRequestType.DeployMarketplaceDataProduct,
      handler: sendMetrics,
    },
  ];
  return layerConfigs.map(createLayer);
}
