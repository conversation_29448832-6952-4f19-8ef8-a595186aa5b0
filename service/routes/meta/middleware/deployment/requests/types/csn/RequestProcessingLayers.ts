/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import {
  DecoratorType,
  DeploymentRequestType,
  GenericDeploymentRequest,
  ILayer,
  ILayerConfig,
  LayerType,
} from "../../../interfaces";
import { createLayer } from "../../../types";
import {
  auditDeploy,
  auditMassDeploy,
  deployCsn,
  deployRemoteCsn,
  finishDeployContext,
  insertDataAfterDeploy,
  notifyAsyncDeploymentEnd,
  notifyAsyncDeploymentStart,
  prepareDeployContext,
  prepareDeployCsn,
  prepareMassDeployCsn,
  prepareRemoteDeployContext,
  sendMetrics,
  sendResponse,
  shareEntities,
  updateAccessPolicy,
} from "../../common";
import { validateDeploymentProgress, validateDeploymentRequest, validateLockedSpace } from "../../validators";
import { saveForDeploy } from "../save";
import { clearViewPersistency } from "./ClearViewPersistency";
import { revokeGrants } from "./RevokeGrants";

export function getDeployCsnRequestProcessingLayers(req: GenericDeploymentRequest): ILayer[] {
  const { async, skipNotification, forceSendNotification } = req.body;
  const { isMassDeployment } = req;
  const skipNotificationMiddleWare = !async || skipNotification;
  const layerConfigs: ILayerConfig[] = [
    /**
     * Ensures that the request is well-formatted
     */
    {
      type: LayerType.Validation,
      handler: validateDeploymentRequest,
      identifier: DeploymentRequestType.DeployCsn,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },
    /**
     * Saves the CSN before deploying. For mass deployment it's
     * not required
     */
    {
      type: LayerType.Processing,
      handler: saveForDeploy,
      decorators: [
        { type: DecoratorType.DeployMetricsProcessor },
        { type: DecoratorType.PerformanceLogger, configuration: { warningThreshold: 15000 } },
      ],
      /**
       * @see SKIPPED FOR MASS DEPLOYMENT
       */
      skipWhen: isMassDeployment,
    },
    /**
     * Ensures that another deployment for the same object is not in progress
     */
    {
      type: LayerType.Validation,
      handler: validateDeploymentProgress,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
      /**
       * @see SKIPPED FOR MASS DEPLOYMENT
       */
      skipWhen: isMassDeployment,
    },
    /**
     * Ensures that deployment is not started on a locked space
     */
    {
      type: LayerType.Validation,
      handler: validateLockedSpace,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },
    /**
     * Notifies the start of the async deployment
     */
    {
      type: LayerType.StartNotification,
      handler: notifyAsyncDeploymentStart,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
      /**
       * @see SKIPPED FOR SYNC DEPLOYMENT
       */
      skipWhen: skipNotificationMiddleWare,
    },
    /**
     * Prepares CSN for deployment
     */
    {
      type: LayerType.Processing,
      handler: isMassDeployment ? prepareMassDeployCsn : prepareDeployCsn,
      decorators: [
        { type: DecoratorType.DeployMetricsProcessor },
        { type: DecoratorType.PerformanceLogger, configuration: { warningThreshold: 5000 } },
      ],
    },
    /**
     * Prepares deployer context so we can access information from targeting space.
     */
    {
      type: LayerType.Initialization,
      handler: prepareDeployContext,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },
    /**
     * Deploys the CSN
     */
    {
      type: LayerType.Processing,
      handler: deployCsn,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }, { type: DecoratorType.PerformanceLogger }],
    },
    /**
     * Fixes the broken shares
     */
    {
      type: LayerType.Processing,
      handler: shareEntities,
      decorators: [
        { type: DecoratorType.DeployMetricsProcessor },
        { type: DecoratorType.PerformanceLogger, configuration: { warningThreshold: 5000 } },
      ],
    },
    /**
     * Inserts data into entities when the data has been transported via ACN and the entities are newly deployed with no existing data.
     */
    {
      type: LayerType.Processing,
      handler: insertDataAfterDeploy,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }, { type: DecoratorType.PerformanceLogger }],
    },
    /**
     * Revoke Grants for un-published views
     */
    {
      type: LayerType.Processing,
      handler: revokeGrants,
      decorators: [
        { type: DecoratorType.DeployMetricsProcessor },
        { type: DecoratorType.PerformanceLogger, configuration: { warningThreshold: 5000 } },
      ],
    },

    /**
     * Clear view persistency
     */
    {
      type: LayerType.Processing,
      handler: clearViewPersistency,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },

    /**
     * Update access policy any time CSN contains any delta sharing objects
     */
    {
      type: LayerType.Processing,
      handler: updateAccessPolicy,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }, { type: DecoratorType.PerformanceLogger }],
    },

    /**
     * Audit deploy
     */
    {
      type: LayerType.Audit,
      handler: isMassDeployment ? auditMassDeploy : auditDeploy,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },

    /**
     * Finish the deploy context opened as part of prepareDeployContext
     */
    {
      type: LayerType.Cleanup,
      handler: finishDeployContext,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },
    /**
     * Notify the end of the deployment
     */
    {
      type: LayerType.Termination,
      handler: notifyAsyncDeploymentEnd,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
      /**
       * @see SKIPPED FOR SYNC DEPLOYMENT
       */
      skipWhen: !forceSendNotification && skipNotificationMiddleWare,
    },

    /**
     * Send the results and status back to the client in case of sync deployment
     */
    {
      type: LayerType.Termination,
      handler: sendResponse,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
      /**
       * @see SKIPPED FOR SYNC DEPLOYMENT
       */
      skipWhen: async,
    },
    /**
     * Send deploy route metrics to dynatrace
     */
    {
      type: LayerType.MetricsReporter,
      identifier: DeploymentRequestType.DeployCsn,
      handler: sendMetrics,
    },
  ];
  return layerConfigs.map(createLayer);
}

export function getRemoteDeployCsnRequestProcessingLayers(req: GenericDeploymentRequest): ILayer[] {
  const layerConfigs: ILayerConfig[] = [
    /**
     * Ensures that the request is well-formatted
     */
    {
      type: LayerType.Validation,
      handler: validateDeploymentRequest,
      identifier: DeploymentRequestType.DeployCsnToRemote,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },
    /**
     * Notifies the start of the async deployment
     */
    {
      type: LayerType.StartNotification,
      handler: notifyAsyncDeploymentStart,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },
    /**
     * Prepares deployer context so we can access information from targeting space.
     */
    {
      type: LayerType.Initialization,
      handler: prepareRemoteDeployContext,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },
    /**
     * Deploys the CSN
     */
    {
      type: LayerType.Processing,
      handler: deployRemoteCsn,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }, { type: DecoratorType.PerformanceLogger }],
    },
    /**
     * Audit deploy
     */
    {
      type: LayerType.Audit,
      handler: auditDeploy,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },
    /**
     * Notify the end of the deployment
     */
    {
      type: LayerType.Termination,
      handler: notifyAsyncDeploymentEnd,
      decorators: [{ type: DecoratorType.DeployMetricsProcessor }],
    },
    /**
     * Send deploy route metrics to dynatrace
     */
    {
      type: LayerType.MetricsReporter,
      identifier: DeploymentRequestType.DeployCsn,
      handler: sendMetrics,
    },
  ];
  return layerConfigs.map(createLayer);
}
