/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
// FILEOWNER: [DSP General]
import {
  AccessPrivilege,
  IEndPoints,
  IExpressionPermissions,
  IExpressions,
  IFeatureFlagsMap,
  endpointsValidator,
  isNodeDevEnvironment,
} from "@sap/dwc-context-checks";
import { Configurable } from "@sap/dwc-json-patch";
import { Activities, AuthType } from "@sap/dwc-permissions";
import { KeyExtractorTypes, RateLimiterTypes, State } from "@sap/dwc-rate-limiting";
// Runs rate limiters in dry run mode for local dev setups
export const rateLimiterState = isNodeDevEnvironment() ? State.DRYRUN : State.ENABLED;

const expressions: IExpressions = {
  dataBuilderCreateOrUpdate: (ff: IFeatureFlagsMap, p: IExpressionPermissions) =>
    p.DWC_DATABUILDER.create || p.DWC_DATABUILDER.update,
  dataIntegrationOrDataBuilderRead: (ff: IFeatureFlagsMap, p: IExpressionPermissions) =>
    p.DWC_DATAINTEGRATION.read || p.DWC_DATABUILDER.read,
  deploymentEndpointPermissions: (ff: IFeatureFlagsMap, p: IExpressionPermissions) =>
    (p.DWC_DATABUILDER.create && p.DWC_DATABUILDER.update) ||
    (p.DWC_CONSUME_MODEL.create && p.DWC_CONSUME_MODEL.update),
  general: (ff: IFeatureFlagsMap, p: IExpressionPermissions) =>
    p.DWC_GENERAL.read || (ff.DWCO_BDC_COCKPIT && p.BDC_PACKAGES.read),
  generalUpdate: (ff: IFeatureFlagsMap, p: IExpressionPermissions) =>
    p.DWC_GENERAL.update || (ff.DWCO_BDC_COCKPIT && p.BDC_PACKAGES.update),
  isNodeDev: () => isNodeDevEnvironment(),
  isGetOnSpaceHdiAllowed: (ff: IFeatureFlagsMap, p: IExpressionPermissions) =>
    (ff.DWC_DUMMY_SPACE_PERMISSIONS && p.DWC_SPACES.read) ||
    (!ff.DWC_DUMMY_SPACE_PERMISSIONS && p.DWC_REMOTECONNECTION.read),
};

const endpoints: IEndPoints["endpoints"] = {
  "/^\\/[^\\/]*\\.html$/": {
    GET: [],
    responsible: ["D023588"],
  },
  "/assets/*": {
    GET: [],
    responsible: ["D023588"],
    skipCheck: true,
  },
  "/base/*": {
    GET: [],
    responsible: ["D023588"],
    skipCheck: true,
  },
  "/esh-search-ui/*": {
    GET: [],
    responsible: ["D023588"],
    skipCheck: true,
  },
  "/dragonet-ui5-table-editor-2025.13.1/*": {
    GET: [],
    responsible: ["D023588"],
    skipCheck: true,
  },
  "/skyline-core/*": {
    GET: [],
    responsible: ["I051545"],
    skipCheck: true,
  },
  "/skyline-tools/*": {
    GET: [],
    responsible: ["I051545"],
    skipCheck: true,
  },
  "/coastguard-ui-texts/*": {
    GET: [],
    responsible: ["D060348"],
    skipCheck: true,
  },
  "/task-scheduler/*": {
    GET: [],
    responsible: ["I529837"],
    skipCheck: true,
  },
  "/": {
    spacecheck: { filter: true }, // this means that at least one space should have a scoped DWC_GENERAL.read privilege
    GET: "general",
    responsible: ["D071863"],
    skipCheck: true,
  },
  "/api/v1/content": {
    spacecheck: { nameParameter: "space", inSpaceManagement: true },
    GET: [{ authorization: AuthType.DWC_SPACES, activities: Activities.read }],
    PUT: [], // Create, Update and Manage will be checked at the endpoint itself, cannot differentiate between Create and Update in endpointsfilter.ts
    PATCH: [], // Also for "Save only" endpoint we rely on spacePrivilegeChecker.checkSpaceUpdate to ignore Space Capabilities rules
    DELETE: [{ authorization: AuthType.DWC_SPACES, activities: Activities.delete }],
    responsible: ["d023588"],
    usedBy: ["public", "orca/data_marketplace", "orca/dsp_joule_content"],
    rateLimiting: {
      PUT: {
        name: "contentLimitChain",
        state: rateLimiterState,
        rateLimiters: [
          {
            name: "contentPutLimiter",
            type: RateLimiterTypes.REQUEST,
            consumes: 1,
            state: State.DRYRUN,
            options: {
              points: 5, // 5 requests
              duration: 60, // per minute
            },
          },
        ],
      },
    },
  },
  "/api/v1/consumption/catalog*": {
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I864016", "I564455", "I537293", "I559288", "I752767", "I752286"],
    featureFlags: ["DWCO_CONSUMPTION_API_NEW_ROUTES"],
  },
  "/api/v1/spaces": {
    // spacecheck: {filter: true},
    GET: [{ authorization: AuthType.DWC_SPACES, activities: Activities.read }],
    responsible: ["d065291"],
    usedBy: ["public"],
  },
  "/api/v1/scopedroles": {
    GET: [{ authorization: AuthType.PROFILE, activities: Activities.read }],
    POST: [{ authorization: AuthType.PROFILE, activities: Activities.create }],
    responsible: ["i547303"],
    usedBy: ["CLI", "orca/dsp_joule_content"],
  },
  "/api/v1/globalroles": {
    GET: [{ authorization: AuthType.PROFILE, activities: Activities.read }],
    responsible: ["i067629"],
    usedBy: ["CLI"],
  },
  "/api/v1/globalroles/:role/users": {
    POST: [{ authorization: AuthType.PROFILE, activities: Activities.update }],
    GET: [{ authorization: AuthType.PROFILE, activities: Activities.read }],
    responsible: ["i067629"],
    usedBy: ["CLI"],
  },
  "/api/v1/globalroles/:role/users/mass-delete": {
    POST: [{ authorization: AuthType.PROFILE, activities: Activities.update }],
    responsible: ["i067629"],
    usedBy: ["CLI"],
  },
  "/api/v1/scopedroles/:role": {
    GET: [{ authorization: AuthType.PROFILE, activities: Activities.read }],
    PUT: [{ authorization: AuthType.PROFILE, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.PROFILE, activities: Activities.delete }],
    responsible: ["i547303"],
    usedBy: ["CLI"],
  },
  "/api/v1/users": {
    GET: [{ authorization: AuthType.USER, activities: Activities.read }],
    POST: [{ authorization: AuthType.USER, activities: Activities.create }],
    PUT: [{ authorization: AuthType.USER, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.USER, activities: Activities.delete }],
    responsible: ["i062774"],
    usedBy: ["CLI"],
  },
  "/api/v1/configuration/security/certificates": {
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["I565901"],
    usedBy: ["CLI"],
    rateLimiting: {
      GET: {
        name: "CertificatesListLimitAPIChain",
        state: rateLimiterState,
        rateLimiters: [
          {
            name: "certificatesListLimiter",
            type: RateLimiterTypes.REQUEST,
            consumes: 1,
            state: rateLimiterState,
            options: {
              points: 25, // 25 requests
              duration: 60, // per minute
            },
          },
        ],
      },
      POST: {
        name: "certificateCreateLimitAPIChain",
        state: rateLimiterState,
        rateLimiters: [
          {
            name: "certificateCreateLimiter",
            type: RateLimiterTypes.REQUEST,
            consumes: 1,
            state: rateLimiterState,
            options: {
              points: 25, // 25 requests
              duration: 60, // per minute
            },
          },
        ],
      },
    },
    featureFlags: [],
  },
  "/api/v1/configuration/security/certificates/:fingerprint": {
    DELETE: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["I565901"],
    usedBy: ["CLI"],
    rateLimiting: {
      DELETE: {
        name: "certificateDeleteLimitAPIChain",
        state: rateLimiterState,
        rateLimiters: [
          {
            name: "certificateDeleteLimiter",
            type: RateLimiterTypes.REQUEST,
            consumes: 1,
            state: rateLimiterState,
            options: {
              points: 25, // 25 requests
              duration: 60, // per minute
            },
          },
        ],
      },
    },
    featureFlags: [],
  },
  "/api/v1/spaces/:space/connections": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    POST: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.create }],
    responsible: ["i565901"],
    usedBy: ["CLI"],
    rateLimiting: {
      GET: {
        name: "connectionsListLimitAPIChain",
        state: rateLimiterState,
        rateLimiters: [
          {
            name: "connectionListLimiter",
            type: RateLimiterTypes.REQUEST,
            consumes: 1,
            state: rateLimiterState,
            options: {
              points: 25, // 25 requests
              duration: 60, // per minute
            },
          },
        ],
      },
      POST: {
        name: "connectionCreateLimitAPIChain",
        state: rateLimiterState,
        rateLimiters: [
          {
            name: "connectionCreateLimiter",
            type: RateLimiterTypes.REQUEST,
            consumes: 1,
            state: rateLimiterState,
            options: {
              points: 25, // 25 requests
              duration: 60, // per minute
            },
          },
        ],
      },
    },
    featureFlags: [],
  },
  "/api/v1/spaces/:space/connections/:name/validation": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    responsible: ["i565901"],
    usedBy: ["CLI"],
    rateLimiting: {
      GET: {
        name: "connectionStatusLimitAPIChain",
        state: rateLimiterState,
        rateLimiters: [
          {
            name: "connectionStatusLimiter",
            type: RateLimiterTypes.REQUEST,
            consumes: 1,
            state: rateLimiterState,
            options: {
              points: 25, // 25 requests
              duration: 60, // per minute
            },
          },
        ],
      },
    },
    featureFlags: [],
  },
  "/api/v1/spaces/:space/connections/:name": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Write },
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    PUT: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.delete }],
    responsible: ["i565901"],
    usedBy: ["CLI"],
    rateLimiting: {
      GET: {
        name: "connectionGetLimitAPIChain",
        state: rateLimiterState,
        rateLimiters: [
          {
            name: "connectionGetLimiter",
            type: RateLimiterTypes.REQUEST,
            consumes: 1,
            state: rateLimiterState,
            options: {
              points: 25, // 25 requests
              duration: 60, // per minute
            },
          },
        ],
      },
      PUT: {
        name: "connectionUpdateLimitAPIChain",
        state: rateLimiterState,
        rateLimiters: [
          {
            name: "connectionUpdateLimiter",
            type: RateLimiterTypes.REQUEST,
            consumes: 1,
            state: rateLimiterState,
            options: {
              points: 25, // 25 requests
              duration: 60, // per minute
            },
          },
        ],
      },
      DELETE: {
        name: "connectionDeleteLimitAPIChain",
        state: rateLimiterState,
        rateLimiters: [
          {
            name: "connectionDeleteLimiter",
            type: RateLimiterTypes.REQUEST,
            consumes: 1,
            state: rateLimiterState,
            options: {
              points: 25, // 25 requests
              duration: 60, // per minute
            },
          },
        ],
      },
    },
    featureFlags: [],
  },
  "/api/v1/spaces/:space/databaseusers": {
    GET: [{ authorization: AuthType.DWC_SPACES, activities: Activities.read }], // Get all users
    POST: [{ authorization: AuthType.DWC_SPACES, activities: Activities.create }], // Add a user
    PUT: [{ authorization: AuthType.DWC_SPACES, activities: Activities.update }], // Edit a user
    DELETE: [{ authorization: AuthType.DWC_SPACES, activities: Activities.delete }], // Delete a user
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read, inSpaceManagement: true },
    responsible: ["D068830"],
    usedBy: ["CLI", "orca/data_marketplace"],
  },
  "/api/v1/spaces/:space/databaseusers/certificate/distinguishedname": {
    spacecheck: { nameParameter: ":space", inSpaceManagement: true },
    POST: [{ authorization: AuthType.DWC_SPACES, activities: Activities.update }],
    responsible: ["DL Datasphere Foundation - Vancouver <<EMAIL>>"],
    usedBy: ["public"],
    featureFlags: ["DWCO_X509_AUTH_USER"],
  },
  "/api/v1/spaces/:space/databaseusers/:databaseuser/resetpassword": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read, inSpaceManagement: true },
    POST: [], // authorization will be checked in the endpoint implementation, and this call will fallback to the previous endpoint,
    // to ensure retro-compatibility with the old implementation
    responsible: ["D065291"],
    usedBy: ["public"],
  },
  "/api/v1/spaces/:space/users": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACES, activities: Activities.read }],
    PUT: [
      { authorization: AuthType.DWC_SPACES, activities: Activities.update },
      { authorization: AuthType.SCOPEROLEUSERASSIGN, activities: Activities.assign },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACES, activities: Activities.update },
      { authorization: AuthType.SCOPEROLEUSERASSIGN, activities: Activities.assign },
    ],
    responsible: ["d065291"],
    usedBy: ["CLI"],
  },
  "/api/v1/spaces/:spaceid/users/idp-user-id": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_DAC, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
      { authorization: AuthType.DWC_CONSUMPTION, activities: Activities.update },
    ],
    featureFlags: ["DWCO_DAC_USER_IMPERSONATION"],
    responsible: ["I560587"],
    usedBy: ["DAC"],
  },
  "/api/v1/spaces/:space/users/mass-delete": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [
      { authorization: AuthType.DWC_SPACES, activities: Activities.update },
      { authorization: AuthType.SCOPEROLEUSERASSIGN, activities: Activities.assign },
    ],
    responsible: ["d065291"],
    usedBy: ["CLI"],
  },
  "/api/v1/scopedroles/:role/users": {
    GET: [{ authorization: AuthType.PROFILE, activities: Activities.read }],
    responsible: ["i537733"],
    usedBy: ["CLI"],
  },
  "/api/v2/scopedroles/:role/users": {
    POST: [{ authorization: AuthType.PROFILE, activities: Activities.update }],
    responsible: ["i067629"],
    usedBy: ["CLI"],
  },
  "/api/v2/scopedroles/:role/users/mass-delete": {
    POST: [{ authorization: AuthType.PROFILE, activities: Activities.update }],
    responsible: ["i067629"],
    usedBy: ["CLI"],
  },
  "/api/v1/scopedroles/:role/scopes": {
    GET: [{ authorization: AuthType.PROFILE, activities: Activities.read }],
    responsible: ["i537733"],
    usedBy: ["CLI", "orca/dsp_joule_content"],
  },
  "/api/v2/scopedroles/:role/scopes": {
    POST: [{ authorization: AuthType.PROFILE, activities: Activities.update }],
    responsible: ["i067629"],
    usedBy: ["CLI", "orca/dsp_joule_content"],
  },
  "/api/v2/scopedroles/:role/scopes/mass-delete": {
    POST: [{ authorization: AuthType.PROFILE, activities: Activities.update }],
    responsible: ["i067629"],
    usedBy: ["CLI"],
  },
  "/api/v1/discovery": {
    GET: [],
    responsible: ["I322804"],
    featureFlags: [],
  },
  "/api/v1/jobstatus": {
    // No privilege check on purpose, tech user has implicitly full privileges
    GET: [],
    responsible: ["I309440"],
  },
  "/api/v1/connections": {
    PUT: [
      {
        activities: Activities.update,
        authorization: AuthType.DWC_REMOTECONNECTION,
      },
    ],
    responsible: ["D053379"],
    featureFlags: ["DWCO_DW009374_DATA_PRODUCT_POC"],
    usedBy: ["orca/data_marketplace"],
  },
  "/api/v1/tasks/logs/:spaceid/:logid": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_DATAINTEGRATION,
      },
    ],
    responsible: ["I549762"],
    featureFlags: [],
  },
  "/api/v1/tasks/logs/:spaceid": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_DATAINTEGRATION,
      },
    ],
    responsible: ["I549762"],
    featureFlags: [],
  },
  "/api/v1/tasks/chains/:spaceid/run/:objectid": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    POST: [
      {
        authorization: AuthType.DWC_DATAINTEGRATION,
        activities: Activities.update,
      },
    ],
    responsible: ["I549762"],
    featureFlags: [],
  },
  "/api/v1/tasks/consent": {
    POST: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_GENERAL,
      },
    ],
    DELETE: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_GENERAL,
      },
    ],
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_GENERAL,
      },
    ],
    responsible: ["I549762"],
    featureFlags: [],
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(localtables)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(localtables)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/bwpce/data/:spaceid/tables/:tableid/hasdata": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    responsible: ["I537933"],
    featureFlags: ["DWCO_LOCAL_TABLE_FILES_BWPUSH"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/bwpce/data/:spaceid/tables/:tableid/merge": {
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update },
    ],
    responsible: ["D046539", "I544699"],
    featureFlags: ["DWCO_LOCAL_TABLE_FILES_BWPUSH_MERGE"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(remotetables)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(remotetables)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(views)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(views)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(ermodels)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(ermodels)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(contexts)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(contexts)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(types)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(types)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(dataflows)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(dataflows)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(replicationflows)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(replicationflows)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(transformationflows)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(transformationflows)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(taskchains)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(taskchains)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(intelligentlookups)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(intelligentlookups)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(analyticmodels)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(analyticmodels)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(dataaccesscontrols)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DAC, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DAC, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(dataaccesscontrols)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DAC, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DAC, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_DAC, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(businessentities)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_BUSINESS_ENTITY, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_BUSINESS_ENTITY, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(businessentities)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_BUSINESS_ENTITY, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_BUSINESS_ENTITY, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_BUSINESS_ENTITY, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(factmodels)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_FACT_MODEL, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_FACT_MODEL, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(factmodels)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_FACT_MODEL, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_FACT_MODEL, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_FACT_MODEL, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(consumptionmodels)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_CONSUME_MODEL, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_CONSUME_MODEL, activities: Activities.create },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(consumptionmodels)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_CONSUME_MODEL, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_CONSUME_MODEL, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_CONSUME_MODEL, activities: Activities.update },
    ],
    responsible: ["I575730", "I516067"],
    featureFlags: [],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(ontologies)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I575730", "I742471"],
    featureFlags: ["DWCO_GRAPH_REPO_EXPLORER", "DWCO_GRAPH_ONTOLOGY_EDITOR"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(ontologies)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_CONSUME_MODEL, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_CONSUME_MODEL, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_CONSUME_MODEL, activities: Activities.update },
    ],
    responsible: ["I575730", "I742471"],
    featureFlags: ["DWCO_GRAPH_REPO_EXPLORER", "DWCO_GRAPH_ONTOLOGY_EDITOR"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },

  "/api/v1/spaces/:spaceid/:repoobjecttype(services)": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I544481"],
    featureFlags: ["DWCO_INFRA_REPOSITORY_SERVICE_DEFINITION"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/spaces/:spaceid/:repoobjecttype(services)/:objecttechname": {
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_CONSUME_MODEL, activities: Activities.delete },
    ],
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_CONSUME_MODEL, activities: Activities.read },
    ],
    PUT: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
      { authorization: AuthType.DWC_CONSUME_MODEL, activities: Activities.update },
    ],
    responsible: ["I544481"],
    featureFlags: ["DWCO_INFRA_REPOSITORY_SERVICE_DEFINITION"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/api/v1/catalog/dataproducts/install": {
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I063991", "I516067"],
    featureFlags: ["DWCO_BDC_CLI_FOR_DATA_PRODUCTS"],
  },
  "/api/v1/catalog/dataproducts/uninstall": {
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete },
    ],
    responsible: ["I517627", "I063975"],
    featureFlags: ["DWCO_BDC_CLI_FOR_DATA_PRODUCTS"],
  },
  "/api/v1/configuration/system-connections": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    featureFlags: ["DWCO_BDC_CLI_FOR_DATA_PRODUCTS"],
    responsible: ["I063991", "I063975"],
    usedBy: ["CLI"],
  },
  "/api/v1/configuration/system-connections/authorize": {
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    featureFlags: ["DWCO_BDC_CLI_FOR_DATA_PRODUCTS"],
    responsible: ["I077452"],
    usedBy: ["CLI"],
  },
  "/api/v2/jobstatus": {
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    featureFlags: ["DWCO_BDC_CLI_FOR_DATA_PRODUCTS"],
    responsible: ["I348534"],
    usedBy: ["CLI"],
  },
  "/api/v1/workloadmanagement": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    PUT: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["D070561"],
    featureFlags: ["DWCO_WORKLOAD_MANAGEMENT_UI"],
  },
  // Common Data Model (CDM) provider endpoints
  "/api/v2/cdm": {
    GET: [{ authorization: AuthType.DWC_GENERAL, activities: Activities.read }],
    responsible: ["@orca/dw101_crossarchitecture"],
    featureFlags: ["DWCO_BUILD_WZ_INTEGRATION"],
  },
  "/circuitbreaker": {
    GET: [], // No check needed
    responsible: ["I863108", "D071863"],
  },
  "/cds": {
    POST: [],
    responsible: ["I063946"],
  },
  "/cds/parse/expr": {
    POST: [],
    responsible: ["I063946"],
  },
  "/metadataimport/bw/:spaceid/:bw4hybridconnectionid/convert": {
    POST: [],
    responsible: ["D029388"],
  },
  "/metadataimport/bw/:spaceid/:bw4hybridconnectionid/children/:fullqualifiedname": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    responsible: ["D029388"],
  },
  "/metadataimport/bw/:spaceid/:bw4hybridconnectionid/metadata/:fullqualifiedname": {
    spacecheck: { GUIDParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    responsible: ["D029388"],
  },
  "/metadataimport/bw/:spaceid/:bw4hybridconnectionid/search": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    responsible: ["D029388"],
  },
  "/metadataimport/csn/:spaceid/:connectionid/metadata/importobjects": {
    spacecheck: { GUIDParameter: ":spaceid", access: AccessPrivilege.Read },
    POST: [
      {
        authorization: AuthType.DWC_REMOTECONNECTION,
        activities: Activities.read,
      },
      {
        authorization: AuthType.DWC_DATABUILDER,
        activities: Activities.update + Activities.create,
      },
    ],
    responsible: ["I058204"],
  },
  "/metadataimport/csn/:spaceid/:connectionid/metadata/:fullqualifiedname": {
    spacecheck: { GUIDParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    responsible: ["I058204"],
  },
  "/cdssql/buildsql": {
    POST: [], // no check required here as information would need to be retrieved by all users
    responsible: ["I037790"],
  },
  "/cdssql/buildcqn": {
    POST: [], // no check required as this is pure sql to csn conversion no lookup in repo
    responsible: ["I037790"],
  },
  "/customerinfo": {
    POST: [],
    responsible: ["D043947"],
  },
  "/data/:spaceid/tables/:tableid": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    DELETE: [{ authorization: AuthType.DWC_CONSUMPTION, activities: Activities.update }],
    POST: [{ authorization: AuthType.DWC_CONSUMPTION, activities: Activities.update }],
    responsible: ["D043947"],
  },
  "/data/:spaceid/tables/:tableid/hasdata": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["D043947", "D003255"],
  },
  "/datasources/getchildren": {
    spacecheck: { nameParameter: "?space", access: AccessPrivilege.Read },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_REMOTECONNECTION,
      },
    ],
    responsible: ["d027946"],
  },
  "/datasources/searchchildren": {
    spacecheck: { nameParameter: "?space", access: AccessPrivilege.Read },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_REMOTECONNECTION,
      },
    ],
    responsible: ["d027946"],
  },
  "/datasources/getcsndefinition": {
    responsible: ["d027946"],
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_REMOTECONNECTION,
      },
    ],
    usedBy: ["orca/data_marketplace"],
  },
  "/datasources/sdi/adapter/:adaptername/location": {
    responsible: ["d023588", "i819462"],
    GET: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.DWC_REMOTECONNECTION,
      },
    ],
  },
  "/datasources/sdi/location": {
    responsible: ["d023588", "i819462"],
    GET: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.DWC_REMOTECONNECTION,
      },
    ],
  },
  "/connections/drivers/sync": {
    POST: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I553812"],
  },
  "/connections/drivers": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    POST: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I553812"],
  },
  "/connections/drivers/:fingerprint": {
    DELETE: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I553812"],
  },
  "/connections/drivers/fingerprints": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I553812"],
  },
  "/connections/lsacertificaterotation": {
    POST: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["D029701"],
    featureFlags: [],
  },
  "/connections/uclcertificaterotation": {
    POST: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["D029701"],
  },
  "/connections/ucl/:technical_name": {
    DELETE: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I531495"],
    featureFlags: [],
  },
  "/api/v1/tenantinformation/signavio": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_DATAINTEGRATION,
      },
    ],
    responsible: ["D029701"],
    featureFlags: ["DWCO_CONNECTION_SIGNAVIO_MAPPING"],
  },
  "/dpagent": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I537933"],
  },
  "/dpagent/:agentname": {
    DELETE: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    PATCH: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    POST: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I537933"],
  },
  "/dpagent/:agentname/adapters": {
    DELETE: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    POST: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I537933"],
  },
  "/dpagent/:agentname/refreshadapters": {
    POST: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I537933"],
  },
  "/dpagent/:agentname/pauseconnections": {
    POST: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I537933"],
    featureFlags: [],
  },
  "/dpagent/:agentname/restartconnections": {
    POST: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I537933"],
    featureFlags: [],
  },
  "/dpagent/:agentname/setupdpagenttracelogfilereader": {
    POST: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I537933"],
  },
  "/dpagent/:agentname/deactivatedpagenttracelogfilereader": {
    POST: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I537933"],
  },
  "/dpagent/:agentname/getdpagenttracelogfilereader": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I537933"],
  },
  "/dpagent/:agentname/getdpagenttracelogfilereader/properties": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I537933"],
  },
  "/dpagent/:agentname/setupdpagentconnectionstatusmonitoring": {
    POST: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I537933"],
    featureFlags: [],
  },
  "/dpagent/notification/:scheduleid/ownerchange": {
    PUT: [
      {
        activities: Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["i546422"],
    featureFlags: [],
  },
  "/dpagent/:agentname/getdpagentconnectionstatusmonitoringactivation": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I537933"],
    featureFlags: [],
  },
  "/dpagent/:agentname/deactivatedpagentconnectionstatusmonitoring": {
    DELETE: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I537933"],
    featureFlags: [],
  },
  "/dpagent/:agentname/adapters/:adaptername/refresh": {
    POST: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I537933"],
  },
  "/support/tenant/tf/systemschedules/status/:pauseorresume": {
    // protected by special token
    PUT: [],
    responsible: ["I517217"],
    featureFlags: [],
  },
  "/support/tenant/tf/systemschedules/edit": {
    // protected by special token
    PUT: [],
    responsible: ["I517217"],
    featureFlags: [],
  },
  "/tf/admin/logs/cleanup": {
    PUT: [
      {
        activities: Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I546422", "I549762"],
    featureFlags: ["DWC_INFRA_STORAGE_DATA_UI"],
  },
  "/tf/admin/logs/updatecleanupschedule": {
    PUT: [
      {
        activities: Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I549762"],
    featureFlags: [],
  },
  "/tf/admin/logs/getcleanupschedule": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I549762"],
    featureFlags: [],
  },
  "/tf/admin/logs/usage": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I546422", "I549762"],
    featureFlags: ["DWC_INFRA_STORAGE_DATA_UI"],
  },
  "/tf/tenant/start": {
    PUT: [
      {
        activities: Activities.update,
        authorization: AuthType.DWC_DATAINTEGRATION,
      },
      {
        activities: Activities.update,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I546422"],
    featureFlags: [],
  },
  "/tf/tenant/stop": {
    PUT: [
      {
        activities: Activities.update,
        authorization: AuthType.DWC_DATAINTEGRATION,
      },
      {
        activities: Activities.update,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I546422"],
    featureFlags: [],
  },
  "/tf/tenant/runningtasks": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_DATAINTEGRATION,
      },
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I546422"],
    featureFlags: [],
  },
  "/tf/execute": {
    POST: [], // permission check in the task being executed
    responsible: ["I546422", "I549762"],
    featureFlags: [],
  },
  "/tf/technicaluser/execute": {
    POST: [], // permission check in the task being executed
    responsible: ["I549762"],
    featureFlags: [],
  },
  "/tf/directexecute": {
    spacecheck: {
      nameParameter: "spaceId",
      access: AccessPrivilege.Read,
    },
    POST: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I546422", "I549762"],
    featureFlags: [],
  },
  "/tf/tasks/cancellable": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_GENERAL,
      },
    ],
    responsible: ["I546422"],
  },
  "/tf/tasks/spark": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_GENERAL,
      },
    ],
    responsible: ["I517217"],
  },
  "/tf/cancelexecute": {
    POST: [], // permission check in the task being executed
    responsible: ["I546422"],
  },
  "/tf/global/directexecute": {
    POST: [], // permission check in the task being executed
    responsible: ["I546422", "I549762"],
    featureFlags: ["DWCO_TF_ODC_TASKS"],
  },
  "/tf/technicalexecute": {
    POST: [], // technical token only
    responsible: ["I546422", "I549762"],
    featureFlags: [],
  },
  "/tf/nodedev/execute": {
    POST: "isNodeDev",
    responsible: ["I546422"],
    featureFlags: [],
  },
  "/tf/nodedev/technicalexecute": {
    POST: "isNodeDev",
    responsible: ["I546422"],
    featureFlags: [],
  },
  "/tf/createinstantjob": {
    POST: [], // technical token only
    responsible: ["I517217"],
    featureFlags: [],
  },
  "/tf/schedules/consent": {
    PUT: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_GENERAL,
      },
    ],
    DELETE: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_GENERAL,
      },
    ],
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_GENERAL,
      },
    ],
    responsible: ["I546422", "I549762"],
    featureFlags: [],
  },
  "/tf/schedules/consent/expirationlist": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I517217"],
  },
  "/tf/schedules/nextruns": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_GENERAL,
      },
    ],
    responsible: ["I546422"],
    featureFlags: [],
  },
  "/tf/:spaceid/logs": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        authorization: AuthType.DWC_SPACEFILE,
        activities: Activities.read,
      },
    ],
    responsible: ["I546422", "I549762"],
    featureFlags: [],
    usedBy: ["orca/data_marketplace"],
  },
  "/tf/:spaceid/logs/:logid": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I546422"],
    featureFlags: [],
    usedBy: ["orca/data_marketplace"],
  },
  "/tf/:spaceid/extendedlogs/:logid": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I549762"],
    featureFlags: [],
  },
  "/tf/global/:globalspaceid/logs": {
    // spacecheck is not required here as it is done inside at the node level.
    GET: [],
    responsible: ["I517217"],
    featureFlags: [], // FF check added inside getValidSpaceId() in service\task\logger\controllers\logController.ts [DWCO_TF_ODC_TASKS]
  },
  "/tf/global/:globalspaceid/logs/:logid": {
    // spacecheck is not required here as it is done inside at the node level.
    GET: [],
    responsible: ["I517217"],
    featureFlags: [], // FF check added inside getValidSpaceId() in service\task\logger\controllers\logController.ts [DWCO_TF_ODC_TASKS]
  },
  "/tf/:spaceid/logs/:logid/messages/:messageno/blobcontent": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I517217"],
  },
  "/tf/:spaceid/logs/:logid/messages/:messageno/jsondata": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I517217"],
    featureFlags: [],
  },
  "/tf/:spaceid/setfailed/:logid": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    PUT: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I546422", "I549762"],
    featureFlags: [],
  },
  "/tf/:spaceid/schedules": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    POST: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I546422", "I549762"],
    featureFlags: [],
  },
  "/tf/:spaceid/schedules/:scheduleid": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    DELETE: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    PUT: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I546422", "I549762"],
    featureFlags: [],
  },
  "/tf/:spaceid/schedules/list/:operation": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    POST: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    PUT: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I517217", "I549762"],
    featureFlags: [],
  },
  "/tf/:spaceid/schedules/:scheduleid/ownerchange": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    PUT: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I546422", "I549762"],
    featureFlags: [],
  },
  "/tf/:spaceid/:objectid/chainable": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I546422", "I549762"],
    featureFlags: [],
  },
  "/tf/:spaceid/chainabletasks": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I549762"],
    featureFlags: ["DWCO_TASK_CHAINS_REMOVE_DATA"],
  },
  "/tf/:spaceid/:objectid/chainparameters": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_SPACEFILE,
      },
    ],
    responsible: ["I549762"],
    featureFlags: ["DWCO_TASK_FRAMEWORK_CHAIN_PARAMETERS"],
  },
  "/monitor/:spaceid/taskchains": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_DATAINTEGRATION,
      },
    ],
    responsible: ["I546422", "I549762"],
    featureFlags: [],
  },
  "/tf/:spaceid/taskchains/:logid": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_DATAINTEGRATION,
      },
    ],
    responsible: ["I546422", "I549762"],
    featureFlags: [],
  },
  "/tf/:spaceid/taskchains/:chainname/info": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_DATAINTEGRATION,
      },
    ],
    responsible: ["I546422", "I549762"],
    featureFlags: [],
  },
  "/tf/:spaceid/taskchains/:chainname/start": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    POST: [
      {
        authorization: AuthType.DWC_DATAINTEGRATION,
        activities: Activities.update,
      },
    ],
    responsible: ["I546422", "I549762"],
    featureFlags: [],
  },
  "/tf/:spaceid/taskchains/:objectid/updatemailinglist": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    PUT: [
      {
        authorization: AuthType.DWC_DATAINTEGRATION,
        activities: Activities.update,
      },
    ],
    responsible: ["I549762"],
    featureFlags: [],
  },
  "/tf/:spaceid/taskchains/:objectid/getmailinglist": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        authorization: AuthType.DWC_DATAINTEGRATION,
        activities: Activities.read,
      },
    ],
    responsible: ["I549762"],
    featureFlags: [],
  },
  "/tf/:spaceid/taskchains/:chainname/retry": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    POST: [
      {
        authorization: AuthType.DWC_DATAINTEGRATION,
        activities: Activities.update,
      },
    ],
    responsible: ["I517217", "I549762"],
    featureFlags: [],
  },
  "/tf/:spaceid/taskchains/non-repository/artefact/types": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_DATABUILDER,
      },
    ],
    responsible: ["I520900"],
  },
  "/tf/:spaceid/taskchains/non-repository/artefact/:type/tree": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_DATABUILDER,
      },
    ],
    responsible: ["I520900"],
  },
  "/tf/:spaceid/taskchains/connections/http": {
    spacecheck: {
      nameParameter: ":spaceid",
      access: AccessPrivilege.Read,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_DATABUILDER,
      },
      {
        activities: Activities.read,
        authorization: AuthType.DWC_REMOTECONNECTION,
      },
    ],
    responsible: ["I520900"],
    featureFlags: ["DWCO_INFRA_TASKS_API_TASK"],
  },
  "/errortraceconfig": {
    GET: [],
    responsible: ["D043947"],
  },
  "/service-registry": {
    GET: [],
    responsible: ["I050650"],
  },
  "/featureflags": {
    GET: [],
    responsible: ["D043947"],
    usedBy: ["orca/data_marketplace"],
  },
  "/feedback/usage": {
    POST: [],
    responsible: ["D043947"],
  },
  "/health": {
    GET: [],
    responsible: ["D043947", "I046287"],
    rateLimiting: {
      name: "health",
      state: rateLimiterState,
      extends: {}, // Don't use default limiting chain
      rateLimiters: [
        {
          name: "healthLimiter",
          type: RateLimiterTypes.REQUEST,
          consumes: 1,
          options: {
            points: 5, // 5 requests
            duration: 60, // per minute
          },
          keyExtractor: {
            type: KeyExtractorTypes.STATIC,
            key: "health",
          },
        },
      ],
    },
  },
  "/healthcf": {
    GET: [],
    responsible: ["D043824"],
    nonPatchable: true,
  },
  "/healthtenants/:tenantid?": {
    GET: [], // Special technical user token check exists
    responsible: ["D043947"],
    rateLimiting: {
      name: "healthTenants",
      state: rateLimiterState,
      extends: {}, // Don't use default limiting chain
      rateLimiters: [
        {
          name: "healthTenantsLimiter",
          type: RateLimiterTypes.REQUEST,
          consumes: 1,
          options: {
            points: 5, // 5 requests
            duration: 60, // per minute
          },
          keyExtractor: {
            type: KeyExtractorTypes.PATH,
          },
        },
      ],
    },
  },
  "/bdc/health": {
    skipCheck: true, // only exposed in sidecar
    GET: [],
    responsible: ["I864016", "D043824"],
    featureFlags: [], // Will be procted by a env var on sidecar
    rateLimiting: {
      name: "bdcHealth",
      state: rateLimiterState,
      extends: {}, // Don't use default limiting chain
      rateLimiters: [
        {
          name: "bdcHealthLimiter",
          type: RateLimiterTypes.REQUEST,
          consumes: 1,
          options: {
            points: 5, // 5 requests
            duration: 60, // per minute
          },
          keyExtractor: {
            type: KeyExtractorTypes.STATIC,
            key: "bdcHealth",
          },
        },
      ],
    },
  },
  "/bdc/healthtenants/:tenantid?": {
    skipCheck: true, // only exposed in sidecar
    GET: [], // Special technical user token check exists
    responsible: ["I864016", "D043824"],
    featureFlags: [], // Will be procted by a env var on sidecar
    rateLimiting: {
      name: "bdcHealthTenants",
      state: rateLimiterState,
      extends: {}, // Don't use default limiting chain
      rateLimiters: [
        {
          name: "bdcHealthTenantsLimiter",
          type: RateLimiterTypes.REQUEST,
          consumes: 1,
          options: {
            points: 5, // 5 requests
            duration: 60, // per minute
          },
          keyExtractor: {
            type: KeyExtractorTypes.PATH,
          },
        },
      ],
    },
  },
  "/metamodel/:space/preview-object/:entity": {
    spacecheck: { GUIDParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["D071863"],
    usedBy: ["orca/data_marketplace"],
  },
  "/odata/v4/catalog*": {
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I864016", "I564455", "I537293"],
  },
  "/odata/v4/consumption/analytical/:space/:entity/:id(*)/set": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I864016", "I564455", "I537293"],
  },
  "/odata/v4/consumption/analytical/:space/:entity/:id/\\$count": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I864016", "I564455", "I537293"],
  },
  "/odata/v4/consumption/analytical/:space/:entity/:id": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }], // Only read access is required as only GET operations are required in the $batch
    responsible: ["I864016", "I564455", "I537293"],
  },
  "/odata/v4/consumption/analytical/:space/:entity": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I864016", "I564455", "I537293"],
  },
  "/odata/v4/consumption/relational/:space/:entity/:id(*)/set": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I864016", "I564455", "I537293"],
  },
  "/odata/v4/consumption/relational/:space/:entity/:id/\\$count": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I864016", "I564455", "I537293"],
  },
  "/odata/v4/consumption/relational/:space/:entity/:id": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }], // Only read access is required as only GET operations are required in the $batch
    responsible: ["I864016", "I564455", "I537293"],
  },
  "/odata/v4/consumption/relational/:space/:entity": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I864016", "I564455", "I537293"],
  },
  "/api/v1/consumption/analytical/:space/:entity/:id(*)/set": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I864016", "I564455", "I537293", "I559288"],
    featureFlags: ["DWCO_CONSUMPTION_API_NEW_ROUTES"],
  },
  "/api/v1/consumption/analytical/:space/:entity/:id/\\$count": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I864016", "I564455", "I537293", "I559288"],
    featureFlags: ["DWCO_CONSUMPTION_API_NEW_ROUTES"],
  },
  "/api/v1/consumption/analytical/:space/:entity/:id": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }], // Only read access is required as only GET operations are required in the $batch
    responsible: ["I864016", "I564455", "I537293", "I559288"],
    featureFlags: ["DWCO_CONSUMPTION_API_NEW_ROUTES"],
  },
  "/api/v1/consumption/analytical/:space/:entity": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I864016", "I564455", "I537293", "I559288"],
    featureFlags: ["DWCO_CONSUMPTION_API_NEW_ROUTES"],
  },
  "/api/v1/consumption/relational/:space/:entity/:id(*)/set": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I864016", "I564455", "I537293", "I559288"],
    featureFlags: ["DWCO_CONSUMPTION_API_NEW_ROUTES"],
  },
  "/api/v1/consumption/relational/:space/:entity/:id/\\$count": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I864016", "I564455", "I537293", "I559288"],
    featureFlags: ["DWCO_CONSUMPTION_API_NEW_ROUTES"],
  },
  "/api/v1/consumption/relational/:space/:entity/:id": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }], // Only read access is required as only GET operations are required in the $batch
    responsible: ["I864016", "I564455", "I537293", "I559288"],
    featureFlags: ["DWCO_CONSUMPTION_API_NEW_ROUTES"],
  },
  "/api/v1/consumption/relational/:space/:entity": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I864016", "I564455", "I537293", "I559288"],
    featureFlags: ["DWCO_CONSUMPTION_API_NEW_ROUTES"],
  },
  "/nlq/metadata": {
    POST: [],
    responsible: ["I537293", "I864016", "I564455"],
    featureFlags: ["DWCO_SAC_JUSTASK_NLQ_METADATA"],
    technical: {
      oAuthScopes: ["$XSAPPNAME.JustAskNlq"],
    },
  },
  "/data-access/*": {
    GET: [],
    POST: [],
    responsible: ["I309649", "I037471", "D043947", "I324893"],
  },

  "/monitoring/:entity/*": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["I343950"],
    featureFlags: [],
  },
  "/monitoring/mdsstatistics/hasdata": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["I551803"],
  },
  "/monitoring/mdsstatistics/data": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["I551803"],
  },
  "/monitoring/ecn/avg-resource-utilisation": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["I551803"],
    featureFlags: [],
  },
  "/monitoring/ecn/fetch-run-details": {
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["I524923", "D066459"],
    featureFlags: [],
  },
  "/monitoring/ecn/memory-utilisation": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["I551803"],
    featureFlags: [],
  },
  "/monitoring/capacity-monitoring": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["I747296"],
    featureFlags: ["DWCO_CAPACITY_CONSUMPTION"],
  },
  "/monitoring/capacity-monitoring/download": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["I852708"],
    featureFlags: ["DWCO_CAPACITY_CONSUMPTION"],
  },
  "/monitoring/hdlf/usage": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["D066459"],
    featureFlags: ["DWCO_LSA_RESOURCE_MONITOR"],
  },
  "/monitoring/spark/config/:appindex": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["D066459"],
    featureFlags: ["DWCO_LSA_RESOURCE_MONITOR"],
  },
  "/monitoring/spark/usage/spaces/:spaceid": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["D066459"],
    featureFlags: ["DWCO_LSA_RESOURCE_MONITOR"],
  },
  "/metamodel/:space/preview-csn": {
    spacecheck: { GUIDParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["D071863", "D059337"],
  },
  "/metamodel/:space/validate-csn": {
    spacecheck: { GUIDParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.update }],
    responsible: ["I328544"],
    featureFlags: [],
  },
  "/metamodel/:space/checksqlsyntax": {
    spacecheck: { GUIDParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.update }],
    responsible: ["D037121"],
  },
  "/metamodel/:space/search/*": {
    spacecheck: { GUIDParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I058204"],
  },
  "/deploy": {
    spacecheck: { GUIDParameter: "folderGuid", access: AccessPrivilege.Write },
    POST: "deploymentEndpointPermissions",
    responsible: ["D066022"],
    offFeatureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
    usedBy: ["orca/data_marketplace"],
  },
  "/:spaceid/deploy": {
    // deprecated because it causes issues for space names e.g. base
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Write },
    POST: "deploymentEndpointPermissions",
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
    responsible: ["D066022"],
    usedBy: ["orca/data_marketplace"],
  },
  "/deploy/:spaceid/base": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Write },
    POST: "deploymentEndpointPermissions",
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
    responsible: ["D066022"],
    usedBy: ["orca/data_marketplace"],
  },
  "/deployobjects": {
    spacecheck: { GUIDParameter: "folderGuid", access: AccessPrivilege.Write },
    POST: "deploymentEndpointPermissions",
    responsible: ["I544481"],
    offFeatureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/deployobjectswithcallback": {
    // No privilege check on purpose, tech user has implicitly full privileges
    POST: [],
    responsible: ["I544481"],
    usedBy: ["orca/data_marketplace"],
  },
  "/:spaceid/deployobjects": {
    // deprecated because it causes issues for space names e.g. base
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Write },
    POST: "deploymentEndpointPermissions",
    responsible: ["D066014", "I544481"],
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/deploy/:spaceid/objects": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Write },
    POST: "deploymentEndpointPermissions",
    responsible: ["D066014", "I544481"],
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/deploy/data": {
    spacecheck: { GUIDParameter: "folderGuid", access: AccessPrivilege.Write },
    POST: [
      {
        authorization: AuthType.DWC_DATABUILDER,
        activities: Activities.create + Activities.update,
      },
      {
        authorization: AuthType.DWC_CONSUMPTION,
        activities: Activities.update,
      },
    ],
    responsible: ["D066022"],
    offFeatureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/:spaceid/deploy/data": {
    // deprecated because it causes issues for space names e.g. base
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Write },
    POST: [
      {
        authorization: AuthType.DWC_DATABUILDER,
        activities: Activities.create + Activities.update,
      },
    ],
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
    responsible: ["D066022"],
  },
  "/deploy/:spaceid/data": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Write },
    POST: [
      {
        authorization: AuthType.DWC_DATABUILDER,
        activities: Activities.create + Activities.update,
      },
      {
        authorization: AuthType.DWC_CONSUMPTION,
        activities: Activities.update,
      },
    ],
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
    responsible: ["D066022"],
  },
  "/monitor/:space/persistedviews": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }], // { authorization: AuthType.DWC_MONITOR, activities: Activities.read }
    responsible: ["D074254"],
    featureFlags: [],
  },
  "/monitor/:space/persistedviews/:viewname": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }], // { authorization: AuthType.DWC_MONITOR, activities: Activities.read }
    responsible: ["D074254"],
    featureFlags: [],
  },
  "/monitor/:space/persistedviewssettings/:viewname": {
    spacecheck: {
      nameParameter: ":space",
      access: AccessPrivilege.Read,
    },
    PUT: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D040293"],
  },
  "/monitor/:space/remotetables/dataaccessstatus": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["D059849", "D046539"],
  },
  "/monitor/:space/remotetables/bwbridgeurl": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I300089"],
  },
  "/monitor/:space/remotetables": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }], // { authorization: AuthType.DWC_MONITOR, activities: Activities.read }
    responsible: ["D059849", "I816602"],
  },
  "/monitor/:space/remotetables/:tablename": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }], // { authorization: AuthType.DWC_MONITOR, activities: Activities.read }
    responsible: ["D059849", "I816602"],
  },
  "/monitor/:space/remotetables/:tablename/sharedreplica": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I537933", "I816602"],
    featureFlags: [],
  },
  "/monitor/:space/remotetables/processerror/:processtype/:exceptionid": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D046539", "I816602"],
  },
  "/monitor/:space/localtables": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["D027946"],
  },
  "/monitor/:space/localtables/:tablename": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["D027946"],
  },
  "/monitor/:space/localtables/:tablename/columns": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["D027946"],
  },
  "/monitor/:space/localtables/:tablename/estimatesize": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I345794"],
  },
  "/monitor/:space/localtables/:tablename/schedules": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read },
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update },
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.create },
    ],
    responsible: ["D027946"],
  },
  "/monitor/:space/localtables/:tablename/schedules/:scheduleid": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    DELETE: [
      { authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update },
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
    ],
    PUT: [
      { authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update },
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.update },
    ],
    GET: [
      { authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read },
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
    ],
    responsible: ["D027946"],
  },
  "/monitor/:space/localtables/:tablename/settings": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["I585524"],
    featureFlags: ["DWCO_LARGE_SYSTEMS_SPARK_SELECTION"],
  },
  "/monitor/:space/localtables/:tablename/logs": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["D027946"],
  },
  "/monitor/:space/localtables/:tablename/deletionlogs": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["D027946"],
  },
  "/monitor/:space/localtables/:tablename/lowwatermark": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["D027946"],
  },
  "/monitor/:space/remotequeries": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }], // { authorization: AuthType.DWC_MONITOR, activities: Activities.read }
    responsible: ["D027946"],
  },
  "/tenant/links": {
    spacecheck: { filter: true },
    GET: "general",
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read + Activities.update }],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
    responsible: ["D069743", "I513063"],
  },
  "/space/:spacename/check": {
    spacecheck: { nameParameter: ":spacename", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["D023588"],
  },
  "/space/hdi/containers": {
    spacecheck: { filter: true },
    GET: "isGetOnSpaceHdiAllowed",
    responsible: ["I024090"],
  },
  "/space/hdi/mapping": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    PATCH: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    featureFlags: ["DWCO_SPACES_HDI_MAPPING"],
    responsible: ["I024090"],
  },
  "/navigation/menu": {
    GET: [],
    responsible: ["D043947"],
  },
  "/newsfeed": {
    GET: [],
    responsible: ["D043947"],
  },
  "/notifications": {
    spacecheck: { filter: true },
    GET: "general",
    POST: "general",
    PUT: "general",
    DELETE: "general",
    responsible: ["D043947"],
  },
  "/notifications/online": {
    spacecheck: { filter: true },
    POST: "general",
    responsible: ["D043824"],
  },
  "/notifications/update": {
    spacecheck: { filter: true },
    POST: "generalUpdate",
    responsible: ["D043824"],
  },
  "/notifications/count": {
    spacecheck: { filter: true },
    GET: "general",
    responsible: ["D043947"],
  },
  "/notifications/bns": {
    spacecheck: { filter: true },
    GET: "general",
    responsible: ["D043824"],
  },
  "/persistence/:space/mass/persistedviews/stop": {
    spacecheck: {
      nameParameter: ":space",
      access: AccessPrivilege.Read,
    },
    PUT: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D040293"],
  },
  "/replication/:space/remotetables/:tablename": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["D059849", "D046539"],
  },
  "/replication/:space/remotetables/:tablename/start": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D059849", "D046539"],
    usedBy: ["orca/data_marketplace"],
  },
  "/replication/:space/remotetables/:tablename/stop": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D059849", "D046539"],
    usedBy: ["orca/data_marketplace"],
  },
  "/replication/:space/remotetables/:tablename/disablerealtime": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["I816602", "D046539"],
  },
  "/replication/:space/mass/remotetables/stop": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D046539", "I816602"],
  },
  "/replication/:space/mass/remotetables/disablerealtime": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["I816602", "D046539"],
  },
  "/replication/:space/remotetables/:tablename/cancel": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D046539", "I816602"],
  },
  "/replication/:space/remotetables/:tablename/retry": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["I537933", "I816602"],
    featureFlags: [],
  },
  "/statistics/:space/remotetables": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }], // { authorization: AuthType.DWC_MONITOR, activities: Activities.read }
    responsible: ["D027946", "D046539"],
  },
  "/statistics/:space/remotetables/:tablename": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    PUT: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D027946", "D046539"],
  },
  "/statistics/:space/remotetables/:tablename/refresh": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D027946", "D046539"],
  },
  "/statistics/:space/mass/remotetables/delete": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D027946", "D046539"],
  },
  "/partitioning/:space/remotetables/:tablename": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D027946", "D059849"],
  },
  "/partitioning/:space/persistedviews/:viewname": {
    spacecheck: {
      nameParameter: ":space",
      access: AccessPrivilege.Read,
    },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D074254"],
  },
  "/batchprocessing/:space/transformationflow/:flowname": {
    spacecheck: {
      nameParameter: ":space",
      access: AccessPrivilege.Read,
    },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    PUT: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    featureFlags: ["DWCO_TRF_BATCHES"],
    responsible: ["D040293"],
  },
  "/repository/allusers": {
    GET: [], // Will be checked in the endpoint
    responsible: ["D043947", "I517627"],
    skipCheck: true,
    usedBy: ["orca/dsp_joule_content"],
  },
  "/repository/candeleteelement": {
    spacecheck: { filter: true },
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    responsible: ["I309440"],
    skipCheck: true,
  },
  "/repository/contentlib/v1/:dwctypes/:id": {
    // No privilege check on purpose, tech user has implicitly full privileges
    DELETE: [],
    PATCH: [],
    responsible: ["I309440"],
    skipCheck: true,
  },
  "/repository/contentlib/v1/:dwctypes/:id/copy": {
    // No privilege check on purpose, tech user has implicitly full privileges
    POST: [],
    responsible: ["I309440"],
    skipCheck: true,
  },
  "/repository/consistency": {
    GET: [
      { authorization: AuthType.DWC_SPACES, activities: Activities.assign },
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.assign },
    ],
    responsible: ["I318411"],
    skipCheck: true,
  },
  "/repository/currentuser": {
    GET: [], // No specific privilege required
    responsible: ["I063949"],
    skipCheck: true,
  },
  "/repository/dependencies": {
    spacecheck: { filter: true },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I063991", "I517627"],
    skipCheck: true,
  },
  "/repository/:spaceid/dependencies": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I063975", "I517627"],
  },
  "/repository/deployedobjects": {
    spacecheck: { filter: true },
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_CONSUMPTION, activities: Activities.read },
    ],
    responsible: ["I063975", "I517627"],
    skipCheck: true,
  },
  "/repository/:spaceid/deployedobjects": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_CONSUMPTION, activities: Activities.read },
    ],
    responsible: ["I063975", "I517627", "I318411"],
    skipCheck: true,
  },
  "/repository/designobjects": {
    spacecheck: { filter: true },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I063949", "I517627"],
    skipCheck: true,
  },
  "/repository/:spaceid/designobjects": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I063949", "I318411", "I517627"],
    skipCheck: true,
  },
  "/repository/version": {
    spacecheck: { filter: true },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.update }],
    responsible: ["I063975", "I517627"],
    skipCheck: true,
  },
  "/repository/:spaceid/version": {
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    responsible: ["I063975", "I517627"],
    skipCheck: true,
  },
  "/repository/versions": {
    spacecheck: { filter: true },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I077452"],
  },
  "/repository/:spaceid/versions": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I077452"],
  },
  "/repository/information": {
    GET: [], // will be checked in the endpoint
    featureFlags: ["DWCO_INFRA_SDPCONVERSION"],
    responsible: ["I517627"],
    skipCheck: true,
  },
  "/support/tenant/supportuserscopeassignment": {
    GET: [],
    responsible: ["@orca/dw101_crossarchitecture"],
  },
  "/support/tenant/repository/information": {
    POST: [], // protected by special token
    featureFlags: ["DWCO_INFRA_SDPCONVERSION"],
    responsible: ["I517627"],
    skipCheck: true,
  },
  "/support/hana/configuration/selectstatistic": {
    POST: [],
    responsible: ["I816602"],
    skipCheck: true,
  },
  "/support/repository/healthmetadata": {
    // protected by special token
    GET: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/support/repository/tenants": {
    // protected by special token
    GET: [],
    responsible: ["I575730"],
    skipCheck: true,
  },
  "/support/repository/tenants/:id": {
    // protected by special token
    GET: [],
    responsible: ["I575730"],
    skipCheck: true,
  },
  "/support/repository/jobs": {
    // protected by special token
    GET: [],
    responsible: ["I050650"],
    skipCheck: true,
  },
  "/support/repository/jobs/:id": {
    // protected by special token
    GET: [],
    responsible: ["I050650"],
    skipCheck: true,
  },
  "/support/repository/broadcast": {
    // protected by special token
    POST: [],
    responsible: ["I050650"],
    skipCheck: true,
  },
  "/support/repository/tenantregistry": {
    // protected by special token
    POST: [],
    responsible: ["I050650"],
    skipCheck: true,
  },
  "/support/repository/tenantregistry/entries/:id": {
    // protected by special token
    PUT: [],
    DELETE: [],
    responsible: ["I050650"],
    skipCheck: true,
  },
  "/support/tenant/repository/jobs": {
    // protected by special token
    GET: [],
    responsible: ["I050650"],
    skipCheck: true,
  },
  "/support/tenant/repository/jobs/:id": {
    // protected by special token
    GET: [],
    responsible: ["I050650"],
    skipCheck: true,
  },
  "/support/tenant/repository/operations/enterMaintenanceMode": {
    // protected by special token
    POST: [],
    responsible: ["I050650"],
    skipCheck: true,
  },
  "/support/tenant/repository/operations/exitMaintenanceMode": {
    // protected by special token
    POST: [],
    responsible: ["I050650"],
    skipCheck: true,
  },
  "/support/deepsea/profile/memory/heapprofile": {
    GET: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea-daemon/profile/memory/heapprofile": {
    GET: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea/profile/memory/heapsnapshot": {
    GET: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea-daemon/profile/memory/heapsnapshot": {
    GET: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea/profile/memory/heapstatistics": {
    // protected by special token
    GET: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea-daemon/profile/memory/heapstatistics": {
    // protected by special token
    GET: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea/profile/memory/heapspacestatistics": {
    // protected by special token
    GET: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea-daemon/profile/memory/heapspacestatistics": {
    // protected by special token
    GET: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea/profile/memory/gctracesenable": {
    // protected by special token
    POST: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea-daemon/profile/memory/gctracesenable": {
    // protected by special token
    POST: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea/profile/memory/gctracesdisable": {
    // protected by special token
    POST: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea-daemon/profile/memory/gctracesdisable": {
    // protected by special token
    POST: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea/profile/cpu": {
    // protected by special token
    GET: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea-daemon/profile/cpu": {
    // protected by special token
    GET: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea/profile/eventloopblockers": {
    // protected by special token
    GET: [],
    DELETE: [],
    POST: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/deepsea-daemon/profile/eventloopblockers": {
    // protected by special token
    GET: [],
    DELETE: [],
    POST: [],
    responsible: ["I532686"],
    skipCheck: true,
  },
  "/support/repository/multidb": {
    // protected by special token
    POST: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/support/repository/databases": {
    // protected by special token
    GET: [],
    responsible: ["I575730"],
    skipCheck: true,
  },
  "/support/repository/databases/:id": {
    // protected by special token
    GET: [],
    PATCH: [],
    responsible: ["I575730"],
    skipCheck: true,
  },
  "/support/repository/namespaces": {
    // protected by special token, upsert namespaces for tanants
    POST: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/support/tenant/repository/namespaces": {
    // protected by special token, upsert namespace for a single tenant
    featureFlags: ["DWCO_MODELING_ALLOW_DOTS"],
    GET: [],
    POST: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/support/tenant/repository/namespaces/:namespace": {
    // protected by special token, upsert namespace for a single tenant
    featureFlags: ["DWCO_MODELING_ALLOW_DOTS"],
    DELETE: [],
    PUT: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/support/repository/syncdbconfig": {
    // protected by special token
    POST: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/support/repository/alltenantsinformation": {
    // protected by special token
    GET: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/repository/dac": {
    spacecheck: { filter: true },
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DAC, activities: Activities.read },
    ],
    responsible: ["d023588"],
    skipCheck: true,
  },
  "/repository/:spaceid/dac": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DAC, activities: Activities.read },
    ],
    responsible: ["d023588"],
    skipCheck: true,
  },
  "/repository/documents": {
    spacecheck: { filter: true },
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I063991"],
    skipCheck: true,
  },
  "/repository/:spaceid/documents": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.create },
    ],
    responsible: ["I063991", "I318411"],
    skipCheck: true,
  },
  "/repository/document": {
    spacecheck: { filter: true },
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    responsible: ["I063991", "I517627"],
    skipCheck: true,
  },
  "/repository/:spaceid/processdocument": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }], // not modifying any object in the repository but just converts an input CSN into a JSON output, reason why we check only Read
    responsible: ["I063991", "I517627"],
    skipCheck: true,
  },
  "/repository/ei/exports/:resourceid": {
    // No privilege check on purpose, tech user has implicitly full privileges
    POST: [],
    responsible: ["I063991"],
  },
  "/repository/ei/exports/:resourceid/chunks/:chunkno": {
    // No privilege check on purpose, tech user has implicitly full privileges
    POST: [],
    responsible: ["I063991"],
  },
  "/repository/ei/imports/:resourceid": {
    // No privilege check on purpose, tech user has implicitly full privileges
    POST: [],
    responsible: ["I063991"],
  },
  "/repository/ei/imports/:resourceid/chunks/:chunkno": {
    // No privilege check on purpose, tech user has implicitly full privileges
    POST: [],
    responsible: ["I063991"],
  },
  "/repository/ei/imports/afterallimport": {
    // No privilege check on purpose, tech user has implicitly full privileges
    POST: [],
    responsible: ["I544481"],
  },
  "/repository/ei/jobs/:jobid": {
    // No privilege check on purpose, tech user has implicitly full privileges
    GET: [],
    responsible: ["I544481"],
  },
  "/repository/ei/resources": {
    spacecheck: { filter: true },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }], // acn use this post request body to list objects
    PATCH: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete }], // acn use this patch request to delete space objects
    responsible: ["I309440"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
    skipCheck: true,
  },
  "/repository/ei/resources/dependencies": {
    spacecheck: { filter: true },
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I309440"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
    skipCheck: true,
  },
  "/repository/ei/resources/types": {
    spacecheck: { filter: true },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I309440"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
    skipCheck: true,
  },
  "/repository/objects": {
    spacecheck: { filter: true },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete }],
    responsible: ["I063949"],
    usedBy: ["orca/data_marketplace"],
  },
  "/repository/:spaceid/objects": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.update }],
    PATCH: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete }],
    responsible: ["I063949", "I318411", "I517627", "I309440"],
    usedBy: ["orca/dsp_joule_content"],
  },
  "/repository/objects/:object_id": {
    spacecheck: { filter: true },
    DELETE: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete }],
    responsible: ["I063949"],
    usedBy: ["orca/data_marketplace"],
  },
  "/repository/:spaceid/objects/:object_id": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    DELETE: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete }],
    responsible: ["I517627"],
  },
  "/repository/:requesttype/get": {
    spacecheck: { filter: true },
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I063975"],
  },
  "/repository/:spaceid/:requesttype/get": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I063975"],
  },
  "/repository/:spaceid/delete": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete }],
    responsible: ["I545000"],
  },
  "/repository/recentfiles": {
    spacecheck: { filter: true },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    DELETE: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I309440"],
    featureFlags: [],
    skipCheck: true,
  },
  "/repository/uniquename": {
    spacecheck: { filter: true },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I318411"],
    skipCheck: true,
  },
  "/repository/:spaceid/uniquename": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I318411"],
    skipCheck: true,
  },
  "/repository/usagetracking": {
    GET: [{ authorization: AuthType.DWC_SPACES, activities: Activities.assign }],
    responsible: ["I063949", "D066014"],
    skipCheck: true,
  },
  "/repository/userfavorites": {
    spacecheck: { filter: true },
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    DELETE: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["D065302"],
    skipCheck: true,
  },
  "/repository/userpreferences/:key": {
    spacecheck: { filter: true },
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    DELETE: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["D065302"],
    skipCheck: true,
  },
  "/repository/touch/:object_identifier": {
    spacecheck: { filter: true },
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I309440"],
    featureFlags: [],
    skipCheck: true,
  },
  "/repository/taskstatus": {
    GET: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/repository/:object_identifier/copy": {
    spacecheck: { filter: true },
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.create }],
    responsible: ["I309440"],
    skipCheck: true,
  },
  "/platform/repository/v1/objects": {
    GET: [],
    POST: [],
    DELETE: [],
    responsible: ["I063991"],
    usedBy: ["orca/data_marketplace"],
    skipCheck: true,
  },
  "/platform/repository/v1/objects/:object_id": {
    DELETE: [],
    responsible: ["I063991"],
    skipCheck: true,
  },
  "/platform/repository/v1/designobjects": {
    GET: [],
    responsible: ["I063991"],
    skipCheck: true,
  },
  "/platform/repository/v1/namespaces": {
    GET: [],
    POST: [],
    DELETE: [],
    responsible: ["I063991"],
    skipCheck: true,
  },
  "/platform/repository/v1/namespaces/:namespace_id": {
    DELETE: [],
    responsible: ["I063991"],
    skipCheck: true,
  },
  "/repository/remotes": {
    // Privilege checks are performed in the code behind the endpoint
    GET: [],
    POST: [],
    DELETE: [],
    responsible: ["d023588", "i819462"],
    usedBy: ["orca/data_marketplace"],
  },
  "/repository/remotes/:remote_id": {
    // Privilege checks are performed in the code behind the endpoint
    DELETE: [],
    responsible: ["d023588", "i819462"],
  },
  "/repository/search/([$])*": {
    spacecheck: { filter: true },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["i058204", "d065302"],
    usedBy: ["orca/data_marketplace", "orca/dsp_joule_content"],
  },
  "/repository/changelist/search/([$])*": {
    spacecheck: { filter: true },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["i058204"],
  },
  "/repository/:spaceid/search/([$])*": {
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_GENERAL, activities: Activities.read },
    ],
    responsible: ["i058204", "d065302"],
  },
  "/repository/search/connection/([$])*": {
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["i058204", "d065302"],
    featureFlags: ["DWCO_SEMANTIC_ON_BOARDING"],
    skipCheck: true,
  },
  "/repository/search/space/([$])*": {
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["i058204", "d065302"],
    featureFlags: ["DWCO_SEMANTIC_ON_BOARDING"],
    skipCheck: true,
  },
  "/repository/search/connection/:uclsystemtenantid/:spaceconnectiontype/([$])*": {
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["i058204", "d065302"],
    // featureFlags: ['DWCO_IMPORT_METADATA_VIA_UCL_SYSTEM_TYPE'],
    skipCheck: true,
  },
  "/repository/search/connection/:spaceconnectiontype/([$])*": {
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["i058204", "d065302"],
    featureFlags: ["DWCO_SEMANTIC_ON_BOARDING"],
    skipCheck: true,
  },

  "/repository/shares": {
    spacecheck: { filter: true },
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.share },
    ],
    responsible: ["I822765", "D066014"],
    usedBy: ["orca/data_marketplace"],
  },
  "/repository/:spaceid/shares": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    responsible: ["I517627"],
  },
  "/repository/spaces": {
    GET: [], // Will be chcekd in the endpoint
    responsible: ["D069743"],
    usedBy: ["orca/dsp_joule_content"],
  },
  "/repository/listspaces": {
    GET: [], // Will be chcekd in the endpoint deepsea
    responsible: ["D023588", "I517627"],
    usedBy: ["orca/data_marketplace"],
  },
  "/repository/userprivileges": {
    GET: [],
    responsible: ["D066014", "D069743"],
    skipCheck: true,
  },
  "/repository/:spaceid/i18nsection": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I077452"],
    skipCheck: true,
  },
  "/userprivileges": {
    GET: [],
    responsible: ["D066014", "D069743", "I819519"],
    usedBy: ["orca/dsp_joule_content"],
  },
  "/sap/bc/ina/service/v2/getresponse": {
    POST: [], // check is in the code
    responsible: ["D071862", "D059337"],
  },
  "/sap/bc/ina/service/v2/getserverinfo": {
    GET: [], // check is in the code
    responsible: ["D071862", "D059337"],
  },
  "/sap/bc/ina/service/v2/cors/auth.html": {
    GET: [],
    responsible: ["I819637"],
  },
  "/sap/bc/ina/service/v2/determinespace": {
    POST: [], // check is in the code
    responsible: ["D043947"],
    featureFlags: ["DWCO_UNIVERSAL_MODELS"],
  },
  "/sap/bc/ina/service/v2/:connection_name/determinespace": {
    POST: [], // check is in the code
    responsible: ["D043947"],
    featureFlags: ["DWCO_UNIVERSAL_MODELS"],
  },
  "/ecns": {
    GET: [
      { authorization: AuthType.SYSTEMINFO, activities: Activities.read },
      { authorization: AuthType.DWC_SPACES, activities: Activities.assign },
    ],
    POST: [
      { authorization: AuthType.SYSTEMINFO, activities: Activities.update },
      { authorization: AuthType.DWC_SPACES, activities: Activities.assign },
    ],
    featureFlags: [],
    responsible: ["i024090"],
  },
  "/ecns/:ecnid": {
    GET: [
      { authorization: AuthType.SYSTEMINFO, activities: Activities.read },
      { authorization: AuthType.DWC_SPACES, activities: Activities.assign },
    ],
    PUT: [
      { authorization: AuthType.SYSTEMINFO, activities: Activities.update },
      { authorization: AuthType.DWC_SPACES, activities: Activities.assign },
    ],
    DELETE: [
      { authorization: AuthType.SYSTEMINFO, activities: Activities.update },
      { authorization: AuthType.DWC_SPACES, activities: Activities.assign },
    ],
    featureFlags: [],
    responsible: ["i024090"],
  },
  "/ecns/:ecnid/status": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    featureFlags: [],
    responsible: ["D066459"],
  },
  "/ecns/:ecnid/start": {
    PUT: [
      { authorization: AuthType.SYSTEMINFO, activities: Activities.read },
      { authorization: AuthType.SYSTEMINFO, activities: Activities.update },
    ],
    responsible: ["D066459"],
    featureFlags: [],
  },
  "/ecns/:ecnid/stop": {
    PUT: [
      { authorization: AuthType.SYSTEMINFO, activities: Activities.read },
      { authorization: AuthType.SYSTEMINFO, activities: Activities.update },
    ],
    responsible: ["D066459"],
    featureFlags: [],
  },
  "/ecns/runtime/logs": {
    GET: [
      { authorization: AuthType.SYSTEMINFO, activities: Activities.read },
      { authorization: AuthType.DWC_SPACES, activities: Activities.assign },
    ],
    responsible: ["D066459"],
    featureFlags: [],
  },
  "/ecns/runtime/logs/:logid": {
    GET: [
      { authorization: AuthType.SYSTEMINFO, activities: Activities.read },
      { authorization: AuthType.DWC_SPACES, activities: Activities.assign },
    ],
    responsible: ["D066459"],
    featureFlags: [],
  },
  "/ecns/runtime/logs/:logid/cancel": {
    PUT: [
      { authorization: AuthType.SYSTEMINFO, activities: Activities.read },
      { authorization: AuthType.DWC_SPACES, activities: Activities.assign },
    ],
    responsible: ["D066459"],
    featureFlags: [],
  },
  "/ecns/taskchains/logs/:logid": {
    GET: [
      { authorization: AuthType.SYSTEMINFO, activities: Activities.read },
      { authorization: AuthType.DWC_SPACES, activities: Activities.assign },
    ],
    responsible: ["D066459"],
    featureFlags: [],
  },
  "/ecns/readiness": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["D066459"],
    featureFlags: [],
  },
  "/ecns/sizes/ranges": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["D066459"],
    featureFlags: [],
  },
  "/ecn/schedules": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["D066459"],
    featureFlags: [],
  },
  "/ecn/schedules/:scheduleid": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    PUT: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["D066459"],
    featureFlags: [],
  },
  "/ecn/schedules/:scheduleid/ownerchange": {
    PUT: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["D066459"],
    featureFlags: [],
  },
  "/ecn/schedules/:scheduleid/pause": {
    PUT: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["D066459"],
    featureFlags: [],
  },
  "/ecn/schedules/:scheduleid/resume": {
    PUT: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["D066459"],
    featureFlags: [],
  },
  "/ecn-advisor/configure": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    PATCH: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["D066459"],
    featureFlags: ["DWCO_ECN_ADVISOR_CONFIGURATION"],
  },
  "/ecn-advisor/data": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    PUT: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["D066459"],
    featureFlags: ["DWCO_ECN_ADVISOR_CONFIGURATION"],
  },
  "/security/audit-privileges": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    PATCH: [
      {
        activities: Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["D071863"],
  },
  "/security/monitoring-config": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    PATCH: [
      {
        activities: Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["D071863"],
  },
  "/security/mds-config": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    PATCH: [
      {
        activities: Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I551803"],
  },
  "/security/monitoringsnapshot-config": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    PATCH: [
      {
        activities: Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    featureFlags: [],
    responsible: ["I551803"],
  },
  "/security/ipallowlist": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    POST: [
      {
        activities: Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["D071863"],
  },
  "/security/sccipallowlist": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    POST: [
      {
        activities: Activities.update,
        authorization: AuthType.SYSTEMINFO,
      },
    ],
    responsible: ["I545218"],
  },
  "/security/analysisusers": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    PATCH: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["D070561"],
  },
  "/security/analysisusers/reset": {
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["D070561"],
  },
  "/security/databaseusergroups": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["D070561"],
  },
  "/security/databaseusergroups/reset": {
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["D070561"],
  },
  "/security/logonname": {
    GET: [{ authorization: AuthType.DWC_DAC, activities: Activities.read }],
    responsible: ["I860980"],
  },
  "/security/abap-bridge-configuration": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["I824612", "I513063"],
  },
  "/security/abap-bridge-configuration/calculate": {
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["I824612", "I513063"],
  },
  "/security/customerhana/config": {
    GET: [], // authorization will be checked in the endpoint implementation
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["D070561"],
  },
  "/security/customerhana/info": {
    GET: [{ authorization: AuthType.DWC_SPACES, activities: Activities.read }],
    responsible: ["D070561"],
  },
  "/security/customerhana/flexible-configuration/configuration-status": {
    GET: [],
    responsible: ["I513063"],
  },
  "/security/customerhana/flexible-configuration/features": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }], // retreive which FTC feature is available on the landscape
    responsible: ["I546644"],
  },
  "/security/customerhana/flexible-configuration/ui-facade": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["I558111"],
  },
  "/security/customerhana/license": {
    spacecheck: { filter: true },
    GET: "general",
    responsible: ["I513063"],
  },
  "/security/customerhana/capacity-units/calculate": {
    POST: [], // retrieving default values - no check required
    responsible: ["I513063"],
  },
  "/security/customerhana/sizes/resize": {
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["I513063"],
  },
  "/security/customerhana/patches/upgrade-to-latest": {
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["@orca/dw101_foundation_br"],
    featureFlags: ["DWCO_HANA_PATCH_UPGRADE"],
  },
  "/security/customerhana/database-restart": {
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["@orca/dw101_foundation_br"],
  },
  "/security/customerhana/auditlogs": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    DELETE: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["D070561"],
  },
  "/security/customtenantclassification": {
    GET: [],
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["@orca/dw101_crossarchitecture"],
    usedBy: ["orca/dsp_joule_content"],
  },
  "/security/aifeatures/status": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["@orca/dw101_crossarchitecture"],
  },
  "/security/aifeatures/activation/status": {
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["@orca/dw101_crossarchitecture"],
    featureFlags: ["DWCO_AI_UNITS_SKU"],
  },
  "/support/aifeatures/:featureid/configstoredata": {
    DELETE: [],
    responsible: ["@orca/dw101_crossarchitecture"],
  },
  "/api/v1/aifeatures/:featureid/executable/status": {
    GET: [],
    responsible: ["@orca/dw101_crossarchitecture"],
  },
  "/space/:spaceid/unlock": {
    spacecheck: { nameParameter: ":spaceid", inSpaceManagement: true },
    PUT: [{ authorization: AuthType.DWC_SPACES, activities: Activities.update }],
    responsible: ["D069743"],
  },
  "/space/:spaceid/databaseusers": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read, inSpaceManagement: true },
    GET: [{ authorization: AuthType.DWC_SPACES, activities: Activities.read }],
    responsible: ["D070561"],
  },
  "/space/:spaceid/databaseusers/reset": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read, inSpaceManagement: true },
    POST: [], // authorization will be checked in the endpoint implementation
    responsible: ["D070561"],
  },
  "/space/:spaceid/timedata": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [], // authorization will be checked in the endpoint implementation
    POST: [], // authorization will be checked in the endpoint implementation
    DELETE: [], // authorization will be checked in the endpoint implementation
    responsible: ["D069852", "D062618", "D029876"],
  },
  "/space/:spaceid/dependencies": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["D069852", "D062618", "D029876"],
  },
  "/space/:spaceid/userassignment": {
    spacecheck: { nameParameter: ":spaceid", inSpaceManagement: true },
    GET: [{ authorization: AuthType.DWC_SPACES, activities: Activities.read }],
    PUT: [
      { authorization: AuthType.DWC_SPACES, activities: Activities.read },
      { authorization: AuthType.SCOPEROLEUSERASSIGN, activities: Activities.assign },
    ],
    DELETE: [
      { authorization: AuthType.DWC_SPACES, activities: Activities.read },
      { authorization: AuthType.SCOPEROLEUSERASSIGN, activities: Activities.assign },
    ],
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
    responsible: ["D066535"],
    usedBy: ["orca/dsp_joule_content"],
  },
  "/space/:spaceid/listscoperoles": {
    spacecheck: { nameParameter: ":spaceid", inSpaceManagement: true },
    GET: [{ authorization: AuthType.SCOPEROLEUSERASSIGN, activities: Activities.assign }],
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
    responsible: ["D066535"],
    usedBy: ["orca/dsp_joule_content"],
  },
  "/space/spacesusercount": {
    GET: [],
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
    responsible: ["D066535"],
  },
  "/resources": {
    GET: [], // authorization will be checked in the endpoint implementation
    responsible: ["D070561"],
  },
  "/resources/spaces": {
    GET: [], // authorization will be checked in the endpoint implementation
    PUT: [{ authorization: AuthType.DWC_SPACES, activities: Activities.assign }],
    responsible: ["D070561"],
    usedBy: ["orca/dsp_joule_content"],
  },
  "/resources/spaces/locking": {
    PUT: [{ authorization: AuthType.DWC_SPACES, activities: Activities.update }],
    responsible: ["D070561"],
    usedBy: ["orca/dsp_joule_content"],
  },
  // -- Data Products --
  "/dataproducts/:dataproductordid/apis/:apiresourceordid/installations": {
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I050650"],
    featureFlags: ["DWCO_BDC_DP_UNINSTALLATION"],
  },
  "/dataproducts/:dataproductordid/apis/:apiresourceordid/installations/:spaceid": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    DELETE: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete },
    ],
    responsible: ["I050650"],
    featureFlags: ["DWCO_BDC_DP_UNINSTALLATION"],
  },
  // ---------------------
  "/sql/:space/analyze": {
    // TODO: at Nirupama: please check if this is still used
    POST: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.update }],
    responsible: ["I035978"],
  },
  "/sql/formatsql": {
    POST: [], // no check required just a sql formatter no lookup in repo
    responsible: ["i070083"],
  },
  "/sql/parsesql": {
    POST: [], // no check required just a sql parser no lookup in repo
    responsible: ["i070083"],
  },
  "/sql/getdependentobjects": {
    POST: [], // no check required. just collecting the objects from sql parser. no lookup in repo
    responsible: ["i070083"],
  },
  "/support/discovery": {
    // protected by special token
    GET: [],
    responsible: ["D023588"],
  },
  "/support/space/datarepository": {
    // protected by special token
    PUT: [],
    responsible: ["D069743"],
  },
  "/support/:space/credentials": {
    // protected by special token
    GET: [],
    responsible: ["D029876"],
  },
  "/support/space/:space/bw-bridge-connection": {
    // protected by special token
    GET: [],
    responsible: ["D071863"],
  },
  "/support/delete/spaces": {
    // protected by special token
    DELETE: [],
    responsible: ["D069743"],
  },
  "/support/delete/scopes": {
    // protected by special token
    DELETE: [],
    responsible: ["D066535"],
  },
  "/support/versions": {
    // protected by special token
    GET: [],
    responsible: ["@orca/dw101_crossarchitecture"],
  },
  "/support/repository/health": {
    // protected by special token
    GET: [],
    responsible: ["I077452"],
  },
  "/support/show/space/consistency": {
    // protected by special token
    GET: [],
    responsible: ["D069743"],
  },
  "/support/sdp/conversion": {
    // protected by special token
    PUT: [],
    responsible: ["D069743"],
  },
  "/support/sdp/state": {
    // protected by special token
    GET: [],
    responsible: ["D066535"],
  },
  "/support/sdp/conversion/validation": {
    // protected by special token
    POST: [],
    responsible: ["D066535"],
  },
  "/support/repository/auditadmin": {
    // protected by special token
    GET: [],
    responsible: ["I063949"],
    skipCheck: true,
  },
  "/support/repository/consistency": {
    // protected by special token
    GET: [],
    responsible: ["I318411"],
    skipCheck: true,
  },
  "/support/repository/loghistory": {
    // protected by special token
    GET: [],
    responsible: ["I062999", "D066014"],
    skipCheck: true,
  },
  "/support/repository/repodbtenants": {
    // protected by special token
    GET: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/support/repository/credentials": {
    // protected by special token
    GET: [],
    responsible: ["I063949"],
    skipCheck: true,
  },
  "/support/repository/credstore": {
    // protected by special token
    GET: [],
    DELETE: [],
    POST: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/support/repository/fix-admin-user-inconsistency": {
    // protected by special token
    POST: [],
    responsible: ["I062999", "I063949"],
    skipCheck: true,
  },
  "/support/repository/reconfig": {
    // protected by special token
    POST: [],
    responsible: ["I062999", "I063949"],
    skipCheck: true,
  },
  "/support/repository/resetjwtsso": {
    // protected by special token
    POST: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/support/repository/hatakeover": {
    // protected by special token
    PATCH: [],
    responsible: ["I309440"],
    skipCheck: true,
  },
  "/support/repair/spaces": {
    // protected by special token
    PATCH: [],
    responsible: ["D069743"],
  },
  "/support/tenant/minimum-log-level": {
    // protected by special token
    POST: [],
    GET: [],
    responsible: ["I553812"],
  },
  "/support/minimum-log-level": {
    // protected by special token
    POST: [],
    GET: [],
    responsible: ["I553812"],
  },
  "/support/tenant/rotate-passwords": {
    // protected by special token
    POST: [],
    responsible: ["@orca/dw101_crossarchitecture"],
  },
  "/support/tenant/register-with-ucl": {
    // register aplication with UCL
    POST: [],
    GET: [],
    responsible: ["I546644"],
  },
  "/support/tenant/repository/rotate-backup-user": {
    // protected by special token
    // deprecated
    POST: [],
    responsible: ["@orca/dw101_crossarchitecture"],
  },
  "/support/clear-hana-connection-pools": {
    // protected by special token
    POST: [],
    responsible: ["I545218"],
  },
  "/support/customerhana/trigger-tenants-upgrade": {
    // protected by special token
    POST: [],
    responsible: ["I322804"],
  },
  "/support/tenant/backup/replication/:operation": {
    // protected by special token
    // deprecated
    POST: [],
    responsible: ["@orca/dw101_crossarchitecture"],
    featureFlags: ["DWCO_REPOSITORY_BACKUP" as any],
  },
  "/support/tenant/token-exchanger-configuration": {
    // protected by special token
    POST: [],
    DELETE: [],
    responsible: ["I567958"],
    featureFlags: ["DWCO_USER_PROPAGATION_CONFIG_TOKEN_EXCHANGER"],
  },
  "/support/tenant/usage-tracking": {
    // protected by special token
    POST: [],
    responsible: ["@orca/dw101_crossarchitecture"],
  },
  "/support/tenant/customerhana/request-support-user": {
    // protected by special token
    POST: [],
    responsible: ["D071863"],
  },
  "/support/tenant/embeddeddatalake/request-support-user": {
    POST: [],
    responsible: ["I024090"],
    featureFlags: ["DWCO_LARGE_SYSTEMS_SUPPORT_USER"],
    technical: {
      oAuthScopes: ["$XSAPPNAME.DWCSupport"],
    },
  },
  "/support/tenant/customerhana/alerts/events": {
    // protected by special token
    GET: [],
    responsible: ["I545218"],
    featureFlags: ["DWCO_SUPPORT_ROUTE_HANA_ALERTS"],
  },
  "/support/tenant/customerhana/alerts/rules": {
    // protected by special token
    GET: [],
    responsible: ["I545218"],
    featureFlags: ["DWCO_SUPPORT_ROUTE_HANA_ALERTS"],
  },
  "/support/tenant/customerhana/metrics/definitions": {
    // protected by special token
    GET: [],
    responsible: ["I545218"],
    featureFlags: ["DWCO_SUPPORT_ROUTE_HANA_ALERTS"],
  },
  "/support/tenant/customerhana/metrics/values": {
    // protected by special token
    GET: [],
    responsible: ["I545218"],
    featureFlags: ["DWCO_SUPPORT_ROUTE_HANA_ALERTS"],
  },
  "/support/tenant/customerhana/toggle-multi-az": {
    // protected by special token
    POST: [],
    responsible: ["I558111"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
  },
  "/support/tenant/repository/request-support-user": {
    // protected by special token
    POST: [],
    responsible: ["I062999"],
  },
  "/support/tenant/repository/recovery": {
    // protected by special token
    POST: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/support/tenant/provision": {
    // protected by special token
    POST: [],
    responsible: ["D071863"],
  },
  "/support/admin-api/api-instance": {
    // protected by special token
    GET: [],
    DELETE: [],
    responsible: ["I820212"],
  },
  "/support/admin-api/provision-api-instance": {
    // protected by special token
    POST: [],
    responsible: ["I820212"],
  },
  "/support/admin-api/renew-api-key": {
    // protected by special token
    POST: [],
    responsible: ["I820212"],
  },
  "/support/lsa/spark-history-server": {
    // protected by special token
    POST: [],
    GET: [],
    DELETE: [],
    responsible: ["I566473"],
    featureFlags: ["DWCO_LARGE_SYSTEMS"],
  },
  "/support/lsa/allowlist": {
    // protected by special token
    GET: [],
    POST: [],
    responsible: ["I558111"],
    featureFlags: ["DWCO_LARGE_SYSTEMS"],
  },
  "/support/lsa/spark-instance": {
    // protected by special token
    POST: [],
    responsible: ["I558111"],
    featureFlags: ["DWCO_LARGE_SYSTEMS"],
  },
  "/support/lsa/hdlf-instance": {
    // protected by special token
    POST: [],
    responsible: ["I558111"],
    featureFlags: ["DWCO_LARGE_SYSTEMS"],
  },

  "/support/lsa/plugin-configuration": {
    // protected by special token
    POST: [],
    responsible: ["I585977"],
    featureFlags: ["DWCO_HANA_LAKEHOUSE_PLUGIN"],
  },
  "/support/tenant/customerhana/fix-user-manager-inconsistency": {
    // protected by special token
    POST: [],
    responsible: ["D071863"],
  },
  "/support/tenant/customerhana/fix-admin-user-inconsistency": {
    // protected by special token
    POST: [],
    responsible: ["I544493"],
  },
  "/support/tenant/customerhana/grant-datalake-role": {
    // protected by special token
    POST: [],
    responsible: ["@orca/dw101_foundation_br"],
  },
  "/support/telemetry/datasphere-metrics/credentials": {
    // protected by special token
    POST: [],
    GET: [],
    responsible: ["DL DW Data Layer ALL <<EMAIL>>"],
  },
  "/support/tenant/customerhana/force-upgrade": {
    // protected by special token
    POST: [],
    responsible: ["D071863"],
  },
  "/support/tenant/customerhana/flexible-configuration/metadata": {
    // protected by special token
    GET: [],
    responsible: ["I558111"],
  },
  "/support/tenant/customerhana/flexible-configuration/metadata/clear": {
    // protected by special token
    POST: [],
    responsible: ["I546644"],
  },
  "/support/tenant/customerhana/update-hana-parameter": {
    // protected by special token
    POST: [],
    responsible: ["I558111"],
  },
  "/support/tenant/ip-allowlist": {
    // protected by special token
    GET: [],
    POST: [],
    DELETE: [],
    responsible: ["I544493"],
  },
  "/support/tenant/ip-allowlist/check": {
    // protected by special token
    POST: [],
    responsible: ["I544493"],
  },
  "/support/ucl/formation": {
    // protected by special token
    GET: [],
    responsible: ["I561398"],
  },
  "/support/ucl/application-template": {
    // protected by special token
    GET: [],
    POST: [],
    PATCH: [],
    DELETE: [],
    responsible: ["I558111"],
  },
  "/support/bdc/ucl/system-removal-validation": {
    // endpoint for UCL use - check a tenant for exclusion from BDC Cockpit formation
    POST: [],
    responsible: ["I557089"],
    featureFlags: ["DWCO_LS_BDCC_SUPPORT_UI"],
    technical: {
      oAuthScopes: ["$XSAPPNAME.DWCUclIntegration", "$XSAPPNAME.DWCSupport"],
    },
  },
  "/support/environment": {
    // protected by special token
    GET: [],
    responsible: ["D043947"],
  },
  "/support/circuitbreaker": {
    // protected by special token
    GET: [],
    PUT: [],
    responsible: ["I863108", "D071863"],
  },
  "/support/circuitbreaker/test": {
    // protected by special token
    GET: [],
    responsible: ["I863108", "D071863"],
  },
  "/support/abap-bridge/database-connection": {
    // protected by special token
    POST: [],
    responsible: ["I513063", "I824612"],
  },
  "/support/abap-bridge/connection-hh-prep": {
    // protected by special token
    POST: [],
    responsible: ["D024795", "I513063"],
  },
  "/support/abap-bridge/connection-hh-ack": {
    // protected by special token
    POST: [],
    responsible: ["D024795", "I513063"],
  },
  "/support/abap-bridge-configuration/force-refresh": {
    POST: [], // protected by special token
    responsible: ["I824612", "I513063"],
  },
  "/support/broadcast": {
    POST: [], // protected by special token
    responsible: ["@orca/dw101_crossarchitecture"],
  },
  "/support/tenant/metering/reports": {
    GET: [], // protected by special token
    PUT: [], // protected by special token
    responsible: ["I544493"],
  },
  "/support/redis/status": {
    // protected by special token
    GET: [],
    responsible: ["I553812"],
  },
  "/support/redis/slowlog": {
    // protected by special token
    GET: [],
    responsible: ["I553812"],
  },
  "/support/redis/flushall": {
    // protected by special token
    POST: [],
    responsible: ["I553812"],
  },
  "/support/redis/cache/:key": {
    // protected by special token
    GET: [],
    DELETE: [],
    responsible: ["I864016"],
  },
  "/support/oneagent/logs/:logtype": {
    // protected by special token
    GET: [],
    responsible: ["I545218"],
  },
  "/support/profile/memory/heapprofile": {
    // protected by special token
    GET: [],
    responsible: ["I545218"],
  },
  "/support/profile/memory/heapsnapshot": {
    // protected by special token
    GET: [],
    responsible: ["I553812"],
  },
  "/support/profile/memory/heapstatistics": {
    // protected by special token
    GET: [],
    responsible: ["I553812"],
  },
  "/support/profile/memory/heapspacestatistics": {
    // protected by special token
    GET: [],
    responsible: ["I553812"],
  },
  "/support/profile/memory/gctracesenable": {
    // protected by special token
    POST: [],
    responsible: ["I553812"],
  },
  "/support/profile/memory/gctracesdisable": {
    // protected by special token
    POST: [],
    responsible: ["I553812"],
  },
  "/support/profile/cpu": {
    // protected by special token
    GET: [],
    responsible: ["I553812"],
  },
  "/support/profile/eventloopblockers": {
    // protected by special token
    DELETE: [],
    GET: [],
    POST: [],
    responsible: ["I864016", "I553812", "I545218"],
  },
  "/support/auditlog/state": {
    // protected by special token
    POST: [],
    responsible: ["I553812"],
  },
  "/support/featureflag/flushflags": {
    // protected by special token
    POST: [],
    responsible: ["I513089"],
  },
  "/support/hana/query": {
    // protected by special token
    GET: [],
    responsible: ["I553812"],
  },
  "/support/configurations/parameters/:key/effectivevalue": {
    // protected by special token
    GET: [],
    responsible: ["I863108", "I864016"],
  },
  "/support/configurations/parameters/:key/values": {
    // protected by special token
    POST: [],
    responsible: ["I863108", "I864016"],
  },
  "/support/di-e/tenant/rmspodcount": {
    // protected by special token
    GET: [],
    PUT: [],
    responsible: ["I520900"],
  },
  "/support/:space/deploy/cleanupmdsmetadata": {
    // protected by special token
    POST: [],
    responsible: ["D043947"],
  },
  "/support/ratelimiting/state": {
    // protected by special token
    GET: [],
    responsible: ["I553812"],
    rateLimiting: {
      name: "rateLimitingStateChain",
      state: State.DISABLED,
      rateLimiters: [],
    },
  },
  "/support/endpoints": {
    // protected by special token
    GET: [],
    POST: [],
    responsible: ["I553812"],
    rateLimiting: {
      $ref: "configEndpoints",
    },
  },
  "/support/endpoints/disable": {
    // protected by special token
    POST: [],
    responsible: ["I553812"],
    rateLimiting: {
      $ref: "configEndpoints",
    },
  },
  "/support/endpoints/reset": {
    // protected by special token
    POST: [],
    responsible: ["I553812"],
    rateLimiting: {
      $ref: "configEndpoints",
    },
  },
  "/support/cache/invalidate": {
    // protected by special token
    POST: [],
    responsible: ["I553812"],
  },
  "/support/compute/v1/nodes/:id/unassign/:spaceid": {
    // protected by special token
    PUT: [],
    responsible: ["D066459"],
  },
  "/support/compute/v1/nodes/:id": {
    // protected by special token
    DELETE: [],
    responsible: ["D066459"],
  },
  "/configurations/:key": {
    GET: [],
    responsible: ["@orca/dw101_crossarchitecture"],
  },
  "/versions": {
    // any authenticated user should see this
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["D043947"],
  },
  "/dataflow/runtime": {
    spacecheck: { nameParameter: "space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["I517217"],
  },
  "/support/metering/check-metering-availability/:key": {
    GET: [],
    responsible: ["I585977"],
  },
  "/support/metering/metric-status": {
    GET: [],
    responsible: ["I827700"],
  },
  "/support/tenant/metering/report-insightapps": {
    GET: [], // protected by special token
    POST: [], // protected by special token
    responsible: ["I747296"],
  },
  "/support/metering/report-insightapps": {
    GET: [], // protected by special token
    POST: [], // protected by special token
    responsible: ["@orca/dw101_foundation_br"],
  },

  "/dataflow/:spaceid/run": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["I545672"],
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/dataflow/runtime/:log_id": {
    spacecheck: { nameParameter: "?space", access: AccessPrivilege.Read },
    DELETE: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["I343950"],
  },
  "/dataflow/unscheduled": {
    spacecheck: { nameParameter: "?space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I343950"],
  },
  "/dataflow/status": {
    spacecheck: { nameParameter: "?space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I343950"],
  },
  "/dataflow/status/:graph_id": {
    spacecheck: { nameParameter: "?space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I343950"],
  },
  "/dataflow/status/:handle_id/metrices": {
    spacecheck: { nameParameter: "?space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I331866"],
  },
  "/dataflow/support/:handle_id": {
    spacecheck: { nameParameter: "?space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I343950"],
  },
  "/c4s/internal_services/:spaceid/deploycsndata": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I503688"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Write },
  },
  "/dataflow/documentation/:id": {
    GET: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["I343950"],
  },
  "/dataflow/connections/:connection_id/datasets/:file_name/data": {
    spacecheck: { nameParameter: "?space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I343950"],
  },
  "/dataflow/datasets/data": {
    spacecheck: { nameParameter: "?space", access: AccessPrivilege.Read },
    PUT: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I343950"],
  },
  "/dataflow/metadata": {
    spacecheck: { nameParameter: "?space", access: AccessPrivilege.Read },
    PUT: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["I343950"],
  },
  "/dataflow/datasetdefinition": {
    spacecheck: { nameParameter: "?space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["I343950"],
  },
  "/dataflow/:spaceid/connections/:connectionid/browse": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["I308097"],
  },
  "/dataflow/accessmethods": {
    spacecheck: { nameParameter: "?space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["I308097"],
  },
  "/c4s/internal_services/:spaceid/loadcurrencyconversionselectionvalues": {
    GET: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I562671"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/:spaceid/checkcurrencyconversionclientexists": {
    GET: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I562671"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/:spaceid/dwcdeleteobject": {
    DELETE: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.delete }],
    responsible: ["I548638"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/hybriddatasource": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["D029388"],
    offFeatureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/c4s/internal_services/:spaceid/hybriddatasource": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["D029388"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/c4s/internal_services/v1/:spaceid/value-help/semantic-attributes/:semantic_attribute_id": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/value-help/attributes/:attribute_id": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/datasources/:data_source_version_id/check-keys": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/datasources/:data_source_version_id/check-hierarchy-keys": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/views/check-keys": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/datasources/:data_source_version_id/navigations/:key_id/check": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/datasources/:data_source_version_id/textassociation-checkkeys": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/queries/:query_id/deploy": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read + Activities.create }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/queries/:query_id/download": {
    GET: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/queries/:query_id/csn": {
    GET: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/downloadsql/datasource": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/downloadsql/semantics/:semantic_version_id": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/semantics/:semantic_version_id": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/scenarios/:scenario_id": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/datasources": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/c4s/internal_services/v1/:spaceid/value-help/parameters": {
    POST: [{ authorization: AuthType.DWC_BUSINESSBUILDER, activities: Activities.read }],
    responsible: ["I548816"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/connections/types": {
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    responsible: ["I520900"],
  },
  "/connections/:space_name/types": {
    spacecheck: {
      nameParameter: ":space_name",
      access: AccessPrivilege.Read,
      inSpaceManagement: true,
    },
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    responsible: ["I520900"],
  },
  "/connections/:space_name/types/:typeid": {
    spacecheck: {
      nameParameter: ":space_name",
      access: AccessPrivilege.Read,
      inSpaceManagement: true,
    },
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    responsible: ["I520900"],
  },
  "/connections/uimetadata/:space_name/:typeid": {
    spacecheck: {
      nameParameter: ":space_name",
      access: AccessPrivilege.Read,
      inSpaceManagement: true,
    },
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    responsible: ["I520900"],
  },
  "/connections/status/:remote_id": {
    // Privilege checks are performed in the code behind the endpoint (get object from repository)
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_REMOTECONNECTION,
      },
    ],
    responsible: ["d046539"],
    usedBy: ["orca/data_marketplace"],
    rateLimiting: {
      GET: {
        name: "connectionStatusLimitChain",
        state: rateLimiterState,
        rateLimiters: [
          {
            name: "connectionStatusLimiter",
            type: RateLimiterTypes.REQUEST,
            consumes: 1,
            state: rateLimiterState,
            options: {
              points: 25, // 25 requests
              duration: 60, // per minute
            },
          },
        ],
      },
    },
  },
  "/connections/status/:space_name/:remote_source_name/remotetables": {
    spacecheck: {
      nameParameter: ":space_name",
      access: AccessPrivilege.Read,
      inSpaceManagement: true,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_REMOTECONNECTION,
      },
    ],
    responsible: ["i816602"],
    usedBy: ["orca/data_marketplace"],
    rateLimiting: {
      GET: {
        name: "connectionRemoteTablesStatusLimitChain",
        state: rateLimiterState,
        rateLimiters: [
          {
            name: "connectionRemoteTablesStatusLimiter",
            type: RateLimiterTypes.REQUEST,
            consumes: 1,
            state: State.DRYRUN,
            options: {
              points: 25, // 25 requests
              duration: 60, // per minute
            },
          },
        ],
      },
    },
  },
  "/connections/replication/:connection_name": {
    PUT: [
      {
        activities: Activities.update,
        authorization: AuthType.DWC_REMOTECONNECTION,
      },
    ],
    offFeatureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
    responsible: ["d027946"],
  },
  "/connections/replication/:space_name/:connection_name": {
    spacecheck: {
      nameParameter: ":space_name",
      access: AccessPrivilege.Read,
      inSpaceManagement: true,
    },
    PUT: [
      {
        activities: Activities.update,
        authorization: AuthType.DWC_REMOTECONNECTION,
      },
    ],
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
    responsible: ["d027946"],
  },
  "/connections/:space_name/upsertpartnertile": {
    spacecheck: {
      nameParameter: ":space_name",
      access: AccessPrivilege.Read,
      inSpaceManagement: true,
    },
    PUT: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.DWC_REMOTECONNECTION,
      },
    ],
    responsible: ["I537933"],
    featureFlags: ["DWCO_CONNECTION_PARTNER_TEST"],
  },

  "/connections/:space_name/getpartnertile": {
    spacecheck: {
      nameParameter: ":space_name",
      access: AccessPrivilege.Read,
      inSpaceManagement: true,
    },
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.DWC_REMOTECONNECTION,
      },
    ],
    responsible: ["I537933"],
    featureFlags: ["DWCO_CONNECTION_PARTNER_TEST"],
  },
  "/connections/:space_name/deletepartnertile": {
    spacecheck: {
      nameParameter: ":space_name",
      access: AccessPrivilege.Read,
      inSpaceManagement: true,
    },
    DELETE: [
      {
        activities: Activities.read + Activities.update,
        authorization: AuthType.DWC_REMOTECONNECTION,
      },
    ],
    responsible: ["I537933"],
    featureFlags: ["DWCO_CONNECTION_PARTNER_TEST"],
  },
  "/connections/list/tunnel": {
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    responsible: ["I517217"],
  },
  "/connections/tunnel/:sac_tunnel_connection/bw4hanadbinfo": {
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    responsible: ["I520900"],
  },
  "/certificate": {
    PUT: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["I545218"],
    usedBy: ["orca/data_marketplace"],
  },
  "/certificates": {
    GET: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.read }],
    responsible: ["I545218"],
    usedBy: ["orca/data_marketplace"],
  },
  "/certificates/:fingerprint": {
    DELETE: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    PUT: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["I545218", "I567958"],
    usedBy: ["orca/data_marketplace"],
  },
  "/connections/openconnectors/:space_name/configuration": {
    spacecheck: {
      nameParameter: ":space_name",
      access: AccessPrivilege.Read,
      inSpaceManagement: true,
    },
    POST: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.create }],
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    responsible: ["D068183"],
  },
  "/connections/openconnectors/:space_name/configuration/:configuration_id": {
    spacecheck: {
      nameParameter: ":space_name",
      access: AccessPrivilege.Read,
      inSpaceManagement: true,
    },
    PUT: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.delete }],
    responsible: ["D068183"],
  },
  "/connections/openconnectors/:space_name/instances": {
    spacecheck: {
      nameParameter: ":space_name",
      access: AccessPrivilege.Read,
      inSpaceManagement: true,
    },
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    responsible: ["D068183"],
  },
  "/connections/openconnectors/regions": {
    GET: [{ authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read }],
    responsible: ["D068183"],
  },
  "/connections/resethanasccsettings": {
    POST: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["I545218"],
  },
  "/il/runtime/space/:spaceid": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["I517217"],
  },
  "/il/preview/space/:space/ilname/:name/rule/:ruleid": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_CONSUMPTION, activities: Activities.execute }],
    responsible: ["I333245"],
  },
  "/il/preview/space/:space/ilname/:name/rule/:ruleid/bucket/:bucket": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_CONSUMPTION, activities: Activities.execute }],
    responsible: ["I333245"],
  },
  "/il/command/space/:spaceid": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D066459"],
  },
  "/il/metrics/space/:space/ilname/:name": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_CONSUMPTION, activities: Activities.execute }],
    responsible: ["I333245"],
  },
  "/il/workarea/space/:space/ilname/:name/rule/:ruleid/bucket/:bucket/matchrecords": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_CONSUMPTION, activities: Activities.execute }],
    responsible: ["I333245"],
  },
  "/il/workarea/space/:space/ilname/:name/rule/:ruleid/bucket/:bucket/childrecords": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_CONSUMPTION, activities: Activities.execute }],
    responsible: ["I333245"],
  },
  "/il/data/space/:space/ilname/:name": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    DELETE: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D066459"],
  },
  "/il/data/space/:space/ilname/:name/rule/:ruleid": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    DELETE: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D066459"],
  },
  "/il/data/space/:space/ilname/:name/hasdata": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["D066459"],
  },
  "/dac/:spacename/availablenames": {
    spacecheck: { nameParameter: ":spacename", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["I545334"],
  },
  "/dac/permissionstable": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DAC, activities: Activities.read },
    ],
    responsible: ["I545334"],
    offFeatureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/dac/:spacename/permissionstable": {
    spacecheck: { nameParameter: ":spacename", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DAC, activities: Activities.read },
    ],
    responsible: ["I545334"],
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/dac/importedobjects": {
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DAC, activities: Activities.read },
    ],
    responsible: ["I545334"],
    offFeatureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/dac/:spacename/importedobjects": {
    spacecheck: { nameParameter: ":spacename", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DAC, activities: Activities.read },
    ],
    responsible: ["I545334"],
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/dac/objectscreationstatus": {
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DAC, activities: Activities.read },
    ],
    responsible: ["I545334"],
    offFeatureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/dac/:spacename/objectscreationstatus": {
    spacecheck: { nameParameter: ":spacename", access: AccessPrivilege.Read },
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DAC, activities: Activities.read },
    ],
    responsible: ["I545334"],
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/dac/createdesignobjects": {
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DAC, activities: Activities.read },
      { authorization: AuthType.DWC_DAC, activities: Activities.create },
      { authorization: AuthType.DWC_DAC, activities: Activities.update },
    ],
    responsible: ["I545334"],
    offFeatureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/dac/:spacename/createdesignobjects": {
    spacecheck: { nameParameter: ":spacename", access: AccessPrivilege.Read },
    POST: [
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
      { authorization: AuthType.DWC_DAC, activities: Activities.read },
      { authorization: AuthType.DWC_DAC, activities: Activities.create },
      { authorization: AuthType.DWC_DAC, activities: Activities.update },
    ],
    responsible: ["I545334"],
    featureFlags: ["DWC_DUMMY_SPACE_PERMISSIONS"],
  },
  "/scheduler/oauth/callback": {
    GET: [],
    responsible: ["D041662", "D055938", "D024197"],
  },
  "/scheduler/authorization-callback-url": {
    GET: [],
    responsible: ["D041662", "D055938", "D024197"],
  },
  "/scheduler/has-user-consent": {
    GET: [],
    responsible: ["D041662", "D055938", "D024197"],
  },
  "/querybuilder/:spaceid/loaddataentitydetails": {
    POST: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["I547909"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/querybuilder/:spaceid/adaptversion": {
    POST: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    responsible: ["D043610"],
  },
  "/querybuilder/:spaceid/logpreviewcall": {
    POST: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    responsible: ["I564031"],
  },
  "/querybuilder/:spaceid/convertcsn": {
    POST: "dataBuilderCreateOrUpdate",
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    responsible: ["I864016"],
  },
  "/querybuilder/:spaceid/previewdeployment": {
    POST: "dataBuilderCreateOrUpdate",
    DELETE: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.delete }],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    responsible: ["I864016", "I517675"],
  },
  "/querybuilder/:spaceid/runtimecsn": {
    POST: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    responsible: ["D029388"],
  },
  "/querybuilder/:spaceid/convertanalyticaldataset": {
    POST: "dataBuilderCreateOrUpdate",
    responsible: ["D029388"],
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
  },
  "/querybuilder/:spaceid/preconditionpreviewdeployment": {
    POST: "dataBuilderCreateOrUpdate",
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    responsible: ["I517675"],
  },
  "/advisor/:space/analyze/:name": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
      { authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read },
    ],
    responsible: ["I063874"],
  },
  "/advisor/:space/execute/:name": {
    spacecheck: {
      nameParameter: ":space",
      access: AccessPrivilege.Read,
    },
    POST: [
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
      { authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update },
    ],
    responsible: ["D037121"],
  },
  "/advisor/:space/result/:tasklogid": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
      { authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read },
    ],
    responsible: ["D037121"],
  },

  "/advisor/:space/logs/:logid/messages/:messageno/planviz": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
      { authorization: AuthType.DWC_RUNTIME, activities: Activities.read },
    ],
    responsible: ["D037121"],
  },
  "/advisor/:space/logs/:logid/messages/:messageno/explainplan": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
      { authorization: AuthType.DWC_RUNTIME, activities: Activities.read },
    ],
    responsible: ["D074254"],
  },
  "/advisor/:space/partitioncolumn/:name": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
      { authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read },
    ],
    responsible: ["I063874"],
  },
  "/advisor/:space/previewlineage": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
      { authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read },
    ],
    responsible: ["I063874"],
  },
  "/advisor/v2/:space/previewlineage": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["I063874"],
    featureFlags: ["DWCO_ADVISOR_CROSSSPACE_2"],
  },

  "/advisor/:space/performanceanalysis/:name": {
    spacecheck: {
      nameParameter: ":space",
      access: AccessPrivilege.Read,
    },
    POST: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.update }],
    GET: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["D037121"],
    featureFlags: ["DWCO_VIEW_RUNTIME_METRICS"],
  },

  "/support/tenant/repository/workload-class/:tenantid?": {
    GET: [],
    PUT: [],
    responsible: ["I827737"],
    skipCheck: true,
  },
  "/support/tenant/repository/workload-class-statistics/:tenantid?": {
    GET: [],
    responsible: ["I827737"],
    skipCheck: true,
  },
  "/support/tenant/repository/workload-class-toggle/:tenantid?": {
    PATCH: [],
    responsible: ["I827737"],
    skipCheck: true,
  },
  "/support/tenant/catalog/workload-class/:tenantid?": {
    GET: [],
    PUT: [],
    responsible: ["I542012"],
    skipCheck: true,
    featureFlags: ["DWCO_CATALOG_WORKLOAD_APIS"],
  },
  "/support/tenant/catalog/workload-class-statistics/:tenantid?": {
    GET: [],
    responsible: ["I542012"],
    skipCheck: true,
    featureFlags: ["DWCO_CATALOG_WORKLOAD_APIS"],
  },
  "/support/tenant/catalog/workload-class-toggle/:tenantid?": {
    PATCH: [],
    responsible: ["I542012"],
    skipCheck: true,
    featureFlags: ["DWCO_CATALOG_WORKLOAD_APIS"],
  },
  "/support/repository/workload-class": {
    GET: [],
    PATCH: [],
    responsible: ["I827737"],
    skipCheck: true,
  },
  "/support/repository/workload-class-statistics": {
    GET: [],
    responsible: ["I827737"],
    skipCheck: true,
  },
  "/support/repository/workload-class-consistency": {
    GET: [],
    responsible: ["I827737"],
    skipCheck: true,
  },
  "/support/repository/workload-class-toggle": {
    PATCH: [],
    responsible: ["I827737"],
    skipCheck: true,
  },
  "/support/tenant/repository/backup-workload-class/:tenantid?": {
    GET: [],
    PUT: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/support/tenant/repository/backup-workload-class-statistics/:tenantid?": {
    GET: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/support/tenant/repository/backup-workload-class-toggle/:tenantid?": {
    PATCH: [],
    responsible: ["I062999"],
    skipCheck: true,
  },
  "/support/repository/onboard-client": {
    POST: [],
    responsible: ["I546644"],
  },
  "/support/tenant/customerhanahdi/containers": {
    // protected by special token
    GET: [],
    responsible: ["I546644"],
  },
  "/support/tenant/customerhanahdi/cache": {
    // protected by special token
    GET: [],
    DELETE: [],
    responsible: ["I546644"],
  },
  "/support/bwbridge/create-instance": {
    POST: [],
    responsible: ["I555907"],
  },
  "/support/bwbridge/:instancename": {
    DELETE: [],
    responsible: ["I751143"],
  },
  "/support/bwbridge/refresh": {
    PATCH: [],
    responsible: ["I751143"],
  },
  "/support/bwbridge/keyandconnection": {
    POST: [],
    responsible: ["I555907"],
  },
  "/support/lsa/deleteinstances": {
    DELETE: [],
    responsible: ["I555907"],
  },
  "/support/tenant/service-instance": {
    GET: [],
    responsible: ["I573496"],
  },
  "/support/tenant/workflows": {
    GET: [],
    responsible: ["I573496"],
  },
  "/support/tenant/license/allocated-cu": {
    GET: [],
    responsible: ["@orca/dw101_foundation_br"],
  },
  "/support/consistency/meta": {
    GET: [],
    responsible: ["@orca/dw101_crossarchitecture"],
    featureFlags: ["DWCO_CONSISTENCY_FRAMEWORK"],
  },
  "/support/consistency/report": {
    POST: [],
    responsible: ["@orca/dw101_crossarchitecture"],
    featureFlags: ["DWCO_CONSISTENCY_FRAMEWORK"],
  },
  "/support/consistency/reconciliation": {
    POST: [],
    responsible: ["@orca/dw101_crossarchitecture"],
    featureFlags: ["DWCO_CONSISTENCY_FRAMEWORK"],
  },
  "/support/consistency/orchestrator/meta": {
    GET: [],
    responsible: ["@orca/dw101_crossarchitecture"],
    featureFlags: ["DWCO_CONSISTENCY_FRAMEWORK"],
  },
  "/support/consistency/orchestrator/report": {
    POST: [],
    responsible: ["@orca/dw101_crossarchitecture"],
    featureFlags: ["DWCO_CONSISTENCY_FRAMEWORK"],
  },
  "/replicationflow/space/:space/flows/:name/validate": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["I333638"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
  },
  "/replicationflow/space/:space/usage": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I500531"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
  },
  "/replicationflow/space/:space/flows/:name/run": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["I500531"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
  },
  "/replicationflow/space/:space/flows/:name/suspend": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["I500531"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
  },
  "/replicationflow/space/:space/flows/:name/terminate": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["I500531"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
  },
  "/replicationflow/space/:space/flows/:name/restart": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["I500531"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
  },
  "/replicationflow/space/:space/flows/:name/support": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I500531"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
  },
  "/replicationflow/space/:space/flows/:name/status": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I500531"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
  },
  "/replicationflow/:space/flows/:name/runtimeparams": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I570040"],
    featureFlags: ["DWCO_DS_REPLICATION_FLOW_SUPPORT_RUN_TIME_PARAMS"],
  },
  "/replicationflow/space/:space/connections/:connectionid/getchildren": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["I553810"],
    featureFlags: ["DWCO_DS_REPLICATION_FLOW_CONFLUENT_AS_A_SOURCE"],
  },
  "/replicationflow/space/:space/connections/:connectionid/getmetadata": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["I553810"],
    featureFlags: ["DWCO_DS_REPLICATION_FLOW_CONFLUENT_AS_A_SOURCE"],
  },
  "/replicationflow/space/:space/flows/:name/checkreplicationflow": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["I524945"],
    featureFlags: ["DWCO_DS_REPLICATION_FLOW_ALLOW_SAME_SOURCE"],
  },
  "/replicationflow/space/:space/flows/:name/getnotificationinfo": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I333638"],
    featureFlags: ["DWCO_DS_REPLICATION_FLOW_EMAIL_NOTIFICATION"],
  },
  "/replicationflow/space/:space/flows/:name/updatenotificationinfo": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    PUT: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["I333638"],
    featureFlags: ["DWCO_DS_REPLICATION_FLOW_EMAIL_NOTIFICATION"],
  },
  "/bwbridge/whereused": {
    POST: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["D067576"],
    featureFlags: ["DWCO_BRIDGE_USAGE_CHECK"],
  },
  "/api/v1/bwpce/space": {
    // See service/api/bwpce.json - internal API
    spacecheck: { nameParameter: "space" }, // this is one of the rare cases where nameParameter is contained on body and not URL
    POST: [], // Create, Update and Manage will be checked at the endpoint itself, cannot differentiate between Create and Update in endpointsfilter.ts
    responsible: ["d023588"],
    featureFlags: [],
    usedBy: ["BW PCE"],
  },
  "/homepage/task-logs": {
    GET: [],
    responsible: ["@orca/dw101_crossarchitecture"],
  },
  "/homepage/spaces": {
    GET: [],
    responsible: ["@orca/dw101_crossarchitecture"],
  },
  "/joule/allusers": {
    GET: [{ authorization: AuthType.USER, activities: Activities.read }],
    responsible: ["@orca/dw101_crossarchitecture"],
    usedBy: ["orca/dsp_joule_content"],
    featureFlags: ["DWCO_JOULE_DWAASCORE_FUNCTIONAL_BASIC"],
  },
  "/joule/allroles": {
    GET: [{ authorization: AuthType.PROFILE, activities: Activities.read }],
    responsible: ["@orca/dw101_crossarchitecture"],
    usedBy: ["orca/dsp_joule_content"],
  },
  "/joule/config": {
    GET: [], // Permission checks will be conducted in the endpoint
    PATCH: [
      {
        authorization: AuthType.SYSTEMINFO,
        activities: Activities.update,
      },
    ],
    responsible: ["@orca/dw101_crossarchitecture"],
    featureFlags: ["DWCO_UCL_JOULE"],
  },
  "/joule/space/namevalidation": {
    POST: [{ authorization: AuthType.DWC_SPACES, activities: Activities.create }],
    responsible: ["@orca/dw101_crossarchitecture"],
    usedBy: ["orca/dsp_joule_content"],
    featureFlags: ["DWCO_JOULE_DWAASCORE_FUNCTIONAL_BASIC"],
  },
  "/joule/space/allspaces": {
    spacecheck: { filter: true },
    GET: [{ authorization: AuthType.DWC_SPACEFILE, activities: Activities.read }],
    responsible: ["@orca/dw101_crossarchitecture"],
    usedBy: ["orca/dsp_joule_content"],
    featureFlags: ["DWCO_JOULE_DWAASCORE_FUNCTIONAL_BASIC"],
  },
  "/joule/space/auditlog": {
    spacecheck: { filter: true },
    GET: [
      { authorization: AuthType.DWC_SPACES, activities: Activities.read },
      { authorization: AuthType.DWC_SPACEFILE, activities: Activities.read },
    ],
    responsible: ["@orca/dw101_crossarchitecture"],
    usedBy: ["orca/dsp_joule_content"],
    featureFlags: ["DWCO_JOULE_DWAASCORE_ENHANCED_SPACE_INFO"],
  },
  "/odc/support/clientid": {
    GET: [],
    responsible: ["I571022"],
    usedBy: ["orca/deepsea"],
    featureFlags: ["DWCO_TF_ODC_TASKS"],
    technical: {
      oAuthScopes: ["$XSAPPNAME.OdcClientIdRetrieval"],
    },
  },
  "/odc/schedules": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.ODC_SYSTEM,
      },
    ],
    POST: [
      {
        activities: Activities.create,
        authorization: AuthType.ODC_SYSTEM,
      },
    ],
    responsible: ["I816751"],
    featureFlags: ["DWCO_TF_ODC_TASKS"],
  },
  "/odc/schedules/:scheduleid": {
    GET: [
      {
        activities: Activities.read,
        authorization: AuthType.ODC_SYSTEM,
      },
    ],
    PUT: [
      {
        activities: Activities.update,
        authorization: AuthType.ODC_SYSTEM,
      },
    ],
    DELETE: [
      {
        activities: Activities.delete,
        authorization: AuthType.ODC_SYSTEM,
      },
    ],
    responsible: ["I816751"],
    featureFlags: ["DWCO_TF_ODC_TASKS"],
  },
  "/odc/schedules/:scheduleid/ownerchange": {
    PUT: [
      {
        activities: Activities.update,
        authorization: AuthType.ODC_SYSTEM,
      },
    ],
    responsible: ["I816751"],
    featureFlags: ["DWCO_TF_ODC_TASKS"],
  },
  "/transformationflow/:spaceid/status/:name": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I327609"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
  },
  "/transformationflow/:space/deltasubscriptions/:flowname": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: "dataIntegrationOrDataBuilderRead",
    responsible: ["D040293"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
  },
  "/transformationflow/:space/runtimesettings/:flowname": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    PATCH: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.update }],
    responsible: ["D040293"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
  },
  "/transformationflow/:space/logs/:logid/messages/:messageno/planviz": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
      { authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read },
      { authorization: AuthType.DWC_RUNTIME, activities: Activities.read },
    ],
    responsible: ["I544811"],
    featureFlags: ["DWCO_TRANSFORMATION_FLOW_PLAN_VIZ"],
  },
  "/transformationflow/:space/logs/:logid/messages/:messageno/explainplan": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
      { authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read },
      { authorization: AuthType.DWC_RUNTIME, activities: Activities.read },
    ],
    responsible: ["I544811"],
    featureFlags: ["DWCO_TRANSFORMATION_FLOW_EXPLAIN_PLAN"],
  },
  "/remoteobject/:space/validate/:tablename": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [
      { authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    responsible: ["D027946"],
  },
  "/remoteobject/:space/validate": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [
      { authorization: AuthType.DWC_REMOTECONNECTION, activities: Activities.read },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.update },
      { authorization: AuthType.DWC_DATABUILDER, activities: Activities.read },
    ],
    responsible: ["D027946"],
  },
  "/validation/model/:space/:entity/pre-check": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["D066022", "D069892", "D070420"],
    featureFlags: ["DWCO_MODELING_VALIDATION_ENABLE_REMOTE_TABLES"],
  },
  "/validation/model/:space/:entity/validate": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.update }],
    responsible: ["D066022", "D069892", "D070420"],
    featureFlags: ["DWCO_MODEL_VALIDATION"],
  },
  "/validation/model/:space/:entity/validation-status": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.update }],
    responsible: ["D066022", "D069892", "D070420"],
    featureFlags: ["DWCO_MODEL_VALIDATION"],
  },
  "/genai-modeling/semantic-enrichment/:space": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["D066022", "D069892", "D060348"],
    featureFlags: ["DWCO_MODELING_GEN_AI_SEMANTIC_ENRICHMENT"],
  },
  "/genai-modeling/semantic-enrichment/:space/progress/:id": {
    spacecheck: { nameParameter: ":space", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["D066022", "D069892", "D060348"],
    featureFlags: ["DWCO_MODELING_GEN_AI_SEMANTIC_ENRICHMENT"],
  },
  "/ucl/tenant-mapping/:tenantid": {
    PATCH: [],
    responsible: ["I546644"],
  },
  "/transformationflow/:spaceid/hasremotetables": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    POST: [{ authorization: AuthType.DWC_DATABUILDER, activities: Activities.read }],
    responsible: ["I327609"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE"],
  },

  "/transformationflow/:spaceid/:transformationflowname/deployedobject": {
    spacecheck: { nameParameter: ":spaceid", access: AccessPrivilege.Read },
    GET: [{ authorization: AuthType.DWC_DATAINTEGRATION, activities: Activities.read }],
    responsible: ["I527227"],
    featureFlags: ["INFRA_DWC_TWO_TENANT_MODE", "DWCO_TRF_INPUT_PARAMETERS_SUPPORT"],
  },

  "/calm/csa": {
    POST: [], // Authorization check done through technical user and scope check
    responsible: ["I819684", "I868099"],
    featureFlags: [],
  },

  "/calm/healthmon": {
    POST: [], // Authorization check done through technical user and scope check
    responsible: ["I868099"],
  },

  "/support/bdc/onboarding": {
    POST: [], // Special technical user token check exists
    responsible: ["I540824", "I544481"],
    technical: {
      oAuthScopes: ["$XSAPPNAME.DWCSupport"],
    },
  },
  "/support/bdc/offboarding": {
    POST: [], // Special technical user token check exists
    responsible: ["I062999", "I544481"],
    technical: {
      oAuthScopes: ["$XSAPPNAME.DWCSupport"],
    },
    featureFlags: ["DWCO_BDC_GA"],
  },

  "/support/tenant/bdc/datareplicationstatus/:jobid": {
    // No privilege check on purpose, tech user has implicitly full privileges
    GET: [],
    responsible: ["I544481"],
    technical: {
      oAuthScopes: ["$XSAPPNAME.DWCSupport"],
    },
  },

  "/bdc/onboarding": {
    POST: [], // Special technical user token check exists
    responsible: ["I540824"],
  },
  "/bdc/offboarding": {
    POST: [], // Special technical user token check exists
    responsible: ["I062999"],
    featureFlags: ["DWCO_BDC_GA"],
  },
  "/bdc/packages": {
    GET: [{ authorization: AuthType.BDC_PACKAGES, activities: Activities.read }],
    responsible: ["I555958"],
    featureFlags: ["DWCO_BDC_COCKPIT"],
  },
  "/bdc/packages/setrepobranch": {
    POST: [{ authorization: AuthType.BDC_PACKAGES, activities: Activities.update }],
    responsible: ["I067779"],
    featureFlags: ["DWCO_BDC_COCKPIT"],
  },
  "/bdc/packages/:packageid": {
    GET: [{ authorization: AuthType.BDC_PACKAGES, activities: Activities.read }],
    responsible: ["D069852"],
    featureFlags: ["DWCO_BDC_COCKPIT"],
  },
  "/bdc/installedpackages": {
    GET: [{ authorization: AuthType.BDC_PACKAGES, activities: Activities.read }],
    POST: [{ authorization: AuthType.BDC_PACKAGES, activities: Activities.update }],
    responsible: ["I540824"],
    featureFlags: ["DWCO_BDC_COCKPIT"],
  },
  "/bdc/installedpackages/:installationid": {
    GET: [{ authorization: AuthType.BDC_PACKAGES, activities: Activities.read }],
    PUT: [{ authorization: AuthType.BDC_PACKAGES, activities: Activities.update }],
    DELETE: [{ authorization: AuthType.SYSTEMINFO, activities: Activities.update }],
    responsible: ["I540824"],
    featureFlags: ["DWCO_BDC_COCKPIT"],
  },
  "/bdc/installedpackages/:installationid/cancel": {
    POST: [{ authorization: AuthType.BDC_PACKAGES, activities: Activities.update }],
    responsible: ["I540824"],
    featureFlags: ["DWCO_BDC_COCKPIT"],
  },
  "/bdc/installedpackages/:installationid/selfhealingtimeout": {
    POST: [{ authorization: AuthType.BDC_PACKAGES, activities: Activities.update }],
    responsible: ["I067779"],
    featureFlags: ["DWCO_BDC_COCKPIT"],
  },
  "/bdc/uninstallpackage/:installationid": {
    DELETE: [{ authorization: AuthType.BDC_PACKAGES, activities: Activities.update }],
    responsible: ["I564876"],
    featureFlags: ["DWCO_BDC_COCKPIT", "DWCO_BDC_MANUAL_OFFBOARDING"],
  },
  "/bdc/datareplicationstatus/:jobid": {
    // No privilege check on purpose, tech user has implicitly full privileges
    GET: [],
    responsible: ["I544481"],
  },
  "/bdc/systemlandscape": {
    GET: [{ authorization: AuthType.BDC_PACKAGES, activities: Activities.read }],
    responsible: ["I557089"],
    featureFlags: ["DWCO_BDC_COCKPIT"],
  },
  "/bdc/ucl/tenant-mapping/:tenantid": {
    // protected by $XSAPPNAME.DWCUclIntegration scope
    PATCH: [],
    responsible: ["I819519"],
  },
  "/bdc/support/ucl/application-template": {
    // protected by special token
    GET: [],
    POST: [],
    PATCH: [],
    DELETE: [],
    responsible: ["I819519"],
  },
  "/bdc/support/packages": {
    // protected by special token
    GET: [],
    responsible: ["I583264"],
    technical: {
      oAuthScopes: ["$XSAPPNAME.DWCSupport"],
    },
    featureFlags: ["DWCO_BDC_CLI_FOR_BDC_PACKAGES"],
  },
  "/bdc/check-connection/:assignmentid": {
    GET: [{ authorization: AuthType.BDC_PACKAGES, activities: Activities.read + Activities.update }],
    responsible: ["I575743"],
    featureFlags: ["DWCO_BDC_COCKPIT"],
  },
};

export const endpointSchema: IEndPoints = {
  expressions,
  endpoints,
  rateLimiters: [],
  rateLimitingChains: {
    default: {
      state: rateLimiterState,
      rateLimiters: [
        {
          name: "defaultRequestLimiter",
          consumes: 1,
          type: RateLimiterTypes.REQUEST,
          options: {
            points: parseInt(process.env.RATE_LIMIT_MAX!, 10),
            duration: parseInt(process.env.RATE_LIMIT_WINDOW!, 10) / 1000,
          },
        },
        {
          name: "defaultSlowDownLimiter",
          type: RateLimiterTypes.SLOWDOWN,
          options: {
            windowMs: parseInt(process.env.RATE_LIMIT_SLOW_WINDOW!, 10),
            delayAfter: parseInt(process.env.RATE_LIMIT_SLOW_MAX!, 10),
            delayMs: 500,
            maxDelayMs: 2000,
          },
        },
      ],
    },
    configEndpoints: {
      state: State.DISABLED,
      rateLimiters: [],
    },
  },
};

export const ConfigurableEndpointSchema = new Configurable<IEndPoints>(
  endpointSchema,
  endpointsValidator(endpointSchema)
);
