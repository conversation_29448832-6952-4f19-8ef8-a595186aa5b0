{"openapi": "3.0.3", "info": {"title": "SAP Data Warehouse Cloud Command-Line Interface dwc Support Commands", "description": "This document describes the available support commands for the SAP Data Warehouse Cloud command-line interface dwc and CIC support UI.", "contact": {"name": "SAP SE", "url": "https://www.sap.com/products/data-warehouse-cloud/get-started.html"}, "license": {"name": "SAP FREEWARE LICENSE", "url": "https://tools.hana.ondemand.com/sap-freeware-license.txt"}, "version": "2021.23", "x-document-version": "2021.23"}, "tags": [{"name": "ops", "description": "Operations"}, {"name": "support", "description": "Support"}, {"name": "repository", "description": "Repository"}, {"name": "customerhana", "description": "Customer HANA"}, {"name": "lsa", "description": "HDLF and Spark"}, {"name": "embeddeddatalake", "description": "HDLF and Spark"}, {"name": "spaces", "description": "Spaces"}, {"name": "security", "description": "Security"}, {"name": "provision", "description": "Provisioning"}, {"name": "catalog", "description": "Catalog"}, {"name": "profile", "description": "Profiling"}, {"name": "ucl", "description": "UCL"}], "externalDocs": {"description": "\"Create, Read, Update, and Delete Spaces via the Command Line\" on help.sap.com", "url": "https://help.sap.com/docs/SAP_DATASPHERE/9f804b8efa8043539289f42f372c4862/5eac5b71e2d34c32b63f3d8d47a0b1d0.html"}, "paths": {"/health": {"get": {"operationId": "ops health", "tags": ["ops"], "summary": "Health", "responses": {"200": {"$ref": "#/components/responses/200"}, "900": {"description": "CustomerHanaUpgradeFailed"}, "901": {"description": "MdsVersionsFailed"}, "903": {"description": "EnvVarSACSuffixUrlNotAvailable"}, "904": {"description": "TmsDown"}, "911": {"description": "RepositoryHanaCloudDown"}, "912": {"description": "RepositoryTenantDatabaseTypeFailure"}, "918": {"description": "RepositoryCredentialStoreNotAccessible"}, "908": {"description": "CredStoreCannotFindCredentials"}, "909": {"description": "CredStoreDown"}, "910": {"description": "CredStoreHealthCheckDown"}, "913": {"description": "<PERSON>red<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "914": {"description": "DwcHdiDeploymentInfrastructureFailure"}, "916": {"description": "DeployerNotAvailable"}, "917": {"description": "DiServiceCheckFailure"}}}}, "/healthtenants": {"get": {"operationId": "ops healthtenants", "tags": ["ops"], "summary": "Health", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/bdc/healthtenants": {"get": {"x-tenant-types": ["BDC"], "tags": ["ops"], "operationId": "ops healthtenants", "summary": "Health", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/versions": {"get": {"operationId": "ops versions", "tags": ["ops"], "summary": "Versions", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/environment": {"get": {"operationId": "ops environment", "tags": ["ops"], "summary": "Environment Variables", "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/repository/consistency": {"get": {"operationId": "ops repo consistency1", "tags": ["ops", "repository"], "summary": "Repo Consistency ops", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/consistency": {"get": {"operationId": "support repo consistency2", "tags": ["ops", "repository"], "summary": "Repo Consistency support", "parameters": [{"in": "query", "name": "databaseInstance", "description": "deepsea repository db instance ID", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "0"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/repository/namespaces": {"post": {"operationId": "support repository namespaces for tenant", "tags": ["ops", "repository"], "summary": "Upsert repository namespaces for tenant", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"namespaceDefinition": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the namespace"}, "allowChildNamespaces": {"type": "boolean", "description": "The namespace allow child or not, optional and default value is false"}}}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}, "201": {"$ref": "#/components/responses/201"}}}, "get": {"operationId": "support repository unregistered namespaces for tenant", "tags": ["ops", "repository"], "summary": "Get unregistered or registered repository namespaces for tenant", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "query", "name": "unregistered", "description": "unregistered=true to get unregistered namespaces for this tenant", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "true"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/loghistory": {"get": {"operationId": "ops repo loghistory", "tags": ["ops", "repository"], "summary": "Repo Log History", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/jobs": {"get": {"operationId": "ops repo jobs", "tags": ["ops", "repository"], "summary": "Get information on all support jobs in the repository", "parameters": [{"in": "query", "name": "names", "description": "names: job name or comma-separated list of names", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "Tenant Recovery for 20a76535-ccb4-4033-9773-af4345f2b916"}}, {"in": "query", "name": "status", "description": "status: job status or comma-separated list of status", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "Pending,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,Failed,Aborted"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/jobs/{jobId}": {"get": {"operationId": "ops repo job", "tags": ["ops", "repository"], "summary": "Get information on a specific repository support job", "parameters": [{"in": "path", "name": "jobId", "description": "Identifier of the job", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "5736381fb8284bab8fc7dd0d467c208f"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/repository/jobs": {"get": {"operationId": "ops tenant repo jobs", "tags": ["ops", "repository"], "summary": "Get information on all support jobs for a tenant", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "query", "name": "names", "description": "names: job name or comma-separated list of names", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "Tenant Recovery for 20a76535-ccb4-4033-9773-af4345f2b916"}}, {"in": "query", "name": "operations", "description": "operations: job operation or comma-separated list of operations", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "tenantRecovery,tenantMove"}}, {"in": "query", "name": "status", "description": "status: job status or comma-separated list of status", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "Pending,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,Failed,Aborted"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/repository/jobs/{jobId}": {"get": {"operationId": "ops tenant repo job", "tags": ["ops", "repository"], "summary": "Get information on a specific repository support job for a tenant", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "path", "name": "jobId", "description": "Identifier of the job", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "5736381fb8284bab8fc7dd0d467c208f"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/databases": {"get": {"operationId": "ops repo get databases", "tags": ["ops", "repository"], "summary": "Databases - get information", "parameters": [{"in": "query", "name": "details", "description": "allows to get detailed information", "required": false, "allowEmptyValue": true, "schema": {"type": "string", "default": "all", "enum": ["all", "isPrimary", "isDefaultOnboardingInstance", "provisioningConfiguration"]}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/databases/{databaseId}": {"get": {"operationId": "ops repo get specific database", "tags": ["ops", "repository"], "summary": "Databases - get information on a specific database", "parameters": [{"in": "path", "name": "databaseId", "description": "Database instance ID (e.g. 0 for the primary repository database)", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}, "patch": {"operationId": "ops repo patch database", "tags": ["ops", "repository"], "summary": "Databases - patch database information", "parameters": [{"in": "path", "name": "databaseId", "description": "Database instance ID (e.g. 0 for the primary repository database)", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"provisioningConfiguration": {"type": "object", "description": "Database provisioning configuration", "properties": {"useServiceInstanceBasedProvisioning": {"type": "boolean", "description": "Use service instance based provisioning", "default": false}, "supportsHanaMultitenancy": {"type": "boolean", "description": "Is this database configured for HANA multitenancy", "default": false}, "enableHanaMultitenancy": {"type": "boolean", "description": "Is provisioning HANA native tenants enabled by default", "default": false}}}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/tenants": {"get": {"operationId": "ops repo tenants", "tags": ["ops", "repository"], "summary": "Get information on all tenants", "parameters": [{"in": "query", "name": "expand", "description": "expand: if true, details on tenants are returned", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/tenants/{tenantId}": {"get": {"operationId": "ops repo tenant", "tags": ["ops", "repository"], "summary": "Get information on a specific tenant", "parameters": [{"in": "path", "name": "tenantId", "description": "UUID of the tenant", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "7dbc51bd-cb7c-469b-8290-fef6ddedd2d9"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/tenantregistry": {"post": {"operationId": "ops repo tenant registry", "tags": ["ops", "repository"], "summary": "Tenant registry - repair or rebuild", "parameters": [{"in": "query", "name": "dryRun", "description": "When true, runs the request in dry run mode, no changes will be applied", "required": true, "allowEmptyValue": false, "schema": {"type": "boolean", "default": true}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "required": ["action"], "properties": {"action": {"type": "string", "description": "Action to perform on the tenant registry ('rebuild' or 'repair')", "enum": ["rebuild", "repair"], "default": "repair"}, "appendOnly": {"type": "boolean", "description": "When true, only append missing entries to the tenant registry (no modification/deletion of existing entries)", "default": true}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/tenantregistry/entries/{tenantId}": {"put": {"operationId": "ops repo upsert tenant registry entry", "tags": ["ops", "repository"], "summary": "Tenant registry - add or update a tenant entry", "parameters": [{"in": "path", "name": "tenantId", "description": "UUID of the tenant", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "7dbc51bd-cb7c-469b-8290-fef6ddedd2d9"}}, {"in": "query", "name": "dryRun", "description": "When true, runs the request in dry run mode, no changes will be applied", "required": true, "allowEmptyValue": false, "schema": {"type": "boolean", "default": true}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "required": ["tenantConfiguration"], "properties": {"tenantConfiguration": {"type": "object", "properties": {"databaseInstance": {"type": "number", "description": "Repository database instance ID", "example": 0, "default": 0}}}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}, "delete": {"operationId": "ops repo delete tenant registry entry", "tags": ["ops", "repository"], "summary": "Tenant registry - delete a tenant entry", "parameters": [{"in": "path", "name": "tenantId", "description": "UUID of the tenant", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "7dbc51bd-cb7c-469b-8290-fef6ddedd2d9"}}, {"in": "query", "name": "dryRun", "description": "When true, runs the request in dry run mode, no changes will be applied", "required": true, "allowEmptyValue": false, "schema": {"type": "boolean", "default": true}}], "responses": {"200": {"$ref": "#/components/responses/200"}, "204": {"$ref": "#/components/responses/204"}}}}, "/support/tenant/repository/operations/enterMaintenanceMode": {"post": {"operationId": "ops tenant repo enter maintenance mode", "tags": ["ops", "repository"], "summary": "Enter a tenant in maintenance mode", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "required": ["parameters"], "properties": {"parameters": {"type": "object", "properties": {"force": {"type": "boolean", "description": "if true, tenant will enter in selected maintenance mode even if it is already in maintenance.", "default": false}, "maintenanceMode": {"type": "string", "description": "Repository database instance ID", "enum": ["manual", "recovery"], "default": "manual"}}}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/repository/operations/exitMaintenanceMode": {"post": {"operationId": "ops tenant repo exit maintenance mode", "tags": ["ops", "repository"], "summary": "Exit a tenant from maintenance mode", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/repository/operations/recoverNativeTenant": {"post": {"operationId": "support tenant repo recover native tenant", "tags": ["support", "repository"], "summary": "Recover a native tenant in a point in time", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "required": ["parameters"], "properties": {"parameters": {"type": "object", "properties": {"force": {"type": "boolean", "description": "if true, tenant will enter recovery mode even if it is already in maintenance.", "default": false}, "recoverToTimestamp": {"type": "string", "description": "Specify the point in time (in UTC) up to which the database tenant will be restored, in ISO 8601 format (e.g., 2023-10-01T12:00:00Z).", "example": "2023-10-01T12:00:00Z"}}}}}}}}, "responses": {"200": {"$ref": "#/components/responses/202"}}}}, "/repository/auditadmin": {"get": {"operationId": "ops repo auditadmin", "tags": ["ops", "repository"], "summary": "Returns Database Admin User Audit information", "parameters": [{"in": "query", "name": "tenantId", "description": "The uuid of the tenant to audit", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "2438ba16-bcba-4f0d-a333-ceffc6949c3e"}}, {"in": "query", "name": "action", "description": "Supports only DROP SCHEMA value. If omitted, report all actions for a given tenant", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "DROP SCHEMA", "default": "DROP SCHEMA"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/supportuserscopeassignment": {"get": {"operationId": "support tenant support user scope assignment", "tags": ["support", "security"], "summary": "Assign support user to one or more spaces", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "query", "name": "email", "description": "Email address of the user", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "<EMAIL>"}}, {"in": "query", "name": "spaces", "description": "List of spaces to get access to, separated by comma without whitespace", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "Space1,Space2"}}], "responses": {"202": {"$ref": "#/components/responses/202"}}}}, "/support/broadcast": {"post": {"operationId": "ops broadcast", "tags": ["ops"], "summary": "Broadcast a notification to all users of all or a certain list of tenants.", "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "required": ["title", "body"], "properties": {"title": {"type": "string", "description": "Title of the broadcast notification"}, "body": {"type": "string", "description": "Body of the broadcast notification"}, "broadcastType": {"type": "string", "description": "Broadcast type ('Problem' or 'Resolution')", "enum": ["Problem", "Resolution"], "default": "Problem"}, "link": {"type": "string", "description": "Link (URL) for further external resources"}, "recipientTenantIds": {"type": "string", "description": "Comma separated IDs of the tenants that should receive the broadcast notification. If empty, all tenants will be the receipients."}, "validityTimeInHours": {"type": "number", "description": "Amount of hours before the notification expires", "default": 72}, "chunkSize": {"type": "number", "description": "Chunk size defining how many tenants are processed in parallel", "default": 40}, "delayBetweenChunksInMs": {"type": "number", "description": "Delay between the chunks (in ms)", "default": 1000}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/aifeatures/{featureid}/configstoredata": {"delete": {"operationId": "ops aifeatures delete", "tags": ["ops"], "summary": "Deletes the configuration data for a given AI Feature", "parameters": [{"in": "path", "name": "featureid", "description": "Feature ID for the AI feature", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "AI-LPR998-TRANSACTION-9999-45Q1"}}], "responses": {"202": {"$ref": "#/components/responses/202"}}}}, "/support/tenant/rotate-passwords": {"post": {"operationId": "ops password rotation", "tags": ["ops", "customerhana", "security"], "summary": "Rotates the passwords and certificates for technical users.", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"adminUser": {"type": "boolean", "description": "Status whether to rotate Admin and Activator credentials.", "default": false}, "userManager": {"type": "boolean", "description": "Status whether to rotate User Manager credentials.", "default": false}, "globalUsers": {"type": "boolean", "description": "Status whether to rotate global technical users credentials.", "default": false}, "spaceUsers": {"type": "boolean", "description": "Status whether to rotate space users credentials.", "default": false}, "bwBridgeUsers": {"type": "boolean", "description": "Status whether to rotate BW Bridge users credentials.", "default": false}, "jwtKeyPair": {"type": "boolean", "description": "Status whether to rotate JWT key pair.", "default": false}, "incremental": {"type": "boolean", "description": "Status whether to rotate user passwords incrementally.", "default": false}}}}}}, "responses": {"202": {"description": "Accepted", "content": {"text/plain": {"schema": {"type": "string", "example": "Request for password rotation accepted."}}}}}}}, "/support/minimum-log-level": {"post": {"operationId": "ops loglevel set", "tags": ["ops"], "summary": "Set Minimum Log Level", "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "required": ["minimumLogLevel"], "properties": {"minimumLogLevel": {"type": "number", "description": "New minimum log level (0 = Debug, 1 = Verbose, 2 = Info, 3 = Warn, 4 = Error)", "enum": [0, 1, 2, 3, 4], "default": 3}, "component": {"type": "string", "description": "The component which the log level will be set to. Place between slashes (/.*/) to create a regular expression."}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}, "get": {"operationId": "ops loglevel get", "tags": ["ops"], "summary": "Get Minimum Log Level", "parameters": [{"in": "query", "name": "component", "required": false, "schema": {"type": "string"}, "description": "The component to filter for retrieving the log level"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/minimum-log-level": {"post": {"x-tenant-types": ["DSP", "BDC"], "operationId": "ops loglevel tenant set", "tags": ["ops"], "summary": "Set Minimum Log Level", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "required": ["minimumLogLevel"], "properties": {"minimumLogLevel": {"type": "number", "description": "New minimum log level (0 = Debug, 1 = Verbose, 2 = Info, 3 = Warn, 4 = Error)", "enum": [0, 1, 2, 3, 4], "default": 3}, "component": {"type": "string", "description": "The component which the log level will be set to. Place between slashes (/.*/) to create a regular expression."}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}, "get": {"x-tenant-types": ["DSP", "BDC"], "operationId": "ops loglevel tenant get", "tags": ["ops"], "summary": "Get Minimum Log Level", "parameters": [{"in": "query", "name": "component", "required": false, "schema": {"type": "string"}, "description": "The component to filter for retrieving the log level"}, {"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/health": {"get": {"operationId": "support repository health", "tags": ["support", "repository"], "summary": "Repository Health", "parameters": [], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/show/space/consistency": {"get": {"operationId": "support space consistency", "tags": ["support", "spaces"], "summary": "Show Space Consistency", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "query", "name": "array", "description": "Space Names", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "default": "[]"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/sdp/conversion": {"put": {"operationId": "support sdp conversion", "tags": ["support", "security"], "summary": "SDP Conversion - in case of doubt leave everything to default", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "query", "name": "conversion", "description": "trigger SDP conversion - only then clean up needs to be false", "required": false, "allowEmptyValue": true, "schema": {"type": "boolean", "default": true}}, {"in": "query", "name": "cleanup", "description": "trigger tenant clean up for SDP conversion - only then conversion needs to be false", "required": false, "allowEmptyValue": true, "schema": {"type": "boolean", "default": true}}, {"in": "query", "name": "retries", "description": "how many rest calls should fail before termination", "required": false, "allowEmptyValue": true, "schema": {"type": "number", "default": 1}}, {"in": "query", "name": "chunkSize", "description": "in which chunks UMS will be called", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "default": 7000}}, {"in": "query", "name": "async", "description": "either sync or async call", "required": false, "allowEmptyValue": true, "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"enforceTenantsConversion": {"type": "array", "description": "Enforces tenant SDP conversion despite more then 200 spaces/scopes, provide a tenant id as string", "default": "[ ]"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repair/spaces": {"patch": {"operationId": "support space repair", "tags": ["support", "spaces"], "summary": "Repair Space", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "required": ["space"], "properties": {"space": {"type": "string", "description": "Space Name"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/repository/recovery": {"post": {"operationId": "support tenant recover", "tags": ["support", "repository"], "summary": "<PERSON><PERSON>", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"async": {"type": "boolean", "description": "Run the recovery process in the background", "default": true}, "allowMigrateDataThroughApplication": {"type": "boolean", "description": "Allow migrate data through application", "default": false}, "source": {"type": "object", "properties": {"host": {"description": "Host", "type": "string"}, "port": {"description": "Port", "type": "number"}, "user": {"description": "User", "type": "string"}, "password": {"description": "Password", "type": "string"}}}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}, "202": {"$ref": "#/components/responses/202"}}}}, "/support/tenant/repository/request-support-user": {"post": {"operationId": "support repository requestuser", "tags": ["support", "repository"], "summary": "Request Repository Support User", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["role", "userid"], "properties": {"role": {"type": "string", "description": "Role: can be DWC_SUP_RO, DWC_SUP_WT or DWC_SUP_EM", "enum": ["DWC_SUP_RO", "DWC_SUP_WT", "DWC_SUP_EM"]}, "userid": {"type": "string", "description": "User ID: the D/I number of the requestor"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/customerhana/request-support-user": {"post": {"operationId": "support customerhana requestuser", "tags": ["support", "customerhana"], "summary": "Request Customerhana Support User", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["role", "userid"], "properties": {"role": {"type": "string", "description": "Role: can be DWC_SUP_RO, DWC_SUP_WT or DWC_SUP_EM", "enum": ["DWC_SUP_RO", "DWC_SUP_WT", "DWC_SUP_EM"]}, "userid": {"type": "string", "description": "User ID: the D/I number of the requestor"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/embeddeddatalake/request-support-user": {"post": {"operationId": "support embeddeddatalake requestuser", "tags": ["support", "lsa", "embeddeddatalake"], "summary": "Request Embeddedatalake Support User", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["role", "userid", "spaceIds", "ingressAllowlist", "egressAllowlist"], "properties": {"role": {"type": "string", "description": "Role: can be DWC_SUP_RO, DWC_SUP_WT or DWC_SUP_EM", "enum": ["DWC_SUP_RO", "DWC_SUP_WT", "DWC_SUP_EM"]}, "userid": {"type": "string", "description": "User ID: the D/I number of the requestor"}, "spaceIds": {"type": "array", "description": "Provide a comma separated list of space identifiers (technical names) to request datalakefiles access"}, "ingressAllowlist": {"type": "array", "description": "Provide a comma separated list of ingress allow listed networks / ips"}, "egressAllowlist": {"type": "array", "description": "Provide a comma separated list of egress allow listed networks / ips"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/customerhana/fix-admin-user-inconsistency": {"post": {"operationId": "ops customerhana fixadminuserinconsistency", "tags": ["ops", "repository"], "summary": "Fix Customerhana admin user inconsistency", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/customerhana/fix-user-manager-inconsistency": {"post": {"operationId": "ops customerhana fixusermanagerinconsistency", "tags": ["ops", "customerhana"], "summary": "Fix Customer HANA user manager inconsistency", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/customerhana/force-upgrade": {"post": {"operationId": "ops customerhana forceupgrade", "tags": ["ops", "customerhana"], "summary": "Force  Customerhana upgrade", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}, "902": {"$ref": "#/components/responses/902"}}}}, "/support/tenant/customerhana/grant-datalake-role": {"post": {"operationId": "ops customerhana grant datalake role", "tags": ["ops", "customerhana", "provision"], "summary": "Grant Data Lake role to tenant owner", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/admin-api/api-instance": {"get": {"operationId": "ops get admin-api instance", "tags": ["ops", "lsa"], "summary": "Get Instance of Admin Api Access", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}, "delete": {"operationId": "ops delete admin-api instance", "tags": ["ops", "lsa"], "summary": "Deletes Instance of Admin Api Access", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/admin-api/provision-api-instance": {"post": {"operationId": "ops provision admin-api instance", "tags": ["ops", "lsa"], "summary": "Provisions an Instance of Admin Api Access", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/admin-api/renew-api-key": {"post": {"operationId": "ops renew admin-api instance key", "tags": ["ops", "lsa"], "summary": "Renews Admin Api Access instance key", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/lsa/spark-history-server": {"post": {"operationId": "ops provision spark history server", "tags": ["ops", "lsa"], "summary": "Provisions Apache Spark History Server", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "required": ["spaceId", "data"], "properties": {"spaceId": {"type": "string", "description": "SpaceId of the space that the instance will be created", "default": ""}, "data": {"type": "object", "properties": {"logStore": {"type": "string", "description": "logStore"}, "parentID": {"type": "string", "description": "parentID: id of Apache Spark Instance"}, "conf": {"type": "string", "description": "conf (optional)"}, "serviceStopped": {"type": "boolean", "description": "serviceStopped: true or false (optional)"}, "sparkVersion": {"type": "string", "description": "spark version (optional)"}}}}}}}}}, "get": {"operationId": "ops get apache spark history server instance parameters", "tags": ["ops", "lsa"], "summary": "Get Apache Spark History Server parameters", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "query", "name": "spaceId", "description": "space id", "required": true, "allowEmptyValue": false, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}, "delete": {"operationId": "ops deletes apache spark history server", "tags": ["ops", "lsa"], "summary": "Deletes an Apache Spark History Server instance", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"spaceId": {"type": "string", "description": "SpaceId of the space to delete the Apache Spark History Server instance"}}}}}}}}, "/support/lsa/allowlist": {"get": {"operationId": "ops object store space allowlist", "tags": ["ops", "lsa"], "summary": "Get IP allowlists from Object Store spaces", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/202"}}}, "post": {"operationId": "ops object store space allowlist", "tags": ["ops", "lsa"], "summary": "Post IP allowlists to Object Store space", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"202": {"$ref": "#/components/responses/202"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"spaceId": {"type": "string", "description": "Space name (in Datasphere)", "default": "", "example": ""}, "instanceName": {"type": "string", "description": "Instance Name (hashed space name)", "default": "", "example": ""}, "hdlfAllowlist": {"type": "array", "description": "Comma separated list for HDLF IPs", "default": "", "example": ""}, "sparkIngressAllowlist": {"type": "array", "description": "Comma separated list for Spark Ingress IPs", "default": "", "example": ""}, "sparkEgressAllowlist": {"type": "array", "description": "Comma separated list for Spark Egress IPs", "default": "", "example": ""}}}}}}}}, "/support/lsa/hdlf-instance": {"post": {"operationId": "ops object store hdlf instance update", "tags": ["ops", "lsa"], "summary": "Update HDLF instance based on SpaceId or Instance Name for tenant", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"202": {"$ref": "#/components/responses/202"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"spaceId": {"type": "string", "description": "Space name (in Datasphere)", "default": "", "example": ""}, "instanceName": {"type": "string", "description": "Instance Name (hashed space name)", "default": "", "example": ""}, "updatePayload": {"type": "object", "additionalProperties": false, "properties": {"authorizations": {"type": "array", "description": "HDLF authorizations", "default": [], "items": {"type": "object", "properties": {"pattern": {"type": "string", "description": "Authorization pattern"}, "rank": {"type": "number", "description": "Authorization rank"}, "roles": {"type": "array", "description": "Comma separated list of authorization roles"}}}}, "roles": {"type": "array", "description": "HDLF roles with privileges", "default": [], "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Role name"}, "privileges": {"type": "array", "description": "Comma separated list of role privileges"}}}}, "trusts": {"type": "array", "description": "HDLF trusts with certificate data", "default": [], "items": {"type": "object", "properties": {"alias": {"type": "string", "description": "Trust alias"}, "certData": {"type": "string", "description": "PEM formatted certificate"}}}}}}}}}}}}}, "/support/lsa/spark-instance": {"post": {"operationId": "ops object store spark instance update", "tags": ["ops", "lsa"], "summary": "Update Spark instance based on SpaceId or Instance Name for tenant", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"202": {"$ref": "#/components/responses/202"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"spaceId": {"type": "string", "description": "Space name (in Datasphere)", "default": "", "example": ""}, "instanceName": {"type": "string", "description": "Instance Name (hashed space name)", "default": "", "example": ""}, "updatePayload": {"type": "object", "additionalProperties": false, "properties": {"storage": {"type": "number", "description": "Storage for the Spark instance", "default": ""}, "memory": {"type": "number", "description": "Memory for the Spark instance", "default": ""}, "vcpu": {"type": "number", "description": "VCPU for the Spark instance", "default": ""}, "allowSparkConnect": {"type": "boolean", "description": "Allow spark connect, true or false", "default": "false"}, "authorizations": {"type": "array", "description": "Spark authorizations", "default": [], "items": {"type": "object", "properties": {"pattern": {"type": "string", "description": "Authorization pattern"}, "rank": {"type": "number", "description": "Authorization rank"}, "roles": {"type": "array", "description": "Comma separated list of authorization roles"}}}}, "roles": {"type": "array", "description": "Spark roles with privileges", "default": [], "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Role name"}, "privileges": {"type": "array", "description": "Comma separated list of role privileges"}}}}, "trusts": {"type": "array", "description": "Spark trusts with certificate data", "default": [], "items": {"type": "object", "properties": {"alias": {"type": "string", "description": "Trust alias"}, "certData": {"type": "string", "description": "PEM formatted certificate"}}}}}}}}}}}}}, "/support/lsa/deleteinstances": {"delete": {"operationId": "ops delete Spark and HDLF instances with status failed", "tags": ["ops", "lsa"], "summary": "Delete Spark and HDLF instances", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"202": {"$ref": "#/components/responses/202"}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/tenant/customerhana/alerts/events": {"get": {"operationId": "ops customerhana alertevents", "tags": ["ops", "customerhana"], "summary": "Customer <PERSON><PERSON>", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "query", "name": "alertState", "description": "alertState: filter alerts by the given alert state", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "ACTIVE", "default": "ACTIVE"}}, {"in": "query", "name": "alertRules", "description": "alertRules: filter alerts by the specified alert rule names. Multiple values can be specified split with comma(,)", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "HDBDiskUsage"}}, {"in": "query", "name": "severity", "description": "severity: filter alerts by the given severity values. Multiple values can be specified split with comma(,)", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "ERROR", "default": "ERROR"}}, {"in": "query", "name": "startTimestamp", "description": "startTimestamp: the start time of time range in which alerts occurred in ISO 8601 format", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "2020-01-02T15:04:05Z"}}, {"in": "query", "name": "endTimestamp", "description": "endTimestamp: end time of time range in which alerts occurred in ISO 8601 format", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "2020-01-02T15:04:05Z"}}, {"in": "query", "name": "$skip", "description": "$skip: number of items to skip", "required": false, "allowEmptyValue": false, "schema": {"type": "integer", "example": 0}}, {"in": "query", "name": "$top", "description": "$top: number of items to deliver", "required": false, "allowEmptyValue": false, "schema": {"type": "integer", "example": 10}}, {"in": "query", "name": "$orderby", "description": "$orderby: ordering rule for retrieving the results", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "startTimestamp desc"}}, {"in": "query", "name": "$select", "description": "$select: selection of the fields (comma separated) which are part of the schema SubaccountAlertEvent", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "serviceInstanceID,severity,alertRule"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/customerhana/alerts/rules": {"get": {"operationId": "ops customerhana alertrules", "tags": ["ops", "customerhana"], "summary": "Customer <PERSON><PERSON>", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "query", "name": "alertRules", "description": "alertRules: filter alert rules by the specified alert rule names", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "HDBDiskUsage"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/customerhana/metrics/definitions": {"get": {"operationId": "ops customerhana metricdefinitions", "tags": ["ops", "customerhana"], "summary": "Customer <PERSON><PERSON> Definitions", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "query", "name": "$skip", "description": "$skip: number of items to skip", "required": false, "allowEmptyValue": false, "schema": {"type": "integer", "example": 0}}, {"in": "query", "name": "$top", "description": "$top: number of items to deliver", "required": false, "allowEmptyValue": false, "schema": {"type": "integer", "example": 10}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/customerhana/metrics/values": {"get": {"operationId": "ops customerhana metricvalues", "tags": ["ops", "customerhana"], "summary": "Customer <PERSON><PERSON> Metric Values", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "query", "name": "names", "description": "names: list of metric names split with comma(,)", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "HDBMemoryUsed"}}, {"in": "query", "name": "startTimestamp", "description": "startTimestamp: start time stamp of time range by which metric values are returned", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "2020-01-02T15:04:05Z"}}, {"in": "query", "name": "endTimestamp", "description": "endTimestamp: end time stamp of time range by which metric values are returned", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "2020-01-02T15:04:05Z"}}, {"in": "query", "name": "aggregates", "description": "aggregates: list of aggregates of metric over time range, split with comma(,)", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "max"}}, {"in": "query", "name": "interval", "description": "interval: time interval over which aggregation is performed", "required": false, "allowEmptyValue": false, "schema": {"type": "integer", "example": 60}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/customerhana/update-hana-parameter": {"post": {"operationId": "ops customerhana update customer hana parameters", "tags": ["ops", "customerhana"], "summary": "Change update strategy for Customer HANA and enable/disable Script server", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"update_strategy": {"type": "string", "description": "Update strategy for customer HANA, with_restart or without_restart.", "default": ""}, "scriptserver": {"type": "boolean", "description": "Status HANA Script server, true or false.", "default": ""}}}}}}}}, "/support/tenant/customerhana/toggle-multi-az": {"post": {"operationId": "ops customerhana toggle multi az", "tags": ["ops", "customerhana"], "summary": "Toggle Multi AZ for Customer HANA", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"202": {"$ref": "#/components/responses/202"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"enable": {"type": "string", "description": "enable/disable", "default": "", "example": "enable"}}}}}}}}, "/support/telemetry/datasphere-metrics/credentials": {"post": {"operationId": "ops upsertDatasphereMetricsCredentials", "tags": ["ops"], "summary": "Upsert Datasphere Metrics Credentials", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["schemaName", "authType", "host", "port", "user"], "properties": {"schemaName": {"type": "string", "description": "Open SQL schema name"}, "authType": {"type": "string", "description": "Authentication type (e.g., 'Basic' or 'X509', case-insensitive)"}, "host": {"type": "string", "description": "Hostname"}, "port": {"type": "number", "description": "Port number"}, "user": {"type": "string", "description": "Username"}, "password": {"type": "string", "description": "Password for Basic authentication"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"type": "string"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}}}}}, "get": {"operationId": "ops telemetry getDatasphereMetricsCredentials", "tags": ["ops", "telemetry"], "summary": "Return Datasphere Metrics Credential in JSON format", "responses": {"200": {"$ref": "#/components/responses/200"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"type": "string"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/tenant/provision": {"post": {"operationId": "ops tenant provision", "tags": ["ops", "provision"], "summary": "(Re)provision tenant", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/hatakeover": {"patch": {"operationId": "support repository hatakeover", "tags": ["support", "repository"], "summary": "Perform repository hatakeover", "parameters": [{"in": "query", "name": "databaseInstance", "description": "deepsea repository db instance ID", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "0"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/bwbridge/create-instance": {"post": {"operationId": "ops Create an instance of BW Bridge in Canary landscape Only.", "tags": ["ops", "provision"], "summary": "BW Bridge: Create an instance (Canary landscape only)", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"instanceName": {"type": "string", "description": "instance name", "default": ""}, "servicePlan": {"type": "string", "description": "instance plan", "default": "saas_oem"}, "serviceName": {"type": "string", "description": "service name", "default": "abap-canary"}, "serviceParameters": {"type": "object", "description": "service parameters", "properties": {"addon_product_name": {"type": "string", "description": "addon product name", "default": "DWC BW4 SERVICE"}, "addon_product_version": {"type": "string", "description": "format <release>.<sp>.<patch> (optional)", "default": ""}, "admin_email": {"type": "string", "description": "admin email", "default": ""}, "admin_user_name": {"type": "string", "description": "admin user name (optional)", "default": ""}, "consumer_id": {"type": "string", "description": "TMS consumer ID", "default": ""}, "consumer_tenant_limit": {"type": "number", "description": "consumer tenant limit", "default": 0}, "description": {"type": "string", "description": "instance description or business name", "default": ""}, "is_development_allowed": {"type": "boolean", "description": "development allowed boolean", "default": true}, "parent_saas_appname": {"type": "string", "description": "bwbridge-<landscape>", "default": "", "example": "bwbridge-master"}, "parent_service_instance_guid": {"type": "string", "description": "parent service instance guid (optional)", "default": ""}, "parent_service_label": {"type": "string", "description": "parent service label (optional)", "default": ""}, "parent_service_parameters": {"type": "object", "description": "parent service parameters", "properties": {"saas_id": {"type": "string", "description": "dwaas-core approuter client ID, derived from landscape", "default": "", "example": "sb-dwaas-core-sac-orcamaster!t2320"}, "bridge_ui_url": {"type": "string", "description": "<tenant name>--<space name>.bwbridge.<domain>", "default": ""}, "dwcDisplayName": {"type": "string", "description": "tenant consumer account display name", "default": ""}, "dwcTenantUuid": {"type": "string", "description": "tenant uuid", "default": ""}}}, "sapsystemname": {"type": "string", "description": "sap system name", "default": "BB8"}, "size_of_persistence": {"type": "number", "description": "HCUs, derived from thresholdBWBridge1, DS00-1805", "default": ""}, "size_of_persistence_disk": {"type": "number", "description": "120 + 40 * size_of_persistance", "default": ""}, "size_of_runtime": {"type": "number", "description": "size of runtime", "default": 1}, "ui_host": {"type": "string", "description": "<tenant name>--<space name>.bwbridge.<domain>", "default": ""}}}}}}}}, "responses": {"202": {"$ref": "#/components/responses/202"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"type": "string"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/bwbridge/{instancename}": {"delete": {"operationId": "ops Delete an instance of BW Bridge in Canary landscape only", "tags": ["ops", "provision"], "summary": "BW Bridge: Delete an instance (Canary landscape only)", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "path", "name": "instancename", "description": "Bw Bridge Instance Name", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "BWBRIDGESPACE"}}], "responses": {"202": {"description": "Accepted", "content": {"application/json": {"schema": {"type": "object"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string", "example": "0"}}}}}}}, "/support/bwbridge/refresh": {"patch": {"operationId": "ops Sync and Refresh BW Bridge", "tags": ["ops", "provision"], "summary": "BW Bridge: Sync and Refresh BW Bridge", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/bwbridge/keyandconnection": {"post": {"operationId": "ops create BW bridge Key and Connection", "tags": ["ops", "provision"], "summary": "BW Bridge: Create key and connection", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"instanceId": {"type": "string", "description": "steampunk instance ID", "default": ""}, "subAccountId": {"type": "string", "description": "subaccount ID", "default": ""}}}}}}, "responses": {"202": {"$ref": "#/components/responses/202"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/tenant/customerhanahdi/containers": {"get": {"operationId": "support Return all metadata about HDI containers", "tags": ["support", "customerhana"], "summary": "HDI Containers: Return all metadata", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/tenant/customerhanahdi/cache": {"get": {"operationId": "support Get the deploy cache state", "tags": ["support", "customerhana"], "summary": "HDI Containers: get the deploy cache state", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}}}}}, "delete": {"operationId": "ops Clear the deployed cache", "tags": ["support", "customerhana"], "summary": "HDI Containers: Clear the deployed cache", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/tenant/ip-allowlist": {"get": {"operationId": "ops customerhana ip allowlist", "tags": ["ops", "customerhana"], "summary": "IP Allowlist: <PERSON><PERSON> current list", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}, "post": {"operationId": "ops customerhana ip allowlist", "tags": ["ops", "customerhana"], "summary": "IP Allowlist: Add new entries", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["ipList"], "properties": {"ipList": {"type": "string", "description": "Separate IPs with ;", "default": "", "example": "***********;***********/32"}}}}}}, "responses": {"202": {"$ref": "#/components/responses/202"}}}, "delete": {"operationId": "ops customerhana ip allowlist", "tags": ["ops", "customerhana"], "summary": "IP Allowlist: Delete the given entries", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["ipList"], "properties": {"ipList": {"type": "string", "description": "Separate IPs with ;", "default": "", "example": "***********;***********/32"}}}}}}, "responses": {"202": {"$ref": "#/components/responses/202"}}}}, "/support/tenant/ip-allowlist/check": {"post": {"operationId": "ops customerhana ip allowlist", "tags": ["ops", "customerhana"], "summary": "IP Allowlist: Verify if given IPs are allowlisted", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["ipList"], "properties": {"ipList": {"type": "string", "description": "Separate IPs with ;", "default": "", "example": "***********;***********/32"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/catalog/workload-class": {"get": {"operationId": "ops get tenant catalog user workload class", "tags": ["ops", "catalog"], "summary": "Catalog Workload Class Info", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}, "put": {"operationId": "ops put tenant catalog user workload class", "tags": ["ops", "catalog"], "summary": "Catalog Workload Class Put", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"CATEGORY": {"type": "string", "enum": ["PROD", "TRIAL", "HIGH", "PARTNER", "SUPPORT", "CUSTOM"], "description": "Category to change"}, "CUSTOM": {"type": "object", "properties": {"PRIORITY": {"type": "integer", "minimum": 0, "maximum": 9, "description": "Priority (0 - 9)"}, "TOTAL_STATEMENT_MEMORY_LIMIT": {"type": "number", "description": "Total Statement Memory limit"}, "TOTAL_STATEMENT_MEMORY_LIMIT_UNIT": {"type": "string", "enum": ["Percent", "Gigabyte"], "description": "Total Statement Memory limit Unit (Percent / Gigabyte)"}, "TOTAL_STATEMENT_THREAD_LIMIT": {"type": "number", "description": "Total Statement Thread limit"}, "TOTAL_STATEMENT_THREAD_LIMIT_UNIT": {"type": "string", "enum": ["Percent", "Counter"], "description": "Total Statement Thread limit Unit (Percent / Counter)"}, "ADMISSION_CONTROL_QUEUE_MEMORY_THRESHOLD": {"type": "number", "description": "Admission Control Queue Memory Threshold"}}}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}, "201": {"$ref": "#/components/responses/201"}}}}, "/support/tenant/repository/workload-class": {"get": {"operationId": "ops get tenant repository user workload class", "tags": ["ops", "repository"], "summary": "Repository Workload Class Info", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}, "put": {"operationId": "ops put tenant repository user workload class", "tags": ["ops", "repository"], "summary": "Repository Workload Class Put", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"CATEGORY": {"type": "string", "enum": ["PROD", "TRIAL", "HIGH", "PARTNER", "SUPPORT", "CUSTOM"], "description": "Category to change"}, "CUSTOM": {"type": "object", "properties": {"PRIORITY": {"type": "integer", "minimum": 0, "maximum": 9, "description": "Priority (0 - 9)"}, "TOTAL_STATEMENT_MEMORY_LIMIT": {"type": "number", "description": "Total Statement Memory limit"}, "TOTAL_STATEMENT_MEMORY_LIMIT_UNIT": {"type": "string", "enum": ["Percent", "Gigabyte"], "description": "Total Statement Memory limit Unit (Percent / Gigabyte)"}, "TOTAL_STATEMENT_THREAD_LIMIT": {"type": "number", "description": "Total Statement Thread limit"}, "TOTAL_STATEMENT_THREAD_LIMIT_UNIT": {"type": "string", "enum": ["Percent", "Counter"], "description": "Total Statement Thread limit Unit (Percent / Counter)"}, "ADMISSION_CONTROL_QUEUE_MEMORY_THRESHOLD": {"type": "number", "description": "Admission Control Queue Memory Threshold"}}}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}, "201": {"$ref": "#/components/responses/201"}}}}, "/support/tenant/catalog/workload-class-toggle": {"patch": {"operationId": "ops toggle usage of tenant catalog user workload class", "tags": ["ops", "catalog"], "summary": "Catalog Workload Class Toggle", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"ENABLED": {"type": "boolean", "description": "Enable", "default": true}}}}}}, "responses": {"204": {"$ref": "#/components/responses/204"}}}}, "/support/tenant/repository/workload-class-toggle": {"patch": {"operationId": "ops toggle usage of tenant repository user workload class", "tags": ["ops", "repository"], "summary": "Repository Workload Class Toggle", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"ENABLED": {"type": "boolean", "description": "Enable", "default": true}}}}}}, "responses": {"204": {"$ref": "#/components/responses/204"}}}}, "/support/tenant/catalog/workload-class-statistics": {"get": {"operationId": "ops get statistics of tenant catalog user workload class", "tags": ["ops", "catalog"], "summary": "Catalog Workload Class Statistics", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/repository/workload-class-statistics": {"get": {"operationId": "ops get statistics of tenant repository user workload class", "tags": ["ops", "repository"], "summary": "Repository Workload Class Statistics", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/workload-class": {"get": {"operationId": "ops get landscape user workload classes", "tags": ["ops", "repository"], "summary": "User Workload Class Info", "parameters": [{"in": "query", "name": "databaseInstance", "description": "deepsea repository db instance ID", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "0"}}, {"in": "query", "name": "userType", "description": "The Workload Class User Type", "required": false, "allowEmptyValue": true, "schema": {"type": "string", "enum": ["tenantUser", "tenantBackupUser", "catalogUser", "all"], "default": "all", "example": "tenantUser"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}, "patch": {"operationId": "ops patch landscape user workload classes", "tags": ["ops", "repository"], "summary": "User Workload Class Mass Change", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"databaseInstance": {"type": "string", "description": "deepsea repository db instance ID", "example": "0"}, "userType": {"type": "string", "description": "The Workload Class User Type", "enum": ["tenantUser", "tenantBackupUser", "catalogUser", "all"], "default": "all", "example": "tenantUser"}, "CATEGORY": {"type": "string", "enum": ["PROD", "TRIAL", "HIGH", "PARTNER", "SUPPORT", "CUSTOM"], "description": "Category to change"}, "PRIORITY": {"type": "integer", "minimum": 0, "maximum": 9, "description": "Priority (0 - 9)"}, "TOTAL_STATEMENT_MEMORY_LIMIT": {"type": "number", "description": "Total Statement Memory limit"}, "TOTAL_STATEMENT_MEMORY_LIMIT_UNIT": {"type": "string", "enum": ["Percent", "Gigabyte"], "description": "Total Statement Memory limit Unit (Percent / Gigabyte)"}, "TOTAL_STATEMENT_THREAD_LIMIT": {"type": "number", "description": "Total Statement Thread limit"}, "TOTAL_STATEMENT_THREAD_LIMIT_UNIT": {"type": "string", "enum": ["Percent", "Counter"], "description": "Total Statement Thread limit Unit (Percent / Counter)"}, "ADMISSION_CONTROL_QUEUE_MEMORY_THRESHOLD": {"type": "number", "description": "Admission Control Queue Memory Threshold"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/workload-class-toggle": {"patch": {"operationId": "ops toggle usage of landscape user workload classes", "tags": ["ops", "repository"], "summary": "User Workload Class Toggle", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"databaseInstance": {"type": "string", "description": "deepsea repository db instance ID", "example": "0"}, "userType": {"type": "string", "description": "The Workload Class User Type", "enum": ["tenantUser", "tenantBackupUser", "catalogUser", "all"], "default": "all", "example": "tenantUser"}, "ENABLED": {"type": "boolean", "description": "Enable", "default": true}}}}}}, "responses": {"204": {"$ref": "#/components/responses/204"}}}}, "/support/repository/workload-class-statistics": {"get": {"operationId": "ops get statistics of landscape user workload classes", "tags": ["ops", "repository"], "summary": "User Workload Class Statistics", "parameters": [{"in": "query", "name": "databaseInstance", "description": "deepsea repository db instance ID", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "0"}}, {"in": "query", "name": "userType", "description": "The Workload Class User Type", "required": false, "allowEmptyValue": true, "schema": {"type": "string", "enum": ["tenantUser", "tenantBackupUser", "catalogUser", "all"], "default": "all", "example": "tenantUser"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/workload-class-consistency": {"get": {"operationId": "ops get consistency report of landscape user workload classes", "tags": ["ops", "repository"], "summary": "User Workload Class Consistency", "parameters": [{"in": "query", "name": "databaseInstance", "description": "deepsea repository db instance ID", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "0"}}, {"in": "query", "name": "userType", "description": "The Workload Class User Type", "required": false, "allowEmptyValue": true, "schema": {"type": "string", "enum": ["tenantUser", "tenantBackupUser", "catalogUser", "all"], "default": "all", "example": "tenantUser"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/auditlog/state": {"post": {"operationId": "support auditlog state change", "tags": ["support"], "summary": "Change auditlog state", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "query", "name": "forceExecution", "description": "Add this query param to execute the support route in productive landscapes", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "example": "true"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"injectFailure": {"type": "boolean", "description": "When true will fail all audit log interactions"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/hana/query": {"get": {"operationId": "ops analytical queries", "tags": ["ops", "customerhana"], "summary": "Execute a selected set of pre-defined read-only queries on a provided customer HANA", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "query", "name": "from", "description": "Start time formatted as: yyyy-mm-dd HH:MM", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "2022-10-01 01:00:00"}}, {"in": "query", "name": "to", "description": "End time formatted as: yyyy-mm-dd HH:MM", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "2022-10-01 02:30:00"}}, {"in": "query", "name": "mid", "description": "Mid time formatted as: yyyy-mm-dd HH:MM", "required": false, "allowEmptyValue": true, "schema": {"type": "string", "example": "2022-10-01 02:30:00"}}, {"in": "query", "name": "filter", "description": "AND-chained categories to be used when filtering on which queries to actually execute", "required": false, "allowEmptyValue": false, "schema": {"type": "string", "example": "rca AND fast", "default": "rca AND fast"}}, {"in": "query", "name": "queries", "description": "comma separated list of predefined queries considered to get executed", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "sac,heapMemory,threadLock", "default": "all"}}, {"in": "query", "name": "spaceID", "description": "space ID", "required": true, "allowEmptyValue": true, "schema": {"type": "string"}}, {"in": "query", "name": "aggregateBy", "description": "semicolon separated list of columns to aggregate by", "required": true, "allowEmptyValue": true, "schema": {"type": "string"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/hana/configuration/selectstatistic": {"post": {"operationId": "ops hana parameter enable/disable", "tags": ["ops", "customerhana"], "summary": "Enable/disable hana parameter table_statistics_select_enabled", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "required": ["enableSelect"], "properties": {"enableSelect": {"type": "boolean", "description": "The parameter to enable/disable table_statistics_select_enabled", "example": "true"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/endpoints": {"get": {"operationId": "ops current endpoints", "tags": ["ops"], "summary": "Get Endpoint Status", "parameters": [{"in": "query", "name": "diff", "description": "diff only", "required": false, "allowEmptyValue": true, "schema": {"type": "boolean"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/endpoints/disable": {"post": {"operationId": "ops disable endpoint", "tags": ["ops"], "summary": "Disable Endpoint", "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"endpoint": {"type": "string", "description": "endpoint as defined in endpoints.ts to get disabled"}, "method": {"type": "string", "description": "http method"}, "message": {"type": "string", "description": "technical error message used for the disabled endpoint"}, "statusCode": {"type": "string", "description": "http status code used for the disabled endpoint"}, "tenantIds": {"type": "string", "description": "comma separated list of tenant ids to get disabled"}, "dryRun": {"type": "boolean", "description": "dry run", "default": false}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/endpoints/reset": {"post": {"operationId": "ops reset endpoints", "tags": ["ops"], "summary": "Reset Endpoint Status", "parameters": [{"in": "query", "name": "dryRun", "description": "dry run", "required": false, "allowEmptyValue": true, "schema": {"type": "boolean"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/redis/status": {"get": {"operationId": "ops redis status", "tags": ["ops"], "summary": "Redis Status", "parameters": [], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/redis/slowlog": {"get": {"operationId": "ops redis slowlog", "tags": ["ops"], "summary": "Redis Slow Log Entries", "parameters": [], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/redis/flushall": {"post": {"operationId": "ops redis flush cache", "tags": ["ops"], "summary": "<PERSON><PERSON>", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/cache/invalidate": {"post": {"operationId": "ops cache invalidate", "tags": ["ops"], "summary": "AsyncCache Invalidate", "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"cacheName": {"type": "string", "description": "Cache Name -- should match to a cache defined in AsyncCacheNames.ts"}, "keys": {"type": "string", "description": "List of comma separated keys to be invalidated. If empty whole cache is cleared."}, "clearRedis": {"type": "boolean", "description": "If true also clears out Redis cache"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/redis/cache/{key}": {"get": {"operationId": "ops redis get cache value", "tags": ["ops"], "summary": "Get <PERSON><PERSON> Cache Value by Key", "parameters": [{"in": "path", "name": "key", "description": "<PERSON><PERSON>", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "spaceNameId:02da35ed-4897-4e21-9223-4e64b08f7ce9.SPACE"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}, "delete": {"operationId": "ops redis get cache value", "tags": ["ops"], "summary": "Clear Redis Cache entry by Key", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "path", "name": "key", "description": "<PERSON><PERSON>", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "spaceNameId:02da35ed-4897-4e21-9223-4e64b08f7ce9"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/onboard-client": {"post": {"operationId": "ops onboard repository client", "tags": ["ops", "repository"], "summary": "Run Repository Client Onboard", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/featureflag/flushflags": {"post": {"operationId": "ops flush feature flags", "tags": ["ops"], "summary": "Flush Feature Flag Cache on Redis for a given tenant", "description": "Flush Feature Flag Cache on Redis", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}}}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/oneagent/logs/{logType}": {"get": {"operationId": "ops oneagent logs", "tags": ["ops"], "summary": "Get One Agent Logs", "parameters": [{"in": "path", "name": "logType", "description": "Determines which log file will be fetched. One of installer, nodejs", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "nodejs"}}, {"in": "query", "name": "instance", "description": "Application instance filter", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/profile/cpu": {"get": {"operationId": "ops profile cpu", "tags": ["ops", "profile"], "summary": "Profile CPU", "parameters": [{"in": "query", "name": "durationMs", "description": "Duration in MS to take the CPU profile. Should be smaller than 2 minutes to fit the CIC gateway timeout.", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "60000"}}, {"in": "query", "name": "instance", "description": "Application instance filter", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/profile/memory/heapprofile": {"get": {"operationId": "ops profile heap", "tags": ["ops", "profile"], "summary": "Profile Heap", "parameters": [{"in": "query", "name": "durationMs", "description": "Duration in MS to take the heap profile. Should be smaller than 2 minutes to fit the CIC gateway timeout.", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": 60000}}, {"in": "query", "name": "instance", "description": "Application instance filter", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": 0}}, {"in": "query", "name": "samplingInterval", "description": "After how many bytes of allocation a sample is taken", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "default": 32768}}, {"in": "query", "name": "includeObjectsCollectedByMajorGC", "description": "if profile also includes information about objects freed by major GC while profile was running", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "includeObjectsCollectedByMinorGC", "description": "if profile also includes information about objects freed by minor GC while profile was running", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/profile/memory/heapsnapshot": {"get": {"operationId": "ops profile heapsnapshot", "tags": ["ops", "profile"], "summary": "Heapsnapshot", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "disabledHTTPHealthCheck", "description": "See https://jira.tools.sap/browse/DW101-69051", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "true"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/profile/memory/heapstatistics": {"get": {"operationId": "ops profile heapstatistics", "tags": ["ops", "profile"], "summary": "Heapstatistics", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/profile/memory/heapspacestatistics": {"get": {"operationId": "ops profile heapspacestatistics", "tags": ["ops", "profile"], "summary": "Heapspacestatistics", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/profile/memory/gctracesenable": {"post": {"operationId": "ops profile gctracesenable", "tags": ["ops", "profile"], "summary": "Garbage Collection Traces Enable", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/profile/memory/gctracesdisable": {"post": {"operationId": "ops profile gctracesdisable", "tags": ["ops", "profile"], "summary": "Garbage Collection Traces Disable", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/profile/eventloopblockers": {"post": {"operationId": "ops profile eventloopblockers manual", "tags": ["ops", "profile"], "summary": "Profile Event Loop Blockers Manual Start/Stop", "description": "Profile Event Loop Blockers Manual Start/Stop", "parameters": [{"in": "query", "name": "action", "description": "\"enable\" to enable event loop blocker detection or \"disable\" to disable event loop blocker detection", "required": true, "allowEmptyValue": false, "schema": {"type": "string"}}, {"in": "query", "name": "sampleFrequency", "description": "Sample every \"sampleFrequency\" async task. sampleFrequency = 0 enables async hooks but as no-op. samplefrequency = 1 will sample every async task", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "10"}}, {"in": "query", "name": "threshold", "description": "Execution threshold for async resources to be logged as an event loop blocker", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "400"}}, {"in": "query", "name": "collectFullStack", "description": "If disabled, only stack trace at init of \"task\" is tracked. If enabled, also tracks stack trace at init of \"taskBefore\" given \"taskBefore\" created \"task\" (more expensive)", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "collectMicroTaskBlockers", "description": "If micro task blockers should be collected (more expensive)", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "instance", "description": "Application instance to only execute the event loop blocker profiling", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}], "responses": {"202": {"$ref": "#/components/responses/202"}, "200": {"$ref": "#/components/responses/200"}}}, "get": {"operationId": "ops profile eventloopblockers", "tags": ["ops", "profile"], "summary": "Profile Event Loop Blockers", "description": "Profile Event Loop Blockers", "parameters": [{"in": "query", "name": "duration", "description": "Duration in MS to take the event loop blockers profile. Should be smaller than 2 minutes to fit the CIC gateway timeout.", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "60000"}}, {"in": "query", "name": "threshold", "description": "Execution threshold for async resources to be logged as an event loop blocker", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "400"}}, {"in": "query", "name": "sampleFrequency", "description": "Sample every \"sampleFrequency\" async task. sampleFrequency = 0 enables async hooks but as no-op. samplefrequency = 1 will sample every async task", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "10"}}, {"in": "query", "name": "collectFullStack", "description": "If disabled, only stack trace at init of \"task\" is tracked. If enabled, also tracks stack trace at init of \"taskBefore\" given \"taskBefore\" created \"task\" (more expensive)", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "collectMicroTaskBlockers", "description": "If micro task blockers should be collected (more expensive)", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "instance", "description": "Application instance to only execute the event loop blocker profiling", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea/profile/cpu": {"get": {"operationId": "ops profile cpu for deepsea", "tags": ["ops", "profile"], "summary": "Profile CPU for deepsea", "parameters": [{"in": "query", "name": "durationMs", "description": "Duration in MS to take the CPU profile. Should be smaller than 2 minutes to fit the CIC gateway timeout.", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "60000"}}, {"in": "query", "name": "instance", "description": "Application instance filter is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea/profile/memory/heapprofile": {"get": {"operationId": "ops profile heap for deepsea", "tags": ["ops", "profile"], "summary": "Profile Heap for deepsea", "parameters": [{"in": "query", "name": "durationMs", "description": "Duration in MS to take the heap profile. Should be smaller than 2 minutes to fit the CIC gateway timeout.", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": 60000}}, {"in": "query", "name": "instance", "description": "Application instance filter(mandatory parameter)", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": 0}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}, {"in": "query", "name": "samplingInterval", "description": "After how many bytes of allocation a sample is taken", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "default": 32768}}, {"in": "query", "name": "includeObjectsCollectedByMajorGC", "description": "if profile also includes information about objects freed by major GC while profile was running", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "includeObjectsCollectedByMinorGC", "description": "if profile also includes information about objects freed by minor GC while profile was running", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea/profile/memory/heapsnapshot": {"get": {"operationId": "ops profile heapsnapshot for deepsea", "tags": ["ops", "profile"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> for deepsea", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter(mandatory parameter)", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea/profile/memory/heapstatistics": {"get": {"operationId": "ops profile heapstatistics for deepsea", "tags": ["ops", "profile"], "summary": "Heapstatistics for deepsea", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter(mandatory parameter)", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea/profile/memory/heapspacestatistics": {"get": {"operationId": "ops profile heapspacestatistics for deepsea", "tags": ["ops", "profile"], "summary": "Heapspacestatistics for deepsea", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter(mandatory parameter)", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea/profile/memory/gctracesenable": {"post": {"operationId": "ops profile gctracesenable for deepsea", "tags": ["ops", "profile"], "summary": "Garbage Collection Traces Enable for deepsea", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter(mandatory parameter)", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea/profile/memory/gctracesdisable": {"post": {"operationId": "ops profile gctracesdisable for deepsea", "tags": ["ops", "profile"], "summary": "Garbage Collection Traces Disable for deepsea", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter(mandatory parameter)", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea/profile/eventloopblockers": {"post": {"operationId": "ops profile eventloopblockers manual for deepsea", "tags": ["ops", "profile"], "summary": "Profile Event Loop Blockers Manual Start/Stop for deepsea", "description": "Profile Event Loop Blockers Manual Start/Stop for deepsea", "parameters": [{"in": "query", "name": "action", "description": "\"enable\" to enable event loop blocker detection or \"disable\" to disable event loop blocker detection", "required": true, "allowEmptyValue": false, "schema": {"type": "string"}}, {"in": "query", "name": "sampleFrequency", "description": "Sample every \"sampleFrequency\" async task. sampleFrequency = 0 enables async hooks but as no-op. samplefrequency = 1 will sample every async task", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "10"}}, {"in": "query", "name": "threshold", "description": "Execution threshold for async resources to be logged as an event loop blocker", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "400"}}, {"in": "query", "name": "collectFullStack", "description": "If disabled, only stack trace at init of \"task\" is tracked. If enabled, also tracks stack trace at init of \"taskBefore\" given \"taskBefore\" created \"task\" (more expensive)", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "collectMicroTaskBlockers", "description": "If micro task blockers should be collected (more expensive)", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "instance", "description": "Application instance to only execute the event loop blocker profiling", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"202": {"$ref": "#/components/responses/202"}, "200": {"$ref": "#/components/responses/200"}}}, "get": {"operationId": "ops profile eventloopblockers for deepsea", "tags": ["ops", "profile"], "summary": "Profile Event Loop Blockers for deepsea", "description": "Profile Event Loop Blockers for deepsea", "parameters": [{"in": "query", "name": "duration", "description": "Duration in MS to take the event loop blockers profile. Should be smaller than 2 minutes to fit the CIC gateway timeout.", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "60000"}}, {"in": "query", "name": "threshold", "description": "Execution threshold for async resources to be logged as an event loop blocker", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "400"}}, {"in": "query", "name": "sampleFrequency", "description": "Sample every \"sampleFrequency\" async task. sampleFrequency = 0 enables async hooks but as no-op. samplefrequency = 1 will sample every async task", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "10"}}, {"in": "query", "name": "collectFullStack", "description": "If disabled, only stack trace at init of \"task\" is tracked. If enabled, also tracks stack trace at init of \"taskBefore\" given \"taskBefore\" created \"task\" (more expensive)", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "collectMicroTaskBlockers", "description": "If micro task blockers should be collected (more expensive)", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "instance", "description": "Application instance to only execute the event loop blocker profiling", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea-daemon/profile/cpu": {"get": {"operationId": "ops profile cpu for deepsea-daemon", "tags": ["ops", "profile"], "summary": "Profile CPU for deepsea-daemon", "parameters": [{"in": "query", "name": "durationMs", "description": "Duration in MS to take the CPU profile. Should be smaller than 2 minutes to fit the CIC gateway timeout.", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "60000"}}, {"in": "query", "name": "instance", "description": "Application instance filter is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea-daemon/profile/memory/heapprofile": {"get": {"operationId": "ops profile heap for deepsea-daemon", "tags": ["ops", "profile"], "summary": "Profile Heap for deepsea-daemon", "parameters": [{"in": "query", "name": "durationMs", "description": "Duration in MS to take the heap profile. Should be smaller than 2 minutes to fit the CIC gateway timeout.", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": 60000}}, {"in": "query", "name": "instance", "description": "Application instance filter(mandatory parameter)", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": 0}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}, {"in": "query", "name": "samplingInterval", "description": "After how many bytes of allocation a sample is taken", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "default": 32768}}, {"in": "query", "name": "includeObjectsCollectedByMajorGC", "description": "if profile also includes information about objects freed by major GC while profile was running", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "includeObjectsCollectedByMinorGC", "description": "if profile also includes information about objects freed by minor GC while profile was running", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea-daemon/profile/memory/heapsnapshot": {"get": {"operationId": "ops profile heapsnapshot for deepsea-daemon", "tags": ["ops", "profile"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON> for deepsea-daemon", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter(mandatory parameter)", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea-daemon/profile/memory/heapstatistics": {"get": {"operationId": "ops profile heapstatistics for deepsea-daemon", "tags": ["ops", "profile"], "summary": "Heapstatistics for deepsea-daemon", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter(mandatory parameter)", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea-daemon/profile/memory/heapspacestatistics": {"get": {"operationId": "ops profile heapspacestatistics for deepsea-daemon", "tags": ["ops", "profile"], "summary": "Heapspacestatistics for deepsea-daemon", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter(mandatory parameter)", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea-daemon/profile/memory/gctracesenable": {"post": {"operationId": "ops profile gctracesenable for deepsea-daemon", "tags": ["ops", "profile"], "summary": "Garbage Collection Traces Enable for deepsea-daemon", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter(mandatory parameter)", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea-daemon/profile/memory/gctracesdisable": {"post": {"operationId": "ops profile gctracesdisable for deepsea-daemon", "tags": ["ops", "profile"], "summary": "Garbage Collection Traces Disable for deepsea-daemon", "parameters": [{"in": "query", "name": "instance", "description": "Application instance filter(mandatory parameter)", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/deepsea-daemon/profile/eventloopblockers": {"post": {"operationId": "ops profile eventloopblockers manual for deepsea-daemon", "tags": ["ops", "profile"], "summary": "Profile Event Loop Blockers Manual Start/Stop for deepsea-daemon", "description": "Profile Event Loop Blockers Manual Start/Stop for deepsea-daemon", "parameters": [{"in": "query", "name": "action", "description": "\"enable\" to enable event loop blocker detection or \"disable\" to disable event loop blocker detection", "required": true, "allowEmptyValue": false, "schema": {"type": "string"}}, {"in": "query", "name": "sampleFrequency", "description": "Sample every \"sampleFrequency\" async task. sampleFrequency = 0 enables async hooks but as no-op. samplefrequency = 1 will sample every async task", "required": true, "allowEmptyValue": false, "schema": {"type": "number", "example": "10"}}, {"in": "query", "name": "threshold", "description": "Execution threshold for async resources to be logged as an event loop blocker", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "400"}}, {"in": "query", "name": "collectFullStack", "description": "If disabled, only stack trace at init of \"task\" is tracked. If enabled, also tracks stack trace at init of \"taskBefore\" given \"taskBefore\" created \"task\" (more expensive)", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "collectMicroTaskBlockers", "description": "If micro task blockers should be collected (more expensive)", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "instance", "description": "Application instance to only execute the event loop blocker profiling", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"202": {"$ref": "#/components/responses/202"}, "200": {"$ref": "#/components/responses/200"}}}, "get": {"operationId": "ops profile eventloopblockers for deepsea-daemon", "tags": ["ops", "profile"], "summary": "Profile Event Loop Blockers for deepsea-daemon", "description": "Profile Event Loop Blockers for deepsea-daemon", "parameters": [{"in": "query", "name": "duration", "description": "Duration in MS to take the event loop blockers profile. Should be smaller than 2 minutes to fit the CIC gateway timeout.", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "60000"}}, {"in": "query", "name": "threshold", "description": "Execution threshold for async resources to be logged as an event loop blocker", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "400"}}, {"in": "query", "name": "sampleFrequency", "description": "Sample every \"sampleFrequency\" async task. sampleFrequency = 0 enables async hooks but as no-op. samplefrequency = 1 will sample every async task", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "10"}}, {"in": "query", "name": "collectFullStack", "description": "If disabled, only stack trace at init of \"task\" is tracked. If enabled, also tracks stack trace at init of \"taskBefore\" given \"taskBefore\" created \"task\" (more expensive)", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "collectMicroTaskBlockers", "description": "If micro task blockers should be collected (more expensive)", "required": false, "allowEmptyValue": false, "schema": {"type": "boolean", "default": false}}, {"in": "query", "name": "instance", "description": "Application instance to only execute the event loop blocker profiling", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}, {"in": "query", "name": "application_id", "description": "Application Id is a mandatory parameter for the target service (deepsea or deepsea-daemon).", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "aa97b177-9383-4934-8543-0f97a03a"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/configurations/parameters/{paramKey}/effectiveValue": {"get": {"operationId": "ops fetch configuration value", "tags": ["ops", "provision"], "summary": "Read configuration value from the Configuration Service or its DWC cache", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "path", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Name of the parameter", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "DWC_TEST_PARAM_DEPLOYMENT_AND_TENANT"}}, {"in": "query", "name": "tenantId", "description": "Tenant ID -- leave empty to fetch the landscape wide fallback value", "required": false, "allowEmptyValue": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/configurations/parameters/{paramKey}/values": {"post": {"operationId": "ops upsert configuration value", "tags": ["ops", "provision"], "summary": "Write configuration value to the Configuration Service", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "path", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Name of the parameter", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "DWC_TEST_PARAM_DEPLOYMENT_AND_TENANT"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"tenantId": {"type": "string", "description": "Tenant ID -- should match the one in the header since a tenant support token can only alter tenant-scoped values"}, "value": {"type": "string", "description": "New value for the parameter"}}}}}}, "responses": {"204": {"$ref": "#/components/responses/204"}}}}, "/support/consistency/orchestrator/meta": {"get": {"operationId": "support consistency orchestrator meta", "tags": ["support"], "summary": "Get a list of storages of the consistency framework orchestrator", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/consistency/orchestrator/report": {"post": {"operationId": "support consistency orchestrator report", "tags": ["support"], "summary": "Get a report of the consistency framework orchestrator", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"spaceId": {"type": "string", "description": "Space ID (keep empty for all spaces)"}, "flatStorages": {"type": "string", "description": "Storages (delimited with comma, keep empty for all storages)"}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/di-e/tenant/rmspodcount": {"get": {"operationId": "support di embedded rms pod count", "tags": ["support"], "summary": "Get the DI Embedded RMS Pod Count", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/{space}/deploy/cleanupmdsmetadata": {"post": {"operationId": "support clean up mds metadata in space", "tags": ["support", "spaces"], "summary": "Clean up inconsistent MDS metadata in a single space", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "path", "name": "space", "description": "Space ID", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "E2E_AUTO_BL"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/workflows": {"get": {"operationId": "support get workflows", "tags": ["support"], "summary": "Get workflows list or workflow status by uuid", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "query", "name": "workflowId", "description": "Uuid or empty for list all workflows", "required": false, "allowEmptyValue": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/service-instance": {"get": {"operationId": "support get service instances", "tags": ["support"], "summary": "Get Service Instances list or Service Instance status by id", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "query", "name": "instanceId", "description": "id for full parameters or empty for list all service instances", "required": false, "allowEmptyValue": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/license/allocated-cu": {"get": {"operationId": "support get tenant allocated cu", "tags": ["support"], "summary": "Get allocated Capacity Units for tenant based on license", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/ratelimiting/state": {"get": {"operationId": "ops ratelimiting state", "tags": ["ops"], "summary": "Get rate limit status", "parameters": [{"in": "query", "name": "method", "description": "HTTP method", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "default": "GET"}}, {"in": "query", "name": "endpoint", "description": "url path to identify an endpoint", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "default": ""}}, {"in": "query", "name": "instance", "description": "Application instance to only execute the request on a given instance", "required": false, "allowEmptyValue": false, "schema": {"type": "number", "example": "0"}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/repository/alltenantsinformation": {"get": {"operationId": "support get repository allTenantsInformation", "tags": ["support", "repository"], "summary": "Repository support route to get information for all tenants", "parameters": [{"in": "query", "name": "info", "description": "Type of information requested. One of: sdpMigrationState, sdpSpaceStats, schemaVersion, customerContexts, tenantStats", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "sdpMigrationState"}}, {"in": "query", "name": "async", "description": "If set to true, runs the process in the background", "required": false, "allowEmptyValue": true, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"$ref": "#/components/responses/200"}, "202": {"$ref": "#/components/responses/202"}}}}, "/support/delete/spaces": {"delete": {"operationId": "support delete spaces for technical users", "tags": ["support", "spaces"], "summary": "support delete spaces for technical users [This endpoint allows deleting permanently one ore more spaces, providing a list of space identifiers (technical names)]", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "required": ["spaceIds"], "properties": {"spaceIds": {"type": "array", "description": "Provide a comma separated list of space identifiers (technical names) to delete"}, "opt": {"type": "object", "properties": {"forceDelete": {"type": "boolean", "description": "The forceDelete option is set as true by default, will clean up all leftovers on that space. But, when setting this prop to false, might leave some leftovers behind of that space", "default": true}}}}}}}}, "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/ucl/formation": {"get": {"operationId": "support get ucl formation list", "tags": ["support", "ucl"], "summary": "Get UCL Formations for specific DSP Tenant", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/ucl/application-template": {"get": {"operationId": "support get ucl application templates", "tags": ["support", "ucl"], "summary": "Get DSP UCL Application Templates list by region", "parameters": [{"in": "query", "name": "region", "description": "application template region (optional)", "required": false, "allowEmptyValue": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}, "post": {"operationId": "support post ucl application template", "tags": ["support", "ucl"], "summary": "Onboard DSP UCL Application Template", "responses": {"200": {"$ref": "#/components/responses/200"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"registerPayload": {"type": "string", "description": "application template register payload (optional)", "default": ""}}}}}}}, "delete": {"operationId": "support delete ucl application template", "tags": ["support", "ucl"], "summary": "Delete DSP UCL Application Template", "parameters": [{"in": "query", "name": "id", "description": "application template ID (optional)", "required": false, "allowEmptyValue": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}, "patch": {"operationId": "support update ucl application template", "tags": ["support", "ucl"], "summary": "Update DSP UCL Application Template with updatePayload", "parameters": [{"in": "query", "name": "id", "description": "application template ID (optional)", "required": false, "allowEmptyValue": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"$ref": "#/components/responses/200"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"updatePayload": {"type": "string", "description": "application template update payload", "default": ""}}}}}}}}, "/support/bdc/ucl/system-removal-validation": {"post": {"operationId": "support bdc ucl exclusion from BDC Cockpit formation", "tags": ["support", "ucl"], "summary": "UCL tenant for exclusion from BDC Cockpit formation", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"tenantId": {"type": "string", "description": "ID of tenant to offboard from formation"}, "tenants": {"type": "array", "description": "List of tenants in formation", "items": {"type": "object", "properties": {"applicationNamespace": {"type": "string"}, "applicationTenantId": {"type": "string", "description": "Application tenant ID"}, "uclSystemName": {"type": "string", "description": "UCL system name"}, "uclSystemTenantId": {"type": "string", "description": "UCL system tenant ID"}}}}, "formation": {"type": "object", "properties": {"uclFormationTypeId": {"type": "string", "description": "UCL formation type ID"}, "uclFormationId": {"type": "string", "description": "UCL formation ID"}, "uclFormationName": {"type": "string", "description": "UCL formation name"}}}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "properties": {"errors": {"type": "array", "default": []}}}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/tenant/metering/report-insightapps": {"post": {"x-tenant-types": ["BDC"], "tags": ["support"], "operationId": "support report installed packages", "summary": "Metering Insight Apps", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/bdc/support/ucl/application-template": {"get": {"operationId": "support get bdc ucl application templates", "tags": ["support", "ucl"], "summary": "Get BDC UCL Application Templates list by region", "parameters": [{"in": "query", "name": "region", "description": "application template region (optional)", "required": false, "allowEmptyValue": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}, "post": {"operationId": "support post bdc ucl application template", "tags": ["support", "ucl"], "summary": "Onboard BDC UCL Application Template", "responses": {"200": {"$ref": "#/components/responses/200"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"registerPayload": {"type": "string", "description": "application template register payload (optional)", "default": ""}}}}}}}, "delete": {"operationId": "support delete bdc ucl application template", "tags": ["support", "ucl"], "summary": "Delete BDC UCL Application Template", "parameters": [{"in": "query", "name": "id", "description": "application template ID (optional)", "required": false, "allowEmptyValue": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"$ref": "#/components/responses/200"}}}, "patch": {"operationId": "support update bdc ucl application template", "tags": ["support", "ucl"], "summary": "Update BDC UCL Application Template with updatePayload", "parameters": [{"in": "query", "name": "id", "description": "application template ID (optional)", "required": false, "allowEmptyValue": true, "schema": {"type": "string", "default": ""}}], "responses": {"200": {"$ref": "#/components/responses/200"}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"updatePayload": {"type": "string", "description": "application template update payload", "default": ""}}}}}}}}, "/bdc/support/packages": {"get": {"x-tenant-types": ["BDC"], "operationId": "support bdc get all packages", "tags": ["support"], "summary": "Get BDC Packages", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/compute/v1/nodes/{id}": {"delete": {"operationId": "support deprovision elastic compute node", "tags": ["support", "customerhana"], "summary": "Deprovision an elastic compute node", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "path", "name": "id", "description": "Identifier of the elastic compute node", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "dsecn1"}}], "responses": {"204": {"$ref": "#/components/responses/204"}}}}, "/support/compute/v1/nodes/{id}/unassign/{spaceid}": {"put": {"operationId": "support unassign elastic compute node space", "tags": ["support", "customerhana"], "summary": "Remove space assignment from a given elastic compute node", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "path", "name": "id", "description": "Identifier of the elastic compute node", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "dsecn1"}}, {"in": "path", "name": "spaceid", "description": "Space identifier", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "SPACE_1234"}}], "responses": {"201": {"$ref": "#/components/responses/201"}}}}, "/support/metering/check-metering-availability/{paramKey}": {"get": {"operationId": "support check metering configuration store value", "tags": ["support", "profile"], "summary": "Read configuration value from the Configuration Service or its DWC cache from all tenants and returns if it's enabled or not", "parameters": [{"in": "path", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Name of the parameter", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "DWC_METERING_REPORT_ENABLEMENT"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/metering/report-insightapps": {"post": {"tags": ["support", "profile"], "operationId": "support report bdc insight apps", "summary": "Report BDC Billing Metrics", "responses": {"200": {"$ref": "#/components/responses/200"}}}}, "/support/tenant/customerhana/flexible-configuration/metadata/clear": {"post": {"operationId": "ops support", "tags": ["ops", "customerhana"], "summary": "Erase FTC Metadata from Space Metadata table.", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "properties": {"cleanTag": {"type": "boolean", "description": "Creates TMS workflow to remove tag flexibleTenant_under_configuration.", "default": "false"}, "cleanMetadata": {"type": "boolean", "description": "Erase FTC Metadata from Space Metadata table.", "default": "false"}}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/tenant/customerhana/flexible-configuration/metadata": {"get": {"operationId": "support get ftc metadata for tenant", "tags": ["support", "customerhana"], "summary": "Get FTC Metadata from Space Metadata table.", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"200": {"$ref": "#/components/responses/200"}, "500": {"description": "Internal Server Error", "content": {"text/plain": {"schema": {"type": "string"}}}}}}}, "/support/tenant/tf/systemschedules/status/{pauseorresume}": {"put": {"operationId": "support update activation status of system task schedule", "tags": ["support"], "summary": "Update the activation status of an existing system task schedule to PAUSE or RESUME the schedule", "parameters": [{"$ref": "#/components/parameters/TenantId"}, {"in": "path", "name": "pause<PERSON><PERSON>ume", "description": "Action to be performed: pause/resume", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "pause"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "required": ["applicationId", "activity"], "properties": {"applicationId": {"type": "string", "description": "The applicationId of the system task schedule", "example": "PASSWORD_ROTATION"}, "activity": {"type": "string", "description": "The activity of the system task schedule", "example": "CUSTOMER_HANA_ROTATE"}}}}}}, "responses": {"204": {"$ref": "#/components/responses/204"}}}}, "/support/tenant/tf/systemschedules/edit": {"put": {"operationId": "support edit cron of system task schedule", "tags": ["support"], "summary": "Update the CRON value of an existing system task schedule in the table TASK_SCHEDULES and set a ROLLBACK_TIME in the TASK_SYSTEM_SCHEDULES table", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": false, "required": ["applicationId", "activity", "cron", "rollbackTime"], "properties": {"applicationId": {"type": "string", "description": "The applicationId of the system task schedule", "example": "PASSWORD_ROTATION"}, "activity": {"type": "string", "description": "The activity of the system task schedule", "example": "CUSTOMER_HANA_ROTATE"}, "cron": {"type": "string", "description": "The cron value to be set for the system task schedule", "example": "5 20 * * *"}, "rollbackTime": {"type": "string", "description": "The time set to future when the temporary cron value change is rolled back to its original value", "example": "2021-06-01T00:00:00Z"}}}}}}, "responses": {"204": {"$ref": "#/components/responses/204"}}}}, "/support/tenant/token-exchanger-configuration": {"post": {"operationId": "support IAS OIDC token exchanger configuration", "tags": ["support"], "summary": "Configures the token exchanger with the provided logical DSP client credentials and IAS URL. This also support updating the configuration.", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"logicalDspClientId": {"type": "string", "description": "Logical DSP Client ID", "example": "20e28186-30cc-4533-99f2-a742a647e5d9"}, "logicalDspClientSecret": {"type": "string", "description": "Logical DSP Client Secret", "example": "********************************"}, "iasUrl": {"type": "string", "description": "IAS URL(Please include protocol)", "example": "https://iasexample.accounts400.ondemand.com"}}, "required": ["logicalDspClientId", "logicalDspClientSecret", "iasUrl"]}}}}, "responses": {"204": {"$ref": "#/components/responses/204"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Missing required parameters"}}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Token exchanger configuration failed"}}}}}}}}, "delete": {"operationId": "support removal of IAS OIDC token exchanger configuration", "tags": ["support"], "summary": "Removes the token exchanger configuration including removing the logical DSP client credentials and IAS URL from the securestore", "parameters": [{"$ref": "#/components/parameters/TenantId"}], "responses": {"204": {"$ref": "#/components/responses/204"}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Token exchanger configuration deletion failed"}}}}}}}}}}, "components": {"parameters": {"TenantId": {"name": "x-sap-boc-tenant-id", "in": "header", "description": "Tenant GUID", "required": true, "allowEmptyValue": false, "schema": {"type": "string", "example": "2438ba16-bcba-4f0d-a333-ceffc6949c3e"}}}, "responses": {"200": {"description": "Ok", "content": {"application/json": {"schema": {"type": "object"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"type": "object"}}}}, "202": {"description": "Accepted", "content": {"application/json": {"schema": {"type": "object"}}}}, "204": {"description": "NoContent", "content": {"application/json": {"schema": {"type": "object"}}}}, "902": {"description": "NotDwcEnabled", "content": {"application/json": {"schema": {"type": "object"}}}}}}}