/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [DSP General]
 */

// Usage to be removed by Deepsea Migration - Space
// eslint-disable-next-line no-restricted-imports
import { IRequestContext } from "@sap/deepsea-types";
import { AuditLogClient, IAuditConfigurationChangeMessage } from "@sap/dwc-audit-logger";
import { ISecureStore } from "@sap/dwc-credentials";
import { AuthenticationMode, httpClient } from "@sap/dwc-http-client";
import { promiseMap } from "@sap/dwc-promise-utils";
import { SacClient } from "@sap/dwc-sac-client";
import { cfServiceCredentials } from "@sap/xsenv";
import { NextFunction, Request, RequestHandler, Response, Router } from "express";
import Status, { StatusCodes } from "http-status-codes";
import { AnalyticsProvisioningService } from "../../aps/AnalyticsProvisioningService";
import { centralCircuitBreakerManager } from "../../circuitbreaker";
import { store as configStore } from "../../configurations/main";
import { HaumDataTransferTask } from "../../haum/task/HaumDataTransferTask";
import { DbClient } from "../../lib/DbClient";
import { LogLevel, getLogger } from "../../logger";
import { IClientOptions } from "../../meta/access";
import { MeteringService } from "../../metering/MeteringService";
import {
  postSteampunkConnectionCustomerHanaToSteampunkHanaFinish,
  postSteampunkConnectionCustomerHanaToSteampunkHanaPrep,
} from "../../provisioning/bwBridge/SteampunkmTLS";
import { postSteampunkDatabaseConnection as postSolaceSteampunkDatabaseConnection } from "../../provisioning/bwBridge/steampunkApi";
import {
  fetchUpgradeCredstoreInstance,
  isUpgradeRequired,
  sendSolaceTenantUpgradeQueueMessages,
  updateLatestVersionInCredstore,
} from "../../provisioning/upgrade/upgradePublisherUtil";
import { DeepseaClientFactoryUtils } from "../../repository/client/deepseaClientFactoryUtils";
import { ExternalCallCategory } from "../../repository/security/common/externalCallTypes";
import { RequestContext } from "../../repository/security/requestContext";
import { RepositorySupport } from "../../repository/security/users/supportUser";
import { getAuditLogClient } from "../../reuseComponents/auditlogger/audit-log-utils";
import { IUpgradeTenantObjectsOptions, provisionSystems } from "../../reuseComponents/onboarding/src";
import { SpaceMigrationJob } from "../../reuseComponents/onboarding/src/SpaceMigrationJob";
import {
  activateAdminUser,
  checkAndFixAdminUserInconsistency,
  deactivateAdminUser,
} from "../../reuseComponents/onboarding/src/adminActivator";
import { CustomerHana } from "../../reuseComponents/spaces/src";
import {
  UpgradeTriggerStatus,
  checkAndFixUserManagerInconsistency,
  upgradeCustomerHana,
} from "../../reuseComponents/spaces/src/getUpgradeCustomerHanaMiddleware";
import { getSystemCredentials } from "../../reuseComponents/spaces/src/tenantUsers";
import { getTenantUaaServiceEnv } from "../../reuseComponents/utility/uaaHelper";
import { CodedError, sendErrorResponse } from "../../server/errorResponse";
import { editSchedule, pauseOrResumeSchedule } from "../../task/controllers/systemSchedulesController";
import { ensureRegisteredWithUCL, uclRegistration } from "../../ucl";
import { validateTenantForDeletion } from "../../ucl/bdcc";
import { toAsyncRouter } from "../async-router";
import { DeepseaProxy } from "../repository/deepseaProxy";
import { updateDataRepositoryProperty } from "../sac";
import { AbapBridgeEndpoint } from "../security/abapBridgeEndpointProxy";
import { deleteAIFeatureConfigData } from "../security/aiFeatures";
import {
  allowlistSupportRouteCheck,
  allowlistSupportRouteDelete,
  allowlistSupportRouteGet,
  allowlistSupportRoutePost,
} from "../security/ipAllowlist";
import { SdpConversion } from "../space/SdpConversion";
import { LsSmSupport } from "../space/ls/lsSmSupport";
import { getSdpStateBeforeConversion, validateAfterConversion } from "../space/sdpConversionValidation";
import { SdpConversionOptions } from "../space/types";
import {
  deleteApiInstance,
  getAdminApiInstance,
  provisionApiInstance,
  renewAdminApiKey,
} from "./adminApiAccessProvisioning";
import { addFailedScopeCheckAuditMessage, changeAuditLogStatus } from "./audit-log";
import { postBackupReplicationRoute } from "./backup";
import { broadcastNotification } from "./broadcast";
import {
  createConnectionAndKeyRoute,
  createInstanceBwBridgeRoute,
  deleteInstanceBwBridgeRoute,
  syncRefreshInstanceBwBridgeRoute,
} from "./bwBridgeRoutes";
import { invalidateCache, invalidateCacheEndpointsEnabled } from "./cache/index";
import { checkMeteringAvailabilityRoute } from "./checkMeteringAvailability";
import {
  getCircuitBreakerInfoRoute,
  isCircuitBreakerTestEnvironment,
  putCircuitBreakerStateRoute,
  testCircuitBreakerRoute,
} from "./circuitbreaker";
import {
  configurableEndpointsEnabled,
  disableEndpoint,
  getEndpoints,
  patchEndpoints,
  resetEndpoints,
} from "./configurable-endpoints";
import * as configurations from "./configurations";
import { clearConnectionPoolsRoute } from "./connectionPool";
import { router as consistencyRouter } from "./consistency";
import {
  cleanTagAndMetadataRoute,
  getFtcMetadataRoute,
  toggleMultiAZForCustomerHANARoute,
  updateCustomerHanaParameterRoute,
} from "./customerHanaElasticity";
import {
  clearDeployHdiCacheStatusRoute,
  getDeployHdiCacheStatusRoute,
  getDeployedHdiContainersInfo,
} from "./customerHanaHdi";
import { getDiERmsPodCount, updateDiERmsPodCount } from "./diEPodCount";
import { deleteComputeNode, unassignSpaceComputeNode } from "./elasticComputeNode";
import { getSupportUserScopeAssignment } from "./extendedViewer";
import { flushFeatureFlags } from "./featureFlagService";
import { getBwBridgeConnectionRoute } from "./getBwBridgeConnectionBySpace";
import { getCredentials } from "./getCredentials";
import { getTenantAllocatedCuRoute } from "./getTenantAllocatedCu";
import { grantDataLakeRoleRoute } from "./grantDataLakeRole";
import { setSelectEnabledStatistics } from "./hana/configuration/monitoring";
import { executeHanaQueries } from "./hana/hana-query-executor";
import {
  getAlertEventsRoute,
  getAlertRulesRoute,
  getMetricDefinitionsRoute,
  getMetricValuesRoute,
} from "./hanaMonitoringAPI";
import { getLargeSystemsAllowlistRoute, updateLargeSystemsAllowlistRoute } from "./largeSystemsAllowlist";
import { deleteSparkAndHDLFInstancesRoute } from "./largeSystemsDeleteInstances";
import { installLSAPluginRoute } from "./largeSystemsPlugin";
import {
  deleteSparkHistoryServer,
  getSparkHistoryServerParameters,
  provisionSparkHistoryServer,
} from "./largeSystemsSparkProvisioning";
import { updateHdlfInstanceRoute, updateSparkInstanceRoute } from "./largeSystemsUpdate";
import { changeMinimumLogLevel, getMinimumLogLevelHandler } from "./logging";
import { cleanupMDSMetadata } from "./mds";
import { metricStatusHandler } from "./metricStatus";
import { getOneAgentLogs } from "./oneagent";
import * as passwordRotationUtil from "./passwordRotationUtil";
import * as CpuV2 from "./profiling/cpuV2";
import * as EventLoopBlockersV2 from "./profiling/eventLoopBlockersV2";
import { checkInstanceInBody } from "./profiling/eventLoopBlockersV2";
import * as MemoryV2 from "./profiling/memoryV2";
import { getRateLimitingStatus } from "./ratelimiter/rate-limit";
import {
  deleteRedisEntryByKey,
  flushRedisCacheRoute,
  getRedisSlowlogRoute,
  getRedisStatusRoute,
  getRedisValueByKey,
} from "./redis";
import { reportBdcBillingMetricsLandscape } from "./reportBdcBillingMetricsLandscape";
import { reportInsightApps } from "./reportInsightApps";
import { repositoryOnboardingClientRoute } from "./repositoryOnboardingActivities";
import { registerRepositorySupportApi } from "./repositorySupportApi";
import { getServiceInstancesRoute } from "./serviceInstances";
import { Consistency } from "./spaces/consistency";
import { DeleteSpaces } from "./spaces/deleteSpaces";
import { RepairSpace } from "./spaces/repairSpace";
import { IIssueCode, ISpaceRepairOptions } from "./spaces/types";
import { getSupportDiscovery } from "./supportDiscoveryHandler";
import { datasphereMetricsCredentials, getDatasphereMetricsCredentials } from "./telemetryDatasphereMetrics";
import { deleteTokenExchanger, setTokenExchanger } from "./tokenExchangerConfiguration";
import {
  deleteUclApplicationTemplateRoute,
  getUclApplicationTemplateRoute,
  getUclFormationRoute,
  patchUclApplicationTemplateRoute,
  postUclApplicationTemplateRoute,
} from "./uclApplicationTemplate";
import { getVersions } from "./versions";
import { getWorkflowsRoute } from "./workflows";
const { log, logInfo, logError } = getLogger("ROUTES_SUPPORT");

export const router = toAsyncRouter(Router());

// Expose process metrics
export * as processMetrics from "./profiling/process.metrics";

registerRepositorySupportApi(router);

router.use("/consistency", consistencyRouter);

export function blockNonSupportUsersMiddleware() {
  return async (req: Request, res: Response, next: () => void) => {
    // Only allow technical users to call support routes

    try {
      if (!req.context.userInfo.technical) {
        const auditLogger = getAuditLogClient();
        await auditLogger.securityMessage(`Access to support route ${req.path} without technical token`, req.context);
        return sendErrorResponse(req.context, "nonSupportUserBlocked", {
          status: Status.FORBIDDEN,
          details: {
            message: "Non-support users are blocked on this route.",
          },
        });
      }
      req.context.preventCustomerHanaAutoUpgrade = true;
      next();
    } catch (err) {
      return sendErrorResponse(req.context, "errorBlockingSupportMiddleware", {
        err,
        status: Status.INTERNAL_SERVER_ERROR,
      });
    }
  };
}

/**
 * Checks if a query parameter "instance" is set and if it corresponds to the
 * instance receiving the request. If they don't match return 422 (unprocessable entity).
 *
 * This is mostly a workaround when support routes are called via CIC and a specific instance
 * should be the target of the request.
 */
export function checkInstance(req: Request, res: Response, next: NextFunction) {
  if (req.query.instance && req.query.instance !== process.env.CF_INSTANCE_INDEX) {
    return res.status(Status.UNPROCESSABLE_ENTITY).send();
  }
  return next();
}

router.get("/versions", async (req: Request, res: Response) => {
  try {
    log(LogLevel.Warning, `Request versions information...`, { context: req.context });
    const versions = await getVersions(req.context);
    log(LogLevel.Warning, `Versions information ${JSON.stringify(versions)}`, { context: req.context });
    res.status(Status.OK).send(versions);
  } catch (err) {
    sendErrorResponse(req.context, "getVersionsFailed", { err });
  }
});

// Setup to satisfy endpoints.ts check
router.get("/minimum-log-level", getMinimumLogLevelHandler);
router.get("/tenant/minimum-log-level", getMinimumLogLevelHandler);

/**
 * Check the logLevel form a e.g. passed REST call and
 * checks whether it is part of the LogLevel.
 * Returns the current and previous log level and utc time,
 * if available otherwise undefined.
 */
// Setup to satisfy endpoints.ts check
router.post("/minimum-log-level", changeMinimumLogLevel);
router.post("/tenant/minimum-log-level", changeMinimumLogLevel);

router.post("/tenant/customerhana/request-support-user", async (req: Request, res: Response) => {
  let auditMessage: IAuditConfigurationChangeMessage | undefined;
  try {
    const auditLogger = getAuditLogClient();
    auditMessage = await auditLogger.configurationChange(
      {
        type: "RequestSupportUser",
        attributes: [
          {
            name: "role",
            old: "",
            new: req.body.role,
          },
        ],
      },
      req.context
    );
    if (req.body == null || req.body.role == null) {
      throw new CodedError("missingParameters", "'role' needs to be specified in the body.", Status.BAD_REQUEST);
    }

    const dbClientOptions: Partial<IClientOptions> = {
      exclusive: true,
      skipCircuitBreaker: true,
    };
    const customerHana = await CustomerHana.fromRequestContext(req.context, dbClientOptions);
    const result = await customerHana.createSupportUser(req.body.role, 1, req.body.userid);
    logInfo(`Customer HANA support user is requested with role '${req.body.role}'.`, { context: req.context });
    await auditMessage.success();
    res.send(result);
  } catch (err) {
    await auditMessage?.failure();
    sendErrorResponse(req.context, "createSupportUserFailed", { err });
  }
});

router.post("/tenant/embeddeddatalake/request-support-user", async (req: Request, res: Response) => {
  let auditMessage: IAuditConfigurationChangeMessage | undefined;
  const context = req?.context;
  let spaceIds: string[] | undefined;
  try {
    const role = req?.body?.role;
    const userid = req?.body?.userid;
    spaceIds = Array.from((getSpaceIdsFromRequest(req) as Set<string>).values());
    const ingressAllowlist = Array.from((getSetFromRequest(req, "ingressAllowlist") as Set<string>).values());
    const egressAllowlist = Array.from((getSetFromRequest(req, "egressAllowlist") as Set<string>).values());
    const isEnabled = true;

    const auditLogger = getAuditLogClient();
    auditMessage = await auditLogger.configurationChange(
      {
        type: "RequestEmbeddeddatalakeSupportUser",
        attributes: [
          {
            name: "role",
            old: "",
            new: role,
          },
        ],
      },
      context
    );
    if (role == null) {
      throw new CodedError("missingParameters", "'role' needs to be specified in the body.", Status.BAD_REQUEST);
    }

    const lsSmSupport = new LsSmSupport(context);
    const userCertificateChain = await lsSmSupport.manageSupportUser({
      role,
      userid,
      clientCertificateChain: null,
      spaceIds,
      ingressAllowlist,
      egressAllowlist,
      isEnabled,
    });

    logInfo(`Embedded Data Lake support user is requested with role '${role}'.`, { context });
    await auditMessage.success();
    res.send(userCertificateChain);
  } catch (err) {
    await auditMessage?.failure();
    sendErrorResponse(context, "createSupportUserFailed", { err });
  }
});

function checkTenantId(req: Request, res: Response, next: NextFunction) {
  const tenantId = req.context.userInfo.tenantId;
  if (!tenantId) {
    res.status(Status.BAD_REQUEST).send({
      code: "missingTenantId",
      message: "Tenant id is required",
    });
  }
  return next();
}

router.post(
  "/tenant/backup/replication/:operation",
  checkTenantId,
  postBackupReplicationRoute as unknown as RequestHandler
);
router.post("/tenant/token-exchanger-configuration", checkTenantId, setTokenExchanger as unknown as RequestHandler);
router.delete(
  "/tenant/token-exchanger-configuration",
  checkTenantId,
  deleteTokenExchanger as unknown as RequestHandler
);

router.post("/tenant/repository/rotate-backup-user", async (req: Request, res: Response) => {
  try {
    const response = await httpClient.call({
      url: SacClient.getSacUrl("deepsea", "/support/tenant/repository/request-backup-user"),
      opts: {
        method: "POST",
        body: {
          forceRotatePassword: true,
        },
        authentication: AuthenticationMode.Uaa,
        callCategory: ExternalCallCategory.Deepsea,
        requestContext: req.context,
      },
    });
    res.status(Status.ACCEPTED).send(response.body);
  } catch (err) {
    sendErrorResponse(req.context, "rotateRepoBackupUserFailed", { err });
  }
});

router.get("/tenant/customerhana/alerts/events", checkTenantId, getAlertEventsRoute);
router.get("/tenant/customerhana/alerts/rules", checkTenantId, getAlertRulesRoute);
router.get("/tenant/customerhana/metrics/values", checkTenantId, getMetricValuesRoute);
router.get("/tenant/customerhana/metrics/definitions", checkTenantId, getMetricDefinitionsRoute);

router.post("/tenant/customerhana/fix-admin-user-inconsistency", async (req: Request, res: Response) => {
  try {
    const dbClientOptions: Partial<IClientOptions> = {
      exclusive: true,
      skipCircuitBreaker: true,
    };
    const requestPassword = req.body.password ?? null;
    const fixUsers = await checkAndFixAdminUserInconsistency(req.context, requestPassword, dbClientOptions);
    const auditLogClient = getAuditLogClient();
    await auditLogClient.securityMessage(fixUsers, req.context);
    res.send(fixUsers);
  } catch (err) {
    sendErrorResponse(req.context, "Failed fixing admin user.", { err });
  }
});

router.post("/tenant/customerhana/fix-user-manager-inconsistency", async (req: Request, res: Response) => {
  try {
    const dbClientOptions: Partial<IClientOptions> = {
      exclusive: true,
      skipCircuitBreaker: true,
    };
    const fixed = await checkAndFixUserManagerInconsistency(req.context, dbClientOptions);

    const auditLogClient = getAuditLogClient();
    await auditLogClient.securityMessage(
      fixed
        ? "JWT authentication store has been reset successfully and user manager entry succesfully updated in CF Credstore."
        : "The User Manager had no authentication errors during the check. There is no need to reset JWT authentication store.",
      req.context
    );

    res.send({
      code: fixed ? "userManagerFixed" : "userManagerAlreadyWorking",
      details: {
        message: fixed
          ? "JWT authentication store has been reset successfully and user manager entry succesfully updated in CF Credstore."
          : "The User Manager had no authentication errors during the check. There is no need to reset JWT authentication store.",
      },
    });
  } catch (err) {
    sendErrorResponse(req.context, "fixUserManagerFailed", { err });
  }
});

router.post("/tenant/customerhana/force-upgrade", async (req: Request, res: Response) => {
  const migrateSpacesOnly = req.body?.migrateSpacesOnly || false;
  if (!migrateSpacesOnly) {
    await forceTenantUpgrade(req, res);
  } else {
    await forceSpaceMigration(req, res);
  }
});

async function forceTenantUpgrade(req: Request, res: Response) {
  const auditLogClient = getAuditLogClient();
  let auditMessage: IAuditConfigurationChangeMessage | undefined;
  const dbClientOptions: Partial<IClientOptions> = {
    exclusive: true,
    skipCircuitBreaker: true,
  };

  try {
    auditMessage = await auditLogClient.configurationChange(
      {
        type: "ForceTenantUpgrade",
        attributes: [{ name: "tenantId", new: `${req.context.userInfo.tenantId}`, old: "" }],
      },
      req.context
    );

    const upgradeOptions: IUpgradeTenantObjectsOptions = {
      force: true,
    };

    const minExecutionTime = req.body?.minExecutionTime || 0;
    if (minExecutionTime > 0) {
      upgradeOptions.minExecutionTime = minExecutionTime;
    }

    const resultCode = await upgradeCustomerHana(req.context, upgradeOptions, dbClientOptions);
    if (resultCode !== UpgradeTriggerStatus.SKIPPED) {
      await auditMessage.success();
      res.send({
        code: "forceUpgradeSuccessful",
        details: {
          message: "Force upgrade was successful.",
        },
      });
    } else {
      throw new Error("Upgrade is not in progress and was not started.");
    }
  } catch (err) {
    await auditMessage?.failure();
    sendErrorResponse(req.context, "forceUpgradeFailed", { err });
  }
}

async function forceSpaceMigration(req: Request, res: Response) {
  const dbClientOptions: Partial<IClientOptions> = {
    exclusive: true,
    skipCircuitBreaker: true,
  };

  const auditLogClient = getAuditLogClient();
  let auditMessage: IAuditConfigurationChangeMessage | undefined;
  const context = req.context;
  context.preventCustomerHanaAutoUpgrade = true;

  try {
    auditMessage = await auditLogClient.configurationChange(
      {
        type: "ForceSpaceMigration",
        attributes: [{ name: "tenantId", new: `${context.userInfo.tenantId}`, old: "" }],
      },
      context
    );

    logInfo(`Force space migration without Tenant Upgrade`, { context });
    const customerHana: CustomerHana = await CustomerHana.fromRequestContext(context, dbClientOptions);
    void new SpaceMigrationJob(context, await customerHana.getUserManager()).run();
    await auditMessage.success();
    res.send({
      code: "forceSpaceMigrationSuccessful",
      details: {
        message: "Force space migration was successful.",
      },
    });
  } catch (err) {
    await auditMessage?.failure();
    sendErrorResponse(context, "forceSpaceMigrationFailed", { err });
  }
}

router.post("/tenant/customerhana/update-hana-parameter", updateCustomerHanaParameterRoute);
router.post("/tenant/customerhana/toggle-multi-az", toggleMultiAZForCustomerHANARoute);
router.post("/tenant/customerhana/grant-datalake-role", grantDataLakeRoleRoute);

router.post("/telemetry/datasphere-metrics/credentials", datasphereMetricsCredentials);
router.get("/telemetry/datasphere-metrics/credentials", getDatasphereMetricsCredentials);

// TODO: use deepsea new /tenant/repository/request-support-user APIs
router.post("/tenant/repository/request-support-user", async (req: Request, res: Response) => {
  let auditMessage: IAuditConfigurationChangeMessage | undefined;
  try {
    const auditLogger = getAuditLogClient();
    auditMessage = await auditLogger.configurationChange(
      {
        type: "RequestRespositorySupportUser",
        attributes: [
          {
            name: "role",
            old: "",
            new: req.body.role,
          },
        ],
      },
      req.context
    );
    if (req.body == null || req.body.role == null) {
      throw new CodedError("missingParameters", "'role' needs to be specified in the body.", Status.BAD_REQUEST);
    }

    const result = await RepositorySupport.createRepositorySupportUser(req.context, req.body.role);

    logInfo(`Repository HANA support user is requested with role '${req.body.role}'.`, { context: req.context });
    await auditMessage.success();
    res.send(result);
  } catch (err) {
    await auditMessage?.failure();
    sendErrorResponse(req.context, "createSupportUserFailed", { err });
  }
});

router.post("/tenant/provision", async (req: Request, res: Response) => {
  let auditLogClient: AuditLogClient;
  let auditMessage: IAuditConfigurationChangeMessage | undefined;
  try {
    const message = req.context.userInfo.tenantId == null ? "Tenant id is required." : null;

    if (message != null) {
      throw new CodedError("missingParameters", message, Status.BAD_REQUEST);
    }

    auditLogClient = getAuditLogClient();
    auditMessage = await auditLogClient.configurationChange(
      {
        type: "ProvisionTenant",
        attributes: [{ name: "tenantId", new: `${req.context.userInfo.tenantId}`, old: "" }],
      },
      req.context
    );

    const customerHana = await CustomerHana.fromRequestContext(req.context);
    const systemCreds = await getSystemCredentials(req.context);
    await activateAdminUser(req.context);

    await provisionSystems(req.context, {
      tenantId: req.context.userInfo.tenantId!,
      customerHana: customerHana.createUserCredentials({
        user: systemCreds.username,
        password: systemCreds.password || "",
      }),
      secureStore: cfServiceCredentials({ label: "credstore" }),
      uaa: await getTenantUaaServiceEnv(req.context),
      ...(req.body || {}),
    });
    await auditMessage.success();

    res.send({ code: "systemsProvisioned" });
  } catch (err) {
    await auditMessage?.failure();
    sendErrorResponse(req.context, "provisionSystemsFailed", { err });
  } finally {
    await deactivateAdminUser(req.context);
  }
});

function getSpaceIdsFromRequest(req: Request): Set<string> | Set<ISpaceRepairOptions> {
  return getSetFromRequest(req, "spaceIds");
}

function getSetFromRequest(req: Request, attribute: string): Set<string> | Set<ISpaceRepairOptions> {
  const value = req?.body?.[attribute];
  if (Array.isArray(value) && value?.length > 0) {
    const valueSet = new Set<string>(value.map((s: string) => s));
    return valueSet;
  }
  // Split the attributes string by comma and create a set of attributes
  if (typeof value === "string" && /^[a-zA-Z0-9_-]+(,[a-zA-Z0-9_-]+)*$/.test(value)) {
    const validAttributes = value.split(",");
    if (validAttributes?.length) {
      const valueSet = new Set<string>(value.split(","));
      return valueSet;
    }
  }

  return new Set<string>();
}

/**
 * The body should contain only spaces which can be deleted and do not have any
 * references to existing spaces.
 * {
 *    "spaceIds": [""]
 *  }
 * @return {object} returns a report of which operations of the current state
 */
router.delete("/delete/spaces", async (req: Request, res: Response) => {
  let spaceIds: Set<string> | undefined;
  let auditMessage: IAuditConfigurationChangeMessage | undefined;
  try {
    spaceIds = getSpaceIdsFromRequest(req) as Set<string>;
    if (spaceIds) {
      const auditLogger = getAuditLogClient();
      auditMessage = await auditLogger.configurationChange(
        {
          type: "support/delete/spaces",
          attributes: [
            {
              name: "spaceIds",
              new: Array.from(spaceIds).join(","),
              old: "",
            },
          ],
        },
        req.context
      );
      const message = await new DeleteSpaces(req?.body?.opt).delete(req.context, spaceIds);
      res.status(Status.OK).send(message);
    } else {
      await auditMessage?.failure();
      const err = new Error(`No spaceIds or leftovers have be found ${JSON.stringify(req.body)}`);
      sendErrorResponse(req.context, "deletedNoSpace", { err });
    }
    await auditMessage?.success();
  } catch (err) {
    await auditMessage?.failure();
    sendErrorResponse(req.context, "deleteSpaceError", { err });
  }
});
/**
 * The body should contain only scopes which can be deleted and do not have any
 * references to existing spaces.
 * {
 *    "scopeIds": [""]
 *  }
 * @return {object} returns a report of which operations of the current state
 */
router.delete("/delete/scopes", async (req: Request, res: Response) => {
  let scopeIds: Set<string> | undefined;
  let auditMessage: IAuditConfigurationChangeMessage | undefined;
  try {
    scopeIds = getSpaceIdsFromRequest(req) as Set<string>;
    if (scopeIds) {
      const auditLogger = getAuditLogClient();
      auditMessage = await auditLogger.configurationChange(
        {
          type: "support/delete/scopes",
          attributes: [
            {
              name: "scopeIds",
              new: Array.from(scopeIds).join(","),
              old: "",
            },
          ],
        },
        req.context
      );
      const message = await new DeleteSpaces().deleteScopes(req.context, scopeIds);
      res.status(Status.OK).send(message);
    } else {
      await auditMessage?.failure();
      const err = new Error(`No ScopeId or leftovers have be found ${JSON.stringify(req.body)}`);
      sendErrorResponse(req.context, "deletedNoScope", { err });
    }
    await auditMessage?.success();
  } catch (err) {
    await auditMessage?.failure();
    sendErrorResponse(req.context, "deleteSpaceError", { err });
  }
});
/**
 * Example payload:
 * {
 * 	"spaceIds": [
 * 		{
 * 		  "name": "TEST_SPACE_NAME",
 * 		  "steps": ["RECREATE_FOLDER", "RECREATE_TEAM", "CONNECT_EXISTING_FOLDERS", "UPDATE_CONTENTLIB"]
 * 		}
 * 	]
 * }
 */
router.patch("/repair/spaces", repairSpaceRoute);
export async function repairSpaceRoute(req: Request, res: Response) {
  let spaceIds: Set<ISpaceRepairOptions> | undefined;
  let auditMessage: IAuditConfigurationChangeMessage | undefined;

  try {
    spaceIds = getSpaceIdsFromRequest(req) as Set<ISpaceRepairOptions>;
    if (!spaceIds || spaceIds?.size <= 0) {
      throw new Error("Please provide some space names.");
    }
    const manualRepair: ISpaceRepairOptions[] = [];
    for (const sId of spaceIds) {
      // Space repair with options
      if (sId?.steps && sId?.steps.length > 0) {
        // Check whether all the repair strategies exists and replace it with the actual function calls.
        for (let i = 0; i < sId.steps.length; i++) {
          const enumStrategy = IIssueCode[sId.steps[i]];
          if (!enumStrategy) {
            throw new Error(`The following repair strategy ${sId.steps[i]} does not exist.`);
          }
          sId.steps[i] = enumStrategy;
          manualRepair.push(sId);
        }
      }
    }

    // DW101-92815 Checkmarx one issue: Server DoS due to Looping
    const MAX_SPACE_IDS = 100; // Define a reasonable limit for the number of space IDs
    if (manualRepair.length > MAX_SPACE_IDS) {
      throw new CodedError(
        `TooManySpaceIDs`,
        `The number of space IDs exceeds the allowed limit of ${MAX_SPACE_IDS}.`,
        StatusCodes.BAD_REQUEST
      );
    }

    const auditLogger = getAuditLogClient();
    auditMessage = await auditLogger.configurationChange(
      {
        type: "support/repair/spaces",
        attributes: [
          {
            name: "spaceIds",
            new: Array.from(spaceIds).join(","),
            old: "",
          },
        ],
      },
      req.context
    );
    const repair = new RepairSpace(req.context);
    const consistencyReport = await new Consistency(req.context).generateReport();
    if (!consistencyReport || !consistencyReport.report) {
      throw new Error("Report could not be generated.");
    }
    if (manualRepair.length > 0) {
      await promiseMap(
        manualRepair,
        async (item: ISpaceRepairOptions) => {
          await repair.manuelRepairSpaces(consistencyReport.report, new Set<ISpaceRepairOptions>([item]));
        },
        { concurrency: 5, stopOnError: false }
      );
    }
    await auditMessage.success();
    res.status(Status.OK).send(repair.getStatusMessages);
  } catch (err) {
    await auditMessage?.failure();
    sendErrorResponse(req.context, "spaceRepairFailed", { err });
  }
}

router.get("/repository/health", repositoryHealthRoute);

async function repositoryHealthRoute(req: Request<any, any, any, any>, res: Response) {
  try {
    await DeepseaProxy.of(DeepseaClientFactoryUtils.buildDeepseaServerInfo()).proxy(req.context, req, res, {
      buildTargetPath: () => "/health",
    });
  } catch (err) {
    sendErrorResponse(req.context, "repositoryHealthFailed", {
      status: Status.INTERNAL_SERVER_ERROR,
    });
  }
}

router.get("/show/space/consistency", consistencyReportRoute);
export async function consistencyReportRoute(req: Request<any, any, any, any>, res: Response) {
  try {
    const spaceIds = req?.query?.array ? JSON.parse(req.query.array.toUpperCase()) : undefined;
    const consistencyReport = await new Consistency(req.context, spaceIds).generateReport();
    res.status(Status.OK).send(consistencyReport);
  } catch (err) {
    sendErrorResponse(req.context, "showsSpaceConsistencyFailed", { err });
  }
}

router.get("/:space/credentials", getCredentials);

router.get("/metering/check-metering-availability/:key", checkMeteringAvailabilityRoute);

router.get("/metering/metric-status", metricStatusHandler);

router.post("/tenant/metering/report-insightapps", reportInsightApps);

router.post("/metering/report-insightapps", reportBdcBillingMetricsLandscape);

router.get("/space/:space/bw-bridge-connection", async (req: Request, res: Response) => {
  await getBwBridgeConnectionRoute(req, res);
});

/**
 * Support route for retrieving process environment variables
 */
router.get("/environment", async (req: Request, res: Response) => {
  try {
    // Audit log cannot be send here as for this route there is no tenant information available
    logInfo("Show environment variables request received via support endpoint", { context: req.context });
    // copy the environment variables but remove sensitive special known keys
    const environment_details = { ...process.env };
    delete environment_details.VCAP_SERVICES;
    delete environment_details.VCAP_APPLICATION;
    res.status(Status.OK).send(JSON.stringify(environment_details));
  } catch (err) {
    return sendErrorResponse(req.context, "errorBlockingSupportMiddleware", {
      status: Status.INTERNAL_SERVER_ERROR,
    });
  }
});

router.post("/customerhana/trigger-tenants-upgrade", async (req: Request, res: Response) => {
  let upgradeCredstore: ISecureStore;
  try {
    logInfo("Upgrade of HANA request received via endpoint.", { context: req.context });
    upgradeCredstore = fetchUpgradeCredstoreInstance();
    if (!(await isUpgradeRequired(upgradeCredstore, req.context))) {
      return res.status(Status.BAD_REQUEST).send("The tenants are already upgraded to the latest HANA version.");
    }
  } catch (err) {
    logError([`Error in triggering the upgrade activities for tenants via endpoint: ${err.message}`, err], {
      context: req.context,
    });
    return sendErrorResponse(req.context, "hanaUpgradeRequestFailed", { err });
  }

  const backgroundContext = RequestContext.createNewForBackground(req.context);
  try {
    res.status(Status.ACCEPTED).send("Request for HANA upgrade of tenants accepted.");
    await sendSolaceTenantUpgradeQueueMessages(backgroundContext);
    await updateLatestVersionInCredstore(upgradeCredstore, backgroundContext);
    logInfo("Upgrade messages for tenants successfully published on upgrade queue for request via endpoint.", {
      context: backgroundContext,
    });
  } catch (error) {
    logError([`Error in triggering the upgrade activities for tenants via endpoint: ${error.message}`, error], {
      context: backgroundContext,
    });
  } finally {
    void backgroundContext.finish();
  }
});

router.post("/clear-hana-connection-pools", clearConnectionPoolsRoute);

router.post("/tenant/rotate-passwords", async (req: Request, res: Response) => {
  let client: DbClient | undefined;
  let lockStatus = false;
  let userManagerFixed = false;
  const backgroundContext = RequestContext.createNewForBackground(req.context) as IRequestContext;
  const auditLogger = getAuditLogClient();
  const dbClientOptions: Partial<IClientOptions> = {
    exclusive: true,
    skipCircuitBreaker: true,
  };

  function getDeprecatedOptionMessage(option: string, label: string): string {
    return `deprecated: ${option} has no effect. Credentials for ${label} do not need to be rotated.`;
  }

  try {
    if (
      !req.body ||
      (req.body.adminUser !== true &&
        req.body.userManager !== true &&
        req.body.globalUsers !== true &&
        req.body.spaceUsers !== true &&
        req.body.bwBridgeUsers !== true &&
        req.body.jwtKeyPair !== true &&
        req.body.incremental !== true)
    ) {
      throw Error(
        "Invalid body supplied. Please indicate credential types (e.g. adminUser, jwtKeyPair) need to be rotated."
      );
    }
    // Proactively fix user manager inconsistencies if any
    userManagerFixed = await checkAndFixUserManagerInconsistency(backgroundContext, dbClientOptions);
    client = await passwordRotationUtil.getUserManagerClient(backgroundContext, dbClientOptions);
    lockStatus = await passwordRotationUtil.setPasswordRotationLockStatus(backgroundContext, client, true);
    if (!lockStatus) {
      logError("Credentials rotation failed via endpoint. Parallel upgrade in progress", {
        context: backgroundContext,
      });
      throw Error("Credentials rotation failed via endpoint. Parallel upgrade in progress");
    }

    // Since the rotation of credentials may take some time we do the rotation asyncronously behind the scenes.
    // We do not block the call to avoid gateway timeout error for the request.
    let msg = "";
    if (req.body.globalUsers) {
      msg = `${getDeprecatedOptionMessage("globalUsers", "global users")}\n`;
    }
    if (req.body.userManager) {
      msg = `${msg}{getDeprecatedOptionMessage("userManager", "user manager")}\n`;
    }
    if (req.body.spaceUsers) {
      msg = `${msg}${getDeprecatedOptionMessage("spaceUsers", "space users")}\n`;
    }
    if (req.body.bwBridgeUsers) {
      msg = `${msg}${getDeprecatedOptionMessage("spaceUsers", "bwBridge users")}\n`;
    }
    msg = `${msg}Request for credentials rotation accepted.`;
    res.status(Status.ACCEPTED).send(msg);
  } catch (err) {
    // This will not audit log the senarios when api request is supplied with invalid body.
    await auditLogger.securityMessage(
      `Error during password rotation via endpoint: ${err.message}.`,
      backgroundContext
    );
    return sendErrorResponse(req.context, "passwordRotationFailed", { err });
  }

  try {
    logInfo(`Beginning the password rotation activities as requested via endpoint.`, { context: backgroundContext });

    // With JWT authentication only, incremental rotation just rotates all possible credendtial types.
    // The option was not renamed for backward compatibility reasons.
    if (req.body.incremental) {
      await passwordRotationUtil.rotateTechUserCredentials(backgroundContext, false);
    } else {
      if (req.body.adminUser) {
        await passwordRotationUtil.rotateDbAdminActivatorPasswords(backgroundContext);
      }
      if (req.body.jwtKeyPair && !userManagerFixed) {
        await passwordRotationUtil.rotateJwtKeyPair(backgroundContext, client);
      }
    }

    logInfo("The credentials rotation was successfully completed via endpoint", {
      context: backgroundContext,
    });
  } catch (err) {
    logError([`Error during password rotation via endpoint: ${err.message}`, err], { context: backgroundContext });
    await auditLogger.securityMessage(
      `Error during password rotation via endpoint: ${err.message}.`,
      backgroundContext
    );
  } finally {
    try {
      if (client && lockStatus) {
        await passwordRotationUtil.setPasswordRotationLockStatus(backgroundContext, client, false);
      }
    } catch (error) {
      logError(
        [
          `Error in unlocking the upgradeInProgress flag during password rotation via endpoint: ${error.message}`,
          error,
        ],
        { context: backgroundContext }
      );
      await auditLogger.securityMessage(
        `Error in unlocking the upgradeInProgress flag during password rotation via endpoint: ${error.message}.`,
        backgroundContext
      );
    }
    void backgroundContext.finish();
  }
});

router.post("/tenant/register-with-ucl", async (req: Request, res: Response) => {
  try {
    await ensureRegisteredWithUCL();
    if (uclRegistration.lastError) {
      throw uclRegistration.lastError;
    }
    res.status(Status.OK).send(uclRegistration);
  } catch (err) {
    return sendErrorResponse(req.context, "uclRegistrationError", { err });
  }
});

router.post("/tenant/usage-tracking", async (req: Request, res: Response) => {
  try {
    const haumTask = new HaumDataTransferTask(req.context);
    await haumTask.execute(null as any);
    res.status(Status.ACCEPTED).send("Usage tracking request successfully completed.");
  } catch (err) {
    return sendErrorResponse(req.context, "usageTrackingTransferFailed", { err });
  }
});

router.post("/abap-bridge/database-connection", async (req: Request, res: Response) => {
  if (!req.authInfo?.checkScope("$XSAPPNAME.DWCSteampunkCallback")) {
    await addFailedScopeCheckAuditMessage(req.context, {
      path: req.path,
      scope: "$XSAPPNAME.DWCSteampunkCallback",
    });
    sendErrorResponse(req.context, "noValidAccess", {
      status: Status.FORBIDDEN,
    });
    return;
  }

  await postSolaceSteampunkDatabaseConnection(req, res);
});

router.post("/abap-bridge/connection-hh-prep", async (req: Request, res: Response) => {
  if (!req.authInfo?.checkScope("$XSAPPNAME.DWCSteampunkCallback")) {
    await addFailedScopeCheckAuditMessage(req.context, {
      path: req.path,
      scope: "$XSAPPNAME.DWCSteampunkCallback",
    });
    sendErrorResponse(req.context, "noValidAccess", {
      status: Status.FORBIDDEN,
    });
    return;
  }

  await postSteampunkConnectionCustomerHanaToSteampunkHanaPrep(req, res);
});

router.post("/lsa/spark-history-server", provisionSparkHistoryServer);

router.get("/lsa/spark-history-server", getSparkHistoryServerParameters);

router.delete("/lsa/spark-history-server", deleteSparkHistoryServer);

router.get("/lsa/allowlist", getLargeSystemsAllowlistRoute);
router.post("/lsa/allowlist", updateLargeSystemsAllowlistRoute);

router.delete("/lsa/deleteinstances", deleteSparkAndHDLFInstancesRoute);

router.post("/lsa/plugin-configuration", installLSAPluginRoute);

router.post("/lsa/spark-instance", updateSparkInstanceRoute);
router.post("/lsa/hdlf-instance", updateHdlfInstanceRoute);

router.get("/admin-api/api-instance", getAdminApiInstance);

router.post("/admin-api/provision-api-instance", provisionApiInstance);

router.post("/admin-api/renew-api-key", renewAdminApiKey);

router.delete("/admin-api/api-instance", deleteApiInstance);

router.post("/abap-bridge/connection-hh-ack", async (req: Request, res: Response) => {
  if (!req.authInfo?.checkScope("$XSAPPNAME.DWCSteampunkCallback")) {
    await addFailedScopeCheckAuditMessage(req.context, {
      path: req.path,
      scope: "$XSAPPNAME.DWCSteampunkCallback",
    });
    sendErrorResponse(req.context, "noValidAccess", {
      status: Status.FORBIDDEN,
    });
    return;
  }

  await postSteampunkConnectionCustomerHanaToSteampunkHanaFinish(req, res);
});

router.post("/tenant/customerhana/flexible-configuration/metadata/clear", cleanTagAndMetadataRoute);
router.get("/tenant/customerhana/flexible-configuration/metadata", getFtcMetadataRoute);

router.post("/abap-bridge-configuration/force-refresh", AbapBridgeEndpoint.forceUpdateAbapBridgeEntries);

router.get("/discovery", getSupportDiscovery);

router.get("/tenant/metering/reports", async (req: Request<any, any, any, any>, res: Response) => {
  if (!req.query.$filter) {
    return res
      .status(Status.BAD_REQUEST)
      .send(
        "You should specify the Metering Service ID to fetch the usage. e.g.: /tenant/metering/reports?$filter=service.id eq 'data-analytics'"
      );
  }
  try {
    const metering = new MeteringService(req.context);
    const top = req.query.$top ? parseInt(req.query.$top, 10) : 5;
    const skip = req.query.$skip ? parseInt(req.query.$skip, 10) : 0;

    if (typeof top != "number" || !(top > 0) || !(top <= 100)) {
      return res.status(Status.BAD_REQUEST).send("$top value should be between 1 and 100.");
    }

    if (typeof skip != "number" || !(skip >= 0)) {
      return res.status(Status.BAD_REQUEST).send("$skip value should be 0 or higher.");
    }

    const result = await metering.readUsage(req.query.$filter, top, skip);
    res.status(Status.OK).send(result);
  } catch (err) {
    sendErrorResponse(req.context, "readUsageReportsFailed", { err });
  }
});

router.put("/tenant/metering/reports", async (req: Request, res: Response) => {
  try {
    if (!req.body.usageDocument) {
      res.status(Status.BAD_REQUEST).send("Missing usage document.");
    }
    await AnalyticsProvisioningService.forgetCachedTenantMap();
    const meteringService = new MeteringService(req.context);
    const result = await meteringService.reportUsageWithFallback(req.body.usageDocument);
    res.status(200).send(result);
  } catch (err) {
    sendErrorResponse(req.context, `Failed to report usage document.`, { err });
  }
});

/**
 * This endpoint can be triggered with two queries either conversion=true or cleanup=true or both.
 * If both are not set then the clean up will be triggered before the conversion.
 * The same is true when both queries are set to true.
 * When either false or any other string is being send then the queries will also be set to true,
 * otherwise it does not make sense to call this endpoint, if nothing should be done.
 */
router.put("/sdp/conversion", async (req: Request, res: Response) => {
  try {
    logInfo(`/sdp/conversion started`, { context: req.context });
    if (
      (!req.query?.conversion && !req.query?.cleanup) ||
      (req.query.conversion === "false" && req.query.cleanup === "false")
    ) {
      logInfo(`req.query.conversion and req.query.cleanup set to true original ${JSON.stringify(req.query)}`, {
        context: req.context,
      });
      req.query.conversion = "true";
      req.query.cleanup = "true";
    }

    // Check that it is really a boolean and not a random string
    const conversion = JSON.parse(req.query.conversion as string);
    const cleanup = JSON.parse(req.query.cleanup as string);
    const options: SdpConversionOptions = {
      retries: req.query.retries ? Number(req.query.retries) : 0,
      cleanUp: cleanup,
      allowTenants: req.body?.enforceTenantsConversion ? req.body.enforceTenantsConversion : [],
      // async determines whether we need to run conversion api asynchronously
      // Conversion api would return after setting status to "started" in config service,
      // users need to query for the status themselves
      async: req.query.async ? (req.query.async as string) === "true" : false,
    };
    // chunkSize determines the upper limit of number of entries when assigning user scopes in one SAC request
    // if the entries is more than this chunkSize, these entries would be divided into groups with size of this "chunkSize"
    if (req.query.chunkSize) {
      options.chunkSize = Number(req.query.chunkSize);
    }
    // chunkedConcurrency determines the upper limit of concurrency number when requests need chunked
    if (req.query.chunkedConcurrency) {
      options.chunkedConcurrency = Number(req.query.chunkedConcurrency);
    }

    const sdpConversion = new SdpConversion(req.context, options);
    // Two ifs as the clean up can be started before the conversion
    let sdpStatus = "";
    if (cleanup) {
      await sdpConversion.cleanUpSdpConversion();
    }
    if (conversion) {
      sdpStatus = await sdpConversion.doConvert();
    }
    res.status(StatusCodes.OK).send({ sdpStatus });
  } catch (err) {
    logError([`support/sdp/conversion failed.`, err], { context: req.context });
    sendErrorResponse(req.context, "/sdp/conversion", { err });
  }
});

router.get("/sdp/state", getSdpStateBeforeConversion);
router.post("/sdp/conversion/validation", validateAfterConversion);

router.get("/tenant/ip-allowlist", allowlistSupportRouteGet);
router.post("/tenant/ip-allowlist", allowlistSupportRoutePost);
router.post("/tenant/ip-allowlist/check", allowlistSupportRouteCheck);
router.delete("/tenant/ip-allowlist", allowlistSupportRouteDelete);

router.get("/tenant/license/allocated-cu", getTenantAllocatedCuRoute);

router.put("/space/datarepository", updateDataRepositoryProperty);

router.get("/tenant/customerhanahdi/containers", getDeployedHdiContainersInfo);
router.get("/tenant/customerhanahdi/cache", getDeployHdiCacheStatusRoute);
router.delete("/tenant/customerhanahdi/cache", clearDeployHdiCacheStatusRoute);

router.get("/tenant/service-instance", getServiceInstancesRoute);
router.get("/tenant/workflows", getWorkflowsRoute);

router.post("/repository/onboard-client", repositoryOnboardingClientRoute);

router.post("/featureflag/flushflags", flushFeatureFlags);

router.post("/bwbridge/create-instance", createInstanceBwBridgeRoute);

router.delete("/bwbridge/:instancename", deleteInstanceBwBridgeRoute);

router.patch("/bwbridge/refresh", syncRefreshInstanceBwBridgeRoute);

router.post("/bwbridge/keyandconnection", createConnectionAndKeyRoute);

router.get("/ucl/formation", getUclFormationRoute);

router.get("/ucl/application-template", getUclApplicationTemplateRoute);
router.post("/ucl/application-template", postUclApplicationTemplateRoute);
router.patch("/ucl/application-template", patchUclApplicationTemplateRoute);
router.delete("/ucl/application-template", deleteUclApplicationTemplateRoute);

router.post("/bdc/ucl/system-removal-validation", validateTenantForDeletion);

router.post("/broadcast", broadcastNotification);

router.post("/:space/deploy/cleanupmdsmetadata", cleanupMDSMetadata);

// Support route for Task Framework systemSchedulesController
router.put("/tenant/tf/systemschedules/status/:pauseorresume", pauseOrResumeSchedule);
router.put("/tenant/tf/systemschedules/edit", editSchedule);
router.post("/hana/configuration/selectstatistic", setSelectEnabledStatistics);

function registerCalculatedHandlers() {
  router.get("/circuitbreaker", getCircuitBreakerInfoRoute(centralCircuitBreakerManager));

  router.put("/circuitbreaker", putCircuitBreakerStateRoute(centralCircuitBreakerManager));
  if (isCircuitBreakerTestEnvironment()) {
    router.get("/circuitbreaker/test", testCircuitBreakerRoute(centralCircuitBreakerManager));
  }
  router.get("/redis/status", getRedisStatusRoute());
  router.get("/redis/slowlog", getRedisSlowlogRoute());
  router.post("/redis/flushall", flushRedisCacheRoute());
  router.get("/redis/cache/:key", getRedisValueByKey);
  router.delete("/redis/cache/:key", deleteRedisEntryByKey);

  router.get("/hana/query", executeHanaQueries());

  router.get("/oneagent/logs/:logtype", checkInstance, getOneAgentLogs);
  router.get("/profile/memory/heapprofile", checkInstance, MemoryV2.getHeapProfile);
  router.get("/profile/memory/heapsnapshot", checkInstance, MemoryV2.getCreateHeapSnapshot());
  router.get("/profile/memory/heapstatistics", checkInstance, MemoryV2.getCreateHeapStatistics());
  router.get("/profile/memory/heapspacestatistics", checkInstance, MemoryV2.getCreateHeapSpaceStatistics());
  router.post("/profile/memory/gctracesenable", checkInstance, MemoryV2.getEnableGCTraces());
  router.post("/profile/memory/gctracesdisable", checkInstance, MemoryV2.getDisableGCTraces());
  router.get("/profile/cpu", checkInstance, CpuV2.getCPUProfile);
  router.get(
    "/profile/eventloopblockers",
    [checkInstance, EventLoopBlockersV2.doPerformEventLoopBlockersRun],
    EventLoopBlockersV2.getEventLoopBlockersReport
  );
  router.delete("/profile/eventloopblockers", [checkInstance], EventLoopBlockersV2.deleteEventLoopBlockersReport);
  router.post(
    "/profile/eventloopblockers",
    [checkInstanceInBody, EventLoopBlockersV2.startAndStopEventLoopBlockerDetection],
    EventLoopBlockersV2.getEventLoopBlockersReport
  );

  router.post("/auditlog/state", changeAuditLogStatus());

  router.get("/di-e/tenant/rmspodcount", getDiERmsPodCount);
  router.put("/di-e/tenant/rmspodcount", updateDiERmsPodCount);

  const auditlog = getAuditLogClient();
  router.get(
    "/configurations/parameters/:key/effectivevalue",
    configurations.fetchEffectiveValue(configStore, auditlog)
  );
  router.post("/configurations/parameters/:key/values", configurations.upsertValue(configStore, auditlog));

  router.get("/ratelimiting/state", checkInstance, getRateLimitingStatus);

  router.get("/endpoints", configurableEndpointsEnabled, getEndpoints);
  router.post("/endpoints", configurableEndpointsEnabled, patchEndpoints);
  router.post("/endpoints/disable", configurableEndpointsEnabled, disableEndpoint);
  router.post("/endpoints/reset", configurableEndpointsEnabled, resetEndpoints);

  router.post("/cache/invalidate", invalidateCacheEndpointsEnabled, invalidateCache);

  // ecn support endpoints
  router.put("/compute/v1/nodes/:id/unassign/:spaceid", unassignSpaceComputeNode);
  router.delete("/compute/v1/nodes/:id", deleteComputeNode);

  //  support endpoint for extended viewer
  router.get("/tenant/supportuserscopeassignment", getSupportUserScopeAssignment);

  // delete of ai feature config data for all tenants
  router.delete("/aifeatures/:featureid/configstoredata", deleteAIFeatureConfigData);
}

// Register route handlers that are calculated from imported functions during next event cycle.
// This breaks load of circular dependencies and enables tests to be executed individually.
setTimeout(registerCalculatedHandlers);
