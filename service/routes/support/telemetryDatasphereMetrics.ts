/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 *
 * FILEOWNER: [DataIntegration.RemoteTableBackend]
 */
import { ISacRequestContext } from "@sap/dwc-credentials";
import { Request, Response } from "express-serve-static-core";
import HttpStatusCode from "http-status-codes";
import { pki } from "node-forge";
import { createUserCertificate } from "../../datasphereMetrics/credentials//identity";
import {
  DatasphereMetricsCredentialsService,
  STORE_KEY,
  STORE_NAME,
} from "../../datasphereMetrics/credentials/DatasphereMetricsCredentialsService";
import { AuthTypes, DatasphereAuthBody, X509Auth } from "../../datasphereMetrics/credentials/types";
import { FeatureFlagProvider } from "../../featureflags/FeatureFlagProvider";
import { getLogger } from "../../logger";
import { getAuditLogClient } from "../../reuseComponents/auditlogger/audit-log-utils";
import { SecureStore } from "../../securestore";
const { logErrorWithOptionalContext, logInfoWithOptionalContext } = getLogger("DATASPHERE_METRICS_SUPPORT_ROUTE");

export async function datasphereMetricsCredentials(req: Request, res: Response) {
  if (!(await FeatureFlagProvider.isLandscapeFeatureActive("DWCO_LS_REMOTE_TABLE_USAGE_TRACKING_DATASPHERE"))) {
    return res.status(HttpStatusCode.FORBIDDEN).send({
      message: "Feature not available.",
    });
  }
  const body = req.body;
  if (!body || !body.schemaName || !body.authType || !body.host || !body.port || !body.user) {
    return res.status(HttpStatusCode.BAD_REQUEST).send({
      message: "Missing required fields: schemaName, authType, host, port, user.",
    });
  }
  try {
    let readyToStore: DatasphereAuthBody;
    const authType = body.authType.trim().toLowerCase();

    if (authType === "basic") {
      // Basic Auth validation - Added just for testing purposes until X509 becomes available
      if (!body.password) {
        return res.status(HttpStatusCode.BAD_REQUEST).send({
          message: "Missing required field for Basic auth: password.",
        });
      }

      readyToStore = {
        schemaName: body.schemaName,
        authType: AuthTypes.Basic,
        host: body.host,
        port: body.port,
        user: body.user,
        password: body.password,
      };
    } else if (authType === "x509") {
      const userKeyPair = await createUserCertificate(body.user);

      const certChain = userKeyPair.certificates.join("\n");

      if (!certChain || !userKeyPair.key) {
        logErrorWithOptionalContext("Failed to generate X509 cert or key (missing values from createUserCertificate)");
        return res.status(HttpStatusCode.INTERNAL_SERVER_ERROR).send({
          message: "Failed to generate X509 certificate.",
        });
      }
      const { certificates } = userKeyPair;

      const cert = pki.certificateFromPem(certificates[0]);
      const subject = formatSubjectOrIssuer(cert, true);
      const issuer = formatSubjectOrIssuer(cert, false);
      logInfoWithOptionalContext(`${subject}`);
      logInfoWithOptionalContext(`${issuer}`);
      readyToStore = {
        schemaName: body.schemaName,
        authType: AuthTypes.X509,
        host: body.host,
        port: body.port,
        user: body.user,
        cert: certChain,
        key: userKeyPair.key,
        certSubject: subject,
        certIssuer: issuer,
      };
    } else {
      return res.status(HttpStatusCode.BAD_REQUEST).send({
        message: `Unsupported authType: ${body.authType}`,
      });
    }

    const service = new DatasphereMetricsCredentialsService();
    await service.upsert(readyToStore);

    const auditLogClient = getAuditLogClient();
    await auditLogClient.securityMessage(
      "Datasphere Metrics credentials successfully stored in Credential Store.",
      req.context
    );
    logInfoWithOptionalContext("Datasphere Metrics credentials successfully stored in Credential Store.");
    return res.status(HttpStatusCode.OK).send({
      message: "Datasphere Metrics credentials successfully stored in Credential Store.",
    });
  } catch (err) {
    logErrorWithOptionalContext(["Error during storing Datasphere Metrics credentials into Credential Store", err]);

    return res.status(HttpStatusCode.INTERNAL_SERVER_ERROR).send({
      message: "An unexpected error occurred.",
    });
  }
}

export async function getDatasphereMetricsCredentials(req: Request, res: Response) {
  if (!(await FeatureFlagProvider.isLandscapeFeatureActive("DWCO_LS_REMOTE_TABLE_USAGE_TRACKING_DATASPHERE"))) {
    return res.status(HttpStatusCode.FORBIDDEN).send({
      message: "Feature not available.",
    });
  }
  try {
    const context: ISacRequestContext = { tenantId: null };
    const store = SecureStore.fromStoreName(context, STORE_NAME);
    const credentials = (await store.retrieve(STORE_KEY)) as X509Auth;

    if (!credentials) {
      return res.status(HttpStatusCode.NOT_FOUND).send("No Datasphere Metrics credentials found.");
    }

    if (credentials.authType !== AuthTypes.X509) {
      return res.status(HttpStatusCode.BAD_REQUEST).send("Only X.509 credentials are supported in this view.");
    }

    const responseBody = {
      x509Config: {
        x509Enabled: true,
        subjectName: credentials.certSubject || null,
        issuerName: credentials.certIssuer || null,
      },
      consumption: {
        localSchemaAccess: true,
        scriptServerAccess: true,
        spaceSchemaAccess: true,
        hdiGrantorForCupsAccess: true,
        consumptionWithGrant: true,
      },
      ingestion: {
        auditing: {
          dppRead: {
            isAuditPolicyActive: true,
            retentionPeriod: 30,
          },
          dppChange: {
            isAuditPolicyActive: true,
            retentionPeriod: 30,
          },
        },
      },
    };

    logInfoWithOptionalContext("Returned view of Datasphere Metrics credentials.");
    return res.status(HttpStatusCode.OK).json(responseBody);
  } catch (err) {
    logErrorWithOptionalContext(["Failed to retrieve support Datasphere Metrics credentials", err]);
    return res.status(HttpStatusCode.INTERNAL_SERVER_ERROR).send("Unexpected error occurred.");
  }
}

export function formatSubjectOrIssuer(cert: pki.Certificate, isSubject: boolean): string {
  const attr = isSubject ? cert.subject.attributes : cert.issuer.attributes;
  return attr
    .slice()
    .reverse()
    .map((a) => `${a.shortName}=${a.value}`)
    .join(", ");
}
