/**
 * @format
 * @classdesc Analytic Model to CSN converter for Single Fact Models: Old handling with generation of ∞Dx fields and ∞x associations
 * @class
 * @augments AnalyticModelCsnConverterV2
 */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-undef */

// Check those imports from C4S and see which can be refactored
import { IGetObjectParameters, IRequestContext } from "@sap/deepsea-types";
import { CodedError } from "@sap/dwc-express-utils";
import { getLogger } from "@sap/dwc-logger";
import assert from "assert";
import { StatusCodes } from "http-status-codes";
import _ from "lodash";
import {
  AnalyticModelAttributeType,
  AnalyticModelCollisionHandlingPriority,
  AnalyticModelConstantSelectionType,
  AnalyticModelConversionTypeType,
  AnalyticModelCrossCalculationType,
  AnalyticModelDefaultRangeOption,
  AnalyticModelDefaultRangeSign,
  AnalyticModelExceptionAggregationNcumType,
  AnalyticModelFilterOperandType,
  AnalyticModelMeasureType,
  AnalyticModelParameterType,
  AnalyticModelParameterValueBaseType,
  AnalyticModelReferenceDateType,
  AnalyticModelRestrictedMeasureOperandType,
  AnalyticModelSourceParameterMappingType,
  AnalyticModelSourceType,
  AnalyticModelTargetCurrencyType,
  AnalyticModelTargetUnitType,
  AnalyticModelVariableProcessingType,
  AnalyticModelVariableSelectionType,
  IAnalyticModel,
  IAnalyticModelAssociationContext,
  IAnalyticModelAttribute,
  IAnalyticModelAttributeKey,
  IAnalyticModelCalculatedCrossCalculation,
  IAnalyticModelCalculatedMeasure,
  IAnalyticModelConstantValue,
  IAnalyticModelCurrencyConversionMeasure,
  IAnalyticModelDefaultInterval,
  IAnalyticModelDefaultRange,
  IAnalyticModelDimensionSourceAttribute,
  IAnalyticModelFactSourceAttribute,
  IAnalyticModelFactSourceCrossCalculation,
  IAnalyticModelFilter,
  IAnalyticModelFilterOperandConstant,
  IAnalyticModelFilterSelection,
  IAnalyticModelFilterVariableRangeSingleSelection_MANUALINPUT,
  IAnalyticModelFilterVariableSingleValueSingleSelection_MANUALINPUT,
  IAnalyticModelFiscalVariantVariable_LOOKUP,
  IAnalyticModelFiscalVariantVariable_MANUALINPUT,
  IAnalyticModelKeyDateVariable_MANUALINPUT,
  IAnalyticModelMeasure,
  IAnalyticModelRestrictedCrossCalculation,
  IAnalyticModelRestrictedMeasure,
  IAnalyticModelSource,
  IAnalyticModelSourceMeasure,
  IAnalyticModelSourceParameterMapping_CONSTANTVALUE,
  IAnalyticModelSourceParameterMapping_VARIABLE,
  IAnalyticModelSqlFunctionName,
  IAnalyticModelStoryFilterVariable,
  IAnalyticModelUnitConversionMeasure,
  IAnalyticModelVariableKey,
  IAnalyticModelVariableManualInput,
} from "../../../../shared/queryBuilder/AnalyticModel";
import {
  AnalyticModelCsnConversionElementMapping,
  AnalyticModelCsnConversionResult,
} from "../../../../shared/queryBuilder/AnalyticModelCsnConversionResult";
import {
  ReverseAggregationTypeMapping,
  ReverseAnalyticModelDefaultRangeOption,
  ReverseAnalyticModelDefaultRangeSign,
  ReverseAnalyticModelVariableSelectionType,
  ReverseErrorHandlingMapping,
  ReverseExceptionAggregationNcumTypeMapping,
  ReverseExceptionAggregationTypeMapping,
} from "../../../../shared/queryBuilder/AnalyticModelCsnMappings";
import { AnalyticModelJsonSchemaValidationResult } from "../../../../shared/queryBuilder/AnalyticModelJsonSchemaValidationResult";
import AnalyticModelSharedHelper from "../../../../shared/queryBuilder/AnalyticModelSharedHelper";
import {
  CDS_EXPR_FUNC,
  CsnXprToAMHelper,
  IANALYTIC_MODEL_LOCALIZED_MESSAGE,
  XPR_PARTS,
  getMappedHanaType,
  getValueBasedOnCdsType,
} from "../../../../shared/queryBuilder/CsnXprToAMHelper";
import { DbHints, ErrorCodes } from "../../../../shared/queryBuilder/QueryModel";
import {
  ICreateExpressionResult,
  QueryModelValidator,
  ValidationMessage,
  ValidationMessageType,
} from "../../../../shared/queryBuilder/QueryModelValidator";
import {
  ISimpleTypeMapping,
  fIsFeatureFlagActive,
} from "../../../../shared/queryBuilder/analyticModelValidators/AnalyticModelValidator";
import { FeatureFlagProvider } from "../../../featureflags/FeatureFlagProvider";
import { Commons } from "../../../hybridParser/businessLayer/Commons";
import { RepositoryObjectClient } from "../../../repository/client/repositoryObjectClient";
import { extendFullCsnForAnalyticModel } from "../../../repository/plugins/deployPlugin";
import {
  analyticModelCdsParseExprSync,
  createCdsFormulaToExprConverter,
} from "../utils/AnalyticModelCdsXprParserHelper";
import AnalyticModelHelper from "../utils/AnalyticModelHelper";
import { getRemovedProperties, validateAnalyticModelJson } from "../utils/AnalyticModelJsonValidator";
import { AnalyticModelPerformanceTracker } from "../utils/AnalyticModelPerformanceTracker";
import { AnalyticsDetails, CDSElementKeys, GeneratedNames, HiddenAnnotations } from "../utils/enum";

export interface ISourceField {
  sourceFieldName: string;
  sourceFieldText: string;
  attributeInModel?: string;
  foreignKeyField?: boolean; // Only in V2
  duplicated?: boolean; // Only in V2
  originalAttribute?: string; // Only in V2
  sourceType?: AnalyticModelSourceType; // Only in V2
  sourceKey?: string; // Only in V2
}

export interface ITargetField {
  targetFieldName: string;
  targetFieldText: string;
  representativeKey?: boolean;
}

export interface IDependencyElement {
  sourceType: AnalyticModelSourceType;
  sourceKey: string; // sourceKey in the AM model
  sourceName: string; // Technical name of the source
  sourceAlias: string; // Aliased name of the source. Make sure no doubled names exist
  sourceAssociationName?: string; // Not available if source is a fact source
  sourceAssociationAlias?: string; // Not available if source is a fact source
  sourceFields: ISourceField[]; // Fields used for mapping in the source
  targetAssociationAlias: string; // Association with appended ∞sourceKey. Make sure no doubled names exist
  targetAssociationName: string; // Association name in the entity
  targetAssociationText: string; // Text defined in the UI for the association
  targetKey: string; // sourceKey in the AM model
  targetName: string; // Technical name of the target
  targetAlias: string; // Aliased name of the target. Make sure no doubled names exist
  targetFields: ITargetField[]; // Fields used for mapping in the target. Matches the order in sourceFields
  level: number;
}

/**
 * @classdesc Analytic Model to CSN converter for Single Fact Models (only fact associations - Fact -> Dim1; Fact -> Dim2; Fact -> Dim3)
 * @export
 * @class AnalyticModelCsnConverter
 */
export class AnalyticModelCsnConverter {
  public isFalsyExceptZero = (value: any): boolean =>
    value === undefined || value === null || value === "" || value === false;
  // MARK: PROPERTIES
  protected logger = getLogger("AnalyticModelCsnConverter");

  protected dependencyTree: IDependencyElement[] = [];

  protected sourceModelsByName: {
    [name: string]: ICsnDefinition;
  };

  // Used in V2 for stacked models to read the stack efficiently
  protected idMapping: {
    [name: string]: string;
  };

  protected conversionResult: AnalyticModelCsnConversionResult = {
    hasErrors: false,
    code: "internalServerError",
    status: StatusCodes.INTERNAL_SERVER_ERROR,
  };

  protected static aliasMap: Map<string, string> = new Map();

  protected attributeMappingElementMap: Map<string, string> = new Map();
  protected attributeMappingElementsMap: Map<string, string[]> = new Map();

  protected ffDacSupport: boolean = false;
  protected ffUnitConversion: boolean = false;
  protected ffFormatOfMeasures: boolean = false;
  protected ffMultiRange: boolean = false;
  protected ffGrandTotal: boolean = false;
  protected ffProtectDataExport: boolean = false;
  protected ffCrossCalculation: boolean = false;
  protected ffFiscalVariable: boolean = false;
  protected ffDbHints: boolean = false;

  protected performanceTracker: AnalyticModelPerformanceTracker;

  protected validateJsonSchema: boolean = true;

  // MARK: STATIC METHODS
  /**
   * @description Creates a CSN function call for a currency conversion measure
   * @static
   * @param {string} measureKey
   * @param {IAnalyticModelCurrencyConversionMeasure} measure
   * @param {string} baseMeasureName
   * @param {(string | undefined)} calculatedAttributeName
   * @param {string} spaceKey
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  public static createCurrencyConversionFunctionCall(
    measureKey: string,
    measure: IAnalyticModelCurrencyConversionMeasure,
    baseMeasureName: string,
    calculatedAttributeName: string | undefined,
    spaceKey: string
  ) {
    const func: ICsnFunction = {
      as: measureKey,
      func: "CONVERT_CURRENCY",
      args: {
        AMOUNT: {
          ref: ["$projection", baseMeasureName],
        },
        CLIENT: {
          val: measure.client,
        },
        // SCHEMA: { val: spaceKey }, // => will be added during deployment in ConversionPredeployTask to fix issue with export/import
        ERROR_HANDLING: {
          val: ReverseErrorHandlingMapping.get(measure.errorHandling),
        },
        PRECISIONS_TABLE: {
          val: "SAP.CURRENCY.VIEW.TCURX",
        },
        CONFIGURATION_TABLE: {
          val: "SAP.CURRENCY.VIEW.TCURV",
        },
        PREFACTORS_TABLE: {
          val: "SAP.CURRENCY.VIEW.TCURF",
        },
        RATES_TABLE: {
          val: "SAP.CURRENCY.VIEW.TCURR",
        },
        NOTATIONS_TABLE: {
          val: "SAP.CURRENCY.VIEW.TCURN",
        },
      },
    };

    // CONVERSION_TYPE
    switch (measure.conversionTypeType) {
      case AnalyticModelConversionTypeType.constantValue:
        func.args.CONVERSION_TYPE = {
          val: (measure.conversionType as IAnalyticModelConstantValue).value,
        };
        break;
      case AnalyticModelConversionTypeType.attribute:
        func.args.CONVERSION_TYPE = {
          ref: ["$projection", (measure.conversionType as IAnalyticModelAttributeKey).key],
        };
        break;
      case AnalyticModelConversionTypeType.variable:
        func.args.CONVERSION_TYPE = {
          ref: [(measure.conversionType as IAnalyticModelVariableKey).key],
          param: true,
        };
        break;

      default:
        break;
    }

    // TARGET_UNIT
    switch (measure.targetCurrencyType) {
      case AnalyticModelTargetCurrencyType.constantValue:
        func.args.TARGET_UNIT = {
          val: (measure.targetCurrency as IAnalyticModelConstantValue).value,
        };
        break;
      case AnalyticModelTargetCurrencyType.attribute:
        func.args.TARGET_UNIT = {
          ref: ["$projection", (measure.targetCurrency as IAnalyticModelAttributeKey).key],
        };
        break;
      case AnalyticModelTargetCurrencyType.variable:
        func.args.TARGET_UNIT = {
          ref: [(measure.targetCurrency as IAnalyticModelVariableKey).key],
          param: true,
        };
        break;
      default:
        break;
    }

    // REFERENCE_DATE
    if (calculatedAttributeName) {
      func.args.REFERENCE_DATE = {
        ref: ["$projection", calculatedAttributeName],
      };
    } else {
      switch (measure.referenceDateType) {
        case AnalyticModelReferenceDateType.sqlFunction:
          func.args.REFERENCE_DATE = {
            func: (measure.referenceDate as IAnalyticModelSqlFunctionName).functionName,
          };
          break;
        case AnalyticModelReferenceDateType.attribute:
          func.args.REFERENCE_DATE = {
            ref: ["$projection", (measure.referenceDate as IAnalyticModelAttributeKey).key],
          };
          break;
        case AnalyticModelReferenceDateType.variable:
          func.args.REFERENCE_DATE = {
            ref: [(measure.referenceDate as IAnalyticModelVariableKey).key],
            param: true,
          };
          break;
        case AnalyticModelReferenceDateType.constantValue:
          func.args.REFERENCE_DATE = {
            val: (measure.referenceDate as IAnalyticModelConstantValue).value,
          };
          break;

        default:
          break;
      }
    }

    return func;
  }
  // MARK: STATIC METHODS
  /**
   * @description Creates a CSN function call for a unit conversion measure
   * @static
   * @param {string} measureKey
   * @param {IAnalyticModelUnitConversionMeasure} measure
   * @param {string} baseMeasureName
   * @param {(string | undefined)} calculatedAttributeName
   * @param {string} spaceKey
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  public static createUnitConversionFunctionCall(
    measureKey: string,
    measure: IAnalyticModelUnitConversionMeasure,
    baseMeasureName: string,
    spaceKey: string
  ) {
    const func: ICsnFunction = {
      as: measureKey,
      func: "CONVERT_UNIT",
      args: {
        QUANTITY: {
          ref: ["$projection", baseMeasureName],
        },
        CLIENT: {
          val: measure.client,
        },
        // SCHEMA: { val: spaceKey }, will be added during deployment in ConversionPredeployTask to fix issue with export/import
        ERROR_HANDLING: {
          val: ReverseErrorHandlingMapping.get(measure.errorHandling),
        },
        RATES_TABLE: {
          val: "SAP.UNIT.VIEW.T006",
        },
        DIMENSION_TABLE: {
          val: "SAP.UNIT.VIEW.T006D",
        },
      },
    };

    // TARGET_UNIT
    let targetUnitValue = "";
    switch (measure.targetUnitType) {
      case AnalyticModelTargetUnitType.constantValue:
        targetUnitValue = (measure.targetUnit as IAnalyticModelConstantValue).value;
        func.args.TARGET_UNIT = {
          val: targetUnitValue,
        };
        break;
      case AnalyticModelTargetUnitType.attribute:
        targetUnitValue = (measure.targetUnit as IAnalyticModelAttributeKey).key;
        func.args.TARGET_UNIT = {
          ref: ["$projection", targetUnitValue],
        };
        break;
      case AnalyticModelTargetUnitType.variable:
        targetUnitValue = (measure.targetUnit as IAnalyticModelVariableKey).key;
        func.args.TARGET_UNIT = {
          ref: [targetUnitValue],
          param: true,
        };
        break;
      default:
        break;
    }

    if (targetUnitValue === "") {
      return;
    }

    return func;
  }

  /**
   * @description Creates a CSN function call for a NCUM measure
   * @static
   * @param {string} measureKey
   * @param {string} baseMeasureName
   * @param {string} timeDimensionName
   * @param {AnalyticModelExceptionAggregationNcumType} exceptionAggregationBehavior
   * @param {string} [recordTypeFieldName]
   * @param {IAnalyticModelConstantValue} [reportingMinStartTime]
   * @param {IAnalyticModelConstantValue} [reportingMaxEndTime]
   * @param {boolean} [setNullToZero]
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  public static createNcumMeasureFunctionCall(
    measureKey: string,
    baseMeasureName: string,
    timeDimensionName: string,
    exceptionAggregationBehavior: AnalyticModelExceptionAggregationNcumType,
    recordTypeFieldName?: string,
    reportingMinStartTime?: IAnalyticModelConstantValue,
    reportingMaxEndTime?: IAnalyticModelConstantValue,
    setNullToZero?: boolean
  ) {
    const func: ICsnFunction = {
      as: measureKey,
      func: "CUMULATE_OVER_TIME",
      args: {
        MEASURE: {
          // Required, reference to base measure
          ref: ["$projection", baseMeasureName],
        },
        TIME_DIMENSION: {
          // Required, reference to time dimension key
          ref: ["$projection", timeDimensionName],
        },
        AGGREGATION_BEHAVIOR: {
          // Required, allowed exception aggregations --> FIRST, LAST, MAX, MIN, SUM, AVERAGE_NULL, COUNT_NULL
          val: ReverseExceptionAggregationNcumTypeMapping.get(exceptionAggregationBehavior),
        },
      },
    };

    // Add optionals
    if (recordTypeFieldName) {
      func.args.RECORD_TYPE = {
        // Optional
        ref: ["$projection", recordTypeFieldName],
      };
    }

    if (reportingMinStartTime) {
      func.args.VALIDITY_START_TIME = {
        // Optional, must be a valid master data value of the time dimension
        val: reportingMinStartTime.value,
      };
    }

    if (reportingMaxEndTime) {
      func.args.VALIDITY_END_TIME = {
        // Optional, must be a valid master data value of the time dimension
        val: reportingMaxEndTime.value,
      };
    }

    if (setNullToZero) {
      func.args.SET_NULL_TO_ZERO = {
        // Optional
        val: setNullToZero,
      };
    }

    return func;
  }

  /**
   * @description Create expression for a given formula
   * @static
   * @param {string} formula
   * @param {boolean} [bCaseValidation=true]
   * @returns {*}  {(ICreateExpressionResult | IANALYTIC_MODEL_LOCALIZED_MESSAGE[])}
   * @memberof AnalyticModelCsnConverter
   */
  public static createXprFromFormula(
    formula: string,
    bCaseValidation = true
  ): ICreateExpressionResult | IANALYTIC_MODEL_LOCALIZED_MESSAGE[] {
    try {
      const xpr = analyticModelCdsParseExprSync(formula);
      let errors = CsnXprToAMHelper.inValidation(xpr.expr);
      if (errors.length > 0) {
        return errors;
      }
      if (!bCaseValidation) {
        return xpr;
      }
      errors = CsnXprToAMHelper.caseValidation(xpr.expr);
      if (errors.length > 0) {
        return errors;
      }
      return xpr;
    } catch (err) {
      throw new Error(err);
    }
  }

  /**
   * @description Generate an alias for an attribute
   * @static
   * @param {string} [attributeName]
   * @param {string} [entityName]
   * @returns {*}  {string}
   * @memberof AnalyticModelCsnConverter
   */
  public static generateAttributeAlias(attributeName?: string, entityName?: string): string {
    if (!attributeName) {
      return "";
    }
    // TODO: Replace special characters like . or / ?
    return entityName ? `${entityName}_${attributeName}` : attributeName;
  }

  /**
   * @description Generate an alias for a table
   * @static
   * @param {string} name
   * @returns {*}  {string}
   * @memberof AnalyticModelCsnConverter
   */
  public static generateTableAlias(name: string): string {
    const alias = name.replace(/[.]/g, "");
    AnalyticModelCsnConverter.aliasMap.set(alias, name);
    return alias;
  }

  /**
   * @description Get the table name for a given alias
   * @static
   * @param {string} alias
   * @returns {*}  {string}
   * @memberof AnalyticModelCsnConverter
   */
  public static getTableForAlias(alias: string): string {
    // TODO: Can be removed with FF DWCO_MODELING_CB_PREDEPLOYMENT_ON_DEPLOY, will always return alias
    return AnalyticModelCsnConverter.aliasMap.get(alias) ?? alias;
  }

  // MARK: CONSTRUCTOR
  /**
   * Creates an instance of AnalyticModelCsnConverter.
   * @constructor
   * @param {IRequestContext} context
   * @param {IAnalyticModel} queryModel
   * @param {string} spaceKey
   * @param {string} [packageId]
   * @param {string} [folderAssignment]
   * @param {ICsnDefinitions} [dependentObjects]
   * @param {DbHints} [dbHints]
   * @memberof AnalyticModelCsnConverter
   */
  constructor(
    protected context: IRequestContext,
    protected queryModel: IAnalyticModel,
    protected spaceKey: string,
    protected packageId?: string,
    protected folderAssignment?: string,
    protected dependentObjects?: ICsnDefinitions,
    protected dbHints?: DbHints
  ) {
    this.sourceModelsByName = {};
    this.idMapping = {};

    if (dependentObjects) {
      Object.keys(dependentObjects).forEach((dependentObjectKey) => {
        this.sourceModelsByName[dependentObjectKey] = dependentObjects[dependentObjectKey];
      });
    }

    this.performanceTracker = new AnalyticModelPerformanceTracker();
  }

  // MARK: PUBLIC METHODS
  /**
   * Converts a calculated measure to a CSN expression (xpr).
   *
   * This function takes an `IAnalyticModelCalculatedMeasure` object and converts its formula to a CSN expression.
   * It uses the CDS parser to parse the formula and then traverses the expression to add `$projection` to all references,
   * cast types or adapt function names.
   *
   * @param {IAnalyticModelCalculatedMeasure} calculatedMeasure - The calculated measure to be converted.
   * @returns {any} The converted CSN expression.
   * @throws {CodedError} If the cast type is not valid.
   * @memberof AnalyticModelCsnConverter
   */
  public convertCalculatedMeasureToCsnXpr(
    calculatedMeasure: IAnalyticModelCalculatedMeasure | IAnalyticModelCalculatedCrossCalculation
  ): any {
    const formula =
      calculatedMeasure.formulaRaw && calculatedMeasure.formula
        ? calculatedMeasure.formulaRaw
        : CsnXprToAMHelper.convertFormulaToString(calculatedMeasure);

    // Use CDS to parse the formula
    const xpr = AnalyticModelCsnConverter.createXprFromFormula(formula) as ICreateExpressionResult;

    const callback = (type: XPR_PARTS, expr: any) => {
      switch (type) {
        case XPR_PARTS.REF:
          expr.ref.unshift("$projection");
          break;
        case XPR_PARTS.CAST:
          const cdsType = getMappedHanaType(expr.cast?.type.toUpperCase());
          if (cdsType === undefined) {
            throw new CodedError(
              "typeInCastNotValid",
              `type ${expr.cast?.type} is not a valid type in function CAST`,
              StatusCodes.BAD_REQUEST
            );
          }
          expr.cast.type = cdsType;
          break;
        case XPR_PARTS.FUNC:
          this.adaptFunction(expr as CDS_EXPR_FUNC);
          break;
        default:
          break;
      }
    };
    // Traverse the expression and add $projection to all references, cast types or adapt function names
    CsnXprToAMHelper.traverse(xpr?.expr, callback);

    // If the outer expression is a cast, we need to wrap it in an xpr
    if (XPR_PARTS.CAST in xpr?.expr) {
      xpr.expr = {
        xpr: [xpr.expr],
      };
    }
    return xpr?.expr;
  }

  public adaptFunction(context: CDS_EXPR_FUNC) {
    if (!this.ffGrandTotal) {
      return;
    }
    const functionName = context.func;
    if (functionName.toUpperCase() === "GRANDTOTAL") {
      context.func = "GRAND_TOTAL";
    }
  }

  /**
   * @description Convert a filter to a CSN expression
   * @param {IAnalyticModelFilter} filters
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  public convertFilterToCsnXpr(filters: IAnalyticModelFilter): any[] {
    if (filters.formula && filters.formula.length > 0 && filters.formulaRaw === undefined) {
      // old model where formulaRaw was deleted
      const where: any[] = [];
      const oAllowedTokensExp = new RegExp(
        "\\b(NOT IN|IN|CONTAINS NOT|CONTAINS|NOT|AND|OR|BETWEEN)\\b|\\(|\\)|,|<=|>=|<|>|≠|=|(\\[(\\d+)\\])|(\\S+)",
        "gi"
      );
      const aMatches = filters.formula.match(oAllowedTokensExp) || [];
      aMatches.forEach((match: string) => {
        if (match.startsWith("[")) {
          const eleNr = match.substring(1, match.length - 1);
          const ele = filters.elements![Number(eleNr)];
          if (ele.operandType === AnalyticModelFilterOperandType.ConstantValue) {
            where.push({
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
              val: (ele as IAnalyticModelFilterOperandConstant).value,
            });
          } else if (ele.operandType === AnalyticModelFilterOperandType.Attribute) {
            where.push({
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
              ref: ["$projection", ele.key],
            });
          }
        } else {
          where.push(match);
        }
      });
      return where;
    }
    if (filters.formula && filters.formula.length > 0 && filters.formulaRaw && filters.formulaRaw.length > 0) {
      const xpr = analyticModelCdsParseExprSync(filters.formulaRaw);
      const callback = (type: XPR_PARTS, xpr: any): void => {
        if (type === XPR_PARTS.REF) {
          xpr.ref.unshift("$projection");
        }
      };
      CsnXprToAMHelper.traverse(xpr?.expr, callback);
      return xpr.expr.xpr;
    }

    return [];
  }

  /**
   * @description Convert a restricted measure to a CSN expression (or ref, if only one element is used)
   * @param {IAnalyticModelRestrictedMeasure} restrictedMeasure
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  public convertRestrictedMeasureToCsnXpr(restrictedMeasure: IAnalyticModelRestrictedMeasure): any {
    if (restrictedMeasure.formula && restrictedMeasure.formula.length > 0) {
      const caseCsn: any[] = [];
      caseCsn.push("case");
      caseCsn.push("when");

      if (restrictedMeasure.formulaRaw && restrictedMeasure.formulaRaw.length > 0) {
        const xpr = analyticModelCdsParseExprSync(restrictedMeasure.formulaRaw);
        const callback = (type: XPR_PARTS, xpr: any): void => {
          if (type === XPR_PARTS.REF) {
            xpr.ref.unshift("$projection");
          }
        };
        CsnXprToAMHelper.traverse(xpr?.expr, callback);
        caseCsn.push(...xpr.expr.xpr);
      } else {
        const oAllowedTokensExp = new RegExp(
          "\\b(NOT IN|IN|CONTAINS NOT|CONTAINS|NOT|AND|OR|BETWEEN)\\b|\\(|\\)|,|<=|>=|<|>|≠|=|!=|(\\[(\\d+)\\])|(\\S+)",
          "gi"
        );
        const aMatches = restrictedMeasure.formula?.match(oAllowedTokensExp);
        if (aMatches != null) {
          aMatches.forEach((match) => {
            if (match.startsWith("[")) {
              const eleNr = match.substring(1, match.length - 1);
              const ele = restrictedMeasure.elements?.[Number(eleNr)];
              if (ele?.operandType === AnalyticModelRestrictedMeasureOperandType.ConstantValue) {
                caseCsn.push({
                  val: ele.value,
                });
              } else if (ele?.operandType === AnalyticModelRestrictedMeasureOperandType.Attribute) {
                caseCsn.push({
                  ref: ["$projection", ele.key],
                });
              } else if (ele?.operandType === AnalyticModelRestrictedMeasureOperandType.FilterVariable) {
                caseCsn.push({
                  ref: [ele.key],
                  param: true,
                });
              }
            } else {
              caseCsn.push(match);
            }
          });
        }
      }
      caseCsn.push("then");

      if (restrictedMeasure.hasOwnProperty("sourceKey")) {
        caseCsn.push({
          ref: [restrictedMeasure.key], // TODO put source measure in query measures
        });
      } else {
        caseCsn.push({
          ref: ["$projection", restrictedMeasure.key],
        });
      }

      caseCsn.push("end");
      return caseCsn;
    } else {
      return {
        ref: ["$projection", restrictedMeasure.key],
      };
    }
  }

  /**
   * @description Main function to convert the Analytic Model to a CSN
   * @param {boolean} executePredeployTasks
   * @param {boolean|undefined} executeImpactValidations - Used in V2 for stacking
   * @returns {Promise<AnalyticModelCsnConversionResult>}
   * @memberof AnalyticModelCsnConverter
   */
  public async convertToCsn(
    executePredeployTasks: boolean,
    executeImpactValidations?: boolean
  ): Promise<AnalyticModelCsnConversionResult> {
    if (!this.conversionResult) {
      // should not happen, but just to be sure
      await this.initConverter();
    }

    if (this.conversionResult.hasErrors) {
      this.conversionResult.performance = this.performanceTracker.getResults();
      return this.conversionResult;
    }

    try {
      // Build Query

      const cube = this.buildDesignTimeCsn();

      this.conversionResult.csn!.definitions[this.queryModel.identifier.key] = cube;

      // Execute Predeploy Tasks (generate CSN with inherited information)
      if (executePredeployTasks) {
        let elementMapping: AnalyticModelCsnConversionElementMapping | undefined;

        const factSource =
          this.queryModel.sourceModel.factSources[Object.keys(this.queryModel.sourceModel.factSources)[0]];
        const factSourceCsn = this.sourceModelsByName[factSource.dataEntity.key];
        const isFactSourceAnalyticModel = AnalyticModelSharedHelper.isAnalyticModel(factSourceCsn);

        if (
          isFactSourceAnalyticModel &&
          (await AnalyticModelHelper.isStackingEnabledForAnalyticModelCsn(this.context, cube))
        ) {
          const details = ["id", "qualified_name", "#fullCsnForDeployment"];
          const param: IGetObjectParameters = {
            folderNames: this.spaceKey,
            qualifiedNames:
              this.queryModel.sourceModel.factSources[Object.keys(this.queryModel.sourceModel.factSources)[0]]
                .dataEntity.key,
            details,
          };
          const objects = await RepositoryObjectClient.getObject(this.context, param);
          const fullCsn = objects[0].properties["#fullCsnForDeployment"];
          fullCsn!.definitions[this.queryModel.identifier.key] = cube;
          await extendFullCsnForAnalyticModel(fullCsn!, this.context, this.spaceKey);
        } else {
          elementMapping = await AnalyticModelHelper.executePredeployTasks(
            cube,
            this.context,
            this.queryModel.identifier.key,
            this.spaceKey,
            this.sourceModelsByName
          );

          if (elementMapping) {
            this.conversionResult.generatedElementMapping = elementMapping;
          }
        }

        this.measurePerformance("Run Predeploy Steps");
      }
    } catch (e) {
      this.conversionResult.hasErrors = true;
      this.conversionResult.errorMessage = e.messages || e.message || (e.details && e.details.message);
      if (e instanceof CodedError) {
        this.conversionResult.code = e.code;
        if (e.status) {
          this.conversionResult.status = e.status;
        }
        if (e.parameters) {
          this.conversionResult.parameters = e.parameters;
        }
      }
      this.logger.logError([`Failed to convert Analytic Model to CSN on save`, e], { context: this.context });
    }

    const kibanaMsg = `convertCsn - Performance - ${this.conversionResult.performance?.slice().pop()}`;
    this.logger.logInfo(kibanaMsg, { context: this.context });

    // CSN is always returned, in case of an error the elements and query sections will be empty.
    this.conversionResult.performance = this.performanceTracker.getResults();
    return this.conversionResult;
  }

  /**
   * @description Initialize converter. Execute basic checks, validate JSON schema, determine feature flags, attach backpack to CSN, run validations
   * @returns {*}  {Promise<AnalyticModelCsnConversionResult>}
   * @memberof AnalyticModelCsnConverter
   */
  public async initConverter(): Promise<AnalyticModelCsnConversionResult> {
    // Very Basic Checks
    try {
      // Basic check: Model provided?
      if (Object.keys(this.queryModel).length === 0) {
        // throw new Error("Invalid Analytic Model - Empty");
        throw new CodedError("convertCsnEmpty", "Invalid Analytic Model - Empty", StatusCodes.BAD_REQUEST);
      }

      // Basic check: Technical Name provided?
      if (!this.queryModel?.identifier?.key) {
        // throw new Error("Invalid Analytic Model - Technical Name missing");
        throw new CodedError(
          "convertCsnTechNameMissing",
          "Invalid Analytic Model - Technical Name missing",
          StatusCodes.BAD_REQUEST
        );
      }

      if (AnalyticModelHelper.isUnionNecessary(this.queryModel)) {
        // throw new Error("Invalid Analytic Model - Only Single Fact Models are supported");
        throw new CodedError(
          "convertCsnOnlySingleFact",
          "Invalid Analytic Model - Only Single Fact Models are supported",
          StatusCodes.BAD_REQUEST
        );
      }

      this.measurePerformance("Basic Checks");

      this.logger.logInfo(`Initialize Converter for AM "${this.queryModel.identifier.key}"`, { context: this.context });

      if (this.validateJsonSchema) {
        // JSON Schema Validation
        let validationResult: AnalyticModelJsonSchemaValidationResult;
        try {
          validationResult = await validateAnalyticModelJson(this.queryModel, true);
          this.conversionResult.jsonSchemaValidationResult = validationResult;

          if (!validationResult.bValid) {
            this.conversionResult.hasErrors = true;
            const errorsAsString = JSON.stringify(validationResult.errors);
            this.conversionResult.errorMessage = `Invalid Analytic Model: ${errorsAsString}`;
            this.conversionResult.status = StatusCodes.BAD_REQUEST;
            this.conversionResult.code = ErrorCodes.INVALID_INPUT;
            delete this.conversionResult.csn;
          }
        } catch (err) {
          this.conversionResult.hasErrors = true;
          this.conversionResult.jsonSchemaValidationResult = err.message;
        }

        this.measurePerformance("JSON Schema Validation");

        // use cleansed analytic model JSON from schema validator
        // all properties that are not defined in JSON schema are removed
        const removedProperties = getRemovedProperties(
          this.queryModel,
          validationResult!.validatedAndCleanedAnalyticModel
        );

        // ########################
        // THIS IS A TEMPORARY LOG TO IDENTIFY IF THE MEASURES ARE BEING REORDERED
        if (
          !_.isEqual(
            Object.keys(this.queryModel.measures ?? {}),
            Object.keys(validationResult!.validatedAndCleanedAnalyticModel.measures ?? {})
          )
        ) {
          this.logger.logVerbose(
            `Measure Order of Analytic Model ${this.queryModel.identifier.key} changed during JSON schema validation.`,
            { context: this.context }
          );
        }

        if (
          !_.isEqual(
            Object.keys(this.queryModel.attributes ?? {}),
            Object.keys(validationResult!.validatedAndCleanedAnalyticModel.attributes ?? {})
          )
        ) {
          this.logger.logVerbose(
            `Attribute Order of Analytic Model ${this.queryModel.identifier.key} changed during JSON schema validation.`,
            { context: this.context }
          );
        }
        // ########################

        if (removedProperties.length > 0) {
          this.queryModel = validationResult!.validatedAndCleanedAnalyticModel;

          this.logger.logVerbose(
            `These properties are removed from analytic model: ${JSON.stringify(removedProperties)}`,
            { context: this.context }
          );
        }

        if (this.conversionResult.hasErrors) {
          this.conversionResult.performance = this.performanceTracker.getResults();
          return this.conversionResult;
        }
      }

      this.ffDacSupport = await AnalyticModelHelper.isDacSupportEnabled(this.context);
      this.ffUnitConversion = await FeatureFlagProvider.isFeatureActive(
        this.context,
        "DWCO_MODELING_AM_UNIT_CONVERSION"
      );
      this.ffFormatOfMeasures = await FeatureFlagProvider.isFeatureActive(
        this.context,
        "DWCO_MODELING_AM_FORMAT_OF_MEASURES"
      );
      this.ffGrandTotal = await FeatureFlagProvider.isFeatureActive(this.context, "DWCO_MODELING_AM_GRAND_TOTAL");
      this.ffProtectDataExport = await FeatureFlagProvider.isFeatureActive(
        this.context,
        "DWCO_MODELING_AM_PROTECT_DATA_EXPORT"
      );
      this.ffCrossCalculation = await FeatureFlagProvider.isFeatureActive(
        this.context,
        "DWCO_MODELING_AM_MULTI_STRUCTURE"
      );
      this.ffMultiRange = await FeatureFlagProvider.isFeatureActive(this.context, "DWCO_MODELING_AM_MULTI_RANGE");
      this.ffFiscalVariable = await FeatureFlagProvider.isFeatureActive(
        this.context,
        "DWCO_MODELING_SUPPORT_FISCAL_TIME"
      );
      this.ffDbHints = await FeatureFlagProvider.isFeatureActive(this.context, "DWCO_MODELING_HINTS_FOR_MDS");

      // Fundamental Checks have passed, build the result in order to be able to save the model
      // Populates the csn result even if there are errors during validation
      this.conversionResult = this.getInitialConversionResult();

      this.attachModelAsBackpack(this.conversionResult.csn!, this.queryModel);

      const isFeatureFlagActive: fIsFeatureFlagActive = async (featureFlag: string): Promise<boolean> =>
        await FeatureFlagProvider.isFeatureActive(this.context, featureFlag as any);

      const simpleTypeMapping: ISimpleTypeMapping = {};

      // Load relevant data (like related models from the repo)
      await this.loadModels(simpleTypeMapping);

      // Model Validation
      const validationMessages = await QueryModelValidator.validate(
        this.queryModel,
        createCdsFormulaToExprConverter,
        this.sourceModelsByName,
        isFeatureFlagActive,
        simpleTypeMapping,
        undefined,
        undefined,
        {
          hanaCeVersion: await AnalyticModelHelper.determineCustomerHanaCEVersion(this.context),
        }
      );

      this.measurePerformance("Model Validation");

      if (validationMessages.length) {
        if (validationMessages.some((msg: ValidationMessage) => msg.type === ValidationMessageType.ERROR)) {
          // For now, we throw error based on the first error message
          const firstError: ValidationMessage = validationMessages.find(
            (msg) => msg.type === ValidationMessageType.ERROR
          )!;

          throw new CodedError(firstError.messageKey!, firstError.message, StatusCodes.BAD_REQUEST, {
            p0: firstError.parameters,
          });

          /* In the future aggregate all errors from model in AggregateError, this is available only in nodejs v15.0.0
            const errors = validationMessages.map(({message}) => (new Error(message)))
            throw new AggregateError(errors);
          */
        } else {
          // Warning, no error
          this.conversionResult.hasWarnings = true;
          this.conversionResult.warningMessage = validationMessages[0].message;
          this.conversionResult.parameters = validationMessages[0].parameters;
          this.conversionResult.code = validationMessages[0].messageKey!;
        }
      }

      // No basic issues detected
      this.buildDependencyTree();
    } catch (e) {
      this.conversionResult.hasErrors = true;
      this.conversionResult.errorMessage = e.messages || e.message || (e.details && e.details.message);
      if (e instanceof CodedError) {
        this.conversionResult.code = e.code;
        if (e.status) {
          this.conversionResult.status = e.status;
        }
        if (e.parameters) {
          this.conversionResult.parameters = e.parameters;
        }
      }
      this.logger.logError([`Failed to convert Analytic Model to CSN on save`, e], { context: this.context });
    }

    // CSN is always returned, in case of an error it is basically blank (see initial result.csn in line 77)
    this.conversionResult.performance = this.performanceTracker.getResults();
    return this.conversionResult;
  }

  // MARK: PROTECTED METHODS
  /**
   * @description Add attributes to the CSN
   * @protected
   * @param {ICsnDefinition} queryCsn
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected addAttributesToCsn(queryCsn: ICsnDefinition) {
    Object.entries(this.queryModel.attributes || {}).forEach(([queryAttributeKey, attribute]) => {
      if (
        !this.isAttributeAvailableInSource(attribute, queryAttributeKey) ||
        this.isAttributeHiddenInSource(attribute, queryAttributeKey) ||
        this.isAttributeTextElementInSource(attribute, queryAttributeKey)
      ) {
        // Attribute is not available, hidden or text element in the source entity, skip the rest of this iteration, attribute is not relevant
        return;
      }

      const sourceGeneratedName = AnalyticModelCsnConverter.generateAttributeAlias(queryAttributeKey);

      // Generate CSN names for the elements

      const ref = this.determineAttributeRef(queryAttributeKey, attribute);
      const csnAttribute: any = {};

      if (attribute.attributeType === AnalyticModelAttributeType.FactSourceAttribute) {
        const factSourceAttribute: IAnalyticModelFactSourceAttribute = attribute;

        csnAttribute[CDSElementKeys.LABEL] = factSourceAttribute?.text;
      } else if (attribute.attributeType === AnalyticModelAttributeType.DimensionSourceAttribute) {
        const dimensionSourceAttribute: IAnalyticModelDimensionSourceAttribute = attribute;

        csnAttribute[CDSElementKeys.LABEL] = dimensionSourceAttribute?.text;
        csnAttribute[CDSElementKeys.NAVIGATION_ATTRIBUTE_REF] = this.determineAttributeNavRef(attribute);
      }

      if (
        // Might've been added already as part of an association => mark as hidden
        queryCsn.query?.SELECT?.columns?.find((column) => _.isEqual(column.ref, ref)) &&
        this.isMapElementRepKey(queryAttributeKey) // Hide it only if an association exists for an element. If no association exists, it will be used as a standalone attribute => don't hide it.
      ) {
        csnAttribute[HiddenAnnotations.UI_HIDDEN] = true; // Prevent doubled fields in the Preview/Story
      }

      if (!queryCsn.query?.SELECT?.columns?.find((column) => "as" in column && column.as === sourceGeneratedName)) {
        const column = {
          ref,
          as: sourceGeneratedName,
        } as any;

        queryCsn.query!.SELECT!.columns.push(column);

        // Not already added as part of an association => add it
        queryCsn.elements![sourceGeneratedName] = csnAttribute;
      }
    });
  }

  /**
   * @description Add dbHints to the CSN definition
   * @protected
   * @param {ICsnDefinition} csnDefinitions
   * @param {*} csnMeasure
   * @memberof AnalyticModelCsnConverter
   */
  protected addDbHintsToCsn(csnDefinitions: ICsnDefinition) {
    if (!this.ffDbHints || !this.dbHints || this.dbHints.length === 0) {
      // Feature flag is not active or no dbHints available, skip adding dbHints to CSN
      return;
    }

    (csnDefinitions as any)["@Analytics.dbHints"] = this.dbHints;
  }

  /**
   * @description Add exception aggregation to the CSN definition of a measure or cross calculation
   * @protected
   * @param {IAnalyticModelMeasure} modelMeasure
   * @param {*} csnMeasure
   * @memberof AnalyticModelCsnConverter
   */
  protected addExceptionAggregation(
    modelMeasure: IAnalyticModelMeasure | IAnalyticModelCalculatedCrossCalculation,
    csnMeasure: any
  ) {
    if (modelMeasure.hasOwnProperty("exceptionAggregationType")) {
      const exceptionAggregationBehavior = ReverseExceptionAggregationTypeMapping.get(
        modelMeasure.exceptionAggregationType!
      );
      const exceptionAggregationElements = modelMeasure.exceptionAggregationAttributes?.reduce(
        (acc: any[], exAggElement: string) => acc.concat([exAggElement]),
        []
      );

      csnMeasure["@AnalyticsDetails.exceptionAggregationSteps"] = [
        {
          exceptionAggregationBehavior: {
            "#": exceptionAggregationBehavior,
          },
          exceptionAggregationElements,
        },
      ];
    }
  }

  /**
   * @description Add folder info to _meta of the CSN
   * @protected
   * @param {ICsnDefinition} queryCsn
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected addFolderMetaInfoToCsn(queryCsn: ICsnDefinition) {
    const folderAssignment = this.folderAssignment;
    if (!folderAssignment) {
      return;
    }

    if (!queryCsn._meta) {
      queryCsn._meta = {};
    }
    queryCsn._meta.dependencies = {
      folderAssignment,
    };
  }
  /**
   * @description Add new optional flag to define if export data is protected in the model
   * @protected
   * @param {ICsnDefinition} queryCsn
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected addProtectExportDataFlagToCsn(queryCsn: ICsnDefinition): void {
    if (!this.isStackingAM()) {
      if (this.queryModel.preventAnalyticsCloudDataExport) {
        (queryCsn as any)["@DataWarehouse.preventAnalyticsCloudDataExport"] =
          this.queryModel.preventAnalyticsCloudDataExport;
      }
    }
  }
  /**
   * @description Add key date variable for time dependency to the CSN
   * @protected
   * @param {ICsnDefinition} queryCsn
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected addKeyDateVariableToCsn(queryCsn: ICsnDefinition): void {
    const keyDateVariable = Object.entries(this.queryModel.variables ?? {}).find(
      ([variableName, variable]) => variable.parameterType === AnalyticModelParameterType.KeyDate
    );

    if (keyDateVariable) {
      const variableName = keyDateVariable[0];
      const variable = keyDateVariable[1] as any;
      const amParam: any = {
        [CDSElementKeys.LABEL]: variable.text,
        "@Semantics.businessDate.at": true,
        type: "cds.Date",
      };

      if ("defaultValue" in variable && variable.defaultValue) {
        amParam["@AnalyticsDetails.variable.defaultValue"] = getValueBasedOnCdsType(
          "cds.Date",
          (variable as IAnalyticModelKeyDateVariable_MANUALINPUT).defaultValue
        );
      }

      // Add amParam to queryCsn.params (queryCsn.params may not exist yet)
      (queryCsn as any).params = (queryCsn as any).params || {};
      (queryCsn as any).params[variableName] = amParam;

      return amParam;
    }
  }

  /**
   * @description Add fiscal variant variable to the CSN
   * @protected
   * @param {ICsnDefinition} queryCsn
   * @returns
   * @memberof AnalyticModelCsnConverter
   */
  protected addFiscalVariantVariableToCsn(queryCsn: ICsnDefinition): any {
    if (!this.ffFiscalVariable) {
      return;
    }

    const fiscalVariable = Object.entries(this.queryModel.variables ?? {}).find(
      ([_, variable]) => variable.parameterType === AnalyticModelParameterType.FiscalVariant
    );

    if (!fiscalVariable) {
      return;
    }

    const variableName = fiscalVariable[0];
    const variable = fiscalVariable[1] as
      | IAnalyticModelFiscalVariantVariable_MANUALINPUT
      | IAnalyticModelFiscalVariantVariable_LOOKUP;
    const amParam: any = {
      [CDSElementKeys.LABEL]: variable.text,
      "@Semantics.fiscal.yearVariant": true,
      [HiddenAnnotations.VARIABLE_HIDDEN]: true,
    };

    if ("defaultValue" in variable && variable.defaultValue) {
      const typeInformation = AnalyticModelSharedHelper.determineAnalyticModelFiscalVariableDatatypeFromModel(
        this.queryModel,
        this.sourceModelsByName
      );
      amParam["@AnalyticsDetails.variable.defaultValue"] = getValueBasedOnCdsType(
        typeInformation?.type || "cds.String",
        variable.defaultValue
      );
      amParam.type = typeInformation?.type || "cds.String";
      amParam.length = typeInformation?.length;
    }

    // Add amParam to queryCsn.params (queryCsn.params may not exist yet)
    queryCsn.params = queryCsn?.params || {};
    queryCsn.params[variableName] = amParam;

    return amParam;
  }

  /**
   * @description Add measures to the CSN
   * @protected
   * @param {ICsnDefinition} queryCsn
   * @memberof AnalyticModelCsnConverter
   */
  protected addMeasuresToCsn(queryCsn: ICsnDefinition) {
    Object.keys(this.queryModel.measures || {}).forEach((measureKey) => {
      const measure = this.queryModel.measures![measureKey];

      if (measure.measureType === AnalyticModelMeasureType.CountDistinct) {
        const csnMeasure = this.createCsnMeasure(measureKey);
        (csnMeasure as any)["@Aggregation.referenceElement"] = _.clone(measure.countDistinctAttributes);
        csnMeasure.type = "cds.Integer";

        if (!queryCsn.elements?.hasOwnProperty(measureKey)) {
          queryCsn.elements![measureKey] = csnMeasure;
        }

        if (
          !queryCsn.query?.SELECT?.columns?.find(
            (column) => column.as === measureKey || _.isEqual(column.ref, [measureKey])
          )
        ) {
          const column = {
            val: null,
            as: measureKey,
          } as any;
          queryCsn.query!.SELECT!.columns.push(column);
        }
      } else if (measure.measureType === AnalyticModelMeasureType.FactSourceMeasure) {
        if (this.isMeasureHiddenInSource(measure)) {
          return;
        }

        const sourceGeneratedName = AnalyticModelCsnConverter.generateAttributeAlias(measureKey);

        const csnMeasure = this.createCsnMeasure(measureKey);
        const ref = this.generateMeasureRef(measure);

        if (!queryCsn.elements?.hasOwnProperty(sourceGeneratedName)) {
          queryCsn.elements![sourceGeneratedName] = csnMeasure;
        }

        if (
          !queryCsn.query?.SELECT?.columns?.find(
            (column) => column.as === sourceGeneratedName || _.isEqual(column.ref, [measureKey])
          )
        ) {
          const column = {
            ref,
            as: sourceGeneratedName,
          } as any;

          queryCsn.query!.SELECT!.columns.push(column);
        }
      } else if (measure.measureType === AnalyticModelMeasureType.CurrencyConversionMeasure) {
        const ccMeasure = measure;
        const bHaveToAddCalculatedAttribute =
          ccMeasure.referenceDateType === AnalyticModelReferenceDateType.sqlFunction;
        const calculatedAttributeName = AnalyticModelHelper.CURRENCY_CONVERSION_CURRENT_DATE_HELPER_NAME;
        const baseMeasureName = ccMeasure.key;
        if (bHaveToAddCalculatedAttribute) {
          // due to MDS doesn't support CURRENT_DATE in measure we have to add a calculated attribute
          if (!queryCsn.elements!.hasOwnProperty(calculatedAttributeName)) {
            queryCsn.elements![calculatedAttributeName] = {
              type: "cds.String",
              [CDSElementKeys.LABEL]: "[Hidden] Current Date calc",
              length: 5000,
              [HiddenAnnotations.CONSUMPTION_HIDDEN]: true,
            } as ICsnElement;

            queryCsn.query!.SELECT!.columns.push({
              xpr: [
                {
                  func: "CURRENT_DATE",
                },
              ],
              as: calculatedAttributeName,
            } as any);
          }
        }

        const csnMeasure = this.createCsnMeasure(measureKey);
        if (!queryCsn.elements?.hasOwnProperty(measureKey)) {
          queryCsn.elements![measureKey] = csnMeasure;
        }
        if (
          !queryCsn.query?.SELECT?.columns?.find(
            (column) => column.as === measureKey || _.isEqual(column.ref, [measureKey])
          )
        ) {
          const measure = this.queryModel.measures![measureKey] as IAnalyticModelCurrencyConversionMeasure;
          queryCsn.query!.SELECT!.columns.push(
            AnalyticModelCsnConverter.createCurrencyConversionFunctionCall(
              measureKey,
              measure,
              baseMeasureName,
              bHaveToAddCalculatedAttribute ? calculatedAttributeName : undefined,
              this.spaceKey
            ) as any
          );
        }
      } else if (measure.measureType === AnalyticModelMeasureType.UnitConversionMeasure) {
        if (!this.ffUnitConversion) {
          return;
        }

        const ccMeasure = measure;
        const baseMeasureName = ccMeasure.key;

        const csnMeasure = this.createCsnMeasure(measureKey);
        if (!queryCsn.elements?.hasOwnProperty(measureKey)) {
          queryCsn.elements![measureKey] = csnMeasure;
        }

        const columns = queryCsn.query?.SELECT?.columns;
        if (
          !columns?.find((column) => column.as === measureKey || _.isEqual(column.ref, [measureKey])) &&
          columns?.find((column) => column.as === baseMeasureName || _.isEqual(column.ref, [baseMeasureName]))
        ) {
          const measure = this.queryModel.measures![measureKey] as IAnalyticModelUnitConversionMeasure;
          const unitConversionCall = AnalyticModelCsnConverter.createUnitConversionFunctionCall(
            measureKey,
            measure,
            baseMeasureName,
            this.spaceKey
          ) as any;
          if (unitConversionCall) {
            columns.push(unitConversionCall);
          }
        }
      } else if (measure.measureType === AnalyticModelMeasureType.CalculatedMeasure) {
        if (measure.formulaRaw && measure.formulaRaw.length > 0 && (measure.formula ?? "").length === 0) {
          throw new Error(`Invalid Analytic Model - invalid formula '${measure.formulaRaw}' for measure ${measureKey}`);
        }

        const csnMeasure = this.createCsnMeasure(measureKey);
        if (!queryCsn.elements?.hasOwnProperty(measureKey)) {
          queryCsn.elements![measureKey] = csnMeasure;
        }
        if (
          !queryCsn.query?.SELECT?.columns?.find(
            (column) => column.as === measureKey || _.isEqual(column.ref, [measureKey])
          )
        ) {
          queryCsn.query!.SELECT!.columns.push(this.createCalculatedMeasureXpr(measureKey));
        }
      } else if (measure.measureType === AnalyticModelMeasureType.RestrictedMeasure) {
        if (measure.formulaRaw && measure.formulaRaw.length > 0 && (measure.formula ?? "").length === 0) {
          throw new Error(`Invalid Analytic Model - invalid formula '${measure.formulaRaw}' for measure ${measureKey}`);
        }

        const csnMeasure = this.createCsnMeasure(measureKey) as any;

        // Add Constant Selection
        if (measure.constantSelectionType && measure.constantSelectionType === AnalyticModelConstantSelectionType.All) {
          csnMeasure["@AnalyticsDetails.query.ignoreFurtherFilter.forAllElements"] = true;
        } else if (
          measure.constantSelectionType &&
          measure.constantSelectionType === AnalyticModelConstantSelectionType.Selected &&
          measure.constantSelectionAttributes &&
          Array.isArray(measure.constantSelectionAttributes) &&
          measure.constantSelectionAttributes.length > 0
        ) {
          const attributes = measure.constantSelectionAttributes.reduce(
            (acc, currentAttribute) => acc.concat([currentAttribute]),
            [] as any
          );

          csnMeasure["@AnalyticsDetails.query.ignoreFurtherFilter.forElement"] = attributes.reduce(
            (acc: any[], currentValue: string) => {
              // currentValue is the name of an attribute in the AM
              acc.push({ "=": currentValue });
              return acc;
            },
            [] as any[]
          );
        }

        if (!queryCsn.elements?.hasOwnProperty(measureKey)) {
          queryCsn.elements![measureKey] = csnMeasure;
        }
        if (
          !queryCsn.query?.SELECT?.columns?.find(
            (column) => column.as === measureKey || _.isEqual(column.ref, [measureKey])
          )
        ) {
          queryCsn.query!.SELECT!.columns.push(this.createRestrictedMeasureXpr(measureKey));
        }
      } else if (measure.measureType === AnalyticModelMeasureType.NonCumulativeMeasure) {
        if (!this.queryModel.nonCumulativeSettings) {
          return; // No settings => no NCUM measure
        }

        const csnMeasure = this.createCsnMeasure(measureKey);
        if (!queryCsn.elements?.hasOwnProperty(measureKey)) {
          queryCsn.elements![measureKey] = csnMeasure;
        }

        if (!queryCsn.query?.SELECT?.columns?.find((column) => column.as === measureKey)) {
          const dependencyElement = this.getDependencyElementForTargetTypeAndKey(
            AnalyticModelSourceType.Dimension,
            this.queryModel.nonCumulativeSettings.timeDimensionKey
          );

          const repKeyIndex = dependencyElement.targetFields.findIndex((field) => field.representativeKey);
          if (repKeyIndex === -1) {
            return;
          }
          const repKeyInSource = dependencyElement.sourceFields[repKeyIndex];
          if (!repKeyInSource || !repKeyInSource.attributeInModel) {
            return;
          }

          queryCsn.query!.SELECT!.columns.push(
            AnalyticModelCsnConverter.createNcumMeasureFunctionCall(
              measureKey, // measureKey
              measure.key, // baseMeasureName
              repKeyInSource.attributeInModel, // timeDimensionName
              measure.exceptionAggregationNcumType, // exceptionAggregationBehavior
              this.queryModel.nonCumulativeSettings.recordTypeAttributeKey, // recordTypeFieldName
              this.queryModel.nonCumulativeSettings.reportingMinStartTime, // reportingMinStartTime
              this.queryModel.nonCumulativeSettings.reportingMaxEndTime, // reportingMaxEndTime
              measure.setUnbookedDeltaToZero // setNullToZero
            ) as any
          );
        }
      }
    });
  }

  /**
   * @description Add package info to _meta of the CSN
   * @protected
   * @param {ICsnDefinition} queryCsn
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected addPackageMetaInfoToCsn(queryCsn: ICsnDefinition) {
    const packageValue = this.packageId ?? "_NONE_KEY_PACKAGE_";
    if (packageValue === "_NONE_KEY_PACKAGE_") {
      return;
    }
    if (queryCsn._meta) {
      queryCsn._meta["#repositoryPackage"] = packageValue;
    } else {
      queryCsn._meta = {
        "#repositoryPackage": packageValue,
      };
    }
  }

  /**
   * @description Add parameters (defined in the AM) to the CSN
   * @protected
   * @param {ICsnDefinition} queryCsn
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected addParametersToCsn(queryCsn: ICsnDefinition) {
    const factSourceKeys = Object.keys(this.queryModel.sourceModel.factSources);
    if (factSourceKeys.length === 0) {
      return;
    }

    const factSource = this.queryModel.sourceModel.factSources[factSourceKeys[0]];
    const factSourceModel = this.sourceModelsByName[factSource.dataEntity.key];

    const sourceParameterTypeMap: Map<string, string> = new Map();
    if (factSource.parameterMappings) {
      Object.keys(factSource.parameterMappings).forEach((parameterName) => {
        if (
          factSource.parameterMappings![parameterName].mappingType ===
          AnalyticModelSourceParameterMappingType.MapToSourceParameter
        ) {
          const sourceParameter = (factSourceModel as any).params[parameterName];
          const targetVariableName = (
            factSource.parameterMappings![parameterName] as IAnalyticModelSourceParameterMapping_VARIABLE
          ).variableName;
          sourceParameterTypeMap.set(targetVariableName, sourceParameter.type);
        }
      });
    }

    const queryCsnFrom = queryCsn.query?.SELECT?.from as any;
    if ((queryCsnFrom.ref && queryCsnFrom.ref.length > 0) || (queryCsnFrom.args && queryCsnFrom.args.length === 2)) {
      // write parameter in params section of CSN
      if (this.queryModel.variables) {
        const parameterKeys = Object.keys(this.queryModel.variables);
        if (parameterKeys && parameterKeys.length > 0) {
          parameterKeys.forEach((key) => {
            const parameter: any = structuredClone(this.queryModel.variables![key]);
            let csnParameter: any;
            if (parameter.parameterType === AnalyticModelParameterType.Input) {
              // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
              const ipParameter = parameter;
              csnParameter = {
                [CDSElementKeys.LABEL]: ipParameter.text,
              };

              // Manual Type Info
              if ("dataType" in ipParameter) {
                // Manual Type Info available, add info to the CSN parameter
                csnParameter.type = ipParameter.dataType!.type;

                if ("length" in ipParameter.dataType!) {
                  csnParameter.length = ipParameter.dataType.length;
                }

                if ("precision" in ipParameter.dataType!) {
                  csnParameter.precision = ipParameter.dataType.precision;
                }

                if ("scale" in ipParameter.dataType!) {
                  csnParameter.scale = ipParameter.dataType.scale;
                }

                if ("srid" in ipParameter.dataType!) {
                  csnParameter.srid = ipParameter.dataType.srid;
                }
              }

              // Add default value (if available)
              if ("defaultValue" in ipParameter) {
                csnParameter.default = getValueBasedOnCdsType(
                  sourceParameterTypeMap.get(key)!,
                  ipParameter.defaultValue
                );
              }

              // Add csnParameter to queryCsn.params (queryCsn.params may not exist yet)
              (queryCsn as any).params = (queryCsn as any).params || {};
              (queryCsn as any).params[key] = csnParameter;
            } else if (parameter.parameterType === AnalyticModelParameterType.Filter) {
              // Restricted Measure Variable

              const refField = AnalyticModelHelper.determineReferencedField(
                this.context,
                queryCsn,
                this.queryModel.identifier.key,
                parameter.referenceAttribute, // Always an attribute, never a measure
                this.sourceModelsByName,
                this.logger
              );

              if (
                parameter.selectionType === AnalyticModelVariableSelectionType.SINGLE ||
                parameter.selectionType === AnalyticModelVariableSelectionType.MULTIPLE_SINGLE
              ) {
                csnParameter = {
                  [CDSElementKeys.LABEL]: parameter.text,
                  "@AnalyticsDetails.variable.usageType": {
                    "#": "FILTER",
                  },
                  "@AnalyticsDetails.variable.selectionType": {
                    "#": "SINGLE",
                  },
                  "@AnalyticsDetails.variable.referenceElement": {
                    "=": parameter.referenceAttribute,
                  },
                  "@AnalyticsDetails.variable.multipleSelections": (parameter as IAnalyticModelFilterSelection)
                    .multipleSelections,
                  "@AnalyticsDetails.variable.mandatory":
                    parameter.mandatory === undefined ? true : parameter.mandatory,
                };
                if (parameter.hasOwnProperty("defaultValue")) {
                  if (parameter.selectionType === AnalyticModelVariableSelectionType.MULTIPLE_SINGLE) {
                    const defaultValues = (parameter as IAnalyticModelVariableManualInput)
                      .defaultValue as AnalyticModelParameterValueBaseType[];
                    if (Array.isArray(defaultValues) && defaultValues.length > 0) {
                      // For MULTIPLE_SINGLE we should save the default values as defaultRanges
                      csnParameter["@AnalyticsDetails.variable.defaultRanges"] = defaultValues.map((defaultValue) => ({
                        sign: ReverseAnalyticModelDefaultRangeSign.get(AnalyticModelDefaultRangeSign.INCLUDE)!,
                        option: ReverseAnalyticModelDefaultRangeOption.get(AnalyticModelDefaultRangeOption.EQ),
                        low: getValueBasedOnCdsType(refField!.type, defaultValue),
                        high: "",
                      }));
                    }
                  } else {
                    const defaultValue = getValueBasedOnCdsType(
                      refField!.type,
                      (parameter as IAnalyticModelFilterVariableSingleValueSingleSelection_MANUALINPUT).defaultValue
                    );
                    csnParameter["@AnalyticsDetails.variable.defaultValue"] = defaultValue;
                  }
                }
              } else if (parameter.selectionType === AnalyticModelVariableSelectionType.RANGE) {
                csnParameter = {
                  [CDSElementKeys.LABEL]: parameter.text,
                  "@AnalyticsDetails.variable.usageType": {
                    "#": "FILTER",
                  },
                  "@AnalyticsDetails.variable.selectionType": {
                    "#": "RANGE",
                  },
                  "@AnalyticsDetails.variable.referenceElement": {
                    "=": parameter.referenceAttribute,
                  },
                  "@AnalyticsDetails.variable.multipleSelections": (parameter as IAnalyticModelFilterSelection)
                    .multipleSelections,
                  "@AnalyticsDetails.variable.defaultRanges": [],
                  "@AnalyticsDetails.variable.mandatory":
                    parameter.mandatory === undefined ? true : parameter.mandatory,
                };
                if (parameter.hasOwnProperty("defaultValue")) {
                  if ((parameter as IAnalyticModelFilterSelection).multipleSelections) {
                    if (
                      Array.isArray((parameter as IAnalyticModelVariableManualInput).defaultValue) &&
                      ((parameter as IAnalyticModelVariableManualInput).defaultValue as any[]).length > 0
                    ) {
                      (
                        (parameter as IAnalyticModelVariableManualInput).defaultValue as IAnalyticModelDefaultRange[]
                      ).forEach((defaultValue) => {
                        const defaultRange: any = {
                          sign: ReverseAnalyticModelDefaultRangeSign.get(defaultValue.sign)!,
                          option: ReverseAnalyticModelDefaultRangeOption.get(defaultValue.option),
                          low: getValueBasedOnCdsType(refField!.type, defaultValue.lowValue),
                          high: getValueBasedOnCdsType(refField!.type, defaultValue.highValue),
                        };
                        csnParameter["@AnalyticsDetails.variable.defaultRanges"].push(defaultRange);
                      });
                    }
                  } else {
                    const defaultRange: any = {
                      sign: ReverseAnalyticModelDefaultRangeSign.get(
                        (parameter as IAnalyticModelFilterVariableRangeSingleSelection_MANUALINPUT).defaultValue!.sign
                      )!,
                      option: ReverseAnalyticModelDefaultRangeOption.get(
                        (parameter as IAnalyticModelFilterVariableRangeSingleSelection_MANUALINPUT).defaultValue!.option
                      ),
                      low: getValueBasedOnCdsType(
                        refField!.type,
                        (parameter as IAnalyticModelFilterVariableRangeSingleSelection_MANUALINPUT).defaultValue!
                          .lowValue
                      ),
                      high: getValueBasedOnCdsType(
                        refField!.type,
                        (parameter as IAnalyticModelFilterVariableRangeSingleSelection_MANUALINPUT).defaultValue
                          ?.highValue
                      ),
                    };
                    csnParameter["@AnalyticsDetails.variable.defaultRanges"].push(defaultRange);
                  }
                }
              } else if (parameter.selectionType === AnalyticModelVariableSelectionType.INTERVAL) {
                csnParameter = {
                  [CDSElementKeys.LABEL]: parameter.text,
                  "@AnalyticsDetails.variable.usageType": {
                    "#": "FILTER",
                  },
                  "@AnalyticsDetails.variable.selectionType": {
                    "#": "INTERVAL",
                  },
                  "@AnalyticsDetails.variable.referenceElement": {
                    "=": parameter.referenceAttribute,
                  },
                  "@AnalyticsDetails.variable.multipleSelections": parameter.multipleSelections,
                  "@AnalyticsDetails.variable.mandatory":
                    parameter.mandatory === undefined ? true : parameter.mandatory,
                };

                if (parameter.hasOwnProperty("defaultValue")) {
                  if (parameter.multipleSelections) {
                    const defaultValues = (parameter as IAnalyticModelVariableManualInput)
                      .defaultValue as IAnalyticModelDefaultInterval[];
                    if (Array.isArray(defaultValues) && defaultValues.length > 0) {
                      csnParameter["@AnalyticsDetails.variable.defaultValue"] = getValueBasedOnCdsType(
                        refField!.type,
                        defaultValues[0].lowValue
                      );
                      csnParameter["@AnalyticsDetails.variable.defaultValueHigh"] = getValueBasedOnCdsType(
                        refField!.type,
                        defaultValues[0].highValue
                      );
                    }
                  } else {
                    const defaultValue = (parameter as IAnalyticModelVariableManualInput)
                      .defaultValue as IAnalyticModelDefaultInterval;
                    if (!this.ffMultiRange || (defaultValue.lowValue !== undefined && defaultValue.lowValue !== "")) {
                      csnParameter["@AnalyticsDetails.variable.defaultValue"] = getValueBasedOnCdsType(
                        refField!.type,
                        defaultValue.lowValue
                      );
                    }
                    if (!this.ffMultiRange || (defaultValue.highValue !== undefined && defaultValue.highValue !== "")) {
                      csnParameter["@AnalyticsDetails.variable.defaultValueHigh"] = getValueBasedOnCdsType(
                        refField!.type,
                        defaultValue.highValue
                      );
                    }
                  }
                }

                if (
                  this.isFalsyExceptZero(csnParameter["@AnalyticsDetails.variable.defaultValue"]) &&
                  csnParameter["@AnalyticsDetails.variable.defaultValueHigh"]
                ) {
                  // high set, but low is not (if low is falsy, except for the value 0 which is considered valid), set low to high and unset high.
                  csnParameter["@AnalyticsDetails.variable.defaultValue"] =
                    csnParameter["@AnalyticsDetails.variable.defaultValueHigh"];
                  csnParameter["@AnalyticsDetails.variable.defaultValueHigh"] = undefined;
                }
                if (
                  csnParameter["@AnalyticsDetails.variable.defaultValueHigh"] === undefined ||
                  csnParameter["@AnalyticsDetails.variable.defaultValueHigh"] === ""
                ) {
                  // Get rid of undefined/empty strings
                  delete csnParameter["@AnalyticsDetails.variable.defaultValueHigh"];
                }
              }

              // Add csnParameter to queryCsn.params (queryCsn.params may not exist yet)
              (queryCsn as any).params = (queryCsn as any).params || {};
              (queryCsn as any).params[key] = csnParameter;
            } else if (parameter.parameterType === AnalyticModelParameterType.StoryFilter) {
              csnParameter = this.addStoryFilterVariable(parameter, queryCsn);
            } else if (parameter.parameterType === AnalyticModelParameterType.KeyDate) {
              csnParameter = this.addKeyDateVariableToCsn(queryCsn);
            } else if (parameter.parameterType === AnalyticModelParameterType.FiscalVariant) {
              csnParameter = this.addFiscalVariantVariableToCsn(queryCsn);
            } else {
              throw new Error(`Parameters of type ${parameter.parameterType} are not yet supported`);
            }

            if (parameter.order !== undefined) {
              csnParameter["@AnalyticsDetails.query.variableSequence"] = parameter.order;
            }

            // Add Variable Derivation Info
            if ("variableProcessingType" in parameter) {
              switch (parameter.variableProcessingType) {
                case AnalyticModelVariableProcessingType.LOOKUP:
                case AnalyticModelVariableProcessingType.DYNAMIC_DEFAULT:
                  const lookupParameter: any = parameter; // convert to any, to not do it multiple times
                  // Get rid of the defaultValue
                  delete csnParameter["@AnalyticsDetails.variable.defaultValue"];

                  // Lookup => Hidden
                  if (parameter.variableProcessingType !== AnalyticModelVariableProcessingType.DYNAMIC_DEFAULT) {
                    if (parameter.parameterType === AnalyticModelParameterType.StoryFilter) {
                      csnParameter[HiddenAnnotations.FILTER_HIDDEN] = true;
                    } else {
                      csnParameter[HiddenAnnotations.VARIABLE_HIDDEN] = true;
                    }
                  }
                  // Entity which is used to read the result
                  csnParameter["@Consumption.derivation.lookupEntity"] = {
                    "=": lookupParameter.lookupEntity,
                  };

                  // Element of the lookupEntity providing the result
                  csnParameter["@Consumption.derivation.resultElement"] = {
                    "=": lookupParameter.resultElement,
                  };

                  // Determine access to the lookupEntity by setting parameter values and/or a WHERE clause
                  csnParameter["@Consumption.derivation.binding"] = Object.entries(
                    lookupParameter.parameterBinding ?? {}
                  ).map(([bindingName, bindingValue]) => {
                    const binding: any = {
                      targetParameter: bindingName,
                    };

                    switch ((bindingValue as any).mappingType) {
                      case AnalyticModelSourceParameterMappingType.ConstantValue:
                        binding.type = { "#": "CONSTANT" };
                        binding.value = (
                          bindingValue as IAnalyticModelSourceParameterMapping_CONSTANTVALUE
                        ).constantValue;
                        break;
                      case AnalyticModelSourceParameterMappingType.MapToSourceParameter:
                        binding.type = { "#": "PARAMETER" };
                        binding.value = (bindingValue as IAnalyticModelSourceParameterMapping_VARIABLE).variableName;
                        break;
                      default:
                        break;
                    }

                    return binding;
                  });
                  break;
                case AnalyticModelVariableProcessingType.MANUAL_INPUT:
                default:
                  // Do nothing, already handled by each parameter type individually
                  break;
              }
            }
          });
        }
      }
    }
  }

  /**
   * @description Add parameter mappings to the fact source to the CSN
   * @protected
   * @param {ICsnDefinition} queryCsn
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected addParameterMappingsToCsn(queryCsn: ICsnDefinition) {
    const queryCsnFrom = queryCsn.query?.SELECT?.from as any;
    if (
      (!queryCsnFrom.ref || queryCsnFrom.ref.length === 0) &&
      (!queryCsnFrom.args || queryCsnFrom.args.length !== 2)
    ) {
      return;
    }

    // write source parameter in ref section of SELECT // TODO support only for fact source yet
    const factSource = this.queryModel.sourceModel.factSources[Object.keys(this.queryModel.sourceModel.factSources)[0]];
    if (!factSource || !factSource.parameterMappings) {
      return;
    }

    // Find ref of fact source in from part
    let correspondingFrom: any;
    const fnRecursiveRefFind = (fromPart: any): boolean => {
      if ("args" in fromPart) {
        return fromPart.args.some((arg: any) => fnRecursiveRefFind(arg));
      } else if ("ref" in fromPart) {
        if (fromPart.ref[0] === factSource.dataEntity.key) {
          correspondingFrom = fromPart;
          return true;
        }
      }
      return false;
    };

    if (!fnRecursiveRefFind(queryCsnFrom)) {
      throw new Error(`Ref of Fact Source ${factSource.dataEntity.key} could not be found`);
    }

    if (typeof correspondingFrom.ref[0] === "string") {
      correspondingFrom.ref = [
        {
          id: correspondingFrom.ref[0],
        },
      ];
    }

    if (!correspondingFrom.ref[0].hasOwnProperty("args")) {
      correspondingFrom.ref[0].args = {};
    }

    Object.keys(factSource.parameterMappings).forEach((key) => {
      const mapping = factSource?.parameterMappings?.[key];
      if (mapping) {
        switch (mapping.mappingType) {
          case AnalyticModelSourceParameterMappingType.ConstantValue:
            correspondingFrom.ref[0].args[key] = {
              val: mapping.constantValue,
            };
            break;
          case AnalyticModelSourceParameterMappingType.MapToSourceParameter:
            correspondingFrom.ref[0].args[key] = {
              param: true,
              ref: [mapping.variableName],
            };
            break;
          default:
            break;
        }
      }
    });
  }

  /**
   * @description Add data access controls to the CSN
   * @protected
   * @param {ICsnDefinition} queryCsn
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected addDataAccessControlsToCsn(queryCsn: ICsnDefinition) {
    return;
  }

  /**
   * @description Add story filter to the CSN
   * @protected
   * @param {IAnalyticModelStoryFilterVariable} storyFilterVariable
   * @param {ICsnDefinition} queryCsn
   * @returns {*} Attribute the story filter has been added to, undefined otherwise.
   * @memberof AnalyticModelCsnConverter
   */
  protected addStoryFilterVariable(
    storyFilterVariable: IAnalyticModelStoryFilterVariable,
    queryCsn: ICsnDefinition
  ): ICsnElement | undefined {
    if (storyFilterVariable) {
      const csnAttribute = queryCsn.elements?.[storyFilterVariable.referenceAttribute] as any;

      if (csnAttribute === undefined) {
        return; // TODO throw error
      }

      Object.assign(csnAttribute, {
        "@Consumption.filter.selectionType": {
          "#": ReverseAnalyticModelVariableSelectionType.get(storyFilterVariable.selectionType),
        },
        "@Consumption.filter.mandatory": storyFilterVariable.mandatory,
        "@Consumption.filter.hidden": false,
        "@Consumption.filter.multipleSelections": storyFilterVariable.multipleSelections,
      });

      if (storyFilterVariable.hasOwnProperty("defaultValue")) {
        switch (storyFilterVariable.selectionType) {
          case AnalyticModelVariableSelectionType.SINGLE:
            csnAttribute["@Consumption.filter.defaultValue"] = getValueBasedOnCdsType(
              csnAttribute.type,
              (storyFilterVariable as IAnalyticModelVariableManualInput).defaultValue as any
            );
            break;
          case AnalyticModelVariableSelectionType.MULTIPLE_SINGLE:
            const defaultValues = (storyFilterVariable as IAnalyticModelVariableManualInput)
              .defaultValue as AnalyticModelParameterValueBaseType[];
            if (Array.isArray(defaultValues) && defaultValues.length > 0) {
              // For MULTIPLE_SINGLE we should save the default values as defaultRanges
              const defaultValuesList: any[] = [];
              defaultValues.forEach((defaultValue) => {
                const range: any = {
                  sign: ReverseAnalyticModelDefaultRangeSign.get(AnalyticModelDefaultRangeSign.INCLUDE)!,
                  option: ReverseAnalyticModelDefaultRangeOption.get(AnalyticModelDefaultRangeOption.EQ),
                  low: getValueBasedOnCdsType(csnAttribute.type, defaultValue),
                  high: "",
                };
                defaultValuesList.push(range);
              });
              csnAttribute["@Consumption.filter.defaultRanges"] = defaultValuesList;
            }
            break;
          case AnalyticModelVariableSelectionType.INTERVAL:
            if (storyFilterVariable.multipleSelections) {
              const defaultValues = (storyFilterVariable as IAnalyticModelVariableManualInput)
                .defaultValue as IAnalyticModelDefaultInterval[];
              if (Array.isArray(defaultValues) && defaultValues.length > 0) {
                csnAttribute["@Consumption.filter.defaultValue"] = getValueBasedOnCdsType(
                  csnAttribute.type,
                  defaultValues[0].lowValue
                );
                csnAttribute["@Consumption.filter.defaultValueHigh"] = getValueBasedOnCdsType(
                  csnAttribute.type,
                  defaultValues[0].highValue
                );
              }
            } else {
              const defaultValue = (storyFilterVariable as IAnalyticModelVariableManualInput)
                .defaultValue as IAnalyticModelDefaultInterval;
              if (!this.ffMultiRange || (defaultValue.lowValue !== undefined && defaultValue.lowValue !== "")) {
                csnAttribute["@Consumption.filter.defaultValue"] = getValueBasedOnCdsType(
                  csnAttribute.type,
                  defaultValue.lowValue
                );
              }
              if (!this.ffMultiRange || (defaultValue.highValue !== undefined && defaultValue.highValue !== "")) {
                csnAttribute["@Consumption.filter.defaultValueHigh"] = getValueBasedOnCdsType(
                  csnAttribute.type,
                  defaultValue.highValue
                );
              }
            }
            if (
              this.isFalsyExceptZero(csnAttribute["@Consumption.filter.defaultValue"]) &&
              csnAttribute["@Consumption.filter.defaultValueHigh"]
            ) {
              // high set, but low is not (if low is falsy, except for the value 0 which is considered valid), set low to high and unset high.
              csnAttribute["@Consumption.filter.defaultValue"] = csnAttribute["@Consumption.filter.defaultValueHigh"];
              csnAttribute["@Consumption.filter.defaultValueHigh"] = undefined;
            }
            if (
              csnAttribute["@Consumption.filter.defaultValueHigh"] === undefined ||
              csnAttribute["@Consumption.filter.defaultValueHigh"] === ""
            ) {
              // Get rid of undefined/empty strings
              delete csnAttribute["@Consumption.filter.defaultValueHigh"];
            }
            break;
          case AnalyticModelVariableSelectionType.RANGE:
            const ranges: any[] = [];
            csnAttribute["@Consumption.filter.defaultRanges"] = ranges;
            if (storyFilterVariable.multipleSelections) {
              const defaultValues = (storyFilterVariable as IAnalyticModelVariableManualInput)
                .defaultValue as IAnalyticModelDefaultRange[];
              if (Array.isArray(defaultValues) && defaultValues.length > 0) {
                defaultValues.forEach((defaultValue) => {
                  const range: any = {
                    sign: ReverseAnalyticModelDefaultRangeSign.get(defaultValue.sign),
                    option: ReverseAnalyticModelDefaultRangeOption.get(defaultValue.option),
                    low: getValueBasedOnCdsType(csnAttribute.type, defaultValue.lowValue),
                    high: getValueBasedOnCdsType(csnAttribute.type, defaultValue.highValue),
                  };
                  ranges.push(range);
                });
              }
            } else {
              const defaultValue = (storyFilterVariable as IAnalyticModelVariableManualInput)
                .defaultValue as IAnalyticModelDefaultRange;
              const range: any = {
                sign: ReverseAnalyticModelDefaultRangeSign.get(defaultValue.sign),
                option: ReverseAnalyticModelDefaultRangeOption.get(defaultValue.option),
                low: getValueBasedOnCdsType(csnAttribute.type, defaultValue.lowValue),
                high: getValueBasedOnCdsType(csnAttribute.type, defaultValue.highValue),
              };
              ranges.push(range);
            }
            break;
          default:
            break;
        }
      }

      return csnAttribute;
    }
  }

  /**
   * @description Add where clause to the CSN
   * @protected
   * @param {ICsnDefinition} queryCsn
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected addWhereClauseToCsn(queryCsn: ICsnDefinition) {
    if (
      this.queryModel.filter?.formulaRaw &&
      this.queryModel.filter.formulaRaw.length > 0 &&
      (this.queryModel.filter.formula ?? "").length === 0
    ) {
      throw new Error(`Invalid Analytic Model - invalid formula '${this.queryModel.filter.formulaRaw}' for filter`);
    }

    if (this.queryModel.filter && this.queryModel.filter.formula && this.queryModel.filter.formula.length > 0) {
      const filters = _.cloneDeep(this.queryModel.filter);
      const where = this.convertFilterToCsnXpr(filters);
      (queryCsn.query!.SELECT!.where as any) = where;
    }
  }

  /**
   * @description Add cross calculations to the CSN
   * @protected
   * @param {ICsnDefinition} queryCsn
   * @memberof AnalyticModelCsnConverter
   */
  protected addCrossCalculationsToCsn(queryCsn: ICsnDefinition) {
    const createXprHandlers = {
      [AnalyticModelCrossCalculationType.CalculatedCrossCalculation]:
        this.createCalculatedCrossCalculationXpr.bind(this),
      [AnalyticModelCrossCalculationType.RestrictedCrossCalculation]:
        this.createRestrictedCrossCalculationXpr.bind(this),
      [AnalyticModelCrossCalculationType.FactSourceCrossCalculation]:
        this.createFactSourceCrossCalculationXpr.bind(this),
    };

    Object.keys(this.queryModel.crossCalculations || {}).forEach((crossCalculationKey) => {
      const crossCalculation = this.queryModel.crossCalculations![crossCalculationKey];

      if (
        "formulaRaw" in crossCalculation &&
        crossCalculation.formulaRaw &&
        crossCalculation.formulaRaw.length > 0 &&
        (crossCalculation.formula ?? "").length === 0
      ) {
        throw new Error(
          `Invalid Analytic Model - invalid formula '${crossCalculation.formulaRaw}' for cross calculation ${crossCalculationKey}`
        );
      }

      const csnCrossCalculation = this.createCrossCalculationCsn(crossCalculationKey);

      // Add cross calculation to elements
      if (!queryCsn.elements?.hasOwnProperty(crossCalculationKey)) {
        queryCsn.elements![crossCalculationKey] = csnCrossCalculation;
      }

      if (!queryCsn.query?.SELECT?.columns?.find((column) => column.as === crossCalculationKey)) {
        const createXprHandler = createXprHandlers[crossCalculation.crossCalculationType];
        if (!createXprHandler) {
          throw Error(`Unknown cross calculation type`);
        }
        queryCsn.query!.SELECT!.columns.push(createXprHandler(crossCalculationKey));
      }
    });
  }

  protected createCrossCalculationCsn(crossCalculationKey: string): any {
    const modelCrossCalculation = this.queryModel.crossCalculations![crossCalculationKey];

    const csnCrossCalculation: any = {
      [CDSElementKeys.LABEL]: modelCrossCalculation.text,
    };

    if (modelCrossCalculation.isAuxiliary) {
      csnCrossCalculation[HiddenAnnotations.CONSUMPTION_HIDDEN] = true;
    }

    if (this.ffFormatOfMeasures && modelCrossCalculation.formatting) {
      csnCrossCalculation[AnalyticsDetails.QUERY_SCALING] = modelCrossCalculation.formatting.scaleType;
      csnCrossCalculation[AnalyticsDetails.QUERY_DECIMALS] = modelCrossCalculation.formatting.decimalPlaces;
    }

    switch (modelCrossCalculation.crossCalculationType) {
      case AnalyticModelCrossCalculationType.CalculatedCrossCalculation:
        csnCrossCalculation["@AnalyticsDetails.measureType"] = {
          "#": "CALCULATION",
        };
        csnCrossCalculation["@Aggregation.default"] = {
          "#": "FORMULA",
        };
        if (modelCrossCalculation.hasOwnProperty("exceptionAggregationType")) {
          this.addExceptionAggregation(modelCrossCalculation, csnCrossCalculation);
        }
        break;
      case AnalyticModelCrossCalculationType.RestrictedCrossCalculation:
        csnCrossCalculation["@AnalyticsDetails.measureType"] = {
          "#": "RESTRICTION",
        };
        if (modelCrossCalculation.hasOwnProperty("constantSelectionType")) {
          this.addConstantSelection(modelCrossCalculation, csnCrossCalculation);
        }
        break;
      case AnalyticModelCrossCalculationType.FactSourceCrossCalculation:
        csnCrossCalculation["@AnalyticsDetails.measureType"] = {
          "#": "BASE",
        };
        break;
      default:
        throw Error(`Unknown cross calculation type`);
    }
    csnCrossCalculation["@AnalyticsDetails.query.onCharacteristicStructure"] = true;

    return csnCrossCalculation;
  }

  private addConstantSelection(
    modelCrossCalculation: IAnalyticModelRestrictedCrossCalculation,
    csnCrossCalculation: any
  ) {
    switch (modelCrossCalculation.constantSelectionType) {
      case AnalyticModelConstantSelectionType.All:
        csnCrossCalculation["@AnalyticsDetails.query.ignoreFurtherFilter.forAllElements"] = true;
        break;
      case AnalyticModelConstantSelectionType.Selected:
        if (
          modelCrossCalculation.constantSelectionAttributes &&
          Array.isArray(modelCrossCalculation.constantSelectionAttributes) &&
          modelCrossCalculation.constantSelectionAttributes.length > 0
        ) {
          csnCrossCalculation["@AnalyticsDetails.query.ignoreFurtherFilter.forElement"] =
            modelCrossCalculation.constantSelectionAttributes.map((attribute) => ({
              "=": attribute,
            }));
        }
        break;
      case AnalyticModelConstantSelectionType.None:
      default:
        break;
    }
  }

  protected createCalculatedCrossCalculationXpr(crossCalculationKey: string): any {
    const crossCalculation = this.queryModel.crossCalculations![
      crossCalculationKey
    ] as IAnalyticModelCalculatedCrossCalculation;
    const xpr = this.convertCalculatedCrossCalculationToCsnXpr(crossCalculation);
    xpr.as = crossCalculationKey;
    return xpr;
  }

  protected convertCalculatedCrossCalculationToCsnXpr(crossCalculation: IAnalyticModelCalculatedCrossCalculation): any {
    // Reuse the calculated measure conversion
    const xpr = this.convertCalculatedMeasureToCsnXpr(crossCalculation);
    return xpr;
  }

  protected createRestrictedCrossCalculationXpr(crossCalculationKey: string): any {
    const crossCalculation = this.queryModel.crossCalculations![
      crossCalculationKey
    ] as IAnalyticModelRestrictedCrossCalculation;

    // Empty selection member
    if (crossCalculation.formulaRaw === "" && crossCalculation.formula === "") {
      return this.getEmptySelectionRestrictedCrossCalculation(crossCalculationKey);
    }

    return {
      xpr: this.convertRestrictedCrossCalculationToCsnXpr(crossCalculation),
      as: crossCalculationKey,
    };
  }

  private getEmptySelectionRestrictedCrossCalculation(crossCalculationKey: string): any {
    return {
      xpr: [
        "case",
        "when",
        {
          val: true,
        },
        "then",
        {
          val: 1,
        },
        "end",
      ],
      as: crossCalculationKey,
    };
  }

  protected convertRestrictedCrossCalculationToCsnXpr(crossCalculation: IAnalyticModelRestrictedCrossCalculation): any {
    // Reuse the restricted measure conversion
    // Casting is to make typescript happy as Restricted Cross Calculation does not have "key" property
    const xpr = this.convertRestrictedMeasureToCsnXpr(crossCalculation as unknown as IAnalyticModelRestrictedMeasure);
    // Restricted Cross Calculations have the member reference fixed to 1
    xpr[xpr.length - 2] = { val: 1 };
    return xpr;
  }

  private createFactSourceCrossCalculationXpr(crossCalculationKey: string) {
    const crossCalculation = this.queryModel.crossCalculations![
      crossCalculationKey
    ] as IAnalyticModelFactSourceCrossCalculation;

    const ref = this.generateCrossCalculationRef(crossCalculation);
    return {
      ref,
      as: crossCalculationKey,
    };
  }

  private generateCrossCalculationRef(crossCalculation: IAnalyticModelFactSourceCrossCalculation): [string, string] {
    const factSource = this.queryModel.sourceModel.factSources[crossCalculation.sourceKey];
    return [AnalyticModelCsnConverter.generateTableAlias(factSource.dataEntity.key), crossCalculation.key];
  }

  private addCollisionHandlingToCsn(queryCsn: ICsnDefinition): void {
    if (!this.queryModel.collisionHandlingPriority) {
      return;
    }
    assert(queryCsn.elements, "AnalyticModelCsnConverter.addCollisionHandlingToCsn: elements must be defined");

    const priority = this.queryModel.collisionHandlingPriority;
    const measureKeys = new Set(Object.keys(this.queryModel.measures ?? {}));
    const crossCalculationKeys = new Set(Object.keys(this.queryModel.crossCalculations || {}));

    const keySets = {
      [AnalyticModelCollisionHandlingPriority.Measures]: {
        priorityKeys: measureKeys,
        concurrentKeys: crossCalculationKeys,
      },
      [AnalyticModelCollisionHandlingPriority.CrossCalculations]: {
        priorityKeys: crossCalculationKeys,
        concurrentKeys: measureKeys,
      },
    };
    const { priorityKeys, concurrentKeys } = keySets[priority];

    for (const elementKey of [...measureKeys, ...crossCalculationKeys]) {
      if (!queryCsn.elements.hasOwnProperty(elementKey)) {
        continue; // Skip if the element does not exist in the CSN for some reason
      }

      if (priorityKeys.has(elementKey)) {
        queryCsn.elements[elementKey] = {
          ...queryCsn.elements[elementKey],
          "@AnalyticsDetails.query.collisionHandling.formula": {
            "#": "THIS",
          },
        };
      }

      if (concurrentKeys.has(elementKey)) {
        queryCsn.elements[elementKey] = {
          ...queryCsn.elements[elementKey],
          "@AnalyticsDetails.query.collisionHandling.formula": {
            "#": "CONCURRENT",
          },
        };
      }
    }
  }

  /**
   * @description Build the basic structure of the CSN containing associations and mapping elements. Attributes, measures, ... will be added later
   * @protected
   * @param {ICsnDefinition} queryCsn
   * @param {string} factSourceKey Technical key of the fact source
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected buildCsnStructure(queryCsn: ICsnDefinition, factSourceKey: string): void {
    const addToElements = (elements: ICsnElements, toBeAdded: ICsnElements) => {
      Object.entries(toBeAdded).forEach(([key, value]) => {
        if (!(key in elements)) {
          elements[key] = value;
        }
      });
    };

    const addToColumns = (columns: ICsnRef[], toBeAdded: ICsnRef[]) => {
      toBeAdded.forEach((value) => {
        const found = columns.find((column) =>
          column.as && value.as ? column.as === value.as : _.isEqual(column.ref, value.ref)
        );
        if (!found) {
          columns.push(value);
        }
      });
    };

    const factSource = this.queryModel?.sourceModel?.factSources?.[factSourceKey];

    if (!factSource || !factSource.dataEntity) {
      queryCsn.query = {};
      return;
    }

    queryCsn.query = {
      SELECT: {
        from: {
          ref: [factSource.dataEntity.key],
          as: AnalyticModelCsnConverter.generateTableAlias(factSource.dataEntity.key),
        },
        columns: [],
        mixin: {},
      },
    };

    this.dependencyTree.forEach((dependencyElement) => {
      // Source Field: this is basically what the user will see as selected in the UI
      // Target Field: this is the key of the association target.
      //   => The element and column object are temporary and are used for the text resolution. Once the text resolution is done they'll get removed

      const queryColumnsSource: any[] = [];
      const queryColumnsTarget: any[] = [];
      const elementsSource: {
        [propertyName: string]: any;
      } = {};
      const elementsTarget: {
        [propertyName: string]: any;
      } = {};

      const queryOn: any[] = [];
      const elementOn: any[] = [];

      for (const i of Object.keys(dependencyElement.sourceFields)) {
        const index = Number(i);
        const dependencySourceField = dependencyElement.sourceFields[index];
        const dependencyTargetField = dependencyElement.targetFields[index];
        // Generate CSN names for the corresponding elements
        // If it's a key field or a mapping target of a dimension, this'll determine the actual field in the model (Dimension keys are never part of the model)
        const { attributeInModel, sourceFieldName, entityName } = this.determineBaseField(dependencyElement, index);
        const isAttribute = !!attributeInModel;

        const sourceGeneratedName = AnalyticModelCsnConverter.generateAttributeAlias(
          isAttribute ? attributeInModel : sourceFieldName,
          isAttribute ? undefined : entityName
        );

        // Query Section: Source Mapping Field
        const queryColumnSourceField = {
          ref: [
            dependencyElement.sourceType === AnalyticModelSourceType.Fact
              ? dependencyElement.sourceAlias
              : dependencyElement.sourceAssociationAlias!,
            dependencySourceField.sourceFieldName,
          ],
          as: sourceGeneratedName,
        };

        // Generate element only if the field is not yet part of the model
        if (!queryCsn.query!.SELECT!.columns.some((column) => _.isEqual(column.ref, queryColumnSourceField.ref))) {
          // Elements: Source Mapping Field
          const elementsSourceField: any = {
            [CDSElementKeys.LABEL]: dependencySourceField.sourceFieldText,
          };

          // Generate @Analytics.navigationAttributeRef for dimension attributes
          if (dependencyElement.sourceType === AnalyticModelSourceType.Dimension) {
            elementsSourceField[CDSElementKeys.NAVIGATION_ATTRIBUTE_REF] = this.determineAttributeNavRef({
              attributeType: AnalyticModelAttributeType.DimensionSourceAttribute,
              sourceKey: dependencyElement.sourceKey,
              key: dependencySourceField.sourceFieldName,
            } as IAnalyticModelDimensionSourceAttribute);

            elementsSourceField[HiddenAnnotations.ANALYTICS_HIDDEN] = true; // reintroduced with 356783/2023
          }

          queryColumnsSource.push(queryColumnSourceField);
          elementsSource[sourceGeneratedName] = elementsSourceField;
        }

        // Adjust foreignKey
        if (dependencySourceField.foreignKeyField) {
          const element = elementsSource[sourceGeneratedName] || queryCsn.elements![sourceGeneratedName];
          element[CDSElementKeys.FOREIGN_KEY_ASSOCIATION] = {
            "=": dependencyElement.targetAssociationAlias,
          };
        }

        if (queryOn.length > 0) {
          queryOn.push("and");
        }
        queryOn.push({ ref: ["$projection", sourceGeneratedName] });
        queryOn.push("=");
        queryOn.push({
          ref: [dependencyElement.targetAssociationAlias, dependencyTargetField.targetFieldName],
        });

        if (elementOn.length > 0) {
          elementOn.push("and");
        }
        elementOn.push({ ref: [sourceGeneratedName] });
        elementOn.push("=");
        elementOn.push({
          ref: [dependencyElement.targetAssociationAlias, dependencyTargetField.targetFieldName],
        });
      }

      // Query Section: Association Column (has no as)
      const queryColumnAssociation: ICsnRef = {
        ref: [dependencyElement.targetAssociationAlias],
      };

      // Query Section: Association Mixin
      const queryMixin = {
        type: "cds.Association",
        on: queryOn,
        target: dependencyElement.targetName,
      };

      // Elements: Association
      const elementsAssociation: any = {
        type: "cds.Association",
        on: elementOn,
        target: dependencyElement.targetName,
        [CDSElementKeys.LABEL]: dependencyElement.targetAssociationText,
      };

      // Add columns and elements for the source
      addToColumns(queryCsn.query!.SELECT!.columns, queryColumnsSource);
      addToElements(queryCsn.elements!, elementsSource);

      // Add association to query columns, mixin and elements section
      queryCsn.query!.SELECT!.columns.push(queryColumnAssociation);
      queryCsn.query!.SELECT!.mixin[dependencyElement.targetAssociationAlias] = queryMixin;
      queryCsn.elements![dependencyElement.targetAssociationAlias] = elementsAssociation;

      // Add columns and elements for the target
      addToColumns(queryCsn.query!.SELECT!.columns, queryColumnsTarget);
      addToElements(queryCsn.elements!, elementsTarget);
    });
  }

  /**
   * @description Build the dependency tree to be used for association generation - Generates unique association aliases
   * @protected
   * @returns {void}
   * @memberof AnalyticModelCsnConverter
   */
  protected buildDependencyTree(): void {
    const fnFindAttributeInModel = (
      sourceType: AnalyticModelSourceType,
      sourceKey: string,
      elementName: string
    ): string | undefined => {
      let attrInModel;
      if (sourceType === AnalyticModelSourceType.Fact) {
        attrInModel = Object.entries(this.queryModel.attributes ?? {}).find(
          ([attributeName, attribute]) =>
            attribute.attributeType === AnalyticModelAttributeType.FactSourceAttribute &&
            Object.entries(attribute.attributeMapping).some(
              ([keyOfSource, mapping]) => keyOfSource === sourceKey && mapping.key === elementName
            )
        );
      } else if (sourceType === AnalyticModelSourceType.Dimension) {
        attrInModel = Object.entries(this.queryModel.attributes ?? {}).find(
          ([attributeName, attribute]) =>
            attribute.attributeType === AnalyticModelAttributeType.DimensionSourceAttribute &&
            attribute.sourceKey === sourceKey &&
            attribute.key === elementName
        );
      }

      if (attrInModel) {
        return attrInModel[0]; // key in attribute list
      }
    };

    // Build Join Sequence, starting with the fact source
    const fnEvaluateSource = (sourceType: AnalyticModelSourceType, sourceKey: string, level: number = 1) => {
      let source: IAnalyticModelSource;
      if (sourceType === AnalyticModelSourceType.Fact) {
        source = this.queryModel.sourceModel.factSources[sourceKey];
      } else {
        source = this.queryModel.sourceModel.dimensionSources![sourceKey];
      }

      // iterate over all dimension sources
      Object.entries(this.queryModel.sourceModel.dimensionSources!).forEach(([dimensionSourceName, dimSource]) => {
        let associationName = "";
        // check if there's a associationContext to the provided source
        const assocCtxExists = dimSource.associationContexts.some((ctx: IAnalyticModelAssociationContext) => {
          if (ctx.sourceType === sourceType && ctx.sourceKey === sourceKey) {
            associationName = ctx.associationSteps.slice().pop() as string;
            return true;
          }
          return false;
        });

        if (assocCtxExists) {
          // there is an associationContext to the provided source => add the connection to the join sequence

          const sourceModel = this.sourceModelsByName[source.dataEntity.key];
          const sourceModelAssociation = sourceModel?.elements?.[associationName] as IAssociation;

          if (!sourceModelAssociation) {
            throw new CodedError(
              ErrorCodes.NOT_FOUND,
              `Element with name ${associationName} could not be found in elements of object ${source.dataEntity.key}`,
              StatusCodes.NOT_FOUND
            );
          }

          const foreignKeyElementInSource = Object.entries(sourceModel.elements ?? {}).find(
            ([elementName, element]) =>
              CDSElementKeys.FOREIGN_KEY_ASSOCIATION in element &&
              element[CDSElementKeys.FOREIGN_KEY_ASSOCIATION]["="] === associationName
          )?.[0];

          const targetAssociationText =
            this.queryModel.sourceModel.dimensionSources![dimensionSourceName]?.text ?? dimSource.text;
          const sourceFields: ISourceField[] = [];
          const targetFields: ITargetField[] = [];

          const singleMapping = (sourceModelAssociation?.on as any[]).length === 3;
          const targetRepresentativeKey = this.determineRepresentativeKey(
            this.sourceModelsByName[sourceModelAssociation.target]
          );

          const indexMappingElement = singleMapping
            ? 0
            : (sourceModelAssociation?.on as any[]).findIndex(
                (onPart) =>
                  typeof onPart === "object" &&
                  "ref" in onPart &&
                  _.isEqual(onPart.ref, [associationName, targetRepresentativeKey])
              ) - 2;

          let foreignKeyAssociation;
          const elementSourceForAssociation = (sourceModelAssociation?.on as any[])[indexMappingElement].ref[0];

          if (elementSourceForAssociation) {
            foreignKeyAssociation = (sourceModel?.elements?.[elementSourceForAssociation] as ICsnElement)?.[
              CDSElementKeys.FOREIGN_KEY_ASSOCIATION
            ]?.["="];
          }

          for (const index in sourceModelAssociation?.on as any) {
            const numberIndex = Number(index);
            if (numberIndex === 0 || numberIndex % 4 === 0) {
              const sourceFieldName = (sourceModelAssociation?.on as any)[numberIndex].ref.slice().pop() as string;
              const attributeInModel = fnFindAttributeInModel(sourceType, sourceKey, sourceFieldName);
              const sourceFieldText = !!attributeInModel
                ? this.queryModel.attributes![attributeInModel].text
                : (sourceModel.elements?.[sourceFieldName] as ICsnElement)?.[CDSElementKeys.LABEL] ?? sourceFieldName;
              sourceFields.push({
                sourceFieldName,
                sourceFieldText,
                attributeInModel,
                foreignKeyField: sourceFieldName === foreignKeyElementInSource,
              });
            } else if ((numberIndex - 2) % 4 === 0) {
              const targetFieldName = (sourceModelAssociation?.on as any)[numberIndex].ref.slice().pop() as string;
              const targetFieldText =
                (sourceModel.elements?.[targetFieldName] as ICsnElement)?.[CDSElementKeys.LABEL] ?? targetFieldName;
              const representativeKey =
                (singleMapping || targetRepresentativeKey === targetFieldName) &&
                foreignKeyAssociation === associationName;
              targetFields.push({
                targetFieldName,
                targetFieldText,
                representativeKey,
              });
            }
          }

          let sourceAssociationName;
          let sourceAssociationAlias;
          if (sourceType === AnalyticModelSourceType.Dimension) {
            const sourceDependencyElement = this.getDependencyElementForTargetTypeAndKey(
              sourceType,
              sourceKey,
              sourceFields[0].sourceFieldName
            );
            sourceAssociationName = sourceDependencyElement.targetAssociationName; // Association from "previous" entity to current source
            sourceAssociationAlias = sourceDependencyElement.targetAssociationAlias; // Association from "previous" entity to current source
          }

          this.dependencyTree.push({
            sourceType,
            sourceKey,
            sourceName: source.dataEntity.key,
            sourceAlias: AnalyticModelCsnConverter.generateTableAlias(source.dataEntity.key),
            targetAssociationText,
            sourceFields,
            sourceAssociationName,
            sourceAssociationAlias,
            targetKey: dimensionSourceName,
            targetName: dimSource.dataEntity.key,
            targetAlias: AnalyticModelCsnConverter.generateTableAlias(dimSource.dataEntity.key),
            targetAssociationName: associationName,
            targetAssociationAlias: this.generateAssociationAlias(
              associationName,
              dimensionSourceName,
              this.dependencyTree
            ), // Alias the association => make sure no doubled names exist
            targetFields,
            level,
          });

          fnEvaluateSource(AnalyticModelSourceType.Dimension, dimensionSourceName, level + 1);
        }
      });
    };

    fnEvaluateSource(AnalyticModelSourceType.Fact, Object.keys(this.queryModel.sourceModel.factSources)[0], 1);

    this.dependencyTree.sort((a, b) => a.level - b.level);

    this.measurePerformance("Build Dependency Tree");
  }

  /**
   * @description Build the design time CSN
   * @protected
   * @returns {ICsnDefinition}
   * @memberof AnalyticModelCsnConverter
   */
  protected buildDesignTimeCsn(): ICsnDefinition {
    const queryCsn = this.getInitialQueryDefinition();
    this.buildCsnStructure(queryCsn, Object.keys(this.queryModel?.sourceModel?.factSources)?.[0] || "");

    this.addAttributesToCsn(queryCsn);
    this.addMeasuresToCsn(queryCsn);
    this.addWhereClauseToCsn(queryCsn);
    this.addParametersToCsn(queryCsn);
    this.addParameterMappingsToCsn(queryCsn);
    this.addDataAccessControlsToCsn(queryCsn);

    if (this.ffCrossCalculation) {
      this.addCrossCalculationsToCsn(queryCsn);
      this.addCollisionHandlingToCsn(queryCsn);
    }
    if (this.ffProtectDataExport) {
      this.addProtectExportDataFlagToCsn(queryCsn);
    }

    this.measurePerformance("Build Design Time CSN");

    return queryCsn;
  }

  /**
   * @description Convert a restricted measure to a CSN expression
   * @protected
   * @param {string} measureKey
   * @returns {*}  {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected createRestrictedMeasureXpr(measureKey: string): any {
    const measure = this.queryModel.measures![measureKey] as IAnalyticModelRestrictedMeasure;

    const xpr = this.convertRestrictedMeasureToCsnXpr(measure);

    if (Array.isArray(xpr)) {
      return {
        xpr,
        as: measureKey,
      };
    }

    xpr.as = measureKey;
    return xpr;
  }

  /**
   * @description Create a CSN element with a basic structure for a measure (details might be added later)
   * @protected
   * @param {string} measureKey
   * @returns {*}  {ICsnElement}
   * @memberof AnalyticModelCsnConverter
   */
  protected createCsnMeasure(measureKey: string): ICsnElement {
    const modelMeasure = this.queryModel.measures![measureKey];

    const csnMeasure: any = {
      [CDSElementKeys.LABEL]: modelMeasure.text,
    };

    if (modelMeasure.isAuxiliary) {
      csnMeasure[HiddenAnnotations.CONSUMPTION_HIDDEN] = true;
    }

    if (this.ffFormatOfMeasures && modelMeasure.formatting) {
      csnMeasure[AnalyticsDetails.QUERY_SCALING] = modelMeasure.formatting.scaleType;
      csnMeasure[AnalyticsDetails.QUERY_DECIMALS] = modelMeasure.formatting.decimalPlaces;
    }

    if (modelMeasure.measureType === AnalyticModelMeasureType.CalculatedMeasure) {
      csnMeasure["@AnalyticsDetails.measureType"] = {
        "#": "CALCULATION",
      };
      csnMeasure["@Aggregation.default"] = {
        "#": "FORMULA",
      };
    } else if (modelMeasure.measureType === AnalyticModelMeasureType.RestrictedMeasure) {
      csnMeasure["@AnalyticsDetails.measureType"] = {
        "#": "RESTRICTION",
      };
      if (modelMeasure.aggregation) {
        csnMeasure["@Aggregation.default"] = {
          "#": ReverseAggregationTypeMapping.get(modelMeasure.aggregation),
        };
      }
    } else if (modelMeasure.measureType === AnalyticModelMeasureType.CurrencyConversionMeasure) {
      csnMeasure["@AnalyticsDetails.measureType"] = {
        "#": "CALCULATION",
      };
      csnMeasure["@Aggregation.default"] = {
        "#": "FORMULA",
      };
    } else if (modelMeasure.measureType === AnalyticModelMeasureType.UnitConversionMeasure) {
      csnMeasure["@AnalyticsDetails.measureType"] = {
        "#": "CALCULATION",
      };
      csnMeasure["@Aggregation.default"] = {
        "#": "FORMULA",
      };
    } else if (modelMeasure.measureType === AnalyticModelMeasureType.CountDistinct) {
      csnMeasure["@AnalyticsDetails.measureType"] = {
        "#": "BASE",
      };
      csnMeasure["@Aggregation.default"] = {
        "#": "COUNT_DISTINCT",
      };
    } else if (modelMeasure.measureType === AnalyticModelMeasureType.NonCumulativeMeasure) {
      csnMeasure["@AnalyticsDetails.measureType"] = {
        "#": "CALCULATION",
      };

      // NCUMs don't get an exception aggregation on element level, return right away
      return csnMeasure;
    } else {
      csnMeasure["@AnalyticsDetails.measureType"] = {
        "#": "BASE",
      };
      if (modelMeasure.aggregation) {
        csnMeasure["@Aggregation.default"] = {
          "#": ReverseAggregationTypeMapping.get(modelMeasure.aggregation),
        };
      }
    }

    if (modelMeasure.hasOwnProperty("exceptionAggregationType")) {
      this.addExceptionAggregation(modelMeasure, csnMeasure);
    }

    return csnMeasure;
  }

  /**
   * @description Create expression for a calculated measure
   * @protected
   * @param {string} measureKey
   * @returns {*}  {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected createCalculatedMeasureXpr(measureKey: string): any {
    const measure = this.queryModel.measures![measureKey] as IAnalyticModelCalculatedMeasure;
    const xpr = this.convertCalculatedMeasureToCsnXpr(measure);
    xpr.as = measureKey;
    return xpr;
  }

  /**
   * @description Generate ref for attributes (ref in columns)
   * @protected
   * @param {string} queryAttributeKey
   * @param {IAnalyticModelAttribute} attribute
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected determineAttributeRef(queryAttributeKey: string, attribute: IAnalyticModelAttribute): any[] {
    if (attribute.attributeType === AnalyticModelAttributeType.FactSourceAttribute) {
      const factSourceAttribute = attribute as IAnalyticModelFactSourceAttribute;
      const fsName = Object.keys(factSourceAttribute.attributeMapping)[0];
      const factSourceName = this.queryModel.sourceModel.factSources[fsName].dataEntity.key;
      const refKey = factSourceAttribute.attributeMapping[fsName].key ?? queryAttributeKey;

      return [AnalyticModelCsnConverter.generateTableAlias(factSourceName), refKey];
    } else if (attribute.attributeType === AnalyticModelAttributeType.DimensionSourceAttribute) {
      const dimensionSourceAttribute = attribute as IAnalyticModelDimensionSourceAttribute;
      const dependencyElement = this.getDependencyElementForTargetTypeAndKey(
        AnalyticModelSourceType.Dimension,
        dimensionSourceAttribute.sourceKey,
        dimensionSourceAttribute.key
      );

      return [dependencyElement?.targetAssociationAlias, dimensionSourceAttribute.key];
    }

    return [];
  }

  /**
   * @description Generate contents of @Analytics.navigationAttributeRef for dimension attributes
   * @protected
   * @param {IAnalyticModelAttribute} attribute
   * @returns {*}  {any[]}
   * @memberof AnalyticModelCsnConverter
   */
  protected determineAttributeNavRef(attribute: IAnalyticModelAttribute): any[] {
    if (attribute.attributeType === AnalyticModelAttributeType.DimensionSourceAttribute) {
      const dimensionSourceAttribute = attribute as IAnalyticModelDimensionSourceAttribute;
      const dependencyElement = this.getDependencyElementForTargetTypeAndKey(
        AnalyticModelSourceType.Dimension,
        dimensionSourceAttribute.sourceKey,
        dimensionSourceAttribute.key
      );

      return [dependencyElement?.targetAssociationAlias, dimensionSourceAttribute.key];
    }

    return [];
  }

  /**
   * @description Determine basic information for a field (name in model, source field name, entity name if dimension)
   * @protected
   * @param {IDependencyElement} dependencyElement
   * @param {number} currentIndex
   * @returns {*}  {({ attributeInModel?: string; sourceFieldName: string; entityName: string | undefined })}
   * @memberof AnalyticModelCsnConverter
   */
  protected determineBaseField(
    dependencyElement: IDependencyElement,
    currentIndex: number
  ): { attributeInModel?: string; sourceFieldName: string; entityName: string | undefined } {
    const sourceField = dependencyElement.sourceFields?.[currentIndex];
    const sourceFieldName = sourceField?.sourceFieldName ?? "";
    const entityName =
      dependencyElement.sourceType === AnalyticModelSourceType.Dimension
        ? dependencyElement.sourceAssociationAlias
        : undefined;

    if (dependencyElement.sourceType === AnalyticModelSourceType.Dimension && sourceFieldName) {
      const isKey =
        (this.sourceModelsByName[dependencyElement.sourceName]?.elements?.[sourceFieldName] as ICsnElement)?.key ??
        false;

      const priorDependencyElement = this.getDependencyElementForTargetTypeAndKey(
        AnalyticModelSourceType.Dimension,
        dependencyElement.sourceKey,
        sourceFieldName
      );

      const isAssocTargetField = priorDependencyElement.targetFields?.some(
        (field) => field.targetFieldName === sourceFieldName
      );

      if (isKey || isAssocTargetField) {
        // We're looking at a dimension and the current field is a key or mapping target of this dimension
        // => shadowed, determine base element
        const fieldIndex =
          priorDependencyElement.targetFields?.findIndex((field) => field.targetFieldName === sourceFieldName) ?? -1;
        if (fieldIndex >= 0) {
          return this.determineBaseField(priorDependencyElement, fieldIndex);
        }
      }
    }

    return {
      attributeInModel: sourceField?.attributeInModel,
      sourceFieldName,
      entityName,
    };
  }

  protected determineRepresentativeKey(source?: ICsnDefinition): string | undefined {
    if (!source) {
      return;
    }
    if (!(CDSElementKeys.REPRESENTATIVE_KEY in source)) {
      return;
    }

    let repKey: string | undefined;

    if (typeof source[CDSElementKeys.REPRESENTATIVE_KEY] === "object") {
      repKey = (source[CDSElementKeys.REPRESENTATIVE_KEY] as any)["="];
    } else if (typeof source[CDSElementKeys.REPRESENTATIVE_KEY] === "string") {
      repKey = source[CDSElementKeys.REPRESENTATIVE_KEY];
    }

    return repKey;
  }

  /**
   * @description Generate a unique alias for a given association
   * @protected
   * @param {string} associationName
   * @param {(string | number)} dimensionSourceIndex
   * @param {IDependencyElement[]} [dependencyTree]
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected generateAssociationAlias(
    associationName: string,
    dimensionSourceIndex: string | number,
    dependencyTree?: IDependencyElement[]
  ): string {
    const associationAliasWithoutCounter = `${associationName}${GeneratedNames.GENERATED_NAME_SEPARATOR}${dimensionSourceIndex}`; // reused to generate assocAlias2
    let counter = 1;
    let associationAlias = associationAliasWithoutCounter;

    // Basically, if associationAliasWithoutCounter already exists, we'll append a counter until a new unique name has been found.
    while (
      dependencyTree?.some((dependencyElement) => dependencyElement.targetAssociationName === associationAlias) // does another association with this name already exist?
    ) {
      associationAlias = `${associationAliasWithoutCounter}${GeneratedNames.GENERATED_NAME_SEPARATOR}${counter++}`;
    }

    return associationAlias;
  }

  /**
   * @description Generate ref for measures (ref in columns)
   * @protected
   * @param {IAnalyticModelSourceMeasure} measure
   * @returns {*}  {any[]}
   * @memberof AnalyticModelCsnConverter
   */
  protected generateMeasureRef(measure: IAnalyticModelSourceMeasure): any[] {
    if (measure.measureType === AnalyticModelMeasureType.FactSourceMeasure) {
      const factSource = this.queryModel.sourceModel.factSources[measure.sourceKey];
      return [AnalyticModelCsnConverter.generateTableAlias(factSource.dataEntity.key), measure.key];
    }

    return [];
  }

  /**
   * @description Determine Dependency Element for a given target type and key
   * @protected
   * @param {AnalyticModelSourceType} type
   * @param {string} key
   * @param {string} [attributeName]
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected getDependencyElementForTargetTypeAndKey(
    type: AnalyticModelSourceType,
    key: string,
    attributeName?: string
  ): IDependencyElement {
    const dependencyElement = this.dependencyTree.find(
      (dependencyElement) => AnalyticModelSourceType.Dimension === type && dependencyElement.targetKey === key
    );
    if (!dependencyElement) {
      if (attributeName) {
        throw new Error(`Association could not be found for attribute ${attributeName}`);
      } else {
        throw new Error(`Association could not be found for key ${key}`);
      }
    }
    return dependencyElement;
  }

  /**
   * @description Build the initial conversion result
   * @protected
   * @returns {*}  {AnalyticModelCsnConversionResult}
   * @memberof AnalyticModelCsnConverter
   */
  protected getInitialConversionResult(): AnalyticModelCsnConversionResult {
    return {
      hasErrors: false,
      csn: {
        definitions: {
          // DW101-18079: Save is not possible if definition is empty. In case of errors definition has to be available, the elements and query sections will at least be empty (unless the validation failed)
          [this.queryModel.identifier.key]: this.getInitialQueryDefinition(),
        },
        meta: {
          creator: "Data Builder - Analytic Model Editor",
        },
        version: {
          csn: "1.0",
        },
      },
    };
  }

  /**
   * @description Build initial query definition + folder & package info
   * @protected
   * @returns {*}  {ICsnDefinition}
   * @memberof AnalyticModelCsnConverter
   */
  protected getInitialQueryDefinition(): ICsnDefinition {
    const initialQueryDefinition = {
      kind: "entity",
      [CDSElementKeys.LABEL]: this.queryModel.text,
      elements: {},
      "@ObjectModel.modelingPattern": {
        "#": "ANALYTICAL_CUBE",
      },
      "@ObjectModel.supportedCapabilities": [
        {
          "#": "ANALYTICAL_PROVIDER",
        },
      ],
      "@DataWarehouse.editorType": {
        "#": "DWCQueryModelEditor",
      },
      "@DataWarehouse.hanaCatalog.viewType": {
        "#": "CALCULATION_VIEW",
      },
      // @DataWarehouse.consumption.external has been removed with DW101-89414 based on a customer ticket
      query: {},
    };
    this.addFolderMetaInfoToCsn(initialQueryDefinition);
    this.addPackageMetaInfoToCsn(initialQueryDefinition);
    this.addDbHintsToCsn(initialQueryDefinition);
    return initialQueryDefinition;
  }

  /**
   * @description Determine Attribute from its corresponding source entity
   * @protected
   * @param {IAnalyticModelAttribute} attribute
   * @param {string} queryAttributeKey
   * @returns {*}  {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected determineAttributeInSource(attribute: IAnalyticModelAttribute, queryAttributeKey: string): any {
    let sourceName = "";
    let fieldName = "";
    if (attribute.attributeType === AnalyticModelAttributeType.FactSourceAttribute) {
      const factSourceAttribute = attribute as IAnalyticModelFactSourceAttribute;
      const fsName = Object.keys(factSourceAttribute.attributeMapping)[0];
      sourceName = this.queryModel.sourceModel.factSources[fsName].dataEntity.key;
      fieldName = factSourceAttribute.attributeMapping[fsName].key ?? queryAttributeKey;
    } else if (attribute.attributeType === AnalyticModelAttributeType.DimensionSourceAttribute) {
      const dimensionSourceAttribute = attribute as IAnalyticModelDimensionSourceAttribute;
      const dependencyElement = this.getDependencyElementForTargetTypeAndKey(
        AnalyticModelSourceType.Dimension,
        dimensionSourceAttribute.sourceKey,
        dimensionSourceAttribute.key
      );

      sourceName = dependencyElement.targetName!;
      fieldName = dimensionSourceAttribute.key;
    }

    return this.sourceModelsByName[sourceName]?.elements?.[fieldName];
  }

  /**
   * @description Check whether a given attribute exists in its corresponding source entity
   * @protected
   * @param {IAnalyticModelAttribute} attribute
   * @param {string} queryAttributeKey
   * @returns {*}  {boolean}
   * @memberof AnalyticModelCsnConverter
   */
  protected isAttributeAvailableInSource(attribute: IAnalyticModelAttribute, queryAttributeKey: string): boolean {
    const attributeInSource = this.determineAttributeInSource(attribute, queryAttributeKey);

    if (attributeInSource) {
      return true;
    }

    return false;
  }

  /**
   * @description Check whether a given attribute is hidden in its corresponding source entity
   * @protected
   * @param {IAnalyticModelAttribute} attribute
   * @param {string} queryAttributeKey
   * @returns {*}  {boolean}
   * @memberof AnalyticModelCsnConverter
   */
  protected isAttributeHiddenInSource(attribute: IAnalyticModelAttribute, queryAttributeKey: string): boolean {
    const attributeInSource = this.determineAttributeInSource(attribute, queryAttributeKey);

    if (attributeInSource?.[HiddenAnnotations.ANALYTICS_HIDDEN]) {
      return true;
    }

    return false;
  }

  /**
   * @description Check whether a given attribute is declared a text in its corresponding source entity
   * @protected
   * @param {IAnalyticModelAttribute} attribute
   * @param {string} queryAttributeKey
   * @returns {*}  {boolean}
   * @memberof AnalyticModelCsnConverter
   */
  protected isAttributeTextElementInSource(attribute: IAnalyticModelAttribute, queryAttributeKey: string): boolean {
    const attributeInSource = this.determineAttributeInSource(attribute, queryAttributeKey);

    if (attributeInSource?.["@Semantics.text"]) {
      return true;
    }

    return false;
  }

  /**
   * @description Determine if a given attribute is a representative key
   * @protected
   * @param {string} modelAttributeKey
   * @return {*}
   * @memberof AnalyticModelCsnConverter
   */
  protected isMapElementRepKey(modelAttributeKey: string): boolean {
    const isRepKey = this.dependencyTree.some((dependencyElement) => {
      const attributeIndex = dependencyElement.sourceFields.findIndex(
        (sourceField) => sourceField.attributeInModel === modelAttributeKey
      );
      const targetAttribute = dependencyElement.targetFields[attributeIndex];
      return targetAttribute?.representativeKey ?? false;
    });

    return isRepKey;
  }

  /**
   * @description Check whether a given measure is hidden in its corresponding source entity
   * @protected
   * @param {IAnalyticModelSourceMeasure} measure
   * @returns {*}  {boolean}
   * @memberof AnalyticModelCsnConverter
   */
  protected isMeasureHiddenInSource(measure: IAnalyticModelSourceMeasure): boolean {
    if (measure.measureType === AnalyticModelMeasureType.FactSourceMeasure) {
      const factSource = this.queryModel.sourceModel.factSources[measure.sourceKey];
      const factSourceModel = this.sourceModelsByName[factSource.dataEntity.key];
      if ((factSourceModel?.elements?.[measure.key] as any)?.[HiddenAnnotations.ANALYTICS_HIDDEN]) {
        return true;
      }
    }

    return false;
  }

  protected isStackingAM(): boolean {
    const fsName = Object.values(this.queryModel.sourceModel.factSources ?? {})[0]?.dataEntity?.key;
    return Commons.checkIsAnalyticModel(this.sourceModelsByName[fsName]);
  }

  /**
   * @description Load all relevant models for the Analytic Model from the repository (One repo call for all models)
   * @protected
   * @param {ISimpleTypeMapping} simpleTypeMapping
   * @memberof AnalyticModelCsnConverter
   */
  protected async loadModels(simpleTypeMapping: ISimpleTypeMapping) {
    const modelNames: string[] = [];

    // [SDP] - Add privilege check here when the middleware is ready as the spaceId might be different on each iteration of the loop

    // Collect all source model names from the AM definition
    Object.values(this.queryModel.sourceModel.factSources).forEach((factSourceModel) => {
      if (!modelNames.includes(factSourceModel.dataEntity.key)) {
        modelNames.push(factSourceModel.dataEntity.key);
      }
    });

    Object.values(this.queryModel.sourceModel.dimensionSources ?? {}).forEach((dimensionSourceModel) => {
      if (!modelNames.includes(dimensionSourceModel.dataEntity.key)) {
        modelNames.push(dimensionSourceModel.dataEntity.key);
      }
    });

    // Make sure all lookup entities of variables are loaded
    Object.values(this.queryModel.variables ?? {}).forEach((variable) => {
      if (
        "variableProcessingType" in variable &&
        (variable.variableProcessingType === AnalyticModelVariableProcessingType.LOOKUP ||
          variable.variableProcessingType === AnalyticModelVariableProcessingType.DYNAMIC_DEFAULT)
      ) {
        if (!modelNames.includes(variable.lookupEntity)) {
          modelNames.push(variable.lookupEntity);
        }
      }
    });

    // make sure all DACs are loaded
    if (this.ffDacSupport) {
      Object.keys(this.queryModel.dataAccessControls ?? {}).forEach((dacKey) => {
        modelNames.push(dacKey);
      });
    }

    // Don't load already cached model
    const cachedModelNames = Object.keys(this.sourceModelsByName);

    // Called once for the entire model. A second call is required during predeployment in TextAssociationsPredeployTask.ts
    const repoObjectDefinitionsResult = await AnalyticModelHelper.loadRepoObjectDefinitions(
      this.context,
      this.spaceKey,
      modelNames.filter((modelName) => !cachedModelNames.includes(modelName)),
      simpleTypeMapping
    );

    Object.entries(repoObjectDefinitionsResult.definitions).forEach(
      ([name, object]) => (this.sourceModelsByName[name] = object)
    );
    this.idMapping = repoObjectDefinitionsResult.idMapping ?? {};

    this.measurePerformance(`loadModels (${modelNames.length})`, true);
  }

  /**
   * @description Measure the performance of a given conversion step (duration relative to the previous call)
   * @protected
   * @param {string} description
   * @param {boolean} [repo]
   * @memberof AnalyticModelCsnConverter
   */
  protected measurePerformance(description: string, repo?: boolean) {
    this.performanceTracker.measure(`convertCsn - ${description}`, repo);
  }

  // MARK: PRIVATE METHODS
  /**
   * @description Attach the model as backpack to the CSN (property businessLayerDefinitions)
   * @private
   * @param {ICsn} csn
   * @param {IAnalyticModel} queryModel
   * @returns {*}
   * @memberof AnalyticModelCsnConverter
   */
  private attachModelAsBackpack(csn: ICsn, queryModel: IAnalyticModel) {
    if (queryModel.identifier && queryModel.identifier.key) {
      csn.businessLayerDefinitions = {
        [queryModel.identifier.key]: _.cloneDeep(this.queryModel),
      };
    }
  }
}
