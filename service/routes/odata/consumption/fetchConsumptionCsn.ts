/**
 * Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { ICsnDefinition, ICsnDocument, ICsnElements } from "@sap-nekton/core-types";
import {
  DependencyKind,
  IGetDependenciesParams,
  IGetObjectParameters,
  IObjectDependency,
  IRequestContext,
  RepositoryObjectKind,
  RepositoryObjectType,
} from "@sap/deepsea-types";
import { RepositoryObject } from "@sap/deepsea-utils";
import { AsyncCache, NotUndefined } from "@sap/dwc-cache";
import { getLogger } from "@sap/dwc-logger";
import { Request, Response } from "express";
import { basename } from "path";
import { FeatureFlagProvider } from "../../../featureflags/FeatureFlagProvider";
import { RepositoryObjectClient } from "../../../repository/client/repositoryObjectClient";
import { RequestContext } from "../../../repository/security/requestContext";
import { asyncCacheLoggerFactory, createAsyncCache } from "../../../reuseComponents/caching/AsyncCacheFactory";
import { ListenToDeployEvent } from "../../../reuseComponents/deployment/src";
import { RedisCachePrefixes, getOptionalRedisStore } from "../../../reuseComponents/utility/redis-util";
import { IAdaptationContext } from "../../dataAccess/interfaces/IDataAccess";
import { getSpaceUuidFromName } from "../../dataAccess/lib/getSpaceUuidFromName";
import { resolveDbClient, resolveFullCsn, resolveFullCsnWithAdvisor } from "../../dataAccess/shared";
import { CsnRepositoryTag } from "../../dataAccess/types";

const {
  logPerformance,
  logVerbose,
  logInfo,
  logInfoWithOptionalContext,
  logError,
  logErrorWithOptionalContext,
  logWarning,
} = getLogger(basename(__filename));
export class FetchConsumptionCSNHandler<T extends NotUndefined> {
  private CSN_REDIS_CACHE_TIMEOUT = 6; // 6 hours timeout should be good to start with
  private CSN_REDIS_CACHE_PREFIX = RedisCachePrefixes.ODATA_CSN_CACHE;

  public MAX_ROW_NUMBER = 100000;
  public MIN_ROW_NUMBER = 1000;
  public MAX_CELL_NUMBER = 10000000; // 10 million cells according to https://me.sap.com/notes/2770570

  private intermediateCSNRedisCache = createAsyncCache<string, T>("ConsumptionApiCsnCache", [
    getOptionalRedisStore({
      timeout: this.CSN_REDIS_CACHE_TIMEOUT * 60 * 60 * 1000, // 6 hours to return the last good value before next fetch attempt
      failureTimeout: 1 * 60 * 1000, // 1 minutes to still return last good value (if available) after a fetch failure
      fallbackTimeout: 12 * 60 * 60 * 1000, // 12 hours to keep cache entry before it is physically deleted
      prefix: this.CSN_REDIS_CACHE_PREFIX,
      loggerFactory: asyncCacheLoggerFactory,
    }),
  ]);

  protected generateKey(tenantId: string, spaceId: string, entityName: string): string {
    return `${tenantId}::${spaceId}::${entityName}`;
  }

  public async csnForConsumption(req: Request, res: Response, params?: any) {
    const entityName = req.params.entity;
    const schemaName = req.params.space;
    const context = req.context;

    logVerbose(`entering csnForConsumption with schemaName: ${schemaName} and entityName: ${entityName}`, { context });

    let timestamp = new Date();
    const cacheKey = this.generateKey(context.tenantId!, schemaName, entityName);
    logVerbose("using Redis cache", { context });
    const effectiveCache: AsyncCache<string, T> = this.intermediateCSNRedisCache;
    const fullCsn = await effectiveCache.get(cacheKey, async (forget, _updateTimeout) => {
      try {
        logInfo(`creating new cache entry for key ${cacheKey}`, { context });
        return await this.getCsnWithDynamicPageSize(req, res, params);
      } catch (err) {
        forget();
        throw err;
      }
    });
    timestamp = logPerformance(timestamp, "csnForConsumption", { context });
    logVerbose("leaving csnForConsumption", { context });
    return fullCsn;
  }

  protected async getCsnWithDynamicPageSize(req: Request, res: Response, params?: any): Promise<T> {
    const context = req.context;
    const entityName = req.params.entity;
    const csnDocument = await getFullCsn(req, res);
    const entityDefinition = csnDocument.definitions[entityName];

    logVerbose(`entering getCsnWithDynamicPageSize with entityName: ${entityName}`, { context });

    if (entityDefinition["@cds.query.limit.max"] !== undefined) {
      logVerbose(`Annotation @cds.query.limit.max already exists on entity ${entityName}`, { context });
      return csnDocument as T;
    }

    logVerbose(`Annotation @cds.query.limit.max not found on entity ${entityName} - calculating`, { context });

    let timestamp = new Date();

    const maxRowNumber = await this.calculateMaxRowNumber(context, entityDefinition, res.locals.isAnalyticModel);
    const defaultRowNumber = Math.floor(maxRowNumber / 2);

    logVerbose(`Annotation @cds.query.limit.max calculated for entity ${entityName} with ${maxRowNumber}`, { context });
    logVerbose(`Annotation @cds.query.limit.default calculated for entity ${entityName} with ${defaultRowNumber}`, {
      context,
    });

    entityDefinition["@cds.query.limit.max"] = maxRowNumber;
    entityDefinition["@cds.query.limit.default"] = defaultRowNumber;

    timestamp = logPerformance(timestamp, "getCsnWithDynamicPageSize", { context });
    logVerbose("leaving getCsnWithDynamicPageSize", { context });

    return csnDocument as T;
  }

  public async calculateMaxRowNumber(
    context: RequestContext,
    entityDefinition: ICsnDefinition,
    isAnalytical: boolean
  ): Promise<number> {
    const assumedFileSize = 100 * 1000 * 1000; // 100 MB

    // initialize counter for maxRowNumber
    let maxRowNumber = this.MIN_ROW_NUMBER;
    let rowSize = this.getRowSize(entityDefinition, context);
    logVerbose(`calculated rowSize before UTF-16 conversion ${rowSize}`, { context });

    if (rowSize > 0) {
      // account for UTF-16
      rowSize = rowSize * 2;
      logVerbose(`calculated rowSize after UTF-16 conversion ${rowSize}`, { context });
      // ensure at least page size is equal to minRowNumber (1K)
      maxRowNumber = Math.max(Math.floor(assumedFileSize / rowSize), this.MIN_ROW_NUMBER);
      logVerbose(`calculated row number based on min ${this.MIN_ROW_NUMBER} row limit is ${maxRowNumber}`, { context });

      if (isAnalytical) {
        maxRowNumber = this.adjustMaxRowNumberBasedOnCells(context, entityDefinition.elements!, maxRowNumber);
      }
    }
    // ensure page size is at most the maximum allowed in rows (100K)
    maxRowNumber = Math.min(maxRowNumber, this.MAX_ROW_NUMBER);
    logVerbose(`calculated row number based on max ${this.MAX_ROW_NUMBER} row limit is ${maxRowNumber}`, { context });

    return maxRowNumber;
  }

  public adjustMaxRowNumberBasedOnCells(
    context: RequestContext,
    csnElements: ICsnElements,
    maxRowNumber: number
  ): number {
    const numberOfElements = this.getElementsCount(csnElements);
    const maxRowNumberBasedOnCells = Math.floor(this.MAX_CELL_NUMBER / numberOfElements);
    maxRowNumber = Math.min(maxRowNumber, maxRowNumberBasedOnCells);
    logVerbose(`calculated row number based on max ${this.MAX_CELL_NUMBER} cells limit is ${maxRowNumber}`, {
      context,
    });

    return maxRowNumber;
  }

  /**
   * method to invalidate cache entries, should be called from deployment call
   * @param tenantId tenant in which the object exists
   * @param spaceId space in which the object resides
   * @param correlationId correlation id from deployment call
   * @param entityNames list of object names in given space
   */
  public async invalidateCacheEntries(
    tenantId: string,
    spaceId: string,
    correlationId: string,
    entityNames: string[]
  ): Promise<void> {
    logInfoWithOptionalContext("invalidateCacheEntries", { context: { correlationId } });
    for (const entityName of entityNames) {
      const cacheKey = this.generateKey(tenantId, spaceId, entityName);
      try {
        logInfoWithOptionalContext(`invalidating intermediateRedisCache with key ${cacheKey}`, {
          context: { correlationId },
        });
        await this.intermediateCSNRedisCache.forget(cacheKey);
      } catch (err) {
        logErrorWithOptionalContext([err, `Error while invalidating redis cache with key ${cacheKey}`], {
          context: { correlationId },
        });
      }
    }
  }

  protected async getObjectId(
    context: RequestContext,
    entityName: string,
    spaceId: string
  ): Promise<RepositoryObject[]> {
    const paramsId: IGetObjectParameters = {
      qualifiedNames: [entityName],
      details: ["id", "name", "#technicalType"],
      inSpaceManagement: false,
      folderNames: spaceId,
    };

    return await RepositoryObjectClient.getObject(context, paramsId);
  }

  protected async getObjectsToInvalidate(
    context: RequestContext,
    id: string,
    entitiesToInvalidateBySpace: Map<string, string[]>
  ): Promise<Array<[string, string[]]>> {
    const paramsData: IGetDependenciesParams = {
      ids: id,
      details: ["id", "#spaceName", "name"],
      dependencyTypes: DependencyKind.cdsAssociation,
      kind: RepositoryObjectKind.entity,
      recursive: true,
      impact: true,
      lineage: false,
      level: 4,
    };

    const repoResponseData = await RepositoryObjectClient.getObjectDependencies(context, paramsData);

    const collectDependencies = (dependencies: IObjectDependency[]) => {
      for (const dependency of dependencies) {
        const spaceId = dependency.properties?.["#spaceName"];

        if (!spaceId) {
          continue;
        }

        const existingEntityNames = entitiesToInvalidateBySpace.get(spaceId) || [];
        existingEntityNames.push(dependency.name as string);

        entitiesToInvalidateBySpace.set(spaceId, existingEntityNames);

        if (dependency.dependencies) {
          setImmediate(() => collectDependencies(dependency.dependencies as IObjectDependency[]));
        }
      }
    };

    if (repoResponseData[0]?.dependencies) {
      collectDependencies(repoResponseData[0].dependencies);
    }

    return Array.from(entitiesToInvalidateBySpace.entries());
  }

  @ListenToDeployEvent()
  public async listenToDeployer(message: any) {
    logInfoWithOptionalContext("event 'deploy:success' received", {
      context: { correlationId: message.correlationId },
    });

    const { entityNames, spaceId, tenantId, correlationId } = message;

    const newContext = RequestContext.createFromTenantId(tenantId, { correlationId });

    const isRelationalAssociationEnabled = await FeatureFlagProvider.isFeatureActive(
      newContext,
      "DWCO_ODATA_RELATIONAL_ASSOCIATION"
    );

    let entityToClearTuples: Array<[string, string[]]> = [[spaceId, entityNames]];

    if (isRelationalAssociationEnabled) {
      const repoResponse = await FetchConsumptionCSN.getObjectId(newContext, entityNames[0], spaceId);

      if (repoResponse[0].properties?.["#technicalType"] !== RepositoryObjectType.DWC_ANALYTIC_MODEL) {
        entityToClearTuples = await FetchConsumptionCSN.getObjectsToInvalidate(
          newContext,
          repoResponse[0].id,
          new Map(entityToClearTuples)
        );
      }
    }

    for (const [spaceId, entityNames] of entityToClearTuples) {
      try {
        await FetchConsumptionCSN.invalidateCacheEntries(tenantId, spaceId, correlationId, entityNames);
      } catch (err) {
        logErrorWithOptionalContext(["error invalidating csn cache entries", err], {
          context: { correlationId },
        });
      }
    }
  }

  public getRowSize(entityDefinition: ICsnDefinition, context: IRequestContext): number {
    let rowSize = 0;

    try {
      const entityElements = entityDefinition.elements!;
      const elementNames = Object.keys(entityElements);

      logVerbose(`number of elements found ${elementNames.length}`, { context });

      Object.values(entityElements).forEach((oElement: ICsnElement) => {
        rowSize += this.getDataTypeSize(oElement.type, oElement.length);
      });
    } catch (err) {
      logError(err, { context });
      rowSize = 0;
    }
    return rowSize;
  }

  public getDataTypeSize(type: string, length?: number): number {
    switch (type) {
      case "cds.Boolean":
        return 1; // 1 byte for Boolean
      case "cds.Integer":
      case "hana.SMALLINT":
      case "hana.TINYINT":
      case "cds.hana.SMALLINT":
      case "cds.hana.TINYINT":
        return 4; // 4 byte for Int
      case "cds.Integer64":
        return 8; // 8 byte for Int64
      case "cds.Decimal":
      case "cds.hana.SMALLDECIMAL":
      case "hana.SMALLDECIMAL":
      case "cds.hana.REAL":
      case "hana.REAL":
        return 20; // 20 bytes for Decimal
      case "cds.Double":
      case "cds.BinaryFloat":
        return 20; // 20 bytes for Double
      case "cds.Date":
      case "cds.LocalDate":
        return 10; // 10 bytes for date - example 2021-06-27
      case "cds.Time":
      case "cds.LocalTime":
        return 8; // 8 bytes for time - example 07:59:59
      case "cds.DateTime":
      case "cds.UTCDateTime":
        return 20; // 20 bytes for dateTime - example 2021-06-27T14:52:23Z
      case "cds.Timestamp":
      case "cds.UTCTimestamp":
        return 24; // 24 bytes for timestamp - example 2021-06-27T14:52:23.123Z
      case "cds.String":
      case "cds.hana.CHAR":
      case "cds.hana.NCHAR":
      case "cds.hana.VARCHAR":
      case "hana.CHAR":
      case "hana.NCHAR":
      case "hana.VARCHAR":
        return length!;
      case "cds.Binary":
      case "hana.BINARY":
      case "cds.hana.BINARY":
        return length!;
      case "cds.LargeBinary":
        return 5000; // use 5000 byte as default value for BLOB
      case "cds.LargeString":
      case "cds.hana.CLOB":
      case "hana.CLOB":
        return 5000; // use 5000 byte as default value for CLOB
      case "cds.UUID":
        return 36; // 36 char string UUID
      case "hana.ST_POINT":
      case "hana.ST_GEOMETRY":
      case "cds.hana.ST_POINT":
      case "cds.hana.ST_GEOMETRY":
        return 100; // 100 byte for geometry data
      default: // use 100 byte as default value
        return 100;
    }
  }

  public getElementsCount(csnElements: ICsnElements): number {
    return Object.keys(csnElements).length;
  }
}

logInfoWithOptionalContext("creating singleton instance of FetchConsumptionCSNHandler");
export const FetchConsumptionCSN = new FetchConsumptionCSNHandler<ICsnDocument>();

async function getFullCsn(req: Request, res: Response): Promise<ICsnDocument> {
  const schemaName = req.params.space;
  const context = req.context;
  const entity = req.params.entity;

  const dbClient = await resolveDbClient(context, schemaName);
  logVerbose("dbClient fetched", { context });
  const spaceId = await getSpaceUuidFromName(context, schemaName);
  logVerbose("spaceUuid fetched", { context });

  const adaptationContext: IAdaptationContext = {
    deployedEntity: {},
    entityName: entity,
    spaceId,
    schemaName,
    dbClient,
    requestContext: context,
    isPersistedEntity: false,
    checkPrivilege: false,
  };

  const isRelationalAssociationEnabled = await FeatureFlagProvider.isFeatureActive(
    context,
    "DWCO_ODATA_RELATIONAL_ASSOCIATION"
  );

  let fullCsn: ICsnDocument;
  if (!res.locals.isAnalyticModel) {
    if (isRelationalAssociationEnabled) {
      fullCsn = await resolveFullCsnWithAdvisor(
        adaptationContext,
        req,
        CsnRepositoryTag.DeployedCsnForConsumptionWithAssociations
      );
    } else {
      fullCsn = await resolveFullCsnWithAdvisor(adaptationContext, req);
    }
  } else {
    fullCsn = await resolveFullCsn(adaptationContext, req);
    fullCsn = replaceInvalidElementNames(context, fullCsn, entity);
  }

  // to avoid the need of query and associations for CSN compilation on CAP
  fullCsn.definitions[entity]["@cds.persistence.exists"] = true;
  fullCsn.definitions[entity]["@readonly"] = true;

  return fullCsn;
}

/**
 * Replace Invalid element names for MDS consumption according to https://github.wdf.sap.corp/orca/seal/blob/master/libs/csn/src/naming/Constants.ts
 * @returns
 */
function replaceInvalidElementNames(context: IRequestContext, fullCsn: ICsnDocument, entityName: string): ICsnDocument {
  const NOT_ALLOWED_CHARS_REGEX = /[/\\".]/g;
  const REPLACE_CHAR = "_";
  try {
    const entityDefinition = fullCsn.definitions[entityName];
    const elements = entityDefinition.elements;
    if (!elements) {
      return fullCsn;
    }

    Object.entries(elements).forEach(([elementName, elementProps]) => {
      if (NOT_ALLOWED_CHARS_REGEX.test(elementName)) {
        const newElementName = elementName.replace(NOT_ALLOWED_CHARS_REGEX, REPLACE_CHAR);
        elements[newElementName] = elementProps;
        delete elements[elementName];
      }
    });

    return fullCsn;
  } catch (err) {
    logWarning(["Failed to replace invalid CSN element names", err], { context });
    return fullCsn;
  }
}
