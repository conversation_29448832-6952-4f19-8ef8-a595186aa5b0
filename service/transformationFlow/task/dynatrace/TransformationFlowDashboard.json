{"metadata": {"configurationVersions": [7], "clusterVersion": "1.312.56.20250509-131425"}, "id": "0e1473b5-0a54-4f90-93a1-2e17126089db", "dashboardMetadata": {"name": "Transformation Flow", "shared": true, "owner": "D040293", "dashboardFilter": {"timeframe": "-24h to now"}, "preset": true, "popularity": 2, "dynamicFilters": {"filters": ["CUSTOM_DIMENSION:tenantid", "CUSTOM_DIMENSION:org"], "genericTagFilters": []}, "hasConsistentColors": false}, "tiles": [{"name": "Activity EXECUTE", "tileType": "HEADER", "configured": true, "bounds": {"top": 684, "left": 0, "width": 1330, "height": 38}, "tileFilter": {}, "isAutoRefreshDisabled": false}, {"name": "#Task Executions", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 0, "left": 0, "width": 988, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Table", "queries": [{"id": "A", "metric": "taskFramework.v3.taskExecution.business.runtimeSec", "spaceAggregation": "COUNT", "timeAggregation": "DEFAULT", "splitBy": ["org", "tenantid", "activity"], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"filterOperator": "AND", "nestedFilters": [{"filter": "applicationid", "filterType": "DIMENSION", "filterOperator": "OR", "nestedFilters": [], "criteria": [{"value": "TRANSFORMATION_FLOWS", "evaluator": "EQ"}]}], "criteria": []}, "limit": 100, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "PIE_CHART", "global": {"hideLegend": false}, "rules": [{"matcher": "A:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "#Executions"}, "seriesOverrides": []}], "axes": {"xAxis": {"visible": true}, "yAxes": []}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "columnId": "", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "A", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=Inf&(taskFramework.v3.taskExecution.business.runtimeSec:filter(and(or(eq(applicationid,TRANSFORMATION_FLOWS)))):splitBy(org,tenantid,activity):count:sort(value(avg,descending)):limit(100)):limit(100):names"]}, {"name": "#Activity / Status / Substatus", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 0, "left": 1026, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "taskFramework.v3.taskExecution.business.runtimeSec", "spaceAggregation": "COUNT", "timeAggregation": "DEFAULT", "splitBy": ["activity", "status", "substatus"], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"filterOperator": "AND", "nestedFilters": [{"filter": "applicationid", "filterType": "DIMENSION", "filterOperator": "OR", "nestedFilters": [], "criteria": [{"value": "TRANSFORMATION_FLOWS", "evaluator": "EQ"}]}], "criteria": []}, "limit": 100, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "GRAPH_CHART", "global": {"hideLegend": false}, "rules": [{"matcher": "A:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "taskFramework.v3.taskExecution.business.runtimeSec"}, "seriesOverrides": []}], "axes": {"xAxis": {"displayName": "", "visible": true}, "yAxes": [{"displayName": "", "visible": true, "min": "AUTO", "max": "AUTO", "position": "LEFT", "queryIds": ["A"], "defaultAxis": true}]}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": "1h"}, "metricExpressions": ["resolution=1h&(taskFramework.v3.taskExecution.business.runtimeSec:filter(and(or(eq(applicationid,TRANSFORMATION_FLOWS)))):splitBy(activity,status,substatus):count:sort(value(avg,descending)):limit(100)):limit(100):names"]}, {"name": "MAX(Runtime)", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 342, "left": 0, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "taskFramework.v3.taskExecution.business.runtimeSec", "spaceAggregation": "MAX", "timeAggregation": "DEFAULT", "splitBy": ["activity"], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"filterOperator": "AND", "nestedFilters": [{"filter": "applicationid", "filterType": "DIMENSION", "filterOperator": "OR", "nestedFilters": [], "criteria": [{"value": "TRANSFORMATION_FLOWS", "evaluator": "EQ"}]}], "criteria": []}, "limit": 100, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "GRAPH_CHART", "global": {"hideLegend": false}, "rules": [{"matcher": "A:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "MAX(taskFramework.v3.taskExecution.business.runtimeSec)"}, "seriesOverrides": []}], "axes": {"xAxis": {"displayName": "", "visible": true}, "yAxes": [{"displayName": "", "visible": true, "min": "AUTO", "max": "AUTO", "position": "LEFT", "queryIds": ["A"], "defaultAxis": true}]}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": "1h"}, "metricExpressions": ["resolution=1h&(taskFramework.v3.taskExecution.business.runtimeSec:filter(and(or(eq(applicationid,TRANSFORMATION_FLOWS)))):splitBy(activity):max:sort(value(max,descending)):limit(100)):limit(100):names"]}, {"name": "MAX(Observer Runtime)", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 342, "left": 342, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "B", "metric": "taskFramework.v3.synchronizer.runtime.task", "spaceAggregation": "MAX", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"filterOperator": "AND", "nestedFilters": [{"filter": "applicationid", "filterType": "DIMENSION", "filterOperator": "OR", "nestedFilters": [], "criteria": [{"value": "TRANSFORMATION_FLOWS", "evaluator": "EQ"}]}], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "GRAPH_CHART", "global": {"hideLegend": true}, "rules": [{"matcher": "B:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "MAX(taskFramework.v3.synchronizer.runtime.task)"}, "seriesOverrides": []}], "axes": {"xAxis": {"displayName": "", "visible": true}, "yAxes": [{"displayName": "", "visible": true, "min": "AUTO", "max": "AUTO", "position": "RIGHT", "queryIds": ["B"], "defaultAxis": true}]}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"value": 0, "color": "#7dc540"}, {"value": 30000, "color": "#f5d30f"}, {"value": 60000, "color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": "1h"}, "metricExpressions": ["resolution=1h&(taskFramework.v3.synchronizer.runtime.task:filter(and(or(eq(applicationid,TRANSFORMATION_FLOWS)))):splitBy():max:sort(value(max,descending)):limit(20)):limit(100):names"]}, {"name": "MAX(#Running Tasks per Tenant)", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 342, "left": 684, "width": 646, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "taskFramework.v3.diagnostics.runningTasksPerApplication", "spaceAggregation": "MAX", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"filterOperator": "AND", "nestedFilters": [{"filter": "applicationid", "filterType": "DIMENSION", "filterOperator": "OR", "nestedFilters": [], "criteria": [{"value": "TRANSFORMATION_FLOWS", "evaluator": "EQ"}]}], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "GRAPH_CHART", "global": {"hideLegend": true}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"displayName": "", "visible": true}, "yAxes": [{"displayName": "", "visible": true, "min": "AUTO", "max": "AUTO", "position": "LEFT", "queryIds": ["A"], "defaultAxis": true}]}, "heatmapSettings": {"yAxis": "VALUE", "showLabels": false}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": "1h"}, "metricExpressions": ["resolution=1h&(taskFramework.v3.diagnostics.runningTasksPerApplication:filter(and(or(eq(applicationid,TRANSFORMATION_FLOWS)))):splitBy():max:sort(value(max,descending)):limit(20)):limit(100):names"]}, {"name": "MAX(#Records)", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 760, "left": 0, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.numberOfRecords", "spaceAggregation": "MAX", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "GRAPH_CHART", "global": {"hideLegend": true}, "rules": [{"matcher": "A:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "MAX(transformationFlow.execution.numberOfRecords)"}, "seriesOverrides": []}], "axes": {"xAxis": {"displayName": "", "visible": true}, "yAxes": [{"displayName": "", "visible": true, "min": "AUTO", "max": "AUTO", "position": "LEFT", "queryIds": ["A"], "defaultAxis": true}]}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": "1h"}, "metricExpressions": ["resolution=1h&(transformationFlow.execution.numberOfRecords:splitBy():max:sort(value(max,descending)):limit(20)):limit(100):names"]}, {"name": "MAX(Peak Memory Consumption)", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 760, "left": 342, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.peakMemoryConsumptionMiB", "spaceAggregation": "MAX", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "GRAPH_CHART", "global": {"hideLegend": true}, "rules": [{"matcher": "A:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "MAX(transformationFlow.execution.peakMemoryConsumptionMiB)"}, "seriesOverrides": []}], "axes": {"xAxis": {"displayName": "", "visible": true}, "yAxes": [{"displayName": "", "visible": true, "min": "AUTO", "max": "AUTO", "position": "LEFT", "queryIds": ["A"], "defaultAxis": true}]}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": "1h"}, "metricExpressions": ["resolution=1h&(transformationFlow.execution.peakMemoryConsumptionMiB:splitBy():max:sort(value(max,descending)):limit(20)):limit(100):names"]}, {"name": "#Peak Memory not available", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 760, "left": 684, "width": 646, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Table", "queries": [{"id": "A", "metric": "taskFramework.v3.taskExecution.business.runtimeSec", "spaceAggregation": "COUNT", "timeAggregation": "DEFAULT", "splitBy": ["org", "tenantid"], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"filterOperator": "AND", "nestedFilters": [{"filter": "activity", "filterType": "DIMENSION", "filterOperator": "OR", "nestedFilters": [], "criteria": [{"value": "EXECUTE", "evaluator": "EQ"}]}, {"filter": "applicationid", "filterType": "DIMENSION", "filterOperator": "OR", "nestedFilters": [], "criteria": [{"value": "TRANSFORMATION_FLOWS", "evaluator": "EQ"}]}], "criteria": []}, "limit": -1, "rate": "NONE", "enabled": false}, {"id": "B", "spaceAggregation": "AUTO", "timeAggregation": "DEFAULT", "metricSelector": "transformationFlow.execution.peakMemoryConsumptionMiB:splitBy(org,tenantid):count:sort(value(avg,descending))", "rate": "NONE", "enabled": false}, {"id": "C", "spaceAggregation": "AUTO", "timeAggregation": "DEFAULT", "splitBy": ["org", "tenantid"], "metricSelector": "taskFramework.v3.taskExecution.business.runtimeSec:filter(and(or(eq(activity,EXECUTE)),or(eq(applicationid,TRANSFORMATION_FLOWS)))):splitBy(org,tenantid):count:sort(value(avg,descending))-transformationFlow.execution.peakMemoryConsumptionMiB:splitBy(org,tenantid):count:sort(value(avg,descending)):default(0)", "rate": "NONE", "enabled": true}, {"id": "D", "metric": "taskFramework.v3.taskExecution.business.runtimeSec", "spaceAggregation": "COUNT", "timeAggregation": "DEFAULT", "splitBy": ["org", "tenantid"], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"filterOperator": "AND", "nestedFilters": [{"filter": "applicationid", "filterType": "DIMENSION", "filterOperator": "OR", "nestedFilters": [], "criteria": [{"value": "TRANSFORMATION_FLOWS", "evaluator": "EQ"}]}, {"filter": "activity", "filterType": "DIMENSION", "filterOperator": "OR", "nestedFilters": [], "criteria": [{"value": "EXECUTE", "evaluator": "EQ"}]}], "criteria": []}, "limit": -1, "rate": "NONE", "enabled": true}, {"id": "E", "spaceAggregation": "AUTO", "timeAggregation": "DEFAULT", "metricSelector": "transformationFlow.execution.peakMemoryConsumptionMiB:splitBy(org,tenantid):count:sort(value(avg,descending))", "rate": "NONE", "enabled": false}], "visualConfig": {"type": "TABLE", "global": {"hideLegend": false}, "rules": [{"matcher": "A:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "#Executions"}, "seriesOverrides": []}, {"matcher": "B:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "#Peak Memory available"}, "seriesOverrides": []}, {"matcher": "C:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "#Peak Memory not available"}, "seriesOverrides": []}, {"matcher": "D:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "#Executions"}, "seriesOverrides": []}, {"matcher": "E:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "#Peak Memory available"}, "seriesOverrides": []}], "axes": {"xAxis": {"visible": true}, "yAxes": []}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "columnId": "org", "rules": [{"value": 0, "color": "#7dc540"}, {"value": 1, "color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "C", "visible": true}, {"axisTarget": "LEFT", "columnId": "org", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "A", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": ["A:org.name", "A:tenantid.name", "B:org.name", "B:tenantid.name", "C:org.name", "C:tenantid.name", "D:org.name", "D:tenantid.name", "E:org.name", "E:tenantid.name"]}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=null&(taskFramework.v3.taskExecution.business.runtimeSec:filter(and(or(eq(activity,EXECUTE)),or(eq(applicationid,TRANSFORMATION_FLOWS)))):splitBy(org,tenantid):count:sort(value(avg,descending))-transformationFlow.execution.peakMemoryConsumptionMiB:splitBy(org,tenantid):count:sort(value(avg,descending)):default(0)):names:fold(auto),(taskFramework.v3.taskExecution.business.runtimeSec:filter(and(or(eq(activity,EXECUTE)),or(eq(applicationid,TRANSFORMATION_FLOWS)))):splitBy(org,tenantid):count:sort(value(avg,descending))):names:fold(auto)"]}, {"name": "#Execution: loadType, truncate, deltaSource, deltaTarget, deltaLoad", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 1102, "left": 0, "width": 646, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.dummy", "spaceAggregation": "COUNT", "timeAggregation": "DEFAULT", "splitBy": ["loadtype", "truncate", "deltasource", "deltatarget", "deltaload"], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "PIE_CHART", "global": {"hideLegend": false}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"visible": true}, "yAxes": []}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=Inf&(transformationFlow.execution.dummy:splitBy(loadtype,truncate,deltasource,deltatarget,deltaload):count:sort(value(avg,descending)):limit(20)):limit(100):names"]}, {"name": "#Execution Mode", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 1102, "left": 684, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.dummy", "spaceAggregation": "COUNT", "timeAggregation": "DEFAULT", "splitBy": ["executionmode"], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "PIE_CHART", "global": {"hideLegend": false}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"visible": true}, "yAxes": []}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=Inf&(transformationFlow.execution.dummy:splitBy(executionmode):count:sort(value(avg,descending)):limit(20)):limit(100):names"]}, {"name": "#Load unit of target table", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 1444, "left": 0, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.dummy", "spaceAggregation": "COUNT", "timeAggregation": "DEFAULT", "splitBy": ["loadunittarget"], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "PIE_CHART", "global": {"hideLegend": false}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"visible": true}, "yAxes": []}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=Inf&(transformationFlow.execution.dummy:splitBy(loadunittarget):count:sort(value(avg,descending)):limit(20)):limit(100):names"]}, {"name": "#Remote Access", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 1102, "left": 1026, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.dummy", "spaceAggregation": "COUNT", "timeAggregation": "DEFAULT", "splitBy": ["remoteaccess"], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "PIE_CHART", "global": {"hideLegend": false}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"visible": true}, "yAxes": []}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=Inf&(transformationFlow.execution.dummy:splitBy(remoteaccess):count:sort(value(avg,descending)):limit(20)):limit(100):names"]}, {"name": "#View transform stacked", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 1444, "left": 342, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.dummy", "spaceAggregation": "COUNT", "timeAggregation": "DEFAULT", "splitBy": ["viewtransformstacked"], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "PIE_CHART", "global": {"hideLegend": false}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"visible": true}, "yAxes": []}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=Inf&(transformationFlow.execution.dummy:splitBy(viewtransformstacked):count:sort(value(avg,descending)):limit(20)):limit(100):names"]}, {"name": "#View transform type", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 1444, "left": 684, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.dummy", "spaceAggregation": "COUNT", "timeAggregation": "DEFAULT", "splitBy": ["viewtransformtype"], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "PIE_CHART", "global": {"hideLegend": false}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"visible": true}, "yAxes": []}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=Inf&(transformationFlow.execution.dummy:splitBy(viewtransformtype):count:sort(value(avg,descending)):limit(20)):limit(100):names"]}, {"name": "#Runtime", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 1444, "left": 1026, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.dummy", "spaceAggregation": "COUNT", "timeAggregation": "DEFAULT", "splitBy": ["runtime"], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "PIE_CHART", "global": {"hideLegend": false}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"visible": true}, "yAxes": []}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=Inf&(transformationFlow.execution.dummy:splitBy(runtime):count:sort(value(avg,descending)):limit(20)):limit(100):names"]}, {"name": "#Python operator", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 1786, "left": 0, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.dummy", "spaceAggregation": "COUNT", "timeAggregation": "DEFAULT", "splitBy": ["pythonoperator"], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "PIE_CHART", "global": {"hideLegend": false}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"visible": true}, "yAxes": []}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=Inf&(transformationFlow.execution.dummy:splitBy(pythonoperator):count:sort(value(avg,descending)):limit(20)):limit(100):names"]}, {"name": "Batch Processing", "tileType": "HEADER", "configured": true, "bounds": {"top": 2128, "left": 0, "width": 1330, "height": 38}, "tileFilter": {}, "isAutoRefreshDisabled": false}, {"name": "%Execs using Batch Processing", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 2204, "left": 0, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "%Execs using Batch Processing", "queries": [{"id": "A", "spaceAggregation": "AUTO", "timeAggregation": "DEFAULT", "splitBy": [], "metricSelector": "transformationFlow.execution.numberOfBatches:splitBy():count:sort(value(avg,descending)):limit(20)/transformationFlow.execution.dummy:splitBy():count:sort(value(avg,descending)):limit(20)*100", "rate": "NONE", "enabled": true}], "visualConfig": {"type": "SINGLE_VALUE", "global": {}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"visible": true}, "yAxes": []}, "heatmapSettings": {"yAxis": "VALUE"}, "singleValueSettings": {"showTrend": false, "showSparkLine": false, "linkTileColorToThreshold": false}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "visible": true}], "tableSettings": {"hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=Inf&(transformationFlow.execution.numberOfBatches:splitBy():count:sort(value(avg,descending)):limit(20)/transformationFlow.execution.dummy:splitBy():count:sort(value(avg,descending)):limit(20)*100):limit(100):names"]}, {"name": "#Execs using Batch Processing", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 2204, "left": 342, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "#Execs using Batch Processing", "queries": [{"id": "A", "metric": "transformationFlow.execution.numberOfBatches", "spaceAggregation": "COUNT", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "SINGLE_VALUE", "global": {}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"visible": true}, "yAxes": []}, "heatmapSettings": {"yAxis": "VALUE"}, "singleValueSettings": {"showTrend": false, "showSparkLine": false, "linkTileColorToThreshold": false}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "visible": true}], "tableSettings": {"hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=Inf&(transformationFlow.execution.numberOfBatches:splitBy():count:sort(value(avg,descending)):limit(20)):limit(100):names"]}, {"name": "#Batches", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 2204, "left": 684, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.numberOfBatches", "spaceAggregation": "MAX", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}, {"id": "B", "metric": "transformationFlow.execution.numberOfBatches", "spaceAggregation": "AVG", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}, {"id": "C", "metric": "transformationFlow.execution.numberOfBatches", "spaceAggregation": "MIN", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "GRAPH_CHART", "global": {}, "rules": [{"matcher": "A:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "MAX(#Batches)"}, "seriesOverrides": []}, {"matcher": "B:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "AVG(#Batches)"}, "seriesOverrides": []}, {"matcher": "C:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "MIN(#Bat<PERSON>)"}, "seriesOverrides": []}], "axes": {"xAxis": {"displayName": "", "visible": true}, "yAxes": [{"displayName": "", "visible": true, "min": "AUTO", "max": "AUTO", "position": "LEFT", "queryIds": ["A", "B", "C"], "defaultAxis": true}]}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "visible": true}], "tableSettings": {"hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=null&(transformationFlow.execution.numberOfBatches:splitBy():max:sort(value(max,descending)):limit(20)):limit(100):names,(transformationFlow.execution.numberOfBatches:splitBy():avg:sort(value(avg,descending)):limit(20)):limit(100):names,(transformationFlow.execution.numberOfBatches:splitBy():min:sort(value(min,descending)):limit(20)):limit(100):names"]}, {"name": "MAX(Records of Batch)", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 2546, "left": 0, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.numberOfRecordsOfBatch", "spaceAggregation": "MAX", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "GRAPH_CHART", "global": {}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"displayName": "", "visible": true}, "yAxes": [{"displayName": "", "visible": true, "min": "AUTO", "max": "AUTO", "position": "LEFT", "queryIds": ["A"], "defaultAxis": true}]}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "visible": true}], "tableSettings": {"hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=null&(transformationFlow.execution.numberOfRecordsOfBatch:splitBy():max:sort(value(max,descending)):limit(20)):limit(100):names"]}, {"name": "Memory Consumption of Batch", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 2546, "left": 342, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.memoryConsumptionMiBOfBatch", "spaceAggregation": "MAX", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}, {"id": "B", "metric": "transformationFlow.execution.memoryConsumptionMiBOfBatch", "spaceAggregation": "AVG", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "GRAPH_CHART", "global": {}, "rules": [{"matcher": "A:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "MAX(memoryConsumptionMiBOfBatch)"}, "seriesOverrides": []}, {"matcher": "B:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "AVG(memoryConsumptionMiBOfBatch)"}, "seriesOverrides": []}], "axes": {"xAxis": {"displayName": "", "visible": true}, "yAxes": [{"displayName": "", "visible": true, "min": "AUTO", "max": "AUTO", "position": "LEFT", "queryIds": ["A", "B"], "defaultAxis": true}]}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "visible": true}], "tableSettings": {"hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=null&(transformationFlow.execution.memoryConsumptionMiBOfBatch:splitBy():max:sort(value(max,descending)):limit(20)):limit(100):names,(transformationFlow.execution.memoryConsumptionMiBOfBatch:splitBy():avg:sort(value(avg,descending)):limit(20)):limit(100):names"]}, {"name": "Memory Consumption of reading distinct values of batch column", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 2888, "left": 0, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.step.memoryConsumptionMiB", "spaceAggregation": "MAX", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"filterOperator": "AND", "nestedFilters": [{"filter": "step", "filterType": "DIMENSION", "filterOperator": "OR", "nestedFilters": [], "criteria": [{"value": "readDistinctValuesOfBatchColumn", "evaluator": "EQ"}]}], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}, {"id": "B", "metric": "transformationFlow.execution.step.memoryConsumptionMiB", "spaceAggregation": "AVG", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"filterOperator": "AND", "nestedFilters": [{"filter": "step", "filterType": "DIMENSION", "filterOperator": "OR", "nestedFilters": [], "criteria": [{"value": "readDistinctValuesOfBatchColumn", "evaluator": "EQ"}]}], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "GRAPH_CHART", "global": {}, "rules": [{"matcher": "A:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "MAX(memoryConsumptionMiB)"}, "seriesOverrides": []}, {"matcher": "B:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "AVG(memoryConsumptionMiB)"}, "seriesOverrides": []}], "axes": {"xAxis": {"displayName": "", "visible": true}, "yAxes": [{"displayName": "", "visible": true, "min": "AUTO", "max": "AUTO", "position": "LEFT", "queryIds": ["A", "B"], "defaultAxis": true}]}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "visible": true}], "tableSettings": {"hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=null&(transformationFlow.execution.step.memoryConsumptionMiB:filter(and(or(eq(step,readDistinctValuesOfBatchColumn)))):splitBy():max:sort(value(max,descending)):limit(20)):limit(100):names,(transformationFlow.execution.step.memoryConsumptionMiB:filter(and(or(eq(step,readDistinctValuesOfBatchColumn)))):splitBy():avg:sort(value(avg,descending)):limit(20)):limit(100):names"]}, {"name": "Batch size", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 2204, "left": 1026, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.batchSize", "spaceAggregation": "MAX", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}, {"id": "B", "metric": "transformationFlow.execution.batchSize", "spaceAggregation": "AVG", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}, {"id": "C", "metric": "transformationFlow.execution.batchSize", "spaceAggregation": "MIN", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "GRAPH_CHART", "global": {}, "rules": [{"matcher": "A:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "MAX(#Batch size)"}, "seriesOverrides": []}, {"matcher": "B:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "AVG(#Batch size)"}, "seriesOverrides": []}, {"matcher": "C:", "unitTransform": "auto", "valueFormat": "auto", "properties": {"color": "DEFAULT", "seriesType": "LINE", "alias": "MIN(#Batch size)"}, "seriesOverrides": []}], "axes": {"xAxis": {"displayName": "", "visible": true}, "yAxes": [{"displayName": "", "visible": true, "min": "AUTO", "max": "AUTO", "position": "LEFT", "queryIds": ["A", "B", "C"], "defaultAxis": true}]}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "visible": true}], "tableSettings": {"hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=null&(transformationFlow.execution.batchSize:splitBy():max:sort(value(max,descending)):limit(20)):limit(100):names,(transformationFlow.execution.batchSize:splitBy():avg:sort(value(avg,descending)):limit(20)):limit(100):names,(transformationFlow.execution.batchSize:splitBy():min:sort(value(min,descending)):limit(20)):limit(100):names"]}, {"name": "MAX(Runtime of Batch)", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 2546, "left": 684, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.runtimeMsOfBatch", "spaceAggregation": "MAX", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "GRAPH_CHART", "global": {"hideLegend": true}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"displayName": "", "visible": true}, "yAxes": [{"displayName": "", "visible": true, "min": "AUTO", "max": "AUTO", "position": "LEFT", "queryIds": ["A"], "defaultAxis": true}]}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "visible": true}], "tableSettings": {"hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=null&(transformationFlow.execution.runtimeMsOfBatch:splitBy():max:sort(value(max,descending)):limit(20)):limit(100):names"]}, {"name": "MAX(Records of <PERSON><PERSON><PERSON> Batch)", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 2546, "left": 1026, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.numberOfRecordsOfBatch", "spaceAggregation": "MAX", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"filterOperator": "AND", "nestedFilters": [{"filter": "batchid", "filterType": "DIMENSION", "filterOperator": "OR", "nestedFilters": [], "criteria": [{"value": "OthersNull", "evaluator": "EQ"}]}], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "GRAPH_CHART", "global": {"hideLegend": true}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"displayName": "", "visible": true}, "yAxes": [{"displayName": "", "visible": true, "min": "AUTO", "max": "AUTO", "position": "LEFT", "queryIds": ["A"], "defaultAxis": true}]}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "visible": true}], "tableSettings": {"hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=null&(transformationFlow.execution.numberOfRecordsOfBatch:filter(and(or(eq(batchid,OthersNull)))):splitBy():max:sort(value(max,descending)):limit(20)):limit(100):names"]}, {"name": "MAX(Runtime read distinct values of batch column)", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 2888, "left": 342, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.step.runtimeMs", "spaceAggregation": "MAX", "timeAggregation": "DEFAULT", "splitBy": [], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"filterOperator": "AND", "nestedFilters": [{"filter": "step", "filterType": "DIMENSION", "filterOperator": "OR", "nestedFilters": [], "criteria": [{"value": "readDistinctValuesOfBatchColumn", "evaluator": "EQ"}]}], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "GRAPH_CHART", "global": {"hideLegend": true}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"displayName": "", "visible": true}, "yAxes": [{"displayName": "", "visible": true, "min": "AUTO", "max": "AUTO", "position": "LEFT", "queryIds": ["A"], "defaultAxis": true}]}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "visible": true}], "tableSettings": {"hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=null&(transformationFlow.execution.step.runtimeMs:filter(and(or(eq(step,readDistinctValuesOfBatchColumn)))):splitBy():max:sort(value(max,descending)):limit(20)):limit(100):names"]}, {"name": "#Incremental Aggregation", "tileType": "DATA_EXPLORER", "configured": true, "bounds": {"top": 1786, "left": 342, "width": 304, "height": 304}, "tileFilter": {}, "isAutoRefreshDisabled": false, "customName": "Data explorer results", "queries": [{"id": "A", "metric": "transformationFlow.execution.dummy", "spaceAggregation": "COUNT", "timeAggregation": "DEFAULT", "splitBy": ["incrementalaggregation"], "sortBy": "DESC", "sortByDimension": "", "filterBy": {"nestedFilters": [], "criteria": []}, "limit": 20, "rate": "NONE", "enabled": true}], "visualConfig": {"type": "PIE_CHART", "global": {"hideLegend": false}, "rules": [{"matcher": "A:", "properties": {"color": "DEFAULT"}, "seriesOverrides": []}], "axes": {"xAxis": {"visible": true}, "yAxes": []}, "heatmapSettings": {"yAxis": "VALUE"}, "thresholds": [{"axisTarget": "LEFT", "rules": [{"color": "#7dc540"}, {"color": "#f5d30f"}, {"color": "#dc172a"}], "queryId": "", "visible": true}], "tableSettings": {"isThresholdBackgroundAppliedToCell": false, "hiddenColumns": []}, "graphChartSettings": {"connectNulls": false}, "honeycombSettings": {"showHive": true, "showLegend": true, "showLabels": false}}, "queriesSettings": {"resolution": ""}, "metricExpressions": ["resolution=Inf&(transformationFlow.execution.dummy:splitBy(incrementalaggregation):count:sort(value(avg,descending)):limit(20)):limit(100):names"]}]}