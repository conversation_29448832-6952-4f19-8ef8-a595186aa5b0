/** @format */

import * as semver from "semver";

export type Version = string | number | semver.SemVer | null | undefined;

export function normalizeVersions(...versions: Version[]): Version[] {
  return versions.map((version) =>
    typeof version === "string"
      ? version
          .trim() // trims leading and trailing whitespaces
          .replace(/\b0+\B/gm, "") // removes leading zeros
          .replace(/(?![0-9]+)(\s*\/|\.?\s*)(?!$)/gm, ".") // replace empty spaces and/or slashes with dots
      : version
  );
}

export function semverCoerce(version: Version = ""): string {
  version = normalizeVersions(version)[0];
  return semver.coerce(version, { loose: true })?.version!;
}

export function isSameMajorAndGTE(from: string, to: string): boolean {
  const fromVersion = semver.parse(from);
  const toVersion = semver.parse(to);

  if (!fromVersion || !toVersion) {
    throw new Error("Invalid version format");
  }

  return fromVersion.major === toVersion.major && semver.gte(fromVersion, toVersion);
}

export function gt(from: Version, to: Version) {
  return semver.gt(semverCoerce(from), semverCoerce(to), {
    loose: true,
  });
}

export function gte(from: Version, to: Version) {
  return semver.gte(semverCoerce(from), semverCoerce(to), {
    loose: true,
  });
}

export function lte(from: Version, to: Version) {
  return semver.lte(semverCoerce(from), semverCoerce(to), {
    loose: true,
  });
}
