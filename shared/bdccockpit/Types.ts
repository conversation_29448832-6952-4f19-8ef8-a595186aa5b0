/** @format */

import { IRequestContext } from "@sap/deepsea-types";
import { SpanContext } from "@sap/dwc-tracing";
import { BDCPackageCategory, BDCPackageStatus, BDCPackageType, SourceSystemType } from "./Enums";

// ////////// PAckage //////////
export interface BDCBasePackage {
  id: string;
  name: string;
  preview: BDCPreviewImage[];
  type: BDCPackageType;
  creationDate: string;
  version: string;
  category: BDCPackageCategory;
  description: string;
  supportedSystemVersion?: string;
  systemType?: SourceSystemType;
}

export interface BDCInstalledPackage extends BDCBasePackage {
  installationId: string;
  insightAppLinks: object;
  workspaceLinks: object;
  status?: BDCPackageStatus;
  installedUTC?: string;
  isPackageUpdateAvailable: boolean;
  isBdcPackageAvailable?: boolean;
  sourceSystem?: BDCSystem | undefined;
  systems?: SystemInfo[];
  products: BDCInstalledProduct[];
  contents: BDCInstalledContent[];
  correlationId: string;
  lastModifiedTime?: Date;
  installLocation?: string;
  gracePeriodStart?: Date | null;
  gracePeriodDuration?: number | null;
  installationLocationName?: string;
  failedComponentsInstallationDetails?: FailedComponentInstallationDetails[];
  failedComponentsUninstallationDetails?: FailedComponentUninstallationDetails[];
}

export interface DspComponentInstallationDetails {
  importJobId: string;
  replicationFlowJobId: string;
  isWaitingForDpActivation?: boolean;
}

export interface SacComponentInstallationDetails {
  importJobId: string;
}

export interface FosComponentInstallationDetails {
  ordId: string;
  createdAt?: string;
  modifiedAt?: string;
  selfHealingStartTime?: string;
}

export type ComponentInstallationDetails =
  | DspComponentInstallationDetails
  | SacComponentInstallationDetails
  | FosComponentInstallationDetails[];

// PENDING:                    The component installation has not yet started
// WAITING_TO_INSTALL_READY    Checking if component is ready to be installed. In DSP case it checks of required components are shared in Catalog
// EXECUTING:                  The component is currently installing
// ERROR:                      The component installation has a recoverable error
// DONE:                       The component installation successfully ended
// FAIL:                       The component installation has an unrecoverable error or retryCount has exceeded the threshold
export type InstallationStatusCode =
  | "PENDING"
  | "WAITING_TO_INSTALL_READY"
  | "EXECUTING"
  | "ERROR"
  | "DONE"
  | "FAILED"
  | "NOT ACTIVATED";

export interface FailedComponentDetails {
  componentId: string;
  componentInstallationDetails: ComponentInstallationDetails;
  startTime: Date;
  endTime: Date;
  status: InstallationStatusCode;
  systemStatus?: any;
  message: string;
  retryCount: number;
}

export interface FailedComponentInstallationDetails extends FailedComponentDetails {}

export interface FailedComponentUninstallationDetails extends FailedComponentDetails {}

export interface BDCAvailablePackage extends BDCBasePackage {
  systems: BDCSystem[];
  products: BDCAvailableProduct[];
  contents: BDCAvailableContent[];
  isInstallable?: boolean;
  landscape?: string;
}

// ////////// Component //////////
export interface BDCComponent {
  componentId: string;
  category: ComponentCategory;
  name: string;
  version: string;
  provider: BDCProvider;
  description?: string;
  applications?: Application[];
}

export interface BDCDataProduct extends BDCComponent {
  category: "DataProduct";
  ordid: string;
  numberOfEntities?: number;
  systemVersion?: string;
}

export interface BDCAvailableProduct extends BDCDataProduct {
  isSourceSystemVersionCompatible: boolean;
}

export interface BDCInstalledProduct extends BDCDataProduct {
  isActive?: boolean;
}

export interface BDCContent extends BDCComponent {
  spaces: PackageFolder[];
  spaceName?: string;
  workspaceName?: string;
}

export interface BDCInstalledContent extends BDCContent {}

export interface BDCAvailableContent extends BDCContent {
  isSystemAvailableInFormation: boolean;
}

export interface InsightAppLinks {
  sac: string[];
  dsp: string[];
}

export interface ACNFolder {
  packageName: string;
  packageVersion: string;
  packageFolders: PackageFolder[];
}

export interface PackageFolder {
  name: string;
  sourceId: string;
  visible: boolean;
  url: string;
}

export interface BDCPreviewImage {
  url?: string;
  base64?: string;
}

export type S4NameSpace = "sap.s4pce" | "sap.s4";
export type HcmNameSpace = "sap.sf";
export type SourceNameSpaces = S4NameSpace | HcmNameSpace;

export type AnalyticsNameSpace = "sap.analytics";
export type DatasphereNameSpace = "sap.datasphere";
export type TargetNameSpaces = AnalyticsNameSpace | DatasphereNameSpace;

export type BdcCockpitNameSpace = "sap.bdc-cockpit";
export type ApplicationNamespace = SourceNameSpaces | TargetNameSpaces | BdcCockpitNameSpace;

export type BDCSourceProvider = "sap.s4" | "sap.sf";
export type BDCTargetProvider = "sap.datasphere" | "sap.analytics";
export type BDCProvider = BDCSourceProvider | BDCTargetProvider;
export type ComponentCategory = "DataProduct" | "CnPackage";

export interface BDCSystem {
  applicationNamespace: ApplicationNamespace;
  tenant: string;
  formations?: Formation[];
  name: string;
  version: string;
  isInstallable?: boolean;
  productsCompatibility?: {
    compatible: number;
    incompatible: number;
  };
}

export interface BDCPackageInstallationRequest {
  packageId: string; // id reference to available BDC Package
  installationProperties: BDCPackageInstallationProperties[]; // An array of objects contains installation properties for each provider
}

export interface InstallationResponseBody {
  missingEntitlementDetails?: any; // TODO: entitlement flow results in any
  installationId?: string;
  connectionCheckResult?:
    | {
        success: true;
      }
    | {
        success: false;
        errorMessage: string;
      };
}

export interface BDCPackageInstallationProperties {
  applicationNamespace: ApplicationNamespace;
  systemTenant: string;
  systemName: string;
  formationId?: string;
  formationCatalogUuid?: string;
  additionalInstallationProperties?: AdditionalInstallationProperty[];
}

export interface AdditionalInstallationProperty {
  name: AdditionalInstallationPropertyName;
  value: string;
}

export enum AdditionalInstallationPropertyName {
  fosInstallUrl = "FOS_URL",
  fosSystemAssignmentId = "FOS_SYSTEM_ASSIGNMENT_ID",
  selfHealingCustomTimeout = "SELF_HEALING_CUSTOM_TIMEOUT",
}

export interface SystemInfo {
  systemId: string;
  className: string;
  name: string;
  applicationNamespace: ApplicationNamespace;
  applicationTenantId: string;
  applicationVersion?: string;
  formations: Formation[];
  propertyTags?: PropertyTag[];
  isInstallable?: boolean;
}

export interface Formation {
  catalogUuid?: string;
  formationName?: string;
  formationTypeId: string;
  formationId: string;
  assignmentId?: string;
  installationLocationName?: string;
  isInstallable?: boolean;
  hasInstallationTargetSystems?: boolean;
  assignmentPropertyTags?: PropertyTag[];
}

export interface PropertyTag {
  propertyId: string;
  name: string;
  namespace: string;
  valueObjects: ValueObject[];
}

export interface ValueObject {
  valueString: string;
}

export interface BDCRequestContext extends IRequestContext {
  spanContext?: SpanContext;
}

export interface Application {
  category: "application";
  applicationNamespace: ApplicationNamespace;
  minVersion: string;
}
