/** @format */

import {
  AnalyticModelAggregationTypes,
  AnalyticModelAttributeType,
  AnalyticModelConstantSelectionType,
  AnalyticModelConversionTypeType,
  AnalyticModelExceptionAggregationType,
  AnalyticModelFilterOperandType,
  AnalyticModelMeasureType,
  AnalyticModelParameterType,
  AnalyticModelReferenceDateType,
  AnalyticModelRestrictedMeasureOperandType,
  AnalyticModelTargetCurrencyType,
  AnalyticModelTargetUnitType,
  IAnalyticModelAttribute,
  IAnalyticModelAttributeKey,
  IAnalyticModelCalculatedMeasure,
  IAnalyticModelConstantValue,
  IAnalyticModelCountDistinctMeasure,
  IAnalyticModelCurrencyConversionMeasure,
  IAnalyticModelDimensionSource,
  IAnalyticModelFilterOperandAttribute,
  IAnalyticModelMeasure,
  IAnalyticModelNonCumulativeMeasure,
  IAnalyticModelNonCumulativeSettings,
  IAnalyticModelRestrictedMeasure,
  IAnalyticModelSourceMeasure,
  IAnalyticModelSqlFunctionName,
  IAnalyticModelUnitConversionMeasure,
  IAnalyticModelVariableKey,
} from "../AnalyticModel";
import {
  ReverseAggregationTypeMapping,
  ReverseExceptionAggregationNcumTypeMapping,
  ReverseExceptionAggregationTypeMapping,
} from "../AnalyticModelCsnMappings";
import {
  CDS_EXPR,
  CDS_EXPR_FUNC,
  CDS_EXPR_LIST,
  CDS_EXPR_REF,
  CDS_EXPR_VAL,
  CDS_EXPR_XPR,
  CSN_XPR_COMPARISON,
  CSN_XPR_COMPARISON_PART,
  CsnXprToAMHelper,
  IConvertFormulaResult,
  MODEL_ELEMENT,
  XPR_PARTS,
  findLeftOperand,
  isAllowedOperatorInExpressionEditor,
  isCompareOperator,
  isElement,
  isFunction,
  isValue,
  isVariable,
} from "../CsnXprToAMHelper";
import { BASIC_TYPES, ExprTypeValidator } from "../ExprTypeValidator";
import { MeasureValidationHelper } from "../MeasureValidationHelper";
import { CONVERSION_TYPES_USED_IN_VALIDATION } from "../QueryModel";
import {
  ICreateExpressionResult,
  QueryModelValidator,
  ValidationMessage,
  ValidationMessageType,
} from "../QueryModelValidator";
import { VariablePathHelper } from "../VariablePathHelper";
import { AnalyticModelValidator } from "./AnalyticModelValidator";

export class MeasureValidator extends AnalyticModelValidator {
  registerExpressions(): void {
    for (const measureKey in this.commonParameters.analyticModel.measures ?? {}) {
      const measureProperties = this.commonParameters.analyticModel.measures![measureKey];
      if (
        measureProperties.measureType === AnalyticModelMeasureType.RestrictedMeasure ||
        measureProperties.measureType === AnalyticModelMeasureType.CalculatedMeasure
      ) {
        if ((measureProperties.formulaRaw ?? "").length > 0) {
          this.registerExpression(measureProperties.formulaRaw!);
        }
      }
    }
  }
  async validateInternal(): Promise<void> {
    // Check RKF and CKF for source measure and expression
    // & check names so attributes and measures don't clash

    for (const measureKey in this.commonParameters.analyticModel.measures ?? {}) {
      const measureProperties = this.commonParameters.analyticModel.measures![measureKey];

      /**
       * Restricted Measure validation
       */
      if (measureProperties.measureType === AnalyticModelMeasureType.RestrictedMeasure) {
        await this.validateRestricted(measureProperties, measureKey);
      }

      /**
       * Calculated Measure validation
       */
      if (measureProperties.measureType === AnalyticModelMeasureType.CalculatedMeasure) {
        await this.validateCalculated(measureProperties, measureKey);
      }

      /**
       * Count Distinct validation
       */
      if (measureProperties.measureType === AnalyticModelMeasureType.CountDistinct) {
        await this.validateCountDistinct(measureProperties, measureKey);
      }

      /**
       * Currency Conversion validation
       */
      if (measureProperties.measureType === AnalyticModelMeasureType.CurrencyConversionMeasure) {
        await this.validateCurrencyConversion(measureProperties, measureKey);
      }

      /**
       * Unit Conversion validation
       */
      const isUnitConversionFFActive = await this.isFeatureFlagActive("DWCO_MODELING_AM_UNIT_CONVERSION");
      if (
        isUnitConversionFFActive &&
        measureProperties.measureType === AnalyticModelMeasureType.UnitConversionMeasure
      ) {
        await this.validateUnitConversion(measureProperties, measureKey);
      }

      /**
       * Non-Cumulative Measure validation
       */
      if (measureProperties.measureType === AnalyticModelMeasureType.NonCumulativeMeasure) {
        await this.validateNonCumulativeMeasure(measureProperties, measureKey);
      }

      /**
       * Exception Aggregation validation
       */
      if (measureProperties.measureType !== AnalyticModelMeasureType.NonCumulativeMeasure) {
        await this.validateExceptionAggregation(measureProperties, measureKey);
      }

      // Check Name
      if (
        Object.keys(this.commonParameters.analyticModel.attributes || {}).some(
          (attributeName) => attributeName === measureKey
        )
      ) {
        this.addValidationMessage({
          message: `Measures and Attributes must not have the same technical name (${measureKey})`,
          messageKey: "validationMessageAttributeMeasureTechnicalName",
          descriptionKey: "validationDescriptionAttributeMeasureTechnicalName",
          type: ValidationMessageType.ERROR,
          parameters: [
            measureKey,
            this.commonParameters.analyticModel.attributes![measureKey].text,
            measureProperties.text,
          ],
        });
      }
    }

    /**
     * Common calculated measure properties validation in stack model
     */
    const isStackingFFActive = await this.isFeatureFlagActive("DWCO_MODELING_ANALYTIC_MODEL_STACKING");
    if (isStackingFFActive) {
      this.validateCalculatedMeasureInStackedAnalyticModel();
    }

    /**
     * Common Non-Cumulative properties validation
     */
    if (!!this.commonParameters.analyticModel.nonCumulativeSettings) {
      await this.validateCommonNonCumulativeProperties(this.commonParameters.analyticModel.nonCumulativeSettings);
    }

    Object.entries(this.commonParameters.analyticModel.measures ?? {}).forEach(async ([measureName, measure]) => {
      // Check existence of all measures
      if (measure.measureType === AnalyticModelMeasureType.FactSourceMeasure) {
        const factSource = this.commonParameters.analyticModel.sourceModel.factSources[measure.sourceKey];
        const elements = this.commonParameters.sourceModelsByName![factSource.dataEntity.key]?.elements;
        if (!elements?.hasOwnProperty(measure.key)) {
          this.addValidationMessage({
            message: `Measure "${measure.key}" does not exist in fact source "${factSource.dataEntity.key}"`,
            messageKey: "validationMessageMeasureMissingInFactSource",
            descriptionKey: "validationDescriptionMeasureMissingInFactSource",
            type: ValidationMessageType.ERROR,
            parameters: [measure.key, factSource.dataEntity.key],
            propertyPath: `/measures/${encodeURIComponent(measure.key)}`,
          });
        } else if (
          this.isFactSourceAnAnalyticModel() &&
          (elements?.[measure.key] as any)["@Consumption.hidden"] === true
        ) {
          this.addValidationMessage({
            message: `Measure "${measure.key}" is auxiliary in underlying analytic model "${factSource.dataEntity.key}"`,
            messageKey: "validationMessageMeasureIsAuxiliaryInFactSource",
            descriptionKey: "validationDescriptionMeasureIsAuxiliaryFactSource",
            type: ValidationMessageType.ERROR,
            parameters: [measure.key, factSource.dataEntity.key],
            propertyPath: `/measures/${encodeURIComponent(measure.key)}`,
          });
        } else if (this.isHidden(elements?.[measure.key])) {
          // Check hidden measures in case of analytical datasets
          this.addValidationMessage({
            message: `Measure "${measureName}" is hidden in fact source "${factSource.dataEntity.key}"`,
            messageKey: "validationMessageMeasureHiddenInFactSource",
            descriptionKey: "validationDescriptionMeasureHiddenInFactSource",
            type: ValidationMessageType.ERROR,
            parameters: [measure.text, factSource.text, measureName, factSource.dataEntity.key],
            propertyPath: `/measures/${measureName}`,
          });
        }
      } else if (
        "key" in measure &&
        measure.key &&
        measure.measureType === AnalyticModelMeasureType.RestrictedMeasure
      ) {
        Object.values(measure.elements ?? {}).forEach((element) => {
          if (
            element.operandType === AnalyticModelRestrictedMeasureOperandType.Attribute &&
            !this.commonParameters.analyticModel.attributes?.hasOwnProperty(element.key)
          ) {
            this.addValidationMessage({
              message: `Attribute "${element.key}" does not exist in analytic model`,
              messageKey: "validationMessageAttributeMissingInAnalyticModel",
              descriptionKey: "validationDescriptionAttributeMissingInAnalyticModel",
              type: ValidationMessageType.ERROR,
              parameters: [element.key, measureName],
              propertyPath: `/measures/${encodeURIComponent(measure.key)}`,
            });
          }
          if (
            element.operandType === AnalyticModelRestrictedMeasureOperandType.FilterVariable &&
            !this.commonParameters.analyticModel.variables?.hasOwnProperty(element.key)
          ) {
            this.addValidationMessage({
              message: `Filter variable "${element.key}" does not exist in analytic model`,
              messageKey: "validationMessageFilterVariableMissingInAnalyticModel",
              descriptionKey: "validationDescriptionFilterVariableMissingInAnalyticModel",
              type: ValidationMessageType.ERROR,
              parameters: [element.key, measureName],
              propertyPath: `/measures/${encodeURIComponent(measure.key)}`,
            });
          }
        });
      }
    });

    if (this.commonParameters.analyticModel.measures) {
      const cycles = QueryModelValidator.detectCyclesInModel(this.commonParameters.analyticModel);
      if (cycles.length > 0) {
        cycles.forEach((cycle) => {
          const validationMessage: ValidationMessage = {
            message: "",
            type: ValidationMessageType.ERROR,
            messageKey: "validationMessageCycleInMeasure",
            descriptionKey: "validationMessageCycleInMeasureDescription",
            parameters: [cycle.path[0], cycle.path.join(" -> ")],
            propertyPath: `/measures/${encodeURIComponent(cycle.path[0])}/expression`,
          };
          this.addValidationMessage(validationMessage);
        });
      }
    }
  }

  private async validateCalculated(
    measureProperties: IAnalyticModelCalculatedMeasure,
    measureKey: string
  ): Promise<void> {
    if ((measureProperties.formulaRaw ?? "").length > 0) {
      const xpr = await this.getExpression(measureProperties.formulaRaw!)!;
      if (xpr?.hasOwnProperty("expr")) {
        const f = async (formula: string): Promise<ICreateExpressionResult | undefined> =>
          await this.getExpression(formula);
        const convertFormulaResult = await MeasureValidationHelper.convertStringToCalculatedMeasure(
          measureProperties.formulaRaw!,
          f
        );
        this.validateExpression(measureKey, convertFormulaResult);

        if ((convertFormulaResult.errors ?? []).length === 0) {
          const getCsn = (name: string) => {
            return this.commonParameters.sourceModelsByName![name];
          };
          const result = ExprTypeValidator.checkFuncParameterTypeCompatibility(
            this.commonParameters.analyticModel,
            getCsn,
            MODEL_ELEMENT.CALCULATED_MEASURE,
            measureKey,
            xpr!.expr,
            this.commonParameters.simpleTypeMapping
          );
          if (result.length > 0) {
            result.forEach((subResult) => {
              this.addValidationMessage({
                message: `Calculated '${measureKey}' has no valid expression`,
                messageKey: subResult.errorI18n.messageId,
                descriptionKey: subResult.errorI18n.descriptionId,
                type: subResult.errorI18n.messageType ?? ValidationMessageType.WARN,
                parameters: subResult.errorI18n.params,
                propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
              });
            });
          }
        }
        this.checkIfFormulaHasError(measureKey, xpr, measureProperties, true);
      } else if (xpr?.hasOwnProperty("error")) {
        this.addValidationMessage({
          message: `Calculated measure '${measureKey}' has no valid expression`,
          messageKey: "validationMessageCalculatedInvalidExpression",
          descriptionKey: "validationDescriptionBackendGeneric",
          type: ValidationMessageType.ERROR,
          parameters: [measureKey, xpr!.error.split("Error: ")[1]],
          propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
        });
      }
    } else {
      this.addValidationMessage({
        message: `Calculated measure ${measureKey}: Expression must not be empty`,
        messageKey: "validationMessageCalculatedInvalidExpression",
        descriptionKey: "calculatedMeasureEmptyExpression",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
      });
    }
  }

  private async validateRestricted(
    measureProperties: IAnalyticModelRestrictedMeasure,
    measureKey: string
  ): Promise<void> {
    if (!measureProperties.key) {
      this.addValidationMessage({
        message: `Restricted measure '${measureKey}' has no valid source measure`,
        messageKey: "validationMessageRestrictedInvalidSourceMeasure",
        descriptionKey: "validationDescriptionRestrictedInvalidSourceMeasure",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/sourceMeasure`,
      });
    } else if (!this.commonParameters.analyticModel.measures?.hasOwnProperty(measureProperties?.key)) {
      this.addValidationMessage({
        message: `Restricted measure '${measureKey}' has non existent source measure`,
        messageKey: "validationMessageRestrictedNonExistentSourceMeasure",
        descriptionKey: "validationDescriptionRestrictedInvalidSourceMeasure",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey, measureProperties?.key],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/sourceMeasure`,
      });
    } else if (
      this.commonParameters.analyticModel.measures![measureProperties.key].measureType ===
        AnalyticModelMeasureType.FactSourceMeasure &&
      !!measureProperties.aggregation &&
      !ReverseAggregationTypeMapping.get(measureProperties.aggregation!)
    ) {
      this.addValidationMessage({
        message: `Aggregation type of restricted measure '${measureKey}' is not valid`,
        messageKey: "validationMessageRestrictedInvalidAggregation",
        descriptionKey: "validationDescriptionRestrictedInvalidAggregation",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}`,
      });
    } else if (
      this.commonParameters.analyticModel.measures![measureProperties.key].measureType !==
        AnalyticModelMeasureType.FactSourceMeasure &&
      (!!measureProperties.exceptionAggregationAttributes || !!measureProperties.exceptionAggregationType)
    ) {
      this.addValidationMessage({
        message: `Restricted measure "${measureKey}" contains unsupported exception aggregation`,
        messageKey: "validationMessageExceptionAggregationNotSupported",
        descriptionKey: "validationDescriptionExceptionAggregationNotSupported",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}`,
      });
    }

    if (measureProperties.constantSelectionType === AnalyticModelConstantSelectionType.Selected) {
      if (
        !measureProperties.constantSelectionAttributes ||
        measureProperties.constantSelectionAttributes.length === 0
      ) {
        this.addValidationMessage({
          message: `Restricted measure '${measureKey}' constant selection has no assigned dimensions`,
          messageKey: "validationMessageRestrictedConstantSelectionAttributesIncorrect",
          descriptionKey: "validationDescriptionRestrictedConstantSelectionAttributesEmpty",
          type: ValidationMessageType.ERROR,
          parameters: [measureKey],
          propertyPath: `/measures/${encodeURIComponent(measureKey)}/constantDimensions`,
        });
      } else {
        let missingDimensions = [];
        for (const constantDimension of measureProperties.constantSelectionAttributes) {
          if (!this.commonParameters.analyticModel.attributes?.hasOwnProperty(constantDimension)) {
            missingDimensions.push(constantDimension);
          }
        }
        if (missingDimensions.length > 0) {
          this.addValidationMessage({
            message: `Restricted measure '${measureKey}' constant selection contains not existing dimensions`,
            messageKey: "validationMessageRestrictedConstantSelectionAttributesIncorrect",
            descriptionKey: "validationDescriptionRestrictedMeasureSelectionAttributesMissing",
            type: ValidationMessageType.ERROR,
            parameters: [measureKey, missingDimensions.join("', '")],
            propertyPath: `/measures/${encodeURIComponent(measureKey)}/constantDimensions`,
          });
        }
      }
    }

    if (
      measureProperties.constantSelectionType &&
      measureProperties.constantSelectionType != AnalyticModelConstantSelectionType.None &&
      this.commonParameters.analyticModel.filter
    ) {
      if (measureProperties.constantSelectionType === AnalyticModelConstantSelectionType.Selected) {
        const attributesInConstantSelection: Set<string> = new Set();
        const attributesInFilter: Set<string> = new Set();
        measureProperties.constantSelectionAttributes?.forEach((attributeName) => {
          attributesInConstantSelection.add(attributeName);
        });
        const filter = this.commonParameters.analyticModel.filter;

        if (filter.elements) {
          for (const key in filter?.elements) {
            const opType = filter?.elements[key].operandType;
            if (opType === AnalyticModelFilterOperandType.Attribute) {
              attributesInFilter.add((filter?.elements[key] as IAnalyticModelFilterOperandAttribute).key);
            }
          }
        }

        const commonElements: string[] = [];
        attributesInConstantSelection.forEach((attributeName) => {
          if (attributesInFilter.has(attributeName)) {
            commonElements.push(attributeName);
          }
        });

        if (commonElements.length) {
          this.addValidationMessage({
            message: `Restricted measure '${measureKey}' has no valid expression`,
            messageKey: "validationMessageRestrictedConstantSelectionGlobalFilter",
            descriptionKey: "validationDescriptionRestrictedConstantSelectionGlobalFilter",
            type: ValidationMessageType.WARN,
            parameters: [measureKey, commonElements.join(",")],
            propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
          });
        }
      } else if (measureProperties.constantSelectionType === AnalyticModelConstantSelectionType.All) {
        this.addValidationMessage({
          message: `Restricted measure '${measureKey}' has no valid expression`,
          messageKey: "validationMessageRestrictedConstantSelectionAllGlobalFilter",
          descriptionKey: "validationDescriptionRestrictedConstantSelectionGlobalFilter",
          type: ValidationMessageType.WARN,
          parameters: [measureKey],
          propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
        });
      }
    }

    if ((measureProperties.formulaRaw ?? "").length > 0) {
      const xpr = await this.getExpression(measureProperties.formulaRaw!)!;
      if (xpr?.hasOwnProperty("expr")) {
        const errors = CsnXprToAMHelper.restrictedMeasureExpressionValidation(xpr!.expr);
        if (errors.length > 0) {
          for (let i = 0; i < errors.length; i++) {
            this.addValidationMessage({
              message: `Restricted measure '${measureKey}' has no valid expression`,
              messageKey: errors[i].messageId,
              descriptionKey: "validationDescriptionRestrictedInvalidExpression",
              type: ValidationMessageType.ERROR,
              parameters: errors[i].params,
              propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
            });
          }
          return;
        }
        const f = async (formula: string): Promise<ICreateExpressionResult | undefined> =>
          await this.getExpression(formula);
        const convertFormulaResult = await MeasureValidationHelper.convertStringToRestrictedMeasure(
          measureProperties.formulaRaw!,
          f
        );
        this.validateExpression(measureKey, convertFormulaResult);

        this.checkIfFormulaHasError(measureKey, xpr, measureProperties);
      } else if (xpr?.hasOwnProperty("error")) {
        this.addValidationMessage({
          message: `Restricted measure '${measureKey}' has no valid expression`,
          messageKey: "validationMessageRestrictedInvalidExpression",
          descriptionKey: "validationDescriptionBackendGeneric",
          type: ValidationMessageType.ERROR,
          parameters: [measureKey, xpr!.error.split("Error: ")[1]],
          propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
        });
      }
    }

    // DW101-92792: RKF with exception aggregation on Base measure with AVG aggregation => not allowed
    const keyMeasure = this.commonParameters.analyticModel.measures![measureProperties.key];
    if (
      measureProperties.exceptionAggregationType &&
      keyMeasure?.measureType === AnalyticModelMeasureType.FactSourceMeasure &&
      keyMeasure?.aggregation === AnalyticModelAggregationTypes.AVG
    ) {
      this.addValidationMessage({
        message: `Restricted measure "${measureKey}" with exception aggregation on base measure "${measureProperties.key}" with aggregation type "AVG" is not supported`,
        messageKey: "validationMessageRestrictedExceptionOnBaseWithAVG",
        descriptionKey: "validationDescriptionRestrictedExceptionOnBaseWithAVG",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey, measureProperties.key],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}`,
      });
    }
  }

  /**
   * Checks if the formula has any errors for a given measure.
   * @param measureKey - The key of the measure.
   * @param xpr - The expression result.
   * @param measureProperties - The properties of the measure.
   * @param calculatedMeasureVariablesFeatureFlag - Feature flag for using variables in calculated measure.
   */
  private checkIfFormulaHasError(
    measureKey: string,
    xpr: ICreateExpressionResult,
    measureProperties: IAnalyticModelRestrictedMeasure | IAnalyticModelCalculatedMeasure,
    calculatedMeasureVariablesFeatureFlag: boolean = false /** feature flag for using variables in calculated measure */
  ) {
    const getCsn = (name: string) => {
      return this.commonParameters.sourceModelsByName![name];
    };
    let bFormulaErrors = this.foundErrorsForMeasure(measureKey);
    if (!bFormulaErrors) {
      const comparisons = CsnXprToAMHelper.extractComparisons(xpr!.expr);
      comparisons.forEach((comparison) => {
        if (comparison.xpr.length >= 3) {
          for (let i = 1; i < comparison.xpr.length - 1; i++) {
            const operator = comparison.xpr[i];
            if (typeof operator === "string") {
              if (measureProperties.measureType === AnalyticModelMeasureType.RestrictedMeasure) {
                this.validateRestrictedMeasureExpression(operator, measureKey, comparison, i);
              }
            }
          }
          if (
            calculatedMeasureVariablesFeatureFlag &&
            measureProperties.measureType === AnalyticModelMeasureType.CalculatedMeasure
          ) {
            if (measureProperties.formula && measureProperties.formula.includes("case")) {
              return;
            }
            this.validateCalculatedMeasureExpression(measureKey, comparison, getCsn);
          }
        }
      });
    }
    bFormulaErrors = this.foundErrorsForMeasure(measureKey);
    // advanced type check for filter
    if (!bFormulaErrors && this.commonParameters.sourceModelsByName) {
      this.checkTypeCompatibility(measureProperties, measureKey, xpr, calculatedMeasureVariablesFeatureFlag);
    }
  }

  /**
   * Validates the restricted measure expression.
   * @param operator - The operator used in the expression.
   * @param measureKey - The key of the measure being validated.
   * @param comparison - The comparison object.
   * @param i - The index of the comparison in the expression.
   */
  private validateRestrictedMeasureExpression(
    operator: string,
    measureKey: string,
    comparison: CSN_XPR_COMPARISON,
    i: number
  ) {
    if (!isAllowedOperatorInExpressionEditor(operator)) {
      this.addValidationMessage({
        message: `Restricted measure '${measureKey}' has no valid expression. Operator '${operator}' not allowed`,
        messageKey: "validationMessageNoValidFilterConditionIncompatibleTypesRKF",
        descriptionKey: "validationDescriptionNoValidFilterConditionDisallowedOperator",
        type: ValidationMessageType.ERROR,
        parameters: [operator, "", "", "", measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
      });
    }
    if (isCompareOperator(operator)) {
      const leftOperand = findLeftOperand(comparison, i);
      const isDimension = isElement(leftOperand);
      // check left side
      if (!isDimension) {
        this.addValidationMessage({
          message: `Restricted measure '${measureKey}' has no valid expression. Left side operand ${
            (leftOperand as any).val ?? ""
          } of ${operator} must be a dimension`,
          messageKey: "validationMessageNoValidFilterConditionIncompatibleTypesRKF",
          descriptionKey: "validationDescriptionNoValidFilterConditionLeftSideNotDimension",
          type: ValidationMessageType.ERROR,
          parameters: [(leftOperand as any).val ?? "", operator, "", "", measureKey],
          propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
        });
      } else {
        // check right side
        const rightOperand = comparison.xpr[i + 1];
        // IN|BETWEEN|IS|LIKE
        if (operator.toUpperCase() === "BETWEEN") {
          if (comparison.xpr.length > i + 3) {
            const op2 = comparison.xpr[i + 3];
            if (isElement(rightOperand)) {
              this.addValidationMessage({
                message: `Restricted measure '${measureKey}' has no valid expression. Right side operand ${
                  (rightOperand as CDS_EXPR_REF)?.ref?.[0] ?? ""
                } of ${operator} must not be a dimension`,
                messageKey: "validationMessageNoValidFilterConditionIncompatibleTypesRKF",
                descriptionKey: "validationDescriptionNoValidFilterConditionRideSideDimension",
                type: ValidationMessageType.ERROR,
                parameters: [(rightOperand as CDS_EXPR_REF)?.ref?.[0] ?? "", operator, "", "", measureKey],
                propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
              });
            }
            if (isElement(op2)) {
              this.addValidationMessage({
                message: `Restricted measure '${measureKey}' has no valid expression. Right side operand ${
                  (op2 as CDS_EXPR_REF)?.ref?.[0] ?? ""
                } of ${operator} must not be a dimension`,
                messageKey: "validationMessageNoValidFilterConditionIncompatibleTypesRKF",
                descriptionKey: "validationDescriptionNoValidFilterConditionRideSideDimension",
                type: ValidationMessageType.ERROR,
                parameters: [(op2 as CDS_EXPR_REF)?.ref?.[0] ?? "", operator, "", "", measureKey],
                propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
              });
            }
          }
        } else if (operator.toUpperCase() === "IN") {
          if (rightOperand.hasOwnProperty("list")) {
            const dimFound = (rightOperand as CDS_EXPR_LIST).list.filter((ele) => isElement(ele));
            if (dimFound.length) {
              this.addValidationMessage({
                message: `Restricted measure '${measureKey}' has no valid expression. Right side operand ${
                  (dimFound[0] as CDS_EXPR_REF)?.ref?.[0] ?? ""
                } of ${operator} must not be a dimension`,
                messageKey: "validationMessageNoValidFilterConditionIncompatibleTypesRKF",
                descriptionKey: "validationDescriptionNoValidFilterConditionRideSideDimension",
                type: ValidationMessageType.ERROR,
                parameters: [(dimFound[0] as CDS_EXPR_REF)?.ref?.[0] ?? "", operator, "", "", measureKey],
                propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
              });
            }
          }
        } else if (
          (operator === "!=" || operator === "=") &&
          isValue(rightOperand) &&
          (rightOperand as CDS_EXPR_VAL).val === null
        ) {
          const description =
            operator === "="
              ? "validationDescriptionRestrictedMeasureEqualsNull"
              : "validationDescriptionRestrictedMeasureNotEqualsNull";
          this.addValidationMessage({
            message: `Restricted measure '${measureKey}' has no valid expression. Operator '${operator}' does not support NULL as right side operand.`,
            messageKey: "validationMessageRestrictedMeasureEqualOrNotEqualsNull",
            descriptionKey: description,
            type: ValidationMessageType.ERROR,
            parameters: [measureKey, operator],
            propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
          });
        } else {
          if (isElement(rightOperand)) {
            this.addValidationMessage({
              message: `Restricted measure '${measureKey}' has no valid expression. Right side operand ${
                (rightOperand as CDS_EXPR_REF)?.ref?.[0] ?? ""
              } of ${operator} must not be a dimension`,
              messageKey: "validationMessageNoValidFilterConditionIncompatibleTypesRKF",
              descriptionKey: "validationDescriptionNoValidFilterConditionRideSideDimension",
              type: ValidationMessageType.ERROR,
              parameters: [(rightOperand as CDS_EXPR_REF)?.ref?.[0] ?? "", operator, "", "", measureKey],
              propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
            });
          }
        }
      }
    }
  }

  /**
   * Validates the operator used in expression of a calculated measure.
   * @param expressionPart - The expression part to validate.
   * @param getCsn - A function to retrieve the CSN definition.
   * @param measureKey - The key of the measure being validated.
   */
  private validateCalculatedMeasureExpression(
    measureKey: string,
    comparison: CSN_XPR_COMPARISON,
    getCsn: (name: string) => ICsnDefinition
  ) {
    for (const part of comparison.xpr) {
      if (typeof part === "string") {
        const leftOperand = findLeftOperand(comparison, comparison.xpr.indexOf(part));
        const rightOperand = comparison.xpr[comparison.xpr.indexOf(part) + 1];
        if (leftOperand) {
          if ((leftOperand as CDS_EXPR_XPR).xpr) {
            this.validateCalculatedMeasureExpression(measureKey, leftOperand as CDS_EXPR_XPR, getCsn);
          } else {
            this.checkValidOperatorForCalculatedMeasure(leftOperand, getCsn, part, measureKey);
          }
          if (rightOperand) {
            // if the left operand is fine, we should check the right side as well
            if ((rightOperand as CDS_EXPR_XPR).xpr) {
              this.validateCalculatedMeasureExpression(measureKey, rightOperand as CDS_EXPR_XPR, getCsn);
            } else {
              this.checkValidOperatorForCalculatedMeasure(rightOperand, getCsn, part, measureKey);
            }
          }
        }
      } else if ((part as CDS_EXPR_XPR).xpr) {
        this.validateCalculatedMeasureExpression(measureKey, part as CDS_EXPR_XPR, getCsn);
      }
    }
  }

  /**
   * Checks if the operator is valid for a calculated measure.
   * @param operand - The left operand of the comparison.
   * @param getCsn - A function to retrieve the CSN definition.
   * @param operator - The operator to check.
   * @param measureKey - The key of the measure.
   */
  private checkValidOperatorForCalculatedMeasure(
    operand: CSN_XPR_COMPARISON_PART,
    getCsn: (name: string) => ICsnDefinition,
    operator: string,
    measureKey: string
  ) {
    const analyticMeasureFunctionsMap = QueryModelValidator.getAnalyticMeasureFunctionsMap();
    const actualTypeInfo = ExprTypeValidator.getTypeInfo(
      operand,
      this.commonParameters.analyticModel,
      getCsn,
      this.commonParameters.simpleTypeMapping || {},
      analyticMeasureFunctionsMap
    );
    let isOperandAllowed = true;
    if (actualTypeInfo.typeInfo) {
      if (actualTypeInfo.typeInfo.basicType === BASIC_TYPES.BINARY && operator !== "+") {
        isOperandAllowed = false;
      } else if (
        !(
          actualTypeInfo.typeInfo.basicType === BASIC_TYPES.NUMBER ||
          actualTypeInfo.typeInfo.basicType === BASIC_TYPES.BOOLEAN
        ) &&
        operator !== "+" &&
        !isAllowedOperatorInExpressionEditor(operator)
      ) {
        isOperandAllowed = false;
      }
    }
    if (!isOperandAllowed) {
      if (isElement(operand)) {
        const measure = this.commonParameters.analyticModel.measures?.[(operand as CDS_EXPR_REF).ref[0]];
        if (measure) {
          return undefined;
        }
      } else if (isFunction(operand)) {
        const signature = analyticMeasureFunctionsMap.get((operand as CDS_EXPR_FUNC).func);
        if (!signature?.returnType) {
          return undefined;
        }
      } else if (isVariable(operand) && actualTypeInfo.typeInfo.basicType === BASIC_TYPES.UNKNOWN) {
        return undefined;
      } else if (isValue(operand) && !(operand as CDS_EXPR_VAL).val) {
        return undefined;
      }
      const dataType =
        actualTypeInfo.typeInfo.csnType ?? actualTypeInfo.typeInfo.origCsnType ?? actualTypeInfo.typeInfo.basicType;
      this.addValidationMessage({
        message: `Data type '${dataType}' in calculated measure '${measureKey}' may lead to unexpected results`,
        messageKey: "validationMessageNonNumericTypes",
        descriptionKey: "validationDescriptionNoValidFilterConditionDisallowedOperator",
        type: ValidationMessageType.WARN,
        parameters: [operator, measureKey, dataType],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
      });
    }
  }

  private checkTypeCompatibility(
    measureProperties: IAnalyticModelRestrictedMeasure | IAnalyticModelCalculatedMeasure,
    measureKey: string,
    xpr: ICreateExpressionResult,
    calculatedMeasureVariablesFeatureFlag: boolean
  ) {
    const measureType =
      measureProperties.measureType === AnalyticModelMeasureType.RestrictedMeasure ? "Restricted" : "Calculated";
    if (
      measureProperties.formula &&
      measureProperties.formulaRaw &&
      measureProperties.formula.length > 0 &&
      measureProperties.formulaRaw.length > 0
    ) {
      if (measureProperties.formula.includes("case")) {
        return;
      }
      const getCsn = (name: string) => {
        return this.commonParameters.sourceModelsByName![name];
      };
      const result = ExprTypeValidator.checkTypeCompatibility(
        this.commonParameters.analyticModel,
        getCsn,
        measureProperties.measureType === AnalyticModelMeasureType.RestrictedMeasure
          ? MODEL_ELEMENT.RESTRICTED_MEASURE
          : MODEL_ELEMENT.CALCULATED_MEASURE,
        measureKey,
        xpr!.expr,
        this.commonParameters.simpleTypeMapping,
        true
      );
      if (result.length > 0) {
        result.forEach((subResult) => {
          this.addValidationMessage({
            message: `${measureType} '${measureKey}' has no valid expression`,
            messageKey: subResult.errorI18n.messageId,
            descriptionKey: subResult.errorI18n.descriptionId,
            type: ValidationMessageType.WARN,
            parameters: subResult.errorI18n.params,
            propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
          });
        });
      }
    }
  }

  private foundErrorsForMeasure(measureKey: string) {
    return this.validationMessages.some(
      (validationMessage) =>
        validationMessage.propertyPath == `/measures/${encodeURIComponent(measureKey)}/expression` &&
        validationMessage.type === ValidationMessageType.ERROR
    );
  }

  private validateExpression(measureKey: string, convertFormulaResult: IConvertFormulaResult) {
    const formulaErrors = QueryModelValidator.validateParsedFormula(
      measureKey,
      this.commonParameters.analyticModel,
      convertFormulaResult
    );
    formulaErrors.forEach((formulaError) => {
      this.addValidationMessage({
        message: formulaError.message,
        messageKey: formulaError.messageKey,
        descriptionKey: formulaError.descriptionKey,
        type: formulaError.type,
        parameters: formulaError.parameters,
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/expression`,
      });
    });
  }

  private async validateExceptionAggregation(
    measureProperties:
      | IAnalyticModelSourceMeasure
      | IAnalyticModelCalculatedMeasure
      | IAnalyticModelRestrictedMeasure
      | IAnalyticModelCountDistinctMeasure
      | IAnalyticModelCurrencyConversionMeasure
      | IAnalyticModelUnitConversionMeasure,
    measureKey: string
  ): Promise<void> {
    if (this.isOfDimensionExceptionAggregationType(measureProperties)) {
      const unbookedFeatureFlag = await this.isFeatureFlagActive("DWCO_MODELING_AM_UNBOOKED_EXCEPTION_AGGREGATION");
      if (unbookedFeatureFlag && measureProperties.measureType === AnalyticModelMeasureType.RestrictedMeasure) {
        // X_OF_DIMENSION exception aggregation type does not support stacking exception aggregations
        const sourceMeasure = this.commonParameters.analyticModel.measures?.[measureProperties.key];
        if (
          sourceMeasure &&
          sourceMeasure.measureType === AnalyticModelMeasureType.FactSourceMeasure &&
          sourceMeasure.exceptionAggregationType &&
          sourceMeasure.exceptionAggregationType !== AnalyticModelExceptionAggregationType.NONE
        ) {
          this.addValidationMessage({
            message: `Source measure '${sourceMeasure.text}' of restricted measure '${measureKey}' has exception aggregation`,
            messageKey: "validationExceptionAggregationSourceHasExceptionAggregation",
            descriptionKey: "validationExceptionAggregationSourceHasExceptionAggregationDescription",
            type: ValidationMessageType.ERROR,
            parameters: [
              sourceMeasure.text,
              (
                ReverseExceptionAggregationTypeMapping.get(
                  measureProperties.exceptionAggregationType as AnalyticModelExceptionAggregationType
                ) as string
              ).replace(/_/g, " "),
            ],
            propertyPath: `/measures/${encodeURIComponent(measureKey)}/exceptionAggregationType`,
          });
        }
      }
    }
    if (this.isAttributeMissedForExceptionAggregation(measureProperties)) {
      const descriptionKey = this.isOfDimensionExceptionAggregationType(measureProperties)
        ? "validationDescriptionOfDimensionExceptionAggregationAttributeMissing"
        : "validationDescriptionExceptionAggregationAttributeMissing";
      this.addValidationMessage({
        message: `Exception aggregation of '${measureKey}' has no assigned dimension(s)`,
        messageKey: "validationExceptionAggregationAttributeMissing",
        descriptionKey: descriptionKey,
        type: ValidationMessageType.ERROR,
        parameters: [measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/exceptionAggregationDimensions`,
      });
    } else if (!!measureProperties.exceptionAggregationAttributes) {
      let missingDimensions = [];
      for (const exceptionAggregationDimension of measureProperties.exceptionAggregationAttributes) {
        if (!this.commonParameters.analyticModel.attributes?.hasOwnProperty(exceptionAggregationDimension)) {
          missingDimensions.push(exceptionAggregationDimension);
        }
      }
      if (missingDimensions.length > 0) {
        this.addValidationMessage({
          message: `Restricted measure '${measureKey}' exception aggregation selection contains not existing dimensions`,
          messageKey: "validationExceptionAggregationAttributeMissing",
          descriptionKey: "validationDescriptionRestrictedMeasureSelectionAttributesMissing",
          type: ValidationMessageType.ERROR,
          parameters: [measureKey, missingDimensions.join("', '")],
          propertyPath: `/measures/${encodeURIComponent(measureKey)}/exceptionAggregationDimensions`,
        });
        return;
      }
      if (this.isOfDimensionExceptionAggregationType(measureProperties)) {
        const unbookedFeatureFlag = await this.isFeatureFlagActive("DWCO_MODELING_AM_UNBOOKED_EXCEPTION_AGGREGATION");
        if (
          unbookedFeatureFlag &&
          (measureProperties.measureType === AnalyticModelMeasureType.FactSourceMeasure ||
            measureProperties.measureType === AnalyticModelMeasureType.RestrictedMeasure)
        ) {
          if (measureProperties.exceptionAggregationAttributes.length > 1) {
            this.addValidationMessage({
              message: `Dimension selection of exception aggregation of '${measureKey}' has more than one dimension`,
              messageKey: "validationExceptionAggregationMoreThanOneDimension",
              descriptionKey: "validationExceptionAggregationMoreThanOneDimensionDescription",
              type: ValidationMessageType.ERROR,
              parameters: [
                (
                  ReverseExceptionAggregationTypeMapping.get(
                    measureProperties.exceptionAggregationType as AnalyticModelExceptionAggregationType
                  ) as string
                ).replace(/_/g, " "),
              ],
              propertyPath: `/measures/${encodeURIComponent(measureKey)}/exceptionAggregationDimensions`,
            });
            return;
          }
          const selectedDimension = measureProperties.exceptionAggregationAttributes[0];
          const attribute = this.commonParameters.analyticModel.attributes?.[
            selectedDimension
          ] as IAnalyticModelAttribute;

          const { dataEntityKey, attributeSourceKey } = this.getAttributeSourceInformation(
            this.commonParameters.analyticModel,
            attribute
          );
          if (!dataEntityKey || !attributeSourceKey) {
            return;
          }
          const sourceElement =
            this.commonParameters.sourceModelsByName?.[dataEntityKey]?.elements?.[attributeSourceKey];
          const sourceElementType = sourceElement?.type;
          if (!sourceElementType || sourceElementType !== "cds.Date") {
            this.addValidationMessage({
              message: `Dimension selection of exception aggregation of '${measureKey}' should be type of date`,
              messageKey: "validationExceptionAggregationIncorrectType",
              descriptionKey: "validationExceptionAggregationIncorrectTypeDescription",
              type: ValidationMessageType.WARN,
              parameters: [
                (
                  ReverseExceptionAggregationTypeMapping.get(
                    measureProperties.exceptionAggregationType as AnalyticModelExceptionAggregationType
                  ) as string
                ).replace(/_/g, " "),
                selectedDimension,
              ],
              propertyPath: `/measures/${encodeURIComponent(measureKey)}/exceptionAggregationDimensions`,
            });
            return;
          }
        } else {
          const selectedExceptionAggregationType = (
            ReverseExceptionAggregationTypeMapping.get(
              measureProperties.exceptionAggregationType as AnalyticModelExceptionAggregationType
            ) as string
          ).replace(/_/g, " ");
          const measureType = measureProperties.measureType;
          this.addValidationMessage({
            message: `Exception aggregation type '${selectedExceptionAggregationType}' of '${measureKey}' is invalid`,
            messageKey: "validationExceptionAggregationUnsupportedType",
            descriptionKey: "validationExceptionAggregationUnsupportedTypeDescription",
            type: ValidationMessageType.ERROR,
            parameters: [selectedExceptionAggregationType, measureType],
            propertyPath: `/measures/${encodeURIComponent(measureKey)}/exceptionAggregationDimensions`,
          });
        }
      }
    }
  }

  /**
   * "OfDimensions" are LAST_OF_DIMENSION, FIRST_OF_DIMENSION, AVERAGE_OF_DIMENSION, which considers unbooked values during calculation.
   * Those exception aggregation types have unique requirements for the dimension selection.
   * i.e.: null values
   */
  private isOfDimensionExceptionAggregationType(measureProperties: IAnalyticModelMeasure): boolean {
    return (
      measureProperties.exceptionAggregationType === AnalyticModelExceptionAggregationType.LAST_OF_DIMENSION ||
      measureProperties.exceptionAggregationType === AnalyticModelExceptionAggregationType.FIRST_OF_DIMENSION ||
      measureProperties.exceptionAggregationType === AnalyticModelExceptionAggregationType.AVERAGE_OF_DIMENSION
    );
  }
  private async validateCurrencyConversion(
    measureProperties: IAnalyticModelCurrencyConversionMeasure,
    measureKey: string
  ): Promise<void> {
    let missingDimensions: string[] = [];
    let missingVariables: string[] = [];
    if (
      this.conversionPropertyUndefinedOrEmpty(
        this.getCurrencyValueByCurrencyType(
          measureProperties.targetCurrency,
          measureProperties.targetCurrencyType,
          missingDimensions,
          missingVariables
        ).currencyValue
      )
    ) {
      this.addValidationMessage({
        message: `Currency conversion measure '${measureKey}' is not valid`,
        messageKey: "VAL_CC_INVALID",
        descriptionKey: "VAL_CC_EMPTY_TARGET_CURRENCY",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/targetCurrency`,
      });
    }
    if (
      this.conversionPropertyUndefinedOrEmpty(
        this.getCurrencyValueByCurrencyType(
          measureProperties.conversionType,
          measureProperties.conversionTypeType,
          missingDimensions,
          missingVariables
        ).currencyValue
      )
    ) {
      this.addValidationMessage({
        message: `Currency conversion measure '${measureKey}' is not valid`,
        messageKey: "VAL_CC_INVALID",
        descriptionKey: "VAL_CC_EMPTY_CONVERSION_TYPE",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/conversionType`,
      });
    }
    if (
      this.conversionPropertyUndefinedOrEmpty(
        this.getCurrencyValueByCurrencyType(
          measureProperties.referenceDate,
          measureProperties.referenceDateType,
          missingDimensions,
          missingVariables
        ).currencyValue
      )
    ) {
      this.addValidationMessage({
        message: `Currency conversion measure '${measureKey}' is not valid`,
        messageKey: "VAL_CC_INVALID",
        descriptionKey: "VAL_CC_EMPTY_REFERENCE_DATE",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/referenceDate`,
      });
    }
    await this.validateConversionMeasureReferences(measureProperties, measureKey, missingDimensions, missingVariables);
  }

  private async validateConversionMeasureReferences(
    measureProperties: IAnalyticModelCurrencyConversionMeasure | IAnalyticModelUnitConversionMeasure,
    measureKey: string,
    missingDimensions: string[],
    missingVariables: string[],
    isCurrency: boolean = true
  ): Promise<void> {
    const conversionType = isCurrency
      ? CONVERSION_TYPES_USED_IN_VALIDATION.CURRENCY
      : CONVERSION_TYPES_USED_IN_VALIDATION.UNIT;
    if (!measureProperties.key) {
      this.addValidationMessage({
        message: `${conversionType} conversion measure '${measureKey}' has no valid source measure`,
        messageKey: isCurrency
          ? "validationMessageCurrencyConversionInvalidSourceMeasure"
          : "validationMessageUnitConversionInvalidSourceMeasure",
        descriptionKey: isCurrency
          ? "validationDescriptionCurrencyConversionInvalidSourceMeasure"
          : "validationDescriptionUnitConversionInvalidSourceMeasure",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/sourceMeasure`,
      });
    } else if (!this.commonParameters.analyticModel.measures?.hasOwnProperty(measureProperties?.key)) {
      this.addValidationMessage({
        message: `${conversionType} conversion measure '${measureKey}' has non existent source measure`,
        messageKey: isCurrency
          ? "validationMessageCurrencyConversionNonExistentSourceMeasure"
          : "validationMessageUnitConversionNonExistentSourceMeasure",
        descriptionKey: isCurrency
          ? "validationDescriptionCurrencyConversionInvalidSourceMeasure"
          : "validationDescriptionUnitConversionInvalidSourceMeasure",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey, measureProperties?.key],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/sourceMeasure`,
      });
    }
    if (missingDimensions.length > 0) {
      const additionalMeasurePaths: string[] = [];
      missingDimensions.forEach((res: any) => {
        additionalMeasurePaths.push(`/measures/${encodeURIComponent(measureKey)}/${encodeURIComponent(res.type)}`);
      });
      this.addValidationMessage({
        message: `${conversionType} conversion measure '${measureKey}' contains not existing target dimensions`,
        messageKey: isCurrency
          ? "validationMessageCurrencyConversionTargetDimensionsIncorrect"
          : "validationMessageUnitConversionTargetDimensionsIncorrect",
        descriptionKey: isCurrency
          ? "validationDescriptionCurrencyConversionTargetDimensionsMissing"
          : "validationDescriptionUnitConversionTargetDimensionMissing",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey, missingDimensions.map((dim: any) => dim.value).join("', '")],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}`,
        additionalPropertyPath: additionalMeasurePaths,
      });
    }
    if (missingVariables.length > 0) {
      const additionalMeasurePaths: string[] = [];
      missingVariables.forEach((res: any) => {
        additionalMeasurePaths.push(`/measures/${encodeURIComponent(measureKey)}/${encodeURIComponent(res.type)}`);
      });
      this.addValidationMessage({
        message: `${conversionType} conversion measure '${measureKey}' contains non-existent variables`,
        messageKey: isCurrency
          ? "validationMessageCurrencyConversionVariablesIncorrect"
          : "validationMessageUnitConversionVariableIncorrect",
        descriptionKey: isCurrency
          ? "validationDescriptionCurrencyConversionTargetDimensionsMissing"
          : "validationDescriptionUnitConversionTargetDimensionMissing",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey, missingVariables.map((dim: any) => dim.value).join("', '")],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}`,
        additionalPropertyPath: additionalMeasurePaths,
      });
    }
  }

  private async validateUnitConversion(
    measureProperties: IAnalyticModelUnitConversionMeasure,
    measureKey: string
  ): Promise<void> {
    let missingDimensions: string[] = [];
    let missingVariables: string[] = [];
    if (
      this.conversionPropertyUndefinedOrEmpty(
        this.getCurrencyValueByCurrencyType(
          measureProperties.targetUnit,
          measureProperties.targetUnitType,
          missingDimensions,
          missingVariables
        ).currencyValue
      )
    ) {
      this.addValidationMessage({
        message: `Unit conversion measure '${measureKey}' is not valid`,
        messageKey: "VAL_UC_INVALID",
        descriptionKey: "VAL_UC_EMPTY_TARGET_UNIT",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/targetUnit`,
      });
    }
    await this.validateConversionMeasureReferences(
      measureProperties,
      measureKey,
      missingDimensions,
      missingVariables,
      false
    );

    if (
      measureProperties.targetUnitType === AnalyticModelTargetUnitType.variable &&
      "key" in measureProperties.targetUnit
    ) {
      const variable = this.commonParameters.analyticModel.variables?.[measureProperties.targetUnit.key];
      if (variable && variable.parameterType !== AnalyticModelParameterType.Input) {
        this.addValidationMessage({
          message: `Unit conversion measure '${measureKey}' is not valid`,
          messageKey: "VAL_UC_INVALID",
          descriptionKey: "VAL_UC_TARGET_UNIT_WRONG_VARIABLE_TYPE",
          type: ValidationMessageType.ERROR,
          parameters: [measureKey],
          propertyPath: `/measures/${encodeURIComponent(measureKey)}/targetUnit`,
        });
      }
    }
  }

  private async validateCountDistinct(
    measureProperties: IAnalyticModelCountDistinctMeasure,
    measureKey: string
  ): Promise<void> {
    if (!measureProperties.countDistinctAttributes || measureProperties.countDistinctAttributes.length === 0) {
      this.addValidationMessage({
        message: `Count Distinct measure '${measureKey}' has no assigned attributes`,
        messageKey: "validationMessageCountDistinctInvalidExpression",
        descriptionKey: "validationDescriptionCountDistinctInvalidExpression",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/exceptionAggregationDimensions`,
      });
    } else {
      measureProperties.countDistinctAttributes.forEach((countDistinctAttribute) => {
        if (this.commonParameters.analyticModel.attributes?.[countDistinctAttribute] === undefined) {
          this.addValidationMessage({
            message: `Count Distinct measure '${measureKey}' contains a non existing dimension '${countDistinctAttribute}'`,
            messageKey: "validationMessageCountDistinctInvalidAttribute",
            descriptionKey: "validationDescriptionCountDistinctInvalidAttribute",
            type: ValidationMessageType.ERROR,
            parameters: [measureKey, countDistinctAttribute],
            propertyPath: `/measures/${encodeURIComponent(measureKey)}/exceptionAggregationDimensions`,
          });
        }
      });
    }
  }

  private async validateNonCumulativeMeasure(
    measureProperties: IAnalyticModelNonCumulativeMeasure,
    measureKey: string
  ): Promise<void> {
    if (!!measureProperties && !this.commonParameters.analyticModel.nonCumulativeSettings) {
      this.addValidationMessage({
        message: `Non-Cumulative measure '${measureKey}' has no non-cumulative measure settings`,
        messageKey: "validationMessageNonCumulativeMissingSettings",
        descriptionKey: "validationDescriptionNonCumulativeMissingSettings",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}`,
        additionalPropertyPath: [
          "/nonCumulativeSettings/recordTypeAttributeKey",
          "/nonCumulativeSettings/timeDimensionKey",
        ],
      });
    }
    if (!measureProperties.key) {
      this.addValidationMessage({
        message: `Non-cumulative measure '${measureKey}' has no valid source measure`,
        messageKey: "validationMessageNonCumulativeInvalidSourceMeasure",
        descriptionKey: "validationDescriptionNonCumulatedInvalidSourceMeasure",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/sourceMeasure`,
      });
    } else if (!this.commonParameters.analyticModel.measures?.hasOwnProperty(measureProperties?.key)) {
      this.addValidationMessage({
        message: `Non-cumulative measure '${measureKey}' has non existent source measure`,
        messageKey: "validationMessageNonCumulatedNonExistentSourceMeasure",
        descriptionKey: "validationDescriptionNonCumulatedInvalidSourceMeasure",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey, measureProperties?.key],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}/sourceMeasure`,
      });
    } else if (
      !!measureProperties.exceptionAggregationNcumType &&
      !ReverseExceptionAggregationNcumTypeMapping.get(measureProperties.exceptionAggregationNcumType!)
    ) {
      this.addValidationMessage({
        message: `Non-cumulative measure "${measureKey}" contains unsupported exception aggregation`,
        messageKey: "validationMessageExceptionAggregationNcumNotSupported",
        descriptionKey: "validationDescriptionExceptionAggregationNcumNotSupported",
        type: ValidationMessageType.ERROR,
        parameters: [measureKey],
        propertyPath: `/measures/${encodeURIComponent(measureKey)}`,
      });
    }
  }

  /**
   * Validates stacked calculated measures expression with dimensions
   * for each inherited calculated measure referring to dimensions attributes,
   * check if the attribute is explicitly added as dimension or calculated measure in the base model has the used dimensions added to the exception aggregation
   * Otherwise raise the error.
   */
  private validateCalculatedMeasureInStackedAnalyticModel() {
    const factSources = this.commonParameters.analyticModel.sourceModel?.factSources ?? {};
    const factSourceKeys = Object.keys(factSources);
    if (factSourceKeys.length === 0) {
      return undefined;
    }
    const factSourceAlias = factSourceKeys[0];
    const sourceModelName = factSources[factSourceAlias].dataEntity.key;
    const analyticModelCsn = this.commonParameters?.sourceModelsByName?.[sourceModelName];
    const bAnalyticModel = (analyticModelCsn as any)?.["@DataWarehouse.editorType"]?.["#"] === "DWCQueryModelEditor";
    if (bAnalyticModel) {
      const csnElements = analyticModelCsn?.elements;
      Object.keys(csnElements ?? {}).forEach((calKey) => {
        let refs: string[] = [];
        let modelMeasureKey;
        const measures = this.commonParameters.analyticModel.measures ?? {};
        const isMeasureInModel = Object.entries(measures).some(([measureKey, measure]) => {
          modelMeasureKey = measureKey;
          return measure?.measureType === AnalyticModelMeasureType.FactSourceMeasure && measure?.key === calKey;
        });

        if (!isMeasureInModel) {
          return;
        }
        const foundSourceColumn = this.findColumnInQuery(calKey, analyticModelCsn?.query!);
        if (!foundSourceColumn) {
          return;
        }
        const csnElement = csnElements?.[calKey];
        // check if the calculated measure is include in the analytic model by checking the measure type and key
        if (csnElement && this.isCalculatedMeasure(csnElement, foundSourceColumn)) {
          const callback = (type: XPR_PARTS, xpr: any): void => {
            if (type === XPR_PARTS.REF) {
              const elementName = xpr.ref[xpr.ref.length - 1];
              refs.push(elementName);
            }
          };
          CsnXprToAMHelper.traverse(foundSourceColumn as CDS_EXPR, callback);
        }

        refs.forEach((elementName) => {
          const element = csnElements?.[elementName];
          const currentAttributeKey = VariablePathHelper.getMatchingAnalyticModelAttribute(
            this.commonParameters.analyticModel,
            analyticModelCsn!,
            elementName,
            factSourceAlias
          );
          const isCalculatedMeasureHasError =
            element &&
            this.isAttribute(element) &&
            !this.hasMeasureExceptionAggregation(csnElements?.[calKey], elementName) &&
            !currentAttributeKey;
          if (isCalculatedMeasureHasError) {
            this.addValidationMessage({
              message: `Calculated measure '${modelMeasureKey!}' has no valid expression`,
              messageKey: "validationMessageStackCalculatedMeasureUseDimension",
              descriptionKey: "validationDescriptionStackCalculatedMeasureUseDimension",
              type: ValidationMessageType.ERROR,
              parameters: [modelMeasureKey!, elementName],
              propertyPath: `/measures/${encodeURIComponent(modelMeasureKey!)}/expression`,
            });
          }
        });
      });
    }
  }

  private async validateCommonNonCumulativeProperties(
    nonCumulativeSettings: IAnalyticModelNonCumulativeSettings
  ): Promise<void> {
    if (
      !nonCumulativeSettings.recordTypeAttributeKey ||
      !this.commonParameters.analyticModel.attributes?.hasOwnProperty(nonCumulativeSettings.recordTypeAttributeKey)
    ) {
      this.addValidationMessage({
        message: `Non-cumulative settings record type field invalid`,
        messageKey: "validationMessageNonCumulativeRecordTypeInvalid",
        descriptionKey: "validationDescriptionNonCumulativeRecordTypeInvalid",
        type: ValidationMessageType.ERROR,
        parameters: [],
        propertyPath: `/nonCumulativeSettings/recordTypeAttributeKey`,
      });
    } else {
      Object.values(this.commonParameters.sourceModelsByName || {}).forEach((source) => {
        if (
          (source["@ObjectModel.modelingPattern"]?.["#"] ?? "") === "ANALYTICAL_FACT" &&
          !!source.elements?.[nonCumulativeSettings.recordTypeAttributeKey] &&
          source.elements?.[nonCumulativeSettings.recordTypeAttributeKey].type !== "cds.Integer" &&
          source.elements?.[nonCumulativeSettings.recordTypeAttributeKey].type !== "cds.Integer64"
        ) {
          this.addValidationMessage({
            message: `Non-cumulative settings record type field incorrect data type`,
            messageKey: "validationMessageNonCumulativeRecordTypeDataType",
            descriptionKey: "validationDescriptionNonCumulativeRecordTypeDataType",
            type: ValidationMessageType.ERROR,
            parameters: [],
            propertyPath: `/nonCumulativeSettings/recordTypeAttributeKey`,
          });
        }
      });
    }

    const timeDimensionRepresentativeAttributeName = this.getDimensionName(nonCumulativeSettings.timeDimensionKey);
    if (
      !nonCumulativeSettings.timeDimensionKey ||
      !this.commonParameters.analyticModel.attributes?.hasOwnProperty(timeDimensionRepresentativeAttributeName)
    ) {
      this.addValidationMessage({
        message: `Non-cumulative settings time dimension field invalid`,
        messageKey: "validationMessageNonCumulativeTimeDimensionInvalid",
        descriptionKey: "validationDescriptionNonCumulativeTimeDimensionInvalid",
        type: ValidationMessageType.ERROR,
        parameters: [],
        propertyPath: `/nonCumulativeSettings/timeDimensionKey`,
      });
    } else {
      const attribute = this.commonParameters.analyticModel.attributes![timeDimensionRepresentativeAttributeName];
      const attributeSourceInformation = this.getAttributeSourceInformation(
        this.commonParameters.analyticModel,
        attribute
      );
      if (attributeSourceInformation.dataEntityKey && attributeSourceInformation.attributeSourceKey) {
        const source = this.commonParameters.sourceModelsByName![attributeSourceInformation.dataEntityKey];
        const attributeName = attributeSourceInformation.attributeSourceKey;
        if (source.elements?.[attributeName] && source.elements?.[attributeName].type !== "cds.Date") {
          this.addValidationMessage({
            message: `Non-cumulative settings time dimension incorrect data type`,
            messageKey: "validationMessageNonCumulativeTimeDimensionDataType",
            descriptionKey: "validationDescriptionNonCumulativeTimeDimensionDataType",
            type: ValidationMessageType.ERROR,
            parameters: [],
            propertyPath: `/nonCumulativeSettings/timeDimensionKey`,
          });
        }
      }
    }
    const reportingMinStartTime = nonCumulativeSettings.reportingMinStartTime?.value as string;
    const reportingMaxEndTime = nonCumulativeSettings.reportingMaxEndTime?.value as string;
    if (!!reportingMinStartTime && !this.validateDate(reportingMinStartTime)) {
      this.addValidationMessage({
        message: `Non-cumulative settings reporting start time invalid`,
        messageKey: "validationMessageNonCumulativeStartTimeInvalid",
        descriptionKey: "validationDescriptionNonCumulativeStartTimeInvalid",
        type: ValidationMessageType.ERROR,
        parameters: [],
        propertyPath: `/nonCumulativeSettings/reportingMinStartTime/value`,
      });
    } else if (!!reportingMaxEndTime && !this.validateDate(reportingMaxEndTime)) {
      this.addValidationMessage({
        message: `Non-cumulative settings reporting end time invalid`,
        messageKey: "validationMessageNonCumulativeEndTimeInvalid",
        descriptionKey: "validationDescriptionNonCumulativeEndTimeInvalid",
        type: ValidationMessageType.ERROR,
        parameters: [],
        propertyPath: `/nonCumulativeSettings/reportingMaxEndTime/value`,
      });
    } else if (!!reportingMinStartTime && !reportingMaxEndTime) {
      this.addValidationMessage({
        message: `Non-cumulative settings contain a reporting start time without a reporting end time`,
        messageKey: "validationMessageNonCumulativeStartTimeWithoutEndTime",
        descriptionKey: "validationDescriptionNonCumulativeStartTimeWithoutEndTime",
        type: ValidationMessageType.ERROR,
        parameters: [],
        propertyPath: `/nonCumulativeSettings/reportingMaxEndTime/value`,
      });
    } else if (!!reportingMaxEndTime && !reportingMinStartTime) {
      this.addValidationMessage({
        message: `Non-cumulative settings contain a reporting end time without a reporting start time`,
        messageKey: "validationMessageNonCumulativeEndTimeWithoutStartTime",
        descriptionKey: "validationDescriptionNonCumulativeEndTimeWithoutStartTime",
        type: ValidationMessageType.ERROR,
        parameters: [],
        propertyPath: `/nonCumulativeSettings/reportingMinStartTime/value`,
      });
    } else if (reportingMaxEndTime < reportingMinStartTime) {
      this.addValidationMessage({
        message: `Non-cumulative settings reporting end time is earlier than the reporting start time`,
        messageKey: "validationMessageNonCumulativeEndTimeEarlierStartTime",
        descriptionKey: "validationDescriptionNonCumulativeEndTimeEarlierStartTime",
        type: ValidationMessageType.ERROR,
        parameters: [],
        propertyPath: `/nonCumulativeSettings/reportingMaxEndTime/value`,
      });
    }
  }

  // **************

  private conversionPropertyUndefinedOrEmpty(property: any): boolean {
    if (!property || (Array.isArray(property) && !property.length)) {
      return true;
    }
    return false;
  }

  private getCurrencyValueByCurrencyType(
    conversion: any,
    conversionType: any,
    missingDimensions: any[],
    missingVariables: any[]
  ) {
    let currencyUnitValue = "";
    if (conversionType.includes("AnalyticModelTargetCurrencyType")) {
      switch (conversionType) {
        case AnalyticModelTargetCurrencyType.constantValue:
          currencyUnitValue = (conversion as IAnalyticModelConstantValue).value;
          break;
        case AnalyticModelTargetCurrencyType.attribute:
          currencyUnitValue = (conversion as IAnalyticModelAttributeKey).key;
          if (!this.commonParameters.analyticModel.attributes?.hasOwnProperty(currencyUnitValue)) {
            missingDimensions.push({
              value: currencyUnitValue,
              type: "targetCurrency",
            });
          }
          break;
        case AnalyticModelTargetCurrencyType.variable:
          currencyUnitValue = (conversion as IAnalyticModelVariableKey).key;
          if (!this.commonParameters.analyticModel.variables?.hasOwnProperty(currencyUnitValue)) {
            missingVariables.push({
              value: currencyUnitValue,
              type: "targetCurrency",
            });
          }
          break;
      }
    } else if (conversionType.includes("AnalyticModelReferenceDateType")) {
      switch (conversionType) {
        case AnalyticModelReferenceDateType.constantValue:
          currencyUnitValue = (conversion as IAnalyticModelConstantValue).value;
          break;
        case AnalyticModelReferenceDateType.attribute:
          currencyUnitValue = (conversion as IAnalyticModelAttributeKey).key;
          if (
            !!currencyUnitValue &&
            !this.commonParameters.analyticModel.attributes?.hasOwnProperty(currencyUnitValue)
          ) {
            missingDimensions.push({
              value: currencyUnitValue,
              type: "referenceDate",
            });
          }
          break;
        case AnalyticModelReferenceDateType.variable:
          currencyUnitValue = (conversion as IAnalyticModelVariableKey).key;
          if (!this.commonParameters.analyticModel.variables?.hasOwnProperty(currencyUnitValue)) {
            missingVariables.push({
              value: currencyUnitValue,
              type: "referenceDate",
            });
          }
          break;
        case AnalyticModelReferenceDateType.sqlFunction:
          currencyUnitValue = (conversion as IAnalyticModelSqlFunctionName).functionName;
          break;
      }
    } else if (conversionType.includes("AnalyticModelConversionTypeType")) {
      switch (conversionType) {
        case AnalyticModelConversionTypeType.constantValue:
          currencyUnitValue = (conversion as IAnalyticModelConstantValue).value;
          break;
        case AnalyticModelConversionTypeType.attribute:
          currencyUnitValue = (conversion as IAnalyticModelAttributeKey).key;
          if (!this.commonParameters.analyticModel.attributes?.hasOwnProperty(currencyUnitValue)) {
            missingDimensions.push({
              value: currencyUnitValue,
              type: "conversionType",
            });
          }
          break;
        case AnalyticModelConversionTypeType.variable:
          currencyUnitValue = (conversion as IAnalyticModelVariableKey).key;
          if (!this.commonParameters.analyticModel.variables?.hasOwnProperty(currencyUnitValue)) {
            missingVariables.push({
              value: currencyUnitValue,
              type: "conversionType",
            });
          }
          break;
      }
    } else if (conversionType.includes("AnalyticModelTargetUnitType")) {
      switch (conversionType) {
        case AnalyticModelTargetUnitType.constantValue:
          currencyUnitValue = (conversion as IAnalyticModelConstantValue).value;
          break;
        case AnalyticModelTargetUnitType.attribute:
          currencyUnitValue = (conversion as IAnalyticModelAttributeKey).key;
          if (!this.commonParameters.analyticModel.attributes?.hasOwnProperty(currencyUnitValue)) {
            missingDimensions.push({
              value: currencyUnitValue,
              type: "targetUnit",
            });
          }
          break;
        case AnalyticModelTargetUnitType.variable:
          currencyUnitValue = (conversion as IAnalyticModelVariableKey).key;
          if (!this.commonParameters.analyticModel.variables?.hasOwnProperty(currencyUnitValue)) {
            missingVariables.push({
              value: currencyUnitValue,
              type: "targetUnit",
            });
          }
          break;
      }
    }
    return { currencyValue: currencyUnitValue, missingDimensions: missingDimensions };
  }

  private isAttributeMissedForExceptionAggregation(measureProperties: any): boolean {
    switch (measureProperties.measureType) {
      case AnalyticModelMeasureType.CalculatedMeasure:
      case AnalyticModelMeasureType.RestrictedMeasure:
      case AnalyticModelMeasureType.FactSourceMeasure:
        return this.isAttributeMissed(measureProperties);
      default:
        return false;
    }
  }

  private isAttributeMissed(measureProperties: any): boolean {
    if (
      measureProperties.exceptionAggregationType &&
      measureProperties.exceptionAggregationType !== AnalyticModelExceptionAggregationType.NONE &&
      (!measureProperties.exceptionAggregationAttributes ||
        measureProperties.exceptionAggregationAttributes.length === 0)
    ) {
      return true;
    }
    return false;
  }

  private getDimensionName(sourceKey: string): string {
    const dimensionSource = this.commonParameters.analyticModel.sourceModel.dimensionSources?.[
      sourceKey
    ] as IAnalyticModelDimensionSource;
    if (!dimensionSource || !dimensionSource.associationContexts) {
      return "";
    }
    if (this.commonParameters.analyticModel.supportedCapabilities._DWC_AM_EDITABLE_DIMENSION_NAMES) {
      const representativeKey = Object.keys(this.commonParameters.analyticModel.attributes ?? {}).find((key) => {
        const attribute = this.commonParameters.analyticModel.attributes![key];
        return attribute.usedForDimensionSourceKey === sourceKey && !attribute.duplicated;
      });
      return representativeKey ?? "";
    }

    for (const context of dimensionSource.associationContexts) {
      const sourceEntityName =
        this.commonParameters.analyticModel.sourceModel.factSources[context.sourceKey]?.dataEntity?.key;
      if (!!sourceEntityName) {
        const association = this.commonParameters.sourceModelsByName?.[sourceEntityName]?.elements?.[
          context.associationSteps[0]
        ] as any;
        if (!!association) {
          const timeDimMappingTargetField = association.on[0].ref[0];
          const timeDimMappingAttributeInAM = Object.entries(this.commonParameters.analyticModel.attributes ?? {}).find(
            ([key, value]) =>
              value.attributeType === AnalyticModelAttributeType.FactSourceAttribute &&
              context.sourceKey in value.attributeMapping &&
              value.attributeMapping[context.sourceKey].key === timeDimMappingTargetField
          );
          if (!!timeDimMappingAttributeInAM) {
            return timeDimMappingAttributeInAM[0];
          }
        }
      }
    }
    return "";
  }
}
