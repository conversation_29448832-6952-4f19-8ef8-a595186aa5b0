/** @format */

import {
  FTCProvisioningParameters,
  Measure as FlexTenantMeasure,
  Hyperscaler,
  IBaseValue,
  IPerformanceClassRangeMap,
  LicenseParameters,
  Regions,
  dwcMetrics,
} from "@sap/dwc-flexible-tenant-sizing";
import { ITenantLicenses, TenantClassification } from "@sap/dwc-tms-provider";
import { IExtraOptions, INewLoadedMetaData } from "./MetadataInterfaces";

export const FtcParameters = { ...LicenseParameters, ...FTCProvisioningParameters };

export enum ScalingErrors {
  OutOfRange = "OutOfRange",
  StorageDownsize = "StorageDownsize",
  DataLakeDownsize = "DataLakeDownsize",
  RequestConflict = "RequestConflict",
  BwBridgeMinimumLicense = "BwBridgeMinimumLicense",
  DataLakeMinimumLicense = "DataLakeMinimumLicense",
  LargeSystemsMinimumLicense = "LargeSystemsMinimumLicense",
  LargeSystemsIncompleteConfiguration = "LargeSystemsIncompleteConfiguration",
  LargeSystemsDisabledRegion = "LargeSystemsDisabledRegion",
  EcnMinimumLicense = "EcnMinimumLicense",
  MemoryBelowSpacesAllocation = "MemoryBelowSpacesAllocation",
  DataLakeConnectedToSpace = "DataLakeConnectedToSpace",
  StorageBelowMemoryRequirement = "StorageBelowMemoryRequirement",
  MemoryBelowMinimumDownsize = "MemoryBelowMinimumDownsize",
  HanaScriptServerMinimumLicense = "HanaScriptServerMinimumLicense",
  HanaMultiAZMinimumLicense = "HanaMultiAZMinimumLicense",
  BwBridgeDownsizeUpsizeEnabled = "BwBridgeDownsizeUpsizeEnabled",
  BwBridgeMinStorageConfig = "BwBridgeMinStorageConfig",
  HighComputeUS21Exception = "HighComputeUS21Exception",
}

export enum ScalingWarnings {
  AboveRatio = "AboveRatio",
  BelowRatio = "BelowRatio",
  OverUsage = "OverUsage",
  CustomerHanaFail = "CustomerHanaFail",
  DataLakeFail = "DataLakeFail",
  BwBridgeFail = "BwBridgeFail",
  CustomerHanaLessThanSixHoursUpdate = "CustomerHanaLessThanSixHoursUpdate",
  CustomerHanaLessThanTwentyFourHoursUpdate = "CustomerHanaLessThanTwentyFourHoursUpdate",
}

export enum ConfigurationErrorCode {
  VALIDATION_ERROR = "validationError",
  ONE_HOUR_RETRY = "oneHourRetry",
  GENERIC_ERROR = "submitNewSizeFailed",
}

type NotInSizeRanges =
  | "isHanaScriptServerEnabled"
  | "isHanaMultiAZEnabled"
  | "thresholdECNBlock"
  | "thresholdECNPerformanceClass"
  | "performanceClass"
  | "thresholdLargeSystemsStorage"
  | "thresholdLargeSystemsCompute"
  | "thresholdVCPU"
  | "thresholdCompute"
  | "deleteDataLake";
type OptionalInSizeRanges =
  | "thresholdLargeSystemsStorage"
  | "thresholdLargeSystemsCompute"
  | "thresholdLargeSystemsRequests";

export type SizeRanges = {
  [key in Exclude<keyof typeof FtcParameters, NotInSizeRanges | OptionalInSizeRanges>]: Required<IBaseValue>;
} & { ecnBlocks?: IEcnPlan } & { performanceClass?: IPerformanceClassRangeMap } & {
  [key in OptionalInSizeRanges]?: IBaseValue;
};

export enum FlexibleTenantStatus {
  FAILED = "failed",
  NOT_COMPLETED = "notCompleted",
  COMPLETED = "completed",
}
export enum PatchUpgradeStatus {
  OperationInProgress = "OperationInProgress",
  PatchUpgradeInProgress = "PatchUpgradeInProgress",
  UpgradeCanBePerformed = "UpgradeCanBePerformed",
  PatchAlreadyTheLatest = "PatchAlreadyTheLatest",
  NotAvailableForInternalTenant = "NotAvailableForInternalTenant",
}

export type HyperscalerBasedValue = Record<Hyperscaler, number>;
export interface PerformanceClassesRange {
  range: string[];
  config: ConfigPerformanceClass[];
}

interface ConfigPerformanceClass {
  type: string;
  compute: number;
  memory: number;
}
export const landscapeFeatureKeys = ["hasDataLake", "hasBwBridge", "hasObjectStore"] as const;

export type LandscapeFeatureKey = typeof landscapeFeatureKeys[number];

export type LandscapeFeatures = Record<LandscapeFeatureKey, boolean>;

export interface IPropertiesPerformanceClass {
  type: PerformanceClass;
  compute: number;
  memory: number;
  pricePerHour: number;
  pricePerMonth: number;
}
export interface CapacityUnitsEstimate {
  monthlyEstimate: number;
  averageHourlyEstimate: number;
}

// Derived from ITenantLicenses. Subset with only used props
export interface IFTCRelevantTenantLicense extends IFTCRelevantMeteringThresholds {
  thresholdDWCCU?: string;
  thresholdVCPU?: string;
}

export interface IFTCRelevantMeteringThresholds {
  thresholdStorage?: string;
  thresholdMemory?: string;
  thresholdDataLakeStorage?: string;
  thresholdBWBridge1?: string;
  thresholdRmsNodeHours?: string;
}

export interface ITenantTelemetryInfo {
  tenantId: string;
  tenantClassification: TenantClassification | string;
  requestOrigin: TenantCategory | string;
  requestStatus: ITelemetryStatus;
}

// TODO: Remove this in favour of flexible-tenant-sizing's implementation
export const Measure = {
  DI_EXTRACTION_HOURS: "DI_EXTRACTION_HOURS", // Data Intelligence Embedded Node Hours
  PREMIUM_OUTBOUND: "PREMIUM_OUTBOUND", // Egress
  CATALOG_STORAGE: "CATALOG_STORAGE", // Hana Cloud storage consumed for a catalog tenant
  ELASTIC_COMPUTE_NODE: "ELASTIC_COMPUTE_NODE",
  FOUNDATION_BLOCK: "FOUNDATION_BLOCK",
  THRESHOLD_DATA_LAKE_STORAGE: "THRESHOLD_DATA_LAKE_STORAGE",
  THRESHOLD_MEMORY: "THRESHOLD_MEMORY",
  THRESHOLD_STORAGE: "THRESHOLD_STORAGE",
  THRESHOLD_BW_BRIDGE: "THRESHOLD_BW_BRIDGE",
  LARGE_SYSTEMS_STORAGE: "LARGE_SYSTEMS_STORAGE",
  LARGE_SYSTEMS_COMPUTE: "LARGE_SYSTEMS_COMPUTE",
  LARGE_SYSTEMS_REQUESTS: "LARGE_SYSTEMS_REQUESTS",
} as const;

export type metricsEnablement = { [key in FlexTenantMeasure]: boolean };

export type Measure = typeof Measure[keyof typeof Measure];
export type Metric = typeof dwcMetrics[keyof typeof dwcMetrics];

export interface CapacityUnitsConsumptionValues {
  perMeasureCuConsumption: CapacityUnitsConsumptionRecord;
  currentMonthCuConsumption: number;
  lastMonthCuConsumption: number;
}

export type UsageValues = Partial<Record<Measure, IUsageValue>>;
export type CapacityUnitsConsumptionRecord = Record<Measure, number>;

export interface IUsageValue {
  allocatedValue: number;
  usedValue: number;
  overusageValue: number;
}

export interface UsageConsumptionValues {
  usageValues: UsageValues;
  consumptionValues: CapacityUnitsConsumptionValues;
}

// update type upon FF DWCO_REDESIGN_FTC_UI removal
export type IUsageConsumption = UsageValues | UsageConsumptionValues | undefined;

export interface IEcnPlan {
  thresholdECNPerformanceClass: IECNPerformanceClassDefinition;
  thresholdECNBlock: IBaseValue;
}

export interface IECNPerformanceClassDefinition {
  blocksPossible: IEcnBlocksPossible[];
  range: string[];
}

export interface IEcnBlocksPossible {
  compute: number;
  memory: number;
  storage: number;
  block: number;
  performanceClass: PerformanceClass;
}

export enum PerformanceClass {
  MEMORY = "memory",
  COMPUTE = "compute",
  HIGH_MEMORY = "highMemory",
  HIGH_COMPUTE = "highCompute",
}

export type IntegerTenantLicenses = { [P in keyof Omit<ITenantLicenses, "thresholdECNPerformanceClass">]: number };

export interface IParsedLicenses extends IntegerTenantLicenses {
  thresholdECNPerformanceClass?: string;
}

export interface IGetLicense extends IParsedLicenses {
  tenantType?: TenantType;
  freeTier?: boolean;
  expirationDate?: Date | null;
  tenantClassification?: TenantClassification;
}

export enum TenantType {
  DATA_PLANE = "DataPlane",
  DWC = "DWC",
  CPEA = "DWCCPEA",
  BDC = "BDC",
}

export enum TenantCategory {
  CIS = "CIS",
  SPC = "SPC",
  S4M = "S4M",
}

export interface ITelemetryStatus {
  status: OperationResult;
  error: string;
}

export interface ICalculationResults {
  calculatedCUsEstimate: CapacityUnitsEstimate;
  errors: Array<ScalingErrors | ScalingWarnings>;
}

export interface IBackendDataResponse {
  sizeRanges: SizeRanges;
  usageInfo: IUsageConsumption;
  metadata: INewLoadedMetaData;
  errors: Array<ScalingErrors | ScalingWarnings>;
  hyperscaler: Hyperscaler;
  region: Regions;
}

export enum OperationResult {
  SUCCESS = "SUCCESS",
  FAILED = "FAILED",
}

export enum Operation {
  deleted = "Deleted",
}

export interface IAuditLogInfo {
  licenses?: ITenantLicenses;
  options?: IExtraOptions;
}
