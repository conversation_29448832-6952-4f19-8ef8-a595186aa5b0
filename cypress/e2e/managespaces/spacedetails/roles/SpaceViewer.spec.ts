/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { CLASS } from "../../_util/IDs";
import { TestSpace, TestUser, initTest } from "../../_util/SpacesTestUtil";

// https://wiki.one.int.sap/wiki/display/DWAAS/Bughunt+Session+Space+Dependent+Permissions
describe("Scoped Roles", () => {
  const spaceId = TestSpace.SPACE_3;
  beforeEach(() => {
    initTest(
      { as: TestUser.DWC_ALLSPACES_USER, is: { spaceId, accessible: false } },
      {
        DWCO_INFRA_SPACE_PERMISSIONS: true,
        INFRA_SCOPE_DEPENDENT_ROLES: true,
        INFRA_DWC_TWO_TENANT_MODE: true,
      }
    );
  });

  it("SpaceViewer", () => {
    cy.get(CLASS.IMAGE.$NOTFOUND).should("exist");
  });
});
