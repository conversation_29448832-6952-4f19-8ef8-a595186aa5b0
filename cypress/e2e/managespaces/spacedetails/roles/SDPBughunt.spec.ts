/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { CLASS, ID } from "../../_util/IDs";
import { shouldShowAdminTools, shouldShowCommonTools, shouldShowDsTools } from "../../_util/RolesSpecHelpers";
import { TestSpace, TestUser, clickMenu, deleteSpace, initTest, waitNotBusy } from "../../_util/SpacesTestUtil";

// https://wiki.one.int.sap/wiki/display/DWAAS/Bughunt+Session+Space+Dependent+Permissions
describe("SDP Bughunt Spaces", () => {
  const spaceName = TestSpace.SPACE_NEW;

  const featureflags = {
    DWCO_INFRA_SPACE_PERMISSIONS: true,
    INFRA_SCOPE_DEPENDENT_ROLES: true,
    INFRA_DWC_TWO_TENANT_MODE: true,
    DWCO_SPACES_LOGICAL_DELETE: false,
    DWCO_CLI_CREATE_DELETE_DATABASEUSER: false,
  };

  function toggleSpaceLock(lockSpace: boolean = false): void {
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.TEXT.$STATUS)
      .as("status")
      .should("exist")
      .and("be.visible")
      .and("contain", lockSpace ? "activeLabel" : "lockedLabel");
    cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$LOCKUNLOCK).should("exist").should("not.be.disabled").click();
    cy.get(ID.SPACE.DETAILS.DIALOG.$LOCKUNLOCK).should("exist");
    cy.get(ID.SPACE.DETAILS.DIALOG.LOCKUNLOCK.$BUTTON).should("exist").click();
    waitNotBusy();
    cy.get("@status").should("contain", lockSpace ? "lockedLabel" : "activeLabel");
  }

  describe(`${TestUser.DWC_ADMINISTRATOR}`, () => {
    [
      {
        describeName: "SDP",
        featureflags: {
          DWCO_INFRA_SPACE_PERMISSIONS: true,
          INFRA_SCOPE_DEPENDENT_ROLES: true,
          INFRA_DWC_TWO_TENANT_MODE: true,
          DWCO_SPACES_LOGICAL_DELETE: false,
        },
      },
      {
        describeName: "Non-SDP",
        featureflags: {
          DWCO_INFRA_SPACE_PERMISSIONS: false,
          INFRA_SCOPE_DEPENDENT_ROLES: false,
          DWCO_SPACES_LOGICAL_DELETE: false,
        },
      },
    ].forEach(({ describeName, featureflags }) => {
      describe(describeName, () => {
        it("Create, modify and delete a Space", () => {
          deleteSpace(spaceName);
          initTest(
            {
              as: TestUser.DWC_ADMINISTRATOR,
              navTo: "SPACES",
            },
            featureflags
          );
          shouldShowCommonTools(true);
          if (featureflags.DWCO_INFRA_SPACE_PERMISSIONS) {
            shouldShowDsTools(false, true);
            shouldShowAdminTools(true);
          }

          cy.log("Create Space");
          cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.$CREATESPACE)
            .should("exist")
            .and("be.visible")
            .and("not.be.disabled")
            .click();
          waitNotBusy();
          cy.get(ID.SPACES.OVERVIEW.DIALOG.CREATESPACE.INPUT.$NAME)
            .should("exist")
            .and("be.visible")
            .click()
            .type(spaceName);
          cy.get(ID.SPACES.OVERVIEW.DIALOG.CREATESPACE.BUTTON.$CREATE).click();
          waitNotBusy();
          cy.get(CLASS.$TOASTMESSAGE).should("exist").should("contain", "createSpaceSuccessMessage");

          cy.log("Open created space");
          cy.url().should("contain", `#/managespaces&/ms/details/${spaceName}`);
          waitNotBusy();
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$SAVE).should("exist").and("be.disabled").and("be.visible");
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$DEPLOY)
            .should("exist")
            .and("not.be.disabled")
            .and("be.visible");
          cy.log("Modify created space and save");
          cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.INPUT.$SPACENAME)
            .should("exist")
            .and("be.visible")
            .and("not.be.disabled")
            .clear()
            .type(`${spaceName}_1{enter}`);
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$SAVE).should("exist").and("not.be.disabled").click();
          waitNotBusy();
          cy.get(CLASS.$TOASTMESSAGE).should("exist").and("contain", "updateSettingsSuccessMessage");

          cy.log("Navigate to home");
          clickMenu("HOME");

          cy.log("Navigate back to space");
          clickMenu("SPACES");

          cy.log("Check if changes are saved");
          cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.INPUT.$SPACENAME)
            .should("exist")
            .and("have.value", spaceName + "_1");
          cy.log("Lock unlock space");
          toggleSpaceLock(true);
          toggleSpaceLock(false);

          cy.log("Delete Space");
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$OVERFLOW).click();
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$DELETE).should("exist").click({ force: true });
          cy.get(ID.SPACE.DETAILS.DIALOG.$DELETE).should("exist");
          cy.get(ID.SPACE.DETAILS.DIALOG.DELETE.INPUT.$DELETE)
            .should("exist")
            .and("be.visible")
            .and("not.be.disabled")
            .type("DELETE");
          cy.get(ID.SPACE.DETAILS.DIALOG.DELETE.BUTTON.$DELETE).should("exist").click();
          waitNotBusy();
          deleteSpace(spaceName);
        });
      });
    });
  });
  [TestUser.DWC_MODELER, TestUser.DWC_VIEWER].forEach((user) => {
    it(user, () => {
      initTest(
        {
          as: user,
          navTo: "SPACES",
          is: { accessible: false },
        },
        featureflags
      );
      shouldShowCommonTools();
      shouldShowDsTools(false, false);
      shouldShowAdminTools(false);
      cy.get(CLASS.IMAGE.$NOTFOUND).should("exist");
    });
  });
});
