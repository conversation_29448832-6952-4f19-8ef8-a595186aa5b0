/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { ID } from "../../_util/IDs";
import { shouldShowAdminTools, shouldShowCommonTools, shouldShowDsTools } from "../../_util/RolesSpecHelpers";
import { TestUser, initTest } from "../../_util/SpacesTestUtil";

// https://wiki.one.int.sap/wiki/display/DWAAS/Bughunt+Session+Space+Dependent+Permissions
describe("SDP Space Overview", () => {
  const featureflags = {
    DWCO_INFRA_SPACE_PERMISSIONS: true,
    INFRA_SCOPE_DEPENDENT_ROLES: true,
    INFRA_DWC_TWO_TENANT_MODE: true,
    DWCO_SPACES_LOGICAL_DELETE: false,
  };

  describe(`${TestUser.DWC_ALLSPACES_USER}`, () => {
    describe("Overview", () => {
      it("Cards", () => {
        cy.log("Visibility of cards");
        initTest({ as: TestUser.DWC_ALLSPACES_USER }, featureflags);
        shouldShowCommonTools();
        shouldShowDsTools(true, true);
        shouldShowAdminTools(false);
        cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.$CREATESPACE).should("not.exist");
        // SpaceAdmin
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(0).$ID).should("exist");
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(0).FOOTER.BUTTON.$EDIT).should("exist").and("not.be.disabled");
        // SpaceIntegrator
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(1).$ID).should("exist");
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(1).FOOTER.BUTTON.$EDIT).should("exist").and("not.be.disabled");
        // SpaceModeler
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(2).$ID).should("exist");
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(2).FOOTER.BUTTON.$EDIT).should("exist").and("not.be.disabled");
        // SpaceViewer
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(3).$ID).should("not.exist");
        // SpaceConsumer
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(4).$ID).should("not.exist");
        cy.log("Locking");
        cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.$LOCK).should("exist").and("be.disabled");
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(1).HEADER.$CHECKBOX).click();
        cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.$LOCK).should("exist").and("be.disabled");
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(0).HEADER.$CHECKBOX).click();
        cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.$LOCK).should("be.disabled");
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(1).HEADER.$CHECKBOX).click();
        cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.$LOCK).should("not.be.disabled");
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(0).HEADER.$CHECKBOX).click();
        cy.log("Deletion");
        cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.$DELETESPACE).should("exist").and("be.disabled");
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(1).HEADER.$CHECKBOX).click();
        cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.$DELETESPACE).should("be.disabled");
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(0).HEADER.$CHECKBOX).click();
        cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.$DELETESPACE).should("be.disabled");
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(1).HEADER.$CHECKBOX).click();
        cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.$DELETESPACE).should("not.be.disabled");
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(0).HEADER.$CHECKBOX).click();
        cy.log("Editing");
        cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.$EDITSPACE).should("exist").and("be.disabled");
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(0).HEADER.$CHECKBOX).click();
        cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.$EDITSPACE).should("not.be.disabled");
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(1).HEADER.$CHECKBOX).click();
        cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.$EDITSPACE).should("be.disabled");
      });
    });
  });
});
