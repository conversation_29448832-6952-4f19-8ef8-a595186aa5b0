/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { ID } from "../../_util/IDs";
import { shouldShowAllHeaderButtons } from "../../_util/RolesSpecHelpers";
import { initTest, TestSpace, TestUser } from "../../_util/SpacesTestUtil";

// https://wiki.one.int.sap/wiki/display/DWAAS/Bughunt+Session+Space+Dependent+Permissions
describe("Scoped Roles", () => {
  const spaceId = TestSpace.SPACE_0;
  beforeEach(() => {
    initTest(
      { as: TestUser.DWC_ALLSPACES_USER, is: { spaceId } },
      {
        DWCO_INFRA_SPACE_PERMISSIONS: true,
        INFRA_SCOPE_DEPENDENT_ROLES: true,
        INFRA_DWC_TWO_TENANT_MODE: true,
      }
    );
  });

  it("SpaceAdmin", () => {
    shouldShowAllHeaderButtons();
    cy.get(ID.SPACE.DETAILS.SECTION.$GENERAL).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.INPUT.$SPACEID).should("exist").and("have.attr", "value", spaceId);
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.INPUT.$SPACENAME)
      .should("exist")
      .and("not.have.attr", "disabled", "disabled")
      .and("have.attr", "value", spaceId);
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.TEXT.$STATUS).should("exist").and("contain", "activeLabel");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.TEXT.$TYPE).should("exist").and("contain", "defaultSpaceType");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.TEXT.$CREATEDON).should("exist").and("contain", ":");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.TEXT.$DEPLOYMENTSTATUS).should("exist").and("contain", "@statusActive");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.TEXT.$DEPLOYEDON).should("exist").and("contain", ":");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.STORAGE.CHECKBOX.$QUOTA)
      .should("exist")
      .and("have.attr", "aria-checked", "true")
      .and("have.attr", "aria-disabled", "true");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.DISK.$STEPINPUT).should("exist").and("have.class", "sapMStepInputReadOnly");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.DISK.STEPINPUT.$INPUT).should("have.value", "2.0");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.MEMORY.$STEPINPUT)
      .should("exist")
      .and("have.class", "sapMStepInputReadOnly");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.MEMORY.STEPINPUT.$INPUT).should("have.value", "1.0");
    cy.get(ID.SPACE.DETAILS.SECTION.$WORKLOAD).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.WORKLOAD.$PRIORITY).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.WORKLOAD.PRIORITY.$SLIDER)
      .should("exist")
      .and("have.attr", "aria-disabled", "true")
      .and("have.attr", "aria-valuenow", "5");
    cy.get(ID.SPACE.DETAILS.SECTION.WORKLOAD.$STATEMENTLIMIT).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.WORKLOAD.STATEMENTLIMIT.SELECT.$WORKLOADTYPE)
      .should("exist")
      .and("have.attr", "aria-disabled", "true")
      .and("contain", "default");
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.$ACCESS).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.ACCESS.$CONSUMPTION).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.ACCESS.CONSUMPTION.$CHECKBOX)
      .should("exist")
      .and("not.be.disabled")
      .and("not.have.class", "sapMCbMarkChecked");
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.$NODATA).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.BUTTON.$CREATE).should("exist").and("not.be.disabled");
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.BUTTON.$DELETE).should("exist").and("be.disabled");
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.BUTTON.$EDIT).should("exist").and("be.disabled");
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.BUTTON.$OPENDBX).should("exist").and("be.disabled");
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.$HDICONTAINER).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.BUTTON.$ENABLE).should("exist").should("not.be.disabled");
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.BUTTON.$REMOVE).should("not.exist");
    cy.get(ID.SPACE.DETAILS.SECTION.$DATA).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.DATA.$TIMEDATA).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.DATA.TIMEDATA.MESSAGESTRIP.$DEPLOY).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.DATA.TIMEDATA.BUTTON.$GENERATE).should("exist").and("not.be.disabled");
    cy.get(ID.SPACE.DETAILS.SECTION.$USER).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.USER.$NODATA).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.USER.BUTTON.$ADDUSER).should("exist").and("not.be.disabled");
    cy.get(ID.SPACE.DETAILS.SECTION.USER.BUTTON.$DELETEUSER).should("exist").and("be.disabled");
    cy.get(ID.SPACE.DETAILS.SECTION.USER.BUTTON.$EDITUSER).should("exist").and("be.disabled");
    cy.get(ID.SPACE.DETAILS.SECTION.$REMOTECONNECTION).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.REMOTECONNECTION.BUTTON.$OPENCONNECTIONTOOL).should("exist").and("not.be.disabled");
    cy.get(ID.SPACE.DETAILS.SECTION.$AUDITING).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.MESSAGESTRIP.$DISABLEAUDIT).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.CHECKBOX.$READ)
      .should("exist")
      .and("not.be.disabled")
      .and("not.have.class", "sapMCbMarkChecked");
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.INPUT.$READ)
      .should("exist")
      .and("have.attr", "disabled", "disabled")
      .and("have.value", "7");
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.CHECKBOX.$CHANGE)
      .should("exist")
      .and("not.be.disabled")
      .and("not.have.class", "sapMCbMarkChecked");
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.INPUT.$CHANGE)
      .should("exist")
      .and("have.attr", "disabled", "disabled")
      .and("have.value", "7");
  });
});
