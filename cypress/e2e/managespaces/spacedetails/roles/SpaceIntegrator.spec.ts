/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { ID } from "../../_util/IDs";
import { shouldShowAllHeaderButtons } from "../../_util/RolesSpecHelpers";
import { initTest, TestSpace, TestUser } from "../../_util/SpacesTestUtil";

// https://wiki.one.int.sap/wiki/display/DWAAS/Bughunt+Session+Space+Dependent+Permissions
describe("Scoped Roles", () => {
  const spaceId = TestSpace.SPACE_1;
  beforeEach(() => {
    initTest(
      { as: TestUser.DWC_ALLSPACES_USER, is: { spaceId } },
      {
        DWCO_INFRA_SPACE_PERMISSIONS: true,
        INFRA_SCOPE_DEPENDENT_ROLES: true,
        INFRA_DWC_TWO_TENANT_MODE: true,
      }
    );
  });

  it("SpaceIntegrator", () => {
    shouldShowAllHeaderButtons(false);
    cy.get(ID.SPACE.DETAILS.SECTION.$GENERAL).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.INPUT.$SPACEID).should("exist").and("have.attr", "value", spaceId);
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.INPUT.$SPACENAME)
      .should("exist")
      .and("have.attr", "disabled", "disabled")
      .and("have.attr", "value", spaceId);
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.TEXT.$STATUS).should("exist").and("contain", "activeLabel");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.TEXT.$TYPE).should("exist").and("contain", "abapbridgeSpaceType");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.TEXT.$CREATEDON).should("exist").and("contain", ":");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.TEXT.$DEPLOYMENTSTATUS).should("exist").and("contain", "@statusActive");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.TEXT.$DEPLOYEDON).should("exist").and("contain", ":");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.STORAGE.CHECKBOX.$QUOTA)
      .should("exist")
      .and("have.attr", "aria-checked", "true")
      .and("have.attr", "aria-disabled", "true");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.DISK.$STEPINPUT).should("exist").and("have.class", "sapMStepInputReadOnly");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.DISK.STEPINPUT.$INPUT).should("have.attr", "value", "2.0");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.MEMORY.$STEPINPUT)
      .should("exist")
      .and("have.class", "sapMStepInputReadOnly");
    cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.MEMORY.STEPINPUT.$INPUT).should("have.attr", "value", "1.0");

    cy.get(ID.SPACE.DETAILS.SECTION.$WORKLOAD).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.WORKLOAD.$PRIORITY).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.WORKLOAD.PRIORITY.$SLIDER)
      .should("exist")
      .and("have.attr", "aria-disabled", "true")
      .and("have.attr", "aria-valuenow", "5");
    cy.get(ID.SPACE.DETAILS.SECTION.WORKLOAD.$STATEMENTLIMIT).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.WORKLOAD.STATEMENTLIMIT.SELECT.$WORKLOADTYPE)
      .should("exist")
      .and("have.attr", "aria-disabled", "true")
      .and("contain", "default");
    /* Unfortunately the whole DATABASE and DATA sections are hidden by ActionChecker in TESTS_SPACE_1, as it is now an "abapbridgeSpaceType"
    cy.get(ID.SPACEDETAILS.SECTION.DATABASE_ACCESS).should("exist");
    cy.get(ID.SPACEDETAILS.SECTION.DATABASE_ACCESS.SUBSECTION.DATA_CONSUMPTION).should("exist");
    cy.get(ID.SPACEDETAILS.SECTION.DATABASE_ACCESS.SUBSECTION.DATA_CONSUMPTION.ALLOW_CONSUMPTION_CHECKBOX)
      .should("exist")
      .should("not.be.disabled")
      .should("not.have.class", "sapMCbMarkChecked");
    cy.get(ID.SPACEDETAILS.SECTION.DATABASE_ACCESS.SUBSECTION.DATABASE_USERS.NO_DATA).should("exist");
    // TODO clarify what to do about integrator + db users
    // cy.get(ID.SPACEDETAILS.SECTION.DATABASE_ACCESS.SUBSECTION.DATABASE_USERS.CREATE_BUTTON)
    //   .should("exist")
    //   .should("not.be.disabled");
    // cy.get(ID.SPACEDETAILS.SECTION.DATABASE_ACCESS.SUBSECTION.DATABASE_USERS.DELETE_BUTTON)
    //   .should("exist")
    //   .should("be.disabled");
    // cy.get(ID.SPACEDETAILS.SECTION.DATABASE_ACCESS.SUBSECTION.DATABASE_USERS.EDIT_BUTTON)
    //   .should("exist")
    //   .should("be.disabled");
    // cy.get(ID.SPACEDETAILS.SECTION.DATABASE_ACCESS.SUBSECTION.DATABASE_USERS.OPEN_DBX_BUTTON)
    //   .should("exist")
    //   .should("be.disabled");
    // TODO why is this visible, yet the integrator can not deploy a space?!
    cy.get(ID.SPACEDETAILS.SECTION.DATABASE_ACCESS.SUBSECTION.HDI_CONTAINERS).should("exist");
    cy.get(ID.SPACEDETAILS.SECTION.DATABASE_ACCESS.SUBSECTION.HDI_CONTAINERS.ENABLE_BUTTON)
      .should("exist")
      .should("not.be.disabled");
    cy.get(ID.SPACEDETAILS.SECTION.DATABASE_ACCESS.SUBSECTION.HDI_CONTAINERS.REMOVE_BUTTON}`).should("not.exist");
    cy.get(ID.SPACEDETAILS.SECTION.DATA).should("exist");
    cy.get(ID.SPACEDETAILS.SECTION.DATA.SUBSECTION.TIMEDATA).should("exist");
    cy.get(ID.SPACEDETAILS.SECTION.DATA.SUBSECTION.TIMEDATA.DEPLOY_MESSAGESTRIP).should("not.exist");
    cy.get(ID.SPACEDETAILS.SECTION.DATA.SUBSECTION.TIMEDATA.GENERATE_BUTTON).should("not.exist");
    */
    cy.get(ID.SPACE.DETAILS.SECTION.$USER).should("not.exist");
    cy.get(ID.SPACE.DETAILS.SECTION.USER.$NODATA).should("not.exist");
    cy.get(ID.SPACE.DETAILS.SECTION.USER.BUTTON.$ADDUSER).should("not.exist");
    cy.get(ID.SPACE.DETAILS.SECTION.USER.BUTTON.$DELETEUSER).should("not.exist");
    cy.get(ID.SPACE.DETAILS.SECTION.USER.BUTTON.$EDITUSER).should("not.exist");
    cy.get(ID.SPACE.DETAILS.SECTION.$REMOTECONNECTION).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.REMOTECONNECTION.BUTTON.$OPENCONNECTIONTOOL).should("exist").and("not.be.disabled");
    cy.get(ID.SPACE.DETAILS.SECTION.$AUDITING).should("exist");
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.MESSAGESTRIP.$DISABLEAUDIT).should("not.exist");
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.CHECKBOX.$READ)
      .should("exist")
      .and("not.be.disabled")
      .and("not.have.class", "sapMCbMarkChecked");
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.INPUT.$READ)
      .should("exist")
      .and("have.attr", "disabled", "disabled")
      .and("have.attr", "value", "7");
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.CHECKBOX.$CHANGE)
      .should("exist")
      .and("not.be.disabled")
      .and("not.have.class", "sapMCbMarkChecked");
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.INPUT.$CHANGE)
      .should("exist")
      .and("have.attr", "disabled", "disabled")
      .and("have.attr", "value", "7");
  });
});
