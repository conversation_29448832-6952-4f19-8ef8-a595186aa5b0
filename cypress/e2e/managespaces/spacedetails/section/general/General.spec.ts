/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { MAX_LENGTH_SPACENAME } from "../../../../../../shared/spaces/constants";
import { ShellTestHelper } from "../../../../../integration/crossarchitecture/shell/utility/ShellTestHelper";
import { ID } from "../../../_util/IDs";
import { interceptGetSpaceMetadata } from "../../../_util/Intercepts";
import { initTest, TestSpace, waitNotBusy } from "../../../_util/SpacesTestUtil";

describe("SpaceDetails general", () => {
  const featureflags = {
    DWCO_INFRA_SPACE_PERMISSIONS: true,
    INFRA_SCOPE_DEPENDENT_ROLES: true,
    INFRA_DWC_TWO_TENANT_MODE: true,
    DWCO_SPACES_LOGICAL_DELETE: false,
  };

  describe("Description", () => {
    beforeEach(() => {
      initTest({
        navigation: { to: "SPACES", params: { spaceId: TestSpace.SPACE_0 } },
        intercepts: { spaces_API: { PATCH: { mock: true } } },
        system: { featureflags },
      });
    });
    it("Change, Enforce maximum length and save", () => {
      cy.get(ID.SPACE.DETAILS.HEADER.EXPANDED.$TITLE).should("contain", TestSpace.SPACE_0);
      ShellTestHelper.getBreadCrumbsCurrentText().should("contain", TestSpace.SPACE_0);
      cy.title().should("eq", `${TestSpace.SPACE_0} - menu_spaces - AppShell_SidebarTitle`);
      cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.INPUT.$SPACENAME)
        .clear()
        .type("x".repeat(MAX_LENGTH_SPACENAME + 10));
      interceptGetSpaceMetadata(true, true);
      cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$SAVE).should("exist").and("not.be.disabled").click();
      waitNotBusy();
      cy.get(ID.SPACE.DETAILS.SECTION.GENERAL.TEXT.$DEPLOYMENTSTATUS).should("contain", "@statusChangesToDeploy");
      cy.get(ID.SPACE.DETAILS.HEADER.EXPANDED.$TITLE).should("contain", "x".repeat(MAX_LENGTH_SPACENAME));
      ShellTestHelper.getBreadCrumbsCurrentText().should("contain", "x".repeat(MAX_LENGTH_SPACENAME));
      cy.title().should("eq", `${"x".repeat(MAX_LENGTH_SPACENAME)} - menu_spaces - AppShell_SidebarTitle`);
    });
  });
});
