/** @format */

import { CLASS, ID } from "../../../../_util/IDs";
import { RequestAlias } from "../../../../_util/Intercepts";
import { TestSpace, TestUser, initTest, waitNotBusy } from "../../../../_util/SpacesTestUtil";

describe("HDI container section", () => {
  [
    {
      describeName: "SDP",
      featureflags: {
        DWCO_INFRA_SPACE_PERMISSIONS: true,
        INFRA_SCOPE_DEPENDENT_ROLES: true,
        INFRA_DWC_TWO_TENANT_MODE: true,
        DWCO_SPACES_LOGICAL_DELETE: false,
      },
    },
    {
      describeName: "Non-SDP",
      featureflags: {
        DWCO_INFRA_SPACE_PERMISSIONS: false,
        INFRA_SCOPE_DEPENDENT_ROLES: false,
        DWCO_SPACES_LOGICAL_DELETE: false,
      },
    },
  ].forEach(({ describeName, featureflags }) => {
    describe(describeName, () => {
      describe("Standard scenarios", () => {
        it("HDI containers not enabled", () => {
          initTest(
            {
              as: featureflags.INFRA_SCOPE_DEPENDENT_ROLES ? TestUser.DWC_ALLSPACES_USER : TestUser.DWC_ADMINISTRATOR,
              is: { spaceId: TestSpace.SPACE_2, HDIenabled: false },
              mockCall: { timedata: true },
            },
            featureflags
          );
          cy.wait(RequestAlias.spaces_details_HDIContainers_GET);
          hasHDITable(false);
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.$TABLE)
            .should("exist")
            .and("be.visible")
            .contains("noDataHDIContainers");
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.BUTTON.$ENABLE)
            .should("exist")
            .should("be.visible")
            .click();
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.$ENABLEMENT).should("exist").and("be.visible");
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ENABLEMENT.BUTTON.$OK).should("exist").and("not.be.disabled").click();
          cy.window()
            .its("sap.cdw.components.shell.utility")
            .then((utility: any) => {
              const { action, feature, eventtype } =
                utility.ShellContainer.get().getUsageCollectionService().aRecordedActions[0];
              expect(action).to.equal("createTicketForHDIMapping");
              expect(feature).to.equal("spacemanagement");
              expect(eventtype).to.equal("click");
            });
        });

        it("Delete HDI containers", () => {
          initTest(
            {
              has: { HDIContainers: true },
              mockCall: { saveSpace: true, timedata: true },
              as: featureflags.INFRA_SCOPE_DEPENDENT_ROLES ? TestUser.DWC_ALLSPACES_USER : TestUser.DWC_ADMINISTRATOR,
              is: { spaceId: TestSpace.SPACE_0, HDIenabled: true },
            },
            featureflags
          );
          cy.wait(RequestAlias.spaces_details_HDIContainers_GET);
          hasHDITable();
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.TABLE.$ROWS)
            .should("have.length", 8)
            .eq(1)
            .contains("Awesome HDI Container");
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.INPUT.$SEARCH)
            .should("exist")
            .and("be.visible")
            .type("Standard");
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.TABLE.$ROWS)
            .should("have.length", 2)
            .and("contain", "Standard HDI Container");
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.BUTTON.$RESETSEARCH).click();
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.TABLE.$ROWS).should("have.length", 8);
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.BUTTON.$REMOVE)
            .scrollIntoView()
            .should("exist")
            .and("be.visible")
            .and("be.disabled");
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.TABLE.$SELECTIONS).eq(4).click();
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.TABLE.$SELECTIONS).eq(7).click();
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.BUTTON.$REMOVE).and("not.be.disabled").click();
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$SAVE).click();
          waitNotBusy();
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.BUTTON.$REMOVE).should("be.disabled");
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.$TABLE).should("not.have.class", ".sapMCbMarkChecked");
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.TABLE.$ROWS).should("have.length", 6);
        });

        it("Add HDI containers", () => {
          initTest(
            {
              is: { spaceId: TestSpace.SPACE_0, HDIenabled: true },
              has: { HDIContainers: true, HDIContainersUnassigned: true },
              mockCall: { saveSpace: true, timedata: true },
              as: featureflags.INFRA_SCOPE_DEPENDENT_ROLES ? TestUser.DWC_ALLSPACES_USER : TestUser.DWC_ADMINISTRATOR,
            },
            featureflags
          );
          cy.wait(RequestAlias.spaces_details_HDIContainers_GET);
          hasHDITable();
          openAddHDIContainersDialog();
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.$ADDCONTAINER).should("not.contain", "addHDIContainersDialogNoDataText");
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.$LIST)
            .children()
            .eq(0)
            .should("contain", "Awesome Unassigned HDI Container");
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.$LIST)
            .children()
            .eq(6)
            .should("contain", "Ultra Unassigned HDI Container");
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.INPUT.$SEARCH)
            .should("exist")
            .and("be.visible")
            .type("Normal", { delay: 50, force: true });
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.$LIST).children().its("length").should("eq", 1);
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.$LIST)
            .children()
            .eq(0)
            .should("contain", "Normal Unassigned HDI Container");
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.BUTTON.$CANCEL).click();
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.$ADDCONTAINER).should("not.exist");
          cy.log("Open dialog again and check if filter was reset");
          openAddHDIContainersDialog();
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.$LIST)
            .children()
            .eq(0)
            .should("contain", "Awesome Unassigned HDI Container");
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.$LIST)
            .children()
            .eq(6)
            .should("contain", "Ultra Unassigned HDI Container");
          cy.log("Check invalid search");
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.INPUT.$SEARCH)
            .should("exist")
            .and("be.visible")
            .type("does not exist", { delay: 50, force: true });
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.$LIST).children().its("length").should("eq", 1);
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.$ADDCONTAINER).should("contain", "addHDIContainersDialogNoDataText");
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.BUTTON.$RESETSEARCH).click();
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.$LIST).children().its("length").should("eq", 7);
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.$LIST)
            .children()
            .eq(0)
            .should("contain", "Awesome Unassigned HDI Container");
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.$LIST)
            .children()
            .eq(6)
            .should("contain", "Ultra Unassigned HDI Container");
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.BUTTON.$OK)
            .should("exist")
            .and("be.visible")
            .and("be.disabled");
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.$LIST).find(CLASS.$CHECKBOX).eq(0).click();
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.$LIST).find(CLASS.$CHECKBOX).eq(6).click();
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.BUTTON.$OK).should("not.be.disabled").click();
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.$ADDCONTAINER).should("not.exist");
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.TABLE.$ROWS).should("have.length", 10);
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$SAVE).click();
          cy.wait([RequestAlias.spaces_API_PATCH]);
        });

        it("No unassigned HDI container available", () => {
          initTest(
            {
              is: { spaceId: TestSpace.SPACE_0, HDIenabled: true },
              has: { HDIContainers: false, HDIContainersUnassigned: false },
              mockCall: { saveSpace: true, timedata: true },
              as: featureflags.INFRA_SCOPE_DEPENDENT_ROLES ? TestUser.DWC_ALLSPACES_USER : TestUser.DWC_ADMINISTRATOR,
            },
            featureflags
          );
          cy.wait(RequestAlias.spaces_details_HDIContainers_GET);
          hasHDITable();
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.$TABLE).should("contain", "noDataHDIContainers");
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.TABLE.$ROWS).should("have.length", 2);
          openAddHDIContainersDialog();
          cy.get(ID.SPACE.DETAILS.DIALOG.HDI.$ADDCONTAINER).should("contain", "addHDIContainersDialogNoDataText");
        });

        it("Cannot load assigned HDI containers", () => {
          initTest(
            {
              is: { spaceId: TestSpace.SPACE_0, HDIenabled: true },
              has: { HDIContainers: false, HDIContainersUnassigned: false },
              mockCall: { saveSpace: true, timedata: true },
              callSuccessful: { getHDIContainers: false },
              as: featureflags.INFRA_SCOPE_DEPENDENT_ROLES ? TestUser.DWC_ALLSPACES_USER : TestUser.DWC_ADMINISTRATOR,
            },
            featureflags
          );
          cy.wait(RequestAlias.spaces_details_HDIContainers_GET);
          cy.get(ID.SPACE.DETAILS.MESSAGESTRIP.$LOADERROR).should("exist").should("be.visible");
          hasHDITable(false);
          cy.get(ID.SPACE.DETAILS.SECTION.$USER).scrollIntoView();
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.$TABLE).should("contain", "noDataHDIContainers");
          cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.BUTTON.$ENABLE)
            .should("be.visible")
            .should("be.enabled");
        });
      });
    });
  });

  function hasHDITable(enabled: boolean = true) {
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.$TABLE).should("exist").scrollIntoView();
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.BUTTON.$ADD).should(enabled ? "be.visible" : "not.exist");
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.BUTTON.$ENABLE).should(enabled ? "not.exist" : "be.visible");
  }

  function openAddHDIContainersDialog() {
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.BUTTON.$ADD).click();
    cy.wait(RequestAlias.spaces_details_HDIContainers_GET);
    cy.get(ID.SPACE.DETAILS.DIALOG.HDI.$ADDCONTAINER).should("exist").and("be.visible");
    cy.get(ID.SPACE.DETAILS.DIALOG.HDI.ADDCONTAINER.$LIST)
      .should("exist")
      .children()
      .its("length")
      .should("be.greaterThan", 0);
  }
});
