/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { ID } from "../../../../_util/IDs";
import { initTest, TestSpace } from "../../../../_util/SpacesTestUtil";

describe("DatabaseUserSection", {}, () => {
  [
    {
      describeName: "SDP - Old DBUsers API",
      featureflags: {
        DWCO_INFRA_SPACE_PERMISSIONS: true,
        INFRA_SCOPE_DEPENDENT_ROLES: true,
        INFRA_DWC_TWO_TENANT_MODE: true,
        DWCO_CLI_CREATE_DELETE_DATABASEUSER: false,
      },
    },
    {
      describeName: "Non-SDP - Old DBUsers API",
      featureflags: {
        DWCO_INFRA_SPACE_PERMISSIONS: false,
        INFRA_SCOPE_DEPENDENT_ROLES: false,
        DWCO_CLI_CREATE_DELETE_DATABASEUSER: false,
      },
    },
    {
      describeName: "SDP - New DBUsers API",
      featureflags: {
        DWCO_INFRA_SPACE_PERMISSIONS: true,
        INFRA_SCOPE_DEPENDENT_ROLES: true,
        INFRA_DWC_TWO_TENANT_MODE: true,
        DWCO_CLI_CREATE_DELETE_DATABASEUSER: true,
      },
    },
    {
      describeName: "Non-SDP - New DBUsers API",
      featureflags: {
        DWCO_INFRA_SPACE_PERMISSIONS: false,
        INFRA_SCOPE_DEPENDENT_ROLES: false,
        DWCO_CLI_CREATE_DELETE_DATABASEUSER: true,
      },
    },
  ].forEach(({ describeName, featureflags }) => {
    describe(describeName, () => {
      it("TableHeader", () => {
        initTest({ has: { databaseUsers: true }, is: { spaceId: TestSpace.SPACE_0 } }, featureflags);
        cy.log("should filter database users");
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.$USERS).scrollIntoView().should("exist").and("be.visible");
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.$TABLE).children().should("have.length", 2);
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.INPUT.$FILTER).type("#1");
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.$TABLE).children().should("have.length", 1);
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.INPUT.$FILTER).clear();
        cy.log("check table toolbar buttons");
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.$USERS).scrollIntoView().should("exist").and("be.visible");
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.BUTTON.$CREATE).should("not.be.disabled");
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.BUTTON.$DELETE).should("be.disabled");
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.BUTTON.$EDIT).should("be.disabled");
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.BUTTON.$OPENDBX).should("be.disabled");
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.TABLE.BUTTON.$SELECTALL).click();
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.BUTTON.$DELETE).should("not.be.disabled");
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.BUTTON.$EDIT).should("be.disabled");
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.BUTTON.$OPENDBX).should("be.disabled");
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.TABLE.BUTTON.$SELECTALL).click();
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.$TABLE)
          .children()
          .first()
          .find("[id*=-selectMulti-CbBg]")
          .click();
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.BUTTON.$DELETE).should("not.be.disabled");
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.BUTTON.$EDIT).should("not.be.disabled");
        cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.USERS.BUTTON.$OPENDBX).should("not.be.disabled");
      });
    });
  });
});
