/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { ID } from "../../../_util/IDs";
import { initTest, TestSpace, waitNotBusy } from "../../../_util/SpacesTestUtil";

describe("Audit Settings for Spaces and SQL Schemas", () => {
  [
    {
      describeName: "SDP",
      featureflags: {
        DWCO_INFRA_SPACE_PERMISSIONS: true,
        INFRA_SCOPE_DEPENDENT_ROLES: true,
        INFRA_DWC_TWO_TENANT_MODE: true,
      },
    },
    {
      describeName: "Non-SDP",
      featureflags: {
        DWCO_INFRA_SPACE_PERMISSIONS: false,
        INFRA_SCOPE_DEPENDENT_ROLES: false,
      },
    },
  ].forEach(({ describeName, featureflags }) => {
    describe(describeName, () => {
      beforeEach(() => {
        initTest({ mockCall: { saveSpace: true }, is: { spaceId: TestSpace.SPACE_0 } }, featureflags);
      });
      describe("Audit settings for space", () => {
        it("Check rendering", () => {
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.MESSAGESTRIP.$DISABLEAUDIT).should("exist");
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.CHECKBOX.$READ).should("exist");
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.LABEL.READ.$UNIT).should("exist");
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.INPUT.$READ).should("exist");
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.LABEL.READ.$UNIT).should("exist");
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.CHECKBOX.$READ)
            .should("exist")
            .and("not.have.class", "sapMCbMarkChecked");
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.CHECKBOX.$CHANGE)
            .should("exist")
            .and("not.have.class", "sapMCbMarkChecked");
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.INPUT.$CHANGE).should("exist");
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.LABEL.CHANGE.$UNIT).should("exist");
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.INPUT.$READ).should("have.value", "7");
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.INPUT.$CHANGE).should("have.value", "7");
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$SAVE).should("be.disabled");
        });

        it("Turn on change audit log", () => {
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.CHECKBOX.$CHANGE).click().should("have.class", "sapMCbMarkChecked");
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$SAVE).should("not.be.disabled");
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.INPUT.$CHANGE).should("not.be.disabled").should("have.value", "7");
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.VALUESTATE.MESSAGE.$CHANGE).should("not.exist");
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$SAVE).should("not.be.disabled");
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$SAVE).click();
          waitNotBusy();
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$SAVE).should("be.disabled");
        });

        it("Enable both audit log settings and change retention", () => {
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.CHECKBOX.$READ).click();
          changeReadAuditLogRetention("123", true);
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.CHECKBOX.$CHANGE).click().should("have.class", "sapMCbMarkChecked");
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$SAVE).should("not.be.disabled");
          cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.INPUT.$CHANGE).should("not.be.disabled");
          changeChangeAuditLogRetention("210795", false);
          changeChangeAuditLogRetention("10000", true);
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$SAVE).should("not.be.disabled");
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$SAVE).click();
          waitNotBusy();
          cy.get(ID.SPACE.DETAILS.HEADER.TOOLBAR.BUTTON.$SAVE).should("be.disabled");
        });
      });
    });
  });

  function changeReadAuditLogRetention(value: string, noError: boolean) {
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.LABEL.READ.$UNIT).scrollIntoView().should("be.visible");
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.INPUT.$READ, { timeout: 1000 })
      .should("not.be.disabled")
      .clear()
      .type(value)
      .should("have.value", value);
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.VALUESTATE.MESSAGE.$READ).should(noError ? "not.exist" : "exist");
    if (!noError) {
      cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.VALUESTATE.MESSAGE.$READ)
        .should("have.class", "sapMValueStateMessageError")
        .and("contain", "incorrectRetentionTime");
    }
  }

  function changeChangeAuditLogRetention(value: string, noError: boolean) {
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.LABEL.READ.$UNIT).scrollIntoView().should("be.visible");
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.INPUT.$CHANGE, { timeout: 1000 })
      .should("not.be.disabled")
      .clear()
      .type(value)
      .should("have.value", value);
    cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.VALUESTATE.MESSAGE.$CHANGE).should(noError ? "not.exist" : "exist");
    if (!noError) {
      cy.get(ID.SPACE.DETAILS.SECTION.AUDITING.VALUESTATE.MESSAGE.$CHANGE)
        .should("have.class", "sapMValueStateMessageError")
        .and("contain", "incorrectRetentionTime");
    }
  }
});
