/** @format */

import { State } from "@sap/dwc-circuit-breaker";
import { CLASS } from "./IDs";
import { getInterceptAlias, interceptAll } from "./Intercepts";
import {
  InterceptsMockConfiguration,
  NavigationItemKey,
  SpaceNavigationParameters,
  TestConfiguration,
  UIConstantsType,
} from "./Types";

export enum TestUser {
  DWC_ADMINISTRATOR = "DWC_ADMINISTRATOR",
  DWC_ALLSPACES_USER = "DWC_ALLSPACES_USER",
  DWC_SOMESPACES_USER = "DWC_SOMESPACES_USER",
  DWC_MODELER = "DWC_MODELER",
  DWC_MODELER_ONLY = "DWC_MODELER_ONLY",
  DWC_VIEWER = "DWC_VIEWER",
  DWC_INTEGRATOR = "DWC_INTEGRATOR",
  DWC_SPACE_ADMINISTRATOR = "DWC_SPACE_ADMINISTRATOR",
  DWC_ADMINISTRATOR_ONLY = "DWC_ADMINISTRATOR_ONLY",
  DWC_SDP_MODELER = "DWC_SDP_MODELER",
  DWC_SDP_INTEGRATOR = "DWC_SDP_INTEGRATOR",
  DWC_SDP_VIEWER = "DWC_SDP_VIEWER",
  DWC_SDP_SPACE_ADMIN = "DWC_SDP_SPACE_ADMIN",
  DWC_ADMINISTRATOR_WITH_SYSTEMINFO_READ = "DWC_ADMINISTRATOR_WITH_SYSTEMINFO_READ",
}

export enum TestSpace {
  SPACE_0 = "TESTS_SPACE_0",
  SPACE_1 = "TESTS_SPACE_1",
  SPACE_2 = "TESTS_SPACE_2",
  SPACE_3 = "TESTS_SPACE_3",
  SPACE_4 = "TESTS_SPACE_4",
  SPACE_5 = "TESTS_SPACE_5",
  SPACE_NEW = "TESTS_SPACE_NEW",
}

const navigationHandler: Partial<Record<NavigationItemKey, (args: TestConfiguration) => void>> = {
  HOME: (args) => visitHome(args),
  DATABUILDER: (args) => visitDataBuilder(args),
  ADMINISTRATION_HDI: (args) => visitAdministrationHDI(args),
  ADMINISTRATION_WORKLOADMANAGEMENT: (args) => visitAdministrationWorkloadManagement(args),
  SPACES: (args) => visitSpaces(args),
  SPACEMONITOR: (args) => {
    args.navigation.to = "SPACEMONITOR";
    return visitSpaces(args);
  },
};

export function initTest(testConfiguration: TestConfiguration) {
  const {
    as = TestUser.DWC_ADMINISTRATOR,
    system = { featureflags: {} },
    navigation = { to: "SPACES" },
  } = testConfiguration;
  cy.login(as || TestUser.DWC_ADMINISTRATOR, system.featureflags);
  interceptAll(testConfiguration);
  navigationHandler[navigation.to || "SPACES"](testConfiguration);
}

export function deleteSpace(spaceId: string): Cypress.Chainable<Cypress.Response<any>> {
  return cy.request({
    method: "DELETE",
    url: `${Cypress.config(
      "baseUrl"
    )}dwaas-core/api/v1/content?space=${spaceId}&spaceDefinition=true&connections=true&definitions=true`,
    failOnStatusCode: false,
  });
}

export function visitSpaces({
  intercepts = {} as InterceptsMockConfiguration,
  system: { circuitBreakerState = State.Green } = {},
  navigation,
}: TestConfiguration = {}) {
  const { spaces_overview_list_top25 } = intercepts;
  const params = (navigation?.params as SpaceNavigationParameters) || {};
  const accessible = params.accessible ?? true;
  const handleOverview = () => {
    const url = `#/managespaces${params.showDeleted ? "&/ms/overview?view=tile&showDeleted=true" : ""}`;
    cy.visit(url);
    if (accessible) {
      if (spaces_overview_list_top25 || !navigation || (navigation?.to === "SPACES" && !params?.spaceId)) {
        cy.wait([getInterceptAlias("GET", "spaces_overview_list_top25")]);
      }
      cy.window().then(setCircuitBreakerState(circuitBreakerState));
    }
    cy.url().should(accessible ? "contain" : "not.contain", "overview?view");
  };
  const handleDetails = () => {
    const detailsPath = `#/managespaces&/ms/details/${params.spaceId}${
      navigation?.to === "SPACEMONITOR" ? "/monitoring" : ""
    }`;
    cy.visit(detailsPath);
    if (accessible && navigation?.to !== "SPACEMONITOR") {
      cy.wait([getInterceptAlias("GET", "spaces_API"), getInterceptAlias("GET", "shell_breadcrumbs")]);
      cy.window().then(setCircuitBreakerState(circuitBreakerState));
      cy.url().should("contain", params.spaceId);
    }
  };
  params.spaceId ? handleDetails() : handleOverview();
  ensureUiReady();
}

export function visitHome(_args: TestConfiguration = {}) {
  cy.visit(NAVIGATIONITEMS.HOME.url);
  cy.url().should("contain", NAVIGATIONITEMS.HOME.url);
  ensureUiReady();
}

export function visitAdministrationHDI(_args: TestConfiguration = {}) {
  cy.visit(NAVIGATIONITEMS.ADMINISTRATION_HDI.url);
  cy.url().should("contain", NAVIGATIONITEMS.ADMINISTRATION_HDI.url);
  ensureUiReady();
}

export function visitAdministrationWorkloadManagement(_args: TestConfiguration = {}) {
  cy.visit(NAVIGATIONITEMS.ADMINISTRATION_WORKLOADMANAGEMENT.url);
  cy.url().should("contain", NAVIGATIONITEMS.ADMINISTRATION_WORKLOADMANAGEMENT.url);
  ensureUiReady();
}

export function visitDataBuilder({ intercepts = {} }: TestConfiguration = {}) {
  cy.visit(NAVIGATIONITEMS.DATABUILDER.url);
  cy.url().should("contain", NAVIGATIONITEMS.DATABUILDER.url);
  cy.wait([getInterceptAlias("GET", "spaces_selection")]);
  if (!!intercepts.spaces_overview_resources_spaces?.GET?.successful) {
    cy.wait([getInterceptAlias("GET", "spaces_overview_resources_spaces")]);
  }
  // TODO: we cannot use ensureUiReady for now as the data builder keeps a sapUiBusyIndicator in sap-ui-static
  cy.get(".sapMTitle").should("exist").and("be.visible");
}

export function waitNotBusy(maxRetries = 5, delay = 50): Cypress.Chainable {
  let retries = 0;
  function check(): Cypress.Chainable {
    return cy
      .get(CLASS.BUSYINDICATORS.$ANY, { timeout: 60000 })
      .should("not.exist")
      .then(() => {
        cy.wait(delay);
        const indicators = Cypress.$(CLASS.BUSYINDICATORS.$ANY);
        if (indicators.length === 0) {
          return;
        }
        if (retries++ < maxRetries) {
          return cy.wait(delay).then(check);
        }
        throw new Error("Busy indicator reappeared after max retries");
      });
  }
  return check();
}

export function createUIConstants<T extends Record<string, unknown>>(obj: T): UIConstantsType<T> {
  return transformObject(obj) as UIConstantsType<T>;
}

export function clickMenu(item: keyof typeof NAVIGATIONITEMS) {
  const entry = NAVIGATIONITEMS[item];
  cy.get(CLASS.SHELL.NAVIGATION.$ITEMS).filter(`[title="${entry.key}"]`).click();
  cy.url().should("contain", entry.url);
  waitNotBusy();
}

export function checkAndCloseErrorMsgBox(errorText: string): void {
  cy.get(CLASS.MESSAGEBOX.$ERROR).should("exist").should("contain", errorText);
  cy.get(CLASS.MESSAGEBOX.$BUTTONS).first().click();
  cy.get(CLASS.MESSAGEBOX.$ERROR).should("not.exist");
}

/**
 * Resolves the Unicode character (glyph) for a given SAP icon name like "sap-icon://accept"
 * using the UI5 IconPool. Useful for checking rendered DOM glyphs in Cypress tests.
 *
 * @param iconName - The UI5 icon name, e.g. "sap-icon://accept"
 * @returns The corresponding Unicode character
 */
export function resolveIconUnicode(iconName: string): Cypress.Chainable<string> {
  return cy
    .window()
    .its("sap.ui.core.IconPool")
    .then(async (IconPool) => {
      const { content }: { content: string } = await IconPool.getIconInfo(iconName);
      if (!content) {
        throw new Error(`Icon "${iconName}" not found`);
      }
      return content;
    });
}

/* internal */

export const NAVIGATIONITEMS = {
  HOME: { key: "menu_home", url: "#/home" },
  DATABUILDER: { key: "menu_databuilder", url: "#/databuilder" },
  ADMINISTRATION_HDI: { key: undefined, url: "#/configuration&/cfg/hdiMapping" },
  ADMINISTRATION_WORKLOADMANAGEMENT: { key: undefined, url: "#/configuration&/cfg/workloadManagement" },
  SPACES: { key: "menu_spaces", url: "#/managespaces" },
  SPACEMONITOR: { key: undefined, url: undefined },
} as const;

function transformObject(obj: unknown): unknown {
  if (!obj || typeof obj !== "object") {
    return obj;
  }
  const result: Record<string, unknown> = {};
  for (const [key, value] of Object.entries(obj)) {
    splitKeyAndAssign(result, key, value);
  }
  return result;
}

function splitKeyAndAssign(resultObj: Record<string, unknown>, key: string, value: unknown): void {
  const segments = key.split("_");
  let current = resultObj;
  segments.forEach((segment, i) => {
    if (i === segments.length - 1) {
      if (typeof value === "function") {
        current[segment] = (...args: unknown[]) => transformObject(value(...args));
      } else {
        current[`$${segment}`] = transformObject(value);
      }
    } else {
      if (!current[segment]) {
        current[segment] = {};
      }
      current = current[segment] as Record<string, unknown>;
    }
  });
}

function ensureUiReady() {
  cy.get(".sapMTitle").should("exist").and("be.visible");
  waitNotBusy();
}

// TODO : remove this workaround helper when the circuit breaker works in offline mode again
function setCircuitBreakerState(state: State = State.Green): (win: Window & { sap?: any }) => void {
  return (win) => {
    const core = win.sap?.ui?.getCore?.();
    if (!core) {
      return;
    }
    if (!core.getModel("circuitbreaker")) {
      core.setModel(new win.sap.ui.model.json.JSONModel(), "circuitbreaker");
    }
    core.getModel("circuitbreaker").setProperty("/DataHANA", state);
  };
}
