/** @format */

import { ObjectStatus } from "@sap/deepsea-types";
import { State } from "@sap/dwc-circuit-breaker";
import { CyHttpMessages } from "cypress2/types/net-stubbing";
import { DbUsers } from "../../../../shared/api/SpaceAPI";
import { IDatabaseUser, SpaceStatus } from "../../../../shared/spaces/types";
import { InterceptRoutes } from "./Intercepts";
import { NAVIGATIONITEMS, TestUser } from "./SpacesTestUtil";

export type InterceptsMockConfiguration = {
  [K in InterceptRouteKey]?: {
    [M in InterceptRouteMethod<K>]?: RouteConfiguration;
  };
};

export interface TestConfiguration {
  as?: TestUser;
  has?: Partial<ToolsConfiguration>;
  navigation?: Navigation;
  intercepts?: InterceptsMockConfiguration;
  system?: SystemConfiguration;
}
interface SystemConfiguration {
  hdi?: SystemHDIConfiguration;
  elasticity?: SystemElasticityConfiguration;
  datalake?: SystemDatalakeConfiguration;
  circuitBreakerState?: State;
  featureflags?: Record<string, boolean>;
}
interface SystemHDIConfiguration {
  enabled?: boolean;
  hasUnassignedContainers?: boolean;
  hasHDIMapping?: boolean;
}
interface SystemElasticityConfiguration {
  isReady?: boolean;
  hasECNs?: boolean;
  hasECNSchedules?: boolean;
  isECNUpdated?: boolean;
  hasPerformanceClass?: boolean;
  performanceClassType?: string;
}
interface SystemDatalakeConfiguration {
  available?: boolean;
}
export interface RouteConfiguration {
  mock?: boolean;
  statusCode?: number;
}
type Navigation = {
  [K in keyof NavigationParameters]: {
    to: K;
    params?: NavigationParameters[K];
  };
}[keyof NavigationParameters];

export interface SpaceNavigationParameters {
  showDeleted?: boolean;
  spaceId?: string;
  accessible?: boolean;
}
type NavigationParameters = {
  SPACES?: SpaceNavigationParameters;
} & {
  [K in NavigationItemKey]?: unknown;
};
type ToolsConfiguration = {
  [K in NavigationItemKey | "SPACESELECTION"]?: ToolConfigurations[K];
};
type ToolConfigurations = {
  SPACES: SpacesToolConfiguration;
  SPACESELECTION?: SpaceSelectionConfiguration;
} & {
  [K in NavigationItemKey]?: unknown;
};
interface SpaceSelectionConfiguration {
  spaces?: Record<string, SpaceDefinition>;
  flags?: SpaceSelectionFlags;
}
interface SpaceSelectionFlags {
  numberOfSpaces?: number;
}
interface SpacesToolConfiguration {
  overview?: SpaceOverviewConfiguration;
  spaces?: Record<string, SpaceDefinition>;
}
interface SpaceOverviewConfiguration {
  numberOfSpaces?: number;
  storageFull?: boolean;
}

export interface SpaceDefinition {
  designTime?: DesigntimeDefinition;
  runtime?: RuntimeDefinition;
}

export interface RuntimeDefinition extends SpaceLocationDefinition {
  properties?: RuntimeProperties;
  flags?: RuntimeFlags;
}
interface RuntimeProperties {
  status?: SpaceStatus;
  dataLakeEnabled?: boolean;
  databaseusers?: IDatabaseUser;
}
interface RuntimeFlags {
  temporarilyUnlocked?: boolean;
  hasTimedata?: boolean;
  hasTimedataDependencies?: boolean;
  hasDatabaseUsers?: boolean;
  noQuota?: boolean;
}

export interface RuntimeDefinition extends SpaceLocationDefinition {
  properties?: RuntimeProperties;
  flags?: RuntimeFlags;
}

export interface DesigntimeDefinition extends SpaceLocationDefinition {
  properties?: DesigntimeProperties;
  flags?: DesigntimeFlags;
}
interface DesigntimeProperties {
  business_name?: string;
  enableDataLake?: boolean;
  dbusers?: DbUsers;
  deployment_date?: string;
  deployment_status?: ObjectStatus;
  // TODO fix type..seems to be wrong in spaceAPI
  hdicontainers?: DbUsers;
}
interface DesigntimeFlags {
  veryLongDescription?: boolean;
  hasHDIcontainers?: boolean;
  hasDatabaseUsers?: boolean;
  isCorrupt?: boolean;
}

export interface SpaceLocationDefinition {
  properties?: Record<string, any>;
  flags?: Record<string, any>;
}

export interface SpaceHealthType {
  notDeployed?: boolean;
  corrupt?: boolean;
  locked?: boolean;
  temporarilyUnlocked?: boolean;
}

type ModifyResponseFunction<T> = (
  res: CyHttpMessages.IncomingHttpResponse<T>,
  req: CyHttpMessages.IncomingHttpRequest<any, any>
) => void;

/* If routeConfiguration.mock is true, `body` and optional `routeConfiguration.statusCode` (default 200) will be used 
  with req.reply. If routeConfiguration.mock is false or undefined and modifyResponse is provided, it will be used with
  req.continue. If neither is set, the request will passthrough without modifications. */
export interface InterceptOptions<T = unknown> {
  routeConfiguration?: RouteConfiguration;
  body?: T | undefined;
  modifyResponse?: ModifyResponseFunction<T>;
}

export type InterceptRouteKey = keyof typeof InterceptRoutes;
export type InterceptRouteMethod<K extends InterceptRouteKey> = typeof InterceptRoutes[K]["methods"][number];
export type InterceptAlias<K extends InterceptRouteKey, M extends InterceptRouteMethod<K>> = `@${M}_${string}`;

export type NavigationItemKey = keyof typeof NAVIGATIONITEMS;

type SplitUnderscore<S extends string> = S extends `${infer Head}_${infer Tail}`
  ? [Head, ...SplitUnderscore<Tail>]
  : [S];
type BuildSplittedKey<Val, Parts extends string[]> = Parts extends [infer Only extends string]
  ? Val extends (...args: any[]) => any
    ? {
        [K in Only]: (...args: Parameters<Val>) => TransformSplitted<ReturnType<Val>>;
      }
    : {
        [K in `$${Only}`]: TransformSplitted<Val>;
      }
  : Parts extends [infer Head extends string, ...infer Tail extends string[]]
  ? {
      [K in Head]: BuildSplittedKey<Val, Tail>;
    }
  : Val;
type TransformSplitted<T> = T extends object
  ? T extends (...args: any[]) => any
    ? T
    : {
        [K in keyof T & string]: BuildSplittedKey<T[K], SplitUnderscore<K>>;
      }[keyof T & string] extends infer O
    ? MergeUnion<O>
    : never
  : T;
type MergeUnion<U> = (U extends any ? (x: U) => void : never) extends (x: infer I) => void ? I : never;
type AllTopLevel<T extends Record<string, unknown>> = {
  [K in keyof T & string]: BuildSplittedKey<T[K], SplitUnderscore<K>>;
}[keyof T & string] extends infer O
  ? MergeUnion<O>
  : never;
export type UIConstantsType<T extends Record<string, unknown>> = AllTopLevel<T>;
