/** @format */

import { IRepositoryObject, ISearchResults } from "@sap/deepsea-types";
import { ICircuitBreakerInfo, State } from "@sap/dwc-circuit-breaker";
import { CyHttpMessages } from "cypress2/types/net-stubbing";
import { DataWarehouseCloudAPI, IDWCApiSpace } from "../../../../shared/api/SpaceAPI";
import {
  HdiInstance,
  HdiPlatform,
  IDatabaseUser,
  ISpaceResource,
  IStorageConsumptionOverview,
  SpaceStatus,
  StorageType,
} from "../../../../shared/spaces/types";
import { Space } from "../../../../src/components/managespaces/model/Space";
import { MAX_LENGTH_SPACE_LONG_DESCRIPTION } from "../../../../src/components/managespaces/utility/Constants";
import { TestSpace } from "./SpacesTestUtil";
import {
  InterceptAlias,
  InterceptOptions,
  InterceptRouteKey,
  InterceptRouteMethod,
  RouteConfiguration,
  SpaceDefinition,
  SpaceNavigationParameters,
  TestConfiguration,
} from "./Types";

export const InterceptRoutes = {
  spaces_namespaces: { pattern: "**/repository/namespaces**", methods: ["GET"] },
  system_tenantLinks: { pattern: "**/tenant/links**", methods: ["GET"] },
  home_activityTracking: { pattern: "**/repository/userpreferences/ACTIVITY_TRACKING_OFF**", methods: ["GET"] },
  spaces_overview_userCount: { pattern: "**/space/spacesusercount**", methods: ["GET"] },
  system_configuration_guidedExperienceTenantConfiguration: {
    pattern: "**/configurations/DWC_GUIDED_EXPERIENCE_TENANT**",
    methods: ["GET"],
  },
  system_security_customtenantclassification: {
    pattern: "**/dwaas-core/security/customtenantclassification**",
    methods: ["GET"],
  },
  spaces_overview_list_top25: {
    pattern: "**/repository/search/$all?%24top=25**",
    methods: ["GET"],
  },
  spaces_overview_list_top100000: {
    pattern: "**/repository/search/$all?%24top=100000**",
    methods: ["GET"],
  },
  spaces_overview_list_recycleBin: {
    pattern: "**/repository/search/$all?%24top=25**lifecycle_status%3AEQ%3ADELETED**",
    methods: ["GET"],
  },
  spaces_details_databaseUsers: {
    pattern: /\/spaces\/([^/]+)\/databaseusers$/,
    methods: ["GET", "POST", "PUT", "DELETE"],
  },
  spaces_API: {
    pattern: "**/api/v1/content?space**",
    methods: ["GET", "PATCH", "PUT", "DELETE"],
  },
  system_customerhana_config: {
    pattern: "**/security/customerhana/config",
    methods: ["GET"],
  },
  spaces_overview_resources_total_grouped: {
    pattern: "**/resources?grouped=true",
    methods: ["GET"],
  },
  shell_menu: {
    pattern: "**/navigation/menu",
    methods: ["GET"],
  },
  spaces_overview_resources_total: {
    pattern: "**/resources",
    methods: ["GET"],
  },
  spaces_details_databaseusers_resetPassword: {
    pattern: "**/databaseusers/reset",
    methods: ["POST"],
  },
  shell_privileges: {
    pattern: "**/userprivileges**",
    methods: ["GET"],
  },
  /** make this more dynamic via query parameters? */
  shell_breadcrumbs: {
    pattern: "**/repository/spaces?inSpaceManagement=true&details=id%2Cname%2Cdescription%2Cbusiness_name**",
    methods: ["GET"],
  },
  spaces_details_repository: {
    pattern: /\/repository\/spaces\?.*ids=.*&.*inSpaceManagement=true.*/,
    methods: ["GET"],
  },
  spaces_details_monitoring: {
    pattern: /^\/dwaas-core\/resources\/spaces\?(?=.*\btables=true\b)([^#]*)?$/,
    methods: ["GET"],
  },
  spaces_overview_resources_spaces: {
    pattern: /^.*\/dwaas-core\/resources\/spaces(?:\?(?!.*\btables=true\b)[^#]*)?$/,
    methods: ["GET"],
  },
  spaces_overview_restoreSpace: {
    pattern: "**/dwaas-core/repository/**/objects",
    methods: ["PATCH"],
  },
  spaces_details_timedata: {
    pattern: /\/space\/([^/]+)\/timedata$/,
    methods: ["GET", "POST", "DELETE"],
  },
  spaces_details_timedataDependencies: {
    pattern: /\/space\/([^/]+)\/dependencies$/,
    methods: ["GET"],
  },
  spaces_lock: {
    pattern: "**/resources/spaces/locking**",
    methods: ["PUT"],
  },
  shell_notifications_BNS: {
    pattern: "**/notifications/bns**",
    methods: ["GET"],
  },
  shell_notifications_count: {
    pattern: "**/notifications/count**",
    methods: ["GET"],
  },
  system_customerhana_info: {
    pattern: "**/security/customerhana/info**",
    methods: ["GET"],
  },
  spaces_details_HDIContainers: {
    pattern: "**/space/hdi/containers**",
    methods: ["GET", "POST"],
  },
  spaces_details_userassignment: {
    pattern: "**/userassignment**",
    methods: ["GET"],
  },
  spaces_overview_ecnSchedule: {
    pattern: "**/ecn/schedules**",
    methods: ["GET", "DELETE", "POST"],
  },
  system_ecns: {
    pattern: "**/dwaas-core/ecns",
    methods: ["GET", "POST"],
  },
  system_ecns_schedulesConcent: {
    pattern: "**/ecn/schedules/consent",
    methods: ["GET"],
  },
  system_ecns_sizingPlans: {
    pattern: "**/dwaas-core/ecns/sizes/ranges**",
    methods: ["GET"],
  },
  /** TODO make less id specific */
  system_ecn: {
    pattern: "**/dwaas-core/ecns/dstest",
    methods: ["GET", "PUT"],
  },
  system_ecns_readiness: {
    pattern: "**/dwaas-core/ecns/readiness**",
    methods: ["GET"],
  },
  system_customerInfo: {
    pattern: "**/dwaas-core/customerInfo",
    methods: ["POST"],
  },
  home_newsfeed: {
    pattern: "**/newsfeed**",
    methods: ["GET"],
  },
  shell_circuitbreaker: {
    pattern: "**/circuitbreaker**",
    methods: ["GET"],
  },
  system_customerhana_license: {
    pattern: "**/security/customerhana/license",
    methods: ["GET"],
  },
  system_customerhana_flexibleConfiguration: {
    pattern: "**/customerhana/flexible-configuration/configuration-status**",
    methods: ["GET"],
  },
  administration_HDIMapping: {
    pattern: "**/space/hdi/mapping**",
    methods: ["GET", "POST", "DELETE"],
  },
  system_taskframework_schedules_consent: {
    pattern: "**/tf/schedules/consent",
    methods: ["GET"],
  },
  administration_workloadmanagement: {
    pattern: "**/api/v1/workloadmanagement",
    methods: ["GET", "PUT"],
  },
  spaces_selection: {
    pattern: /\/repository\/spaces\?.*inSpaceManagement=false.*/,
    methods: ["GET"],
  },
} as const;

export function interceptGetRepositoryNamespaces() {
  return intercept("GET", "spaces_namespaces", { routeConfiguration: { mock: true } });
}
export function interceptGetTenantLinks() {
  return intercept("GET", "system_tenantLinks", { routeConfiguration: { mock: true } });
}

export function interceptGetActivityTracking() {
  return intercept("GET", "home_activityTracking", { routeConfiguration: { mock: true } });
}

export function interceptGetSpacesUserCount() {
  return intercept("GET", "spaces_overview_userCount", { routeConfiguration: { mock: true }, body: [] });
}

export function interceptGetGuidedExperienceTenantConfiguration() {
  return intercept("GET", "system_configuration_guidedExperienceTenantConfiguration", {
    routeConfiguration: { mock: true },
    body: { type: "None" },
  });
}

export function interceptGetTenantClassification() {
  return intercept("GET", "system_security_customtenantclassification", { routeConfiguration: { mock: true } });
}

export function interceptGetSpacesSearchTop25(
  routeConfiguration: RouteConfiguration = {},
  numberOfSpaces?: number,
  spaces?: Record<string, SpaceDefinition>
) {
  return intercept<Partial<ISearchResults>>("GET", "spaces_overview_list_top25", {
    routeConfiguration,
    body: {
      ["@odata.count"]: numberOfSpaces,
      value: createMockSpaces(numberOfSpaces || 25),
    },
    modifyResponse: (res) => {
      processSearchResponse(res, numberOfSpaces, spaces);
    },
  });
}

/** TODO make hasSpace useful */
export function interceptGetRecycleBinSpacesSearchTop25(routeConfiguration: RouteConfiguration = {}, hasSpace = true) {
  return intercept<ISearchResults>("GET", "spaces_overview_list_recycleBin", {
    routeConfiguration,
    modifyResponse: (res) => {
      res.body["@odata.count"] = hasSpace ? 1 : 0;
      if (hasSpace) {
        res.body.value.push(createMockSpaces(1));
      } else {
        res.body.value = [];
      }
    },
  });
}

export function interceptGetSpacesSearchTop100000(
  routeConfiguration: RouteConfiguration = {},
  numberOfSpaces?: number,
  spaces?: Record<string, SpaceDefinition>
) {
  return intercept<Partial<ISearchResults>>("GET", "spaces_overview_list_top100000", {
    routeConfiguration,
    body: {
      ["@odata.count"]: numberOfSpaces,
      value: createMockSpaces(Math.min(numberOfSpaces ?? 1000, 1000)),
    },
    modifyResponse: (res) => {
      processSearchResponse(res, numberOfSpaces, spaces);
    },
  });
}

export function interceptGetResourcesGrouped(routeConfiguration: RouteConfiguration = {}) {
  return intercept("GET", "spaces_overview_resources_total_grouped", { routeConfiguration });
}

export function interceptGetResourcesTotal(routeConfiguration: RouteConfiguration = {}, storageFull: boolean = false) {
  return intercept<IStorageConsumptionOverview>("GET", "spaces_overview_resources_total", {
    routeConfiguration,
    body: {
      memory: {
        total: 100 * 1000 * 1000 * 1000,
        assigned: 10 * 1000 * 1000 * 1000,
        used: 1 * 1000 * 1000 * 1000,
      },
      disk: {
        total: 50 * 1000 * 1000 * 1000,
        assigned: 5 * 1000 * 1000 * 1000,
        used: 0.5 * 1000 * 1000 * 1000,
      },
      spaces: {
        cold: 5,
        ok: 0,
        critical: 0,
        locked: 0,
        hibernate: 0,
      },
    },
    modifyResponse: (res) => {
      if (storageFull) {
        res.body.disk.assigned = res.body.disk.total;
      }
    },
  });
}

export function interceptGetMenu() {
  return intercept("GET", "shell_menu");
}

export function interceptPostDBUserReset() {
  return intercept("POST", "spaces_details_databaseusers_resetPassword", { routeConfiguration: { mock: true } });
}

export function interceptGetDatabaseUsers(spaces: Record<string, SpaceDefinition>) {
  return intercept<IDatabaseUser[]>("GET", "spaces_details_databaseUsers", {
    modifyResponse: (res, req) => {
      const match = req.url.match(/\/space\/([^/]+)\/databaseusers/);
      const spaceId = match?.[1];
      const hasDatabaseUsers = spaces?.[spaceId]?.runtime?.flags?.hasDatabaseUsers;
      res.body = !!hasDatabaseUsers
        ? [
            {
              spaceId: spaceId,
              username: `${spaceId}#1`,
              isLocked: false,
            },
            {
              spaceId: spaceId,
              username: `${spaceId}#2`,
              isLocked: false,
            },
          ]
        : [];
    },
  });
}

export function interceptCreateDatabaseUser() {
  return intercept("POST", "spaces_details_databaseUsers", { routeConfiguration: { mock: true }, body: [] });
}

export function interceptEditDatabaseUser() {
  return intercept("PUT", "spaces_details_databaseUsers", { routeConfiguration: { mock: true }, body: [] });
}

export function interceptDeleteDatabaseUser() {
  return intercept("DELETE", "spaces_details_databaseUsers", { routeConfiguration: { mock: true }, body: [] });
}

export function interceptGetPrivileges() {
  return intercept("GET", "shell_privileges");
}

export function interceptGetSpaceSelectionList(
  routeConfiguration: RouteConfiguration = {},
  spaces: Record<string, SpaceDefinition> = {},
  numberOfSpaces: number
) {
  return intercept<{ results: IRepositoryObject[] }>("GET", "spaces_selection", {
    routeConfiguration,
    /** TODO add 'body' mock response  */
    modifyResponse: (res) => {
      res.body.results = res.body.results.slice(0, numberOfSpaces);
      Object.entries(spaces).forEach(([spaceId, space]) => {
        const result = res.body.results.find((space) => space.name === spaceId);
        /** TODO overwrite properties here */
      });
    },
  });
}

export function interceptGetBreadcrumbs(routeConfiguration: RouteConfiguration = {}) {
  return intercept("GET", "shell_breadcrumbs", { routeConfiguration });
}

export function interceptGetSpaceMetadata(
  routeConfiguration: RouteConfiguration = {},
  replaceSave: boolean = false,
  replaceDeploy: boolean = false
) {
  return intercept<{ results: IRepositoryObject[] }>("GET", "spaces_details_repository", {
    routeConfiguration,
    modifyResponse: (res) => {
      res.body.results = res.body.results.filter((v) => v.name.startsWith("TESTS_SPACE"));
      if (replaceSave) {
        res.body.results[0]["#objectStatus"] = 2;
      } else if (replaceDeploy) {
        res.body.results[0]["#objectStatus"] = 1;
      }
    },
  });
}

export function interceptGetResourcesSpaceMonitoring(routeConfiguration: RouteConfiguration = {}) {
  return intercept<{ [key: string]: Partial<ISpaceResource> }>("GET", "spaces_details_monitoring", {
    routeConfiguration,
    /** TODO replace with spaceid instead of hardcoding */
    body: {
      [TestSpace.SPACE_0]: {
        memory: {
          assigned: 1000000000,
          used: 565000000,
        },
        disk: {
          assigned: 1000000000,
          used: 1073741824,
        },
        status: SpaceStatus.Ok,
        statusValidToUtc: "",
        priority: 5,
        dataLakeEnabled: false,
        tables: [
          {
            spaceId: TestSpace.SPACE_0,
            schemaName: "SCHEMA1",
            tableName: "Table 1",
            usedDisk: 8192,
            usedMemory: 56925,
            type: StorageType.InMemory,
            recordCount: 10,
          },
          {
            spaceId: TestSpace.SPACE_0,
            schemaName: "SCHEMA2",
            tableName: "Table 2",
            usedDisk: 18192,
            usedMemory: 76925,
            type: StorageType.Disk,
            recordCount: 10,
          },
          {
            spaceId: TestSpace.SPACE_0,
            schemaName: "SCHEMA3",
            tableName: "Table 3",
            usedDisk: 28192,
            usedMemory: 96925,
            type: StorageType.InMemory,
            recordCount: 10,
          },
        ],
      },
    },
  });
}

export function interceptGetResourcesSpaces(
  routeConfiguration: RouteConfiguration = {},
  spaces: Record<string, SpaceDefinition> = {},
  spaceId: string = ""
) {
  return intercept<{ [key: string]: Partial<ISpaceResource> }>("GET", "spaces_overview_resources_spaces", {
    routeConfiguration,
    body: {
      /** TODO figure out a way to get rid of TestSpace.SPACE_0 here.
       * We still need it in case we are navigating from overview to details
       * without spaceid being set in testconfiguration.*/
      [spaceId || TestSpace.SPACE_0]: {
        memory: {
          assigned: 1000000000,
          used: 565000000,
        },
        disk: {
          assigned: 1000000000,
          used: 1073741824,
        },
      },
    },
    modifyResponse: (res) => {
      Object.entries(spaces).forEach(([spaceId, space]) => {
        const currentSpace = (res.body[spaceId] || {}) as ISpaceResource;
        currentSpace.status = space.runtime?.properties?.status || currentSpace.status;
        if (space?.runtime?.flags?.temporarilyUnlocked) {
          currentSpace.status = SpaceStatus.Active;
          currentSpace.statusValidToUtc = new Date(Date.now() + 5 * 60 * 60 * 1000).toString();
          currentSpace.disk.used = currentSpace.disk.assigned + 100;
          currentSpace.memory.used = currentSpace.memory.assigned + 100;
        }
        currentSpace.dataLakeEnabled = space.runtime?.properties?.dataLakeEnabled || false;
        if (space?.runtime?.flags?.noQuota) {
          currentSpace.disk.assigned = 0;
          currentSpace.memory.assigned = 0;
        }
        res.body[spaceId] = currentSpace;
      });
    },
  });
}

export function interceptDeleteSpace(routeConfiguration: RouteConfiguration = {}) {
  return intercept("DELETE", "spaces_API", { routeConfiguration });
}

export function interceptRestoreSpace(routeConfiguration: RouteConfiguration = {}) {
  return intercept("PATCH", "spaces_overview_restoreSpace", { routeConfiguration });
}

export function interceptGetSpaceCSN(spaces: Record<string, SpaceDefinition> = {}) {
  return intercept<DataWarehouseCloudAPI>("GET", "spaces_API", {
    modifyResponse: (res) => {
      const body = res.body;
      const spaceId = Object.keys(body)?.[0];
      const currentSpace =
        res.body[spaceId] ??
        ({
          spaceDefinition: {},
          spaceDefinitionUiProperties: {},
        } as IDWCApiSpace);
      const replaceWithSpace = spaces[spaceId];
      currentSpace.spaceDefinition.longDescription = replaceWithSpace?.designTime?.flags?.veryLongDescription
        ? "x".repeat(MAX_LENGTH_SPACE_LONG_DESCRIPTION - 50)
        : "A new long\n\ndescription";
      currentSpace.spaceDefinition.enableDataLake = replaceWithSpace?.designTime?.properties?.enableDataLake;
      /** TODO spaceDefinitionUiProperties is really ISpaceDefinitionUiProperties**/
      (currentSpace.spaceDefinitionUiProperties as any).host = "abcd.hana.canary-eu10.hanacloud.ondemand.com";

      const replaceWithDBUsers = replaceWithSpace?.designTime?.properties?.dbusers;
      if (!!replaceWithDBUsers) {
        currentSpace.spaceDefinition.dbusers = replaceWithDBUsers;
      } else if (replaceWithSpace?.designTime?.flags?.hasDatabaseUsers) {
        {
          currentSpace.spaceDefinition.dbusers = {
            [`${spaceId}#1`]: {
              ingestion: {
                auditing: {
                  dppRead: {
                    retentionPeriod: 30,
                    isAuditPolicyActive: false,
                  },
                  dppChange: {
                    retentionPeriod: 30,
                    isAuditPolicyActive: false,
                  },
                },
              },
              consumption: {
                consumptionWithGrant: false,
                spaceSchemaAccess: false,
                scriptServerAccess: false,
                localSchemaAccess: false,
                hdiGrantorForCupsAccess: false,
              },
            },
            [`${spaceId}#2`]: {
              ingestion: {
                auditing: {
                  dppRead: {
                    retentionPeriod: 30,
                    isAuditPolicyActive: false,
                  },
                  dppChange: {
                    retentionPeriod: 30,
                    isAuditPolicyActive: false,
                  },
                },
              },
              consumption: {
                consumptionWithGrant: false,
                spaceSchemaAccess: false,
                scriptServerAccess: false,
                localSchemaAccess: false,
                hdiGrantorForCupsAccess: false,
              },
            },
          };
        }
      } else {
        currentSpace.spaceDefinition.dbusers = {};
      }
      currentSpace.spaceDefinition.hdicontainers =
        replaceWithSpace?.designTime?.properties?.hdicontainers ||
        (replaceWithSpace?.designTime?.flags?.hasHDIcontainers
          ? {
              "Cool HDI Container": {},
              "Great HDI Container": {},
              "Normal HDI Container": {},
              "Awesome HDI Container": {},
              "Standard HDI Container": {},
              "Ultra HDI Container": {},
              "Giga HDI Container": {},
            }
          : {});
    },
  });
}
export function interceptCreateDeploySpace(routeConfiguration: RouteConfiguration = {}) {
  return intercept("PUT", "spaces_API", { routeConfiguration });
}
export function interceptSaveSpace(routeConfiguration: RouteConfiguration = {}) {
  return intercept("PATCH", "spaces_API", { routeConfiguration });
}
export function interceptGetTimedata(
  routeConfiguration: RouteConfiguration = {},
  spaces: Record<string, SpaceDefinition>
) {
  /** TODO extract timedata types into shared to be able to use here */
  return intercept<any>("GET", "spaces_details_timedata", {
    routeConfiguration,
    modifyResponse: (res, req) => {
      const match = req.url.match(InterceptRoutes.spaces_details_timedata.pattern);
      const spaceId = match?.[1];

      res.body.timeDimensions = !spaces?.[spaceId]?.runtime?.flags?.hasTimedata
        ? res.body.timeDimensions || []
        : [
            {
              timesettings: {
                from: "1900",
                to: "2050",
                calendarType: "gregorian",
                granularity: "day",
                status: "COMPLETED",
                userCreated: "DWC_DEVELOPER",
                dateCreated: "2020-03-30",
                timeCreated: "07:31:49",
              },
              timeDataTable: {
                business: "Time Table",
                technical: "SAP.TIME.M_TIME_DIMENSION",
                repoId: "4BC80450F71ED2101700070263A73513",
              },
              textMonthTable: {
                business: "Translation Table - Month",
                technical: "SAP.TIME.M_TIME_DIMENSION_TMONTH",
                repoId: "49C80450F71ED2101700070263A73513",
              },
              textDayTable: {
                business: "Translation Table - Day",
                technical: "SAP.TIME.M_TIME_DIMENSION_TDAY",
                repoId: "4AC80450F71ED2101700070263A73513",
              },
              dimensionDayView: {
                business: "Time Dimension - Day",
                technical: "SAP.TIME.VIEW_DIMENSION_DAY",
                repoId: "4FC80450F71ED2101700070263A73513",
              },
              dimensionMonthView: {
                business: "Time Dimension - Month",
                technical: "SAP.TIME.VIEW_DIMENSION_MONTH",
                repoId: "4EC80450F71ED2101700070263A73513",
              },
              dimensionQuarterView: {
                business: "Time Dimension - Quarter",
                technical: "SAP.TIME.VIEW_DIMENSION_QUARTER",
                repoId: "4DC80450F71ED2101700070263A73513",
              },
              dimensionYearView: {
                business: "Time Dimension - Year",
                technical: "SAP.TIME.VIEW_DIMENSION_YEAR",
                repoId: "4CC80450F71ED2101700070263A73513",
              },
            },
          ];
    },
  });
}
export function interceptPostTimedata(routeConfiguration: RouteConfiguration = {}) {
  return intercept("POST", "spaces_details_timedata", { routeConfiguration });
}
export function interceptDeleteTimedata(routeConfiguration: RouteConfiguration = {}) {
  return intercept("DELETE", "spaces_details_timedata", { routeConfiguration });
}
export function interceptGetTimedataDependencies(spaces: Record<string, SpaceDefinition>) {
  return intercept("GET", "spaces_details_timedataDependencies", {
    modifyResponse(res, req) {
      const match = req.url.match(/\/space\/([^/]+)\/dependencies/);
      const spaceId = match?.[1];
      const hasDependencies = spaces[spaceId]?.runtime?.flags?.hasTimedataDependencies;
      res.body = hasDependencies ? [{}] : [];
    },
  });
}
export function interceptPutLock(routeConfiguration: RouteConfiguration = {}) {
  return intercept("PUT", "spaces_lock", { routeConfiguration });
}
export function interceptGetNotificationsBNS() {
  return intercept("GET", "shell_notifications_BNS");
}
export function interceptGetNotificationsCount() {
  return intercept("GET", "shell_notifications_count");
}
export function interceptGetCustomerHanaInfo(
  routeConfiguration: RouteConfiguration = {},
  dataLakeEnabled: boolean = false
) {
  return intercept<any>("GET", "system_customerhana_info", {
    routeConfiguration,
    modifyResponse: (res) => {
      res.body.dataLakeEnabled = dataLakeEnabled;
    },
  });
}
export function interceptGetCustomerHanaConfig() {
  return intercept("GET", "system_customerhana_config", { body: { isScriptServerEnabled: true } });
}
export function interceptGetUserAssignment() {
  return intercept("GET", "spaces_details_userassignment");
}
export function interceptGetHDIContainers(
  routeConfiguration: RouteConfiguration = {},
  hdiEnabled: boolean = false,
  unassignedContainers: boolean = true
) {
  return intercept("GET", "spaces_details_HDIContainers", {
    routeConfiguration,
    body: {
      exists: (routeConfiguration.statusCode ?? 200) === 200 && hdiEnabled,
      hdis: unassignedContainers
        ? ["Cool", "Great", "Normal", "Awesome", "Standard", "Ultra", "Giga"].map((name) => ({
            name: `${name} Unassigned HDI Container`,
          }))
        : [],
    },
  });
}
export function interceptPostHDIContainers(routeConfiguration: RouteConfiguration = {}) {
  return intercept("POST", "spaces_details_HDIContainers", {
    routeConfiguration,
    body: {
      code: "monitorHANA_PostHDIContainers_10001",
      parameters: {
        param: "SPACEDETAILSTEST",
      },
    },
  });
}

export function interceptGetECNSchedules(hasECNSchedules: boolean) {
  return intercept("GET", "spaces_overview_ecnSchedule", {
    body: hasECNSchedules
      ? [
          {
            activationStatus: "ENABLED",
            activity: "GENERATE_START_CHAIN",
            applicationId: "ELASTIC_COMPUTE_NODE",
            changedAt: "2023-12-08T10:52:30.170Z",
            changedBy: "DWCTESTER",
            createdAt: "2023-11-27T19:08:27.977Z",
            createdBy: "DWCTESTER",
            cron: "30 19 */1 * *",
            description: "dstest",
            durationMin: 120,
            externalScheduleId: "8abc2e2-8f43-4581-81b6-50d257b5a2b9",
            objectId: "dstest",
            owner: "TESTUSER",
            scheduleId: "a8abc2e2-8f43-4581-81b6-50d257b5a2b9",
            spaceId: "$$ecn$$",
            uiVariant: "FORM",
            validFrom: "2023-11-27T00:00:00.000Z",
            validTo: "2023-11-29T23:59:00.000Z",
          },
        ]
      : [],
  });
}

export function interceptDeleteECNSchedule() {
  return intercept("DELETE", "spaces_overview_ecnSchedule", { routeConfiguration: { mock: true }, body: [] });
}

export function interceptSchedulesConcent() {
  return intercept("GET", "system_ecns_schedulesConcent", {
    routeConfiguration: { mock: true },
    body: { consent: false },
  });
}

export function interceptPostECNSchedule() {
  return intercept("POST", "spaces_overview_ecnSchedule", {
    routeConfiguration: { mock: true },
    body: {
      scheduleId: "a8abc2e2-8f43-4581-81b6-50d257b5a2b9",
    },
  });
}

export function interceptGetECNS(hasEcns: boolean = false, hasPerformanceClass = null, isECNUpdated: boolean = false) {
  return intercept("GET", "system_ecns", {
    body: {
      ecns: hasEcns
        ? [
            {
              ecnId: "dstest",
              name: isECNUpdated ? "test1" : "test",
              isEnable: true,
              changedAt: "2023-12-12 12:55:58.259000000",
              changedBy: "DWCTESTER",
              sizingPlan: {
                vcpu: 8,
                memory: 128,
                storage: 360,
                performanceClass: hasPerformanceClass || "memory",
              },
              spaces: isECNUpdated
                ? [
                    {
                      spaceId: "TEST_SPACE_0",
                      isEnable: true,
                      asgmtStrtg: "automatic",
                    },
                  ]
                : [],
              runDetails: {
                phase: isECNUpdated ? "READY" : "NOT_READY",
                status: null,
                isRunning: false,
              },
            },
          ]
        : [],
    },
  });
}

export function interceptGetECNSizingPlan(performanceClass = "memory") {
  return intercept("GET", "system_ecns_sizingPlans", {
    body: [
      {
        block: 1,
        compute: 4,
        memory: 64,
        storage: 200,
        performanceClass,
      },
      {
        block: 2,
        compute: 8,
        memory: 128,
        storage: 360,
        performanceClass,
      },
      {
        block: 3,
        compute: 12,
        memory: 192,
        storage: 520,
        performanceClass,
      },
    ],
  });
}

export function interceptPostECN() {
  return intercept("POST", "system_ecns");
}

export function interceptGetECN(isECNUpdated = false, hasPerformanceClass = null): string {
  //* * TODO currently only considers "dstest" ECN */
  return intercept("GET", "system_ecn", {
    body: {
      ecnId: "dstest",
      name: isECNUpdated ? "test1" : "test",
      isEnable: true,
      changedAt: "2023-12-12 12:55:58.259000000",
      changedBy: "DWCTESTER",
      sizingPlan: {
        vcpu: 8,
        memory: 128,
        storage: 360,
        performanceClass: hasPerformanceClass || "memory",
      },
      spaces: isECNUpdated
        ? [
            {
              spaceId: TestSpace.SPACE_0,
              isEnable: true,
              asgmtStrtg: "automatic",
            },
          ]
        : [],
      runDetails: {
        phase: isECNUpdated ? "READY" : "NOT_READY",
        status: null,
        isRunning: false,
      },
    },
  });
}

export function interceptPutECN(routeConfiguration: RouteConfiguration = {}) {
  return intercept("PUT", "system_ecn", { routeConfiguration });
}

export function interceptElasticityReadinessGet(isReady = true) {
  return intercept("GET", "system_ecns_readiness", {
    body: { isReady, limitUsage: !isReady, remainingComputeBlockHours: 2, performanceClass: "memory" },
  });
}

export function interceptPostCustomerInfo() {
  return intercept("POST", "system_customerInfo", {
    routeConfiguration: { mock: true },
    body: { name: "expirationDate", value: null },
  });
}

export function interceptGetNewsfeed() {
  return intercept("GET", "home_newsfeed");
}

export function interceptCircuitbreaker(circuitBreakerState: State = State.Green) {
  return intercept<{
    instanceIndex: number;
    states: Array<Partial<ICircuitBreakerInfo>>;
  }>("GET", "shell_circuitbreaker", {
    modifyResponse: (res) => {
      const result = res.body.states.find((state) => state?.category === "DataHANA");
      if (result) {
        result.state = circuitBreakerState;
      }
    },
  });
}
export function interceptGetLicense() {
  return intercept("GET", "system_customerhana_license");
}

export function interceptGetFlexibleConfiguration() {
  return intercept("GET", "system_customerhana_flexibleConfiguration");
}

export function interceptGetHDIMapping(routeConfiguration: RouteConfiguration = {}, hasMapping: boolean = true) {
  return intercept<HdiInstance>("GET", "administration_HDIMapping", {
    routeConfiguration,
    body: {
      mappings: hasMapping
        ? [
            {
              platform: HdiPlatform.Cloud_Foundry,
              primaryID: "a2",
              secondaryID: "a3",
            },
            {
              platform: HdiPlatform.Kubernetes,
              primaryID: "b2",
              secondaryID: "b3",
            },
          ]
        : [],
    },
  });
}

export function interceptGetTFSchedulesConcent() {
  return intercept("GET", "system_taskframework_schedules_consent", { routeConfiguration: { mock: true }, body: {} });
}

export function interceptPostHDIMapping(routeConfiguration: RouteConfiguration = {}) {
  return intercept("POST", "administration_HDIMapping", { routeConfiguration });
}

export function interceptDeleteHDIMapping(routeConfiguration: RouteConfiguration = {}) {
  return intercept("DELETE", "administration_HDIMapping", { routeConfiguration });
}

export function interceptGetWorkloadmanagement() {
  return intercept("GET", "administration_workloadmanagement");
}

export function interceptPutWorkloadmanagement() {
  return intercept("PUT", "administration_workloadmanagement");
}

export function interceptAll({ intercepts = {}, has, navigation, system }: TestConfiguration = {}) {
  interceptGetGuidedExperienceTenantConfiguration();
  interceptGetTenantClassification();
  interceptGetSpacesUserCount();
  interceptGetSpacesSearchTop25(
    intercepts.spaces_overview_list_top25?.GET,
    has?.SPACES?.overview?.numberOfSpaces,
    has?.SPACES?.spaces
  );
  interceptGetRecycleBinSpacesSearchTop25(intercepts.spaces_overview_list_recycleBin?.GET);
  interceptGetSpacesSearchTop100000(
    intercepts.spaces_overview_list_top25?.GET,
    has?.SPACES?.overview?.numberOfSpaces,
    has?.SPACES?.spaces
  );
  interceptGetResourcesTotal(intercepts.spaces_overview_resources_total?.GET, has?.SPACES?.overview?.storageFull);
  interceptGetResourcesSpaces(
    intercepts.spaces_overview_resources_spaces?.GET,
    // TODO not ideal, but not easy to determine where the call was made from
    has?.SPACESELECTION?.spaces || has?.SPACES?.spaces,
    (navigation?.params as SpaceNavigationParameters)?.spaceId
  );
  interceptGetResourcesSpaceMonitoring(intercepts.spaces_details_monitoring?.GET);
  interceptCreateDeploySpace(intercepts.spaces_API?.PUT);
  interceptSaveSpace(intercepts.spaces_API?.PATCH);
  interceptPutLock(intercepts.spaces_lock?.PUT);
  interceptGetSpaceCSN(has?.SPACES?.spaces);
  interceptGetSpaceSelectionList(
    intercepts.spaces_selection?.GET,
    has?.SPACESELECTION?.spaces,
    has?.SPACESELECTION?.flags?.numberOfSpaces
  );
  interceptGetBreadcrumbs(intercepts.spaces_selection?.GET);
  interceptGetSpaceMetadata(
    intercepts.spaces_details_repository?.GET,
    intercepts.spaces_API?.PATCH?.mock,
    intercepts.spaces_API?.PUT?.mock
  );
  interceptGetHDIContainers(
    intercepts.spaces_details_HDIContainers?.GET,
    system?.hdi?.enabled,
    system?.hdi?.hasUnassignedContainers
  );
  interceptElasticityReadinessGet(system?.elasticity?.isReady);
  interceptGetECNS(
    system?.elasticity?.hasECNs,
    system?.elasticity?.hasPerformanceClass,
    system?.elasticity?.isECNUpdated
  );
  interceptGetECNSchedules(system?.elasticity?.hasECNSchedules);
  interceptDeleteECNSchedule();
  interceptPostECNSchedule();
  interceptPostECN();
  interceptGetECN(system?.elasticity?.isECNUpdated, system?.elasticity?.hasPerformanceClass);
  interceptGetECNSizingPlan(system?.elasticity?.performanceClassType);
  interceptPutECN(intercepts?.system_ecn?.PUT);
  interceptGetTFSchedulesConcent();
  interceptPostHDIContainers(intercepts.spaces_details_HDIContainers?.POST);
  interceptGetMenu();
  interceptGetPrivileges();
  interceptPostCustomerInfo();
  interceptGetNewsfeed();
  interceptCircuitbreaker(system?.circuitBreakerState);
  interceptGetLicense();
  interceptGetFlexibleConfiguration();
  interceptGetCustomerHanaInfo(intercepts.system_customerhana_info?.GET, system?.datalake?.available);
  interceptGetCustomerHanaConfig();
  interceptGetUserAssignment();
  interceptGetNotificationsCount();
  interceptGetNotificationsBNS();
  interceptDeleteSpace(intercepts.spaces_API?.DELETE);
  interceptRestoreSpace(intercepts.spaces_overview_restoreSpace?.PATCH);
  interceptGetTimedata(intercepts.spaces_details_timedata?.GET, has?.SPACES?.spaces);
  interceptPostTimedata(intercepts.spaces_details_timedata?.POST);
  interceptDeleteTimedata(intercepts.spaces_details_timedata?.DELETE);
  interceptGetTimedataDependencies(has?.SPACES?.spaces);
  interceptGetResourcesGrouped(intercepts.spaces_overview_resources_total_grouped?.GET);
  interceptPostDBUserReset();
  interceptGetDatabaseUsers(has?.SPACES?.spaces);
  interceptCreateDatabaseUser();
  interceptEditDatabaseUser();
  interceptDeleteDatabaseUser();
  interceptGetActivityTracking();
  interceptGetTenantLinks();
  interceptGetRepositoryNamespaces();
  interceptGetHDIMapping(intercepts.administration_HDIMapping?.GET, system?.hdi?.hasHDIMapping);
  interceptPostHDIMapping(intercepts.administration_HDIMapping?.POST);
  interceptDeleteHDIMapping(intercepts.administration_HDIMapping?.DELETE);
  interceptGetWorkloadmanagement();
  interceptPutWorkloadmanagement();
}

function intercept<
  T = unknown,
  K extends InterceptRouteKey = InterceptRouteKey,
  M extends InterceptRouteMethod<K> = InterceptRouteMethod<K>
>(method: M, interceptKey: K, options: InterceptOptions<T> = {}): InterceptAlias<K, M> {
  const pattern = InterceptRoutes[interceptKey].pattern;
  const alias = getInterceptAlias(method, interceptKey);
  cy.intercept<any, any>(method, pattern, (req) => {
    const { body = {}, routeConfiguration, modifyResponse } = options || {};
    const { mock, statusCode } = routeConfiguration || {};
    if (mock || statusCode) {
      req.reply(statusCode ?? 200, body);
    } else if (modifyResponse) {
      req.continue((res) => modifyResponse(res, req));
    } else {
      req.continue();
    }
  }).as(alias.substring(1));
  return alias;
}

function processSearchResponse(
  res: CyHttpMessages.IncomingHttpResponse<Partial<ISearchResults>>,
  numberOfSpaces: number,
  spaces: Record<string, SpaceDefinition> = {}
) {
  res.body.value = res.body.value.filter((v) => v.name.startsWith("TESTS_SPACE"));
  res.body["@odata.count"] = numberOfSpaces ?? res.body.value.length;
  Object.entries(spaces).forEach(([spaceId, space]) => {
    const responseSpace = res.body.value.find((v) => v.name === spaceId);
    let currentSpace: Partial<Space>;
    if (!responseSpace) {
      currentSpace = {
        name: spaceId,
        business_name: space?.designTime?.properties?.business_name || spaceId,
      };
      res.body.value.push(currentSpace);
    } else {
      currentSpace = responseSpace as Partial<Space>;
    }
    // if(space.runtime.properties.status === SpaceStatus.Unknown) {
    currentSpace.deployment_date =
      space?.designTime?.properties?.deployment_date || responseSpace?.deployment_date || "";
    if (space?.designTime?.flags?.isCorrupt) {
      currentSpace.deployment_status = undefined;
    } else {
      currentSpace.deployment_status =
        space?.designTime?.properties?.deployment_status ?? responseSpace?.deployment_status ?? "0";
    }
  });
  if (numberOfSpaces) {
    if (numberOfSpaces <= res.body.value.length) {
      res.body.value.splice(numberOfSpaces);
    } else {
      res.body.value = res.body.value.concat(createMockSpaces(numberOfSpaces - res.body.value.length));
    }
  }
}

function createMockSpaces(count: number, prefix = "MOCK_SPACE"): Array<Partial<Space>> {
  return Array.from({ length: Math.min(count, 25) }, (_, i) => ({
    name: `${prefix}_${i}`,
    business_name: `${prefix}_${i}`,
  }));
}

export function getInterceptAlias<K extends InterceptRouteKey, M extends InterceptRouteMethod<K>>(
  method: M,
  key: K
): InterceptAlias<K, M> {
  return `@${method}_${InterceptRoutes[key].pattern.toString().replace(/\W+/g, "")}`;
}
