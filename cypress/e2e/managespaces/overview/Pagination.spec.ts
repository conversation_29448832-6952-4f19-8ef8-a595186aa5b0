/** @format */

import { ID } from "../_util/IDs";
import { getInterceptAlias } from "../_util/Intercepts";
import { initTest, waitNotBusy } from "../_util/SpacesTestUtil";

describe("Pagination", () => {
  const featureflags = {
    DWCO_INFRA_SPACE_PERMISSIONS: true,
    INFRA_SCOPE_DEPENDENT_ROLES: true,
    INFRA_DWC_TWO_TENANT_MODE: true,
    DWCO_SPACES_LOGICAL_DELETE: false,
  };
  describe("Has Pagination", () => {
    it("more than 25 spaces", () => {
      initTest({
        has: { SPACES: { overview: { numberOfSpaces: 30 } } },
        intercepts: {
          spaces_overview_list_top25: { GET: { mock: true } },
          spaces_overview_resources_spaces: { GET: { mock: true } },
        },
        system: { featureflags },
      });
      cy.get(ID.SPACES.OVERVIEW.$FOOTER).should("exist");
      cy.get(ID.SPACES.OVERVIEW.FOOTER.BUTTONS.$PAGINATION)
        .should("exist")
        .children()
        .should("have.length", 2)
        .first()
        .should("have.attr", "aria-selected", "true");
    });

    it("Paging should change selection and retrieve next page", () => {
      initTest({
        has: { SPACES: { overview: { numberOfSpaces: 30 } } },
        intercepts: {
          spaces_overview_list_top25: { GET: { mock: true } },
          spaces_overview_resources_spaces: { GET: { mock: true } },
        },
        system: { featureflags },
      });
      cy.get(ID.SPACES.OVERVIEW.FOOTER.BUTTONS.$PAGINATION)
        .should("exist")
        .and("be.visible")
        .children()
        .should("have.length", 2)
        .eq(1)
        .click();
      cy.wait(getInterceptAlias("GET", "spaces_overview_list_top25"));
      waitNotBusy();
    });

    it("Back to first page after navigation from table to tile view", () => {
      initTest({
        has: { SPACES: { overview: { numberOfSpaces: 30 } } },
        intercepts: {
          spaces_overview_list_top25: { GET: { mock: true } },
          spaces_overview_resources_spaces: { GET: { mock: true } },
        },
        system: { featureflags },
      });
      cy.get(ID.SPACES.OVERVIEW.FOOTER.BUTTONS.$PAGINATION).should("exist").children().first().next().click();
      cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.LAYOUTSWITCH.$TABLE).click();
      waitNotBusy();
      cy.get(ID.SPACES.OVERVIEW.LAYOUT.$TABLE).should("exist");
      cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.LAYOUTSWITCH.$TILE).click();
      waitNotBusy();
      cy.get(ID.SPACES.OVERVIEW.FOOTER.BUTTONS.$PAGINATION)
        .children()
        .should("have.length", 2)
        .first()
        .should("have.attr", "aria-selected", "true");
    });

    it("Navigating from table to tile view with more than 25 spaces", () => {
      initTest({
        has: { SPACES: { overview: { numberOfSpaces: 30 } } },
        intercepts: {
          spaces_overview_list_top25: { GET: { mock: true } },
          spaces_overview_resources_spaces: { GET: { mock: true } },
        },
        system: { featureflags },
      });
      cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.LAYOUTSWITCH.$TABLE).click();
      waitNotBusy();
      cy.get(ID.SPACES.OVERVIEW.LAYOUT.$TABLE).should("exist");
      cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.LAYOUTSWITCH.$TILE).click();
      waitNotBusy();
      cy.get(ID.SPACES.OVERVIEW.$FOOTER).should("be.visible");
    });
  });

  describe("No pagination", () => {
    it("less than 25 spaces", () => {
      initTest({
        has: { SPACES: { overview: { numberOfSpaces: 5 } } },
        intercepts: {
          spaces_overview_list_top25: { GET: { mock: true } },
          spaces_overview_resources_spaces: { GET: { mock: true } },
        },
        system: { featureflags },
      });
      cy.get(ID.SPACES.OVERVIEW.$FOOTER).should("not.be.visible");
    });
    it("table-view has no paging", () => {
      initTest({
        has: { SPACES: { overview: { numberOfSpaces: 30 } } },
        intercepts: {
          spaces_overview_list_top25: { GET: { mock: true } },
          spaces_overview_resources_spaces: { GET: { mock: true } },
        },
        system: { featureflags },
      });
      cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.LAYOUTSWITCH.$TABLE).click();
      waitNotBusy();
      cy.get(ID.SPACES.OVERVIEW.LAYOUT.$TABLE).should("exist");
      cy.get(ID.SPACES.OVERVIEW.$FOOTER).should("not.be.visible");
    });
    it("Navigating from table to tile view with less than 25 spaces", () => {
      initTest({
        has: { SPACES: { overview: { numberOfSpaces: 5 } } },
        intercepts: {
          spaces_overview_list_top25: { GET: { mock: true } },
          spaces_overview_resources_spaces: { GET: { mock: true } },
        },
        system: { featureflags },
      });
      cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.LAYOUTSWITCH.$TABLE).click();
      waitNotBusy();
      cy.get(ID.SPACES.OVERVIEW.LAYOUT.$TABLE).should("exist");
      cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.LAYOUTSWITCH.$TILE).click();
      waitNotBusy();
      cy.get(ID.SPACES.OVERVIEW.$FOOTER).should("not.be.visible");
    });
  });
});
