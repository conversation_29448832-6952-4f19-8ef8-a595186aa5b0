/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { NOT_AVAILABLE, NOT_INITIALIZED } from "../../../../src/components/managespaces/utility/Constants";
import { ID } from "../_util/IDs";
import { interceptGetResourcesSpaces, interceptGetSpacesSearchTop25 } from "../_util/Intercepts";
import { initTest, waitNotBusy } from "../_util/SpacesTestUtil";

describe("Header", () => {
  const featureflags = {
    DWCO_INFRA_SPACE_PERMISSIONS: true,
    INFRA_SCOPE_DEPENDENT_ROLES: true,
    INFRA_DWC_TWO_TENANT_MODE: true,
    DWCO_CLI_CREATE_DELETE_DATABASEUSER: false,
    DWCO_SPACES_LOGICAL_DELETE: false,
  };

  describe("Loading", () => {
    it("Successful", () => {
      initTest({ system: { featureflags } });
      checkHeaderMetrics();
      expectCreateSpaceButtonEnabled(true);
    });
    describe("Fail", () => {
      it("Spaces search", () => {
        initTest({
          intercepts: { spaces_overview_list_top25: { GET: { statusCode: 500 } } },
          system: { featureflags },
        });
        checkHeaderMetrics(false);
        expectCreateSpaceButtonEnabled(false);
      });

      it("Space resources", () => {
        initTest({
          intercepts: { spaces_overview_resources_spaces: { GET: { statusCode: 500 } } },
          system: { featureflags },
        });
        checkHeaderMetrics(true, false);
        expectCreateSpaceButtonEnabled(true);
      });

      it("Total resources", () => {
        initTest({
          intercepts: { spaces_overview_resources_total: { GET: { statusCode: 500 } } },
          system: { featureflags },
        });
        checkHeaderMetrics(true, true, false);
        expectCreateSpaceButtonEnabled(false);
      });
    });
    describe("Successful retry", () => {
      it("Spaces", () => {
        initTest({
          intercepts: { spaces_overview_list_top25: { GET: { statusCode: 500 } } },
          system: { featureflags },
        });
        expectCreateSpaceButtonEnabled(false);
        checkHeaderMetrics(false, true);
        interceptGetResourcesSpaces();
        interceptGetSpacesSearchTop25();
        cy.get(ID.SPACES.OVERVIEW.HEADER.MESSAGESTRIP.LOADERROR.$LINK).click();
        waitNotBusy();
        cy.get(ID.SPACES.OVERVIEW.HEADER.MESSAGESTRIP.$LOADERROR).should("not.exist");
        checkHeaderMetrics();
      });

      it("Space resources", () => {
        initTest({
          intercepts: { spaces_overview_resources_spaces: { GET: { statusCode: 500 } } },
          system: { featureflags },
        });
        expectCreateSpaceButtonEnabled(true);
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(1).CONTENT.BUSYINDICATOR.$DISK).should("not.exist");
        interceptGetResourcesSpaces();
        cy.get(ID.SPACES.OVERVIEW.HEADER.MESSAGESTRIP.LOADERROR.$LINK).click();
        waitNotBusy();
        cy.get(ID.SPACES.OVERVIEW.HEADER.MESSAGESTRIP.$LOADERROR).should("not.exist");
        cy.get(ID.SPACES.OVERVIEW.getSpaceCard(1).CONTENT.BUSYINDICATOR.$DISK).should("not.exist");
        checkHeaderMetrics();
      });
    });
  });

  function expectCreateSpaceButtonEnabled(enabled: boolean = true): void {
    cy.get(ID.SPACES.OVERVIEW.TOOLBAR.BUTTON.$CREATESPACE)
      .should("exist")
      .should(enabled ? "not.be.disabled" : "be.disabled");
  }

  function checkHeaderMetrics(
    spacesSearchSuccessful: boolean = true,
    spaceResourcesSuccessful: boolean = true,
    resourcesTotalSuccessful: boolean = true
  ) {
    cy.get(ID.SPACES.OVERVIEW.HEADER.$METRICS).should("be.visible");
    cy.get(ID.SPACES.OVERVIEW.HEADER.METRICS.STATUS.$TOTAL).should(
      "contain",
      spacesSearchSuccessful ? "5" : NOT_AVAILABLE
    );
    cy.get(ID.SPACES.OVERVIEW.HEADER.METRICS.STATUS.$COLD).should(
      resourcesTotalSuccessful ? "not.contain" : "contain",
      NOT_INITIALIZED
    );
    cy.get(ID.SPACES.OVERVIEW.HEADER.METRICS.STATUS.$CRITICAL).should(
      resourcesTotalSuccessful ? "not.contain" : "contain",
      NOT_INITIALIZED
    );
    cy.get(ID.SPACES.OVERVIEW.HEADER.METRICS.STATUS.$OK).should(
      resourcesTotalSuccessful ? "not.contain" : "contain",
      NOT_INITIALIZED
    );
    cy.get(ID.SPACES.OVERVIEW.HEADER.METRICS.STATUS.$LOCKED).should(
      resourcesTotalSuccessful ? "not.contain" : "contain",
      NOT_INITIALIZED
    );
  }
});
