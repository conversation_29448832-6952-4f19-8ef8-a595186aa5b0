/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { ID } from "./../_util/IDs";
import { initTest, TestUser } from "./../_util/SpacesTestUtil";

describe("Spaces Table", () => {
  const initSidePanelTest = (showDeleted: boolean = false) => {
    initTest({ as: TestUser.DWC_ADMINISTRATOR_ONLY, is: { showDeleted } }, {});

    cy.log("Space Table should be visible");
    cy.get(ID.SPACES.OVERVIEW.SIDEPANEL.TABLE.$SPACES).should("exist").and("be.visible");
  };

  it("Default behavior - 'All Spaces' should be selected", () => {
    initSidePanelTest();
    cy.url().should("not.contain", "showDeleted");
    cy.log("'All Spaces' should be selected");
    cy.get(ID.SPACES.OVERVIEW.SIDEPANEL.TABLE.SPACES.ITEM.$ALLSPACES)
      .should("exist")
      .and("have.attr", "aria-selected", "true");
  });

  it("Direct jump to recycle bin - 'Recycle Bin' should be selected", () => {
    initSidePanelTest(true);
    cy.url().should("contain", "showDeleted=true");
    cy.log("'Recycle Bin' should be selected");
    cy.get(ID.SPACES.OVERVIEW.SIDEPANEL.TABLE.SPACES.ITEM.$DELETEDSPACES)
      .should("exist")
      .and("have.attr", "aria-selected", "true");
  });
});
