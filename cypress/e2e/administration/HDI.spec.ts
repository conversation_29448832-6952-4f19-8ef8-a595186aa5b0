/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { ID } from "../managespaces/_util/IDs";
import { getInterceptAlias } from "../managespaces/_util/Intercepts";
import { initTest, TestSpace, TestUser } from "../managespaces/_util/SpacesTestUtil";

describe("HDI Instance Mapping", () => {
  const featureflags = { DWCO_SPACES_HDI_MAPPING: true };
  it("Navigate from spacedetails to configuration", () => {
    initTest({
      as: TestUser.DWC_ADMINISTRATOR,
      navigation: { to: "SPACES", params: { spaceId: TestSpace.SPACE_0 } },
      intercepts: { administration_HDIMapping: { GET: { mock: true, successful: true } } },
      system: { featureflags },
    });
    cy.get(ID.SPACE.DETAILS.SECTION.DATABASE.HDICONTAINER.BUTTON.$NAVTOMAPPING)
      .scrollIntoView()
      .should("exist")
      .and("be.visible")
      .click();
    cy.get(ID.ADMINISTRATION.HDIMAPPING.$TABLE).should("exist").and("be.visible");
  });

  it("Error fetching mappings", () => {
    initTest({
      as: TestUser.DWC_ADMINISTRATOR,
      navigation: { to: "ADMINISTRATION_HDI" },
      intercepts: { administration_HDIMapping: { GET: { successful: false } } },
      system: { featureflags },
    });
    cy.get(ID.ADMINISTRATION.HDIMAPPING.$TABLE).should("exist").and("be.visible");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.TABLE.$NODATA).should("contain", "loadHDIMappingError");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.BUTTON.$SAVE).should("exist").and("be.disabled");
  });
  it("No mappings", () => {
    initTest({
      as: TestUser.DWC_ADMINISTRATOR,
      navigation: { to: "ADMINISTRATION_HDI" },
      intercepts: { administration_HDIMapping: { GET: { mock: true } } },
      system: { featureflags, hdi: { hasHDIMapping: false } },
    });
    cy.get(ID.ADMINISTRATION.HDIMAPPING.$TABLE).should("exist").and("be.visible");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.TABLE.$NODATA).should("contain", "noHdiMappings");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.BUTTON.$SAVE).should("exist").and("be.disabled");
  });
  it("Has mappings", () => {
    initTest({
      as: TestUser.DWC_ADMINISTRATOR,
      navigation: { to: "ADMINISTRATION_HDI" },
      intercepts: { administration_HDIMapping: { GET: { mock: true } } },
      system: { featureflags },
    });
    cy.get(ID.ADMINISTRATION.HDIMAPPING.$TABLE).should("exist").and("be.visible");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.TABLE.$NODATA).should("not.exist");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.BUTTON.$SAVE).should("exist").and("be.disabled");
  });
  it("Mapping can be removed", () => {
    initTest({
      as: TestUser.DWC_ADMINISTRATOR,
      navigation: { to: "ADMINISTRATION_HDI" },
      intercepts: {
        administration_HDIMapping: { GET: { mock: true }, DELETE: { mock: true, successful: true } },
      },
      system: { featureflags },
    });
    cy.get(ID.ADMINISTRATION.HDIMAPPING.$TABLE).should("exist").and("be.visible");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.TABLE.$NODATA).should("not.exist");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.BUTTON.$SAVE).should("exist").and("be.disabled");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.TABLE.getRowDeleteButton(0).$ID).should("exist").click({ force: true });
    cy.get(ID.ADMINISTRATION.HDIMAPPING.BUTTON.$SAVE).should("not.be.disabled").click();
    cy.wait(getInterceptAlias("DELETE", "administration_HDIMapping"));
  });
  it("New mapping can be added", () => {
    initTest({
      as: TestUser.DWC_ADMINISTRATOR,
      navigation: { to: "ADMINISTRATION_HDI" },
      intercepts: {
        administration_HDIMapping: { GET: { mock: true }, POST: { mock: true } },
      },
      system: { featureflags, hdi: { hasHDIMapping: false } },
    });
    cy.get(ID.ADMINISTRATION.HDIMAPPING.$TABLE).should("exist").and("be.visible");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.TABLE.$NODATA).should("exist");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.BUTTON.$SAVE).should("exist").and("be.disabled");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.BUTTON.$CREATE).should("exist").and("not.be.disabled").click({ force: true });
    cy.get(ID.ADMINISTRATION.HDIMAPPING.TABLE.$NODATA).should("not.exist");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.BUTTON.$SAVE).should("not.be.disabled").click();
    cy.wait(getInterceptAlias("POST", "administration_HDIMapping")).then((xhr) => {
      expect(xhr.response.statusCode).to.equal(200);
      expect(xhr.request.body).to.have.property("platform");
      expect(xhr.request.body).to.have.property("primaryID");
      expect(xhr.request.body).to.have.property("secondaryID");
    });
    cy.wait(getInterceptAlias("GET", "administration_HDIMapping"));
    cy.get(ID.ADMINISTRATION.HDIMAPPING.TABLE.$NODATA).should("exist");
  });

  it("Add multiple new mappings", () => {
    initTest({
      as: TestUser.DWC_ADMINISTRATOR,
      navigation: { to: "ADMINISTRATION_HDI" },
      intercepts: {
        administration_HDIMapping: { GET: { mock: true }, POST: { mock: true } },
      },
      system: { featureflags, hdi: { hasHDIMapping: false } },
    });
    cy.get(ID.ADMINISTRATION.HDIMAPPING.$TABLE).should("exist").and("be.visible");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.TABLE.$NODATA).should("exist");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.BUTTON.$SAVE).should("exist").and("be.disabled");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.BUTTON.$CREATE).should("exist").and("not.be.disabled").click({ force: true });
    // Important to wait as the hash is generated based on the time
    cy.wait(1);
    cy.get(ID.ADMINISTRATION.HDIMAPPING.BUTTON.$CREATE).click({ force: true });
    cy.get(ID.ADMINISTRATION.HDIMAPPING.TABLE.$NODATA).should("not.exist");
    cy.get(ID.ADMINISTRATION.HDIMAPPING.BUTTON.$SAVE).should("not.be.disabled").click();
    const intercept = getInterceptAlias("POST", "administration_HDIMapping");
    cy.wait([intercept, intercept]).then((xhr) => {
      xhr.every((xhr) => {
        expect(xhr.response.statusCode).to.equal(200) &&
          expect(xhr.request.body).to.have.property("platform") &&
          expect(xhr.request.body).to.have.property("primaryID") &&
          expect(xhr.request.body).to.have.property("secondaryID");
      });
    });
    cy.wait(getInterceptAlias("GET", "administration_HDIMapping"));
    cy.get(ID.ADMINISTRATION.HDIMAPPING.TABLE.$NODATA).should("exist");
  });
});
