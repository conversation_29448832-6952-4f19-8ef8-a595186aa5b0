/**
 * Copyright 2025 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { CLASS, ID } from "../managespaces/_util/IDs";
import { getInterceptAlias } from "../managespaces/_util/Intercepts";
import { TestSpace, TestUser, initTest, waitNotBusy } from "../managespaces/_util/SpacesTestUtil";

function clickRefreshUntilNotBusy(retries = 10) {
  if (retries === 0) {
    throw new Error("Max refresh clicks reached.");
  }
  cy.wait(1000);
  cy.document().then((doc) => {
    const indicator = doc.querySelector(ID.ADMINISTRATION.WORKLOADMANAGEMENT.CLASSESTABLE.$BUSYINDICATOR);
    if (indicator) {
      cy.log(`Busy Indicator found - Try ${11 - retries} / 10`);
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.CLASSESTABLE.BUTTON.$REFRESH).click();
      cy.wait(getInterceptAlias("GET", "administration_workloadmanagement"));
      clickRefreshUntilNotBusy(retries - 1);
    } else {
      cy.log("No Classes Table Busy Indicator found");
      waitNotBusy();
    }
  });
}

function switchAssignment(assignment: "space" | "group", expectedRows) {
  waitNotBusy();
  cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.getAssignment(assignment)).click();
  cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.getAssignment(assignment)).should("have.class", "sapMRbSel");
  cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.BUTTON.$SAVE).should("exist").and("not.be.disabled");
  cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.BUTTON.$SAVE).click();
  waitNotBusy();
  cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.$CONFIRMATION).should("exist");
  cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.$CONFIRMATION).contains("YES").click();
  cy.get(CLASS.$TOASTMESSAGE).should("exist").should("contain", "workloadManagementUpdateStartedSuccess");
  cy.get(CLASS.BUSYINDICATORS.$LOCAL).should("exist");
  clickRefreshUntilNotBusy();
  cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.$CLASSESTABLE)
    .find("tbody > tr:not(.sapUiTableRowHidden):not(.sapUiTableHeaderRow)")
    .then((rows) => {
      const foundRows = Array.from(rows).map((row) => row.innerText.trim());
      expectedRows.forEach((row) => {
        const isFound = foundRows.some((text) => text.includes(row));
        assert.isTrue(isFound, `"${row}" should be in the table`);
      });
    });
}

describe("WorkloadManagement", () => {
  describe("DS00-1028: Workload Management UI", () => {
    beforeEach(() => {
      initTest({
        as: TestUser.DWC_ADMINISTRATOR,
        navigation: { to: "ADMINISTRATION_WORKLOADMANAGEMENT" },
        system: { featureflags: { DWCO_WORKLOAD_MANAGEMENT_UI: true, DWCO_WORKLOAD_MANAGEMENT_GROUPS: false } },
      });
    });

    it("Check Layout", () => {
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.$PAGE).should("exist");
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.$CLASSESTABLE).should("exist");
    });

    it("Can filter", () => {
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.CLASSESTABLE.$SEARCHFIELD).type(TestSpace.SPACE_2);
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.CLASSESTABLE.getCell(0, 0).$ID).should("contain", TestSpace.SPACE_2);
    });

    it("Can switch to custom configuration", () => {
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.CLASSESTABLE.$SEARCHFIELD).type(TestSpace.SPACE_2);
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.CLASSESTABLE.getCell(0, 0).$ID).should("contain", TestSpace.SPACE_2);
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.CLASSESTABLE.getCell(0, 0).$ID).click();
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.$CLASS).should("exist");

      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.CLASS.$TYPESELECT).click();
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.CLASS.TYPESELECT.$CUSTOMCONFIG).click({
        force: true,
      });
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.CLASS.SLIDER.$TOTALMEMORYLIMIT)
        .should("exist")
        .and("be.visible")
        .and("not.be.disabled")
        .clear()
        .type("20");
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.CLASS.SLIDER.$TOTALTHREADLIMIT)
        .should("exist")
        .and("be.visible")
        .and("not.be.disabled")
        .clear()
        .type("10");
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.CLASS.BUTTON.$SAVE).click();
      cy.get(CLASS.$TOASTMESSAGE).should("exist").should("contain", "workloadManagementUpdateSuccess");
    });
    it("Can switch to default configuration", () => {
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.CLASSESTABLE.$SEARCHFIELD).type(TestSpace.SPACE_2);
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.CLASSESTABLE.getCell(0, 0).$ID).should("contain", TestSpace.SPACE_2);
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.CLASSESTABLE.getCell(0, 0).$ID).click();
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.$CLASS).should("exist");

      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.CLASS.$TYPESELECT).click();
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.CLASS.TYPESELECT.$DEFAULTCONFIG).click({
        force: true,
      });
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.CLASS.BUTTON.$SAVE).click();
      cy.get(CLASS.$TOASTMESSAGE).should("exist").should("contain", "workloadManagementUpdateSuccess");
    });
  });

  describe("DS00-1029: Workload Management Groups", () => {
    beforeEach(() => {
      initTest({
        as: TestUser.DWC_ADMINISTRATOR,
        navigation: { to: "ADMINISTRATION_WORKLOADMANAGEMENT" },
        system: { featureflags: { DWCO_WORKLOAD_MANAGEMENT_UI: true, DWCO_WORKLOAD_MANAGEMENT_GROUPS: true } },
      });
    });

    it("Check Layout", () => {
      cy.wait(getInterceptAlias("GET", "administration_workloadmanagement"));
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.$PAGE).should("exist");
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.$CLASSESTABLE).should("exist");
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.CLASSESTABLE.BUTTON.$IMPORT).should("exist");
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.CLASSESTABLE.BUTTON.$EXPORT).should("exist");
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.$TYPERADIOGROUP).should("exist");
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.TYPERADIOGROUP.ITEM.$SPACE).should("exist");
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.TYPERADIOGROUP.ITEM.$GROUP).should("exist");
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.BUTTON.$SAVE).should("exist").and("be.disabled");
    });

    it("Switch to Group", () => {
      const expectedRows = [
        "SQL Access",
        "Modelling",
        "Data Management",
        "Analytic Consumption",
        "SAP Analytics Cloud System Operation",
        "SAP Analytics Cloud Data Management",
        "SAP Analytics Cloud Interactive Operations",
        "SAP Analytics Cloud Long-Running Operations",
      ];
      switchAssignment("group", expectedRows);
    });

    it("Switch to Space", () => {
      const expectedRows = [
        TestSpace.SPACE_0,
        TestSpace.SPACE_1,
        TestSpace.SPACE_2,
        TestSpace.SPACE_3,
        TestSpace.SPACE_4,
      ];
      switchAssignment("space", expectedRows);
    });

    it("Check Import Dialog", () => {
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.CLASSESTABLE.BUTTON.$IMPORT).click();
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.$IMPORT).should("exist").and("be.visible");
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.IMPORT.BUTTON.$IMPORT)
        .should("exist")
        .and("have.attr", "disabled", "disabled");
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.IMPORT.$FILEUPLOADER).should("exist");
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.IMPORT.BUTTON.$CANCEL).click();
      cy.get(ID.ADMINISTRATION.WORKLOADMANAGEMENT.DIALOG.$IMPORT).should("not.exist");
    });
  });
});
