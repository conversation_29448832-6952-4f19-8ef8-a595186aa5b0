{"hasErrors": false, "cubeModel": {"identifier": {"key": ""}, "text": "", "sourceModel": {"factSources": {"AM1_Stacking_Variabl": {"text": "AM1_Stacking Variable", "dataEntity": {"key": "AM1_Stacking_Variable"}, "parameterMappings": {"RESTRICTED_MEASURE_VARIABLE": {"mappingType": "AnalyticModelSourceParameterMappingType.ConstantRanges", "constantValue": [{"lowValue": "1515", "sign": "AnalyticModelDefaultRangeSign.INCLUDE", "option": "AnalyticModelDefaultRangeOption.LE"}]}}}}, "dimensionSources": {}}, "exposedAssociations": {}, "attributes": {"ItemID": {"attributeType": "AnalyticModelAttributeType.FactSourceAttribute", "attributeMapping": {"AM1_Stacking_Variabl": {"key": "ItemID"}}, "text": "ItemID", "duplicated": false}, "Opportunity": {"attributeType": "AnalyticModelAttributeType.FactSourceAttribute", "attributeMapping": {"AM1_Stacking_Variabl": {"key": "Opportunity"}}, "text": "Opportunity_text", "duplicated": false}, "Product": {"attributeType": "AnalyticModelAttributeType.FactSourceAttribute", "attributeMapping": {"AM1_Stacking_Variabl": {"key": "Product"}}, "text": "Product", "duplicated": false}, "Unit": {"attributeType": "AnalyticModelAttributeType.FactSourceAttribute", "attributeMapping": {"AM1_Stacking_Variabl": {"key": "Unit"}}, "text": "Unit", "duplicated": false}, "Currency": {"attributeType": "AnalyticModelAttributeType.FactSourceAttribute", "attributeMapping": {"AM1_Stacking_Variabl": {"key": "<PERSON><PERSON><PERSON><PERSON>"}}, "text": "<PERSON><PERSON><PERSON><PERSON>", "duplicated": false}, "FactAttr": {"attributeType": "AnalyticModelAttributeType.FactSourceAttribute", "attributeMapping": {"AM1_Stacking_Variabl": {"key": "FactAttr"}}, "text": "FactAttr", "duplicated": false}, "ItemStatus": {"attributeType": "AnalyticModelAttributeType.FactSourceAttribute", "attributeMapping": {"AM1_Stacking_Variabl": {"key": "ItemStatus"}}, "text": "ItemStatus ID", "duplicated": false}, "ControllingArea": {"attributeType": "AnalyticModelAttributeType.FactSourceAttribute", "attributeMapping": {"AM1_Stacking_Variabl": {"key": "ControllingArea"}}, "text": "Controlling Area", "duplicated": false}, "SendingCostCenter": {"attributeType": "AnalyticModelAttributeType.FactSourceAttribute", "attributeMapping": {"AM1_Stacking_Variabl": {"key": "SendingCostCenter"}}, "text": "Sending Cost Center", "duplicated": false}, "ReceivingCostCenter": {"attributeType": "AnalyticModelAttributeType.FactSourceAttribute", "attributeMapping": {"AM1_Stacking_Variabl": {"key": "ReceivingCostCenter"}}, "text": "Receiving Cost Center", "duplicated": false}, "SRC_DATE": {"attributeType": "AnalyticModelAttributeType.FactSourceAttribute", "attributeMapping": {"AM1_Stacking_Variabl": {"key": "SRC_DATE"}}, "text": "SRC_DATE", "duplicated": false}, "SRC_STRING": {"attributeType": "AnalyticModelAttributeType.FactSourceAttribute", "attributeMapping": {"AM1_Stacking_Variabl": {"key": "SRC_STRING"}}, "text": "SRC_STRING", "duplicated": false}}, "measures": {"Quantity": {"measureType": "AnalyticModelMeasureType.FactSourceMeasure", "sourceKey": "AM1_Stacking_Variabl", "key": "Quantity", "text": "Quantity"}, "Value": {"measureType": "AnalyticModelMeasureType.FactSourceMeasure", "sourceKey": "AM1_Stacking_Variabl", "key": "Value", "text": "Value"}, "Cal_measure": {"measureType": "AnalyticModelMeasureType.FactSourceMeasure", "sourceKey": "AM1_Stacking_Variabl", "key": "Cal_measure", "text": "Cal_measure"}, "base_measure": {"measureType": "AnalyticModelMeasureType.FactSourceMeasure", "sourceKey": "AM1_Stacking_Variabl", "key": "base_measure", "text": "base_measure"}, "Restricted_Measure": {"measureType": "AnalyticModelMeasureType.FactSourceMeasure", "sourceKey": "AM1_Stacking_Variabl", "key": "Restricted_Measure", "text": "Restricted Measure"}, "Calculated_Measure": {"measureType": "AnalyticModelMeasureType.FactSourceMeasure", "sourceKey": "AM1_Stacking_Variabl", "key": "Calculated_Measure", "text": "Calculated Measure"}}, "version": "1.3.8", "supportedCapabilities": {"_DWC_AM_EDITABLE_DIMENSION_NAMES": true}}}