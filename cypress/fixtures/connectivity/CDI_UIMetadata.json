{"typeId": "CDI", "configuration": {"RemoteSourceDescription": {"ConnectionProperties": {"displayName": "ConnectionDetails", "isRepeatable": "false", "name": "configurations", "PropertyEntry": [{"allowAlterWhenSuspended": "false", "displayName": "URL", "isRepeatable": "false", "isRequired": "true", "name": "url", "type": "URL", "automationName": "url"}, {"allowAlterWhenSuspended": "false", "displayName": "RootPath", "isRepeatable": "false", "isRequired": "false", "name": "rootPath", "type": "STRING"}], "PropertyGroup": [{"displayName": "CloudConnector", "name": "gateway", "PropertyEntry": [{"defaultValue": "false", "description": "Use Cloud Connector", "displayName": "UseCloudConnector", "isRequired": "false", "name": "cloud_connector", "type": "ENUM", "EnumValue": [{"displayName": "true", "name": "true"}, {"displayName": "false", "name": "false"}]}, {"displayName": "CloudConnectorLocation", "isRequired": "false", "name": "cloudConnectorLocation", "type": "STRING", "Activator": {"name": "cloud_connector", "Value": "true"}}, {"defaultValue": "false", "description": "Virtual Destination", "displayName": "VirtualDestination", "isRequired": "false", "name": "virtual_destination", "type": "ENUM", "EnumValue": [{"displayName": "DeriveVirtualHost", "name": "false"}, {"displayName": "EnterVirtualHost", "name": "true"}], "Activator": {"name": "cloud_connector", "Value": "true"}}, {"displayName": "VirtualHost", "isRequired": "true", "name": "virtualHost", "type": "URL", "Activator": [{"name": "cloud_connector", "Value": "true"}, {"name": "virtual_destination", "Value": "true"}]}, {"displayName": "VirtualPort", "isRequired": "true", "name": "virtualPort", "type": "PORT", "maxLength": 5, "Activator": [{"name": "cloud_connector", "Value": "true"}, {"name": "virtual_destination", "Value": "true"}]}]}, {"displayName": "Authentication", "isRepeatable": "false", "name": "connection", "PropertyEntry": [{"visible": false, "allowAlterWhenSuspended": "true", "defaultValue": "V2", "description": "Only OData V4 is supported", "displayName": "Version", "isRepeatable": "false", "isRequired": "false", "name": "version", "type": "ENUM", "EnumValue": {"displayName": "V4", "name": "V4"}}, {"allowAlterWhenSuspended": "true", "defaultValue": "OAuth2", "description": "Authentication Mechanism", "displayName": "AuthenticationType", "isRepeatable": "false", "isRequired": "true", "name": "auth_mech", "type": "ENUM", "EnumValue": [{"displayName": "UserNameAndPassword", "name": "Basic"}, {"displayName": "OAuth2", "name": "OAuth2"}, {"displayName": "NoAuthentication", "name": "NoAuth"}]}]}, {"displayName": "OAuth2", "isRepeatable": "false", "name": "oauth2", "PropertyEntry": [{"allowAlterWhenSuspended": "true", "defaultValue": "client_credentials", "description": "OAuth2 Grant Type", "displayName": "OAuth2GrantType", "isRepeatable": "false", "isRequired": "false", "name": "oauth2_grant_type", "type": "ENUM", "EnumValue": [{"displayName": "UserNameAndPassword", "name": "password"}, {"displayName": "ClientCredentials", "name": "client_credentials"}], "Activator": {"name": "auth_mech", "Value": "OAuth2"}}, {"allowAlterWhenSuspended": "true", "description": "OAuth2 Token Endpoint", "displayName": "OAuth2TokenEndpoint", "isRepeatable": "false", "isRequired": "true", "name": "oauth2_token_endpoint", "type": "STRING", "Activator": {"name": "auth_mech", "Value": "OAuth2"}}, {"allowAlterWhenSuspended": "true", "description": "OAuth2 Scope", "displayName": "OAuth2Scope", "isRepeatable": "false", "isRequired": "false", "name": "oauth2_scope", "type": "STRING", "Activator": {"name": "auth_mech", "Value": "OAuth2"}}, {"allowAlterWhenSuspended": "true", "description": "OAuth2 Resource", "displayName": "OAuth2Resource", "isRepeatable": "false", "isRequired": "false", "name": "oauth2_resource", "type": "STRING", "Activator": {"name": "auth_mech", "Value": "OAuth2"}}, {"allowAlterWhenSuspended": "true", "defaultValue": "token", "description": "OAuth2 Response Type", "displayName": "OAuth2ResponseType", "isRepeatable": "false", "isRequired": "false", "name": "oauth2_response_type", "type": "ENUM", "EnumValue": [{"displayName": "none", "name": "none"}, {"displayName": "token", "name": "token"}], "Activator": {"name": "auth_mech", "Value": "OAuth2"}}, {"allowAlterWhenSuspended": "true", "defaultValue": "url_encoded", "description": "OAuth2 Token Request Content Type", "displayName": "OAuth2TokenRequestContentType", "isRepeatable": "false", "isRequired": "false", "name": "oauth2_token_request_content_type", "type": "ENUM", "EnumValue": [{"displayName": "URLEncoded", "name": "url_encoded"}, {"displayName": "JSON", "name": "json"}], "Activator": {"name": "auth_mech", "Value": "OAuth2"}}], "Activator": {"name": "auth_mech", "Value": "OAuth2"}}]}, "CredentialProperties": {"displayName": "Credential", "name": "credentials", "CredentialMode": {"visible": false, "allowAlterWhenSuspended": "false", "displayName": "CredentialsMode", "isRepeatable": "false", "isRequired": "false", "name": "credentials_mode", "type": "ENUM", "EnumValue": [{"displayName": "None", "name": "none"}, {"displayName": "SecondaryCredentials", "name": "secondarycredentials"}, {"displayName": "TechnicalUser", "name": "technicaluser"}]}, "CredentialEntry": [{"displayName": "CredentialUserNameAndPassword", "name": "credential", "properties": [{"name": "user", "displayName": "UserName", "isRequired": "true", "type": "STRING"}, {"name": "password", "protect": "true", "displayName": "Password", "isRequired": "true", "type": "STRING"}], "Activator": {"name": "auth_mech", "Value": "Basic"}}, {"displayName": "CredentialOAuth2", "name": "oauth2_credential", "properties": [{"name": "user", "displayName": "UserName", "isRequired": "true", "type": "STRING"}, {"name": "password", "protect": "true", "displayName": "PasswordOAuth2", "isRequired": "true", "type": "STRING"}], "Activator": [{"name": "auth_mech", "Value": "OAuth2"}, {"name": "oauth2_grant_type", "Value": "password"}]}, {"displayName": "CredentialOAuth2", "name": "oauth2_client_credential", "properties": [{"name": "user", "displayName": "ClientID", "isRequired": "true", "type": "STRING"}, {"name": "password", "protect": "true", "displayName": "ClientSecret", "isRequired": "true", "type": "STRING"}], "Activator": [{"name": "auth_mech", "Value": "OAuth2"}, {"name": "oauth2_grant_type", "Value": "client_credentials"}]}], "Activator": {"name": "credentials_mode", "Value": "technicaluser"}}, "ConnectionFeatures": {"displayName": "Features", "name": "features", "PropertyEntry": [{"displayName": "RemoteTables", "name": "remoteTables", "type": "LABEL", "description": "RemoteTablesDescription", "featurePopOverText": "RemoteTablesPopOverDPAgent", "DependsOn": [{"name": "dpAgent"}]}, {"displayName": "DataProvisioningAgent", "isRequired": "false", "name": "dpAgent", "type": "DPAGENT"}, {"displayName": "DataFlows", "name": "dataflows", "type": "LABEL", "description": "DataFlowsDescription", "featurePopOverText": "DataFlowsPopOverCloudConnector"}]}}}}