{
  "results": [
      {
          "space_id": "F44F01707E51C04419003EDE32E782BE",
          "id": "995001707E51C04419003EDE32E782BE",
          "#isI18nEnabled": "false",
          "#repositoryPackage": "BDC_FOS_COSTCENTER",
          "#creatorBusinessName": "Insight Application Onboarding",
          "#ownerBusinessName": "Insight Application Onboarding",
          "#repairedCsn": {
              "definitions": {
                  "CostCenterText": {
                      "kind": "entity",
                      "@EndUserText.label": "CostCenterText",
                      "@ObjectModel.modelingPattern": {
                          "#": "DATA_STRUCTURE"
                      },
                      "@ObjectModel.supportedCapabilities": [
                          {
                              "#": "DATA_STRUCTURE"
                          }
                      ],
                      "@DataWarehouse.delta": {
                          "type": {
                              "#": "ACTIVE"
                          },
                          "deltaFromEntities": [
                              "CostCenterText_Delta"
                          ]
                      },
                      "elements": {
                          "CostCenter": {
                              "@EndUserText.label": "CostCenter",
                              "type": "cds.String",
                              "key": true,
                              "notNull": true,
                              "length": 10
                          },
                          "ControllingArea": {
                              "@EndUserText.label": "ControllingArea",
                              "type": "cds.String",
                              "key": true,
                              "notNull": true,
                              "length": 4
                          },
                          "Language": {
                              "@EndUserText.label": "Language",
                              "type": "cds.String",
                              "key": true,
                              "notNull": true,
                              "length": 2
                          },
                          "ValidityEndDate": {
                              "@EndUserText.label": "ValidityEndDate",
                              "type": "cds.Date",
                              "key": true,
                              "notNull": true
                          },
                          "ValidityStartDate": {
                              "@EndUserText.label": "ValidityStartDate",
                              "type": "cds.Date",
                              "key": false
                          },
                          "CostCenterName": {
                              "@EndUserText.label": "CostCenterName",
                              "type": "cds.String",
                              "key": false,
                              "length": 20
                          },
                          "CostCenterDescription": {
                              "@EndUserText.label": "CostCenterDescription",
                              "type": "cds.String",
                              "key": false,
                              "length": 40
                          },
                          "AUTOPREFIX___OPERATION_TYPE": {
                              "@EndUserText.label": "AUTOPREFIX___OPERATION_TYPE",
                              "type": "cds.String",
                              "key": false,
                              "length": 5000
                          },
                          "AUTOPREFIX___TIMESTAMP": {
                              "@EndUserText.label": "AUTOPREFIX___TIMESTAMP",
                              "type": "cds.String",
                              "key": false,
                              "length": 5000
                          },
                          "load_type_8995a2862a8343bd8390aaa82c46e881": {
                              "@EndUserText.label": "load_type_8995a2862a8343bd8390aaa82c46e881",
                              "type": "cds.String",
                              "key": false,
                              "length": 5000
                          },
                          "run_id_8995a2862a8343bd8390aaa82c46e881": {
                              "@EndUserText.label": "run_id_8995a2862a8343bd8390aaa82c46e881",
                              "type": "cds.String",
                              "key": false,
                              "length": 5000
                          },
                          "CostCenterTextOID": {
                              "@EndUserText.label": "CostCenterTextOID",
                              "type": "cds.String",
                              "key": false,
                              "length": 128
                          }
                      },
                      "@DataWarehouse.consumption.external": false,
                      "query": {
                          "SELECT": {
                              "from": {
                                  "ref": [
                                      "CostCenterText_Delta"
                                  ]
                              },
                              "columns": [
                                  {
                                      "ref": [
                                          "CostCenter"
                                      ]
                                  },
                                  {
                                      "ref": [
                                          "ControllingArea"
                                      ]
                                  },
                                  {
                                      "ref": [
                                          "Language"
                                      ]
                                  },
                                  {
                                      "ref": [
                                          "ValidityEndDate"
                                      ]
                                  },
                                  {
                                      "ref": [
                                          "ValidityStartDate"
                                      ]
                                  },
                                  {
                                      "ref": [
                                          "CostCenterName"
                                      ]
                                  },
                                  {
                                      "ref": [
                                          "CostCenterDescription"
                                      ]
                                  },
                                  {
                                      "ref": [
                                          "AUTOPREFIX___OPERATION_TYPE"
                                      ]
                                  },
                                  {
                                      "ref": [
                                          "AUTOPREFIX___TIMESTAMP"
                                      ]
                                  },
                                  {
                                      "ref": [
                                          "load_type_8995a2862a8343bd8390aaa82c46e881"
                                      ]
                                  },
                                  {
                                      "ref": [
                                          "run_id_8995a2862a8343bd8390aaa82c46e881"
                                      ]
                                  },
                                  {
                                      "ref": [
                                          "CostCenterTextOID"
                                      ]
                                  }
                              ],
                              "where": [
                                  {
                                      "ref": [
                                          "Change_Type"
                                      ]
                                  },
                                  "not",
                                  "in",
                                  {
                                      "list": [
                                          {
                                              "val": "D"
                                          },
                                          {
                                              "val": "M"
                                          }
                                      ]
                                  }
                              ]
                          }
                      },
                      "@DataWarehouse.repository.technicalType": {
                          "#": "DWC_LOCAL_TABLE"
                      },
                      "_meta": {
                          "dependencies": {
                              "folderAssignment": null
                          }
                      }
                  },
                  "CostCenterText_Delta": {
                      "kind": "entity",
                      "@EndUserText.label": "CostCenterText",
                      "@ObjectModel.modelingPattern": {
                          "#": "DATA_STRUCTURE"
                      },
                      "@ObjectModel.supportedCapabilities": [
                          {
                              "#": "DATA_STRUCTURE"
                          }
                      ],
                      "@DataWarehouse.delta": {
                          "type": {
                              "#": "UPSERT"
                          },
                          "dateTimeElement": {
                              "=": "Change_Date"
                          },
                          "modeElement": {
                              "=": "Change_Type"
                          }
                      },
                      "@DataWarehouse.enclosingObject": "CostCenterText",
                      "elements": {
                          "CostCenter": {
                              "@EndUserText.label": "CostCenter",
                              "type": "cds.String",
                              "key": true,
                              "notNull": true,
                              "length": 10
                          },
                          "ControllingArea": {
                              "@EndUserText.label": "ControllingArea",
                              "type": "cds.String",
                              "key": true,
                              "notNull": true,
                              "length": 4
                          },
                          "Language": {
                              "@EndUserText.label": "Language",
                              "type": "cds.String",
                              "key": true,
                              "notNull": true,
                              "length": 2
                          },
                          "ValidityEndDate": {
                              "@EndUserText.label": "ValidityEndDate",
                              "type": "cds.Date",
                              "key": true,
                              "notNull": true
                          },
                          "ValidityStartDate": {
                              "@EndUserText.label": "ValidityStartDate",
                              "type": "cds.Date",
                              "key": false
                          },
                          "CostCenterName": {
                              "@EndUserText.label": "CostCenterName",
                              "type": "cds.String",
                              "key": false,
                              "length": 20
                          },
                          "CostCenterDescription": {
                              "@EndUserText.label": "CostCenterDescription",
                              "type": "cds.String",
                              "key": false,
                              "length": 40
                          },
                          "AUTOPREFIX___OPERATION_TYPE": {
                              "@EndUserText.label": "AUTOPREFIX___OPERATION_TYPE",
                              "type": "cds.String",
                              "key": false,
                              "length": 5000
                          },
                          "AUTOPREFIX___TIMESTAMP": {
                              "@EndUserText.label": "AUTOPREFIX___TIMESTAMP",
                              "type": "cds.String",
                              "key": false,
                              "length": 5000
                          },
                          "load_type_8995a2862a8343bd8390aaa82c46e881": {
                              "@EndUserText.label": "load_type_8995a2862a8343bd8390aaa82c46e881",
                              "type": "cds.String",
                              "key": false,
                              "length": 5000
                          },
                          "run_id_8995a2862a8343bd8390aaa82c46e881": {
                              "@EndUserText.label": "run_id_8995a2862a8343bd8390aaa82c46e881",
                              "type": "cds.String",
                              "key": false,
                              "length": 5000
                          },
                          "CostCenterTextOID": {
                              "@EndUserText.label": "CostCenterTextOID",
                              "type": "cds.String",
                              "key": false,
                              "length": 128
                          },
                          "Change_Type": {
                              "@EndUserText.label": "Change_Type",
                              "type": "cds.String",
                              "key": false,
                              "notNull": true,
                              "length": 1,
                              "default": {
                                  "val": "I"
                              }
                          },
                          "Change_Date": {
                              "@EndUserText.label": "Change_Date",
                              "type": "cds.Timestamp",
                              "key": false,
                              "notNull": true,
                              "default": {
                                  "func": "CURRENT_UTCTIMESTAMP"
                              }
                          }
                      },
                      "@DataWarehouse.partition": {
                    "by": {
                        "#": "HASH"
                    },
                    "elements": [
                        {
                            "=": "CostCenterName"
                        },
                    ],
                    "numberOfPartitions": 10
                },
                      "_meta": {
                          "dependencies": {
                              "folderAssignment": null
                          }
                      }
                  }
              },
              "extensions": [
                {
                  "annotate": "CostCenterText",
                  "@DataWarehouse.partition": {
                    "by": {
                        "#": "HASH"
                    },
                    "elements": [
                        {
                            "=": "CostCenterName"
                        },
                        {
                            "=": "CostCenter"
                        }
                    ],
                    "numberOfPartitions": 10
                }
                }
              ]
          },
          "#extensibleProperties":["@DataWarehouse.partition"],
          "#objectPathIdentifier": "entity:BDC_TEST_DP\\CostCenterText"
      }
  ]
}