/** @format */
import * as cyu from "../cypressUtil";
import { CONSTANTS } from "../cypressUtil";
import { DIAGRAM, DIAGRAM_CONTEXT_PAD } from "../descriptor";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    cyu.visitModelA();
  });

  it("DW24-668 - clicking on a node should keep the contextPad visible while you are still hovering on it", () => {
    // Case 1: Hover on a node, click on it, then move the mouse out of the node by hovering on another symbol
    cyu.performActionOnDiagramNode("hover", CONSTANTS.ADDRESS);
    cyu.performActionOnDiagramNode("click", CONSTANTS.ADDRESS);

    cy.wait(2000); // Wait for potential diagram animations that could hide the context pad
    cy.get(DIAGRAM_CONTEXT_PAD.CREATE_SUB_CONCEPT).should("be.visible");

    cyu.performActionOnDiagramNode("mouseout", CONSTANTS.ADDRESS);
    cyu.getDiagramRelationshipLink(CONSTANTS.HAS_TOOL).trigger("mouseover");
    cy.get(DIAGRAM_CONTEXT_PAD.CREATE_SUB_CONCEPT).should("not.exist");
    cyu.getDiagramRelationshipLink(CONSTANTS.HAS_TOOL).trigger("mouseout");
    cyu.performActionOnDiagramNode("hover", CONSTANTS.YANG);

    // Case 2: Hovering back to an already selected node should keep the context pad visible
    cyu.performActionOnDiagramNode("hover", CONSTANTS.ADDRESS);
    cy.wait(2000); // Wait for potential diagram animations that could hide the context pad
    cy.get(DIAGRAM_CONTEXT_PAD.CREATE_SUB_CONCEPT).should("be.visible");

    // Case 3: Even if the mouseout does not occur, the previous context pad should be removed
    cyu.performActionOnDiagramNode("hover", CONSTANTS.ANIMAL);

    // ensure the selected Address is still selected and the Animal is hovered
    cyu.getDiagramNode(CONSTANTS.ANIMAL).should("have.class", "sapGalileiHover");

    cyu.getUi5(DIAGRAM.INNER_EDITOR).then((editor) => {
      const innerEditor = editor.getInnerEditor();
      const selectedSymbol = innerEditor.selectedSymbols.get(0);
      cy.wrap(innerEditor.extension.hasContextButtonPad(selectedSymbol)).as("selectedSymbolHasContextButtonPad");
      cy.get("@selectedSymbolHasContextButtonPad").should("be.false");

      const highlightedSymbol = editor
        .getInnerEditor()
        .getAllSymbols()
        .find((symbol) => symbol.object.displayName === CONSTANTS.ANIMAL);
      cy.wrap(innerEditor.extension.hasContextButtonPad(highlightedSymbol)).as("highlightedSymbolHasContextButtonPad");
      cy.get("@highlightedSymbolHasContextButtonPad").should("be.true");
    });

    cy.get(DIAGRAM_CONTEXT_PAD.REMOVE).should("be.visible");
  });

  it("DW24-553 - BUGFIX - should be able to delete a concept immediately after its creation if it was created by using the tree buttons", () => {
    cyu.createConcept();
    cyu.validateNewlyCreatedConcept(CONSTANTS.FIRST_CONCEPT_LABEL);
    cyu.getWorkingNode(CONSTANTS.FIRST_CONCEPT_LABEL).click();
    cyu.createStringProperty(CONSTANTS.FIRST_CONCEPT_LABEL);
    cyu.validateNewlyCreatedStringProperty(CONSTANTS.FIRST_PROPERTY_LABEL);
    cyu.performActionOnDiagramNode("click", CONSTANTS.FIRST_CONCEPT_LABEL);
    cy.get(DIAGRAM_CONTEXT_PAD.DELETE).click({ force: true }); // force cause is detached due to hover logic
    cyu.confirmDeletionOfConceptAndProperties();
    cyu.validateAfterResourceRemoval(CONSTANTS.FIRST_CONCEPT_LABEL, CONSTANTS.MODEL_A);
  });
});
