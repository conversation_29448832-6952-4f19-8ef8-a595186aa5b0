/** @format */

import * as cyu from "../../../cypressUtil";
import { CONSTANTS, original } from "../../../cypressUtil";
import { PROPERTY_DETAILS, TREE_PANEL, WORKING_ONTOLOGY_TAB } from "../../../descriptor";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    cyu.visitModelA();
  });

  it("clicking on nodes should show the context buttons only of the current selected node", () => {
    // Animal context buttons are not visible
    cyu.getWorkingNode("Animal").find("button").eq(0).should("not.be.visible");
    cyu.getWorkingNode("Animal").find("button").eq(1).should("not.be.visible");

    cyu.getWorkingNode("Animal").click();

    // Context buttons are visible after click
    cyu.getWorkingNode("Animal").find("button").eq(0).should("be.visible");
    cyu.getWorkingNode("Animal").find("button").eq(1).should("be.visible");

    cyu.toggleWorkingNode("Animal");

    cyu.getWorkingNode("Species").click();

    // Animal context buttons shot not be visible anymore
    cyu.getWorkingNode("Animal").find("button").eq(0).should("not.be.visible");
    cyu.getWorkingNode("Animal").find("button").eq(1).should("not.be.visible");

    // Species does not have buttons since is a property
    cyu.getWorkingNode("Species").find("button").should("not.exist");

    // click on dog
    cyu.getWorkingNode("Dog").click();

    // dog context buttons are visible
    cyu.getWorkingNode("Dog").find("button").eq(0).should("be.visible");
    cyu.getWorkingNode("Dog").find("button").eq(1).should("be.visible");

    // species previous clicked node still does not have context buttons
    cyu.getWorkingNode("Species").find("button").should("not.exist");
    cyu.getWorkingNode("Species").find("button").should("not.exist");
  });

  it("a non editable concept should have a gray color, an editable concept should not", () => {
    // icon from external resource should have partial opacity
    cyu.getWorkingNode("Widget").find(".sapUiIcon.partialOpacityResourceIcon").should("exist");

    // label from external  resource  should have a `sapThemeMediumText` css class
    cyu
      .getWorkingNode("Widget") // get line
      .contains("Widget", original) // get label
      .should("have.class", "sapThemeMediumText");

    // non-external resource icon should not have partial opacity
    cyu.getWorkingNode("Address").should("exist");
    cyu.getWorkingNode("Address").find(".sapUiIcon.partialOpacityResourceIcon").should("not.exist");

    // non-external resource label should not have a `sapThemeMediumText` css class
    cyu
      .getWorkingNode("Address") // get line
      .contains("Address", original) // get label
      .should("not.have.class", "sapThemeMediumText");

    cyu.toggleWorkingNode("Animal");
    cyu.toggleWorkingNode("Dog");
    cyu.toggleWorkingNode("Service Dog");
    cyu
      .getWorkingNode("Job Title") // get line
      .contains("Job Title", original) // get label
      .should("have.class", "sapThemeMediumText");
  });

  it("(BUGFIX) - should navigate to a resource that is related to a object property", () => {
    // url was changing to /property instead of /class for the Fizz concept
    cyu.getWorkingNode("Fizz").click();
    cy.url().should("contains", "concept");
    cy.url().should("contains", "Fizz");
  });

  it("should search for inexistent word and check the empty search illustration", () => {
    cy.get(WORKING_ONTOLOGY_TAB.EMPTY_SEARCH_ILLUSTRATION).should("not.exist");
    cy.get(WORKING_ONTOLOGY_TAB.SEARCH).type("hkahskah", { force: true });
    cyu.waitDebounce();
    cy.get(WORKING_ONTOLOGY_TAB.EMPTY_SEARCH_ILLUSTRATION).should("be.visible");
    cy.get(TREE_PANEL.WORKING_ONTOLOGY_SEARCH_TREE).should("not.exist");
    cy.get(WORKING_ONTOLOGY_TAB.SEARCH).clear();
    cy.get(WORKING_ONTOLOGY_TAB.EMPTY_SEARCH_ILLUSTRATION).should("not.exist");
  });

  it("working model external resources should have partial opacity or different label color", () => {
    // resource icon should have partial opacity
    cyu.getWorkingNode("Animal").should("exist");
    cyu.getWorkingNode("Animal").find(".sapUiIcon.partialOpacityResourceIcon").should("exist");

    // resource label should have a `sapThemeMediumText` css class
    cyu.getWorkingNode("Animal").contains("Animal", original).should("have.class", "sapThemeMediumText");
  });

  it("working model resources should have partial opacity or different label color and highlighted search", () => {
    cyu.getWorkingTree().get(".sapThemeLightText", original).should("not.exist");
    cy.get(WORKING_ONTOLOGY_TAB.SEARCH).type("ni", { force: true });
    cyu.getSearchWorkingTree().closest(".sapMPage").should("have.class", "sapUiLocalBusy");
    cyu.getSearchWorkingTree().closest(".sapMPage").should("not.have.class", "sapUiLocalBusy");
    cyu.waitDebounce();

    cyu.getSearchWorkingTree().get(".sapThemeLightText", original).should("exist");

    // resource icon should have partial opacity
    cyu.getSearchWorkingNode("Animal").should("exist");
    cyu.getSearchWorkingNode("Animal").find(".sapUiIcon.partialOpacityResourceIcon").should("exist");

    // resource label should have a `sapThemeMediumText` css class
    cyu
      .getSearchWorkingNode("Animal")
      .contains("Animal", original)
      .children()
      .should("have.class", "sapThemeMediumText");

    // resource label should also have a `sapThemeLightText` css class
    cyu
      .getSearchWorkingNode("Animal")
      .contains("Animal", original)
      .children()
      .should("have.class", "sapThemeLightText");
    cyu.getSearchWorkingNode("Animal").contains("Animal", original).children().should("have.length", 3);

    cy.get(WORKING_ONTOLOGY_TAB.SEARCH).click().focused().clear();
    cyu.getWorkingTree().closest(".sapMPage").should("have.class", "sapUiLocalBusy");
    cyu.getWorkingTree().closest(".sapMPage").should("not.have.class", "sapUiLocalBusy");
  });

  it("should position tree items in the order within working group: relationships, other properties and subconcepts", () => {
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.createSubConcept(CONSTANTS.ADDRESS);
    cyu.validateNewlyCreatedSubConcept(CONSTANTS.FIRST_SUB_CONCEPT_LABEL);
    cyu.typeOnLabel("{backspace}".repeat(CONSTANTS.FIRST_SUB_CONCEPT_LABEL.length));
    cyu.typeOnLabel("BaseAddress");
    cyu.getWorkingNode(CONSTANTS.MODEL_A).click();

    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.detailsLabelInputIs(CONSTANTS.ADDRESS);
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.createRelationship(CONSTANTS.ADDRESS);
    cyu.toggleTargetConcept(CONSTANTS.YING);
    cyu.selectedTokenIs([CONSTANTS.YING], PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT);
    cyu.selectWorkingTreeNode(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
    cyu.typeOnLabel("{backspace}".repeat(CONSTANTS.FIRST_RELATIONSHIP_LABEL.length));
    cyu.typeOnLabel("Ying_Rel");
    cyu.getWorkingNode("Ying_Rel").click();

    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.detailsLabelInputIs(CONSTANTS.ADDRESS);
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.createRelationship(CONSTANTS.ADDRESS);
    cyu.toggleTargetConcept(CONSTANTS.CHILD);
    cyu.selectedTokenIs([CONSTANTS.CHILD], PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT);
    cyu.selectWorkingTreeNode(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
    cyu.typeOnLabel("{backspace}".repeat(CONSTANTS.FIRST_RELATIONSHIP_LABEL.length));
    cyu.typeOnLabel("Child_Rel");
    cyu.getWorkingNode("Child_Rel").click();

    cyu.workingTreeNodeLabelIs(2, "Child_Rel");
    cyu.workingTreeNodeLabelIs(3, "Ying_Rel");
    cyu.workingTreeNodeLabelIs(4, CONSTANTS.CITY);
    cyu.workingTreeNodeLabelIs(5, "BaseAddress");
  });

  it("should sort within properties by referenced/working and then relations on top under each group", () => {
    cyu.getDiagramNode(CONSTANTS.SERVICE_DOG).click();
    cyu.detailsLabelInputIs(CONSTANTS.SERVICE_DOG);
    cyu.toggleWorkingNode(CONSTANTS.SERVICE_DOG);
    cyu.workingTreeNodeLabelIs(7, "Assisting Service");
    cyu.workingTreeNodeLabelIs(8, CONSTANTS.JOB_TITLE);
    cyu.workingTreeNodeLabelIs(9, "Name");

    cyu.getWorkingNode(CONSTANTS.EMPLOYEE).click();
    cyu.createRelationshipOnSubConcept(CONSTANTS.EMPLOYEE, CONSTANTS.CHILD);
    cyu.getWorkingNode(12).click();
    cy.wait(1000);
    cyu.typeOnLabel("{backspace}".repeat(CONSTANTS.FIRST_RELATIONSHIP_LABEL.length));
    cyu.typeOnLabel("Child_Rel");
    cyu.getWorkingNode("Child_Rel").click();

    cyu.selectWorkingTreeNode(CONSTANTS.SERVICE_DOG);
    cyu.createStringPropertyOnSubConcept(CONSTANTS.SERVICE_DOG);
    cyu.selectWorkingTreeNode(CONSTANTS.FIRST_PROPERTY_LABEL);
    cyu.typeOnLabel("{backspace}".repeat(CONSTANTS.FIRST_PROPERTY_LABEL.length));
    cyu.typeOnLabel("Yawn");
    cyu.getWorkingNode(CONSTANTS.SERVICE_DOG).click();

    cyu.createRelationshipOnSubConcept(CONSTANTS.SERVICE_DOG, CONSTANTS.WIDGET);
    cyu.selectWorkingTreeNode(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
    cyu.typeOnLabel("{backspace}".repeat(CONSTANTS.FIRST_RELATIONSHIP_LABEL.length));
    cyu.typeOnLabel("Uses_widget");
    cyu.getWorkingNode("Uses_widget").click();

    cyu.workingTreeNodeLabelIs(6, CONSTANTS.SERVICE_DOG);
    cyu.workingTreeNodeLabelIs(7, "Uses_widget");
    cyu.workingTreeNodeLabelIs(8, "Assisting Service");
    cyu.workingTreeNodeLabelIs(9, "Yawn");
    cyu.workingTreeNodeLabelIs(10, "Child_Rel");
    cyu.workingTreeNodeLabelIs(11, "Job Title");
    cyu.workingTreeNodeLabelIs(13, CONSTANTS.EMPLOYEE);
    cyu.workingTreeNodeLabelIs(14, "Child_Rel");
  });

  it("referenced parent concepts should appear in parenthesis next to concepts", () => {
    cyu.getWorkingNode(3).contains("Employee", original).should("exist");
    cyu.getWorkingNode(3).contains("(Person)", original).should("exist");

    cy.log("reference-only parent concepts should not appear as a node on the tree after performing a search");
    cy.get(WORKING_ONTOLOGY_TAB.SEARCH).type("Person");
    cyu.waitDebounce();
    cy.get(WORKING_ONTOLOGY_TAB.EMPTY_SEARCH_ILLUSTRATION).should("be.visible");
  });

  // TODO: This test is failing and will be fixed - DW24-528

  /* it("node buttons should be visible when interacted with", () => {
    // Model Concept buttons
    cyu.getWorkingNode("Address").click();
    cyu.getWorkingNode("Model A").trigger("hover").find('button[title="cm_add_to_model"]').click({ force: true });
    cyu.getWorkingNode("Model A").find('button[title="cm_add_to_model"]').should("be.visible");
    cyu.getWorkingNode("Model A").find('button[title="cm_more_model_actions"]').should("be.visible");
    cy.get(".sapMMenu").contains("cm_create_concept", original).should("exist");
    cyu.getWorkingNode("Model A").trigger("hover").find('button[title="cm_more_model_actions"]').click({ force: true });
    cy.get(".sapMMenu").contains("cm_export_model", original).should("exist");
    cy.get(WORKING_ONTOLOGY_TAB.SEARCH).focus().click({ force: true });
    cy.get(".sapMMenu").should("not.exist");
    cyu.getWorkingNode("Model A").find('button[title="cm_add_to_model"]').should("not.be.visible");
    cyu.getWorkingNode("Model A").find('button[title="cm_more_model_actions"]').should("not.be.visible");

    // Model Subconcept buttons
    cyu.getWorkingNode("Model A").click();
    cyu.getWorkingNode("Address").trigger("hover").find('button[title="cm_add_to_concept"]').click({ force: true });
    cyu.getWorkingNode("Address").find('button[title="cm_add_to_concept"]').should("be.visible");
    cyu.getWorkingNode("Address").find('button[title="cm_more_concept_actions"]').should("be.visible");
    cy.get(".sapMMenu").contains("cm_property", original).should("exist");
    cy.get(".sapMMenu").contains("cm_relationship", original).should("exist");
    cy.get(".sapMMenu").contains("cm_sub_concept", original).should("exist");
    cyu
      .getWorkingNode("Address")
      .trigger("hover")
      .find('button[title="cm_more_concept_actions"]')
      .click({ force: true });
    cy.get(".sapMMenu").contains("cm_delete_resource", original).should("exist");
    cy.get(WORKING_ONTOLOGY_TAB.SEARCH).focus().click({ force: true });
    cy.get(".sapMMenu").should("not.exist");
    cyu.getWorkingNode("Address").find('button[title="cm_add_to_concept"]').should("not.be.visible");
    cyu.getWorkingNode("Address").find('button[title="cm_more_concept_actions"]').should("not.be.visible");
  }); */
});
