/** @format */

import * as cyu from "../../cypressUtil";
import { CONSTANTS, detailsLabelInputIs, getWorkingTree, original, setupBeforeEach } from "../../cypressUtil";
import { CM_REQUESTS, CM_URL, CONCEPT_DETAILS } from "../../descriptor";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    setupBeforeEach();

    cy.intercept("GET", CM_REQUESTS.SOURCES, {
      fixture: "graphbuilder/conceptualModel/sourceConcepts/getSourceConceptsForUriConflict.json",
    }).as("getSourceConcepts");

    cy.intercept("GET", CM_REQUESTS.ITEM_MODEL, {
      fixture: "graphbuilder/conceptualModel/initialWorkingModelLoad/getItem.json",
    }).as("getOntology");

    cy.visit(CM_URL.ITEM_MODEL, { skipResourceBundle: true }).then(() => {
      getWorkingTree().should("be.visible");
      detailsLabelInputIs(CONSTANTS.ITEM_MODEL);
    });
  });

  it("should abort addSubClass of concept conflicting with working Ontology", () => {
    cyu.getWorkingNode(CONSTANTS.ITEM_MODEL).click();
    cyu.createConcept();
    cyu.validateNewlyCreatedConcept(CONSTANTS.FIRST_CONCEPT_LABEL);
    cyu.typeOnLabel("{backspace}".repeat(CONSTANTS.FIRST_CONCEPT_LABEL.length));
    cyu.typeOnLabel("Conflict");
    searchForItemParentConceptAsConcept();
    interceptOrderItem();
    cyu.getParentConceptsListRow(2).find(".cyParentConceptsLabel").should("have.text", "itemOntology");
    cyu.getParentConceptsListRow(2).click({ force: true });
    cy.get(".sapMMessageBoxError").contains("cm_uri_conflict_error_start", original);
    cy.get(".sapMMessageBoxLinkText").click();
    cy.get(".sapMMessageBoxDetails").should(
      "have.text",
      "cm_concept_uri_conflict_details_error(itemOntology,http://sap.com/item,Item)cm_uri_conflict_error_end"
    );
  });

  it("should abort addSubClass of concept conflicting with working property", () => {
    cyu.getWorkingNode(CONSTANTS.ITEM_MODEL).click();
    cyu.createConcept();
    cyu.validateNewlyCreatedConcept(CONSTANTS.FIRST_CONCEPT_LABEL);
    cyu.typeOnLabel("{backspace}".repeat(CONSTANTS.FIRST_CONCEPT_LABEL.length));
    cyu.typeOnLabel("Conflict");
    searchForItemParentConceptAsConcept();
    interceptOrderItemProp();
    cyu.getParentConceptsListRow(3).find(".cyParentConceptsLabel").should("have.text", "itemProp");
    cyu.getParentConceptsListRow(3).click({ force: true });
    cy.get(".sapMMessageBoxError").contains("cm_uri_conflict_error_start", original);
    cy.get(".sapMMessageBoxLinkText").click();
    cy.get(".sapMMessageBoxDetails").should(
      "have.text",
      "cm_concept_uri_conflict_details_error(itemProp,http://sap.com/itemProp,IPropA)cm_uri_conflict_error_end"
    );
  });

  it("should abort addSubClass of concept having property conflicting with working Concept", () => {
    cyu.getWorkingNode(CONSTANTS.ITEM_MODEL).click();
    cyu.createConcept();
    cyu.typeOnLabel("{backspace}".repeat(CONSTANTS.FIRST_CONCEPT_LABEL.length));
    cyu.typeOnLabel("Conflict");
    searchForItemParentConceptAsConcept();
    interceptOrderDummy();
    cyu.getParentConceptsListRow(1).find(".cyParentConceptsLabel").should("have.text", "DummyItem");
    cyu.getParentConceptsListRow(1).click({ force: true });
    cy.get(".sapMMessageBoxError").contains("cm_uri_conflict_error_start", original);
    cy.get(".sapMMessageBoxLinkText").click();
    cy.get(".sapMMessageBoxDetails").should(
      "have.text",
      "cm_concept_entities_conflict_error(DummyItem) \n\n IClassA: http://sap.com/ItemClasscm_uri_conflict_error_end"
    );
  });

  it("should abort import when imported concept conflicts with working ontology", () => {
    cyu.getWorkingNode(CONSTANTS.ITEM_MODEL).click();
    cyu.createConcept();
    cyu.validateNewlyCreatedConcept(CONSTANTS.FIRST_CONCEPT_LABEL);
    cyu.typeOnLabel("{backspace}".repeat(CONSTANTS.FIRST_CONCEPT_LABEL.length));
    cyu.typeOnLabel("Conflict");
    loadDummyResultForOrder();
    cyu.switchToSourceConceptsTreeView();
    cyu.toggleSourceNode("Order");
    cyu.getSourceNode("Dummy").click();
    interceptImportOrderItem();
    cyu.addSourceNodeAsReference("Dummy");
    cy.get(".sapMMessageBoxError").contains("cm_uri_conflict_error_start", original);
    cy.get(".sapMMessageBoxLinkText").click();
    cy.get(".sapMMessageBoxDetails").should(
      "have.text",
      "cm_concept_entities_conflict_error(Dummy) \n\n Item: http://sap.com/itemcm_uri_conflict_error_end"
    );
  });

  it("should abort import when subclass entities of imported concept conflicts", () => {
    cyu.getWorkingNode(CONSTANTS.ITEM_MODEL).click();
    cyu.createConcept();
    cyu.validateNewlyCreatedConcept(CONSTANTS.FIRST_CONCEPT_LABEL);
    cyu.typeOnLabel("{backspace}".repeat(CONSTANTS.FIRST_CONCEPT_LABEL.length));
    cyu.typeOnLabel("Conflict");
    loadDummyResultForOrder();
    cyu.switchToSourceConceptsTreeView();
    cyu.toggleSourceNode("Order");
    cyu.getSourceNode("Dummy").click();
    interceptImportOrderAlpha();
    cyu.addSourceNodeAsReference("Dummy");
    cy.get(".sapMMessageBoxError").contains("cm_uri_conflict_error_start", original);
    cy.get(".sapMMessageBoxLinkText").click();
    cy.get(".sapMMessageBoxDetails").should(
      "have.text",
      "cm_concept_entities_conflict_error(Dummy) \n\n IClassA: http://sap.com/ItemClasscm_uri_conflict_error_end"
    );
  });

  function searchForItemParentConceptAsConcept() {
    cy.intercept("GET", CM_REQUESTS.SEARCH_FOR_ITEM, {
      fixture: "graphbuilder/conceptualModel/workingModel/parentConceptsSearch/searchForItem.json",
    }).as("getSearchForItem");
    cy.get(CONCEPT_DETAILS.PARENT_CONCEPTS_MULTI_INPUT).click();
    cy.get(CONCEPT_DETAILS.PARENT_CONCEPTS_DIALOG_SEARCH).click().type("item");
    checkParentConceptsBusyState();
  }

  function checkParentConceptsBusyState() {
    cy.get(CONCEPT_DETAILS.PARENT_CONCEPTS_TABLE).should("have.class", "sapUiLocalBusy");
    cy.get(CONCEPT_DETAILS.PARENT_CONCEPTS_TABLE).should("not.have.class", "sapUiLocalBusy");
  }

  function interceptOrderItem() {
    cy.intercept("GET", CM_REQUESTS.GET_ORDER_ITEM_CLASS, {
      fixture: "graphbuilder/conceptualModel/workingModel/parentConceptsSearch/getOrderItem.json",
    }).as("getOrderItem");
  }
  function interceptOrderItemProp() {
    cy.intercept("GET", CM_REQUESTS.GET_ORDER_ITEM_PROP_CLASS, {
      fixture: "graphbuilder/conceptualModel/workingModel/parentConceptsSearch/getOrderItemProp.json",
    }).as("getOrderItemProp");
  }

  function interceptOrderDummy() {
    cy.intercept("GET", CM_REQUESTS.GET_ORDER_DUMMY_CLASS, {
      fixture: "graphbuilder/conceptualModel/workingModel/parentConceptsSearch/getOrderDummy.json",
    }).as("getOrderDummy");
  }

  function loadDummyResultForOrder() {
    cy.intercept("GET", CM_REQUESTS.GET_ORDER, {
      fixture: "graphbuilder/conceptualModel/sourceConcepts/getDummy.json",
    }).as("getDummyResult");
  }

  function interceptImportOrderItem() {
    cy.intercept("GET", CM_REQUESTS.GET_DUMMY_IMPORT_DATA, {
      fixture: "graphbuilder/conceptualModel/workingModel/parentConceptsSearch/getOrderItem.json",
    }).as("importOrderItem");
  }

  function interceptImportOrderAlpha() {
    cy.intercept("GET", CM_REQUESTS.GET_DUMMY_IMPORT_DATA, {
      fixture: "graphbuilder/conceptualModel/sourceConcepts/getAlphaConcept.json",
    }).as("importAplhaItem");
  }
});
