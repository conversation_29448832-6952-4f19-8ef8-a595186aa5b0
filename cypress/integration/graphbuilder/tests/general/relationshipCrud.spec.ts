/** @format */

import * as cyu from "../../cypressUtil";
import { CONSTANTS } from "../../cypressUtil";
import { DETAILS_BASE, DIAGRAM, PROPERTY_DETAILS, TREE_PANEL } from "../../descriptor";

describe.skip(Cypress.spec.relative, () => {
  beforeEach(() => {
    cyu.visitModelA();
  });

  const setupRelationshipOnAddress = (label: string) => {
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.createRelationship(CONSTANTS.ADDRESS);
    cyu.validateNewlyCreatedRelationship(label);
    cyu.checkMultiInputListOccurrence(
      PROPERTY_DETAILS.TARGET_CONCEPTS_DIALOG_CANCEL_BUTTON,
      PROPERTY_DETAILS.TARGET_CONCEPTS_LIST,
      PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER,
      [CONSTANTS.CHILD],
      1
    );
    cyu.toggleTargetConcept(CONSTANTS.CHILD);
    cyu.selectedTokenIs([CONSTANTS.CHILD], PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT);
    cyu.checkMultiInputListOccurrence(
      PROPERTY_DETAILS.TARGET_CONCEPTS_DIALOG_CANCEL_BUTTON,
      PROPERTY_DETAILS.TARGET_CONCEPTS_LIST,
      PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER,
      [CONSTANTS.CHILD],
      0
    );
    cyu.getDiagram().find(`.RelationshipLinkSymbol[sap-automation*="${label}"]`).should("be.visible");
  };

  it("should create and modify a relationship", () => {
    // create and use select list
    setupRelationshipOnAddress(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.createRelationship(CONSTANTS.ADDRESS);
    cyu.validateNewlyCreatedRelationship(CONSTANTS.SECOND_RELATIONSHIP_LABEL);
    cyu.checkMultiInputListOccurrence(
      PROPERTY_DETAILS.TARGET_CONCEPTS_DIALOG_CANCEL_BUTTON,
      PROPERTY_DETAILS.TARGET_CONCEPTS_LIST,
      PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER,
      [CONSTANTS.DOG],
      1
    );
    cyu.toggleTargetConcept(CONSTANTS.DOG);
    cyu.selectedTokenIs([CONSTANTS.DOG], PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT);
    cyu.checkMultiInputListOccurrence(
      PROPERTY_DETAILS.TARGET_CONCEPTS_DIALOG_CANCEL_BUTTON,
      PROPERTY_DETAILS.TARGET_CONCEPTS_LIST,
      PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER,
      [CONSTANTS.DOG],
      0
    );
    cyu.getDiagram().contains(CONSTANTS.SECOND_RELATIONSHIP_LABEL, cyu.original);
  });

  it("should show dismissed missing target concept dialog until user chooses to select or proceed with no target concept", () => {
    setupRelationshipOnAddress(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.createRelationship(CONSTANTS.ADDRESS);
    cyu.validateNewlyCreatedRelationship(CONSTANTS.SECOND_RELATIONSHIP_LABEL);
    cyu.checkMultiInputListOccurrence(
      PROPERTY_DETAILS.TARGET_CONCEPTS_DIALOG_CANCEL_BUTTON,
      PROPERTY_DETAILS.TARGET_CONCEPTS_LIST,
      PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER,
      [CONSTANTS.DOG],
      1
    );
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.closeDialog(TREE_PANEL.MISSING_TARGET_CONCEPT_DIALOG);
    cy.wait(1000);
    cy.get(".targetConceptWarningDialog").should("not.exist");
    cyu.getWorkingNode(CONSTANTS.SECOND_RELATIONSHIP_LABEL).click();
    cy.get(".targetConceptWarningDialog").should("not.exist");
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cy.get(".targetConceptWarningDialog").should("be.visible");
  });

  it("should delete a relationship using the tree", () => {
    setupRelationshipOnAddress(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
    cyu.getWorkingNode(CONSTANTS.FIRST_RELATIONSHIP_LABEL).click();
    cyu.deleteResource(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
    cy.get(".sapMMessageToast").should("have.text", `cm_resource_deleted(${CONSTANTS.FIRST_RELATIONSHIP_LABEL})`);
    cyu.validateAfterResourceRemoval(CONSTANTS.FIRST_RELATIONSHIP_LABEL, CONSTANTS.ADDRESS);
  });

  it("should delete a relationship by clicking on the token icon", () => {
    setupRelationshipOnAddress(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
    cyu.removeToken(PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT, CONSTANTS.CHILD);
    cyu.getDiagram().should("not.contain.text", CONSTANTS.FIRST_RELATIONSHIP_LABEL);
  });

  it("should delete a relationship from details panel", () => {
    setupRelationshipOnAddress(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
    cy.get(DETAILS_BASE.OVERFLOW_BUTTON).click();
    cy.get(".propertyMenu.sapMMenu").contains("cm_delete_resource", cyu.original).closest("li").click();
    cy.get(".sapMMessageToast").should("have.text", `cm_resource_deleted(${CONSTANTS.FIRST_RELATIONSHIP_LABEL})`);
  });

  it("should update tree node and galilei node after changing the label of a relationship", () => {
    setupRelationshipOnAddress(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
    cyu.getWorkingNode(CONSTANTS.FIRST_RELATIONSHIP_LABEL).click();
    cyu.detailsLabelInputIs(CONSTANTS.FIRST_RELATIONSHIP_LABEL);

    // add text
    const newLabel = "MyLabel";
    cyu.pasteOnLabel(newLabel);

    cyu.getWorkingNode(newLabel);
    cy.get(DIAGRAM.AUTOLAYOUT).click();
    cyu.getDiagramRelationshipLink(newLabel).last().should("contain.text", newLabel);

    // remove text
    cyu.typeOnLabel("{backspace}{backspace}{backspace}{backspace}{backspace}");
    cyu.getDiagramRelationshipLink("My").should("not.contain.text", "Label");
  });

  it("A new relationship should be able to add more than one target concept with a warning", () => {
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.createRelationship(CONSTANTS.ADDRESS);
    cyu.checkMultiInputListOccurrence(
      PROPERTY_DETAILS.TARGET_CONCEPTS_DIALOG_CANCEL_BUTTON,
      PROPERTY_DETAILS.TARGET_CONCEPTS_LIST,
      PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER,
      [CONSTANTS.FIZZ, CONSTANTS.ANIMAL, CONSTANTS.EMPLOYEE],
      1
    );
    cyu.toggleTargetConcept(CONSTANTS.ANIMAL);
    cyu.toggleTargetConcept(CONSTANTS.FIZZ);
    cyu.toggleTargetConcept(CONSTANTS.EMPLOYEE);
    cyu.selectedTargetConceptsQuantityIs(3);

    cyu.checkMultiInputListOccurrence(
      PROPERTY_DETAILS.TARGET_CONCEPTS_DIALOG_CANCEL_BUTTON,
      PROPERTY_DETAILS.TARGET_CONCEPTS_LIST,
      PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER,
      [CONSTANTS.FIZZ, CONSTANTS.ANIMAL, CONSTANTS.EMPLOYEE],
      0
    );
    cyu.targetConceptsValueStateWarningIs("multipleTargetConcepts");

    cy.log("should be able to delete all target concepts with multi selection");
    cyu.selectedTargetConceptsQuantityIs(3);
    cy.get(PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT)
      .find("input")
      .focus() // focus first to avoid value help dialog popping up
      .type("{ctrl+a}{backspace}", { force: true } /* force is needed because of the value help added html props */);
    cyu.selectedTargetConceptsQuantityIs(0);
    cyu.targetConceptsValueStateWarningIs("noTargetConcept");
  });

  it("should check the absence of a no-concept warning when first visiting the relationship", () => {
    // create a new relationship
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.createRelationship(CONSTANTS.ADDRESS);
    cyu.getWorkingNode(CONSTANTS.FIRST_RELATIONSHIP_LABEL).click();
    cyu.detailsLabelInputIs(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
    cyu.targetConceptsValueStateWarningIs("none");

    // navigate to another tree item
    cyu.getWorkingNode(CONSTANTS.FIZZ).click();
    cyu.chooseTargetConceptWarningDialogAction("cm_proceed_without_target");
    cyu.detailsLabelInputIs(CONSTANTS.FIZZ);

    // navigate back to Address
    cyu.getWorkingNode(CONSTANTS.FIRST_RELATIONSHIP_LABEL).click();
    cyu.detailsLabelInputIs(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
    cyu.targetConceptsValueStateWarningIs("noTargetConcept");
  });
});
