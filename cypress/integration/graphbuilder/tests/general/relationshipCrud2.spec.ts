/** @format */

import * as cyu from "../../cypressUtil";
import { CONSTANTS } from "../../cypressUtil";
import { PROPERTY_DETAILS } from "../../descriptor";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    cyu.visitModelA();
  });

  const setupRelationshipOnAddress = (label: string) => {
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.createRelationship(CONSTANTS.ADDRESS);
    cyu.validateNewlyCreatedRelationship(label);
    cyu.checkMultiInputListOccurrence(
      PROPERTY_DETAILS.TARGET_CONCEPTS_DIALOG_CANCEL_BUTTON,
      PROPERTY_DETAILS.TARGET_CONCEPTS_LIST,
      PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER,
      [CONSTANTS.CHILD],
      1
    );
    cyu.toggleTargetConcept(CONSTANTS.CHILD);
    cyu.selectedTokenIs([CONSTANTS.CHILD], PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT);
    cyu.checkMultiInputListOccurrence(
      PROPERTY_DETAILS.TARGET_CONCEPTS_DIALOG_CANCEL_BUTTON,
      PROPERTY_DETAILS.TARGET_CONCEPTS_LIST,
      PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER,
      [CONSTANTS.CHILD],
      0
    );
    cyu.getDiagram().find(`.RelationshipLinkSymbol[sap-automation*="${label}"]`).should("be.visible");
  };

  /* THIS TEST IS BROKEN UNRELATED TO THIS PR. CREATED DW24-633 TO ADDRESS IT.
  it("should add more than one target concept and show a warning value state", () => {
    // make sure there is no new relationship yet
    cyu.getWorkingTree().contains("cm_relationship (1)", cyu.original).should("not.exist");

    // create the relationship
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.createRelationship(CONSTANTS.ADDRESS);
    cyu.targetConceptsValueStateWarningIs("none");

    cyu.checkMultiInputListOccurrence(
      PROPERTY_DETAILS.TARGET_CONCEPTS_DIALOG_CANCEL_BUTTON,
      PROPERTY_DETAILS.TARGET_CONCEPTS_LIST,
      PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER,
      [CONSTANTS.FIZZ, CONSTANTS.ANIMAL, CONSTANTS.EMPLOYEE],
      1
    );
    cyu.toggleTargetConcept(CONSTANTS.ANIMAL);
    cyu.toggleTargetConcept(CONSTANTS.FIZZ);
    cyu.toggleTargetConcept(CONSTANTS.EMPLOYEE);

    cyu.typeOnLabel("{selectall}{backspace}mySuperCoolLabel{enter}");

    // should have 3 connections on diagram
    cyu.diagramConnectionsQuantityIs("mySuperCoolLabel", 3);
    // should have 3 tokens on combo box
    cyu.selectedTargetConceptsQuantityIs(3);
    cyu.selectedTokenIs(
      [CONSTANTS.CHILD, CONSTANTS.ANIMAL, CONSTANTS.EMPLOYEE],
      PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT
    );
    // after selecting the target concepts they should not be in the available concepts list
    cyu.checkMultiInputListOccurrence(
      PROPERTY_DETAILS.TARGET_CONCEPTS_DIALOG_CANCEL_BUTTON,
      PROPERTY_DETAILS.TARGET_CONCEPTS_LIST,
      PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER,
      [CONSTANTS.FIZZ, CONSTANTS.ANIMAL, CONSTANTS.EMPLOYEE],
      0
    );
    cyu.targetConceptsValueStateWarningIs("multipleTargetConcepts");

    // switch to another relationship
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.createRelationship(CONSTANTS.ADDRESS);
    cyu.selectedTargetConceptsQuantityIs(0);

    cyu.targetConceptsValueStateWarningIs("noTargetConcept");

    // get back to changed relationship
    cyu.getWorkingNode("mySuperCoolLabel").click();

    // should still have 3 connections on diagram
    cyu.diagramConnectionsQuantityIs("mySuperCoolLabel", 3);

    // should still have 3 tokens on combo box
    cyu.selectedTargetConceptsQuantityIs(3);

    cyu.targetConceptsValueStateWarningIs("multipleTargetConcepts");

    // now validate it can be removed
    cyu.removeToken(PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT, CONSTANTS.ANIMAL);

    cyu.diagramConnectionsQuantityIs("mySuperCoolLabel", 2);
    cyu.selectedTargetConceptsQuantityIs(2);

    cyu.targetConceptsValueStateWarningIs("multipleTargetConcepts");

    cyu.removeToken(PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT, CONSTANTS.EMPLOYEE);
    cyu.targetConceptsValueStateWarningIs("none");

    cyu.removeToken(PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT, CONSTANTS.FIZZ);
    cyu.diagramConnectionsQuantityIs("mySuperCoolLabel", 0);
    cyu.selectedTargetConceptsQuantityIs(0);

    cyu.targetConceptsValueStateWarningIs("none");

    // switch to another relationship
    cyu.getWorkingNode("cm_relationship (1)").click();
    cyu.toggleTargetConcept(CONSTANTS.EMPLOYEE);
    cyu.selectedTargetConceptsQuantityIs(1);

    // get back to previous relationship and make sure is still empty
    cyu.getWorkingNode("mySuperCoolLabel").click();
    cyu.diagramConnectionsQuantityIs("mySuperCoolLabel", 0);
    cyu.selectedTargetConceptsQuantityIs(0);
    cyu.selectedTokenIs([], PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT);
  });
  */

  it("should have a tooltip in the selectable target concepts", () => {
    setupRelationshipOnAddress(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
    cy.get(PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER).click({ force: true });
    cy.get(PROPERTY_DETAILS.TARGET_CONCEPTS_LIST)
      .contains(CONSTANTS.ADDRESS, cyu.original)
      .closest("li")
      .invoke("attr", "title")
      .should("be.equal", "cm_uri_tooltip(http://cambridgesemantics.com/ontologies/Model_A#Address)");

    cy.get(PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER).click({ force: true });
    cy.get(PROPERTY_DETAILS.TARGET_CONCEPTS_LIST)
      .contains(CONSTANTS.ANIMAL, cyu.original)
      .closest("li")
      .invoke("attr", "title")
      .should("be.equal", "cm_uri_tooltip(http://cambridgesemantics.com/ontologies/Base_Model#Animal)");
  });

  it("should show missing target concept dialog after creating and changing the label of a relationship ", () => {
    setupRelationshipOnAddress(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.createRelationship(CONSTANTS.ADDRESS);
    cyu.validateNewlyCreatedRelationship(CONSTANTS.SECOND_RELATIONSHIP_LABEL);
    cyu.checkMultiInputListOccurrence(
      PROPERTY_DETAILS.TARGET_CONCEPTS_DIALOG_CANCEL_BUTTON,
      PROPERTY_DETAILS.TARGET_CONCEPTS_LIST,
      PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER,
      [CONSTANTS.DOG],
      1
    );
    cyu.detailsLabelInputIs(CONSTANTS.SECOND_RELATIONSHIP_LABEL);
    const newLabel = "newLabel";
    cyu.pasteOnLabel(newLabel);
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cy.get(".targetConceptWarningDialog").should("be.visible");
  });

  it("should have a resource icon in the selectable target concepts", () => {
    setupRelationshipOnAddress(CONSTANTS.FIRST_RELATIONSHIP_LABEL);

    cyu.getUi5(PROPERTY_DETAILS.TARGET_CONCEPTS_LIST).should((list: sap.m.ListBase) => {
      // address should have concept icon
      expect((list.getItems()[0] as sap.m.StandardListItem).getIcon()).to.be.equal("sap-icon://sac/concept");
      expect((list.getItems()[0] as sap.m.StandardListItem).getTitle()).to.be.equal(CONSTANTS.ADDRESS);

      // foo should have sub-concept icon
      expect((list.getItems()[3] as sap.m.StandardListItem).getIcon()).to.be.equal("sap-icon://sac/sub-concept");
      expect((list.getItems()[3] as sap.m.StandardListItem).getTitle()).to.be.equal(CONSTANTS.EMPLOYEE);
    });
  });

  it("should open the relationship node on tree on selecting in the diagram ", () => {
    cy.get(".sapUiLocalBusyIndicator").should("not.exist");
    cyu.getWorkingNode(CONSTANTS.YING).click();
    cyu.createSubConcept(CONSTANTS.YING);
    cyu.typeOnLabel("{backspace}".repeat(CONSTANTS.FIRST_SUB_CONCEPT_LABEL.length));
    cyu.typeOnLabel("Car");
    cyu.clickOnSubConceptAddButton("Car");
    cyu.clickOnMenuItem(CONSTANTS.HEADERS.SUB_CONCEPT);
    cyu.validateNewlyCreatedSubConcept(CONSTANTS.FIRST_SUB_CONCEPT_LABEL);
    cyu.typeOnLabel("{backspace}".repeat(CONSTANTS.FIRST_SUB_CONCEPT_LABEL.length));
    cyu.typeOnLabel("SuperCar");

    cyu.clickOnSubConceptAddButton("SuperCar");
    cyu.clickOnMenuItem(CONSTANTS.HEADERS.RELATIONSHIP);
    cyu.toggleTargetConcept(CONSTANTS.ANIMAL);
    cyu.selectedTokenIs([CONSTANTS.ADDRESS], PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT);
    cyu.typeOnLabel("{backspace}".repeat(CONSTANTS.FIRST_RELATIONSHIP_LABEL.length));
    cyu.typeOnLabel("belongs");
    cyu.getWorkingNode("belongs").click();

    cyu.getWorkingNode(CONSTANTS.MODEL_A).click();
    cyu.getDiagramNode(CONSTANTS.ADDRESS).click();
    cyu.getDiagramRelationshipLink("belongs").click();
    cyu.detailsLabelInputIs("belongs");
  });
});
