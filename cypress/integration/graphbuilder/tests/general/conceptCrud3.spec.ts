/** @format */

import * as cyu from "../../cypressUtil";
import { CONSTANTS } from "../../cypressUtil";
import { DETAILS_BASE } from "../../descriptor";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    cyu.visitModelA();
  });

  it.skip("should remove a external/imported/uneditable concept and route to its direct parent", () => {
    cyu.validateBeforeResourceRemoval(CONSTANTS.WIDGET);
    cyu.removeResource(CONSTANTS.WIDGET);
    cyu.validateAfterResourceRemoval(CONSTANTS.WIDGET, CONSTANTS.MODEL_A);
  });

  it("should update the label on the tree and on the diagram when the label is valid", () => {
    const ADMISSION = "Admission";
    // pre-action validation
    cyu.getWorkingTree().should("not.contain", ADMISSION);
    cyu.getDiagram().should("not.contain", ADMISSION);

    // action
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.detailsLabelInputIs(CONSTANTS.ADDRESS);
    cyu.typeOnLabel("{backspace}".repeat(5));
    cyu.typeOnLabel("mission");

    // post action validation
    cyu.getWorkingTree().contains(ADMISSION, cyu.original).should("exist");
    cyu.getDiagramNode(ADMISSION).should("exist");
    cyu.detailsHeaderTitleIs(ADMISSION);
    cyu.labelWarningStateCheck(false);

    // label still the same after leaving the node
    cyu.getWorkingNode(CONSTANTS.FIZZ).click();
    cyu.getWorkingTree().contains(ADMISSION, cyu.original).should("exist");
    cyu.getDiagramNode(ADMISSION).should("exist");

    // label still the same after getting back to the node
    cyu.getWorkingNode(ADMISSION).click();
    cyu.detailsLabelInputIs(ADMISSION);
    cyu.getWorkingTree().contains(ADMISSION, cyu.original).should("exist");
    cyu.getDiagramNode(ADMISSION).should("exist");
    cyu.detailsHeaderTitleIs(ADMISSION);
    cyu.labelWarningStateCheck(false);
  });

  it("should not add unique identifier when changing the label of a new concept into the same name as the uri", () => {
    cyu.getWorkingNode(CONSTANTS.MODEL_A).click();
    cyu.createConcept();
    cy.get(DETAILS_BASE.ENTITY_NAME).contains(CONSTANTS.FIRST_CONCEPT_LABEL, cyu.original).should("be.visible");
    cy.get(DETAILS_BASE.LABEL_INNER + "[value='cm_concept 1']", cyu.original).should("exist");
    cy.get(DETAILS_BASE.LABEL_INNER + "[value='cm_concept 1']", cyu.original)
      .click()
      .type(`cm_concept_1{enter}`);
    cyu.waitDebounce();
    cy.get(DETAILS_BASE.URI_LOCAL_NAME).closest("span").should("have.text", "cm_concept_1");
  });

  it("should have a tooltip for the label", () => {
    cy.get(DETAILS_BASE.LABEL).invoke("attr", "title").should("contain", "Model A");
  });

  it("should display a resource as editable when reloading the page", () => {
    cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
    cyu.browserUrlContainsText(CONSTANTS.ADDRESS);
    cy.get(DETAILS_BASE.LABEL_INNER).should("be.enabled");
    cy.reload();

    cyu.ensureThereAreNoBusyStates();
    cyu.typeOnLabel("this is editable :) ");
    cy.get(DETAILS_BASE.LABEL_INNER).should("be.enabled");
  });

  it("should display a resource as non editable when reloading the page", () => {
    cyu.getWorkingNode(CONSTANTS.ANIMAL).click();
    cyu.browserUrlContainsText(CONSTANTS.ANIMAL);
    cyu.assertElementIsReadOnly(DETAILS_BASE.LABEL_INNER);
    cy.reload();

    cy.get(".sapUiLocalBusy").should("not.exist");
    cyu.assertElementIsReadOnly(DETAILS_BASE.LABEL_INNER);
  });
});
