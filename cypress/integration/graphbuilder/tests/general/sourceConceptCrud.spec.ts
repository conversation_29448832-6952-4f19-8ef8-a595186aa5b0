/** @format */
import * as cyu from "../../cypressUtil";
import { CONSTANTS, original } from "../../cypressUtil";
import { CM_REQUESTS } from "../../descriptor";

describe(Cypress.spec.relative, () => {
  context("Model A Tests", () => {
    beforeEach(() => {
      cyu.visitModelA();
    });

    function loadSalesModel() {
      cy.intercept("GET", CM_REQUESTS.SALES_MODEL_FRAME, {
        fixture: "graphbuilder/conceptualModel/sourceConcepts/getSalesModel",
      }).as("getSalesModelFrame");
    }

    it("select model(parent) node after deleting any of models concept(child)", () => {
      cyu.getWorkingNode(CONSTANTS.EMPLOYEE).click();
      cyu.deleteResource(CONSTANTS.EMPLOYEE);
      cyu.confirmDeletionOfConceptAndProperties();
      cyu.workingTreeNodeIsSelected(CONSTANTS.MODEL_A);
    });

    it("should add a source concept as a reference to the working model", () => {
      cyu.getWorkingNode(CONSTANTS.EMPLOYEE).click();
      cyu.deleteResource(CONSTANTS.EMPLOYEE); // delete current model employee so we can properly verify
      cyu.confirmDeletionOfConceptAndProperties();
      // Employee that is going to be imported from a source concept

      // ensure employee does not exist on working model before
      cyu.validateAfterResourceRemoval(CONSTANTS.EMPLOYEE, CONSTANTS.MODEL_A);

      cyu.getWorkingTree().should("not.contain.text", "Manager");
      // action
      cyu.switchToSourceConceptsTreeView();
      loadSalesModel();
      cyu.toggleSourceNode(CONSTANTS.SALES_MODEL);
      cyu.toggleSourceNode(CONSTANTS.PERSON);
      cyu.getSourceNode(CONSTANTS.EMPLOYEE).click();

      cy.intercept("GET", CM_REQUESTS.SALES_EMPLOYEE_FRAME, {
        fixture: "graphbuilder/conceptualModel/sourceConcepts/getEmployeeFrame.json",
      });

      cyu.addSourceNodeAsReference(CONSTANTS.EMPLOYEE);

      // validate post action
      cy.log("Should expand added node and show properties automatically");
      cyu.getWorkingNode("Manager").should("be.visible");

      cyu.getWorkingNode(CONSTANTS.EMPLOYEE).click();
      cyu.validateNewlyCreatedSubConcept(CONSTANTS.EMPLOYEE);
      cyu.getWorkingNode(CONSTANTS.EMPLOYEE).should("have.attr", "aria-expanded", "true");
      cyu.getWorkingNode(CONSTANTS.HAS_NAME).click();
      cyu.detailsLabelInputIs(CONSTANTS.HAS_NAME);

      cyu.getWorkingNode(CONSTANTS.MODEL_A).click();
      cyu.checkImportedOntologiesListLabel(CONSTANTS.SALES_MODEL, 1);

      // check if warning message appears when adding an existing reference
      cyu.switchToSourceConceptsTreeView();
      cyu.addSourceNodeAsReference(CONSTANTS.EMPLOYEE);
      cyu.validateWarningMessage("cm_concept_already_added_warning_title", "cm_concept_already_added_warning_message");
      cy.contains("MSGBOX_OK", original).click();

      // before add opportunities
      cyu.getDiagram().should("not.contain.text", CONSTANTS.OWNED_BY);

      cyu.getSourceNode("Opportunities").click();

      cy.intercept("GET", CM_REQUESTS.SALES_OPPORTUNITIES_FRAME, {
        fixture: "graphbuilder/conceptualModel/sourceConcepts/getOpportunitiesFrame.json",
      });

      cyu.addSourceNodeAsReference(CONSTANTS.OPPORTUNITIES);
      cyu.validateNewlyCreatedConcept(CONSTANTS.OPPORTUNITIES);

      // should have a link
      cyu.getDiagram().contains(CONSTANTS.OWNED_BY, cyu.original);

      // bugfix check in which imported ontologies were appearing on the working tree
      cyu.getWorkingTree().should("not.contain.text", "cm_untitled_resource");
    });

    it("should clear imported ontologies once imported class removed", () => {
      cyu.getWorkingNode(CONSTANTS.EMPLOYEE).click();
      cyu.deleteResource(CONSTANTS.EMPLOYEE);
      cyu.confirmDeletionOfConceptAndProperties();

      // import from another ontology
      cyu.switchToSourceConceptsTreeView();
      loadSalesModel();
      cyu.toggleSourceNode(CONSTANTS.SALES_MODEL);
      cyu.toggleSourceNode(CONSTANTS.PERSON);
      cyu.getSourceNode(CONSTANTS.EMPLOYEE).click();

      cy.intercept("GET", CM_REQUESTS.SALES_EMPLOYEE_FRAME, {
        fixture: "graphbuilder/conceptualModel/sourceConcepts/getEmployeeFrame.json",
      });

      cyu.addSourceNodeAsReference(CONSTANTS.EMPLOYEE);

      // validate that Sales Model is imported
      cyu.getWorkingNode(CONSTANTS.EMPLOYEE).click();
      cyu.getWorkingNode(CONSTANTS.MODEL_A).click();
      cyu.checkImportedOntologiesListLabel(CONSTANTS.SALES_MODEL, 1);

      // remove employee
      cy.log("Remove employee from working model");
      cyu.removeResource(CONSTANTS.EMPLOYEE, ".referencedConceptMenu.sapMMenu");

      // verify that Sales Model is no longer imported
      cyu.checkImportedOntologiesListLength(1);
    });
  });

  context("Base Model Tests", () => {
    beforeEach(() => {
      cyu.visitBaseModel();
    });

    function interceptModelA() {
      cy.intercept("GET", CM_REQUESTS.MODEL_A_FRAME, {
        fixture: "graphbuilder/conceptualModel/workingModel/parentConceptsSearch/getModelAFrames",
      }).as("getModelAFrame");
    }

    it("should handle a circular reference between ontologies", () => {
      cyu.switchToSourceConceptsTreeView();
      interceptModelA();

      cyu.toggleSourceNode(CONSTANTS.MODEL_A);
      cyu.getSourceNode(CONSTANTS.YANG).click();

      cy.intercept("GET", CM_REQUESTS.MODEL_A_YANG_FRAME, {
        fixture: "graphbuilder/conceptualModel/sourceConcepts/getModelAYang.json",
      });

      cyu.addSourceNodeAsReference(CONSTANTS.YANG);
      cyu.getWorkingNode(CONSTANTS.ANIMAL).should("be.visible");
    });
  });

  context("Grandparent Model Tests", () => {
    beforeEach(() => {
      cyu.visitBookSalesForParentChildTest();
    });

    function interceptGrandparent() {
      cy.intercept("GET", CM_REQUESTS.PARENTCHILD_FRAME, {
        fixture: "graphbuilder/conceptualModel/workingModel/parentConceptsSearch/getGrandparentFrames.json",
      }).as("getModelAFrame");
    }

    it("should handle adding parent and child classes", () => {
      interceptGrandparent();

      cy.intercept("GET", CM_REQUESTS.PARENTCHILD_PARENT_FRAME, {
        fixture: "graphbuilder/conceptualModel/sourceConcepts/getParentChildParent.json",
      });

      cy.intercept("GET", CM_REQUESTS.PARENTCHILD_CHILD_FRAME, {
        fixture: "graphbuilder/conceptualModel/sourceConcepts/getParentChildChild.json",
      });

      cy.intercept("GET", CM_REQUESTS.PARENTCHILD_GRANDPARENT_FRAME, {
        fixture: "graphbuilder/conceptualModel/sourceConcepts/getParentChildGrandparent.json",
      });

      cyu.switchToSourceConceptsTreeView();
      cyu.toggleSourceNode(CONSTANTS.PARENTCHILDTEST);
      cyu.toggleSourceNode(CONSTANTS.GRANDPARENT);
      cyu.getSourceNode(CONSTANTS.PARENT).click();
      cyu.addSourceNodeAsReference(CONSTANTS.PARENT);
      cyu.getWorkingNode(CONSTANTS.PARENT).should("be.visible");

      cyu.switchToSourceConceptsTreeView();
      cyu.addSourceNodeAsReference(CONSTANTS.GRANDPARENT);
      cyu.getWorkingNode(CONSTANTS.GRANDPARENT).should("be.visible");

      cyu.switchToSourceConceptsTreeView();
      cyu.toggleSourceNode(CONSTANTS.PARENT);
      cyu.addSourceNodeAsReference(CONSTANTS.CHILD);
      cyu.getWorkingNode(CONSTANTS.CHILD).should("be.visible");
    });
  });

  context("Node expansion and scroll", () => {
    beforeEach(() => {
      cyu.visitIslandResortMarketing();
    });

    function loadSalesModel() {
      cy.intercept("GET", CM_REQUESTS.SALES_MODEL_FRAME, {
        fixture: "graphbuilder/conceptualModel/sourceConcepts/getSalesModel",
      }).as("getSalesModelFrame");
    }

    it("should scroll to imported concept from source tree", () => {
      cyu.toggleWorkingNode(CONSTANTS.CITY);
      cyu.toggleWorkingNode(CONSTANTS.CUSTOMER);
      cyu.toggleWorkingNode(CONSTANTS.INVOICE_LINE);
      cyu.toggleWorkingNode(CONSTANTS.RESERVATION);
      cyu.toggleWorkingNode(CONSTANTS.RESERVATION_LINE);

      cyu.switchToSourceConceptsTreeView();
      loadSalesModel();
      cyu.toggleSourceNode(CONSTANTS.SALES_MODEL);
      cyu.toggleSourceNode(CONSTANTS.PERSON);
      cyu.getSourceNode(CONSTANTS.EMPLOYEE).click();

      cy.intercept("GET", CM_REQUESTS.SALES_EMPLOYEE_FRAME, {
        fixture: "graphbuilder/conceptualModel/sourceConcepts/getEmployeeFrame.json",
      });
      cyu.addSourceNodeAsReference(CONSTANTS.EMPLOYEE);
      cyu.getWorkingNode(CONSTANTS.EMPLOYEE).should("be.visible");
    });
  });
});
