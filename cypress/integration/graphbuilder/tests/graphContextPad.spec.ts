/** @format */
import * as cyu from "../cypressUtil";
import { CONSTANTS, original } from "../cypressUtil";
import { DIAGRAM_CONTEXT_PAD } from "../descriptor";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    cyu.visitModelA();
  });

  // this ensure the actions work when the node is selected, but also when the node is just being hovered
  for (const action of ["click", "hover"]) {
    it("non-reference node context button pad should have the expected icons and tooltips -- " + action, () => {
      cy.get(".sapGalileiContextButtonPad", original).should("not.exist");
      cyu.performActionOnDiagramNode(action, CONSTANTS.ADDRESS);
      cy.get(".sapGalileiContextButtonPad", original).should("be.visible");

      cy.get(DIAGRAM_CONTEXT_PAD.CREATE_SUB_CONCEPT).contains("cm_diagram_add_sub_concept", original);
      cy.get(`${DIAGRAM_CONTEXT_PAD.CREATE_SUB_CONCEPT} .sapGalileiContextButtonIcon`, original).should(
        "have.attr",
        "icon",
        "sap-icon://sac/sub-concept"
      );

      cy.get(DIAGRAM_CONTEXT_PAD.DELETE).contains("cm_diagram_delete_concept", original);
      cy.get(`${DIAGRAM_CONTEXT_PAD.DELETE} .sapGalileiContextButtonIcon`, original).should(
        "have.attr",
        "icon",
        "sap-icon://delete"
      );

      cy.get(DIAGRAM_CONTEXT_PAD.ENTITY_LINK_SYMBOL).contains("cm_diagram_add_relationship", original);
      cy.get(`${DIAGRAM_CONTEXT_PAD.ENTITY_LINK_SYMBOL} .sapGalileiContextButtonIcon`, original).should(
        "have.attr",
        "icon",
        "sap-icon://sac/relationship"
      );
    });

    it("referenced node context button pad should have the expected icons and tooltips -- " + action, () => {
      cy.get(".sapGalileiContextButtonPad", original).should("not.exist");
      cyu.performActionOnDiagramNode(action, CONSTANTS.ANIMAL);
      cy.get(".sapGalileiContextButtonPad", original).should("be.visible");

      cy.get(DIAGRAM_CONTEXT_PAD.REFERENCE_CREATE_SUB_CONCEPT).contains("cm_diagram_add_sub_concept", original);
      cy.get(`${DIAGRAM_CONTEXT_PAD.REFERENCE_CREATE_SUB_CONCEPT} .sapGalileiContextButtonIcon`, original).should(
        "have.attr",
        "icon",
        "sap-icon://sac/sub-concept"
      );

      cy.get(DIAGRAM_CONTEXT_PAD.REMOVE).contains("cm_diagram_remove_referenced_concept", original);
      cy.get(`${DIAGRAM_CONTEXT_PAD.REMOVE} .sapGalileiContextButtonIcon`, original).should(
        "have.attr",
        "icon",
        "sap-icon://sys-minus"
      );

      cy.get(DIAGRAM_CONTEXT_PAD.OPEN_IN_NEW_TAB).contains("cm_diagram_open_in_new_tab", original);
      cy.get(`${DIAGRAM_CONTEXT_PAD.OPEN_IN_NEW_TAB} .sapGalileiContextButtonIcon`, original).should(
        "have.attr",
        "icon",
        "sap-icon://inspect"
      );
    });

    it(
      "should create a relationship by using the diagram context pad, and should duplicate the link to subConcepts -- " +
        action,
      () => {
        cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
        cyu.createSubConcept(CONSTANTS.ADDRESS);
        cyu.validateNewlyCreatedSubConcept(CONSTANTS.FIRST_SUB_CONCEPT_LABEL);

        cyu.createAndValidateRelationshipFromDiagram("Fizz", CONSTANTS.ADDRESS);

        cyu.validateNewlyCreatedRelationship(CONSTANTS.FIRST_RELATIONSHIP_LABEL, ["Fizz"], true);

        cy.log("Case 2: The relationship selected should be the one that was just created");

        cyu.diagramSelectedRelationshipSourceAndTargetAre(CONSTANTS.ADDRESS, "Fizz");

        cy.log("Case 3: A relationship should be created to the subConcepts as well");

        cyu.getDiagramRelationshipLink(CONSTANTS.FIRST_RELATIONSHIP_LABEL, 0).should("exist");

        cyu.diagramRelationshipExists({
          relationshipLabel: CONSTANTS.FIRST_RELATIONSHIP_LABEL,
          sourceNodeLabel: CONSTANTS.ADDRESS,
          targetNodeLabel: CONSTANTS.FIZZ,
        });

        cy.log("Case 4: Should be able to rename the newly created relationship");

        cyu.detailsLabelInputIs(CONSTANTS.FIRST_RELATIONSHIP_LABEL);
        cyu.pasteOnLabel("newRelationshipLabel");

        cyu.diagramRelationshipExists({
          relationshipLabel: "newRelationshipLabel",
          sourceNodeLabel: CONSTANTS.ADDRESS,
          targetNodeLabel: CONSTANTS.FIZZ,
        });

        cy.log("Case 5: Should be able to rename a concept that has a modified relationship uri connected to it");

        cyu.getWorkingNode(CONSTANTS.ADDRESS).click();
        cyu.detailsLabelInputIs(CONSTANTS.ADDRESS);
        cyu.pasteOnLabel("addressNewLabel");
        cyu.getDiagramNode("addressNewLabel").should("exist");

        cyu.diagramRelationshipExists({
          relationshipLabel: "newRelationshipLabel",
          sourceNodeLabel: "addressNewLabel",
          targetNodeLabel: CONSTANTS.FIZZ,
        });

        cy.log(
          "Case 6: Should be able to rename a relationship that is connected to a concept that has a recent modified uri"
        );

        cyu.getWorkingNode("newRelationshipLabel").click();
        cyu.detailsLabelInputIs("newRelationshipLabel");
        cyu.pasteOnLabel("newRelationshipLabel2");
        cyu.diagramRelationshipExists({
          relationshipLabel: "newRelationshipLabel2",
          sourceNodeLabel: "addressNewLabel",
          targetNodeLabel: CONSTANTS.FIZZ,
        });

        cy.log(
          "Case 7: Should be able to rename a concept that is connected to a relationship which got its URI recently changed"
        );

        cyu.getWorkingNode("addressNewLabel").click();
        cyu.detailsLabelInputIs("addressNewLabel");
        cyu.pasteOnLabel("addressNewLabel2");
        cyu.getDiagramNode("addressNewLabel2").should("exist");

        cy.log(
          "Case 8: Should be able to rename a relationship that is connected to a concept that has a recent modified uri, for the second time"
        );

        cyu.getWorkingNode("newRelationshipLabel2").click();
        cyu.detailsLabelInputIs("newRelationshipLabel2");
        cyu.pasteOnLabel("newRelationshipLabel3");
        cyu.diagramRelationshipExists({
          relationshipLabel: "newRelationshipLabel3",
          sourceNodeLabel: "addressNewLabel2",
          targetNodeLabel: CONSTANTS.FIZZ,
        });
      }
    );

    it("should create a self-relationship by using the diagram context pad -- " + action, () => {
      cyu.createAndValidateRelationshipFromDiagram(CONSTANTS.ADDRESS, CONSTANTS.ADDRESS);
      cyu.validateNewlyCreatedRelationship(CONSTANTS.FIRST_RELATIONSHIP_LABEL, [CONSTANTS.ADDRESS], true);
    });
  }

  it("DW24-1160 - Should be able to create a relationship by using the diagram tool from a newly created sub-concept that was renamed", () => {
    cyu.getWorkingNode(CONSTANTS.ANIMAL).click();
    cyu.createSubConcept(CONSTANTS.ANIMAL);

    cyu.detailsLabelInputIs(CONSTANTS.FIRST_SUB_CONCEPT_LABEL);
    cyu.pasteOnLabel("Madara");

    cyu.createAndValidateRelationshipFromDiagram("Madara", "Address");
  });
});
