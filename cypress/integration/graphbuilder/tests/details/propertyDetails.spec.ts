/** @format */

import * as cyu from "../../cypressUtil";
import { DETAILS_BASE, PROPERTY_DETAILS, WORKING_ONTOLOGY_TAB } from "../../descriptor";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    cyu.visitModelA();
  });

  describe("Animal Species Property Details", () => {
    beforeEach(() => {
      // expand concept
      cyu.toggleWorkingNode(cyu.CONSTANTS.ANIMAL);
      // select property
      cyu.getWorkingNode(cyu.CONSTANTS.SPECIES).click();
    });

    it("should show the details of the current property", () => {
      // should have a label
      cy.get(DETAILS_BASE.LABEL_INNER + `[value='${cyu.CONSTANTS.SPECIES}']`, cyu.original).should("exist");
      // should have a URI
      cy.get(
        DETAILS_BASE.URI_LOCAL_NAME + "[title='http://cambridgesemantics.com/ontologies/Base_Model#p_Species']",
        cyu.original
      ).should("exist");
    });

    it("should open the concept details when loading the page with the property url", () => {
      cy.get(DETAILS_BASE.LABEL_INNER).should("exist");
      cy.reload();
      cy.get(DETAILS_BASE.LABEL_INNER).should("not.exist");
      cy.get(WORKING_ONTOLOGY_TAB.SELF).should("not.exist");
      cy.get(DETAILS_BASE.LABEL_INNER + `[value='${cyu.CONSTANTS.SPECIES}']`, cyu.original).should("exist");
      cy.get(
        DETAILS_BASE.URI_LOCAL_NAME + "[title='http://cambridgesemantics.com/ontologies/Base_Model#p_Species']",
        cyu.original
      ).should("exist");
      cyu.workingTreeNodeIsSelected(cyu.CONSTANTS.SPECIES);
    });

    it("breadcrumbs - should navigate to the clicked resources", () => {
      // a property is selected
      cy.get(DETAILS_BASE.ENTITY_NAME).contains(cyu.CONSTANTS.SPECIES, cyu.original).should("be.visible");
      // navigate to clicked ontology via breadcrumbs
      cy.get(DETAILS_BASE.BREADCRUMBS).contains(cyu.CONSTANTS.MODEL_A, cyu.original).click();
      cy.get(DETAILS_BASE.ENTITY_NAME).contains(cyu.CONSTANTS.MODEL_A, cyu.original).should("be.visible");

      // select a property again
      cyu.getWorkingNode(cyu.CONSTANTS.SPECIES).click();
      cy.get(DETAILS_BASE.ENTITY_NAME).contains(cyu.CONSTANTS.SPECIES, cyu.original).should("be.visible");
      // navigate to parent concept via breadcrumbs
      cy.get(DETAILS_BASE.BREADCRUMBS).contains(cyu.CONSTANTS.ANIMAL, cyu.original).click();
      cy.get(DETAILS_BASE.ENTITY_NAME).contains(cyu.CONSTANTS.ANIMAL, cyu.original).should("be.visible");
    });

    it("details tab - should validate reused property warning message", () => {
      cy.get(DETAILS_BASE.REUSE_PROPERTY_WARNING).should("not.exist");
      // expand Animal Branch
      cyu.toggleWorkingNode("Yang");
      // a subConcept node is selected
      cyu.getWorkingNode("Karma").click();

      cy.get(DETAILS_BASE.REUSE_PROPERTY_WARNING).should("exist");
    });
  });

  describe("Address Concept Details", () => {
    beforeEach(() => {
      // select concept
      cyu.getWorkingNode(cyu.CONSTANTS.ADDRESS).click();
    });

    it("should change the range for a property and validate labels", () => {
      createsStringPropertyInAddressClass();

      cy.get(PROPERTY_DETAILS.DATA_TYPES_RANGE)
        .find("input")
        .should("have.attr", "maxlength", cyu.EXPECTED_DEFAULT_INPUT_MAX_LENGTH);
      cyu.validateMultiInputRangeLength(PROPERTY_DETAILS.DATA_TYPES_RANGE, 1);
      cyu.validateMultiInputRangeLabel(PROPERTY_DETAILS.DATA_TYPES_RANGE, "cm_string", 0);

      cyu.selectPropertyTargetDatatype(2);
      cyu.checkMultiInputListOccurrence(
        PROPERTY_DETAILS.DATA_TYPES_RANGE_POPUP_CANCEL_BUTTON,
        PROPERTY_DETAILS.DATA_TYPES_RANGE_POPUP_LIST,
        PROPERTY_DETAILS.DATA_TYPES_RANGE,
        ["cm_string", "cm_decimal"],
        0
      );
      cyu.validateMultiInputRangeLength(PROPERTY_DETAILS.DATA_TYPES_RANGE, 2);

      cyu.validateMultiInputRangeLabel(PROPERTY_DETAILS.DATA_TYPES_RANGE, "cm_string", 0);
      cyu.validateMultiInputRangeLabel(PROPERTY_DETAILS.DATA_TYPES_RANGE, "cm_decimal", 1);
      cyu.validateMultiInputRangeLength(PROPERTY_DETAILS.DATA_TYPES_RANGE, 2);
      cy.get(PROPERTY_DETAILS.DATA_TYPES_RANGE_STATUS_MSG).should("exist");
      cy.get(PROPERTY_DETAILS.DATA_TYPES_RANGE_STATUS_MSG).should(
        "have.text",
        "INPUTBASE_VALUE_STATE_WARNING cm_multiple_data_types_value_state_warning"
      );
    });

    it("should delete the range for a property and validate warning message", () => {
      createsStringPropertyInAddressClass();

      cyu.validateMultiInputRangeLength(PROPERTY_DETAILS.DATA_TYPES_RANGE, 1);
      cyu.validateMultiInputRangeLabel(PROPERTY_DETAILS.DATA_TYPES_RANGE, "cm_string", 0);
      cyu.checkMultiInputListOccurrence(
        PROPERTY_DETAILS.DATA_TYPES_RANGE_POPUP_CANCEL_BUTTON,
        PROPERTY_DETAILS.DATA_TYPES_RANGE_POPUP_LIST,
        PROPERTY_DETAILS.DATA_TYPES_RANGE,
        ["cm_string"],
        0
      );
      cyu.deleteSelectedRangeOptionByToken(PROPERTY_DETAILS.DATA_TYPES_RANGE, "cm_string");

      cyu.checkMultiInputListOccurrence(
        PROPERTY_DETAILS.DATA_TYPES_RANGE_POPUP_CANCEL_BUTTON,
        PROPERTY_DETAILS.DATA_TYPES_RANGE_POPUP_LIST,
        PROPERTY_DETAILS.DATA_TYPES_RANGE,
        ["cm_string"],
        1
      );

      cy.get(PROPERTY_DETAILS.DATA_TYPES_RANGE_STATUS_MSG).should("exist");
      cy.get(PROPERTY_DETAILS.DATA_TYPES_RANGE_STATUS_MSG).should(
        "have.text",
        "INPUTBASE_VALUE_STATE_ERROR cm_any_data_type_selected_warning"
      );

      cy.log("should be able to delete the range by selecting all tokens and pressing backspace");

      cyu.selectPropertyTargetDatatype(1);
      cyu.selectPropertyTargetDatatype(2);

      cy.get(PROPERTY_DETAILS.DATA_TYPES_RANGE_STATUS_MSG).should("not.exist");
      cy.get(PROPERTY_DETAILS.DATA_TYPES_RANGE).find(".sapMInputBaseContentWrapperWarning").should("exist");
      cy.get(PROPERTY_DETAILS.DATA_TYPES_RANGE).find(".sapMToken").should("have.length", 2);
      cy.get(PROPERTY_DETAILS.DATA_TYPES_RANGE)
        .find("input")
        .focus() // focus first to avoid value help dialog popping up
        .type("{ctrl+a}{backspace}", { force: true } /* force is needed because of the value help added html props */);
      cy.get(PROPERTY_DETAILS.DATA_TYPES_RANGE).find(".sapMToken").should("not.exist");
    });

    it("should check if properties data type range is not visible when we are editing a relationship", () => {
      cyu.createRelationship(cyu.CONSTANTS.ADDRESS);
      // looks like this is already tested in other spec and commenting to pass the jenkins build test
      // cyu.validateNewlyCreatedRelationship(cyu.CONSTANTS.FIRST_RELATIONSHIP_LABEL, []);
      cy.get(PROPERTY_DETAILS.DATA_TYPES_RANGE_TITLE).should("not.exist");
    });

    function createsStringPropertyInAddressClass() {
      cyu.createStringProperty(cyu.CONSTANTS.ADDRESS);
      cyu.selectedTokenIs(["cm_string"], PROPERTY_DETAILS.DATA_TYPES_RANGE);
      cyu.checkMultiInputListOccurrence(
        PROPERTY_DETAILS.DATA_TYPES_RANGE_POPUP_CANCEL_BUTTON,
        PROPERTY_DETAILS.DATA_TYPES_RANGE_POPUP_LIST,
        PROPERTY_DETAILS.DATA_TYPES_RANGE,
        ["cm_string"],
        0
      );
    }
  });
});
