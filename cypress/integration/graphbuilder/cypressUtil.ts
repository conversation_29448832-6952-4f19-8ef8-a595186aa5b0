/** @format */

import { Parser, Store, Writer } from "n3";
import { GalileiCustomEvents } from "../../../src/components/graphbuilder/util/EventUtil";
import { XHelpers } from "../../pageobjects/api/impl/Helpers";
import { BB_URL } from "../../pageobjects/businessbuilder/descriptor";
import {
  CM_REQUESTS,
  CM_URL,
  CONCEPT_DETAILS,
  DETACHED_PROPERTY,
  DETAILS_BASE,
  DIAGRAM,
  DIAGRAM_CONTEXT_PAD,
  EDITURI_DIALOG,
  GB_CONTAINER,
  GB_URL,
  LANDING_PAGE,
  LANDING_PAGE_TABLE,
  ONTOLOGY_DETAILS,
  PROPERTY_DETAILS,
  SOURCE_CONCEPTS_TAB,
  TOOLBAR,
  TREE_PANEL,
  WORKBENCH,
} from "./descriptor";
import { IModelGraph } from "./interface";

/**
 * This is a Util file that can be reused across the graphbuilder cypress tests
 *
 * it can be imported like this:
 * import * as cyu from "../../util";
 * import { original } from "../../util";

 * Note that by doing this you have the autocomplete after typing cyu,
 * CYU stands for Cypress Utils
 */

export const original = { cyOriginal: true };

export enum Privileges {
  Read = "read",
  Create = "create",
  Update = "update",
  Delete = "delete",
}

const defaultSpaceStatus = { "#deleteContentAccess": true, "#writeContentAccess": true };

const PLAIN_CONSTANTS = {
  HEADERS: {
    CONCEPT: "cm_concept",
    PROPERTY: "cm_property",
    RELATIONSHIP: "cm_relationship",
    SUB_CONCEPT: "cm_sub_concept",
  },
  ICONS: {
    CONCEPT: "sac/concept",
    SUB_CONCEPT: "sac/sub-concept",
    PROPERTY_NUMBER: "sac/number",
    PROPERTY_STRING: "text",
    RELATIONSHIP: "sac/relationship",
    ONTOLOGY: "connection",
    FOLDER: "folder-blank",
  },
  COLORS: {
    SAP_CONTENT_DISABLED_TEXT_COLOR: "rgba(51, 51, 51, 0.5)",
    SAP_LIST_TEXT_COLOR: "rgb(51, 51, 51)",
  },
  EMPTY_ONTOLOGY: "cm_empty_ontology",
  OWNED_BY: "Owned by",
  OPPORTUNITIES: "Opportunities",
  CONCEPT: "cm_concept",
  SUB_CONCEPT: "cm_sub_concept",
  PROPERTY: "cm_property",
  RELATIONSHIP: "cm_relationship",
  MODEL_A: "Model A",
  MODEL_B: "Model B",
  BOOK_SALES_ONTOLOGY: "Book Sales Ontology",
  SPECIAL_CHARACTERS_ONTOLOGY: "Spec!al_ch@r@cter$ ontology",
  GRANDPARENT_MODEL: "Grandparent Model",
  ISLAND_RESORT_MARKETING: "Island Resort Marketing",
  SALES_MODEL: "Sales Model",
  BASE_MODEL: "Base Model",
  BASE_MODEL_COPY: "Base Model 1",
  CUSTOMER: "Customer",
  INVOICE_LINE: "Invoice Line",
  RESERVATION: "Reservation",
  RESERVATION_LINE: "Reservation Line",
  RESORT: "Resort",
  SALE: "Sale",
  SALES_PERSON: "Sales Person",
  SERVICE: "Service",
  FIZZ: "Fizz",
  FOO: "Foo",
  ANIMAL: "Animal",
  ASSISTING_SERVICE: "Assisting Service",
  BARK_TYPE: "Bark Type",
  DOG: "Dog",
  ADDRESS: "Address",
  CITY: "City",
  EMPLOYEE: "Employee",
  CHILD: "Child",
  SERVICE_DOG: "Service Dog",
  WIDGET: "Widget",
  PERSON: "Person",
  NAME: "Name",
  JOB_TITLE: "Job Title",
  SPECIES: "Species",
  REGION: "Region",
  COUNTRY: "Country",
  YANG: "Yang",
  YING: "Ying",
  KARMA: "Karma",
  HAS_TOOL: "Has Tool",
  HAS_NAME: "Has Name",
  GRANDPARENT: "Grandparent",
  PARENT: "Parent",
  PARENTCHILDTEST: "PrentChldTest",
  DETACHED_PROP_1: "Detached Prop 1",
  DETACHED_PROP_2: "Detach Property (2)",
  DETACHED_PROP_3: "Detach Property (3)",
  CLASS_1: "Class 1",
  DETACHED_ONT: "One detached prop ontology",
  RDF_LIST_ONT: "RDFList",
  PARENT_CHILD_ONT: "PrentChldTest",
  ITEM_MODEL: "Item",
};

const COMPLEX_CONSTANTS = {
  FIRST_CONCEPT_LABEL: generateIndexedLabel(PLAIN_CONSTANTS.CONCEPT, 1),
  SECOND_CONCEPT_LABEL: generateIndexedLabel(PLAIN_CONSTANTS.CONCEPT, 2),
  THIRD_CONCEPT_LABEL: generateIndexedLabel(PLAIN_CONSTANTS.CONCEPT, 3),
  FIRST_SUB_CONCEPT_LABEL: generateIndexedLabel(PLAIN_CONSTANTS.SUB_CONCEPT, 1),
  SECOND_SUB_CONCEPT_LABEL: generateIndexedLabel(PLAIN_CONSTANTS.SUB_CONCEPT, 2),
  FIRST_PROPERTY_LABEL: generateIndexedLabel(PLAIN_CONSTANTS.PROPERTY, 1),
  SECOND_PROPERTY_LABEL: generateIndexedLabel(PLAIN_CONSTANTS.PROPERTY, 2),
  FIRST_RELATIONSHIP_LABEL: generateIndexedLabel(PLAIN_CONSTANTS.RELATIONSHIP, 1),
  SECOND_RELATIONSHIP_LABEL: generateIndexedLabel(PLAIN_CONSTANTS.RELATIONSHIP, 2),
};

function getIconName(url: string): string | null {
  if (url) {
    const part = url.split("//");
    return part[part.length - 1];
  }
  return null;
}

function checkIcon(result: jQuery, targetIconName: string) {
  const id = result.attr("id");
  // forcing because it complains that we are not using ui5 predefined ids
  cy.byId(id, { force: true }).then((icon: sap.ui.core.Icon) => {
    cy.wrap({ getIconName: () => getIconName(icon.getSrc()) })
      .invoke("getIconName")
      .should("eq", targetIconName);
  });
}

export const WARNING_TYPES = {
  WARNING: "Warning",
  ERROR: "Error",
};

export const EXPECTED_DEFAULT_INPUT_MAX_LENGTH = 1000;

export const CONSTANTS = { ...COMPLEX_CONSTANTS, ...PLAIN_CONSTANTS };

export function generateIndexedLabel(label: string, index: number) {
  return `${label} ${index}`;
}

type TreeType = "working" | "source" | "sourceSearch" | "workingSearch";

// navigation methods

export const setupBeforeEach = (
  featureFlags: object = {},
  enableGraphBuilderFeatureFlag = true,
  privileges = Object.values(Privileges),
  isSpaceLocked = false
) => {
  interceptTenantClassificationRequest();
  cy.log("start: setupBeforeEach");
  cy.mocksWithIntercept("default");
  XHelpers.setupRoutes([...XHelpers.COMMON_MOCK_RESPONSES]);
  cy.intercept("GET", "**/featureflags", {
    DWCO_GRAPH_ONTOLOGY_EDITOR: enableGraphBuilderFeatureFlag,
    DWC_DUMMY_SPACE_PERMISSIONS: true,
    ...featureFlags,
  });

  if (!featureFlags.hasOwnProperty("DWCO_GRAPH_REPO_EXPLORER")) {
    cy.intercept("GET", "**spaces?islocked=&spaceids=" + BB_URL.SPACE, {
      [GB_URL.SPACE]: { isLocked: isSpaceLocked },
    }).as("getSpaceLockStatus");
  }

  cy.fixture("graphbuilder/repository/spaceDetails.json").then((data) => {
    if (isSpaceLocked) {
      data.results[0].status = "locked";
    }
    cy.intercept("GET", "**dwaas-core/repository/spaces?ids=1234**", data).as("getSpaceDetails");
  });

  cy.intercept("GET", "**snow-proxy/api/api/v1/space/sync?spaceId=**", {
    body: {
      headers: {},
      body: {
        objectUri: "http://sap.com/Datasphere/Space/RIX",
        lastSyncDate: 1664468255227,
      },
      statusCode: "OK",
      statusCodeValue: 200,
    },
  }).as("spaceSync");

  cy.intercept("POST", "**dwaas-core/deepsea/repository/**/objects", {
    body: {},
  }).as("deepseaSpaceSync");

  cy.fixture("userprivilegesSDP").then((data) => {
    // eslint-disable-next-line dot-notation
    const { SPACE1234 } = data["spaces"];
    const SPACE_WITH_ACCESS = SPACE1234;
    const privilegeItemGraphBuilder = SPACE_WITH_ACCESS.find((privilege) => privilege.type === "DWC_GRAPH_MODELER");
    privilegeItemGraphBuilder.privileges = privileges;
    // eslint-disable-next-line dot-notation
    data["spaces"]["SPACE_WITH_ACCESS"] = SPACE_WITH_ACCESS;
    // eslint-disable-next-line dot-notation
    data["global"] = SPACE_WITH_ACCESS;
    cy.intercept("GET", "**/userprivileges**", data);
  });
  cy.fixture("navigationMenu").then((data) => {
    if (enableGraphBuilderFeatureFlag) {
      data.items.push({
        title: "menu_graphbuilder",
        icon: "sap-icon://overview-chart",
        key: "graphbuilder",
        featureFlags: ["DWCO_GRAPH_ONTOLOGY_EDITOR"],
        privileges: [],
        parameters: ["spaceId", "objectId"],
      });
    } else {
      data.items = data.items.filter((item: any) => item.title !== "menu_graphbuilder");
    }
    cy.intercept("GET", "**/navigation/menu", data);
  });

  cy.visit("#/");
  cy.get("[id=welcomeCardOuterCard]").should("be.visible");
  cy.log("end: setupBeforeEach");
};

// Note: this message turns our cypress tests flaky because if the message appears after
// the tree is rendered it makes the tree rerender, and thus cypress tries to click on detached elements
// so we want to make sure the tenant info message strip does not appear to avoid this rerender.
const interceptTenantClassificationRequest = () => {
  cy.intercept("**dwaas-core/security/customtenantclassification**", {
    statusCode: 404,
    body: "Not Found [404]",
  });
};
export const visitModelA = (featureFlags: object = {}, delayInMilliseconds: number = 0) => {
  setupBeforeEach(featureFlags);

  const isRepoIntegrationActive = (featureFlags as any).DWCO_GRAPH_REPO_EXPLORER;
  if (isRepoIntegrationActive) {
    // this currently doesn't mock folder data for source concepts, just intercepts the correct call
    cy.intercept("GET", CM_REQUESTS.SOURCES_WITH_FOLDERS, {
      fixture: "graphbuilder/conceptualModel/sourceConcepts/getSourceConcepts.json",
    }).as("getSourceConcepts");

    cy.intercept("GET", CM_REQUESTS.MODEL_A_ONT, {
      fixture: "graphbuilder/conceptualModel/initialWorkingModelLoad/getModelsModelA.json",
      delay: delayInMilliseconds,
    }).as("getOntology");

    cy.intercept("GET", "**deepsea/repository/SPACE1234/designObjects?folderIds=1234&details=id**", {
      fixture: "graphbuilder/repository/sync/getAllGuidsInSpace.json",
    }).as("getAllGuidsInSpace");

    cy.intercept("GET", "**/repository/SPACE1234/designObjects?ids=MODEL_A_REPO_GUID&details=id%2C%23repairedCsn**", {
      fixture: "graphbuilder/repository/sync/modelADetails.json",
    }).as("getModelADetailsFromRepository");

    cy.intercept("POST", "**deepsea/repository/touch/**", {
      message: "object with id = Model_A_REPO_GUID, 1234 is touched",
    }).as("touchModelARepoObject");
  } else {
    cy.intercept("GET", CM_REQUESTS.SOURCES, {
      fixture: "graphbuilder/conceptualModel/sourceConcepts/getSourceConcepts.json",
    }).as("getSourceConcepts");

    cy.intercept("GET", CM_REQUESTS.MODEL_A_ONT, {
      fixture: "graphbuilder/conceptualModel/initialWorkingModelLoad/getModelsModelA.json",
      delay: delayInMilliseconds,
    }).as("getOntology");
  }

  cy.visit(CM_URL.MODEL_A, { skipResourceBundle: true }).then(() => {
    if (isRepoIntegrationActive) {
      cy.log("Ensure opening an ontology retrieves info from repository");
      cy.wait("@getAllGuidsInSpace");
      cy.wait("@getModelADetailsFromRepository");
    }
    getWorkingTree().should("be.visible");
    detailsLabelInputIs(CONSTANTS.MODEL_A);
    cy.get(`${GB_CONTAINER} .sapUiLocalBusy`).should("not.exist");
    cy.get("#systemInfoBannerHBox").should("be.visible");
  });
};

export const interceptUriValidation = (uri: string, isValid: boolean) => {
  cy.intercept("GET", `**/technicalNames/validate?spaceId=SPACE1234&technicalName=${encodeURIComponent(uri)}`, {
    body: {
      body: { valid: isValid },
      statusCodeValue: 200,
      statusCode: "OK",
    },
    delay: 2000,
  }).as("validateUri");
};

export const visitEmptyStateModel = (featureFlags: object = {}, delayInMilliseconds: number = 0) => {
  setupBeforeEach(featureFlags);

  cy.intercept("GET", CM_REQUESTS.SOURCES, {
    fixture: "graphbuilder/conceptualModel/initialWorkingModelLoad/getModelsModelB.json", // Mudar isso para ser uma resposta vazia
  }).as("getSourceConcepts");

  cy.intercept("GET", CM_REQUESTS.MODEL_B_ONT, {
    fixture: "graphbuilder/conceptualModel/initialWorkingModelLoad/getModelsModelB.json",
    delay: delayInMilliseconds,
  }).as("getOntology");

  cy.visit(CM_URL.MODEL_B, { skipResourceBundle: true }).then(() => {
    getWorkingTree().should("be.visible");
    detailsLabelInputIs(CONSTANTS.MODEL_B);
    cy.get(`${GB_CONTAINER} .sapUiLocalBusy`).should("not.exist");
  });
};

export const visitModelRdfList = (featureFlags: object = {}, _delayInMilliseconds: number = 0) => {
  setupBeforeEach(featureFlags);

  cy.intercept("GET", CM_REQUESTS.SOURCES, {
    fixture: "graphbuilder/conceptualModel/sourceConcepts/getSourceConcepts.json",
  }).as("getSourceConcepts");

  mockOntologyRequestWithRdfData("http://sap.com/ontology/rdflist", "rdf_list_ontology.trig", CM_REQUESTS.RDF_LIST_ONT);

  cy.visit(CM_URL.RDF_LIST_MODEL, { skipResourceBundle: true }).then(() => {
    getWorkingTree().should("be.visible");
    detailsLabelInputIs(CONSTANTS.RDF_LIST_ONT);
  });
};

export const visitModelParentChildTest = (featureFlags: object = {}, _delayInMilliseconds: number = 0) => {
  setupBeforeEach(featureFlags);

  cy.intercept("GET", CM_REQUESTS.SOURCES, {
    fixture: "graphbuilder/conceptualModel/sourceConcepts/getSourceConcepts.json",
  }).as("getSourceConcepts");

  mockOntologyRequestWithRdfData(
    "http://sap.com/ontology/rdflist",
    "parent_child_test.trig",
    CM_REQUESTS.PARENT_CHILD_ONT
  );

  cy.visit(CM_URL.PARENT_CHILD_MODEL, { skipResourceBundle: true }).then(() => {
    getWorkingTree().should("be.visible");
    detailsLabelInputIs(CONSTANTS.PARENT_CHILD_ONT);
  });
};

export const visitDetachedPropOnt = (
  featureFlags: object = {},
  privileges = Object.values(Privileges),
  _delayInMilliseconds: number = 0
) => {
  setupBeforeEach(featureFlags, true, privileges);

  cy.intercept("GET", CM_REQUESTS.SOURCES, {
    fixture: "graphbuilder/conceptualModel/sourceConcepts/getSourceConcepts.json",
  }).as("getSourceConcepts");

  mockOntologyRequestWithRdfData("http://test.org/detachedprop", "detachedprop.trig", CM_REQUESTS.DETACHED_PROPS_ONT);

  cy.visit(CM_URL.DETACHED_PROP_MODEL, { skipResourceBundle: true }).then(() => {
    getWorkingTree().should("be.visible");
    detailsLabelInputIs(CONSTANTS.DETACHED_ONT);
    cy.get("#systemInfoBannerHBox").should("be.visible");
  });
};

export const closeDialog = (dialogId: string) => {
  getUi5(dialogId).then((object) => {
    object.close();
  });
};

export const visitBookSalesOntology = (_delayInMilliseconds: number = 0) => {
  setupBeforeEach();

  cy.intercept("GET", CM_REQUESTS.SOURCES, {
    fixture: "graphbuilder/conceptualModel/sourceConcepts/getSourceConcepts.json",
  }).as("getSourceConcepts");

  mockOntologyRequestWithRdfData(
    "http://sap.ds/conceptual_model/book_sales_ontology2",
    "book_sales_ontology2.trig",
    CM_REQUESTS.BOOK_SALES_ONTOLOGY
  );

  cy.visit(CM_URL.BOOK_SALES_ONTOLOGY_MODEL, { skipResourceBundle: true }).then(() => {
    getWorkingTree().should("be.visible");
    detailsLabelInputIs(CONSTANTS.BOOK_SALES_ONTOLOGY);
  });
};

export const visitSpecialCharactersOntology = (featureFlags: object = {}, _delayInMilliseconds: number = 0) => {
  setupBeforeEach(featureFlags);

  cy.intercept("GET", CM_REQUESTS.SOURCES, {
    fixture: "graphbuilder/conceptualModel/sourceConcepts/getSourceConcepts.json",
  }).as("getSourceConcepts");

  mockOntologyRequestWithRdfData(
    "http://sap.com/ontology/Spec!al_ch@r@cter$",
    "special_characters.trig",
    CM_REQUESTS.SPECIAL_CHARACTERS_ONTOLOGY
  );

  cy.visit(CM_URL.SPECIAL_CHARACTERS_MODEL, { skipResourceBundle: true }).then(() => {
    getWorkingTree().should("be.visible");
    detailsLabelInputIs(CONSTANTS.SPECIAL_CHARACTERS_ONTOLOGY);
  });
};

export const visitModelANoLocalName = (delayInMilliseconds: number = 0) => {
  setupBeforeEach();

  cy.intercept("GET", CM_REQUESTS.SOURCES, {
    fixture: "graphbuilder/conceptualModel/sourceConcepts/getSourceConceptsNoLocalName.json",
  }).as("getSourceConcepts");

  cy.intercept("GET", CM_REQUESTS.MODEL_A_ONT, {
    fixture: "graphbuilder/conceptualModel/initialWorkingModelLoad/getModelsModelA.json",
    delay: delayInMilliseconds,
  }).as("getOntology");

  cy.visit(CM_URL.MODEL_A, { skipResourceBundle: true }).then(() => {
    getWorkingTree().should("be.visible");
    detailsLabelInputIs(CONSTANTS.MODEL_A);
  });
};

export const visitBaseModel = (featureFlags: object = {}, delayInMilliseconds: number = 0) => {
  setupBeforeEach(featureFlags);

  let sourcesRequest = CM_REQUESTS.SOURCES;
  let ontRequest = CM_REQUESTS.BASE_MODEL_ONT_WITH_FRAMES;
  // eslint-disable-next-line dot-notation
  if (featureFlags["DWCO_GRAPH_REPO_EXPLORER"]) {
    sourcesRequest = sourcesRequest.replace("&multiPropFlag=true", "&includeFolderData=true&multiPropFlag=true");
  }
  cy.intercept("GET", sourcesRequest, {
    fixture: "graphbuilder/conceptualModel/sourceConcepts/getSourceConceptsForBaseModel.json",
  }).as("getSourceConcepts");

  cy.intercept("GET", ontRequest, {
    fixture: "graphbuilder/conceptualModel/initialWorkingModelLoad/getBaseModel.json",
    delay: delayInMilliseconds,
  }).as("getOntology");

  cy.visit(CM_URL.BASE_MODEL, { skipResourceBundle: true }).then(() => {
    getWorkingTree().should("be.visible");
    detailsLabelInputIs(CONSTANTS.BASE_MODEL);
  });

  cy.wait("@getOntology");
};

export const visitBookSalesForParentChildTest = (featureFlags: object = {}, _delayInMilliseconds: number = 0) => {
  setupBeforeEach(featureFlags);

  cy.intercept("GET", CM_REQUESTS.SOURCES, {
    fixture: "graphbuilder/conceptualModel/sourceConcepts/getSourceConceptsWithGrandparent.json",
  }).as("getSourceConcepts");

  mockOntologyRequestWithRdfData(
    "http://sap.ds/conceptual_model/book_sales_ontology2",
    "book_sales_ontology2.trig",
    CM_REQUESTS.BOOK_SALES_ONTOLOGY
  );

  cy.visit(CM_URL.BOOK_SALES_ONTOLOGY_MODEL, { skipResourceBundle: true }).then(() => {
    getWorkingTree().should("be.visible");
    detailsLabelInputIs(CONSTANTS.BOOK_SALES_ONTOLOGY);
  });
};

export const interceptSearchModel = () => {
  cy.intercept("GET", CM_REQUESTS.MODELS_SEARCH, {
    fixture: "graphbuilder/conceptualModel/sourceConcepts/search/modelsSearch.json",
  });
};

export const visitIslandResortMarketing = () => {
  setupBeforeEach();

  cy.intercept("GET", CM_REQUESTS.SOURCES, {
    fixture: "graphbuilder/conceptualModel/sourceConcepts/getIslandResortMarketingSourceConcepts.json",
  }).as("getSourceConcepts");
  cy.intercept("GET", CM_REQUESTS.ISLAND_RESORT_MODEL, {
    fixture: "graphbuilder/conceptualModel/sourceConcepts/getIslandResortMarketing.json",
  }).as("getOntology");

  cy.visit(CM_URL.ISLAND_RESORT_MARKETING, { skipResourceBundle: true }).then(() => {
    getWorkingTree().should("be.visible");
  });
};

export const visitAdvancedRelationshipsModel = () => {
  setupBeforeEach();

  cy.intercept("GET", CM_REQUESTS.SOURCES, {
    fixture: "graphbuilder/conceptualModel/sourceConcepts/getSourceConcepts.json",
  }).as("getSourceConcepts");
  cy.intercept("GET", CM_REQUESTS.ADVANCED_PROPS_ONTOLOGY, {
    fixture: "graphbuilder/conceptualModel/initialWorkingModelLoad/getAdvancedPropsModel.json",
  }).as("getOntology");
  cy.visit(CM_URL.ADVANCED_PROPS, { skipResourceBundle: true }).then(() => {
    getWorkingTree().should("be.visible");
  });
};

export const visitGraphBuilderLandingPage = (
  featureFlags: object = {},
  graphBuilderFeatureFlagEnabled = true,
  privileges = Object.values(Privileges),
  spaceStatus = defaultSpaceStatus,
  isSpaceLocked = false
) => {
  cy.log("start: visitGraphBuilderLandingPage");
  setupBeforeEach(featureFlags, graphBuilderFeatureFlagEnabled, privileges, isSpaceLocked);

  cy.fixture("spacesResponse.json").then((data) => {
    const foundMockedData = data.results.find((space) => space.name === "SPACE1234");
    foundMockedData["#deleteContentAccess"] = spaceStatus["#deleteContentAccess"];
    foundMockedData["#writeContentAccess"] = spaceStatus["#writeContentAccess"];
  });

  mockSourceFramesWithRdfData("/landingPage/get_frames.trig");

  cy.visit(GB_URL.BASE, { skipResourceBundle: true });
  cy.log("end: visitGraphBuilderLandingPage");
};

export const browserUrlContainsText = (uri: string) => {
  cy.url().should((url) => {
    expect(decodeURIComponent(url)).to.contain(uri);
  });
};

export const browserUrlDoesNotContainText = (uri: string) => {
  cy.url().should((url) => {
    expect(decodeURIComponent(url)).not.to.contain(uri);
  });
};

export function switchToSourceConceptsTreeView() {
  cy.get(TREE_PANEL.NAVIGATION_HEADER).click();
  cy.get(".sapMPopoverCont.sapUiScrollDelegate", original)
    .contains("cm_source_concepts_tab", original)
    .closest("li")
    .click({ force: true }); // I could not find a way to get rid of this force here
}

export function switchToWorkingOntologyTreeView() {
  cy.get(TREE_PANEL.NAVIGATION_HEADER).click();
  cy.get(".sapMPopoverCont.sapUiScrollDelegate", original)
    .contains("cm_working_ontology_tab", original)
    .closest("li")
    .click({ force: true }); // I could not find a way to get rid of this force here
}

export const clickOnDetailsToggle = () => {
  cy.get(TOOLBAR.DETAILS_TOGGLE).click();
};

export const selectWorkingTreeNode = (label: string) => {
  ensureThereAreNoBusyStates();
  getWorkingNode(label).focus().click();
  detailsLabelInputIs(label);
  ensureThereAreNoBusyStates();
};
export const ensureThereAreNoBusyStates = () => {
  // this local busy class is added whenever an element has the isBusy set as true
  // e.g myElement.setBusy(true);
  cy.get(".sapUiLocalBusyIndicator").should("not.exist");
};

export const clickOnTreePanelToggle = () => {
  cy.get(TOOLBAR.TREE_PANEL_TOGGLE).click();
};

export const performDragAndDrop = (handleSelector: string, endPosition: { x: number; y: number }) => {
  const dataTransfer = new DataTransfer();

  cy.get(handleSelector, original)
    .eq(0)
    .trigger("keydown", { keyCode: 16, force: true })
    .trigger("mousedown", { which: 1, force: true })
    .trigger("dragstart", { dataTransfer, force: true })
    .trigger("mousemove", endPosition.x, endPosition.y, { force: true })
    .trigger("mouseup", { force: true })
    .trigger("dragend", { force: true });
};

// tree methods

export function getSourcesTree() {
  return cy.get(TREE_PANEL.SOURCE_CONCEPTS_TREE);
}
export function getSearchSourcesTree() {
  return cy.get(TREE_PANEL.SOURCE_CONCEPTS_SEARCH_TREE);
}

export function getWorkingTree() {
  return cy.get(TREE_PANEL.WORKING_ONTOLOGY_TREE);
}

export function getSearchWorkingTree() {
  return cy.get(TREE_PANEL.WORKING_ONTOLOGY_SEARCH_TREE);
}

const getTree = (treeType: TreeType) => {
  if (treeType === "working") {
    return getWorkingTree();
  } else if (treeType === "source") {
    return getSourcesTree();
  } else if (treeType === "sourceSearch") {
    return getSearchSourcesTree();
  } else if (treeType === "workingSearch") {
    return getSearchWorkingTree();
  }
};

export const clickOnConceptAddButton = (conceptNode: string | number) => {
  getWorkingNode(conceptNode).click();
  getWorkingNode(conceptNode).find('button[title*="cm_add_to_concept"]').click();
};

export const clickOnSubConceptAddButton = (conceptNode: string | number) => {
  getWorkingNode(conceptNode).click();
  getWorkingNode(conceptNode).find('button[title*="cm_add_to_subconcept"]').click();
};

export const clickOnAddButton = (conceptNode: string | number) => {
  getWorkingNode(conceptNode).click();
  getWorkingNode(conceptNode).find('button[title*="cm_add"]').click();
};

// node retrieval methods

const getNodeText = (nodeName: string, treeType: TreeType, options: object = { cyOriginal: true }) =>
  getTree(treeType).contains(nodeName, options);

export const getSourceNodeLabel = (nodeName: string, options: object = { cyOriginal: true }) =>
  getNodeText(nodeName, "source", options);

export const getWorkingNodeLabel = (nodeName: string, options: object = { cyOriginal: true }) =>
  getNodeText(nodeName, "working", options);

const getNodeLine = (nodeIndex: number, treeType: TreeType) =>
  getTree(treeType).find(".sapMTreeItemBase").eq(nodeIndex);

const getNodeLineByLabel = (nodeName: string, treeType: TreeType, options: object = { cyOriginal: true }) =>
  getTree(treeType).contains(nodeName, options).closest("li[role='treeitem']");

const getNode = (nodeIdentifier: string | number, treeType: TreeType) => {
  if (typeof nodeIdentifier === "string") {
    return getNodeLineByLabel(nodeIdentifier, treeType);
  } else if (typeof nodeIdentifier === "number") {
    return getNodeLine(nodeIdentifier, treeType);
  } else {
    throw new Error("Invalid argument type. Expected string or number.");
  }
};

export const getSourceNode = (nodeIdentifier: string | number) => getNode(nodeIdentifier, "source");
export const getSearchSourceNode = (nodeIdentifier: string | number) => getNode(nodeIdentifier, "sourceSearch");

export const getWorkingNode = (nodeIdentifier: string | number) => getNode(nodeIdentifier, "working");
export const getSearchWorkingNode = (nodeIdentifier: string | number) => getNode(nodeIdentifier, "workingSearch");

export const getDiagram = () => cy.get(WORKBENCH.DIAGRAM);

export const getDiagramRelationshipLink = (label: string, index = 0) =>
  cy.get(`${WORKBENCH.DIAGRAM} .RelationshipLinkSymbol[sap-automation*="${label}"]`, original).eq(index);

export const getDiagramSubconceptLink = (nodeLabel: string, index = 0) =>
  cy.get(`${WORKBENCH.DIAGRAM} [sap-automation='SubconceptLink/${nodeLabel}']`, original).eq(index);

export const getDiagramNode = (nodeLabel: string, index = 0) =>
  cy.get(`${WORKBENCH.DIAGRAM} [sap-automation='Entity/${nodeLabel}']`, original).eq(index);

export const performActionOnDiagramNode = (action: string, constant: string) => {
  if (action === "click") {
    getDiagramNode(constant).click();
  } else if (action === "hover") {
    getDiagramNode(constant).trigger("mouseover");
  } else if (action === "mouseout") {
    getDiagramNode(constant).trigger("mouseout");
  }
};

// toggle methods

const toggleNodeByName = (nodeName: string, treeType: TreeType) =>
  getNodeLineByLabel(nodeName, treeType).find(".sapUiIconTitle").first().click();

const toggleNodeByIndex = (nodeIndex: number, treeType: TreeType) =>
  getTree(treeType).find("li[role='treeitem']").eq(nodeIndex).find(".sapUiIconTitle").first().click();

const toggleNode = (nodeIdentifier: string | number, treeType: TreeType) => {
  cy.wait(500); // TODO: remove this after DW24-731 gets fixed
  if (typeof nodeIdentifier === "string") {
    return toggleNodeByName(nodeIdentifier, treeType);
  } else if (typeof nodeIdentifier === "number") {
    return toggleNodeByIndex(nodeIdentifier, treeType);
  } else {
    throw new Error("Invalid argument type. Expected string or number.");
  }
};
export const toggleSearchSourceNode = (nodeIdentifier: string | number) => toggleNode(nodeIdentifier, "sourceSearch");

export const toggleSourceNode = (nodeIdentifier: string | number) => toggleNode(nodeIdentifier, "source");

export const toggleWorkingNode = (nodeIdentifier: string | number) => toggleNode(nodeIdentifier, "working");

export const toggleSearchWorkingNode = (nodeIdentifier: string | number) => toggleNode(nodeIdentifier, "workingSearch");

export const getLandingTableCell = (rowIndex: number, columnIndex: number) =>
  cy.get(`${LANDING_PAGE_TABLE.SELF} tr.sapUiTableContentRow`).eq(rowIndex).find("td").eq(columnIndex);

export const openEditUriDialog = (isOntologyLevel: boolean = false) => {
  if (isOntologyLevel) {
    cy.get(DETAILS_BASE.EDITURI_ONTOLOGY_BUTTON).click();
  } else {
    cy.get(DETAILS_BASE.EDITURI_BUTTON).click();
  }
};

export const cancelAndCloseEditUriDialog = () => {
  cy.get(EDITURI_DIALOG.CANCEL_BUTTON).click();
};
export const saveAndCloseEditUriDialog = () => {
  cy.get(EDITURI_DIALOG.SAVE_URI_BUTTON).should("be.visible").click();
};

// crud operations

export const selectLandingRowCheckbox = (index: number) =>
  cy.get(`${LANDING_PAGE_TABLE.SELF} .sapUiTableRowSelectionCell`).eq(index).click();

export const createProperty = (conceptNode: string | number, propertyTypeI18nKey: string) => {
  getWorkingNode(conceptNode).contains("cm_add_to_concept", original).closest(".sapMBtn").click();
  clickOnMenuItem(CONSTANTS.PROPERTY, ".addToConceptMenu.sapMMenu");
  isMenuItem(propertyTypeI18nKey);
  clickOnMenuItem(propertyTypeI18nKey, ".addToConceptMenu.sapMMenu");
};

export const createPropertyOnSubConcept = (conceptNode: string, propertyTypeI18nKey: string) => {
  clickOnSubConceptAddButton(conceptNode);
  clickOnMenuItem(CONSTANTS.PROPERTY, ".addToConceptMenu.sapMMenu");
  clickOnMenuItem(propertyTypeI18nKey, ".addToConceptMenu.sapMMenu");
};

export const createStringPropertyOnSubConcept = (conceptNode: string) => {
  createPropertyOnSubConcept(conceptNode, "cm_string");
};

export const createNumberProperty = (conceptNode: string | number) => createProperty(conceptNode, "cm_integer");

export const createStringProperty = (conceptNode: string | number) => createProperty(conceptNode, "cm_string");

export const createRelationship = (conceptNode: string | number) => {
  getWorkingNode(conceptNode).find('button[title*="cm_add_to_concept"]').click();
  cy.get(".sapMMenu")
    .invoke("show")
    .then(() => {
      clickOnMenuItem(CONSTANTS.RELATIONSHIP);
    });
};

export const createRelationshipOnSubConcept = (conceptNode: string, targetConcept?: string) => {
  clickOnSubConceptAddButton(conceptNode);
  cy.get(".addToConceptMenu.sapMMenu", original).contains("cm_relationship", original).closest("li").click();
  if (targetConcept) {
    toggleTargetConcept(targetConcept);
  }
};

export const createAndValidateRelationshipFromDiagram = (
  baseConceptLabel: string,
  targetConceptLabel: string,
  forceGalileiSapAutomationUpdate = true
) => {
  createRelationshipFromDiagram(baseConceptLabel, targetConceptLabel);
  validateNewlyCreatedRelationship(
    CONSTANTS.FIRST_RELATIONSHIP_LABEL,
    [targetConceptLabel],
    forceGalileiSapAutomationUpdate
  );
};

export const createRelationshipFromDiagram = (baseConceptLabel: string, targetConceptLabel: string) => {
  performActionOnDiagramNode("click", targetConceptLabel);
  cy.get(DIAGRAM_CONTEXT_PAD.ENTITY_LINK_SYMBOL).should("exist");
  cy.window().then((win) => {
    const editor = win.sap.ui
      .getCore()
      .byId(
        "shellMainContent---graphbuilderComponent---ConceptualModel--GalileiDiagramView--galileiDiagramEditorControl"
      )
      .getInnerEditor();
    const sourceSymbol = editor.getAllSymbols().find((symbol) => symbol.object.displayName === targetConceptLabel);
    const targetSymbol = editor.getAllSymbols().find((symbol) => symbol.object.displayName === baseConceptLabel);
    const oTool = editor.selectTool("sap.modeling.ontology.ui.RelationshipLinkSymbol", false);
    oTool.isContextButton = true;
    const button = win.document.querySelector(DIAGRAM_CONTEXT_PAD.ENTITY_LINK_SYMBOL);
    const btnRect = button.getBoundingClientRect();
    const targetSymbolPosition = targetSymbol.shape.svgNode.getBoundingClientRect();
    const evt = new MouseEvent("mousedown", {
      bubbles: true,
      cancelable: true,
      clientX: btnRect.x + btnRect.width / 2,
      clientY: btnRect.y + btnRect.height / 2,
    }) as any;
    evt.contextButtonSymbol = sourceSymbol;
    button.dispatchEvent(evt);
    oTool.onPointerDown(evt);
    oTool.onDragStart(
      new MouseEvent("mousemove", {
        bubbles: true,
        cancelable: true,
        clientX: btnRect.x,
        clientY: btnRect.y,
      })
    );
    oTool.onDrag(
      new MouseEvent("mousemove", {
        bubbles: true,
        cancelable: true,
        clientX: targetSymbolPosition.x + targetSymbolPosition.width / 2,
        clientY: targetSymbolPosition.y + targetSymbolPosition.height / 2,
      })
    );
    oTool.onDragEnd(
      new MouseEvent("mouseup", {
        bubbles: true,
        cancelable: true,
        clientX: targetSymbolPosition.x + targetSymbolPosition.width / 2,
        clientY: targetSymbolPosition.y + targetSymbolPosition.height / 2,
      })
    );
  });
};

export const changeTargetConceptOfRelationshipOnDiagram = (
  linkSymbolLabel: string,
  targetConceptLabel: string,
  oldTargetConceptLabel: string,
  index: number = 0
) => {
  cy.get(".sapGalileiHandle").should("exist");
  cy.window().then((win) => {
    const editor = win.sap.ui
      .getCore()
      .byId(
        "shellMainContent---graphbuilderComponent---ConceptualModel--GalileiDiagramView--galileiDiagramEditorControl"
      )
      .getInnerEditor();
    const linkSymbol = editor.getAllSymbols().find((symbol) => symbol.object.displayName === linkSymbolLabel);
    const oldTargetSymbol = editor
      .getAllSymbols()
      .find((symbol) => symbol.object.displayName === oldTargetConceptLabel);
    const newTargetSymbol = editor.getAllSymbols().find((symbol) => symbol.object.displayName === targetConceptLabel);

    if (index === 1) {
      (win as any).sap.galilei.core.Event.publish(GalileiCustomEvents.ON_CHANGE_TARGET_CONCEPT, this, undefined, {
        oldConcept: oldTargetSymbol,
        newConcept: newTargetSymbol,
        linkSymbol: linkSymbol,
      });
    } else {
      (win as any).sap.galilei.core.Event.publish(GalileiCustomEvents.ON_CHANGE_SOURCE_CONCEPT, this, undefined, {
        oldConcept: oldTargetSymbol,
        newConcept: newTargetSymbol,
        linkSymbol: linkSymbol,
      });
    }
  });
};

export const createConcept = () => {
  getWorkingNode(0).find('button[title*="cm_add"]').click();
  cy.get(".sapMMenu")
    .invoke("show")
    .then(() => {
      clickOnMenuItem("cm_create_concept");
    });
  ensureThereAreNoBusyStates();
};

export const createSubConcept = (conceptNode: string | number) => {
  getWorkingNode(conceptNode).find("button[title*='cm_add_to_concept']").click();
  cy.get(".sapMMenu")
    .invoke("show")
    .then(() => {
      clickOnMenuItem("cm_sub_concept");
    });
};

export const removeResource = (node: string | number, removedClass: string = ".propertyMenu.sapMMenu") => {
  getWorkingNode(node).contains("cm_more", original).closest(".sapMBtn").click({ force: true });
  clickOnMenuItem("cm_remove_resource", removedClass);
};

export const removeMultiplePropertyOption = (
  propertyNode: string | number,
  option: string = "cm_remove_resource_from_concept"
) => {
  getWorkingNode(propertyNode).contains("cm_more", original).closest(".sapMBtn").click();
  cy.get(".reusedPropertyMenu").contains(option, original).closest("li").click({ force: true });
};

export const deleteResource = (resourceNode: string | number) => {
  getWorkingNode(resourceNode).contains("cm_more", original).closest(".sapMBtn").click({ force: true });
  clickOnMenuItem("cm_delete_resource");
};

export const deleteProperty = (resourceNode: string | number) => {
  getWorkingNode(resourceNode).contains("cm_more", original).closest(".sapMBtn").click({ force: true });
  clickOnMenuItem("cm_delete_resource", ".propertyMenu.sapMMenu");
};

export const addSourceNodeAsReference = (sourceNode: string | number) => {
  getSourceNode(sourceNode).find("button[title*='cm_add']").click({ force: true });
};

export const typeOnInput = (inputId: string, valuesToType: string) => {
  cy.get(DETAILS_BASE.LABEL).click();
  cy.get(`${inputId}`).closest("input").type(`{moveToEnd}${valuesToType}`);
  waitDebounce(); // since most of our inputs have setTimeout debounce techniques in place
};

export const typeOnLabel = (valuesToType: string) => {
  typeOnInput(DETAILS_BASE.LABEL_INNER, valuesToType);
};

export const typeOnUri = (valuesToType: string) => {
  typeOnInput(DETAILS_BASE.URI_INNER, valuesToType);
};

export const typeOnEditDialogUri = (valuesToType: string) => {
  cy.get(EDITURI_DIALOG.INPUT_URI).type("{moveToEnd}").type(valuesToType);
  waitDebounce();
};

export const typeAndSaveEditUri = (valuesToType: string, isOntologyLevel: boolean = false) => {
  openEditUriDialog(isOntologyLevel);
  typeOnEditDialogUri(valuesToType);
  saveAndCloseEditUriDialog();
};

export const typeAndCancelEditUri = (valuesToType: string, isOntologyLevel: boolean = false) => {
  openEditUriDialog(isOntologyLevel);
  typeOnEditDialogUri(valuesToType);
  cancelAndCloseEditUriDialog();
};
export const typeOnDescription = (valuesToType: string) => {
  cy.get(`${DETAILS_BASE.DESCRIPTION}`).type(valuesToType);
};

export const confirmDeletionOfConceptAndProperties = () => {
  cy.get(".deleteConceptsAndPropertiesWarning").contains("cm_delete_properties_and_concept", original).click();
};

export const confirmDeletionOfConceptOnly = () => {
  cy.get(".deleteConceptsAndPropertiesWarning").contains("cm_delete_concept", original).click();
};

// note: we use ui5 apis because cypress does not have any reliable way to simulate paste on a textarea
// the returned type of this object can be cast to leverage type definitions after using it, e.g:
// getUi5(id) as sap.m.Label
export const getUi5 = (controlId: string, removeFirstCharOnId = true): any => {
  cy.get(controlId, original).should("exist");
  return cy
    .window()
    .its("sap")
    .then((sap) =>
      sap.ui.getCore().byId(removeFirstCharOnId ? controlId.substring(1) /* remove # from string*/ : controlId)
    );
};

export const pasteOnDescription = (textToPaste: string) => {
  getUi5(DETAILS_BASE.DESCRIPTION).then((textArea: sap.m.TextArea) => {
    textArea.setValue(textToPaste);
    textArea.fireChange();
    textArea.fireLiveChange();
  });
};

// I don't love using ui5 here instead of plain cypress but I did not manage to do this with just cypress
export const pasteOnLabel = (textToPaste: string) => {
  getUi5(DETAILS_BASE.LABEL).then((input: sap.m.Input) => {
    input.setValue(textToPaste);
    input.fireChange();
    input.fireLiveChange();
  });
};

export const pasteOnUri = (textToPaste: string) => {
  getUi5(DETAILS_BASE.URI).then((input: sap.m.Input) => {
    input.setValue(textToPaste);
  });
};

export const checkCursorPositionOnInput = (inputId: string, position: number) => {
  getUi5(inputId).then((input: sap.m.Input) => {
    cy.wrap((input.getFocusDomRef() as any).selectionStart).should("be.eq", position);
  });
};

export const inputValueStateIs = (inputId: string, expectedValue: string) => {
  assertOnUI5ComponentFunctionValue(inputId, "getValueState", (value) => {
    expect(value).to.equal(expectedValue);
  });
};

export const inputValueStateTextIs = (inputId: string, expectedValue: string) => {
  assertOnUI5ComponentFunctionValue(inputId, "getValueStateText", (value) => {
    expect(value).to.equal(expectedValue);
  });
};

export const textValueTextFieldEquals = (textFieldId: string, expectedValue: string) => {
  assertOnUI5ComponentFunctionValue(textFieldId, "getText", (value) => {
    expect(value).to.equal(expectedValue);
  });
};

export const textValueTextFieldMatch = (textFieldId: string, expectedValue: string) => {
  assertOnUI5ComponentFunctionValue(textFieldId, "getText", (value) => {
    const exp = new RegExp(expectedValue);
    expect(value).to.match(exp);
  });
};

export const descriptionInitialRowsQuantityIs = (expectedQuantity: number) => {
  assertOnUI5ComponentFunctionValue(DETAILS_BASE.DESCRIPTION, "getRows", (initialRows) => {
    expect(initialRows).to.equal(expectedQuantity);
  });
};

export const descriptionMaxRowsQuantityIs = (expectedQuantity: number) => {
  assertOnUI5ComponentFunctionValue(DETAILS_BASE.DESCRIPTION, "getGrowingMaxLines", (maxRows) => {
    expect(maxRows).to.equal(expectedQuantity);
  });
};

export const toggleTargetConcept = (targetRangeLabeL: string) => {
  cy.get(PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER).should("be.visible");
  cy.get(PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT_INNER).click();
  cy.get(PROPERTY_DETAILS.TARGET_CONCEPTS_LIST).contains(targetRangeLabeL, original).click();
  // make sure the new token is now set on the input
  cy.get(`${PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT}-content .sapMToken:last`).should(
    "have.text",
    targetRangeLabeL
  );
};

export const removeToken = (inputId: string, targetRangeLabeL: string) => {
  cy.get(inputId).should("be.visible");
  cy.get(`${inputId}-content .sapMToken`).contains(targetRangeLabeL, original).closest(".sapMToken").click();
  cy.get(`${inputId}-content .sapMTokenSelected`).find(".sapMTokenIcon").click();
};

export const toggleMultiValue = () => {
  cy.get(PROPERTY_DETAILS.MULTI_VALUE_SWITCH_BUTTON).click();
};

export const toggleRequired = () => {
  cy.get(PROPERTY_DETAILS.REQUIRED_SWITCH_BUTTON).click();
};

export const selectLandingTableEntity = (entityName: string) => {
  cy.get(LANDING_PAGE.TABLE).contains(entityName, original).closest("tr").click();
};

export function selectTableEntity(tableId: string, entityName: string) {
  cy.get(tableId)
    .contains(entityName, original)
    .closest("tr")
    .then(($tr) => {
      getUi5("#" + $tr.get(0).id).then((ui5TableRow: sap.ui.table.Row) => {
        ui5TableRow.setSelected(true);
        ui5TableRow.getTable().fireSelectionChange();
      });
    });
}

export const checkColumnHeaders = (headerSelector: string, headers: string[]) => {
  cy.get(headerSelector + " tr td").should("have.length", headers.length);
  headers.forEach((header, index) => {
    cy.get(headerSelector + " tr td")
      .eq(index)
      .should("have.text", header);
  });
};

export const checkRowData = (rowSelector: string, rowData: string[]) => {
  rowData.forEach((data, index) => {
    cy.get(rowSelector, original).find("td").eq(index).should("contain.text", data);
  });
};

export const navBackOnShell = () => {
  cy.get(".sapHcsShellHeader button[title='AppShell_BackTooltip']", original).click();
};

export const clickOnShellSpaceBreadcrumb = () => {
  cy.get(".sapHcsShellHeader .sapHcsShellNavBreadcrumbsSpace", original).click();
};

export const clickOnCloseButtonShellSpaceBreadcrumb = () => {
  cy.get(".sapHcsShellHeader button[title='AppShell_CloseInstanceTooltip']", original).click();
};

export const clickOnHomePageMenu = () => {
  cy.get('.sapHcsShellNavigationStatic li[title="menu_home"]', original).click();
  cy.get("[id=welcomeCardOuterCard]").should("be.visible");
};

export const clickOnGraphBuilderMenu = () => {
  cy.get('.sapHcsShellSideNavigationFlexible li[title="menu_graphbuilder"]', original).click();
};
export const clickOnRepositoryExplorerMenu = () => {
  cy.get('.sapHcsShellSideNavigationFlexible li[title="menu_repositoryexplorer"]', original).click();
};
// visual checks

// value 'true' if switch is Yes/On, 'false' if Off/No
const switchStateIs = (value: boolean, controlId: string) => {
  cy.get(controlId).invoke("attr", "aria-checked").should("be.eq", String(value));
};

export const tooltipIs = (value: string, controlId: string) => {
  cy.get(controlId).should("have.attr", "aria-label", value);
};

export const multiValueSwitchIs = (value: boolean) => {
  switchStateIs(value, PROPERTY_DETAILS.MULTI_VALUE_SWITCH_BUTTON);
  tooltipIs("cm_multi_value_property_see_help_information", PROPERTY_DETAILS.MULTI_VALUE_INFO_BUTTON);
};

export const isRequiredSwitchIs = (value: boolean) => {
  switchStateIs(value, PROPERTY_DETAILS.REQUIRED_SWITCH_BUTTON);
  tooltipIs("cm_required_property_see_help_information", PROPERTY_DETAILS.REQUIRED_INFO_BUTTON);
};

export const diagramSelectedRelationshipSourceAndTargetAre = (sourceLabel: string, targetLabel: string) => {
  getUi5(DIAGRAM.INNER_EDITOR)
    .then((element) => element.getInnerEditor().selectedSymbols?.get(0))
    .then((selectedSymbol) => {
      cy.wrap(selectedSymbol?.sourceSymbol.object.displayName).should("eq", sourceLabel);
      cy.wrap(selectedSymbol?.targetSymbol.object.displayName).should("eq", targetLabel);
    });
};

export const diagramRelationshipExists = ({ relationshipLabel, sourceNodeLabel, targetNodeLabel }) => {
  // Wrap the entire logic inside cy.wrap for retryability
  getUi5(DIAGRAM.INNER_EDITOR).then((element) => {
    cy.wrap({
      getMatchingSymbol: () =>
        element
          .getInnerEditor()
          .getAllSymbols()
          .filter(
            (symbol) =>
              symbol.object.displayName === relationshipLabel &&
              symbol.sourceSymbol.object.displayName === sourceNodeLabel &&
              symbol.targetSymbol.object.displayName === targetNodeLabel
          ),
    })
      .invoke("getMatchingSymbol")
      .its("length")
      .should("be.at.least", 1);
  });
};

export const selectDiagramRelationshipWithSourceAndTargetInfo = (
  relationshipLabel: string,
  sourceLabel: string,
  targetLabel: string
) => {
  getUi5(DIAGRAM.INNER_EDITOR)
    .then((element) =>
      element
        .getInnerEditor()
        .getAllSymbols()
        .find(
          (symbol) =>
            symbol.object.displayName === relationshipLabel &&
            symbol.sourceSymbol.object.displayName === sourceLabel &&
            symbol.targetSymbol.object.displayName === targetLabel
        )
    )
    // eslint-disable-next-line no-underscore-dangle
    .then((element) => cy.get(`#${element.shape._id}`).click({ force: true })); // force due to helper invisible element that the diagram uses
};

export const diagramConnectionsQuantityIs = (targetLabel: string, expectedQuantity: number) => {
  let count = 0;
  getDiagram()
    .find("text")
    .each(($el) => {
      if ($el.text() === targetLabel) {
        count++;
      }
    })
    .then(() => {
      expect(count).to.equal(expectedQuantity);
    });
};

export const selectedTargetConceptsQuantityIs = (expectedQuantity: number) => {
  if (!expectedQuantity) {
    cy.get(PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT + " .sapMToken", original).should("not.exist");
  } else {
    cy.get(PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT + " .sapMToken", original).should(
      "have.length",
      expectedQuantity
    );
  }
};

export const targetConceptsValueStateWarningIs = (typeOfWarning: string) => {
  if (typeOfWarning === "none") {
    cy.get(`${PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT}-message-sr`).should("not.exist");
  } else {
    if (typeOfWarning === "multipleTargetConcepts") {
      cy.get(`${PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT}-message-sr`).should(
        "have.text",
        "INPUTBASE_VALUE_STATE_WARNING cm_multiple_target_concepts_warning"
      );
    }
    if (typeOfWarning === "noTargetConcept") {
      cy.get(`${PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT}-message-sr`).should(
        "have.text",
        "INPUTBASE_VALUE_STATE_WARNING cm_no_target_concept_defined_warning"
      );
    }
  }
};

export const checkMultiInputListOccurrence = (
  cancelButtonId: string,
  multiInputList: string,
  multiInputId: string,
  listItems: string[],
  expectedElementsCount: number
) => {
  cy.get(multiInputId).click();
  let listElementsCount = 0;
  listItems.forEach((listItemLabel) => {
    cy.get(multiInputList)
      .find("li")
      .each((listElement) => {
        if (listElement.text() === listItemLabel) {
          listElementsCount += 1;
        }
      })
      .then(() => {
        expect(listElementsCount).to.be.equal(expectedElementsCount);
        listElementsCount = 0;
      });
  });
  cy.get(cancelButtonId).click();
};

export const detailsHeaderTitleIs = (targetText: string) => {
  cy.get(DETAILS_BASE.ENTITY_NAME).should("have.text", targetText);
};

export const headerTextContains = (targetText: string) => {
  cy.get(DETAILS_BASE.HEADER_TEXT).contains(targetText, original);
};

export const inputValueIs = (inputId: string, targetText: string) => {
  assertOnUI5ComponentFunctionValue(inputId, "getValue", (value) => {
    expect(value).to.equal(targetText);
  });
};

export const detailsLabelInputIs = (targetText: string) => {
  inputValueIs(DETAILS_BASE.LABEL, targetText);
};

export const detailsUriInputIs = (targetText: string) => {
  assertOnUI5ComponentFunctionValue(DETAILS_BASE.URI, "getValue", (value) => {
    expect(value).to.equal(targetText);
  });
};

export const editUriDialogInputIs = (targetText: string) => {
  assertOnUI5ComponentFunctionValue(EDITURI_DIALOG.INPUT_URI, "getValue", (value) => {
    expect(value).to.equal(targetText);
  });
};

export const extractAndRoundTranslatePosition = (str: string): { x: number; y: number } => {
  // extract the numbers from the string
  const matches = str.match(/translate\(([^,]+),([^)]+)\)/);
  if (!matches) {
    throw new Error("Invalid format");
  }
  return { x: Math.floor(parseFloat(matches[1])), y: Math.floor(parseFloat(matches[2])) };
};

export const detailsUriInputContains = (targetText: string) => {
  cy.get(DETAILS_BASE.URI).contains(targetText);
};

export const checkEditUriDialogHasUri = (targetText: string, isOntologyLevel: boolean = false) => {
  openEditUriDialog(isOntologyLevel);
  editUriDialogInputIs(targetText);
  cancelAndCloseEditUriDialog();
};

export const uriValueStateIs = (value: boolean) => {
  getUi5(DETAILS_BASE.URI).then((input: sap.m.Input) => {
    const targetValueState = value ? "None" : "Error";
    cy.wrap({ getValueState: () => input.getValueState() })
      .invoke("getValueState")
      .should("be.equal", targetValueState);
  });
};

export const detailsDescriptionInputIs = (targetText: string) => {
  if (targetText.length) {
    cy.get(DETAILS_BASE.DESCRIPTION_INNER).contains(targetText, original);
  } else {
    cy.get(DETAILS_BASE.DESCRIPTION_INNER).should("have.html", "&nbsp;");
  }
};

export const checkImportedOntologiesListLabel = (label: string, index: number) => {
  cy.get(ONTOLOGY_DETAILS.IMPORTED_ONTOLOGIES_LIST)
    .find("li")
    .eq(index)
    .find(".sapMTextLineClamp")
    .should("have.text", label);

  // check label style
  cy.get(ONTOLOGY_DETAILS.IMPORTED_ONTOLOGIES_LIST)
    .find("li")
    .eq(index)
    .find("div[style='order: 0; flex: 0 1 auto; min-height: auto; min-width: auto;']");
  // check max 4 lines
  cy.get(ONTOLOGY_DETAILS.IMPORTED_ONTOLOGIES_LIST).find("li").eq(index).find("span[style='-webkit-line-clamp: 4;']");
};

export const checkImportedOntologiesListDoesNotHaveLabel = (label: string, index: number) => {
  cy.get(ONTOLOGY_DETAILS.IMPORTED_ONTOLOGIES_LIST).find("li").should("not.have.text", label);
};

export const checkImportedOntologiesListLength = (count: number) => {
  cy.get(ONTOLOGY_DETAILS.IMPORTED_ONTOLOGIES_LIST).find("li").should("have.length", count);
};

export const checkImportedOntologiesListIconColor = (iconColor: string, index: number) => {
  cy.get(ONTOLOGY_DETAILS.IMPORTED_ONTOLOGIES_LIST)
    .find("li")
    .eq(index)
    .find(`span[style='font-family: FPA-icons-own; font-size: 1rem; color: ${iconColor};']`);
};

export const checkImportedOntologiesListButton = (buttonType: string, buttonTooltip: string, index: number) => {
  cy.get(ONTOLOGY_DETAILS.IMPORTED_ONTOLOGIES_LIST).find("li").eq(index).find(`button[aria-label='${buttonTooltip}']`);
  cy.get(ONTOLOGY_DETAILS.IMPORTED_ONTOLOGIES_LIST).find("li").eq(index).find(`.sapMBtn${buttonType}`);
};

export const headerIconIs = (targetIconName: string) => {
  cy.get(`${DETAILS_BASE.HEADER_ICON}`, original).then((result: jQuery) => checkIcon(result, targetIconName));
};

export const workingTreeIconIs = (targetIconName: string, targetConcept: string | number) => {
  getWorkingNode(targetConcept)
    .find(".resourceIcon")
    .then((result: jQuery) => checkIcon(result, targetIconName));
};

export const sourceNodeIconIs = (targetIconName: string, targetConcept: string | number) => {
  getSourceNode(targetConcept)
    .find(".resourceIcon")
    .then((result: jQuery) => checkIcon(result, targetIconName));
};

export const selectedTokenIs = (tokenLabels: string[], inputId: string) => {
  if (tokenLabels.length) {
    let tokensIndex = 0;
    cy.get(`${inputId} .sapMToken`).each((token) => {
      expect(token.text() === tokenLabels[tokensIndex]);
      tokensIndex += 1;
    });
  } else {
    expect(cy.get(`${inputId} .sapMToken`).should("have.length", 0));
  }
};

export const workingTreeNodeIsSelected = (nodeIdentifier: string) => {
  getNode(nodeIdentifier, "working").get(".sapMLIBSelected", original).should("exist");
};

export const diagramNodeIsSelected = (nodeLabel: string, _expectedSelectedNodes = 1) =>
  getDiagramNode(nodeLabel).should("have.class", "sapGalileiSelected");

export const diagramNodeIsNotSelected = (nodeLabel: string) =>
  getDiagramNode(nodeLabel).should("not.have.class", "sapGalileiSelected");

export const diagramLinkIsSelected = (label: string, _expectedSelectedLinks = 1) => {
  getDiagramRelationshipLink(label).should("have.class", "sapGalileiSelected");
};

export const diagramLinkIsNotSelected = (label: string) =>
  getDiagramRelationshipLink(label).should("not.have.class", "sapGalileiSelected");

export const assertOnUI5ComponentFunctionValue = (id: string, componentFn: string, assertionFn: (value: any) => void) =>
  getUi5(id).then((object) => {
    cy.wrap({
      fn: () => object[componentFn](),
    })
      .invoke("fn")
      .should(assertionFn);
  });

export const workingTreeNodeLabelIs = (nodeIndex: number, label: string) => {
  cy.get(`${TREE_PANEL.WORKING_ONTOLOGY_TREE} .conceptualModelTreeNodeFormattedLabel`, original)
    .eq(nodeIndex)
    .then(($el) => {
      const textContent = $el.text().trim();
      if (label.length) {
        expect(textContent).to.be.equal(label);
      } else {
        expect(textContent).to.be("empty");
      }
    });
};

export const workingTreeVisibleLabelsQuantity = (targetLabel: string, quantity: number) => {
  if (quantity > 0) {
    getWorkingTree()
      .find(".conceptualModelTreeNodeFormattedLabel")
      .filter((index, element) => {
        const textContent = Cypress.$(element).text().trim();
        return textContent === targetLabel;
      })
      .should("have.length", quantity);
  } else {
    getWorkingTree().should("not.contain.text", targetLabel);
  }
};
export const labelWarningStateCheck = (hasWarningState: boolean) => {
  if (hasWarningState) {
    return cy.get(`${DETAILS_BASE.LABEL} .sapMInputBaseContentWrapperWarning`).should("exist");
  }
  return cy.get(`${DETAILS_BASE.LABEL} .sapMInputBaseContentWrapperWarning`).should("not.exist");
};

export const validateNewlyCreatedResource = (resourceLabel: string, _isVisibleInDiagram = true) => {
  detailsLabelInputIs(resourceLabel);
  getWorkingNode(resourceLabel).should("exist");
  workingTreeNodeIsSelected(resourceLabel);
};

export const validateNewlyCreatedConcept = (conceptLabel: string) => {
  validateNewlyCreatedResource(conceptLabel);
  getDiagramNode(conceptLabel).should("exist");
  headerIconIs(CONSTANTS.ICONS.CONCEPT);
  workingTreeIconIs(CONSTANTS.ICONS.CONCEPT, conceptLabel);
  headerTextContains(CONSTANTS.HEADERS.CONCEPT);
};

export const validateNewlyCreatedSubConcept = (conceptLabel: string) => {
  validateNewlyCreatedResource(conceptLabel);
  getDiagramNode(conceptLabel).should("exist");
  headerIconIs(CONSTANTS.ICONS.SUB_CONCEPT);
  workingTreeIconIs(CONSTANTS.ICONS.SUB_CONCEPT, conceptLabel);
  headerTextContains(CONSTANTS.HEADERS.SUB_CONCEPT);
};

export const validateNewlyCreatedProperty = (propertyLabel: string) => {
  validateNewlyCreatedResource(propertyLabel, false);
  multiValueSwitchIs(true);
  isRequiredSwitchIs(false);
  headerTextContains(CONSTANTS.HEADERS.PROPERTY);
  getDiagram().should("not.contain", propertyLabel);
};

export const validateNewlyCreatedNumberProperty = (propertyLabel: string) => {
  validateNewlyCreatedProperty(propertyLabel);
  headerIconIs(CONSTANTS.ICONS.PROPERTY_NUMBER);
  workingTreeIconIs(CONSTANTS.ICONS.PROPERTY_NUMBER, propertyLabel);
};

export const validateNewlyCreatedStringProperty = (propertyLabel: string) => {
  validateNewlyCreatedProperty(propertyLabel);
  headerIconIs(CONSTANTS.ICONS.PROPERTY_STRING);
  workingTreeIconIs(CONSTANTS.ICONS.PROPERTY_STRING, propertyLabel);
};

export const validateNewlyCreatedRelationship = (
  relationshipLabel: string,
  selectedTargetConcepts = [],
  forceGalileiSapAutomationUpdate = false
) => {
  if (forceGalileiSapAutomationUpdate) {
    forceSapAutomationUpdate(relationshipLabel);
  }
  workingTreeIconIs(CONSTANTS.ICONS.RELATIONSHIP, relationshipLabel);
  headerIconIs(CONSTANTS.ICONS.RELATIONSHIP);
  headerTextContains(CONSTANTS.HEADERS.RELATIONSHIP);
  selectedTokenIs(selectedTargetConcepts, PROPERTY_DETAILS.TARGET_CONCEPTS_MULTI_INPUT);
  validateNewlyCreatedResource(relationshipLabel, false);
  multiValueSwitchIs(true);
  isRequiredSwitchIs(false);
  if (selectedTargetConcepts.length) {
    diagramLinkIsSelected(relationshipLabel);
  }
};

const forceSapAutomationUpdate = (currentRelationshipLabel: string) => {
  // workaround on cypress to ensure the galilei sap-automation attribute gets updated
  // note: this issue is not replicable on the true UI
  waitDebounce();
  detailsLabelInputIs(currentRelationshipLabel);
  typeOnLabel("{moveToEnd}");
  typeOnLabel("t");
  detailsLabelInputIs(currentRelationshipLabel + "t");
  cy.get(DETAILS_BASE.DESCRIPTION).click(); // change focus to fire the change event
  typeOnLabel("{moveToEnd}{backspace}");
  detailsLabelInputIs(currentRelationshipLabel);
  // end of workaround
};

export const validateNewlyAddedReference = (referenceLabel: string) => {
  workingTreeIconIs(CONSTANTS.ICONS.CONCEPT, referenceLabel);
  headerIconIs(CONSTANTS.ICONS.CONCEPT);
  headerTextContains(CONSTANTS.HEADERS.CONCEPT);
  validateNewlyCreatedResource(referenceLabel);
  detailsLabelInputIs(referenceLabel);
  getWorkingNode(referenceLabel).should("exist");
};

export const validateBeforeResourceRemoval = (resourceLabel: string) => {
  getWorkingTree().should("contain.text", resourceLabel);
  getWorkingNode(resourceLabel).click();
  workingTreeNodeIsSelected(resourceLabel);
  detailsLabelInputIs(resourceLabel);
};

export const validateBeforeConceptRemoval = (conceptLabel: string) => {
  validateBeforeResourceRemoval(conceptLabel);
  getDiagramNode(conceptLabel).should("exist");
};

export const validateBeforePropertyRemoval = (propertyLabel: string) => {
  validateBeforeResourceRemoval(propertyLabel);
};

export const validateAfterResourceRemoval = (deletedResourceLabel: string, parentNodeLabel: string) => {
  detailsLabelInputIs(parentNodeLabel);
  getWorkingTree().should("not.contain.text", deletedResourceLabel);
  getDiagram().should("not.contain.text", deletedResourceLabel);
  workingTreeNodeIsSelected(parentNodeLabel);
};

export const validateConceptRemoval = (deletedConceptLabel: string) => {
  getWorkingTree().should("not.contain.text", deletedConceptLabel);
  getDiagram().should("not.contain.text", deletedConceptLabel);
};

// properties and relationship range
export const validateMultiInputRangeLength = (rangeId: string, rangeLength: number) => {
  cy.get(rangeId).find(".sapMToken").should("have.lengthOf", rangeLength);
};

export const validateMultiInputRangeLabel = (rangeId: string, itemLabel: string, itemIndex: number) => {
  cy.get(rangeId).find(".sapMToken").eq(itemIndex).find(".sapMTokenText").should("have.text", itemLabel);
};

export const validateMultiComboBoxWarning = (rangePopupId: string, warningMessage: string, warningType: string) => {
  cy.get(rangePopupId)
    .find(`.sapMValueStateHeader${warningType}`)
    .should("be.visible")
    .contains(warningMessage, original);
};

export const validateWarningMessage = (warningTitle: string, warningMessage: string) => {
  cy.get(`.sapMMessageBoxWarning`).should("exist");
  cy.get(`.sapMMessageBoxWarning`).find(`.sapMDialogTitle`).should("be.visible").should("have.text", warningTitle);
  cy.get(`.sapMMessageBoxWarning`).find(`.sapMMsgBoxText`).should("be.visible").contains(warningMessage, original);
};

export const validateErrorMessage = (errorMessage: string) => {
  cy.get(`.sapMMessageBoxError`).should("exist");
  cy.get(`.sapMMessageBoxError`).find(`.sapMMsgBoxText`).should("be.visible").contains(errorMessage, original);
};

export const validateMessageToast = (messageText: string) => {
  cy.get(`.sapMMessageToast`).then(($input) => {
    cy.wrap($input).should("exist");
    cy.wrap($input).contains(messageText, original);
  });
};

export const validateListItemIcon = (listId: string, listItemLabel: string, expectedIcon: string) => {
  cy.get(listId)
    .contains(listItemLabel, original)
    .closest("li")
    .then(($el) => {
      const id = $el.get(0).id;
      assertOnUI5ComponentFunctionValue("#" + id, "getIcon", (value) => {
        expect(value).to.equal(expectedIcon);
      });
    });
};

export const selectPropertyTargetDatatype = (elementIndex: number) => {
  cy.get(PROPERTY_DETAILS.DATA_TYPES_RANGE).click();
  cy.get(PROPERTY_DETAILS.DATA_TYPES_RANGE_POPUP_LIST).find("li").eq(elementIndex).click();
};

export const selectDiagramNode = (nodeLabel: string) => {
  cy.get(`${WORKBENCH.DIAGRAM} [sap-automation='Entity/${nodeLabel}']`, original).first().click();
  getDiagramNode(nodeLabel).click();
  getDiagramNode(nodeLabel).first().should("have.class", "sapGalileiSelected");
};

export const selectDiagramRelationship = (nodeLabel: string) => {
  // force is required because entity links are covered by a helper element
  getDiagramRelationshipLink(nodeLabel).first().click({ force: true });
  getDiagramRelationshipLink(nodeLabel).first().should("have.class", "sapGalileiSelected");
};

export const selectDiagramSubconceptLink = (nodeLabel: string) => {
  // force is required because entity links are covered by a helper element
  getDiagramSubconceptLink(nodeLabel).first().click({ force: true });
  getDiagramSubconceptLink(nodeLabel).first().should("have.class", "sapGalileiSelected");
};

export const deleteSelectedRangeOptionByToken = (rangeId: string, rangeLabel: string) => {
  cy.get(rangeId).find(".sapMToken").should("have.text", rangeLabel).find("span[role='presentation']").click();
};

export const deleteParentConceptToken = (tokenIndex: number) => {
  cy.get(`${CONCEPT_DETAILS.PARENT_CONCEPTS_MULTI_INPUT} .sapUiIconTitle`).eq(tokenIndex).click();
};

// detached properties dialog
export const selectConceptFromDetachedPropertiesDialog = (
  conceptIndex: number,
  conceptLabel: string,
  detachedPropertyDialogListPanel: string
) => {
  cy.get(detachedPropertyDialogListPanel)
    .find("li")
    .eq(conceptIndex)
    .find(".sapMSLITitleOnly")
    .should("have.text", conceptLabel)
    .click();
};

export const openAddDetachedPropertyDialogViaTree = (propertyNode: string) => {
  getWorkingNode(propertyNode).click();
  getWorkingNode(propertyNode).contains("cm_more", original).closest(".sapMBtn").click();
  clickOnMenuItem("cm_add_to_concept");
  cy.get(".sapMDialog").should("be.visible");
};

export const openAddDetachedPropertyDialogViaDetailsPanel = (propertyNode: string) => {
  getWorkingNode(propertyNode).click();
  cy.get(DETAILS_BASE.OVERFLOW_BUTTON).click();
  clickOnMenuItem("cm_add_to_concept");
  cy.get(".sapMDialog").should("be.visible");
};

// existing properties dialog
export const selectExistingProperty = (propertyIndex: number, propertyLabel: string) => {
  cy.get(TREE_PANEL.EXISTING_PROPERTIES_DIALOG_LIST)
    .find("li")
    .eq(propertyIndex)
    .find(".sapMSLITitleOnly")
    .should("have.text", propertyLabel)
    .click();
};

export const checkRepeatedElementsInWorkingTree = (propertyLabel: string, expectedPropertyCount: number) => {
  let propertyCount = 0;
  cy.get(TREE_PANEL.SELF)
    .find("li")
    .find(".conceptualModelTreeNodeFormattedLabel")
    .each((treeElement) => {
      if (treeElement.text() === propertyLabel) {
        propertyCount += 1;
      }
    })
    .then(() => {
      expect(propertyCount).to.be.equal(expectedPropertyCount);
    });
};

export const selectAllExistingProperties = (listLength: number) => {
  let listIndex = 0;
  while (listIndex < listLength) {
    cy.get(TREE_PANEL.EXISTING_PROPERTIES_DIALOG_LIST).find("li").find(".sapMCb.sapMLIBSelectM").eq(listIndex).click();
    listIndex += 1;
  }
};

export const openExistingPropertiesDialogFromFocusedConcept = () => {
  clickOnConceptAddButton("Address");
  clickOnMenuItem("cm_property");
  isMenuItem("cm_existing_property");
  clickOnMenuItem("cm_existing_property");
  cy.get(TREE_PANEL.EXISTING_PROPERTIES_DIALOG).should("be.visible");
  cy.get(TREE_PANEL.EXISTING_PROPERTIES_DIALOG_TITLE).should("have.text", "cm_reuse_properties_dialog_title(Address)");
};

export const addExistingPropertyToConcept = () => {
  openExistingPropertiesDialogFromFocusedConcept();
  selectExistingProperty(0, "Assisting Service");
  cy.get(TREE_PANEL.EXISTING_PROPERTIES_DIALOG_CONFIRMATION_BUTTON).click();
  getWorkingNode("Assisting Service").should("exist");
  toggleWorkingNode("Animal");
  toggleWorkingNode("Dog");
  toggleWorkingNode("Service Dog");
  checkRepeatedElementsInWorkingTree("Assisting Service", 2);
};

export const getParentConceptsListRow = (rowNumber: number) =>
  cy.get(`${CONCEPT_DETAILS.PARENT_CONCEPTS_TABLE} li`).eq(rowNumber);

// dropdown menu methods

export const isMenuItem = (item: string) => {
  cy.get(".sapMMenu").contains(`${item}`, original).should("exist");
};

export const isNotMenuItem = (item: string) => {
  cy.get(".sapMMenu").contains(`${item}`, original).should("not.exist");
};

export const numberOfMenuItems = (menuIndex: number, numberOfItems: number) => {
  cy.get(".sapMMenu")
    .eq(menuIndex)
    .find('ul[role*="menu"]')
    .find('li[role*="menuitem"]')
    .should("have.length", numberOfItems);
};

export const clickOnMenuItem = (item: string, className: string = ".sapMMenu") => {
  cy.get(className, original).contains(item, original).closest("li").click({ force: true });
};

export const numberOfOpenMenus = (numberOfOpenMenus: number) => {
  cy.get(".sapMMenu").should("have.length", numberOfOpenMenus);
};

// TODO: This custom selector has been reapired in a makeshift way to allow for SAPUI5 ugrade to version 1.119.1.
// Background for current change: v1.119.1 no longer renders a SAP Icon available for accesiblity APIs, means a
// list menu icon does no longer have any "area-label" attribute to check for. Potentially, the other attributes like
// "ul[role*="menu"] and li[*role="menuitem"] may also be changed in future.
// Please note that traversing the DOM by dynamically rendered properties is a bad way to test SAPUI5 view elements.
// Please redesign your test with proper Cypress wrappers that get the actual SAPUI5 objects and do the checks on their
// properties.
export const validateMenuIconAndLabel = (menuIndex: number, menuItemIndex: number, label: string, icon: string) => {
  cy.get(".sapMMenu")
    .eq(menuIndex)
    .find('ul[role*="menu"]')
    .find('li[role*="menuitem"]')
    .eq(menuItemIndex)
    .then((result: jQuery) => {
      const id = result.attr("id");
      cy.byId(id, { force: true }).then((listItem: sap.ui.unified.MenuItem) => {
        cy.wrap({ getIconName: () => getIconName(listItem.getIcon()) })
          .invoke("getIconName")
          .should("eq", icon);
      });
    });

  cy.get(".sapMMenu")
    .eq(menuIndex)
    .find('ul[role*="menu"]')
    .find('li[role*="menuitem"]')
    .find('div[class="sapUiMnuItmTxt"]')
    .eq(menuItemIndex)
    .should("have.text", label);
};

export const validateMenuItemsEnabled = (menuSelector: string, areEnabled: boolean[]) => {
  let currentElement = 0;
  cy.get(`${menuSelector} .sapUiMnuItm`).each(($el) => {
    const ui5Element = (Cypress as any).state("window").sap.ui.getCore().byId($el.get(0).id);
    cy.wrap(ui5Element)
      .invoke("getEnabled")
      .should(areEnabled[currentElement] ? "be.true" : "be.false");
    currentElement++;
  });
};

export const shellCurrentEntityTextIs = (entityName: string) => {
  if (!entityName.length) {
    cy.get(`.sapHcsShellHeader span.sapHcsShellNavBreadcrumbsText`, original).should("not.exist");
  } else {
    cy.get(
      `.sapHcsShellHeader span.sapHcsShellNavBreadcrumbsText[title='cm_conceptual_model_editor_breadcrumbs(${entityName})']`,
      original
    ).should("exist");
  }
};

export const shellCurrentSpaceTextIs = (spaceName: string) => {
  cy.get(`.sapHcsShellHeader .sapHcsShellNavBreadcrumbsSpace[title='${spaceName}']`, original);
};

export const principalMenuList = [
  {
    label: "cm_string",
    icon: "text",
  },
  {
    label: "cm_integer",
    icon: "sac/number",
  },
  {
    label: "cm_decimal",
    icon: "sac/number-decimals",
  },
  {
    label: "cm_date",
    icon: "date-time",
  },
  {
    label: "cm_datetime",
    icon: "date-time",
  },
  {
    label: "cm_time",
    icon: "sac/time",
  },
  {
    label: "cm_float",
    icon: "sac/number-decimals",
  },
  {
    label: "cm_double",
    icon: "sac/number-decimals",
  },
];

export const otherMenuList1 = [
  {
    label: "cm_anysimpletype",
    icon: "sac/generic",
  },
  {
    label: "cm_anytype",
    icon: "sac/generic",
  },
  {
    label: "cm_anyuri",
    icon: "sac/text",
  },
  {
    label: "cm_base64binary",
    icon: "sac/binary",
  },
  {
    label: "cm_boolean",
    icon: "sac/boolean",
  },
  {
    label: "cm_byte",
    icon: "sac/binary",
  },
  {
    label: "cm_day",
    icon: "date-time",
  },
  {
    label: "cm_daytimeduration",
    icon: "fob-watch",
  },
  {
    label: "cm_hexbinary",
    icon: "sac/binary",
  },
  {
    label: "cm_int",
    icon: "sac/number",
  },
  {
    label: "cm_month",
    icon: "sac/calendar",
  },
  {
    label: "cm_monthday",
    icon: "sac/calendar",
  },
];

export const otherMenuList2 = [
  {
    label: "cm_negativeinteger",
    icon: "sac/number",
  },
  {
    label: "cm_nonnegativeinteger",
    icon: "sac/number",
  },
  {
    label: "cm_nonpositiveinteger",
    icon: "sac/number",
  },
  {
    label: "cm_password",
    icon: "sac/password",
  },
  {
    label: "cm_positiveinteger",
    icon: "sac/number",
  },
  {
    label: "cm_short",
    icon: "sac/number",
  },
  {
    label: "cm_unsignedbyte",
    icon: "sac/binary",
  },
  {
    label: "cm_unsignedint",
    icon: "sac/number",
  },
  {
    label: "cm_unsignedlong",
    icon: "sac/number",
  },
  {
    label: "cm_unsignedshort",
    icon: "sac/number",
  },
  {
    label: "cm_year",
    icon: "sac/calendar",
  },
  {
    label: "cm_yearmonth",
    icon: "sac/calendar",
  },
  {
    label: "cm_yearmonthduration",
    icon: "fob-watch",
  },
];

export function sap() {
  return (Cypress as any).state("window").sap;
}

function addFrameToFile(fileName: string) {
  const lastDotIndex = fileName.lastIndexOf(".");
  if (lastDotIndex === -1) {
    // No extension found
    return fileName + "_frame";
  } else {
    // Insert '_frame' before the file extension
    return fileName.substring(0, lastDotIndex) + "_frame" + fileName.substring(lastDotIndex);
  }
}

function getFileType(fileName: string) {
  const lastDotIndex = fileName.lastIndexOf(".");
  if (lastDotIndex === -1 || lastDotIndex === fileName.length - 1) {
    // No extension found or the dot is at the end
    return "";
  } else {
    // Get extension and convert it to uppercase
    return fileName.substring(lastDotIndex + 1).toUpperCase();
  }
}

export function mockOntologyRequestWithRdfData(
  ontUri: string,
  rdfFileName: string,
  request: string,
  alias = "getOntology",
  lastModifiedDate = "2025-01-01T00:00:00Z"
) {
  const rdfFilesPath = "cypress/fixtures/graphbuilder/rdf/";
  const ontFilePath = rdfFilesPath + "ontologies/" + rdfFileName;
  const ontFrameUri = ontUri + "_baseframe";
  const frameFilePath = addFrameToFile(rdfFilesPath + "frames/" + rdfFileName);
  const fileType = getFileType(rdfFileName);

  cy.readFile(ontFilePath).then((ontTrig) => {
    cy.readFile(frameFilePath).then((frameTrig) => {
      cy.intercept("GET", request, {
        body: {
          body: [
            {
              data: ontTrig,
              format: fileType,
              isComplete: true,
              isEditable: true,
              graphType: "http://www.w3.org/2002/07/owl#Ontology",
              uri: ontUri,
              lastModifiedDate,
            },
            {
              data: frameTrig,
              format: fileType,
              isComplete: true,
              isEditable: true,
              graphType: "http://sap.com/semantics/BaseFrameOntology#BaseFrameOntology",
              uri: ontFrameUri,
              lastModifiedDate,
            },
          ],
          headers: {},
          statusCodeValue: 200,
        },
      }).as(alias);
    });
  });
}

export function mockSourceFramesWithRdfData(innerFixturePath: string, delay = 0, searchRequest?: string) {
  const rdfFilesPath = "cypress/fixtures/graphbuilder/rdf/";
  const fullPath = rdfFilesPath + innerFixturePath;
  const fileType = getFileType(fullPath);
  cy.readFile(fullPath).then((frameTrig) => {
    const store = new Store();
    const parser = new Parser();

    const frameQuads = parser.parse(frameTrig);
    store.addQuads(frameQuads);
    const bodyData = [];

    store.getGraphs(null, null, null).forEach((graph) => {
      const writer = new Writer();
      const quads = store.getQuads(null, null, null, graph);
      writer.addQuads(quads);
      let payload = "";
      writer.end((_err, result) => (payload = result));

      bodyData.push({
        data: payload,
        format: fileType,
        isComplete: true,
        isEditable: true,
        graphType: "http://sap.com/semantics/BaseFrameOntology#BaseFrameOntology",
        uri: graph.id,
      });
    });

    let targetRequest: string;
    if (searchRequest) {
      targetRequest = searchRequest;
    } else {
      targetRequest = CM_REQUESTS.SOURCES;
    }
    cy.intercept("GET", targetRequest, {
      body: {
        body: bodyData,
        headers: {},
        statusCodeValue: 200,
      },
      delay,
    }).as("getSourceFrames");
  });
}

export function mockSourceFramesWithRdfDataAndFolders(
  sourceFramesFilePath: string,
  folderGraphsFilePath: string,
  searchRequest?: string
) {
  const rdfFilesPath = "cypress/fixtures/graphbuilder/rdf/";
  cy.readFile(rdfFilesPath + sourceFramesFilePath).then((sourceFramesFile) => {
    const bodyData = [];
    bodyData.push(
      ...readGraphsFromTrigFile(sourceFramesFile, "http://sap.com/semantics/BaseFrameOntology#BaseFrameOntology")
    );

    cy.readFile(rdfFilesPath + folderGraphsFilePath).then((foldersTrig) => {
      bodyData.push(...readGraphsFromTrigFile(foldersTrig, "http://com.sap.dwc/Datasphere#Folder"));
    });

    let targetRequest: string;
    if (searchRequest) {
      targetRequest = searchRequest;
    } else {
      targetRequest = CM_REQUESTS.SOURCES_WITH_FOLDERS;
    }
    cy.intercept("GET", targetRequest, {
      body: {
        body: bodyData,
        headers: {},
        statusCodeValue: 200,
      },
    }).as("getSourceFrames");
  });
}

const readGraphsFromTrigFile = (trigFile: any, graphType: string): IModelGraph[] => {
  const bodyData = [];

  const foldersStore = new Store();
  const parser = new Parser();
  const folderQuads = parser.parse(trigFile);
  foldersStore.addQuads(folderQuads);

  foldersStore.getGraphs(null, null, null).forEach((graph) => {
    const foldersWriter = new Writer();
    const quads = foldersStore.getQuads(null, null, null, graph);
    foldersWriter.addQuads(quads);
    let payload = "";
    foldersWriter.end((_err, result) => (payload = result));

    bodyData.push({
      data: payload,
      format: "TRIG",
      isComplete: true,
      isEditable: true,
      uri: graph.id,
      graphType,
    });
  });
  return bodyData;
};

// TODO - change this to use native cypress cy.selectFile instead when cypress gets updated to version 9.3
// and the method becomes available
export function uploadFile(fileUploaderName: string, fixtureFilePath: string, fileName: string) {
  cy.get(`input[type=file][name=${fileUploaderName}]`, original).then((subject) => {
    cy.fixture(fixtureFilePath).then((fileContent) => {
      const testFile = new File([fileContent], fileName, { type: "application/x-trig" });
      const dataTransfer = new DataTransfer();
      const fileInput = subject[0];
      dataTransfer.items.add(testFile);
      (fileInput as any).files = dataTransfer.files;
      cy.wrap(subject).trigger("change", { force: true });
    });
  });
}

export function waitDebounce(debounceTimeInMilliseconds: number = 1000) {
  cy.wait(debounceTimeInMilliseconds);
}

export function searchSources(searchText: string) {
  cy.get(SOURCE_CONCEPTS_TAB.SEARCH).type(searchText);
  waitDebounce();
}

export function searchParentConcepts(searchText: string) {
  cy.get(CONCEPT_DETAILS.PARENT_CONCEPTS_DIALOG_SEARCH).click().type(searchText);
  waitDebounce();
}

export function addDetachedPropertyToConceptFromDialog(concepts: string[], parentPanel?: string) {
  for (let i = 0; i < concepts.length; i++) {
    selectConceptFromDetachedPropertiesDialog(
      i,
      concepts[i],
      DETAILS_BASE.SELF === parentPanel ? DETACHED_PROPERTY(false).DIALOG_LIST : DETACHED_PROPERTY(true).DIALOG_LIST
    );
  }
  cy.get(
    DETAILS_BASE.SELF === parentPanel
      ? DETACHED_PROPERTY(false).DIALOG_SELECT_BUTTON
      : DETACHED_PROPERTY(true).DIALOG_SELECT_BUTTON
  ).click();
}

export function waitForRepoExplorer() {
  cy.wait(2000); // the loading state on the generic repo explorer seem a bit flaky, this attempts to make this test more stable
}

export function chooseTargetConceptWarningDialogAction(chosenAction: "cm_proceed_without_target" | "cm_choose_target") {
  cy.get(".targetConceptWarningDialog").should("be.visible");
  targetConceptsValueStateWarningIs("noTargetConcept");
  cy.get(".targetConceptWarningDialog").contains(chosenAction, original).click();
}

export function checkConsistencyOfTreeItemDisplayed(itemLabel: string) {
  workingTreeNodeIsSelected(itemLabel);
  detailsLabelInputIs(itemLabel);
}

export function assertElementIsReadOnly(elementId: string) {
  cy.get(elementId).invoke("attr", "readonly").should("be.eq", "readonly");
}

export function validateSaveDialogWithFoldersDesign(dialogId: string) {
  // testing just because its a custom control used on datasphere
  assertOnUI5ComponentFunctionValue(dialogId, "getContentWidth", (width) => {
    expect(width).to.equal("50%");
  });
  assertOnUI5ComponentFunctionValue(dialogId, "getContentHeight", (height) => {
    expect(height).to.equal("auto");
  });
}
