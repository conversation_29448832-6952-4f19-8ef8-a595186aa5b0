/** @format */

import {
  assertControl,
  assertMTableRowCount,
  openViewMonitorDetailsPage,
  prepareTestEnvironment,
  selectSectionByIndex,
} from "../../pageobjects/dataIntegration/DataIntegrationHelper";
/* jscpd:ignore-start */
describe.skip("View Persistency - Metrics", () => {
  beforeEach(() => {
    prepareTestEnvironment({});
    cy.intercept("GET", "**/persistedViews/View**", { fixture: "viewmonitor/persistedView1Details.json" }).as(
      "getPersistedViewDetails"
    );
    cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });
    cy.intercept("GET", "**/persistedViews**", { fixture: "viewmonitor/persistedViews" }).as("getPersistedViews");
    cy.intercept("GET", "**/schedules**", []).as("getSchedules");

    cy.intercept("GET", "**/monitoring/TASKS/TASK_LOGS_MEMORY**", {
      "@odata.context": "$metadata#TASK_LOGS_MEMORY",
      value: [
        {
          APPLICATION_NAME: "VIEWS",
          OBJECT_ID: "OrdersViewManualPartition_AS",
          STATUS: "COMPLETED",
          RECORDS: 13,
          STATEMENTS: null,
          RUN_TIME: 10,
          USED_IN_DISK: 0.19,
          OOM: false,
          START_TIME: "2024-03-29T04:49:36.183Z",
          USED_IN_MEMORY: 0.38,
          TARGET_TABLE: "OrdersViewManualPartition_AS_$PT1",
          SPACE_ID: "AKOM_SPACE",
          USER: "VV",
          PEAK_CPU: 3438.45,
          TASK_LOG_ID: 5684426,
          ACTIVITY: "PERSIST",
          PEAK_MEMORY: 32.24,
          SUB_STATUS: null,
        },
      ],
    }).as("getTaskLogsMemory");
    cy.intercept("GET", "**/logs?objectId=View1&getLocks=true", {
      fixture: "viewmonitor/persistencylogs",
    }).as("getPersistencyLogs");
    cy.intercept("GET", "**/extendedlogs/**", { fixture: "viewmonitor/persistencylogswithmetrics" }).as(
      "getExtendedLogs"
    );
    cy.intercept("GET", "**/resources/spaces**", { fixture: "spacesResponse" }).as("getSpaceDetails");
  });

  describe("View Persistency general metrics", () => {
    beforeEach(() => {
      openViewMonitorDetailsPage("SPACE1234", "View1");
      cy.wait(["@getPersistedViewDetails", "@getPersistencyLogs", "@getExtendedLogs", "@getTaskLogsMemory"]);
    });

    it.skip("Check Metrics Table", function () {
      selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--detailLogTable", 2);
      assertControl("shellMainContent---dataIntegrationComponent---taskLog--metricsTable", "exist");
    });

    it("Search in the Metric Search Field", function () {
      selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--detailLogTable", 2);
      cy.byId("shellMainContent---dataIntegrationComponent---taskLog--metricSearch").then(
        async (searchField: sap.m.SearchField) => {
          searchField.setValue("Mode");
          searchField.fireLiveChange({ newValue: "Mode" });
        }
      );
      assertMTableRowCount("shellMainContent---dataIntegrationComponent---taskLog--metricsTable", 1);
    });

    it("Check Partition Metrics Table", function () {
      selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--detailLogTable", 3);
      assertControl("shellMainContent---dataIntegrationComponent---taskLog--partitionMetricsTable", "exist");
    });

    it("Search in the Partition Search Field", function () {
      selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--detailLogTable", 3);
      cy.byId("shellMainContent---dataIntegrationComponent---taskLog--partitionmetricSearch").then(
        async (searchField: sap.m.SearchField) => {
          searchField.setValue("Null");
          searchField.fireLiveChange({ newValue: "Null" });
        }
      );
      assertMTableRowCount("shellMainContent---dataIntegrationComponent---taskLog--partitionMetricsTable", 1);
    });
  });

  describe("View Persistency general metrics - Error scenario", () => {
    beforeEach(() => {
      cy.intercept("GET", "**/monitoring/TASKS/TASK_LOGS_MEMORY**", {
        statusCode: 502,
        body: "Bad Gateway",
        headers: {
          "content-type": "text/plain",
        },
      }).as("getTaskLogsMemory");
      openViewMonitorDetailsPage("SPACE1234", "View1");
      cy.wait(["@getPersistedViewDetails", "@getPersistencyLogs", "@getExtendedLogs", "@getTaskLogsMemory"]);
    });

    it.skip("Check Metrics Table", function () {
      selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--detailLogTable", 2);
      cy.wait(2);
      assertControl("shellMainContent---dataIntegrationComponent---taskLog--metricsTable", "exist");
    });
  });
});
