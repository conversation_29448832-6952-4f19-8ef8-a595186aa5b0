/** @format */

import { DIPage } from "../../pageobjects/dataIntegration/DIDescriptor";
import {
  openViewMonitorOverviewPage,
  prepareTestEnvironment,
  selectSegmentedButton,
  selectTableRow,
} from "../../pageobjects/dataIntegration/DataIntegrationHelper";
/* jscpd:ignore-start */
describe("View Monitor - Basic", () => {
  beforeEach(() => {
    prepareTestEnvironment({
      DWC_REMOTE_TABLE_REPLICATION_TASK: true,
      DWC_DUMMY_SPACE_PERMISSIONS: false,
    });

    cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });

    cy.intercept("GET", "**/persistedViews**", { fixture: "viewmonitor/persistedViews.json" }).as("getPersistedViews");
    cy.intercept("GET", "**/schedules?**", []).as("getSchedules");
    cy.intercept("POST", "**/tf/directexecute", {}).as("stopPersistency");
    cy.intercept("POST", "**/tf/directexecute", {}).as("start");
    cy.intercept("POST", "**/persistedViews/View1/stop?isDeletePersistedView=true", {}).as("deleteView");
  });

  describe("Remote table replication logs and messages", () => {
    beforeEach(() => {
      openViewMonitorOverviewPage("SPACE1234");
      cy.wait(["@getUser", "@getContractEnddate", "@getPersistedViews"]);
      selectSegmentedButton("shellMainContent---dataIntegrationComponent---viewmonitor--ViewsBtn", 0);
    });

    it("Validate the no. of rows in view monitor table", function () {
      cy.get("#" + "shellMainContent---dataIntegrationComponent---viewmonitor--viewMonitorTable").then((element) => {
        cy.window().then((w) => {
          const table = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
          expect(table.getModel().getData().tables.length).to.equal(2);
        });
      });
    });
    it("Search in the Search Field", function () {
      cy.byId("shellMainContent---dataIntegrationComponent---viewmonitor--searchTablesInput").then(
        async (searchField: sap.m.SearchField) => {
          searchField.setValue("1");
          searchField.fireLiveChange({ newValue: "1" });
        }
      );
    });

    it("Start view Persistency", function () {
      cy.get(`#${DIPage.VIEW_MONITOR_TBL}`).should("exist");
      selectTableRow(DIPage.VIEW_MONITOR_TBL, 0);
      cy.byId("shellMainContent---dataIntegrationComponent---viewmonitor--viewPersistencyActionMenuBtn").then(
        (menu: sap.m.MenuButton) => {
          menu.getMenu().getItems()[0].firePress();
        }
      );
    });

    it("Re-Start view Persistency - Error Scenario", function () {
      cy.get(`#${DIPage.VIEW_MONITOR_TBL}`).should("exist");
      selectTableRow(DIPage.VIEW_MONITOR_TBL, 0);
      cy.byId("shellMainContent---dataIntegrationComponent---viewmonitor--viewPersistencyActionMenuBtn").then(
        (menu: sap.m.MenuButton) => {
          menu.getMenu().getItems()[0].firePress();
        }
      );
    });
    it("Stop view Persistency", function () {
      cy.get(`#${DIPage.VIEW_MONITOR_TBL}`).should("exist");
      selectTableRow(DIPage.VIEW_MONITOR_TBL, 1);
      cy.byId("shellMainContent---dataIntegrationComponent---viewmonitor--viewPersistencyActionMenuBtn").then(
        (menu: sap.m.MenuButton) => {
          menu.getMenu().getItems()[1].firePress();
        }
      );
    });
  });
});
/* jscpd:ignore-end */
