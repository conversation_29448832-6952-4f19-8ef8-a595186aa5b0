/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { BuilderType, getAPIInstance, IAPI } from "../../pageobjects/api/IAPI";
import { XHelpers } from "../../pageobjects/api/impl/Helpers";
import { ShellPages } from "../../pageobjects/shell";
/* jscpd:ignore-start */
describe("view persitency details ui and action", () => {
  let api: IAPI;
  const DI_PAGE = "shellMainContent---dataIntegrationComponent---viewmonitor";
  const TAB_ID = "shellMainContent---dataIntegrationComponent---dilandingpage--iconTabBar";
  const TAB_KEY = "viewMonitor";
  const tableid = `${DI_PAGE}--viewMonitorTable`;
  const taskSchedule = "taskLog--scheduleMenuTask";
  const tasklogTableId = "shellMainContent---dataIntegrationComponent---taskLog";

  describe(`${Cypress.spec.relative} without SDP`, () => {
    beforeEach(async () => {
      api = getAPIInstance();
      const routes = [...XHelpers.DEFAULT_MOCK_REPONSES];
      XHelpers.modifyFeatureFlag(routes, "DWC_DUMMY_SPACE_PERMISSIONS", false);
      await api.setupBeforeEach(/*useMock*/ true, /*defaultFixture*/ true, /*routes*/ routes);
      await api.mockServerCall(
        "GET",
        "**/remoteTables**",
        "fixture:remotetablemonitor/remoteTableBlueGreenReplication",
        "getRemoteTables"
      );
      await api.mockServerCall(
        "GET",
        "**/persistedViews**",
        "fixture:viewmonitor/persistedViewForTasklogDetails",
        "getPersistedViews"
      );
      await api.mockServerCall(
        "GET",
        "**/View02**",
        "fixture:viewmonitor/persistedViewForTasklogDetails",
        "getPersistedViews"
      );
      await api.mockServerCall(
        "GET",
        "**/logs?objectId=View02&getLocks=true",
        "fixture:viewmonitor/viewDetailLog",
        "getPersistencyLogs"
      );
      await api.mockServerCall(
        "GET",
        "**/logs?taskLogId=41",
        "fixture:viewmonitor/viewDetailLogMessages",
        "getPersistencyLogMessages"
      );
      await api.mockServerCall("GET", "**/persistedViews/View01", "fixture:viewmonitor/viewDetails", "getViewDetails");
      await api.mockServerCall("GET", "**/logs?objectId=View2&getLocks=true", [], "getPersistencyLogs");
      await api.mockServerCall("PUT", "**/setfailed/41", { status: "success" }, "setToFailed", 204);
      await api.mockServerCall("GET", "**/schedules/consent", { consent: false }, "getConsent");
      await api.mockServerCall("GET", "**/schedules**", [], "getSchedules");
      await api.openModulePage(ShellPages.DataIntegration);
      await api.openSpace("SPACE1234", BuilderType.dataintegration);
      await api.XHRWait("@getRemoteTables");
    });

    describe("View Persistency Improvements - Toggle Business Name", () => {
      it("Check if meu buttons, new columns are working properly", async () => {
        await api.selectIconTabBarItem(TAB_ID, TAB_KEY);
        await api.assertContainsText({
          id: "shellMainContent---dataIntegrationComponent---dilandingpage--TabViewMonitor",
          value: "TabViewMonitorNew",
        });
        await api.XHRWait("@getPersistedViews");
        await api.assertButton({
          button: "shellMainContent---dataIntegrationComponent---viewmonitor--viewPersistencyActionMenuBtn",
          enabled: false,
        });
        await api.assertButton({
          button: "shellMainContent---dataIntegrationComponent---viewmonitor--scheduleMenu",
          enabled: false,
        });
        await api.assertButton({
          button: "shellMainContent---dataIntegrationComponent---viewmonitor--personalizeTableButton",
          enabled: true,
        });
        await api.selectSegmentedButton("shellMainContent---dataIntegrationComponent---viewmonitor--ViewsBtn", 0, true);
        await api.selectTableRow(tableid, 0, false, true);
        const table = await cy.byId(tableid);
        await api.assertControlExists({
          id: "shellMainContent---dataIntegrationComponent---viewmonitor--viewPersistencyActionMenuBtn",
          enabled: true,
        });
        await api.assertControlExists({
          id: "shellMainContent---dataIntegrationComponent---viewmonitor--scheduleMenu",
          enabled: true,
        });
        await api.pressButton("shellMainContent---dataIntegrationComponent---viewmonitor--personalizeTableButton");
        await api.setCheckBox(
          "shellMainContent---dataIntegrationComponent---viewmonitor--viewMonitorTable-PersoDialog-cli-shellMainContent---dataIntegrationComponent---viewmonitor--viewMonitorTable-PersoDialog-colTable-6",
          true
        );
        await api.setCheckBox(
          "shellMainContent---dataIntegrationComponent---viewmonitor--viewMonitorTable-PersoDialog-cli-shellMainContent---dataIntegrationComponent---viewmonitor--viewMonitorTable-PersoDialog-colTable-9",
          true
        );
        await api.pressButton(
          "shellMainContent---dataIntegrationComponent---viewmonitor--viewMonitorTable-PersoDialog-buttonOk"
        );
        await api.assertControlExists({
          id: "shellMainContent---dataIntegrationComponent---viewmonitor--numOfRecordsCol",
        });
        await api.assertControlExists({
          id: "shellMainContent---dataIntegrationComponent---viewmonitor--scheduleOwnerId",
        });
        await table.getRows()[0].getRowAction().getItems()[0].firePress();
        // await api.XHRWait("@getPersistencyLogMessages");
        await api.assertControlExists({
          id: "shellMainContent---dataIntegrationComponent---taskLog--idEcnActivity",
        });
      });

      it("Check if meu button action is disabled when one of the instance is running", async () => {
        await api.mockServerCall(
          "GET",
          "**/persistedViews**",
          "fixture:viewmonitor/persistedViewForTasklogDetails1",
          "getPersistedViews1"
        );
        await api.mockServerCall("POST", "**/tf/directexecute", {}, "dataPersistence");
        await api.mockServerCall("GET", "**/schedules**", "fixture:viewmonitor/schedulesforViews", "schedules");
        await api.selectIconTabBarItem(TAB_ID, TAB_KEY);
        await api.XHRWait("@getPersistedViews1");
        await api.assertControlExists({
          id: "shellMainContent---dataIntegrationComponent---viewmonitor--viewMonitoringPage",
        });
        await api.selectSegmentedButton("shellMainContent---dataIntegrationComponent---viewmonitor--ViewsBtn", 0, true);
        await api.selectTableRow(tableid, 5, false, true);
        const table = await cy.byId(tableid);
        await api.assertControlExists({
          id: "shellMainContent---dataIntegrationComponent---viewmonitor--viewPersistencyActionMenuBtn",
          enabled: true,
        });
        await api.assertControlExists({
          id: "shellMainContent---dataIntegrationComponent---viewmonitor--scheduleMenu",
          enabled: true,
        });
        await api.clickMenuButtonItem(
          "shellMainContent---dataIntegrationComponent---viewmonitor--viewPersistencyActionMenuBtn",
          0
        );
        await api.assertMenuButtonItem({
          id: "shellMainContent---dataIntegrationComponent---viewmonitor--viewPersistencyActionMenuBtn",
          itemIndex: 0,
          enabled: false,
        });
      });
      it("Check for search and filter in monitor", async () => {
        await api.selectIconTabBarItem(TAB_ID, TAB_KEY);
        await api.assertContainsText({
          id: "shellMainContent---dataIntegrationComponent---dilandingpage--TabViewMonitor",
          value: "TabViewMonitor",
        });

        await api.XHRWait("@getPersistedViews");
        await api.selectSegmentedButton("shellMainContent---dataIntegrationComponent---viewmonitor--ViewsBtn", 0, true);
        await api.typeText("shellMainContent---dataIntegrationComponent---viewmonitor--searchTablesInput", "one");
        await api.assertTable({
          table: tableid,
          rows: 1,
        });
        const table = await cy.byId(tableid);
        const tableColumnId = "shellMainContent---dataIntegrationComponent---viewmonitor--viewNameColumn";
        await table
          .getColumns()
          .find((column) => column.getId() === tableColumnId)
          .setFilterValue("tbook");
        await api.typeText("shellMainContent---dataIntegrationComponent---viewmonitor--searchTablesInput", "one");
        await api.assertTable({
          table: tableid,
          rows: 1,
        });
      });
    });
  });
});
/* jscpd:ignore-end */
