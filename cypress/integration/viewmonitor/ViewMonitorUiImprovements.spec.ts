/** @format */

import { BuilderType, getAPIInstance, IAPI } from "../../pageobjects/api/IAPI";
import { XHelpers } from "../../pageobjects/api/impl/Helpers";
import { ShellPages } from "../../pageobjects/shell";
/* jscpd:ignore-start */
describe("View Monitoring Mass Operations", () => {
  let api: IAPI;
  const DI_PAGE = "shellMainContent---dataIntegrationComponent---viewmonitor";
  const TAB_ID = "shellMainContent---dataIntegrationComponent---dilandingpage--iconTabBar";
  const TAB_KEY = "viewMonitor";
  const persistencyMenu = "shellMainContent---dataIntegrationComponent---viewmonitor--viewPersistencyActionMenuBtn";
  const scheduleMenu = "shellMainContent---dataIntegrationComponent---viewmonitor--scheduleMenu";
  const tableId = "shellMainContent---dataIntegrationComponent---viewmonitor--viewMonitorTable";
  const remoteTableId = `shellMainContent---dataIntegrationComponent---remotetablemonitor--idRemoteTablesTable`;

  const scheduleList = [
    {
      scheduleId: "8d0fcf26-3c55-4439-a9b0-40abd5282e97",
      spaceId: "MODELING_BLR",
      applicationId: "VIEWS",
      activity: "PERSIST",
      objectId: "Order_Details_Extends1",
      description: "Order_Details_Extends1",
      cron: "33 20 * * 0",
      validFrom: "2020-10-27T20:33:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "RICKY_ROY",
      createdAt: "2020-10-27T20:33:22.814Z",
      changedAt: "2020-10-27T20:33:22.876Z",
      nextRun: "2020-11-01T20:33:00.000Z",
    },
    {
      scheduleId: "8663963a-4d92-4150-9c1c-c29446c9466e",
      spaceId: "MODELING_BLR",
      applicationId: "VIEWS",
      activity: "PERSIST",
      objectId: "9030factView2",
      description: "9030factView2",
      cron: "37 20 * * 3",
      validFrom: "2020-10-27T20:37:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "RICKY_ROY",
      createdAt: "2020-10-27T20:37:17.084Z",
      changedAt: "2020-10-27T20:37:17.144Z",
      nextRun: "2020-11-04T20:37:00.000Z",
    },
  ];
  beforeEach(async () => {
    api = getAPIInstance();
    const routes = [...XHelpers.DEFAULT_MOCK_REPONSES];
    await api.setupBeforeEach(/*useMock*/ true, /*defaultFixture*/ true, /*routes*/ routes);
    XHelpers.modifyFeatureFlag(routes, "DWC_DUMMY_SPACE_PERMISSIONS", false);
    XHelpers.modifyFeatureFlag(routes, "DWCO_DI_MONITOR_UI_IMPROVEMENTS", true);
    XHelpers.modifyFeatureFlag(routes, "DWCO_REUSABLE_TASK_SCHEDULING_UI", true);
    await api.mockServerCall("GET", "**/localTables**", { localTables: [] }, "getLocalTables");
    await api.mockServerCall(
      "GET",
      "**/persistedViews**",
      "fixture:viewmonitor/taskScheduleViewMonitor.json",
      "getPersistedViews"
    );
    await api.mockServerCall("GET", "**/resources/spaces**", "fixture:spacesResponse", "getSpaceDetails");
    await api.mockServerCall("GET", "**/schedules?**", scheduleList, "getSchedules");
    await api.mockServerCall("GET", "**/schedules/consent", { consent: true }, "getConsent");
    await api.mockServerCall("PUT", "**/mass/persistedViews/stop", "ok", "removePersistency");
    await api.mockServerCall("POST", "**/stop", "ok", "removePersistedData");
    await api.mockServerCall("POST", "**/tf/directexecute", {}, "start");
  });
  describe("View Monitor Ui Improvements", () => {
    it("should check for column order schedule popover", async () => {
      await api.enableUIFeatureflag("DWCO_DI_MONITOR_UI_IMPROVEMENTS");
      await api.enableUIFeatureflag("DWCO_REUSABLE_TASK_SCHEDULING_UI");
      await api.openModulePage(ShellPages.DataIntegration);
      await api.openSpace("SPACE1234", BuilderType.dataintegration);
      await api.XHRWait("@getLocalTables");
      await api.selectIconTabBarItem(TAB_ID, TAB_KEY);
      await api.XHRWait("@getPersistedViews");
      await api.selectSegmentedButton("shellMainContent---dataIntegrationComponent---viewmonitor--ViewsBtn", 0, true);
      await api.assertMenuButtonItem({
        id: persistencyMenu,
        itemIndex: 0,
        enabled: false,
      });
      await api.assertMenuButtonItem({
        id: persistencyMenu,
        itemIndex: 1,
        enabled: false,
      });
      const table = await cy.byId(tableId);
      expect(table.getColumns()[2].getLabel().getText()).to.equals("DATA_PERSISTENCY");
      expect(table.getColumns()[3].getLabel().getText()).to.equals("LAST_UPDATED");
      expect(table.getColumns()[7].getLabel().getText()).to.equals("refreshFrequencyNewNew");
      expect(table.getRows()[4].getCells()[5].getItems()[1].getItems()[0].getText()).to.equals("33 20 * * 0");
      expect(table.getRows()[3].getCells()[5].getItems()[1].getItems()[0].getText()).to.equals("37 20 * * 3");
      table.getRows()[4].getCells()[5].getItems()[1].getItems()[0].firePress();
      await api.XHRWait("@getSchedules");
      await api.assertControlExists({
        id: "shellMainContent---dataIntegrationComponent---viewmonitor--viewScheduleDialog--schedulePopover--TaskScheduleInfo",
      });
      await api.assertControlExists({
        id: "shellMainContent---dataIntegrationComponent---viewmonitor--viewScheduleDialog--schedulePopover--editScheduleButton",
      });
    });
  });
});
/* jscpd:ignore-end */
