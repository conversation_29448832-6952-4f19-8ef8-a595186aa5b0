/** @format */

import {
  assertButton,
  assertControl,
  openViewMonitorDetailsPage,
  prepareTestEnvironment,
  pressButton,
  selectSectionByIndex,
} from "../../pageobjects/dataIntegration/DataIntegrationHelper";
import { DB_BUTTON, DB_CONTROL } from "../../pageobjects/databuilder/descriptor";

/* jscpd:ignore-start */
describe("cypress/integration/viewmonitor/ViewPartitionSection", () => {
  const createPartitionButton = "shellMainContent---dataIntegrationComponent---taskLog--DefinePartition";
  const deletePartitionButton = "shellMainContent---dataIntegrationComponent---taskLog--DeletePartitionButton";
  const SCORED_COLUMN_TABLE =
    "shellMainContent---dataIntegrationComponent---taskLog--partitionDialog--scoredColumnsTable";
  const INTERVAL_BUTTON =
    "shellMainContent---dataIntegrationComponent---taskLog--partitionDialog--defineIntervalButton";
  const RANGE_LIST = "shellMainContent---dataIntegrationComponent---taskLog--partitionDialog--rangeList";

  describe(`${Cypress.spec.relative} without SDP`, () => {
    beforeEach(() => {
      prepareTestEnvironment({
        DWC_DUMMY_SPACE_PERMISSIONS: false,
        DWCO_DI_MONITOR_UI_IMPROVEMENTS: false,
      });

      cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });

      cy.intercept("GET", "**/schedules**", []).as("getSchedules");
      cy.intercept("GET", "**/persistedViews/View2**", { fixture: "viewmonitor/persistedView2Details.json" }).as(
        "getPersistedViewDetails"
      );
      cy.intercept("GET", "**/partitioning/**", { fixture: "viewmonitor/view2PartitionData.json" }).as(
        "getViewPartitionData"
      );
      cy.intercept("GET", "**/logs?objectId=View2&getLocks=true", []).as("getPersistencyLogs");
      cy.intercept("DELETE", "**/partitioning/**", { response: {}, statusCode: 200 }).as("deletePartitionData");
      cy.intercept("GET", "**/schedules/consent", { consent: false }).as("getConsent");
      cy.intercept("GET", "**/resources/spaces**", { fixture: "spacesResponse.json" }).as("getSpaceDetails");
    });

    describe("View Partition Section", () => {
      it("Create view partitioning", () => {
        cy.intercept("GET", "**/persistedViews/View1**", { fixture: "viewmonitor/persistedView1Details.json" }).as(
          "getPersistedViewDetails"
        );
        cy.intercept("GET", "**/partitioning/**", { fixture: "viewmonitor/view1PartitionData.json" }).as(
          "getViewPartitionData"
        );
        cy.intercept("GET", "**/logs?objectId=View1&getLocks=true", []).as("getPersistencyLogs");
        cy.intercept("POST", "**/partitioning/**", { response: {}, statusCode: 200 }).as("savePartitionData");

        openViewMonitorDetailsPage("SPACE1234", "View1");

        selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--masterLogTableOPL", 1);
        assertButton(createPartitionButton, true);
        pressButton(createPartitionButton);

        cy.wait("@getPersistedViewDetails");
        assertControl(DB_CONTROL.VIEW_PARTITION_DIALOG + "--partitionDialog", "exist");

        cy.get("#" + SCORED_COLUMN_TABLE).then((element) => {
          cy.window().then((w) => {
            const table = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const item = table.getItems()[0];
            table.setSelectedItem(item, true);
            table.fireSelectionChange({ listItems: [item], listItem: item, selected: true });
            item.firePress();
          });
        });
        pressButton(INTERVAL_BUTTON);

        cy.get("#" + RANGE_LIST).then((element) => {
          cy.window().then((w) => {
            const list = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const lowIp = list.getItems()[0].getCells()[1].getItems()[1].getItems()[0];
            lowIp.setValue("0aa");
            lowIp.fireChange({ newValue: "0aa" });
            const highIp = list.getItems()[0].getCells()[1].getItems()[3].getItems()[0];
            highIp.setValue("bb");
            highIp.fireChange({ newValue: "bb" });
          });
        });

        pressButton(DB_BUTTON.VIEW_PARTITION_DIALOG_SAVE);
      });

      it("Create DateTime partitioning", () => {
        cy.intercept("GET", "**/persistedViews/View1**", { fixture: "viewmonitor/persistedView1Details.json" }).as(
          "getPersistedViewDetails"
        );
        cy.intercept("GET", "**/partitioning/**", { fixture: "viewmonitor/view4PartitionData.json" }).as(
          "getViewPartitionData"
        );
        cy.intercept("GET", "**/logs?objectId=View1&getLocks=true", []).as("getPersistencyLogs");
        cy.intercept("POST", "**/partitioning/**", { response: {}, statusCode: 200 }).as("savePartitionData");

        openViewMonitorDetailsPage("SPACE1234", "View1");

        selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--masterLogTableOPL", 1);
        assertButton(createPartitionButton, true);
        pressButton(createPartitionButton);

        cy.wait("@getPersistedViewDetails");
        assertControl(DB_CONTROL.VIEW_PARTITION_DIALOG + "--partitionDialog", "exist");

        cy.get("#" + SCORED_COLUMN_TABLE).then((element) => {
          cy.window().then((w) => {
            const table = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const item = table.getItems()[0];
            table.setSelectedItem(item, true);
            table.fireSelectionChange({ listItems: [item], listItem: item, selected: true });
            item.firePress();
          });
        });
        pressButton(INTERVAL_BUTTON);

        cy.get("#" + RANGE_LIST).then((element) => {
          cy.window().then((w) => {
            const list = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const lowIp = list.getItems()[0].getCells()[1].getItems()[1].getItems()[0];
            lowIp.setValue("Dec 1, 2024, 13:06:28");
            lowIp.fireChange({ newValue: "Dec 1, 2024, 13:06:28" });
            const highIp = list.getItems()[0].getCells()[1].getItems()[3].getItems()[0];
            highIp.setValue("Dec 28, 2024, 13:06:28");
            highIp.fireChange({ newValue: "Dec 28, 2024, 13:06:28" });
            pressButton(
              "shellMainContent---dataIntegrationComponent---taskLog--partitionDialog--lowDateTimePicker-__clone71-icon"
            );
            cy.wait(500);
            pressButton(
              "shellMainContent---dataIntegrationComponent---taskLog--partitionDialog--lowDateTimePicker-__clone71-Cancel-BDI-content"
            );
          });
        });

        pressButton(DB_BUTTON.VIEW_PARTITION_DIALOG_SAVE);
      });

      it("Create Timestamp partitioning", () => {
        cy.intercept("GET", "**/persistedViews/View1**", { fixture: "viewmonitor/persistedView1Details.json" }).as(
          "getPersistedViewDetails"
        );
        cy.intercept("GET", "**/partitioning/**", { fixture: "viewmonitor/view5PartitionData.json" }).as(
          "getViewPartitionData"
        );
        cy.intercept("GET", "**/logs?objectId=View1&getLocks=true", []).as("getPersistencyLogs");
        cy.intercept("POST", "**/partitioning/**", { response: {}, statusCode: 200 }).as("savePartitionData");

        openViewMonitorDetailsPage("SPACE1234", "View1");

        selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--masterLogTableOPL", 1);
        assertButton(createPartitionButton, true);
        pressButton(createPartitionButton);

        cy.wait("@getPersistedViewDetails");
        assertControl(DB_CONTROL.VIEW_PARTITION_DIALOG + "--partitionDialog", "exist");

        cy.get("#" + SCORED_COLUMN_TABLE).then((element) => {
          cy.window().then((w) => {
            const table = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const item = table.getItems()[0];
            table.setSelectedItem(item, true);
            table.fireSelectionChange({ listItems: [item], listItem: item, selected: true });
            item.firePress();
          });
        });
        pressButton(INTERVAL_BUTTON);

        cy.get("#" + RANGE_LIST).then((element) => {
          cy.window().then((w) => {
            const list = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const lowIp = list.getItems()[0].getCells()[1].getItems()[1].getItems()[0];
            lowIp.setValue("Dec 1, 2024, 13:06:28");
            lowIp.fireChange({ newValue: "Dec 1, 2024, 13:06:28" });
            const highIp = list.getItems()[0].getCells()[1].getItems()[3].getItems()[0];
            highIp.setValue("Dec 28, 2024, 13:06:28");
            highIp.fireChange({ newValue: "Dec 28, 2024, 13:06:28" });
          });
        });

        pressButton(DB_BUTTON.VIEW_PARTITION_DIALOG_SAVE);
      });

      it("Check Timestamp partitioning in YYYYMMDDHHmmSS format", () => {
        cy.intercept("GET", "**/persistedViews/View1**", { fixture: "viewmonitor/persistedView1Details.json" }).as(
          "getPersistedViewDetails"
        );
        cy.intercept("GET", "**/partitioning/**", { fixture: "viewmonitor/view5PartitionData.json" }).as(
          "getViewPartitionData"
        );
        cy.intercept("GET", "**/logs?objectId=View1&getLocks=true", []).as("getPersistencyLogs");
        cy.intercept("POST", "**/partitioning/**", { response: {}, statusCode: 200 }).as("savePartitionData");

        openViewMonitorDetailsPage("SPACE1234", "View1");

        selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--masterLogTableOPL", 1);
        assertButton(createPartitionButton, true);
        pressButton(createPartitionButton);

        cy.wait("@getPersistedViewDetails");
        assertControl(DB_CONTROL.VIEW_PARTITION_DIALOG + "--partitionDialog", "exist");

        cy.get("#" + SCORED_COLUMN_TABLE).then((element) => {
          cy.window().then((w) => {
            const table = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const item = table.getItems()[0];
            table.setSelectedItem(item, true);
            table.fireSelectionChange({ listItems: [item], listItem: item, selected: true });
            item.firePress();
          });
        });
        pressButton(INTERVAL_BUTTON);

        cy.get("#" + RANGE_LIST).then((element) => {
          cy.window().then((w) => {
            const list = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const lowIp = list.getItems()[0].getCells()[1].getItems()[1].getItems()[0];
            lowIp.setValue("202411111111");
            lowIp.fireChange({ newValue: "202411111111" });
            const highIp = list.getItems()[0].getCells()[1].getItems()[3].getItems()[0];
            highIp.setValue("202411111112");
            highIp.fireChange({ newValue: "202411111112" });
          });
        });
        pressButton(DB_BUTTON.VIEW_PARTITION_DIALOG_SAVE);
      });

      it("Create DateTime partitioning in YYYYMMDDHHmmSS format", () => {
        cy.intercept("GET", "**/persistedViews/View1**", { fixture: "viewmonitor/persistedView1Details.json" }).as(
          "getPersistedViewDetails"
        );
        cy.intercept("GET", "**/partitioning/**", { fixture: "viewmonitor/view4PartitionData.json" }).as(
          "getViewPartitionData"
        );
        cy.intercept("GET", "**/logs?objectId=View1&getLocks=true", []).as("getPersistencyLogs");
        cy.intercept("POST", "**/partitioning/**", { response: {}, statusCode: 200 }).as("savePartitionData");

        openViewMonitorDetailsPage("SPACE1234", "View1");

        selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--masterLogTableOPL", 1);
        assertButton(createPartitionButton, true);
        pressButton(createPartitionButton);

        cy.wait("@getPersistedViewDetails");
        assertControl(DB_CONTROL.VIEW_PARTITION_DIALOG + "--partitionDialog", "exist");

        cy.get("#" + SCORED_COLUMN_TABLE).then((element) => {
          cy.window().then((w) => {
            const table = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const item = table.getItems()[0];
            table.setSelectedItem(item, true);
            table.fireSelectionChange({ listItems: [item], listItem: item, selected: true });
            item.firePress();
          });
        });
        pressButton(INTERVAL_BUTTON);

        cy.get("#" + RANGE_LIST).then((element) => {
          cy.window().then((w) => {
            const list = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const lowIp = list.getItems()[0].getCells()[1].getItems()[1].getItems()[0];
            lowIp.setValue("20241201130628");
            lowIp.fireChange({ newValue: "20241201130628" });
            const highIp = list.getItems()[0].getCells()[1].getItems()[3].getItems()[0];
            highIp.setValue("20241228130628");
            highIp.fireChange({ newValue: "20241228130628" });
            pressButton(
              "shellMainContent---dataIntegrationComponent---taskLog--partitionDialog--lowDateTimePicker-__clone71-icon"
            );
            cy.wait(500);
            pressButton(
              "shellMainContent---dataIntegrationComponent---taskLog--partitionDialog--lowDateTimePicker-__clone71-Cancel-BDI-content"
            );
          });
        });

        pressButton(DB_BUTTON.VIEW_PARTITION_DIALOG_SAVE);
      });

      it("Create view partitioning - INT datatype", () => {
        cy.intercept("GET", "**/persistedViews/View1**", { fixture: "viewmonitor/persistedView1Details.json" }).as(
          "getPersistedViewDetails"
        );
        cy.intercept("GET", "**/partitioning/**", { fixture: "viewmonitor/view1PartitionData.json" }).as(
          "getViewPartitionData"
        );
        cy.intercept("GET", "**/logs?objectId=View1&getLocks=true", []).as("getPersistencyLogs");
        cy.intercept("POST", "**/partitioning/**", { response: {}, statusCode: 202 }).as("savePartitionData");

        openViewMonitorDetailsPage("SPACE1234", "View1");
        cy.wait(["@getUser", "@getContractEnddate", "@getPersistedViewDetails"]);

        selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--masterLogTableOPL", 1);
        assertButton(createPartitionButton, true);
        pressButton(createPartitionButton);

        assertControl(DB_CONTROL.VIEW_PARTITION_DIALOG + "--partitionDialog", "exist");
        cy.get("#" + SCORED_COLUMN_TABLE).then((element) => {
          cy.window().then((w) => {
            const table = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const item = table.getItems()[1];
            table.setSelectedItem(item, true);
            table.fireSelectionChange({ listItems: [item], listItem: item, selected: true });
            item.firePress();
          });
        });
        pressButton(INTERVAL_BUTTON);

        cy.get("#" + RANGE_LIST).then((element) => {
          cy.window().then((w) => {
            const list = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const lowIp = list.getItems()[0].getCells()[1].getItems()[1].getItems()[0];
            lowIp.setValue("000");
            lowIp.fireChange({ newValue: "000" });
            const highIp = list.getItems()[0].getCells()[1].getItems()[3].getItems()[0];
            highIp.setValue("011");
            highIp.fireChange({ newValue: "011" });
          });
        });

        pressButton(DB_BUTTON.VIEW_PARTITION_DIALOG_SAVE);
      });

      it("Create view partitioning - Error response", () => {
        cy.intercept("GET", "**/persistedViews/View1**", { fixture: "viewmonitor/persistedView1Details.json" }).as(
          "getPersistedViewDetails"
        );
        cy.intercept("GET", "**/partitioning/**", { response: {}, statusCode: 500 }).as("getViewPartitionData");
        cy.intercept("GET", "**/logs?objectId=View1&getLocks=true", []).as("getPersistencyLogs");
        cy.intercept("POST", "**/partitioning/**", { response: {}, statusCode: 202 }).as("savePartitionData");

        openViewMonitorDetailsPage("SPACE1234", "View1");
        cy.wait(["@getUser", "@getContractEnddate", "@getPersistedViewDetails"]);

        selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--masterLogTableOPL", 1);
        assertButton(createPartitionButton, true);
        pressButton(createPartitionButton);
      });

      it("Save and delete view partition", () => {
        openViewMonitorDetailsPage("SPACE1234", "View2");

        selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--masterLogTableOPL", 1);
        assertButton(deletePartitionButton, true);
        pressButton(deletePartitionButton);

        cy.get(".sapMMessageBox").should("contain", "Partition data will be deleted");
        cy.get(".sapMDialogFooter").find("#__mbox-btn-0").click();
        cy.get(".sapMMessageToast").then((messageToast) => {
          expect(messageToast).to.contain("Partition data deleted successfully.");
        });
      });

      it("Save failed - view partitioning", () => {
        cy.intercept("GET", "**/persistedViews/View1?includeBusinessNames=true", {
          fixture: "viewmonitor/persistedView1Details.json",
        }).as("getPersistedViewDetails");
        cy.intercept("GET", "**/partitioning/**", { fixture: "viewmonitor/view1PartitionData.json" }).as(
          "getViewPartitionData"
        );
        cy.intercept("GET", "**/logs?objectId=View1&getLocks=true", []).as("getPersistencyLogs");
        cy.intercept("POST", "**/partitioning/**", { response: {}, statusCode: 500 }).as("savePartitionData");

        openViewMonitorDetailsPage("SPACE1234", "View1");
        cy.wait(["@getUser", "@getContractEnddate", "@getPersistedViewDetails"]);

        selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--masterLogTableOPL", 1);
        assertButton(createPartitionButton, true);
        pressButton(createPartitionButton);

        assertControl(DB_CONTROL.VIEW_PARTITION_DIALOG + "--partitionDialog", "exist");
        cy.get("#" + SCORED_COLUMN_TABLE).then((element) => {
          cy.window().then((w) => {
            const table = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const item = table.getItems()[0];
            table.setSelectedItem(item, true);
            table.fireSelectionChange({ listItems: [item], listItem: item, selected: true });
            item.firePress();
          });
        });
        pressButton(INTERVAL_BUTTON);

        cy.get("#" + RANGE_LIST).then((element) => {
          cy.window().then((w) => {
            const list = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const lowIp = list.getItems()[0].getCells()[1].getItems()[1].getItems()[0];
            lowIp.setValue("0aa");
            lowIp.fireChange({ newValue: "0aa" });
            const highIp = list.getItems()[0].getCells()[1].getItems()[3].getItems()[0];
            highIp.setValue("bb");
            highIp.fireChange({ newValue: "bb" });
          });
        });

        pressButton(DB_BUTTON.VIEW_PARTITION_DIALOG_SAVE);
      });

      it("View partition details", () => {
        openViewMonitorDetailsPage("SPACE1234", "View2");

        selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--masterLogTableOPL", 1);
        assertButton(deletePartitionButton, true);
        cy.get("#taskLog--partitionAvailableLbl-text").click();
        cy.get("#" + DB_CONTROL.VIEW_MONITOR_TASKLOG + "--columName").should("have.text", "ID");
        cy.get("#" + DB_CONTROL.VIEW_MONITOR_TASKLOG + "--noOfPartition").should("have.text", "3");
      });
    });
  });
  describe(`${Cypress.spec.relative} with SDP`, () => {
    beforeEach(() => {
      prepareTestEnvironment({
        DWC_DUMMY_SPACE_PERMISSIONS: false,
      });

      cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });

      cy.intercept("GET", "**/schedules**", []).as("getSchedules");
      cy.intercept("GET", "**/persistedViews/View2**", { fixture: "viewmonitor/persistedView2Details.json" }).as(
        "getPersistedViewDetails"
      );
      cy.intercept("GET", "**/partitioning/**", { fixture: "viewmonitor/view2PartitionData.json" }).as(
        "getViewPartitionData"
      );
      cy.intercept("GET", "**/logs?objectId=View2&getLocks=true", []).as("getPersistencyLogs");
      cy.intercept("DELETE", "**/partitioning/**", { response: {}, statusCode: 200 }).as("deletePartitionData");
      cy.intercept("GET", "**/schedules/consent", { consent: false }).as("getConsent");
      cy.intercept("GET", "**/resources/spaces**", { fixture: "spacesResponse.json" }).as("getSpaceDetails");
    });

    describe("View Partition Section", () => {
      it("Create view partitioning", () => {
        cy.intercept("GET", "**/persistedViews/View1**", { fixture: "viewmonitor/persistedView1Details.json" }).as(
          "getPersistedViewDetails"
        );
        cy.intercept("GET", "**/partitioning/**", { fixture: "viewmonitor/view1PartitionData.json" }).as(
          "getViewPartitionData"
        );
        cy.intercept("GET", "**/logs?objectId=View2&getLocks=true", []).as("getPersistencyLogs");
        cy.intercept("POST", "**/partitioning/**", { response: {}, statusCode: 202 }).as("savePartitionData");

        openViewMonitorDetailsPage("SPACE1234", "View1");
        cy.wait(["@getUser", "@getContractEnddate", "@getPersistedViewDetails"]);

        selectSectionByIndex("shellMainContent---dataIntegrationComponent---taskLog--masterLogTableOPL", 1);
        assertButton(createPartitionButton, true);
        pressButton(createPartitionButton);

        assertControl(DB_CONTROL.VIEW_PARTITION_DIALOG + "--partitionDialog", "exist");
        cy.get("#" + SCORED_COLUMN_TABLE).then((element) => {
          cy.window().then((w) => {
            const table = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const item = table.getItems()[0];
            table.setSelectedItem(item, true);
            table.fireSelectionChange({ listItems: [item], listItem: item, selected: true });
            item.firePress();
          });
        });
        pressButton(INTERVAL_BUTTON);

        cy.get("#" + RANGE_LIST).then((element) => {
          cy.window().then((w) => {
            const list = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
            const lowIp = list.getItems()[0].getCells()[1].getItems()[1].getItems()[0];
            lowIp.setValue("0aa");
            lowIp.fireChange({ newValue: "0aa" });
            const highIp = list.getItems()[0].getCells()[1].getItems()[3].getItems()[0];
            highIp.setValue("bb");
            highIp.fireChange({ newValue: "bb" });
          });
        });

        pressButton(DB_BUTTON.VIEW_PARTITION_DIALOG_SAVE);
      });
    });
  });
});
/* jscpd:ignore-end */
