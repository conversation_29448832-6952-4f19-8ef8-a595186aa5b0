/** @format */

import {
  openViewMonitorDetailsPage,
  openViewMonitorOverviewPage,
  prepareTestEnvironment,
  selectSegmentedButton,
} from "../../pageobjects/dataIntegration/DataIntegrationHelper";

/* jscpd:ignore-start */
describe("cypress/integration/viewmonitor/ViewPartiallyPersisted", () => {
  const createPartitionButton = "shellMainContent---dataIntegrationComponent---taskLog--DefinePartition";
  const deletePartitionButton = "shellMainContent---dataIntegrationComponent---taskLog--DeletePartitionButton";
  const SCORED_COLUMN_TABLE =
    "shellMainContent---dataIntegrationComponent---taskLog--partitionDialog--scoredColumnsTable";
  const INTERVAL_BUTTON =
    "shellMainContent---dataIntegrationComponent---taskLog--partitionDialog--defineIntervalButton";
  const RANGE_LIST = "shellMainContent---dataIntegrationComponent---taskLog--partitionDialog--rangeList";

  describe(`${Cypress.spec.relative} without SDP`, () => {
    beforeEach(() => {
      prepareTestEnvironment({
        DWC_DUMMY_SPACE_PERMISSIONS: false,
      });

      cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });
      cy.intercept("GET", "**persistedViews**", { fixture: "viewmonitor/partiallyPersistedViews.json" }).as(
        "getPersistedViews"
      );
      cy.intercept("GET", "**/schedules**", []).as("getSchedules");
      cy.intercept("GET", "**/persistedViews/ParameterViewWithLocalTable**", {
        fixture: "viewmonitor/partiallyPersistedViewDetails.json",
      }).as("getPersistedViewDetails");
      cy.intercept("GET", "**/partitioning/**", { fixture: "viewmonitor/view2PartitionData.json" }).as(
        "getViewPartitionData"
      );
      cy.intercept("GET", "**/logs?objectId=ParameterViewWithLocalTable&getLocks=true", []).as("getPersistencyLogs");
      cy.intercept("DELETE", "**/partitioning/**", { response: {}, statusCode: 200 }).as("deletePartitionData");
      cy.intercept("GET", "**/schedules/consent", { consent: false }).as("getConsent");
      cy.intercept("GET", "**/resources/spaces**", { fixture: "spacesResponse.json" }).as("getSpaceDetails");
    });

    describe("View Monitor and Details Section", () => {
      it("Check for partially persisted flow for View Monitor OverViewPage", () => {
        cy.intercept("GET", "**/logs?objectId=View1&getLocks=true", []).as("getPersistencyLogs");
        openViewMonitorOverviewPage("SPACE1234");
        cy.wait("@getPersistedViews");
        selectSegmentedButton("shellMainContent---dataIntegrationComponent---viewmonitor--ViewsBtn", 0);
        cy.wait(200);
        cy.get("#shellMainContent---dataIntegrationComponent---viewmonitor--viewMonitorTable-rows-row2-col2").should(
          "contain.text",
          "Partially Persisted"
        );
        cy.get("#" + "shellMainContent---dataIntegrationComponent---viewmonitor--viewMonitorTable").then((element) => {
          cy.window().then((w) => {
            const uiTable = w.sap.ui.core.Element.closestTo(element[0]) as sap.ui.table.Table;
            const row = uiTable.getRows()[2];
            const column = row.getCells()[2];
            const item = column.getItems()[1].getItems()[0] as sap.m.ObjectAttribute;
            item.firePress();
          });
        });
        cy.get(
          "#shellMainContent---dataIntegrationComponent---viewmonitor--partiallyPersistedPopover--partiallyPersistedInfo"
        ).should("contain.text", "Input ParameterPRODUCT_PARAMValueTabletPersisted AtApr 29, 2024 7:00:26");
      });

      it("Check for partially persisted flow for View Monitor Details Page", () => {
        cy.intercept("GET", "**/logs?objectId=View1&getLocks=true", []).as("getPersistencyLogs");
        openViewMonitorDetailsPage("SPACE1234", "ParameterViewWithLocalTable");
        cy.wait("@getPersistedViewDetails");
        cy.wait("@getPersistencyLogs");
        cy.wait(200);
        cy.get("#taskLog--patiallyPersistedLink").should("contain.text", "Partially Persisted");
        cy.get("#taskLog--patiallyPersistedLink").then((element) => {
          cy.window().then((w) => {
            const elem = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.ObjectAttribute;
            elem.firePress();
          });
        });
        cy.get(
          "#shellMainContent---dataIntegrationComponent---taskLog--partiallyPersistedPopover--partiallyPersistedInfo"
        ).should("contain.text", "Input ParameterPRODUCT_PARAMValueTabletPersisted AtApr 29, 2024 7:00:26");
      });
    });
  });
});
/* jscpd:ignore-end */
