/** @format */

import { BuilderType, getAPIInstance } from "../../../pageobjects/api/IAPI";
import { APIImpl } from "../../../pageobjects/api/impl/APIImpl";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { ShellPages } from "../../../pageobjects/shell";
/* jscpd:ignore-start */
describe("Remote table Monitoring task scheduling with Cron String Support and new UI", () => {
  let api: APIImpl;
  const DI_PAGE = "shellMainContent---dataIntegrationComponent---viewmonitor";
  const TAB_ID = "shellMainContent---dataIntegrationComponent---dilandingpage--iconTabBar";
  const TAB_KEY = "viewMonitor";
  const TASKSCHEDULE_DIALOG = "viewTaskScheduler--taskScheduleDialog--view";
  const TASKSCHEDULE_DIALOG_ID = "TaskSchedulerDialog";
  const scheduleList = [
    {
      scheduleId: "8d0fcf26-3c55-4439-a9b0-40abd5282e97",
      spaceId: "MODELING_BLR",
      applicationId: "VIEWS",
      activity: "PERSIST",
      objectId: "Order_Details_Extends1",
      description: "Order_Details_Extends1",
      cron: "33 20 * * 0",
      validFrom: "2020-10-27T20:33:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "RICKY_ROY",
      createdAt: "2020-10-27T20:33:22.814Z",
      changedAt: "2020-10-27T20:33:22.876Z",
      nextRun: "2020-11-01T20:33:00.000Z",
    },
    {
      scheduleId: "8663963a-4d92-4150-9c1c-c29446c9466e",
      spaceId: "MODELING_BLR",
      applicationId: "VIEWS",
      activity: "PERSIST",
      objectId: "9030factView2",
      description: "9030factView2",
      cron: "37 20 * * 3",
      validFrom: "2020-10-27T20:37:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "RICKY_ROY",
      createdAt: "2020-10-27T20:37:17.084Z",
      changedAt: "2020-10-27T20:37:17.144Z",
      nextRun: "2020-11-04T20:37:00.000Z",
    },
  ];

  describe(`${Cypress.spec.relative} without SDP`, () => {
    beforeEach(async () => {
      api = getAPIInstance();
      const routes = [...XHelpers.DEFAULT_MOCK_REPONSES];
      XHelpers.modifyFeatureFlag(routes, "DWC_DUMMY_SPACE_PERMISSIONS", false);
      XHelpers.modifyFeatureFlag(routes, "DWCO_REUSABLE_TASK_SCHEDULING_UI", true);
      XHelpers.modifyFeatureFlag(routes, "DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION", true);
      XHelpers.modifyFeatureFlag(routes, "DWCO_DI_MONITOR_UI_IMPROVEMENTS", true);
      await api.setupBeforeEach(/*useMock*/ true, /*defaultFixture*/ true, routes);
      await api.mockServerCall(
        "GET",
        "**/remoteTables**",
        "fixture:remotetablemonitor/taskScheduleRemoteTables",
        "getRemoteTables"
      );
      await api.mockServerCall(
        "GET",
        "**/persistedViews**",
        "fixture:viewmonitor/taskScheduleViewMonitor.json",
        "getPersistedViews"
      );
      await api.mockServerCall("GET", "**/schedules?**", [], "getSchedules");
    });

    describe("{TaskSchedule} Check remote table monitoring task schedule flow with Cron String UI", () => {
      it("Check for weekly schedule", async () => {
        await api.mockServerCall("GET", "**/localTables**", { localTables: [] }, "getLocalTables");
        await api.mockServerCall("GET", "**/schedules/consent", { consent: false }, "getConsent");
        await api.mockServerCall(
          "POST",
          "**/schedules",
          { scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" },
          "createSchedule"
        );
        await api.mockServerCall("GET", "**/schedules?**", scheduleList, "getSchedules");
        await api.mockServerCall("GET", "**/schedules/**", scheduleList[0], "getSchedule");
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        await api.XHRWait("@getLocalTables");
        await api.selectIconTabBarItem(TAB_ID, TAB_KEY);
        await api.XHRWait("@getPersistedViews");
        await api.selectSegmentedButton("shellMainContent---dataIntegrationComponent---viewmonitor--ViewsBtn", 0, true);
        await api.wait(2);
        await api.selectTableRow(`${DI_PAGE}--viewMonitorTable`, 3, false, true);
        await api.clickMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 1);
        await api.assertText({ id: "TaskSchedulerDialog", value: "editScheduleTitleNeww(9030factView2)" });
        await api.assertText({ id: "TaskSchedulerDialog", value: "frequencyLabelNew" });
        await api.assertText({ id: "TaskSchedulerDialog", value: "enterAsTextNew" });
        await api.assertText({ id: "TaskSchedulerDialog", value: "repeatLabelNew" });
        await api.assertText({ id: "TaskSchedulerDialog", value: "nextRunsLabelForPopOver" });
        await api.assertText({ id: "TaskSchedulerDialog", value: "timeZoneLabel" });
      });
    });
  });
});
/* jscpd:ignore-end */
