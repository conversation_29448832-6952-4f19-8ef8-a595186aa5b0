/** @format */

import { selectTableRow } from "../../pageobjects/businessbuilder/BusinessbuilderHelper";
import {
  assertControl,
  openViewMonitorDetailsPage,
  openViewMonitorOverviewPage,
  prepareTestEnvironment,
  pressButton,
  selectSegmentedButton,
} from "../../pageobjects/dataIntegration/DataIntegrationHelper";

/* jscpd:ignore-start */
describe("View Persistency Task Log", () => {
  beforeEach(() => {
    prepareTestEnvironment({ DWC_DUMMY_SPACE_PERMISSIONS: false });
    cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });

    cy.intercept("GET", "**/schedules**", []).as("getSchedules");
    cy.intercept("GET", "**/resources/spaces?islocked&spaceids=SPACE1234**", { SPACE1234: { isLocked: false } }).as(
      "getSpaceLockStatus"
    );
    cy.intercept("GET", "**/persistedViews**", { fixture: "viewmonitor/persistedViewForTasklogDetails.json" }).as(
      "getPersistedViews"
    );
    cy.intercept("GET", "**/persistedViews/View**", { fixture: "viewmonitor/persistedView3Details" }).as(
      "getPersistedViewDetails"
    );
    cy.intercept("GET", "**/logs?objectId=View1&getLocks=true", { fixture: "viewmonitor/viewDetailLog.json" }).as(
      "getPersistencyLogs"
    );
    cy.intercept("GET", "**/logs?taskLogId=41", { fixture: "viewmonitor/viewDetailsLogMessages3.json" }).as(
      "getPersistencyLogMessages"
    );
  });

  describe("Open View Monitor", () => {
    it("Open View Monitor and close the Run Details panel", () => {
      openViewMonitorOverviewPage("SPACE1234");
      cy.wait("@getPersistedViews");
      selectSegmentedButton("shellMainContent---dataIntegrationComponent---viewmonitor--ViewsBtn", 0);
      selectTableRow("shellMainContent---dataIntegrationComponent---viewmonitor--viewMonitorTable", 0);
      cy.get("#shellMainContent---dataIntegrationComponent---viewmonitor--viewMonitorTable").then((element) => {
        cy.window().then((w) => {
          const table = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table | sap.ui.table.Table;
          table.getRows()[0].getRowAction().getItems()[0].firePress();
        });
      });
      cy.wait(["@getUser", "@getContractEnddate", "@getPersistedViewDetails"]);
      cy.get("#shellMainContent---dataIntegrationComponent---taskLog--flexDetailsPage-midColumn");
      pressButton("shellMainContent---dataIntegrationComponent---taskLog--closeMidColumn");
    });
  });

  describe("View Persistency Task Log - Filter, Sort and Search", () => {
    beforeEach(() => {
      openViewMonitorDetailsPage("SPACE1234", "View1");
      cy.wait(["@getUser", "@getContractEnddate", "@getPersistedViewDetails"]);
    });
    it("View Persistency Task Log - Filter", () => {
      pressButton("shellMainContent---dataIntegrationComponent---taskLog--messagesFilterSettingButton");
      assertControl("taskLog--messageFilter-filterlist");
      cy.get("ul#taskLog--messageFilter-filterlist-listUl").then((element) => {
        cy.window().then((w) => {
          const unorderedList = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.List;
          unorderedList.getItems()[1].firePress();
        });
      });
      cy.get("[id*= 'taskLog--messageFilter-page2-cont'] div:nth-child(2) ul").find("li").eq(1).click();
      cy.get("[id*= 'taskLog--messageFilter-page2-cont'] div:nth-child(2) ul").find("li").eq(2).click();
      pressButton("taskLog--messageFilter-acceptbutton");
    });

    it("View Persistency Task Log - Sort", () => {
      pressButton("shellMainContent---dataIntegrationComponent---taskLog--messagesSortSettingButton");
      cy.get("ul#taskLog--messageSort-sortorderlist-listUl").find("li").eq(2).click();
      cy.get("ul#taskLog--messageSort-sortlist-listUl").find("li").eq(3).click();
      pressButton("taskLog--messageSort-acceptbutton");
    });

    it("View Persistency Task Log - Search", () => {
      cy.get("#shellMainContent---dataIntegrationComponent---taskLog--messagesSearch").type("persist{enter}");
    });
  });
});
/* jscpd:ignore-end */
