/** @format */

import {
  openDISpaces,
  openViewMonitorOverviewPage,
  prepareTestEnvironment,
  pressButton,
  selectSegmentedButton,
  sortTableColumn,
} from "../../pageobjects/dataIntegration/DataIntegrationHelper";
/* jscpd:ignore-start */
describe("View Monitor - Basic", () => {
  const DI_PAGE = "shellMainContent---dataIntegrationComponent---viewmonitor";
  const SEARCH_FIELD = `${DI_PAGE}--searchTablesInput`;
  beforeEach(() => {
    prepareTestEnvironment({
      DWC_DUMMY_SPACE_PERMISSIONS: false,
    });

    cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });

    cy.intercept("GET", "**/persistedViews**", { fixture: "viewmonitor/allViews.json" }).as("getPersistedViews");
    cy.intercept("GET", "**/schedules?**", [
      {
        scheduleId: "9f7cb5c6-2327-4d6e-a4e0-c7cfcd41603c",
        spaceId: "HD_SPACE_02",
        applicationId: "VIEWS",
        objectId: "Employee_Dimension_PC_Hier",
        activity: "PERSIST",
        externalScheduleId: "08950b25-434f-4e85-bd08-2864767bdc1f",
        description: "Employee_Dimension_PC_Hier",
        validFrom: "2022-12-08T00:00:00.000Z",
        validTo: "2022-12-08T23:59:00.000Z",
        activationStatus: "ENABLED",
        createdBy: "D037121",
        createdAt: "2022-12-08T10:59:33.302Z",
        changedBy: "D037121",
        changedAt: "2022-12-08T10:59:33.646Z",
        owner: "D037121",
        uiVariant: "FORM",
        frequency: {
          interval: 1,
          type: "DAILY",
          startDate: "2022-12-08T12:00:00.000Z",
        },
      },
    ]).as("getSchedules");

    cy.intercept("GET", "**/resources/spaces?islocked&spaceids=SPACE1234**", { SPACE1234: { isLocked: false } }).as(
      "getSpaceLockStatus"
    );
  });

  describe("View Monitor", () => {
    beforeEach(() => {
      openDISpaces();
      openViewMonitorOverviewPage("SPACE1234");
      cy.wait(["@getUser", "@getContractEnddate", "@getPersistedViews"]);
      selectSegmentedButton("shellMainContent---dataIntegrationComponent---viewmonitor--ViewsBtn", 0);
    });
    it("filter check", () => {
      cy.get("#" + DI_PAGE + "--dataPersistencyColumn")
        .click({ force: true })
        .focused()
        .type("{downArrow}{downArrow}virtual{enter}");
      cy.get("#" + DI_PAGE + "--scheduledViewBtn-button").should("contain.text", "Scheduled (1)");
      cy.go(-1);
      openViewMonitorOverviewPage("SPACE12345");
      cy.wait(1000);
      cy.get("#" + DI_PAGE + "--dataPersistencyColumn")
        .click({ force: true })
        .focused()
        .type("{downArrow}{downArrow}")
        .should("contain.text", "");
    });
    it("Search check", () => {
      cy.get("#" + SEARCH_FIELD).type("View{enter}");
      cy.get("#" + DI_PAGE + "--scheduledViewBtn-button").should("contain.text", "Scheduled (1)");
      cy.go(-1);
      openViewMonitorOverviewPage("SPACE12345");
      cy.wait(3000);
      cy.get("#" + SEARCH_FIELD).should("contain.text", "");
    });
    it("Check if original number is displayed after search", () => {
      cy.get("#" + SEARCH_FIELD).type("View{enter}");
      pressButton(DI_PAGE + "--allViewsbtn-button");
      cy.get("#" + DI_PAGE + "--scheduledViewBtn-button").should("contain.text", "Scheduled (1)");
      cy.get("#" + DI_PAGE + "--persistedViewBtn-button").click();
      cy.get("#" + DI_PAGE + "--persistedViewBtn-button").should("contain.text", "Persisted (2)");
      cy.get("#" + DI_PAGE + "--allViewsbtn-button").click();
      cy.get("#" + DI_PAGE + "--allViewsbtn-button").should("contain.text", "All Views (4)");
    });
    it("Check if search is retained when we navigate to different segmented tabs", () => {
      cy.get("#" + SEARCH_FIELD).type("View{enter}");
      pressButton(DI_PAGE + "--allViewsbtn-button");
      cy.get("#" + DI_PAGE + "--viewMonitorTable tbody tr")
        .filter((index, row) => Cypress.$(row).text().trim() !== "")
        .should("have.length", 4);
      cy.get("#" + DI_PAGE + "--persistedViewBtn-button").click();
      cy.wait(1000);
      cy.get("#" + DI_PAGE + "--viewMonitorTable tbody tr")
        .filter((index, row) => Cypress.$(row).text().trim() !== "")
        .should("have.length", 2);
      cy.get("#" + DI_PAGE + "--allViewsbtn-button").click();
      cy.wait(1000);
      cy.get("#" + DI_PAGE + "--viewMonitorTable tbody tr")
        .filter((index, row) => Cypress.$(row).text().trim() !== "")
        .should("have.length", 4);
    });
    it("Sort order retention check", () => {
      sortTableColumn(DI_PAGE + "--dataPersistencyColumn", true);
      cy.get("#" + DI_PAGE + "--refeshTableButton").click();

      cy.get("#" + DI_PAGE + "--dataPersistencyColumn").then((element) => {
        cy.window().then((w) => {
          const sortOrder = (w.sap.ui.core.Element.closestTo(element[0]) as sap.ui.table.Column).getSortOrder();
          expect(sortOrder).to.equal("Descending");
        });
      });
    });
  });
});
/* jscpd:ignore-end */
