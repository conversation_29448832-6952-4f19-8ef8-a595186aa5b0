/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { BusinessBuilderPageActions } from "../../actions/business_builder/BusinessBuilderPageActions";
import { IAPI } from "../../pageobjects/api/IAPI";
import { XHelpers } from "../../pageobjects/api/impl/Helpers";
import { MockConfig, pressComponent, typeInInput } from "../../pageobjects/businessbuilder/BusinessbuilderHelper";
import { BB_BUTTON, BB_CONTROL, BB_DIALOG, BB_INPUT, BB_TABLE } from "../../pageobjects/businessbuilder/descriptor";

describe("cypress/integration/c4sbuilder/landingPageCreateFromTile", () => {
  let api: IAPI;

  beforeEach(() => {
    MockConfig.setupBeforeEach([...XHelpers.COMMON_MOCK_RESPONSES, ...XHelpers.BB_MOCK_RESPONSES]);
    cy.intercept("POST", MockConfig.buildC4SRoute("/c4s/internal_services/loadContent*"), {
      fixture: "c4sbuilder/LandingPage/packaged/loadContentRootNoFilter",
    }).as("loadContent");
    cy.visit("#/businessbuilder&/bb/SPACE1234/");
    cy.wait("@loadContent");
  });

  afterEach(() => MockConfig.reset());

  it("Trigger create authorization scenario from tile", () => {
    // 1. click tile for new authorization scenario
    // load mock content for responsibility scenario values
    cy.intercept("GET", MockConfig.buildC4SRoute("/c4s/internal_services/loadResponsibilityScenarioSelectionValues*"), {
      fixture: "c4sbuilder/authorizationScenario/loadResponsibilityScenarioSelectionValues.json",
    }).as("loadResponsibilityScenarioSelectionValues");

    pressComponent(BB_CONTROL.AUTH_TILE);
    cy.wait("@loadResponsibilityScenarioSelectionValues");

    // 2. Create New Authorization Scenario dialog: Check dialog is displayed
    cy.get(`[id=${BB_DIALOG.CREATE_AUTH}]`).should("exist");
  });

  it("Trigger create dimension from tile - Technical Name check", () => {
    // 1. click tile for new dimension
    // load mock content for datasource values
    cy.intercept("GET", MockConfig.buildC4SRoute("/c4s/internal_services/loadDataSourceSelectionValues*"), {
      fixture: "c4sbuilder/loadDataSourceSelection.json",
    }).as("loadDataSourceSelectionValues");
    pressComponent(BB_CONTROL.DIM_TILE);

    // 2. Create New Dimension dialog: Check dialog is displayed
    cy.get(`[id=${BB_DIALOG.CREATE_DIMENSION}]`).should("exist");
    // mock true unique name
    BusinessBuilderPageActions.mockUniqueNameCheck(true);

    // check technical name derivation
    typeInInput(BB_INPUT.SELECT_DATASOURCE_INPUT_BUSINESS_TITLE, "Entity.Name with more than 30 Characters", {
      clearInputBeforeTyping: false,
    });
    cy.wait("@checkUniqueTechnicalName");
    cy.byId(BB_INPUT.SELECT_DATASOURCE_INPUT_TECHNICAL_TITLE).then((inputName: sap.m.Input) => {
      cy.wrap({ getValue: () => inputName.getValue() })
        .invoke("getValue")
        .should("eq", "BE_Entity_Name_with_more_than_");
    });
    cy.byId(BB_INPUT.SELECT_DATASOURCE_INPUT_TECHNICAL_TITLE).then((inputName: sap.m.Input) => {
      cy.wrap({ getValue: () => inputName.getValueState() })
        .invoke("getValue")
        .should("eq", "None");
    });

    // clear inputs
    typeInInput(BB_INPUT.SELECT_DATASOURCE_INPUT_BUSINESS_TITLE, "{backspace}", { clearInputBeforeTyping: true });

    // mock true unique name
    BusinessBuilderPageActions.mockUniqueNameCheck(true);

    // Click on dummy with dot on its name
    cy.get(`[id=${BB_TABLE.CREATE_DIMENSION}]`).contains("dummy.dottest", { cyOriginal: true }).click().click();
    cy.get(`[id=${BB_BUTTON.DIMENSION_CREATE}]`).should("be.enabled");

    cy.byId(BB_INPUT.SELECT_DATASOURCE_INPUT_TECHNICAL_TITLE).then((inputName: sap.m.Input) => {
      cy.wrap({ getValue: () => inputName.getValue() })
        .invoke("getValue")
        .should("eq", "BE_dummy_dottest");
    });
    cy.byId(BB_INPUT.SELECT_DATASOURCE_INPUT_TECHNICAL_TITLE).then((inputName: sap.m.Input) => {
      cy.wrap({ getValue: () => inputName.getValueState() })
        .invoke("getValue")
        .should("eq", "None");
    });

    // mock false unique name
    BusinessBuilderPageActions.mockUniqueNameCheck(false);

    // 3. Type non-unique technical name
    typeInInput(BB_INPUT.SELECT_DATASOURCE_INPUT_TECHNICAL_TITLE, "NON_UNIQUE_NAME", { clearInputBeforeTyping: true });
    cy.wait("@checkUniqueTechnicalName");
    cy.byId(BB_INPUT.SELECT_DATASOURCE_INPUT_TECHNICAL_TITLE).then((inputName: sap.m.Input) => {
      cy.wrap({ getValue: () => inputName.getValueState() })
        .invoke("getValue")
        .should("eq", "Error");
    });

    // 4. Select Object
    cy.get(`[id=${BB_TABLE.CREATE_DIMENSION}]`).find("tr").eq(1).click();
    cy.get(`[id=${BB_BUTTON.DIMENSION_CREATE}]`).should("be.disabled");

    cy.byId(BB_INPUT.SELECT_DATASOURCE_INPUT_TECHNICAL_TITLE).then((inputName: sap.m.Input) => {
      cy.wrap({ getValue: () => inputName.getValueState() })
        .invoke("getValue")
        .should("eq", "Error");
    });

    // 5. Type invalid character
    BusinessBuilderPageActions.mockUniqueNameCheck(true);
    typeInInput(BB_INPUT.SELECT_DATASOURCE_INPUT_TECHNICAL_TITLE, "TESTNAME", { clearInputBeforeTyping: true });
    cy.wait("@checkUniqueTechnicalName");
    typeInInput(BB_INPUT.SELECT_DATASOURCE_INPUT_TECHNICAL_TITLE, ".", { clearInputBeforeTyping: false });
    cy.byId(BB_INPUT.SELECT_DATASOURCE_INPUT_TECHNICAL_TITLE).then((inputName: sap.m.Input) => {
      cy.wrap({ getValue: () => inputName.getValueState() })
        .invoke("getValue")
        .should("eq", "Error");
    });

    // 6. Type valid name
    typeInInput(BB_INPUT.SELECT_DATASOURCE_INPUT_TECHNICAL_TITLE, "TESTNAME", { clearInputBeforeTyping: true });
    cy.byId(BB_INPUT.SELECT_DATASOURCE_INPUT_TECHNICAL_TITLE).then((inputName: sap.m.Input) => {
      cy.wrap({ getValue: () => inputName.getValueState() })
        .invoke("getValue")
        .should("eq", "None");
    });
    cy.get(`[id=${BB_BUTTON.DIMENSION_CREATE}]`).should("be.enabled");
  });
});
