/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { BusinessBuilderPageActions } from "../../../actions/business_builder/BusinessBuilderPageActions";
import { SemanticSubPageActions } from "../../../actions/business_builder/subpages/SemanticSubPageActions";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { MockConfig } from "../../../pageobjects/businessbuilder/BusinessbuilderHelper";
import { BB_URL } from "../../../pageobjects/businessbuilder/descriptor";
import { generateSemanticRepresentations } from "../textAssociations/TextAssociationsHelper";

describe(Cypress.spec.relative, { labels: ["actions_module"] }, () => {
  beforeEach(() => {
    MockConfig.setupBeforeEach([...XHelpers.COMMON_MOCK_RESPONSES, ...XHelpers.BB_MOCK_DEPENDENCIES_RESPONSES]);
  });

  afterEach(() => {
    MockConfig.reset();
  });

  it("Create new perspective via Data Preview flow", function () {
    SemanticSubPageActions.openDataPreview({ spaceId: "SPACE1234", entityId: "17", entityVersion: "1" }); // Consumption_MODEL
    // Create perspective via preview
    const DataPreviewActions = SemanticSubPageActions.getDataPreviewTabActions();

    DataPreviewActions.getSavePerspectiveButton().getDOM().click();
    cy.intercept("POST", MockConfig.buildC4SRoute("/c4s/internal_services/v1/*/semantics/*"), {
      fixture: "c4sbuilder/ConsumptionModel/dependencies/getSemanticPreviewDataNewPerspective.json",
    }).as("getSemanticPreviewData");

    cy.fixture("c4sbuilder/ConsumptionModel/dependencies/loadSemanticSaveNewPerspective.json").then((model) => {
      const enrichedFixture = generateSemanticRepresentations(model);
      cy.intercept(
        "GET",
        MockConfig.buildC4SRoute("/c4s/internal_services/loadSemantic?SemanticID*"),
        enrichedFixture
      ).as("loadConsumptionModel");
    });

    BusinessBuilderPageActions.mockUniqueNameCheck(true);
    DataPreviewActions.getNewPerspectiveBusinessNameInput().doDOMType("New Unique name 2 12345678901234567890");
    cy.wait(1000); // hard wait as technical name entry has a delayed trigger of validation calls
    cy.wait("@checkUniqueTechnicalName");
    DataPreviewActions.getNewPerspectiveTechnicalNameInput()
      .getUI5Value()
      .should("eq", "PP_New_Unique_name_2_123456789");

    // check derived technical name

    // check technical name validation
    // - non-unique name
    BusinessBuilderPageActions.mockUniqueNameCheck(false);
    DataPreviewActions.getNewPerspectiveTechnicalNameInput().doDOMType("NON_UNIQUE_NAME");
    cy.wait(1000); // hard wait as technical name entry has a delayed trigger of validation calls
    cy.wait("@checkUniqueTechnicalName");
    DataPreviewActions.getNewPerspectiveTechnicalNameInput().getUI5ValueState().should("eq", "Error");
    DataPreviewActions.getNewPerspectiveSaveButton().getDOM().should("be.disabled");

    // - Type invalid character
    BusinessBuilderPageActions.mockUniqueNameCheck(false);
    DataPreviewActions.getNewPerspectiveTechnicalNameInput().doDOMType(".");
    cy.wait(1000); // hard wait as technical name entry has a delayed trigger of validation calls
    // no check on backend call as invalid characters are already checked by name validator on the UI itself
    DataPreviewActions.getNewPerspectiveTechnicalNameInput().getUI5ValueState().should("eq", "Error");
    DataPreviewActions.getNewPerspectiveSaveButton().getDOM().should("be.disabled");

    // - Type invalid name (whitespaces)
    BusinessBuilderPageActions.mockUniqueNameCheck(false);
    DataPreviewActions.getNewPerspectiveTechnicalNameInput().doDOMType("New Unique name 2");
    cy.wait(1000); // hard wait as technical name entry has a delayed trigger of validation calls
    // no check on backend call as invalid characters are already checked by name validator on the UI itself
    DataPreviewActions.getNewPerspectiveTechnicalNameInput().getUI5ValueState().should("eq", "Error");

    // - Type valid name
    BusinessBuilderPageActions.mockUniqueNameCheck(true);
    DataPreviewActions.getNewPerspectiveTechnicalNameInput().doDOMType("New_Unique_name_2");
    cy.wait(1000); // hard wait as technical name entry has a delayed trigger of validation calls
    cy.wait("@checkUniqueTechnicalName");
    DataPreviewActions.getNewPerspectiveTechnicalNameInput().getUI5ValueState().should("eq", "None");
    DataPreviewActions.getNewPerspectiveSaveButton().getDOM().should("be.enabled");

    DataPreviewActions.getNewPerspectiveSaveButton().getDOM().click();
    cy.wait("@cudSemantic");
    cy.wait("@loadConsumptionModel");
    cy.get(".sapMMessageToast").should("contain", "modelSavedSuccessful");
    cy.url().should("contain", BB_URL.BASE + "/model/17/version/1/preview/29"); // Consumption_MODEL + new_perspective
    cy.get("@getSemanticPreviewData");

    // assert we have perspective buttons
    DataPreviewActions.getPerspectiveSaveButton().getDOM().should("be.disabled");
  });

  it("Create new perspective via perspective add button", function () {
    SemanticSubPageActions.openPerspectivesTab({ spaceId: "SPACE1234", entityId: "17", entityVersion: "1" });

    const PerspectivesTabActions = SemanticSubPageActions.getPerspectivesTabActions();

    // create new perspective
    PerspectivesTabActions.clickAddPerspectiveButton();
    cy.url().should("contain", BB_URL.BASE + "/model/17/version/1/perspectives/create"); // Consumption_MODEL

    BusinessBuilderPageActions.mockUniqueNameCheck(true);
    PerspectivesTabActions.getBusinessNameInput().doDOMType("New perspective 12345678901234567890");
    PerspectivesTabActions.getDescriptionInput().doDOMType("Some Business Description");
    PerspectivesTabActions.getPurposeInput().doDOMType("Some Business Purpose");
    PerspectivesTabActions.getContactInput().doDOMType("Max Mustermann");
    PerspectivesTabActions.getResponsibleTeamInput().doDOMType("DWC");
    PerspectivesTabActions.getTagsInput().doDOMType("Sales");
    cy.wait(1000); // hard wait as technical name entry has a delayed trigger of validation calls
    cy.wait("@checkUniqueTechnicalName");
    // check derived technical name
    PerspectivesTabActions.getTechnicalNameInput().getUI5Value().should("eq", "PP_New_perspective_12345678901");

    // check technical name validation
    const getTechnicalNameInput = () =>
      cy.get(
        "#shellMainContent---c4sbuilderComponent---semanticQuery--perspectiveTitleSectionId--technicalNameInput-message-text"
      );

    // - non-unique name
    BusinessBuilderPageActions.mockUniqueNameCheck(false);
    PerspectivesTabActions.getTechnicalNameInput().doDOMType("NON_UNIQUE_NAME");
    cy.wait(1000); // hard wait as technical name entry has a delayed trigger of validation calls
    cy.wait("@checkUniqueTechnicalName");
    PerspectivesTabActions.getTechnicalNameInput().getDOM().click();
    getTechnicalNameInput().contains("identifier_not_unique", { cyOriginal: true });
    PerspectivesTabActions.getSaveButton().getDOM().should("be.disabled");

    // - Type invalid character
    BusinessBuilderPageActions.mockUniqueNameCheck(true);
    PerspectivesTabActions.getTechnicalNameInput().doDOMType(".");
    cy.wait(1000); // hard wait as technical name entry has a delayed trigger of validation calls
    // no check on backend call as invalid characters are already checked by name validator on the UI itself
    getTechnicalNameInput().contains("nameValidationInvalidCharacter", { cyOriginal: true });
    PerspectivesTabActions.getSaveButton().getDOM().should("be.disabled");

    // - Type invalid name (whitespaces)
    BusinessBuilderPageActions.mockUniqueNameCheck(false);
    PerspectivesTabActions.getTechnicalNameInput().doDOMType("New perspective");
    cy.wait(1000); // hard wait as technical name entry has a delayed trigger of validation calls
    // no check on backend call as invalid characters are already checked by name validator on the UI itself
    getTechnicalNameInput().contains("nameValidationInvalidCharacter", { cyOriginal: true });

    // - Type valid name
    BusinessBuilderPageActions.mockUniqueNameCheck(true);
    PerspectivesTabActions.getTechnicalNameInput().doDOMType("New_perspective");
    cy.wait(1000); // hard wait as technical name entry has a delayed trigger of validation calls
    cy.wait("@checkUniqueTechnicalName");
    PerspectivesTabActions.getTechnicalNameInput().getUI5ValueState().should("eq", "None");

    cy.fixture("c4sbuilder/ConsumptionModel/dependencies/loadSemanticSaveNewPerspective.json").then((model) => {
      const enrichedFixture = generateSemanticRepresentations(model);
      cy.intercept(
        "GET",
        MockConfig.buildC4SRoute("/c4s/internal_services/loadSemantic?SemanticID*"),
        enrichedFixture
      ).as("loadConsumptionModel");
    });

    PerspectivesTabActions.getSaveButton().getDOM().should("be.enabled");
    PerspectivesTabActions.getSaveButton().getDOM().click();
    cy.wait("@cudSemantic");
    cy.wait("@loadConsumptionModel");
    PerspectivesTabActions.getMessageToast().should("contain", "modelSavedSuccessful");
    cy.url().should("contain", BB_URL.BASE + "/model/17/version/1/perspectives/edit/29"); // Consumption_MODEL + new_perspective
  });
});
