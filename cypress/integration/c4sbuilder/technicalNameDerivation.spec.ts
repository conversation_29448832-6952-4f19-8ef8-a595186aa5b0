/**
 * Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { XHelpers } from "../../pageobjects/api/impl/Helpers";
import {
  MockConfig,
  openBusinessBuilderLandingPage,
  openBusinessEntity,
  openSemantic,
  pressComponent,
  TabElements,
  typeInInput,
} from "../../pageobjects/businessbuilder/BusinessbuilderHelper";
import {
  BB_BUTTON,
  BB_CONTROL,
  BB_DIALOG,
  BB_INPUT,
  BB_LIST,
  BB_TABLE,
  BB_URL,
  BB_VIEW,
} from "../../pageobjects/businessbuilder/descriptor";

describe(Cypress.spec.relative, () => {
  function prepareFactModelMocks() {
    // ===== Declaration of mocked backend calls =====
    // Mock backend calls: load semantic title
    cy.intercept("GET", MockConfig.buildC4SRoute("/c4s/internal_services/loadGlobalModels*"), {
      fixture: "c4sbuilder/loadGlobalModels",
    }).as("loadGlobalModels");
    cy.intercept("GET", MockConfig.buildC4SRoute("/c4s/internal_services/loadSemanticTitle?*"), {
      fixture: "c4sbuilder/modelGeneration/loadSemanticTitle",
    }).as("loadSemanticTitle");
    // Mock backend call: Load fact model
    cy.intercept("GET", MockConfig.buildC4SRoute("/c4s/internal_services/loadSemantic?SemanticID=99*"), {
      fixture: "c4sbuilder/modelGeneration/loadFactModel",
    }).as("loadSemantic");
    // Mock backend call: Load data preview data
    cy.intercept("POST", MockConfig.buildC4SRoute("/c4s/internal_services/v1/*/semantics/1452"), {
      fixture: "c4sbuilder/modelGeneration/loadPreviewDataFM",
    }).as("loadPreviewDataFM");
  }

  afterEach(() => MockConfig.reset());

  describe(`${Cypress.spec.relative} with SDP`, () => {
    beforeEach(() => {
      MockConfig.setupBeforeEach(
        [
          ...XHelpers.COMMON_MOCK_RESPONSES,
          ...XHelpers.BB_COMMON_MOCK_RESPONSES,
          ...XHelpers.BB_MOCK_RESPONSES,
          ...XHelpers.BB_MOCK_DEPENDENCIES_RESPONSES,
          ...XHelpers.BB_BUSINESS_ENTITY_MOCK_RESPONSES,
          ...XHelpers.SDP_SPECIFIC_MOCK_RESPONSES,
        ],
        [],
        [{ name: "DWC_DUMMY_SPACE_PERMISSIONS", isEnabled: true }]
      );
      cy.intercept("POST", MockConfig.buildC4SRoute("/c4s/internal_services/SPACE1234/loadContent*"), {
        fixture: "c4sbuilder/loadContent6657358.json",
      }).as("loadContent");
      cy.intercept("GET", MockConfig.buildC4SRoute("/c4s/internal_services/SPACE1234/loadEditablePackages**"), {
        fixture: "c4sbuilder/LandingPage/loadEditablePackages",
      }).as("loadEditablePackagesSDP");
      cy.intercept("GET", MockConfig.buildC4SRoute("/c4s/internal_services/checkUniqueTechnicalName?*"), {
        isUnique: true,
      }).as("checkUniqueTechnicalName");

      openBusinessBuilderLandingPage("SPACE1234");
      cy.wait("@loadContent");
    });

    it("Save As - Technical Name derivation with Space Dependent Permissions FF ON", () => {
      openBusinessEntity(
        {
          spaceId: BB_URL.SPACE,
          entityId: "58",
          entityVersion: "1",
        },
        TabElements.GENERAL
      );

      // Click Save As Button
      cy.get(`[id=${BB_BUTTON.DATA_SOURCE_SAVE_AS}]`).should("be.enabled");
      pressComponent(BB_BUTTON.DATA_SOURCE_SAVE_AS);
      cy.wait("@loadEditablePackagesSDP");

      // Open Save As Dialog
      cy.get(`[id=${BB_VIEW.DATASOURCE_SAVE_AS}]`).should("be.visible");
      cy.get(`[id=${BB_INPUT.DATASOURCE_SAVE_AS_NAME}]`).should("be.visible");
      cy.get(`[id=${BB_INPUT.DATASOURCE_SAVE_AS_NAME}]`).should("be.enabled");
      cy.get(`[id=${BB_INPUT.DATASOURCE_SAVE_AS_TECHNICAL_NAME}]`).should("be.visible");
      cy.get(`[id=${BB_INPUT.DATASOURCE_SAVE_AS_TECHNICAL_NAME}]`).should("be.enabled");

      // Derive Technical Title
      cy.get(`[id=${BB_VIEW.DATASOURCE_SAVE_AS}--applyBtn]`).should("be.disabled");
      cy.get(`[id=${BB_INPUT.DATASOURCE_SAVE_AS_NAME}]`).type("TEST.BUSINESS NAME", { force: true });
      cy.wait("@checkUniqueTechnicalNameSDP");
      cy.get(`[id=${BB_INPUT.DATASOURCE_SAVE_AS_TECHNICAL_NAME}]`).should("have.value", "BE_TEST_BUSINESS_NAME");
      cy.get(`[id=${BB_VIEW.DATASOURCE_SAVE_AS}--applyBtn]`).should("be.enabled").click();
      cy.wait("@saveAsDataSourceSDP");
    });
  });

  describe(`${Cypress.spec.relative} without SDP`, () => {
    beforeEach(() => {
      MockConfig.setupBeforeEach(
        [
          ...XHelpers.COMMON_MOCK_RESPONSES,
          ...XHelpers.BB_COMMON_MOCK_RESPONSES,
          ...XHelpers.BB_MOCK_RESPONSES,
          ...XHelpers.BB_MOCK_DEPENDENCIES_RESPONSES,
          ...XHelpers.BB_BUSINESS_ENTITY_MOCK_RESPONSES,
        ],
        [{ name: "DWC_DUMMY_SPACE_PERMISSIONS", isEnabled: false }]
      );
      cy.intercept("POST", MockConfig.buildC4SRoute("/c4s/internal_services/loadContent*"), {
        fixture: "c4sbuilder/loadContent6657358.json",
      }).as("loadContent");
      cy.intercept("GET", MockConfig.buildC4SRoute("/c4s/internal_services/loadEditablePackages**"), {
        fixture: "c4sbuilder/LandingPage/loadEditablePackages",
      }).as("loadEditablePackages");
      cy.intercept("GET", MockConfig.buildC4SRoute("/c4s/internal_services/checkUniqueTechnicalName?*"), {
        isUnique: true,
      }).as("checkUniqueTechnicalName");

      openBusinessBuilderLandingPage("SPACE1234");
      cy.wait("@loadContent");
    });

    it("Create dimension from tile - Technical Name derivation", () => {
      // load mock content for datasource values
      cy.intercept("GET", MockConfig.buildC4SRoute("/c4s/internal_services/loadDataSourceSelectionValues*"), {
        fixture: "c4sbuilder/loadDataSourceSelection.json",
      }).as("loadDataSourceSelectionValues");

      // click tile for new dimension
      pressComponent(BB_CONTROL.DIM_TILE);
      cy.wait("@loadDataSourceSelectionValues");

      typeInInput(BB_INPUT.SELECT_DATASOURCE_INPUT_BUSINESS_TITLE, "TEST.BUSINESS NAME");
      cy.wait("@checkUniqueTechnicalName");
      cy.get(`[id=${BB_INPUT.SELECT_DATASOURCE_INPUT_TECHNICAL_TITLE}-inner]`).should(
        "have.value",
        "BE_TEST_BUSINESS_NAME"
      );
    });

    it("Perspective Page - Technical Name derivation", () => {
      openSemantic(
        {
          spaceId: BB_URL.SPACE,
          entityId: "17",
          entityVersion: "1",
        },
        TabElements.PERSPECTIVES
      );

      // create new perspective
      pressComponent(BB_BUTTON.CONSUNMPTION_MODEL_PERSPECTIVE_ADD);
      cy.url().should("contain", BB_URL.BASE + "/model/17/version/1/perspectives/create");

      // Type business name and check derived technical name
      typeInInput(BB_INPUT.CONSUMPTION_MODEL_INPUT_QUERY_TITLE, "TEST.BUSINESS NAME");
      cy.wait("@checkUniqueTechnicalName");
      cy.get(`[id=${BB_INPUT.CONSUMPTION_MODEL_INPUT_QUERY_TECHNICAL_TITLE}-inner]`).should(
        "have.value",
        "PP_TEST_BUSINESS_NAME"
      );
    });

    it("Data Preview Save New - Technical Name derivation", () => {
      openSemantic(
        {
          spaceId: BB_URL.SPACE,
          entityId: "17",
          entityVersion: "1",
        },
        TabElements.PERSPECTIVES
      );

      // Create perspective via preview
      pressComponent(BB_BUTTON.CONSUMPTION_MODEL_PREVIEW);
      cy.wait("@getSemanticPreviewData");
      cy.url().should("contain", BB_URL.BASE + "/model/17/version/1/preview");

      pressComponent(BB_BUTTON.CM_PERSPECTIVE_SAVE_NEW);

      cy.intercept("POST", MockConfig.buildC4SRoute("/c4s/internal_services/v1/*/semantics/*"), {
        fixture: "c4sbuilder/ConsumptionModel/dependencies/getSemanticPreviewDataNewPerspective.json",
      }).as("getSemanticPreviewData");
      cy.intercept("GET", MockConfig.buildC4SRoute("/c4s/internal_services/loadSemantic*"), {
        fixture: "c4sbuilder/ConsumptionModel/dependencies/loadSemanticSaveNewPerspective.json",
      }).as("loadConsumptionModel");

      cy.get(`[id=${BB_INPUT.CM_PERSPECTIVE_TITLE}-inner]`).type("TEST.BUSINESS NAME", { force: true });

      cy.wait("@checkUniqueTechnicalName");
      cy.get(`[id=${BB_INPUT.CM_PERSPECTIVE_TECHNICAL_TITLE}-inner]`).should("have.value", "PP_TEST_BUSINESS_NAME");
    });

    it("Create CM from tile - Technical Name derivation", () => {
      // click tile for new CM
      pressComponent(BB_CONTROL.CONSUMPTION_TILE);

      cy.get(`[id=${BB_DIALOG.CREATE_CONSUMPTION_MODEL}]`).should("be.visible");
      cy.get(`[id=${BB_INPUT.CONSUMPTION_MODEL_DIALOG_INPUT_TITLE}-inner]`).should("be.enabled");
      cy.get(`[id=${BB_INPUT.CONSUMPTION_MODEL_DIALOG_INPUT_TITLE}-inner]`).should("be.visible");
      cy.get(`[id=${BB_INPUT.CONSUMPTION_MODEL_DIALOG_INPUT_TITLE}-inner]`).type("TEST.BUSINESS NAME", { force: true });
      cy.wait("@checkUniqueTechnicalName");
      cy.get(`[id=${BB_INPUT.CONSUMPTION_MODEL_DIALOG_TECHNICAL_TITLE}-inner]`).should(
        "have.value",
        "CM_TEST_BUSINESS_NAME"
      );
      cy.get(`[id=${BB_BUTTON.CONSUMPTION_MODEL_DIALOG_STEP2}]`).should("be.visible");
      cy.get(`[id=${BB_BUTTON.CONSUMPTION_MODEL_DIALOG_STEP2}]`).should("be.enabled");
    });

    it("Save As - Technical Name derivation", () => {
      openBusinessEntity(
        {
          spaceId: BB_URL.SPACE,
          entityId: "58",
          entityVersion: "1",
        },
        TabElements.GENERAL
      );

      // Click Save As Button
      cy.get(`[id=${BB_BUTTON.DATA_SOURCE_SAVE_AS}]`).should("be.enabled");
      pressComponent(BB_BUTTON.DATA_SOURCE_SAVE_AS);
      cy.wait("@loadEditablePackages");

      // Open Save As Dialog
      cy.get(`[id=${BB_VIEW.DATASOURCE_SAVE_AS}]`).should("be.visible");
      cy.get(`[id=${BB_INPUT.DATASOURCE_SAVE_AS_NAME}]`).should("be.visible");
      cy.get(`[id=${BB_INPUT.DATASOURCE_SAVE_AS_NAME}]`).should("be.enabled");
      cy.get(`[id=${BB_INPUT.DATASOURCE_SAVE_AS_TECHNICAL_NAME}]`).should("be.visible");
      cy.get(`[id=${BB_INPUT.DATASOURCE_SAVE_AS_TECHNICAL_NAME}]`).should("be.enabled");

      // Derive Technical Title
      cy.get(`[id=${BB_VIEW.DATASOURCE_SAVE_AS}--applyBtn]`).should("be.disabled");
      cy.get(`[id=${BB_INPUT.DATASOURCE_SAVE_AS_NAME}]`).type("TEST.BUSINESS NAME", { force: true });
      cy.wait("@checkUniqueTechnicalName");
      cy.get(`[id=${BB_INPUT.DATASOURCE_SAVE_AS_TECHNICAL_NAME}]`).should("have.value", "BE_TEST_BUSINESS_NAME");
      cy.get(`[id=${BB_VIEW.DATASOURCE_SAVE_AS}--applyBtn]`).should("be.enabled").click();
      cy.wait("@saveAsDataSource");
    });

    it("Copy Semantic from Landing Page - Technical Name derivation", () => {
      // Open Copy Model Dialog
      cy.get(`#${BB_TABLE.ENTITIES}-rowsel0`).click();
      pressComponent(BB_BUTTON.L_COPY);
      cy.get(`[id=${BB_INPUT.C_ALIAS}-inner]`).should("be.visible");
      cy.get(`[id=${BB_INPUT.C_ALIAS}-inner]`).should("be.enabled");
      cy.get(`[id=${BB_INPUT.C_TECHNICAL_NAME}-inner]`).should("be.visible");
      cy.get(`[id=${BB_INPUT.C_TECHNICAL_NAME}-inner]`).should("be.enabled");

      // derive Technical Title
      cy.get(`[id=${BB_DIALOG.COPY}--applyBtn]`).should("be.disabled");
      cy.get(`[id=${BB_INPUT.C_ALIAS}-inner]`).type("TEST.BUSINESS NAME", { force: true });
      cy.wait("@checkUniqueTechnicalName");
      cy.get(`[id=${BB_INPUT.C_TECHNICAL_NAME}-inner]`).should("have.value", "CM_Semantic_BW4HCPR_EXAMPLE_FM");
      cy.get(`[id=${BB_DIALOG.COPY}--applyBtn]`).should("be.enabled");
    });

    it("Transfer Sources to Fact Model - Technical Name derivation", () => {
      openSemantic(
        {
          spaceId: "SPACE1234",
          entityId: 23,
          entityVersion: 1,
        },
        TabElements.SOURCE_MODEL
      );

      // open Transfer to Fact Model Dialog
      pressComponent(BB_BUTTON.CONSUMPTION_MODEL_TRANSFER_INTO_FACT_MODEL);
      cy.get(`[id=${BB_LIST.CONSUMPTION_MODEL_TRANSFER_TO_KPI_MODEL_SOURCESELECTION}]`).find("li").eq(0).click();
      pressComponent(BB_BUTTON.CONSUMPTION_MODEL_TRANSFER_INTO_FACT_MODEL_SOURCESELECTION_NEXT);
      cy.get(`[id=${BB_LIST.CONSUMPTION_MODEL_TRANSFER_TO_KPI_MODEL_MEASURESELECTION}]`).find("li").eq(0).click();
      pressComponent(BB_BUTTON.CONSUMPTION_MODEL_TRANSFER_INTO_FACT_MODEL_MEASURESELECTION_NEXT);

      // derive Technical Title
      cy.get(`[id=${BB_INPUT.CONSUMPTION_MODEL_TRANSFER_TO_KPI_MODEL_KPI_TITLE}]`).type("TEST.BUSINESS NAME");
      cy.get(`[id=${BB_INPUT.CONSUMPTION_MODEL_TRANSFER_TO_KPI_MODEL_KPI_TECHNICAL_TITLE}]`).should(
        "have.value",
        "FM_TEST_BUSINESS_NAME"
      );
      cy.wait("@checkUniqueTechnicalName");
    });

    it("Data Preview Save As CM - Technical Name derivation", () => {
      prepareFactModelMocks();

      openSemantic(
        {
          spaceId: BB_URL.SPACE,
          entityId: "99",
          entityVersion: "1",
        },
        TabElements.PREVIEW
      );

      cy.wait("@loadSemanticTitle");
      cy.wait("@loadGlobalModels");
      cy.wait("@loadSemantic");
      cy.wait("@loadPreviewDataFM");

      cy.get(`[id=${BB_BUTTON.FACT_MODEL_PREVIEW_SAVE_AS_CONSUMPTION_MODEL}]`).should("be.enabled");
      pressComponent(BB_BUTTON.FACT_MODEL_PREVIEW_SAVE_AS_CONSUMPTION_MODEL);
      cy.wait("@loadEditablePackages");

      // wait for dialog
      cy.get(`[id=${BB_INPUT.FM_PREVIEW_AS_CONSUMPTION_MODEL_TITLE}-inner]`).should("be.visible");
      cy.get(`[id=${BB_INPUT.FM_PREVIEW_AS_CONSUMPTION_MODEL_TITLE}-inner]`).should("be.enabled");
      cy.get(`[id=${BB_INPUT.FM_PREVIEW_AS_CONSUMPTION_MODEL_TECHNICAL_TITLE}-inner]`).should("be.visible");
      cy.get(`[id=${BB_INPUT.FM_PREVIEW_AS_CONSUMPTION_MODEL_TECHNICAL_TITLE}-inner]`).should("be.enabled");
      cy.get(`[id=${BB_INPUT.FM_PREVIEW_AS_CONSUMPTION_MODEL_PERSPECTIVE_TITLE}-inner]`).should("be.visible");
      cy.get(`[id=${BB_INPUT.FM_PREVIEW_AS_CONSUMPTION_MODEL_PERSPECTIVE_TITLE}-inner]`).should("be.enabled");
      cy.get(`[id=${BB_INPUT.FM_PREVIEW_AS_CONSUMPTION_MODEL_PERSPECTIVE_TECHNICAL_TITLE}-inner]`).should("be.visible");
      cy.get(`[id=${BB_INPUT.FM_PREVIEW_AS_CONSUMPTION_MODEL_PERSPECTIVE_TECHNICAL_TITLE}-inner]`).should("be.enabled");

      // derive CM technical title
      cy.get(`[id=${BB_BUTTON.FACT_MODEL_PREVIEW_AS_CONSUMPTION_MODEL_SAVE}]`).should("be.disabled");
      cy.get(`[id=${BB_INPUT.FM_PREVIEW_AS_CONSUMPTION_MODEL_TITLE}-inner]`).type("TEST.BUSINESS NAME", {
        force: true,
      });
      cy.wait("@checkUniqueTechnicalName");
      cy.get(`[id=${BB_INPUT.FM_PREVIEW_AS_CONSUMPTION_MODEL_TECHNICAL_TITLE}-inner]`).should(
        "have.value",
        "CM_TEST_BUSINESS_NAME"
      );
      cy.get(`[id=${BB_BUTTON.FACT_MODEL_PREVIEW_AS_CONSUMPTION_MODEL_SAVE}]`).should("be.disabled");

      // derive Perspective technical title
      cy.get(`[id=${BB_INPUT.FM_PREVIEW_AS_CONSUMPTION_MODEL_PERSPECTIVE_TITLE}-inner]`).type("TEST.BUSINESS NAME", {
        force: true,
      });
      cy.wait("@checkUniqueTechnicalName");
      cy.get(`[id=${BB_INPUT.FM_PREVIEW_AS_CONSUMPTION_MODEL_PERSPECTIVE_TECHNICAL_TITLE}-inner]`).should(
        "have.value",
        "PP_TEST_BUSINESS_NAME"
      );
      cy.get(`[id=${BB_BUTTON.FACT_MODEL_PREVIEW_AS_CONSUMPTION_MODEL_SAVE}]`).should("be.enabled");
    });
  });
});
