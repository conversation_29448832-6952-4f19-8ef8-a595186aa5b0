/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { TabElements } from "../../../../../pageobjects/businessbuilder/BusinessbuilderHelper";
import { assertComponentsList, assertUI5ComponentsList, suiteParameters } from "../../utils";
import { BusinessEntityMeasureTest } from "./BusinessEntityMeasureTest";

export class BusinessEntityMeasureCalculatedTest extends BusinessEntityMeasureTest {
  protected getSuiteFunction() {
    return () => {
      it("Calculated Measure", () => {
        cy.get("#shellMainContent---c4sbuilderComponent---dataSourceMeasures--table-rows-row1-col0").click();
        cy.wait("@loadDataSourceSDP");

        this.getInputTitleExpectedStates();

        assertComponentsList(this.suiteParameter.existence, [
          "shellMainContent---c4sbuilderComponent---dataSourceMeasure--saveButton",
          "shellMainContent---c4sbuilderComponent---dataSourceMeasure--cancelButton",
        ]);

        assertUI5ComponentsList("getEnabled", this.suiteParameter.boolean, [
          "shellMainContent---c4sbuilderComponent---dataSourceMeasure--selectSource",
          "shellMainContent---c4sbuilderComponent---dataSourceMeasure--selectTypeExceptionAggregationId",
          "shellMainContent---c4sbuilderComponent---dataSourceMeasure--switchIsAuxiliary",
          "shellMainContent---c4sbuilderComponent---dataSourceMeasure--calculator",
          "shellMainContent---c4sbuilderComponent---dataSourceMeasure--switchReplaceNullWithZero",
        ]);
      });
    };
  }
}

suiteParameters.forEach((current) => {
  describe(
    `${Cypress.spec.relative} - isEditor: ${current.isEditor}`,
    new BusinessEntityMeasureCalculatedTest(current, TabElements.MEASURES).getSuite()
  );
});
