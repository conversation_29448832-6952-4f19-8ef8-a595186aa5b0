/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { TabElements } from "../../../../pageobjects/businessbuilder/BusinessbuilderHelper";
import { assertComponentsList, assertDeleteButton, assertUI5ComponentsList, suiteParameters } from "../utils";
import { BusinessEntityTest } from "./BusinessEntityTest";

export class BusinessEntityAuthorizationScenarioTest extends BusinessEntityTest {
  protected getSuiteFunction() {
    return () => {
      it("Authorization Scenarios", () => {
        cy.get("#shellMainContent---c4sbuilderComponent---dataSourceResponsibilityScenarios--addButton").should(
          this.suiteParameter.existence
        );
        assertDeleteButton("dataSourceResponsibilityScenarios", this.suiteParameter);

        cy.get(
          "#shellMainContent---c4sbuilderComponent---dataSourceResponsibilityScenarios--table-rows-row0-col0"
        ).click();
        cy.wait("@loadDataSourceSDP");

        assertComponentsList(this.suiteParameter.existence, [
          "shellMainContent---c4sbuilderComponent---dataSourceResponsibilityScenario--saveButton",
          "shellMainContent---c4sbuilderComponent---dataSourceResponsibilityScenario--cancelButton",
        ]);

        assertUI5ComponentsList("getEnabled", "be.false", [
          // should be false, regardless if Modeler or Viewer, as changing this field shouldn't be possible either way
          "shellMainContent---c4sbuilderComponent---dataSourceResponsibilityScenario--selectAuthScenarioId",
        ]);

        assertUI5ComponentsList("getEnabled", this.suiteParameter.boolean, [
          '[id^="shellMainContent---c4sbuilderComponent---dataSourceResponsibilityScenario--selectRestrictionContextId"]',
        ]);
      });
    };
  }
}

suiteParameters.forEach((current) => {
  describe(
    `${Cypress.spec.relative} - isEditor: ${current.isEditor}`,
    new BusinessEntityAuthorizationScenarioTest(current, TabElements.AUTHORIZATION_SCENARIOS).getSuite()
  );
});
