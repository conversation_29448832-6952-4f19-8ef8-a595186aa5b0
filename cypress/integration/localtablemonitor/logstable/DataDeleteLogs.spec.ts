/** @format */
// FILEOWNER: [local-table-administration]

import {
  assertMTableRowCount,
  openLocalTableMonitorDetailsPage,
  prepareTestEnvironment,
} from "../../../pageobjects/dataIntegration/DataIntegrationHelper";
describe("Local Table Object Page", () => {
  beforeEach(() => {
    cy.intercept("GET", "**/resources/spaces?islocked&spaceids=SPACE1234**", { SPACE1234: { isLocked: false } }).as(
      "getSpaceLockStatus"
    );

    cy.intercept("GET", "**/SPACE1234/localTables/abc/schedules", {
      fixture: "localtablemonitor/schedulesEditBindings.json",
    }).as("getSchedule");

    // user privilege
    cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });

    // data delete logs mock
    cy.intercept("GET", "**/monitor/SPACE1234/localtables/abc/deletionlogs**", {
      fixture: "localtablemonitor/logs.json",
    }).as("getLogs");

    // single table metrics call mock
    cy.intercept("GET", "**/SPACE1234/localTables/abc?includeBusinessNames=true", {
      fixture: "localtablemonitor/tables.json",
    }).as("getMetrics");
  });

  describe("Navigate to local table object page", () => {
    beforeEach(() => {
      prepareTestEnvironment({
        DWC_DUMMY_SPACE_PERMISSIONS: false,
      });
      openLocalTableMonitorDetailsPage("SPACE1234", "abc");
      cy.wait(["@getMetrics", "@getLogs"]);
    });

    it("Check logs table", function () {
      assertMTableRowCount(
        "shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteLogTableView--dataDeleteLogTableId",
        24
      );

      cy.get("#shellMainContent---dataIntegrationComponent---localTableObjectDetails--refreshMetricsBtnId").click();

      cy.get(
        "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteLogTableView--deleteLogSearchId-I"
      ).type("DWC");

      assertMTableRowCount(
        "shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteLogTableView--dataDeleteLogTableId",
        1
      );

      cy.get(
        "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteLogTableView--deleteLogSearchId-reset"
      ).click();

      cy.get(
        "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteLogTableView--dataDeleteLogsSortBtn"
      ).click();
      cy.get(
        "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteLogSort-acceptbutton"
      ).click();

      cy.get(
        "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteLogTableView--dataDeleteLogsFilterBtn"
      ).click();

      cy.get(
        "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteLogTableFilter-acceptbutton"
      ).click();
    });
  });

  describe("Replace user column with Run started by", () => {
    beforeEach(() => {
      prepareTestEnvironment({
        DWC_DUMMY_SPACE_PERMISSIONS: false,
        DWCO_LTA_DATA_DELETION: true,
      });
      openLocalTableMonitorDetailsPage("SPACE1234", "abc");
      cy.wait(["@getMetrics", "@getLogs"]);
    });

    it("Check Run started by column cell values", function () {
      cy.get(
        "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteLogTableView--UserColumnHeader"
      ).then(($header) => {
        expect($header.text().trim()).to.equal("Run Started By");
      });
      cy.get('[id*="dataDeleteLogTableView--dataDeleteLogTableId-0-cell4"]').then(($cell) => {
        expect($cell.text().trim()).to.equal("DWC (Manual)");
      });
      cy.get('[id*="dataDeleteLogTableView--dataDeleteLogTableId-1-cell4"]').then(($cell) => {
        expect($cell.text().trim()).to.equal("I345794 (Scheduled)");
      });
    });
  });
});
