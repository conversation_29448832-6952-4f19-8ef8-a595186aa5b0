/** @format */

export const cssSelectors = {
  columnSelectorId:
    "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterColumns-shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterValues-0-arrow",
  ddrCalendarButtonId:
    "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--dynamic-range-shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterValues-0-input-vhi",
  dataDeleteRadioButtonGroupId:
    "shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--dataDeleteRadioButtonGrp",
  columnSelectorListClass: ".sapMSelectList",
  filterHeaderGroupClass: ".sapMGHLITitle",
  objectPageSettingsTabId:
    "shellMainContent---dataIntegrationComponent---localTableObjectDetails--localTableDataManagement-anchBar-shellMainContent---dataIntegrationComponent---localTableObjectDetails--hdlfSettingsObjectPageId-anchor",
  objectPageSettingsExpandedId:
    "shellMainContent---dataIntegrationComponent---localTableObjectDetails--localTableDataManagement-anchBar-shellMainContent---dataIntegrationComponent---localTableObjectDetails--hdlfSettingsObjectPageId-anchor-expandButton",
  zOrderEditButtonId: "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--zOrderEditBtnId",
  zOrderDeleteButtonId: "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--zOrderDeleteBtnId",
  zOrderDefineButtonId: "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--addZOrderBtnId",
  zOrderConfirmButtonId:
    "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--zOrderDailog-confirmBtn",
  zOrderCancelButtonId:
    "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--zOrderDailog-cancelBtn",
  optimizePopupColumnsId:
    "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--zOrderDetailsId-content",
};

export const DATE_FILTERS = [
  "DATERANGE",
  "TODAYFROMTO",
  "NEXTWEEKS",
  "NEXTMONTHS",
  "NEXTQUARTERS",
  "NEXTYEARS",
  "NEXTDAYSINCLUDED",
  "NEXTWEEKSINCLUDED",
  "NEXTMONTHSINCLUDED",
  "NEXTQUARTERSINCLUDED",
  "NEXTYEARSINCLUDED",
  "LASTYEARSINCLUDED",
  "LASTYEARS",
  "LASTQUARTERSINCLUDED",
  "LASTQUARTERS",
  "LASTMONTHSINCLUDED",
  "LASTMONTHS",
  "LASTWEEKSINCLUDED",
  "LASTWEEKS",
  "LASTDAYSINCLUDED",
  "LASTDAYS",
  "YEARTODATE",
  "DATETOYEAR",
  "FROM",
  "TO",
  "DATE",
];

export const DATE_TIME_FILTERS = [
  "FROMDATETIME",
  "TODATETIME",
  "NEXTMINUTESINCLUDED",
  "NEXTHOURSINCLUDED",
  "LASTHOURSINCLUDED",
  "LASTHOURS",
  "LASTMINUTESINCLUDED",
  "LASTMINUTES",
];
