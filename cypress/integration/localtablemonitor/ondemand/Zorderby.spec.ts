/** @format */
// FILEOWNER: [local-table-administration]

import {
  assertMessageToast,
  openLocalTableMonitorDetailsPage,
  prepareTestEnvironment,
  pressButton,
} from "../../../pageobjects/dataIntegration/DataIntegrationHelper";
import { cssSelectors } from "./Constants";

describe("Optimize with Z order columns support", () => {
  beforeEach(() => {
    prepareTestEnvironment({
      DWC_DUMMY_SPACE_PERMISSIONS: true,
      DWCO_LOCAL_TABLE_FILES_MONITOR: true,
      DWCO_LTA_DETAIL_SCREENS: true,
      DWCO_LARGE_SYSTEMS_APPS_API: true,
      DWCO_LARGE_SYSTEMS_SPARK_SELECTION: true,
      DWCO_LOCAL_TABLE_FILES_ZORDER: true,
    });
    cy.intercept("GET", "**/monitor/SPACE1234/localTables/LTF_01**", {
      fixture: "localtablemonitor/localTableFiles.json",
    }).as("getLocalTableFile01");

    cy.intercept("GET", "**/monitor/SPACE1234/localTables/LTF_02**", {
      localTables: [
        {
          tableName: "LTF_02",
          businessName: "LTF 02",
          recordCount: 14,
          isDeltaEnabled: true,
          isFileStorage: true,
          partitionColumnsCount: 2,
          bufferMergeStatus: "COMPLETED",
          activeRecordCount: 10,
          activeRecordFileStorageMiB: 0.1,
          previousVersionsFileStorageMiB: 0.2,
          totalFileStorageMiB: 0.3,
          bufferFileSizeMiB: 1.76,
          bufferFileCount: 29,
        },
      ],
    }).as("getLocalTableFile02");

    cy.intercept("GET", "**/userprivileges", { fixture: "localtablemonitor/privilegesDI.json" });

    cy.intercept("GET", "**/resources/spaces**", { fixture: "spacesResponse" }).as("getSpaceDetails");

    cy.intercept("GET", "**/resources/spaces?islocked&spaceids=SPACE1234**", { SPACE1234: { isLocked: false } }).as(
      "getSpaceLockStatus"
    );

    cy.intercept("GET", "**schedules?applicationId=LOCAL_TABLE", []).as("getSchedules");

    cy.intercept("GET", "**/logs", []).as("getLogs");

    cy.intercept("GET", "**/resources/spaces?spaceids=SPACE1234", {
      fixture: "localtablemonitor/sparkApps.json",
    }).as("getSparkInfo");

    cy.intercept("POST", "**/directexecute", {
      applicationId: "LOCAL_TABLE",
      spaceId: "SPACE1234",
      objectId: "LTF_01",
      activity: "OPTIMIZE_FILES",
    }).as("optimizeTable");

    cy.intercept("GET", "**/customtenantclassification", { data: {} });

    cy.intercept("GET", "**/webassistant/**", {});

    cy.intercept("GET", "**/tf/schedules/consent", {
      fixture: "localtablemonitor/consent.json",
    }).as("getConsent");

    cy.intercept("POST", "**/settings", {
      MERGE_FILES: {
        sparkApplication: "500",
      },
      OPTIMIZE_FILES: {
        sparkApplication: "300",
      },
    }).as("getSettings");

    cy.intercept("GET", "**/columns", {
      fixture: "localtablemonitor/columns.json",
    }).as("getColumns");
  });

  it("Add z order columns", function () {
    openLocalTableMonitorDetailsPage("SPACE1234", "LTF_02");
    cy.wait(["@getLocalTableFile02"]);
    pressButton(cssSelectors.objectPageSettingsExpandedId);
    cy.get(".sapMITBSelectList ").find("li").eq(1).click({ force: true });
    cy.get(cssSelectors.zOrderEditButtonId).click({ force: true });
    cy.get('[id*="zOrderColumnsPanel-innerSelectionPanelTable-1-ModeCell"]').click();
    cy.get(cssSelectors.zOrderCancelButtonId).click();
    cy.get(cssSelectors.zOrderDefineButtonId).should("exist").click();
    cy.get('[id*="zOrderColumnsPanel-innerSelectionPanelTable-1-ModeCell"]').click();
    cy.get(cssSelectors.zOrderConfirmButtonId).click();
    cy.get(cssSelectors.zOrderDefineButtonId).should("not.exist");
  });

  it("Show read only z orderby columns on the optimize popup", function () {
    openLocalTableMonitorDetailsPage("SPACE1234", "LTF_01");
    cy.wait(["@getLocalTableFile01"]);
    cy.get("#shellMainContent---dataIntegrationComponent---localTableObjectDetails--optimizeTableActionButton").click();
    cy.get(cssSelectors.optimizePopupColumnsId)
      .find("input")
      .then(($el) => {
        expect($el.val()).to.equal("string_column");
      });
    cy.get("#shellMainContent---dataIntegrationComponent---localTableObjectDetails--msgBoxTableActionBtnId").click();
    cy.wait(["@optimizeTable", "@getSettings"]);
    assertMessageToast(`Optimize task has been started for table "LTF_01"`);
  });

  it("Delete z order columns", function () {
    openLocalTableMonitorDetailsPage("SPACE1234", "LTF_01");
    cy.wait(["@getLocalTableFile01"]);
    pressButton(cssSelectors.objectPageSettingsExpandedId);
    cy.get(".sapMITBSelectList ").find("li").eq(1).click({ force: true });

    cy.intercept("GET", "**/monitor/SPACE1234/localTables/LTF_01**", {
      localTables: [
        {
          tableName: "LTF_01",
          businessName: "LTF_01",
          recordCount: 14,
          isDeltaEnabled: true,
          isFileStorage: true,
          settings: {
            OPTIMIZE_FILES: {
              sparkApplication: "300",
              zOrderby: [],
            },
          },
          partitionColumnsCount: 2,
          bufferMergeStatus: "COMPLETED",
          activeRecordCount: 10,
          activeRecordFileStorageMiB: 0.1,
          previousVersionsFileStorageMiB: 0.2,
          totalFileStorageMiB: 0.3,
          bufferFileSizeMiB: 1.76,
          bufferFileCount: 29,
        },
      ],
    }).as("getLocalTableFile01");

    cy.get(cssSelectors.zOrderDeleteButtonId).click({ force: true });
    cy.wait(["@getSettings"]);
    assertMessageToast(`Apache Spark Application Settings saved.`);
    cy.get(cssSelectors.zOrderDefineButtonId).should("be.visible");
  });
});
