/** @format */
// FILEOWNER: [local-table-administration]

import { expect } from "chai";
import {
  assertRadioButton,
  openLocalTableMonitorDetailsPage,
  prepareTestEnvironment,
  selectRadioButton,
} from "../../../pageobjects/dataIntegration/DataIntegrationHelper";
import { DATE_FILTERS, DATE_TIME_FILTERS, cssSelectors } from "./Constants";

describe("On demand selective delete - Filtered Delete", () => {
  beforeEach(() => {
    prepareTestEnvironment({
      DWC_DUMMY_SPACE_PERMISSIONS: false,
      DWCO_LTA_DYNA_DATE_SELECT: false,
    });

    cy.intercept("GET", "**/resources/spaces?islocked&spaceids=SPACE1234**", { SPACE1234: { isLocked: false } }).as(
      "getSpaceLockStatus"
    );

    cy.intercept("GET", "**/SPACE1234/localTables/abc/schedules", {
      fixture: "localtablemonitor/schedulesEditBindings.json",
    }).as("getSchedule");

    // user privilege
    cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });
    cy.intercept("GET", "**/SPACE1234/localTables/abc?includeBusinessNames=true", {
      fixture: "localtablemonitor/tables.json",
    }).as("getMetrics");
    cy.intercept("GET", "**/monitor/SPACE1234/localtables/abc/deletionlogs**", {
      fixture: "localtablemonitor/logs.json",
    }).as("getLogs");
    cy.intercept("GET", "**/columns", {
      fixture: "localtablemonitor/columns.json",
    }).as("getColumns");
  });

  describe("on demand selective delete for local tables - filtered deletion ", () => {
    beforeEach(() => {
      openLocalTableMonitorDetailsPage("SPACE1234", "abc");
      cy.wait(800);
      cy.wait(["@getMetrics"]);

      selectRadioButton(
        "shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--dataDeleteWithFilterId-Button"
      );
      assertRadioButton(
        "shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--dataDeleteWithFilterId-Button",
        true
      );
      cy.wait(800);
      cy.wait(["@getColumns"]);
      cy.wait(800);
    });

    it("Check that the columns are listed for selective delete", function () {
      cy.get(
        "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterColumns-shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterValues-0-arrow"
      ).click();

      cy.get(".sapMSelectList").children().should("have.length", 7);
      cy.get(".sapMSelectList ").find("li").eq(2).click({ force: true });
    });

    it("check that the appropriate operators are displayed", function () {
      const expectedItems = [
        "Equal To",
        "Between",
        "Less Than",
        "Greater Than",
        "Less Than or Equal To",
        "Greater Than or Equal To",
        "Empty",
      ];
      cy.get(
        "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterColumns-shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterValues-0-arrow"
      ).click({ force: true });
      cy.get(".sapMSelectList ").find("li").eq(2).click({ force: true });

      cy.get(
        "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--operators-shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterValues-0-arrow"
      ).click({ force: true });

      cy.get(
        "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--operators-shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterValues-0-valueStateText-flexContentContainer"
      )
        .children()
        .find("li")
        .should("have.length", expectedItems.length);
    });

    it("check that the dynamic date range operator is displayed for date column", function () {
      cy.get(
        "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterColumns-shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterValues-0-arrow"
      ).click({ force: true });
      cy.get(".sapMSelectList ").find("li").eq(5).click({ force: true });
      cy.get(
        "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--dynamic-range-shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterValues-0-input-vhi"
      )
        .should("be.visible")
        .click({ force: true });
      cy.get(".sapMSLIDiv").children().should("contain.text", "BeforeXDays").click({ force: true });
      cy.wait(1000);
      // cy.get("#dynamicDateRangeStepInputId").should("be.visible");
      // cy.get("#dynamicDateRangeStepInputId-incrementBtn").should("be.visible").click({ force: true });
      // cy.get("#dynamicDateRangeStepInputId-decrementBtn").should("be.visible").click({ force: true });

      // cy.get("#dynamicDateRangeStepInputId").should("be.visible").should("have.value", "1");
      // cy.get(".sapMBtnBase").should("have.text", "Apply").click({ force: true });
      cy.get('[id*="BDI-content"]').first().should("have.text", "Apply").click({ force: true });
      // cy.wait(5000);
      // cy.get(
      //   "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--dynamic-range-shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterValues-0-input"
      // ).should("have.text", ">= Before 1 days");
    });

    it("should display only BeforeXDays filter when dynamic date feature flag is disabled", function () {
      // arrange
      cy.get(cssSelectors.columnSelectorId).click({ force: true });
      cy.get(cssSelectors.columnSelectorListClass).find("li").eq(5).click({ force: true });

      // act
      cy.get(cssSelectors.ddrCalendarButtonId).should("be.visible").click({ force: true });

      // assert
      cy.get(cssSelectors.filterHeaderGroupClass).should("have.length", 1);
    });
  });
});
/**
 * @issueid DW101-87267
 */

describe("On demand selective delete - Filtered Delete for more columns", () => {
  beforeEach(() => {
    prepareTestEnvironment({
      DWC_DUMMY_SPACE_PERMISSIONS: false,
      DWCO_LTA_DYNA_DATE_SELECT: false,
    });

    cy.intercept("GET", "**/resources/spaces?islocked&spaceids=SPACE1234**", { SPACE1234: { isLocked: false } }).as(
      "getSpaceLockStatus"
    );

    cy.intercept("GET", "**/SPACE1234/localTables/abc/schedules", {
      fixture: "localtablemonitor/schedulesEditBindings.json",
    }).as("getSchedule");

    // user privilege
    cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });
    cy.intercept("GET", "**/SPACE1234/localTables/abc?includeBusinessNames=true", {
      fixture: "localtablemonitor/tables.json",
    }).as("getMetrics");
    cy.intercept("GET", "**/monitor/SPACE1234/localtables/abc/deletionlogs**", {
      fixture: "localtablemonitor/logs.json",
    }).as("getLogs");
    cy.intercept("GET", "**/columns", {
      fixture: "localtablemonitor/columns200.json",
    }).as("getColumns200");
  });
  describe("on demand selective delete for local tables - filtered deletion ", () => {
    beforeEach(() => {
      openLocalTableMonitorDetailsPage("SPACE1234", "abc");
      cy.wait(800);
      cy.wait(["@getMetrics"]);

      selectRadioButton(
        "shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--dataDeleteWithFilterId-Button"
      );
      assertRadioButton(
        "shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--dataDeleteWithFilterId-Button",
        true
      );
      cy.wait(800);
      cy.wait(["@getColumns200"]);
      cy.wait(800);
    });
    it("Check that the more than 100 columns are listed for selective delete ", function () {
      cy.get(
        "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterColumns-shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterValues-0-arrow"
      ).click({ force: true });

      cy.get(".sapMSelectList").children().should("have.length", 200);
    });
  });
});

describe("On demand selective delete - filtered deleted for dynamic date ranges", () => {
  beforeEach(() => {
    prepareTestEnvironment({
      DWC_DUMMY_SPACE_PERMISSIONS: false,
      DWCO_LTA_DYNA_DATE_SELECT: true,
    });

    cy.intercept("GET", "**/resources/spaces?islocked&spaceids=SPACE1234**", { SPACE1234: { isLocked: false } }).as(
      "getSpaceLockStatus"
    );

    cy.intercept("GET", "**/SPACE1234/localTables/abc/schedules", {
      fixture: "localtablemonitor/schedulesEditBindings.json",
    }).as("getSchedule");

    // user privilege
    cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });
    cy.intercept("GET", "**/SPACE1234/localTables/abc?includeBusinessNames=true", {
      fixture: "localtablemonitor/tables.json",
    }).as("getMetrics");
    cy.intercept("GET", "**/monitor/SPACE1234/localtables/abc/deletionlogs**", {
      fixture: "localtablemonitor/logs.json",
    }).as("getLogs");
    cy.intercept("GET", "**/columns", {
      fixture: "localtablemonitor/columns.json",
    }).as("getColumns");

    openLocalTableMonitorDetailsPage("SPACE1234", "abc");
    cy.wait(800);
    cy.wait(["@getMetrics"]);

    selectRadioButton(
      "shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--dataDeleteWithFilterId-Button"
    );
    assertRadioButton(
      "shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--dataDeleteWithFilterId-Button",
      true
    );
    cy.wait(800);
    cy.wait(["@getColumns"]);
    cy.wait(800);
  });

  it("should display all date range filters when toggle is enabled", function () {
    // arrange
    cy.get(
      "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterColumns-shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterValues-0-arrow"
    ).click({ force: true });
    cy.get(".sapMSelectList ").find("li").eq(5).click({ force: true });
    // act
    cy.get(
      "#shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--dynamic-range-shellMainContent---dataIntegrationComponent---localTableObjectDetails--dataDeleteSectionId--filterValues-0-input-vhi"
    )
      .should("be.visible")
      .click({ force: true });
    // assert
    const filterGroupHeaders = cy.get(".sapMGHLITitle");
    filterGroupHeaders.should("have.length", 3);
    filterGroupHeaders.each((header) => {
      const headerText = header.text();
      expect(headerText).to.be.oneOf(["Custom Options", "Date Ranges", "Single Dates"]);
    });
  });

  it("should display date range filters when column is of type date", function () {
    // arrange
    cy.get(cssSelectors.columnSelectorId).click({ force: true });
    cy.get(cssSelectors.columnSelectorListClass).find("li").eq(5).click({ force: true });

    // act
    cy.get(cssSelectors.ddrCalendarButtonId).should("be.visible").click({ force: true });

    // assert
    cy.byId(cssSelectors.dataDeleteRadioButtonGroupId).then((radioButtonGroup) => {
      const standardOptions = radioButtonGroup.getModel().getData().stdOpts;
      expect(standardOptions).to.exist.and.to.deep.equal(DATE_FILTERS);
    });
  });

  it("should display datetime filters for datetime or timestamp columns", function () {
    // arrange
    cy.get(cssSelectors.columnSelectorId).click({ force: true });
    cy.get(cssSelectors.columnSelectorListClass).find("li").eq(4).click({ force: true });

    // act
    cy.get(cssSelectors.ddrCalendarButtonId).should("be.visible").click({ force: true });

    // assert
    cy.byId(cssSelectors.dataDeleteRadioButtonGroupId).then((radioButtonGroup) => {
      const standardOptions = radioButtonGroup.getModel().getData().stdOpts;
      cy.writeFile("cypress/logs/cypress-logs", standardOptions.toString());
      expect(standardOptions).to.exist.and.to.deep.equal([...DATE_FILTERS, ...DATE_TIME_FILTERS]);
    });
  });
});
