/** @format */
// FILEOWNER: [local-table-administration]

import { IAPI, getAPIInstance } from "../../../pageobjects/api/IAPI";
import {
  assertButton,
  assertMTableRowCount,
  assertMessageBox,
  openLocalTableMonitorDetailsPage,
  prepareTestEnvironment,
  pressButton,
} from "../../../pageobjects/dataIntegration/DataIntegrationHelper";
import { UITesting } from "../../databuilder/common/UITesting";

describe("Data delete schedules listing table", () => {
  const tableId = `shellMainContent---dataIntegrationComponent---localTableObjectDetails--scheduleDeleteView--scheduleDataDeleteTableId`;
  const scheduleId = "shellMainContent---dataIntegrationComponent---localTableObjectDetails--scheduleDeleteView";
  let api: IAPI;
  beforeEach(() => {
    prepareTestEnvironment({
      DWC_DUMMY_SPACE_PERMISSIONS: false,
    });
    api = getAPIInstance();

    cy.intercept("GET", "**/resources/spaces?islocked&spaceids=SPACE1234**", { SPACE1234: { isLocked: false } }).as(
      "getSpaceLockStatus"
    );
    cy.intercept("GET", "**/repository/remotes?space_ids=**", { results: [] }).as("getRemoteSourceInfo");

    // user privilege
    cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });

    cy.intercept("GET", "**/SPACE1234/localTables/abc/schedules", {
      fixture: "localtablemonitor/scheduleDeleteTable.json",
    }).as("getSchedule");

    cy.intercept("GET", "**/SPACE1234/localTables/abc?**", {
      fixture: "localtablemonitor/tables.json",
    }).as("getMetrics");

    cy.intercept("GET", "**/monitor/SPACE1234/localtables/abc/deletionlogs**", {
      fixture: "localtablemonitor/logs.json",
    }).as("getLogs");

    cy.intercept("GET", "**/tf/schedules/consent", {
      fixture: "localtablemonitor/consent.json",
    }).as("getConsent");
  });

  describe("Check getSchedule and bindings", () => {
    beforeEach(() => {
      openLocalTableMonitorDetailsPage("SPACE1234", "abc");
      cy.wait(["@getSchedule", "@getMetrics", "@getLogs"]);
    });

    it("Check for number of records and if breadcrumb shows name according to user preference", async function () {
      assertMTableRowCount(tableId, 3);
      await api.assertCurrentBreadcrumbs({
        text: "Table's Business Name",
      });
      const sap = UITesting.sap();
      sap?.ui
        .getCore()
        .getEventBus()
        .publish("userPreferences", "change", {
          changes: {
            DWC_OBJECT_NAME_DISPLAY: {
              newValue: "technicalName",
            },
          },
        });
      await api.assertCurrentBreadcrumbs({
        text: "abc",
      });
    });

    it("Check if the edit and delete buttons are disabled", function () {
      assertButton(scheduleId + "--DataDeleteScheduleEditBtnId", false);
      assertButton(scheduleId + "--DataDeleteScheduleDeleteBtnId", false);
    });

    it("Check the search functionality", function () {
      cy.get("#" + scheduleId + "--deleteScheduleSearchId")
        .should("be.visible")
        .find("input")
        .type("created", { force: true });
      cy.wait(10);
      cy.get("#" + scheduleId + "--deleteScheduleSearchId-F").click({ force: true });
      cy.wait(10);
      assertMTableRowCount(tableId, 1);
    });

    it("Check if edit and delete buttons are enabled after selecting a list item", function () {
      cy.get(
        '[id*="-shellMainContent---dataIntegrationComponent---localTableObjectDetails--scheduleDeleteView--scheduleDataDeleteTableId-0-cell1"]'
      ).click({ force: true });
      assertButton(scheduleId + "--DataDeleteScheduleEditBtnId", true);
      assertButton(scheduleId + "--DataDeleteScheduleDeleteBtnId", true);
    });

    it("Check if clicking on Authorize displays a popup", function () {
      cy.wait(["@getConsent"]);
      cy.get("#" + scheduleId + "--dataDeleteScheduleAuth--taskscheduleAuthorise")
        .should("be.visible")
        .click();
      assertMessageBox(
        "InformationUser authentication is required to run scheduled tasks in SAP Datasphere.\n By clicking Authorize, you provide consent to run future scheduled tasks on your behalf.AuthorizeCancel"
      );
    });

    it("Check the sort functionality", function () {
      cy.get(
        '[id*= "-shellMainContent---dataIntegrationComponent---localTableObjectDetails--scheduleDeleteView--scheduleDataDeleteTableId-0-text"]'
      ).should("have.text", "Failed");
      pressButton(scheduleId + "--dataDeleteScheduleSortBtn");
      cy.get("#" + scheduleId + "--dataDeleteScheduleSort-sortlist")
        .find("li")
        .last()
        .click();
      cy.get("#" + scheduleId + "--dataDeleteScheduleSort-acceptbutton").click();
      cy.get(
        '[id*= "-shellMainContent---dataIntegrationComponent---localTableObjectDetails--scheduleDeleteView--scheduleDataDeleteTableId-0-text"]'
      ).should("have.text", "Completed");
    });

    it("Check the filter functionality", function () {
      pressButton(scheduleId + "--dataDeleteScheduleFilterBtn");
      cy.get("#" + scheduleId + "--scheduleDataDeleteTableFilter-filterlist")
        .find("li")
        .last()
        .click();
      cy.get("#" + scheduleId + "--scheduleDataDeleteTableFilter-page2")
        .find("li")
        .last()
        .click();
      cy.get("#" + scheduleId + "--scheduleDataDeleteTableFilter-acceptbutton").click();
      cy.wait(1000);
      assertMTableRowCount(tableId, 1);

      pressButton(scheduleId + "--dataDeleteScheduleFilterBtn");
      pressButton(scheduleId + "--scheduleDataDeleteTableFilter-detailresetbutton");
      cy.get("#" + scheduleId + "--scheduleDataDeleteTableFilter-acceptbutton").click();
      cy.wait(1000);
      assertMTableRowCount(tableId, 3);
    });
  });
});
