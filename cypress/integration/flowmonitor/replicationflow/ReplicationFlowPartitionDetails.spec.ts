/** @format */

import { getAPIInstance } from "../../../pageobjects/api/IAPI";
import { APIImpl } from "../../../pageobjects/api/impl/APIImpl";
import {
  DI_PAGE,
  MESSAGES_TABLE,
  MESSAGES_TAB_ID,
  OptionalRoutesMocks,
  PARTITION_TABLE,
  REPLICATIONS_TABLE,
  TAB_KEYS,
  TWO_TENANT,
  openReplicationflowDetails,
  setDefaultMock,
  setReplicationflowStatusMock,
} from "./Util";

describe("cypress/integration/flowmonitor/replicationflow/ReplicationFlowPartitionDetails", () => {
  let api: APIImpl;
  const ADDITIONAL_ERROR_TABLE =
    "shellMainContent---dataIntegrationComponent---replicationflowdetails--additionalErrorTable";
  const LAST_DATA_TRANSFERRED_AT =
    "shellMainContent---dataIntegrationComponent---replicationflowdetails--lastTransferredAtValue";

  beforeEach(async () => {
    api = getAPIInstance();
    await setDefaultMock(api, [OptionalRoutesMocks.COMMON_ROUTE, OptionalRoutesMocks.USER_ROUTE], [TWO_TENANT]);
    await setReplicationflowStatusMock(api);
  });
  /**
   * @issueid DW101-82156
   */
  describe("{replicationflowmonitordetails} open partition dialog and check the details", () => {
    it("should check for partition table and additional error information", async () => {
      await fetchMessages(api);
      const table = await cy.byId(MESSAGES_TABLE);
      table.getItems()[4].getCells()[3].firePress();
      await api.assertTable({
        table: `${DI_PAGE}--partitionsTable`,
        rows: 2,
      });
      const partitionsTable = await cy.byId(PARTITION_TABLE);
      expect(partitionsTable.getItems()[1].getCells()[1].getText()).to.equals("Error");
      await api.assertTable({
        table: `${DI_PAGE}--additionalErrorTable`,
        rows: 3,
      });
      const additionalTable = await cy.byId(ADDITIONAL_ERROR_TABLE);
      expect(additionalTable.getItems()[0].getCells()[1].getText()).to.equals("TXT_FAILED");
      await api.selectTableRow(PARTITION_TABLE, 0, true);
      await api.assertContainsText({ id: LAST_DATA_TRANSFERRED_AT, value: "Apr 22, 2024" });
    });

    it("should open task error message", async () => {
      await fetchMessages(api);
      const table = await cy.byId(MESSAGES_TABLE);
      table.getItems()[1].getCells()[3].firePress();
      await api.assertControlExists({
        id: "errorViewDetails",
      });
      await api.clickButton("__mbox-btn-0");
    });
  });
});

async function fetchMessages(api: APIImpl) {
  await api.mockServerCall(
    "GET",
    "**/replicationflow/space/SPACE1234/flows/Test_RF/status?digTaskId=5638bba2-2947-427f-a867-43d9e5733709",
    "fixture:replicationflowmonitor/replicationflowpartitions",
    "getreplicationmetrics"
  );
  await openReplicationflowDetails(api, true, 0, [TWO_TENANT]);
  await api.XHRWait("@getreplicationflowstatus");
  await api.XHRWait("@getreplicationmetrics");
  await api.selectTableRow(REPLICATIONS_TABLE, 1, true);
  await api.selectIconTabBarItem(MESSAGES_TAB_ID, TAB_KEYS.MESSAGES_TAB_KEY);
  await api.assertTable({
    table: `${DI_PAGE}--messagesTable` as any,
    rows: 5,
    timeout: 10,
  });
}
