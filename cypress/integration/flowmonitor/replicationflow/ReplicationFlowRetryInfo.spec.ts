/** @format */

import { getAPIInstance } from "../../../pageobjects/api/IAPI";
import { APIImpl } from "../../../pageobjects/api/impl/APIImpl";
import {
  DI_PAGE,
  MESSAGES_TABLE,
  MESSAGES_TAB_ID,
  OptionalRoutesMocks,
  REPLICATIONS_TABLE,
  TAB_KEYS,
  TWO_TENANT,
  openReplicationflowDetails,
  setDefaultMock,
  setReplicationflowStatusMock,
} from "./Util";

describe("cypress/integration/flowmonitor/replicationflow/ReplicationFlowPartitionDetails", () => {
  let api: APIImpl;
  const RETRY_LOG_TABLE = "shellMainContent---dataIntegrationComponent---replicationflowdetails--retryLogTable";
  const METRICS_VIEW_DETAILS =
    "shellMainContent---dataIntegrationComponent---replicationflowdetails--partitiondetailslink";

  beforeEach(async () => {
    api = getAPIInstance();
    await setDefaultMock(api, [OptionalRoutesMocks.COMMON_ROUTE, OptionalRoutesMocks.USER_ROUTE], [TWO_TENANT]);
    await setReplicationflowStatusMock(api);
  });

  /**
   * @issueid DW101-82154
   */
  describe("{replicationflowmonitordetails} open partition dialog and check retry logs", () => {
    it("should check for retry information and retry log", async () => {
      await fetchMessages(api);
      await api.assertTable({
        table: `${DI_PAGE}--messagesTable` as any,
        rows: 7,
        timeout: 10,
      });
      const table = await cy.byId(MESSAGES_TABLE);
      table.getItems()[4].getCells()[3].firePress();
      await api.assertTable({
        table: `${DI_PAGE}--partitionsTable`,
        rows: 10,
      });
      await api.assertTable({
        table: `${DI_PAGE}--retryLogTable`,
        rows: 2,
      });
      const retryTable = await cy.byId(RETRY_LOG_TABLE);
      expect(retryTable.getItems()[0].getCells()[1].getText()).to.equals("384");
      expect(retryTable.getItems()[0].getCells()[2].getText()).to.equals("390:03:55");
    });

    it("open partition dialog from the metrics section when ff is on", async () => {
      await fetchMessages(api, true);
      await api.assertControlExists({
        id: METRICS_VIEW_DETAILS,
      });
      await api.clickButton(METRICS_VIEW_DETAILS, true);
      await api.assertTable({
        table: `${DI_PAGE}--partitionsTable`,
        rows: 10,
      });
    });
  });
});

async function fetchMessages(api: APIImpl, isMetricsTab?: boolean) {
  await api.mockServerCall(
    "GET",
    "**/replicationflow/space/SPACE1234/flows/Test_RF/status?digTaskId=5638bba2-2947-427f-a867-43d9e5733709",
    "fixture:replicationflowmonitor/replicationflowretryInfo",
    "getreplicationmetrics"
  );
  await openReplicationflowDetails(api, true, 0, [TWO_TENANT]);
  await api.XHRWait("@getreplicationflowstatus");
  await api.XHRWait("@getreplicationmetrics");
  await api.selectTableRow(REPLICATIONS_TABLE, 1, true);
  isMetricsTab
    ? await api.selectIconTabBarItem(MESSAGES_TAB_ID, TAB_KEYS.METRICS_TAB_KEY)
    : await api.selectIconTabBarItem(MESSAGES_TAB_ID, TAB_KEYS.MESSAGES_TAB_KEY);
}
