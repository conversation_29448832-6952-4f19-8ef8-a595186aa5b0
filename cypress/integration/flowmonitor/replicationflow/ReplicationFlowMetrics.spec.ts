/** @format */

import { getAPIInstance } from "../../../pageobjects/api/IAPI";
import { APIImpl } from "../../../pageobjects/api/impl/APIImpl";
import {
  DI_PAGE,
  openReplicationflowDetails,
  OptionalRoutesMocks,
  REPLICATIONS_TABLE,
  setDefaultMock,
  setReplicationflowStatusMock,
  TWO_TENANT,
} from "./Util";

describe("cypress/integration/flowmonitor/replicationflow/ReplicationFlowDetails1", () => {
  let api: APIImpl;

  beforeEach(async () => {
    api = getAPIInstance();
    await setDefaultMock(api, [OptionalRoutesMocks.COMMON_ROUTE, OptionalRoutesMocks.USER_ROUTE], [TWO_TENANT]);
    await setReplicationflowStatusMock(api);
  });

  describe("{replicationflowmonitordetails} replications metrics information", () => {
    it("replication table count", async () => {
      await utilFunc(api);
      await api.assertTable({
        table: REPLICATIONS_TABLE as any,
        rows: 10,
        timeout: 10,
      });
    });

    /**
     * @issueid DW101-42193
     */

    it("should show metrics information", async () => {
      await utilFunc(api);
      await api.assertText({
        id: `${DI_PAGE}--idInitialCount-text`,
        value: "19484",
      });
      await api.assertText({
        id: `${DI_PAGE}--idInitialDuration-text`,
        value: "48:00:19",
      });
      await api.assertText({
        id: `${DI_PAGE}--idNumberOfPartitions-text`,
        value: "2",
      });
      await api.assertText({
        id: `${DI_PAGE}--idPartitionCompleted-text`,
        value: "2",
      });
      await api.assertText({
        id: `${DI_PAGE}--idPartitionError-text`,
        value: "0",
      });
      await api.assertText({
        id: `${DI_PAGE}--idStateValue`,
        value: "Completed",
      });
      await api.assertText({
        id: `${DI_PAGE}--idDeltaPartitionValue`,
        value: "7",
      });
    });
  });
});

async function utilFunc(api: APIImpl) {
  await openReplicationflowDetails(api, true, 0, [TWO_TENANT]);
  await api.mockServerCall(
    "GET",
    "**/replicationflow/space/SPACE1234/flows/Test_RF/status?taskLogId=2567544&isHistory=true",
    "fixture:replicationflowmonitor/replicationflowdetails2",
    "getreplicationflowtaskdetails"
  );
  await api.XHRWait("@getreplicationflowstatus");
  await api.XHRWait("@getreplicationflowtaskdetails");
  await api.XHRWait("@getreplicationmetrics");
  await api.assertControlExists({
    id: REPLICATIONS_TABLE,
  });
}
