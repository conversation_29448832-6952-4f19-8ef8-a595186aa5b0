/** @format */

import { getAPIInstance } from "../../../pageobjects/api/IAPI";
import { APIImpl } from "../../../pageobjects/api/impl/APIImpl";
import {
  DI_PAGE,
  MESSAGES_TABLE,
  MESSAGES_TAB_ID,
  openReplicationflowDetails,
  OptionalRoutesMocks,
  REPLICATIONS_TABLE,
  setDefaultMock,
  setReplicationflowStatusMock,
  TAB_KEYS,
  TWO_TENANT,
} from "./Util";

describe("cypress/integration/flowmonitor/replicationflow/ReplicationFlowMessages", () => {
  let api: APIImpl;

  beforeEach(async () => {
    api = getAPIInstance();
    await setDefaultMock(api, [OptionalRoutesMocks.COMMON_ROUTE, OptionalRoutesMocks.USER_ROUTE], [TWO_TENANT]);
    await setReplicationflowStatusMock(api);
  });

  describe("{replicationflowmonitordetails} messages table", () => {
    it("should show messages", async () => {
      await fetchMessages(api);
      const table = await cy.byId(MESSAGES_TABLE);
      expect(table.getItems()[3].getCells()[1].getText()).to.equals("TXT_FAILED");
    });

    it("messages table view details", async () => {
      await fetchMessages(api);
      const table = await cy.byId(MESSAGES_TABLE);
      table.getItems()[3].getCells()[3].firePress();
      await api.assertControlExists({
        id: "errorViewDetails",
      });
      await api.clickButton("__mbox-btn-0");
    });

    /**
     * @issueid DW101-85304
     */
    it("should show category as information", async () => {
      await fetchMessages(api, true);
      const table = await cy.byId(MESSAGES_TABLE);
      const errorMsg =
        "Delta load partitioning completed. Data transfer messages are available for each of the partitions.";
      expect(table.getItems()[0].getCells()[1].getText()).to.equals("TXT_INFO");
      expect(table.getItems()[0].getCells()[2].getText()).to.contain(errorMsg);
    });

    /**
     * @issueid DW101-84856
     */
    it("should display local time in the message not UTC", async () => {
      await fetchMessages(api, true);
      const table = await cy.byId(MESSAGES_TABLE);
      expect(table.getItems()[1].getCells()[1].getText()).to.equals("TXT_INFO");
      expect(table.getItems()[1].getCells()[2].getText()).to.contain(
        "CorrelationId: 29df7686-753e-020d-1900-1cca75a44913@03-Oct-2024 12:52:28."
      );
    });
  });
});

async function fetchMessages(api: APIImpl, categoryCheck?: boolean) {
  const getDetails = categoryCheck ? "replicationflowmessagesretrying" : "replicationflowmessages";
  await api.mockServerCall(
    "GET",
    "**/replicationflow/space/SPACE1234/flows/Test_RF/status?digTaskId=5638bba2-2947-427f-a867-43d9e5733709",
    "fixture:replicationflowmonitor/" + getDetails,
    "getreplicationmetrics"
  );
  await openReplicationflowDetails(api, true, 0, [TWO_TENANT]);
  await api.XHRWait("@getreplicationflowstatus");
  await api.XHRWait("@getreplicationmetrics");
  await api.selectTableRow(REPLICATIONS_TABLE, 1, true);
  await api.selectIconTabBarItem(MESSAGES_TAB_ID, TAB_KEYS.MESSAGES_TAB_KEY);
  const rows = categoryCheck ? 3 : 6;
  await api.assertTable({
    table: `${DI_PAGE}--messagesTable` as any,
    rows: rows,
    timeout: 10,
  });
}
