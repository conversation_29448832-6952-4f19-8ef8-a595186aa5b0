/** @format */

import { BuilderType } from "../../../pageobjects/api/IAPI";
import { APIImpl } from "../../../pageobjects/api/impl/APIImpl";
import { Method, RouteConfig, XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { ShellPages } from "../../../pageobjects/shell";

export enum OptionalRoutesMocks {
  COMMON_ROUTE = "COMMON_ROUTE",
  USER_ROUTE = "USER_ROUTE",
}

export const scheduleList = [
  {
    scheduleId: "3ef51439-58a9-4eed-ac81-be398ca044b0",
    spaceId: "SPACE1234",
    applicationId: "REPLICATION_FLOWS",
    activity: "EXECUTE",
    objectId: "Test_RF",
    description: "Test_RF",
    cron: "46 9 */1 * *",
    validFrom: "2020-09-08T09:46:00.000Z",
    activationStatus: "ENABLED",
    createdBy: "DWCDEVELOPER",
    createdAt: "2020-09-08T09:47:37.549Z",
    changedAt: "2020-09-08T09:47:37.572Z",
    nextRun: "2020-09-09T09:46:00.000Z",
  },
];
export const TWO_TENANT = "INFRA_DWC_TWO_TENANT_MODE";
export const replicationFlowFFs = [TWO_TENANT];
export const SDP_FF = "DWC_DUMMY_SPACE_PERMISSIONS";
export const DI_PAGE = "shellMainContent---dataIntegrationComponent---replicationflowdetails";
export const REPLICATIONS_TABLE =
  "shellMainContent---dataIntegrationComponent---replicationflowdetails--replicationsTable";
export const RUNS_TABLE = "shellMainContent---dataIntegrationComponent---replicationflowdetails--runsTable";
export const MESSAGES_TABLE = "shellMainContent---dataIntegrationComponent---replicationflowdetails--messagesTable";
export const PARTITION_TABLE = "shellMainContent---dataIntegrationComponent---replicationflowdetails--partitionsTable";
export const RUNLOG_TABLE = "shellMainContent---dataIntegrationComponent---replicationflowdetails--runLogTable";
export const TASKSCHEDULE_DIALOG =
  "shellMainContent---dataIntegrationComponent---replicationflowdetails--detailSchedulingDialog--taskScheduleDialog";
export const TAB_ID = "shellMainContent---dataIntegrationComponent---dilandingpage--iconTabBar";
export const TAB_KEY = "dataFlowMonitor";

export const MESSAGES_TAB_ID =
  "shellMainContent---dataIntegrationComponent---replicationflowdetails--idMessagesAndMetricsTab";
export enum TAB_KEYS {
  MESSAGES_TAB_KEY = "MESSAGES_TAB",
  METRICS_TAB_KEY = "METRICS_TAB",
}

export const MONITORING_OVERVIEW_BASE_PAGE = "shellMainContent---dataIntegrationComponent---dataflowmonitor";

export let MONITORING_DI_PAGE = `${MONITORING_OVERVIEW_BASE_PAGE}--flowMonitorTable`;
let MONITORING_TASKSCHEDULE_DIALOG = `${MONITORING_OVERVIEW_BASE_PAGE}--dataFlowMonitorTable--monitoringSchedulingDialog--taskScheduleDialog`;

export async function openReplicationflowDetails(
  api: APIImpl,
  openRun = true,
  rowIndex = 0,
  featureFlags = []
): Promise<void> {
  await api.openModulePage(ShellPages.DataIntegration);
  await api.XHRWait("@getspacesResponse");
  await api.openSpace("SPACE1234", BuilderType.dataintegration);
  await api.XHRWait("@getRemoteTables");
  await api.selectIconTabBarItem(TAB_ID, TAB_KEY);

  await api.assertSegmentedButton({
    button: `${MONITORING_DI_PAGE}--segmentedButton`,
    selectedKey: "ALL",
  });
  await api.selectSegmentedButton(`${MONITORING_DI_PAGE}--segmentedButton`, 2, true);
  await api.XHRWait("@getreplicationflows");

  if (openRun) {
    const { MONITORING_DI_PAGE } = getMonitoringIds(featureFlags);
    await api.assertControlExists({
      id: `${MONITORING_DI_PAGE}--flowTable`,
      timeout: 10,
    });
    const replicationFlowTable = await cy.byId(`${MONITORING_DI_PAGE}--flowTable`);
    await api.assertControlExists({
      id: `${MONITORING_DI_PAGE}--flowTable-rows-row0-col0`,
      timeout: 100,
    });
    const tableRows = await replicationFlowTable.getRows();
    await tableRows[rowIndex].getRowAction().getItems()[0].firePress();
  }
}

export async function setReplicationflowStatusMock(api: APIImpl) {
  // once overview changes is done instead of getdataflows replace it with getreplicationflows
  await api.mockServerCall(
    "GET",
    "**/dataflow/status?space=SPACE1234**",
    "fixture:replicationflowmonitor/replicationflows",
    "getreplicationflows"
  );
  await api.mockServerCall(
    "GET",
    "**/replicationflow/space/SPACE1234/flows/Test_RF/status?updateLock=true",
    "fixture:replicationflowmonitor/replicationflowdetails",
    "getreplicationflowstatus"
  );
  await api.mockServerCall(
    "GET",
    "**/replicationflow/space/SPACE1234/flows/Test_RF/status?taskLogId=2567544&isHistory=true",
    "fixture:replicationflowmonitor/replicationflowtaskdetails",
    "getreplicationflowtaskdetails"
  );
  await api.mockServerCall(
    "GET",
    "**/replicationflow/space/SPACE1234/flows/Test_RF/status?digTaskId=5638bba2-2947-427f-a867-43d9e5733709",
    "fixture:replicationflowmonitor/replicationflowmetrics",
    "getreplicationmetrics"
  );
}
export async function setFailingReplicationFlowStatusMock(api: APIImpl) {
  await api.mockServerCall(
    "GET",
    "**/dataflow/status?space=SPACE1234**",
    "fixture:replicationflowmonitor/replicationflows",
    "getreplicationflows"
  );
  await api.mockServerCall(
    "GET",
    "**/replicationflow/space/SPACE1234/flows/Test_RF/status?updateLock=true",
    "fixture:replicationflowmonitor/replicationflowdetailsFailed",
    "getreplicationflowstatus"
  );
  await api.mockServerCall(
    "GET",
    "**/replicationflow/space/SPACE1234/flows/Test_RF/status?taskLogId=2567544&isHistory=true",
    "fixture:replicationflowmonitor/replicationflowtaskdetailsFailed",
    "getreplicationflowtaskdetails"
  );
}

export async function setDefaultMock(
  api: APIImpl,
  optionalRoutes: OptionalRoutesMocks[] = [],
  featureFlags: string[] = []
) {
  const routes = XHelpers.DEFAULT_MOCK_REPONSES;
  await handleAdditionalRoutes(routes, optionalRoutes);

  await api.setupBeforeEach(
    /* useMock*/ true,
    /* defaultFixture*/ true,
    /* routes*/ routes,
    /* overrideRouteCallback */ undefined,
    /* enabledFeatureFlags */ ["DWC_MODELING_SWITCH_TECHNICAL_NAME", ...featureFlags]
  );
  await api.mockServerCall(
    "GET",
    "**/remoteTables**",
    "fixture:remotetablemonitor/taskScheduleRemoteTables",
    "getRemoteTables"
  );
  await api.mockServerCall("GET", "**/schedules/consent", { consent: false }, "getConsent");
  await api.mockServerCall("GET", "**/schedules?**", [], "getSchedules");
  await api.mockServerCall("GET", "**/metrices?space=*", {}, "getMetrices");
}

export function getMonitoringIds(featureFlags: string[] = []) {
  if (featureFlags.includes("INFRA_DWC_TWO_TENANT_MODE")) {
    MONITORING_DI_PAGE = "shellMainContent---dataIntegrationComponent---dataflowmonitor--flowMonitorTable";
    MONITORING_TASKSCHEDULE_DIALOG =
      "shellMainContent---dataIntegrationComponent---dataflowmonitor--flowMonitorTable--monitoringSchedulingDialog--taskScheduleDialog";
  }

  return {
    MONITORING_DI_PAGE,
    MONITORING_TASKSCHEDULE_DIALOG,
  };
}

async function handleAdditionalRoutes(routes: RouteConfig[], additionalRoutes: OptionalRoutesMocks[]) {
  additionalRoutes.forEach(async (newRoute) => {
    if (newRoute === OptionalRoutesMocks.COMMON_ROUTE) {
      routes.concat(XHelpers.BB_COMMON_MOCK_RESPONSES);
    }
    if (newRoute === OptionalRoutesMocks.USER_ROUTE) {
      // overwrite default SAC user repsonse
      const userRoute = await getUserRoute();
      routes.push(userRoute);
    }
  });
}

async function getUserRoute() {
  const userResponse = await (cy.fixture("SACUserResponse") as any);
  const parameter = userResponse.user.parameters.find((param) => param.name === "DWC_OBJECT_NAME_DISPLAY");
  if (parameter) {
    parameter.value = "businessName";
  } else {
    userResponse.user.parameters.push({ name: "DWC_OBJECT_NAME_DISPLAY", value: "businessName" });
  }
  return {
    protocol: "GET" as Method,
    path: "**/sap/fpa/services/rest/epm/session?action=logon**",
    response: userResponse,
    as: "getSACUserResponse",
  };
}

export async function navigateBack(api: APIImpl): Promise<void> {
  const backButton = await api.getElementId('[aria-label="AppShell_BackTooltip"]');
  await api.clickButton(backButton, false, true);
}
