/** @format */

import { getAPIInstance } from "../../../pageobjects/api/IAPI";
import { APIImpl } from "../../../pageobjects/api/impl/APIImpl";
import { DI_PAGE, openTFDetails, OptionalRoutesMocks, setDefaultMock, setTFStatusMock, TWO_TENANT_FF } from "./Util";

describe("cypress/integration/flowmonitor/dataflow/TFHANABatchProcessingDetails", () => {
  let api: APIImpl;
  let batchDataBeforeProcessing, batchDataAfterProcessing;
  const SPACE_NAME = "SPACE1234";
  const EDIT_BATCHES_DIALOG = `${DI_PAGE}--editBatchesFragment`;

  async function mockBatchesServerCall(method, response, alias) {
    await api.mockServerCall(method, `**/batchprocessing/${SPACE_NAME}/transformationflow/test_Tf**`, response, alias);
  }

  async function openBatchesTab(batchData) {
    await mockBatchesServerCall("GET", batchData, "getBatches");
    await openTFDetails(api, true, 7);
    await api.assertButton({ button: `${DI_PAGE}--masterRunsTableOPL-anchBar-${DI_PAGE}--batches-anchor` });
    await api.clickButton(`${DI_PAGE}--masterRunsTableOPL-anchBar-${DI_PAGE}--batches-anchor`);
  }

  beforeEach(async () => {
    api = getAPIInstance();
    await setDefaultMock(api, [OptionalRoutesMocks.USER_ROUTE], [TWO_TENANT_FF, "DWCO_TRF_BATCHES"]);
    await setTFStatusMock(api, SPACE_NAME);
    await api.mockServerCall(
      "GET",
      `**/transformationflow/${SPACE_NAME}/runtimesettings/test_Tf**`,
      "fixture:dataflowmonitor/TFExecutionModeSettings",
      "getExecutionModeSettings"
    );
    await api.mockServerCall(
      "GET",
      `**/transformationflow/${SPACE_NAME}/status/test_Tf?includeBusinessNames=true`,
      "fixture:dataflowmonitor/transformationflowDetails",
      "gettransformationflowDetails"
    );
    batchDataBeforeProcessing = require("../../../fixtures/dataflowmonitor/TFBatchesDetailsHANA.json");
    batchDataAfterProcessing = {
      ...batchDataBeforeProcessing,
      column: "EEID",
      batchSize: 4,
      isPartitioningColumnValid: true,
      onlyInitialLoad: false,
    };
  });

  afterEach(async () => {
    delete require.cache[require.resolve("../../../fixtures/dataflowmonitor/TFBatchesDetailsHANA.json")];
  });

  it("should define batches for a transformation flow in HANA runtime", async () => {
    await openBatchesTab(batchDataBeforeProcessing);
    await api.assertButton({ button: `${DI_PAGE}--defineBatchesBtn` });
    await api.clickButton(`${DI_PAGE}--defineBatchesBtn`);
    await api.typeInputText(`${EDIT_BATCHES_DIALOG}--batchSizeInput`, "4");
    await api.selectTableRow(`${EDIT_BATCHES_DIALOG}--columnsTable`, 0);
    await mockBatchesServerCall(
      "PUT",
      {
        column: "EEID",
        batchSize: 4,
        onlyInitialLoad: false,
      },
      "saveBatches"
    );
    await mockBatchesServerCall("GET", batchDataAfterProcessing, "getBatches");
    await api.assertButton({ button: `${EDIT_BATCHES_DIALOG}--saveBatches` });
    await api.clickButton(`${EDIT_BATCHES_DIALOG}--saveBatches`);
    await api.assertText({
      id: `${DI_PAGE}--batchColumnText`,
      value: "EEID",
    });
    await api.assertText({
      id: `${DI_PAGE}--batchSizeText`,
      value: "4",
    });
  });

  it("should edit batches for a transformation flow in HANA runtime", async () => {
    await openBatchesTab(batchDataAfterProcessing);
    await api.assertButton({ button: `${DI_PAGE}--editBatchesBtn` });
    await api.clickButton(`${DI_PAGE}--editBatchesBtn`);
    await api.typeInputText(`${EDIT_BATCHES_DIALOG}--batchSizeInput`, "7");
    await api.selectTableRow(`${EDIT_BATCHES_DIALOG}--columnsTable`, 2);
    await mockBatchesServerCall(
      "PUT",
      {
        column: "Annual Salary",
        batchSize: 7,
        onlyInitialLoad: false,
      },
      "saveBatches"
    );
    await mockBatchesServerCall(
      "GET",
      {
        ...batchDataAfterProcessing,
        column: "Annual Salary",
        batchSize: 7,
        isPartitioningColumnValid: true,
        onlyInitialLoad: true,
      },
      "getBatches"
    );
    await api.assertButton({ button: `${EDIT_BATCHES_DIALOG}--saveBatches` });
    await api.clickButton(`${EDIT_BATCHES_DIALOG}--saveBatches`);
    await api.assertText({
      id: `${DI_PAGE}--batchColumnText`,
      value: "Annual Salary",
    });
    await api.assertText({
      id: `${DI_PAGE}--batchSizeText`,
      value: "7",
    });
  });

  it("should show error message strip for invalid partitioning", async () => {
    await openBatchesTab({ ...batchDataAfterProcessing, isPartitioningColumnValid: false });
    await api.assertControlExists({
      id: `${DI_PAGE}--invalidBatchesStrip`,
    });
  });

  it("should disable save partition when no columns are available for partitioning", async () => {
    const noColumnsBatchData = { ...batchDataBeforeProcessing };
    noColumnsBatchData.partitioningColumns = {};
    await openBatchesTab(noColumnsBatchData);
    await api.assertButton({ button: `${DI_PAGE}--defineBatchesBtn` });
    await api.clickButton(`${DI_PAGE}--defineBatchesBtn`);
    await api.assertButton({ button: `${EDIT_BATCHES_DIALOG}--saveBatches`, enabled: false });
  });
});
