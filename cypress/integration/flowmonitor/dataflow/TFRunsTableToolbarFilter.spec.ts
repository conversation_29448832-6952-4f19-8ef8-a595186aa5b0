/** @format */

import { getAPIInstance } from "../../../pageobjects/api/IAPI";
import { APIImpl } from "../../../pageobjects/api/impl/APIImpl";
import {
  DI_PAGE,
  getMonitoringIds,
  navigateBack,
  openTFDetails,
  OptionalRoutesMocks,
  RUNS_TABLE,
  setDefaultMock,
  setTFStatusMock,
  TF_MESSAGES_TABLE,
  TWO_TENANT_FF,
} from "./Util";

enum ORDERBY {
  ASSENDING = 1,
  DESCENDING,
}

enum SORTBY {
  START = 1,
  ACTIVITY,
  STATUS,
}

enum FILTERBY {
  COMPLETED,
  FAILED,
  RUNNING,
}

describe.skip("cypress/integration/flowmonitor/dataflow/RunsTableToolbarFilter", () => {
  let api: APIImpl;

  beforeEach(async () => {
    api = getAPIInstance();
    await setDefaultMock(api, [OptionalRoutesMocks.COMMON_ROUTE], [TWO_TENANT_FF]);
    await setTFStatusMock(api);
  });

  describe("{transformationflowmonitordetails} runs table", () => {
    it("should show runs table entries", async () => {
      await openTFDetails(api, true, 7);
      await api.assertTable({
        table: RUNS_TABLE,
        rows: 4,
      });
      await api.clickListItem(`${DI_PAGE}--runsTable`, 0, true);
      await api.assertControlExists({
        id: TF_MESSAGES_TABLE,
      });
      await api.assertTable({
        table: TF_MESSAGES_TABLE as any,
        rows: 3,
        timeout: 10,
      });
    });
  });

  describe("{transformationflowmonitordetails} filter table", () => {
    it("By Status", async () => {
      await filterTFRunTableByStatus(api, FILTERBY.RUNNING);
      await api.assertTable({
        table: RUNS_TABLE,
        rows: 1,
        timeout: 10,
      });
    });
  });

  describe("{transformationflowmonitordetails} filter & sort should be reset on going back", () => {
    it("By sort", async () => {
      await sortTFRunTable(api, ORDERBY.DESCENDING, SORTBY.STATUS);
      expect(await getTableItems(api, 3, 2)).to.eq("TXT_COMPLETED");
      await navigateBack(api);
      const { MONITORING_DI_PAGE } = getMonitoringIds([], false);
      const dataFlowTable = await cy.byId(`${MONITORING_DI_PAGE}--flowTable`);
      const tableRows = await dataFlowTable.getRows();
      await tableRows[7].getRowAction().getItems()[0].firePress();
      await api.XHRWait("@gettransformationflowDetails");
      await api.assertTable({
        table: `${DI_PAGE}--runsTable` as any,
        rows: 4,
        timeout: 10,
      });
      expect(await getTableItems(api, 0, 2)).to.eq("TXT_COMPLETED");
    });
  });
});

async function sortTFRunTable(api: APIImpl, orderBy: number, sortBy: number) {
  await openTFDetails(api, true, 7);
  // Check for sort button and click it
  await api.assertButton({
    button: `${DI_PAGE}--runsTableSortBtn`,
    visible: true,
  });
  await api.clickButton(`${DI_PAGE}--runsTableSortBtn`, true);
  cy.get("#TFRunsSortDialog--logTableSort-sortorderlist-listUl").eq(0).find("li").eq(orderBy).click();
  cy.get("#TFRunsSortDialog--logTableSort-sortlist-listUl").eq(0).find("li").eq(sortBy).click();
  await api.clickButton(`TFRunsSortDialog--logTableSort-acceptbutton`, true);
}

async function filterTFRunTableByStatus(api: APIImpl, filterBy: number) {
  await openTFDetails(api, true, 7);
  await api.assertButton({
    button: `${DI_PAGE}--runsTableFilterBtn`,
    visible: true,
  });
  await api.clickButton(`${DI_PAGE}--runsTableFilterBtn`, true);
  cy.get("#TFRunsFilterDialog--logTableFilter-filterlist-listUl").eq(0).find("li").eq(3).click();
  cy.get("[id*= 'TFRunsFilterDialog--logTableFilter-page2-cont'] div:nth-child(2) ul").find("li").eq(filterBy).click();
  await api.clickButton(`TFRunsFilterDialog--logTableFilter-acceptbutton`, true);
}

async function getTableItems(api, row: number, cell: number) {
  const table = await cy.byId(RUNS_TABLE);
  await api.assertTable({
    table: `${DI_PAGE}--runsTable` as any,
    rows: 4,
    timeout: 10,
  });
  return table.getItems()[row].getCells()[cell].getText();
}
