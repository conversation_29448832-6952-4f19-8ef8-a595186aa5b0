/** @format */
import { getAPIInstance } from "../../../pageobjects/api/IAPI";
import { APIImpl } from "../../../pageobjects/api/impl/APIImpl";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DI_PAGE, TWO_TENANT_FF, openLSATFDetails } from "./Util";
describe("cypress/integration/flowmonitor/dataflow/TransformationFlowLsaRunTimeSettings", () => {
  let api: APIImpl;

  beforeEach(async () => {
    api = getAPIInstance();
    const routes = [...XHelpers.DEFAULT_MOCK_REPONSES, ...XHelpers.SDP_SPECIFIC_MOCK_RESPONSES];
    routes.push({
      protocol: "GET",
      path: "**/userprivileges**",
      response: "fixture:dataflowmonitor/TFLsaUserprivileges",
      as: "getUpdatedUserPrivileges",
    });
    await api.setupBeforeEach(
      /* useMock*/ true,
      /* defaultFixture*/ true,
      /* routes*/ routes,
      /* overrideRouteCallback */ undefined,
      /* enabledFeatureFlags */ [TWO_TENANT_FF, "DWC_DUMMY_SPACE_PERMISSIONS"]
    );
    await api.mockServerCall("GET", "**/localTables*", { localTables: [] }, "getLocalTables");
  });

  describe("{transformationflowmonitor} LSA", () => {
    beforeEach(async () => {
      await setupMockCalls(api, "SPACE1234", "test_Tf", 898826); // Shared mock calls
      await openLSATFDetails(api, true, 7); // Common navigation to transformation flow details
    });

    it("Ability to run TrF with different remote source configurations", async () => {
      await api.assertTable({
        table: `${DI_PAGE}--runsTable` as any,
        rows: 1,
        timeout: 10,
      });
      await navigateToTFSettings(api);

      await api.selectSelectItem(`shellMainContent---dataIntegrationComponent---dataflowdetails--sparkSize`, 3);
      await mockExecutionModeSettings(api, "SPACE1234", "test_Tf", 0, "200");
      await assertAndClickButton(api, `${DI_PAGE}--spaceDefault`, true);

      await mockExecutionModeSettings(api, "SPACE1234", "test_Tf", 0, "");
      await assertAndClickButton(api, `${DI_PAGE}--defineSettings`, true);
    });
  });

  async function setupMockCalls(api, spaceId, testName, taskId) {
    const mockData = [
      { method: "GET", url: "**/schedules/consent", response: { consent: false }, alias: "getConsent" },
      { method: "GET", url: "**/schedules?**", response: [], alias: "getSchedules" },
      { method: "GET", url: "**/metrices?space=*", response: {}, alias: "getMetrices" },
      {
        method: "GET",
        url: `**/dataflow/status?space=${spaceId}**`,
        response: "fixture:dataflowmonitor/dataflows",
        alias: "getdataflows",
      },
      {
        method: "GET",
        url: `**/transformationflow/${spaceId}/status/${testName}**`,
        response: "fixture:dataflowmonitor/TFPlanVizLogs",
        alias: "gettransformationflowDetails",
      },
      {
        method: "GET",
        url: `**/transformationflow/${spaceId}/status/${testName}?taskId=${taskId}`,
        response: "fixture:dataflowmonitor/TFPlanVizTaskDetails",
        alias: "gettransformationflowTaskDetails",
      },
      {
        method: "GET",
        url: "**/resources/spaces?**",
        response: {
          spaceName: {
            memory: { assigned: 300000000, used: 216994806 },
            disk: { assigned: 1000000000, used: 937295872 },
            status: "ok",
            lockReason: null,
            statusValidToUtc: "",
            priority: 3,
            dataLakeEnabled: false,
            isLocked: false,
          },
        },
        alias: "spaceIsLocked",
      },
      {
        method: "GET",
        url: `**/transformationflow/${spaceId}/logs/${taskId}/messages/6/planviz`,
        response: "fixture:dataflowmonitor/blobContent",
        alias: "getBlobContent",
      },
      {
        method: "GET",
        url: "**/resources/spaces**",
        response: "fixture:dataflowmonitor/spaceResources",
        alias: "getSpaceResources",
      },
    ];

    for (const { method, url, response, alias } of mockData) {
      await api.mockServerCall(method, url, response, alias);
    }
  }

  async function navigateToTFSettings(api) {
    await api.clickButton(`${DI_PAGE}--masterRunsTableOPL-anchBar-${DI_PAGE}--tfSettings-anchor`);
    await api.clickDOM({
      domAttributes: [
        {
          attribute: "id",
          value: "shellMainContent---dataIntegrationComponent---dataflowdetails--defineSettings-label-text",
        },
      ],
      containsHTML: "defineSettings",
    });
  }

  async function assertAndClickButton(api, buttonSelector, isEnabled) {
    await api.assertButton({ button: buttonSelector, enabled: isEnabled });
    await api.clickDOM({
      domAttributes: [
        {
          attribute: "id",
          value: `${buttonSelector}-label-text`,
        },
      ],
      containsHTML: buttonSelector.split("--").pop(),
    });
  }

  async function mockExecutionModeSettings(api, spaceId, testName, executionMode, sparkRemoteSource) {
    await api.mockServerCall(
      "GET",
      `**/transformationflow/${spaceId}/runtimesettings/${testName}**`,
      {
        executionMode: executionMode,
        sparkRemoteSource: sparkRemoteSource,
      },
      "getExecutionModeSettings"
    );
  }
});
