/** @format */
// FILEOWNER: [Dataflow]

import { getAPIInstance } from "../../../pageobjects/api/IAPI";
import { APIImpl } from "../../../pageobjects/api/impl/APIImpl";
import {
  DI_PAGE,
  openTFDetails,
  OptionalRoutesMocks,
  RUNS_TABLE,
  setDefaultMock,
  setTFStatusMock,
  TWO_TENANT_FF,
} from "./Util";

enum ORDERBY {
  ASSENDING = 1,
  DESCENDING,
}

enum SORTBY {
  START = 1,
  ACTIVITY,
  STATUS,
}

describe.skip("cypress/integration/flowmonitor/dataflow/RunsTableToolbarSort", () => {
  let api: APIImpl;

  beforeEach(async () => {
    api = getAPIInstance();
    await setDefaultMock(api, [OptionalRoutesMocks.COMMON_ROUTE], [TWO_TENANT_FF]);
    await setTFStatusMock(api);
  });

  describe("{transformationflowmonitordetails} sort table", async () => {
    it("sort by start", async () => {
      await sortTFRunTable(api, ORDERBY.DESCENDING, SORTBY.START);
      expect(Date.parse(await getTableItems(api, 3, 0))).to.be.lessThan(Date.parse(await getTableItems(api, 0, 0)));
    });
    it("sort by status", async () => {
      await sortTFRunTable(api, ORDERBY.DESCENDING, SORTBY.STATUS);
      expect(await getTableItems(api, 0, 2)).to.eq("TXT_RUNNING");
    });
    it("sort on Status when subStatus FF is enabled", async () => {
      await sortTFRunTable(api, ORDERBY.DESCENDING, SORTBY.STATUS);
      expect(await getTableItems(api, 1, 2)).to.eq("TXT_FAILED (FAIL_CONSENT_NOT_AVAILABLE)");
    });
    it("sort on Activity", async () => {
      await sortTFRunTable(api, ORDERBY.ASSENDING, SORTBY.ACTIVITY);
      expect(await getTableItems(api, 0, 1)).to.eq("TXT_CANCEL");
    });
  });
});

async function sortTFRunTable(api: APIImpl, orderBy: number, sortBy: number) {
  await openTFDetails(api, true, 7);
  // Check for sort button and click it
  await api.assertButton({
    button: `${DI_PAGE}--runsTableSortBtn`,
    visible: true,
  });
  await api.clickButton(`${DI_PAGE}--runsTableSortBtn`, true);
  cy.get("#TFRunsSortDialog--logTableSort-sortorderlist-listUl").eq(0).find("li").eq(orderBy).click();
  cy.get("#TFRunsSortDialog--logTableSort-sortlist-listUl").eq(0).find("li").eq(sortBy).click();
  await api.clickButton(`TFRunsSortDialog--logTableSort-acceptbutton`, true);
}

async function getTableItems(api, row: number, cell: number) {
  const table = await cy.byId(RUNS_TABLE);
  await api.assertTable({
    table: `${DI_PAGE}--runsTable` as any,
    rows: 4,
    timeout: 10,
  });
  return table.getItems()[row].getCells()[cell].getText();
}
