/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DB_BUTTON, DB_INPUT, DB_LIST, DB_VIEW } from "../../../pageobjects/databuilder/descriptor";
import AppEmulator from "../common/appEmulator/AppEmulator";

describe.skip("cypress/integration/databuilder/ermodeler/ImportFromFile", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(/* useMock*/ true, /* defaultFixture*/ true, /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES);
  });

  describe("{erModeler} Import From CSN File spec", () => {
    /* jscpd:ignore-start */
    it("support csn file with csn entry", async function () {
      const erModeler = await AppEmulator.oldAPIAdapter.openNewERModeler(api, "SPACE1234");
      await api.waitHTMLStabilized(true);
      await api.assertBlocklayerIsDiasppeared();

      await erModeler.clickButton(DB_BUTTON.TOGGLE_TREE);

      await erModeler.clickButton(DB_BUTTON.ER_IMPORT);
      await erModeler.clickButton(DB_BUTTON.ER_IMPORT_FROM_CSN_FILE);
      await erModeler.clickButton(DB_BUTTON.ER_IMPORT_CSN_DIALOG_CANCEL);
      await api.waitHTMLStabilized(true);
      await api.assertBlocklayerIsDiasppeared();

      await erModeler.clickButton(DB_BUTTON.ER_IMPORT);
      await erModeler.clickButton(DB_BUTTON.ER_IMPORT_FROM_CSN_FILE);
      await erModeler.setFileUploader(
        DB_INPUT.IMPORTCSNDIALOG_FILEUPLOADER,
        "databuilder/ermodeler/importfile2",
        "test.json"
      );
      await erModeler.clickButton(DB_BUTTON.ER_IMPORT_CSN_DIALOG_NEXT);
      await erModeler.assertControlExists({ id: DB_VIEW.BUSY_INDICATOR_DIALOG });
      await erModeler.assertControlNotExists({ id: DB_VIEW.BUSY_INDICATOR_DIALOG });
      await api.waitHTMLStabilized(true);
      await erModeler.clickButton(DB_BUTTON.ER_IMPORT_CSN_DIALOG_SELECT_ALL);
      await erModeler.clickButton(DB_BUTTON.ER_IMPORT_CSN_DIALOG_OK);
      await erModeler.assertControlExists({ id: DB_VIEW.BUSY_INDICATOR_DIALOG });
      await erModeler.assertControlNotExists({ id: DB_VIEW.BUSY_INDICATOR_DIALOG });
      await api.waitHTMLStabilized(true);
      await erModeler.diagram.assertSymbol({
        id: "Table/Categories",
      });
      await erModeler.diagram.selectSymbol({ id: "Table/Categories" }, true);
    });

    it("Replace with csn file when non selected dependencies exist in repository", async function () {
      const erModeler = await AppEmulator.oldAPIAdapter.openNewERModeler(api, "SPACE1234");
      await api.waitHTMLStabilized(true);
      await api.assertBlocklayerIsDiasppeared();

      await erModeler.clickButton(DB_BUTTON.TOGGLE_TREE);

      await erModeler.clickButton(DB_BUTTON.ER_IMPORT);
      await erModeler.clickButton(DB_BUTTON.ER_IMPORT_FROM_CSN_FILE);
      await erModeler.setFileUploader(
        DB_INPUT.IMPORTCSNDIALOG_FILEUPLOADER,
        "databuilder/ermodeler/importfileDependency",
        "test.json"
      );
      await erModeler.clickButton(DB_BUTTON.ER_IMPORT_CSN_DIALOG_NEXT);
      await erModeler.assertControlExists({ id: DB_VIEW.BUSY_INDICATOR_DIALOG });
      await erModeler.assertControlNotExists({ id: DB_VIEW.BUSY_INDICATOR_DIALOG });
      await api.waitHTMLStabilized(true);
      await erModeler.selectTableRow(DB_BUTTON.ER_ADD_FROM_REPOSITORY_TABLE, 2);
      await erModeler.clickButton(DB_BUTTON.ER_IMPORT_CSN_DIALOG_OK);
      await api.waitHTMLStabilized(true);

      // Click YES - replace by object in CSN file, not use repository object
      await erModeler.confirmDialog(DB_VIEW.CONFIRM_IMPORT_CSN_DIALOG, "MSGBOX_YES");
      await erModeler.diagram.selectSymbol({ id: "Table/table_2" }, true);
      await api.waitHTMLStabilized(true);
      await erModeler.diagram.selectSymbol({ id: "Table/table_1" }, true);
      await api.waitHTMLStabilized(true);
      await erModeler.properties.assertList({
        list: DB_LIST.ER_ENTITY_COLUMNS_LIST,
        rows: 3,
      });

      // Add a new column and import again, the new column will be removed
      await erModeler.diagram.clickContextPad(DB_BUTTON.ER_ADD_COLUMN);
      await erModeler.properties.assertList({
        list: DB_LIST.ER_ENTITY_COLUMNS_LIST,
        rows: 4,
      });
      await erModeler.clickButton(DB_BUTTON.ER_IMPORT);
      await erModeler.clickButton(DB_BUTTON.ER_IMPORT_FROM_CSN_FILE);
      await erModeler.setFileUploader(
        DB_INPUT.IMPORTCSNDIALOG_FILEUPLOADER,
        "databuilder/ermodeler/importfileDependency",
        "test.json"
      );
      await erModeler.clickButton(DB_BUTTON.ER_IMPORT_CSN_DIALOG_NEXT);
      await erModeler.assertControlExists({ id: DB_VIEW.BUSY_INDICATOR_DIALOG });
      await erModeler.assertControlNotExists({ id: DB_VIEW.BUSY_INDICATOR_DIALOG });
      await api.waitHTMLStabilized(true);
      await erModeler.selectTableRow(DB_BUTTON.ER_ADD_FROM_REPOSITORY_TABLE, 0); // table_1
      await erModeler.clickButton(DB_BUTTON.ER_IMPORT_CSN_DIALOG_OK);
      await erModeler.assertControlExists({ id: DB_VIEW.BUSY_INDICATOR_DIALOG });
      await erModeler.assertControlNotExists({ id: DB_VIEW.BUSY_INDICATOR_DIALOG });
      await api.waitHTMLStabilized(true);
      await erModeler.diagram.selectSymbol({ id: "Table/table_1" }, true);
      await api.waitHTMLStabilized(true);
      await erModeler.properties.assertList({
        list: DB_LIST.ER_ENTITY_COLUMNS_LIST,
        rows: 3,
      });
    });
    /* jscpd:ignore-end */
  });
});
