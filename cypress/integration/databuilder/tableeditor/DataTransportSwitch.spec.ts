/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DataBuilder } from "../../../pageobjects/databuilder";
import { OUTPUT_TYPES } from "../../../pageobjects/databuilder/DBPropertiesView";
import { DB_BUTTON } from "../../../pageobjects/databuilder/descriptor";
import { ShellPages } from "../../../pageobjects/shell";

describe("cypress/integration/databuilder/tableeditor/DataTransportSwitch", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(
      /* useMock*/ true,
      /* defaultFixture*/ true,
      /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES,
      undefined,
      []
    );
  });

  describe("{tableEditor} Data Transport Switch Tests", () => {
    it("Should show data transport switch for DIMENSION data category when feature flag is enabled", async function () {
      // Enable the feature flag
      await api.enableUIFeatureflag("DWCO_BDC_REPOSITORY_TRANSPORT_DATA");

      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const tableEditor = await databuilder.landingPage.createNewTableEditor();

      // Set data category to DIMENSION
      await tableEditor.selectSelectItem(DB_BUTTON.TE_TYPE_SELECTOR, OUTPUT_TYPES.Dimension);

      // Verify the data transport switch is visible
      await tableEditor.assertControlExists({
        id: DB_BUTTON.ALLOW_DATA_TRANSPORT_SWITCH,
      });

      // Verify the switch is initially OFF (false)
      await tableEditor.assertSwitch({
        id: DB_BUTTON.ALLOW_DATA_TRANSPORT_SWITCH,
        state: false,
      });
    });

    it("Should hide data transport switch for non-DIMENSION/TEXT data categories", async function () {
      // Enable the feature flag
      await api.enableUIFeatureflag("DWCO_BDC_REPOSITORY_TRANSPORT_DATA");

      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const tableEditor = await databuilder.landingPage.createNewTableEditor();

      // Set data category to RelationalDataSet (FACT)
      await tableEditor.selectSelectItem(DB_BUTTON.TE_TYPE_SELECTOR, OUTPUT_TYPES.RelationalDataSet);

      // Verify the data transport switch is not visible
      await tableEditor.assertControlNotExists({
        id: DB_BUTTON.ALLOW_DATA_TRANSPORT_SWITCH,
      });
    });

    it("Should send correct CSN annotation when switch is ON and data category is DIMENSION", async function () {
      // Enable the feature flag
      await api.enableUIFeatureflag("DWCO_BDC_REPOSITORY_TRANSPORT_DATA");

      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const tableEditor = await databuilder.landingPage.createNewTableEditor();
      await api.wait(2);

      // Set data category to DIMENSION
      await tableEditor.selectSelectItem(DB_BUTTON.TE_TYPE_SELECTOR, OUTPUT_TYPES.Dimension);

      // Turn ON the data transport switch
      await api.setStateSwitch({
        id: DB_BUTTON.ALLOW_DATA_TRANSPORT_SWITCH,
        state: true,
      });

      // Mock the save API call
      await api.mockServerCall("POST", "**/repository/objects/", "{}", "save", 201);

      // Save the table
      await tableEditor.save();
      await api.wait(1);
      await api.pressButton("sap-cdw-components-abstractbuilder-view-validations--dialog--view--ok");
      await api.wait(2);
      await api.pressButton("saveModelDialog--saveButton");

      // Verify the API payload contains the correct annotation
      await api.assertServerRequestParameters("@save", undefined, {
        containsText: '"@DataWarehouse.dataTransport":"allowed"',
      });
    });
  });
});
