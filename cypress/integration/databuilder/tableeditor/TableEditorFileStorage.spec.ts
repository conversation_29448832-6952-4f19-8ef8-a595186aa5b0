/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { IAPI } from "../../../pageobjects/api/IAPI";
import {
  assertControl,
  assertSwitch,
  assertSwitchState,
  prepareTestEnvironment,
  pressButton,
  setTableCheckBoxAI,
  typeTextInput,
} from "../../../pageobjects/dataIntegration/DataIntegrationHelper";
import { DB_BUTTON } from "../../../pageobjects/databuilder/descriptor";

describe("cypress/integration/databuilder/tableeditor/TableEditorFileStorage", () => {
  let api: IAPI;

  const readPrivileges = {
    spaces: {
      SPACE1234: [
        {
          type: "DWC_BUSINESS_ENTITY",
          privileges: ["create", "read", "update", "delete"],
        },
        {
          type: "DELETEDFILE",
          privileges: ["assign"],
        },
        {
          type: "DWC_DATAINTEGRATION",
          privileges: ["read", "update", "execute"],
        },
        {
          type: "CONTENTOWNERSHIP",
          privileges: ["execute"],
        },
        {
          type: "DWC_CONSUMPTION",
          privileges: ["read", "update", "execute"],
        },
        {
          type: "DWC_REMOTECONNECTION",
          privileges: ["create", "read", "update", "delete", "share"],
        },
        {
          type: "DWC_GENERAL",
          privileges: ["read"],
        },
        {
          type: "TRANSLATION",
          privileges: ["create", "read", "delete"],
        },
        {
          type: "USER",
          privileges: ["read"],
        },
        {
          type: "DWC_DAC",
          privileges: ["create", "read", "update", "delete"],
        },
        {
          type: "DWC_FOLDER",
          privileges: ["create", "update", "delete"],
        },
        {
          type: "DWC_BUSINESSCATALOG",
          privileges: ["create", "read", "update", "delete"],
        },
        {
          type: "DWC_CONSUME_MODEL",
          privileges: ["create", "read", "update", "delete"],
        },
        {
          type: "PROFILE",
          privileges: ["read"],
        },
        {
          type: "DWC_BUSINESSBUILDER",
          privileges: ["create", "read", "update", "delete"],
        },
        {
          type: "SCOPEROLEUSERASSIGN",
          privileges: ["assign"],
        },
        {
          type: "DWC_DATABUILDER",
          privileges: ["create", "read", "update", "delete", "share"],
        },
        {
          type: "DWC_SPACES",
          privileges: ["read", "update", "delete"],
        },
        {
          type: "PROFILETEMPLATE",
          privileges: ["read"],
        },
        {
          type: "DWC_AUTH_SCENARIO",
          privileges: ["create", "read", "update", "delete"],
        },
        {
          type: "LIFECYCLE",
          privileges: ["read", "maintain", "share"],
        },
        {
          type: "TEAM",
          privileges: ["read", "update", "delete"],
        },
        {
          type: "DWC_FACT_MODEL",
          privileges: ["create", "read", "update", "delete"],
        },
        {
          type: "DWC_SPACEFILE",
          privileges: ["create", "read", "update", "delete"],
        },
      ],
    },
    global: [
      {
        type: "PROFILE",
        privileges: ["create", "read", "update", "delete"],
      },
      {
        type: "PROFILETEMPLATE",
        privileges: ["read"],
      },
      {
        type: "USER",
        privileges: ["create", "read", "update", "delete", "assign"],
      },
      {
        type: "TEAM",
        privileges: ["create", "read", "update", "delete"],
      },
      {
        type: "AUDIT",
        privileges: ["read"],
      },
      {
        type: "SYSTEMINFO",
        privileges: ["read", "update"],
      },
      {
        type: "DELETEDFILE",
        privileges: ["assign"],
      },
      {
        type: "CONTENTOWNERSHIP",
        privileges: ["execute"],
      },
      {
        type: "DWC_GENERAL",
        privileges: ["read"],
      },
      {
        type: "DWC_SPACES",
        privileges: ["create", "read", "update", "delete", "assign"],
      },
      {
        type: "DWC_SPACEFILE",
        privileges: ["create", "read", "update", "delete", "assign"],
      },
      {
        type: "DWC_REMOTECONNECTION",
        privileges: ["create", "read", "update", "delete", "share"],
      },
      {
        type: "DWC_DATABUILDER",
        privileges: ["create", "read", "update", "delete", "share"],
      },
      {
        type: "DWC_BUSINESSCATALOG",
        privileges: ["create", "read", "update", "delete"],
      },
      {
        type: "STORY",
        privileges: ["create", "read", "update", "delete", "share"],
      },
      {
        type: "LIFECYCLE",
        privileges: ["read", "maintain", "share"],
      },
      {
        type: "DWC_BUSINESSBUILDER",
        privileges: ["create", "read", "update", "delete"],
      },
      {
        type: "DWC_BUSINESS_ENTITY",
        privileges: ["create", "read", "update", "delete"],
      },
      {
        type: "DWC_AUTH_SCENARIO",
        privileges: ["create", "read", "update", "delete"],
      },
      {
        type: "DWC_FACT_MODEL",
        privileges: ["create", "read", "update", "delete"],
      },
      {
        type: "DWC_CONSUME_MODEL",
        privileges: ["create", "read", "update", "delete"],
      },
      {
        type: "DWC_FOLDER",
        privileges: ["create", "update", "delete"],
      },
      {
        type: "DWC_DAC",
        privileges: ["create", "read", "update", "delete"],
      },
      {
        type: "OTHERDATASOURCE",
        privileges: ["execute"],
      },
      {
        type: "EXTERNALCONNECTION",
        privileges: ["create", "read", "update", "delete", "maintain"],
      },
      {
        type: "DWC_DATAINTEGRATION",
        privileges: ["read", "update", "execute"],
      },
      {
        type: "DWC_CONSUMPTION",
        privileges: ["read", "update", "execute"],
      },
      {
        type: "TRANSLATION",
        privileges: ["create", "read", "delete"],
      },
      {
        type: "DWC_RUNTIME",
        privileges: ["read", "execute"],
      },
      {
        type: "DASHBOARD",
        privileges: ["create", "read", "update", "delete", "share"],
      },
      {
        type: "VISUALIZATION",
        privileges: ["create", "read", "update", "delete"],
      },
      {
        type: "VIDEO_DATA_STORY",
        privileges: ["create", "read", "update", "delete", "share"],
      },
    ],
    spaceCapabilities: { SPACE1234: ["hdlfStorage"] },
  };
  beforeEach(() => {
    prepareTestEnvironment({
      DWC_DUMMY_SPACE_PERMISSIONS: true,
    });
    cy.intercept("GET", "**/customtenantclassification", {
      active: false,
      type: "development",
      colorName: "systemInfoDevelopmentColor",
      title: "",
    });
    cy.intercept("GET", "**/configuration-status", { isConfigurationNeeded: false });
    cy.intercept("GET", "**/userprivileges", readPrivileges);
    cy.intercept("GET", "**/repository/spaces**", { fixture: "spacesResponse.json" }).as("getSpaces");
    cy.intercept("GET", "**designObjects**", { results: [] }).as("designObjects");
    cy.intercept("GET", "**/repository/shares?spaceName=SPACE1234", {}).as("getShares");
    cy.intercept("GET", "/hasData", { hasData: false }).as("hasData");
    cy.intercept("GET", "**/repository/remotes**", "fixture:connectivity/connections").as("getConnections");
  });

  describe("{tableEditor} DeltaCapture LocalTable ", () => {
    beforeEach(() => {
      const routeUrl = `#/databuilder&/db/SPACE1234`;
      cy.visit(routeUrl, { skipResourceBundle: true });
      cy.wait(["@getContractEnddate", "@getSpaces"]);
      cy.wait(3000);
    });

    it.skip("check for Local Table delete Records through task frame work", function () {
      pressButton(
        "shellMainContent---databuilderComponent---databuilderLandingPage--entitySelectionLandingPage--createTableTile"
      );
      cy.wait(1000);
      assertControl("shellMainContent---databuilderComponent---databuilderWorkbench--hierarchy", "not.exist");
      assertControl(DB_BUTTON.TE_UPLOAD_DATA, "not.exist");
      assertSwitch("tableEditor--deltaCapture", false);
      assertSwitchState("tableEditor--deltaCapture", true);
      assertControl("tableEditor--addAttributeButton");
      pressButton("tableEditor--addAttributeButton");
      pressButton("tableEditor--addAttributeButton");
      pressButton("tableEditor--addAttributeButton");
      cy.wait(1000);
      setTableCheckBoxAI("tableEditor--attributesTable", 3);
      cy.wait(1000);
      pressButton("tableEditor--DefinePartition");
      assertControl("ltfPartitionPopup-dialog", "exist");
      pressButton("ltfPartitionPopup-confirmBtn");
      typeTextInput("tableEditor--businessNameInput", "TABLE_BUSINESS");
      typeTextInput("tableEditor--technicalNameInput", "Table-LTF%#-1");
      pressButton("tableEditor--tableEditorPage-anchBar-tableEditor--dependentObjectSection-anchor");
    });
  });
});
