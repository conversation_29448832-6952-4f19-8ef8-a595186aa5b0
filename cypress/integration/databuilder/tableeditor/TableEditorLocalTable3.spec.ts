/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { IAPI } from "../../../pageobjects/api/IAPI";
import { selectTableRow } from "../../../pageobjects/businessbuilder/BusinessbuilderHelper";
import {
  assertControl,
  assertSwitch,
  changeSwitchState,
  prepareTestEnvironment,
  pressButton,
} from "../../../pageobjects/dataIntegration/DataIntegrationHelper";
import { DB_BUTTON } from "../../../pageobjects/databuilder/descriptor";

describe("cypress/integration/databuilder/tableeditor/TableEditorLocalTable3", () => {
  let api: IAPI;
  beforeEach(() => {
    prepareTestEnvironment({
      DWC_DUMMY_SPACE_PERMISSIONS: false,
    });
    cy.intercept("GET", "**/customtenantclassification", {
      active: false,
      type: "development",
      colorName: "systemInfoDevelopmentColor",
      title: "",
    });
    cy.intercept("GET", "**/configuration-status", { isConfigurationNeeded: false });
    cy.intercept("GET", "**/userprivileges", { fixture: "DWConsumptionUpdatePrivileges.json" }).as("getUserPrivileges");
    cy.intercept("GET", "**/repository/spaces**", { fixture: "spacesResponse.json" }).as("getSpaces");
    cy.intercept("GET", "**designObjects**", { results: [] }).as("designObjects");
    cy.intercept("GET", "**/repository/shares?spaceName=SPACE1234", {}).as("getShares");
    cy.intercept("GET", "/hasData", { hasData: false }).as("hasData");
    cy.intercept("GET", "**/repository/remotes**", "fixture:connectivity/connections").as("getConnections");
  });

  describe("{tableEditor} DeltaCapture LocalTable ", () => {
    beforeEach(() => {
      const routeUrl = `#/databuilder&/db/SPACE1234`;
      cy.visit(routeUrl, { skipResourceBundle: true });
      cy.wait(["@getContractEnddate", "@getSpaces"]);
      cy.wait(3000);
    });

    it.skip("check for Local Table delete Records through task frame work", function () {
      pressButton(
        "shellMainContent---databuilderComponent---databuilderLandingPage--entitySelectionLandingPage--createTableTile"
      );
      cy.wait(1000);
      assertControl("shellMainContent---databuilderComponent---databuilderWorkbench--hierarchy", "exist");
      assertControl(DB_BUTTON.TE_UPLOAD_DATA, "not.exist");
      assertSwitch("tableEditor--deltaCapture", true);
      changeSwitchState("tableEditor--deltaCapture", true);
      pressButton("tableEditor--addAttributeButton");
      pressButton("tableEditor--addAttributeButton");
      pressButton("tableEditor--addAttributeButton");
      pressButton("tableEditor--tableEditorPage-anchBar-tableEditor--measuresSection-anchor");
      selectTableRow("tableEditor--attributesTable", 2);
      pressButton("tableEditor--deleteAttributeButton");
      pressButton("tableEditor--tableEditorPage-anchBar-tableEditor--steampunkServicesSection-anchor");
      pressButton("shellMainContent---databuilderComponent---databuilderWorkbench--detailsView");
    });
  });
});
