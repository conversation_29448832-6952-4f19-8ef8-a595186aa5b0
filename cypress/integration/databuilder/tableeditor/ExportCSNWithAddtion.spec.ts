/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DataBuilder } from "../../../pageobjects/databuilder";
import { ShellPages } from "../../../pageobjects/shell";

describe("Export CSN file with addition", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(
      /* useMock*/ true,
      /* defaultFixture*/ true,
      /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES,
      (route) => {
        return route;
      },
      []
    );
  });

  describe("{tableEditor} Export CSN file with addition", () => {
    it.skip("Export I18N section", async function () {
      await api.mockServerCall(
        "GET",
        "**/repository/designObjects**",
        "fixture:databuilder/objectsWithI18N",
        "designObjects"
      );
      // await api.mockServerCall("GET", "**/hasdata", { "hasData": false }, "hasData");

      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      await databuilder.landingPage.openModel("table_1");
      const oModeler = await databuilder.landingPage.openTableEditor();

      // Check CSN data
      const activeEditor = oModeler.controller.activeEditor;
      const oCSN = await activeEditor?.onExportCSN(oModeler.controller?.spaceId, true);
      assert.equal(oCSN?.i18n !== undefined, true, "Check CSN data");
    });

    it.skip("Export businessLayerDefinition", async function () {
      await api.disableUIFeatureflag("DWCO_INFRA_REPOSITORY_EXTENSION_CORE");
      await api.mockServerCall(
        "GET",
        "**/repository/designObjects**",
        "fixture:databuilder/objectsWithBusinessLayer",
        "designObjects"
      );

      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      await databuilder.landingPage.openModel("table_1");
      const oModeler = await databuilder.landingPage.openTableEditor();

      // Check CSN data
      const activeEditor = oModeler.controller.activeEditor;
      const oCSN = await activeEditor?.onExportCSN(oModeler.controller?.spaceId, true);
      assert.equal(oCSN?.businessLayerDefinitions !== undefined, true, "Check CSN data");
    });
  });
});
