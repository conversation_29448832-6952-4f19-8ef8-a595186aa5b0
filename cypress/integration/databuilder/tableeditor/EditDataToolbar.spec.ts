/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { AB_BUTTON } from "../../../pageobjects/abstractbuilder/descriptor";
import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { DataBuilder } from "../../../pageobjects/databuilder";
import { DB_BUTTON, DB_TABLE } from "../../../pageobjects/databuilder/descriptor";
import { ShellPages } from "../../../pageobjects/shell";
import { FEATURE_FLAGS_SET, NAMED_FEATURE_FLAGS } from "../common/serverEmulator/FeatureFlagsConstants";
import { documents } from "../common/serverEmulator/repository/documents";
import ServerEmulator from "../common/serverEmulator/ServerEmulator";
import { DWC_ROUTES, UITesting } from "../common/UITesting";

describe("cypress/integration/databuilder/tableeditor/EditDataToolbar", () => {
  let api: IAPI;

  beforeEach(() => {
    api = getAPIInstance();
    UITesting.setup({ debug: false });

    ServerEmulator.setupConfiguration({
      spaces: {
        SPACE: {
          // modeler by default
          objects: [documents.Cypress_SimpleER],
          computedProperties: [
            {
              documentName: "Cypress_SimpleTable",
              properties: {
                designObjects: {
                  "#deleteAccess": false,
                  "#deploymentExecutionStatus": "success",
                  "#objectStatus": "1",
                },
              },
            },
          ],
        },
        VIEWER: {
          objects: [documents.Cypress_SimpleER],
          config: {
            defaultRole: "databuilderviewer",
          },
        },
      },
      featureFlagsSet: FEATURE_FLAGS_SET.LEGACY,
      featureFlags: {
        ...NAMED_FEATURE_FLAGS.SDP,
      },
    });

    UITesting.visit({ route: DWC_ROUTES.home });
  });

  it.skip("Edit toolbar section hidden in edit data mode", async function () {
    ServerEmulator.FeatureFlags.setEnableUIFeatureFlags({
      DWCO_MODELING_COMPATIBILITY_CONTRACTS: true,
    });
    const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
    await api.openSpace("SPACE");
    await api.assertButton({
      button: DB_BUTTON.DBH_DELETE_ENTITY,
      enabled: false,
    });

    await databuilder.landingPage.openModel("Cypress_SimpleTable");
    let tableEditor = await databuilder.landingPage.openTableEditor();

    await tableEditor.pressButton(DB_BUTTON.MDM_EDIT_DATA_TOGGLE);

    await tableEditor.assertControlExists({
      id: DB_TABLE.MDM_PREVIEW_TABLE,
    });

    await tableEditor.assertControl({
      id: "__bar1-BarLeft",
      notContainsHTML: "edit",
    });
    await tableEditor.assertToolbar({
      items: [
        {
          button: AB_BUTTON.SAVEANDSAVEAS,
          visible: false,
        },
      ],
    });

    await api.openSpace("VIEWER");
    await api.assertButton({
      button: DB_BUTTON.DBH_DELETE_ENTITY,
      visible: false,
    });
    await databuilder.landingPage.openModel("Cypress_SimpleTable");
    tableEditor = await databuilder.landingPage.openTableEditor();
    await tableEditor.assertToolbar({
      items: [
        {
          button: DB_BUTTON.TOGGLE_DATAPREVIEW,
          visible: true,
        },
      ],
    });
  });
});
