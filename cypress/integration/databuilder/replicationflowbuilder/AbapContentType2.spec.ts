/** @format */

import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import {
  DB_BUTTON,
  DB_CONTROL,
  DB_INPUT,
  DB_LIST,
  DB_TABLE,
  DB_TEXT,
} from "../../../pageobjects/databuilder/descriptor";
import { openReplicationFlow, setDefaultMock } from "./Util";

describe("cypress/integration/databuilder/replicationflowbuilder/ABAPContentType2", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(true, true, XHelpers.DEFAULT_MOCK_REPONSES, undefined);
    await setDefaultMock(api);
  });

  describe("ABAP Content Type", () => {
    it("Assert cdc columns should not be deleted on changing of content type with confluent as target", async function () {
      await assertCDCColumnsAfterContentTypeChange(
        api,
        "Content_type_conflu_as_tar",
        ["__operation_type", "__sequence_number", "__timestamp"],
        ["string", "unit64", "timestamp"]
      );
    });

    it("Assert cdc columns should not be deleted on changing of content type with gbq as target", async function () {
      await assertCDCColumnsAfterContentTypeChange(
        api,
        "Content_type_gbq_as_tar",
        ["operation_flag", "recordstamp", "is_deleted"],
        ["string", "timestamp", "bool"]
      );
    });
    it("Assert cdc columns should not be deleted on changing of content type with dwc as target", async function () {
      await assertCDCColumnsAfterContentTypeChange(
        api,
        "Content_type_dwc_as_tar",
        ["change_date", "change_type"],
        ["timestamp", "string"]
      );
    });
  });
});

async function assertCDCColumnsAfterContentTypeChange(
  api: IAPI,
  replicationFlowName: string,
  columns: string[],
  types: string[]
) {
  const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", replicationFlowName);
  if (replicationFlowName == "Content_type_dwc_as_tar") {
    await api.clickButton(DB_BUTTON.MSGBOX_OK);
  }
  await replicationFlowEditor.selectSelectItem(DB_LIST.RF_CONTENT_TYPE, 1);
  await api.assertMessageBox({ text: "confirmChangeContentTypeMessage" });

  if (replicationFlowName == "Content_type_dwc_as_tar") {
    await api.clickButton(DB_BUTTON.MSGBOX_OK1);
  } else await api.clickButton(DB_BUTTON.MSGBOX_OK);

  await assertMultipleDatatypes(columns, types, replicationFlowEditor, true);
}

async function assertMultipleDatatypes(columns: string[], types: string[], replicationFlowEditor, onlyTarget: boolean) {
  for (let i = 0; i < columns.length; i++) {
    await AssertDatatype(columns[i], types[i], replicationFlowEditor, onlyTarget);
  }
}

async function AssertDatatype(columnName, dataType, replicationFlowEditor, onlyTarget: boolean) {
  await selectRowAndOpenTransformationDialogue(0, replicationFlowEditor);

  await replicationFlowEditor.assertControlExists({
    id: DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID,
  });

  await replicationFlowEditor.selectIconTabBarItem(DB_CONTROL.RF_TRANSFORMATION_DIALOG_ICONTAB_ID, "filter");

  await replicationFlowEditor.assertControlExists({
    id: DB_CONTROL.RF_TRANSFORMATION_DIALOG_FILTER_TAB_CONTENT,
  });

  await replicationFlowEditor.properties.typeTextMenuField(
    DB_INPUT.RF_TRANSFORMATION_DIALOG_ADD_TRANSFORMATION_NAME_INPUT_ID,
    "transformation1"
  );

  if (!onlyTarget) {
    await replicationFlowEditor.properties.typeTextMenuField(
      DB_INPUT.RF_TRANSFORMATION_FILTER_TOKEN_SEARCH_FIELD_ID,
      columnName
    );
    await replicationFlowEditor.clickButton(DB_BUTTON.RF_TRANSFORMATION_FILTER_COLUMN_INFO_BUTTON);
    await replicationFlowEditor.assertText({
      id: DB_TEXT.RF_DATE_TEXT_FILTER_TAB_COLUMN_INFO_POPOVER,
      value: dataType,
    });
  }

  await replicationFlowEditor.selectIconTabBarItem(DB_CONTROL.RF_TRANSFORMATION_DIALOG_ICONTAB_ID, "info");

  await replicationFlowEditor.properties.typeTextMenuField(
    DB_INPUT.RF_TRANSFORMATION_MAPPING_TAB_SEARCH_FIELD_ID,
    columnName
  );
  await replicationFlowEditor.assertTable({
    table: DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID,
    rows: 1,
  });
}

async function selectRowAndOpenTransformationDialogue(rowNum, replicationFlowEditor) {
  await replicationFlowEditor.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, rowNum);

  await replicationFlowEditor.assertButton({
    button: DB_BUTTON.RF_MAIN_CANVAS_ADD_TRANSFORMATION_BUTTON_ID,
    enabled: true,
  });
  await replicationFlowEditor.clickButton(DB_BUTTON.RF_MAIN_CANVAS_ADD_TRANSFORMATION_BUTTON_ID);

  await replicationFlowEditor.assertControlExists({
    id: DB_CONTROL.TRANSFORMATION_DIALOG_ID,
  });

  await replicationFlowEditor.selectIconTabBarItem(DB_CONTROL.RF_TRANSFORMATION_DIALOG_ICONTAB_ID, "info");
}
