/** @format */

import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DB_BUTTON, DB_CONTROL, DB_INPUT, DB_LIST, DB_TABLE } from "../../../pageobjects/databuilder/descriptor";
import {
  clickOnAddExpressionInFilterTab,
  searchItemInFilterTokenList,
  typeTextInFilterExpression,
} from "./TransformationDialogUtils";
import { openReplicationFlow, setDefaultMock } from "./Util";
import EditorUtils from "./Utils/RFEditorUtils";

describe("cypress/integration/databuilder/replicationflowbuilder/HANAFilter", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(
      /* useMock*/ true,
      /* defaultFixture*/ true,
      /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES,
      /* overrideRouteCallback */ undefined
    );
    await setDefaultMock(api);
  });
  describe("HANA Filter Tests", () => {
    it("Assert presence of all possible filters in projection for HANA as source RF", async function () {
      //Open RF with HANA cloud as source and HANA cloud as Target
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "RF_aks_HANA_cloud");

      await replicationFlowEditor.assertTable({
        table: DB_TABLE.RF_EDITOR_MAIN_CANVAS,
        rows: 1,
      });

      await api.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, 0);
      //  open projection Dialog
      await EditorUtils.openTransformationDialog(replicationFlowEditor);

      // Searching Filter
      await searchItemInFilterTokenList(replicationFlowEditor, "Column_4");

      await replicationFlowEditor.properties.selectToken(DB_LIST.RF_SOURCE_TABLE_ELEMENT_LIST, "Column_4", 0);

      // Add value to search text
      await typeTextInFilterExpression(replicationFlowEditor, "Global");
      await clickOnAddExpressionInFilterTab(replicationFlowEditor);

      const TRANSFORMATION_DIALOG_FILTER_TAB_EXP_LIST = DB_LIST.TRANSFORMATION_DIALOG_FILTER_TAB_EXP_LIST;

      await replicationFlowEditor.assertSelect({
        id: TRANSFORMATION_DIALOG_FILTER_TAB_EXP_LIST as any,
        selectedItemKey: "equal to",
      });

      await replicationFlowEditor.selectSelectItem(TRANSFORMATION_DIALOG_FILTER_TAB_EXP_LIST, 2);

      await replicationFlowEditor.assertSelect({
        id: TRANSFORMATION_DIALOG_FILTER_TAB_EXP_LIST as any,
        selectedItemKey: "less than or equal to",
      });
    });
  });
});
