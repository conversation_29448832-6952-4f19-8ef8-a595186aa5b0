/** @format */
import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DB_BUTTON, DB_CONTROL, DB_TABLE } from "../../../pageobjects/databuilder/descriptor";
import {
  assertValidationInsideProjectionDialog,
  clickEditContentCheckbox,
  saveTransformationDialog,
  switchToMappingTab,
  typeTransformationName,
} from "./TransformationDialogUtils";

import {
  assertAndClickDataset,
  assertValidationInReplicationObjectPanel,
  createReplicationFlow,
  openProjectionDialogFromToolbar,
  openReplicationFlow,
  setDefaultMock,
} from "./Util";
import ConnectionUtils from "./Utils/ConnectionUtils";
import ContainerUtils from "./Utils/ContainerUtils";
import EditorUtils from "./Utils/RFEditorUtils";

describe("cypress/integration/databuilder/replicationflowbuilder/CaseInsensitiveMapping", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(
      /* useMock*/ true,
      /* defaultFixture*/ true,
      /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES,
      /* overrideRouteCallback */ undefined
    );
    await setDefaultMock(api);
  });

  describe("Check Case insensitive Mapping ", () => {
    /**
     * @issueid DW101-93137
     */
    it("check mapping in case of case insensitive mapping and cdc columns", async function () {
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "mapping_priority");
      await addProjectionAndSwitchToMappingTab(0, replicationFlowEditor);
      await typeTransformationName(replicationFlowEditor, "transformation1");
      await replicationFlowEditor.clickButton(DB_BUTTON.RF_TRANSFORMATION_AUTO_MAP_BUTTON_ID);
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 0, 0, "column1");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 0, 1, "COLUMN1");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 3, 0, "__OPERATION_TYPE");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 3, 1, "__OPERATION_TYPE");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 1, 1, "COLUmn3");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 6, 1, "__operation_type");
      await assertValidationInsideProjectionDialog(replicationFlowEditor, 0, 0, 0, 0);
      await saveTransformationDialog(replicationFlowEditor);
    });
    it("check mapping in case of case insensitive mapping with dwc as target", async function () {
      // Open RF with datasets which has invalid chars in column names
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "RF_DB_AUTOPROJECT0");

      // Select Datasphere target and create new tables
      await ConnectionUtils.searchAndSelectTargetConnection(
        "SAP Datasphere",
        "SAP Datasphere",
        false,
        api,
        replicationFlowEditor,
        "RFSPACE"
      );
      await api.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, 0);
      await api.waitHTMLStabilized();
      await assertValidationInReplicationObjectPanel(replicationFlowEditor, {
        infoCount: 1,
        containsMessage: "validationAutoRenameTarget",
      });
      await openProjectionDialogFromToolbar(api, replicationFlowEditor);
      await switchToMappingTab(replicationFlowEditor);

      await replicationFlowEditor.assertTableCell(
        DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID,
        8,
        1,
        "AUTOPREFIX__StartUnderscore"
      );

      await assertValidationInsideProjectionDialog(replicationFlowEditor, 8, 0, 0, 8, "targetAutoRenameUpdated");
      await changeSourceMapping(2, 11, api, "Id");

      await typeInEditContentInput(3);
      await typeInInput(3, "DU_PLICAte_2");

      await typeInEditContentInput(9);
      await typeInInput(9, "DU_PLICAte");
      await replicationFlowEditor.clickButton(DB_BUTTON.RF_TRANSFORMATION_AUTO_MAP_BUTTON_ID);
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 0, "DU/PLICATE");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 9, 0, "DU_PLICATE");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 8, 0, "DU_PLICATE");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 8, 1, "DU_PLICAte");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 0, "DU/PLICATE");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 1, "DU_PLICAte_2");
      await assertValidationInsideProjectionDialog(replicationFlowEditor, 10, 2, 0, 8);
    });
    it("Check case insenstive mapping in case of gbq as target", async function () {
      const replicationFlowEditor = await createReplicationFlow(api, "RFSPACE");
      await createnewHANAGBQRF(
        replicationFlowEditor,
        api,
        "hanaGBQAutoprojection",
        "hanaGBQUnsupporteddataType",
        0,
        1,
        "AM_SAMPLE_FOR_GBQ_ALL_DATATYPE",
        "AM_SAMPLE_FOR_GBQ_AUTOPROJECTION_AUTO_TYPE"
      );
      await api.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, 0);
      await openProjectionDialogFromToolbar(api, replicationFlowEditor);
      await switchToMappingTab(replicationFlowEditor);

      await typeInEditContentInput(1);
      await typeInInput(1, "_NA_me");

      await typeInEditContentInput(2);
      await typeInInput(2, "_Na_me");

      await typeInEditContentInput(3);
      await typeInInput(3, "AUTOPREFIX__TABLE_TEAM");
      await replicationFlowEditor.clickButton(DB_BUTTON.RF_TRANSFORMATION_AUTO_MAP_BUTTON_ID);
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 0, 0, "/Na/me");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 0, 1, "_NA_me");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 1, 0, "/Na/me");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 1, 1, "_Na_me");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 0, "_TABLE_Team");
      await replicationFlowEditor.assertTableCell(
        DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID,
        2,
        1,
        "AUTOPREFIX__TABLE_TEAM"
      );
      await assertValidationInsideProjectionDialog(replicationFlowEditor, 12, 2, 0, 10);
    });
    /**
     * @issueid DW101-98780
     * @format */
    it("check mapping in case of case insensitive mapping and primary key", async function () {
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "case_ins_bug");
      await addProjectionAndSwitchToMappingTab(0, replicationFlowEditor);
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 1, 0, "empty");
    });
  });
});

async function addProjectionAndSwitchToMappingTab(rowNum, replicationFlowEditor) {
  let mainCanvasRowIndex = rowNum;
  await replicationFlowEditor.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, mainCanvasRowIndex);

  //  Assert Add Transformation Enabled and Click on Transformation Button
  await EditorUtils.openTransformationDialog(replicationFlowEditor);

  //  Open Transformation Dialog and change to mapping tab and add new row
  await switchToMappingTab(replicationFlowEditor);
}
async function assertValidations(
  replicationFlowEditor,
  checkboxIndex: number,
  messageCount: number,
  errorCount?: number,
  warnCount?: number,
  infoCount?: number,
  message?: string,
  messageNotContained?: string
) {
  await clickEditContentCheckbox(checkboxIndex, replicationFlowEditor);
  await assertValidationInsideProjectionDialog(
    replicationFlowEditor,
    messageCount,
    errorCount,
    warnCount,
    infoCount,
    message,
    messageNotContained
  );
}

async function changeSourceMapping(rowInd: number, listItem: number, api: IAPI, text: string) {
  await api.clickChildInTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, rowInd, 0, 0);
  await cy
    .byId(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID)
    .get(".sapMSelectList")
    .filter(`:contains("${text}")`)
    .should("have.css", "visibility", "visible")
    .children()
    .eq(listItem)
    .click({ force: true });
}

async function typeInEditContentInput(rowNo: number) {
  await cy
    .byId(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID)
    .get("table > tbody > tr")
    .eq(rowNo)
    .find("td")
    .eq(1)
    .find("input")
    .should("have.class", "sapMInputBaseInner")
    .clear();
}

async function typeInInput(rowNo: number, textToInsert: string) {
  await cy
    .byId(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID)
    .get("table > tbody > tr")
    .eq(rowNo)
    .find("td")
    .eq(1)
    .find("input")
    .should("have.class", "sapMInputBaseInner")
    .type(textToInsert);
}
async function createnewHANAGBQRF(
  replicationFlowEditor,
  api,
  gbqDatasetName1,
  gbqDatasetName2,
  chkbxNum1,
  chkbxNum2,
  dataset1,
  dataset2
) {
  await replicationFlowEditor.assertControlExists({
    id: DB_CONTROL.RF_WELCOME_PANEL,
  });
  await ConnectionUtils.searchAndSelectSourceConnection(
    "SAPHANAOpConnectionForRF",
    "SAPHANAOpConnectionForRF (HANA)",
    false,
    api,
    replicationFlowEditor
  );
  await replicationFlowEditor.assertButton({
    button: DB_BUTTON.RF_WELCOME_PANEL_ADD_SOURCE_CONTAINER_BUTTON_ID,
    enabled: true,
  });
  //Source Container
  await ContainerUtils.searchAndSelectSourceContainer(
    "TEST_CAFE_ADMIN",
    "/TEST_CAFE_ADMIN",
    false,
    api,
    replicationFlowEditor
  );
  await api.waitHTMLStabilized();
  cy.intercept("GET", "**/datasources/getchildren**", (req) => {
    req.reply({
      fixture: "databuilder/replicationflowbuilder/hanaGBQtestObjects",
    });
  }).as("getContainerFromGetChildren");
  await replicationFlowEditor.clickButton(DB_BUTTON.RF_ADD_SOURCE_DATASET_BUTTON);
  await api.XHRWait("@getContainerFromGetChildren");

  await replicationFlowEditor.assertControlExists({
    id: DB_TABLE.RF_ADD_SOURCE_DATASET_OBJECT_TABLE_ID,
  });

  //Selecting Datasets
  await assertAndClickDataset(chkbxNum1, `${dataset1}`, api);
  await assertAndClickDataset(chkbxNum2, `${dataset2}`, api);
  await replicationFlowEditor.assertButton({
    button: DB_BUTTON.ER_REMOTE_OBJECTS_SELECTOR_OK,
    enabled: true,
  });
  await replicationFlowEditor.clickButton(DB_BUTTON.ER_REMOTE_OBJECTS_SELECTOR_OK);
  await replicationFlowEditor.assertButton({
    button: DB_BUTTON.RF_IMPORT_OBJECTS_FROM_CONNECTION_ADD_SELECTION_BUTTON,
    enabled: true,
  });
  let getSCNCall = 2;

  await cy
    .intercept("GET", "**/datasources/getcsndefinition**", (req) => {
      if (getSCNCall % 2 === 0) {
        req.reply({
          fixture: `databuilder/replicationflowbuilder/${gbqDatasetName1}`,
        });
      } else {
        req.reply({
          fixture: `databuilder/replicationflowbuilder/${gbqDatasetName2}`,
        });
      }
      getSCNCall++;
    })
    .as("getSCN");

  // Following click will fail, if no await
  await replicationFlowEditor.clickButton(DB_BUTTON.RF_IMPORT_OBJECTS_FROM_CONNECTION_ADD_SELECTION_BUTTON);
  await api.XHRWait("@getSCN");
  await api.waitHTMLStabilized();
  await api.XHRWait("@getSCN");
  //Target connection
  await ConnectionUtils.searchAndSelectTargetConnection(
    "GBQ",
    "GBQ (BIGQUERY)",
    true,
    api,
    replicationFlowEditor,
    "RFSPACE"
  );
  await ContainerUtils.searchAndSelectTargetContainer("RMS", "/RMS", true, api, replicationFlowEditor);
}
