/** @format */

import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import {
  DB_BUTTON,
  DB_CONTROL,
  DB_INPUT,
  DB_LIST,
  DB_TABLE,
  DB_TEXT,
} from "../../../pageobjects/databuilder/descriptor";
import { ReplicationFlowEditor } from "../../../pageobjects/databuilder/ReplicationFlowEditor";
import {
  addRowInMappingTab,
  assertValidationInsideProjectionDialog,
  cancelTransformationDialog,
  checkStateOfAddRemUpDownButtons,
  selectRowInMappingTab,
  switchToMappingTab,
} from "./TransformationDialogUtils";
import {
  assertAndClickDataset,
  checkTargetObjectName,
  createReplicationFlow,
  mapToExistingTargetForDWCTarget,
  openReplicationFlow,
  renameDataset,
  setDefaultMock,
} from "./Util";
import ConnectionUtils from "./Utils/ConnectionUtils";
import EditorUtils from "./Utils/RFEditorUtils";
describe("cypress/integration/databuilder/replicationflowbuilder/ABAPSourceWithoutPK.spec.ts", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(
      /* useMock*/ true,
      /* defaultFixture*/ true,
      /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES,
      /* overrideRouteCallback */ undefined,
      /* enabledFeatureFlags */ []
    );
    await setDefaultMock(api);
    await api.mockServerCall(
      "POST",
      "**/repository/objects**",
      "fixture:databuilder/replicationflowbuilder/hanaHdlSaveObjectCall",
      "afterSaveObjects"
    );
  });

  describe("ABAP Source without Primary Key with DPID Column ", () => {
    it("Check DPID Column for Datasphere target (new)", async function () {
      //  Opening RF with source ABAP and add datasets without keys and add datasphere target
      const replicationFlowEditor = await openRF(api);
      await addCDSDatasets(replicationFlowEditor, api, "cdsNoKeyForHANATarget");
      await ConnectionUtils.searchAndSelectTargetConnection(
        "SAP Datasphere",
        "SAP Datasphere",
        false,
        api,
        replicationFlowEditor,
        "RFSPACE"
      );
      /**
       * @issueid DW101-77191
       */
      // Toast for DPID Column of 1 objects
      // DPID Column added in property panel
      // Auto-projection added due to name conflict with __load_package_id
      await replicationFlowEditor.assertMessageToast({
        text: "messageForToastForDPIDColumn2",
      });
      const tableIndex = 0;
      await checkTargetObjectName(tableIndex, "I_BPDATACONTROLLERUSAGE (3)", replicationFlowEditor, true);
      const mapCount1 = DB_CONTROL.RF_CANVAS_TRANSFORM_TOKEN_MAPPINGTEXT_PREFIX + tableIndex;
      await replicationFlowEditor.assertText({
        id: mapCount1 as any,
        value: "1",
      });
      await replicationFlowEditor.assertText({
        id: DB_CONTROL.RF_CANVAS_TRANSFORM_TAB as any,
        value: "transformationsTab (1)",
      });
      await replicationFlowEditor.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, tableIndex);
      await api.waitHTMLStabilized();
      await replicationFlowEditor.properties.assertText({
        id: DB_TEXT.RF_TARGET_COLUMN_PANEL,
        value: "targetColumns (3)",
      });
      await replicationFlowEditor.properties.assertListItemData({
        id: DB_LIST.RF_TARGET_COLUMNS_LIST as any,
        itemIndex: 2,
        path: "columnsModel>/columns/" + 2 + "/name",
        data: "__load_package_id",
      });
      await assertValidationInPanel("newTarget", "info", 1, "validationAutoRenameTarget", replicationFlowEditor);
      // Turn on Delta Capture and check validation error for delta capture on and delta loadtype, and removed when fixed
      await replicationFlowEditor.assertButton({
        button: DB_BUTTON.RF_DELTA_CAPTURE_SWITCH,
      });
      await replicationFlowEditor.clickButton(DB_BUTTON.RF_DELTA_CAPTURE_SWITCH);
      await assertValidationInPanel(
        "newTarget",
        "error",
        2,
        "sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget",
        replicationFlowEditor
      );
      /**
       * @issueid DW101-77712
       */
      // Save not allowed with Delta Capture error
      await replicationFlowEditor.save();
      await replicationFlowEditor.assertControlExists({
        id: DB_CONTROL.RF_VALIDATIONS_DIALOG_ID,
      });
      await replicationFlowEditor.properties.assertValidations({
        containerId: DB_CONTROL.RF_VALIDATIONS_DIALOG_LIST_ID as any,
        containsMessage: "sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget",
      });
      await replicationFlowEditor.properties.assertValidations({
        containerId: DB_CONTROL.RF_VALIDATIONS_DIALOG_LIST_ID as any,
        containsMessage: "sourceABAPWithoutKeyLoadTypeError",
      });
      await replicationFlowEditor.assertButton({
        button: DB_BUTTON.RF_AFTER_SAVE_VALIDATION_OK,
        enabled: false,
      });
      await replicationFlowEditor.clickButton(DB_BUTTON.RF_AFTER_SAVE_VALIDATION_CANCEL_ID);
      await replicationFlowEditor.clickButton(DB_BUTTON.RF_DELTA_CAPTURE_SWITCH);
      await assertValidationRemovedInPanel("sourceABAPWithoutKeyDeltaCaptureErrorForNewTarget", replicationFlowEditor);
      await replicationFlowEditor.properties.assertSelect({
        id: DB_INPUT.RF_LOAD_TYPE as any,
        selectedItemKey: "REPLICATE",
        valueState: "Error",
      });
      await replicationFlowEditor.selectSelectItem(DB_INPUT.RF_LOAD_TYPE, 0);
      await replicationFlowEditor.properties.assertSelect({
        id: DB_INPUT.RF_LOAD_TYPE as any,
        selectedItemKey: "INITIAL",
      });
      await assertValidationRemovedInPanel("sourceABAPWithoutKeyLoadTypeError", replicationFlowEditor);

      // Projection Dialog
      // Change to mapping tab and assert DPID Column properties
      await openProjectionDialog(replicationFlowEditor);
      await assertValidationInsideProjectionDialog(replicationFlowEditor, 1, 0, 0, 1, "targetAutoRenameUpdated");
      await replicationFlowEditor.assertTableCell(
        DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID,
        1,
        1,
        "AUTOPREFIX___load_package_id"
      );

      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 1, "__load_package_id");
      /**
       * @issueid DW101-77196
       */
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 3, "binary");
      await replicationFlowEditor.assertTableCellDisabled(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 1, true);
      // DPID Col can't be modified/moved/auto mapped
      const checkboxIndex = 2;
      await assertToolbarButtons(checkboxIndex, replicationFlowEditor);
      await addRowInMappingTab(replicationFlowEditor);
      //  New row added before DPID Col
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 1, "NewColumn_1");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 3, 1, "__load_package_id"); // Technical Name
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 3, 2, "__load_package_id"); // Business Name
      await cancelTransformationDialog(replicationFlowEditor);

      // MAP TO EXISTING TARGET WITH KEY && RENAME
      // Map to an existing target with key and check validation and then rename to new target to fix and check dpid col added in new column and PK removed
      await mapToExistingTargetForDWCTarget(api, replicationFlowEditor, tableIndex, "DATASPHERE_EXISTING_WITH_KEY");
      await cancelTransformationDialog(replicationFlowEditor);
      // Existing target without DPID and with PK
      await checkTargetObjectName(tableIndex, "DATASPHERE_EXISTING_WITH_KEY (4)", replicationFlowEditor, false);
      await assertValidationInPanel(
        "existingTarget",
        "error",
        1,
        "sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1",
        replicationFlowEditor
      );
      // PK Removed and DPID Added
      await renameDataset(tableIndex, "RenamedNewTarget", "RenamedNewTarget (5)", replicationFlowEditor, api, "new");
      await assertValidationRemovedInPanel(
        "sourceABAPWithoutKeyPKMismatchErrorForExistingHANATarget1",
        replicationFlowEditor
      );
      await replicationFlowEditor.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, tableIndex);
      await api.waitHTMLStabilized();
      await replicationFlowEditor.properties.assertListItemData({
        id: DB_LIST.RF_TARGET_COLUMNS_LIST as any,
        itemIndex: 2,
        path: "columnsModel>/columns/" + 4 + "/name",
        data: "__load_package_id",
      });
    });

    /**
     * @issueid DW101-77320
     */
    it("Existing targets without valid DPID Column for Datasphere target", async function () {
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "RF_DB_ABAP_DWC_NO_PK_DOD");
      let tableIndex = 0;
      // Validation for existing target without DPID
      await mapToExistingTargetForDWCTarget(api, replicationFlowEditor, tableIndex, "DATASPHERE_EXISTING_WITHOUT_DPID");
      await replicationFlowEditor.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, tableIndex);
      await api.waitHTMLStabilized();
      await assertValidationInPanel(
        "existingTarget",
        "error",
        1,
        "sourceABAPWithoutKeyNoDPIDColForExistingHANATarget",
        replicationFlowEditor
      );
      // Validation for existing target with invalid datatype of DPID Column
      await mapToExistingTargetForDWCTarget(
        api,
        replicationFlowEditor,
        tableIndex,
        "DATASPHERE_EXISTING_WITH_INVALID_DPID"
      );
      await assertValidationRemovedInPanel("sourceABAPWithoutKeyNoDPIDColForExistingHANATarget", replicationFlowEditor);
      await api.waitHTMLStabilized();
      await assertValidationInPanel(
        "existingTarget",
        "error",
        1,
        "sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1",
        replicationFlowEditor
      );
      // Should delete the invalid DPID Col and insert a DPID Column
      await renameDataset(tableIndex, "RenamedNewTarget", "RenamedNewTarget (3)", replicationFlowEditor, api, "new");
      await assertValidationRemovedInPanel(
        "sourceABAPWithoutKeyDPIDColDatatypeErrorForExistingHANATarget1",
        replicationFlowEditor
      );
      await replicationFlowEditor.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, tableIndex);
      await api.waitHTMLStabilized();
      await replicationFlowEditor.properties.assertListItemData({
        id: DB_LIST.RF_TARGET_COLUMNS_LIST as any,
        itemIndex: 2,
        path: "columnsModel>/columns/" + 2 + "/name",
        data: "__load_package_id",
      });
    });
  });
});

async function openRF(api: IAPI, createRF?: string) {
  let replicationFlowEditor;
  if (createRF) {
    replicationFlowEditor = await createReplicationFlow(api, "RFSPACE");
    await replicationFlowEditor.assertControlExists({
      id: DB_CONTROL.RF_WELCOME_PANEL,
    });
  } else {
    replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "RF_Source_Abap_DPID");
  }
  return replicationFlowEditor;
}

async function addCDSDatasets(replicationFlowEditor, api: IAPI, csnDefinition) {
  await replicationFlowEditor.assertButton({
    button: DB_BUTTON.RF_ADD_SOURCE_DATASET_BUTTON,
  });
  await cy
    .intercept("GET", "**/datasources/getchildren**", (req) => {
      req.reply({
        fixture: "databuilder/replicationflowbuilder/cdsImportObjects", // for dataset
      });
    })
    .as("getContainerFromGetChildren");
  await openAndSelectFromDatasetsDialog(replicationFlowEditor, api);
  let csnMocks = 4;
  await cy
    .intercept("GET", "**/datasources/getcsndefinition**", (req) => {
      if (csnMocks % 2 === 0) {
        req.reply({
          fixture: `databuilder/replicationflowbuilder/ABAPSourceWithoutKeys/${csnDefinition}`,
        });
      } else {
        req.reply({
          fixture: "databuilder/replicationflowbuilder/cdsExtractionEnabledScn",
        });
      }
      csnMocks++;
    })
    .as("getCSN");
  await replicationFlowEditor.clickButton(DB_BUTTON.RF_IMPORT_OBJECTS_FROM_CONNECTION_ADD_SELECTION_BUTTON);
  await api.XHRWait("@getCSN");
  await api.XHRWait("@getCSN");
}

async function openAndSelectFromDatasetsDialog(replicationFlowEditor: ReplicationFlowEditor, api: IAPI) {
  //  Source Dataset Dialog
  await replicationFlowEditor.clickButton(DB_BUTTON.RF_ADD_SOURCE_DATASET_BUTTON);
  await api.XHRWait("@getContainerFromGetChildren");
  await api.waitHTMLStabilized();
  await replicationFlowEditor.assertControlExists({
    id: DB_TABLE.ER_REMOTE_OBJECTS_SELECTOR_SOURCE_TABLE,
  });

  //  Clicking Datasets
  await assertAndClickDataset(0, "I_BPDATACONTROLLERUSAGE - BP Data Controller Usage", api);
  await assertAndClickDataset(1, "I_BUSINESSPARTNERLEGALFORMTEXT - Business Partner Legal Form - Text", api);

  //  Selecting Datasets
  await replicationFlowEditor.assertButton({
    button: DB_BUTTON.ER_REMOTE_OBJECTS_SELECTOR_OK,
  });
  await replicationFlowEditor.clickButton(DB_BUTTON.ER_REMOTE_OBJECTS_SELECTOR_OK);
  await replicationFlowEditor.assertButton({
    button: DB_BUTTON.RF_IMPORT_OBJECTS_FROM_CONNECTION_ADD_SELECTION_BUTTON,
  });
}

async function openProjectionDialog(replicationFlowEditor) {
  await EditorUtils.openTransformationDialog(replicationFlowEditor);
  await switchToMappingTab(replicationFlowEditor);
}

async function assertValidationInPanel(
  targetType: string,
  assertType: string,
  Count: number,
  message: string,
  replicationFlowEditor
) {
  await replicationFlowEditor.assertButton({
    button: DB_BUTTON.RF_OBJECT_VALIDATION_BTN,
  });
  await replicationFlowEditor.properties.clickButton(DB_BUTTON.RF_OBJECT_VALIDATION_BTN);
  if (assertType === "error") {
    if (targetType === "newTarget") {
      await replicationFlowEditor.properties.assertValidations({
        containerId: DB_CONTROL.RF_VALIDATION_POPOVER as any,
        errCount: Count,
        containsMessage: message,
      });
    } else {
      await replicationFlowEditor.properties.assertValidations({
        containerId: DB_CONTROL.RF_VALIDATION_POPOVER as any,
        errCount: Count,
        containsMessage: message,
      });
    }
  } else if (assertType === "info") {
    if (targetType === "newTarget") {
      await replicationFlowEditor.properties.assertValidations({
        containerId: DB_CONTROL.RF_VALIDATION_POPOVER as any,
        infoCount: Count,
        containsMessage: message,
      });
    } else {
      await replicationFlowEditor.properties.assertValidations({
        containerId: DB_CONTROL.RF_VALIDATION_POPOVER as any,
        infoCount: Count,
        containsMessage: message,
      });
    }
  }
  await replicationFlowEditor.properties.closeValidationPopover();
}

async function assertValidationRemovedInPanel(messageNotContained: string, replicationFlowEditor) {
  await replicationFlowEditor.assertButton({
    button: DB_BUTTON.RF_OBJECT_VALIDATION_BTN,
  });
  await replicationFlowEditor.clickButton(DB_BUTTON.RF_OBJECT_VALIDATION_BTN);

  await replicationFlowEditor.properties.assertValidations({
    containerId: DB_CONTROL.RF_VALIDATION_POPOVER as any,
    notContainsMessage: messageNotContained,
  });
  await replicationFlowEditor.clickButton(DB_BUTTON.RF_OBJECT_VALIDATION_BTN);
}

async function assertToolbarButtons(checkboxIndex, replicationFlowEditor) {
  await selectRowInMappingTab(checkboxIndex, replicationFlowEditor);
  await checkStateOfAddRemUpDownButtons(true, false, false, false, replicationFlowEditor);
  await replicationFlowEditor.assertButton({
    button: DB_BUTTON.RF_TRANSFORMATION_AUTO_MAP_BUTTON_ID,
    enabled: false,
  });
}
