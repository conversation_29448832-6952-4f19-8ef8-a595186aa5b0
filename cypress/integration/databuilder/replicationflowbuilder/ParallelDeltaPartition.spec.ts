/** @format */

import { <PERSON><PERSON><PERSON>, getAPIInstance } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { ReplicationFlowEditor } from "../../../pageobjects/databuilder/ReplicationFlowEditor";
import {
  DB_BUTTON,
  DB_CONTROL,
  DB_INPUT,
  DB_LIST,
  DB_TABLE,
  DB_TEXT,
} from "../../../pageobjects/databuilder/descriptor";
import { openReplicationFlow, setDefaultMock } from "./Util";

describe("cypress/integration/databuilder/replicationflowbuilder/ParallelDeltaPartition", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(
      /* useMock*/ true,
      /* defaultFixture*/ true,
      /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES,
      /* overrideRouteCallback */ undefined
    );
    await setDefaultMock(api);
  });
  describe("Assert Connection Setting Dialog Box", () => {
    it("Asserting the label with FF on", async function () {
      // Opening RF with target DWC
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "RF_TARGET_DWC");

      // Asserting that Browser target setting button is disabled
      await replicationFlowEditor.properties.assertControl({
        id: DB_BUTTON.RF_TARGET_SETTINGS_DIALOG_BUTTON,
        enabled: true,
      });

      await replicationFlowEditor.clickButton(DB_BUTTON.RF_TARGET_SETTINGS_DIALOG_BUTTON);

      await replicationFlowEditor.assertText({
        id: DB_TEXT.RF_TARGET_REPLICATION_THREAD_COUNT as any,
        value: "targetConnectionThreadlimit",
      });
    });
    /**
     * @issueid DW101-74302, DW101-74257
     */
    it("Check the text when there is no task with load type Delta", async function () {
      // Opening RF
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "RF_Delta_Partition");

      // cypress fix added after enhancement for dsp table create on rf save story,
      // since old rf has unsaved dsp targetobjects, assert an additional dialog prompt to resave the unsaved dsp targets when such replication flow is opened
      await api.assertMessageBox({ text: "openReplicationFlowUnsavedTargetObject" });
      await api.clickButton(DB_BUTTON.MSGBOX_OK);

      // Asserting that Browser target setting button is disabled
      await replicationFlowEditor.properties.assertControl({
        id: DB_BUTTON.RF_SOURCE_SETTINGS_DIALOG_BUTTON,
        enabled: true,
      });

      await replicationFlowEditor.clickButton(DB_BUTTON.RF_SOURCE_SETTINGS_DIALOG_BUTTON);

      await replicationFlowEditor.properties.assertText({
        id: DB_TEXT.RF_MESSAGE_DELTA_PARTITION_DISABLE,
        value: "deltaPartitionnotEnabled",
      });
    });
    it("Check the delta partition in global setting dialog box", async function () {
      // Opening RF
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "RF_Delta_Partition");

      // cypress fix added after enhancement for dsp table create on rf save story,
      // since old rf has unsaved dsp targetobjects, assert an additional dialog prompt to resave the unsaved dsp targets when such replication flow is opened
      await api.assertMessageBox({ text: "openReplicationFlowUnsavedTargetObject" });
      await api.clickButton(DB_BUTTON.MSGBOX_OK);

      await selectLoadTypeDelta(api, replicationFlowEditor, true);

      await replicationFlowEditor.clickButton(DB_BUTTON.RF_SOURCE_SETTINGS_DIALOG_BUTTON);

      await ChangeValueAndAssertElementStates(
        replicationFlowEditor,
        "1",
        null,
        "None",
        DB_INPUT.RF_GLOBAL_DELTA_PARTITION
      );

      //  Change Object Thread Limit for Delta Loads value and assert error state
      await ChangeValueAndAssertElementStates(
        replicationFlowEditor,
        "0",
        "0",
        "Error",
        DB_INPUT.RF_GLOBAL_DELTA_PARTITION
      );

      //  Assert Save button
      await replicationFlowEditor.properties.assertControl({
        id: DB_BUTTON.RF_SOURCE_SETTING_OK,
        enabled: false,
      });

      //  Change Object Thread Limit for Delta Loads value and assert normal state
      await ChangeValueAndAssertElementStates(
        replicationFlowEditor,
        "1",
        "1",
        "None",
        DB_INPUT.RF_GLOBAL_DELTA_PARTITION
      );

      await replicationFlowEditor.properties.assertButton({
        button: DB_BUTTON.RF_SOURCE_SETTING_OK,
        enabled: true,
      });

      //  Change Object Thread Limit for Delta Loads value and assert error state
      await ChangeValueAndAssertElementStates(
        replicationFlowEditor,
        "11",
        "11",
        "Error",
        DB_INPUT.RF_GLOBAL_DELTA_PARTITION
      );

      //  Assert Save button
      await replicationFlowEditor.properties.assertControl({
        id: DB_BUTTON.RF_SOURCE_SETTING_OK,
        enabled: false,
      });

      //  Clear Object Thread Limit for Delta Loads value and assert error state
      await ChangeValueAndAssertElementStates(
        replicationFlowEditor,
        "",
        " ",
        "Error",
        DB_INPUT.RF_GLOBAL_DELTA_PARTITION
      );

      //  Assert Save button
      await replicationFlowEditor.properties.assertButton({
        button: DB_BUTTON.RF_SOURCE_SETTING_OK,
        enabled: false,
      });

      //  Pass any string on Object Thread Limit for Delta Loads value and assert error state
      await ChangeValueAndAssertElementStates(
        replicationFlowEditor,
        "",
        "test",
        "Error",
        DB_INPUT.RF_GLOBAL_DELTA_PARTITION
      );

      //  Assert Save button
      await replicationFlowEditor.properties.assertButton({
        button: DB_BUTTON.RF_SOURCE_SETTING_OK,
        enabled: false,
      });

      //  Change Object Thread Limit for Delta Loads value and assert normal state
      await ChangeValueAndAssertElementStates(
        replicationFlowEditor,
        "10",
        "10",
        "None",
        DB_INPUT.RF_GLOBAL_DELTA_PARTITION
      );

      //  Assert Save button
      await replicationFlowEditor.properties.assertButton({
        button: DB_BUTTON.RF_SOURCE_SETTING_OK,
        enabled: true,
      });

      // click on save button
      await replicationFlowEditor.properties.clickButton(DB_BUTTON.RF_SOURCE_SETTING_OK);
    });
    it("Check the delta partition in property panel", async function () {
      // Opening RF
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "RF_Delta_Partition");

      // cypress fix added after enhancement for dsp table create on rf save story,
      // since old rf has unsaved dsp targetobjects, assert an additional dialog prompt to resave the unsaved dsp targets when such replication flow is opened
      await api.assertMessageBox({ text: "openReplicationFlowUnsavedTargetObject" });
      await api.clickButton(DB_BUTTON.MSGBOX_OK);

      await selectLoadTypeDelta(api, replicationFlowEditor, true);

      //  Pass any string on Object Thread Limit for Delta Loads value and assert error state
      await ChangeValueAndAssertElementStates(
        replicationFlowEditor,
        "",
        "test",
        "Error",
        DB_INPUT.RF_OBJECT_DELTA_PARTITION
      );

      await checkValidation(replicationFlowEditor, 1);

      await ChangeValueAndAssertElementStates(
        replicationFlowEditor,
        "-85",
        "-85",
        "Error",
        DB_INPUT.RF_OBJECT_DELTA_PARTITION
      );

      await checkValidation(replicationFlowEditor, 1);

      await ChangeValueAndAssertElementStates(
        replicationFlowEditor,
        "",
        " ",
        "Error",
        DB_INPUT.RF_OBJECT_DELTA_PARTITION
      );

      await checkValidation(replicationFlowEditor, 1);

      await ChangeValueAndAssertElementStates(
        replicationFlowEditor,
        "111",
        "111",
        "Error",
        DB_INPUT.RF_OBJECT_DELTA_PARTITION
      );

      await checkValidation(replicationFlowEditor, 1);

      await ChangeValueAndAssertElementStates(
        replicationFlowEditor,
        "8",
        "8",
        "None",
        DB_INPUT.RF_OBJECT_DELTA_PARTITION
      );

      await checkValidation(replicationFlowEditor, 0);
      await api.mockServerCall("POST", "*objects**", "{}", "save");
      await replicationFlowEditor.save();
      //  JSON Body shouldn't contain the property
      await api.assertServerRequestParameters("@save", undefined, {
        containsText: "maxDesiredParallelDeltaTransfers",
      });
    });
    it("Check the overwrite delta partition", async function () {
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "RF_Delta_Partition");

      // cypress fix added after enhancement for dsp table create on rf save story,
      // since old rf has unsaved dsp targetobjects, assert an additional dialog prompt to resave the unsaved dsp targets when such replication flow is opened
      await api.assertMessageBox({ text: "openReplicationFlowUnsavedTargetObject" });
      await api.clickButton(DB_BUTTON.MSGBOX_OK);

      await selectLoadTypeDelta(api, replicationFlowEditor, true);

      await replicationFlowEditor.clickButton(DB_BUTTON.RF_SOURCE_SETTINGS_DIALOG_BUTTON);

      await ChangeValueAndAssertElementStates(
        replicationFlowEditor,
        "9",
        "9",
        "None",
        DB_INPUT.RF_GLOBAL_DELTA_PARTITION
      );

      //  Assert Save button
      await replicationFlowEditor.properties.assertButton({
        button: DB_BUTTON.RF_SOURCE_SETTING_OK,
        enabled: true,
      });

      await replicationFlowEditor.clickButton(DB_BUTTON.RF_SOURCE_SETTING_OK);

      await replicationFlowEditor.clickButton(DB_BUTTON.RF_SOURCE_SETTINGS_DIALOG_BUTTON);

      await replicationFlowEditor.properties.assertInput({
        input: DB_INPUT.RF_OBJECT_DELTA_PARTITION,
        text: "9",
        valueState: "None",
      });

      await replicationFlowEditor.clickButton(DB_BUTTON.RF_SOURCE_SETTING_OK);

      await replicationFlowEditor.clickButton(DB_BUTTON.RF_SOURCE_SETTINGS_DIALOG_BUTTON);

      await ChangeValueAndAssertElementStates(
        replicationFlowEditor,
        "8",
        "8",
        "None",
        DB_INPUT.RF_GLOBAL_DELTA_PARTITION
      );

      await api.clickButton(DB_BUTTON.RF_SOURCE_OVERWRITE_DATASET_SETTINGS);

      //  Assert Save button
      await replicationFlowEditor.clickButton(DB_BUTTON.RF_SOURCE_SETTING_OK);

      await replicationFlowEditor.properties.assertInput({
        input: DB_INPUT.RF_OBJECT_DELTA_PARTITION,
        text: "8",
        valueState: "None",
      });
      await api.mockServerCall("POST", "*objects**", "{}", "save");
      await replicationFlowEditor.save();
      //  JSON Body shouldn't contain the property
      await api.assertServerRequestParameters("@save", undefined, {
        containsText: "globalDeltaPartitionValue",
      });
    });
  });
});

async function ChangeValueAndAssertElementStates(
  replicationFlowEditor: ReplicationFlowEditor,
  assertingValue,
  changedValue: string,
  elementState: string,
  id
) {
  if (changedValue) {
    await replicationFlowEditor.properties.typeInputText(id, changedValue);
  }
  await replicationFlowEditor.properties.assertInput({
    input: id,
    text: assertingValue,
    valueState: elementState,
  });
}

async function selectLoadTypeDelta(api, replicationFlowEditor, ff: boolean) {
  await api.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, 1);
  await api.waitHTMLStabilized();
  await replicationFlowEditor.assertButton({
    button: DB_CONTROL.RF_SETTING_BUTTON,
  });
  await replicationFlowEditor.clickButton(DB_CONTROL.RF_SETTING_BUTTON);
  const loadTypeAllDropdown = DB_LIST.RF_LOAD_TYPE_ALL + "-1-arrow";
  const loadTypeAll = DB_LIST.RF_LOAD_TYPE_ALL + "-1";
  await replicationFlowEditor.clickButton(loadTypeAllDropdown);
  await replicationFlowEditor.properties.assertList({
    list: loadTypeAll as any,
    rows: 2,
  });
  await replicationFlowEditor.selectSelectItem(loadTypeAll, 1);
  await replicationFlowEditor.properties.assertSelect({
    id: loadTypeAll as any,
    selectedItemKey: "REPLICATE",
  });
  if (ff) {
    await replicationFlowEditor.assertMessageToast({
      text: "deltaPartitionEnable",
    });
  }
  await replicationFlowEditor.properties.assertButton({
    button: DB_BUTTON.RF_SOURCE_SETTINGS_DIALOG_BUTTON,
    enabled: true,
  });
}

async function checkValidation(replicationFlowEditor, errorCount) {
  await replicationFlowEditor.clickButton(DB_BUTTON.RF_OBJECT_VALIDATION_BTN);
  await replicationFlowEditor.properties.assertValidations({
    containerId: DB_CONTROL.RF_VALIDATION_POPOVER as any,
    errorCount: errorCount,
  });
  await replicationFlowEditor.clickButton(DB_BUTTON.RF_OBJECT_VALIDATION_BTN);
}
