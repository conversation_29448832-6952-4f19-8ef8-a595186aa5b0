/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { constants } from "perf_hooks";
import { IAPI, getAPIInstance } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DB_BUTTON, DB_CONTROL, DB_INPUT, DB_TABLE, DB_VIEW } from "../../../pageobjects/databuilder/descriptor";
import {
  assertValidationInReplicationObjectPanel,
  openProjectionDialogFromToolbar,
  openReplicationFlow,
  setDefaultMock,
} from "./Util";
import { assertValidationInsideProjectionDialog, switchToMappingTab } from "./TransformationDialogUtils";
import ConnectionUtils from "./Utils/ConnectionUtils";

describe("cypress/integration/databuilder/replicationflowbuilder/RFValidationDwcTarget", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(
      /* useMock*/ true,
      /* defaultFixture*/ true,
      /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES,
      /* overrideRouteCallback */ undefined,
      []
    );
    await setDefaultMock(api);
  });

  describe("{replicationflow} with target dwc system validations spec", () => {
    it("Open RF & add replication and validate invalid delta capture artifact", async function () {
      // Open RF with datasets which has invalid chars in column names
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "RF_DB_AUTOPROJECT0");

      // Select Datasphere target and create new tables
      await ConnectionUtils.searchAndSelectTargetConnection(
        "SAP Datasphere",
        "SAP Datasphere",
        false,
        api,
        replicationFlowEditor,
        "RFSPACE"
      );
      await api.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, 0);
      await api.waitHTMLStabilized();
      await assertValidationInReplicationObjectPanel(replicationFlowEditor, {
        infoCount: 1,
        containsMessage: "validationAutoRenameTarget",
      });
      await openProjectionDialogFromToolbar(api, replicationFlowEditor);
      await switchToMappingTab(replicationFlowEditor);
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 1, 0, "DU#PLICATE");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 1, 1, "DU_PLICATE_0");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 0, "DU/PLICATE");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 1, "DU_PLICATE_2");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 3, 0, "DU PLICATE");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 3, 1, "DU_PLICATE_3");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 4, 0, "TWO/SLASH/");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 4, 1, "TWO_SLASH_");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 7, 0, "HYPHEN-SLASH/");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 7, 1, "HYPHEN_SLASH_");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 8, 0, "_StartUnderscore");
      // Original Column names which were present should not be changed and any renamed column should be updated with a unique name
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 9, 0, "DU_PLICATE");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 9, 1, "DU_PLICATE");
      await replicationFlowEditor.assertTableCell(
        DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID,
        8,
        1,
        "AUTOPREFIX__StartUnderscore"
      );
      await assertValidationInsideProjectionDialog(replicationFlowEditor, 8, 0, 0, 8, "targetAutoRenameUpdated");
      // Change Mapping and check auto map
      await changeSourceMapping(2, 11, api);
      await changeSourceMapping(9, 11, api);
      await replicationFlowEditor.clickButton(DB_BUTTON.RF_TRANSFORMATION_AUTO_MAP_BUTTON_ID);
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 0, "DU/PLICATE");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 9, 0, "DU_PLICATE");
    });
  });
});
async function changeSourceMapping(rowInd: number, listItem: number, api: IAPI) {
  await api.clickChildInTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, rowInd, 0, 0);
  await cy
    .byId(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID)
    .get(".sapMSelectList")
    .filter(':contains("Id")')
    .children()
    .eq(listItem)
    .click({ force: true });
}
