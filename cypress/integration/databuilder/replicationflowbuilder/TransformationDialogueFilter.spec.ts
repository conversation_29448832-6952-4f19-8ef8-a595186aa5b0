/** @format */

import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DB_BUTTON, DB_CONTROL, DB_INPUT, DB_LIST, DB_TABLE } from "../../../pageobjects/databuilder/descriptor";
import { ReplicationFlowEditor } from "../../../pageobjects/databuilder/ReplicationFlowEditor";
import { searchItemInFilterTokenList } from "./TransformationDialogUtils";
import { openReplicationFlow, setDefaultMock } from "./Util";
import EditorUtils from "./Utils/RFEditorUtils";

describe("cypress/integration/databuilder/replicationflowbuilder/TransformationDialogueFilter", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(
      /* useMock*/ true,
      /* defaultFixture*/ true,
      /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES,
      /* overrideRouteCallback */ undefined
    );
    await setDefaultMock(api);
  });
  describe("Transformation Dialogue Filter Tests", () => {
    it("Assert presence of all possible filters in projection for HANA as source RF with column as boolean", async function () {
      //Open RF with HANA cloud as source and HANA cloud as Target
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "hana_filter");
      await assertExpression("BOOL", replicationFlowEditor, api, 6);
    });
    it("Assert presence of all possible filters in projection for DWC as source RF with column as boolean", async function () {
      //Open RF with HANA cloud as source and HANA cloud as Target
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "filter_Exp");
      await assertExpression("Boolean", replicationFlowEditor, api, 6);
      await assertExpression("Binary", replicationFlowEditor, api, 1);
    });
  });
});

async function assertExpression(text: string, replicationFlowEditor: ReplicationFlowEditor, api: IAPI, count: number) {
  await replicationFlowEditor.assertTable({
    table: DB_TABLE.RF_EDITOR_MAIN_CANVAS,
    rows: 1,
  });
  await api.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, 0);
  await EditorUtils.openTransformationDialog(replicationFlowEditor);
  await searchItemInFilterTokenList(replicationFlowEditor, text);

  await replicationFlowEditor.properties.selectToken(DB_LIST.RF_SOURCE_TABLE_ELEMENT_LIST, text, 1);
  await replicationFlowEditor.clickButton(DB_LIST.RF_FILTER_EXPRESSION_LIST + "-arrow");
  await replicationFlowEditor.properties.assertList({
    list: DB_LIST.RF_FILTER_EXPRESSION_LIST as any,
    rows: count,
  });
}
