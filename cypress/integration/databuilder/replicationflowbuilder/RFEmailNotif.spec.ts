/** @format */

import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DB_BUTTON, DB_TEXT } from "../../../pageobjects/databuilder/descriptor";
import { openReplicationFlow, setDefaultMock } from "./Util";

describe("cypress/integration/databuilder/replicationflowbuilder/RFEmailNotif", () => {
  let api: IAPI;

  const setupCommonMocks = async () => {
    await api.mockServerCall(
      "GET",
      "**//replicationflow/space/RFSPACE/flows/AM_RF_DEPLOYED_RUNING/status**",
      "fixture:databuilder/replicationflowbuilder/runStatusRunningRF",
      "getRunningStatus"
    );

    await api.mockServerCall(
      "GET",
      "**/allusers",
      "fixture:databuilder/replicationflowbuilder/EmailAllUsers",
      "getAllUsers"
    );

    await api.mockServerCall(
      "GET",
      "**/dwaas-core/replicationflow/space/RFSPACE/flows/AM_RF_DEPLOYED_RUNING/getnotificationinfo**",
      {
        condition: "ANY_DATASET_FAILED",
        emailConfiguration: {
          tenantMembers: [],
          others: ["<EMAIL>"],
        },
        subject: "Mock Subject",
        body: "Mock Body Message",
      },
      "getNotificationMailingList"
    );

    await api.mockServerCall(
      "POST",
      "**/customerInfo",
      { name: "ownerEmail", value: "<EMAIL>" },
      "EmailCustomerInfo"
    );
  };

  beforeEach(async () => {
    api = getAPIInstance();

    await api.setupBeforeEach(
      /* useMock*/ true,
      /* defaultFixture*/ true,
      /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES,
      /* overrideRouteCallback */ undefined,
      /* enabledFeatureFlags */ ["INFRA_DWC_TWO_TENANT_MODE"]
    );
    await setDefaultMock(api);
  });

  describe("Replication Flow Email Notiifcation", () => {
    it("Assert email notif", async function () {
      api.enableUIFeatureflag("DWCO_DS_REPLICATION_FLOW_EMAIL_NOTIFICATION");
      await setupCommonMocks();
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "AM_RF_DEPLOYED_RUNING");
      await api.XHRWait("@getRunningStatus");
      await openEmailDialog(api, replicationFlowEditor);

      await replicationFlowEditor.assertSelect({
        id: DB_TEXT.RF_EMAIL_NOTIF_OPTIONS,
        selectedIndex: 1,
      });

      await replicationFlowEditor.assertContainsText({
        id: DB_TEXT.RF_EMAIL_SUBJECT_INPUT,
        value: "Mock Subject",
      });
      await api.typeTextArea(DB_TEXT.RF_EMAIL_BODY_INPUT, "notification subject text test");
      await api.typeInputText(DB_TEXT.RF_EMAIL_SUBJECT_INPUT, "notification subject text test");
      await api.clearInput(DB_TEXT.RF_EMAIL_SUBJECT_INPUT);
      await api.clearInput(DB_TEXT.RF_EMAIL_BODY_INPUT);
      await api.assertInput({
        input: "emailNotificationDialog--dialog--view--emailSubject",
        valueState: "Error",
      });
      await api.assertInput({
        input: "emailNotificationDialog--dialog--view--emailMessageBody",
        valueState: "Error",
      });
    });

    it("should check the email value help dialog", async () => {
      api.enableUIFeatureflag("DWCO_DS_REPLICATION_FLOW_EMAIL_NOTIFICATION");
      await setupCommonMocks();

      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "AM_RF_DEPLOYED_RUNING");
      await api.XHRWait("@getRunningStatus");
      await openEmailDialog(api, replicationFlowEditor);

      await replicationFlowEditor.openMultiInputValueHelp(DB_TEXT.RF_EMAIL_RECIPIENT_INPUT);

      await api.assertList({
        list: DB_TEXT.RF_EMAIL_TENANT_MEMBER_LIST,
        rows: 10,
      });

      await api.selectIconTabBarItem(DB_TEXT.RF_EMAIL_TABS, "others");

      await api.assertList({
        list: DB_TEXT.RF_EMAIL_NON_TENANT_LIST,
        rows: 1,
      });

      await api.typeInputText(DB_TEXT.RF_EMAIL_SELECTED_INPUT, "testmail");
      await api.assertInput({
        input: DB_TEXT.RF_EMAIL_SELECTED_INPUT,
        valueState: "Error",
      });

      await api.clickButton(DB_TEXT.RF_EMAIL_CANCEL_BTN);

      await replicationFlowEditor.openMultiInputValueHelp(DB_TEXT.RF_EMAIL_RECIPIENT_INPUT);

      await api.selectListRow(DB_TEXT.RF_EMAIL_TENANT_MEMBER_LIST, 1, true);

      await api.typeInputText(DB_TEXT.RF_EMAIL_SEARCH_TENANT_INPUT, "test");

      await api.assertInput({
        input: DB_TEXT.RF_EMAIL_SELECTED_INPUT,
        valueState: "None",
      });

      await api.selectIconTabBarItem(DB_TEXT.RF_EMAIL_TABS, "others");

      await api.typeInputText(DB_TEXT.RF_EMAIL_SELECTED_INPUT, "<EMAIL>{enter}");
      await api.typeInputText(DB_TEXT.RF_EMAIL_SELECTED_INPUT, "<EMAIL>{enter}");
      await api.typeInputText(DB_TEXT.RF_EMAIL_SELECTED_INPUT, "<EMAIL>{enter}");

      await api.clickButton(DB_TEXT.RF_EMAIL_ADD_NON_TENANT_BTN);
      await api.assertInput({
        input: DB_TEXT.RF_EMAIL_SELECTED_INPUT,
        valueState: "None",
      });
      await api.clickButton(DB_TEXT.RF_EMAIL_SELECT_BTN);
    });
    it("should check for email input validation", async () => {
      api.enableUIFeatureflag("DWCO_DS_REPLICATION_FLOW_EMAIL_NOTIFICATION");
      await setupCommonMocks();

      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "AM_RF_DEPLOYED_RUNING");
      await api.XHRWait("@getRunningStatus");
      await openEmailDialog(api, replicationFlowEditor);

      const multiInput = (await cy.byId(DB_TEXT.RF_EMAIL_RECIPIENT_INPUT)) as sap.m.MultiInput;

      await replicationFlowEditor.typeInputText(DB_TEXT.RF_EMAIL_RECIPIENT_INPUT, "<EMAIL>");
      await replicationFlowEditor.assertInput({
        input: DB_TEXT.RF_EMAIL_RECIPIENT_INPUT,
        valueState: "None",
      });
      multiInput.removeAllTokens();
      await replicationFlowEditor.typeInputText(DB_TEXT.RF_EMAIL_RECIPIENT_INPUT, "test");
      await replicationFlowEditor.assertInput({
        input: DB_TEXT.RF_EMAIL_RECIPIENT_INPUT,
        valueState: "Error",
      });
      multiInput.removeAllTokens();
      await replicationFlowEditor.assertInput({
        input: DB_TEXT.RF_EMAIL_RECIPIENT_INPUT,
        valueState: "Error",
      });
    });
    it("email notification button shouldnt be there if ff is off in rfbuilder", async () => {
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "AM_RF_DEPLOYED_RUNING");
      await replicationFlowEditor.assertButton({
        button: DB_BUTTON.RF_EMAIL_NOTIF_BUTTON_ID,
        visible: false,
      });
    });
    it("email notification button shouldnt be enabled there if replication flow is not deployed", async () => {
      api.enableUIFeatureflag("DWCO_DS_REPLICATION_FLOW_EMAIL_NOTIFICATION");
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "abap_cypress");
      await replicationFlowEditor.assertButton({
        button: DB_BUTTON.RF_EMAIL_NOTIF_BUTTON_ID,
        enabled: false,
      });
    });
  });
});

async function openEmailDialog(api, replicationFlowEditor) {
  await replicationFlowEditor.assertButton({
    button: DB_BUTTON.RF_EMAIL_NOTIF_BUTTON_ID,
    enabled: true,
  });
  await replicationFlowEditor.clickButton(DB_BUTTON.RF_EMAIL_NOTIF_BUTTON_ID);
  await api.XHRWait("@getNotificationMailingList");
  await api.XHRWait("@getAllUsers");
  await api.XHRWait("@EmailCustomerInfo");
  return replicationFlowEditor;
}
