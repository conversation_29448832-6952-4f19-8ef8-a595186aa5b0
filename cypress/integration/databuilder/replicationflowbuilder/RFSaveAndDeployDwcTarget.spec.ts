/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { IAPI, getAPIInstance } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DB_BUTTON, DB_CONTROL, DB_TABLE, DB_TEXT } from "../../../pageobjects/databuilder/descriptor";
import { cancelTransformationDialog, switchToMappingTab } from "./TransformationDialogUtils";
import {
  assertAndAddDatasetObjects,
  openProjectionDialogFromToolbar,
  openReplicationFlow,
  selectReplicationObjectByIndex,
  setDefaultMock,
} from "./Util";

describe("cypress/integration/databuilder/replicationflowbuilder/RFSaveAndDeployDwcTarget", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(
      /* useMock*/ true,
      /* defaultFixture*/ true,
      /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES,
      /* overrideRouteCallback */ undefined,
      []
    );
    await setDefaultMock(api);
  });

  describe("{replicationflow} with target dwc system spec", () => {
    /**
     * @issueid DW101-89351
     */
    it("Open RF & assert actions - add replication, save and deploy", async function () {
      // open RF (HANA-DWC) with no replications
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "RF_SAVE_DEPLOY_DWC_TARGET");

      // add source dataset with new dwc targets
      await assertAndAddDatasetObjects(
        api,
        replicationFlowEditor,
        "RFSaveAndDeployDwcTarget/getChildren",
        2,
        0,
        "TABLE_A_DATATYPES",
        "RFSaveAndDeployDwcTarget/getDefinitionTableAllDatatypes",
        2,
        1,
        "TABLE_B",
        "RFSaveAndDeployDwcTarget/getDefinitionTableB"
      );

      // select replication
      await selectReplicationObjectByIndex(api, 0);

      // assert dwc target is new
      await assertDwcTableObjectState(0, replicationFlowEditor, true);

      // assert dwc target label, assert 19 columns since 1(col_large_string) is skipped
      const newTargetLabelID = DB_TABLE.RF_MAIN_CANVAS_NEW_TARGET_LABEL_ID + 0;
      await replicationFlowEditor.assertText({
        id: newTargetLabelID as any,
        value: "TABLE_A_DATATYPES (19)",
      });

      // assert dwc table object status doesn't exist
      await replicationFlowEditor.properties.assertControlNotExists({
        id: DB_TEXT.RF_TARGET_OBJECT_DEPLOYMENT_STATUS,
      });

      // save
      await api.mockServerCall(
        "POST",
        "**/objects**",
        '{"message":"object with id = A715FED30526B7EF18006F6A264C27D9 is updated"}',
        "savemock",
        201
      );
      await replicationFlowEditor.save();

      // assert csn and all csn datatypes
      await api.assertServerRequestParameters("@savemock", undefined, {
        containsText: [
          '"definitions":{"TABLE_A_DATATYPES":{"kind":"entity","@EndUserText.label":"TABLE_A_DATATYPES","@ObjectModel.modelingPattern":{"#":"DATA_STRUCTURE"},"@ObjectModel.supportedCapabilities":[{"#":"DATA_STRUCTURE"}],"elements":{',
          '"id":{"@EndUserText.label":"id","type":"cds.Integer","key":true,"notNull":true}',
          '"col_uint8":{"@EndUserText.label":"col_uint8","type":"cds.hana.TINYINT","key":false}',
          '"col_int8":{"@EndUserText.label":"col_int8","type":"cds.hana.SMALLINT","key":false}',
          '"col_int16":{"@EndUserText.label":"col_int16","type":"cds.hana.SMALLINT","key":false}',
          '"col_int32":{"@EndUserText.label":"col_int32","type":"cds.Integer","key":false}',
          '"col_int64":{"@EndUserText.label":"col_int64","type":"cds.Integer64","key":false}',
          '"col_uint64":{"@EndUserText.label":"col_uint64","type":"cds.Decimal","key":false,"precision":20,"scale":0}',
          '"col_boolean":{"@EndUserText.label":"col_boolean","type":"cds.Boolean","key":false}',
          '"col_decfloat16":{"@EndUserText.label":"col_decfloat16","type":"cds.hana.SMALLDECIMAL","key":false}',
          '"col_decfloat34":{"@EndUserText.label":"col_decfloat34","type":"cds.DecimalFloat","key":false}',
          '"col_float32":{"@EndUserText.label":"col_float32","type":"cds.hana.REAL","key":false}',
          '"col_float64":{"@EndUserText.label":"col_float64","type":"cds.Double","key":false}',
          '"col_date":{"@EndUserText.label":"col_date","type":"cds.Date","key":false}',
          '"col_time":{"@EndUserText.label":"col_time","type":"cds.Time","key":false}',
          '"col_timestamp":{"@EndUserText.label":"col_timestamp","type":"cds.Timestamp","key":false}',
          '"col_decimal_10_5":{"@EndUserText.label":"col_decimal_10_5","type":"cds.Decimal","key":false,"precision":10,"scale":5}',
          '"col_decimal_38_9":{"@EndUserText.label":"col_decimal_38_9","type":"cds.Decimal","key":false,"precision":38,"scale":9}',
          '"col_string_2000":{"@EndUserText.label":"col_string_2000","type":"cds.String","key":false,"length":2000}',
          '"col_binary_2000":{"@EndUserText.label":"col_binary_2000","type":"cds.Binary","key":false,"length":2000}',
        ],
      });

      // assert saved toast
      await replicationFlowEditor.assertMessageToast({
        text: "modelSavedSuccessful",
      });

      // assert dwc table as existing
      await assertDwcTableObjectState(0, replicationFlowEditor, false);

      // assert dwc target status visible
      await replicationFlowEditor.properties.assertControlExists({
        id: DB_TEXT.RF_TARGET_OBJECT_DEPLOYMENT_STATUS,
      });

      /**
       * @issueid DW101-77853
       */
      // assert dwc target status not deployed
      await replicationFlowEditor.properties.assertText({
        id: DB_TEXT.RF_TARGET_OBJECT_DEPLOYMENT_STATUS,
        value: "@statusNew",
      });

      /**
       * @issueid DW101-78089
       */
      // open projection dialog and assert info message strip
      await openProjectionDialogFromToolbar(api, replicationFlowEditor);
      await switchToMappingTab(replicationFlowEditor);
      await replicationFlowEditor.assertText({
        id: DB_CONTROL.RF_TRANSFORMATION_DIALOG_MAPPING_TAB_EXISTING_TABLE_INFO_STRIP,
        value: "messageForExistingDWCTargetModification",
      });
      await cancelTransformationDialog(replicationFlowEditor);

      // back link
      await replicationFlowEditor.properties.pressButton(DB_BUTTON.RF_BACK_LINK);

      // assert replication flow status not deployed
      await replicationFlowEditor.assertText({
        id: DB_TEXT.RF_OBJECT_STATUS as any,
        value: "@statusNew",
      });

      // deploy
      await api.mockServerCall(
        "POST",
        "**/deploy**",
        [
          {
            text: "Saved and Deployment started",
            description: "Saved and Deployment started",
            severity: 4,
            messageCode: "DEPLOYMENT_STARTED",
          },
        ],
        "deploy"
      );
      await replicationFlowEditor.clickButton(DB_BUTTON.DEPLOY);
      await api.XHRWait("@deploy");

      // deployment started notification
      await replicationFlowEditor.assertMessageToast({
        text: "deploymentStarted",
      });

      // mock deployed design object
      await api.mockServerCall(
        "GET",
        "**/repository/designObjects**",
        {
          results: [
            {
              space_id: "B63500B0125134EC16000602BD71B1DF",
              qualified_name: "RF_SAVE_DEPLOY_DWC_TARGET",
              name: "RF_SAVE_DEPLOY_DWC_TARGET",
              kind: "sap.dis.replicationflow",
              owner: "I333638",
              creator: "I333638",
              deployment_date: "2024-12-01 11:43:58.671000000 UTC",
              modification_date: "2024-12-01 09:45:29.330000000 UTC",
              creation_date: "2024-12-01 09:45:29.330000000 UTC",
              id: "A715FED30526B7EF18006F6A264C27D9",
              "@EndUserText.label": "RF_SAVE_DEPLOY_DWC_TARGET",
              "#technicalType": "DWC_REPLICATIONFLOW",
              "#objectStatus": "1",
              "#deleteAccess": true,
              "#deployAccess": true,
              "#deploymentExecutionStatus": "success",
              "#writeAccess": true,
            },
          ],
        },
        "deployed",
        200
      );
      await api.XHRWait("@deployed");

      // deployment successfull notification
      await api.mockNotification([
        {
          id: "68BF875F28FCC68DE2002365FD039461",
          title: "The deployment of aggregation was successful",
          body: "Deploy successfull",
          para: '"{\\"status\\":\\"\\",\\"spaceName\\":\\"RFSPACE\\",\\"modelName\\":\\"RF_SAVE_DEPLOY_DWC_TARGET\\",\\"notificationSource\\":\\"deployment\\",\\"results\\":[{\\"saveSucceeded\\":true}]}"',
          type: 8,
          icon: "sap-icon://sac/success",
          link: '"{\\"target\\":{\\"semanticObject\\":\\"databuilder\\"},\\"params\\":{\\"spaceId\\":\\"RFSPACE\\",\\"model\\":\\"RF_SAVE_DEPLOY_DWC_TARGET\\"}}"',
        },
      ]);
      await replicationFlowEditor.assertMessageToast({
        text: "Deploy successful",
        checkOnMultiple: true,
      });

      // assert RF deployed
      await replicationFlowEditor.assertText({
        id: DB_TEXT.RF_OBJECT_STATUS as any,
        value: "@statusActive",
      });

      // assert target dwc deployed
      await selectReplicationObjectByIndex(api, 0);
      await replicationFlowEditor.properties.assertText({
        id: DB_TEXT.RF_TARGET_OBJECT_DEPLOYMENT_STATUS,
        value: "@statusActive",
      });
    });

    it("Open RF & assert existing targets, object status", async function () {
      // open RF with unsaved dsp targets
      await api.mockServerCall(
        "GET",
        "**/repository/designObjects**",
        "fixture:databuilder/replicationflowbuilder/RFSaveAndDeployDwcTarget/rfWithExistingTarget.json",
        "designObjects"
      );

      // open RF with 2 saved dwc targets (one is "changes to deploy" and other is "deployed" object status)
      const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", "RF_WITH_EXISTING_TARGET");

      // assert replication flow status "Design Time Error"
      await replicationFlowEditor.properties.assertText({
        id: DB_TEXT.RF_OBJECT_STATUS as any,
        value: "@statusDesignTimeError",
      });

      // assert dwc target is existing
      await assertDwcTableObjectState(0, replicationFlowEditor, false);
      await assertDwcTableObjectState(1, replicationFlowEditor, false);

      // select replication and assert target dwc "Changes to Deploy"
      await selectReplicationObjectByIndex(api, 0);
      await replicationFlowEditor.properties.assertText({
        id: DB_TEXT.RF_TARGET_OBJECT_DEPLOYMENT_STATUS,
        value: "@statusChangesToDeploy",
      });

      // select replication and assert target dwc "Deployed"
      await selectReplicationObjectByIndex(api, 1);
      await replicationFlowEditor.properties.assertText({
        id: DB_TEXT.RF_TARGET_OBJECT_DEPLOYMENT_STATUS,
        value: "@statusActive",
      });
    });

    /**
     * @issueid DW101-77733
     */
    it("Open old RF have unsaved dsp targets & prompt", async function () {
      // open RF with unsaved dsp targets
      await api.mockServerCall(
        "GET",
        "**/repository/designObjects**",
        "fixture:databuilder/replicationflowbuilder/RFSaveAndDeployDwcTarget/oldRFUnsavedDwcTable.json",
        "designObjects"
      );

      // open RF with unsaved dwc targets
      await openReplicationFlow(api, "RFSPACE", "OLD_RF_UNSAVED_DWC_TARGET");

      // assert dialog prompt to resave the unsaved dsp targets
      await api.assertMessageBox({ text: "openReplicationFlowUnsavedTargetObject" });
      await api.clickButton(DB_BUTTON.MSGBOX_OK);
    });

    async function assertDwcTableObjectState(tableIndex, replicationFlowEditor, assertNewTarget = false) {
      const newTargetLabelID = DB_TABLE.RF_MAIN_CANVAS_NEW_TARGET_LABEL_ID + tableIndex;
      const existingTargetLabelID = DB_TABLE.RF_MAIN_CANVAS_EXISTING_TARGET_LABEL_ID + tableIndex;
      if (assertNewTarget) {
        await replicationFlowEditor.assertControlExists({ id: newTargetLabelID });
        await replicationFlowEditor.assertControlNotExists({ id: existingTargetLabelID });
      } else {
        await replicationFlowEditor.assertControlExists({ id: existingTargetLabelID });
        await replicationFlowEditor.assertControlNotExists({ id: newTargetLabelID });
      }
    }
  });
});
