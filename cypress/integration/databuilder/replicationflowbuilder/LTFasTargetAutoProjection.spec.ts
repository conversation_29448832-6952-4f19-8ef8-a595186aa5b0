/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 * @format
 */

import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DB_BUTTON, DB_CONTROL, DB_TABLE } from "../../../pageobjects/databuilder/descriptor";
import spaceCapabilities from "./spaceCapabilities";
import {
  addProjectionAndSwitchToMappingTab,
  assertCountOfRowsInMappingTab,
  assertValidationInsideProjectionDialog,
  switchToMappingTab,
} from "./TransformationDialogUtils";
import { assertValidationInReplicationObjectPanel, openProjectionDialogFromToolbar, openReplicationFlow } from "./Util";
import ConnectionUtils from "./Utils/ConnectionUtils";

describe("cypress/integration/databuilder/replicationflowbuilder/LTFAsTargetAutoProjection", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    const routes = [...XHelpers.DEFAULT_MOCK_REPONSES, ...XHelpers.SDP_SPECIFIC_MOCK_RESPONSES];
    const readPrivileges = spaceCapabilities.readPrivilegesLSASpace;
    routes.push({
      protocol: "GET",
      path: "**/userprivileges**",
      response: readPrivileges,
      as: "getUpdatedUserPrivileges",
    });

    await api.setupBeforeEach(
      /* useMock*/ true,
      /* defaultFixture*/ true,
      /* routes*/ routes,
      /* overrideRouteCallback */ undefined,
      /* enabledFeatureFlags */ ["INFRA_DWC_TWO_TENANT_MODE", "DWC_DUMMY_SPACE_PERMISSIONS"]
    );
    await api.mockServerCall(
      "GET",
      "**/repository/spaces**",
      "fixture:databuilder/replicationflowbuilder/spaces",
      "getSpaces"
    );
    await api.mockServerCall(
      "GET",
      "**/repository/search/$all?**",
      "fixture:databuilder/replicationflowbuilder/LTFAsTarget/search$All",
      "landingPage"
    );
    await api.mockServerCall(
      "GET",
      "**/repository/RFSPACE/designObjects?folderIds**",
      "fixture:databuilder/replicationflowbuilder/LTFAsTarget/landingPageResponse",
      "designObjectsLandingPage"
    );
  });

  describe("Assert actions for target DWC ( HDL_Files ) / LTF", () => {
    it("Assert auto-projection for LTF Target", async function () {
      await api.mockServerCall(
        "GET",
        "**/repository/RFSPACE/designObjects?ids**",
        "fixture:databuilder/replicationflowbuilder/LTFAsTarget/AutoProjectionRF",
        "designObjects"
      );
      const replicationFlowEditor = await openReplicationFlowWithLTFAsTarget(api, "RF_DB_LTF_0");
      await api.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, 0);
      await api.waitHTMLStabilized();
      await assertValidationInReplicationObjectPanel(replicationFlowEditor, {
        infoCount: 1,
        containsMessage: "validationAutoRenameTarget",
      });
      await openProjectionDialogFromToolbar(api, replicationFlowEditor);
      await switchToMappingTab(replicationFlowEditor);
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 0, "_address");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 1, "AUTOPREFIX__address");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 3, 0, "street name");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 3, 1, "street_name");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 4, 0, "street+name");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 4, 1, "street_name_0");
      await assertValidationInsideProjectionDialog(replicationFlowEditor, 3, 0, 0, 3, "targetAutoRenameUpdated");
    });
    it("Assert auto-Datatype for LTF Target", async function () {
      await api.mockServerCall(
        "GET",
        "**/repository/RFSPACE/designObjects?ids**",
        "fixture:databuilder/replicationflowbuilder/LTFAsTarget/AutoProjectionRF",
        "designObjects"
      );
      const replicationFlowEditor = await openReplicationFlowWithLTFAsTarget(api, "RF_DB_LTF_0");
      await api.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, 1);
      await api.waitHTMLStabilized();
      await assertValidationInReplicationObjectPanel(replicationFlowEditor, {
        infoCount: 1,
        containsMessage: "validationAutoTargetTypeConversion",
      });
      await openProjectionDialogFromToolbar(api, replicationFlowEditor);
      await switchToMappingTab(replicationFlowEditor);
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 0, "COL_uint64");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 2, 3, "decimal");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 4, 0, "COL_DECFLOAT16");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 4, 3, "decimal");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 5, 0, "COL_DECFLOAT34");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 5, 3, "decimal");
      await assertValidationInsideProjectionDialog(replicationFlowEditor, 3, 0, 0, 3, "targetAutoDataType");
    });
    it("Time column in source as Primary Key", async function () {
      await api.mockServerCall(
        "GET",
        "**/repository/RFSPACE/designObjects?ids**",
        "fixture:databuilder/replicationflowbuilder/LTFAsTarget/TimeKeyRF",
        "designObjects"
      );
      const replicationFlowEditor = await openReplicationFlowWithLTFAsTarget(api, "RF_DB_LTF_TIME");
      await openValidationsInPropertyPanel(api, replicationFlowEditor);
      await replicationFlowEditor.properties.assertValidations({
        containerId: DB_CONTROL.RF_VALIDATION_POPOVER as any,
        errCount: 1,
        containsMessage: "validatePKTimeColumnLTF1",
      });
      await replicationFlowEditor.properties.assertValidations({
        containerId: DB_CONTROL.RF_VALIDATION_POPOVER as any,
        infoCount: 1,
        containsMessage: "TargetColumnSkippedLTF",
      });
    });
    it("Uint8 column in source as only Primary Key", async function () {
      await api.disableUIFeatureflag("DWCO_LOCAL_TABLE_FILES_HANAQRC4_2024");
      await api.mockServerCall(
        "GET",
        "**/repository/RFSPACE/designObjects?ids**",
        "fixture:databuilder/replicationflowbuilder/LTFAsTarget/Uint8KeyRF",
        "designObjects"
      );
      const replicationFlowEditor = await openReplicationFlowWithLTFAsTarget(api, "RF_DB_LTF_UINT8");
      await openValidationsInPropertyPanel(api, replicationFlowEditor);
      await replicationFlowEditor.properties.assertValidations({
        containerId: DB_CONTROL.RF_VALIDATION_POPOVER as any,
        errCount: 1,
        containsMessage: "validateNoPKInLTFTarget",
      });
      await replicationFlowEditor.properties.assertValidations({
        containerId: DB_CONTROL.RF_VALIDATION_POPOVER as any,
        infoCount: 1,
        containsMessage: "TargetColumnSkippedLTF",
      });
    });
    it("check the uint8 in the target data type dropdown", async function () {
      await api.enableUIFeatureflag("DWCO_LOCAL_TABLE_FILES_HANAQRC4_2024");
      await api.mockServerCall(
        "GET",
        "**/repository/RFSPACE/designObjects?ids**",
        "fixture:databuilder/replicationflowbuilder/LTFAsTarget/Uint8AsDataType",
        "designObjects"
      );

      const replicationFlowEditor = await openReplicationFlowWithLTFAsTarget(api, "RF_TEST_12");
      await addProjectionAndSwitchToMappingTab(0, replicationFlowEditor);
      await assertCountOfRowsInMappingTab(replicationFlowEditor, 12);

      await checkDatatypeInMappingTab(4, 2, "uint8", api);

      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 4, 0, "IS_ENABLED");
      await replicationFlowEditor.assertTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, 4, 3, "uint8");
    });
  });
});

async function openReplicationFlowWithLTFAsTarget(api: IAPI, rfName: string) {
  const replicationFlowEditor = await openReplicationFlow(api, "RFSPACE", rfName);
  await ConnectionUtils.searchAndSelectTargetConnection(
    "SAP Datasphere",
    "SAP Datasphere (HDL_FILES)",
    false,
    api,
    replicationFlowEditor,
    "RFSPACE"
  );
  return replicationFlowEditor;
}

async function openValidationsInPropertyPanel(api: IAPI, replicationFlowEditor) {
  await api.clickListItem(DB_TABLE.RF_EDITOR_MAIN_CANVAS, 0);
  await api.waitHTMLStabilized();
  await replicationFlowEditor.assertButton({
    button: DB_BUTTON.RF_OBJECT_VALIDATION_BTN,
  });
  await replicationFlowEditor.clickButton(DB_BUTTON.RF_OBJECT_VALIDATION_BTN);
}

async function checkDatatypeInMappingTab(rowInd: number, listItem: number, dataType: string, api: IAPI) {
  // Click the datatype dropdown cell
  await api.clickChildInTableCell(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID, rowInd, 3, 0);

  await cy
    .byId(DB_TABLE.TRANSFORMATION_DIALOG_TABLE_ID)
    .get(".sapUiSimpleFixFlexFlexContent")
    .filter(`:contains("${dataType}")`)
    .should("have.css", "visibility", "hidden")
    .find("li")
    .eq(listItem);
}
