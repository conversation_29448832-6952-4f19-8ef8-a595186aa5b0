/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { DB_BUTTON } from "../../../pageobjects/databuilder/descriptor";
import AppEmulator from "../common/appEmulator/AppEmulator";
import { NAMED_FEATURE_FLAGS } from "../common/serverEmulator/FeatureFlagsConstants";
import { documents } from "../common/serverEmulator/repository/documents";
import ServerEmulator from "../common/serverEmulator/ServerEmulator";
import { UITesting } from "../common/UITesting";
/* jscpd:ignore-start */
const lcPrefix =
  "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView";
describe("cypress/integration/databuilder/new_ermodeler/FiscalTimeDimension", () => {
  beforeEach(() => {
    UITesting.setup({ debug: false });
    ServerEmulator.setupConfiguration({
      spaces: {
        SPACE: {
          objects: [documents.Cypress_SimpleER],
        },
      },
      featureFlags: {
        ...NAMED_FEATURE_FLAGS.SDP,
        DWCO_REUSABLE_ERMODELER: true,
        DWCO_MODELING_SUPPORT_FISCAL_TIME: true,
      },
    });
  });

  describe("Check if coastguard validations are triggered for new ER modeler, FT table", () => {
    it.skip("Check for new table creation", function () {
      AppEmulator.dataBuilder.landing.visitObject("SPACE", "-newERModel");
      AppEmulator.dataBuilder.workbench.er.waitUntilAvailable();

      AppEmulator.dataBuilder.workbench.clickButton(DB_BUTTON.ER_TOGGLE_TREE);

      // Create table1
      AppEmulator.dataBuilder.workbench.er.getCreateTable().click();
      AppEmulator.dataBuilder.workbench.er.diagram.dropOnERDiagram(400, 400);
      AppEmulator.dataBuilder.workbench.er.diagram.assertEntityCount(1);
      AppEmulator.dataBuilder.workbench.er.ppty.selectDataSetType("@dimension");
      cy.get(`#${lcPrefix}--dimensionTypeSel-arrow`).scrollIntoView().click({ force: true });
      cy.wait(500);
      cy.get(`#${lcPrefix}--dimensionTypeSel-valueStateText`)
        .should("exist")
        .find(`li[role="option"]:contains("@txtDimensionTypeFiscalTime")`)
        .should("exist")
        .click({ force: true });
      cy.get(`#${lcPrefix}--detailsHeader--validationButton-BDI-content`).should("exist").should("contain.text", `8`);
    });
  });
});
/* jscpd:ignore-end */
