/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

/* eslint-disable @typescript-eslint/no-misused-promises */
/* eslint-disable @typescript-eslint/await-thenable */
/* eslint-disable @typescript-eslint/semi */
/* eslint-disable prefer-const */
/* eslint-disable @typescript-eslint/require-await */
import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DataBuilder } from "../../../pageobjects/databuilder";
import { DB_BUTTON, DB_LIST, DB_TABLE } from "../../../pageobjects/databuilder/descriptor";
import { ShellPages } from "../../../pageobjects/shell";

describe("SQL Editor Test", () => {
  let api: IAPI;

  beforeEach(async () => {
    api = getAPIInstance();

    await api.setupBeforeEach(/*useMock*/ true, /*defaultFixture*/ true, /*routes*/ XHelpers.DEFAULT_MOCK_REPONSES);
  });

  describe.skip("SQL Editor Util", async () => {
    it("Element count,icon,label check", async function () {
      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const sqlEditor = await databuilder.landingPage.createNewSQLEditor();

      await sqlEditor.setValue("SELECT * FROM view1");
      await api.mockServerCall(
        "POST",
        "**/buildcqn**",
        "fixture:databuilder/viewbuilder/checkdependency/designObjBuildCqnCols",
        "buildcqn"
      );

      await sqlEditor.clickButton(DB_BUTTON.SQL_VALIDATE_INNER);

      await sqlEditor.properties.assertList({
        list: DB_LIST.OUTPUT_ATTRIBUTES_LIST as any,
        rows: 12,
        tokens: [
          "col1",
          "col2",
          "currencyCode",
          "IsCalendarDate",
          "IsLanguageIdentifier",
          "IsDigitSequence",
          "IsFiscalYear",
          "IsFiscalYearVariant",
          "DateFrom",
          "DateTo",
          "measure1",
          "measure2",
        ],
      });
    });

    it("Element count,icon,label check without select star", async function () {
      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const sqlEditor = await databuilder.landingPage.createNewSQLEditor();

      await sqlEditor.setValue("SELECT measure2 FROM view1");
      await api.mockServerCall(
        "POST",
        "**/buildcqn**",
        "fixture:databuilder/viewbuilder/checkdependency/designObjBuildCqnMeasure",
        "buildcqn"
      );

      await sqlEditor.clickButton(DB_BUTTON.SQL_VALIDATE_INNER);

      await sqlEditor.properties.assertList({
        list: DB_LIST.OUTPUT_ATTRIBUTES_LIST as any,
        rows: 1,
        tokens: ["measure2"],
      });
    });

    it("Element count,icon,label check for sub select", async function () {
      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const sqlEditor = await databuilder.landingPage.createNewSQLEditor();
      sqlEditor.setValue("SELECT M1 from (Select 	V1.measure1 as M1	FROM view1 as V1)");
      await api.mockServerCall(
        "POST",
        "**/buildcqn**",
        "fixture:databuilder/viewbuilder/checkdependency/designObjBuildCqnSubquery",
        "buildcqn"
      );

      await sqlEditor.clickButton(DB_BUTTON.SQL_VALIDATE_INNER);
      await sqlEditor.properties.assertList({
        list: DB_LIST.OUTPUT_ATTRIBUTES_LIST as any,
        rows: 1,
        tokens: ["M1"],
      });
    });

    it("Buttons disable check", async function () {
      api.enableUIFeatureflag("DWCO_MODELING_SQL_INLINE_COMMENTS");

      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const sqlEditor = await databuilder.landingPage.createNewSQLEditor();
      const oEditor = (await cy.byId("sqlEditor")).getController();

      oEditor.buttonEnable(false);

      await api.assertButton({
        button: DB_BUTTON.SQL_VALIDATE_INNER,
        enabled: false,
      });
      await cy
        .byId("shellMainContent---databuilderComponent---databuilderWorkbench--save")
        .invoke("getEnabled")
        .should("eq", false);
      await cy
        .byId("shellMainContent---databuilderComponent---databuilderWorkbench--deploy")
        .invoke("getEnabled")
        .should("eq", false);
      await cy
        .byId("shellMainContent---databuilderComponent---databuilderWorkbench--detailsView")
        .invoke("getEnabled")
        .should("eq", false);
    });

    it("Refresh data and button enable check", async function () {
      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const sqlEditor = await databuilder.landingPage.createNewSQLEditor();
      const oData = {
        name: "ACCOUNT",
        id: "ACCOUNT",
        hasChildren: false,
        type: 8,
        icon: "sac/live-table",
        definitions: {
          ACCOUNT: {
            kind: "entity",
            elements: {
              NUMID: {
                type: "cds.Decimal",
                key: true,
                "@DataWarehouse.native.dataType": "DECIMAL",
                precision: 6,
                scale: 0,
              },
              LOGIN: { type: "cds.String", "@DataWarehouse.native.dataType": "VARCHAR", length: 250 },
              PSWD: { type: "cds.String", "@DataWarehouse.native.dataType": "VARCHAR", length: 250 },
              ADMIN: { type: "cds.Integer", "@DataWarehouse.native.dataType": "SMALLINT" },
            },
            "@EndUserText.label": "ACCOUNT",
            "@DataWarehouse.remote.connection": "hana-lucy",
            "@DataWarehouse.remote.entity": '"LUCY"."ACCOUNT"',
            "@Analytics.dataCategory": { "#": "DataSet" },
            "@DataWarehouse.pinToMemory": true,
          },
        },
        creator: "LUCY",
        creation_date: "2019-09-30 01:28:44.*********",
        owner: "LUCY",
        modification_date: "2019-10-14 06:22:13.*********",
        deployment_date: "2019-10-15 07:58:46.*********",
      };
      const oEditor = (await cy.byId("sqlEditor")).getController();
      oEditor.onDefaultSourceDragEnter(undefined, undefined, oData);
      oEditor.notifyDropEntity();
      sqlEditor.setValue("SELECT col1 FROM view1");

      const csn = await oEditor.getSqlAsCsn("testView");
      await expect(JSON.stringify(csn.definitions.testView.query)).to.equal(
        '{"SELECT":{"from":{"ref":["view1"]},"columns":[{"ref":["col1"]}]}}'
      );
    });

    it("Validate Error Check", async function () {
      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const sqlEditor = await databuilder.landingPage.createNewSQLEditor();
      sqlEditor.setValue("SELECT");
      await sqlEditor.clickButton(DB_BUTTON.SQL_VALIDATE_INNER);
      await cy
        .get(
          "#shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--detailsHeader--validationButton-content"
        )
        .should("have.text", "1");
      sqlEditor.setValue('SELECT column1, from "Table1" ');
      await sqlEditor.clickButton(DB_BUTTON.SQL_VALIDATE_INNER);
      await cy
        .get(
          "#shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--detailsHeader--validationButton-content"
        )
        .should("have.text", "1");
    });

    it("Drag and drop of the dataSource", async function () {
      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const viewModeler = await databuilder.landingPage.createNewSQLEditor();
      await viewModeler.wait(5);
      await viewModeler.browser.expandTreeList();
      let editor = viewModeler.getEditorControl().getController();
      await viewModeler.browser.dropOnSQLEditor("sqlEditor", "view1");
      await expect(editor.getValue()).to.equal("view1");
    });

    it("Element data type check for sub select with aggregation", async function () {
      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const sqlEditor = await databuilder.landingPage.createNewSQLEditor();
      sqlEditor.setValue(
        'SELECT sub1."M1",sub1."col2"  FROM (  SELECT  count("col1") AS "M1","col2" FROM "Customer")as sub1'
      );
      await sqlEditor.clickButton(DB_BUTTON.SQL_VALIDATE_INNER);
      await sqlEditor.properties.assertList({
        list: DB_LIST.OUTPUT_ATTRIBUTES_LIST as any,
        rows: 2,
        tokens: ["M1", "col2"],
      });
      await sqlEditor.properties.pressButton(DB_BUTTON.ER_PROPERTIES_EDIT_ATTRIBUTE);
      await sqlEditor.assertTableCell(DB_TABLE.ER_EDIT_ATTRIBUTE_DIALOG_TABLE, 0, 3, "Integer", false, true);
    });

    it("Element data type check for sub select with Join", async function () {
      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const sqlEditor = await databuilder.landingPage.createNewSQLEditor();
      sqlEditor.setValue(
        'SELECT sub1."newdate" FROM "view1" LEFT JOIN (SELECT to_date("col1") as "newdate","Col2" FROM "view1") AS sub1 ON sub1."Col2" = "Col2"'
      );
      await sqlEditor.clickButton(DB_BUTTON.SQL_VALIDATE_INNER);
      await sqlEditor.properties.assertList({
        list: DB_LIST.OUTPUT_ATTRIBUTES_LIST as any,
        rows: 1,
        tokens: ["newdate"],
      });
      await sqlEditor.properties.pressButton(DB_BUTTON.ER_PROPERTIES_EDIT_ATTRIBUTE);
      await sqlEditor.assertTableCell(DB_TABLE.ER_EDIT_ATTRIBUTE_DIALOG_TABLE, 0, 3, "Date", false, true);
    });

    it("Element data type check for multiple sub select with Join", async function () {
      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const sqlEditor = await databuilder.landingPage.createNewSQLEditor();
      sqlEditor.setValue(
        'SELECT sub1."newdate" FROM "view1" LEFT JOIN (SELECT  sub2."newdate",sub2."Col2" from ( select sub3."newdate",sub3."Col2"  from (SELECT to_date("col1") as "newdate","Col2" FROM "view1") as sub3)as sub2) AS sub1 ON sub1."Col2" = "Col2"'
      );
      await sqlEditor.clickButton(DB_BUTTON.SQL_VALIDATE_INNER);
      await sqlEditor.properties.assertList({
        list: DB_LIST.OUTPUT_ATTRIBUTES_LIST as any,
        rows: 1,
        tokens: ["newdate"],
      });
      await sqlEditor.properties.pressButton(DB_BUTTON.ER_PROPERTIES_EDIT_ATTRIBUTE);
      await sqlEditor.assertTableCell(DB_TABLE.ER_EDIT_ATTRIBUTE_DIALOG_TABLE, 0, 3, "Date", false, true);
    });

    it("subselect CONCAT calculated column string length set to ZERO(FPA101-10601)", async function () {
      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const sqlEditor = await databuilder.landingPage.createNewSQLEditor();
      sqlEditor.setValue(
        "SELECT sub1.Cal1 AS Cal1  FROM ((  SELECT  CONCAT('AA', 'BB') AS Cal1 FROM Customer) AS sub1 INNER JOIN view1 ON sub1.col1 = view1.measure1)"
      );
      await sqlEditor.clickButton(DB_BUTTON.SQL_VALIDATE_INNER);
      await sqlEditor.properties.assertList({
        list: DB_LIST.OUTPUT_ATTRIBUTES_LIST as any,
        rows: 1,
        tokens: ["Cal1"],
      });
      await sqlEditor.properties.pressButton(DB_BUTTON.ER_PROPERTIES_EDIT_ATTRIBUTE);
      await sqlEditor.assertTableCell(DB_TABLE.ER_EDIT_ATTRIBUTE_DIALOG_TABLE, 0, 3, "String(4)", false, true);
    });
  });
});
