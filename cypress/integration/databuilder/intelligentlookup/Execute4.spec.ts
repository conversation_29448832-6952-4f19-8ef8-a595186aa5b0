/**
 *
 * @format
 */

import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DataBuilder } from "../../../pageobjects/databuilder";
import { DB_BUTTON, DB_LIST, DB_TABLE, DB_TEXT, DB_VIEW } from "../../../pageobjects/databuilder/descriptor";
import { ShellPages } from "../../../pageobjects/shell";

const RULE1_NODE = "Node/rule1";
const SOURCE1_NODE = "Node/input1";
const SOURCE2_NODE = "Node/lookup1";
const OUTPUTVIEW = "Node/outputview1";

describe.skip("cypress/integration/databuilder/intelligentlookup/Execute4", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(/* useMock*/ true, /* defaultFixture*/ true, /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES);
    await api.mockServerCall(
      "GET",
      "**/repository/spaces**",
      "fixture:databuilder/intelligentlookup/spaces",
      "getSpaces"
    );
    await api.mockServerCall("POST", "**/deploy/**", [{ saveSucceeded: true }], "saveSucceeded");
    await api.mockServerCall("POST", "**/il/runtime**", {}, "triggerExecution");
    await api.mockServerCall("GET", "**/tf/**", [], "getStatus");
    await api.mockServerCall("GET", "**/metrics/**", "fixture:databuilder/intelligentlookup/ruleMetrics", "getMetrics");
    await api.mockServerCall(
      "GET",
      "**/repository/designObjects**",
      "fixture:databuilder/intelligentlookup/designObjects",
      "designObjects"
    );
  });

  describe("Execute spec part 4", () => {
    it("open intelligent lookup and execute should show success message toast", async function () {
      /* jscpd:ignore-start */
      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("ILSPACE");
      const openILEditor = await databuilder.landingPage.createNewIntelligentLookup();
      await openILEditor.toggleSidePanel();

      // expand the tree list
      await openILEditor.browser.expandTreeList();

      // add input node
      await openILEditor.browser.dragEntity("Opportunities");
      await openILEditor.diagram.dropOnSymbol({ id: SOURCE1_NODE });
      // click on input node
      cy.get("#" + DB_LIST.ILT_INPUT_COLUMNS).should("exist");
      // check if dragging columns work
      await openILEditor
        .propertiesView()
        .dragUI5ListItems(DB_LIST.ILT_INPUT_TABLE_ELEMENTS, ["Email"], DB_LIST.ILT_INPUT_COLUMNS + "-nodata");
      await openILEditor.propertiesView().dropUI5ListItems();
      await api.waitHTMLStabilized(true);

      // add lookup node
      await openILEditor.browser.dragEntity("SalesRep");
      await openILEditor.diagram.dropOnSymbol({ id: SOURCE2_NODE });
      await openILEditor.diagram.selectSymbol({ id: SOURCE2_NODE }, true);

      // click on lookup node
      cy.get("#" + DB_LIST.ILT_LOOKUP_COLUMNS).should("exist");
      // check if dragging columns work
      await openILEditor
        .propertiesView()
        .dragUI5ListItems(DB_LIST.ILT_LOOKUP_TABLE_ELEMENTS, ["Seniority"], DB_LIST.ILT_LOOKUP_COLUMNS + "-nodata");
      await openILEditor.propertiesView().dropUI5ListItems();
      await api.waitHTMLStabilized(true);
      // rule node enabled
      await openILEditor.diagram.selectSymbol({ id: RULE1_NODE }, true);
      cy.get("#" + DB_LIST.ILT_RULE_TYPE).should("exist");
      await openILEditor.selectSelectItem(DB_LIST.ILT_RULE_TYPE, 0);

      await api.wait(1);
      await openILEditor.assertText({
        id: DB_TEXT.ILT_RULE_TYPE_TEXT,
        value: "txtEXACTMATCH",
      });

      // Add mapping
      await openILEditor.mapJoinColumns(
        "Owner",
        "Full Name",
        DB_LIST.ILT_RULE_MAPPING_LISTA,
        DB_LIST.ILT_RULE_MAPPING_LISTB
      );
      await openILEditor.assertColumnsMapping("Owner", "Full Name", DB_VIEW.ILT_RULE_PROPERTIES);

      // check the execution status
      await openILEditor.clickButton(DB_BUTTON.ILT_AUTO_LAYOUT);
      await cy.get("#" + DB_TEXT.ILT_MODEL_RUN_STATUS).should("exist");
      await openILEditor.properties.assertText({
        id: DB_TEXT.ILT_MODEL_RUN_STATUS,
        value: "@lblNotRunYet",
      });

      // output node enabled
      await openILEditor.diagram.selectSymbol({ id: OUTPUTVIEW }, true);
      await cy.get("#" + DB_LIST.ILT_OUTPUTVIEW_COLUMNS_ELEMENTS).should("exist");
      // check output table columns
      await openILEditor.properties.assertList({
        list: DB_LIST.ILT_OUTPUTVIEW_COLUMNS_ELEMENTS as any,
        rows: 14,
      });

      await openILEditor.diagram.clickButton(DB_BUTTON.TOGGLE_DATAPREVIEW);
      await openILEditor.assertControl({
        id: DB_TABLE.ILT_OUTPUT_PREVIEW_TABLE,
        containsHTML: "msgNotDeployed",
      });
      /* jscpd:ignore-end */
    });
  });
});
