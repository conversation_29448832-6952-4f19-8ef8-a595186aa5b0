/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
// import { APIImpl } from "../../../pageobjects/api/impl/APIImpl";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DataBuilder } from "../../../pageobjects/databuilder";
import { DB_BUTTON, DB_VIEW } from "../../../pageobjects/databuilder/descriptor";
import { ShellPages } from "../../../pageobjects/shell";

const INPUT1 = "Node/input1";
const LOOKUP1 = "Node/lookup1";
let openILEditor;

describe("cypress/integration/databuilder/intelligentlookup/Editor4", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(/* useMock*/ true, /* defaultFixture*/ true, /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES);
    await api.mockServerCall(
      "GET",
      "**/repository/designObjects**",
      "fixture:databuilder/intelligentlookup/designObjects",
      "designObjects"
    );
    const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
    await api.openSpace("SPACE1234");
    openILEditor = await databuilder.landingPage.createNewIntelligentLookup();
    await openILEditor.browser.expandTreeList();
    await cy.get("#" + DB_VIEW.IL_MODEL_PROPERTIES).should("exist");
    await openILEditor.browser.dragEntity("Opportunities");
    await openILEditor.diagram.dropOnSymbol({ id: INPUT1 });
    await cy.get("#" + DB_VIEW.ILT_INPUT_PROPERTIES).should("exist");
  });

  describe.skip("check delete button", () => {
    /**
     * @issueid DW101-25528
     */
    it.skip("check delete button", async function () {
      /* jscpd:ignore-start */
      await openILEditor.browser.dragEntity("SalesRep");
      await openILEditor.diagram.dropOnSymbol({ id: LOOKUP1 });
      await cy.get("#" + DB_VIEW.ILT_LOOKUP_PROPERTIES).should("exist");
      await cy.get("#" + DB_BUTTON.ILT_DELETE_BUTTON).should("exist");
      await api.assertButton({
        button: DB_BUTTON.ILT_DELETE_BUTTON,
        enabled: false,
      });
      await openILEditor.diagram.selectSymbol({ id: "Node/rule1" });
      await cy.get("#" + DB_VIEW.ILT_RULE_PROPERTIES).should("exist");
      await openILEditor.assertButton({
        button: DB_BUTTON.ILT_DELETE_BUTTON,
        enabled: false,
      });
      await openILEditor.diagram.selectSymbol({ id: "Node/outputview1" });
      await cy.get("#" + DB_VIEW.ILT_OUTPUT_PROPERTIES).should("exist");
      await openILEditor.assertButton({
        button: DB_BUTTON.ILT_DELETE_BUTTON,
        enabled: false,
      });
      await openILEditor.diagram.selectSymbol({ id: "Node/rule1" });
      await openILEditor.diagram.clickContextPad(DB_BUTTON.ILT_ADD_NEW_RULE);
      const selectorBtn = openILEditor.win.jQuery("#il-addnewruleonmultitext");
      assert.isOk(selectorBtn.is(":visible"), "selector is visible");
      await openILEditor.addNewRule("il-addnewruleonmultitext", "@txtAddNewRuleOnMulti");
      await openILEditor.assertButton({
        button: DB_BUTTON.ILT_DELETE_BUTTON,
        enabled: true,
      });
      await openILEditor.diagram.unselectAll();
      await openILEditor.assertButton({
        button: DB_BUTTON.ILT_DELETE_BUTTON,
        enabled: false,
      });
      await openILEditor.diagram.selectSymbol({ id: "Node/rule2" });
      await openILEditor.assertButton({
        button: DB_BUTTON.ILT_DELETE_BUTTON,
        enabled: true,
      });
      /* jscpd:ignore-end */
    });

    it.skip("check delete data button should throw error", async function () {
      /* jscpd:ignore-start */
      await openILEditor.browser.dragEntity("SalesRep");
      await openILEditor.diagram.dropOnSymbol({ id: LOOKUP1 });
      await cy.get("#" + DB_VIEW.ILT_LOOKUP_PROPERTIES).should("exist");
      await cy.get("#shellMainContent---databuilderComponent---databuilderWorkbench--deleteData").click();
      await cy.get("#deleteDataErrorMsgbox").should("exist");
      /* jscpd:ignore-end */
    });
  });
});
