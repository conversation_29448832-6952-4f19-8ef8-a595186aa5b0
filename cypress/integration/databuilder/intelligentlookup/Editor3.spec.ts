/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { AB_BUTTON } from "../../../pageobjects/abstractbuilder/descriptor";
import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DataBuilder } from "../../../pageobjects/databuilder";
import { DB_BUTTON, DB_TEXT, DB_VIEW } from "../../../pageobjects/databuilder/descriptor";
import { ShellPages } from "../../../pageobjects/shell";

describe("cypress/integration/databuilder/intelligentlookup/Editor3", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(/* useMock*/ true, /* defaultFixture*/ true, /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES);
    await api.mockServerCall(
      "GET",
      "**/repository/designObjects**",
      "fixture:databuilder/intelligentlookup/designObjects",
      "designObjects"
    );
  });

  describe("check whether IL editor and has loaded on click of new tile 3", () => {
    /**
     * @issueid DW101-11243,DW101-11639,DW101-19626,DW101-23436
     */
    it.skip("check technical and business name cases", async function () {
      /* jscpd:ignore-start */
      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("SPACE1234");
      const openILEditor = await databuilder.landingPage.createNewIntelligentLookup();
      await cy.get("#" + DB_VIEW.IL_MODEL_PROPERTIES).should("exist");
      await cy.get("#" + DB_TEXT.ILT_MODEL_BUSINESSNAME).should("exist");
      await openILEditor.typeInputText(DB_TEXT.ILT_MODEL_BUSINESSNAME, "IL BNAME");
      await openILEditor.properties.assertInput({
        input: DB_TEXT.ILT_MODEL_TECHNICALNAME,
        text: "IL_BNAME",
      });
      await openILEditor.properties.typeInputText(DB_TEXT.ILT_MODEL_TECHNICALNAME, "IL_TNAME");
      await openILEditor.browser.expandTreeList();
      await openILEditor.browser.dragEntity("Opportunities");
      await openILEditor.diagram.dropOnSymbol({ id: "Node/input1" });
      await cy.get("#" + DB_VIEW.ILT_INPUT_PROPERTIES).should("exist");
      await openILEditor.browser.dragEntity("SalesRep");
      await openILEditor.diagram.dropOnSymbol({ id: "Node/lookup1" });
      await cy.get("#" + DB_VIEW.ILT_LOOKUP_PROPERTIES).should("exist");
      await openILEditor.diagram.clickContextPad(DB_BUTTON.ILT_DATA_PREVIEW);
      await openILEditor.diagram.selectSymbol({ id: "Node/outputview1" }, true);
      await openILEditor.properties.assertInput({
        input: DB_TEXT.ILT_OUTPUT_BUSINESSNAME,
        text: "IL BNAME",
      });
      await openILEditor.properties.assertInput({
        input: DB_TEXT.ILT_OUTPUT_TECHNICALNAME,
        text: "IL_TNAME",
      });
      await openILEditor.properties.typeInputText(DB_TEXT.ILT_OUTPUT_BUSINESSNAME, "IL BNAME modified");
      await openILEditor.properties.typeInputText(DB_TEXT.ILT_OUTPUT_TECHNICALNAME, "IL_TNAME_modified");
      await openILEditor.diagram.unselectAll();
      await openILEditor.properties.assertInput({
        input: DB_TEXT.ILT_MODEL_BUSINESSNAME,
        text: "IL BNAME modified",
      });
      await openILEditor.properties.assertInput({
        input: DB_TEXT.ILT_MODEL_TECHNICALNAME,
        text: "IL_TNAME_modified",
      });
      await openILEditor.save();
      await cy.get("#" + DB_BUTTON.COMMON_SAVE_ANYWAY).should("exist");
      await openILEditor.assertControl(
        {
          id: DB_BUTTON.COMMON_SAVE_ANYWAY as any,
          enabled: true,
        },
        true
      );
      // save anyway
      await openILEditor.clickButton(DB_BUTTON.COMMON_SAVE_ANYWAY);
      await cy.get("#" + AB_BUTTON.SAVE_DIALOG_SAVE).should("exist");
      await openILEditor.clickButton(AB_BUTTON.SAVE_DIALOG_SAVE);
      await openILEditor.properties.assertControlNotExists({
        id: "__error0",
      });

      /* jscpd:ignore-end */
    });
  });
});
