/** @format */

import { AB_BUTTON } from "../../../../pageobjects/abstractbuilder/descriptor";
import { getAPIInstance, IAPI } from "../../../../pageobjects/api/IAPI";
import { DB_BUTTON, DB_LIST, DB_VIEW } from "../../../../pageobjects/databuilder/descriptor";
import { IRepoObjectConfig } from "../../common/serverEmulator/ServerEmulator";
import { createNewTF, setupServerEmulator } from "../Util";

describe("cypress/integration/databuilder/transformationflow/spark/TFSparkTableAsSource", () => {
  let api: IAPI;

  beforeEach(() => {
    api = getAPIInstance();
    const additionalObjects: IRepoObjectConfig[] = [
      {
        document: require("../files/spark/Transformation_Flow_LTF.json"),
        technicalName: "Transformation_Flow_LTF",
        type: "er",
      },
    ];
    const additionalMocks: any[] = [
      {
        path: `**/userprivileges**`,
        protocol: "GET",
        response: "databuilder/transformationflow/userPrivileges.json",
        statusCode: 200,
        as: "getUserprivileges",
      },
    ];
    setupServerEmulator(
      additionalObjects,
      {
        DWC_DUMMY_SPACE_PERMISSIONS: true,
        DWCO_TRF_SPARK_EDITOR_MONITORING: true,
        DWCO_TRF_SPARK_PRI_ED_DND: true,
      },
      additionalMocks
    );
  });

  it("verify context pad options on drag and drop of table as source", async () => {
    const editor = await dragAndDropTableAsSource(api, "LTF_TABLE");

    await api.assertControlExists({
      id: `${DB_VIEW.TF_SOURCE_TABLE_NODE_PROPERTIES}`,
    });
    await api.assertControlExists({
      id: "Sap-Cdw-Transformationflow-AddPythonOperator",
    });
    await api.assertControlExists({
      id: "Sap-Cdw-Transformationflow-DataPreview",
    });
    await api.assertControlExists({
      id: "Sap-Cdw-Transformationflow-ImpactLineage",
    });
    await api.assertControlExists({
      id: "Sap-Cdw-Transformationflow-OpenInNewTab",
    });
    await api.assertControlExists({
      id: "Sap-Cdw-Transformationflow-Delete",
    });
  });

  it("verify property panel properties on drag and drop of table as source", async () => {
    const editor = await dragAndDropTableAsSource(api, "LTF_TABLE");
    // General Panel
    await api.assertControlExists({
      id: `${DB_VIEW.TF_SOURCE_TABLE_NODE_PROPERTIES}--GeneralPanel`,
      enabled: true,
    });
    await editor.properties.assertValue({
      id: `${DB_VIEW.TF_SOURCE_TABLE_NODE_PROPERTIES}--bname-inner`,
      value: "LTF TABLE",
    });
    await editor.properties.assertValue({
      id: `${DB_VIEW.TF_SOURCE_TABLE_NODE_PROPERTIES}--technicalNameInput-inner`,
      value: "LTF_TABLE",
    });
    await editor.properties.assertValue({
      id: `${DB_VIEW.TF_SOURCE_TABLE_NODE_PROPERTIES}--deltaCaptureName-inner`,
      value: "LTF_TABLE_Delta",
    });
    await api.assertSwitch({
      id: `${DB_VIEW.TF_SOURCE_TABLE_NODE_PROPERTIES}--deltaCapture`,
      state: true,
    });
    // Delta settings panel
    await api.assertControlExists({
      id: `${DB_VIEW.TF_SOURCE_TABLE_NODE_PROPERTIES}--deltaSettingsPanel`,
      enabled: true,
    });
    // Columns panel
    await api.assertControlExists({
      id: `${DB_VIEW.TF_SOURCE_TABLE_NODE_PROPERTIES}--NodeColumnsPanel`,
    });
    await editor.properties.assertList({
      list: DB_LIST.TF_SOURCE_TABLE_NODE_PROPERTIES_LIST,
      rows: 5,
    });
  });

  it("verify new target columns on drag and drop of table as source", async () => {
    const editor = await dragAndDropTableAsSource(api, "LTF_TABLE");

    await editor.createNewTarget();
    await editor.properties.assertList({
      list: DB_LIST.TF_TARGET_NODE_COLUMNS_LIST,
      rows: 5,
    });
  });

  it("save transformation flow with table as a source", async () => {
    const editor = await dragAndDropTableAsSource(api, "LTF_TABLE");

    await editor.createNewTarget();

    await saveTF(editor, api, "sourcetable1");
  });
});

async function dragAndDropTableAsSource(api, name) {
  const editor = await createNewTF(api);
  await editor.browser.expandTreeList();
  await editor.browser.assertItem({
    title: name,
    shouldExist: true,
  });
  await editor.dragAndDropToSource(name);
  return editor;
}

async function saveTF(editor, api, text) {
  await editor.pressButton(DB_BUTTON.SAVE);
  await editor.pressButton(AB_BUTTON.SAVE_DIALOG_SAVE);
  await api.assertServerRequestParameters("@saveModel", undefined, {
    containsText: text,
  });
  await editor.assertMessageToast({
    text: "modelSavedSuccessful(modelNameTransformationFlow)",
  });
}
