/** @format */

import { getAPIInstance, IAPI } from "../../../../pageobjects/api/IAPI";
import { DB_VIEW } from "../../../../pageobjects/databuilder/descriptor";
import { DI_PAGE } from "../../../flowmonitor/dataflow/Util";
import { MONITORING_DI_PAGE } from "../../../flowmonitor/replicationflow/Util";
import { IRepoObjectConfig } from "../../common/serverEmulator/ServerEmulator";
import { openTF, setupServerEmulator } from "../Util";

describe("cypress/integration/databuilder/transformationflow/spark/TFSparkNavToMonitoring", async () => {
  let api: IAPI;

  beforeEach(() => {
    api = getAPIInstance();
    const additionalObjects: IRepoObjectConfig[] = [
      {
        document: require("../files/spark/Transformation_Flow_LTF.json"),
        technicalName: "Transformation_Flow_LTF",
        type: "er",
      },
      {
        document: require("../files/spark/Transformation_Flow_LTF_TEST.json"),
        technicalName: "Transformation_Flow_LTF_TEST",
        type: "transformationflow",
        computedProperties: [
          {
            documentName: "Transformation_Flow_LTF_TEST",
            properties: {
              designObjects: {
                "#objectStatus": "1",
                deployment_date: "2023-08-18 05:36:07.369000000 UTC",
              },
            },
          },
        ],
      },
    ];
    const additionalMocks: any[] = [
      {
        path: `**/userprivileges**`,
        protocol: "GET",
        response: "databuilder/transformationflow/userPrivileges.json",
        statusCode: 200,
        as: "getUserprivileges",
      },
      {
        path: `**/runtimesettings/**`,
        protocol: "GET",
        response: {},
        statusCode: 200,
        as: "getRuntimeSettings",
      },
      {
        path: "**/deltasubscriptions/**",
        protocol: "GET",
        response: { data: [] },
        statusCode: 200,
        as: "deltasubscriptions",
      },
      {
        path: `**/resources/spaces?islocked&spaceids=**`,
        protocol: "GET",
        response: { SPACE: { isLocked: false } },
        statusCode: 200,
        as: "getSpaceLocked",
      },
      {
        path: "**/transformationflow/SPACE/status/Transformation_Flow_LTF_TEST**",
        protocol: "GET",
        response: {
          runs: [
            {
              logId: 901785,
              spaceId: "SPACE",
              applicationId: "TRANSFORMATION_FLOWS",
              objectId: "Transformation_Flow_LTF_TEST",
              activity: "EXECUTE",
              startTime: "2024-11-05T14:58:54.699Z",
              endTime: "2024-11-05T14:59:04.985Z",
              status: "COMPLETED",
              user: "Rishikesh Kumar",
              externalInstanceId: '{"asyncCallId":49426,"pollStart":"2024-11-05T14:59:04.273Z"}',
              runId: "fb3ce608f2f04914526636b72aa01d14",
              runTime: 10286,
            },
          ],
        },
        statusCode: 200,
        as: "getTransformationFlowRunStatus",
      },
    ];
    setupServerEmulator(
      additionalObjects,
      {
        DWC_DUMMY_SPACE_PERMISSIONS: true,
      },
      additionalMocks
    );
  });

  it("should save transformation flow with spark runtime and verify properties in monitoring screen as well as in overview screen", async function () {
    const editor = await openTF(api, "Transformation_Flow_LTF_TEST");
    await editor.properties.assertControlExists({
      id: `${DB_VIEW.TF_MODEL_PROPERTIES}--navIcon`,
    });

    await editor.properties.clickButton(`${DB_VIEW.TF_MODEL_PROPERTIES}--navIcon`, true);

    await editor.properties.assertControlExists({
      id: "shellMainContent---dataIntegrationComponent---dataflowdetails--runsTable",
    });

    await api.assertContainsText({
      id: `${DB_VIEW.DI_COMP}---dataflowdetails--runType` as any,
      value: "Apache Spark",
    });

    await api.assertControlExists({
      id: `${DI_PAGE}--masterRunsTableOPL-anchBar-${DI_PAGE}--tfSettings-anchor`,
    });

    await api.clickButton("__link1");

    await api.assertControlNotExists({
      id: `${MONITORING_DI_PAGE}--dataFlowsSegBtn-button`,
    });
  });
});
