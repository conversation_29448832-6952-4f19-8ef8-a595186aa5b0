/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { getAPIInstance } from "../../../../pageobjects/api/IAPI";
import { APIImpl } from "../../../../pageobjects/api/impl/APIImpl";
import { DB_BUTTON, DB_LIST, DB_TEXT, DB_VIEW } from "../../../../pageobjects/databuilder/descriptor";
import { VIEWTRANSFORM } from "../../../../pageobjects/databuilder/TransformationFlowEditor";
import { createNewTF, openTF, setupServerEmulator } from "../Util";

// eslint-disable-next-line @typescript-eslint/no-misused-promises
describe("cypress/integration/databuilder/transformationflow/sqleditor/SQLEditorConvertGVSourceToSQL", async () => {
  let api: APIImpl;
  beforeEach(() => {
    api = getAPIInstance();

    const buildCQNResponse = {
      status: 200,
      sql: 'SELECT "Column_1_String",\n\t"Column_2_Decimal",\n\t"Column_3_Integer",\n\t"Column_4_Date",\n\t"Column_5_Boolean"\nFROM "Cypress_SimpleTable"',
      csn: {
        SELECT: {
          from: { ref: ["Cypress_SimpleTable"] },
          columns: [
            { ref: ["Column_1_String"] },
            { ref: ["Column_2_Decimal"] },
            { ref: ["Column_3_Integer"] },
            { ref: ["Column_4_Date"] },
            { ref: ["Column_5_Boolean"] },
          ],
        },
      },
    };

    const additionalMocks: any[] = [
      {
        path: "**/buildcqn",
        protocol: "POST",
        response: buildCQNResponse,
        statusCode: 200,
        as: "buildcqn",
      },
      {
        path: "**/validate-csn",
        protocol: "POST",
        response: { result: [] },
        statusCode: 200,
        as: "validate-csn",
      },
      {
        path: "**/cdssql/buildsql",
        protocol: "POST",
        response: {
          sqlObj: [
            'CREATE TABLE "Customer" (\n  "COMPANY" NVARCHAR(5000),\n  "REGION" NVARCHAR(5000),\n  "TYPE" NVARCHAR(5000)\n);',
            'CREATE TABLE "Sales" (\n  "COMPANY" NVARCHAR(5000),\n  "YEAR" BIGINT,\n  "AMOUNT" BIGINT\n);',
            'CREATE VIEW "sqltransform1" AS SELECT\n  "Customer_0"."COMPANY",\n  "Customer_0"."REGION",\n  "Customer_0"."TYPE",\n  "Sales_1"."YEAR",\n  "Sales_1"."AMOUNT"\nFROM ("Customer" AS "Customer_0" INNER JOIN "Sales" AS "Sales_1" ON "Customer_0"."COMPANY" = "Sales_1"."COMPANY");',
          ],
        },
        statusCode: 200,
        as: "buildsql",
      },
    ];

    setupServerEmulator(undefined, {}, additionalMocks);
  });

  it("Open existing TF with view transform - switch to sql source and verify", async function () {
    const editor = await openTF(api, "Transformation_Flow_Simple");
    await editor.diagram.selectSymbol({ id: VIEWTRANSFORM }, true);
    await api.assertText({
      id: DB_TEXT.TF_VT_MODE,
      value: "graphicalView",
    });
    await editor.diagram.clickContextPad(DB_BUTTON.TF_OPEN_SQL_EDITOR);
    await editor.assertControlExists({
      id: DB_VIEW.TF_GV_SQL_SWITCH_DIALOG,
    });
    await api.confirmDialog(DB_VIEW.TF_GV_SQL_SWITCH_DIALOG, "confirmSQLViewTransform");
    editor.secondaryEditor = await editor.getSQLSecondaryEditor();
    const sqlEditor = editor.getSecondaryEditorController();
    await api.XHRWait("@buildsql");
    await api.assertList({
      list: DB_LIST.TF_SQL_SECONDARY_EDITOR_COLUMNS_LIST as any,
      rows: 5,
    });
    // update the script
    await sqlEditor.setValue('SELECT * FROM "Cypress_SimpleTable"');
    await editor.navigateBackToPrimaryEditor();

    await editor.assertMessageToast({
      text: "validateSQLOk",
    });

    await editor.assertMessageToast({
      text: "viewTransformChangesUpdated",
    });

    await api.assertText({
      id: DB_TEXT.TF_VT_MODE,
      value: "sqlView",
    });

    await api.assertText({
      id: DB_TEXT.TF_VT_DB_VIEW_TYPE,
      value: "@sql",
    });

    // check the number of columns in view transform
    await editor.properties.assertList({
      list: DB_LIST.TF_VIEW_TRANSFORM_NODE_COLUMNS_LIST,
      rows: 5,
    });
  });

  it("DW101-68455 create TF with error in graphical view transform - switch to sql source should show error ", async function () {
    const editor = await createNewTF(api);
    await editor.diagram.selectSymbol({ id: VIEWTRANSFORM }, true);
    await editor.pressUseGraphicalViewEditor();

    await editor.browser.expandTreeList();
    await editor.browser.assertItem({
      title: "Cypress_SimpleTable",
      shouldExist: true,
    });

    await editor.dragAndDropEntityToSecondaryEditor("Cypress_SimpleTable");
    await editor.diagram.clickContextPad(DB_BUTTON.GV_ADD_CALCULATED_NODE);
    // click the add calculated column menu item
    await editor.properties.clickMenuButtonItem(DB_BUTTON.GV_ADD_CALCULATED_COLUMN_MENU_WITH_RENAME_FF, 1); // Has to set index to 2
    await editor.properties.clickButton(DB_BUTTON.GV_NODE_RENAME_CALCULATED_BACK_TO_FIELDS, true); // Back

    await editor.navigateBackToPrimaryEditor();

    await editor.assertMessageToast({
      text: "viewTransformChangesUpdated",
    });
    await editor.diagram.clickContextPad(DB_BUTTON.TF_OPEN_SQL_EDITOR);
    await editor.assertControlExists({
      id: "convertSQLViewTransformErrorMessageBox",
    });
  });
});
