/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { CardinalityValues } from "../../../../src/components/commonmodel/model/types/cds.types";
import { getAPIInstance, IAPI } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { DataBuilder } from "../../../pageobjects/databuilder";
import { DB_BUTTON, DB_INPUT, DB_LIST, DB_TEXT } from "../../../pageobjects/databuilder/descriptor";
import { ShellPages } from "../../../pageobjects/shell";

describe("cypress/integration/databuilder/viewbuilder/JoinSuggestion2", () => {
  let api: IAPI;
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(
      /* useMock*/ true,
      /* defaultFixture*/ true,
      /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES,
      /* overrideRouteCallback*/ undefined,
      /* enabledFeatureFlags*/ ["DWC_SHELL_USER_PROFILE", "DWC_MODELING_SWITCH_TECHNICAL_NAME"]
    );
  });

  describe("{viewbuilder} Join Suggestion spec2", () => {
    it("join related entities from context pad", async function () {
      await api.enableUIFeatureflag("DWC_MODELING_VIEW_JOIN_CARDINALITIES");
      await api.mockServerCall(
        "GET",
        "**/repository/shares**",
        "fixture:databuilder/ermodeler/addrelated/sharedObjects_city",
        "sharedObjects"
      );
      await api.mockServerCall(
        "GET",
        "**/repository/dependencies/**",
        "fixture:databuilder/ermodeler/addrelated/dependencies3",
        "dependencies"
      );
      await api.mockServerCall(
        "GET",
        "**/repository/spaces**",
        "fixture:databuilder/ermodeler/checkdependency/spaces",
        "getSpaces"
      );
      await api.mockServerCall(
        "GET",
        "**/repository/designObjects**",
        "fixture:databuilder/ermodeler/checkdependency/designObjects",
        "designObjects"
      );

      const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
      await api.openSpace("ERSPACE");
      const viewbuilder = await databuilder.landingPage.createNewGraphicalView();

      await viewbuilder.browser.expandTreeList();
      await api.waitHTMLStabilized(true);

      // Switch display settings to techname
      await api.mockServerCall("POST", "**/security/preference/**", "1", "SwitchTechBussName");
      await viewbuilder.clickButton("AppShell_UserInfoTooltip");
      await viewbuilder.clickButton("SettingsTooltip");
      await viewbuilder.clickListItem(DB_LIST.USER_SETTINGS_SECTION_LIST, 4, true);
      await viewbuilder.clickButton(DB_BUTTON.OBJECT_NAME_DISPLAY_TECHNICAL_NAME);
      await viewbuilder.clickButton(DB_BUTTON.USER_SETTINGS_SAVE_BUTTON);
      await api.waitHTMLStabilized(true);

      // drag&drop Entity Table_Types
      await viewbuilder.browser.dragEntity("Table_Types");
      await viewbuilder.diagram.dropOnGraph();
      await api.waitHTMLStabilized(true);
      await viewbuilder.diagram.assertSymbol({
        id: "Entity/Table_Types",
      });
      await viewbuilder.diagram.selectSymbol({ id: "Entity/Table_Types" }, true);
      await api.waitHTMLStabilized(true);
      await viewbuilder.diagram.assertControl({ id: DB_BUTTON.GV_JOIN_SUGGESTION }, true);

      // click join related entities
      await viewbuilder.diagram.clickContextPad(DB_BUTTON.GV_JOIN_SUGGESTION);
      await api.XHRWait("@dependencies");
      await api.XHRWait("@sharedObjects");
      await api.waitHTMLStabilized(true);
      await viewbuilder.assertControl(
        {
          id: DB_BUTTON.GV_JOIN_SUGGESTION_SELECT,
          containsHTML: "Table_Dim1",
        },
        true
      );
      await viewbuilder.assertControl(
        {
          id: DB_BUTTON.GV_JOIN_SUGGESTION_SELECT,
          notContainsHTML: "Table Dim1",
        },
        true
      );
      await viewbuilder.assertControl(
        {
          id: DB_BUTTON.GV_JOIN_SUGGESTION_SELECT,
          containsHTML: "CHAOYUE519.city",
        },
        true
      );
      await viewbuilder.assertControl(
        {
          id: DB_BUTTON.GV_JOIN_SUGGESTION_SELECT,
          notContainsHTML: "(CHAOYUE519.city",
        },
        true
      );
      await viewbuilder.selectSelectItem(DB_BUTTON.GV_JOIN_SUGGESTION_SELECT, 1);
      await api.waitHTMLStabilized(true);
      await viewbuilder.diagram.assertSymbol({
        id: "Entity/Table_Dim1",
      });

      // Many to one default cardinality is set if associated tables are joined
      await viewbuilder.assertSelect({
        id: DB_INPUT.JOIN_LEFT_CARDINALITY as any,
        selectedItemKey: CardinalityValues.MANY,
      });
      await viewbuilder.assertSelect({
        id: DB_INPUT.JOIN_RIGHT_CARDINALITY as any,
        selectedItemKey: CardinalityValues.ONE,
      });

      // check object status
      await viewbuilder.diagram.selectSymbol({ id: "Entity/Table_Dim1" }, true);
      await api.waitHTMLStabilized(true);
      await viewbuilder.properties.assertInput({
        input: DB_INPUT.ENTITY_TECHNICAL_NAME,
        text: "Table_Dim1",
      });
      await viewbuilder.properties.assertText({
        id: DB_TEXT.GV_ENTITY_OBJECT_STATUS,
        value: "@statusActive",
      });

      // Switch display settings to bussname
      await api.mockServerCall("POST", "**/security/preference/**", "1", "SwitchTechBussName");
      await viewbuilder.clickButton("AppShell_UserInfoTooltip");
      await viewbuilder.clickButton("SettingsTooltip");
      await viewbuilder.clickListItem(DB_LIST.USER_SETTINGS_SECTION_LIST, 3, true);
      await viewbuilder.clickButton(DB_BUTTON.OBJECT_NAME_DISPLAY_BUSINESS_NAME);
      await viewbuilder.clickButton(DB_BUTTON.USER_SETTINGS_SAVE_BUTTON);
      await api.waitHTMLStabilized(true);

      await viewbuilder.diagram.selectSymbol({ id: "Entity/Table_Types" }, true);
      await api.waitHTMLStabilized(true);

      // click join related entities for cross space sharing
      await viewbuilder.diagram.assertControl({ id: DB_BUTTON.GV_JOIN_SUGGESTION }, true);
      await viewbuilder.diagram.clickContextPad(DB_BUTTON.GV_JOIN_SUGGESTION);
      await api.XHRWait("@dependencies");
      await api.XHRWait("@sharedObjects");
      await api.mockServerCall(
        "GET",
        "**/repository/shares*filters=*city",
        "fixture:databuilder/ermodeler/addrelated/sharedObjects_city",
        "sharedObjects_city"
      );
      await api.assertItemsCount({
        id: DB_BUTTON.GV_JOIN_SUGGESTION_SELECT,
        count: 3,
      });
      await api.assertControl(
        {
          id: DB_BUTTON.GV_JOIN_SUGGESTION_SELECT,
          containsHTML: "city (CHAOYUE519.city)",
        },
        true
      );
      await viewbuilder.assertControl(
        {
          id: DB_BUTTON.GV_JOIN_SUGGESTION_SELECT,
          containsHTML: "Table Dim1",
        },
        true
      );
      await viewbuilder.assertControl(
        {
          id: DB_BUTTON.GV_JOIN_SUGGESTION_SELECT,
          notContainsHTML: "Table Dim1 (",
        },
        true
      );

      await viewbuilder.selectSelectItem(DB_BUTTON.GV_JOIN_SUGGESTION_SELECT, 0);
      await api.XHRWait("@sharedObjects_city");
      await api.waitHTMLStabilized(true);
      await viewbuilder.diagram.assertSymbol({
        id: "Entity/CHAOYUE519.city",
      });

      // click join related entities when no object shared to current space
      await api.mockServerCall("GET", "**/repository/shares**", {}, "sharedObjects");
      await api.waitHTMLStabilized(true);
      await viewbuilder.diagram.selectSymbol({ id: "Entity/Table_Types" }, true);
      await api.waitHTMLStabilized(true);
      await viewbuilder.diagram.assertControl({ id: DB_BUTTON.GV_JOIN_SUGGESTION }, true);
      await viewbuilder.diagram.clickContextPad(DB_BUTTON.GV_JOIN_SUGGESTION);
      await api.XHRWait("@dependencies");
      await api.XHRWait("@sharedObjects");
      await api.assertItemsCount({
        id: DB_BUTTON.GV_JOIN_SUGGESTION_SELECT,
        count: 2,
      });
    });
  });
});
