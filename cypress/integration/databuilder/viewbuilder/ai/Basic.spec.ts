/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { ToolName } from "../../../../../src/components/reuse/utility/Constants";
import { ShellTestHelper } from "../../../crossarchitecture/shell/utility/ShellTestHelper";
import AppEmulator from "../../common/appEmulator/AppEmulator";
import { FEATURE_FLAGS_SET, NAMED_FEATURE_FLAGS } from "../../common/serverEmulator/FeatureFlagsConstants";
import { documents } from "../../common/serverEmulator/repository/documents";
import ServerEmulator from "../../common/serverEmulator/ServerEmulator";
import { DWC_ROUTES, UITesting } from "../../common/UITesting";
/* jscpd:ignore-start */

describe("cypress/integration/databuilder/viewbuilder/ai/Basic", () => {
  beforeEach(() => {
    UITesting.setup({ debug: false });
    ServerEmulator.setupConfiguration({
      spaces: {
        SOURCE_SPACE: {
          objects: [documents.Cypress_SimpleView_L2],
        },
      },
      featureFlagsSet: FEATURE_FLAGS_SET.LEGACY,
      featureFlags: {
        DWCO_MODELING_GEN_AI_SEMANTIC_ENRICHMENT: true,
        ...NAMED_FEATURE_FLAGS.SDP,
      },
    });
    UITesting.visit({ route: DWC_ROUTES.home });
  });

  const finialResponse = {
    csnEnrichments: {
      definitions: {
        Cypress_SimpleView_L2: {
          kind: "entity",
          elements: {
            Column_1_String: {
              "@AI.reason": "reason to text",
              "@Semantics.text": true,
            },
            Column_2_Decimal: {
              "@AI.reason": "reason to key",
              key: true,
              "@ObjectModel.text.element": {
                "=": "Column_1_String",
              },
            },
            Column_3_Integer: {
              "@AI.reason": "reason to measure",
              "@AnalyticsDetails.measureType": {
                "#": "BASE",
              },
              "@Aggregation.default": {
                "#": "SUM",
              },
              "@Semantics.amount.currencyCode": {
                "=": "Column_4_Date",
              },
            },
            Column_4_Date: {
              "@AI.reason": "reason to currency code",
              "@Semantics.currencyCode": true,
            },
          },
          "@AI.reason": "reason to fact",
          "@ObjectModel.modelingPattern": {
            "#": "ANALYTICAL_FACT",
          },
        },
      },
    },
  };

  it("Basic flow", function () {
    AppEmulator.visitDataBuilderEditor({
      spaceId: "SOURCE_SPACE",
      objectTechnicalName: "Cypress_SimpleView_L2",
    });
    AppEmulator.dataBuilder.workbench.browser.waitUntilAvailable();
    AppEmulator.dataBuilder.workbench.waitUntilDOMStable();
    cy.wait("@designObjects");
    AppEmulator.dataBuilder.workbench.gv.diagram.selectSymbol("Output/Cypress_SimpleView_L2");
    AppEmulator.dataBuilder.workbench.gv.ppty.assertOutputObjectStatus("@statusNew");

    AppEmulator.dataBuilder.workbench.getUndoButton().should("be.disabled");
    AppEmulator.dataBuilder.workbench.gv.ppty.getAIStatusButton().should("not.exist");

    AppEmulator.dataBuilder.workbench.clickGenerateSemantics();
    AppEmulator.dataBuilder.workbench.aicDialog.verifyOptions(3);
    AppEmulator.interceptPostRequest("**/genai-modeling/semantic-enrichment/SOURCE_SPACE", "startAI", undefined, {
      requestId: "some-guid",
    });
    AppEmulator.mockServerCall(
      "GET",
      "**/genai-modeling/semantic-enrichment/SOURCE_SPACE/progress/some-guid",
      {
        messageCode: "GENAI_MODELING_SEMANTIC_ENRICHMENT_IN_PROGRESS",
        parameters: [3],
        text: "Enriching: 3 columns remaining",
      },
      "AIProgress1"
    );
    AppEmulator.dataBuilder.workbench.aicDialog.generate();
    cy.wait("@startAI");
    cy.wait("@AIProgress1");
    AppEmulator.mockServerCall(
      "GET",
      "**/genai-modeling/semantic-enrichment/SOURCE_SPACE/progress/some-guid",
      finialResponse,
      "AICompletion"
    );
    cy.wait("@AICompletion");

    // Check undo button
    AppEmulator.dataBuilder.workbench.getUndoButton().should("be.enabled");

    // Check AI status button
    AppEmulator.dataBuilder.workbench.gv.ppty.getAIStatusButton().should("be.enabled").click();
    AppEmulator.dataBuilder.workbench.aisPopover.close();

    // Check AI change message strip on entity
    AppEmulator.dataBuilder.workbench.gv.ppty.getMessageStripEntityHasAIChange().should("be.visible");

    // Check Review Data Category AI Change Button
    AppEmulator.dataBuilder.workbench.gv.ppty
      .getReviewDataCategoryAIChangeButton()
      .should("exist")
      .and("be.visible")
      .click();
    AppEmulator.dataBuilder.workbench.airPopover
      .getPreviousValuesOfDataCategory()
      .should("exist")
      .and("contain.text", "@dataSet");
    AppEmulator.dataBuilder.workbench.airPopover.close();

    // Check Data Category AI Change
    AppEmulator.dataBuilder.workbench.gv.ppty
      .getDataSetTypeSelAI()
      .should("be.visible")
      .should("contain.html", "@sqlfact");
    AppEmulator.dataBuilder.workbench.gv.ppty.getDataSetTypeSelAIArrow().click({ force: true });
    AppEmulator.dataBuilder.workbench.gv.ppty
      .getDataSetTypeSelAIList()
      .should(
        "contain.text",
        "@recommendations@sqlfact@other@dimension@text@hierarchy@hierarchyWithDirectory@dataSet@factDeprecated"
      );

    AppEmulator.dataBuilder.workbench.gv.ppty
      .getReviewDataCategoryAIChangeButton()
      .should("exist")
      .and("be.visible")
      .click();
    AppEmulator.dataBuilder.workbench.airPopover.revert();
    AppEmulator.dataBuilder.workbench.gv.ppty.getReviewDataCategoryAIChangeButton().should("not.be.enabled");
    AppEmulator.dataBuilder.workbench.gv.ppty
      .getDataSetTypeSelAI()
      .should("be.visible")
      .should("contain.html", "@dataSet");
    AppEmulator.dataBuilder.workbench.getUndoButton().click(); // undo the revert
    AppEmulator.dataBuilder.workbench.gv.ppty.getReviewDataCategoryAIChangeButton().should("be.enabled");
    AppEmulator.dataBuilder.workbench.gv.ppty
      .getDataSetTypeSelAI()
      .should("be.visible")
      .should("contain.html", "@sqlfact");

    // Check Attributes AI Change
    AppEmulator.dataBuilder.workbench.gv.ppty.getMessageStripAttributesHasAIChange().should("be.visible");

    // Open the attributes dialog
    AppEmulator.dataBuilder.workbench.gv.ppty.editAttributes();

    // Check semantic type of 1st row
    AppEmulator.dataBuilder.workbench.gv.ppty.eaDialog.checkSelectValueInAttributesTableCell(
      0,
      4,
      "@Semantics.text",
      "input"
    );
    // Check text/association column for 2nd row
    AppEmulator.dataBuilder.workbench.gv.ppty.eaDialog.checkSelectValueInAttributesTableCell(
      1,
      5,
      "Column_1_String",
      "span"
    );

    // Check review popover of 2nd row
    AppEmulator.dataBuilder.workbench.gv.ppty.eaDialog.clickReviewButton(1, 6);
    AppEmulator.dataBuilder.workbench.airPopover
      .getPopover()
      .should("exist")
      .and(
        "contain.text",
        "@reviewTitlereasoning:reason to keypreviousValueskey:notSelected@txtLabelAssociation:@none@revertChanges"
      );

    // Check the rest of review buttons are hidden
    AppEmulator.dataBuilder.workbench.gv.ppty.eaDialog.getReviewButton(3, 6).should("not.exist");

    // Test revert changes
    AppEmulator.dataBuilder.workbench.gv.ppty.eaDialog.clickReviewButton(0, 6);
    AppEmulator.dataBuilder.workbench.airPopover
      .getPopover()
      .should("exist")
      .and(
        "contain.text",
        "@reviewTitlereasoning:reason to textpreviousValueskey:selectedsemanticType:@none@revertChanges"
      );
    AppEmulator.dataBuilder.workbench.airPopover.revert();
    AppEmulator.dataBuilder.workbench.gv.ppty.eaDialog.getReviewButton(0, 6).should("not.be.enabled");
    AppEmulator.dataBuilder.workbench.gv.ppty.eaDialog.checkSelectValueInAttributesTableCell(0, 4, "@none", "input");
    // Check text/association column for 2nd row also changed due to no label column
    AppEmulator.dataBuilder.workbench.gv.ppty.eaDialog.checkSelectValueInAttributesTableCell(1, 5, "", "span");

    AppEmulator.dataBuilder.workbench.gv.ppty.eaDialog.close();

    // Open the measures dialog
    AppEmulator.dataBuilder.workbench.gv.ppty.editMeasures();
    // Check semantic type and unit column of 1st row
    AppEmulator.dataBuilder.workbench.gv.ppty.emDialog.checkSelectValueInAttributesTableCell(
      0,
      4,
      "@Semantics.amount.currencyCode",
      "input"
    );
    AppEmulator.dataBuilder.workbench.gv.ppty.emDialog.checkSelectValueInAttributesTableCell(
      0,
      5,
      "Column_4_Date",
      "input"
    );
    // Check review popover of 1st row
    AppEmulator.dataBuilder.workbench.gv.ppty.emDialog.clickReviewButton(0, 6);
    AppEmulator.dataBuilder.workbench.airPopover
      .getPopover()
      .should("exist")
      .and(
        "contain.text",
        "@reviewTitlereasoning:reason to measurepreviousValuessemanticType:@none@txtMeasureUnitColumn:@nonecolumnType:@attribute@revertChanges"
      );
  });

  it("No AI changes then no dirty", function () {
    AppEmulator.visitDataBuilderEditor({
      spaceId: "SOURCE_SPACE",
      objectTechnicalName: "Cypress_SimpleView_L2",
    });
    AppEmulator.dataBuilder.workbench.browser.waitUntilAvailable();
    AppEmulator.dataBuilder.workbench.waitUntilDOMStable();
    cy.wait("@designObjects");
    AppEmulator.dataBuilder.workbench.gv.diagram.selectSymbol("Output/Cypress_SimpleView_L2");
    AppEmulator.dataBuilder.workbench.gv.ppty.assertOutputObjectStatus("@statusNew");

    AppEmulator.dataBuilder.workbench.getUndoButton().should("be.disabled");
    AppEmulator.dataBuilder.workbench.gv.ppty.getAIStatusButton().should("not.exist");

    AppEmulator.dataBuilder.workbench.clickGenerateSemantics();
    AppEmulator.dataBuilder.workbench.aicDialog.verifyOptions(3);
    AppEmulator.interceptPostRequest("**/genai-modeling/semantic-enrichment/SOURCE_SPACE", "startAI", undefined, {
      requestId: "some-guid",
    });
    AppEmulator.mockServerCall(
      "GET",
      "**/genai-modeling/semantic-enrichment/SOURCE_SPACE/progress/some-guid",
      {
        messageCode: "GENAI_MODELING_SEMANTIC_ENRICHMENT_IN_PROGRESS",
        parameters: [3],
        text: "Enriching: 3 columns remaining",
      },
      "AIProgress1"
    );
    AppEmulator.dataBuilder.workbench.aicDialog.generate();
    cy.wait("@startAI");
    cy.wait("@AIProgress1");
    // Mocking the server response to simulate no AI changes
    AppEmulator.mockServerCall(
      "GET",
      "**/genai-modeling/semantic-enrichment/SOURCE_SPACE/progress/some-guid",
      {
        csnEnrichments: {
          definitions: {
            Cypress_SimpleView_L2: {
              kind: "entity",
              elements: {
                Column_1_String: {
                  "@AI.reason": "reason to text",
                  key: true, // Same key as before
                },
                Column_2_Decimal: {
                  "@AI.reason": "reason to key",
                },
              },
              "@AI.reason": "reason to dimensions",
              "@ObjectModel.modelingPattern": {
                "#": "DATA_STRUCTURE",
              },
            },
          },
        },
      },
      "AICompletion"
    );
    cy.wait("@AICompletion");
    AppEmulator.dataBuilder.workbench.waitUntilDOMStable();
    AppEmulator.dataBuilder.workbench.waitUntilToolbarDomStable();

    // Check disabled undo button
    AppEmulator.dataBuilder.workbench.getUndoButton().should("be.disabled");
    // Check workbench is not dirty
    ShellTestHelper.checkToolDirtyState(ToolName.DataBuilder, false);
  });

  // Note:
  // No way to test the scenario: If the final response is completed after 2 seconds,
  // a new/useless call will be triggered with 404 response,
  // but we should not show the error message.
  // This test is just to ensure the secondary AI quest still working.
  it("Ensure the secondary AI quest working", function () {
    AppEmulator.visitDataBuilderEditor({
      spaceId: "SOURCE_SPACE",
      objectTechnicalName: "Cypress_SimpleView_L2",
    });
    AppEmulator.dataBuilder.workbench.browser.waitUntilAvailable();
    AppEmulator.dataBuilder.workbench.waitUntilDOMStable();
    cy.wait("@designObjects");
    AppEmulator.dataBuilder.workbench.gv.diagram.selectSymbol("Output/Cypress_SimpleView_L2");
    AppEmulator.dataBuilder.workbench.gv.ppty.assertOutputObjectStatus("@statusNew");

    AppEmulator.dataBuilder.workbench.getUndoButton().should("be.disabled");
    AppEmulator.dataBuilder.workbench.gv.ppty.getAIStatusButton().should("not.exist");

    AppEmulator.dataBuilder.workbench.clickGenerateSemantics();
    AppEmulator.dataBuilder.workbench.aicDialog.verifyOptions(3);
    AppEmulator.interceptPostRequest("**/genai-modeling/semantic-enrichment/SOURCE_SPACE", "startAI", undefined, {
      requestId: "some-guid",
    });
    AppEmulator.mockServerCall(
      "GET",
      "**/genai-modeling/semantic-enrichment/SOURCE_SPACE/progress/some-guid",
      {
        messageCode: "GENAI_MODELING_SEMANTIC_ENRICHMENT_IN_PROGRESS",
        parameters: [3],
        text: "Enriching: 3 columns remaining",
      },
      "AIProgress1"
    );
    AppEmulator.dataBuilder.workbench.aicDialog.generate();
    cy.wait("@startAI");
    cy.wait("@AIProgress1");

    AppEmulator.mockServerCall(
      "GET",
      "**/genai-modeling/semantic-enrichment/SOURCE_SPACE/progress/some-guid",
      finialResponse,
      "AICompletion"
    );
    cy.wait("@AICompletion");

    // Check undo button
    AppEmulator.dataBuilder.workbench.getUndoButton().should("be.enabled");

    // Open the measures dialog
    AppEmulator.dataBuilder.workbench.gv.ppty.editMeasures();
    // Check aggregation and review button of 1st row
    AppEmulator.dataBuilder.workbench.gv.ppty.emDialog.checkSelectValueInAttributesTableCell(0, 3, "SUM");
    AppEmulator.dataBuilder.workbench.gv.ppty.emDialog.clickReviewButton(0, 6);
    AppEmulator.dataBuilder.workbench.airPopover
      .getPopover()
      .should("exist")
      .and(
        "contain.text",
        "@reviewTitlereasoning:reason to measurepreviousValuessemanticType:@none@txtMeasureUnitColumn:@nonecolumnType:@attribute"
      );
    // Revert the changes
    AppEmulator.dataBuilder.workbench.airPopover.revert();
    AppEmulator.dataBuilder.workbench.gv.ppty.emDialog.checkNoDataMessage();
    // Close the measures dialog
    AppEmulator.dataBuilder.workbench.gv.ppty.emDialog.close();

    // Undo the revert
    AppEmulator.dataBuilder.workbench.getUndoButton().click();
    AppEmulator.dataBuilder.workbench.waitUntilDOMStable();
    // Undo all AI changes
    AppEmulator.dataBuilder.workbench.getUndoButton().click();
    // Check undo button
    AppEmulator.dataBuilder.workbench.getUndoButton().should("be.disabled");
    AppEmulator.dataBuilder.workbench.waitUntilDOMStable();

    // AI request again
    AppEmulator.dataBuilder.workbench.clickGenerateSemantics();
    AppEmulator.dataBuilder.workbench.aicDialog.verifyOptions(3);
    AppEmulator.interceptPostRequest("**/genai-modeling/semantic-enrichment/SOURCE_SPACE", "startAI", undefined, {
      requestId: "some-new-guid",
    });
    AppEmulator.dataBuilder.workbench.aicDialog.generate();
    cy.wait("@startAI");
    AppEmulator.mockServerCall(
      "GET",
      "**/genai-modeling/semantic-enrichment/SOURCE_SPACE/progress/some-new-guid",
      finialResponse,
      "AICompletion"
    );
    cy.wait("@AICompletion");
    // Check undo button
    AppEmulator.dataBuilder.workbench.getUndoButton().should("be.enabled");
  });
});
/* jscpd:ignore-end */
