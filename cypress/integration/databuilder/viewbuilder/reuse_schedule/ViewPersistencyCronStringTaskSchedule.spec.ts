/** @format */

import moment from "moment";
import { get<PERSON><PERSON><PERSON><PERSON>, IAPI } from "../../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../../pageobjects/api/impl/Helpers";
import { DataBuilder } from "../../../../pageobjects/databuilder";
import { DB_BUTTON } from "../../../../pageobjects/databuilder/descriptor";
import { ShellPages } from "../../../../pageobjects/shell";

describe.skip("cypress/integration/databuilder/viewbuilder/ViewPersistency", () => {
  let api: IAPI;
  let viewModeler;
  const scheduleList = [
    {
      scheduleId: "9f7cb5c6-2327-4d6e-a4e0-c7cfcd41603c",
      spaceId: "HD_SPACE_02",
      applicationId: "VIEWS",
      objectId: "View_Persisted_0",
      activity: "PERSIST",
      externalScheduleId: "08950b25-434f-4e85-bd08-2864767bdc1f",
      description: "View_Persisted_0",
      validFrom: "2022-12-08T00:00:00.000Z",
      validTo: "2022-12-08T23:59:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "D037121",
      createdAt: "2022-12-08T10:59:33.302Z",
      changedBy: "D037121",
      changedAt: "2022-12-08T10:59:33.646Z",
      owner: "D037121",
      uiVariant: "FORM",
      frequency: {
        interval: 1,
        type: "DAILY",
        startDate: "2022-12-08T12:00:00.000Z",
      },
    },
  ];
  beforeEach(async () => {
    api = getAPIInstance();
    await api.setupBeforeEach(/* useMock*/ true, /* defaultFixture*/ true, /* routes*/ XHelpers.DEFAULT_MOCK_REPONSES);
    api.enableUIFeatureflag("DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION");
    api.enableUIFeatureflag("DWCO_REUSABLE_TASK_SCHEDULING_UI");
    await api.mockServerCall("POST", "**/tf/directexecute", {}, "stopPersistency");
    await api.mockServerCall("POST", "**/tf/directexecute", {}, "start");
    await api.mockServerCall(
      "GET",
      "**/monitor/*/persistedViews/View_Persisted_0**",
      "fixture:databuilder/viewbuilder/persistency/viewPersistencyInfo",
      "getViewPersistencyDetails"
    );
    await api.mockServerCall(
      "GET",
      "**/repository/spaces**",
      "fixture:databuilder/viewbuilder/checkdependency/spaces",
      "getSpaces"
    );
    await api.mockServerCall(
      "GET",
      "**/repository/designObjects**",
      "fixture:databuilder/viewbuilder/persistency/deployedView",
      "designObjects"
    );
    const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
    await api.openSpace("VIEWSPACE");
    await databuilder.landingPage.openModel("View_Persisted_0");
    viewModeler = await databuilder.landingPage.openGraphicalView();
  });
  describe("{ViewBuilder} View Persistency Action", () => {
    it("Check for create schedule", async () => {
      await api.mockServerCall(
        "POST",
        "**/schedules",
        { scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" },
        "createSchedule"
      );
      await api.mockServerCall(
        "GET",
        "**/schedules/0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
        scheduleList[0],
        "getSchedule"
      );
      await api.wait(2);
      await api.clickMenuButtonItem(DB_BUTTON.SCHEDULE_PERSISTENCY_MENU_BUT, 0);
      await api.assertControlExists({
        id: "TaskSchedulerDialog",
      });
      await api.pressButton("viewEditorTaskScheduler--taskScheduleDialog--view--createButton");
      await api.assertMessageToast({
        text: "createScheduleSuccess",
      });
    });

    it("Check for Dialog components", async () => {
      await api.mockServerCall(
        "POST",
        "**/schedules",
        { scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" },
        "createSchedule"
      );
      await api.mockServerCall("GET", "**/schedules?**", scheduleList, "getSchedules");
      await api.mockServerCall(
        "GET",
        "**/schedules/0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
        scheduleList[0],
        "getSchedule"
      );
      await api.clickMenuButtonItem(DB_BUTTON.SCHEDULE_PERSISTENCY_MENU_BUT, 0);
      await api.assertControlExists({
        id: "TaskSchedulerDialog",
      });
      await api.assertSelect({ id: `frequencyType`, items: 2, selectedItemKey: "FORM" });
      await api.assertSelect({ id: `futureRunZone`, items: 2, selectedItemKey: "utc" });
      await api.assertContainsText({
        id: `utcLabelInfo`,
        value: "utcInfoLabelText",
      });
    });

    it("Check for Simple Schedule flow", async () => {
      await api.mockServerCall(
        "POST",
        "**/schedules",
        { scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" },
        "createSchedule"
      );
      await api.mockServerCall("GET", "**/schedules?**", scheduleList, "getSchedules");
      await api.mockServerCall(
        "GET",
        "**/schedules/0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
        scheduleList[0],
        "getSchedule"
      );

      await api.clickMenuButtonItem(DB_BUTTON.SCHEDULE_PERSISTENCY_MENU_BUT, 1);
      await api.assertControlExists({
        id: "TaskSchedulerDialog",
      });
      // await cy.get("#" + `endDate`).clear();
      // await cy
      //   .get("#" + `endDate`)
      //   .should("exist")
      //   .focus()
      //   .blur();
      await api.clearInput(`endDate`);
      await api.typeText(`endDate`, "Jul 30, 2023");
      await api.assertInput({
        input: `endDate`,
        text: "Jul 30, 2023",
      });
      await api.clearInput(`endDate`);

      await api.assertControlNotExists({
        id: `expiredScheduleText`,
      });

      // Check for Hourly Schedule with offset
      // await api.selectSelectItem(`frequencyType`, 0);
      await api.selectSelectItem(`recurrenceType`, 0);
      await api.selectSelectItem(`hourRec`, 4);

      // await cy.get("#" + `scheduleTime`).type("03:00");
      // await cy.get("#" + `scheduleTime`).focused().blur();

      await api.typeInputText(`scheduleTime`, "03:00");

      const runHours = [3, 8, 13, 18, 23];
      const now = moment.utc();
      let nextHour = 3;
      for (const val of runHours) {
        if (now.hour() < val) {
          nextHour = val;
          break;
        }
      }
      let dayOffset;
      let newDate = moment.utc();
      newDate.hour(nextHour);
      newDate.minute(0);
      newDate.second(0);
      // const nextRun = [
      //   "2025-02-10T13:00:00.000Z",
      //   "2025-02-10T18:00:00.000Z",
      //   "2025-02-10T23:00:00.000Z",
      //   "2025-02-11T03:00:00.000Z",
      //   "2025-02-11T08:00:00.000Z",
      // ];
      // await api.mockServerCall("GET", "**/nextruns?from**", nextRun, "getScheduleNextRun");
      // let newDate = moment("2025-02-10T13:00:00.000Z").utc();
      const hourlyRec = newDate.format("MMM D, YYYY H:mm:ss");
      await api.assertText({
        id: `nextRun1`,
        value: hourlyRec,
      });

      // Check for Daily schedule
      await api.selectSelectItem(`recurrenceType`, 1);
      await api.setCheckBox(`resetFrequencyCheck`, true);
      await api.selectSelectItem(`dailyRec`, 7);
      // await cy.get("#" + `scheduleTime`).type("00:00");
      // await cy.get("#" + `scheduleTime`).focused().blur();
      await api.typeInputText(`scheduleTime`, "00:00");
      newDate = moment.utc();
      const daysRunList = [1, 9, 17, 25];
      dayOffset = 1;
      for (const val of daysRunList) {
        if (newDate.date() < val) {
          dayOffset = val;
          break;
        }
      }
      const monOffset = newDate.date() < 25 ? 0 : 1;
      newDate.add(monOffset, "month");
      newDate.date(dayOffset);
      newDate.hour(0);
      newDate.minute(0);
      newDate.second(0);
      const dailyRec = newDate.format("MMM D, YYYY H:mm:ss");
      await api.assertText({
        id: `nextRun1`,
        value: dailyRec,
      });

      // Check for Weekly schedule
      await api.selectSelectItem(`recurrenceType`, 2);
      newDate = moment.utc();
      if (newDate.day() !== 1) {
        newDate.day() < 1 ? newDate.day(1) : newDate.day(8);
      } else {
        newDate.day(8);
      }
      newDate.hour(0);
      newDate.minute(0);
      newDate.second(0);
      const weeklyRec = newDate.format("MMM D, YYYY H:mm:ss");
      await api.assertText({
        id: `nextRun1`,
        value: weeklyRec,
      });

      // Check for Monthly Schedule
      await api.selectSelectItem(`recurrenceType`, 3);
      await api.selectSelectItem(`monthlyRec`, 1);
      await api.selectSelectItem(`dailyMonRec`, 3);
      newDate = moment.utc();
      const monthOffset = (newDate.month() + 1) % 2 === 0 ? 1 : newDate.date() < 4 ? 0 : 2;
      newDate.add(monthOffset, "month");
      newDate.date(4);
      newDate.hour(0);
      newDate.minute(0);
      newDate.second(0);
      const monthlyRec = newDate.format("MMM D, YYYY H:mm:ss");
      await api.assertText({
        id: `nextRun1`,
        value: monthlyRec,
      });
    });

    it("Check for cron string scenarios", async () => {
      await api.mockServerCall(
        "POST",
        "**/schedules",
        { scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" },
        "createSchedule"
      );
      await api.mockServerCall("GET", "**/schedules?**", scheduleList, "getSchedules");
      await api.mockServerCall(
        "GET",
        "**/schedules/0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
        scheduleList[0],
        "getSchedule"
      );
      await api.clickMenuButtonItem(DB_BUTTON.SCHEDULE_PERSISTENCY_MENU_BUT, 1);
      await api.assertControlExists({
        id: "TaskSchedulerDialog",
      });
      await api.clearInput(`endDate`);
      await api.typeText(`endDate`, "Jul 30, 2023");
      await api.assertInput({
        input: `endDate`,
        text: "Jul 30, 2023",
      });
      // TODO: Do not use typeText, since it is deprecated
      // await api.typeText(`endDate`, "");
      // await cy.get(`#endDate`).clear().focused().blur();
      await api.clearInput(`endDate`);
      await api.assertControlNotExists({
        id: `expiredScheduleText`,
      });

      // Check for Minute scenario
      await api.selectSelectItem(`frequencyType`, 1);
      // TODO: Do not use typeText, since it is deprecated
      // await api.typeText(`cronMin`, "30");
      // await api.typeText(`cronHour`, "*/1");
      // await api.typeText(`cronDay`, "*");

      // await cy.get(`#cronMin-inner`).clear().type("30").focused().blur();
      // await cy.get(`#cronHour-inner`).clear().type("*/1").focused().blur();
      // await cy.get(`#cronDay-inner`).clear().type("*").focused().blur();

      await api.typeInputText(`cronMin`, "30");
      await api.typeInputText(`cronHour`, "*/1");
      await api.typeInputText(`cronDay`, "*");

      let newStart = moment.utc();
      if (newStart.minute() > 30) {
        newStart.add(1, "hour");
        newStart.minute(30);
      } else {
        newStart.minute(30);
      }
      newStart.second(0);
      const minRec = newStart.format("MMM D, YYYY H:mm:ss");
      await api.assertText({
        id: `nextRun1`,
        value: minRec,
      });

      // Check for hourly
      // TODO: Do not use typeText, since it is deprecated
      // await api.typeText(`cronMin`, "0");
      // await api.typeText(`cronHour`, "3-23/3");
      // await cy.get(`#cronMin-inner`).clear().type("0").focused().blur();
      // await cy.get(`#cronHour-inner`).clear().type("3-23/3").focused().blur();

      await api.typeInputText(`cronMin`, "0");
      await api.typeInputText(`cronHour`, "3-23/3");

      newStart = moment.utc();
      let hour = 3;
      const hourRunList = [3, 6, 9, 12, 15, 18, 21];
      for (const val of hourRunList) {
        if (newStart.hour() < val) {
          hour = val;
          break;
        }
      }
      const dayOffset = newStart.hour() > 20 ? 1 : 0;
      newStart.add(dayOffset, "day");
      newStart.hour(hour);
      newStart.minute(0);
      newStart.second(0);
      const hourRec = newStart.format("MMM D, YYYY H:mm:ss");
      await api.assertText({
        id: `nextRun1`,
        value: hourRec,
      });

      // Check for Daily
      // TODO: Do not use typeText, since it is deprecated
      // await api.typeText(`cronMin`, "15");
      // await api.typeText(`cronHour`, "12");
      // await api.typeText(`cronDay`, "15-20");
      // await cy.get(`#cronMin-inner`).clear().type("15").focused().blur();
      // await cy.get(`#cronHour-inner`).clear().type("12").focused().blur();
      // await cy.get(`#cronDay-inner`).clear().type("15-20").focused().blur();

      await api.typeInputText(`cronMin`, "15");
      await api.typeInputText(`cronHour`, "12");
      await api.typeInputText(`cronDay`, "15-20");

      newStart = moment.utc();
      let date = newStart.date();
      let min = newStart.minute();
      const maxRunByDate = moment.utc().date(20).hour(12).minute(15);
      hour = newStart.hour();
      if (newStart.unix() < maxRunByDate.unix()) {
        const todaysMaxRunTime = moment.utc().hour(12).minute(15).second(0);
        if (newStart.date() < 15) {
          newStart.hour(12).minute(15).second(0);
          if (date < 15) {
            newStart.date(15);
          }
        } else if (newStart.unix() < todaysMaxRunTime.unix()) {
          newStart.hour(12).minute(15).second(0);
          // if (date < 15) {
          //   newStart.date(15);
          // }
        } else if (newStart.date() === 20) {
          newStart.add(1, "month");
          newStart.date(15).hour(12).minute(15).second(0);
        } else {
          newStart.add(1, "day");
          newStart.hour(12).minute(15).second(0);
        }
      } else {
        newStart.add(1, "month");
        newStart.date(15).hour(12).minute(15).second(0);
      }
      let dayRec = newStart.format("MMM D, YYYY H:mm:ss");
      await api.assertText({
        id: `nextRun1`,
        value: dayRec,
      });

      // Check for Day(Month) & Day(Week) combination
      // TODO: Do not use typeText, since it is deprecated
      // await api.typeText(`cronMin`, "0");
      // await api.typeText(`cronHour`, "0");
      // await api.typeText(`cronDay`, "*/11");
      // await api.typeText(`cronMon`, "*");
      // await api.typeText(`cronWeek`, "1");
      // await cy.get(`#cronMin-inner`).clear().type("0").focused().blur();
      // await cy.get(`#cronHour-inner`).clear().type("0").focused().blur();
      // await cy.get(`#cronDay-inner`).clear().type("*/11").focused().blur();
      // await cy.get(`#cronMon-inner`).clear().type("*").focused().blur();
      // await cy.get(`#cronWeek-inner`).clear().type("1").focused().blur();

      await api.typeInputText(`cronMin`, "0");
      await api.typeInputText(`cronHour`, "0");
      await api.typeInputText(`cronDay`, "*/11");
      await api.typeInputText(`cronMon`, "*");
      await api.typeInputText(`cronWeek`, "1");

      const dateList = [1, 12, 23];
      newStart = moment.utc();
      const date2 = moment.utc();
      const nextDate = date2.day() === 0 ? moment.utc().day(1) : moment.utc().day(8);
      dateList.push(nextDate.date());
      dateList.sort();
      let startDay = 1;
      for (const val of dateList) {
        if (date2.date() < val) {
          startDay = val;
          break;
        }
      }
      newStart.second(0);
      newStart.minute(0);
      newStart.hour(0);
      if (newStart.date() > dateList[dateList.length - 1]) {
        newStart.add(1, "month");
      }
      newStart.date(startDay);
      dayRec = newStart.format("MMM D, YYYY H:mm:ss");
      await api.assertText({
        id: `nextRun1`,
        value: dayRec,
      });
    });
  });
});
