/**
 * eslint-disable @typescript-eslint/require-await
 *
 * @format
 */

/* eslint-disable @typescript-eslint/no-misused-promises */
/** Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved. */
import AppEmulator from "../../common/appEmulator/AppEmulator";
import { FEATURE_FLAGS_SET } from "../../common/serverEmulator/FeatureFlagsConstants";
import ServerEmulator from "../../common/serverEmulator/ServerEmulator";
import { DWC_ROUTES, UITesting } from "../../common/UITesting";

describe("Add association object selection dialog", () => {
  beforeEach(() => {
    UITesting.setup({ debug: true });

    ServerEmulator.setupConfiguration({
      spaces: {
        MYSPACE: {
          objects: [
            {
              document: require("./View_asso_osd.json"),
              type: "view",
              technicalName: "View_asso_osd",
            },
          ],
        },
      },
      featureFlagsSet: FEATURE_FLAGS_SET.LEGACY,
      featureFlags: {
        DWCO_HARMONIZATION_OBJECT_SELECTION: true,
      },
    });

    UITesting.visit({ route: DWC_ROUTES.home });
  });

  it.skip("add association and check the dialog", function () {
    AppEmulator.dataBuilder.landing.visitObject("MYSPACE", "View_asso_osd");
    AppEmulator.dataBuilder.workbench.gv.waitUntilAvailable();
    AppEmulator.dataBuilder.workbench.gv.ppty.waitUntilDOMStable();
    AppEmulator.dataBuilder.workbench.gv.selectAddAssociationMenuItem();
    AppEmulator.dataBuilder.workbench.gv.selectTargetInOSDialog("AFKO_P45", true);
  });
});
