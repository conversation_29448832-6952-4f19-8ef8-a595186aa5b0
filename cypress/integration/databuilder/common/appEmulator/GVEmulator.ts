/** @format */

import { BaseEditorEmulator } from "./BaseEditorEmulator";
import { BaseEmulator } from "./BaseEmulator";
import { BasePropertiesEmulator } from "./BasePropertiesEmulator";
import { ChangeManagementDialogGVEmulator } from "./ChangeManagementDialogGVEmulator";
import { ConversionCodesTableDialogEmulator } from "./ConversionCodesTableDialogEmulator";
import { DiagramEmulator } from "./DiagramEmulator";
import { ERPropertiesEmulator } from "./ERPropertiesEmulator";
import { GVPropertiesEmulator } from "./GVPropertiesEmulator";
import { OutputPropertiesEmulator } from "./OutputPropertiesEmulator";
import { ProjectionColumnDialogEmulator } from "./ProjectionColumnDialogEmulator";
import { ReplaceSourceMappingEmulator } from "./ReplaceSourceMappingDialogEmulator";

export const propertyPanelId =
  "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties";
export const associationSectionId = propertyPanelId + "--EntityPropertiesView--associationsList";
export const associationMappingTableAId =
  propertyPanelId + "--EntityPropertiesView--joinPropertiesDialogView--mappings--listA";
export const associationSectionDeleteButton = propertyPanelId + "--EntityPropertiesView--deleteAssociationButton";

export class GVEmulator extends DiagramEmulator {
  public static diagram = DiagramEmulator;
  public static ppty = this.combineStaticMethods(
    BaseEmulator,
    BasePropertiesEmulator,
    ERPropertiesEmulator,
    OutputPropertiesEmulator,
    GVPropertiesEmulator
  ) as typeof ERPropertiesEmulator & typeof OutputPropertiesEmulator & typeof GVPropertiesEmulator;
  public static ccDialog = ChangeManagementDialogGVEmulator;
  public static cctDialog = ConversionCodesTableDialogEmulator;
  public static pcDialog = ProjectionColumnDialogEmulator;
  public static bee = BaseEditorEmulator;
  public static rsmDialog = ReplaceSourceMappingEmulator;

  public static dropOnViewDiagram() {
    this.getDiagramEditor()
      .should("exist")
      .then(() => {
        const window = (Cypress as any).state("window");
        this.dnd.dropOnGraph(window.sap.ui.getCore().byId(this.getDiagramEditorId()));
        this.waitUntilDOMStable();
      });
  }

  public static dropToReplaceSource(symbolId: string) {
    this.getDiagramEditor()
      .should("exist")
      .then(() => {
        const window = (Cypress as any).state("window");
        this.dnd.dragOnSymbol(
          window.sap.ui.getCore().byId(this.getDiagramEditorId()),
          symbolId,
          "csnQueryBuilderEditor--replaceSelector"
        );
        this.waitUntilDOMStable();
      });
  }

  public static dropOnSymbol(symbolId: string, positionOffset?: { x: number; y: number }) {
    this.diagram.dropOnSymbol(symbolId, "csnQueryBuilderEditor--csnQueryBuilderEditorControl", positionOffset);
  }

  public static dropToCreateUnion(symbolId: string, expectedUnionNodeId: string = "Union/Union 1") {
    this.getDiagramEditor()
      .should("exist")
      .then(() => {
        const window = (Cypress as any).state("window");
        this.dnd.dragOnSymbol(
          window.sap.ui.getCore().byId(this.getDiagramEditorId()),
          symbolId,
          "csnQueryBuilderEditor--unionSelector"
        );
        cy.get(`g[sap-automation="${expectedUnionNodeId}"]`, { cyOriginal: true, timeout: 10000 }) // Adjust the timeout as needed
          .should("be.visible") // Ensure it's visible
          .then(() => {
            this.waitUntilDOMStable();
            window.sap.ui.getCore().getEventBus().publish("SOURCES_LIST", "DRAG_END", {});
          });
      });
  }

  public static dropToCreateJoin(symbolId: string) {
    this.getDiagramEditor()
      .should("exist")
      .then(() => {
        const window = (Cypress as any).state("window");
        this.dnd.dragOnSymbol(
          window.sap.ui.getCore().byId(this.getDiagramEditorId()),
          symbolId,
          "csnQueryBuilderEditor--joinSelector"
        );
        this.waitUntilDOMStable();
      });
  }

  public static isCalculatedElementsBusy(busy: boolean) {
    return cy
      .get(
        "#shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-CalculatedPropertiesWithRenameFeature-busyIndicator"
      )
      .should(busy ? "be.visible" : "not.exist");
  }

  public static isSourceCurrencyBusy(busy: boolean) {
    return cy
      .get(
        "#shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-CalculatedPropertiesWithRenameFeature--sourceCurrency-busyIndicator"
      )
      .should(busy ? "exist" : "not.exist");
  }

  public static assertDeleteButton(options: { exist?: boolean; enabled?: boolean }) {
    if (options.exist) {
      this.getDeleteButton().should("exist");
    } else if (options.exist === false) {
      this.getDeleteButton().should("not.exist");
    }
    if (options.enabled) {
      this.getDeleteButton().should("be.enabled");
    } else if (options.enabled === false) {
      this.getDeleteButton().should("not.be.enabled");
    }
  }

  public static getDiagramEditor() {
    return cy.get(`#${this.getDiagramEditorId()}`);
  }

  public static getDiagramEditorId() {
    return "csnQueryBuilderEditor--csnQueryBuilderEditorControl";
  }

  public static getDeleteButton() {
    return cy.get("#csnQueryBuilderEditor--deleteNodeButton");
  }

  public static clickDeleteButton() {
    cy.get("#csnQueryBuilderEditor--deleteNodeButton").should("be.enabled").click({ force: true });
  }

  public static getUndoButon() {
    return cy.get("#shellMainContent---databuilderComponent---databuilderWorkbench--undo");
  }

  public static waitUntilAvailable() {
    cy.get(`#${propertyPanelId}`).should("exist");
    cy.get("#workbenchBusyIndicator-Dialog").should("not.exist");
  }

  public static getChangeManagementDialog() {
    return cy.get("#sap-cdw-components-csnquerybuilder-view-ChangeManagementInfo--dialog");
  }

  // #region action
  public static clickAssociationInPropertyPanel(index: number) {
    cy.get(`#${associationSectionId}`).scrollIntoView();
    cy.get(`#${associationSectionId} .sapMListUl .token-list-item`).eq(index).click();
  }

  public static clickDeleteAssociationButton() {
    cy.get(`#${associationSectionDeleteButton}`).click();
  }

  public static selectItemInAddAssociationMenu(item: string) {
    cy.get(`#${associationSectionId}`).scrollIntoView();
    const menuId =
      "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--addAssociationMenu";
    this.selectItemInMenu(menuId, item);
  }

  public static selectItemInMenu(menuId: string, item: string) {
    this.waitUntilDOMStable();
    cy.get(`#${menuId}-internalBtn`).should("exist").click({ force: true });
    cy.get(`div[class*="sapUiMnu"] ul[role="menu"] li div:contains("${item}")`, { force: true } as any)
      .should("exist")
      .click({ force: true });
  }

  public static selectAddAssociationMenuItem() {
    const item = "@txtAddAssociation";
    this.selectItemInAddAssociationMenu(item);
  }

  public static AddDAC() {
    cy.get(
      `#shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--addViewDataAccessControlButton`
    ).click();
  }

  public static editAssociation(index: number) {
    cy.get(`#${associationSectionId} .sapMListUl .token-list-item`)
      .eq(index)
      .find(".token-action_buttons button")
      .last()
      .click();
  }
  // #endregion

  public static selectTargetInOSDialog(item: string, secondCell?: boolean) {
    this.bee.selectTargetInOSDialog(item, secondCell);
  }

  // #region assertion
  public static checkAssociationMappingTableAItemsCount(count: number) {
    cy.get(`#${associationMappingTableAId} .sapMListUl .token-list-item`).should("have.length", count);
  }

  // #endregion
}
