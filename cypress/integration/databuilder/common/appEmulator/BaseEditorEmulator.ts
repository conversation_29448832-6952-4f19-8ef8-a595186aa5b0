/** @format */

import { BaseEmulator } from "./BaseEmulator";
import { HierarchyDialogEmulator } from "./HierarchyDialogEmulator";
import { HierarchyWithDirectoryDialogEmulator } from "./HierarchyWithDirectoryDialogEmulator";

export class BaseEditorEmulator extends BaseEmulator {
  public static hierarchyWithDirectory = HierarchyWithDirectoryDialogEmulator;

  public static hierarchyDialog = HierarchyDialogEmulator;

  public static getHierarchyButton() {
    return cy.get("#shellMainContent---databuilderComponent---databuilderWorkbench--hierarchy");
  }

  public static clickHierarchyButton() {
    this.getHierarchyButton().should("be.enabled").click();
  }

  public static selectValueInTableCell(tableId: string, row: int, col: int, value: string) {
    this.waitUntilDOMStable(tableId);
    cy.get(`#${tableId}`)
      .should("exist")
      .find(`#${tableId}-rows-row${row}-col${col} div[role="combobox"]`)
      .should("exist")
      .click({ force: true });
    cy.get(`.sapMPopover[role="dialog"][style*="visibility: visible"]`, { force: true } as any)
      .should("exist")
      .find(`li[role="option"]:contains("${value}")`)
      .should("exist")
      .click({ force: true });
  }

  public static clickCheckboxInTableCell(tableId: string, row: int, col: int) {
    this.waitUntilDOMStable(tableId);
    cy.get(`#${tableId}`)
      .should("exist")
      .find(`#${tableId}-rows-row${row}-col${col} div[role="checkbox"]`)
      .should("exist")
      .click({ force: true });
  }

  public static clickButtonInTableCell(tableId: string, row: int, col: int) {
    this.waitUntilDOMStable(tableId);
    cy.get(`#${tableId}`)
      .should("exist")
      .find(`#${tableId}-rows-row${row}-col${col} button`)
      .should("exist")
      .click({ force: true });
  }

  public static checkValueInSelectInTableCell(tableId: string, row: int, col: int, value: string, isIn: boolean) {
    this.waitUntilDOMStable(tableId);
    cy.wait(1000); // Wait for the select to be rendered
    cy.get(`#${tableId}`)
      .should("exist")
      .find(`#${tableId}-rows-row${row}-col${col} div[role="combobox"]`)
      .should("exist")
      .click({ force: true }); // Open
    cy.get(`.sapMPopover[role="dialog"][style*="visibility: visible"]`, { force: true } as any)
      .should("exist")
      .find(`li[role="option"]>span:nth-child(1):contains("${value}")`)
      .should(isIn ? "exist" : "not.exist"); // Check
    cy.get(`#${tableId}`)
      .should("exist")
      .find(`#${tableId}-rows-row${row}-col${col} div[role="combobox"]`)
      .should("exist")
      .click({ force: true }); // Close
  }

  public static checkAdditionalTextInSelectInTableCell(
    tableId: string,
    row: int,
    col: int,
    value: string,
    isIn: boolean
  ) {
    this.waitUntilDOMStable(tableId);
    cy.get(`#${tableId}`)
      .should("exist")
      .find(`#${tableId}-rows-row${row}-col${col} div[role="combobox"]`)
      .should("exist")
      .click({ force: true }); // Open
    cy.get(`.sapMPopover[role="dialog"][style*="visibility: visible"]`, { force: true } as any)
      .should("exist")
      .find(`li[role="option"]>span:nth-child(2):contains("${value}")`)
      .should(isIn ? "exist" : "not.exist"); // Check
    cy.get(`#${tableId}`)
      .should("exist")
      .find(`#${tableId}-rows-row${row}-col${col} div[role="combobox"]`)
      .should("exist")
      .click({ force: true }); // Close
  }

  public static checkTextInTableHeader(tableId: string, col: int, value: string) {
    tableId = `${tableId}-header`;
    this.waitUntilDOMStable(tableId);
    cy.get(`#${tableId}`).should("exist").find(`td[aria-colindex="${col}"] span`).should("have.text", value);
  }

  public static selectItemInMenu(menuId: string, item: string) {
    this.waitUntilDOMStable();
    cy.get(`#${menuId}-internalBtn`).should("exist").click({ force: true });
    cy.get(`div[class*="sapUiMnu"] ul[role="menu"] li div:contains("${item}")`, { force: true } as any)
      .should("exist")
      .click({ force: true });
  }

  public static selectItemInMenuById(menuId: string, itemId: string) {
    this.waitUntilDOMStable();
    cy.get(`#${menuId}-internalBtn`).should("exist").click({ force: true });
    cy.get(`#${itemId}`).should("exist").click({ force: true });
  }

  public static selectItemInSegmentedButtonByIndex(segmentedButtonId: string, itemIndex: number) {
    this.waitUntilDOMStable();
    cy.get(`#${segmentedButtonId}>li:nth-child(${itemIndex + 1})`)
      .should("exist")
      .click({ force: true });
  }

  public static getItemInMenu(menuId: string, item: string, showMenu: boolean = true) {
    this.waitUntilDOMStable();
    if (showMenu) {
      cy.get(`#${menuId}-internalBtn`).should("exist").click({ force: true });
    }
    return cy.get(`div[class*="sapUiMnu"] ul[role="menu"] li div:contains("${item}")`, { force: true } as any).parent();
  }

  public static selectItemInTable(tableId: string, item: string, secondCell?: boolean) {
    this.waitUntilDOMStable(tableId);
    if (secondCell) {
      cy.get(`#${tableId} td:contains("${item}")`).eq(1).should("exist").click({ force: true });
    } else {
      cy.get(`#${tableId} span:contains("${item}"):first`).should("exist").click({ force: true });
    }
  }

  public static selectTargetInAddAssociationDialog(item: string) {
    const dialogId = "associationDialog";
    this.waitUntilDOMStable(dialogId);
    const tableId = "targetTable";
    this.selectItemInTable(tableId, item, true);
    cy.get(`#ok`).should("be.enabled").click({ force: true });
  }

  public static selectTargetInOSDialog(item: string, secondCell?: boolean) {
    const dialogId = "explorerSelectorDialogExt";
    this.waitUntilDOMStable(dialogId);
    const tableId = "explorerSelectorDialogExt-cnt-scc-ushell-search-result-table-listUl";
    this.selectItemInTable(tableId, item, secondCell);
    cy.get(`#explorerSelectorDialogExt-cnt-select`).should("be.enabled").click({ force: true });
  }

  public static checkSelectValueInTableCell(tableId: string, row: int, col: int, value: string, htmlTag?: string) {
    tableId = `${tableId}`;
    this.waitUntilDOMStable(tableId);
    htmlTag = htmlTag ? htmlTag : "div";
    if (htmlTag === "input") {
      cy.get(`#${tableId}`)
        .should("exist")
        .find(`#${tableId}-rows-row${row}-col${col} ${htmlTag}[role="combobox"]`)
        .should("exist")
        .invoke("val")
        .then((val) => {
          expect(val).to.equal(value);
        });
    } else if (htmlTag === "span") {
      if (value !== "") {
        cy.get(`#${tableId}-rows-row${row}-col${col} ${htmlTag}.sapMSelectListItemText:contains("${value}")`, {
          force: true,
        } as any).should("exist");
      } else {
        // span with empty value
        cy.get(`#${tableId}-rows-row${row}-col${col} ${htmlTag}.sapMSelectListItemText`, {
          force: true,
        } as any)
          .should("exist")
          .and("have.text", "");
      }
    } else {
      cy.get(`#${tableId}-rows-row${row}-col${col} ${htmlTag}[role="combobox"]:contains("${value}")`, {
        force: true,
      } as any).should("exist");
    }
  }
}
