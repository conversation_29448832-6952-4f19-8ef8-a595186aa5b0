/** @format */

import { BaseEmulator } from "./BaseEmulator";

export class AIConfigurationDialogEmulator extends BaseEmulator {
  public static dialogId = "oAIConfigurationDialogId";

  public static getDialog() {
    return cy.get(`#${this.dialogId}`);
  }

  public static verifyOptions(count: number) {
    this.getDialog().should("be.visible").find(`[type="CheckBox"]`).should("have.length", count);
  }

  public static getGenerateButton() {
    return cy.get(`#${this.dialogId}-generate`);
  }

  public static generate() {
    this.getDialog().should("be.visible");
    this.getGenerateButton().should("be.visible").should("be.enabled").click();
    this.getDialog().should("not.exist");
  }
}
