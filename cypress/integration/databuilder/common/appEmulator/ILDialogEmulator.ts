/** @format */

import { BaseEmulator } from "./BaseEmulator";

type DependecyRequestType = "entity_ana" | "columns" | "col_ana";
export class ILDialogEmulator extends BaseEmulator {
  // #region ID getters
  static dialogId = "sap-cdw-components-reuse-control-impactLineage-ImpactAnalysisDialog--dialog--view";
  static columnPopoverId = this.dialogId + "--column-popover";
  static columnSelectButtonId = this.dialogId + "--column-select-btn";
  static columnCloseButtonId = this.dialogId + "--column-close-btn";
  static infobarId = this.dialogId + "--infobar";
  static backButtonId = this.dialogId + "--backButton";
  static ilanalyzerId = this.dialogId + "--ilanalyzer";
  static layoutMenuId = `${this.ilanalyzerId}--layoutOptions`;
  static layoutDropdownId = `${this.layoutMenuId}-valueStateText-flexContentContainer`;
  static impactSwitchId = this.ilanalyzerId + "--impact-switch";
  static resetId = this.ilanalyzerId + "--reset";
  static detailPopoverId = this.ilanalyzerId + "--contextPadPopover-popover";
  // #endregion

  static open() {
    cy.get("#shellMainContent---databuilderComponent---databuilderWorkbench--impact").click();
    cy.window().then((win) => {
      (win.sap.galilei as any).unitTest = true; // not working as expected
      (win.sap.galilei.ui.editor.DiagramEditor as any).ENABLE_SYMBOL_EDITING_DELAY = false;
      const svgNS = (win.sap.galilei.ui.common.svg as any).Svg;
      const originalAddClass = svgNS.addClass;
      svgNS.addClass = function (oElement, sClass, bUseDelay) {
        originalAddClass(oElement, sClass, false);
      };
      const originalRemoveClass = svgNS.removeClass;
      svgNS.removeClass = function (oElement, sClass, bUseDelay) {
        originalRemoveClass(oElement, sClass, false);
      };
    });
  }

  static interceptDependecyRequest(fixture: string | object, alias: string, type: DependecyRequestType = "entity_ana") {
    let urlPattern = "**/dependencies/?ids**csn.valueHelp.entity";
    if (type == "columns") {
      urlPattern = "**/dependencies/?ids**sap.dwc.parent";
    } else if (type == "col_ana") {
      urlPattern = "**/dependencies/?ids**csn.column.select";
    }
    if (typeof fixture === "string") {
      cy.fixture(fixture).then((data) => {
        cy.intercept("GET", urlPattern, data).as(alias);
      });
    } else {
      cy.intercept("GET", urlPattern, fixture).as(alias);
    }
  }

  static interceptSharedRequest(fixture, alias) {
    cy.fixture(fixture).then((data) => {
      cy.intercept("GET", "**/repository/shares?targetSpace**", data).as(alias);
    });
  }

  static waitDiagramStabilized() {
    this.waitUntilDOMStable(this.dialogId);
  }

  static getSymbol(name: string) {
    return cy.get(`[sap-automation="Node/${name}"]`, { cyOriginal: true });
  }

  static getUI5Control(id: string) {
    return (Cypress as any).state("window").sap.ui.getCore().byId(id);
  }

  static getLink(source: string, target?: string) {
    const selectors = [`[source="${source}"]`];
    if (target) {
      selectors.push(`[target="${target}"]`);
    }
    return cy.get(selectors.join(""), { cyOriginal: true });
  }

  static getContextButton(role: string) {
    return cy.get(`.sapGalileiContextButton[role="${role}"]`, { cyOriginal: true });
  }

  static getNodeSymbols() {
    return cy.get(
      `#${this.dialogId} .sapGalileiSymbolNode[role*="entity"], #${this.dialogId} .sapGalileiSymbolNode[role*="container"]`,
      {
        cyOriginal: true,
      }
    );
  }

  static getColumnSymbols() {
    return cy.get(`#${this.dialogId} .sapGalileiSymbolNode[role*="column"]`, {
      cyOriginal: true,
    });
  }

  static getColumnListHeader() {
    return cy.get(`#${this.columnPopoverId} .il-column-list-panel-list-header`);
  }

  static getColumnListMessgaeStrip() {
    return cy.get(`#${this.columnPopoverId} .sapMMsgStripMessage`);
  }

  // #region action
  static clickLineagePort(nodeName: string) {
    this.getSymbol(nodeName).find(".lineage-port").click();
  }

  static clickImpactPort(nodeName: string) {
    this.getSymbol(nodeName).find(".impact-port").click();
  }

  static clickShowNext() {
    cy.get('[data-role="shownext"]', { cyOriginal: true }).click({ force: true });
    cy.get('[data-role="shownext"]', { cyOriginal: true }).should("not.exist");
  }

  static clickSymbol(name: string) {
    this.getSymbol(name).trigger("mousedown");
    this.getSymbol(name).trigger("mouseup");
  }

  static clickColumnIcon(nodeName: string) {
    this.getSymbol(nodeName).find(`[role="column"]`).click();
  }

  static selectColumnInPopover(index: number) {
    cy.get(`#${this.columnPopoverId} .sapMList .sapMLIB .sapMCb`).eq(index).click({ force: true });
  }

  static searchColumnInPopover(query: string) {
    cy.get(`#sap-cdw-components-reuse-control-impactLineage-ImpactAnalysisDialog--dialog--view--search input`).type(
      query,
      { force: true }
    );
  }

  // static toggleSelectAllInColumnPopover() {
  //   this.getColumnListHeader().find(".sapMCb").click({ force: true });
  // }

  static clickSelectInPopover() {
    cy.get(`#${this.columnSelectButtonId}`).click();
  }

  static clickBack() {
    cy.get(`#${this.backButtonId}`).click();
  }

  static switchToDependencyAnalysis() {
    cy.get(`#${this.dialogId}`).find(".sapMSegB > li").last().click();
  }

  static switchToDataAnalysis() {
    cy.get(`#${this.dialogId}`).find(".sapMSegB > li").first().click();
  }

  static closeInfobar() {
    cy.get(`#${this.infobarId} > .sapUiIcon`).click();
  }

  static expandNode(name: string, availableOp?: number) {
    this.getSymbol(name).find(".expand-button").click();
    if (availableOp) {
      cy.get(".sapUiMnu li").should("have.length", availableOp);
    }
    cy.get(".sapUiMnu li").first().click();
  }

  static collapseNode(name: string, availableOp?: number) {
    this.getSymbol(name).find(".expand-button").click();
    if (availableOp) {
      cy.get(".sapUiMnu li").should("have.length", availableOp);
    }
    cy.get(".sapUiMnu li").last().click();
  }

  static swictchLayout(index: number) {
    cy.get(`#${this.layoutMenuId}`).click();
    cy.get(`#${this.layoutDropdownId}`).within(() => {
      cy.get("ul").find("li").eq(index).click({ force: true });
    });
  }
  // #endregion

  // #region assertion
  static checkNodeSymbolCount(count: number) {
    this.getNodeSymbols().should("have.length", count);
  }

  static checkLinkSymbolCount(count: number) {
    cy.get(`#${this.dialogId} .sapGalileiSymbolLink`).should("have.length", count);
  }

  static checkActiveLinkSymbolCount(count: number) {
    cy.get(`#${this.dialogId} .sapGalileiSymbolLink .interaction_link`).should("have.length", count);
  }

  static checkColumnIcon(nodeName: string, exist = true) {
    this.getSymbol(nodeName)
      .find(`[role="column"]`)
      .should(exist ? "exist" : "not.exist");
  }

  static checkColumnIconText(nodeName: string, text: string) {
    this.getSymbol(nodeName).find(`[role="column"]`).should("contain.text", text);
  }

  static checkColumnListRowCount(count: number) {
    cy.get(`#${this.columnPopoverId} .sapMList li.sapMLIB`).should("have.length", count);
  }

  static checkColumnListSelectionCount(count: number) {
    cy.get(`#${this.columnPopoverId} .sapMList li.sapMLIB.sapMLIBSelected`).should("have.length", count);
  }

  static checkColumnIsKey(row: number, isKey = true) {
    cy.get(`#${this.columnPopoverId} .sapMList li.sapMLIB`)
      .eq(row)
      .find(`[aria-label="primary-key"]`)
      .should(isKey ? "exist" : "not.exist");
  }

  // static checkSelectAllStateInColumnPopover(state: "true" | "false" | "mixed") {
  //   this.getColumnListHeader().find(".sapMCb").should("have.attr", "aria-checked", state);
  // }

  static isRefNode(name: string) {
    this.getSymbol(name).should("have.attr", "role").and("contain", "refNode");
  }

  static hasAssociation(link: string, expected = true) {
    this.getLink(link)
      .find(".association")
      .should(expected ? "exist" : "not.exist");
  }

  static hasDAC(link: string, expected = true) {
    this.getLink(link)
      .find(".dac")
      .should(expected ? "exist" : "not.exist");
  }

  static checkSelectedNodeSymbolCount(count: number) {
    cy.get(`#${this.dialogId} .sapGalileiSelected`).should("have.length", count);
  }

  static isSymbolHighlight(name: string, expected = true) {
    this.getSymbol(name).should(expected ? "have.class" : "not.have.class", "highlight");
  }

  static checkHighlightSymbolCount(count: number) {
    cy.get(`#${this.dialogId} .highlight`).should("have.length", count);
  }

  static checkGrayoutSymbolCount(count: number) {
    cy.get(`#${this.dialogId} .grayOut`).should("have.length", count);
  }

  static checkStatusInSymbol(nodeName: string, statusName: string, exist = true) {
    this.getSymbol(nodeName)
      .find(`[role="${statusName}"]`)
      .should(exist ? "exist" : "not.exist");
  }

  static checkTopRightIconInSymbol(nodeName: string, iconName: string, exist = true) {
    this.getSymbol(nodeName)
      .find(`.il_top_right_icon[role="${iconName}"]`)
      .should(exist ? "exist" : "not.exist");
  }
  // #endregion
}
