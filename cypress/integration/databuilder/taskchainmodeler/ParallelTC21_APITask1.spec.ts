/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { AB_VIEW } from "../../../pageobjects/abstractbuilder/descriptor";
import { IAPI, getAPIInstance } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { selectItem } from "../../../pageobjects/dataIntegration/DataIntegrationHelper";
import { DataBuilder } from "../../../pageobjects/databuilder";
import { DB_BUTTON, DB_INPUT, DB_VIEW } from "../../../pageobjects/databuilder/descriptor";
import { ShellPages } from "../../../pageobjects/shell";
/* jscpd:ignore-start */
async function initialAction(api) {
  const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
  await api.openSpace("SPACE1234");
  const TCModeler = await databuilder.landingPage.createNewTaskChainModel();
  await api.waitHTMLStabilized();

  await api.waitHTMLStabilized();
  return TCModeler;
}

// ASYNC with invalid inputs
async function test_scenario_1(api: IAPI) {
  const TCModeler = await initialAction(api);

  await TCModeler.browser.assertControlExists({
    id: "taskChainModeler--taskChainModelerEditorControl--diagram-intHeader",
  });
  await TCModeler.browser.clickButton("taskChainModeler--taskChainModelerEditorControl--RESTfulTask");
  await TCModeler.diagram.dropOnSymbol({ id: "Start/txtBegin" });

  // properties panel
  await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_TECHNICAL_NAME, " ");
  await api.assertInput({
    input: DB_INPUT.TC_API_NODE_TECHNICAL_NAME,
    valueState: "Error",
  });

  await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_CONNECTION);
  await selectItem(DB_INPUT.TC_API_NODE_CONNECTION, 12);
  await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_CONNECTION);
  await api.assertInput({
    input: DB_INPUT.TC_API_NODE_CONNECTION,
    valueState: "Warning",
  });
  await api.waitHTMLStabilized();

  await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_INVOKE_APIPATH, "{");
  await api.assertInput({
    input: DB_INPUT.TC_API_NODE_INVOKE_APIPATH,
    valueState: "Error",
  });

  await api.setRadioButton(DB_INPUT.TC_API_NODE_INVOKE_MODE, 1);
  await api.typeText(DB_INPUT.TC_API_NODE_INVOKE_REQUESTBODY, "{{}id: 10{}}");
  await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_INVOKE_FORMATBTN);
  await TCModeler.properties.assertControlExists({ id: DB_INPUT.TC_API_NODE_INVOKE_REQUESTBODY_ERROR });
  await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_INVOKE_REQUESTBODY_ERROR);
  await api.wait(1);

  await TCModeler.browser.clickButton(DB_INPUT.TC_API_NODE_INVOKE_CSRFTOKEN_SWITCH);
  await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_INVOKE_CSRFTOKEN_INPUT, "{");
  await api.assertInput({
    input: DB_INPUT.TC_API_NODE_INVOKE_CSRFTOKEN_INPUT,
    valueState: "Error",
  });
  await TCModeler.browser.clickButton(DB_INPUT.TC_API_NODE_INVOKE_CSRFTOKEN_SWITCH);

  await api.setRadioButton(DB_INPUT.TC_API_NODE_INVOKE_RESPONSETYPE, 1);
  await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_INVOKE_JOBPATH, "[]");
  await api.assertInput({
    input: DB_INPUT.TC_API_NODE_INVOKE_JOBPATH,
    valueState: "Error",
  });
  await api.setRadioButton(DB_INPUT.TC_API_NODE_INVOKE_RESPONSETYPE, 0);

  await api.assertInput({
    input: DB_INPUT.TC_API_NODE_STATUS_URL,
    text: "",
    enabled: false,
  });
  await api.assertInput({
    input: DB_INPUT.TC_API_NODE_STATUS_APIPATH,
    text: "",
    enabled: false,
  });
  await api.assertInput({
    input: DB_INPUT.TC_API_NODE_STATUS_REQUESTBODY,
    text: "",
  });

  await api.setRadioButton(DB_INPUT.TC_API_NODE_STATUS_RESPONSETYPE, 1);
  await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_STATUS_SUCCESSVARIABLE, ".status[]");
  await api.assertInput({
    input: DB_INPUT.TC_API_NODE_STATUS_SUCCESSVARIABLE,
    valueState: "Error",
  });

  await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_STATUS_ERRORVALUE, "$FAILED");
  await api.assertInput({
    input: DB_INPUT.TC_API_NODE_STATUS_ERRORVALUE,
    valueState: "Error",
  });

  await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_STATUS_ERROR_REASON, ".status[]");
  await api.assertInput({
    input: DB_INPUT.TC_API_NODE_STATUS_ERROR_REASON,
    valueState: "Error",
  });
  await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_TESTRUN_BTN);
  await TCModeler.assertValidations({
    containerId: DB_VIEW.VAL_ERR_DIALOG as any,
    errorCount: 2,
  });
  await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_TESTRUN_REFRESH_BTN);
  await TCModeler.browser.assertControlNotExists({ id: DB_INPUT.TC_API_NODE_TESTRUN_MONITOR });
}

describe("cypress/integration/databuilder/taskchainmodeler/ParallelTC21_APITask", () => {
  let api: IAPI;

  describe(`${Cypress.spec.relative} with SDP`, () => {
    beforeEach(async () => {
      api = getAPIInstance();
      const routes = [...XHelpers.DEFAULT_MOCK_REPONSES, ...XHelpers.SDP_SPECIFIC_MOCK_RESPONSES];
      await api.setupBeforeEach(
        /* useMock*/ true,
        /* defaultFixture*/ true,
        /* routes*/ routes,
        /* overrideRouteCallback*/ undefined,
        /* enabledFeatureFlags*/ [
          "DWC_DUMMY_SPACE_PERMISSIONS",
          "INFRA_DWC_TWO_TENANT_MODE",
          "DWCO_TASK_CHAINS_REMOVE_DATA",
          "DWCO_INFRA_TASKS_API_TASK",
        ]
      );

      // mock the search-metadata calls (entitySets: SEARCH_DESIGN, SEARCH_FOLDER)
      const repoSpaceSearchPathPrefix = "**/repository/SPACE1234/search/";
      await api.mockServerCall(
        "GET",
        `${repoSpaceSearchPathPrefix}$metadata`,
        "fixture:repositoryexplorer/folder/searchMetadata.xml",
        "getSearchMetadata"
      );
      await api.mockServerCall(
        "GET",
        "**/chainabletasks**",
        "fixture:databuilder/taskchainmodeler/chainabletasks",
        "chainabletasks"
      );
      // mock the search calls
      // - SEARCH_DESIGN
      await api.mockServerCall(
        "GET",
        `${repoSpaceSearchPathPrefix}$all**SEARCH_DESIGN**`,
        "fixture:databuilder/taskchainmodeler/search_design.json",
        "getSearch"
      );
      await api.mockServerCall(
        "GET",
        "**/getchildren**",
        {
          partial: false,
          items: [
            {
              id: "remotes",
              name: "%%remotesources%%",
              hasChildren: true,
              type: "remotes",
            },
            {
              id: "SPACE1234#D067576",
              name: "SPACE1234#D067576",
              hasChildren: true,
              type: "schema",
            },
          ],
        },
        "getChildren"
      );
      // - SEARCH_FOLDER
      await api.mockServerCall(
        "GET",
        `${repoSpaceSearchPathPrefix}$all**SEARCH_FOLDER**`,
        "fixture:repositoryexplorer/folder/search_folder.json",
        "getSearchFolder"
      );

      await api.mockServerCall(
        "GET",
        "**/shares?targetSpace=**",
        "fixture:databuilder/taskchainmodeler/shares",
        "getShares"
      );
      await api.mockServerCall("GET", "**/http", "fixture:databuilder/taskchainmodeler/http", "http");
      await api.mockServerCall(
        "GET",
        "**/getmailinglist",
        "fixture:databuilder/taskchainmodeler/MailingList",
        "getMailingList"
      );
      await api.mockServerCall(
        "GET",
        "**/designObjects?folderIds**",
        "fixture:databuilder/taskchainmodeler/designObjects",
        "designObjects"
      );
      await api.mockServerCall(
        "GET",
        "**/dependencies/?ids**",
        "fixture:databuilder/taskchainmodeler/dependencies",
        "dependenciesView"
      );
      await api.mockServerCall(
        "GET",
        "**/repository/designObjects**",
        "fixture:databuilder/taskchainmodeler/designObjects",
        "designObjects"
      );

      await api.mockServerCall(
        "GET",
        "**/designObjects?ids=BCBC7D71307B9BD91700C08EBA5B0444**",
        "fixture:databuilder/taskchainmodeler/TC_BW",
        "designObject_TC_BW"
      );
      await api.mockServerCall("GET", "**/chainable?applicationId**", { objectCurrentlyChainable: true }, "chainable");
      await api.mockServerCall("POST", "**/directexecute", {}, "directExecute");
    });

    describe("{taskchain} ParallelTC21_APITask spec with SDP", () => {
      // does not work with folders
      it.skip("Check for invalid inputs in API Task", async function () {
        const TCModeler = await initialAction(api);

        await TCModeler.browser.assertControlExists({
          id: "taskChainModeler--taskChainModelerEditorControl--diagram-intHeader",
        });
        await TCModeler.browser.clickButton("taskChainModeler--taskChainModelerEditorControl--RESTfulTask");
        await TCModeler.diagram.dropOnSymbol({ id: "Start/txtBegin" });

        // properties panel
        await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_TECHNICAL_NAME, " ");
        await api.assertInput({
          input: DB_INPUT.TC_API_NODE_TECHNICAL_NAME,
          valueState: "Error",
        });

        await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_CONNECTION);
        await selectItem(DB_INPUT.TC_API_NODE_CONNECTION, 12);
        await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_CONNECTION);
        await api.assertInput({
          input: DB_INPUT.TC_API_NODE_CONNECTION,
          valueState: "Warning",
        });
        await api.waitHTMLStabilized();

        await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_INVOKE_APIPATH, "{");
        await api.assertInput({
          input: DB_INPUT.TC_API_NODE_INVOKE_APIPATH,
          valueState: "Error",
        });

        await api.setRadioButton(DB_INPUT.TC_API_NODE_INVOKE_MODE, 1);
        await api.typeText(DB_INPUT.TC_API_NODE_INVOKE_REQUESTBODY, "{{}id: 10{}}");
        await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_INVOKE_FORMATBTN);
        await TCModeler.properties.assertControlExists({ id: DB_INPUT.TC_API_NODE_INVOKE_REQUESTBODY_ERROR });
        await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_INVOKE_REQUESTBODY_ERROR);
        await api.wait(1);

        await TCModeler.browser.clickButton(DB_INPUT.TC_API_NODE_INVOKE_CSRFTOKEN_SWITCH);
        await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_INVOKE_CSRFTOKEN_INPUT, "{");
        await api.assertInput({
          input: DB_INPUT.TC_API_NODE_INVOKE_CSRFTOKEN_INPUT,
          valueState: "Error",
        });
        await TCModeler.browser.clickButton(DB_INPUT.TC_API_NODE_INVOKE_CSRFTOKEN_SWITCH);

        await api.setRadioButton(DB_INPUT.TC_API_NODE_INVOKE_RESPONSETYPE, 1);
        await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_INVOKE_JOBPATH, "[]");
        await api.assertInput({
          input: DB_INPUT.TC_API_NODE_INVOKE_JOBPATH,
          valueState: "Error",
        });
        await api.setRadioButton(DB_INPUT.TC_API_NODE_INVOKE_RESPONSETYPE, 0);

        await api.assertInput({
          input: DB_INPUT.TC_API_NODE_STATUS_URL,
          text: "",
          enabled: false,
        });
        await api.assertInput({
          input: DB_INPUT.TC_API_NODE_STATUS_APIPATH,
          text: "",
          enabled: false,
        });
        await api.assertInput({
          input: DB_INPUT.TC_API_NODE_STATUS_REQUESTBODY,
          text: "",
        });

        await api.setRadioButton(DB_INPUT.TC_API_NODE_STATUS_RESPONSETYPE, 1);
        await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_STATUS_SUCCESSVARIABLE, ".status[]");
        await api.assertInput({
          input: DB_INPUT.TC_API_NODE_STATUS_SUCCESSVARIABLE,
          valueState: "Error",
        });

        await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_STATUS_ERRORVALUE, "$FAILED");
        await api.assertInput({
          input: DB_INPUT.TC_API_NODE_STATUS_ERRORVALUE,
          valueState: "Error",
        });

        await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_STATUS_ERROR_REASON, ".status[]");
        await api.assertInput({
          input: DB_INPUT.TC_API_NODE_STATUS_ERROR_REASON,
          valueState: "Error",
        });
        await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_TESTRUN_BTN);
        await TCModeler.assertValidations({
          containerId: DB_VIEW.VAL_ERR_DIALOG as any,
          errorCount: 2,
        });
        await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_TESTRUN_REFRESH_BTN);
        await TCModeler.browser.assertControlNotExists({ id: DB_INPUT.TC_API_NODE_TESTRUN_MONITOR });
      });
      it.skip("In API task connection throws an error", async function () {
        const errorResp = {
          code: "generalConnectionError",
          details: {
            stack: "See correlation id b0e660e7-d207-421e-43b1-21d3eabce8f9",
            message: "Failed to establish connection",
            code: 2,
            status: 404,
          },
        };
        await api.mockServerCall("GET", "**/http", errorResp, "http", 404);

        const TCModeler = await initialAction(api);
        await TCModeler.browser.assertControlExists({
          id: "taskChainModeler--taskChainModelerEditorControl--diagram-intHeader",
        });
        await TCModeler.browser.clickButton("taskChainModeler--taskChainModelerEditorControl--RESTfulTask");
        await TCModeler.diagram.dropOnSymbol({ id: "Start/txtBegin" });

        await TCModeler.pressButton(DB_BUTTON.TC_NODE_VALIDATIONS);

        await TCModeler.assertValidations({
          containerId: AB_VIEW.VALIDATIONS as any,
          errCount: 2,
          containsMessage: "VAL_REST_API_CONNECTION",
        });

        // properties panel
        await TCModeler.properties.assertControlExists({ id: DB_INPUT.TC_API_NODE_CONNECTION_ERRORBTN });
        await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_CONNECTION_ERRORBTN);
      });
      it("Feature flag is disabled", async function () {
        await api.disableUIFeatureflag("DWCO_INFRA_TASKS_API_TASK");
        const TCModeler = await initialAction(api);

        await TCModeler.browser.assertControlExists({
          id: "taskChainModeler--taskChainModelerEditorControl--diagram-intHeader",
        });
        await TCModeler.browser.assertControlNotExists({
          id: "taskChainModeler--taskChainModelerEditorControl--RESTfulTask",
        });
      });
    });
  });
});
/* jscpd:ignore-end */
