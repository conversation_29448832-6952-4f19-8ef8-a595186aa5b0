/**
 * Copyright 2020 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { IAPI, getAPIInstance } from "../../../pageobjects/api/IAPI";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { selectItem } from "../../../pageobjects/dataIntegration/DataIntegrationHelper";
import { DataBuilder } from "../../../pageobjects/databuilder";
import { DB_INPUT } from "../../../pageobjects/databuilder/descriptor";
import { ShellPages } from "../../../pageobjects/shell";
/* jscpd:ignore-start */
async function initialAction(api) {
  const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
  await api.openSpace("SPACE1234");
  const TCModeler = await databuilder.landingPage.createNewTaskChainModel();
  await api.waitHTMLStabilized();

  await api.waitHTMLStabilized();

  await TCModeler.browser.assertControlExists({
    id: "taskChainModeler--taskChainModelerEditorControl--diagram-intHeader",
  });
  await TCModeler.browser.clickButton("taskChainModeler--taskChainModelerEditorControl--RESTfulTask");
  await TCModeler.diagram.dropOnSymbol({ id: "Start/txtBegin" });
  return TCModeler;
}

describe("cypress/integration/databuilder/taskchainmodeler/ParallelTC21_APITask", () => {
  let api: IAPI;
  describe(`${Cypress.spec.relative} with SDP`, () => {
    beforeEach(async () => {
      api = getAPIInstance();
      const routes = [...XHelpers.DEFAULT_MOCK_REPONSES, ...XHelpers.SDP_SPECIFIC_MOCK_RESPONSES];
      await api.setupBeforeEach(
        /* useMock*/ true,
        /* defaultFixture*/ true,
        /* routes*/ routes,
        /* overrideRouteCallback*/ undefined,
        /* enabledFeatureFlags*/ [
          "DWC_DUMMY_SPACE_PERMISSIONS",
          "INFRA_DWC_TWO_TENANT_MODE",
          "DWCO_TASK_CHAINS_REMOVE_DATA",
          "DWCO_INFRA_TASKS_API_TASK",
        ]
      );

      // mock the search-metadata calls (entitySets: SEARCH_DESIGN, SEARCH_FOLDER)
      const repoSpaceSearchPathPrefix = "**/repository/SPACE1234/search/";
      await api.mockServerCall(
        "GET",
        `${repoSpaceSearchPathPrefix}$metadata`,
        "fixture:repositoryexplorer/folder/searchMetadata.xml",
        "getSearchMetadata"
      );
      await api.mockServerCall(
        "GET",
        "**/chainabletasks**",
        "fixture:databuilder/taskchainmodeler/chainabletasks",
        "chainabletasks"
      );
      // mock the search calls
      // - SEARCH_DESIGN
      await api.mockServerCall(
        "GET",
        `${repoSpaceSearchPathPrefix}$all**SEARCH_DESIGN**`,
        "fixture:databuilder/taskchainmodeler/search_design.json",
        "getSearch"
      );
      await api.mockServerCall(
        "GET",
        "**/getchildren**",
        {
          partial: false,
          items: [
            {
              id: "remotes",
              name: "%%remotesources%%",
              hasChildren: true,
              type: "remotes",
            },
            {
              id: "SPACE1234#D067576",
              name: "SPACE1234#D067576",
              hasChildren: true,
              type: "schema",
            },
          ],
        },
        "getChildren"
      );
      // - SEARCH_FOLDER
      await api.mockServerCall(
        "GET",
        `${repoSpaceSearchPathPrefix}$all**SEARCH_FOLDER**`,
        "fixture:repositoryexplorer/folder/search_folder.json",
        "getSearchFolder"
      );

      await api.mockServerCall("GET", "**/shares?targetSpace=**", { demo: [] }, "getShares");
      await api.mockServerCall("GET", "**/http", "fixture:databuilder/taskchainmodeler/http", "http");
      await api.mockServerCall(
        "GET",
        "**/getmailinglist",
        "fixture:databuilder/taskchainmodeler/MailingList",
        "getMailingList"
      );
      await api.mockServerCall(
        "GET",
        "**/designObjects?folderIds**",
        "fixture:databuilder/taskchainmodeler/designObjects",
        "designObjects"
      );
      await api.mockServerCall(
        "GET",
        "**/repository/designObjects**",
        "fixture:databuilder/taskchainmodeler/designObjects",
        "designObjects"
      );
      await api.mockServerCall(
        "GET",
        "**/repository/designObjects?folderIds**",
        "fixture:databuilder/taskchainmodeler/designObjects",
        "designObjects"
      );
      await api.mockServerCall(
        "GET",
        "**/repository/SPACE1234/designObjects?ids=791B0FB0C0**",
        "fixture:databuilder/taskchainmodeler/demo",
        "demo"
      );
      await api.mockServerCall("POST", "**/touch/sap.dwc.taskChain**", {}, "touched");
      await api.mockServerCall("GET", "**/chainable?applicationId**", { objectCurrentlyChainable: true }, "chainable");
      await api.mockServerCall("POST", "**/directexecute", {}, "directExecute");
      await api.mockServerCall(
        "GET",
        "**logs?objectId=demo.**",
        {
          locks: [],
          logs: [
            {
              logId: 1526159,
              spaceId: "I520900RV",
              applicationId: "API",
              objectId: "SPACE1234.APITask_0",
              activity: "TEST_RUN",
              startTime: "2025-06-02T04:02:05.140Z",
              endTime: "2025-06-02T04:02:13.226Z",
              status: "RUNNING",
              user: "Parul Kashyap",
              runId: "61b9c7c474a0f4c9255b5860a7f977ec",
              runTime: 8086,
            },
          ],
        },
        "taskChains"
      );
    });

    describe("{taskchain} ParallelTC21_APITask spec with SDP", () => {
      it("Check status request body should be disabled for Async and ", async function () {
        const TCModeler = await initialAction(api);
        await api.waitHTMLStabilized();

        // properties panel
        await api.setRadioButton(DB_INPUT.TC_API_NODE_INVOKE_MODE, 1);
        await api.setRadioButton(DB_INPUT.TC_API_NODE_INVOKE_RESPONSETYPE, 0);
        await TCModeler.properties.assertControlExists({ id: DB_INPUT.TC_API_NODE_STATUS_REQUESTBODY, enabled: false });
      });
      it("Verify test run can be reinitiated", async function () {
        const databuilder = (await api.openModulePage(ShellPages.DataBuilder)) as DataBuilder;
        await api.openSpace("SPACE1234");
        await databuilder.landingPage.openModel("demo");
        const TCModeler = await databuilder.landingPage.openTaskChainModel();
        await api.waitHTMLStabilized();

        await TCModeler.browser.assertControlExists({
          id: "taskChainModeler--taskChainModelerEditorControl--diagram-intHeader",
        });
        await TCModeler.diagram.selectSymbol({ id: "Task/demo" }, true);

        // properties panel
        await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_CONNECTION);
        await selectItem(DB_INPUT.TC_API_NODE_CONNECTION, 6);
        await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_CONNECTION);
        await api.waitHTMLStabilized();

        await TCModeler.properties.typeInputText(
          DB_INPUT.TC_API_NODE_INVOKE_APIPATH,
          "/async-code-and-location-url-status-from-code"
        );
        await api.setRadioButton(DB_INPUT.TC_API_NODE_INVOKE_MODE, 1);
        await api.typeText(DB_INPUT.TC_API_NODE_INVOKE_REQUESTBODY, '{{}"id": "10"{}}');
        await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_INVOKE_FORMATBTN);
        await api.wait(1);

        await TCModeler.browser.clickButton(DB_INPUT.TC_API_NODE_INVOKE_CSRFTOKEN_SWITCH);
        await TCModeler.browser.clickButton(DB_INPUT.TC_API_NODE_INVOKE_CSRFTOKEN_SWITCH);
        await api.setRadioButton(DB_INPUT.TC_API_NODE_INVOKE_RESPONSETYPE, 0);
        await api.setRadioButton(DB_INPUT.TC_API_NODE_STATUS_METHOD, 0);
        await api.setRadioButton(DB_INPUT.TC_API_NODE_STATUS_RESPONSETYPE, 1);
        await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_STATUS_SUCCESSVARIABLE, "status");
        await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_STATUS_SUCCESSVALUE, "SUCCESS");
        await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_STATUS_ERRORVARIABLE, "status");
        await TCModeler.properties.typeInputText(DB_INPUT.TC_API_NODE_STATUS_ERRORVALUE, "FAILED");
        await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_TESTRUN_BTN);

        await api.wait(2);
        await TCModeler.properties.clickButton(DB_INPUT.TC_API_NODE_TESTRUN_BTN);
        await TCModeler.browser.assertControlExists({ id: "apiTaskRunningDialog" });
        await TCModeler.browser.clickButton("dialogBtnOK");
        await api.XHRWait("@directExecute");
      });
    });
  });
});
/* jscpd:ignore-end */
