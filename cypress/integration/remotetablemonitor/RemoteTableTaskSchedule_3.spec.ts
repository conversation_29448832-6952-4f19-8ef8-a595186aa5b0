/** @format */
/* jscpd:ignore-start */
import {
  assertControl,
  assertInput,
  assertMenuButtonItem,
  assertMessageBox,
  assertUITableCellText,
  assertUITableRowCount,
  clickMenuButtonItem,
  confirmMessageBox,
  openRemoteTablesOverviewPage,
  prepareTestEnvironment,
  pressButton,
  selectTableRow,
} from "../../pageobjects/dataIntegration/DataIntegrationHelper";

describe("Remote Table Monitoring task schedule 2", () => {
  const DI_PAGE = "shellMainContent---dataIntegrationComponent---remotetablemonitor";
  const TASKSCHEDULE_DIALOG =
    "shellMainContent---dataIntegrationComponent---remotetablemonitor--scheduleDialog--taskScheduleDialog--view";
  const scheduleList = [
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "Covid19_Data",
      description: "Covid19_Data",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      owner: "DWC_DEVELOPER1",
      changedAt: "2020-07-25T07:03:07.464Z",
      nextSchedule: "2020-07-25T22:30:00.000Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "HDB_Invoices",
      description: "HDB_Invoices",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "ProductCategories",
      description: "ProductCategories",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "Products",
      description: "Products",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "HDB_Order_Details",
      description: "HDB_Order_Details",
      cron: "37 20 * * 3",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      owner: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
  ];

  describe(`${Cypress.spec.relative}`, () => {
    beforeEach(() => {
      prepareTestEnvironment({ DWC_DUMMY_SPACE_PERMISSIONS: false, DWCO_DI_MONITOR_UI_IMPROVEMENTS: false });
      cy.intercept("GET", "**/remoteTables**", { fixture: "remotetablemonitor/taskScheduleRemoteTables" }).as(
        "getRemoteTables"
      );
      cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });
      cy.intercept("GET", "**/resources/spaces?**", { fixture: "viewmonitor/spacesLockedResponse" }).as(
        "getSpaceDetails"
      );
      cy.intercept("GET", "**/schedules/consent", { consent: true }).as("getConsent");
    });

    describe("{TaskSchedule} Check remote table monitoring task schedule flow 3", () => {
      it("Check for menu options when schedule api fails withh 500 error", () => {
        cy.intercept("GET", "**/schedules?**", { statusCode: 500 }).as("getSchedules");
        const tableid = `${DI_PAGE}--idRemoteTablesTable`;
        openRemoteTablesOverviewPage("SPACE1234");
        // cy.wait(["@getUser", "@getContractEnddate", "@getRemoteTables"]);
        cy.wait("@getRemoteTables");
        // cy.wait(600000);
        assertControl(tableid, "exist");
        cy.wait(100);
        assertUITableRowCount(tableid, 13);
        selectTableRow(tableid, 1);
        cy.wait(100);
        assertMenuButtonItem(`${DI_PAGE}--tableReplicationMenu`, 0, false);
      });

      it("Check for Schedule Dialog to close without any changes", () => {
        const tableid = `${DI_PAGE}--idRemoteTablesTable`;
        cy.intercept("GET", "**/schedules?**", scheduleList).as("getSchedules");
        cy.intercept("PUT", "**/ownerchange", { statusCode: 204 }).as("ownerChange");
        openRemoteTablesOverviewPage("SPACE1234");
        cy.wait("@getRemoteTables");
        cy.wait(1000);
        selectTableRow(tableid, 5);
        cy.wait(100);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 0, false);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 1, true);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 2, true);
        clickMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 1);
        pressButton(`${DI_PAGE}--scheduleDialog--taskScheduleDialog--view--cancelButton`);
        assertControl("closeWarn", "not.exist");
      });

      it("Check for Task schedule Takeover", () => {
        const ownerChangeResp = {
          scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
          spaceId: "VIEW_MONITOR_SPACE",
          applicationId: "REMOTE_TABLES",
          activity: "REPLICATE",
          objectId: "Covid19_Data",
          description: "Covid19_Data",
          cron: "30 22 */2 * *",
          validFrom: "2020-07-26T12:00:00.000Z",
          validTo: "2020-07-30T12:00:00.000Z",
          activationStatus: "ENABLED",
          createdBy: "DWC_DEVELOPER",
          createdAt: "2020-07-25T07:03:06.673Z",
          changedBy: null,
          owner: "DWC_DEVELOPER2",
          changedAt: "2020-07-25T07:03:07.464Z",
          nextSchedule: "2020-07-25T22:30:00.000Z",
        };
        cy.intercept("GET", "**/schedules?**", scheduleList).as("getSchedules");
        cy.intercept("PUT", "**/ownerchange", { statusCode: 204 }).as("ownerChange");
        openRemoteTablesOverviewPage("SPACE1234");
        cy.wait("@getRemoteTables");
        cy.wait(100);
        const tableid = `${DI_PAGE}--idRemoteTablesTable`;
        selectTableRow(tableid, 5);
        cy.wait(100);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 0, false);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 1, true);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 2, true);
        clickMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 1);
        assertInput(`${DI_PAGE}--scheduleDialog--taskScheduleDialog--view--owner`, "DWC_DEVELOPER1");
        pressButton(`${DI_PAGE}--scheduleDialog--taskScheduleDialog--view--takeover`);
        assertMessageBox("ConfirmationUser");
        cy.intercept("GET", "**/schedules/**", ownerChangeResp).as("getSchedule");
        confirmMessageBox();
        cy.wait("@getSchedule");
        assertInput(`${DI_PAGE}--scheduleDialog--taskScheduleDialog--view--owner`, "DWC_DEVELOPER2");
      });

      it("Check status to enable schedule and check for expired runs", () => {
        const schedules = [
          {
            scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
            spaceId: "VIEW_MONITOR_SPACE",
            applicationId: "REMOTE_TABLES",
            activity: "REPLICATE",
            objectId: "Covid19_Data",
            description: "Covid19_Data",
            cron: "30 22 */2 * *",
            validFrom: "2020-07-26T12:00:00.000Z",
            validTo: "2020-07-30T12:00:00.000Z",
            activationStatus: "ENABLED",
            createdBy: "DWC_DEVELOPER",
            createdAt: "2020-07-25T07:03:06.673Z",
            changedBy: null,
            owner: "DWC_DEVELOPER1",
            changedAt: "2020-07-25T07:03:07.464Z",
          },
        ];
        const tableid = `${DI_PAGE}--idRemoteTablesTable`;
        cy.intercept("GET", "**/schedules?**", schedules).as("getSchedules");
        cy.intercept("PUT", "**/ownerchange", { statusCode: 204 }).as("ownerChange");
        openRemoteTablesOverviewPage("SPACE1234");
        cy.wait("@getRemoteTables");
        cy.wait(200);
        selectTableRow(tableid, 0);
        cy.wait(100);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 0, true);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 1, false);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 2, false);
        assertUITableCellText(tableid, 5, 6, "Expired");
      });
    });
  });
});
/* jscpd:ignore-end */
