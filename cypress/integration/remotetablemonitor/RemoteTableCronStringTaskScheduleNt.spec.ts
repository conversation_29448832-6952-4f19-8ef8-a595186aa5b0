/** @format */

import {
  assertControl,
  assertInput,
  assertMenuButtonItem,
  clickMenuButtonItem,
  openRemoteTablesOverviewPage,
  prepareTestEnvironment,
  pressButton,
  selectItem,
  selectTableRow,
  typeInputText,
} from "../../pageobjects/dataIntegration/DataIntegrationHelper";

describe("Remote table Monitor - Disable Real time replication", () => {
  const DI_PAGE = "shellMainContent---dataIntegrationComponent---remotetablemonitor";
  const tableid = `${DI_PAGE}--idRemoteTablesTable`;
  const TASKSCHEDULE_DIALOG =
    "shellMainContent---dataIntegrationComponent---remotetablemonitor--scheduleDialog--taskScheduleDialog--view";
  const scheduleList = [
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "Covid19_Data",
      description: "Covid19_Data",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      owner: "DWC_DEVELOPER1",
      changedAt: "2020-07-25T07:03:07.464Z",
      nextSchedule: "2020-07-25T22:30:00.000Z",
      uiVariant: "FORM",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "HDB_Invoices",
      description: "HDB_Invoices",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "ProductCategories",
      description: "ProductCategories",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "Products",
      description: "Products",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "HDB_Order_Details",
      description: "HDB_Order_Details",
      cron: "37 20 * * 3",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      owner: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
  ];
  beforeEach(() => {
    prepareTestEnvironment({
      DWC_DUMMY_SPACE_PERMISSIONS: false,
      DWCO_DI_MONITOR_UI_IMPROVEMENTS: false,
    });
    cy.intercept("GET", "**/remoteTables**", { fixture: "remotetablemonitor/taskScheduleRemoteTables" }).as(
      "getRemoteTables"
    );
    cy.intercept("GET", "**/remoteTables/TEST_TABLE2?**", {
      fixture: "remotetablemonitor/remoteTableDropSubscriptionDetails",
    }).as("getRemoteTableDetails");
    cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });
    cy.intercept("GET", "**/persistedViews**", { fixture: "viewmonitor/persistedViews.json" }).as("getPersistedViews");
    cy.intercept("GET", "**/schedules?**", scheduleList).as("getSchedules");
    cy.intercept("GET", "**/resources/spaces?islocked&spaceids=SPACE1234**", { SPACE1234: { isLocked: false } }).as(
      "getSpaceLockStatus"
    );
    cy.intercept("GET", "**/logs?**", []).as("getPersistencyLogs");
    cy.intercept("GET", "**/repository/remotes?space_ids=**", { fixture: "remotetablemonitor/connection" }).as(
      "getRemoteSourceInfo"
    );
    cy.intercept("GET", "**/connections/status/**", { fixture: "remotetablemonitor/remoteTableIsConnectionValid" }).as(
      "isConnectionValid"
    );
    cy.intercept("POST", "**/schedules", { scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" });
  });

  describe("Remote table disable replication - details page", () => {
    beforeEach(() => {
      openRemoteTablesOverviewPage("SPACE1234");
      cy.wait(["@getUser", "@getContractEnddate", "@getRemoteTables"]);
    });

    it("DW101-75522 - Users can schedule every 1 minute from the UI", () => {
      cy.wait(200);
      assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 0, false);
      assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 1, false);
      assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 2, false);
      selectTableRow(`${DI_PAGE}--idRemoteTablesTable`, 2);
      clickMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 1);
      cy.wait(200);
      assertControl(`${DI_PAGE}--scheduleDialog`);
      cy.wait(200);
      assertInput(`${TASKSCHEDULE_DIALOG}--endDate`, "2020-07-30");
      cy.get(`#${TASKSCHEDULE_DIALOG}--endDate`).clear();
      cy.get(`#${TASKSCHEDULE_DIALOG}--endDate`).type("{enter}");
      cy.get("#" + `${TASKSCHEDULE_DIALOG}--expiredScheduleText`).should("not.exist");

      // Check for Minute scenario
      selectItem(`${TASKSCHEDULE_DIALOG}--frequencyType`, 1);

      typeInputText(`${TASKSCHEDULE_DIALOG}--cronMin`, "*");
      typeInputText(`${TASKSCHEDULE_DIALOG}--cronHour`, "*");
      typeInputText(`${TASKSCHEDULE_DIALOG}--cronDay`, "*");
      typeInputText(`${TASKSCHEDULE_DIALOG}--cronMon`, "*");
      typeInputText(`${TASKSCHEDULE_DIALOG}--cronWeek`, "*");
      pressButton(`${TASKSCHEDULE_DIALOG}--createButton`);
    });
  });
});
