/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { BuilderType, getAPIInstance, IAPI } from "../../pageobjects/api/IAPI";
import { XHelpers } from "../../pageobjects/api/impl/Helpers";
import { selectTableRow } from "../../pageobjects/dataIntegration/DataIntegrationHelper";
import { ShellPages } from "../../pageobjects/shell";
/* jscpd:ignore-start */
describe("Remote Table Monitoring task schedule 5", () => {
  let api: IAPI;
  const DI_PAGE = "shellMainContent---dataIntegrationComponent---remotetablemonitor";
  const TASKSCHEDULE_DIALOG =
    "shellMainContent---dataIntegrationComponent---remotetablemonitor--scheduleDialog--taskScheduleDialog--view";
  const scheduleList = [
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "Covid19_Data",
      description: "Covid19_Data",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      owner: "DWC_DEVELOPER1",
      changedAt: "2020-07-25T07:03:07.464Z",
      nextSchedule: "2020-07-25T22:30:00.000Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "HDB_Invoices",
      description: "HDB_Invoices",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "ProductCategories",
      description: "ProductCategories",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "Products",
      description: "Products",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "HDB_Order_Details",
      description: "HDB_Order_Details",
      cron: "37 20 * * 3",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      owner: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "9483bfdb-266f-4f56-a470-782797be9fad",
      spaceId: "SYLVAINFREINTEST",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "Employees",
      externalScheduleId: "deb37c4f-79ce-48bc-9414-df3a20ab310b",
      description: "DATA_FOR_DATA_FLOW_SOURCE",
      validFrom: "2023-07-19T00:00:00.000Z",
      activationStatus: "DISABLED",
      createdBy: "DEVIPRIYA_DHARMARAJ",
      createdAt: "2023-07-19T18:28:00.994Z",
      changedBy: "DEVIPRIYA_DHARMARAJ",
      changedAt: "2023-07-19T18:29:36.225Z",
      owner: "DEVIPRIYA_DHARMARAJ",
      uiVariant: "FORM",
      frequency: {
        interval: 1,
        startDate: "2023-07-19T19:27:00.000Z",
        type: "DAILY",
      },
      nextRun: "2023-07-20T19:27:00.000Z",
    },
  ];

  describe(`${Cypress.spec.relative} without SDP`, () => {
    beforeEach(async () => {
      api = getAPIInstance();
      const routes = [...XHelpers.DEFAULT_MOCK_REPONSES];
      XHelpers.modifyFeatureFlag(routes, "DWC_DUMMY_SPACE_PERMISSIONS", false);
      XHelpers.modifyFeatureFlag(routes, "DWCO_DI_MONITOR_UI_IMPROVEMENTS", false);
      await api.setupBeforeEach(/*useMock*/ true, /*defaultFixture*/ true, /*routes*/ routes);
      await api.mockServerCall(
        "GET",
        "**/remoteTables**",
        "fixture:remotetablemonitor/taskScheduleRemoteTables",
        "getRemoteTables"
      );
      await api.mockServerCall("GET", "**/schedules?**", [], "getSchedules");
    });

    describe("{TaskSchedule} Check remote table monitoring task schedule flow 4", () => {
      it("Check for create schedule dialog does not contain warning strip if schedule is paused", async () => {
        await api.mockServerCall("GET", "**/schedules/consent", { consent: true }, "getConsent");
        await api.mockServerCall(
          "PUT",
          "**/schedules/**",
          { id: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" },
          "putSchedules"
        );
        await api.mockServerCall("GET", "**/schedules?**", scheduleList, "getSchedules");
        await api.mockServerCall("GET", "**/schedules/**", scheduleList[4], "getSchedule");
        const tableid = `${DI_PAGE}--idRemoteTablesTable`;
        const createScheduleMenu = `${DI_PAGE}--scheduleMenu`;
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        await api.assertTable({
          table: tableid,
          rows: 13,
        });
        selectTableRow(`${DI_PAGE}--idRemoteTablesTable`, 2).then(async () => {
          await api.assertMenuButtonItem({
            id: `${DI_PAGE}--scheduleMenu`,
            itemIndex: 0,
            enabled: false,
          });
          await api.assertMenuButtonItem({
            id: `${DI_PAGE}--scheduleMenu`,
            itemIndex: 1,
            enabled: true,
          });
          await api.assertMenuButtonItem({
            id: `${DI_PAGE}--scheduleMenu`,
            itemIndex: 2,
            enabled: true,
          });
          await api.clickMenuButtonItem(createScheduleMenu, 1);
          await api.assertControlNotExists({
            id: "shellMainContent---dataIntegrationComponent---remotetablemonitor--scheduleDialog--taskScheduleDialog--view--msgRealTimeReplication",
          });
        });
      });
    });
  });

  describe(`${Cypress.spec.relative} with SDP`, () => {
    beforeEach(async () => {
      api = getAPIInstance();
      const routes = [...XHelpers.DEFAULT_MOCK_REPONSES, ...XHelpers.SDP_SPECIFIC_MOCK_RESPONSES];
      await api.setupBeforeEach(
        /* useMock*/ true,
        /* defaultFixture*/ true,
        /* routes*/ routes,
        /* overrideRouteCallback*/ undefined,
        /* enabledFeatureFlags*/ ["DWC_DUMMY_SPACE_PERMISSIONS"]
      );
      await api.mockServerCall(
        "GET",
        "**/remoteTables**",
        "fixture:remotetablemonitor/taskScheduleRemoteTables",
        "getRemoteTables"
      );
      await api.mockServerCall("GET", "**/schedules?**", [], "getSchedules");
    });

    describe("{TaskSchedule} Check remote table monitoring task schedule flow 4", () => {
      it("Check if schedule is enabled for real-time post blue-green feature implementation", async () => {
        const tableid = `${DI_PAGE}--idRemoteTablesTable`;
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        await api.assertTable({
          table: tableid,
          rows: 13,
        });
        selectTableRow(`${DI_PAGE}--idRemoteTablesTable`, 2).then(async () => {
          await api.assertMenuButtonItem({
            id: `${DI_PAGE}--scheduleMenu`,
            itemIndex: 0,
            enabled: true,
          });
          await api.assertMenuButtonItem({
            id: `${DI_PAGE}--scheduleMenu`,
            itemIndex: 1,
            enabled: false,
          });
          await api.assertMenuButtonItem({
            id: `${DI_PAGE}--scheduleMenu`,
            itemIndex: 2,
            enabled: false,
          });
        });
      });
    });
  });
});
/* jscpd:ignore-end */
