/**
 * Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { BuilderType, getAPIInstance, IAPI } from "../../pageobjects/api/IAPI";
import { XHelpers } from "../../pageobjects/api/impl/Helpers";
import { ShellPages } from "../../pageobjects/shell";
/* jscpd:ignore-start */
describe("Remote table details - Busniess name toggle", () => {
  let api: IAPI;
  const DI_PAGE = "shellMainContent---dataIntegrationComponent---remotetablemonitor";

  describe(`${Cypress.spec.relative} without SDP`, () => {
    beforeEach(async () => {
      api = getAPIInstance();
      const routes = [...XHelpers.DEFAULT_MOCK_REPONSES];
      XHelpers.modifyFeatureFlag(routes, "DWC_DUMMY_SPACE_PERMISSIONS", false);
      XHelpers.modifyFeatureFlag(routes, "DWCO_DI_MONITOR_UI_IMPROVEMENTS", false);
      await api.setupBeforeEach(/*useMock*/ true, /*defaultFixture*/ true, /*routes*/ routes);
      await api.mockServerCall(
        "GET",
        "**/remoteTables**",
        "fixture:remotetablemonitor/remoteTableBlueGreenReplication",
        "getRemoteTables"
      );
      await api.mockServerCall(
        "GET",
        "**/remoteTables/ADDRESS?**",
        "fixture:remotetablemonitor/remoteTableDetails",
        "getRemoteTableDetails"
      );
      await api.mockServerCall("GET", "**/schedules**", [], "getSchedules");
      await api.mockServerCall("GET", "**/logs?**", [], "getPersistencyLogs");
      await api.mockServerCall("GET", "**/schedules/consent", { consent: false }, "getConsent");
      await api.mockServerCall("GET", "**/statistics/**", [], "getStatistics");
      await api.mockServerCall("GET", "**/resources/spaces**", "fixture:spacesResponse", "getSpaceDetails");
    });

    describe("Remote table details - Toggle Business Name", () => {
      it("Check table name & connection name", async () => {
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        const tableid = `${DI_PAGE}--idRemoteTablesTable`;

        await api.assertControlExists({
          id: tableid,
        });
        await api.XHRWait("@getRemoteTables");
        await api.wait(2);
        await api.selectTableRow(tableid, 0, false, true);
        const table = await cy.byId(tableid);
        expect(table.getRows()[5].getCells()[0].getText()).to.equals("W4D_HANA");
        expect(table.getRows()[5].getCells()[1].getText()).to.equals("TBook");
        expect(table.getRows()[0].getCells()[0].getText()).to.equals("W4D_HANA");
      });

      it("Check Latest Change source and Number of Records with out Feature Flag", async () => {
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        const tableid = `${DI_PAGE}--idRemoteTablesTable`;

        await api.assertControlExists({
          id: tableid,
        });
        await api.XHRWait("@getRemoteTables");
        await api.assertTable({
          table: tableid,
          rows: 13,
        });
        await api.wait(2);
        await api.pressButton(
          "shellMainContent---dataIntegrationComponent---remotetablemonitor--personalizeTableButton"
        );
        await api.setCheckBox(
          "shellMainContent---dataIntegrationComponent---remotetablemonitor--idRemoteTablesTable-PersoDialog-cli-shellMainContent---dataIntegrationComponent---remotetablemonitor--idRemoteTablesTable-PersoDialog-colTable-6",
          true
        );
        await api.selectTableRow(tableid, 0, false, true);
        const table = await cy.byId(tableid);
        expect(table.getRows()[0].getCells()[7].getText()).to.equals("450");
        expect(table.getRows()[4].getCells()[7].getText()).to.equals("0");
      });

      it("Check Latest Change source and Number of Records in Details page with Feature Flag", async () => {
        await api.mockServerCall(
          "GET",
          "**/remoteTables/ADDRESS?**",
          "fixture:remotetablemonitor/newremoteTableDetails",
          "getDetails"
        );
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        const tableid = `${DI_PAGE}--idRemoteTablesTable`;

        await api.assertControlExists({
          id: tableid,
        });
        await api.XHRWait("@getRemoteTables");
        await api.selectTableRow(tableid, 0, false, true);
        const table = await cy.byId(tableid);
        await table.getRows()[0].getRowAction().getItems()[0].firePress();
        await api.XHRWait("@getDetails");
        await api.wait(1);
        const latestChangeSourceId = await cy.byId("taskLog--latestChangeSource");
        const numberOfRecordsId = await cy.byId("taskLog--numberOfRecords");
        assert.equal(latestChangeSourceId.getText(), "");
        assert.equal(numberOfRecordsId.getText(), "450");
      });
    });
  });

  describe(`${Cypress.spec.relative} with SDP`, () => {
    beforeEach(async () => {
      api = getAPIInstance();
      const routes = [...XHelpers.DEFAULT_MOCK_REPONSES, ...XHelpers.SDP_SPECIFIC_MOCK_RESPONSES];
      await api.setupBeforeEach(
        /* useMock*/ true,
        /* defaultFixture*/ true,
        /* routes*/ routes,
        /* overrideRouteCallback*/ undefined,
        /* enabledFeatureFlags*/ ["DWC_DUMMY_SPACE_PERMISSIONS"]
      );
      await api.mockServerCall(
        "GET",
        "**/remoteTables**",
        "fixture:remotetablemonitor/remoteTableBlueGreenReplication",
        "getRemoteTables"
      );
      await api.mockServerCall(
        "GET",
        "**/remoteTables/ADDRESS?**",
        "fixture:remotetablemonitor/remoteTableDetails",
        "getRemoteTableDetails"
      );
      await api.mockServerCall("GET", "**/schedules**", [], "getSchedules");
      await api.mockServerCall("GET", "**/logs?**", [], "getPersistencyLogs");
      await api.mockServerCall("GET", "**/schedules/consent", { consent: false }, "getConsent");
      await api.mockServerCall("GET", "**/statistics/**", [], "getStatistics");
      await api.mockServerCall("GET", "**/resources/spaces**", "fixture:spacesResponse", "getSpaceDetails");
    });

    describe("Remote table details - Toggle Business Name", () => {
      it("Check table name & connection name", async () => {
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        const tableid = `${DI_PAGE}--idRemoteTablesTable`;

        await api.assertControlExists({
          id: tableid,
        });
        await api.XHRWait("@getRemoteTables");
        await api.selectTableRow(tableid, 0, false, true);
        const table = await cy.byId(tableid);
        expect(table.getRows()[5].getCells()[0].getText()).to.equals("W4D_HANA");
        expect(table.getRows()[5].getCells()[1].getText()).to.equals("TBook");
        expect(table.getRows()[0].getCells()[0].getText()).to.equals("W4D_HANA");
      });
    });
  });
});
/* jscpd:ignore-end */
