/** @format */
/* jscpd:ignore-start */
import moment from "moment";
import {
  assertButton,
  assertInput,
  assertInputValueState,
  assertMenuButtonItem,
  clickMenuButtonItem,
  openRemoteTablesOverviewPage,
  prepareTestEnvironment,
  pressButton,
  selectItem,
  selectTableRow,
  typeTextInput,
} from "../../pageobjects/dataIntegration/DataIntegrationHelper";

describe("Remote Table Monitoring task schedule", () => {
  const DI_PAGE = "shellMainContent---dataIntegrationComponent---remotetablemonitor";
  const tableid = `${DI_PAGE}--idRemoteTablesTable`;
  const TASKSCHEDULE_DIALOG =
    "shellMainContent---dataIntegrationComponent---remotetablemonitor--scheduleDialog--taskScheduleDialog--view";
  const scheduleList = [
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "Covid19_Data",
      description: "Covid19_Data",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      owner: "DWC_DEVELOPER1",
      changedAt: "2020-07-25T07:03:07.464Z",
      nextSchedule: "2020-07-25T22:30:00.000Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "HDB_Invoices",
      description: "HDB_Invoices",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "ProductCategories",
      description: "ProductCategories",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "Products",
      description: "Products",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "HDB_Order_Details",
      description: "HDB_Order_Details",
      cron: "37 20 * * 3",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      owner: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "HDB_Order_Details",
      description: "HDB_Order_Details",
      cron: "37 20 * * 3",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      owner: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
  ];

  const scheduleList2 = [
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "Covid19_Data",
      description: "Covid19_Data",
      cron: "*/10 * * * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      owner: "DWC_DEVELOPER1",
      changedAt: "2020-07-25T07:03:07.464Z",
      nextSchedule: "2020-07-25T22:30:00.000Z",
    },
  ];

  describe(`${Cypress.spec.relative}`, () => {
    beforeEach(() => {
      prepareTestEnvironment({
        DWC_DUMMY_SPACE_PERMISSIONS: false,
        DWCO_DI_MONITOR_UI_IMPROVEMENTS: false,
      });
      cy.intercept("GET", "**remoteTables**", { fixture: "remotetablemonitor/taskScheduleRemoteTables" }).as(
        "getRemoteTables"
      );
      cy.intercept("GET", "**/schedules?**", []).as("getSchedules");
      cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });
      cy.intercept("GET", "**/schedules/consent", { consent: true }).as("getConsent");
      cy.intercept("GET", "**/resources/spaces?**", { fixture: "viewmonitor/spacesLockedResponse" }).as(
        "getSpaceDetails"
      );
    });

    describe("{TaskSchedule} Check remote table monitoring task schedule flow 4", () => {
      it("Check for invalid start date", () => {
        cy.intercept("PUT", "**/schedules/**", { id: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" }).as("putSchedules");
        cy.intercept("GET", "**/schedules?**", scheduleList).as("getSchedules");
        cy.intercept("GET", "**/schedules/**", scheduleList[0]).as("getSchedule");
        openRemoteTablesOverviewPage("SPACE1234");
        cy.wait("@getRemoteTables");
        cy.wait(100);
        selectTableRow(tableid, 5).then(() => {
          cy.wait(100);
          clickMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 1);
          cy.wait(100);
          assertButton(`${TASKSCHEDULE_DIALOG}--createButton`, true);
          assertInputValueState(`${TASKSCHEDULE_DIALOG}--startDate`, "None");
          typeTextInput(`${TASKSCHEDULE_DIALOG}--startDate`, "Jul 31, 2020");
          cy.get("#" + `${TASKSCHEDULE_DIALOG}--startDate`).type("{enter}");
          cy.wait(100);
          assertInputValueState(`${TASKSCHEDULE_DIALOG}--startDate`, "Error");
          assertButton(`${TASKSCHEDULE_DIALOG}--createButton`, true);
          cy.get("#" + `${TASKSCHEDULE_DIALOG}--startDate`).clear();
          cy.get("#" + `${TASKSCHEDULE_DIALOG}--startDate`).type("Jul 26, 2020{enter}");
          cy.wait(100);
          assertInputValueState(`${TASKSCHEDULE_DIALOG}--startDate`, "None");
          assertButton(`${TASKSCHEDULE_DIALOG}--createButton`, true);
          assertInput(`${TASKSCHEDULE_DIALOG}--endDate`, "2020-07-30");
        });
      });

      it("should check if start time is set one hour from current time", () => {
        cy.intercept("PUT", "**/schedules/**", { id: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" }).as("putSchedules");
        cy.intercept("GET", "**/schedules?**", scheduleList).as("getSchedules");
        cy.intercept("GET", "**/schedules/**", scheduleList[0]).as("getSchedule");
        openRemoteTablesOverviewPage("SPACE1234");
        cy.wait("@getRemoteTables");
        cy.wait(100);
        selectTableRow(tableid, 1);
        cy.wait(100);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 0, true);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 1, false);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 2, false);
        clickMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 0);
        cy.wait(100);
        const defaultStartTime = moment.utc().add(1, "hour");
        const startHourVal =
          defaultStartTime.hour() < 10 ? `0${defaultStartTime.hour()}` : `${defaultStartTime.hour()}`;
        const startMinVal =
          defaultStartTime.minute() < 10 ? `0${defaultStartTime.minute()}` : `${defaultStartTime.minute()}`;
        const defStartTimeValue = `${startHourVal}:${startMinVal}`;
        assertInput(`${TASKSCHEDULE_DIALOG}--scheduleTime`, defStartTimeValue);
      });

      it("should check for Minute recurrence in Simple form", () => {
        cy.intercept("GET", "**/schedules?**", scheduleList).as("getSchedules");
        cy.intercept("GET", "**/schedules/**", scheduleList[0]).as("getSchedule");
        openRemoteTablesOverviewPage("SPACE1234");
        cy.wait("@getRemoteTables");
        cy.wait(100);
        selectTableRow(tableid, 1).then(() => {
          cy.wait(100);
          assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 0, true);
          assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 1, false);
          assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 2, false);
          clickMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 0);
          cy.wait(100);
          selectItem(`${TASKSCHEDULE_DIALOG}--recurrenceType`, 4);
        });
      });

      it("should check for Minute recurrence Edit Scenario in Simple form", () => {
        cy.intercept("GET", "**/schedules?**", scheduleList2).as("getSchedules");
        cy.intercept("GET", "**/schedules/**", scheduleList2[0]).as("getSchedule");
        cy.intercept("PUT", "**/schedules/0dc5199e-76f2-4160-b11b-4a223aa9fa8d", { statusCode: 204 }).as(
          "saveSchedule"
        );
        openRemoteTablesOverviewPage("SPACE1234");
        cy.wait("@getRemoteTables");
        cy.wait(100);
        selectTableRow(tableid, 5);
        cy.wait(100);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 1, true);
        clickMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 1);
        cy.wait(100);
        selectItem(`${TASKSCHEDULE_DIALOG}--byMinRec`, 3);
        pressButton(`${TASKSCHEDULE_DIALOG}--createButton`);
        cy.wait("@saveSchedule");
      });
    });
  });
});
/* jscpd:ignore-end */
