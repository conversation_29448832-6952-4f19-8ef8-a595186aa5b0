/** @format */
/* jscpd:ignore-start */
import moment from "moment";
import {
  assertContainsText,
  assertControl,
  assertMenuButtonItem,
  assertSelectEnabled,
  clickMenuButtonItem,
  openRemoteTablesOverviewPage,
  prepareTestEnvironment,
  selectItem,
  selectTableRow,
  typeInputText,
} from "../../pageobjects/dataIntegration/DataIntegrationHelper";

describe("Remote table Monitoring refined task scheduling Future runs test", () => {
  const DI_PAGE = "shellMainContent---dataIntegrationComponent---remotetablemonitor";
  const TASKSCHEDULE_DIALOG =
    "shellMainContent---dataIntegrationComponent---remotetablemonitor--scheduleDialog--taskScheduleDialog--view";
  const scheduleList = [
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "Covid19_Data",
      description: "Covid19_Data",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      owner: "DWC_DEVELOPER1",
      changedAt: "2020-07-25T07:03:07.464Z",
      nextSchedule: "2020-07-25T22:30:00.000Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "HDB_Invoices",
      description: "HDB_Invoices",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "ProductCategories",
      description: "ProductCategories",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "Products",
      description: "Products",
      cron: "30 22 */2 * *",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
    {
      scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
      spaceId: "VIEW_MONITOR_SPACE",
      applicationId: "REMOTE_TABLES",
      activity: "REPLICATE",
      objectId: "HDB_Order_Details",
      description: "HDB_Order_Details",
      cron: "37 20 * * 3",
      validFrom: "2020-07-26T12:00:00.000Z",
      validTo: "2020-07-30T12:00:00.000Z",
      activationStatus: "ENABLED",
      createdBy: "DWC_DEVELOPER",
      owner: "DWC_DEVELOPER",
      createdAt: "2020-07-25T07:03:06.673Z",
      changedBy: null,
      changedAt: "2020-07-25T07:03:07.464Z",
    },
  ];

  describe(`${Cypress.spec.relative}`, () => {
    beforeEach(() => {
      prepareTestEnvironment({ DWCO_DI_MONITOR_UI_IMPROVEMENTS: false });
      cy.intercept("GET", "**/remoteTables**", { fixture: "remotetablemonitor/taskScheduleRemoteTables" }).as(
        "getRemoteTables"
      );
      cy.intercept("GET", "**/schedules?**", []).as("getSchedules");
    });

    describe("{TaskSchedule} Check remote table monitoring task schedule flow for Future runs implementation in UTC", () => {
      it("Check for create schedule", () => {
        cy.intercept("GET", "**/schedules/consent", { consent: false }).as("getConsent");
        cy.intercept("POST", "**/schedules", { scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" }).as(
          "createSchedule"
        );
        openRemoteTablesOverviewPage("SPACE1234");
        cy.wait("@getRemoteTables");
        cy.wait(1000);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 0, false);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 1, false);
        assertMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 2, false);
        selectTableRow(`${DI_PAGE}--idRemoteTablesTable`, 1);
        clickMenuButtonItem(`${DI_PAGE}--scheduleMenu`, 0);
        cy.wait(1000);
        assertControl(`${DI_PAGE}--scheduleDialog`, "exist");
        assertSelectEnabled(`${TASKSCHEDULE_DIALOG}--recurrenceType`, true);
        cy.wait(500);
        selectItem(`${TASKSCHEDULE_DIALOG}--recurrenceType`, 0);
        cy.wait(500);
        typeInputText(`${TASKSCHEDULE_DIALOG}--scheduleTime`, "00:00");

        // Check for Hourly Schedule
        let nowUtc = moment.utc();
        let offsetMin = 60 - nowUtc.minute();
        let firstRun = nowUtc.add(offsetMin, "minute");
        let date1 = firstRun.format("MMM D, YYYY");
        let date2 = firstRun.add(1, "hour").format("MMM D, YYYY");
        let date3 = firstRun.add(1, "hour").format("MMM D, YYYY");
        let date4 = firstRun.add(1, "hour").format("MMM D, YYYY");
        let date5 = firstRun.add(1, "hour").format("MMM D, YYYY");
        cy.wait(500);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun1`, date1);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun2`, date2);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun3`, date3);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun4`, date4);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun5`, date5);

        // Check for Daily Schedule
        selectItem(`${TASKSCHEDULE_DIALOG}--recurrenceType`, 1);
        typeInputText(`${TASKSCHEDULE_DIALOG}--scheduleTime`, "00:00");
        nowUtc = moment.utc();
        firstRun = nowUtc.add(1, "day");
        nowUtc.minute(0);
        nowUtc.hour(0);
        nowUtc.second(0);
        date1 = firstRun.format("MMM D, YYYY");
        date2 = firstRun.add(1, "day").format("MMM D, YYYY");
        date3 = firstRun.add(1, "day").format("MMM D, YYYY");
        date4 = firstRun.add(1, "day").format("MMM D, YYYY");
        date5 = firstRun.add(1, "day").format("MMM D, YYYY");
        cy.wait(500);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun1`, date1);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun2`, date2);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun3`, date3);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun4`, date4);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun5`, date5);

        // Check for weekly schedule
        selectItem(`${TASKSCHEDULE_DIALOG}--recurrenceType`, 2);
        nowUtc = moment.utc();
        if (nowUtc.weekday() === 0) {
          nowUtc.weekday(1);
        } else {
          nowUtc.weekday(8);
        }
        nowUtc.minute(0);
        nowUtc.hour(0);
        nowUtc.second(0);
        firstRun = nowUtc;
        date1 = firstRun.format("MMM D, YYYY");
        date2 = firstRun.add(1, "week").format("MMM D, YYYY");
        date3 = firstRun.add(1, "week").format("MMM D, YYYY");
        date4 = firstRun.add(1, "week").format("MMM D, YYYY");
        date5 = firstRun.add(1, "week").format("MMM D, YYYY");
        cy.wait(500);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun1`, date1);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun2`, date2);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun3`, date3);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun4`, date4);
        assertContainsText(`${TASKSCHEDULE_DIALOG}--nextRun5`, date5);
      });
    });
  });
});
/* jscpd:ignore-end */
