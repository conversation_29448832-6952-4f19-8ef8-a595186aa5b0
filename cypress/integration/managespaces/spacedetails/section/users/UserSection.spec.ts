/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { SpaceDetailsPageObject } from "../../../../../pageobjects/spaceDetails";
import { waitNotBusy } from "../../../_util/SpacesTestUtil";

function prepareTestEnvironment(
  featureToggles: any = {},
  userIsMemberOfSpace: boolean = true,
  hasTimeData: boolean = false,
  hasDependencies: boolean = false
) {
  cy.server();
  cy.fixture("SACUserResponse").as("user");
  cy.fixture("SACContractEndDateFUTUREResponse").as("contractEndDateFUTURE");
  cy.fixture("navigationMenu").as("navigationMenu");
  cy.fixture("spaces/privilegesSDP").as("userprivilegesSDP");
  cy.fixture("spacesResponse").as("spaces");
  cy.fixture("spaces/spaceUsers").as("spaceUsers");
  cy.fixture("spaces/spaceUsersWithScopedRoles").as("spaceUsersWithScopedRoles");
  cy.fixture("spaces/scopedRoles").as("scopedRoles");
  cy.fixture("spaces/allUsers").as("allUsers");
  cy.fixture("spaces/timeData").as("timeData");
  cy.fixture("spaces/timeDataDependencies").as("timeDataDependencies");
  cy.fixture("connectivity/adapterList").as("adapterList");
  cy.route("GET", "**/notifications**", []);
  cy.route("GET", "**/notifications/count", {});
  cy.route("GET", "**/notifications/bns", {});
  cy.route("GET", "**/repository/spaces**", "@spaces").as("getSpaces");
  cy.route("GET", "**/repository/spaces**", "@spaces").as("getSpaces");
  cy.route("GET", "**/dwaas-core/", {});
  cy.route("GET", "**/repository/remotes**", {}).as("getConnectionCounts");
  cy.route("GET", "**/space/*/users", userIsMemberOfSpace ? "@spaceUsers" : []).as("getSpaceUsers");
  cy.route("POST", "**/space/*/users", { roles: "DW Administrator" }).as("attachSpaceUsers");
  cy.route("POST", "**/space/*/users/**", {}).as("updateUser");
  cy.route("DELETE", "**/space/*/users", {}).as("detachSpaceUsers");
  cy.route("GET", "**/space/*/listscoperoles", "@scopedRoles").as("getScopedRoles");
  cy.route("GET", "**/space/*/userassignment", "@spaceUsersWithScopedRoles").as("getSpaceUsersWithScopedRoles");
  cy.route("PUT", "**/space/*/userassignment", []).as("updateUsersWithScopedRoles");
  cy.route("DELETE", "**/space/*/userassignment", []).as("deleteUsersWithScopedRoles");
  cy.route("GET", "**/repository/designObjects**", {});
  cy.route("GET", "**/navigation/menu", "@navigationMenu");
  cy.route("GET", "**/userprivileges**", "@userprivilegesSDP").as("getPrivileges");
  cy.route("GET", "**/space/*/timedata", hasTimeData ? "@timeData" : { timeDimensions: [] }).as("getTimeData");
  cy.route("GET", "**/space/*/dependencies*", hasDependencies ? "@timeDataDependencies" : []).as(
    "getTimeDataDependencies"
  );
  cy.route("GET", "**/space/hdi/**", []);
  cy.route("GET", "**/security/customerhana/license", {}).as("getLicense");
  cy.route("GET", "**/security/customerhana/info", {
    ram: 256000000000,
    storage: 256000000000,
    threads: 128,
  });
  cy.route("POST", "**/repository/remotes/**", {});
  cy.route("GET", "**/repository/remotes**", []);
  cy.route("GET", "**/repository/allusers**", "@allUsers").as("getAllUsers");
  cy.fixture("spaces/spaceDetailsCSN").then((data) => {
    cy.route("GET", "**/api/v1/content?space**", data).as("getSpaceDetails");
  });
  cy.route("GET", "**/databaseusers", []);

  cy.fixture("spaces/resources/resourcesSpaces").then((data) => {
    cy.route("GET", "**/resources/**", data).as("getResources");
  });

  cy.route("GET", "**/space/hdi/containers?spaceId=SPACEDETAILSTEST", {
    exists: false,
    hdis: [],
  });

  cy.route({
    method: "POST",
    url: "**/repository/spaces?**",
    response: {},
    status: 200,
  }).as("setSpaceDetails");

  cy.route({
    url: "**/customerInfo**",
    method: "POST",
    response: "@contractEndDateFUTURE",
    status: 200,
  }).as("getContractEnddate");

  cy.route("GET", "**/featureflags", featureToggles || {});

  cy.visit("#/managespaces&/ms/details/SPACEDETAILSTEST");
  cy.wait("@getSpaceDetails");
  waitNotBusy();
}
describe("UserSection - Space dependent permissions", () => {
  describe("Section", () => {
    it("Should load and be visible", async () => {
      prepareTestEnvironment({ DWC_DUMMY_SPACE_PERMISSIONS: true });
      const spaceDetails = await cy.pageobject(
        "shellMainContent---managespacesComponent---spaceDetails",
        SpaceDetailsPageObject
      );
      await spaceDetails.isLoaded();
      cy.get("#shellMainContent---managespacesComponent---spaceDetails--UserDetailsView")
        .scrollIntoView()
        .should("be.visible");
    });

    it("Should display users", async () => {
      prepareTestEnvironment({ DWC_DUMMY_SPACE_PERMISSIONS: true });
      const spaceDetails = await cy.pageobject(
        "shellMainContent---managespacesComponent---spaceDetails",
        SpaceDetailsPageObject
      );
      await spaceDetails.isLoaded();
      cy.wrap({ getValue: async () => await spaceDetails.getNumberOfShownUsersSectionTable(true) })
        .invoke("getValue")
        .should("eq", 3);
    });

    it("Should filter users", async () => {
      prepareTestEnvironment({ DWC_DUMMY_SPACE_PERMISSIONS: true });
      const spaceDetails: SpaceDetailsPageObject = await cy.pageobject(
        "shellMainContent---managespacesComponent---spaceDetails",
        SpaceDetailsPageObject
      );
      await spaceDetails.isLoaded();
      await spaceDetails.filterUsersSectionTable("MOCKUSER1", true);
      cy.wrap({ getValue: async () => await spaceDetails.getNumberOfShownUsersSectionTable(true) })
        .invoke("getValue")
        .should("eq", 1);
    });

    it("Should detach users", async () => {
      prepareTestEnvironment({ DWC_DUMMY_SPACE_PERMISSIONS: true });
      const spaceDetails: SpaceDetailsPageObject = await cy.pageobject(
        "shellMainContent---managespacesComponent---spaceDetails",
        SpaceDetailsPageObject
      );
      await spaceDetails.isLoaded();
      await spaceDetails.detachUser("MOCKUSER1", true);
      cy.get(".sapMDialog").should("exist");
      cy.byId("removeUserDialog").then((dialog) => {
        dialog.getBeginButton().firePress();
      });
      cy.wrap({ getValue: async () => await spaceDetails.getNumberOfShownUsersSectionTable(true) })
        .invoke("getValue")
        .should("eq", 2);
    });

    it("Attach user without role - Button to be disabled", async () => {
      prepareTestEnvironment({ DWC_DUMMY_SPACE_PERMISSIONS: true });
      const spaceDetails: SpaceDetailsPageObject = await cy.pageobject(
        "shellMainContent---managespacesComponent---spaceDetails",
        SpaceDetailsPageObject
      );
      await spaceDetails.isLoaded();
      openAddUsersDialog();
      submitUserSelectionDialog(true, [0, 2]);
      cy.wait(["@getScopedRoles"]);
      cy.byId(
        "shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--RoleEditorDialog--dialog--view--ok"
      ).then((createUserButton) => {
        expect(createUserButton.getEnabled()).to.equal(false);
      });
    });

    it("Should attach users", async () => {
      prepareTestEnvironment({ DWC_DUMMY_SPACE_PERMISSIONS: true });
      const spaceDetails: SpaceDetailsPageObject = await cy.pageobject(
        "shellMainContent---managespacesComponent---spaceDetails",
        SpaceDetailsPageObject
      );
      await spaceDetails.isLoaded();
      openAddUsersDialog();
      submitUserSelectionDialog(true, [0, 2]);
      cy.wait(["@getScopedRoles"]);
      cy.byId(
        "shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--RoleEditorDialog--dialog--view--availableScopedRoles"
      ).then((rolesList: sap.m.List) => {
        const customListItems = rolesList.getItems() as sap.m.CustomListItem[];
        customListItems.forEach((item: sap.m.CustomListItem) => {
          const checkbox = item.getContent()[0] as sap.m.CheckBox;
          checkbox.setSelected(true);
          checkbox.fireSelect({ selected: true });
        });
      });
      cy.get(
        "#shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--RoleEditorDialog--dialog--view--ok"
      ).click();
      cy.get(".sapMMessageToast").should("exist");
    });

    it("Navigate to role editor", async () => {
      prepareTestEnvironment({ DWC_DUMMY_SPACE_PERMISSIONS: true });
      const spaceDetails: SpaceDetailsPageObject = await cy.pageobject(
        "shellMainContent---managespacesComponent---spaceDetails",
        SpaceDetailsPageObject
      );
      await spaceDetails.isLoaded();
      cy.byId("shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--usersTableNew").then(
        (table: sap.m.Table) => {
          const flexBox = (table.getItems() as sap.m.ColumnListItem[])[0].getCells()[1] as sap.m.FlexBox;
          const roleIdLink = (flexBox.getItems()[0] as sap.m.CustomListItem).getContent()[0] as sap.m.Link;
          roleIdLink.firePress();
          cy.url().should("contain", "SCOPE_ROLE_1");
        }
      );
    });

    describe("Edit user", () => {
      it("Unselect all selected roles - Update Button should be disabled", async () => {
        prepareTestEnvironment({ DWC_DUMMY_SPACE_PERMISSIONS: true });
        const spaceDetails: SpaceDetailsPageObject = await cy.pageobject(
          "shellMainContent---managespacesComponent---spaceDetails",
          SpaceDetailsPageObject
        );
        await spaceDetails.isLoaded();
        spaceDetails.selectAttachedUser("MOCKUSER1", true);
        cy.get("#shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--editUsersBtn")
          .scrollIntoView()
          .click();
        cy.byId(
          "shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--RoleEditorDialog--dialog--view--availableScopedRoles"
        ).then((rolesList: sap.m.List) => {
          const customListItems = rolesList.getItems() as sap.m.CustomListItem[];
          customListItems.forEach((item: sap.m.CustomListItem) => {
            const checkbox = item.getContent()[0] as sap.m.CheckBox;
            checkbox.setSelected(false);
            checkbox.fireSelect();
          });
        });
        cy.byId(
          "shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--RoleEditorDialog--dialog--view--ok"
        ).then((updateUserButton) => {
          expect(updateUserButton.getEnabled()).to.equal(false);
        });
      });

      it("Users with simialar roles - Tri state selection", async () => {
        prepareTestEnvironment({ DWC_DUMMY_SPACE_PERMISSIONS: true });
        const spaceDetails: SpaceDetailsPageObject = await cy.pageobject(
          "shellMainContent---managespacesComponent---spaceDetails",
          SpaceDetailsPageObject
        );
        await spaceDetails.isLoaded();
        spaceDetails.selectAttachedUser("MOCKUSER1", true);
        spaceDetails.selectAttachedUser("MOCKUSER2", true);
        cy.get("#shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--editUsersBtn")
          .scrollIntoView()
          .click();
        cy.byId(
          "shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--RoleEditorDialog--dialog--view--availableScopedRoles"
        ).then((rolesList: sap.m.List) => {
          const customListItems = rolesList.getItems() as sap.m.CustomListItem[];
          const roleCheckbox1: any = customListItems[0].getContent()[0];
          expect(roleCheckbox1.getSelected()).to.equal(true);
          expect(roleCheckbox1.getPartiallySelected()).to.equal(true);
          const roleCheckbox2: any = customListItems[2].getContent()[0];
          expect(roleCheckbox1.getSelected()).to.equal(true);
          expect(roleCheckbox2.getPartiallySelected()).to.equal(true);
        });
      });

      it("Updated user to have new role", async () => {
        prepareTestEnvironment({ DWC_DUMMY_SPACE_PERMISSIONS: true });
        const spaceDetails: SpaceDetailsPageObject = await cy.pageobject(
          "shellMainContent---managespacesComponent---spaceDetails",
          SpaceDetailsPageObject
        );
        await spaceDetails.isLoaded();
        cy.byId("shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--usersTableNew").then(
          (table: sap.m.Table) => {
            const flexBox = (table.getItems() as sap.m.ColumnListItem[])[0].getCells()[1] as sap.m.FlexBox;
            const numberOfRoles = flexBox.getItems().length;
            expect(numberOfRoles).to.equal(2);
          }
        );
        spaceDetails.selectAttachedUser("MOCKUSER1", true);
        cy.get("#shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--editUsersBtn")
          .scrollIntoView()
          .click();
        cy.byId(
          "shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--RoleEditorDialog--dialog--view--availableScopedRoles"
        ).then((rolesList: sap.m.List) => {
          const customListItems = rolesList.getItems() as sap.m.CustomListItem[];
          const checkbox = customListItems[3].getContent()[0] as sap.m.CheckBox;
          checkbox.setSelected(true);
          checkbox.fireSelect({ selected: true });
        });
        cy.get(
          "#shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--RoleEditorDialog--dialog--view--ok"
        ).click();
        await spaceDetails.isLoaded();
        cy.get(".sapMMessageToast").should("exist");
      });

      it("Remove user from selected role list section", async () => {
        prepareTestEnvironment({ DWC_DUMMY_SPACE_PERMISSIONS: true });
        const spaceDetails: SpaceDetailsPageObject = await cy.pageobject(
          "shellMainContent---managespacesComponent---spaceDetails",
          SpaceDetailsPageObject
        );
        await spaceDetails.isLoaded();
        cy.byId("shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--usersTableNew").then(
          (table: sap.m.Table) => {
            const flexBox = (table.getItems() as sap.m.ColumnListItem[])[0].getCells()[1] as sap.m.FlexBox;
            const numberOfRoles = flexBox.getItems().length;
            expect(numberOfRoles).to.equal(2);
          }
        );
        spaceDetails.selectAttachedUser("MOCKUSER1", true);
        cy.get("#shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--editUsersBtn")
          .scrollIntoView()
          .click();
        cy.get(
          "#shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--RoleEditorDialog--dialog--view--selecteditemRoleList-shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--RoleEditorDialog--dialog--view--selectedScopedRoles-0-imgDel-inner"
        ).click();
        cy.get(
          "#shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--RoleEditorDialog--dialog--view--ok"
        ).click();
        cy.get(".sapMMessageToast").should("exist");
      });
    });
  });

  function openAddUsersDialog() {
    cy.get("#shellMainContent---managespacesComponent---spaceDetails--UserDetailsView--addUsersBtn")
      .scrollIntoView()
      .click();
    cy.get("#UserListDialog-list").should("exist");
  }

  function submitUserSelectionDialog(isSubmit: boolean, indicesToSelect: number[] = []): void {
    cy.get("#UserListDialog-list").should("exist");

    cy.byId("UserListDialog-ok").then((button: sap.m.Button) => {
      expect(button.getEnabled()).to.equal(false);
    });

    cy.byId("UserListDialog-list").then((userList: sap.m.List) => {
      for (let i: number = 0; i < indicesToSelect.length; i++) {
        const itemToSelect = userList.getItems()[indicesToSelect[i]] as sap.m.StandardListItem;
        itemToSelect.setSelected(true);
        userList.fireSelect();
      }
    });

    if (indicesToSelect && indicesToSelect.length > 0) {
      cy.byId("UserListDialog-ok").then((button: sap.m.Button) => {
        expect(button.getEnabled()).to.equal(true);
      });
    }

    if (isSubmit) {
      cy.get("#UserListDialog-ok").click();
    } else {
      cy.get("#UserListDialog-cancel").click();
    }
    cy.get("#UserListDialog-list").should("not.exist");
  }
});
