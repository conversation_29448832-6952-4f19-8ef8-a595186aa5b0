/**
 * Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { MAX_LENGTH_SPACEID, MAX_LENGTH_SPACENAME } from "../../../src/components/managespaces/utility/Constants";
import * as ID from "./_util/IDs";
import { TestNavigation, TestSpace, initTest, waitNotBusy } from "./_util/SpacesTestUtil";
describe("Managespaces - Create New Space", () => {
  const openSpaceCreation = () => {
    cy.get("#" + ID.CREATE_SPACE_BUTTON).click();
    cy.get("#" + ID.CREATE_SPACE_DIALOG).should("exist");
    waitNotBusy();
    cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT)
      .should("exist")
      .should("be.visible");
  };

  describe("Create", () => {
    [
      {
        describeName: "SDP",
        featureflags: {
          DWCO_INFRA_SPACE_PERMISSIONS: true,
          INFRA_SCOPE_DEPENDENT_ROLES: true,
          INFRA_DWC_TWO_TENANT_MODE: true,
          DWCO_SPACES_LOGICAL_DELETE: false,
        },
      },
      {
        describeName: "Non-SDP",
        featureflags: {
          DWCO_INFRA_SPACE_PERMISSIONS: false,
          INFRA_SCOPE_DEPENDENT_ROLES: false,
          DWCO_SPACES_LOGICAL_DELETE: false,
        },
      },
    ].forEach(({ describeName, featureflags }) => {
      describe(describeName, () => {
        describe("From homepage", () => {
          it("Dialog opens", () => {
            initTest({ navTo: TestNavigation.HOME, mockCall: { saveSpace: true } }, featureflags);
            cy.get("#" + ID.HOME_CREATE_SPACE_BUTTON).click();
            cy.get("#" + ID.INSUFFICIENT_STORAGE_POPOVER).should("not.exist");
          });
          it("Insufficient storage notification", () => {
            initTest(
              {
                navTo: TestNavigation.HOME,
                mockCall: { saveSpace: true },
                is: { storageFull: true },
              },
              featureflags
            );
            cy.get("#" + ID.HOME_CREATE_SPACE_BUTTON).click();
            waitNotBusy();
            cy.get("#" + ID.INSUFFICIENT_STORAGE_POPOVER).should("exist");
          });
        });

        describe("From header", () => {
          it("Insufficient storage notification", () => {
            initTest(
              { mockCall: { saveSpace: true }, is: { storageFull: true, runtimeAvailable: true } },
              featureflags
            );
            cy.get("#" + ID.CREATE_SPACE_BUTTON).click();
            cy.get("#" + ID.INSUFFICIENT_STORAGE_POPOVER).should("exist");
          });
        });
      });
    });
  });

  describe("Name Validation", () => {
    beforeEach(() => {
      initTest(
        { mockCall: { saveSpace: true } },
        {
          DWCO_INFRA_SPACE_PERMISSIONS: true,
          INFRA_SCOPE_DEPENDENT_ROLES: true,
          INFRA_DWC_TWO_TENANT_MODE: true,
          DWCO_SPACES_LOGICAL_DELETE: false,
        }
      );
      openSpaceCreation();
    });
    it("Combined test cases for CreateSpace dialog", () => {
      cy.log("Changing spaceId does autoupdate it after from spacename");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_NAME_INPUT)
        .focus()
        .type("test");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT)
        .clear()
        .focus()
        .type("newValue");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_NAME_INPUT)
        .focus()
        .clear()
        .type("yetAnotherNewValue");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT)
        .invoke("val")
        .should("contain", "YETANOTHERNEWVALUE");

      cy.log("Character limits exceeded");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_NAME_INPUT)
        .focus()
        .clear()
        .type("A".repeat(MAX_LENGTH_SPACENAME * 2))
        .invoke("val")
        .should("have.length", MAX_LENGTH_SPACENAME);
      cy.get("#" + ID.CREATE_SPACE_DIALOG_NAME_INPUT_VALUESTATE_MESSAGE)
        .should("exist")
        .should("have.class", "sapMValueStateMessageError");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT_VALUESTATE_MESSAGE)
        .should("exist")
        .should("have.class", "sapMValueStateMessageError");

      cy.log("Character limits not reached");
      const limit = MAX_LENGTH_SPACEID - 1;
      cy.get("#" + ID.CREATE_SPACE_DIALOG_NAME_INPUT)
        .focus()
        .clear()
        .type("A".repeat(limit))
        .invoke("val")
        .should("have.length", limit);
      cy.get("#" + ID.CREATE_SPACE_DIALOG_NAME_INPUT_VALUESTATE_MESSAGE).should("not.exist");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_NAME_INPUT)
        .focus()
        .invoke("val")
        .should("have.length", limit)
        .should("contain", "A".repeat(limit));
      cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT_VALUESTATE_MESSAGE).should("not.exist");

      cy.log("validate empty create dialog");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT)
        .focus()
        .clear();
      cy.get("#" + ID.CREATE_SPACE_DIALOG_NAME_INPUT)
        .focus()
        .clear();
      cy.get("#" + ID.CREATE_SPACE_DIALOG_CREATE_BUTTON).click();
      cy.get("#" + ID.CREATE_SPACE_DIALOG_NAME_INPUT_VALUESTATE_MESSAGE)
        .should("exist")
        .should("have.class", "sapMValueStateMessageError")
        .should("contain", "spaceNameEmptyValidationError");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT_VALUESTATE_MESSAGE)
        .should("exist")
        .should("have.class", "sapMValueStateMessageError")
        .should("contain", "spaceIDInputEmptyValidationError");

      cy.log("validate invalid space id");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT)
        .clear()
        .focus()
        .type("$$$");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT_VALUESTATE_MESSAGE)
        .should("exist")
        .should("have.class", "sapMValueStateMessageError")
        .should("contain", "spaceIDInputInValidError");

      cy.log("validate duplicate space id");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT)
        .clear()
        .focus()
        .type(TestSpace.SPACE_0);
      cy.get("#" + ID.CREATE_SPACE_DIALOG_CREATE_BUTTON).click();
      cy.get("#" + ID.CREATE_SPACE_DIALOG_NAME_INPUT_VALUESTATE_MESSAGE)
        .should("exist")
        .should("have.class", "sapMValueStateMessageError")
        .should("contain", "Error");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT_VALUESTATE_MESSAGE)
        .should("exist")
        .should("have.class", "sapMValueStateMessageError")
        .should("contain", "spaceIDInputDuplicateError");

      cy.log("validate invalid keyword");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT)
        .clear()
        .focus()
        .type("CREATE");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT_VALUESTATE_MESSAGE)
        .should("exist")
        .should("have.class", "sapMValueStateMessageError")
        .should("contain", "nameValidationReservedKeyword");

      cy.log("should warn for prefix DWC_");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT)
        .clear()
        .focus()
        .type("DWC_");
      cy.get("#" + ID.CREATE_SPACE_DIALOG_ID_INPUT_VALUESTATE_MESSAGE)
        .should("exist")
        .should("have.class", "sapMValueStateMessageWarning")
        .should("contain", "nameValidationWarningReservedPrefix");
    });
  });
});
