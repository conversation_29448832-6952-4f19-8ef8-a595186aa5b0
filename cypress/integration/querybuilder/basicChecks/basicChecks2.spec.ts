/**
 * Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { it } from "mocha";
import { AttributeDetails } from "../helpers/attributes/attributeDetails";
import { DimensionsOutput } from "../helpers/output/dimensionsOutput";
import { Output } from "../helpers/output/output";
import { Setup } from "../helpers/setup/setup";
import { Diagram } from "../helpers/sources/diagram";
import { NodeProperties } from "../helpers/sources/nodeProperties";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    Setup.setupBeforeEach();
    Setup.openQueryModel("TEST_QUERY_MODEL", "TESTS_SPACE_1", {
      repositoryFixture: "querybuilder/loadSourcesFromRepository",
      loadDataEntityDetailsFixture: "querybuilder/loadAssociations",
      designObjectsIds: "querybuilder/loadQueryModel",
    });

    Output.expectModelPropertiesToBeReady();
    Diagram.assertNumberOfNodesInModel(1); // Start of with 1 node in the model
  });

  it("Fullscreen is maintained when changing properties panel", () => {
    DimensionsOutput.openAttribute(0);
    AttributeDetails.breadcrumbSelectNavBack(true);
    cy.get(`button[id*=ModelProperties--toggleFullScreen]`).should("have.attr", "aria-label", "Icon.exit-full-screen");
  });

  it("Should remove leading/trilling whitespace from Node's Business Name", () => {
    Diagram.openSourceNodeUsingDiagram({ name: "B4 Opportunities" });
    cy.get(`[id*=selectedNodeBusinessName]`).should("be.visible");
    NodeProperties.changeAliasOSource("      WHITESPACE DIMENSION    ");
    cy.get(`[id*=selectedNodeBusinessName]`).find("input").should("have.value", "WHITESPACE DIMENSION");
  });
});
