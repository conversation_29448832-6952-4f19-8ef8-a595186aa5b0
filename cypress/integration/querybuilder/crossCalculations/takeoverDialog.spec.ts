/**
 * Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { it } from "mocha";
import { FactTypesForNewModel, Setup } from "../helpers/setup/setup";

describe(Cypress.spec.relative, () => {
  const BASE_PATH = "querybuilder/crossCalculations/stacking/takeover";

  beforeEach(() => {
    Setup.setupBeforeEach(
      {
        DWCO_MODELING_ANALYTIC_MODEL_STACKING: true,
        DWCO_MODELING_AM_MULTI_STRUCTURE: true,
        DWCO_MODELING_AM_NEW_DIM_HANDLING: true,
      },
      true
    );
  });

  it("should display the variables accordingly to the added measures and cross calculations", () => {
    Setup.createNewModel(
      {
        loadDataEntityDetails: BASE_PATH + "/loadDataEntityDetails",
        designObjectFolder: BASE_PATH + "/designObjectFolderIds",
        designObjectId: BASE_PATH + "/designObjectsIds",
        allAnalyticModels: BASE_PATH + "/allAnalyticModels",
      },
      false,
      { factType: FactTypesForNewModel.ANALYTIC_MODEL, name: "L1_AM_Cross_Calculation_with_Variables" }
    );
    Setup.Assert.variablesTableRowCountIs(6);
    Setup.clickOnAddAllMeasuresOnTakeoverDialog();
    Setup.Assert.variablesTableRowCountIs(4);
    Setup.clickOnAddAllCrossCalculationsOnTakeoverDialog();
    Setup.Assert.variablesTableRowCountIs(0);
    Setup.clickOnAddAllCrossCalculationsOnTakeoverDialog();
    Setup.Assert.variablesTableRowCountIs(4);
    Setup.clickOnAddAllMeasuresOnTakeoverDialog();
    Setup.Assert.variablesTableRowCountIs(6);
  });
});
