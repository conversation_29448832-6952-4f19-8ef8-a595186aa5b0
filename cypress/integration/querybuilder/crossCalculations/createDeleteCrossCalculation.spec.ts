/** @format */

import { QB_OUTPUT_OVERVIEW_PAGE } from "../../../pageobjects/querybuilder/ComponentsDescriptor";
import { CrossCalculationDetails } from "../helpers/crossCalculations/crossCalculationDetails";
import { CrossCalculationOutput, CrossCalculationType } from "../helpers/output/crossCalculationOutput";
import { Output } from "../helpers/output/output";
import { Setup } from "../helpers/setup/setup";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    Setup.setupBeforeEach({
      DWCO_MODELING_AM_MULTI_STRUCTURE: true,
    });
    Setup.openQueryModel("AM_Multi_Structure", "TESTS_SPACE_1", {
      loadDataEntityDetailsFixture: "querybuilder/crossCalculations/loadDataEntityDetails",
      designObjectsFolderIds: "querybuilder/crossCalculations/designObjectsFolderIds",
      designObjectsIds: "querybuilder/crossCalculations/designObjectsIds",
    });
    Output.expectModelPropertiesToBeReady();
  });

  it("should create a Calculated Cross Calculation and delete it", () => {
    CrossCalculationOutput.createCrossCalculation(CrossCalculationType.CALCULATED);
    CrossCalculationDetails.breadcrumbSelectNavBack();
    cy.get(`#${QB_OUTPUT_OVERVIEW_PAGE.OUTPUT_CROSS_CALCULATION_LIST} li`).should("have.length", 1);
    CrossCalculationOutput.deleteCrossCalculation(0);
    cy.get(`#${QB_OUTPUT_OVERVIEW_PAGE.OUTPUT_CROSS_CALCULATION_LIST}-nodata-text`).should("exist");
  });

  it("should create Restricted Cross Calculation and delete it", () => {
    CrossCalculationOutput.createCrossCalculation(CrossCalculationType.RESTRICTED);
    CrossCalculationDetails.breadcrumbSelectNavBack();
    cy.get(`#${QB_OUTPUT_OVERVIEW_PAGE.OUTPUT_CROSS_CALCULATION_LIST} li`).should("have.length", 1);
    CrossCalculationOutput.deleteCrossCalculation(0);
    cy.get(`#${QB_OUTPUT_OVERVIEW_PAGE.OUTPUT_CROSS_CALCULATION_LIST}-nodata-text`).should("exist");
  });
});
