/** @format */

import { CrossCalculationDetails } from "../helpers/crossCalculations/crossCalculationDetails";
import { ExceptionAggregationType } from "../helpers/crossCalculations/exceptionAggregation";
import { CrossCalculationOutput } from "../helpers/output/crossCalculationOutput";
import { Output } from "../helpers/output/output";
import { Setup } from "../helpers/setup/setup";
import { Diagram } from "../helpers/sources/diagram";

describe(Cypress.spec.relative, () => {
  const BASE_PATH = "querybuilder/crossCalculations/withRestrictedAndCalculated";

  beforeEach(() => {
    Setup.setupBeforeEach({
      DWCO_MODELING_AM_MULTI_STRUCTURE: true,
    });
    Setup.openQueryModel("Sales_Status_Multi_Structure", "TESTS_SPACE_1", {
      loadDataEntityDetailsFixture: BASE_PATH + "/loadDataEntityDetails",
      designObjectsIds: BASE_PATH + "/designObjectsIds",
      designObjectsFolderIds: BASE_PATH + "/designObjectsFolderIds",
    });
    Output.expectModelPropertiesToBeReady();
  });

  it("should not be able to set exception aggregation for restricted cross calculation", () => {
    CrossCalculationOutput.openCrossCalculation(1);
    CrossCalculationDetails.Assertions.exceptionAggregationPanelIsHidden();
  });

  it("should be able to set exception aggregation for calculated cross calculation", () => {
    CrossCalculationOutput.openCrossCalculation(0);
    CrossCalculationDetails.Assertions.exceptionAggregationPanelIsVisible();
  });

  it("should show error message when no dimension are selected", () => {
    CrossCalculationOutput.openCrossCalculation(0);
    CrossCalculationDetails.ExceptionAggregation.changeExceptionAggregation(ExceptionAggregationType.SUM);
    CrossCalculationDetails.Assertions.exceptionAggregationAttributesHasError();
  });

  it("should show error message when selected dimensions are missing", () => {
    CrossCalculationOutput.openCrossCalculation(0);
    CrossCalculationDetails.ExceptionAggregation.changeExceptionAggregation(ExceptionAggregationType.SUM);
    CrossCalculationDetails.ExceptionAggregation.changeExceptionAggregationAttributes([0, 3]);
    Diagram.openSourceNodeUsingDiagram({ name: "Opportunities BizName" });
    Diagram.deleteSelectedNode();
    CrossCalculationOutput.openCrossCalculation(0);
    CrossCalculationDetails.Assertions.exceptionAggregationAttributesHasError();
    CrossCalculationDetails.Assertions.exceptionAggregationAttributesListHas([
      "Opportunity (diagramFactSourceNode)",
      "Status_Opportunity",
    ]);
  });
});
