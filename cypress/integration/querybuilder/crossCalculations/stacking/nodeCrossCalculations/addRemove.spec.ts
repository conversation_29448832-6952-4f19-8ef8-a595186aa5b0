/** @format */

import { CrossCalculationDetails } from "../../../helpers/crossCalculations/crossCalculationDetails";
import { CrossCalculationOutput, CrossCalculationType } from "../../../helpers/output/crossCalculationOutput";
import { Output } from "../../../helpers/output/output";
import { Setup } from "../../../helpers/setup/setup";
import { CHECKBOX_STATE } from "../../../helpers/sources/assertions/NodeCrossCalculationsAssertions";
import { Diagram } from "../../../helpers/sources/diagram";
import { NodeCrossCalculations } from "../../../helpers/sources/NodeCrossCalculations";
import { NodeProperties } from "../../../helpers/sources/nodeProperties";
describe(Cypress.spec.relative, () => {
  const BASE_PATH = "querybuilder/crossCalculations/stacking/simple";

  beforeEach(() => {
    Setup.setupBeforeEach({
      DWCO_MODELING_AM_MULTI_STRUCTURE: true,
    });
    Setup.openQueryModel("L1_AM_Measures_and_Cross_Calculations", "TESTS_SPACE_1", {
      loadDataEntityDetailsFixture: BASE_PATH + "/loadDataEntityDetails",
      designObjectsFolderIds: BASE_PATH + "/designObjectsFolderIds",
      designObjectsIds: BASE_PATH + "/designObjectsIds",
    });
    Output.expectModelPropertiesToBeReady();
  });

  it("should remove one cross calculation through node list", () => {
    Diagram.openSourceNodeUsingDiagram({ name: "Base AM Measures and Cross Calculations" });
    NodeCrossCalculations.Assert.checkboxStateIs(CHECKBOX_STATE.CHECKED);
    NodeCrossCalculations.clickOnCrossCalculation(0);
    NodeCrossCalculations.Assert.checkboxStateIs(CHECKBOX_STATE.PARTIALLY_CHECKED);
    NodeProperties.breadcrumbSelectNavBack();
    CrossCalculationOutput.Assert.crossCalculationsListSizeIs(1);
  });

  it("should add one cross calculation through node list", () => {
    CrossCalculationOutput.deleteCrossCalculation(0);
    CrossCalculationOutput.Assert.crossCalculationsListSizeIs(1);
    Diagram.openSourceNodeUsingDiagram({ name: "Base AM Measures and Cross Calculations" });
    NodeCrossCalculations.Assert.checkboxStateIs(CHECKBOX_STATE.PARTIALLY_CHECKED);
    NodeCrossCalculations.clickOnCrossCalculation(0);
    NodeCrossCalculations.Assert.checkboxStateIs(CHECKBOX_STATE.CHECKED);
    NodeProperties.breadcrumbSelectNavBack();
    CrossCalculationOutput.Assert.crossCalculationsListSizeIs(2);
  });

  it("should remove/add all cross calculations through node list", () => {
    // Remove all
    Diagram.openSourceNodeUsingDiagram({ name: "Base AM Measures and Cross Calculations" });
    NodeCrossCalculations.clickOnSelectAllCheckbox();
    NodeProperties.breadcrumbSelectNavBack();
    CrossCalculationOutput.Assert.crossCalculationsListSizeIs(0);
    // Add all
    Diagram.openSourceNodeUsingDiagram({ name: "Base AM Measures and Cross Calculations" });
    NodeCrossCalculations.clickOnSelectAllCheckbox();
    NodeProperties.breadcrumbSelectNavBack();
    CrossCalculationOutput.Assert.crossCalculationsListSizeIs(2);
  });

  it("should add cross calculations with unique id if a cross calculation with the same id already exists", () => {
    CrossCalculationOutput.deleteCrossCalculation(0);
    CrossCalculationOutput.deleteCrossCalculation(0);
    CrossCalculationOutput.createCrossCalculation(CrossCalculationType.CALCULATED);
    CrossCalculationDetails.breadcrumbSelectNavBack();
    CrossCalculationOutput.createCrossCalculation(CrossCalculationType.RESTRICTED);
    Diagram.openSourceNodeUsingDiagram({ name: "Base AM Measures and Cross Calculations" });
    NodeCrossCalculations.Assert.checkboxStateIs(CHECKBOX_STATE.UNCHECKED);
    NodeCrossCalculations.clickOnCrossCalculation(0);
    NodeCrossCalculations.Assert.checkboxStateIs(CHECKBOX_STATE.PARTIALLY_CHECKED);
    NodeCrossCalculations.clickOnSelectAllCheckbox();
    NodeCrossCalculations.Assert.checkboxStateIs(CHECKBOX_STATE.CHECKED);
    NodeProperties.breadcrumbSelectNavBack();
    CrossCalculationOutput.Assert.crossCalculationsListSizeIs(4);
  });
});
