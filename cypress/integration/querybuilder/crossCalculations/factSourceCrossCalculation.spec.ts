/** @format */

import { CrossCalculationDetails } from "../helpers/crossCalculations/crossCalculationDetails";
import { CrossCalculationOutput } from "../helpers/output/crossCalculationOutput";
import { Output } from "../helpers/output/output";
import { Setup } from "../helpers/setup/setup";

describe(Cypress.spec.relative, () => {
  const BASE_PATH = "querybuilder/crossCalculations/stacking/simple";

  beforeEach(() => {
    Setup.setupBeforeEach({
      DWCO_MODELING_AM_MULTI_STRUCTURE: true,
    });
    Setup.openQueryModel("L1_AM_Measures_and_Cross_Calculations", "TESTS_SPACE_1", {
      loadDataEntityDetailsFixture: BASE_PATH + "/loadDataEntityDetails",
      designObjectsFolderIds: BASE_PATH + "/designObjectsFolderIds",
      designObjectsIds: BASE_PATH + "/designObjectsIds",
    });
    Output.expectModelPropertiesToBeReady();
  });

  it("should see source of the Fact Source Cross Calculation", () => {
    CrossCalculationOutput.openCrossCalculation(0);
    CrossCalculationDetails.Assertions.sourceNameIs("Base AM Measures and Cross Calculations");
    CrossCalculationDetails.Assertions.sourceLinkIs(
      "#/databuilder&/db/TESTS_SPACE_1/Base_AM_Measures_and_Cross_Calculations"
    );
    CrossCalculationDetails.Assertions.sourceFieldIs("Calculated Cross Calculation");
  });

  it("should update technical and business name of a Fact Source Cross Calculations", () => {
    CrossCalculationOutput.openCrossCalculation(0);
    CrossCalculationDetails.changeTechnicalName("NEW_NAME");
    CrossCalculationDetails.changeBusinessName("NEW_NAME");
    CrossCalculationDetails.breadcrumbSelectNavBack();
    CrossCalculationOutput.openCrossCalculation(0);
    CrossCalculationDetails.Assertions.technicalNameIs("NEW_NAME");
    CrossCalculationDetails.Assertions.businessNameIs("NEW_NAME");
    CrossCalculationDetails.Assertions.sourceFieldIs("Calculated Cross Calculation");
  });
});
