/** @format */

import { Output } from "../helpers/output/output";
import { Setup } from "../helpers/setup/setup";
import { Diagram } from "../helpers/sources/diagram";
import { Validations } from "../helpers/validations/validations";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    Setup.setupBeforeEach(
      {
        DWCO_MODELING_AM_REPLACE_SOURCES: true,
        DWCO_MODELING_AM_NEW_DIM_HANDLING: true,
      },
      true
    );
    Setup.mockRepoRequests({
      all: "querybuilder/allAM",
      allTables: "querybuilder/replaceFactSource/allAMTablesReplace",
      allViews: "querybuilder/allAMViews",
    });
    // open existing model
    Setup.openQueryModel("Cypress_Opportunity_Items", "TESTS_SPACE_1", {
      loadDataEntityDetailsFixture: "querybuilder/replaceFactSource/loadDataEntityDetailsOriginalForReplace",
      designObjectsFolderIds: "querybuilder/designObjectsFoldersOppIt",
      designObjectsIds: "querybuilder/replaceFactSource/designObjectsIdsReplace",
    });
    Output.expectModelPropertiesToBeReady();
  });

  it("Replace a fact source and check if the repair the dimension source button is not visible", () => {
    // replace fact source
    Diagram.openReplaceFactSourceDialog("MCT_OpportunityItems_Rep", "TESTS_SPACE_1", {
      loadDataEntityDetails: "querybuilder/replaceFactSource/loadDataEntityDetailsReplace",
    });
    // check verification table in replace fact source dialog
    cy.get(`[id*=replaceSource--replacementVerificationTable-table] > tbody`).as("replaceVerificationTable");
    cy.get("@replaceVerificationTable")
      .find("tr")
      .eq(2)
      .find("td")
      .eq(3)
      .find("div")
      .find("div")
      .find("div")
      .find("div")
      .find("span")
      .find("span")
      .should("have.text", "_MCT_Produ;");
    cy.get("@replaceVerificationTable")
      .find("tr")
      .eq(3)
      .find("td")
      .eq(3)
      .find("div")
      .find("div")
      .find("div")
      .find("div")
      .find("span")
      .find("span")
      .should("have.text", "MCT_Products;");
    // click replace button in replace fact source confirmation dialog
    Diagram.confirmReplaceFactSource();
    // check error message appeared
    Validations.checkSingleValidationError("validationMessageAssociationDoesNotExist(Product ID)");
    // click on the node diagram
    Diagram.openSourceNodeUsingDiagram({ name: "MCT Products" }, 0);
    // check if the repair command is not available
    cy.get(`#sap-cdw-analyticmodel-RepairCommand`).should("not.exist");
  });

  it("should try to replace a stacked model and check the message toast saying it is not allowed", () => {
    Diagram.findAndReplaceObject("protectExportTest_Stacked_1st");
    cy.get(".sapMMessageToast").should("exist").should("have.text", "replaceForStackedFactSourceNotSupported");
  });
});
