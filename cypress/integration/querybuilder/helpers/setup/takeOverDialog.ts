/** @format */

import * as QueryBuilderDescriptor from "../../../../pageobjects/querybuilder/ComponentsDescriptor";
import { Common } from "../common";

export class TakeOverDialog {
  public static clickOnActionType(rowNumber: number, columnNumber: number) {
    cy.get(
      `#${QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_PARAMETER_LIST}-rows-row${rowNumber}-col${columnNumber} [id*=${QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_IMPORT_ACTION_SELECT}] span[id*=-arrow]`
    ).click();
  }

  public static clickOnFilterType(rowNumber: number, columnNumber: number) {
    cy.get(
      `#${QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_PARAMETER_LIST}-rows-row${rowNumber}-col${columnNumber} [id*=${QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_IMPORT_FILTER_TYPE}] span[id*=-arrow]`
    ).click();
  }

  public static assertActionTypeSelectLength(length: number) {
    cy.get(`.sapMPopover.sapUiUserSelectable [id*=TakeOverDialog--actionSelect] ul li`, {
      cyOriginal: true,
    }).should("have.length", length);
  }

  public static assertActionTypeEnable(rowNumber: number, columnNumber: number) {
    cy.get(
      `#${QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_PARAMETER_LIST}-rows-row${rowNumber}-col${columnNumber} [id*=${QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_IMPORT_ACTION_SELECT}]`
    ).should("not.have.attr", "aria-hidden", "true");
  }

  public static setActionType(index: number) {
    cy.get(`[id*=${QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_IMPORT_ACTION_SELECT}] ul`)
      .find("li")
      .eq(index)
      .click({ force: true });
  }

  public static setFilterType(index: number) {
    cy.get(`[id*=${QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_IMPORT_FILTER_TYPE}] ul`)
      .find("li")
      .eq(index)
      .click({ force: true });
  }

  public static setRangeValueInput(
    rowNumber: number,
    columnNumber: number,
    valueIndex: number,
    value: string,
    high: boolean = false
  ) {
    if (high) {
      cy.get(
        `#${
          QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_PARAMETER_LIST
        }-rows-row${rowNumber}-col${columnNumber} [id*=multiRangeList-listUl] li:nth-child(${valueIndex + 1}) [id*=${
          QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_IMPORT_MULTI_RANGE
        }] input[id*=inputHigh]`
      ).type(`{selectAll}${value}{enter}`);
    } else {
      cy.get(
        `#${
          QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_PARAMETER_LIST
        }-rows-row${rowNumber}-col${columnNumber} [id*=multiRangeList-listUl] li:nth-child(${valueIndex + 1}) [id*=${
          QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_IMPORT_MULTI_RANGE
        }] input[id*=inputLow]`
      ).type(`{selectAll}${value}{enter}`);
    }
  }

  public static changeRangeOption(valueIndex: number) {
    cy.get(
      `.sapMPopover[style*="visibility: visible"] [id*=${"propertyDialogMultipleRange"}][id*=valueStateText] li:nth-child(${
        valueIndex + 1
      })`,
      Common.original
    ).click({
      force: true,
    });
  }

  public static expandRangeOption(rowNumber: number, columnNumber: number, valueIndex: number) {
    cy.get(
      `#${
        QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_PARAMETER_LIST
      }-rows-row${rowNumber}-col${columnNumber} [id*=multiRangeList-listUl] li:nth-child(${valueIndex + 1}) [id*=arrow]`
    ).click();
  }

  public static addNewValueForMultiValueOrRange(rowNumber: number, columnNumber: number) {
    cy.get(
      `#${QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_PARAMETER_LIST}-rows-row${rowNumber}-col${columnNumber} a[id*=addInput]`
    ).click();
  }

  public static deleteValueForMultiValueOrRange(index: number) {
    cy.get(`[id*=multiRangeList-listUl] li:nth-child(${index + 1}) button[id*=imgDel]`).click();
  }

  public static assertFilterIsNotApplicable(rowNumber: number, columnNumber: number) {
    cy.get(
      `#${QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_PARAMETER_LIST}-rows-row${rowNumber}-col${columnNumber} div[id*=-hiddenSelect]`,
      Common.original
    ).should("include.text", "NOT_APPLICABLE");
  }

  public static assertRangeOptionValue(rowNumber: number, columnNumber: number, rangeOption: string) {
    cy.get(
      `#${QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_PARAMETER_LIST}-rows-row${rowNumber}-col${columnNumber} [id*=multiRangeList-listUl] li:nth-child(1) [id*=variableDefaultRangeOption][id*=labelText]`
    ).should("contain", rangeOption, Common.original);
  }

  public static openValueHelpForSingleValue(rowNumber: number, columnNumber: number) {
    cy.get(
      `#${QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_PARAMETER_LIST}-rows-row${rowNumber}-col${columnNumber} div[id*=--customInput]`
    ).within(() => {
      cy.get(`[id*=customInput-vhi]`).should("exist");
      cy.get(`[id*=customInput-vhi]`).click({ force: true });
    });
  }

  public static openValueHelpForIntervalValue(
    rowNumber: number,
    columnNumber: number,
    index: number,
    high: boolean = false
  ) {
    const inputType = high ? "inputHigh" : "inputLow";
    cy.get(
      `#${
        QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_PARAMETER_LIST
      }-rows-row${rowNumber}-col${columnNumber} [id*=multiRangeList-listUl] li:nth-child(${index + 1}) [id*=${
        QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_IMPORT_INTERVAL
      }] div[id*=--${inputType}] div[id*=--customInput]`
    ).within(() => {
      cy.get(`div [id*=customInput-vhi]`).should("exist").click({ force: true });
    });
  }

  public static openValueHelpForRangeValue(
    rowNumber: number,
    columnNumber: number,
    index: number,
    high: boolean = false
  ) {
    const inputType = high ? "inputHigh" : "inputLow";
    cy.get(
      `#${
        QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_PARAMETER_LIST
      }-rows-row${rowNumber}-col${columnNumber} [id*=multiRangeList-listUl] li:nth-child(${index + 1}) [id*=${
        QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_IMPORT_MULTI_RANGE
      }] div[id*=--${inputType}] div[id*=--customInput]`
    ).within(() => {
      cy.get(`div [id*=customInput-vhi]`).should("exist").click({ force: true });
    });
  }

  public static assertRangeInputValue(
    rowNumber: number,
    columnNumber: number,
    index: number,
    value: string,
    high: boolean = false
  ) {
    const inputType = high ? "inputHigh" : "inputLow";
    cy.get(
      `#${
        QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_PARAMETER_LIST
      }-rows-row${rowNumber}-col${columnNumber} [id*=multiRangeList-listUl] li:nth-child(${index + 1}) [id*=${
        QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_IMPORT_MULTI_RANGE
      }] div[id*=--${inputType}] div[id*=--customInput] div[id*=--customInput-content]`
    ).within(() => {
      cy.get(`input[id*=--customInput-inner]`).should("have.value", value);
    });
  }

  public static assertIntervalInputValue(
    rowNumber: number,
    columnNumber: number,
    index: number,
    value: string,
    high: boolean = false
  ) {
    const inputType = high ? "inputHigh" : "inputLow";
    cy.get(
      `#${
        QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_PARAMETER_LIST
      }-rows-row${rowNumber}-col${columnNumber} [id*=multiRangeList-listUl] li:nth-child(${index + 1}) [id*=${
        QueryBuilderDescriptor.QB_BUTTONS.ASSOCIATION_DIALOG_IMPORT_INTERVAL
      }] div[id*=--${inputType}] div[id*=--customInput] div[id*=--customInput-content]`
    ).within(() => {
      cy.get(`input[id*=--customInput-inner]`).should("have.value", value);
    });
  }
}
