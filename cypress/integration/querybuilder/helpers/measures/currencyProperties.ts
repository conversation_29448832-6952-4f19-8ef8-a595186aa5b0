/** @format */

import {
  QB_DIALOGS,
  QB_INPUTS,
  QB_MESSAGES,
  QB_SELECTS,
  QB_VIEWS,
} from "../../../../pageobjects/querybuilder/ComponentsDescriptor";
import { Common, waitTime } from "../common";

export class ConversionProperties {
  public static openConversionTypeDialog(fixture = "querybuilder/currencyConversion/SAP.CURRENCY.VIEW.TCURV") {
    cy.intercept("POST", `**/dwaas-core/data-access/valuehelp/instant/TESTS_SPACE_1/${"SAP.CURRENCY.VIEW.TCURV"}`, {
      fixture: fixture,
    }).as("getCurrencyConversions");
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_CC_CONVERSION_TYPE}]`, Common.original).click();
    cy.get(`div[id=${QB_DIALOGS.CONVERSION_TYPE_DIALOG}]`, Common.original).should("be.visible");
    cy.wait(waitTime);
  }

  public static searchConversionTypeValue(value, searchFiled = QB_SELECTS.CC_CONVERSION_TYPE_SEARCH_FIELD) {
    cy.get(`[id=${searchFiled}]`, Common.original).type(value);
    cy.wait(waitTime);
  }
  public static clickConversionType(index: number, table = QB_SELECTS.CC_CONVERSION_TYPE_FIXED_VALUE_TBL) {
    cy.get(`#${table} tbody tr:nth-child(${index + 1})`, Common.original).should("be.visible");
    cy.get(`#${table} tbody tr:nth-child(${index + 1})`, Common.original).click();
  }

  public static clickConversionTypeDimension(index: number) {
    cy.get(
      `#${QB_SELECTS.CC_CONVERSION_TYPE_DIMENSION_TBL} tbody tr:nth-child(${index + 1}) td:nth-child(1)`,
      Common.original
    ).click();
  }

  public static selectConversionTypeType(
    index,
    conversionTypeDialog = QB_SELECTS.CC_CONVERSION_TYPE_DIALOG_SELECT_CURRENCY_TYPE
  ) {
    cy.byId(conversionTypeDialog).then((field: sap.m.Select) => {
      field.setSelectedItem(field.getItems()[index]);
      field.fireChange(field.getItems()[index]);
      cy.wait(waitTime);
    });
  }

  public static okConversionTypeDialog() {
    cy.get(`#${QB_SELECTS.CC_CONVERSION_TYPE_DIALOG_SELECT_BUTTON}`, Common.original).should("be.visible").click();
    cy.wait(waitTime);
  }

  public static openReferenceDateDialog() {
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_CC_REFERENCE_DATE}]`, Common.original).click();
    cy.get(`div[id=${QB_DIALOGS.REFERENCE_DATE_DIALOG}]`, Common.original).should("be.visible");
    cy.wait(waitTime);
  }

  public static selectReferenceDateType(index) {
    cy.byId(QB_SELECTS.CC_REFERENCE_DATE_DIALOG_SELECT_CURRENCY_TYPE).then((field: sap.m.Select) => {
      field.setSelectedItem(field.getItems()[index]);
      field.fireChange(field.getItems()[index]);
      cy.wait(waitTime);
    });
  }

  public static okReferenceDateDialog() {
    cy.get(`#${QB_SELECTS.CC_REFERENCE_DATE_DIALOG_SELECT_BUTTON}`, Common.original).click();
    cy.wait(waitTime);
  }

  public static openTargetDialog(fixture = "querybuilder/currencyConversion/SAP.CURRENCY.VIEW.TCURC") {
    const isCurrencyConversion = fixture === "querybuilder/currencyConversion/SAP.CURRENCY.VIEW.TCURC";
    cy.intercept(
      "POST",
      `**/dwaas-core/data-access/valuehelp/instant/TESTS_SPACE_1/${
        isCurrencyConversion ? "SAP.CURRENCY.VIEW.TCURC" : "SAP.UNIT.VIEW.T006A"
      }`,
      {
        fixture: fixture,
      }
    ).as(isCurrencyConversion ? "getCurrencyValues" : "getUnitValues");
    cy.get(
      `[id=${
        isCurrencyConversion ? QB_INPUTS.MEASURE_DETAILS_CC_TARGET_CURRENCY : QB_INPUTS.MEASURE_DETAILS_UC_TARGET_UNIT
      }]`,
      Common.original
    ).click();
    cy.get(
      `div[id=${isCurrencyConversion ? QB_DIALOGS.TARGET_CURRENCY_DIALOG : QB_DIALOGS.TARGET_UNIT_DIALOG}]`,
      Common.original
    ).should("be.visible");
    cy.wait(waitTime);
  }

  public static searchTargetValue(value, targetSearchField = QB_SELECTS.CC_TARGET_CURRENCY_SEARCH_FIELD) {
    cy.get(`[id=${targetSearchField}]`, Common.original).type(value);
    cy.wait(waitTime);
  }

  public static clickTargetConversionValue(
    index: number,
    targetConversionFixedValue = QB_SELECTS.CC_TARGET_CURRENCY_FIXED_VALUE_TBL
  ) {
    cy.get(`#${targetConversionFixedValue} tbody tr:nth-child(${index + 1})`, Common.original).click();
  }

  public static clickTargetUnitFixedValue(index: number) {
    cy.get(
      `#${QB_SELECTS.UC_TARGET_UNIT_FIXED_VALUE_TBL} tbody tr:nth-child(${index + 1}) td:nth-child(1)`,
      Common.original
    ).click();
  }

  public static okTargetConversionDialog(
    targetConversionDialogSelectButton = QB_SELECTS.CC_TARGET_CURRENCY_DIALOG_SELECT_BUTTON
  ) {
    cy.get(`#${targetConversionDialogSelectButton}`, Common.original).click();
    cy.wait(waitTime);
  }

  public static selectTargetConversionType(index, conversionTypeValue) {
    // uppercase values
    cy.get(`[id*=target${conversionTypeValue}--target${conversionTypeValue}Type-arrow]`).click();
    cy.get(`[id*=target${conversionTypeValue}--target${conversionTypeValue}Type-${index}]`).click({ force: true });
    cy.wait(waitTime);
  }
  public static changeVariableTechnicalNameWithoutLeavingInput(name) {
    cy.get(`[id*=sourceVariableTechnicalName-inner]`).type(name);
  }

  public static leaveInput() {
    Common.clickOutside();
    cy.wait(waitTime);
  }

  public static cancelTargetCurrencyDialog() {
    cy.get("[id=targetCurrency--cancel]", Common.original).click();
  }

  public static cancelTargetUnitDialog() {
    cy.get("[id=targetUnit--cancel]", Common.original).click();
  }

  public static cancelConversionTypeDialog() {
    cy.get("[id=conversionType--cancel]", Common.original).click();
  }

  public static selectCurrencyClientOption(index: number) {
    cy.get(`[id*=${QB_VIEWS.MEASURE_DETAILS}--analyticModelClientId-arrow]`).click();
    cy.get(`[id*=${QB_VIEWS.MEASURE_DETAILS}--analyticModelClientId-${index}]`, Common.original).click({ force: true });
  }

  public static emptyTableShouldNotBeVisible() {
    cy.get(`[id=${QB_MESSAGES.CC_VIEWS_EMPTY_MESSAGE}]`, Common.original).should("not.exist");
  }

  public static emptyTableShouldBeVisible() {
    cy.get(`[id=${QB_MESSAGES.CC_VIEWS_EMPTY_MESSAGE}]`, Common.original).should("exist");
  }

  public static conversionTypeIsEmpty() {
    cy.get(`[id*=${QB_INPUTS.MEASURE_DETAILS_CC_CONVERSION_TYPE}]`, Common.original).should("have.value", "");
  }

  public static unitClientSelectOptionsQuantityIs(quantity: number) {
    cy.get(`[id*=${QB_VIEWS.MEASURE_DETAILS}--analyticModelUnitClientId-arrow]`).click();
    cy.get(`[id*=${QB_VIEWS.MEASURE_DETAILS}--analyticModelUnitClientId-] li`, Common.original)
      .its("length")
      .should("eq", quantity);
    cy.get(`[id*=${QB_VIEWS.MEASURE_DETAILS}--analyticModelUnitClientId-arrow]`).click();
  }
}
