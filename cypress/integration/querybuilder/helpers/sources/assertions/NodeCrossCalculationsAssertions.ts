/** @format */

import { QB_QUERY_MODEL_TAB } from "../../../../../pageobjects/querybuilder/ComponentsDescriptor";

export enum CHECKBOX_STATE {
  CHECKED = "true",
  UNCHECKED = "false",
  PARTIALLY_CHECKED = "mixed",
}

export class NodeCrossCalculationsAssertions {
  public static checkboxStateIs(state: CHECKBOX_STATE) {
    cy.get(`[id*=selectAllCrossCalculationsCheckBox]`).should("have.attr", "aria-checked", state);
  }

  public static crossCalculationsListSizeIs(expectedSize: number) {
    cy.get(`[id=${QB_QUERY_MODEL_TAB.NODE_PROPERTIES_PANEL}--availableCrossCalculationsList] li`).should(
      "have.length",
      expectedSize
    );
  }
}
