/** @format */

import {
  QB_BUTTONS,
  QB_EXPRESSION_EDITOR,
  QB_QUERY_MODEL_TAB,
} from "../../../../pageobjects/querybuilder/ComponentsDescriptor";
import { Common, waitTime } from "../common";
import { NodeParametersAssertions } from "./assertions/NodeParametersAssertions";
import { Diagram } from "./diagram";

export enum FilterType {
  SINGLE = 0,
  MULTIPLE_SINGLE = 1,
  INTERVAL = 2,
  RANGE = 3,
}
export enum DefaultRangeOption {
  EQ = 0, // equal
  BT = 1, // between
  CP = 2, // contains pattern
  LE = 3, // less equal
  GE = 4, // greater than
  NE = 5, // not equal
  NB = 6, // not between
  NP = 7, // not CP
  GT = 8, // greater than
  LT = 9, // less than
}
export class NodeParameters {
  public static Assert = NodeParametersAssertions;

  public static countVariablesForStackedSource(count: number) {
    cy.get(`[id*=${QB_BUTTONS.SOURCE_STACKED_VARIABLE_LIST}] div li`, {
      cyOriginal: true,
    }).should("have.length", count);
  }

  public static switchToInactiveParameters(retriesLeft = 5) {
    cy.get(`#${QB_BUTTONS.PARAMETER_LIST_SEGMENT_BUTTON_INACTIVE}`).click();
    cy.get(`#${QB_BUTTONS.PARAMETER_LIST_SEGMENT_BUTTON_INACTIVE}`)
      .invoke("attr", "aria-selected")
      .then((ariaSelected: string) => {
        if (retriesLeft === 0) {
          throw new Error(`Failed to switch to inactive parameters. aria-selected state is: ${ariaSelected}`);
        }
        if (ariaSelected === "false") {
          NodeParameters.switchToInactiveParameters(retriesLeft - 1);
        }
      });
  }

  public static openParameterPopover(index) {
    cy.get(`button[id*=elementInfoPopoverButtonParameters]`).eq(index).click({ force: true });
  }

  public static scrollStackedParameterIntoView(index) {
    cy.get(`button[id*=elementInfoPopoverButtonStackedParameters]`).eq(index).scrollIntoView();
  }

  public static openStackedParameterPopover(index: number) {
    cy.get(
      `ul[id*=availableStackedParameterList] :nth-child(${
        index + 1
      }) li button[id*=elementInfoPopoverButtonStackedParameters]`,
      Common.original
    ).click({ force: true });
  }

  public static scrollIntoView(index) {
    cy.get(`button[id*=elementInfoPopoverButtonParameters]`).eq(index).scrollIntoView();
  }

  public static mapToVariable(index, indexVariable) {
    cy.get(
      `ul[id*=availableParameterList] :nth-child(${index + 1}) li button[id*=elementParameterMoreButton]`,
      Common.original
    ).click({ force: true });
    cy.get(`li[id*=unifiedmenu]:nth-child(2)`, Common.original).click();
    cy.get(`.sapUiSubmenu li[id*=unifiedmenu]:nth-child(${indexVariable + 1})`, Common.original).click();
    Diagram.expectNodeToBeReadyAfterOperation();
  }

  public static countVariablesForMapping(index, count) {
    cy.get(
      `ul[id*=availableParameterList] :nth-child(${index + 1}) li button[id*=elementParameterMoreButton]`,
      Common.original
    ).click({ force: true });
    cy.get(`li[id*=unifiedmenu]:nth-child(2)`, Common.original).click();
    cy.get(`.sapUiSubmenu li[id*=unifiedmenu]`, Common.original).should("have.length", count);
    Common.clickOutside();
    Diagram.expectNodeToBeReadyAfterOperation();
  }

  public static openSetValueDialog(index) {
    cy.get(
      `ul[id*=availableParameterList] :nth-child(${index + 1}) li button[id*=elementParameterMoreButton]`,
      Common.original
    ).click({ force: true });
    cy.get(`li[id*=unifiedmenu]:nth-child(1)`, Common.original).click();
    cy.wait(waitTime);
  }

  public static openStackedVariableSetValueDialog(index) {
    cy.get(
      `ul[id*=availableStackedParameterList] :nth-child(${index + 1}) li button[id*=elementParameterMoreButton]`,
      Common.original
    ).click({ force: true });
    cy.get(`li[id*=unifiedmenu]:nth-child(1)`, Common.original).click();
    cy.wait(waitTime);
  }

  public static setValueForInput(value: string) {
    cy.get(`input[id*=customInput]`, Common.original).type(`{selectAll}${value}{enter}`);
    NodeParameters.okSetValueDialog();
  }

  public static setDateValueToToday() {
    cy.get(`[id*=customDatePicker-icon]`).should("exist").click({ force: true });
    const { completeDate } = Common.getDateToday();
    cy.get('[data-sap-day="' + completeDate + '"]', { cyOriginal: true }).click({ force: true });
    NodeParameters.okSetValueDialog();
  }

  public static setCustomTimePicker(time: string) {
    cy.get(`input[id*=customTimePicker-inner]`).type(`{selectAll}${time}{enter}`);
    NodeParameters.okSetValueDialog();
  }

  public static setCustomDateTimePicker(time: string) {
    cy.get(`input[id*=customDateTimePicker-inner]`).type(`{selectAll}${time}{enter}`);
    NodeParameters.okSetValueDialog();
  }

  public static closeSetValueDialog() {
    cy.get(`[id*=analyticalModelDefaultValDialog-footer] button[id*=cancel]`, Common.original).click();
  }

  public static isOkSetValueDialogDisabled() {
    cy.get(`[id*=analyticalModelDefaultValDialog-footer] button[id*=ok]`, Common.original).should("be.disabled");
  }

  public static okSetValueDialog() {
    cy.get(`[id*=analyticalModelDefaultValDialog-footer] button[id*=ok]`, Common.original).click();
    Diagram.expectNodeToBeReadyAfterOperation();
  }

  public static addNewValueForMultiValueOrRange() {
    cy.get(`a[id*=addInput]`).click();
  }

  public static deleteValueForMultiValueOrRange(index) {
    cy.get(`[id*=multiRangeList-listUl] li:nth-child(${index + 1}) button[id*=imgDel]`).click();
  }

  public static clickOnPopoverTitle(title) {
    cy.get(`#${QB_QUERY_MODEL_TAB.NODE_PROPERTIES_PANEL}--infoPopover--analyticalModelParameterPopover`)
      .contains(title, { cyOriginal: true })
      .click({ force: true });
  }

  public static assertPopoverValue(title: string, value: string) {
    cy.get(`#${QB_QUERY_MODEL_TAB.NODE_PROPERTIES_PANEL}--infoPopover--${title}`).contains(value, {
      cyOriginal: true,
    });
  }

  public static assertNewDefaultValueNotExist() {
    cy.get(`#${QB_QUERY_MODEL_TAB.NODE_PROPERTIES_PANEL}--infoPopover--displayNewDefaultValueText`).should("not.exist");
  }

  public static expandRangeOption(index) {
    cy.get(`#${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_MULTI_RANGE} [id*=multiRangeList-listUl]`)
      .find("li")
      .eq(index)
      .within(() => {
        cy.get(`[id*=arrow]`).should("exist");
        cy.get(`[id*=arrow]`).click({ force: true });
      });
  }

  public static changeFilterType(index) {
    cy.get(`[id=${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_FILTER_VAR_SELECTION_TYPE}]`, Common.original).click({
      force: true,
    });
    cy.get(
      `li[id*=${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_FILTER_VAR_SELECTION_TYPE_STATE}-${index}]`,
      Common.original
    ).click({
      force: true,
    });
  }

  public static setIntervalValueInputInSourceDetail(valueIndex: number, value: string, high = false) {
    if (high) {
      cy.get(
        `#${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_INTERVAL} [id*=multiRangeList-listUl] li:nth-child(${
          valueIndex + 1
        }) input[id*=inputHigh][id*=customInput]`
      ).type(`{selectAll}${value}{enter}`);
    } else {
      cy.get(
        `#${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_INTERVAL} [id*=multiRangeList-listUl] li:nth-child(${
          valueIndex + 1
        }) input[id*=inputLow][id*=customInput]`
      ).type(`{selectAll}${value}{enter}`);
    }
    NodeParameters.okSetValueDialog();
  }

  public static setRangeValueInputInSourceDetail(valueIndex: number, value: string, high = false) {
    if (high) {
      cy.get(
        `#${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_MULTI_RANGE} [id*=multiRangeList-listUl] li:nth-child(${
          valueIndex + 1
        }) input[id*=inputHigh][id*=customInput]`
      ).type(`{selectAll}${value}{enter}`);
    } else {
      cy.get(
        `#${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_MULTI_RANGE} [id*=multiRangeList-listUl] li:nth-child(${
          valueIndex + 1
        }) input[id*=inputLow][id*=customInput]`
      ).type(`{selectAll}${value}{enter}`);
    }
    NodeParameters.okSetValueDialog();
  }

  public static openValueHelpForRangeValue(index: number) {
    cy.get(`#${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_MULTI_RANGE} [id*=multiRangeList-listUl]`)
      .find("li")
      .eq(index)
      .within(() => {
        cy.get(`[id*=customInput-vhi]`).should("exist");
        cy.get(`[id*=customInput-vhi]`).click({ force: true });
      });
  }

  public static openValueHelpForSingleValue() {
    cy.get(`[id=${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_SINGLE_VALUE}-vhi]`).should("exist");
    cy.get(`[id=${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_SINGLE_VALUE}-vhi]`).click({ force: true });
  }

  public static assertNodeParameterInputValueWithValueHelp(value: string) {
    cy.get(`ul[id*=availableStackedParameterList] :nth-child(2) li .token-details .sapMLabel`, Common.original).should(
      "contain",
      `txtValueWithPlaceHolder(${value})`
    );
  }
  public static assertNodeParameterInputValue(value: string) {
    cy.get(`[id=${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_SINGLE_VALUE}-inner]`, Common.original).should(
      "have.value",
      value
    );
  }

  public static assertFilterType(value: string) {
    cy.get(
      `[id=${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_FILTER_VAR_SELECTION_TYPE_LABELTEXT}]`,
      Common.original
    ).should("contain", value);
  }
}
