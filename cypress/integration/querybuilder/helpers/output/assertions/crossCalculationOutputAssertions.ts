/** @format */

import { QB_OUTPUT_OVERVIEW_PAGE } from "../../../../../pageobjects/querybuilder/ComponentsDescriptor";

export class CrossCalculationOutputAssertions {
  public static crossCalculationsListSizeIs(expectedSize: number) {
    if (expectedSize) {
      cy.get(`#${QB_OUTPUT_OVERVIEW_PAGE.OUTPUT_CROSS_CALCULATION_LIST} li`).should("have.length", expectedSize);
    } else {
      cy.get(`[id*=outputCrossCalculationsList-nodata-text]`).should("exist");
    }
  }
}
