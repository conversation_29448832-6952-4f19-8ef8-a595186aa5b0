/** @format */

import {
  QB_BUTTONS,
  QB_INPUTS,
  QB_OUTPUT_OVERVIEW_PAGE,
} from "../../../../pageobjects/querybuilder/ComponentsDescriptor";
import { Common, waitTime } from "../common";
import { CrossCalculationDetails } from "../crossCalculations/crossCalculationDetails";
import { CrossCalculationOutputAssertions } from "./assertions/crossCalculationOutputAssertions";

export enum CrossCalculationType {
  CALCULATED = 0,
  RESTRICTED = 1,
}

export class CrossCalculationOutput {
  static Assert = CrossCalculationOutputAssertions;

  public static createCrossCalculation(typeIndex: CrossCalculationType = 0) {
    cy.get(`[id=${QB_BUTTONS.ADD_CROSS_CALCULATION}]`).click();
    Common.getMenuButtonItem(QB_BUTTONS.ADD_CROSS_CALCULATION, typeIndex).then((button) => {
      button.firePress();
    });
    CrossCalculationDetails.expectCrossCalculationDetailsToBeReady();
    cy.get(`input[id=${QB_INPUTS.CROSS_CALCULATION_DETAILS_BUSINESS_NAME}]`).should("be.visible");
  }

  public static openCrossCalculation(index: number) {
    cy.get(`#${QB_OUTPUT_OVERVIEW_PAGE.OUTPUT_CROSS_CALCULATION_LIST} div li`)
      .eq(index)
      .find("button[id*=elementCrossCalculationDetailsButton]")
      .click({ force: true });
    CrossCalculationDetails.expectCrossCalculationDetailsToBeReady();
  }

  public static deleteCrossCalculation(index: number) {
    CrossCalculationOutput.selectCrossCalculation(index);
    CrossCalculationOutput.pressDelete();
    cy.wait(waitTime / 2);
  }

  public static selectCrossCalculation(index: number) {
    cy.get(
      `ul[id*=${QB_OUTPUT_OVERVIEW_PAGE.OUTPUT_CROSS_CALCULATION_LIST}] :nth-child(${index + 1}) li .token`,
      Common.original
    ).click({ ctrlKey: true });
  }

  public static pressDelete() {
    cy.get(`button[id*=removeCrossCalculationButton]`).click();
  }
}
