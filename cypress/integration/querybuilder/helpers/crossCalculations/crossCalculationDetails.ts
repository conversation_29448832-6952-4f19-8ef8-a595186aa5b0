/** @format */

import {
  QB_BREADCRUMBS,
  QB_BUTTONS,
  QB_INPUTS,
  QB_VIEWS,
} from "../../../../pageobjects/querybuilder/ComponentsDescriptor";
import { waitTime } from "../common";
import { Output } from "../output/output";
import { CrossCalculationDetailsAssertions } from "./assertions";
import { CrossCalculationDetailsExceptionAggregation } from "./exceptionAggregation";
import { Settings } from "./settings";
import { CrossCalculationDetailsUsedIn } from "./usedIn";

export class CrossCalculationDetails {
  static Settings = Settings;
  static UsedIn = CrossCalculationDetailsUsedIn;
  static Assertions = CrossCalculationDetailsAssertions;
  static ExceptionAggregation = CrossCalculationDetailsExceptionAggregation;

  public static expectCrossCalculationDetailsToBeReady() {
    cy.get(`[id*=${QB_VIEWS.CROSS_CALCULATION_DETAILS}]`).should("exist");
    cy.get(`[id*=${QB_VIEWS.CROSS_CALCULATION_DETAILS}]`).should("be.visible");
    cy.wait(waitTime);
  }

  public static breadcrumbSelectNavBack() {
    cy.get(`[id=${QB_BUTTONS.TOGGLE_FULLSCREEN}]`).click();
    cy.get(`[id=${QB_BREADCRUMBS.DETAILS_BACK}]`).click();
    cy.get(`[id=${QB_BUTTONS.TOGGLE_FULLSCREEN}]`).click();
    Output.expectModelPropertiesToBeReady();
  }

  public static changeBusinessName(name: string) {
    cy.get(`input[id=${QB_INPUTS.CROSS_CALCULATION_DETAILS_BUSINESS_NAME}]`).type(`{selectall}${name}{enter}`);
    CrossCalculationDetails.expectCrossCalculationDetailsToBeReady();
  }

  public static changeTechnicalName(name: string) {
    cy.get(`input[id=${QB_INPUTS.CROSS_CALCULATION_DETAILS_TECHNICAL_NAME}]`).type(`{selectall}${name}{enter}`);
    CrossCalculationDetails.expectCrossCalculationDetailsToBeReady();
  }
}
