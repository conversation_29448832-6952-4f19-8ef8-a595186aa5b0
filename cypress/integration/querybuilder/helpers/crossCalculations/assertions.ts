/** @format */

import {
  QB_CHECKBOXES,
  QB_EXPRESSION_EDITOR,
  QB_INPUTS,
  QB_LINKS,
  QB_PANELS,
  QB_SELECTS,
  QB_VIEWS,
} from "../../../../pageobjects/querybuilder/ComponentsDescriptor";
import { Common } from "../common";

export class CrossCalculationDetailsAssertions {
  public static businessNameIs(value: string) {
    cy.get(`input[id=${QB_INPUTS.CROSS_CALCULATION_DETAILS_BUSINESS_NAME}]`).should("have.value", value);
  }

  public static technicalNameIs(value: string) {
    cy.get(`input[id=${QB_INPUTS.CROSS_CALCULATION_DETAILS_TECHNICAL_NAME}]`).should("have.value", value);
  }

  public static restrictedExpressionIs(value: string) {
    cy.get(`#${QB_EXPRESSION_EDITOR.RESTRICTED_CROSS_CALCULATION}`).contains(value, Common.original);
  }

  public static calculatedExpressionIs(value: string) {
    cy.get(`#${QB_EXPRESSION_EDITOR.CALCULATED_CROSS_CALCULATION}`).contains(value, Common.original);
  }

  public static isAuxiliaryIs(value: "checked" | "unchecked") {
    const should = {
      checked: "be.checked",
      unchecked: "not.be.checked",
    };
    cy.get(`[id*=${QB_CHECKBOXES.CROSS_CALCULATION_DETAILS_IS_AUXILIARY}]`, Common.original).should(should[value]);
  }

  public static sourceNameIs(value: string) {
    cy.get(`[id*=${QB_LINKS.CROSS_CALCULATION_DETAILS_SOURCE}] span`, Common.original).should("contain.text", value);
  }

  public static sourceLinkIs(value: string) {
    cy.get(`[id*=${QB_LINKS.CROSS_CALCULATION_DETAILS_SOURCE}]`, Common.original).should("have.attr", "href", value);
  }

  public static sourceFieldIs(value: string) {
    cy.get(`[id*=${QB_INPUTS.CROSS_CALCULATION_DETAILS_SOURCE_FIELD}]`, Common.original).should("have.value", value);
  }

  public static exceptionAggregationPanelIsHidden() {
    cy.get(`[id*=${QB_PANELS.CROSS_CALCULATION_DETAILS_EXCEPTION_AGGREGATION}]`, Common.original).should(
      "have.attr",
      "aria-hidden",
      "true"
    );
  }

  public static exceptionAggregationPanelIsVisible() {
    cy.get(`[id*=${QB_PANELS.CROSS_CALCULATION_DETAILS_EXCEPTION_AGGREGATION}]`, Common.original).should(
      "not.have.attr",
      "aria-hidden"
    );
  }

  public static exceptionAggregationAttributesHasError() {
    cy.get(`#${QB_SELECTS.CROSS_CALCULATION_DETAILS_EXCEPTION_AGGREGATION_ATTRIBUTES}-content`).should(
      "have.class",
      "sapMInputBaseContentWrapperError"
    );
  }

  public static exceptionAggregationAttributesListHas(attributesNames: string[]) {
    cy.get(`[id*=${QB_VIEWS.CROSS_CALCULATION_DETAILS}--crossCalculationDetailsAttributes]`).should("exist");

    cy.get(`[id*=${QB_VIEWS.CROSS_CALCULATION_DETAILS}--crossCalculationDetailsAttributes]`)
      .find("li")
      .then(($items) => {
        const actualNames = $items.toArray().map((el) => el.textContent?.trim() || "");
        const actualNamesSet = new Set(actualNames);

        attributesNames.forEach((key) => {
          // eslint-disable-next-line @typescript-eslint/no-unused-expressions
          expect(actualNamesSet.has(key)).to.be.true;
        });
      });
  }
}
