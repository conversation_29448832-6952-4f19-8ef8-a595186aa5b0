/** @format */

import { DB_BUTTON, DB_VIEW } from "../../../../pageobjects/databuilder/descriptor";
import {
  QB_BUTTONS,
  QB_DIALOGS,
  QB_INPUTS,
  QB_QUERY_MODEL_TAB,
  QB_TOOLBAR_BUTTONS,
} from "../../../../pageobjects/querybuilder/ComponentsDescriptor";
import { waitTime } from "../common";
import { Output } from "../output/output";
import { REQUESTS } from "../setup/setup";

export class Toolbar {
  public static pressUndo() {
    cy.get(`button[id=${QB_TOOLBAR_BUTTONS.TOOLBAR_UNDO}]`).click();
    cy.wait(waitTime);
  }

  public static pressRedo() {
    cy.get(`button[id=${QB_TOOLBAR_BUTTONS.TOOLBAR_REDO}]`).click();
    cy.wait(waitTime);
  }

  public static checkUndoEnabled(isEnabled) {
    cy.get(`button[id=${QB_TOOLBAR_BUTTONS.TOOLBAR_UNDO}]`).should(isEnabled ? "be.enabled" : "be.disabled");
  }

  public static checkRedoEnabled(isEnabled) {
    cy.get(`button[id=${QB_TOOLBAR_BUTTONS.TOOLBAR_REDO}]`).should(isEnabled ? "be.enabled" : "be.disabled");
  }

  public static deployModel(
    deployFixture = "querybuilder/deploy",
    statusCode = 202,
    isValidationsDialogExpected = true
  ) {
    cy.intercept("POST", "**/dwaas-core/deploy/**", { fixture: deployFixture, statusCode }).as("deployModel");
    cy.get(`#${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}`);
    Toolbar.clickDeploy();
    cy.get(`#${DB_VIEW.BUSY_INDICATOR_DIALOG}`).should("not.exist");
    if (!isValidationsDialogExpected) {
      cy.wait("@deployModel");
    }
  }

  public static clickDeploy() {
    cy.get(`#${DB_BUTTON.CUBE_DEPLOY}`).click();
  }

  public static saveNewModel(businessName: string, technicalName: string, error = false) {
    cy.intercept("POST", "**/dwaas-core/querybuilder/*/convertCsn", { fixture: "querybuilder/convertCsn" });
    cy.get(`[id=${QB_TOOLBAR_BUTTONS.TOOLBAR_SAVE}]`).click();
    cy.wait(waitTime);
    cy.get(`div[id=${QB_DIALOGS.SAVE_MODEL_DIALOG}]`).should("be.visible");
    cy.get(`#${QB_INPUTS.SAVE_MODEL_DIALOG_BUSINESS_NAME}`, { cyOriginal: true })
      .click()
      .type(`{selectAll}${businessName}`);
    cy.get(`#${QB_INPUTS.SAVE_MODEL_DIALOG_TECHNICAL_NAME}`, { cyOriginal: true })
      .click()
      .type(`{selectAll}${technicalName}`);
    cy.get(`button[id=${QB_BUTTONS.SAVE_DIALOG_CONFIRM}]`).click();
    if (!error) {
      cy.get(`#${DB_VIEW.BUSY_INDICATOR_DIALOG}`).should("not.exist");
    }
  }

  public static saveModel() {
    cy.intercept("POST", "**/dwaas-core/querybuilder/*/convertCsn", { fixture: "querybuilder/convertCsn" });
    cy.get(`button[id*=databuilderWorkbench--saveAndSaveAs]`).click();
    cy.get(`li[id*=databuilderWorkbench--saveonly]`).click();
    cy.get(`#${DB_VIEW.BUSY_INDICATOR_DIALOG}`).should("not.exist");
  }

  public static saveAs(businessName: string, technicalName: string, saveAnyway = true) {
    cy.intercept("POST", "**/dwaas-core/querybuilder/*/convertCsn", { fixture: "querybuilder/convertCsn" });
    cy.get(`button[id*=databuilderWorkbench--saveAndSaveAs]`).click();
    cy.get(`#${QB_TOOLBAR_BUTTONS.TOOLBAR_SAVE_AS}`).click();
    cy.wait(waitTime);
    if (saveAnyway) {
      cy.get(`[id=${QB_TOOLBAR_BUTTONS.TOOLBAR_SAVE_ANYWAY}]`).click();
      cy.wait(waitTime);
    }
    cy.get(`div[id=${QB_DIALOGS.SAVE_MODEL_DIALOG}]`).should("be.visible");
    cy.get(`#${QB_INPUTS.SAVE_MODEL_DIALOG_BUSINESS_NAME}`, { cyOriginal: true })
      .click()
      .type(`{selectAll}${businessName}`);
    cy.get(`#${QB_INPUTS.SAVE_MODEL_DIALOG_TECHNICAL_NAME}`, { cyOriginal: true })
      .click()
      .type(`{selectAll}${technicalName}`);
    cy.get(`button[id=${QB_BUTTONS.SAVE_DIALOG_CONFIRM}]`).click();
    cy.get(`#${DB_VIEW.BUSY_INDICATOR_DIALOG}`).should("not.exist");
  }

  public static saveAnyway() {
    cy.get(`[id=${QB_TOOLBAR_BUTTONS.TOOLBAR_SAVE_ANYWAY}]`).click({ force: true });
    // wait for the save busy indicator to disappear
    cy.get(`#${DB_VIEW.BUSY_INDICATOR_DIALOG}`).should("not.exist");
  }

  public static deployAnyway() {
    Toolbar.saveAnyway();
  }

  public static doAsyncSave() {
    cy.intercept("POST", REQUESTS.REPOSITORY_OBJECTS, {
      statusCode: 202,
      body: {
        message: "object with id = <cypress tests object> is updated",
      },
    }).as("save");

    cy.get(`#${QB_TOOLBAR_BUTTONS.TOOLBAR_GROUP_SAVE}`).click();
    cy.get(`#${QB_TOOLBAR_BUTTONS.TOOLBAR_SAVE_ONLY}`).click();
    cy.wait("@save");
  }

  public static checkDirty(isDirty) {
    cy.get(`.sapHcsShellHeader .sapMText.sapHcsShellNavBreadcrumbsText`).should(
      isDirty ? "have.attr" : "not.have.attr",
      "data-dirty",
      "true"
    );
  }

  public static restoreVersion() {
    cy.get(`#versionsReadOnlyMenuButton`).click();
    cy.get(`.sapMMenu li`).should("be.visible");
    cy.get(`.sapMMenu li`).eq(0).click();
    Output.expectModelPropertiesToBeReady();
  }
}
