/** @format */

/** @format */

export const waitTime = 2000;

export class Common {
  static original = { cyOriginal: true };

  public static clickOutside() {
    // this is used so we click outside of an input and the change event is run
    cy.get("body").click(0, 0);
  }

  public static expectLoadingIndicatorToNotExist() {
    cy.get("[id*=cubebuilderComponent-busyIndicator]").should("not.exist");
    cy.wait(waitTime);
  }

  public static getMenuButtonItem(buttonMenuId: string, itemIndex: number): Cypress.Chainable<sap.m.Button> {
    return cy.byId(buttonMenuId).then((button) => cy.wrap(button.getMenu().getItems()[itemIndex]));
  }

  public static requestsCount(url) {
    return (cy as any).state("requests").filter((a) => a.xhr.url.indexOf(url) !== -1).length;
  }

  public static getDateToday(): { completeDate: string; date: string } {
    const currentTime = new Date();
    const month = `${currentTime.getMonth() + 1}`.padStart(2, "0");
    const day = currentTime.getDate().toString().padStart(2, "0");
    const completeDate = `${currentTime.getFullYear()}${month}${day}`;
    const date = currentTime.getDate().toString();
    return { completeDate, date };
  }
}
