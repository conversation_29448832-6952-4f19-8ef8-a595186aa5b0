/**
 * Copyright 2021 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */
import { QB_BUTTONS, QB_SELECTS } from "../../../pageobjects/querybuilder/ComponentsDescriptor";
import { Common } from "../helpers/common";
import { Output } from "../helpers/output/output";
import { VariablesOutput } from "../helpers/output/variablesOutput";
import { Setup } from "../helpers/setup/setup";
import { Diagram } from "../helpers/sources/diagram";
import { NodeParameters } from "../helpers/sources/nodeParameters";
import { NodeProperties } from "../helpers/sources/nodeProperties";
import { VariableDetails } from "../helpers/variables/variableDetails";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    Setup.setupBeforeEach({}, true);
    Setup.createNewModel({
      designObjectId: "querybuilder/designObjectId",
      loadDataEntityDetails: "querybuilder/loadDataEntityDetailsAM",
      convertAnalyticalDataset: "querybuilder/convertAnalyticalAM",
    });
    Output.expectModelPropertiesToBeReady();
  });

  it("should un-map and set default value to input parameter", function () {
    VariablesOutput.countVariables(2);

    Diagram.openSourceNodeUsingDiagram({ name: "N_Test_Param" });

    cy.get(`[id*=${QB_BUTTONS.INPUT_PARAMETER_LIST}] div li`, Common.original).should("have.length", 10);

    // set value
    NodeParameters.openSetValueDialog(0);
    NodeParameters.setValueForInput("test");

    cy.get(`ul[id*=availableParameterList] :nth-child(1) li .token-details .sapMLabel`, Common.original).should(
      "contain",
      "txtValueWithPlaceHolder(test)"
    );
  });

  it("should change mapped and show the validation error for not used variable", function () {
    VariablesOutput.countVariables(2);

    Diagram.openSourceNodeUsingDiagram({ name: "N_Test_Param" });

    cy.get(`[id*=${QB_BUTTONS.INPUT_PARAMETER_LIST}] div li`, Common.original).should("have.length", 10);

    // map to existing variable
    NodeParameters.countVariablesForMapping(1, 3);
    NodeParameters.mapToVariable(1, 1);
    cy.get(`ul[id*=availableParameterList] :nth-child(2) li .token-details .sapMLabel`, Common.original).should(
      "contain",
      "txtMappedTo(PARAM_1_String)"
    );

    // create new mapped Parameter
    NodeParameters.mapToVariable(1, 0);
    cy.get(`ul[id*=availableParameterList] :nth-child(2) li .token-details .sapMLabel`, Common.original).should(
      "contain",
      "txtMappedTo(PARAM_1_Boolean_1)"
    );
  });

  it("should change mapped to new variable and validate the default value type is changed w.r.t to mapped source input param", function () {
    VariablesOutput.countVariables(2);

    Diagram.openSourceNodeUsingDiagram({ name: "N_Test_Param" });

    cy.get(`[id*=${QB_BUTTONS.INPUT_PARAMETER_LIST}] div li`, Common.original).should("have.length", 10);

    // create new mapped Parameter
    NodeParameters.mapToVariable(1, 0);

    // create new mapped Parameter
    NodeParameters.mapToVariable(1, 0);

    NodeProperties.breadcrumbSelectNavBack();
    VariablesOutput.countVariables(4);

    VariablesOutput.openVariable(0);
    cy.get(`[id=${QB_SELECTS.VARIABLE_BUSINESS_NAME}-inner]`, Common.original).should("have.value", "PARAM_1_Boolean");
    // the un-mapped variable has not data type and no default value visible
    cy.get(`input[id*=${QB_SELECTS.VARIABLE_DATA_TYPE}-hiddenInput]`).should("have.value", "Inherited");
    cy.get(`input[id*=${QB_SELECTS.PANEL_VARIABLE_DEFAULT_VALUE_INPUT}]`, Common.original).should("not.exist");
    VariableDetails.breadcrumbSelectNavBack();

    VariablesOutput.openVariable(2);
    cy.get(`[id=${QB_SELECTS.VARIABLE_BUSINESS_NAME}-inner]`, Common.original).should(
      "have.value",
      "PARAM_1_Boolean_1"
    );
    // the un-mapped variable has not data type and no default value visible
    cy.get(`input[id*=${QB_SELECTS.VARIABLE_DATA_TYPE}-hiddenInput]`).should("have.value", "Inherited");
    cy.get(`input[id*=${QB_SELECTS.PANEL_VARIABLE_DEFAULT_VALUE_INPUT}]`, Common.original).should("not.exist");
    VariableDetails.breadcrumbSelectNavBack();

    VariablesOutput.openVariable(3);
    cy.get(`[id=${QB_SELECTS.VARIABLE_BUSINESS_NAME}-inner]`, Common.original).should(
      "have.value",
      "PARAM_1_Boolean_2"
    );
    cy.get(`[id*=variableDetails--panelVariableDefaultValue--customSelect-hiddenSelect]`, Common.original).should(
      "include.text",
      "booleanType_value_true"
    );
  });
});
