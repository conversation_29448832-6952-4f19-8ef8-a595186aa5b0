/** @format */

import { QB_INPUTS, QB_SELECTS } from "../../../../pageobjects/querybuilder/ComponentsDescriptor";
import { Common } from "../../helpers/common";
import { ConversionProperties } from "../../helpers/measures/currencyProperties";
import { MeasureDetails } from "../../helpers/measures/measureDetails";
import { Settings } from "../../helpers/measures/settings";
import { MeasuresOutput } from "../../helpers/output/measuresOutput";
import { Output } from "../../helpers/output/output";
import { Setup } from "../../helpers/setup/setup";

// VAL_UC_CAN_NOT_FIND_CONFIG_TABLES

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    Setup.setupBeforeEach({
      DWCO_MODELING_AM_UNIT_CONVERSION: true,
    });
    Setup.openQueryModel("Cypress_Opportunity_Items", "TESTS_SPACE_1", {
      loadDataEntityDetailsFixture: "querybuilder/unitConversion/loadDataEntityDetail",
      designObjectsFolderIds: "querybuilder/designObjectsFoldersOppIt",
      designObjectsIds: "querybuilder/unitConversion/designObjectsIds",
    });
    Output.expectModelPropertiesToBeReady();
  });

  it("Create a Unit Conversion measure by Constant Value", () => {
    // create Unit conversion measure
    MeasuresOutput.createConversionMeasure(
      "querybuilder/unitConversion/SAP.UNIT.VIEW.T006D.CLIENT",
      "SAP.UNIT.VIEW.T006D"
    );
    // enter business name
    MeasureDetails.changeBusinessNameOfMeasure("Cypress Measure_CC 1");
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_TECHNICAL_NAME}]`, Common.original).should(
      "have.value",
      "Cypress_Measure_CC_1"
    );
    cy.get(`[id*=${QB_SELECTS.MEASURE_DETAILS_SOURCE_MEASURE}] li`, Common.original).should("have.length", 3);
    // select source measure "Quantity"
    MeasureDetails.changeSourceMeasure(2);
    /** Target Unit */
    ConversionProperties.openTargetDialog("querybuilder/unitConversion/SAP.UNIT.VIEW.T006A");
    /** Target Unit - Constant Value */
    /** search the value */
    ConversionProperties.searchTargetValue("E49", QB_SELECTS.UC_TARGET_UNIT_SEARCH_FIELD);
    openTargetDialogAndSelectValue();
    MeasureDetails.changeSourceMeasure(1);
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_UC_TARGET_UNIT}]`, Common.original).should("have.value", "");
    ConversionProperties.openTargetDialog("querybuilder/unitConversion/SAP.UNIT.VIEW.T006A");
    /** Target Unit - Constant Value */
    /** search the value */
    cy.get(`#${QB_SELECTS.UC_TARGET_UNIT_SEARCH_FIELD_RESET}`, Common.original).click({ force: true });
    ConversionProperties.searchTargetValue("E49", QB_SELECTS.UC_TARGET_UNIT_SEARCH_FIELD);
    openTargetDialogAndSelectValue();
    // select the is-auxiliary checkbox
    Settings.toggleIsAuxiliaryCheckbox();
    // click back breadcrumb
    MeasureDetails.breadcrumbSelectNavBack();
    // check for the new measure on the list
    MeasuresOutput.countMeasures(4);
    // selects the measure
    MeasuresOutput.selectMeasureByName("Cypress Measure_CC 1");
    // removes the measure
    MeasuresOutput.pressDelete();
    // with the new measure removed there should be 2 measures in the output list
    MeasuresOutput.countMeasures(3);
  });

  it("Create a Unit Conversion measure by Dimensions", () => {
    // create measure
    MeasuresOutput.createConversionMeasure(
      "querybuilder/unitConversion/SAP.UNIT.VIEW.T006D.CLIENT",
      "SAP.UNIT.VIEW.T006D"
    );
    // enter business name
    MeasureDetails.changeBusinessNameOfMeasure("Cypress Measure_CC 1");
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_TECHNICAL_NAME}]`, Common.original).should(
      "have.value",
      "Cypress_Measure_CC_1"
    );
    cy.get(`[id*=${QB_SELECTS.MEASURE_DETAILS_SOURCE_MEASURE}] li`, Common.original).should("have.length", 3);
    // select source measure "Quantity"
    MeasureDetails.changeSourceMeasure(1);

    /** Target Unit - Check Dimension */
    ConversionProperties.openTargetDialog("querybuilder/unitConversion/SAP.UNIT.VIEW.T006A");
    ConversionProperties.selectTargetConversionType(1, "Unit");
    cy.get(
      `#${QB_SELECTS.UC_TARGET_UNIT_DIMENSION_TBL} tbody tr:not(.sapUiTableHeaderRow):not(.sapUiTableRowHidden)`,
      Common.original
    ).should("have.length", 2);

    ConversionProperties.selectTargetConversionType(0, "Unit");
    ConversionProperties.clickTargetUnitFixedValue(4);
    ConversionProperties.okTargetConversionDialog(QB_SELECTS.UC_TARGET_UNIT_DIALOG_SELECT_BUTTON);
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_UC_TARGET_UNIT}]`, Common.original).should("have.value", "2M");

    ConversionProperties.openTargetDialog("querybuilder/unitConversion/SAP.UNIT.VIEW.T006A");
    typeInSearchField("test");
    clickCancelButtonInDialog();
    ConversionProperties.openTargetDialog("querybuilder/unitConversion/SAP.UNIT.VIEW.T006A");
    checkSearchFieldIsEmpty();
  });
});

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    Setup.setupBeforeEach({
      DWCO_MODELING_AM_UNIT_CONVERSION: true,
    });
    Setup.openQueryModel("N_Test_Param", "TESTS_SPACE_1", {
      designObjectsFolderIds: "querybuilder/designObjectFolderId",
      designObjectsIds: "querybuilder/MCT_OpportunityItems/emptyAM",
    });
    Output.expectModelPropertiesToBeReady();
  });

  it("Should not display the Exception Aggregation panel if there are no Measures configured for Conversion", () => {
    MeasuresOutput.createConversionMeasure(
      "querybuilder/unitConversion/SAP.UNIT.VIEW.T006D.CLIENT",
      "SAP.UNIT.VIEW.T006D"
    );
    cy.get(`[id=${QB_SELECTS.MEASURE_DETAILS_EXCEPTION_AGGREGATION_PANEL}]`, Common.original).should("not.exist");
  });
});

function checkSearchFieldIsEmpty() {
  cy.get(`#${QB_SELECTS.UC_TARGET_UNIT_DIALOG_SELECT_SEARCH_FIELD}`).click().should("have.value", "");
}
function typeInSearchField(textField: string) {
  cy.get(`#${QB_SELECTS.UC_TARGET_UNIT_DIALOG_SELECT_SEARCH_FIELD}`).click().type(textField);
}

function clickCancelButtonInDialog() {
  cy.get(`#${QB_SELECTS.UC_TARGET_UNIT_DIALOG_SELECT_CANCEL}`).click();
}
function openTargetDialogAndSelectValue() {
  cy.get(`#${QB_SELECTS.UC_TARGET_UNIT_FIXED_VALUE_TBL} tbody tr:nth-child(1)`, Common.original).should(
    "contain",
    "E49"
  );
  ConversionProperties.clickTargetUnitFixedValue(0);

  // check save button in target Unit value help dialog is enabled and press it
  ConversionProperties.okTargetConversionDialog(QB_SELECTS.UC_TARGET_UNIT_DIALOG_SELECT_BUTTON);
  cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_UC_TARGET_UNIT}]`, Common.original).should("have.value", "E49");
}
