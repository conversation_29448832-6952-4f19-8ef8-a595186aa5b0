/** @format */

import { QB_INPUTS, QB_MESSAGES, QB_SELECTS } from "../../../../pageobjects/querybuilder/ComponentsDescriptor";
import { Common } from "../../helpers/common";
import { ConversionProperties } from "../../helpers/measures/currencyProperties";
import { MeasureDetails } from "../../helpers/measures/measureDetails";
import { Settings } from "../../helpers/measures/settings";
import { MeasuresOutput } from "../../helpers/output/measuresOutput";
import { Output } from "../../helpers/output/output";
import { Setup } from "../../helpers/setup/setup";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    Setup.setupBeforeEach({
      DWCO_MODELING_AM_UNIT_CONVERSION: true,
    });
    Setup.openQueryModel("Cypress_Opportunity_Items", "TESTS_SPACE_1", {
      loadDataEntityDetailsFixture: "querybuilder/loadDataEntityDetailsOppIt",
      designObjectsFolderIds: "querybuilder/designObjectsFoldersOppIt",
      designObjectsIds: "querybuilder/designObjectsIdsOppIt",
    });
    Output.expectModelPropertiesToBeReady();
  });

  it("Create a Currency Conversion measure by Constant Value", () => {
    // create measure
    MeasuresOutput.createConversionMeasure("querybuilder/currencyConversion/SAP.CURRENCY.VIEW.TCURC.CLIENT");
    // enter business name
    MeasureDetails.changeBusinessNameOfMeasure("Cypress Measure_CC 1");
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_TECHNICAL_NAME}]`, Common.original).should(
      "have.value",
      "Cypress_Measure_CC_1"
    );
    // select source measure "Value"
    cy.get(`[id*=${QB_SELECTS.MEASURE_DETAILS_SOURCE_MEASURE}] li`, Common.original).should("have.length", 2);

    /** Target Currency */
    ConversionProperties.openTargetDialog();
    /** Target Currency - Constant Value */
    /** search the value */
    ConversionProperties.searchTargetValue("AED");
    cy.get(`#${QB_SELECTS.CC_TARGET_CURRENCY_FIXED_VALUE_TBL} tbody tr:nth-child(1)`, Common.original).should(
      "contain",
      "AED"
    );
    ConversionProperties.clickTargetConversionValue(0);

    // check save button in target currency value help dialog is enabled and press it
    ConversionProperties.okTargetConversionDialog();
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_CC_TARGET_CURRENCY}]`, Common.original).should("have.value", "AED");

    /** Reference date- Select CURRENT_DATE function */
    ConversionProperties.openReferenceDateDialog();
    ConversionProperties.selectReferenceDateType(2);
    ConversionProperties.okReferenceDateDialog();
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_CC_REFERENCE_DATE}]`, Common.original).should(
      "have.value",
      "ccTypesCurrentDate"
    );

    /** Conversion type */
    ConversionProperties.openConversionTypeDialog();
    ConversionProperties.searchConversionTypeValue("001D");
    cy.get(`#${QB_SELECTS.CC_CONVERSION_TYPE_FIXED_VALUE_TBL} tbody tr:nth-child(1)`, Common.original).should(
      "contain",
      "001D"
    );
    ConversionProperties.clickConversionType(0);
    ConversionProperties.okConversionTypeDialog();
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_CC_CONVERSION_TYPE}]`, Common.original).should("have.value", "001D");

    // select the is-auxiliary checkbox
    Settings.toggleIsAuxiliaryCheckbox();
    // click back breadcrumb
    MeasureDetails.breadcrumbSelectNavBack();
    // check for the new measure on the list
    MeasuresOutput.countMeasures(3);
    // selects the measure
    MeasuresOutput.selectMeasureByName("Cypress Measure_CC 1");
    // removes the measure
    MeasuresOutput.pressDelete();
    // with the new measure removed there should be 2 measures in the output list
    MeasuresOutput.countMeasures(2);
  });

  it("Create a Currency Conversion measure by Dimensions", () => {
    // create measure
    MeasuresOutput.createConversionMeasure("querybuilder/currencyConversion/SAP.CURRENCY.VIEW.TCURC.CLIENT");
    // enter business name
    MeasureDetails.changeBusinessNameOfMeasure("Cypress Measure_CC 1");
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_TECHNICAL_NAME}]`, Common.original).should(
      "have.value",
      "Cypress_Measure_CC_1"
    );
    // select source measure "Value"
    cy.get(`[id*=${QB_SELECTS.MEASURE_DETAILS_SOURCE_MEASURE}] li`, Common.original).should("have.length", 2);

    /** Target Currency - Dimensions */
    ConversionProperties.openTargetDialog();
    ConversionProperties.selectTargetConversionType(1, "Currency");
    cy.get(
      `#${QB_SELECTS.CC_TARGET_CURRENCY_DIMENSION_TBL} tbody tr:not(.sapUiTableHeaderRow):not(.sapUiTableRowHidden)`,
      Common.original
    ).should("have.length", 1);
    ConversionProperties.selectTargetConversionType(0, "Currency");
    ConversionProperties.clickTargetConversionValue(1);
    ConversionProperties.okTargetConversionDialog();
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_CC_TARGET_CURRENCY}]`, Common.original).should("have.value", "AED");

    /** Reference date- Select CURRENT_DATE function */
    ConversionProperties.openReferenceDateDialog();
    ConversionProperties.selectReferenceDateType(1);
    cy.get(
      `#${QB_SELECTS.CC_REFERENCE_DATE_DIMENSION_TBL} tbody tr:not(.sapUiTableHeaderRow):not(.sapUiTableRowHidden)`,
      Common.original
    ).should("have.length", 1);
    ConversionProperties.selectReferenceDateType(2);
    ConversionProperties.okReferenceDateDialog();
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_CC_REFERENCE_DATE}]`, Common.original).should(
      "have.value",
      "ccTypesCurrentDate"
    );

    /** Conversion type */
    ConversionProperties.openConversionTypeDialog();
    ConversionProperties.searchConversionTypeValue("0011");
    cy.get(`#${QB_SELECTS.CC_CONVERSION_TYPE_FIXED_VALUE_TBL} tbody tr:nth-child(1)`, Common.original).should(
      "contain",
      "0011"
    );
    ConversionProperties.clickConversionType(0);
    ConversionProperties.okConversionTypeDialog();
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_CC_CONVERSION_TYPE}]`, Common.original).should("have.value", "0011");
  });
});

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    Setup.setupBeforeEach({
      DWCO_MODELING_AM_UNIT_CONVERSION: true,
    });
    Setup.openQueryModel("N_Test_Param", "TESTS_SPACE_1", {
      designObjectsFolderIds: "querybuilder/designObjectFolderId",
      designObjectsIds: "querybuilder/MCT_OpportunityItems/emptyAM",
    });
    Output.expectModelPropertiesToBeReady();
  });

  it("Should not display the Exception Aggregation panel if there are no Measures configured for Currency Conversion and no CC views", () => {
    cy.intercept("POST", "**/dwaas-core/data-access/valuehelp/instant/TESTS_SPACE_1/SAP.CURRENCY.VIEW.TCURC", {
      statusCode: 404,
    }).as("getClients");
    MeasuresOutput.createMeasure(3);
    cy.wait("@getClients");
    cy.get(`[id=${QB_MESSAGES.CC_VIEWS_NOT_AVAILABLE_MESSAGE}]`, Common.original).should("exist");
    cy.get(`[id=${QB_SELECTS.MEASURE_DETAILS_EXCEPTION_AGGREGATION_PANEL}]`, Common.original).should("not.exist");
  });

  it("Should not display the Exception Aggregation panel if there are no Measures configured for Currency Conversion", () => {
    MeasuresOutput.createConversionMeasure("querybuilder/currencyConversion/SAP.CURRENCY.VIEW.TCURC.CLIENT");
    cy.get(`[id=${QB_SELECTS.MEASURE_DETAILS_EXCEPTION_AGGREGATION_PANEL}]`, Common.original).should("not.exist");
  });
});
