/** @format */

import { QB_INPUTS, QB_MESSAGES, QB_SELECTS } from "../../../../pageobjects/querybuilder/ComponentsDescriptor";
import { Common } from "../../helpers/common";
import { ConversionProperties } from "../../helpers/measures/currencyProperties";
import { MeasureDetails } from "../../helpers/measures/measureDetails";
import { MeasureType, MeasuresOutput } from "../../helpers/output/measuresOutput";
import { Output } from "../../helpers/output/output";
import { Setup } from "../../helpers/setup/setup";
import { Validations } from "../../helpers/validations/validations";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    Setup.setupBeforeEach({
      DWCO_MODELING_AM_UNIT_CONVERSION: true,
    });
    Setup.openQueryModel("Cypress_Opportunity_Items", "TESTS_SPACE_1", {
      loadDataEntityDetailsFixture: "querybuilder/loadDataEntityDetailsOppIt",
      designObjectsFolderIds: "querybuilder/designObjectsFoldersOppIt",
      designObjectsIds: "querybuilder/designObjectsIdsOppIt",
    });
    Output.expectModelPropertiesToBeReady();
  });

  it("Create a Currency Conversion measure by Variables", () => {
    // open existing model
    MeasuresOutput.createConversionMeasure();
    /** Target Currency*/
    ConversionProperties.openTargetDialog();
    ConversionProperties.selectTargetConversionType(2, "Currency");
    cy.get(
      `#${QB_SELECTS.CC_TARGET_CURRENCY_VARIABLE_TBL} tbody tr:not(.sapUiTableHeaderRow):not(.sapUiTableRowHidden)`,
      Common.original
    ).should("have.length", 2);
    ConversionProperties.okTargetConversionDialog();
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_CC_TARGET_CURRENCY}]`, Common.original).should(
      "have.value",
      "defaultNameTargetCurrencyVariableInput(1)"
    );

    /** Reference date */
    ConversionProperties.openReferenceDateDialog();
    ConversionProperties.selectReferenceDateType(3);
    cy.get(
      `#${QB_SELECTS.CC_REFERENCE_DATE_VARIABLE_TBL} tbody tr:not(.sapUiTableHeaderRow):not(.sapUiTableRowHidden)`,
      Common.original
    ).should("have.length", 3);
    ConversionProperties.okReferenceDateDialog();
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_CC_REFERENCE_DATE}]`, Common.original).should(
      "have.value",
      "defaultNameReferenceDateVariableInput(1)"
    );

    /** Conversion type */
    //select Dimensions
    ConversionProperties.openConversionTypeDialog();
    ConversionProperties.selectConversionTypeType(1);
    cy.get(
      `#${QB_SELECTS.CC_CONVERSION_TYPE_DIMENSION_TBL} tbody tr:not(.sapUiTableHeaderRow):not(.sapUiTableRowHidden)`,
      Common.original
    ).should("have.length", 12);
    ConversionProperties.searchConversionTypeValue("Product");
    cy.get(
      `#${QB_SELECTS.CC_CONVERSION_TYPE_DIMENSION_TBL} tbody tr:nth-child(1) td:nth-child(1)`,
      Common.original
    ).should("contain", "Product");
    ConversionProperties.clickConversionTypeDimension(0);
    ConversionProperties.okConversionTypeDialog();
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_CC_CONVERSION_TYPE}]`, Common.original).should(
      "have.value",
      "Product ID (diagramFactSourceNode)"
    );

    //Select variables
    ConversionProperties.openConversionTypeDialog();
    ConversionProperties.selectConversionTypeType(2);
    cy.get(
      `#${QB_SELECTS.CC_CONVERSION_TYPE_VARIABLE_TBL} tbody tr:not(.sapUiTableHeaderRow):not(.sapUiTableRowHidden)`,
      Common.original
    ).should("have.length", 4);
    ConversionProperties.okConversionTypeDialog();
    cy.get(`[id=${QB_INPUTS.MEASURE_DETAILS_CC_CONVERSION_TYPE}]`, Common.original).should(
      "have.value",
      "defaultNameConversionTypeVariableInput(1)"
    );
  });

  it("Validation when currency conversion views do not exists", () => {
    // menu button: add currency conversion measure
    cy.intercept("POST", "**/dwaas-core/data-access/valuehelp/instant/TESTS_SPACE_1/SAP.CURRENCY.VIEW.TCURC", {
      statusCode: 404,
    }).as("getClients");
    MeasuresOutput.createMeasure(MeasureType.CONVERSION);
    cy.wait("@getClients");
    cy.get(`[id=${QB_MESSAGES.CC_VIEWS_NOT_AVAILABLE_MESSAGE}]`, Common.original).should("exist");

    Validations.analyticModelValidationPopoverMsgs(["VAL_CC_INVALID(defaultConversionMeasureBusinessName)"]);
  });

  it("Validation when currency conversion views are empty", () => {
    MeasuresOutput.createConversionMeasure(
      "querybuilder/currencyConversion/SAP.CURRENCY.VIEW.TCURC.NOCLIENT",
      "SAP.CURRENCY.VIEW.TCURC"
    );
    cy.get(`[id=${QB_MESSAGES.CC_VIEWS_EMPTY_MESSAGE}]`, Common.original).should("exist");
  });

  it("Copy currency conversion measure", () => {
    // create original measure
    MeasuresOutput.createConversionMeasure();
    // click back breadcrumb
    MeasureDetails.breadcrumbSelectNavBack();
    // select and copy currency conversion measure
    MeasuresOutput.copyMeasure(0);
    // check client id in measure details
    cy.get(`input[id*=analyticModelClientId]`, Common.original).should("have.value", "000");
  });
});
