/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { it } from "mocha";
import { AttributePopover } from "../../helpers/attributes/attributePopover";
import { Common } from "../../helpers/common";
import { MeasurePopover } from "../../helpers/measures/measurePopover";
import { DimensionsOutput } from "../../helpers/output/dimensionsOutput";
import { MeasuresOutput } from "../../helpers/output/measuresOutput";
import { Output } from "../../helpers/output/output";
import { VariablesOutput } from "../../helpers/output/variablesOutput";
import { Setup } from "../../helpers/setup/setup";
import { VariablePopover } from "../../helpers/variables/variablePopover";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    Setup.setupBeforeEach();
    Setup.openQueryModel("GC20", "TESTS_SPACE_1", {
      loadDataEntityDetailsFixture: "querybuilder/compounding/loadDataEntityDimensions",
      designObjectsIds: "querybuilder/compounding/designObjectsIdsDimensions2.json",
      designObjectsFolderIds: "querybuilder/compounding/designObjectsFolderIdsDimensions.json",
    });
    Output.expectModelPropertiesToBeReady();
  });

  it("measures should have links in popover used in lists", () => {
    const expectedExpression = `<a target="_self" href="#/databuilder&amp;/db/TESTS_SPACE_1/GC20/cubeQueryModel/Folder_PMFFCYTC/measure/defaultCalculatedMeasureBusine" class="sapMLnk" style="position: static !important;">defaultCalculatedMeasureBusinessName (calculatedMeasure)</a><br style="position: static !important;"><a target="_self" href="#/databuilder&amp;/db/TESTS_SPACE_1/GC20/cubeQueryModel/Folder_PMFFCYTC/measure/defaultRestrictedMeasureBusine" class="sapMLnk" style="position: static !important;">defaultRestrictedMeasureBusinessName (restrictedMeasure)</a><br style="position: static !important;">`;
    MeasuresOutput.openMeasurePopover(3);
    MeasurePopover.popoverUsedInListContains(expectedExpression);
  });

  it("variables should have links in popover used in lists", () => {
    const expectedExpression = `<a target="_self" href="#/databuilder&amp;/db/TESTS_SPACE_1/GC20/cubeQueryModel/Folder_PMFFCYTC/attribute/ControllingArea" class="sapMLnk" style="position: static !important;">variableInfoFilterOnAttributeText(Controlling Area,diagramFactSourceNode)</a><br style="position: static !important;">`;
    VariablesOutput.openVariablePopover(0);
    VariablePopover.popoverUsedInListContains(expectedExpression);
  });

  it("attributes should have links in popover used in lists", () => {
    const expectedExpression = `<a target="_self" href="#/databuilder&amp;/db/TESTS_SPACE_1/GC20/cubeQueryModel/Folder_PMFFCYTC/measure/defaultCalculatedMeasureBusine" class="sapMLnk" style="position: static !important;">defaultCalculatedMeasureBusinessName (calculatedMeasure)</a><br style="position: static !important;"><a target="_self" href="#/databuilder&amp;/db/TESTS_SPACE_1/GC20/cubeQueryModel/Folder_PMFFCYTC/measure/defaultRestrictedMeasureBusine" class="sapMLnk" style="position: static !important;">defaultRestrictedMeasureBusinessName (restrictedMeasure)</a><br style="position: static !important;">`;
    DimensionsOutput.openAttributePopover(0);
    AttributePopover.popoverUsedInListContains(expectedExpression);
    const expectedExpressionShowMore = `<a target="_self" href="#/databuilder&amp;/db/TESTS_SPACE_1/GC20/cubeQueryModel/Folder_PMFFCYTC/variable/ControllingArea" class="sapMLnk" style="position: static !important;">Controlling Area (storyFilterVariable)</a><br style="position: static !important;"><a target="_self" href="#/databuilder&amp;/db/TESTS_SPACE_1/GC20/cubeQueryModel/Folder_PMFFCYTC/variable/DEFAULTNAMEVARIABLERESTRICTEDM" class="sapMLnk" style="position: static !important;">defaultNameVariableRestrictedMeasure(1) (restrictedMeasureVariable)</a><br style="position: static !important;">`;
    AttributePopover.popoverUsedInListShowMoreContains(expectedExpressionShowMore);
    Common.clickOutside();
    DimensionsOutput.openAttributePopover(0);
    AttributePopover.popoverUsedInListShowMoreContainsIsHidden();
  });
});
