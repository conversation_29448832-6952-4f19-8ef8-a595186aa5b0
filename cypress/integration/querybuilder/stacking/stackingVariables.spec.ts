/** @format */

import {
  QB_BUTTONS,
  QB_EXPRESSION_EDITOR,
  QB_QUERY_MODEL_TAB,
} from "../../../pageobjects/querybuilder/ComponentsDescriptor";
import { Common } from "../helpers/common";
import { MeasureDetails } from "../helpers/measures/measureDetails";
import { Settings } from "../helpers/measures/settings";
import { MeasuresOutput } from "../helpers/output/measuresOutput";
import { Output } from "../helpers/output/output";
import { VariableType, VariablesOutput } from "../helpers/output/variablesOutput";
import { FactTypesForNewModel, Setup } from "../helpers/setup/setup";
import { TakeOverDialog } from "../helpers/setup/takeOverDialog";
import { NodeMeasures } from "../helpers/sources/NodeMeasures";
import { Diagram } from "../helpers/sources/diagram";
import { NodeParameters } from "../helpers/sources/nodeParameters";
import { NodeProperties } from "../helpers/sources/nodeProperties";
import { VariableDetails } from "../helpers/variables/variableDetails";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    Setup.setupBeforeEach(
      {
        DWCO_MODELING_ANALYTIC_MODEL_STACKING: true,
        DWCO_MODELING_AM_NEW_DIM_HANDLING: true,
        DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE: false,
      },
      true
    );
    Setup.createNewModel(
      {
        loadDataEntityDetails: "querybuilder/stacking/loadDataEntityDetailsStackedVariablesAM1",
        convertAnalyticalDataset: "querybuilder/stacking/convertAnalyticalDatasetStackedVariables",
        designObjectFolder: "querybuilder/stacking/designObjectsFolderIds",
        allAnalyticModels: "querybuilder/allReusableAnalyticModels",
      },
      false,
      { factType: FactTypesForNewModel.ANALYTIC_MODEL, name: "AM1_Stacking_Variable" }
    );

    Setup.clickOnAddAllAttributesOnTakeoverDialog();
    Setup.clickOnAddAllMeasuresOnTakeoverDialog();
    cy.get("#TakeOverDialog--importParameterList").should("exist");
    cy.get(
      `#${"TakeOverDialog--importParameterList"} tbody tr:not(.sapUiTableHeaderRow):not(.sapUiTableRowHidden)`,
      Common.original
    ).should("have.length", 3);

    Setup.clickOnAddAllMeasuresOnTakeoverDialog();
    Setup.Assert.variablesTableRowCountIs(6);
    // Check the action type with DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE false has only 1 option and the row is enabled
    // changed the assertActionTypeSelectLength to 2 when the feature is activated
    // check for Variable "Country"
    TakeOverDialog.clickOnActionType(0, 1);
    TakeOverDialog.assertActionTypeSelectLength(1);
    TakeOverDialog.assertActionTypeEnable(0, 1);

    Setup.completeTakeoverDialog();

    Output.expectModelPropertiesToBeReady();
  });

  it("Create Analytic Model with stacked variables and check (Copy properties dialog, fact source detail panel, Output Structure Variable section)", () => {
    Diagram.openSourceNodeUsingDiagram({ name: "AM1_Stacking Variable" });
    cy.get(`#${QB_BUTTONS.SOURCE_STACKED_VARIABLE_LIST}`).find("li").should("have.length", 6);
    NodeMeasures.clickOnMeasure(5);
    NodeParameters.countVariablesForStackedSource(5);
    // click on inactive segment button & check the variables list
    NodeParameters.switchToInactiveParameters();
    NodeParameters.countVariablesForStackedSource(2);
    // go back and check variables
    NodeProperties.breadcrumbSelectNavBack();
    VariablesOutput.countVariables(4);
    Diagram.openSourceNodeUsingDiagram({ name: "AM1_Stacking Variable" });
    NodeParameters.countVariablesForStackedSource(5);
    NodeParameters.switchToInactiveParameters();
    NodeParameters.countVariablesForStackedSource(2);
    // unselect Restricted measure and check inactive variable list
    NodeMeasures.clickOnMeasure(4);
    NodeParameters.countVariablesForStackedSource(4);
    NodeParameters.switchToInactiveParameters();
    NodeParameters.countVariablesForStackedSource(3);
    // select Restricted measure and check active variable list
    NodeMeasures.clickOnMeasure(4);
    NodeParameters.countVariablesForStackedSource(5);
    // go back and check variables
    NodeProperties.breadcrumbSelectNavBack();
    // Open Restricted measure
    MeasuresOutput.openMeasure(4);
    // select the is-auxiliary checkbox & check if does not affect the inactive list
    Settings.toggleIsAuxiliaryCheckbox();
    MeasureDetails.breadcrumbSelectNavBack();
    Diagram.openSourceNodeUsingDiagram({ name: "AM1_Stacking Variable" });
    NodeParameters.switchToInactiveParameters();
    NodeParameters.countVariablesForStackedSource(2);
  });

  it("Check the order of the stacked variables when a new restricted measure variable is added", () => {
    VariablesOutput.createVariable(VariableType.RESTRICTION_VARIABLE);
    VariableDetails.changeBusinessNameOfVariable("Restricted Measure");
    // Go back
    VariableDetails.breadcrumbSelectNavBack();
    VariablesOutput.countVariables(6);
    // check the list order
    cy.get(`[id*=${QB_EXPRESSION_EDITOR.VARIABLE_LIST}] div li`, { cyOriginal: true }).then(($items) => {
      const allVariableItems = Array.from($items);
      // Extract the text from the list items
      const texts = allVariableItems.map((variableItem) => variableItem.innerText);
      // Assert the order
      expect(texts).to.deep.equal([
        "Restricted Measure",
        "SRC_STRING",
        "Restricted Measure Variable",
        "Reference Date",
        "Opportunity",
        "Standard Variable",
      ]);
      // click info popover for stack variable and check entries
      VariablesOutput.openVariablePopover(2);
      cy.get(`#${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--VariableInfoPopover--variableFormTitle`)
        .find("span")
        .contains("Restricted Measure Variable", { cyOriginal: true });
      cy.get(`#${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--VariableInfoPopover--technicalName`).contains(
        "RESTRICTED_MEASURE_VARIABLE",
        {
          cyOriginal: true,
        }
      );
      cy.get(`#${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--VariableInfoPopover--variableFilterTypeText`).contains(
        "filterVarSelectionTypeInterval",
        { cyOriginal: true }
      );
      cy.get(`#${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--VariableInfoPopover--variableDefaultValueText`).contains(
        "variableInfoDefaultValueIntervalText(0,2001)",
        { cyOriginal: true }
      );
      cy.get(`#${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--VariableInfoPopover--variableSourceLink`).contains(
        "AM1_Stacking Variable",
        {
          cyOriginal: true,
        }
      );
      cy.get(
        `#${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--VariableInfoPopover--variableSourceFieldFactSource`
      ).contains("Restricted Measure Variable", { cyOriginal: true });
    });
  });
});
