/** @format */

import { QB_BUTTONS, QB_QUERY_MODEL_TAB } from "../../../pageobjects/querybuilder/ComponentsDescriptor";
import { Common } from "../helpers/common";
import { Output } from "../helpers/output/output";
import { VariablesOutput } from "../helpers/output/variablesOutput";
import { FactTypesForNewModel, Setup } from "../helpers/setup/setup";
import { NodeMeasures } from "../helpers/sources/NodeMeasures";
import { Diagram } from "../helpers/sources/diagram";
import { NodeParameters } from "../helpers/sources/nodeParameters";
import { NodeProperties } from "../helpers/sources/nodeProperties";
import { VariablePopover } from "../helpers/variables/variablePopover";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    Setup.setupBeforeEach(
      {
        DWCO_MODELING_ANALYTIC_MODEL_STACKING: true,
        DWCO_MODELING_AM_NEW_DIM_HANDLING: true,
        DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE: true,
        DWCO_MODELING_AM_MULTI_RANGE: true,
      },
      true
    );
  });

  it("check source and source field of stacked variable in Info popup in second level", () => {
    Setup.openQueryModel("AM3_Stacking_Variable", "TESTS_SPACE_1", {
      loadDataEntityDetailsFixture: "querybuilder/stacking/loadDataEntityDetailsStackedVariablesAM2",
      designObjectsIds: "querybuilder/stacking/designObjectsIdsReplaceStackedVariableAM2",
      designObjectsFolderIds: "querybuilder/stacking/designObjectsFolderIds",
    });
    Output.expectModelPropertiesToBeReady();
    // click info popover for stack variable and check entries
    // open the variable popover
    VariablesOutput.openVariablePopover(1);
    // check the info popover title
    cy.get(`#${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--VariableInfoPopover--variableFormTitle`)
      .find("span")
      .contains("Restricted Measure Variable", { cyOriginal: true });
    cy.get(`#${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--VariableInfoPopover--technicalName`).contains(
      "RESTRICTED_MEASURE_VARIABLE",
      {
        cyOriginal: true,
      }
    );
    cy.get(`#${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--VariableInfoPopover--variableFilterTypeText`).contains(
      "filterVarSelectionTypeInterval",
      { cyOriginal: true }
    );
    cy.get(`#${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--VariableInfoPopover--variableDefaultValueText`).contains(
      "variableInfoDefaultValueIntervalText(1001,2001)",
      { cyOriginal: true }
    );
    cy.get(`#${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--VariableInfoPopover--variableSourceLink`).contains(
      "AM2_Stacking Variable",
      {
        cyOriginal: true,
      }
    );
    cy.get(
      `#${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--VariableInfoPopover--variableSourceFieldFactSource`
    ).contains("Restricted Measure Variable", { cyOriginal: true });
  });

  it("check parameter active list with usage of look up entity", () => {
    Setup.openQueryModel("GC_TEST_DERIVATION_2", "TESTS_SPACE_1", {
      loadDataEntityDetailsFixture: "querybuilder/stacking/loadDataEntityDetailsStackedVariablesAM3",
      designObjectsIds: "querybuilder/stacking/designObjectsIdsReplaceStackedVariableAM3.json",
      designObjectsFolderIds: "querybuilder/stacking/designObjectsFolderIds",
    });
    Output.expectModelPropertiesToBeReady();

    VariablesOutput.countVariables(5);

    Diagram.openSourceNodeUsingDiagram({ name: "GC_TEST_DERIVATION" });
    NodeParameters.countVariablesForStackedSource(5);

    NodeParameters.switchToInactiveParameters();
    NodeParameters.countVariablesForStackedSource(8);
    // select Calculated_Measure4 and check active/inactive variable list
    NodeMeasures.clickOnMeasure(12);
    NodeParameters.countVariablesForStackedSource(8);
    NodeParameters.switchToInactiveParameters();
    NodeParameters.countVariablesForStackedSource(5);
    // go back and check variables
    NodeProperties.breadcrumbSelectNavBack();
    VariablesOutput.countVariables(8);
    Diagram.openSourceNodeUsingDiagram({ name: "GC_TEST_DERIVATION" });
    // unselect Calculated_Measure4 and check active/inactive variable list
    NodeMeasures.clickOnMeasure(12);
    NodeParameters.countVariablesForStackedSource(5);
    NodeParameters.switchToInactiveParameters();
    NodeParameters.countVariablesForStackedSource(8);
    // go back and check variables
    NodeProperties.breadcrumbSelectNavBack();
    VariablesOutput.countVariables(5);
  });

  it("check parameter active list with usage of variables in cal measure with hidden flag", () => {
    Setup.createNewModel(
      {
        loadDataEntityDetails: "querybuilder/stacking/loadDataEntityDetailsStackedVariablesAM4",
        convertAnalyticalDataset: "querybuilder/stacking/convertAnalyticalDatasetStackedVariables2",
        designObjectFolder: "querybuilder/stacking/designObjectsFolderIds",
        allAnalyticModels: "querybuilder/allReusableAnalyticModels",
      },
      false,
      { factType: FactTypesForNewModel.ANALYTIC_MODEL, name: "GC_TEST_DERIVATION_2" }
    );
    Setup.clickOnAddAllAttributesOnTakeoverDialog();
    Setup.clickOnAddAllMeasuresOnTakeoverDialog();
    cy.get("#TakeOverDialog--importParameterList").should("exist");
    cy.get(
      `#${"TakeOverDialog--importParameterList"} tbody tr:not(.sapUiTableHeaderRow):not(.sapUiTableRowHidden)`,
      Common.original
    ).should("have.length", 3);
    Setup.clickOnAddAllMeasuresOnTakeoverDialog();
    Setup.Assert.variablesTableRowCountIs(5);
    Setup.completeTakeoverDialog();
    Output.expectModelPropertiesToBeReady();

    VariablesOutput.countVariables(5);
    Diagram.openSourceNodeUsingDiagram({ name: "GC_TEST_DERIVATION_2" });
    NodeParameters.countVariablesForStackedSource(5);

    // check the list order of stacked variables in fact source details screen
    cy.get(`[id*=${QB_BUTTONS.SOURCE_STACKED_VARIABLE_LIST}] div li`, { cyOriginal: true }).then(($items) => {
      const allVariableItems = Array.from($items);
      // Extract the text from the list items
      const texts = allVariableItems.map((variableItem) => variableItem.innerText);
      // Assert the order
      expect(texts).to.deep.equal([
        "Currency\ntxtInherit",
        "PARAM_1_1\ntxtInherit",
        "Date\ntxtInherit",
        "Date_from\ntxtInherit",
        "STANDARD_VARIABLE_DYNAMIC_PROD\ntxtInherit",
      ]);
    });
    NodeParameters.switchToInactiveParameters();
    NodeParameters.countVariablesForStackedSource(0);
  });

  it("check icon for hidden variables", () => {
    Setup.openQueryModel("AM3_Stacking_Variable", "TESTS_SPACE_1", {
      loadDataEntityDetailsFixture: "querybuilder/stacking/loadDataEntityDetailsStackedVariablesAM2",
      designObjectsIds: "querybuilder/stacking/designObjectsIdsReplaceStackedVariableAM2",
      designObjectsFolderIds: "querybuilder/stacking/designObjectsFolderIds",
    });
    Output.expectModelPropertiesToBeReady();

    VariablesOutput.scrollVariableIntoView(3);
    VariablePopover.checkVariableType(3, "inheritedVariable");
    VariablesOutput.checkHiddenIcon(3);

    VariablesOutput.scrollVariableIntoView(4);
    VariablePopover.checkVariableType(4, "inheritedVariable");
    VariablesOutput.checkHiddenIcon(4);
  });
});
