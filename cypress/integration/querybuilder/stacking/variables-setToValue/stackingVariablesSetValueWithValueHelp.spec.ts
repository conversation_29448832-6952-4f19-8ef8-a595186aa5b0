/** @format */

import * as BuilderCommon from "../../../../pageobjects/querybuilder/QueryBuilderCommon";
import { Output } from "../../helpers/output/output";
import { VariablesOutput } from "../../helpers/output/variablesOutput";
import { FactTypesForNewModel, Setup } from "../../helpers/setup/setup";
import { TakeOverDialog } from "../../helpers/setup/takeOverDialog";
import { Diagram } from "../../helpers/sources/diagram";
import { DefaultRangeOption, FilterType, NodeParameters } from "../../helpers/sources/nodeParameters";
import { NodeProperties } from "../../helpers/sources/nodeProperties";
import { ValueHelpDialog } from "../../helpers/valueHelp/valueHelpDialog";

describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    Setup.setupBeforeEach(
      {
        DWCO_MODELING_ANALYTIC_MODEL_STACKING: true,
        DWCO_MODELING_AM_NEW_DIM_HANDLING: true,
        DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE: true,
        DWCO_MODELING_AM_MULTI_RANGE: true,
      },
      true
    );
  });
  describe("Open Analytic Model and set value the stacked variables with value help in source node", () => {
    beforeEach(() => {
      Setup.openQueryModel("Variable_Used_In_Expression_Stack2", "TESTS_SPACE_1", {
        loadDataEntityDetailsFixture: "querybuilder/stacking/loadDataEntityDetailsStackedVariablesAM5",
        designObjectsIds: "querybuilder/stacking/designObjectsIdsReplaceStackedVariableAM5",
        designObjectsFolderIds: "querybuilder/stacking/designObjectsFolderIds",
      });
      Output.expectModelPropertiesToBeReady();
      cy.intercept("GET", BuilderCommon.REQUESTS.REPOSITORY_DESIGN_OBJECTS_IDS, {
        fixture: "querybuilder/stacking/designObjectsIdsReplaceStackedVariable_ValueHelp",
      }).as("designObjectsIdsValueHelp");

      cy.intercept(
        "GET",
        "**/odata/v4/consumption/analytical/TESTS_SPACE_1/Variable_Used_In_Expression_Stack1/Variable_Used_In_Expression_Stack1?$top=100&$select=Product,Quantity&$orderby=Product",
        {
          fixture: "querybuilder/stacking/valueHelpProduct",
        }
      ).as("valueHelp");
    });

    it("value help should work for stacked variable set to value and check info popup for new value", () => {
      VariablesOutput.countVariables(3);
      Diagram.openSourceNodeUsingDiagram({ name: "Variable_Used_In_Expression_Stack1" });
      NodeParameters.countVariablesForStackedSource(4);
      // Check displayed default value in Info popover
      NodeParameters.scrollStackedParameterIntoView(2);
      NodeParameters.openStackedParameterPopover(2);
      NodeParameters.assertPopoverValue(
        "displayDefaultValueText",
        "variableInfoDefaultValueIntervalText(800000101,800000115)"
      );
      NodeParameters.assertNewDefaultValueNotExist();
      // go back and check variables
      NodeProperties.breadcrumbSelectNavBack();
      Diagram.openSourceNodeUsingDiagram({ name: "Variable_Used_In_Expression_Stack1" });
      // set value for Range variable
      NodeParameters.openStackedVariableSetValueDialog(1);
      // Change filter type to Range
      NodeParameters.changeFilterType(FilterType.RANGE);
      NodeParameters.expandRangeOption(DefaultRangeOption.EQ);
      NodeParameters.openValueHelpForRangeValue(0);
      cy.wait(["@designObjectsIdsValueHelp", "@valueHelp"]);
      // select value from value help
      ValueHelpDialog.clickOnValueHelpItem(1);
      NodeParameters.okSetValueDialog();
      NodeParameters.assertNodeParameterInputValueWithValueHelp("= PR-1001");
      // Check displayed default value in Info popover with New Default Value
      NodeParameters.scrollStackedParameterIntoView(1);
      NodeParameters.openStackedParameterPopover(1);
      NodeParameters.assertPopoverValue(
        "displayDefaultValueText",
        "variableInfoDefaultValueIntervalText(PR-1004,PR-1012)"
      );
      NodeParameters.assertPopoverValue("displayNewDefaultValueText", "= PR-1001");
    });

    it("should handle filter type change and value help for stacked variable", () => {
      VariablesOutput.countVariables(3);
      Diagram.openSourceNodeUsingDiagram({ name: "Variable_Used_In_Expression_Stack1" });
      NodeParameters.countVariablesForStackedSource(4);

      // open set value dialog
      NodeParameters.openStackedVariableSetValueDialog(1);
      // Change filter type to Single Value
      NodeParameters.changeFilterType(FilterType.SINGLE);
      NodeParameters.openValueHelpForSingleValue();
      // Wait for value help data and select a value
      cy.wait(["@designObjectsIdsValueHelp", "@valueHelp"]);
      ValueHelpDialog.clickOnValueHelpItem(3);
      NodeParameters.okSetValueDialog();
      // Verify the selected value
      NodeParameters.assertNodeParameterInputValueWithValueHelp("PR-1003");
      // open set value dialog
      NodeParameters.openStackedVariableSetValueDialog(1);
      // Change filter type to Multiple Single Value
      NodeParameters.changeFilterType(FilterType.MULTIPLE_SINGLE);
      NodeParameters.isOkSetValueDialogDisabled();
      NodeParameters.closeSetValueDialog();
      // Verify the selected value
      NodeParameters.assertNodeParameterInputValueWithValueHelp("PR-1003");
      // open set value dialog again to check if Filter type is SINGLE
      NodeParameters.openStackedVariableSetValueDialog(1);
      // assert filter type is Single
      NodeParameters.assertFilterType("filterVarSelectionTypeSingle");
      NodeParameters.assertNodeParameterInputValueWithValueHelp("PR-1003");
      NodeParameters.closeSetValueDialog();
      // open set value dialog for Country variable
      NodeParameters.openStackedVariableSetValueDialog(3);
      NodeParameters.assertNodeParameterInputValue("IN");
      NodeParameters.closeSetValueDialog();
    });
  });

  describe("Open Analytic Model and set value the stacked variables with value help in copy property panel", () => {
    beforeEach(() => {
      Setup.createNewModel(
        {
          loadDataEntityDetails: "querybuilder/stacking/loadDataEntityDetailsStackedVariablesAM5",
          convertAnalyticalDataset: "querybuilder/stacking/convertAnalyticalDatasetStackedVariables4",
          designObjectFolder: "querybuilder/stacking/designObjectsFolderIds",
          allAnalyticModels: "querybuilder/allReusableAnalyticModels",
        },
        false,
        { factType: FactTypesForNewModel.ANALYTIC_MODEL, name: "Variable_Used_In_Expression_Stack1" }
      );
      cy.intercept("GET", BuilderCommon.REQUESTS.REPOSITORY_DESIGN_OBJECTS_IDS, {
        fixture: "querybuilder/stacking/designObjectsIdsReplaceStackedVariable_ValueHelp",
      }).as("designObjectsIdsValueHelp");

      cy.intercept(
        "GET",
        "**/odata/v4/consumption/analytical/TESTS_SPACE_1/Variable_Used_In_Expression_Stack1/Variable_Used_In_Expression_Stack1?$top=100&$select=Product,Quantity&$orderby=Product",
        {
          fixture: "querybuilder/stacking/valueHelpProduct",
        }
      ).as("valueHelp");

      cy.intercept(
        "GET",
        "**/odata/v4/consumption/analytical/TESTS_SPACE_1/Variable_Used_In_Expression_Stack1/Variable_Used_In_Expression_Stack1?$top=100&$select=ItemID,Quantity&$orderby=ItemID",
        {
          fixture: "querybuilder/sharing/valueHelp/valueHelpItemId",
        }
      ).as("valueHelpItemID");
    });

    it("value help should work for stacked variable set to value in copy property dialog", () => {
      // set value for Range variable (Restricted Variable)
      TakeOverDialog.clickOnActionType(3, 1);
      TakeOverDialog.assertActionTypeSelectLength(2);
      TakeOverDialog.setActionType(1);
      // select low value from value help
      TakeOverDialog.openValueHelpForRangeValue(3, 3, 0);
      cy.wait(["@designObjectsIdsValueHelp", "@valueHelp"]);
      // select value from value help
      ValueHelpDialog.clickOnValueHelpItem(7);
      TakeOverDialog.assertRangeInputValue(3, 3, 0, "PR-1007");
      // select high value from value help
      TakeOverDialog.openValueHelpForRangeValue(3, 3, 0, true);
      cy.wait(["@valueHelp"]);
      // select value from value help
      ValueHelpDialog.clickOnValueHelpItem(9);
      TakeOverDialog.assertRangeInputValue(3, 3, 0, "PR-1009", true);

      // DW15-3790# TODO- the block is commented because of the issue with Action type for the next row, with update of cypress version we will uncomment it
      // set value for Interval variable (RESTRICTED_MEASURE_VARIABLE_1)
      /* TakeOverDialog.clickOnActionType(2, 1);
      TakeOverDialog.assertActionTypeSelectLength(2);
      TakeOverDialog.setActionType(1);
      // select low value from value help
      TakeOverDialog.openValueHelpForIntervalValue(2, 3, 0);
      cy.wait(["@valueHelpItemID"]);
      // select value from value help
      ValueHelpDialog.clickOnValueHelpItem(3);
      TakeOverDialog.assertIntervalInputValue(2, 3, 0, "800000103");*/

      Setup.completeTakeoverDialog();
      Output.expectModelPropertiesToBeReady();

      VariablesOutput.countVariables(4);
      Diagram.openSourceNodeUsingDiagram({ name: "Variable_Used_In_Expression_Stack1" });
      NodeParameters.countVariablesForStackedSource(6);
    });
  });
});
