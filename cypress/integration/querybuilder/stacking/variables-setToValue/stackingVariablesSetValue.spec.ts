/** @format */

import { QB_BUTTONS, QB_EXPRESSION_EDITOR } from "../../../../pageobjects/querybuilder/ComponentsDescriptor";
import { Common } from "../../helpers/common";
import { Output } from "../../helpers/output/output";
import { VariablesOutput } from "../../helpers/output/variablesOutput";
import { FactTypesForNewModel, Setup } from "../../helpers/setup/setup";
import { TakeOverDialog } from "../../helpers/setup/takeOverDialog";
import { Diagram } from "../../helpers/sources/diagram";
import { FilterType, NodeParameters } from "../../helpers/sources/nodeParameters";
import { NodeProperties } from "../../helpers/sources/nodeProperties";
import { ValueHelpDialog } from "../../helpers/valueHelp/valueHelpDialog";
describe(Cypress.spec.relative, () => {
  beforeEach(() => {
    Setup.setupBeforeEach(
      {
        DWCO_MODELING_ANALYTIC_MODEL_STACKING: true,
        DWCO_MODELING_AM_NEW_DIM_HANDLING: true,
        DWCO_MODELING_AM_SET_VALUE_STACKING_VARIABLE: true,
        DWCO_MODELING_AM_MULTI_RANGE: true,
      },
      true
    );
  });

  it("Create Analytic Model with stacked variables and check (Copy properties dialog, fact source detail panel, Output Structure Variable section)", () => {
    Setup.createNewModel(
      {
        loadDataEntityDetails: "querybuilder/stacking/loadDataEntityDetailsStackedVariablesAM1",
        convertAnalyticalDataset: "querybuilder/stacking/convertAnalyticalDatasetStackedVariables3",
        designObjectFolder: "querybuilder/stacking/designObjectsFolderIds",
        allAnalyticModels: "querybuilder/allReusableAnalyticModels",
      },
      false,
      { factType: FactTypesForNewModel.ANALYTIC_MODEL, name: "AM1_Stacking_Variable" }
    );
    // select action Type to set value
    TakeOverDialog.clickOnActionType(3, 1);
    TakeOverDialog.assertActionTypeSelectLength(2);
    TakeOverDialog.setActionType(1);
    // select filter Type to Range
    TakeOverDialog.clickOnFilterType(3, 2);
    TakeOverDialog.setFilterType(3);
    // select multi Range
    TakeOverDialog.assertRangeOptionValue(3, 3, "=");
    TakeOverDialog.expandRangeOption(3, 3, 0);
    TakeOverDialog.changeRangeOption(2);
    TakeOverDialog.assertRangeOptionValue(3, 3, "<=");
    TakeOverDialog.setRangeValueInput(3, 3, 0, "1515");
    TakeOverDialog.addNewValueForMultiValueOrRange(3, 3);
    TakeOverDialog.setRangeValueInput(3, 3, 1, "1516");
    TakeOverDialog.deleteValueForMultiValueOrRange(1);
    // reference date variable should not have set value option
    TakeOverDialog.assertActionTypeEnable(2, 1);
    Setup.completeTakeoverDialog();
    Output.expectModelPropertiesToBeReady();

    VariablesOutput.countVariables(4);
    Diagram.openSourceNodeUsingDiagram({ name: "AM1_Stacking Variable" });
    cy.get(`#${QB_BUTTONS.SOURCE_STACKED_VARIABLE_LIST}`).find("li").should("have.length", 6);
    cy.get(`ul[id*=availableStackedParameterList] :nth-child(4) li .token-details .sapMLabel`, Common.original).should(
      "contain",
      "txtValueWithPlaceHolder(<= 1515)"
    );
    // set value for Range variable
    NodeParameters.openStackedVariableSetValueDialog(3);
    NodeParameters.addNewValueForMultiValueOrRange();
    NodeParameters.setRangeValueInputInSourceDetail(1, "1516");
    cy.get(`ul[id*=availableStackedParameterList] :nth-child(4) li .token-details .sapMLabel`, Common.original).should(
      "contain",
      "txtValueWithPlaceHolder(<= 1515, = 1516)"
    );
    NodeParameters.openStackedVariableSetValueDialog(3);
    NodeParameters.deleteValueForMultiValueOrRange(1);
    NodeParameters.okSetValueDialog();
    cy.get(`ul[id*=availableStackedParameterList] :nth-child(4) li .token-details .sapMLabel`, Common.original).should(
      "contain",
      "txtValueWithPlaceHolder(<= 1515)"
    );
    // set value for Interval variable
    NodeParameters.openStackedVariableSetValueDialog(1);
    cy.get("[id*=stackedVariableFilterType").should("be.visible");
    cy.get(
      `#${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_MULTI_RANGE} [id*=multiRangeList-listUl] li:nth-child(1) [id*=variableDefaultRangeOption][id*=labelText]`
    ).should("contain", "=", Common.original);

    cy.get(
      `#${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_MULTI_RANGE} [id*=multiRangeList-listUl] li:nth-child(1) input[id*=inputLow]`
    ).should("have.value", "");
    // Change filter type to Interval
    NodeParameters.changeFilterType(FilterType.INTERVAL);
    cy.get(
      `#${QB_EXPRESSION_EDITOR.PARAMETER_DEFAULTVAL_INTERVAL} [id*=multiRangeList-listUl] li:nth-child(1) [id*=variableDefaultRangeOption][id*=labelText]`
    ).should("contain", "[ ]", Common.original);
    NodeParameters.setIntervalValueInputInSourceDetail(0, "1003", true);
    cy.get(
      `ul[id*=availableStackedParameterList] :nth-child(2) li .token-details .sapMLabel`,
      Common.original
    ).contains("txtValueWithPlaceHolder(variableInfoDefaultValueIntervalText({0},1003))", Common.original);
    NodeParameters.openStackedVariableSetValueDialog(3);
    NodeParameters.setValueForInput("test");
    // go back and check variables
    NodeProperties.breadcrumbSelectNavBack();
    VariablesOutput.countVariables(3);
  });

  it("Create Analytic Model with stacked variables and check value help", () => {
    Setup.createNewModel(
      {
        loadDataEntityDetails: "querybuilder/stacking/loadDataEntityDetailsStackedVariablesAM6",
        designObjectFolder: "querybuilder/stacking/designObjectsFolderIds",
        allAnalyticModels: "querybuilder/allReusableAnalyticModels",
        designObjectId: "querybuilder/stacking/designObjectsIdsReplaceStackedVariable_ValueHelp2.json",
      },
      false,
      { factType: FactTypesForNewModel.ANALYTIC_MODEL, name: "MCT_VALUEHELP_Params_AM1" }
    );

    cy.intercept("GET", "**/repository/*/designObjects?ids=3B**", {
      fixture: "querybuilder/sqlDataPreview/designObjectsAM_Products_Dim",
    }).as("designObjects2");

    cy.intercept("POST", "**/dwaas-core/data-access/valuehelp/instant/TESTS_SPACE_1/**", {
      fixture: "querybuilder/valueHelpProducts",
    }).as("valueHelp");

    // select action Type to set value
    TakeOverDialog.clickOnActionType(0, 1);
    TakeOverDialog.assertActionTypeSelectLength(2);
    TakeOverDialog.setActionType(1);
    TakeOverDialog.assertFilterIsNotApplicable(0, 2);
    TakeOverDialog.openValueHelpForSingleValue(0, 3);
    cy.wait("@designObjects2");
    cy.wait("@valueHelp");
    // select value from value help
    ValueHelpDialog.clickOnValueHelpItem(3);
    Setup.completeTakeoverDialog();
    Output.expectModelPropertiesToBeReady();
  });
});
