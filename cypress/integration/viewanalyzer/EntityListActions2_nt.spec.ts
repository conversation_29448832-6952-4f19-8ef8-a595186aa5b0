/** @format */

import {
  openViewMonitorDetailsPage,
  prepareTestEnvironment,
  pressButton,
  selectRadioButton,
} from "../../pageobjects/dataIntegration/DataIntegrationHelper";

/* jscpd:ignore-start */
describe("cypress/integration/viewanalyzer/EntityListActions2_Nt", () => {
  beforeEach(() => {
    prepareTestEnvironment({
      DWC_DUMMY_SPACE_PERMISSIONS: false,
    });

    cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });

    cy.intercept("GET", "**/schedules?**", []).as("getSchedules");
    cy.intercept("GET", "**/persistedViews/View**", { fixture: "viewmonitor/persistedView3Details" }).as(
      "getPersistedViewDetails"
    );
    cy.intercept("GET", "**/logs?objectId=View1&getLocks=true", {
      locks: [],
      logs: [
        {
          logId: 3387666,
          spaceId: "SHUBHAM_NEW",
          applicationId: "VIEWS",
          objectId: "JoinCardianality_PeakMemory1",
          activity: "EXECUTE_VIEW_ANALYZER",
          startTime: "2023-01-12T04:25:54.544Z",
          endTime: "2023-01-12T04:25:57.000Z",
          status: "COMPLETED",
          user: "Shubham Rai",
          runId: "c86ee801-e52c-4aa9-7a46-4d581e74622f",
          runTime: 2456,
        },
        {
          logId: 3387664,
          spaceId: "SHUBHAM_NEW",
          applicationId: "VIEWS",
          objectId: "JoinCardianality_PeakMemory1",
          activity: "EXECUTE_VIEW_ANALYZER",
          startTime: "2023-01-12T04:24:54.509Z",
          endTime: "2023-01-12T04:25:10.000Z",
          status: "COMPLETED",
          user: "Shubham Rai",
          runId: "8f1cb660-6aaa-4cab-529b-840b96d379e3",
          runTime: 15491,
        },
      ],
    }).as("getPersistencyLogs");
    cy.intercept("GET", "**/logs?taskLogId=3387666", { fixture: "viewmonitor/logmessages" }).as("getMessages");
    cy.intercept("GET", "**/result/3387666", { fixture: "viewanalyzer/analyzerDetails" }).as("getanalyzerlogDetails");
    cy.intercept("GET", "**/schedules/consent", { consent: false }).as("getConsent");
    cy.intercept("GET", "**/resources/spaces?islocked&spaceids=SPACE1234**", { SPACE1234: { isLocked: false } }).as(
      "getSpaceLockStatus"
    );
    cy.intercept("GET", "**/repository/designObjects?folderIds=**", {
      fixture: "viewanalyzer/designObjectsFolderIds",
    }).as("designObjects1");

    cy.intercept("GET", "**/security/customerhana/info", {
      ram: 256000000000,
      storage: 256000000000,
      threads: 128,
    }).as("getCustomerHanaDetails");
    cy.intercept("GET", "**content?space**", {
      SHUBHAM_NEW: {
        spaceDefinition: {
          version: "1.0.4",
          label: "Shubham_New",
          assignedStorage: 2200000000,
          assignedRam: 1000000000,
          enableDataLake: false,
          priority: 5,
          auditing: {
            dppRead: {
              retentionPeriod: 7,
              isAuditPolicyActive: false,
            },
            dppChange: {
              retentionPeriod: 7,
              isAuditPolicyActive: false,
            },
          },
          hdicontainers: {},
          workloadClass: {
            totalStatementMemoryLimit: {
              value: 1,
              unit: "Gigabyte",
            },
            totalStatementThreadLimit: {
              value: 14,
              unit: "Counter",
            },
          },
          workloadType: "custom",
          allowConsumption: false,
        },
      },
    }).as("getSpaceDetails");
    cy.intercept("POST", "**/execute/**", {}).as("startAnalysis");
    openViewMonitorDetailsPage("SPACE1234", "View1");
    cy.wait(["@getUser", "@getContractEnddate", "@getPersistedViewDetails", "@getanalyzerlogDetails"]);

    cy.get("#shellMainContent---dataIntegrationComponent---taskLog--detailLogTable").then((element) => {
      cy.window().then((w) => {
        const entities = w.sap.ui.core.Element.closestTo(element[0]) as sap.uxap.ObjectPageSection;
        entities.setSelectedSection("shellMainContent---dataIntegrationComponent---taskLog--Entities");
      });
    });
  });

  describe("View Analyzer Entity List View", () => {
    it("Entity Action - Navigate to Editor & Monitor - Local Tables", () => {
      cy.get("#entitiesListView--entitiesListTable").then((element) => {
        cy.window().then((w) => {
          const entitiesTable = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table;
          entitiesTable?.setSelectedItem(entitiesTable.getItems()[0], true, true);
        });
      });
      cy.get("#entitiesListView--navigationMenuEntities").then((element) => {
        cy.window().then((w) => {
          const menu = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.MenuButton;
          expect(menu.getEnabled()).to.equal(true);
        });
      });
    });

    it("Entity Table - Filter Dialog", () => {
      pressButton("entitiesListView--entitiesFilter");
      cy.byId("Settings--entityFilterDialog").then((dialog: sap.m.ViewSettingsDialog) => {
        dialog.setSelectedFilterCompoundKeys({ dataAccess: { dataAccess___EQ___Virtual: true } }).fireConfirm({
          filterItems: [dialog.getFilterItems()[2]],
          filterKeys: { dataAccess: { dataAccess___EQ___Virtual: true } },
        });
      });
    });

    it("Entity Table - Filter Strip", () => {
      pressButton("entitiesListView--entitiesFilter");
      cy.byId("Settings--entityFilterDialog").then((dialog: sap.m.ViewSettingsDialog) => {
        dialog.setSelectedFilterCompoundKeys({ dataAccess: { dataAccess___EQ___Virtual: true } }).fireConfirm({
          filterItems: [dialog.getFilterItems()[2]],
          filterKeys: { dataAccess: { dataAccess___EQ___Virtual: true } },
        });
        pressButton("Settings--entityFilterDialog-acceptbutton");
        cy.get("#entitiesListView--entitiesTableFilterBarText").should(
          "include.text",
          "Filtered By: Data Access (Virtual)"
        );
        cy.get("#entitiesListView--FilterStripClearButton").should("include.text", "Clear Filter");
        pressButton("entitiesListView--FilterStripClearButton");
      });
    });

    it("Entity Table - Duration Field", () => {
      cy.get("#entitiesListView--entitiesListTable").then((element) => {
        cy.window().then((w) => {
          const entitiesTable = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table;
          expect(entitiesTable.getItems()[0].getCells()[6].getText()).to.equal("00:20:33");
        });
      });
    });

    it("Entity Action - Inspect Search", async () => {
      pressButton("entitiesListView--inspectButton");
      cy.get("#entitiesListView--AnalyzerDetailsDialog--persistencyAadvisorDialog").should("exist");
      cy.get("#entitiesListView--AnalyzerDetailsDialog--advisorSearch").then((element) => {
        cy.window().then((w) => {
          const searchField = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.SearchField;
          searchField.setValue("joins");
          searchField.fireLiveChange({ newValue: "joins" });
        });
      });
      pressButton("entitiesListView--AnalyzerDetailsDialog--okAdvisor");
      pressButton("entitiesListView--inspectButton");
      cy.get("#entitiesListView--AnalyzerDetailsDialog--persistencyAadvisorDialog").should("exist");
      cy.get("#entitiesListView--AnalyzerDetailsDialog--advisorSearch").then((element) => {
        cy.window().then((w) => {
          const searchField = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.SearchField;
          expect(searchField.getValue()).to.equal("");
        });
      });
    });

    it("Check request body whe starting planviz from entities list", () => {
      cy.get("#entitiesListView--entitiesListTable").then((element) => {
        cy.window().then((w) => {
          const entitiesTable = w.sap.ui.core.Element.closestTo(element[0]) as sap.m.Table;
          entitiesTable?.setSelectedItem(entitiesTable.getItems()[2], true, true);
        });
      });
      pressButton("entitiesListView--startAnalyzerButton");
      cy.wait(["@getCustomerHanaDetails", "@getSpaceDetails"]);
      selectRadioButton("entitiesListView--startAnalyzerrDialog--generatePlanVizBtn");
      pressButton("entitiesListView--startAnalyzerrDialog--startAnalyzer");

      cy.wait("@startAnalysis").then((xhr) => {
        expect(xhr.request.body.viewWithPlanViz).to.include({
          entity: "JoinCardianality_PeakMemory1",
        });
      });
    });
  });
});
/* jscpd:ignore-end */
