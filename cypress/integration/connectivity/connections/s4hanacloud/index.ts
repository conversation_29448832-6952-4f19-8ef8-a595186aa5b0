/**
 * Copyright 2024 SAP SE or an SAP affiliate company. All rights reserved.
 * @format
 */

/* eslint-disable camelcase */

import * as IDS from "../../constants/uilds_new";
import { checkRequestData, goToNextStep, insertFile, setDropdownValue, setInputValue } from "../../constants/utils";

export const featureFlags = {
  DWCO_USER_PROPAGATION: true,
  DWCO_BDC_DP_INGESTION_SPACE: true,
  DWCO_CONNECTION_RUCKSACK_S4CLOUD: true,
};

const businessName = "Test S4HANA Cloud Connection";
const technicalName = "test_s4hana_cloud_connection";
// dpAgent and tunnel data
const selectedDpAgentValue = "dwc_managed_dp_agent";

const host = "s4hanacloud.example.com";
const port = "16789";
const client = "003";
const sysid = "S4H";
const langcode = "DE";

// scc data
const virtualHost = "virtualHost";
const virtualPort = "20001";

const authTypes = ["basic", "x509 client certificate"];
const user = "testUser";
const password = "testPassword";

const cert = "";
const key = "";

const clientCertificateFilePath = "connectivity/credentialX509Certificate.pem";
const clientPrivateKeyFilePath = "connectivity/credentialX509Key.key";

const abapSQLServiceBinding = "MySchema";
const iasApplicationDependencyName = "MyApp";

const CREATE_PAYLOAD = {
  businessName,
  name: technicalName,
  description: "",
  credentialMode: "technicaluser",
  configuration: {
    ConnectionProperties: {
      ConnectionInfo: {
        host,
        client,
        sysid,
        langcode,
        port,
        enableUserPropagation: "false",
        abapSQLServiceBinding: "",
        iasApplicationDependencyName: "",
        authentication: {
          // use basic as default auth type
          auth_type: "Basic",
        },
      },
    },
    CredentialProperties: {
      credentials_mode: "technicaluser",
      credential: { user, password },
    },
    ConnectionFeatures: {
      features: {
        remoteTables: false,
        dpAgent: "",
        modelImport: false,
        dataflows: true,
        replicationflows: true,
        useFastSerialization: "true",
      },
    },
  },
  typeId: "SAPS4HANACLOUD",
  capabilityHanaSdi: "false",
  capabilityModelTransfer: "false",
  capabilityDataflowSource: "true",
  capabilityReplicationflowSource: "true",
  repositoryPackage: "_NONE_KEY_PACKAGE_",
};

// expected data for the edited connection
export const expectedData = {
  businessName: "A-S4-HANA-Cloud",
  name: "SPACEDETAILSTEST_F",
  description: "",
  credentialMode: "technicaluser",
  configuration: {
    ConnectionProperties: {
      ConnectionInfo: {
        host,
        client,
        sysid,
        langcode,
        port,
        enableUserPropagation: "false",
        abapSQLServiceBinding: "",
        iasApplicationDependencyName: "",
        authentication: {
          auth_type: "Basic",
        },
      },
    },
    CredentialProperties: {
      credentials_mode: "technicaluser",
      credential: {
        user: user,
        password: password,
      },
    },
    ConnectionFeatures: {
      features: {
        dataProvisioningOption: "none",
        remoteTables: false,
        dpAgent: "",
        modelImport: false,
        dataflows: true,
        replicationflows: true,
        useFastSerialization: "false",
      },
    },
  },
  typeId: "SAPS4HANACLOUD",
  capabilityHanaSdi: "false",
  capabilityModelTransfer: "false",
  capabilityDataflowSource: "true",
  capabilityReplicationflowSource: "true",
  repositoryPackage: "_NONE_KEY_PACKAGE_",
};

export const s4hanaCloudTestData = {
  host,
  port,
  client,
  sysid,
  langcode,
  user,
  password,
};

export function createConnection(
  requestData: Record<string, unknown>,
  isBasicAuth: boolean,
  dpOption: string = "none",
  userPropagation = false
) {
  cy.byId(IDS.NAVIGATOR).then((wizard: sap.m.Wizard) => {
    expect(wizard.getStepCount()).to.equal(3);
  });
  cy.wait(1000);
  inputS4HANACloudDetails(isBasicAuth, dpOption, userPropagation);
  goToNextStep();
  inputBusinessAndTechNames();
  goToNextStep();

  checkRequestData(requestData);
}

function getRemoteTableOptions(dpOption: string) {
  const dataAccess = dpOption === "dpAgent" ? "federationAndReplication" : "federationOnly";
  // set default abap odbc params
  const abapOdbcParameters = {
    uidType: "alias",
    typeMap: "semantic",
  };
  return {
    dataProvisioningOption: dpOption,
    ...(dpOption !== "none" && { dataAccess }),
    ...(dpOption === "direct" && { abapOdbcParameters }),
  };
}

export function createPayload(isBasicAuth: boolean, dpOption: string = "none", userPropagation = false) {
  const payload = Cypress._.cloneDeep<any>(CREATE_PAYLOAD);

  // for testing only check the data provisioning option. In real scenario, the
  // dp agent must be selected when dp option is 'dpAgent'
  const isRemoteTablesEnabled = ["dpAgent", "direct"].includes(dpOption);
  const isModelImportEnabled = dpOption === "dpAgent";
  const isDataflowEnabled = !userPropagation;
  const isReplicationflowEnabled = !userPropagation;

  // remote tables are only enabled when data provisioning option 'dpAgent' or 'direct' is used
  payload.capabilityHanaSdi = `${isRemoteTablesEnabled}`;
  // model import is only enabled for data provisioning option 'dpAgent'
  payload.capabilityModelTransfer = `${isModelImportEnabled}`;
  // dataflows and replicationflows are disabled when user propagation is enabled
  payload.capabilityDataflowSource = `${isDataflowEnabled}`;
  payload.capabilityReplicationflowSource = `${isReplicationflowEnabled}`;

  if (dpOption === "dpAgent") {
    payload.location = {
      agentName: selectedDpAgentValue,
      location: "agent",
      connected: true,
    };
  }

  if (userPropagation) {
    Object.assign(payload.configuration.ConnectionProperties.ConnectionInfo, {
      enableUserPropagation: "true",
      abapSQLServiceBinding,
      iasApplicationDependencyName,
    });
  }

  Object.assign(payload.configuration.ConnectionFeatures.features, {
    ...getRemoteTableOptions(dpOption),
    remoteTables: isRemoteTablesEnabled,
    dpAgent: isModelImportEnabled ? selectedDpAgentValue : "",
    modelImport: isModelImportEnabled,
    dataflows: isDataflowEnabled,
    replicationflows: isReplicationflowEnabled,
  });

  if (!isBasicAuth) {
    // remove basic auth properties and add x509 client certificate properties
    payload.configuration.CredentialProperties = Object.assign(
      Cypress._.omit(payload.configuration.CredentialProperties, "credential"),
      {
        x509_client_credential: {
          x509_client_certificate: cert,
          x509_client_private_key: key,
          x509_client_private_key_password: "",
        },
      }
    );

    payload.configuration.ConnectionProperties.ConnectionInfo.authentication.auth_type = "ClientCertificate";
  }

  return payload;
}

function inputS4HANACloudDetails(isBasicAuth: boolean, dpOption: string = "none", userPropagation = false) {
  cy.get("#" + IDS.S4HANACLOUD_CONN_HOST)
    .should("be.visible")
    .then(() => {
      cy.get("#" + IDS.S4HANACLOUD_CONN_HOST).scrollIntoView();
      setInputValue(IDS.S4HANACLOUD_CONN_HOST, host);
      setInputValue(IDS.S4HANACLOUD_CONN_CLIENT, client);
      setInputValue(IDS.S4HANACLOUD_CONN_SYSTEMID, sysid);
      setInputValue(IDS.S4HANACLOUD_CONN_LANGUAGE, langcode);
      setInputValue(IDS.S4HANACLOUD_CONN_PORT, port);
    });

  inputAuthenticationData(isBasicAuth);

  cy.get("#" + IDS.S4HANACLOUD_CONN_DATA_PROVISION_OPTION).scrollIntoView();
  // check the number of options in the data provisioning dropdown, "direct"
  // option is only available for basic auth
  cy.byId(IDS.S4HANACLOUD_CONN_DATA_PROVISION_OPTION).then((oSelect: sap.m.Select) => {
    expect(oSelect.getItems().length).to.eq(isBasicAuth ? 3 : 2);
  });

  if (dpOption === "dpAgent") {
    setDropdownValue(IDS.S4HANACLOUD_CONN_DATA_PROVISION_OPTION, 0); // "dpAgent"
    // data access select is readonly
    cy.get("#" + IDS.S4HANACLOUD_CONN_DATA_ACCESS).should("have.prop", "isContentEditable", false);
    // selecting dp option as dpAgent without selecting dp agent will trigger
    // error popup
    setDropdownValue(IDS.S4HANACLOUD_CONN_DATA_PROVISION_AGENT, 1); // 0 is empty
  } else if (dpOption === "direct") {
    setDropdownValue(IDS.S4HANACLOUD_CONN_DATA_PROVISION_OPTION, 1); // "direct"
    // data access select is readonly
    cy.get("#" + IDS.S4HANACLOUD_CONN_DATA_ACCESS).should("have.prop", "isContentEditable", false);
    setDropdownValue(IDS.S4HANACLOUD_CONN_DATA_PROVISION_OPTION_DIRECT_TYPEMAP, 1); // semantic
    if (userPropagation) {
      setDropdownValue(IDS.S4HANACLOUD_CONN_DATA_PROVISION_OPTION_USERPROPAGATION, 1); // true
      cy.get("#" + IDS.S4HANACLOUD_CONN_DATA_PROVISION_OPTION_SAPHANATOKENEXCHANGER)
        .should("be.visible")
        .then(() => {
          setInputValue(IDS.S4HANACLOUD_CONN_DATA_PROVISION_OPTION_ABAPSQLSERVICEBINDING, abapSQLServiceBinding);
          setInputValue(
            IDS.S4HANACLOUD_CONN_DATA_PROVISION_OPTION_IASAPPLICATIONDEPENDENCYNAME,
            iasApplicationDependencyName
          );
        });
    } else {
      setDropdownValue(IDS.S4HANACLOUD_CONN_DATA_PROVISION_OPTION_USERPROPAGATION, 0); // false
    }
  } else {
    setDropdownValue(IDS.S4HANACLOUD_CONN_DATA_PROVISION_OPTION, isBasicAuth ? 2 : 1); // "none"
  }

  cy.wait(500, { log: false });
}

function inputBusinessAndTechNames() {
  setInputValue(IDS.CONN_BUSINESS_NAME_FIELD, businessName);
  setInputValue(IDS.CONN_TECH_NAME_FIELD, technicalName);
}

/**
 * Sets the authentication data for the connection.
 * @param isBasicAuth true for basic auth, false for x509 client certificate
 * @param withData Flag to control whether to set the authentication data or
 * not. Default is true.
 */
function inputAuthenticationData(isBasicAuth: boolean, withData: boolean = true) {
  cy.get("#" + IDS.S4HANACLOUD_CONN_AUTH_TYPE).scrollIntoView();

  if (isBasicAuth) {
    setDropdownValue(IDS.S4HANACLOUD_CONN_AUTH_TYPE, 1);
    if (withData) {
      setInputValue(IDS.S4HANACLOUD_CONN_USERNAME, user);
      setInputValue(IDS.S4HANACLOUD_CONN_PASSWORD, password);
    }
  } else {
    setDropdownValue(IDS.S4HANACLOUD_CONN_AUTH_TYPE, 0);
    if (withData) {
      insertFile(IDS.S4HANACLOUD_CONN_CLIENT_CERTIFICATE, clientCertificateFilePath, 0);
      insertFile(IDS.S4HANACLOUD_CONN_CLIENT_PRIVATE_KEY, clientPrivateKeyFilePath, 1);
    }
  }
}

export function setConnectionMetadata() {
  cy.get("#" + IDS.S4HANACLOUD_EDIT_CONN_HOST).scrollIntoView();
  setInputValue(IDS.S4HANACLOUD_EDIT_CONN_HOST, host);
  setInputValue(IDS.S4HANACLOUD_EDIT_CONN_CLIENT, client);
  setInputValue(IDS.S4HANACLOUD_EDIT_CONN_SYSTEMID, sysid);
  setInputValue(IDS.S4HANACLOUD_EDIT_CONN_LANGUAGE, langcode);
  setInputValue(IDS.S4HANACLOUD_EDIT_CONN_PORT, port);
  cy.wait(500, { log: false });
}
