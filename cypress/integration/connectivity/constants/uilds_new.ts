/**
 * Copyright 2022 SAP SE or an SAP affiliate company. All rights reserved.
 * @format
 */

// Connections Table
const BASE = "shellMainContent---connectionsComponent---connections--RemoteConnectionListView";
export const CONNECTIONS_TABLE = BASE + "--remoteConnectionsTable";
export const ADD_BTN_LIST = BASE + "--addButton";
export const ADD_BTN = BASE + "--remoteTableAddButton";
export const EDIT_BTN = BASE + "--remoteTableEditButton";
export const GROWING_BTN = BASE + "--remoteConnectionsTable-triggerText";

// edit connection dialog
export const EDIT_CONN_FORM_CONTAINER = BASE + "--EditConnectionFormContainer";
export const EDIT_CONN_FORM = EDIT_CONN_FORM_CONTAINER + "--connectionForm";
export const CONNECTION_EDIT_DIALOG = "editConnectionDialog";

// *** SAP S4HANA On-Premise connection edit *** //
export const S4HANAOP_EDIT_CONN_SERVER_TYPE = EDIT_CONN_FORM + "--connectionType";
export const S4HANAOP_EDIT_CONN_SERVER = EDIT_CONN_FORM + "--host";
export const S4HANAOP_EDIT_CONN_CLIENT = EDIT_CONN_FORM + "--client";
export const S4HANAOP_EDIT_CONN_LANGUAGE = EDIT_CONN_FORM + "--language";
// application server
export const S4HANAOP_EDIT_CONN_APP_SERVER_SYSTEMNO = EDIT_CONN_FORM + "--systemnumber";
export const S4HANAOP_EDIT_CONN_APP_SERVER_SYSTEMID = EDIT_CONN_FORM + "--sysidAppServer";
// message server
export const S4HANAOP_EDIT_CONN_MSG_SERVER_PORT = EDIT_CONN_FORM + "--port";
export const S4HANAOP_EDIT_CONN_MSG_SERVER_GROUP = EDIT_CONN_FORM + "--serverGroup";
export const S4HANAOP_EDIT_CONN_MSG_SERVER_SYSTEMID = EDIT_CONN_FORM + "--sysidMsgServer";
// cloud connector
export const S4HANAOP_EDIT_CLOUD_CONNECTOR = EDIT_CONN_FORM + "--cloudConnector--cloudConnector";
export const S4HANAOP_EDIT_CLOUD_CONNECTOR_LOCATION = EDIT_CONN_FORM + "--cloudConnector--cloudConnectorLocation";
export const S4HANAOP_EDIT_CLOUD_CONNECTOR_VIRTUAL_DESTINATION =
  EDIT_CONN_FORM + "--cloudConnector--virtualDestination";
export const S4HANAOP_EDIT_CLOUD_CONNECTOR_VIRTUAL_HOST = EDIT_CONN_FORM + "--cloudConnector--virtualHost";
export const S4HANAOP_EDIT_CLOUD_CONNECTOR_VIRTUAL_PORT_APP_SERVER =
  EDIT_CONN_FORM + "--cloudConnector--virtualPortAppServer";
export const S4HANAOP_EDIT_CLOUD_CONNECTOR_VIRTUAL_PORT_MSG_SERVER =
  EDIT_CONN_FORM + "--cloudConnector--virtualPortMsgServer";
// authentication
export const S4HANAOP_EDIT_CONN_AUTH_TYPE = EDIT_CONN_FORM + "--auth_type";
export const S4HANAOP_EDIT_CONN_USERNAME = EDIT_CONN_FORM + "--usernamePasswordCredentials--user";
export const S4HANAOP_EDIT_CONN_FAILED_MESSAGE = EDIT_CONN_FORM_CONTAINER + "--replicationFailedMessage-content";
export const S4HANAOP_EDIT_CONN_PASSWORD = EDIT_CONN_FORM + "--usernamePasswordCredentials--password";
// OAuth2
export const S4HANAOP_EDIT_OAUTH2_GRANT_TYPE = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2GrantType";
export const S4HANAOP_EDIT_OAUTH2_TOKEN_ENDPOINT = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2TokenEndpoint";
export const S4HANAOP_EDIT_OAUTH2_SCOPE = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2Scope";
export const S4HANAOP_EDIT_OAUTH2_TOKEN_REQUEST_CONTENT_TYPE =
  EDIT_CONN_FORM + "--oAuth2Credentials--oauth2TokenRequestContentType";
export const S4HANAOP_EDIT_OAUTH2_CLIENT_ID = EDIT_CONN_FORM + "--oAuth2Credentials--clientId";
export const S4HANAOP_EDIT_OAUTH2_CLIENT_SECRET = EDIT_CONN_FORM + "--oAuth2Credentials--clientSecret";
// advanced properties
export const SAP4HANAOP_EDIT_REMOTE_TABLES_LABEL_ENABLED = EDIT_CONN_FORM + "--remoteTableFeature--remoteTablesEnabled";
export const SAP4HANAOP_EDIT_REMOTE_TABLES_LABEL_DISABLED =
  EDIT_CONN_FORM + "--remoteTableFeature--remoteTablesDisabled";
export const SAP4HANAOP_EDIT_MODEL_TRANSFER_LABEL_ENABLED = EDIT_CONN_FORM + "--modelImportFeature--modelImportEnabled";
export const SAP4HANAOP_EDIT_MODEL_TRANSFER_LABEL_DISABLED =
  EDIT_CONN_FORM + "--modelImportFeature--modelImportDisabled";
export const ABAP_EDIT_DPAGENT_FEATURE_FIELD = EDIT_CONN_FORM + "--features--dpAgentName";
export const S4HANAOP_EDIT_ADVANCE_PROPERTES = EDIT_CONN_FORM_CONTAINER + "--advancedFeaturesButton";
export const S4HANAOP_EDIT_ADVANCE_PROPERTES_FORM = EDIT_CONN_FORM + "--connectionAdvancedPropertyForm";
export const S4HANAOP_EDIT_ADVANCE_PROPERTES_RFC_SERIALIZATION =
  S4HANAOP_EDIT_ADVANCE_PROPERTES_FORM + "--serialization";
// S4HANAOP data provisioning options related fields
export const S4HANAOP_EDIT_DATA_PROVISION_OPTION = EDIT_CONN_FORM + "--dataProvisioningOption";
export const S4HANAOP_EDIT_DATA_PROVISION_AGENT = EDIT_CONN_FORM + "--dpAgents--dpAgent";
export const S4HANAOP_EDIT_DATA_ACCESS = EDIT_CONN_FORM + "--dataAccess";
export const S4HANAOP_EDIT_DATA_PROVISION_OPTION_DIRECT_TYPEMAP = EDIT_CONN_FORM + "--typeMap";
export const S4HANAOP_EDIT_STREAMING_READ = EDIT_CONN_FORM + "--streamingRead";
export const S4HANAOP_EDIT_GATEWAY_HOST = EDIT_CONN_FORM + "--gwHost";
export const S4HANAOP_EDIT_GATEWAY_PORT = EDIT_CONN_FORM + "--gwPort";
export const S4HANAOP_EDIT_RFC_DESTINATION = EDIT_CONN_FORM + "--rfcDest";
export const SAP4HANAOP_EDIT_LIVE_DATA_TUNNEL_CONNECTION = EDIT_CONN_FORM + "--sacTunnelConnName";
export const S4HANAOP_EDIT_DATA_PROVISION_OPTION_USERPROPAGATION = EDIT_CONN_FORM + "--enableUserPropagation";
// *** SAP S4HANA On-Premise connection edit *** //

// S4HANA Cloud connection edit
export const S4HANACLOUD_EDIT_CONN_HOST = EDIT_CONN_FORM + "--host";
export const S4HANACLOUD_EDIT_CONN_CLIENT = EDIT_CONN_FORM + "--client";
export const S4HANACLOUD_EDIT_CONN_SYSTEMID = EDIT_CONN_FORM + "--sysidAppServer";
export const S4HANACLOUD_EDIT_CONN_LANGUAGE = EDIT_CONN_FORM + "--language";
export const S4HANACLOUD_EDIT_CONN_PORT = EDIT_CONN_FORM + "--port";
export const S4HANACLOUD_EDIT_CONN_USERNAME = EDIT_CONN_FORM + "--usernamePasswordCredentials--user";
export const S4HANACLOUD_EDIT_CONN_PASSWORD = EDIT_CONN_FORM + "--usernamePasswordCredentials--password";
export const S4HANACLOUD_EDIT_CONN_AUTH_TYPE = EDIT_CONN_FORM + "--auth_type";
export const S4HANACLOUD_EDIT_CONN_CLIENT_CERTIFICATE =
  EDIT_CONN_FORM + "--x509ClientCredentials--certificateFileUploader";
export const S4HANACLOUD_EDIT_CONN_CLIENT_PRIVATE_KEY = EDIT_CONN_FORM + "--x509ClientCredentials--keyFileUploader";
export const S4HANACLOUD_EDIT_CONN_CLIENT_PRIVATE_KEY_PASSWORD =
  EDIT_CONN_FORM + "--x509ClientCredentials--clientPrivateKeyPassword";

export const S4HANACLOUD_EDIT_CONN_DATA_PROVISION_AGENT = EDIT_CONN_FORM + "--dpAgents--dpAgent";
export const S4HANACLOUD_EDIT_CONN_DATA_PROVISION_OPTION = EDIT_CONN_FORM + "--dataProvisioningOption";
export const S4HANACLOUD_EDIT_CONN_DATA_PROVISION_OPTION_DIRECT_TYPEMAP = EDIT_CONN_FORM + "--typeMap";
export const S4HANACLOUD_EDIT_CONN_DATA_ACCESS = EDIT_CONN_FORM + "--dataAccess";
export const S4HANACLOUD_EDIT_CONN_BUSINESSNAME = EDIT_CONN_FORM + "--connectionInfo--businessName";
export const S4HANACLOUD_EDIT_DATA_PROVISION_OPTION_USERPROPAGATION = EDIT_CONN_FORM + "--enableUserPropagation";

export const HDLDB_EDIT_HOST = EDIT_CONN_FORM + "--host";
export const HDLDB_EDIT_PORT = EDIT_CONN_FORM + "--port";
export const HDLDB_EDIT_AUTH_TYPE = EDIT_CONN_FORM + "--auth_type";
export const HDLDB_EDIT_USERNAME = EDIT_CONN_FORM + "--usernamePasswordCredentials--user";
export const HDLDB_EDIT_PASSWORD = EDIT_CONN_FORM + "--usernamePasswordCredentials--password";
export const HDLDB_EDIT_CLIENT_CERTIFICATE = EDIT_CONN_FORM + "--x509ClientCredentials--certificateFileUploader";
export const HDLDB_EDIT_CLIENT_PRIVATE_KEY = EDIT_CONN_FORM + "--x509ClientCredentials--keyFileUploader";

export const HDLFILES_EDIT_HOST = EDIT_CONN_FORM + "--host";
export const HDLFILES_EDIT_ROOT_PATH = EDIT_CONN_FORM + "--rootPath";
export const HDLFILES_EDIT_KEYSTORE_FILE_UPLOADER = EDIT_CONN_FORM + "--keystoreFileCredentials--fileUploader";
export const HDLFILES_EDIT_KEYSTORE_PASSWORD = EDIT_CONN_FORM + "--keystoreFileCredentials--keystorePwd";

export const CPEM_EDIT_HOST = EDIT_CONN_FORM + "--host";
export const ARIBA_EDIT_URL = EDIT_CONN_FORM + "--url";
export const ARIBA_EDIT_SOURCE_ID = EDIT_CONN_FORM + "--sourceId";
export const CPEM_EDIT_OAUTH2_CRED = EDIT_CONN_FORM + "--OAuth2Credentials--oAuth2";
export const ARIBA_EDIT_EVENT_MESH = EDIT_CONN_FORM + "--connectionsForDataLoad--deltaLoad";
export const ARIBA_EDIT_OBJECT_STORE = EDIT_CONN_FORM + "--connectionsForDataLoad--initialLoad";
export const CPEM_EDIT_OAUTH2_ENDPOINT = EDIT_CONN_FORM + "--OAuth2Credentials--oAuth2TokenEndpoint";
export const CPEM_EDIT_OAUTH2_SCOPE = EDIT_CONN_FORM + "--OAuth2Credentials--oAuth2Scope";
export const CPEM_EDIT_OAUTH2_RESOURCE = EDIT_CONN_FORM + "--OAuth2Credentials--oAuth2Resource";
export const CPEM_EDIT_OAUTH2_RESPONSETYPE = EDIT_CONN_FORM + "--OAuth2Credentials--oAuth2ResponseType";
export const CPEM_EDIT_OAUTH2_CONTENTTYPE = EDIT_CONN_FORM + "--OAuth2Credentials--oAuth2TokenRequestContentType";
export const CPEM_EDIT_OAUTH2_CLIENTCREDENTIAL_ID = EDIT_CONN_FORM + "--OAuth2Credentials--clientID";
export const CPEM_EDIT_OAUTH2_CLIENTCREDENTIAL_SECRET = EDIT_CONN_FORM + "--OAuth2Credentials--clientSecret";
export const CPEM_EDIT_OAUTH2_X509_ID = EDIT_CONN_FORM + "--OAuth2Credentials--clientIDX509";
export const CPEM_EDIT_OAUTH2_X509_KEY_UPLOADER = EDIT_CONN_FORM + "--OAuth2Credentials--keyFileUploader";
export const CPEM_EDIT_OAUTH2_X509_CERTIFICATE_UPLOADER =
  EDIT_CONN_FORM + "--OAuth2Credentials--certificateFileUploader";
export const SAPSF_EDIT_URL = EDIT_CONN_FORM + "--url";
export const SAPSF_EDIT_VERSION = EDIT_CONN_FORM + "--version";
export const SAPSF_EDIT_AUTH_TYPE = EDIT_CONN_FORM + "--auth_type";
export const SAPSF_EDIT_USERNAME = EDIT_CONN_FORM + "--usernamePasswordCredentials--user";
export const SAPSF_EDIT_PASSWORD = EDIT_CONN_FORM + "--usernamePasswordCredentials--password";
export const SAPSF_EDIT_OAUTH_TOKEN_ENDPOINT = EDIT_CONN_FORM + "--SAMLBearer--oauth2TokenEndpoint";
export const SAPSF_EDIT_API_ENDPOINT = EDIT_CONN_FORM + "--SAMLBearer--oauth2ApiEndpoint";
export const SAPSF_EDIT_PROVIDE_SAML_ASSERTION = EDIT_CONN_FORM + "--SAMLBearer--provideSamlAssertion";
export const SAPSF_EDIT_USER_ID = EDIT_CONN_FORM + "--SAMLBearer--oauth2UserId";
export const SAPSF_EDIT_COMPANY_ID = EDIT_CONN_FORM + "--SAMLBearer--oauth2CompanyId";
export const SAPSF_EDIT_CLIENT_ID = EDIT_CONN_FORM + "--SAMLBearer--clientId";
export const SAPSF_EDIT_CLIENT_SECRET = EDIT_CONN_FORM + "--SAMLBearer--clientSecret";
export const SAPSF_EDIT_SAML_ASSERTION = EDIT_CONN_FORM + "--SAMLBearer--samlAssertion";

export const KAFKAF_EDIT_BROKERS = EDIT_CONN_FORM + "--KAFKABrokers";
export const KAFKAF_EDIT_CLOUD_CONNECTOR = EDIT_CONN_FORM + "--cloudConnector--cloudConnector";
export const KAFKAF_EDIT_CLOUD_CONNECTOR_LOCATION = EDIT_CONN_FORM + "--cloudConnector--cloudConnectorLocation";
export const KAFKAF_EDIT_CLOUD_CONNECTOR_VIRTUAL_DESTINATION = EDIT_CONN_FORM + "--cloudConnector--virtualDestination";
export const KAFKAF_EDIT_CLOUD_CONNECTOR_VIRTUAL_HOST = EDIT_CONN_FORM + "--cloudConnector--virtualHost";
export const KAFKAF_EDIT_AUTH_TYPE = EDIT_CONN_FORM + "--auth_type";
export const KAFKAF_EDIT_TLS_ENABLE = EDIT_CONN_FORM + "--clientCertificate";

export const KAFKAF_EDIT_SASL_USER = EDIT_CONN_FORM + "--SASLCredentials--user";
export const KAFKAF_EDIT_SASL_PASSWORD = EDIT_CONN_FORM + "--SASLCredentials--password";
export const KAFKAF_EDIT_SASL_KERBEROS_SERVICENAME = EDIT_CONN_FORM + "--SASLCredentials--kerberosServiceName";
export const KAFKAF_EDIT_SASL_KERBEROS_REALM = EDIT_CONN_FORM + "--SASLCredentials--kerberosRealm";
export const KAFKAF_EDIT_SASL_KERBEROS_CONFIG_FILE = EDIT_CONN_FORM + "--SASLCredentials--kerberosConfigFile";
export const KAFKAF_EDIT_SASL_KERBEROS_KEYTAB = EDIT_CONN_FORM + "--SASLCredentials--kerberoskeyTab";
export const KAFKAF_EDIT_CLOUD_CONNECTOR_VIRTUAL_PORT_MSG_SERVER =
  EDIT_CONN_FORM + "--cloudConnector--virtualPortMsgServer";
export const KAFKAF_EDIT_CLIENT_KEY_UPLOADER = EDIT_CONN_FORM + "--TLSCredential--clientKey";
export const KAFKAF_EDIT_CERTIFICATE_UPLOADER = EDIT_CONN_FORM + "--TLSCredential--clientCertificate";

//
export const CONFLUENT_EDIT_BROKERS = EDIT_CONN_FORM + "--KAFKABrokers";
export const CONFLUENT_EDIT_CLOUD_CONNECTOR = EDIT_CONN_FORM + "--cloudConnector--cloudConnector";
export const CONFLUENT_EDIT_CLOUD_CONNECTOR_LOCATION = EDIT_CONN_FORM + "--cloudConnector--cloudConnectorLocation";
export const CONFLUENT_EDIT_CLOUD_CONNECTOR_VIRTUAL_DESTINATION =
  EDIT_CONN_FORM + "--cloudConnector--virtualDestination";
export const CONFLUENT_EDIT_CLOUD_CONNECTOR_VIRTUAL_HOST = EDIT_CONN_FORM + "--cloudConnector--virtualHost";
export const CONFLUENT_EDIT_AUTH_TYPE = EDIT_CONN_FORM + "--auth_type";
export const CONFLUENT_EDIT_TLS_ENABLE = EDIT_CONN_FORM + "--clientCertificate";
export const CONFLUENT_EDIT_MTLS_ENABLE = EDIT_CONN_FORM + "--TLSCredential--useMTLS";
export const CONFLUENT_EDIT_SASL_USER = EDIT_CONN_FORM + "--SASLCredentials--user";
export const CONFLUENT_EDIT_SASL_PASSWORD = EDIT_CONN_FORM + "--SASLCredentials--password";
export const CONFLUENT_EDIT_SASL_KERBEROS_SERVICENAME = EDIT_CONN_FORM + "--SASLCredentials--kerberosServiceName";
export const CONFLUENT_EDIT_SASL_KERBEROS_REALM = EDIT_CONN_FORM + "--SASLCredentials--kerberosRealm";
export const CONFLUENT_EDIT_SASL_KERBEROS_CONFIG_FILE = EDIT_CONN_FORM + "--SASLCredentials--kerberosConfigFile";
export const CONFLUENT_EDIT_SASL_KERBEROS_KEYTAB = EDIT_CONN_FORM + "--SASLCredentials--kerberoskeyTab";
export const CONFLUENT_EDIT_CLOUD_CONNECTOR_VIRTUAL_PORT_MSG_SERVER =
  EDIT_CONN_FORM + "--cloudConnector--virtualPortMsgServer";
export const CONFLUENT_EDIT_CLIENT_KEY_UPLOADER = EDIT_CONN_FORM + "--TLSCredential--clientKey";
export const CONFLUENT_EDIT_CERTIFICATE_UPLOADER = EDIT_CONN_FORM + "--TLSCredential--clientCertificate";

export const SAPODATA_EDIT_URL = EDIT_CONN_FORM + "--url";
export const SAPODATA_EDIT_VERSION = EDIT_CONN_FORM + "--version";
export const SAPODATA_EDIT_ENDPOINT = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2TokenEndpoint";
export const SAPODATA_EDIT_TOKEN_REQUEST_CONTENT_TYPE =
  EDIT_CONN_FORM + "--oAuth2Credentials--oauth2TokenRequestContentType";
export const SAPODATA_EDIT_CLIENT_ID = EDIT_CONN_FORM + "--oAuth2Credentials--clientId";
export const SAPODATA_EDIT_CLIENT_SECRET = EDIT_CONN_FORM + "--oAuth2Credentials--clientSecret";
export const SAPODATA_EDIT_AUTH_TYPE = EDIT_CONN_FORM + "--auth_type";

export const EDIT_CONN_SAVE_BTN =
  "shellMainContent---connectionsComponent---connections--RemoteConnectionListView--EditConnectionDialogSaveBtn";
export const EDIT_CONN_CANCEL_BTN =
  "shellMainContent---connectionsComponent---connections--RemoteConnectionListView--EditConnectionDialogCancelBtn";

// create connection dialog
export const CONNECTION_CREATION_DIALOG = "connectionCreationDialog";
export const NAVIGATOR = "connectionWizardView--navigator";
export const NEXT_STEP_BTN = "connectionWizardView--wizardDialogNextStepBtn";

// Tile selection - stage 1
export const S4HANAOP_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-15";
export const S4HANACLOUD_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-23";
export const HDLFILES_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-20";
export const HDLDB_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-17";

export const CPEM_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-24";
export const ARIBA_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-25";
export const CONCUR_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-26";
export const FIELDGLASS_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-27";
export const HANA_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-7";
export const SFTP_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-22";
export const SAPSF_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-28";
export const ODATA_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-10";
export const S3_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-12";
export const BIGQUERY_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-29";
export const ADL_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-2";
export const GCS_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-6";
export const KAFKA_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-30";
export const CONFLUENT_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-31";
export const ORACLEDB_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-11";
export const ABAP_ADAPTER_TITLE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-0";
export const MSSQL_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-9";
export const JDBC_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-34";
export const AZURESQL_TILE = "connectionWizardView--datasourceTile-connectionWizardView--DatasourceSelector-4";

// Connection details - stage 2
export const CONN_FORM = "connectionWizardView--connectionForm";
export const CONN_DESCRIPTION_FIELD = "connectionWizardView--connectionForm--connectionInfo--connectionDescription";
export const S4HANAOP_CONN_SERVER_TYPE = CONN_FORM + "--connectionType";
export const S4HANAOP_CONN_SERVER = CONN_FORM + "--host";

export const S4HANAOP_CONN_APP_SERVER = CONN_FORM + "--host";
export const S4HANAOP_CONN_APP_SERVER_SYSTEMNO = CONN_FORM + "--systemnumber";
export const S4HANAOP_CONN_APP_SERVER_SYSTEMID = CONN_FORM + "--sysidAppServer";

export const S4HANAOP_CONN_MSG_SERVER = CONN_FORM + "--host";
export const S4HANAOP_CONN_MSG_SERVER_PORT = CONN_FORM + "--port";
export const S4HANAOP_CONN_MSG_SERVER_GROUP = CONN_FORM + "--serverGroup";
export const S4HANAOP_CONN_MSG_SERVER_SYSTEMID = CONN_FORM + "--sysidMsgServer";
export const S4HANAOP_CONN_RFC_SERIALIZATION = CONN_FORM + "--connectionAdvancedPropertyForm--serialization";

export const S4HANAOP_CONN_CLIENT = CONN_FORM + "--client";
export const S4HANAOP_CONN_LANGUAGE = CONN_FORM + "--language";
export const S4HANAOP_CLOUD_CONNECTOR = CONN_FORM + "--cloudConnector--cloudConnector";
export const S4HANAOP_CLOUD_CONNECTOR_LOCATION = CONN_FORM + "--cloudConnector--cloudConnectorLocation";
export const S4HANAOP_CLOUD_CONNECTOR_VIRTUAL_DESTINATION = CONN_FORM + "--cloudConnector--virtualDestination";
export const S4HANAOP_CLOUD_CONNECTOR_VIRTUAL_HOST = CONN_FORM + "--cloudConnector--virtualHost";

export const S4HANAOP_CLOUD_CONNECTOR_VIRTUAL_PORT = CONN_FORM + "--cloudConnector--VirtualPort";
export const S4HANAOP_CLOUD_CONNECTOR_VIRTUAL_PORT_MSG_SERVER = CONN_FORM + "--cloudConnector--virtualPortMsgServer";
export const S4HANAOP_CLOUD_CONNECTOR_VIRTUAL_PORT_APP_SERVER = CONN_FORM + "--cloudConnector--virtualPortAppServer";
export const ABAP_MODEL_TRANSFER_LABEL_ENABLED = CONN_FORM + "--features--modelImportEnabled";
export const ABAP_MODEL_TRANSFER_LABEL_DISABLED = CONN_FORM + "--features--modelImportDisabled";
export const S4HANAOP_DATAFLOWS_ENABLED = CONN_FORM + "--dataFlowFeature--dataFlowsEnabled";
export const S4HANAOP_DATAFLOWS_DISABLED = CONN_FORM + "--dataFlowFeature--dataFlowsDisabled";
export const S4HANAOP_REPLICATIONFLOW_ENABLED = CONN_FORM + "--replicationFlowFeature--replicationFlowsEnabled";
export const S4HANAOP_REPLICATIONFLOW_DISABLED = CONN_FORM + "--replicationFlowFeature--replicationFlowsDisabled";
// S4HANAOP data provisioning options
export const S4HANAOP_CONN_DATA_PROVISION_OPTION = CONN_FORM + "--dataProvisioningOption";
export const S4HANAOP_CONN_DATA_PROVISION_AGENT = CONN_FORM + "--dpAgents--dpAgent";
export const S4HANAOP_CONN_DATA_ACCESS = CONN_FORM + "--dataAccess";
export const S4HANAOP_CONN_STREAMING_READ = CONN_FORM + "--streamingRead";
export const S4HANAOP_CONN_DATA_PROVISION_OPTION_DIRECT_TYPEMAP = CONN_FORM + "--typeMap";

export const S4HANAOP_CONN_DATA_PROVISION_OPTION_USERPROPAGATION = CONN_FORM + "--enableUserPropagation";
export const S4HANAOP_CONN_DATA_PROVISION_OPTION_SAPHANATOKENEXCHANGER = CONN_FORM + "--sapHanaTokenExchanger-label";
export const S4HANAOP_CONN_DATA_PROVISION_OPTION_ABAPSQLSERVICEBINDING = CONN_FORM + "--abapSQLServiceBinding";
export const S4HANAOP_CONN_DATA_PROVISION_OPTION_IASAPPLICATIONDEPENDENCYNAME =
  CONN_FORM + "--iasApplicationDependencyName";

export const S4HANAOP_CONN_REMOTE_TABLES_LABEL_DISABLED = CONN_FORM + "--remoteTableFeature--remoteTablesDisabled";
export const S4HANAOP_CONN_MODEL_TRANSFER_LABEL_ENABLED = CONN_FORM + "--modelImportFeature--modelImportEnabled";
export const S4HANAOP_CONN_MODEL_TRANSFER_LABEL_DISABLED = CONN_FORM + "--modelImportFeature--modelImportDisabled";
export const S4HANAOP_CONN_LIVE_DATA_TUNNEL_CONNECTION = CONN_FORM + "--sacTunnelConnName";
export const S4HANAOP_CONN_AUTH_TYPE = CONN_FORM + "--auth_type";
export const S4HANAOP_CONN_USERNAME = CONN_FORM + "--usernamePasswordCredentials--user";
export const S4HANAOP_CONN_PASSWORD = CONN_FORM + "--usernamePasswordCredentials--password";
export const S4HANAOP_OAUTH2_GRANT_TYPE = CONN_FORM + "--oAuth2Credentials--oauth2GrantType";
export const S4HANAOP_OAUTH2_TOKEN_ENDPOINT = CONN_FORM + "--oAuth2Credentials--oauth2TokenEndpoint";
export const S4HANAOP_OAUTH2_SCOPE = CONN_FORM + "--oAuth2Credentials--oauth2Scope";
export const S4HANAOP_OAUTH2_TOKEN_REQUEST_CONTENT_TYPE =
  CONN_FORM + "--oAuth2Credentials--oauth2TokenRequestContentType";
export const S4HANAOP_OAUTH2_CLIENT_ID = CONN_FORM + "--oAuth2Credentials--clientId";
export const S4HANAOP_OAUTH2_CLIENT_SECRET = CONN_FORM + "--oAuth2Credentials--clientSecret";

export const ABAP_DPAGENT_FEATURE_FIELD = CONN_FORM + "--features--dpAgentName";
export const ABAP_MODEL_TRANSFER_TUNNEL_FIELD = CONN_FORM + "--features--sacTunnelConnName";
export const ABAP_REMOTE_TABLES = CONN_FORM + "--features--remoteTablesEnabled";
export const ABAP_REMOTE_TABLES_DISABLED = CONN_FORM + "--features--remoteTablesDisabled"; //currently not used anywhere
export const ABAP_DPAGENT_STREAMING_READ = CONN_FORM + "--features--streamingRead";
export const ABAP_DPAGENT_GATEWAY_HOST = CONN_FORM + "--features--gwHost";
export const ABAP_DPAGENT_GATEWAY_PORT = CONN_FORM + "--features--gwPort";
export const ABAP_DPAGENT_RFC_DESTINATION = CONN_FORM + "--features--rfcDest";

export const S4HANACLOUD_CONN_HOST = CONN_FORM + "--host";
export const S4HANACLOUD_CONN_CLIENT = CONN_FORM + "--client";
export const S4HANACLOUD_CONN_SYSTEMID = CONN_FORM + "--sysidAppServer";
export const S4HANACLOUD_CONN_LANGUAGE = CONN_FORM + "--language";
export const S4HANACLOUD_CONN_PORT = CONN_FORM + "--port";

export const S4HANACLOUD_CONN_AUTH_TYPE = CONN_FORM + "--auth_type";
export const S4HANACLOUD_CONN_CLIENT_CERTIFICATE = CONN_FORM + "--x509ClientCredentials--certificateFileUploader";
export const S4HANACLOUD_CONN_CLIENT_PRIVATE_KEY = CONN_FORM + "--x509ClientCredentials--keyFileUploader";
export const S4HANACLOUD_CONN_CLIENT_PRIVATE_KEY_PASSWORD =
  CONN_FORM + "--x509ClientCredentials--clientPrivateKeyPassword";
export const S4HANACLOUD_CONN_USERNAME = CONN_FORM + "--usernamePasswordCredentials--user";
export const S4HANACLOUD_CONN_PASSWORD = CONN_FORM + "--usernamePasswordCredentials--password";

export const S4HANACLOUD_CONN_DATA_PROVISION_AGENT = CONN_FORM + "--dpAgents--dpAgent";
export const S4HANACLOUD_CONN_DATA_PROVISION_OPTION = CONN_FORM + "--dataProvisioningOption";
export const S4HANACLOUD_CONN_DATA_PROVISION_OPTION_DIRECT_TYPEMAP = CONN_FORM + "--typeMap";
export const S4HANACLOUD_CONN_DATA_ACCESS = CONN_FORM + "--dataAccess";

export const S4HANACLOUD_CONN_DATA_PROVISION_OPTION_USERPROPAGATION = CONN_FORM + "--enableUserPropagation";
export const S4HANACLOUD_CONN_DATA_PROVISION_OPTION_SAPHANATOKENEXCHANGER = CONN_FORM + "--sapHanaTokenExchanger-label";
export const S4HANACLOUD_CONN_DATA_PROVISION_OPTION_ABAPSQLSERVICEBINDING = CONN_FORM + "--abapSQLServiceBinding";
export const S4HANACLOUD_CONN_DATA_PROVISION_OPTION_IASAPPLICATIONDEPENDENCYNAME =
  CONN_FORM + "--iasApplicationDependencyName";

export const HDLFILES_HOST = CONN_FORM + "--host";
export const HDLFILES_ROOT_PATH = CONN_FORM + "--rootPath";
export const HDLFILES_DATA_ACCESS_LEVEL = CONN_FORM + "--dataAccessLevel";
export const HDLFILES_KEYSTORE_FILE_UPLOADER = CONN_FORM + "--keystoreFileCredentials--fileUploader";
export const HDLFILES_KEYSTORE_PASSWORD = CONN_FORM + "--keystoreFileCredentials--keystorePwd";

export const HDLDB_CONN_HOST = CONN_FORM + "--host";
export const HDLDB_CONN_PORT = CONN_FORM + "--port";
export const HDLDB_CONN_AUTH_TYPE = CONN_FORM + "--auth_type";
export const HDLDB_CONN_CLIENT_CERTIFICATE = CONN_FORM + "--x509ClientCredentials--certificateFileUploader";
export const HDLDB_CONN_CLIENT_PRIVATE_KEY = CONN_FORM + "--x509ClientCredentials--keyFileUploader";
export const HDLDB_CONN_CLIENT_PRIVATE_KEY_PASSWORD = CONN_FORM + "--x509ClientCredentials--clientPrivateKeyPassword";
export const HDLDB_CONN_USERNAME = CONN_FORM + "--usernamePasswordCredentials--user";
export const HDLDB_CONN_PASSWORD = CONN_FORM + "--usernamePasswordCredentials--password";

export const CPEM_HOST = CONN_FORM + "--host";
export const ARIBA_URL = CONN_FORM + "--url";
export const ARIBA_SOURCE_ID = CONN_FORM + "--sourceId";
export const ARIBA_EVENT_MESH = CONN_FORM + "--connectionsForDataLoad--deltaLoad";
export const ARIBA_OBJECT_STORE = CONN_FORM + "--connectionsForDataLoad--initialLoad";
export const CPEM_OAUTH2_CRED = CONN_FORM + "--OAuth2Credentials--oAuth2";
export const CPEM_OAUTH2_ENDPOINT = CONN_FORM + "--OAuth2Credentials--oAuth2TokenEndpoint";
export const CPEM_OAUTH2_SCOPE = CONN_FORM + "--OAuth2Credentials--oAuth2Scope";
export const CPEM_OAUTH2_RESOURCE = CONN_FORM + "--OAuth2Credentials--oAuth2Resource";
export const CPEM_OAUTH2_RESPONSETYPE = CONN_FORM + "--OAuth2Credentials--oAuth2ResponseType";
export const CPEM_OAUTH2_CONTENTTYPE = CONN_FORM + "--OAuth2Credentials--oAuth2TokenRequestContentType";
export const CPEM_OAUTH2_CLIENTCREDENTIAL_ID = CONN_FORM + "--OAuth2Credentials--clientID";
export const CPEM_OAUTH2_CLIENTCREDENTIAL_SECRET = CONN_FORM + "--OAuth2Credentials--clientSecret";
export const CPEM_OAUTH2_X509_ID = CONN_FORM + "--OAuth2Credentials--clientIDX509";
export const CPEM_OAUTH2_X509_KEY_UPLOADER = CONN_FORM + "--OAuth2Credentials--certificateFileUploader";
export const CPEM_OAUTH2_X509_CERTIFICATE_UPLOADER = CONN_FORM + "--OAuth2Credentials--certificateFileUploader";

export const ORACLEDB_EDIT_DPAGENT_FEATURE_FIELD = EDIT_CONN_FORM + "--dpAgents--dpAgent";
export const ORACLEDB_EDIT_ADVANCE_PROPERTIES = EDIT_CONN_FORM_CONTAINER + "--advancedFeaturesButton";
export const ORACLEDB_EDIT_ADVANCE_PROPERTIES_FORM = EDIT_CONN_FORM + "--connectionAdvancedPropertyForm";
export const ORACLEDB_EDIT_ADVANCE_PROPERTES_MAPINTEGERTODEMICAL =
  ORACLEDB_EDIT_ADVANCE_PROPERTIES_FORM + "--mapIntegerToDecimal";
export const ORACLEDB_EDIT_CONN_USERNAME = EDIT_CONN_FORM + "--user";
export const ORACLEDB_EDIT_CONN_PASSWORD = EDIT_CONN_FORM + "--password";
export const ORACLEDB_EDIT_CLOUD_CONNECTOR = EDIT_CONN_FORM + "--cloudConnector--cloudConnector";
export const ORACLEDB_EDIT_DATA_PROVISION_AGENT = EDIT_CONN_FORM + "--dpAgents--dpAgent";
export const ORACLEDB_EDIT_CONN_BUSINESSNAME = EDIT_CONN_FORM + "--connectionInfo--businessName";

export const ORACLEDB_CLOUD_CONNECTOR = CONN_FORM + "--cloudConnector--cloudConnector";
export const ORACLEDB_CLOUD_CONNECTOR_LOCATION = CONN_FORM + "--cloudConnector--cloudConnectorLocation";
export const ORACLEDB_CLOUD_CONNECTOR_VIRTUAL_DESTINATION = CONN_FORM + "--cloudConnector--virtualDestination";
export const ORACLEDB_CLOUD_CONNECTOR_VIRTUAL_HOST = CONN_FORM + "--cloudConnector--virtualHost";
export const ORACLEDB_CLOUD_CONNECTOR_VIRTUAL_PORT = CONN_FORM + "--cloudConnector--virtualPortAppServer";
export const ORACLEDB_HOST = CONN_FORM + "--host";
export const ORACLEDB_PORT = CONN_FORM + "--port";
export const ORACLEDB_DATABASENAME = CONN_FORM + "--databaseNameOrSID";
export const ORACLEDB_SERVICENAME = CONN_FORM + "--serviceName";
export const ORACLEDB_VERSION = CONN_FORM + "--version";
export const ORACLEDB_USESSL = CONN_FORM + "--useSSL";
export const ORACLEDB_USERNAME = CONN_FORM + "--user";
export const ORACLEDB_PASSWORD = CONN_FORM + "--password";
export const ORACLEDB_DNCERTIFICATE = CONN_FORM + "--distinguishedNameInCertificate";
export const ORACLEDB_CONN_DATA_PROVISION_AGENT = CONN_FORM + "--dpAgents--dpAgent";
export const ORACLEDB_CONN_TIMEZONE = CONN_FORM + "--connectionAdvancedPropertyForm--timeZoneFormat";
export const ORACLEDB_CONN_ENABLECOMPOUNDTRIGGERS =
  CONN_FORM + "--connectionAdvancedPropertyForm--enableCompoundTriggers";
export const ORACLEDB_CONN_TRIGGERSRECORDPKONLY =
  CONN_FORM + "--connectionAdvancedPropertyForm--triggersRecordPrimaryKeysOnly";
export const ORACLEDB_CONN_TRIGGERSCBAAI =
  CONN_FORM + "--connectionAdvancedPropertyForm--triggersCaptureBeforeAndAfterImages";
export const ORACLEDB_CONN_FLUSHTHRESHOLD =
  CONN_FORM + "--connectionAdvancedPropertyForm--flushThresholdOfCompoundTriggers";

// SFTP ids
export const SFTP_CATEGORY = CONN_FORM + "--category";
export const SFTP_HOST = CONN_FORM + "--host";
export const SFTP_PORT = CONN_FORM + "--port";
export const SFTP_CONN_CLIENT_HOST_KEY = CONN_FORM + "--hostKey";
export const SFTP_ROOTPATH = CONN_FORM + "--rootPath";
export const SFTP_AUTH_TYPE = CONN_FORM + "--auth_type";
export const SFTP_CONN_USER = CONN_FORM + "--credential--user";
export const SFTP_CONN_PASSWORD = CONN_FORM + "--credential--password";
export const SFTP_CONN_SSH_USER = CONN_FORM + "--ssh_credential--user";
export const SFTP_CONN_CLIENT_PRIVATE_KEY = CONN_FORM + "--ssh_credential--sshPrivateKey";
export const SFTP_CONN_PASSPHRASE = CONN_FORM + "--ssh_credential--passphrase";
export const SFTP_DATA_FLOWS = CONN_FORM + "--dataflows--dataFlowsEnabled";
export const SFTP_DATA_FLOWS_DISABLED = CONN_FORM + "--dataflows--dataFlowsDisabled";
export const SFTP_REPLICATION_FLOWS = CONN_FORM + "--replicationFlowFeature--replicationFlowsEnabled";

// SFTP Edit
export const SFTP_EDIT_CONN_BUSINESSNAME = EDIT_CONN_FORM + "--connectionInfo--businessName";
export const SFTP_EDIT_CONN_DESCRIPTION = EDIT_CONN_FORM + "--connectionInfo--connectionDescription";
export const SFTP_EDIT_CONN_CATEGORY = EDIT_CONN_FORM + "--category";
export const SFTP_EDIT_CONN_HOST = EDIT_CONN_FORM + "--host";
export const SFTP_EDIT_CONN_PORT = EDIT_CONN_FORM + "--port";
export const SFTP_EDIT_CONN_HOSTKEY = EDIT_CONN_FORM + "--hostKey"; // hostKey
export const SFTP_EDIT_CONN_ROOTPATH = EDIT_CONN_FORM + "--rootPath";
export const SFTP_EDIT_CONN_USERNAME = EDIT_CONN_FORM + "--credential--user";
export const SFTP_EDIT_CONN_PASSWORD = EDIT_CONN_FORM + "--credential--password";
export const SFTP_EDIT_CONN_SSH_USER = EDIT_CONN_FORM + "--ssh_credential--user";
export const SFTP_EDIT_CONN_CLIENT_PRIVATE_KEY = EDIT_CONN_FORM + "--ssh_credential--sshPrivateKey";
export const SFTP_EDIT_CONN_PASSPHRASE = EDIT_CONN_FORM + "--ssh_credential--passphrase";
export const SFTP_EDIT_CONN_AUTH_TYPE = EDIT_CONN_FORM + "--auth_type";
export const SFTP_EDIT_CONN_REPLICATION_FLOWS = EDIT_CONN_FORM + "--replicationFlowFeature--replicationFlowsEnabled";
export const SFTP_EDIT_COMM_DATA_FLOWS = EDIT_CONN_FORM + "--dataflows--dataFlowsEnabled";

// SFTP On Premise
export const SFTP_CLOUD_CONNECTOR = CONN_FORM + "--cloud_connector--cloudConnector";
export const SFTP_CLOUD_CONNECTOR_LOCATION = CONN_FORM + "--cloud_connector--cloudConnectorLocation";
export const SFTP_CLOUD_CONNECTOR_VIRTUAL_DESTINATION = CONN_FORM + "--cloud_connector--virtualDestination";
export const SFTP_CLOUD_CONNECTOR_VIRTUAL_HOST = CONN_FORM + "--cloud_connector--virtualHost";
export const SFTP_CLOUD_CONNECTOR_VIRTUAL_PORT = CONN_FORM + "--cloud_connector--virtualPortAppServer";
// Edit SFTP On Premise
export const SFTP_EDIT_CLOUD_CONNECTOR = EDIT_CONN_FORM + "--cloud_connector--cloudConnector";
export const SFTP_EDIT_CLOUD_CONNECTOR_LOCATION = EDIT_CONN_FORM + "--cloud_connector--cloudConnectorLocation";
export const SFTP_EDIT_CLOUD_CONNECTOR_VIRTUAL_DESTINATION = EDIT_CONN_FORM + "--cloud_connector--virtualDestination";
export const SFTP_EDIT_CLOUD_CONNECTOR_VIRTUAL_HOST = EDIT_CONN_FORM + "--cloud_connector--virtualHost";
export const SFTP_EDIT_CLOUD_CONNECTOR_VIRTUAL_PORT = EDIT_CONN_FORM + "--cloud_connector--virtualPortAppServer";

export const MSSQL_EDIT_DPAGENT_FEATURE_FIELD = EDIT_CONN_FORM + "--dpAgents--dpAgent";
export const MSSQL_EDIT_ADVANCE_PROPERTIES = EDIT_CONN_FORM_CONTAINER + "--advancedFeaturesButton";
export const MSSQL_EDIT_ADVANCE_PROPERTIES_FORM = EDIT_CONN_FORM + "--connectionAdvancedPropertyForm";
export const MSSQL_EDIT_CONN_USERNAME = EDIT_CONN_FORM + "--user";
export const MSSQL_EDIT_CONN_PASSWORD = EDIT_CONN_FORM + "--password";
export const MSSQL_EDIT_CLOUD_CONNECTOR = EDIT_CONN_FORM + "--cloudConnector--cloudConnector";
export const MSSQL_EDIT_DATA_PROVISION_AGENT = EDIT_CONN_FORM + "--dpAgents--dpAgent";
export const MSSQL_EDIT_CONN_BUSINESSNAME = EDIT_CONN_FORM + "--connectionInfo--businessName";
export const MSSQL_EDIT_SERVERNAME = EDIT_CONN_FORM + "--serverName";
export const MSSQL_EDIT_PORT = EDIT_CONN_FORM + "--port";
export const MSSQL_EDIT_DATABASENAME = EDIT_CONN_FORM + "--databaseName";
export const MSSQL_EDIT_USESSL = EDIT_CONN_FORM + "--useSSL";
export const MSSQL_EDIT_HOSTNAMECERTIFICATE = EDIT_CONN_FORM + "--hostNameInServerCertificate";
export const MSSQL_EDIT_CONN_ADDJDBCPROPS =
  EDIT_CONN_FORM + "--connectionAdvancedPropertyForm--additionalJDBCConnectionProperties";

export const MSSQL_CLOUD_CONNECTOR = CONN_FORM + "--cloudConnector--cloudConnector";
export const MSSQL_CLOUD_CONNECTOR_LOCATION = CONN_FORM + "--cloudConnector--cloudConnectorLocation";
export const MSSQL_CLOUD_CONNECTOR_VIRTUAL_DESTINATION = CONN_FORM + "--cloudConnector--virtualDestination";
export const MSSQL_CLOUD_CONNECTOR_VIRTUAL_HOST = CONN_FORM + "--cloudConnector--virtualHost";
export const MSSQL_CLOUD_CONNECTOR_VIRTUAL_PORT = CONN_FORM + "--cloudConnector--virtualPortAppServer";
export const MSSQL_SERVERNAME = CONN_FORM + "--serverName";
export const MSSQL_PORT = CONN_FORM + "--port";
export const MSSQL_DATABASENAME = CONN_FORM + "--databaseName";
export const MSSQL_VERSION = CONN_FORM + "--version";
export const MSSQL_USESSL = CONN_FORM + "--useSSL";
export const MSSQL_USERNAME = CONN_FORM + "--user";
export const MSSQL_PASSWORD = CONN_FORM + "--password";
export const MSSQL_HOSTNAMECERTIFICATE = CONN_FORM + "--hostNameInServerCertificate";
export const MSSQL_CONN_DATA_PROVISION_AGENT = CONN_FORM + "--dpAgents--dpAgent";
export const MSSQL_CONN_ADDJDBCPROPS =
  CONN_FORM + "--connectionAdvancedPropertyForm--additionalJDBCConnectionProperties";
export const MSSQL_CONN_ENABLEABAPMTN =
  CONN_FORM + "--connectionAdvancedPropertyForm--enableABAPManageableTriggerNamespace";
export const MSSQL_CONN_ABAPMTN = CONN_FORM + "--connectionAdvancedPropertyForm--abapManageableTriggerNamespace";
export const MSSQL_CONN_ENABLECOMPOUNDTRIGGERS = CONN_FORM + "--connectionAdvancedPropertyForm--enableCompoundTriggers";
export const MSSQL_CONN_TRIGGERSRECORDPKONLY =
  CONN_FORM + "--connectionAdvancedPropertyForm--triggersRecordPrimaryKeysOnly";
export const MSSQL_CONN_TRIGGERSCBAAI =
  CONN_FORM + "--connectionAdvancedPropertyForm--triggersCaptureBeforeAndAfterImages";
export const MSSQL_CONN_ENABLETRANSMERGE = CONN_FORM + "--connectionAdvancedPropertyForm--enableTransactionMerge";
export const MSSQL_CONN_TRANSMITDATA = CONN_FORM + "--connectionAdvancedPropertyForm--transmitDataInCompactMode";

export const AZURESQL_SERVERNAME = CONN_FORM + "--serverName";
export const AZURESQL_PORT = CONN_FORM + "--port";
export const AZURESQL_AUTH_TYPE = CONN_FORM + "--auth_type";
export const AZURESQL_DATABASENAME = CONN_FORM + "--databaseName";
export const AZURESQL_VERSION = CONN_FORM + "--version";
export const AZURESQL_USESSL = CONN_FORM + "--useSSL";
export const AZURESQL_USERNAME = CONN_FORM + "--usernamePasswordCredentials--user";
export const AZURESQL_PASSWORD = CONN_FORM + "--usernamePasswordCredentials--password";
export const AZURESQL_HOSTNAMECERTIFICATE = CONN_FORM + "--hostNameInServerCertificate";
export const AZURESQL_CONN_DATA_PROVISION_AGENT = CONN_FORM + "--dpAgents--dpAgent";
export const AZURESQL_CONN_CLIENT_CERTIFICATE_USERNAME = CONN_FORM + "--x509ClientCredentials--user";
export const AZURESQL_CONN_CLIENT_CERTIFICATE = CONN_FORM + "--x509ClientCredentials--certificateFileUploader";
export const AZURESQL_CONN_CLIENT_PRIVATE_KEY = CONN_FORM + "--x509ClientCredentials--keyFileUploader";
export const AZURESQL_CONN_CLIENT_PRIVATE_KEY_PASSWORD =
  CONN_FORM + "--x509ClientCredentials--clientPrivateKeyPassword";

export const AZURESQL_EDIT_AUTH_TYPE = EDIT_CONN_FORM + "--auth_type";
export const AZURESQL_EDIT_DPAGENT_FEATURE_FIELD = EDIT_CONN_FORM + "--dpAgents--dpAgent";
export const AZURESQL_EDIT_CONN_USERNAME = EDIT_CONN_FORM + "--usernamePasswordCredentials--user";
export const AZURESQL_EDIT_CONN_PASSWORD = EDIT_CONN_FORM + "--usernamePasswordCredentials--password";
export const AZURESQL_EDIT_DATA_PROVISION_AGENT = EDIT_CONN_FORM + "--dpAgents--dpAgent";
export const AZURESQL_EDIT_CONN_BUSINESSNAME = EDIT_CONN_FORM + "--connectionInfo--businessName";
export const AZURESQL_EDIT_SERVERNAME = EDIT_CONN_FORM + "--serverName";
export const AZURESQL_EDIT_PORT = EDIT_CONN_FORM + "--port";
export const AZURESQL_EDIT_VERSION = EDIT_CONN_FORM + "--version";
export const AZURESQL_EDIT_DATABASENAME = EDIT_CONN_FORM + "--databaseName";
export const AZURESQL_EDIT_HOSTNAMECERTIFICATE = EDIT_CONN_FORM + "--hostNameInServerCertificate";
export const AZURESQL_EDIT_CONN_CLIENT_CERTIFICATE_USERNAME = EDIT_CONN_FORM + "--x509ClientCredentials--user";
export const AZURESQL_EDIT_CONN_CLIENT_CERTIFICATE =
  EDIT_CONN_FORM + "--x509ClientCredentials--certificateFileUploader";
export const AZURESQL_EDIT_CONN_CLIENT_PRIVATE_KEY = EDIT_CONN_FORM + "--x509ClientCredentials--keyFileUploader";
export const AZURESQL_EDIT_CONN_CLIENT_PRIVATE_KEY_PASSWORD =
  EDIT_CONN_FORM + "--x509ClientCredentials--clientPrivateKeyPassword";
export const AZURESQL_EDIT_REMOTE_TABLES_LABEL_ENABLED = EDIT_CONN_FORM + "--remoteTableFeature--remoteTablesEnabled";
export const AZURESQL_EDIT_CONN_FAILED_MESSAGE = EDIT_CONN_FORM_CONTAINER + "--replicationFailedMessage-content";
export const HANA_CATEGORY = CONN_FORM + "--category";
export const HANA_HOST = CONN_FORM + "--host";
export const HANA_PORT = CONN_FORM + "--port";
export const HANA_AUTH_TYPE = CONN_FORM + "--auth_type";
export const HANA_CONN_USER = CONN_FORM + "--usernamePasswordCredentials--user";
export const HANA_CONN_PASSWORD = CONN_FORM + "--usernamePasswordCredentials--password";
export const HANA_CONN_CLIENT_CERTIFICATE = CONN_FORM + "--x509ClientCredentials--certificateFileUploader";
export const HANA_CONN_CLIENT_PRIVATE_KEY = CONN_FORM + "--x509ClientCredentials--keyFileUploader";
export const HANA_CONN_CLIENT_PRIVATE_KEY_PASSWORD = CONN_FORM + "--x509ClientCredentials--clientPrivateKeyPassword";
export const HANA_REMOTE_TABLES = CONN_FORM + "--remoteTableFeature--remoteTablesEnabled";
export const HANA_REMOTE_TABLES_LABEL_FIELD = CONN_FORM + "--remoteTableFeature--remoteTables-9--labelField";
export const HANA_DATA_FLOWS = CONN_FORM + "--dataFlowFeature--dataFlowsEnabled";
export const HANA_REPLICATION_FLOWS = CONN_FORM + "--replicationFlowFeature--replicationFlowsEnabled";
export const HANA_REMOTE_TABLES_DISABLED = CONN_FORM + "--remoteTableFeature--remoteTablesDisabled";
export const HANA_DATA_FLOWS_DISABLED = CONN_FORM + "--dataFlowFeature--dataFlowsDisabled";
export const HANA_REPLICATION_FLOWS_DISABLED = CONN_FORM + "--replicationFlowFeature--replicationFlowsDisabled";
export const HANA_SSL_ENCRYPTION = CONN_FORM + "--security--encrypt";
export const HANA_SERVER_CERTIFICATE = CONN_FORM + "--security--validate_certificate";
export const HANA_HOSTNAME_SERVER_CERTIFICATE = CONN_FORM + "--security--host_name_in_certificate";

export const HANA_DATA_PROVISION_OPTION = CONN_FORM + "--dataProvisioningOption";
export const HANA_DATA_PROVISION_AGENT = CONN_FORM + "--dpAgents--dpAgent";
export const HANA_DATA_PROVISION_OPTION_CLOUD = CONN_FORM + "--cloudDataProvisioningOption";
export const HANA_EDIT_CONN_USERNAME = EDIT_CONN_FORM + "--usernamePasswordCredentials--user";
export const HANA_EDIT_CONN_PASSWORD = EDIT_CONN_FORM + "--usernamePasswordCredentials--password";
export const HANA_EDIT_DATA_PROVISION_AGENT = EDIT_CONN_FORM + "--dpAgents--dpAgent";
export const HANA_EDIT_CONN_BUSINESSNAME = EDIT_CONN_FORM + "--connectionInfo--businessName";

export const ABAP_DATAFLOWS_LABEL_FIELD = CONN_FORM + "--dataFlowFeature--dataFlowsEnabled";
export const ABAP_DATAFLOWS_BUTTON_FIELD = CONN_FORM + "--dataFlowfeatures--dataFlowsInfo";
export const ABAP_REMOTETABLES_LABEL_ENABLED_FIELD = CONN_FORM + "--remoteTableFeature--remoteTablesEnabled";
export const ABAP_REMOTETABLES_LABEL_DISABLED_FIELD = CONN_FORM + "--remoteTableFeature--remoteTablesDisabled";
export const ABAP_REMOTETABLES_BUTTON_FIELD = CONN_FORM + "--remoteTableFeature--remoteTablesInfo";

export const ABAP_CONN_PROTOCOL = CONN_FORM + "--endpoint";
export const ABAP_CONN_SERVER_TYPE = CONN_FORM + "--connectionType";
export const ABAP_CONN_APP_SERVER = CONN_FORM + "--host";
export const ABAP_CONN_RFC_MSG_SERVER = CONN_FORM + "--host";
export const ABAP_CONN_RFC_MSG_SERVER_PORT = CONN_FORM + "--port";
export const ABAP_CONN_WSRFC_APP_SERVER_PORT = CONN_FORM + "--port";
export const ABAP_CONN_RFC_MSG_SERVER_GROUP = CONN_FORM + "--serverGroup";
export const ABAP_CONN_RFC_APP_SERVER_SYSTEMNO = CONN_FORM + "--systemnumber";
export const ABAP_ADAPTER_SYSTEMNO = CONN_FORM + "--systemnumber";
export const ABAP_ADAPTER_CLIENT = CONN_FORM + "--client";
export const ABAP_CONN_APP_SERVER_SYSTEMID = CONN_FORM + "--sysidAppServer";
export const ABAP_CONN_MSG_SERVER_SYSTEMID = CONN_FORM + "--sysidMsgServer";
export const ABAP_CONN_LANGUAGE = CONN_FORM + "--language";
export const ABAP_ADAPTER_INSTANCE = CONN_FORM + "--ConnectionPropertyEntry-5--stringField";
// authentication
export const ABAP_CONN_AUTH_TYPE = CONN_FORM + "--auth_type";
export const ABAP_ADAPTER_USERNAME = CONN_FORM + "--usernamePasswordCredentials--user";
export const ABAP_ADAPTER_PASSWORD = CONN_FORM + "--usernamePasswordCredentials--password";
// OAuth2
export const ABAP_OAUTH2_GRANT_TYPE = CONN_FORM + "--oAuth2Credentials--oauth2GrantType";
export const ABAP_OAUTH2_TOKEN_ENDPOINT = CONN_FORM + "--oAuth2Credentials--oauth2TokenEndpoint";
export const ABAP_OAUTH2_SCOPE = CONN_FORM + "--oAuth2Credentials--oauth2Scope";
export const ABAP_OAUTH2_TOKEN_REQUEST_CONTENT_TYPE = CONN_FORM + "--oAuth2Credentials--oauth2TokenRequestContentType";
export const ABAP_OAUTH2_CLIENT_ID = CONN_FORM + "--oAuth2Credentials--clientId";
export const ABAP_OAUTH2_CLIENT_SECRET = CONN_FORM + "--oAuth2Credentials--clientSecret";
// x509
export const ABAP_CLIENT_CERTIFICATE = CONN_FORM + "--x509ClientCredentials--certificateFileUploader";
export const ABAP_CLIENT_PRIVATE_KEY = CONN_FORM + "--x509ClientCredentials--keyFileUploader";
export const ABAP_CLIENT_PRIVATE_KEY_PASSWORD = CONN_FORM + "--x509ClientCredentials--clientPrivateKeyPassword";
// cloud connector
export const ABAP_ADAPTER_USE_CLOUD_CONNECTOR = CONN_FORM + "--cloudConnector--cloudConnector";
export const ABAP_ADAPTER_LOCATION = CONN_FORM + "--cloudConnector--cloudConnectorLocation";
export const ABAP_CLOUD_CONNECTOR_VIRTUAL_DESTINATION = CONN_FORM + "--cloudConnector--virtualDestination";
export const ABAP_CLOUD_CONNECTOR_VIRTUAL_HOST = CONN_FORM + "--cloudConnector--virtualHost";
export const ABAP_CLOUD_CONNECTOR_VIRTUAL_PORT = CONN_FORM + "--cloudConnector--VirtualPort";
export const ABAP_CLOUD_CONNECTOR_VIRTUAL_PORT_MSG_SERVER = CONN_FORM + "--cloudConnector--virtualPortMsgServer";
export const ABAP_CLOUD_CONNECTOR_VIRTUAL_PORT_APP_SERVER = CONN_FORM + "--cloudConnector--virtualPortAppServer";
// remote tables feature
export const ABAP_DATA_PROVISION_OPTION = CONN_FORM + "--dataProvisioningOption";
export const ABAP_DATA_PROVISION_AGENT = CONN_FORM + "--dpAgents--dpAgent";
export const ABAP_DATA_ACCESS = CONN_FORM + "--dataAccess";
export const ABAP_DATA_PROVISION_OPTION_DIRECT_TYPEMAP = CONN_FORM + "--typeMap";
export const ABAP_STREAMING_READ = CONN_FORM + "--streamingRead";
export const ABAP_CONN_GATEWAY_HOST = CONN_FORM + "--gwHost";
export const ABAP_CONN_GATEWAY_PORT = CONN_FORM + "--gwPort";
export const ABAP_CONN_RFC_DESTINATION = CONN_FORM + "--rfcDest";
export const ABAP_CONN_RFC_SERIALIZATION = CONN_FORM + "--connectionAdvancedPropertyForm--serialization";

export const ABAP_EDIT_BUSINESS_NAME = EDIT_CONN_FORM + "--connectionInfo--businessName";
export const ABAP_EDIT_CONN_SERVER_PROTOCOL = EDIT_CONN_FORM + "--endpoint";
export const ABAP_EDIT_CONN_SERVER_TYPE = EDIT_CONN_FORM + "--connectionType";
export const ABAP_EDIT_CONN_SERVER = EDIT_CONN_FORM + "--host";
export const ABAP_EDIT_CONN_APP_SERVER_SYSTEMNO = EDIT_CONN_FORM + "--systemnumber";
export const ABAP_EDIT_CONN_MSG_SERVER_PORT = EDIT_CONN_FORM + "--port";
export const ABAP_EDIT_CONN_MSG_SERVER_GROUP = EDIT_CONN_FORM + "--serverGroup";
export const ABAP_EDIT_CONN_CLIENT = EDIT_CONN_FORM + "--client";

export const ABAP_EDIT_CONN_SYSTEMID = EDIT_CONN_FORM + "--connectionForm--ConnectionPropertyEntry-10--stringField";

export const ABAP_EDIT_CONN_APP_SERVER_SYSTEMID = EDIT_CONN_FORM + "--sysidAppServer";
export const ABAP_EDIT_CONN_MSG_SERVER_SYSTEMID = EDIT_CONN_FORM + "--sysidMsgServer";

export const ABAP_EDIT_CONN_LANGUAGE = EDIT_CONN_FORM + "--language";
// cloud connector
export const ABAP_EDIT_CLOUD_CONNECTOR = EDIT_CONN_FORM + "--cloudConnector--cloudConnector";
export const ABAP_EDIT_CLOUD_CONNECTOR_LOCATION = EDIT_CONN_FORM + "--cloudConnector--cloudConnectorLocation";
export const ABAP_EDIT_CLOUD_CONNECTOR_VIRTUAL_DESTINATION = EDIT_CONN_FORM + "--cloudConnector--virtualDestination";
export const ABAP_EDIT_CLOUD_CONNECTOR_VIRTUAL_HOST = EDIT_CONN_FORM + "--cloudConnector--virtualHost";
export const ABAP_EDIT_CLOUD_CONNECTOR_VIRTUAL_PORT = EDIT_CONN_FORM + "--cloudConnector--VirtualPort";
export const ABAP_EDIT_CLOUD_CONNECTOR_VIRTUAL_PORT_MSG_SERVER =
  EDIT_CONN_FORM + "--cloudConnector--virtualPortMsgServer";
export const ABAP_EDIT_CLOUD_CONNECTOR_VIRTUAL_PORT_APP_SERVER =
  EDIT_CONN_FORM + "--cloudConnector--virtualPortAppServer";
export const ABAP_EDIT_DATA_PROVISION_OPTION = EDIT_CONN_FORM + "--dataProvisioningOption";
// authentication type
export const ABAP_EDIT_CONN_AUTH_TYPE = EDIT_CONN_FORM + "--auth_type";

// Basic
export const ABAP_EDIT_CONN_USERNAME = EDIT_CONN_FORM + "--usernamePasswordCredentials--user";
export const ABAP_EDIT_CONN_PASSWORD = EDIT_CONN_FORM + "--usernamePasswordCredentials--password";
// X509
export const ABAP_EDIT_CERTIFICATE = EDIT_CONN_FORM + "--x509ClientCredentials--certificateFileUploader";
export const ABAP_EDIT_PRIVATE_KEY = EDIT_CONN_FORM + "--x509ClientCredentials--keyFileUploader";
export const ABAP_EDIT_PRIVATE_KEY_PASSWORD = EDIT_CONN_FORM + "--x509ClientCredentials--clientPrivateKeyPassword";
// OAuth2
export const ABAP_EDIT_OAUTH2_GRANT_TYPE = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2GrantType";
export const ABAP_EDIT_OAUTH2_TOKEN_ENDPOINT = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2TokenEndpoint";
export const ABAP_EDIT_OAUTH2_SCOPE = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2Scope";
export const ABAP_EDIT_OAUTH2_TOKEN_REQUEST_CONTENT_TYPE =
  EDIT_CONN_FORM + "--oAuth2Credentials--oauth2TokenRequestContentType";
export const ABAP_EDIT_OAUTH2_CLIENT_ID = EDIT_CONN_FORM + "--oAuth2Credentials--clientId";
export const ABAP_EDIT_OAUTH2_CLIENT_SECRET = EDIT_CONN_FORM + "--oAuth2Credentials--clientSecret";
// remote tables feature
export const ABAP_EDIT_REMOTE_TABLES_FEATURE_TEXT_ENABLED =
  EDIT_CONN_FORM + "--remoteTableFeature--remoteTablesEnabled";
export const ABAP_EDIT_REMOTE_TABLES_FEATURE_TEXT_DISABLED =
  EDIT_CONN_FORM + "--remoteTableFeature--remoteTablesDisabled";

export const S3_ENDPOINT = CONN_FORM + "--endpoint";
export const S3_POROTOCOL = CONN_FORM + "--porotocol";
export const S3_ROOTPATH = CONN_FORM + "--rootPath";
export const S3_SERVERSIDEENCRYPTION = CONN_FORM + "--serverSideEncryption--serverSideEncryption";
export const S3_ENCRYPTIONKEYTYPE = CONN_FORM + "--serverSideEncryption--encryptionKeyType";
export const S3_ENCRYPTIONKEYARN = CONN_FORM + "--serverSideEncryption--encryptionKeyARN";
export const S3_ASSUMEROLE = CONN_FORM + "--assumeRole--assumeRole";
export const S3_ROLEARN = CONN_FORM + "--assumeRole--roleARN";
export const S3_ROLESESSIONNAME = CONN_FORM + "--assumeRole--roleSessionName";
export const S3_EXTERNALID = CONN_FORM + "--assumeRole--externalId";
export const S3_ROLEPOLICY = CONN_FORM + "--assumeRole--rolePolicy";
export const S3_ACCESSKEY = CONN_FORM + "--accessKeyCredential--accessKey";
export const S3_SECRETEKEY = CONN_FORM + "--accessKeyCredential--secretKey";

export const S3_EDIT_ACCESSKEY = EDIT_CONN_FORM + "--accessKeyCredential--accessKey";
export const S3_EDIT_SECRETEKEY = EDIT_CONN_FORM + "--accessKeyCredential--secretKey";

export const BIGQUERY_PROJECT = CONN_FORM + "--project";
export const BIGQUERY_LOCATION = CONN_FORM + "--location";
export const BIGQUERY_KEY = CONN_FORM + "--credential--password";

export const GCS_PROJECT = CONN_FORM + "--project";
export const GCS_ROOTPATH = CONN_FORM + "--rootpath";
export const GCS_KEY = CONN_FORM + "--credential--password";
export const GCS_KEY_FORMAT_ERROR = "id-*************-418";

export const ADL_AUTH_TYPE = CONN_FORM + "--auth_type";
export const ADL_ACCOUNTNAME = CONN_FORM + "--accountName";
export const ADL_ROOTPATH = CONN_FORM + "--rootPath";
export const ADL_SHAREDKEY_PASSWORD = CONN_FORM + "--sharedkey_credential--password";
export const ADL_SHARED_ACC_SIG_PASSWORD = CONN_FORM + "--sharedaccesssignature_credential--password";
export const ADL_OAUTH2_GRANT_TYPE = CONN_FORM + "--oAuth2Credentials--oauth2GrantType";
export const ADL_OAUTH2_TOKEN_ENDPOINT = CONN_FORM + "--oAuth2Credentials--oauth2TokenEndpoint";
export const ADL_OAUTH2_CLIENT_ENDPOINT = CONN_FORM + "--oAuth2Credentials--oauth2ClientEndpoint";
export const ADL_OAUTH2_CLIENT_ID = CONN_FORM + "--oAuth2Credentials--clientId";
export const ADL_OAUTH2_CLIENT_SECRET = CONN_FORM + "--oAuth2Credentials--clientSecret";
export const ADL_OAUTH2_X509_KEY_UPLOADER = CONN_FORM + "--oAuth2Credentials--keyFileUploader";
export const ADL_OAUTH2_X509_CERTIFICATE_UPLOADER = CONN_FORM + "--oAuth2Credentials--certificateFileUploader";
export const ADL_OAUTH2_PASSWORDGT_USERNAME = CONN_FORM + "--oAuth2Credentials--user";
export const ADL_OAUTH2_PASSWORDGT_PASSWORD = CONN_FORM + "--oAuth2Credentials--password";

export const ADL_EDIT_AUTH_TYPE = EDIT_CONN_FORM + "--auth_type";
export const ADL_EDIT_ACCOUNTNAME = EDIT_CONN_FORM + "--accountName";
export const ADL_EDIT_ROOTPATH = EDIT_CONN_FORM + "--rootPath";
export const ADL_EDIT_OAUTH2_GRANT_TYPE = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2GrantType";
export const ADL_EDIT_SHAREDKEY_PASSWORD = EDIT_CONN_FORM + "--sharedkey_credential--password";
export const ADL_EDIT_SHARED_ACC_SIG_PASSWORD = EDIT_CONN_FORM + "--sharedaccesssignature_credential--password";

export const ADL_EDIT_OAUTH2_TOKEN_ENDPOINT = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2TokenEndpoint";
export const ADL_EDIT_OAUTH2_CLIENT_ENDPOINT = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2ClientEndpoint";
export const ADL_EDIT_OAUTH2_CLIENT_ID = EDIT_CONN_FORM + "--oAuth2Credentials--clientId";
export const ADL_EDIT_OAUTH2_CLIENT_SECRET = EDIT_CONN_FORM + "--oAuth2Credentials--clientSecret";
export const ADL_EDIT_OAUTH2_X509_KEY_UPLOADER = EDIT_CONN_FORM + "--oAuth2Credentials--keyFileUploader";
export const ADL_EDIT_OAUTH2_X509_CERTIFICATE_UPLOADER =
  EDIT_CONN_FORM + "--OAuth2Credentials--certificateFileUploader";
export const ADL_EDIT_OAUTH2_PASSWORDGT_USERNAME = EDIT_CONN_FORM + "--oAuth2Credentials--username";
export const ADL_EDIT_OAUTH2_PASSWORDGT_PASSWORD = EDIT_CONN_FORM + "--oAuth2Credentials--password";

export const BIGQUERY_EDIT_KEY = EDIT_CONN_FORM + "--credential--password";
export const BIGQUERY_EDIT_LOCATION = EDIT_CONN_FORM + "--location";
export const BIGQUERY_EDIT_PROJECT = EDIT_CONN_FORM + "--project";

export const GCS_EDIT_KEY = EDIT_CONN_FORM + "--credential--password";
export const GCS_EDIT_ROOTPATH = EDIT_CONN_FORM + "--rootpath";
export const GCS_EDIT_PROJECT = EDIT_CONN_FORM + "--project";

export const SAPSF_URL = CONN_FORM + "--url";
export const SAPSF_VERSION = CONN_FORM + "--version";
export const SAPSF_AUTH_TYPE = CONN_FORM + "--auth_type";
export const SAPSF_TOKEN_ENDPOINT = CONN_FORM + "--SAMLBearer--oauth2TokenEndpoint";
export const SAPSF_OAUTH_SCOPE = CONN_FORM + "--SAMLBearer--oauth2Scope";
export const SAPSF_API_ENDPOINT = CONN_FORM + "--SAMLBearer--oauth2ApiEndpoint";
export const SAPSF_USER_ID = CONN_FORM + "--SAMLBearer--oauth2UserId";
export const SAPSF_COMPANY_ID = CONN_FORM + "--SAMLBearer--oauth2CompanyId";
export const SAPSF_CLIENT_ID = CONN_FORM + "--SAMLBearer--clientId";
export const SAPSF_CLIENT_SECRET = CONN_FORM + "--SAMLBearer--clientSecret";
export const SAPSF_SAML_ASSERTION = CONN_FORM + "--SAMLBearer--samlAssertion";
export const SAPSF_USERNAME = CONN_FORM + "--usernamePasswordCredentials--user";
export const SAPSF_PASSWORD = CONN_FORM + "--usernamePasswordCredentials--password";

export const KAFKA_BROKERS = CONN_FORM + "--KAFKABrokers";
export const KAFKA_CLOUD_CONNECTOR = CONN_FORM + "--cloudConnector--cloudConnector";
export const KAFKA_CLOUD_CONNECTOR_LOCATION = CONN_FORM + "--cloudConnector--cloudConnectorLocation";
export const KAFKA_CLOUD_CONNECTOR_VIRTUAL_DESTINATION = CONN_FORM + "--cloudConnector--virtualDestination";
export const KAFKA_CLOUD_CONNECTOR_VIRTUAL_HOST = CONN_FORM + "--cloudConnector--virtualHost";
export const KAFKA_AUTH_TYPE = CONN_FORM + "--auth_type";
export const KAFKA_MTLS_ENABLE = CONN_FORM + "--TLSCredential--useMTLS";

export const KAFKA_SASL_USER = CONN_FORM + "--SASLCredentials--user";
export const KAFKA_SASL_PASSWORD = CONN_FORM + "--SASLCredentials--password";
export const KAFKA_SASL_KERBEROS_SERVICENAME = CONN_FORM + "--SASLCredentials--kerberosServiceName";
export const KAFKA_SASL_KERBEROS_REALM = CONN_FORM + "--SASLCredentials--kerberosRealm";
export const KAFKA_SASL_KERBEROS_CONFIG_FILE = CONN_FORM + "--SASLCredentials--kerberosConfigFile";
export const KAFKA_SASL_KERBEROS_KEYTAB = CONN_FORM + "--SASLCredentials--kerberoskeyTab";
export const KAFKA_CLOUD_CONNECTOR_VIRTUAL_PORT_MSG_SERVER = CONN_FORM + "--cloudConnector--virtualPortMsgServer";
export const KAFKA_CLIENT_KEY_UPLOADER = CONN_FORM + "--TLSCredential--clientKey";
export const KAFKA_CERTIFICATE_UPLOADER = CONN_FORM + "--TLSCredential--clientCertificate";

export const KAFKA_CLIENT_KEY_SR_UPLOADER = CONN_FORM + "--TLSCredentialSchemaRegistry--SchemaRegistryClientKey";
export const KAFKA_CERTIFICATE_SR_UPLOADER =
  CONN_FORM + "--TLSCredentialSchemaRegistry--SchemaRegistryClientCertificate";

export const CONFLUENT_BROKERS = CONN_FORM + "--KAFKABrokers";
export const CONFLUENT_SYSTEM_TYPE = CONN_FORM + "--systemType";
export const CONFLUENT_CLOUD_CONNECTOR = CONN_FORM + "--cloudConnector--cloudConnector";
export const CONFLUENT_CLOUD_CONNECTOR_LOCATION = CONN_FORM + "--cloudConnector--cloudConnectorLocation";
export const CONFLUENT_CLOUD_CONNECTOR_VIRTUAL_DESTINATION = CONN_FORM + "--cloudConnector--virtualDestination";
export const CONFLUENT_CLOUD_CONNECTOR_VIRTUAL_HOST = CONN_FORM + "--cloudConnector--virtualHost";
export const CONFLUENT_AUTH_TYPE = CONN_FORM + "--auth_type";
export const CONFLUENT_MTLS_ENABLE = CONN_FORM + "--TLSCredential--useMTLS";

export const CONFLUENT_TLS_SR_ENABLE = CONN_FORM + "--TLSCredentialSchemaRegistry--SchemaRegistryUseTLS";
export const CONFLUENT_VCertificate_SR_ENABLE =
  CONN_FORM + "--TLSCredentialSchemaRegistry--SchemaRegistryValidateCertificate";

export const CONFLUENT_SASL_USER = CONN_FORM + "--SASLCredentials--user";
export const CONFLUENT_SASL_PASSWORD = CONN_FORM + "--SASLCredentials--password";
export const CONFLUENT_SASL_KERBEROS_SERVICENAME = CONN_FORM + "--SASLCredentials--kerberosServiceName";
export const CONFLUENT_SASL_KERBEROS_REALM = CONN_FORM + "--SASLCredentials--kerberosRealm";
export const CONFLUENT_SASL_KERBEROS_CONFIG_FILE = CONN_FORM + "--SASLCredentials--kerberosConfigFile";
export const CONFLUENT_SASL_KERBEROS_KEYTAB = CONN_FORM + "--SASLCredentials--kerberoskeyTab";
export const CONFLUENT_CLOUD_CONNECTOR_VIRTUAL_PORT_MSG_SERVER = CONN_FORM + "--cloudConnector--virtualPortMsgServer";
export const CONFLUENT_CLIENT_KEY_UPLOADER = CONN_FORM + "--TLSCredential--clientKey";
export const CONFLUENT_CERTIFICATE_UPLOADER = CONN_FORM + "--TLSCredential--clientCertificate";
export const CONFLUENT_CLIENT_KEY_SR_UPLOADER = CONN_FORM + "--TLSCredentialSchemaRegistry--SchemaRegistryClientKey";
export const CONFLUENT_CERTIFICATE_SR_UPLOADER =
  CONN_FORM + "--TLSCredentialSchemaRegistry--SchemaRegistryClientCertificate";

export const CONFLUENT_SCHEMA_REGISTERY_URL = CONN_FORM + "--schemaRegistry--schemaRegistryUrl";
export const CONFLUENT_SCHEMA_REGISTERY_AUTH_TYPE = CONN_FORM + "--schemaRegistry--schemaRegistryAuthType";
export const CONFLUENT_SCHEMA_REGISTERY_USER = CONN_FORM + "--schemaRegistry--schemaRegistryUser";
export const CONFLUENT_SCHEMA_REGISTERY_PASS = CONN_FORM + "--schemaRegistry--schemaRegistryPassword";

export const HTTP_CONN_HOST = CONN_FORM + "--host";
export const HTTP_CONN_PORT = CONN_FORM + "--port";
export const HTTP_CONN_PROTOCOL = CONN_FORM + "--protocol";
export const HTTP_CONN_PATH = CONN_FORM + "--path";
export const HTTP_CLOUD_CONNECTOR = CONN_FORM + "--cloudConnector--cloudConnector";
export const HTTP_CLOUD_CONNECTOR_LOCATION = CONN_FORM + "--cloudConnector--cloudConnectorLocation";
export const HTTP_CLOUD_CONNECTOR_VIRTUAL_DESTINATION = CONN_FORM + "--cloudConnector--virtualDestination";
export const HTTP_CLOUD_CONNECTOR_VIRTUAL_HOST = CONN_FORM + "--cloudConnector--virtualHost";
export const HTTP_CLOUD_CONNECTOR_VIRTUAL_PORT = CONN_FORM + "--cloudConnector--virtualPortAppServer";
export const HTTP_CONN_AUTH_TYPE = CONN_FORM + "--auth_type";
export const HTTP_EDIT_AUTH_TYPE = EDIT_CONN_FORM + "--auth_type";
export const HTTP_USERNAME = CONN_FORM + "--usernamePasswordCredentials--user";
export const HTTP_EDIT_USERNAME = EDIT_CONN_FORM + "--usernamePasswordCredentials--user";
export const HTTP_PASSWORD = CONN_FORM + "--usernamePasswordCredentials--password";
export const HTTP_EDIT_PASSWORD = EDIT_CONN_FORM + "--usernamePasswordCredentials--password";
export const HTTP_OAUTH2_GRANT_TYPE = CONN_FORM + "--oAuth2Credentials--oauth2GrantType";
export const HTTP_EDIT_OAUTH2_GRANT_TYPE = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2GrantType";
export const HTTP_OAUTH2_TOKEN_ENDPOINT = CONN_FORM + "--oAuth2Credentials--oauth2TokenEndpoint";
export const HTTP_EDIT_OAUTH2_TOKEN_ENDPOINT = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2TokenEndpoint";
export const HTTP_OAUTH2_SCOPE = CONN_FORM + "--oAuth2Credentials--oauth2Scope";
export const HTTP_EDIT_OAUTH2_SCOPE = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2Scope";
export const HTTP_OAUTH2_TOKEN_REQUEST_CONTENT_TYPE = CONN_FORM + "--oAuth2Credentials--oauth2TokenRequestContentType";
export const HTTP_EDIT_OAUTH2_TOKEN_REQUEST_CONTENT_TYPE =
  EDIT_CONN_FORM + "--oAuth2Credentials--oauth2TokenRequestContentType";
export const HTTP_OAUTH2_RESOURCE = CONN_FORM + "--oAuth2Credentials--oauth2Resource";
export const HTTP_EDIT_OAUTH2_RESOURCE = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2Resource";
export const HTTP_OAUTH2_RESPONSE_TYPE = CONN_FORM + "--oAuth2Credentials--oauth2ResponseType";
export const HTTP_EDIT_OAUTH2_RESPONSE_TYPE = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2ResponseType";
export const HTTP_OAUTH2_CLIENT_ID = CONN_FORM + "--oAuth2Credentials--clientId";
export const HTTP_EDIT_OAUTH2_CLIENT_ID = EDIT_CONN_FORM + "--oAuth2Credentials--clientId";
export const HTTP_OAUTH2_CLIENT_SECRET = CONN_FORM + "--oAuth2Credentials--clientSecret";
export const HTTP_EDIT_OAUTH2_CLIENT_SECRET = EDIT_CONN_FORM + "--oAuth2Credentials--clientSecret";
export const HTTP_OAUTH2_X509_CERTIFICATE_UPLOADER = CONN_FORM + "--oAuth2Credentials--certificateFileUploader";
export const HTTP_EDIT_OAUTH2_X509_CERTIFICATE_UPLOADER =
  EDIT_CONN_FORM + "--oAuth2Credentials--certificateFileUploader";
export const HTTP_OAUTH2_X509_KEY_UPLOADER = CONN_FORM + "--oAuth2Credentials--keyFileUploader";
export const HTTP_EDIT_OAUTH2_X509_KEY_UPLOADER = EDIT_CONN_FORM + "--oAuth2Credentials--keyFileUploader";
export const HTTP_OAUTH2_PASSWORD_USERNAME = CONN_FORM + "--oAuth2Credentials--user";
export const HTTP_EDIT_OAUTH2_PASSWORD_USERNAME = EDIT_CONN_FORM + "--oAuth2Credentials--user";
export const HTTP_OAUTH2_PASSWORD_PASSWORD = CONN_FORM + "--oAuth2Credentials--password";
export const HTTP_EDIT_OAUTH2_PASSWORD_PASSWORD = EDIT_CONN_FORM + "--oAuth2Credentials--password";
export const HTTP_OAUTH2_PASSWORD_CLIENT_ID = CONN_FORM + "--oAuth2Credentials--clientIdPasswordGrant";
export const HTTP_EDIT_OAUTH2_PASSWORD_CLIENT_ID = EDIT_CONN_FORM + "--oAuth2Credentials--clientIdPasswordGrant";
export const HTTP_OAUTH2_PASSWORD_CLIENT_SECRET = CONN_FORM + "--oAuth2Credentials--clientSecretPasswordGrant";
export const HTTP_EDIT_OAUTH2_PASSWORD_CLIENT_SECRET =
  EDIT_CONN_FORM + "--oAuth2Credentials--clientSecretPasswordGrant";
export const HTTP_API_TASKS = CONN_FORM + "--apiTasksFeature--apiTasksEnabled";

export const ONELAKE_CONN_REGION = CONN_FORM + "--region";
export const ONELAKE_CONN_ROOTPATH = CONN_FORM + "--rootPath";
export const ONELAKE_CONN_URL = CONN_FORM + "--url";
export const ONELAKE_CONN_AUTH_TYPE = CONN_FORM + "--auth_type";
export const ONELAKE_EDIT_AUTH_TYPE = EDIT_CONN_FORM + "--auth_type";
export const ONELAKE_OAUTH2_GRANT_TYPE = CONN_FORM + "--oAuth2Credentials--oauth2GrantType";
export const ONELAKE_EDIT_OAUTH2_GRANT_TYPE = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2GrantType";
export const ONELAKE_OAUTH2_TOKEN_ENDPOINT = CONN_FORM + "--oAuth2Credentials--oauth2TokenEndpoint";
export const ONELAKE_EDIT_OAUTH2_TOKEN_ENDPOINT = EDIT_CONN_FORM + "--oAuth2Credentials--oauth2TokenEndpoint";
export const ONELAKE_OAUTH2_CLIENT_ENDPOINT = CONN_FORM + "--oAuth2Credentials--oauth2ClientEndpoint";
export const ONELAKE_OAUTH2_CLIENT_ID = CONN_FORM + "--oAuth2Credentials--clientId";
export const ONELAKE_EDIT_OAUTH2_CLIENT_ID = EDIT_CONN_FORM + "--oAuth2Credentials--clientId";
export const ONELAKE_OAUTH2_CLIENT_SECRET = CONN_FORM + "--oAuth2Credentials--clientSecret";
export const ONELAKE_EDIT_OAUTH2_CLIENT_SECRET = EDIT_CONN_FORM + "--oAuth2Credentials--clientSecret";
export const ONELAKE_OAUTH2_X509_CERTIFICATE_UPLOADER = CONN_FORM + "--oAuth2Credentials--certificateFileUploader";
export const ONELAKE_EDIT_OAUTH2_X509_CERTIFICATE_UPLOADER =
  EDIT_CONN_FORM + "--oAuth2Credentials--certificateFileUploader";
export const ONELAKE_OAUTH2_X509_KEY_UPLOADER = CONN_FORM + "--oAuth2Credentials--keyFileUploader";
export const ONELAKE_EDIT_OAUTH2_X509_KEY_UPLOADER = EDIT_CONN_FORM + "--oAuth2Credentials--keyFileUploader";
export const ONELAKE_OAUTH2_PASSWORD_USERNAME = CONN_FORM + "--oAuth2Credentials--user";
export const ONELAKE_EDIT_OAUTH2_PASSWORD_USERNAME = EDIT_CONN_FORM + "--oAuth2Credentials--user";
export const ONELAKE_OAUTH2_PASSWORD_PASSWORD = CONN_FORM + "--oAuth2Credentials--password";
export const ONELAKE_EDIT_OAUTH2_PASSWORD_PASSWORD = EDIT_CONN_FORM + "--oAuth2Credentials--password";
export const ONELAKE_OAUTH2_PASSWORD_CLIENT_ID = CONN_FORM + "--oAuth2Credentials--clientIdPasswordGrant";
export const ONELAKE_EDIT_OAUTH2_PASSWORD_CLIENT_ID = EDIT_CONN_FORM + "--oAuth2Credentials--clientIdPasswordGrant";
export const ONELAKE_OAUTH2_PASSWORD_CLIENT_SECRET = CONN_FORM + "--oAuth2Credentials--clientSecretPasswordGrant";
export const ONELAKE_EDIT_OAUTH2_PASSWORD_CLIENT_SECRET =
  EDIT_CONN_FORM + "--oAuth2Credentials--clientSecretPasswordGrant";

export const ODATA_URL = CONN_FORM + "--url";
export const ODATA_VERSION = CONN_FORM + "--version";
export const ODATA_AUTH_TYPE = CONN_FORM + "--auth_type";
export const ODATA_OAUTH2_TOKEN_ENDPOINT = CONN_FORM + "--oAuth2Credentials--oauth2TokenEndpoint";
export const ODATA_OAUTH2_SCOPE = CONN_FORM + "--oAuth2Credentials--oauth2Scope";
export const ODATA_OAUTH2_TOKEN_REQUEST_CONTENT_TYPE = CONN_FORM + "--oAuth2Credentials--oauth2TokenRequestContentType";
export const ODATA_CLIENT_ID = CONN_FORM + "--oAuth2Credentials--clientId";
export const ODATA_CLIENT_SECRET = CONN_FORM + "--oAuth2Credentials--clientSecret";
export const ODATA_USERNAME = CONN_FORM + "--usernamePasswordCredentials--user";
export const ODATA_PASSWORD = CONN_FORM + "--usernamePasswordCredentials--password";
export const ODATA_HTTP_KEY =
  CONN_FORM +
  "--connectionAdvancedPropertyForm--keyId-connectionWizardView--connectionForm--connectionAdvancedPropertyForm--tableId-0";
export const ODATA_HTTP_VALUE =
  CONN_FORM +
  "--connectionAdvancedPropertyForm--valueId-connectionWizardView--connectionForm--connectionAdvancedPropertyForm--tableId-0";
export const ODATA_HTTP_ADD =
  "connectionWizardView--connectionForm--connectionAdvancedPropertyForm--addHttpHeaderButton";
export const REPLICATION_FLOW_FEATURE_SECTION_CREATE = CONN_FORM + "--replicationFlowFeature";
export const REPLICATION_FLOW_FEATURE_SECTION_EDIT = EDIT_CONN_FORM + "--replicationFlowFeature";

export const DATA_FLOW_FEATURE_SECTION_CREATE = CONN_FORM + "--dataFlowFeature";
export const DATA_FLOW_FEATURE_SECTION_EDIT = EDIT_CONN_FORM + "--dataFlowFeature";

// Connection Info - stage 4
export const CONN_BUSINESS_NAME_FIELD = CONN_FORM + "--connectionInfo--businessName";
export const CONN_TECH_NAME_FIELD = CONN_FORM + "--connectionInfo--technicalName";

// JDBC ids
export const JDBC_DRIVER_CLASS = CONN_FORM + "--driverClass";
export const JDBC_URL = CONN_FORM + "--url";
export const JDBC_USER = CONN_FORM + "--usernamePasswordCredentials--user";
export const JDBC_PASSWORD = CONN_FORM + "--usernamePasswordCredentials--password";
export const JDBC_DPAGENT = CONN_FORM + "--dpAgents--dpAgent";
export const JDBC_REMOTE_TABLES_LABEL_ENABLED = CONN_FORM + "--remoteTableFeature--remoteTablesEnabled";
export const JDBC_REMOTE_TABLES_LABEL_DISABLED = CONN_FORM + "--remoteTableFeature--remoteTablesDisabled";

export const JDBC_EDIT_CONN_DRIVER_CLASS = EDIT_CONN_FORM + "--driverClass";
export const JDBC_EDIT_CONN_URL = EDIT_CONN_FORM + "--url";
export const JDBC_EDIT_CONN_USER = EDIT_CONN_FORM + "--usernamePasswordCredentials--user";
export const JDBC_EDIT_CONN_PASSWORD = EDIT_CONN_FORM + "--usernamePasswordCredentials--password";
export const JDBC_EDIT_CONN_DPAGENT = EDIT_CONN_FORM + "--dpAgents--dpAgent";
export const JDBC_EDIT_REMOTE_TABLES_LABEL_ENABLED = EDIT_CONN_FORM + "--remoteTableFeature--remoteTablesEnabled";
export const JDBC_EDIT_REMOTE_TABLES_LABEL_DISABLED = EDIT_CONN_FORM + "--remoteTableFeature--remoteTablesDisabled";
