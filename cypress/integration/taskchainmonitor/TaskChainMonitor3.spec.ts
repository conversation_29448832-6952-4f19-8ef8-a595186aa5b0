/** @format */

import moment from "moment";
import { BuilderType, getAPIInstance } from "../../pageobjects/api/IAPI";
import { APIImpl } from "../../pageobjects/api/impl/APIImpl";
import { XHelpers } from "../../pageobjects/api/impl/Helpers";
import { ShellPages } from "../../pageobjects/shell";
/* jscpd:ignore-start */
describe.skip("Task Chain Monitoring: 3", () => {
  let api: APIImpl;
  const DI_PAGE = "shellMainContent---dataIntegrationComponent---taskchainmonitor";
  const DI_LOG_PAGE = "shellMainContent---dataIntegrationComponent---taskLog";
  const TAB_ID = "shellMainContent---dataIntegrationComponent---dilandingpage--iconTabBar";
  const TAB_KEY = "taskChainMonitor";
  const TASKSCHEDULE_DIALOG = `${DI_PAGE}--taskChainSchedulingDialog--taskScheduleDialog--view`;
  const SEARCH_FIELD = `${DI_PAGE}--searchTablesInput`;
  const savedSchedule = {
    scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
    spaceId: "SPACE1234",
    applicationId: "TASK_CHAINS",
    activity: "RUN_CHAIN",
    objectId: "fabian_chain_2",
    externalScheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
    description: "Task_Chain_1",
    validFrom: "2022-04-22T00:00:00.000Z",
    activationStatus: "ENABLED",
    createdBy: "RICKY_ROY",
    createdAt: "2022-04-22T04:24:22.260Z",
    changedBy: "RICKY_ROY",
    changedAt: "2022-04-22T04:24:28.683Z",
    owner: "RICKY_ROY",
    uiVariant: "FORM",
    frequency: { interval: 1, startDate: "2022-04-22T00:00:00.000Z", type: "DAILY" },
    nextRun: "2022-04-22T04:24:00.000Z",
  };
  const headerInfo = {
    logId: 2396775,
    spaceId: "MODELING_BLR",
    applicationId: "TASK_CHAINS",
    objectId: "Task_Chain_Schedule_Test",
    activity: "RUN_CHAIN",
    startTime: "2022-05-24T03:13:52.680Z",
    endTime: "2022-05-24T03:17:01.796Z",
    status: "FAILED",
    runId: "ced2686f-2f87-4615-67f5-a1234535581d",
    user: "Ravikumar Kasirajan",
    parameters: '{"tf":{"isDirect":true,"isOnlyUpdate":false}}',
    runTime: 189116,
  };

  describe(`${Cypress.spec.relative} without SDP`, () => {
    beforeEach(async () => {
      api = getAPIInstance();
      const routes = [...XHelpers.DEFAULT_MOCK_REPONSES];
      XHelpers.modifyFeatureFlag(routes, "DWC_DUMMY_SPACE_PERMISSIONS", false);
      XHelpers.modifyFeatureFlag(routes, "DWC_MODELING_SWITCH_TECHNICAL_NAME", true);
      await api.setupBeforeEach(/*useMock*/ true, /*defaultFixture*/ true, /*routes*/ routes);
      await api.mockServerCall(
        "GET",
        "**/remoteTables**",
        "fixture:remotetablemonitor/remoteTableBlueGreenReplication",
        "getRemoteTables"
      );
      await api.mockServerCall("GET", "**/taskchains**", "fixture:taskchainmonitor/taskChainOverview", "getTaskChains");
      await api.mockServerCall(
        "GET",
        "**/schedules?**",
        "fixture:taskchainmonitor/taskChainSchedules",
        "getTaskChainSchedules"
      );
      await api.mockServerCall("GET", "**/schedules/*", savedSchedule, "getSchedule");
      await api.mockServerCall(
        "GET",
        "**/logs?objectId**",
        "fixture:taskchainmonitor/taskChainLogs",
        "getAllTaskChainLogs"
      );
      await api.mockServerCall(
        "GET",
        "**/taskchains/2233384",
        "fixture:taskchainmonitor/taskChainLogDetail",
        "getTaskChainDetailLog1"
      );
      await api.mockServerCall(
        "GET",
        "**/taskchains/2219265",
        "fixture:taskchainmonitor/taskChainLogDetail2",
        "getTaskChainDetailLog2"
      );
      await api.mockServerCall(
        "GET",
        "**/logs?taskLogId=2233385",
        "fixture:taskchainmonitor/chainRemoteTableLog",
        "getTaskChainTableDetailLog"
      );
      await api.mockServerCall(
        "GET",
        "**/logs?taskLogId=2240412",
        "fixture:taskchainmonitor/chainRemoteTableLog",
        "getTaskChainTableDetailLog2"
      );
      cy.intercept("GET", "**/resources/spaces?islocked&spaceids=SPACE1234**", { SPACE1234: { isLocked: false } }).as(
        "getSpaceLockStatus"
      );
      await api.mockServerCall("GET", "**/schedules/consent", { consent: true }, "getConsent");
      await api.mockServerCall("GET", "**/info", headerInfo, "getHeaderInfo");
    });

    describe("Task Chain monitoring schedule dialog checks", () => {
      it("Check for next run warning in create Schedule", async () => {
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        await api.selectIconTabBarItem(TAB_ID, TAB_KEY);
        const tableId = `${DI_PAGE}--taskChainsTable`;
        await api.XHRWait("@getTaskChains");
        await api.assertControlExists({
          id: tableId,
        });
        await api.assertTable({
          table: tableId,
          rows: 11,
        });
        const newSchedule = [
          {
            scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
            spaceId: "TASKFRAMEWORK",
            applicationId: "TASK_CHAINS",
            activity: "RUN_CHAIN",
            objectId: "fabian_chain_2",
            externalScheduleId: "99c80954-7ea7-4f94-94c9-b932b8a2731d",
            description: "fabian_chain_2",
            cron: "0 */1 * * *",
            validFrom: "2022-01-12T00:00:00.000Z",
            activationStatus: "ENABLED",
            createdBy: "RAVIKUMAR_KASIRAJAN",
            createdAt: "2022-01-12T13:49:54.275Z",
            changedBy: "RAVIKUMAR_KASIRAJAN",
            changedAt: "2022-01-12T13:49:54.896Z",
            owner: "RAVIKUMAR_KASIRAJAN",
            uiVariant: "FORM",
            nextRun: "2022-01-18T21:00:00.000Z",
          },
        ];
        await api.mockServerCall("GET", "**/schedules?**", newSchedule, "getTaskChainSchedules");
        const table = await cy.byId(tableId);
        await table.getRows()[1].getRowAction().getItems()[0];
        await api.selectTableRow(tableId, 1, false, true);
        await api.clickMenuButtonItem(`${DI_PAGE}--monitoringSchedulingMenu`, 0);
        await api.assertControlExists({
          id: `${DI_PAGE}--taskChainSchedulingDialog--taskScheduleDialog`,
        });
        const now = moment.utc();
        now.add(8, "minute");
        let min = now.minute();
        let hour = now.hour();
        let time = (hour < 9 ? "0" + hour : hour) + ":" + (min < 9 ? "0" + min : min);
        cy.get(`#${TASKSCHEDULE_DIALOG}--scheduleWarnInfo`).should("not.exist");
        cy.get(`#${TASKSCHEDULE_DIALOG}--scheduleTime-inner`).clear().type(time).focused().blur();
        cy.get(`#${TASKSCHEDULE_DIALOG}--scheduleWarnInfo`).should("exist");
        now.add(10, "minute");
        min = now.minute();
        hour = now.hour();
        time = (hour < 9 ? "0" + hour : hour) + ":" + (min < 9 ? "0" + min : min);
        cy.get(`#${TASKSCHEDULE_DIALOG}--scheduleTime-inner`).clear().type(time).focused().blur();
        cy.get(`#${TASKSCHEDULE_DIALOG}--scheduleWarnInfo`).should("not.exist");

        now.add(5, "day");
        const date1Format = now.format("MMM D, YYYY");
        cy.get(`#${TASKSCHEDULE_DIALOG}--startDate-inner`).clear().type(date1Format).focused().blur();
        cy.get(`#${TASKSCHEDULE_DIALOG}--nextRun1`).should("contain.text", date1Format);
      });

      it("Check for create Schedule", async () => {
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        await api.selectIconTabBarItem(TAB_ID, TAB_KEY);
        const tableId = `${DI_PAGE}--taskChainsTable`;
        await api.XHRWait("@getTaskChains");
        await api.assertControlExists({
          id: tableId,
        });
        await api.assertTable({
          table: tableId,
          rows: 11,
        });
        const newSchedule1 = [
          {
            scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
            spaceId: "TASKFRAMEWORK",
            applicationId: "TASK_CHAINS",
            activity: "RUN_CHAIN",
            objectId: "fabian_chain_2",
            externalScheduleId: "99c80954-7ea7-4f94-94c9-b932b8a2731d",
            description: "fabian_chain_2",
            cron: "0 */1 * * *",
            validFrom: "2022-01-12T00:00:00.000Z",
            activationStatus: "ENABLED",
            createdBy: "RAVIKUMAR_KASIRAJAN",
            createdAt: "2022-01-12T13:49:54.275Z",
            changedBy: "RAVIKUMAR_KASIRAJAN",
            changedAt: "2022-01-12T13:49:54.896Z",
            owner: "RAVIKUMAR_KASIRAJAN",
            uiVariant: "FORM",
            nextRun: "2022-01-18T21:00:00.000Z",
          },
        ];
        await api.mockServerCall("GET", "**/schedules?**", newSchedule1, "getTaskChainSchedules1");
        const table = await cy.byId(tableId);
        await table.getRows()[1].getRowAction().getItems()[0];
        await api.selectTableRow(tableId, 1, false, true);
        await api.wait(1);
        await api.clickMenuButtonItem(`${DI_PAGE}--monitoringSchedulingMenu`, 0);
        await api.wait(1);
        await api.assertControlExists({
          id: `${DI_PAGE}--taskChainSchedulingDialog--taskScheduleDialog`,
        });
        await api.mockServerCall(
          "POST",
          "**/schedules**",
          { scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" },
          "createSchedule"
        );
        await api.clickButton(`${DI_PAGE}--taskChainSchedulingDialog--taskScheduleDialog--view--createButton`, false);
        await api.XHRWait("@createSchedule");
        await api.XHRWait("@getTaskChains");
        await api.XHRWait("@getTaskChainSchedules1");
        await api.assertTable({
          table: tableId,
          rows: 11,
        });
        const table2 = await cy.byId(tableId);
        await table2.getRows()[7].getRowAction().getItems()[0];
        await api.selectTableRow(tableId, 7, false, true);
        await api.wait(1);
        await api.assertMenuButtonItem({
          id: `${DI_PAGE}--monitoringSchedulingMenu`,
          itemIndex: 0,
          enabled: false,
        });
        await api.assertMenuButtonItem({
          id: `${DI_PAGE}--monitoringSchedulingMenu`,
          itemIndex: 1,
          enabled: true,
        });
        await api.assertMenuButtonItem({
          id: `${DI_PAGE}--monitoringSchedulingMenu`,
          itemIndex: 2,
          enabled: true,
        });
      });

      it("Check for Task chain with not triggered children", async () => {
        await api.mockServerCall(
          "GET",
          "**/taskchains/2233384",
          "fixture:taskchainmonitor/taskChainLogDetail4",
          "getTaskChainDetailLog1"
        );
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        await api.selectIconTabBarItem(TAB_ID, TAB_KEY);
        const tableId = `${DI_PAGE}--taskChainsTable`;
        const logTable = `${DI_LOG_PAGE}--taskLogTable`;
        const treeMsgtable = `${DI_LOG_PAGE}--taskChainMessageTable`;
        const treeTable = `${DI_LOG_PAGE}--taskChainLogMessagesTable`;
        await api.XHRWait("@getTaskChains");
        await api.assertControlExists({
          id: tableId,
        });
        await api.assertTable({
          table: tableId,
          rows: 11,
        });
        await api.selectTableRow(tableId, 2, false, true);
        const table = await cy.byId(tableId);
        await table.getRows()[0].getRowAction().getItems()[0].firePress();
        await api.assertTable({
          table: logTable,
          rows: 11,
        });
        await api.assertTable({
          table: treeTable,
          rows: 4,
        });
        await api.assertTable({
          table: treeMsgtable,
          rows: 8,
        });
        await api.selectTableRow(treeTable, 3, false, true);
        await api.assertTable({
          table: treeMsgtable,
          rows: 0,
        });
        await api.assertText({
          id: `${treeTable}-rows-row3-col3`,
          value: "",
        });
      });
    });
  });

  describe(`${Cypress.spec.relative} with SDP`, () => {
    beforeEach(async () => {
      api = getAPIInstance();
      const routes = [...XHelpers.DEFAULT_MOCK_REPONSES, ...XHelpers.SDP_SPECIFIC_MOCK_RESPONSES];
      XHelpers.modifyFeatureFlag(routes, "DWC_MODELING_SWITCH_TECHNICAL_NAME", true);
      await api.setupBeforeEach(
        /* useMock*/ true,
        /* defaultFixture*/ true,
        /* routes*/ routes,
        /* overrideRouteCallback*/ undefined,
        /* enabledFeatureFlags*/ ["DWC_DUMMY_SPACE_PERMISSIONS"]
      );
      await api.mockServerCall(
        "GET",
        "**/remoteTables",
        "fixture:remotetablemonitor/remoteTableBlueGreenReplication",
        "getRemoteTables"
      );
      await api.mockServerCall("GET", "**/taskchains", "fixture:taskchainmonitor/taskChainOverview", "getTaskChains");
      await api.mockServerCall(
        "GET",
        "**/schedules?**",
        "fixture:taskchainmonitor/taskChainSchedules",
        "getTaskChainSchedules"
      );
      await api.mockServerCall("GET", "**/schedules/*", savedSchedule, "getSchedule");
      await api.mockServerCall(
        "GET",
        "**/logs?objectId**",
        "fixture:taskchainmonitor/taskChainLogs",
        "getAllTaskChainLogs"
      );
      await api.mockServerCall(
        "GET",
        "**/taskchains/2233384",
        "fixture:taskchainmonitor/taskChainLogDetail",
        "getTaskChainDetailLog1"
      );
      await api.mockServerCall(
        "GET",
        "**/taskchains/2219265",
        "fixture:taskchainmonitor/taskChainLogDetail2",
        "getTaskChainDetailLog2"
      );
      await api.mockServerCall(
        "GET",
        "**/logs?taskLogId=2233385",
        "fixture:taskchainmonitor/chainRemoteTableLog",
        "getTaskChainTableDetailLog"
      );
      await api.mockServerCall("GET", "**/schedules/consent", { consent: true }, "getConsent");
      await api.mockServerCall("GET", "**/info", headerInfo, "getHeaderInfo");
    });

    describe("Task Chain monitoring schedule dialog checks", () => {
      it("Check for next run warning in create Schedule", async () => {
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        await api.selectIconTabBarItem(TAB_ID, TAB_KEY);
        const tableId = `${DI_PAGE}--taskChainsTable`;
        await api.XHRWait("@getTaskChains");
        await api.assertControlExists({
          id: tableId,
        });
        await api.assertTable({
          table: tableId,
          rows: 11,
        });
        const newSchedule = [
          {
            scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d",
            spaceId: "TASKFRAMEWORK",
            applicationId: "TASK_CHAINS",
            activity: "RUN_CHAIN",
            objectId: "fabian_chain_2",
            externalScheduleId: "99c80954-7ea7-4f94-94c9-b932b8a2731d",
            description: "fabian_chain_2",
            cron: "0 */1 * * *",
            validFrom: "2022-01-12T00:00:00.000Z",
            activationStatus: "ENABLED",
            createdBy: "RAVIKUMAR_KASIRAJAN",
            createdAt: "2022-01-12T13:49:54.275Z",
            changedBy: "RAVIKUMAR_KASIRAJAN",
            changedAt: "2022-01-12T13:49:54.896Z",
            owner: "RAVIKUMAR_KASIRAJAN",
            uiVariant: "FORM",
            nextRun: "2022-01-18T21:00:00.000Z",
          },
        ];
        await api.mockServerCall("GET", "**/schedules?**", newSchedule, "getTaskChainSchedules");
        const table = await cy.byId(tableId);
        await table.getRows()[1].getRowAction().getItems()[0];
        await api.selectTableRow(tableId, 1, false, true);
        await api.clickMenuButtonItem(`${DI_PAGE}--monitoringSchedulingMenu`, 0);
        await api.assertControlExists({
          id: `${DI_PAGE}--taskChainSchedulingDialog--taskScheduleDialog`,
        });
        const now = moment.utc();
        now.add(8, "minute");
        let min = now.minute();
        let hour = now.hour();
        let time = (hour < 9 ? "0" + hour : hour) + ":" + (min < 9 ? "0" + min : min);
        cy.get(`#${TASKSCHEDULE_DIALOG}--scheduleWarnInfo`).should("not.exist");
        cy.get(`#${TASKSCHEDULE_DIALOG}--scheduleTime-inner`).clear().type(time).focused().blur();
        cy.get(`#${TASKSCHEDULE_DIALOG}--scheduleWarnInfo`).should("exist");
        now.add(10, "minute");
        min = now.minute();
        hour = now.hour();
        time = (hour < 9 ? "0" + hour : hour) + ":" + (min < 9 ? "0" + min : min);
        cy.get(`#${TASKSCHEDULE_DIALOG}--scheduleTime-inner`).clear().type(time).focused().blur();
        cy.get(`#${TASKSCHEDULE_DIALOG}--scheduleWarnInfo`).should("not.exist");

        now.add(5, "day");
        const date1Format = now.format("MMM D, YYYY");
        cy.get(`#${TASKSCHEDULE_DIALOG}--startDate-inner`).clear().type(date1Format).focused().blur();
        cy.get(`#${TASKSCHEDULE_DIALOG}--nextRun1`).should("contain.text", date1Format);
      });
    });
  });
});
/* jscpd:ignore-end */
