/** @format */

import moment from "moment";
import { BuilderType, getAPIInstance } from "../../../pageobjects/api/IAPI";
import { APIImpl } from "../../../pageobjects/api/impl/APIImpl";
import { XHelpers } from "../../../pageobjects/api/impl/Helpers";
import { ShellPages } from "../../../pageobjects/shell";
/* jscpd:ignore-start */
describe.skip("Remote table Monitoring task scheduling with Cron String Support and new UI", () => {
  let api: APIImpl;
  const DI_PAGE = "shellMainContent---dataIntegrationComponent---taskchainmonitor";
  const DI_LOG_PAGE = "shellMainContent---dataIntegrationComponent---taskLog";
  const TAB_ID = "shellMainContent---dataIntegrationComponent---dilandingpage--iconTabBar";
  const TAB_KEY = "taskChainMonitor";
  const TASKSCHEDULE_DIALOG_ID = `${DI_PAGE}--taskChainSchedulingDialog`;
  const SEARCH_FIELD = `${DI_PAGE}--searchTablesInput`;
  const scheduleList = [
    {
      activationStatus: "DISABLED",
      activity: "RUN_CHAIN",
      applicationId: "TASK_CHAINS",
      changedAt: "2022-01-13T11:13:13.410Z",
      changedBy: "MEIERO",
      createdAt: "2022-01-13T11:13:12.772Z",
      createdBy: "MEIERO",
      cron: "0 0 */1 * *",
      description: "orderChain",
      externalScheduleId: "905bf005-4cb1-4e2d-82c2-bfacbc26a5d5",
      nextRun: "2022-01-19T00:00:00.000Z",
      objectId: "orderChain",
      owner: "MEIERO",
      scheduleId: "c5c85b8a-8140-46ed-89ff-e01edf74d567",
      spaceId: "TASKFRAMEWORK",
      uiVariant: "FORM",
      validFrom: "2022-01-13T00:00:00.000Z",
    },
    {
      activationStatus: "ENABLED",
      activity: "RUN_CHAIN",
      applicationId: "TASK_CHAINS",
      changedAt: "2022-01-12T13:49:54.896Z",
      changedBy: "RAVIKUMAR_KASIRAJAN",
      createdAt: "2022-01-12T13:49:54.275Z",
      createdBy: "RAVIKUMAR_KASIRAJAN",
      cron: "0 */1 * * *",
      description: "Task_Chain_Demo1",
      externalScheduleId: "99c80954-7ea7-4f94-94c9-b932b8a2731d",
      nextRun: "2022-01-18T21:00:00.000Z",
      objectId: "Task_Chain_Demo1",
      owner: "RAVIKUMAR_KASIRAJAN",
      scheduleId: "5d7c6b08-b182-49df-8ea7-f77a5afea296",
      spaceId: "TASKFRAMEWORK",
      uiVariant: "FORM",
      validFrom: "2022-01-12T00:00:00.000Z",
    },
    {
      activationStatus: "ENABLED",
      activity: "RUN_CHAIN",
      applicationId: "TASK_CHAINS",
      changedAt: "2022-01-18T16:06:09.371Z",
      changedBy: "MEIERO",
      createdAt: "2022-01-18T16:06:09.044Z",
      createdBy: "MEIERO",
      cron: "0 0 */1 * *",
      description: "Task_Chain_666",
      externalScheduleId: "6ff6744d-13ee-4e1e-b54a-60521302e81d",
      nextRun: "2022-01-19T00:00:00.000Z",
      objectId: "Task_Chain_666",
      owner: "MEIERO",
      scheduleId: "cd18c9c3-b04e-4c3d-bfce-d6f4fffd2aa9",
      spaceId: "TASKFRAMEWORK",
      uiVariant: "FORM",
      validFrom: "2022-01-18T00:00:00.000Z",
    },
  ];
  describe(`${Cypress.spec.relative} without SDP`, () => {
    beforeEach(async () => {
      api = getAPIInstance();
      const routes = [...XHelpers.DEFAULT_MOCK_REPONSES];
      XHelpers.modifyFeatureFlag(routes, "DWC_DUMMY_SPACE_PERMISSIONS", false);
      XHelpers.modifyFeatureFlag(routes, "DWCO_REUSABLE_TASK_SCHEDULING_UI", true);
      XHelpers.modifyFeatureFlag(routes, "DWCO_REUSABLE_TASK_SCHEDULER_ADOPTION", true);
      await api.setupBeforeEach(/*useMock*/ true, /*defaultFixture*/ true, routes);
      await api.mockServerCall(
        "GET",
        "**/remoteTables**",
        "fixture:remotetablemonitor/taskScheduleRemoteTables",
        "getRemoteTables"
      );
      await api.mockServerCall("GET", "**/taskchains**", "fixture:taskchainmonitor/taskChainOverview", "getTaskChains");
      await api.mockServerCall("GET", "**/schedules?**", [], "getSchedules");
    });

    describe("{TaskSchedule} Check remote table monitoring task schedule flow with Cron String UI 2", async () => {
      it("Check for Cron String Future Run", async () => {
        await api.mockServerCall("GET", "**/schedules/consent", { consent: false }, "getConsent");
        await api.mockServerCall(
          "POST",
          "**/schedules",
          { scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" },
          "createSchedule"
        );
        await api.mockServerCall("GET", "**/schedules?**", scheduleList, "getSchedules");
        await api.mockServerCall("GET", "**/schedules/**", scheduleList[0], "getSchedule");
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        await api.XHRWait("@getRemoteTables");
        await api.selectIconTabBarItem(TAB_ID, TAB_KEY);
        await api.XHRWait("@getTaskChains");
        await api.assertMenuButtonItem({
          id: `${DI_PAGE}--monitoringSchedulingMenu`,
          itemIndex: 0,
          enabled: false,
        });
        await api.assertMenuButtonItem({
          id: `${DI_PAGE}--monitoringSchedulingMenu`,
          itemIndex: 1,
          enabled: false,
        });
        await api.assertMenuButtonItem({
          id: `${DI_PAGE}--monitoringSchedulingMenu`,
          itemIndex: 2,
          enabled: false,
        });
        await api.wait(2);
        await api.selectTableRow(`${DI_PAGE}--taskChainsTable`, 2, false, true);
        await api.clickMenuButtonItem(`${DI_PAGE}--monitoringSchedulingMenu`, 1);

        // await api.waitForDialogReady(`${TASKSCHEDULE_DIALOG}`);
        await api.wait(2);
        await api.selectSelectItem(`frequencyType`, 1);
        // await cy.get(`#cronMin-inner`).clear().type("30").focused().blur();
        await api.typeInputText(`cronMin`, "30");
        await api.clearInput(`endDate`);
        const date1 = moment.utc().minute(30).hour(0).add(1, "day");
        const date2 = moment.utc().minute(30).hour(0).add(2, "day");
        const date1Format = date1.format("MMM D, YYYY");
        const date2Format = date2.format("MMM D, YYYY");
        await api.assertContainsText({
          id: `nextRun1`,
          value: date1Format,
        });
        await api.assertContainsText({
          id: `nextRun2`,
          value: date2Format,
        });
      });

      it("Check for Cron string validations", async () => {
        await api.mockServerCall("GET", "**/schedules/consent", { consent: false }, "getConsent");
        await api.mockServerCall(
          "POST",
          "**/schedules",
          { scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" },
          "createSchedule"
        );
        await api.mockServerCall("GET", "**/schedules?**", scheduleList, "getSchedules");
        await api.mockServerCall("GET", "**/schedules/**", scheduleList[0], "getSchedule");
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        await api.XHRWait("@getRemoteTables");
        await api.selectIconTabBarItem(TAB_ID, TAB_KEY);
        await api.XHRWait("@getTaskChains");
        await api.wait(2);

        await api.selectTableRow(`${DI_PAGE}--taskChainsTable`, 2, false, true);
        await api.clickMenuButtonItem(`${DI_PAGE}--monitoringSchedulingMenu`, 1);

        await api.assertControlExists({
          id: `${TASKSCHEDULE_DIALOG_ID}`,
        });
        // await api.waitForDialogReady(`${TASKSCHEDULE_DIALOG}`);
        await api.clearInput(`endDate`);
        await api.selectSelectItem(`frequencyType`, 1);

        await api.typeInputText(`cronMin`, "*/1");
        await api.assertInput({
          input: `cronMin`,
          valueState: "Error",
        });

        await api.typeInputText(`cronMin`, "10");
        await api.assertInput({
          input: `cronMin`,
          valueState: "None",
        });

        await api.typeInputText(`cronMin`, "65");
        await api.assertInput({
          input: `cronMin`,
          valueState: "Error",
        });

        await api.typeInputText(`cronMin`, "10,*");
        await api.assertInput({
          input: `cronMin`,
          valueState: "Error",
        });

        await api.typeInputText(`cronMin`, "10");
        await api.assertInput({
          input: `cronMin`,
          valueState: "None",
        });

        await api.typeInputText(`cronDay`, "1-10,*/2");
        await api.assertInput({
          input: `cronDay`,
          valueState: "Error",
        });

        await api.typeInputText(`cronDay`, "1");
        await api.assertInput({
          input: `cronDay`,
          valueState: "None",
        });

        await api.typeInputText(`cronMon`, "13");
        await api.assertInput({
          input: `cronMon`,
          valueState: "Error",
        });

        await api.typeInputText(`cronMon`, "1");
        await api.assertInput({
          input: `cronMon`,
          valueState: "None",
        });

        await api.typeInputText(`cronWeek`, "1-8");
        await api.assertInput({
          input: `cronWeek`,
          valueState: "Error",
        });

        await api.typeInputText(`cronWeek`, "1");
        await api.assertInput({
          input: `cronWeek`,
          valueState: "None",
        });
      });

      it("Check for Cron string validations for minutes recurrence", async () => {
        await api.mockServerCall("GET", "**/schedules/consent", { consent: false }, "getConsent");
        await api.mockServerCall(
          "POST",
          "**/schedules",
          { scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" },
          "createSchedule"
        );
        await api.mockServerCall("GET", "**/schedules?**", scheduleList, "getSchedules");
        await api.mockServerCall("GET", "**/schedules/**", scheduleList[0], "getSchedule");
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        await api.XHRWait("@getRemoteTables");
        await api.selectIconTabBarItem(TAB_ID, TAB_KEY);
        await api.XHRWait("@getTaskChains");
        await api.wait(2);

        await api.selectTableRow(`${DI_PAGE}--taskChainsTable`, 2, false, true);
        await api.clickMenuButtonItem(`${DI_PAGE}--monitoringSchedulingMenu`, 1);

        await api.assertControlExists({
          id: `${TASKSCHEDULE_DIALOG_ID}`,
        });
        //  await api.waitForDialogReady(`${TASKSCHEDULE_DIALOG}`);
        await api.clearInput(`endDate`);
        await api.selectSelectItem(`frequencyType`, 1);
        // Check for */10 pattern
        await api.typeInputText(`cronMin`, "*/10");
        await api.assertInput({
          input: `cronMin`,
          valueState: "None",
        });

        await api.typeInputText(`cronMin`, "*/9");
        await api.assertInput({
          input: `cronMin`,
          valueState: "Error",
        });

        await api.typeInputText(`cronMin`, "*/60");
        await api.assertInput({
          input: `cronMin`,
          valueState: "Error",
        });
      });

      it("Check for weekday and day of week with cron", async () => {
        await api.mockServerCall("GET", "**/schedules/consent", { consent: false }, "getConsent");
        await api.mockServerCall("GET", "**/schedules?**", scheduleList, "getSchedules");
        await api.mockServerCall("GET", "**/schedules/**", scheduleList[0], "getSchedule");
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        await api.XHRWait("@getRemoteTables");
        await api.selectIconTabBarItem(TAB_ID, TAB_KEY);
        await api.XHRWait("@getTaskChains");
        await api.wait(2);

        await api.selectTableRow(`${DI_PAGE}--taskChainsTable`, 2, false, true);
        await api.clickMenuButtonItem(`${DI_PAGE}--monitoringSchedulingMenu`, 1);

        await api.assertControlExists({
          id: `${TASKSCHEDULE_DIALOG_ID}`,
        });
        //  await api.waitForDialogReady(`${TASKSCHEDULE_DIALOG}`);
        await api.clearInput(`endDate`);
        await api.typeInputText(`endDate`, " ");
        await api.selectSelectItem(`frequencyType`, 1);

        const now = new Date();
        const weekDay = (now.getUTCDay() + 2) % 7;
        let newDate = moment.utc().minute(0).hour(0).second(0).add(2, "days");

        await api.typeInputText(`cronMin`, "0");
        await api.typeInputText(`cronHour`, "0");
        await api.typeInputText(`cronDay`, "*/1");
        await api.typeInputText(`cronMon`, "*");
        await api.typeInputText(`cronWeek`, weekDay.toString());

        let date1Format = newDate.format("MMM D, YYYY");
        await api.assertContainsText({
          id: `nextRun1`,
          value: date1Format,
        });

        await api.typeInputText(`cronDay`, "*/2");
        newDate = moment
          .utc()
          .minute(0)
          .hour(0)
          .second(0)
          .add(moment.utc().date() % 2 === 0 ? 1 : 2, "days");
        date1Format = newDate.format("MMM D, YYYY");
        await api.assertContainsText({
          id: `nextRun1`,
          value: date1Format,
        });
      });

      it("Check for range ordering", async () => {
        await api.mockServerCall("GET", "**/schedules/consent", { consent: false }, "getConsent");
        await api.mockServerCall(
          "POST",
          "**/schedules",
          { scheduleId: "0dc5199e-76f2-4160-b11b-4a223aa9fa8d" },
          "createSchedule"
        );
        await api.mockServerCall("GET", "**/schedules?**", scheduleList, "getSchedules");
        await api.mockServerCall("GET", "**/schedules/**", scheduleList[0], "getSchedule");
        await api.openModulePage(ShellPages.DataIntegration);
        await api.openSpace("SPACE1234", BuilderType.dataintegration);
        await api.XHRWait("@getRemoteTables");
        await api.selectIconTabBarItem(TAB_ID, TAB_KEY);
        await api.XHRWait("@getTaskChains");
        await api.wait(2);

        await api.selectTableRow(`${DI_PAGE}--taskChainsTable`, 2, false, true);
        await api.clickMenuButtonItem(`${DI_PAGE}--monitoringSchedulingMenu`, 1);

        await api.assertControlExists({
          id: `${TASKSCHEDULE_DIALOG_ID}`,
        });
        //  await api.waitForDialogReady(`${TASKSCHEDULE_DIALOG}`);
        await api.clearInput(`endDate`);
        await api.selectSelectItem(`frequencyType`, 1);
        await api.typeInputText(`cronHour`, "10-15,4");
        await api.assertInput({
          input: `cronHour`,
          text: "4,10-15",
        });
        await api.typeInputText(`cronDay`, "10-15,2-7,1");
        await api.assertInput({
          input: `cronDay`,
          text: "1,2-7,10-15",
        });
      });
    });
  });
});
/* jscpd:ignore-end */
