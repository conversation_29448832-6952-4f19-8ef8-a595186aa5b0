/** @format */

import {
  assertButton,
  openRemoteTableStatisticsOverviewPage,
  prepareTestEnvironment,
  pressButton,
} from "../../pageobjects/dataIntegration/DataIntegrationHelper";
/* jscpd:ignore-start */
describe("Remote Table Statistics Mass Operations", () => {
  const tableId =
    "shellMainContent---dataIntegrationComponent---remotetablestatisticsmonitor--idRemoteTableStatisticsTable";

  beforeEach(() => {
    prepareTestEnvironment({
      DWC_DUMMY_SPACE_PERMISSIONS: false,
    });

    cy.intercept("GET", "**/userprivileges", { fixture: "privilegesDI.json" });
    cy.intercept("GET", "**/SPACE1234/remotetables**", {
      fixture: "remotetablestatisticsmonitor/remotetableDARemote",
    }).as("getRemoteTables");
    cy.intercept("GET", "**/remotetables?dataAccess=REMOTE**", {
      fixture: "remotetablestatisticsmonitor/remotetableDARemote",
    }).as("getRemoteTablesStatistics");
    cy.intercept("GET", "**/remotetables", { fixture: "remotetablestatisticsmonitor/remotetableAll" }).as(
      "getRemoteTablesStatisticsAll"
    );

    cy.intercept("GET", "**/schedules?**", [
      {
        scheduleId: "9f7cb5c6-2327-4d6e-a4e0-c7cfcd41603c",
        spaceId: "HD_SPACE_02",
        applicationId: "VIEWS",
        objectId: "Employee_Dimension_PC_Hier",
        activity: "PERSIST",
        externalScheduleId: "08950b25-434f-4e85-bd08-2864767bdc1f",
        description: "Employee_Dimension_PC_Hier",
        validFrom: "2022-12-08T00:00:00.000Z",
        validTo: "2022-12-08T23:59:00.000Z",
        activationStatus: "ENABLED",
        createdBy: "D037121",
        createdAt: "2022-12-08T10:59:33.302Z",
        changedBy: "D037121",
        changedAt: "2022-12-08T10:59:33.646Z",
        owner: "D037121",
        uiVariant: "FORM",
        frequency: {
          interval: 1,
          type: "DAILY",
          startDate: "2022-12-08T12:00:00.000Z",
        },
      },
    ]).as("getSchedules");

    cy.intercept("GET", "**/resources/spaces?islocked&spaceids=SPACE1234**", { SPACE1234: { isLocked: false } }).as(
      "getSpaceLockStatus"
    );
    cy.intercept("GET", "**/security/customerhana/info", {
      ram: 256000000000,
      storage: 256000000000,
      threads: 128,
    }).as("getCustomerHanaDetails");
    cy.intercept("GET", "**content?space=SPACE1234**", {
      SHUBHAM_NEW: {
        spaceDefinition: {
          version: "1.0.4",
          label: "Shubham_New",
          assignedStorage: 2200000000,
          assignedRam: 1000000000,
          enableDataLake: false,
          priority: 5,
          auditing: {
            dppRead: {
              retentionPeriod: 7,
              isAuditPolicyActive: false,
            },
            dppChange: {
              retentionPeriod: 7,
              isAuditPolicyActive: false,
            },
          },
          hdicontainers: {},
          workloadClass: {
            totalStatementMemoryLimit: {
              value: 1,
              unit: "Gigabyte",
            },
            totalStatementThreadLimit: {
              value: 14,
              unit: "Counter",
            },
          },
          workloadType: "custom",
          allowConsumption: false,
        },
      },
    }).as("getSpaceDetails");
  });

  describe("Remote Table Statistics Mass Operations", () => {
    beforeEach(() => {
      openRemoteTableStatisticsOverviewPage("SPACE1234");
      cy.wait(["@getUser", "@getContractEnddate"]);
    });

    it.skip("should check for removing statistics with mass operations", function () {
      assertButton(
        "shellMainContent---dataIntegrationComponent---remotetablestatisticsmonitor--deleteStatisticsBtn",
        false
      );

      cy.get(".sapUiTableSelectAllCheckBox").click();

      assertButton(
        "shellMainContent---dataIntegrationComponent---remotetablestatisticsmonitor--deleteStatisticsBtn",
        true
      );
      pressButton("shellMainContent---dataIntegrationComponent---remotetablestatisticsmonitor--deleteStatisticsBtn");
    });
  });

  /* jscpd:ignore-end */
});
/* jscpd:ignore-end */
