/** @format */

import { IUsageConsumption, Measure } from "../../../../shared/provisioning/ftc/types";
import { assertUI5ComponentState, id } from "./Assertions";
import { AdminLandingPageID, CloudConfigTabID } from "./Elements";
import { LicensesObj, mockIdealConfiguredUnits, prepareTestEnvironment } from "./Environment";

describe("FTC Number Format", () => {
  beforeEach(() => {
    cy.fixture("SACUserResponse").then((userResponse) => {
      const param = userResponse.user.parameters.find((x: any) => x.name === "DECIMAL_FORMAT");
      if (param) {
        param.value = '{"decimalFormat":{"decimalSeparator":{"symbol":"."},"groupingSeparator":{"symbol":" "}}}';
      }

      cy.intercept("GET", "**/sap/fpa/services/rest/epm/session?action=logon**", (req) => {
        req.reply({
          status: 200,
          body: userResponse,
          headers: {
            "X-Csrf-Token": "34E29D1EC0BC5A4F876F1322CB5B85ED",
          },
        });
      }).as("getUser");
    });
  });

  it("Numbers should use number formatting that came in user settings", () => {
    prepareTestEnvironment({
      licenses,
      configuredCUsMock: mockIdealConfiguredUnits,
      featureToggles,
      usageConsumption,
    });
    cy.wait(["@getUser", "@calculateCUs"]);
    assertUI5ComponentState(AdminLandingPageID.CLOUD_CONFIGURATION_TAB, "getVisible", true);

    cy.get(id(CloudConfigTabID.STORAGE_INPUT)).should("exist");
    assertUI5ComponentState(CloudConfigTabID.STORAGE_INPUT, "getValue", 262400);
    cy.get(id(CloudConfigTabID.STORAGE_INPUT_INNER)).should("have.value", "262 400");

    // base configuration
    assertValueFormat(CloudConfigTabID.STORAGE_INPUT, 262400, "262 400");
    assertValueFormat(CloudConfigTabID.MEMORY_INPUT, 102400, "102 400");
    assertTextFormat(CloudConfigTabID.VCPUS_DISPLAY, "6 400");

    // Additional Data Warehouse Configuration
    assertValueFormat(CloudConfigTabID.DATALAKE_INPUT, 9000, "9 000");
    assertValueFormat(CloudConfigTabID.BWBRIDGE_INPUT, 5000, "5 000");

    // Large Systems
    assertValueFormat(CloudConfigTabID.LARGE_SYSTEMS_STORAGE_INPUT, 1024, "1 024");
    assertValueFormat(CloudConfigTabID.LARGE_SYSTEMS_COMPUTE_INPUT, 1100, "1 100");

    // Elastic Compute Node
    assertTextFormat(CloudConfigTabID.ECN_BLOCK_SPECK, "ecnPerformanceClassTemplate(1,16)");
    assertValueFormat(CloudConfigTabID.ECN_BLOCKS, 2000, "2 000");

    // Data Integration
    assertValueFormat(CloudConfigTabID.DATA_INTEGRATION_INPUT, 1100, "1 100");
    assertTextFormat(CloudConfigTabID.DATA_INTEGRATION_EXECUTION_HOURS, "executionHoursTemplate(2 100)");
    assertTextFormat(CloudConfigTabID.DATA_INTEGRATION_MAXIMUM_PARALLEL_JOBS, "10");

    // Premium Outbound Integration
    assertValueFormat(CloudConfigTabID.PREMIUM_OUTBOUND_INPUT, 1110, "1 110");
    assertTextFormat(CloudConfigTabID.PREMIUM_OUTBOUND_MEMORY, "22 200 gigabytes");

    // Catalog
    assertValueFormat(CloudConfigTabID.CATALOG_INPUT, 1000, "1 000");
    assertTextFormat(CloudConfigTabID.ODC_STORAGE_BLOCK_VALUE, "gigabyteTemplate(1 000)");

    // Estimated Consumption
    assertTextFormat(CloudConfigTabID.CONFIGURED_CUS_STATUS, "12 118.952 CU");
    assertTextFormat(CloudConfigTabID.CU_PER_HOUR_OBJ_STATUS, "CUPerMonth(8 846 835.279)");

    // Capacity Units Card
    assertTextFormat(CloudConfigTabID.AVAILABLE_UNITS, "10 000 000");
    assertTextFormat(CloudConfigTabID.CAPACITY_UNITS_CARD_PER_MONTH_CONSUMPTION, "8 846 835.279");
    assertTextFormat(CloudConfigTabID.CAPACITY_UNITS_CARD_PER_HOUR_CONSUMPTION, "12 118.952");
    assertTextFormat(CloudConfigTabID.REMAINING_CUS, "1 153 164.721");

    // Data Integration Usage Card
    assertTextFormat(CloudConfigTabID.ALLOCATED_EXECUTION_HOURS_DATA_INTEGRATION, "20 000.111");
    assertTextFormat(CloudConfigTabID.USED_EXECUTION_HOURS_DATA_INTEGRATION, "25 000.111");
    assertTextFormat(CloudConfigTabID.OVERUSAGE_EXECUTION_HOURS_DATA_INTEGRATION, "5 000.111");

    // Premium Outbound Integration Card
    assertTextFormat(CloudConfigTabID.ALLOCATED_VOLUME_PREMIUM_OUTBOUND, "20 000.111");
    assertTextFormat(CloudConfigTabID.USED_VOLUME_PREMIUM_OUTBOUND, "25 000.111");
    assertTextFormat(CloudConfigTabID.OVERUSAGE_PREMIUM_OUTBOUND, "5 000.111");

    // Catalog Usage Card
    assertTextFormat(CloudConfigTabID.ALLOCATED_CATALOG_STORAGE, "19 531.25");
    assertTextFormat(CloudConfigTabID.USED_CATALOG_STORAGE, "24 414.063");
    assertTextFormat(CloudConfigTabID.OVERUSAGE_CATALOG_STORAGE, "4 882.813");

    // Elastic Compute Node Usage Card
    assertTextFormat(CloudConfigTabID.ALLOCATED_ELASTIC_COMPUTE_NODE, "20 000.111");
    assertTextFormat(CloudConfigTabID.USED_ELASTIC_COMPUTE_NODE, "25 000.111");
    assertTextFormat(CloudConfigTabID.OVERUSAGE_ELASTIC_COMPUTE_NODE, "5 000.111");
  });
});

function assertValueFormat(FtcId: CloudConfigTabID, valueRaw: number, valueFormatted: string) {
  cy.get(id(FtcId)).should("exist");
  assertUI5ComponentState(FtcId, "getValue", valueRaw);
  cy.get(id(`${FtcId}-input-inner`)).should("have.value", valueFormatted);
}

function assertTextFormat(FtcId: CloudConfigTabID, valueFormatted: string) {
  cy.get(id(FtcId)).should("exist");
  cy.get(id(FtcId)).should("include.text", valueFormatted);
}

const featureToggles = {
  DWCO_FTC_BW_BRIDGE_NEW_SIZES: true,
  DWCO_LARGE_SYSTEMS: true,
};

const usageConsumption = {
  [Measure.DI_EXTRACTION_HOURS]: {
    allocatedValue: 20000.111,
    usedValue: 25000.111,
    overusageValue: 5000.111,
  },
  [Measure.CATALOG_STORAGE]: {
    allocatedValue: 20000000.111,
    usedValue: 25000000.111,
    overusageValue: 5000000.111,
  },
  [Measure.ELASTIC_COMPUTE_NODE]: {
    allocatedValue: 20000.111,
    usedValue: 25000.111,
    overusageValue: 5000.111,
  },
  [Measure.PREMIUM_OUTBOUND]: {
    allocatedValue: 20000.111,
    usedValue: 25000.111,
    overusageValue: 5000.111,
  },
} as IUsageConsumption;

const licenses: LicensesObj = {
  thresholdDWCCU: 10000000,
  thresholdStorage: 262400,
  thresholdDataLakeStorage: 9000,
  thresholdBWBridge1: 5000,
  thresholdMemory: 102400,
  thresholdVCPU: 6400,
  thresholdCatalogStorage: 1024000,
  thresholdECNBlock: 2000,
  thresholdRmsNodeHours: 1100,
  thresholdRmsPremiumOutbound: 1110,
  thresholdECNPerformanceClass: "memory",
  thresholdRmsConcurrencyIncluded: 111000,
  thresholdRmsNodeHoursIncluded: 1000,
  thresholdLargeSystemsStorage: 1024,
  thresholdLargeSystemsCompute: 1100,
};
