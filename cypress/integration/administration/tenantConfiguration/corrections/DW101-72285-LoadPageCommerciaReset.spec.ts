/** @format */

import { TenantClassification } from "../../../../../shared/provisioning/ftc/types";
import { assertUI5ComponentState } from "../Assertions";
import { CloudConfigTabID } from "../Elements";
import { LicensesObj, mockIdealConfiguredUnits, prepareTestEnvironment } from "../Environment";

context("Correction DW101-72285", () => {
  it("Should load correct the page for DWC tenants", () => {
    const licenses: LicensesObj = {
      thresholdDWCCU: 50000,
      thresholdStorage: 0,
      thresholdMemory: 0,
      thresholdVCPU: 0,
      thresholdDataLakeStorage: 0,
      thresholdBWBridge1: 0,
      tenantType: TenantClassification.DWC,
    };
    prepareTestEnvironment({
      licenses,
      configuredCUsMock: mockIdealConfiguredUnits,
    });

    assertUI5ComponentState(CloudConfigTabID.STORAGE_INPUT, "getValue", 128);
    assertUI5ComponentState(CloudConfigTabID.MEMORY_INPUT, "getValue", 32);
  });

  it("Should load correct the page for Test Tenants", () => {
    const licenses: LicensesObj & { tenantClassification: TenantClassification } = {
      thresholdDWCCU: 50000,
      thresholdStorage: 0,
      thresholdMemory: 0,
      thresholdVCPU: 0,
      thresholdDataLakeStorage: 0,
      thresholdBWBridge1: 0,
      tenantClassification: TenantClassification.DWCcustomerTest,
    };
    prepareTestEnvironment({
      licenses,
      configuredCUsMock: mockIdealConfiguredUnits,
    });

    assertUI5ComponentState(CloudConfigTabID.STORAGE_INPUT, "getValue", 128);
    assertUI5ComponentState(CloudConfigTabID.MEMORY_INPUT, "getValue", 32);
  });
});
