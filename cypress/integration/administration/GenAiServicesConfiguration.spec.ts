/** @format */

// FILEOWNER: [dw101_crossarchitecture]

import {
  IAIFeaturesConfigStoreData,
  ICategoryWithAIFeatures,
  ICategoryWithAvailableAIFeatures,
} from "../../../shared/administration/types";
import { TestUser } from "../managespaces/_util/SpacesTestUtil";
describe("Gen AI Services Configuration", () => {
  const aiFeaturesConfigurationRequiredFeatureFlags = {
    DWCO_AI_UNITS_SKU: true,
    DWCO_JOULE_AI_CONFIGURATION_SCREEN: false,
  };

  const aiFeaturesConfigurationRequiredFeatureFlagsWithAIFeatureFlags = {
    DWCO_AI_UNITS_SKU: true,
    DWCO_JOULE_AI_CONFIGURATION_SCREEN: false,
    DWC_FEATURE1: false,
    DWC_FEATURE2: false,
    DWC_FEATURE4: true,
  };

  const administrationMainId = "shellMainContent---administrationComponent---administration";
  const aiServicesConfigurationMainId = "shellMainContent---administrationComponent---genAiServicesConfiguration";

  beforeEach(() => {
    cy.intercept("GET", "**/deepsea*", {});
    cy.intercept("GET", "**/notifications/**", {});
    cy.intercept("GET", "**/security/customerhana/license", {});
    cy.intercept("GET", "**/security/customtenantclassification*", { data: {} });
    cy.intercept("GET", "**/tf/admin/logs/getcleanupschedule", {});
    cy.intercept("GET", "**/tf/schedules/consent", {});
    cy.intercept("GET", "**/dpagent", {});
    cy.intercept("GET", "**/tenant/links", {});
  });

  describe("AI Services Configuration Screen", () => {
    it("ai service tab should not be accessible for an user without administrator privileges although a necessary feature flag is true", () => {
      cy.login(TestUser.DWC_SDP_MODELER, {
        DWCO_JOULE_AI_CONFIGURATION_SCREEN: true,
      });
      cy.visit("#configuration");
      cy.get(".sapHcsShell404").should("be.visible");
      cy.visit("#configuration&/cfg/genAiServicesConfiguration");
      cy.get(".sapHcsShell404").should("be.visible");
    });

    it("ai service tab and the ai features section should be displayed if ff 'DWCO_JOULE_AI_CONFIGURATION_SCREEN' is false", () => {
      cy.login(TestUser.DWC_ADMINISTRATOR_ONLY, {
        DWCO_JOULE_AI_CONFIGURATION_SCREEN: false,
      });
      cy.visit("#configuration&/cfg/genAiServicesConfiguration");
      cy.get(`#${administrationMainId}--AdministrationLandingPageIconTabBar`).should("be.visible");
      cy.get(`#${administrationMainId}--genAIServicesConfigurationTab`).should("exist");
      cy.get(`#${aiServicesConfigurationMainId}--genAIServicesConfigurationPage`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--aiFeaturesConfigurationSection`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--genAIMeteringDashboard`).should("be.visible");
      cy.get(`#${administrationMainId}--jouleConfigurationSection`).should("not.exist");
    });

    it("ai service tab, the ai features section and the joule section should be displayed if ff 'DWCO_JOULE_AI_CONFIGURATION_SCREEN' is true", () => {
      cy.login(TestUser.DWC_ADMINISTRATOR_ONLY, {
        DWCO_JOULE_AI_CONFIGURATION_SCREEN: true,
      });
      cy.visit("#configuration&/cfg/genAiServicesConfiguration");
      cy.get(`#${administrationMainId}--AdministrationLandingPageIconTabBar`).should("be.visible");
      cy.get(`#${administrationMainId}--genAIServicesConfigurationTab`).should("exist");
      cy.get(`#${aiServicesConfigurationMainId}--genAIServicesConfigurationPage`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--jouleConfigurationSection`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--aiFeaturesConfigurationSection`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--genAIMeteringDashboard`).should("be.visible");
    });
  });

  describe("AI Features - No data set", () => {
    it("ai service tab and the main ai service configuration page should be displayed for an User with privileges if ff 'DWCO_AI_UNITS_SKU' is true", () => {
      cy.intercept("GET", "**/security/aifeatures/status*", { isAICoreAvailable: true, features: null }).as(
        "getAiFeaturesStatus"
      );
      cy.login(TestUser.DWC_ADMINISTRATOR_ONLY, aiFeaturesConfigurationRequiredFeatureFlags);

      cy.visit("#configuration&/cfg/genAiServicesConfiguration");
      cy.get(`#${administrationMainId}--AdministrationLandingPageIconTabBar`).should("be.visible");
      cy.get(`#${administrationMainId}--genAIServicesConfigurationTab`).should("exist");
      cy.wait(["@getAiFeaturesStatus"]);
      cy.get(`#${aiServicesConfigurationMainId}--genAIServicesConfigurationPage`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--genAIMeteringDashboardText`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--genAIFeaturesFooter`).should("not.exist");
      cy.get(`#${aiServicesConfigurationMainId}--genAIFeaturesVbox`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--manageUsersLink`).should("be.visible");
      cy.byId(`${aiServicesConfigurationMainId}--manageUsersLink`).invoke("getEnabled").should("eq", true);
      cy.get(`#${aiServicesConfigurationMainId}--genAISelectAllItemsLink`).should("be.visible");
      cy.byId(`${aiServicesConfigurationMainId}--genAISelectAllItemsLink`).invoke("getEnabled").should("eq", false);
      cy.get(`#${aiServicesConfigurationMainId}--genAIFeaturesTree`).should("be.visible");
      cy.byId(`${aiServicesConfigurationMainId}--genAIFeaturesTree`)
        .invoke("getNoDataText")
        .should("eq", "aiFeaturesNoDataText");
    });

    it("the 'illustratedMessageAIFeaturesGetStarted' should be displayed if 'DWCO_AI_UNITS_SKU=false' but 'isAICoreAvailable=true'", () => {
      cy.intercept("GET", "**/security/aifeatures/status*", { isAICoreAvailable: true, features: null }).as(
        "getAiFeaturesStatus"
      );
      cy.login(TestUser.DWC_ADMINISTRATOR_ONLY, {
        DWCO_AI_UNITS_SKU: false,
        DWCO_JOULE_AI_CONFIGURATION_SCREEN: false,
      });

      cy.visit("#configuration&/cfg/genAiServicesConfiguration");
      cy.get(`#${aiServicesConfigurationMainId}--genAIServicesConfigurationPage`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--genAIFeaturesFooter`).should("not.exist");
      cy.get(`#${aiServicesConfigurationMainId}--genAIFeaturesVbox`).should("not.exist");

      checkViewDataModelProperty("/isAIFeatureEnabled", false);
      checkViewDataModelProperty("/isAICoreAvailable", true);
      cy.byId(`${aiServicesConfigurationMainId}--manageUsersLink`).invoke("getVisible").should("eq", false);
      cy.byId(`${aiServicesConfigurationMainId}--genAISelectAllItemsLink`).invoke("getVisible").should("eq", false);
      cy.get(`#${aiServicesConfigurationMainId}--genAIMeteringDashboardText`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--illustratedMessageAIFeaturesGetStarted`).should("be.visible");
      cy.byId(`${aiServicesConfigurationMainId}--illustratedMessageAIFeaturesGetStarted`)
        .invoke("getTitle")
        .should("eq", "aiFeaturesGetStartedTitle");
    });

    it("the 'illustratedMessageAIFeaturesIsNotSupported' should be displayed if 'DWCO_AI_UNITS_SKU=false' and 'isAICoreAvailable=false'", () => {
      cy.intercept("GET", "**/security/aifeatures/status*", { isAICoreAvailable: false, features: null }).as(
        "getAiFeaturesStatus"
      );
      cy.login(TestUser.DWC_ADMINISTRATOR_ONLY, {
        DWCO_AI_UNITS_SKU: false,
        DWCO_JOULE_AI_CONFIGURATION_SCREEN: false,
      });
      cy.visit("#configuration&/cfg/genAiServicesConfiguration");
      cy.wait(["@getAiFeaturesStatus"]);
      cy.get(`#${aiServicesConfigurationMainId}--genAIServicesConfigurationPage`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--genAIFeaturesFooter`).should("not.exist");
      cy.get(`#${aiServicesConfigurationMainId}--genAIFeaturesVbox`).should("not.exist");
      checkViewDataModelProperty("/isAIFeatureEnabled", false);
      checkViewDataModelProperty("/isAICoreAvailable", false);
      cy.byId(`${aiServicesConfigurationMainId}--manageUsersLink`).invoke("getVisible").should("eq", false);
      cy.byId(`${aiServicesConfigurationMainId}--genAISelectAllItemsLink`).invoke("getVisible").should("eq", false);
      cy.get(`#${aiServicesConfigurationMainId}--genAIMeteringDashboardText`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--illustratedMessageAIFeaturesIsNotSupported`).should("be.visible");
      cy.byId(`${aiServicesConfigurationMainId}--illustratedMessageAIFeaturesIsNotSupported`)
        .invoke("getTitle")
        .should("eq", "aiFeaturesIsNotSupportedTitle");
    });
  });

  describe("AI Features - with data", () => {
    beforeEach(() => {
      cy.intercept("GET", "**/security/aifeatures/status*", {
        isAICoreAvailable: true,
        features: configStoreData,
      }).as("getAiFeaturesStatus");
    });

    it("show all registered AI features in the genAIServicesConfigurationPage", () => {
      cy.login(TestUser.DWC_ADMINISTRATOR_ONLY, aiFeaturesConfigurationRequiredFeatureFlagsWithAIFeatureFlags);

      cy.visit("#configuration&/cfg/genAiServicesConfiguration");
      cy.get(`#${administrationMainId}--AdministrationLandingPageIconTabBar`).should("be.visible");
      cy.get(`#${administrationMainId}--genAIServicesConfigurationTab`).should("exist");

      cy.window().then((win) => {
        cy.stub(win.cypressMocks, "getRegisteredCategoriesWithAIFeatures").returns(registeredAIFeatures);
      });
      cy.wait(["@getAiFeaturesStatus"]);

      cy.get(`#${aiServicesConfigurationMainId}--genAIServicesConfigurationPage`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--genAIFeaturesTree`).should("be.visible");
      cy.byId(`${aiServicesConfigurationMainId}--genAIFeaturesTree`).invoke("getItems").should("have.length", 6);

      cy.byId(`${aiServicesConfigurationMainId}--genAIFeaturesTree`)
        .invoke("getItems")
        .each((item, index) => {
          // That's the title item with the category name
          let text = item.getContent()[0].getItems()[0].getText();
          if (item.getContent()[0].getItems()[1].getVisible()) {
            // That's the checkbox item with the feature description and the checkbox state
            text = item.getContent()[0].getItems()[1].getText();
            const active: boolean = item.getContent()[0].getItems()[1].getSelected();
            expect(active).to.equal(expectedActivatedFeatures[index]);
          }
          expect(text).to.equal(expectedItemsText[index]);
        });

      cy.byId(`${aiServicesConfigurationMainId}--genAISelectAllItemsLink`).invoke("getEnabled").should("eq", true);
      checkModelDataChanges(false);
    });

    it("Use User with only SYSTEMINFO.Read", () => {
      cy.login(
        TestUser.DWC_ADMINISTRATOR_WITH_SYSTEMINFO_READ,
        aiFeaturesConfigurationRequiredFeatureFlagsWithAIFeatureFlags
      );

      cy.visit("#configuration&/cfg/genAiServicesConfiguration");
      cy.get(`#${administrationMainId}--AdministrationLandingPageIconTabBar`).should("be.visible");
      cy.get(`#${administrationMainId}--genAIServicesConfigurationTab`).should("exist");

      cy.window().then((win) => {
        cy.stub(win.cypressMocks, "getRegisteredCategoriesWithAIFeatures").returns(registeredAIFeatures);
      });
      cy.wait(["@getAiFeaturesStatus"]);

      cy.get(`#${aiServicesConfigurationMainId}--genAIServicesConfigurationPage`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--genAIFeaturesTree`).should("be.visible");
      cy.byId(`${aiServicesConfigurationMainId}--genAIFeaturesTree`).invoke("getItems").should("have.length", 6);

      // Check that all controls are visible but disabled
      cy.get(`#${aiServicesConfigurationMainId}--genAISelectAllItemsLink`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--saveAIFeaturesButton`).should("be.visible");
      cy.get(`#${aiServicesConfigurationMainId}--cancelAIFeaturesButton`).should("be.visible");
      cy.byId(`${aiServicesConfigurationMainId}--genAISelectAllItemsLink`).invoke("getEnabled").should("eq", false);
      cy.byId(`${aiServicesConfigurationMainId}--saveAIFeaturesButton`).invoke("getEnabled").should("eq", false);
      cy.byId(`${aiServicesConfigurationMainId}--cancelAIFeaturesButton`).invoke("getEnabled").should("eq", false);
      cy.byId(`${aiServicesConfigurationMainId}--genAIFeaturesTree`)
        .invoke("getItems")
        .its(1) // The first sap.m.CustomTreeItem element with a checkbox
        .invoke("getContent")
        .its(0)
        .invoke("getItems")
        .its(1) // The sap.m.CheckBox element
        .invoke("getEnabled")
        .should("eq", false);
    });

    it("check the changes and actions in the genAIServicesConfigurationPage", () => {
      cy.intercept("POST", "**/security/aifeatures/activation/status*", (req) => {
        req.reply({ statusCode: 200, response: {} });
      }).as("checkRequestConfigStoreData");

      cy.login(TestUser.DWC_ADMINISTRATOR_ONLY, aiFeaturesConfigurationRequiredFeatureFlagsWithAIFeatureFlags);

      cy.visit("#configuration&/cfg/genAiServicesConfiguration");
      cy.get(`#${administrationMainId}--AdministrationLandingPageIconTabBar`).should("be.visible");
      cy.get(`#${administrationMainId}--genAIServicesConfigurationTab`).should("exist");
      cy.window().then((win) => {
        cy.stub(win.cypressMocks, "getRegisteredCategoriesWithAIFeatures").returns(registeredAIFeatures);
      });

      cy.wait(["@getAiFeaturesStatus"]);
      // At the beginning, there will be no changes in the model data
      checkModelDataChanges(false);
      cy.get(`#${aiServicesConfigurationMainId}--genAISelectAllItemsLink`).click();
      // After selecting all features, there will be changes in the model data
      checkModelDataChanges(true);
      // Check in the tree items that all features are selected
      cy.byId(`${aiServicesConfigurationMainId}--genAIFeaturesTree`)
        .invoke("getItems")
        .each((item) => {
          if (item.getContent()[0].getItems()[1].getVisible()) {
            // Check that all features are selected
            const active: boolean = item.getContent()[0].getItems()[1].getSelected();
            expect(active).to.equal(true);
          }
        });

      // Check that the select all link is disabled
      cy.byId(`${aiServicesConfigurationMainId}--genAISelectAllItemsLink`).invoke("getEnabled").should("eq", false);

      cy.get(`#${aiServicesConfigurationMainId}--cancelAIFeaturesButton`).click();
      // After clicking the cancel button, there will be no changes in the model data
      checkModelDataChanges(false);

      // Check that the select all link is again enabled
      cy.byId(`${aiServicesConfigurationMainId}--genAISelectAllItemsLink`).invoke("getEnabled").should("eq", true);

      cy.get(`#${aiServicesConfigurationMainId}--genAISelectAllItemsLink`).click();
      checkModelDataChanges(true);
      cy.get(`#${aiServicesConfigurationMainId}--saveAIFeaturesButton`).click();

      cy.wait("@checkRequestConfigStoreData").then((intercept) => {
        // Check the request body of the config store data
        expect(intercept.request.body).to.deep.equal(changedConfigStoreData);
      });

      // check the model data after the save action, that there will be no more changes and we will have a changed model data object (newExpectedAIFeaturesModelData)
      checkModelDataChanges(false, newExpectedAIFeaturesModelData);
    });

    it("check the isDirty functionality in the genAIServicesConfigurationPage", () => {
      cy.intercept("POST", "**/security/aifeatures/activation/status*", (req) => {
        req.reply({ statusCode: 200, response: {} });
      }).as("checkRequestConfigStoreData");

      cy.login(TestUser.DWC_ADMINISTRATOR_ONLY, aiFeaturesConfigurationRequiredFeatureFlagsWithAIFeatureFlags);

      cy.visit("#configuration&/cfg/genAiServicesConfiguration");
      cy.get(`#${administrationMainId}--AdministrationLandingPageIconTabBar`).should("be.visible");

      cy.get(`#${administrationMainId}--genAIServicesConfigurationTab`).should("exist");
      cy.window().then((win) => {
        cy.stub(win.cypressMocks, "getRegisteredCategoriesWithAIFeatures").returns(registeredAIFeatures);
      });

      cy.wait(["@getAiFeaturesStatus"]);
      // At the beginning, there will be no changes in the model data
      checkModelDataChanges(false);

      // check the isDirty functionality, that after trying to leave the page with changes in the model data, a daily dialog should be displayed
      cy.byId(`${aiServicesConfigurationMainId}--genAIFeaturesTree`)
        .invoke("getItems")
        .its("1")
        .invoke("getContent")
        .its("0")
        .invoke("getItems")
        .its("1")
        .invoke("getId")
        .then((itemId) => {
          cy.get(`#${itemId}`, { force: true }).click();
        });

      checkModelDataChanges(true);

      cy.get(`#${aiServicesConfigurationMainId}--manageUsersLink`).click();
      // daily dialog should be displayed after clicking on the manage users link
      cy.get(".sapMBtnContent").contains("Save", { cyOriginal: true }).should("be.visible");
      cy.get(".sapMBtnContent").contains("Discard", { cyOriginal: true }).should("be.visible");
      // Cancel the isDirtyDialog
      cy.get(".sapMBtnContent").contains("Cancel", { cyOriginal: true }).should("be.visible").click();

      checkModelDataChanges(true);

      // click on another tab
      cy.get(`#${administrationMainId}--onPremiseTab`).click();
      // Discard the changes in the isDirtyDialog
      cy.get(".sapMBtnContent").contains("Discard", { cyOriginal: true }).should("be.visible").click();

      cy.get(
        `#shellMainContent---administrationComponent---dpagentcloudconfigComponent---dpagentonpremiseconfig`
      ).should("be.visible");

      // Move back to the AI Services page
      cy.visit("#configuration&/cfg/genAiServicesConfiguration");
      // check that there are no changes in the model data
      checkModelDataChanges(false);

      cy.get(`#${aiServicesConfigurationMainId}--genAISelectAllItemsLink`).click();

      cy.go("back");
      // daily dialog should be displayed after clicking on the go back button
      // Cancel the isDirtyDialog
      cy.get(".sapMBtnContent").contains("Cancel", { cyOriginal: true }).should("be.visible").click();

      checkModelDataChanges(true);
      cy.get(`#${administrationMainId}--onPremiseTab`).click();
      // Save the changes in the isDirtyDialog
      cy.get(".sapMBtnContent").contains("Save", { cyOriginal: true }).should("be.visible").click();

      cy.wait("@checkRequestConfigStoreData").then((intercept) => {
        // Check the request body of the config store data
        expect(intercept.request.body).to.deep.equal(changedConfigStoreData);
      });

      // Move back to the AI Services tab
      cy.visit("#configuration&/cfg/genAiServicesConfiguration");
      // check the model data after the save action of the isDirtyDialog, that there will be no more changes and we will have a changed model data object (newExpectedAIFeaturesModelData)
      checkModelDataChanges(false, newExpectedAIFeaturesModelData);
    });
  });

  describe("AI Features - Error Cases", () => {
    it("failed loading aiFeaturesStatus", () => {
      cy.login(TestUser.DWC_ADMINISTRATOR_ONLY, aiFeaturesConfigurationRequiredFeatureFlags);
      cy.intercept("GET", "**/security/aifeatures/status*", { statusCode: 500 }).as("getAiFeaturesStatusFailed");

      cy.visit("#configuration&/cfg/genAiServicesConfiguration");
      cy.wait(["@getAiFeaturesStatusFailed"]);
      cy.get(`#${aiServicesConfigurationMainId}--illustratedMessageAIFeaturesDataLoadingFailed`).should("be.visible");
      cy.byId(`${aiServicesConfigurationMainId}--illustratedMessageAIFeaturesDataLoadingFailed`)
        .invoke("getTitle")
        .should("eq", "illustratedImageErrorTitle");
      cy.byId(`${aiServicesConfigurationMainId}--illustratedMessageAIFeaturesDataLoadingFailed`)
        .invoke("getDescription")
        .should("eq", "illustratedImageErrorDescription");

      checkViewDataModelProperty("/aiFeaturesDataLoadingFailureType", "standardFailure");
    });

    it("failed saving ConfigStoreData", () => {
      cy.login(TestUser.DWC_ADMINISTRATOR_ONLY, aiFeaturesConfigurationRequiredFeatureFlagsWithAIFeatureFlags);
      cy.intercept("GET", "**/security/aifeatures/status*", {
        isAICoreAvailable: true,
        features: configStoreData,
      }).as("getAiFeaturesStatus");
      cy.intercept("POST", "**/security/aifeatures/activation/status*", { statusCode: 500 }).as(
        "postConfigStoreDataFailed"
      );

      cy.visit("#configuration&/cfg/genAiServicesConfiguration");
      cy.get(`#${administrationMainId}--AdministrationLandingPageIconTabBar`).should("be.visible");
      cy.get(`#${administrationMainId}--genAIServicesConfigurationTab`).should("exist");
      cy.window().then((win) => {
        cy.stub(win.cypressMocks, "getRegisteredCategoriesWithAIFeatures").returns(registeredAIFeatures);
      });
      cy.wait(["@getAiFeaturesStatus"]);

      cy.get(`#${aiServicesConfigurationMainId}--genAIFeaturesVbox`).should("be.visible");
      // Click on a feature to change the model data
      cy.byId(`${aiServicesConfigurationMainId}--genAIFeaturesTree`)
        .invoke("getItems")
        .its("1")
        .invoke("getContent")
        .its("0")
        .invoke("getItems")
        .its("1")
        .invoke("getId")
        .then((itemId) => {
          cy.get(`#${itemId}`, { force: true }).click();
        });
      checkModelDataChanges(true);
      // Try to save the changes
      cy.get(`#${aiServicesConfigurationMainId}--saveAIFeaturesButton`).click();
      cy.wait(["@postConfigStoreDataFailed"]);

      // Error dialog should be displayed
      cy.get("#aiFeaturesDatabaseErrorDialog").should("be.visible");
      cy.byId("aiFeaturesDatabaseErrorDialog")
        .invoke("getContent")
        .its("0")
        .invoke("getItems")
        .its("0")
        .invoke("getText")
        .should("eq", "aiFeaturesDatabaseErrorMessage");

      cy.byId("aiFeaturesDatabaseErrorDialog")
        .invoke("getButtons")
        .its("0")
        .invoke("getId")
        .then((id) => {
          cy.get(`#${id}`, { force: true }).click();
        });

      // After closing the error dialog, the model data should still be changed
      checkModelDataChanges(true);
    });
  });

  function checkModelDataChanges(
    modelDataChanges: boolean,
    categoriesWithAvailableAIFeatures = expectedAIFeaturesModelData
  ) {
    const action = modelDataChanges ? "not.deep.equal" : "deep.equal";
    checkViewDataModelProperty("/categoriesWithAvailableAIFeatures", categoriesWithAvailableAIFeatures, action);
    checkViewDataModelProperty("/modelDataChanges", modelDataChanges);
    cy.byId(`${aiServicesConfigurationMainId}--saveAIFeaturesButton`)
      .invoke("getEnabled")
      .should("eq", modelDataChanges);
    cy.byId(`${aiServicesConfigurationMainId}--cancelAIFeaturesButton`)
      .invoke("getEnabled")
      .should("eq", modelDataChanges);
  }

  function checkViewDataModelProperty(propertyName, propertyValue, action = "eq") {
    cy.window()
      .its("sap")
      .its("ui")
      .invoke("getCore")
      .invoke("byId", "shellMainContent---administrationComponent---genAiServicesConfiguration")
      .invoke("getModel", "viewData")
      .invoke("getProperty", propertyName)
      .should(action, propertyValue);
  }

  // initial loaded registeredAIFeatures
  const registeredAIFeatures: ICategoryWithAIFeatures[] = [
    {
      // Category with only one feature with an inactive feature flag should not be visible
      i18nCategoryNameKey: "categoryWithInactiveFeatureFlag",
      priority: 2,
      features: [
        {
          featureId: "feature1",
          i18nDescriptionKey: "feature1_description",
          featureFlagName: "DWC_FEATURE1",
        },
      ],
    },
    {
      // Category without feature should not be visible
      i18nCategoryNameKey: "categoryWithoutFeature",
      priority: 2,
      features: [],
    },
    {
      // Category with one feature with an inactive feature flag but with another feature without a feature flag should be visible
      i18nCategoryNameKey: "categoryWithInactiveFeatureFlagAndFeatureWithoutFeatureFlag",
      priority: 2,
      features: [
        {
          featureId: "feature2",
          i18nDescriptionKey: "feature2_description",
          featureFlagName: "DWC_FEATURE2",
        },
        {
          featureId: "feature3",
          i18nDescriptionKey: "feature3_description",
        },
      ],
    },
    {
      // Category with a feature with an active feature flag and with another feature without feature flag should be visible and show both features
      i18nCategoryNameKey: "categoryWithActiveFeatureFlagAndFeatureWithoutFeatureFlag",
      priority: 1,
      features: [
        {
          featureId: "feature4",
          i18nDescriptionKey: "feature4_description",
          featureFlagName: "DWC_FEATURE4",
        },
        {
          featureId: "feature5",
          i18nDescriptionKey: "feature5_description",
        },
        {
          featureId: "newFeature1",
          i18nDescriptionKey: "newFeature1_description",
        },
      ],
    },
  ];

  // initial loaded configStoreData
  const configStoreData: IAIFeaturesConfigStoreData = {
    feature1: {
      active: false,
    },
    feature2: {
      active: false,
    },
    feature3: {
      active: true,
    },
    feature4: {
      active: false,
    },
    feature5: {
      active: false,
    },
    newFeature1: {
      active: false,
    },
  };

  // changed config store data which should be saved
  const changedConfigStoreData: IAIFeaturesConfigStoreData = {
    feature4: {
      active: true,
    },
    feature5: {
      active: true,
    },
    newFeature1: {
      active: true,
    },
  };

  // expected items text in the tree of the expectedAIFeaturesModelData
  const expectedItemsText: string[] = [
    "categoryWithActiveFeatureFlagAndFeatureWithoutFeatureFlag",
    "feature4_description",
    "feature5_description",
    "newFeature1_description",
    "categoryWithInactiveFeatureFlagAndFeatureWithoutFeatureFlag",
    "feature3_description",
  ];

  // expected selected state of the features in the tree of the expectedAIFeaturesModelData
  const expectedActivatedFeatures: boolean[] = [null, false, false, false, null, true];

  // expected model data after the first load in the UI
  const expectedAIFeaturesModelData: ICategoryWithAvailableAIFeatures[] = [
    {
      i18nCategoryNameKey: "categoryWithActiveFeatureFlagAndFeatureWithoutFeatureFlag",
      priority: 1,
      features: [
        {
          featureId: "feature4",
          i18nDescriptionKey: "feature4_description",
          featureFlagName: "DWC_FEATURE4",
          active: false,
        },
        {
          featureId: "feature5",
          i18nDescriptionKey: "feature5_description",
          active: false,
        },
        {
          featureId: "newFeature1",
          i18nDescriptionKey: "newFeature1_description",
          active: false,
        },
      ],
    },
    {
      i18nCategoryNameKey: "categoryWithInactiveFeatureFlagAndFeatureWithoutFeatureFlag",
      priority: 2,
      features: [
        {
          featureId: "feature3",
          i18nDescriptionKey: "feature3_description",
          active: true,
        },
      ],
    },
  ];

  // new expected model data after changes and the save action
  const newExpectedAIFeaturesModelData: ICategoryWithAvailableAIFeatures[] = [
    {
      i18nCategoryNameKey: "categoryWithActiveFeatureFlagAndFeatureWithoutFeatureFlag",
      priority: 1,
      features: [
        {
          featureId: "feature4",
          i18nDescriptionKey: "feature4_description",
          featureFlagName: "DWC_FEATURE4",
          active: true,
        },
        {
          featureId: "feature5",
          i18nDescriptionKey: "feature5_description",
          active: true,
        },
        {
          featureId: "newFeature1",
          i18nDescriptionKey: "newFeature1_description",
          active: true,
        },
      ],
    },
    {
      i18nCategoryNameKey: "categoryWithInactiveFeatureFlagAndFeatureWithoutFeatureFlag",
      priority: 2,
      features: [
        {
          featureId: "feature3",
          i18nDescriptionKey: "feature3_description",
          active: true,
        },
      ],
    },
  ];
});
