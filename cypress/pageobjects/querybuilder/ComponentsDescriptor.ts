/** @format */

import { AB_VIEW } from "../abstractbuilder/descriptor";
import { ShellPages } from "../shell";

export const DEFAULT_SPACE = "TESTS_SPACE_1";
export const DEFAULT_URL = `#/${ShellPages.DataBuilder}&/db/${DEFAULT_SPACE}`;
export const QUERY_BUILDER_COMPONENT = "--analyticModelEditor";
export const TEST_MODEL = "Cypress_Opportunity_Items";
export const TEST_URL = `#/${ShellPages.DataBuilder}&/db/${DEFAULT_SPACE}/${TEST_MODEL}`;
export const QUERY_BUILDER_COMPONENT_VIEW = "cubebuilderComponent";
export const QUERY_BUILDER_NODE_PROPERTIES = "cubebuilder-properties-NodeProperties";
export const QUERY_BUILDER_COMPONENT_MODEL_URL = `#/databuilder&/db/${DEFAULT_SPACE}/{modelName}/cubeQueryModel`;
export const QUERY_BUILDER_COMPONENT_SPACE_MODEL_URL = `#/databuilder&/db/{spaceName}/{modelName}/cubeQueryModel`;
export const DATA_BUILDER_COMPONENT = "shellMainContent---databuilderComponent";
export const DATA_BUILDER_WORKBENCH = `${DATA_BUILDER_COMPONENT}---databuilderWorkbench`;
export const PROPERTY_PANEL = `${DATA_BUILDER_WORKBENCH}--graphSplitView--PropertyPanel`;
export const DATABUILDER_LANDING = `${DATA_BUILDER_COMPONENT}---databuilderLandingPage--entitySelectionLandingPage`;
export const BREADCRUMB_MODEL_NAME_ID = "cdwrootapp--shellBreadcrumbs-currentText";
export const QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES = `${DATA_BUILDER_WORKBENCH}--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties`;
export const QUERY_BUILDER_GRAPHVIEW_NODEPROPERTIES = `${DATA_BUILDER_WORKBENCH}--graphSplitView--PropertyPanel--cubebuilder-properties-NodeProperties`;

export const CUBE_BUILDER_MENU = {
  expanded: false,
  hasExpander: false,
  icon: "sap-icon://database",
  isFolder: false,
  items: [],
  key: "cubebuilder",
  parameters: ["spaceId", "model"],
  title: "menu_cube",
};

export const QB_VIEWS = {
  EDITOR: `${QUERY_BUILDER_COMPONENT}---editor`,
  PROPERTIES: `${DATA_BUILDER_WORKBENCH}--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties`,
  NODE_PROPERTIES: `${DATA_BUILDER_WORKBENCH}--graphSplitView--PropertyPanel--cubebuilder-properties-NodeProperties`,
  MEASURE_DETAILS: `${DATA_BUILDER_WORKBENCH}--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties--measureDetails`,
  GLOBAL_FILTER_DETAILS: `${DATA_BUILDER_WORKBENCH}--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties--globalFilterDetails`,
  VALIDATIONS: AB_VIEW.VALIDATIONS,
  ATTRIBUTE_DETAILS: `${DATA_BUILDER_WORKBENCH}--graphSplitView--PropertyPanel--cubebuilder-properties-AttributeProperties--attributeDetails`,
  CROSS_CALCULATION_DETAILS: `${DATA_BUILDER_WORKBENCH}--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties--crossCalculationDetails`,
  MEASURE_DEPENDENCIES_ILANALYZER: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--measureDependencies--measureDependenciesDiagram--ilanalyzer`,
  DATA_ACCESS_CONTROL_DETAILS: `${DATA_BUILDER_WORKBENCH}--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties--dataAccessControlDetails`,
};

export const QB_LANDING_PAGE = {
  CREATE_QUERY_TILE: `${DATABUILDER_LANDING}--createCubeTile`,
  ADD_ENTITY_BUTTON: `${DATABUILDER_LANDING}--addEntity`,
  ADD_QUERY_MODEL: `${DATABUILDER_LANDING}--addEntity--menuButtoncreateCubeTile-unifiedmenu`,
  OBJECTS_TABLE: `${DATABUILDER_LANDING}--spaceEntityTable-table`,
  OBJECTS_TABLE_PARENT: `${DATABUILDER_LANDING}--spaceEntityTable`,
  OBJECTS_LIST_TOOLBAR: `${DATABUILDER_LANDING}--unifiedLandingBar`,
};

export const QB_DIALOGS = {
  SAVE_MODEL_DIALOG: "saveModelDialog",
  REMOVE_CONFIRM_DIALOG: "removeConfirmDialog",
  TARGET_CURRENCY_DIALOG: "targetCurrency--dialog",
  REFERENCE_DATE_DIALOG: "referenceDate--dialog",
  CONVERSION_TYPE_DIALOG: "conversionType--dialog",
  TARGET_UNIT_DIALOG: "targetUnit--dialog",
  COLLISION_HANDLING_DIALOG: "CollisionHandlingDialog",
};

export const QB_PANELS = {
  SOURCE_TREE_PANEL: `${DATA_BUILDER_WORKBENCH}--TreeView`,
  SOURCE_TREE_REPO: `${DATA_BUILDER_WORKBENCH}--TreeView--modelTree`,
  SOURCE_TREE_SEARCH_BAR: `${DATA_BUILDER_WORKBENCH}--TreeView--searchRepository`,
  DETAILS_PANEL: `${DATA_BUILDER_WORKBENCH}--detailsContainer`,
  DATA_PREVIEW_TABLE: `${DATA_BUILDER_WORKBENCH}--previewTable`,
  CROSS_CALCULATION_DETAILS_EXCEPTION_AGGREGATION: `${QB_VIEWS.CROSS_CALCULATION_DETAILS}--exceptionAggregationPanel`,
};

export const QB_TOOLBAR_BUTTONS = {
  TOOLBAR_GROUP_SAVE: `${DATA_BUILDER_WORKBENCH}--saveAndSaveAs`,
  TOOLBAR_SAVE: `${DATA_BUILDER_WORKBENCH}--save`,
  TOOLBAR_SAVE_ONLY: `${DATA_BUILDER_WORKBENCH}--saveonly-unifiedmenu`,
  TOOLBAR_SAVE_AS: `${DATA_BUILDER_WORKBENCH}--saveAs-unifiedmenu`,
  TOOLBAR_UNDO: `${DATA_BUILDER_WORKBENCH}--undo`,
  TOOLBAR_REDO: `${DATA_BUILDER_WORKBENCH}--redo`,
  DEPLOYMENT_STATUS_ICON: "objectStatusIcon",
  TOGGLE_SOURCE_TREE: `${DATA_BUILDER_WORKBENCH}--toggleTree`,
  TOGGLE_DETAILS_PANEL: `${DATA_BUILDER_WORKBENCH}--detailsView`,
  TOOLBAR_SAVE_ANYWAY: "sap-cdw-components-cubebuilder-view-Validations--dialog--view--ok",
};

export const QB_SOURCES_PAGE = {
  SEARCH_RESULTS_TOOLBAR: `${QUERY_BUILDER_COMPONENT_VIEW}--searchResultsToolbar`,
  SEARCH_RESULTS_COUNT: `${QUERY_BUILDER_COMPONENT_VIEW}--resultCount`,
  CUBE_SOURCES_GRID: `${QUERY_BUILDER_COMPONENT_VIEW}--cubeSourcesGrid`,
  CUBE_SOURCES_TABLE: `${QUERY_BUILDER_COMPONENT_VIEW}--sourceTable--cubeSourceTable`,
  STATUS_FILTER: `${QUERY_BUILDER_COMPONENT_VIEW}--statusMenuButton`,
  TYPE_FILTER: `${QUERY_BUILDER_COMPONENT_VIEW}--businessTypeSelection`,
  DATA_PREVIEW_DIALOG: "sourceDataPreviewDialog",
  SOURCE_DATA_PREVIEW_BUTTON_PATTERN: "--previewCubeSourceButton",
  ADD_SOURCE: `${QUERY_BUILDER_COMPONENT_VIEW}--addCubeSourceButton`,
  ASSOCIATED_DIMENSION_DIALOG: "AddAssociatedDimensionDialog--addAssociationDialog",
  ASSOCIATED_DIMENSION_DIALOG_SELECTALL: "AddAssociatedDimensionDialog--addAssociationTable-selall",
  ASSOCIATED_DIMENSION_DIALOG_ADD_ALLATTRIBUTES: "AddAssociatedDimensionDialog--addAllAttributes",
  ASSOCIATED_DIMENSION_DIALOG_ADD_ALLMEASURES: "AddAssociatedDimensionDialog--addAllMeasures",
  ASSOCIATED_DIMENSION_DIALOG_IMPORT: "AddAssociatedDimensionDialog--associationDialogImportButton",
  MEASURE_SOURCES_LIST: `${QUERY_BUILDER_COMPONENT_VIEW}--listMeasureSources`,
  DIMENSION_SOURCES_LIST: `${QUERY_BUILDER_COMPONENT_VIEW}--listDimensionSources`,
  SOURCES_CONTEXT_MENU: "factSourceContextMenu",
  MEASURES_LIST: "cubebuilderComponent--availableMeasuresList",
  SOURCE_DATA_TAB_TITLE: "cubebuilderComponent--sourceDataTab",
  MEASURE_SOURCE_REMOVE_BUTTON:
    "cubebuilderComponent--measureSourceRemoveButton-cubebuilderComponent--listMeasureSources",
  ALL_OUTPUT_MEASURE_LIST: "cubebuilder-properties-ModelProperties--outputMeasuresList",
  ALL_OUTPUT_ATTRIBUTE_LIST: "cubebuilderComponent--outputAttributesList",
  ALL_AVAILABLE_ATTRIBUTE_LIST: "cubebuilderComponent--availableAttributesList",
  ALL_OUTPUT_PARAMETER_LIST: "cubebuilderComponent--outputParametersList",
  OUTPUT_MEASURE_CONTEXT: "cubebuilderComponent--MeasuresContextMenu",
  PARAMETER_TABLE: "AddAssociatedDimensionDialog--importParameterList",
  PARAMETER_DEFAULTVAL_DIALOG: "analyticalModelDefaultValDialog",
  VARIABLE_INFO_POPOVER: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--VariableInfoPopover--variableInfoPopover-cont`,
  VARIABLE_INFO_POPOVER_VALUE_DEFINITION: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--VariableInfoPopover--variableValueDefinition`,
  VARIABLE_INFO_POPOVER_DEFAULT_VALUE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--VariableInfoPopover--variableDefaultValueLabel-text`,
  OUTPUT_VARIABLE_LIST: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--outputParametersList`,
  VALIDATION_POPOVER_MSGLIST_PAGE: "analyticModelValidationPopoverlistPage",
};

export const QB_QUERY_MODEL_TAB = {
  MODEL_PROPERTIES_PANEL: `${PROPERTY_PANEL}--cubebuilder-properties-ModelProperties`,
  NODE_PROPERTIES_PANEL: `${PROPERTY_PANEL}--cubebuilder-properties-NodeProperties`,
  QUERY_MODEL_TAB_TITLE: `${QUERY_BUILDER_COMPONENT_VIEW}--queryModelTab`,
  SOURCEDATA_MODEL_TAB_TITLE: `${QUERY_BUILDER_COMPONENT_VIEW}--sourceDataTab`,
  QUERY_MODEL_EMPTY_MESSAGE_PAGE: `${QUERY_BUILDER_COMPONENT_VIEW}--welcomeTextPanel`,
  AVAILABLE_ASSOCIATIONS_LIST: `${QUERY_BUILDER_NODE_PROPERTIES}--availableAssociationsList-listUl`,
  AVAILABLE_INPUT_PARAMETER_LIST: `${QUERY_BUILDER_NODE_PROPERTIES}--availableParameterList-listUl`,
  AVAILABLE_STACKED_INPUT_PARAMETER_LIST: `${QUERY_BUILDER_NODE_PROPERTIES}--availableStackedParameterList-listUl`,
  AVAILABLE_ATTRIBUTES_LIST: `${QUERY_BUILDER_NODE_PROPERTIES}--availableAttributesList`,
  AVAILABLE_MEASURES_LIST: `${QUERY_BUILDER_COMPONENT_VIEW}--availableMeasuresList`,
  AVAILABLE_PROPERTIES_SEARCH_INPUT: `${QUERY_BUILDER_COMPONENT_VIEW}--availablePropertiesSearchInput`,
  OUTPUT_SOURCE_ATTRIBUTE_LIST: `${QUERY_BUILDER_COMPONENT_VIEW}--outputAttributesList-listUl`,
  OUTPUT_SOURCE_MEASURES_LIST: `${QUERY_BUILDER_COMPONENT_VIEW}--outputMeasuresList-listUl`,
  MODEL_TREE_LIST_UL: `${DATA_BUILDER_COMPONENT}---databuilderWorkbench--TreeView--modelTree`,
  MODEL_TREE_VIEW: `${DATA_BUILDER_COMPONENT}---databuilderWorkbench--TreeView--modelTree-2-content`,
  AVAILABLE_ATTRIBUTES_LIST_SYMBOL_SELECT: `${QUERY_BUILDER_NODE_PROPERTIES}--availableAttributesList`,
  COMPOUND_KEY_LIST: `${QUERY_BUILDER_GRAPHVIEW_NODEPROPERTIES}--keyAttributes`,
  DEPENDENT_OBJECTS_PANEL: `${PROPERTY_PANEL}--cubebuilder-properties-ModelProperties--dependentObjectPanel`,
  NODE_DEPENDENT_OBJECTS_PANEL: `${PROPERTY_PANEL}--cubebuilder-properties-NodeProperties--dependentObjectPanel`,
};

export const QB_OUTPUT_OVERVIEW_PAGE = {
  OUTPUT_CONTENT: `${QB_VIEWS.PROPERTIES}--outputOverview-cont`,
  OUTPUT_MEASURE_LIST: `${QB_VIEWS.PROPERTIES}--outputMeasuresList`,
  OUTPUT_ATTRIBUTES_LIST: `${QB_VIEWS.PROPERTIES}--outputAttributesList`,
  OUTPUT_PARAMETERS_LIST: `${QB_VIEWS.PROPERTIES}--outputParametersList`,
  OUTPUT_DAC_LIST: `${QB_VIEWS.PROPERTIES}--outputDataAccessControlsList`,
  OUTPUT_GLOBAL_FILTER_LIST: `${QB_VIEWS.PROPERTIES}--outputGlobalFilterList`,
  OUTPUT_CROSS_CALCULATION_LIST: `${QB_VIEWS.PROPERTIES}--outputCrossCalculationsList`,
  MODEL_TREE_LIST_UL: `${DATA_BUILDER_COMPONENT}---databuilderWorkbench--TreeView--modelTree-listUl`,
  MODEL_TREE_VIEW: `shellMainContent---databuilderComponent---databuilderWorkbench--TreeView--modelTree-2`,
};

export const QB_INPUTS = {
  SAVE_MODEL_DIALOG_BUSINESS_NAME: `${QB_DIALOGS.SAVE_MODEL_DIALOG}--nameInput-inner`,
  SAVE_MODEL_DIALOG_TECHNICAL_NAME: `${QB_DIALOGS.SAVE_MODEL_DIALOG}--technicalnameInput-inner`,
  RENAME_SOURCE_ALIAS: `${QUERY_BUILDER_COMPONENT_VIEW}--renameSourceAliasInput-inner`,
  MEASURE_DETAILS_BUSINESS_NAME: `${QB_VIEWS.MEASURE_DETAILS}--measureDetailsBusinessName-inner`,
  MEASURE_DETAILS_TECHNICAL_NAME: `${QB_VIEWS.MEASURE_DETAILS}--measureDetailsTechnicalName-inner`,
  FILTER_DETAILS_NAME: `${QB_VIEWS.GLOBAL_FILTER_DETAILS}--globalFilterName-inner`,
  PARAMETER_DEFAULT_STRINPUT: "stringInput",
  INPUT_PARAMETER_POPOVER_BNAME: `${QUERY_BUILDER_GRAPHVIEW_NODEPROPERTIES}--infoPopover--businessName`,
  ATTRIBUTE_DETAILS_BUSINESS_NAME: `${QB_VIEWS.ATTRIBUTE_DETAILS}--attributeDetailBusinessName`,
  ATTRIBUTE_DETAILS_TECHNICAL_NAME: `${QB_VIEWS.ATTRIBUTE_DETAILS}--attributeDetailTechnicalName`,
  /** Currency Conversion */
  MEASURE_DETAILS_CC_TARGET_CURRENCY: `${QB_VIEWS.MEASURE_DETAILS}--analyticModelTargetCurrency-inner`,
  MEASURE_DETAILS_CC_REFERENCE_DATE: `${QB_VIEWS.MEASURE_DETAILS}--analyticModelReferenceDate-inner`,
  MEASURE_DETAILS_CC_CONVERSION_TYPE: `${QB_VIEWS.MEASURE_DETAILS}--conversionType-inner`,
  MEASURE_DETAILS_UC_TARGET_UNIT: `${QB_VIEWS.MEASURE_DETAILS}--analyticModelTargetUnit-inner`,
  MEASURE_DETAILS_NON_CUMULATIVE_MIN_START_TIME: `${QB_VIEWS.MEASURE_DETAILS}--reportingMinStartTime-inner`,
  MEASURE_DETAILS_NON_CUMULATIVE_MAX_END_TIME: `${QB_VIEWS.MEASURE_DETAILS}--reportingMaxEndTime-inner`,
  VARIABLE_PRECISION_INPUT: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--variablePrecision-inner`,
  VARIABLE_SCALE_INPUT: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--variableScale-inner`,
  VARIABLE_LENGTH_INPUT: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--variableLength-inner`,
  CROSS_CALCULATION_DETAILS_BUSINESS_NAME: `${QB_VIEWS.CROSS_CALCULATION_DETAILS}--crossCalculationDetailsBusinessName-inner`,
  CROSS_CALCULATION_DETAILS_TECHNICAL_NAME: `${QB_VIEWS.CROSS_CALCULATION_DETAILS}--crossCalculationDetailsTechnicalName-inner`,
  CROSS_CALCULATION_DETAILS_TECHNICAL_NAME_VALUE_STATE_MESSAGE: `${QB_VIEWS.CROSS_CALCULATION_DETAILS}--crossCalculationDetailsTechnicalName-message`,
  CROSS_CALCULATION_DETAILS_SOURCE_FIELD: `${QB_VIEWS.CROSS_CALCULATION_DETAILS}--crossCalculationDetailSourceField-inner`,
};

export const QB_SELECTS = {
  MEASURE_DETAILS_SOURCE_MEASURE: `${QB_VIEWS.MEASURE_DETAILS}--measureDetailsSourceMeasure`,
  MEASURE_DETAILS_AGGREGATION_TYPE: `${QB_VIEWS.MEASURE_DETAILS}--measureDetailsAggregationType`,
  VARIABLE_DEFAULT_VALUE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--panelVariableDefaultValue--customInput`,
  VARIABLE_DEFAULT_VALUE_SELECT: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--panelVariableDefaultValue--customSelect`,
  PANEL_VARIABLE_DEFAULT_VALUE_INPUT: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--panelVariableDefaultValue`,

  VARIABLE_DEFAULT_VALUE_HELP: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--panelVariableDefaultValue--customInput-vhi`,
  VARIABLE_BUSINESS_NAME: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--variableDetailBusinessName`,
  VARIABLE_TECHNICAL_NAME: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--variableDetailTechnicalName`,
  VARIABLE_DATA_TYPE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--variableDatatype`,
  FILTER_VAR_SELECTION_TYPE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--panelVariableFilterType-arrow`,
  FILTER_VAR_SELECTION_TYPE_VAL: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--panelVariableFilterType-hiddenInput`,
  FILTER_VAR_SELECTION_TYPE_STATE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--panelVariableFilterType`,
  FILTER_VAR_SELECTION_TYPE_LABELTEXT: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--panelVariableFilterType-hiddenSelect`,
  FILTER_VAR_VALUE_STATE_MESSAGE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--panelVariableFilterType-valueStateText`,
  VARIABLE_DEFAULT_VAL_HIGH: "variableDefaultIntervalHighVal--customInput",
  VARIABLE_DEFAULT_VAL_LOW: "variableDefaultIntervalLowVal--customInput",
  VARIABLE_DETAILS_PARAMETERS_FILLED_BY: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--parameterFilledBy`,
  VARIABLE_DEFAULT_DATE_PICKER_INPUT: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--panelVariableDefaultValue--customDatePicker`,
  VARIABLE_DEFAULT_LOOKUP_ENTITY_INPUT: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--lookupEntity`,
  VARIABLE_DEFAULT_RESULT_COLUMN: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--resultElement`,

  VAR_MANDATORY: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--panelVariableMandatoryCheckBox`,
  FILTER_VAR_DIMENSION: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--variableDetailDimension`,
  MEASURE_DETAILS_EXCEPTION_AGGREGATION_TYPE: `${QB_VIEWS.MEASURE_DETAILS}--measureDetailsExceptionAggregationType`,
  MEASURE_DETAILS_EXCEPTION_AGGREGATION_PANEL: `${QB_VIEWS.MEASURE_DETAILS}--exceptionAggregationPanel`,
  MEASURE_DETAILS_EXCEPTION_AGGREGATION_ATTRIBUTES: `${QB_VIEWS.MEASURE_DETAILS}--measureDetailsAttributes`,
  MEASURE_DETAILS_EXCEPTION_AGGREGATION_ATTRIBUTES_ARROW: `${QB_VIEWS.MEASURE_DETAILS}--measureDetailsAttributes-arrow`,
  MEASURE_DETAILS_CONSTANT_SELECTION_TYPE: `${QB_VIEWS.MEASURE_DETAILS}--constantSelectionType`,
  MEASURE_DETAILS_CONSTANT_SELECTION_DIMENSIONS: `${QB_VIEWS.MEASURE_DETAILS}--constantSelectionDimensions`,
  MEASURE_DETAILS_CONSTANT_SELECTION_DIMENSIONS_ARROW: `${QB_VIEWS.MEASURE_DETAILS}--constantSelectionDimensions-arrow`,
  /** Currency Conversion */
  MEASURE_DETAILS_CC_ERROR_HANDLING: `${QB_VIEWS.MEASURE_DETAILS}--analyticModelErrorHandling-arrow`,
  CC_TARGET_CURRENCY_FIXED_VALUE_TBL: "targetCurrency--fixedValueTable-table",
  CC_TARGET_CURRENCY_DIMENSION_TBL: "targetCurrency--targetCurrencyColumnsTable",
  CC_TARGET_CURRENCY_VARIABLE_TBL: "targetCurrency--targetCurrencyVariableTable",
  CC_TARGET_CURRENCY_DIALOG_SELECT_BUTTON: "targetCurrency--select",
  CC_TARGET_CURRENCY_SEARCH_FIELD: "targetCurrency--searchField",
  CC_TARGET_CURRENCY_DIALOG_SELECT_CURRENCY_TYPE: "targetCurrency--targetCurrencyType",

  CC_CONVERSION_TYPE_FIXED_VALUE_TBL: "conversionType--conversionTable-table",
  CC_CONVERSION_TYPE_DIMENSION_TBL: "conversionType--conversionTypeAttributeTable-table",
  CC_CONVERSION_TYPE_VARIABLE_TBL: "conversionType--conversionTypeVariableTable",
  CC_CONVERSION_TYPE_DIALOG_SELECT_BUTTON: "conversionType--select",
  CC_CONVERSION_TYPE_SEARCH_FIELD: "conversionType--searchField",
  CC_CONVERSION_TYPE_DIALOG_SELECT_CURRENCY_TYPE: "conversionType--conversionCurrencyType",

  CC_REFERENCE_DATE_DIALOG_SELECT_BUTTON: "referenceDate--select",
  CC_REFERENCE_DATE_DIALOG_SELECT_CURRENCY_TYPE: "referenceDate--currencyType",
  CC_REFERENCE_DATE_DIMENSION_TBL: "referenceDate--referenceDateColumnsTable",
  CC_REFERENCE_DATE_VARIABLE_TBL: "referenceDate--referenceDateVariableTable",

  /** Unit Conversion */
  UC_TARGET_UNIT_FIXED_VALUE_TBL: "targetUnit--fixedUnitValueTable-table",
  UC_TARGET_UNIT_DIMENSION_TBL: "targetUnit--targetUnitColumnsTable", // Dimensions
  UC_TARGET_UNIT_VARIABLE_TBL: "targetUnit--targetUnitVariableTable",
  UC_TARGET_UNIT_DIALOG_SELECT_BUTTON: "targetUnit--select",
  UC_TARGET_UNIT_SEARCH_FIELD: "targetUnit--searchField",
  UC_TARGET_UNIT_SEARCH_FIELD_RESET: "targetUnit--searchField-reset",
  UC_TARGET_UNIT_DIALOG_SELECT_CANCEL: "targetUnit--cancel",
  UC_TARGET_UNIT_DIALOG_SELECT_SEARCH_FIELD: "targetUnit--searchField-I",

  MEASURE_DETAILS_NON_CUMULATIVE_RECORD_TYPE: `${QB_VIEWS.MEASURE_DETAILS}--recordTypeFieldSelect`,
  MEASURE_DETAILS_NON_CUMULATIVE_RECORD_TYPE_VAL: `${QB_VIEWS.MEASURE_DETAILS}--recordTypeFieldSelect-hiddenInput`,
  MEASURE_DETAILS_NON_CUMULATIVE_TIME_DIMENSION: `${QB_VIEWS.MEASURE_DETAILS}--timeDimensionSelect`,
  MEASURE_DETAILS_NON_CUMULATIVE_TIME_DIM_VAL: `${QB_VIEWS.MEASURE_DETAILS}--timeDimensionSelect-hiddenInput`,
  MEASURE_DETAILS_NON_CUMULATIVE_EXCEPTION_AGGREGATION: `${QB_VIEWS.MEASURE_DETAILS}--measureDetailsExceptionAggregationNcumSelect`,

  CROSS_CALCULATION_DETAILS_EXCEPTION_AGGREGATION_TYPE: `${QB_VIEWS.CROSS_CALCULATION_DETAILS}--exceptionAggregationType`,
  CROSS_CALCULATION_DETAILS_EXCEPTION_AGGREGATION_ATTRIBUTES: `${QB_VIEWS.CROSS_CALCULATION_DETAILS}--crossCalculationDetailsAttributes`,
  COLLISION_HANDLING_SELECT_PRIORITY: `${QB_DIALOGS.COLLISION_HANDLING_DIALOG}--collisionHandlingConfigurationSelect`,
};

export const QB_CHECKBOXES = {
  ASSOCIATED_DIMENSION_DIALOG_SELECT_ALL: QB_SOURCES_PAGE.ASSOCIATED_DIMENSION_DIALOG.replace("dialog", "table-sa"),
  MEASURE_DETAILS_IS_AUXILIARY: `${QB_VIEWS.MEASURE_DETAILS}--isAuxiliaryCheckBox`,
  MEASURE_DETAILS_UNBOOKED_DELTA_HANDLING: `${QB_VIEWS.MEASURE_DETAILS}--unbookedDeltaHandlingCheckBox`,
  CROSS_CALCULATION_DETAILS_IS_AUXILIARY: `${QB_VIEWS.CROSS_CALCULATION_DETAILS}--isAuxiliaryCheckBox`,
};

export const QB_BUTTONS = {
  SAVE_DIALOG_CONFIRM: `${QB_DIALOGS.SAVE_MODEL_DIALOG}--saveButton`,
  ASSOCIATED_DIMENSION_DIALOG_OK: QB_SOURCES_PAGE.ASSOCIATED_DIMENSION_DIALOG.replace("dialog", "ok"),
  SOURCES_CONTEXT_MENU_RENAME: `${QB_SOURCES_PAGE.SOURCES_CONTEXT_MENU}--rename`,
  RENAME_SOURCE_ALIAS_SAVE: `${QUERY_BUILDER_COMPONENT_VIEW}--renameSourceAlias--save`,
  SWITCH_TABLE_VIEW: `${QUERY_BUILDER_COMPONENT_VIEW}--tableViewButton-button`,
  TABLE_ADD_SOURCES: `${QUERY_BUILDER_COMPONENT_VIEW}--addButton`,
  ADD_SOURCE: `${QUERY_BUILDER_COMPONENT_VIEW}--sourceTile--addCubeSourceButton-cubebuilderComponent--cubeSourcesGrid-0`,
  OUTPUT_MEASURE_CONTEXT_MENU: `${QUERY_BUILDER_COMPONENT_VIEW}--measureContextButton-cubebuilderComponent--outputMeasuresList`,
  OUTPUT_MEASURE_CONTEXT_REMOVE: `${QB_SOURCES_PAGE.OUTPUT_MEASURE_CONTEXT}--remove`,
  ASSOCIATION_DIALOG_IMPORT: "TakeOverDialog--associationDialogImportButton",
  ASSOCIATION_DIALOG_CANCEL: "TakeOverDialog--associationDialogCancelButton",
  ASSOCIATION_DIALOG_PARAMETER_LIST: "TakeOverDialog--importParameterList",
  ASSOCIATION_DIALOG_IMPORT_ACTION_SELECT: "TakeOverDialog--actionSelect",
  ASSOCIATION_DIALOG_IMPORT_FILTER_TYPE: "TakeOverDialog--stackedVariableFilterType",
  ASSOCIATION_DIALOG_IMPORT_MULTI_RANGE: "TakeOverDialog--propertyDialogMultipleRange",
  ASSOCIATION_DIALOG_IMPORT_INTERVAL: "TakeOverDialog--propertyDialogInterval",
  NODE_PROPERTIES_NAV: `${DATA_BUILDER_WORKBENCH}--graphSplitView--PropertyPanel--cubebuilder-properties-NodeProperties--backLink-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--cubebuilder-properties-NodeProperties--backBreadCrumbs`,
  MODEL_PROPERTIES_NAV: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--backLink-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties--backBreadCrumbs`,
  OUTPUT_VARIABLE_DETAIL: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--navigateParameterButton-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties--outputParametersList`,
  OUTPUT_VARIABLE_INFOPOPOVER: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableInfoPopoverButton-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties--outputParametersList`,
  ADD_NEW_VARIABLE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--addNewVariableDetails-internalBtn`,
  ADD_NEW_VARIABLE_DETAILS: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--addNewVariableDetails`,
  ADD_SOURCE_VARIABLE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--addSourceVariableDetails-unifiedmenu`,
  ADD_FILTER_VARIABLE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--addFilterVariableDetails-unifiedmenu`,
  ADD_STORY_FILTER_VARIABLE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--addStoryFilterVariableDetails-unifiedmenu`,
  ADD_REFERENCE_DATE_VARIABLE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--addReferenceDateVariable-unifiedmenu`,
  VARIABLE_LIST: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--outputParametersList`,
  ATTRIBUTE_LIST_DAC: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--dataAccessControlDetails--DataAccessControlMapping--mappings--listA`,
  CRITERION_LIST_DAC: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--dataAccessControlDetails--DataAccessControlMapping--mappings--listB`,
  INPUT_PARAMETER_LIST: `${QUERY_BUILDER_GRAPHVIEW_NODEPROPERTIES}--availableParameterList`,
  SOURCE_STACKED_VARIABLE_LIST: `${QUERY_BUILDER_GRAPHVIEW_NODEPROPERTIES}--availableStackedParameterList`,
  PARAMETER_LIST_SEGMENT_BUTTON: `${QUERY_BUILDER_GRAPHVIEW_NODEPROPERTIES}--parameterListSegmentedButton`,
  PARAMETER_LIST_SEGMENT_BUTTON_INACTIVE: `${QUERY_BUILDER_GRAPHVIEW_NODEPROPERTIES}--allInactiveParamsbtn-button`,
  REMOVE_VARIABLE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--removeVariableButton`,
  ADD_MEASURE: `${QB_VIEWS.PROPERTIES}--menuAddMeasureElement`,
  ADD_CALCULATED_MEASURE: `${QB_VIEWS.PROPERTIES}--menuAddMeasureElement--calculatedMeasureMenuItem-unifiedmenu`,
  REMOVE_MEASURE: `${QB_VIEWS.PROPERTIES}--removeMeasureButton`,
  ADD_GLOBAL_FILTER: `${QB_VIEWS.PROPERTIES}--addFilterButton`,
  BREADCRUM_SELECT: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--backBreadCrumbs`,
  PARAMETER_ELEMENT_INFO: `${QUERY_BUILDER_GRAPHVIEW_NODEPROPERTIES}--elementInfoPopoverButtonParameters`,
  VALIDATION_POPOVER_BUTTON: `${DATA_BUILDER_COMPONENT}---databuilderWorkbench--validationPopoverButton`,
  NAV_BACK: `--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties--backLink-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties--backBreadCrumbs`,
  TOGGLE_FULLSCREEN: `${QB_VIEWS.PROPERTIES}--toggleFullScreen`,
  ATTRIBUTE_LIST: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--attributeTree`,
  CC_TARGET_CURRENCY_DIM_TOGGLE_BUTTON: "targetCurrency--dimensionsTargetToggleButton-button",
  CC_TARGET_CURRENCY_FIXED_TOGGLE_BUTTON: "targetCurrency--fixedTargetToggleButton-button",
  UC_TARGET_UNIT_DIM_TOGGLE_BUTTON: "targetUnit--dimensionsTargetToggleButton-button",
  UC_TARGET_UNIT_FIXED_TOGGLE_BUTTON: "targetUnit--fixedTargetToggleButton-button",
  HIDDEN_PARAMETER: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--cubebuilder-properties-ModelProperties--hiddenParamButton`,
  ADD_CROSS_CALCULATION: `${QB_VIEWS.PROPERTIES}--menuAddCrossCalculationElement`,
  CONFIGURE_COLLISION_HANDLING_BUTTON: `${QB_VIEWS.PROPERTIES}--configureCollisionHandlingButton`,
  COLLISION_HANDLING_DIALOG_OK: `${QB_DIALOGS.COLLISION_HANDLING_DIALOG}--collisionHandlingConfigurationDialogOkButton`,
  COLLISION_HANDLING_DIALOG_CANCEL: `${QB_DIALOGS.COLLISION_HANDLING_DIALOG}--collisionHandlingConfigurationDialogCloseButton`,
  SWITCH_DATA_EXPORT: `${QB_VIEWS.PROPERTIES}--allowDataExportSwitch`,
  OPEN_MEASURE_DEPENDENCIES: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--measureDependenciesButton`,
  CLOSE_MEASURE_DEPENDENCIES: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--measureDependencies--closeButton`,
};

export const QB_BREADCRUMBS = {
  DETAILS_BACK: `${QB_VIEWS.PROPERTIES}--backLink-${QB_VIEWS.PROPERTIES}--backBreadCrumbs-0`,
  MODEL_BREADCRUMB_LIST: `${DATA_BUILDER_COMPONENT}---databuilderWorkbench--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties--backBreadCrumbs`,
  ATTRIBUTE_BREADCRUMB_LIST: `${DATA_BUILDER_COMPONENT}---databuilderWorkbench--graphSplitView--PropertyPanel--cubebuilder-properties-AttributeProperties--backBreadCrumbs`,
  NODE_DETAILS_BACK: `${QB_VIEWS.NODE_PROPERTIES}--backLink-${QB_VIEWS.NODE_PROPERTIES}--backBreadCrumbs-0`,
};

export const QB_EXPRESSION_EDITOR = {
  OPERATOR_TOOLBAR: `${QB_VIEWS.MEASURE_DETAILS}--calculatedMeasureExpressionEditor--hboxOtherOperators`,
  INSERT_VALUES_BUTTON_RESTR: `${QB_VIEWS.MEASURE_DETAILS}--restrictedMeasureExpressionEditor--valueHelp`,
  VALIDATE_BUTTON: `${QB_VIEWS.MEASURE_DETAILS}--calculatedMeasureExpressionEditor--validateIcon`,
  VALIDATE_BUTTON_RESTR: `${QB_VIEWS.MEASURE_DETAILS}--restrictedMeasureExpressionEditor--validateIcon`,
  EDITOR_TEXTAREA: `${QB_VIEWS.MEASURE_DETAILS}--calculatedMeasureExpressionEditor--expressionCodeEditor-editor-textarea`,
  EDITOR_TEXTAREA_RESTR: `${QB_VIEWS.MEASURE_DETAILS}--restrictedMeasureExpressionEditor--expressionCodeEditor-editor-textarea`,
  EDITOR_CALCU: `${QB_VIEWS.MEASURE_DETAILS}--calculatedMeasureExpressionEditor--expressionCodeEditor-editor`,
  EDITOR_RESTR: `${QB_VIEWS.MEASURE_DETAILS}--restrictedMeasureExpressionEditor--expressionCodeEditor-editor`,
  EDITOR_TEXTAREA_FILTER: `${QB_VIEWS.GLOBAL_FILTER_DETAILS}--globalFilterExpressionEditor--expressionCodeEditor-editor-textarea`,
  EDITOR_FILTER: `${QB_VIEWS.GLOBAL_FILTER_DETAILS}--globalFilterExpressionEditor--expressionCodeEditor-editor`,
  EDITOR_MESSAGE_STRIP: `${QB_VIEWS.MEASURE_DETAILS}--calculatedMeasureExpressionEditor--statusMessageStrip`,
  EDITOR_MESSAGE_STRIP_RESTR: `${QB_VIEWS.MEASURE_DETAILS}--restrictedMeasureExpressionEditor--statusMessageStrip`,
  COLUMN_SELECT_RESTR: `${QB_VIEWS.MEASURE_DETAILS}--restrictedMeasureExpressionEditor--expressionBuilderCategory`,
  COLUMN_SELECT_FILTER: `${QB_VIEWS.GLOBAL_FILTER_DETAILS}--globalFilterExpressionEditor--expressionBuilderCategory`,
  MEASURE_COLUMN_LIST: `${QB_VIEWS.MEASURE_DETAILS}--calculatedMeasureExpressionEditor-selectedMeasureColumn-cont`,
  ATTRIBUTE_COLUMN_LIST: `${QB_VIEWS.MEASURE_DETAILS}--restrictedMeasureExpressionEditor-selectedAttributeColumn-cont`,
  ATTRIBUTE_COLUMN_LIST_FILTER: `${QB_VIEWS.GLOBAL_FILTER_DETAILS}--globalFilterExpressionEditor-selectedAttributeColumn-cont`,
  ATTRIBUTE_COLUMN_LIST_CALCULATED_MEASURE: `${QB_VIEWS.MEASURE_DETAILS}--calculatedMeasureExpressionEditor-selectedAttributeColumn-cont`,
  FUNCTION_COLUMN_LIST: `${QB_VIEWS.MEASURE_DETAILS}--calculatedMeasureExpressionEditor-functions-cont`,
  CALCULATED_VARIABLE_COLUMN_LIST: `${QB_VIEWS.MEASURE_DETAILS}--calculatedMeasureExpressionEditor-selectedVariableColumn-cont`,
  EXPRESSION_SELECT: `${QB_VIEWS.MEASURE_DETAILS}--calculatedMeasureExpressionEditor--expressionBuilderCategorySelect`,
  VARIABLE_COLUMN_LIST: `${QB_VIEWS.MEASURE_DETAILS}--restrictedMeasureExpressionEditor-selectedVariableColumn-cont`,
  OPERATOR_COLUMN_LIST: `${QB_VIEWS.GLOBAL_FILTER_DETAILS}--globalFilterExpressionEditor-others-cont`,
  EXPRESSION_VALUE_HELP_TABLE: `${QB_VIEWS.MEASURE_DETAILS}--expressionValueHelpDialog--valueListTable-table`,
  EXPRESSION_VALUE_HELP_SELECT: `${QB_VIEWS.MEASURE_DETAILS}--expressionValueHelpDialog-cnt-saveButton`,
  NODE_PROPERTIES_NAV: `${DATA_BUILDER_WORKBENCH}--graphSplitView--PropertyPanel--cubebuilder-properties-NodeProperties--backLink-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--cubebuilder-properties-NodeProperties--backBreadCrumbs`,
  MODEL_PROPERTIES_NAV: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--backLink-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties--backBreadCrumbs`,
  OUTPUT_VARIABLE_DETAIL: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--navigateParameterButton-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties--outputParametersList`,
  OUTPUT_VARIABLE_INFOPOPOVER: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableInfoPopoverButton-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties--outputParametersList`,
  NO_DATA_VARIABLES: `#shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--cubebuilder-properties-ModelProperties--outputParametersList-nodata-text`,
  ADD_NEW_VARIABLE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--addNewVariableDetails-internalBtn`,
  ADD_SOURCE_VARIABLE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--addSourceVariableDetails-unifiedmenu-txt`,
  ADD_FILTER_VARIABLE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--addFilterVariableDetails-unifiedmenu-txt`,
  VARIABLE_LIST: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--outputParametersList`,
  REMOVE_VARIABLE: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--removeVariableButton`,
  PARAMETERS_MAPPING_DIALOG: "sap-cdw-components-csnquerybuilder-view-ParameterMapping--dialog",
  PARAMETER_DEFAULTVAL_DIALOG: "analyticalModelDefaultValDialog",
  PARAMETER_DEFAULTVAL_FILTER_VAR_SELECTION_TYPE: "stackedVariableFilterType-arrow",
  PARAMETER_DEFAULTVAL_FILTER_VAR_SELECTION_TYPE_VAL: "stackedVariableFilterType-hiddenInput",
  PARAMETER_DEFAULTVAL_FILTER_VAR_SELECTION_TYPE_STATE: "stackedVariableFilterType",
  PARAMETER_DEFAULTVAL_FILTER_VAR_SELECTION_TYPE_LABELTEXT: "stackedVariableFilterType-labelText",
  PARAMETER_DEFAULTVAL_MULTI_RANGE: "parameterDetailMultipleRange",
  PARAMETER_DEFAULTVAL_SINGLE_VALUE: "parameterDefaultValueInput--customInput",
  PARAMETER_DEFAULTVAL_INTERVAL: "parameterDetailInterval",
  USED_PARAM_LIST: `${QUERY_BUILDER_GRAPHVIEW_MODELPROPERTIES}--variableDetails--usedParamList`,
  CALCULATED_CROSS_CALCULATION: `${QB_VIEWS.CROSS_CALCULATION_DETAILS}--calculatedCrossCalculationExpressionEditor--expressionCodeEditor`,
  RESTRICTED_CROSS_CALCULATION: `${QB_VIEWS.CROSS_CALCULATION_DETAILS}--restrictedCrossCalculationExpressionEditor--expressionCodeEditor`,
  SEARCH: `[id*=ExpressionEditor] input[id*=columnSearchInput]`,
};

export const QB_LABELS = {
  BREADCRUMB_NEW_QUERY: "newAnalyticModel",
};

export const QB_MESSAGES = {
  DUPLICATED_NAME_MESSAGE: "duplicatedNameMsgbox",
  CC_VIEWS_NOT_AVAILABLE_MESSAGE: `${QB_VIEWS.MEASURE_DETAILS}--messageStripCCTablesNotAvailableError`,
  CC_VIEWS_EMPTY_MESSAGE: `${QB_VIEWS.MEASURE_DETAILS}--messageStripCCTablesNoContent`,
  UC_VIEWS_NOT_AVAILABLE_MESSAGE: `${QB_VIEWS.MEASURE_DETAILS}--messageStripUCTablesNotAvailableError`,
  UC_VIEWS_EMPTY_MESSAGE: `${QB_VIEWS.MEASURE_DETAILS}--messageStripUCTablesNoContent`,
};

export const QB_FIXTURE_REQUESTS = {
  LANDING_PAGE_OBJECTS: "querybuilderLandingPageObjects",
  SEARCH_SOURCES_REQUEST: "querybuilderSearchSources",
};

export const QB_SUMMARY_TAB = {
  SUMMARY_TAB_TITLE: `${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--summaryTab`,
  SUMMARY_BUSINESS_NAME: `${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--businessNameInput`,
  SUMMARY_TECHNICAL_NAME: `${QUERY_BUILDER_COMPONENT_VIEW}--technicalNameInput`,
  SUMMARY_DATA_ACCESS: `${QUERY_BUILDER_COMPONENT_VIEW}--settingsPublicDataAccess`,
  DEPLOYMENT_STATUS_TEXT: `${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--objectStatusText`,
  DEPLOYMENT_STATUS_ICON: `${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--objectStatusIcon`,
  DEPLOYMENT_STATUS_LABEL: `${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--objectStatusLabel`,
  SEARCH_INPUT: `${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--outputSearchField-I`,
  OUTPUT_MEASURE_LIST: `${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--outputMeasuresList`,
  OUTPUT_ATTRIBUTE_LIST: `${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--outputAttributesList`,
  OUTPUT_ATTRIBUTE_TREE: `${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--attributeTree`,
  OUTPUT_PARAMETER_LIST: `${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--outputParametersList`,
  OUTPUT_FILTER_LIST: `${QB_QUERY_MODEL_TAB.MODEL_PROPERTIES_PANEL}--outputGlobalFilterList`,
};

export const QB_ICONS = {
  OUTPUT_ATTRIBUTES_ICON: `${QUERY_BUILDER_COMPONENT_VIEW}--outputAttributeTextRelationIcon-cubebuilderComponent--outputAttributesList`,
};

export const QB_LOADING_INDICATORS = {
  DATA_BUILDER: `${DATA_BUILDER_COMPONENT}---databuilderWorkbench-busyIndicator`,
  CUBE_BUILDER: "cubebuilderComponent-busyIndicator",
};

export const QB_LINKS = {
  CROSS_CALCULATION_DETAILS_SOURCE: `${QB_VIEWS.CROSS_CALCULATION_DETAILS}--crossCalculationSourceLink`,
};
