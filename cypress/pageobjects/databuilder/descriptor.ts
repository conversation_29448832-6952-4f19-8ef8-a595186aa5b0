/** @format */

import { AB_BUT<PERSON><PERSON>, AB_CONTROL, AB_INPUT, AB_LIST, AB_TABLE, AB_TEXT, AB_VIEW } from "../abstractbuilder/descriptor";

/** Copyright 2019 SAP SE or an SAP affiliate company. All rights reserved. */

/*
                  ---------------------
                  | CypressPageObject | @interface
                  ---------------------
                            ^
                          /   \
                          -----
                            |
                  ---------------------
                  |       View        | @abstract
                  ---------------------
                            ^
                          /   \
                          -----
                            |
            |-------------------------------|-----------------|----------------------|------------------|------------------|
  ---------------------               --------------   ------------------   -------------------   ---------------   ---------------
  |       Editor      | @abstract     | BrowerView |   | PropertiesView |   | DataPreviewView |   | TableEditor |   | DiagramView |
  ---------------------               --------------   ------------------   -------------------   ---------------   ---------------
            ^      |--------composed--------^-----------------^-----------------^        ^-----composed---|               ^
          /   \                                                                                                           |
          -----                                                                                                           |
            |---------------------------------|-----------------------------composed---------------------------------------
  -------------                      ------------------
  | SQLEditor |                      | DiagramEditor  | @abstract
  -------------                       ------------------
                                            ^
                                          /   \
                                          -----
                                            |
                                   |-----------------|
                      ---------------------    -------------------
                      | ViewBuilderEditor |    | ERModelerEditor |
                      ---------------------    -------------------
*/

export let CURRENT_BUILDER = {
  COMPONENT_NAME: "databuilderComponent",
  WORKBENCH_NAME: "databuilderWorkbench",
};

export const DB_VIEW = {
  ROOT: "",
  DBH: "shellMainContent---databuilderComponent---databuilderLandingPage--entitySelectionLandingPage",
  DBHN: "shellMainContent---databuilderComponent---databuilderLandingPage--compositeSearchLandingPage",
  DBWB: "shellMainContent---databuilderComponent---databuilderWorkbench",
  STORY: "shellMainContent---storiesComponent---storySelection--storySelectionLandingPage",
  TOOLBAR_DATA_PREVIEW: "shellMainContent---databuilderComponent---databuilderWorkbench--toolbarDataPreview",
  ENTITY_PROPERTY_VIEW:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView",
  GV_ENTITY:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-EntityProperties",
  GV_JOIN:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-JoinPropertiesWithRenameFeature",
  GV_UNION:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-UnionPropertiesWithRenameFeature",
  GV_PROJECTION:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-RenameElementsPropertiesWithRenameFeature",
  GV_PROJECTION_WITH_RENAME_FF:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-RenameElementsPropertiesWithRenameFeature",
  GV_FILTER:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-FilterProperties",
  GV_CALCULATED:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-CalculatedPropertiesWithRenameFeature",
  GV_NODE_RENAME_CALCULATED:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-CalculatedPropertiesWithRenameFeature",
  GV_AGGREGATED:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-AggregationPropertiesWithRenameFeature",
  GV_RENAME_AGGREGATED:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-AggregationPropertiesWithRenameFeature",
  OUTPUT:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties",
  GV_FORMULA:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--formulaDetailsContainer",
  GV_RESTRICTED_MEASURE:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--restrictedMeasureDetailsContainer",
  ER_ENTITY:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView",
  ER_MODEL_PRO:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-ModelProperties",
  CROSS_SPACE_SHARE_DIALOG: "sap-cdw-components-databuilder-view-ArtefactSharingDialog--dialog--view",
  ER_DIALOG_ROOT: "sap-cdw-components-ermodeler-view-",
  ER_SELECT_REMOTE_SOURCE: "sap-cdw-components-ermodeler-view-SelectRemoteSource--dialog--view",
  ER_REMOTE_OBJECTS_SELECTOR: "sap-cdw-components-ermodeler-view-RemoteObjectsSelector--dialog--view",
  VALIDATIONS: AB_VIEW.VALIDATIONS,
  VALIDATIONSCHEMA: "sourceSchemaValidationPopover-messagePopover-popover",
  VALIDATIONS_WITH_RENAME_FF: AB_VIEW.VALIDATIONS,
  TABLE_EDITOR: "tableEditor",
  SOURCE_CANCEL_BUTTON: "sap-cdw-components-replicationflow-view-SourceConnectionSettingDialog--dialog--view--cancel",
  SOURCE_SAVE_BUTTON: "sap-cdw-components-replicationflow-view-SourceConnectionSettingDialog--dialog--view--ok",
  SCHEMA_CANCEL_BUTTON: "sourceSchemaDialog--dialog--view--cancel",
  SCHEMA_SAVE_BUTTON: "sourceSchemaDialog--dialog--view--ok",
  SCHEMA_TABLE: "sourceSchemaDialog--dialog--view--sourceTable",
  SCHEMA_DAILOG_ID: "sourceSchemaDialog--dialog--view",
  SQL_EDITOR: "sqlEditor",
  INNER_SQL_EDITOR: "sqlEditor--sqlEditor",
  BUSY_INDICATOR_DIALOG: "workbenchBusyIndicator-Dialog",
  ERROR_POPOVER: AB_VIEW.ERROR_POPOVER,
  TE_VALIDATIONS: AB_VIEW.VALIDATIONS,
  TE_VALIDATIONS_MARKUP: "validationsView--validations--listMarkupDescription",
  TE_HIERARCHY_DIALOG_VIEW: "shellMainContent---databuilderComponent---databuilderWorkbench--hierarchyDialogView",
  ER_HIERARCHY_PARENT_SELECT:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--ck--parentElementId",
  ER_HIERARCHY_CHILD_SELECT:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--ck--childElementId",
  ER_ASSOCIATION:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-AssociationProperties",
  GV_CALCULATED_EXPRESSION:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-CalculatedPropertiesWithRenameFeature--vBoxExContainer",
  HIERARCHY_VIEW:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--hierarchyDialogView",
  HIERARCHY_DIALOG:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--hierarchyDialog",
  ALLOW_CONSUMPTION_LABEL:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--allowConsumptionLabel",
  ALLOW_CONSUMPTION_SWITCH:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--allowConsumptionSwitch",
  ALLOW_CONSUMPTION_HELPBUTTON:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--allowConsumptionHelpButton",
  USE_OLAP_DB_HINT_LABEL:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--useOLAPDBHintLabel",
  USE_OLAP_DB_HINT_SWITCH:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--useOLAPDBHintSwitch",
  VIEW_PERSISTENCY_ACTION_MENUBUTTON:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--viewPersistencyActionForEditorMenuBtn",
  VIEW_PERSISTENCY_MONITOR_NAVIGATION_LINKBUTTON:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--viewMonitorLink",
  VIEW_PERSISTENCY_REFRESHBUTTON:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--refreshPersistencyButton",
  DF_EDITOR: "dataFlowModelerEditor",
  DF_EDITOR_CONTROL: "dataFlowModelerEditor--dataFlowModelerEditorControl",
  DF_NODE_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--dataflowmodeler-properties-NodeProperties",
  DF_MODEL_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--dataflowmodeler-properties-ModelProperties",
  DF_TASKSCHEDULE_DIALOG:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--dataflowmodeler-properties-ModelProperties--detailSchedulingDialog--taskScheduleDialog",
  DF_AGGREGATION_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--dataflowmodeler-properties-AggregationProperties",
  DF_PROJECTION_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--dataflowmodeler-properties-ProjectionProperties",
  DF_UNION_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--dataflowmodeler-properties-UnionProperties",
  DF_JOIN_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--dataflowmodeler-properties-JoinProperties",
  DF_SCRIPT_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--dataflowmodeler-properties-ScriptProperties",
  DF_SCRIPT_PROPERTIES_OBJECT_PAGE:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--dataflowmodeler-properties-ScriptPropertiesObjectPage",
  TF_MODEL_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--transformationflow-properties-ModelProperties",
  RF_PROPERTY_PANEL:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--replicationflow-properties-ReplicationFlowProperties",
  VAL_ERR_DIALOG: "sap-cdw-components-abstractbuilder-view-validations--dialog",
  IL_DIALOG: "sap-cdw-components-reuse-control-impactLineage-ImpactAnalysisDialog--dialog",
  ODATA_DIALOG: "odataEditor--dialog--odataEditorDialog",
  ODATA_DIALOG_VIEW: "odataEditor--dialog",
  ODATA_VIEW_API_BUILDER: "odataEditor--dialog--API_BUILDER",
  ODATA_VIEW_API_PREVIEW: "odataEditor--dialog--API_PREVIEW",
  DF_PRJECTION_ADD_COLUM: "projectionColumnSelector--columnSelectDialog-dialog",
  DF_SCRIPT_ADD_COLUM: "scriptColumnSelector--columnSelectDialog-dialog",
  DF_INPUT_PARAMETERS_DIALOG: "dfParametersEditor--dfParametersDialog",
  DF_CHANGE_MANAGEMENT_DIALOG: "sap-cdw-components-dataflowmodeler-view-ChangeManagementInfo--dialog",
  DF_UNSUPPORTED_SOURCE_ERROR_DIALOG: "unsupportedViewErrMsg",
  DF_UNSUPPORTED_CDS_VIEW_ERROR: "errorConfirmDialog",
  NO_ACCESS_DIALOG: "noAccessDialog",
  CONFIRM_IMPORT_CSN_DIALOG: "confirmImportCsnDialog",
  ER_IMPACT_ERROR_DIALOG: "sap-cdw-components-ermodeler-view-ImpactErrors--dialog",
  CHANGE_MANAGEMENT_DIALOG: "sap-cdw-components-csnquerybuilder-view-ChangeManagementInfo--dialog",
  SQL_CHANGE_MANAGEMENT_DIALOG: "sap-cdw-components-sqleditor-view-ChangeManagementInfo--dialog",
  REPLACE_SOURCE_MAPPING_DIALOG: "sap-cdw-components-ermodeler-properties-ReplaceSourceMapping--dialog",
  EXTERNAL_HIERARCHY_MAPPING_DIALOG: "sap-cdw-components-ermodeler-properties-ExternalHierarchyMapping--dialog",
  DF_JOIN_OBJECT_PAGE:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--dataflowmodeler-properties-JoinPropertiesObjectPage",
  DF_ADD_COLUMN_DIALOG: "sap-cdw-components-dataflowmodeler-properties-AddColumnsDialog--dialog",
  DF_SCRIPT_DIALOG_COLUMN: "sap-cdw-components-dataflowmodeler-properties-ColumnPropertyEditor--dialog--view",
  DF_NODE_CSV_DIALOG: "nodeProperties--DataAccessConfigDialog",
  DF_NODE_CSV_BUSY_DIALOG: "DataAccessConfigDialog-busyIndicator",
  TREE_VIEW_BUSY: "shellMainContent---databuilderComponent---databuilderWorkbench--TreeView-busyIndicator",
  DF_NODE_JSON_DIALOG: "nodeProperties--JsonConfigDialog",
  IL_MODEL_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--intelligentlookup-properties-ModelProperties",
  ILT_EDITOR: "intelligentLookupEditor",
  ILT_RULE_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--intelligentlookup-properties-RuleProperties",
  ILT_RULE_PROPERTIES_FULLSCREEN:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--intelligentlookup-properties-RuleNodePropertiesExpanded",
  ILT_INPUT_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--intelligentlookup-properties-InputNodeProperties",
  ILT_LOOKUP_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--intelligentlookup-properties-LookupNodeProperties",
  ILT_OUTPUT_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--intelligentlookup-properties-OutputNodeProperties",
  ILT_WELCOME_TEXT: "intelligentLookupEditor--ilWelcomeText-bdi",
  ILT_MAPPING_PAGE:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--intelligentlookup-properties-RuleProperties",
  ILT_RULE_TAB_BAR:
    "shellMainContent---databuilderComponent---databuilderWorkbench--RuleDataPreview--ruleNodeTabContainer",
  IL_CHANGE_MANAGEMENT_DIALOG: "sap-cdw-components-intelligentlookup-view-ChangeManagementInfo--dialog",
  IL_DELETION_CONFIRM_DIALOG: "intelligentLookupEditor--deletionConfirmDialog",
  UPLOADCSV_FILEUPLOADER: "sap-cdw-components-fileupload-views-ImportFile--dialog",
  WRANGLING_BUSY_DIALOG: "wranglingBusyIndicator-Dialog",
  IMPORT_FAILED_DIALOG: "importFailedMsgbox",
  VF_DETAILS:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--filterColumnDetailsContainer",
  UNION_ALL_POPOVER:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-UnionPropertiesWithRenameFeature--GenericPopover--popover",
  TC_EDITOR: "taskChainModeler",
  TC_EDITOR_CONTROL: "taskChainModeler--taskChainModelerEditorControl",
  TC_EDITOR_CONTROL_INNER: "taskChainModeler--taskChainModelerEditorControl--editorControl",
  TC_NODE_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--taskchainmodeler-properties-ModelProperties--nodeDetailsContainer",
  TC_MODEL_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--taskchainmodeler-properties-ModelProperties",
  TC_API_NODE_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--taskchainmodeler-properties-ModelProperties--RESTTaskDetailsContainer",
  TC_TASKSCHEDULE_DIALOG:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--taskchainmodeler-properties-ModelProperties--scheduleDialog--taskScheduleDialog",
  TC_OPERATION_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--taskchainmodeler-properties-ModelProperties--operatorDetailsContainer",
  TC_INPUT_PARAMETERS_DIALOG: "sap-cdw-components-taskchainmodeler-view-InputParameter--dialog",
  TC_NON_REPOSITORY_TREE:
    "shellMainContent---databuilderComponent---databuilderWorkbench--TreeView--nonRepoTreeItem-shellMainContent---databuilderComponent---databuilderWorkbench--TreeView--nonRepoTree",
  TC_SEARCH_FIELD: "shellMainContent---databuilderComponent---databuilderWorkbench--TreeView--searchNonRepo",
  SAVE_ERROR_DIALOG: "__error0",
  DEPLOY_ERROR_DIALOG: "deployFailedMsgbox",
  GV_CONFIRM_SAVE: "csnQueryBuilderEditor--confirmSaveDialog",
  ER_CONFIRM_SAVE: "erModelerEditor--confirmSaveDialog",
  DATA_PREVIEW_COLUMNS_CONTROL_DIALOG: "customizeColumnsDialog",
  GV_INPUT_PARAMETER_VALUE_DIALOG:
    "sap-cdw-components-databuilder-view-InputParameterDataPreview--dialog--view--IPTableDataPreview",
  GV_RENAME_DIALOG: "changeNameDialog",
  DF_SOURCE_FILTERS_DIALOG: "sap-cdw-components-dataflowmodeler-properties-DataSourceObjectDialog--dialog",
  GV_INPUT_PARAMTER_DATA_PREVIEW_DIALOG_PARAM_NAME:
    "sap-cdw-components-databuilder-view-InputParameterDataPreview--dialog--view--IPTableDataPreview-rows-row0-col0",
  GV_INPUT_PARAMTER_DATA_PREVIEW_DIALOG_PARAM_NAME_ROW2:
    "sap-cdw-components-databuilder-view-InputParameterDataPreview--dialog--view--IPTableDataPreview-rows-row1-col0",
  GV_INPUT_PARAMTER_DATA_PREVIEW_DIALOG_PARAM_VALUE_TEXTBOX:
    "sap-cdw-components-databuilder-view-InputParameterDataPreview--dialog--view--IPTableDataPreview-rows-row0-col1",
  DATA_PREVIEW_BUSY_INDICATOR:
    "shellMainContent---databuilderComponent---databuilderWorkbench--previewTable-busyIndicator",
  DF_REPOSITORY_VIEW: "shellMainContent---databuilderComponent---databuilderWorkbench--TreeView--tabDataSource",
  TF_SOURCES_TAB: "shellMainContent---databuilderComponent---databuilderWorkbench--TreeView--remoteTab",
  DF_SOURCE_TARGET_SWITCH_WARNING:
    "sap-cdw-components-dataflowmodeler-properties-NodeProperties--sourceTargetSwitchWarning",
  DF_NODE_EXCEL_DIALOG: "nodeProperties--ExcelPropertiesDialog",
  MDM_VALIDATION_POPOVER:
    "shellMainContent---databuilderComponent---databuilderWorkbench--mdmPreviewEditor--mdmMessagePopover-messagePopover-popover",
  MDM_CUSTOMIZE_COLUMN_NAVIGATION_ITEMS: "mdmCustomizeColumnsDialog-navigationItems",
  GV_ANALYTIC_MEASURE_POPOVER:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--AnalyticalMeasureInfoPopover",
  DI_COMP: "shellMainContent---dataIntegrationComponent",
  VIEW_MONITOR_PAGE: "shellMainContent---dataIntegrationComponent---viewmonitor",
  MDM_DATA_PREVIEW_WARNING: AB_VIEW.GENWORKBENCH + "--mdmPreviewEditor--mdm-illusMsg",
  DF_JOIN_PROP_DIALOG: "sap-cdw-components-dataflowmodeler-properties-JoinAdvancedProperties--dialog",
  DF_JOIN_ADVANCED_PROP_DIALOG: "sap-cdw-components-dataflowmodeler-properties-JoinAdvancedProperties--dialog--view",
  DF_RUN_WITH_INPUT_PARAMETERS_DIALOG: "dfRunWithParameterDialog--runWithInputParameterDialog",
  DF_MEMORY_ALLOCATION_CONFIRM_DIALOG: "memoryConfigurationConfirmation",
  GENWORKBENCH: `shellMainContent---${CURRENT_BUILDER.COMPONENT_NAME}---${CURRENT_BUILDER.WORKBENCH_NAME}`,
  DF_REMOTE_SOURCE_ADD_DIALOG: "remoteSourceColumnSelector--columnSelectDialog-dialog",
  DF_REMOTE_SOURCE_REFRESH_DIALOG:
    "sap-cdw-components-dataflowmodeler-properties-RemoteSourceColumnRefreshDialog--dialog",
  VIEW_MONITOR_TASKLOG: "shellMainContent---dataIntegrationComponent---taskLog",
  TARGETCURRENCY_DIALOG: "targetCurrency--dialog",
  CONVERSIONTYPE_DIALOG: "conversionType--dialog",
  ARTEFACT_DEPLOY_DIALOG: "sap-cdw-components-reuse-deploy-ArtefactDeployDialog--dialog--view",
  RF_EDITOR: "replicationFlowBuilder",
  RF_PROPERTY_PANEL:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--replicationflow-properties-ReplicationFlowProperties",
  RF_OBJECT_PROPERTY_PANEL:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--replicationflow-properties-ReplicationObjectProperties",
  RF_CONNECTION_SETTING_DIALOG: "sap-cdw-components-replicationflow-view-TargetConnectionSettingDialog--dialog",
  RF_SOURCE_CONNECTION_SETTING_DIALOG: "sap-cdw-components-replicationflow-view-SourceConnectionSettingDialog--dialog",
  RF_TRANSFORMATION_DIALOG: "sap-cdw-components-replicationflow-view-AddTransformationDialog--dialog--view",
  RF_DATASET_RENAME_DIALOG: "renameDialog",
  RF_CONN_CHANGE_CONFIRM_DIALOG: "confirmChangeConnectionDialog",
  TF_EDITOR: "transformationFlowEditor",
  TF_VIEW_TRANSFORM_NODE_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--transformationflow-properties-ViewTransformNodeProperties",
  TF_TARGET_NODE_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--transformationflow-properties-TargetNodeProperties",
  TF_SECONDARY_SQL_EDITOR_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--transformationflow-properties-secondary-SQLOutputProperties",
  TF_SECONDARY_SQL_EDITOR_EDIT_COLUMN_DIALOG:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--transformationflow-properties-secondary-SQLOutputProperties--editColumnsDialog",
  TF_SECONDARY_GV_EDITOR_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--transformationflow-properties-secondary-GVOutputProperties",
  TF_PYTHON_NODE_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--transformationflow-properties-ScriptProperties",
  TF_SOURCE_TABLE_NODE_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--transformationflow-properties-SourceTableNodeProperties",
  TF_CHANGE_MANAGEMENT_DIALOG: "sap-cdw-components-transformationflow-view-ChangeManagementInfo--dialog",
  TF_GV_SQL_SWITCH_DIALOG: "sap-cdw-components-transformationflow-view-TFModeler--gvSQLSwitchWarning",
  TF_SQL_WITH_ERROR_CONFIRMATION_DIALOG: "tfSQLNavigationWarning",
  MAD: "MassActionDialog",
  MAD_DCD: "shellMainContent---databuilderComponent---databuilderLandingPage--deleteObjectConfirmDialog",
  PSC: "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--propertiesScrollContainer",
  ADD_REPOSITORY_OBJECTS_DIALOG:
    "shellMainContent---databuilderComponent---databuilderLandingPage--addRepositoryObjectsDlg--tableSelectDialog-dialog",
  DF_UNSUPPORTED_DELTA_TABLE_ERROR_DIALOG: "UnsupportedDeltaTable",
  RF_DDL_DIALOG: "previewSQLTextDialog-scrollCont",
  IL_DIALOG_VIEW: "sap-cdw-components-reuse-control-impactLineage-ImpactAnalysisDialog--dialog--view",
  ILA_EDITOR:
    "sap-cdw-components-reuse-control-impactLineage-ImpactAnalysisDialog--dialog--view--ilanalyzer--editorControl",
  TF_SECONDARY_SOURCE_PROPERTIES:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--transformationflow-properties-SecondarySourceProperties",
  DAC_IMPERSONATION_USER_LIST:
    "shellMainContent---databuilderComponent---databuilderWorkbench--ImpersonationDialogView--userList",
  RUNTIME_METRICS: "shellMainContent---databuilderComponent---databuilderWorkbench--runtimeMetrics",
  RUNTIME_METRICS_RUN_BUTTON: "runtimeMetrics--runMetricsButton",
  RUNTIME_METRICS_CANCEL_BUTTON: "runtimeMetrics--cancelRunMetricsButton",
  TF_RUN_WITH_INPUT_PARAMETERS_DIALOG: "runWithInputParameterDialogTF--runWithInputParameterDialogTF",
};

export const DB_CONTROL = {
  TE_PAGE: "tableEditor--tableEditorPage",
  TE_PARTITION_SECTION: "tableEditor--partitionSection",
  ATTRIBUTES_DIALOG: DB_VIEW.OUTPUT + "--EntityPropertiesView--editAttributesDialog",
  GV_DATA_PREVIEW_TAB: AB_CONTROL.DATA_PREVIEW_TAB,
  DBH_IMPORT_CSN_DIALOG_BUSY:
    DB_VIEW.ROOT +
    "shellMainContent---databuilderComponent---databuilderLandingPage--addRepositoryObjectsDlg--tableSelectDialog-dialog-busyIndicator",
  GV_CURRENCY_CONVERSION_CALENDAR: "referenceDate--CalendarContainer",
  FILTER_PANEL: DB_VIEW.TABLE_EDITOR + "--filterPanel",
  TE_TASK_SCHEDULE: DB_VIEW.TABLE_EDITOR + "--remoteTableScheduleReplication--taskScheduleDialog--view",
  GV_CALCULATED_NODE_SCROLL: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--listAScrollContainer",
  GV_NAME_ICON: DB_VIEW.OUTPUT + "--EntityPropertiesView--detailsHeader--nameIcon",
  CONFIRM_SAVE_DIALOG: DB_VIEW.ENTITY_PROPERTY_VIEW + "--confirmSaveDialog",
  REMOTE_TBL_TASKLOG: DB_VIEW.DI_COMP + "---taskLog",
  VIEW_MONITOR_TASKLOG: DB_VIEW.DI_COMP + "---taskLog",
  GV_CALCULATED_EXP_PAGE: DB_VIEW.GV_CALCULATED + "--expressionPage",
  GV_CALCULATED_EXP_PAGE_RENAME: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--expressionPage",
  GV_FORMULA_EXP_PAGE: DB_VIEW.ENTITY_PROPERTY_VIEW + "--formulaDetailsPage",
  GV_RESTRICTED_MEASURE_PAGE: DB_VIEW.ENTITY_PROPERTY_VIEW + "--restrictedMeasureDetailsPage",
  GV_MEASURES_PANEL_TITLE: DB_VIEW.ENTITY_PROPERTY_VIEW + "--MeasuresPanelTitle",
  GV_COUNT_DISTINCT_PAGE: DB_VIEW.ENTITY_PROPERTY_VIEW + "--countDistinctDetailsPage",
  GV_COUNT_DISTINCT_REFERENCE_ATTRIBUTES:
    DB_VIEW.ENTITY_PROPERTY_VIEW + "--countDistinctDetailsContainer--referenceAttributes",
  GV_EXCEPTION_AGGREGATION_PAGE: DB_VIEW.ENTITY_PROPERTY_VIEW + "--exceptionAggregationDetailsPage",
  OUTPUT_MSGTRIP_DROPDISALLOWED: DB_VIEW.OUTPUT + "--EntityPropertiesView--dropDisallowedMsgTripId",
  DBH_DEL_CONFIRM_DIALOG: DB_VIEW.DBH + "--deleteObjectConfirmDialog",
  DB_SPACE_SELECTION_MSGSTRIP: "shellMainContent---databuilderComponent---spaceSelection--spaceSelectionMsgStrip",
  DB_SPACE_SELECTION: "shellMainContent---databuilderComponent---spaceSelection--spaceListHeader",
  DB_SPACE_LANDINGPAGE_MSGSTRIP:
    "shellMainContent---databuilderComponent---databuilderLandingPage--entitySelectionLandingPage--landingPageMsgStrip",
  DB_WORKBENCH_MSGSTRIP: DB_VIEW.DBWB + "--dbuilderWorkbenchMsgStrip",
  DBH_NEWTBALE_TILE: DB_VIEW.DBH + "--createTableTile",
  DBH_IMPORTCSV_TILE: DB_VIEW.DBH + "--uploadDataTile",
  DBH_NEWGRPHICALVIEW_TILE: DB_VIEW.DBH + "--createGraphicalViewTile",
  DBH_NEWSQLVIEW_TILE: DB_VIEW.DBH + "--createSQLViewTile",
  DBH_NEWERMODEL_TILE: DB_VIEW.DBH + "--createERModelTile",
  DBH_NEWRF_TILE: DB_VIEW.DBH + "--createReplicationFlowTile",
  DBH_NEWTF_TILE: DB_VIEW.DBH + "--createTransformationFlowTile",
  DBH_NODATA_CONTENT: DB_VIEW.DBH + "--unifiedlandingnodata-content",
  ER_IMPACT_ERROR_VBOX: DB_VIEW.ROOT + "sap-cdw-components-ermodeler-view-ImpactErrors--dialog--view--vBox",
  ER_ADD_RELATED_DIALOG: DB_VIEW.ROOT + "suggestDialog",
  SELECT_ASSOCIATION_DIALOG: DB_VIEW.ROOT + "associationDialog",
  COPY_ASSOCIATION_DIALOG: DB_VIEW.ROOT + "copyFromSourceAssociationsDialog",
  ASSOCIATION_SEARCH: DB_VIEW.ROOT + "searchAssociation",
  ASSOCIATION_TARGET_TYPEFILTER: DB_VIEW.ROOT + "targetType",
  TE_EXPORT_CSN: AB_CONTROL.EXPORT_CSN,
  TE_HIERARCHY_DIALOG: AB_VIEW.GENWORKBENCH + "--hierarchyDialog",
  SQL_SAVE_VIEW_DIALOG: AB_CONTROL.SAVE_MODEL_DIALOG,
  SQL_EDITOR: "div.view-line",
  SQL_DUPLICATE_VIEW_ERROR: AB_CONTROL.DUPLICATE_MODEL_ERROR,
  ER_EDITOR_CONTROL: DB_VIEW.ROOT + "erModelerEditor--erModeler--editorControl",
  GV_EDITOR_CONTROL: DB_VIEW.ROOT + "csnQueryBuilderEditor--csnQueryBuilderEditorControl",
  PROPERTIES_LIST_PAGE: DB_VIEW.ER_ENTITY + "--ListPage",
  PROPERTIES_DETAILS_PAGE: DB_VIEW.ER_ENTITY + "--associationDetailsPage",
  TABLE_ASSOCIATION_DETAILS_PAGE: DB_VIEW.TABLE_EDITOR + "--associationDetailsPage",
  TABLE_ASSOCIATION_LIST_PAGE: DB_VIEW.TABLE_EDITOR + "--ListPage",
  PREVIEW_SQL_DIALOG: DB_VIEW.ROOT + "csnQueryBuilderEditor--PreviewSQLDialog",
  ER_REMOTE_OBJECTS_SELECTOR_MSGSTRIP: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--askForFilterMS",
  ER_REMOTE_OBJECTS_SELECTOR_REMOTE_LABEL: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--remoteLabel",
  GV_CALCULATED_PROP_CASE_BOX: DB_VIEW.GV_CALCULATED + "--hboxCaseExpressions",
  GV_FILTER_PROP_CASE_BOX: DB_VIEW.GV_FILTER + "--hboxCaseExpressions",
  BROWSER_WRN_ERR_ICON: DB_VIEW.DBWB + "--TreeView--warnErrorIcon",
  TREEVIEW_MODELTREE: DB_VIEW.DBWB + "--TreeView--modelTree",
  TREEVIEW_REMOTETREE: DB_VIEW.DBWB + "--TreeView--remoteTree",
  TREEVIEW_NONREPOTREE: DB_VIEW.DBWB + "--TreeView--othersTab",
  TREEVIEW_SEARCH: DB_VIEW.DBWB + "--TreeView--nonRepoTypeSelectForSearch",
  TE_ASSO_BUSY: "tableEditor--associationsList-busyIndicator",
  PP_BUSY:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel-busyIndicator",
  REPO_TREE_ITEM: AB_VIEW.BROWSER + "--repoTreeItem-" + DB_VIEW.DBWB + "--TreeView--modelTree",
  GV_CALCULATED_GEO_LATITUDE_SELECT: DB_VIEW.GV_CALCULATED + "--latitudeSelect",
  GV_CALCULATED_GEO_LONGITUDE_SELECT: DB_VIEW.GV_CALCULATED + "--longitudeSelect",
  ER_MODEL_CONTEXTS_PANEL: DB_VIEW.ER_MODEL_PRO + "--ContextsPanelTitle",
  ER_MODEL_SIMPLETYPES_PANEL: DB_VIEW.ER_MODEL_PRO + "--SimpleTypesPanelTitle",
  DF_PALETTE: DB_VIEW.DF_EDITOR_CONTROL + "--df-palette-container",
  DF_JOIN_ADD_DIALOG: DB_VIEW.DF_ADD_COLUMN_DIALOG,
  DF_PROJECTION_HELPER_FUNC_POPOVER: "expressionHelperPopover-popover",
  DF_UNION_MAPPING: DB_VIEW.DF_UNION_PROPERTIES + "--unionMapping",
  IL_OPERATORS_SHELL: "intelligentLookupEditor--il-palette-container",
  ILT_PALETTE: DB_VIEW.ILT_EDITOR + "--il-palette-container",
  VALIDATION_MESSAGE: DB_VIEW.DF_PROJECTION_PROPERTIES + "--filterExpression--statusMessageStrip",
  PARAMETER_DIALOG: "parametersEditor--parametersDialog",
  ANALYTIC_PARAMETER_DIALOG: "analyticParametersEditor--parametersDialog",
  PARAMETERS_MAPPING_DIALOG: "sap-cdw-components-csnquerybuilder-view-ParameterMapping--dialog",
  PARAMETER_DEFAULTVAL_DIALOG: "defaultValDialog",
  DF_NODE_OBJECT_DIALOG: DB_VIEW.DF_SOURCE_FILTERS_DIALOG,
  DF_NODE_FILTERS_DIALOG_MSG_STRIP: DB_VIEW.DF_SOURCE_FILTERS_DIALOG + "--view--filterMessageStrip",
  MDM_EDITOR: AB_CONTROL.MDM_EDITOR,
  MDM_EDITOR_REOPEN: AB_CONTROL.MDM_EDITOR_REOPEN,
  DF_NODE_PROPERTIES_UPDATE_KEY_CHKBOX: DB_VIEW.DF_NODE_PROPERTIES + "--upsertModeSelector",
  DF_EXECUTION_VIEW_DETAILS: DB_VIEW.DF_MODEL_PROPERTIES + "--viewDetails",
  DF_EXECUTION_ERROR_DIALOG: "executionErrorDetailsMsgBox",
  DF_FAILED_EXECUTION_DETAILS_LINK: DB_VIEW.DF_MODEL_PROPERTIES + "--viewFailedDetails",
  DF_FAILED_EXECUTION_MSGBOX: "failedExecutionMsgBox",
  DF_NODE_EXCEL_HEADER_CHECKBOX: "nodeProperties--excelHeader",
  DF_AUTO_RESTART_RUN_CHECKBOX: DB_VIEW.DF_MODEL_PROPERTIES + "--autoRestartCheck",
  DF_VALIDATIONS_DIALOG_LIST_ID:
    "sap-cdw-components-abstractbuilder-view-validations--dialog--view--validations--listlistPage-cont",
  GV_ANALYTIC_MEASURE_ELEMENTS_GRID_LIST_ITEM0:
    DB_VIEW.ENTITY_PROPERTY_VIEW +
    "--analyticalMeasureItem-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--analyticalMeasuresGridList-0",
  TARGET_DELETE_COL_INFOTEXT: DB_VIEW.DF_NODE_PROPERTIES + "--DeleteModeInfoText",
  REFRESH_CONFIRM_DIALOG: "refreshConfirmDialog",
  GV_ANALYTIC_MEASURE_ELEMENTS_GRID_LIST_ITEM1:
    DB_VIEW.ENTITY_PROPERTY_VIEW +
    "--analyticalMeasureItem-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--analyticalMeasuresGridList-1",
  GV_EXCEPTION_AGGREGATION_CONTAINER: DB_VIEW.ENTITY_PROPERTY_VIEW + "--exceptionAggregationContainer",
  GV_ANALYTIC_MEASURE:
    DB_VIEW.ENTITY_PROPERTY_VIEW +
    "--analyticalMeasureItem-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--AnalyticalMeasures",
  VIEW_PARTITION_MENU: "taskLog--partitionMenu",
  VIEW_PARTITION_DIALOG: DB_VIEW.DI_COMP + "---taskLog--partitionDialog",
  REMOTE_TBL_PARTITION_DIALOG: DB_VIEW.DI_COMP + "---taskLog--remote--partitionDialog",
  TC_EXECUTION_VIEW_DETAILS: DB_VIEW.TC_MODEL_PROPERTIES + "--viewDetails",
  MDM_P13_DIALOG: "mdmCustomizeColumnsDialog",
  IMPORT_CONNECTION_ERROR: DB_VIEW.ROOT + "importConnectErrorMsgbox",
  IMPORT_WIZARD: "importWizardView--wizardDialog",
  DF_JOIN_ADVANCED_PROP_PANEL_TITLE: DB_VIEW.DF_JOIN_PROPERTIES + "--joinPropertiesTitle",
  ERROR_DIALOG: "deployErrorMessageBox",
  DF_JOIN_ADD_COLUMN_SEARCH: DB_VIEW.DF_ADD_COLUMN_DIALOG + "--view--joinAddColumnsSearchField",
  RF_WELCOME_PANEL: DB_VIEW.RF_EDITOR + "--replicationFlowWelcomeTextPanel",
  RF_PREMIUM_INBOUND_WARMING_STRIP: DB_VIEW.RF_EDITOR + "--premiumInBoundWarningStrip",
  RF_DETAILS_MONITORING_SCREEN: "shellMainContent---dataIntegrationComponent---replicationflowdetails",
  RF_SOURCE_DATASET_TOOLTIP: DB_VIEW.RF_EDITOR + "--sourceInfoPopover--datasetInfoPopover",
  DF_REMOTE_TABLE_MESSAGE_STRIP: DB_VIEW.DF_NODE_PROPERTIES + "--remoteTargetCreationMessage",
  TRANSFORMATION_DIALOG: "sap-cdw-components-replicationflow-view-AddTransformationDialog--dialog--view",
  TRANSFORMATION_DIALOG_ID: "sap-cdw-components-replicationflow-view-AddTransformationDialog--dialog",
  RF_SOURCE_CONTAINER_ID_EMPTY: "replicationFlowBuilder--sourceContainerEmpty",
  RF_TARGET_CONTAINER_ID_EMPTY: "replicationFlowBuilder--targetContainerEmpty",
  RF_TRANSFORMATION_FILTER: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--rfFilter",
  RF_TRANSFORMATION_MAPPING: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--rfMapping",
  RF_CANVAS_MAIN_TAB: DB_VIEW.RF_EDITOR + "--transformSettingTabHeader",
  RF_CANVAS_TRANSFORM_TAB: DB_VIEW.RF_EDITOR + "--transformTab-text",
  RF_CANVAS_TRANSFORM_TOKEN_PREFIX:
    DB_VIEW.RF_EDITOR + "--rfEditorTransform-replicationFlowBuilder--replicationFlowMainTable-",
  RF_CANVAS_TRANSFORM_TOKEN_FILTERTEXT_PREFIX:
    DB_VIEW.RF_EDITOR + "--filterCountText-replicationFlowBuilder--replicationFlowMainTable-",
  RF_CANVAS_TRANSFORM_TOKEN_FILTERICON_PREFIX:
    DB_VIEW.RF_EDITOR + "--filter-replicationFlowBuilder--replicationFlowMainTable-",
  RF_CANVAS_TRANSFORM_TOKEN_MAPPINGTEXT_PREFIX:
    DB_VIEW.RF_EDITOR + "--mappingCountText-replicationFlowBuilder--replicationFlowMainTable-",
  RF_CANVAS_TRANSFORM_TOKEN_MAPPINGICON_PREFIX:
    DB_VIEW.RF_EDITOR + "--mapping-replicationFlowBuilder--replicationFlowMainTable-",
  RF_OBJECT_STATUS: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--replicationObjectStatus",
  // "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--replicationflow-properties-ReplicationObjectProperties--replicationObjectStatus",
  RF_CANVAS_TRANSFORM_TOKEN_DEL_PREFIX:
    DB_VIEW.RF_EDITOR + "--deleteTransformationButton-replicationFlowBuilder--replicationFlowMainTable-",
  RF_TRANSFORMATION_DIALOG_ICONTAB_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--transformationTab",
  RF_TRANSFORMATION_DIALOG_MAPPING_TAB: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--rf-td-mapping-tab",
  RF_TRANSFORMATION_DIALOG_FILTER_TAB: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--rf-td-filter-tab",
  RF_TRANSFORMATION_DIALOG_FILTER_TAB_CONTENT: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--rf-td-filter-tab-main-content",
  RF_TRANSFORMATION_DIALOG_FILTER_TAB_COUNT: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--rf-td-filter-tab-count",
  RF_TRANSFORMATION_DIALOG_INVALID_COLUMNS_POPOVER:
    DB_VIEW.RF_TRANSFORMATION_DIALOG + "--InvalidColsInfoPopover--invalidColumnsPopover",
  RF_TRANSFORMATION_DIALOG_MAPPING_TAB_EXISTING_TABLE_INFO_STRIP:
    DB_VIEW.RF_TRANSFORMATION_DIALOG + "--beforeDeploymentMessageForAddColumn",
  RF_CONTAINER_SEARCH_ID: "containerSearch-I",
  RF_SLT_SELECTION_MESSAGE_ID: "containerInfoStrip-info",
  RF_VALIDATIONS_DIALOG_ID: "sap-cdw-components-abstractbuilder-view-validations--dialog",
  RF_VALIDATIONS_DIALOG_LIST_ID:
    "sap-cdw-components-abstractbuilder-view-validations--dialog--view--validations--listlistPage-cont",
  RF_SAVE_MODEL_DIALOG_ID: "saveModelDialog",
  RF_CLAMPING_DATA_LABEL: DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--clampingLbl",
  RF_INCOMPATIBLE_FAIL_LABEL: DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--failOnIncompatibleLbl",
  RF_MAX_PARTITION_LABEL: DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--maxpartitionInputLbl",
  RF_INCLUDE_SUBFOLDER: DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--includeSubFolderLbl",
  RF_INCLUDE_SUBFOLDER_SFTP: DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--sftpIncludeText",
  RF_UNSUPPORTED_DATASET_NO_KEYS_ONLY_DIALOG_ID: "unsupportedDatasetWarningNoKeys",
  RF_DWC_UNSUPPORTED_DATASET_NO_KEYS_ONLY_DIALOG_ID: "unsupportedDatasetErrorNoKeysDWC",
  RF_UNSUPPORTED_SAC_ARTEFACT_ERROR_DIALOG: "unsupportedDatasetFromSAC",
  RF_RENAME_TO_UNSUPPORTED_SAC_ARTEFACT_ERROR_DIALOG: "renameToUnsupportedDatasetFromSAC",
  RF_MAP_TO_EXISTING_TO_UNSUPPORTED_SAC_ARTEFACT_ERROR_DIALOG: "mapToExistToUnsupportedDatasetFromSAC",
  RF_UNSUPPORTED_DATASET_EXTRACTION_DISABLED_DIALOG_ID: "unsupportedDatasetWarningExtractionDisabled",
  RF_UNSUPPORTED_DATASET_NO_KEYS_AND_EXTRACTION_DISABLED_DIALOG_ID: "allUnsupportedDatasetWarning",
  RF_PATH_CONTAINER_DIALOG_ID: "containerDialog",
  RF_TRANSFORMATION_VALIDATIONS_POPOVER_ID: "transformationValidationPopover-messageView-navContainer",
  RF_PROJECTION_IN_MAIN_CANVAS:
    "replicationFlowBuilder--rfEditorTransform-replicationFlowBuilder--replicationFlowMainTable-0",
  RF_SETTING_BUTTON: DB_VIEW.RF_EDITOR + "--settingTab",
  RF_DEPLOY_VALIDATION_DIALOG_ID: "deployRunningReplicationFlowErrorMsgBox",
  RF_DEPLOY_INVALID_DWC_TARGET_DIALOG_ID: "deployInValidDWCTargetDeltaCaptureObjectMsgBox",
  RF_UNSUPPORTED_DWC_DATASET_DIALOG_ID: "renameToUnsupportedDatasetFromDWCMsgBox",
  RF_DELTACAPTURE_VALIDATION_DIALOG_ID: "sourceNonDeltaSupportErrorMsgbox",
  RF_DELTACAPTURE_SOURCE_NON_DELTA_VALIDATION_DIALOG_ID: "sourceObjectNonDeltaSupportErrorMainEditorMsgbox",
  RF_DELTACAPTURE_SOURCE_NON_DELTA_ERROR_DIALOG_ID: "sourceObjectNonDeltaSupportEditorErrorMsgBox",
  RF_HDLF_DELTASHARE_INVALID_TARGET_ERROR_DIALOG_ID: "invalidTargetForHDLF",
  RF_CONNECTION_COMBINATION_ERROR_DIALOG_ID: "connectionCombinationUnsupportedErrorBox",
  RF_SELECT_DATASET_HEADER: "containerDialog-header",
  DF_NODE_ODATA_NAV_PROP_DIALOG: "nodeProperties--odataPropertiesDialog",
  DF_NODE_ODATA_MESSAGE_STRIP: "nodeProperties--odataPropertiesMessage-content",
  DF_NODE_ODATA_WARNING_MSG: DB_VIEW.DF_NODE_PROPERTIES + "--odataWarningMsg",
  TF_VALIDATIONS_DIALOG_LIST_ID:
    "sap-cdw-components-abstractbuilder-view-validations--dialog--view--validations--listlistPage-cont",
  TF_TARGET_MAPPING_PANEL: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--targetMapping",
  RF_CLAMPING_CHKBX: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--clampingChkBx",
  DBL_CREATE_TILES: DB_VIEW.DBHN + "--IconTabBar",
  RF_VALIDATION_POPOVER: "validationsView--validations--list-navContainer",
  IL_COLUMN_POPOVER: DB_VIEW.IL_DIALOG_VIEW + "--column-popover",
  IL_COLUMN_POPOVER_LIST: DB_VIEW.IL_DIALOG_VIEW + "--list",
  IL_INFOBAR: DB_VIEW.IL_DIALOG_VIEW + "--infobar",
  VALIDATION_DIALOG: "validateRemoteTablesDlg--validateRemoteTableDialog",
  RF_SOURCE_DATASET_POPOVER:
    DB_VIEW.RF_EDITOR + "--sourceDatasetInfo-replicationFlowBuilder--replicationFlowMainTable-0",
  TF_GV_CALC_COLUMN_MESSAGESTRIP: DB_VIEW.GV_CALCULATED + "--statusMessageStrip",
  TF_GV_FILTER_MESSAGESTRIP: DB_VIEW.GV_FILTER + "--statusMessageStrip",
  TF_RUNTIME_TEXT: DB_VIEW.TF_MODEL_PROPERTIES + "--runtime",
  RF_OBJECT_TRUNCATE_MESSAGE_STRIP: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--messageStripTruncate",
  TF_CREATE_NEW_COLUMN_DIALOG: "sap-cdw-components-transformationflow-properties-ColumnPropertyEditor--dialog",
  TF_ADD_COLUMN_DIALOG: "scriptColumnSelector--columnSelectDialog-dialog",
  TF_ADD_COLUMN_TABLE: "scriptColumnSelector--columnSelectDialog-table",
  TF__SECONDARY_SOURCE_DELTA_SETTINGS_PANEL: DB_VIEW.TF_SECONDARY_SOURCE_PROPERTIES + "--deltaSettingsPanel",
  RF_SUBJECT_NAME_RESULT: "sourceSchemaDialog--dialog--view--searchField",
  TF_INPUT_PARAMETERS_PANEL: DB_VIEW.TF_MODEL_PROPERTIES + "--ParameterPanelTitle",
  TF_INPUT_PARAMETERS_DIALOG: "tfInputParametersEditor--tfInputParametersDialog",
  TF_PARAMETERS_DETAILS_VIEW: "tfInputParametersEditor--detail-cont",
  TF_PARAMETERS_DATATYPE: "tfInputParametersEditor--datatype",
  DELTA_PANEL_VB: DB_VIEW.ENTITY_PROPERTY_VIEW + "--deltaCapturePanel",
};

export const DB_INPUT = {
  GV_ANALYTIC_MEASURE_COUNT_DISTINCT_ATTRIBUTE:
    DB_VIEW.ENTITY_PROPERTY_VIEW + "--countDistinctDetailsContainer--referenceAttributes",
  BROWSER_SEARCHREPOSITORY_ESH: AB_INPUT.BROWSER_SEARCHREPOSITORY_ESH,
  BROWSER_SEARCHREPOSITORY_ESH_INPUT: AB_INPUT.BROWSER_SEARCHREPOSITORY_ESH_INPUT,
  BROWSER_SEARCHREPOSITORY: AB_INPUT.BROWSER_SEARCHREPOSITORY,
  BROWSER_SEARCHREMOTE: AB_INPUT.BROWSER_SEARCHREMOTE,
  SAVE_DIALOG_BUSINESSNAME: AB_INPUT.SAVE_DIALOG_BUSINESSNAME,
  SAVE_DIALOG_TECHNICALNAME: AB_INPUT.SAVE_DIALOG_TECHNICALNAME,
  ENTITY_BUSINESS_NAME: DB_VIEW.GV_ENTITY + "--bname",
  ENTITY_TECHNICAL_NAME: DB_VIEW.GV_ENTITY + "--technicalNameInput",
  ENTITY_ALIAS: DB_VIEW.GV_ENTITY + "--alias",
  ENTITY_DISTINCT_VALUES: DB_VIEW.GV_ENTITY + "--distinctValues",
  JOIN_DISTINCT_VALUES: DB_VIEW.GV_JOIN + "--distinctValues",
  JOIN_LEFT_CARDINALITY: DB_VIEW.GV_JOIN + "--leftCardinalitySel",
  JOIN_RIGHT_CARDINALITY: DB_VIEW.GV_JOIN + "--rightCardinalitySel",
  GV_FILTER_CODEEDITOR_EXPRESSION: DB_VIEW.GV_FILTER + "--expressionCodeEditor",
  GV_CURRENCY_CONVERSION_CODEEDITOR_EXPRESSION: "referenceDate--expressionCodeEditor",
  GV_CALCULATED_ELEMENTS_CODEEDITOR_EXPRESSION: DB_VIEW.GV_CALCULATED + "--expressionCodeEditor",
  GV_CALCULATED_ELEMENTS_CODEEDITOR_EXPRESSION_RENAME: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--expressionCodeEditor",
  GV_AGGREGATION_CODEEDITOR_EXPRESSION: DB_VIEW.GV_AGGREGATED + "--expressionCodeEditor",
  GV_FORMULA_CODEEDITOR_EXPRESSION: DB_VIEW.GV_FORMULA + "--expressionCodeEditor",
  GV_RESTRICTED_MEASURE_CODEEDITOR_EXPRESSION: DB_VIEW.GV_RESTRICTED_MEASURE + "--expressionCodeEditor",
  GV_FILTER_FUNCSEARCHINPUT: DB_VIEW.GV_FILTER + "--functionsSearchInput",
  GV_CALCULATED_FUNCSEARCHINPUT: DB_VIEW.GV_CALCULATED + "--functionsSearchInput",
  GV_FILTER_COLSEARCHINPUT: DB_VIEW.GV_FILTER + "--columnSearchInput",
  GV_JOIN_MAPPINGS_SEARCHINPUT_LISTA: DB_VIEW.GV_JOIN + "--mappings--listASearchField",
  GV_JOIN_MAPPINGS_SEARCHINPUT_LISTB: DB_VIEW.GV_JOIN + "--mappings--listBSearchField",
  GV_UNION_MAPPINGS_SEARCHINPUT_LISTA: DB_VIEW.GV_UNION + "--mappings--listASearchField",
  GV_UNION_MAPPINGS_SEARCHINPUT_LISTB: DB_VIEW.GV_UNION + "--mappings--listBSearchField",
  GV_CALCULATED_ELEMENTS_FIELDSEARCHINPUT: DB_VIEW.GV_CALCULATED + "--columnSearchInput",
  GV_ENTITY_DEPENDENT_OBJECT_LIST_SEARCH: DB_VIEW.GV_ENTITY + "--searchField",
  GV_OUTPUT_DEPENDENT_OBJECT_LIST_SEARCH: DB_VIEW.OUTPUT + "--searchField",
  ER_SELECT_REMOTE_SOURCE: DB_VIEW.ER_SELECT_REMOTE_SOURCE + "--remoteSourceCombo",
  TF_JOIN_TYPES: DB_VIEW.GV_JOIN + "--joinTypes",
  TE_BUSINESS_NAME: DB_VIEW.TABLE_EDITOR + "--businessNameInput",
  TE_TECHNICAL_NAME: DB_VIEW.TABLE_EDITOR + "--technicalNameInput",
  TE_DELTACAPTURE_NAME: DB_VIEW.TABLE_EDITOR + "--deltaCaptureTableName",
  TE_DELTACAPTURE_NAME_NEW: DB_VIEW.TABLE_EDITOR + "--deltaCaptureTableName_new",
  TE_DELTA_OUTBOUND_FIELD: DB_VIEW.TABLE_EDITOR + "--deltaOutboundfield",
  TE_TABLE_TYPE: DB_VIEW.TABLE_EDITOR + "--dataSetTypeSel",
  TE_TABLE_DESC: DB_VIEW.TABLE_EDITOR + "--busdef",
  TE_TABLE_PURPOSE: DB_VIEW.TABLE_EDITOR + "--buspurp",
  TE_TABLE_CONTACT: DB_VIEW.TABLE_EDITOR + "--buscont",
  TE_TABLE_RESPONSIBLE: DB_VIEW.TABLE_EDITOR + "--responsible",
  TE_TABLE_TAGS: DB_VIEW.TABLE_EDITOR + "--tags",
  TE_TABLE_CONNECTION: DB_VIEW.TABLE_EDITOR + "--rcnx",
  TE_TABLE_REMOTETABLE: DB_VIEW.TABLE_EDITOR + "--rtbl",
  TE_COLUMN_BUSINESS_NAME: DB_VIEW.TABLE_EDITOR + "--columnBusinessName-__clone",
  TE_COLUMN_TECHNICAL_NAME: DB_VIEW.TABLE_EDITOR + "--columnTechnicalName-__clone",
  TE_DEPENDENT_OBJECT_LIST_SEARCH: DB_VIEW.TABLE_EDITOR + "--searchField",
  SELECT_ASSOCIATION_DIALOG_TARGETTYPE: DB_VIEW.ROOT + "targetType",
  GV_PROP_SEARCH_COLUMN: DB_VIEW.GV_CALCULATED + "--columnSearchInput",
  ER_ENTITY_CONNECTION: DB_VIEW.ER_ENTITY + "--connection",
  ER_ENTITY_ORDID: DB_VIEW.ER_ENTITY + "--ordId",
  ER_ENTITY_BUSINESS_NAME: DB_VIEW.ER_ENTITY + "--businessName",
  ER_ENTITY_TECHNICAL_NAME: DB_VIEW.ER_ENTITY + "--technicalNameInput",
  ER_ENTITY_TECHNICAL_NAME_SHARED_OBJECT: DB_VIEW.ER_ENTITY + "--technicalNameInputSharedObject",
  ER_ENTITY_CONTEXT_NAME: DB_VIEW.ER_ENTITY + "--spaceOrContextName",
  GV_ENTITY_CONTEXT_NAME: DB_VIEW.GV_ENTITY + "--spaceOrContextName",
  GV_ENTITY_QUALIFIED_NAME: DB_VIEW.GV_ENTITY + "--qualifiedName",
  TE_ENTITY_CONTEXT_NAME: DB_VIEW.TABLE_EDITOR + "--spaceOrContextName",
  GV_CALCULATED_EXPRESSION: DB_VIEW.GV_CALCULATED + "--expressionTextArea",
  GV_CALCULATED_BUSINESSNAME: DB_VIEW.GV_CALCULATED + "--businessName",
  GV_CALCULATED_TECHNAME: DB_VIEW.GV_CALCULATED + "--technicalName",
  GV_CALCULATED_BUSINESSNAME_R: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--businessName",
  GV_CALCULATED_TECHNAME_R: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--technicalName",
  GV_CALCULATED_LENGTH: DB_VIEW.GV_CALCULATED + "--length",
  ER_MODEL_CONNECTION: DB_VIEW.ER_MODEL_PRO + "--connection",
  ER_MODEL_ORDID: DB_VIEW.ER_MODEL_PRO + "--ordId",
  ER_MODEL_BUSINESS_NAME: DB_VIEW.ER_MODEL_PRO + "--modelDescription",
  ER_MODEL_TECHNICAL_NAME: DB_VIEW.ER_MODEL_PRO + "--modelPropertiesName",
  ER_MODEL_PRO_TABLES_FILTER: DB_VIEW.ER_MODEL_PRO + "--filterModelObjects",
  ER_ENTITY_FILTER_ATTRS: DB_VIEW.ER_ENTITY + "--filterBusinessAttributes",
  ER_ENTITY_RESPONSIBLETEAM_VHDIALOG_SEARCH_FIELD: DB_VIEW.ER_ENTITY + "--responsibleTeamVHDialog--dialog-searchField",
  ER_NEW_ASSOCIATION_PRO_LISTASFL: DB_VIEW.ER_ENTITY + "--joinPropertiesDialogView--mappings--listASearchField",
  IMPORTCSNDIALOG_FILEUPLOADER: DB_VIEW.ROOT + "importCsnDialog--view--fileUploader",
  GV_PROJECTION_COLUMNS_SEARCH_FIELD: DB_VIEW.GV_PROJECTION + "--listASearchField",
  GV_CALCULATED_COLUMNS_SEARCH_FIELD: DB_VIEW.GV_CALCULATED + "--listASearchField",
  DATA_PREVIEW_FILTER: DB_VIEW.ROOT + AB_INPUT.DATA_PREVIEW_FILTER,
  DATA_PREVIEW_FILTER1: DB_VIEW.ROOT + AB_INPUT.DATA_PREVIEW_FILTER1,
  DATA_PREVIEW_COLUMNS_CONTROL_DIALOG_SEARCH_FIELD: "columnsControlTable-searchField",
  HIERARCHY_DESCRIPTION_INPUT: DB_VIEW.HIERARCHY_VIEW + "--hierarchyDescriptionInput",
  HIERARCHY_NAME_INPUT: DB_VIEW.HIERARCHY_VIEW + "--hierarchyNameInput",
  EXTERNAL_HIERARCHY_DESCRIPTION_INPUT: DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--exhierarchyDescriptionInput",
  EXTERNAL_HIERARCHY_NAME_INPUT: DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--exhierarchyNameInput",
  EXTERNAL_HIERARCHY_SOURCE_NAME: DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--externalHierarchyName",
  EXTERNAL_HIERARCHY_SOURCE_PARENT: DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--externalHierarchyParentCol",
  EXTERNAL_HIERARCHY_SOURCE_CHILD: DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--externalHierarchyChildCol",
  TE_HIERARCHY_DESCRIPTION_INPUT: DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--hierarchyDescriptionInput",
  TE_HIERARCHY_NAME_INPUT: DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--hierarchyNameInput",
  GV_EXTERNAL_HIERARCHY_DESCRIPTION_INPUT: DB_VIEW.HIERARCHY_VIEW + "--exhierarchyDescriptionInput",
  GV_EXTERNAL_HIERARCHY_NAME_INPUT: DB_VIEW.HIERARCHY_VIEW + "--exhierarchyNameInput",
  GV_EXTERNAL_HIERARCHY_SOURCE_NAME: DB_VIEW.HIERARCHY_VIEW + "--externalHierarchyName",
  GV_EXTERNAL_HIERARCHY_SOURCE_NAME_NEW: DB_VIEW.HIERARCHY_VIEW + "--externalHierarchyNameMulti",
  GV_EXTERNAL_HIERARCHY_SOURCE_PARENT: DB_VIEW.HIERARCHY_VIEW + "--externalHierarchyParentCol",
  GV_EXTERNAL_HIERARCHY_SOURCE_PARENT_NEW:
    DB_VIEW.HIERARCHY_VIEW +
    "--externalHierarchyPareantColMultiNode-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--hierarchyDialogView--parentElementMultinode-0",
  GV_EXTERNAL_HIERARCHY_SOURCE_CHILD_NEW:
    DB_VIEW.HIERARCHY_VIEW +
    "--externalHierarchyChildColMultiNode-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--hierarchyDialogView--childElementMultiNode-0",
  GV_EXTERNAL_HIERARCHY_SOURCE_CHILD: DB_VIEW.HIERARCHY_VIEW + "--externalHierarchyChildCol",
  SQL_DEPLOY_VIEW_NAME: DB_VIEW.ROOT + "saveModelDialog--nameInput",
  FILTER_ATTRIBUTE_TABLE:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--editAttributesDialogView--filterAttributesButton",
  GV_RENAME_BUSINESS_NAMEINPUT: "changeNameDialog--nameInput",
  REMOTE_TABLE_SEARCH_INPUT: "importWizardView--objectsView--objectListSearch",
  GV_RENAME_TECHNICAL_NAMEINPUT: "changeNameDialog--technicalnameInput",
  GV_ASSOCIATION_BUSINESS_NAME: DB_VIEW.OUTPUT + "--EntityPropertiesView--associationBName",
  GV_ASSOCIATION_TECHNICAL_NAME: DB_VIEW.OUTPUT + "--EntityPropertiesView--associationTName",
  SELECT_TARGET_SEARCH: DB_VIEW.ROOT + "searchField",
  GV_ASSOCIATION_FROM: DB_VIEW.OUTPUT + "--EntityPropertiesView--fromAssociation",
  GV_ASSOCIATION_TO: DB_VIEW.OUTPUT + "--EntityPropertiesView--toAssociation",
  PREVIEW_SQL_EDITOR: DB_CONTROL.PREVIEW_SQL_DIALOG + "--previewSQLEditor",
  TE_FILTER_MEASURE_TABLE: DB_VIEW.TABLE_EDITOR + "--filterMeasures",
  TE_FILTER_ATTRIBUTE_TABLE: DB_VIEW.TABLE_EDITOR + "--filterAttributesButton",
  TE_FILTER_ASSOCIATIONS_TABLE: DB_VIEW.TABLE_EDITOR + "--searchAssociationsButton",
  ER_REMOTE_OBJECTS_SELECTOR_SOURCE_SEARCH: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--sourceSearch",
  DF_PROJECTION_COLUMNS_SEARCH_FIELD: DB_VIEW.DF_PROJECTION_PROPERTIES + "--listASearchField",
  DF_COLUMN_NAME: DB_VIEW.DF_PROJECTION_PROPERTIES + "--colName",
  DF_UNION_NODE_LABEL: DB_VIEW.DF_UNION_PROPERTIES + "--label",
  DF_FILTER_EXPRESSION_EDITOR: DB_VIEW.DF_PROJECTION_PROPERTIES + "--filterExpression--expressionCodeEditor",
  DF_CALC_EXPRESSION_EDITOR: DB_VIEW.DF_PROJECTION_PROPERTIES + "--calcColumnExpression--expressionCodeEditor",
  DF_SCRIPT_COLUMNS_SEARCH_FIELD: DB_VIEW.DF_SCRIPT_PROPERTIES + "--listASearchField",
  DF_SCRIPT_TEXTAREA: DB_VIEW.DF_SCRIPT_PROPERTIES + "--scriptTextarea",
  DF_COLUMN_PROP_EDITOR_COLUMN_NAME:
    "sap-cdw-components-dataflowmodeler-properties-ColumnPropertyEditor--dialog--view--columnName",
  DF_COLUMN_PROP_EDITOR_COLUMN_LENGTH:
    "sap-cdw-components-dataflowmodeler-properties-ColumnPropertyEditor--dialog--view--columnLength",
  DF_COLUMN_PROP_EDITOR_COLUMN_SCALE:
    "sap-cdw-components-dataflowmodeler-properties-ColumnPropertyEditor--dialog--view--columnScale",
  DF_COLUMN_PROP_EDITOR_COLUMN_PRECISION:
    "sap-cdw-components-dataflowmodeler-properties-ColumnPropertyEditor--dialog--view--columnPrecision",
  DF_PROJECTION_LENGTH: DB_VIEW.DF_PROJECTION_PROPERTIES + "--length",
  DF_PROJECTION_PRECISION: DB_VIEW.DF_PROJECTION_PROPERTIES + "--precision",
  DF_PROJECTION_SCALE: DB_VIEW.DF_PROJECTION_PROPERTIES + "--scale",
  DF_PRJECTION_FILTER_FUNCTION_SEARCH: DB_VIEW.DF_PROJECTION_PROPERTIES + "--filterExpression--functionsSearchInput",
  DF_PRJECTION_FILTER_COLUMN_SEARCH: DB_VIEW.DF_PROJECTION_PROPERTIES + "--filterExpression--columnSearchInput",
  DF_PROJECTION_INPUT_PARAMETERS_SEARCH:
    DB_VIEW.DF_PROJECTION_PROPERTIES + "--filterExpression--inputParametersSearchInput",
  DF_SCRIPT_CODE_EDIT: DB_VIEW.DF_SCRIPT_PROPERTIES_OBJECT_PAGE + "--scriptCodeEditor",
  DBH_SEARCH_ENTITY: DB_VIEW.DBH + "--filterEntities",
  DBH_UPLOAD_CSNFILE: DB_VIEW.DBH + "--importFileUploader",
  SQL_EDIT_DATATYPE_LENGTH: DB_VIEW.ER_ENTITY + "--editAttributesDialogView--Popover-content--lengthInput",
  SQL_EDIT_DATATYPE_SELECT: DB_VIEW.ER_ENTITY + "--editAttributesDialogView--Popover-content--dataTypeSelect",
  IP_EDIT_DATATYPE_SELECT: "parametersEditor--datatype",
  DF_MODEL_BUSINESS_NAME: DB_VIEW.DF_MODEL_PROPERTIES + "--businessName",
  DF_MODEL_TECHNICAL_NAME: DB_VIEW.DF_MODEL_PROPERTIES + "--technicalName",
  GV_AGGREGATION_NAME: DB_VIEW.GV_AGGREGATED + "--nodeRenameId",
  GV_NEW_AGGREGATION_NAME: DB_VIEW.GV_RENAME_AGGREGATED + "--nodeRenameId",
  GV_NEW_FILTER_NAME: DB_VIEW.GV_FILTER + "--nodeRenameId",
  DF_NODE_BUSINESS_NAME: DB_VIEW.DF_NODE_PROPERTIES + "--businessName",
  DF_NODE_TECHNICAL_NAME: DB_VIEW.DF_NODE_PROPERTIES + "--technicalName",
  DF_PROJECTION_LABEL: DB_VIEW.DF_PROJECTION_PROPERTIES + "--label",
  DF_TARGET_TABEL_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--label",
  DF_NODE_CSV_ESC_CHAR: "nodeProperties--escapeCharacter",
  DF_NODE_COLUMNS_SEARCH_FIELD: DB_VIEW.DF_NODE_PROPERTIES + "--listASearchField",
  DF_UNION_MAPPINGS_SEARCHINPUT_LISTA: DB_VIEW.DF_UNION_PROPERTIES + "--mappings--listASearchField",
  DF_UNION_MAPPINGS_SEARCHINPUT_LISTB: DB_VIEW.DF_UNION_PROPERTIES + "--mappings--listBSearchField",
  DF_UNION_MAPPINGS_SEARCHINPUT_LISTA_RESET: DB_VIEW.DF_UNION_PROPERTIES + "--mappings--listASearchField-reset",
  DF_UNION_MAPPINGS_SEARCHINPUT_LISTB_RESET: DB_VIEW.DF_UNION_PROPERTIES + "--mappings--listBSearchField-reset",
  DF_NODE_LOCAL_SCHEMA_NAME: DB_VIEW.DF_NODE_PROPERTIES + "--localSchemaName",
  DF_NODE_SPACE_NAME: DB_VIEW.DF_NODE_PROPERTIES + "--spaceId",
  DF_NODE_TYPE: DB_VIEW.DF_NODE_PROPERTIES + "--type",
  DF_SCHEMA_TECHNICAL_NAMEINPUT: "saveModelDialog--technicalnameInput",
  DF_SCHEMA_TECHNICAL_NAME: DB_VIEW.DF_NODE_PROPERTIES + "--technicalName",
  DF_IMPORT_CONNECTION_NAME: DB_VIEW.DF_NODE_PROPERTIES + "--tbConnectionName",
  DF_UNION_COLUMNS_SEARCH_FIELD: DB_VIEW.DF_UNION_PROPERTIES + "--listASearchField",
  DF_NODE_CHAR_SET_SELECT: "nodeProperties--characterSetList",
  PARAMETER_DEFAULT_STRINPUT: "stringInput",
  PARAMETER_DEFAULT_INTINPUT: "intInput",
  PARAMETER_DEFAULT_DECINPUT: "decInput",
  GV_PARAMETER_NAME_IN_DIALOG: "parametersEditor--businessName",
  GV_PARAMETER_TECH_IN_DIALOG: "parametersEditor--technicalName",
  GV_PARAMETER_DATATYPE_IN_DIALOG: "parametersEditor--datatype",
  GV_PARAMETER_LENGTH_IN_DIALOG: "parametersEditor--length",
  GV_PARAMETER_PRECISION_IN_DIALOG: "parametersEditor--precision",
  GV_PARAMETER_SCALE_IN_DIALOG: "parametersEditor--scale",
  GV_PARAMETER_STR_DEFAULT_IN_DIALOG: "parametersEditor--stringDefaultValue",
  GV_PARAMETER_INT_DEFAULT_IN_DIALOG: "parametersEditor--intDefaultValue",
  GV_PARAMETER_DEC_DEFAULT_IN_DIALOG: "parametersEditor--decDefaultValue",
  GV_PARAMETER_DATE_DEFAULT_IN_DIALOG: "parametersEditor--dateDefaultVal",
  GV_PARAMETER_BOOL_DEFAULT_IN_DIALOG: "parametersEditor--booleanSelect",
  GV_ANALYTIC_PARAMETER_NAME_IN_DIALOG: "analyticParametersEditor--businessName",
  GV_ANALYTIC_PARAMETER_TECH_IN_DIALOG: "analyticParametersEditor--technicalName",
  GV_ANALYTIC_PARAMETER_REFCOLUMN_IN_DIALOG: "analyticParametersEditor--refenceColInput",
  GV_ANALYTIC_PARAMETER_DATATYPE_IN_DIALOG: "analyticParametersEditor--datatype",
  GV_ANALYTIC_PARAMETER_LENGTH_IN_DIALOG: "analyticParametersEditor--length",
  GV_ANALYTIC_PARAMETER_PRECISION_IN_DIALOG: "analyticParametersEditor--precision",
  GV_ANALYTIC_PARAMETER_SCALE_IN_DIALOG: "analyticParametersEditor--scale",
  DF_NODE_JSON_FLATTEN_LEVEL_INPUT: "nodeProperties--jsonFlattenLevel",
  DF_NODE_FILTER_TEXT_INPUT_LOW:
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--textInputValueLow-" +
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--filterValues-",
  DF_NODE_FILTER_DATE_INPUT_LOW:
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--dateDefaultValLow-" +
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--filterValues-",
  DF_NODE_FILTER_DATETIME_INPUT_LOW:
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--datetimeDefaultValLow-" +
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--filterValues-",
  DF_NODE_FILTER_TEXT_INPUT_HIGH:
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--textInputValueHigh-" +
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--filterValues-",
  CROSS_SPACE_SHARE_DIALOG_INPUT: DB_VIEW.CROSS_SPACE_SHARE_DIALOG + "--spacesMultiInput",
  TE_FILTER_LOWVAL: DB_VIEW.TABLE_EDITOR + "--textInputValueLow-tableEditor--filterValues-",
  TE_FILTER_HIGHVAL: DB_VIEW.TABLE_EDITOR + "--textInputValueHigh-tableEditor--filterValues-",
  TE_DATA_ACCESS: DB_VIEW.TABLE_EDITOR + "--rstat",
  IL_INPUT_COLUMNS_SEARCH_FIELD: DB_VIEW.ILT_INPUT_PROPERTIES + "--listASearchField",
  IL_LOOKUP_COLUMNS_SEARCH_FIELD: DB_VIEW.ILT_LOOKUP_PROPERTIES + "--listASearchField",
  IL_DEFAULT_VALUE_TEXT: DB_VIEW.ILT_OUTPUT_PROPERTIES + "--ColDefaultValuePopover--defaultValueInput",
  IL_DEFAULT_VALUE_DATE: DB_VIEW.ILT_OUTPUT_PROPERTIES + "--ColDefaultValuePopover--defaultValueDatePicker",
  IL_DEFAULT_VALUE_SELECT: DB_VIEW.ILT_OUTPUT_PROPERTIES + "--ColDefaultValuePopover--defaultValueSelect",
  DF_FETCH_SIZE_FIELD: DB_VIEW.DF_NODE_PROPERTIES + "--fetchSize",
  DF_CHUNK_SIZE_FIELD: DB_VIEW.DF_NODE_PROPERTIES + "--chunkSize",
  DF_CHUNK_WEBSERVICE_DATA_RECEIVE_TIMEOUT_FIELD: DB_VIEW.DF_NODE_PROPERTIES + "--webServiceDataReceiveTimeout",
  DF_BATCH_SIZE_FIELD: DB_VIEW.DF_NODE_PROPERTIES + "--batchSize",
  TC_TECHNICAL_NAME: DB_VIEW.TC_MODEL_PROPERTIES + "--technicalName",
  TC_BUSINESSNAME: DB_VIEW.TC_MODEL_PROPERTIES + "--businessName",
  TC_NODE_STATUS: DB_VIEW.TC_NODE_PROPERTIES + "--objectStatusText",
  TC_NODE_BUSINESSNAME: DB_VIEW.TC_NODE_PROPERTIES + "--businessName",
  TC_OPERATION_BUSINESSNAME: DB_VIEW.TC_OPERATION_PROPERTIES + "--operatorName",
  TC_SEARCH_FIELD: DB_VIEW.TC_MODEL_PROPERTIES + "--taskChainSearchField",
  TC_NODE_RETENTIONDAYS: DB_VIEW.TC_NODE_PROPERTIES + "--inputNumberOfRetentionDays",
  TC_NODE_ACTIVITYTEXT: DB_VIEW.TC_NODE_PROPERTIES + "--activityText",
  TC_NODE_STORAGETYPETEXT: DB_VIEW.TC_NODE_PROPERTIES + "--storageTypeText",
  TC_INPUT_PARAMETER_DIALOG: DB_VIEW.TC_INPUT_PARAMETERS_DIALOG + "--view--IPTable-rows-row",
  TC_NODE_DESCRIPTION: DB_VIEW.TC_NODE_PROPERTIES + "--nrDescription",
  TC_NODE_APACHE_CUSTOM_INPUT: DB_VIEW.TC_NODE_PROPERTIES + "--apacheSparkCustomInput",
  TC_API_NODE_TECHNICAL_NAME: DB_VIEW.TC_API_NODE_PROPERTIES + "--technicalName",
  TC_API_NODE_CONNECTION: DB_VIEW.TC_API_NODE_PROPERTIES + "--connectionList",
  TC_API_NODE_CONNECTION_ERRORBTN: DB_VIEW.TC_API_NODE_PROPERTIES + "--connectionErrorBtn",
  TC_API_NODE_INVOKE_URL: DB_VIEW.TC_API_NODE_PROPERTIES + "--url",
  TC_API_NODE_INVOKE_APIPATH: DB_VIEW.TC_API_NODE_PROPERTIES + "--apiPath",
  TC_API_NODE_INVOKE_REQUESTBODY: DB_VIEW.TC_API_NODE_PROPERTIES + "--requestBody",
  TC_API_NODE_INVOKE_REQUESTBODY_ERROR: DB_VIEW.TC_API_NODE_PROPERTIES + "--requestBodyError",
  TC_API_NODE_INVOKE_FORMATBTN: DB_VIEW.TC_API_NODE_PROPERTIES + "--btnFormat",
  TC_API_NODE_INVOKE_DIALOGBTN: DB_VIEW.TC_API_NODE_PROPERTIES + "--openEditorDialog",
  TC_API_NODE_INVOKE_MODE: DB_VIEW.TC_API_NODE_PROPERTIES + "--mode",
  TC_API_NODE_INVOKE_CSRFTOKEN_SWITCH: DB_VIEW.TC_API_NODE_PROPERTIES + "--CSRFToken",
  TC_API_NODE_INVOKE_CSRFTOKEN_INPUT: DB_VIEW.TC_API_NODE_PROPERTIES + "--CSRFtokenURL",
  TC_API_NODE_INVOKE_JOBPATH: DB_VIEW.TC_API_NODE_PROPERTIES + "--responseId",
  TC_API_NODE_INVOKE_RESPONSETYPE: DB_VIEW.TC_API_NODE_PROPERTIES + "--responseType",
  TC_API_NODE_STATUS_METHOD: DB_VIEW.TC_API_NODE_PROPERTIES + "--statusMethod",
  TC_API_NODE_STATUS_URL: DB_VIEW.TC_API_NODE_PROPERTIES + "--statusUrl",
  TC_API_NODE_STATUS_APIPATH: DB_VIEW.TC_API_NODE_PROPERTIES + "--statusApiPath",
  TC_API_NODE_STATUS_REQUESTBODY: DB_VIEW.TC_API_NODE_PROPERTIES + "--statusRequestBody",
  TC_API_NODE_STATUS_RESPONSETYPE: DB_VIEW.TC_API_NODE_PROPERTIES + "--statusResponseType",
  TC_API_NODE_STATUS_SUCCESSVARIABLE: DB_VIEW.TC_API_NODE_PROPERTIES + "--statusSuccessVariable",
  TC_API_NODE_STATUS_SUCCESSCONDITION: DB_VIEW.TC_API_NODE_PROPERTIES + "--statusSuccessCondition",
  TC_API_NODE_STATUS_SUCCESSVALUE: DB_VIEW.TC_API_NODE_PROPERTIES + "--statusSuccessValue",
  TC_API_NODE_STATUS_ERRORVARIABLE: DB_VIEW.TC_API_NODE_PROPERTIES + "--statusErrorVariable",
  TC_API_NODE_STATUS_ERRORCONDITION: DB_VIEW.TC_API_NODE_PROPERTIES + "--statusErrorCondition",
  TC_API_NODE_STATUS_ERRORVALUE: DB_VIEW.TC_API_NODE_PROPERTIES + "--statusErrorValue",
  TC_API_NODE_STATUS_ERROR_REASON: DB_VIEW.TC_API_NODE_PROPERTIES + "--statusErrorReason",
  TC_API_NODE_TESTRUN_BTN: DB_VIEW.TC_API_NODE_PROPERTIES + "--runTest",
  TC_API_NODE_TESTRUN_REFRESH_BTN: DB_VIEW.TC_API_NODE_PROPERTIES + "--statusRefreshBtn",
  TC_API_NODE_TESTRUN_MONITOR: DB_VIEW.TC_API_NODE_PROPERTIES + "--restTaskMonitorBtn",
  ER_REMOTE_OBJECTS_SELECTOR_SELECTION_TABLE_ROW0_COL1:
    DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--selectionTable-rows-row0-col1",
  GV_EXCEPTION_AGGREGATION_SOURCEMEASURE:
    DB_CONTROL.GV_EXCEPTION_AGGREGATION_CONTAINER + "--exceptionAggregationSourceMeasure",
  VIEW_PARTITION_LOW_RANGE_INPUT: `${DB_CONTROL.VIEW_PARTITION_DIALOG}--lowInput-${DB_CONTROL.VIEW_PARTITION_DIALOG}--rangeList`,
  VIEW_PARTITION_HIGH_RANGE_INPUT: `${DB_CONTROL.VIEW_PARTITION_DIALOG}--highInput-${DB_CONTROL.VIEW_PARTITION_DIALOG}--rangeList`,
  GV_CALCULATED_CLIENT: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--client",
  GV_CALCULATED_REFERENCE_DATE: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--referenceDate",
  GV_CALCULATED_CONVERSION_TYPE_INPUT: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--conversionType",
  GV_CALCULATED_CONVERSION_TYPE_SEARCH: "conversionType--searchField",
  GV_CALCULATED_CONVERSION_TARGET_CURRENCY_TYPE_SEARCH: "targetCurrency--searchField",
  GV_CALCULATED_SOURCE_CURRENCY_INPUT: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--sourceCurrency",
  DF_JOIN_LEFT_INPUT_RANK_VALUE: DB_VIEW.DF_JOIN_ADVANCED_PROP_DIALOG + "--leftInputRankValue",
  DF_JOIN_RIGHT_INPUT_RANK_VALUE: DB_VIEW.DF_JOIN_ADVANCED_PROP_DIALOG + "--rightInputRankValue",
  DF_REMOTE_TABLE_NAME: DB_VIEW.DF_NODE_PROPERTIES + "--remoteTableName",
  RF_BUSINESS_NAME: DB_VIEW.RF_PROPERTY_PANEL + "--rfBusinessName",
  RF_TECHNICAL_NAME: DB_VIEW.RF_PROPERTY_PANEL + "--rfTechnicalName",
  RF_LOAD_TYPE: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--replicationLoadTypeAll",
  RF_COLUMNS_SEARCH_FIELD: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--columnSearchField",
  RF_FLOWS_CONNECTION_SEARCH_ID: "ConnectionDialog-searchField-I",
  RF_CANVAS_SOURCE_OBJECT_SEARCHBOX: DB_VIEW.RF_EDITOR + "--searchSourceDataset-I",
  RF_CANVAS_TARGET_OBJECT_SEARCHBOX: DB_VIEW.RF_EDITOR + "--searchTargetDataset-I",
  DF_NODE_HANA_TARGET_QUALIFIED_NAME: DB_VIEW.DF_NODE_PROPERTIES + "--qualifiedName",
  RF_SAVE_MODEL_DIALOG_BUSINESS_NAME_INPUT_ID: "saveModelDialog--nameInput-inner",
  RF_OBJECT_STORE_SOURCE_SETTING_CLAMPING: DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--clampingDataInput",
  RF_OBJECT_STORE_SOURCE_SETTING_FAIL_ON_INCOMPATIBLE:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--failOnIncompatibleData",
  RF_OBJECT_STORE_SOURCE_SETTING_MAX_PARTITION_SWITCH:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--maxpartitionCheckbox",
  RF_OBJECT_STORE_SOURCE_SETTING_MAX_PARTITION_INPUT:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--maxpartitionInput",
  RF_OBJECT_STORE_SOURCE_SETTING_INCLUDE_SUBFOLDER:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--includeSubFolder",
  RF_OBJECT_STORE_SOURCE_SETTING_GLOB_PATTERN:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--fileGlobalPatternInput",
  RF_OBJECT_STORE_SOURCE_SETTING_OVERWRITE:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--overwriteDatasetSettingCb",
  RF_CONFLUENT_SOURCE_SETTING_CONSUME_OTHER_SCHEMA_SWITCH:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--consumeOtherSchema",
  RF_CONFLUENT_SOURCE_SETTING_IGNORE_SCHEMA_MISSMATCH_SWITCH:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--ignoreSchemamissmatch",
  RF_CONFLUENT_SOURCE_SETTING_FAIL_ON_TRUNCATION_SWITCH:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--confleuntDatatruncation",

  RF_CONFLUENT_SOURCE_SETTING_CONSUME_OTHER_SCHEMA_PANEL_SWITCH:
    DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--consumeOtherSchemainPanel",
  RF_CONFLUENT_SOURCE_SETTING_IGNORE_SCHEMA_MISSMATCH_PANEL_SWITCH:
    DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--ignoreSchemamissmatchinPanel",
  RF_CONFLUENT_SOURCE_SETTING_FAIL_ON_TRUNCATION_PANEL_SWITCH:
    DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confleuntDatatruncationinPanel",
  RF_CONFLUENT_SCHEMA_DIALOG_INCLUDE_TECHNICLE_KEY_SWITCH: DB_VIEW.SCHEMA_DAILOG_ID + "--inlcudeTechKeySwitch",
  RF_PROPERTY_PANEL_SETTING_CLAMPING: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--clampingSwitch",
  RF_PROPERTY_PANEL_SETTING_FAIL_ON_INCOMPATIBLE: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--failOnIncompatibleSwitch",
  RF_PROPERTY_PANEL_SETTING_MAX_PARTITION_SWITCH: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--maxpartitionCheckbox",
  RF_PROPERTY_PANEL_SETTING_MAX_PARTITION_INPUT: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--maxPartitionInput",
  RF_PROPERTY_PANEL_SETTING_INCLUDE_SUBFOLDER: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--includeSubFolder",
  RF_PROPERTY_PANEL_SETTING_INCLUDE_SUBFOLDER_SFTP: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--sftpIncludeText",
  RF_PROPERTY_PANEL_SETTING_INCLUDE_GLOB_PATTERN: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--fileGlobalPattern",
  RF_TRANSFORMATION_DIALOG_ADD_TRANSFORMATION_NAME_INPUT_ID: DB_CONTROL.TRANSFORMATION_DIALOG + "--transformName-inner",
  RF_TRANSFORMATION_FILTER_ADD_EXPRESSION_INPUT_TAB_ID:
    DB_VIEW.RF_TRANSFORMATION_DIALOG + "--filterAddExpression-inner",
  RF_TRANSFORMATION_FILTER_DATE_PICKER_LOW: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--filterDatePickerLow-inner",
  RF_DATASET_RENAME_INPUT_ID: "renameTarget-inner",
  RF_DATASET_RENAME_INPUT: "renameTarget",
  RF_DATASET_RENAME_DIALOGUE_BUSINESS_NAME: "businessNameDisplay",
  RF_TRANSFORMATION_FILTER_TOKEN_SEARCH_FIELD_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--filtertokenSearchField-I",
  RF_TRANSFORMATION_MAPPING_TAB_SEARCH_FIELD_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--transformationSearchField-I",
  RF_IMPORT_OBJECTS_FROM_CONECTION_SEARCH_ID: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--selectionSearch-I",
  RF_DELTA_CAPTURE_TABLE_NAME: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--deltaCaptureTableName",
  RF_OBJECT_STORAGE_TYPE: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--storageType",
  GV_EXPRESSION_EDITOR_AGGR_VALUE_HELP_SELECTEDVALUES_MULTIINPUT: DB_VIEW.GV_AGGREGATED + "--subStatus--selectedValues",
  RF_LOCAL_REPO_BROWSER_SEARCHBOX_ID: DB_VIEW.RF_EDITOR + "--addRepositoryObjectsDlg--tableSelectDialog-searchField-I",
  RF_KAFKA_PARTITION_FIELD: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--kafkaNumberOfPartitions",
  RF_KAFKA_REPLICATION_FACTOR_FIELD: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--kafkaReplicationFactor",
  RF_CONFLUENT_PARTITION_FIELD: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--confluentNumberOfPartitions",
  RF_CONFLUENT_PARTITION_FIELD_PANEL: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentNumberOfPartitions",
  RF_CONFLUENT_REPLICATION_FACTOR_FIELD: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--confluentReplicationFactor",
  RF_CONFLUENT_RECORD_NAME_PANEL: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentRecordName",
  RF_CONFLUENT_TOPIC_NAME_PANEL: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentTopicName",
  RF_CONFLUENT_SUBJECTNAME_PREVIEW: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentSubjectNamePreview",
  RF_KAFKA_REPLICATION_FACTOR_FIELD_PANLE: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentReplicationFactor",
  RF_KAFKA_PARTITION_FIELD_IN_PANEL: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--kafkaNumberOfPartitions",
  RF_KAFKA_REPLICATION_FACTOR_FIELD_IN_PANEL: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--kafkaReplicationFactor",
  RF_DELTA_INTERVAL_HOUR: DB_VIEW.RF_PROPERTY_PANEL + "--rfHour",
  RF_DELTA_INTERVAL_MINUTES: DB_VIEW.RF_PROPERTY_PANEL + "--rfMinutes",
  RF_CONTAINER_DIALOG_CONTAINER_PATH: "containerPath",
  HIERARCHY_NAME_COLUMN_INPUT: "ExternalDirectoryHierarchyEditor--hierarchyNameColumn-input",
  HIERARCHY_NAME_COLUMN_INPUT_INNER: "ExternalDirectoryHierarchyEditor--hierarchyNameColumn-input-inner",
  HIERARCHY_NODE_TYPE_VALUE: "ExternalDirectoryHierarchyEditor--value",
  TF_TARGET_NODE_TECHNICAL_NAME: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--technicalName",
  TF_TARGET_NODE_EXISTING_TABLE_TECHNICAL_NAME: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--technicalNameExistingTarget",
  TF_TARGET_NODE_DELTA_CAPTURE_TABLE_NAME: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--deltaCaptureTableName",
  TF_TARGET_NODE_BUSINESS_NAME: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--businessName",
  TF_TARGET_NODE_DELTA_CAPTURE_STATE: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--deltaCaptureTF",
  RF_CONNECTION_DIALOG_REPLICATION_THREAD_LIMIT_INPUT:
    DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--sourceConnReplicatioThreadLimit",
  RF_CONNECTION_DIALOG_WRITE_MODE_INPUT: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--gbqWriteMode",
  CLAMPING_CHKBX: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--clampingChkBx",
  GV_PARAMETER_IS_VALUE_HELP_DROPDOWN: "parametersEditor--isValueHelp",
  GV_PARAMETER_OBJECT_SELECTION: "parametersEditor--objectMapping",
  GV_PARAMETER_COLUMN_SELECTION: "parametersEditor--columnMapping",
  GV_PARAMETER_OBJECT_LIST: "explorerSelectorDialogExt-cnt-scc-ushell-search-result-table",
  RF_PACKAGE_SELECTOR: DB_VIEW.RF_PROPERTY_PANEL + "--packageSelector-select",
  TF_INPUT_PARAM_STRING_INPUT: "parameterMappingTable-rows-row0-col1",
  ODATA_URL: DB_VIEW.ODATA_VIEW_API_BUILDER + "--generatedURL",
  ODATA_PARAM: DB_VIEW.ODATA_VIEW_API_BUILDER + "--queryParam",
  ODATA_PREVIEW_CODE_EDITOR: DB_VIEW.ODATA_VIEW_API_PREVIEW + "--odataPreviewCodeEditor",
  RF_SOURCE_THREAD_LIMIT: DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--sourceConnReplicatioThreadLimit",
  RF_GLOBAL_DELTA_PARTITION: DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--deltaPartitionLblInput",
  RF_OBJECT_DELTA_PARTITION: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--deltaPartitionLblInput",
  RF_TARGET_OBJECT_BUSINESS_NAME: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--targetBusinessNameInput",
  RF_TARGET_OBJECT_TECHNICAL_NAME: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--targetTechnicalNameInput",
  ODATA_PARAM_KEYINPUT: DB_VIEW.ODATA_VIEW_API_BUILDER + "--odataParams--keyInput",
  ODATA_PARAM_VALUEINPUT: DB_VIEW.ODATA_VIEW_API_BUILDER + "--valueInput",
  ODATA_PARAM_TEXTASSOCIATIONINPUT: DB_VIEW.ODATA_VIEW_API_BUILDER + "--textAssociationInput",
  ODATA_MULTI_SINGLE_VALUE_INPUT: DB_VIEW.ODATA_VIEW_API_BUILDER + "--multiSingleValueInput",
  ODATA_PARAM_LISTVALUEINPUT: DB_VIEW.ODATA_VIEW_API_BUILDER + "--queryParamsValueInput",
  ODATA_LANGUAGE_SELECTOR: DB_VIEW.ODATA_VIEW_API_BUILDER + "--languageComboBox",
  FILE_STORAGE_PRIMARY_INPUT: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--storage",
  TF_SCRIPT_COLUMNS_SEARCH_FIELD: DB_VIEW.TF_PYTHON_NODE_PROPERTIES + "--listASearchField",
  TF_SCRIPT_COLUMNS_BUSINESS_NAME: DB_CONTROL.TF_CREATE_NEW_COLUMN_DIALOG + "--view--businessName",
  TF_SCRIPT_COLUMNS_TECHNICAL_NAME: DB_CONTROL.TF_CREATE_NEW_COLUMN_DIALOG + "--view--technicalName",
  TF_SCRIPT_COLUMN_LENGTH: DB_CONTROL.TF_CREATE_NEW_COLUMN_DIALOG + "--view--columnLength",
  TF_SCRIPT_COLUMN_SCALE: DB_CONTROL.TF_CREATE_NEW_COLUMN_DIALOG + "--view--columnScale",
  TF_SCRIPT_COLUMN_PRECISION: DB_CONTROL.TF_CREATE_NEW_COLUMN_DIALOG + "--view--columnPrecision",
  TF_SECONDARY_SOURCE_PROPERTIES_BNAME: DB_VIEW.TF_SECONDARY_SOURCE_PROPERTIES + "--bname",
  TF_SECONDARY_SOURCE_PROPERTIES_PACKAGE: DB_VIEW.TF_SECONDARY_SOURCE_PROPERTIES + "--packageSelector-select",
  TF_SECONDARY_SOURCE_PROPERTIES_TECHNICAL_NAME: DB_VIEW.TF_SECONDARY_SOURCE_PROPERTIES + "--technicalNameInput",
  DAC_IMPERSONATION_USER_INPUT:
    "shellMainContent---databuilderComponent---databuilderWorkbench--ImpersonationDialogView--userInput",
  TF_SECONDARY_SOURCE_PROPERTIES_SPACE_NAME: DB_VIEW.TF_SECONDARY_SOURCE_PROPERTIES + "--spaceOrContextName",
  TF_SECONDARY_SOURCE_PROPERTIES_DELTA_CAPTURE_NAME: DB_VIEW.TF_SECONDARY_SOURCE_PROPERTIES + "--deltaCaptureName",
  RF_CLAMPING_CHECKBOX: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--ltfClampingChkBx",
  RF_OVERWRITE_DATASET_SETTINGS_CHECKBOX: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--overwriteDatasetSettingCb",
  TF_PARAMETERS_DATATYPE_TEXT: "tfInputParametersEditor--datatype-hiddenInput",
  TF_PARAMETERS_STRING_DEFAULT_VALUE: "tfInputParametersEditor--stringDefaultValue",
  TF_PARAMETERS_INT_DEFAULT_VALUE: "tfInputParametersEditor--intDefaultValue",
  TF_PARAMETERS_SEARCH_INPUT: "tfInputParametersEditor--parameterSearch",
  TF_PARAMETERS_NAME_INPUT: "tfInputParametersEditor--tfInputParameterName",
  TF_PARAMETERS_DECIMAL_PRECISION_INPUT: "tfInputParametersEditor--columnPrecision",
  TF_PARAMETERS_DECIMAL_SCALE_INPUT: "tfInputParametersEditor--columnScale",
  TF_PARAMETERS_DECIMAL_DEFAULT_VALUE: "tfInputParametersEditor--decDefaultValue",
  TF_PARAMETERS_DATE_DEFAULT_VALUE: "tfInputParametersEditor--dateDefaultVal",
  TF_PARAMETERS_DATETIME_DEFAULT_VALUE: "tfInputParametersEditor--timestampDefaultVal",
  DF_SCRIPT_PYTHON_VERSION: DB_VIEW.DF_SCRIPT_PROPERTIES + "--pythonVersion",
};

export const DB_BUTTON = {
  GV_PARAMETER_OBJECT_LIST_ADDOBJECT_BUTTON: "explorerSelectorDialogExt-cnt-select",
  GV_PARAMETER_OBJECT_VALUE_HELP: "parametersEditor--objectMapping-vhi",
  GV_PARAMETER_COLUMN_VALUE_HELP: "parametersEditor--columnMapping-input-vhi",
  MAD_OB: "MassActionDialog-OkButton",
  MAD_CB: "MassActionDialog-cancelButton",
  GV_EXPRESSION_EDITOR_FILTER_ADD_VALUE: DB_VIEW.GV_FILTER + "--addValueButton",
  GV_EXPRESSION_EDITOR_CC_ADD_VALUE: DB_VIEW.GV_CALCULATED + "--addValueButton",
  GV_EXPRESSION_EDITOR_AGGR_ADD_VALUE: DB_VIEW.GV_AGGREGATED + "--addValueButton",
  GV_EXPRESSION_EDITOR_VALUE_HELP_LIST_OK_BUTTON: DB_VIEW.GV_FILTER + "--subStatus--okPressValueSelection",
  GV_EXPRESSION_EDITOR_VALUE_HELP_LIST_CANCEL_BUTTON: DB_VIEW.GV_FILTER + "--subStatus--cancelPressValueSelection",
  GV_EXPRESSION_EDITOR_VALUE_HELP_LIST_SEARCH_CLOSE: DB_VIEW.GV_FILTER + "--subStatus--valueHelpSearch-reset",
  GV_EXPRESSION_EDITOR_VALUE_HELP_LIST_CC_OK_BUTTON: DB_VIEW.GV_CALCULATED + "--subStatus--okPressValueSelection",
  GV_EXPRESSION_EDITOR_VALUE_HELP_LIST_AGGR_OK_BUTTON: DB_VIEW.GV_AGGREGATED + "--subStatus--okPressValueSelection",
  GV_EXPRESSION_EDITOR_VALUE_HELP_LIST_AGGR_CANCEL_BUTTON:
    DB_VIEW.GV_AGGREGATED + "--subStatus--cancelPressValueSelection",
  GV_EXPRESSION_EDITOR_VALUE_HELP_LIST_SEARCH: DB_VIEW.GV_FILTER + "--subStatus--valueHelpSearch",
  JOB_EXEC_TYPE: DB_VIEW.VIEW_MONITOR_TASKLOG + "--jobExecType",
  DATA_CLEANSING_CHECKBOX: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--dataCleansingGeoValueId",
  DEFAULT_EXEC: DB_VIEW.VIEW_MONITOR_TASKLOG + "--defaultExec",
  SYNC_EXEC: DB_VIEW.VIEW_MONITOR_TASKLOG + "--syncExec",
  ASYNC_EXEC: DB_VIEW.VIEW_MONITOR_TASKLOG + "--asyncExec",
  REFRESH_VIEW_BTN: DB_VIEW.VIEW_MONITOR_PAGE + "--refeshTableButton",
  PERSISTENCY_MENU_BTN: DB_VIEW.VIEW_MONITOR_PAGE + "--viewPersistencyActionMenuBtn",
  SCHEDULE_MENU_BTN: DB_VIEW.VIEW_MONITOR_PAGE + "--scheduleMenu",
  TE_REPL_STATUS: DB_VIEW.TABLE_EDITOR + "--replicationStatus",
  TE_REPL_STATUS_DESCRIPTION: DB_VIEW.TABLE_EDITOR + "--replicationStatus-state-text",
  TE_REPL_ACTIONS: DB_VIEW.TABLE_EDITOR + "--tableReplicationMenu",
  TE_SCHEDULE_REPLICATION_MENU: DB_VIEW.TABLE_EDITOR + "--scheduleActions",
  TC_MODEL_EMAIL_MULTI_INPUT_RESET_BUTTON: DB_VIEW.TC_MODEL_PROPERTIES + "--resetMsgBody-content",
  TE_CREATE_SCHEDULE_REPLICATION: DB_CONTROL.TE_TASK_SCHEDULE + "--createButton",
  TE_CANCEL_SCHEDULE_REPLICATION: DB_CONTROL.TE_TASK_SCHEDULE + "--cancelButton",
  NAV_TO_EDITOR_FROM_REMOTE_TBL: DB_CONTROL.REMOTE_TBL_TASKLOG + "--openInEditorId",
  NAV_TO_EDITOR_FROM_VIEW_MONITOR: DB_CONTROL.VIEW_MONITOR_TASKLOG + "--openInEditorId",
  NAV_BACK: AB_BUTTON.NAV_BACK,
  SAVE_DIALOG_SAVE: AB_BUTTON.SAVE_DIALOG_SAVE,
  SAVE: AB_BUTTON.SAVE,
  SAVEANDSAVEAS: AB_BUTTON.SAVEANDSAVEAS,
  SAVEONLY: AB_BUTTON.SAVEONLY,
  SAVEAS: AB_BUTTON.SAVEAS,
  SAVE_ANYWAY: "sap-cdw-components-ermodeler-view-validations--dialog--view--ok",
  COMMON_SAVE_ANYWAY: "sap-cdw-components-abstractbuilder-view-validations--dialog--view--ok",
  BROWSER_REMOTE_BUTTON: AB_BUTTON.BROWSER_REMOTE_BUTTON,
  BROWSER_REPO_BUTTON: AB_VIEW.BROWSER + "--repositoryTab",
  SEARCH_REPO_RESET_BUTTON: DB_INPUT.BROWSER_SEARCHREPOSITORY + "-reset",
  TOGGLE_DATAPREVIEW: AB_BUTTON.TOGGLE_DATAPREVIEW,
  ER_MENU_IMPORT: DB_VIEW.DBWB + "--import",
  TOGGLE_TREE: AB_BUTTON.TOGGLE_TREE,
  ER_TOGGLE_TREE: DB_VIEW.DBWB + "--toggleTree",
  IMPACT_LINEAGE: DB_VIEW.DBWB + "--impact",
  ODATA: DB_VIEW.DBWB + "--odata",
  ODATA_SEGMENTEDBUTTON: DB_VIEW.ODATA_VIEW_API_BUILDER + "--RequestTypeSegmentedButton",
  ODATA_DATA_BTN: DB_VIEW.ODATA_VIEW_API_BUILDER + "--data-button",
  ODATA_METADATA_BTN: DB_VIEW.ODATA_VIEW_API_BUILDER + "--metadata-button",
  ODATA_SERVICE_BTN: DB_VIEW.ODATA_VIEW_API_BUILDER + "--service-button",
  ODATA_RESET: DB_VIEW.ODATA_VIEW_API_BUILDER + "--resetButton",
  ODATA_NEXT_BACK: DB_VIEW.ODATA_DIALOG_VIEW + "--okButton",
  ODATA_PREVIEW_COPY: DB_VIEW.ODATA_VIEW_API_PREVIEW + "--previewCopy",
  ODATA_DOWNLOAD_OPENAPI: DB_VIEW.ODATA_VIEW_API_BUILDER + "--download",
  ODATA_COPY_URL: DB_VIEW.ODATA_VIEW_API_BUILDER + "--copyURL",
  ODATA_PARAMS: DB_VIEW.ODATA_VIEW_API_BUILDER + "--odataParams",
  ODATA_ADD_ASSOCIATION: DB_VIEW.ODATA_VIEW_API_BUILDER + "--addAssociation",
  ODATA_PARAMS_LIST: DB_VIEW.ODATA_VIEW_API_BUILDER + "--queryParamsValueList",
  ODATA_ADD_MULTI_VALUE: DB_VIEW.ODATA_VIEW_API_BUILDER + "--addMultiValue",
  TOGGLE_PROPERTIES: AB_BUTTON.TOGGLE_PROPERTIES,
  ENTITY_SORT: DB_VIEW.GV_ENTITY + "--sortListBButton",
  DIAGRAM_JOIN: DB_VIEW.ROOT + "csnQueryBuilderEditor--joinSelector",
  DIAGRAM_UNION: DB_VIEW.ROOT + "csnQueryBuilderEditor--unionSelector",
  DIAGRAM_REPLACE: DB_VIEW.ROOT + "csnQueryBuilderEditor--replaceSelector",
  ER_DELETE: DB_VIEW.ROOT + "erModelerEditor--erModeler--erdDeleteNodeButton",
  ER_AUTO_LAYOUT: DB_VIEW.ROOT + "erModelerEditor--erModeler--erdAutoLayout",
  ER_EXPAND_ALL: DB_VIEW.ROOT + "erModelerEditor--erModeler--erdExpandAll",
  ER_COLLAPSE_ALL: DB_VIEW.ROOT + "erModelerEditor--erModeler--erdCollapseAll",
  ER_IMPACT_ERROR_DIALOG_OK: DB_VIEW.ROOT + "sap-cdw-components-ermodeler-view-ImpactErrors--dialog--view--ok",
  ER_CREATE_ENTITY: DB_VIEW.ROOT + "erModelerEditor--erModeler--erdCreateEntity",
  ER_IMPORT: AB_BUTTON.IMPORT,
  ER_EXPORT: AB_BUTTON.EXPORT,
  ER_EDITCSN: AB_BUTTON.EDITCSN,
  ER_IMPORT_FROM_CONNECTION: AB_VIEW.GENWORKBENCH + "--importRemoteSource-unifiedmenu",
  ER_ADD_PARENT_CHILD_ASSOCIATION:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--ck--addPCElm",
  ER_EXPORT_CSN_FILE: AB_BUTTON.EXPORT_CSN_FILE,
  REMOTE_OBJECTS_SELECTOR_SELECTION_TABLE_SELECT_ALL:
    "sap-cdw-components-ermodeler-view-RemoteObjectsSelector--dialog--view--selectionTable-selall",
  ER_REMOTE_OBJECTS_SELECTOR_SOURCE_TABLE_SELECT_ALL: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--sourceTable-selall",
  ER_REMOTE_OBJECTS_SELECTOR_OK: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--ok",
  ER_REMOTE_OBJECTS_SELECTOR_DELTE: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--deleteSelectionButton",
  ER_IMPORT_FROM_CSN_FILE: AB_BUTTON.IMPORT_FROM_CSN_FILE,
  ER_IMPORT_CSN_DIALOG_NEXT: DB_VIEW.ROOT + "importCsnDialog--uploadButton",
  ER_IMPORT_CSN_DIALOG_SELECT_ALL:
    DB_VIEW.ROOT + "erModelerEditor--addRepositoryObjectsDlg--tableSelectDialog-table-sa-CB",
  ER_IMPORT_CSN_DIALOG_OK: DB_VIEW.ROOT + "erModelerEditor--addRepositoryObjectsDlg--tableSelectDialog-ok",
  ER_IMPORT_CSN_DIALOG_CANCEL: DB_VIEW.ROOT + "importCsnDialog--cancelButton",
  ER_ADD_FROM_REPOSITORY: AB_VIEW.GENWORKBENCH + "--addFromRepository",
  ER_ADD_FROM_REPOSITORY_SELECT_ALL:
    DB_VIEW.ROOT + "erModelerEditor--addRepositoryObjectsDlg--tableSelectDialog-table-sa-CB",
  ER_ADD_FROM_REPOSITORY_OK: DB_VIEW.ROOT + "erModelerEditor--addRepositoryObjectsDlg--tableSelectDialog-ok",
  ER_ADD_FROM_REPOSITORY_TABLE: DB_VIEW.ROOT + "erModelerEditor--addRepositoryObjectsDlg--tableSelectDialog-table",
  ER_SELECT_REMOTE_SOURCE_OK: DB_VIEW.ER_SELECT_REMOTE_SOURCE + "--ok",
  ER_EDIT_ENTITY: DB_VIEW.ROOT + "sap-cdw-ermodeler-EditTableCommand",
  ER_ADD_COLUMN: DB_VIEW.ROOT + "sap-cdw-ermodeler-command-AddAttribue",
  ER_ADD_COLUMN_BELOW: DB_VIEW.ROOT + "sap-cdw-ermodeler-command-AddAttribueBelow",
  ER_CREATE_TABLE: DB_VIEW.ROOT + "sap-cdw-ermodeler-ui-TableSymbol",
  ER_ADD_RELATED: DB_VIEW.ROOT + "sap-cdw-ermodeler-SuggestRelatedConnections",
  ER_ADD_RELATED_DIALOG_OK: "okBtn",
  GV_ASSOCIATION_OK: "ok",
  ER_ADD_RELATED_DIALOG_SELECT_ALL: DB_VIEW.ROOT + "suggestionTable-sa-CB",
  ER_ADD_VIEW: DB_VIEW.ROOT + "sap-cdw-ermodeler-CreateViewCommand",
  ER_PRVIEW: DB_VIEW.ROOT + "sap-cdw-ermodeler-DataPreviewCommand",
  GV_PRVIEW: DB_VIEW.ROOT + "sap-cdw-querybuilder-DataPreviewCommand",
  IMPACT_LINEAGE_COMMAND: DB_VIEW.ROOT + "sap-cdw-ermodeler-ImpactLineageCommand",
  ODATA_COMMAND: DB_VIEW.ROOT + "sap-cdw-ermodeler-ODataCommand",
  ER_ADD_VIEW_DIALOG_OK: DB_VIEW.ROOT + "createViewDialog--saveButton",
  ER_ADD_VIEW_DIALOG_OPEN_VIEW: DB_VIEW.ROOT + "createViewDialog-openViewBuilderCheckBox",
  ER_SAVE_DIALOG_CANCEL: DB_VIEW.ROOT + "saveModelDialog--cancelButton",
  ER_IMPORT_TABLE_DIALOG_OK: DB_VIEW.ROOT + "saveModelDialog--saveButton",
  GV_RENAME_DIALOG_CANCEL: DB_VIEW.ROOT + "changeNameDialog--cancelButton",
  GV_RENAME_DIALOG_SAVE: DB_VIEW.ROOT + "changeNameDialog--saveButton",
  REMOTE_TABLE_SEARCH: "importWizardView--objectsView--objectListSearch-search",
  ER_EDIT_VIEW: DB_VIEW.ROOT + "sap-cdw-ermodeler-EditViewCommand",
  ER_FLIP_ASSOCIATION: DB_VIEW.ROOT + "sap-cdw-ermodeler-FlipAssociationCommand",
  ER_ADD_ASSOCIATION: DB_VIEW.ROOT + "sap-cdw-ermodeler-ui-AssociationSymbol",
  ER_PROPERTIES_EDIT_ATTRIBUTE: DB_VIEW.ER_ENTITY + "--editAttributesButton",
  ER_PROPERTIES_EDIT_MEASURE: DB_VIEW.ER_ENTITY + "--editMeasuresButton",
  ER_PROPERTIES_EDIT_PARAMETER: DB_VIEW.ER_ENTITY + "--editParametersButton",
  ER_MODEL_PRO_EXPAND_TABLES: DB_VIEW.ER_MODEL_PRO + "--TablesPanelTitle",
  ER_MODEL_PRO_EXPAND_TABLES_ICON: DB_VIEW.ER_MODEL_PRO + "--TablesPanel-expandButton",
  ER_EDIT_MEASURE_DIALOG_ADD: DB_VIEW.ER_ENTITY + "--editMeasuresDialogView--addAttributeButton",
  ER_ENTITY_ORD_EXPAND: DB_VIEW.ER_ENTITY + "--ordPanel-expandButton",
  ER_MODEL_PRO_VALIDATIONS: "@validateModel",
  ER_MODEL_PRO_ORD_EXPAND: DB_VIEW.ER_MODEL_PRO + "--ordPanel-expandButton",
  ER_MODEL_PRO_ORD_SETVALUES: DB_VIEW.ER_MODEL_PRO + "--setValues",
  ER_MODEL_PRO_TABLES_ADD: DB_VIEW.ER_MODEL_PRO + "--createEntityButton",
  ER_MODEL_PRO_TABLES_DELETE: DB_VIEW.ER_MODEL_PRO + "--deleteEntityButton",
  ER_MODEL_PRO_TABLES_FIND: DB_VIEW.ER_MODEL_PRO + "--findEntityButton",
  ER_MODEL_PRO_TABLES_FILTER_SEARCH: DB_VIEW.ER_MODEL_PRO + "--filterModelObjects-search",
  ER_MODEL_PRO_TABLES_FILTER_RESET: DB_VIEW.ER_MODEL_PRO + "--filterModelObjects-reset",
  ER_ENTITY_FILTER_ATTRS_SEARCH: DB_VIEW.ER_ENTITY + "--filterBusinessAttributes-search",
  ER_ENTITY_FILTER_ATTRS_RESET: DB_VIEW.ER_ENTITY + "--filterBusinessAttributes-reset",
  ER_ENTITY_TYPE_SELECT: DB_VIEW.ER_ENTITY + "--dataSetTypeSel",
  ER_ENTITY_PACKAGE_SELECT: DB_VIEW.ER_ENTITY + "--packageSelector-select",
  ER_ENTITY_COLS_SET_AS_KEY: DB_VIEW.ER_ENTITY + "--setAsKeyButton",
  ER_ENTITY_COLS_REMOVE_AS_KEY: DB_VIEW.ER_ENTITY + "--removeAsKeyButton",
  ER_ENTITY_COLS_SET_VISIBLE: DB_VIEW.ER_ENTITY + "--setVisibleButton",
  ER_ENTITY_COLS_SET_HIDDEN: DB_VIEW.ER_ENTITY + "--setHiddenButton",
  ER_ENTITY_EXPAND_BUSINESS_PURPOSE: DB_VIEW.ER_ENTITY + "--BusinessPanel-header",
  ER_ENTITY_EXPAND_BUSINESS_PURPOSE_EXPAND: DB_VIEW.ER_ENTITY + "--BusinessPanel-expandButton",
  ER_ENTITY_EXPAND_DEPENTENT_OBJECT_LIST: DB_VIEW.ER_ENTITY + "--dependentObjectPanel-expandButton-img",
  ER_ENTITY_PARAMETERS_PANEL: DB_VIEW.ER_ENTITY + "--ParameterPanel",
  ER_ENTITY_EXPAND_RESPONSIBLE_TEAM_HELP: DB_VIEW.ER_ENTITY + "--responsible-vhi",
  ER_ENTITY_RESPONSIBLETEAM_VHDIALOG_SEARCH: DB_VIEW.ER_ENTITY + "--responsibleTeamVHDialog--dialog-searchField-search",
  ER_ENTITY_RESPONSIBLETEAM_VHDIALOG_CLOSE: DB_VIEW.ER_ENTITY + "--responsibleTeamVHDialog--dialog-cancel",
  ER_EDIT_ATTRIBUTE_DIALOG_CLOSE: DB_VIEW.ER_ENTITY + "--editAttributesDialog-closeBtn",
  ER_EDIT_MEASURE_DIALOG_CLOSE: DB_VIEW.ER_ENTITY + "--editMeasuresDialog-closeBtn",
  ER_ENTITY_VALIDATIONS: DB_VIEW.ER_ENTITY + "--detailsHeader--validationButton",
  DELETE_SELECTED_SYMBOL: DB_VIEW.ROOT + "csnQueryBuilderEditor--deleteNodeButton",
  GV_ENTITY_VALIDATIONS: DB_VIEW.GV_ENTITY + "--detailsHeader--validationButton",
  GV_PROJECTION_VALIDATIONS: DB_VIEW.GV_PROJECTION + "--detailsHeader--validationButton",
  GV_ADD_PROJECTION_NODE: DB_VIEW.ROOT + "sap-cdw-querybuilder-ui-RenameSymbol",
  GV_ADD_CALCULATION_NODE: DB_VIEW.ROOT + "sap-cdw-querybuilder-ui-CalculatedSymbol",
  GV_ADD_FILTER_NODE: DB_VIEW.ROOT + "sap-cdw-querybuilder-ui-FilterSymbol",
  GV_ADD_CALCULATED_NODE: DB_VIEW.ROOT + "sap-cdw-querybuilder-ui-CalculatedSymbol",
  GV_ADD_AGGREGATION_NODE: DB_VIEW.ROOT + "sap-cdw-querybuilder-ui-AggregationSymbol",
  GV_JOIN_SUGGESTION: DB_VIEW.ROOT + "sap-cdw-querybuilder-JoinSuggestionCommand",
  GV_DATA_PREVIEW: DB_VIEW.ROOT + "sap-cdw-querybuilder-DataPreviewCommand",
  GV_AGGREGATED_VALIDATIONS: DB_VIEW.GV_AGGREGATED + "--detailsHeader--validationButton",
  GV_AGGREGATED_RENAME_FEATURE_VALIDATIONS: DB_VIEW.GV_RENAME_AGGREGATED + "--detailsHeader--validationButton",
  GV_FILTER_VALIDATIONS: DB_VIEW.GV_FILTER + "--detailsHeader--validationButton",
  GV_AGGREGATION_VALIDATIONS: DB_VIEW.GV_AGGREGATED + "--detailsHeader--validationButton",
  GV_FILTER_FUNCTYPE_SELECT: DB_VIEW.GV_FILTER + "--dataTypeSelect",
  GV_FILTER_EXPRESSION_BUILDER_CATEGORY_SEGMENTED_BUTTON: DB_VIEW.GV_FILTER + "--expressionBuilderCategory",
  GV_FILTER_FUNCSBUTTON: DB_VIEW.GV_FILTER + "--functionsButton-button",
  GV_AGG_COLMSBUTTON: DB_VIEW.GV_RENAME_AGGREGATED + "--columnsButton-button",
  GV_AGG_FUNCBUTTON: DB_VIEW.GV_RENAME_AGGREGATED + "--functionsButton-button",
  GV_FILTER_COLMSBUTTON: DB_VIEW.GV_FILTER + "--columnsButton-button",
  GV_FILTER_OPERSBUTTON: DB_VIEW.GV_FILTER + "--operatorsButton-button",
  GV_FILTER_VALIDEXPRBUTTON: DB_VIEW.GV_FILTER + "--validateIcon",
  GV_FORMULA_VALIDEXPRBUTTON: DB_VIEW.GV_FORMULA + "--validateIcon",
  GV_RESTRICTED_MEASURE_SOURCE_MEASURE: DB_VIEW.GV_RESTRICTED_MEASURE + "--sourceMeasure",
  GV_RESTRICTED_MEASURE_VALIDEXPRBUTTON: DB_VIEW.GV_RESTRICTED_MEASURE + "--validateIcon",
  GV_CALCULATED_ELEMENTS_COLMSBUTTON: DB_VIEW.GV_CALCULATED + "--columnsButton-button",
  GV_CALCULATED_ELEMENTS_FUNCSBUTTON: DB_VIEW.GV_CALCULATED + "--functionsButton-button",
  GV_CALCULATED_ELEMENTS_VALIDEXPRBUTTON: DB_VIEW.GV_CALCULATED + "--validateIcon",
  GV_CALCULATED_ELEMENTS_VALIDEXPRBUTTON_RENAME: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--validateIcon",
  GV_OUTPUT_HEADER_OBJECT_STATUS: DB_VIEW.OUTPUT + "--EntityPropertiesView--detailsHeader--objectStatusIcon",
  GV_OUTPUT_VALIDATIONS: DB_VIEW.OUTPUT + "--EntityPropertiesView--detailsHeader--validationButton",
  GV_UNION_VALIDATIONS: DB_VIEW.GV_UNION + "--detailsHeader--validationButton",
  GV_JOIN_VALIDATIONS: DB_VIEW.GV_JOIN + "--detailsHeader--validationButton",
  GV_CALCULATED_VALIDATIONS: DB_VIEW.GV_CALCULATED + "--calcHeaderDetails--validationButton",
  GV_CALCULATED_VALIDATIONS_WITH_RENAME_FF: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--calcHeaderDetails--validationButton",
  GV_CALCULATED_SOURCE_CURRENCY_HELP: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--sourceCurrency-vhi",
  GV_CALCULATED_TARGET_CURRENCY_HELP: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--targetCurrency-vhi",
  GV_CALCULATED_REFERENCE_DATE_HELP: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--referenceDate-vhi",
  GV_CALCULATED_CURRENCY_DATATYPE: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--datatype",
  GV_CALCULATED_SOURCE_AMMOUNT_COLUMN: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--sourceAmountColumn",
  GV_CALCULATED_ERROR_HANDLING: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--errorHandling",
  GV_CALCULATED_LOOKUP: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--lookup",
  GV_CALCULATED_ACCURACY: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--accuracy",
  GV_CALCULATED_DATE_FORMAT: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--dateFormat",
  GV_CALCULATED_CONVERSION_TYPE_HELP: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--conversionType-vhi",
  GV_OUTPUT_TYPE_SELECT: DB_VIEW.OUTPUT + "--EntityPropertiesView--dataSetTypeSel",
  GV_OUTPUT_TYPE_SELECT_ADJUSTED:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--entityPropertiesForm--FC-NoHead--Grid-wrapperfor-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--dataSetTypeSel",
  GV_CALCULATED_DATATYPE: DB_VIEW.GV_CALCULATED + "--datatype",
  GV_ENTITY_DEPENDENT_OBJECT_LIST_FILTER_ALL: DB_VIEW.GV_ENTITY + "--filterButtonAll-button",
  GV_ENTITY_DEPENDENT_OBJECT_LIST_FILTER_ERROR: DB_VIEW.GV_ENTITY + "--filterButtonErrors-button",
  GV_ENTITY_EXPAND_DEPENTENT_OBJECT_LIST: DB_VIEW.GV_ENTITY + "--dependentObjectPanel-expandButton-img",
  GV_OUTPUT_DEPENDENT_OBJECT_LIST_FILTER_ALL: DB_VIEW.OUTPUT + "--filterButtonAll-button",
  GV_OUTPUT_DEPENDENT_OBJECT_LIST_FILTER_ERROR: DB_VIEW.OUTPUT + "--filterButtonErrors-button",
  GV_OUTPUT_EXPAND_DEPENTENT_OBJECT_LIST:
    DB_VIEW.OUTPUT + "--EntityPropertiesView--dependentObjectPanel-expandButton-img",
  TE_ADD_COLUMN: DB_VIEW.TABLE_EDITOR + "--addAttributeButton",
  TE_ADD_MEASURE: DB_VIEW.TABLE_EDITOR + "--addMeasureButton",
  TE_ADD_ADDASSOMENU: DB_VIEW.TABLE_EDITOR + "--addAssociationMenu-internalBtn",
  TE_ADD_ADDTEXTASSOMENUITEM: DB_VIEW.TABLE_EDITOR + "--addTextAssociation-unifiedmenu",
  TE_ADD_ADDHIERARCHYASSOMENUITEM: DB_VIEW.TABLE_EDITOR + "--addHierarchyAssociation-unifiedmenu",
  ADD_LB_HIERARCHY:
    "shellMainContent---databuilderComponent---databuilderWorkbench--hierarchyDialogView--addLbHierarchyMenuItem-unifiedmenu",
  ER_ADD_ADDASSOMENU: DB_VIEW.OUTPUT + "--EntityPropertiesView--addAssociationMenu-internalBtn",
  TE_ADD_ADDASSOMENUITEM: DB_VIEW.TABLE_EDITOR + "--addAssociation-unifiedmenu",
  ER_ADD_ADDASSOMENUITEM: DB_VIEW.OUTPUT + "--EntityPropertiesView--addAssociation-unifiedmenu",
  ER_ADD_ADDTEXTASSOMENUITEM: DB_VIEW.OUTPUT + "--EntityPropertiesView--addTextAssociation-unifiedmenu",
  ER_ADD_COPYASSOMENUITEM: DB_VIEW.OUTPUT + "--EntityPropertiesView--addPushedAssociation-unifiedmenu",
  ER_ADD_ADDHIERARCHYASSOMENUITEM: DB_VIEW.OUTPUT + "--EntityPropertiesView--addHierarchyAssociation-unifiedmenu",
  TE_VALIDATIONS: DB_VIEW.DBWB + "--validationPopoverButton",
  TE_HIERARCHY_DIALOG_ADD: DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--addHierarchyButton",
  TE_HIERARCHY_DIALOG_ADD_BUTTON: DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--addHierarchyButton-internalBtn",
  TE_HIERARCHY_DIALOG_ADD_LEVEL: DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--hierarchyAddLevel",
  TE_HIERARCHY_DIALOG_LEVEL_LIST_ITEM:
    DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--elementsSelect-" + DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--hierarchyLevelsList",
  TE_DATASET_TYPE_SELECT: DB_VIEW.TABLE_EDITOR + "--dataSetTypeSel",
  TE_CAHNGE_TO_MEASURE: DB_VIEW.TABLE_EDITOR + "--changeAttributeToMeasureButton",
  TE_CAHNGE_TO_ATTRIBUTE: DB_VIEW.TABLE_EDITOR + "--changeMeasureToAttributeButton",
  TE_DELETE_COLUMN: DB_VIEW.TABLE_EDITOR + "--deleteAttributeButton",
  TE_DELETE_MEASURE_COLUMN: DB_VIEW.TABLE_EDITOR + "--deleteMeasureButton",
  TE_VAL_ERR_DIALOG_CANCEL: DB_VIEW.ROOT + "sap-cdw-components-abstractbuilder-view-validations--dialog--view--cancel",
  TE_UPLOAD_DATA: AB_BUTTON.UPLOAD_DATA,
  TE_DELETE_DATA: "shellMainContent---databuilderComponent---databuilderWorkbench--deleteData",
  UPLOADCSV_DIALOG_OK: DB_VIEW.UPLOADCSV_FILEUPLOADER + "--view--ok",
  UPLOADCSV_DIALOG_USEFIRSTROW: DB_VIEW.UPLOADCSV_FILEUPLOADER + "--view--useFirstRowAsHeaderCb",
  UPLOADCSV_DIALOG_DELETEDATA: DB_VIEW.UPLOADCSV_FILEUPLOADER + "--view--deleteExistingData",
  HIERARCHY: AB_VIEW.GENWORKBENCH + "--hierarchy",
  UNDO: AB_BUTTON.UNDO,
  REDO: AB_BUTTON.REDO,
  DEPLOY: AB_BUTTON.DEPLOY,
  OBJECT_STATUS: DB_VIEW.ROOT + "objectStatusIcon",
  TE_REVERT_OBJECT_STATUS: DB_VIEW.TABLE_EDITOR + "--revertObjectStatusButton",
  GV_REVERT_OBJECT_STATUS: DB_VIEW.OUTPUT + "--EntityPropertiesView--revertObjectStatusButton",
  REVERT_DIALOG_OK: DB_VIEW.ROOT + "revertDialog--ok",
  GV_SOURCE_CURRENCY_DIALOG_SELECT: "sourceCurrency--select",
  GV_TARGET_CURRENCY_DIALOG_SELECT: "targetCurrency--select",
  GV_REFERENCE_DATE_DIALOG_SELECT: "referenceDate--select",
  GV_TARGET_CURRENCY_DIALOG_CANCEL: "targetCurrency--cancel",
  GV_REFERENCE_DATE_DIALOG_CANCEL: "referenceDate--cancel",
  GV_TARGET_CURRENCY_DIALOG_SELECT_CURRENCY_TYPE: "targetCurrency--currencyType",
  GV_REFERENCE_DATE_DIALOG_SELECT_CURRENCY_TYPE: "referenceDate--currencyType",
  GV_REFERENCE_DATE_DIALOG_SELECT_TODAY: "referenceDate--selectToday",
  GV_CONVERSION_TYPE_DIALOG_OK: "conversionType--select",
  GV_CONFIG_TABLES_DIALOG_OK: "configTables--select",
  GV_UNION_PREDECESSOR_SELECTOR: DB_VIEW.GV_UNION + "--unionInputsSelector",
  GV_UNION_MAPPING_TOOLS: DB_VIEW.GV_UNION + "--unionMappingToolsPickerMb",
  GV_JOIN_DELETE: DB_VIEW.GV_JOIN + "--mappings--deleteConnectionButton",
  GV_UNION_DELETE: DB_VIEW.GV_UNION + "--mappings--deleteConnectionButton",
  DBH_DELETE_ENTITY: DB_VIEW.DBH + "--deleteEntity",
  DBH_DELETE_ENTITY_NEW: "shellMainContent---databuilderComponent---databuilderLandingPage--deleteButton",
  DBH_SHARE_ENTITY: DB_VIEW.DBH + "--shareEntity",
  DBH_DEPLOY_ENTITY: DB_VIEW.DBH + "--deployEntity",
  DBH_ADD_ENTITY: DB_VIEW.DBH + "--addEntity",
  DBWB_SHARE_ENTITY: DB_VIEW.DBWB + "--shareEntity",
  DBH_IMPACT_ENTITY: DB_VIEW.DBH + "--impactAnalysisEntity",
  CROSS_SPACE_SHARE_DIALOG_OK: DB_VIEW.CROSS_SPACE_SHARE_DIALOG + "--ok",
  CROSS_SPACE_SHARE_DIALOG_COLLAPSED: DB_VIEW.CROSS_SPACE_SHARE_DIALOG + "--sharedWithPanel",
  CROSS_SPACE_SHARE_DIALOG_SELECT_ALL: DB_VIEW.CROSS_SPACE_SHARE_DIALOG + "--sharingTable-selall",
  CROSS_SPACE_SHARE_DIALOG_DELETE: DB_VIEW.CROSS_SPACE_SHARE_DIALOG + "--deleteSharingButton",
  DBH_SELALL: DB_VIEW.DBH + "--spaceEntityTable-selall",
  MSGBOX_OK: DB_VIEW.ROOT + "__mbox-btn-0",
  MSGBOX_OK1: DB_VIEW.ROOT + "__mbox-btn-1", // When the msgbox is opened twice
  MSGBOX_OK2: DB_VIEW.ROOT + "__mbox-btn-2",
  MSGBOX_OK3: DB_VIEW.ROOT + "__mbox-btn-3",
  MSGBOX_OK4: DB_VIEW.ROOT + "__mbox-btn-4",
  MSGBOX_OK5: DB_VIEW.ROOT + "__mbox-btn-5",
  MSGBOX_OK6: DB_VIEW.ROOT + "__mbox-btn-6",
  MSGBOX_OK7: DB_VIEW.ROOT + "__mbox-btn-7",
  MSGBOX_OK8: DB_VIEW.ROOT + "__mbox-btn-8",
  MSGBOX_OK9: DB_VIEW.ROOT + "__mbox-btn-9",
  MSGBOX_OK10: DB_VIEW.ROOT + "__mbox-btn-10",
  MSGBOX_OK11: DB_VIEW.ROOT + "__mbox-btn-11",
  MSGBOX_OK12: DB_VIEW.ROOT + "__mbox-btn-12",
  GV_ADD_CALCULATED_COLUMN_MENU: DB_VIEW.GV_CALCULATED + "--menuAddCalculatedElement",
  GV_ADD_CALCULATED_COLUMN_MENU_WITH_RENAME_FF: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--menuAddCalculatedElement",
  GV_DELETE_CALCULATED_COLUMN: DB_VIEW.GV_CALCULATED + "--listDeleteCalculatedElement",
  GV_CALCULATED_NODE_NAME: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--nodeRenameFragment--nodeRenameId",
  GV_CALCULATED_HIDE_UNMODIFIED_COLUMNS: DB_VIEW.GV_CALCULATED + "--listDisplayOnlyModified",
  GV_CALCULATED_COL_FUNC_SELECTOR: DB_VIEW.GV_CALCULATED + "--dataTypeSelect",
  GV_CALCULATED_PROP_COLUMNS: DB_VIEW.GV_CALCULATED + "--columnsButton-button",
  GV_CALCULATED_PROP_OPERATORS: DB_VIEW.GV_CALCULATED + "--operatorsButton-button",
  GV_CALCULATED_PROP_HELP_CASE: DB_VIEW.GV_CALCULATED + "--helpCaseButton-img",
  GV_OUTPUT_EDIT_ATTRIBUTES: DB_VIEW.OUTPUT + "--EntityPropertiesView--editAttributesButton",
  GV_OUTPUT_EDIT_MEASURES: DB_VIEW.OUTPUT + "--EntityPropertiesView--editMeasuresButton",
  GV_OUTPUT_ATTRIBUTES_DIALOG_CLOSE: DB_VIEW.OUTPUT + "--EntityPropertiesView--editAttributesDialog-closeBtn",
  GV_PROJ_SELECTALL: DB_VIEW.GV_PROJECTION + "--selectAllButton",
  GV_PROJ_EXCLUDE_SELECTED: DB_VIEW.GV_PROJECTION + "--hideSelectedButton",
  GV_PROJ_RESTORE_SELECTED: DB_VIEW.GV_PROJECTION + "--restoreSelectedButton",
  GV_ZOOM_TO_FIT: DB_VIEW.ROOT + "csnQueryBuilderEditor--zoomToFitButton",
  GV_OUTPUT_MORE_RENAME_MENUITEM:
    DB_VIEW.OUTPUT + "--EntityPropertiesView--morepopover--Menu--moreRenameColumn-unifiedmenu",
  GV_ENTITY_MORE_RENAME_MENUITEM: DB_VIEW.ER_ENTITY + "--morepopover--Menu--moreRenameColumn-unifiedmenu",
  DATA_PREVIEW_COLUMN0_ASC: AB_BUTTON.DATA_PREVIEW_COLUMN0 + "-menu-asc",
  DATA_PREVIEW_COLUMN0_DESC: AB_BUTTON.DATA_PREVIEW_COLUMN0 + "-menu-desc",
  DATA_PREVIEW_COLUMN2_ASC: AB_BUTTON.DATA_PREVIEW_COLUMN2 + "-menu-asc",
  DATA_PREVIEW_COLUMN2_DESC: AB_BUTTON.DATA_PREVIEW_COLUMN2 + "-menu-desc",
  DATA_PREVIEW_COLUMN0: AB_BUTTON.DATA_PREVIEW_COLUMN0,
  DATA_PREVIEW_COLUMN1: AB_BUTTON.DATA_PREVIEW_COLUMN1,
  DATA_PREVIEW_COLUMN2: AB_BUTTON.DATA_PREVIEW_COLUMN2,
  DATA_PREVIEW_COLUMN3: AB_BUTTON.DATA_PREVIEW_COLUMN3,
  GV_JOIN_SUGGESTION_SELECT: DB_VIEW.ROOT + "suggestionlist",
  SQL_VALIDATE: AB_BUTTON.VALIDATE,
  SQL_DEPLOY: AB_BUTTON.DEPLOY,
  SQL_ERROR_CLOSE: AB_BUTTON.ERROR_CLOSE,
  SQL_SAVE_VIEW: DB_VIEW.ROOT + "saveModelDialog--saveButton",
  SQL_DB_VIEW_TYPE: DB_VIEW.OUTPUT + "--EntityPropertiesView--dbViewType",
  SQL_DB_VIEW_TYPE_CHANGE_WARNING: "btnOkDBViewTypeWarning",
  SQL_OUTPUT_DATATYPE: DB_VIEW.OUTPUT + "--EntityPropertiesView--editAttributesDialogView--attribute-datatype",
  GV_JOIN_MAPPINGS_SORT_LEFT_TABLE: DB_VIEW.GV_JOIN + "--mappings--sortListAButton",
  GV_JOIN_MAPPINGS_SORT_RIGHT_TABLE: DB_VIEW.GV_JOIN + "--mappings--sortListBButton",
  GV_UNION_MAPPINGS_SORT_LEFT_TABLE: DB_VIEW.GV_UNION + "--mappings--sortListAButton",
  GV_UNION_MAPPINGS_SORT_RIGHT_TABLE: DB_VIEW.GV_UNION + "--mappings--sortListBButton",
  GV_FILTER_VALIDATE: DB_VIEW.GV_FILTER + "--validateIcon",
  GV_AGGREGATED_VALIDATE: DB_VIEW.GV_AGGREGATED + "--validateIcon",
  GV_CALCULATED_VALIDATE: DB_VIEW.GV_CALCULATED + "--validateIcon",
  GV_CALCULATED_BACK_TO_FIELDS: DB_VIEW.GV_CALCULATED + "--backToFields",
  GV_NODE_RENAME_CALCULATED_BACK_TO_FIELDS: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--backToFields",
  TE_TYPE_SELECTOR: DB_VIEW.TABLE_EDITOR + "--dataSetTypeSel",
  TE_HIERARCHY_WITH_DIRECTORY: DB_VIEW.TABLE_EDITOR + "--hwd--hierarchyWithDirectorySettings",
  ER_HIERARCHY_WITH_DIRECTORY: DB_VIEW.ENTITY_PROPERTY_VIEW + "--hwd--hierarchyWithDirectorySettings",
  TE_HIERARCHY_WITH_DIRECTORY_VALIDATE: DB_VIEW.TABLE_EDITOR + "--hwd--validationPopoverButton",
  HIERARCHY_WITH_DIRECTORY_CANCEL: "ExternalDirectoryHierarchyEditor-cnt-cancelButton",
  HIERARCHY_WITH_DIRECTORY_OK: "ExternalDirectoryHierarchyEditor-cnt-OkButton",
  OUTPUT_ADD_HIERARCHY: DB_VIEW.OUTPUT + "--EntityPropertiesView--detailsHeader--headerHierarchyButton",
  TE_HIERARCHY_PARENT: DB_VIEW.TABLE_EDITOR + "--hierarchyParentSelect",
  TE_HIERARCHY_CHILD: DB_VIEW.TABLE_EDITOR + "--hierarchyChildSelect",
  GV_HIERARCHY_PARENT: DB_VIEW.ENTITY_PROPERTY_VIEW + "--pch--hierarchyParentSelect",
  GV_HIERARCHY_PARENT_NEW: DB_VIEW.ENTITY_PROPERTY_VIEW + "--ck--parentElementId",
  GV_HIERARCHY_CHILD: DB_VIEW.ENTITY_PROPERTY_VIEW + "--pch--hierarchyChildSelect",
  GV_HIERARCHY_CHILD_NEW: DB_VIEW.ENTITY_PROPERTY_VIEW + "--ck--childElementId",
  SQL_HIERARCHY_PARENT: DB_VIEW.ENTITY_PROPERTY_VIEW + "--pch--hierarchyChildSelect",
  HIERARCHY_ADD: DB_VIEW.HIERARCHY_VIEW + "--addHierarchyButton",
  HIERARCHY_DELETE: DB_VIEW.HIERARCHY_VIEW + "--deleteHierarchyButton",
  HIERARCHY_LIST: DB_VIEW.HIERARCHY_VIEW + "--hierarchyListItem-" + DB_VIEW.HIERARCHY_VIEW + "--hierarchyList",
  HIERARCHY_LIST_FIRST: DB_VIEW.HIERARCHY_VIEW + "--hierarchyListItem-" + DB_VIEW.HIERARCHY_VIEW + "--hierarchyList-0",
  HIERARCHY_PARENT: DB_VIEW.HIERARCHY_VIEW + "--hierarchyParentSelect",
  HIERARCHY_ADD_LEVEL: DB_VIEW.HIERARCHY_VIEW + "--hierarchyAddLevel",
  HIERARCHY_LEVELS_LIST:
    DB_VIEW.HIERARCHY_VIEW + "--elementsSelect-" + DB_VIEW.HIERARCHY_VIEW + "--hierarchyLevelsList",
  HIERARCHY_LEVELS_LIST_FIRST:
    DB_VIEW.HIERARCHY_VIEW + "--elementsSelect-" + DB_VIEW.HIERARCHY_VIEW + "--hierarchyLevelsList-0",
  GV_EDIT_ENTITY: DB_VIEW.DBH + "--editEntity",
  DBH_FILES_ADD: DB_VIEW.DBH + "--addEntity",
  DBH_FILES_DELETE: DB_VIEW.DBH + "--deleteEntity",
  DBH_FILES_IMPORT: DB_VIEW.DBH + "--importEntity",
  DBH_FILES_DEPLOY: DB_VIEW.DBH + "--deployEntity",
  STORY_FILES_IMPORT: DB_VIEW.STORY + "--importEntity",
  DBH_IMPORT_CSN_DIALOG_SELECT_ALL1:
    DB_VIEW.ROOT +
    "shellMainContent---databuilderComponent---databuilderLandingPage--addRepositoryObjectsDlg--tableSelectDialog-table-sa",
  DBH_IMPORT_CSN_DIALOG_SELECT_ALL:
    DB_VIEW.ROOT +
    "shellMainContent---databuilderComponent---databuilderLandingPage--addRepositoryObjectsDlg--tableSelectDialog-table-sa-CB",
  DBH_IMPORT_CSN_DIALOG_OK:
    DB_VIEW.ROOT +
    "shellMainContent---databuilderComponent---databuilderLandingPage--addRepositoryObjectsDlg--tableSelectDialog-ok",
  DELETE_ASSOCIATION: DB_VIEW.OUTPUT + "--EntityPropertiesView--deleteAssociationButton",
  ADD_VIEW_DATA_ACCESS_CONTROL: DB_VIEW.OUTPUT + "--EntityPropertiesView--addViewDataAccessControlButton",
  DELETE_VIEW_DATA_ACCESS_CONTROL: DB_VIEW.OUTPUT + "--EntityPropertiesView--deleteViewDataAccessControlButton",
  GV_BACK_LINK: DB_VIEW.OUTPUT + "--EntityPropertiesView--detailsHeader--backLink",
  GV_BACK_ICON: DB_VIEW.OUTPUT + "--EntityPropertiesView--detailsHeader--backBreadCrumbs-select",
  GV_BACK_LIST_ITEM: DB_VIEW.OUTPUT + "--EntityPropertiesView--detailsHeader--backBreadCrumbs",
  EDIT_ASSO: DB_VIEW.ROOT + "@editAssociations",
  PREVIEW_SQL_COPY: DB_CONTROL.PREVIEW_SQL_DIALOG + "--copy",
  PREVIEW_SQL_CLOSE: DB_CONTROL.PREVIEW_SQL_DIALOG + "--close",
  PREVIEW_SQL: AB_VIEW.GENWORKBENCH + "--previewSQLButton",
  DATA_PREVIEW_CLEAR_FILTER: DB_VIEW.DBWB + "--clearFilter",
  DATA_PREVIEW_REFRESH: AB_VIEW.GENWORKBENCH + "--refreshDataPreviewButton",
  DATA_PREVIEW_COLUMNS_CONTROL: AB_VIEW.GENWORKBENCH + "--customizeColumnsButton",
  DATA_PREVIEW_CLOSE: AB_VIEW.GENWORKBENCH + "--closePeviewButton",
  DATA_PREVIEW_COLUMNS_CONTROL_DIALOG_OK: "customizeColumnsDialog-ok",
  DATA_PREVIEW_COLUMNS_CONTROL_DIALOG_CANCEL: "customizeColumnsDialog-cancel",
  DATA_PREVIEW_COLUMNS_CONTROL_DIALOG_RESET: "customizeColumnsDialog-reset",
  DATA_PREVIEW_COLUMNS_CONTROL_DIALOG_SELECT_ALL: "__table0-sa",
  DATA_PREVIEW_COLUMNS_CONTROL_DIALOG_TO_TOP: "COLUMNSPANEL_MOVE_TO_TOP",
  DATA_PREVIEW_COLUMNS_CONTROL_DIALOG_TO_BOTTOM: "COLUMNSPANEL_MOVE_TO_BOTTOM",
  DATA_PREVIEW_COLUMNS_CONTROL_DIALOG_SHOW_XXX: "columnsControlTable-showSelected",
  GV_TABLE_EDITOR: DB_VIEW.ROOT + "sap-cdw-querybuilder-EditWithCommand",
  PREVIEW_SQL_TAB: DB_CONTROL.PREVIEW_SQL_DIALOG + "--previewSQLTab",
  PREVIEW_SQL_PROBLEMS_TAB: DB_CONTROL.PREVIEW_SQL_DIALOG + "--previewSQLProblemsTab",
  GV_JOIN_MAPPINGS_FILTER_BUTTON_ALL: DB_VIEW.GV_JOIN + "--mappings--filterButtonAll-button",
  DF_AUTO_LAYOUT: DB_VIEW.DF_EDITOR_CONTROL + "--dfAutoLayout",
  DF_DELETE_NODE: DB_VIEW.DF_EDITOR_CONTROL + "--dfDeleteNodeButton",
  DF_SOURCE: DB_VIEW.DF_EDITOR + "--dfsourceSelector",
  DF_TARGET: DB_VIEW.DF_EDITOR + "--dftargetSelector",
  RUN: AB_VIEW.GENWORKBENCH + "--rundataflow",
  GV_JOIN_MAPPINGS_FILTER_BUTTON_MAPPED: DB_VIEW.GV_JOIN + "--mappings--filterButtonMapped-button",
  GV_JOIN_MAPPINGS_FILTER_BUTTON_UNMAPPED: DB_VIEW.GV_JOIN + "--mappings--filterButtonUnmapped-button",
  GV_JOIN_MAPPINGS_TOGGLE_BUTTON_FULLSCREEN: DB_VIEW.GV_JOIN + "--toggleFullScreen",
  GV_JOIN_MAPPINGS_SEARCHINPUT_LISTA_RESET: DB_VIEW.GV_JOIN + "--mappings--listASearchField-reset",
  GV_JOIN_MAPPINGS_SEARCHINPUT_LISTB_RESET: DB_VIEW.GV_JOIN + "--mappings--listBSearchField-reset",
  GV_JOIN_MAPPINGS_SWAP_BUTTON: DB_VIEW.GV_JOIN + "--mappings--swapButton",
  GV_UNION_MAPPINGS_FILTER_BUTTON_ALL: DB_VIEW.GV_UNION + "--mappings--filterButtonAll-button",
  GV_UNION_MAPPINGS_FILTER_BUTTON_MAPPED: DB_VIEW.GV_UNION + "--mappings--filterButtonMapped-button",
  GV_UNION_MAPPINGS_FILTER_BUTTON_UNMAPPED: DB_VIEW.GV_UNION + "--mappings--filterButtonUnmapped-button",
  GV_UNION_MAPPINGS_TOGGLE_BUTTON_FULLSCREEN: DB_VIEW.GV_UNION + "--toggleFullScreen-img",
  GV_UNION_ALL_SWITCH: DB_VIEW.GV_UNION + "--unionAll",
  GV_ENTITY_DEPENDENT_OBJECT_LIST_SORT: DB_VIEW.GV_ENTITY + "--sortListButton-img",
  OUTPUT_DEPENDENT_OBJECT_LIST_SORT: DB_VIEW.OUTPUT + "--sortListButton-img",
  GV_ENTITY_DEPENDENT_OBJECT_LIST_REFRESH: DB_VIEW.GV_ENTITY + "--refreshListButton-img",
  OUTPUT_DEPENDENT_OBJECT_LIST_REFRESH: DB_VIEW.OUTPUT + "--refreshListButton-img",
  GV_ENTITY_TOGGLE_BUTTON_FULLSCREEN: DB_VIEW.GV_ENTITY + "--toggleFullScreen-img",
  OUTPUT_TOGGLE_BUTTON_FULLSCREEN: DB_VIEW.OUTPUT + "--toggleFullScreen-img",
  UNION_ALL_HELPBUTTON: DB_VIEW.GV_UNION + "--unionAllHelpButton",
  DF_ADD_CALCULATED_COLUMN: DB_VIEW.DF_PROJECTION_PROPERTIES + "--addCalculatedColumn-unifiedmenu-txt",
  DF_ADD_COLUMN: DB_VIEW.DF_PROJECTION_PROPERTIES + "--addColumn",
  DF_FILTER_PANEL: DB_VIEW.DF_PROJECTION_PROPERTIES + "--filterPanel",
  DF_FILTER_FUNCTIONS: DB_VIEW.DF_PROJECTION_PROPERTIES + "--filterExpression--functionsButton-button",
  DF_FILTER_COLUMNS: DB_VIEW.DF_PROJECTION_PROPERTIES + "--filterExpression--columnsButton-button",
  DF_FILTER_OPERATORS: DB_VIEW.DF_PROJECTION_PROPERTIES + "--filterExpression--operatorsButton-button",
  DF_FILTER_INPUT_PARAMETERS: DB_VIEW.DF_PROJECTION_PROPERTIES + "--filterExpression--inputParametersButton-button",
  DF_COLUMN_MORE: DB_VIEW.DF_PROJECTION_PROPERTIES + "--moreButton",
  DF_BACK_LINK: DB_VIEW.DF_PROJECTION_PROPERTIES + "--backToMainPage",
  DF_FUNCTION_HELPER_LINK:
    DB_VIEW.DF_PROJECTION_PROPERTIES +
    "--calcColumnExpression--expressionHelperLink-" +
    DB_VIEW.DF_PROJECTION_PROPERTIES +
    "--calcColumnExpression--functionsList-0",
  DF_DELETE_LIST: DB_VIEW.DF_PROJECTION_PROPERTIES + "--columnListDeleteColumns",
  DF_UNION_ALL_SWITCH: DB_VIEW.DF_UNION_PROPERTIES + "--unionAll",
  DF_SCRIPT_PANEL: DB_VIEW.DF_SCRIPT_PROPERTIES + "--scriptPropertyPanel",
  DF_SCRIPT_EDIT: DB_VIEW.DF_SCRIPT_PROPERTIES + "--scriptEditBtn",
  DF_SCRIPT_CUSTOM_COL_ICON: DB_VIEW.DF_SCRIPT_PROPERTIES + "--isCustomScriptColumn",
  DF_EXIT_FULLSCREEN:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--exitFullscreen",
  DF_JOIN_ENTER_FULLSCREEN: DB_VIEW.DF_JOIN_PROPERTIES + "--toggleFullScreen",
  DF_JOIN_ADVANCED_PROP_FULLSCREEN: DB_VIEW.DF_JOIN_OBJECT_PAGE + "--joinAdvancedPropertiesPanel-expandButton",
  DF_SCRIPT_ADD_COLUMN: DB_VIEW.DF_SCRIPT_PROPERTIES + "--addColumnScript",
  DF_SAVE_COLUMN: "sap-cdw-components-dataflowmodeler-properties-ColumnPropertyEditor--dialog--view--ok",
  DF_CANCEL_COLUMN: "sap-cdw-components-dataflowmodeler-properties-ColumnPropertyEditor--dialog--view--cancel",
  DF_SCRIPT_DELETE_LIST: DB_VIEW.DF_SCRIPT_PROPERTIES + "--columnListDeleteColumns",
  DF_SCRIPT_SELECT_COLUMN: "scriptColumnSelector--columnSelectDialog-ok",
  DF_SCRIPT_HELP: DB_VIEW.DF_SCRIPT_PROPERTIES_OBJECT_PAGE + "--scriptHelpBtn",
  DF_SCRIPT_HELP_CLOSE: "btnHelpClose",
  DF_PROJECTION_SELECT_COLUMN: "projectionColumnSelector--columnSelectDialog-ok",
  DF_MODEL_VALIDATIONS: DB_VIEW.DF_MODEL_PROPERTIES + "--detailsHeader--validationButton",
  DF_PROJECTION_VALIDATIONS: DB_VIEW.DF_PROJECTION_PROPERTIES + "--detailsHeader--validationButton",
  DF_SCRIPT_VALIDATIONS: DB_VIEW.DF_SCRIPT_PROPERTIES + "--detailsHeader--validationButton",
  DF_AGGREGATION_VALIDATIONS: DB_VIEW.DF_AGGREGATION_PROPERTIES + "--detailsHeader--validationButton",
  DF_JOIN_VALIDATIONS: DB_VIEW.DF_JOIN_PROPERTIES + "--detailsHeader--validationButton",
  DF_UNION_VALIDATIONS: DB_VIEW.DF_UNION_PROPERTIES + "--detailsHeader--validationButton",
  DF_NODE_VALIDATIONS: DB_VIEW.DF_NODE_PROPERTIES + "--detailsHeader--validationButton",
  DF_REVERT_OBJECT_STATUS: DB_VIEW.DF_MODEL_PROPERTIES + "--revertObjectStatusButton",
  DF_EDIT_INPUT_PARAMETERS_BTN: DB_VIEW.DF_MODEL_PROPERTIES + "--editDfParametersButton",
  DF_CREATE_LINK: DB_VIEW.ROOT + "sap-cdw-dataflowmodeler-ui-FlowSymbol",
  DF_CREATE_TABLE: DB_VIEW.ROOT + "Sap-Cdw-Dataflow-CreateTable",
  DF_DATA_PREVIEW: DB_VIEW.ROOT + "Sap-Cdw-Dataflow-DataPreview",
  DF_IMPACT_LINEAGE: DB_VIEW.ROOT + "Sap-Cdw-Dataflow-ImpactLineage",
  DF_DELETE: DB_VIEW.ROOT + "Sap-Cdw-Dataflow-Delete",
  DF_EXECUTE_ANYWAY: "btnExecuteAnyway",
  DF_CREATE_DEPLOY_TABLE: DB_VIEW.DF_NODE_PROPERTIES + "--createTable",
  DF_CREATE_DEPLOY_TABLE_CONFIRM: "btnCreateAndDeploy",
  DF_SCHEDULE_MENU_ICON: DB_VIEW.DF_MODEL_PROPERTIES + "--detailSchedulingMenu",
  DF_CREATE_SCHEDULE_BTN: DB_VIEW.DF_TASKSCHEDULE_DIALOG + "--view--createButton",
  DF_NAV_ICON: DB_VIEW.DF_MODEL_PROPERTIES + "--navIcon",
  DF_PACKAGE_SELECTOR: DB_VIEW.DF_MODEL_PROPERTIES + "--packageSelector-select",
  DF_NODE_PACKAGE_SELECTOR: DB_VIEW.DF_NODE_PROPERTIES + "--packageSelector-select",
  TC_PACKAGE_SELECTOR: DB_VIEW.TC_MODEL_PROPERTIES + "--packageSelector-select",
  DBH_SEARCH_ENTITY_BUTTON: DB_INPUT.DBH_SEARCH_ENTITY + "-search",
  CHANGE_MANAGEMENT_DIALOG_OK: DB_VIEW.CHANGE_MANAGEMENT_DIALOG + "--view--ok",
  SQL_CHANGE_MANAGEMENT_DIALOG_OK: DB_VIEW.SQL_CHANGE_MANAGEMENT_DIALOG + "--view--ok",
  DF_CHANGE_MANAGEMENT_DIALOG_OK: DB_VIEW.DF_CHANGE_MANAGEMENT_DIALOG + "--view--ok",
  REPLACE_SOURCE_DIALOG_REPLACE: DB_VIEW.REPLACE_SOURCE_MAPPING_DIALOG + "--view--ok",
  REPLACE_SOURCE_DIALOG_CANCEL: DB_VIEW.REPLACE_SOURCE_MAPPING_DIALOG + "--view--cancel",
  EXTERNAL_HIERARCHY_DIALOG_ADD: DB_VIEW.EXTERNAL_HIERARCHY_MAPPING_DIALOG + "--view--ok",
  EXTERNAL_HIERARCHY_DIALOG_CANCEL: DB_VIEW.EXTERNAL_HIERARCHY_MAPPING_DIALOG + "--view--cancel",
  ER_REMOTE_OBJECTS_SELECTOR_CANCEL: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--cancel",
  DF_PROJECTION_COLUMN_FILTER_RESET: DB_VIEW.DF_PROJECTION_PROPERTIES + "--listASearchField-reset",
  DF_MODEL_REFRESH_STATUS: DB_VIEW.DF_MODEL_PROPERTIES + "--statusRefreshBtn",
  TC_MODEL_REFRESH_STATUS: DB_VIEW.TC_MODEL_PROPERTIES + "--statusRefreshBtn",
  TE_DELETE_ASSOCIATION_MAPPING: DB_VIEW.TABLE_EDITOR + "--joinPropertiesDialogView--mappings--deleteConnectionButton",
  JP_ASSOCIATION_MAPPING_DELETE:
    DB_VIEW.OUTPUT + "--EntityPropertiesView--joinPropertiesDialogView--mappings--deleteConnectionButton",
  DF_JOIN_EDIT_CONDITION: DB_VIEW.DF_JOIN_PROPERTIES + "--joinMappingEditBtn",
  DF_OBJECT_JOIN_EDIT_CONDITION: DB_VIEW.DF_JOIN_OBJECT_PAGE + "--joinMappingEditBtn",
  DF_JOIN_DELETE_CONDITION: DB_VIEW.DF_JOIN_PROPERTIES + "--mappings--deleteConnectionButton",
  DF_JOIN_ADD_COLUMN: DB_VIEW.DF_JOIN_PROPERTIES + "--columnListAddColumns",
  DF_JOIN_SELECT_COLUMN: DB_VIEW.DF_ADD_COLUMN_DIALOG + "--view--ok",
  DF_JOIN_SELECT_FIRST_ROW: DB_VIEW.DF_ADD_COLUMN_DIALOG + "--view--inputColumnTreeTable-rowsel1",
  DF_JOIN_DELETE: DB_VIEW.DF_JOIN_OBJECT_PAGE + "--mappings--deleteConnectionButton",
  DF_CALC_COLUMNS: DB_VIEW.DF_PROJECTION_PROPERTIES + "--calcColumnExpression--columnsButton-button",
  DF_PARAMETERS: DB_VIEW.GV_CALCULATED + "--parametersButton-button",
  DF_PARAMETER_POP_OVER: DB_VIEW.GV_CALCULATED + "--parameterLink-" + DB_VIEW.GV_CALCULATED + "--parametersList-0",
  DF_PARAMETER_POP_OVER_BUSINESS_NAME: DB_VIEW.GV_CALCULATED + "--ParamInfoPopover--businessName",
  DF_CALC_COLUMN_LINK:
    DB_VIEW.DF_PROJECTION_PROPERTIES +
    "--calcColumnExpression--columnLink-" +
    DB_VIEW.DF_PROJECTION_PROPERTIES +
    "--calcColumnExpression--fieldsList-0",
  DF_PARAMETER_COLUMN_LINK: DB_VIEW.GV_CALCULATED + "--parametersList-0",
  DF_DELETE_MODE_DIALOG_OK: "deleteModeOkBtn",
  DF_DELETE_MODE_DIALOG_CANCEL: "deleteModeCancelBtn",
  DF_NODE_CSV_PANEL: DB_VIEW.DF_NODE_PROPERTIES + "--csvPanel",
  DF_NODE_MODIFY_CSV_PROPS: DB_VIEW.DF_NODE_PROPERTIES + "--modifyCsvProps",
  DF_NODE_RESET_CSV_PROPS: "nodeProperties--reset-BDI-content",
  DF_NODE_CSV_HEADER_SWITCH: "nodeProperties--includeHeaderSwitch",
  DF_NODE_CSV_SAVE_CHANGES: "nodeProperties--save-changes",
  DF_NODE_CSV_CANCEL: "nodeProperties--cancel",
  DF_NODE_JSON_PANEL: DB_VIEW.DF_NODE_PROPERTIES + "--jsonPanel",
  DF_NODE_MODIFY_JSON_PROPS: DB_VIEW.DF_NODE_PROPERTIES + "--modifyJsonProps",
  DF_NODE_RESET_JSON_PROPS: "nodeProperties--resetJsonProps",
  DF_NODE_JSON_SAVE_CHANGES: "nodeProperties--saveJsonProps",
  DF_NODE_JSON_CANCEL: "nodeProperties--cancelChanges",
  DF_NODE_COLUMN_FILTER_RESET: DB_VIEW.DF_NODE_PROPERTIES + "--listASearchField-reset",
  DF_UNION_SETTINGS: DB_VIEW.DF_UNION_PROPERTIES + "--unionMappingToolsPickerMb",
  DF_UNION_MAPPING_DELETE: DB_VIEW.DF_UNION_PROPERTIES + "--mappings--deleteConnectionButton",
  DF_SCHEMA_IMPORT_AND_DEPLOY: "saveModelDialog--saveButton",
  DF_UNION_DELETE_LIST: DB_VIEW.DF_UNION_PROPERTIES + "--columnListDeleteColumns",
  DF_UNION_MAPPED: DB_VIEW.DF_UNION_PROPERTIES + "--mappings--filterButtonMapped-button",
  DF_UNION_UNMAPPED: DB_VIEW.DF_UNION_PROPERTIES + "--mappings--filterButtonUnmapped-button",
  DF_UNION_FILTER_ALL: DB_VIEW.DF_UNION_PROPERTIES + "--mappings--filterButtonAll-button",
  DF_IMPORT_DIALOG_ADD_SELECTION: "sap-cdw-components-ermodeler-view-RemoteObjectsSelector--dialog--view--ok",
  DF_IMPORT_DIALOG_AVAILABLE: "sap-cdw-components-ermodeler-view-RemoteObjectsSelector--dialog--view--toPSource-button",
  ILT_DATA_PREVIEW: DB_VIEW.ROOT + "Sap-Cdw-Intelligentlookup-DataPreview",
  ILT_ADD_NEW_RULE: "Sap-Cdw-Intelligentlookup-Addrule",
  ILT_TRUSTED_PREVIEW_TABLE1: DB_VIEW.DBWB + "--TrustedMatchDataPreview--noMatchTable1",
  ILT_TRUSTED_PREVIEW_TABLE2: DB_VIEW.DBWB + "--TrustedMatchDataPreview--noMatchTable2",
  ILT_NOMATCH_PREVIEW_TABLE1: DB_VIEW.DBWB + "--NoMatchDataPreview--noMatchTable1",
  ILT_NOMATCH_PREVIEW_TABLE2: DB_VIEW.DBWB + "--NoMatchDataPreview--noMatchTable2",
  ILT_MULMATCH_PREVIEW_TABLE1: DB_VIEW.DBWB + "--RuleDataPreview--multiple--TreeTableinputtable-mm",
  ILT_MATCHED_PREVIEW_TABLE: DB_VIEW.DBWB + "--RuleDataPreview--matched--TreeTableinputtable",
  ILT_UNPROCESSED_PREVIEW_TABLE: DB_VIEW.DBWB + "--RuleDataPreview--unprocessed--TreeTableinputtable",
  ILT_REVIEW_PREVIEW_TABLE: DB_VIEW.DBWB + "--RuleDataPreview--review--TreeTableinputtable",
  ILT_MULMATCH_PREVIEW_TABLE2: DB_VIEW.DBWB + "--MultipleMatch--TreeTablelookuptable",
  ILT_RULE_PREVIEW: "shellMainContent---databuilderComponent---databuilderWorkbench--RuleDataPreview--rulePreviewTable",
  ILT_AUTO_LAYOUT: DB_VIEW.ILT_EDITOR + "--ilAutoLayout",
  ILT_FULL_SCREEN:
    "shellMainContent---databuilderComponent---databuilderWorkbench--RuleDataPreview--workbenchfullscreen",
  ILT_DELETE_BUTTON: "intelligentLookupEditor--ilDeleteNodeButton",
  ILT_RULE_EDIT_CONDITION: DB_VIEW.ILT_RULE_PROPERTIES + "--joinMappingEditBtn",
  ILT_LEFT_ADD_COLUMN: DB_VIEW.ILT_RULE_PROPERTIES + "--leftColumnAddLink",
  ILT_RIGHT_ADD_COLUMN: DB_VIEW.ILT_RULE_PROPERTIES + "--rightColumnAddLink",
  ILT_LEFT_DROPDOWN: DB_VIEW.ILT_RULE_PROPERTIES + "--input1ColumnAdd-arrow",
  ILT_RIGHT_DROPDOWN: DB_VIEW.ILT_RULE_PROPERTIES + "--input2ColumnAdd-arrow",
  ILT_LOOKUPNODE: "intelligentLookupEditor--ilTargetSelector",
  ILT_LEFT_DROPDOWN_LIST: DB_VIEW.ILT_RULE_PROPERTIES + "--input1ColumnAdd-popup-list",
  ILT_RIGHT_DROPDOWN_LIST: DB_VIEW.ILT_RULE_PROPERTIES + "--input2ColumnAdd-popup-list",
  ILT_SOURCE: DB_VIEW.ILT_EDITOR + "--ilsourceSelector",
  ILT_TARGET: DB_VIEW.ILT_EDITOR + "--iltargetSelector",
  ILT_PROPERTIES_FULLSCREEN:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--intelligentlookup-properties-RuleProperties--toggleFullScreen",
  ILT_PROPERTIES_EXITFULLSCREEN:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--exitFullscreen",
  ILT_PROPERTIES_FILTER_ALL:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--intelligentlookup-properties-RuleProperties--mappings--filterButtonAll-button",
  ILT_PROPERTIES_FILTER_MAPPED:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--intelligentlookup-properties-RuleProperties--mappings--filterButtonMapped-button",
  ILT_PROPERTIES_FILTER_UNMAPPED:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--intelligentlookup-properties-RuleProperties--mappings--filterButtonUnmapped-button",
  ILT_PREVIEW_ERROR_TABLE: DB_VIEW.DBWB + "--RuleDataPreview--ruleProblemsTable-table",
  ILT_WORKAREA_MATCH_BUTTON:
    "shellMainContent---databuilderComponent---databuilderWorkbench--RuleDataPreview--multiple--mmMatchButton2",
  ILT_WORKAREA_MM_UNMATCH_BUTTON:
    "shellMainContent---databuilderComponent---databuilderWorkbench--RuleDataPreview--multiple--leftTableRejectButton",
  ILT_TRUSTEDWORKAREA_UNMATCH_BUTTON:
    "shellMainContent---databuilderComponent---databuilderWorkbench--RuleDataPreview--matched--mmunmatchedbutton",
  ILT_REVIEWWORKAREA_CONFIRM_BUTTON:
    "shellMainContent---databuilderComponent---databuilderWorkbench--RuleDataPreview--review--mmmconfirmbutton",
  ILT_REVIEWORKAREA_UNMATCH_BUTTON:
    "shellMainContent---databuilderComponent---databuilderWorkbench--RuleDataPreview--review--mmunmatchedbutton",
  ILT_UNMATCHWORKAREA_MATCH_BUTTON:
    "shellMainContent---databuilderComponent---databuilderWorkbench--RuleDataPreview--unmatch--mmMatchButton2",
  ILT_COLUMNCUSTOMIZETRUSTED_BUTTON: DB_VIEW.DBWB + "--RuleDataPreview--matched--multipleMatchB2",
  ILT_COLUMNCUSTOMIZEMultiple_BUTTON: DB_VIEW.DBWB + "--RuleDataPreview--multiple--mmmultipleMatchB2",
  ILT_COLUMNCUSTOMIZEMMULTIPLETAB_LOOKUP_BUTTON: DB_VIEW.DBWB + "--RuleDataPreview--multiple--mmmultipleMatch",
  ILT_RULEREFRESH_BUTTON: DB_VIEW.DBWB + "--RuleDataPreview--rulerefreshbutton",
  ILT_RULECLOSE_BUTTON: DB_VIEW.DBWB + "--RuleDataPreview--closePeviewButton",
  ILT_FUZZY_LEFT_COLUMN_ADD_BUTTON: DB_VIEW.ILT_RULE_PROPERTIES + "--leftAddButton",
  ILT_FUZZY_RIGHT_COLUMN_ADD_BUTTON: DB_VIEW.ILT_RULE_PROPERTIES + "--rightAddButton",
  ILT_INPUTSELECT_COLUMN_OK_BUTTON: "fuzzySearchInputColumnSelector--columnSelectDialog-ok",
  ILT_LOOKUPSELECT_COLUMN_OK_BUTTON: "fuzzySearchLookupColumnSelector--columnSelectDialog-ok",
  ILT_DEFAULT_VALUE_OK_BUTTON: DB_VIEW.ILT_OUTPUT_PROPERTIES + "--ColDefaultValuePopover--okButton",
  ILT_INCLUDE_UNMATCHED_RECORDS_SWITCH: DB_VIEW.ILT_OUTPUT_PROPERTIES + "--includeUnmatchedRecordsSwitch",
  ILT_INCLUDE_REVIEW_RECORDS_SWITCH: DB_VIEW.ILT_OUTPUT_PROPERTIES + "--includeReviewRecordsSwitch",
  ILT_INCLUDE_DEFAULT_COLUMN_CHECKBOX: DB_VIEW.ILT_OUTPUT_PROPERTIES + "--defaultValueCheckbox",
  ILT_CASE_SENSITIVE_CHECKBOX: DB_VIEW.ILT_RULE_PROPERTIES + "--caseSensitiveCheckbox",
  ILT_TRIM_SPACE_CHECKBOX: DB_VIEW.ILT_RULE_PROPERTIES + "--trimSpacesCheckbox",
  ILT_EXPORTTOCSN_BUTTON: "shellMainContent---databuilderComponent---databuilderWorkbench--export-internalBtn",
  IL_CHANGE_MANAGEMENT_DIALOG_OK: DB_VIEW.IL_CHANGE_MANAGEMENT_DIALOG + "--view--ok",
  IL_MODEL_VALIDATIONS: DB_VIEW.IL_MODEL_PROPERTIES + "--detailsHeader--validationButton-inner",
  IL_INODE_VALIDATIONS: DB_VIEW.ILT_INPUT_PROPERTIES + "--detailsHeader--validationButton",
  IL_LNODE_VALIDATIONS: DB_VIEW.ILT_LOOKUP_PROPERTIES + "--detailsHeader--validationButton",
  IL_RULE_VALIDATIONS: DB_VIEW.ILT_RULE_PROPERTIES + "--detailsHeader--validationButton",
  IL_DELETION_CONFIRM_DIALOG_DELETE: "btnDeleteAnyway",
  IL_CANCEL_ACTION: "btnCancle",
  DF_UNION_COLUMN_FILTER_RESET: DB_VIEW.DF_UNION_PROPERTIES + "--listASearchField-reset",
  VF_SELECT_FILTER_TYPE: DB_VIEW.VF_DETAILS + "--cmbFSelectionType",
  VF_FILTER_INP_DEFAULT_INT: DB_VIEW.VF_DETAILS + "--filterDefaultValue--intDefaultValue",
  VF_FILTER_INP_DEFAULT_INT_HIGH: DB_VIEW.VF_DETAILS + "--filterDefaultValue--intDefaultValHigh",
  VF_FILTER_INP_DEFAULT_DATE: DB_VIEW.VF_DETAILS + "--filterDefaultValue--dateDefaultVal",
  VF_FILTER_INP_DEFAULT_DATE_HIGH: DB_VIEW.VF_DETAILS + "--filterDefaultValue--dateDefaultValHigh",
  VF_FILTER_INP_DEFAULT_TIME: DB_VIEW.VF_DETAILS + "--filterDefaultValue--timeDefaultVal",
  VF_FILTER_INP_DEFAULT_TIME_HIGH: DB_VIEW.VF_DETAILS + "--filterDefaultValue--timeDefaultValHigh",
  VF_FILTER_CLEAR: DB_VIEW.VF_DETAILS + "--clearFilter",
  VF_FILTER_MANDATORY: DB_VIEW.VF_DETAILS + "--cbMandatory",
  VF_FILTER_MULTIPLE_SEL: DB_VIEW.VF_DETAILS + "--cbMultipleEntries",
  VF_FILTER_HIDDEN: DB_VIEW.VF_DETAILS + "--cbFilterHidden",
  VF_FILTER_INP_DEFAULT_DEC: DB_VIEW.VF_DETAILS + "--filterDefaultValue--decDefaultValue",
  VF_FILTER_INP_DEFAULT_DEC_HIGH: DB_VIEW.VF_DETAILS + "--filterDefaultValue--decDefaultValHigh",
  VF_FILTER_INP_DEFAULT_STR: DB_VIEW.VF_DETAILS + "--filterDefaultValue--stringDefaultValue",
  VF_FILTER_INP_DEFAULT_STR_HIGH: DB_VIEW.VF_DETAILS + "--filterDefaultValue--stringDefaultValHigh",
  DBH_WRANGLING_DIALOG_UPLOAD: DB_VIEW.ROOT + "fileUploadDialog-uploadBtn",
  DBH_WRANGLING_DIALOG_CANCEL: DB_VIEW.ROOT + "fileUploadDialog-cancelBtn",
  UPLOAD_DIALOG_DEPLOY: DB_VIEW.ROOT + "footerToolbar-deployBtn",
  UPLOAD_DIALOG_CANCEL: DB_VIEW.ROOT + "footerToolbar-cancelBtn",
  VALIDATE_EXPN_BTN: DB_VIEW.DF_PROJECTION_PROPERTIES + "--filterExpression--validateExpressionBtn",
  VALIDATE_CALC_EXPN_BTN: DB_VIEW.DF_PROJECTION_PROPERTIES + "--calcColumnExpression--validateExpressionBtn",
  TABLE_EDITOR_COLUMNS_TAB: DB_VIEW.TABLE_EDITOR + "--tableEditorPage-anchBar-__section3-anchor",
  PARAMETER_MAPPING_OKBUTTON: DB_CONTROL.PARAMETERS_MAPPING_DIALOG + "--view--ok",
  ADD_PARAMETER_BUTTON: "parametersEditor--addParameter",
  DELETE_PARAMETER_BUTTON: "parametersEditor--deleteBtn",
  OK_PARAMETER_BUTTON: "parametersEditor--parametersOkBtn",
  ANALYTIC_PARAMETER_OK: "analyticParametersEditor--parametersOkBtn",
  ANALYTIC_PARAMETER_ADD: "analyticParametersEditor--addParameter",
  ANALYTIC_PARAMETER_DELETE: "analyticParametersEditor--deleteBtn",
  PARAMETER_SEARCH: "parametersEditor--parameterSearch",
  TABLE_EDITOR_GENERAL_TAB: DB_VIEW.TABLE_EDITOR + "--tableEditorPage-anchBar-tableEditor--generalSection-anchor",
  TABLE_EDITOR_COLUMNS_SECTION: DB_VIEW.TABLE_EDITOR + "--tableEditorPage-anchBar-tableEditor--measuresSection-anchor",
  TABLE_EDITOR_FILTERS_SECTION: DB_VIEW.TABLE_EDITOR + "--tableEditorPage-anchBar-tableEditor--filterSection-anchor",
  TABLE_EDITOR_PARTITIONS_SECTION:
    DB_VIEW.TABLE_EDITOR + "--tableEditorPage-anchBar-tableEditor--partitionSection-anchor",
  TABLE_EDITOR_DEPENDENT_OBJECT_LIST_TAB:
    DB_VIEW.TABLE_EDITOR + "--tableEditorPage-anchBar-tableEditor--dependentObjectSection-anchor",
  TABLE_EDITOR_ASSOCIATIONS_TAB:
    DB_VIEW.TABLE_EDITOR + "--tableEditorPage-anchBar-tableEditor--associationSection-anchor",
  TABLE_CREATE_FILTER: DB_VIEW.TABLE_EDITOR + "--addFilterBtn",
  TABLE_EDITOR_REPRESENTATIVE: "tableEditor--CompoundKeyButton",
  TABLE_CREATE_NEWFILTER: DB_VIEW.TABLE_EDITOR + "--addNewFilterBtn",
  TABLE_DEFINE_PARTITIONS: DB_VIEW.TABLE_EDITOR + "--DefinePartition",
  TABLE_EDIT_PARTITION: DB_VIEW.TABLE_EDITOR + "--EditPartitionBtn",
  TABLE_DELETE_PARTITION: DB_VIEW.TABLE_EDITOR + "--DeletePartitionButton",
  TE_FILTER_DEL_ROW: DB_VIEW.TABLE_EDITOR + "--deleteRow-tableEditor--filterValues-",
  TE_FILTER_ADD_ROW: DB_VIEW.TABLE_EDITOR + "--addRow-tableEditor--filterValues-",
  TABLE_DELETE_ASSOCIATION: DB_VIEW.TABLE_EDITOR + "--deleteAssociationButton",
  TABLE_NAV_ASSOCIATION: DB_VIEW.TABLE_EDITOR + "--associationDetailsPage-navButton",
  UNDO_BUTTON: DB_VIEW.DBWB + "--undo",
  REDO_BUTTON: DB_VIEW.DBWB + "--redo",
  DF_NODE_DEFINE_FILTERS: DB_VIEW.DF_NODE_PROPERTIES + "--defineFilter",
  DF_NODE_FILTER_DELETE_ROW:
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--deleteRow-" +
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--filterValues-",
  DF_NODE_FILTER_ADD_ROW:
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG + "--view--addRow-" + DB_VIEW.DF_SOURCE_FILTERS_DIALOG + "--view--filterValues-",
  DF_NODE_SAVE_FILTERS: DB_VIEW.DF_SOURCE_FILTERS_DIALOG + "--view--ok",
  DF_NODE_FILTERS_PREVIEW: DB_VIEW.DF_NODE_PROPERTIES + "--filterDataPreview",
  DF_NODE_FILTERS_INFO: DB_VIEW.DF_NODE_PROPERTIES + "--filterHelp",
  DF_NODE_DISCARD_FILTERS: DB_VIEW.DF_SOURCE_FILTERS_DIALOG + "--view--cancel",
  DF_NODE_RESET_FILTERS: DB_VIEW.DF_SOURCE_FILTERS_DIALOG + "--view--resetFilters",
  DF_PROJECTION_SELECTALL: DB_VIEW.DF_PROJECTION_PROPERTIES + "--selectAllButton",
  DF_SCRIPT_SELECTALL: DB_VIEW.DF_SCRIPT_PROPERTIES + "--selectAllButton",
  DF_JOIN_AUTOMAP: DB_VIEW.DF_JOIN_PROPERTIES + "--joinAutoMap",
  DF_OBJECT_JOIN_AUTOMAP: DB_VIEW.DF_JOIN_OBJECT_PAGE + "--joinAutoMap",
  DF_JOIN_REMOVE_MAPPING: DB_VIEW.DF_JOIN_PROPERTIES + "--joinRemoveAllMapping",
  DF_OBJECT_JOIN_REMOVE_MAPPING: DB_VIEW.DF_JOIN_OBJECT_PAGE + "--joinRemoveAllMapping",
  DF_TARGET_AUTOMAP: DB_VIEW.DF_NODE_PROPERTIES + "--autoMap",
  DF_TARGET_REMOVE_MAPPING: DB_VIEW.DF_NODE_PROPERTIES + "--removeAllMapping",
  ADD_TBL_FUNC_ATR: DB_VIEW.OUTPUT + "--EntityPropertiesView--editAttributesDialogView--addAttributeButton",
  PARAMETER_MAPPING_CANCEL_BTN: DB_CONTROL.PARAMETERS_MAPPING_DIALOG + "--view--cancel",
  MDM_EDIT_DATA_TOGGLE: DB_VIEW.DBWB + "--mdm-editDataToggle",
  MDM_PREVIEW_REFRESH_BUTTON: DB_CONTROL.MDM_EDITOR + "--refreshDataPreviewButton",
  MDM_EDITOR_REFRESH_BUTTON: DB_CONTROL.MDM_EDITOR + "--refreshDataPreviewButton-img",
  MDM_PREVIEW_REFRESH_BUTTON_FULL_SCREEN: DB_CONTROL.MDM_EDITOR + "--refreshDataEditorButton",
  MDM_PREVIEW_ADD_BUTTON: DB_CONTROL.MDM_EDITOR + "--addRow",
  MDM_PREVIEW_DELETE_BUTTON: DB_CONTROL.MDM_EDITOR + "--deleteRows",
  MDM_PREVIEW_DUPLICATE_BUTTON: DB_CONTROL.MDM_EDITOR + "--duplicateRows",
  MDM_PREVIEW_MISSING_STRING_OPTION_COMBO: DB_CONTROL.MDM_EDITOR + "--mdmMissingStringsOption",
  MDM_PREVIEW_CLOSE_BUTTON: DB_CONTROL.MDM_EDITOR + "--closePeviewButton",
  MDM_PREVIEW_CLOSE_BUTTON_REOPEN: DB_CONTROL.MDM_EDITOR_REOPEN + "--closePeviewButton",
  MDM_PREVIEW_VALIDATION_BUTTON: "mdm-customValidationButton",
  MDM_DATA_PREVIEW_WARNING_CLOSE: AB_VIEW.GENWORKBENCH + "--mdmPreviewEditor--mdm-closePeviewWarning",
  MDM_DATA_PREVIEW_WARNING_CONFIRM: AB_VIEW.GENWORKBENCH + "--mdmPreviewEditor--mdm-loadPreviewConfirm",
  MDM_PREVIEW_SQL: "previewSqlBtn",
  MDM_PREVIEW_WARNING_CONFIRM: DB_CONTROL.MDM_EDITOR + "--mdm-warningMsgConfirm",
  MDM_PREVIEW_WARNING_MSG: DB_CONTROL.MDM_EDITOR + "--mdm-warningMsg",
  MDM_PREVIEW_WARNING_STRIP: DB_CONTROL.MDM_EDITOR + "--mdm-RemoteBannerStrip",
  DB_DATA_PREVIEW_INPUT_PARAMETER_CANCEL_BUTTON:
    "sap-cdw-components-databuilder-view-InputParameterDataPreview--dialog--view--cancel-BDI-content",
  TOGGLE_TREE_INNER: DB_VIEW.DBWB + "--toggleTree-inner",
  DETAILS_VIEW_INNER: DB_VIEW.DBWB + "--detailsView-inner",
  IL_ICONTABBAR: DB_VIEW.DBWB + "--RuleDataPreview--ruleNodeTabContainer",
  INPUT_TABLE_TREETABLE_MM: DB_VIEW.DBWB + "--RuleDataPreview--multiple--TreeTableinputtable-mm",
  INPUT_TABLE_TABLE_MM: DB_VIEW.DBWB + "--RuleDataPreview--multiple--noMatchTable2-mm",
  INPUT_TABLE_TREETABLE_NM: DB_VIEW.DBWB + "--RuleDataPreview--unmatch--TreeTableinputtable-mm",
  INPUT_TABLE_TABLE_NM: DB_VIEW.DBWB + "--RuleDataPreview--unmatch--noMatchTable2-mm",
  TRUSTED_DRILLDOWN_BUTTON: DB_VIEW.DBWB + "--RuleDataPreview--matched--drilldown",
  TRUSTED_DRILLUP_BUTTON: DB_VIEW.DBWB + "--RuleDataPreview--matched--drillup",
  MULTIPLE_DRILLDOWN_BUTTON: DB_VIEW.DBWB + "--RuleDataPreview--multiple--mmdrilldown",
  MULTIPLE_DRILLUP_BUTTON: DB_VIEW.DBWB + "--RuleDataPreview--multiple--mmdrillup",
  ER_REMOTE_OBJECTS_SELECTOR_REMOTE_LIST_EXPANDOR0:
    DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--remoteTree-rows-row0-treeicon",
  ER_REMOTE_OBJECTS_SELECTOR_REMOTE_LIST_EXPANDOR1:
    DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--remoteTree-rows-row1-treeicon",
  ER_REMOTE_OBJECTS_SELECTOR_REMOTE_LIST_Text0: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--remoteTree-rows-row0-col0",
  ER_REMOTE_OBJECTS_SELECTOR_REMOTE_LIST_Text1: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--remoteTree-rows-row1-col0",
  ER_REMOTE_OBJECTS_SELECTOR_SOURCE_TABLE_ROW_SEL0: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--sourceTable-rowsel0",
  GV_ADD_ANALYTIC_MEASURE_COLUMN_MENU: DB_VIEW.ENTITY_PROPERTY_VIEW + "--menuAddAnalyticMeasureElement",
  GV_DELETE_ANALYTIC_MEASURE_COLUMN: DB_VIEW.ENTITY_PROPERTY_VIEW + "--analyticalMeasuresDeleteBtn",
  DF_ADVANCEDPROPS_PANEL: DB_VIEW.DF_NODE_PROPERTIES + "--advancedPropertiesPanel",
  DF_FORCEFETCH_SWITCH: DB_VIEW.DF_NODE_PROPERTIES + "--forceFetch",
  DF_FORCE_BATCH_SWITCH: DB_VIEW.DF_NODE_PROPERTIES + "--forceBatch",
  DF_BATCH_QUERY_SWITCH: DB_VIEW.DF_NODE_PROPERTIES + "--batchQuery",
  DF_ODBC_TRACING_SWITCH: DB_VIEW.DF_NODE_PROPERTIES + "--enableODBCTracing",
  DF_SOURCE_TARGET_SWITCH: DB_VIEW.DF_NODE_PROPERTIES + "--sourceTargetSwitch",
  DF_SOURCE_BUTTON: DB_VIEW.DF_NODE_PROPERTIES + "--sourceButton",
  DF_TARGET_BUTTON: DB_VIEW.DF_NODE_PROPERTIES + "--targetButton",
  DF_NODE_EXCEL_PANEL: DB_VIEW.DF_NODE_PROPERTIES + "--excelPropertiesPanel",
  DF_NODE_MODIFY_EXCEL_PROPS: DB_VIEW.DF_NODE_PROPERTIES + "--modifyExcelProperties",
  DF_NODE_RESET_EXCEL_PROPS: "nodeProperties--resetExcelProperties",
  DF_NODE_EXCEL_SAVE_CHANGES: "nodeProperties--saveExcelProperties",
  DF_NODE_EXCEL_CANCEL: "nodeProperties--cancelExcelChanges",
  GV_ANALYTIC_MEASURE_ELEMENTS_GRID_LIST_ITEM0_LINK:
    DB_VIEW.ENTITY_PROPERTY_VIEW +
    "--detailsLink-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--analyticalMeasuresGridList-0",
  GV_ANALYTIC_MEASURE_ELEMENTS_GRID_LIST_ITEM0_MORE:
    DB_VIEW.ENTITY_PROPERTY_VIEW +
    "--moreMenuButton-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--analyticalMeasuresGridList-0",
  GV_ANALYTIC_MEASURE_ELEMENTS_GRID_LIST_ITEM1_MORE:
    DB_VIEW.ENTITY_PROPERTY_VIEW +
    "--moreMenuButton-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--analyticalMeasuresGridList-1",
  GV_ANALYTIC_MEASURE_ELEMENTS_GRID_LIST_ITEM0_MORE_MENU_DELETE:
    DB_VIEW.ENTITY_PROPERTY_VIEW + "--analyticalMeasureMoreMenuPopover--Menu--deleteAnalyticalMeasure-unifiedmenu",
  NAV_TO_REMOTE_TBL_MONITOR: DB_VIEW.TABLE_EDITOR + "--toRemoteTableMonitor",
  TE_REFRESH_BUTTON: DB_VIEW.DBWB + "--refreshTable",
  GV_ANALYTIC_MEASURE_ELEMENTS_GRID_LIST_MORE_MENU_MOVETOTOP:
    DB_VIEW.ENTITY_PROPERTY_VIEW + "--analyticalMeasureMoreMenuPopover--Menu--moveToTop-unifiedmenu",
  GV_ANALYTIC_MEASURE_ELEMENTS_GRID_LIST_MORE_MENU_MOVEUP:
    DB_VIEW.ENTITY_PROPERTY_VIEW + "--analyticalMeasureMoreMenuPopover--Menu--moveUp-unifiedmenu",
  GV_ANALYTIC_MEASURE_ELEMENTS_GRID_LIST_MORE_MENU_MOVEDOWN:
    DB_VIEW.ENTITY_PROPERTY_VIEW + "--analyticalMeasureMoreMenuPopover--Menu--moveDown-unifiedmenu",
  GV_ANALYTIC_MEASURE_ELEMENTS_GRID_LIST_MORE_MENU_MOVETOBOTTOM:
    DB_VIEW.ENTITY_PROPERTY_VIEW + "--analyticalMeasureMoreMenuPopover--Menu--moveToBottom-unifiedmenu",
  GV_ANALYTIC_MEASURE_ELEMENTS_GRID_LIST_ITEM1_LINK:
    DB_VIEW.ENTITY_PROPERTY_VIEW +
    "--detailsLink-shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--analyticalMeasuresGridList-1",
  GV_ANALYTIC_PARAMETERS_BUTTON:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--restrictedMeasureDetailsContainer--analyticParametersButton-button",
  GV_EDIT_ANALYTIC_PARAMETERS:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--restrictedMeasureDetailsContainer--editAnalyticParameters",
  EXCEPTION_AGG_ATTRIBUTES: DB_CONTROL.GV_EXCEPTION_AGGREGATION_CONTAINER + "--excAggrAttributes",
  FILE_UPLOAD_OK: DB_VIEW.UPLOADCSV_FILEUPLOADER + "--view--ok",
  OBJECT_NAME_DISPLAY_BUSINESS_NAME: "userSettings---userSettingsView--uiSettingsView--objectNameDisplayBusinessName",
  OBJECT_NAME_DISPLAY_TECHNICAL_NAME: "userSettings---userSettingsView--uiSettingsView--objectNameDisplayTechnicalName",
  USER_SETTINGS_SAVE_BUTTON: "userSettings---userSettingsView--userSettingsSaveButton",
  GV_ANALYTIC_COUNT_DISTINCT_LIST: DB_VIEW.ENTITY_PROPERTY_VIEW + "--analyticMeasureEditButton",
  DI_TABS: DB_VIEW.DI_COMP + "---dilandingpage--iconTabBar",
  VIEW_PARTITION_DIALOG_SAVE: DB_CONTROL.VIEW_PARTITION_DIALOG + "--saveData",
  VIEW_PARTITION_DIALOG_CANCEL: DB_CONTROL.VIEW_PARTITION_DIALOG + "--cancel",
  ADD_VIEW_PARTITION_LINK: DB_CONTROL.VIEW_PARTITION_DIALOG + "--addPartitionLink",
  MDM_COLUMN_SETTINGS_PREVIEW: "__columns1--customizeColumnsButton",
  MDM_COLUMN_SETTINGS_EDIT: "__columns0--customizeColumnsButton",
  MDM_COLUMN_SETTINGS_CANCEL: "mdmCustomizeColumnsDialog-cancel",
  MDM_COLUMN_SETTINGS_OK: "mdmCustomizeColumnsDialog-ok",
  SCHEDULE_PERSISTENCY_MENU_BUT: DB_VIEW.ENTITY_PROPERTY_VIEW + "--scheduleActions",
  CREATE_SCHEDULE_PRESISTENCY_BUT:
    DB_VIEW.ENTITY_PROPERTY_VIEW + "--sidepanelSchedulingDialog--taskScheduleDialog--view--createButton",
  CANCEL_SCHEDULE_PRESISTENCY_BUT:
    DB_VIEW.ENTITY_PROPERTY_VIEW + "--sidepanelSchedulingDialog--taskScheduleDialog--view--cancelButton",
  SQL_VALIDATE_INNER: "sqlEditor--btnValidate",
  SQL_FORMAT_INNER: "sqlEditor--btnFormat",
  IMPORT_PREVIOUS_STEP: "importWizardView--btnPreviousStep",
  IMPORT_NEXT_STEP: "importWizardView--btnNextStep",
  IMPORT_DEPLOY_STEP: "importWizardView--btnImportAndDeploy",
  IMPORT_CLOSE_STEP: "importWizardView--btnCloseDialog",
  MDM_VALIDATION_BUTTON: "mdm-customValidationButton",
  DF_JOIN_ADVANCED_PROP_PANEL: DB_VIEW.DF_JOIN_PROPERTIES + "--joinAdvancedPropertiesPanel",
  DF_MODIFY_JOIN_PROPS: DB_VIEW.DF_JOIN_PROPERTIES + "--modifyJoinProps",
  DF_SAVE_JOIN_PROPS: DB_VIEW.DF_JOIN_ADVANCED_PROP_DIALOG + "--ok",
  DF_ADV_PROP_MEMORY_ALLOCATION_SWITCH: DB_VIEW.DF_MODEL_PROPERTIES + "--memorySettingsSwitch",
  DF_MEMORY_ALLOCATION_CONFIRM_BUTTON: "confirmMemoryConfiguration",
  DF_MEMORY_ALLOCATION_CANCEL_BUTTON: "cancelMemoryConfiguration",
  DF_DATA_VOLUME_SELECT: DB_VIEW.DF_MODEL_PROPERTIES + "--dataVolume",
  TC_AUTO_LAYOUT: DB_VIEW.TC_EDITOR_CONTROL + "--tcAutoLayout",
  TC_ZOOM_TO_FIT: DB_VIEW.TC_EDITOR_CONTROL + "--tcZoomToFitButton",
  TC_DELETE_NODE: DB_VIEW.TC_MODEL_PROPERTIES + "--deleteNodesButton",
  TC_DELETE_NODE_TOOLBAR: DB_VIEW.TC_EDITOR_CONTROL + "--tcDeleteNodeButton",
  TC_NODE_VALIDATIONS: DB_VIEW.TC_MODEL_PROPERTIES + "--detailsHeader--validationButton",
  TC_OPERATION_RADIO_ANY:
    DB_VIEW.TC_OPERATION_PROPERTIES +
    "--operatorRadioButton-" +
    DB_VIEW.TC_OPERATION_PROPERTIES +
    "--operatorRadioButtonGroup-1",
  TC_BACK_LINK: DB_VIEW.TC_MODEL_PROPERTIES + "--detailsHeader--backLink",
  TC_CONTEXT_DELETE: "sap-cdw-taskchainmodeler-DeleteSymbol",
  TC_CONTEXT_IL: "sap-cdw-taskchainmodeler-ImpactLineageCommand",
  TC_SCHEDULE_MENU_ICON: DB_VIEW.TC_MODEL_PROPERTIES + "--scheduleMenu",
  TC_CREATE_SCHEDULE_BTN: DB_VIEW.TC_TASKSCHEDULE_DIALOG + "--view--createButton",
  TC_NAV_ICON: DB_VIEW.TC_MODEL_PROPERTIES + "--navIcon",
  TC_CONTEXT_ALL: "sap-cdw-taskchainmodeler-AddOperationCommand",
  TC_CONTEXT_ANY: "sap-cdw-taskchainmodeler-AddAnyOperationCommand",
  TC_CONTEXT_PARALLEL: "sap-cdw-taskchainmodeler-AddParallelBranchCommand",
  TC_CONTEXT_PLACEHOLDER: "sap-cdw-taskchainmodeler-AddPlaceHolderCommand",
  TC_CONTEXT_LINK: "sap-cdw-taskchainmodeler-ui-LinkSymbol",
  TC_CONTEXT_EDIT: "sap-cdw-taskchainmodeler-EditWithCommand",
  TC_INPUT_PARAMETER_DIALOG_OK: DB_VIEW.TC_INPUT_PARAMETERS_DIALOG + "--view--ok",
  TC_INPUT_PARAMETER_DIALOG_CANCEL: DB_VIEW.TC_INPUT_PARAMETERS_DIALOG + "--view--cancel",
  TC_EDIT_INPUT_PARAMETERS: DB_VIEW.TC_NODE_PROPERTIES + "--editParametersButton",
  TC_INPUT_PARAMETERS_INFO: DB_VIEW.TC_NODE_PROPERTIES + "--infoButton",
  DIAGRAM_TC_ADD_NEW: DB_VIEW.ROOT + "taskChainModeler--addNewSelector",
  DIAGRAM_TC_PARALLEL: DB_VIEW.ROOT + "taskChainModeler--addParallelSelector",
  DIAGRAM_TC_REPLACE: DB_VIEW.ROOT + "taskChainModeler--replaceSelector",
  CUBE_DEPLOY: DB_VIEW.GENWORKBENCH + "--deploy",
  DF_REMOTE_SOURCE_ADD_COLUMN: DB_VIEW.DF_NODE_PROPERTIES + "--columnListAddColumns",
  DF_REMOTE_SOURCE_ADD_DIALOG_CANCEL: "remoteSourceColumnSelector--columnSelectDialog-cancel",
  DF_REMOTE_SOURCE_ADD_DIALOG_CONFIRM: "remoteSourceColumnSelector--columnSelectDialog-ok",
  DF_REMOTE_SOURCE_REFRESH_DIALOG_CONFIRM: DB_VIEW.DF_REMOTE_SOURCE_REFRESH_DIALOG + "--view--ok",
  DF_REMOTE_SOURCE_REFRESH_COLUMN: DB_VIEW.DF_NODE_PROPERTIES + "--columnListRefreshColumns",
  DF_NODE_DELETE_LIST: DB_VIEW.DF_NODE_PROPERTIES + "--columnListDeleteColumns",
  TE_HIERARCHY_ADD_MULT_PC: DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--addPCElement",
  TE_HIERARCHY_ADD_PARENT_CHILD_RELATION: "tableEditor--addPCElm",
  TE_HIERARCHY_PARENT_LIST: DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--parentElementId",
  TE_HIERARCHY_CHILD_LIST: DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--childElementId",
  DF_ADVANCED_FAIL_ON_STRING_SWITCH: DB_VIEW.DF_NODE_PROPERTIES + "--failOnStringTruncationSwitch",
  DF_ADV_PROP_RESTORE_DEFAULT: DB_VIEW.DF_NODE_PROPERTIES + "--restoreDefault",
  GV_CM_OK: "sap-cdw-components-csnquerybuilder-view-ChangeManagementInfo--dialog--view--ok",
  DF_CM_OK: "sap-cdw-components-dataflowmodeler-view-ChangeManagementInfo--dialog--view--ok",
  DAC_OK: "dacOk",
  DF_JOIN_ADD_COLUMNS_SEARCH_RESET: DB_VIEW.DF_ADD_COLUMN_DIALOG + "--view--joinAddColumnsSearchField-reset",
  CONVERSIONTYPE_CANCEL: "conversionType--cancel",
  ARTEFACT_DEPLOY_DIALOG_DEPLOYABLEBUTTON: DB_VIEW.ARTEFACT_DEPLOY_DIALOG + "--deployableButton-button",
  ARTEFACT_DEPLOY_DIALOG_NOTDEPLOYABLEBUTTON: DB_VIEW.ARTEFACT_DEPLOY_DIALOG + "--notDeployableButton-button",
  ARTEFACT_DEPLOY_DIALOG_DEPLOY: DB_VIEW.ARTEFACT_DEPLOY_DIALOG + "--ok",
  SQL_VIEW_COMPOUND_KEY_BUTTON: DB_VIEW.ENTITY_PROPERTY_VIEW + "--EditKeyColumnButton",
  SQL_VIEW_REPRESENTATIVE_KEY_BUTTON: DB_VIEW.ENTITY_PROPERTY_VIEW + "--keyColumnEditDialogView--addRepoKeyBtn",
  SQL_VIEW_REPRESENTATIVE_KEY_MOVE_DOWN: DB_VIEW.ENTITY_PROPERTY_VIEW + "--keyColumnEditDialogView--moveDown",
  SQL_VIEW_REPRESENTATIVE_KEY_MOVE_UP: DB_VIEW.ENTITY_PROPERTY_VIEW + "--keyColumnEditDialogView--moveUp",
  SQL_VIEW_REPRESENTATIVE_KEY_CLOSE_DIALOG: DB_VIEW.ENTITY_PROPERTY_VIEW + "--keyColumnEditDialog-closeBtn",
  DF_DWC_TARGET_REMOTE_TARGET_SWITCH: DB_VIEW.DF_NODE_PROPERTIES + "--dwcTargetRemoteTargetSwitch",
  DF_DWC_TARGET_BUTTON: DB_VIEW.DF_NODE_PROPERTIES + "--dwcTargetButton",
  DF_REMOTE_TARGET_BUTTON: DB_VIEW.DF_NODE_PROPERTIES + "--RemoteTargetButton",
  TRANSFORM_OK_BUTTON: DB_CONTROL.TRANSFORMATION_DIALOG + "--ok",
  TRANSFORM_CANCEL_BUTTON: DB_CONTROL.TRANSFORMATION_DIALOG + "--cancel",
  RF_SOURCE_CONNECTION_DIALOG_BUTTON: DB_VIEW.RF_EDITOR + "--browseSourceConnection",
  RF_SOURCE_SETTINGS_DIALOG_BUTTON: DB_VIEW.RF_EDITOR + "--browseSourceSetting",
  RF_SOURCE_CONTAINER_DIALOG_BUTTON: DB_VIEW.RF_EDITOR + "--browseSourceContainer",
  RF_TARGET_CONNECTION_DIALOG_BUTTON: DB_VIEW.RF_EDITOR + "--browseTargetConnection",
  RF_TARGET_CONTAINER_DIALOG_BUTTON: DB_VIEW.RF_EDITOR + "--browseTargetContainer",
  RF_TARGET_SETTINGS_DIALOG_BUTTON: DB_VIEW.RF_EDITOR + "--browseTargetSetting",
  RF_DELETE_REPLICATION_BTN:
    DB_VIEW.RF_EDITOR + "--sourceDatasetRemove-" + DB_VIEW.RF_EDITOR + "--replicationFlowMainTable" + "-",
  RF_TOOLTIP_REPLICATION_BTN:
    DB_VIEW.RF_EDITOR + "--sourceDatasetInfo-" + DB_VIEW.RF_EDITOR + "--replicationFlowMainTable" + "-",
  RF_CONTEXT_REPLICATION_BTN:
    DB_VIEW.RF_EDITOR + "--rfRowContextMenuDots-" + DB_VIEW.RF_EDITOR + "--replicationFlowMainTable" + "-",
  RF_CONTEXT_RENAME_BTN:
    DB_VIEW.RF_EDITOR + "--contextRenameTargetBtn-" + DB_VIEW.RF_EDITOR + "--replicationFlowMainTable" + "-",
  RF_CONTEXT_MAPTOEXISTING_BTN:
    DB_VIEW.RF_EDITOR + "--contextMapTargetBtn-" + DB_VIEW.RF_EDITOR + "--replicationFlowMainTable" + "-",
  RF_CONTAINER_DIALOG_CANCEL_BUTTON: "containerDialogCancelBtn",
  RF_NAV_ICON: DB_VIEW.RF_PROPERTY_PANEL + "--navToRfMonitoringIcon",
  RF_ADD_TRANSFORMATION: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--addTransformationProps",
  RF_CONFIGURE_SCHEMA: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--schemaPropertiesButton",
  RF_EDIT_TRANSFORMATION: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--editTransformationBtn",
  RF_DELETE_TRANSFORMATION: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--deleteTransformationBtn",
  RF_DELTA_CAPTURE_SWITCH: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--deltaCapture",
  RF_CLAMPING_SWITCH: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--clampingChkbx",
  RF_TRUNCATE_CHECKBOX: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--rfTruncateCB",
  RF_BACK_LINK: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--backToRfPropertyPage",
  RF_REVERT_OBJECT_STATUS: DB_VIEW.RF_PROPERTY_PANEL + "--revertObjectStatusButton",
  RF_CONTAINER_DIALOG_SELECT_BUTTON: "containerDialogSelectBtn",
  RF_ADD_SOURCE_DATASET_BUTTON: DB_VIEW.RF_EDITOR + "--initSourceBrowseDatasetBtn",
  RF_TOOLBAR_BROWSE_SOURCE_DATASET_BUTTON_ID: DB_VIEW.RF_EDITOR + "--browseSourceDataset",
  RF_WELCOME_PANEL_ADD_SOURCE_CONNECTION_BUTTON_ID: DB_VIEW.RF_EDITOR + "--initSourceBrowseConnBtn",
  RF_WELCOME_PANEL_ADD_SOURCE_CONTAINER_BUTTON_ID: DB_VIEW.RF_EDITOR + "--initSourceBrowseContainerBtn",
  RF_IMPORT_OBJECTS_FROM_CONNECTION_ADD_SELECTION_BUTTON: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--ok",
  RF_IMPORT_OBJECTS_FROM_CONNECTION_DIALOG_DELETE_DATASET_BUTTON:
    DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--deleteSelectionButton",
  RF_IMPORT_OBJECTS_FROM_CONNECTION_DIALOG_SEARCHFIELD_RESET_BUTTON_ID:
    DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--selectionSearch-reset",
  RF_CONNECTION_SEARCH_BUTTON_ID: "ConnectionDialog-searchField-search",
  RF_CONNECTION_RESET_BUTTON_ID: "ConnectionDialog-searchField-reset",
  RF_CONNECTION_CANCEL_BUTTON_ID: "ConnectionDialog-cancel",
  RF_CANVAS_SOURCE_OBJECT_SEARCH_RESET: DB_VIEW.RF_EDITOR + "--searchSourceDataset-reset",
  RF_CANVAS_TARGET_OBJECT_SEARCH_RESET: DB_VIEW.RF_EDITOR + "--searchTargetDataset-reset",
  RF_CONTAINER_SEARCH_BUTTON: "containerSearch-search",
  RF_CONTAINER_RESET_BUTTON: "containerSearch-reset",
  HIERARCHY_AD_BUTTON_ID: "tableEditor--addPCElm",
  RF_MAIN_SHELL_SAVE_BUTTON_ID: "shellMainContent---databuilderComponent---databuilderWorkbench--save",
  RF_AFTER_SAVE_VALIDATION_CANCEL_ID: "sap-cdw-components-abstractbuilder-view-validations--dialog--view--cancel",
  RF_AFTER_SAVE_VALIDATION_OK: "sap-cdw-components-abstractbuilder-view-validations--dialog--view--ok",
  RF_DEPLOY_VALIDATION_OK_ID: "sap-cdw-components-abstractbuilder-view-validations--dialog--view--cancel",
  RF_EDIT_TRANSFORMATION_BUTTON:
    DB_VIEW.RF_EDITOR + "--editTransformationButton-" + DB_VIEW.RF_EDITOR + "--replicationFlowMainTable-",
  RF_BROWSER_TARGET_SETTING_OK_BUTTON: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--ok",
  RF_BROWSER_TARGET_SETTING_CANCEL_BUTTON: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--cancel",
  RF_MAIN_CANVAS_ADD_TRANSFORMATION_BUTTON_ID: "addRFTransformation",
  RF_TRANSFORMATION_FILTER_ADD_EXPRESSION_BUTTON_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--rf-filter-add-exp-btn",
  RF_TRANSFORMATION_MAPPING_ADD_ROW_BUTTON_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--addRowButton",
  RF_TRANSFORMATION_FILTER_TOKEN_LIST_SEARCH_FIELD_RESET_BUTTON_ID:
    DB_VIEW.RF_TRANSFORMATION_DIALOG + "--filtertokenSearchField-reset",
  RF_TRANSFORMATION_MAPPING_REMOVE_ROW_BUTTON_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--removeRowButton",
  RF_TRANSFORMATION_MAPPING_MOVE_ROW_UP_BUTTON_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--moveUp",
  RF_TRANSFORMATION_MAPPING_MOVE_ROW_DOWN_BUTTON_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--moveDown",
  RF_TRANSFORMATION_FILTER_COLUMN_INFO_BUTTON:
    DB_VIEW.RF_TRANSFORMATION_DIALOG +
    "--infoButton" +
    "-" +
    DB_VIEW.RF_TRANSFORMATION_DIALOG +
    "--rf-filter-token-list-0",
  RF_TRANSFORMATION_MAPPING_SEARCH_FIELD_RESET_BUTTON_ID:
    DB_VIEW.RF_TRANSFORMATION_DIALOG + "--transformationSearchField-reset",
  RF_TRANSFORMATION_AUTO_MAP_BUTTON_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--autoMap",
  RF_TRANSFORMATION_VALIDATION_BUTTON_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--messagePopoverBtn",
  RF_TRANSFORMATION_FILTER_INVALID_COLUMNS_INFO_BUTTON: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--sourceInvalidColumnsInfo",
  RF_TRANSFORMATION_MAPPING_INVALID_COLUMNS_INFO_BUTTON:
    DB_VIEW.RF_TRANSFORMATION_DIALOG + "--targetInvalidColumnsInfo",
  RF_DATASET_RENAME_BUTTON:
    DB_VIEW.RF_EDITOR + "--contextRenameTargetBtn-" + DB_VIEW.RF_EDITOR + "--replicationFlowMainTable-",
  RF_DATASET_RENAME_DAILOG_OK_BUTTON: "renameDialogRenameBtn",
  RF_DATASET_RENAME_DIALOG_CANCEL_BTN: "renameDialogCancelBtn",
  RF_PROPERTY_PANEL_REFRESH_BUTTON: DB_VIEW.RF_PROPERTY_PANEL + "--rfStatusRefreshBtn",
  RF_LOCAL_REPO_BROWSER_SEARCHBOX_RESET_ID:
    DB_VIEW.RF_EDITOR + "--addRepositoryObjectsDlg--tableSelectDialog-searchField-reset",
  RF_LOCAL_REPO_BROWSER_SEARCHBOX_OK_ID: DB_VIEW.RF_EDITOR + "--addRepositoryObjectsDlg--tableSelectDialog-ok",
  ER_TABLES_EXPAND:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-ModelProperties--TablesPanel-expandButton",
  DF_NODE_ODATA_PROPERTIES_PANEL: DB_VIEW.DF_NODE_PROPERTIES + "--odataPropertiesPanel",
  DF_NODE_DATASOURCE_FILTER_PANEL: DB_VIEW.DF_NODE_PROPERTIES + "--datasourceFilterPanel",
  DF_NODE_ODATA_NAV_PROP_MODIFY: DB_VIEW.DF_NODE_PROPERTIES + "--modifyOdataProperties",
  DF_NODE_ODATA_NAV_PROP_DIALOG_MODIFY: "nodeProperties--saveodataProperties",
  DF_NODE_ODATA_NAV_PROP_DIALOG_CANCEL: "nodeProperties--cancelodataChanges",
  RF_MAIN_CANVAS_DATA_INTEGRATION_MONITOR_BUTTON_ID:
    "shellMainContent---databuilderComponent---databuilderWorkbench--navToMonitoringView",
  RF_MAIN_CANVAS_RUN_BUTTON_ID: "shellMainContent---databuilderComponent---databuilderWorkbench--rundataflow",
  RF_VALIDATION_BTN: DB_VIEW.RF_PROPERTY_PANEL + "--rfHeader--validationButton",
  RF_OBJECT_VALIDATION_BTN: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--rfTaskDetailsHeader--validationButton",
  DF_NODE_DEFINE_PARAMETERS: DB_VIEW.DF_NODE_PROPERTIES + "--defineParameters",
  DF_NODE_PARAMTERS_PREVIEW: DB_VIEW.DF_NODE_PROPERTIES + "--parameterDataPreview",
  SUPPORT_DELTA_CAPTURE: "tableEditor--deltaCapture",
  SUPPORT_DELTA_CAPTURE_NEW: "tableEditor--deltaCapture_new",
  SUPPORT_DELTA_OUTBOUND: "tableEditor--deltaOutboundToggle",
  DELTA_CAPTURE_VB: DB_VIEW.GV_ENTITY + "--deltaCaptureVB",
  DELTA_CAPTURE_ER: DB_VIEW.ER_ENTITY + "--deltaCaptureER",
  RF_EMAIL_NOTIF_BUTTON_ID: "shellMainContent---databuilderComponent---databuilderWorkbench--openEmailNotification",
  RF_EMAIL_NOTIF_CANCEL_BUTTON: "emailNotificationDialog--dialog--view--cancel",
  RF_OVERWRITE_DATASET_SETTINGS: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--overwriteDatasetSettingCb",
  RF_TRANSFORMATION_DIALOGUE_CLOSE_BUTTON: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--cancel",
  RF_CONTAINER_DIALOG_ROOT_BUTTON: "containerRoot",
  RF_SOURCE_OVERWRITE_DATASET_SETTINGS:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--overwriteDatasetSettingCb",
  RF_OBJECTSTORE_SUPPRESS_DUPLICATES_CHECKBOX:
    DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--suppressDuplicatesCheckbox",
  RF_OBJECTSTORE_PARQUET_APACHE_SPARK_MODE_CHECKBOX:
    DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--sparkCompatibilityParquetCheckbox",
  RF_OBJECTSTORE_PROPERTY_PANEL_APACHE_SPARK_MODE_CHECKBOX:
    DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--sparkCompatibilityParquetPanelCheckbox",
  RF_CONFLUENT_APPLY_SCHEMA_BUTTON: DB_VIEW.SCHEMA_DAILOG_ID + "--configureSchema",
  RF_CONFLUENT_APPLY_SEGMENTED_BUTTON: DB_VIEW.SCHEMA_DAILOG_ID + "--confluentSegmentButton",
  RF_CONFLUENT_SCHEMA_DIALOG_OK_BUTTON: DB_VIEW.SCHEMA_DAILOG_ID + "--ok",
  TF_CHANGE_MANAGEMENT_DIALOG_OK: DB_VIEW.TF_CHANGE_MANAGEMENT_DIALOG + "--view--ok",
  TF_TARGET_NODE_VALIDATIONS: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--detailsHeader--validationButton",
  TF_TARGET_NODE_CREATE_NEW_TABLE: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--createNewTargetTable",
  TF_MODEL_VALIDATIONS: DB_VIEW.TF_MODEL_PROPERTIES + "--detailsHeader--validationButton",
  TF_TARGET_TRUNCATE_SWITCH: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--truncateSelector",
  TF_VAL_ERR_DIALOG_CANCEL: DB_VIEW.ROOT + "sap-cdw-components-abstractbuilder-view-validations--dialog--view--cancel",
  SECONDARY_GV_PROJ_SELECTALL: DB_VIEW.GV_PROJECTION_WITH_RENAME_FF + "--selectAllButton",
  SECONDARY_GV_PROJ_EXCLUDE_SELECTED: DB_VIEW.GV_PROJECTION_WITH_RENAME_FF + "--hideSelectedButton",
  TF_VIEW_TRANSFORM_NODE_VALIDATIONS: DB_VIEW.TF_VIEW_TRANSFORM_NODE_PROPERTIES + "--detailsHeader--validationButton",
  TF_VIEW_TRANSFORM_NODE_USE_SQL_VIEW: DB_VIEW.TF_VIEW_TRANSFORM_NODE_PROPERTIES + "--useSQLEditor",
  TF_VIEW_TRANSFORM_NODE_USE_GRAPHICAL_VIEW: DB_VIEW.TF_VIEW_TRANSFORM_NODE_PROPERTIES + "--useGraphicalViewEditor",
  TF_TARGET_REMOVE_MAPPING: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--removeAllMappings",
  TF_TARGET_NODE_MAPPING_DELETE: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--mappings--deleteConnectionButton",
  SECONDARY_GV_CAL_COL_EDIT: DB_VIEW.GV_PROJECTION_WITH_RENAME_FF + "--editButton",
  TF_DATA_PREVIEW: "Sap-Cdw-Transformationflow-DataPreview",
  TF_OPEN_SQL_EDITOR: "Sap-Cdw-Transformationflow-OpenSQLEditor",
  TF_EDIT_VIEW_TRANSFORM: "Sap-Cdw-Transformationflow-OpenEditor",
  TF_OPEN_GV_EDITOR: "Sap-Cdw-Transformationflow-OpenGVEditor",
  TF_ADD_PYTHON_OPERATOR: "Sap-Cdw-Transformationflow-AddPythonOperator",
  TF_SECONDARY_SQL_EDITOR_EDIT_COLUMN: DB_VIEW.TF_SECONDARY_SQL_EDITOR_PROPERTIES + "--editColumnsButton",
  TF_SECONDARY_SQL_EDITOR_EDIT_COLUMN_DIALOG_VALIDATE_BUTTON:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--transformationflow-properties-secondary-SQLOutputProperties--editColumnsDialogView--validationButton",
  TF_SECONDARY_SQL_EDITOR_DELETE_PARAMETERS: DB_VIEW.TF_SECONDARY_SQL_EDITOR_PROPERTIES + "--deleteParameters",
  TF_SECONDARY_SQL_EDITOR_EDIT_COLUMN_DIALOG_DELETE:
    DB_VIEW.TF_SECONDARY_SQL_EDITOR_PROPERTIES + "--editColumnsDialogView--deleteColumnButton",
  TF_SECONDARY_SQL_EDITOR_EDIT_COLUMN_DIALOG_CLOSE:
    DB_VIEW.TF_SECONDARY_SQL_EDITOR_PROPERTIES + "--editColumnsDialog-closeBtn",
  IL_COLUMN_POPOVER_LIST_SELECT: DB_VIEW.IL_DIALOG_VIEW + "--column-select-btn",
  IL_DIALOG_BACK: DB_VIEW.IL_DIALOG_VIEW + "--backButton",
  MV_VALIDATE_BUTTON: "validateRemoteTablesDlg--validateRemoteTableBtn",
  RF_PROPERTY_PANEL_COLUMN_COUNT_ICON: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--rfTaskDetailsHeader--headerIconCount",
  TF_DELTA_CAPTURE_SETTINGS_SWITCH: "secondaryEditorDeltaSettingsView--deltaCaptureButton",
  TF_SOURCE_PARAMETERS_PANEL: DB_VIEW.GV_ENTITY + "--ParameterPanel",
  TF_GVE_PARAMETERS_DELETE_BUTTON: DB_VIEW.TF_SECONDARY_GV_EDITOR_PROPERTIES + "--deleteParameters",
  TF_GVE_PARAMETERS_PANEL: DB_VIEW.TF_SECONDARY_GV_EDITOR_PROPERTIES + "--ParameterPanel",
  RF_USE_BROKER_REP_FACTOR_SWITCH: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--userBrokerFactor",
  RF_USE_BROKER_REP_FACTOR_SWITCH_PANLE: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--userBrokerFactor",
  RF_COFLUENT_SCHEMA_REG_SWITCH: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--confluentSchemaRegistry",
  RF_CONFLUENT_CLAMP_SWITCH: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--confluentClamping",
  RF_COFLUENT_SCHEMA_REG_SWITCH_PANEL: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentSchemaRegistry",
  RF_CONFLUENT_CLAMP_SWITCH_PANEL: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentClamping",
  RF_CONFLUENT_SOURCE_CONSUME_OTHER_SCHEMA_SWITCH:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--consumeOtherSchema",
  RF_CONFLUENT_SOURCE_IGNORE_SCHEMA_MISSMATCH_SWITCH:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--ignoreSchemamissmatch",
  RF_CONFLUENT_SOURCE_DATA_TRUNCATION_SWITCH:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--confleuntDatatruncation",
  RF_LTF_TARGET_OBJECT_CLAMPING_SWITCH: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--ltfClampingChkbx",
  TF_DELTA_CAPTURE_RADIO_BUTTON: "secondaryEditorDeltaSettingsView--deltaCaptureButton",
  TF_ACTIVE_RECORDS_BUTTON: "secondaryEditorDeltaSettingsView--allActiveRecordsButton",
  TF_VIEW_TRANSFORM_VALIDATIONS:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--transformationflow-properties-ViewTransformNodeProperties--detailsHeader--validationButton",
  RF_SOURCE_SETTING_OK: DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--ok",
  RF_SOURCE_SETTING_CANCEL: DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--cancel",
  RF_SOURCE_SCHEMA_OVERFLOW_BUTTON:
    DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--sourceOverflowSchemaSettingsToolbar-overflowButton-inner",
  RF_REVERT_LAST_VERSION: DB_VIEW.RF_PROPERTY_PANEL + "--revertObjectStatusButton-content",
  TF_SCRIPT_EDIT: DB_VIEW.TF_PYTHON_NODE_PROPERTIES + "--scriptEditBtn",
  TF_SCRIPT_CODE_EDITOR: DB_VIEW.TF_PYTHON_NODE_PROPERTIES + "--scriptCodeEditor",
  TF_SCRIPT_ADD_COLUMN: DB_VIEW.TF_PYTHON_NODE_PROPERTIES + "--addColumnScript",
  TF_SAVE_NEW_COLUMN: DB_CONTROL.TF_CREATE_NEW_COLUMN_DIALOG + "--view--ok",
  TF_CALCULATED_DATATYPE: DB_CONTROL.TF_CREATE_NEW_COLUMN_DIALOG + "--view--columnDatatype",
  TF_SCRIPT_DELETE_COLUMN: DB_VIEW.TF_PYTHON_NODE_PROPERTIES + "--columnListDeleteColumns",
  TF_SAVE_ADD_COLUMN: "scriptColumnSelector--columnSelectDialog-ok",
  TF_PYTHON_NODE_VALIDATIONS: DB_VIEW.TF_PYTHON_NODE_PROPERTIES + "--detailsHeader--validationButton",
  TF_SHOW_SECONDARY_SOURCES: DB_VIEW.TF_EDITOR + "--transformationFlowEditorControl--showSourcesSwitch",
  TF_SECONDARY_SOURCE_PROPERTIES_DELTA_CAPTURE: DB_VIEW.TF_SECONDARY_SOURCE_PROPERTIES + "--deltaCapture",
  DAC_IMPERSONATION_VIEW_AS_USER: "impersonationButton",
  DAC_IMPERSONATION_VIEW:
    "shellMainContent---databuilderComponent---databuilderWorkbench--ImpersonationDialogView--viewButton",
  DAC_IMPERSONATION_RESET:
    "shellMainContent---databuilderComponent---databuilderWorkbench--ImpersonationDialogView--resetButton",
  TF_SECONDARY_SOURCE_DELTA_CAPTURE_RB: DB_VIEW.TF_SECONDARY_SOURCE_PROPERTIES + "--deltaCaptureButton",
  TF_SECONDARY_SOURCE_ACTIVE_RECORDS_RB: DB_VIEW.TF_SECONDARY_SOURCE_PROPERTIES + "--allActiveRecordsButton",
  TF_SECONDARY_SOURCE_LOAD_RECORDS_RB: DB_VIEW.TF_SECONDARY_SOURCE_PROPERTIES + "--loadFromSwitch",
  RF_EXPORTTOCSN_BUTTON: "shellMainContent---databuilderComponent---databuilderWorkbench--export-internalBtn",
  RF_EXPORTTOCSN_EXPORT: "shellMainContent---databuilderComponent---databuilderWorkbench--exportCSN-unifiedmenu",
  RF_VERSION_BUTTON: "versionsMenuButton",
  TF_MDM_GVE_REFRESH_BUTTON: "__editor3--refreshDataPreviewButton",
  TF_REPLACE_CANCEL_BUTTON: "sap-cdw-components-ermodeler-properties-ReplaceSourceMapping--dialog--view--cancel",
  RF_DATASET_SOURCE_COLUMN_CHECKBOX: "CopyCourceColumnCb",
  TF_EDIT_INPUT_PARAMETERS: DB_VIEW.TF_MODEL_PROPERTIES + "--editParametersButton",
  TF_ADD_NEW_PARAMETER: "tfInputParametersEditor--addParameter",
  TF_PARAMETERS_DIALOG_OK: "tfInputParametersEditor--parametersOkBtn",
  TF_PARAMETERS_DELETE_BUTTON: "tfInputParametersEditor--deleteBtn",
  TF_PARAMETERS_RESET_SEARCH: "tfInputParametersEditor--parameterSearch-reset",
  TF_INPUT_PARAMETERS_RUN_BUTTON: "runWithInputParameterDialogTF--btnRun",
};

export const DB_LIST = {
  UNION_JOIN_REPLACE_SELECTOR: DB_VIEW.ROOT + "csnQueryBuilderEditor--queryBuilderUnionJoinReplaceSelectorContainer",
  ENTITY_COLUMNS_LIST: DB_VIEW.GV_ENTITY + "--tableElements",
  ENTITY_DEPENDENT_OBJECT_LIST: DB_VIEW.GV_ENTITY + "--dependentObjectList",
  ER_ENTITY_COLUMNS_LIST: DB_VIEW.ER_ENTITY + "--entityBusinessPropertiesElementsTokenList",
  ER_ENTITY_MEASURES_LIST: DB_VIEW.ER_ENTITY + "--entityMeasuresElementsTokenList",
  ER_ENTITY_DEPENDENT_OBJECT_LIST: DB_VIEW.ER_ENTITY + "--dependentObjectList",
  GV_PROJECTION_COLUMNS_LIST: DB_VIEW.GV_PROJECTION + "--tableElements",
  GV_PROJECTION_COLUMNS_LIST_WITH_RENAME_FF: DB_VIEW.GV_PROJECTION_WITH_RENAME_FF + "--tableElements",
  GV_CALCULATED_COLUMNS_LIST: DB_VIEW.GV_CALCULATED + "--tableElements",
  GV_NODE_RENAME_CALCULATED_COLUMNS_LIST: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--tableElements",
  GV_JOIN_MAPPINGS_COLUMNS_LISTA: DB_VIEW.GV_JOIN + "--mappings--listA",
  GV_JOIN_MAPPINGS_COLUMNS_LISTB: DB_VIEW.GV_JOIN + "--mappings--listB",
  GV_JOIN_MAPPINGS_CONNECTION_SVG: DB_VIEW.GV_JOIN + "--mappings--connectionsSvg",
  GV_UNION_MAPPINGS_COLUMNS_LISTA: DB_VIEW.GV_UNION + "--mappings--listA",
  GV_UNION_MAPPINGS_COLUMNS_LISTB: DB_VIEW.GV_UNION + "--mappings--listB",
  GV_UNION_MAPPINGS_CONNECTION_SVG: DB_VIEW.GV_UNION + "--mappings--connectionsSvg",
  GV_ENTITY_DEPENDENT_OBJECT_LIST: DB_VIEW.GV_ENTITY + "--dependentObjectList",
  REPLACE_SOURCE_MAPPINGS_COLUMNS_LISTA: DB_VIEW.REPLACE_SOURCE_MAPPING_DIALOG + "--view--mappings--listA",
  REPLACE_SOURCE_MAPPINGS_COLUMNS_LISTB: DB_VIEW.REPLACE_SOURCE_MAPPING_DIALOG + "--view--mappings--listB",
  EXTERNAL_HIERARCHY_MAPPING_LISTA: DB_VIEW.EXTERNAL_HIERARCHY_MAPPING_DIALOG + "--view--mappings--listA",
  EXTERNAL_HIERARCHY_MAPPING_LISTB: DB_VIEW.EXTERNAL_HIERARCHY_MAPPING_DIALOG + "--view--mappings--listB",
  BROWSER_LIST: AB_LIST.BROWSER_LIST,
  BROWSER_LIST_ESH: AB_LIST.BROWSER_LIST_ESH,
  FILTER_FUNCS_LIST: DB_VIEW.GV_FILTER + "--functionsList",
  GV_CURRENCY_CONVERSION_FUNCS_LIST: "referenceDate--functionsList",
  FILTER_COLUMNS_LIST: DB_VIEW.GV_FILTER + "--fieldsList",
  GV_CA_ELEMENTS_LIST: DB_VIEW.GV_CALCULATED + "--tableElements",
  GV_ANALYTIC_MEASURE_ELEMENTS_LIST: DB_VIEW.ENTITY_PROPERTY_VIEW + "--analyticalMeasuresList",
  GV_ANALYTIC_MEASURE_ELEMENTS_GRID_LIST: DB_VIEW.ENTITY_PROPERTY_VIEW + "--analyticalMeasuresGridList",
  GV_CA_ELEMENTS_COLUMNS_LIST: DB_VIEW.GV_CALCULATED + "--fieldsList",
  BROWSER_REMOTE_LIST: AB_LIST.BROWSER_REMOTE_LIST,
  ER_REMOTE_OBJECTS_SELECTOR_REMOTE_LIST: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--remoteTree",
  IMPORTWIZARD_REMOTE_TREE: "importWizardView--objectsView--remoteTree",
  OUTPUT_MEASURES_LIST: DB_VIEW.OUTPUT + "--EntityPropertiesView--entityMeasuresElementsTokenList",
  OUTPUT_ATTRIBUTES_LIST: DB_VIEW.OUTPUT + "--EntityPropertiesView--entityBusinessPropertiesElementsTokenList",
  OUTPUT_PARAMETER_LIST: DB_VIEW.OUTPUT + "--EntityPropertiesView--parametersList",
  OUTPUT_PARAMETER_DIALOG_LIST: "parametersEditor--parametersList",
  OUTPUT_ANALYTIC_PARAMETER_DIALOG_LIST: "analyticParametersEditor--parametersList",
  OUTPUT_DEPENDENT_OBJECT_LIST: DB_VIEW.OUTPUT + "--EntityPropertiesView--dependentObjectList",
  ENTITY_PARAMETERS_LIST: DB_VIEW.GV_ENTITY + "--parametersList",
  GV_CALCULATED_SEARCH_COL_LIST: DB_VIEW.GV_CALCULATED + "--fieldsList",
  GV_CALCULATED_SEARCH_FUNCTION_LIST: DB_VIEW.GV_CALCULATED + "--functionsList",
  ER_ASSOCIATION_PRO_LISTA: DB_VIEW.ER_ENTITY + "--joinPropertiesDialogView--mappings--listA",
  TE_ASSOCIATION_LISTA: DB_VIEW.TABLE_EDITOR + "--joinPropertiesDialogView--mappings--listA",
  SQL_PROP_PANEL_LIST:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--entityBusinessPropertiesElementsTokenList",
  GV_OUTPUT_ASSOCIATION_LIST: DB_VIEW.OUTPUT + "--EntityPropertiesView--associationsList",
  ER_ENTITY_ASSOCIATION_LIST: DB_VIEW.ER_ENTITY + "--associationsList",
  OUTPUT_ASSOCIATION_COLUMNS_LISTA:
    DB_VIEW.OUTPUT + "--EntityPropertiesView--joinPropertiesDialogView--mappings--listA",
  OUTPUT_ASSOCIATION_COLUMNS_LISTB:
    DB_VIEW.OUTPUT + "--EntityPropertiesView--joinPropertiesDialogView--mappings--listB",
  TE_ASSOCIATION_COLUMNS_LISTA: DB_VIEW.TABLE_EDITOR + "--joinPropertiesDialogView--mappings--listA",
  TE_ASSOCIATION_COLUMNS_LISTB: DB_VIEW.TABLE_EDITOR + "--joinPropertiesDialogView--mappings--listB",
  TE_FILTER_LIST: DB_VIEW.TABLE_EDITOR + "--filterValues",
  TE_FILTER_OP_SELECT: DB_VIEW.TABLE_EDITOR + "--operators-tableEditor--filterValues-",
  TE_FILTER_COL_SELECT: DB_VIEW.TABLE_EDITOR + "--filterColumns-tableEditor--filterValues-",
  TE_HIERARCHY_LIST: DB_VIEW.TE_HIERARCHY_DIALOG_VIEW + "--hierarchyList",
  GV_OUTPUT_VIEW_DATA_ACCESS_CONTROL_LIST: DB_VIEW.OUTPUT + "--EntityPropertiesView--viewDataAccessControlsList",
  OUTPUT_DATA_ACCESS_CONTROL_COLUMNS_LISTA:
    DB_VIEW.OUTPUT + "--EntityPropertiesView--daclJoin--daclPropertiesDialogView--mappings--listA",
  OUTPUT_DATA_ACCESS_CONTROL_COLUMNS_LISTB:
    DB_VIEW.OUTPUT + "--EntityPropertiesView--daclJoin--daclPropertiesDialogView--mappings--listB",
  DF_PROJECTION_COLUMNS_LIST: DB_VIEW.DF_PROJECTION_PROPERTIES + "--tableElements",
  DF_AGGREGATION_COLUMNS_LIST: DB_VIEW.DF_AGGREGATION_PROPERTIES + "--tableElements",
  DF_NODE_COLUMNS_LIST: DB_VIEW.DF_NODE_PROPERTIES + "--tableElements",
  DF_UNION_COLUMNS_LIST: DB_VIEW.DF_UNION_PROPERTIES + "--tableElements",
  DF_DATATYPE_LIST: DB_VIEW.DF_PROJECTION_PROPERTIES + "--datatype",
  DF_COLUMN_PROP_EDITOR_DATATYPE_LIST:
    "sap-cdw-components-dataflowmodeler-properties-ColumnPropertyEditor--dialog--view--columnDatatype",
  DF_SCRIPT_COLUMNS_LIST: DB_VIEW.DF_SCRIPT_PROPERTIES + "--tableElements",
  DF_PROJECTION_TABLE_SELECT_ROWS: "projectionColumnSelector--columnSelectDialog-table",
  DF_SCRIPT_TABLE_SELECT_ROWS: "scriptColumnSelector--columnSelectDialog-table",
  DF_PROJECTION_FUNCTIONS_TYPES: DB_VIEW.DF_PROJECTION_PROPERTIES + "--filterExpression--dataTypeSelect",
  DF_PROJECTION_FUNCTION_LIST: DB_VIEW.DF_PROJECTION_PROPERTIES + "--filterExpression--functionsList",
  DF_PROJECTION_FIELD_LIST: DB_VIEW.DF_PROJECTION_PROPERTIES + "--filterExpression--fieldsList",
  DF_JOIN_JOIN_TYPE: DB_VIEW.DF_JOIN_OBJECT_PAGE + "--joinTypeSelect",
  DF_JOIN_COLUMNS_LIST: DB_VIEW.DF_JOIN_PROPERTIES + "--tableElements",
  DF_JOIN_OBJECT_PAGE_MAPPINGS_COLUMNS_LISTA: DB_VIEW.DF_JOIN_OBJECT_PAGE + "--mappings--listA",
  DF_JOIN_OBJECT_PAGE_MAPPINGS_COLUMNS_LISTB: DB_VIEW.DF_JOIN_OBJECT_PAGE + "--mappings--listB",
  DF_NODE_MODES_SELECT: DB_VIEW.DF_NODE_PROPERTIES + "--targetMode",
  DF_NODE_ACCESS_MODES_SELECT: DB_VIEW.DF_NODE_PROPERTIES + "--accessMethod",
  AGGREGATION_COLUMNS_LIST: DB_VIEW.GV_AGGREGATED + "--columnsTokenList",
  DF_NODE_COL_DEL_SELECT: "nodeProperties--columnSeperatorList",
  DF_NODE_TEXT_DEL_SELECT: "nodeProperties--textSeparatorList",
  DF_UNION_INPUT_SELECT: DB_VIEW.DF_UNION_PROPERTIES + "--unionInputsSelector",
  DF_UNION_MAPPINGS_COLUMNS_LISTA: DB_VIEW.DF_UNION_PROPERTIES + "--mappings--listA",
  DF_UNION_MAPPINGS_COLUMNS_LISTB: DB_VIEW.DF_UNION_PROPERTIES + "--mappings--listB",
  DF_NODE_MAPPINGS_COLUMNS_LISTA: DB_VIEW.DF_NODE_PROPERTIES + "--mappings--listA",
  DF_NODE_MAPPINGS_COLUMNS_LISTB: DB_VIEW.DF_NODE_PROPERTIES + "--mappings--listB",
  DF_JOIN_MAPPINGS_COLUMNS_LISTA: DB_VIEW.DF_JOIN_PROPERTIES + "--mappings--listA",
  DF_JOIN_MAPPINGS_COLUMNS_LISTB: DB_VIEW.DF_JOIN_PROPERTIES + "--mappings--listB",
  ILT_INPUT_TABLE_ELEMENTS: DB_VIEW.ILT_INPUT_PROPERTIES + "--tableElements",
  ILT_ADDITIONAL_SETTINGS: DB_VIEW.ILT_INPUT_PROPERTIES + "--additionalsettingsform",
  ILT_LOOKUP_TABLE_ELEMENTS: DB_VIEW.ILT_LOOKUP_PROPERTIES + "--tableElements",
  ILT_INPUT_COLUMNS: DB_VIEW.ILT_INPUT_PROPERTIES + "--additionalColumnElements",
  ILT_LOOKUP_COLUMNS: DB_VIEW.ILT_LOOKUP_PROPERTIES + "--additionalColumnElements",
  ILT_RULE_TYPE: DB_VIEW.ILT_RULE_PROPERTIES + "--ruleMatchTypeSelect",
  ILT_RULE_MATCHTYPE:
    "shellMainContent---databuilderComponent---databuilderWorkbench--RuleDataPreview--multiple--mmruleMatchCandidatesSelect",
  ILT_FUZZY_ADAVANCED_SETTING_LIST: DB_VIEW.ILT_RULE_PROPERTIES + "--fuzzyCalculationModeSelect",
  ILT_MATCH_KPI_SELECT: DB_VIEW.ILT_INPUT_PROPERTIES + "--nodematchKPISelect",
  ILT_RULE_MAPPING_LISTA: DB_VIEW.ILT_RULE_PROPERTIES + "--mappings--listA",
  ILT_RULE_MAPPING_LISTB: DB_VIEW.ILT_RULE_PROPERTIES + "--mappings--listB",
  ILT_RULE_MAPPING_FULLSCREEN_LISTA: DB_VIEW.ILT_RULE_PROPERTIES_FULLSCREEN + "--mappings--listA",
  ILT_RULE_MAPPING_FULLSCREEN_LISTB: DB_VIEW.ILT_RULE_PROPERTIES_FULLSCREEN + "--mappings--listB",
  ILT_OUTPUTVIEW_COLUMNS_ELEMENTS: DB_VIEW.ILT_OUTPUT_PROPERTIES + "--tableElements",
  ILT_COLUMN_SELECT_INPUT_LIST: "fuzzySearchInputColumnSelector--columnSelectDialog-list",
  ILT_COLUMN_SELECT_LOOKUP_LIST: "fuzzySearchLookupColumnSelector--columnSelectDialog-list",
  ILT_COLUMNS_LIST_FUZZYA: DB_VIEW.ILT_RULE_PROPERTIES + "--tableAElements",
  ILT_COLUMNS_LIST_FUZZYB: DB_VIEW.ILT_RULE_PROPERTIES + "--tableBElements",
  DF_NODE_FILTER_COLUMNS_SELECT:
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--filterColumns-" +
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--filterValues-",
  DF_NODE_OPERATORS_SELECT:
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--operators-" +
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--filterValues-",
  DF_NODE_FILTER_CONDITIONS_LIST: DB_VIEW.DF_NODE_PROPERTIES + "--filterConditionsList",
  DF_NODE_DEPENDENT_OBJECT_LIST: DB_VIEW.DF_NODE_PROPERTIES + "--dependentObjectList",
  DF_FILTER_VALUES_LIST: DB_VIEW.DF_SOURCE_FILTERS_DIALOG + "--view--filterValues",
  DF_NODE_EXCEL_SHEET_LIST: "nodeProperties--excelSheetList",
  TC_NODE_LIST: DB_VIEW.TC_MODEL_PROPERTIES + "--nodesTokenList",
  USER_SETTINGS_SECTION_LIST: "userSettings---userSettingsView--userSettingsSectionList",
  VIEW_PARTITION_DIALOG_COLUMN_SELECT: DB_CONTROL.VIEW_PARTITION_DIALOG + "--columnCombo",
  REMOTE_TBL_PARTITION_DIALOG_COLUMN_SELECT: DB_CONTROL.REMOTE_TBL_PARTITION_DIALOG + "--columnCombo",
  GV_CALCULATED_SOURCE_CURRENCY_POPUP_LIST: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--sourceCurrency-popup-list",
  DF_JOIN_OPT_SELECT: DB_VIEW.DF_JOIN_ADVANCED_PROP_DIALOG + "--joinOptimizationTypeSelect",
  DF_JOIN_LEFT_CACHE_SELECT: DB_VIEW.DF_JOIN_ADVANCED_PROP_DIALOG + "--leftCacheSelect",
  DF_JOIN_RIGHT_CACHE_SELECT: DB_VIEW.DF_JOIN_ADVANCED_PROP_DIALOG + "--rightCacheSelect",
  DF_REMOTE_SOURCE_ADD_COLUMN_ROWS: "remoteSourceColumnSelector--columnSelectDialog-table",
  DF_REMOTE_SOURCE_NEW_COLUMN_LIST: DB_VIEW.DF_REMOTE_SOURCE_REFRESH_DIALOG + "--view--addColumnsList",
  DF_REMOTE_SOURCE_DELETED_COLUMN_LIST: DB_VIEW.DF_REMOTE_SOURCE_REFRESH_DIALOG + "--view--deletedColumnsList",
  DF_REMOTE_SOURCE_KEY_COLUMN_LIST: DB_VIEW.DF_REMOTE_SOURCE_REFRESH_DIALOG + "--view--keyFieldModifiedList",
  SQL_VIEW_COMPOUND_KEY_LIST: DB_VIEW.ENTITY_PROPERTY_VIEW + "--keyColumnEditDialogView--keyColumnList",
  SQL_VIEW_REPRESENTATIVE_KEY_LIST: DB_VIEW.ENTITY_PROPERTY_VIEW + "--keyColumnEditDialogView--representativelist",
  TABLE_TABLE_REPRESENTATIVE_KEY_LIST: "tableEditor--keyColumnEditDialogView--representativelist",
  DF_REMOTE_CONNECTION_LIST: DB_VIEW.DF_NODE_PROPERTIES + "--tbConnectionSelect",
  DF_REMOTE_SCHEMA_LIST: DB_VIEW.DF_NODE_PROPERTIES + "--remoteSchemaSelect",
  RF_TRANSFORMATION_LIST: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--rfTransformation",
  RF_TARGET_COLUMNS_LIST: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--targetTableElements",
  RF_FILTER_EXPRESSION_LIST: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--Filter_operator_select",
  TE_TABLE_EDITOR_PARENT_ELEMENT_LIST: "tableEditor--parentElementId",
  TE_TABLE_EDITOR_CHILD_ELEMENT_LIST: "tableEditor--childElementId",
  HIERARCHY_WITH_DIRECTORY_VALIDATIONS: "validationsView--validations--listlistPage",
  TRANSFORMATION_DIALOG_FILTER_TAB_EXP_LIST: DB_CONTROL.TRANSFORMATION_DIALOG + "--rf-filter-exp-list",
  TRANSFORMATION_DIALOG_FILTER_TAB_TOKEN_LIST: DB_CONTROL.TRANSFORMATION_DIALOG + "--rf-filter-token-list",
  RF_GROUP_DELTA_BY_LIST: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--fileGroupDeltaFilesBy",
  RF_FILE_TYPE_LIST: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--fileFormat",
  RF_FILE_COMPRESSION_LIST: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--fileCompression",
  RF_FILE_HEADER_LIST: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--fileIsHeaderIncluded",
  RF_KAFKA_MESSAGE_ENCODER_LIST: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--kafkaMessageEncoder",
  RF_KAFKA_MESSAGE_COMPRESSION_LIST: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--kafkaCompressionFormat",
  RF_KAFKA_PANEL_MESSAGE_ENCODER_LIST: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--kafkaMessageEncoder",
  RF_KAFKA_PANEL_MESSAGE_COMPRESSION_FORMAT_LIST: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--kafkaCompressionFormat",
  RF_TRANSFORMATION_DIALOG_FILTER_LEFT_PANEL_TOKEN_LIST_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--rf-filter-token-list",
  RF_TRANSFORMATION_DIALOG_FILTER_EXPRESSION_LIST_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--rf-filter-exp-list",
  RF_LOAD_TYPE_DELTA: DB_VIEW.RF_EDITOR + "--loadtypeInitial-replicationFlowBuilder--replicationFlowMainTable",
  RF_LOAD_TYPE_ALL: DB_VIEW.RF_EDITOR + "--loadtypeAll-replicationFlowBuilder--replicationFlowMainTable",
  ER_TABLES_LIST: DB_VIEW.ER_MODEL_PRO + "--modelPropertiesTablesTokenList",
  DF_NODE_PARAMETER_COLUMNS_SELECT:
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--odataParameters-" +
    DB_VIEW.DF_SOURCE_FILTERS_DIALOG +
    "--view--filterValues-",
  DF_NODE_PARAMTER_CONDITIONS_LIST: DB_VIEW.DF_NODE_PROPERTIES + "--parametersList",
  DF_NODE_DEPTH_VALUE_SELECT: "nodeProperties--depthValue",
  RF_DATASET_SETTING_GROUP_DELTA_BY_LIST: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--fileGroupDeltaFilesBy",
  RF_DATASET_SETTING_FILE_TYPE_LIST: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--fileFormat",
  RF_DATASET_SETTING_FILE_ORIENT_LIST: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--fileOrient",
  RF_DATASET_SETTING_FILE_COLUMN_DELIMITER: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--fileColumnDelimiter",
  RF_DATASET_SETTING_FILE_HEADER: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--fileIsHeaderIncluded",
  RF_DATASET_SETTING_FILE_COMPRESSION_LIST: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--fileCompression",
  HIERARCHY_NAME_COLUMN_LIST: "ExternalDirectoryHierarchyEditor--hierarchyNameColumn-list",
  TF_VIEW_TRANSFORM_NODE_COLUMNS_LIST: DB_VIEW.TF_VIEW_TRANSFORM_NODE_PROPERTIES + "--tableElements",
  TF_TARGET_NODE_COLUMNS_LIST: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--tableElements",
  TF_SOURCE_TABLE_NODE_PROPERTIES_LIST: DB_VIEW.TF_SOURCE_TABLE_NODE_PROPERTIES + "--tableElements",
  TF_SQL_SECONDARY_EDITOR_COLUMNS_LIST: DB_VIEW.TF_SECONDARY_SQL_EDITOR_PROPERTIES + "--tableElements",
  TF_SQL_SECONDARY_EDITOR_PARAMETERS_LIST: DB_VIEW.TF_SECONDARY_SQL_EDITOR_PROPERTIES + "--gvParametersList",
  TF_SQL_SECONDARY_EDITOR_TYPE: DB_VIEW.TF_SECONDARY_SQL_EDITOR_PROPERTIES + "--dbViewTypeTF",
  TF_LOAD_TYPE_SELECT: DB_VIEW.TF_MODEL_PROPERTIES + "--loadType",
  TF_PACKAGE_SELECT: DB_VIEW.TF_MODEL_PROPERTIES + "--packageSelector-select",
  TF_TARGET_PACKAGE_SELECT: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--packageSelector-select",
  MV_CONNECTION_LIST: "validateRemoteTablesDlg--vConnectionsList",
  RF_ABAP_EXIT: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--abapExitSelect",
  RF_CONTENT_TYPE:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--replicationflow-properties-ReplicationFlowProperties--abapContentType",
  TF_SECONDARY_GV_EDITOR_COLUMN_LIST: DB_VIEW.TF_SECONDARY_GV_EDITOR_PROPERTIES + "--tableElements",
  TF_PARAMETERS_LIST: DB_VIEW.TF_SECONDARY_GV_EDITOR_PROPERTIES + "--gvParametersList",
  RF_CONFLUENT_MESSAGE_ENCODER_LIST: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--confluentMessageEncoder",
  RF_CONFLUENT_MESSAGE_ENCODER_LIST_PANEL: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentMessageEncoder",
  RF_CONFLUENT_COMPRESSION_TYPE_LIST: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--confluentCompressionFormat",
  RF_CONFLUENT_COMPRESSION_TYPE_LIST_PANEL: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentCompressionFormat",
  RF_CONFLUENT_SUBJECTNAMAE_STRAT_LIST: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--confluentSubjectNameStrat",
  RF_CONFLUENT_COMPATABILITY_LIST: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--confluentCompatabilityMode",
  RF_CONFLUENT_SUBJECTNAMAE_STRAT_LIST_PANEL: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentSubjectNameStrat",
  RF_CONFLUENT_COMPATABILITY_LIST_PANEL: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentCompatabilityModel",
  RF_CONFLUENT_SOURCE_SETTING_STARTING_POINT_SELECT:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--confluentOffset",
  RF_CONFLUENT_SOURCE_SETTING_STARTING_POINT_PANEL_SELECT: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentOffset",
  RF_TARGET_TABLE_ELEMENT_LIST: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--targetTableElements",
  RF_SOURCE_TABLE_ELEMENT_LIST: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--rf-filter-token-list",
  RF_CONFLUENT_SCHEMA_DIALOG_SUBJECT_LIST: DB_VIEW.SCHEMA_DAILOG_ID + "--searchField",
  RF_CONFLUENT_SCHEMA_DIALOG_VERSION_LIST: DB_VIEW.SCHEMA_DAILOG_ID + "--versionName",
  RF_CONFLUENT_SCHEMA_DIALOG_EXPAND_ARRAY_OR_MAP_LIST: DB_VIEW.SCHEMA_DAILOG_ID + "--expandArrayOrMap",
  RF_CONFLUENT_SCHEMA_DIALOG_EXPAND_OMIT_NON_EXPANABLE_ARRAYS:
    DB_VIEW.SCHEMA_DAILOG_ID + "--omitNonExpandedArraysSwitch",
  TF_SOURCE_PARAMETER_LIST: "secondaryEditorSourceParameterPanel--sourceParametersList",
  RF_OBJECT_LOAD_TYPE_SELECT: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--replicationLoadTypeAll",
  TF_SCRIPT_COLUMNS_LIST: DB_VIEW.TF_PYTHON_NODE_PROPERTIES + "--tableElements",
  TF_SCRIPT_COLUMNS_DATATYPE: DB_CONTROL.TF_CREATE_NEW_COLUMN_DIALOG + "--view--columnDatatype",
  TF_SECONDARY_SOURCE_COLUMNS: DB_VIEW.TF_SECONDARY_SOURCE_PROPERTIES + "--tableElements",
  TF_PARAMETERS_DATATYPE_SELECT: "tfInputParametersEditor--datatype",
  TF_USER_DEFINED_PARAMETERS_LIST: DB_VIEW.TF_MODEL_PROPERTIES + "--parametersList",
  TF_DIALOG_PARAMETERS_LIST: "tfInputParametersEditor--parametersList",
};

export const DB_TABLE = {
  MAD_T: "MassActionDialog--table",
  CROSS_SPACE_SHARE_DIALOG_TABLE: DB_VIEW.CROSS_SPACE_SHARE_DIALOG + "--sharingTable",
  COPY_ASSOCIATION_TABLE: DB_VIEW.ROOT + "sourceAssociationsControl",
  GV_CONVERSION_TYPE_TABLE: "conversionType--targetTable",
  GV_CONFIG_TABLES_TABLE: "configTables--targetTable",
  GV_SOURCE_CURRENCY_FIXEDVALUE_TABLE: "sourceCurrency--fixedValueTable",
  GV_TARGET_CURRENCY_FIXEDVALUE_TABLE: "targetCurrency--fixedValueTable",
  GV_TARGET_CURRENCY_COLUMNS_TABLE: "targetCurrency--columnsTable",
  GV_REFERENCE_DATE_INPUT_PARAMETER_TABLE: "targetCurrency--inputParametersTable",
  ER_ADD_RELATED_DIALOG_TABLE: DB_VIEW.ROOT + "suggestionTable",
  PREVIEW_ERRORS: AB_VIEW.GENWORKBENCH + "--problemsTable-table",
  TE_ATTRIBUTES: DB_VIEW.TABLE_EDITOR + "--attributesTable",
  TE_MEASURES: DB_VIEW.TABLE_EDITOR + "--measuresTable",
  TE_ASSOCIATIONS: DB_VIEW.TABLE_EDITOR + "--associationsList",
  TE_DEPENDENT_OBJECT_LIST: DB_VIEW.TABLE_EDITOR + "--dependentObjectList",
  ER_EDIT_ATTRIBUTE_DIALOG_TABLE: DB_VIEW.ER_ENTITY + "--editAttributesDialogView--editModeBusinessAttributesTable",
  ER_EDIT_MEASURE_DIALOG_TABLE: DB_VIEW.ER_ENTITY + "--editMeasuresDialogView--editModeBusinessMeasuresTable",
  DBH_SPACE_ENTITY_TABLE: DB_VIEW.DBH + "--spaceEntityTable",
  ER_MODEL_PRO_CONTEXTS_LIST: DB_VIEW.ER_MODEL_PRO + "--modelPropertiesContextsTokenList",
  ER_MODEL_PRO_TABLES_LIST: DB_VIEW.ER_MODEL_PRO + "--modelPropertiesTablesTokenList",
  ER_MODEL_PRO_VIEWS_LIST: DB_VIEW.ER_MODEL_PRO + "--modelPropertiesViewsTokenList",
  GV_OUTPUT_EDIT_ATTRIBUTES:
    DB_VIEW.OUTPUT + "--EntityPropertiesView--editAttributesDialogView--editModeBusinessAttributesTable",
  GV_DATA_PREVIEW_TABLE: AB_TABLE.DATA_PREVIEW_TABLE,
  MDM_PREVIEW_ERROR_TABLE_REOPEN: AB_TABLE.MDM_PREVIEW_ERROR_TABLE_REOPEN,
  MDM_PREVIEW_TABLE: AB_TABLE.MDM_PREVIEW_TABLE,
  MDM_PREVIEW_TABLE_REOPEN: AB_TABLE.MDM_PREVIEW_TABLE_REOPEN,
  MDM_PREVIEW_ERROR_TABLE: AB_TABLE.MDM_PREVIEW_ERROR_TABLE,
  ER_REMOTE_OBJECTS_SELECTOR_SOURCE_TABLE: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--sourceTable",
  ER_REMOTE_OBJECTS_SELECTOR_SELECTION_TABLE: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--selectionTable",
  PREVIEW_SQL_DIALOG_PROBLEMS: DB_CONTROL.PREVIEW_SQL_DIALOG + "--previewSQLProblemsTable-table",
  SELECT_ASSOCIATION_TABLE: DB_VIEW.ROOT + "targetTable",
  SQL_PREVIEW_ERROR: DB_VIEW.DBWB + "--problemsTable",
  CHANGE_MANAGEMENT_DIALOG_TABLE: DB_VIEW.CHANGE_MANAGEMENT_DIALOG + "--view--changesTable",
  SQL_CHANGE_MANAGEMENT_DIALOG_TABLE: DB_VIEW.SQL_CHANGE_MANAGEMENT_DIALOG + "--view--changesTable",
  DF_CHANGE_MANAGEMENT_DIALOG_TABLE: DB_VIEW.DF_CHANGE_MANAGEMENT_DIALOG + "--view--changesTable",
  TE_ROW1: DB_VIEW.TABLE_EDITOR + "--attributesTable-rows-row0",
  TE_ROW2: DB_VIEW.TABLE_EDITOR + "--attributesTable-rows-row1",
  TE_ROW3: DB_VIEW.TABLE_EDITOR + "--attributesTable-rows-row2",
  TE_FILEUPLOAD_PREVIEW_TABLE: DB_VIEW.UPLOADCSV_FILEUPLOADER + "--view--previewTable",
  TE_FILEUPLOAD_PREVIEW_TABLE_HEADER: DB_VIEW.UPLOADCSV_FILEUPLOADER + "--view--previewTable--selectHeader",
  TE_FILEUPLOAD_PREVIEW_TABLE_CELL_0_1: DB_VIEW.UPLOADCSV_FILEUPLOADER + "--view--previewTable-rows-row0-col1",
  DBH_IMPORT_CSN_SELECTION_TABLE:
    DB_VIEW.ROOT +
    "shellMainContent---databuilderComponent---databuilderLandingPage--addRepositoryObjectsDlg--tableSelectDialog-table",
  DATA_PREVIEW_COLUMNS_CONTROL_DIALOG_TABLE: "__table0",
  PARAMETERS_MAPPING_TABLE: DB_CONTROL.PARAMETERS_MAPPING_DIALOG + "--view--parameterMappingTable",
  GV_DATA_PREVIEW_TABLE_INPUT_PARAMETER_INFO_BAR:
    "shellMainContent---databuilderComponent---databuilderWorkbench--detailsTabContainer-content",
  GV_INPUT_PARAMETER_VALUE_DIALOG_OK_BUTTON:
    "sap-cdw-components-databuilder-view-InputParameterDataPreview--dialog--view--ok",
  GV_INPUT_PARAMETER_VALUE_DIALOG_CANCEL_BUTTON:
    "sap-cdw-components-databuilder-view-InputParameterDataPreview--dialog--view--cancel",
  ILT_OUTPUT_PREVIEW_TABLE: "shellMainContent---databuilderComponent---databuilderWorkbench--Output",
  IL_CHANGE_MANAGEMENT_DIALOG_TABLE: DB_VIEW.IL_CHANGE_MANAGEMENT_DIALOG + "--view--changesTable",
  VIEW_MONITOR_TBL: DB_VIEW.VIEW_MONITOR_PAGE + "--viewMonitorTable",
  CONNECTIONS_TABLE: "importWizardView--connectionsView--connectionListTable",
  REMOTE_TABLE: "importWizardView--objectsView--remoteTables",
  TO_IMPORT_TABLE: "importWizardView--recapView--tableToImport",
  DATA_ACCESS_CONTROL: "targetDataAccessControl",
  DF_JOIN_ADD_COLUMNS_TABLE: DB_VIEW.DF_ADD_COLUMN_DIALOG + "--view--inputColumnTreeTable",
  ARTEFACT_DEPLOY_TABLE: DB_VIEW.ARTEFACT_DEPLOY_DIALOG + "--artefactDeployCatSelection",
  RF_EDITOR_MAIN_CANVAS: DB_VIEW.RF_EDITOR + "--replicationFlowMainTable",
  DATA_PREVIEW_FIRST_COLUNM_FIRST_ROW: DB_VIEW.DBWB + "--previewTable-rows-row0-col0",
  TRANSFORMATION_DIALOG_TABLE_ID: DB_CONTROL.TRANSFORMATION_DIALOG + "--transformationTable",
  RF_CONNECTION_DIALOG_TABLE: "ConnectionDialog-table",
  RF_SOURCE_CONTAINER_DIALOG_TREE_TABLE: "containerTreeList",
  RF_TARGET_CONTAINER_DIALOG_TREE_TABLE: "containerTreeList",
  RF_ADD_SOURCE_DATASET_OBJECT_TABLE_ID: DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--sourceTable-table",
  RF_ADD_SOURCE_DATASET_OBJECT_SELECT_BOX_CONTAINER_ID:
    DB_VIEW.ER_REMOTE_OBJECTS_SELECTOR + "--sourceTable-sapUiTableRowHdrScr",
  RF_CONNECTION_TABLE_ID: "ConnectionDialog-table-listUl",
  RF_MAIN_CANVAS_NEW_TARGET_LABEL_ID:
    DB_VIEW.RF_EDITOR + "--newTargetObjectID-" + DB_VIEW.RF_EDITOR + "--replicationFlowMainTable-",
  RF_MAIN_CANVAS_EXISTING_TARGET_LABEL_ID:
    DB_VIEW.RF_EDITOR + "--existingTargetObjectID-" + DB_VIEW.RF_EDITOR + "--replicationFlowMainTable-",
  RF_LOCAL_REPO_BROWSER_DIALOG_ID: DB_VIEW.RF_EDITOR + "--addRepositoryObjectsDlg--tableSelectDialog-table",
  RF_MAIN_CANVAS_TARGET_COLUMN_HIDDEN_CELL_ID:
    "sap-ui-invisible-" +
    DB_VIEW.RF_EDITOR +
    "--targetObjectColumnID-" +
    DB_VIEW.RF_EDITOR +
    "--replicationFlowMainTable-",
  RF_TRANSFORMATION_MAPPING_SELECT_CHECKBOX: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--transformationTable-rowsel",
  SAVE_DIALOG_TABLE: "saveModelDialog--folderselector--foldersTable",
  TF_CHANGE_MANAGEMENT_DIALOG_TABLE: DB_VIEW.TF_CHANGE_MANAGEMENT_DIALOG + "--view--changesTable",
  TF_SECONDARY_SQL_EDITOR_EDIT_COLUMN_DIALOG_TABLE:
    DB_VIEW.TF_SECONDARY_SQL_EDITOR_PROPERTIES + "--editColumnsDialogView--editModeBusinessAttributesTable",
  REMOTETABLES_TABLE: "validateRemoteTablesDlg--remoteTables",
  TF_PARAMETER_MAPPING_TABLE: "parameterMappingTable",
  ODATA_PARAMETER_TABLE: DB_VIEW.ODATA_VIEW_API_BUILDER + "--parameters",
  ODATA_RANGE_VAR_SELECT: DB_VIEW.ODATA_VIEW_API_BUILDER + "--rangeOperatorSelect",
  ODATA_INTERVAL_VAR_SELECT: DB_VIEW.ODATA_VIEW_API_BUILDER + "--intervalOperatorSelect",
  RF_VERSION_DIALOG_TABLE: "vhDialog--table",
};

export const DB_COLUMN = {
  DBC_OBJECT_BUSINESS_NAME: DB_VIEW.DBH + "--spaceEntityTabledescriptionCol",
  DBC_OBJECT_NAME: DB_VIEW.DBH + "--spaceEntityTableobjectNameCol",
  DBC_OBJECT_CREATOR: DB_VIEW.DBH + "--spaceEntityTablecreatorCol",
  DBC_OBJECT_TYPE: DB_VIEW.DBH + "--spaceEntityTabletypeCol",
  DBC_OBJECT_STATUS: DB_VIEW.DBH + "--spaceEntityTablestatusCol",
};

export const DB_COLUMN_MENU = {
  DBCM_OBJECT_NAME_DESC: DB_COLUMN.DBC_OBJECT_NAME + "-menu-desc",
  DBCM_OBJECT_NAME_FILTER: DB_COLUMN.DBC_OBJECT_NAME + "-menu-filter",
  DBCM_OBJECT_BUSINESS_NAME_TEXT: DB_COLUMN.DBC_OBJECT_BUSINESS_NAME + "-menu-filter-tf",
  DBCM_OBJECT_NAME_FILTER_TEXT: DB_COLUMN.DBC_OBJECT_NAME + "-menu-filter-tf",
  DBCM_OBJECT_CREATOR_FILTER_TEXT: DB_COLUMN.DBC_OBJECT_CREATOR + "-menu-filter-tf",
  DBCM_OBJECT_TYPE_FILTER_TEXT: DB_COLUMN.DBC_OBJECT_TYPE + "-menu-filter-tf",
  DBCM_OBJECT_STATUS_FILTER_TEXT: DB_COLUMN.DBC_OBJECT_STATUS + "-menu-filter-tf",
};

export const DB_TEXT = {
  MAD_AI: "MassActionDialog--availableInfo",
  MAD_AB: "MassActionDialog--availableButton-button",
  MAD_NAB: "MassActionDialog--notAvailableButton-button",
  TREEVIEW_REMOTETREE0:
    "shellMainContent---databuilderComponent---databuilderWorkbench--TreeView--treeItem-shellMainContent---databuilderComponent---databuilderWorkbench--TreeView--remoteTree-0",
  GV_EXPRESSION_EDITOR_VALUE_HELP_LIST: DB_VIEW.GV_FILTER + "--subStatus--valueListItems",
  GV_EXPRESSION_EDITOR_CC_VALUE_HELP_LIST: DB_VIEW.GV_CALCULATED + "--subStatus--valueListItems",
  GV_EXPRESSION_EDITOR_AGGR_VALUE_HELP_LIST: DB_VIEW.GV_AGGREGATED + "--subStatus--valueListItems",
  DATA_CLEANSING_LABEL: DB_VIEW.GV_NODE_RENAME_CALCULATED + "--dataCleansingGeoValueId",
  GV_ENTITY_DEPLOY_STATUS_LABEL: DB_VIEW.GV_ENTITY + "--deployStatusLabel-bdi",
  ENTITY_PROPERTY_VIEW_DEPLOY_STATUS_LABEL: DB_VIEW.ENTITY_PROPERTY_VIEW + "--deployStatusLabel-bdi",
  ENTITY_BUSINESS_PANEL_TITLE: DB_VIEW.ENTITY_PROPERTY_VIEW + "--businessPanelTitle",
  GV_BACK_TEXT: DB_VIEW.OUTPUT + "--EntityPropertiesView--detailsHeader--backBreadCrumbs-currentText",
  GV_ANALYTIC_MEASURE_POPOVER_BNAME: DB_VIEW.GV_ANALYTIC_MEASURE_POPOVER + "--businessName",
  GV_ANALYTIC_MEASURE_POPOVER_TNAME: DB_VIEW.GV_ANALYTIC_MEASURE_POPOVER + "--businessTechnicalName",
  GV_ANALYTIC_MEASURE_POPOVER_DATATYPE: DB_VIEW.GV_ANALYTIC_MEASURE_POPOVER + "--displayDataTypeText",
  GV_ANALYTIC_MEASURE_POPOVER_REFERENCE_ATTRIBUTES: DB_VIEW.GV_ANALYTIC_MEASURE_POPOVER + "--referenceAttributes",
  GV_ANALYTIC_MEASURE_POPOVER_EXPRESSION: DB_VIEW.GV_ANALYTIC_MEASURE_POPOVER + "--formulaExpression",
  GV_ANALYTIC_MEASURE_POPOVER_AGGTYPE: DB_VIEW.GV_ANALYTIC_MEASURE_POPOVER + "--aggregationType",
  ENTITY_HEADER_TEXT: DB_VIEW.GV_ENTITY + "--propertyPanelHeaderText",
  ER_ENTITY_HEADER_TEXT: DB_VIEW.OUTPUT + "--propertyPanelHeaderText",
  ENTITY_DEPENDENT_OBJECT_MISS_REPO_ID: DB_VIEW.GV_ENTITY + "--dependentObjectMissingRepoIdMessageStrip",
  ENTITY_DEPENDENT_OBJECT_NO_ACCESS_COUNT: DB_VIEW.GV_ENTITY + "--numberOfItemsWithoutPermissionToView",
  OUTPUT_DEPLOYMENT_STATUS: DB_VIEW.OUTPUT + "--EntityPropertiesView--deploymentStatusText",
  OUTPUT_OBJECT_STATUS: DB_VIEW.OUTPUT + "--EntityPropertiesView--objectStatusText",
  OUTPUT_DEPENDENT_OBJECT_MISS_REPO_ID:
    DB_VIEW.OUTPUT + "--EntityPropertiesView--dependentObjectMissingRepoIdMessageStrip",
  OUTPUT_DEPENDENT_OBJECT_NO_ACCESS_COUNT:
    DB_VIEW.OUTPUT + "--EntityPropertiesView--numberOfItemsWithoutPermissionToView",
  ER_ENTITY_OBJECT_STATUS: DB_VIEW.ER_ENTITY + "--objectStatusText",
  ER_ENTITY_DEPENDENT_OBJECT_MISS_REPO_ID: DB_VIEW.ER_ENTITY + "--dependentObjectMissingRepoIdMessageStrip",
  GV_ENTITY_OBJECT_STATUS: DB_VIEW.GV_ENTITY + "--objectStatusText",
  GV_OUTPUT_DEPLOYMENT_STATUS: DB_VIEW.GV_ENTITY + "--DeploymentStatusTxt",
  OUTPUT_HIDDDEN_VALUE: DB_VIEW.OUTPUT + "--EntityPropertiesView--ElemInfoPopover--hiddenValue",
  OUTPUT_KEY_VALUE: DB_VIEW.OUTPUT + "--EntityPropertiesView--ElemInfoPopover--isKey",
  OUTPUT_BUSINESS_NAME: DB_VIEW.OUTPUT + "--EntityPropertiesView--ElemInfoPopover--businessName",
  OUTPUT_SEMANTIC_TYPE: DB_VIEW.OUTPUT + "--EntityPropertiesView--ElemInfoPopover--displaySemanticType",
  AGGREGATION_ELEMENT_BN_INFO: DB_VIEW.GV_AGGREGATED + "--ElemInfoPopover--businessName",
  AGGREGATION_ELEMENT_TN_INFO: DB_VIEW.GV_AGGREGATED + "--ElemInfoPopover--businessTechnicalName",
  VIEW_PERSISTENCY_STATUS: DB_VIEW.OUTPUT + "--EntityPropertiesView--viewPersistenceStatusText",
  EDIT_COLUMNS_DIALOGTITLE: DB_VIEW.OUTPUT + "--EntityPropertiesView--editAttributesDialog-title-inner",
  GV_ENTITY_BUSINESS_NAME: DB_VIEW.GV_ENTITY + "--ElemInfoPopover--businessName",
  GV_FILTER_EXPR_VALIDATESTATUS: DB_VIEW.GV_FILTER + "--statusMessageStrip",
  GV_OUTPUT_PROPERTY_PANEL_NAME: DB_VIEW.OUTPUT + "--EntityPropertiesView--detailsHeader--nameLabel",
  GV_DATA_PREVIEW_TAB_TEXT: AB_TEXT.DATA_PREVIEW_TAB_TEXT,
  DATA_PREVIEW_FILTER_INFO: DB_VIEW.DBWB + "--filterInfo",
  DATA_PREVIEW_PROBLEMS_TAB_TEXT: AB_TEXT.DATA_PREVIEW_PROBLEMS_TAB_TEXT,
  DATA_PREVIEW_PROBLEMS_TAB_COLUMN1_TITLE: AB_TEXT.DATA_PREVIEW_PROBLEMS_TAB_COLUMN1_TITLE,
  DATA_PREVIEW_PROBLEMS_TAB_COLUMN2_TITLE: AB_TEXT.DATA_PREVIEW_PROBLEMS_TAB_COLUMN2_TITLE,
  DATA_PREVIEW_PROBLEMS_TAB_COLUMN3_TITLE: AB_TEXT.DATA_PREVIEW_PROBLEMS_TAB_COLUMN3_TITLE,
  DATA_PREVIEW_PROBLEMS_TAB_NODATA: AB_TEXT.DATA_PREVIEW_PROBLEMS_TAB_NODATA,
  ERROR_POPOVER_MSG_TITLE: "errorPopover-messageViewMessageTitleText",
  ERROR_POPOVER_MSG_DESC: "errorPopover-messageViewMessageDescriptionText",
  OUTPUT_DATACATEGORY_TEXT: DB_BUTTON.GV_OUTPUT_TYPE_SELECT + "-labelText",
  TECHNICAL_NAME_INFO: DB_VIEW.OUTPUT + "--EntityPropertiesView--AssociationInfoPopover--technicalNameInfo",
  BUSINESS_NAME_INFO: DB_VIEW.OUTPUT + "--EntityPropertiesView--AssociationInfoPopover--businessNameInfo",
  FROM_INFO_DAC: DB_VIEW.OUTPUT + "--EntityPropertiesView--DacInfoPopover--fromInfo",
  FROM_INFO: DB_VIEW.OUTPUT + "--EntityPropertiesView--AssociationInfoPopover--fromInfo",
  TO_INFO: DB_VIEW.OUTPUT + "--EntityPropertiesView--AssociationInfoPopover--toInfo",
  ER_ENTITY_SPACE_OR_CONTEXT_NAME_LABEL: DB_VIEW.ER_ENTITY + "--spaceOrContextNameLabel",
  GV_ENTITY_SPACE_OR_CONTEXT_NAME_LABEL: DB_VIEW.GV_ENTITY + "--spaceOrContextNameLabel",
  GV_JOIN_MAPPINGS_INFO_POPOVER_HIDDEN_VALUE: DB_VIEW.GV_JOIN + "--ElemInfoPopover--hiddenValue",
  GV_UNION_MAPPINGS_INFO_POPOVER_HIDDEN_VALUE: DB_VIEW.GV_UNION + "--ElemInfoPopover--hiddenValue",
  DF_NODE_PROPERTIES_OBJECT_STATUS: DB_VIEW.DF_NODE_PROPERTIES + "--objectStatusText",
  DF_SCRIPT_COL_INFO_POPOVER_NAME: DB_VIEW.DF_SCRIPT_PROPERTIES + "--ColInfoPopover--name",
  DF_SCRIPT_COL_INFO_POPOVER_DATATYPE: DB_VIEW.DF_SCRIPT_PROPERTIES + "--ColInfoPopover--datatypetext",
  DF_PROJECTION_COL_INFO_POPOVER_NAME: DB_VIEW.DF_PROJECTION_PROPERTIES + "--ColInfoPopover--name",
  DF_PROJECTION_COL_INFO_POPOVER_DATATYPE: DB_VIEW.DF_PROJECTION_PROPERTIES + "--ColInfoPopover--datatypetext",
  DF_PROJECTION_COL_INFO_POPOVER_QUALIFIED_NAME: DB_VIEW.DF_PROJECTION_PROPERTIES + "--ColInfoPopover--qualifiedname",
  DF_PROJECTION_COL_INFO_POPOVER_SOURCE: DB_VIEW.DF_PROJECTION_PROPERTIES + "--ColInfoPopover--source",
  DF_AGGREGATION_COL_INFO_POPOVER_NAME: DB_VIEW.DF_AGGREGATION_PROPERTIES + "--ColInfoPopover--name",
  DF_AGGREGATION_COL_INFO_POPOVER_DATATYPE: DB_VIEW.DF_AGGREGATION_PROPERTIES + "--ColInfoPopover--datatypetext",
  DF_UNION_COL_INFO_POPOVER_NAME: DB_VIEW.DF_UNION_PROPERTIES + "--ColInfoPopover--name",
  DF_UNION_COL_INFO_POPOVER_DATATYPE: DB_VIEW.DF_UNION_PROPERTIES + "--ColInfoPopover--datatypetext",
  DF_JOIN_COL_INFO_POPOVER_NAME: DB_VIEW.DF_JOIN_PROPERTIES + "--ColInfoPopover--name",
  DF_JOIN_COL_INFO_POPOVER_SOURCE: DB_VIEW.DF_JOIN_PROPERTIES + "--ColInfoPopover--source",
  DF_FUNCTION_HELPER_SHORT_DESC:
    DB_VIEW.DF_PROJECTION_PROPERTIES +
    "--calcColumnExpression--expressionHelperShortDesc-" +
    DB_VIEW.DF_PROJECTION_PROPERTIES +
    "--calcColumnExpression--functionsList-0",
  ER_ENTITY_INFO_POPOVER_BASE_TYPE: DB_VIEW.ER_ENTITY + "--ElemInfoPopover--baseTypeText",
  ER_ENTITY_INFO_POPOVER_DISPLAY_DATATYPE: DB_VIEW.ER_ENTITY + "--ElemInfoPopover--displayDataTypeText",
  GV_PARAMETER_INFO_POPOVER_DISPLAY_DATATYPE: DB_VIEW.GV_ENTITY + "--ParameterInfoPopover--displayDataTypeText",
  DF_PROJECTION_COLUMNS_TITLE: DB_VIEW.DF_PROJECTION_PROPERTIES + "--columnsTitle-inner",
  DF_SCRIPT_COLUMNS_TITLE: DB_VIEW.DF_SCRIPT_PROPERTIES + "--columnsTitle-inner",
  DF_MODEL_RUN_STATUS: DB_VIEW.DF_MODEL_PROPERTIES + "--runStatus",
  DF_MODEL_LAST_RUN_STATUS: DB_VIEW.DF_MODEL_PROPERTIES + "--lastRunStatus",
  DF_MODEL_DEPLOYMENT_STATUS: DB_VIEW.DF_MODEL_PROPERTIES + "--objectStatusText",
  DF_MODEL_DEPLOYMENT_DATE: DB_VIEW.DF_MODEL_PROPERTIES + "--deploymentDate",
  DF_JOIN_JOIN_TYPE: DB_VIEW.DF_JOIN_OBJECT_PAGE + "--joinTypeSelect-labelText",
  DF_NODE_COL_INFO_POPOVER_NAME: DB_VIEW.DF_NODE_PROPERTIES + "--ColInfoPopover--name",
  DF_NODE_COL_INFO_POPOVER_QUALIFIEDNAME: DB_VIEW.DF_NODE_PROPERTIES + "--ColInfoPopover--qualifiedname",
  DF_NODE_COL_INFO_POPOVER_DATATYPE: DB_VIEW.DF_NODE_PROPERTIES + "--ColInfoPopover--datatypetext",
  DF_NODE_MODES_SELECT: DB_VIEW.DF_NODE_PROPERTIES + "--targetMode-labelText",
  DF_NODE_CSV_PROPERTIES_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--csvPropsTitle-inner",
  DF_NODE_JSON_PROPERTIES_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--jsonPropsTitle",
  DF_NODE_CSV_COL_DEL_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--columnSeperatorLabel",
  DF_NODE_CSV_COL_DEL_VALUE: DB_VIEW.DF_NODE_PROPERTIES + "--columnSeperator",
  DF_NODE_CSV_INC_HEADER_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--includeHeaderLabel",
  DF_NODE_CSV_INC_HEADER_VALUE: DB_VIEW.DF_NODE_PROPERTIES + "--includeHeader",
  DF_NODE_CSV_TEXT_DEL_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--textSeparatorLabel",
  DF_NODE_CSV_TEXT_DEL_VALUE: DB_VIEW.DF_NODE_PROPERTIES + "--textSeparator",
  DF_NODE_CSV_CHAR_SET_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--characterSetLabel",
  DF_NODE_CSV_CHAR_SET_VALUE: DB_VIEW.DF_NODE_PROPERTIES + "--characterSet",
  DF_NODE_CSV_ESC_CHAR_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--escapeCharacterLabel",
  DF_NODE_CSV_ESC_CHAR_VALUE: DB_VIEW.DF_NODE_PROPERTIES + "--escapeCharacter",
  DF_NODE_JSON_MAX_LEVEL_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--maxJsonLevelLabel",
  DF_NODE_JSON_MAX_LEVEL_VALUE: DB_VIEW.DF_NODE_PROPERTIES + "--maxLevel",
  DF_NODE_JSON_FLATTENED_LEVEL_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--flattenedLevelLabel",
  DF_NODE_JSON_FLATTENED_LEVEL_VALUE: DB_VIEW.DF_NODE_PROPERTIES + "--flattenedLevel",
  DF_NODE_COLUMNS_TITLE: DB_VIEW.DF_NODE_PROPERTIES + "--columnsTitle-inner",
  GV_TABLE_HIDDEN_VALUE: DB_VIEW.GV_ENTITY + "--ElemInfoPopover--hiddenValue",
  DF_UNION_COLUMNS_TITLE: DB_VIEW.DF_UNION_PROPERTIES + "--columnsTitle-inner",
  SAVING_TEXTLABEL: DB_VIEW.ROOT + "workbenchBusyIndicator-TextLabel",
  BUSINESS_DESCRIPTION: DB_VIEW.ENTITY_PROPERTY_VIEW + "--description",
  BUSINESS_PURPOSE: DB_VIEW.ENTITY_PROPERTY_VIEW + "--businessDefinitionPurpose",
  BUSINESS_CONTACT_PERSON: DB_VIEW.ENTITY_PROPERTY_VIEW + "--businessDefinitionContact",
  BUSINESS_RESPONSIBLE_TEAM: DB_VIEW.ENTITY_PROPERTY_VIEW + "--responsible",
  BUSINESS_TAGS: DB_VIEW.ENTITY_PROPERTY_VIEW + "--tags",
  GV_JOIN_MAPPINGS_TABLEA_HEADER: DB_VIEW.GV_JOIN + "--mappings--tableA_HeaderHbox",
  GV_JOIN_MAPPINGS_TABLEB_HEADER: DB_VIEW.GV_JOIN + "--mappings--tableB_HeaderHbox",
  DF_NODE_SOURCE_FILTERS_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--datasourceFilterTitle-inner",
  DF_NODE_NO_FILTERS_ADDED: DB_VIEW.DF_NODE_PROPERTIES + "--filterConditionsList-nodata-text",
  DF_NODE_FILTERS_DIALOG_TITLE: DB_VIEW.DF_SOURCE_FILTERS_DIALOG + "-title",
  DF_NODE_NO_FILTERS_ALLOWED: DB_VIEW.DF_NODE_PROPERTIES + "--noFilterAllowedText",
  DF_NODE_FILTERS_INFO_POPOVER: DB_VIEW.DF_NODE_PROPERTIES + "--helpInfoPopover--helpPopoverTitle",
  DF_EXECUTION_ERROR_TEXT: DB_VIEW.DF_MODEL_PROPERTIES + "--errorDetails",
  DF_DATA_PREVIEW_WARNING_MSG: DB_VIEW.DBWB + "--previewMessageStrip",
  DF_NODE_EXCEL_PROPERTIES_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--excelPropertiesTitle",
  DF_NODE_EXCEL_SHEET_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--excelSheetLbl",
  DF_NODE_EXCEL_SHEET_VALUE: DB_VIEW.DF_NODE_PROPERTIES + "--excelSheet",
  DF_NODE_EXCEL_HEADER_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--excelIncludeHeaderLbl",
  DF_NODE_EXCEL_HEADER_VALUE: DB_VIEW.DF_NODE_PROPERTIES + "--excelIncludeHeader",
  DF_CREATE_TABLE_CONFIRMATION_MSG: "createTableConfirmationMessage",
  ILT_RULE_TYPE_TEXT: DB_VIEW.ILT_RULE_PROPERTIES + "--ruleMatchTypeSelect-labelText",
  ILT_RULE_MATCHTYPE_TEXT: DB_LIST.ILT_RULE_MATCHTYPE + "-labelText",
  ILT_FUZZY_FLEXIBLE_MODE_TEXT: DB_VIEW.ILT_RULE_PROPERTIES + "--fuzzyCalculationModeSelect-labelText",
  TE_DEPENDENT_OBJECT_NO_ACCESS_COUNT: DB_VIEW.TABLE_EDITOR + "--numberOfItemsWithoutPermissionToView",
  ILT_MODEL_RUN_STATUS: DB_VIEW.IL_MODEL_PROPERTIES + "--runStatus",
  ILT_MODEL_DEPLOYMENT_STATUS: DB_VIEW.IL_MODEL_PROPERTIES + "--deploymentStatusText",
  ILT_MODEL_BUSINESSNAME: DB_VIEW.IL_MODEL_PROPERTIES + "--businessName",
  ILT_MODEL_TECHNICALNAME: DB_VIEW.IL_MODEL_PROPERTIES + "--technicalName",
  ILT_OUTPUT_BUSINESSNAME: DB_VIEW.ILT_OUTPUT_PROPERTIES + "--businessName",
  ILT_OUTPUT_TECHNICALNAME: DB_VIEW.ILT_OUTPUT_PROPERTIES + "--technicalName",
  ILT_MATCH_SCORE_LABEL: DB_VIEW.ILT_RULE_PROPERTIES + "--lblMatchedRecordsScore",
  ILT_REVIEW_SCORE_LABEL: DB_VIEW.ILT_RULE_PROPERTIES + "--lblReviewRecordsScore",
  ILT_UNMATCH_SCORE_LABEL: DB_VIEW.ILT_RULE_PROPERTIES + "--lblUnmatchedRecordsScore",
  ILT_INPUTTABLE_LABEL: DB_VIEW.ILT_RULE_PROPERTIES + "--inputTableTitle",
  ILT_LOOKUPTABLE_LABEL: DB_VIEW.ILT_RULE_PROPERTIES + "--lookupTableTitle",
  TE_EDIT_ASSOCIATION: "@editAssociations",
  TE_HIERARCHY_COUNT: DB_BUTTON.HIERARCHY + "-content",
  ILT_FUZZY_ADVANCED_SETTING: DB_VIEW.ILT_RULE_PROPERTIES + "--fuzzyAdvancedRulePanel",
  ILT_FUZZY_ADVANCED_SETTING_FLEXIBLE1: DB_VIEW.ILT_RULE_PROPERTIES + "--lengthToleranceScore",
  ILT_FUZZY_ADVANCED_SETTING_FLEXIBLE2: DB_VIEW.ILT_RULE_PROPERTIES + "--errorDevaluateScore",
  GV_PARAMETER_POPOVER_BNAME: DB_VIEW.GV_ENTITY + "--ParameterInfoPopover--businessName",
  GV_CALCULATED_HEADER_NAME:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-CalculatedProperties--calcHeaderDetails--nameLabel",
  GV_OUTPUT_NAME:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--detailsHeader--nameLabel",
  GV_ENTITY_NAME:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-EntityProperties--detailsHeader--nameLabel",
  DF_JOIN_ADVANCED_PROP_TITLE: DB_VIEW.DF_JOIN_PROPERTIES + "--joinPropertiesTitle",
  DF_JOIN_OPT_TYPE_LABEL: DB_VIEW.DF_JOIN_PROPERTIES + "--joinOptimizationLabel-text",
  DF_JOIN_OPT_LABEL_FULLSCREEN: DB_VIEW.DF_JOIN_OBJECT_PAGE + "--joinOptimizationLabel",
  DF_JOIN_OPT_TYPE_VALUE: DB_VIEW.DF_JOIN_PROPERTIES + "--joinOptimization",
  DF_JOIN_OPT_VALUE_FULLSCREEN: DB_VIEW.DF_JOIN_OBJECT_PAGE + "--joinOptimization",
  DF_JOIN_PROP_DIALOG_LEFT_INPUT_LABEL: DB_VIEW.DF_JOIN_ADVANCED_PROP_DIALOG + "--leftInputLabel",
  DF_JOIN_PROP_DIALOG_LEFT_INPUT_NAME: DB_VIEW.DF_JOIN_ADVANCED_PROP_DIALOG + "--leftInputName",
  DF_JOIN_PROP_DIALOG_RIGHT_INPUT_LABEL: DB_VIEW.DF_JOIN_ADVANCED_PROP_DIALOG + "--rightInputLabel",
  DF_JOIN_PROP_DIALOG_RIGHT_INPUT_NAME: DB_VIEW.DF_JOIN_ADVANCED_PROP_DIALOG + "--rightInputName",
  DF_JOIN_ADV_PANEL_LEFT_INPUT_NAME: DB_VIEW.DF_JOIN_PROPERTIES + "--leftInputName",
  DF_JOIN_ADV_PANEL_RIGHT_INPUT_NAME: DB_VIEW.DF_JOIN_PROPERTIES + "--rightInputName",
  DF_JOIN_ADV_PANEL_LEFT_RANK_VALUE: DB_VIEW.DF_JOIN_PROPERTIES + "--leftInputRankValue",
  DF_JOIN_ADV_PANEL_RIGHT_RANK_VALUE: DB_VIEW.DF_JOIN_PROPERTIES + "--rightInputRankValue",
  DF_JOIN_ADV_PANEL_LEFT_CACHE_VALUE: DB_VIEW.DF_JOIN_PROPERTIES + "--leftCacheValue",
  DF_JOIN_ADV_PANEL_RIGHT_CACHE_VALUE: DB_VIEW.DF_JOIN_PROPERTIES + "--rightCacheValue",
  DF_ADV_PANEL_MEMORY_ALLOCATION_LABEL: DB_VIEW.DF_MODEL_PROPERTIES + "--memoryAllocationLbl",
  DF_ADVANCED_PROP_PANEL: DB_VIEW.DF_MODEL_PROPERTIES + "--memoryConfigurationPanel",
  DF_ADV_PROP_DATA_VOLUME_TXT: DB_VIEW.DF_MODEL_PROPERTIES + "--dataVolumeLbl",
  TC_EXECUTION_ERROR_TEXT: DB_VIEW.TC_MODEL_PROPERTIES + "--errorDetails",
  TC_MODEL_RUN_STATUS: DB_VIEW.TC_MODEL_PROPERTIES + "--runStatus",
  TC_MODEL_EMAIL_SETTING_INPUT: DB_VIEW.TC_MODEL_PROPERTIES + "--notificationSettingsOptions",
  TC_MODEL_SCHEDULE_MENU: DB_VIEW.TC_MODEL_PROPERTIES + "--scheduleMenu",
  TC_MODEL_EMAIL_SUBJECT_INPUT: DB_VIEW.TC_MODEL_PROPERTIES + "--emailSubject-inner",
  TC_MODEL_EMAIL_CODE_EDITOR_BODY: DB_VIEW.TC_MODEL_PROPERTIES + "--emailMessageBody",
  TC_MODEL_EMAIL_EMAIL_MULTI_INPUT: DB_VIEW.TC_MODEL_PROPERTIES + "--recipientEmailAddrInput",
  TC_MODEL_TENANT_MEMBERS_LIST: DB_VIEW.TC_MODEL_PROPERTIES + "--subStatus--tenantMemberList",
  TC_MODEL_TENANT_MEMBERS_SEARCH: DB_VIEW.TC_MODEL_PROPERTIES + "--subStatus--searchTenantMemberInput",
  TC_MODEL_NONTENANT_MEMBERS_LIST: DB_VIEW.TC_MODEL_PROPERTIES + "--subStatus--nonTenantMembersList",
  TC_MODEL_SELECTED_MEMBERS_VALUHELP_LIST: DB_VIEW.TC_MODEL_PROPERTIES + "--subStatus--selectedEmailAddress",
  TC_MODEL_CANCEL_VALUHELP_BUTTON: DB_VIEW.TC_MODEL_PROPERTIES + "--subStatus--cancelEmailRecipientsBtn",
  TC_MODEL_SAVE_VALUHELP_BUTTON: DB_VIEW.TC_MODEL_PROPERTIES + "--subStatus--saveEmailRecipientsBtn",
  TC_MODEL_ADD_NONTENANTMEMBER_BUTTON: DB_VIEW.TC_MODEL_PROPERTIES + "--subStatus--addNonTenantMemberBtn",
  TC_MODEL_VALUEHELP_TABBAR: DB_VIEW.TC_MODEL_PROPERTIES + "--subStatus--emailRecipientsTabs",
  TC_MODEL_DEPLOYMENT_STATUS: DB_VIEW.TC_MODEL_PROPERTIES + "--objectStatusText",
  TC_MODEL_DEPLOYMENT_DATE: DB_VIEW.TC_MODEL_PROPERTIES + "--deploymentDate",
  TC_TARGET_NODE_PANEL: DB_VIEW.TC_MODEL_PROPERTIES + "--NodesPanelTitle",
  DF_ADD_COLUMN_NO_DATA_TXT: "remoteSourceColumnSelector--columnSelectDialog-table-nodata-text",
  IL_RULEPOPERTIES_LABEL: DB_VIEW.ILT_RULE_PROPERTIES + "--label",
  IL_RULEMATCHTYPE_SELECT_ARROW: DB_VIEW.ILT_RULE_PROPERTIES + "--ruleMatchTypeSelect-arrow",
  IL_OUTPUTPROPRTIES_BUSINESSNAME: DB_VIEW.ILT_OUTPUT_PROPERTIES + "--businessName",
  IL_OUTPUTNODE_EXPANDBUTTON: DB_VIEW.ILT_OUTPUT_PROPERTIES + "--NodeColumnsPanel-expandButton",
  IMPACT_LINEAGE_TITLE: DB_VIEW.ROOT + "headerTitle",
  DF_REMOTE_TABLE_NAME: DB_VIEW.DF_NODE_PROPERTIES + "--remoteTableName",
  RF_RUN_STATUS: DB_VIEW.RF_PROPERTY_PANEL + "--rfRunStatus",
  RF_OBJECT_STATUS: DB_VIEW.RF_PROPERTY_PANEL + "--rfObjectStatusText",
  RF_DEPLOYMENT_DATE: DB_VIEW.RF_PROPERTY_PANEL + "--rfDeploymentDate",
  RF_TRANSFORMATION_PANEL: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--transformationPropsTitle",
  RF_SETTINGS_PANEL: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--settingsPropertiesTitle",
  RF_TARGET_COLUMN_PANEL: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--targetColumnsTitle",
  RF_OBJECT_TEXT: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--rfTaskDetailsHeader--nameLabel",
  RF_TARGET_OBJECT_DEPLOYMENT_STATUS: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--replicationObjectStatusText",
  RF_HEADER_NAME: DB_VIEW.RF_PROPERTY_PANEL + "--rfHeader--nameLabel",
  RF_TRANSFORM_NAME: DB_CONTROL.TRANSFORMATION_DIALOG + "--transformName",
  RF_SOURCE_CONNECTION_LABEL_ID: DB_VIEW.RF_EDITOR + "--sourceConnection",
  RF_SOURCE_CONTAINER_LABEL_ID: DB_VIEW.RF_EDITOR + "--sourceContainer",
  RF_TARGET_CONNECTION_LABEL_ID: DB_VIEW.RF_EDITOR + "--targetConnection",
  RF_TARGET_CONTAINER_LABEL_ID: DB_VIEW.RF_EDITOR + "--targetContainer",
  RF_SOURCE_CONNECTION_EMPTY_LABEL_ID: DB_VIEW.RF_EDITOR + "--sourceConnectionEmpty",
  RF_TARGET_CONNECTION_EMPTY_LABEL_ID: DB_VIEW.RF_EDITOR + "--targetConnectionEmpty",
  RF_SOURCE_CONTAINER_EMPTY_LABEL_ID: DB_VIEW.RF_EDITOR + "--sourceContainerEmpty",
  RF_TARGET_CONTAINER_EMPTY_LABEL_ID: DB_VIEW.RF_EDITOR + "--targetContainerEmpty",
  RF_DATASET_TOOLTIP_TITLE: DB_VIEW.RF_EDITOR + "--sourceInfoPopover--sourceTitle",
  RF_PANEL_DATASET_OBJECT_COUNT_TEXT_ID: DB_VIEW.RF_EDITOR + "--sourceDatasetCount",
  RF_DELETE_ALL_BEFORE_LOADING_ID: "replicationFlowBuilder--subheaderTruncate",
  RF_DELETE_ALL_TEXT_OBJECT_PROPERTY_PANEL_ID: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--rfTruncateCBLbl",
  RF_TRANSFORMATION_MAPPING_TABLE_COUNT_LABEL_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--tableCount-text",
  RF_TRANSFORMATION_FILTER_TOKEN_LIST_LABEL_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--filterTabListCountLabel",
  RF_TRANSFORMATION_FILTER_EXPRESSION_ID: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--filterExpressionHBOX",
  RF_TRANSFORMATION_DIALOG_FILTER_INVALID_COLUMNS_POPOVER_TEXT:
    DB_VIEW.RF_TRANSFORMATION_DIALOG + "--InvalidColsInfoPopover--invalidColumnsText",
  RF_CONFLUENT_SCHEMA_SETTINGS_PANEL_SUBJECTNAME: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentSubjectName",
  RF_CONFLUENT_SCHEMA_SETTINGS_PANEL_VERSION: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentSchemaVersion",
  RF_CONFLUENT_SCHEMA_SETTINGS_PANEL_INCLUDE_TECHNICLE_KEY:
    DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentIncludeTechKey",
  RF_CONFLUENT_SCHEMA_SETTINGS_PANEL_OMIT_NON_EXPANDED_ARRAYS:
    DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentOmitNonExpandedArrays",
  RF_CONFLUENT_SCHEMA_SETTINGS_PANEL_EXPAND_ARRAY_OR_MAP:
    DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentExpandArrayOrMap",
  RF_MAIN_CANVAS_LEFT_COLUMN_TARGET_LABEL_ID:
    DB_VIEW.RF_EDITOR + "--newTargetObjectLeftColID-" + DB_VIEW.RF_EDITOR + "--replicationFlowMainTable-",
  RF_CONFLUENT_PANEL_SUBJECTNAME: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentSubjectName",
  RF_DATE_TEXT_FILTER_TAB_COLUMN_INFO_POPOVER: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--ColInfoPopover--datatypetext",
  RF_CONFLUENT_PANEL_VERSION: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentSchemaVersion",
  RF_CONFLUENT_PANEL_INCLUDE_TECHNICLE_KEY: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--confluentIncludeTechKey",
  RF_SIGNAVIO_TARGET_PROPERTY_PANEL_GROUP_DELTA_PROPERTY:
    DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--signavioGroupDeltaFilesByText",
  RF_SIGNAVIO_TARGET_PROPERTY_PANEL_FILE_FORMAT: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--signavioFileFormatText",
  RF_SIGNAVIO_TARGET_PROPERTY_PANEL_APACHE_SPARK_COMPATIBILITY:
    DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--signavioSparkCompatibilityParquetText",
  RF_SIGNAVIO_TARGET_PROPERTY_PANEL_FILE_COMPRESSION:
    DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--siganvioFileCompressionText",
  RF_SIGNAVIO_TARGET_PROPERTY_PANEL_DUPLICATE_SUPPRESION:
    DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--siganvioSuppressDuplicatesText",
  RF_SIGNAVIO_TARGET_TARGET_SETTINGS_GROUP_DELTA_BY:
    DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--signavioGroupDeltaFilesByText",
  RF_SIGNAVIO_TARGET_TARGET_SETTINGS_FILE_FORMAT:
    DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--signavioFileFormatText",
  RF_SIGNAVIO_TARGET_TARGET_SETTINGS_APACHE_SPARK_COMPATIBILITY:
    DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--signavioSparkCompatibilityParquetText",
  RF_SIGNAVIO_TARGET_TARGET_SETTINGS_FILE_COMPRESSION:
    DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--siganvioFileCompressionText",
  RF_SIGNAVIO_TARGET_TARGET_SETTINGS_DUPLICATE_SUPPRESION:
    DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--siganvioSuppressDuplicatesText",
  ER_TABLE_POPOVER_BNAME: DB_VIEW.ER_MODEL_PRO + "--EntityInfoPopover--businessName",
  ER_TABLE_POPOVER_BNAME_LINK: DB_VIEW.ER_MODEL_PRO + "--EntityInfoPopover--businessNameLink",
  ER_TABLE_POPOVER_TNAME: DB_VIEW.ER_MODEL_PRO + "--EntityInfoPopover--technicalName",
  ER_TABLE_POPOVER_TYPE: DB_VIEW.ER_MODEL_PRO + "--EntityInfoPopover--type",
  ER_TABLE_POPOVER_STATUS: DB_VIEW.ER_MODEL_PRO + "--EntityInfoPopover--objectStatusText",
  RF_EMAIL_NOTIF_OPTIONS: "emailNotificationDialog--dialog--view--notificationSettingsOptions",
  RF_EMAIL_SUBJECT_INPUT: "emailNotificationDialog--dialog--view--emailSubject-inner",
  RF_EMAIL_BODY_INPUT: "emailNotificationDialog--dialog--view--emailMessageBody-inner",
  RF_EMAIL_RECIPIENT_INPUT: "emailNotificationDialog--dialog--view--recipientEmailAddrInput",
  RF_EMAIL_TENANT_MEMBER_LIST: "emailNotificationDialog--dialog--view--tenantMemberList",
  RF_EMAIL_SEARCH_TENANT_INPUT: "emailNotificationDialog--dialog--view--searchTenantMemberInput",
  RF_EMAIL_NON_TENANT_LIST: "emailNotificationDialog--dialog--view--nonTenantMembersList",
  RF_EMAIL_TABS: "emailNotificationDialog--dialog--view--emailRecipientsTabs",
  RF_EMAIL_SELECTED_INPUT: "emailNotificationDialog--dialog--view--selectedEmailAddress",
  RF_EMAIL_ADD_NON_TENANT_BTN: "emailNotificationDialog--dialog--view--addNonTenantMemberBtn",
  RF_EMAIL_CANCEL_BTN: "emailNotificationDialog--dialog--view--cancelSelectedEmailRecipientsBtn",
  RF_EMAIL_SELECT_BTN: "emailNotificationDialog--dialog--view--selectEmailRecipientsBtn",
  DF_NODE_ODATA_NAV_PROP_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--odataPropertiesTitle",
  DF_NODE_ODATA_NAV_PROP_DEPTH_LBL: DB_VIEW.DF_NODE_PROPERTIES + "--depthLbl",
  DF_NODE_ODATA_NAV_PROP_DEPTH_VALUE: DB_VIEW.DF_NODE_PROPERTIES + "--depth",
  ER_ENTITY_ASSO_MAPPING_TABLEB_HEADER:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--joinPropertiesDialogView--mappings--tableB_HeaderHbox",
  ER_ENTITY_ASSO_MAPPING_TABLEA_HEADER:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--joinPropertiesDialogView--mappings--tableA_HeaderHbox",
  ER_ENTITY_ASSO_POPOVER_TABLEB:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--AssociationInfoPopover--toInfoLink",
  ER_ENTITY_ASSO_POPOVER_TABLEA:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--AssociationInfoPopover--fromInfoLink",
  ER_ENTITY_DAC_POPOVER_TABLEB:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--DacInfoPopover--toInfoLink",
  ER_ENTITY_DAC_MAPPING_TABLEB_HEADER:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--daclJoin--daclPropertiesDialogView--mappings--tableB_HeaderHbox",
  ER_ENTITY_DAC_MAPPING_TABLEA_HEADER:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--ermodeler-properties-EntityProperties--EntityPropertiesView--daclJoin--daclPropertiesDialogView--mappings--tableA_HeaderHbox",
  GV_JOIN_MAPPING_TABLEA_HEADER:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-JoinPropertiesWithRenameFeature--mappings--tableA_HeaderHbox",
  GV_JOIN_MAPPING_TABLEB_HEADER:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-JoinPropertiesWithRenameFeature--mappings--tableB_HeaderHbox",
  GV_UNION_MAPPING_TABLEA_HEADER:
    "shellMainContent---databuilderComponent---databuilderWorkbench--graphSplitView--PropertyPanel--csnquerybuilder-properties-UnionProperties--mappings--tableA_HeaderHbox",
  DF_NODE_NO_PARAMETERS_ADDED: DB_VIEW.DF_NODE_PROPERTIES + "--parametersList-nodata-text",
  DF_NODE_PARAMETERS_LABEL: DB_VIEW.DF_NODE_PROPERTIES + "--odataParametersTitle-inner",
  HIERARCHY_NODE_TYPE_VALUE_NODATA: "ExternalDirectoryHierarchyEditor--typeValueList-nodata",
  TF_TARGET_NODE_OBJECT_STATUS: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--objectStatusText",
  PREMIUM_OUTBOUND_STRIP_MESSAGE: DB_VIEW.RF_EDITOR + "--premiumOutBoundWarningStrip",
  TF_VT_MODE: DB_VIEW.TF_VIEW_TRANSFORM_NODE_PROPERTIES + "--modeTF",
  TF_VT_DB_VIEW_TYPE: DB_VIEW.TF_VIEW_TRANSFORM_NODE_PROPERTIES + "--dbViewTypeTF",
  TF_MP_DETAILS_HEADER_NAME_LABEL: DB_VIEW.TF_MODEL_PROPERTIES + "--detailsHeader--nameLabel",
  TF_SECONDARY_GV_EDITOR_COL_INFO_NAME: DB_VIEW.TF_SECONDARY_GV_EDITOR_PROPERTIES + "--TFColInfoPopover--name",
  RF_SOURCE_DATASET_BUSINESS_NAME: DB_VIEW.RF_EDITOR + "--sourceInfoPopover--sourceBusinessNameText",
  RF_TARGET_COLUMN_POPOVER_BUSINESS_NAME: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "--ColInfoPopover--sourceBusinessNameText",
  RF_SOURCE_COLUMN_POPOVER_BUSINESS_NAME: DB_VIEW.RF_TRANSFORMATION_DIALOG + "--ColInfoPopover--sourceBusinessNameText",
  RF_MESSAGE_DELTA_PARTITION_DISABLE:
    DB_VIEW.RF_SOURCE_CONNECTION_SETTING_DIALOG + "--view--deltaPartitionNotEnabledText",
  RF_TARGET_REPLICATION_THREAD_COUNT: DB_VIEW.RF_CONNECTION_SETTING_DIALOG + "--view--targetInputErrorLabel",
  RF_SOURCE_SCHEMA_COLUMN_COUNT: DB_VIEW.SCHEMA_DAILOG_ID + "--SourceColumnCount",
  TF_TARGET_COL_INFO_POPOVER_DATATYPE: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--TFColInfoPopover--displayDataTypeText",
  TF_TARGET_COL_INFO_POPOVER_BASETYPE: DB_VIEW.TF_TARGET_NODE_PROPERTIES + "--TFColInfoPopover--baseTypeText",
  ODATA_PREVIEW_NOT_ENABLED_MESSAGE: DB_VIEW.ODATA_VIEW_API_BUILDER + "--previewNotEnabledMessage",
  TF_SCRIPT_COL_INFO_POPOVER_BUSINESSNAME: DB_VIEW.TF_PYTHON_NODE_PROPERTIES + "--TFColInfoPopover--businessName",
  TF_SCRIPT_COL_INFO_POPOVER_DATATYPE: DB_VIEW.TF_PYTHON_NODE_PROPERTIES + "--TFColInfoPopover--displayDataTypeText",
  TF_SECONDARY_SOURCE_PROPERTIES_OBJECT_STATUS: DB_VIEW.TF_SECONDARY_SOURCE_PROPERTIES + "--objectStatusText",
  RF_PROPERTY_PANEL_COLUMN_COUNT: DB_VIEW.RF_OBJECT_PROPERTY_PANEL + "s--rfTaskDetailsHeader--headerLabelCount-text",
};
