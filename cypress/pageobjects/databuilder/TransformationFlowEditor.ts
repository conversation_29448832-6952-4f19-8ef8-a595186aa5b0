/**
 * Copyright 2023 SAP SE or an SAP affiliate company. All rights reserved.
 *
 * @format
 */

import { DBDiagramEditor } from "./DBDiagramEditor";
import { DB_BUTTON, DB_LIST, DB_VIEW } from "./descriptor";

export const VIEWTRANSFORM = "Node/viewtransform1";
export const SQLTRANSFORM = "Node/sqltransform1";
export const SQLSCRIPTTRANSFORM = "Node/sqlscripttransform1";
export const TARGET = "Node/target1";
export const PYTHON = "Node/python1";
export class TransformationFlowEditor extends DBDiagramEditor {
  public secondaryEditor: any;

  getEditorControl(): sap.galilei.ui5.Editor {
    return this.win.sap.ui.getCore().byId("transformationFlowEditor--transformationFlowEditorControl--editorControl");
  }

  private async getSecondaryEditor(): Promise<any> {
    await this.assertControlExists({ id: "csnQueryBuilderEditor" });
    return this.controller.isSecondaryEditorActive && this.controller.secondaryEditor;
  }

  public async getSQLSecondaryEditor(): Promise<any> {
    await this.assertControlExists({ id: "sqlEditor" });
    return this.controller.isSecondaryEditorActive && this.controller.secondaryEditor;
  }

  public async dragAndDropEntity(entityName: string) {
    await this.browser.expandTreeList();
    await this.browser.dragEntity(entityName);
    await this.diagram.dropOnGraph();
  }

  public async dragAndDropEntityToSecondaryEditor(entityName: string, x?: number, y?: number, isSharedTable?: boolean) {
    await this.browser.expandTreeList();
    await this.browser.dragEntity(entityName);
    if (isSharedTable) {
      await this.api.XHRWait("@shares");
      await this.api.XHRWait("@spaces");
    }
    await this.api.dropOnSecondaryGraph(this.secondaryEditor, x, y);
  }

  public async dragAndDropToTarget(entityName: string) {
    await this.browser.dragEntity(entityName);
    await this.diagram.dropOnSymbol({ id: TARGET });
  }

  public async dragAndDropToSource(entityName: string) {
    await this.browser.dragEntity(entityName);
    await this.diagram.dropOnSymbol({ id: VIEWTRANSFORM });
  }

  public getId(): string {
    return "transformationflow";
  }

  public async assertTemplate() {
    await this.diagram.assertSymbol({ id: VIEWTRANSFORM });
    await this.diagram.assertSymbol({ id: TARGET });
  }

  public async assertGeneralToolbar(enabled: boolean, isNew: boolean, isRunEnabled: boolean) {
    if (isNew) {
      await this.assertButton({
        button: DB_BUTTON.SAVE,
        enabled: enabled,
      });
    } else {
      await this.assertButton({
        button: DB_BUTTON.SAVEANDSAVEAS,
        enabled: enabled,
      });
    }
    await this.assertButton({
      button: DB_BUTTON.DEPLOY,
      enabled: enabled,
    });
    await this.assertButton({
      button: DB_BUTTON.RUN,
      enabled: isRunEnabled,
    });
  }

  public async assertViewTransformButton(isNew: boolean) {
    if (isNew) {
      this.openGVEditor();
    } else {
      this.editViewTransform();
    }
  }

  /**
   * open viewtransform editor using edit button on context pad
   *
   * @memberof TransformationFlowEditor
   */
  public async editViewTransform() {
    await this.diagram.assertSymbol({ id: VIEWTRANSFORM });
    await this.diagram.selectSymbol({ id: VIEWTRANSFORM }, true);
    await this.diagram.clickContextPad("Sap-Cdw-Transformationflow-OpenEditor");
    this.secondaryEditor = await this.getSecondaryEditor();
  }

  /**
   * open SQL editor using SQL button on context pad
   *
   * @memberof TransformationFlowEditor
   */
  public async useSQLEditor() {
    await this.diagram.assertSymbol({ id: VIEWTRANSFORM });
    await this.diagram.selectSymbol({ id: VIEWTRANSFORM }, true);
    await this.diagram.clickContextPad(DB_BUTTON.TF_OPEN_SQL_EDITOR);
    this.secondaryEditor = await this.getSQLSecondaryEditor();
    await this.properties.assertControlExists({
      id: DB_VIEW.TF_SECONDARY_SQL_EDITOR_PROPERTIES,
      hasWidth: true,
    });
  }

  /**
   * open GV editor using GV button on context pad
   *
   * @memberof TransformationFlowEditor
   */
  public async openGVEditor() {
    await this.diagram.assertSymbol({ id: VIEWTRANSFORM });
    await this.diagram.selectSymbol({ id: VIEWTRANSFORM }, true);
    await this.diagram.clickContextPad(DB_BUTTON.TF_OPEN_GV_EDITOR);
    this.secondaryEditor = await this.getSecondaryEditor();
  }

  /**
   * get secondary editor controller
   *
   * @return {*}
   * @memberof TransformationFlowEditor
   */
  public getSecondaryEditorController() {
    return this.secondaryEditor?.controller();
  }

  /**
   * create new target table using context pad
   *
   * @memberof TransformationFlowEditor
   */
  public async createNewTarget() {
    await this.diagram.assertSymbol({ id: TARGET });
    await this.diagram.selectSymbol({ id: TARGET }, true);
    await this.diagram.clickContextPad("Sap-Cdw-Transformationflow-CreateTable");
    await this.properties.assertList({
      list: DB_LIST.TF_TARGET_NODE_COLUMNS_LIST,
    });
  }

  /**
   * create new target table using the button in properties panel
   *
   * @memberof TransformationFlowEditor
   */
  public async pressCreateNewTargetTable() {
    await this.pressButton(DB_BUTTON.TF_TARGET_NODE_CREATE_NEW_TABLE);
    await this.properties.assertList({
      list: DB_LIST.TF_TARGET_NODE_COLUMNS_LIST,
    });
  }

  /**
   * define sql view transform using the button in properties panel
   *
   * @memberof TransformationFlowEditor
   */
  public async pressUseSQLEditor() {
    await this.pressButton(DB_BUTTON.TF_VIEW_TRANSFORM_NODE_USE_SQL_VIEW);
    this.secondaryEditor = await this.getSQLSecondaryEditor();
  }

  /**
   * define graphical view transform using the button in properties panel
   *
   * @memberof TransformationFlowEditor
   */
  public async pressUseGraphicalViewEditor() {
    await this.pressButton(DB_BUTTON.TF_VIEW_TRANSFORM_NODE_USE_GRAPHICAL_VIEW);
    this.secondaryEditor = await this.getSecondaryEditor();
  }

  public async navigateBackToPrimaryEditor() {
    this.secondaryEditor = null;
    await this.pressButton("backToTFlow");
  }

  public async deleteTarget() {
    await this.diagram.selectSymbol({ id: TARGET }, true);
    await this.diagram.clickContextPad("Sap-Cdw-Transformationflow-Delete");
  }

  public async deleteViewTransform() {
    await this.diagram.selectSymbol({ id: VIEWTRANSFORM }, true);
    await this.diagram.clickContextPad("Sap-Cdw-Transformationflow-Delete");
  }

  public assertColumnsMapping(column1: string, column2: string, containerId: string) {
    return this.editor.api.assertDOMNode({
      containerId: containerId,
      domAttributes: [
        {
          attribute: "columnTableA",
          value: column1,
        },
        {
          attribute: "columnTableB",
          value: column2,
        },
      ],
    });
  }

  /**
   * creates mapping from column1 (in idLeft1) to column2 (in idright1)
   * @param {string} column1
   * @param {string} column2
   * @param {string} idLeft1
   * @param {string} idright1
   */
  public async mapJoinColumns(column1: string, column2: string, idLeft1: string, idright1: string) {
    await this.dragUI5ListItems(
      idLeft1 as any,
      [column1],
      idright1 as any,
      /* dragOnFirstChildInstead*/ undefined,
      column2
    );

    await this.dropUI5ListItems();
  }

  public async assertGraphicalViewEditorGeneralToolbar(isNew: boolean) {
    if (isNew) {
      await this.assertButton({
        button: DB_BUTTON.SAVE,
        enabled: false,
      });
    } else {
      await this.assertButton({
        button: DB_BUTTON.SAVEANDSAVEAS,
        enabled: false,
      });
    }
    await this.assertButton({
      button: DB_BUTTON.DEPLOY,
      enabled: false,
    });
    await this.assertButton({
      button: DB_BUTTON.RUN,
      enabled: false,
    });
  }

  /**
   * create python operator using context pad
   *
   * @memberof TransformationFlowEditor
   */
  public async createPythonOperator() {
    await this.diagram.assertSymbol({ id: VIEWTRANSFORM });
    await this.diagram.selectSymbol({ id: VIEWTRANSFORM }, true);
    await this.diagram.clickContextPad(DB_BUTTON.TF_ADD_PYTHON_OPERATOR);
  }
}
