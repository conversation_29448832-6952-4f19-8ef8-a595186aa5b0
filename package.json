{"name": "sap.orca.datamart", "version": "2025.14.32", "description": "Agile Data Warehouse Cloud SAC Plugin", "engines": {"node": "^22.11.0", "npm": "^10.7.0"}, "bin": {"dwc": "./support/index.js"}, "config": {"node_options": "--max_old_space_size=7168"}, "scripts": {"preinstall": "node ./dev/preinstall.js", "update": "node ./dev/update-dependencies.js", "dev:offline": "node ./dev/devOffline.js", "dev:cf": "node ./dev/devCF.js", "dev:hc": "node ./dev/devOffline.js provision-hc", "dev:cf-relog": "npm run dev:offline && npm run dev:cf", "dwc": "node ./support/index.js", "start": "npm run start-backend", "build": "npm run build-backend && npm run build-ui", "test-mocha": "node ./test/mocha.js --mode=unit-only", "test-mocha-integration": "node ./test/mocha.js --mode=integration-only", "create:nekton:test:runner": "nekton-space-test-client -- --testAction=create_test_runner --testRunnerUser=TR_DS --testGlobalSqlDir=service/tests/deploy/sql", "test": "npm run build-mocha && node --max_old_space_size=7168 --max-http-header-size=65536 ./test/index.js", "test:estimate-time-per-spec": "node ./test/testMetaFilter/estimateTimePerSpec.js", "test-memory": "node ./test/memoryTests/memoryTestCLI.js", "coverage:merge-conti-lcov": "node ./test/mergeContiLCov.js", "voter-hc:create": "node ./test/prOptimizations/ondemandHCInstance.js create", "voter-hc:teardown": "node ./test/prOptimizations/ondemandHCInstance.js teardown", "voter-hc:create-ahead": "node ./test/prOptimizations/createAheadHanas.js", "cpd:analyse": "node --max-semi-space-size=32 --max-old-space-size=4096 ./test/copyPasteDetector/analyseDuplication", "clear-reports": "node deleteReports.js", "deploy": "node ./deploy/deploy.js", "undeploy": "node ./deploy/deploy.js undeploy", "report": "node ./test/report.js", "add-version": "node add-version.js", "eslint": "cross-env-shell NODE_OPTIONS=$npm_package_config_node_options \"eslint service --ext .ts --quiet && eslint src --ext .ts,.js --quiet && eslint cypress --ext .ts,.js --quiet\"", "eslint:git-pr": "cross-env-shell NODE_OPTIONS=$npm_package_config_node_options \"node test/prOptimizations/prCheckLinting.js\"", "build-backend": "cross-env-shell NODE_OPTIONS=$npm_package_config_node_options \"npm run add-version && node copyfiles.js && tsc --incremental -p shared && tsc --incremental -p service \"", "build-ui": "cross-env-shell NODE_OPTIONS=$npm_package_config_node_options webpack", "build-mocha": "tsc -p service/tests --incremental", "watch-backend": "node copyfiles.js && cross-env-shell NODE_OPTIONS=--max-old-space-size=6144 \"tsc -p service --incremental --watch\"", "watch-mocha": "tsc -p service/tests --incremental --watch", "watch-shared": "tsc -p shared --incremental --watch", "watch-ui": "cross-env-shell NODE_OPTIONS=$npm_package_config_node_options WEBPACK_NAMED=1 WEBPACK_TRANSPILEONLY=1 \"webpack --watch --progress\"", "watch-backend-coverage": "cross-env-shell COVERAGE=1 LOCAL_COVERAGE=1 \"node copyfiles.js && tsc -p service --incremental --watch\"", "watch-shared-coverage": "cross-env-shell COVERAGE=1 LOCAL_COVERAGE=1 \"tsc -p shared --incremental --watch\"", "watch-ui-coverage": "cross-env-shell NODE_OPTIONS=$npm_package_config_node_options WEBPACK_NAMED=1 WEBPACK_TRANSPILEONLY=1 COVERAGE=1 LOCAL_COVERAGE=1 \"webpack --watch --progress\"", "start-backend": "node --max-http-header-size=65536 --max-semi-space-size=32 --max-old-space-size=6144 build/service/index.js", "check-format": "npx prettier@2.8.0 --require-pragma --no-plugin-search --check .", "check-format:git-pr": "node test/prOptimizations/prCheckFormatting.js --prettier-version=2.8.0", "format": "npx prettier@2.8.0 --require-pragma --no-plugin-search --write .", "remove-cypress-dep": "npm uninstall cypress cypress2 cypress-file-upload --save-dev", "remove-dev-dep": "npm prune --production", "remove-modules-cache": "npx rimraf node_modules/.cache", "update-shrinkwrap": "npm cache clean --force && npx rimraf node_modules npm-shrinkwrap.json && npm ci && npm shrinkwrap", "remove-servicemod-for-ui": "npm uninstall @sap/hana-client && npm uninstall @sap/hdi && npm uninstall @sap/hdi-deploy", "shrinkTestName": "node ./test/shrinkTestName.js", "upgrade-seal": "node ./dev/upgrade-seal.js", "upgrade-dragonet": "node ./dev/upgrade-dragonet.js", "upgrade-deepsea": "node ./dev/upgrade-deepsea.js", "hdi-upgrade": "node ./dev/hdi-upgrade.js", "clean-cache": "npm cache clean --force", "occi:generate-json": "node ./test/occiHelper/generateJson.js", "redis-start": "docker-compose -f ./dev/docker-compose.yml up -d", "redis-stop": "docker-compose -f ./dev/docker-compose.yml down -v"}, "author": "SAP", "dependencies": {"@dynatrace/oneagent-sdk": "1.5.0", "@rdfjs/types": "2.0.1", "@sap-coastguard/commons": "^0.59.0", "@sap-coastguard/consumer-sdk-backend": "^0.59.0", "@sap-coastguard/consumer-sdk-frontend": "^0.59.0", "@sap-coastguard/interfaces": "^0.59.0", "@sap-coastguard/ui-texts": "^0.59.0", "@sap-nekton/dac-utils": "1.62.1", "@sap-nekton/decorators": "1.62.1", "@sap-nekton/gen-ai": "1.63.0", "@sap-nekton/observability": "1.63.0", "@sap-theming/theming-base-content": "11.29.3", "@sap/app-extension-fwk": "5.1.7", "@sap/audit-logging": "6.7.1", "@sap/cds": "7.9.3", "@sap/cds-compiler": "4.6.2", "@sap/cds-compiler-v5": "npm:@sap/cds-compiler@5.9.0", "@sap/cds-v8": "npm:@sap/cds@8.9.0", "@sap/credential-store-client-node": "1.53.0", "@sap/deepsea-catalog-models": "2025.14.13", "@sap/deepsea-catalog-types": "2025.14.13", "@sap/deepsea-client": "2025.14.13", "@sap/deepsea-connection": "2025.14.13", "@sap/deepsea-controllers": "2025.14.13", "@sap/deepsea-crud": "2025.14.13", "@sap/deepsea-csnmetadata": "2025.14.13", "@sap/deepsea-dbsetup": "2025.14.13", "@sap/deepsea-document-analysis-wrapper": "2025.14.13", "@sap/deepsea-exports": "2025.14.13", "@sap/deepsea-persistence": "2025.14.13", "@sap/deepsea-plugin-framework": "2025.14.13", "@sap/deepsea-recovery": "2025.14.13", "@sap/deepsea-sac": "2025.14.13", "@sap/deepsea-sqlutils": "2025.14.13", "@sap/deepsea-types": "2025.14.13", "@sap/deepsea-utils": "2025.14.13", "@sap/dh-expression-parser": "2219.1.1", "@sap/dragonet-consumer-sdk": "2025.13.1", "@sap/dragonet-ui5-table-editor": "2025.13.1", "@sap/dwc-audit-logger": "1.148.1", "@sap/dwc-bdc": "1.148.1", "@sap/dwc-cache": "1.148.1", "@sap/dwc-circuit-breaker": "1.148.1", "@sap/dwc-configuration": "1.148.1", "@sap/dwc-connection-pooling": "1.148.1", "@sap/dwc-consistency-framework": "1.136.2", "@sap/dwc-context-checks": "1.148.1", "@sap/dwc-credentials": "1.127.0", "@sap/dwc-express-utils": "1.148.1", "@sap/dwc-flexible-tenant-sizing": "1.145.3", "@sap/dwc-generative-ai": "1.142.0", "@sap/dwc-http-client": "1.148.1", "@sap/dwc-json-patch": "1.148.1", "@sap/dwc-locking": "1.132.1", "@sap/dwc-logger": "1.144.1", "@sap/dwc-message-queuing": "1.126.0", "@sap/dwc-name-validator": "1.147.2", "@sap/dwc-notifications": "1.130.0", "@sap/dwc-performance-profiling": "1.148.1", "@sap/dwc-permissions": "1.103.6", "@sap/dwc-promise-utils": "1.148.1", "@sap/dwc-rate-limiting": "1.148.1", "@sap/dwc-redis-client": "1.148.1", "@sap/dwc-sac-client": "1.142.0", "@sap/dwc-sap-passport": "1.136.1", "@sap/dwc-service-cache-stores": "1.148.1", "@sap/dwc-space-permissions": "1.27.0", "@sap/dwc-taskschedule": "1.144.4", "@sap/dwc-telemetry": "1.148.1", "@sap/dwc-tms-provider": "1.148.1", "@sap/dwc-tracing": "1.148.1", "@sap/dwc-ucl-utils": "1.136.2", "@sap/dwc-uihelper": "1.66.2", "@sap/dwc-usage-tracking": "1.66.3", "@sap/e2e-trace": "5.4.0", "@sap/embed-iframe": "1.0.37", "@sap/enterprise-search-objects": "1.1.9", "@sap/enterprise-search-parser": "1.0.70", "@sap/esh-search-ui": "1.137.0", "@sap/galilei-ui5": "1.26.1", "@sap/hana-client": "2.24.24", "@sap/hdi": "4.7.0", "@sap/hdi-deploy": "5.4.2", "@sap/orca-shell": "1.202512.1", "@sap/orca-starter-security": "4.11.0", "@sap/orca-starter-solace": "4.11.0", "@sap/pacemaker": "2025.13.0", "@sap/prom-hana-client": "1.13.0", "@sap/seal-consumer-sdk": "2025.13.0", "@sap/seal-csn": "2025.13.0", "@sap/seal-deployer": "2025.13.0", "@sap/seal-interfaces": "2025.13.0", "@sap/seal-plugin-table": "2025.13.0", "@sap/seal-plugin-view-materialization": "2025.13.0", "@sap/skyline-consumer-sdk": "2025.13.0", "@sap/skyline-core": "2025.13.0", "@sap/skyline-tools": "2025.13.0", "@sap/velocity-data-tools-ui": "2006.1.1", "@sap/xotel-agent-ext-js": "1.5.26", "@sap/xsenv": "5.5.0", "@sentry/browser": "8.55.0", "@stomp/stompjs": "^7.0.0", "@types/adm-zip": "0.5.7", "@types/busboy": "0.3.1", "adm-zip": "0.5.16", "ajv": "8.17.1", "ajv-keywords": "5.1.0", "amqplib": "0.10.5", "bignumber.js": "9.1.2", "broadcast-channel": "7.1.0", "busboy": "1.0.0", "class-validator": "0.14.1", "dependency-graph": "1.0.0", "express": "4.21.2", "express-openapi-validator": "5.5.3", "fs-extra": "11.3.0", "helmet": "8.1.0", "http-proxy-agent": "7.0.2", "http-status-codes": "^2.3.0", "iso-639-1": "3.1.3", "jsonpath-plus": "10.3.0", "lodash": "4.17.21", "moment": "2.30.1", "moment-timezone": "0.5.46", "n3": "1.23.1", "node-forge": "1.3.1", "papaparse": "^5.3.0", "passport": "0.7.0", "protobufjs": "7.4.0", "rdf-js": "4.0.2", "rdf-literal": "2.0.0", "rimraf": "5.0.10", "semver": "^7.5.3", "source-map-support": "0.5.21", "sqlparser-js": "5.0.16", "tiny-glob": "0.2.9", "uuid": "11.1.0", "workerpool": "9.2.0", "xml2js": "0.6.2"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/register": "^7.12.13", "@cypress/webpack-preprocessor": "6.0.2", "@sap-nekton/core-types": "1.62.1", "@sap-nekton/space-test-client": "1.62.1", "@sap/dwc-webpack-sapui5": "1.26.0", "@sap/eslint-plugin-dwc-lints": "1.40.1", "@sap/seal-plugin-registry": "2025.13.0", "@types/amqplib": "^0.5.17", "@types/chai": "4.2.0", "@types/chai-as-promised": "7.1.1", "@types/express": "4.17.16", "@types/express-serve-static-core": "4.17.33", "@types/express-session": "1.17.6", "@types/fs-extra": "^11.0.4", "@types/jquery": "3.3.30", "@types/luxon": "3.4.2", "@types/mocha": "8.0.4", "@types/n3": "1.10.4", "@types/node": "22.10.10", "@types/node-fetch": "2.6.1", "@types/node-forge": "1.3.11", "@types/passport": "1.0.2", "@types/rewire": "2.5.30", "@types/semver": "^6.2.0", "@types/sinon": "10.0.20", "@types/sinon-chai": "3.2.12", "@types/sinon-express-mock": "^1.3.8", "@types/supertest": "^2.0.8", "@types/uuid": "3.4.6", "@types/xml2js": "^0.4.5", "@typescript-eslint/eslint-plugin": "6.5.0", "@typescript-eslint/parser": "6.5.0", "babel-plugin-const-enum": "1.1.0", "babel-plugin-istanbul": "6.0.0", "browserify-zlib": "0.2.0", "chai": "4.2.0", "chai-as-promised": "7.1.1", "chai-http": "4.3.0", "concat-stream": "1.6.2", "copy-webpack-plugin": "12.0.2", "crc": "4.3.2", "cross-env": "7.0.3", "crypto-browserify": "3.12.1", "css-loader": "^4.3.0", "css-url-relative-plugin": "1.0.0", "cypress": "8.7.0", "cypress-file-upload": "^5.0.2", "cypress-multi-reporters": "1.4.0", "cypress-recurse": "1.13.1", "cypress2": "npm:cypress@14.4.0", "eslint": "8.3.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-ban": "1.6.0", "eslint-plugin-header": "3.1.1", "eslint-plugin-no-only-tests": "2.6.0", "express-list-endpoints": "4.0.1", "express-session": "1.17.3", "find-test-names": "1.15.0", "glob": "7.1.6", "glob-promise": "3.4.0", "html-webpack-plugin": "^4.5.2", "http-proxy-middleware": "2.0.9", "https-browserify": "1.0.0", "ioredis-mock": "8.9.0", "js-yaml": "4.1.0", "jscpd": "3.3.26", "json-schema-to-typescript": "^10.1.5", "jsonwebtoken": "9.0.0", "jszip": "^3.6.0", "less": "^4.1.1", "less-loader": "^7.3.0", "mini-css-extract-plugin": "^1.3.8", "mocha": "10.6.0", "mocha-chai-jest-snapshot": "1.1.6", "mocha-junit-reporter": "2.0.0", "mocha-multi-reporters": "1.5.1", "mocha-xunit-reporter": "2.3.0", "mocked-env": "^1.3.2", "nyc": "15.1.0", "open": "8.2.1", "openapi-framework": "^7.3.0", "os-browserify": "0.3.0", "path-browserify": "1.0.1", "prettier": "2.8.0", "prettier-plugin-organize-imports": "3.2.2", "process": "0.11.10", "proxyquire": "2.1.3", "readline-sync": "1.4.10", "rewire": "5.0.0", "sinon": "15.2.0", "sinon-chai": "3.7.0", "sinon-express-mock": "2.2.0", "stream-browserify": "3.0.0", "stream-http": "3.2.0", "supertest": "^4.0.2", "tar": "7.4.3", "timers-browserify": "2.0.12", "ts-loader": "9.5.1", "ts-sinon": "2.0.2", "typescript": "5.2.2", "typescript-json-schema": "0.53.1", "url": "0.11.0", "util": "0.12.5", "webpack": "5.98.0", "webpack-cli": "5.1.4", "webpack-plugin-istanbul": "1.0.3", "yaml": "1.10.2", "zod": "3.23.8"}, "nyc": {"check-coverage": false, "per-file": true, "extension": [".js", ".ts"], "exclude": ["**/*.d.ts", "**/*.spec.*", "**/test/**/*", "**/tests/**/*"], "include": ["service/**/*", "src/**/*", "build/**/*"], "reporter": ["lcov"], "cache": true, "all": true}, "standard": {"env": {"mocha": true, "browser": true, "node": true}, "globals": ["cy", "Cypress", "sap"]}}